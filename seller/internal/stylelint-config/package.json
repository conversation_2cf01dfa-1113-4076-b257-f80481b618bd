{"name": "@vben/stylelint-config", "version": "1.0.0", "private": true, "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": {"url": "https://github.com/vbenjs/vue-vben-admin/issues"}, "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "internal/stylelint-config"}, "license": "MIT", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"clean": "pnpm rimraf .turbo node_modules dist", "lint": "pnpm eslint .", "stub": "pnpm unbuild --stub"}, "devDependencies": {"postcss": "^8.4.24", "postcss-html": "^1.5.0", "postcss-less": "^6.0.0", "postcss-scss": "^4.0.6", "prettier": "^2.8.8", "stylelint": "^15.10.1", "stylelint-config-property-sort-order-smacss": "^9.1.0", "stylelint-config-recommended": "^13.0.0", "stylelint-config-recommended-scss": "^12.0.0", "stylelint-config-recommended-vue": "^1.4.0", "stylelint-config-standard": "^34.0.0", "stylelint-config-standard-scss": "^10.0.0", "stylelint-order": "^6.0.3", "stylelint-prettier": "^3.0.0"}}