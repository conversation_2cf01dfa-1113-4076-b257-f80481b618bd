{"name": "@vben/vite-config", "version": "1.0.0", "private": true, "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": {"url": "https://github.com/vbenjs/vue-vben-admin/issues"}, "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "internal/vite-config"}, "license": "MIT", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"clean": "pnpm rimraf .turbo node_modules dist", "lint": "pnpm eslint .", "stub": "pnpm unbuild --stub"}, "dependencies": {"@ant-design/colors": "^7.0.0", "vite": "^4.4.0"}, "devDependencies": {"@types/fs-extra": "^11.0.1", "@vitejs/plugin-vue": "^4.2.3", "@vitejs/plugin-vue-jsx": "^3.0.1", "ant-design-vue": "^3.2.20", "dayjs": "^1.11.9", "dotenv": "^16.3.1", "fs-extra": "^11.1.1", "less": "^4.1.3", "picocolors": "^1.0.0", "pkg-types": "^1.0.3", "rollup-plugin-visualizer": "^5.9.2", "sass": "^1.63.6", "unocss": "^0.53.4", "vite-plugin-compression": "^0.5.1", "vite-plugin-dts": "^3.1.0", "vite-plugin-html": "^3.2.0", "vite-plugin-mock": "^2.9.6", "vite-plugin-purge-icons": "^0.9.2", "vite-plugin-svg-icons": "^2.0.1"}}