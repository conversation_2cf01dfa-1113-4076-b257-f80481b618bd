// @import "~antd/lib/style/themes/default.less";
p{
  margin-bottom: 0;
}
.goods_import {
  span.anticon:not( .app-iconify, .anticon-vertical-align-top, .anticon-bell, .anticon-left, .anticon-right ) {
    vertical-align: middle !important;
  }
  .ant-btn {
    font-size: 14px !important;
    padding: 4px 15px;
    font-weight: 500;
    height: 32px;
  }

  .import_success_con {
    font-size: 16px;
    font-weight: 700;
    margin-top: 20px;
  }

  .import_success_tip {
    color: rgb(102, 102, 102);
    margin-top: 30px;
    margin-bottom: 10px;
  }
}

.import_store_goods {
  .left_part {
    width: 220px;
    background: #FFF;
    border-radius: 2px;
    border: 1px solid #EBEDF0;
    margin-right: 10px;
    position: relative;

    .title {
      width: 100%;
      height: 49px;
      border-bottom: 1px solid #D8D8D8;
      font-size: 14px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #323233;
      padding-left: 20px;
    }

    .search_box {
      width: 100%;
      padding: 10px;
      span {
        display: inline-block;
        color: #323233;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        white-space: nowrap;
        margin-left: 10px;
        cursor: pointer;
      }
    }

    .goods_cat {
      width: 100%;

      .cat_item,
      .category_item {
        position: relative;
        height: 44px;
        padding-left: 20px;
        cursor: pointer;
        
        &.category_item {
          height: unset;
          padding-top: 10px;
          padding-bottom: 10px;
          padding-left: 10px;
        }

        &.category_item_none {
          .category_item_btns {
            display: none;
          }
  
          .category_item_num {
            display: block;
            flex: 1;
            text-align: right;
            padding-right: 10px;
          }
        }

        &.category_item_hover {
          .category_item_btns {
            display: none;
            flex: 1;
            text-align: right;
            padding-right: 10px;
  
            .category_item_btns_item {
              margin-left: 6px;
              &:hover {
                color: #FF701E;
              }
            }
          }
  
          .category_item_num {
            display: block;
            flex: 1;
            text-align: right;
            padding-right: 10px;
          }

          &:hover {
            .category_item_btns {
              display: flex;
            }
  
            .category_item_num {
              display: none;
            }
          }
        }

        &:hover {
          .cat_name {
            color: @primary-color !important;
          }

          .to_right_icon svg {
            fill: @primary-color !important;
          }
        }

        .cat_name {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #323233;
          line-height: 20px;

          &.margin_left {
            margin-left: 6px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }

        .to_right_icon {
          padding-right: 76px;
          display: flex;
        }
      }
      
      .category_item_child {
        .category_item {
          .cat_name {
            &.margin_left {
              margin-left: 28px;
            }
          }
        }
      }

      .selected_cat_item {
        background: #F9F9F9;

        .cat_name {
          color: #FF701E;
        }
      }
    }

    .more_cat {
      width: 800px;
      background: #F9F9F9;
      border: 1px solid #DFDFDF;
      position: absolute;
      left: 204px;
      top: 48px;
      z-index: 999;
      zoom: 1;
      height: calc(100% - 48px);
      .more_cat_box{
        padding: 25px;
        padding-left: 15px;
        
      }
      .more_cat_spin{
        position: absolute;
        width: 800px;
        top: 0;
        bottom: 0;
        left: 0;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .item {
        .second_cat {
          cursor: pointer;
          width: 108px;
          flex-shrink: 0;

          &:hover {
            .cat_name {
              color: #FF701E !important;
            }

            .to_right_icon svg {
              fill: #FF701E !important;
            }
          }

          .cat_name {
            height: 20px;
            font-size: 14px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #4C4C4C;
            line-height: 20px;
          }

          .to_right_icon {
            margin-left: 6px;
            display: flex;
          }
        }

        .third_cat {
          flex-wrap: wrap;
          margin-left: 20px;

          .item {
            height: 20px;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #999;
            line-height: 20px;
            margin-right: 20px;
            margin-bottom: 15px;

            &:hover {
              color: #FF701E !important;
            }
          }
        }
      }
    }
  }

  .right_goods {
    flex: 1;
    flex-wrap: wrap;

    .operate_bg {
      height: 40px;
      width: 100%;
      background: #f2f2f2;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 10px;
    
      :global {
        .ant-input-search > .ant-input-suffix > .ant-input-search-button {
          height: 29px !important;
        }
    
        .ant-input-search.ant-input-search-enter-button > .ant-input {
          height: 29px !important;
        }
      }
    }

    .operate_wrap {
      width: 100%;
      height: 50px;
      background: #FFF;
      border: 1px solid #EBEBEB;
      margin-bottom: -1px;

      .btn {
        font-size: 13px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #595959;
        height: 28px;
        background: #FFF;
        border-radius: 4px;
        border: 1px solid #D9D9D9;
        line-height: 26px;
        padding: 0 16px;
        margin-left: 20px;

        &:hover {
          border-color: @primary-color;
          color: @primary-color;
        }
      }
      .sel_goods_num_tip{
        font-size: 13px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999;
      }
      .sel_goods_num{
        color: @primary-color;
        font-weight: bold;
        margin-left: 2px;
        margin-right: 2px;
      }
    }

    .right_goods_item {
      flex-wrap: wrap;

      .item {
        position: relative;
        overflow: hidden;
        flex-shrink: 0;
        margin-right: 10px;
        margin-bottom: 15px;

        &.active {
          .img_wrap {
            border-color: #FF701E;
          }
        }

        .img_wrap {
          position: relative;
          overflow: hidden;
          border: 1px solid #E0E0E0;
          border-radius: 4px;
          background-size: contain;
          background-position: center;
          background-repeat: no-repeat;

          .checked {
            position: absolute;
            top: 0;
            right: 0;
            z-index: 9;
            cursor: pointer;
            width: 22px;
            height: 22px;
          }

          .file_state {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 9;
            height: 22px;
            line-height: 22px;
            cursor: default;
            text {
              padding-left: 4px;
              &.file_state_on {
                color: #ffffff;
              }
              &.file_state_off {
                color: #000000A6;
              }
            }
            img {
              position: absolute;
              left: 0;
              top: 0;
              z-index: -1;
              width: 58px;
              height: 22px;
            }
          }
          
          .file_size {
            position: absolute;
            bottom: 0;
            width: 100%;
            height: 26px;
            line-height: 26px;
            color: #fff;
            text-align: center;
            background: #00000085;
            cursor: default;
          }
        }

        .file_name {
          height: 34px;
          line-height: 18px;
          font-size: 13px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          margin-top: 5px;
          margin-bottom: 5px;
          padding-left: 6px;
          padding-right: 6px;
          display: -webkit-box;
          overflow: hidden;
          word-break: break-word;
          text-overflow: ellipsis;
          -webkit-line-clamp: 2;
          /* autoprefixer: off */
          -webkit-box-orient: vertical;
        }
        .item_btns {
          color: #FF701E;
          font-size: 13px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          white-space: nowrap;
          padding-left: 6px;
          padding-right: 6px;
          flex-wrap: wrap;
          
          span {
            margin-right: 8px;
            cursor: pointer;
          }
        }
      }
    }

    .right_goods,
    .right_goods_grid {
      flex: 1;
      flex-wrap: wrap;
  
      .item {
        cursor: pointer;
        position: relative;
        background: #FFF;
        border-radius: 2px;
        border: 1px solid #EAEAEA;
        margin-left: 10px;
        margin-bottom: 10px;
        padding-bottom: 10px;
        &:hover,
        &.active {
          border-color: #FF9147;
        }
        .checked {
          position: absolute;
          right: -1px;
          top: -1px;
          z-index: 99;
          width: 18px;
          height: 18px;
        }
        .virtual_goods_flag {
          position: absolute;
          left: -1px;
          top: -1px;
          font-size: 12px;
          color: #fff;
          background: linear-gradient(90deg, #F7D47E 0%, #E6B845 100%);
          border-radius: 3px 0 3px 0;
          padding: 0 3px;
        }
        .img_wrap {
          background: #F8F8F8;
          border-radius: 2px 2px 0 0;
  
          img {
            max-width: 100%;
            max-height: 100%;
          }
        }
  
        .goods_name {
          padding: 0px 10px;
          font-size: 13px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #333;
          line-height: 20px;
          height: 40px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          /*! autoprefixer: off */
          -webkit-box-orient: vertical;
          word-break: break-word;
          margin-top: 10px;
        }
  
        .price {
          font-size: 16px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #FC1C00;
          line-height: 22px;
          margin: 5px 10px 10px;
  
          .price_title {
            color: #AAAAAA;
            font-size: 13px;
            font-weight: 400;
            margin-right: 5px;
            white-space: nowrap;
          }
        }
  
        .supplier_price {
          font-size: 13px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #FC1C00;
          line-height: 22px;
          margin: 5px 10px 0;
  
          .supplier_title,
          .price_title {
            color: #AAAAAA;
            font-size: 12px;
            font-weight: 400;
            margin-right: 5px;
            white-space: nowrap;
          }
          .supplier_title {
            margin-left: 6px;
          }
        }
      }
    }
  }
}
.platform_goods_detail_overflow{
  max-height: calc(100vh - 48px - 88px);
  overflow: auto;
  padding-top: 20px;

}
.platform_goods_detail{
  width: 100%;
  .content{
    width: 1000px;
    .top_left{
      .main_img{
        width: 340px;
        height: 340px;
        border: 1px solid #eee;
        img{
          max-width: 100%;
          max-height: 100%;
        }
      }
      .small_img{
        margin-top: 10px;
        width: 293px;
        margin-left:25px;
        margin-bottom: 45px;
        :global{
          .slick-slide{
            width: 60px!important;
          }
          .slick-prev:before, .slick-next:before{
            color: #a3a3a3;
          }
        }
        .item_one{
          display: flex !important;
          height: 50px;
          margin-left: 9px;
          &:first-child{
            margin-left: 0;
          }
          img{
            width: 50px !important;
            max-width: 100%;
            max-height: 100%;
          }
        }
        .item{
          width: 50px !important;
          height: 50px;
          margin-left: 9px;
          border: 1px solid #eee;
          &:first-child{
            margin-left: 0;
          }
          img{
            max-width: 100%;
            max-height: 100%;
          }
        }
        
      }
    }
    .top_right{
      margin-left: 20px;
      width: 640px;
      .name{
        margin-top: 10px;
        font-size: 16px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #404040;
        line-height: 25px;
        margin-bottom: 20px;
      }
      .desc{
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #404040;
        line-height: 20px;
        margin-bottom: 20px;
      }
      .spec{
        .price{
          width: 640px;
          height: 81px;
          background-repeat:no-repeat;
          background-size: cover;
          padding-left: 20px;
          .tip{
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #666666;
            line-height: 21px;
          }
          .price_detail{
            color: #ED3142;
            font-weight: 500;
            font-family: PingFangSC-Medium, PingFang SC;
            .unit{
              font-size: 18px;
              letter-spacing: 1px;
            }
            .number{
              font-size: 25px;
            }
            .from_to{
              font-size: 20px;
              margin: 0 6px;
            ;
            }
          }
        }
        .spec_detail{
          padding: 15px 15px 0 15px;
          width: 640px;
          border: 1px solid #EBEBEB;
          border-top: 0;
          margin-top: -1px;
          margin-bottom: 24px;
          .tip{
            width: 44px;
            height: 20px;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #404040;
            line-height: 20px;
            flex-shrink: 0;
            margin-top: 11px;
          }
          .spec_item{
            height: 42px;
            background: #FFFFFF;
            border-radius: 2px;
            border: 1px solid #DFDFDF;
            padding: 0 10px;
            position: relative;
            flex-shrink: 0;
            margin-bottom: 15px;
            margin-left: 10px;
            cursor: pointer;
            .spec_name{
              height: 20px;
              font-size: 14px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #666666;
              line-height: 20px;
            }
            .spec_price{
              height: 20px;
              font-size: 14px;
              font-family: PingFangSC-Medium, PingFang SC;
              font-weight: 500;
              color: #333333;
              line-height: 20px;
              margin-left: 30px;
            }
            .selected_spec_item_icon{
              position: absolute;
              right: -1px;
              bottom: -1px;
              z-index: 2;
              line-height: 0;
              display: none;
            }
          }
          .selected_spec_item{
            border-color: #FE7F2D;
            display: flex !important;
          }
        }
        .freight_detail {
          width: 640px;
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          border: 1px solid #EBEBEB;
          border-top: none;
          border-bottom: none;
          margin-top: -1px;
          padding: 26px 15px 15px;
          .tip {
            width: 44px;
            height: 20px;
            color: #404040;
            line-height: 20px;
            flex-shrink: 0;
          }
          .title {
            color: #333333;
            line-height: 20px;
          }
          .more {
            position: relative;

            :global {
              ::-webkit-scrollbar {
                width: 5px;
                height: 10px;
                border-radius: 10px;
              }
              ::-webkit-scrollbar-thumb {
                display: block;
                width: 100%;
                cursor: pointer;
                border-radius: inherit;
                background-color: rgba(0, 0, 0, 0.2);
                transform: translateY(0px);
              }
              ::-webkit-scrollbar-track {
                border-radius: 10px;
              }
            }
            
            &:hover {
              .more_modal {
                display: block;
              }
            }
            .more_img {
              width: 25px;
              height: 25px;
              margin-left: 10px;
              padding: 5px;
              cursor: pointer;
            }
            .more_modal {
              display: none;
              position: absolute;
              top: 26px;
              left: 10px;
              z-index: 99999;
              width: 600px;
              max-height: 400px;
              overflow-x: auto;
              background: #FFFFFF;
              box-shadow: 0 0 9px 1px rgba(157, 157, 157, .1);

              .more_modal_list {
                border-top: 1px solid #DFDFDF;
                padding: 16px 15px;
                &:nth-child(1) {
                  border-top: none;
                }
                .more_modal_city {
                  flex: 1;
                  flex-wrap: wrap;
                  color: #666666;
                  font-size: 12px;
                  font-family: Microsoft YaHei;
                  font-weight: 400;
                  padding-right: 10px;
                  .more_modal_city_item {
                    margin: 2px 8px 2px 0;
                    padding: 3px;
                    border-radius: 3px;
                    cursor: pointer;
                    &:hover {
                      color: #ffffff;
                      background: #FA6F1E;
                    }
                  }
                }
                .more_modal_desc {
                  position: relative;
                  flex-shrink: 0;
                  width: 144px;
                  line-height: 20px;
                  color: #666666;
                  font-size: 12px;
                  font-family: Microsoft YaHei;
                  font-weight: 400;
                  text-align: center;
                  padding-left: 25px;
                  padding-right: 10px;
                  &:before {
                    display: block;
                    content: '';
                    position: absolute;
                    left: 0;
                    top: 50%;
                    margin-top: -10px;
                    width: 1px;
                    height: 20px;
                    background: #DFDFDF;
                  }
                }
              }
            }
          }
        }
      }
      .add_to_store{
        height: 40px;
        background: #FE7F2D;
        border-radius: 2px;
        padding: 0 18px;
        font-size: 15px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #FFF;
        line-height: 22px;
      }
      .add_to_storage{
        height: 40px;
        background: #FFF2EA;
        border-radius: 2px;
        border: 1px solid #F6BD98;
        padding: 0 18px;
        font-size: 15px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #FE7F2D;
        line-height: 22px;
        margin-left: 13px;
      }
    }
    .body_title{
      width: 100%;
      height: 50px;
      background: #F8F8F8;
      border: 1px solid #E2E2E2;
      .con{
        width: 160px;
        height: 48px;
        background: #FE7F2D;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #FFF;
      }
    }
    .body{
      width: 100%;
      min-height: 300px;
    }
  }
}

.goods_freight {
  .freight_detail {
    border: 1px solid #F0F0F0;
    .tip {
      width: 10%;
      height: 72px;
      line-height: 72px;
      color: #333333;
      font-size: 13px;
      font-weight: 600;
      text-align: right;
      padding-right: 10px;
      border-right: 1px solid #F0F0F0;
      flex-shrink: 0;
    }
    .title {
      color: rgba(0, 0, 0, 0.65);
      line-height: 72px;
      font-size: 13px;
      margin-left: 20px;
    }
    .more {
      position: relative;

      :global {
        ::-webkit-scrollbar {
          width: 5px;
          height: 10px;
          border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb {
          display: block;
          width: 100%;
          cursor: pointer;
          border-radius: inherit;
          background-color: rgba(0, 0, 0, 0.2);
          transform: translateY(0px);
        }
        ::-webkit-scrollbar-track {
          border-radius: 10px;
        }
      }
      
      &:hover {
        .more_modal {
          display: block;
        }
      }
      .more_img {
        width: 25px;
        height: 25px;
        margin-left: 10px;
        padding: 5px;
        cursor: pointer;
      }
      .more_modal {
        display: none;
        position: absolute;
        top: 26px;
        left: 10px;
        z-index: 99999;
        width: 700px;
        max-height: 400px;
        overflow-x: auto;
        background: #FFFFFF;
        box-shadow: 0 0 9px 1px rgba(157, 157, 157, .1);

        .more_modal_list {
          border-top: 1px solid #DFDFDF;
          padding: 16px 15px;
          &:nth-child(1) {
            border-top: none;
          }
          .more_modal_city {
            flex: 1;
            flex-wrap: wrap;
            color: #666666;
            font-size: 12px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            padding-right: 10px;
            .more_modal_city_item {
              margin: 2px 8px 2px 0;
              padding: 3px;
              border-radius: 3px;
              cursor: pointer;
              &:hover {
                color: #ffffff;
                background: #29c2ba;
              }
            }
          }
          .more_modal_desc {
            position: relative;
            flex-shrink: 0;
            width: 144px;
            line-height: 20px;
            color: #666666;
            font-size: 12px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            text-align: center;
            padding-left: 25px;
            padding-right: 10px;
            &:before {
              display: block;
              content: '';
              position: absolute;
              left: 0;
              top: 50%;
              margin-top: -10px;
              width: 1px;
              height: 20px;
              background: #DFDFDF;
            }
          }
        }
      }
    }
  }
}

.material_operate_bg {
  height: 40px;
  width: 100%;
  background: #f2f2f2;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 10px;
  padding-left: 15px;

  :global {
    .ant-input-search > .ant-input-suffix > .ant-input-search-button {
      height: 29px !important;
    }

    .ant-input-search.ant-input-search-enter-button > .ant-input {
      height: 29px !important;
    }
  }
}
.material_modal {
  .right_goods_item {
    flex-wrap: wrap;

    .item {
      position: relative;
      overflow: hidden;
      flex-shrink: 0;
      width: 144px;
      margin-left: 8px;
      margin-right: 8px;
      margin-bottom: 15px;

      &.active {
        .img_wrap {
          border-color: #FF701E;
        }
      }

      .img_wrap {
        width: 144px;
        height: 144px;
        position: relative;
        overflow: hidden;
        border: 1px solid #E0E0E0;
        border-radius: 4px;
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;

        .checked {
          position: absolute;
          top: 0;
          right: 0;
          z-index: 9;
          cursor: pointer;
          width: 22px;
          height: 22px;
        }

        .file_state {
          position: absolute;
          top: 0;
          left: 0;
          z-index: 9;
          height: 22px;
          line-height: 22px;
          cursor: default;
          text {
            padding-left: 4px;
            &.file_state_on {
              color: #ffffff;
            }
            &.file_state_off {
              color: #000000A6;
            }
          }
          img {
            position: absolute;
            left: 0;
            top: 0;
            z-index: -1;
            width: 58px;
            height: 22px;
          }
        }
        
        .file_size {
          position: absolute;
          bottom: 0;
          width: 100%;
          height: 26px;
          line-height: 26px;
          color: #fff;
          text-align: center;
          background: #00000085;
          cursor: default;
        }
      }

      .file_name {
        width: 144px;
        height: 34px;
        line-height: 18px;
        font-size: 13px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        margin-top: 5px;
        margin-bottom: 5px;
        padding-left: 6px;
        padding-right: 6px;
        display: -webkit-box;
        overflow: hidden;
        word-break: break-word;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        /* autoprefixer: off */
        -webkit-box-orient: vertical;
      }
      .file_category {
        width: 144px;
        line-height: 18px;
        font-size: 13px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        padding-left: 6px;
        padding-right: 6px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .item_btns {
        color: #FF701E;
        font-size: 13px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        white-space: nowrap;
        padding-left: 6px;
        padding-right: 6px;
        flex-wrap: wrap;
        
        span {
          margin-right: 8px;
          cursor: pointer;
        }
      }
    }
  }
}

.spin_height{
  height: calc(100vh - @header-height - 40px - 74px);
  display: flex;
    flex-direction: column;
}

.section_padding_back_scroll{
  .ant-spin-nested-loading{
    flex: 1;
    .ant-spin-container{
      height: 100%;
    }
  }
  .import_store_goods{
    flex: 1;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    min-height: 200px;
    overflow: hidden;
    .left_part{
       height: 100%;
    }
    .right_goods{
      align-items: stretch;
      .item_width{
        width: calc((100% - 50px) / 5);
      }
      .item_width_warp{
        width: 100%;
        height: calc(100% - 87px);
      }
    }
  }
}
