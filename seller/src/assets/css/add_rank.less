.sld_table_row_two {
  :global {
    .ant-form-item-control-wrapper {
      width: 100%;
    }
  }
}

.sld_det_lr_wrap {
  width: 1000px;
  padding-top: 1px;
}

.sld_det_lr_item_wrap {
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  justify-content: flex-start;
  min-width: 800px;
  border-left: 1px solid #f0f0f0;
}

.sld_det_r_item {
  padding-right: 10px;
  border: 1px solid #f0f0f0;
  border-left: 0;
  background-color: #fff;
}

.sld_det_r_text {
  color: rgb(0 0 0 / 85%);
  font-size: 13px !important;
  font-weight: 500;

  :global {
    .ant-form-item {
      margin-bottom: 0 !important;
    }
  }
}

.item {
  width: 100%;
}

.spec_wrap {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}

.spec_r_wrap {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;

  .spec_l {
    align-items: center;
    justify-content: flex-end;
    width: 100px;
    padding-top: 10px;
  }

  .spec_item_wrap {
    width: 100%;
    padding: 10px;
  }
}

.show_bot_border {
  border-bottom: 1px dashed #dcdcdc;
}

.word_break {
  word-wrap: break-word;
  word-break: break-all;
}

.add_rand_title_bg {
  width: 100%;
  height: 45px;
  border: 1px solid #f5f5f5;
  border-bottom-width: 0;
  background: #fffaf7;
  line-height: 45px;

  .title {
    padding-left: 15px;
    color: #323233;
    font-family: PingFangSC-Regular, 'PingFang SC';
    font-size: 14px;
    font-weight: 500;
  }
}

.add_rank_goods_wrap {
  width: 1000px;
  margin-top: 20px;
  border: 1px solid #eee;

  .add_rank_search_goods_wrap {
    width: 975px;
    margin-top: -5px;
    margin-right: 15px;
    margin-bottom: 15px;
  }

  .add_rank_goods_title {
    width: 100%;
    height: 45px;
    margin-bottom: 20px;
    padding-left: 15px;
    border-bottom: 1px solid #eee;
    line-height: 15px;

    .add_rank_goods_title_con,
    .add_rank_goods_title_bind_num_limit {
      color: #323233;
      font-family: PingFangSC-Regular, 'PingFang SC';
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;
    }

    .add_rank_goods_title_bind_num_limit {
      margin-left: 15px;
    }
  }
}
