html,
body,
#root {
  height: 100%;
}

.ant-layout {
  min-height: 100vh;
}

canvas {
  display: block;
}

body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizelegibility;
}

.globalSpin {
  width: 100%;
  margin: 40px 0 !important;
}

ul,
ol {
  list-style: none;
}

.rank_sync_icon {
  cursor: pointer;

  &:hover {
    .anticon {
      color: @primary-color !important;
    }
  }
}


.help_wrap {
  background: #FFF5F0;
  border-radius: 4px;
  padding: 10px 15px;
  width: 100%;
  color: #DB5609;
  font-size: 12px;
}

.tabs_bottom{
  .ant-tabs-nav{
    margin-bottom: 10px !important;
  }
}

.app_set_scroll {
  height: calc(100vh - @header-height - 54px - 20px - 22px - 32px - 24px);
  overflow: auto;
}

.goods_info {
  position: relative;

  .virtual_goods_flag {
    position: absolute;
    top: 1px;
    left: 1px;
    padding: 0 3px;
    border-radius: 3px 0;
    background: linear-gradient(90deg, #f7d47e 0%, #e6b845 100%);
    color: #fff;
    font-size: 12px;
  }

  .goods_detail {
    flex: 1;
    width: 169px;
    height: 80px;
    margin-left: 10px;
  }

  .goods_img {
    display: inline-block;
    width: 80px;
    height: 80px;
    overflow: hidden;
    border: 1px solid rgb(226 229 246 / 100%);
    border-radius: 3px;
    background: rgb(248 248 248 / 100%);
  }

  .goods_name {
    display: -webkit-box;
    flex-shrink: 0;
    height: 34px;
    overflow: hidden;
    color: #333;
    font-size: 13px;
    line-height: 17px;
    text-align: left;
    text-overflow: ellipsis;
    word-break: break-word;
    -webkit-line-clamp: 2;

    /*! autoprefixer: off */
    -webkit-box-orient: vertical;
  }

  .goods_brief {
    display: inline-block;
    width: 100%;
    overflow: hidden;
    color: #666;
    font-size: 12px;
    text-align: left;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

textarea.ant-input {
  resize: none;
}

.tableListForm {
  margin-top: 10px;
  margin-bottom: 5px;
  position: relative;
  .ant-form {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    padding-right: 180px;
  }
}

.SldScrollbars{
  height: 100%;
  width: 100%;
  overflow-y: auto;

}

//flex 常用布局
.flex_row_center_center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex_row_center_between {
  display: flex;
  align-items: space-between;
  justify-content: center;
}

.flex_row_center_around {
  display: flex;
  align-items: space-around;
  justify-content: center;
}

.flex_row_center_start {
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

.flex_row_center_end {
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.flex_row_between_center {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex_row_between_between {
  display: flex;
  align-items: space-between;
  justify-content: space-between;
}

.flex_row_between_around {
  display: flex;
  align-items: space-around;
  justify-content: space-between;
}

.flex_row_between_start {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.flex_row_between_end {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
}

.flex_row_around_center {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.flex_row_around_between {
  display: flex;
  align-items: space-between;
  justify-content: space-around;
}

.flex_row_around_around {
  display: flex;
  align-items: space-around;
  justify-content: space-around;
}

.flex_row_around_start {
  display: flex;
  align-items: flex-start;
  justify-content: space-around;
}

.flex_row_around_end {
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
}

.flex_row_start_center {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex_row_start_between {
  display: flex;
  align-items: space-between;
  justify-content: flex-start;
}

.flex_row_start_around {
  display: flex;
  align-items: space-around;
  justify-content: flex-start;
}

.flex_row_start_start {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}

.flex_row_start_end {
  display: flex;
  align-items: flex-end;
  justify-content: flex-start;
}

.flex_row_end_center {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.flex_row_end_between {
  display: flex;
  align-items: space-between;
  justify-content: flex-end;
}

.flex_row_end_around {
  display: flex;
  align-items: space-around;
  justify-content: flex-end;
}

.flex_row_end_start {
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
}

.flex_row_end_end {
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
}

.flex_column_center_center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.flex_column_center_between {
  display: flex;
  flex-direction: column;
  align-items: space-between;
  justify-content: center;
}

.flex_column_center_around {
  display: flex;
  flex-direction: column;
  align-items: space-around;
  justify-content: center;
}

.flex_column_center_start {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
}

.flex_column_center_end {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: center;
}

.flex_column_between_center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}

.flex_column_between_between {
  display: flex;
  flex-direction: column;
  align-items: space-between;
  justify-content: space-between;
}

.flex_column_between_around {
  display: flex;
  flex-direction: column;
  align-items: space-around;
  justify-content: space-between;
}

.flex_column_between_start {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
}

.flex_column_between_end {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
}

.flex_column_around_center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
}

.flex_column_around_between {
  display: flex;
  flex-direction: column;
  align-items: space-between;
  justify-content: space-around;
}

.flex_column_around_around {
  display: flex;
  flex-direction: column;
  align-items: space-around;
  justify-content: space-around;
}

.flex_column_around_start {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-around;
}

.flex_column_around_end {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-around;
}

.flex_column_start_center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}

.flex_column_start_between {
  display: flex;
  flex-direction: column;
  align-items: space-between;
  justify-content: flex-start;
}

.flex_column_start_around {
  display: flex;
  flex-direction: column;
  align-items: space-around;
  justify-content: flex-start;
}

.flex_column_start_start {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}

.flex_column_start_end {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-start;
}

.flex_column_end_center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
}

.flex_column_end_between {
  display: flex;
  flex-direction: column;
  align-items: space-between;
  justify-content: flex-end;
}

.flex_column_end_around {
  display: flex;
  flex-direction: column;
  align-items: space-around;
  justify-content: flex-end;
}

.flex_column_end_start {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-end;
}

.flex_column_end_end {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-end;
}

.com_flex_row_flex_start {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}

.com_flex_column {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.com_flex_column_space_between {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
}

.com_flex_column_center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.goods_sku_tab {
  :global {
    .ant-tabs {
      flex: 1;
    }

    .ant-tabs-bar {
      margin-bottom: 10px !important;
    }

    .ant-tabs.ant-tabs-card > .ant-tabs-bar .ant-tabs-nav-container {
      height: 35px !important;
    }

    .ant-tabs.ant-tabs-card > .ant-tabs-bar .ant-tabs-tab {
      padding: 0 15px !important;
      line-height: 35px !important;
    }

    .ant-form-item,
    .ant-form-inline .ant-form-item-with-help {
      margin-bottom: 0 !important;
    }
  }
}

.input_left_side_tip {
  display: inline-block;
  margin-right: 5px;
  color: rgb(0 0 0 / 65%);
}

.input_right_side_tip {
  display: inline-block;
  margin-left: 5px;
  color: rgb(0 0 0 / 65%);
}

.btn_right_side_tip {
  margin-left: -5px;
  color: #666;
  font-size: 13px;
  font-weight: 400;
  line-height: 20px;
}

.form_item_bottom_tip {
  margin-top: 2px;
  color: rgb(0 0 0 / 45%);
  font-size: 12px;
  line-height: 16px;
}

// 新
.ant-tabs-nav-wrap {
  .ant-tabs-tab {
    height: 32px !important;
    padding: 0 16px !important;
    color: rgb(0 0 0 / 65%) !important;
    font-size: 14px !important;
  }
}

.vben-basic-table {
  .basic-form {
    position: relative;

    & > .ant-row {
      padding-right: 240px !important;
    }

    .ant-form-item-control .ant-form-item-explain {
      position: absolute;
      z-index: 2;
      top: -10px;
      right: 5%;
      min-height: 20px;
      background: #fff;
      font-size: 13px;
      text-align: left;
    }
  }
}
.basic-form {
  position: relative;

  & > .ant-row {
    padding-right: 240px !important;
  }

  .ant-form-item-control .ant-form-item-explain {
    position: absolute;
    z-index: 2;
    top: -10px;
    right: 5%;
    min-height: 20px;
    background: #fff;
    font-size: 13px;
    text-align: left;
  }
}

.operate_bg {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: 40px;
  margin-top: 10px;
  margin-bottom: 10px;
  background: #ffede4;
}

.basic-form-list {
  margin-bottom: 5px !important;

  .from-search {
    position: static;
  }

  & > .ant-row {
    padding-right: 0 !important;
    padding-left: 0;
  }

  .form_item_table {
    border: 1px solid #f0f0f0;
    border-radius: 4px;

    .form_item_table_label {
      position: relative;
      max-width: 86px !important;
      height: 32px;
      padding-left: 8px;
      overflow: hidden;
      color: #555;
      font-size: 13px !important;
      line-height: 31px;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .ant-select-selection-placeholder {
      position: absolute;
      top: 50%;
      right: 9px;
      left: 0;
      max-width: 100%;
      height: 20px;
      margin-top: -10px;
      margin-left: 11px;
      overflow: hidden;
      color: #bfbfbf;
      line-height: 20px !important;
      text-align: left;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .ant-select-single:not(.ant-select-customize-input)
      .ant-select-selector
      .ant-select-selection-search-input {
      height: 32px;
    }

    .ant-select {
      font-size: 13px !important;
    }

    .ant-select-selector {
      border: none !important;
      box-shadow: none !important;
    }

    .ant-input-affix-wrapper {
      border: none !important;
      font-size: 13px !important;
    }

    .ant-picker {
      flex: 1;
      border: none !important;
      .ant-picker-input > input{
        font-size: 13px !important;
      }
    }

    .ant-input {
      flex: 1;
      border: none !important;
      line-height: 19px;
      font-size: 13px;
    }

    .ant-input:focus,
    .ant-input-focused {
      box-shadow: none !important;
    }

    .ant-input-number {
      flex: 1;
      border: none !important;
    }

    .ant-input-number:focus,
    .ant-input-number-focused {
      box-shadow: none !important;
    }

    .ant-picker-focused {
      box-shadow: none !important;
    }

    .ant-input-affix-wrapper:focus,
    .ant-input-affix-wrapper-focused {
      box-shadow: none !important;
    }
  }

  .table_search {
    .ant-btn-sm {
      padding-right: 0;
    }
  }
}

.vben-basic-form--compact .ant-form-item {
  margin-bottom: 8px !important;
}

.vben-basic-form .ant-form-item:not(.ant-form-item-with-help) {
  margin-bottom: 8px !important;
}

.com_line {
  width: 100%;
  background: #f2f2f2;
}

// 全局页面样式 start
.section_padding {
  padding: 10px;
}

.section_padding_back {
  height: calc(100vh - @header-height - 54px);
  padding: 10px;
  overflow: hidden;
  background: #fff;
}
// 全局页面样式 end

.section_padding_back_scroll_mark{
  height: calc(100vh - @header-height - 54px - 40px);
  padding: 10px;
  overflow: auto;
  background: #fff;
}

// 通屏
.min_height {
  min-height: calc(100vh - @header-height - 20px);
}


// 首页高度
.section_padding_home_scroll {
  max-height: calc(100vh - @header-height - 54px);
  overflow: auto;
  border-radius: 6px;
}

// 表格顶部操作按钮
.toolbar {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: 42px;
  margin-bottom: 5px;
  background: rgba(255, 126, 40, .1);
}
.toolbar_btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 30px;
  margin-left: 10px;
  padding: 0 7px;
  border-radius: 3px;
  background: #fff;
  cursor: pointer;

  span {
    margin-left: 5px;
  }
}
.toolbar_btn_opacity {
  cursor: not-allowed;

  svg {
    opacity: 0.3;
  }

  span {
    color: rgb(203 203 203);
  }
}

// 底部保存按钮
.m_diy_bottom_wrap {
  display: flex;
  position: absolute;
  z-index: 999;
  right: 10px;
  bottom: 0;
  left: 0;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  height: 60px;
  background: rgb(255 255 255 / 100%);
  box-shadow: 0 0 20px 0 rgb(187 187 187 / 15%);
}

.add_goods_bottom_btn {
  box-sizing: border-box;
  width: 80px;
  margin-right: 20px;
  border: 1px solid @primary-color;
  border-radius: 2px;
  color: @primary-color;
  font-size: 13px;
  line-height: 30px;
  text-align: center;
  cursor: pointer;
}

.add_goods_bottom_btn_rig {
  margin-right: 0;
}

.add_goods_bottom_btn_sel {
  margin-right: 0;
  background: @primary-color;
  color: #fff;
}

.goods_img_wrap_80 {
  width: 80px;
  height: 80px;
  overflow: hidden;
  border-radius: 3px;

  img {
    max-width: 100%;
    max-height: 100%;
  }
}

.ant-picker-suffix {
  line-height: 0 !important;
}

// 弹框样式
.ant-modal-header {
  padding: 12px 24px !important;
  background-color: @primary-color !important;
}

.ant-modal-title {
  color: #fff !important;
  font-size: 14px !important;
  line-height: 18px !important;
}

.ant-modal-close-x .anticon svg {
  fill: #fff;
}

.ant-modal-close {
  top: 12px !important;
  right: 20px !important;
  height: auto !important;

  .ant-modal-close-x {
    display: flex !important;
    align-items: center;
    justify-content: center;
    width: auto !important;
    height: auto !important;
    line-height: normal;
  }
}

.ant-modal .ant-modal-content {
  border-radius: 4px;
}

.ant-form-item-with-help {
  margin-bottom: 24px;
}

.ant-form-item-has-error .ant-input-number,
.ant-form-item-has-error .ant-picker {
  border-color: #ed6f6f !important;
}

.ant-upload-list-item-actions {
  display: flex !important;
  align-items: center;
  justify-content: center;

  .anticon-eye {
    display: flex !important;
    align-items: center;
    justify-content: center;
  }
}

.ant_form_ex {
  .ant-form-item-control {
    .ant-form-item-explain {
      position: absolute;
      z-index: 2;
      top: -10px;
      right: 2%;
      min-height: 20px;
      background: #fff;
      font-size: 13px;
      text-align: left;
    }
  }

  .ant-form-item-with-help {
    margin-bottom: 24px;
  }
}

.action_btn {
  color: @primary-color;
}

.primary_color {
  color: @primary-color;
}

.TableAction {
  flex-wrap: wrap;
}

// 详情页高度
.height_detail {
  max-height: calc(100vh - @header-height - 127px);
  overflow: auto;
}

// 入驻页高度
.apply_detail {
  max-height: calc(100vh - @header-height - 115px);
  overflow: auto;
}

.site_info_scroll {
  height: calc(100vh - @header-height - 54px - 20px - 22px - 12px);
  overflow: auto;
}

.form_list_img_item {
  display: inline-block;
  position: relative;
  margin-right: 10px;

  &:hover {
    .form_list_img_item_right {
      display: block;
    }
  }

  .form_list_img_item_img {
    max-width: 100px;
    max-height: 100px;
  }

  .form_list_img_item_right {
    display: none;
    position: absolute;
    z-index: 999;
    top: 0;
    left: 100%;
    width: 212px;
    max-height: 200px;

    &:hover {
      display: block;
    }
  }

  .form_list_img_item_pre {
    position: relative;
    max-width: 200px;
    max-height: 200px;
    margin-left: 12px;
    padding: 12px;
    border-radius: 4px;
    background: padding-box rgb(255 255 255);
    box-shadow: rgb(0 0 0 / 15%) 0 2px 8px;
    text-align: center;

    .form_list_img_item_pre_arrow {
      display: block;
      position: absolute;
      top: 12px;
      left: -4px;
      width: 8px;
      height: 8px;
      transform: rotate(45deg);
      border-width: 4px;
      border-style: solid;
      border-color: transparent transparent rgb(255 255 255) rgb(255 255 255);
      background: transparent;
      box-shadow: rgb(0 0 0 / 7%) -3px 3px 7px;
    }

    .form_list_img_item_pre_img {
      max-width: 100%;
      max-height: 100%;
    }
  }
}

.common_page_20 {
  width: 100%;
  padding: 20px;
  background: #fff;
}

.common_page_edit {
  position: relative;
  margin-right: 5px;
  padding-right: 5px;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    color: #ff6a12;
  }

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 0;
    width: 1px;
    height: 12px;
    margin-top: -5px;
    background: #ddd;
  }

  &:last-of-type {
    &::after {
      display: none;
    }
  }
}

.common_page {
  width: 100%;
  padding: 20px;
  background: #fff;



  &.common_page_toolbar {
    .vben-basic-table-form-container .ant-form {
      margin-bottom: 0;
    }

    .toolbar {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      width: 100%;
      height: 42px;
      margin-bottom: 5px;
      background: #ffe3d5;

      .toolbar_btn {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 30px;
        margin-left: 10px;
        padding: 0 7px;
        border-radius: 3px;
        background: #fff;
        cursor: pointer;

        span {
          margin-left: 5px;
        }
      }
    }
  }
}

.common_page_btn {
  display: flex;
  position: fixed;
  z-index: 999;
  right: 0;
  bottom: 0;
  left: 160px;
  align-items: center;
  justify-content: center;
  height: 50px;
  background: #fff;
  box-shadow: 0 0 20px 0 hsl(0deg 0% 73.3% / 15%);

  .common_page_btn_back,
  .common_page_btn_save {
    width: 80px;
    margin-right: 20px;
    border-radius: 2px;
    font-size: 13px;
    line-height: 30px;
    text-align: center;
    cursor: pointer;
  }

  .common_page_btn_back {
    border: 1px solid #fc701e;
    color: #fc701e;
  }

  .common_page_btn_save {
    margin-right: 0;
    border: 1px solid #fc701e;
    background: #fc701e;
    color: #fff;
  }
}

.goods_list_mainIamge {
  display: flex;
  position: relative;
  .isVirtua_flag {
    position: absolute;
    padding: 0 5px;
    background: @primary-color;
    color: #fff;
    font-size: 12px;
  }
  .goods_list_leftImage {
    display: inline-block;
    flex-shrink: 0;
    width: 80px;
    height: 80px;
    overflow: hidden;
    border: 1px solid #e2e5f6;
    border-radius: 3px;
    background: #f8f8f8;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
  }

  .goods_list_rightInfo {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: flex-start;
    justify-content: space-between;
    margin-left: 10px;
    padding: 1px 0;

    .goods_list_goodsname {
      display: -webkit-box;
      flex-shrink: 0;
      height: 34px;
      overflow: hidden;
      color: #333;
      font-size: 14px;
      line-height: 17px;
      text-align: left;
      text-overflow: ellipsis;
      word-break: break-word;
      white-space: normal;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .goods_list_extraninfo {
      color: #666;
      font-size: 12px;
      text-align: left;

      div,span {
          display: -webkit-box;
          flex-shrink: 0;
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: break-word;
          white-space: normal;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
      }
    }
  }
}

.goods_list_mainIamge_pop {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 200px;
  height: 200px;

  img {
    max-width: 100%;
    max-height: 100%;
  }
}

.common_description {
  .vben-collapse-container__header {
    border-bottom: none;
    cursor: default;

    .vben-basic-title {
      cursor: default;
    }
  }

  .ant-descriptions-row {
    display: flex;

    .ant-descriptions-item-label {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      justify-content: flex-end;
      width: 160px;
      padding: 12px 15px;
      background: #fff8f4;
    }

    .ant-descriptions-item-content {
      flex: 1;
    }
  }
}

.form_detail{
  .ant-form-item{
    margin-bottom: 6px !important;
  }
  .ant-form-item-explain{
    position: absolute;
    z-index: 2;
    top: -10px;
    right: 5%;
    min-height: 20px;
    background: #fff;
    font-size: 13px;
    text-align: left;
  }
}

.comm_line_sperator {
  margin-top: 15px;
  margin-bottom: 15px;
  border-top: 1px solid #eee;
}
.full_width {
  width: 100%;
}
.tableListFormAdd .ant-form {
  margin-top: 20px;
}
.tableListFormAdd :global .ant-form-inline .ant-form-item > .ant-form-item-label {
  width: 90px !important;
}

.right_instance {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}
.com_zoom {
  zoom: 0.8;
}
.instance_pre_a {
  display: inline-block;
  height: 36px;
}
.com_zoom_2 {
  zoom: 0.1;
}
.flex_com_row_wrap {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  flex-wrap: wrap;
}

.seller_logo {
  width: 130px;
  height: 40px;
  margin: 0 auto;
}
.seller_logo img {
  max-width: 100%;
  max-height: 100%;
}
.goods_modal_img {
  width: 30px;
  height: 30px;
  margin: 0 auto;
}
.goods_modal_img img {
  max-height: 100%;
  max-width: 100%;
}
.sld_top_tab_wrap .title_add_goods {
  position: absolute;
  z-index: 2;
}
.sld_tab_pl_150 .ant-tabs-nav-container {
  padding-left: 150px;
}
.sld_tab_pl_0 .ant-tabs-nav-container {
  padding-left: 0;
}
.goods_img_wrap_30 {
  width: 30px;
  height: 30px;
}
.goods_img_wrap_160 {
  width: 160px;
  height: 160px;
}
.goods_img_wrap_30 img,
.goods_img_wrap_160 img {
  max-width: 100%;
  max-height: 100%;
}
.input_after_wrap {
  cursor: pointer;
}
.goods_img_wrap_200 {
  width: 200px;
  height: 200px;
}
.goods_img_wrap_200 img {
  max-width: 100%;
  max-height: 100%;
}
.com_img_wrap img {
  max-width: 100%;
  max-height: 100%;
}
.btn_fixed_bottom {
  width: 135px;
  height: 44px;
  font-size: 13px !important;
  margin-left: 50px;
}
.clearfix {
  clear: both;
  overflow: hidden;
}

.sld_modal_top_tip{
  width: 100%;
  padding: 6px 15px;
  margin-bottom: 8px;
  background-color: #FFF9E2;
  position: absolute;
  top: 42px;
  left: 0;
}
.vben-basic-form .ant-form-item:not(.ant-form-item-with-help){
  margin-bottom: 10px !important;
}

.business_load_img {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  margin: auto;

  img {
    max-width: 100%;
    max-height: 100%;
  }
}
body{
  .vben-basic-table,.slodon-basic-table{
    .ant-table-wrapper{
      padding: 0px !important;
    }
  } 
  .vben-layout-content,.slodon-layout-content{

    .basic-form-list {
      display: block !important;
      padding-top: 10px !important;
      padding-right: 0px !important;
      padding-left: 0px !important;
      padding-bottom: 0 !important;
      .from-search {
        position: static;
      }
    
      & > .ant-row {
        padding-right: 0 !important;
        padding-left: 0;
      }
    
      .form_item_table {
        border: 1px solid #f0f0f0;
    
        .form_item_table_label {
          position: relative;
          max-width: 86px !important;
          height: 32px;
          padding-left: 8px;
          overflow: hidden;
          color: #555;
          font-size: 13px !important;
          line-height: 31px;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
    
        .ant-select-selection-placeholder {
          position: absolute;
          top: 50%;
          right: 9px;
          left: 0;
          max-width: 100%;
          height: 20px;
          margin-top: -10px;
          margin-left: 11px;
          overflow: hidden;
          color: #bfbfbf;
          line-height: 20px !important;
          text-align: left;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
    
        .ant-select-single:not(.ant-select-customize-input)
          .ant-select-selector
          .ant-select-selection-search-input {
          height: 32px;
        }
    
        .ant-select {
          font-size: 13px !important;
        }
    
        .ant-select-selector {
          border: none !important;
          box-shadow: none !important;
        }
    
        .ant-input-affix-wrapper {
          border: none !important;
          font-size: 13px !important;
        }
    
        .ant-picker {
          flex: 1;
          border: none !important;
          .ant-picker-input > input{
            font-size: 13px !important;
          }
        }
    
        .ant-input {
          flex: 1;
          font-size: 13px !important;
          border: none !important;
        }
    
        .ant-input:focus,
        .ant-input-focused {
          box-shadow: none !important;
        }
    
        .ant-input-number {
          flex: 1;
          border: none !important;
        }
    
        .ant-input-number:focus,
        .ant-input-number-focused {
          box-shadow: none !important;
        }
    
        .ant-picker-focused {
          box-shadow: none !important;
        }
    
        .ant-input-affix-wrapper:focus,
        .ant-input-affix-wrapper-focused {
          box-shadow: none !important;
        }
      }
    
      .table_search {
        .ant-btn-sm {
          padding-right: 0;
        }
      }
    }
  }
  .ant-tabs-top > .ant-tabs-nav{
    margin: 0;
  }
}
.vben-basic-form--compact .ant-form-item {
  margin-bottom: 8px !important;
}

.vben-basic-form .ant-form-item:not(.ant-form-item-with-help) {
  margin-bottom: 8px !important;
}

.slodon-basic-table-form-container .ant-form {
  padding-top: 0 !important;
  padding-right: 6px !important;
  padding-left: 6px !important;
  padding-bottom: 0 !important;
}

.ant-descriptions-bordered .ant-descriptions-item-label{
  background: rgb(248, 249, 250) !important;
}

.text_overflow_hidden {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
  white-space: normal !important;
  -webkit-line-clamp: 1;

  /*! autoprefixer: off */
  -webkit-box-orient: vertical;
}

.text_overflow_hidden_two {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
  white-space: normal !important;
  -webkit-line-clamp: 2;

  /*! autoprefixer: off */
  -webkit-box-orient: vertical;
}

.com_default_styles {
  flex-wrap: wrap;
  margin: 10px;

  .item {
    width: 50px;
    height: 40px;
    margin-right: 10px;

    img {
      max-width: 100%;
      max-height: 100%;
    }
  }

  .svideo_show_style_item {
    width: 112px;
    height: 112px;
    padding: 6px;
    border: 1px solid #fff;

    img {
      max-width: 100%;
      max-height: 100%;
    }
  }

  .notice_show_style_item {
    width: 376px;
    height: 43px;
    padding: 6px;
    border: 1px solid #fff;
    cursor: pointer;

    img {
      max-width: 100%;
      max-height: 100%;
    }
  }

  .notice_show_style_item:last-child{
    margin-top: 10px;
  }

  .svideo_show_style_item.sel_svideo_show_style,.notice_show_style_item.sel_svideo_show_style {
    border: 1px solid #429efd;
  }
}

.flex_com_row {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: center;
}

.flex_com_row_start_start {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
}

.flex_com_row_start_center {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}

.flex_com_row_center {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.flex_com_column {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}

.flex_com_column_center_flex_start {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
}

.flex_com_column_space_betweent_center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}

.flex_com_column_flex_end {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-end;
}

.flex_com_column_center_center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.flex_com{
  display: flex;
  flex-direction: column;
}

.flex_com_column_start_start {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}

.flex_com_space_between {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.flex_com_row_space_around_center {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;
}

.ant-message{
  span.anticon:not( .app-iconify, .anticon-vertical-align-top, .anticon-bell, .anticon-left, .anticon-right ){
    vertical-align: -2px !important;
  }
}

.ant-radio-group-small .ant-radio-button-wrapper{
  height: 27px!important;
  color: rgb(0 0 0 / 65%);
  font-size: 13px;
  line-height: 26px!important;
  &:first-child {
    border-radius: 4px 0 0 4px;
  }
  &:last-child {
    border-radius: 0 4px 4px 0;
  }
}
