<template>
  <div>
    <span v-if="prefix" :style="prefixStyle">
      {{ prefix }}
    </span>
    <span :style="{ color }">
      {{ value }}
    </span>
    <span v-if="suffix || isMoneyFormat" :style="suffixStyle">
      {{ isMoneyFormat ? valueUnit : suffix }}
    </span>
  </div>
</template>
<script lang="ts">
  import { getCurrentInstance, defineComponent, ref, computed, watchEffect, unref, onMounted, watch } from 'vue';
  import { useTransition, TransitionPresets } from '@vueuse/core';
  import { isNumber } from '/@/utils/is';

  const props = {
    startVal: { type: Number, default: 0 },
    endVal: { type: Number, default: 2021 },
    duration: { type: Number, default: 1500 },
    autoplay: { type: Boolean, default: true },
    decimals: {
      type: Number,
      default: 0,
      validator(value: number) {
        return value >= 0;
      },
    },
    prefix: { type: String, default: '' },
    prefixStyle: { type: Object, default: () => {} },
    suffix: { type: String, default: '' },
    suffixStyle: { type: Object, default: { fontSize: '17px' } },
    separator: { type: String, default: ',' },
    decimal: { type: String, default: '.' },
    isMoneyFormat: { type: Boolean, default: false },
    /**
     * font color
     */
    color: { type: String },
    /**
     * Turn on digital animation
     */
    useEasing: { type: Boolean, default: true },
    /**
     * Digital animation
     */
    transition: { type: String, default: 'linear' },
  };

  export default defineComponent({
    name: 'CountTo',
    props,
    emits: ['onStarted', 'onFinished'],
    setup(props, { emit }) {
        
      const vm = getCurrentInstance();
      const L = vm?.appContext.config.globalProperties.$sldComLanguage;

      const source = ref(props.startVal);
      const disabled = ref(false);
      let outputValue = useTransition(source);
      const valueUnit = ref('');

      const value = computed(() => {
        if (props.isMoneyFormat) {
          let tempValue = formatNumMoney(unref(outputValue));
          return formatNumber(tempValue);
        }
        return formatNumber(unref(outputValue));
      });

      watchEffect(() => {
        source.value = props.startVal;
      });

      watch([() => props.startVal, () => props.endVal], () => {
        if (props.autoplay) {
          start();
        }
      });

      onMounted(() => {
        props.autoplay && start();
      });

      function start() {
        run();
        source.value = props.endVal;
      }

      function reset() {
        source.value = props.startVal;
        run();
      }

      function run() {
        outputValue = useTransition(source, {
          disabled,
          duration: props.duration,
          onFinished: () => emit('onFinished'),
          onStarted: () => emit('onStarted'),
          ...(props.useEasing ? { transition: TransitionPresets[props.transition] } : {}),
        });
      }

      function formatNumber(num: number | string) {
        if (!num && num !== 0) {
          return '';
        }
        const { decimals, decimal, separator } = props;
        num = Number(num).toFixed(decimals);
        num += '';

        const x = num.split('.');
        let x1 = x[0];
        const x2 = x.length > 1 ? decimal + x[1] : '';

        const rgx = /(\d+)(\d{3})/;
        if (separator && !isNumber(separator)) {
          while (rgx.test(x1)) {
            x1 = x1.replace(rgx, '$1' + separator + '$2');
          }
        }
        return x1 + x2;
      }

      function formatNumMoney(num) {
        let target: any = num;
        if (num) {
          if (target >= 10000) {
            target = Number(target / 10000);
            valueUnit.value = `${L('万')}`;
          }
        } else {
          target = num;
        }
        return target;
      }

      return { value, start, reset, valueUnit, L };
    },
  });
</script>
