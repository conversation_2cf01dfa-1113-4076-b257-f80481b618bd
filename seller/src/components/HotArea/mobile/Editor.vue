<template>
  <div class="hot_area_editor">
    <div class="hot_area_item">
      <div class="subtitle">背景色</div>
      <div class="flex_row_start_center">
        <ColorPicker :color="bgColor" @update:color="handleColorUpdate" />
      </div>
    </div>
    <div class="hot_area_item">
      <div class="subtitle">添加图片</div>
      <div class="ha_item flex_row_start_start">
        <div class="img_wrapper flex_column_center_center" @click="openMaterial">
          <img :src="areaImg.imgUrl" alt="" v-if="areaImg.imgUrl" />
          <div class="img_placeholder flex_column_center_center" v-else>
            <AliSvgIcon iconName="iconjiahao" width="18px" height="18px" fillColor="#333" />
          </div>
        </div>
        <div class="ha_item_right">
          <Button type="primary" :disabled="isBtnDisabled" @click="setHotArea">设置热区</Button>
          <div class="mt-4 ha_text">已设置{{ areaData.length }}个热区</div>
        </div>
      </div>
    </div>
    <div class="hot_area_item">
      <div class="subtitle">边距</div>
      <div class="flex_row_start_center mb-3">
        <div class="item_label">上边距</div>
        <Slider v-model:value="paddingValue.top" :min="0" :max="20" class="w-40" @afterChange="sliderAfterChange">
        </Slider>
        <div class="item_px_label">{{ paddingValue.top }}&nbsp;px</div>
      </div>
      <div class="flex_row_start_center mb-3">
        <div class="item_label">下边距</div>
        <Slider v-model:value="paddingValue.bottom" :min="0" :max="20" class="w-40" @afterChange="sliderAfterChange">
        </Slider>
        <div class="item_px_label">{{ paddingValue.bottom }}&nbsp;px</div>
      </div>
      <div class="flex_row_start_center mb-3">
        <div class="item_label">左边距</div>
        <Slider v-model:value="paddingValue.left" :min="0" :max="20" class="w-40" @afterChange="sliderAfterChange">
        </Slider>
        <div class="item_px_label">{{ paddingValue.left }}&nbsp;px</div>
      </div>
      <div class="flex_row_start_center mb-3">
        <div class="item_label">右边距</div>
        <Slider v-model:value="paddingValue.right" :min="0" :max="20" class="w-40" @afterChange="sliderAfterChange">
        </Slider>
        <div class="item_px_label">{{ paddingValue.right }}&nbsp;px</div>
      </div>
    </div>

    <div class="hot_area_item">
      <div class="subtitle">圆角</div>
      <div class="flex_row_start_center mb-3">
        <div class="item_label">上圆角</div>
        <Slider v-model:value="borderRadiusValue.top" :min="0" :max="20" class="w-40" @afterChange="sliderAfterChange">
        </Slider>
        <div class="item_px_label">{{ borderRadiusValue.top }}&nbsp;px</div>
      </div>
      <div class="flex_row_start_center mb-3">
        <div class="item_label">下圆角</div>
        <Slider v-model:value="borderRadiusValue.bottom" :min="0" :max="20" class="w-40" @afterChange="sliderAfterChange">
        </Slider>
        <div class="item_px_label">{{ borderRadiusValue.bottom }}&nbsp;px</div>
      </div>
    </div>

    <SldMaterialImgs ref="sldMaterialImgs" @confirmMaterial="selectedMaterialImg" :max-upload-num="1" />
    <HotArea ref="hotAreaRef" :imgInfo="areaImg" :areaDataList="areaData" @confirm="hotAreaConfirm"></HotArea>
  </div>
</template>

<script setup>
import SldMaterialImgs from '/@/components/SldMaterialFiles/sldMaterialImgs.vue';
import { computed, inject, provide, reactive, ref, unref } from 'vue';
import { Button, Slider, message } from 'ant-design-vue';
import SvgIcon from '@/components/SvgIcon/index.vue';
import { resolveImageBindId } from '/@/utils';
import ColorPicker from '/@/components/ColorPicker/index.vue';
import HotArea from '../src/HotAreaModal.vue';
const sldMaterialImgs = ref();
const hotAreaRef = ref();

const props = defineProps({
  data: {
    type: Object,
    default: () => { },
  },
});

//校验器，提供给父组件在保存数据给接口时调用校验
const collectDecoValidator = inject('collectValidator');
collectDecoValidator?.(() => {
  let { img_info } = unref(combineEmitData);
  if (!img_info.imgUrl) {
    message.warn('请设置图片热区主图');
    return false;
  }
  return true;
});

const initialize = (type) => {
  switch (type) {
    case 'area_list': {
      return props.data.area_list;
    }
    case 'img_info': {
      let { img_info } = props.data;
      return {
        imgPath: img_info.imgPath,
        imgUrl: img_info.imgUrl,
        width: img_info.width,
        height: img_info.height,
      };
    }
    case 'bgColor': {
      let { style } = props.data;
      return style.bgColor ? style.bgColor : '#ffffff';
    }

    case 'padding':
    case 'borderRadius': {
      let { style } = props.data;
      let item = {};
      for (let k in style[type]) {
        item[k] = style[type][k] ? style[type][k] : 0;
      }
      return item;
    }
  }
};

const areaImg = ref(initialize('img_info'));

const areaData = ref(initialize('area_list'));

const bgColor = ref(initialize('bgColor'));

const emit = defineEmits(['handleCurSelData']);

const isBtnDisabled = computed(() => {
  return !unref(areaImg).imgUrl;
});

const paddingValue = reactive(initialize('padding'));

const borderRadiusValue = reactive(initialize('borderRadius'));

const combineEmitData = computed(() => ({
  area_list: unref(areaData),
  img_info: unref(areaImg),
  style: {
    padding: { ...paddingValue },
    borderRadius: { ...borderRadiusValue },
    bgColor: unref(bgColor),
  },
}));

/*
 * 输入框内容更改事件
 * val 组件传回来的值
 * type 修改值对应的键名
 * */
function onChange (val, type) {
  props.data[type] = val;
  emit('handleCurSelData', props.data);
}

const emitData = () => {
  emit('handleCurSelData', unref(combineEmitData));
};

const openMaterial = () => {
  let selectedData = { data: [], ids: [] };
  if (unref(areaImg).imgPath) {
    let fileData = {
      bindId: resolveImageBindId(unref(areaImg).imgPath),
      width: unref(areaImg).width,
      height: unref(areaImg).height,
      fileUrl: unref(areaImg).imgUrl,
      filePath: unref(areaImg).imgPath,
    };

    selectedData = {
      data: [fileData],
      ids: [fileData.bindId],
    };
    sldMaterialImgs.value.setMaterialModal(true, selectedData);
  } else {
    sldMaterialImgs.value.setMaterialModal(true, selectedData);
  }
};

const selectedMaterialImg = ({ data }) => {
  let [img] = data;
  areaImg.value = {
    imgUrl: img.fileUrl,
    imgPath: img.filePath,
    width: img.width,
    height: img.height,
  };
  emitData();
};

const setHotArea = () => {
  unref(hotAreaRef).open(true);
};

// 颜色选择器
const handleColorUpdate = (val) => {
  bgColor.value = 'rgba(' + val.r + ',' + val.g + ',' + val.b + ',' + val.a + ')';
  emitData();
};

const sliderAfterChange = () => {
  emitData();
};

const hotAreaConfirm = () => {
  emitData();
};
</script>

<style lang="less" scoped>
.hot_area_item {
  padding: 0 20px 20px;
  border-bottom: 5px solid #f6f7f9;

  .subtitle {
    color: #47565d;
    font-size: 13px;
    font-weight: bolder;
    line-height: 40px;
  }
}

.ha_item {
  padding: 10px;
  background-color: #f7f8fa;

  .ha_text {
    color: #333;
    font-size: 14px;
  }

  .ha_item_right {
    height: 100%;
    margin-left: 16px;
    padding-top: 5px;
  }
}

.item_label {
  margin-right: 10px;
  color: #666;
  font-size: 13px;
}

.item_px_label {
  width: 70px;
  height: 30px;
  margin-left: 5px;
  border-radius: 5px;
  background-color: #eee;
  line-height: 30px;
  text-align: center;
}

.img_wrapper {
  width: 80px;
  min-width: 80px;
  height: 80px;
  border: 1px solid #e5e5e5;
  background-color: #fff;
  cursor: pointer;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  &.grey {
    background-color: #f2f4f6;
  }

  &:hover {
    border: 1px dashed @primary-color;
  }

  .img_placeholder {
    color: @primary-color;
    font-size: 30px;
    font-weight: bold;
  }
}
</style>
