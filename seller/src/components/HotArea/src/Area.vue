<template>
  <div
    class="areaBox"
    :style="{
      width: move.areaWidth + 'px',
      height: move.areaHeight + 'px',
      left: move.startX + 'px',
      top: move.startY + 'px',
      minWidth: '50px',
      minHeight: '50px',
    }"
    @mousedown="mouseDown"
    @mouseup="mouseUp"
  >
    <span class="promptText">热区{{ id + 1 }}</span>
    <!--删除-->
    <div class="del" @click.stop="del" />

    <!--四周边缘区域，用作拉伸start-->
    <span
      ><div
        style="
          position: absolute;
          top: -5px;
          left: 0;
          width: 100%;
          height: 10px;
          cursor: row-resize;
          user-select: none;
        "
        @mousedown.stop="(e) => shapeDown(e, 'top')"
        @mouseup.stop="mouseUp"
      ></div
      ><div
        style="
          position: absolute;
          top: 0;
          right: -5px;
          width: 10px;
          height: 100%;
          cursor: col-resize;
          user-select: none;
        "
        @mousedown.stop="(e) => shapeDown(e, 'right')"
        @mouseup.stop="mouseUp($event)"
      ></div
      ><div
        style="
          position: absolute;
          bottom: -5px;
          left: 0;
          width: 100%;
          height: 10px;
          cursor: row-resize;
          user-select: none;
        "
        @mousedown.stop="(e) => shapeDown(e, 'bottom')"
        @mouseup.stop="mouseUp($event)"
      ></div
      ><div
        style="
          position: absolute;
          top: 0;
          left: -5px;
          width: 10px;
          height: 100%;
          cursor: col-resize;
          user-select: none;
        "
        @mousedown.stop="(e) => shapeDown(e, 'left')"
        @mouseup.stop="mouseUp($event)"
      ></div
      ><div
        style="
          position: absolute;
          top: -10px;
          right: -10px;
          width: 20px;
          height: 20px;
          cursor: ne-resize;
          user-select: none;
        "
        @mousedown.stop="(e) => shapeDown(e, 'top_right')"
        @mouseup.stop="mouseUp($event)"
      ></div
      ><div
        style="
          position: absolute;
          right: -10px;
          bottom: -10px;
          width: 20px;
          height: 20px;
          cursor: se-resize;
          user-select: none;
        "
        @mousedown.stop="(e) => shapeDown(e, 'bottom_right')"
        @mouseup.stop="mouseUp($event)"
      ></div
      ><div
        style="
          position: absolute;
          bottom: -10px;
          left: -10px;
          width: 20px;
          height: 20px;
          cursor: sw-resize;
          user-select: none;
        "
        @mousedown.stop="(e) => shapeDown(e, 'bottom_left')"
        @mouseup.stop="mouseUp($event)"
      ></div
      ><div
        style="
          position: absolute;
          top: -10px;
          left: -10px;
          width: 20px;
          height: 20px;
          cursor: nw-resize;
          user-select: none;
        "
        @mousedown.stop="(e) => shapeDown(e, 'top_left')"
        @mouseup.stop="mouseUp($event)"
      ></div
    ></span>
    <!--四周边缘区域，用作拉伸end-->
  </div>
</template>

<script setup>
  import { reactive, watch } from 'vue';
  const props = defineProps({
    areaInit: {
      type: Object,
      default: () => {},
    },
    id: {
      type: Number,
      default: null,
    },
  });

  const move = reactive(props.areaInit);
  const emit = defineEmits(['delAreaBox']);
  const mouseDown = (e) => {
    let startX = e.clientX;
    let startY = e.clientY;
    if (!document.onmousemove) {
      const initX = move.startX;
      const initY = move.startY;
      document.onmousemove = (ev) => {
        move.startX = initX + ev.clientX - startX;
        move.startY = initY + ev.clientY - startY;
      };
    }
  };
  // 结束拖动/变形
  const mouseUp = (e) => {
    document.onmousemove = null;
  };

  // 形变开始
  const shapeDown = (e, type) => {
    let start1X = e.clientX;
    let start1Y = e.clientY;
    if (!document.onmousemove) {
      const initX1 = move.areaWidth;
      const initY1 = move.areaHeight;

      const initX = move.startX;
      const initY = move.startY;

      document.onmousemove = (ev) => {
        let moveOffsetX = ev.clientX - start1X;
        let moveOffsetY = ev.clientY - start1Y;
        switch (type) {
          case 'left':
            //左
            move.startX = initX + moveOffsetX;
            move.areaWidth = initX1 - moveOffsetX;
            break;
          case 'top':
            //上
            move.startY = initY + moveOffsetY;
            move.areaHeight = initY1 - moveOffsetY;
            break;
          case 'right':
            //右
            move.areaWidth = initX1 + moveOffsetX;
            break;
          case 'bottom':
            //下
            move.areaHeight = initY1 + moveOffsetY;
            break;
          case 'top_left':
            //左上
            move.startX = initX + moveOffsetX;
            move.areaWidth = initX1 - moveOffsetX;
            move.startY = initY + moveOffsetY;
            move.areaHeight = initY1 - moveOffsetY;
            break;

          case 'bottom_left':
            //左下
            move.startX = initX + moveOffsetX;
            move.areaWidth = initX1 - moveOffsetX;
            move.areaHeight = initY1 + moveOffsetY;
            break;

          case 'top_right':
            //右上
            move.areaWidth = initX1 + moveOffsetX;
            move.startY = initY + moveOffsetY;
            move.areaHeight = initY1 - moveOffsetY;
            break;
          case 'bottom_right':
            //右下
            move.areaHeight = initY1 + moveOffsetY;
            move.areaWidth = initX1 + moveOffsetX;
            break;
        }
      };
    }
  };

  //删除
  const del = () => {
    emit('delAreaBox', props.areaInit.uid);
  };
</script>

<style scoped lang="scss" ref="stylesheet/scss">
  .areaBox {
    display: flex;
    position: absolute;
    align-items: center;
    justify-content: center;
    border: 0.7px dashed #34495e;
    background: rgba(#2980b9, 0.7);
    color: #34495e;
    font-size: 14px;
    cursor: move;

    .promptText {
      display: inline;
      max-width: 100%;
      max-height: 100%;
      overflow: hidden;
      color: #fff;
      text-align: center;
    }

    .del {
      position: absolute;
      z-index: 20;
      top: 0;
      right: 0;
      width: 10px;
      height: 10px;
      transform: translate3d(50%, -50%, 0);
      border-radius: 50%;
      background: #bdc3c7;
      cursor: pointer;
    }

    .del:hover {
      background: #ecf0f1;
    }

    .shape {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 7px;
      height: 7px;
      transform: translate3d(50%, 50%, 0);
      background: transparent;
      cursor: nwse-resize;
    }
  }
</style>
