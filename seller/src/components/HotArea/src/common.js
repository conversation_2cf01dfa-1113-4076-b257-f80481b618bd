

import { extraProperty } from '/@/utils';

export const handleDiyGoodsConfirm = (link_type, e) => {
  let chosen_val = Array.isArray(e) ? e[0] : e;

  let item = {}
  if (link_type == 'goods') {
    item.link_value = chosen_val.goodsName;
    item.info = extraProperty(chosen_val, [
      'defaultProductId',
      'actualSales',
      'productId',
      'goodsId',
      'goodsName',
      'goodsPrice',
      'mainImage',
    ]);
  }

  return item
}

export const MODAL_HEIGHT = window.innerHeight - 250;


export const DECO_DISPLAY_WIDTH_MOBILE = 371 //手机装修展示区域的宽度

export const EDIT_AREA_WIDTH = 1200 - 380 //装修时，图片热区编辑热区的modal，左侧图片容器宽度

export const DECO_DISPLAY_WIDTH_PC = 1210 //PC装修展示区域的宽度


export const EDIT_AREA_HEIGHT = MODAL_HEIGHT - 40 //PC、手机装修时，图片热区编辑热区的modal，左侧图片容器高度
