<template>
  <Menu
    v-bind="getBindValues"
    :activeName="activeName"
    :openNames="getOpenKeys"
    :class="prefixCls"
    :activeSubMenuNames="activeSubMenuNames"
    @select="handleSelect"
  >
    <template v-for="item in items" :key="item.path">
      <SimpleSubMenu
        :item="item"
        :parent="true"
        :collapsedShowTitle="collapsedShowTitle"
        :collapse="collapse"
      />
    </template>
    <div :style="{ height: getCollapsed ? '147px' : '170px' }"></div>
    <div
      v-if="system_tel || system_email"
      class="menu_fixed_link flex_column_center_start"
      :style="{ width: getRealWidth + 'px', height: getCollapsed ? '62px' : '92px'  }">
      <template v-if="getCollapsed">
        <div class="hide_left flex_column_center_center">
          <div class="hide_left_icon flex_row_center_center">
            <img src="@/assets/images/slide_icon_2.png" :title="system_tel">
          </div>
          <div class="hide_left_icon flex_row_center_center">
            <img src="@/assets/images/slide_icon_1.png" :title="system_email">
          </div>
        </div>
      </template>
      <template v-else>
        <div class="link_title">联系方式</div>
        <div class="link_item flex_row_start_center" v-if="system_tel">
          <img class="left_icon" src="@/assets/images/slide_icon_2.png">
          <div class="right_label" :title="system_tel">{{ system_tel }}</div>
        </div>
        <div class="link_item flex_row_start_center" v-if="system_email">
          <img class="left_icon" src="@/assets/images/slide_icon_1.png">
          <div class="right_label" :title="system_email">{{ system_email }}</div>
        </div>
      </template>
    </div>
  </Menu>
</template>
<script lang="ts">
  import type { MenuState } from './types';
  import type { Menu as MenuType } from '/@/router/types';
  import type { RouteLocationNormalizedLoaded } from 'vue-router';
  import { defineComponent, computed, ref, unref, reactive, toRefs, watch, PropType, onMounted } from 'vue';
  import { useDesign } from '/@/hooks/web/useDesign';
  import Menu from './components/Menu.vue';
  import SimpleSubMenu from './SimpleSubMenu.vue';
  import { listenerRouteChange } from '/@/logics/mitt/routeChange';
  import { propTypes } from '/@/utils/propTypes';
  import { REDIRECT_NAME } from '/@/router/constant';
  import { useRouter } from 'vue-router';
  import { isFunction, isUrl } from '/@/utils/is';
  import { openWindow } from '/@/utils';
  import { useOpenKeys } from './useOpenKeys';
  import { getSettingListApi } from '/@/api/common/common';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';

  export default defineComponent({
    name: 'SimpleMenu',
    components: {
      Menu,
      SimpleSubMenu,
    },
    inheritAttrs: false,
    props: {
      items: {
        type: Array as PropType<MenuType[]>,
        default: () => [],
      },
      collapse: propTypes.bool,
      mixSider: propTypes.bool,
      theme: propTypes.string,
      accordion: propTypes.bool.def(true),
      collapsedShowTitle: propTypes.bool,
      beforeClickFn: {
        type: Function as PropType<(key: string) => Promise<boolean>>,
      },
      isSplitMenu: propTypes.bool,
    },
    emits: ['menuClick'],
    setup(props, { attrs, emit }) {
      const { getCollapsed, getRealWidth } = useMenuSetting();
      const currentActiveMenu = ref('');
      const isClickGo = ref(false);

      const menuState = reactive<MenuState>({
        activeName: '',
        openNames: [],
        activeSubMenuNames: [],
      });

      const { currentRoute } = useRouter();
      const { prefixCls } = useDesign('simple-menu');
      const { items, accordion, mixSider, collapse } = toRefs(props);

      const { setOpenKeys, getOpenKeys } = useOpenKeys(
        menuState,
        items,
        accordion,
        mixSider as any,
        collapse as any,
      );

      const getBindValues = computed(() => ({ ...attrs, ...props }));

      const system_tel = ref('');
      const system_email = ref('');

      watch(
        () => props.collapse,
        (collapse) => {
          if (collapse) {
            menuState.openNames = [];
          } else {
            setOpenKeys(currentRoute.value.path);
          }
        },
        { immediate: true },
      );

      watch(
        () => props.items,
        () => {
          if (!props.isSplitMenu) {
            return;
          }
          setOpenKeys(currentRoute.value.path);
        },
        { flush: 'post' },
      );

      listenerRouteChange((route) => {
        if (route.name === REDIRECT_NAME) return;

        currentActiveMenu.value = route.meta?.currentActiveMenu as string;
        handleMenuChange(route);

        if (unref(currentActiveMenu)) {
          menuState.activeName = unref(currentActiveMenu);
          setOpenKeys(unref(currentActiveMenu));
        }
      });

      async function handleMenuChange(route?: RouteLocationNormalizedLoaded) {
        if (unref(isClickGo)) {
          isClickGo.value = false;
          return;
        }
        const path = (route || unref(currentRoute)).path;

        menuState.activeName = path;

        setOpenKeys(path);
      }

      async function handleSelect(key: string) {
        if (isUrl(key)) {
          openWindow(key);
          return;
        }
        const { beforeClickFn } = props;
        if (beforeClickFn && isFunction(beforeClickFn)) {
          const flag = await beforeClickFn(key);
          if (!flag) return;
        }

        emit('menuClick', key);

        isClickGo.value = true;
        setOpenKeys(key);
        menuState.activeName = key;
      }

      const get_system_info = async () => {
        const res = await getSettingListApi({ str: 'basic_site_phone,basic_site_email' });
        if (res.state == 200) {
          res.data.map(item => {
            if (item.name == 'basic_site_phone') {
              system_tel.value = item.value;
            } else if (item.name == 'basic_site_email') {
              system_email.value = item.value;
            }
          });
        }
      };

      onMounted(() => {
        get_system_info()
      });

      return {
        getCollapsed,
        getRealWidth,
        prefixCls,
        getBindValues,
        handleSelect,
        getOpenKeys,
        ...toRefs(menuState),
        system_tel,
        system_email,
      };
    },
  });
</script>
<style lang="less">
  @import url('./index.less');

  .menu_fixed_link {
    position: fixed;
    left: 0;
    bottom: 36px;
    z-index: 9;
    height: 92px;
    padding-top: 8px;
    padding-bottom: 8px;
    background: #fff;

    .link_title {
      font-size: 13px;
      margin-left: 12px;
      margin-bottom: 6px;
    }
    .link_item {
      font-size: 12px;
      margin-top: 3px;
      margin-left: 12px;
      .left_icon {
        width: 16px;
      }
      .right_label {
        margin-left: 6px;
      }
    }
    .hide_left {
      width: 100%;
      .hide_left_icon {
        height: 23px;
        img {
          width: 16px;
        }
      }
    }
  }
</style>
