import SldSelGoodsSingleDiy from './index.vue';
import { extraProperty } from '/@/utils';
// dev_supplier-start
import { useUserStore } from '/@/store/modules/user';
const userStore = useUserStore();
// dev_supplier-end

export { SldSelGoodsSingleDiy };

export const handleDiyGoodsConfirm = (link_type, e) => {
  let chosen_val = Array.isArray(e) ? e[0] : e;
  let item = {}
  if (link_type == 'goods') {
    item.link_value = chosen_val.goodsName;
    item.info = extraProperty(chosen_val, [
      'actualSales',
      'productId',
      'goodsId',
      'goodsName',
      'goodsPrice',
      'mainImage',
      'defaultProductId'
    ]);
  }

  return item
}

//选择器选择到需要弹窗的选项时的设置属性
export const setSldSelProps = (type) => {
  const modalProps = {
    searchInfo: {},
    link_type: type
  };
  return modalProps;
};
