<!-- 公共头部组件 -->
<template>
  <div>
    <Modal
      destroyOnClose
      :confirmLoading="confirmLoading"
      :maskClosable="false"
      :title="title ? title : L('选择器')"
      :visible="modalVisible"
      :width="modalWidth"
      @ok="sldConfirm"
      @cancel="sldCancle"
      :footer="props.look ? null : undefined"
    >
      <div class="common_page sld_sel_goods_single_diy goods_list_mainIamge" :style="{ paddingTop: modalPaddingTop+'px' }">
        <BasicTable
          @register="registerTable"
          @change="change"
          @selection-change="selectionChange"
          @fetch-success="onFetchSuccess"
          :tableFlag="false"
        >
          <template #bodyCell="data">
            <slot name="bodyCell" v-bind="data || {}"></slot>
            <template v-if="data.column.dataIndex == 'mainImage'">
              <Popover placement="right">
                <template #content>
                  <div class="goods_list_mainIamge_pop">
                    <img :src="data.text" />
                  </div>
                </template>
                <div
                  class="goods_list_leftImage"
                  :style="{ backgroundImage: `url('${data.text}')` }"
                ></div>
              </Popover>
            </template>
          </template>
        </BasicTable>
      </div>
    </Modal>
  </div>
</template>
<script setup>
  import { getCurrentInstance, ref, computed, watch } from 'vue';
  import { validatorEmoji } from '/@/utils/validate';
  import { Modal, Popover } from 'ant-design-vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import {
    getCouponNormalListApi,
    getSellerGoodsListApi,
    getSellerExpressListApi,
  } from '/@/api/sldSelGoodsSingleDiy/index';

  // dev_supplier-start
  import { useUserStore } from '/@/store/modules/user';
  const userStore = useUserStore();
  // dev_supplier-end

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const emit = defineEmits(['cancleEvent', 'confirmEvent']);

  const props = defineProps({
    modalTitle: { type: String, default: '' }, //弹窗标题
    link_type: { type: String, default: '' }, //类型
    look: { type: Boolean, required: false, default: false }, //仅查看
    confirmLoading: { type: Boolean, required: false, default: false }, //确定按钮 loading
    modalVisible: { type: Boolean, required: false, default: false }, //弹窗开关
    modalWidth: { type: Number, required: false, default: 900 }, //弹窗开关
    api: { type: Function, required: false, default: () => null }, //请求接口
    dataSource: { type: Array, required: false, default: [] }, //静态数据
    selectedRowKeys: {
      type: Array,
      required: true,
      default: () => [],
    }, //选中行的id
    selectedRows: {
      type: Array,
      required: true,
      default: () => [],
    }, //选中行的数据
    column: {
      type: Array,
      required: true,
      default: () => [],
    }, //表单列信息
    formConfig: {
      type: Array,
      required: false,
      default: () => [],
    }, //表单配置
    searchInfo: {
      type: Object,
      required: false,
      default: () => {},
    }, //额外的请求参数
    rowId: { type: [Number, String], default: '' }, //类型
    checkedType: { type: Boolean, required: false, default: false }, //传接口信息时多选必传 不传就是单选
    pagination: { type: Boolean, required: false, default: true }, //分页是否展示
    formCompact: { type: Boolean, required: false, default: true }, //表单是否为紧凑模式
    modalPaddingTop: { type: Number, default: 0 },
  });

  //由于watch无法监听modalVisible，所以需要计算属性转为ref对象
  const modalVisible = computed(() => {
    return props.modalVisible;
  });
  const confirmLoading = computed(() => {
    return props.confirmLoading;
  });
  const modalTitle = computed(() => {
    return props.modalTitle;
  });
  const modalPaddingTop = computed(() => {
    return props.modalPaddingTop;
  });
  const title = ref('');
  const data = ref([]); //表格的数据
  const searchInfos = ref({});
  const selectedRows_row = ref([]);
  const selectedKeys = ref([]);
  const selectedRows_info = ref([]);
  const selectedRowKeys_info = ref([]);
  const rowKey = ref(''); //table 行唯一标识
  const columns = ref([]);
  const rowSelection = ref({});
  const search_data = ref([
    //筛选器
    {
      component: 'Input',
      label: L('活动名称'),
      field: 'drawName',
      colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
      componentProps: {
        placeholder: L('请输入活动名称'),
      },
      rules: [
        {
          // @ts-ignore
          validator: async (rule, value) => {
            await validatorEmoji(rule, value);
          },
          trigger: 'change',
        },
      ],
    },
  ]);

  // 商品
  const goods_columns = ref([
    {
      title: L('商品图片'),
      dataIndex: 'mainImage',
      align: 'center',
      width: 100,
    },
    {
      title: L('商品名称'),
      dataIndex: 'goodsName',
      align: 'center',
      width: 100,
    },
    {
      title: L('商品价格(元)'),
      dataIndex: 'goodsPrice',
      align: 'center',
      width: 100,
    },
    {
      title: L('销量'),
      dataIndex: 'actualSales',
      align: 'center',
      width: 100,
    },
  ]);

  // 优惠券
  const voucher_columns = ref([
    {
      title: L('优惠券名称'),
      dataIndex: 'couponName',
      align: 'center',
      width: 100,
    },
    {
      title: L('优惠券类型'),
      dataIndex: 'couponTypeValue',
      align: 'center',
      width: 100,
    },
    {
      title: L('优惠券内容'), //优惠券内容
      dataIndex: 'couponContent',
      align: 'center',
      width: 100,
    },
    {
      title: L('未领取数量'), //未领取数量
      dataIndex: 'remainNum',
      align: 'center',
      width: 100,
    },
  ]);

  // 可以添加的平台物流公司列表
  const express_columns = ref([
    {
      title: L('物流名称'),
      dataIndex: 'expressName',
      align: 'center',
      width: 100,
    },
    {
      title: L('电子面单'),
      dataIndex: 'isSupportFaceSheet',
      align: 'center',
      width: 100,
      customRender: ({ text }) => {
        return text ? L('支持') : L('不支持');
      },
    },
  ]);

  watch(
    modalVisible,
    () => {
      if (modalVisible.value) {
        title.value = modalTitle.value;
        selectedRows_row.value = []; //选中的行数据
        selectedKeys.value = []; //选中的行id
        if (props.selectedRows.length > 0) {
          selectedRows_row.value = JSON.parse(JSON.stringify(props.selectedRows));
        }
        if (props.selectedRowKeys.length > 0) {
          selectedKeys.value = JSON.parse(JSON.stringify(props.selectedRowKeys));
        }
        columns.value = [];
        if (props.link_type == 'voucher') {
          let api_info = getCouponNormalListApi;
          if (props.link_type == 'voucher') {
            // 优惠券
            columns.value = voucher_columns.value;
            title.value = L('选择优惠券');
            search_data.value = [
              {
                component: 'Input',
                label: L('优惠券名称'), //优惠券名称
                field: 'couponName',
                labelWidth: 87,
                colProps: { span: 6, style: 'width:250px !important;max-width:250px;flex:none !important;' },
                componentProps: {
                  placeholder: L('请输入优惠券名称'),
                },
                rules: [
                  {
                    // @ts-ignore
                    validator: async (rule, value) => {
                      await validatorEmoji(rule, value);
                    },
                    trigger: 'change',
                  },
                ],
              },
            ];
            rowKey.value = 'couponId';
            rowSelection.value = props.checkedType
              ? {
                  //单选多选
                  type: 'checkbox',
                }
              : {
                  type: 'radio',
                };
          }
          setTimeout(() => {
            setProps({
              api: api_info,
              rowKey: rowKey.value,
              columns: columns.value,
              rowSelection: rowSelection.value,
              showIndexColumn: true,
              // 搜索内容
              formConfig: {
                labelWidth: 75,
                schemas: search_data.value,
              },
            });
            if (props.selectedRowKeys.length > 0 && props.checkedType) {
              setSelectedRowKeys(props.selectedRowKeys);
            }
            reload();
          });
        } else if (props.link_type == 'goods') {
          let api_info = getSellerGoodsListApi;
          let search_info = props.searchInfo ? props.searchInfo : {}
          if (!search_info.state) {
            search_info.state = '3'
          }
          if (props.link_type == 'goods') {
            // 商品
            let goodsColumns = JSON.parse(JSON.stringify(goods_columns.value))
            // dev_supplier-start
            if(userStore.getStoreInfo.shopType&&userStore.getStoreInfo.shopType=='3'){
              goodsColumns.forEach(item=>{
                if(item.dataIndex == 'goodsPrice'){
                  item.dataIndex = 'wholesalePrice'
                }
              })
              search_info = {...search_info,saleModel:'2'}
            }
            // dev_supplier-end
            columns.value = goodsColumns;
            title.value = L('选择商品');
            search_data.value = [
              {
                component: 'Input',
                label: L('商品名称'), //优惠券名称
                field: 'goodsName',
                colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
                componentProps: {
                  placeholder: L('请输入商品名称'),
                },
                rules: [
                  {
                    // @ts-ignore
                    validator: async (rule, value) => {
                      await validatorEmoji(rule, value);
                    },
                    trigger: 'change',
                  },
                ],
              },
            ];
            rowKey.value = 'goodsId';
            rowSelection.value = props.checkedType
              ? {
                  //单选多选
                  type: 'checkbox',
                }
              : {
                  type: 'radio',
                };
          }
          setTimeout(() => {
            setProps({
              api: api_info,
              rowKey: rowKey.value,
              searchInfo: search_info ? search_info : {},
              columns: columns.value,
              rowSelection: rowSelection.value,
              showIndexColumn: true,
              // 搜索内容
              formConfig: {
                labelWidth: 75,
                schemas: search_data.value,
              },
            });
            if (props.selectedRowKeys.length > 0 && props.checkedType) {
              setSelectedRowKeys(props.selectedRowKeys);
            }
            reload();
          });
        } else if (props.link_type == 'express_company') {
          let api_info = getSellerExpressListApi;
          if (props.link_type == 'express_company') {
            // 商品
            columns.value = express_columns.value;
            title.value = props.modalTitle ? props.modalTitle : L('添加物流公司');
            search_data.value = [
              {
                component: 'Input',
                label: L('物流名称'), //物流名称
                field: 'expressName',
                colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
                componentProps: {
                  placeholder: L('请输入物流名称'),
                },
                rules: [
                  {
                    // @ts-ignore
                    validator: async (rule, value) => {
                      await validatorEmoji(rule, value);
                    },
                    trigger: 'change',
                  },
                ],
              },
            ];
            rowKey.value = 'expressId';
            rowSelection.value = {
              type: 'checkbox',
            };
          }
          setTimeout(() => {
            setProps({
              api: api_info,
              rowKey: rowKey.value,
              columns: columns.value,
              rowSelection: rowSelection.value,
              showIndexColumn: true,
              // 搜索内容
              formConfig: {
                labelWidth: 75,
                schemas: search_data.value,
              },
            });
            reload();
          });
        } else {
          props.column.forEach((item) => {
            if (!item.customRender) {
              item.customRender = ({ text }) => {
                return text !== undefined && text !== null ? text : '--';
              };
            }
          });
          setTimeout(() => {
            rowKey.value = props.rowId;
            if (props.dataSource && props.dataSource.length > 0) {
              setProps({
                dataSource: props.dataSource,
                rowKey: props.rowId, //id
                columns: props.column,
                // 参数
                searchInfo: props.searchInfo ? props.searchInfo : {},
                useSearchForm: props.formConfig.length > 0 ? true : false, //搜索开关
                // 搜索内容
                formConfig: {
                  labelWidth: 75,
                  schemas: props.formConfig,
                },
                rowSelection: props.look
                  ? undefined
                  : props.checkedType
                  ? {
                      //单选多选
                      type: 'checkbox',
                    }
                  : {
                      type: 'radio',
                    },
              });
            } else {
              setProps({
                api: props.api,
                rowKey: props.rowId, //id
                columns: props.column,
                // 参数
                searchInfo: props.searchInfo ? props.searchInfo : {},
                useSearchForm: props.formConfig.length > 0 ? true : false, //搜索开关
                // 搜索内容
                formConfig: {
                  labelWidth: 75,
                  schemas: props.formConfig,
                },
                rowSelection: props.look
                  ? undefined
                  : props.checkedType
                  ? {
                      //单选多选
                      type: 'checkbox',
                    }
                  : {
                      type: 'radio',
                    },
              });
            }
            if (props.selectedRowKeys.length > 0) {
              setSelectedRowKeys(props.selectedRowKeys);
            }
            reload();
          });
        }
      }
    },
    { deep: true },
  );

  // 表格数据
  const [
    registerTable,
    { reload, setProps, getSelectRowKeys, setSelectedRowKeys, getDataSource, clearSelectedRowKeys },
  ] = useTable({
    // 请求接口
    rowKey: props.link_type ? rowKey.value : props.rowId,
    immediate: false,
    columns: props.link_type ? columns.value : props.column,
    // 参数
    searchInfo: props.link_type ? () => searchInfos.value : () => props.searchInfo,
    // 点击搜索前处理的参数
    beforeFetch(values) {
      values.startTime = values.startTime
        ? values.startTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
      return values;
    },
    maxHeight: 300,
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      compact: props.formCompact,
      schemas: !props.link_type ? props.formConfig : search_data.value,
    },
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    pagination: props.pagination,
    // 是否显示序号列
    showIndexColumn: true,
    // 表格右侧操作列配置
  });
  const sldConfirm = () => {
    // 选中行：getSelectRows()
    let record;
    let recordId;
    let modalTableSeleData = {};
    let modalTableSeleDataIds = {};
    if (props.link_type == 'voucher') {
      if (props.checkedType) {
        modalTableSeleData = [];
        modalTableSeleData = selectedRows_row.value;
        modalTableSeleDataIds = selectedKeys.value;
      } else {
        record = selectedRows_row.value[0];
        recordId = selectedKeys.value;
        modalTableSeleData.couponId = record.couponId;
        modalTableSeleData.couponName = record.couponName;
        modalTableSeleData.couponContent = record.couponContent;
        modalTableSeleData.publishNum = record.publishNum;
        modalTableSeleData.publishValue = record.publishValue;
        modalTableSeleData.remainNum = record.remainNum;
        modalTableSeleData.couponType = record.couponType;
        modalTableSeleData.randomMin = record.randomMin;
        modalTableSeleData.randomMax = record.randomMax;
        modalTableSeleDataIds = recordId;
      }
    } else if (props.link_type == 'goods') {
      if (props.checkedType) {
        modalTableSeleData = selectedRows_row.value;
        modalTableSeleDataIds = selectedKeys.value;
      } else {
        record = selectedRows_row.value[0];
        recordId = selectedKeys.value;
        modalTableSeleData.goodsId = record.goodsId;
        modalTableSeleData.goodsName = record.goodsName;
        modalTableSeleData.goodsPrice = record.goodsPrice;
        modalTableSeleData.actualSales = record.actualSales;
        modalTableSeleData.mainImage = record.mainImage;
        modalTableSeleData.productId = record.productId;
        modalTableSeleDataIds = recordId;
        // dev_supplier-start
        if(userStore.getStoreInfo.shopType&&userStore.getStoreInfo.shopType=='3'){
          modalTableSeleData.goodsPrice = record.wholesalePrice;
          modalTableSeleData.wholesalePrice = record.wholesalePrice;
        }
        // dev_supplier-end
      }
    } else if (props.link_type == 'express_company') {
      record = selectedRows_row.value[0];
      recordId = selectedKeys.value;
      modalTableSeleData = selectedRows_row.value;
      modalTableSeleDataIds = recordId;
    } else if (!props.link_type) {
      setSelectedRowKeys(selectedKeys.value);
      modalTableSeleData = selectedRows_row.value;
      modalTableSeleDataIds = selectedKeys.value;
    }
    emit('confirmEvent', modalTableSeleData, modalTableSeleDataIds);
  };

  const change = (e) => {
    if (props.checkedType && !props.look) {
      clearSelectedRowKeys();
      setSelectedRowKeys(selectedKeys.value);
      selectedRows_info.value = JSON.parse(JSON.stringify(selectedRows_row.value));
      selectedRowKeys_info.value = JSON.parse(JSON.stringify(getSelectRowKeys()));
      selectedKeys.value = selectedRowKeys_info.value;
    }
  };

  const onFetchSuccess = (e) => {
    if (props.look) {
      return;
    }
    data.value = [];
    getDataSource().forEach((item) => {
      data.value.push(item[rowKey.value]);
    });
  };

  const selectionChange = (e) => {
    if (props.look) {
      return;
    }
    if (props.checkedType) {
      //针对翻页无法保存选择的行数据处理
      let selectedRowKey = JSON.parse(JSON.stringify(selectedKeys.value));
      for (let i in data.value) {
        if (selectedKeys.value.indexOf(data.value[i]) != -1) {
          selectedKeys.value.splice(selectedKeys.value.indexOf(data.value[i]), 1);
        }
      }
      selectedKeys.value = [
        ...new Set(selectedKeys.value.concat(JSON.parse(JSON.stringify(getSelectRowKeys())))),
      ];
      let rows = e.rows;
      let rowKeys = JSON.parse(JSON.stringify(selectedKeys.value));
      let selectedRow = selectedRows_row.value;
      let pre_sele_rows_keyarray = [];
      let main_key = rowKey.value;
      for (let i in selectedRow) {
        pre_sele_rows_keyarray.push(selectedRow[i][main_key]);
      }
      // //去掉的话要删掉行数据
      for (let i in selectedRowKey) {
        if (rowKeys.indexOf(selectedRowKey[i]) == -1) {
          selectedRow = selectedRow.filter((item) => item[main_key] != selectedRowKey[i]);
        }
      }
      // //没有的话追加行数据
      for (let i in rowKeys) {
        if (pre_sele_rows_keyarray.indexOf(rowKeys[i]) == -1) {
          let cur_row = rows.filter((item) => item[main_key] == rowKeys[i])[0];
          selectedRow.push(cur_row);
        }
      }
      selectedRows_row.value = selectedRow; //选中的行数据
    } else {
      selectedRows_row.value = e.rows; //选中的行数据
      selectedKeys.value = e.keys; //选中的行id
    }
  };

  const sldCancle = () => {
    emit('cancleEvent');
  };
</script>
<style lang="less">
  .sld_sel_goods_single_diy {
    max-height: 600px;
    .ant-table-body {
      height: auto !important;
    }
  }
</style>
