<template>
  <div class="express-select-wrapper">
    <Select
      class="express-select"
      :placeholder="L('请选择物流公司')"
      :getPopupContainer="(triggerNode) => triggerNode.parentNode"
      v-model:value="expressId"
      @change="handleChange"
    >
      <Select.Option
        v-for="(item, index) in expressList"
        :key="index"
        :value="item.expressId"
      >{{ item.expressName }}</Select.Option>
    </Select>
    <Button type="link" class="add-express-btn" @click="showAddExpressModal">
      {{ L('添加物流公司') }}
    </Button>

    <!-- 添加物流公司弹窗 -->
    <SldSelGoodsSingleDiy
      @confirm-event="handleConfirm"
      @cancle-event="handleCancel"
      :confirmLoading="confirmLoading"
      link_type="express_company"
      :checkedType="true"
      :modalVisible="modalVisible"
    >
    </SldSelGoodsSingleDiy>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue';
import { Select, Button } from 'ant-design-vue';
import { getCurrentInstance } from 'vue';
import { failTip, sucTip } from '@/utils/utils';
import { getSellerExpressAddApi } from '/@/api/order/express';
import SldSelGoodsSingleDiy from '/@/components/SldSelGoodsSingleDiy/index.vue';

const vm = getCurrentInstance();
const L = vm?.appContext.config.globalProperties.$sldComLanguage;

const props = defineProps({
  value: [String, Number],
  expressList: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:value', 'change', 'refresh']);

const expressId = ref(props.value);
const modalVisible = ref(false);
const confirmLoading = ref(false);

watch(() => props.value, (newVal) => {
  expressId.value = newVal;
});

const handleChange = (value) => {
  expressId.value = value;
  emit('update:value', value);
  emit('change', value);
};

const showAddExpressModal = () => {
  modalVisible.value = true;
};

const handleCancel = () => {
  modalVisible.value = false;
};

// 物流公司弹框确定
const handleConfirm = async(record, ids) => {
  if (ids.length == 0) {
    failTip(L('请选择要添加的物流公司'));
    return;
  }
  confirmLoading.value = true;
  try {
    const res = await getSellerExpressAddApi({expressIds: ids.join(',')});
    if(res.state == 200){
      sucTip(res.msg);
      modalVisible.value = false;
      emit('refresh');
    } else {
      failTip(res.msg);
    }
  } finally {
    confirmLoading.value = false;
  }
};
</script>

<style lang="less" scoped>
.express-select-wrapper {
  display: flex;
  align-items: center;
  
  .express-select {
    flex: 1;
  }

  .add-express-btn {
    margin-left: 8px;
    padding: 0 8px;
    white-space: nowrap;
  }
}
</style> 