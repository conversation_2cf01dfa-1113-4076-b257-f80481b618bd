<template>
  <Modal
    :visible="show_preview_modal"
    :footer="null"
    :style="{ textAlign: 'center', width: modal_width }"
    @cancel="handleModalVisible"
  >
    <img :alt="preview_alt_con" :src="img" :style="{ maxWidth: '100%', maxHeight: '100%' }" />
  </Modal>
</template>
<script setup>
  import { Modal } from 'ant-design-vue';

  const props = defineProps({
    show_preview_modal: { type: Boolean, required: true },
    modal_width: { type: Number, default: 500, required: false },
    preview_alt_con: { type: String, default: undefined, required: false },
    img: { type: String, required: true },
  });

  const emit = defineEmits(['closePreviewModal']);

  // slodon_预览关闭modal
  function handleModalVisible() {
    emit('closePreviewModal');
  }
</script>
