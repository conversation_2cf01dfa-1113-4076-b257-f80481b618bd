<template>
  <div>
    <Modal
      destroyOnClose
      :maskClosable="false"
      :title="props.title"
      :visible="props.modalVisible"
      :width="props.width"
      @ok="sldConfirm"
      @cancel="sldCancle"
    >
      <template #footer>
        <Button key="back" @click="sldCancle">{{ props.cancelBtnText ? props.cancelBtnText : L('取消') }}</Button>
        <Button
          key="submit"
          type="primary"
          :loading="props.confirmBtnLoading"
          @click="sldConfirm"
          >{{ props.confirmBtnText ? props.confirmBtnText : L('确定') }}</Button
        >
      </template>
      <div class="component_sele_more flex_column_start_start">
        <div class="tableListForm">
          <div style="position: relative;">
            <BasicForm ref="formRef" submitOnReset @register="registerForm" class="basic-form-sld">
            </BasicForm>
          </div>
        </div>
        <div class="content flex_row_start_start" :style="{ height: height + 'px' }">
          <div class="scroll_box" :style="{height: height+'px', background: '#f5f5f5',width:'438px',zIndex:1}" @scroll="handleScrollLeft">
            <div class="left flex_row_start_start" :style="{ height: height + 'px' }">
              <template v-if="data.list != undefined && data.list.length > 0">
                <a v-for="(item,index) in data.list" :key="index" class="item flex_row_start_start" :style="{marginBottom: index == data.list.length - 1 ? 10 : 0}" @click="handleLeftItem(item)">
                  <!-- <div class="mask flex_row_center_center" v-if="item.activityState != 1">已参加其他活动</div>
                  <div class="mask flex_row_center_center" v-if="item.activityState == 1 && item.goodsStock == 0">暂时无货</div> -->
                  <div class="item_left flex_row_center_center">
                    <img :src="item.mainImage" alt="" class="live_img">
                  </div>
                  <div class="item_right flex_column_start_start">
                    <span class="svideo_name">{{ item.goodsName }}</span>
                    <span class="svideo_label">¥{{ item.goodsPrice }}</span>
                    <div class="sele_svideo_flag" v-if="selectedRowKeys.indexOf(item.goodsId) > -1">
                      <AliSvgIcon
                          iconName="iconyixuan"
                          width="19px"
                          height="19px"
                          fillColor="#FF711E"
                        />
                    </div>
                  </div>
                </a>
              </template>
              <template v-if="data.list == undefined || data.list.length == 0">
                <div class="flex_column_center_center" :style="{ width: '100%', height: '100%' }">
                  <Empty :image="simpleImage"></Empty>
                </div>
              </template>
            </div>
          </div>
          <div class="center flex_row_center_center">
            <AliSvgIcon
              iconName="iconmove-up1"
              width="39px"
              height="32px"
              fillColor="#fff6f4"
            />
          </div>
          <div class="scroll_box" :style="{ height: height + 'px', background: '#f5f5f5', width: '438px', zIndex: 1 }">
            <div class="right flex_row_start_start" :style="{ height: height + 'px' }">
              <template v-if="selectedRows.length > 0">
                <a v-for="(item,index) in selectedRows" :key="index" class="item flex_row_start_start" :style="{marginBottom: index == selectedRows.length - 1 ? 10 : 0}" @click="handleRightItem(item)">
                  <div class="item_left flex_row_center_center">
                    <img :src="item.goodsImage || item.mainImage" alt="" class="live_img">
                  </div>
                  <div class="item_right flex_column_start_start">
                    <span class="svideo_name">{{ item.goodsName }}</span>
                    <span class="svideo_label">¥{{ item.goodsPrice }}</span>
                    <div class="sele_svideo_flag">
                      <AliSvgIcon
                          iconName="iconziyuan21"
                          width="19px"
                          height="19px"
                          fillColor="#FF711E"
                        />
                    </div>
                  </div>
                </a>
              </template>
              <template v-else>
                <div class="flex_column_center_center" :style="{ width: '100%', height: '100%' }">
                  <Empty :image="simpleImage" :description="L('您还未选择数据')"></Empty>
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  </div>
</template>
<script>
export default {
  name: 'SldSelMoreLeftRightGoods',
};
</script>
<script setup>
import { failTip } from '@/utils/utils';
import { Button, Empty, Modal } from 'ant-design-vue';
import { computed, getCurrentInstance, onMounted, ref, watch } from 'vue';
import { getGoodsListApi } from '/@/api/common/commons';
import { BasicForm, useForm } from '/@/components/Form/index';
import { list_com_page_size_16 } from '/@/utils/utils';
import { validatorEmoji } from '/@/utils/validate';
// dev_supplier-start
import { useUserStore } from '/@/store/modules/user';
const userStore = useUserStore();
// dev_supplier-end

const vm = getCurrentInstance();
const L = vm?.appContext.config.globalProperties.$sldComLanguage;


const selectedRows = ref([])
const selectedRowKeys = ref([])//selectedRows的key
const loading = ref(false)
const data = ref({})
const formValues = ref({})
const loading_pagination_flag = ref({})
const title = ref('')
const params = ref({ pageSize: list_com_page_size_16 });//搜索条件
const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;

const props = defineProps({
  title: { type: String, required: true }, //弹框标题
  modalVisible: { type: Boolean, required: true }, //弹框是否显示
  confirmBtnLoading: { type: Boolean, default: false }, //弹框确认按钮loading属性，默认不显示
  height: { type: Number }, //弹框内容高度
  width: { type: Number, default: 416 }, //弹框宽度，默认416px
  confirmBtnText: { type: String, }, //弹框底部确认按钮显示文案，默认为确认
  cancelBtnText: { type: String, }, //弹框底部取消按钮显示文案，默认为取消
  client: { type: String, default: '' }, //装修类型
  arrType:{ type: String, default: 'object' },
  selectedRow: { type: Array, required: true, default: () => [] }, //表单数据
  selectedRowKey: { type: Array, required: true, default: () => [] }, //表单数据
  extra: { type: Object }, //表单数据
})

const emit = defineEmits([
  'cancleEvent',
  'confirmEvent',
]);

//由于watch无法监听props.modalVisible，所以需要计算属性转为ref对象
const modalVisible = computed(() => {
  return props.modalVisible;
});

watch(
  modalVisible,
  () => {
    if (modalVisible.value) {
      get_list({ pageSize: list_com_page_size_16 })
      if(props.arrType=='array'){
        let obj = []
        props.selectedRow.forEach(item=>{
          obj.push(...item)
        })
        selectedRows.value = obj;
        selectedRowKeys.value = [...props.selectedRowKey];
      }else{
        selectedRows.value = [...props.selectedRow];
        selectedRowKeys.value = [...props.selectedRowKey];
      }
    }
  }
);

// 获取数据
const get_list = async (param) => {
  let new_params = { ...param, ...formValues.value, state: 3 };
  // dev_supplier-start
  if (userStore.getStoreInfo.shopType && userStore.getStoreInfo.shopType == '3') {
    new_params = { ...new_params, saleModel: 2, state: 3 }
  }
  // dev_supplier-end
  let res = await getGoodsListApi(new_params)
  if (res.state == 200) {
    if (res.data.pagination != undefined) {
      if (userStore.getStoreInfo.shopType && userStore.getStoreInfo.shopType == '3') {
        res.data.list.forEach(item => {
          item.goodsPrice = item.wholesalePrice
        })
      }
      if (res.data.pagination.current == 1) {
        data.value = res.data;
      } else {
        data.value.list = data.value.list.concat(res.data.list);
        data.value.pagination = res.data.pagination;
      }
    }
    loading_pagination_flag.value = false
  }
}


//表单
const [registerForm, { validate, getFieldsValue }] = useForm({
  schemas: [
    {
      field: 'goodsName',
      component: 'Input',
      colProps: { span: 6, style: 'width:250px;max-width:100%;flex:none' },
      componentProps: {
        placeholder: L('请输入商品名称'),
        size: 'default',
      },
      label: L('商品名称'),
      labelWidth: 70,
      rules: [
        {
          // @ts-ignore
          validator: async (rule, value) => {
            await validatorEmoji(rule, value);
          },
          trigger: 'change',
        },
      ],
    },
  ],
  labelWidth: 80,
  baseColProps: { span: 6 },
  actionColOptions: { span: 24 },
  autoSubmitOnEnter: true,
  showAdvancedButton: true,
  submitFunc: search,
  resetFunc: reset,
});

// 搜索
async function search () {
  await validate();
  let values = getFieldsValue();
  for (let i in values) {
    if (values[i] == undefined) {
      delete values[i];
    }
  }
  params.value = { pageSize: list_com_page_size_16 }
  formValues.value = values;
  get_list({ ...params.value });
}

async function reset () {
  params.value.current = 1;
}

// 确定
const sldConfirm = () => {
  if (selectedRowKeys.value.length > 0) {
    if (props.extra.min_num != undefined && props.extra.min_num > 0 && selectedRowKeys.value.length < props.extra.min_num) {
      failTip(`${L('该模块至少需要选择')}${props.extra.min_num}${L('个商品')}`);
      return false;
    }
    if (props.extra.total_num != undefined && props.extra.total_num > 0 && selectedRowKeys.value.length != props.extra.total_num) {
      failTip(`${L('该模块需要选择')}${props.extra.total_num}${L('个商品')}`);//该模块需要选择   个商品
      return false;
    }
    if (props.extra.max_num != undefined && props.extra.max_num > 0 && selectedRowKeys.value.length > props.extra.max_num) {
      failTip(`${L('该模块最多选择')}${props.extra.max_num}${L('个商品')}`);
      return false;
    }
    let obj = []
    if (props.client == 'mobile'||props.client == 'pc') {
      selectedRows.value.forEach(item => {
        let info = {
          goodsId: item.goodsId,
          productId: item.productId,
          goodsName: item.goodsName,
          goodsPrice: item.goodsPrice,
          mainImage: item.mainImage,
          marketPrice: item.marketPrice,
          state: item.state,
          actualSales: item.actualSales,
          virtualSales: item.virtualSales,
        }
        obj.push(info)
      })
    }else{
      obj = JSON.parse(JSON.stringify(selectedRows.value))
    }
    emit('confirmEvent', obj, selectedRowKeys.value);
  } else {
    failTip(L('请选择商品'));//请选择商品
  }
}

// 取消
const sldCancle = () => {
  selectedRows.value = []
  selectedRowKeys.value = []
  params.value = { pageSize: list_com_page_size_16 }
  formValues.value = {};
  emit('cancleEvent');
}

const handleScrollLeft = (e) => {
  //e.srcElement.scrollTop: 滚动条距离页面顶部距离
  //e.srcElement.clientHeight: 滚动页面高度
  //e.srcElement.scrollHeight: 滚动条高度
  // if (e.srcElement.scrollTop + e.srcElement.clientHeight > e.srcElement.scrollHeight - 10) {//-50距离底部50px是触发以下内容
  // }
  if (data.value.pagination.current * list_com_page_size_16 < data.value.pagination.total && !loading_pagination_flag.value) {
    //请求分页数据
    loading_pagination_flag.value = true;
    get_list({ pageSize: list_com_page_size_16, current: data.value.pagination.current * 1 + 1 });
  }
}

//左侧数据点击事件（将选中的数据添加到右侧，左侧添加选中标识）
const handleLeftItem = (item) => {
  if (selectedRowKeys.value.indexOf(item.goodsId) == -1) {
    selectedRowKeys.value.push(item.goodsId);
    selectedRows.value.push(item);
  }
}

//右侧数据点击事件（移除选中数据，右侧将不显示，左侧的选中标识去掉）
const handleRightItem = (item) => {
  selectedRows.value = selectedRows.value.filter(items => items.goodsId != item.goodsId);
  selectedRowKeys.value = selectedRowKeys.value.filter(items => items != item.goodsId);
};

onMounted(() => {
});
</script>
<style lang="less">
@import './index.less';
</style>
