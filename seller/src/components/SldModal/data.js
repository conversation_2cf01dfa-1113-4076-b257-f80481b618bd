import { sldComLanguage } from '@/utils/utils';
// 分类管理-品牌弹窗列表
export const cate_list_brand_column = [
  {
    title: sldComLanguage('品牌名称'),
    dataIndex: 'brandName',
    width: 100,
  },
  {
    title: sldComLanguage('创建时间'),
    dataIndex: 'createTime',
    width: 100,
  },
];
// 分类管理-品牌弹窗筛选项
export const cate_list_brand_schema = [
  {
    field: 'brandName',
    component: 'Input',
    colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
    componentProps: {
      minWidth: 300,
      maxlength: 10,
      placeholder: sldComLanguage('请输入品牌名称'),
      size: 'default',
    },
    label: sldComLanguage('品牌名称'),
    labelWidth: 70,
  },
];
// 分类管理-属性弹窗筛选项
export const cate_list_attr_column = [
  {
    title: sldComLanguage('属性名称'),
    dataIndex: 'attributeName',
    width: 100,
  },
  {
    title: sldComLanguage('创建时间'),
    dataIndex: 'createTime',
    width: 100,
  },
  {
    title: sldComLanguage('排序'),
    dataIndex: 'sort',
    width: 100,
  },
];
// 分类管理-属性弹窗筛选项
export const cate_list_attr_schema = [
  {
    field: 'attributeName',
    component: 'Input',
    colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
    componentProps: {
      minWidth: 300,
      maxlength: 10,
      placeholder: sldComLanguage('请输入属性名称'),
      size: 'default',
    },
    label: sldComLanguage('属性名称'),
    labelWidth: 70,
  },
];
