<template>
  <div class="sld_modal">
    <Modal :destroyOnClose="true" :maskClosable="false" :title="props.title" :visible="props.visible"
      :zIndex="props.zIndex" :width="props.width" @ok="sldConfirm" @cancel="sldCancle"
      :footer="props.showFoot ? undefined : null">
      <template #footer>
        <a-button key="back" @click="sldCancle">{{
          props.cancelBtnText ? props.cancelBtnText : L('取消')
          }}</a-button>
        <a-button key="submit" type="primary" :loading="props.confirmBtnLoading" @click="sldConfirm">{{
          props.confirmBtnText ? props.confirmBtnText : L('确定') }}</a-button>
      </template>
      <div class="modal_box" :style="{
        overflow: 'auto',
        maxHeight: props.height ? props.height + 'px' : 'auto',
        padding: typeof conType != 'undefined' && conType == 'moreCheck' ? '0px' : '10px',
      }">
        <Form ref="formRef" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }" :model="curFormData"
          :scrollToFirstError="true" @finish="onFinish" style="margin-top: 5px">
          <div v-if="showTopTip">
            <div class="flex_row_start_center sld_modal_top_tip">
              <div :style="{ lineHeight: '0px' }">
                <AliSvgIcon iconName="icontishi3" width="15px" height="15px" fillColor="#333333" />
              </div>
              <span :style="{ fontSize: '13px', marginLeft: '6px', color: 'rgba(0,0,0,.65)' }">{{
                showTopTip
                }}</span>
            </div>
            <div style="width: 100%; height: 27px; background: rgb(255 255 255)"></div>
          </div>
          <template v-if="typeof conType != 'undefined' && conType == 'moreCheck'">
            <div class="sldPerminss">
              <template v-if="visible_one">
                <template v-for="(item, index) in formData" :key="index">
                  <Checkbox
                    :indeterminate="item.checkList != undefined && item.checkList.length > 0 && item.checkList.length < item.sldchild.length ? true : false"
                    v-model:checked="item.checkAll" @change="(e) => permissionAll(e, item.id)">
                    <span class="sldGroup">{{ item.name }}</span>
                  </Checkbox>
                  <CheckboxGroup :options="item.sldchild" v-model:value="item.checkList"
                    @change="(e) => permissionSingle(e, item.id)">
                  </CheckboxGroup>
                </template>
              </template>
            </div>
          </template>
          <template v-else>
            <template v-for="(item, index) in formData" :key="index">
              <template v-if="item.type == 'tabs'">
                <Tabs type="card" v-model:activeKey="curFormData[item.name]" @change="(e) => tabsChange(e, item)"
                  style="margin-bottom: 10px;">
                  <TabPane :key="it.key" :tab="it.tab" v-for="(it, ind) in item.data">
                  </TabPane>
                </Tabs>
              </template>
              <template v-if="item.type == 'input' && (!item.show || item.show(curFormData))">
                <Form.Item :validateFirst="true" :key="index" :name="item.name" :label="item.label" :extra="item.extra"
                  :rules="item.rules != undefined
                    ? [
                      ...item.rules,
                      {
                        validator: async (rule, value) => {
                          await validatorEmoji(rule, value);
                        },
                      },
                    ]
                    : [
                      {
                        validator: async (rule, value) => {
                          await validatorEmoji(rule, value);
                        },
                      },
                    ]
                    ">
                  <a-input :placeholder="item.placeholder" :showCount="item.showCount ? item.showCount : false"
                    :type="item.input_type ? item.input_type : 'text'"
                    :disabled="item.disable === true ? item.disable : false"
                    :maxLength="item.maxLength !== undefined ? item.maxLength : item.maxlength !== undefined ? item.maxlength : 255"
                    v-model:value="curFormData[item.name]" @change="
                      (value) =>
                        handleSelectChange(item, value.target.value, null, null, null, null)
                    " />
                </Form.Item>
              </template>
              <template v-else-if="item.type == 'textarea'">
                <Form.Item :validateFirst="true" :key="index" :name="item.name" :label="item.label" :extra="item.extra"
                  :rules="item.rules != undefined
                    ? [
                      ...item.rules,
                      {
                        validator: async (rule, value) => {
                          await validatorEmoji(rule, value);
                        },
                      },
                    ]
                    : [
                      {
                        validator: async (rule, value) => {
                          await validatorEmoji(rule, value);
                        },
                      },
                    ]
                    ">
                  <a-textarea :placeholder="item.placeholder" :disabled="item.disable === true ? item.disable : false"
                    :maxlength="item.maxLength ? item.maxLength : 255"
                    :showCount="item.showCount ? item.showCount : false" :autoSize="{ minRows: 4, maxRows: 6 }"
                    v-model:value="curFormData[item.name]" @change="
                      (value) =>
                        handleSelectChange(item, value.target.value, null, null, null, null)
                    " />
                </Form.Item>
              </template>
              <template v-else-if="item.type == 'inputnum' && shouldShowField(item)">
                <Form.Item :validateFirst="true" :key="index" :name="item.name" :label="item.label" :extra="item.extra"
                  :rules="item.rules">
                  <div class="flex_row_center_center">
                    <InputNumber v-if="item.formatter" :placeholder="item.placeholder"
                      :disabled="item.disable === true ? item.disable : false" v-model:value="curFormData[item.name]"
                      :min="item.min ? item.min : 0" :max="item.max ? item.max : 9999999"
                      :precision="item.precision ? item.precision : 0" :step="item.step ? item.step : 1"
                      :formatter="(value) => `${value}${item.formatter ? item.formatter : ''}`"
                      @change="(value) => handleSelectChange(item, value, null, null, null, null)" />
                    <InputNumber v-else :placeholder="item.placeholder"
                      :disabled="item.disable === true ? item.disable : false" v-model:value="curFormData[item.name]"
                      :min="item.min ? item.min : 0" :max="item.max ? item.max : 9999999"
                      :precision="item.precision ? item.precision : 0" :step="item.step ? item.step : 1"
                      @change="(value) => handleSelectChange(item, value, null, null, null, null)" />
                    <span :style="{
                      width: item.avoid_width ? item.avoid_width : '100px',
                      marginLeft: '5px',
                    }" v-if="item.avoid != null" class="avoid_style" @click="freeExpress(item.name)">{{ item.avoid
                      }}</span>
                  </div>
                </Form.Item>
              </template>
              <template v-else-if="item.type == 'select'">
                <Form.Item :validateFirst="true" :key="index"
                  :wrapper-col="item.wrapperCol ? item.wrapperCol : { span: 16 }" :name="item.name" :label="item.label"
                  :extra="item.extra" :rules="item.rules">
                  <div v-if="item.reSelectClick" @click="reSelectClick" class="hover_select"></div>
                  <Select :placeholder="item.placeholder" :disabled="item.disable === true ? item.disable : false"
                    ref="select" v-model:value="curFormData[item.name]" @change="
                      (value, option) => handleSelectChange(item, value, option, null, null, null)
                    " @focus="() => handleSelectFocus(item)" :style="{
                      width:
                        item.width !== undefined
                          ? item.width + (typeof item.width == 'number' ? 'px' : '')
                          : '',
                    }">
                    <Select.Option v-for="(itemSel, indexSel) in item.selData" :key="indexSel"
                      :value="item.diy === true ? itemSel[item.selKey] : itemSel.key">{{ item.diy === true ?
                        itemSel[item.selName] : itemSel.name }}</Select.Option>
                  </Select>
                </Form.Item>
              </template>
              <template v-else-if="item.type == 'date_picker'">
                <Form.Item :validateFirst="true" :key="index" :wrapper-col="{ span: 7 }" :name="item.name"
                  :label="item.label" :extra="item.extra" :rules="item.rules">
                  <DatePicker :format="item.format ? item.format : 'YYYY-MM-DD'" v-model:value="curFormData[item.name]"
                    :showTime="item.show_time ? item.show_time : false"
                    :placeholder="item.placeholder ? item.placeholder : L('请选择日期')" style="width: 100%"
                    @change="(value) => handleSelectChange(item, value, null, null, null, null)" />
                </Form.Item>
              </template>
              <template v-else-if="item.type == 'upload_file'">
                <Form.Item :validateFirst="true" :key="index" :wrapper-col="{ span: 7 }" :name="item.name"
                  :label="item.label" :extra="item.extra" :rules="item.rules">
                  <div class="upload_file">
                    <Upload :accept="item.accept ? item.accept : undefined" :action="`${apiUrl}${item.upload_url}`"
                      :file-list="curFormData[item.name] ? curFormData[item.name] : []" :beforeUpload="(file) => beforeUpload(file, index, item.accept ? item.accept : '')
                        " @click="beforeUploadClick" :data="item.fileData ? item.fileData : undefined"
                      @change="(e) => handleFileChange(e, index)" :headers="{
                        Authorization: imgToken,
                      }">
                      <div class="upload_file_btn" v-if="
                        item.num == undefined
                          ? true
                          : item.num > item.curFormData[item.name].length
                      ">{{ item.btn ? item.btn : L('点击上传>>') }}</div>
                    </Upload>
                    <div v-if="item.desc" class="upload_file_desc">{{ item.desc }}</div>
                  </div>
                </Form.Item>
              </template>
              <template v-else-if="item.type == 'verify_code'">
                <Form.Item :validateFirst="true" :key="index" :name="item.name" :label="item.label" :extra="item.extra"
                  :rules="item.rules">
                  <div class="get_sms_code">
                    <a-input :placeholder="item.placeholder" :type="item.input_type ? item.input_type : 'text'"
                      :disabled="item.disable === true ? item.disable : false" :maxlength="item.maxLength"
                      v-model:value="curFormData[item.name]" @change="
                        (value) =>
                          handleSelectChange(item, value.target.value, null, null, null, null)
                      " />
                    <div class="get_sms_code_wrap">
                      <span class="v_split">|</span>
                      <span class="sms_code" :style="countDownM ? 'opacity: 0.6' : ''"
                        @click="getSmsCodeEvent(item.mobile, item)">
                        {{ countDownM ? countDownM + L('s后重新获取') : L('获取验证码') }}
                      </span>
                    </div>
                  </div>
                </Form.Item>
              </template>
              <template v-else-if="item.type == 'onlytxt'">
                <div :key="index" :style="{
                  display: 'flex',
                  flexDirection: 'column',
                  width: props.width ? props.width * 0.9 + 'px' : 416,
                  marginBottom: '20px',
                  paddingLeft: props.width * 0.24 + 'px',
                }">
                  <div :style="{
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'flex-start',
                    color: item.fontColor,
                    width: (item.right / 25) * (props.width ? props.width : 416) + 'px',
                    fontSize: item.fontSize,
                    marginBottom: 10,
                  }">{{ item.content }}</div>
                  <span :style="{
                    display: 'inline-block',
                    width: (item.right / 24) * (props.width ? props.width : 416) + 'px',
                    height: 1,
                    backgroundColor: item.bgcColor,
                  }"></span>
                </div>
              </template>
              <template v-else-if="item.type == 'seckill_time_select'">
                <Form.Item :validateFirst="true" :key="index" :name="item.name" :label="item.label" :extra="item.extra"
                  :rules="item.rules">
                  <!-- 秒杀时间选择 -->
                  <div class="flex_row_start_start seckill_time_wrap" style="flex-wrap: wrap">
                    <div v-for="(items, ind) in day_hour()" :key="ind" class="flex_row_center_center seckill_time_item"
                      :style="{
                        borderTopWidth: ind < 8 ? 1 : 0,
                        background: item.sel_data?.indexOf(items) > -1 ? '#FF7F40' : '#fff',
                        color: item.sel_data?.indexOf(items) > -1 ? '#fff' : '#333',
                        fontWeight: item.sel_data?.indexOf(items) > -1 ? '700' : '500',
                        cursor: 'pointer',
                      }" @click="handleSelTime(items, item, index)">{{ items }}</div>
                  </div>
                </Form.Item>
              </template>
              <template v-else-if="item.type == 'show_content' && shouldShowField(item)">
                <Form.Item :validateFirst="true" :key="index" :name="item.name" :label="item.label" :extra="item.extra"
                  :rules="item.rules" :style="item.style">
                  <div style="line-height: 32px;" v-if="item.label">{{ item.content }}<span
                      v-if="item.unit && ((item.content && item.content != null && item.content != '--') || item.content == 0)">{{
                        item.unit }}</span></div>
                  <div style="line-height: 32px;text-align: center;width: 100%;" v-else>{{ item.content }}<span
                      v-if="item.unit && ((item.content && item.content != null && item.content != '--') || item.content == 0)">{{
                        item.unit }}</span></div>
                </Form.Item>
              </template>
              <template v-else-if="item.type == 'show_content_cont'">
                <div style="line-height: 32px;text-align: center;width: 100%;">{{ item.content }}<span
                    v-if="item.unit && ((item.content && item.content != null && item.content != '--') || item.content == 0)">{{
                      item.unit }}</span></div>
              </template>
              <template v-else-if="item.type == 'show_img'">
                <Form.Item :validateFirst="true" :key="index" :name="item.name" :label="item.label" :extra="item.extra"
                  :rules="item.rules">
                  <div class="flex_row_common" :style="{ width: item.width, height: item.height }">
                    <img class="show_img" :src="item.content" />
                  </div>
                </Form.Item>
              </template>
              <template v-else-if="item.type == 'show_content_map'">
                <Form.Item :validateFirst="true" :key="index" :name="item.name" :label="item.label" :extra="item.extra"
                  :rules="item.rules">
                  <template v-if="item.content.length > 0">
                    <div style="margin-top: 5px">
                      <div v-for="(val, ind) in item.content" :key="ind">
                        {{ val.createTime }}&nbsp;&nbsp;&nbsp;&nbsp;{{ val.content }}
                      </div>
                    </div>
                  </template>
                </Form.Item>
              </template>
              <template v-else-if="item.type == 'show_image'">
                <Form.Item :validateFirst="true" :key="index" :name="item.name" :label="item.label" :extra="item.extra"
                  :rules="item.rules">
                  <div :style="{ width: item.width, height: item.height, overflow: 'hidden' }">
                    <Image :src="item.content" />
                  </div>
                </Form.Item>
              </template>
              <template v-else-if="item.type == 'show_img_more'">
                <Form.Item :validateFirst="true" :key="index" :name="item.name" :label="item.label" :extra="item.extra"
                  :rules="item.rules">
                  <template v-if="item.content.length > 0">
                    <ImagePreviewGroup>
                      <div class="ImagePreviewGroup">
                        <Image :width="item.width" :style="{ marginRight: '5px' }" :src="val"
                          v-for="(val, ind) in item.content" :key="ind" />
                      </div>
                    </ImagePreviewGroup>
                  </template>
                </Form.Item>
              </template>
              <template v-else-if="item.type == 'show_editor_con'">
                <Form.Item :validateFirst="true" :key="index" :name="item.name" :label="item.label" :extra="item.extra"
                  :rules="item.rules">
                  <div class="show_editor_con">
                    <div v-html="item.content"></div>
                  </div>
                </Form.Item>
              </template>
              <template v-else-if="item.type == 'show_content_table'">
                <Form.Item :validateFirst="true" :key="index" :name="item.name" :label="item.label" :extra="item.extra"
                  :rules="item.rules" :wrapper-col="item.wrapperCol ? item.wrapperCol : { span: 16 }">
                  <div v-if="item.scroll != undefined && !item.scroll">
                    <Table :columns="item.columns" :data-source="item.data" bordered
                      :width="item.width ? item.width : ''" :scroll="item.scrollHeight ? { y: item.scrollHeight } : {}"
                      :pagination="item.pagination ? item.pagination : false" class="sld_modal_table"
                      @change="handleTableChange">
                      <template v-slot:bodyCell="{ column, record, text }">
                        <slot name="bodyCell" v-bind="{ column, record, text } || {}"></slot>
                        <template v-if="column.dataType === 'img'">
                          <Popover placement="rightTop">
                            <template #content>
                              <div :style="{ width: '200px', height: '200px', margin: '0' }"
                                class="flex_com_row_center">
                                <img :src="record[column.dataIndex]" alt="" style="max-width: 100%; max-height: 100%" />
                              </div>
                            </template>
                            <div class="flex_com_row_center" :style="{ width: column.imgWidth }" style="margin: auto">
                              <img :src="record[column.dataIndex]" :style="{ width: column.imgWidth }" />
                            </div>
                          </Popover>
                        </template>
                      </template>
                    </Table>
                  </div>
                </Form.Item>
              </template>
              <template v-else-if="item.type == 'more_color_picker'">
                <Form.Item :validateFirst="true" :key="index" :name="item.name" :label="item.label" :extra="item.extra"
                  :rules="item.rules">
                  <ColorPicker :color="curFormData[item.name]" @update:color="handleColorUpdate($event, item.name)">
                  </ColorPicker>
                </Form.Item>
              </template>
              <template v-else-if="item.type == 'switch'">
                <Form.Item :validateFirst="true" :key="index" :name="item.name" :label="item.label" :extra="item.extra"
                  :rules="item.rules">
                  <Switch :un-checked-children="item.unCheckedText ? item.unCheckedText : ''"
                    v-model:checked="curFormData[item.name]" :un-checked-value="item.unCheckedValue != undefined ? item.unCheckedValue : false
                      " :checked-children="item.checkedText ? item.checkedText : ''"
                    :checked-value="item.checkedValue != undefined ? item.checkedValue : true" />
                </Form.Item>
              </template>
              <template v-else-if="item.type == 'cascader_common'">
                <Form.Item :validateFirst="true" :key="index"
                  :wrapper-col="item.wrapperCol ? item.wrapperCol : undefined" :name="item.name" :label="item.label"
                  :extra="item.extra" :rules="item.rules" :style="{ width: '100%' }">
                  <div :style="{ width: '100%' }">
                    <Cascader v-model:value="curFormData[item.name]" :options="item.data"
                      :placeholder="item.placeholder"
                      @change="(e, selectedOptions) => cascader_change(e, selectedOptions, item)" :fieldNames="item.fieldNames != undefined
                        ? item.fieldNames
                        : {
                          label: 'label',
                          value: 'value',
                          children: 'children',
                        }
                        " class="ant-col-16" :style="{ width: '100%' }" />
                  </div>
                </Form.Item>
              </template>
              <template v-else-if="item.type == 'cascader_common_load'">
                <!-- 多级联动选择器-通用-一级一级获取，动态加载 -->
                <Form.Item :validateFirst="true" :key="index" :name="item.name"
                  :wrapper-col="item.wrapperCol ? item.wrapperCol : undefined" :label="item.label" :extra="item.extra"
                  :rules="item.rules">
                  <div :style="{ width: '100%' }">
                    <Cascader v-model:value="curFormData[item.name]" :options="item.data"
                      :placeholder="item.placeholder" :changeOnSelect="item.changeOnSelect"
                      @change="(e, selectedOptions) => cascader_change(e, selectedOptions, item)"
                      :loadData="(selectedOptions) => loadData(selectedOptions, item)" :fieldNames="item.fieldNames != undefined
                        ? item.fieldNames
                        : {
                          label: 'label',
                          value: 'value',
                          children: 'children',
                        }
                        " :style="{ width: '100%' }" />
                  </div>
                </Form.Item>
              </template>
              <template v-else-if="item.type == 'checkboxgroup'">
                <Form.Item :validateFirst="true" :key="index" :name="item.name" :label="item.label" :extra="item.extra"
                  :rules="item.rules">
                  <CheckboxGroup v-model:value="curFormData[item.name]" :options="item.sldOptions"
                    @change="(value) => handleSelectChange(item, value, null, null, null, null)" />
                </Form.Item>
              </template>
              <template v-else-if="item.type == 'TreeSelect'">
                <Form.Item :validateFirst="true" :key="index"
                  :wrapper-col="{ span: item.colSpan != undefined ? item.colSpan : 16 }" :name="item.name"
                  :label="item.label" :extra="item.extra" :rules="item.rules">
                  <TreeSelect v-model:value="curFormData[item.name]"
                    :disabled="item.disabled != undefined ? item.disabled : false" :style="{ width: '100%' }"
                    :treeData="item.data" :showSearch="true" :placeholder="item.placeholder"
                    :allowClear="item.allowClear" :onSelect="item.onSelect"
                    :treeNodeFilterProp="item.treeNodeFilterProp" :fieldNames="item.fieldNames
                      ? item.fieldNames
                      : { children: 'children', label: 'title', value: 'value' }
                      " @change="
                        (value, label, extra) => treeSelectChange(value, label, extra, index, item)
                      " :dropdownStyle="{ maxHeight: '300px' }" />
                </Form.Item>
              </template>
              <template v-else-if="item.type == 'show_subtitle'">
                <!-- 左侧一条数线，右侧文字，起到标题的左右 -->
                <div :key="index" :style="{
                  marginTop: item.distance.top + 'px',
                  marginLeft: item.distance.left + 'px',
                  marginRight: item.distance.right + 'px',
                  marginBottom: item.distance.bottom + 'px',
                }">
                  <div class="title_add_goods">
                    <span :style="{ backgroundColor: item.color }" class="left_border"></span>
                    <span>{{ item.name }}</span>
                  </div>
                </div>
              </template>
              <template v-else-if="item.type == 'radio_select' || item.type == 'radio_orignal'">
                <Form.Item :validateFirst="true" :key="index" :name="item.name" :label="item.label" :extra="item.extra"
                  :rules="item.rules">
                  <RadioGroup size="small" v-model:value="curFormData[item.name]" buttonstyle="solid"
                    :disabled="item.disable" @change="radioHandleChange($event, index, item)">
                    <template v-if="item.type == 'radio_select'">
                      <RadioButton v-for="(val, ind) in item.data" :key="ind" :value="val.key">
                        {{ val.value }}
                      </RadioButton>
                    </template>
                    <template v-else>
                      <Radio v-for="(val, ind) in item.data" :key="ind" :value="val.key">
                        {{ val.value }}
                      </Radio>
                    </template>
                  </RadioGroup>
                </Form.Item>
              </template>
              <template v-else-if="item.type == 'range_picker'">
                <Form.Item :validateFirst="true" :key="index" :name="item.name" :label="item.label" :extra="item.extra"
                  :rules="item.rules">
                  <RangePicker :format="item.format ? item.format : 'YYYY-MM-DD'" v-model:value="curFormData[item.name]"
                    :disabledDate="item.disabledDate" :showTime="item.show_time != undefined ? item.show_time : false"
                    :style="{ width: '100%' }" :placeholder="[item.placeholder1, item.placeholder2]" />
                </Form.Item>
              </template>
              <template v-else-if="item.type == 'upload_img_upload'">
                <Form.Item :class="{ tiny_spot: item.star }" :key="index" :name="item.name" :label="item.label"
                  :extra="item.extra" :rules="item.rules">
                  <div class="upload_file" style="overflow: auto;max-height: 300px;">
                    <Upload :maxCount="item.maxCount ? item.maxCount : 1"
                      :accept="item.accept ? item.accept : undefined" :name="item.upload_name"
                      :multiple="item.multiple ? item.multiple : false" :action="`${apiUrl}${item.upload_url}`"
                      listType="picture-card" :file-list="curFormData[item.name] ? curFormData[item.name] : []"
                      :beforeUpload="(file) => beforeUpload(file, index, item.accept ? item.accept : '')
                        " @click="beforeUploadClick" :data="item.fileData ? item.fileData : undefined"
                      @change="(e) => handleFileChange(e, index)" :headers="{
                        Authorization: imgToken,
                      }">
                      <div v-if="curFormData[item.name].length < (item.maxCount ? item.maxCount : 1)">
                        <PlusOutlined />
                        <div className="ant-upload-text">{{ L('上传图片') }}</div>
                      </div>
                    </Upload>
                    <div v-if="item.desc" class="upload_file_desc">{{ item.desc }}</div>
                  </div>
                </Form.Item>
              </template>
              <template v-else-if="item.type == 'upload_video_upload'">
                <Form.Item :class="{ tiny_spot: item.star }" :key="index" :name="item.name" :label="item.label"
                  :extra="item.extra" :rules="item.rules">
                  <div class="upload_file" style="overflow: auto;max-height: 300px;">
                    <Upload :maxCount="item.maxCount ? item.maxCount : 1"
                      :accept="item.accept ? item.accept : undefined" :name="item.upload_name"
                      :action="`${apiUrl}${item.upload_url}`" listType="picture-card"
                      :multiple="item.multiple ? item.multiple : false"
                      :file-list="curFormData[item.name] ? curFormData[item.name] : []" :beforeUpload="(file) => beforeUpload(file, index, item.accept ? item.accept : '')
                        " @click="beforeUploadClick" :data="item.fileData ? item.fileData : undefined"
                      @change="(e) => handleFileChange(e, index)" :headers="{
                        Authorization: imgToken,
                      }">
                      <div v-if="curFormData[item.name].length < (item.maxCount ? item.maxCount : 1)">
                        <PlusOutlined />
                        <div className="ant-upload-text">{{ L('上传视频') }}</div>
                      </div>
                    </Upload>
                    <div v-if="item.desc" class="upload_file_desc">{{ item.desc }}</div>
                  </div>
                </Form.Item>
              </template>
              <template v-else-if="item.type == 'upload_mp3_upload'">
                <Form.Item :validateFirst="true" :key="index" :name="item.name" :label="item.label" :extra="item.extra"
                  :rules="item.rules">
                  <div class="upload_file">
                    <Upload :maxCount="1" :withCredentials="true" :accept="item.accept ? item.accept : '.mp3, .m4a,'"
                      :name="item.upload_name" :disabled="item.disabled" listType="text"
                      :action="`${apiUrl}${item.upload_url}`"
                      :file-list="curFormData[item.name] ? curFormData[item.name] : []" :beforeUpload="(file) => beforeUpload(file, index, item.accept ? item.accept : '')
                        " @click="beforeUploadClick" :data="item.fileData ? item.fileData : undefined"
                      @change="(e) => handleFileChange(e, index)" :headers="{
                        Authorization: imgToken,
                      }">
                      <template #itemRender="{ file, actions }">
                        <Space @click="actions.download" style="">
                          <a href="javascript:;" v-if="file.response && file.response.data"><span
                              :style="file.status === 'error' ? 'color: red' : ''">{{ L('音乐') }}.{{
                                file.name.split('.')[1] }}&nbsp;</span>
                            {{ L('时长') }}&nbsp;{{ file.response.data.duration }}&nbsp;|&nbsp;{{
                              L('大小')
                            }}&nbsp;{{ (file.response.data.size / 1024 / 1024).toFixed(2) }}M</a>
                        </Space>
                      </template>
                      <Button :disabled="item.disabled" :loading="curFormData[item.name] &&
                        curFormData[item.name][0] &&
                        !curFormData[item.name][0].response
                        " v-if="curFormData[item.name] && curFormData[item.name].length > 0">{{ L('重新上传') }}</Button>
                      <Button :disabled="item.disabled" v-else :loading="curFormData[item.name] &&
                        curFormData[item.name][0] &&
                        !curFormData[item.name][0].response
                        ">+{{ L('上传音乐') }}</Button>
                    </Upload>
                    <div v-if="item.desc" class="upload_file_desc">{{ item.desc }}</div>
                  </div>
                </Form.Item>
              </template>
              <template v-else-if="item.type == 'upload_img_upload_modal'">
                <Form.Item :validateFirst="true" :key="index" :name="item.name" :label="item.label" :extra="item.extra"
                  :rules="item.rules">
                  <div class="upload_file" @click="
                    curFormData[item.name] && curFormData[item.name].length
                      ? () => null
                      : openMaterial()
                    ">
                    <Upload :withCredentials="true" :maxCount="1" :accept="item.accept ? item.accept : undefined"
                      :name="item.upload_name" :action="`${apiUrl}${item.upload_url}`" listType="picture-card"
                      :file-list="curFormData[item.name] ? curFormData[item.name] : []" :openFileDialogOnClick="false"
                      :beforeUpload="(file) => beforeUpload(file, index, item.accept ? item.accept : '')
                        " @click="beforeUploadClick" :data="item.fileData ? item.fileData : undefined"
                      @change="(e) => handleFileChange(e, index)" :headers="{
                        Authorization: imgToken,
                      }">
                      <div v-if="curFormData[item.name].length < 1">
                        <PlusOutlined />
                        <div className="ant-upload-text">{{ L('上传图片') }}</div>
                      </div>
                    </Upload>
                    <div v-if="item.desc" class="upload_file_desc">{{ item.desc }}</div>
                  </div>
                </Form.Item>
              </template>
              <template v-else-if="item.type == 'attr_tags'">
                <Form.Item :validateFirst="true" :key="index" :name="item.name" :label="item.label" :extra="item.extra"
                  :rules="item.rules">
                  <div class="attr_tags">
                    <Tag v-for="tag in curFormData[item.name]" :key="tag"
                      :closable="item.disable ? !item.disable : true" @close="tagHandleClose(tag, item.name, index)">
                      {{ tag }}
                    </Tag>
                    <a-input :maxLength="10" v-if="
                      item.inputVisible &&
                      curFormData[item.name] &&
                      curFormData[item.name].length < item.maxNum
                    " type="text" size="small" :style="{ width: '150px' }" v-model:value="item.inputValue"
                      @change="tagInputChange($event, index)" @blur="tagInputConfirm(index, item.name, 'blur')"
                      @PressEnter="tagInputConfirm(index, item.name, 'press')" />
                    <Tag v-if="
                      !item.inputVisible &&
                      curFormData[item.name] &&
                      curFormData[item.name].length < item.maxNum &&
                      !item.disable
                    " style="border-style: dashed; background: #fff; cursor: default"
                      @click="showInput(index, item.name)">
                      <plus-outlined style="margin-top: 5px; vertical-align: -1px !important" />
                      {{ item.tip_con }}
                    </Tag>
                  </div>
                </Form.Item>
              </template>
              <template v-else-if="item.type == 'empty'">
                <div :key="index">
                  <div :style="{ width: '100%', height: item.height, background: '#fff' }"></div>
                </div>
              </template>
              <template v-else-if="item.type == 'tag_show_btn_sel'">
                <Form.Item :validateFirst="true" :key="index" :name="item.name" :label="item.label" :extra="item.extra"
                  :rules="item.rules">
                  <div class="tag_list">
                    <div v-if="item.data && item.data.length > 0" class="tag_btn">
                      <div v-for="(items, indexs) in item.data" :key="indexs" class="tag_btn_item">
                        <span class="tag_btn_item_name">{{
                          item.diy ? items[item.diy_key] : item.name
                          }}</span>
                        <img class="tag_btn_item_img" src="@/assets/images/del_icon.png" />
                        <img class="tag_btn_item_img_hover" @click="handleTagClick('del', item, items)"
                          src="@/assets/images/del_icon_hover.png" />
                      </div>
                    </div>
                    <div class="tag_btn_add" @click="handleTagClick('add', item, item)">
                      <AliSvgIcon iconName="iconjia" width="17px" height="17px" fillColor="#fff" />
                      <span>{{ item.btnText ? item.btnText : L('选择') }}</span>
                    </div>
                  </div>
                </Form.Item>
              </template>
              <template v-else-if="item.type == 'show_express'">
                <div :style="{ display: 'flex', flexDirection: 'column', marginTop: '0px' }" :key="index">
                  <p class="express_title" v-if="item.content.expressName">{{ item.content.expressName }}：{{
                    item.content.expressNumber }}</p>
                  <div v-if="
                    item.content && item.content.routeList && item.content.routeList.length > 0
                  " :style="{ marginLeft: '20px', marginRight: '10px' }">
                    <Timeline>
                      <TimelineItem v-for="(it, ind) in item.content.routeList" :key="ind" color="#FA6F1E">
                        <span class="content">&nbsp;&nbsp;&nbsp;&nbsp;{{ it.acceptTime }}&nbsp;&nbsp;</span>
                        <span class="content">{{ it.acceptStation || it.remark }}</span>
                      </TimelineItem>
                    </Timeline>
                  </div>
                  <div v-else class="flex_column_center_center empty_express">
                    <img :src="express_empty" />
                    <p>{{ L('暂无物流进度') }}</p>
                  </div>
                </div>
              </template>
              <template v-else-if="item.type == 'view_video'">
                <Form.Item :validateFirst="true" v-if="item.label" :key="index" :name="item.name" :label="item.label"
                  :extra="item.extra" :rules="item.rules">
                  <div>
                    <video :id="'modal_view_video_' + item.name" :src="item.content" :width="item.width"
                      :height="item.height" controls autoPlay />
                  </div>
                </Form.Item>
                <div v-if="!item.label" :key="index">
                  <video :id="'view_video_' + index" :src="item.content" :width="item.width" :height="item.height"
                    controls autoPlay />
                </div>
              </template>
              <template v-else-if="item.type == 'view_img_text'">
                <Form.Item :validateFirst="true" v-if="item.label" :key="index" :name="item.name" :label="item.label"
                  :extra="item.extra" :rules="item.rules">
                  <div class="flex_column_start_start svideo_img_text" :key="index">
                    <Carousel v-if="item.content && item.content.length != undefined && item.content.length"
                      :dots="true">
                      <template #prevArrow>
                        <div class="custom-slick-arrow" style="z-index: 1; left: 10px">
                          <LeftCircleOutlined />
                        </div>
                      </template>
                      <template #nextArrow>
                        <div class="custom-slick-arrow" style="right: 10px">
                          <RightCircleOutlined />
                        </div>
                      </template>
                      <div v-for="(img_item, ind) in item.content" :key="ind" class="flex_row_center_center img_wrap">
                        <img :src="img_item" />
                      </div>
                    </Carousel>
                    <div class="text">
                      {{ item.text }}
                    </div>
                  </div>
                </Form.Item>
                <div class="flex_row_center_start svideo_img_text" :key="index" v-if="!item.label"
                  style="margin-top: 10px; margin-bottom: 15px">
                  <div>
                    <Carousel v-if="item.content && item.content.length != undefined && item.content.length"
                      :arrows="true">
                      <template #prevArrow>
                        <div class="custom-slick-arrow" style="z-index: 1; left: 10px"> </div>
                      </template>
                      <template #nextArrow>
                        <div class="custom-slick-arrow" style="right: 10px">
                          <RightCircleOutlined />
                        </div>
                      </template>
                      <div v-for="(img_item, ind) in item.content" :key="ind" class="flex_row_center_center img_wrap">
                        <img :src="img_item" />
                      </div>
                    </Carousel>
                    <div class="text">
                      {{ item.text }}
                    </div>
                  </div>
                </div>
              </template>
              <template v-else-if="item.type == 'tree'">
                <Form.Item :validateFirst="true" :key="index" :name="item.name" :label="item.label" :extra="item.extra"
                  :rules="item.rules">
                  <Tree style="margin-left: 20px" :style="{
                    maxHeight:
                      item.maxHeight !== undefined
                        ? item.maxHeight + (typeof item.maxHeight == 'number' ? 'px' : '')
                        : 'un-set',
                  }" :checkable="item.checkable !== undefined ? item.checkable : false"
                    :treeData="item.treeData !== undefined ? item.treeData : []" :checkedKeys="curFormData[item.name] !== undefined ? curFormData[item.name] : undefined
                      " :expandedKeys="item.expandedKeys !== undefined ? item.expandedKeys : undefined"
                    :selectedKeys="item.selectedKeys !== undefined ? item.selectedKeys : undefined"
                    :disabled="item.disabled !== undefined ? item.disabled : false"
                    :selectable="item.selectable !== undefined ? item.selectable : true" :defaultExpandAll="item.defaultExpandAll !== undefined ? item.defaultExpandAll : true
                      " :fieldNames="item.fieldNames !== undefined ? item.fieldNames : undefined"
                    @check="(keys, rows) => treeHandleChange(keys, rows, index, item)">
                  </Tree>
                </Form.Item>
              </template>
              <template v-else-if="item.type == 'ueditor'">
                <Form.Item :validateFirst="true" v-if="item.label" :key="index" :name="item.name" :label="item.label"
                  :extra="item.extra" :rules="item.rules">

                  <SldUEditor style="height:200px;" :id="item.name" :initEditorContent="item.initialValue"
                    :getContentFlag="item.getflag" :getEditorContent="(value) => getEditorContent(value, item.name)" />

                </Form.Item>
              </template>
            </template>
          </template>
        </Form>
      </div>
    </Modal>
  </div>
  <SldSelGoodsSingleDiy :checkedType="checkedType" :api="modalApi" :rowId="rowId" :modalTitle="modalTitle"
    :modalVisible="modalVisible" :column="modalColumn" :formConfig="formConfig" :selectedRows="selectedRows"
    :selectedRowKeys="selectedRowKeys" @confirm-event="tableConfirm" @cancle-event="tableCancle" />
</template>
<script setup>
import { failTip } from '@/utils/utils';
import { LeftCircleOutlined, PlusOutlined, RightCircleOutlined } from '@ant-design/icons-vue';
import {
  Button,
  Carousel,
  Cascader,
  Checkbox,
  CheckboxGroup,
  DatePicker,
  Form,
  Image,
  ImagePreviewGroup,
  InputNumber,
  Modal,
  Popover,
  Radio,
  RadioButton,
  RadioGroup,
  RangePicker,
  Select,
  Space,
  Switch,
  TabPane,
  Table,
  Tabs,
  Tag,
  Timeline,
  TimelineItem,
  Tree,
  TreeSelect,
  Upload
} from 'ant-design-vue';
import dayjs from 'dayjs';
import { computed, getCurrentInstance, nextTick, ref, watch } from 'vue';
import {
  cate_list_attr_column,
  cate_list_attr_schema,
  cate_list_brand_column,
  cate_list_brand_schema,
} from './data';
import { getCommonSendTlVerifyApi } from '/@/api/common/common';
import { getAttrList, getBrandList } from '/@/api/manage/manage';
import { getSmsCode } from '/@/api/sys/account';
import ColorPicker from '/@/components/ColorPicker/index.vue';
import SldSelGoodsSingleDiy from '/@/components/SldSelGoodsSingleDiy/index.vue';
import SldUEditor from '/@/components/SldUEditor/index.vue';
import { useGlobSetting } from '/@/hooks/setting';
import { useUserStore } from '/@/store/modules/user';
import { getToken } from '/@/utils/auth';
import { day_hour } from '/@/utils/utils';
import { mobile_reg, validatorEmoji } from '/@/utils/validate';
import express_empty from '/src/assets/images/express_empty.png';

const vm = getCurrentInstance();
const L = vm?.appContext.config.globalProperties.$sldComLanguage;

const { apiUrl } = useGlobSetting();

const props = defineProps({
  title: { type: String, required: true }, //弹框标题
  visible: { type: Boolean, required: true }, //弹框是否显示
  visible_one: { type: Boolean, required: false }, //弹框是否显示
  confirmBtnLoading: { type: Boolean, default: false }, //弹框确认按钮loading属性，默认不显示
  zIndex: { type: Number, default: 1000 }, //弹框的层级，默认为1000，最小值也为1000
  height: { type: Number }, //弹框内容高度
  width: { type: Number, default: 416 }, //弹框宽度，默认416px
  showFoot: { type: Boolean, default: true }, //是否显示弹框底部操作按钮，默认显示
  confirmBtnText: { type: String }, //弹框底部确认按钮显示文案，默认为确认 在props里使用变量存在作用域的问题 所以将默认文案放到模版里使用
  cancelBtnText: { type: String }, //弹框底部取消按钮显示文案，默认为取消
  content: { type: Array },
  showTopTip: { type: String }, //sldmodal顶部提示
  parentPage: { type: String }, //引用页面，用于方法内的判断处理
  conType: { type: String }, //引用页面，用于方法内的判断处理
  isReset: { type: Boolean, default: false }, //是否重置数据
  resetIds: { type: Array, default: [] }, //需要重置的数据
});

const emit = defineEmits([
  'cancleEvent',
  'confirmEvent',
  'callbackEvent',
  'clickselectEvent',
  'radioChangeEvent',
  'treeSelectEvent',
  'tagSelectEvent',
  'treeSelectChangeEvent',
  'cascaderChangeEvent',
  'loadDataEvent',
  'clickSmsEvent',
  'tagsEvent',
  'ueditorEvent',
  'tableEvent'
]);
const isResetVal = computed(() => {
  return props.isReset;
});
const userStore = useUserStore();
const formRef = ref();
// 重新构造Form表单组件的model数据，主要用于校验，否则校验不生效
const curFormData = ref({});
//由于watch无法监听props.content，所以需要计算属性转为ref对象
const formData = computed(() => {
  return props.content;
});

// 计算每个字段是否应该显示
const shouldShowField = (item) => {
  if (!item.show) return true;
  
  try {
    const result = item.show(curFormData.value || {});
    return result;
  } catch (error) {
    console.error('show函数执行错误:', item.name, error);
    return true; // 出错时默认显示
  }
};
const conType = computed(() => {
  return props.conType;
});
// 弹窗开关
const formVisible = computed(() => {
  return props.visible;
});
// 弹窗开关
const visible_one = computed(() => {
  return props.visible_one;
});
// sldmodal顶部提示
const showTopTip = computed(() => {
  return props.showTopTip;
});
// 定时器
let timers = ref(null);
const imgToken = ref('Bearer ' + getToken() || ''); //上传文件token参数
const countDownM = ref(0); //验证码倒计时
const countDownEvent = ref(null); //验证码倒计时事件
const click_countDown = ref(false); //验证码倒计时事件
const clickFlag = ref(false);

watch(
  formData,
  () => {
    if (typeof conType.value != 'undefined' && conType.value == 'moreCheck') {
    } else {
      if (!clickFlag.value) {
        curFormData.value = {};
        props.content.map((item) => {
          if (item.type == 'date_picker' && item.initialValue) {
            curFormData.value[item.name] = dayjs(
              item.initialValue,
              item.format ? item.format : 'YYYY-MM-DD',
            );
          } else if (item.type == 'range_picker' && item.initialValue) {
            curFormData.value[item.name] = [
              dayjs(item.initialValue[0], item.format ? item.format : 'YYYY-MM-DD'),
              dayjs(item.initialValue[1], item.format ? item.format : 'YYYY-MM-DD'),
            ];
          } else {
            if (item.type != 'onlytxt') {
              curFormData.value[item.name] = item.initialValue;
              if (item.name === 'commissionType' || item.name === 'commission' || item.name === 'commissionRate') {
                console.log('SldModal表单数据更新:', item.name, '=', item.initialValue, '当前curFormData:', JSON.stringify(curFormData.value));
              }
            }
          }
        });
      } else {
        clickFlag.value = false;
      }
    }
  },
  { deep: true, immediate: true },
);

watch(
  formVisible,
  () => {
    imgToken.value = 'Bearer ' + userStore.getToken
    fileList_flag_to.value = false
    if (typeof conType.value != 'undefined' && conType.value == 'moreCheck') {
    } else {
      if (
        !imgToken.value &&
        props.content.length > 0 &&
        props.content.filter((item) => item.type == 'upload_file').length > 0
      ) {
        // getImageToken();
      }
      if (!props.visible && countDownEvent.value) {
        clearInterval(countDownEvent.value);
        countDownEvent.value = '';
        countDownM.value = 0;
      }
    }
  },
  { deep: true },
);

watch(
  isResetVal,
  () => {
    if (props.isReset) {
      if (props.resetIds && props.resetIds.length > 0) {
        formRef.value.resetFields(props.resetIds);
      } else {
        formRef.value.resetFields();
      }
    }
  },
  { deep: true, immediate: true },
)
const getEditorContent = (value, name) => {
  curFormData.value[name] = value;
  emit('ueditorEvent', curFormData.value)
}
const handleTableChange = (value) => {
  console.log('-----', value)
  emit('tableEvent', value)
}
// radio_select 点击添加新的内容
const radioHandleChange = (e, index, it) => {
  if (it.callback) {
    clickFlag.value = false;
    let from_info = [];
    from_info = JSON.parse(JSON.stringify(formData.value));
    from_info.map((item) => {
      if (item.type == 'date_picker' && curFormData.value[item.name]) {
        let timer = JSON.parse(JSON.stringify(curFormData.value[item.name]));
        item.initialValue = dayjs(timer).format(item.format ? item.format : 'YYYY-MM-DD');
      } else if (item.type == 'range_picker' && curFormData.value[item.name]) {
        let timer = JSON.parse(JSON.stringify(curFormData.value[item.name]));
        timer[0] = dayjs(timer[0]).format(item.format ? item.format : 'YYYY-MM-DD');
        timer[1] = dayjs(timer[1]).format(item.format ? item.format : 'YYYY-MM-DD');
        item.initialValue = timer;
      } else {
        item.initialValue = curFormData.value[item.name];
      }
    });
    formRef.value.resetFields();
    emit('radioChangeEvent', e.target.value, index, from_info);
  }
};

// 单选按钮
const permissionSingle = (checkList, index) => {
  let temp = formData.value?.filter(item => item.id == index)
  if (temp.length > 0) {
    let sldchild = temp[0]['sldchild'];
    temp[0]['checkList'] = checkList;
    if (sldchild.length == checkList.length) {
      temp[0]['indeterminate'] = false;
      temp[0]['checkAll'] = true;
    } else {
      temp[0]['indeterminate'] = false;
      temp[0]['checkAll'] = false;
    }
  }
};

// 全选按钮
const permissionAll = (e, index) => {
  let temp = formData.value?.filter(item => item.id == index)
  if (temp.length > 0) {
    temp[0]['checkAll'] = e.target.checked;
    if (e.target.checked) {
      let sldchild = temp[0]['sldchild'];
      if (sldchild.length > 0) {
        for (let i = 0; i < sldchild.length; i++) {
          if (temp[0]['checkList'].indexOf(sldchild[i]['value']) == -1) {
            temp[0]['checkList'].push(sldchild[i]['value']);
          }
        }
      }
    } else {
      temp[0]['checkList'] = [];
    }
    temp[0]['indeterminate'] = false;
  }
};

// 级联选择器change事件
const cascader_change = (e, selectedOptions, item) => {
  if (item.returnName) {
    emit('cascaderChangeEvent', selectedOptions);
  }
};

const loadData = (selectedOptions, items) => {
  if (
    selectedOptions[selectedOptions.length - 1].children &&
    selectedOptions[selectedOptions.length - 1].children.length > 0
  ) {
    return;
  }
  setTimeout(() => {
    if (items.loadData) {
      emit('loadDataEvent', selectedOptions, curFormData.value);
    }
  });
};

// TreeSelect 树选择事件
const treeSelectChange = (e, label, extra, index, it) => {
  if (it.callback) {
    clickFlag.value = false;
    let from_info = [];
    from_info = JSON.parse(JSON.stringify(formData.value));
    from_info.map((item) => {
      if (item.type == 'date_picker' && curFormData.value[item.name]) {
        let timer = JSON.parse(JSON.stringify(curFormData.value[item.name]));
        item.initialValue = dayjs(timer).format(item.format ? item.format : 'YYYY-MM-DD');
      } else if (item.type == 'range_picker' && curFormData.value[item.name]) {
        let timer = JSON.parse(JSON.stringify(curFormData.value[item.name]));
        timer[0] = dayjs(timer[0]).format(item.format ? item.format : 'YYYY-MM-DD');
        timer[1] = dayjs(timer[1]).format(item.format ? item.format : 'YYYY-MM-DD');
        item.initialValue = timer;
      } else {
        item.initialValue = curFormData.value[item.name];
      }
    });
    formRef.value.resetFields();
    emit('treeSelectChangeEvent', e, label, extra, index, from_info);
  }
};

// tree 选择事件
const treeHandleChange = (keys, rows, index, item) => {
  if (item.callback) {
    clickFlag.value = false;
    emit('treeSelectEvent', keys, rows, index);
  }
};

//取消/关闭弹框事件
const sldCancle = () => {
  emit('cancleEvent');
  formData.value.forEach((item, index) => {
    if (
      item.type == 'upload_file' ||
      item.type == 'upload_img_upload' ||
      item.type == 'upload_mp3_upload'
    ) {
      item.fileList = [];
      if (item.img_info) {
        item.img_info = {};
      }
      if (item.mp3Info) {
        item.mp3Info = {};
      }
    } else if (item.type == 'view_video') {
      item.content = '';
    }
  });
};

const freeExpress = (name) => {
  curFormData.value[name] = 0;
};

//确认事件
const sldConfirm = async () => {
  formRef.value
    .validate()
    .then((res) => {
      let seckill_time_select = JSON.parse(JSON.stringify(formData.value)).filter(
        (item) => item.type === 'seckill_time_select',
      );
      if (seckill_time_select && seckill_time_select.length > 0) {
        seckill_time_select.forEach((item) => {
          res[item.name] = item.sel_data;
        });
      }
      formData.value.forEach((item) => {
        if (item.type == 'date_picker' && res[item.name]) {
          res[item.name] = res[item.name].format(item.format ? item.format : 'YYYY-MM-DD');
        }
        if (item.type == 'range_picker' && res[item.name]) {
          let timer = JSON.parse(JSON.stringify(res[item.name]));
          timer[0] = dayjs(timer[0]).format(item.format ? item.format : 'YYYY-MM-DD');
          timer[1] = dayjs(timer[1]).format(item.format ? item.format : 'YYYY-MM-DD');
          res[item.name] = timer;
        }
      });
      emit('confirmEvent', res);
    })
    .catch((err) => {
      console.log('error', err);
    });
};
const onFinish = (values) => {
  console.log('Success:', values);
};
// type为attr_tags start的事件
const showInput = (index, name) => {
  clickFlag.value = true;
  formData.value[index].inputValue = '';
  formData.value[index].inputVisible = true;
  timers.value = setTimeout(() => {
    clickFlag.value = false;
    clearTimeout(timers);
  });
};

// type为attr_tags 输入框事件
const tagInputChange = (e, index) => {
  clickFlag.value = true;
  if (e.target.value && e.target.value.indexOf(',') != -1) {
    failTip(L('请输入合法字符'));
    formData.value[index].inputValue = e.target.value.split(',')[0];
  } else {
    formData.value[index].inputValue = e.target.value;
  }
  setTimeout(() => {
    clickFlag.value = false;
  });
};

let tag_flag = false;
// type为attr_tags  添加属性值事件
const tagInputConfirm = (index, name, type) => {
  if (tag_flag) {
    tag_flag = false;
    return;
  } else if (type == 'press') {
    tag_flag = true;
  }
  let obj = [];
  obj = JSON.parse(JSON.stringify(curFormData.value[name]));
  formData.value[index].inputValue = formData.value[index].inputValue.trim();
  if (obj.length >= formData.value[index].maxNum) {
    failTip(`属性值最多可添加${formData.value[index].maxNum}个～`);
    return false;
  }
  if (obj.indexOf(formData.value[index].inputValue) > -1) {
    failTip('属性值不可以重复～');
    return false;
  }
  clickFlag.value = true;
  obj.push(formData.value[index].inputValue);
  curFormData.value[name] = obj;
  formData.value[index].inputVisible = false;
  formData.value[index].inputValue = '';
  setTimeout(() => {
    clickFlag.value = false;
  });
};

// 属性值删除事件
const tagHandleClose = (removedTag, name, index) => {
  clickFlag.value = true;
  formData.value[index].inputVisible = false;
  formData.value[index].inputValue = '';
  let tar_tag_data = JSON.parse(JSON.stringify(curFormData.value[name])).filter(
    (tag) => tag !== removedTag,
  );
  nextTick(() => {
    curFormData.value[name] = tar_tag_data;
  });
  setTimeout(() => {
    clickFlag.value = false;
  });
};
// type为attr_tags end

const handleSelectChange = (item, val1, val2, val3, val4, val5) => {
  clickFlag.value = true;
  if (!val1 || val1 == null) {
    curFormData.value[item.name] = '';
  }
  if (item.callback) {
    emit('callbackEvent', {
      contentItem: { ...item, eventType: 'change' },
      val1: val1,
      val2: val2,
      val3: val3,
      val4: val4,
      val5: val5,
    });
  }
};

const handleSelTime = (item, it, index) => {
  if (it.callback) {
    clickFlag.value = true;
    if (formData.value[index].sel_data.indexOf(item) > -1) {
      formData.value[index].sel_data = formData.value[index].sel_data.filter((i) => i != item);
    } else {
      formData.value[index].sel_data.push(item);
    }
  }
};

const handleSelectFocus = (item) => {
  if (item.focusCallback) {
    emit('callbackEvent', {
      contentItem: { ...item, eventType: 'focus' },
    });
  }
};

// 点击之前的数据
const fileList_info = [];
const fileList_flag_to = ref(false);

const beforeUploadClick = async () => {
  imgToken.value = 'Bearer ' + userStore.getToken
  if (userStore.getToken) {
    let res = await userStore.getSldCommonService()
    if (res.state == 200) {
      fileList_flag_to.value = true
      imgToken.value = 'Bearer ' + res.token
    }
  }
}

//文件上传前处理数据
function beforeUpload(file, index, accept) {
  if (!fileList_flag_to.value) {
    imgToken.value = 'Bearer ' + userStore.getToken
  }
  if (accept != undefined && accept != null && accept) {
    //校验文件格式类型
    let accept_list = accept
      .replaceAll(' ', '')
      .split(',')
      .filter((item) => item && item.trim());
    let file_type = file.name ? '.' + file.name.split('.').at(-1) : '';
    if (accept_list.length > 0 && file_type != '' && accept_list.indexOf(file_type) == -1) {
      failTip(L('上传文件格式有误'));
      return false;
    }
  }
  let uploadLimit = formData.value[index].limit ? formData.value[index].limit : 20;
  if (file.size > 1024 * 1024 * uploadLimit) {
    failTip(L('上传文件过大，请上传小于') + uploadLimit + L('M的文件'));
    return false;
  }
  fileList_info.value = JSON.parse(JSON.stringify(curFormData.value[formData.value[index].name]));
}

//数据变化事件
function handleFileChange(e, index) {
  if (e.fileList.length > 0 && e.file.response != undefined && e.file.response.state != 200) {
    failTip(e.file.response.msg);
    curFormData.value[formData.value[index].name] =
      fileList_info.value ? JSON.parse(JSON.stringify(fileList_info.value)) : '';
    return;
  }
  if (e.file.status != undefined && e.file.status != 'error') {
    e.fileList.forEach((item) => {
      if (item.response && item.response.data) {
        item.url = item.response.data.url;
      }
    });
    curFormData.value[formData.value[index].name] = e.fileList;
  }
}

function reSelectClick() {
  emit('clickselectEvent');
}

//获取验证码
const getSmsCodeEvent = async (phone, item) => {
  if (click_countDown.value || countDownM.value > 0) return;
  let mobile = curFormData.value[phone];
  let obj = {
    mobile: mobile
  }
  if (item.param) {
    for (let i in item.param) {
      if (!curFormData.value[item.param[i]] || !curFormData.value[item.param[i]].trim()) {
        failTip(item.paramPlace[i]);
        return
      } else {
        obj[item.param[i]] = curFormData.value[item.param[i]].trim()
      }
    }
    delete obj.mobile
  }
  if (!mobile || !mobile.trim()) {
    failTip(L('请输入手机号'));
  } else if (mobile_reg.test(mobile)) {
    click_countDown.value = true;
    let res;
    if (item.api) {
      res = await item.api({
        ...obj
      });
    } else if (item.verification) {
      res = await getCommonSendTlVerifyApi({
        ...obj
      });
    } else {
      res = await getSmsCode({
        type: item.smsType ? item.smsType : 'login',
        mobile,
      });
    }
    if (res.state == 200) {
      if (item.callback) {
        emit('clickSmsEvent', res.data);
      }
      countDownM.value = 60;
      countDownEvent.value = setInterval(() => {
        countDownM.value--;
        if (countDownM.value < 1) {
          clearInterval(countDownEvent.value);
          countDownEvent.value = '';
          countDownM.value = 0;
        }
      }, 1000);
    } else {
      failTip(res.msg);
    }
    click_countDown.value = false;
  }
};

// 颜色选择器
const handleColorUpdate = (val, name) => {
  curFormData.value[name] = 'rgba(' + val.r + ',' + val.g + ',' + val.b + ',' + val.a + ')';
};

const openMaterial = () => {
  console.log('openMaterial');
};

const checkedType = ref(true); // true 多选 false 单选
const rowId = ref(''); //弹窗表格key值
const modalApi = ref(null); //弹窗表格接口
const modalTitle = ref(''); //弹窗表格标题
const modalVisible = ref(false); //弹窗表格是否显示
const modalColumn = ref([]); //弹窗表格列数据
const formConfig = ref([]); //弹窗表格筛选项数据
const selectedRows = ref([]); //弹窗表格选中行
const selectedRowKeys = ref([]); //弹窗表格选中行id
const modalOperateItem = ref(null); //弹窗表格当前操作的对象
const modalOperateType = ref(''); //弹窗表格当前操作的类型

//按钮组件点击事件
function handleTagClick(type, item, record) {
  if (item.callback) {
    modalOperateType.value = type;
    modalOperateItem.value = record;
    let operate_table = false; //当前操作是否需要打开表格弹窗
    if (props.parentPage == 'cate_lists') {
      // 分类管理页
      if (item.name == 'bindBrands') {
        //品牌
        if (type == 'add') {
          //添加
          operate_table = true;
          rowId.value = 'brandId';
          modalApi.value = getBrandList;
          modalTitle.value = L('请选择关联品牌');
          modalColumn.value = cate_list_brand_column;
          formConfig.value = cate_list_brand_schema;
        } else if (type == 'del') {
          //删除
          emit('tagSelectEvent', modalOperateType.value, item, record, null);
        }
      } else if (item.name == 'bindAttributes') {
        // 属性
        if (type == 'add') {
          //添加
          operate_table = true;
          rowId.value = 'attributeId';
          modalApi.value = getAttrList;
          modalTitle.value = L('请选择关联属性');
          modalColumn.value = cate_list_attr_column;
          formConfig.value = cate_list_attr_schema;
        } else if (type == 'del') {
          //删除
          emit('tagSelectEvent', modalOperateType.value, item, record, null);
        }
      }
    }
    if (operate_table) {
      selectedRows.value = item.data ? item.data : [];
      selectedRowKeys.value = item.ids ? item.ids : [];
      modalVisible.value = true;
    }
  }
}

//表格取消事件
function tableCancle() {
  modalVisible.value = false;
  modalTitle.value = '';
}

//表格确认事件
function tableConfirm(rows, rowKeys) {
  emit('tagSelectEvent', modalOperateType.value, modalOperateItem.value, rows, rowKeys);
  tableCancle();
}

const tabsChange = (e, item) => {
  formRef.value.resetFields();
  if (item.callback) {
    emit('tagsEvent', e, item);
  }
}

</script>
<style lang="less">
.tiny_spot {
  .ant-form-item-label {
    label::before {
      content: '*';
      display: inline-block;
      margin-right: 4px;
      color: @primary-color;
      font-family: SimSun, sans-serif;
      font-size: 14px;
      line-height: 1;
    }
  }
}

.ant-modal-body .ant-form-item-with-help .ant-form-item-explain {
  display: flex;
  position: absolute;
  z-index: 2;
  top: -11px;
  right: 8px;
  min-height: 20px;
  background: #fff;
}

.ant-modal-header {
  padding: 12px 24px !important;
  background-color: @primary-color;
}

.ant-modal-title {
  color: #fff;
  font-size: 14px !important;
  line-height: 18px !important;
}

.ant-modal-close-x .anticon svg {
  fill: #fff;
}

.ant-modal .ant-modal-content {
  border-radius: 4px;
}

.ant-form-item-with-help {
  margin-bottom: 24px;
}

.ant-form-item-has-error .ant-input-number,
.ant-form-item-has-error .ant-picker {
  border-color: @primary-color !important;
}

.hover_select {
  position: absolute;
  z-index: 9;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.upload_file {
  .upload_file_btn {
    color: @primary-color;
    font-size: 13px;
    cursor: pointer;
  }

  .upload_file_desc {
    margin-top: 8px;
    color: #999;
    font-size: 12px;
  }
}

.get_sms_code {
  position: relative;

  .get_sms_code_wrap {
    display: flex;
    position: absolute;
    z-index: 9;
    top: 50%;
    right: 0;
    align-items: center;
    justify-content: center;
    width: 94px;
    transform: translateY(-50%);

    .v_split {
      display: inline-block;
      margin-top: -3px;
      opacity: 0.5;
      color: @primary-color;
    }

    .sms_code {
      width: 118px;
      color: @primary-color;
      font-size: 12px;
      text-align: center;
      cursor: pointer;
    }
  }
}

.ant-modal-close {
  top: 12px;
  right: 20px;
  height: auto !important;

  .ant-modal-close-x {
    display: flex !important;
    align-items: center;
    justify-content: center;
    width: auto !important;
    height: auto !important;
    line-height: normal;
  }
}

.flex_row_start_start {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}

.flex_row_center_center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.seckill_time_wrap {
  margin-bottom: 5px;
  border-left: 1px solid #f2f2f2;

  .seckill_time_item {
    width: 76px;
    height: 60px;
    border-top: 1px solid #f2f2f2;
    border-right: 1px solid #f2f2f2;
    border-bottom: 1px solid #f2f2f2;
    background: #fff;
    color: #333;
    font-size: 14px;
  }
}

.flex_row_common {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.flex_com_row_center {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.show_img {
  max-width: 100%;
  max-height: 100%;
}

.ant-image-mask-info {
  display: flex;
  align-items: center;
}

.ImagePreviewGroup {
  .ant-image {
    margin-right: 8px;
    margin-bottom: 8px;
  }
}

.show_editor_con {
  display: flex;
  justify-content: center;
  width: 100%;
}

.title_add_goods {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 20px;
  color: #101010;
  font-size: 14px;
  font-weight: 700;

  span {
    display: inline-block;
    line-height: 0;
    vertical-align: middle;
  }

  .left_border {
    display: inline-block;
    width: 4px;
    height: 16px;
    margin-right: 7px;
  }

  .title {
    color: #333;
    font-size: 14px;
    font-weight: 700;
  }

  .sub_title {
    margin-left: 9px;
    color: @primary-color;
    font-size: 12px;
    font-weight: 400;
  }
}

.ant-upload-list-item-actions {
  display: flex;
  align-items: center;

  &>a {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.attr_tags {
  .ant-tag {
    &>.anticon-close {
      vertical-align: -1px !important;
    }
  }
}

.express_title {
  margin-bottom: 25px;
  padding: 10px 10px 0 20px;
  color: #333;
  font-size: 14px;
  font-weight: 600;

  .content {
    font-size: 12px;
  }
}

.empty_express {
  width: 100%;
  height: 200px;
  margin-bottom: 70px;

  img {
    width: 80px;
  }

  p {
    margin-top: 17px;
    color: #333;
    font-size: 14px;
  }
}

.flex_column_center_center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.flex_column_start_start {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}

.svideo_img_text {
  div {
    width: 300px !important;
    height: 300px;
  }

  .slick-track {
    display: flex;
  }

  .img_wrap {
    display: flex !important;
    width: 300px !important;
    height: 300px;

    img {
      max-width: 100%;
      max-height: 100%;
    }
  }

  .text {
    width: 300px;
    height: auto;
    margin-top: 10px;
    padding: 3px;
  }

  :global {
    .slick-list {
      width: 300px;
      height: 300px;
    }

    .slick-track {
      position: absolute !important;
      height: 300px;
    }

    .slick-next {
      z-index: 9999;
      right: 15px !important;
    }

    .slick-prev {
      z-index: 9999;
      left: 15px !important;
    }

    .slick-prev::before,
    .slick-next::before {
      color: rgb(0 0 0 / 40%);
    }
  }
}

.sld_modal_top_tip {
  position: absolute;
  z-index: 9999;
  top: 42px;
  left: 0;
  width: 100%;
  margin-bottom: 8px;
  padding: 6px 15px;
  background-color: #fff9e2;
}

.flex_row_start_center {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.modal_box {
  .ant-btn {
    border-radius: 4px;
  }

  .ant-form-item {
    margin-bottom: 10px !important;
  }

  .ant-picker-input>input {
    text-align: center;
  }

  .ant-form-item-control {
    margin-bottom: 0px !important;
  }

  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }

  input[type="number"] {
    -moz-appearance: textfield;
  }
}

.tag_list {
  display: flex;
  flex-wrap: wrap;

  .tag_btn {
    .tag_btn_item {
      display: inline-block;
      position: relative;
      height: 38px;
      margin-right: 10px;
      margin-bottom: 10px;
      padding: 0 20px;
      border: 1px solid #ddd;
      border-radius: 4px;
      line-height: 38px;
      cursor: default;

      .tag_btn_item_name {
        display: inline-block;
        max-width: 100%;
        overflow: hidden;
        color: #333;
        font-size: 14px;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .tag_btn_item_img,
      .tag_btn_item_img_hover {
        position: absolute;
        top: 0;
        right: 0;
        width: 25px;
        height: 24px;
        cursor: pointer;
      }

      .tag_btn_item_img {
        display: block;
      }

      .tag_btn_item_img_hover {
        display: none;
      }

      &:hover {
        .tag_btn_item_img {
          display: none;
        }

        .tag_btn_item_img_hover {
          display: block;
        }
      }
    }
  }

  .tag_btn_add {
    display: inline-block;
    height: 32px;
    padding: 0 7px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: @primary-color;
    line-height: 29px;
    cursor: pointer;

    svg {
      position: relative;
      top: 4px;
    }

    span {
      margin-left: 3px;
      color: #fff;
      font-size: 13px;
    }
  }
}

.avoid_style {
  color: @primary-color;
  cursor: pointer;
}

.sldPerminss {
  height: 400px;
  overflow-y: scroll;

  .sldGroup {
    font-weight: 600 !important;
    color: #262626 !important;
  }

  div {
    border-bottom: 1px solid #e9e9e9;
  }

  div:last-child {
    border-bottom: none;
  }

  .ant-checkbox-wrapper {
    padding: 10px !important;
  }

  .ant-checkbox-group {
    display: block;
  }
}
</style>
