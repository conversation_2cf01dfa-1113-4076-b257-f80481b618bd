<template>
  <div>
    <Modal
      destroyOnClose
      :maskClosable="false"
      :title="props.title"
      :visible="modalVisible"
      :width="props.width"
      @ok="sldConfirm"
      @cancel="sldCancle"
    >
      <Form ref="formRef" :model="data">
        <ShowMoreHelpTip
          :tipData="modal_tip"
          :marginTop="0"
          v-if="modal_tip.length > 0"
        ></ShowMoreHelpTip>
        <div class="SldDiySingleImgModal">
          <Table
            :showHeader="false"
            :columns="columns"
            :bordered="true"
            :pagination="false"
            :dataSource="data.data"
            :maxHeight="400"
            :canResize="false"
          >
            <template #bodyCell="{ column, text, record, index }">
              <template v-if="column.dataIndex == 'name'">
                <div class="table_left_con">
                  <span>{{ text }}</span>
                </div>
              </template>
              <template v-if="column.dataIndex == 'type'">
                <template v-if="record.key == 'img'">
                  <div class="modal_img">
                    <div
                      class="adv_01_img_thumb relative"
                      :style="{
                        width: (layout.img_show_width ?? 150) + 'px',
                        height: (layout.img_show_height ?? 150) + 'px',
                      }"
                    >
                      <img
                        :src="record.imgUrl"
                        class="adv_01_img"
                        alt=""
                        v-if="record.imgUrl != ''"
                      />
                      <AliSvgIcon
                        iconName="iconkehubiaoqian"
                        v-else
                        width="30px"
                        height="30px"
                        fillColor="#999"
                      />
                      <span @click.stop="del_img" class="del_img">删除</span>
                    </div>
                    <span class="modal_tip_color"
                      >建议上传宽度为{{ layout.width }}像素、高度{{
                        layout.height == 0 ? '不限制' : layout.width + '像素'
                      }}的图片；支持格式gif，jpg，png。</span
                    >
                    <div>
                      <Button @click="openMaterial">
                        <div class="flex_row_center_center">
                          <UploadOutlined />
                          <span>上传图片</span>
                        </div>
                      </Button>
                    </div>
                  </div>
                </template>
                <template v-if="record.key == 'text'">
                  <div class="modal_img">
                    <Form.Item
                      style="width: 300px"
                      :name="['data', index, 'value']"
                      :rules="[
                        {
                          validator: async (rule, value) => {
                            await validatorEmoji(rule, value);
                          },
                        },
                      ]"
                    >
                      <Input
                        :maxlength="4"
                        style="width: 300px"
                        :placeholder="`请输入${record.label}`"
                        v-model:value="record.value"
                      />
                    </Form.Item>
                  </div>
                </template>
              </template>
            </template>
          </Table>
        </div>
      </Form>
    </Modal>
    <!-- 图片素材选择 start -->
    <SldMaterialImgs
      ref="sldMaterialImgsRef"
      :visibleModal="chooseFile == 1 ? true : false"
      :maxUploadNum="1"
      :allowRepeat="false"
      :selectedData="selectImageData"
      @closeMaterial="() => closeMaterial()"
      @confirmMaterial="(val) => confirmMaterial('image', val)"
    ></SldMaterialImgs>
    <!-- 图片素材选择 end -->
  </div>
</template>
<script>
  export default {
    name: 'SldNavModal',
  };
</script>
<script setup>
  import { ref, onMounted, computed, watch } from 'vue';
  import { failTip, isEmptyObject } from '/@/utils/utils';
  import { Modal, Form, Table, Input, Button } from 'ant-design-vue';
  import ShowMoreHelpTip from '/@/components/ShowMoreHelpTip/index.vue';
  import { UploadOutlined } from '@ant-design/icons-vue';
  import { validatorEmoji } from '/@/utils/validate';
  import SldMaterialImgs from '@/components/SldMaterialFiles/sldMaterialImgs.vue';
  import { cloneDeep } from 'lodash-es';

  const emit = defineEmits(['cancleEvent', 'confirmEvent', 'update:modalVisible']);

  const sldMaterialImgsRef = ref();

  const props = defineProps({
    width: { type: Number, default: 416 }, //弹框宽度，默认416px
    height: { type: Number }, //弹框高度
    title: { type: String, required: true }, //弹框标题
    submiting: { type: Boolean, default: false }, //弹框确认按钮loading属性，默认不显示
    modalVisible: { type: Boolean, required: true }, //弹框是否显示
    content: { type: Object, default: () => {} }, //内容
    modal_tip: { type: Array, default: [] }, //提示语
  });

  const modal_tip = computed(() => {
    return props.modal_tip;
  });

  const show_width = ref(150);
  const show_height = ref(150);

  const layout = ref({});

  const formRef = ref();
  const chooseFile = ref(0); //上传素材弹窗显示 1:图片 2:视频

  //提示语
  //数据
  const data = ref({
    data: [
      {
        key: 'img',
        name: '',
        value: 'https://img.alicdn.com/simba/img/TB1jsBeKYPpK1RjSZFFSuu5PpXa.jpg',
        imgPath: '/images/brand/3e56e20d-453d-4cf8-906f-a928de37ce6a.png',
        type: 'img',
        required: true,
      },
      {
        key: 'link_type',
        name: `操作`,
        value: '',
        type: 'link_type',
      },
    ],
  });

  const columns = ref([
    { dataIndex: 'name', width: 150, align: 'right' },
    { dataIndex: 'type', align: 'left' },
  ]);

  const selectImageData = ref({ ids: [], data: [] });

  watch(
    () => props.modalVisible,
    () => {
      if (props.modalVisible) {
        if (!isEmptyObject(props.content)) {
          let ope_data = cloneDeep(props.content);
          let tmp_info = [];
          let selectImageDatas = { ids: [], data: [] };
          if (ope_data.img) {
            tmp_info.push({
              key: 'img',
              name: ope_data.img.label ? ope_data.img.label : `图片`,
              ...ope_data.img,
            });
            if (ope_data.img.imgPath) {
              selectImageDatas = {
                // @ts-ignore
                ids: [Number(ope_data.img.imgPath.split('?bindId=')[1])],
                // @ts-ignore
                data: [
                  // @ts-ignore
                  {
                    bindId: ope_data.img.imgPath.split('?bindId=')[1],
                    checked: true,
                    filePath: ope_data.img.imgPath,
                    fileUrl: ope_data.img.imgUrl,
                    fileType: 1,
                  },
                ],
              };
            }
            selectImageData.value = selectImageDatas;
          }

          if (ope_data.text) {
            tmp_info.push({
              key: 'text',
              name: ope_data.text.label ? ope_data.text.label : `操作`,
              ...ope_data.text,
            });
          }
          layout.value = props.content.layout ?? {};
          data.value.data = tmp_info;
        }
      }
    },
  );

  //关闭上传素材文件
  function closeMaterial() {
    chooseFile.value = 0;
  }

  const openMaterial = () => {
    chooseFile.value = 1;
  };

  const confirmMaterial = (type, val) => {
    data.value.data[0].imgUrl = val.data.length ? val.data[0].fileUrl : '';
    data.value.data[0].imgPath = val.data.length ? val.data[0].filePath : '';
    chooseFile.value = 0;
  };

  //删除该图片对应的数据
  const del_img = (index) => {
    data.value.data[0].imgUrl = '';
    data.value.data[0].imgPath = '';
  };

  const sldConfirm = () => {
    let tmp_info = {};
    for (let i in data.value.data) {
      if (data.value.data[i].key == 'img') {
        tmp_info.imgUrl = data.value.data[i].imgUrl;
        tmp_info.imgPath = data.value.data[i].imgPath;
      } else if (data.value.data[i].key == 'text') {
        tmp_info.text = data.value.data[i].value;
      }
    }
    emit('confirmEvent', tmp_info);
  };

  const sldCancle = () => {
    emit('cancleEvent');
    emit('update:modalVisible', false);
  };

  onMounted(() => {});
</script>
<style lang="less">
  .SldDiySingleImgModal {
    .ant-form-item-with-help {
      margin-bottom: 0;
    }

    .ant-form-item {
      margin-bottom: 0;
    }

    .table_left_con {
      color: #333;
      font-size: 13px;
    }

    .table_left_require {
      color: red;
    }
  }
</style>
