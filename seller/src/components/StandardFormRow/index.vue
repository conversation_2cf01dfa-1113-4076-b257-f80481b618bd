<!-- 公共表单列表组件 -->
<template>
  <div class="standard_form_row" :class="hideBorder ? 'hide_' + hideBorder : ''">
    <Form ref="rowForm" :model="curFormData">
      <div
        v-for="(item, index) in props.data"
        :key="index"
        :style="{
          width:
            item.type == 'title'
              ? '100%'
              : item.item_width != undefined
              ? item.item_width + (typeof item.item_width == 'number' ? 'px' : '')
              : width,
        }"
        class="common_list"
      >
        <div
          v-if="item.type == 'title'"
          class="common_title"
          :style="{
            backgroundColor: item.bgColor ? item.bgColor : '',
          }"
        >
          <div class="common_title_label">
            <template v-if="item.require"><span class="common_item_require">*</span></template>{{ item.label }}
          </div>
          <div
            v-if="item.desc"
            class="common_title_desc"
          >{{ item.desc }}</div>
        </div>
        <div v-else-if="item.type == 'show_text'" class="common_item">
          <div
            class="common_item_left"
            :style="{
               width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
              minWidth:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            :style="{
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
            >{{ item.value || '--' }}</div
          >
        </div>
        <div v-else-if="item.type == 'input'" class="common_item">
          <div
            class="common_item_left"
            :style="{
               width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
              minWidth:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            :style="{
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
          >
            <FormItem
              :validateFirst="true"
              :name="item.key != undefined ? item.key : 'form_row_' + index"
              :rules="
                item.rules != undefined
                  ? [
                      ...item.rules,
                      {
                        validator: async (rule, value) => {
                          await validatorEmoji(rule, value);
                        },
                      },
                    ]
                  : [
                      {
                        validator: async (rule, value) => {
                          await validatorEmoji(rule, value);
                        },
                      },
                    ]
              "
              :style="{
                width: item.right_width
                  ? item.right_width +
                    (typeof item.right_width == 'number' ? 'px !important' : ' !important')
                  : '300px !important',
              }"
            >
              <Input
                :placeholder="item.placeholder ? item.placeholder : '请输入'"
                :disabled="item.disable === true ? item.disable : false"
                :allowClear="item.allowClear ? item.allowClear : false"
                :maxlength="item.maxlength ? item.maxlength : 255"
                v-model:value="curFormData[item.key]"
                @change="(e) => handleChange(item, e.target.value, null)"
                :style="{
                  width: item.right_width
                    ? item.right_width +
                      (typeof item.right_width == 'number' ? 'px !important' : ' !important')
                    : '300px !important',
                }"
              />
            </FormItem>
            <div
              v-if="item.desc"
              class="common_item_right_desc"
              :style="{
                width: item.desc_width
                  ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                  : '300px',
              }"
              >{{ item.desc }}</div
            >
          </div>
        </div>
        <div v-else-if="item.type == 'inputnum'" class="common_item">
          <div
            class="common_item_left"
            :style="{
               width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
              minWidth:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            :style="{
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
          >
            <FormItem
              :name="item.key != undefined ? item.key : 'form_row_' + index"
              :rules="item.rules != undefined ? item.rules : undefined"
              :style="{
                width: item.right_width
                  ? item.right_width +
                    (typeof item.right_width == 'number' ? 'px !important' : ' !important')
                  : '300px !important',
              }"
            >
              <InputNumber
                :placeholder="item.placeholder ? item.placeholder : '请输入'"
                :min="item.min ? item.min : 0"
                :max="item.max ? item.max : 9999999"
                :precision="item.precision ? item.precision : 0"
                :disabled="item.disable === true ? item.disable : false"
                v-model:value="curFormData[item.key]"
                @change="(e) => handleChange(item, e, null)"
                :style="{
                  width: item.right_width
                    ? item.right_width +
                      (typeof item.right_width == 'number' ? 'px !important' : ' !important')
                    : '300px !important',
                }"
              />
            </FormItem>
            <div
              v-if="item.desc"
              class="common_item_right_desc"
              :style="{
                width: item.desc_width
                  ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                  : '300px',
              }"
              >{{ item.desc }}</div
            >
          </div>
        </div>
        <div v-else-if="item.type == 'textarea'" class="common_item">
          <div
            class="common_item_left"
            :style="{
              width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
              minWidth:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            :style="{
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
          >
            <FormItem
              :name="item.key != undefined ? item.key : 'form_row_' + index"
              :rules="item.rules != undefined ? item.rules : undefined"
              :style="{
                width: item.right_width
                  ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                  : '300px',
              }"
            >
              <a-textarea
                :placeholder="item.placeholder ? item.placeholder : '请输入'"
                :maxlength="item.maxlength ? item.maxlength : 255"
                :rows="item.rows ? item.rows : 3"
                :autoSize="{ minRows: item.rows ? item.rows : 3 }"
                :showCount="item.showCount ? item.showCount : false"
                :allowClear="item.allowClear ? item.allowClear : false"
                :disabled="item.disabled ? item.disabled : false"
                v-model:value="curFormData[item.key]"
                @change="(e) => handleChange(item, e.target.value, null)"
                :style="{
                  width: item.right_width
                    ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                    : '300px',
                }"
              />
            </FormItem>
            <div
              v-if="item.desc"
              class="common_item_right_desc"
              :style="{
                width: item.desc_width
                  ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                  : '300px',
              }"
              >{{ item.desc }}</div
            >
          </div>
        </div>
        <div v-else-if="item.type == 'ueditor'" class="common_item">
          <div
            class="common_item_left"
            :style="{
              width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
              minWidth:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            :style="{
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
          >
            <FormItem
              :name="item.key != undefined ? item.key : 'form_row_' + index"
              :rules="item.rules != undefined ? item.rules : undefined"
              :style="{
                width: item.right_width
                  ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                  : '300px',
              }"
            >
              
                <SldUEditor :id="item.key" :initEditorContent="item.value"
                   :getEditorContent="(value) => handleChange(item, value, null)" />
            </FormItem>
          </div>
        </div>
        <div v-else-if="item.type == 'select'" class="common_item">
          <div
            class="common_item_left"
            :style="{
               width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
              minWidth:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            :style="{
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
          >
            <FormItem
              :name="item.key != undefined ? item.key : 'form_row_' + index"
              :rules="item.rules != undefined ? item.rules : undefined"
              :style="{
                width: item.right_width
                  ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                  : '300px',
              }"
            >
              <Select
                :placeholder="item.placeholder ? item.placeholder : '请选择'"
                :mode="item.mode ? item.mode : ''"
                :allowClear="item.allowClear ? item.allowClear : false"
                :disabled="item.disabled ? item.disabled : false"
                :defaultOpen="item.defaultOpen ? item.defaultOpen : false"
                :showSearch="item.showSearch ? item.showSearch : false"
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                v-model:value="curFormData[item.key]"
                @change="(e) => handleChange(item, e, null)"
                :style="{
                  width: item.right_width
                    ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                    : '300px',
                }"
              >
                <template v-if="item.data && item.data.length > 0">
                  <SelectOption
                    v-for="(items, indexs) in item.data"
                    :key="indexs"
                    :value="item.diy && item.diy_value ? items[item.diy_value] : items.value"
                    >{{
                      item.diy && item.diy_key ? items[item.diy_key] : items.title
                    }}</SelectOption
                  >
                </template>
              </Select>
            </FormItem>
            <div
              v-if="item.desc"
              class="common_item_right_desc"
              :style="{
                width: item.desc_width
                  ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                  : '300px',
              }"
              >{{ item.desc }}</div
            >
          </div>
        </div>
        <div v-else-if="item.type == 'radio'&&!item.hidden" class="common_item">
          <div
            class="common_item_left"
            :style="{
               width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
              minWidth:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            :style="{
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
          >
            <FormItem
              :name="item.key != undefined ? item.key : 'form_row_' + index"
              :rules="item.rules != undefined ? item.rules : undefined"
              :style="{
                width: item.right_width
                  ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                  : '',
              }"
            >
              <RadioGroup
                :disabled="item.disabled ? item.disabled : false"
                v-model:value="curFormData[item.key]"
                @change="(e) => handleChange(item, e.target.value, null)"
                :style="{
                  width: item.right_width
                    ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                    : '',
                }"
              >
                <div :class="{'flex_column_start_start':item.flex_type=='column'}">
                <Radio :style="{marginTop:item.flex_type=='column'&&indexs!=0?'5px':'0px'}" :value="item.diy && item.diy_key ? items[item.diy_key] : items.value" v-for="(items,indexs) in item.options" :key="indexs">{{ item.diy && item.diy_value ? items[item.diy_value] : items.label }}</Radio>
                
                </div>
              </RadioGroup>
            </FormItem>
            <template v-if="item.desc">
              <template v-if="Array.isArray(item.desc)">
                <div
                  v-for="(items,keyss) in item.desc" :key="keyss"
                  class="common_item_right_desc"
                  :style="{
                    width: item.desc_width
                      ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                      : '300px',
                  }"
                  >{{ items }}</div
                >
              </template>
              <template v-else>
                <div
                  class="common_item_right_desc"
                  :style="{
                    width: item.desc_width
                      ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                      : '300px',
                  }"
                  >{{ item.desc }}</div
                >
              </template>
            </template>
          </div>
        </div>
        <div v-else-if="item.type == 'radio_button'" class="common_item">
          <div
            class="common_item_left"
            :style="{
               width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
              minWidth:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            :style="{
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
          >
            <FormItem
              :name="item.key != undefined ? item.key : 'form_row_' + index"
              :rules="item.rules != undefined ? item.rules : undefined"
              :style="{
                width: item.right_width
                  ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                  : '',
              }"
            >

              <RadioGroup
                :disabled="item.disabled ? item.disabled : false"
                v-model:value="curFormData[item.key]"
                @change="(e) => handleChange(item, e.target.value, null)"
                :style="{
                  width: item.right_width
                    ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                    : '',
                }"
              >
                <template v-for="(items,indexs) in item.options" :key="indexs">
                  <RadioButton :value="items.value">{{ items.label }}</RadioButton>
                </template>
              </RadioGroup>
            </FormItem>
            <div
              v-if="item.desc"
              class="common_item_right_desc"
              :style="{
                width: item.desc_width
                  ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                  : '300px',
              }"
              >{{ item.desc }}</div
            >
          </div>
        </div>
        <div v-else-if="item.type == 'checkbox'" class="common_item">
          <div
            class="common_item_left"
            :style="{
               width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
              minWidth:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            :style="{
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
          >
            <FormItem
              :name="item.key != undefined ? item.key : 'form_row_' + index"
              :rules="item.rules != undefined ? item.rules : undefined"
              :style="{
                width: item.right_width
                  ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                  : '',
              }"
            >
              <CheckboxGroup
                :disabled="item.disabled ? item.disabled : false"
                v-model:value="curFormData[item.key]"
                @change="(e) => handleChange(item, e, null)"
                :style="{
                  width: item.right_width
                    ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                    : '',
                }"
              >
                <template v-if="item.data && item.data.length > 0">
                  <Checkbox
                    v-for="(items, indexs) in item.data"
                    :key="indexs"
                    :value="item.diy && item.diy_value ? items[item.diy_value] : items.value"
                    :style="{
                      width: item.checkbox_width
                        ? item.checkbox_width + (typeof item.checkbox_width == 'number' ? 'px' : '')
                        : '',
                      margin: '2px 0',
                      padding: 0,
                    }"
                    >{{ item.diy && item.diy_key ? items[item.diy_key] : items.title }}</Checkbox
                  >
                </template>
              </CheckboxGroup>
            </FormItem>
            <div
              v-if="item.desc"
              class="common_item_right_desc"
              :style="{
                width: item.desc_width
                  ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                  : '300px',
              }"
              >{{ item.desc }}</div
            >
          </div>
        </div>
        <div v-else-if="item.type == 'cascader'" class="common_item">
          <div
            class="common_item_left"
            :style="{
               width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
              minWidth:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            :style="{
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
          >
            <FormItem
              :name="item.key != undefined ? item.key : 'form_row_' + index"
              :rules="item.rules != undefined ? item.rules : undefined"
              :style="{
                width: item.right_width
                  ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                  : '300px',
              }"
            >
              <Cascader
                :placeholder="item.placeholder ? item.placeholder : '请选择'"
                :options="item.data ? item.data : []"
                :allowClear="item.allowClear ? item.allowClear : false"
                :disabled="item.disabled ? item.disabled : false"
                :expandTrigger="item.expandTrigger ? item.expandTrigger : 'click'"
                :fieldNames="
                  item.fieldNames
                    ? item.fieldNames
                    : {
                        label: 'label',
                        value: 'value',
                        children: 'children',
                      }
                "
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                :showSearch="item.showSearch ? item.showSearch : false"
                :matchInputWidth="item.matchInputWidth ? item.matchInputWidth : true"
                v-model:value="curFormData[item.key]"
                @change="(val, options) => handleChange(item, val, options)"
                :style="{
                  width: item.right_width
                    ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                    : '300px',
                }"
              />
            </FormItem>
            <div
              v-if="item.desc"
              class="common_item_right_desc"
              :style="{
                width: item.desc_width
                  ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                  : '300px',
              }"
              >{{ item.desc }}</div
            >
          </div>
        </div>
        <div v-else-if="item.type == 'upload_img'" class="common_item">
          <div
            class="common_item_left"
            :style="{
               width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
              minWidth:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span><div><p>{{ item.label }}</p><p v-if="item.note" class="common_item_require" style="font-weight: bold;">{{ item.note }}</p></div>
            </div
          >
          <div
            class="common_item_right"
            :style="{
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
          >
            <FormItem
              :name="item.key != undefined ? item.key : 'form_row_' + index"
              :rules="item.rules != undefined ? item.rules : undefined"
              :style="{
                width: item.right_width
                  ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                  : '300px',
              }"
            >
              <Upload
                :accept="item.accept ? item.accept : '.jpg, .jpeg, .png, .gif'"
                :action="item.action"
                :method="item.method ? item.method : 'post'"
                :beforeUpload="
                  (file, fileList) =>
                    beforeUpload(
                      file,
                      fileList,
                      item.accept ? item.accept : '.jpg, .jpeg, .png, .gif',
                    )
                "
                :data="item.data ? item.data : {}"
                :disabled="item.disabled ? item.disabled : false"
                listType="picture-card"
                :fileList="item.fileList ? item.fileList : []"
                :multiple="item.multiple ? item.multiple : false"
                @change="(file, fileList) => handleChange(item, file, fileList)"
                @preview="(file) => handlePreview(item, file)"
                @click="beforeUploadClick"
                :style="{
                  width: item.right_width
                    ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                    : '300px',
                }"
                :headers="{
                  Authorization: imgToken,
                }"
              >
                <div
                  v-if="
                    item.limit != undefined
                      ? item.fileList.length < item.limit
                      : !item.fileList || item.fileList.length == 0
                  "
                  class="upload_image"
                >
                  <PlusOutlined v-if="true" />
                  <LoadingOutlined v-else />
                  <span>上传图片</span>
                </div>
              </Upload>
            </FormItem>
            <div
              v-if="item.desc"
              class="common_item_right_desc"
              :style="{
                width: item.desc_width
                  ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                  : '300px',
              }"
              >
                <a v-if="item.href" :href="item.href" target="_blank">{{ item.desc }}</a>
                <span v-else>{{ item.desc }}</span>
              </div
            >
          </div>
        </div>
        <div v-else-if="item.type == 'goods_category'" class="common_item">
          <div
            class="common_item_left"
            :style="{
               width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
              minWidth:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            :style="{
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
          >
            <div class="flex_row_start_center goods_category_label">
              <div class="goods_category_label_content">{{ item.value ? item.value : '--' }}</div>
              <div class="goods_category_label_btn" v-if="!item.btnFlag" @click="resetGoodsCategory(item)">{{
                item.btnText ? item.btnText : '选择'
              }}</div>
            </div>
            <div
              v-if="item.desc"
              class="common_item_right_desc"
              :style="{
                width: item.desc_width
                  ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                  : '300px',
              }"
              >{{ item.desc }}</div
            >
          </div>
        </div>
        <div v-else-if="item.type == 'material_file'&&!item.draggable" class="common_item">
          <div
            class="common_item_left"
            :style="{
               width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
              minWidth:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            :style="{
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
          >
            <FormItem
              :name="item.key != undefined ? item.key : 'form_row_' + index"
              :rules="item.rules != undefined ? item.rules : undefined"
              :style="{
                width: item.right_width
                  ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                  : '300px',
              }"
            >
              <Upload
                listType="picture-card"
                :disabled="item.disabled ? item.disabled : false"
                :fileList="item.fileList ? item.fileList : []"
                :open-file-dialog-on-click="false"
                @change="(file, fileList, event) => handleChangeUpload(item, file, fileList, event)"
                @preview="(file) => handlePreview(item, file)"
                :style="{
                  width: item.right_width
                    ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                    : '300px',
                }"
              >
                <div
                  v-if="
                    item.limit != undefined
                      ? item.fileList.length < item.limit
                      : !item.fileList || item.fileList.length == 0
                  "
                  class="upload_image"
                  @click="handleMaterial(item, index)"
                  @touchstart.prevent="handleMaterial(item, index)"
                >
                  <PlusOutlined v-if="true" />
                  <LoadingOutlined v-else />
                  <span>{{ item.upload_text ? item.upload_text : '上传' }}</span>
                </div>
              </Upload>
            </FormItem>
            <div
              v-if="item.desc"
              class="common_item_right_desc"
              :style="{
                width: item.desc_width
                  ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                  : '300px',
              }"
              >{{ item.desc }}</div
            >
          </div>
        </div>
        <div v-else-if="item.type == 'material_file'&&item.draggable" class="common_item">
          <div
            class="common_item_left"
            :style="{
               width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
              minWidth:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            :style="{
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
          >
            <FormItem
              :name="item.key != undefined ? item.key : 'form_row_' + index"
              :rules="item.rules != undefined ? item.rules : undefined"
              :style="{
                maxWidth: item.right_width
                  ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                  : '300px',
              }"
            >
              <div class="flex_row_start_start imgList_dr_box" style="flex-wrap:wrap;">
                <div class="imgList_dr">
                    <VueDraggable
                    v-model="item.fileList"
                    animation="150"
                    filter=".forbid"
                    :group="{
                        name:'itxst'+index,//组名为itxst
                        pull: false,//是否允许拖出当前组
                        put:false,//是否允许拖入当前组
                    }"
                    class="flex_row_start_start left_menu_body" style="flex-wrap:wrap;"
                  >
                    <div class="imgList" v-for="(it,ind) in item.fileList" :key="it.uid">
                      <a>
                        <div class="mask">
                          <EyeOutlined class="icon-preview" @click="handlePreview(item,{url:it.thumbUrl})" />
                          <DeleteOutlined class="icon-delete" @click="deleteImg({...item,index:index},it)" />
                        </div>
                      </a>
                      <img :src="it.thumbUrl" alt="" class="img" />
                    </div>
                    <Upload
                        class="forbid"
                      :showUploadList="false"
                      listType="picture-card"
                      :maxCount="item.limit?item.limit:undefined"
                      :disabled="item.disabled ? item.disabled : false"
                      :fileList="item.fileList ? item.fileList : []"
                      :open-file-dialog-on-click="false"
                      @change="(file, fileList, event) => handleChangeUpload({...item,index:index}, file, fileList, event)"
                      @preview="(file) => handlePreview(item, file)"
                      :style="{
                        width: 'auto',
                      }"
                      v-if="item.limit != undefined
                            ? item.fileList.length < item.limit
                            : !item.fileList || item.fileList.length == 0"
                    >
                      <div
                        v-if="
                          item.limit != undefined
                            ? item.fileList.length < item.limit
                            : !item.fileList || item.fileList.length == 0
                        "
                        class="upload_image"
                        @click="handleMaterial(item, index)"
                        @touchstart.prevent="handleMaterial(item, index)"
                      >
                        <PlusOutlined v-if="true" />
                        <LoadingOutlined v-else />
                        <span>{{ item.upload_text ? item.upload_text : '上传' }}</span>
                      </div>
                    </Upload>
                  </VueDraggable>
                </div>
                
              </div>
            </FormItem>
            <div
              v-if="item.desc"
              class="common_item_right_desc"
              :style="{
                width: item.desc_width
                  ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                  : '300px',
              }"
              >{{ item.desc }}</div
            >
          </div>
        </div>
        <div v-else-if="item.type == 'material_video'" class="common_item">
          <div
            class="common_item_left"
            :style="{
               width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
              minWidth:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            :style="{
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
          >
            <FormItem
              :name="item.key != undefined ? item.key : 'form_row_' + index"
              :rules="item.rules != undefined ? item.rules : undefined"
              :style="{
                width: item.right_width
                  ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                  : '300px',
              }"
            >
              <div
                v-if="(item.fileList.length > 0 && item.fileList[0].status == 'done' && item.fileList[0].response != undefined)"
                class="material_video_show">
                <video width="102" height="102" :src="item.fileList[0].response.data.url" controls autoPlay></video>
                <div class="material_video_del" @click="deleteMaterial(item)">
                  <AliSvgIcon
                    width="18px"
                    height="18px"
                    iconName="iconqingchu"
                    fillColor="#c8c8c8"
                  />
                </div>
              </div>
              <Upload
                v-else
                listType="picture-card"
                :disabled="item.disabled ? item.disabled : false"
                :fileList="item.fileList ? item.fileList : []"
                :open-file-dialog-on-click="false"
                @change="(file, fileList, event) => handleChangeUpload(item, file, fileList, event)"
                @preview="(file) => handlePreview(item, file)"
                :style="{
                  width: item.right_width
                    ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                    : '300px',
                }"
              >
                <div
                  v-if="
                    item.limit != undefined
                      ? item.fileList.length < item.limit
                      : !item.fileList || item.fileList.length == 0
                  "
                  class="upload_image"
                  @click="handleMaterial(item, index)"
                  @touchstart.prevent="handleMaterial(item, index)"
                >
                  <PlusOutlined v-if="true" />
                  <LoadingOutlined v-else />
                  <span>{{ item.upload_text ? item.upload_text : '上传' }}</span>
                </div>
              </Upload>
            </FormItem>
            <div
              v-if="item.desc"
              class="common_item_right_desc"
              :style="{
                width: item.desc_width
                  ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                  : '300px',
              }"
              >{{ item.desc }}</div
            >
          </div>
        </div>
        <div v-else-if="item.type == 'goods_spec'" class="common_item">
          <div
            class="common_item_left"
            style="position: absolute; top: 0; bottom: 0; height: auto"
            :style="{
               width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
              minWidth:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            style="height: auto; padding: 15px 20px"
            :style="{
              marginLeft:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
            }"
          >
            <div
              style="width: 100%; margin-bottom: 15px"
              v-for="(spec_item, spec_index) in item.selectSpecList"
              :key="spec_index"
            >
              <div class="flex_row_start_center goods_spec">
                <div class="goods_spec_label">规格名：</div>
                <Select
                  show-search
                  :show-arrow="false"
                  optionFilterProp="label"
                  :defaultActiveFirstOption="false"
                  placeholder="请输入或选择规格名"
                  :value="
                    spec_item.selectedSpec && spec_item.selectedSpec.specName
                      ? spec_item.selectedSpec.specName
                      : undefined
                  "
                  @change="(e) => handleSpec('selectSpec', e, spec_index, null)"
                  @keydown.enter.native="(e) => handleSpec('addSpecDown', e.target.value, spec_index, null)"
                  style="width: 200px; margin-left: 5px"
                >
                  <SelectOption
                    v-for="(items, indexs) in item.specList"
                    :key="indexs"
                    :value="items.value"
                    :label="items.label"
                    >{{ items.label }}</SelectOption
                  >
                </Select>
                <Checkbox
                  v-if="spec_item.selectedSpec && spec_item.selectedSpec.specId"
                  style="margin-left: 20px"
                  :checked="spec_item.setImg !== undefined ? spec_item.setImg : false"
                  @click="(e) => handleSpec('setSpecImg', !spec_item.setImg, spec_index, null)"
                  >设置图片规格</Checkbox
                >
                <AliSvgIcon
                  class="goods_spec_label_delbtn"
                  width="17px"
                  height="17px"
                  iconName="iconqingchu"
                  fillColor="#c8c8c8"
                  @click="(e) => handleSpec('deleteSpec', e, spec_index, null)"
                />
              </div>
              <div
                v-if="spec_item.selectedSpec && spec_item.selectedSpec.specName"
                class="flex_row_start_start goods_spec_value"
              >
                <div class="goods_spec_value_label">规格值：</div>
                <div class="flex_row_start_center goods_spec_value_list">
                  <div
                    v-for="(spec_val_item, spec_val_index) in spec_item.selectedValueSpec"
                    :key="spec_val_index"
                    class="goods_spec_value_select"
                  >
                    <Select
                      show-search
                      :show-arrow="false"
                      optionFilterProp="label"
                      :defaultActiveFirstOption="false"
                      placeholder="请输入或选择规格项"
                      :value="spec_val_item.specValue ? spec_val_item.specValue : undefined"
                      @change="(e) => handleSpec('selectSpecVal', e, spec_index, spec_val_index)"
                      @keydown.enter.native="(e) => handleSpec('addSpecValDown', e.target.value, spec_index, spec_val_index)"
                      @blur="(e) => handleSpec('selectSpecVal', e.target.value, spec_index, spec_val_index)"
                      @search="(val) => handleSpec('selectSpecVal', val, spec_index, spec_val_index)"
                      style="width: 200px; margin-left: 5px"
                    >
                      <SelectOption
                        v-for="(items, indexs) in spec_item.specValueList"
                        :key="indexs"
                        :value="
                          item.spec_val_diy && item.spec_val_diy_value
                            ? items[item.spec_val_diy_value]
                            : items.value
                        "
                        :label="
                          item.spec_val_diy && item.spec_val_diy_key
                            ? items[item.spec_val_diy_key]
                            : items.title
                        "
                        >{{
                          item.spec_val_diy && item.spec_val_diy_key
                            ? items[item.spec_val_diy_key]
                            : items.title
                        }}</SelectOption
                      >
                    </Select>
                    <AliSvgIcon
                      class="goods_spec_value_select_delbtn"
                      width="17px"
                      height="17px"
                      iconName="iconqingchu"
                      fillColor="#ff711e"
                      @click="(e) => handleSpec('deleteSpecVal', e, spec_index, spec_val_index)"
                    />
                  </div>
                  <div
                    v-if="!(spec_item.selectedValueSpec!=undefined &&spec_item.selectedValueSpec.length!=undefined&&spec_item.selectedValueSpec.length>=addGoodsSpecValLimit)"
                    class="goods_spec_value_btn"
                    @click="(e) => handleSpec('addSpecVal', null, spec_index, null)"
                    >添加规格值</div
                  >
                </div>
              </div>
            </div>
            <div
              v-if="
                !item.selectSpecList ||
                item.selectSpecList.length == 0 ||
                Object.keys(item.selectSpecList[item.selectSpecList.length - 1].selectedSpec)
                  .length > 0
              "
              class="goods_spec_btn"
            >
              <div class="goods_spec_btn_add" @click="handleSpec('add', null, null, null)"
                >添加规格项</div
              >
            </div>
          </div>
        </div>
        <div v-else-if="item.type == 'add_reserve_info'" class="common_item">
          <div
            class="common_item_left"
            style="position: absolute; top: 0; bottom: 0; height: auto"
            :style="{
              width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
              minWidth:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            style="height: auto; padding: 15px 20px"
            :style="{
              marginLeft:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
            }"
          >
            <div
              style="width: 100%; margin-bottom: 15px"
              v-for="(reserve_item, reserve_index) in item.data"
              :key="reserve_index"
            >
              <div class="flex_row_start_center reserve_info">
                <div class="reserve_info_xing">*</div>
                <FormItem
                  :validateFirst="true"
                  :name="['sld_form', index, 'data', reserve_index, 'reserveName']"
                  :rules="[{ required: true, whitespace: true, message: '该项必填' },{ validator: async (rule, value) => { await validatorEmoji(rule, value); } }]"
                  :style="{
                    width: item.right_width
                      ? item.right_width +
                        (typeof item.right_width == 'number' ? 'px !important' : ' !important')
                      : '230px !important',
                  }"
                >
                  <Input
                    placeholder="请输入预留信息名称,最多10个字"
                    :maxlength="10"
                    :value="reserve_item.reserveName !== undefined ? reserve_item.reserveName : undefined"
                    @change="(e) => handleReserveInfo('edit', item, 'reserveName', reserve_index, e.target.value, ['sld_form', index, 'data', reserve_index, 'reserveName'])"
                    :style="{
                      width: item.right_width
                        ? item.right_width +
                          (typeof item.right_width == 'number' ? 'px !important' : ' !important')
                        : '230px !important',
                    }"
                  />
                </FormItem>
                <Select
                  placeholder="请选择"
                  :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                  :value="reserve_item.reserveType !== undefined ? reserve_item.reserveType : undefined"
                  @change="(e) => handleReserveInfo('edit', item, 'reserveType', reserve_index, e, null)"
                  style="width: 110px; margin-left: 15px"
                >
                  <SelectOption
                    v-for="(items, indexs) in item.select_list"
                    :key="indexs"
                    :value="items.value"
                    :label="item.label"
                    >{{ items.label }}</SelectOption
                  >
                </Select>
                <Checkbox
                  :checked="reserve_item.isRequired !== undefined ? reserve_item.isRequired : false"
                  @click="(e) => handleReserveInfo('edit', item, 'isRequired', reserve_index, !reserve_item.isRequired, null)"
                  style="margin-left: 15px"
                  >必填</Checkbox
                >
                <AliSvgIcon
                  width="16px"
                  height="15px"
                  iconName="iconshanchu7"
                  fillColor="#e20e0e"
                  @click="(e) => handleReserveInfo('del', item, null, reserve_index, e, ['sld_form', index, 'data', reserve_index, 'reserveName'])"
                />
              </div>
            </div>
            <div class="reserve_info_btn" @click="handleReserveInfo('add', item, null, null, null, null)">新增预留信息</div>
          </div>
        </div>
        <div v-else-if="item.type == 'tree_select'" class="common_item">
          <div
            class="common_item_left"
            :style="{
               width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
              minWidth:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            :style="{
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
          >
            <FormItem
              :name="item.key != undefined ? item.key : 'form_row_' + index"
              :rules="item.rules != undefined ? item.rules : undefined"
              :style="{
                width: item.right_width
                  ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                  : '',
              }"
            >
              <TreeSelect
                v-if="item.fieldNames"
                v-model:value="curFormData[item.key]"
                :treeData="item.data !== undefined ? item.data : []"
                
                :placeholder="item.placeholder !== undefined ? item.placeholder : '请选择'"
                :getPopupContainer="triggerNode => triggerNode.parentNode"
                :treeCheckable="item.treeCheckable !== undefined ? item.treeCheckable : false"
                :treeDefaultExpandAll="item.treeDefaultExpandAll !== undefined ? item.treeDefaultExpandAll : false"
                :showSearch="item.showSearch !== undefined ? item.showSearch : false"
                :treeNodeFilterProp="item.treeNodeFilterProp !== undefined ? item.treeNodeFilterProp : 'value'"
                :allowClear="item.allowClear !== undefined ? item.allowClear : undefined"
                :onSelect="item.onSelect !== undefined ? item.onSelect : null"
                :fieldNames="item.fieldNames
                "
                @change="
                  (value, label, extra) => treeSelectChange(value, label, extra, index, item)
                "
                :dropdownStyle="{ maxHeight: '300px' }"
                :style="{
                  width: item.right_width
                    ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                    : '',
                }"
              />
              <TreeSelect
                v-model:value="curFormData[item.key]"
                :treeData="item.data !== undefined ? item.data : []"
                v-else
                :placeholder="item.placeholder !== undefined ? item.placeholder : '请选择'"
                :getPopupContainer="triggerNode => triggerNode.parentNode"
                :treeCheckable="item.treeCheckable !== undefined ? item.treeCheckable : false"
                :treeDefaultExpandAll="item.treeDefaultExpandAll !== undefined ? item.treeDefaultExpandAll : false"
                :showSearch="item.showSearch !== undefined ? item.showSearch : false"
                :treeNodeFilterProp="item.treeNodeFilterProp !== undefined ? item.treeNodeFilterProp : 'value'"
                :allowClear="item.allowClear !== undefined ? item.allowClear : undefined"
                :onSelect="item.onSelect !== undefined ? item.onSelect : null"
                @change="
                  (value, label, extra) => treeSelectChange(value, label, extra, index, item)
                "
                :dropdownStyle="{ maxHeight: '300px' }"
                :style="{
                  width: item.right_width
                    ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                    : '',
                }"
              />
            </FormItem>
            <div
              v-if="item.desc"
              class="common_item_right_desc"
              :style="{
                width: item.desc_width
                  ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                  : '300px',
              }"
              >{{ item.desc }}</div
            >
          </div>
        </div>
        <div v-else-if="item.type == 'set_level_price'&&!item.hidden" class="common_item">
          <div
            class="common_item_left"
            style="position: absolute; top: 0; bottom: 0; height: auto"
            :style="{
               width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
              minWidth:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            style="height: auto; padding: 15px 20px"
            :style="{
              marginLeft:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
            }"
          >
            <div class="spec_wrap" style="padding: 10px 0">
              <div v-if="item.data.length > 0" style="margin-bottom: 10px">
                <span class="add_price_title">批发数量</span>
                <span class="add_price_title">阶梯价</span>
              </div>
              <template v-for="(level_item, lavel_index) in item.data" :key="lavel_index">
                <div class="flex_row_start_start" style="margin-bottom: 8px">
                  <FormItem
                    :name="['sld_form', index, 'data', lavel_index, 'level_num']"
                    style="width: 230px"
                  >
                    <div class="flex_row_start_center">
                      <span class="add_price_item_pre">≥</span>
                      <InputNumber
                        class="item"
                        :placeholder="item.placeholder"
                        :min="item.min != undefined ? item.min : 0"
                        :max="item.max != undefined ? item.max : 9999999"
                        :step="item.step ? item.step : 1"
                        :precision="0"
                        :disabled="lavel_index == 0 ? true : item.disable"
                        :value="level_item.level_num != undefined ? level_item.level_num : ''"
                        @change="(e) => handleLevelOnchange(e, item, 'level_num', lavel_index)"
                        @blur="handleLevelBlur(item, 'level_num')" />
                      </div>
                  </FormItem>
                  <FormItem
                    :name="['sld_form', index, 'data', lavel_index, 'level_price']"
                    style="width: 230px;"
                  >
                    <div class="flex_row_start_center">
                      <span class="add_price_item_pre">￥</span>
                      <InputNumber
                        class="item"
                        :placeholder="item.placeholder"
                        :min="item.min != undefined ? item.min : 0.01"
                        :max="item.max != undefined ? item.max : 9999999"
                        :step="item.step ? item.step : 1"
                        :precision="item.precision != undefined ? item.precision : 2"
                        :disabled="item.disable"
                        :value="level_item.level_price != undefined ? level_item.level_price : ''"
                        @change="(e) => handleLevelOnchange(e, item, 'level_price', lavel_index)"
                        @blur="handleLevelBlur(item, 'level_price')" />
                    </div>
                  </FormItem>
                  <div v-if="lavel_index > 0" style="margin-left: 10px; padding-top: 8px" @click="handleLevelDelete(item, level_item.level_key)">
                    <AliSvgIcon
                      fillColor="#e20e0e" width="15px" height="15px" iconName="iconshanchu7" style="cursor: pointer" />
                  </div>
                </div>
              </template>
              <div class="add_price_btn" @click="handleLevelAdd(item)">新增区间</div>
            </div>
          </div>
        </div>
        <div v-else-if="item.type == 'show_logistics'" class="common_item goods_freight">
          <div
            class="common_item_left"
            :style="{
               width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
              minWidth:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            :style="{
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
          >
            <div class="flex_row_start_center freight_detail">
              <div class="title">{{ item.value.typeValue ? item.value.typeValue : '--' }}</div>
              <div v-if="item.value.type == 3" class="flex_row_center_center more">
                <img class="more_img" src="@/assets/images/arrow_right.png" />
                <div class="more_modal">
                  <template v-for="(items,indexs) in item.value.cityInfoList" :key="indexs">
                    <div class="flex_row_between_center more_modal_list">
                      <div class="flex_row_start_start more_modal_city">
                        <template v-for="(itemss,indexss) in items.cityList" :key="indexss">
                          <div class="more_modal_city_item">{{ itemss.cityName }}</div>
                        </template>
                      </div>
                      <div class="more_modal_desc">{{ items.description }}</div>
                    </div>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Form>
  </div>
</template>
<script lang="ts" setup>
  import { failTip } from '@/utils/utils';
import { DeleteOutlined, EyeOutlined, LoadingOutlined, PlusOutlined } from '@ant-design/icons-vue';
import {
Cascader,
Checkbox,
CheckboxGroup,
Form,
FormItem,
Input,
InputNumber,
Radio,
RadioButton,
RadioGroup,
Select,
SelectOption,
TreeSelect,
Upload
} from 'ant-design-vue';
import SldUEditor from '/@/components/SldUEditor/index.vue';
import { computed, onMounted, ref, watch } from 'vue';
import { VueDraggable } from 'vue-draggable-plus';
import { useUserStore } from '/@/store/modules/user';
import { getToken } from '/@/utils/auth';
import { addGoodsSpecValLimit } from '/@/utils/utils';
import { validatorEmoji } from '/@/utils/validate';
  const { SHOW_PARENT } = TreeSelect;

  const emit = defineEmits([
    'callbackEvent',
    'validaEvent',
    'categorySelectEvent',
    'materialEvent',
    'materialDelete',
    'goodsSpecEvent',
    'changeUpload',
    'reserveInfoEvent',
    'treeSelectChangeEvent',
    'levelChange',
    'levelBlur',
    'levelDelete',
    'levelAdd',
  ]);
  const rowForm: any = ref({});
  const curFormData = ref({sld_form:[]});
  const imgToken = ref('Bearer '+getToken() || ''); //上传文件token参数

  const userStore = useUserStore();

  const props: any = defineProps({
    width: { type: String, require: false, default: '100%' }, //列表宽度
    left_width: { type: [String, Number], reqiore: false }, //列表label宽度
    data: { type: Array, required: true, default: () => [] }, //表单数据
    valida: { type: Boolean, required: false, default: false }, //是否提交进行校验
    hideBorder: { type: String, default: '' }, //是否隐藏部分border bottom/right
  });

  onMounted(() => {
    props.data.map((item) => {
      if (item.key) {
        curFormData.value[item.key] =
          item.type == 'upload_img'
            ? item.fileList && item.fileList.length > 0
              ? item.fileList[0]
              : {}
            : item.value;
      }
    });
  });

  const formData: any = computed(() => {
    return props.data;
  });

  const curValida: any = computed(() => {
    return props.valida;
  });

  watch(
    formData,
    () => {
     imgToken.value = 'Bearer ' + userStore.getToken
      props.data.map((item) => {
        if (item.key) {
          curFormData.value[item.key] =
            item.type == 'upload_img'
              ? item.fileList && item.fileList.length > 0
                ? item.fileList[0]
                : {}
              : item.value;
        }
      });
      curFormData.value.sld_form = JSON.parse(JSON.stringify(formData.value));
    },
    { deep: true },
  );

  watch(
    curValida,
    () => {
     imgToken.value = 'Bearer ' + userStore.getToken
      if (props.valida) {
        rowForm.value
          .validate()
          .then(() => {
            emit('validaEvent', true);
          })
          .catch(() => {
            emit('validaEvent', false);
          });
      }
    },
    { deep: true },
  );

  function handleChange(item, val, extra) {
    if (item.callback) {
      emit('callbackEvent', {
        contentItem: { ...item, eventType: 'change' },
        val,
        extra,
      });
    }
  }

  const beforeUploadClick = async()=> {
    imgToken.value = 'Bearer ' + userStore.getToken
    if(userStore.getToken){
      let res = await userStore.getSldCommonService()
      if(res.state == 200){
        imgToken.value = 'Bearer ' + res.token
      }
    }
  }

  function handleChangeUpload(item, file, fileList, event) {
    emit('changeUpload', {
      contentItem: item,
      file,
      fileList,
      event
    });
  }

  function handlePreview(item, file) {
    window.open(file.url ? file.url : file.thumbUrl);
  }

  //文件上传前数据校验
  function beforeUpload(file, fileList, accept, limit = 20) {
    //校验文件格式类型
    if (accept != undefined && accept != null && accept) {
      let accept_list = accept
        .replaceAll(' ', '')
        .split(',')
        .filter((item) => item && item.trim());
      let file_type = file.name ? '.' + file.name.split('.').at(-1) : '';
      if (accept_list.length > 0 && file_type != '' && accept_list.indexOf(file_type.toLowerCase()) == -1) {
        console.warn('[beforeUpload]:',accept_list,file_type,file,fileList)
        failTip('上传文件格式有误:'+file_type);
        return false;
      }
    }
    //校验文件大小
    if (file.size != undefined && file.size > 1024 * 1024 * limit) {
      failTip(`上传文件过大，请上传小于${limit}M的图片`);
      return false;
    }
  }

  function resetGoodsCategory(item) {
    if (item.callback) {
      emit('categorySelectEvent');
    }
  }

  function handleMaterial(item, index) {
    if (item.callback) {
      emit('materialEvent', { ...item, index });
    }
  }

  function deleteMaterial(item, index) {
    if (item.callback) {
      emit('materialDelete');
    }
  }

  function handleSpec(type, val, index, indexs) {
    emit('goodsSpecEvent', type, val, index, indexs);
  }

  //用户预留信息回调事件
  function handleReserveInfo(type, item, key, index, val, ids) {
    if (item.reserveInfoBack) {
      emit('reserveInfoEvent', { type, key, index, val });
      if (key == 'reserveName' || key == 'isRequired'  || type == 'del') {
        if (ids) {
          curFormData.value[ids[0]][ids[1]][ids[2]][ids[3]][ids[4]] = val;
          rowForm.value.resetFields(ids);
        }
      }
    }
  }
  
  //TreeSelect树选择事件
  function treeSelectChange(val, label, extra, index, item) {
    if (item.callback) {
      emit('treeSelectChangeEvent', val, label, extra, index, item);
    }
  };

   // 处理阶梯价input内容变化事件
   function handleLevelOnchange(e, item, type, index) {
    if (item.callback) {
      emit('levelChange', e, type, index);
    }
  };

  // 处理阶梯价input失焦事件
  function handleLevelBlur(item, level_key) {
    if (item.callback) {
      emit('levelBlur', level_key);
    }
  };

  function handleLevelDelete(item, level_key) {
    if (item.callback) {
      emit('levelDelete', level_key);
    }
  };

  function handleLevelAdd(item) {
    if (item.callback) {
      emit('levelAdd');
    }
  };

  const deleteImg = (item,it)=> {
    item.fileList = item.fileList.filter(ite=>it.uid!=ite.uid)
    setTimeout(()=>{
    emit('changeUpload', {
      contentItem: item,
      file:{
       file:it,
       fileList:[...item.fileList],
      },
      fileList:undefined,
    });
    })
  }

</script>
<style lang="less">
  .standard_form_row {
    display: flex;
    flex-wrap: wrap;
    padding: 0.5rem;
    background: #fff;

    .ant-form {
      display: flex;
      flex-wrap: wrap;
      align-items: flex-start;
      justify-content: flex-start;
      width: 100%;

      .ant-form-item {
        margin-bottom: 0;
      }

      .ant-input-affix-wrapper,
      .ant-select-selector,
      .ant-input-number {
        border-color: #eee;
      }
    }

    .ant-form-item-control {
      .ant-form-item-explain {
        position: absolute;
        z-index: 2;
        top: -11px;
        right: 12%;
        min-height: 20px;
        padding: 0 5px;
        background: #fff;
        font-size: 13px;
        text-align: right;
      }
    }

    .common_title {
      width: 100%;
      height: auto;
      line-height: 40px;
      padding: 0 10px;
      background-color: #fafafa;
      border-radius: 4px;

      .common_title_label {
        font-weight: 500;
        color: #323233;
        
        .common_item_require {
          color: #e20e0e;
          margin-right: 4px;
        }
      }

      .common_title_desc {
        line-height: 1.5;
        padding: 0 0 8px 0;
      }
    }

    .common_item {
      display: flex;
      position: relative;
      flex-shrink: 0;
      justify-content: flex-start;
      font-size: 13px;

      .common_item_left {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        width: 200px;
        min-width: 200px;
        padding: 12px 15px;
        border: 1px solid #F2F2F2;
        background: #F9F9F9;
        color: #333;
        font-size: 13px;
        font-weight: 400;
        text-align: right;

        .common_item_require {
          color: red;
        }
      }

      .common_item_right {
        display: flex;
        flex: 1;
        flex-direction: column;
        flex-shrink: 0;
        align-items: flex-start;
        justify-content: center;
        min-height: 72px;
        padding: 10px 0;
        padding-right: 20px;
        padding-left: 20px;
        border: 1px solid #f0f0f0;
        border-left: none;

        .common_item_right_desc {
          margin-top: 3px;
          color: rgb(0 0 0 / 45%);
          font-size: 12px;
          line-height: 14px;
        }

        .goods_category_label {
          .goods_category_label_content {
            color: #999;
            font-size: 12px;
            line-height: 32px;
          }

          .goods_category_label_btn {
            height: 32px;
            margin-left: 10px;
            padding: 0 10px;
            transition: all 0.3s ease;
            border-radius: 4px;
            border: 1px solid @primary-color;
            color: @primary-color;
            font-weight: 400;
            line-height: 30px;
            cursor: pointer;
          }
        }

        .material_video_show {
          position: relative;
          margin-top: 12px;

          .material_video_del {
            position: absolute;
            z-index: 99;
            top: -11px;
            left: 93px;
            cursor: pointer;
          }
        }

        .reserve_info {
          .reserve_info_xing {
            width: 11px;
            color: #f5222d;
            font-size: 14px;
          }

          .svgIcon {
            margin-top: 1px;
            margin-left: 4px;
            cursor: pointer;
          }
        }

        .reserve_info_btn {
          width: 95px;
          margin-right: 20px;
          margin-left: 10px;
          border: 1px solid #FC701E;
          border-radius: 2px;
          color: #FC701E;
          font-size: 13px;
          line-height: 30px;
          text-align: center;
          cursor: pointer;
        }
      }
    }

    .upload_image {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 90px;
      height: 90px;

      span {
        margin-top: 8px;
        color: rgb(102 102 102);
        font-size: 13px;
      }
    }

    .upload_file {
      color: @primary-color;
      font-size: 13px;
      cursor: pointer;
    }

    .goods_spec {
      position: relative;
      width: 100%;
      height: 45px;
      padding: 15px 0;

      .goods_spec_label {
        margin-left: 20px;
        color: #333;
        font-size: 14px;
        font-weight: bold;
      }

      .goods_spec_label_delbtn {
        display: none;
        position: relative;
        top: 8.5px;
        left: 10px;
        transform: translateY(-50%);
        cursor: pointer;
      }

      &:hover {
        .goods_spec_label_delbtn {
          display: block;
        }
      }
    }

    .goods_spec_value {
      width: 100%;
      min-height: 45px;
      padding: 15px 0;
      border-bottom: 1px dashed #dcdcdc;

      .goods_spec_value_label {
        flex-shrink: 0;
        line-height: 32px;
        margin-left: 20px;
        margin-bottom: 10px;
        color: #333;
        font-size: 14px;
        font-weight: bold;
      }

      .goods_spec_value_list {
        flex-wrap: wrap;
      }

      .goods_spec_value_select {
        position: relative;
        margin-bottom: 10px;

        .goods_spec_value_select_delbtn {
          display: none;
          position: absolute;
          z-index: 9;
          top: -8px;
          right: -8px;
          cursor: pointer;
        }

        .ant-select-show-search.ant-select:not(.ant-select-customize-input)
          .ant-select-selector
          input {
          cursor: pointer;
        }

        &:hover {
          .goods_spec_value_select_delbtn {
            display: block;
          }
        }
      }

      .goods_spec_value_btn {
        flex-shrink: 0;
        height: 32px;
        line-height: 32px;
        margin-left: 20px;
        margin-bottom: 10px;
        color: #ff711e;
        font-size: 14px;
        font-weight: bold;
        cursor: pointer;
      }
    }

    .goods_spec_btn {
      position: relative;
      width: 100%;
      height: 45px;

      .goods_spec_btn_add {
        width: 120px;
        height: 36px;
        margin-top: 5px;
        margin-left: 5px;
        border: 1px solid #ff711e;
        border-radius: 3px;
        background: #fff;
        color: #ff711e;
        font-size: 14px;
        font-weight: bold;
        line-height: 34px;
        text-align: center;
        cursor: pointer;
      }
    }

    &.hide_bottom {
      .common_list {
        .common_item {
          .common_item_left,
          .common_item_right {
            border-bottom: 0;
          }
        }
        &:last-of-type {
          .common_item {
            .common_item_left,
            .common_item_right {
              border-bottom: 1px solid #f0f0f0;
            }
          }
        }
      }
    }

    &.hide_right {
      .common_list {
        &:nth-child(2n+1) {
          .common_item {
            .common_item_left {
              border-left: 0;
              
            }
          }
        }
      }
    }

    .spec_wrap{
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
    }

    .add_price_title {
      display: inline-block;
      width: 194px;
      text-indent: 80px;
      margin-left: 5px;
      margin-right: 56px;
    }
    .add_price_item_pre {
      margin-left: 5px;
      margin-right: 10px;
    }
    .add_price_btn {
      width: 120px;
      height: 36px;
      line-height: 36px;
      color: @primary-color;
      font-size: 14px;
      font-weight: bold;
      text-align: center;
      background: #fff;
      border: 1px solid @primary-color;
      border-radius: 3px;
      margin-top: 5px;
      margin-left: 5px;
      cursor: pointer;
    }
  }

    .goods_freight {
    .freight_detail {
      .title {
        color: rgba(0, 0, 0, 0.65);
        line-height: 72px;
        font-size: 13px;
      }
      .more {
        position: relative;

        :global {
          ::-webkit-scrollbar {
            width: 5px;
            height: 10px;
            border-radius: 10px;
          }
          ::-webkit-scrollbar-thumb {
            display: block;
            width: 100%;
            cursor: pointer;
            border-radius: inherit;
            background-color: rgba(0, 0, 0, 0.2);
            transform: translateY(0px);
          }
          ::-webkit-scrollbar-track {
            border-radius: 10px;
          }
        }
        
        &:hover {
          .more_modal {
            display: block;
          }
        }
        .more_img {
          width: 25px;
          height: 25px;
          margin-left: 10px;
          padding: 5px;
          cursor: pointer;
        }
        .more_modal {
          display: none;
          position: absolute;
          top: 26px;
          left: 10px;
          z-index: 99999;
          width: 700px;
          max-height: 400px;
          overflow-x: auto;
          background: #FFFFFF;
          box-shadow: 0 0 9px 1px rgba(157, 157, 157, .1);

          .more_modal_list {
            border-top: 1px solid #DFDFDF;
            padding: 16px 15px;
            &:nth-child(1) {
              border-top: none;
            }
            .more_modal_city {
              flex: 1;
              flex-wrap: wrap;
              color: #666666;
              font-size: 12px;
              font-family: Microsoft YaHei;
              font-weight: 400;
              padding-right: 10px;
              .more_modal_city_item {
                margin: 2px 8px 2px 0;
                padding: 3px;
                border-radius: 3px;
                cursor: pointer;
                &:hover {
                  color: #ffffff;
                  background: @primary-color;
                }
              }
            }
            .more_modal_desc {
              position: relative;
              flex-shrink: 0;
              width: 144px;
              line-height: 20px;
              color: #666666;
              font-size: 12px;
              font-family: Microsoft YaHei;
              font-weight: 400;
              text-align: center;
              padding-left: 25px;
              padding-right: 10px;
              &:before {
                display: block;
                content: '';
                position: absolute;
                left: 0;
                top: 50%;
                margin-top: -10px;
                width: 1px;
                height: 20px;
                background: #DFDFDF;
              }
            }
          }
        }
      }
    }
  }
  .imgList_dr_box{
   .ant-upload-picture-card-wrapper{
    width: 104px;
    margin-top: 3px;
   }
  }

  .imgList_dr{
  
    .imgList {
      position: relative;
      margin-top: 5px;
    }

    .mask {
      position: absolute;
      top: 0;
      left: 0;
      width: 100px;
      height: 100px;
      margin-right: 10px;
      opacity: 0;
      background: rgb(101 101 101 / 60%);
      color: #fff;
    }

    .imgList a:hover .mask {
      opacity: 1;
    }

    .icon-delete {
      position: absolute;
      margin-top: 43px;
      margin-left: 53px;
      color: white;
      cursor: pointer;
    }

    .icon-preview {
      position: absolute;
      margin-top: 43px;
      margin-left: 30px;
      color: white;
      cursor: pointer;
    }

    .img {
      width: 100px;
      height: 100px;
      margin-right: 10px;
    }

    :deep(.ant-upload-list) {
      display: none;
    }
  }
</style>
