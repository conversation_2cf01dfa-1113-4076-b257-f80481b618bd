import type { App } from 'vue';
import { Button } from './Button';
import { Input, Layout } from 'ant-design-vue';

import SldComHeader from './SldComHeader/index.vue';
import SldModal from './SldModal/index.vue';
import PageWrapper from './PageWrapper/index.vue';

export function registerGlobComp(app: App) {
  app.use(Input).use(Button).use(Layout);
  app.component('SldComHeader', SldComHeader);
  app.component('SldModal', SldModal);
  app.component('PageWrapper', PageWrapper);
}
