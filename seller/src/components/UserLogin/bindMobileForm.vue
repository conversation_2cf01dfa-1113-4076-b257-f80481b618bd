<template>
  <div>
    <Modal :visible="visible" title="绑定手机号" @ok="handleOk" @cancel="handleCancel" class="bind_mobile_modal_class" :footer="null"
      :width="490" :afterClose="afterClose">
      <div class="h-20px"></div>
      <Form class="flex flex-col items-center justify-center" :model="formState" :rules="formRules" ref="formRef">
        <!-- 手机号 -->
        <FormItem class="!w-370px" name="vendorMobile">
          <Input v-model:value="formState.vendorMobile" class="!p-0 !pr-3" placeholder="请输入手机号" allowClear
            type="number" :maxlength="11" @change="clearValidate('vendorMobile')" @keydown="handleKeydown">
          <template #prefix>
            <div class="text-center text-[#333] text-[14px] bg-[#F8F8F8] w-90px leading-10 mr-2">
              <span class="text-[#ef1216] text-xs">*</span>
              <span>手机号</span>
            </div>
          </template>
          </Input>
        </FormItem>

        <!-- //验证码 -->
        <FormItem class="!w-370px" name="verifyCode">
          <Input v-model:value="formState.verifyCode" placeholder="请输入验证码" allowClear class="!p-0" :maxlength="4"
            @change="clearValidate('verifyCode')">
          <template #prefix>
            <div class="text-center text-[#333] text-[14px] bg-[#F8F8F8] w-90px leading-10 mr-2">
              <span class="text-[#ef1216] text-xs">*</span>
              <span>验证码</span>
            </div>
          </template>

          <template #suffix>
            <img :src="capchaImg" alt="" class="h-full" @click="getCapImage" />
          </template>
          </Input>
        </FormItem>

        <!-- 短信验证码 -->
        <FormItem class="!w-370px" name="smsCode">
          <Input v-model:value="formState.smsCode" class="!p-0" placeholder="请输入短信验证码" allowClear
            type="number" @change="clearValidate('smsCode')" @keydown="handleKeydown">
          <template #prefix>
            <div class="text-center text-[#333] text-[14px] bg-[#F8F8F8] w-90px leading-10 mr-2">
              <span class="text-[#ef1216] text-xs">*</span>
              <span>短信验证码</span>
            </div>
          </template>

          <template #suffix>
            <div
              class="text-xs leading-4 text-center transition ease-in-out border border-white cursor-pointer w-94px hover:text-opacity-50 border_color"
              @click="startSms">
              {{ countTime == 0 ? '获取验证码' : `${countTime}秒` }}
            </div>
          </template>
          </Input>
        </FormItem>
        <FormItem class="!w-370px">
          <Button class="!h-40px w-full" type="primary" @click="onSubmit(1)" :loading="isloading">确认绑定</Button>
        </FormItem>
      </Form>
    </Modal>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, onMounted } from 'vue';
import { Modal, Form, FormItem, Input, InputPassword, Button, message } from 'ant-design-vue';
import useCheck, { rulesCheckType } from '../../hooks/common/useCheck';
import type { RuleObject } from 'ant-design-vue/lib/form/interface';
import { bindMobileApi, getSmsCode } from '/@/api/sys/account';
import countDown from '/@/utils/countDown';
import { getCaptchaApi } from '@/api/sys/example';
import { useUserStore } from '/@/store/modules/user';

export default defineComponent({
  name: 'BindMobileForm',
  components: {
    Modal,
    Form,
    FormItem,
    Input,
    InputPassword,
    Button,
  },
  setup() {
    const visible = ref<boolean>(false);
    const u = ref('')
    const userStore = useUserStore();
    const checkUnite = useCheck();
    const formRef = ref();
    const capchaImg = ref('');
    const capchaKey = ref('');
    const isloading = ref<boolean>(false);
    const { startDown, countTime } = countDown();
    const formState = reactive({
      vendorMobile: '',
      smsCode: '',
      verifyCode: '',
    });

    const validatePass = async (rule: RuleObject, value: string, type: string) => {
      const checkMethod = checkUnite[type];
      const { checkState, stateValue } = checkMethod(value);
      if (checkState > 0) {
        return Promise.reject(stateValue);
      } else {
        return Promise.resolve();
      }
    };

    const formRules: rulesCheckType = {
      vendorMobile: [
        {
          required: true,
          trigger: 'blur',
          validator: (r, v) => validatePass(r, v, 'checkMobile'),
        },
      ],
      verifyCode: {
        required: true,
        trigger: 'blur',
        validator: (r, v) => validatePass(r, v, 'checkImgCode'),
      },
      smsCode: [
        {
          required: true,
          trigger: 'blur',
          validator: (r, v) => validatePass(r, v, 'checkSmsCode'),
        },
      ],
    };

    const handleOk = (): void => {
      visible.value = false;
    };
    const handleCancel = (): void => {
      countTime.value = 0
      visible.value = false;
      u.value = ''
    };
    const openModal = (e): void => {
      visible.value = true;
      u.value = e
    };

    const preventClick = ref<boolean>(false);
    const startSms = async () => {
      try {
        await formRef.value.validate(['vendorMobile', 'verifyCode']);
        if (preventClick.value) {
          return;
        }
        preventClick.value = true;
        let param = {
          type: 'bindMobile',
          mobile: formState.vendorMobile,
          verifyCode: formState.verifyCode,
          verifyKey: capchaKey.value,
        }
        const result: any = await getSmsCode(param);

        if (result?.state == 200) {
          message.success(result?.msg);
          startDown(() => {
            preventClick.value = false;
          });
        } else {
          preventClick.value = false;
          message.error(result?.msg);
        }
      } catch (error) {
        preventClick.value = false;
      }
    };

    const clearValidate = (name) => {
      if (name == 'vendorMobile') {
        formState.vendorMobile = formState.vendorMobile.toString().substring(0, 11);
      }

      if (name == 'smsCode') {
        formState.smsCode = formState.smsCode.toString().substring(0, 6);
      }

      formRef.value.clearValidate(name);
    };

    const getCapImage = async () => {
      const cap = await getCaptchaApi();
      if (cap?.['data']) {
        capchaImg.value = 'data:image/png;base64,' + cap?.['data']['captcha'];
        capchaKey.value = cap?.['data']['key'];
      }
    };

    const onSubmit = async (type): Promise<void> => {
      isloading.value = true;
      try {
        await formRef.value.validate();
        const param = {
          mobile: formState.vendorMobile,
          resource:'1',
          smsCode: formState.smsCode,
          bindType:type,
          u:u.value,
          verifyCode: formState.verifyCode,
          verifyKey: capchaKey.value,
        };
        const res: any = await bindMobileApi(param);
        if (res?.state == 200) {
          Modal.destroyAll();
          visible.value = false;
          formRef.value.resetFields();
          let cur_top_nav = []; //顶部菜单
          let cur_top_nav_info = []; //顶部菜单详细信息
          if (res.data.resourceList.length > 0) {
            localStorage.setItem('sld_menu_data', JSON.stringify(res.data.resourceList));
            let sld_all_routes = []; //所有页面的路由
            res.data.resourceList.map((item) => {
              item.children.map((child) => {
                sld_all_routes.push(child.frontPath);
              });
            });
            localStorage.setItem('sld_all_routes', JSON.stringify(sld_all_routes));
            let tmp_data = res.data.resourceList;
            for (let i in tmp_data) {
              let split_first = tmp_data[i].frontPath.split('/');
              let target = split_first[1];
              if (cur_top_nav.indexOf(target) == -1) {
                let target_data = {};
                target_data.top_nav = target;
                target_data.path = tmp_data[i].children[0].frontPath;
                if (target == 'basic') {
                  target_data.name = '概况';
                  target_data.left_icon = new URL('/src/assets/images/nav/basic.png', import.meta.url).href;
                } else if (target == 'goods') {
                  target_data.name = '商品';
                  target_data.left_icon = new URL('/src/assets/images/nav/goods.png', import.meta.url).href;
                } else if (target == 'order') {
                  target_data.name = '订单';
                  target_data.left_icon = new URL('/src/assets/images/nav/order.png', import.meta.url).href;
                } else if (target == 'store') {
                  target_data.name = '店铺';
                  target_data.left_icon = new URL('/src/assets/images/nav/store.png', import.meta.url).href;
                } else if (target == 'bill') {
                  target_data.name = '结算';
                  target_data.left_icon = new URL('/src/assets/images/nav/bill.png', import.meta.url).href;
                } else if (target == 'point') {
                  target_data.name = '积分商城';
                  target_data.left_icon = new URL('/src/assets/images/nav/point.png', import.meta.url).href;
                } else if (target == 'im') {
                  target_data.name = '客服';
                  target_data.left_icon = new URL('/src/assets/images/nav/point.png', import.meta.url).href;
                } else if (target == 'spreader') {
                  target_data.name = '推手';
                  target_data.left_icon = new URL('/src/assets/images/nav/spreader.png', import.meta.url).href;
                } else if (target == 'statistics') {
                  target_data.name = '统计';
                  target_data.left_icon = new URL('/src/assets/images/nav/statistics.png', import.meta.url).href;
                } else if (target == 'marketing') {
                  target_data.name = '应用';
                  target_data.left_icon = new URL('/src/assets/images/nav/marketing.png', import.meta.url).href;
                }
                if (target_data.name) {
                  cur_top_nav.push(target);
                  cur_top_nav_info.push(target_data);
                }
              }
            }
            localStorage.setItem('cur_top_nav', JSON.stringify(cur_top_nav));
            localStorage.setItem('cur_top_nav_info', JSON.stringify(cur_top_nav_info));
            setTimeout(() => {
              loginUpdateInfo(res.data);
            });
          }
        } else if (res.state == 267) {
          Modal.confirm({
            title: '温馨提示',
            content: '该手机号已被其他账号关联占用，如确认进行本次操作，本手机号将与原关联账号进行解绑，以保证您的账号安全',
            onOk() {
              onSubmit(2)
            },
            onCancel() {
              Modal.destroyAll();
              isloading.value = false;
            },
            class: 'test',
          });
        } else {
          isloading.value = false;
          message.error(res?.msg);
        }
      } catch (error) {
        isloading.value = false;
        console.info('error',error);
      } finally {
        
      }
    };

    const loginUpdateInfo = (data) => {
      userStore.loginUpdateInfo(data);
    };


    const handleKeydown = (e) => {
      let prohibitInput = ['-', 'e', '+', 'E', '.'];
      if (prohibitInput.includes(e.key)) {
        e.preventDefault();
      }
    };

    const afterClose = () => {
      formRef.value.resetFields();
    };

    onMounted(() => {
      getCapImage()
    })

    return {
      afterClose,
      visible,
      handleOk,
      openModal,
      formState,
      handleCancel,
      formRules,
      formRef,
      onSubmit,
      clearValidate,
      isloading,
      countTime,
      startSms,
      handleKeydown,
      capchaImg,
      capchaKey,
      getCapImage,
      userStore
    };
  },
});
</script>

<style lang="less">
.bind_mobile_modal_class {
  .ant-input-prefix{
    background: rgb(248, 248, 248);
  }
  .ant-modal-header {
    background-color: @primary-color;

    .ant-modal-title {
      color: #fff;
    }
  }

  .border_color {
    border-left-color: @primary-color;
    color: @primary-color;
  }

  .items-center {
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
    }
    input[type="number"]{
        -moz-appearance: textfield;
    }
  }
}

</style>
