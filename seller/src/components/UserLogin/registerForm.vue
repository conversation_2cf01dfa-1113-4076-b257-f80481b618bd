<template>
  <div>
    <Modal :visible="visible" title="商家账号注册" @ok="handleOk" @cancel="handleCancel" class="register_modal_class"
      :footer="null" :width="490" :afterClose="afterClose">
      <div class="h-20px"></div>

      <Form class="flex flex-col items-center justify-center " ref="formRef" :rules="rules" :model="formState">
        <!-- 手机号 -->
        <FormItem class="!w-370px" name="vendorName">
          <Input v-model:value="formState.vendorName" placeholder="请输入6~20位数字字母组合" allowClear :maxlength="20"
            class="!p-0 !pr-3" type="text" @change="clearValidate('vendorName')" @keydown="handleKeydown">
          <template #prefix>
            <div class="text-center text-[#333] text-[14px] bg-[#F8F8F8] w-90px leading-10 mr-2">
              <span class="text-[#ef1216] text-xs">*</span>
              <span>商户账号</span>
            </div>
          </template>
          </Input>
        </FormItem>
        <!-- 手机号 -->
        <FormItem class="!w-370px" name="vendorMobile">
          <Input v-model:value="formState.vendorMobile" placeholder="请输入手机号" allowClear :maxlength="11" class="!p-0 !pr-3"
            @change="clearValidate('vendorMobile')" @keydown="handleKeydown">
          <template #prefix>
            <div class="text-center text-[#333] text-[14px] bg-[#F8F8F8] w-90px leading-10 mr-2">
              <span class="text-[#ef1216] text-xs">*</span>
              <span>手机号</span>
            </div>
          </template>
          </Input>
        </FormItem>
        <!-- //验证码 -->
        <FormItem class="!w-370px" name="verifyCode">
          <Input v-model:value="formState.verifyCode" placeholder="请输入验证码" allowClear class="!p-0" :maxlength="4"
            @change="clearValidate('verifyCode')">
          <template #prefix>
            <div class="text-center text-[#333] text-[14px] bg-[#F8F8F8] w-90px leading-10 mr-2">
              <span class="text-[#ef1216] text-xs">*</span>
              <span>验证码</span>
            </div>
          </template>

          <template #suffix>
            <img :src="capchaImg" alt="" class="h-full" @click="getCapImage" />
          </template>
          </Input>
        </FormItem>

        <!-- 短信验证码 -->
        <FormItem class="!w-370px" name="smsCode">
          <Input v-model:value="formState.smsCode" placeholder="请输入短信验证码" allowClear class="!p-0" type="number"
            @change="clearValidate('smsCode')" @keydown="handleKeydown">
          <template #prefix>
            <div class="text-center text-[#333] text-[14px] bg-[#F8F8F8] w-90px leading-10 mr-2">
              <span class="text-[#ef1216] text-xs">*</span>
              <span>短信验证码</span>
            </div>
          </template>

          <template #suffix>
            <div
              class="text-xs leading-4 text-center transition ease-in-out border border-white cursor-pointer w-94px hover:text-opacity-50 border_color"
              @click="startSms">
              {{ countTime == 0 ? '获取验证码' : `${countTime}秒` }}
            </div>
          </template>
          </Input>
        </FormItem>

        <!-- 邮箱 -->
        <FormItem class="!w-370px" name="vendorEmail">
          <Input v-model:value="formState.vendorEmail" placeholder="请输入邮箱" allowClear class="!p-0 !pr-3" type="email"
            :maxlength="50" @change="clearValidate('vendorEmail')">
          <template #prefix>
            <div class="text-center text-[#333] text-[14px] bg-[#F8F8F8] w-90px leading-10 mr-2">
              <span>邮箱</span>
            </div>
          </template>
          </Input>
        </FormItem>

        <!-- 新密码 -->
        <FormItem class="!w-370px" name="vendorPassword">
          <InputPassword v-model:value="formState.vendorPassword" placeholder="请设置6～20位字母、数字或符号组成的密码" allowClear
            class="!p-0 !pr-3 border-none placeholder:text-xs" :maxlength="20" @change="clearValidate('vendorPassword')"
            title="请设置6～20位字母、数字或符号组成的密码">
            <template #prefix>
              <div class="text-center text-[#333] text-[14px] bg-[#F8F8F8] w-90px leading-10 mr-2">
                <span class="text-[#ef1216] text-xs">*</span>
                <span>设置密码</span>
              </div>
            </template>
          </InputPassword>
        </FormItem>
        <!-- 再次输入密码 -->
        <FormItem class="!w-370px" name="confirmPassword">
          <InputPassword v-model:value="formState.confirmPassword" placeholder="请再次输入密码" allowClear class="!p-0 !pr-3"
            :maxlength="20" @change="clearValidate('confirmPassword')">
            <template #prefix>
              <div class="text-center text-[#333] text-[14px] bg-[#F8F8F8] w-90px leading-10 mr-2">
                <span class="text-[#ef1216] text-xs">*</span>
                <span>确认密码</span>
              </div>
            </template>
          </InputPassword>
        </FormItem>
        <FormItem class="!w-370px">
          <Button htmlType="submit" class="!h-40px w-full" type="primary" @click="onSubmit"
            :loading="isLoading">提交注册</Button>
        </FormItem>
      </Form>
    </Modal>

    <Modal :visible="visibleSuccess" title="商家账号注册" @ok="handleOk" @cancel="handlesCancel" class="register_modal_class"
      :footer="null" :width="450">
      <div class="mt-30px">
        <div class="flex flex-col items-center justify-center">
          <img :src="vendor_login_head_portrait" alt="" width="80" height="80" class="mb-2.5" />
          <p class="text-sm mt-2.5">注册成功</p>
          <Button @click="handleSuccessBtn" class="mt-4 mb-8 text-base text-white w-350px !h-40px"
            type="primary">点击入驻</Button>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script lang="ts">
import { getSmsCode, registerPost, } from '@/api/sys/account';
import { getCaptchaApi } from '@/api/sys/example';
import { Button, Form, FormItem, Input, InputPassword, Modal, message } from 'ant-design-vue';
import type { RuleObject } from 'ant-design-vue/lib/form/interface';
import { defineComponent, reactive, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import useCheck, { rulesCheckType } from '../../hooks/common/useCheck';
import {
SLD_ACCESS_TOKEN_KEY,
SLD_IS_STORE_ADMIN_KEY,
SLD_LAST_GET_TOKEN_TIME_KEY,
SLD_MENU_LIST_KEY,
SLD_REFRESH_TOKEN_KEY,
SLD_STORE_INFO_KEY,
SLD_VENDOR_INFO_KEY,
} from '/@/enums/cacheEnum';
import { useUserStore } from '/@/store/modules/user';
import { getImagePath } from '/@/utils';
import { setAuthCache } from '/@/utils/auth';
import countDown from '/@/utils/countDown';

const userStore = useUserStore();

export default defineComponent({
  name: 'RegisterForm',
  components: {
    Modal,
    Form,
    FormItem,
    Input,
    InputPassword,
    Button,
  },
  props: {
    enable: String,
  },
  setup(props,) {
    const router = useRouter();
    const vendor_login_head_portrait = getImagePath('images/<EMAIL>');
    const visible = ref<boolean>(false);
    const capchaImg = ref('');
    const capchaKey = ref('');
    const checkUnite = useCheck();
    const formRef = ref();
    const isLoading = ref<boolean>(false);
    const { startDown, countTime } = countDown();
    const handleOk = (): void => {
      visible.value = false;
    };
    const handleCancel = (): void => {
      visible.value = false;
    };
    const openModal = (): void => {
      visible.value = true;
    };

    const visibleSuccess = ref(false);

    const formState = reactive({
      vendorMobile: '',
      confirmPassword: '',
      verifyCode: '',
      smsCode: '',
      vendorPassword: '',
      vendorEmail: '',
      vendorName: ''
    });

    const validatePass = async (rule: RuleObject, value: string, type: string) => {
      const checkMethod = checkUnite[type];
      const { checkState, stateValue } = checkMethod(value);
      if (checkState > 0) {
        return Promise.reject(stateValue);
      } else {
        return Promise.resolve();
      }
    };

    const checkConfirmPass = (rules, value) => {
      if (value !== formState.vendorPassword) {
        return Promise.reject('两次密码输入不一致');
      } else {
        return Promise.resolve();
      }
    };

    const checkVendorName = (rules, value) => {
      if (!value) {
        return Promise.reject('请输入商家账号');
      } else if (value.length < 6 || value.length > 20) {
        return Promise.reject('请输入6-20位数字与字母组合');
      } else if (!(/^[0-9a-zA-Z]+$/.test(value))) {
        return Promise.reject('商家账号只能输入数字和字母');
      } else if (/^[0-9]+$/.test(value)) {
        return Promise.reject('商家账号不能输入纯数字');
      } else if (/^[a-zA-Z]+$/.test(value)) {
        return Promise.reject('商家账号不能输入纯字母');
      } else {
        return Promise.resolve();
      };
    }

    const rules: rulesCheckType = {
      vendorMobile: {
        required: true,
        trigger: 'blur',
        validator: (r, v) => validatePass(r, v, 'checkMobile'),
      },
      verifyCode: {
        required: true,
        trigger: 'blur',
        validator: (r, v) => validatePass(r, v, 'checkImgCode'),
      },
      smsCode: {
        required: true,
        trigger: 'blur',
        validator: (r, v) => validatePass(r, v, 'checkSmsCode'),
      },
      vendorEmail: {
        trigger: 'blur',
        validator: (r, v) => validatePass(r, v, 'checkEmail'),
      },
      vendorPassword: {
        required: true,
        trigger: 'blur',
        validator: (r, v) => validatePass(r, v, 'checkPasswordNewOld'),
      },
      confirmPassword: {
        required: true,
        trigger: 'blur',
        validator: checkConfirmPass,
      },
      vendorName: {
        required: true,
        trigger: 'blur',
        validator: checkVendorName,
      },
    };

    const preventClick = ref<boolean>(false);
    const startSms = async (): Promise<void> => {
      try {
        console.info('startSms...', props.enable, formState.vendorMobile, formState.verifyCode)
        await formRef.value.validate(['vendorMobile', 'verifyCode']);
        if (preventClick.value) {
          return;
        }
        preventClick.value = true;
        if (!props.enable || props.enable == '0') {
          // 无需验证
          preventClick.value = false;
          return

        }
        let result = null;
        result = await getSmsCode({
          type: 'register',
          mobile: formState.vendorMobile,
          verifyCode: formState.verifyCode,
          verifyKey: capchaKey.value,
        });

        if (result?.state == 200) {
          message.success(result?.msg);
          startDown(() => {
            preventClick.value = false;
          });
        } else {
          preventClick.value = false;
          message.error(result?.msg);
        }
      } catch (error) {

      }
    };

    const getCapImage = async () => {
      const cap = await getCaptchaApi();
      if (cap?.['data']) {
        capchaImg.value = 'data:image/png;base64,' + cap?.['data']['captcha'];
        capchaKey.value = cap?.['data']['key'];
      }
    };

    const clearValidate = (name) => {
      if (name == 'smsCode') {
        formState.smsCode = formState.smsCode.toString().substring(0, 6);
      }

      formRef.value.clearValidate(name);
    };

    const handleLoginData = (res) => {
      if (res.state == 267) {
        let cur_top_nav = ['apply'];//顶部菜单
        let cur_top_nav_info = [{
          top_nav: 'apply',
          name: '商户入驻',
          left_icon: new URL('/src/assets/images/nav/store.png', import.meta.url).href,
          path: '/apply/settled_protocol',
        }];//顶部菜单详细信息
        if (res.data.resourceList.length > 0) {
          localStorage.setItem('sld_menu_data', JSON.stringify(res.data.resourceList));
          let sld_all_routes = [];//所有页面的路由
          res.data.resourceList.map(item => {
            item.children.map(child => {
              sld_all_routes.push(child.frontPath)
            })
          })
          localStorage.setItem('sld_all_routes', JSON.stringify(sld_all_routes));
        }
        localStorage.setItem('cur_top_nav', JSON.stringify(cur_top_nav));
        localStorage.setItem('cur_top_nav_info', JSON.stringify(cur_top_nav_info));
      } else if (res.state === 200) {
        let cur_top_nav: any = []; //顶部菜单
        let cur_top_nav_info: any = []; //顶部菜单详细信息
        if (res.data.resourceList.length > 0) {
          localStorage.setItem('sld_menu_data', JSON.stringify(res.data.resourceList));
          let sld_all_routes: any = []; //所有页面的路由
          res.data.resourceList.map((item) => {
            item.children.map((child) => {
              sld_all_routes.push(child.frontPath);
            });
          });
          localStorage.setItem('sld_all_routes', JSON.stringify(sld_all_routes));
          let tmp_data = res.data.resourceList;
          for (let i in tmp_data) {
            let split_first = tmp_data[i].frontPath.split('/');
            let target = split_first[1];
            if (cur_top_nav.indexOf(target) == -1) {
              let target_data: any = {};
              target_data.top_nav = target;
              target_data.path = tmp_data[i].children[0].frontPath;
              if (target == 'basic') {
                target_data.name = '概况';
                target_data.left_icon = new URL('/src/assets/images/nav/basic.png', import.meta.url).href;
              } else if (target == 'goods') {
                target_data.name = '商品';
                target_data.left_icon = new URL('/src/assets/images/nav/goods.png', import.meta.url).href;
              } else if (target == 'order') {
                target_data.name = '订单';
                target_data.left_icon = new URL('/src/assets/images/nav/order.png', import.meta.url).href;
              } else if (target == 'store') {
                target_data.name = '店铺';
                target_data.left_icon = new URL('/src/assets/images/nav/store.png', import.meta.url).href;
              } else if (target == 'bill') {
                target_data.name = '结算';
                target_data.left_icon = new URL('/src/assets/images/nav/bill.png', import.meta.url).href;
              } else if (target == 'point') {
                target_data.name = '积分商城';
                target_data.left_icon = new URL('/src/assets/images/nav/point.png', import.meta.url).href;
              } else if (target == 'im') {
                target_data.name = '客服';
                target_data.left_icon = new URL('/src/assets/images/nav/point.png', import.meta.url).href;
              } else if (target == 'spreader') {
                target_data.name = '推手';
                target_data.left_icon = new URL('/src/assets/images/nav/spreader.png', import.meta.url).href;
              } else if (target == 'statistics') {
                target_data.name = '统计';
                target_data.left_icon = new URL('/src/assets/images/nav/statistics.png', import.meta.url).href;
              } else if (target == 'marketing') {
                target_data.name = '应用';
                target_data.left_icon = new URL('/src/assets/images/nav/marketing.png', import.meta.url).href;
              }
              if (target_data.name) {
                cur_top_nav.push(target);
                cur_top_nav_info.push(target_data);
              }
            }
          }
          localStorage.setItem('cur_top_nav', JSON.stringify(cur_top_nav));
          localStorage.setItem('cur_top_nav_info', JSON.stringify(cur_top_nav_info));
        }
      }
    };

    const onSubmit = async (): Promise<void> => {
      isLoading.value = true;
      try {
        await formRef.value.validate();
        let obj = {}
        const result: any = await registerPost({ ...formState, verifyKey: capchaKey.value, ...obj });
        if (result?.state == 200) {
          message.success(result?.msg);
        } else if (result?.state == 267) {
          const accessToken = result.data.access_token;
          const refreshToken = result.data.refresh_token;
          const lastGetTokenTime = new Date().getTime();
          const isStoreAdmin = result.data.isStoreAdmin;
          const vendorInfo = {
            vendorId: result.data.vendorId,
            vendorName: result.data.vendorName ? result.data.vendorName : formState.vendorName,
            roleName: result.data.roleName,
          };
          const storeInfo = {
            storeId: result.data.storeId,
            // dev_o2o-start
            shopType: result.data.shopType ? result.data.shopType : 1,
            // dev_o2o-end
            storeName: result.data.storeName ? result.data.storeName
              : result.data.vendorName ? result.data.vendorName : formState.vendorName,
            storeLogo: result.data.storeLogo,
            storeGradeName: result.data.storeGradeName,
          };
          setAuthCache(SLD_ACCESS_TOKEN_KEY, accessToken);
          setAuthCache(SLD_REFRESH_TOKEN_KEY, refreshToken);
          setAuthCache(SLD_LAST_GET_TOKEN_TIME_KEY, lastGetTokenTime);
          setAuthCache(SLD_IS_STORE_ADMIN_KEY, isStoreAdmin);
          setAuthCache(SLD_VENDOR_INFO_KEY, vendorInfo);
          setAuthCache(SLD_STORE_INFO_KEY, storeInfo);
          setAuthCache(SLD_MENU_LIST_KEY, result.data.resourceList);
          localStorage.removeItem('agree_protocol');
          localStorage.removeItem('base_info');
          localStorage.removeItem('business_info');
          localStorage.removeItem('apply_state');
          localStorage.setItem('cur_step', '0');
          userStore.setAccessToken(accessToken);
          handleLoginData(result);
          isLoading.value = false;
          visible.value = false;
          visibleSuccess.value = true;
        } else {
          getCapImage();
          message.error(result?.msg);
          isLoading.value = false;
        }
      } finally {
        isLoading.value = false;
      }
    };

    watch(visible, () => {
      if (visible.value) {
        getCapImage();
      }
    });

    const handleKeydown = (e) => {
      let prohibitInput = ['-', '+', '.'];
      if (prohibitInput.includes(e.key)) {
        e.preventDefault();
      }
    };

    const afterClose = () => {
      formRef.value.resetFields();
    };

    const handlesCancel = () => {
      visibleSuccess.value = false;
    };

    const handleSuccessBtn = () => {
      window.location.replace('/apply/settled_protocol');
      // router.replace('/apply/settled_protocol');
    };

    return {
      afterClose,
      visible,
      handleOk,
      openModal,
      formState,
      handleCancel,
      getCapImage,
      capchaImg,
      rules,
      onSubmit,
      formRef,
      clearValidate,
      isLoading,
      startSms,
      countTime,
      handleKeydown,
      visibleSuccess,
      vendor_login_head_portrait,
      handleSuccessBtn,
      handleLoginData,
      handlesCancel,
    };
  },
});
</script>

<style lang="less">
.register_modal_class {
  top: 50px;

  .ant-modal-header {
    background-color: @primary-color;

    .ant-modal-title {
      color: #fff;
    }
  }

  .border_color {
    border-left-color: @primary-color;
    color: @primary-color;
  }

  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
  }

  input[type='number'] {
    -moz-appearance: textfield;
  }
}
</style>
