<template>
  <div>
    <Modal :visible="visible" title="找回密码" @ok="handleOk" @cancel="handleCancel" class="modal_class" :footer="null"
      :width="490" :afterClose="afterClose">
      <div class="h-20px"></div>
      <Form class="flex flex-col items-center justify-center" :model="formState" :rules="formRules" ref="formRef">
        <!-- 手机号 -->
        <FormItem class="!w-370px" name="vendorMobile">
          <Input v-model:value="formState.vendorMobile" class="!p-0 !pr-3" placeholder="请输入手机号" allowClear
            type="number" :maxlength="11" @change="clearValidate('vendorMobile')" @keydown="handleKeydown">
          <template #prefix>
            <div class="text-center text-[#333] text-[14px] bg-[#F8F8F8] w-90px leading-10 mr-2">
              <span class="text-[#ef1216] text-xs">*</span>
              <span>手机号</span>
            </div>
          </template>
          </Input>
        </FormItem>

        <!-- //验证码 -->
        <FormItem class="!w-370px" name="verifyCode">
          <Input v-model:value="formState.verifyCode" placeholder="请输入验证码" allowClear class="!p-0" :maxlength="4"
            @change="clearValidate('verifyCode')">
          <template #prefix>
            <div class="text-center text-[#333] text-[14px] bg-[#F8F8F8] w-90px leading-10 mr-2">
              <span class="text-[#ef1216] text-xs">*</span>
              <span>验证码</span>
            </div>
          </template>

          <template #suffix>
            <img :src="capchaImg" alt="" class="h-full" @click="getCapImage" />
          </template>
          </Input>
        </FormItem>

        <!-- 短信验证码 -->
        <FormItem class="!w-370px" name="smsCode">
          <Input v-model:value="formState.smsCode" class="!p-0" placeholder="请输入短信验证码" allowClear
            type="number" @change="clearValidate('smsCode')" @keydown="handleKeydown">
          <template #prefix>
            <div class="text-center text-[#333] text-[14px] bg-[#F8F8F8] w-90px leading-10 mr-2">
              <span class="text-[#ef1216] text-xs">*</span>
              <span>短信验证码</span>
            </div>
          </template>

          <template #suffix>
            <div
              class="text-xs leading-4 text-center transition ease-in-out border border-white cursor-pointer w-94px hover:text-opacity-50 border_color"
              @click="startSms">
              {{ countTime == 0 ? '获取验证码' : `${countTime}秒` }}
            </div>
          </template>
          </Input>
        </FormItem>
        <!-- 新密码 -->
        <FormItem class="!w-370px" name="vendorPassword">
          <InputPassword v-model:value="formState.vendorPassword" placeholder="请设置6～20位字母、数字或符号组成的密码" allowClear
            class="!p-0 !pr-3 border-none" type="text" :maxlength="20" @change="clearValidate('vendorPassword')">
            <template #prefix>
              <div class="text-center text-[#333] text-[14px] bg-[#F8F8F8] w-90px leading-10 mr-2">
                <span class="text-[#ef1216] text-xs">*</span>
                <span>新密码</span>
              </div>
            </template>
          </InputPassword>
        </FormItem>
        <!-- 再次输入密码 -->
        <FormItem class="!w-370px" name="confirmPassword">
          <InputPassword v-model:value="formState.confirmPassword" placeholder="请再次输入密码" allowClear :maxlength="20"
            class="!p-0 !pr-3" @change="clearValidate('confirmPassword')">
            <template #prefix>
              <div class="text-center text-[#333] text-[14px] bg-[#F8F8F8] w-90px leading-10 mr-2">
                <span class="text-[#ef1216] text-xs">*</span>
                <span>确认密码</span>
              </div>
            </template>
          </InputPassword>
        </FormItem>
        <FormItem class="!w-370px">
          <Button class="!h-40px w-full" type="primary" @click="onSubmit" :loading="isloading">找回密码</Button>
        </FormItem>
      </Form>
    </Modal>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, onMounted } from 'vue';
import { Modal, Form, FormItem, Input, InputPassword, Button, message } from 'ant-design-vue';
import useCheck, { rulesCheckType } from '../../hooks/common/useCheck';
import type { RuleObject } from 'ant-design-vue/lib/form/interface';
import { retrievePwd, getSmsCode } from '/@/api/sys/account';
import countDown from '/@/utils/countDown';
import { getCaptchaApi } from '@/api/sys/example';

export default defineComponent({
  name: 'ForgetPassword',
  components: {
    Modal,
    Form,
    FormItem,
    Input,
    InputPassword,
    Button,
  },
  setup() {
    const visible = ref<boolean>(false);
    const checkUnite = useCheck();
    const formRef = ref();
    const capchaImg = ref('');
    const capchaKey = ref('');
    const isloading = ref<boolean>(false);
    const { startDown, countTime } = countDown();
    const formState = reactive({
      vendorMobile: '',
      smsCode: '',
      vendorPassword: '',
      confirmPassword: '',
      verifyCode: '',
    });

    const validatePass = async (rule: RuleObject, value: string, type: string) => {
      const checkMethod = checkUnite[type];
      const { checkState, stateValue } = checkMethod(value);
      if (checkState > 0) {
        return Promise.reject(stateValue);
      } else {
        return Promise.resolve();
      }
    };

    const checkConfirmPass = (rules, value) => {
      if (value !== formState.vendorPassword) {
        return Promise.reject('两次密码输入不一致');
      } else {
        return Promise.resolve();
      }
    };

    const formRules: rulesCheckType = {
      vendorMobile: [
        {
          required: true,
          trigger: 'blur',
          validator: (r, v) => validatePass(r, v, 'checkMobile'),
        },
      ],
      verifyCode: {
        required: true,
        trigger: 'blur',
        validator: (r, v) => validatePass(r, v, 'checkImgCode'),
      },
      smsCode: [
        {
          required: true,
          trigger: 'blur',
          validator: (r, v) => validatePass(r, v, 'checkSmsCode'),
        },
      ],
      vendorPassword: {
        required: true,
        trigger: 'blur',
        validator: (r, v) => validatePass(r, v, 'checkPasswordNewOld'),
      },
      confirmPassword: {
        required: true,
        trigger: 'blur',
        validator: checkConfirmPass,
      },
    };

    const handleOk = (): void => {
      visible.value = false;
    };
    const handleCancel = (): void => {
      visible.value = false;
    };
    const openModal = (): void => {
      visible.value = true;
    };

    const preventClick = ref<boolean>(false);
    const startSms = async () => {
      try {
        await formRef.value.validate(['vendorMobile', 'verifyCode']);
        if (preventClick.value) {
          return;
        }
        preventClick.value = true;
        const result: any = await getSmsCode({
          type: 'retrieve',
          mobile: formState.vendorMobile,
          verifyCode: formState.verifyCode,
          verifyKey: capchaKey.value,
        });

        if (result?.state == 200) {
          message.success(result?.msg);
          startDown(() => {
            preventClick.value = false;
          });
        } else {
          preventClick.value = false;
          message.error(result?.msg);
        }
      } catch (error) {
        preventClick.value = false;
      }
    };

    const clearValidate = (name) => {
      if (name == 'vendorMobile') {
        formState.vendorMobile = formState.vendorMobile.toString().substring(0, 11);
      }

      if (name == 'smsCode') {
        formState.smsCode = formState.smsCode.toString().substring(0, 6);
      }

      formRef.value.clearValidate(name);
    };

    const getCapImage = async () => {
      const cap = await getCaptchaApi();
      if (cap?.['data']) {
        capchaImg.value = 'data:image/png;base64,' + cap?.['data']['captcha'];
        capchaKey.value = cap?.['data']['key'];
      }
    };

    const onSubmit = async (): Promise<void> => {
      isloading.value = true;
      try {
        await formRef.value.validate();
        const param = {
          mobile: formState.vendorMobile,
          verifyCode: formState.verifyCode,
          verifyKey: capchaKey.value,
          smsCode: formState.smsCode,
          newPwd: formState.vendorPassword,
          confirmPwd: formState.confirmPassword,
        };
        const result: any = await retrievePwd(param);
        if (result?.state == 200) {
          visible.value = false;
          formRef.value.resetFields();
          message.success(result?.msg);
        } else {
          isloading.value = false;
          message.error(result?.msg);
        }
      } catch (error) {
        console.log(error);
      } finally {
        isloading.value = false;
      }
    };

    const handleKeydown = (e) => {
      let prohibitInput = ['-', 'e', '+', 'E', '.'];
      if (prohibitInput.includes(e.key)) {
        e.preventDefault();
      }
    };

    const afterClose = () => {
      formRef.value.resetFields();
    };

    onMounted(() => {
      getCapImage()
    })

    return {
      afterClose,
      visible,
      handleOk,
      openModal,
      formState,
      handleCancel,
      formRules,
      formRef,
      onSubmit,
      clearValidate,
      isloading,
      countTime,
      startSms,
      handleKeydown,
      capchaImg,
      capchaKey,
      getCapImage
    };
  },
});
</script>

<style lang="less">
.modal_class {
  .ant-modal-header {
    background-color: @primary-color;

    .ant-modal-title {
      color: #fff;
    }
  }

  .border_color {
    border-left-color: @primary-color;
    color: @primary-color;
  }

  .items-center {
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
    }
    input[type="number"]{
        -moz-appearance: textfield;
    }
  }
}

</style>
