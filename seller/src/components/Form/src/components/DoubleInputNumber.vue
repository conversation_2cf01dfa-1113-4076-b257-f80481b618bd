<template>
  <div class="flex_row_start_center" style="width: 100%;">
    <InputNumber
      :min="0"
      :max="showFields[fields[1]] ? showFields[fields[1]] : 9999999"
      :precision="precision"
      :placeholder="t('component.icon.min_input_search')"
      v-model:value="showFields[fields[0]]"
      @change="(e) => handleChange(e, fields[0])"
      style="flex: 1"
    />
    <Icon
      icon="ion:arrow-forward-outline"
      style="margin-left: 10px;color: rgb(0 0 0 / 25%);font-size: 14px;"
    />
    <InputNumber
      :min="showFields[fields[0]] ? showFields[fields[0]] : 0"
      :max="9999999"
      :precision="precision"
      :placeholder="t('component.icon.max_input_search')"
      v-model:value="showFields[fields[1]]"
      @change="(e) => handleChange(e, fields[1])"
      style="flex: 1"
    />
  </div>
</template>
<script lang="ts">
import { defineComponent, ref, watch } from 'vue';
import { InputNumber } from 'ant-design-vue';
import { useI18n } from '/@/hooks/web/useI18n';
import { useRuleFormItem } from '/@/hooks/component/useFormItem';
import Icon from '@/components/Icon/Icon.vue';

export default defineComponent({
  name: 'DoubleInputNumber',
  components: { InputNumber, Icon },
  props: {
    value: {
      type: String,
      default: ''
    },
    fields: {
      type: Array,
      default: ['min', 'max']
    },
    fields_value: {
      type: Object,
      default: { min: '', max: '' }
    },
    precision: {
      type: Number,
      default: 0,
    },
  },
  emits: ['change', 'update:value'],
  setup(props, { emit }) {
    const { t } = useI18n();

    const emitData = ref<any>('');
    const showFields = ref(props.fields_value);
    const [state] = useRuleFormItem(props, 'value', 'change', emitData);
    const canReset = ref(true);

    watch(
      () => props.value,
      (v) => {
        if (canReset.value) {
          v = ''
          state.value = ''
          showFields.value[props.fields[0]] = ''
          showFields.value[props.fields[1]] = ''
        } else {
          canReset.value = true
        }
        emit('update:value', v);
      },
    );

    function handleChange(value, key) {
      let state_val = []
      props.fields.map((field_key) => {
        if (field_key == key) {
          showFields.value[field_key] = value
        }
        state_val.push(showFields.value[field_key])
      })
      state.value = state_val.join(',')
      canReset.value = false
      emit('change', state.value);
    }

    return { showFields, state, t, handleChange };
  },
});
</script>
