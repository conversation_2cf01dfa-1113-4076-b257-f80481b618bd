<template>
  <a-col
    v-bind="actionColOpt"
    v-if="showActionButtonGroup"
    :class="{ 'from-search': true, table_search: tableFlag }"
    :style="{
      width: tableFlag ? width + '%' : '230px',
      maxWidth: tableFlag ? width + '%' : '230px',
    }"
  >
    <div style="width: 100%" :style="{ textAlign: actionColOpt.style.textAlign }">
      <FormItem>
        <slot name="resetBefore"></slot>
        <Button
          type="default"
          class="mr-2 from-search-bottom"
          v-bind="getResetBtnOptions"
          @click="resetAction"
          v-if="showResetButton"
        >
          {{ getResetBtnOptions.text }}
        </Button>
        <slot name="submitBefore"></slot>

        <Button
          type="primary"
          class="mr-2 from-search-bottom"
          v-bind="getSubmitBtnOptions"
          @click="submitAction"
          v-if="showSubmitButton"
          :style="{
            marginRight: tableFlag
              ? showAdvancedButton && !hideAdvanceBtn
                ? '0.5rem'
                : '0px'
              : '0.5rem',
          }"
        >
          {{ getSubmitBtnOptions.text }}
        </Button>

        <slot name="advanceBefore"></slot>
        <Button
          type="link"
          size="small"
          @click="toggleAdvanced"
          v-if="showAdvancedButton && !hideAdvanceBtn"
        >
          {{ isAdvanced ? t('component.form.putAway') : t('component.form.unfold') }}
          <BasicArrow class="ml-1" :expand="!isAdvanced" up />
        </Button>
        <slot name="advanceAfter"></slot>
      </FormItem>
    </div>
  </a-col>
</template>
<script lang="ts">
  import type { ColEx } from '../types/index';
  //import type { ButtonProps } from 'ant-design-vue/es/button/buttonTypes';
  import { defineComponent, computed, PropType, watch, ref } from 'vue';
  import { Form, Col } from 'ant-design-vue';
  import { Button, ButtonProps } from '/@/components/Button';
  import { BasicArrow } from '/@/components/Basic';
  import { useFormContext } from '../hooks/useFormContext';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { propTypes } from '/@/utils/propTypes';

  type ButtonOptions = Partial<ButtonProps> & { text: string };

  export default defineComponent({
    name: 'BasicFormAction',
    components: {
      FormItem: Form.Item,
      Button,
      BasicArrow,
      [Col.name]: Col,
    },
    props: {
      showActionButtonGroup: propTypes.bool.def(true),
      showResetButton: propTypes.bool.def(true),
      showSubmitButton: propTypes.bool.def(true),
      showAdvancedButton: propTypes.bool.def(true),
      tableFlag: propTypes.bool.def(false),
      resetButtonOptions: {
        type: Object as PropType<ButtonOptions>,
        default: () => ({}),
      },
      submitButtonOptions: {
        type: Object as PropType<ButtonOptions>,
        default: () => ({}),
      },
      actionColOptions: {
        type: Object as PropType<Partial<ColEx>>,
        default: () => ({}),
      },
      actionSpan: propTypes.number.def(6),
      isAdvanced: propTypes.bool,
      hideAdvanceBtn: propTypes.bool,
      getSchema: {
        type: Array,
      },
    },
    emits: ['toggle-advanced'],
    setup(props, { emit }) {
      const { t } = useI18n();
      const width = ref(20);
      const tableFlag = computed(() => {
        let { tableFlag, isAdvanced, getSchema } = props;
        if (tableFlag) {
          let num = 0;
          if (!isAdvanced) {
            setTimeout(() => {
              for (let i = 0; i < getSchema?.length; i++) {
                if (getSchema[i].padding_type > 180) {
                  width.value = 100 - (num % 100);
                  break;
                } else {
                  num = getSchema[i].padding_type;
                  if (getSchema.length - 1 == i) {
                    width.value = 100 - (num % 100);
                  }
                }
              }
            }, 100);
          } else {
            width.value = 100 - (getSchema[getSchema.length - 1].padding_type % 100);
          }
        }
        return tableFlag;
      });
      const isAdvanced = computed(() => {
        let { isAdvanced, getSchema, tableFlag } = props;
        if (tableFlag) {
          let num = 0;
          if (!isAdvanced) {
            setTimeout(() => {
              for (let i = 0; i < getSchema?.length; i++) {
                if (getSchema[i].padding_type > 180) {
                  width.value = 100 - (num % 100);
                  break;
                } else {
                  num = getSchema[i].padding_type;
                  if (getSchema.length - 1 == i) {
                    width.value = 100 - (num % 100);
                  }
                }
              }
            }, 100);
          } else {
            width.value = 100 - (getSchema[getSchema.length - 1].padding_type % 100);
          }
        }
        return isAdvanced;
      });
      const actionColOpt = computed(() => {
        const { showAdvancedButton, actionSpan: span, actionColOptions } = props;
        const actionSpan = 24 - span;
        const advancedSpanObj = showAdvancedButton
          ? { span: actionSpan < 6 ? 24 : actionSpan }
          : {};
        const actionColOpt: Partial<ColEx> = {
          style: { textAlign: 'right' },
          span: showAdvancedButton ? 6 : 4,
          ...advancedSpanObj,
          ...actionColOptions,
        };
        return actionColOpt;
      });

      const getResetBtnOptions = computed((): ButtonOptions => {
        return Object.assign(
          {
            text: t('common.resetText'),
          },
          props.resetButtonOptions,
        );
      });

      const getSubmitBtnOptions = computed(() => {
        return Object.assign(
          {
            text: t('common.queryText'),
          },
          props.submitButtonOptions,
        );
      });

      function toggleAdvanced() {
        emit('toggle-advanced');
      }

      return {
        t,
        actionColOpt,
        getResetBtnOptions,
        getSubmitBtnOptions,
        toggleAdvanced,
        ...useFormContext(),
        tableFlag,
        width,
        isAdvanced,
      };
    },
  });
</script>
<style lang="less">
  .from-search {
    position: absolute;
    right: 0;
    flex: 1;
    width: 230px;

    .from-search-bottom {
      height: 29px !important;
      padding: 0 15px;

      span {
        font-size: 13px;
      }
    }
  }
</style>
