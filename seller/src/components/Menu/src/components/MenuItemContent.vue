<template>
  <span :class="`${prefixCls}- flex items-center must_add_head_position`">
    <!-- <Icon
      v-if="getIcon && getPath != '/spreader'"
      :icon="getIcon"
      :size="18"
      :class="`${prefixCls}-wrapper__icon mr-2`"
    /> -->
    {{ getI18nName }}
    <img
      src="@/assets/images/must_add.png"
      alt=""
      v-if="extra.indexOf(getPath)!=-1&&specialFlag > -3"
      class="must_add_head"
    />
  </span>
</template>
<script lang="ts">
  import { computed, defineComponent,ref } from 'vue';
  import Icon from '@/components/Icon/Icon.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { specialFlag } from '/@/utils/utils';
  import { contentProps } from '../props';

  const { t } = useI18n();

  export default defineComponent({
    name: 'MenuItemContent',
    components: {
      Icon,
    },
    props: contentProps,
    setup(props) {
      const { prefixCls } = useDesign('basic-menu-item-content');
      const getI18nName = computed(() => t(props.item?.name));
      const getIcon = computed(() => props.item?.icon);
      const getPath = computed(() => props.item?.path);
      const extra = ref(['/spreader'])
      return {
        prefixCls,
        getI18nName,
        getIcon,
        getPath,
        extra,
        specialFlag
      };
    },
  });
</script>

<style lang="less">
  .must_add_head_position{
    .must_add_head{
      width: 28px;
      height: 14px;
      position: relative;
      top: -8px;
      margin-left: -2px;
    }
  }
</style>

