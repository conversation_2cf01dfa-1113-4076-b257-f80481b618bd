<template>
  <div style="display: block" class="SldEditFormCom">
    <div class="flex_com_row_wrap">
      <template v-for="(item, index) in search_data" :key="key">
        <template v-if="item.type == 'input'">
          <Form.Item
            :validateFirst="true"
            :key="index"
            :name="item.name"
            :label="item.label"
            :extra="item.extra"
            :style="{ width: item.width != undefined ? item.width + 80 + 'px' : '250px' }"
            :rules="
              item.rules != undefined
                ? [
                    ...item.rules,
                    {
                      validator: async (rule, value) => {
                        await validatorEmoji(rule, value);
                      },
                    },
                  ]
                : [
                    {
                      validator: async (rule, value) => {
                        await validatorEmoji(rule, value);
                      },
                    },
                  ]
            "
          >
            <Input
              class="item"
              :placeholder="item.placeholder"
              :showCount="item.showCount ? item.showCount : false"
              :type="item.input_type ? item.input_type : 'text'"
              :disabled="item.disable === true ? item.disable : false"
              :maxlength="item.maxLength !== undefined ? item.maxLength : 99999999"
              v-model:value="curFormData[item.name]"
            />
          </Form.Item>
        </template>
        <template v-if="item.type == 'radio'">
          <Form.Item
            :validateFirst="true"
            :key="index"
            :name="item.name"
            :label="item.label"
            :extra="item.extra"
            :style="{ width: item.width != undefined ? item.width + 80 + 'px' : '250px' }"
            :rules="item.rules"
          >
            <RadioGroup
              size="small"
              v-model:value="curFormData[item.name]"
              buttonstyle="solid"
              :disabled="item.disable"
            >
              <Radio v-for="(val, ind) in item.sel_data" :key="ind" :value="val.key">
                {{ val.name }}
              </Radio>
            </RadioGroup>
          </Form.Item>
        </template>
        <template v-if="item.type == 'inputnum'">
          <Form.Item
            :validateFirst="true"
            :key="index"
            :name="item.name"
            :label="item.label"
            :extra="item.extra"
            :rules="item.rules"
          >
          <div class="flex_row_center_center">
            <InputNumber
              class="item"
              v-if="item.formatter"
              :placeholder="item.placeholder"
              :disabled="item.disable === true ? item.disable : false"
              v-model:value="curFormData[item.name]"
              :min="item.min ? item.min : 0"
              :max="item.max ? item.max : 999999999"
              :precision="item.precision ? item.precision : 0"
              :step="item.step ? item.step : 1"
              :formatter="(value) => `${value}${item.formatter ? item.formatter : ''}`"
            />
            <InputNumber
              v-else
              :placeholder="item.placeholder"
              :disabled="item.disable === true ? item.disable : false"
              v-model:value="curFormData[item.name]"
              :min="item.min ? item.min : 0"
              :max="item.max ? item.max : 999999999"
              :precision="item.precision ? item.precision : 0"
              :step="item.step ? item.step : 1"
            />
            <span :style="{width: item.avoid_width?item.avoid_width:'100px',marginLeft:'5px'}" v-if="item.avoid!=null" class="avoid_style" @click="freeExpress(item.name)">{{ item.avoid }}</span>
          </div>
          </Form.Item>
        </template>
      </template>
    </div>
  </div>
</template>
<script>
export default {
  name: 'OrderService',
};
</script>
<script setup>
import { ref, onMounted, computed } from 'vue';
import { mobile_reg, validatorEmoji } from '/@/utils/validate';
import { Spin, Input, Form, RadioGroup, Radio,InputNumber } from 'ant-design-vue';
const props = defineProps({
  // title: { type: String, required: true }, //弹框标题
  search_data: { type: Array, required: true, default: () => [] }, //表单数据
  curForm_data: { type: Object }, //引用页面，用于方法内的判断处理
});

//由于watch无法监听props.content，所以需要计算属性转为ref对象
const search_data = computed(() => {
  return props.search_data;
});
//由于watch无法监听props.content，所以需要计算属性转为ref对象
const curFormData = computed(() => {
  return props.curForm_data;
});

onMounted(() => {});
</script>
<style lang="less">
@import './SldEditFormCom.less';
</style>
