export type PickerMode = 'd1' | 'd7' | 'd30' | 'dY' | 'dR' | 'diy';

export interface PropType {
  title?: string;
  name: string;
  extraTab?: Array<any>;
  extraTabValue?: any;
  datePicker?: boolean;
}

export interface RankOptions {
  title: string;
  dataIndex: string;
  sorter?: boolean | Function;
  sortDirections?: Array<'ascend' | 'descend'>;
  width?: number;
  customRender?: (...args) => JSX.Element;
  customRenderType?: string;
}

export interface Series {
  name: string;
  data: Array<string | number>;
  type: 'bar' | 'line';
}

export interface LinearOptions {
  xAxisData: Array<string | number>;
  series: Series[];
  maxMount?: number | string;
}

export type ExtraTab = {
  label: string;
  value: any;
};

export interface GeoOptions {
  series: Array<any>;
  seriesName: string;
  dataName: string;
  dataValue: string;
}

export interface PieOptions {
  series: Array<{ name: string; value: number }>;
  isRing: boolean;
  isLegend: boolean;
  title?: Object;
}

export interface ExportOption {
  api: string;
  fileName: string;
}
