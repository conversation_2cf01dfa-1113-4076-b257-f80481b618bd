import { ITEM_COLOR } from '../constant/color'
import echarts from '/@/utils/lib/echarts';
export const echartsOptions = () => {
  const constantOption = {
    legend: {
      orient: 'horizontal',
      right: 'center',
      top: 'top',
      icon: 'circle',
      type: 'scroll',
      width: '80%',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        lineStyle: {
          width: 1,
          color: '#019680',
        },
      },
    },
    grid: { left: '2%', right: '5%', top: '15%', bottom: '1%', containLabel: true },
  };
  const xAxisOption = (xAxisData) => {
    return {
      xAxis: {
        type: 'category',
        boundaryGap: false,
        nameRotate: 45,
        data: xAxisData,
        axisTick: {
          show: false,
          length: 10,
          alignWithLabel: true,
        },
        axisLabel: {
          interval: parseInt(xAxisData.length / 9),
        },
      },
    };
  };
  const yAxisOption = (yAxisMax = 80000) => {
    const stringNumber = parseInt(yAxisMax).toString();
    const [preffix] = stringNumber;
    const limitMax = Math.pow(10, stringNumber.length - 1) * (Number(preffix) + 1);
    let finalMax;
    if (yAxisMax >= limitMax / 2) {
      finalMax = limitMax;
    } else {
      finalMax = limitMax / 2;
    }
    return {
      yAxis: [
        {
          type: 'value',
          max: finalMax,
          min: 0,
          splitNumber: 4,
          axisTick: {
            show: false,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#999',
            },
          },
        },
      ],
    };
  };
  const seriesOption = (data) => {
    return {
      series: data.map((item, index) => {
        return {
          ...item,
          smooth: true,
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0.5,
                color: ITEM_COLOR[index]
              },
              {
                offset: 1,
                color: '#fff'
              }
            ])
          },
          itemStyle: {
            color: ITEM_COLOR[index],
          },
        }
      }),
    };
  };
  return { constantOption, xAxisOption, yAxisOption, seriesOption };
};
export const setLinearCharts = (setOptions, options) => {
  const { constantOption, xAxisOption, yAxisOption, seriesOption } = echartsOptions();
  const { xAxisData, maxMount, series } = options;
  return new Promise((resolve) => {
    setOptions({
      ...constantOption,
      ...xAxisOption(xAxisData),
      ...yAxisOption(maxMount),
      ...seriesOption(series),
    }).then(resolve);
  });
};
