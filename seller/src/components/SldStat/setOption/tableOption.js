import { defHttp } from '/@/utils/http/axios';
import { downloadByData } from '@/utils/file/download';
import {
  PriceCustomRender,
  GoodsCustomRender,
  TextCustomRender,
  TextHiddenCustomRender,
} from '../StatRank/customRender';
import { sldComLanguage } from '@/utils/utils';
import { isArrayBuffer, isObject } from 'lodash-es';
import { message } from 'ant-design-vue'
const cusRenderMap = {
  priceRender: PriceCustomRender,
  goodsRender: GoodsCustomRender,
  textRender: TextCustomRender,
  textHidRender: TextHiddenCustomRender,
};
export const rankDataOption = (col, isIndex) => {
  const basicCol = {
    title: sldComLanguage('序号'),
    dataIndex: 'index',
    align: 'center',
    width: 100,
  };
  const targetCols = col.map((single) => {
    let obj = {
      ...single,
      align: 'center',
      width: single.width || 150,
    };
    if (obj.customRenderType) {
      obj.customRender = cusRenderMap[obj.customRenderType];
      delete obj.customRenderType;
    }
    return obj;
  });
  return isIndex ? [basicCol, ...targetCols] : targetCols;
};
export const exportRequestOption = async (options, dateRange) => {
  const params = {
    fileName: options.fileName,
    ...dateRange,
  };
  const headers = {
    'Content-Type': 'application/vnd.ms-excel;charset=UTF-8',
  };
  const result = await defHttp.get({
    url: options.api,
    params,
    headers,
    responseType: 'blob',
  });
  if (result.type.indexOf('excel') > -1) {
    message.success('导出报表成功')
  }
};
