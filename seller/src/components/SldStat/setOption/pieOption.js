export const echartsOptions = (options) => {
  const data = options.series;
  const constantOption = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        stillShowZeroSum: false,
        type: 'pie',
        label: {
          show: true,
          formatter (param) {
            return param.name + ' (' + param.percent + '%)';
          },
        },
        radius: options.isRing ? ['40%', '50%'] : '50%',
        data,
      },
    ],
  };
  options.isLegend &&
    (constantOption.legend = {
      orient: 'horizontal',
      right: 'center',
      top: 'top',
      icon: 'circle',
      type: 'plain',
      width: '80%',
    });
  options.title &&
    (constantOption.title = Object.assign(options.title, { left: 'center', top: '45%' }));
  return constantOption;
};
export const setPieCharts = (setOptions, options) => {
  return new Promise((resolve) => {
    setOptions({
      ...echartsOptions(options),
    }).then(resolve);
  });
};
