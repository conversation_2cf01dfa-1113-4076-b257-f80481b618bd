import dayjs from 'dayjs';
import { withInstall } from '/@/utils';
import StatChartsVue from './StatCharts/index.vue';
import useStatCharts from './StatCharts/useStatChartsContext';
import StatRankVue from './StatRank/StatRank.vue';
import useStatRank from './StatRank/useStatRankContext';
import StatCardVue from './StatCard.vue';
import ConverImageVue from './ConverImage.vue';
import pickerVue from './DynaPicker/picker.vue';
import ReportFormVue from './ReportTable/ReportForm.vue';
import ReportWrapperVue from './ReportTable/ReportWrapper.vue';
import CardLayout from './layout';
const SldStatCharts = withInstall(StatChartsVue);
const SldStatRank = withInstall(StatRankVue);
const SldStatCard = withInstall(StatCardVue);
const ConverImage = withInstall(ConverImageVue);
const DateMultiPicker = withInstall(pickerVue);
const ReportForm = withInstall(ReportFormVue);
const ReportWrapper = withInstall(ReportWrapperVue);
export {
  SldStatCharts,
  useStatCharts,
  SldStatRank,
  useStatRank,
  SldStatCard,
  ConverImage,
  DateMultiPicker,
  ReportForm,
  ReportWrapper,
  CardLayout,
};
const initialDate = () => {
  const yesterDay = new Date();
  yesterDay.setDate(yesterDay.getDate() - 1);
  const format = dayjs(yesterDay).format('YYYY-MM-DD');
  const startTime = format + ' 00:00:00';
  const endTime = format + ' 23:59:59:999';
  return { startTime, endTime };
};
export * from './StatRank/customRender';
export const initialDateVar = initialDate();
