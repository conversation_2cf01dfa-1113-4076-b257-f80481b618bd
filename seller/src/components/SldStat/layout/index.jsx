import './index.less';
import { getImagePath } from '/@/utils';

const icons = {
  goods_icon1: getImagePath('images/stat/goods_icon1.png'),
  goods_icon2: getImagePath('images/stat/goods_icon2.png'),
  goods_icon3: getImagePath('images/stat/goods_icon3.png'),
  brand_icon1: getImagePath('images/stat/brand_icon1.png'),
  brand_icon2: getImagePath('images/stat/brand_icon2.png'),
  store_icon1: getImagePath('images/stat/store_icon1.png'),
  store_icon2: getImagePath('images/stat/store_icon2.png'),
  user_icon1: getImagePath('images/stat/user_icon1.png'),
  user_icon2: getImagePath('images/stat/user_icon1.png'),
};

const colorSet = {
  purple: 'linear-gradient(90deg, #EA67E3, #DA20DA)',
  blue: 'linear-gradient(90deg, #6A7BFB, #8997F9)',
  yellow: 'linear-gradient(90deg, #F0BD37, #EBA311)',
  green: 'linear-gradient(90deg, #12CDD0, #01BFC2)',
  orange: 'linear-gradient(90deg, #FD9D66, #FE7B29)',
};

export default function CardLayout({ color, icon }, { slots }) {
  return (
    <div
      className="colorful_item flex flex-col justify-between"
      style={{ background: colorSet[color] }}
    >
      {slots.default()}
      <img src={icons[icon]} alt="alt" className="icon_img" />
    </div>
  );
}
