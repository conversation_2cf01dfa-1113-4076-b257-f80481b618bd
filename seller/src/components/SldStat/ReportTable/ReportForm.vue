<template>
  <div class="pt-[15px]">
    <DynaPicker @change="dynaChange" />
    <div class="h-[15px]"></div>
    <BasicTable @register="standardTable" />
  </div>
</template>

<script setup>
  import DynaPicker from '../DynaPicker/picker.vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import { initialDateVar } from '../index';
  import { ref, inject, unref } from 'vue';

  const groupFunc = inject('report-group');
  const props = defineProps({
    columns: {
      type: Array,
      requred: true,
    },
    searchData: {
      type: Array,
      default: () => [],
    },
    isColumnIndex: Boolean,
    api: Function,
    beforeFetch: Function,
    hidePagination: { type: Boolean, default: false },
    listField: { type: String, default: 'data.list' },
  });

  const dateParam = ref(initialDateVar);
  const sortParam = ref({});

  const basicChange = (pagination, filter, sort) => {
    groupFunc && groupFunc({ pagination, sort });
    sortParam.value = sort;
  };

  const handlerDateSelectSchema = (schemas) => {
    for (const i in schemas) {
      if (
        ['DatePicker', 'RangePicker', 'MonthPicker', 'WeekPicker'].includes(schemas[i].component)
      ) {
        schemas[i].componentProps.disabledDate = (current) => {
          const { startTime, endTime } = unref(dateParam);
          return current && (new Date(endTime) < current.$d || current.$d < new Date(startTime));
        };
      }
    }
    return schemas;
  };

  const beforeFetchHandler = (params) => {
    groupFunc && groupFunc(params);
    return props.beforeFetch?.(params);
  };
  const [standardTable, { reload }] = useTable({
    api: props.api,
    columns: props.columns,
    useSearchForm: props.searchData.length > 0,
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: props.listField,
      totalField: 'data.pagination.total',
    },
    pagination: props.hidePagination
      ? false
      : {
          pageSize: 10,
        },
    formConfig: {
      schemas: handlerDateSelectSchema(props.searchData),
    },
    searchInfo: () => dateParam.value,
    showIndexColumn: props.isColumnIndex,
    onChange: basicChange,
    beforeFetch: beforeFetchHandler,
    maxHeight: 450,
    canResize: false,
  });

  const dynaChange = (paramForApi) => {
    dateParam.value = paramForApi;
    groupFunc && groupFunc({ dateParam: paramForApi });
    reload({
      searchInfo: dateParam.value,
    });
  };
</script>

<style scoped></style>
