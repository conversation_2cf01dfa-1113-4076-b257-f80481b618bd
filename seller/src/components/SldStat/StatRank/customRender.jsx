export function GoodsCustomRender({ record, text }) {
  return (
    <div className="flex flex-row items-center">
      <img src={record.goodsImage} alt="img" width={25} height={25} />
      <span title={text} className="truncate ml-2 text-xs " style={{ color: 'rgb(255, 112, 30)' }}>
        {text}
      </span>
    </div>
  );
}
export function TextCustomRender({ record, text }) {
  return <span style={{ color: 'rgb(255, 112, 30)' }}>{text}</span>;
}
export function PriceCustomRender({ record, text }) {
  return <span>¥{Number(text).toFixed(2)}</span>;
}
export function TextHiddenCustomRender({ record, text }) {
  return (
    <div title={text} className="truncate ml-2 text-xs ">
      {text}
    </div>
  );
}

export function ImageCustomRender({ record, text }) {
  return (
    <img
      src={record.goodsImage || record.mainImage || record.productImage}
      alt="img"
      width={80}
      height={90}
    />
  );
}
