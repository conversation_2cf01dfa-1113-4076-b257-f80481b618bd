<template>
  <div class="visualized_item" ref="visualizedRef">
    <Spin :spinning="loading">
      <div
        class="flex p-10px border-b-1 border-[#EEEEEE] items-center flex-row justify-between min-h-44px flex-wrap"
      >
        <div class="flex flex-row items-center" v-if="props.title || $slots.extraSlot">
          <div class="position-title font-bold !text-[14px] mb-4 xl:mb-0 mr-5">{{
            props.title
          }}</div>
          <div v-if="$slots.extraSlot">
            <slot name="extraSlot"></slot>
          </div>
        </div>
        <DynamicPicker @change="submitValue" v-if="props.datePicker" />
        <div v-if="$slots.rightSlot">
          <slot name="rightSlot"></slot>
        </div>
      </div>
      <div class="p-10px h-[492px]">
        <Table
          :dataSource="currentDataSource"
          :columns="formColumns"
          bordered
          :pagination="false"
          size="small"
          rowKey="index"
          @change="tableChange"
          :scroll="{ y: 450 }"
        >
          <template #bodyCell="{ column, index, record }">
            <span v-if="column && column.dataIndex == 'index'">{{ index + 1 }}</span>
            <div v-if="column.dataIndex == 'action'">
              <slot name="action" v-bind="record"></slot>
            </div>
          </template>
          <template #emptyText>
            <div class="h-[392px] flex_column_center_center">
              <Empty
                :image="Empty.PRESENTED_IMAGE_SIMPLE"
                imageStyle="height: 78px"
                description="暂无数据~"
              ></Empty>
            </div>
          </template>
        </Table>
      </div>
    </Spin>
  </div>
</template>

<script setup>
  import { getCurrentInstance, onMounted, reactive, ref, watch, watchEffect } from 'vue';
  import { defHttp } from '/@/utils/http/axios';
  import DynamicPicker from '../DynaPicker/picker.vue';
  import { Spin, Table, Empty } from 'ant-design-vue';
  import { sucTip, failTip } from '@/utils/utils';
  import { rankDataOption } from '../setOption/tableOption';
  import { initialDateVar } from '../index';
  const props = defineProps({
    title: String,
    options: Array,
    dataSource: Array,
    datePicker: Boolean,
    isIndex: Boolean,
    rawSort: Boolean,
  });
  const emits = defineEmits(['change', 'register']);
  const currentDataSource = ref(props.dataSource);
  const currentDayType = ref('d1');
  const formColumns = ref();
  const currentDateParam = ref(initialDateVar);
  const sortOption = ref({});
  const loading = ref(true);
  const behaviorSet = reactive({
    apiUrl: '',
    paramHandler: (...arg) => ({}),
    dataHandler: (...arg) => ({}),
  });

  watch(
    () => props.options,
    () => {
      formColumns.value = rankDataOption(
        props.options,
        props.isIndex == undefined ? true : props.isIndex,
      );
    },
  );

  watchEffect(() => {
    if (props.dataSource) {
      currentDataSource.value = props.dataSource;
      loading.value = false;
    }
  });

  const submitValue = (paramForApi, dayType) => {
    currentDayType.value = dayType;
    currentDateParam.value = paramForApi;
    if (props.dataSource) {
      emitChange();
    } else {
      execApi();
    }
  };

  const tableChange = (...e) => {
    sortOption.value = e[2];
    if (props.dataSource) {
      emitChange();
    } else {
      !props.rawSort && execApi();
    }
  };

  const setBehavior = ({ apiUrl, paramHandler, dataHandler }) => {
    apiUrl && (behaviorSet.apiUrl = apiUrl);
    behaviorSet.paramHandler = paramHandler ?? null;
    behaviorSet.dataHandler = dataHandler ?? null;
  };

  const emitChange = () => {
    emits('change', { dateParam: currentDateParam.value, sortParam: sortOption.value });
  };

  const execApi = async () => {
    loading.value = true;
    const params = behaviorSet.paramHandler
      ? behaviorSet.paramHandler(currentDateParam.value, sortOption.value)
      : props.datePicker
      ? currentDateParam.value
      : {};
    const res = await defHttp.get({ url: behaviorSet.apiUrl, params });
    if (res?.state == 200) {
      currentDataSource.value = behaviorSet.dataHandler
        ? behaviorSet.dataHandler(res.data)
        : res.data;
      loading.value = false;
    } else if(res?.state == 259){
      loading.value = false;
    }else{
      failTip(res.msg);
    }
  };

  const setApiUrl = (url) => {
    behaviorSet.apiUrl = url;
  };

  const setDataSource = (source) => {
    currentDataSource.value = source;
  };

  const instance = getCurrentInstance();
  instance && emits('register', { setBehavior, execApi, setApiUrl, setDataSource }, instance.uid);

  onMounted(() => {
    formColumns.value = rankDataOption(
      props.options,
      props.isIndex == undefined ? true : props.isIndex,
    );
    behaviorSet.apiUrl && execApi();
  });
</script>

<style lang="less">
  .export_excel {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    height: 28px;
    padding-right: 7px;
    padding-left: 7px;
    border-radius: 3px;
    background: #fff;
    background-color: @primary-color;
    cursor: pointer;
  }

  .visualized_item {
    width: calc((100% - 8px) / 2);
    max-height: 559px;
    margin-top: 10px;
    border-radius: 4px;
    background-color: #fff;
  }

  .position-title {
    position: relative;
    height: 18px;
    padding-left: 8px;
    border-left: 4px solid @primary-color;
    color: #3a3e43;
    font-size: 14px;
    font-weight: 700;
    line-height: 18px;
    white-space: nowrap;
  }
</style>
