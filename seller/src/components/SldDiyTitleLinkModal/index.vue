<template>
  <div>
    <Modal
      destroyOnClose
      :maskClosable="false"
      :title="props.title"
      :visible="modalVisible"
      :zIndex="props.zIndex"
      :width="props.width"
      @ok="sldConfirm"
      :footer="props.show_foot ? undefined : null"
      @cancel="sldCancle"
    >
      <Form ref="formRef" :model="data">
        <ShowMoreHelpTip :tipData="modal_tip" :marginTop="0" v-if="modal_tip.length>0"></ShowMoreHelpTip>
        <div class="SldDiyTitleLinkModal" :style="{overflow:props.scroll?'auto':'initial',maxHeight:props.scroll?props.scrollHeight+'px':'none'}">
          <Table
            :showHeader="false"
            :columns="columns"
            :bordered="true"
            :pagination="false"
            :dataSource="data.data"
            :maxHeight="400"
            :canResize="false"
          >
            <template #bodyCell="{ column, text, record,index }">
              <template v-if="column.dataIndex == 'name'">
                <div class="table_left_con">
                  <span>{{ text }}</span>
                  <span v-if="record.required!=undefined&&record.required" class="table_left_require">
                    *
                  </span>
                </div>
              </template>
              <template v-if="column.dataIndex == 'type'">
                <template v-if="record.type == 'input'">
                  <Form.Item
                    validateFirst
                    style="width: 300px;"
                    :name="['data',index,`${record.key}`]"
                    :rules="[{
                    required: record.required,
                    whitespace: true,
                    message: `请输入${record.name}`,
                  },{
                    validator: async (rule, value) => {
                      await validatorEmoji(rule, value);
                    },
                  },]"
                  >
                    <Input
                      :maxlength="props.extra!=undefined&&props.extra[record.key+'_limit']!=undefined&&props.extra[record.key+'_limit'] ? props.extra[record.key+'_limit'] : 250"
                      :placeholder="'请输入'+record.name"
                      style="width: 300px"
                      v-model:value="record[record.key]"
                    />
                  </Form.Item>       
                </template>
                <template v-if="record.type == 'more_color_picker'">
                  <Form.Item
                    validateFirst
                    style="width: 300px"
                    :name="['data', index, `${record.key}`]"
                    :rules="[
                      {
                        required: record.required,
                        whitespace: true,
                        message: `请输入${record.name}`,
                      },
                    ]"
                  >
                    <ColorPicker
                      :color="record.value"
                      @update:color="handleColorUpdate($event, record.key)"
                    >
                    </ColorPicker>
                  </Form.Item>
                </template>
                <template v-if="record.type == 'link_type'">
                  <Select
                    placeholder="请选择链接类型"
                    style="width: 120px"
                    :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                    v-model:value="record.value"
                    @change="(e)=>sldHandSeleChange(e,record.key)"
                  >
                    <Select.Option
                      v-for="(item, index) in diy_link_type()"
                      :key="index"
                      :value="item.key"
                      >{{ item.name }}</Select.Option
                    >
                  </Select>
                </template>
                <template v-if="record.type == 'url'">
                  <Form.Item
                    validateFirst
                    style="width: 300px;"
                    :name="['data',index,'link_value']"
                    :rules="[{
                    required: record.required,
                    whitespace: true,
                    message: `请输入链接地址`,
                  },{
                    validator: async (rule, value) => {
                      await validatorEmoji(rule, value);
                    },
                  },]"
                  >
                    <Input
                      :maxlength="250"
                      style="width: 300px"
                      placeholder="请输入链接地址"
                      v-model:value="record.link_value"
                    />
                  </Form.Item>   
                </template>
                <template v-if="record.type == 'keyword'">
                  <Form.Item
                    validateFirst
                    style="width: 300px;"
                    :name="['data',index,'link_value']"
                    :rules="[{
                    required: record.required,
                    whitespace: true,
                    message: `请输入关键字`,
                  },{
                    validator: async (rule, value) => {
                      await validatorEmoji(rule, value);
                    },
                  },]"
                  >
                    <Input
                      :maxlength="250"
                      style="width: 300px"
                      placeholder="请输入关键字"
                      v-model:value="record.link_value"
                    />
                  </Form.Item>   
                </template>
                <template  v-if="record.type == 'goods'||record.type == 'category'||record.type == 'topic'||record.type == 'new_goods'||record.type == 'cms'||record.type == 'company_new'||record.type == 'store'">
                  <div>
                    <span>{{record.value}}</span>
                  </div>
                </template>
              </template>
            </template>
          </Table>
        </div>
      </Form>
    </Modal>


     <!-- 链接选择弹窗 - start -->
    <SldSelGoodsSingleDiy
      client="pc"
      :link_type="link_type"
      :modalVisible="modalVisibleSelLink"
      @confirm-event="seleSku"
      @cancle-event="sldHandleLinkCancle"
    />
    <!-- 链接选择弹窗 - end -->

  </div>
</template>
<script>
  export default {
    name: 'SldDiyTitleLinkModal',
  };
</script>
<script setup>
  import { ref, onMounted,computed,watch } from 'vue';
  import { useRouter } from 'vue-router';
  import { sucTip, failTip,isEmptyObject,diy_link_type } from '/@/utils/utils';
  import { Modal,Form,Table,Input,Select } from 'ant-design-vue';
  import ShowMoreHelpTip from '/@/components/ShowMoreHelpTip/index.vue';
  import SldSelGoodsSingleDiy from '@/components/SldSelGoodsSingleDiy/index.vue';
  import { validatorEmoji } from '/@/utils/validate';
  import ColorPicker from '/@/components/ColorPicker/index.vue';

  const emit = defineEmits(['cancleEvent', 'confirmEvent']);

  const props = defineProps({
    width: { type: Number, default: 416 }, //弹框宽度，默认416px
    title: { type: String, required: true }, //弹框标题
    submiting: { type: Boolean, default: false }, //弹框确认按钮loading属性，默认不显示
    modalVisible: { type: Boolean, required: false }, //弹框是否显示
    content: { type: Array },//内容
    modal_tip: { type: Array,default: [] },//提示语
    client: { type: String, default: '' }, 
    scroll: { type: Boolean, required: false }, //是否需要滚动
    scrollHeight: { type: Number, required: 400 }, //最大滚动高度
    type: { type: String, default: '' }, 
    extra: { type: Object }, //表单数据
    zIndex: { type: Number, default: 1000 }, //弹框的层级，默认为1000，最小值也为1000
    show_foot: { type: Boolean, default: true }, //是否显示弹框底部操作按钮，默认显示

  });

  //由于watch无法监听props.modalVisible，所以需要计算属性转为ref对象
  const modalVisible = computed(() => {
    return props.modalVisible;
  });
  const modal_tip = computed(() => {
    return props.modal_tip;
  });
  
  const formRef = ref()
  const source = ref('')
  const link_type = ref('')
  const sele_index = ref(0)

  const modalVisibleSelLink = ref(false)
  //提示语
  //数据
  const data = ref({
    data:[
      {
        key:'link_type',
        name:`操作`,
        value:'',
        type:'link_type',
      }
    ]
  })
  
  const columns = ref([
  { dataIndex: 'name', width: 150,align : 'right' , },
  { dataIndex: 'type',align : 'left' , },
  ])

  const first_flag = ref(false)
  const operate_info = ref({}) //操作下拉框选择事件的信息

  watch(modalVisible, () => {
    if (modalVisible.value) {
      if(!first_flag.value&&!isEmptyObject(props.content)){
        let ope_data = JSON.parse(JSON.stringify(props.content));
        let tmp_info = [];
        if(ope_data.title!=undefined){
          let obj = {
            key: ope_data.title.name,
            name: ope_data.title.label,
            value: ope_data.title.initialValue,
            type: 'input',
            required:ope_data.title.required,
          }
          obj[ope_data.title.name] = ope_data.title.initialValue
          tmp_info.push(obj);
        }
        if(ope_data.sub_title!=undefined){
          let obj = {
            key: ope_data.sub_title.name,
            name: ope_data.sub_title.label,
            value: ope_data.sub_title.initialValue,
            type: 'input',
            required:ope_data.sub_title.required,
          }
          obj[ope_data.sub_title.name] = ope_data.sub_title.initialValue
          tmp_info.push(obj);
        }
        if (ope_data.title_color != undefined) {
          let obj = {
            key: ope_data.title_color.name,
            name: ope_data.title_color.label,
            value: ope_data.title_color.initialValue,
            type: 'more_color_picker',
            required: ope_data.title_color.required,
          };
          obj[ope_data.title_color.name] = ope_data.title_color.initialValue;
          tmp_info.push(obj);
        }
        if (ope_data.background != undefined) {
          let obj = {
            key: ope_data.background.name,
            name: ope_data.background.label,
            value: ope_data.background.initialValue,
            type: 'more_color_picker',
            required: ope_data.background.required,
          };
          obj[ope_data.background.name] = ope_data.background.initialValue;
          tmp_info.push(obj);
        }
        tmp_info.push({
          key: 'link_type',
          name: `操作`,
          value: ope_data.link_type,
          type: 'link_type',
        });
        if(ope_data.link_value!=undefined&&ope_data.link_value){
        let tmp_info_new = {
          key:'link_value',
          name:`关键字`,
          value:ope_data.link_value,
          link_value:ope_data.link_value,
          type:ope_data.link_type,
          info:ope_data.info!=undefined?ope_data.info:{},
          required:true,
        }
        if(ope_data.link_type == 'url'){
          tmp_info_new.name = `链接地址`;
          tmp_info_new.required = true;
        }else if(ope_data.link_type == 'keyword'){
          tmp_info_new.name = `关键字`;
          tmp_info_new.required = true;
        }else if(ope_data.link_type == 'goods'){
          tmp_info_new.name = `商品名称`;
          tmp_info_new.required = false;
        }else if(ope_data.link_type == 'category'){
          tmp_info_new.name = `分类名称`;
          tmp_info_new.required = false;
        }
        tmp_info.push(tmp_info_new);
      }
        data.value.data = tmp_info
        source.value = props.content.source!=undefined?props.content.source:''
      }
    }
  });

  //操作类型选择事件
  const sldHandSeleChange = (val,key)=> {
    let flag = true;
    let cur_data = [];
    for(let i in data.value.data){
      if(data.value.data[i].key!='link_value'){
        if(data.value.data[i].key=='link_type'){
          data.value.data[i].value = val;
        }
        cur_data.push(data.value.data[i]);
      }
    }
    for(let i=0;i<cur_data.length;i++){
      if(cur_data[i].key == 'link_type'){
        if(val == 'url'){
          cur_data.splice(i+1,0,{
            key:'link_value',
            name:`链接地址`,
            value:'',
            type:'url',
            required:true,
          });
          flag = false
        }else if(val == 'keyword'){
          cur_data.splice(i+1,0,{
            key:'link_value',
            name:`关键字`,
            value:'',
            type:'keyword',
            required:true,
          });
          flag = false
        }else if(val == 'goods'){
          cur_data.splice(i+1,0,{
            key:'link_value',
            name:`商品名称`,
            value:'',
            info:{},
            type:'goods',
            required:true,
          });
        }else if(val == 'category'){
          cur_data.splice(i+1,0,{
            key:'link_value',
            name:`分类名称`,
            value:'',
            info:{},
            type:'category',
            required:true,
          });
        }else if(val == 'topic'){
          cur_data.splice(i+1,0,{
            key:'link_value',
            name:`专题名称`,
            value:'',
            info:{},
            type:'topic',
            required:true,
          });
        }else if(val == 'store'){
          cur_data.splice(i+1,0,{
            key:'link_value',
            name:`店铺名称`,
            value:'',
            info:{},
            type:'store',
            required:true,
          });
        }else{
          flag = false
        }
      }
    }

    data.value.data= cur_data;
    link_type.value = val
    modalVisibleSelLink.value = flag;
  }

    // 颜色选择器
  const handleColorUpdate = (val, name) => {
    for (let i in data.value.data) {
      if (data.value.data[i].key == name) {
        data.value.data[i].value = 'rgba(' + val.r + ',' + val.g + ',' + val.b + ',' + val.a + ')';
        return;
      }
    }
  };

  const sldConfirm = ()=> {
    formRef.value
      .validate()
      .then((res) => {
        let tmp_info = {}
        for(let i in data.value.data){
          
          if(data.value.data[i].key == 'img'){
            tmp_info.imgUrl = data.value.data[i].value;
            tmp_info.imgPath = data.value.data[i].imgPath;
          }else if(data.value.data[i].key == 'link_type'){
            tmp_info.link_type = data.value.data[i].value;
            tmp_info.link_value = '';
            tmp_info.info = {};
          }else if(data.value.data[i].key == 'link_value'){
            tmp_info.link_type = data.value.data[i].type;
            tmp_info.link_value = data.value.data[i].link_value;
            tmp_info.info = data.value.data[i].info ? data.value.data[i].info : {};
          }else if(data.value.data[i].type == 'input'){
            tmp_info[data.value.data[i].key] = data.value.data[i][data.value.data[i].key]
          } else if (data.value.data[i].type == 'more_color_picker') {
            tmp_info[data.value.data[i].key] = data.value.data[i].value;
          }
        }
        emit('confirmEvent',tmp_info);

      })
  }

  const sldCancle = ()=> {
    emit('cancleEvent');
  }

  //商品或分类选中事件
  const seleSku = (val)=> {
    data.value.data.map(item => {
			if (item.type == 'goods') {
				if(Array.isArray(val)){
          item.value = val[0].goodsName;
          item.link_value = val[0].goodsName;
          item.info = val[0];
        }else{
          item.value = val.goodsName;
          item.link_value = val.goodsName;
          item.info = val;
        }
			}else if (item.type == 'category') {
				item.value = val.categoryName;
        item.link_value = val.categoryName;
				item.info = val;
			}else if (item.type == 'topic') {
				item.value = val[0].decoName;
        item.link_value = val[0].decoName;
				item.info = val[0];
			}else if (item.type == 'store') {
				item.value = val[0].storeName;
				item.info = val[0];
        item.link_value = val[0].storeName;
			}
		});
    link_type.value = ''
    modalVisibleSelLink.value = false
  }

  // 链接选择弹窗关闭
  const sldHandleLinkCancle = ()=> {
    link_type.value = '';
    let cur_data = [];
		for (let i in data.value.data) {
			if (data.value.data[i].key != 'link_value') {
				if (data.value.data[i].key == 'link_type') {
					data.value.data[i].value = '';
				}
				cur_data.push(data.value.data[i]);
			}
		}
		data.value.data = cur_data;
    modalVisibleSelLink.value = false;
  }


  const router = useRouter();

  onMounted(() => {});
</script>
<style lang="less">
  .SldDiyTitleLinkModal{
    max-height: 400px;
    overflow: auto;
    .ant-form-item-with-help{
      margin-bottom: 0;
    }
    .ant-form-item{
      margin-bottom: 0;
    }
    .table_left_con {
      font-size: 13px;
      color: #333;
    }
    .table_left_require {
      color: red;
    }
  }
</style>
