<!-- 链接选择器选择之后渲染页面 -- 无标题 -->
<template>
  <template v-if="curFormData.url_type == 'url'">
    <Input
      :key="curFormData.type + '_' + curFormData.id + '_url_' + parent_index + '_' + index"
      :maxLength="250"
      class="more_link_input"
      :style="{
        width: label_width + 'px',
        marginTop: label_top + 'px',
        marginBottom: label_bottom + 'px',
      }"
      placeholder="请输入链接地址"
      :defaultValue="curFormData.url"
      @change="(e) => onChange(e.target.value, 'url', index, parent_index)"
    />
  </template>
  <template v-if="curFormData.url_type == 'keyword'">
    <Input
      :key="curFormData.type + '_' + curFormData.id + '_keyword_' + parent_index + '_' + index"
      :maxLength="15"
      class="more_link_input"
      :style="{
        width: label_width + 'px',
        marginTop: label_top + 'px',
        marginBottom: label_bottom + 'px',
      }"
      placeholder="请输入关键词"
      :defaultValue="curFormData.url"
      @change="(e) => onChange(e.target.value, 'url', index, parent_index)"
    />
  </template>
  <template v-if="curFormData.url_type == 'goods'">
    <Input
      :key="curFormData.type + '_' + curFormData.id + '_goods_' + parent_index + '_' + index"
      class="more_link_input"
      :style="{
        width: label_width + 'px',
        marginTop: label_top + 'px',
        marginBottom: label_bottom + 'px',
      }"
      :disabled="true"
      :value="curFormData.info.goodsName"
      :title="curFormData.info.goodsName"
    />
  </template>
  <template v-if="curFormData.url_type == 'category'">
    <Input
      :key="curFormData.type + '_' + curFormData.id + '_category_' + parent_index + '_' + index"
      class="more_link_input"
      :style="{
        width: label_width + 'px',
        marginTop: label_top + 'px',
        marginBottom: label_bottom + 'px',
      }"
      :disabled="true"
      :value="(diy_type=='integral' || diy_type=='spreader') ? curFormData.info.labelName : curFormData.info.categoryName"
      :title="(diy_type=='integral' || diy_type=='spreader') ? curFormData.info.labelName : curFormData.info.categoryName"
    />
  </template>
  <template v-if="curFormData.url_type == 'topic'">
    <Input
      :key="curFormData.type + '_' + curFormData.id + '_topic' + index"
      class="more_link_input"
      :style="{
        width: label_width + 'px',
        marginTop: label_top + 'px',
        marginBottom: label_bottom + 'px',
      }"
      :disabled="true"
      :value="curFormData.info.name"
    />
  </template>
  <template v-if="curFormData.url_type == 'seckill'">
    <Input
      :key="curFormData.type + '_' + curFormData.id + '_seckill_' + parent_index + '_' + index"
      class="more_link_input"
      :style="{
        width: label_width + 'px',
        marginTop: label_top + 'px',
        marginBottom: label_bottom + 'px',
      }"
      :disabled="true"
      :value="curFormData.info.seckillName"
    />
  </template>
  <template v-if="curFormData.url_type == 'store'">
    <Input
      :key="curFormData.type + '_' + curFormData.id + '_store_' + parent_index + '_' + index"
      class="more_link_input"
      :style="{
        width: label_width + 'px',
        marginTop: label_top + 'px',
        marginBottom: label_bottom + 'px',
      }"
      :disabled="true"
      :value="curFormData.info.storeName"
    />
  </template>
  <template v-if="curFormData.url_type == 'voucher'">
    <Input
      :key="curFormData.type + '_' + curFormData.id + '_voucher_' + parent_index + '_' + index"
      class="more_link_input"
      :style="{
        width: label_width + 'px',
        marginTop: label_top + 'px',
        marginBottom: label_bottom + 'px',
      }"
      :disabled="true"
      :value="curFormData.info.red_title"
    />
  </template>
  <template v-if="curFormData.url_type == 'live'">
    <Input
      :key="curFormData.type + '_' + curFormData.id + '_live_' + parent_index + '_' + index"
      class="more_link_input"
      :style="{
        width: label_width + 'px',
        marginTop: label_top + 'px',
        marginBottom: label_bottom + 'px',
      }"
      :disabled="true"
      :value="curFormData.info.live_name"
    />
  </template>
  <template v-if="curFormData.url_type == 'svideo'">
    <Input
      :key="curFormData.type + '_' + curFormData.id + '_svideo_' + parent_index + '_' + index"
      class="more_link_input"
      :style="{
        width: label_width + 'px',
        marginTop: label_top + 'px',
        marginBottom: label_bottom + 'px',
      }"
      :disabled="true"
      :value="curFormData.info.videoName"
    />
  </template>
  <template v-if="curFormData.url_type == 'draw'">
    <Input
      :key="curFormData.type + '_' + curFormData.id + '_draw' + index"
      class="more_link_input"
      :style="{
        width: label_width + 'px',
        marginTop: label_top + 'px',
        marginBottom: label_bottom + 'px',
      }"
      :disabled="true"
      :value="curFormData.info.drawName"
    />
  </template>
</template>
<script setup>
  import { ref, computed, watch } from 'vue';
  import { Input } from 'ant-design-vue';

  const props = defineProps({
    data: {
      type: Object,
      default: {}
    },
    index: {
      type: Number,
      default: 0
    },
    parent_index: {
      type: Number,
      default: 0
    },
    label_width: {
      type: Number,
      default: 300
    },
    label_top: {
      type: Number,
      default: 0
    },
    label_bottom: {
      type: Number,
      default: 16
    },
    diy_type: { //装修来源类型: home-首页手机装修 topic-专题装修 integral-积分商城装修 spreader-推手装修
      type: String,
      default: 'home',
    }
  });

  const curFormData = ref({});

  const formData = computed(() => {
    return props.data;
  });

  watch(
    formData,
    () => {
      curFormData.value = props.data;
    },
    { deep: true, immediate: true },
  );

  const emit = defineEmits(['handleChange']);

  function onChange(val, type, tar_index = 0, parent_index = -1) {
    emit('handleChange', val, type, tar_index, parent_index);
  }
</script>
<style lang="less" scoped>
.more_link_input {}
</style>
