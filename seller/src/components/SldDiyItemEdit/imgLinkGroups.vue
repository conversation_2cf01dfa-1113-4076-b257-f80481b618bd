<!-- 图片链接选择器组 -->
<template>
  <div class="flex_com_row_start_center sld_com_img">
    <div class="common_img_part flex_com_column_center_center">
      <div class="upload_img flex_column_center_center">
        <Upload
          name="file"
          :withCredentials="true"
          accept=".gif, .jpeg, .png, .jpg,"
          :showUploadList="false"
          :openFileDialogOnClick="false"
        >
          <div class="flex_column_center_center" @click="openMaterial(curFormData, index, -1)">
              <img v-if="curFormData.img" :src="curFormData.img"/>
              <AliSvgIcon
                v-else
                width="40px"
                height="40px"
                fillColor="#FC701E"
                iconName="iconziyuan110"
              />
            <span class="upload_btn">选择图片</span>
          </div>
        </Upload>
      </div>
      <span class="upload_img_tip">{{ curUploadTip }}</span>
    </div>
    <div class="img_con flex_com_column_center_flex_start">
      <template v-if="show_color_picker">
        <div class="flex_row_start_center" style="margin-bottom: 8px">
          <span class="selected_color_tip">选择背景色：</span>

          <div class="flex_row_start_center fzx_color_show">
            <ColorPicker
              :color="curFormData['bg_color']"
              @update:color="(e) => onChange('rgba(' + e.r + ',' + e.g + ',' + e.b + ',' + e.a + ')', 'bg_color', index)"
              :hideReset="true"
            ></ColorPicker>
           
          </div>
        </div>
      </template>
      <Input
        v-if="type != 'top_cat_nav' && type != 'lunbo'"
        :maxLength="limintLength"
        class="title"
        :placeholder="title_placeholder"
        @change="(e) => onChange(e.target.value, title_key, index)"
        :value="curFormData[title_key]"
      />
      <Select
        :value="curFormData.url_type"
        placeholder="请选择链接类型"
        @select="(e) => sldHandSeleLink(e, index)"
        style="width: 232px"
      >
        <template v-if="diy_type == 'integral'">
          <Select.Option
            v-for="(items, index) in m_diy_point_link_type()"
            :key="index"
            :value="items.key"
          >{{ items.name }}</Select.Option>
        </template>
        <template v-else-if="diy_type == 'spreader'">
          <Select.Option
            v-for="(items, index) in m_diy_spreader_link_type()"
            :key="index"
            :value="items.key"
          >{{ items.name }}</Select.Option>
        </template>
        <template v-else>
          <Select.Option
            v-for="(items, index) in m_diy_link_type()"
            :key="index"
            :value="items.key"
          >{{ items.name }}</Select.Option>
        </template>
      </Select>
      <SldDiyItemSelectLinkHideLabel
        :data="curFormData"
        :index="index"
        :parent_index="parent_index"
        @handleChange="onChange"
        :label_width="label_width"
        :label_top="label_top"
        :label_bottom="label_bottom"
      />
    </div>
    <div
      v-if="show_del_icon"
      class="flex_com_column_flex_end del_sld_com_img"
      @click="delSldComImg(index)"
    >
      <AliSvgIcon
        width="16px"
        height="16px"
        fillColor="#666"
        iconName="iconqingchu"
      />
    </div>
  </div>

  <!-- 图片素材选择 start -->
  <SldMaterialImgs
    ref="sldMaterialImgsRef"
    :visibleModal="chooseFile == 1 ? true : false"
    :maxUploadNum="1"
    :allowRepeat="false"
    :selectedData="selectImageData"
    @closeMaterial="() => closeMaterial()"
    @confirmMaterial="(val) => confirmMaterial(val)"
  ></SldMaterialImgs>
  <!-- 图片素材选择 end -->
</template>
<script setup>
  import { ref, computed, watch } from 'vue';
  import { Upload, Input, Select } from 'ant-design-vue';
  import { m_diy_link_type, m_diy_point_link_type, m_diy_spreader_link_type } from '@/utils/utils';
  import SldDiyItemSelectLinkHideLabel from './indexHideLabel.vue';
  import SldMaterialImgs from '@/components/SldMaterialFiles/sldMaterialImgs.vue';
  import ColorPicker from '/@/components/ColorPicker/index.vue';
  const props = defineProps({
    data: {
      type: Object,
      default: {}
    },
    list: {
      type: Array,
      default: []
    },
    index: {
      type: Number,
      default: 0
    },
    parent_index: {
      type: Number,
      default: -1
    },
    upload_img_tip: {
      type: String,
      default: ''
    },
    type: { //组件类型
      type: String,
      default: ''
    },
    label_width: {
      type: Number,
      default: 300
    },
    label_top: {
      type: Number,
      default: 0
    },
    label_bottom: {
      type: Number,
      default: 16
    },
    diy_type: { //装修来源类型: home-首页手机装修 topic-专题装修 integral-积分商城装修 spreader-推手装修
      type: String,
      default: 'home',
    }
  });

  const limintLength = ref(15); //限制内容长度
  const title_key = ref(''); //标题的key
  const title_placeholder = ref('请输入'); //标题的placeholder
  const show_color_picker = ref(false);
  const show_del_icon = ref(true); //显示删除图标show_del_icon
  const chooseFile = ref(0); //上传素材弹窗显示 1:图片 2:视频
  const selectImageData = { ids:[], data:[] }; //产品图片数据
  const oprate_more_tab_index = ref(0); //TAB切换当前操作的数据index
  const operate_files_index = ref(''); //当前操作选择图片下标
  const operate_files_index_parent = ref(''); //当前操作选择图片上级下标
  const operate_bottom_nav_type = ref(''); //当前操作的底部tabbar图片类型:  未选择 autoImg  已选择 selectImg
  const curFormData = ref({});
  const formData = computed(() => {
    return props.data;
  });
  const curUploadTip = ref({});

  watch(
    formData,
    () => {
      curFormData.value = props.data;
      curUploadTip.value = props.upload_img_tip;
      if (props.type == 'lunbo') { //轮播
        show_del_icon.value = true;
      } else if (props.type == 'top_cat_nav') { //顶部分类导航
        show_color_picker.value = true; //显示颜色选择器
        show_del_icon.value = true;
      } else if (props.type == 'nav') { //导航
        limintLength.value = 5;
        title_key.value = 'name';
        title_placeholder.value = '请输入导航名称';
        show_del_icon.value = true;
      } else if (props.type == 'tupianzuhe') { //图片组合
        if (props.list['sele_style'] == 0 || props.list['sele_style'] == 1) {
          curUploadTip.value = '宽750,高不限制';
        } else if (props.list['sele_style'] == 2) {
          curUploadTip.value = '宽300*高300';
        } else if (props.list['sele_style'] == 3) {
          curUploadTip.value = '宽200*高200';
        } else if (props.list['sele_style'] == 4) {
          if (props.index == 0) {
            curUploadTip.value = '宽300*高320';
          } else {
            curUploadTip.value = '宽300*高150';
          }
        } else if (props.list['sele_style'] == 5) {
          if (props.index == 0 || props.index == 3) {
            curUploadTip.value = '宽200*高200';
          } else {
            curUploadTip.value = '宽400*高200';
          }
        } else if (props.list['sele_style'] == 6) {
          if (props.index == 0 || props.index == 3) {
            curUploadTip.value = '宽300*高150';
          } else {
            curUploadTip.value = '宽300*高300';
          }
        } else if (props.list['sele_style'] == 7) {
          if (props.index == 4) {
            curUploadTip.value = '宽200*高420';
          } else {
            curUploadTip.value = '宽200*高200';
          }
        }
        title_key.value = 'title';
        title_placeholder.value = '请输入图片标题';
        if (props.list['sele_style'] < 4) {
          show_del_icon.value = true;
        } else {
          show_del_icon.value = false;
        }
      }
    },
    { deep: true, immediate: true },
  );

  const emit = defineEmits(['handleChange','handSeleLink','handleCurSelData']);

  function onChange(val, type, tar_index = 0, parent_index = -1) {
    emit('handleChange', val, type, tar_index, parent_index);
  };

  function sldHandSeleLink(e, index) {
    emit('handSeleLink', e, index);
  };

  //打开上传素材文件
  function openMaterial(item, index, indexs = -1, bottom_index) {
    if (indexs !== -1) {
      operate_files_index_parent.value = indexs;
    } else {
      operate_files_index_parent.value = '';
    }
    operate_bottom_nav_type.value = bottom_index ? bottom_index : '';
    operate_files_index.value = index;
    let selectImageDataVal = { ids: [], data: [] };
    if (item.img_path) {
      selectImageDataVal = {
        ids:[Number(item.img_path.split('?bindId=')[1])],
        data:[{
          bindId: item.img_path.split('?bindId=')[1],
          checked: true,
          filePath: item.img_path,
          fileUrl: item.img,
          fileType: 1,
        }]
      };
    }
    chooseFile.value = 1;
    selectImageData.value = selectImageDataVal;
  };
    
  //关闭上传素材文件
  function closeMaterial() {
    operate_files_index.value = '';
    chooseFile.value = 0;
  };
  
  //确认上传素材文件
  function confirmMaterial(val) {
    let data = { ...props.list };
    if (operate_files_index_parent.value !== '') {
      data.data[operate_files_index_parent.value].img[operate_files_index.value].img = val.ids.length ? val.data[0].fileUrl : '';
      data.data[operate_files_index_parent.value].img[operate_files_index.value].img_path = val.ids.length ? val.data[0].filePath : '';
      data.data[operate_files_index_parent.value].img[operate_files_index.value].imgPath = val.ids.length ? val.data[0].filePath : '';
      data.data[operate_files_index_parent.value].img[operate_files_index.value].width = val.ids.length ? val.data[0].width : '';
      data.data[operate_files_index_parent.value].img[operate_files_index.value].height = val.ids.length ? val.data[0].height : '';
      data.width = val.ids.length ? val.data[0].width : '';
      data.height = val.ids.length ? val.data[0].height : '';
    } else if (operate_bottom_nav_type.value) {
      data.data[operate_files_index.value][operate_bottom_nav_type.value].img = val.ids.length ? val.data[0].fileUrl : '';
      data.data[operate_files_index.value][operate_bottom_nav_type.value].img_path = val.ids.length ? val.data[0].filePath : '';
      data.data[operate_files_index.value][operate_bottom_nav_type.value].imgPath = val.ids.length ? val.data[0].filePath : '';
      data.data[operate_files_index.value][operate_bottom_nav_type.value].width = val.ids.length ? val.data[0].width : '';
      data.data[operate_files_index.value][operate_bottom_nav_type.value].height = val.ids.length ? val.data[0].height : '';
    } else {
      data.data[operate_files_index.value].img = val.ids.length ? val.data[0].fileUrl : '';
      data.data[operate_files_index.value].img_path = val.ids.length ? val.data[0].filePath : '';
      data.data[operate_files_index.value].imgPath = val.ids.length ? val.data[0].filePath : '';
      data.data[operate_files_index.value].width = val.ids.length ? val.data[0].width : '';
      data.data[operate_files_index.value].height = val.ids.length ? val.data[0].height : '';
      data.width = val.ids.length ? val.data[0].width : '';
      data.height = val.ids.length ? val.data[0].height : '';
    }
    chooseFile.value = 0;
    selectImageData.value = val;
    emit('handleCurSelData', data);
    operate_files_index_parent.value = '';
    operate_files_index.value = '';
  };

  //删除图片item,针对于多条数据的处理（用于轮播/导航/图片组合）
  function delSldComImg(tar_index) {
    let data = props.list.data.filter((item, index) => index != tar_index);
    props.list.data = data;
    emit('handleCurSelData', props.list);
  };
</script>
<style lang="less" scoped>
.sld_com_img {
  position: relative;
  margin-top: 10px;
  padding: 10px 0;
  border: 1px solid #eee;
  background: #f8f8f8;

  .common_img_part {
    .upload_img_tip {
      margin-top: -20px;
      margin-bottom: 21px;
      transform: scale(0.9);
      color: #bbb;
      font-size: 12px;
      line-height: 12px;
    }

    .upload_img {
      position: relative;
      margin: 22px 0 22px 22px;
      padding-right: 22px;
      border-right: 1px solid #eeee;

      img {
        max-width: 48px;
        max-height: 48px;
        cursor: pointer;
      }

      .upload_btn {
        margin-top: 5px;
        color: #666;
        font-size: 12px;
        line-height: 12px;
        text-align: center;
        white-space: nowrap;
        cursor: pointer;
      }
    }

    .upload_img:hover {
      .img_mask {
        display: inline-block !important;
      }
    }

    &.nav_bottom_img_part {
      &:nth-child(2) {
        .upload_img {
          border-right: none;
        }
      }

      .upload_img  {
        margin: 10px 0 10px 17px;
        padding-right: 17px;

        img {
          max-width: 38px;
          max-height: 38px;
        }

        .upload_btn {
          margin-top: 10px;
        }

        .upload_btn_select {
          margin-top: 2px;
          color: #ABAAAA;
          font-family: "Microsoft YaHei";
          font-size: 12px;
          font-weight: 400;
        }
      }
    }
  }

  .img_con {
    margin-right: 15px;
    margin-left: 15px;

    .title {
      width: 232px;
      margin-bottom: 8px;
    }

    .bg_color_current {
      border-color: #429DFD !important;
      cursor: pointer;
    }

    &.nav_bottom_img_con {
      height: 74px;
      margin-right: 16px;
      margin-left: 0;

      .title {
        width: 180px;
      }
    }
  }

  .del_sld_com_img {
    position: absolute;
    top: -8px;
    right: -8px;
    margin-left: 20px;
    cursor: pointer;
  }
}
</style>
