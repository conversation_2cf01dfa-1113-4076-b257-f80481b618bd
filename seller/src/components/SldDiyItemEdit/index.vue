<!-- 链接选择器选择之后渲染页面 -->
<template>
  <div v-if="formData.url_type == 'url'" class="sub_part" :style="{ borderBottomWidth: 0 }">
    <div class="subtitle" :class="showBorderTop ? 'subBorderTop' : ''">链接地址</div>
    <FormItem
      :name="`link_url_`+index"
    >
      <Input
        :maxLength="250"
        :defaultValue="formData.url"
        placeholder="请输入链接地址"
        @change="(e) => onChange(e.target.value, 'url', index, undefined)"
        style="width: 300px"
      />
    </FormItem>
  </div>
  <div v-else-if="formData.url_type == 'keyword'" class="sub_part" :style="{ borderBottomWidth: 0 }">
    <div class="subtitle" :class="showBorderTop ? 'subBorderTop' : ''">关键词</div>
    <FormItem
      :name="`link_keyword_`+index"
    >
      <Input
        :maxLength="250"
        :defaultValue="formData.url"
        placeholder="请输入关键词"
        @change="(e) => onChange(e.target.value, 'url', index, undefined)"
        style="width: 300px"
      />
    </FormItem>
  </div>
  <div v-else-if="formData.url_type == 'goods'" class="sub_part" :style="{ borderBottomWidth: 0 }">
    <div class="subtitle" :class="showBorderTop ? 'subBorderTop' : ''">商品名称</div>
    <FormItem
      :name="`link_goods_`+index"
    >
      <Input
        :maxLength="250"
        :value="formData.info.goodsName"
        :disabled="true"
        style="width: 300px"
      />
    </FormItem>
  </div>
  <div v-else-if="formData.url_type == 'category'" class="sub_part" :style="{ borderBottomWidth: 0 }">
    <div class="subtitle" :class="showBorderTop ? 'subBorderTop' : ''">分类名称</div>
    <FormItem
      :name="`link_category_`+index"
    >
      <Input
        :maxLength="250"
        :value="formData.info.categoryName !== undefined
          ? formData.info.categoryName : formData.info.labelName"
        :disabled="true"
        style="width: 300px"
      />
    </FormItem>
  </div>
  <div v-else-if="formData.url_type == 'topic'" class="sub_part" :style="{ borderBottomWidth: 0 }">
    <div class="subtitle" :class="showBorderTop ? 'subBorderTop' : ''">专题名称</div>
    <FormItem
      :name="`link_topic_`+index"
    >
      <Input
        :maxLength="250"
        :value="formData.info.name"
        :disabled="true"
        style="width: 300px"
      />
    </FormItem>
  </div>
  <div v-else-if="formData.url_type == 'seckill'" class="sub_part" :style="{ borderBottomWidth: 0 }">
    <div class="subtitle" :class="showBorderTop ? 'subBorderTop' : ''">秒杀活动名称</div>
    <FormItem
      :name="`link_seckill_`+index"
    >
      <Input
        :maxLength="250"
        :value="formData.info.seckillName"
        :disabled="true"
        style="width: 300px"
      />
    </FormItem>
  </div>
  <div v-else-if="formData.url_type == 'store'" class="sub_part" :style="{ borderBottomWidth: 0 }">
    <div class="subtitle" :class="showBorderTop ? 'subBorderTop' : ''">店铺名称</div>
    <FormItem
      :name="`link_store_`+index"
    >
      <Input
        :maxLength="250"
        :value="formData.info.storeName"
        :disabled="true"
        style="width: 300px"
      />
    </FormItem>
  </div>
  <div v-else-if="formData.url_type == 'voucher'" class="sub_part" :style="{ borderBottomWidth: 0 }">
    <div class="subtitle" :class="showBorderTop ? 'subBorderTop' : ''">优惠券名称</div>
    <FormItem
      :name="`link_voucher_`+index"
    >
      <Input
        :maxLength="250"
        :value="formData.info.red_title"
        :disabled="true"
        style="width: 300px"
      />
    </FormItem>
  </div>
  <div v-else-if="formData.url_type == 'live'" class="sub_part" :style="{ borderBottomWidth: 0 }">
    <div class="subtitle" :class="showBorderTop ? 'subBorderTop' : ''">直播名称</div>
    <FormItem
      :name="`link_live_`+index"
    >
      <Input
        :maxLength="250"
        :value="formData.info.live_name"
        :disabled="true"
        style="width: 300px"
      />
    </FormItem>
  </div>
  <div v-else-if="formData.url_type == 'svideo'" class="sub_part" :style="{ borderBottomWidth: 0 }">
    <div class="subtitle" :class="showBorderTop ? 'subBorderTop' : ''">短视频名称</div>
    <FormItem
      :name="`link_svideo_`+index"
    >
      <Input
        :maxLength="250"
        :value="formData.info.videoName"
        :disabled="true"
        style="width: 300px"
      />
    </FormItem>
  </div>
  <div v-else-if="formData.url_type == 'draw'" class="sub_part" :style="{ borderBottomWidth: 0 }">
    <div class="subtitle" :class="showBorderTop ? 'subBorderTop' : ''">活动名称</div>
    <FormItem
      :name="`link_draw_`+index"
    >
      <Input
        :maxLength="250"
        :value="formData.info.drawName"
        :disabled="true"
        style="width: 300px"
      />
    </FormItem>
  </div>
</template>
<script setup>
  import { ref, computed, watch } from 'vue';
  import { Input } from 'ant-design-vue';
  
  const props = defineProps({
    data: {
      type: Object,
      default: {}
    },
    index: {
      type: Number,
      default: 0
    },
    showBorderTop: {
      type: Boolean,
      default: false
    },
  });

  const curFormData = ref({});

  const formData = computed(() => {
    return props.data;
  });

  watch(
    formData,
    () => {
      curFormData.value = props.data;
    },
    { deep: true, immediate: true },
  );

  const emit = defineEmits(['handleChange']);

  function onChange(val, type, tar_index = 0, parent_index = -1) {
    emit('handleChange', val, type, tar_index, parent_index);
  }
</script>
<style lang="less" scoped>
.subtitle {
  color: #343434;
  font-size: 14px;
  line-height: 40px;

  &.subBorderTop {
    border-top: 1px solid #eee;
  }
}
</style>