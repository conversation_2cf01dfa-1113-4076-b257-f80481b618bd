<!-- 魔方装修展示组件 -->
<template>
  <div
    :style="{
      background: data.color ? data.color : '#fff',
      paddingTop: data.top_margin + 'px',
      paddingBottom: data.bottom_margin + 'px',
    }">
    <div class="cube_display">
      <div
        class="cube_item"
        v-for="(item, index) in props.data.data"
        :key="index"
        :style="dynaStyle(item)"
      >
        <img :src="item.img.imgUrl" v-if="item.img.imgUrl" />
      </div>
    </div>
  </div>
</template>

<script setup>
  import { unref } from 'vue';

  const props = defineProps({
    data: {
      type: Object,
      default: () => ({
        data: [],
        size: 4,
      }),
    },
  });

  const dynaStyle = ({ areaInfo }) => {
    let { start, width, height } = areaInfo;

    let unitLength = 371 / unref(props.data.size);

    return {
      width: width * unitLength + 'px',
      height: height * unitLength + 'px',
      left: start.x * unitLength + 'px',
      top: start.y * unitLength + 'px',
    };
  };
</script>

<style lang="scss" scoped>
  .cube_display {
    position: relative;
    width: 371px;
    height: 371px;
    background: #eee;

    .cube_item {
      position: absolute;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
  }
</style>
