<!-- 
  魔方组件控制组件
-->
<template>
  <div class="layout" :style="{ width: layoutSize + 'px' }">
    <div class="cube-wrap">
      <CubeLayer v-bind="$props" @selected="selectPosition" ref="FloorCubeRef" />

      <!-- 选择区域后蒙层start -->
      <div>
        <div
          v-for="(item, index) in maskList"
          :key="index"
          class="mask_item"
          :style="getMaskStyle(item.areaInfo)"
          :class="{ focused: focusVar == item.uid }"
          @click="focusSelectedMask(item)"
        >
          <span v-show="!item.img.imgUrl">{{ getMaskText(item.areaInfo) }}</span>
          <img :src="item.img.imgUrl" alt="" v-show="item.img.imgUrl" class="cube_img" />
          <div class="close_icon" @click.stop="removeMask(index)">
            <AliSvgIcon iconName="iconshanchu3" width="14px" height="14px" fillColor="#666" />
          </div>
        </div>
      </div>
      <!-- 选择区域后蒙层end -->
    </div>
  </div>
</template>

<script setup>
  import { inject, ref, unref, watch } from 'vue';
  import SvgIcon from '/@/components/SvgIcon/index.vue';
  import lodash from 'lodash-es';
  import CubeLayer from './CubeLayer.vue';

  const props = defineProps({
    layoutSize: {
      type: Number,
      default: 350,
    },

    cubeSize: {
      type: Number,
      default: 4,
    },

    list: {
      type: Array,
      default: [],
    },
  });

  //切换魔方密度时重置列表
  watch(
    () => props.cubeSize,
    () => {
      resetAllMask();
    },
  );

  const FloorCubeRef = ref();

  const emit = defineEmits(['remove']);

  const editorProvider = inject('editorProvider');

  const focusVar = ref(-1);
  const maskList = ref(props.list);

  //点击叉号删除目标蒙层
  const removeMask = (index) => {
    let mask = unref(maskList)[index];
    let { start, end } = mask.areaInfo;
    unref(FloorCubeRef).clearTargetArea(start, end);
    editorProvider.getCurrentCube({ content: {}, img: {} });
    maskList.value.splice(index, 1);
  };

  //CubeLayer在选择区域的回调，蒙层列表插入对应蒙层
  const selectPosition = (areaInfo) => {
    insertMask(areaInfo);
  };

  //点击目标蒙层事件
  const focusSelectedMask = (item) => {
    focusVar.value = item.uid;
    editorProvider.getCurrentCube(item);
  };

  //生成UID，根据每个蒙层的起始坐标和长宽数值组成，始终唯一
  const formUidBySelectPosition = (start, width, height) => {
    let startCom = Object.values(start).join('');
    return `${startCom}${width}${height}`;
  };

  //插入蒙层
  const insertMask = (areaInfo) => {
    let { width, height, start } = areaInfo;
    let maskItem = {
      uid: formUidBySelectPosition(start, width, height),
      areaInfo,
      img: {
        imgUrl: '',
        imgPath: '',
        width: 0,
        height: 0,
      },
      content: {
        link_type: '',
        link_value: '',
        info: {},
      },
    };
    // @ts-ignore
    maskList.value.push(lodash.cloneDeep(maskItem));
    focusSelectedMask(maskItem);
  };

  //蒙层尺寸文本
  const getMaskText = ({ width, height }) => {
    let { layoutSize, cubeSize } = props;
    let unitLength = Number(layoutSize / cubeSize);
    let maskWidth = width * unitLength;
    let maskHeight = height * unitLength;
    return `${maskWidth} x ${maskHeight}`;
  };

  //蒙层样式
  const getMaskStyle = ({ width, height, start }) => {
    let { layoutSize, cubeSize } = props;
    let unitLength = Number(layoutSize / cubeSize);
    let maskWidth = width * unitLength;
    let maskHeight = height * unitLength;
    let maskTop = start.y * unitLength;
    let maskLeft = start.x * unitLength;
    return {
      top: maskTop + 'px',
      left: maskLeft + 'px',
      width: maskWidth + 'px',
      height: maskHeight + 'px',
    };
  };

  //Editor父组件在对某个蒙层编辑时，走次方法
  const setMaskInfo = (info) => {
    let maskItem = unref(maskList).find((mask) => mask.uid === info.uid);
    Object.keys(info).forEach((key) => {
      maskItem[key] = info[key];
    });
    editorProvider.emitMaskList(unref(maskList));
  };

  //清空蒙层列表
  const resetAllMask = () => {
    maskList.value = [];
  };

  defineExpose({
    insertMask,
    resetAllMask,
    setMaskInfo,
  });
</script>

<style lang="less" scoped>
  .cube-wrap {
    position: relative;
  }

  .mask_item {
    display: flex;
    position: absolute;
    align-items: center;
    justify-content: center;
    background: #e8f7fd;
    background-repeat: no-repeat !important;
    background-position: center !important;
    background-size: cover !important;
    color: #88c4dc;
    font-size: 12px;
    text-align: center;

    .cube_img {
      width: 98%;
      height: 98%;
      object-fit: contain;
    }

    &.focused {
      box-shadow: 1px 1px #0f36ff inset, -1px -1px #0f36ff inset;
    }

    &:hover {
      .close_icon {
        display: block;
      }
    }

    .close_icon {
      display: none;
      position: absolute;
      z-index: 10;
      top: -5px;
      right: -5px;
      height: 14px;
      border-radius: 50%;
      background-color: #fff;
      cursor: pointer;
    }
  }
</style>
