<template>
    <Modal destroyOnClose :maskClosable="false" :width="500" :title="L('申请经营类目')" v-if="visible" :visible="true"
        :confirmLoading="confirmBtnLoading" @cancel="handleCancle" @ok="handleConfirm">
        <div style="margin: 10px;max-height: 460px;overflow-y: auto;">
            <Tree checkable :tree-data="applyCategoryList"
                :fieldNames="{ children: 'children', title: 'categoryName', key: 'categoryId' }"
                :selectedKeys="[]"
                @check="treeCheck">
                <template #title="{ categoryName, grade, scaling }">
                    <div class="category-item-content">
                        <span>{{ categoryName }}</span>
                        <div v-if="grade === 3" class="commission-rate">
                            <span class="commission-label">类目服务费</span>
                            <span :class="getScalingClass(scaling)">{{ formatScaling(scaling) }}</span>
                        </div>
                    </div>
                </template>
            </Tree>
        </div>
    </Modal>
</template>
<script lang="ts">
export default {
    name: 'SgAddCategory',
};
</script>

<script lang="ts" setup>

import {
applyBindStoreCategory,
} from '@/api/decorate/deco';
import { getApplyCategoryApi } from '@/api/settled/settled';
import { failTip, sucTip } from '@/utils/utils';
import { Modal, Tree } from 'ant-design-vue';
import { getCurrentInstance, ref, h } from 'vue';
import { useRouter } from 'vue-router';

const applyCategorySelect = ref<string[]>([]); //已选经营类目数据
const applyCategoryList = ref([]); //经营类目数据
const vm = getCurrentInstance();
const L = vm?.appContext.config.globalProperties.$sldComLanguage;
const props = defineProps({
    visible: { type: Boolean, required: true }, // 控制模态框的显示
    closeCb: { type: Function, default: () => { } }, // 关闭模态框的回调函数
});
const confirmBtnLoading = ref(false);
const router = useRouter();
const treeCheck = (checkedKeys: any, e: { checkedNodes: any[]; }) => {
    let select_cat_id: string[] = [];
    if (e.checkedNodes.length > 0) {
        e.checkedNodes.map(item_one => {
            if (item_one.grade == 3) {
                let tmp_data = item_one.path.split('/');
                select_cat_id.push(`${tmp_data[1]}-${tmp_data[2]}-${item_one.categoryId}`);
            }
        })
    }
    applyCategorySelect.value = select_cat_id;
}

const get_bind_category_list = async () => {
    const res = await getApplyCategoryApi();
    console.log(res);
    if (res.state == 200) {
        applyCategoryList.value = res.data;
    }
};

get_bind_category_list();
const handleConfirm = async () => {
    if (applyCategorySelect.value.length == 0) {
        failTip(L('请选择经营类目'));
        return;
    }
    confirmBtnLoading.value = true;
    const res = await applyBindStoreCategory({ goodsCateIds: applyCategorySelect.value.join(',') });
    confirmBtnLoading.value = false;
    if (res?.state == 200) {
        sucTip(res.msg + '自动通过!');
        handleCancle();
        router.push({
            path: '/goods/goods_list_to_add',
            query: {
                // 用于强制刷新组件，添加随机数避免缓存
                refresh: new Date().getTime()
            }
        })
    } else {
        failTip(res.msg);
    }
};
const handleCancle = () => {
    applyCategorySelect.value = [];
    props.closeCb && props.closeCb();
};

const formatScaling = (scaling: number | undefined) => {
    if (scaling === undefined || scaling === null) return '0%';
    return `${(scaling * 100).toFixed(1)}%`;
};

const getScalingClass = (scaling: number | undefined) => {
    if (scaling === undefined || scaling === null) return '';
    const percentage = scaling * 100;
    if (percentage <= 10) return 'scaling-green';
    if (percentage <= 20) return 'scaling-orange';
    return 'scaling-red';
};

</script>

<style lang="less" scoped>
.category-item-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.commission-rate {
    display: flex;
    align-items: center;
    gap: 4px;
    margin-left: 16px;
}

.commission-label {
    color: #999;
    font-size: 12px;
}

.scaling-green {
    color: #52c41a;
}

.scaling-orange {
    color: #faad14;
}

.scaling-red {
    color: #ff4d4f;
}
</style>