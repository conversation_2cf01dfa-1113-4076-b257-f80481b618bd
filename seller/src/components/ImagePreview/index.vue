<template>
  <div class="image-preview-wrapper">
    <template v-if="imageList && imageList.length > 0">
      <div class="image-item">
        <div class="image-container">
          <img :src="imageList[0].imageUrl" :alt="imageList[0].image" class="preview-img">
          <div class="hover-actions">
            <EyeOutlined class="action-icon" @click="handlePreview(imageList[0].imageUrl)" />
            <DeleteOutlined class="action-icon" @click="handleDelete(0)" />
          </div>
        </div>
      </div>
    </template>
    
    <template v-else>
      <Upload
        :action="`${apiUrl}${uploadApi}`"
        :show-upload-list="false"
        accept=".jpg,.jpeg,.png,.gif"
        :headers="headers"
        name="file"
        withCredentials
        @change="handleUploadChange"
      >
        <div class="upload-entry" v-if="!uploading">
          <PlusOutlined />
          <div class="upload-text">上传图片</div>
        </div>
        <div class="upload-progress" v-else>
          <Progress 
            type="circle" 
            :percent="uploadPercent" 
            :width="80"
            :stroke-color="{'0%': '#ff7e28', '100%': '#ff4d4f'}"
          />
        </div>
      </Upload>
    </template>
  </div>
  
  <Modal
    v-model:visible="previewVisible"
    :footer="null"
    @cancel="handleCancel"
    width="800px"
  >
    <img :src="previewImage" style="width: 100%" />
  </Modal>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue';
import { EyeOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { Modal, Upload, Progress } from 'ant-design-vue';
import { useGlobSetting } from '/@/hooks/setting';
import type { UploadChangeParam } from 'ant-design-vue';
import { useUserStoreWithOut } from '/@/store/modules/user';

interface ImageItem {
  image: string;
  imageUrl: string;
}

export default defineComponent({
  name: 'ImagePreview',
  components: {
    EyeOutlined,
    DeleteOutlined,
    PlusOutlined,
    Modal,
    Upload,
    Progress
  },
  props: {
    imageList: {
      type: Array as () => ImageItem[],
      required: true
    },
    uploadApi: {
      type: String,
      default: '/v3/oss/seller/upload?source=logo'
    }
  },
  emits: ['delete', 'upload-success'],
  setup(props, { emit }) {
    const { apiUrl } = useGlobSetting();
    const userStore = useUserStoreWithOut();
    const previewVisible = ref(false);
    const previewImage = ref('');
    const uploading = ref(false);
    const uploadPercent = ref(0);

    // 修改获取认证信息的方式
    const headers = computed(() => {
      const token = userStore.getToken;
      return {
        'Authorization': token ? `Bearer ${token}` : '',
        'X-Requested-With': 'XMLHttpRequest'
      };
    });

    const handlePreview = (imageUrl: string) => {
      previewImage.value = imageUrl;
      previewVisible.value = true;
    };

    const handleDelete = (index: number) => {
      emit('delete', index);
    };

    const handleCancel = () => {
      previewVisible.value = false;
    };

    const handleUploadChange = (info: UploadChangeParam) => {
      const { file } = info;
      
      if (file.status === 'uploading') {
        uploading.value = true;
        uploadPercent.value = Math.round(file.percent || 0);
      } else if (file.status === 'done') {
        uploading.value = false;
        uploadPercent.value = 100;
        const response = file.response;
        if (response.state === 200 && response.data) {
          emit('upload-success', {
            image: response.data.path,
            imageUrl: response.data.url,
          });
        }
      } else if (file.status === 'error') {
        uploading.value = false;
        uploadPercent.value = 0;
      }
    };

    return {
      apiUrl,
      headers,
      previewVisible,
      previewImage,
      uploading,
      uploadPercent,
      handlePreview,
      handleDelete,
      handleCancel,
      handleUploadChange,
    };
  }
});
</script>

<style lang="less" scoped>
.image-preview-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;

  .image-item {
    position: relative;
    
    .image-container {
      position: relative;
      width: 100px;
      height: 100px;
      border: 1px solid #e8e8e8;
      border-radius: 2px;
      padding: 4px;
      background: #fff;
      
      transition: all 0.3s;
      
      &:hover {
        box-shadow: 0 2px 8px rgba(255, 77, 79, 0.09);
        
        .hover-actions {
          display: flex;
        }
      }

      .preview-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 2px;
      }

      .hover-actions {
        display: none;
        position: absolute;
        top: 4px;
        left: 4px;
        right: 4px;
        bottom: 4px;
        background: rgba(0, 0, 0, 0.5);
        justify-content: center;
        align-items: center;
        gap: 10px;
        border-radius: 2px;

        .action-icon {
          color: white;
          font-size: 18px;
          cursor: pointer;
          padding: 6px;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.2);
          transition: all 0.3s;
          
          &:hover {
            color: #ff4d4f;
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
          }
        }
      }
    }
  }

  .upload-progress {
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fafafa;
    border: 1px dashed #d9d9d9;
    border-radius: 2px;

    :deep(.ant-progress-text) {
      color: #ff4d4f;
    }
  }

  .upload-entry {
    width: 100px;
    height: 100px;
    border: 1px dashed #d9d9d9;
    border-radius: 2px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    transition: all 0.3s;
    background: #fafafa;
    padding: 4px;

    &:hover {
      border-color: #ff4d4f;
      background: #fff1f0;

      .anticon {
        color: #ff4d4f;
      }

      .upload-text {
        color: #ff4d4f;
      }
    }

    .anticon {
      font-size: 24px;
      color: #999;
      transition: color 0.3s;
    }

    .upload-text {
      margin-top: 8px;
      color: #666;
      font-size: 12px;
      transition: color 0.3s;
    }
  }
}

// 预览图片的模态框样式
:deep(.ant-modal-content) {
  padding: 8px;
  background: #fff;
  
  .ant-modal-body {
    padding: 0;
    
    img {
      display: block;
      max-width: 100%;
      margin: 0 auto;
    }
  }

  .ant-modal-close {
    color: #999;
    transition: color 0.3s;

    &:hover {
      color: #666;
    }
  }
}
</style> 