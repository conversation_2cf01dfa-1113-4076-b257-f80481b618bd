<template>
  <svg
    class="svgIcon"
    aria-hidden="true"
    :style="{ width: width, height: height, fill: computedFillColor, ...extra }"
  >
    <use :xlink:href="'#' + iconName" />
  </svg>
</template>

<script>
  import setting from '@/settings/projectSetting';
  import { computed } from 'vue';
  export default {
    name: 'AliSvgIcon',
    props: {
      iconName: {
        required: true,
        type: String,
      },
      width: {
        type: String,
        default: '20px',
      },
      height: {
        type: String,
        default: '20px',
      },
      fillColor: {
        type: String,
        default: '#fff',
      },
      extra: {
        type: Object,
        default: {},
      }
    },
    setup(props) {
      const computedFillColor = computed(() => {
        if (props.fillColor == 'primaryColor') {
          return setting.themeColor;
        } else {
          return props.fillColor;
        }
      });

      return { computedFillColor };
    },
  };
</script>

<style lang="scss" scoped></style>
