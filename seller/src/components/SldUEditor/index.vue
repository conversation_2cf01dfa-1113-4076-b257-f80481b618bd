<template>
  <div :id="props.id" name="content" type="text/plain" style="width: 100%"></div>
</template>
<script setup>
import { computed, watch, onMounted, onUnmounted, ref } from 'vue';
import { useGlobSetting } from '/@/hooks/setting';
import { getToken } from '/@/utils/auth';

const { apiUrl } = useGlobSetting();
const props = defineProps({
  id: { type: String, required: true, default: '' }, //编辑器id-唯一
  initEditorContent: { type: String, require: true, default: '' }, //编辑器内容
  getContentFlag: { type: Boolean, require: true, default: false }, //编辑器内容加载
  getEditorContent: { type: Function, require: true, default: () => null }, //编辑器内容变更事件
});

const imgToken = ref(getToken() || ''); //上传文件token参数

  const contentFlag = computed(() => {
    return props.getContentFlag;
  });

  // 全局修复UEditor的语言问题
  (function() {
    if (typeof window !== 'undefined') {
      // 在全局脚本中等待UEditor加载
      function checkAndFixUE() {
        if (window['UE']) {
          // 修复UEditor的getLang方法
          if (window['UE'].Editor && window['UE'].Editor.prototype) {
            // 确保lang属性存在
            window['UE'].Editor.prototype.lang = 'zh-cn';
            
            // 保存原始getLang方法
            const originalGetLang = window['UE'].Editor.prototype.getLang;
            
            // 重写getLang方法，增加容错处理
            window['UE'].Editor.prototype.getLang = function(path) {
              try {
                // 确保lang和I18N属性存在
                this.lang = this.lang || 'zh-cn';
                window['UE'].I18N = window['UE'].I18N || {};
                window['UE'].I18N[this.lang] = window['UE'].I18N[this.lang] || {};
                
                // 尝试使用原始方法
                if (originalGetLang) {
                  try {
                    return originalGetLang.call(this, path);
                  } catch (err) {
                    // 原始方法失败时不抛出错误
                  }
                }
                
                // 兜底实现
                if (!path) return window['UE'].I18N[this.lang];
                
                const pathArray = path.split('.');
                let current = window['UE'].I18N[this.lang];
                
                for (let i = 0; i < pathArray.length; i++) {
                  if (!current) return path;
                  current = current[pathArray[i]];
                }
                
                return current || path;
              } catch (e) {
                console.warn('UEditor getLang error:', e);
                return path || '';
              }
            };
          }
        } else {
          // UE还没加载，继续等待
          setTimeout(checkAndFixUE, 100);
        }
      }
      
      // 开始检查
      checkAndFixUE();
    }
  })();

  watch(
    contentFlag,
    () => {
      if (props.getContentFlag) {
        let con = window['UE'].getEditor(props.id).getContent();
        if(con && con.indexOf("&quot;Microsoft YaHei&quot;")!== -1){
          con = con.replace(/&quot;Microsoft YaHei&quot;/g,"Microsoft YaHei");
        }
        if(con && con.indexOf("&#39;Microsoft YaHei&#39;")!== -1){
          con = con.replace(/&#39;Microsoft YaHei&#39;/g,"Microsoft YaHei");
        }
        props.getEditorContent(con);
      }
    },
    { deep: true },
  );

  function initEditor() {
    // 修复UEditor中getLang无法获取lang的错误
    if (window['UE']) {
      // 确保I18N对象存在并包含必要的语言条目
      if (!window['UE'].I18N) {
        window['UE'].I18N = {};
      }
      
      // 设置中文语言包
      window['UE'].I18N['zh-cn'] = {
        // 添加必要的语言条目
        'labelMap': {
          'anchor': '锚点',
          'undo': '撤销',
          'redo': '重做',
          'bold': '加粗',
          'indent': '首行缩进',
          'snapscreen': '截图',
          'insertcode': '代码语言',
          'fontfamily': '字体',
          'fontsize': '字号',
          'paragraph': '段落格式',
          'inserttable': '表格',
          'link': '超链接',
          'emotion': '表情',
          'insertimage': '图片',
          'fullscreen': '全屏',
          'lineheight': '行间距',
          'edittip': '编辑提示',
          'customstyle': '自定义标题',
          'autotypeset': '自动排版',
          'touppercase': '转为大写',
          'tolowercase': '转为小写'
        },
        'insertorderedlist': {
          'num': '1,2,3...',
          'num1': '1),2),3)...',
          'num2': '(1),(2),(3)...',
          'cn': '一,二,三....',
          'cn1': '一),二),三)....',
          'cn2': '(一),(二),(三)....'
        },
        'insertunorderedlist': {
          'circle': '○ 小圆圈',
          'disc': '● 小圆点',
          'square': '■ 小方块'
        },
        'paragraph': {
          'p': '段落',
          'h1': '标题 1',
          'h2': '标题 2',
          'h3': '标题 3',
          'h4': '标题 4',
          'h5': '标题 5',
          'h6': '标题 6'
        },
        'fontfamily': {
          'songti': '宋体',
          'kaiti': '楷体',
          'heiti': '黑体',
          'lishu': '隶书',
          'yahei': '微软雅黑',
          'andaleMono': 'andale mono',
          'arial': 'arial',
          'arialBlack': 'arial black',
          'comicSansMs': 'comic sans ms',
          'impact': 'impact',
          'timesNewRoman': 'times new roman'
        },
        'customstyle': {
          'tc': '标题居中',
          'tl': '标题居左',
          'im': '强调',
          'hi': '明显强调'
        },
        'elementPathTip': '元素路径',
        'wordCountTip': '字数统计',
        'wordCountMsg': '当前已输入{#count}个字符, 您还可以输入{#leave}个字符',
        'wordOverFlowMsg': '<span style="color:red;">你输入的字符个数已经超出最大允许值，服务器可能会拒绝保存！</span>',
        'ok': '确认',
        'cancel': '取消',
        'closeDialog': '关闭对话框',
        'tableDragTip': '表格拖动必须引入uiUtils.js文件！',
        'autofloatMsg': '工具栏浮动依赖编辑器UI，您首先需要引入UI文件!',
        'snapScreen_plugin': {
          'browserMsg': '仅支持IE浏览器！',
          'callBackErrorMsg': '服务器返回数据有误，请检查配置项之后重试。',
          'uploadErrorMsg': '截图上传失败，请检查服务器端环境! '
        },
        't_row': '行',
        't_col': '列',
        'more': '更多',
        'pasteOpt': '粘贴选项',
        'pasteSourceFormat': '保留源格式',
        'tagFormat': '只保留标签',
        'pasteTextFormat': '只保留文本',
        'autoTypeSet': {
          'mergeLine': '合并空行',
          'delLine': '清除空行',
          'removeFormat': '清除格式',
          'indent': '首行缩进',
          'alignment': '对齐方式',
          'imageFloat': '图片浮动',
          'removeFontsize': '清除字号',
          'removeFontFamily': '清除字体',
          'removeHtml': '清除冗余HTML代码',
          'pasteFilter': '粘贴过滤',
          'run': '执行',
          'symbol': '符号转换',
          'bdc2sb': '全角转半角',
          'tobdc': '半角转全角'
        },
        'background': {
          'static': {
            'lang_background_normal': '背景设置',
            'lang_background_local': '本地图片',
            'lang_background_set': '选项',
            'lang_background_none': '无',
            'lang_background_colored': '有背景色',
            'lang_background_color': '颜色设置',
            'lang_background_netimg': '网络图片',
            'lang_background_align': '对齐方式',
            'lang_background_position': '精确定位',
            'repeatType': {'options': ['居中', '横向重复', '纵向重复', '平铺', '自定义']}
          }
        },
        'insertimage': {
          'static': {
            'lang_tab_remote': '插入图片',
            'lang_tab_upload': '本地上传',
            'lang_tab_online': '在线管理',
            'lang_tab_search': '图片搜索',
            'lang_input_url': '地址',
            'lang_input_size': '大小',
            'lang_input_width': '宽度',
            'lang_input_height': '高度',
            'lang_input_border': '边框',
            'lang_input_vhspace': '边距',
            'lang_input_title': '描述',
            'lang_input_align': '图片浮动方式',
            'lang_imgLoading': '图片加载中……',
            'lang_start_upload': '开始上传',
            'lock': {'title': '锁定宽高比例'},
            'searchType': {'title': '图片类型', 'options': ['新闻', '壁纸', '表情', '头像']},
            'searchTxt': {'value': '请输入搜索关键词'},
            'searchBtn': {'value': '百度一下'},
            'searchReset': {'value': '清空搜索'},
            'noneAlign': {'title': '无浮动'},
            'leftAlign': {'title': '左浮动'},
            'rightAlign': {'title': '右浮动'},
            'centerAlign': {'title': '居中独占一行'}
          }
        },
        'webapp': {
          'tip1': '本功能由百度APP提供，如看到此页面，请各位站长首先申请百度APPKey！',
          'tip2': '申请完成之后请至ueditor.config.js中配置获得的appkey! ',
          'applyFor': '点此申请',
          'anthorApi': '百度API'
        },
        'template': {
          'static': {
            'lang_template_bkcolor': '背景颜色',
            'lang_template_clear': '保留原有内容',
            'lang_template_select': '选择模板'
          }
        },
        'anchor': '锚点',
        'clearColor': '清除颜色',
        'standardColor': '标准颜色',
        'themeColor': '主题颜色',
        'property': '属性',
        'default': '默认',
        'modify': '修改',
        'justifyleft': '左对齐',
        'justifyright': '右对齐',
        'justifycenter': '居中',
        'justify': '对齐方式',
        'clear': '清除',
        'anchorMsg': '锚点',
        'delete': '删除',
        'clickToUpload': '点击上传',
        'unset': '尚未设置语言文件',
        'confirmClear': '确定清空当前文档么？',
        'contextMenu': {
          'delete': '删除',
          'selectall': '全选',
          'deletecode': '删除代码',
          'cleardoc': '清空文档',
          'copy': '复制(Ctrl + c)',
          'cut': '剪切(Ctrl + x)',
          'paste': '粘贴(Ctrl + v)',
          'insertparagraphbefore': '前插入段落',
          'insertparagraphafter': '后插入段落',
          'highlightcode': '复制代码',
          'print': '打印',
          'preview': '预览',
          'horizontal': '分隔线',
          'removeformat': '清除格式',
          'time': '时间',
          'date': '日期',
          'inserttable': '表格',
          'paragraph': '段落',
          'fontfamily': '字体',
          'fontsize': '字号',
          'forecolor': '字体颜色',
          'backcolor': '背景色',
          'table': '表格',
          'justifyleft': '居左对齐',
          'justifycenter': '居中对齐',
          'justifyright': '居右对齐',
          'justifyjustify': '两端对齐',
          'aligntd': '单元格对齐',
          'aligntable': '表格对齐',
          'alignJustifyleft': '左对齐',
          'alignJustifycenter': '居中对齐',
          'alignJustifyright': '右对齐',
          'alignJustifyjustify': '两端对齐'
        },
        'copymsg': '浏览器不支持,请使用 Ctrl+c 复制',
        'pastemsg': '浏览器不支持,请使用 Ctrl+v 粘贴'
      };
    }

    const ueEditor = window['UE'].getEditor(props.id, {
      serverUrl: `${apiUrl}/v3/oss/ueditor/upload?configPath=config.json&token=${imgToken.value}`,
    });
    ueEditor.ready((editor) => {
      if (!editor) {
        window['UE'].delEditor(props.id);
        initEditor();
      } else {
        if (props.initEditorContent) {
          window['UE'].getEditor(props.id).setContent(props.initEditorContent);
        }
      }
    });
  }

  onMounted(() => {
    initEditor();
  });

  onUnmounted(() => {
    window['UE'].delEditor(props.id);
  });
</script>
