<template>
  <div>
    <Modal
      destroyOnClose
      :maskClosable="false"
      :title="props.title"
      :visible="modalVisible"
      :zIndex="props.zIndex"
      :width="props.width"
      @ok="sldConfirm"
      :footer="props.show_foot ? undefined : null"
      @cancel="sldCancle"
    >
      <Form ref="formRef" :model="data">
        <ShowMoreHelpTip :tipData="modal_tip" :marginTop="0" v-if="modal_tip.length>0"></ShowMoreHelpTip>
        <div class="SldDiyTitleLinkModal">
          <Table
            :showHeader="false"
            :columns="columns"
            :bordered="true"
            :pagination="false"
            :dataSource="data.data"
            :maxHeight="400"
            :canResize="false"
          >
            <template #bodyCell="{ column, text, record,index }">
              <template v-if="column.dataIndex == 'name'">
                <div class="table_left_con">
                  <span>{{ text }}</span>
                  <span v-if="record.required!=undefined&&record.required" class="table_left_require">
                    *
                  </span>
                </div>
              </template>
              <template v-if="column.dataIndex == 'type'">
                <template v-if="record.type == 'input'">
                  <Form.Item
                    validateFirst
                    style="width: 300px;"
                    :name="['data',index,`${record.key}`]"
                    :rules="[{
                    required: record.required,
                    whitespace: true,
                    message: `请输入${record.name}`,
                  },{
                    validator: async (rule, value) => {
                      await validatorEmoji(rule, value);
                    },
                  },]"
                  >
                    <Input
                      :maxlength="props.extra!=undefined&&props.extra['input_limit']!=undefined&&props.extra['input_limit'] ? props.extra['input_limit'] : 250"
                      :placeholder="'请输入'+record.name"
                      style="width: 300px"
                      v-model:value="record[record.key]"
                    />
                  </Form.Item>       
                </template>
                <template v-if="record.type == 'link_type'">
                  <Select
                    placeholder="请选择链接类型"
                    style="width: 120px"
                    :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                    v-model:value="record.value"
                    @change="(e)=>sldHandSeleChange(e,record.key)"
                  >
                    <Select.Option
                      v-for="(item, index) in diy_link_type()"
                      :key="index"
                      :value="item.key"
                      >{{ item.name }}</Select.Option
                    >
                  </Select>
                </template>
                <template v-if="record.type == 'url'">
                  <Form.Item
                    validateFirst
                    style="width: 300px;"
                    :name="['data',index,'link_value']"
                    :rules="[{
                    required: record.required,
                    whitespace: true,
                    message: `请输入链接地址`,
                  },{
                    validator: async (rule, value) => {
                      await validatorEmoji(rule, value);
                    },
                  },]"
                  >
                    <Input
                      :maxlength="250"
                      style="width: 300px"
                      placeholder="请输入链接地址"
                      v-model:value="record.link_value"
                    />
                  </Form.Item>   
                </template>
                <template v-if="record.type == 'keyword'">
                  <Form.Item
                    validateFirst
                    style="width: 300px;"
                    :name="['data',index,'link_value']"
                    :rules="[{
                    required: record.required,
                    whitespace: true,
                    message: `请输入关键字`,
                  },{
                    validator: async (rule, value) => {
                      await validatorEmoji(rule, value);
                    },
                  },]"
                  >
                    <Input
                      :maxlength="250"
                      style="width: 300px"
                      placeholder="请输入关键字"
                      v-model:value="record.link_value"
                    />
                  </Form.Item>   
                </template>
                <template  v-if="record.type == 'goods'">
                  <div>
                    <span>{{record.value}}</span>
                  </div>
                </template>
                <template  v-if="record.type == 'category'">
                  <div>
                    <span>{{record.value}}</span>
                  </div>
                </template>
                <template  v-if="record.type == 'topic'">
                  <div>
                    <span>{{record.value}}</span>
                  </div>
                </template>
              </template>
            </template>
          </Table>
        </div>
      </Form>
    </Modal>

     <!-- 链接选择弹窗 - start -->
    <SldSelGoodsSingleDiy
      client="pc"
      :link_type="link_type"
      :modalVisible="modalVisibleSelLink"
      @confirm-event="seleSku"
      @cancle-event="sldHandleLinkCancle"
    />
    <!-- 链接选择弹窗 - end -->
  </div>
</template>
<script>
  export default {
    name: 'SldDiyMoreTitleLinkModal',
  };
</script>
<script setup>
  import { ref, onMounted,computed,watch } from 'vue';
  import { useRouter } from 'vue-router';
  import { sucTip, failTip,isEmptyObject,diy_link_type } from '/@/utils/utils';
  import { Modal,Form,Table,Input,Select } from 'ant-design-vue';
  import ShowMoreHelpTip from '/@/components/ShowMoreHelpTip/index.vue';
  import { validatorEmoji } from '/@/utils/validate';
  import SldSelGoodsSingleDiy from '@/components/SldSelGoodsSingleDiy/index.vue';

  const emit = defineEmits(['cancleEvent', 'confirmEvent']);

  const props = defineProps({
    width: { type: Number, default: 416 }, //弹框宽度，默认416px
    title: { type: String, required: true }, //弹框标题
    submiting: { type: Boolean, default: false }, //弹框确认按钮loading属性，默认不显示
    modalVisible: { type: Boolean, required: true }, //弹框是否显示
    content: { type: Array, default: [] },//内容
    modal_tip: { type: Array },//提示语
    client: { type: String, default: '' }, 
    type: { type: String, default: '' }, 
    extra: { type: Object }, //表单数据
    zIndex: { type: Number, default: 1000 }, //弹框的层级，默认为1000，最小值也为1000
    show_foot: { type: Boolean, default: true }, //是否显示弹框底部操作按钮，默认显示

  });

  //由于watch无法监听props.modalVisible，所以需要计算属性转为ref对象
  const modalVisible = computed(() => {
    return props.modalVisible;
  });
  const modal_tip = computed(() => {
    return props.modal_tip;
  });
  
  const formRef = ref()
  const source = ref('')
  const link_type = ref('')

  const modalVisibleSelLink = ref(false)
  //提示语
  //数据
  const data = ref({
    data:[
      {
        key:'link_type',
        name:`操作`,
        value:'',
        type:'link_type',
      }
    ]
  })
  
  const columns = ref([
  { dataIndex: 'name', width: 150,align : 'right' , },
  { dataIndex: 'type',align : 'left' , },
  ])

  const first_flag = ref(false)
  const operate_info = ref({}) //操作下拉框选择事件的信息

  watch(modalVisible, () => {
    if (modalVisible.value) {
      if(!first_flag.value&&!isEmptyObject(props.content)){
        let ope_data = JSON.parse(JSON.stringify(props.content));
        let tmp_info = [];
        ope_data.forEach((item,index)=>{
          if(item.title!=undefined){
            let obj = {
              key: item.title.name,
              name: item.title.label,
              value: item.title.initialValue,
              type: 'input',
              required:item.title.required,
            }
            obj[item.title.name] = item.title.initialValue
            tmp_info.push(obj);
          }
          tmp_info.push({
            key: 'link_type_'+index,
            name: `操作`,
            value: item.link_type,
            type: 'link_type',
          });
          if(item.link_value!=undefined&&item.link_value) {
            let tmp_info_new = {
              key: 'link_value_' + index,
              name: `关键字`,
              value: item.link_value,
              link_value:item.link_value,
              type: item.link_type,
              info: item.info != undefined ? item.info : {},
              required: true,
            }
            if (item.link_type == 'url') {
              tmp_info_new.name = `链接地址`;
              tmp_info_new.required = true;
            } else if (item.link_type == 'keyword') {
              tmp_info_new.name = `关键字`;
              tmp_info_new.required = true;
            } else if (item.link_type == 'goods') {
              tmp_info_new.name = `商品名称`;
              tmp_info_new.required = false;
            } else if (item.link_type == 'category') {
              tmp_info_new.name = `分类名称`;
              tmp_info_new.required = false;
            }
            tmp_info.push(tmp_info_new);
          }
        });
        data.value.data = tmp_info
        source.value = props.content.source!=undefined?props.content.source:''
      }
    }
  });

  //操作类型选择事件
  const sldHandSeleChange = (val,key)=> {
    let flag = true;
    let key_index = 0;//当前操作数据的key所属的index（父组件里面的index）
    let ley_array = key.split('_');
    key_index = ley_array[ley_array.length-1];
    
    let target_data = data.value.data.filter(item=>item.key == key);
    target_data[0].value = val;
    target_data = data.value.data.filter(item=>item.key != ('link_value_'+key_index));
    let cur_link_type_index = data.value.data.findIndex((item)=>{
      return item.key == key
    })

    operate_info.value = {
      link_type_key:key,
      link_type_parent_index:key_index,
      link_value_index:cur_link_type_index+1,
    };//操作下拉框选择事件的信息

    let temp = {};
    let cur_key = 'link_value_'+key_index;

    if(val == 'url'){
      temp = {
        key:cur_key,
        name:`链接地址`,
        value:'',
        type:'url',
        required:true,
      }
      flag = false
      target_data.splice(cur_link_type_index+1,0,temp);
    }else if(val == 'keyword'){
      temp = {
        key:cur_key,
        name:`关键字`,
        value:'',
        type:'keyword',
        required:true,
      }
      flag = false
      target_data.splice(cur_link_type_index+1,0,temp);
    }else if(val == 'goods'){
      temp = {
        key:cur_key,
        name:`商品名称`,
        value:'',
        info:{},
        type:'goods',
        required:true,
      }
      target_data.splice(cur_link_type_index+1,0,temp);
    }else if(val == 'category'){
      temp = {
        key:cur_key,
        name:`分类名称`,
        value:'',
        info:{},
        type:'category',
        required:true,
      }
      target_data.splice(cur_link_type_index+1,0,temp);
    }else if(val == 'topic'){
      temp = {
        key:cur_key,
        name:`专题名称`,
        value:'',
        info:{},
        type:'topic',
        required:true,
      }
      target_data.splice(cur_link_type_index+1,0,temp);
    }else{
      flag = false
    }
    
    data.value.data = JSON.parse(JSON.stringify(target_data))
    link_type.value = val
    modalVisibleSelLink.value = flag;
  }

  const sldConfirm = ()=> {
    formRef.value
      .validate()
      .then((res) => {
        //将数据组装，返回给上级页面
        let target = [];
        let target_index = 1;
        data.value.data.map((item,index)=>{
          if(item.key.indexOf('title')>-1){
            target.push({
              title: {
                label: item.name,
                name: 'title'+target_index,
                initialValue: item[item.key],
                required: true,
              },
              link_type: '',
              link_value: '',
              info: {},
            })
            target_index++;
          }else{
            let key_array = item.key.split('_');
            let parent_index = key_array[key_array.length-1];
            if(item.key.indexOf('link_type')>-1){
              target[parent_index].link_type = item.value;
            }else{
              target[parent_index].link_value = item.link_value;
              target[parent_index].info = item.info ? item.info : {};
            }
          }
        });
        emit('confirmEvent',target);
      })
  }

  const sldCancle = ()=> {
    emit('cancleEvent');
  }

  //商品或分类选中事件
  const seleSku = (val)=> {
    let target = data.value.data.filter(item=>item.key == ('link_value_'+operate_info.value.link_type_parent_index))[0];
    if (target.type == 'goods') {
      if(Array.isArray(val)){
        target.value = val[0].goodsName;
        target.link_value = val[0].goodsName;
        target.info = val[0];
      }else{
        target.value = val.goodsName;
        target.link_value = val.goodsName;
        target.info = val;
      }
    } else if (target.type == 'category') {
      target.value = val.categoryName;
      target.link_value = val.categoryName;
      target.info = val;
    } else if (target.type == 'topic') {
      if (props.client == 'mobile') {
        target.value = val[0].name;
        target.link_value = val[0].name;
      } else {
        target.value = val[0].decoName;
        target.link_value = val[0].decoName;
      }
      target.info = val;
    }
    link_type.value = ''
    modalVisibleSelLink.value = false
  }

  // 链接选择弹窗关闭
  const sldHandleLinkCancle = ()=> {
    link_type.value = '';
    data.value.data[operate_info.value.link_value_index-1].value = '';
    data.value.data = data.value.data.filter(item=>item.key != ('link_value_'+operate_info.value.link_type_parent_index));
    modalVisibleSelLink.value = false;
  }

  const router = useRouter();

  onMounted(() => {});
</script>
<style lang="less">
  .SldDiyTitleLinkModal{
    max-height: 400px;
    overflow: auto;
    .ant-form-item-with-help{
      margin-bottom: 0;
    }
    .ant-form-item{
      margin-bottom: 0;
    }
    .table_left_con {
      font-size: 13px;
      color: #333;
    }
    .table_left_require {
      color: red;
    }
  }
</style>
