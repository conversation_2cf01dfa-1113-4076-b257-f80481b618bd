// @import '~antd/lib/style/themes/default.less';


.tableListForm {
  :global {
    .ant-form-item {
      width: 250px!important;
      margin-bottom: 24px;
      margin-right: 0;
      display: flex;
      > .ant-form-item-label {
        width: auto;
        line-height: 31px;
        padding-right: 8px;
      }
      .ant-form-item-control {
        line-height: 31px;
      }
    }
    .ant-form-item-control-wrapper {
      flex: 1;
    }
    .ant-form-item-label{
      padding-right: 0 !important;
    }
    .ant-form-item-label>label{
      font-size: 13px !important;
      color: #555;
    }
    .ant-form-item-label>label:after{
      content: '' !important;
    }
    .ant-form  input{
      border-color: #E5E5E5;
      height: 31px !important;
    }
    .ant-btn span{
      font-size: 13px;
    }
    .ant-calendar-picker  span.ant-calendar-picker-input{
      padding-top: 0 !important;
    }
    .ant-calendar-picker .ant-calendar-range-picker-separator{
      padding-top: 4px;
    }
    .ant-input-group > .ant-input:first-child{
      border-right: 0;
    }

  }
  .submitButtons {
    display: block;
    white-space: nowrap;
    margin-bottom: 24px;
  }

  }
:global {
  .ant-select-dropdown-menu-item:hover, .ant-select-dropdown-menu-item-active {
    background: #3B8CFF !important;
    color: #fff;
  }
  .ant-input-group > .ant-input:first-child{
    border-right: 0;
  }
  .ant-input-group-addon{
    background: #fff;
  }
  .anticon svg{
    //color:#979899;
  }
  .ant-input-group-addon{
    padding: 0 9px;
  }
}

.input_after_ml {
  margin-left: 10px;
}

.flex_com_row_wrap{
  .item{
    width: 150px;
    margin-left: 3px;
  }
  display: flex;
  flex-direction: row;
  flex-wrap:wrap;
  :global {
    .ant-form-item {
      width: 250px !important;
      margin-bottom: 24px;
      margin-right: 0;
      display: flex;
      > .ant-form-item-label {
        width: auto;
        line-height: 31px;
        padding-right: 8px;
      }
      .ant-form-item-control {
        line-height: 31px;
      }
    }
  }
}
.SldExpressCom{
  .vben-basic-table{
    height: auto !important;
  }
  .ant-form-item-with-help .ant-form-item-explain{
    position: absolute;
    z-index: 2;
    top: -10px;
    right: 8%;
    min-height: 20px;
    background: #fff;
    font-size: 13px;
    text-align: left;
  }
}
