<template>
  <div class="SldExpressCom">
    <SldEditFormCom :search_data="base_info" :curForm_data="curFormData"></SldEditFormCom>
    <BasicTable
      rowKey="key"
      :columns="column"
      :dataSource="data_table"
      :pagination="false"
      :bordered="true"
      :canResize="false"
      :ellipsis="false"
      class="SldExpressCom_table"
      >
      <template #bodyCell="{ column, record, text, index }">
        <template v-if="column.dataIndex == 'action'">
          <Popconfirm placement="rightBottom" :title="L('确定删除该行吗？')"
            @confirm="remove(record.key)">
            <a href="javascript:;" style="margin:5px;display: inline-block;">
              <AliSvgIcon iconName="iconshanchu" width="20px" height="20px" fillColor="#FF711E" />
            </a>
          </Popconfirm>
        </template>
        <template v-if="column.dataIndex == 'deliver_areas'">
          <a href="javascript:;" style="color: #FF711E;" @click="combine_area_info(record.key, record.sele_area_id_array)">{{ text=='' ?  L('点击选择配送地区') : text}}</a>
        </template>
        <template v-if="column.dataIndex == 'trans_weight'||column.dataIndex == 'trans_fee'||column.dataIndex == 'trans_add_weight'||column.dataIndex == 'trans_add_fee'">
          <Form.Item
            :name="['data_table', index, column.dataIndex]"
            :rules="[
              {
                required: true,
                message: L('该项必填'),
              },
            ]"
          >
            <InputNumber
              :max="999999999"
              :min="0"
              :precision="2"
              v-model:value="record[column.dataIndex]"
            />
          </Form.Item>
        </template>
      </template>
    </BasicTable>
    <Button type="dashed"  :style="{ width: '100%', marginTop: '16px', marginBottom: '8px'}" @click="add_spec_table_item">
      <div class="flex_row_center_center">
        <PlusOutlined></PlusOutlined>
        <span style="margin-left:5px;">{{ L('添加数据') }}</span>
      </div>
    </Button>
    <SldModal
      :width="900"
      :title="L('选择地址')"
      :visible="modalVisible"
      :content="permissionList"
      :showFoot="true"
      :conType="'moreCheck'"
      :visible_one="visible_one"
      @cancle-event="sldHandleCancle"
      @confirm-event="sldHandleConfirmPer"
    />
  </div>
</template>
<script>
  export default {
    name: 'OrderService',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, onMounted ,computed,h,watch} from 'vue';
  import { BasicTable,useTable } from '/@/components/Table';
  import SldModal from '@/components/SldModal/index.vue';
  import SldEditFormCom from '/@/components/SldEditFormCom/SldEditFormCom.vue';
  import { Popconfirm,InputNumber,Button,Form } from 'ant-design-vue';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import areaData from '@/assets/json/area.json';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;


  const props = defineProps({
    // title: { type: String, required: true }, //弹框标题
    curForm_data: { type: Object }, //引用页面，用于方法内的判断处理
  });
  const emit = defineEmits([
    'saveSeleArea',
  ]);
  //由于watch无法监听props.content，所以需要计算属性转为ref对象
  const curFormData = computed(() => {
    return props.curForm_data;
  });



  const base_info = ref([
    {
      type: 'inputnum',
      label: L('首重/件'),
      name: 'trans_com_weight',
      placeholder: L('首重/件(kg/件)'),
      initialValue: '',
      precision: 2,
      step:1,
      min:0,
      max:9999,
      rules: [{
        required: true,
        message: L('该项必填'),
      }],
    }, {
      type: 'inputnum',
      label: L('首费(元)'),
      name: 'trans_com_fee',
      placeholder: L('首费(元)'),
      initialValue: '',
      precision: 2,
      step:2,
      min:0,
      max:9999,
      rules: [{
        required: true,
        message: L('该项必填'),
      }],
    }, {
      type: 'inputnum',
      label: L('续重/件'),
      name: 'trans_com_add_weight',
      placeholder: L('续重/件(kg/件)'),
      initialValue: '',
      precision: 2,
      step:1,
      min:0,
      max:9999,
      rules: [{
        required: true,
        message: L('该项必填'),
      }],
    }, {
      type: 'inputnum',
      label: L('续费(元)'),
      name: 'trans_com_add_fee',
      placeholder: L('续费(元)'),
      precision: 2,
      step:1,
      min:0,
      max:9999,
      initialValue: '',
      rules: [{
        required: true,
        message: L('该项必填'),
      }],
    }
  ])

  const addData = ref([
    {
      type: 'city_area_deliver',
      name: 'deliver_area',
      label: L('选择地区'),
      placeholder: L('请选择地区'),
      content: '',
      data: [],
    }
  ])
  const modalVisible = ref(false)//是否展示对话框
  const visible_one = ref(false)//是否展示对话框
  const area_data = ref(areaData)//地区数据
  const permissionList = ref([]); //地区数据
  const data_table = ref([]); //表格数据
  const cur_key = ref(-1); //表格数据
  
  const column = ref([
    {
      title: L('操作'),
      dataIndex: 'action',
      align: 'center',
      width: 70,
    },
    {
      title: L('配送地区'),
      dataIndex: 'deliver_areas',
      align: 'center',
      width: 300,
    },
    {
      title: L('首重/件(kg/元)'),
      dataIndex: 'trans_weight',
      align: 'center',
      width: 120,
    },
    {
      title: L('首费(元)'),
      dataIndex: 'trans_fee',
      align: 'center',
      width: 120,
    },
    {
      title: L('续重/件(kg/元)'),
      dataIndex: 'trans_add_weight',
      align: 'center',
      width: 120,
    },
    {
      title: L('续费(元)'),
      dataIndex: 'trans_add_fee',
      align: 'center',
      width: 120,
    },
  ])
  watch(
    curFormData,
    () => {
      setTimeout(()=>{
          data_table.value = curFormData.value.data_table
        })
    },
    { deep: true, immediate: true },
  );
  //添加一条数据
  const add_spec_table_item = ()=> {
    let key = curFormData.value.data_table.length + 1;
    const newData = curFormData.value.data_table
    newData.push({
      key: key,
      deliver_areas: '',
      trans_weight: 1,
      trans_fee: 1,
      trans_add_weight: 1,
      trans_add_fee: 1,
      sele_area_id_array: [],
    });
    curFormData.value.data_table.forEach((item,index)=>{
      item.key = index+1
    })
  }

  // 删除数据
  const remove = (key)=> {
    curFormData.value.data_table = curFormData.value.data_table.filter(item => item.key != key);
  }

  //地区数据组装，已经选择的地区id数据跟通用地区组装
  const combine_area_info = (key, sele_id_array)=> {
    let tmp_data = JSON.parse(JSON.stringify(area_data.value));
    let obj_data = data_table.value.filter(item=>item.key!=key)
    let ids = []
    obj_data.forEach(item=>{
      ids = [...ids,...item.sele_area_id_array]
    })
    let init_com_data = [];
    for (let i in tmp_data) {
      let tmp_info = {};
      tmp_info.key = i;
      tmp_info.id = i;
      tmp_info.indeterminate = true;//有选中的为true
      tmp_info.checkList = [];
      tmp_info.sldchild = [];//下级数据
      tmp_info.name = tmp_data[i].regionName;
      //查看子元素是否有选中的
      tmp_info.checkAll = true;//是否全部选中
      for (let j in tmp_data[i].children) {
        let cur_data = tmp_data[i].children[j];
        if(cur_data.regionCode){
          let temp = ids.indexOf(cur_data.regionCode)
          if(temp!=-1){
            continue
          }
        }
        if (sele_id_array.length == 0) {
          tmp_info.checkAll = false;//是否全部选中
        } else {
          if (sele_id_array.indexOf(cur_data.regionCode) == -1) {
            tmp_info.checkAll = false;
          } else {
            tmp_info.checkList.push(cur_data.regionCode);
          }
        }
        tmp_info.sldchild.push({ id: j, key: j, label: cur_data.regionName, value: cur_data.regionCode });
      }
      if (tmp_info.checkList.length == 0) {
        tmp_info.indeterminate = false;
      }
      if(tmp_info&&tmp_info.sldchild&&tmp_info.sldchild.length>0){
        init_com_data.push(tmp_info);
      }
    }
    cur_key.value = key
    permissionList.value = init_com_data
    visible_one.value = true
    modalVisible.value = true
  }

  const sldHandleConfirmPer = ()=> {
    let seleSldPermiss = [];//选中的地区id结合
    for (let i = 0; i < permissionList.value.length; i++) {
      seleSldPermiss = seleSldPermiss.concat(permissionList.value[i]['checkList']);
    }

    //获取选中的城市名称
    let sele_area_name_array = [];
    for (let i in permissionList.value) {
      if (permissionList.value[i].checkList.length > 0) {
        for (let j  in permissionList.value[i].sldchild) {
          let tmp = permissionList.value[i].sldchild[j];
          if (seleSldPermiss.indexOf(tmp.value) != -1) {
            sele_area_name_array.push(tmp.label);
          }
        }
      }
    }
    for (let i in curFormData.value.data_table) {
      if (curFormData.value.data_table[i].key == cur_key.value) {
        curFormData.value.data_table[i].sele_area_id_array = seleSldPermiss;
        curFormData.value.data_table[i].deliver_areas = sele_area_name_array.join(',');
      }
    }
    emit('saveSeleArea',curFormData.value);
    sldHandleCancle()
  }

  const sldHandleCancle = ()=> {
    visible_one.value = false
    setTimeout(()=>{
      modalVisible.value = false
    })
  }
  onMounted(() => {
  });
  
</script>
<style lang="less">
  @import './SldExpressCom.less';
  .SldExpressCom_table .ant-table-cell .ant-form-item-with-help{
    margin-bottom: 0 !important;
  }

</style>
