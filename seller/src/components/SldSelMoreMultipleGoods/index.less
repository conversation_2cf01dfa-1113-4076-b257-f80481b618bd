// @import '~antd/lib/style/themes/default.less';

.component_sele_more_multiple{
  width: 100%;
  min-height: 422px;
  .ant-form-inline .ant-form-item-with-help{
    margin-bottom: 0px !important;
  }
  .content{
    width: 100%;
    overflow: hidden;
    .left,.right{
      width: 438px;
      background:rgba(245,245,245,1);
      border:1px solid rgba(238,238,238,1);
      flex-wrap: wrap;
      align-content: flex-start;
    }
    .item{
      width: 200px;
      height: 80px;
      background: #fff;
      border-radius:3px;
      margin: 10px 10px 0;
      position: relative;
      .item_left{
        width: 80px;
        height:80px;
        border-radius:3px 0 0 3px;
        position: relative;
        overflow: hidden;
        flex-shrink: 0;
        .left_play_icon{
          position: absolute;
          z-index: 2;
          left: 0;
          top: 0;
          width: 18px;
          height: 12px;
        }
        img.live_img{
          max-width: 100%;
          max-height: 100%;
        }
        .play_icon{
          position: absolute;
          z-index: 2;
          width: 22px;
          height: 22px;
          top: 50%;
          left: 50%;
          margin-left: -11px;
          margin-top: -11px;
        }
        .play_num{
          position: absolute;
          z-index: 2;
          left: 0;
          bottom: 0;
          width: 80px;
          height:20px;
          line-height: 20px;
          background:linear-gradient(0deg,rgba(51,51,51,1) 0%,rgba(0,0,0,0) 100%);
          border-radius: 0 0 3px 3px;
          color: #fff;
          font-size: 10px;
          text-align: center;
          padding: 0 4px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          span{
            transform: scale(0.85);
            display: inline-block;
          }
        }
      }
      .item_right{
        flex: 1;
        padding: 10px;
        .svideo_name{
          height:32px;
          font-size:12px;
          line-height: 16px;
          width: 105px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          /*! autoprefixer: off */
          -webkit-box-orient: vertical;
          word-break: break-word;
          color: #333;
        }
        .svideo_label{
          width: 82px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: inline-block;
          height: 14px;
          line-height: 14px;
          font-size: 12px;
          margin-top: 15px;
          white-space: nowrap;
          color: #FC1C1C;
        }
        .sele_svideo_flag{
          position: absolute;
          z-index: 2;
          bottom: 0;
          right: 0;
          width: 19px;
          height: 19px;
        }
      }
    }
    .item:nth-child(2n){
      margin-right: 0 !important;
      margin-left: 0;
    }
    .center{
      height: 100%;
      flex: 1;
      transform:rotate(90deg);
      -ms-transform:rotate(90deg); 	/* IE 9 */
      -moz-transform:rotate(90deg); 	/* Firefox */
      -webkit-transform:rotate(90deg); /* Safari 和 Chrome */
      -o-transform:rotate(90deg); 	/* Opera */
      z-index: 0;
    }
  }
  :global{
    .ant-form {
      padding-top: 0 !important;
    }
  }
}

.filter_box {
  min-height: 600px;
  padding: 0;
  background: #f5f5f5;
  .filter_supplier {
    width: 320px;
    margin-top: 10px;
    margin-left: 10px;
    min-height: 600px;
    padding: 0 6px;
    display: flex;
    flex-direction: column;
    background: #ffffff;
    padding-bottom: 10px;
    .filter_title {
      margin-top: 10px;
      margin-bottom: 8px;
      font-weight: 700;
    }
    .filter_main {
      .filter_main_item {
        margin-top: 10px;
        .left_title {
          width: 75px;
          margin-right: 6px;
          text-align: right;
          flex-shrink: 0;
        }
      }

      .filter_main_list {
        width: 102%;
        max-height: 475px;
        overflow-y: auto;
        overflow-x: hidden;

        .filter_main_item {
          margin-top: 5px;
          overflow: hidden;
          .left_title {
            width: 56px;
            margin-right: 6px;
            text-align: right;
            flex-shrink: 0;
          }
        }

        &::-webkit-scrollbar {
          width: 6px;
        }
        &::-webkit-scrollbar-thumb {
          border-radius: 10px;
          -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
          background: rgba(0, 0, 0, 0.1);
        }
        &::-webkit-scrollbar-track {
          -webkit-box-shadow: inset 0 0 5px transparent;
          border-radius: 0;
          background: transparent;
        }
      }
    }
  }
}
.goods_lists_scroll{
  max-height: 620px;
  min-height: 620px;
  overflow-y: auto;
}
.goods_lists {
  max-height: 620px;
  flex-wrap: wrap;
  padding-top: 10px;

  .goods_lists_item {
    position: relative;
    width: 316px;
    margin-left: 10px;
    margin-bottom: 10px;
    padding: 8px;
    background: #fff;
    svg {
      position: absolute;
      right: 0;
      bottom: 0;
      z-index: 2;
    }
    .goodsImage {
      width: 110px;
      height: 110px;
      background: #f8f8f8;
      border-radius: 4px;
      margin-right: 5px;
      flex-shrink: 0;

      img {
        max-width: 100%;
        max-height: 100%;
      }
    }
    .goodsInfo {
      flex: 1;

      .goodsName {
        display: -webkit-box;
        /*! autoprefixer: off */
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        word-break: break-all;
        text-overflow: ellipsis;
        height: 40px;
        color: #333333;
        font-size: 13px;
        font-family: PingFang SC;
        font-weight: bold;
      }
      .storeName {
        height: 20px;
        line-height: 20px;
        color: #999999;
        font-size: 13px;
        font-family: PingFang SC;
        font-weight: 500;
      }
      .coupon {
        height: 22px;
        font-size: 12px;
        font-family: PingFang SC;
        font-weight: 500;

        .coupon_amount {
          position: relative;
          margin-right: 6px;
          padding: 0 6px;
          span {
            color: #FE6232;
          }
          img {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
          }
        }
        .coupon_commission {
          span {
            display: inline-block;
            height: 20px;
            line-height: 19px;
            padding: 0 5px;
            &:nth-child(1) {
              color: #ffffff;
              background: linear-gradient(90deg, #FF745D, #FC3533);
              border-top: 1px solid #FE6232;
              border-left: 1px solid #FE6232;
              border-bottom: 1px solid #FE6232;
              border-top-left-radius: 4px;
              border-bottom-left-radius: 4px;
            }
            &:nth-child(2) {
              color: #FE6232;
              border-top: 1px solid #FE6232;
              border-right: 1px solid #FE6232;
              border-bottom: 1px solid #FE6232;
              border-top-right-radius: 4px;
              border-bottom-right-radius: 4px;
            }
          }
        }
      }
      .price {
        font-size: 12px;
        font-family: PingFang SC;
        font-weight: 500;
        margin-top: 6px;

        .price_amount {
          .price_amount_extra {
            color: #FD2B24;
          }
          .price_amount_unit {
            color: #FD2B24;
          }
          .price_amount_num {
            color: #FC1C1C;
            font-size: 16px;
            font-weight: bold;
          }
          .price_amount_market {
            color: #999999;
            font-size: 13px;
            text-decoration: line-through;
            margin-left: 4px;
          }
        }
        .price_num {
          color: #999999;
        }
      }
    }
  }
}

.mask{
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99;
  background: rgba(0,0,0,.4);
  color: #fff;
  font-size: 13px;
  font-weight: 700;
}
