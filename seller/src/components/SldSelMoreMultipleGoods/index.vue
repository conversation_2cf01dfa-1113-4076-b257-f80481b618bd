<template>
  <div>
    <Modal
      destroyOnClose
      :maskClosable="false"
      :title="props.title"
      :visible="props.modalVisible"
      :width="1000"
      @ok="sldConfirm"
      @cancel="sldCancle"
    >
      <template #footer>
        <Button key="back" @click="sldCancle">{{ props.cancelBtnText }}</Button>
        <Button
          key="submit"
          type="primary"
          :loading="props.confirmBtnLoading"
          @click="sldConfirm"
          >{{ props.confirmBtnText }}</Button
        >
      </template>
      <div class="component_sele_more_multiple flex_row_between_start filter_box">
        <Form layout="inline" ref="formRef" :model="detail">
          <div class="filter_supplier">
            <div class="filter_title">TAB栏设置</div>
            <div class="filter_main">
              
              <div class="flex_row_start_center filter_main_item">
                <div class="left_title"><span style="color: red;">*</span>设置标题</div>
                <FormItem
                  name="title"
                  v-bind:rules="[
                    {
                    required: true,
                    whitespace: true,
                    message: `请输入标题`,
                  }
                  ]"
                >
                  <Input style="width: 220px;"
                    :maxlength="6"
                    placeholder="请输入标题"
                    v-model:value="detail.title"
                    allowClear
                    />
                </FormItem>
              </div>
              <div class="flex_row_start_center filter_main_item">
                <div class="left_title">设置子标题</div>
                <FormItem
                  name="subtitle"
                >
                  <Input style="width: 220px;"
                    :maxlength="8"
                    placeholder="请输入子标题"
                    allowClear
                    v-model:value="detail.subtitle"
                    />
                </FormItem>
              </div>
            </div>
            <!-- 筛选条件-start -->
            <div class="filter_title">筛选条件</div>
            <div class="filter_main">
              
              <div class="flex_row_start_center filter_main_item">
                <div class="left_title">商品名称</div>
                <FormItem
                  name="goodsName"
                >
                  <Input style="width: 220px;"
                    :maxlength="50"
                    allowClear
                    placeholder="请输入商品名称"
                    v-model:value="detail.goodsName"
                    @change="changeValue"
                    />
                </FormItem>
              </div>
              <div class="flex_row_start_center filter_main_item">
                <div class="left_title">商品分类</div>
                <FormItem
                  name="categoryIds"
                >
                <TreeSelect
                  v-model:value="detail.categoryIds"
                  :style="{ width: '220px' }"
                  :treeData="goods_category_list"
                  showCheckedStrategy="SHOW_PARENT"
                  allowClear
                  multiple
                  treeCheckable
                  :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                  :placeholder="`请选择商品分类`"
                  @change="changeValue"
                  :dropdownStyle="{ maxHeight: '215px' }"
                  :listHeight="205"
                />
                </FormItem>
              </div>
              <div class="flex_row_start_center filter_main_item">
                <div class="left_title">商品价格</div>
                <FormItem
                  name="lowPrice"
                  style="width: 100px;margin-right: 5px;"
                >
                  <div class="flex_row_start_center">
                    <InputNumber
                      :max="99999999"
                      :min="0"
                      @change="changeValue"
                      style="width: 100px !important"
                      :precision="2"
                      placeholder="最小值"
                      v-model:value="detail.lowPrice"
                    />
                  </div>
                </FormItem>
                <div style="margin-top: 3px">~</div>
                <FormItem
                  name="highPrice"
                  style="width: 100px; margin-left: 5px"
                >
                  <div class="flex_row_start_center">
                    <InputNumber
                      :max="99999999"
                      @change="changeValue"
                      :min="0.01"
                      style="width: 100px !important"
                      :precision="2"
                      placeholder="最大值"
                      v-model:value="detail.highPrice"
                    />
                  </div>
                </FormItem>
              </div>
              <div class="flex_row_start_center filter_main_item">
                <div class="left_title">商品品牌</div>
                <FormItem
                  name="brandIds"
                >
                  <Select
                  mode="multiple"
                  placeholder="请选择商品品牌"
                  optionFilterProp="label"
                  style="width: 220px"
                  allowClear
                  @change="changeValue"
                  :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                  v-model:value="detail.brandIds"
                >
                  <Select.Option
                    v-for="(items, indexs) in brand_list"
                    :key="indexs"
                    :value="items.brandId"
                    :label="items.brandName"
                    >{{ items.brandName }}</Select.Option
                  >
                </Select>
                </FormItem>
              </div>
              <div class="flex_row_start_center filter_main_item">
                <div class="left_title">发布时间</div>
                <FormItem
                  name="brandIds"
                >
                  <RangePicker
                    style="width: 220px"
                    :format="'YYYY-MM-DD'"
                    @change="changeValue"
                    v-model:value="detail.startTime"
                    :placeholder="['开始时间', '结束时间']"
                    :getPopupContainer="
                      (triggerNode) => {
                        return triggerNode.parentNode;
                      }
                    "
                  />
                </FormItem>
              </div>
              <div class="flex_row_start_center filter_main_item">
                <div class="left_title">销量</div>
                <RadioGroup v-model:value="detail.couponPrice"  @change="changeValue">
                  <div class="flex_column_start_start">
                    <Radio :value="1">全部</Radio>
                    <div style="width: 100%;height: 4px;"></div>
                    <Radio :value="2">
                     <div class="flex_row_start_start">
                      <FormItem
                        name="lowSaleNum"
                        style="width:88px;margin-right: 5px;"
                      >
                        <div class="flex_row_start_center">
                          <InputNumber
                          @change="changeValue('lowSaleNum')"
                            :max="99999999"
                            :min="0"
                            style="width: 88px !important"
                            :precision="2"
                            placeholder="最小值"
                            v-model:value="detail.lowSaleNum"
                          />
                        </div>
                      </FormItem>
                      <div style="margin-top: 3px">~</div>
                      <FormItem
                        name="highSaleNum"
                        style="width: 88px; margin-left: 5px"
                      >
                        <div class="flex_row_start_center">
                          <InputNumber
                            :max="99999999"
                            :min="0.01"
                            @change="changeValue('highSaleNum')"
                            style="width: 88px !important"
                            :precision="2"
                            placeholder="最大值"
                            v-model:value="detail.highSaleNum"
                          />
                        </div>
                      </FormItem>
                     </div>
                    </Radio>

                  </div>
                </RadioGroup>
              </div>
              <div class="flex_row_start_center filter_main_item">
                <div class="left_title">排序</div>
                <FormItem
                  name="sort"
                >
                  <RadioGroup v-model:value="detail.sort" @change="changeValue">
                    <div class="flex_column_start_start">
                      <Radio :value="0">默认</Radio>
                      <div style="width: 100%;height: 4px;"></div>
                      <Radio :value="1">销量优先</Radio>
                      <div style="width: 100%;height: 4px;"></div>
                      <Radio :value="4">价格由高到低</Radio>
                      <div style="width: 100%;height: 4px;"></div>
                      <Radio :value="5">人气优先</Radio>
                    </div>
                  </RadioGroup>
                </FormItem>
              </div>
            </div>
            <!-- 筛选条件-end -->
          </div>
        </Form>
        <div style="width: 720px;">
          <div class="goods_lists_scroll">
            <div class="flex_row_start_start goods_lists">
              <template v-if="data.list != undefined && data.list.length > 0">
                <div v-for="(item,index) in data.list" :key="index" style="position: relative;" class="goods_lists_item">
                  <div class="mask flex_row_center_center" v-if="false">已参加其他活动</div>
                  <div class="mask flex_row_center_center" v-if="false">暂时无货</div>
                  <div class="flex_row_start_start" @click="chooseGoods(item)">
                    <template v-if="choose_list.ids.length>0 && choose_list.ids.indexOf(item.goodsId)!=-1">
                      <AliSvgIcon
                          iconName="iconyixuan"
                          width="19px"
                          height="19px"
                          fillColor="#FF711E"
                        />
                    </template>
                    <div class="flex_row_center_center goodsImage">
                      <img :src="item.goodsImage" alt="">
                    </div>
                    <div class="goodsInfo">
                      <div class="goodsName">
                        {{ item.goodsName ? item.goodsName.replaceAll('<font color="red">','').replaceAll('</font>','') : '' }}
                      </div>
                      <div class="storeName"></div>
                      <div class="flex_row_start_center coupon"></div>
                      <div class="flex_row_between_center price">
                        <div class="price_amount">
                          <span v-if="item.goodsPrice && item.goodsPrice>=0">
                            <span class="price_amount_unit">￥</span>
                            <span class="price_amount_num">{{ item.goodsPrice }}</span>
                          </span>
                          <span v-else style="color: #999;font-size: 13px;margin-left: 4px;">--</span>
                          <span class="price_amount_market" v-if="item.marketPrice>0 && (item.goodsPrice<item.marketPrice) ">￥{{item.marketPrice}}</span>
                        </div>
                        <div class="price_num">{{ item.saleNum }}人付款</div>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                    class="flex_row_end_center"
                    style="width: 100%;margin-right: 10px;margin-bottom: 8px;"
                    v-if="data.pagination && data.pagination.total > 0"
                  >
                    <Pagination
                      size="small"
                      :pageSize="10"
                      :showSizeChanger="false"
                      :current="data.pagination.current"
                      :total="data.pagination.total"
                      @change="onChangePage"
                    />
                  </div>
              </template>
              <template v-else>
                <div class="flex_column_center_center" :style="{ width: '100%', height: '605px' }">
                  <Empty :image="simpleImage"  description="暂无数据"></Empty>
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  </div>
</template>
<script>
  export default {
    name: 'SldSelMoreMultipleGoods',
  };
</script>
<script setup>
  import moment from 'moment';
  import { ref, onMounted, computed, watch, reactive } from 'vue';
  import { Modal, Button, Empty,Form,FormItem,Input,TreeSelect,InputNumber,Select,RangePicker,RadioGroup,Radio,Pagination } from 'ant-design-vue';
  import { getGoodsGoodsListApi } from '/@/api/common/commons';
  import { list_com_page_size_16, failTip } from '/@/utils/utils';
  import dayjs from 'dayjs';
  import {
    getCategoryTree,
    getBrandList
  } from '/@/api/manage/manage';
  import { useUserStore } from '/@/store/modules/user';

  const data = reactive({
    list:[]
  });
  const choose_list = reactive({//点击选择的商品列表数据
    data: [],
    ids: [],
  })
  const detail = ref({
    couponPrice:1
  })
  const filter_code = ref({})
  const formRef = ref()
  const shop_flag  = ref(false)
  const formValues = ref({});
  const loading_pagination_flag = ref({});
  const brand_list = ref([])
  const goods_category_list = ref([])
  const params = ref({ pageSize: 10 }); //搜索条件
  const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;
  const userStore = useUserStore();

  const getUserInfo = computed(() => {
    return userStore.getStoreInfo;
  });


  const props = defineProps({
    title: { type: String, required: true }, //弹框标题
    modalVisible: { type: Boolean, required: true }, //弹框是否显示
    confirmBtnLoading: { type: Boolean, default: false }, //弹框确认按钮loading属性，默认不显示
    height: { type: Number }, //弹框内容高度
    confirmBtnText: { type: String, default: '确认' }, //弹框底部确认按钮显示文案，默认为确认
    cancelBtnText: { type: String, default: '取消' }, //弹框底部取消按钮显示文案，默认为取消
    formValue:{type: Object},//参数
    client: { type: String, default: '' },//装修来源类型 supplier供应链中心
  });

  const dataKey = ref('goodsId'); //列表key值，默认为商品id

  const emit = defineEmits(['cancleEvent', 'confirmEvent']);

  //由于watch无法监听props.modalVisible，所以需要计算属性转为ref对象
  const modalVisible = computed(() => {
    return props.modalVisible;
  });
  //由于watch无法监听props.modalVisible，所以需要计算属性转为ref对象
  const formValue = computed(() => {
    return props.formValue;
  });

  watch(modalVisible, () => {
    if (modalVisible.value) {
      detail.value = {
        couponPrice:1
      }
      let showGood = false
      formValues.value = {}
      choose_list.data = []
      choose_list.ids = []
      data.list = []
      filter_code.value = {}
      if(formValue.value){
        let obj = JSON.parse(JSON.stringify(formValue.value))
        if(obj.title){
          detail.value.title = obj.title
          detail.value.subtitle = obj.subtitle
          if(obj.qc.showGoods){
            showGood = true
            formValues.value = obj.qc
            delete formValues.value.showGoods
            for(let i in formValues.value){
              let item = formValues.value[i]
              if(i=='categoryIds' || i=='brandIds'){
                detail.value[i] = item.split(',')
              }else if(i=='startTime' || i=='endTime'){
                if(i=='startTime'){
                  detail.value[i] = [dayjs(formValues.value[i], 'YYYY-MM-DD'), dayjs(formValues.value.endTime, 'YYYY-MM-DD')]
                }
              }else if(i=='lowSaleNum' || i == 'highSaleNum'){
                detail.value.couponPrice = 2
                detail.value[i] = item
              }else{
                detail.value[i] = item
              }
            }
            get_list({ pageSize: 10,...formValues.value });
          }
          choose_list.data = obj.info
          choose_list.ids = obj.ids
        }
      }
      params.value = { pageSize: 10 }
      if(!showGood){
        get_list({ pageSize: 10 });
      }
      get_category_tree()
      get_brand_list()
    }
  });

  // 获取品牌列表数据
  const get_brand_list = async()=> {
   let res = await getBrandList({pageSize:10000});
   if (res.state == 200) {
      brand_list.value = res.data.list
    }
  }

  // 筛选条件输入
  const changeValue = (type)=> {
    if(type=='lowSaleNum' || type== 'highSaleNum'){
      if(detail.value.couponPrice==1){
        return
      }
    }
    let serach_info = {}
    for(let i in detail.value){
      if((detail.value[i]!=null&&detail.value[i]&&detail.value[i]!='')||detail.value[i]==0){
        if(i=='categoryIds' || i=='brandIds'){
          serach_info[i] = detail.value[i].join(',')
        }else if(i=='startTime'){
          serach_info.startTime =  detail.value[i][0]
            ? detail.value[i][0].format('YYYY-MM-DD 00:00:00')
            : '';
          serach_info.endTime =  detail.value[i][1]
            ? detail.value[i][1].format('YYYY-MM-DD 11:59:59')
            : '';
        }else if(i=='lowSaleNum' || i == 'highSaleNum'){
          if(detail.value.couponPrice==2){
            serach_info[i] = detail.value[i]
          }
        }else if(i == 'couponPrice'){

        }else{
          serach_info[i] = detail.value[i]
        }

      }
    }
    for(let i in serach_info){
      if(i == 'title' || i == 'subtitle'){
        delete serach_info[i]
      }
      if(serach_info[i]==''){
        delete serach_info[i]
      }
    }
    formValues.value = JSON.parse(JSON.stringify(serach_info))
    get_list({...formValues.value,...params.value})
  }

  //改变页码
  const onChangePage = (page) => {
    let curParams = { pageSize: 10, current: page, ...formValues.value };
    if (filter_code.value) {
      curParams.state = filter_code.value;
    }
    params.value = curParams;
    get_list(curParams);
  };

  // 获取数据
  const get_list = async (param) => {
    let new_params = { ...param};
    let res = null;
    res = await getGoodsGoodsListApi(new_params);
    if (res.state == 200) {
      if (res.data.pagination != undefined) {
        if (res.data.pagination.current == 1) {
          data.list = res.data.list
          data.pagination = res.data.pagination;
        } else {
          data.list = res.data.list
          data.pagination = res.data.pagination;
        }
      }
      loading_pagination_flag.value = false;
    }
  };

  
  // 确定
  const sldConfirm = () => {
    formRef.value
      .validate()
      .then((res) => {
        let arrayInfo = []
        if(choose_list.data.length>0&&props.client&&props.client=='pc'){
          choose_list.data.forEach(item=>{
            arrayInfo.push({
              goodsId:item.goodsId,
              defaultProductId:item.defaultProductId,
              goodsName:item.goodsName,
              goodsPrice:item.goodsPrice,
              goodsImage:item.goodsImage,
              marketPrice:item.marketPrice,
              state:item.state,
              shopType:item.shopType,
            })
          })
        }else{
          arrayInfo = choose_list.data
        }
        let obj = {
          title:detail.value.title?detail.value.title:'',
          subtitle:detail.value.subtitle?detail.value.subtitle:'',
          qc:{
            showGoods:choose_list.data.length>0?false:true,
          },
          ids:choose_list.ids.length>0?choose_list.ids:[],
          info:choose_list.data.length>0?choose_list.data:[],
        }
        if(choose_list.data.length==0){
          obj.qc = {...obj.qc,...formValues.value}
        }else{
          obj.qc = {showGoods:false}
        }
        emit('confirmEvent', obj);
      }).catch((err)=>{

      })
  };

  // 取消
  const sldCancle = () => {
    params.value = { pageSize: list_com_page_size_16 };
    formValues.value = {};
    emit('cancleEvent');
  };

  //选择商品（支持推荐商品、tab切换组件）
  const chooseGoods = (item)=> {
    if (choose_list.ids.length > 0) {
      let choose_index = choose_list.ids.indexOf(item.goodsId);
      if (choose_index != -1) {
        choose_list.data.splice(choose_index,1);
        choose_list.ids.splice(choose_index,1);
      } else {
        choose_list.data.push(item);
        choose_list.ids.push(item.goodsId);
      }
    } else {
      choose_list.data.push(item);
      choose_list.ids.push(item.goodsId);
    }
  }

  //获取分类树数据
  const get_category_tree = async () => {
        let param = {goodsSource:0}
        // dev_supplier-start
        if(getUserInfo.value.shopType&&getUserInfo.value.shopType=='3'){
          param = {goodsSource:2}
        }
        // dev_supplier-end
        const res = await getCategoryTree(param);
        if (res.state == 200 && res.data) {
          for (let i in res.data) {
            res.data[i].key = res.data[i].categoryId;
            res.data[i].value = res.data[i].categoryId;
            res.data[i].title = res.data[i].categoryName;
            if (res.data[i].children != null && res.data[i].children.length > 0) {
              res.data[i].children.map(item => {
                item.key = item.categoryId;
                item.value = item.categoryId;
                item.title = item.categoryName;
                if (item.children != null && item.children.length > 0) {
                  item.children.map(items => {
                    items.key = items.categoryId;
                    items.value = items.categoryId;
                    items.title = items.categoryName;
                  });
                }
              });
            }
          }
          goods_category_list.value = res.data
        }
      };


  onMounted(() => {});
</script>
<style lang="less">
  @import './index.less';
</style>
