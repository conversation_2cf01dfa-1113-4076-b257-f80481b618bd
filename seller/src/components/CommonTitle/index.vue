<template>
  <div class="common_title_bg flex_row_between_center">
    <div class="flex_row_start_center">
      <slot name="button"></slot>
      <span class="title">{{ props.text }}</span>
      <span
        :style="{ color: describeColor ? describeColor : 'gray', fontSize: '12px', paddingLeft: '10px' }"
        v-if="props.describe.length > 0"
        >{{ props.describe }}</span
      >
    </div>
    <div class="del_ladder_pro" v-if="closeFlag" @click="close_click">
      <AliSvgIcon iconName="iconqingchu" width="18px" height="18px" fillColor="#c8c8c8" />
    </div>
  </div>
</template>
<script setup>
  const props = defineProps({
    text: { type: String, default: '' }, //标题
    describe: { type: String, default: '' }, //描述
    describeColor: { type: String, default: '' }, //描述颜色
    closeFlag: { type: <PERSON>olean,  required: false, default: false }, //是否需要删除键
  });

  const emit = defineEmits([
    'closeClick'
  ]);

  const close_click = ()=>{
    emit('closeClick');
  }

</script>
<style lang="less">
  .common_title_bg {
    width: 100%;
    height: 40px;
    border-radius: 2px;
    background: rgba(255, 75, 17, .05);
    line-height: 40px;

    .title {
      padding-left: 10px;
      color: #333;
      font-size: 13px;
    }
    .del_ladder_pro{
      display: none;
      padding-right: 15px;
      padding-top: 7px;
    }
    &:hover{
      .del_ladder_pro{
        display: inline-block;
      }
    }
  }
</style>
