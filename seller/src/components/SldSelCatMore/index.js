
import { getDrawListApi } from '@/api/promotion/draw';
import { getSeckilUnfinishedApi } from '@/api/promotion/seckill';
import SldSelGoodsSingleDiy from './index.vue';
import { getMobleDecoListApi,getCategoryList ,getDecoList} from '/@/api/decorate/deco';

export { SldSelGoodsSingleDiy };

//选择器选择到需要弹窗的选项时的设置属性
export const setSldSelProps = (type, isMobile) => {
  const modalProps = {
    searchInfo: {},
  };

  switch (type) {
    case 'topic': {
      modalProps.modalTitle = '请选择专题名称';
      modalProps.api = isMobile ? getMobleDecoListApi : getDecoList;
      modalProps.searchInfo = { decoType: 'topic', isEnable: 1 };
      modalProps.column = [
        { title: '专题名称', dataIndex: isMobile ? 'name' : 'decoName' },

        { title: '创建时间', dataIndex: 'createTime' },

        { title: '修改时间', dataIndex: 'updateTime' },
      ];
      modalProps.formConfig = [
        {
          component: 'Input',
          field: 'decoName',
          label: '专题名称',
        },
      ];
      break;
    }
    case 'o2o_topic': {
      modalProps.modalTitle = '请选择专题名称';
      modalProps.api = getMobleDecoListApi;
      modalProps.searchInfo = { decoType: 'o2o_topic', isEnable: 1 };
      modalProps.column = [
        { title: '专题名称', dataIndex: isMobile ? 'name' : 'decoName' },

        { title: '创建时间', dataIndex: 'createTime' },

        { title: '修改时间', dataIndex: 'updateTime' },
      ];
      modalProps.formConfig = [
        {
          component: 'Input',
          field: 'decoName',
          label: '专题名称',
        },
      ];
      break;
    }
    case 'category': {
      modalProps.modalTitle = '选择分类';
      modalProps.api = getCategoryList;
      modalProps.showHeader = false;
      modalProps.showIndex = false;
      modalProps.searchInfo = { categoryId: 0, pageSize: 100 };
      modalProps.link_type = 'category';
      modalProps.rowId = 'categoryId';
      modalProps.column = [
        {
          title: `分类名称`, //分类名称
          align: 'left',
          dataIndex: 'categoryName',
          width: 250,
        },
      ];
      break;
    }
  }

  return modalProps;
};

export const setSldSelPromoProps = (type) => {
  const modalProps = {
    searchInfo: {},
  };

  switch (type) {
    case 'draw': {
      modalProps.modalTitle = '选择抽奖活动';
      modalProps.api = getDrawListApi;
      modalProps.searchInfo = { state: 3 };
      modalProps.column = [
        {
          title: '活动名称',
          dataIndex: 'drawName',
          align: 'center',
          width: 100,
        },
        {
          title: '活动类型',
          dataIndex: 'drawTypeValue',
          align: 'center',
          width: 100,
        },
        {
          title: '活动时间',
          dataIndex: 'startTime',
          align: 'center',
          width: 200,
          customRender: ({ text, record }) => {
            return text + '~' + record.endTime;
          },
        },
        {
          title: '活动状态',
          dataIndex: 'stateValue',
          align: 'center',
          width: 100,
        },
      ];
      modalProps.formConfig = [
        {
          component: 'Input',
          field: 'drawName',
          label: '活动名称',
          componentProps: {
            placeHolder: '请输入活动名称',
          },
        },
        {
          component: 'Select',
          label: `活动类型`,
          field: 'drawType',
          componentProps: {
            placeHolder: '请选择活动类型',
            options: [
              { value: 0, label: '全部' },
              { value: 1, label: '幸运抽奖' },
              { value: 2, label: '大转盘抽奖' },
              { value: 3, label: '刮刮卡' },
              { value: 4, label: '摇一摇' },
              { value: 5, label: '翻翻看' },
            ],
          },
        },
        {
          type: 'Select',
          label: `活动状态`,
          field: 'state',
          componentProps: {
            placeHolder: '请选择活动状态',
            options: [
              { value: '', label: '全部' },
              { value: '1', label: '未开始' },
              { value: '2', label: '进行中' },
            ],
          },
        },
      ];
      break;
    }

    case 'seckill': {
      modalProps.modalTitle = '请选择专题名称';
      modalProps.api = getSeckilUnfinishedApi;
      modalProps.column = [
        {
          title: '活动名称', //活动名称
          dataIndex: 'seckillName',
          align: 'center',
          width: 100,
        },
        {
          title: '活动时间', //活动时间
          dataIndex: 'startTime',
          align: 'center',
          width: 200,
          customRender: (text, record, index) => {
            return text + '~' + record.endTime;
          },
        },
        {
          title: '活动状态', //活动状态
          dataIndex: 'stateValue',
          align: 'center',
          width: 100,
        },
      ];
      modalProps.formConfig = [
        {
          component: 'Input',
          field: 'seckillName',
          label: '活动名称',
          componentProps: {
            placeHolder: '请输入活动名称',
          },
        },
        {
          component: 'Select',
          label: `活动状态`,
          field: 'state',
          componentProps: {
            placeHolder: '请选择活动状态',
            options: [
              { value: 0, label: '全部' },
              { value: 1, label: '未开始' },
              { value: 2, label: '进行中' },
            ],
          },
        },
      ];
      break;
    }
  }

  return modalProps;
};
