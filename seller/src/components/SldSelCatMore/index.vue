<template>
  <Modal
    destroyOnClose
    :maskClosable="false"
    :title="modal_title"
    :visible="modalVisible"
    :width="modalWidth"
    @ok="sldConfirm"
    @cancel="sldCancle"
    :footer="props.look ? null : undefined"
  >
    <div class="common_page sld_sel_goods_single_diy">
      <BasicTable
        :dataSource="selectedRows_row_list.data"
        :columns="columns_info"
        :pagination="false"
        :bordered="true"
        :maxHeight="150"
        :ellipsis="false"
        :actionColumn="{
          title: '操作',
          width: 80,
          dataIndex: 'action',
        }"
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'action'">
            <div class="flex_row_center_center">
              <span @click="delGoods(record.goodsId)" style="cursor: pointer">
                <AliSvgIcon
                  iconName="iconshanchu5"
                  width="18px"
                  height="18px"
                  :fillColor="'#2d2d2d'"
                />
              </span>
            </div>
          </template>
        </template>
      </BasicTable>

      <BasicTable
        :dataSource="dataSource.data"
        @register="registerTable"
        @expand="expandedRowsChange"
        :tableFlag="false"
      >
        <template #tableTitle v-if="tableTitle">
          <slot name="tableTitle"></slot>
        </template>
        <template #bodyCell="data">
          <slot name="bodyCell" v-bind="data || {}"></slot>
          <template v-if="data.column.dataIndex">
            {{ data.text ? data.text : '--' }}
          </template>
        </template>
      </BasicTable>
    </div>
  </Modal>
</template>
<script setup>
  import { ref, reactive, computed, watch } from 'vue';
  import { Modal } from 'ant-design-vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import { getCategoryList } from '/@/api/decorate/deco';

  import { failTip, selectRadio, SelectAll } from '@/utils/utils';

  const emit = defineEmits(['cancleEvent', 'confirmEvent', 'expandEvent', 'selectEvent']);

  const props = defineProps({
    modalTitle: { type: String, default: '选择器' }, //弹窗标题
    tableTitle: { type: Boolean, required: false, default: false }, //tableTitle插槽是否启用
    modalVisible: { type: Boolean, required: false, default: false }, //弹窗开关
    modalWidth: { type: Number, required: false, default: 1000 }, //弹窗开关
    selectedRowKeys: {
      type: Array,
      required: true,
      default: () => [],
    }, //选中行的id
    selectedRows: {
      type: Array,
      required: true,
      default: () => [],
    }, //选中行的数据
    client: { type: String, default: '' },
    extra: { type: Object },
  });

  //由于watch无法监听modalVisible，所以需要计算属性转为ref对象
  const modalVisible = computed(() => {
    return props.modalVisible;
  });
  const modalTitle = computed(() => {
    return props.modalTitle;
  });

  const tableTitle = computed(() => {
    return props.tableTitle;
  });
  const modal_title = ref('');
  const selectedRows_row = ref([]);
  const selectedRows_row_list = reactive({
    data: [],
  });
  const selectedKeys = ref([]);
  const dataSource = reactive({
    data: [],
  });
  const rowSelection = ref({});

  //
  const columns = ref([
    {
      title: '分类名称',
      align: 'left',
      dataIndex: 'categoryName',
      width: 250,
    },
  ]);
  //
  const columns_info = ref([
    {
      title: '分类名称',
      align: 'left',
      dataIndex: 'categoryName',
      width: 250,
    },
    {
      title: '级别',
      align: 'left',
      dataIndex: 'grade',
      width: 250,
    },
  ]);

  watch(
    modalVisible,
    () => {
      if (modalVisible.value) {
        dataSource.data = [];
        modal_title.value = modalTitle.value;
        selectedRows_row.value = []; //选中的行数据
        selectedKeys.value = []; //选中的行id
        if (props.selectedRows.length > 0) {
          selectedRows_row.value = JSON.parse(JSON.stringify(props.selectedRows));
          selectedRows_row_list.data = JSON.parse(JSON.stringify(props.selectedRows));
        }
        if (props.selectedRowKeys.length > 0) {
          selectedKeys.value = JSON.parse(JSON.stringify(props.selectedRowKeys));
        }

        get_category_list({ categoryId: 0, pageSize: 10000 }, { grade: 0, pid: 0 });
      }
    },
    { deep: true },
  );

  // 表格数据
  const [registerTable, { reload, setProps, setSelectedRowKeys }] = useTable({
    // 请求接口
    rowKey: 'categoryId',
    immediate: false,
    columns: columns.value,

    maxHeight: 300,
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    pagination: false,
    // 是否显示序号列
    showIndexColumn: false,
    // 表格右侧操作列配置
    showHeader: true,
    ellipsis: false,
    isTreeTable: true,
    clickToRowSelect: false,
    rowSelection: {
      type: 'checkbox',
      selectedRowKeys: selectedKeys,
      onSelect: onSelect,
      onSelectAll: onSelectAll,
    },
    onExpand: (expande, record) => onExpandTable(expande, record),
  });

  const sldConfirm = () => {
    // 选中行：getSelectRows()
    if (selectedKeys.value.length > 0) {
      if (
        props.extra.min_num != undefined &&
        props.extra.min_num > 0 &&
        selectedKeys.value.length < props.extra.min_num
      ) {
        failTip(`该模块至少需要选择${props.extra.min_num}个分类`); //该模块至少需要选择   个分类
        return false;
      }
      if (props.extra.total_num > 0 && selectedKeys.value.length != props.extra.total_num) {
        failTip(`该模块需要选择${props.extra.total_num}个分类`); //该模块需要选择   个分类
        return false;
      }
      if (props.extra.max_num > 0 && selectedKeys.value.length > props.extra.max_num) {
        failTip(`该模块最多只能选择${props.extra.max_num}个分类`); //该模块最多只能选择   个分类
        return false;
      }
      let modalTableSeleData = JSON.parse(JSON.stringify(selectedRows_row_list));
      let modalTableSeleDataIds = JSON.parse(JSON.stringify(selectedKeys.value));
      emit('confirmEvent', modalTableSeleData, modalTableSeleDataIds);
    } else {
      failTip('请选择分类');
    }
  };

  // 单选多选事件
  function onSelect(record, selected) {
    let rows = selectRadio(
      selectedKeys.value,
      selectedRows_row.value,
      'categoryId',
      record,
      selected,
    );
    selectedKeys.value = rows.selectedRowKeys;
    selectedRows_row.value = rows.selectedRows;
    let obj = JSON.parse(JSON.stringify(rows.selectedRows));
    let arr = [];
    obj.forEach((item) => {
      arr.push({
        categoryId: item.categoryId,
        categoryName: item.categoryName,
        grade: item.grade,
        pid: item.pid,
      });
    });
    selectedRows_row_list.data = JSON.parse(JSON.stringify(arr));
  }

  // 全选按钮事件
  function onSelectAll(selected, rows, changeRows) {
    let rowAll = SelectAll(
      selectedKeys.value,
      selectedRows_row.value,
      'categoryId',
      selected,
      rows,
      changeRows,
    );
    selectedKeys.value = rowAll.selectedRowKeys;
    selectedRows_row.value = rowAll.selectedRows;
    let obj = JSON.parse(JSON.stringify(rowAll.selectedRows));
    let arr = [];
    obj.forEach((item) => {
      arr.push({
        categoryId: item.categoryId,
        categoryName: item.categoryName,
        grade: item.grade,
        pid: item.pid,
      });
    });
    selectedRows_row_list.data = JSON.parse(JSON.stringify(arr));
  }

  const sldCancle = () => {
    emit('cancleEvent');
  };

  const deepSearch = (data, categoryId, targetChildren) => {
    let key = '';
    if (props.diy_type == 'integral' || props.diy_type == 'spreader') {
      key = 'labelId';
    } else {
      key = 'categoryId';
    }
    for (let ins in data) {
      if (data[ins][key] == categoryId) {
        data[ins].children = targetChildren;
        data[ins].dataRequested = true;
      } else if (data[ins].children && data[ins].children.length) {
        deepSearch(data[ins].children, categoryId, targetChildren);
      }
    }
    return data;
  };

  //表格展开关闭事件
  function onExpandTable(expande, record) {
    if (expande && (!record.children || record.children.length == 0)) {
      if (props.diy_type == 'integral') {
        get_category_list({ labelId: record.labelId, pageSize: 10000 }, record);
      } else if (props.diy_type == 'spreader') {
        get_category_list({ labelId: record.labelId, isShow: 1, pageSize: 10000 }, record);
      } else {
        get_category_list({ categoryId: record.categoryId, pageSize: 10000 }, record);
      }
    }
  }

  const get_category_list = async (params, extra) => {
    let res = null;
    let key = '';

    res = await getCategoryList(params);
    key = 'categoryId';
    if (res.state == 200 && res.data && res.data.list) {
      if (extra.grade == 0) {
        dataSource.data = res.data.list;
      } else if (extra.grade == 1) {
        let temp = dataSource.data.filter((item) => item[key] == extra[key]);
        if (temp.length > 0) {
          temp[0].children = res.data.list;
        }
      } else if (extra.grade == 2) {
        let temp = dataSource.data.filter((item) => item[key] == extra.pid);
        if (temp.length > 0) {
          let temps = temp[0].children.filter((item) => item[key] == extra[key]);
          if (temps.length > 0) {
            temps[0].children = res.data.list;
          }
        }
      } else if (extra.grade == 3) {
        let temp = dataSource.data.filter((item) => item[key] == extra.path.split('/')[1]);
        if (temp.length > 0) {
          let temps = temp[0].children.filter((item) => item[key] == extra.pid);
          if (temps.length > 0) {
            let tempss = temps[0].children.filter((item) => item[key] == extra[key]);
            if (tempss.length > 0) {
              tempss[0].children = res.data.list;
            }
          }
        }
      }
      setProps({
        dataSource: dataSource.data,
      });
      setSelectedRowKeys(selectedKeys.value);
    } else {
      failTip(res.msg);
    }
  };
</script>
<style lang="less">
  .sld_sel_goods_single_diy {
    max-height: 600px;

    .ant-table-body {
      height: auto !important;
    }
  }

  p {
    margin-bottom: 0;
  }
</style>
