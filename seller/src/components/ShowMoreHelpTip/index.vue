<!-- 公共头部组件 -->
<template>
  <div class="show_more_help_tip">
    <div v-if="(props.tipTitle || props.tipData.length > 0) && show_tip" class="common_header_tip">
      <div v-if="props.tipTitle" class="common_header_tip_title">{{ props.tipTitle }}</div>
      <div v-if="props.tipData.length > 0" class="common_header_tip_list">
        <ul>
          <template v-for="(item, index) in props.tipData" :key="index">
            <li v-if="item">• {{ item }}</li>
          </template>
        </ul>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';

  const props: any = defineProps({
    showTip: { type: Boolean, required: false, default: true }, //提示内容是否展示
    tipTitle: { type: String, required: false, default: '' }, //提示内容标题
    tipData: { type: Array, required: false, default: () => [] }, //提示内容信息
  });

  const show_tip = ref(props.showTip); //提示信息是否显示
</script>
<style lang="less" scoped>
  .show_more_help_tip {
    background-color: #fff;

    .common_header_tip {
      width: 100%;
      margin-top: 10px;
      padding: 10px 15px;
      border-radius: 4px;
      background: rgba(255, 126, 40, .07);

      .common_header_tip_title {
        color: #db5609;
        font-size: 16px;
      }

      .common_header_tip_list {
        ul {
          margin: 0;
          padding: 0;

          li {
            padding: 2px 0;
            color: #db5609;
            font-size: 12px;
          }
        }
      }
    }
  }
</style>
