<!-- 富文本组件 -->
<template>
    <div class="clearfix">
        <!-- v-model:content="curValueData" -->
        <quill-editor
            ref="myQuillEditor"
            class="ql-editor"
            :options="toolbarOptions"
            @ready="onEditorReady($event)"
            @focus="onEditorFocus($event)"
            @blur="onEditorBlur($event)"
            @update:content="onEditorChange($event)"
            :style="{
                minHeight: '300px',
                height: height !== undefined ? height + 'px' : 'auto'
            }"
        />
    </div>
</template>
<script setup>
    import { ref, watch, computed } from 'vue';
    import { Quill, QuillEditor } from '@vueup/vue-quill';
    import '@vueup/vue-quill/dist/vue-quill.snow.css';

    const props = defineProps({
        value: {
            type: String,
            default: '<p>这是一段正文内容</p>',
        },
        toolbarOptions: {
            type: Object,
            default: {
                placeholder: '',
                modules: {
                    toolbar: [
                        ['bold', 'italic', 'underline', 'strike'],                      // 加粗 斜体 下划线 删除线
                        ['blockquote', 'code-block'],                                   // 引用  代码块
                        [{ 'header': 1 }, { 'header': 2 }],                             // 1、2 级标题
                        [{ 'list': 'ordered' }, { 'list': 'bullet' }],                  // 有序、无序列表
                        [{ 'script': 'sub' }, { 'script': 'super' }],                   // 上标/下标
                        [{ 'indent': '-1' }, { 'indent': '+1' }],                       // 缩进
                        [{ 'direction': 'rtl' }],                                       // 文本方向
                        [{ 'size': ['12px', false, '16px', '18px', '20px', '30px'] }],  // 字体大小
                        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],                      // 标题
                        [{ 'color': [] },{ 'background': [] }],                         // 字体颜色、字体背景颜色
                        [                                                               // 字体种类
                            { 'font': [false, 'SimSun', 'SimHei', 'Microsoft-YaHei', 'KaiTi', 'FangSong', 'Arial'] }
                        ],
                        [{ 'align': [] }],                                              // 对齐方式
                        ['clean'],                                                      // 清除文本格式
                        ['link', 'image', 'video'],                                     // 链接、图片、视频
                    ],
                },
            },
        },
        height: {
            type: Number,
            default: undefined,
        }
    });

    const myQuillEditor = ref();

    const curValueData = ref('');

    // const valueData = computed(() => {
    //     return props.value;
    // })

    // watch(
    //     valueData,
    //     () => {
    //         curValueData.value = props.value;
    //         if (curValueData.value) {
    //             if (myQuillEditor.value && myQuillEditor.value.setHTML) {
    //                 myQuillEditor.value.setHTML(curValueData.value);
    //             } else {
    //                 setTimeout(() => {
    //                     if (myQuillEditor.value && myQuillEditor.value.setHTML) {
    //                         myQuillEditor.value.setHTML(curValueData.value);
    //                     }
    //                 }, 50)
    //             }
    //         }
    //     },
    //     { deep: true, immediate: true },
    // )

    const emit = defineEmits(['getRQContent']);

    // 准备富文本编辑器
    const onEditorReady = (quill) => {
        // console.log('on-ready', quill);
    };

    // 获得焦点事件
    const onEditorFocus = (quill) => {
        // console.log('on-focus', quill);
    };

    // 失去焦点事件
    const onEditorBlur = (quill) => {
        // console.log('on-blur', quill);
    };

    // 内容改变事件
    const onEditorChange = (quill) => {
        // console.log('on-change');
        curValueData.value = (quill.ops[0].insert && quill.ops[0].insert.trim()) ? myQuillEditor.value.getHTML() : '';
        emit('getRQContent', curValueData.value);
    };

</script>
<style lang="less">
.ql-snow.ql-toolbar button,
.ql-snow .ql-toolbar button,
.ql-snow .ql-picker {
    margin: 0;
}

.ql-toolbar.ql-snow+.ql-container.ql-snow {
    overflow: hidden;
}

.ql-container {
    padding: 0;
}

.ql-editor {
    min-height: 300px !important;
}
</style>
