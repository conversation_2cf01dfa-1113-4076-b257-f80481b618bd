<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader :title="L('秒杀活动管理')" />
      <Tabs type="card" v-model:activeKey="activeKey" v-if="enableFlag == 1&&!isFirstLoading">
        <TabPane key="1" :tab="L('秒杀活动')">
          <ALLList></ALLList>
        </TabPane>
        <TabPane key="2" :tab="L('已参加活动')">
          <JoinedList></JoinedList>
        </TabPane>
      </Tabs>
      <div v-if="enableFlag != 1&&!isFirstLoading">
        <div style="width: 100%;height:150px;"></div>
        <Empty :image="moudle_disable" :image-style="{ height: '80px' }" :description="L('秒杀活动模块暂未开启')"/>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'Promotion<PERSON>eckill',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, reactive, onMounted } from 'vue';
  import { Tabs, TabPane,Empty } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { getSettingListApi } from '/@/api/common/common';
  import { sucTip, failTip } from '@/utils/utils';
  import moudle_disable from '/src/assets/images/moudle_disable.png';
  import ALLList from './all_list.vue';
  import JoinedList from './joined_list.vue';
  import { useUserStore } from '/@/store/modules/user';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const activeKey = ref('1');
  const enableFlag = ref(0)
  const isFirstLoading = ref(true)
  const userStore = useUserStore();

  const str_info = ref('seckill_is_enable')

   //获取站点基本配置
  const getSetting = async () => {
    const res = await getSettingListApi({
      str: str_info.value,
    });
    if (res.state == 200 && res.data) {
      enableFlag.value = res.data[0].value;
      isFirstLoading.value = false
    } else {
      failTip(res.msg);
    }
  };

  onMounted(() => {
    // dev_supplier-start
    if(userStore.getStoreInfo.shopType&&userStore.getStoreInfo.shopType=='3'){
      str_info.value = 'supplier_seckill_is_enable'
    }else{
      str_info.value = 'seckill_is_enable'
    }
    // dev_supplier-end
    getSetting()
  });
</script>
<style lang="less">
  .coupon_home {
    padding: 10px;

    .coupon_home_back {
      width: 100%;
      min-height: calc(100vh - @header-height - 20px);
      padding: 10px;
      background-color: #fff;
    }
  }
</style>
