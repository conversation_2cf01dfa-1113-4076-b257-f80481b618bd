<template>
  <div class="section_padding add_seckill seckill">
    <div class="section_padding_back full_activity">
      <SldComHeader :title="L('秒杀活动')" />
      <div class="com_line" style="height: 1px"></div>
      <Spin :spinning="loading">
        <div class="add_coupon_height">
          <Form layout="inline" ref="formRef" :model="detail_info">
            <!-- 活动基本信息 start -->
            <CommonTitle :text="L('活动基本信息')" style="margin-top: 10px" />
            <div class="full_acm_activity flex_column_start_start">
              <!-- 活动名称 start -->
              <div class="item flex_row_start_start">
                <div class="left">{{ L('活动名称') }} </div>
                <div class="right right_show_content">
                  <Form.Item>
                    {{ detail.seckillName }}
                  </Form.Item>
                </div>
              </div>
              <!-- 活动名称 end -->
               <!-- 活动时间 start -->
               <div class="item flex_row_start_start">
                <div class="left">{{ L('活动时间') }} </div>
                <div class="right right_show_content">
                  <Form.Item>
                    {{ detail.startTime }} ~ {{ detail.endTime }}
                  </Form.Item>
                </div>
              </div>
              <!-- 活动时间 end -->
              <!-- 活动场次 start -->
              <div class="item flex_row_start_center">
                <div class="left"> <span style="color: red">*</span>{{ L('活动场次') }}</div>
                <div class="right">
                  <Form.Item name="stageId" style="width: 400px" :rules="[{
                    required: true,
                    message: L('请选择活动场次'),
                  }]">
                    <Select
                      :placeholder="L('请选择活动场次')"
                      :disabled="viewFlag"
                      style="width: 400px"
                      :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                      v-model:value="detail_info.stageId"
                    >
                      <Select.Option
                        v-for="(item, index) in activity_stages"
                        :key="index"
                        :value="item.stageId"
                        >{{ item.stageName }}</Select.Option
                      >
                    </Select>
                  </Form.Item>
                </div>
              </div>
              <!-- 活动场次 end -->
              <!-- 活动标签 start -->
              <div class="item flex_row_start_center">
                <div class="left"> <span style="color: red">*</span>{{ L('活动标签') }}</div>
                <div class="right">
                  <Form.Item name="labelId" style="width: 400px" :rules="[{
                    required: true,
                    message: L('请选择活动标签'),
                  }]">
                    <Select
                      :placeholder="L('请选择活动标签')"
                      :disabled="viewFlag"
                      style="width: 400px"
                      :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                      v-model:value="detail_info.labelId"
                    >
                      <Select.Option
                        v-for="(item, index) in activity_labels"
                        :key="index"
                        :value="item.labelId"
                        >{{ item.labelName }}</Select.Option
                      >
                    </Select>
                  </Form.Item>
                </div>
              </div>
              <!-- 活动标签 end -->
              <!-- 商品秒杀结束时间 start -->
              <div class="item flex_row_start_start">
                <div class="left">{{ L('商品秒杀结束时间') }}</div>
                <div class="right">
                  <Form.Item name="activityTime" style="width: 400px" :extra="L('设置后商品的秒杀结束时间以设置时间为准，若未设置秒杀时间，则本场秒杀结束时，商品同时结束秒杀。')">
                    <DatePicker
                    :showTime="{ format: 'HH:mm' }"
                      format="YYYY-MM-DD HH:mm:00"
                      v-model:value="detail_info.activityTime"
                      :placeholder="L('请选择商品秒杀结束时间')"
                      style="width: 400px"
                    />
                  </Form.Item>
                </div>
              </div>
              <!-- 商品秒杀结束时间 end -->
            </div>
            <!-- 活动基本信息 end -->
            <CommonTitle style="margin-top: 10px" :describe="L('提醒：至少添加一个商品,已参加其他平台活动或其他秒杀场次的商品不可参与该活动')" describeColor="#FF7F40">
              <template #button>
                  <div class="toolbar_btn" @click="addGoods">
                    <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" :fillColor="'rgb(255, 89, 8)'" />
                    <span style="margin-left: 4px">{{ L('添加活动商品') }}</span>
                  </div>
                </template>
            </CommonTitle>
            <div class="sele_goods" v-for="(item,indexs) in detail_info.selectedRows" :key="indexs">
              <img :src="del_seckill_goods" alt="" class="del_spu" @click="delSpu(item.goodsId)">
              <div class="goods_info flex_row_between_start">
                <div class="goods_info_left flex_row_start_start">
                  <div class="goods_img_wrap flex_row_center_center">
                    <img :src="item.goodsImage" alt="" class="goods_img">
                  </div>
                  <p class="goods_name">{{ item.goodsName }}</p>
                </div>
                <div class="goods_info_right flex_row_end_end">
                  <Popconfirm @confirm="(e) => {
                              batchConfirm(e, 'seckillPrice', item,indexs);
                            }">
                    <template #title>
                      <InputNumber :min="0.01" :max="9999999" :precision="2" style="width: 100%;" v-model:value="battchVal" @change="(e) => {handleFieldBattchChange(e, 'seckillPrice', item)}"></InputNumber>
                    </template>
                    <div class="batch_btn flex_row_center_center">
                      {{ L('批量设置秒杀价') }}
                    </div>
                  </Popconfirm>
                  <Popconfirm @confirm="(e) => {
                              batchConfirm(e, 'seckillStock', item,indexs);
                            }">
                    <template #title>
                      <InputNumber :min="1" :max="99999999" :precision="0" style="width: 100%;" v-model:value="battchVal" @change="(e) => {handleFieldBattchChange(e, 'seckillStock', item)}"></InputNumber>
                    </template>
                    <div class="batch_btn flex_row_center_center">
                      {{ L('批量设置秒杀库存') }}
                    </div>
                  </Popconfirm>
                  <Popconfirm @confirm="(e) => {
                              batchConfirm(e, 'upperLimit', item,indexs);
                            }">
                    <template #title>
                      <InputNumber :min="0" :max="99999999" :precision="0" style="width: 100%;" v-model:value="battchVal"  @change="(e) => {handleFieldBattchChange(e, 'upperLimit', item)}"></InputNumber>
                    </template>
                    <div class="batch_btn flex_row_center_center">
                      {{ L('批量设置限购数量') }}
                    </div>
                  </Popconfirm>
                  <div class="batch_btn flex_row_center_center">
                    <Checkbox @change="setAll($event, item)">
                      <span class="sel_all">{{ L('全部参与') }}</span>
                    </Checkbox>
                  </div>
                </div>
              </div>
              <BasicTable
                rowKey="productId"
                :columns="columns_spec"
                :dataSource="item.productList"
                size="'small'"
                :pagination="false"
                :bordered="true"
                :ellipsis="false"
                :canResize="false"
              >
                <template #headerCell="{ column }">
                  <template v-if="column.dataIndex == 'seckillPrice'">
                    <span><span style="color: #FF2929;font-size:13px;">*</span>{{ L('秒杀价') }}</span>
                  </template>
                  <template v-else-if="column.dataIndex == 'seckillStock'">
                    <span><span style="color: #FF2929;font-size:13px;">*</span>{{ L('秒杀库存') }}</span>
                  </template>
                  <template v-else-if="column.dataIndex == 'upperLimit'">
                    <div style="position: relative;">
                      <span>{{ L('限购数量') }}</span>
                      <Tooltip placement="bottom">
                        <template #title> {{ L('限制每个会员ID在本场活动中的购买数量') }} </template>
                        <div style="right: 0px;top: 2px;position: absolute;">
                          <AliSvgIcon iconName="iconwen" width="14px" height="14px" fillColor="#bfbbba" />
                        </div>
                      </Tooltip>
                    </div>
                  </template>
                  <template v-else>
                    {{ column.customTitle }}
                  </template>
                </template>
                <template #bodyCell="{ column, text, record,index }">
                  <template v-if="column.dataIndex === 'seckillPrice'">
                    <Form.Item
                      :name="['selectedRows', indexs, 'productList',index,'seckillPrice']"
                      :rules="record.state == 1?[
                        {
                          required: true,
                          message: L('该项必填'),
                        },
                      ]:[]"
                    >
                      <InputNumber
                        :max="9999999"
                        :min="0.01"
                        style="width: 400px !important"
                        :precision="2"
                        @change="(e) => {handleFieldChange(e, 'seckillPrice', record)}"
                        v-model:value="record.seckillPrice"
                      />
                    </Form.Item>
                  </template>
                  <template v-if="column.dataIndex === 'seckillStock'">
                    <Form.Item
                      :name="['selectedRows', indexs, 'productList', index, 'seckillStock']"
                      :rules="record.state == 1?[
                        {
                          required: true,
                          message: L('该项必填'),
                        },
                      ]:[]"
                    >
                      <InputNumber
                        :max="9999999"
                        :min="1"
                        style="width: 400px !important"
                        :precision="0"
                        @change="(e) => {handleFieldChange(e, 'seckillStock', record)}"
                        v-model:value="record.seckillStock"
                      />
                    </Form.Item>
                  </template>
                  <template v-if="column.dataIndex === 'upperLimit'">
                    <InputNumber
                      :max="99999999"
                      :min="0"
                      style="width: 400px !important"
                      :precision="0"
                      @change="(e) => {handleFieldChange(e, 'upperLimit', record)}"
                      v-model:value="record.upperLimit"
                    />
                  </template>
                  <template v-if="column.dataIndex === 'state'">
                    <Switch
                      @change="
                        (e) =>
                        handleFieldChange(e ? 1 : 2, 'state', record,index,indexs)
                      "
                      :checked="text == 1 ? true : false"
                    />
                  </template>
                </template>
              </BasicTable>
            </div>
            <div style="width: 100%; height: 60px; background: #fff"></div>
          </Form>
        </div>
        <div
          class="m_diy_bottom_wrap"
          :style="{ position: 'fixed', left: getRealWidth + 10 + 'px' }"
        >
          <div class="add_goods_bottom_btn" @click="goBack"> {{ L('返回') }} </div>
          <div class="add_goods_bottom_btn add_goods_bottom_btn_sel" @click="handleSaveAllData">
            {{ L('保存') }}
          </div>
        </div>
      </Spin>
    </div>
    <SldSelMoreLeftRightSeckillGoods 
      @confirm-event="seleGoods"
      @cancle-event="sldHandleCancle" 
      :width="1000"
      :selectedRow="sele_more_goods.info"
      :selectedRowKey="sele_more_goods.ids"
      :title="L('请选择商品(至少选择一个)')"
      :height='height - 400'
      :modalVisible="modalVisibleGoods"
      :extra="sele_more_goods"
      >

    </SldSelMoreLeftRightSeckillGoods>
  </div>
</template>
<script>
  export default {
    name: 'SeckillToAdd',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, reactive, onMounted } from 'vue';
  import {  Spin,Select,DatePicker,Form,Popconfirm,InputNumber,Checkbox,Tooltip,Switch } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import CommonTitle from '/@/components/CommonTitle/index.vue';
  import SldSelMoreLeftRightSeckillGoods from '/@/components/SldSelMoreLeftRightSeckillGoods/index.vue';
  import { BasicTable } from '/@/components/Table';
  import del_seckill_goods from '/src/assets/images/del_seckill_goods.png';
  import { getSeckilDetailApi,getSeckilStageListApi,getSeckillLabelListApi,getSeckilJoinSeckillApi } from '/@/api/promotion/seckill';
  import { useRoute, useRouter } from 'vue-router';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import { list_com_page_more,sucTip, failTip } from '/@/utils/utils';
  import { useUserStore } from '/@/store/modules/user';
  import { pageClose } from '/@/utils/utils';
  import { useMultipleTabStore } from '/@/store/modules/multipleTab';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const loading = ref(false);
  const route = useRoute();
  const tabStore = useMultipleTabStore();
  const router = useRouter();
  const userStore = useUserStore();
  const { getRealWidth } = useMenuSetting();

  const battchVal = ref('');//批量设置里面的值
  const activity_stages = ref([])//活动场次
  const activity_labels = ref([])//活动标签
  const modalVisibleGoods = ref(false);
  const formRef = ref();
  const height = ref(document.body.clientHeight)
  const detail = ref({})
  const sele_more_goods = ref({
    info: [],//选择的商品数组
    ids: [],//选择的商品id数组
    min_num: 1,//最小数量，0为不限制
  })
  const detail_info = ref({
    selectedRows:[],
    selectedRowKeys:[]
  })

  const columns_spec = ref([
    {
      title: L('SKU规格'),
      dataIndex: 'specValues',
      align: 'center',
      width: 100,
      customRender: ({ text, record, index }) => {
        return text ? text : L('默认');
      },
    },
    {
      title: L('原价(¥)'),
      dataIndex: 'productPrice',
      align: 'center',
      width: 100,
    },
    {
      title: L('库存'),
      dataIndex: 'productStock',
      align: 'center',
      width: 100,
    },
    {
      title: L('秒杀价'),
      dataIndex: 'seckillPrice',
      align: 'center',
      width: 100,
    },
    {
      title: L('秒杀库存'),
      dataIndex: 'seckillStock',
      align: 'center',
      width: 100,
    },
    {
      title: L('限购数量'),
      dataIndex: 'upperLimit',
      align: 'center',
      width: 100,
    },
    {
      title: L('参与'),
      dataIndex: 'state',
      align: 'center',
      width: 100,
    },
  ])

  // 获取详情
  const get_detail = async(id)=> {
    let res = await getSeckilDetailApi({ seckillId: id })
    if(res.state == 200){
      detail.value = res.data;
    }else{
      failTip(res.msg)
    }
  }

  //获取活动场次
  const get_activity_stage = async(id)=> {
    let res = await getSeckilStageListApi({ seckillId: id, pageSize: list_com_page_more })
    if(res.state == 200){
      activity_stages.value = res.data.list;
    }
  }

  //获取活动标签
  const get_activity_label = async(id)=> {
    let res = await getSeckillLabelListApi({ pageSize: list_com_page_more })
    if(res.state == 200){
      activity_labels.value = res.data.list.filter(item => item.isShow == 1);
    }
  }

  // 打开商品弹窗
  const addGoods = ()=> {
    modalVisibleGoods.value = true
  }

  // 关闭弹窗
  const sldHandleCancle = ()=>{
    modalVisibleGoods.value = false
  }

  // 弹窗点击确定
  const seleGoods = (selectedRowsP,selectedRowKeysP)=>{
    selectedRowsP.map((item, index) => {
      item.productList.map((child_item) => {
        child_item.goodsId = item.goodsId;
        child_item.state = 1;
      });
    });
    //如果多次选择的话，数据要保留之前的
    detail_info.value.selectedRowKeys.map((item) => {
      if (selectedRowKeysP.indexOf(item) > -1) {
        let pre_item_data = detail_info.value.selectedRows.filter(val => val.goodsId == item)[0];
        for (let i = 0; i < selectedRowsP.length; i++) {
          if (selectedRowsP[i].goodsId == item) {
            selectedRowsP[i] = { ...pre_item_data };
            break;
          }
        }
      }
    });
    sele_more_goods.value.ids = [...selectedRowKeysP];
    sele_more_goods.value.info = JSON.parse(JSON.stringify(selectedRowsP));
    detail_info.value.selectedRows = selectedRowsP
    detail_info.value.selectedRowKeys = selectedRowKeysP
    sldHandleCancle()
  }

  // 商品批量设置
  const batchConfirm = (e, type, val,indexs)=> {
    handleFieldBattchChange(battchVal.value, type, val)
    let sku_product_id = [];
    for (let i in detail_info.value.selectedRows) {
      if (detail_info.value.selectedRows[i].goodsId == val.goodsId) {
        detail_info.value.selectedRows[i].productList.map((item,index) => {
          item[type] = battchVal.value;
          formRef.value.clearValidate([['selectedRows', indexs, 'productList',index,type]]);
          sku_product_id.push(item.productId);
        });
        break;
      }
    }
    battchVal.value = ''
    
  }

  //批量设置
  const handleFieldBattchChange = (e, type, item)=> {
    //将sku里对应的数据抽成数组，排序，获取最小的值，批量设置的值不能大于最小的值
    let tmp_type_key = type == 'seckillPrice' ? 'productPrice' : 'productStock';
    let typeAarry = [];
    item.productList.map(val => {
      typeAarry.push(val[tmp_type_key]);
    });

    let minVal = typeAarry.sort()[0];
    if (e > minVal) {
      e = minVal;
    }
    battchVal.value = e
  }

  //全部参与事件
  const setAll = (e, val) => {
    let states = [];
    for (let i in detail_info.value.selectedRows) {
      if (detail_info.value.selectedRows[i].goodsId == val.goodsId) {
        detail_info.value.selectedRows[i].productList.map(item => {
          item.state = e.target.checked ? 1 : 0;
          states.push('state'+item.productId);
        });
        break;
      }
    }
  };
  
  //spec_data_table 表格编辑事件
  const handleFieldChange = (val, fieldName, record,index,indexs)=> {
    //秒杀库存和限购数量都不可以超过最大库存
    if ((fieldName == 'seckillStock' || fieldName == 'upperLimit') && val > record.productStock) {
      val = record.productStock;
    }
    //秒杀价格都不可以超过商品价格
    if (fieldName == 'seckillPrice' && val > record.productPrice) {
      val = record.productPrice;
    }
    let tar_sku_list = detail_info.value.selectedRows.filter(item => item.goodsId == record.goodsId);
    if (tar_sku_list.length > 0) {
      let tar_data = tar_sku_list[0].productList.filter(item => item.productId == record.productId);
      if (tar_data.length > 0) {
        tar_data[0][fieldName] = val;
      }
    }
    formRef.value.clearValidate([['selectedRows', indexs, 'productList',index,'seckillPrice'],['selectedRows', indexs, 'productList',index,'seckillStock']]);
  }

  //删除添加的商品spu
  const delSpu = (goodsId) => {
    detail_info.value.selectedRows = detail_info.value.selectedRows.filter(item => item.goodsId != goodsId);
    detail_info.value.selectedRowKeys = detail_info.value.selectedRowKeys.filter(item => item != goodsId);
    sele_more_goods.value.ids = [...detail_info.value.selectedRowKeys];
    sele_more_goods.value.info = JSON.parse(JSON.stringify(detail_info.value.selectedRows));
  };

  // 返回
  const goBack = () => {
    if (window.history && window.history.length == 1) {
      pageClose();
    } else {
      const { fullPath } = route;
      tabStore.closeTabByKey(fullPath, router);
      router.back();
    }
  };

  // 保存
  const handleSaveAllData = async()=> {
    try {
      formRef.value.validate().then(async (values) => {
        let params = {};
        //秒杀商品结束时间
        if (values.activityTime) {
          params.endTime = values.activityTime.format('YYYY-MM-DD HH:mm:00');
          delete values.activityTime;
        }
        //秒杀商品
        params.goodsInfoList = [];
        detail_info.value.selectedRows.map((item) => {
          let joined_sku_array = [];
          item.productList.map((child) => {
            if (child.state == 1) {
              joined_sku_array.push({
                productId: child.productId,
                seckillPrice: child.seckillPrice,
                seckillStock: child.seckillStock,
                upperLimit: child.upperLimit != undefined ? child.upperLimit : 0,
              });
            }
          });
          if (joined_sku_array.length > 0) {
            params.goodsInfoList.push({
              goodsId: item.goodsId,
              productInfoList: joined_sku_array,
            });
          }
        });
        if (params.goodsInfoList.length == 0) {
          failTip(L('请选择要参与活动的商品'));
          return false;
        }
        loading.value = true
        params.seckillId = route.query.id;// 秒杀活动id
        params.labelId = values.labelId;// 活动标签id
        params.stageId = values.stageId;// 秒杀场次id
        let res = await getSeckilJoinSeckillApi(params)
        if(res.state == 200){
          loading.value = false
          sucTip(res.msg);
          userStore.setDelKeepAlive([route.name,'PromotionSeckill'])
          setTimeout(() => {
            goBack();
          }, 500);
        }else{
          loading.value = false
          failTip(res.msg)
        }
      })
    } catch (error) {
      console.info(error,'error')
    }
  }

  onMounted(() => {
    if (route.query.id != undefined && Number(route.query.id) > 0) {
      get_detail(route.query.id);
    }
    get_activity_stage(route.query.id)
    get_activity_label()
  });
</script>
<style lang="less">
@import './style/add_seckill.less';
</style>
