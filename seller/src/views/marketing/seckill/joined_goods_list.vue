<template>
  <div class="section_padding seckill_goods_lists">
    <div class="section_padding_back">
      <SldComHeader :title="L('秒杀活动商品')" :back="true" />
      <div class="com_line" style="height: 1px"></div>
      <BasicTable @register="registerTable" rowKey="couponId">
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex == 'mainImage'">
            <div class="goods_info com_flex_row_flex_start">
              <div class="goods_img" style="border: none">
                <Popover placement="rightTop">
                  <template #content>
                    <div style="width: 200px;height:200px;display:flex;align-items:center;justify-content:center;">
                      <img :src="text" alt="" style="max-width: 100%; max-height: 100%" />
                    </div>
                  </template>
                  <div class="business_load_img">
                    <img :src="text" alt="" />
                  </div>
                </Popover>
              </div>
              <div class="com_flex_column_space_between goods_detail">
                <div
                  class="goods_name"
                  style="margin-top: 6px; white-space: initial"
                  :title="record.goodsName"
                >
                  {{ record.goodsName }}
                </div>
              </div>
            </div>
          </template>
          <template v-if="column.dataIndex === 'startTime'">
            <div class="voucher_time_wrap" :title="text + '~' + record.endTime">
              <p>{{ text }}</p>
              <p>~</p>
              <p>{{ record.endTime }}</p>
            </div>
          </template>
          <template v-if="column.dataIndex === 'action'">
            <TableAction
              :actions="[
                {
                  onClick: viewSpec.bind(null, record),
                  label: L('查看SKU'),
                },
              ]"
            />
          </template>
        </template>
      </BasicTable>
    </div>
    <SldModal
      :width="900"
      :title="L('商品SKU')"
      :visible="modalVisible"
      :height="300"
      :content="operateContent"
      :showFoot="false"
      @cancle-event="handleModalCancle"
    />
  </div>
</template>
<script>
  export default {
    name: 'SeckillJoinedGoodsList',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, reactive, onMounted } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { useRoute } from 'vue-router';
  import { Popover } from 'ant-design-vue';
  import { sucTip, failTip } from '@/utils/utils';
  import { getSeckilGoodsJoinListApi, getSeckilJoinProductListApi } from '/@/api/promotion/seckill';
  import SldModal from '/@/components/SldModal/index.vue';
  import { validatorEmoji } from '/@/utils/validate';
  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const route = useRoute();

  // 参数
  const id = ref(route.query?.id);

  const modalVisible = ref(false);

  const operateContent = ref([
    {
      type: 'show_content_table', //展示表格，没有分页，不能勾选
      name: 'bind_goods',
      width: 880,
      wrapperCol: { flex: 1, style: 'margin-bottom:-19px' },
      label: ``,
      content: '',
      data: [],
      scrollHeight: false, // 设置纵向滚动，也可用于指定滚动区域的高，可以设置为像素值
      columns: [
        {
          title: ' ',
          align: 'center',
          width: 100,
          minWidth: 100,
          customRender: ({ text, record, index }) => {
            return `${index + 1}`;
          },
        },
        {
          title: L('商品规格'),
          dataIndex: 'specValues',
          align: 'center',
          width: 200,
          customRender: ({ text }) => {
            return text ? text : L('默认');
          },
          minWidth: 200,
        },
        {
          title: L('原价(元)'),
          dataIndex: 'productPrice',
          align: 'center',
          width: 200,
          minWidth: 200,
        },
        {
          title: L('商品库存'),
          dataIndex: 'productStock',
          align: 'center',
          width: 200,
          minWidth: 200,
        },
        {
          title: L('秒杀价(元)'),
          dataIndex: 'seckillPrice',
          align: 'center',
          width: 200,
          minWidth: 200,
        },
        {
          title: L('秒杀库存'),
          dataIndex: 'seckillStock',
          align: 'center',
          width: 200,
          minWidth: 200,
        },
        {
          title: L('限购数量'),
          dataIndex: 'upperLimit',
          align: 'center',
          width: 200,
          minWidth: 200,
        },
      ],
      rowKey: 'productId',
      scroll: false,
    },
  ]);

  const searchInfo = ref({
    seckillId: id.value,
  });
  const columns = reactive({
    data: [
      {
        title: L('商品信息'),
        dataIndex: 'mainImage',
        align: 'center',
        width: 250,
      },
      {
        title: L('参加场次'),
        dataIndex: 'stageName',
        align: 'center',
        width: 100,
      },
      {
        title: L('场次时间'),
        dataIndex: 'startTime',
        align: 'center',
        width: 150,
      },
      {
        title: L('活动标签'),
        dataIndex: 'labelName',
        align: 'center',
        width: 100,
      },
      {
        title: L('审核状态'),
        dataIndex: 'stateValue',
        align: 'center',
        width: 100,
      },
      {
        title: L('拒绝原因'),
        dataIndex: 'remark',
        align: 'center',
        width: 100,
        customRender: ({ text, record }) => {
          return text?text:'--';
        },
      },
    ],
  });

  const searchFormSchema = reactive({
    data: [
      {
        component: 'Input',
        label: L('商品名称'),
        field: 'goodsName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: L('请输入商品名称'),
        },
        rules: [
          {
            validator: async (rule, value) => {
              await validatorEmoji(rule, value);
            },
          },
        ],
      },
      {
        colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
        component: 'RangePicker',
        label: L('场次时间'),
        field: '[startTime,endTime]',
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: [L('开始时间'), L('结束时间')],
        },
      },
      {
        component: 'Select',
        label: L('审核状态'),
        field: 'state',
        labelWidth: 90,
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: L('请选择审核状态'),
          options: [
            { value: '', label: L('全部') },
            { value: '1', label: L('待审核') },
            { value: '2', label: L('审核通过') },
            { value: '3', label: L('审核拒绝') },
          ],
        },
      },
    ],
  });

  // 表格数据
  const [registerTable] = useTable({
    // 请求接口
    api: getSeckilGoodsJoinListApi,
    // 表头
    columns: columns.data,
    immediate: true,
    rowKey: 'goodsId',
    // 参数
    searchInfo: searchInfo.value,
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: searchFormSchema.data,
    },
    inset: false,
    // 点击搜索前处理的参数
    beforeFetch(values) {
      values.startTime = values.startTime
        ? values.startTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
      return values;
    },
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    ellipsis: false,
    // 是否显示序号列
    showIndexColumn: true,
    // 表格右侧操作列配置
    actionColumn: {
      width: 160,
      title: L('操作'),
      dataIndex: 'action',
      // slots: { customRender: 'action' },
    },
  });

  // 查看sku
  const viewSpec = async (record) => {
    try {
      let res = await getSeckilJoinProductListApi({
        seckillId: route.query?.id,
        goodsId: record.goodsId,
        stageId: record.stageId,
      });
      if (res.state == 200) {
        operateContent.value[0].data = [];
        operateContent.value[0].data = res.data;
        modalVisible.value = true;
      } else {
        failTip(res.msg);
      }
    } catch (error) {
      console.log(error, 'error');
    }
  };

  //关闭弹框
  const handleModalCancle = () => {
    modalVisible.value = false;
    operateContent.value[0].data = [];
  };
  onMounted(() => {});
</script>
<style lang="less">
  p {
    margin-bottom: 0;
  }
  .business_load_img {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    margin: auto;

    img {
      max-width: 100%;
      max-height: 100%;
    }
  }
</style>
