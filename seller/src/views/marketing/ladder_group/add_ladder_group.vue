<template>
  <div class="section_padding add_ladder_group add_ladder_group_one">
    <div class="section_padding_back full_activity">
      <SldComHeader :title="L('阶梯团活动')" />
      <div class="com_line" style="height: 1px"></div>
      <Spin :spinning="loading">
        <div class="add_coupon_height">
          <Form layout="inline" ref="formRef" :model="detail">
            <!-- 活动基本信息 start -->
            <CommonTitle :text="L('活动基本信息')" style="margin-top: 10px" />
            <div class="full_acm_activity flex_column_start_start">
              <!-- 活动名称 start -->
              <div class="item flex_row_start_start">
                <div class="left"><span style="color: red">*</span>{{ L('活动名称') }} </div>
                <div class="right">
                  <Form.Item
                    :validateFirst="true"
                    :extra="L('最多输入20个字')"
                    name="groupName"
                    style="width: 400px"
                    :rules="[
                      {
                        required: true,
                        whitespace: true,
                        message: L('请输入活动名称'),
                      },
                      {
                        validator: async (rule, value) => {
                          await validatorEmoji(rule, value);
                        },
                      },
                    ]"
                  >
                    <Input
                      :maxLength="20"
                      style="width: 400px !important"
                      :placeholder="L('请输入活动名称')"
                      v-model:value="detail.groupName"
                    />
                  </Form.Item>
                </div>
              </div>
              <!-- 活动名称 end -->
               <!-- 活动时间 start -->
               <div class="item flex_row_start_start">
                <div class="left"><span style="color: red">*</span>{{ L('活动时间') }} </div>
                <div class="right">
                  <Form.Item
                    name="activityTime"
                    style="width: 400px"
                    :rules="[
                      {
                        required: true,
                        message: L('请选择活动时间'),
                      },
                    ]"
                  >
                    <RangePicker
                      style="width: 400px"
                      :format="'YYYY-MM-DD HH:mm:00'"
                      v-model:value="detail.activityTime"
                      :disabledDate="
                        (currentDate) => currentDate && currentDate < moment().subtract(1, 'days')
                      "
                      :showTime="{ format: 'HH:mm' }"
                      :placeholder="[L('开始时间'), L('结束时间')]"
                      :getPopupContainer="
                        (triggerNode) => {
                          return triggerNode.parentNode;
                        }
                      "
                    />
                  </Form.Item>
                </div>
              </div>
              <!-- 活动时间 end -->
              <!-- 活动标签 start -->
              <div class="item flex_row_start_center">
                <div class="left"> <span style="color: red">*</span>{{ L('活动标签') }}</div>
                <div class="right">
                  <Form.Item name="labelId" style="width: 400px" :rules="[{
                    required: true,
                    message: L('请选择活动标签'),
                  }]">
                    <Select
                      :placeholder="L('请选择活动标签')"
                      style="width: 400px"
                      :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                      v-model:value="detail.labelId"
                    >
                      <Select.Option
                        v-for="(item, index) in activity_labels"
                        :key="index"
                        :value="item.labelId"
                        >{{ item.labelName }}</Select.Option
                      >
                    </Select>
                  </Form.Item>
                </div>
              </div>
              <!-- 活动标签 end -->
              <!-- 尾款时间 start -->
              <div class="item flex_row_start_start">
                <div class="left"><span style="color: red">*</span>{{ L('尾款时间') }} </div>
                <div class="right">
                  <Form.Item
                    :extra="L('活动结束多少小时内需要支付尾款')"
                    name="balanceTime"
                    class="balanceTime_box"
                    style="width: 330px"
                    :rules="[
                      {
                        required: true,
                        message: L('请输入尾款时间'),
                      },
                    ]"
                  >
                    <div class="flex_row_start_center">
                      
                      <InputNumber :min="1" :max="23*9" :precision="0" style="width: 150px !important;" v-model:value="detail.balanceTime" :placeholder="L('请输入尾款时间')"></InputNumber>
                      <span :style="{
                            display: 'inline-block',
                            marginLeft: '5px',
                            color: 'rgba(0, 0, 0, 0.65)',
                          }">{{ L('小时') }}</span>
                    </div>
                  </Form.Item>
                </div>
              </div>
              <!-- 尾款时间 end -->
              <!-- 限购数量 start -->
              <div class="item flex_row_start_start">
                <div class="left"><span style="color: red">*</span>{{ L('限购数量') }} </div>
                <div class="right">
                  <Form.Item
                    :extra="L('每位会员限制购买的数量，0代表不限制')"
                    name="buyLimitNum"
                    class="buyLimit_box"
                    style="width: 300px"
                    :rules="[
                      {
                        required: true,
                        message: L('请输入限购数量'),
                      },
                    ]"
                  >
                    <div class="flex_row_start_center">
                      
                      <InputNumber :min="0" :max="999" :precision="0" style="width: 150px !important;" v-model:value="detail.buyLimitNum" :placeholder="L('请输入限购数量')"></InputNumber>
                    </div>
                  </Form.Item>
                </div>
              </div>
              <!-- 限购数量 end -->
              <!-- 退还定金 start -->
              <div class="item flex_row_start_start">
                <div class="left"><span style="color: red">*</span>{{ L('退还定金') }} </div>
                <div class="right">
                  <Form.Item
                    :extra="L('选择是代表由会员导致的退款退定金，否则不退定金')"
                    name="isRefundDeposit"
                    style="width: 450px"
                  >
                    <RadioGroup
                          v-model:value="detail.isRefundDeposit"
                        size="small"
                      >
                        <Radio :value="1">{{ L('是') }}</Radio>
                        <Radio :value="0">{{ L('否') }}</Radio>
                      </RadioGroup>
                  </Form.Item>
                </div>
              </div>
              <!-- 退还定金 end -->
              <!-- 阶梯优惠方式 start -->
              <div class="item flex_row_start_start">
                <div class="left"><span style="color: red">*</span>{{ L('阶梯优惠方式') }} </div>
                <div class="right">
                  <Form.Item
                    name="discountType"
                    style="width: 450px"
                  >
                    <RadioGroup
                          v-model:value="detail.discountType"
                        size="small"
                        @change="handlePromotionType"
                      >
                        <Radio :value="1">{{ L('阶梯价格') }}</Radio>
                        <Radio :value="2">{{ L('阶梯折扣') }}</Radio>
                      </RadioGroup>
                  </Form.Item>
                </div>
              </div>
              <!-- 阶梯优惠方式 end -->
            </div>
            <!-- 活动基本信息 end -->
            <CommonTitle style="margin-top: 10px" :describe="L('提醒：已参加活动的商品不可参与该活动')" describeColor="#FF7F40">
              <template #button>
                  <div class="toolbar_btn" @click="addGoods">
                    <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" :fillColor="'rgb(255, 89, 8)'" />
                    <span style="margin-left: 4px">{{ L('添加活动商品') }}</span>
                  </div>
                </template>
            </CommonTitle>
            <div class="sele_goods" v-for="(item,indexs) in detail.selectedRows" :key="indexs">
              <img :src="del_seckill_goods" alt="" class="del_spu" @click="delSpu(item.goodsId)">
              <div class="goods_info flex_row_between_start">
                <div class="goods_info_left flex_row_start_start">
                  <div class="goods_img_wrap flex_row_center_center">
                    <img :src="item.goodsImage" alt="" class="goods_img">
                  </div>
                  <p class="goods_name">{{ item.goodsName }}</p>
                </div>
              </div>
              <div class="flex_column_start_start ladder_part">
                <div class="deposit_part">
                  <div class="flex_row_start_center">
                    <span :style="{
                            display: 'inline-block',
                            marginRight: '5px',
                            color: '#333',
                            fontSize: '13px',
                          }">
                          <span style="color: red">*</span>{{ L('预付定金：') }}
                    </span>
                    <Form.Item
                      :name="['selectedRows', indexs, 'advanceDeposit']"
                      style="width: 100px;margin-right: 5px;"
                      :rules="[
                        {
                          required: true,
                          message: L('该项必填'),
                        },
                      ]"
                    >
                      <InputNumber :min="1" :max="1000" :precision="0" style="width: 100px !important;" v-model:value="item.advanceDeposit" placeholder=""></InputNumber>
                    </Form.Item>
                    <span :style="{
                        display: 'inline-block',
                        marginLeft: '5px',
                        color: 'rgba(0, 0, 0, 0.65)',
                      }">{{ L('元') }}</span>
                      <span class="tip">{{ L('预付定金不能大于最小阶梯价格') }}</span>
                  </div>
                </div>
                <div class="ladder_item" v-for="(item_ladder_num, index_ladder_num) in item.ladder_data" :key="index_ladder_num">
                  <div class="flex_row_start_center">
                    <template v-if="index_ladder_num == 0">
                      <span style="color: #333;font-size: 13px;">
                        <span style="color: red">*</span>{{ L('参团人数：') }}
                      </span>
                    </template>
                      <span :style="{
                        display: 'inline-block',
                        marginRight: '5px',
                        color: '#333',
                        fontSize: '13px',
                        marginLeft: index_ladder_num > 0 ? '70px' : 0,
                      }">
                        <span>{{L('第')}}{{item_ladder_num.num}}{{ L('阶梯') }}</span>
                      </span>
                      <Form.Item
                      :name="['selectedRows', indexs,'ladder_data',index_ladder_num,'value']"
                      style="width: 100px;margin-right: 5px;"
                      :rules="[
                        {
                          required: true,
                          message: L('该项必填'),
                        },
                      ]"
                    >
                      <InputNumber :min="1" :max="1000" :precision="0" style="width: 100px !important;" v-model:value="item_ladder_num.value" placeholder="" @blur="handleLadderNum($event,indexs, index_ladder_num)"></InputNumber>
                    </Form.Item>
                    <span :style="{
                        display: 'inline-block',
                        marginLeft: '5px',
                        color: 'rgba(0, 0, 0, 0.65)',
                      }">{{ L('人') }}</span>
                    <div class="del" v-if="(index_ladder_num == (item.ladder_data.length - 1) && index_ladder_num > 0)" @click="delLadder(indexs, index_ladder_num)">
                      <AliSvgIcon iconName="iconshanchu7" width="16px" height="16px" fillColor="#e20e0e" />
                    </div>
                  </div>
                </div>
                <div class="add_ladder flex_row_start_center" v-if="item.ladder_data.length < 3">
                  <a class="flex_row_center_center btn" @click="addLadder(indexs)">{{ L('添加阶梯') }}</a>
                  <span class="tip">{{ L('最多添加三级阶梯') }}</span>
                </div>
              </div>
              <BasicTable
                rowKey="productId"
                :columns="columns_spec"
                :dataSource="item.productList"
                size="'small'"
                :pagination="false"
                :bordered="true"
                :ellipsis="false"
                :canResize="false"
              >
                <template #headerCell="{ column}">
                  <template v-if="column.dataIndex == `ladderPrice${column.index + 1}`">
                    <div class="flex_row_center_center">
                      <span>
                        <span style="color: #FF2929;font-size:13px;">*</span>
                        {{ column.customTitle }}
                      </span>
                      <Popconfirm @confirm="(e) => {
                              batchConfirm(e,column.index,indexs);
                            }">
                      <template #title>
                        <InputNumber :min="0.01" :max="9999999" :precision="2" style="width: 100%;" v-model:value="battchVal"  :placeholder="L('批量设置价格')" @change="(e) => {handleFieldBattchChange(e, column.index,indexs)}"></InputNumber>
                      </template>
                      <div class="flex_row_center_center batch_btn">
                        <AliSvgIcon iconName="iconbianji2" width="16px" height="16px" fillColor="#5586f6" />
                      </div>
                    </Popconfirm>
                    </div>
                  </template>
                  <template v-else-if="column.dataIndex == 'state'">
                    <Checkbox @change="setAll($event, item,indexs)">
                      <span class="sel_all">{{ L('参与') }}</span>
                    </Checkbox>
                  </template>
                  <template v-else>
                    {{ column.customTitle }}
                  </template>
                </template>
                <template #bodyCell="{ column, text, record,index }">
                  <template v-if="column.dataIndex === `ladderPrice${column.index + 1}`">
                    <Form.Item
                      v-if="detail.discountType==1"
                      :name="['selectedRows', indexs, 'productList',index,`ladderPrice${column.index + 1}`]"
                      :rules="record.state == 1?[
                        {
                          required: true,
                          message: L('该项必填'),
                        },
                      ]:[]"
                    >
                      <InputNumber
                        :max="Number(record[`ladderPrice${column.index}`] != undefined && record[`ladderPrice${column.index}`] ? (record[`ladderPrice${column.index}`] * 1 - 0.01).toFixed(2) : (record.productPrice * 1 - 0.01).toFixed(2))"
                        :min="0.01"
                        style="width: 400px !important"
                        :precision="2"
                        @change="(e) => {handleFieldBlur(e, `ladderPrice${column.index + 1}`, record)}"
                        v-model:value="record[`ladderPrice${column.index + 1}`]"
                      />
                    </Form.Item>
                    <Form.Item
                      v-else
                      :name="['selectedRows', indexs, 'productList',index,`ladderPrice${column.index + 1}`]"
                      :rules="record.state == 1?[
                        {
                          required: true,
                          message: L('该项必填'),
                        },
                      ]:[]"
                    >
                      <InputNumber
                        :max="Number(record[`ladderPrice${column.index}`] != undefined && record[`ladderPrice${column.index}`] ? (record[`ladderPrice${column.index}`] * 1 - 0.1).toFixed(2) : 9.9)"
                        :min="0.1"
                        style="width: 400px !important"
                        :precision="1"
                        @change="(e) => {handleFieldBlur(e, `ladderPrice${column.index + 1}`, record)}"
                        v-model:value="record[`ladderPrice${column.index + 1}`]"
                      />
                    </Form.Item>
                  </template>
                  <template v-if="column.dataIndex === 'state'">
                    <Switch
                      @change="
                        (e) =>
                        handleFieldChange(e ? 1 : 2, 'state', record,index,indexs)
                      "
                      :checked="text == 1 ? true : false"
                    />
                  </template>
                </template>
              </BasicTable>
            </div>
            <div style="width: 100%; height: 60px; background: #fff"></div>
          </Form>
        </div>
        <div
          class="m_diy_bottom_wrap"
          :style="{ position: 'fixed', left: getRealWidth + 10 + 'px' }"
        >
          <div class="add_goods_bottom_btn" @click="goBack"> {{ L('返回') }} </div>
          <div class="add_goods_bottom_btn add_goods_bottom_btn_sel" @click="handleSaveAllData">
            {{ L('保存') }}
          </div>
        </div>
      </Spin>
    </div>
    <SldSelMoreLeftRightSeckillGoods 
      @confirm-event="seleGoods"
      @cancle-event="sldHandleCancle" 
      :width="1000"
      :selectedRow="sele_more_goods.info"
      :selectedRowKey="sele_more_goods.ids"
      :title="L('请选择商品(只能选择一个)')"
      :height='height - 400'
      :modalVisible="modalVisibleGoods"
      :extra="sele_more_goods"
      >

    </SldSelMoreLeftRightSeckillGoods>
  </div>
</template>
<script setup>
  import { getCurrentInstance, ref, reactive, onMounted } from 'vue';
  import moment from 'moment';
  import dayjs from 'dayjs';
  import { Spin,Select,Form,Popconfirm,InputNumber,Checkbox,Switch,Input,RangePicker,RadioGroup,Radio } from 'ant-design-vue';
  import { num_to_num,sucTip, failTip } from '/@/utils/utils';
  import { validatorEmoji } from '/@/utils/validate';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import CommonTitle from '/@/components/CommonTitle/index.vue';
  import SldSelMoreLeftRightSeckillGoods from '/@/components/SldSelMoreLeftRightSeckillGoods/index.vue';
  import { BasicTable } from '/@/components/Table';
  import del_seckill_goods from '/src/assets/images/del_seckill_goods.png';
  import { getLadderLabelListApi,getLadderAddApi,getLadderUpdateApi,getLadderDetailApi } from '/@/api/promotion/ladder_group';
  import { useRoute, useRouter } from 'vue-router';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import { list_com_page_more } from '/@/utils/utils';
  import { useUserStore } from '/@/store/modules/user';
  import { pageClose } from '/@/utils/utils';
  import { useMultipleTabStore } from '/@/store/modules/multipleTab';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const loading = ref(false);
  const tabStore = useMultipleTabStore();
  const userStore = useUserStore();
  const route = useRoute();
  const router = useRouter();
  const { getRealWidth } = useMenuSetting();
  const leaderPromotion = ref(0)//团长优惠是否开启，默认为0 未开启，1为开启

  const battchVal = ref('');//批量设置里面的值
  const activity_stages = ref([])//活动场次
  const activity_labels = ref([])//活动标签
  const modalVisibleGoods = ref(false);
  const query = ref(route.query);
  const selectedRows = ref([])
  const selectedRowKeys = ref([])//selectedRows的key
  const formRef = ref();
  const height = ref(document.body.clientHeight)
  const sele_more_goods = ref({
    info: [],//选择的商品数组
    ids: [],//选择的商品id数组
    max_num: 1,//最小数量，0为不限制
  })
  const detail = ref({
    selectedRows:[],
    selectedRowKeys:[],
    isRefundDeposit:1,
    discountType:1,
    balanceTime:24,
    buyLimitNum:0
  })

  const columns_spec_spellPrice = ref({
      title: L('团长优惠价(¥)'),
      dataIndex: 'leaderPrice',
      align: 'center',
      width: 100,
  })

  const columns_spec = ref([
    {
      title: L('SKU规格'),
      dataIndex: 'specValues',
      align: 'center',
      width: 100,
      customRender: ({ text, record, index }) => {
        return text ? text : L('默认');
      },
    },
    {
      title: L('原价(¥)'),
      dataIndex: 'productPrice',
      align: 'center',
      width: 100,
    },
    {
      title: L('库存'),
      dataIndex: 'productStock',
      align: 'center',
      width: 100,
    },
    {
      title: L('参与'),
      dataIndex: 'state',
      align: 'center',
      width: 100,
    },
  ])

  //添加阶梯
  const addLadder = (index)=> {
    let cur_data = detail.value.selectedRows[index].ladder_data;
    let cur_length = cur_data.length;
    cur_data.push({
      num: num_to_num()[cur_length + 1],
      name: 'ladder_num_' + (cur_length + 1),
      value: '',
    });
    detail.value.selectedRows[index].productList.map((pro_item, pro_index) => {
      pro_item['ladderPrice' + (cur_length + 1)] = '';
    });
    detail.value.discountType == 1 ? initColumnPrice() : initColumnDiscount();
  }

  //删除阶梯
  const delLadder = (index, ladder_index)=> {
    detail.value.selectedRows[index].productList.map((pro_item) => {
      pro_item['ladderPrice' + (ladder_index + 1)] = '';
    });
    detail.value.selectedRows[index].ladder_data = detail.value.selectedRows[index].ladder_data.filter((item, index) => index != ladder_index);
    formRef.value.resetFields([['selectedRows', index, 'ladder_data','ladder_num_' + ladder_index]]);
    detail.value.discountType == 1 ? initColumnPrice() : initColumnDiscount();
  }

  const handleLadderNum = (e, index, ladder_index)=> {
    e = e.target.value;
    //需要对每个阶梯做最大值限制
    if (e * 1 > 1000 - (2 - ladder_index * 1)) {
      e = 1000 - (2 - ladder_index * 1);
    }
    detail.value.selectedRows[index].ladder_data[ladder_index].value = e;
    let resetFields = [];
    detail.value.selectedRows[index].ladder_data.map(item => {
      resetFields.push(['selectedRows', index,'ladder_data',ladder_index,'value']);
    });
    formRef.value.clearValidate(resetFields);
  }

  // 获取详情
  const get_detail = async(id)=> {
    let res = await getLadderDetailApi({ groupId: id })
    if(res.state == 200){
      loading.value = false
      res.data.selectedRowKeys = []
      res.data.selectedRows = []
      let selectedRows = []
      let selectedRowKeys = []
      if (res.data.startTime) {
        res.data.activityTime = [
          dayjs(res.data.startTime, 'YYYY-MM-DD HH:mm:ss'),
          dayjs(res.data.endTime, 'YYYY-MM-DD HH:mm:ss'),
        ];
      }
      //初始化ladder_data数据
      selectedRows.push({
        ladder_data: [],
        productList: [],
        advanceDeposit: res.data.goodsInfo.advanceDeposit,
        goodsImage: res.data.goodsInfo.goodsImage,
        goodsName: res.data.goodsInfo.goodsName,
      });
      res.data.ruleList.map(item => {
        selectedRows[0].ladder_data.push({
          num: num_to_num()[item.ladderLevel],
          name: 'ladder_num_' + (item.ladderLevel),
          value: item.joinGroupNum,
        });
      });
      res.data.goodsInfo.productList.map(item => {
        let temp = {};
        temp.state = 1;
        temp.productId = item.productId;
        temp.goodsId = item.goodsId;
        temp.productPrice = item.productPrice;
        temp.specValues = item.specValues;
        temp.productStock = item.stock;
        temp.ladderPrice1 = item.ladderPrice1;
        if (item.ladderPrice2 != undefined && item.ladderPrice2) {
          temp.ladderPrice2 = item.ladderPrice2;
        }
        if (item.ladderPrice3 != undefined && item.ladderPrice3) {
          temp.ladderPrice3 = item.ladderPrice3;
        }
        selectedRows[0].productList.push(temp);
      });
      res.data.selectedRowKeys = selectedRowKeys
      res.data.selectedRows = selectedRows
      detail.value = res.data;
      if (detail.value.discountType == 1) {
        initColumnPrice();
      } else {
        initColumnDiscount();
      }
    }else{
      failTip(res.msg)
    }
  }


  //获取活动标签
  const get_activity_label = async(id)=> {
    let res = await getLadderLabelListApi({ pageSize: list_com_page_more })
    if(res.state == 200){
      activity_labels.value = res.data
    }
  }

  // 打开商品弹窗
  const addGoods = ()=> {
    modalVisibleGoods.value = true
  }

  // 关闭弹窗
  const sldHandleCancle = ()=>{
    modalVisibleGoods.value = false
  }

  // 弹窗点击确定
  const seleGoods = (selectedRowsP,selectedRowKeysP)=>{
    selectedRowsP.map((item, index) => {
      item.productList.map((child_item) => {
        child_item.goodsId = item.goodsId;
        child_item.state = 1;
        child_item['ladderPrice1'] = '';
      });
      item.advanceDeposit = '';
      item.ladder_data = [{
        num: '一',
        name: 'ladder_num_1',
        value: '',
      }];
    });
    //如果多次选择的话，数据要保留之前的
    detail.value.selectedRowKeys.map((item) => {
      if (selectedRowKeysP.indexOf(item) > -1) {
        let pre_item_data = detail.value.selectedRows.filter(val => val.goodsId == item)[0];
        for (let i = 0; i < selectedRowsP.length; i++) {
          if (selectedRowsP[i].goodsId == item) {
            selectedRowsP[i] = { ...pre_item_data };
            break;
          }
        }
      }
    });
    sele_more_goods.value.ids = [...selectedRowKeysP];
    sele_more_goods.value.info = JSON.parse(JSON.stringify(selectedRowsP));
    detail.value.selectedRows = selectedRowsP
    detail.value.selectedRowKeys = selectedRowKeysP
    detail.value.discountType == 1 ? initColumnPrice() : initColumnDiscount();
    sldHandleCancle()
  }

  //处理优惠类型
  const handlePromotionType = (e)=> {
    if (e.target.value == 1) {
      //阶梯价格
      initColumnPrice();
    } else {
      //阶梯折扣
      initColumnDiscount();
    }
  }

  //阶梯价格
  const initColumnPrice = ()=> {
    columns_spec.value = columns_spec.value.filter(item=>item.dataIndex.indexOf('ladderPrice') == -1);
    let tar_index = 3;
    if (detail.value.selectedRows.length > 0) {
      detail.value.selectedRows[0].ladder_data.forEach((item,index)=>{
        columns_spec.value.splice(tar_index,0,{
          title: L('第')+item.num+L('阶梯价格'),
          dataIndex: 'ladderPrice' + (index + 1),
          align: 'center',
          width: 100,
          index: index,
        })
        tar_index = tar_index + 1;
      })
      detail.value.selectedRows[0].productList.map((item, index) => {
        item.ladder_data = detail.value.selectedRows[0].ladder_data;
      });
      detail.value.selectedRows[0].productList.map((item,ind) => {
        detail.value.selectedRows[0].ladder_data.forEach((it,index)=>{
          let num =  Number(item[`ladderPrice${index}`] != undefined && item[`ladderPrice${index}`] ? (item[`ladderPrice${index}`] * 1 - 0.01).toFixed(2) : (item.productPrice * 1 - 0.01).toFixed(2))
          item['ladderPrice' + (index + 1)] = num > Number(item['ladderPrice' + (index + 1)]) ? item['ladderPrice' + (index + 1)] : num;
        })
      });
      
    }
  }

  //阶梯折扣
  const initColumnDiscount = ()=> {
    columns_spec.value = columns_spec.value.filter(item=>item.dataIndex.indexOf('ladderPrice') == -1);
    let tar_index = 3;
    if (detail.value.selectedRows.length > 0) {
      detail.value.selectedRows[0].ladder_data.forEach((item,index)=>{
        columns_spec.value.splice(tar_index,0,{
          title: L('第')+item.num+L('阶梯折扣'),
          dataIndex: 'ladderPrice' + (index + 1),
          align: 'center',
          width: 100,
          index: index,
        })
        tar_index = tar_index + 1;
      })
      detail.value.selectedRows[0].productList.map((item, index) => {
        item.ladder_data = detail.value.selectedRows[0].ladder_data;
      });
      detail.value.selectedRows[0].productList.map((item,ind) => {
        detail.value.selectedRows[0].ladder_data.forEach((it,index)=>{
          let num =  Number(item[`ladderPrice${index}`] != undefined && item[`ladderPrice${index}`] ? (item[`ladderPrice${index}`] * 1 - 0.1).toFixed(2) : 9.9)
          item['ladderPrice' + (index + 1)] = num > Number(item['ladderPrice' + (index + 1)]) ? item['ladderPrice' + (index + 1)] : num;
        })
      });
    }
  }

  // 商品批量设置
  const batchConfirm = (e, index,indexs)=> {
    let resetFields = [];
    detail.value.selectedRows[0].productList.map((item,ind) => {
      let num
      if(detail.value.discountType==1){
        num =  Number(item[`ladderPrice${index}`] != undefined && item[`ladderPrice${index}`] ? (item[`ladderPrice${index}`] * 1 - 0.01).toFixed(2) : (item.productPrice * 1 - 0.01).toFixed(2))
      }else{
        num =  Number(item[`ladderPrice${index}`] != undefined && item[`ladderPrice${index}`] ? (item[`ladderPrice${index}`] * 1 - 0.1).toFixed(2) : 9.9)
      }
      item['ladderPrice' + (index + 1)] = num > Number(battchVal.value) ? battchVal.value : num;
      resetFields.push(['selectedRows', indexs, 'productList',ind,`ladderPrice${index + 1}`]);
    });
    battchVal.value = ''
    formRef.value.clearValidate(resetFields);
  }

  //批量设置
  const handleFieldBattchChange = (e, type, item)=> {
    battchVal.value = e
  }

  //全部参与事件
  const setAll = (e, val,indexs) => {
    let states = [];
    for (let i in detail.value.selectedRows) {
      if (detail.value.selectedRows[i].goodsId == val.goodsId) {
        detail.value.selectedRows[i].productList.map(item => {
          item.state = e.target.checked ? 1 : 0;
          states.push('state'+item.productId);
        });
        break;
      }
    }
    if(!e.target.checked){
      let resetFields = []
      detail.value.selectedRows[0].productList.forEach((item,index)=>{
        detail.value.selectedRows[0].ladder_data.forEach((it,ind) => {
          resetFields.push(['selectedRows', indexs, 'productList',index,`ladderPrice${ind + 1}`]);
        })
      })
      formRef.value.clearValidate(resetFields);
    }
  };

  const handleFieldBlur = (val, fieldName, record)=>{
    val = val.target!=undefined?val.target.value*1:val*1;
    let tar_data = detail.value.selectedRows[0].productList.filter(item => item.productId == record.productId);
    if (tar_data.length > 0) {
      tar_data[0][fieldName] = val;
      let resetFiledDatas = [];
      record.ladder_data.forEach((item, index) => {
        resetFiledDatas.push('ladderPrice' + (index + 1) + '_' + record.productId);
        if(fieldName.indexOf('ladderPrice')>-1&&index>0){
          if(tar_data[0]['ladderPrice' + (index + 1)]!=undefined&&tar_data[0]['ladderPrice' + (index + 1)]){
            if(tar_data[0]['ladderPrice' + (index + 1)]>=tar_data[0]['ladderPrice' + (index)]){
              tar_data[0]['ladderPrice' + (index + 1)] = tar_data[0]['ladderPrice' + (index)]*1-0.01;
            }
          }
        }
      });
    }
  }

  const handleFieldChange = (val, fieldName, record,index,indexs)=> {
    handleFiledContent(val, fieldName, record);
    let resetFields = []
    detail.value.selectedRows[0].ladder_data.map((item,ind) => {
      resetFields.push(['selectedRows', indexs, 'productList',index,`ladderPrice${ind + 1}`]);
    })
    formRef.value.clearValidate(resetFields);
  }


  //spec_data_table 表格编辑事件
  const handleFiledContent = (val, fieldName, record,index,indexs)=> {
    let tar_sku_list = detail.value.selectedRows
    if (tar_sku_list.length > 0) {
      let tar_data = tar_sku_list[0].productList.filter(item => item.productId == record.productId);
      if (tar_data.length > 0) {
        tar_data[0][fieldName] = val;
      }
    }
  }

  //删除添加的商品spu
  const delSpu = (goodsId) => {
    detail.value.selectedRows = detail.value.selectedRows.filter(item => item.goodsId != goodsId);
    detail.value.selectedRowKeys = detail.value.selectedRowKeys.filter(item => item != goodsId);
    sele_more_goods.value.ids = [...detail.value.selectedRowKeys];
    sele_more_goods.value.info = JSON.parse(JSON.stringify(detail.value.selectedRows));
  };

  // 返回
  const goBack = () => {
    if (window.history && window.history.length == 1) {
      pageClose();
    } else {
      const { fullPath } = route;
      tabStore.closeTabByKey(fullPath, router);
      router.back();
    }
  };

  // 保存
  const handleSaveAllData = async()=> {
    try {
      formRef.value.validate().then(async (values) => {
        let params = {};
       //活动时间处理
        if (values.activityTime) {
          params.startTime = values.activityTime[0].format('YYYY-MM-DD HH:mm:00');
          params.endTime = values.activityTime[1].format('YYYY-MM-DD HH:mm:00');
        }
        //活动开始时间必须小于结束时间
        if(Date.parse(params.startTime) >= Date.parse(params.endTime)){
          failTip(L('活动开始时间必须小于结束时间'));
          return false;
        }
        let selectedRows = detail.value.selectedRows
        //阶梯团商品
        params.productList = [];
        if (selectedRows.length == 0) {
          failTip(L('请选择要参与活动的商品'));
          return false;
        }
        let rule_data = [];
        let ladders = selectedRows[0].ladder_data;
        for (let l in ladders) {
          //阶梯人数已经验证过了，不需要再验证了
          rule_data.push({
            joinGroupNum: ladders[l].value,
            ladderLevel: Number(l) * 1 + 1,
          });
          // if (l > 0 && ladders[l].value * 1 <= ladders[l-1].value * 1) {
          //   failTip(`第` + num_to_num[l + 1] + '阶梯的人数须大于第' + num_to_num[l] + '阶梯的人数');
          //   return;
          // } else {
          //   rule_data.push({
          //     joinGroupNum: ladders[l].value,
          //     ladderLevel: l * 1 + 1,
          //   });
          // }
        }
        let total_ladder = selectedRows[0].ladder_data.length;
        let products = selectedRows[0].productList;
        for (let i in products) {
          if (products[i].state == 1) {
            let product_item = {};
            product_item.advanceDeposit = values.selectedRows[0].advanceDeposit;
            product_item.productId = products[i].productId;
            for (let j = 0; j < total_ladder; j++) {
              product_item['ladderPrice' + (j + 1)] = products[i]['ladderPrice' + (j + 1)];
            }

            //预付定金必须小于最低优惠价格
            let curLastladderPrice = 0;
            // 阶梯优惠方式：1-阶梯价格；2-阶梯折扣
            if (values.discountType == 1) {
              curLastladderPrice = products[i]['ladderPrice' + total_ladder] * 1;
              if (values.selectedRows[0].advanceDeposit * 1 >= curLastladderPrice) {
                failTip(L('预付定金须小于最低优惠价，最低优惠价目前是：') + curLastladderPrice + L('元'));
                return;
              }
            } else {
              curLastladderPrice = Number((products[i]['ladderPrice' + total_ladder] * products[i].productPrice / 10).toFixed(2));
              if (values.selectedRows[0].advanceDeposit * 1 >= curLastladderPrice) {
                failTip(L('预付定金须小于最低优惠价，最低优惠价目前是：') + products[i]['ladderPrice' + total_ladder] * 1 + L('折，') + curLastladderPrice + L('元'));
                return;
              }
            }

            params.productList.push(product_item);
          }
        }
        if(params.productList.length == 0){
          failTip(L('请选择参与活动的商品规格～'));
          return false;
        }
        params.ruleList = rule_data;
        params.balanceTime = values.balanceTime;// 尾款时间(活动结束多少小时内需要支付尾款)
        params.buyLimitNum = values.buyLimitNum;// 限购件数，0为不限购
        params.discountType = values.discountType;// 阶梯优惠方式：1-阶梯价格；2-阶梯折扣
        params.groupName = values.groupName;// 阶梯团活动名称
        params.isRefundDeposit = values.isRefundDeposit;// 是否退还定金：1-是；0-否
        params.labelId = values.labelId;// 活动标签id
        loading.value = true
        let res;
        if(route.query.id != undefined && Number(route.query.id) > 0 && route.query.tar == 'edit'){
          params.groupId = route.query.id;// 活动id
          res = await getLadderUpdateApi(params)
        }else{
          res = await getLadderAddApi(params)
        }
        if(res.state == 200){
          loading.value = false
          sucTip(res.msg);
          userStore.setDelKeepAlive([route.name,'PromotionLadderGroup'])
          setTimeout(() => {
            goBack();
          }, 500);
        }else{
          loading.value = false
          failTip(res.msg)
        }
      })
    } catch (error) {
      console.info(error,'error')
    }
  }

  onMounted(() => {
    if (route.query.id != undefined && Number(route.query.id) > 0) {
      loading.value = true
      get_detail(route.query.id);
    }
    get_activity_label()
  });
</script>
<style lang="less">
@import './style/add_ladder_group.less';
</style>
