<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader :title="L('视频带货绑定会员')" />
      <BasicTable @register="registerTable" rowKey="bindId" :resizeHeightOffset="4">
        <template #tableTitle>
          <div class="toolbar">
            <div class="toolbar_btn" @click="add">
              <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" :fillColor="'#08A9B7'" />
              <span style="margin-left: 4px">{{ L('绑定会员') }}</span>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction
              stopButtonPropagation
              :actions="[
                {
                  label: L('删除'),
                  popConfirm: {
                    title: L('删除后不可恢复，是否确定删除？'),
                    placement: 'left',
                    confirm: operateVideo.bind(null, record, 'del'),
                  },
                },
              ]"
            />
          </template>
        </template>
      </BasicTable>
    </div>
    <SldModal
      :width="500"
      :title="title"
      :visible="modalVisible"
      :height="600"
      :content="operateData"
      :showFoot="showFoot"
      :confirmBtnLoading="modalConfirmBtnLoading"
      @cancle-event="handleModalCancle"
      @confirm-event="sldConfirm"
    />
  </div>
</template>
<script>
  export default {
    name: 'PromotionVideo',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, reactive, onMounted } from 'vue';
  import { Tabs, TabPane, RadioGroup, RadioButton } from 'ant-design-vue';
  import { sucTip, failTip } from '@/utils/utils';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { getVideoMemberListApi,getVideoMemberBindMemberApi,getVideoMemberDelApi } from '/@/api/promotion/video';
  import { getVideoSmsCodeApi } from '/@/api/common/common';
  import SldModal from '/@/components/SldModal/index.vue';
  import { mobile_reg,checkSmsCode } from '/@/utils/validate';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const modalVisible = ref(false);

  const btnLoading = ref(false);

  const showFoot = ref(true);
  const modalConfirmBtnLoading = ref(false);
  const title = ref('')
  const type = ref('add')
  const operateData = ref([])
  const addData = ref([
    {
      type: 'input',
      label: L('手机号'),
      name: 'memberMobile',
      placeholder: L('请输入手机号'),
      maxLength: 11,
      rules: [
        {
          required: true,
          whitespace: true,
          message: L('请输入手机号'),
        },
        {
          pattern: mobile_reg,
          message: L('请输入正确的手机号'),
        },
      ],
    },
    {
      type: 'verify_code', //验证码
      label: L('短信验证码'),
      name: 'smsCode',
      mobile: 'memberMobile', //关联的手机号字段
      placeholder: L('请输入短信验证码'),
      extra: ``,
      api:getVideoSmsCodeApi,
      initialValue: '',
      maxLength: 6, //最多字数
      rules: [
        {
          required: true,
          whitespace: true,
          message: L('请输入短信验证码'),
        },
        {
          // @ts-ignore
          validator: async (rule, value) => {
            await checkSmsCode(rule, value);
          },
          trigger: 'change',
        },
      ],
    },
  ])

  const columns = reactive({
    data: [
      {
        title: L('会员名称'),
        dataIndex: 'memberName',
        align: 'center',
        width: 150,
      },
      {
        title: L('昵称'),
        dataIndex: 'memberNickName',
        align: 'center',
        width: 150,
      },
      {
        title: L('手机号'),
        dataIndex: 'memberMobile',
        align: 'center',
        width: 100,
      },
      {
        title: L('会员状态'),
        dataIndex: 'memberStateValue',
        align: 'center',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: L('短视频状态'),
        dataIndex: 'svideoStateValue',
        align: 'center',
        width: 150,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: L('直播状态'),
        dataIndex: 'liveStateValue',
        align: 'center',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
    ],
  });

  const searchFormSchema = reactive({
    data: [
      {
        component: 'Input',
        label: L('会员名称'),
        field: 'memberName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: L('请输入会员名称'),
        },
      },
      {
        component: 'Input',
        label: L('会员昵称'),
        field: 'memberNickName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: L('请输入会员昵称'),
        },
      },
      {
        component: 'Input',
        label: L('手机号'),
        field: 'memberMobile',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: L('请输入会员手机号'),
        },
      },
    ],
  });


  // 表格数据
  const [registerTable, { reload }] = useTable({
    // 请求接口
    api: getVideoMemberListApi,
    // 表头
    columns: columns.data,
    immediate: true,
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: searchFormSchema.data,
    },
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    ellipsis: false,
    // 是否显示序号列
    showIndexColumn: true,
    // 表格右侧操作列配置
    actionColumn: {
      width: 240,
      title: L('操作'),
      dataIndex: 'action',
      // slots: { customRender: 'action' },
    },
  });

  // 操作  type==show 解除禁止 del 删除  ''禁止
  const operateVideo = async (record, type) => {
    let res = await getVideoMemberDelApi({bindId:record.bindId})
    if(res.state == 200){
      sucTip(res.msg)
      reload()
    }else{
      failTip(res.msg)
    }
  }

  // 新增绑定会员
  const add = ()=> {
    operateData.value = addData.value
    title.value = L('绑定会员')
    modalVisible.value = true;
  }

  //关闭弹框
  const handleModalCancle = () => {
    operateData.value = [];
    modalVisible.value = false;
  };

  // 新增视频会员
  const sldConfirm = async(values)=> {
    modalConfirmBtnLoading.value = true
    values.mobile = values.memberMobile
    delete values.memberMobile
    let res = await getVideoMemberBindMemberApi(values)
    if(res.state == 200){
      sucTip(res.msg)
      reload()
      modalVisible.value = false
      modalConfirmBtnLoading.value = false
      title.value = ''
    }else{
      failTip(res.msg)
      modalConfirmBtnLoading.value = false
    }
  }

  onMounted(() => {});
</script>
<style lang="less">
 
</style>
