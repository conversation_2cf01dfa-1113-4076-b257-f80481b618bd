<template>
  <div class="presale_lists">
    <BasicTable @register="registerTable" rowKey="presellId">
      <template #tableTitle>
        <div class="toolbar">
          <div class="toolbar_btn" @click="addPresale('add',null,'PresaleToAdd')">
            <AliSvgIcon iconName="iconfabu1" width="15px" height="15px" :fillColor="'#fa6f1e'" />
            <span style="margin-left: 4px">{{ L('新建全款预售') }}</span>
          </div>
        </div>
      </template>
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'startTime'">
          <div class="voucher_time_wrap" :title="text + '~' + record.endTime">
            <span>{{ text }}</span>
            <span>~</span>
            <span>{{ record.endTime }}</span>
          </div>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            class="TableAction"
            :actions="[
              {
                label: L('查看商品'),
                onClick: view.bind(null, record.presellId, 'shop'),
              },
              {
                label: L('查看详情'),
                onClick: view.bind(null, record.presellId, 'detail'),
              },
              {
                label: L('复制'),
                onClick: addPresale.bind(null, 'copy',record.presellId,'PresaleToCopy'),
              },
              // 只有待发布的才可以编辑
              {
                label: L('编辑'),
                ifShow: record.state == 1,
                onClick: addPresale.bind(null, 'edit',record.presellId,'PresaleToEdit'),
              },
              // 只有未开始、进行中的才可以失效
              {
                label: L('发布'),
                ifShow: record.state == 1,
                popConfirm: {
                  title: L('发布后不可撤销，是否确定发布？'),
                  placement: 'left',
                  confirm: operate.bind(null, { presellId: record.presellId }, 'publish'),
                },
              },
              // 只有未开始、进行中的才可以失效
              {
                label: L('失效'),
                ifShow: record.state == 2 || record.state == 3,
                popConfirm: {
                  title: L('确定失效该活动吗？'),
                  placement: 'left',
                  confirm: operate.bind(null, { presellId: record.presellId }, 'invalid'),
                },
              },
              // 只有待发布、已失效、已结束的才可以删除
              {
                label: L('删除'),
                ifShow: record.state == 1 || record.state == 4 || record.state == 5,
                popConfirm: {
                  title: L('删除后不可恢复，是否确定删除？'),
                  placement: 'left',
                  confirm: operate.bind(null, { presellId: record.presellId }, 'del'),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
  </div>
</template>
<script>
  export default {
    name: 'PresaleListsAll',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, reactive, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { validatorEmoji } from '/@/utils/validate';
  import { sucTip, failTip } from '@/utils/utils';
  import { useUserStore } from '/@/store/modules/user';
  import {
    getPreSellListApi,
    getPreSellInvalidApi,
    getPreSellDelApi,
    getPreSellPublishApi
  } from '/@/api/promotion/presale';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const router = useRouter();
  const userStore = useUserStore();

  const presellId = ref(0);

  const columns = reactive({
    data: [
      {
        title: L('活动名称'),
        dataIndex: 'presellName',
        align: 'center',
        width: 120,
      },
      {
        title: L('活动时间'),
        dataIndex: 'startTime',
        align: 'center',
        width: 100,
        customRender: ({ text, record }) => {
          return text + '~' + record.endTime;
        },
      },
      {
        title: L('活动状态'),
        dataIndex: 'stateValue',
        align: 'center',
        width: 100,
      },
    ],
  });

  const searchFormSchema = reactive({
    data: [
      {
        component: 'Input',
        label: L('活动名称'),
        field: 'presellName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: L('请输入活动名称'),
        },
        rules: [
          {
            // @ts-ignore
            validator: async (rule, value) => {
              await validatorEmoji(rule, value);
            },
            trigger: 'change',
          },
        ],
      },
      {
        colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
        component: 'RangePicker',
        label: L('活动时间'),
        field: '[startTime,endTime]',
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: [L('开始时间'), L('结束时间')],
        },
      },
      {
        component: 'Select',
        label: L('状态'),
        field: 'state',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: L('请选择状态'),
          options: [
            { value: '', label: L('全部') },
            { value: '1', label: L('待发布') },
            { value: '2', label: L('未开始') },
            { value: '3', label: L('进行中') },
            { value: '4', label: L('已失效') },
            { value: '5', label: L('已结束') },
          ],
        },
      },
    ],
  });

  const searchInfo = ref({
    type: 2,
  });
  // 表格数据
  const [registerTable, { reload, getForm }] = useTable({
    // 请求接口
    api: getPreSellListApi,
    // 表头
    columns: columns.data,
    // 参数
    searchInfo: searchInfo.value,
    immediate: true,
    // 点击搜索前处理的参数
    // 点击搜索前处理的参数
    beforeFetch(values) {
      values.startTime = values.startTime
        ? values.startTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
      return values;
    },
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: searchFormSchema.data,
    },
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    // 取消...
    ellipsis: false,
    // 是否显示序号列
    showIndexColumn: true,
    // 表格右侧操作列配置
    actionColumn: {
      width: 220,
      title: L('操作'),
      dataIndex: 'action',
      // slots: { customRender: 'action' },
    },
  });

  const view = (id, type) => {
    if (type == 'detail') {
      router.push({
        path: `/marketing/presale_to_view`,
        query: { id: id, type: 2 },
      });
    } else if (type == 'shop') {
      router.push({
        path: `/marketing/presale_bind_goods`,
        query: { id: id, type: 2 },
      });
    }
  };

  //操作  type: invalid 失效
  const operate = async (id, type) => {
    let param_data = {};
    let res = null;
    if (type == 'invalid') {
      param_data = id;
      res = await getPreSellInvalidApi(param_data);
    }
    if (type == 'publish') {
      param_data = id;
      res = await getPreSellPublishApi(param_data);
    }
    if (type == 'del') {
      param_data = id;
      res = await getPreSellDelApi(param_data);
    }
    if (res.state == 200) {
      sucTip(res.msg);
      reload();
    } else {
      failTip(res.msg);
    }
  };

  const addPresale = (type,id,pathName)=> {
    userStore.setDelKeepAlive([pathName])
    if(type=='add'){
      router.push({
        path: `/marketing/presale_to_add`,
        query:{
          type:2
        }
      });
    }else{
      router.push({
        path: `/marketing/presale_to_${type}`,
        query:{
          id: id,
          tar: type,
          type:2
        }
      });
    }
  }

  onMounted(() => {});
</script>
<style lang="less" scoped>
  p {
    margin-bottom: 0;
  }

  .presale_lists {
    .voucher_num {
      color: @primary-color;
      cursor: pointer;
    }
  }
</style>
<style lang="less">
  .full_modal {
    .ant-modal-header {
      padding: 12px 24px !important;
      background-color: @primary-color;
    }

    .ant-modal-title {
      color: #fff;
      font-size: 14px !important;
      line-height: 18px !important;
    }

    .ant-modal-close-x .anticon svg {
      fill: #fff;
    }

    .ant-modal .ant-modal-content {
      border-radius: 4px;
    }

    .ant-form-item-with-help {
      margin-bottom: 24px;
    }

    .ant-form-item-has-error .ant-input-number,
    .ant-form-item-has-error .ant-picker {
      border-color: #ed6f6f !important;
    }

    .ant-modal-close {
      top: 12px;
      right: 20px;
      height: auto !important;

      .ant-modal-close-x {
        display: flex !important;
        align-items: center;
        justify-content: center;
        width: auto !important;
        height: auto !important;
        line-height: normal;
      }
    }
  }
</style>
