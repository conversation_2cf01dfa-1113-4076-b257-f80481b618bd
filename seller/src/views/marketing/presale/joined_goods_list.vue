<template>
  <div class="section_padding presale_goods_lists">
    <div class="section_padding_back">
      <SldComHeader :title="L('活动商品')" :back="true" />
      <div class="com_line" style="height: 1px"></div>
      <BasicTable @register="registerTable" rowKey="goodsId">
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'goodsImage'">
            <div class="goods_info com_flex_row_flex_start">
              <div class="goods_img">
                <Popover placement="rightTop">
                  <template #content>
                    <div style="width: 200px;height: 200px;" class="flex_row_center_center">
                      <img :src="text" alt="" style="max-width: 100%; max-height: 100%" />
                    </div>
                  </template>
                  <div class="goods_img_wrap_80">
                    <img :src="text" alt="" />
                  </div>
                </Popover>
              </div>
              <div class="com_flex_column_space_between goods_detail">
                <span class="goods_name">
                  {{ record.goodsName }}
                </span>
              </div>
            </div>
          </template>
          <template v-if="column.dataIndex === 'action'">
            <TableAction
              :actions="[
                {
                  onClick: viewSpec.bind(null, record),
                  label: L('查看规格'),
                },
                {
                label: L('删除'),
                popConfirm: {
                  title: L('删除后不可恢复，是否确定删除？'),
                  placement: 'left',
                  confirm: operate_click.bind(null, record, 'del'),
                },
              },
              ]"
            />
          </template>
        </template>
      </BasicTable>
    </div>
    <SldModal
      :width="900"
      :title="L('商品SKU')"
      :visible="modalVisible"
      :height="300"
      :content="operateContent"
      :showFoot="false"
      @cancle-event="handleModalCancle"
    />
  </div>
</template>
<script>
  export default {
    name: 'PresaleGoodsList',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, reactive, onMounted } from 'vue';
  import {  Popover } from 'ant-design-vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { useRoute, useRouter } from 'vue-router';
  import { getPreSellGoodListApi,getPreSellDelGoodsApi } from '/@/api/promotion/presale';
  import SldModal from '/@/components/SldModal/index.vue';
  import { sucTip, failTip } from '@/utils/utils';
  
  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const route = useRoute();

  const router = useRouter();

  // 参数
  const id = ref(route.query?.id);

  const modalVisible = ref(false);

  const operate = ref([
    {
      title: ' ',
      align: 'center',
      width: 100,
      minWidth: 100,
      customRender: ({ text, record, index }) => {
        return `${index + 1}`;
      },
    },
    {
      title: L('SKU规格'),
      dataIndex: 'specValues',
      align: 'center',
      width: 200,
      customRender: ({ text }) => {
        return text ? text : L('默认');
      },
      minWidth: 200,
    },
    {
      title: L('原价(￥)'),
      dataIndex: 'productPrice',
      align: 'center',
      width: 200,
      minWidth: 200,
    },
    {
      title: L('商品库存'),
      dataIndex: 'stock',
      align: 'center',
      width: 200,
      minWidth: 200,
    },
    {
      title: L('预售价(￥)'),
      dataIndex: 'presellPrice',
      align: 'center',
      width: 200,
      minWidth: 200,
    },
    {
      title: L('预售定金(￥)'),
      dataIndex: 'firstMoney',
      align: 'center',
      width: 200,
      minWidth: 200,
    },
    {
      title: L('预售库存'),
      dataIndex: 'presellStock',
      align: 'center',
      width: 200,
      minWidth: 200,
    },
    {
      title: L('定金膨胀(￥)'),
      dataIndex: 'firstExpand',
      align: 'center',
      width: 200,
      minWidth: 200,
    },
  ]);

  const operate_all = ref([
    {
      title: ' ',
      align: 'center',
      width: 100,
      minWidth: 100,
      customRender: ({ text, record, index }) => {
        return `${index + 1}`;
      },
    },
    {
      title: L('SKU规格'),
      dataIndex: 'specValues',
      align: 'center',
      width: 200,
      customRender: ({ text }) => {
        return text ? text : L('默认');
      },
      minWidth: 200,
    },
    {
      title: L('原价(￥)'),
      dataIndex: 'productPrice',
      align: 'center',
      width: 200,
      minWidth: 200,
    },
    {
      title: L('商品库存'),
      dataIndex: 'stock',
      align: 'center',
      width: 200,
      minWidth: 200,
    },
    {
      title: L('预售价(￥)'),
      dataIndex: 'presellPrice',
      align: 'center',
      width: 200,
      minWidth: 200,
    },
    {
      title: L('预售库存'),
      dataIndex: 'presellStock',
      align: 'center',
      width: 200,
      minWidth: 200,
    },
  ]);

  // 弹框数据
  const operateContent = ref([
    {
      type: 'show_content_table', //展示表格，没有分页，不能勾选
      name: 'bind_goods',
      width: 880,
      wrapperCol: { flex: 1, style: 'margin-bottom:-19px' },
      label: ``,
      content: '',
      data: [],
      scrollHeight: false, // 设置纵向滚动，也可用于指定滚动区域的高，可以设置为像素值
      columns: route.query?.type == 1 ? operate.value : operate_all.value,
      rowKey: 'presellId',
      scroll: false,
    },
  ]);

  const searchInfo = ref({
    presellId: id.value,
  });
  const columns = reactive({
    data: [
      {
        title: L('商品信息'),
        dataIndex: 'goodsImage',
        align: 'center',
        width: 180,
      },
      {
        title: L('原价(￥)'),
        dataIndex: 'productPrice',
        align: 'center',
        width: 100,
      },
      {
        title: L('预售价(￥)'),
        dataIndex: 'presellPrice',
        align: 'center',
        width: 100,
      },
      {
        title: L('预售库存'),
        dataIndex: 'presellStock',
        align: 'center',
        width: 100,
      },
      {
        title: L('已售件数'),
        dataIndex: 'saleNum',
        align: 'center',
        width: 100,
      },
    ],
  });

  const searchFormSchema = reactive({
    data: [
      {
        component: 'Input',
        label: L('商品名称'),
        field: 'goodsName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: L('请输入商品名称'),
        },
      },
    ],
  });

  // 表格数据
  const [registerTable,{reload}] = useTable({
    // 请求接口
    api: getPreSellGoodListApi,
    // 表头
    columns: columns.data,
    immediate: true,
    // 参数
    searchInfo: searchInfo.value,
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: searchFormSchema.data,
    },
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    ellipsis: false,
    // 是否显示序号列
    showIndexColumn: true,
    // 表格右侧操作列配置
    actionColumn: {
      width: 160,
      title: L('操作'),
      dataIndex: 'action',
      // slots: { customRender: 'action' },
    },
  });

  const viewSpec = (record, type) => {
    operateContent.value[0].data = [];
    operateContent.value[0].columns = route.query?.type == 1 ? operate.value : operate_all.value;
    operateContent.value[0].data = record.productList;
    modalVisible.value = true;
  };

  //关闭弹框
  const handleModalCancle = () => {
    modalVisible.value = false;
    operateContent.value[0].data = [];
  };

  // 操作 删除del
  const operate_click = async(record,type)=> {
    let param_data = {};
    param_data.goodsId = record.goodsId
    param_data.presellId = route.query?.id
    let res = await getPreSellDelGoodsApi(param_data)
    if(res.state == 200){
      sucTip(res.msg)
      reload()
    }else{
      failTip(res.msg)
    }
  }
  onMounted(() => {});
</script>
<style lang="less" scoped>
  .presale_goods_lists {
    .goods_info {
      position: relative;

      .virtual_goods_flag {
        position: absolute;
        top: 1px;
        left: 1px;
        padding: 0 3px;
        border-radius: 3px 0;
        background: linear-gradient(90deg, #f7d47e 0%, #e6b845 100%);
        color: #fff;
        font-size: 12px;
      }

      .goods_detail {
        flex: 1;
        width: 169px;
        height: 80px;
        margin-left: 10px;
      }

      .goods_img {
        display: inline-block;
        width: 80px;
        height: 80px;
        overflow: hidden;
        border: 1px solid rgb(226 229 246 / 100%);
        border-radius: 3px;
        background: rgb(248 248 248 / 100%);
      }

      .goods_name {
        display: -webkit-box;
        flex-shrink: 0;
        height: 34px;
        overflow: hidden;
        color: #333;
        font-size: 13px;
        line-height: 17px;
        text-align: left;
        text-overflow: ellipsis;
        word-break: break-word;
        -webkit-line-clamp: 2;

        /*! autoprefixer: off */
        -webkit-box-orient: vertical;
      }

      .goods_brief {
        display: inline-block;
        width: 100%;
        overflow: hidden;
        color: #666;
        font-size: 12px;
        text-align: left;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  p {
    margin-bottom: 0;
  }
</style>
