<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader :title="L('预售活动')" />
      <Tabs class="tabs_bottom" type="card" v-model:activeKey="activeKey" v-if="enableFlag == 1&&!isFirstLoading">
        <TabPane key="1" :tab="L('定金预售')">
          <PresaleLists />
        </TabPane>
        <TabPane key="2" :tab="L('全款预售')">
          <PresaleListsAll />
        </TabPane>
      </Tabs>
      <div class="" v-if="enableFlag != 1&&!isFirstLoading">
        <div style="width: 100%;height:150px;"></div>
        <Empty :image="moudle_disable" :image-style="{ height: '80px' }" :description="L('预售活动模块暂未开启')"/>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'PromotionPresale',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, reactive, onMounted } from 'vue';
  import { Tabs, TabPane, Empty } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { getSettingListApi } from '/@/api/common/common';
  import PresaleLists from './presale_lists.vue';
  import PresaleListsAll from './presale_lists_all.vue';
  import moudle_disable from '/src/assets/images/moudle_disable.png';
  import { useUserStore } from '/@/store/modules/user';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const activeKey = ref('1');
  const userStore = useUserStore();

  const enableFlag = ref(0);//预售活动开关
  const isFirstLoading = ref(true)

  //获取系统配置(预售活动是否开启)
  const getSetting = async () => {
    try {
      let res = await getSettingListApi({ str: 'presale_is_enable' });
      if (res.state == 200) {
        enableFlag.value = res.data[0].value
        isFirstLoading.value = false
      }
    } catch (error) {}
  };

  onMounted(() => {
    if(userStore.getUpdateTab.length>0){
      let index = userStore.getUpdateTab.findIndex(item=>item.name == 'PromotionPresale')
      if(index>-1){
        activeKey.value = userStore.getUpdateTab[index].index
        userStore.setDelTab('PromotionPresale')
      }
    }
    getSetting()
  });
</script>
<style lang="less">
  .RadioGroup_back {
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within {
      box-shadow: none;
    }
  }
</style>
