<template>
  <div class="section_padding add_presale seckill">
    <div class="section_padding_back full_activity">
      <SldComHeader :title="title" />
      <div class="com_line" style="height: 1px"></div>
      <Spin :spinning="loading">
        <div class="add_coupon_height">
          <Form layout="inline" ref="formRef" :model="detail">
            <!-- 活动基本信息 start -->
            <CommonTitle :text="L('活动基本信息')" :describe="Number(query.type) == 1 ? L('温馨提示：若因商家问题导致无法发货，需要对会员进行赔偿，赔偿金额为定金的')+compensateRate+L('倍。') : ''" style="margin-top: 10px" />
            <div class="full_acm_activity flex_column_start_start">
              <!-- 活动名称 start -->
              <div class="item flex_row_start_start">
                <div class="left"><span style="color: red">*</span>{{ L('活动名称') }} </div>
                <div class="right">
                  <Form.Item
                    :validateFirst="true"
                    :extra="L('最多输入20个字')"
                    name="presellName"
                    style="width: 400px"
                    :rules="[
                      {
                        required: true,
                        whitespace: true,
                        message: L('请输入活动名称'),
                      },
                      {
                        validator: async (rule, value) => {
                          await validatorEmoji(rule, value);
                        },
                      },
                    ]"
                  >
                    <Input
                      :maxLength="20"
                      style="width: 400px !important"
                      :placeholder="L('请输入活动名称')"
                      v-model:value="detail.presellName"
                    />
                  </Form.Item>
                </div>
              </div>
              <!-- 活动名称 end -->
              <!-- 活动标签 start -->
              <div class="item flex_row_start_center">
                <div class="left"> <span style="color: red">*</span>{{ L('活动标签') }}</div>
                <div class="right">
                  <Form.Item name="presellLabelId" style="width: 400px" :rules="[{
                    required: true,
                    message: L('请选择活动标签'),
                  }]">
                    <Select
                      :placeholder="L('请选择活动标签')"
                      style="width: 400px"
                      :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                      v-model:value="detail.presellLabelId"
                    >
                      <Select.Option
                        v-for="(item, index) in activity_labels"
                        :key="index"
                        :value="item.presellLabelId"
                        >{{ item.presellLabelName }}</Select.Option
                      >
                    </Select>
                  </Form.Item>
                </div>
              </div>
              <!-- 活动标签 end -->
              <!-- 定金时间 start -->
              <div class="item flex_row_start_start" v-if="Number(query.type) == 1">
                <div class="left"><span style="color: red">*</span>{{ L('定金时间') }} </div>
                <div class="right">
                  <Form.Item
                    name="remainStart"
                    :extra="L('请设置定金的支付时间')"
                    style="width: 400px"
                    :rules="[
                      {
                        required: true,
                        message: L('请选择定金时间'),
                      },
                    ]"
                  >
                    <RangePicker
                      style="width: 400px"
                      :format="'YYYY-MM-DD HH:mm:00'"
                      v-model:value="detail.remainStart"
                      :disabledDate="
                        (currentDate) => currentDate && currentDate < moment().subtract(1, 'days')
                      "
                      :showTime="{ format: 'HH:mm' }"
                      :placeholder="[L('开始时间'), L('结束时间')]"
                      :getPopupContainer="
                        (triggerNode) => {
                          return triggerNode.parentNode;
                        }
                      "
                    />
                  </Form.Item>
                </div>
              </div>
              <!-- 定金时间 end -->
              <!-- 活动时间 start -->
              <div class="item flex_row_start_start" v-if="Number(query.type) == 2">
                <div class="left"><span style="color: red">*</span>{{ L('活动时间') }} </div>
                <div class="right">
                  <Form.Item
                    name="remainStart"
                    :extra="L('设置活动时间')"
                    style="width: 400px"
                    :rules="[
                      {
                        required: true,
                        message: L('请选择活动时间'),
                      },
                    ]"
                  >
                    <RangePicker
                      style="width: 400px"
                      :format="'YYYY-MM-DD HH:mm:00'"
                      v-model:value="detail.remainStart"
                      :disabledDate="
                        (currentDate) => currentDate && currentDate < moment().subtract(1, 'days')
                      "
                      :showTime="{ format: 'HH:mm' }"
                      :placeholder="[L('开始时间'), L('结束时间')]"
                      :getPopupContainer="
                        (triggerNode) => {
                          return triggerNode.parentNode;
                        }
                      "
                    />
                  </Form.Item>
                </div>
              </div>
              <!-- 活动时间 end -->
              <!-- 尾款时间 start -->
              <div class="item flex_row_start_start" v-if="Number(query.type) == 1">
                <div class="left"><span style="color: red">*</span>{{ L('尾款时间') }} </div>
                <div class="right">
                  <Form.Item
                    name="remain"
                    :extra="L('请设置尾款的支付时间')"
                    style="width: 400px"
                    :rules="[
                      {
                        required: true,
                        message: L('请选择尾款时间'),
                      },
                    ]"
                  >
                    <RangePicker
                      style="width: 400px"
                      :format="'YYYY-MM-DD HH:mm:00'"
                      v-model:value="detail.remain"
                      :disabledDate="
                        (currentDate) => currentDate && currentDate < moment().subtract(1, 'days')
                      "
                      :showTime="{ format: 'HH:mm' }"
                      :placeholder="[L('开始时间'), L('结束时间')]"
                      :getPopupContainer="
                        (triggerNode) => {
                          return triggerNode.parentNode;
                        }
                      "
                    />
                  </Form.Item>
                </div>
              </div>
              <!-- 尾款时间 end -->
              <!-- 发货时间 start -->
              <div class="item flex_row_start_start">
                <div class="left"><span style="color: red">*</span>{{ L('发货时间') }} </div>
                <div class="right">
                  <Form.Item
                    name="deliverTime"
                    :extra="Number(query.type) ==1?L('请设置具体的发货时间，若因商家未在约定时间内发货，导致买家退款，商家需退还定金并赔偿买家定金金额的')+compensateRate+L('倍。') : L('请设置具体的发货时间') "
                    style="width: 400px"
                    :rules="[
                      {
                        required: true,
                        message: L('请选择发货时间'),
                      },
                    ]"
                  >
                  <DatePicker
                    :showTime="{ format: 'HH:mm' }"
                      format="YYYY-MM-DD HH:mm:00"
                      v-model:value="detail.deliverTime"
                      :placeholder="L('请选择发货时间')"
                      style="width: 400px"
                    />
                  </Form.Item>
                </div>
              </div>
              <!-- 发货时间 end -->
              <!-- 限购件数 start -->
              <div class="item flex_row_start_start">
                <div class="left"><span style="color: red">*</span>{{ L('限购件数') }} </div>
                <div class="right">
                  <Form.Item
                    :extra="L('每位会员限制购买的件数，0代表不限制件数')"
                    name="buyLimit"
                    style="width: 300px"
                    :rules="[
                      {
                        required: true,
                        message: L('请输入限购件数'),
                      },
                    ]"
                  >
                    <div class="flex_row_start_center">
                      
                      <InputNumber :min="0" :max="9999" :precision="0" style="width: 300px !important;" v-model:value="detail.buyLimit" :placeholder="L('请输入限购件数')"></InputNumber>
                    </div>
                  </Form.Item>
                </div>
              </div>
              <!-- 限购件数 end -->
            </div>
            <!-- 活动基本信息 end -->
            <CommonTitle style="margin-top: 10px" :describe="Number(query.type) == 1 ? L('预售商品规则：预付定金不能大于预售价，预售价不能大于商品原价') : L('预售商品规则：预售价不能大于商品原价')">
              <template #button>
                  <div class="toolbar_btn" @click="addGoods">
                    <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" :fillColor="'rgb(255, 89, 8)'" />
                    <span style="margin-left: 4px">{{ L('选择商品') }}</span>
                  </div>
                </template>
            </CommonTitle>
            <div class="sele_goods" v-for="(item,indexs) in detail.selectedRows" :key="indexs">
              <img :src="del_seckill_goods" alt="" class="del_spu" @click="delSpu(item.goodsId)">
              <div class="goods_info flex_row_between_start">
                <div class="goods_info_left flex_row_start_start">
                  <div class="goods_img_wrap flex_row_center_center">
                    <img :src="item.goodsImage" alt="" class="goods_img">
                  </div>
                  <p class="goods_name">{{ item.goodsName }}</p>
                </div>
                <div class="goods_info_right flex_row_end_end">
                  <Popconfirm @confirm="(e) => {
                              batchConfirm(e, 'presellPrice', item,indexs);
                            }">
                    <template #title>
                      <InputNumber :min="0.01" :max="9999999" :precision="2" style="width: 100%;" v-model:value="battchVal" :placeholder="L('批量设置预售价')"  @change="(e) => {handleFieldBattchChange(e, 'presellPrice', item)}"></InputNumber>
                    </template>
                    <div class="batch_btn flex_row_center_center">
                      {{ L('设置预售价') }}
                    </div>
                  </Popconfirm>
                  <Popconfirm v-if="Number(query.type) == 1" @confirm="(e) => {
                              batchConfirm(e, 'firstMoney', item,indexs);
                            }">
                    <template #title>
                      <InputNumber :min="1" :max="99999999" :precision="0" style="width: 100%;" v-model:value="battchVal" :placeholder="L('批量设置定金')"  @change="(e) => {handleFieldBattchChange(e, 'firstMoney', item)}"></InputNumber>
                    </template>
                    <div class="batch_btn flex_row_center_center">
                      {{ L('设置定金') }}
                    </div>
                  </Popconfirm>
                  <Popconfirm v-if="Number(query.type) == 1" @confirm="(e) => {
                              batchConfirm(e, 'firstExpand', item,indexs);
                            }">
                    <template #title>
                      <InputNumber :min="1" :max="99999999" :precision="0" style="width: 100%;" v-model:value="battchVal" :placeholder="L('批量设置膨胀金')"  @change="(e) => {handleFieldBattchChange(e, 'firstExpand', item)}"></InputNumber>
                    </template>
                    <div class="batch_btn flex_row_center_center">
                      {{ L('设置膨胀') }}
                    </div>
                  </Popconfirm>
                  <Popconfirm @confirm="(e) => {
                              batchConfirm(e, 'presellStock', item,indexs);
                            }">
                    <template #title>
                      <InputNumber :min="1" :max="99999999" :precision="0" style="width: 100%;" v-model:value="battchVal"  :placeholder="L('批量设置预售库存')" @change="(e) => {handleFieldBattchChange(e, 'presellStock', item)}"></InputNumber>
                    </template>
                    <div class="batch_btn flex_row_center_center">
                      {{ L('设置预售库存') }}
                    </div>
                  </Popconfirm>
                  <div class="batch_btn flex_row_center_center">
                    <Checkbox @change="setAll($event, item)">
                      <span class="sel_all">{{ L('全部参与') }}</span>
                    </Checkbox>
                  </div>
                </div>
              </div>
              <BasicTable
                :maxHeight="300"
                rowKey="productId"
                :columns="Number(query.type) == 1 ? columns_spec : columns_spec_all"
                :dataSource="item.productList"
                :pagination="false"
                :bordered="true"
                :ellipsis="false"
              >
                <template #headerCell="{ column }">
                  <template v-if="column.dataIndex == 'presellPrice'">
                    <span><span style="color: #FF2929;font-size:13px;">*</span>{{ L('预售价格') }}</span>
                  </template>
                  <template v-else-if="column.dataIndex == 'firstMoney'">
                    <span><span style="color: #FF2929;font-size:13px;">*</span>{{ L('预售定金') }}</span>
                  </template>
                  <template v-else-if="column.dataIndex == 'presellStock'">
                    <span><span style="color: #FF2929;font-size:13px;">*</span>{{ L('预售库存') }}</span>
                  </template>
                  <template v-else-if="column.dataIndex == 'firstExpand'">
                    <div class="flex_row_center_center">
                      <span>{{ L('定金膨胀') }}</span>
                      <Tooltip placement="bottom">
                        <template #title> {{ L('设置定金可抵用金额，如商品总价为1000元，定金设置100元，定金膨胀设置200元，则定金抵用200元，尾款需支付800元') }} </template>
                        <div style="margin-left: 5px;margin-top: 3px;">
                          <AliSvgIcon iconName="iconwen" width="14px" height="14px" fillColor="#bfbbba" />
                        </div>
                      </Tooltip>
                    </div>
                  </template>
                  <template v-else>
                    {{ column.customTitle }}
                  </template>
                </template>
                <template #bodyCell="{ column, text, record,index }">
                  <template v-if="column.dataIndex === 'presellPrice'">
                    <Form.Item
                      :name="['selectedRows', indexs, 'productList',index,'presellPrice']"
                      :rules="record.state == 1?[
                        {
                          required: true,
                          message: L('该项必填'),
                        },
                      ]:[]"
                    >
                      <InputNumber
                        :max="9999999"
                        :min="0.01"
                        style="width: 400px !important"
                        :precision="2"
                        @blur="handleFieldBlur($event, 'presellPrice', record,0.01,9999999)"
                        v-model:value="record.presellPrice"
                      />
                    </Form.Item>
                  </template>
                  <template v-if="column.dataIndex === 'firstMoney'">
                    <Form.Item
                      :name="['selectedRows', indexs, 'productList', index, 'firstMoney']"
                      :rules="record.state == 1?[
                        {
                          required: true,
                          message: L('该项必填'),
                        },
                      ]:[]"
                    >
                      <InputNumber
                        :max="99999999"
                        :min="0.01"
                        style="width: 400px !important"
                        :precision="2"
                        @blur="(e) => {handleFieldBlur(e, 'firstMoney', record,0.01,99999999)}"
                        v-model:value="record.firstMoney"
                      />
                    </Form.Item>
                  </template>
                  <template v-if="column.dataIndex === 'firstExpand'">
                    <Form.Item
                      :name="['selectedRows', indexs, 'productList', index, 'firstExpand']"
                    >
                      <InputNumber
                        :max="99999999"
                        :min="0.01"
                        style="width: 400px !important"
                        :precision="2"
                        @blur="(e) => {handleFieldBlur(e, 'firstExpand', record,0.01,99999999)}"
                        v-model:value="record.firstExpand"
                      />
                    </Form.Item>
                  </template>
                  <template v-if="column.dataIndex === 'presellStock'">
                    <Form.Item
                      :name="['selectedRows', indexs, 'productList', index, 'presellStock']"
                      :rules="record.state == 1?[
                        {
                          required: true,
                          message: L('该项必填'),
                        },
                      ]:[]"
                    >
                      <InputNumber
                        :max="99999999"
                        :min="1"
                        style="width: 400px !important"
                        :precision="0"
                        @blur="(e) => {handleFieldBlur(e, 'presellStock', record,1,99999999)}"
                        v-model:value="record.presellStock"
                      />
                    </Form.Item>
                  </template>
                  <template v-if="column.dataIndex === 'state'">
                    <Switch
                      @change="
                        (e) =>
                        handleFieldChange(e ? 1 : 2, 'state', record,index,indexs)
                      "
                      :checked="text == 1 ? true : false"
                    />
                  </template>
                </template>
              </BasicTable>
            </div>
            <div style="width: 100%; height: 60px; background: #fff"></div>
          </Form>
        </div>
        <div
          class="m_diy_bottom_wrap"
          :style="{ position: 'fixed', left: getRealWidth + 10 + 'px' }"
        >
          <div class="add_goods_bottom_btn" @click="goBack"> {{ L('返回') }} </div>
          <div class="add_goods_bottom_btn add_goods_bottom_btn_sel" @click="handleSaveAllData">
            {{ L('保存') }}
          </div>
        </div>
      </Spin>
    </div>
    <SldSelMoreLeftRightSeckillGoods 
      @confirm-event="seleGoods"
      @cancle-event="sldHandleCancle" 
      :width="1000"
      :selectedRow="sele_more_goods.info"
      :selectedRowKey="sele_more_goods.ids"
      :title="L('请选择商品(至少选择一个)')"
      :height='height - 400'
      :modalVisible="modalVisibleGoods"
      :extra="sele_more_goods"
      >

    </SldSelMoreLeftRightSeckillGoods>
  </div>
</template>
<script setup>
  import { getCurrentInstance, ref, reactive, onMounted } from 'vue';
  import moment from 'moment';
  import dayjs from 'dayjs';
  import { Spin,Select,Form,Popconfirm,InputNumber,Checkbox,Switch,Input,RangePicker,DatePicker,Tooltip } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { sucTip, failTip } from '@/utils/utils';
  import { validatorEmoji } from '/@/utils/validate';
  import CommonTitle from '/@/components/CommonTitle/index.vue';
  import SldSelMoreLeftRightSeckillGoods from '/@/components/SldSelMoreLeftRightSeckillGoods/index.vue';
  import { BasicTable } from '/@/components/Table';
  import del_seckill_goods from '/src/assets/images/del_seckill_goods.png';
  import {
    getPreSellLabelListApi,
    getPreSellAddApi,
    getPreSellUpdateApi,
    getPreSellDetailApi
  } from '/@/api/promotion/presale';
  import { useUserStore } from '/@/store/modules/user';
  import { getSettingListApi } from '/@/api/common/common';
  import { useRoute, useRouter } from 'vue-router';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import { list_com_page_more } from '/@/utils/utils';
  import { pageClose } from '/@/utils/utils';
  import { useMultipleTabStore } from '/@/store/modules/multipleTab';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const loading = ref(false);
  const tabStore = useMultipleTabStore();
  const route = useRoute();
  const router = useRouter();
  const userStore = useUserStore();
  const title = ref(L('新建定金预售'))
  const { getRealWidth } = useMenuSetting();
  const leaderPromotion = ref(0)//团长优惠是否开启，默认为0 未开启，1为开启
  const compensateRate = ref(0)//赔偿倍数

  const battchVal = ref('');//批量设置里面的值
  const activity_labels = ref([])//活动标签
  const modalVisibleGoods = ref(false);
  const query = ref(route.query);
  const formRef = ref();
  const height = ref(document.body.clientHeight)
  const sele_more_goods = ref({
    info: [],//选择的商品数组
    ids: [],//选择的商品id数组
    min_num: 1,//最小数量，0为不限制
  })
  const detail = ref({
    selectedRows:[],
    selectedRowKeys:[]
  })

  // 定金预售
  const columns_spec = ref([
    {
      title: L('SKU规格'),
      dataIndex: 'specValues',
      align: 'center',
      width: 100,
      customRender: ({ text, record, index }) => {
        return text ? text : L('默认');
      },
    },
    {
      title: L('原价(¥)'),
      dataIndex: 'productPrice',
      align: 'center',
      width: 100,
    },
    {
      title: L('库存'),
      dataIndex: 'productStock',
      align: 'center',
      width: 100,
    },
    {
      title: L('预售价格'),
      dataIndex: 'presellPrice',
      align: 'center',
      width: 100,
    },
    {
      title: L('预售定金'),
      dataIndex: 'firstMoney',
      align: 'center',
      width: 100,
    },
    {
      title: L('定金膨胀'),
      dataIndex: 'firstExpand',
      align: 'center',
      width: 100,
    },
    {
      title: L('预售库存'),
      dataIndex: 'presellStock',
      align: 'center',
      width: 100,
    },
    {
      title: L('参与'),
      dataIndex: 'state',
      align: 'center',
      width: 100,
    },
  ])

  // 全款预售
  const columns_spec_all = ref([
  {
      title: L('SKU规格'),
      dataIndex: 'specValues',
      align: 'center',
      width: 100,
      customRender: ({ text, record, index }) => {
        return text ? text : L('默认');
      },
    },
    {
      title: L('原价(¥)'),
      dataIndex: 'productPrice',
      align: 'center',
      width: 100,
    },
    {
      title: L('库存'),
      dataIndex: 'productStock',
      align: 'center',
      width: 100,
    },
    {
      title: L('预售价格'),
      dataIndex: 'presellPrice',
      align: 'center',
      width: 100,
    },
    {
      title: L('预售库存'),
      dataIndex: 'presellStock',
      align: 'center',
      width: 100,
    },
    {
      title: L('参与'),
      dataIndex: 'state',
      align: 'center',
      width: 100,
    },
  ])

  // 获取详情
  const get_detail = async(id)=> {
    let res = await getPreSellDetailApi({ presellId: id })
    if(res.state == 200){
      loading.value = false
      res.data.selectedRowKeys = []
      res.data.selectedRows = []
      if (res.data.startTime) {
        res.data.remainStart = [
          dayjs(res.data.startTime, 'YYYY-MM-DD HH:mm:ss'),
          dayjs(res.data.endTime, 'YYYY-MM-DD HH:mm:ss'),
        ];
      }
      if (res.data.remainStartTime && Number(route.query.type) == 1) {
        res.data.remain = [
          dayjs(res.data.remainStartTime, 'YYYY-MM-DD HH:mm:ss'),
          dayjs(res.data.remainEndTime, 'YYYY-MM-DD HH:mm:ss'),
        ];
      }
      if (res.data.deliverTime) {
        res.data.deliverTime = dayjs(res.data.deliverTime, 'YYYY-MM-DD HH:mm:ss')
      }
      let goodsInfo = res.data.goodsList;
      goodsInfo.forEach(item=>{
        res.data.selectedRowKeys.push(item.goodsId);
        let productList = [];
        item.productList.forEach(child=>{
          productList.push({
            goodsId:item.goodsId,
            productId:child.productId,
            productPrice:child.productPrice,
            productStock:child.stock,
            specValues:child.specValues,
            presellPrice:child.presellPrice!=null ? child.presellPrice : '',
            presellStock:child.presellStock!=null ? child.presellStock : '',
            firstMoney:child.firstMoney!=null ? child.firstMoney : '',
            firstExpand:child.firstExpand!=null ? child.firstExpand*1 : '',
            state:1,
          });
        })
        res.data.selectedRows.push({
          goodsId:item.goodsId,
          goodsPrice:item.goodsPrice,
          goodsImage:item.goodsImage,
          goodsName:item.goodsName,
          productList:productList,
        });
      });
      sele_more_goods.value.ids = [...res.data.selectedRowKeys];
      sele_more_goods.value.info = JSON.parse(JSON.stringify(res.data.selectedRows));
      if(route.query.tar == 'copy'){
        //复制功能需要部分内容
        res.data.presellName = '';//清空活动名称
        //清空活动时间
        res.data.startTime = '';
        res.data.endTime = '';
        //清空发货时间
        res.data.deliverTime = '';
        if(Number(route.query.type) == 1){
          //定金预售清空尾款时间
          res.data.remainStartTime = '';
          res.data.remainEndTime = '';
        }
      }
      detail.value = res.data;
    }else{
      failTip(res.msg)
    }
  }


  //获取活动标签
  const get_activity_label = async(id)=> {
    let res = await getPreSellLabelListApi({ pageSize: list_com_page_more })
    if(res.state == 200){
      activity_labels.value = res.data
    }
  }

  // 打开商品弹窗
  const addGoods = ()=> {
    modalVisibleGoods.value = true
  }

  // 关闭弹窗
  const sldHandleCancle = ()=>{
    modalVisibleGoods.value = false
  }

  // 弹窗点击确定
  const seleGoods = (selectedRowsP,selectedRowKeysP)=>{
    selectedRowsP.map((item, index) => {
      item.productList.map((child_item) => {
        child_item.goodsId = item.goodsId;
        child_item.state = 1;
      });
    });
    //如果多次选择的话，数据要保留之前的
    detail.value.selectedRowKeys.map((item) => {
      if (selectedRowKeysP.indexOf(item) > -1) {
        let pre_item_data = detail.value.selectedRows.filter(val => val.goodsId == item)[0];
        for (let i = 0; i < selectedRowsP.length; i++) {
          if (selectedRowsP[i].goodsId == item) {
            selectedRowsP[i] = { ...pre_item_data };
            break;
          }
        }
      }
    });
    sele_more_goods.value.ids = [...selectedRowKeysP];
    sele_more_goods.value.info = JSON.parse(JSON.stringify(selectedRowsP));
    detail.value.selectedRows = selectedRowsP
    detail.value.selectedRowKeys = selectedRowKeysP
    sldHandleCancle()
  }

  // 商品批量设置
  const batchConfirm = (e, type, val,indexs)=> {
    let cur_goods = detail.value.selectedRows.filter(item=>item.goodsId == val.goodsId)[0];
    if(!battchVal.value){
      return false
    }
    //数据验证
    for(let i=0;i<cur_goods.productList.length;i++){
      let item_data = cur_goods.productList[i];
      let specValuesName = item_data.specValues?item_data.specValues:L('默认规格')
      if(type == 'presellStock'&&battchVal.value>item_data.productStock){
        //预售库存不能超过所有sku里面的最小库存
        battchVal.value = item_data.productStock;
      }

      if(type == 'presellPrice'){
        //预售价格必须小于商品价格
        if(battchVal.value>=item_data.productPrice){
          failTip(`${specValuesName}${L('的预售价格必须小于商品价格')}`);
          battchVal.value = ''
          return false;
        }
        if(Number(route.query.type) == 1){
          if(item_data.firstMoney && battchVal.value <= item_data.firstMoney){
            failTip(`${specValuesName}${L('的预售价格必须大于预售定金')}`)
            battchVal.value = ''
            return false;
          }else if(item_data.firstExpand && battchVal.value <= item_data.firstExpand){
            failTip(`${specValuesName}${L('的预售价格必须大于膨胀金额')}`)
            battchVal.value = ''
            return false;
          }
        }
      }

      //预售定金不可以超过预售价格，且不可以超过定金膨胀
      if(type == 'firstMoney'){
        if(item_data.presellPrice&&battchVal.value>=item_data.presellPrice){
          failTip(`${specValuesName}${L('的预售定金必须小于预售价格')}`);
          battchVal.value = ''
          return false;
        }else if(item_data.firstExpand && battchVal.value >= item_data.firstExpand){
          failTip(`${specValuesName}${L('的预售定金必须小于定金膨胀金额')}`);
          battchVal.value = ''
          return false;
        }else if(!item_data.presellPrice && !item_data.firstExpand && battchVal.value >= item_data.productPrice){
          failTip(`${specValuesName}${L('的预售定金必须小于商品价格')}`);
          battchVal.value = ''
          return false;
        }
      }

     //定金膨胀不可以超过预售价格，但是要大于预售定金
      if(type == 'firstExpand'){
        if(item_data.firstMoney&&battchVal.value<=item_data.firstMoney){
          failTip(`${specValuesName}${L('的定金膨胀必须大于预售定金')}`);
          battchVal.value = ''
          return false;
        }else if(item_data.presellPrice && battchVal.value >= item_data.presellPrice){
          failTip(`${specValuesName}${L('的定金膨胀必须小于预售价格')}`);
          battchVal.value = ''
          return false;
        }else if(!item_data.presellPrice && !item_data.firstExpand && battchVal.value >= item_data.productPrice){
          failTip(`${specValuesName}${L('的定金膨胀必须小于商品价格')}`);
          battchVal.value = ''
          return false;
        }
      }
    }

    let sku_product_id = [];
    cur_goods.productList.map((item,index) => {
      item[type] = battchVal.value;
      sku_product_id.push(item.productId);
      formRef.value.clearValidate([['selectedRows', indexs, 'productList',index,type]]);
    });
    battchVal.value = ''
   
  }

  //批量设置
  const handleFieldBattchChange = (e, type, item)=> {
    battchVal.value = e
  }

  //全部参与事件
  const setAll = (e, val) => {
    let states = [];
    for (let i in detail.value.selectedRows) {
      if (detail.value.selectedRows[i].goodsId == val.goodsId) {
        detail.value.selectedRows[i].productList.map(item => {
          item.state = e.target.checked ? 1 : 0;
          states.push('state'+item.productId);
        });
        break;
      }
    }
    if(!e.target.checked){
      detail.value.selectedRows.forEach((item,indexs)=>{
        item.productList.forEach((it,index)=>{
          formRef.value.clearValidate([['selectedRows', indexs, 'productList',index,'presellPrice'],['selectedRows', indexs, 'productList',index,'firstMoney'],['selectedRows', indexs, 'productList',index,'presellStock']]);
        })
      })
    }
  };

  const handleFieldBlur = (val, fieldName, record,min,max)=>{
    val = val.target.value
    if(val<min&&val.length!=0){
      val = min
    }
    if(val>max&&val.length!=0){
      val = max
    }
    if(val.length!=0){
      handleFiledContent(val*1,fieldName, record);

    }
  }

  const handleFieldChange = (val, fieldName, record,index,indexs)=> {
    handleFiledContent(val, fieldName, record);
    formRef.value.clearValidate([['selectedRows', indexs, 'productList',index,'presellPrice'],['selectedRows', indexs, 'productList',index,'firstMoney'],['selectedRows', indexs, 'productList',index,'presellStock']]);
  }
  

  //spec_data_table 表格编辑事件
  const handleFiledContent = (val, fieldName, record,index,indexs)=> {
    //预售库存都不可以超过最大库存
    if (fieldName == 'presellStock' && val > record.productStock) {
      val = record.productStock;
    }

    //预售价格必须小于商品价格
    if (fieldName == 'presellPrice') {
      if(val>=record.productPrice){
        val='';
        failTip(`${L('预售价格必须小于商品价格')}`);
      }
      if(Number(route.query.type) == 1){
        if(record.firstMoney && val <= record.firstMoney){
          val = '';
          failTip(`${L('预售价格必须大于预售定金')}`);
        }else if(record.firstExpand && val <= record.firstExpand){
          val = '';
          failTip(`${L('预售价格必须大于膨胀金额')}`);
        }
      }
    }

    //预售定金不可以超过预售价格，且不可以超过定金膨胀
    if(fieldName == 'firstMoney'&&val){
      if(record.presellPrice && val >= record.presellPrice){
        val='';
        failTip(`${L('预售定金必须小于预售价格')}`);
      }else if(record.firstExpand && val >= record.firstExpand){
        val='';
        failTip(`${L('预售定金必须小于定金膨胀金额')}`);
      }else if(!record.presellPrice && !record.firstExpand && val >= record.productPrice){
        val='';
        failTip(`${L('预售定金必须小于商品价格')}`);
      }
    }

    //定金膨胀不可以超过预售价格，但是要大于预售定金
    if(fieldName == 'firstExpand'&&val){
      if(record.firstMoney && val <= record.firstMoney){
        val='';
        failTip(`${L('定金膨胀必须大于预售定金')}`);
      }else if(record.presellPrice && val >= record.presellPrice){
        val='';
        failTip(`${L('定金膨胀必须小于预售价格')}`);
      }else if(!record.presellPrice && !record.firstExpand && val >= record.productPrice){
        val='';
        failTip(`${L('定金膨胀必须小于商品价格')}`);
      }
    }

    let tar_sku_list = detail.value.selectedRows.filter(item => item.goodsId == record.goodsId);
    if (tar_sku_list.length > 0) {
      let tar_data = tar_sku_list[0].productList.filter(item => item.productId == record.productId);
      if (tar_data.length > 0) {
        tar_data[0][fieldName] = val;
      }
    }
  }

  //删除添加的商品spu
  const delSpu = (goodsId) => {
    detail.value.selectedRows = detail.value.selectedRows.filter(item => item.goodsId != goodsId);
    detail.value.selectedRowKeys = detail.value.selectedRowKeys.filter(item => item != goodsId);
    sele_more_goods.value.ids = [...detail.value.selectedRowKeys];
    sele_more_goods.value.info = JSON.parse(JSON.stringify(detail.value.selectedRows));
  };

  // 返回
  const goBack = () => {
    if (window.history && window.history.length == 1) {
      pageClose();
    } else {
      const { fullPath } = route;
      tabStore.closeTabByKey(fullPath, router);
      router.back();
    }
  };

  // 保存
  const handleSaveAllData = async()=> {
    try {
      formRef.value.validate().then(async (values) => {
        let params = {};
        //秒杀商品结束时间
        if (values.remainStart) {
          params.startTime = values.remainStart[0] ? values.remainStart[0].format('YYYY-MM-DD HH:mm:00') : '';
          params.endTime = values.remainStart[1] ? values.remainStart[1].format('YYYY-MM-DD HH:mm:00') : '';
          values.remainStart = '';
        }
        // params.presellId = route.query.id;// 预售活动id
        params.presellName = values.presellName
        params.presellLabelId = values.presellLabelId
        params.buyLimit = values.buyLimit
        if (values.remain) {
          params.remainStartTime = values.remain[0] ? values.remain[0].format('YYYY-MM-DD HH:mm:00') : '';
          params.remainEndTime = values.remain[1] ? values.remain[1].format('YYYY-MM-DD HH:mm:00') : '';
          values.remain = '';
        }
        if (values.deliverTime) {
          params.deliverTime = values.deliverTime.format('YYYY-MM-DD HH:mm:00');
        }
        if(Number(route.query.type) == 1){
          //定金预售
          //定金开始时间必须小于结束时间
          if(Date.parse(params.startTime) >= Date.parse(params.endTime)){
            failTip(L('定金开始时间必须小于结束时间'));
            return false;
          }
          //尾款开始时间必须小于结束时间
          if(Date.parse(params.remainStartTime) >= Date.parse(params.remainEndTime)){
            failTip(L('尾款开始时间必须小于结束时间'));
            return false;
          }
          //尾款开始时间必须大于定金结束时间
          if(Date.parse(params.remainStartTime) <= Date.parse(params.endTime)){
            failTip(L('尾款开始时间必须大于定金结束时间'));
            return false;
          }
          //发货时间不能小于尾款结束时间
          if(Date.parse(params.deliverTime) < Date.parse(params.remainEndTime)){
            failTip(L('发货时间不能小于尾款结束时间'));
            return false;
          }
        }else{
          //全款预售
          //活动开始时间必须小于结束时间
          if(Date.parse(params.startTime) >= Date.parse(params.endTime)){
            failTip(L('活动开始时间必须小于结束时间'));
            return false;
          }
          //发货时间不能小于活动结束时间
          if(Date.parse(params.deliverTime) < Date.parse(params.endTime)){
            failTip(L('发货时间不能小于活动结束时间'));
            return false;
          }
        }
        params.goodsInfoList = [];
        params.type = Number(route.query.type)*1;
        let joined_sku_array = [];
        detail.value.selectedRows.forEach((item) => {
          item.productList.forEach((child) => {
            if (child.state == 1) {
              joined_sku_array.push({
                productId: child.productId,
                presellPrice: child.presellPrice,
                presellStock: child.presellStock,
                firstMoney: child.firstMoney,
                firstExpand: child.firstExpand,
              });
            }
          });
        });
        if (joined_sku_array.length == 0) {
          failTip(`${L('请选择要参与活动的商品')}`);
          return false;
        }
        let goodsList = JSON.stringify(joined_sku_array)
        params.goodsList = goodsList
        delete params.goodsInfoList
        loading.value = true
        let res;
        if(route.query.id != undefined && Number(route.query.id) > 0 && route.query.tar == 'edit'){
          params.presellId = route.query.id;// 活动id
          res = await getPreSellUpdateApi(params)
        }else{
          res = await getPreSellAddApi(params)
        }
        if(res.state == 200){
          loading.value = false
          sucTip(res.msg);
          userStore.setDelKeepAlive([route.name,'PromotionPresale'])
          userStore.setUpdateTab({name:'PromotionPresale',index:route.query&&route.query.type=='2'?'2':'1'})
          setTimeout(() => {
            goBack();
          }, 500);
        }else{
          loading.value = false
          failTip(res.msg)
        }
      })
    } catch (error) {
      console.info(error,'error')
    }
  }

  //获取系统配置(预售活动是否开启)
  const getSetting = async () => {
    try {
      let res = await getSettingListApi({ str: 'presale_compensate' });
      if (res.state == 200) {
        compensateRate.value = res.data[0].value
        // isFirstLoading.value = false
      }
    } catch (error) {}
  };

  onMounted(() => {
    if (route.query.id != undefined && Number(route.query.id) > 0) {
      loading.value = true
      get_detail(route.query.id);
    }
    if(route.query.type&&route.query.type=='1'){
      title.value = L('新建定金预售')
      if(route.query.tar&&route.query.tar=='edit'){
        title.value = L('编辑定金预售')
      }
    }else if(route.query.type&&route.query.type=='2'){
      title.value = L('新建全款预售')
      if(route.query.tar&&route.query.tar=='edit'){
        title.value = L('编辑全款预售')
      }
    }
    get_activity_label()
    getSetting()
  });
</script>
<style lang="less">
@import './style/add_presale.less';
</style>
