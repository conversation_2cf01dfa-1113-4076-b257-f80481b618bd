<template>
  <div class="section_padding add_full_acm">
    <div class="section_padding_back full_activity">
      <SldComHeader :title="L('满减活动')" />
      <div class="com_line" style="height: 1px"></div>
      <Spin :spinning="loading">
        <div class="add_coupon_height">
          <Form layout="inline" ref="formRef" :model="detail">
            <CommonTitle :text="L('活动基本信息')" style="margin-top: 10px" />
            <div class="full_acm_activity flex_column_start_start">
              <!-- 活动名称 start -->
              <div class="item flex_row_start_start">
                <div class="left"> <span style="color: red">*</span>{{ L('活动名称') }} </div>
                <div class="right">
                  <Form.Item
                    :extra="L('最多输入20个字')"
                    name="fullName"
                    style="width: 400px"
                    :rules="[
                      {
                        required: true,
                        whitespace: true,
                        message: L('请输入活动名称'),
                      },
                      {
                        validator: async (rule, value) => {
                          await validatorEmoji(rule, value);
                        },
                      },
                    ]"
                  >
                    <Input
                      :maxLength="20"
                      :disabled="viewFlag"
                      style="width: 400px !important"
                      :placeholder="L('请输入活动名称')"
                      v-model:value="detail.fullName"
                    />
                  </Form.Item>
                </div>
              </div>
              <!-- 活动名称 end -->
              <!-- 活动时间 start -->
              <div class="item flex_row_start_start">
                <div class="left"> <span style="color: red">*</span>{{ L('活动时间') }}</div>
                <div class="right">
                  <Form.Item
                    name="activityTime"
                    :extra="L('活动时间不可与其他活动重叠')"
                    style="width: 400px"
                    :rules="[
                      {
                        required: true,
                        message: L('请选择活动时间'),
                      },
                    ]"
                  >
                    <RangePicker
                      :disabled="viewFlag"
                      style="width: 400px"
                      :format="'YYYY-MM-DD HH:mm:00'"
                      v-model:value="detail.activityTime"
                      :disabledDate="
                        (currentDate) => currentDate && currentDate < moment().subtract(1, 'days')
                      "
                      :showTime="{ format: 'HH:mm' }"
                      :placeholder="[L('开始时间'), L('结束时间')]"
                      :getPopupContainer="
                        (triggerNode) => {
                          return triggerNode.parentNode;
                        }
                      "
                    />
                  </Form.Item>
                </div>
              </div>
              <!-- 活动时间 end -->
              <!-- 优惠门槛 start -->
              <div class="item flex_row_start_start">
                <div class="left"> <span style="color: red">*</span>{{ L('优惠门槛') }} </div>
                <div class="right">
                  <Form.Item
                    :extra="L('以元为单位，设置使用该活动的最低金额')"
                    name="fullValue"
                    style="width: 400px"
                    :rules="[
                      {
                        required: true,
                        message: L('请输入优惠门槛'),
                      },
                    ]"
                  >
                    <InputNumber
                      :max="9999999"
                      :min="1"
                      :disabled="viewFlag"
                      style="width: 400px !important"
                      :precision="2"
                      :placeholder="L('请输入优惠门槛')"
                      v-model:value="detail.fullValue"
                    />
                  </Form.Item>
                </div>
              </div>
              <!-- 优惠门槛 end -->
              <!-- 优惠内容 start -->
              <div class="item flex_row_start_start">
                <div class="left"> <span style="color: red">*</span>{{ L('优惠内容') }}</div>
                <div class="right">
                  <Form.Item
                    :extra="L('以元为单位，满足优惠门槛后可以享受优惠的金额')"
                    name="minusValue"
                    class="publishValue_box"
                    style="width: 300px"
                  >
                    <div class="flex_row_start_center">
                      <span
                        style="display: inline-block; margin-right: 5px; color: rgb(0 0 0 / 65%)"
                        >{{ L('减') }}</span
                      >
                      <InputNumber
                        :max="9999999"
                        :min="0.01"
                        :disabled="viewFlag"
                        style="width: 140px !important"
                        :precision="2"
                        :placeholder="L('请输入优惠内容')"
                        v-model:value="detail.minusValue"
                      />
                      <span
                        style="display: inline-block; margin-left: 5px; color: rgb(0 0 0 / 65%)"
                        >{{ L('元') }}</span
                      >
                    </div>
                  </Form.Item>
                </div>
              </div>
              <!-- 优惠内容 end -->
              <!-- 送优惠券 start -->
              <div class="item flex_row_start_start">
                <div class="left"> </div>
                <div class="right check_input">
                  <Form.Item>
                    <div class="flex_row_start_center">
                      <Checkbox
                        :checked="couponFlag"
                        :disabled="viewFlag"
                        @change="checkClick('add', null)"
                      >
                        {{ L('送优惠券') }}
                      </Checkbox>
                    </div>
                    <div class="sel_goods flex_column_start_start" v-if="couponFlag">
                      <Button
                        type="primary"
                        @click="handleVoucher('add', null)"
                        v-if="couponFlag && !viewFlag"
                        style="margin-top: 5px"
                        >选择优惠券</Button
                      >
                      <div
                        class=""
                        v-if="
                          sel_voucher.selectedRows != undefined &&
                          sel_voucher.selectedRows.length > 0
                        "
                        style="margin-top: 10px"
                      >
                        <BasicTable
                          style="width: 800px"
                          rowKey="couponId"
                          :columns="columns_sel_voucher"
                          :dataSource="sel_voucher.selectedRows"
                          :pagination="false"
                          :bordered="true"
                          :ellipsis="false"
                          :actionColumn="
                            viewFlag
                              ? null
                              : {
                                  title: L('操作'),
                                  width: 80,
                                  dataIndex: 'action',
                                }
                          "
                        >
                          <template #bodyCell="{ column, text, record }">
                            <template v-if="column.dataIndex === 'action'">
                              <div
                                @click="delvoucher(record.couponId)"
                                class="coupon_goods_operate flex_row_center_center"
                              >
                                <AliSvgIcon
                                  iconName="iconshanchu5"
                                  width="18px"
                                  height="18px"
                                  :fillColor="'#2d2d2d'"
                                />
                              </div>
                            </template>
                          </template>
                        </BasicTable>
                      </div>
                    </div>
                  </Form.Item>
                </div>
              </div>
              <!-- 送优惠券 end -->
              <!-- 送赠品 start -->
              <div class="item flex_row_start_start">
                <div class="left"> </div>
                <div class="right check_input">
                  <Form.Item>
                    <div class="flex_row_start_center">
                      <Checkbox
                        :checked="goodsFlag"
                        :disabled="viewFlag"
                        @change="checkClick('add', 'goods')"
                      >
                        {{ L('送赠品') }}
                      </Checkbox>
                    </div>
                    <div class="sel_goods flex_column_start_start" v-if="goodsFlag">
                      <Button
                        type="primary"
                        @click="handleVoucher('add', 'goods')"
                        v-if="goodsFlag && !viewFlag"
                        style="margin-top: 5px"
                        >选择赠品</Button
                      >
                      <div
                        class=""
                        v-if="
                          sel_goods.selectedRows != undefined && sel_goods.selectedRows.length > 0
                        "
                        style="margin-top: 10px"
                      >
                        <BasicTable
                          style="width: 800px"
                          rowKey="goodsId"
                          :columns="columns_spu"
                          :dataSource="sel_goods.selectedRows"
                          :pagination="false"
                          :bordered="true"
                          :ellipsis="false"
                          :actionColumn="
                            viewFlag
                              ? null
                              : {
                                  title: L('操作'),
                                  width: 80,
                                  dataIndex: 'action',
                                }
                          "
                        >
                          <template #bodyCell="{ column, text, record }">
                            <template v-if="column.dataIndex == 'mainImage'">
                              <Popover placement="rightTop">
                                <template #content>
                                  <div style="max-width: 200px; text-align: center">
                                    <img
                                      :src="text"
                                      alt=""
                                      style="max-width: 100%; max-height: 100%"
                                    />
                                  </div>
                                </template>
                                <div class="business_load_img">
                                  <img :src="text" alt="" />
                                </div>
                              </Popover>
                            </template>
                            <template v-if="column.dataIndex == 'goodsPrice'">
                              {{ text || text == 0 ? '￥' + Number(text).toFixed(2) : '--' }}
                            </template>
                            <template v-if="column.dataIndex == 'goodsStock'">
                              {{ text ? text : record.productStock ? record.productStock : '--' }}
                            </template>
                            <template v-if="column.dataIndex === 'action'">
                              <div
                                @click="delGoods(record.goodsId)"
                                class="coupon_goods_operate flex_row_center_center"
                              >
                                <AliSvgIcon
                                  iconName="iconshanchu5"
                                  width="18px"
                                  height="18px"
                                  :fillColor="'#2d2d2d'"
                                />
                              </div>
                            </template>
                          </template>
                        </BasicTable>
                      </div>
                    </div>
                  </Form.Item>
                </div>
              </div>
              <!-- 送赠品 end -->
            </div>
          </Form>
          <div style="width: 100%; height: 60px; background: #fff"></div>
        </div>
        <div
          class="m_diy_bottom_wrap"
          :style="{ position: 'fixed', left: getRealWidth + 10 + 'px' }"
        >
          <div
            class="add_goods_bottom_btn"
            :class="{ add_goods_bottom_btn_rig: viewFlag }"
            @click="goBack"
          >
            {{ L('返回') }}
          </div>
          <div
            class="add_goods_bottom_btn add_goods_bottom_btn_sel"
            v-if="!viewFlag"
            @click="handleSaveAllData"
          >
            {{ L('保存') }}
          </div>
        </div>
      </Spin>
    </div>
    <SldSelGoodsSingleDiy
      :modalTitle="modalTitle"
      @confirm-event="handleConfirm"
      @cancle-event="handleCancle"
      :link_type="link_type"
      :checkedType="checkedType"
      :selectedRows="selectedRows"
      :selectedRowKeys="selectedRowKeys"
      :modalVisible="visibleModal"
    >
    </SldSelGoodsSingleDiy>
  </div>
</template>
<script setup>
import { getCurrentInstance, ref, reactive, onMounted } from 'vue';
import SldComHeader from '/@/components/SldComHeader/index.vue';
import { BasicTable } from '/@/components/Table';
import CommonTitle from '/@/components/CommonTitle/index.vue';
import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
import { validatorEmoji } from '/@/utils/validate';
import SldSelGoodsSingleDiy from '/@/components/SldSelGoodsSingleDiy/index.vue';
import { getFullAcmUpdateApi, getFullAcmAddApi, getFullAcmDetailApi } from '/@/api/promotion/full';
import moment from 'moment';
import dayjs from 'dayjs';
import { useUserStore } from '/@/store/modules/user';
import { useRoute, useRouter } from 'vue-router';
import { sucTip, failTip } from '@/utils/utils';
import { pageClose } from '/@/utils/utils';
import { useMultipleTabStore } from '/@/store/modules/multipleTab';
import {
  Checkbox,
  Popover,
  Spin,
  Form,
  Input,
  InputNumber,
  RadioGroup,
  Radio,
  Tree,
  RangePicker,
  Button,
} from 'ant-design-vue';

const vm = getCurrentInstance();
const L = vm?.appContext.config.globalProperties.$sldComLanguage;

const route = useRoute();
const tabStore = useMultipleTabStore();
const router = useRouter();
const userStore = useUserStore();
const sel_goods = ref({
  selectedRows: [],
  selectedRowKeys: [],
}); //选择的赠品信息
const { getRealWidth } = useMenuSetting();
const sel_voucher = ref({
  selectedRows: [],
  selectedRowKeys: [],
}); //选择的优惠券信息

const columns_sel_voucher = ref([
  {
    title: L('优惠券名称'),
    dataIndex: 'couponName',
    align: 'center',
    width: 150,
  },
  {
    title: L('优惠券类型'),
    dataIndex: 'couponTypeValue',
    align: 'center',
    width: 100,
  },
  {
    title: L('优惠券内容'),
    dataIndex: 'couponContent',
    align: 'center',
    width: 100,
  },
  {
    title: L('剩余可用'),
    dataIndex: 'remainNum',
    align: 'center',
    width: 100,
  },
]);

const columns_spu = ref([
  {
    title: L('商品图片'),
    dataIndex: 'mainImage',
    align: 'center',
    width: 100,
  },
  {
    title: L('商品名称'),
    dataIndex: 'goodsName',
    align: 'center',
    width: 150,
  },
  {
    title: L('商品价格'),
    dataIndex: 'goodsPrice',
    align: 'center',
    width: 100,
  },
  {
    title: L('商品库存'),
    dataIndex: 'goodsStock',
    align: 'center',
    width: 100,
  },
]);

const link_type = ref('voucher');
const loading = ref(false);
const checkedType = ref(false);
const query = ref(route.query);
const selectedRows = ref([]);
const selectedRowKeys = ref([]);
const visibleModal = ref(false);
const formRef = ref();
const modalTitle = ref('');
const detail = ref({}); //活动详情数据
const viewFlag = ref(route.query.tar != undefined && route.query.tar == 'view' ? true : false);

const couponFlag = ref(false);
const goodsFlag = ref(false);

const checkClick = (type, link) => {
  if (link == 'goods') {
    goodsFlag.value = !goodsFlag.value;
    if (!goodsFlag.value) {
      sel_goods.value.selectedRows = [];
      sel_goods.value.selectedRowKeys = [];
    }
  } else {
    couponFlag.value = !couponFlag.value;
    if (!couponFlag.value) {
      sel_voucher.value.selectedRows = [];
      sel_voucher.value.selectedRowKeys = [];
    }
  }
};

// 优惠券弹框打开
const handleVoucher = (type, link) => {
  checkedType.value = false;
  if (link == 'goods') {
    checkedType.value = true;
    modalTitle.value = L('选择商品');
    link_type.value = 'goods';
    selectedRows.value = JSON.parse(JSON.stringify(sel_goods.value.selectedRows));
    selectedRowKeys.value = JSON.parse(JSON.stringify(sel_goods.value.selectedRowKeys));
    visibleModal.value = true;
  } else {
    checkedType.value = true;
    link_type.value = 'voucher';
    selectedRows.value = JSON.parse(JSON.stringify(sel_voucher.value.selectedRows));
    selectedRowKeys.value = JSON.parse(JSON.stringify(sel_voucher.value.selectedRowKeys));
    modalTitle.value = L('选择优惠券');
    visibleModal.value = true;
  }
};

// 优惠券弹框确定
const handleConfirm = (record, ids) => {
  if (link_type.value == 'voucher') {
    if (record.length > 10) {
      failTip('最多可以选择10张优惠券');
      return;
    }
    sel_voucher.value.selectedRows = record;
    sel_voucher.value.selectedRowKeys = ids;
  } else {
    if (record.length > 10) {
      failTip('最多可以选择10个赠品');
      return;
    }
    sel_goods.value.selectedRows = record;
    sel_goods.value.selectedRowKeys = ids;
  }
  visibleModal.value = false;
};

//优惠券删除事件
const delvoucher = (couponId) => {
  let selectedRows = JSON.parse(JSON.stringify(sel_voucher.value.selectedRows));
  let selectedRowKeys = JSON.parse(JSON.stringify(sel_voucher.value.selectedRowKeys));
  selectedRows = selectedRows.filter((item) => item.couponId != couponId);
  selectedRowKeys = selectedRowKeys.filter((item) => item != couponId);
  sel_voucher.value.selectedRows = JSON.parse(JSON.stringify(selectedRows));
  sel_voucher.value.selectedRowKeys = JSON.parse(JSON.stringify(selectedRowKeys));
};

//赠品删除事件
const delGoods = (goodsId) => {
  let selectedRows = JSON.parse(JSON.stringify(sel_goods.value.selectedRows));
  let selectedRowKeys = JSON.parse(JSON.stringify(sel_goods.value.selectedRowKeys));
  selectedRows = selectedRows.filter((item) => item.goodsId != goodsId);
  selectedRowKeys = selectedRowKeys.filter((item) => item != goodsId);
  sel_goods.value.selectedRows = JSON.parse(JSON.stringify(selectedRows));
  sel_goods.value.selectedRowKeys = JSON.parse(JSON.stringify(selectedRowKeys));
};

// 优惠券弹框取消
const handleCancle = () => {
  visibleModal.value = false;
};

const goBack = () => {
  if (window.history && window.history.length == 1) {
    pageClose();
  } else {
    const { fullPath } = route;
    tabStore.closeTabByKey(fullPath, router);
    router.back();
  }
};

// 保存
const handleSaveAllData = () => {
  formRef.value.validate().then(async (values) => {
    if (values.activityTime) {
      values.startTime = values.activityTime[0]
        ? values.activityTime[0].format('YYYY-MM-DD HH:mm:00')
        : '';
      values.endTime = values.activityTime[1]
        ? values.activityTime[1].format('YYYY-MM-DD HH:mm:00')
        : '';
      delete values.activityTime;
    }
    //活动开始时间必须小于结束时间
    if (Date.parse(values.startTime) > Date.parse(values.endTime)) {
      failTip(L('活动开始时间必须小于结束时间'));
      return false;
    }
    //优惠券id
    if (
      couponFlag.value &&
      sel_voucher.value.selectedRows != undefined &&
      sel_voucher.value.selectedRows.length > 0
    ) {
      values.sendCouponIds = JSON.parse(JSON.stringify(sel_voucher.value.selectedRowKeys)).join(
        ',',
      );
    } else {
      if (couponFlag.value) {
        failTip(L('请选择优惠券'));
        return;
      } else {
        values.sendCouponIds = '';
      }
    }
    //商品id
    if (
      goodsFlag.value &&
      sel_goods.value.selectedRows != undefined &&
      sel_goods.value.selectedRows.length > 0
    ) {
      values.sendGoodsIds = JSON.parse(JSON.stringify(sel_goods.value.selectedRowKeys)).join(',');
    } else {
      if (goodsFlag.value) {
        failTip(L('请选择赠品'));
        return;
      } else {
        values.sendGoodsIds = '';
      }
    }
    //优惠内容必填
    if (!(values.minusValue * 1 || values.sendCouponIds || values.sendGoodsIds)) {
      failTip(L('请设置优惠内容～'));
      return false;
    }
    //满减内容必须小于优惠门槛
    if (values.minusValue >= values.fullValue) {
      failTip(L('优惠内容金额必须小于优惠门槛金额'));
      return;
    }
    loading.value = true;
    let res;
    if (route.query.id != undefined && Number(route.query.id) > 0 && route.query.tar == 'edit') {
      //编辑满减
      values.fullId = route.query.id;
      res = await getFullAcmUpdateApi(values);
    } else {
      //新增满减
      res = await getFullAcmAddApi(values);
    }
    if (res.state == 200) {
      sucTip(res.msg);
      userStore.setDelKeepAlive([route.name, 'PromotionFullAcm']);
      setTimeout(() => {
        goBack();
        loading.value = false;
      }, 500);
    } else {
      loading.value = false;
      failTip(res.msg);
    }
  });
};

// 查看详情
const get_full_acm_detail = async () => {
  try {
    loading.value = true;
    let res = await getFullAcmDetailApi({ fullId: route.query.id });
    if (res.state == 200) {
      loading.value = false;
      if (res.data.startTime) {
        res.data.activityTime = [
          dayjs(res.data.startTime, 'YYYY-MM-DD HH:mm:ss'),
          dayjs(res.data.endTime, 'YYYY-MM-DD HH:mm:ss'),
        ];
      }
      detail.value = res.data;
      //初始化选中的优惠券数据
      if (
        detail.value.couponList != null &&
        detail.value.couponList.length != undefined &&
        detail.value.couponList.length > 0
      ) {
        sel_voucher.value.selectedRows = JSON.parse(JSON.stringify(detail.value.couponList));
        sel_voucher.value.selectedRowKeys = [];
        sel_voucher.value.selectedRows.forEach((item) => {
          item.remainNum = item.couponStock;
          sel_voucher.value.selectedRowKeys.push(item.couponId);
        });
        couponFlag.value = true;
      }
      //初始化选中的商品数据
      if (
        detail.value.giftList != null &&
        detail.value.giftList.length != undefined &&
        detail.value.giftList.length > 0
      ) {
        sel_goods.value.selectedRows = JSON.parse(JSON.stringify(detail.value.giftList));
        sel_goods.value.selectedRowKeys = [];
        sel_goods.value.selectedRows.forEach((item) => {
          item.mainImage = item.goodsImage;
          sel_goods.value.selectedRowKeys.push(item.goodsId);
        });
        goodsFlag.value = true;
      }
    }
  } catch (error) {}
};

onMounted(() => {
  if (route.query.id != undefined && Number(route.query.id) > 0) {
    get_full_acm_detail();
  }
});
</script>
<style lang="less">
@import './style/add_full_acm.less';
.add_full_acm {
  .ant-table-body {
    height: auto !important;
    max-height: 400px !important;
  }
  .check_input {
    .ant-form-item-control-input {
      align-items: start;
    }
  }
}
.business_load_img {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 35px;
  height: 35px;
  margin: auto;

  img {
    max-width: 100%;
    max-height: 100%;
  }
}
</style>
