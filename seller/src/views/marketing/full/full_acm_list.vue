<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader :title="L('满减活动')" />
      <div class="full_acm_list">
        <BasicTable @register="registerTable" rowKey="fullId">
          <template #tableTitle>
            <div class="toolbar">
              <div class="toolbar_btn" @click="add_full">
                <AliSvgIcon iconName="iconfabu1" width="15px" height="15px" :fillColor="'#fa6f1e'" />
                <span style="margin-left: 4px">{{ L('新建满减活动') }}</span>
              </div>
            </div>
          </template>
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.dataIndex === 'startTime'">
              <div class="voucher_time_wrap">
                <p>{{ text }}</p>
                <p>~</p>
                <p>{{ record.endTime }}</p>
              </div>
            </template>
            <template v-if="column.key === 'action'">
              <TableAction
                :actions="[
                  {
                    label: L('查看'),
                    onClick: view.bind(null, record.fullId,'view','FullAcmToView'),
                  },
                  {
                    label: L('复制'),
                    onClick: view.bind(null, record.fullId,'copy','FullAcmToCopy'),
                  },
                  {
                    label: L('编辑'),
                    ifShow: record.state == 1,
                    onClick: view.bind(null, record.fullId,'edit','FullAcmToEdit'),
                  },
                  {
                    label: L('发布'),
                    ifShow: record.state == 1,
                    popConfirm: {
                      title: L('发布后不可撤销，是否确定发布？'),
                      placement: 'left',
                      confirm: operate.bind(null, { fullId: record.fullId }, 'publish'),
                    },
                  },
                  {
                    label: L('失效'),
                    ifShow: record.state == 2 || record.state == 3,
                    popConfirm: {
                      title: L('失效后不可恢复，是否确定失效？'),
                      placement: 'left',
                      confirm: operate.bind(null, { fullId: record.fullId }, 'invalid'),
                    },
                  },
                  {
                    label: L('删除'),
                    ifShow: record.state == 1 || record.state == 4 || record.state == 5,
                    popConfirm: {
                      title: L('删除后不可恢复，是否确定删除？'),
                      placement: 'left',
                      confirm: operate.bind(null, { fullId: record.fullId }, 'del'),
                    },
                  },
                ]"
              />
            </template>
          </template>
        </BasicTable>
        <Modal
          wrapClassName="full_modal"
          :visible="visible"
          :title="L('查看详情')"
          :zIndex="999"
          :maskClosable="false"
          :width="1000"
          :footer="null"
          :destroyOnClose="true"
          @cancel="sldHandleCancle"
        >
        </Modal>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'PromotionFullAcm',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, reactive, onMounted } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { useRouter } from 'vue-router';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { Modal } from 'ant-design-vue';
  import { sucTip, failTip } from '@/utils/utils';
  import { validatorEmoji } from '/@/utils/validate';
  import { getFullAcmListApi, getFullAcmInvalidApi, getFullAcmDelApi,getFullAcmPublishApi } from '/@/api/promotion/full';
  import { useUserStore } from '/@/store/modules/user';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const visible = ref(false);

  const fullId = ref(0);

  const userStore = useUserStore();

  const router = useRouter();

  const columns = reactive({
    data: [
      {
        title: L('活动名称'),
        dataIndex: 'fullName',
        align: 'center',
        width: 150,
      },
      {
        title: L('活动时间'),
        dataIndex: 'startTime',
        align: 'center',
        width: 150,
      },
      {
        title: L('活动状态'),
        dataIndex: 'stateValue',
        align: 'center',
        width: 80,
      },
    ],
  });

  const searchFormSchema = reactive({
    data: [
      {
        component: 'Input',
        label: L('活动名称'),
        field: 'fullName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: L('请输入活动名称'),
        },
        rules: [
          {
            // @ts-ignore
            validator: async (rule, value) => {
              await validatorEmoji(rule, value);
            },
            trigger: 'change',
          },
        ],
      },
      {
        colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
        component: 'RangePicker',
        label: L('活动时间'),
        field: '[startTime,endTime]',
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: [L('开始时间'), L('结束时间')],
        },
      },
      {
        component: 'Select',
        label: L('活动状态'),
        field: 'state',
        labelWidth: 90,
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: L('请选择活动状态'),
          options: [
            { value: '', label: L('全部') },
            { value: '1', label: L('待发布') },
            { value: '2', label: L('未开始') },
            { value: '3', label: L('进行中') },
            { value: '5', label: L('已失效') },
            { value: '4', label: L('已结束') },
          ],
        },
      },
    ],
  });

  // 表格数据
  const [registerTable, { reload, getForm }] = useTable({
    // 请求接口
    api: getFullAcmListApi,
    // 表头
    columns: columns.data,
    immediate: true,
    // 点击搜索前处理的参数
    beforeFetch(values) {
      values.startTime = values.startTime
        ? values.startTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
      return values;
    },
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: searchFormSchema.data,
    },
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    // 是否显示序号列
    showIndexColumn: true,
    // 表格右侧操作列配置
    actionColumn: {
      width: 150,
      title: L('操作'),
      dataIndex: 'action',
      // slots: { customRender: 'action' },
    },
  });

  const sldHandleCancle = () => {
    visible.value = false;
  };

  // 查看
  const view = (id,type,pathName) => {
    userStore.setDelKeepAlive([pathName])
    fullId.value = id;
    router.push({
      path: `/marketing/full_acm_to_${type}`,
      query: { id: id, tar: type },
    });
  };

  // 添加
  const add_full = () => {
    userStore.setDelKeepAlive(['FullAcmToAdd'])
    router.push({
      path: `/marketing/full_acm_to_add`,
    });
  };

  //操作  type: invalid 失效
  const operate = async (id, type) => {
    let param_data = {};
    let res = null;
    if (type == 'invalid') {
      param_data = id;
      res = await getFullAcmInvalidApi(param_data);
    }
    if (type == 'del') {
      param_data = id;
      res = await getFullAcmDelApi(param_data);
    }
    if (type == 'publish') {
      param_data = id;
      res = await getFullAcmPublishApi(param_data);
    }
    if (res.state == 200) {
      sucTip(res.msg);
      reload();
    } else {
      failTip(res.msg);
    }
  };

  onMounted(() => {});
</script>
<style lang="less" scoped>
  p {
    margin-bottom: 0;
  }

  .full_acm_list {
    .voucher_num {
      color: @primary-color;
      cursor: pointer;
    }
  }
</style>
<style lang="less">
  .full_modal {
    .ant-modal-header {
      padding: 12px 24px !important;
      background-color: @primary-color;
    }

    .ant-modal-title {
      color: #fff;
      font-size: 14px !important;
      line-height: 18px !important;
    }

    .ant-modal-close-x .anticon svg {
      fill: #fff;
    }

    .ant-modal .ant-modal-content {
      border-radius: 4px;
    }

    .ant-form-item-with-help {
      margin-bottom: 24px;
    }

    .ant-form-item-has-error .ant-input-number,
    .ant-form-item-has-error .ant-picker {
      border-color: #ed6f6f !important;
    }

    .ant-modal-close {
      top: 12px;
      right: 20px;
      height: auto !important;

      .ant-modal-close-x {
        display: flex !important;
        align-items: center;
        justify-content: center;
        width: auto !important;
        height: auto !important;
        line-height: normal;
      }
    }
  }
</style>
