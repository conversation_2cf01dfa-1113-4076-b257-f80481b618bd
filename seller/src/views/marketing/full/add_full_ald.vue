<template>
  <div class="section_padding add_full_a add_full_ald">
    <div class="section_padding_back full_activity">
      <SldComHeader :title="L('满N元折扣活动')" />
      <div class="com_line" style="height: 1px"></div>
      <Spin :spinning="loading">
        <div class="add_coupon_height">
          <Form layout="inline" ref="formRef" :model="detail">
            <CommonTitle :text="L('活动基本信息')" style="margin-top: 10px" />
            <div class="full_acm_activity">
              <!-- 活动名称 start -->
              <div class="item flex_row_start_start">
                <div class="left"> <span style="color: red">*</span>{{ L('活动名称') }} </div>
                <div class="right">
                  <Form.Item
                    :validateFirst="true"
                    :extra="L('最多输入20个字')"
                    name="fullName"
                    style="width: 400px"
                    :rules="[
                      {
                        required: true,
                        whitespace: true,
                        message: L('请输入活动名称'),
                      },
                      {
                        validator: async (rule, value) => {
                          await validatorEmoji(rule, value);
                        },
                      },
                    ]"
                  >
                    <Input
                      :maxLength="20"
                      :disabled="viewFlag"
                      style="width: 400px !important"
                      :placeholder="L('请输入活动名称')"
                      v-model:value="detail.fullName"
                    />
                  </Form.Item>
                </div>
              </div>
              <!-- 活动名称 end -->
              <!-- 活动时间 start -->
              <div class="item flex_row_start_start">
                <div class="left"> <span style="color: red">*</span>{{ L('活动时间') }}</div>
                <div class="right">
                  <Form.Item
                    name="activityTime"
                    :extra="L('活动时间不可与其他活动重叠')"
                    style="width: 400px"
                    :rules="[
                      {
                        required: true,
                        message: L('请选择活动时间'),
                      },
                    ]"
                  >
                    <RangePicker
                      :disabled="viewFlag"
                      style="width: 400px"
                      :format="'YYYY-MM-DD HH:mm:00'"
                      v-model:value="detail.activityTime"
                      :disabledDate="
                        (currentDate) => currentDate && currentDate < moment().subtract(1, 'days')
                      "
                      :showTime="{ format: 'HH:mm' }"
                      :placeholder="[L('开始时间'), L('结束时间')]"
                      :getPopupContainer="
                        (triggerNode) => {
                          return triggerNode.parentNode;
                        }
                      "
                    />
                  </Form.Item>
                </div>
              </div>
              <!-- 活动时间 end -->
              <div class="" v-for="(item, index) in detail.ladder_promotion" :key="index">
                <CommonTitle
                  :text="num_to_num()[index + 1] + L('级优惠')"
                  style="margin-top: 10px"
                  :closeFlag="index > 0"
                  @close-click="delPromotion(item.key)"
                />
                <!-- 优惠门槛 start -->
                <div class="item flex_row_start_start">
                  <div class="left"> <span style="color: red">*</span>{{ L('优惠门槛') }} </div>
                  <div class="right">
                    <Form.Item
                      :extra="L('以元为单位，设置使用该活动的最低金额')"
                      :name="['ladder_promotion', index, 'fullValue']"
                      style="width: 400px"
                      :rules="[
                        {
                          required: true,
                          message: L('请输入优惠门槛'),
                        },
                      ]"
                    >
                      <InputNumber
                        :max="9999999"
                        :min="1"
                        :disabled="viewFlag"
                        style="width: 400px !important"
                        :precision="2"
                        :placeholder="L('请输入优惠门槛')"
                        v-model:value="item.fullValue"
                      />
                    </Form.Item>
                  </div>
                </div>
                <!-- 优惠门槛 end -->
                <!-- 优惠内容 start -->
                <div class="item flex_row_start_start">
                  <div class="left"> <span style="color: red">*</span>{{ L('优惠内容') }}</div>
                  <div class="right">
                    <Form.Item
                      :extra="L('优惠折扣，满足优惠门槛后可以享受该优惠折扣，例如：输入90代表9折')"
                      :name="['ladder_promotion', index, 'minusValue']"
                      class="publishValue_box"
                      style="width: 300px"
                    >
                      <div class="flex_row_start_center">
                        <span
                          style="display: inline-block; margin-right: 5px; color: rgb(0 0 0 / 65%)"
                          >{{ L('打') }}</span
                        >
                        <InputNumber
                          :max="100"
                          :min="1"
                          :disabled="viewFlag"
                          style="width: 140px !important"
                          :precision="0"
                          :placeholder="L('请输入优惠内容')"
                          v-model:value="item.minusValue"
                        />
                        <span
                          style="display: inline-block; margin-left: 5px; color: rgb(0 0 0 / 65%)"
                          >{{ L('折') }}</span
                        >
                      </div>
                    </Form.Item>
                  </div>
                </div>
                <!-- 优惠内容 end -->
                <!-- 送优惠券 start -->
                <div class="item flex_row_start_start">
                  <div class="left"> </div>
                  <div class="right check_input">
                    <Form.Item>
                      <div class="flex_row_start_center">
                        <Checkbox
                          :checked="item.sendCouponIds"
                          :disabled="viewFlag"
                          @change="checkClick('add', null, item)"
                        >
                          {{ L('送优惠券') }}
                        </Checkbox>
                      </div>
                      <div class="sel_goods flex_column_start_start" v-if="item.sendCouponIds">
                        <Button
                          type="primary"
                          @click="handleVoucher('add', null, item)"
                          v-if="item.sendCouponIds && !viewFlag"
                          style="margin-top: 5px"
                          >选择优惠券</Button
                        >
                        <div
                          class=""
                          v-if="
                            item.sel_voucher.selectedRows != undefined &&
                            item.sel_voucher.selectedRows.length > 0
                          "
                          style="margin-top: 10px"
                        >
                          <BasicTable
                            style="width: 800px"
                            rowKey="couponId"
                            :columns="columns_sel_voucher"
                            :dataSource="item.sel_voucher.selectedRows"
                            :pagination="false"
                            :bordered="true"
                            :ellipsis="false"
                            :actionColumn="
                              viewFlag
                                ? null
                                : {
                                    title: L('操作'),
                                    width: 80,
                                    dataIndex: 'action',
                                  }
                            "
                          >
                            <template #bodyCell="{ column, text, record }">
                              <template v-if="column.dataIndex === 'action'">
                                <div
                                  @click="delvoucher(record.couponId, item)"
                                  class="coupon_goods_operate flex_row_center_center"
                                >
                                  <AliSvgIcon
                                    iconName="iconshanchu5"
                                    width="18px"
                                    height="18px"
                                    :fillColor="'#2d2d2d'"
                                  />
                                </div>
                              </template>
                            </template>
                          </BasicTable>
                        </div>
                      </div>
                    </Form.Item>
                  </div>
                </div>
                <!-- 送优惠券 end -->
                <!-- 送赠品 start -->
                <div class="item flex_row_start_start">
                  <div class="left"> </div>
                  <div class="right check_input">
                    <Form.Item>
                      <div class="flex_row_start_center">
                        <Checkbox
                          :checked="item.sendGoodsIds"
                          :disabled="viewFlag"
                          @change="checkClick('add', 'goods', item)"
                        >
                          {{ L('送赠品') }}
                        </Checkbox>
                      </div>
                      <div class="sel_goods flex_column_start_start" v-if="item.sendGoodsIds">
                        <Button
                          type="primary"
                          @click="handleVoucher('add', 'goods', item)"
                          v-if="item.sendGoodsIds && !viewFlag"
                          style="margin-top: 5px"
                          >选择赠品</Button
                        >
                        <div
                          class=""
                          v-if="
                            item.sel_goods.selectedRows != undefined &&
                            item.sel_goods.selectedRows.length > 0
                          "
                          style="margin-top: 10px"
                        >
                          <BasicTable
                            style="width: 800px"
                            rowKey="goodsId"
                            :columns="columns_spu"
                            :dataSource="item.sel_goods.selectedRows"
                            :pagination="false"
                            :bordered="true"
                            :ellipsis="false"
                            :actionColumn="
                              viewFlag
                                ? null
                                : {
                                    title: L('操作'),
                                    width: 80,
                                    dataIndex: 'action',
                                  }
                            "
                          >
                            <template #bodyCell="{ column, text, record }">
                              <template v-if="column.dataIndex == 'mainImage'">
                                <Popover placement="rightTop">
                                  <template #content>
                                    <div style="max-width: 200px; text-align: center">
                                      <img
                                        :src="text"
                                        alt=""
                                        style="max-width: 100%; max-height: 100%"
                                      />
                                    </div>
                                  </template>
                                  <div class="business_load_img">
                                    <img :src="text" alt="" />
                                  </div>
                                </Popover>
                              </template>
                              <template v-if="column.dataIndex == 'goodsPrice'">
                                {{
                                  text == -1
                                    ? '在线询价'
                                    : text
                                    ? '￥' + Number(text).toFixed(2)
                                    : '--'
                                }}
                              </template>
                              <template v-if="column.dataIndex == 'goodsStock'">
                                {{ text ? text : record.productStock ? record.productStock : '--' }}
                              </template>
                              <template v-if="column.dataIndex === 'action'">
                                <div
                                  @click="delGoods(record.goodsId, item)"
                                  class="coupon_goods_operate flex_row_center_center"
                                >
                                  <AliSvgIcon
                                    iconName="iconshanchu5"
                                    width="18px"
                                    height="18px"
                                    :fillColor="'#2d2d2d'"
                                  />
                                </div>
                              </template>
                            </template>
                          </BasicTable>
                        </div>
                      </div>
                    </Form.Item>
                  </div>
                </div>
                <!-- 送赠品 end -->
              </div>
              <CommonTitle
                v-if="detail.ladder_promotion.length < 5 && !viewFlag"
                style="margin-top: 10px"
                :describe="
                  L(
                    '提醒：每级优惠不叠加，如：满足二级优惠条件后则不再享有一级优惠，最多支持5级优惠~',
                  )
                "
                describeColor="#FF7F40"
              >
                <template #button>
                  <div class="toolbar_btn" @click="addPromotion">
                    <AliSvgIcon
                      iconName="iconxinzeng"
                      width="15px"
                      height="15px"
                      :fillColor="'rgb(255, 89, 8)'"
                    />
                    <span style="margin-left: 4px">{{ L('添加下级优惠') }}</span>
                  </div>
                </template>
              </CommonTitle>
            </div>
          </Form>
          <div style="width: 100%; height: 60px; background: #fff"></div>
        </div>
        <div
          class="m_diy_bottom_wrap"
          :style="{ position: 'fixed', left: getRealWidth + 10 + 'px' }"
        >
          <div
            class="add_goods_bottom_btn"
            :class="{ add_goods_bottom_btn_rig: viewFlag }"
            @click="goBack"
          >
            {{ L('返回') }}
          </div>
          <div
            class="add_goods_bottom_btn add_goods_bottom_btn_sel"
            v-if="!viewFlag"
            @click="handleSaveAllData"
          >
            {{ L('保存') }}
          </div>
        </div>
      </Spin>
    </div>
    <SldSelGoodsSingleDiy
      :modalTitle="modalTitle"
      @confirm-event="handleConfirm"
      @cancle-event="handleCancle"
      :link_type="link_type"
      :selectedRows="selectedRows"
      :selectedRowKeys="selectedRowKeys"
      :checkedType="checkedType"
      :modalVisible="visibleModal"
    >
    </SldSelGoodsSingleDiy>
  </div>
</template>
<script setup>
import { getCurrentInstance, ref, reactive, onMounted } from 'vue';
import SldComHeader from '/@/components/SldComHeader/index.vue';
import CommonTitle from '/@/components/CommonTitle/index.vue';
import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
import SldSelGoodsSingleDiy from '/@/components/SldSelGoodsSingleDiy/index.vue';
import { BasicTable } from '/@/components/Table';
import { num_to_num } from '/@/utils/utils';
import { validatorEmoji } from '/@/utils/validate';
import { getFullAldUpdateApi, getFullAldAddApi, getFullAldDetailApi } from '/@/api/promotion/full';
import moment from 'moment';
import dayjs from 'dayjs';
import { useRoute, useRouter } from 'vue-router';
import { useUserStore } from '/@/store/modules/user';
import { sucTip, failTip } from '@/utils/utils';
import { pageClose } from '/@/utils/utils';
import { useMultipleTabStore } from '/@/store/modules/multipleTab';
import {
  Checkbox,
  Popover,
  Spin,
  Form,
  Input,
  InputNumber,
  RadioGroup,
  Radio,
  Tree,
  RangePicker,
  Button,
} from 'ant-design-vue';

const vm = getCurrentInstance();
const userStore = useUserStore();
const L = vm?.appContext.config.globalProperties.$sldComLanguage;

const key = ref(1);
const tabStore = useMultipleTabStore();
const ladder_item = ref({
  key: 1,
  fullValue: '', //优惠门槛
  minusValue: '', //优惠金额
  sendCouponIds: false, //是否赠送优惠券
  sel_voucher: {
    selectedRows: [],
    selectedRowKeys: [],
  }, //选择的优惠券信息
  sendGoodsIds: false, //是否送赠品
  sel_goods: {
    selectedRows: [],
    selectedRowKeys: [],
  }, //选择的赠品信息
}); //阶梯优惠数组

const columns_sel_voucher = ref([
  {
    title: L('优惠券名称'),
    dataIndex: 'couponName',
    align: 'center',
    width: 150,
  },
  {
    title: L('优惠券类型'),
    dataIndex: 'couponTypeValue',
    align: 'center',
    width: 100,
  },
  {
    title: L('优惠券内容'),
    dataIndex: 'couponContent',
    align: 'center',
    width: 100,
  },
  {
    title: L('剩余可用'),
    dataIndex: 'remainNum',
    align: 'center',
    width: 100,
  },
]);

const columns_spu = ref([
  {
    title: L('商品图片'),
    dataIndex: 'mainImage',
    align: 'center',
    width: 100,
  },
  {
    title: L('商品名称'),
    dataIndex: 'goodsName',
    align: 'center',
    width: 150,
  },
  {
    title: L('商品价格'),
    dataIndex: 'goodsPrice',
    align: 'center',
    width: 100,
  },
  {
    title: L('商品库存'),
    dataIndex: 'goodsStock',
    align: 'center',
    width: 100,
  },
]);

const checkedType = ref(false);
const route = useRoute();
const router = useRouter();
const itemKey = ref(0);
const sel_goods = ref({}); //选择的赠品信息
const { getRealWidth } = useMenuSetting();
const sel_voucher = ref({}); //选择的优惠券信息
const link_type = ref('voucher');
const loading = ref(false);
const query = ref(route.query);
const selectedRows = ref([]);
const selectedRowKeys = ref([]);
const visibleModal = ref(false);
const formRef = ref();
const modalTitle = ref('');
const detail = ref({
  ladder_promotion: [
    {
      key: 1,
      fullValue: '', //优惠门槛
      minusValue: '', //优惠金额
      sendCouponIds: false, //是否赠送优惠券
      sel_voucher: {
        selectedRows: [],
        selectedRowKeys: [],
      }, //选择的优惠券信息
      sendGoodsIds: false, //是否送赠品
      sel_goods: {
        selectedRows: [],
        selectedRowKeys: [],
      }, //选择的赠品信息
    },
  ],
}); //活动详情数据
const viewFlag = ref(route.query.tar != undefined && route.query.tar == 'view' ? true : false);

const checkClick = (type, link, val) => {
  let tmp_data = detail.value.ladder_promotion.filter((item) => item.key == val.key)[0];
  if (link == 'goods') {
    tmp_data.sendGoodsIds = !tmp_data.sendGoodsIds;
    if (!tmp_data.sendGoodsIds) {
      tmp_data.sel_goods.selectedRows = [];
      tmp_data.sel_goods.selectedRowKeys = [];
    }
  } else {
    tmp_data.sendCouponIds = !tmp_data.sendCouponIds;
    if (!tmp_data.sendCouponIds) {
      tmp_data.sel_voucher.selectedRows = [];
      tmp_data.sel_voucher.selectedRowKeys = [];
    }
  }
};

// 优惠券弹框打开
const handleVoucher = (type, link, val) => {
  itemKey.value = val.key;
  checkedType.value = false;
  let tmp_data = detail.value.ladder_promotion.filter((item) => item.key == val.key)[0];
  if (link == 'goods') {
    checkedType.value = true;
    modalTitle.value = L('选择产品');
    link_type.value = 'goods';
    selectedRows.value = JSON.parse(JSON.stringify(tmp_data.sel_goods.selectedRows));
    selectedRowKeys.value = JSON.parse(JSON.stringify(tmp_data.sel_goods.selectedRowKeys));
    visibleModal.value = true;
  } else {
    checkedType.value = true;
    link_type.value = 'voucher';
    modalTitle.value = L('选择优惠券');
    selectedRows.value = JSON.parse(JSON.stringify(tmp_data.sel_voucher.selectedRows));
    selectedRowKeys.value = JSON.parse(JSON.stringify(tmp_data.sel_voucher.selectedRowKeys));
    visibleModal.value = true;
  }
};

// 优惠券弹框确定
const handleConfirm = (record, ids) => {
  let tmp_data = detail.value.ladder_promotion.filter((item) => item.key == itemKey.value)[0];
  if (link_type.value == 'voucher') {
    if (record.length > 10) {
      failTip('最多可以选择10张优惠券');
      return;
    }
    tmp_data.sel_voucher.selectedRows = record;
    tmp_data.sel_voucher.selectedRowKeys = ids;
  } else {
    if (record.length > 10) {
      failTip('最多可以选择10个赠品');
      return;
    }
    tmp_data.sel_goods.selectedRows = record;
    tmp_data.sel_goods.selectedRowKeys = ids;
  }
  visibleModal.value = false;
  itemKey.value = -1;
};

// 优惠券弹框取消
const handleCancle = () => {
  visibleModal.value = false;
  itemKey.value = -1;
};

//优惠券删除事件
const delvoucher = (couponId, val) => {
  let tmp_data = detail.value.ladder_promotion.filter((item) => item.key == val.key)[0];
  let selectedRows = JSON.parse(JSON.stringify(tmp_data.sel_voucher.selectedRows));
  let selectedRowKeys = JSON.parse(JSON.stringify(tmp_data.sel_voucher.selectedRowKeys));
  selectedRows = selectedRows.filter((item) => item.couponId != couponId);
  selectedRowKeys = selectedRowKeys.filter((item) => item != couponId);
  tmp_data.sel_voucher.selectedRows = JSON.parse(JSON.stringify(selectedRows));
  tmp_data.sel_voucher.selectedRowKeys = JSON.parse(JSON.stringify(selectedRowKeys));
};

//赠品删除事件
const delGoods = (goodsId, val) => {
  let tmp_data = detail.value.ladder_promotion.filter((item) => item.key == val.key)[0];
  let selectedRows = JSON.parse(JSON.stringify(tmp_data.sel_goods.selectedRows));
  let selectedRowKeys = JSON.parse(JSON.stringify(tmp_data.sel_goods.selectedRowKeys));
  selectedRows = selectedRows.filter((item) => item.goodsId != goodsId);
  selectedRowKeys = selectedRowKeys.filter((item) => item != goodsId);
  tmp_data.sel_goods.selectedRows = JSON.parse(JSON.stringify(selectedRows));
  tmp_data.sel_goods.selectedRowKeys = JSON.parse(JSON.stringify(selectedRowKeys));
};

const goBack = () => {
  if (window.history && window.history.length == 1) {
    pageClose();
  } else {
    const { fullPath } = route;
    tabStore.closeTabByKey(fullPath, router);
    router.back();
  }
};

const delPromotion = (key) => {
  detail.value.ladder_promotion = detail.value.ladder_promotion.filter((item) => item.key != key);
};

const addPromotion = () => {
  detail.value.ladder_promotion.push({
    ...JSON.parse(JSON.stringify(ladder_item.value)),
    key: key.value + 1,
  });
  key.value += 1;
};

// 保存
const handleSaveAllData = () => {
  formRef.value.validate().then(async (values) => {
    let param = {};
    param.fullName = values.fullName;
    if (values.activityTime) {
      param.startTime = values.activityTime[0]
        ? values.activityTime[0].format('YYYY-MM-DD HH:mm:00')
        : '';
      param.endTime = values.activityTime[1]
        ? values.activityTime[1].format('YYYY-MM-DD HH:mm:00')
        : '';
    }
    //活动开始时间必须小于结束时间
    if (Date.parse(values.startTime) > Date.parse(values.endTime)) {
      failTip(L('活动开始时间必须小于结束时间'));
      return false;
    }

    let rule_data = [];

    for (let i = 0; i < detail.value.ladder_promotion.length; i++) {
      let item = detail.value.ladder_promotion[i];
      let minusValue = item.minusValue || 0;
      let sendGoodsIds =
        item.sel_goods.selectedRowKeys != undefined && item.sel_goods.selectedRowKeys.length > 0
          ? JSON.parse(JSON.stringify(item.sel_goods.selectedRowKeys)).join(',')
          : '';
      let sendCouponIds =
        item.sel_voucher.selectedRowKeys != undefined && item.sel_voucher.selectedRowKeys.length > 0
          ? JSON.parse(JSON.stringify(item.sel_voucher.selectedRowKeys)).join(',')
          : '';
      //校验优惠内容必须有其中一个，否则无法提交
      if (minusValue == undefined || !minusValue) {
        failTip(`${L('请设置')}${num_to_num()[i + 1]}${L('级优惠的优惠内容')}`);
        return false;
      }

      if (item.sendCouponIds && !sendCouponIds) {
        failTip(`${L('请选择')}${num_to_num()[i + 1]}${L('级优惠的优惠券')}`);
        return false;
      }
      if (item.sendGoodsIds && !sendGoodsIds) {
        failTip(`${L('请选择')}${num_to_num()[i + 1]}${L('级优惠的赠品')}`);
        return false;
      }
      rule_data.push({
        fullValue: item.fullValue,
        minusValue: minusValue,
        sendIntegral: 0,
        sendCouponIds: sendCouponIds,
        sendGoodsIds: sendGoodsIds,
      });
    }
    param.ruleJson = JSON.stringify(rule_data);
    loading.value = true;
    let res;
    if (route.query.id != undefined && Number(route.query.id) > 0 && route.query.tar == 'edit') {
      //编辑阶梯满减
      param.fullId = route.query.id;
      res = await getFullAldUpdateApi(param);
    } else {
      //新增阶梯满减
      res = await getFullAldAddApi(param);
    }
    if (res.state == 200) {
      sucTip(res.msg);
      userStore.setDelKeepAlive([route.name, 'promotionFullAld']);
      setTimeout(() => {
        goBack();
        loading.value = false;
      }, 500);
    } else {
      loading.value = false;
      failTip(res.msg);
    }
  });
};

// 查看详情
const get_full_asm_detail = async () => {
  try {
    loading.value = true;
    let res = await getFullAldDetailApi({ fullId: route.query.id });
    if (res.state == 200) {
      loading.value = false;
      if (res.data.startTime) {
        res.data.activityTime = [
          dayjs(res.data.startTime, 'YYYY-MM-DD HH:mm:ss'),
          dayjs(res.data.endTime, 'YYYY-MM-DD HH:mm:ss'),
        ];
      }
      res.data.ladder_promotion = [
        {
          key: 1,
          fullValue: '', //优惠门槛
          minusValue: '', //优惠金额
          sendCouponIds: false, //是否赠送优惠券
          sel_voucher: {
            selectedRows: [],
            selectedRowKeys: [],
          }, //选择的优惠券信息
          sendGoodsIds: false, //是否送赠品
          sel_goods: {
            selectedRows: [],
            selectedRowKeys: [],
          }, //选择的赠品信息
        },
      ];
      res.data.ruleList.map((item, index) => {
        if (index == 0) {
          res.data.ladder_promotion[0].fullValue = item.fullValue;
          res.data.ladder_promotion[0].minusValue = item.minusValue;
          //初始化选中的优惠券数据
          if (
            item.couponList != null &&
            item.couponList.length != undefined &&
            item.couponList.length > 0
          ) {
            res.data.ladder_promotion[0].sel_voucher.selectedRows = JSON.parse(
              JSON.stringify(item.couponList),
            );
            res.data.ladder_promotion[0].sel_voucher.selectedRowKeys = [];
            res.data.ladder_promotion[0].sel_voucher.selectedRows.forEach((it) => {
              it.remainNum = it.couponStock;
              res.data.ladder_promotion[0].sel_voucher.selectedRowKeys.push(it.couponId);
            });
            res.data.ladder_promotion[0].sendCouponIds = true;
          }
          //初始化选中的商品数据
          if (
            item.giftList != null &&
            item.giftList.length != undefined &&
            item.giftList.length > 0
          ) {
            res.data.ladder_promotion[0].sel_goods.selectedRows = JSON.parse(
              JSON.stringify(item.giftList),
            );
            res.data.ladder_promotion[0].sel_goods.selectedRowKeys = [];
            res.data.ladder_promotion[0].sel_goods.selectedRows.forEach((it) => {
              it.mainImage = it.goodsImage;
              res.data.ladder_promotion[0].sel_goods.selectedRowKeys.push(it.goodsId);
            });
            res.data.ladder_promotion[0].sendGoodsIds = true;
          }
        } else {
          let obj = {
            fullValue: item.fullValue,
            minusValue: item.minusValue,
            sendCouponIds:
              item.couponList != null &&
              item.couponList.length != undefined &&
              item.couponList.length > 0
                ? true
                : false,
            sendGoodsIds:
              item.giftList != null && item.giftList.length != undefined && item.giftList.length > 0
                ? true
                : false,
            key: index + 1,
            sel_voucher: {
              selectedRows: [],
              selectedRowKeys: [],
            }, //选择的优惠券信息
            sel_goods: {
              selectedRows: [],
              selectedRowKeys: [],
            }, //选择的赠品信息
          };
          //初始化选中的优惠券数据
          if (
            item.couponList != null &&
            item.couponList.length != undefined &&
            item.couponList.length > 0
          ) {
            obj.sel_voucher.selectedRows = JSON.parse(JSON.stringify(item.couponList));
            obj.sel_voucher.selectedRowKeys = [];
            obj.sel_voucher.selectedRows.forEach((it) => {
              it.remainNum = it.couponStock;
              obj.sel_voucher.selectedRowKeys.push(it.couponId);
            });
            obj.sendCouponIds = true;
          }
          //初始化选中的产品数据
          if (
            item.giftList != null &&
            item.giftList.length != undefined &&
            item.giftList.length > 0
          ) {
            obj.sel_goods.selectedRows = JSON.parse(JSON.stringify(item.giftList));
            obj.sel_goods.selectedRowKeys = [];
            obj.sel_goods.selectedRows.forEach((it) => {
              it.mainImage = it.goodsImage;
              obj.sel_goods.selectedRowKeys.push(it.goodsId);
            });
            obj.sendGoodsIds = true;
          }

          res.data.ladder_promotion.push(obj);
          key.value += 1;
        }
      });
      detail.value = res.data;
    }
  } catch (error) {
    console.info(error, 'error');
  }
};

onMounted(() => {
  if (route.query.id != undefined && Number(route.query.id) > 0) {
    get_full_asm_detail();
  }
});
</script>
<style lang="less">
@import './style/add_full.less';
.add_full_ald {
  .ant-table-body {
    height: auto !important;
    max-height: 400px !important;
  }
  .check_input {
    .ant-form-item-control-input {
      align-items: start;
    }
  }
}
.business_load_img {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 35px;
  height: 35px;
  margin: auto;

  img {
    max-width: 100%;
    max-height: 100%;
  }
}
</style>
