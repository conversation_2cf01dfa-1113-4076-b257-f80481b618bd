<template>
  <Spin :spinning="loading">
    <div class="section_padding promotion_center">
      <div class="section_padding_back promotion_center_back">
        <div class="section_padding_back_scroll_mark">
          <div class="center_title flex_row_start_center">{{ L('应用中心') }}</div>
          <template v-for="(item, index) in data_center.data" :key="index">
            <div class="center_item flex_column_start_start" v-if="item.show_num">
              <div class="center_item_title">{{ item.title }}</div>
              <div
                class="center_item_content flex_row_start_center"
                style="flex-wrap: wrap; width: 100%"
              >
                <template v-for="(child_item, child_index) in item.child" :key="child_index">
                  <div
                    class="child_item flex_row_start_start child_width"
                    :style="{ marginLeft: child_index % 4 > 0 ? '20px' : 0 }" @click="go(child_item)"
                  >
                    <img :src="child_item.icon" alt="" class="left_img" />
                    <div class="right_part flex_column_center_start">
                      <div class="right_part_top flex_row_start_start">
                        <span class="top_title">{{ child_item.title }}</span>
                        <span
                          v-if="child_item.extraFlag && specialFlag > -3"
                          class="top_flag flex_row_center_center"
                          >{{ L('加配') }}</span
                        >
                      </div>
                      <span class="desc" :title="child_item.desc" :style="{ width: '100%' }">{{
                        child_item.desc
                      }}</span>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </Spin>
</template>
<script>
  export default {
    name: 'PromotionCenter',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, reactive, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { specialFlag } from '/@/utils/utils';
  import { Spin } from 'ant-design-vue';
  import { usePermissionStore } from '/@/store/modules/permission';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const menu = ref(['/marketing', '/point', '/spreader']); //拥有的菜单字符串数组

  const emptyFlag = ref(false); //是否展示空提示

  const loading = ref(true)

  const permissionStore = usePermissionStore();

  const router = useRouter();

  const itemW = ref((document.body.clientWidth * 1 - 90 - 195) / 4);

  const descW = ref(itemW.value - 60 - 40 - 15);

  const all_data = reactive({
    data: [
      {
        name: 'spell_group',
        icon: new URL('/src/assets/images/center/spell_group.png', import.meta.url).href,
        title: L('拼团'),
        extraFlag: false,
        showFlag: false,
        desc: L('通过社交关系，降低拉新成本提提高获客效率。'),
        path: '/marketing/spell_group',
      },
      {
        name: 'ladder_group',
        icon: new URL('/src/assets/images/center/ladder_group.png', import.meta.url).href,
        title: L('阶梯团'),
        extraFlag: false,
        showFlag: false,
        desc: L('进阶版拼团，可设置阶梯拼团价格。'),
        path: '/marketing/ladder_group',
      },
      {
        name: 'spreader',
        icon: new URL('/src/assets/images/center/spreader.png', import.meta.url).href,
        title: L('推手'),
        extraFlag: true,
        desc: L('招募分销员，提高销售效率。'),
        path: '/spreader',
      },
      {
        name: 'sckill',
        icon: new URL('/src/assets/images/center/sckill.png', import.meta.url).href,
        title: L('秒杀'),
        extraFlag: false,
        showFlag: false,
        desc: L('快速抢购，引导顾客完成消费。'),
        path: '/marketing/seckill',
      },
      {
        name: 'store_coupon',
        icon: new URL('/src/assets/images/center/store_coupon.png', import.meta.url).href,
        title: L('店铺优惠券'),
        extraFlag: false,
        showFlag: false,
        desc: L('查看和管理平台内店铺的优惠券。'),
        path: '/marketing/coupon_list',
      },
      {
        name: 'svideo',
        icon: new URL('/src/assets/images/center/svideo.png', import.meta.url).href,
        title: L('短视频'),
        extraFlag: false,
        showFlag: false,
        desc: L('短视频带货，以直观的内容促进转化。'),
        path: '/marketing/video',
      },
      {
        name: 'live',
        icon: new URL('/src/assets/images/center/live.png', import.meta.url).href,
        title: L('直播'),
        extraFlag: true,
        showFlag: false,
        desc: L('会员与带货主播实时交流互动促进转化。'),
        path: '/marketing/video',
      },
      {
        name: 'presale',
        icon: new URL('/src/assets/images/center/presale.png', import.meta.url).href,
        title: L('预售'),
        extraFlag: false,
        showFlag: false,
        desc: L('热门商品提前预发。'),
        path: '/marketing/presale',
      },
      {
        name: 'full_acm',
        icon: new URL('/src/assets/images/center/full_acm.png', import.meta.url).href,
        title: L('满减'),
        extraFlag: false,
        showFlag: false,
        desc: L('满足条件的用户享受减价购买。'),
        path: '/marketing/full_acm',
      },
      {
        name: 'full_asm',
        icon: new URL('/src/assets/images/center/full_asm.png', import.meta.url).href,
        title: L('阶梯满减'),
        extraFlag: false,
        showFlag: false,
        desc: L('进阶版满减，满足条件的可享受多级优惠。'),
        path: '/marketing/full_asm',
      },
      {
        name: 'full_ald',
        icon: new URL('/src/assets/images/center/full_ald.png', import.meta.url).href,
        title: L('满N元折扣'),
        extraFlag: false,
        showFlag: false,
        desc: L('购买满N元享受折扣。'),
        path: '/marketing/full_ald',
      },
      {
        name: 'full_nld',
        icon: new URL('/src/assets/images/center/full_nld.png', import.meta.url).href,
        title: L('满N件折扣'),
        extraFlag: false,
        showFlag: false,
        desc: L('购买满N件享受折扣。'),
        path: '/marketing/full_nld',
      },
      {
        name: 'point',
        icon: new URL('/src/assets/images/center/point.png', import.meta.url).href,
        title: L('积分商城'),
        extraFlag: false,
        showFlag: false,
        desc: L('积分换购商品，促进用户购买。'),
        path: '/point',
      },
    ], //所有应用一维数据
  });

  const data_center = reactive({
    data: [
      {
        title: L('拉新获客'),
        child_path_array: ['/marketing_spreader', '/marketing_promotion'],
        child_name_array: ['spreader', 'spell_group', 'ladder_group'],
        show_num: 0, //展示应用的数量
        child: [
        ],
      },
      {
        title: L('促进转化'),
        child_path_array: ['/marketing_svideo', '/marketing_live', '/marketing_promotion'],
        child_name_array: ['svideo', 'live', 'sckill', 'system_coupon', 'store_coupon', 'rank'],
        show_num: 0, //展示应用的数量
        child: [],
      },
      {
        title: L('提高客单价'),
        child_path_array: ['recommend', 'marketing_promotion'],
        child_name_array: ['recommend', 'presale', 'full_acm', 'full_asm', 'full_ald', 'full_nld'],
        show_num: 0, //展示应用的数量
        child: [],
      },
      {
        title: L('留存复购'),
        child_path_array: ['marketing_point', 'marketing_promotion'],
        child_name_array: ['sign', 'point'],
        show_num: 0, //展示应用的数量
        child: [],
      },
    ], //应用中心数据
  });

  const initData = async() => {
    // 筛选
    let sld_menu_data = localStorage.getItem('sld_menu_data');
    if (sld_menu_data != undefined && sld_menu_data) {
      sld_menu_data = JSON.parse(sld_menu_data);//全部菜单缓存数据
    }
    let cur_menu_data = sld_menu_data.filter(item => menu.value.indexOf(item.frontPath) > -1);//当前页面拥有的菜单数据
    if (cur_menu_data.length > 0) {
      cur_menu_data.map(item => {
        if (item.frontPath == '/point' || item.frontPath == '/spreader') {
          let temp_data = all_data.data.filter(child => child.path == item.frontPath);
          if(temp_data.length>0){
            temp_data[0].showFlag = true;
            temp_data[0].path = item.children[0].frontPath;

          }
        } else if (item.frontPath == '/marketing') {
          let all_child_path = [];//该管理员所拥有的应用中心菜单名称数组
          item.children.map(child => {
            all_child_path.push(child.frontPath);
          });
          all_data.data.map(child => {
            if (all_child_path.indexOf(child.path) > -1) {
              child.showFlag = true;
            }
          });
        }
      });

      //将all_data 下面的数据都分别组装到data里面
      data_center.data.map(item => {
        all_data.data.map(child => {
          if (item.child_name_array.indexOf(child.name) > -1 && child.showFlag) {
            item.child.push({ ...child });
            item.show_num += 1;
          }
        });
      });

    } else {
      emptyFlag.value = true;
    }
    loading.value = false
  };

  const go = (val) => {
    router.push(val.path);
  };

  onMounted(() => {
    initData();
  });
</script>
<style lang="less">
  .promotion_center {
    padding: 10px;
    .promotion_center_back {
      padding: 20px;
      background: #fff;
      .section_padding_back_scroll_mark{
        &::-webkit-scrollbar{
          display: none;
        }
      }
    }
    .center_title {
      color: #404040;
      font-family: PingFangSC-Semibold, 'PingFang SC';
      font-size: 18px;
      font-weight: 600;
    }

    .center_item {
      .center_item_title {
        margin-top: 30px;
        color: #404040;
        font-size: 15px;
        font-weight: 600;
      }

      .center_item_content {
        .child_item {
          width: calc(25% - 15px);
          height: 100px;
          margin-top: 20px;
          padding-top: 20px;
          border-radius: 10px;
          background: #f9f9f9;
          cursor: pointer;

          .left_img {
            width: 60px;
            height: 60px;
            margin-left: 20px;
          }

          .right_part {
            width: calc(100% - 90px);
            margin-left: 15px;
            padding-right: 20px;

            .right_part_top {
              margin-top: 4px;

              .top_title {
                color: #404040;
                font-family: PingFangSC-Semibold, 'PingFang SC';
                font-size: 16px;
                font-weight: 600;
                line-height: 25px;
              }

              .top_flag {
                display: inline-block;
                height: 20px;
                margin-left: 10px;
                padding: 0 10px;
                border-radius: 10px 10px 10px 0;
                background: linear-gradient(0deg, #fcc102 0%, #ffa710 100%);
                color: #fff;
                font-family: PingFangSC-Semibold, 'PingFang SC';
                font-size: 12px;
                font-weight: 600;
                line-height: 20px;
              }
            }

            .desc {
              margin-top: 5px;
              overflow: hidden;
              color: rgb(64 64 64 / 74%);
              font-family: PingFangSC-Regular, 'PingFang SC';
              font-size: 14px;
              font-weight: 400;
              line-height: 20px;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
      }
    }
  }
</style>
