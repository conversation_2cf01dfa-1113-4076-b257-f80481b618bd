<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader :title="L('优惠券管理')" />
      <div class="coupon_home_system_lists">
        <BasicTable @register="registerTable" rowKey="couponId">
          <template #tableTitle>
            <div class="toolbar">
              <div class="toolbar_btn" :class="{'toolbar_btn_opacity':enableFlag==0}" @click="()=>enableFlag==0?null:add_coupon('', null,'MarketingCouponListToAdd')">
                <AliSvgIcon iconName="iconfabu1" width="15px" height="15px" :fillColor="'#fa6f1e'" />
                <span style="margin-left: 4px">{{ L('新建优惠券') }}</span>
              </div>
            </div>
          </template>
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.dataIndex == 'publishNum'">
              <router-link :to="`/marketing/coupon_list_to_receive_list?id=${record.couponId}`">
                <div class="voucher_num"
                  >{{ record.receivedNum }}/{{ record.usedNum }}/{{ record.publishNum }}</div
                >
              </router-link>
            </template>
            <template v-if="column.dataIndex == 'publishStartTime'">
              <div v-if="record.publishType != 3">
                <p>{{ text }}</p>
                <p>~</p>
                <p>{{ record.publishEndTime }}</p>
              </div>
              <div v-else> -- </div>
            </template>
            <template v-if="column.dataIndex == 'effectiveStart'">
              <div v-if="record.cycle">
                {{ `${L('领取后')}${record.cycle}${L('天内')}` }}
              </div>
              <div v-else>
                <p>{{ text }}</p>
                <p>~</p>
                <p>{{ record.effectiveEnd }}</p>
              </div>
            </template>
            <template v-if="column.key === 'action'">
              <TableAction
                :actions="[
                  {
                    label: L('查看'),
                    onClick: view.bind(null, record.couponId),
                  },
                  {
                    label: L('复制'),
                    onClick: add_coupon.bind(null, 'copy', record.couponId,'MarketingCouponListToCopy'),
                  },
                  {
                    label: L('编辑'),
                    ifShow: record.state == 1,
                    onClick: add_coupon.bind(null, 'edit', record.couponId,'MarketingCouponListToEdit'),
                  },
                  {
                    label: L('失效'),
                    ifShow: record.state == 3,
                    popConfirm: {
                      title: L('失效后不可恢复，是否确定失效？'),
                      placement: 'left',
                      confirm: operate.bind(null, { couponId: record.couponId }, 'invalid'),
                    },
                  },
                  {
                    label: L('删除'),
                    ifShow: record.state == 1 || record.state == 2 || record.state == 4,
                    popConfirm: {
                      title: L('删除后不可恢复，是否确定删除？'),
                      placement: 'left',
                      confirm: operate.bind(null, { couponId: record.couponId }, 'del'),
                    },
                  },
                ]"
              />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'MarketingCouponList',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, reactive, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { Switch } from 'ant-design-vue';
  import { sucTip, failTip } from '@/utils/utils';
  import { useUserStore } from '/@/store/modules/user';
  import { getSettingListApi } from '/@/api/common/common';
  import {
    getCouponListApi,
    getCouponInvalidApi,
    getCouponDelApi,
  } from '/@/api/promotion/coupon';

  const vm = getCurrentInstance();
  const userStore = useUserStore();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const enableFlag = ref(0); //优惠券开关

  const str_info = ref('coupon_is_enable')

  const router = useRouter();

  const columns = reactive({
    data: [
      {
        title: L('优惠券名称'),
        dataIndex: 'couponName',
        align: 'center',
        width: 100,
      },
      {
        title: L('优惠券类型'),
        dataIndex: 'couponTypeValue',
        align: 'center',
        width: 100,
      },
      {
        title: L('优惠内容'),
        dataIndex: 'couponContent',
        align: 'center',
        width: 100,
      },
      {
        title: L('已领取/已使用/发布数'),
        dataIndex: 'publishNum',
        align: 'center',
        width: 150,
      },
      {
        title: L('活动时间'),
        dataIndex: 'publishStartTime',
        align: 'center',
        width: 150,
      },
      {
        title: L('使用时间'),
        dataIndex: 'effectiveStart',
        align: 'center',
        width: 150,
      },
      {
        title: L('获取方式'),
        dataIndex: 'publishTypeValue',
        align: 'center',
        width: 100,
      },
      {
        title: L('活动状态'),
        dataIndex: 'stateValue',
        align: 'center',
        width: 100,
      },
    ],
  });
  const searchFormSchema = reactive({
    data: [
      {
        component: 'Input',
        label: L('优惠券名称'),
        field: 'couponName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: L('请输入优惠券名称'),
        },
      },
      {
        component: 'Select',
        label: L('活动状态'),
        field: 'state',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: L('请选择活动状态'),
          options: [
            { value: '', label: L('全部') },
            { value: '1', label: L('未开始') },
            { value: '2', label: L('进行中') },
            { value: '3', label: L('已失效') },
            { value: '4', label: L('已结束') },
          ],
        },
      },
      {
        component: 'Select',
        label: L('优惠券类型'),
        field: 'couponType',
        labelWidth: 90,
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: L('请选择优惠券类型'),
          options: [
            { value: '', label: L('全部') },
            { value: '1', label: L('满减券') },
            { value: '2', label: L('折扣券') },
            { value: '3', label: L('随机金额券') },
          ],
        },
      },
      
      {
        colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
        component: 'RangePicker',
        label: L('活动时间'),
        field: '[publishStartTime,publishEndTime]',
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: [L('开始时间'), L('结束时间')],
        },
      },
      {
        component: 'Select',
        label: L('获取方式'),
        field: 'publishType',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: L('请选择获取方式'),
          options: [
            { value: '', label: L('全部') },
            { value: '1', label: L('免费领取') },
            { value: '3', label: L('活动赠送') },
          ],
        },
      },
      {
        colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
        component: 'RangePicker',
        label: L('使用时间'),
        field: '[effectiveStart,effectiveEnd]',
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: [L('开始时间'), L('结束时间')],
        },
      },
    ],
  });

  // 表格数据
  const [registerTable, { reload }] = useTable({
    // 请求接口
    api: getCouponListApi,
    // 表头
    columns: columns.data,
    immediate: true,
    // 点击搜索前处理的参数
    beforeFetch(values) {
      values.publishStartTime = values.publishStartTime
        ? values.publishStartTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.publishEndTime = values.publishEndTime
        ? values.publishEndTime.split(' ')[0] + ' 23:59:59'
        : undefined;
      values.effectiveStart = values.effectiveStart
        ? values.effectiveStart.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.effectiveEnd = values.effectiveEnd
        ? values.effectiveEnd.split(' ')[0] + ' 23:59:59'
        : undefined;
      return values;
    },
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: searchFormSchema.data,
    },
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    // 是否显示序号列
    showIndexColumn: true,
    // 表格右侧操作列配置
    actionColumn: {
      width: 200,
      title: L('操作'),
      dataIndex: 'action',
      // slots: { customRender: 'action' },
    },
  });

  //优惠券操作  type: invalid 失效 copy 复制  del 删除
  const operate = async (id, type) => {
    let param_data = {};
    let res = null;
    if (type == 'invalid') {
      param_data = id;
      res = await getCouponInvalidApi(param_data);
    }
    if (type == 'del') {
      param_data = id;
      res = await getCouponDelApi(param_data);
    }
    if (res.state == 200) {
      sucTip(res.msg);
      reload();
    } else {
      failTip(res.msg);
    }
  };

  const view = (id) => {
    router.push({
      path: `/marketing/coupon_list_to_view`,
      query: { id: id, type: 'system' },
    });
  };

  const add_coupon = (type, id,pathName) => {
    userStore.setDelKeepAlive([pathName])
    if (type) {
      router.push({
        path: `/marketing/coupon_list_to_${type}`,
        query: {
          type: type,
          id: id,
        },
      });
    } else {
      router.push({
        path: '/marketing/coupon_list_to_add',
      });
    }
  };

   //获取站点基本配置
   const getSetting = async () => {
    const res = await getSettingListApi({
      str: str_info.value,
    });
    if (res.state == 200 && res.data) {
      enableFlag.value = res.data[0].value;
    } else {
      failTip(res.msg);
    }
  };


  onMounted(() => {
    // dev_supplier-start
    if(userStore.getStoreInfo.shopType&&userStore.getStoreInfo.shopType=='3'){
      str_info.value = 'supplier_coupon_is_enable'
    }else{
      str_info.value = 'coupon_is_enable'
    }
    // dev_supplier-end
    getSetting()
  });
</script>
<style lang="less" scoped>
  @import './style/system_lists.less';

  p {
    margin-bottom: 0;
  }
  // .coupon_home_system_lists {
  // }
</style>
