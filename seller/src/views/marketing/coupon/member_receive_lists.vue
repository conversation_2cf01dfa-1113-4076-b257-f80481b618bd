<template>
  <div class="section_padding">
    <div class="section_padding_back member_receive_lists">
      <div class="member_receive_lists_back">
        <SldComHeader :title="L('领取详情')" :back="true" />
        <div class="com_line" style="height: 1px"></div>
        <BasicTable @register="registerTable" rowKey="couponId">
          <!-- dev_supplier-start -->
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.dataIndex == 'storeName'">
              <div class="flex_column_center_center">
                <span class="mem_name" v-if="record.storeName">{{ record.storeName }}</span>
                <span class="mem_name" v-if="record.userName">{{ record.userName }}</span>
                <span class="mem_name" v-if="!record.storeName&&!record.userName">--</span>
              </div>
            </template>
          </template>
          <!-- dev_supplier-end -->
        </BasicTable>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'CouponReceiveList',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, reactive, onMounted,computed } from 'vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { useRoute } from 'vue-router';
  import { getCouponReceiveListsApi } from '/@/api/promotion/coupon';
  import { useUserStore } from '/@/store/modules/user';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const route = useRoute();

  const userStore = useUserStore();

  const getUserInfo = computed(() => {
    return userStore.getStoreInfo;
  });

  // 参数
  const id = ref(route.query?.id);

  const searchInfo = ref({
    couponId: id.value,
  });
  const columns = reactive({
    data: [
      {
        title: L('会员名称'),
        dataIndex: 'memberName',
        align: 'center',
        width: 100,
      },
      {
        title: L('使用状态'),
        dataIndex: 'useStateValue',
        align: 'center',
        width: 100,
      },
      {
        title: L('领取时间'),
        dataIndex: 'receiveTime',
        align: 'center',
        width: 100,
      },
      {
        title: L('使用时间'),
        dataIndex: 'useTime',
        align: 'center',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
    ],
  });
  const searchFormSchema = reactive({
    data: [
      {
        component: 'Input',
        label: L('会员名称'),
        field: 'memberName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: L('请输入会员名称'),
        },
      },
      {
        colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
        component: 'RangePicker',
        label: L('领取时间'),
        field: '[receiveStartTime,receiveEndTime]',
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: [L('开始时间'), L('结束时间')],
        },
      },
      {
        colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
        component: 'RangePicker',
        label: L('使用时间'),
        field: '[useStartTime,useEndTime]',
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: [L('开始时间'), L('结束时间')],
        },
      },
      {
        component: 'Select',
        label: L('使用状态'),
        field: 'useState',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: L('请选择使用状态'),
          options: [
            { value: '', label: L('全部') },
            { value: '1', label: L('未使用') },
            { value: '2', label: L('已使用') },
            { value: '3', label: L('已过期') },
          ],
        },
      },
    ],
  });

  // 表格数据
  const [registerTable,{setColumns,getForm}] = useTable({
    // 请求接口
    api: getCouponReceiveListsApi,
    // 表头
    columns: columns.data,
    immediate: true,
    // 参数
    searchInfo: searchInfo.value,
    // 点击搜索前处理的参数
    beforeFetch(values) {
      values.receiveStartTime = values.receiveStartTime
        ? values.receiveStartTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.receiveEndTime = values.receiveEndTime
        ? values.receiveEndTime.split(' ')[0] + ' 23:59:59'
        : undefined;
      values.useStartTime = values.useStartTime
        ? values.useStartTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.useEndTime = values.useEndTime
        ? values.useEndTime.split(' ')[0] + ' 23:59:59'
        : undefined;
      return values;
    },
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: searchFormSchema.data,
    },
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    // 是否显示序号列
    showIndexColumn: true,
  });

  onMounted(() => {
    // dev_supplier-start
    if(getUserInfo.value.shopType&&getUserInfo.value.shopType=='3'){
      for (let i = 0; i < columns.data.length; i++) {
        if (columns.data[i].dataIndex == 'memberName') {
          columns.data[i].title = '用户信息'
          columns.data[i].dataIndex = 'storeName'
          break;
        }
      }
      for (let i = 0; i < searchFormSchema.data.length; i++) {
        if (searchFormSchema.data[i].field == 'memberName') {
          searchFormSchema.data[i].field = 'storeName'
          searchFormSchema.data[i].label = '用户名称'
          searchFormSchema.data[i].componentProps.placeholder = '请输入用户名称'
          break;
        }
      }
      getForm().updateSchema({field: 'storeName',label:'用户名称', componentProps: { placeholder:'请输入用户名称' } })
      setColumns(columns.data);
    }
    // dev_supplier-end
  });
</script>
<style lang="less" scoped>
  @import './style/system_lists.less';

  p {
    margin-bottom: 0;
  }

  
</style>
