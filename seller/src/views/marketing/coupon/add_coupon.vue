<template>
  <div class="section_padding add_coupon">
    <div class="section_padding_back full_activity">
      <SldComHeader :title="title" />
      <div class="com_line" style="height: 1px"></div>
      <Spin :spinning="loading">
        <div class="add_coupon_height">
          <div style="margin-top: 150px;" v-if="enableFlag == 0">
            <Empty :image="simpleImage"   
            :image-style="{
              height: '80px',
            }" :description="L('优惠券模块暂未开启')"/>
          </div>
          <Form layout="inline" ref="formRef" :model="coupon_detail">
            <div v-if="enableFlag == 1">
              <CommonTitle :text="L('优惠券基本信息')" style="margin-top: 10px" />
              <div class="full_acm_activity flex_column_start_start">
                <!-- 优惠券名称 start -->
                <div class="item flex_row_start_start">
                  <div class="left"> <span style="color: red">*</span>{{ L('优惠券名称') }} </div>
                  <div class="right">
                    <Form.Item
                      :validateFirst="true"
                      :extra="L('最多输入20个字')"
                      name="couponName"
                      style="width: 400px"
                      :rules="[
                        {
                          required: true,
                          whitespace: true,
                          message: L('请输入优惠券名称'),
                        },
                        {
                          validator: async (rule, value) => {
                            await validatorEmoji(rule, value);
                          },
                        },
                      ]"
                    >
                      <Input
                        :maxLength="20"
                        style="width: 400px !important"
                        :placeholder="L('请输入优惠券名称')"
                        v-model:value="coupon_detail.couponName"
                      />
                    </Form.Item>
                  </div>
                </div>
                <!-- 优惠券名称 end -->
                <!-- 发放总量 start -->
                <div class="item flex_row_start_start">
                  <div class="left"> <span style="color: red">*</span>{{ L('发放总量') }} </div>
                  <div class="right">
                    <Form.Item
                      :extra="L('最多100000000张，修改优惠券总量时只能增加不能减少，请谨慎设置')"
                      name="publishNum"
                      style="width: 400px"
                      :rules="[
                        {
                          required: true,
                          message: L('请输入发放总量'),
                        },
                      ]"
                    >
                      <InputNumber
                        :max="100000000"
                        :min="
                          query.type != undefined && query.type == 'edit'
                            ? coupon_detail.publishNum
                            : 1
                        "
                        style="width: 400px !important"
                        :precision="0"
                        :placeholder="L('请输入发放总量')"
                        v-model:value="coupon_detail.publishNum"
                      />
                    </Form.Item>
                  </div>
                </div>
                <!-- 发放总量 end -->
                <!-- 适用商品 start -->
                <div class="item flex_row_start_start">
                  <div class="left"> <span style="color: red">*</span>{{ L('适用商品') }} </div>
                  <div class="right">
                    <Form.Item name="useType" style="width: 400px">
                      <RadioGroup size="small" v-model:value="coupon_detail.useType" @change="handleUseType">
                        <Radio :value="1">{{ L('全部商品可用') }}</Radio>
                        <Radio :value="2">{{ L('指定商品可用') }}</Radio>
                      </RadioGroup>
                    </Form.Item>
                  </div>
                </div>
                <!-- 适用商品 end -->
                <!-- 已选择商品 start -->
                <div class="item flex_row_start_start" v-if="coupon_detail.useType == 2">
                  <div class="left"> <span style="color: red">*</span>{{ L('已选择商品') }} </div>
                  <div class="right">
                    <span class="reset_sel" @click="resetSelGoods">{{ L('重新选择') }}</span>
                    <div class="">
                      <BasicTable
                        style="width: 800px"
                        rowKey="goodsId"
                        :columns="columns_spu"
                        :dataSource="selectedRows"
                        :pagination="false"
                        :bordered="true"
                        :maxHeight="200"
                        :ellipsis="false"
                        :actionColumn="{
                          title: L('操作'),
                          width: 80,
                          dataIndex: 'action',
                        }"
                      >
                        <template #bodyCell="{ column, text, record }">
                          <template v-if="column.dataIndex == 'mainImage'">
                            <Popover placement="rightTop">
                              <template #content>
                                <div style="width: 200px">
                                  <img
                                    :src="text"
                                    alt=""
                                    style="max-width: 100%; max-height: 100%"
                                  />
                                </div>
                              </template>
                              <div class="business_load_img">
                                <img :src="text" alt="" />
                              </div>
                            </Popover>
                          </template>
                          <template v-if="column.dataIndex === 'action'">
                            <div
                              @click="delGoods(record.goodsId)"
                              class="coupon_goods_operate flex_row_center_center"
                            >
                              <AliSvgIcon
                                iconName="iconshanchu5"
                                width="18px"
                                height="18px"
                                :fillColor="'#2d2d2d'"
                              />
                            </div>
                          </template>
                        </template>
                      </BasicTable>
                    </div>
                  </div>
                </div>
                <!-- 已选择商品 end -->
                <!-- 选择分类 start -->
                <div class="item flex_row_start_start" v-if="coupon_detail.useType == 3">
                  <div class="left"> <span style="color: red">*</span>{{ L('选择分类') }}</div>
                  <div class="right">
                    <div class="scroll_bars_tree">
                      <Tree
                        style="min-width: 180px; padding-right: 10px"
                        checkable
                        :autoExpandParent="false"
                        :treeData="cat_data"
                        @check="handleCatCheck"
                        :checkedKeys="checkedCatIds"
                      />
                    </div>
                  </div>
                </div>
                <!-- 选择分类 end -->
                 <!-- 获取方式 start -->
                <div class="item flex_row_start_start">
                  <div class="left"> <span style="color: red">*</span>{{ L('获取方式') }} </div>
                  <div class="right" style="margin-top: 6px">
                    <Form.Item name="publishType" style="width: 365px">
                      <RadioGroup size="small" v-model:value="coupon_detail.publishType">
                        <Radio :value="1">{{ L('免费领取') }}</Radio>
                        <Radio :value="3">{{ L('活动赠送') }}</Radio>
                      </RadioGroup>
                      <div class="extra_box">
                        <div>{{ L('选择“免费领取”类型则领取方式为用户在领券中心等处直接点击领取') }}</div>
                        <div>{{ L('选择“活动赠送”类型则在成功参与指定商城活动后系统自动赠送该券') }}</div>
                      </div>
                    </Form.Item>
                  </div>
                </div>
                <!-- 获取方式 end -->
                <!-- 活动时间 start -->
                <div class="item flex_row_start_start" v-if="coupon_detail.publishType == 1">
                  <div class="left"> <span style="color: red">*</span>{{ L('活动时间') }}</div>
                  <div class="right">
                    <Form.Item
                      name="publishTime"
                      :extra="L('该时间为优惠券领取的起止时间')"
                      style="width: 400px"
                      :rules="[
                        {
                          required: true,
                          message: L('请选择活动时间'),
                        },
                      ]"
                    >
                      <RangePicker
                        style="width: 400px"
                        :format="'YYYY-MM-DD HH:mm:00'"
                        v-model:value="coupon_detail.publishTime"
                        :disabledDate="
                          (currentDate) => currentDate && currentDate < moment().subtract(1, 'days')
                        "
                        :showTime="{ format: 'HH:mm' }"
                        :placeholder="[L('开始时间'), L('结束时间')]"
                        :getPopupContainer="
                          (triggerNode) => {
                            return triggerNode.parentNode;
                          }
                        "
                      />
                    </Form.Item>
                  </div>
                </div>
                <!-- 活动时间 end -->
                <!-- 使用时间 start -->
                <div class="item flex_row_start_start">
                  <div class="left"> <span style="color: red">*</span>{{ L('使用时间') }} </div>
                  <div class="right">
                    <Form.Item name="useTimeType" style="width: 400px">
                      <RadioGroup size="small" v-model:value="coupon_detail.useTimeType">
                        <Radio :value="1">{{ L('固定使用时间') }}</Radio>
                        <Radio :value="2">{{ L('灵活使用时间') }}</Radio>
                      </RadioGroup>
                    </Form.Item>
                  </div>
                </div>
                <!-- 使用时间 end -->
                <!-- 设置固定使用时间 start -->
                <div class="item flex_row_start_start" v-if="coupon_detail.useTimeType == 1">
                  <div class="left"> <span style="color: red">*</span>{{ L('设置固定使用时间') }}</div>
                  <div class="right">
                    <Form.Item
                      name="effectiveTime"
                      style="width: 400px"
                      :rules="[
                        {
                          required: true,
                          message: L('请先设置固定使用时间'),
                        },
                      ]"
                    >
                      <RangePicker
                        style="width: 400px"
                        format="YYYY-MM-DD HH:mm:00"
                        v-model:value="coupon_detail.effectiveTime"
                        :disabledDate="
                          (currentDate) => currentDate && currentDate < moment().subtract(1, 'days')
                        "
                        :showTime="{ format: 'HH:mm' }"
                        :placeholder="[L('开始时间'), L('结束时间')]"
                        :getPopupContainer="
                          (triggerNode) => {
                            return triggerNode.parentNode;
                          }
                        "
                      />
                    </Form.Item>
                  </div>
                </div>
                <!-- 设置固定使用时间 end -->
                <!-- 设置灵活使用时间 start -->
                <div class="item flex_row_start_start" v-if="coupon_detail.useTimeType == 2">
                  <div class="left"> <span style="color: red">*</span>{{ L('设置灵活使用时间') }}</div>
                  <div class="right">
                    <Form.Item
                      :extra="L('以天为单位')"
                      name="cycle"
                      class="cycle_box"
                      style="width: 300px"
                      :rules="[
                        {
                          required: true,
                          message: L('请输入灵活使用时间'),
                        },
                      ]"
                    >
                      <div class="flex_row_start_center">
                        <span
                          style="display: inline-block; margin-right: 5px; color: rgb(0 0 0 / 65%)"
                          >{{ L('领券当日起') }}</span
                        >
                        <InputNumber
                          :max="1000"
                          :min="1"
                          style="width: 180px !important"
                          :precision="0"
                          :placeholder="L('请输入灵活使用时间')"
                          v-model:value="coupon_detail.cycle"
                        />
                        <span
                          style="display: inline-block; margin-left: 5px; color: rgb(0 0 0 / 65%)"
                          >{{ L('天') }}</span
                        >
                      </div>
                    </Form.Item>
                  </div>
                </div>
                <!-- 设置灵活使用时间 end -->
                <!-- 使用门槛 start -->
                <div class="item flex_row_start_start">
                  <div class="left"> <span style="color: red">*</span>{{ L('使用门槛') }}</div>
                  <div class="right">
                    <Form.Item
                      :extra="L('订单满多少元时可以使用此优惠券，0元代表无使用门槛')"
                      name="limitQuota"
                      class="limitQuota_box"
                      style="width: 300px"
                      :rules="[
                        {
                          required: true,
                          message: L('请输入使用门槛'),
                        },
                      ]"
                    >
                      <div class="flex_row_start_center">
                        <span
                          style="display: inline-block; margin-right: 5px; color: rgb(0 0 0 / 65%)"
                          >{{ L('订单满') }}</span
                        >
                        <InputNumber
                          :max="99999999"
                          :min="0"
                          style="width: 180px !important"
                          :precision="2"
                          :placeholder="L('请输入使用门槛')"
                          v-model:value="coupon_detail.limitQuota"
                        />
                        <span
                          style="display: inline-block; margin-left: 5px; color: rgb(0 0 0 / 65%)"
                          >{{ L('元') }}</span
                        >
                      </div>
                    </Form.Item>
                  </div>
                </div>
                <!-- 使用门槛 end -->
                <!-- 获取方式 start -->
                <div class="item flex_row_start_start">
                  <div class="left"> <span style="color: red">*</span>{{ L('优惠券类型') }} </div>
                  <div class="right">
                    <Form.Item name="couponType" style="width: 365px">
                      <RadioGroup size="small" v-model:value="coupon_detail.couponType">
                        <Radio :value="1">{{ L('满减券') }}</Radio>
                        <Radio :value="2">{{ L('折扣券') }}</Radio>
                        <Radio :value="3">{{ L('随机金额券') }}</Radio>
                      </RadioGroup>
                    </Form.Item>
                  </div>
                </div>
                <!-- 获取方式 end -->
                <!-- 选择满减券的优惠内容 start -->
                <div class="item flex_row_start_start" v-if="coupon_detail.couponType == 1">
                  <div class="left"> <span style="color: red">*</span>{{ L('优惠内容') }}</div>
                  <div class="right">
                    <Form.Item
                      name="publishValue"
                      class="publishValue_box"
                      style="width: 300px"
                      :rules="[
                        {
                          required: true,
                          message: L('请输入优惠内容'),
                        },
                      ]"
                    >
                      <div class="flex_row_start_center">
                        <span
                          style="display: inline-block; margin-right: 5px; color: rgb(0 0 0 / 65%)"
                          >{{ L('减免') }}</span
                        >
                        <InputNumber
                          :max="99999"
                          :min="0.01"
                          style="width: 140px !important"
                          :precision="2"
                          :placeholder="L('请输入优惠内容')"
                          v-model:value="coupon_detail.publishValue"
                        />
                        <span
                          style="display: inline-block; margin-left: 5px; color: rgb(0 0 0 / 65%)"
                          >{{ L('元') }}</span
                        >
                      </div>
                    </Form.Item>
                  </div>
                </div>
                <!-- 选择满减券的优惠内容 end -->
                <!-- 选择折扣券的优惠内容 start -->
                <div class="item flex_row_start_start" v-if="coupon_detail.couponType == 2">
                  <div class="left"> <span style="color: red">*</span>{{ L('优惠内容') }}</div>
                  <div class="right">
                    <Form.Item
                      name="publishValue"
                      class="publishValue_box_one"
                      style="width: 190px"
                      :extra="L('输入90代表9折，85代表85折')"
                      :rules="[
                        {
                          required: true,
                          message: L('请输入折扣'),
                        },
                      ]"
                    >
                      <div class="flex_row_start_center">
                        <span
                          style="display: inline-block; margin-right: 5px; color: rgb(0 0 0 / 65%)"
                          >{{ L('打') }}</span
                        >
                        <InputNumber
                          :max="100"
                          :min="1"
                          style="width: 140px !important"
                          :precision="0"
                          :placeholder="L('请输入折扣')"
                          v-model:value="coupon_detail.publishValue"
                        />
                        <span
                          style="display: inline-block; margin-left: 5px; color: rgb(0 0 0 / 65%)"
                          >{{ L('折') }}</span
                        >
                      </div>
                    </Form.Item>
                  </div>
                  <div class="right">
                    <Form.Item
                      name="discountLimitAmount"
                      class="discountLimitAmount_box"
                      style="width: 300px"
                      :rules="[
                        {
                          required: true,
                          message: L('请输入优惠金额'),
                        },
                      ]"
                    >
                      <div class="flex_row_start_center">
                        <span
                          style="display: inline-block; margin-right: 5px; color: rgb(0 0 0 / 65%)"
                          >{{ L('最多优惠') }}</span
                        >
                        <InputNumber
                          :max="99999999"
                          :min="0"
                          style="width: 140px !important"
                          :precision="2"
                          :placeholder="L('请输入优惠金额')"
                          v-model:value="coupon_detail.discountLimitAmount"
                        />
                        <span
                          style="display: inline-block; margin-left: 5px; color: rgb(0 0 0 / 65%)"
                          >{{ L('元') }}</span
                        >
                      </div>
                    </Form.Item>
                  </div>
                </div>
                <!-- 选择折扣券的优惠内容 end -->
                <!-- 选择随机金额券的优惠内容 start -->
                <div class="item flex_row_start_start" v-if="coupon_detail.couponType == 3">
                  <div class="left"> <span style="color: red">*</span>{{ L('优惠内容') }}</div>
                  <div class="right">
                    <Form.Item
                      name="randomMin"
                      class="randomMin_box"
                      style="width: 218px"
                      :rules="[
                        {
                          required: true,
                          message: L('请输入最小值'),
                        },
                      ]"
                    >
                      <div class="flex_row_start_center">
                        <span
                          style="display: inline-block; margin-right: 5px; color: rgb(0 0 0 / 65%)"
                          >{{ L('范围内随机') }}</span
                        >
                        <InputNumber
                          :max="99999999"
                          :min="0"
                          style="width: 140px !important"
                          :precision="2"
                          :placeholder="L('请输入最小值')"
                          v-model:value="coupon_detail.randomMin"
                        />
                      </div>
                    </Form.Item>
                  </div>
                  <div style="margin-top: 3px">~</div>
                  <div class="right">
                    <Form.Item
                      name="randomMax"
                      class="randomMax_box"
                      style="width: 210px; margin-left: 16px"
                      :rules="[
                        {
                          required: true,
                          message: L('请输入最大值'),
                        },
                      ]"
                    >
                      <div class="flex_row_start_center">
                        <InputNumber
                          :max="99999999"
                          :min="0.01"
                          style="width: 140px !important"
                          :precision="2"
                          :placeholder="L('请输入最大值')"
                          v-model:value="coupon_detail.randomMax"
                        />
                        <span
                          style="display: inline-block; margin-left: 5px; color: rgb(0 0 0 / 65%)"
                          >{{ L('元') }}</span
                        >
                      </div>
                    </Form.Item>
                  </div>
                </div>
                <div
                  class="full_acm_activity flex_column_start_start"
                  v-if="coupon_detail.couponType == 3"
                >
                  <div class="item flex_row_start_start">
                    <div class="left"> <span style="color: red">*</span>{{ L('优惠总金额') }}</div>
                    <div class="right">
                      <Form.Item
                        :extra="L('本次生成的随机金额优惠券的金额之和')"
                        name="publishAmount"
                        class="publishAmount_box"
                        style="width: 300px"
                        :rules="[
                          {
                            required: true,
                            message: L('请输入优惠总金额'),
                          },
                        ]"
                      >
                        <InputNumber
                          :max="99999999"
                          :min="0.01"
                          style="width: 150px !important"
                          :precision="0"
                          :placeholder="L('请输入优惠总金额')"
                          v-model:value="coupon_detail.publishAmount"
                        />
                      </Form.Item>
                    </div>
                  </div>
                </div>
                <!-- 选择随机金额券的优惠内容 end -->
              </div>
              <CommonTitle
                :text="L('领取和使用规则')"
                style="margin-top: 10px"
                v-if="coupon_detail.publishType == 1"
              />
              <!-- 每人限领次数 start -->
              <div
                class="full_acm_activity flex_column_start_start"
                v-if="coupon_detail.publishType == 1"
              >
                <div class="item flex_row_start_start">
                  <div class="left"> <span style="color: red">*</span>{{ L('每人限领次数') }}</div>
                  <div class="right">
                    <Form.Item
                      :extra="L('每位会员限制领取的次数，0代表不限制次数')"
                      name="limitReceive"
                      style="width: 400px"
                      :rules="[
                        {
                          required: true,
                          message: L('请输入限制领取次数'),
                        },
                      ]"
                    >
                      <InputNumber
                        :max="100"
                        :min="0"
                        style="width: 400px !important"
                        :precision="0"
                        :placeholder="L('请输入限制领取次数')"
                        v-model:value="coupon_detail.limitReceive"
                      />
                    </Form.Item>
                  </div>
                </div>
              </div>
              <!-- 每人限领次数 end -->
            </div>
            <div style="width: 100%; height: 60px; background: #fff"></div>
          </Form>
        </div>
        <div
          class="m_diy_bottom_wrap"
          v-if="enableFlag == 1 && !isFirstLoading"
          :style="{ position: 'fixed', left: getRealWidth + 10 + 'px' }"
        >
          <div class="add_goods_bottom_btn" @click="goBack"> {{ L('返回') }} </div>
          <div class="add_goods_bottom_btn add_goods_bottom_btn_sel" @click="handleSaveAllData">
            {{ L('保存') }}
          </div>
        </div>
      </Spin>
    </div>
    <SldSelMoreLeftRightGoods 
      @confirm-event="seleGoods"
      @cancle-event="sldHandleCancle" 
      :width="1000"
      :selectedRow="sele_more_goods.info"
      :selectedRowKey="sele_more_goods.ids"
      :title="L('选择商品(至少选择一个)')"
      :height='height - 400'
      :modalVisible="modalVisibleGoods"
      :extra="sele_more_goods"
      >

    </SldSelMoreLeftRightGoods>
  </div>
</template>
<script setup>
  import { getCurrentInstance, ref, reactive, onMounted } from 'vue';
  import {
    Spin,
    Form,
    Input,
    InputNumber,
    RadioGroup,
    Radio,
    Tree,
    RangePicker,
    Empty
  } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import CommonTitle from '/@/components/CommonTitle/index.vue';
  import SldSelMoreLeftRightGoods from '/@/components/SldSelMoreLeftRightGoods/index.vue';
  import moment from 'moment';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import dayjs from 'dayjs';
  import { useRoute, useRouter } from 'vue-router';
  import { sucTip, failTip } from '@/utils/utils';
  import { BasicTable } from '/@/components/Table';
  import { validatorEmoji } from '/@/utils/validate';
  import { useUserStore } from '/@/store/modules/user';
  import { getSettingListApi } from '/@/api/common/common';
  import simpleImage from '/@/assets/images/moudle_disable.png';
  import { pageClose } from '/@/utils/utils';
  import { useMultipleTabStore } from '/@/store/modules/multipleTab';
  import {
    getCouponGoodsListApi,
    getCouponDetailApi,
    getCouponUpdateApi,
    getCouponAddApi,
  } from '/@/api/promotion/coupon';

  const vm = getCurrentInstance();
  const userStore = useUserStore();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const height = ref(document.body.clientHeight)
  const tabStore = useMultipleTabStore();
  const title = ref(L('发布优惠券'))
  const route = useRoute();
  const { getRealWidth } = useMenuSetting();
  const router = useRouter();
  const formRef = ref();
  const str_info = ref('coupon_is_enable')
  const enableFlag = ref(-1); //优惠券开关
  const isFirstLoading = ref(true); //是否第一次加载
  const coupon_detail = ref({
    useType: 1,
    useTimeType: 1,
    publishType: 1,
    couponType: 1, //当前选择的优惠券类型
  }); //优惠券详情
  const cat_data = ref([]); //三级分类数据
  const checkedCatIds = ref([]); //选择的分类id数组
  const sle_more_title = ref(''); //选择商品的标题
  const modalVisibleGoods = ref(false);
  const query = ref(route.query);
  const loading = ref(false);
  const selectedRows = ref([]);
  const selectedRowKeys = ref([]); //selectedRows的key
  const columns_spu = ref([
    {
      title: L('商品图片'),
      dataIndex: 'mainImage',
      align: 'center',
      width: 100,
    },
    {
      title: L('商品名称'),
      dataIndex: 'goodsName',
      align: 'center',
      width: 150,
    },
    {
      title: L('商品价格(¥)'),
      dataIndex: 'goodsPrice',
      align: 'center',
      width: 100,
    },
    {
      title: L('商品库存'),
      dataIndex: 'goodsStock',
      align: 'center',
      width: 100,
    },
  ]);
  const sele_more_goods = ref({
    info: [], //选择的商品数组
    ids: [], //选择的商品id数组
    min_num: 1, //最小数量，0为不限制
    max_num: 10000, //最多选择10000个
  });
  const sele_cat_ids_array = ref([]); //选择的最后一级分类id数组
  //获取站点基本配置
  const getSetting = async () => {
    const res = await getSettingListApi({
      str: str_info.value,
    });
    if (res.state == 200 && res.data) {
      enableFlag.value = res.data[0].value;
      if(res.data[0].value=='1'){
        if (route.query?.id != undefined && Number(route.query?.id) > 0) {
          get_detail(route.query?.id);
        } else {
          loading.value = false
          isFirstLoading.value = false;
        }
        if(route.query.type&&route.query.type == 'edit'){
          title.value = L('编辑优惠券')
        }else{
          title.value = L('发布优惠券')
        }
      }else{
        loading.value = false
      }
    } else {
      failTip(res.msg);
    }
  };

  //选择分类事件
  const handleCatCheck = (checkedKeys, e) => {
    sele_cat_ids_array.value = [];
    e.checkedNodes.map((item) => {
      if (item.children == null || item.children.length == 0) {
        sele_cat_ids_array.value.push(item.categoryId);
      }
    });
    checkedCatIds.value = checkedKeys;
  };

  //获取优惠券详情
  const get_detail = async (id) => {
    loading.value = true;
    try {
      let res = await getCouponDetailApi({ couponId: id });
      if (res.state == 200) {
        if (res.data.useType == 2) {
          get_goods_list(id); //获取商品列表
        }
        if (res.data.useType == 3) {
          res.data.goodsCategoryList.map((item) => {
            let tar_id = item.categoryId3;
            tar_id = tar_id ? tar_id : item.categoryId2;
            tar_id = tar_id ? tar_id : item.categoryId1;
            checkedCatIds.value.push(tar_id);
            if (tar_id) {
              sele_cat_ids_array.value.push(tar_id);
            }
          });
        }
        if (route.query?.type != undefined && route.query?.type == 'copy') {
          res.data.couponName = ''; //清空优惠券名称
          res.data.publishStartTime = ''; //清空优惠券的活动时间
          if (res.data.effectiveTimeType == 1) {
            //如果是固定使用时间，需要清空使用时间
            res.data.effectiveStart = '';
          }
        }
        if (res.data.effectiveStart) {
          res.data.effectiveTime = [
            dayjs(res.data.effectiveStart, 'YYYY-MM-DD HH:mm:ss'),
            dayjs(res.data.effectiveEnd, 'YYYY-MM-DD HH:mm:ss'),
          ];
        }
        if (res.data.publishStartTime) {
          res.data.publishTime = [
            dayjs(res.data.publishStartTime, 'YYYY-MM-DD HH:mm:ss'),
            dayjs(res.data.publishEndTime, 'YYYY-MM-DD HH:mm:ss'),
          ];
        }
        coupon_detail.value = res.data;
        coupon_detail.value.useTimeType = res.data.effectiveTimeType;
        isFirstLoading.value = false;
        loading.value = false;
      } else {
        failTip(res.msg);
      }
    } catch (error) {}
  };

  // 获取商品列表
  const get_goods_list = async (id) => {
    try {
      let res = await getCouponGoodsListApi({ couponId: id });
      if (res.state == 200) {
        if (res.data.length > 0) {
          selectedRows.value = res.data;
          selectedRows.value.map((item) => {
            item.mainImage = item.goodsImage;
            selectedRowKeys.value.push(item.goodsId);
          });
          sele_more_goods.value.info = selectedRows.value;
          sele_more_goods.value.ids = selectedRowKeys.value;
        }
      } else {
        failTip(res.msg);
      }
    } catch (error) {
      console.log(error, 'error');
    }
  };

  // 返回
  const goBack = () => {
    if (window.history && window.history.length == 1) {
      pageClose();
    } else {
      const { fullPath } = route;
      tabStore.closeTabByKey(fullPath, router);
      router.back();
    }
  };

  // 保存
  const handleSaveAllData = async () => {
    formRef.value.validate().then(async (values) => {
      if (values.publishType == 1) {
        //免费领取优惠券——活动时间处理
        if (values.publishTime) {
          values.publishStartTime = values.publishTime[0]
            ? values.publishTime[0].format('YYYY-MM-DD HH:mm:00')
            : '';
          values.publishEndTime = values.publishTime[1]
            ? values.publishTime[1].format('YYYY-MM-DD HH:mm:00')
            : '';
          if (values.publishTime[0].unix() == values.publishTime[1].unix()) {
            failTip(L('活动结束时间必须晚于开始时间～'));
            return false;
          }
          delete values.publishTime;
        }
      }
      //使用时间处理
      if (values.effectiveTime) {
        values.effectiveStart = values.effectiveTime[0]
          ? values.effectiveTime[0].format('YYYY-MM-DD HH:mm:00')
          : '';
        values.effectiveEnd = values.effectiveTime[1]
          ? values.effectiveTime[1].format('YYYY-MM-DD HH:mm:00')
          : '';
        if (values.effectiveTime[0].unix() == values.effectiveTime[1].unix()) {
          failTip(L('使用结束时间必须晚于开始时间～'));
          return false;
        }
        delete values.effectiveTime;
      }
      //适用商品类型为指定商品
      if (values.useType == 2) {
        values.goodsIds = selectedRowKeys.value.join(',');
      }
      //适用商品类型为指定分类
      if (values.useType == 3) {
        values.categoryIds = sele_cat_ids_array.value.join(',');
      }
      if (values.publishType == 1) {
        if (values.limitReceive * 1 > values.publishNum * 1) {
          failTip(L('每人限领次数不能超过发放总数～'));
          return false;
        }
      } else {
        values.limitReceive = 0; //活动赠送的优惠券不限制会员领取的数量
      }
      if(values.useTimeType&&values.useTimeType==1){
        values.cycle = ''
      }
      let res = '';
      if (
        route.query?.id != undefined &&
        Number(route.query?.id) > 0 &&
        route.query?.type == 'edit'
      ) {
        //编辑优惠券
        values.couponId = route.query?.id;
        loading.value = true;
        res = await getCouponUpdateApi(values);
      } else {
        //新增优惠券
        loading.value = true;
        res = await getCouponAddApi(values);
      }
      if (res.state == 200) {
        sucTip(res.msg);
        if(route.query.type == 'edit'){
          userStore.setDelKeepAlive([route.name,'MarketingCouponList','MarketingCouponListToView'])
        }else{
          userStore.setDelKeepAlive([route.name,'MarketingCouponList'])
        }
        setTimeout(() => {
          loading.value = true;
          goBack();
        }, 500);
      } else {
        loading.value = false;
        failTip(res.msg);
      }
    });
  };

  //商品删除事件
  const delGoods = (goodsId) => {
    selectedRows.value = selectedRows.value.filter((item) => item.goodsId != goodsId);
    selectedRowKeys.value = selectedRowKeys.value.filter((item) => item != goodsId);
    sele_more_goods.value.ids = [...selectedRowKeys.value];
    sele_more_goods.value.info = JSON.parse(JSON.stringify(selectedRows.value));
  };

  // 打开商品弹窗
  const handleUseType = (e)=> {
    if(e.target.value == 2){
      modalVisibleGoods.value = true
    }else{
      selectedRows.value  = []
      selectedRowKeys.value  = []
      sele_more_goods.value.ids = []
      sele_more_goods.value.info = []
    }
  }

  // 重新选择
  const resetSelGoods = ()=> {
    modalVisibleGoods.value = true
  }

  // 关闭弹窗
  const sldHandleCancle = ()=>{
    modalVisibleGoods.value = false
  }

  // 弹窗点击确定
  const seleGoods = (selectedRowsP, selectedRowKeysP)=>{
    sele_more_goods.value.ids = [...selectedRowKeysP];
    sele_more_goods.value.info = JSON.parse(JSON.stringify(selectedRowsP));
    selectedRows.value = selectedRowsP
    selectedRowKeys.value = selectedRowKeysP
    sldHandleCancle()
  }

  onMounted(() => {
    // dev_supplier-start
    if(userStore.getStoreInfo.shopType&&userStore.getStoreInfo.shopType=='3'){
      str_info.value = 'supplier_coupon_is_enable'
    }else{
      str_info.value = 'coupon_is_enable'
    }
    // dev_supplier-end
    
    getSetting();
  });
</script>
<style lang="less">
  @import './style/add_coupon.less';
</style>
