<template>
  <div class="section_padding view_coupon">
    <div class="section_padding_back">
      <SldComHeader :title="L('优惠券详情')" :back="true" />
      <div class="com_line" style="height: 1px;margin-bottom: 10px;"></div>
      <div class="height_detail">
        <CommonTitle :text="L('优惠券基本信息')"  />
        <!-- 基本信息-start -->
        <div class="full_acm_activity flex_column_start_start">
          <div class="item flex_row_start_center">
            <div class="left">
              <span style="color: red">*</span>
              {{ L('优惠券名称') }}
            </div>
            <div class="right">
              {{ coupon_detail.couponName }}
            </div>
          </div>
          <div class="item flex_row_start_center">
            <div class="left">
              <span style="color: red">*</span>
              {{ L('发放总量') }}
            </div>
            <div class="right">
              {{ coupon_detail.publishNum }}
            </div>
          </div>
          <div class="item flex_row_start_center">
            <div class="left">
              <span style="color: red">*</span>
              {{ L('适用商品') }}
            </div>
            <div class="right">
              {{ useType == 1 ? L('全部商品可用') : '' }}
              {{ useType == 2 ? L('指定商品可用') : '' }}
            </div>
          </div>
          <div class="item flex_row_start_start" v-if="useType == 2">
            <div class="left">
              <span style="color: red">*</span>
              {{ L('已选择商品') }}
            </div>
            <div class="right">
              <BasicTable
                style="width: 700px"
                :columns="columns_spu"
                :dataSource="selectedRows"
                :pagination="false"
                :bordered="true"
                :maxHeight="200"
                :ellipsis="false"
              >
                <template #bodyCell="{ column, text }">
                  <template v-if="column.dataIndex == 'mainImage'">
                    <Popover placement="rightTop">
                      <template #content>
                        <div style="width: 200px">
                          <img :src="text" alt="" style="max-width: 100%; max-height: 100%" />
                        </div>
                      </template>
                      <div class="business_load_img">
                        <img :src="text" alt="" />
                      </div>
                    </Popover>
                  </template>
                </template>
              </BasicTable>
            </div>
          </div>
          <div
            class="item flex_row_start_center"
            v-if="coupon_detail.publishType != undefined && coupon_detail.publishType == 1"
          >
            <div class="left">
              <span style="color: red">*</span>
              {{ L('活动时间') }}
            </div>
            <div class="right">
              {{ coupon_detail.publishStartTime }} ~ {{ coupon_detail.publishEndTime }}
            </div>
          </div>
          <div class="item flex_row_start_center">
            <div class="left">
              <span style="color: red">*</span>
              {{ L('使用时间') }}
            </div>
            <div class="right">
              {{
                useTimeType == 1
                  ? `${coupon_detail.effectiveStart} ~ ${coupon_detail.effectiveEnd}`
                  : ''
              }}
              {{ useTimeType == 2 ? `${L('领券当日起')}${coupon_detail.cycle}${L('天内可以使用')}` : '' }}
            </div>
          </div>
          <div class="item flex_row_start_center">
            <div class="left">
              <span style="color: red">*</span>
              {{ L('获取方式') }}
            </div>
            <div class="right">
              {{ coupon_detail.publishType == 1 ? L('免费领取') : '' }}
              {{ coupon_detail.publishType == 3 ? L('活动赠送') : '' }}
            </div>
          </div>
          <div class="item flex_row_start_center">
            <div class="left">
              <span style="color: red">*</span>
              {{ L('使用门槛') }}
            </div>
            <div class="right">
              {{
                coupon_detail.limitQuota
                  ? `${L('订单满')}${coupon_detail.limitQuota}${L('元时可以使用此优惠券')}`
                  : L('无使用门槛')
              }}
            </div>
          </div>
          <div class="item flex_row_start_center">
            <div class="left">
              <span style="color: red">*</span>
              {{ L('优惠内容') }}
            </div>
            <div class="right">
              {{ curCouponType == 1 ? `${L('满足使用门槛后可以减免')}${coupon_detail.publishValue}${L('元')}` : '' }}
              {{
                curCouponType == 2
                  ? `${L('满足使用门槛后可以享受')}${(coupon_detail.publishValue / 10) * 1}${L('折')}`
                  : ''
              }}
              {{
                curCouponType == 2 && coupon_detail.discountLimitAmount
                  ? `${L('，最多可以优惠')}${coupon_detail.discountLimitAmount}${L('元')}`
                  : ''
              }}
              {{
                curCouponType == 3
                  ? `${L('在')}${coupon_detail.randomMin}～${coupon_detail.randomMax}${L('元范围内随机生成，总共不超过')}${coupon_detail.publishAmount}${L('元')}`
                  : ''
              }}
            </div>
          </div>
        </div>
        <!-- 基本信息-end -->
        <CommonTitle :text="L('领取和使用规则')" style="margin-top: 10px" />
        <div class="full_acm_activity flex_column_start_start">
          <div class="item flex_row_start_center">
            <div class="left">
              <span style="color: red">*</span>
              {{ L('每人限领次数') }}
            </div>
            <div class="right">
              {{
                coupon_detail.limitReceive
                  ? `${L('每位会员限制领取')}${coupon_detail.limitReceive}${L('次')}`
                  : L('不限制次数')
              }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'MarketingCouponListToView',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, reactive, onMounted } from 'vue';
  import { Popover, Tree } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import CommonTitle from '/@/components/CommonTitle/index.vue';
  import { useRoute } from 'vue-router';
  import { BasicTable } from '/@/components/Table';
  import { sucTip, failTip } from '@/utils/utils';
  import {
    getCouponDetailApi,
    getCouponGoodsListApi,
  } from '/@/api/promotion/coupon';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const route = useRoute();
  // 参数
  const id = ref(route.query?.id);
  const query = ref(route.query);
  const coupon_detail = ref({}); //优惠券详情
  const checkedCatIds = ref([]); //选择的分类id数组
  const useType = ref(1); //适用商品类型
  const useTimeType = ref(1); //使用时间类型
  const curCouponType = ref(1); //当前选择的优惠券类型
  const loading = ref(false);
  const selectedRows = ref([]);
  const selectedRowKeys = ref([]);
  const columns_spu = ref([
    {
      title: L('店铺名称'),
      dataIndex: 'storeName',
      align: 'center',
      width: 100,
    },
    {
      title: L('商品图片'),
      dataIndex: 'mainImage',
      align: 'center',
      width: 70,
    },
    {
      title: L('商品名称'),
      dataIndex: 'goodsName',
      align: 'center',
      width: 100,
    },
    {
      title: L('商品价格(¥)'),
      dataIndex: 'goodsPrice',
      align: 'center',
      width: 100,
    },
    {
      title: L('商品库存'),
      dataIndex: 'goodsStock',
      align: 'center',
      width: 100,
    },
  ]);
  const get_detail = async () => {
    try {
      let res = await getCouponDetailApi({ couponId: id.value });
      if (res.state == 200) {
        if (res.data.useType == 2) {
          get_goods_list(); //获取商品列表
        }
        if (res.data.useType == 3) {
          res.data.goodsCategoryList.map((item) => {
            let tar_id = item.categoryId3;
            tar_id = tar_id ? tar_id : item.categoryId2;
            tar_id = tar_id ? tar_id : item.categoryId1;
            checkedCatIds.value.push(tar_id);
          });
        }
        coupon_detail.value = res.data;
        loading.value = false;
        useTimeType.value = res.data.effectiveTimeType; //使用时间类型
        useType.value = res.data.useType; //适用商品类型
        curCouponType.value = res.data.couponType; //优惠券类型
        checkedCatIds;
      } else {
        failTip(res.msg);
      }
    } catch (error) {}
  };

  const get_goods_list = async () => {
    try {
      let res = await getCouponGoodsListApi({ couponId: id.value });
      if (res.state == 200) {
        if (res.data.length > 0) {
          selectedRows.value = res.data;
          selectedRows.value.map((item) => {
            item.mainImage = item.goodsImage;
            selectedRowKeys.value.push(item.goodsId);
          });
        }
      } else {
        failTip(res.msg);
      }
    } catch (error) {}
  };


  onMounted(() => {
    if (id.value != undefined && Number(id.value) > 0) {
      get_detail();
    }
  });
</script>
<style lang="less" scoped>
  @import './style/view_coupon.less';

  p {
    margin-bottom: 0;
  }
</style>
