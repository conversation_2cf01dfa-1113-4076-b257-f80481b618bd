<template>
  <div class="section_padding spell_group_order_lists">
    <div class="section_padding_back section_padding_back_flex">
      <SldComHeader :title="L('订单管理')" back />
      <div style="margin-bottom: 10px">
        <RadioGroup size="small" v-model:value="filter_code" @change="clickFilter">
          <RadioButton
            :value="item.filter_code"
            v-for="(item, index) in filter_data"
            :key="index"
            >{{ item.filter_name }}</RadioButton
          >
        </RadioGroup>
      </div>
      <Spin :spinning="loading" class="Spin_box">
        <div class="spin_height">
          <BasicForm ref="formRef" submitOnReset @register="registerForm" class="basic-form">
          </BasicForm>
          <div class="order_list">
            <ul class="header">
              <li class="width_50 pl_100">{{ L('多人拼团') }}</li>
              <li class="width_10 center">{{ L('单价(元)') }}</li>
              <li class="width_10 center">{{ L('数量') }}</li>
              <li class="width_10 center">{{ L('订单金额') }}</li>
              <li class="width_10 center">{{ L('订单状态') }}</li>
              <li class="width_10 center">{{ L('操作') }}</li>
            </ul>
            <div class="order_content">
              <Empty
                :image="Empty.PRESENTED_IMAGE_SIMPLE"
                v-if="data.list != undefined && data.list.length == 0"
              />
              <div
                class="order_content_height"
                v-if="data.list != undefined && data.list.length > 0"
              >
                <div class="item" v-for="(item, index) in data.list" :key="index">
                  <div class="order_info_num flex_row_between_center">
                    <div class="left flex_row_start_start">
                      <span class="num">{{L('序号:')}}{{ index + 1 }}</span>
                    </div>
                    <div class="right flex_row_end_center">
                      <span class="simulate_group" v-if="item.finishType == 2">{{
                        item.finishTypeValue
                      }}</span>
                      <div class="spell_state"
                        >{{ L('拼团状态：') }}<span class="spell_state_val">{{ item.stateValue }}</span></div
                      >
                    </div>
                  </div>
                  <div class="order_info flex_row_between_center">
                    <div class="left flex_row_start_center">
                      <div class="goods_img_wrap flex_row_center_center">
                        <img :src="item.goodsImage" alt="" />
                      </div>
                      <span class="goods_name">{{ item.goodsName }}</span>
                      <div class="spell_group_num flex_row_start_center">
                        <img src="@/assets/images/spell_group_icon.png" alt="" />
                        <span>{{ item.requiredNum }}{{ L('人团') }}</span>
                      </div>
                    </div>
                    <span class="spell_time"
                      >{{L('开团时间：')}}{{ item.createTime }} ～ {{ item.endTime }}</span
                    >
                  </div>
                  <template v-if="item.memberList && item.memberList.length > 0">
                    <div
                      v-for="(child, child_index) in item.memberList"
                      :key="child_index"
                      class="order_goods_part flex_row_start_center"
                    >
                      <div
                        class="goods flex_column_start_start width_70"
                        :class="{
                          goods_split:
                            item.orderProductListVOList != undefined &&
                            item.orderProductListVOList.length > 1,
                        }"
                      >
                        <div class="goods_item flex_row_start_center" style="width: 100%">
                          <div class="flex_column_between_start" style="width: 72%">
                            <div class="flex_row_start_center">
                              <div class="goods_img_wrap flex_row_center_center">
                                <div
                                  class="member_avatar"
                                  :style="{ backgroundImage: 'url(' + child.memberAvatar + ')' }"
                                >
                                  <span class="spell_leader_flag" v-if="child.isLeader == 1"
                                    >{{ L('团长') }}</span
                                  >
                                </div>
                              </div>
                              <div class="goods_info flex_column_start_start">
                                <span class="member_name">{{ child.memberName }}</span>
                                <span class="goods_spec">{{ child.specValues }}</span>
                              </div>
                            </div>
                            <div class="member_order_info">
                              <span class="tip"
                                >{{ L('订单号：') }}<span class="content">{{ child.orderSn }}</span></span
                              >
                              <span class="tip" style="margin-left: 50px">
                                {{ L('下单时间：') }}<span class="content">{{ child.createTime }}</span>
                              </span>
                            </div>
                          </div>
                          <span class="goods_price width_10 center" style="width: 14%"
                            >￥{{ child.spellPrice }}</span
                          >
                          <span class="buy_num width_10 center" style="width: 14%">{{
                            child.productNum
                          }}</span>
                        </div>
                      </div>
                      <div class="pay_amount width_10 center">{{L('￥')}}{{ child.orderAmount }}</div>
                      <div class="order_state width_10 center">{{ child.orderStateValue }}</div>
                      <div class="operate width_10 center flex_row_center_center">
                        <div class="operate_btn" @click="goDetail(child)">{{ L('查看详情') }}</div>
                      </div>
                    </div>
                  </template>
                </div>
              </div>
            </div>
            <div class="pagination">
              <Pagination
                v-if="
                  data.list != undefined && data.list.length > 0 && data.pagination != undefined
                "
                v-model:current="data.pagination.current"
                size="small"
                show-quick-jumper
                :show-total="(total) => `${L('共')} ${total} ${L('条数据')}`"
                :total="data.pagination.total"
                :defaultPageSize="PAGE_SIZE"
                :pageSizeOptions="PAGE_SIZE_OPTIONS"
                @change="onPageChange"
              />
            </div>
          </div>
        </div>
      </Spin>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'SpellGroupOrder',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, reactive, onMounted, computed, watch } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import { message, RadioGroup, RadioButton, Spin, Empty, Pagination } from 'ant-design-vue';
  import { sucTip, failTip } from '@/utils/utils';
  import { list_com_page_size_10 } from '/@/utils/utils';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { PAGE_SIZE, PAGE_SIZE_OPTIONS } from '/@/components/Table/src/const.ts';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { getSpellOrderListApi } from '/@/api/promotion/spell_group';
  import { validatorEmoji } from '/@/utils/validate';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const router = useRouter();
  const route = useRoute();

  const pageSize = ref(list_com_page_size_10);
  const query = ref(route.query.id);
  const filter_code = ref(''); //过滤器默认值
  const loading = ref(false);
  const data = ref({});
  const params = ref({ pageSize: pageSize.value });
  const formValues = ref({}); //搜索条件

  const filter_data = ref([
    { filter_code: '', filter_name: L('全部订单') },
    { filter_code: '1', filter_name: L('拼团中') },
    { filter_code: '2', filter_name: L('已成团') },
    { filter_code: '3', filter_name: L('已失效') },
  ]);

  //表单
  const [registerForm, { validate, getFieldsValue }] = useForm({
    schemas: [
      {
        field: 'orderSn',
        component: 'Input',
        colProps: { span: 6, style: 'width:250px;max-width:100%;flex:none' },
        componentProps: {
          minWidth: 300,
          placeholder: L('请输入订单号'),
          size: 'default',
        },
        label: L('订单号'),
        labelWidth: 70,
        rules: [
          {
            // @ts-ignore
            validator: async (rule, value) => {
              await validatorEmoji(rule, value);
            },
            trigger: 'change',
          },
        ],
      },
      {
        field: 'memberName',
        component: 'Input',
        colProps: { span: 6, style: 'width:250px;max-width:100%;flex:none' },
        componentProps: {
          minWidth: 300,
          placeholder: L('请输入会员名称'),
          size: 'default',
        },
        label: L('会员名称'),
        labelWidth: 70,
        rules: [
          {
            // @ts-ignore
            validator: async (rule, value) => {
              await validatorEmoji(rule, value);
            },
            trigger: 'change',
          },
        ],
      },
      {
        field: 'goodsName',
        component: 'Input',
        colProps: { span: 6, style: 'width:250px;max-width:100%;flex:none' },
        componentProps: {
          minWidth: 300,
          placeholder: L('请输入商品名称'),
          size: 'default',
        },
        label: L('商品名称'),
        labelWidth: 70,
        rules: [
          {
            // @ts-ignore
            validator: async (rule, value) => {
              await validatorEmoji(rule, value);
            },
            trigger: 'change',
          },
        ],
      },
      {
        colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
        component: 'RangePicker',
        label: L('下单时间'),
        field: 'fieldTime',
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: [L('开始时间'), L('结束时间')],
        },
      },
      {
        component: 'Select',
        label: L('订单类型'),
        field: 'isVirtualGoods',
        labelWidth: 70,
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: L('请选择订单类型'),
          options: [
            { value: '', label: L('全部') },
            { value: '1', label: L('实物商品订单') },
            { value: '2', label: L('虚拟商品订单') },
          ],
        },
      },
    ],
    fieldMapToTime: [['fieldTime', ['startTime', 'endTime'], 'YYYY-MM-DD']],
    labelWidth: 80,
    baseColProps: { span: 6 },
    actionColOptions: { span: 24 },
    autoSubmitOnEnter: true,
    showAdvancedButton: true,
    submitFunc: search,
    resetFunc: reset,
  });

  //订单条件过滤器
  const clickFilter = (e) => {
    filter_code.value = e.target.value;
    let param = { pageSize: pageSize.value, current: 1, ...formValues.value };
    if (e.target.value) {
      param.state = e.target.value;
    }
    get_list(param);
  };

  async function search() {
    await validate();
    let values = getFieldsValue();
    values.startTime = values.startTime ? values.startTime.split(' ')[0] + ' 00:00:00' : undefined;
    values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
    for (let i in values) {
      if (values[i] == undefined) {
        delete values[i];
      }
    }
    formValues.value = values;
    get_list({ ...params.value, ...formValues.value });
  }

  //改变页码
  const onPageChange = (page, pageSize) => {
    let curParams = { pageSize: pageSize, current: page, ...formValues };
    if (filter_code.value) {
      curParams.state = filter_code.value;
    }
    params.value = curParams;
    get_list(curParams);
  };

  async function reset() {
    params.value.current = 1;
  }

  // 查看详情
  const goDetail = (item) => {
    router.push({
      path: '/marketing/spell_group_order_to_detail',
      query: {
        orderSn: item.orderSn,
      },
    });
  };

  //获取数据列表
  const get_list = async (param) => {
    loading.value = true;
    let res = await getSpellOrderListApi({ ...param, spellId: route.query.id });
    if (res.state == 200) {
      loading.value = false;
      if (param.current > 1 && res.data.list.length == 0 && params.value.current > 1) {
        param.current = param.current - 1;
        get_list(params);
      } else {
        data.value = res.data;
      }
    }
  };

  onMounted(() => {
    get_list({ pageSize: pageSize.value });
  });
</script>
<style lang="less">
  @import './style/order.less';
</style>
