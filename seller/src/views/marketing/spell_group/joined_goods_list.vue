<template>
  <div class="section_padding joined_goods_list">
    <div class="section_padding_back">
      <SldComHeader :title="L('活动商品')" :back="true" />
      <div class="com_line" style="height: 1px"></div>
      <BasicTable @register="registerTable" rowKey="goodsId">
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'goodsImage'">
            <div class="goods_info com_flex_row_flex_start">
              <div class="goods_img">
                <Popover placement="rightTop">
                  <template #content>
                    <div style="width: 200px;height: 200px;" class="flex_row_center_center">
                      <img :src="text" alt="" style="max-width: 100%; max-height: 100%" />
                    </div>
                  </template>
                  <div class="goods_img_wrap_80">
                    <img :src="text" alt="" />
                  </div>
                </Popover>
              </div>
              <div class="com_flex_column_space_between goods_detail">
                <span class="goods_name">
                  {{ record.goodsName }}
                </span>
              </div>
            </div>
          </template>
          <template v-if="column.dataIndex === 'action'">
            <TableAction
              :actions="[
                {
                  onClick: viewSpec.bind(
                    null,
                    { spellId: route.query?.id, goodsId: record.goodsId },
                    '1',
                  ),
                  label: L('查看团队'),
                },
                {
                  onClick: viewSpec.bind(null, record, '2'),
                  label: L('查看规格'),
                },
                {
                    label: L('删除'),
                    popConfirm: {
                      title: L('删除后不可恢复，是否确定删除？'),
                      placement: 'left',
                      confirm: operate.bind(null, record),
                    },
                  },
              ]"
            />
          </template>
        </template>
      </BasicTable>
    </div>
    <SldModal
      :width="900"
      :title="L('商品SKU')"
      :visible="modalVisible"
      :height="300"
      :content="operateContent"
      :showFoot="false"
      @cancle-event="handleModalCancle"
    />
  </div>
</template>
<script>
  export default {
    name: 'SpellJoinedGoodsList',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, reactive, onMounted } from 'vue';
  import {  Popover } from 'ant-design-vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { sucTip, failTip } from '@/utils/utils';
  import { useRoute, useRouter } from 'vue-router';
  import { getSpellGoodListApi,getSpellDelGoodsApi } from '/@/api/promotion/spell_group';
  import SldModal from '/@/components/SldModal/index.vue';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const route = useRoute();

  const router = useRouter();

  // 参数
  const id = ref(route.query?.id);

  const modalVisible = ref(false);

  const operateContent = ref([
    {
      type: 'show_content_table', //展示表格，没有分页，不能勾选
      name: 'bind_goods',
      width: 880,
      wrapperCol: { flex: 1, style: 'margin-bottom:-19px' },
      label: ``,
      content: '',
      data: [],
      scrollHeight: false, // 设置纵向滚动，也可用于指定滚动区域的高，可以设置为像素值
      columns: [
        {
          title: ' ',
          align: 'center',
          width: 100,
          minWidth: 100,
          customRender: ({ text, record, index }) => {
            return `${index + 1}`;
          },
        },
        {
          title: L('SKU规格'),
          dataIndex: 'specValues',
          align: 'center',
          width: 200,
          customRender: ({ text }) => {
            return text ? text : L('默认');
          },
          minWidth: 200,
        },
        {
          title: L('原价(￥)'),
          dataIndex: 'productPrice',
          align: 'center',
          width: 200,
          minWidth: 200,
        },
        {
          title: L('商品库存'),
          dataIndex: 'stock',
          align: 'center',
          width: 200,
          minWidth: 200,
        },
        {
          title: L('拼团价(￥)'),
          dataIndex: 'spellPrice',
          align: 'center',
          width: 200,
          minWidth: 200,
        },
        {
          title: L('团长优惠(￥)'),
          dataIndex: 'leaderPrice',
          align: 'center',
          width: 200,
          minWidth: 200,
        },
        {
          title: L('拼团库存'),
          dataIndex: 'spellStock',
          align: 'center',
          width: 200,
          minWidth: 200,
        },
      ],
      rowKey: 'goodsId',
      scroll: false,
    },
  ]);

  const searchInfo = ref({
    spellId: id.value,
  });
  const columns = reactive({
    data: [
      {
        title: L('商品信息'),
        dataIndex: 'goodsImage',
        align: 'center',
        width: 180,
      },
      {
        title: L('原价(￥)'),
        dataIndex: 'productPrice',
        align: 'center',
        width: 100,
      },
      {
        title: L('拼团价(￥)'),
        dataIndex: 'spellPrice',
        align: 'center',
        width: 100,
      },
      {
        title: L('活动团队'),
        dataIndex: 'spellTeamNum',
        align: 'center',
        width: 100,
      },
    ],
  });

  const searchFormSchema = reactive({
    data: [
      {
        component: 'Input',
        label: L('商品名称'),
        field: 'goodsName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: L('请输入商品名称'),
        },
      },
    ],
  });

  // 表格数据
  const [registerTable,{reload}] = useTable({
    // 请求接口
    api: getSpellGoodListApi,
    // 表头
    columns: columns.data,
    immediate: true,
    // 参数
    searchInfo: searchInfo.value,
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: searchFormSchema.data,
    },
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    ellipsis: false,
    // 是否显示序号列
    showIndexColumn: true,
    // 表格右侧操作列配置
    actionColumn: {
      width: 160,
      title: L('操作'),
      dataIndex: 'action',
      // slots: { customRender: 'action' },
    },
  });

  const viewSpec = (record, type) => {
    if (type == 1) {
      router.push({
        path: `/marketing/spell_group_team_list`,
        query: record,
      });
    } else if (type == 2) {
      operateContent.value[0].data = [];
      operateContent.value[0].data = record.productList;
      modalVisible.value = true;
    }
  };

  //关闭弹框
  const handleModalCancle = () => {
    modalVisible.value = false;
    operateContent.value[0].data = [];
  };

  // 删除
  const operate = async(record)=> {
    let param_data = {};
    param_data.goodsId = record.goodsId
    param_data.spellId = route.query?.id
    let res = await getSpellDelGoodsApi(param_data)
    if(res.state == 200){
      sucTip(res.msg)
      reload()
    }else{
      failTip(res.msg)
    }
  }
  onMounted(() => {});
</script>
<style lang="less" scoped>
  .joined_goods_list {
    .goods_info {
      position: relative;

      .virtual_goods_flag {
        position: absolute;
        top: 1px;
        left: 1px;
        padding: 0 3px;
        border-radius: 3px 0;
        background: linear-gradient(90deg, #f7d47e 0%, #e6b845 100%);
        color: #fff;
        font-size: 12px;
      }

      .goods_detail {
        flex: 1;
        width: 169px;
        height: 80px;
        margin-left: 10px;
      }

      .goods_img {
        display: inline-block;
        width: 80px;
        height: 80px;
        overflow: hidden;
        border: 1px solid rgb(226 229 246 / 100%);
        border-radius: 3px;
        background: rgb(248 248 248 / 100%);
      }

      .goods_name {
        display: -webkit-box;
        flex-shrink: 0;
        height: 34px;
        overflow: hidden;
        color: #333;
        font-size: 13px;
        line-height: 17px;
        text-align: left;
        text-overflow: ellipsis;
        word-break: break-word;
        -webkit-line-clamp: 2;

        /*! autoprefixer: off */
        -webkit-box-orient: vertical;
      }

      .goods_brief {
        display: inline-block;
        width: 100%;
        overflow: hidden;
        color: #666;
        font-size: 12px;
        text-align: left;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  p {
    margin-bottom: 0;
  }
</style>
