<template>
  <div class="section_padding spell_team_list">
    <div class="section_padding_back">
      <SldComHeader :title="L('活动团队')" :back="true" />
      <div class="com_line" style="height: 1px"></div>
      <BasicTable @register="registerTable" rowKey="couponId">
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'memberName'">
            <div class="spell_group_team_wrap flex_row_center_center">
              <div
                class="member flex_column_start_center"
                v-for="(item, index) in record.memberList"
                :key="index"
              >
                <div
                  class="avatar"
                  :style="{ backgroundImage: 'url(' + item.memberAvatar + ')' }"
                ></div>
                <span
                  class="name"
                  :style="{
                    background: index == 0 ? 'rgba(255, 106, 18, 1)' : 'rgba(255, 210, 183, 1)',
                  }"
                  :title="index == 0 ? L('团长：') + item.memberName : item.memberName"
                >
                  {{ item.memberName }}
                </span>
              </div>
            </div>
          </template>
        </template>
      </BasicTable>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'SpellGroupTeamList',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, reactive, onMounted } from 'vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { useRoute } from 'vue-router';
  import { getSpellTeamListApi } from '/@/api/promotion/spell_group';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const route = useRoute();

  // 参数
  const id = ref(route.query?.spellId);

  const searchInfo = ref({
    spellId: id.value,
    goodsId: route.query?.goodsId,
  });
  const columns = reactive({
    data: [
      {
        title: L('开团时间'),
        dataIndex: 'createTime',
        align: 'center',
        width: 180,
      },
      {
        title: L('成团结束时间'),
        dataIndex: 'endTime',
        align: 'center',
        width: 100,
      },
      {
        title: L('拼团人数'),
        dataIndex: 'joinedNum',
        align: 'center',
        width: 100,
      },
      {
        title: L('会员'),
        dataIndex: 'memberName',
        align: 'center',
        width: 100,
      },
      {
        title: L('状态'),
        dataIndex: 'stateValue',
        align: 'center',
        width: 100,
      },
    ],
  });

  const searchFormSchema = reactive({
    data: [
      {
        component: 'Select',
        label: L('拼团状态'),
        field: 'state',
        labelWidth: 90,
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: L('请选择拼团状态'),
          options: [
            { value: '', label: L('全部状态') },
            { value: '1', label: L('进行中') },
            { value: '2', label: L('拼团成功') },
            { value: '3', label: L('拼团失败') },
          ],
        },
      },
    ],
  });

  // 表格数据
  const [registerTable] = useTable({
    // 请求接口
    api: getSpellTeamListApi,
    // 表头
    columns: columns.data,
    immediate: true,
    // 参数
    searchInfo: searchInfo.value,
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: searchFormSchema.data,
    },
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    ellipsis: false,
    // 是否显示序号列
    showIndexColumn: true,
  });
  onMounted(() => {});
</script>
<style lang="less" scoped>
  .spell_team_list {
    .spell_group_team_wrap {
      .member {
        position: relative;
        flex-shrink: 0;
        width: 60px;
        height: 31px;
        margin: 0 10px 10px;
        border-radius: 50%;

        .avatar {
          width: 31px;
          height: 31px;
          overflow: hidden;
          border: 1px solid #eee;
          border-radius: 50%;
          background-position: center;
          background-size: cover;
        }

        .name {
          position: absolute;
          z-index: 2;
          bottom: -12px;
          width: 60px;
          height: 14px;
          padding: 0 5px;
          overflow: hidden;
          border-radius: 7px;
          background: #ff711e;
          color: #fff;
          font-size: 12px;
          line-height: 14px;
          text-align: center;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }

  p {
    margin-bottom: 0;
  }
</style>
