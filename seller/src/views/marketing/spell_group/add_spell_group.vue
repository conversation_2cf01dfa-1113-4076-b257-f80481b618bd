<template>
  <div class="section_padding add_seckill seckill">
    <div class="section_padding_back full_activity">
      <SldComHeader :title="L('拼团活动')" />
      <div class="com_line" style="height: 1px"></div>
      <Spin :spinning="loading">
        <div class="add_coupon_height">
          <Form layout="inline" ref="formRef" :model="detail">
            <!-- 活动基本信息 start -->
            <CommonTitle :text="L('活动基本信息')" style="margin-top: 10px" />
            <div class="full_acm_activity flex_column_start_start">
              <!-- 活动名称 start -->
              <div class="item flex_row_start_start">
                <div class="left"><span style="color: red">*</span>{{ L('活动名称') }} </div>
                <div class="right">
                  <Form.Item
                    :validateFirst="true"
                    :extra="L('最多输入20个字')"
                    name="spellName"
                    style="width: 400px"
                    :rules="[
                      {
                        required: true,
                        whitespace: true,
                        message: L('请输入活动名称'),
                      },
                      {
                        validator: async (rule, value) => {
                          await validatorEmoji(rule, value);
                        },
                      },
                    ]"
                  >
                    <Input
                      :maxLength="20"
                      style="width: 400px !important"
                      :placeholder="L('请输入活动名称')"
                      v-model:value="detail.spellName"
                    />
                  </Form.Item>
                </div>
              </div>
              <!-- 活动名称 end -->
               <!-- 活动时间 start -->
               <div class="item flex_row_start_start">
                <div class="left"><span style="color: red">*</span>{{ L('活动时间') }} </div>
                <div class="right">
                  <Form.Item
                    name="activityTime"
                    style="width: 400px"
                    :rules="[
                      {
                        required: true,
                        message: L('请选择活动时间'),
                      },
                    ]"
                  >
                    <RangePicker
                      style="width: 400px"
                      :format="'YYYY-MM-DD HH:mm:00'"
                      v-model:value="detail.activityTime"
                      :disabledDate="
                        (currentDate) => currentDate && currentDate < moment().subtract(1, 'days')
                      "
                      :showTime="{ format: 'HH:mm' }"
                      :placeholder="[L('开始时间'), L('结束时间')]"
                      :getPopupContainer="
                        (triggerNode) => {
                          return triggerNode.parentNode;
                        }
                      "
                    />
                  </Form.Item>
                </div>
              </div>
              <!-- 活动时间 end -->
              <!-- 活动标签 start -->
              <div class="item flex_row_start_center">
                <div class="left"> <span style="color: red">*</span>{{ L('活动标签') }}</div>
                <div class="right">
                  <Form.Item name="spellLabelId" style="width: 400px" :rules="[{
                    required: true,
                    message: L('请选择活动标签'),
                  }]">
                    <Select
                      :placeholder="L('请选择活动标签')"
                      style="width: 400px"
                      :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                      v-model:value="detail.spellLabelId"
                    >
                      <Select.Option
                        v-for="(item, index) in activity_labels"
                        :key="index"
                        :value="item.spellLabelId"
                        >{{ item.spellLabelName }}</Select.Option
                      >
                    </Select>
                  </Form.Item>
                </div>
              </div>
              <!-- 活动标签 end -->
              <!-- 参团人数 start -->
              <div class="item flex_row_start_start">
                <div class="left"><span style="color: red">*</span>{{ L('参团人数') }} </div>
                <div class="right">
                  <Form.Item
                    :extra="L('请填写2～100的数字')"
                    name="requiredNum"
                    class="requiredNum_box"
                    style="width: 150px"
                    :rules="[
                      {
                        required: true,
                        message: L('请输入参团人数'),
                      },
                    ]"
                  >
                    <div class="flex_row_start_center">
                      
                      <InputNumber :min="2" :max="100" :precision="0" style="width: 130px;" v-model:value="detail.requiredNum"></InputNumber>
                      <span :style="{
                            display: 'inline-block',
                            marginLeft: '5px',
                            color: 'rgba(0, 0, 0, 0.65)',
                          }">{{ L('人') }}</span>
                    </div>
                  </Form.Item>
                </div>
              </div>
              <!-- 参团人数 end -->
              <!-- 拼团有效期 start -->
              <div class="item flex_row_start_start">
                <div class="left"><span style="color: red">*</span>{{ L('拼团有效期') }} </div>
                <div class="right">
                  <Form.Item
                    :extra="L('设置拼团的有效时间，用户开团后，需要在设置的时间内拼购指定人数，否则拼团失败')"
                    name="cycle"
                    class="cycle_box"
                    style="width: 330px"
                    :rules="[
                      {
                        required: true,
                        message: L('请输入拼团有效期'),
                      },
                    ]"
                  >
                    <div class="flex_row_start_center">
                      
                      <InputNumber :min="1" :max="23*9" :precision="0" style="width: 130px !important;" v-model:value="detail.cycle"></InputNumber>
                      <span :style="{
                            display: 'inline-block',
                            marginLeft: '5px',
                            color: 'rgba(0, 0, 0, 0.65)',
                          }">{{ L('小时') }}</span>
                    </div>
                  </Form.Item>
                </div>
              </div>
              <!-- 拼团有效期 end -->
              <!-- 限购数量 start -->
              <div class="item flex_row_start_start">
                <div class="left"><span style="color: red">*</span>{{ L('限购数量') }} </div>
                <div class="right">
                  <Form.Item
                    :extra="L('每位会员限制购买的数量，0代表不限制')"
                    name="buyLimit"
                    class="buyLimit_box"
                    style="width: 300px"
                    :rules="[
                      {
                        required: true,
                        message: L('请输入限购数量'),
                      },
                    ]"
                  >
                    <div class="flex_row_start_center">
                      
                      <InputNumber :min="0" :max="999" :precision="0" style="width: 150px !important;" v-model:value="detail.buyLimit" :placeholder="L('请输入限购数量')"></InputNumber>
                    </div>
                  </Form.Item>
                </div>
              </div>
              <!-- 限购数量 end -->
              <!-- 模拟成团 start -->
              <div class="item flex_row_start_start">
                <div class="left">{{ L('模拟成团') }} </div>
                <div class="right">
                  <Form.Item
                    :extra="L('开启模拟成团后，拼团有效期内人数未满的团，可以点击模拟成团按钮，使该团成团。你只需要对已付款参团的真实买家发货。建议合理开启，以提高成团率')"
                    name="isSimulateGroup"
                    style="width: 450px"
                  >
                   <checkbox class="" v-model:checked="detail.isSimulateGroup">
                    {{ L('开启') }}
                   </checkbox>
                  </Form.Item>
                </div>
              </div>
              <!-- 模拟成团 end -->
              <!-- 团长优惠 start -->
              <div class="item flex_row_start_start">
                <div class="left">{{ L('团长优惠') }} </div>
                <div class="right">
                  <Form.Item
                    :extra="L('开启团长(开团人)优惠后，团长将享受更优惠价格，有助于提高开团率和成团率。请注意：模拟成团的团长也能享受团长优惠，请谨慎设置，避免资金损失')"
                    name="leaderIsPromotion"
                    style="width: 450px"
                  >
                   <checkbox class="" v-model:checked="detail.leaderIsPromotion" @change="handleLeaderPromotion($event)">
                    {{ L('开启') }}
                   </checkbox>
                  </Form.Item>
                </div>
              </div>
              <!-- 团长优惠 end -->
            </div>
            <!-- 活动基本信息 end -->
            <CommonTitle style="margin-top: 10px" :describe="L('提醒：已参加活动的商品不可参与该活动')" describeColor="#FF7F40">
              <template #button>
                  <div class="toolbar_btn" @click="addGoods">
                    <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" :fillColor="'rgb(255, 89, 8)'" />
                    <span style="margin-left: 4px">{{ L('添加活动商品') }}</span>
                  </div>
                </template>
            </CommonTitle>
            <div class="sele_goods" v-for="(item,indexs) in detail.selectedRows" :key="indexs">
              <img :src="del_seckill_goods" alt="" class="del_spu" @click="delSpu(item.goodsId)">
              <div class="goods_info flex_row_between_start">
                <div class="goods_info_left flex_row_start_start">
                  <div class="goods_img_wrap flex_row_center_center">
                    <img :src="item.goodsImage" alt="" class="goods_img">
                  </div>
                  <p class="goods_name">{{ item.goodsName }}</p>
                </div>
                <div class="goods_info_right flex_row_end_end">
                  <Popconfirm @confirm="(e) => {
                              batchConfirm(e, 'spellPrice', item,indexs);
                            }">
                    <template #title>
                      <InputNumber :min="0.01" :max="9999999" :precision="2" style="width: 100%;" v-model:value="battchVal" :placeholder="L('批量设置拼团价')"  @change="(e) => {handleFieldBattchChange(e, 'spellPrice', item)}"></InputNumber>
                    </template>
                    <div class="batch_btn flex_row_center_center">
                      {{ L('设置拼团价') }}
                    </div>
                  </Popconfirm>
                  <Popconfirm v-if="leaderPromotion==1" @confirm="(e) => {
                              batchConfirm(e, 'leaderPrice', item,indexs);
                            }">
                    <template #title>
                      <InputNumber :min="0.01" :max="9999999" :precision="2" style="width: 100%;" v-model:value="battchVal" :placeholder="L('批量设置团长优惠价')"  @change="(e) => {handleFieldBattchChange(e, 'leaderPrice', item)}"></InputNumber>
                    </template>
                    <div class="batch_btn flex_row_center_center">
                      {{ L('设置团长优惠价') }}
                    </div>
                  </Popconfirm>
                  <Popconfirm @confirm="(e) => {
                              batchConfirm(e, 'spellStock', item,indexs);
                            }">
                    <template #title>
                      <InputNumber :min="1" :max="99999999" :precision="0" style="width: 100%;" v-model:value="battchVal"  :placeholder="L('批量设置拼团库存')" @change="(e) => {handleFieldBattchChange(e, 'spellStock', item)}"></InputNumber>
                    </template>
                    <div class="batch_btn flex_row_center_center">
                      {{ L('设置拼团库存') }}
                    </div>
                  </Popconfirm>
                  <div class="batch_btn flex_row_center_center">
                    <Checkbox @change="setAll($event, item)">
                      <span class="sel_all">{{ L('全部参与') }}</span>
                    </Checkbox>
                  </div>
                </div>
              </div>
              <BasicTable
                rowKey="productId"
                :columns="columns_spec"
                :dataSource="item.productList"
                size="'small'"
                :pagination="false"
                :bordered="true"
                :ellipsis="false"
                :canResize="false"
              >
                <template #headerCell="{ column }">
                  <template v-if="column.dataIndex == 'spellPrice'">
                    <span><span style="color: #FF2929;font-size:13px;">*</span>{{ L('拼团价(¥)') }}</span>
                  </template>
                  <template v-else-if="column.dataIndex == 'spellStock'">
                    <span><span style="color: #FF2929;font-size:13px;">*</span>{{ L('拼团库存') }}</span>
                  </template>
                  <template v-else-if="column.dataIndex == 'leaderPrice'">
                    <span><span style="color: #FF2929;font-size:13px;">*</span>{{ L('团长优惠价(¥)') }}</span>
                  </template>
                  <template v-else>
                    {{ column.customTitle }}
                  </template>
                </template>
                <template #bodyCell="{ column, text, record,index }">
                  <template v-if="column.dataIndex === 'spellPrice'">
                    <Form.Item
                      :name="['selectedRows', indexs, 'productList',index,'spellPrice']"
                      :rules="record.state == 1?[
                        {
                          required: true,
                          message: L('该项必填'),
                        },
                      ]:[]"
                    >
                      <InputNumber
                        :max="9999999"
                        :min="0.01"
                        style="width: 400px !important"
                        :precision="2"
                        @change="(e) => {handleFieldBlur(e, 'spellPrice', record)}"
                        v-model:value="record.spellPrice"
                      />
                    </Form.Item>
                  </template>
                  <template v-if="column.dataIndex === 'spellStock'">
                    <Form.Item
                      :name="['selectedRows', indexs, 'productList', index, 'spellStock']"
                      :rules="record.state == 1?[
                        {
                          required: true,
                          message: L('该项必填'),
                        },
                      ]:[]"
                    >
                      <InputNumber
                        :max="9999999"
                        :min="1"
                        style="width: 400px !important"
                        :precision="0"
                        @change="(e) => {handleFieldBlur(e, 'spellStock', record)}"
                        v-model:value="record.spellStock"
                      />
                    </Form.Item>
                  </template>
                  <template v-if="column.dataIndex === 'leaderPrice'">
                    <Form.Item
                      :name="['selectedRows', indexs, 'productList', index, 'leaderPrice']"
                      :rules="record.state == 1?[
                        {
                          required: true,
                          message: L('该项必填'),
                        },
                      ]:[]"
                    >
                      <InputNumber
                        :max="99999999"
                        :min="0.01"
                        style="width: 400px !important"
                        :precision="2"
                        @change="(e) => {handleFieldBlur(e, 'leaderPrice', record)}"
                        v-model:value="record.leaderPrice"
                      />
                    </Form.Item>
                  </template>
                  <template v-if="column.dataIndex === 'state'">
                    <Switch
                      @change="
                        (e) =>
                        handleFieldChange(e ? 1 : 2, 'state', record,index,indexs)
                      "
                      :checked="text == 1 ? true : false"
                    />
                  </template>
                </template>
              </BasicTable>
            </div>
            <div style="width: 100%; height: 60px; background: #fff"></div>
          </Form>
        </div>
        <div
          class="m_diy_bottom_wrap"
          :style="{ position: 'fixed', left: getRealWidth + 10 + 'px' }"
        >
          <div class="add_goods_bottom_btn" @click="goBack"> {{ L('返回') }} </div>
          <div class="add_goods_bottom_btn add_goods_bottom_btn_sel" @click="handleSaveAllData">
            {{ L('保存') }}
          </div>
        </div>
      </Spin>
    </div>
    <SldSelMoreLeftRightSeckillGoods 
      @confirm-event="seleGoods"
      @cancle-event="sldHandleCancle" 
      :width="1000"
      :selectedRow="sele_more_goods.info"
      :selectedRowKey="sele_more_goods.ids"
      :title="L('请选择商品(至少选择一个)')"
      :height='height - 400'
      :modalVisible="modalVisibleGoods"
      :extra="sele_more_goods"
      >

    </SldSelMoreLeftRightSeckillGoods>
  </div>
</template>
<script setup>
  import { getCurrentInstance, ref, reactive, onMounted } from 'vue';
  import moment from 'moment';
  import dayjs from 'dayjs';
  import {  Spin,Select,Form,Popconfirm,InputNumber,Checkbox,Switch,Input,RangePicker } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import CommonTitle from '/@/components/CommonTitle/index.vue';
  import { validatorEmoji } from '/@/utils/validate';
  import SldSelMoreLeftRightSeckillGoods from '/@/components/SldSelMoreLeftRightSeckillGoods/index.vue';
  import { BasicTable } from '/@/components/Table';
  import del_seckill_goods from '/src/assets/images/del_seckill_goods.png';
  import { getSpellLabelListApi,getSpellAddApi,getSpellUpdateApi,getSpellDetailApi } from '/@/api/promotion/spell_group';
  import { useRoute, useRouter } from 'vue-router';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import { useUserStore } from '/@/store/modules/user';
  import { list_com_page_more,sucTip, failTip } from '/@/utils/utils';
  import { pageClose } from '/@/utils/utils';
  import { useMultipleTabStore } from '/@/store/modules/multipleTab';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const loading = ref(false);
  const tabStore = useMultipleTabStore();
  const route = useRoute();
  const router = useRouter();
  const userStore = useUserStore();
  const { getRealWidth } = useMenuSetting();
  const leaderPromotion = ref(0)//团长优惠是否开启，默认为0 未开启，1为开启

  const battchVal = ref('');//批量设置里面的值
  const activity_stages = ref([])//活动场次
  const activity_labels = ref([])//活动标签
  const modalVisibleGoods = ref(false);
  const query = ref(route.query);
  const selectedRows = ref([])
  const selectedRowKeys = ref([])//selectedRows的key
  const formRef = ref();
  const height = ref(document.body.clientHeight)
  const sele_more_goods = ref({
    info: [],//选择的商品数组
    ids: [],//选择的商品id数组
    min_num: 1,//最小数量，0为不限制
  })
  const detail = ref({
    selectedRows:[],
    selectedRowKeys:[]
  })

  const columns_spec_spellPrice = ref({
      title: L('团长优惠价(¥)'),
      dataIndex: 'leaderPrice',
      align: 'center',
      width: 100,
  })

  const columns_spec = ref([
    {
      title: L('SKU规格'),
      dataIndex: 'specValues',
      align: 'center',
      width: 100,
      customRender: ({ text, record, index }) => {
        return text ? text : L('默认');
      },
    },
    {
      title: L('原价(¥)'),
      dataIndex: 'productPrice',
      align: 'center',
      width: 100,
    },
    {
      title: L('库存'),
      dataIndex: 'productStock',
      align: 'center',
      width: 100,
    },
    {
      title: L('拼团价(¥)'),
      dataIndex: 'spellPrice',
      align: 'center',
      width: 100,
    },
    {
      title: L('拼团库存'),
      dataIndex: 'spellStock',
      align: 'center',
      width: 100,
    },
    {
      title: L('参与'),
      dataIndex: 'state',
      align: 'center',
      width: 100,
    },
  ])

  // 获取详情
  const get_detail = async(id)=> {
    let res = await getSpellDetailApi({ spellId: id })
    if(res.state == 200){
      loading.value = false
      res.data.selectedRowKeys = []
      res.data.selectedRows = []
      if (res.data.startTime) {
        res.data.activityTime = [
          dayjs(res.data.startTime, 'YYYY-MM-DD HH:mm:ss'),
          dayjs(res.data.endTime, 'YYYY-MM-DD HH:mm:ss'),
        ];
      }
      leaderPromotion.value = res.data.leaderIsPromotion;//团长是否有优惠
      initColumn(leaderPromotion.value)
      let goodsList = res.data.goodsList;
      goodsList.forEach(item=>{
        res.data.selectedRowKeys.push(item.goodsId);
        let productList = [];
        item.productList.forEach(child=>{
          productList.push({
            goodsId:item.goodsId,
            productId:child.productId,
            productPrice:child.productPrice,
            productStock:child.stock,
            specValues:child.specValues,
            spellPrice:child.spellPrice,
            spellStock:child.spellStock,
            leaderPrice:child.leaderPrice==0?0.01:child.leaderPrice,
            state:1,
          });
        })
        res.data.selectedRows.push({
          goodsId:item.goodsId,
          goodsImage:item.goodsImage,
          goodsName:item.goodsName,
          productList:productList,
        });
        sele_more_goods.value.ids = [...res.data.selectedRowKeys];
        sele_more_goods.value.info = JSON.parse(JSON.stringify(res.data.selectedRows));
      });
      if(route.query.tar == 'copy'){
        //复制功能需要部分内容
        res.data.spellName = '';//清空活动名称
        //清空活动时间
        res.data.startTime = '';
        res.data.endTime = '';
      }
      detail.value = res.data;
    }else{
      failTip(res.msg)
    }
  }


  //获取活动标签
  const get_activity_label = async(id)=> {
    let res = await getSpellLabelListApi({ pageSize: list_com_page_more })
    if(res.state == 200){
      activity_labels.value = res.data
    }
  }

  // 打开商品弹窗
  const addGoods = ()=> {
    modalVisibleGoods.value = true
  }

  // 关闭弹窗
  const sldHandleCancle = ()=>{
    modalVisibleGoods.value = false
  }

  // 弹窗点击确定
  const seleGoods = (selectedRowsP,selectedRowKeysP)=>{
    selectedRowsP.map((item, index) => {
      item.productList.map((child_item) => {
        child_item.goodsId = item.goodsId;
        child_item.state = 1;
      });
    });
    //如果多次选择的话，数据要保留之前的
    detail.value.selectedRowKeys.map((item) => {
      if (selectedRowKeysP.indexOf(item) > -1) {
        let pre_item_data = detail.value.selectedRows.filter(val => val.goodsId == item)[0];
        for (let i = 0; i < selectedRowsP.length; i++) {
          if (selectedRowsP[i].goodsId == item) {
            selectedRowsP[i] = { ...pre_item_data };
            break;
          }
        }
      }
    });
    sele_more_goods.value.ids = [...selectedRowKeysP];
    sele_more_goods.value.info = JSON.parse(JSON.stringify(selectedRowsP));
    detail.value.selectedRows = selectedRowsP
    detail.value.selectedRowKeys = selectedRowKeysP
    sldHandleCancle()
  }

  // 商品批量设置
  const batchConfirm = (e, type, val,indexs)=> {
    let cur_goods = detail.value.selectedRows.filter(item=>item.goodsId == val.goodsId)[0];
    if(!battchVal.value){
      return false
    }
    //数据验证
    for(let i=0;i<cur_goods.productList.length;i++){
      let item_data = cur_goods.productList[i];
      let specValuesName = item_data.specValues?item_data.specValues:L('默认规格')
      if(type == 'spellStock'&&battchVal.value>item_data.productStock){
        //库存不能超过所有sku里面的最小库存
        battchVal.value = item_data.productStock;
      }

      if(type == 'spellPrice'){
        //拼团价必须小于原价格
        if(battchVal.value>=item_data.productPrice){
          failTip(`${specValuesName}${L('的拼团价必须小于商品原价')}`);
          battchVal.value = ''
          return false;
        }else if(leaderPromotion.value == 1&&item_data.leaderPrice&&battchVal.value<=item_data.leaderPrice){
          //如果开启团长优惠，拼团价必须高于团长优惠价
          failTip(`${specValuesName}${L('的拼团价必须高于团长优惠价')}`);
          battchVal.value = ''
          return false;
        }
      }

      if(type == 'leaderPrice'){
        if(item_data.spellPrice&&battchVal.value>=item_data.spellPrice){
          //团长优惠价必须小于拼团价
          failTip(`${specValuesName}${L('的团长优惠价必须小于拼团价')}`);
          battchVal.value = ''
          return false;
        }else if(battchVal.value>=item_data.productPrice){
          //团长优惠价必须小于商品原价
          failTip(`${specValuesName}${L('的团长优惠价必须小于商品原价')}`);
          battchVal.value = ''
          return false;
        }
      }
    }

    let sku_product_id = [];
    cur_goods.productList.map((item,index) => {
      item[type] = battchVal.value;
      sku_product_id.push(item.productId);
      formRef.value.clearValidate([['selectedRows', indexs, 'productList',index,type]]);
    });
    battchVal.value = ''
   
  }

  //批量设置
  const handleFieldBattchChange = (e, type, item)=> {
    battchVal.value = e
  }

  //全部参与事件
  const setAll = (e, val) => {
    let states = [];
    for (let i in detail.value.selectedRows) {
      if (detail.value.selectedRows[i].goodsId == val.goodsId) {
        detail.value.selectedRows[i].productList.map(item => {
          item.state = e.target.checked ? 1 : 0;
          states.push('state'+item.productId);
        });
        break;
      }
    }
  };

  const handleFieldBlur = (val, fieldName, record)=>{
    handleFiledContent(val*1,fieldName, record);
  }

  const handleFieldChange = (val, fieldName, record,index,indexs)=> {
    handleFiledContent(val, fieldName, record);
    formRef.value.clearValidate([['selectedRows', indexs, 'productList',index,'spellPrice'],['selectedRows', indexs, 'productList',index,'spellStock']]);
    if(leaderPromotion.value==1){
      formRef.value.clearValidate([['selectedRows', indexs, 'productList',index,'leaderPrice']]);
    }
  }
  
  const handleLeaderPromotion = (e)=> {
    leaderPromotion.value = e.target.checked?1:0;
    initColumn(leaderPromotion.value)
  }

  //初始化column，leaderPromotion为true则显示团长优惠价，否则不显示
  const initColumn = (leaderPromotion)=> {
    columns_spec.value = columns_spec.value.filter(item=>item.dataIndex!='leaderPrice');
    if(leaderPromotion){
      let j = 0
      //开启团长优惠，商品信息需要显示团长优惠价
      for(let i=0; i< columns_spec.value.length;i++){
        if(columns_spec.value[i].dataIndex == 'spellPrice'){
          j = i
        }
      }
      columns_spec.value.splice(j + 1, 0,columns_spec_spellPrice.value)
      detail.value.selectedRows.forEach((item,index)=>{
        item.productList.forEach((it,ind)=>{
          formRef.value.clearValidate([['selectedRows', index, 'productList',ind,'spellPrice'],['selectedRows', index, 'productList',ind,'spellStock']]);
          if(leaderPromotion.value==1){
            formRef.value.clearValidate([['selectedRows', index, 'productList',ind,'leaderPrice']]);
          }
        })
      })
    }
  }

  //spec_data_table 表格编辑事件
  const handleFiledContent = (val, fieldName, record,index,indexs)=> {
    //拼团库存不能超过商品库存
    if (fieldName == 'spellStock' && val > record.productStock) {
      val = record.productStock;
    }

    //拼团价必须小于原价格
    if (fieldName == 'spellPrice'&&val) {
      if(val>=record.productPrice){
        val='';
        failTip(`${L('拼团价必须小于商品原价')}`);
      }else if(leaderPromotion.value == 1&&record.leaderPrice&&val<=record.leaderPrice){
        //如果开启团长优惠，拼团价必须高于团长优惠价
        val='';
        failTip(`${L('拼团价必须高于团长优惠价')}`);
      }
    }

    if(fieldName == 'leaderPrice'&&val){
      if(record.spellPrice&&val>=record.spellPrice){
        //团长优惠价必须小于拼团价
        val='';
        failTip(`${L('团长优惠价必须小于拼团价')}`);
      }else if(val>=record.productPrice){
        //团长优惠价必须小于商品原价
        val='';
        failTip(`${L('团长优惠价必须小于商品原价')}`);
      }
    }
    let tar_sku_list = detail.value.selectedRows.filter(item => item.goodsId == record.goodsId);
    if (tar_sku_list.length > 0) {
      let tar_data = tar_sku_list[0].productList.filter(item => item.productId == record.productId);
      if (tar_data.length > 0) {
        tar_data[0][fieldName] = val;
      }
    }
  }

  //删除添加的商品spu
  const delSpu = (goodsId) => {
    detail.value.selectedRows = detail.value.selectedRows.filter(item => item.goodsId != goodsId);
    detail.value.selectedRowKeys = detail.value.selectedRowKeys.filter(item => item != goodsId);
    sele_more_goods.value.ids = [...detail.value.selectedRowKeys];
    sele_more_goods.value.info = JSON.parse(JSON.stringify(detail.value.selectedRows));
  };

  // 返回
  const goBack = () => {
    if (window.history && window.history.length == 1) {
      pageClose();
    } else {
      const { fullPath } = route;
      tabStore.closeTabByKey(fullPath, router);
      router.back();
    }
  };

  // 保存
  const handleSaveAllData = async()=> {
    try {
      formRef.value.validate().then(async (values) => {
        let params = {};
        //秒杀商品结束时间
        if (values.activityTime) {
          params.startTime = values.activityTime[0].format('YYYY-MM-DD HH:mm:00');
          params.endTime = values.activityTime[1].format('YYYY-MM-DD HH:mm:00');
        }
        //活动开始时间必须小于结束时间
        if(Date.parse(params.startTime) >= Date.parse(params.endTime)){
          failTip(L('活动开始时间必须小于结束时间'));
          return false;
        }
        //拼团商品
        let goodsList = [];
        detail.value.selectedRows.forEach((item) => {
          item.productList.forEach((child) => {
            if (child.state == 1) {
              goodsList.push({
                productId: child.productId,
                spellPrice: child.spellPrice,
                leaderPrice: leaderPromotion.value == 1?(child.leaderPrice?child.leaderPrice:0.01):'',
                spellStock: child.spellStock,
              });
            }
          });
        });
        if (goodsList.length == 0) {
          failTip(L('请选择要参与活动的商品'));
          return false;
        }

        let dis_type = '';
        params.goodsList = JSON.stringify(goodsList);
        params.buyLimit = values.buyLimit;// 活动最大限购数量；0为不限购
        params.cycle = values.cycle;// 成团周期（开团-截团时长），单位：小时
        params.requiredNum = values.requiredNum;// 要求成团人数
        params.spellName = values.spellName;// 拼团活动名称
        params.spellLabelId = values.spellLabelId;// 活动标签id
        params.isSimulateGroup = values.isSimulateGroup!=undefined&&values.isSimulateGroup?1:0;// 	是否模拟成团(0-关闭/1-开启）
        params.leaderIsPromotion = leaderPromotion.value;// 团长是否有优惠(0-没有/1-有）
        loading.value = true
        let res;
        if(route.query.id != undefined && Number(route.query.id) > 0 && route.query.tar == 'edit'){
          params.spellId = route.query.id;// 活动id
          res = await getSpellUpdateApi(params)
        }else{
          res = await getSpellAddApi(params)
        }
        if(res.state == 200){
          loading.value = false
          sucTip(res.msg);
          userStore.setDelKeepAlive([route.name,'PromotionSpellGroup'])
          setTimeout(() => {
            goBack();
          }, 500);
        }else{
          loading.value = false
          failTip(res.msg)
        }
      })
    } catch (error) {
      console.info(error,'error')
    }
  }

  onMounted(() => {
    if (route.query.id != undefined && Number(route.query.id) > 0) {
      loading.value = true
      get_detail(route.query.id);
    }
    get_activity_label()
  });
</script>
<style lang="less">
@import './style/add_spell_group.less';
</style>
