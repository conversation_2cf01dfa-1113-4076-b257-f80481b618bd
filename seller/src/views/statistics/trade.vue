<template>
  <div class="section_padding">
    <div class="section_padding_home_scroll">
      <!-- 交易总览start -->
      <div class="w-full bg-white">
        <div class="flex flex-row h-12 items-center pl-3 border-b-1 border-zinc-100">
          <div class="position-title font-bold mr-2"> {{ L('交易总览') }} </div>
          <DateMultiPicker name="over_view" @change="dateOverChange"></DateMultiPicker>
        </div>
        <div style="padding: 0 20px 50px">
          <div class="flex flex-row mt-4 justify-between" style="position: relative">
            <div style="width: 100%">
              <div
                v-for="({ children }, index) in fieldMap"
                :key="index"
                class="flex flex-row"
                :style="{
                  background: index == 0 ? '#fdfdfd' : index == 1 ? '#f9f9f9' : '#f5f5f5',
                  boxShadow:
                    index == 0
                      ? '0 1px 12px 0 rgba(0,0,0,.06)'
                      : index == 1
                      ? '0 3px 6px 0 #f5f5f5'
                      : '0 2px 4px 0 hsla(0,0%,73.3%,.18)',
                  borderRadius: index == 2 ? '20px 20px 0 0' : '0 0 20px 20px',
                  marginTop: index == 0 ? '' : '-20px',
                  padding: index == 2 ? '14px 22px 0' : '14px 22px 10px',
                }"
              >
                <div
                  v-for="(child, childx) in children"
                  :key="childx"
                  style="width: 210.5px; margin-bottom: 22px"
                >
                  <SldStatCard
                    :label="child.label"
                    :value="child.num"
                    :tip="child.tip"
                    :is-growth="true"
                    :growth-text="L('较上期')"
                    :is-growth-up="child.isUp"
                    :is-money="child.isMoney"
                    :growth-value="child.differenceNum"
                  ></SldStatCard>
                </div>
              </div>
            </div>
            <ConverImage
              style="position: absolute; right: 33px; top: 12px"
              :view-pay="chartsInfoData.pvPayRate"
              :view-submit="chartsInfoData.pvSubmitRate"
              :submit-pay="chartsInfoData.submitPayRate"
            ></ConverImage>
          </div>
        </div>
      </div>
      <!-- 交易总览end -->

      <!-- 近30天变化趋势start -->
      <div class="w-full bg-white mt-3">
        <SldStatCharts
          mode="linear"
          :title="L('近30天变化趋势')"
          @register="trade30Trend_register"
          :api-url="API.GET_30_TREND"
          :data-handler="trendDataHandler"
        >
          <template #extraMiddleSlot>
            <div class="px-7 py-5">
              <div class="flex flex-row items-center">
                <div class="mr-3">{{ L('筛选项') }}</div>
                <RadioGroup size="small" v-model:value="current30Radio" @change="trend30RadioChange">
                  <Radio
                    :value="item.value"
                    class="day_size"
                    v-for="(item, index) in radio30Trend"
                    :key="index"
                    >{{ item.title }}</Radio
                  >
                </RadioGroup>
              </div>
            </div>
          </template>
        </SldStatCharts>
      </div>
      <!-- 近30天变化趋势end -->

      <div class="w-full mt-1 flex flex-row">
        <SldStatRank
          :title="L('商品销售排行') + ' - TOP10'"
          @register="goodsRank_register"
          :options="[
            { title: L('商品名称'), dataIndex: 'goodsName', customRenderType: 'goodsRender' },
            {
              title: L('销售额'),
              dataIndex: 'saleAmount',
              sortDirections: ['descend'],
              sorter: true,
              customRenderType: 'priceRender',
            },
            { title: L('销量'), dataIndex: 'saleNum', sortDirections: ['descend'], sorter: true },
          ]"
          :date-picker="true"
          :is-index="true"
        ></SldStatRank>
        <div class="min-w-3"></div>
        <SldStatCharts
          :title="L('订单变化趋势')"
          @register="orderTrend_register"
          :date-picker="true"
          :api-url="API.GET_ORDER_TREND"
          :data-handler="orderTrendDataHandler"
        >
        </SldStatCharts>
      </div>

      <div class="w-full mt-1 flex flex-row">
        <ReportWrapper
          :title="L('交易报表')"
          :export-option="{ api: API.TRADE_EXPORT, fileName: L('订单导出') }"
        >
          <ReportForm
            :columns="[
              {
                title: `${L('时间')}`,
                dataIndex: 'statsTime',
                sorter: true,
                width: 60,
              },
              {
                title: `${L('下单数')}`,
                dataIndex: 'orderSubmitNum',
                sorter: true,
                width: 80,
              },
              {
                title: `${L('下单人数')}`,
                dataIndex: 'orderSubmitMemberNum',
                sorter: true,
                width: 80,
              },
              {
                title: `${L('下单金额')}`,
                dataIndex: 'orderSubmitAmount',
                sorter: true,
                width: 80,
              },
              {
                title: `${L('支付订单数')}`,
                dataIndex: 'orderPayNum',
                sorter: true,
                width: 80,
              },
              {
                title: `${L('支付人数')}`,
                dataIndex: 'orderPayMemberNum',
                sorter: true,
                width: 80,
              },
              {
                title: `${L('支付金额')}`,
                dataIndex: 'orderPayAmount',
                sorter: true,
                width: 80,
              },
              {
                title: L('支付转化率'),
                dataIndex: 'submitPayRate',
                sorter: true,
                width: 80,
                helpMessage: L('下单-支付转化率：统计时间内，支付人数/下单人数'),
              },
            ]"
            :api="getTradeReport"
            :before-fetch="tradeReportFetch"
          >
          </ReportForm>
        </ReportWrapper>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { sldConvert } from '@/utils';
  import { getCurrentInstance, onMounted, ref } from 'vue';
  import { getTradeReport, API } from '@/api/sys/statAnalysis';
  import { RadioGroup, Radio } from 'ant-design-vue';
  import { tradeOverView, trade30Trend, handleOrderTrend } from './actions/trade';
  import {
    SldStatCharts,
    useStatCharts,
    SldStatRank,
    useStatRank,
    SldStatCard,
    ConverImage,
    DateMultiPicker,
    ReportWrapper,
    ReportForm,
  } from '@/components/SldStat';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const { fieldMap, chartsInfoData, dateOverChange } = tradeOverView();

  //近30天变化趋势start--------------------------
  const {
    register: trade30Trend_register,
    execApi: trade30Trend_execApi,
    execDataHandler: trade30Trend_execDataHandler,
  } = useStatCharts();
  const { current30Radio, radio30Trend, trend30RadioChange, trendDataHandler } = trade30Trend(
    trade30Trend_execApi,
    trade30Trend_execDataHandler,
  );
  //近30天变化趋势end--------------------------

  //各终端销售趋势start-----------------
  const { register: orderTrend_register } = useStatCharts();
  const { orderTrendDataHandler } = handleOrderTrend();
  //各终端销售趋势end-------------------

  //商品销售排行start------------------
  const goodsRank_paramHandler = (date, sortParam) => {
    let param = { ...date };
    if (sortParam.order) {
      param.sort = sortParam.field == 'orderPayAmount' ? 1 : 2;
    }
    return param;
  };
  const { register: goodsRank_register } = useStatRank({
    apiUrl: API.GET_GOODS_RANK,
    paramHandler: goodsRank_paramHandler,
  });
  //商品销售排行end-----------------------

  //交易报表start----------------------------
  const tradeReportFetch = ({ startTime, endTime, field, order, current }) => {
    let paramLocal = { startTime, endTime, current };
    if (order) {
      paramLocal.sort = sldConvert(field);
      paramLocal.type = order == 'descend' ? 'desc' : 'asc';
    }
    return paramLocal;
  };
  //交易报表end----------------------------

  onMounted(() => {});
</script>

<style scoped lang="less">
  .position-title {
    position: relative;
    height: 15px;
    padding-left: 8px;
    border-left: 4px solid @primary-color;
    color: #3a3e43;
    font-size: 14px;
    font-weight: 700;
    line-height: 15px;
    white-space: nowrap;
  }
</style>
