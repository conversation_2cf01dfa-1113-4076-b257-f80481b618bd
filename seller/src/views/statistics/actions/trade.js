import { ref, reactive, onMounted } from 'vue';
import { getTradeViewStat } from '@/api/sys/statAnalysis';
import { initialDateVar } from '/@/components/SldStat';
import { sldComLanguage } from '@/utils/utils';
export const tradeOverView = () => {
  const fieldMap = reactive([
    {
      children: [
        {
          label: `${sldComLanguage('浏览量')}`,
          num: 0,
          differenceNum: '',
          isUp: false,
          tip: `${sldComLanguage('统计时间内，全平台所有页面被访问的次数总和')}`,
          mapValue: 'viewNum',
          mapDifferentValue: 'previousViewNum',
        },
        {
          label: `${sldComLanguage('访客数')}`,
          num: 0,
          differenceNum: '',
          isUp: false,
          tip: `${sldComLanguage('统计时间内，全平台所有页面的去重人数总和')}`,
          mapValue: 'visitorNum',
          mapDifferentValue: 'previousVisitorNum',
        },
      ],
    },
    {
      children: [
        {
          label: `${sldComLanguage('下单金额')}`,
          num: 0,
          differenceNum: '',
          isUp: false,
          tip: `${sldComLanguage('统计时间内，全平台用户成功提交订单的金额总和')}`,
          mapValue: 'orderSubmitAmount',
          mapDifferentValue: 'previousOrderSubmitAmount',
          isMoney: true,
        },
        {
          label: `${sldComLanguage('下单数')}`,
          num: 0,
          differenceNum: '',
          isUp: false,
          tip: `${sldComLanguage('统计时间内，全平台用户成功提交订单的笔数总和')}`,
          mapValue: 'orderSubmitNum',
          mapDifferentValue: 'previousOrderSubmitNum',
        },
        {
          label: `${sldComLanguage('下单人数')}`,
          num: 0,
          differenceNum: '',
          isUp: false,
          tip: `${sldComLanguage('统计时间内，全平台成功提交订单的去重人数总和')}`,
          mapValue: 'orderSubmitMemberNum',
          mapDifferentValue: 'previousOrderSubmitMemberNum',
        },
        {
          label: `${sldComLanguage('下单客单价')}`,
          num: 0,
          differenceNum: '',
          isUp: false,
          tip: `${sldComLanguage('统计时间内，全平台下单金额/下单人数')}`,
          mapValue: 'orderSubmitAtv',
          mapDifferentValue: 'previousOrderSubmitAtv',
          isMoney: true,
        },
      ],
    },
    {
      children: [
        {
          label: `${sldComLanguage('支付金额')}`,
          num: 0,
          differenceNum: '',
          isUp: false,
          tip: `${sldComLanguage('统计时间内，全平台用户成功支付的金额总和')}`,
          mapValue: 'orderPayAmount',
          mapDifferentValue: 'previousOrderPayAmount',
          isMoney: true,
        },
        {
          label: `${sldComLanguage('支付订单数')}`,
          num: 0,
          differenceNum: '',
          isUp: false,
          tip: `${sldComLanguage('统计时间内，全平台用户成功支付的订单数量总和')}`,
          mapValue: 'orderPayNum',
          mapDifferentValue: 'previousOrderPayNum',
        },
        {
          label: `${sldComLanguage('支付人数')}`,
          num: 0,
          differenceNum: '',
          isUp: false,
          tip: `${sldComLanguage('统计时间内，全平台成功付款的去重人数总和')}`,
          mapValue: 'orderPayMemberNum',
          mapDifferentValue: 'previousOrderPayMemberNum',
        },
        {
          label: `${sldComLanguage('支付客单价')}`,
          num: 0,
          differenceNum: '',
          isUp: false,
          tip: `${sldComLanguage('统计时间内，全平台支付金额/下单人数')}`,
          mapValue: 'orderPayAtv',
          mapDifferentValue: 'previousOrderPayAtv',
          isMoney: true,
        },
      ],
    },
  ]);
  let paramView = {};
  const chartsInfoData = reactive({});
  const getTradeOverView = async (params) => {
    const res = await getTradeViewStat(params);
    if (res?.state == 200) {
      chartsInfoData.pvPayRate = res.data.pvPayRate || '--';
      chartsInfoData.pvSubmitRate = res.data.pvSubmitRate || '--';
      chartsInfoData.submitPayRate = res.data.submitPayRate || '--';
      fieldMap.forEach((item, index) => {
        item.children.forEach((item2, index2) => {
          if (
            res.data[item2.mapDifferentValue] &&
            res.data[item2.mapDifferentValue].indexOf('-') != 0
          ) {
            //上涨
            item2['differenceNum'] = '+' + res.data[item2.mapDifferentValue];
            item2.isUp = true;
          } else {
            //下降
            item2['differenceNum'] = res.data[item2.mapDifferentValue];
            item2.isUp = false;
          }
          if (!res.data[item2.mapValue]) {
            item2['num'] = 0;
            return;
          }
          item2['num'] = item2.isMoney
            ? parseFloat(res.data[item2.mapValue]).toFixed(2)
            : res.data[item2.mapValue];
        });
      });
    }
  };
  const dateOverChange = (param) => {
    paramView = param;
    getTradeOverView(paramView);
  };
  const radioTerminalChange = ({ target }) => {
    const { value } = target;
    let radioParam = {};
    if (value != 'all') {
      radioParam.terminalType = value;
    }
    getTradeOverView({ ...paramView, ...radioParam });
  };
  onMounted(() => {
    paramView = initialDateVar;
    getTradeOverView(paramView);
  });
  return { fieldMap, getTradeOverView, chartsInfoData, dateOverChange, radioTerminalChange };
};
export const trade30Trend = (execApi, execDataHandler) => {
  const trade30Option = reactive({
    series: [],
    xAxisData: [],
    maxMount: 0,
  });
  const current30Radio = ref(0);
  const radio30Trend = [
    {
      title: sldComLanguage('下单金额/支付金额'),
      value: 0,
      list: [
        { name: sldComLanguage('下单金额'), key: 'orderSubmitAmount' },
        { name: sldComLanguage('支付金额'), key: 'orderPayAmount' },
      ],
    },
    {
      title: sldComLanguage('下单数/支付订单数'),
      value: 1,
      list: [
        { name: sldComLanguage('下单数'), key: 'orderSubmitNum' },
        { name: sldComLanguage('支付订单数'), key: 'orderPayNum' },
      ],
    },
    {
      title: sldComLanguage('下单客单价/支付客单价'),
      value: 2,
      list: [
        { name: sldComLanguage('下单客单价'), key: 'orderSubmitAtv' },
        { name: sldComLanguage('支付客单价'), key: 'orderPayAtv' },
      ],
    },
    {
      title: sldComLanguage('浏览量'),
      value: 3,
      list: [{ name: sldComLanguage('浏览量'), key: 'viewNum' }],
    },
    {
      title: sldComLanguage('浏览-支付转化率'),
      value: 4,
      list: [{ name: sldComLanguage('浏览-支付转化率(%)'), key: 'pvPayRate' }],
    },
  ];
  const trendDataHandler = (return_data) => {
    const target = radio30Trend[current30Radio.value];
    trade30Option.xAxisData = return_data.map((item) => item.statsTime);
    const maxList = [];
    trade30Option.series = [];
    target.list.forEach((tar) => {
      let dataList = return_data.map((item) => parseFloat(item[tar.key]));
      trade30Option.series.push({
        name: tar.name,
        data: dataList,
        type: 'bar',
        barGap: '0',
        barWidth: '22%',
      });
      maxList.push(Math.max.apply(null, dataList));
    });
    trade30Option.maxMount = Math.max.apply(null, maxList);
    return trade30Option;
  };
  const trend30RadioChange = () => {
    execDataHandler();
  };
  const trend30TerminalChange = ({ target }) => {
    execApi();
  };
  return {
    trendDataHandler,
    trade30Option,
    current30Radio,
    radio30Trend,
    trend30RadioChange,
    trend30TerminalChange,
  };
};
export const handleOrderTrend = (execApi) => {
  const orderTrendOptions = reactive({
    xAxisData: [],
    series: [],
    maxMount: 0,
  });
  const orderTrendDataHandler = (return_data) => {
    orderTrendOptions.xAxisData = return_data.orderPayNumList.map((item) => item.statsTime);
    const orderAmountLists = return_data.orderSubmitNumList.map((i) => i.orderSubmitNum);
    const orderNumLists = return_data.orderPayNumList.map((i) => i.orderPayNum);
    orderTrendOptions.maxMount = Math.max.apply(null, [...orderAmountLists, ...orderNumLists]);
    orderTrendOptions.series = [
      {
        name: sldComLanguage('支付订单数'),
        data: orderAmountLists,
        type: 'line',
      },
      {
        name: sldComLanguage('下单数'),
        data: orderNumLists,
        type: 'line',
      },
    ];
    return orderTrendOptions;
  };

  return {
    orderTrendDataHandler,
  };
};

