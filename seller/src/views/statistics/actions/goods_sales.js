import { getImagePath, sldConvert } from '/@/utils';
import { getGoodsOverView, getDayReport, getGoodsReport, getGoodsNumApi, API } from '/@/api/sys/statAnalysis';
import { initialDateVar } from '@/components/SldStat';
import { sucTip, failTip } from '@/utils/utils';
import { computed, onMounted, reactive, ref } from 'vue';
import moment from 'moment';
import { TextHiddenCustomRender, PriceCustomRender } from '@/components/SldStat';
import { sldComLanguage } from '@/utils/utils';
export const handleGoodsOverView = () => {
  const goodsPreviewData = ref([
    {
      name: sldComLanguage('商品SPU总数'),
      isShowOperate: false,
      num: 0,
      differenceNum: '',
      bg: getImagePath('images/goods_item_bg_1.png'),
      mapValue: 'goodsTotalNum',
      mapDifferentValue: '',
      isDifferenceShow: false,
      isUp: false,
    },
    {
      name: sldComLanguage('动销商品数'),
      isShowOperate: true,
      opValue: 0,
      num: 0,
      differenceNum: '',
      bg: getImagePath('images/goods_item_bg_2.png'),
      mapValue: 'movableGoodsNum',
      mapDifferentValue: 'preMovableGoods',
      isDifferenceShow: true,
      isUp: false,
    },
    {
      name: sldComLanguage('新增商品数'),
      isShowOperate: true,
      opValue: 0,
      num: 0,
      differenceNum: '',
      bg: getImagePath('images/goods_item_bg_3.png'),
      mapValue: 'newGoodsNum',
      mapDifferentValue: 'preNewGoods',
      isDifferenceShow: true,
      isUp: false,
    },
    {
      name: sldComLanguage('支付金额(元)'),
      isShowOperate: true,
      opValue: 0,
      num: 0,
      differenceNum: '',
      bg: getImagePath('images/goods_item_bg_4.png'),
      isDifferenceShow: true,
      mapValue: 'orderPayAmount',
      mapDifferentValue: 'preOrderPay',
      isUp: false,
    },
    {
      name: sldComLanguage('下单数'),
      isShowOperate: true,
      opValue: 0,
      num: 0,
      differenceNum: '',
      bg: getImagePath('images/goods_item_bg_5.png'),
      mapValue: 'orderSubmitNum',
      mapDifferentValue: 'preOrderSubmit',
      isDifferenceShow: true,
      isUp: false,
    },
  ]);
  const getGoodsOverViewData = async (params, itemIndex) => {


    const res = await getGoodsOverView(params);
    if (res?.state == 200) {
      let item;
      for (let i = 1, len = goodsPreviewData.value.length; i < len; i++) {
        if (itemIndex != undefined) {
          item = goodsPreviewData.value[itemIndex];
        } else {
          item = goodsPreviewData.value[i];
        }
        if (
          res.data[item.mapDifferentValue] &&
          res.data[item.mapDifferentValue].indexOf('-') === 0
        ) {
          //较上期下降
          item.differenceNum = res.data[item.mapDifferentValue];
          item.isUp = false;
        } else {
          //较上期上涨
          item.differenceNum = '+' + (res.data[item.mapDifferentValue] || '0');
          item.isUp = true;
        }
        item.num = res.data[item.mapValue];
        if (itemIndex != undefined) {
          break;
        }
      }
    } else {
      failTip(res.msg);
    }
  };
  const selectData = [
    {
      label: sldComLanguage('昨天'),
      param: {
        startTime: moment().subtract(1, 'days').format('YYYY-MM-DD') + ' 00:00:00',
        endTime: moment().subtract(1, 'days').format('YYYY-MM-DD') + ' 23:59:59:999',
      },
    },
    {
      label: sldComLanguage('近7天'),
      param: {
        startTime: moment().subtract(7, 'days').format('YYYY-MM-DD') + ' 00:00:00',
        endTime: moment().subtract(1, 'days').format('YYYY-MM-DD') + ' 23:59:59:999',
      },
    },
    {
      label: sldComLanguage('近30天'),
      param: {
        startTime: moment().subtract(30, 'days').format('YYYY-MM-DD') + ' 00:00:00',
        endTime: moment().subtract(1, 'days').format('YYYY-MM-DD') + ' 23:59:59:999',
      },
    },
  ];
  const changeSelData = (op, opdx, itemIndex) => {
    op.opValue = opdx;
    getGoodsOverViewData(selectData[opdx].param, itemIndex);
  };

  const getGoodsNum = async () => {
    const res = await getGoodsNumApi()
    if (res.state == 200) {
      goodsPreviewData.value[0].num = res.data.goodsTotalNum
    }
  }

  onMounted(() => {
    getGoodsNum()
    getGoodsOverViewData(selectData[0].param);
  });
  return { goodsPreviewData, getGoodsOverViewData, selectData, changeSelData };
};
export const handleSaleTrend = (exeDatahandler) => {
  const saleTrend_radio = ref(1);
  const saleTrend_radio_data = {
    1: ['newGoodsList', 'newGoodsNum', sldComLanguage('新增商品数')],
    2: ['movableGoodsList', 'movableGoodsNum', sldComLanguage('动销商品数')],
    3: ['orderNumList', 'orderSubmitNum', sldComLanguage('下单数')],
    4: ['orderPayNumList', 'orderPayNum', sldComLanguage('支付订单数')],
    5: ['orderPayAmountList', 'orderPayAmount', sldComLanguage('支付金额')],
    6: ['orderAmountList', 'orderSubmitAmount', sldComLanguage('下单金额')],
  };
  const saleTrend_option = reactive({
    xAxisData: [],
    series: [],
    maxMount: 0,
  });
  const saleTrendDataHandler = (return_data) => {
    const currentKey = saleTrend_radio_data[saleTrend_radio.value];
    saleTrend_option.xAxisData = return_data[currentKey[0]].map((i) => i.statsTime);
    saleTrend_option.series = [
      {
        name: currentKey[2],
        data: return_data[currentKey[0]].map((i) => i[currentKey[1]]),
        type: 'line',
      },
    ];
    const pureMap = return_data[currentKey[0]].map((i) => i[currentKey[1]]);
    saleTrend_option.maxMount = Math.max.apply(null, pureMap);
    return saleTrend_option;
  };
  const saleTrend_radio_change = () => {
    exeDatahandler();
  };
  return { saleTrend_radio, saleTrendDataHandler, saleTrend_radio_change };
};
export const handleSimpleRank = (apiFunc, sortHandler) => {
  const dataSource = ref([]);
  const getData = async (params) => {
    const res = await apiFunc(params);
    if (res?.state == 200) {
      dataSource.value = res.data.list || res.data;
    } else {
      failTip(res.msg);
    }
  };
  const change = ({ dateParam, sortOption }) => {
    const sortParam = sortHandler ? sortHandler(sortOption) : {};
    getData({ ...dateParam, ...sortParam });
  };
  onMounted(() => {
    getData(initialDateVar);
  });
  return [dataSource, change, getData];
};
export const handleGoodsSaleTrend = (exeDatahandler) => {
  const goodsSaleTrend_radio = ref(1);
  const saleTrend_option = reactive({
    xAxisData: [],
    series: [],
    maxMount: 0,
  });
  const goodsSaleRankDataHandler = (return_data) => {
    const sortObject = {};
    saleTrend_option.xAxisData = return_data.map((i) => i.statsTime);
    let maxMount = 0;
    const goodsListMap = return_data.map((i) => i.goodsList).flat(1);
    goodsListMap.forEach((item) => {
      let targetMount = goodsSaleTrend_radio.value == 1 ? item.saleAmount : item.saleNum;
      if (targetMount > maxMount) maxMount = targetMount;
      sortObject[item.goodsId] = sortObject[item.goodsId] || {
        name: item.goodsName,
        data: [],
      };
      sortObject[item.goodsId].data.push(targetMount);
    });
    saleTrend_option.series = Object.keys(sortObject).map((sort) => ({
      name: sortObject[sort].name,
      data: sortObject[sort].data,
      type: 'line',
    }));
    saleTrend_option.maxMount = maxMount;
    return saleTrend_option;
  };
  const goodsSaleRank_radio_change = () => {
    exeDatahandler();
  };
  return { goodsSaleRankDataHandler, goodsSaleTrend_radio, goodsSaleRank_radio_change };
};
export const handleReport = () => {
  const reportOptionsSet = ref([
    {
      name: sldComLanguage('按天'),
      key: 'byDay',
      apiFunc: getDayReport,
      columns: [
        { title: sldComLanguage('时间'), dataIndex: 'statsTime' },
        { title: sldComLanguage('新增商品数'), dataIndex: 'newGoodsNum', sorter: true },
        { title: sldComLanguage('动销商品数'), dataIndex: 'movableGoodsNum', sorter: true },
        { title: sldComLanguage('下单数'), dataIndex: 'orderSubmitNum', sorter: true },
        { title: sldComLanguage('下单金额'), dataIndex: 'orderSubmitAmount', sorter: true },
        { title: sldComLanguage('支付订单数'), dataIndex: 'orderPayNum', sorter: true },
        { title: sldComLanguage('支付金额'), dataIndex: 'orderPayAmount', sorter: true },
      ],
      isColumnIndex: false,
      beforeFetch ({ startTime, endTime, field, order, current, pageSize }) {
        let paramLocal = { startTime, endTime, current, pageSize };
        if (order) {
          paramLocal.sort = sldConvert(field);
          paramLocal.type = order == 'descend' ? 'desc' : 'asc';
        }
        return paramLocal;
      },
    },
    {
      name: sldComLanguage('按商品'),
      key: 'byGoods',
      apiFunc: getGoodsReport,
      columns: [
        {
          title: sldComLanguage('商品名称'),
          width: 150,
          dataIndex: 'goodsName',
          customRender: TextHiddenCustomRender,
        },
        { title: sldComLanguage('销量'), width: 150, dataIndex: 'saleNum', sorter: true },
        {
          title: sldComLanguage('支付金额'),
          width: 150,
          dataIndex: 'saleAmount',
          sorter: true,
          customRender: PriceCustomRender,
        },
        {
          title: sldComLanguage('下单金额'),
          width: 150,
          dataIndex: 'orderSubmitAmount',
          sorter: true,
          customRender: PriceCustomRender,
        },
        { title: sldComLanguage('支付订单数'), width: 150, dataIndex: 'orderPayNum', sorter: true },
        { title: sldComLanguage('下单数'), width: 150, dataIndex: 'orderSubmitNum', sorter: true },
        { title: sldComLanguage('下单转化率'), width: 150, dataIndex: 'pvSubmitRate', sorter: true, helpMessage: sldComLanguage('访客-下单转化率：统计时间内，下单人数/店铺访客数') },
        { title: sldComLanguage('访客数'), width: 150, dataIndex: 'visitorNum', sorter: true },
        { title: sldComLanguage('浏览量'), width: 150, dataIndex: 'viewNum', sorter: true },
        { title: sldComLanguage('收藏人数'), width: 150, dataIndex: 'collectionNum', sorter: true },
        { title: sldComLanguage('加购人数'), width: 150, dataIndex: 'addCartMemberNum', sorter: true },
        { title: sldComLanguage('退单数'), width: 150, dataIndex: 'returnNum', sorter: true },
        {
          title: sldComLanguage('退单金额'),
          width: 100,
          dataIndex: 'returnAmount',
          sorter: true,
          customRender: PriceCustomRender,
        },
      ],
      searchData: [
        {
          field: 'goodsName',
          component: 'Input',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            maxlength: 20,
            placeholder: sldComLanguage('请输入商品名称'),
            size: 'default',
          },
          label: sldComLanguage('商品名称'),
          labelWidth: 80,
        },
      ],
      isColumnIndex: true,
      beforeFetch ({ startTime, endTime, field, order, current, goodsName, pageSize }) {
        let paramLocal = { startTime, endTime, current, goodsName, pageSize };
        if (order) {
          paramLocal.sort = sldConvert(field);
          paramLocal.type = order == 'descend' ? 'desc' : 'asc';
        }
        return paramLocal;
      },
    },
  ]);
  const reportTabValue = ref('byDay');
  const currentExportOption = computed(() => ({
    api:
      reportTabValue.value == 'byDay'
        ? API.GOODS_DAY_EXPORT
        : API.GOODS_EXPORT,
    fileName: sldComLanguage('商品报表'),
  }));
  return {
    reportOptionsSet,
    reportTabValue,
    currentExportOption,
  };
};
