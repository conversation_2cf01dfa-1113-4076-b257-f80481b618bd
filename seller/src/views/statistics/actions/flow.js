import { computed, onMounted, reactive, ref } from 'vue';
import { getFlowReport, API, getFlowOverView } from '/@/api/sys/statAnalysis';
import { initialDateVar } from '/@/components/SldStat';
import { sucTip, failTip } from '@/utils/utils';
import { getImagePath, sldConvert } from '/@/utils';
import { sldComLanguage } from '@/utils/utils';
export const trade30FlowTrend = (execApi, execDataHandler) => {
  const trade30Option = reactive({
    series: [],
    xAxisData: [],
    maxMount: 0,
  });
  const current30Radio = ref(0);
  const radio30Trend = [
    {
      title: sldComLanguage('店铺访客数'),
      value: 0,
      list: [
        { name: sldComLanguage('店铺访客数'), key: 'visitorNum' },
      ],
    },
    {
      title: sldComLanguage('店铺浏览量'),
      value: 1,
      list: [
        { name: sldComLanguage('店铺浏览量'), key: 'viewNum' },
      ],
    },
    {
      title: sldComLanguage('人均浏览量'),
      value: 2,
      list: [
        { name: sldComLanguage('人均浏览量'), key: 'perViewNum' },
      ],
    },
    {
      title: sldComLanguage('商品访客数'),
      value: 3,
      list: [{ name: sldComLanguage('商品访客数'), key: 'goodsVisitorNum' }],
    },
    {
      title: sldComLanguage('商品浏览量'),
      value: 4,
      list: [{ name: sldComLanguage('商品浏览量'), key: 'goodsViewNum' }],
    },
    {
      title: sldComLanguage('支付人数'),
      value: 5,
      list: [{ name: sldComLanguage('支付人数'), key: 'orderPayMemberNum' }],
    },
    {
      title: sldComLanguage('支付订单数'),
      value: 6,
      list: [{ name: sldComLanguage('支付订单数'), key: 'orderPayNum' }],
    },
    {
      title: sldComLanguage('浏览-付款转化率'),
      value: 7,
      list: [{ name: sldComLanguage('浏览-付款转化率(%)'), key: 'pvPayRate' }],
    },
  ];
  const trendDataHandler = (return_data) => {
    const target = radio30Trend[current30Radio.value];
    trade30Option.xAxisData = return_data.map((item) => item.statsTime);
    const maxList = [];
    trade30Option.series = [];
    target.list.forEach((tar) => {
      let dataList = return_data.map((item) => parseFloat(item[tar.key]));
      trade30Option.series.push({
        name: tar.name,
        data: dataList,
        type: 'bar',
        barGap: '0',
        barWidth: '22%',
      });
      maxList.push(Math.max.apply(null, dataList));
    });
    trade30Option.maxMount = Math.max.apply(null, maxList);
    return trade30Option;
  };
  const trend30RadioChange = () => {
    execDataHandler();
  };
  const trend30TerminalChange = () => {
    execApi();
  };
  return {
    trade30Option,
    current30Radio,
    radio30Trend,
    trend30RadioChange,
    trend30TerminalChange,
    trendDataHandler,
  };
};
export const handleFlowReport = () => {
  const dayReportOption = [
    {
      title: `${sldComLanguage('时间')}`,
      dataIndex: 'statsTime',
      sorter: true,
      width: 60,
    },
    {
      title: `${sldComLanguage('店铺访客数')}`,
      dataIndex: 'visitorNum',
      sorter: true,
      width: 80,
    },
    {
      title: `${sldComLanguage('店铺浏览量')}`,
      dataIndex: 'viewNum',
      sorter: true,
      width: 80,
    },
    {
      title: sldComLanguage('人均浏览量'),
      dataIndex: 'perViewNum',
      sorter: true,
      width: 80,
    },
    {
      title: `${sldComLanguage('商品访客数')}`,
      dataIndex: 'goodsVisitorNum',
      sorter: true,
      width: 80,
    },
    {
      title: `${sldComLanguage('商品浏览量')}`,
      dataIndex: 'goodsViewNum',
      sorter: true,
      width: 80,
    },
  ];

  const reportOptionsSet = {
    apiFunc: getFlowReport,
    columns: dayReportOption,
    isColumnIndex: false,
    beforeFetch ({ startTime, endTime, field, order, current, pageSize }) {
      let paramLocal = { startTime, endTime, current, pageSize };
      if (order) {
        paramLocal.sort = sldConvert(field);
        paramLocal.type = order == 'descend' ? 'desc' : 'asc';
      }
      return paramLocal;
    },
  }

  const reportTabValue = ref('byDay');
  const currentExportOption = computed(() => ({
    api: API.FLOW_EXPORT,
    fileName: sldComLanguage('流量报表'),
  }));
  return {
    currentExportOption,
    reportOptionsSet,
    reportTabValue,
  };
};
export const flowOverView = () => {
  const flowData1 = reactive({
    icon: getImagePath('images/flow_head_icon-2.png'),
    title: sldComLanguage('浏览访问'),
    list: [
      {
        name: sldComLanguage('店铺访客数'),
        value: 0,
        preValue: '',
        isHelpIcon: true,
        tip: sldComLanguage('统计时间内，店铺内所有页面被访问的去重人数'),
        mapKey: 'visitorNum',
        mapPreKey: 'preVisitorNum',
        isMoney: false
      },
      {
        name: sldComLanguage('店铺浏览量'),
        value: 0,
        preValue: '',
        isHelpIcon: true,
        tip: sldComLanguage('统计时间内，店铺内所有页面被访问的次数'),
        mapKey: 'viewNum',
        mapPreKey: 'preViewNum',
        isMoney: false
      },
      {
        name: sldComLanguage('人均浏览量'),
        value: 0,
        preValue: '',
        isHelpIcon: true,
        tip: sldComLanguage('店铺浏览量/店铺访客数'),
        mapKey: 'perViewNum',
        mapPreKey: 'prePerViewNum',
        isMoney: false
      },
      {
        name: sldComLanguage('商品访客数'),
        value: 0,
        preValue: '',
        isHelpIcon: true,
        tip: sldComLanguage('统计时间内，访问店铺内商品详情页的去重人数'),
        mapKey: 'goodsVisitorNum',
        mapPreKey: 'preGoodsVisitorNum',
        isMoney: false
      },
      {
        name: sldComLanguage('商品浏览量'),
        value: 0,
        preValue: '',
        isHelpIcon: true,
        tip: sldComLanguage('统计时间内，访问店铺内商品详情页的人次数'),
        mapKey: 'goodsViewNum',
        mapPreKey: 'preGoodsViewNum',
        isMoney: false
      },
    ],
  });

  const flowData2 = reactive({
    icon: getImagePath('images/flow_head_icon-1.png'),
    title: sldComLanguage('成交转化'),
    list: [
      {
        name: sldComLanguage('支付人数'),
        value: 0,
        preValue: '',
        isHelpIcon: true,
        tip: sldComLanguage('统计时间内，统计时间内，成功付款的去重人数，拼团在成团时计入付款人数；定金预售在尾款支付时计入付款人数'),
        mapKey: 'orderPayMemberNum',
        mapPreKey: 'preOrderPayMemberNum',
      },
      {
        name: sldComLanguage('支付订单数'),
        value: 0,
        preValue: '',
        isHelpIcon: true,
        tip: sldComLanguage('统计时间内，该店铺下所有已支付订单的总数量'),
        mapKey: 'orderPayNum',
        mapPreKey: 'preOrderPayNum',
      },
      {
        name: sldComLanguage('支付金额'),
        value: 0,
        preValue: '',
        isHelpIcon: true,
        tip: sldComLanguage('统计时间内，所有支付订单金额之和，会员储值不计算在内。 拼团在成团时计入支付金额；定金预售在尾款支付时计入支付金额。'),
        mapKey: 'orderPayAmount',
        mapPreKey: 'preOrderPayAmount',
        isMoney: true
      },
      {
        name: sldComLanguage('支付客单价'),
        value: 0,
        preValue: '',
        isHelpIcon: true,
        tip: sldComLanguage('统计时间内，付款金额/付款人数'),
        mapKey: 'orderPayAtv',
        mapPreKey: 'preOrderPayAtv',
        isMoney: true
      },
      {
        name: sldComLanguage('浏览-支付转化率'),
        value: 0,
        preValue: '',
        isHelpIcon: true,
        tip: sldComLanguage('统计时间内，支付人数/店铺访客数'),
        mapKey: 'pvPayRate',
        mapPreKey: 'prePvPayRate',
        isPercent: true
      },
    ],
  });

  const mapHandler = (item2, originData) => {
    if (
      originData[item2.mapPreKey] && originData[item2.mapPreKey].indexOf('-') != 0
    ) {
      //上涨
      item2.preValue = '+' + originData[item2.mapPreKey];
      item2.isUp = true;
    } else {
      //下降
      item2.preValue = originData[item2.mapPreKey];
      item2.isUp = false;
    }
    if (!originData[item2.mapKey]) {
      return;
    }
    item2.value = item2.isPercent ? parseInt(originData[item2.mapKey]) : originData[item2.mapKey];
  }
  let paramView = initialDateVar
  const getTradeOverView = async (params) => {
    const res = await getFlowOverView(params);
    if (res?.state == 200) {
      flowData1.list.map((item) => mapHandler(item, res.data));
      flowData2.list.map((item) => mapHandler(item, res.data));
    } else {
      failTip(res.msg);
    }
  };
  const dateOverChange = (param) => {
    paramView = param;
    getTradeOverView(paramView);
  };

  onMounted(() => {
    getTradeOverView(paramView);
  });
  return { getTradeOverView, dateOverChange, flowData1, flowData2 };
};

