import { getImagePath } from '/@/utils';
import { sldComLanguage } from '@/utils/utils';
export const realTimeData1 = {
  icon: getImagePath('images/real_icon_1.png'),
  title: sldComLanguage('店铺汇总'),
  list: [
    {
      name: sldComLanguage('销售总额'),
      value: 0,
      isHelpIcon: false,
      tip: ``,
      mapKey: 'orderPayAmountTotal',
      isMoney: true,
    },
    {
      name: sldComLanguage('用户总数'),
      value: 0,
      isHelpIcon: true,
      tip: sldComLanguage('在本店铺有过浏览行为的历史去重人数总和'),
      mapKey: 'memberNum',
    },
    {
      name: sldComLanguage('在售商品数'),
      value: 0,
      isHelpIcon: true,
      tip: sldComLanguage('截止至当前时间状态为在售的商品数量'),
      mapKey: 'saleGoodsNum',
    },
    {
      name: sldComLanguage('关注店铺人数'),
      value: 0,
      isHelpIcon: true,
      tip: sldComLanguage('截止至当前时间，关注店铺的累计用户数'),
      mapKey: 'followMemberNum',
    },
  ],
};
export const realTimeData2 = {
  icon: getImagePath('images/real_icon_2.png'),
  title: sldComLanguage('今日实时'),
  list: [
    {
      name: sldComLanguage('今日销售额'),
      value: 0,
      isHelpIcon: false,
      tip: sldComLanguage('今日0时至当前时间的销售额'),
      mapKey: 'orderPayAmount',
      isMoney: true,
    },
    {
      name: sldComLanguage('新增用户数'),
      value: 0,
      isHelpIcon: true,
      tip: sldComLanguage('统计时间内，注册并首次访问店铺的用户数'),
      mapKey: 'newMemberNum',
    },
    {
      name: sldComLanguage('新增商品数'),
      value: 0,
      isHelpIcon: true,
      tip: sldComLanguage('统计时间内，店铺新增商品spu数'),
      mapKey: 'newGoodsNum',
    },
    {
      name: sldComLanguage('动销商品数'),
      value: 0,
      isHelpIcon: true,
      tip: sldComLanguage('统计时间内，销量不为0的商品数'),
      mapKey: 'salingGoodsNum',
    },
    {
      isBr: true,
    },
    {
      name: sldComLanguage('店铺访客数'),
      value: 0,
      isHelpIcon: true,
      tip: sldComLanguage('统计时间内，店铺内所有页面被访问的去重人数'),
      mapKey: 'visitorNum',
    },
    {
      name: sldComLanguage('店铺浏览量'),
      value: 0,
      isHelpIcon: true,
      tip: sldComLanguage('统计时间内，店铺内所有页面被访问的次数'),
      mapKey: 'viewNum',
    },
    {
      name: sldComLanguage('商品访客数'),
      value: 0,
      isHelpIcon: true,
      tip: sldComLanguage('统计时间内，访问商品详情页的去重人数'),
      mapKey: 'goodsVisitorNum',
    },
    {
      name: sldComLanguage('商品浏览量'),
      value: 0,
      isHelpIcon: true,
      tip: sldComLanguage('统计时间内，访问商品详情页的人次数'),
      mapKey: 'goodsViewNum',
    },
    {
      isBr: true,
    },
    {
      name: sldComLanguage('下单数'),
      value: 0,
      isHelpIcon: true,
      tip: sldComLanguage('统计时间内，全平台用户成功提交订单的笔数总和'),
      mapKey: 'orderSubmitNum',
    },
    {
      name: sldComLanguage('下单人数'),
      value: 0,
      isHelpIcon: true,
      tip: sldComLanguage('统计时间内，成功提交订单的去重人数'),
      mapKey: 'orderSubmitMemberNum',
    },
    {
      name: sldComLanguage('下单金额'),
      value: 0,
      isHelpIcon: true,
      tip: sldComLanguage('统计时间内，客户成功提交订单的总金额'),
      mapKey: 'orderSubmitAmount',
      isMoney: true,
    },
    {
      name: sldComLanguage('下单客单价'),
      value: 0,
      isHelpIcon: true,
      tip: sldComLanguage('统计时间内，下单金额/下单人数'),
      mapKey: 'orderSubmitAtv',
      isMoney: true,
    },
    {
      name: sldComLanguage('访问-下单转化率'),
      value: 0,
      isHelpIcon: true,
      tip: sldComLanguage('统计时间内，下单人数/平台访客数'),
      mapKey: 'pvSubmitRate',
      isPercent: true

    },
    {
      isBr: true,
    },
    {
      name: sldComLanguage('支付订单数'),
      value: 0,
      isHelpIcon: true,
      tip: sldComLanguage('统计时间内，该店铺下所有已支付订单的总数量'),
      mapKey: 'orderPayNum',
    },
    {
      name: sldComLanguage('支付人数'),
      value: 0,
      isHelpIcon: true,
      tip: sldComLanguage('统计时间内，成功付款的去重人数，拼团在成团时计入付款人数；定金预售在尾款支付时计入付款人数；'),
      mapKey: 'orderPayMemberNum',
    },
    {
      name: sldComLanguage('支付金额'),
      value: 0,
      isHelpIcon: true,
      tip: sldComLanguage('统计时间内，所有支付订单金额之和，会员储值不计算在内。 拼团在成团时计入支付金额；定金预售在尾款支付时计入支付金额。'),
      mapKey: 'orderPayAmount',
      isMoney: true,
    },
    {
      name: sldComLanguage('支付客单价'),
      value: 0,
      isHelpIcon: true,
      tip: sldComLanguage('统计时间内，下单金额/下单人数'),
      mapKey: 'orderPayAtv',
      isMoney: true,
    },
    {
      name: sldComLanguage('访问-支付转化率'),
      value: 0,
      isHelpIcon: true,
      tip: sldComLanguage('统计时间内，支付人数/平台访客数'),
      mapKey: 'pvPayRate',
      isPercent: true
    },
  ],
};
