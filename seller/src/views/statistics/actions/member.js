import moment from 'moment';
import { getImagePath, sldConvert } from '/@/utils';
import { computed, onMounted, reactive, ref } from 'vue';
import { sucTip, failTip } from '@/utils/utils';
import {
  getMemberOverView,
  getMemberNum,
  getMemberReport,
  getMemberDayReport,
  API
} from '@/api/sys/statAnalysis';
import { PriceCustomRender } from '/@/components/SldStat';
import { sldComLanguage } from '@/utils/utils';
export const handleMemberOverView = () => {
  const memeberNumData = reactive({
    name: sldComLanguage('会员总数'),
    isShowOperate: false,
    opValue: 0,
    num: 0,
    differenceNum: '',
    bg: getImagePath('images/store_item_bg_1.png'),
    mapValue: 'memberNum',
    mapDifferentValue: '',
    isDifferenceShow: false,
    isUp: false,
  });
  const memberPreviewData = ref([
    {
      name: sldComLanguage('新增会员数'),
      isShowOperate: true,
      opValue: 0,
      num: 0,
      differenceNum: '',
      bg: getImagePath('images/store_item_bg_2.png'),
      mapValue: 'newMemberNum',
      mapDifferentValue: 'preNewMember',
      isDifferenceShow: true,
      isUp: false,
    },
    {
      name: sldComLanguage('店铺访客数'),
      isShowOperate: true,
      opValue: 0,
      num: 0,
      differenceNum: '',
      bg: getImagePath('images/store_item_bg_3.png'),
      mapValue: 'visitorNum',
      mapDifferentValue: 'preVisitor',
      isDifferenceShow: true,
      isUp: false,
    },
    {
      name: sldComLanguage('支付人数'),
      isShowOperate: true,
      opValue: 0,
      num: 0,
      differenceNum: '',
      bg: getImagePath('images/store_item_bg_4.png'),
      mapValue: 'payMemberNum',
      mapDifferentValue: 'prePayMember',
      isDifferenceShow: true,
      isUp: false,
    },
  ]);
  const getMemberNumData = async () => {
    const res = await getMemberNum();
    memeberNumData.num = res.data.memberNum;
  };
  const getMemberOverViewData = async (params, itemIndex) => {
    const res = await getMemberOverView(params);
    if (res?.state == 200) {
      let item;
      for (let i = 0, len = memberPreviewData.value.length; i < len; i++) {
        if (itemIndex != undefined) {
          item = memberPreviewData.value[itemIndex - 1];
        } else {
          item = memberPreviewData.value[i];
        }
        if (
          res.data[item.mapDifferentValue] &&
          res.data[item.mapDifferentValue].indexOf('-') === 0
        ) {
          //较上期下降
          item.differenceNum = res.data[item.mapDifferentValue];
          item.isUp = false;
        } else {
          //较上期上涨
          item.differenceNum = '+' + (res.data[item.mapDifferentValue] || '0');
          item.isUp = true;
        }
        item.num = res.data[item.mapValue];
        if (itemIndex != undefined) {
          break;
        }
      }
    } else {
      failTip(res.msg);
    }
  };
  const selectData = [
    {
      label: sldComLanguage('昨天'),
      param: {
        startTime: moment().subtract(1, 'days').format('YYYY-MM-DD') + ' 00:00:00',
        endTime: moment().subtract(1, 'days').format('YYYY-MM-DD') + ' 23:59:59:999',
      },
    },
    {
      label: sldComLanguage('近7天'),
      param: {
        startTime: moment().subtract(7, 'days').format('YYYY-MM-DD') + ' 00:00:00',
        endTime: moment().subtract(1, 'days').format('YYYY-MM-DD') + ' 23:59:59:999',
      },
    },
    {
      label: sldComLanguage('近30天'),
      param: {
        startTime: moment().subtract(30, 'days').format('YYYY-MM-DD') + ' 00:00:00',
        endTime: moment().subtract(1, 'days').format('YYYY-MM-DD') + ' 23:59:59:999',
      },
    },
  ];
  const changeSelData = (op, opdx, itemIndex) => {
    op.opValue = opdx;
    getMemberOverViewData(selectData[opdx].param, itemIndex);
  };
  onMounted(() => {
    getMemberOverViewData(selectData[0].param);
    getMemberNumData();
  });
  return { memberPreviewData, getMemberOverViewData, selectData, changeSelData, memeberNumData };
};
export const handleReport = () => {
  const dayReportOption = [
    {
      title: `${sldComLanguage('时间')}`,
      dataIndex: 'statsTime',
      sorter: true,
      width: 60,
    },
    {
      title: sldComLanguage('新增会员数'),
      dataIndex: 'newMemberNum',
      sorter: true,
      width: 80,
    },
    {
      title: sldComLanguage('店铺访客数'),
      dataIndex: 'visitorNum',
      sorter: true,
      width: 80,
    },
    {
      title: sldComLanguage('下单人数'),
      dataIndex: 'orderSubmitMemberNum',
      sorter: true,
      width: 80,
    },
    {
      title: sldComLanguage('支付人数'),
      dataIndex: 'orderPayMemberNum',
      sorter: true,
      width: 80,
    },
    {
      title: sldComLanguage('关注店铺人数'),
      dataIndex: 'collectionStoreNum',
      sorter: true,
      width: 80,
    },
  ];
  const memberReportOption = [
    {
      title: sldComLanguage('会员名'),
      dataIndex: 'memberName',
      sorter: true,
      width: 60,
    },

    {
      title: sldComLanguage('下单数'),
      dataIndex: 'orderSubmitNum',
      sorter: true,
      width: 80,
    },
    {
      title: sldComLanguage('下单金额'),
      dataIndex: 'orderSubmitAmount',
      sorter: true,
      width: 80,
      customRender: PriceCustomRender,
    },
    {
      title: sldComLanguage('支付订单数'),
      dataIndex: 'orderPayNum',
      sorter: true,
      width: 80,
    },
    {
      title: sldComLanguage('支付金额'),
      dataIndex: 'orderPayAmount',
      sorter: true,
      width: 80,
      customRender: PriceCustomRender,
    },
    {
      title: sldComLanguage('退单数'),
      dataIndex: 'returnNum',
      sorter: true,
      width: 80,
    },
    {
      title: sldComLanguage('退单金额'),
      dataIndex: 'returnAmount',
      sorter: true,
      width: 80,
      customRender: PriceCustomRender,
    },
  ];
  const replaceTime = (target, time) => {
    const date = target.split(' ')[0];
    return date + ' ' + time;
  };
  const reportOptionsSet = ref([
    {
      name: sldComLanguage('按天'),
      key: 'byDay',
      apiFunc: getMemberDayReport,
      columns: dayReportOption,
      isColumnIndex: false,
      beforeFetch ({ startTime, endTime, field, order, current, pageSize }) {
        let paramLocal = { startTime, endTime, current, pageSize };
        if (order) {
          paramLocal.sort = sldConvert(field);
          paramLocal.type = order == 'descend' ? 'desc' : 'asc';
        }
        return paramLocal;
      },
    },
    {
      name: sldComLanguage('按用户'),
      key: 'byMember',
      apiFunc: getMemberReport,
      columns: memberReportOption,
      searchData: [
        {
          field: 'memberName',
          component: 'Input',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            maxlength: 6,
            placeholder: sldComLanguage('请输入用户名'),
            size: 'default',
          },
          label: sldComLanguage('用户名'),
          labelWidth: 60,
        }
      ],
      isColumnIndex: true,
      beforeFetch ({
        startTime,
        endTime,
        field,
        order,
        current,
        memberName,
        pageSize,
        registerTime,
      }) {
        let paramLocal = { startTime, endTime, current, pageSize };
        if (memberName) paramLocal.memberName = memberName;
        if (order) {
          paramLocal.sort = sldConvert(field);
          paramLocal.type = order == 'descend' ? 'desc' : 'asc';
        }
        if (registerTime) {
          paramLocal.registerStartTime = replaceTime(registerTime[0], '00:00:00');
          paramLocal.registerEndTime = replaceTime(registerTime[1], '23:59:59');
        }
        return paramLocal;
      },
    },
  ]);
  const reportTabValue = ref('byDay');
  const currentExportOption = computed(() => ({
    api:
      reportTabValue.value == 'byDay'
        ? API.MEMBER_DAY_EXPORT
        : API.MEMBER_EXPORT,
    fileName: sldComLanguage('会员报表'),
  }));
  return {
    currentExportOption,
    reportOptionsSet,
    reportTabValue,
  };
};
