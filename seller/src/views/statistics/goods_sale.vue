<template>
  <div class="section_padding">
    <div class="section_padding_home_scroll">
      <!-- 商品总览start -->
      <div class="w-full bg-white">
        <div class="flex flex-row h-12 items-center pl-3 border-b-1 border-zinc-100">
          <div class="position-title font-bold mr-2"> {{ L('商品总览') }} </div>
        </div>

        <div class="p-5 flex flex-row justify-between">
          <div
            class="colorful_item flex flex-col justify-between"
            :style="{ backgroundImage: `url(${item.bg})` }"
            v-for="(item, index) in goodsPreviewData"
            :key="index"
          >
            <div class="flex flex-row justify-between items-center">
              <div class="text-sm text-white">{{ item.name }}</div>

              <div v-if="item.isShowOperate">
                <Popover placement="bottom" trigger="click">
                  <template #content>
                    <div class="bg-white w-17">
                      <div
                        class="flex flex-row items-center py-1 justify-between hover:text-[#ff590d] cursor-pointer"
                        v-for="(op, opdx) in selectData"
                        :key="opdx"
                        @click="changeSelData(item, opdx, index)"
                      >
                        <span class="">{{ op.label }}</span>
                        <CheckOutlined style="color: #ff590d" v-if="item.opValue == opdx" />
                      </div>
                    </div>
                  </template>
                  <div class="flex flex-row items-center cursor-pointer">
                    <span class="text-sm text-white">{{ selectData[item.opValue].label }}</span>
                    <CaretDownOutlined style="color: white; font-size: 15px" class="ml-1" />
                  </div>
                </Popover>
              </div>
            </div>

            <div>
              <div class="mt-3 text-3xl font-bold">
                <CountTo
                  :color="'#fff'"
                  :startVal="0"
                  :prefixStyle="{ fontSize: '17px' }"
                  :endVal="Number(item.num) || 0"
                  :duration="1000"
                />
              </div>
              <div class="w-1/2 mt-3 h-px bg-white"></div>
              <div
                class="flex flex-row items-center text-white mt-3"
                :class="{ invisible: !item.isShowOperate }"
              >
                <span>{{ L('较上期') }}</span>
                <div class="ml-2 font-bold">{{ item.differenceNum }}</div>
                <ArrowUpOutlined class="ml-1" v-if="item.isUp" style="font-weight: bold" />
                <ArrowDownOutlined class="ml-1" v-else style="font-weight: bold" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 商品总览end -->

      <!-- 动销趋势start -->
      <div class="w-full bg-white mt-3">
        <SldStatCharts
          :title="L('商品动销趋势')"
          name="saleTrend"
          @register="saleTrend_register"
          :datePicker="true"
          :api-url="API.GET_GOODS_SALE_TREND"
          :data-handler="saleTrendDataHandler"
        >
          <template #extraMiddleSlot>
            <div class="flex flex-row items-center px-5 py-4">
              <span>{{ L('筛选项：') }}</span>
              <RadioGroup
                v-model:value="saleTrend_radio"
                @change="saleTrend_radio_change"
                size="small"
              >
                <Radio :value="1">{{ L('新增商品数') }}</Radio>
                <Radio :value="2">{{ L('动销商品数') }}</Radio>
                <Radio :value="3">{{ L('下单数') }}</Radio>
                <Radio :value="4">{{ L('支付订单数') }}</Radio>
                <Radio :value="5">{{ L('支付金额') }}</Radio>
                <Radio :value="6">{{ L('下单金额') }}</Radio>
              </RadioGroup>
            </div>
          </template>
        </SldStatCharts>
      </div>
      <!-- 动销趋势end -->

      <!-- 商品销售变化趋势/商品销售排行start -->
      <div class="w-full mt-1 flex flex-row justify-center">
        <SldStatCharts
          name="goodsSaleTrend"
          :title="L('商品销售变化趋势') + ' - TOP10'"
          :datePicker="true"
          @register="goodsSaleTrend_register"
          :api-url="API.GET_GOODS_SALE_RANK"
          :data-handler="goodsSaleRankDataHandler"
        >
          <template #extraSlot>
            <RadioGroup
              v-model:value="goodsSaleTrend_radio"
              @change="goodsSaleRank_radio_change"
              size="small"
            >
              <RadioButton :value="1">{{ L('销售额') }}</RadioButton>
              <RadioButton :value="2">{{ L('销量') }}</RadioButton>
            </RadioGroup>
          </template>
        </SldStatCharts>
        <div class="min-w-3"></div>
        <SldStatRank
          :title="L('商品销售排行') + ' - TOP10'"
          :options="[
            {
              title: L('商品名称'),
              dataIndex: 'goodsName',
              customRenderType: 'goodsRender',
            },
            {
              title: L('销售额'),
              dataIndex: 'saleAmount',
              sorter: true,
              sortDirections: ['descend'],
              customRenderType: 'priceRender',
            },
            {
              title: L('销量'),
              dataIndex: 'saleNum',
              sorter: true,
              sortDirections: ['descend'],
            },
          ]"
          :date-picker="true"
          @register="goodsRankSort_register"
          :isIndex="true"
        ></SldStatRank>
      </div>
      <!-- 商品销售变化趋势/商品销售排行end -->

      <!-- 商品报表start--------------------- -->
      <div class="w-full mt-3 flex flex-row justify-center bg-white">
        <ReportWrapper :title="L('商品报表')" :export-option="currentExportOption">
          <Tabs type="card" v-model:active-key="reportTabValue">
            <TabPane :key="tab.key" :tab="tab.name" v-for="tab in reportOptionsSet">
              <ReportForm
                :columns="tab.columns"
                :searchData="tab.searchData"
                :beforeFetch="tab.beforeFetch"
                :api="tab.apiFunc"
                :isColumnIndex="tab.isColumnIndex"
              >
              </ReportForm>
            </TabPane>
          </Tabs>
        </ReportWrapper>
      </div>
      <!-- 商品报表end--------------------- -->
    </div>
  </div>
</template>

<script setup>
  import {
    handleGoodsOverView,
    handleSaleTrend,
    handleGoodsSaleTrend,
    handleReport,
  } from './actions/goods_sales';
  import { CountTo } from '@/components/CountTo';
  import {
    SldStatCharts,
    useStatCharts,
    SldStatRank,
    useStatRank,
    ReportForm,
    ReportWrapper,
  } from '@/components/SldStat';
  import { RadioGroup, Radio, Popover, RadioButton, TabPane, Tabs } from 'ant-design-vue';
  import {
    ArrowUpOutlined,
    ArrowDownOutlined,
    CaretDownOutlined,
    CheckOutlined,
  } from '@ant-design/icons-vue';
  import { API } from '@/api/sys/statAnalysis';
  import { getCurrentInstance, onMounted, ref } from 'vue';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  //start商品总览--------------------
  const { goodsPreviewData, changeSelData, selectData } = handleGoodsOverView();
  //end商品总览----------------------

  //商品动销趋势start---------------
  const { register: saleTrend_register, execDataHandler: saleTrend_execDataHandler } =
    useStatCharts();
  const { saleTrend_radio, saleTrendDataHandler, saleTrend_radio_change } =
    handleSaleTrend(saleTrend_execDataHandler);
  //商品动销趋势end-----------------

  const brandGoodRankSortHandler = (dataParam, sortOption) => {
    let param = { ...dataParam };
    if (sortOption.order) {
      param.sort = sortOption.field == 'saleNum' ? 2 : 1;
    }
    return param;
  };

  //商品销售排行start--------------
  const { register: goodsRankSort_register } = useStatRank({
    apiUrl: API.GET_GOODS_RANK,
    paramHandler: brandGoodRankSortHandler,
  });
  //商品销售排行end---------------

  //商品销售趋势start-----------------------------
  const { register: goodsSaleTrend_register, execDataHandler: goodsSaleTrend_execDataHander } =
    useStatCharts();
  const { goodsSaleTrend_radio, goodsSaleRankDataHandler, goodsSaleRank_radio_change } =
    handleGoodsSaleTrend(goodsSaleTrend_execDataHander);
  //商品销售趋势end----------------------------

  //商品报表start---------------------
  const { reportOptionsSet, currentExportOption, reportTabValue } = handleReport();
  //商品报表end---------------------
</script>

<style lang="less">
  .position-title {
    position: relative;
    height: 15px;
    padding-left: 8px;
    border-left: 4px solid @primary-color;
    color: #3a3e43;
    font-size: 14px;
    font-weight: 700;
    line-height: 15px;
    white-space: nowrap;
  }

  .vben-basic-help {
    display: inline;
  }

  .colorful_item {
    box-sizing: border-box;
    flex: 1;
    height: 163px;
    margin-right: 20px;
    padding: 12px 30px 12px 2.4%;
    background-repeat: no-repeat;
    background-position: top;
    background-size: auto 100%;

    &:last-child {
      margin-right: 0;
    }
  }
</style>
