<template>
  <div class="section_padding">
    <div class="section_padding_home_scroll">

      <!-- 会员总览start -->
      <div class="w-full bg-white">
        <div class="flex flex-row h-12 items-center pl-3 border-b-1 border-zinc-100">
          <div class="position-title font-bold mr-2"> {{ L('会员总览') }} </div>
        </div>
  
        <div class="p-5 flex_row_between_center">
          <div
            v-for="(item, index) in conbineData"
            :key="index"
            class="colorful_item flex flex-col justify-between"
            :style="{
              width: `${100 / conbineData.length - 1}%`,
              backgroundImage: `url(${item.bg})`,
            }"
          >
            <div class="flex flex-row justify-between items-center">
              <div class="text-sm text-white">{{ item.name }}</div>
              <div v-if="item.isShowOperate">
                <Popover placement="bottom" trigger="click">
                  <template #content>
                    <div class="bg-white w-17">
                      <div
                        v-for="(op, opdx) in selectData"
                        :key="opdx"
                        class="flex flex-row items-center py-1 justify-between hover:text-[#ff590d] cursor-pointer"
                        @click="changeSelData(item, opdx, index)"
                      >
                        <span class="">{{ op.label }}</span>
                        <CheckOutlined style="color: #ff590d" v-if="item.opValue == opdx" />
                      </div>
                    </div>
                  </template>
                  <div class="flex flex-row items-center cursor-pointer">
                    <span class="text-sm text-white">{{ selectData[item.opValue].label }}</span>
                    <CaretDownOutlined style="color: white; font-size: 15px" class="ml-1" />
                  </div>
                </Popover>
              </div>
            </div>
  
            <div>
              <div class="mt-3 text-3xl font-bold">
                <CountTo
                  :color="'#fff'"
                  :startVal="0"
                  :prefixStyle="{ fontSize: '17px' }"
                  :endVal="Number(item.num) || 0"
                  :duration="1000"
                />
              </div>
              <div class="w-1/2 mt-3 h-px bg-white"></div>
              <div
                class="flex flex-row items-center text-white mt-3"
                :class="{ invisible: !item.isShowOperate }"
              >
                <span>{{ L('较上期') }}</span>
                <div class="ml-2 font-bold">{{ item.differenceNum }}</div>
                <ArrowUpOutlined class="ml-1" v-if="item.isUp" style="font-weight: bold" />
                <ArrowDownOutlined class="ml-1" v-else style="font-weight: bold" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 会员总览end -->
  
      <!-- 变化趋势start -->
      <div class="w-full mt-1">
        <SldStatCharts
          :title="L('变化趋势')"
          :date-picker="true"
          :api-url="API.GET_MEMBER_30_TREND"
          :data-handler="trendDataHandler"
          @register="trend_register"
          mode="linear"
        >
          <template #extraMiddleSlot>
            <div class="flex flex-row items-center px-5 py-4">
              <div class="mr-3">{{ L('筛选项') }}</div>
              <RadioGroup size="small" @change="radioTrendChange" v-model:value="trendRadioValue">
                <Radio value="newMember"> {{ L('新增用户数') }} </Radio>
                <Radio value="visitor"> {{ L('店铺访客数') }} </Radio>
                <Radio value="submitMember"> {{ L('下单人数') }} </Radio>
                <Radio value="payMember">{{ L('支付人数') }}</Radio>
                <Radio value="collectionStore">{{ L('关注店铺数') }}</Radio>
              </RadioGroup>
            </div>
          </template>
        </SldStatCharts>
      </div>
      <!-- 变化趋势end -->
  
      <!-- 会员偏好商品排行/各终端会员占比start -->
      <div class="w-full mt-1 flex flex-row justify-between">
        <SldStatRank
          :title="L('会员偏好商品排行') + ' - TOP10'"
          :date-picker="true"
          @register="favRegister"
          :options="favOption"
          :isIndex="true"
        >
        </SldStatRank>
        <div class="min-w-3"></div>
        <SldStatCharts
          :title="L('支付客单价变化趋势')"
          :date-picker="true"
          :api-url="API.GET_ORDER_PAY_TREND"
          :data-handler="orderPayTrendDataHandler"
          @register="orderPay_trend_register"
          mode="linear"
        >
        </SldStatCharts>
      </div>
      <!-- 会员偏好商品排行/各终端会员占比end -->
  
      <!-- 会员报表start--------------------- -->
      <div class="w-full mt-3 flex flex-row">
        <ReportWrapper :title="L('会员报表')" :export-option="currentExportOption">
          <Tabs type="card" v-model:active-key="reportTabValue">
            <TabPane :key="tab.key" :tab="tab.name" v-for="tab in reportOptionsSet">
              <ReportForm
                :columns="tab.columns"
                :searchData="tab.searchData"
                :beforeFetch="tab.beforeFetch"
                :api="tab.apiFunc"
                :isColumnIndex="tab.isColumnIndex"
              >
              </ReportForm>
            </TabPane>
          </Tabs>
        </ReportWrapper>
      </div>
      <!-- 会员报表end--------------------- -->
    </div>
  </div>
</template>

<script setup>
  import {
    SldStatCharts,
    useStatCharts,
    SldStatRank,
    useStatRank,
    ReportForm,
    ReportWrapper,
  } from '@/components/SldStat';
  import { API } from '@/api/sys/statAnalysis';
  import { RadioGroup, Radio, RadioButton, Popover, TabPane, Tabs } from 'ant-design-vue';
  import { getCurrentInstance, ref } from 'vue';
  import { CountTo } from '@/components/CountTo';
  import { handleMemberOverView, handleReport } from './actions/member';
  import {
    ArrowUpOutlined,
    ArrowDownOutlined,
    CaretDownOutlined,
    CheckOutlined,
  } from '@ant-design/icons-vue';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  // 会员总览start---------------------------
  const { memberPreviewData, selectData, changeSelData, memeberNumData } = handleMemberOverView();
  const conbineData = ref([memeberNumData, ...memberPreviewData.value]);
  // 会员总览end-----------------------------

  //会员30天趋势start------------------
  const { register: trend_register, execDataHandler: trendExecDataHandler } = useStatCharts();
  const trendRadioValue = ref('newMember');
  const trendRadioMap = {
    newMember: 'newMemberNum',
    visitor: 'visitorNum',
    submitMember: 'orderSubmitMemberNum',
    payMember: 'orderPayMemberNum',
    collectionStore: 'collectionStoreNum',
  };
  const trendDataHandler = (return_data) => {
    let memberTrendOptions = {};
    const xAxisData = return_data.map((item) => item.statsTime);
    const seriesData = return_data.map((item) => item[trendRadioMap[trendRadioValue.value]]);
    let maxMount = Math.max.apply({}, seriesData);

    const series = [{ data: seriesData, type: 'line' }];
    memberTrendOptions.maxMount = maxMount;
    memberTrendOptions.series = series;
    memberTrendOptions.xAxisData = xAxisData;
    return memberTrendOptions;
  };
  const radioTrendChange = () => {
    trendExecDataHandler();
  };
  //会员30天趋势end------------------

  //会员终端占比start------------------------------
  const { register: orderPay_trend_register } = useStatCharts();
  const orderPayTrendDataHandler = (return_data) => {
    let orderPayTrendOptions = {};
    const xAxisData = return_data.map((item) => item.statsTime);
    const seriesData = return_data.map((item) => item.orderPayAtv);
    let maxMount = Math.max.apply({}, seriesData);

    const series = [{ data: seriesData, type: 'line' }];
    orderPayTrendOptions.maxMount = maxMount;
    orderPayTrendOptions.series = series;
    orderPayTrendOptions.xAxisData = xAxisData;
    return orderPayTrendOptions;
  };
  //会员终端占比end------------------------------

  //会员偏好商品排行start-------------------
  const favOption = [
    { title: L('商品名称'), dataIndex: 'goodsName', customRenderType: 'goodsRender' },
    {
      title: L('商品浏览量'),
      dataIndex: 'viewNum',
      sorter: true,
      sortDirections: ['descend'],
      width: 120,
    },
    {
      title: L('商品收藏数'),
      dataIndex: 'collectionNum',
      sorter: true,
      sortDirections: ['descend'],
      width: 120,
    },
    { title: L('支付订单数'), dataIndex: 'orderPayNum', sorter: true, sortDirections: ['descend'] },
  ];
  const favParamHandler = (date, sortP) => {
    let param = date;
    if (sortP.field) {
      param.sort = sortP.field == 'viewNum' ? 1 : sortP.field == 'collectionNum' ? 2 : 3;
    }
    return param;
  };
  const { register: favRegister } = useStatRank({
    apiUrl: API.GET_PREFER_GOODS_RANK,
    paramHandler: favParamHandler,
  });
  //会员偏好商品排行end-------------------

  //会员报表start-------------------------
  const { currentExportOption, reportOptionsSet, reportTabValue } = handleReport();
  //会员报表end-------------------------
</script>

<style lang="less">
  .colorful_item {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    height: 163px;
    padding: 12px 30px;
    box-sizing: border-box;

    &:last-child {
      margin-right: 0;
    }
  }
</style>
