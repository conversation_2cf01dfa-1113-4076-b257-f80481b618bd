<template>
  <div class="section_padding">
    <div class="section_padding_home_scroll">
      <!-- 流量总览start -->
      <div class="w-full bg-white">
        <div class="flex flex-row h-12 items-center pl-3 border-b-1 border-zinc-100">
          <div class="position-title font-bold mr-2"> {{ L('流量总览') }} </div>
          <DateMultiPicker name="over_view" @change="dateOverChange"></DateMultiPicker>
        </div>
        <div class="px-7">
          <div class="stat-num_stat_item flex flex-row" style="height: 180px">
            <div class="stat-left_side flex flex-col items-center justify-center">
              <img :src="flowData1.icon" alt="" width="40" height="40" />
              <div class="text-[14px] text-[#333] mt-2">{{ flowData1.title }}</div>
            </div>
  
            <div class="flex flex-row items-center" style="flex-wrap: wrap;">
              <div
                v-for="(item, index2) in flowData1.list"
                :key="index2"
                class="stat-right_main_item flex flex-row items-center"
              >
                <SldStatCard
                  :label="item.name"
                  :value="item.value"
                  :is-money="item.isMoney"
                  :tip="item.tip"
                  :is-growth="true"
                  :growth-text="L('较上期')"
                  :growth-value="item.preValue"
                  :is-growth-up="item.isUp"
                ></SldStatCard>
              </div>
            </div>
          </div>
        </div>
        <div class="px-7 pb-5">
          <div class="stat-num_stat_item flex flex-row" style="height: 180px">
            <div class="stat-left_side flex flex-col items-center justify-center">
              <img :src="flowData2.icon" alt="" width="40" height="40" />
              <div class="text-[14px] text-[#333] mt-2">{{ flowData2.title }}</div>
            </div>
  
            <div class="flex flex-row items-center" style="flex-wrap: wrap;">
              <div
                v-for="(item, index2) in flowData2.list"
                :key="index2"
                class="stat-right_main_item flex flex-row items-center"
              >
                <SldStatCard
                  :label="item.name"
                  :value="item.value"
                  :is-money="item.isMoney"
                  :isPercent="item.isPercent"
                  :tip="item.tip"
                  :is-growth="true"
                  :growth-text="L('较上期')"
                  :growth-value="item.preValue"
                  :is-growth-up="item.isUp"
                ></SldStatCard>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 流量总览end -->
  
      <!-- 30天变化趋势start -->
      <div class="w-full bg-white mt-3">
        <SldStatCharts
          :title="L('近30天变化趋势')"
          :api-url="API.GET_30_FLOW_TREND"
          :param-handler="trend30Flow_paramHandler"
          :data-handler="trendDataHandler"
          @register="trend30flow_Register"
        >
          <template #extraMiddleSlot>
            <div class="px-7 py-5">
              <div class="flex flex-row items-center">
                <div class="mr-3">{{ L('筛选项') }}</div>
                <RadioGroup size="small" v-model:value="current30Radio" @change="trend30RadioChange">
                  <Radio
                    :value="item.value"
                    class="day_size"
                    v-for="(item, index) in radio30Trend"
                    :key="index"
                    >{{ item.title }}</Radio
                  >
                </RadioGroup>
              </div>
            </div>
          </template>
        </SldStatCharts>
      </div>
      <!-- 30天变化趋势end -->
  
      <!-- 流量报表start--------------------- -->
      <div class="w-full mt-3 flex flex-row">
        <ReportWrapper :title="L('流量报表')" :export-option="currentExportOption">
          <ReportForm
            :columns="reportOptionsSet.columns"
            :searchData="reportOptionsSet.searchData"
            :beforeFetch="reportOptionsSet.beforeFetch"
            :api="reportOptionsSet.apiFunc"
            :isColumnIndex="reportOptionsSet.isColumnIndex"
          >
          </ReportForm>
        </ReportWrapper>
      </div>
      <!-- 流量报表end--------------------- -->
    </div>
  </div>
</template>

<script setup>
  import { getCurrentInstance, reactive, ref } from 'vue';
  import { API } from '@/api/sys/statAnalysis';
  import { RadioGroup, Radio, RadioButton, Tabs, TabPane } from 'ant-design-vue';
  import {
    SldStatCharts,
    useStatCharts,
    SldStatRank,
    useStatRank,
    SldStatCard,
    ConverImage,
    DateMultiPicker,
    ReportWrapper,
    ReportForm,
  } from '@/components/SldStat';

  import { trade30FlowTrend, handleFlowReport, flowOverView } from './actions/flow';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  //近30天变化趋势start-------------------------------
  const radioTerminalValue = ref('all');
  const {
    register: trend30flow_Register,
    execApi: trend30Flow_execApi,
    execDataHandler: trend30Flow_execDataHandler,
  } = useStatCharts();
  const trend30Flow_paramHandler = () => {
    let param = {};
    if (radioTerminalValue.value != 'all') {
      param.terminalTypes = radioTerminalValue.value;
    }
    return param;
  };
  const { current30Radio, radio30Trend, trend30RadioChange, trendDataHandler } = trade30FlowTrend(
    trend30Flow_execApi,
    trend30Flow_execDataHandler,
  );
  //近30天变化趋势end-------------------------------

  //流量报表start-------------------------------
  const { currentExportOption, reportOptionsSet, reportTabValue } = handleFlowReport();
  //流量报表end-------------------------------

  //流量总览start----------------------------
  const { flowData1, flowData2, dateOverChange } = flowOverView();
  //流量总览end-----------------------------
</script>

<style scoped lang="less">
  .position-title {
    position: relative;
    height: 15px;
    padding-left: 8px;
    border-left: 4px solid @primary-color;
    color: #3a3e43;
    font-size: 14px;
    font-weight: 700;
    line-height: 15px;
    white-space: nowrap;
  }

  .overViewBackground {
    background-color: #f5f5f5;
  }

  .stat-num_stat_item {
    box-sizing: border-box;
    align-items: stretch;
    width: 100%;
    height: 100%;
    padding: 20px 15px;
  }

  .stat-left_side {
    flex: 0 0 150px;
    background-color: #fff7f4;
  }

  .stat-right_main_item {
    width: 216px;
    height: 100%;
    background-color: hsla(0, 0%, 93.3%, 0.2);
    border-radius: 2px;
    padding-left: 35px;
    box-sizing: border-box;
    flex: 1 1;
  }
</style>
