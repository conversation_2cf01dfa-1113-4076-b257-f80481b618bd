<template>
  <div class="p-2">
    <div class="p-2 bg-white">
      <SldComHeader title="经营信息"></SldComHeader>
      <div :style="{ minHeight: minHight + 'px' }">
        <Form ref="busFormRef" :model="busFormState">
          <div class="mt-5">
            <Row type="flex">
              <Col span="15">
              <FormItem label="店铺名称" :required="true" name="storeName" :label-col="{ span: 6 }" :auto-link="false">
                <Input v-model:value="busFormState['storeName']" placeholder="请输入店铺名字，最多10字" :maxlength="10"
                  style="width: 80%" :disabled="disabledEdit" ></Input>
                <div class="ant-form-item-explain" :class="{ 'ant-form-item-explain-connected': !errMsg.storeName }">
                  <div class="ant-form-item-explain-error">{{ errMsg.storeName }}</div>
                </div>
              </FormItem>
              </Col>
              <Col flex="2">
              </Col>
            </Row>
            <div class="h-10"></div>
            <Row type="flex">
              <Col span="15">
              <FormItem label="店铺等级" name="storeGradeId" :require="true" :label-col="{ span: 6 }" :autoLink="false"
                ref="storeGradeIdRef">
                <BasicTable @register="standardTable" @selection-change="tableSelectChange">
                  <template #bodyCell="{ column, text }">
                    <template v-if="column.key == 'price'">
                      <div class="text_overflow_hidden_two" :title="text>0 ? `￥${Number(text).toFixed(2)}` : '￥0'">{{ text>0 ? `￥${Number(text).toFixed(2)}` : '￥0' }}</div>
                    </template>
                    <template v-else-if="column.key">
                      <div class="text_overflow_hidden_two" :title="text">{{ (text !== undefined && text !== null && text !== '') ? text : '--' }}</div>
                    </template>
                  </template>
                </BasicTable>
                <div class="ant-form-item-explain" :class="{ 'ant-form-item-explain-connected': !errMsg.storeGradeId }">
                  <div class="ant-form-item-explain-error">{{ errMsg.storeGradeId }}</div>
                </div>
              </FormItem>

              </Col>
              <Col flex="2">
              </Col>
            </Row>
            <div class="h-10"></div>

            <Row type="flex">
              <Col span="15">
              <FormItem label="开店时长" name="applyYear" :label-col="{ span: 6 }" :required="true" :autoLink="false"
                ref="applyYearRef">
                <Select placeholder="请选择开店时长" :options="openTimeOptions" :value="busFormState.applyYear"
                  @change="selectChange" :disabled="disabledEdit">
                </Select>
                <div class="ant-form-item-explain" :class="{ 'ant-form-item-explain-connected': !errMsg.applyYear }">
                  <div class="ant-form-item-explain-error">{{ errMsg.applyYear }}</div>
                </div>
              </FormItem>
              </Col>
              <Col flex="2">
              </Col>
            </Row>
            <div class="h-10"></div>

            <Row type="flex">
              <Col span="15">
              <FormItem label="经营类型" :required="true" name="checkedCatePath" :label-col="{ span: 6 }" :autoLink="false">
                <Tree checkable :tree-data="categoryList" :disabled="disabledEdit"
                  :checkedKeys="busFormState.checkedCateKeys !== undefined ? busFormState.checkedCateKeys : undefined"
                  :fieldNames="{ children: 'children', title: 'categoryName', key: 'categoryId' }" @check="treeCheck">
                </Tree>
                <div class="ant-form-item-explain"
                  :class="{ 'ant-form-item-explain-connected': !errMsg.checkedCatePath }">
                  <div class="ant-form-item-explain-error">{{ errMsg.checkedCatePath }}</div>
                </div>
              </FormItem>
              </Col>
              <Col flex="2">
              </Col>
            </Row>
          </div>
        </Form>
        <div class="settled_btn_list">
          <div class="settled_btn_pre" @click="preStep">上一步</div>
          <div v-if="!disabledEdit" class="settled_btn_next" @click="nextStep">提交审核</div>
          <div v-else class="settled_btn_next" @click="nextStep">下一步</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { defineComponent, onUnmounted, onMounted, ref, reactive } from 'vue';
import { PageWrapper } from '/@/components/Page';
import { Spin, Upload, Form, FormItem, Col, Row, Input, Select, Tree } from 'ant-design-vue';
import { saveStoreApplyApi, getStoreGradeApi, getStoreOpenTimeApi, getApplyCategoryApi } from '/@/api/settled/settled';
import { replaceEmoji, sucTip, failTip } from '/@/utils/utils';
import { useGo } from '/@/hooks/web/usePage';
import { useGlobSetting } from '/@/hooks/setting';
import { useTable, BasicTable } from '/@/components/Table';
import { isEmpty, isNullOrUnDef } from '/@/utils/is';
import { useUserStore } from '/@/store/modules/user';

const { apiUrl } = useGlobSetting();

export default defineComponent({
  name: 'business_info',
  components: {
    PageWrapper,
    [Spin.name]: Spin,
    [Select.name]: Select,
    ASelectOption: Select.Option,
    [Upload.name]: Upload,
    Form, FormItem,
    Col, Row, Input,
    BasicTable,
    Select,
    Tree
  },
  beforeRouteEnter(to, from, next) {
    if (localStorage.getItem('apply_state') != undefined) {
      next((vm) => {
        vm.disabledEdit = true;
      });
    } else {
      next((vm) => {
        vm.disabledEdit = false;
      });
    }
  },
  setup() {
    const go = useGo();
    const userStore = useUserStore();
    // 页面最小高
    const minHight = ref(document.body.clientHeight - 50 - 20 - 64);
    const spinning = ref(false);
    const disabledEdit = ref(false);
    const detail = ref({
      applyYear: undefined,
      storeName: '',
      storeGradeId: '',
      checkedCateKeys: [],
      checkedCatePath: [],
      goodsCategoryIds: ""
    });
    const errMsg = ref({}); //报错信息
    const industry_list = ref([]);
    const uploadLimit = ref(10); //上传文件大小限制
    const fileData = ref({}); //接口额外传参
    const imgToken = ref(''); //文件token
    const click_event = ref(false);
    const busFormState = reactive({
      applyYear: undefined,
      storeName: '',
      storeGradeId: '',
      checkedCateKeys: [],
      checkedCatePath: [],
      goodsCategoryIds: ""
    })
    const openTimeOptions = ref([])
    const categoryList = ref([])
    const busFormRef = ref()
    const storeGradeIdRef = ref()
    const applyYearRef = ref()
    const [standardTable, { setSelectedRowKeys }] = useTable({
      api: getStoreGradeApi,
      fetchSetting: {
        pageField: 'current',
        sizeField: 'pageSize',
        listField: 'data.list',
        totalField: 'data.pagination.total',
      },
      pagination: false,
      canResize: false,
      showIndexColumn: false,
      columns: [
        {
          width: 150,
          title: '店铺等级',
          dataIndex: 'gradeName'
        },
        {
          width: 150,
          title: '可发布商品',
          dataIndex: 'goodsLimit'
        },
        {
          width: 150,
          title: '可发推荐商品',
          dataIndex: 'recommendLimit'
        },
        {
          width: 150,
          title: '收费标准',
          dataIndex: 'price'
        },
        {
          width: 300,
          title: '申请说明',
          dataIndex: 'description'
        }
      ],
      rowKey: 'gradeId',
      clickToRowSelect: false,
      rowSelection: {
        type: 'radio',
        getCheckboxProps(record) {
          if (disabledEdit.value) {
            return { disabled: true };
          } else {
            return { disabled: false };
          }
        },
      },
    })

    function saveSettleData(key, val) {
      if (Object.prototype.toString.call(val) === '[object Array]' || Object.prototype.toString.call(val) === '[object Object]') {
        //数组或对象
        localStorage.setItem(key, JSON.stringify(val));
      } else {
        localStorage.setItem(key, val);
      }
    }

    const tableSelectChange = (e) => {
      let { rows } = e
      if (e && rows && rows.length > 0) {
        busFormState.storeGradeId = rows[0].gradeId
      }
    }

    const selectChange = (e) => {
      busFormState.applyYear = e
    }

    const handleApi = async () => {
      const cate_res = await getApplyCategoryApi()
      categoryList.value = cate_res?.data

      const open_res = await getStoreOpenTimeApi()
      openTimeOptions.value = open_res?.data?.map(item => ({ label: `${item}年`, value: item }))

    }

    onMounted(() => {
      handleApi();
      if (localStorage.getItem('apply_state') != undefined) {
        disabledEdit.value = true;
      } else if (
        localStorage.getItem('cur_step') != undefined &&
        localStorage.getItem('cur_step') != '2'
      ) {
        history.back();
        return;
      }
      window.addEventListener('resize', onResize, true);
      if (
        localStorage.getItem('business_info') != undefined &&
        localStorage.getItem('business_info')
      ) {
        let data = JSON.parse(localStorage.getItem('business_info') || '{}');
        for (let key in detail.value) {
          if (key == 'goodsCategoryIds') {
            detail.value[key] = data[key] != undefined ? data[key].split(',') : undefined;
          } else if (key == 'storeGradeId') {
            detail.value[key] = data[key] != undefined ? data[key] : undefined;
            if (data[key]) {
              setSelectedRowKeys([data[key]]);
            }
          } else {
            detail.value[key] = data[key] != undefined ? data[key] : undefined;
          }
          busFormState[key] = detail.value[key];
        }
      }
    });

    const treeCheck = (checkedKeys, e) => {
      let select_cat_id = []
      if (e.checkedNodes.length > 0) {
        e.checkedNodes.map(item_one => {
          if (item_one.grade == 3) {
            let tmp_data = item_one.path.split('/');
            select_cat_id.push(`${tmp_data[1]}-${tmp_data[2]}-${item_one.categoryId}`);
          }
        })
      }
      busFormState.checkedCatePath = select_cat_id
      busFormState.goodsCategoryIds = select_cat_id.toString()
      busFormState.checkedCateKeys = checkedKeys;
    }

    // 卸载onResize事件
    onUnmounted(() => {
      window.removeEventListener('resize', onResize, true);
    });

    // 更新页面高度
    const onResize = () => {
      minHight.value = document.body.clientHeight - 50 - 20 - 44;
    };

    //上一步
    function preStep() {
      localStorage.setItem('cur_step', '1');
      go('/apply/base_info');
    }


    const diyValidate = () => {
      let rules = {
        storeName: {
          message: '请选择店铺名称'
        },
        storeGradeId: {
          message: '请选择店铺等级'
        },
        applyYear: {
          message: '请选择开店时长'
        },
        checkedCatePath: {
          message: '请选择经营类型'
        }
      }
      let check_flag = true
      for (let key in rules) {
        if (!handleCheck(key, rules[key].message)) {
          check_flag = false
        }
      }

      if (!check_flag) {
        throw new Error('error validate')
      }
    }

    //下一步
    function nextStep() {
      if (disabledEdit.value) {
        localStorage.setItem('cur_step', '3');
        go('/apply/open_up');
        return;
      }
      try {
        diyValidate()
        let baseInfoRaw = localStorage.getItem('base_info')
        let base_info = JSON.parse(baseInfoRaw.replace(/&quot;/g, "\""));
        saveSettleData('bussinessInfo', JSON.stringify(busFormState));//经营信息存缓存

        let tar_params = {};

        for (let keybus in busFormState) {
          if (keybus !== 'checkedCateKeys' && keybus !== 'checkedCatePath') {
            tar_params[keybus] = busFormState[keybus]
          }
        }

        for (let key in base_info) {
          if (key.indexOf('Url') < 0) {
            tar_params[key] = base_info[key]
          }
        }
        if(tar_params.goodsCategoryIds&&Array.isArray(tar_params.goodsCategoryIds)){
          tar_params.goodsCategoryIds = tar_params.goodsCategoryIds.join(',')
        }
        saveStoreApply(tar_params);
      } catch (err) {}
    }

    //提交数据
    const saveStoreApply = async (params) => {
      const res = await saveStoreApplyApi(params);
      click_event.value = false;
      if (res.state == 200) {
        sucTip(res.msg);
        userStore.setDelKeepAlive(['protocol', 'base_info', 'business_info', 'open_up']);
        localStorage.setItem('cur_step', '3');
        setTimeout(() => {
          go('/apply/open_up');
        }, 50)
      } else {
        failTip(res.msg);
      }
    };

    function handleCheck(flag_key, msg) {
      if (isEmpty(busFormState[flag_key]) || isNullOrUnDef(busFormState[flag_key])) {
        errMsg.value[flag_key] = msg
        return false
      }
      errMsg.value[flag_key] = ''
      return true
    }

    return {
      storeGradeIdRef,
      applyYearRef,
      busFormRef,
      treeCheck,
      spinning,
      detail,
      errMsg,
      disabledEdit,
      industry_list,
      uploadLimit,
      fileData,
      imgToken,
      apiUrl,
      click_event,
      busFormState,
      preStep,
      nextStep,
      saveStoreApply,
      minHight,
      handleCheck,
      standardTable,
      openTimeOptions,
      categoryList,
      tableSelectChange,
      selectChange,
    };
  },
});
</script>
<style lang="less" scoped>
.settled_info {
  padding: 0 20px 40px;

  .settled_info_title {
    margin-top: 15px;
    margin-bottom: 15px;
    margin-left: 5px;
    color: #333;
    font-size: 14px;
    font-weight: bold;
  }

  .settled_info_item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin: 20px 0 25px;

    .settled_info_item_title {
      width: 80px;
      margin-right: 20px;
      color: #333;
      font-size: 13px;
      font-weight: 500;
      text-align: right;

      span {
        color: #f00;
      }
    }

    .settled_info_item_main {
      width: 400px;

      .error_msg {
        height: 0;
        color: #ed6f6f;
      }

      &.settled_info_item_main_width {
        width: 660px;
      }

      .settled_info_item_main_btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100px;
        height: 30px;
        padding: 0;
        border: 1px solid @primary-color;
        border-radius: 3px;
        color: @primary-color;
        font-size: 13px;
        cursor: pointer;
      }

      .settled_info_item_main_spec {
        .settled_info_item_main_spec_item {
          position: relative;
          margin-top: 15px;
          padding: 7px 15px 20px;
          background: rgb(249 249 249);

          .settled_info_item_main_spec_item_util {
            position: absolute;
            top: 5px;
            right: 15px;

            .settled_info_item_main_spec_item_utils {
              width: 16px;
              height: 16px;
              cursor: pointer;
            }
          }

          .settled_info_item_main_spec_item_title {
            margin-top: 5px;
            margin-bottom: 4px;
            color: #333;
            font-size: 14px;
            font-weight: 500;
          }

          .settled_info_item_main_spec_item_info {
            display: flex;
            align-items: center;
            justify-content: flex-start;

            .settled_info_item_main_spec_item_info_item {
              display: flex;
              align-items: center;
              justify-content: flex-start;
              width: 300px;
              margin-right: 30px;

              .settled_info_item_main_spec_item_info_item_span {
                margin-right: 8px;
                color: #999;
                font-size: 12px;
                white-space: nowrap;
              }
            }
          }

          .settled_info_item_main_spec_item_list {
            .settled_info_item_main_spec_item_del {
              padding: 3px 7px 5px;
              transition: all 0.2s ease;
              border-radius: 3px;
              color: #333;
              font-size: 13px;
              white-space: nowrap;
              cursor: pointer;

              &:hover {
                background-color: @primary-color;
                color: #fff;
              }
            }
          }

          .settled_info_item_main_spec_item_btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 105px;
            height: 32px;
            margin-top: 15px;
            border: 1px dashed #d2d2d2;
            border-radius: 3px;
            color: @primary-color;
            font-family: 'Microsoft YaHei';
            font-size: 13px;
            font-weight: 400;
            cursor: pointer;
          }
        }
      }

      &.settled_info_item_upload {
        .settled_info_item_upload_btn {
          color: rgb(61 133 255);
          font-size: 13px;
          cursor: pointer;
        }

        .settled_info_item_upload_desc {
          margin-top: 12px;
          color: #999;
          font-size: 12px;
        }
      }
    }
  }
}

.settled_btn_list {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px auto;

  .settled_btn_pre,
  .settled_btn_next {
    width: 100px;
    height: 37px;
    border-radius: 4px;
    font-size: 14px;
    line-height: 35px;
    text-align: center;
    cursor: pointer;
  }

  .settled_btn_pre {
    transition: all 0.5s ease;
    border: 1px solid #aaa;
    color: #666;

    &:hover {
      border-color: @primary-color;
      color: @primary-color;
    }
  }

  .settled_btn_next {
    margin-left: 30px;
    border: 1px solid @primary-color;
    background: @primary-color;
    color: #fff;
  }
}
</style>
