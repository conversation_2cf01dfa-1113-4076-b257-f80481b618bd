<template>
  <div class="p-2">
    <div class="p-2 bg-white">
      <SldComHeader :title="L('店铺入驻结果')"></SldComHeader>
      <div class="commen_wrap">
        <img class="apply_state_img" :src="state_img_data.apply_pending" v-if="apply_progress.applyStep == 1" />
        <img class="apply_state_img" :src="state_img_data.apply_success"
          v-if="apply_progress.applyStep == 2 || apply_progress.applyStep == 4" />
        <img class="apply_state_img" :src="state_img_data.apply_fail" v-if="apply_progress.applyStep == 3" />
        <p class="apply_state_con">{{ apply_progress.stateValue }}</p>

        <div class="check_fail_wrap flex_column_center_center" v-if="apply_progress.applyStep == 3">
          <p>{{ L('审核拒绝原因') }}：{{ apply_progress.refuseReason }}</p>
          <p v-if="apply_progress.auditInfo">{{ L('备注') }}：{{ apply_progress.auditInfo }}</p>
        </div>

        <div :style="{ height: 1 }" class="split_line"></div>

        <!-- 待付款2 start -->
        <div v-if="apply_progress.applyStep == 2">
          <div class="settle_pay">
            <RadioGroup v-if="!payFlag&&firstFlag" @change="handlePayMethod" v-model:value="pay_method">
              <Radio value='alipay'>
                <div class="pay_item flex_row_center_center pay_item_mr">
                  <img class="pay_item_img" :src="state_img_data.aliPayImage" />
                </div>
              </Radio>
              <Radio value='wx'>
                <div class="pay_item flex_row_center_center">
                  <img class="pay_item_img" :src="state_img_data.wxPayImage" />
                </div>
              </Radio>
            </RadioGroup>
            
          </div>
          <div class="flex_column_center_center">
            <div class="pay_line" />
            <a class="go_pay" @click="goPay">{{ L('立即付款') }}</a>
          </div>
        </div>
        <!-- 待付款2 end -->

        <!-- 待审核1和待付款2的付款清单和经营类型信息 start -->
        <div v-if="apply_progress.applyStep == 1 || apply_progress.applyStep == 2">
          <div>
            <p class="flex_row_start_center table_title">{{ L('付费清单列表') }}</p>
            <div>
              <!-- <SldTableRowTwo r_color={'#333'} l_color={'#999'} l_fontw={500} r_fontw={600} form={this.props.form}
                           data={store_pay_data}/> -->

              <Row type="flex">
                <Col flex="1">
                <Row type="flex" v-for="(item, index) in store_pay_data" :key="index">
                  <Col flex="1">
                  <div class="text-right col_title">{{ item.label }}</div>
                  </Col>
                  <Col flex="3">
                  <div class="col_value col_title">{{ item.text }}</div>
                  </Col>
                </Row>
                </Col>
                <Col flex="1">
                </Col>
              </Row>

            </div>
          </div>
          <div>
            <p class="flex_row_start_center table_title">{{ L('经营类目列表') }}</p>
            <div class="flex_row_center_center">
              <Table bordered style="width:100%" rowKey='goodsCategoryId3' :pagination="false"
                :columns="columns_apply_cat" :dataSource="apply_progress.storeGoodsCateVOList" size='small' />
            </div>
          </div>
        </div>
        <!-- 待审核1和待付款2的付款清单和经营类型信息 end -->

        <div class="flex_row_center_center">
          <!-- 审核拒绝3 start -->
          <a class="operate_btn" @click="changeInfo" v-if="apply_progress.applyStep == 3">
            {{ L('修改信息') }}
          </a>
          <!-- 审核拒绝3 end -->

          <!-- 入驻完成4 start -->
          <a class="operate_btn" @click="goStore" v-if="apply_progress.applyStep == 4">
            {{ L('进入商户后台') }}
          </a>
          <!-- 入驻完成4 end -->
        </div>
      </div>

      <!-- 微信支付弹框 start -->
      <Modal :destroyOnClose="true" :maskClosable="false" :title="pay_title" :width="400" :visible="modalVisible"
        @ok="sldConfirm" @cancel="sldCancle">
        <template #footer>
          <Button key="back" @click="sldCancle">{{ L('取消') }}</Button>,
          <Button key="submit" type="primary" :loading="submiting" @click="sldConfirm">
            {{ L('已支付') }}
          </Button>,
        </template>
        <div class="flex_column_center_center wx_pay_qrcode">
          <img :src="wx_pay_qrcode" v-if="!payFlag" />
          
          <p class="pay_tip">
            {{ L('使用') }}
            <span style="color: '#228AFF'" v-if="pay_wx=='wx'" >
              {{ L('微信APP') }}
            </span>
            <span style="color: '#228AFF'" v-if="pay_wx!='wx'" >
              {{ L('支付宝支付APP') }}
            </span>
            {{ L('扫码支付') }}
          </p>
          <a class="wx_refresh" @click="refreshWxQrcode">{{ L('刷新') }}</a>
        </div>
      </Modal>
      <!-- 微信支付弹框 end -->
    </div>
  </div>
</template>
<script>
  export default {
    name: 'open_up',
  };
</script>
<script setup>
import { ref, onMounted, onUnmounted, getCurrentInstance } from 'vue';
import { getApplyDetailApi, getApplyProgressApi, registerPayApi } from '/@/api/settled/settled';
import { RadioGroup, Radio, Table, Row, Col, Button, Modal } from 'ant-design-vue';
import { failTip } from '@/utils/utils';
import { useGo } from '/@/hooks/web/usePage';
import { getImagePath } from '/@/utils';
import { useUserStore } from '/@/store/modules/user';
// dev_tong-start
import { QrCode } from '/@/components/Qrcode/index';
// dev_tong-end

const userStore = useUserStore();
const vm = getCurrentInstance();
const L = vm?.appContext.config.globalProperties.$sldComLanguage;

const payFlag = ref(false)
const pay_title = ref('微信支付')
const pay_wx = ref('wx')
const firstFlag = ref(false)

const state_img_data = {
  apply_success: getImagePath('images/apply_success.png'),
  apply_pending: getImagePath('images/apply_pending.png'),
  apply_fail: getImagePath('images/apply_fail.png'),
  aliPayImage: getImagePath('images/alipay.png'),
  wxPayImage: getImagePath('images/wx.png'),
  };
const apply_progress = ref({})
const pay_method = ref('alipay')
const submiting = ref(false)
const modalVisible = ref(false)
const wx_pay_qrcode = ref('')

const columns_apply_cat = [{
  title: '一级类目',
  dataIndex: 'goodsCateName1',
  align: 'center',
  width: 100,
}, {
  title: '二级类目',
  dataIndex: 'goodsCateName2',
  align: 'center',
  width: 100,
}, {
  title: '三级类目',
  dataIndex: 'goodsCateName3',
  align: 'center',
  width: 100,
}, {
  title: '佣金比例',
  dataIndex: 'scaling',
  align: 'center',
  width: 100,
  render: (text, record) => {
    return (text * 100).toFixed(1) + '%'
  },
}];

const store_pay_data = ref([{
  type: 'show_text',
  label: `${L('店铺等级')}`,
  name: 'gradeName',
  extra: ``,
  item_height: 42,
  text: ``,
}, {
  type: 'show_text',
  label: `${L('收费标准')}`,
  name: 'price',
  extra: ``,
  item_height: 42,
  text: ``,
}, {
  type: 'show_text',
  label: `${L('开店时长')}`,
  name: 'applyYear',
  extra: ``,
  item_height: 42,
  text: ``,
}, {
  type: 'show_text',
  label: `${L('应付金额')}`,
  name: 'payAmount',
  extra: ``,
  item_height: 42,
  text: ``,
}])//付费清单数据

const go = useGo();
// 页面最小高
const minHight = ref(document.body.clientHeight - 50 - 20 - 42);

//获取入驻信息
const getApplyDetail = async () => {
  const res = await getApplyDetailApi();
  if (res.state == 200) {
    let result = res.data
    let base_info = {};
    //基本信息
    base_info.enterType = result.enterType;
    // dev_o2o_supplier-start
    base_info.shopType = result.shopType;
    // dev_o2o_supplier-end
    
    base_info.companyProvinceCode = result.companyProvinceCode;
    base_info.companyCityCode = result.companyCityCode;
    base_info.companyAreaCode = result.companyAreaCode;
    base_info.area = [base_info.companyProvinceCode, base_info.companyCityCode, base_info.companyAreaCode];
    base_info.companyAddress = result.companyAddress;
    base_info.areaInfo = result.areaInfo;//省市区组合
    base_info.contactName = result.contactName;
    base_info.contactPhone = result.contactPhone;
    base_info.personCardUp = result.personCardUp;
    base_info.personCardUpUrl = result.personCardUpPath;
    base_info.personCardDown = result.personCardDown;
    base_info.personCardDownUrl = result.personCardDownPath;
    if (result.enterType == 1) {
      //企业入驻
      base_info.companyName = result.companyName;
      base_info.businessLicenseImage = result.businessLicenseImage;
      base_info.businessLicenseImageUrl = result.businessLicenseImagePath;
      if (result.moreQualification1) {
        base_info.moreQualification1 = result.moreQualification1;
        base_info.moreQualification1Url = result.moreQualification1Path;
      }
      if (result.moreQualification2) {
        base_info.moreQualification2 = result.moreQualification2;
        base_info.moreQualification2Url = result.moreQualification2Path;
      }
      if (result.moreQualification3) {
        base_info.moreQualification3 = result.moreQualification3;
        base_info.moreQualification3Url = result.moreQualification3Path;
      }
    }
    localStorage.setItem('baseInfo', JSON.stringify(base_info));//基本信息存缓存
    //经营信息
    let business_info = {};
    business_info.applyYear = result.applyYear;
    business_info.storeName = result.storeName;//店铺名称
    business_info.storeGradeId = result.storeGradeId;//店铺等级
    //经营类目信息
    let sel_cat_id_array = [];//选择的分类id
    let select_cat_id_three = [];//选择的分类id，eg：1级-2级-3级
    result.storeGoodsCateVOList.map(item => {
      sel_cat_id_array.push(item.goodsCategoryId3);
      select_cat_id_three.push(`${item.goodsCategoryId1}-${item.goodsCategoryId2}-${item.goodsCategoryId3}`);
    });
    business_info.checkedCatePath = select_cat_id_three
    business_info.goodsCategoryIds = select_cat_id_three.join(',');//申请分类id字符串,例1级-2级-3级;1级-2级-3级
    business_info.checkedCateKeys = sel_cat_id_array;//选择的分类id数组，用于选中分类tree
    localStorage.setItem('agree_protocol', 'true');
    localStorage.setItem('base_info', JSON.stringify(base_info));
    localStorage.setItem('business_info', JSON.stringify(business_info));
  } else {
    failTip(res.msg);
  }
};

//获取入驻进度
const getApplyProgress = async () => {
  const res = await getApplyProgressApi();
  if (res.state == 200) {
    store_pay_data.value.map(item => {
      item.text = res.data[item.name];
    });
    if (res.data.applyStep != 2) {
      modalVisible.value = false;
    }
    apply_progress.value = res.data

    localStorage.setItem('apply_state', res.data.applyStep.toString());
  } else {
    failTip(res.msg);
  }
};

//修改信息
function changeInfo() {
  userStore.setDelKeepAlive(['protocol', 'base_info', 'business_info', 'open_up']);
  localStorage.removeItem('apply_state');
  localStorage.setItem('cur_step', '1');
  setTimeout(() => {
    go('/apply/base_info');
  }, 50)
}

onMounted(() => {
  if (
    !localStorage.getItem('apply_state') &&
    localStorage.getItem('cur_step') != undefined &&
    localStorage.getItem('cur_step') != '3'
  ) {
    localStorage.removeItem('apply_state');
    history.back();
    return;
  }
  window.addEventListener('resize', onResize, true);
  getApplyDetail();
  getApplyProgress();
  firstFlag.value = true
  payFlag.value = false
});

// 卸载onResize事件
onUnmounted(() => {
  window.removeEventListener('resize', onResize, true);
});

// 更新页面高度
const onResize = () => {
  minHight.value = document.body.clientHeight - 50 - 20 - 42;
};

//支付
let timer
const goPay = () => {
  registerPayApi({ payMethod: pay_method.value, paySn: apply_progress.value.paySn,payType:'NATIVE' }).then(res => {
    if (res.state == 200) {
      if (pay_method.value == 'alipay') {
        document.write(res.data.payData);//自动提交表单数据
      } else if (pay_method.value == 'wx') {
        pay_title.value = '微信支付'
        pay_wx.value = 'wx'
        modalVisible.value = true
        wx_pay_qrcode.value = 'data:image/png;base64,' + res.data.payData
        // 定时查询是否支付成功
        if (!timer) {
          timer = setInterval(() => {
            getApplyProgress();
          }, 5000);
        }
      }

      }else{
      failTip(res.msg)
    }
  })
};

//刷新微信支付二维码
const refreshWxQrcode = () => {
  goPay();
};

//进入商户后台
const goStore = () => {
  userStore.loginOut();
};

const sldCancle = () => {
  modalVisible.value = false;
};

const sldConfirm = () => {
  modalVisible.value = false;
};

</script>
<style lang="less" scoped>
.settled_open_up {
  display: flex;
  position: relative;
  bottom: 15px;
  flex-direction: column;
  align-items: center;
  margin: 0 15px;
  background-color: #fff;
  justify-items: flex-start;

  .settled_open_up_state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 25px 0;

    .settled_open_up_state_img {
      width: 169px;
      height: 131px;
    }

    .settled_open_up_state_title {
      margin-top: 20px;
      margin-bottom: 15px;
      color: #666;
      font-family: 'Microsoft YaHei';
      font-size: 20px;
      font-weight: 400;
      line-height: 18px;
    }

    .settled_open_up_state_time {
      margin-bottom: 10px;
    }
  }

  .settled_open_up_table {
    width: 95%;
    margin-bottom: 20px;
  }

  .business_load {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-start;

    .business_load_img {
      width: 100px;
      height: 100px;
      margin: 0 5px;
    }
  }

  .settled_open_up_state_btn {
    display: inline-block;
    height: 30px;
    margin-top: 30px;
    margin-bottom: 30px;
    padding: 0 22px;
    border-radius: 4px;
    background: #3d85ff;
    color: #fff;
    font-size: 13px;
    font-weight: bold;
    line-height: 30px;
    cursor: pointer;
  }
}


.title {
  padding: 10px 15px 15px 15px;
  background-color: #fff;
  width: 100%;
  border-radius: 15px;

  .third_party {
    font-size: 12px;

  }
}

.settle_pay {
  margin: 30px 0;
}

.pay_item {
  width: 160px;
  height: 60px;
  background: #FFF;
  border: 1px solid #f2f2f2;
  border-radius: 3px;
  display: inline-block;

  .pay_item_img {
    width: 105px;
    height: 35px;
    margin-top: 11px;
  }
}

.pay_item_mr {
  margin-right: 7vw;
}

.pay_line {
  width: 70%;
  height: 1px;
  background: #F2F2F2;
}

.go_pay {
  width: 120px;
  height: 35px;
  background: #FC1C1C;
  border-radius: 5px;
  font-size: 17px;
  font-family: 'Microsoft YaHei';
  font-weight: bold;
  color: #FFF;
  line-height: 35px;
  margin: 30px 0;
  cursor: pointer;

  &:hover {
    color: #fff !important;
  }
}

.commen_wrap {
  background: #fff;
  margin-top: 50px;
  text-align: center;
  padding: 0 20px 10px;
  height: calc(100vh - 190px);
  overflow-y: auto;

  .apply_state_img {
    width: 169px;
    height: 131px;
  }

  .apply_state_con {
    font-size: 20px;
    font-family: 'Microsoft YaHei';
    font-weight: 400;
    color: #666;
    line-height: 18px;
    margin-top: 20px;
    margin-bottom: 30px;
  }

  .split_line {
    width: 100%;
    height: 1px;
    background: #F2F2F2;
  }

  .table_title {
    font-size: 14px;
    color: #333;
    margin-top: 20px;
    margin-bottom: 10px;
  }

  .operate_btn {
    height: 30px;
    line-height: 30px;
    background: #FF782D;
    border-radius: 2px;
    font-size: 13px;
    padding: 0 23px;
    color: #fff;
    margin-top: 30px;
    font-weight: bold;

    &:hover {
      color: #fff !important;
    }
  }

}

.wx_pay_qrcode {
  img {
    width: 170px;
    height: 170px;
    margin-top: 25px;
    margin-bottom: 20px;
    border: 1px solid #EEE;
    box-shadow: 0 0 20px 0 rgba(153, 153, 153, 0.2);
    border-radius: 3px;
  }

  .pay_tip {
    color: #999;
    font-size: 14px;
  }

  .wx_refresh {
    display: inline-block;
    width: 97px;
    height: 25px;
    line-height: 25px;
    background: #FF4C4C;
    border-radius: 3px;
    color: #fff;
    font-size: 12px;
    margin-top: 10px;
    outline: none;
    text-align: center;
    margin-bottom: 25px;
  }
}

.check_fail_wrap {
  background: #FFF;
  margin-bottom: 23px;

  p {
    color: #666;
    font-size: 12px;
    line-height: 20px;
  }
}

.col_title {
  height: 44px;
  border: 1px solid #f0f0f0;
  padding: 0 10px;
  line-height: 44px;
  color: rgb(153, 153, 153);
}

.col_value {
  color: rgb(51, 51, 51);
}
</style>
