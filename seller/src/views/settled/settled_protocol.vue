<template>
  <a-spin :spinning="spinning">
    <PageWrapper
      title="入驻协议"
      contentBackground
      style="margin: 0 10px; padding-top: 10px; padding-bottom: 10px"
    >
      <div :style="{ minHeight: minHight + 'px', background: '#fff' }">
        <Spin :spinning="loading">
          <div class="settled_protocol">
            <div class="settled_protocol_title">{{ title }}</div>
            <div class="settled_protocol_content" v-html="content"></div>
          </div>
        </Spin>
        <div class="settled_check">
          <Checkbox v-model:checked="checked" :disabled="disabledEdit">
            我已阅读并同意以上协议
          </Checkbox>
        </div>
        <div class="settled_btn" :class="checked ? 'active' : ''" @click="nextStep">下一步</div>
      </div>
    </PageWrapper>
  </a-spin>
</template>
<script>
  import { defineComponent, onUnmounted, onMounted, ref } from 'vue';
  import { PageWrapper } from '/@/components/Page';
  import { getProtocolApi } from '/@/api/settled/settled';
  import { quillEscapeToHtml ,sucTip, failTip} from '/@/utils/utils';
  import { Spin, Checkbox } from 'ant-design-vue';
  import { useGo } from '/@/hooks/web/usePage';

  export default defineComponent({
    name: 'protocol',
    components: {
      PageWrapper,
      Checkbox,
      Spin,
    },
    beforeRouteEnter(to, from, next) {
      if (localStorage.getItem('apply_state') != undefined) {
        next((vm) => {
          vm.disabledEdit = true;
        });
      } else {
        next((vm) => {
          vm.disabledEdit = false;
        });
      }
    },
    setup() {
      const go = useGo();
      // 页面最小高
      const minHight = ref(document.body.clientHeight - 50 - 20 - 44);
      const spinning = ref(true);
      const disabledEdit = ref(false);
      const title = ref('');
      const content = ref('');
      const checked = ref(false);
      const loading = ref(false);

      //获取协议
      const getProtocol = async () => {
        loading.value = true;
        const res = await getProtocolApi({ agreementCode: 'business_residence_agreement' });
        loading.value = false;
        if (res.state == 200) {
          title.value = res.data.title;
          content.value = quillEscapeToHtml(res.data.content);
          spinning.value = false;
        } else {
          failTip(res.msg);
        }
      };

      //下一步
      function nextStep() {
        if (checked.value) {
          localStorage.setItem('cur_step', '1');
          if (!disabledEdit.value) {
            localStorage.setItem('agree_protocol', 'true');
          }
          go('/apply/base_info');
        }
      }

      onMounted(() => {
        if (localStorage.getItem('apply_state') != undefined) {
          disabledEdit.value = true;
        } else if (
          localStorage.getItem('cur_step') != undefined &&
          localStorage.getItem('cur_step') != '0'
        ) {
          history.back();
          return;
        }
        window.addEventListener('resize', onResize, true);
        if (
          localStorage.getItem('agree_protocol') != undefined &&
          localStorage.getItem('agree_protocol') == 'true'
        ) {
          checked.value = true;
          if (
            !localStorage.getItem('apply_state') &&
            (!localStorage.getItem('cur_step') || localStorage.getItem('cur_step') != '0')
          ) {
            nextStep();
          } else {
            getProtocol();
          }
        } else {
          getProtocol();
        }
      });

      // 卸载onResize事件
      onUnmounted(() => {
        window.removeEventListener('resize', onResize, true);
      });

      // 更新页面高度
      const onResize = () => {
        minHight.value = document.body.clientHeight - 50 - 20 - 44;
      };

      return {
        spinning,
        disabledEdit,
        title,
        content,
        checked,
        getProtocol,
        nextStep,
        minHight,
        loading,
      };
    },
  });
</script>
<style lang="less" scoped>
  .ant-layout {
    background: #fff;
  }

  .settled_protocol {
    min-height: 341px;
    margin: 0 20px;
    padding: 20px;
    border: 1px solid rgb(0 0 0 / 10%);
    border-radius: 10px;
    background: #fff;

    .settled_protocol_title {
      margin-bottom: 15px;
      color: #333;
      font-size: 16px;
      text-align: center;
    }

    .settled_protocol_content {
      p {
        margin-bottom: 10px;
        line-height: 22px;
        word-break: break-all;
      }
    }
  }

  .settled_check {
    margin-top: 15px;
    margin-left: 22px;
  }

  .settled_btn {
    width: 100px;
    height: 37px;
    margin: 50px auto 20px;
    transition: all 0.5s ease;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background: #f5f5f5;
    color: rgb(0 0 0 / 25%);
    font-size: 14px;
    line-height: 37px;
    text-align: center;
    cursor: not-allowed;

    &.active {
      border-color: @primary-color;
      background: @primary-color;
      color: #fff;
      cursor: pointer;
    }
  }
</style>
