<template>
  <Spin :spinning="spinning">
    <div class="section_padding">
      <div class="section_padding_back">
        <SldComHeader title="店铺信息"></SldComHeader>

        <div class="mt-5 apply_detail base_info_apply">


          <div>
            <div class="apply_title_bg">
              <span class="title">入驻类型</span>
            </div>
            <div class="mt-5">
              <Row type="flex">
                <Col type="flex" :span="12">
                <FormItem label="入驻类型" :required="true" :label-col="{ span: 6 }">
                  <RadioGroup v-model:value="cur_apply_type" @change="cur_apply_type_change_base">
                    <Radio :value="1">企业入驻</Radio>
                    <Radio :value="0" v-if="!payFlag" :disabled="true">个人入驻</Radio>
                  </RadioGroup>
                </FormItem>
                </Col>
              </Row>
              <!-- dev_supplier_o2o-start -->
              <Row type="flex">
                <Col type="flex" :span="12">
                <FormItem label="店铺类型" :required="true" :label-col="{ span: 6 }">
                  <RadioGroup :disabled="dict_disabled" v-model:value="shop_type" @change="shop_type_change_base">
                    <Radio :value="it.dictKey" v-for="(it,ind) in dict_list" :key="ind">{{it.dictValue}}</Radio>
                  </RadioGroup>
                </FormItem>
                </Col>
              </Row>
              <!-- dev_supplier_o2o-end -->
            </div>
          </div>

          <!-- 个人入驻信息start -->
          <template v-if="cur_apply_type == 0">
            <Form ref="personalFormRef" :model="personalFormState">
              <div>
                <div class="apply_title_bg">
                  <span class="title">店铺联系人信息</span>
                </div>
                <div class="mt-5">
                  <Row type="flex">
                    <Col type="flex" :span="12">
                      <FormItem label="所在地" name="area" :label-col="{ span: 6 }"  :rules="[{required: true,message: '请选择所在地',}]">
                        <Cascader :disabled="disabledEdit" v-model:value="personalFormState['area']" :options="areaOptions"
                        placeholder="请选择所在地" style="width: 100%"
                        @change="(value, selectedOptions) => handlerCascaderChange(selectedOptions, 'area', 'person')"
                        @blur="handleCheck('area')" :fieldNames="fieldNames" />
                      </FormItem>
                    </Col>
                  </Row>
                  <Row type="flex">
                    <Col type="flex" :span="12">
                      <FormItem label="详细地址" :required="true" name="companyAddress" :label-col="{ span: 6 }">
                        <div class="companyAddress_pos">
                          <Input v-model:value="personalFormState['companyAddress']" placeholder="请输入详细地址，最多40字"
                            :maxlength="40" style="width: 100%" @change="handlerAnge"></Input>
                            
                        </div>
                      </FormItem>
                    </Col>
                  </Row>
                  
                  <Row type="flex">
                    <Col flex="2" :span="12">
                      <FormItem label="联系人" :required="true" name="contactName" :label-col="{ span: 6 }">
                        <Input v-model:value="personalFormState['contactName']" placeholder="请输入联系人姓名，最多6字" :maxlength="6"
                          style="width: 100%"></Input>
                      </FormItem>
                    </Col>
                  </Row>
                  <Row type="flex">
                    <Col flex="2" :span="12">
                    <FormItem>
                      <FormItem label="联系手机号" :required="true" name="contactPhone" :label-col="{ span: 6 }">
                        <Input v-model:value="personalFormState['contactPhone']" placeholder="请输入联系人手机号" :maxlength="11"
                          style="width: 100%"></Input>
                      </FormItem>
                    </FormItem>
                    </Col>
                  </Row>
                </div>
              </div>
              <div>
                <div class="apply_title_bg">
                  <span class="title">身份证信息</span>
                </div>
                <div class="mt-5">
                  <Row type="flex">
                    <Col type="flex" :span="12">
                    <FormItem label="身份证正面" :required="true" name="personCardUp" :label-col="{ span: 6 }"
                      extra="支持JPG/PNG,大小不超过20M">
                      <div class="upload_file">
                        <Upload :disabled="disabledEdit" :maxCount="1" accept=" .gif, .jpeg, .png, .jpg," name="file"
                          :action="`${apiUrl}/v3/oss/seller/upload?source=sellerApply`" listType="picture-card"
                          @click="beforeUploadClick"
                          :beforeUpload="(file) => beforeUpload(file, ' .gif, .jpeg, .png, .jpg,', 20)"
                          :file-list="personalFormState['personCardUp']"
                          @change="(e) => handleFilePersonChange(e, 'personCardUp')" :headers="{
                            Authorization: imgToken,
                          }">
                          <div v-if="personalFormState['personCardUp'].length < 1">
                            <PlusOutlined />
                            <div className="ant-upload-text">上传图片</div>
                          </div>
                        </Upload>
                      </div>
                    </FormItem>
                    <FormItem label="身份证反面" :required="true" name="personCardDown" :label-col="{ span: 6 }"
                      extra="支持JPG/PNG,大小不超过20M">
                      <div class="upload_file">
                        <Upload :disabled="disabledEdit" :maxCount="1" accept=" .gif, .jpeg, .png, .jpg," name="file"
                          @click="beforeUploadClick"
                          :action="`${apiUrl}/v3/oss/seller/upload?source=sellerApply`" listType="picture-card"
                          :beforeUpload="(file) => beforeUpload(file, ' .gif, .jpeg, .png, .jpg,', 20)"
                          :file-list="personalFormState['personCardDown']"
                          @change="(e) => handleFilePersonChange(e, 'personCardDown')" :headers="{
                            Authorization: imgToken,
                          }">
                          <div v-if="personalFormState['personCardDown'].length < 1">
                            <PlusOutlined />
                            <div className="ant-upload-text">上传图片</div>
                          </div>
                        </Upload>
                      </div>
                    </FormItem>
                    </Col>
                  </Row>
                </div>
              </div>
            </Form>
          </template>
          <!-- 个人入驻信息end -->

          <!-- 企业入驻信息start -->
          <template v-if="cur_apply_type == 1">
            <Form ref="companyFormRef" :model="companyFormState">
              <div>
                <div class="apply_title_bg">
                  <span class="title">店铺联系人信息</span>
                </div>
                <div class="mt-5">
                  <Row type="flex">
                    <Col type="flex" :span="12">
                    <FormItem label="公司名称" :required="true" name="companyName" :label-col="{ span: 6 }">
                      <Input v-model:value="companyFormState['companyName']" placeholder="请输入公司名称，最多50字" :maxlength="50"
                        style="width: 100%"></Input>
                    </FormItem>
                    </Col>
                  </Row>
                  <Row type="flex">
                    <Col  type="flex" :span="12">
                    <FormItem label="所在地" :required="true" name="area" :label-col="{ span: 6 }">
                      <Cascader :disabled="disabledEdit" v-model:value="companyFormState['area']" :options="areaOptions"
                        placeholder="请选择所在地" style="width: 100%"
                        @change="(value, selectedOptions) => handlerCascaderChange(selectedOptions, 'area', 'company')"
                        @blur="handleCheck('area')" :fieldNames="fieldNames" />
                    </FormItem>
                    </Col>
                  </Row>
                  <Row type="flex">
                    <Col type="flex" :span="12">
                    <FormItem label="详细地址" :required="true" name="companyAddress" :label-col="{ span: 6 }">
                      <div class="companyAddress_pos">
                        <Input :disabled="disabledEdit" v-model:value="companyFormState['companyAddress']"
                          placeholder="请输入详细地址，最多40字" :maxlength="40" style="width: 100%"  @change="handlerAnge"></Input>
                          
                      </div>
                    </FormItem>
                    </Col>
                  </Row>
                   
                  <Row type="flex">
                    <Col type="flex" :span="12">
                    <FormItem label="联系人" :required="true" name="contactName" :label-col="{ span: 6 }">
                      <Input :disabled="disabledEdit" v-model:value="companyFormState['contactName']"
                        placeholder="请输入联系人姓名，最多6字" :maxlength="6" style="width: 100%"></Input>
                    </FormItem>
                    </Col>
                  </Row>
                  <Row type="flex">
                    <Col type="flex" :span="12">
                    <FormItem label="联系手机号" :required="true" name="contactPhone" :label-col="{ span: 6 }">
                      <Input :disabled="disabledEdit" v-model:value="companyFormState['contactPhone']"
                        placeholder="请输入联系人手机号" :maxlength="11" style="width: 100%"></Input>
                    </FormItem>
                    </Col>
                    <Col flex="2">

                    </Col>
                  </Row>
                </div>
              </div>
              <div>
                <div class="apply_title_bg">
                  <span class="title">营业执照信息</span>
                </div>
                <div class="mt-5">
                  <Row type="flex">
                    <Col type="flex" :span="12">
                    <FormItem label="营业执照" :required="true" name="businessLicenseImage" :label-col="{ span: 6 }"
                      extra="支持JPG/PNG,大小不超过20M">
                      <div class="upload_file">
                        <Upload :disabled="disabledEdit" :maxCount="1" accept=" .gif, .jpeg, .png, .jpg," name="file"
                          @click="beforeUploadClick"
                          :action="`${apiUrl}/v3/oss/seller/upload?source=setting`" listType="picture-card"
                          :beforeUpload="(file) => beforeUpload(file, ' .gif, .jpeg, .png, .jpg,', 20)"
                          :file-list="companyFormState['businessLicenseImage']"
                          @change="(e) => handleFileCompanyChange(e, 'businessLicenseImage')" :headers="{
                            Authorization: imgToken,
                          }">
                          <div v-if="companyFormState['businessLicenseImage'].length < 1">
                            <PlusOutlined />
                            <div className="ant-upload-text">上传图片</div>
                          </div>
                        </Upload>
                      </div>
                    </FormItem>
                    </Col>
                  </Row>
                </div>
              </div>
              <div>
                <div class="apply_title_bg">
                  <span class="title">法人身份信息</span>
                </div>
                <div class="mt-5">
                  <Row type="flex">
                    <Col type="flex" :span="12">
                    <FormItem label="身份证正面" :required="true" name="personCardUp" :label-col="{ span: 6 }"
                      extra="支持JPG/PNG,大小不超过20M">
                      <div class="upload_file">
                        <Upload :disabled="disabledEdit" :maxCount="1" accept=" .gif, .jpeg, .png, .jpg," name="file"
                          @click="beforeUploadClick"
                          :action="`${apiUrl}/v3/oss/seller/upload?source=setting`" listType="picture-card"
                          :beforeUpload="(file) => beforeUpload(file, ' .gif, .jpeg, .png, .jpg,', 20)"
                          :file-list="companyFormState['personCardUp']"
                          @change="(e) => handleFileCompanyChange(e, 'personCardUp')" :headers="{
                            Authorization: imgToken,
                          }">
                          <div v-if="companyFormState['personCardUp'].length < 1">
                            <PlusOutlined />
                            <div className="ant-upload-text">上传图片</div>
                          </div>
                        </Upload>
                      </div>
                    </FormItem>
                    <FormItem label="身份证反面" :required="true" name="personCardDown" :label-col="{ span: 6 }"
                      extra="支持JPG/PNG,大小不超过20M">
                      <div class="upload_file">
                        <Upload :disabled="disabledEdit" :maxCount="1" accept=" .gif, .jpeg, .png, .jpg," name="file"
                          @click="beforeUploadClick"
                          :action="`${apiUrl}/v3/oss/seller/upload?source=setting`" listType="picture-card"
                          :beforeUpload="(file) => beforeUpload(file, ' .gif, .jpeg, .png, .jpg,', 20)"
                          :file-list="companyFormState['personCardDown']"
                          @change="(e) => handleFileCompanyChange(e, 'personCardDown')" :headers="{
                            Authorization: imgToken,
                          }">
                          <div v-if="companyFormState['personCardDown'].length < 1">
                            <PlusOutlined />
                            <div className="ant-upload-text">上传图片</div>
                          </div>
                        </Upload>
                      </div>
                    </FormItem>
                    </Col>
                  </Row>
                </div>
              </div>
              <div>
                <div class="apply_title_bg">
                  <span class="title">补充认证信息</span>
                </div>
                <div class="mt-5">
                  <Row type="flex">
                    <Col type="flex" :span="12">
                    <FormItem label="补充认证一" name="companyAddress" :label-col="{ span: 6 }" extra="支持JPG/PNG,大小不超过20M">
                      <div class="upload_file">
                        <Upload :disabled="disabledEdit" :maxCount="1" accept=" .gif, .jpeg, .png, .jpg," name="file"
                          @click="beforeUploadClick"
                          :action="`${apiUrl}/v3/oss/seller/upload?source=setting`" listType="picture-card"
                          :beforeUpload="(file) => beforeUpload(file, ' .gif, .jpeg, .png, .jpg,', 20)"
                          :file-list="companyFormState['moreQualification1']"
                          @change="(e) => handleFileCompanyChange(e, 'moreQualification1')" :headers="{
                            Authorization: imgToken,
                          }">
                          <div v-if="companyFormState['moreQualification1'].length < 1">
                            <PlusOutlined />
                            <div className="ant-upload-text">上传图片</div>
                          </div>
                        </Upload>
                      </div>
                    </FormItem>
                    <FormItem label="补充认证二" name="companyAddress" :label-col="{ span: 6 }" extra="支持JPG/PNG,大小不超过20M">
                      <div class="upload_file">
                        <Upload :disabled="disabledEdit" :maxCount="1" accept=" .gif, .jpeg, .png, .jpg," name="file"
                          :action="`${apiUrl}/v3/oss/seller/upload?source=sellerApply`" listType="picture-card"
                          @click="beforeUploadClick"
                          :beforeUpload="(file) => beforeUpload(file, ' .gif, .jpeg, .png, .jpg,', 20)"
                          :file-list="companyFormState['moreQualification2']"
                          @change="(e) => handleFileCompanyChange(e, 'moreQualification2')" :headers="{
                            Authorization: imgToken,
                          }">
                          <div v-if="companyFormState['moreQualification2'].length < 1">
                            <PlusOutlined />
                            <div className="ant-upload-text">上传图片</div>
                          </div>
                        </Upload>
                      </div>
                    </FormItem>
                    <FormItem label="补充认证三" name="companyAddress" :label-col="{ span: 6 }" extra="支持JPG/PNG,大小不超过20M">
                      <div class="upload_file">
                        <Upload :disabled="disabledEdit" :maxCount="1" accept=" .gif, .jpeg, .png, .jpg," name="file"
                          :action="`${apiUrl}/v3/oss/seller/upload?source=setting`" listType="picture-card"
                          :beforeUpload="(file) => beforeUpload(file, ' .gif, .jpeg, .png, .jpg,', 20)"
                          :file-list="companyFormState['moreQualification3']"
                          @click="beforeUploadClick"
                          @change="(e) => handleFileCompanyChange(e, 'moreQualification3')" :headers="{
                            Authorization: imgToken,
                          }">
                          <div v-if="companyFormState['moreQualification3'].length < 1">
                            <PlusOutlined />
                            <div className="ant-upload-text">上传图片</div>
                          </div>
                        </Upload>
                      </div>
                    </FormItem>
                    </Col>
                  </Row>
                </div>
              </div>
            </Form>
          </template>
          <!-- 企业入驻信息end -->


          <div class="settled_btn_list">
            <!-- <div class="settled_btn_pre" @click="preStep">上一步</div> -->
            <div class="settled_btn_next" @click="nextStep">下一步</div>
          </div>

          <!-- <div class="settled_info">
            <div class="settled_info_item">
              <div class="settled_info_item_title"><span>*</span>供应商名称</div>
              <div class="settled_info_item_main">
                <a-input :disabled="disabledEdit" v-model:value="detail['storeName']" placeholder="请输入供应商名称"
                  style="width: 500px" :maxlength="50" showCount @change="handleChange($event, 'storeName')"
                  @blur="handleCheck('storeName')" />
                <div v-if="errMsg['storeName']" class="error_msg">{{ errMsg['storeName'] }}</div>
              </div>
            </div>
            <div class="settled_info_item">
              <div class="settled_info_item_title"><span>*</span>联系人姓名</div>
              <div class="settled_info_item_main">
                <a-input :disabled="disabledEdit" v-model:value="detail['contactName']" placeholder="请输入联系人姓名"
                  style="width: 500px" :maxlength="10" showCount @change="handleChange($event, 'contactName')"
                  @blur="handleCheck('contactName')" />
                <div v-if="errMsg['contactName']" class="error_msg">{{ errMsg['contactName'] }}</div>
              </div>
            </div>
            <div class="settled_info_item">
              <div class="settled_info_item_title"><span>*</span>联系方式</div>
              <div class="settled_info_item_main">
                <a-input :disabled="disabledEdit" v-model:value="detail['contactPhone']" placeholder="请输入联系方式"
                  style="width: 500px" :maxlength="13" showCount @change="handleChange($event, 'contactPhone')"
                  @blur="handleCheck('contactPhone')" />
                <div v-if="errMsg['contactPhone']" class="error_msg">{{
                  errMsg['contactPhone']
                }}</div>
              </div>
            </div>
            <div class="settled_info_item">
              <div class="settled_info_item_title"><span>*</span>所在地</div>
              <div class="settled_info_item_main">
                <a-cascader :disabled="disabledEdit" v-model:value="detail['area']" :options="areaOptions"
                  placeholder="请选择所在地" style="width: 500px"
                  @change="(value, selectedOptions) => handleChange(selectedOptions, 'area')" @blur="handleCheck('area')"
                  :fieldNames="fieldNames" />
                <div v-if="errMsg['area']" class="error_msg">{{ errMsg['area'] }}</div>
              </div>
            </div>
            <div class="settled_info_item" style="align-items: flex-start; margin-bottom: -15px">
              <div class="settled_info_item_title"><span>*</span>详细地址</div>
              <div class="settled_info_item_main">
                <a-textarea :disabled="disabledEdit" v-model:value="detail['companyAddress']" placeholder="请输入详细地址"
                  :rows="3" :maxlength="50" showCount style="width: 500px; max-width: 500px; resize: none"
                  @change="handleChange($event, 'companyAddress')" @blur="handleCheck('companyAddress')" />
                <div v-if="errMsg['companyAddress']" class="error_msg">{{
                  errMsg['companyAddress']
                }}</div>
              </div>
            </div>
            <div class="settled_info_item" style="align-items: flex-start">
              <div class="settled_info_item_title" style="margin-top: 2px"><span>*</span>营业执照</div>
              <div class="settled_info_item_main settled_info_item_upload">
                <a-upload :disabled="disabledEdit" :action="apiUrl" list-type="picture-card" accept=".jpg, .jpeg, .png"
                  :file-list="detail['businessLicenseImage']"
                  :beforeUpload="(file) => beforeUpload(file, '.jpg, .jpeg, .png', 10)" :data="fileData"
                  @change="(file) => handleChange(file, 'businessLicenseImage')" @preview="(file) => handlePreview(file)">
                  <div v-if="!detail['businessLicenseImage'] || detail['businessLicenseImage'].length == 0
                    " class="settled_info_item_upload_btn">+上传图片</div>
                </a-upload>
                <div class="settled_info_item_upload_desc">图片最大不超过10M，支持jpg、jpeg、png</div>
                <div v-if="errMsg['businessLicenseImage']" class="error_msg">{{
                  errMsg['businessLicenseImage']
                }}</div>
              </div>
            </div>
          </div>
          <div class="settled_btn_list">
            <div class="settled_btn_pre" @click="preStep">上一步</div>
            <div class="settled_btn_next" @click="nextStep">下一步</div>
          </div> -->



        </div>
      </div>
    </div>
  </Spin>
</template>
<script>
import { failTip } from '@/utils/utils';
import { PlusOutlined } from '@ant-design/icons-vue';
import { Cascader, Col, Form, FormItem, Input, Radio, RadioGroup, Row, Spin, Upload } from 'ant-design-vue';
import { defineComponent, onMounted, onUnmounted, reactive, ref } from 'vue';
import AreaJson from '/@/assets/json/area.json';
import { useGlobSetting } from '/@/hooks/setting';
import { useGo } from '/@/hooks/web/usePage';
import { useUserStore } from '/@/store/modules/user';
// dev_supplier_o2o-start
import { dictDataListApi } from '/@/api/common/common';
// dev_supplier_o2o-end
import { beforeUpload, replaceEmoji } from '/@/utils/utils';

const { apiUrl, mapKey, mapSecurity } = useGlobSetting();

export default defineComponent({
  name: 'base_info',
  components: {
    Spin,
    Cascader,
    Upload,
    Row, Col,
    Form, FormItem,
    Input,
    RadioGroup,
    Radio,
    PlusOutlined
  },
  beforeRouteEnter(to, from, next) {
    if (localStorage.getItem('apply_state') != undefined) {
      next((vm) => {
        vm.disabledEdit = true;
      });
    } else {
      next((vm) => {
        vm.disabledEdit = false;
      });
    }
  },
  setup() {

    const payFlag = ref(false)
    const go = useGo();
    // 页面最小高
    const minHight = ref(document.body.clientHeight - 50 - 20 - 64);
    const spinning = ref(false);
    const disabledEdit = ref(false);
    const detail = ref({
      storeName: undefined, //供应商名称
      contactName: undefined, //联系人姓名
      contactPhone: undefined, //联系方式
      area: undefined, //所在地
      companyAddress: undefined, //详细地址
      businessLicenseImage: [], //营业执照
    });
    const userStore = useUserStore()
    const errMsg = ref({}); //报错信息
    const areaOptions = ref(AreaJson);
    const fieldNames = ref({ label: 'regionName', value: 'regionCode', children: 'children' });
    const uploadLimit = ref(10); //上传文件大小限制
    const fileData = ref({}); //接口额外传参
    const imgToken = ref('Bearer ' + userStore.getToken || ''); //上传文件token参数
    const upload_tip = ref(true); //上传文件是否显示数据变化后的提示信息
    let check_flag = true; //校验结果
    const getToken = 'Bearer ' + userStore.getAccessToken
    const bgFileList = ref([])
    const cur_apply_type = ref(1)
    const dict_disabled = ref(false)

    // dev_supplier_o2o-start
    // 店铺类型
    const dict_list = ref([])
    const shop_type = ref('1')
    // dev_supplier_o2o-end

    

    //个人入驻信息
    const personalFormRef = ref()
    const personalFormState = reactive({
      areaInfo: "",
      area: [],
      companyAddress: '',
      contactName: '',
      contactPhone: "",
      personCardUp: [],
      personCardDown: []
    })


    //企业入驻信息
    const companyFormRef = ref()
    const companyFormState = reactive({
      areaInfo: "",
      area: [],
      companyAddress: '',
      contactName: '',
      contactPhone: "",
      personCardUp: [],
      personCardDown: [],
      businessLicenseImage: [],
      companyName: '',
      moreQualification1: [],
      moreQualification2: [],
      moreQualification3: []
    })

    const handlerAnge = (e)=> {
      if(shop_type.value==2){
        tudeInfo.longitude = '';
        tudeInfo.latitude = '';
        if(document.getElementById('panel')){
          document.getElementById('panel').innerHTML = '';
        }
        searchMap('del')
        map_marker.value.remove(); //移除自选标记点
      }
    }

    const handlerCascaderChange = (e, key, type) => {
      if(key=='area'&&shop_type.value==2){
        if(shop_type.value==2){
        tudeInfo.longitude = '';
        tudeInfo.latitude = '';
        if(document.getElementById('panel')){
          document.getElementById('panel').innerHTML = '';
        }
        searchMap('del')
        map_marker.value.remove(); //移除自选标记点
      }
      }
      if(e){
        let [provice, city, area] = e
        let areaInfo = `${provice?.regionName}${city?.regionName}${area?.regionName}`
        type == 'person' ? (personalFormState["areaInfo"] = areaInfo) : (companyFormState["areaInfo"] = areaInfo)
      }else{
        type == 'person' ? (personalFormState["areaInfo"] = '') : (companyFormState["areaInfo"] = '')
      }
    }

    //编辑数据
    function handleChange(e, key) {
      if (
        key == 'storeName' ||
        key == 'contactName' ||
        key == 'contactPhone' ||
        key == 'companyAddress'
      ) {
        detail.value[key] = e.target.value ? replaceEmoji(e.target.value).trim() : '';
      } else if (key == 'area') {
        detail.value[key] =
          e && e.length > 0 ? [e[0].regionCode, e[1].regionCode, e[2].regionCode] : undefined;
        detail.value['provinceCode'] = e && e.length > 0 ? e[0].regionCode : undefined;
        detail.value['cityCode'] = e && e.length > 0 ? e[1].regionCode : undefined;
        detail.value['areaCode'] = e && e.length > 0 ? e[2].regionCode : undefined;
        detail.value['areaInfo'] =
          e && e.length > 0 ? e[0].regionName + e[1].regionName + e[2].regionName : undefined;
      } else if (key == 'businessLicenseImage') {
        if (e.file.status != undefined && e.file.status != 'error') {
          detail.value[key] = e.fileList;
          if (detail.value[key].length > 0) {
            delete errMsg.value[key];
          } else {
            errMsg.value[key] = '请上传营业执照';
          }
        } else if (upload_tip.value) {
          failTip('上传失败');
        } else {
          upload_tip.value = true;
        }
      }
      if (errMsg.value[key] && key != 'businessLicenseImage') {
        delete errMsg.value[key];
      }
    }

    function handleCheck(flag_key) {
      check_flag = true;
      [
        'storeName',
        'contactName',
        'contactPhone',
        'area',
        'companyAddress',
        'businessLicenseImage',
      ].map((key) => {
        if (
          (flag_key && flag_key == 'storeName' && key == 'storeName') ||
          (!flag_key && key == 'storeName')
        ) {
          if (!detail.value[key]) {
            check_flag = false;
            errMsg.value[key] = '请输入供应商名称';
          } else {
            delete errMsg.value[key];
          }
        } else if (
          (flag_key && flag_key == 'contactName' && key == 'contactName') ||
          (!flag_key && key == 'contactName')
        ) {
          if (!detail.value[key]) {
            check_flag = false;
            errMsg.value[key] = '请输入联系人姓名';
          } else {
            delete errMsg.value[key];
          }
        } else if (
          (flag_key && flag_key == 'contactPhone' && key == 'contactPhone') ||
          (!flag_key && key == 'contactPhone')
        ) {
          const mobile_reg =
            /^((\+?86)|(\(\+86\)))?(13[*********][0-9]{8}|15[*********][0-9]{8}|18[02356789][0-9]{8}|147[0-9]{8}|1349[0-9]{7})$/;
          const tell_reg = /^([0-9]{3,4}-)?[0-9]{7,8}$/;
          if (!detail.value[key]) {
            check_flag = false;
            errMsg.value[key] = '请输入联系方式';
          } else if (!mobile_reg.test(detail.value[key]) && !tell_reg.test(detail.value[key])) {
            check_flag = false;
            errMsg.value[key] = '请输入正确的联系方式';
          } else {
            delete errMsg.value[key];
          }
        } else if (
          (flag_key && flag_key == 'area' && key == 'area') ||
          (!flag_key && key == 'area')
        ) {
          if (!detail.value[key]) {
            check_flag = false;
            errMsg.value[key] = '请选择所在地';
          } else {
            delete errMsg.value[key];
          }
        } else if (
          (flag_key && flag_key == 'companyAddress' && key == 'companyAddress') ||
          (!flag_key && key == 'companyAddress')
        ) {
          if (!detail.value[key]) {
            check_flag = false;
            errMsg.value[key] = '请选中详细地址';
          } else {
            delete errMsg.value[key];
          }
        } else if (
          (flag_key && flag_key == 'businessLicenseImage' && key == 'businessLicenseImage') ||
          (!flag_key && key == 'businessLicenseImage')
        ) {
          if (!detail.value[key] || detail.value[key].length == 0) {
            check_flag = false;
            errMsg.value[key] = '请上传营业执照';
          } else {
            delete errMsg.value[key];
          }
        }
      });
    }

    const showBack = () => {
      const data = JSON.parse(localStorage.getItem('base_info') || '{}');
      dict_disabled.value = true
      cur_apply_type.value = data.enterType
      let targetVariant = cur_apply_type.value == 0 ? personalFormState : companyFormState
      let imageType = ['personCardUp', 'personCardDown', 'businessLicenseImage', 'moreQualification1', 'moreQualification2', 'moreQualification3']
      for (let key in targetVariant) {
        if (imageType.includes(key)) {
          if (data[key]&&data[key].length) {
            targetVariant[key] = [
              {
                response: {
                  data: {
                    path: data[key],
                    url: data[`${key}Url`],
                  },
                },
                name: data[key],
                uid: data[key],
                status: 'done',
                thumbUrl: data[`${key}Url`],
              },
            ];
          } else {
            targetVariant[key] = []
          }

        } else if (key == 'area') {
          targetVariant[key] = [data.companyProvinceCode, data.companyCityCode, data.companyAreaCode]
        } else {
          targetVariant[key] = data[key]
        }
      }
      // dev_supplier_o2o-start
      shop_type.value = String(data.shopType)
      // dev_supplier_o2o-end
       
    }

    // dev_supplier_o2o-start
    // 获取店铺类型
    const getDictDataList = async()=>{
      let res = await dictDataListApi({dictCode:'shop_type'})
      if(res.state == 200){
        dict_list.value = res.data
        if(res.data.length>0){
          if (localStorage.getItem('base_info') != undefined && localStorage.getItem('base_info')){
          }else{
            shop_type.value = res.data[0].dictKey
          }
        }
      }
    }

    // dev_supplier_o2o-end
    onMounted(() => {
      // dev_supplier_o2o-start
      getDictDataList()
      // dev_supplier_o2o-end
      dict_disabled.value = false
      if (localStorage.getItem('apply_state') != undefined) {
        disabledEdit.value = true;
      } else if (
        localStorage.getItem('cur_step') != undefined &&
        localStorage.getItem('cur_step') != '1'
      ) {
        history.back();
        return;
      }
      window.addEventListener('resize', onResize, true);
      if (localStorage.getItem('base_info') != undefined && localStorage.getItem('base_info')) {
        showBack()
        // let data = JSON.parse(localStorage.getItem('base_info') || '{}');
        // for (let key in detail.value) {
        //   if (
        //     key == 'storeName' ||
        //     key == 'contactName' ||
        //     key == 'contactPhone' ||
        //     key == 'companyAddress'
        //   ) {
        //     detail.value[key] = data[key] != undefined ? data[key] : undefined;
        //   } else if (key == 'area') {
        //     detail.value[key] = [
        //       data['companyProvinceCode'],
        //       data['companyCityCode'],
        //       data['companyAreaCode'],
        //     ];
        //     detail.value['provinceCode'] = data['companyProvinceCode'];
        //     detail.value['cityCode'] = data['companyCityCode'];
        //     detail.value['areaCode'] = data['companyAreaCode'];
        //     detail.value['areaInfo'] = data['areaInfo'];
        //   } else if (key == 'businessLicenseImage') {
        //     if (data[key]) {
        //       detail.value[key] = [
        //         {
        //           response: {
        //             data: {
        //               path: data[key],
        //               url: data[key],
        //             },
        //           },
        //           name: data[key],
        //           uid: data[key],
        //           status: 'done',
        //           thumbUrl: data[key],
        //         },
        //       ];
        //     }
        //   }
        // }
      }
    });

    // 卸载onResize事件
    onUnmounted(() => {
      window.removeEventListener('resize', onResize, true);
    });

    // 更新页面高度
    const onResize = () => {
      minHight.value = document.body.clientHeight - 50 - 20 - 44;
    };

    //上一步
    function preStep() {
      localStorage.setItem('cur_step', '0');
      go('/apply/settled_protocol');
    }

    //下一步
    const nextStep = async () => {
      if (disabledEdit.value) {
        localStorage.setItem('cur_step', '2');
        go('/apply/business_info');
        return;
      }

      try {
        let params = {
          enterType: cur_apply_type.value,
          // dev_supplier_o2o-start
          shopType:shop_type.value,
           // dev_supplier_o2o-end
        };
        let imageType = ['personCardUp', 'personCardDown', 'businessLicenseImage', 'moreQualification1', 'moreQualification2', 'moreQualification3']
        if (cur_apply_type.value == 0) {
          await personalFormRef.value.validate()
          
          Object.keys(personalFormState).forEach(key => {
            if (key == 'area') {
              let [provice, city, area] = personalFormState.area
              params.companyProvinceCode = provice; //企业注册省编码
              params.companyCityCode = city; //	企业注册市编码
              params.companyAreaCode = area; //企业注册区编码
            } else if (imageType.includes(key) && personalFormState[key].length) {
              let [targetFile] = personalFormState[key]
              params[`${key}Url`] = targetFile.response.data?.url
              params[key] = targetFile.response.data?.path
            } else {
              params[key] = personalFormState[key]
            }
          })
        } else {
          await companyFormRef.value.validate()
          
          Object.keys(companyFormState).forEach(key => {
            if (key == 'area') {
              let [provice, city, area] = companyFormState.area
              params.companyProvinceCode = provice; //企业注册省编码
              params.companyCityCode = city; //	企业注册市编码
              params.companyAreaCode = area; //企业注册区编码
            } else if (imageType.includes(key)) {
              if (companyFormState[key].length) {
                let [targetFile] = companyFormState[key]
                if (targetFile) {
                  params[`${key}Url`] = targetFile.response?.data?.url
                  params[key] = targetFile.response?.data?.path
                }
              }
            } else {
              params[key] = companyFormState[key]
            }
          })
        }
        localStorage.setItem('cur_step', '2');
        localStorage.setItem('base_info', JSON.stringify(params));
        go('/apply/business_info');
      } catch (err) {}
    }

    //预览时间
    function handlePreview(file) {
      if (file.response.key) {
        window.open(uploadViewUrl + file.response.key);
      } else if (file.response.data.url) {
        window.open(file.response.data.url);
      }
    }

    const handleFilePersonChange = (e, type) => {
      if (e.file.status != undefined && e.file.status != 'error') {
        e.fileList.forEach((item) => {
          if (item.response && item.response.data) {
            item.url = item.response.data.url;
          }
        });
        personalFormState[type] = e.fileList;
      }
    }

    const handleFileCompanyChange = (e, type) => {
      if (e.file.status != undefined && e.file.status != 'error') {
        e.fileList.forEach((item) => {
          if (item.response && item.response.data) {
            item.url = item.response.data.url;
          }
        });
        companyFormState[type] = e.fileList;
      }
    }
    
    
    // 入驻类型切换事件
    const cur_apply_type_change_base = (e)=> {
      cur_apply_type.value = e.target.value
      
    }
    
    const shop_type_change_base = (e)=> {
      shop_type.value = e.target.value
      
    }
    
    


    const beforeUploadClick = async()=> {
      imgToken.value = 'Bearer ' + userStore.getToken
      if(userStore.getToken){
        let res = await userStore.getSldCommonService()
        if(res.state == 200){
          imgToken.value = 'Bearer ' + res.token
        }
      }
    }

    return {
      handleFileCompanyChange,
      handleFilePersonChange,
      spinning,
      disabledEdit,
      detail,
      errMsg,
      areaOptions,
      fieldNames,
      uploadLimit,
      fileData,
      imgToken,
      apiUrl,
      upload_tip,
      getToken,
      beforeUpload,
      handleChange,
      preStep,
      nextStep,
      minHight,
      handlePreview,
      handleCheck,
      bgFileList,
      cur_apply_type,
      personalFormRef,
      personalFormState,
      companyFormRef,
      companyFormState,
      handlerCascaderChange,
      dict_disabled,
      handlerAnge,
      // dev_supplier_o2o-start
      getDictDataList,
      dict_list,
      shop_type,
      shop_type_change_base,
      // dev_supplier_o2o-end
      cur_apply_type_change_base,
      
       beforeUploadClick,
      payFlag,
    };
  },
});
</script>
<style lang="less" scoped>
.companyAddress_pos{
  position: relative;
  .companyAddress_btn{
    position: absolute; 
    right: 1px; 
    top: 1px;
    height: 30px;
    font-size: 13px;
    color: @primary-color;
    border-radius: 0 3px 3px 0;
    background-color: rgb(255 126 40 / 15%);
    padding: 4px 10px;
    cursor: pointer;
  }
}
.settled_info {
  padding: 0 20px 40px;

  .settled_info_title {
    margin-top: 15px;
    margin-bottom: 15px;
    margin-left: 5px;
    color: #333;
    font-size: 14px;
    font-weight: bold;
  }

  .settled_info_item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin: 20px 0 25px;

    .settled_info_item_title {
      width: 80px;
      margin-right: 20px;
      color: #333;
      font-size: 13px;
      font-weight: 500;
      text-align: right;

      span {
        color: #f00;
      }
    }

    .settled_info_item_main {
      width: 400px;

      .error_msg {
        height: 0;
        color: #ed6f6f;
      }

      &.settled_info_item_main_width {
        width: 660px;
      }

      .settled_info_item_main_btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100px;
        height: 30px;
        padding: 0;
        border: 1px solid @primary-color;
        border-radius: 3px;
        color: @primary-color;
        font-size: 13px;
        cursor: pointer;
      }

      .settled_info_item_main_spec {
        .settled_info_item_main_spec_item {
          position: relative;
          margin-top: 15px;
          padding: 7px 15px 20px;
          background: rgb(249 249 249);

          .settled_info_item_main_spec_item_util {
            position: absolute;
            top: 5px;
            right: 15px;

            .settled_info_item_main_spec_item_utils {
              width: 16px;
              height: 16px;
              cursor: pointer;
            }
          }

          .settled_info_item_main_spec_item_title {
            margin-top: 5px;
            margin-bottom: 4px;
            color: #333;
            font-size: 14px;
            font-weight: 500;
          }

          .settled_info_item_main_spec_item_info {
            display: flex;
            align-items: center;
            justify-content: flex-start;

            .settled_info_item_main_spec_item_info_item {
              display: flex;
              align-items: center;
              justify-content: flex-start;
              width: 300px;
              margin-right: 30px;

              .settled_info_item_main_spec_item_info_item_span {
                margin-right: 8px;
                color: #999;
                font-size: 12px;
                white-space: nowrap;
              }
            }
          }

          .settled_info_item_main_spec_item_list {
            .settled_info_item_main_spec_item_del {
              padding: 3px 7px 5px;
              transition: all 0.2s ease;
              border-radius: 3px;
              color: #333;
              font-size: 13px;
              white-space: nowrap;
              cursor: pointer;

              &:hover {
                background-color: @primary-color;
                color: #fff;
              }
            }
          }

          .settled_info_item_main_spec_item_btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 105px;
            height: 32px;
            margin-top: 15px;
            border: 1px dashed #d2d2d2;
            border-radius: 3px;
            color: @primary-color;
            font-family: 'Microsoft YaHei';
            font-size: 13px;
            font-weight: 400;
            cursor: pointer;
          }
        }
      }

      &.settled_info_item_upload {
        .settled_info_item_upload_btn {
          color: rgb(61 133 255);
          font-size: 13px;
          cursor: pointer;
        }

        .settled_info_item_upload_desc {
          margin-top: 12px;
          color: #999;
          font-size: 12px;
        }
      }
    }
  }
}

.settled_btn_list {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px auto;

  .settled_btn_pre,
  .settled_btn_next {
    width: 100px;
    height: 37px;
    border-radius: 4px;
    font-size: 14px;
    line-height: 35px;
    text-align: center;
    cursor: pointer;
  }

  .settled_btn_pre {
    transition: all 0.5s ease;
    border: 1px solid #aaa;
    color: #666;

    &:hover {
      border-color: @primary-color;
      color: @primary-color;
    }
  }

  .settled_btn_next {
    margin-left: 30px;
    border: 1px solid @primary-color;
    background: @primary-color;
    color: #fff;
  }
}
</style>

<style lang="less"> 
.base_info_apply{
  .ant-form-item-with-help{
    margin-bottom: 0px;
  }
  .item{
    margin-bottom: 24px;
    flex: 0 0 50%;
    max-width: 50%;
    .left{
      display: block;
    flex: 0 0 25%;
    max-width: 25%;
      height: 32px;
      font-size: 13px;
      line-height: 32px;
      text-align: right;
      &::before{
        display: inline-block;
        margin-right: 4px;
        color: #ff4d4f;
        font-size: 14px;
        font-family: SimSun, sans-serif;
        line-height: 1;
        content: '*';
      }
    }
  }
  #panel {
  position: absolute;
  background-color: white;
  max-height: 500px;
  overflow-y: auto;
  top: 0;
  right: -290px;
  width: 280px;
}
}
.apply_title_bg {
   height: 36px;
   line-height: 36px;
   background: rgba(255, 120, 45, .05);
   border-radius: 3px;
   margin-left: 20px;
   margin-right: 20px;

   .title {
     color: #333;
     font-size: 13px;
     padding-left: 10px;
   }
 }
</style>
