<template>
  <div class="section_padding point_order_lists">
    <div class="section_padding_back section_padding_back_flex">
      <SldComHeader
        :title="L('订单管理')"
      />
      <Spin :spinning="loading">
        <div class="spin_height">
          <BasicForm ref="formRef" :tableFlag="true" submitOnReset @register="registerForm" class="basic-form">
          </BasicForm>
          <div style="margin-bottom: 10px">
            <RadioGroup size="small" v-model:value="filter_code" @change="clickFilter">
              <RadioButton
                :value="item.filter_code"
                v-for="(item, index) in filter_data"
                :key="index"
                >{{ item.filter_name }}</RadioButton
              >
            </RadioGroup>
          </div>
          <div class="order_list">
            <ul class="header">
              <li class="width_40 pl_100">{{ L('商品信息') }}</li>
              <li class="width_10 center">{{ L('单价') }}</li>
              <li class="width_10 center">{{ L('数量') }}</li>
              <li class="width_10 center">{{ L('会员') }}</li>
              <li class="width_10 center">{{ L('实付金额') }}</li>
              <li class="width_10 center">{{ L('订单状态') }}</li>
              <li class="width_10 center">{{ L('操作') }}</li>
            </ul>
            <div class="order_content">
              <Empty
                :image="Empty.PRESENTED_IMAGE_SIMPLE"
                v-if="data.list != undefined && data.list.length == 0"
              />
              <div
                class="order_content_height"
                v-if="data.list != undefined && data.list.length > 0"
              >
                <div class="item" v-for="(item, index) in data.list" :key="index">
                  <div class="order_info flex_row_between_center">
                    <div class="left flex_row_start_start">
                      <span class="order_sn">{{ L('订单号：') }}{{ item.orderSn }}</span>
                      <div class="order_type" v-if="item.isVirtualGoods == 2">
                        <img
                          src="@/assets/images/order/virtural_goods_order_icon.png"
                          alt=""
                          class="order_type_icon"
                        />
                        <span class="order_type_text">{{ L('虚拟商品订单') }}</span>
                      </div>
                    </div>
                    <span class="create_time">{{ L('兑换时间：') }}{{ item.createTime }}</span>
                  </div>
                  <div class="order_goods_part flex_row_start_center">
                    <div
                      class="goods flex_column_start_start width_60"
                      :class="{
                        goods_split:
                          item.orderProductList != undefined && item.orderProductList.length > 1,
                      }"
                    >
                      <template
                        v-if="
                          item.orderProductList != undefined && item.orderProductList.length > 0
                        "
                      >
                        <div
                          class="goods_item flex_row_start_center"
                          style="width: 100%"
                          v-for="(item_goods, index_goods) in item.orderProductList"
                          :key="index_goods"
                        >
                          <div class="flex_row_start_center" style="width: 66.66%">
                            <div class="goods_img_wrap flex_row_center_center">
                              <img :src="item_goods.productImage" alt="" />
                            </div>
                            <div class="goods_info flex_column_start_start">
                              <span class="goods_name">{{ item_goods.goodsName }}</span>
                              <span class="goods_spec">{{ item_goods.specValues }}</span>
                            </div>
                          </div>
                          <span class="goods_price width_10 center" style="width: 16.68%">
                            {{ L('积分') }}{{ item_goods.integralPrice }}+￥{{ item_goods.cashPrice }}
                          </span>
                          <span class="buy_num width_10 center" style="width: 16.66%">
                            {{ item_goods.productNum }}
                          </span>
                        </div>
                      </template>
                    </div>
                    <div class="member_info flex_column_center_center width_10">
                      <span class="mem_name">{{ item.memberName }}</span>
                    </div>
                    <div class="pay_amount order_state width_10 center">
                      <div>{{ L('积分') }}{{ item.integral }}+￥{{ item.cashAmount }}</div>
                      {{ item.paymentName }}
                    </div>
                    <div class="order_state width_10 center">{{ item.orderStateValue }}</div>
                    <div class="operate width_10 center flex_column_center_center">
                      <div class="operate_btn" @click="goDetail(item)">{{ L('查看详情') }}</div>
                      <template v-if="item.orderState == 20">
                        <Popconfirm :title="L('该订单为虚拟商品订单，确认执行该操作吗?')"
                          @confirm="virturalGoodsOrderDeliverConfirm(item)" v-if="item.isVirtualGoods == 2">
                          <div class="operate_btn">{{ L('发货') }}</div>
                        </Popconfirm>
                        <div class="operate_btn" v-else @click="agreeDeliver(item)">{{ L('发货') }}</div>
                      </template>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="pagination">
              <Pagination
                v-if="
                  data.list != undefined && data.list.length > 0 && data.pagination != undefined
                "
                v-model:current="data.pagination.current"
                size="small"
                show-quick-jumper
                :show-total="(total) => `${L('共')} ${total} ${L('条数据')}`"
                :total="data.pagination.total"
                :defaultPageSize="PAGE_SIZE"
                :pageSizeOptions="PAGE_SIZE_OPTIONS"
                @change="onPageChange"
              />
            </div>
          </div>
        </div>
      </Spin>
    </div>
    <Modal :title="L('商品发货')" :width="modal_width" :visible="demodalVisible" @ok="deliverConfirm" @cancel="sldDeliverHandleCancle" :okText="modalItem.deliverType == 3 ? L('打印并发货') : L('确定')" :confirmLoading="confirmLoading">
      <div v-if="modalItem.deliverType == '1'">
        <div :style="{
          width: modal_width+'px',
          paddingLeft: (modal_width * 0.11)+'px',
          marginTop: '5px',
          marginBottom:'8px',
        }">
          <div :style="{
            color: '#FF711E',
            width: (18 / 24 * (modal_width ? modal_width : 416))+'px',
            fontSize: '12px',
            borderBottom:'1px solid #f2f2f2',
            lineHeight: '28px'
          }">
            * {{ L('请认真填写物流公司及快递单号') }}
          </div>
        </div>
      </div>
      <Form layout="horizontal" ref="formRef" class="form_detail" :model="modalItem" >
        <div v-if="modalItem.deliverType != '1'" style="width: 100%;height:10px;"></div>
        <Form.Item :label="L('收货人姓名')" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}">
          <span>{{modalItem.receiverName}}</span>
        </Form.Item>
        <Form.Item :label="L('收货人电话')" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}">
          <span>{{modalItem.receiverMobile}}</span>
        </Form.Item>
        <Form.Item :label="L('收货人地址')" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}">
          <span style="word-break: break-all;">{{modalItem.receiverAreaInfo}}{{modalItem.receiverAddress}}</span>
        </Form.Item>
        <Form.Item :label="L('发货方式')" name="deliverType" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}" :rules="[{required: true,message:L('请选择发货方式')}]">
          <RadioGroup
            size="small"
            v-model:value="modalItem.deliverType"
            @change="redioOnChange($event)"
          >
            <Radio :value="1" > {{ L('物流发货') }} </Radio>
            <Radio :value="3"> {{ L('电子面单') }} </Radio>
            <Radio :value="2"> {{ L('无需物流') }} </Radio>
          </RadioGroup>
        </Form.Item>
        <Form.Item :label="L('物流公司')" name="expressId" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}" :rules="[{required: true,message:L('请选择物流公司')}]" v-if="modalItem.deliverType == 3">
          <Select
            :placeholder="L('请选择物流公司')"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            v-model:value="modalItem.expressId"
            @change="(e)=>handleSelExpress(e)"
          >
            <Select.Option
              v-for="(item, index) in email_expressList"
              :key="index"
              :value="item.expressId"
              >{{ item.expressName }}</Select.Option
            >
          </Select> 
        </Form.Item>
        <Form.Item :label="L('物流公司')" name="expressId" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}" :rules="[{required: true,message:L('请选择物流公司')}]" v-if="modalItem.deliverType == 1">
          <Select
            :placeholder="L('请选择物流公司')"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            v-model:value="modalItem.expressId"
            @change="(e)=>handleSelExpress(e)"
          >
            <Select.Option
              v-for="(item, index) in expressList"
              :key="index"
              :value="item.expressId"
              >{{ item.expressName }}</Select.Option
            >
          </Select> 
        </Form.Item>
        <Form.Item :label="L('快递单号')" name="expressNumber" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}" :rules="[{required: true,message:L('请输入物流单号')},rule]" v-if="modalItem.deliverType == 1" :extra="L('可以直接输入物流单号，也可以点击获取物流单号通过电子面单自动获取物流单号')">
          <div class="flex_row_start_center" style="position: relative;">
            <Input :maxLength="20" :placeholder="L('请输入物流单号')" v-model:value="modalItem.expressNumber"/>
            <a v-if="showPrintExterFaceSheetBtn" class="free_freight get_exter_face_sheet_btn" @click="printExterFaceSheetBtn">{{ L('打印电子面单') }}</a>
            <a v-else-if="isAllowApplyExpressNum" class="free_freight get_exter_face_sheet_btn" @click="getExterFaceSheetBtn">{{ L('获取物流单号') }}</a>
          </div>
        </Form.Item>
        <Form.Item :label="L('联系人')" name="deliverName" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}" :rules="[{required: true,message:L('请输入联系人')}]" v-if="modalItem.deliverType == 2">
          <Input :maxLength="10" placeholder="请输入联系人" v-model:value="modalItem.deliverName"/>
        </Form.Item>
        <Form.Item :label="L('联系方式')" name="deliverMobile" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}" :rules="[{required: true,message:L('请输入联系方式')},{pattern: mobile_reg,message:L('请输入正确的手机号')}]" v-if="modalItem.deliverType == 2">
          <Input :maxLength="11" :placeholder="L('请输入联系方式')" v-model:value="modalItem.deliverMobile"/>
        </Form.Item>
      </Form>
    </Modal>
  </div>
</template>
<script>
  export default {
    name: 'PointOrderList',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, onMounted } from 'vue';
  import { RadioGroup, RadioButton, Spin, Empty, Pagination ,Popconfirm,Form,Modal,Radio,Select,Input} from 'ant-design-vue';
  import { getOrderPointListApi,getExpressListApi,getIntegralPrintApi,getIntegralLogisticCodeApi,getIntegralOrderDeliverApi } from '/@/api/point/order';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { list_com_page_size_10 } from '/@/utils/utils';
  import { useRouter } from 'vue-router';
  import { PAGE_SIZE, PAGE_SIZE_OPTIONS } from '/@/components/Table/src/const.ts';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { list_com_page_more,sucTip, failTip  } from '/@/utils/utils';
  import { useUserStore } from '/@/store/modules/user';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const filter_code = ref('');

  const userStore = useUserStore();

  const modal_width = ref(550)
  const rule = ref({pattern: /^[0-9a-zA-Z]*$/g,message:L('请输入正确的单号')})
  const mobile_reg = ref(/(1[3-9]\d{9}$)/)
  const expressList = ref([])
  const exterFaceSheet = ref('');//通过电子面单获取的物流单号
  const isAllowApplyExpressNum = ref(false);//是否允许通过电子面单申请物流单号
  const showPrintExterFaceSheetBtn = ref(false);//已经有物流单号的话直接显示
  const email_expressList = ref([])
  const formRef = ref();
  const confirmLoading = ref(false);
  const modalItem = ref({
    deliverType:1
  });
  const modalItemInfo = ref({});
  const demodalVisible = ref(false)

  const pageSize = ref(list_com_page_size_10);

  const router = useRouter();

  const filter_data = ref([
    { filter_code: '', filter_name: L('全部订单') },
    { filter_code: '10', filter_name: L('待付款订单') },
    { filter_code: '20', filter_name: L('待发货订单') },
    { filter_code: '30', filter_name: L('待收货订单') },
    { filter_code: '40', filter_name: L('已完成订单') },
    { filter_code: '0', filter_name: L('已取消订单') },
  ]);
  const loading = ref(false);
  const data = ref({
    list: [],
  });
  const params = ref({ pageSize: pageSize.value });
  const formValues = ref({}); //搜索条件

  //表单
  const [registerForm, { validate, getFieldsValue }] = useForm({
    schemas: [
      {
        field: 'orderSn',
        component: 'Input',
        componentProps: {
          minWidth: 300,
          placeholder: L('请输入订单号'),
          size: 'default',
        },
        label: L('订单号'),
      },
      {
        field: 'memberName',
        component: 'Input',
        componentProps: {
          placeholder: L('请输入会员名称'),
          size: 'default',
        },
        label: L('会员名称'),
      },
      {
        field: 'tradeSn',
        component: 'Input',
        colProps: { span: 6, style: 'width:250px;max-width:100%;flex:none' },
        componentProps: {
          minWidth: 300,
          placeholder: '请输入支付流水号',
          size: 'default',
        },
        label: '支付流水号',
        labelWidth: 75,
      },
      {
        labelWidth: 70,
        component: 'RangePicker',
        label: L('兑换时间'),
        field: 'fieldTime',
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: [L('开始时间'), L('结束时间')],
        },
      },
    ],
    fieldMapToTime: [['fieldTime', ['startTime', 'endTime'], 'YYYY-MM-DD']],
    labelWidth: 80,
    baseColProps: { span: 6 },
    actionColOptions: { span: 24 },
    autoSubmitOnEnter: true,
    showAdvancedButton: true,
    submitFunc: search,
    resetFunc: reset,
  });

  // 搜索
  async function search() {
    await validate();
    let values = getFieldsValue();
    values.startTime = values.startTime ? values.startTime.split(' ')[0] + ' 00:00:00' : undefined;
    values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
    for (let i in values) {
      if (values[i] == undefined) {
        delete values[i];
      }
    }
    formValues.value = values;
    let curParams = { ...params.value, ...formValues.value };
    if (filter_code.value) {
      curParams.orderState = filter_code.value;
    }
    get_list(curParams);;
  }

  async function reset() {
    filter_code.value = ''
    params.value.current = 1;
  }

  // 请选择物流公司
  const handleSelExpress = (val)=> {
    let obj = JSON.parse(JSON.stringify(modalItemInfo.value));
    if (val != obj.expressId) {
      modalItem.value.expressNumber = '';
      showPrintExterFaceSheetBtn.value = false;
    } else {
      modalItem.value.expressNumber = obj.expressNumber;
      showPrintExterFaceSheetBtn.value = true;
    }
  }

  //打印电子面单
  const printExterFaceSheetBtn = async() => {
    let res = await getIntegralPrintApi({orderSn:modalItem.value.orderSn})
    if(res.state == 200){
      sucTip(res.msg)
      if(res.data){
        window.open(res.data, '_blank');
      }
    }else{
      failTip(res.msg)
    }
  }

  //电子面单获取物流单号
  const getExterFaceSheetBtn = ()=> {
    formRef.value
        .validate(['expressId'])
        .then(async(values) => {
          values.orderSn = modalItem.value.orderSn;
          let res = await getIntegralLogisticCodeApi(values)
          if(res.state == 200){
            if(filter_code.value){
              params.value.orderState = filter_code; 
            }
            get_list({ ...params.value, ...formValues.value });
            showPrintExterFaceSheetBtn.value = true
            modalItem.value.exterFaceSheet = res.data.logisticCode;
          }else{
            failTip(res.msg)
          }
        }).catch((err)=>{
         
        })
  }

  // 发货按钮打开弹窗
  const agreeDeliver = (item)=> {
    modalItem.value = item
    modalItem.value.deliverType = 1
    exterFaceSheet.value = item.expressNumber?item.expressNumber:''
    isAllowApplyExpressNum.value = item.expressNumber?false:true
    showPrintExterFaceSheetBtn.value = item.expressNumber?true:false
    demodalVisible.value = true
    modalItemInfo.value = JSON.parse(JSON.stringify(modalItem.value))
  }

  // 发货确定
  const deliverConfirm = ()=>{
    formRef.value.validate().then(async(values) => {
      values.orderSn = modalItem.value.orderSn;
      let res = await getIntegralOrderDeliverApi(values)
      if(res.state == 200){
        sucTip(res.msg)
        modalItem.value = {deliverType:1}
        demodalVisible.value =  false
        userStore.setDelKeepAlive(['PointOrderListToDetail'])
        let cur_param = {...formValues.value, ...params.value};
        get_list(cur_param);
      }else{
        failTip(res.msg)
      }
    })
  }

  // 请选择发货方式
  const redioOnChange = (e)=> {
    modalItem.value.expressId = undefined
  }

  // 发货弹框点击取消
  const sldDeliverHandleCancle = ()=> {
    demodalVisible.value = false
    formRef.value.resetFields();
  }

  //订单条件过滤器
  const clickFilter = (e) => {
    filter_code.value = e.target.value;
    let param = { pageSize: pageSize.value, current: 1, ...formValues.value };
    if (e.target.value) {
      param.orderState = e.target.value;
    }
    params.value.orderState = e.target.value;
    get_list(param);
  };

  //获取数据列表
  const get_list = async (param) => {
    loading.value = true;
    let res = await getOrderPointListApi({ ...param });
    if (res.state == 200) {
      loading.value = false;
      if (param.current > 1 && res.data.list.length == 0 && params.value.current > 1) {
        param.current = param.current - 1;
        get_list(params);
      } else {
        data.value = res.data;
      }
    } else {
      loading.value = false;
      failTip(res.msg);
    }
  };

  
  // 发货确定
  const virturalGoodsOrderDeliverConfirm = async(item)=>{
    let res = await getIntegralOrderDeliverApi({orderSn: item.orderSn})
    if(res.state == 200){
      sucTip(res.msg)
      demodalVisible.value =  false
      userStore.setDelKeepAlive(['PointOrderListToDetail'])
      let cur_param = {...formValues.value, ...params.value};
      get_list(cur_param);
    }else{
      failTip(res.msg)
    }
  }

  //改变页码
  const onPageChange = (page, pageSize) => {
    let curParams = {...formValues.value };
    if (filter_code.value) {
      curParams.orderState = filter_code.value;
    }
    formValues.value = curParams
    params.value = { pageSize: pageSize, current: page};
    get_list({...params.value,...curParams});
  };

  // 查看详情
  const goDetail = (item) => {
    userStore.setDelKeepAlive(['PointOrderListToDetail'])
    setTimeout(()=>{
      router.push({
        path: '/point/order_list_to_detail',
        query: {
          orderSn: item.orderSn,
        },
      });
    },200)
  };

  // 获取店铺启用的物流公司
  const get_express_list = async()=> {
    let res = await getExpressListApi({pageSize:list_com_page_more,expressState: 1 })
    if(res.state == 200){
      expressList.value = JSON.parse(JSON.stringify(res.data.list));
      email_expressList.value = JSON.parse(JSON.stringify(res.data.list.filter(item => item.isSupportFaceSheet == 1)));
    }else{
      failTip(res.msg)
    }
  }

  onMounted(() => {
    get_list({ pageSize: pageSize.value });
    get_express_list();
  });
</script>
<style lang="less">
  @import './style/order.less';
</style>
