<template>
  <div class="section_padding point_order_detail">
    <div class="section_padding_back">
      <SldComHeader :title="L('订单详情')" back />
      <div style="width: 100%; height: 10px"></div>
      <Spin :spinning="loading">
        <div class="height_detail">
          <div class="flex_row_center_start progress">
            <div
              class="flex_column_start_center item"
              v-for="(item, index) in return_progress_data"
              :key="index"
            >
              <div class="top flex_row_center_center">
                <span class="left_line" :style="{ borderColor: item.line_color }"></span>
                <img :src="item.icon" alt="" />
                <span class="right_line" :style="{ borderColor: item.line_color }"></span>
              </div>
              <span class="state" :style="{ color: item.state_color }">{{ item.state }}</span>
              <span class="time" :style="{ color: item.time_color }">{{ item.time }}</span>
            </div>
          </div>
          <div class="state_part flex_column_start_center" v-if="order_detail.orderState == 0">
            <span class="title">{{ L('订单已取消') }}</span>
            <span class="tip">{{ L('取消原因') }}:{{order_detail.refuseReason + (order_detail.refuseRemark ? (',' + order_detail.refuseRemark) : '') }}</span>
          </div>
          <div class="state_part flex_column_start_center" v-if="order_detail.orderState == 10">
            <span class="title">{{ L('订单已提交,等待买家付款') }}</span>
          </div>
          <div
            class="state_part flex_column_start_center"
            v-if="order_detail.orderState == 20"
          >
            <span class="title">{{ L('付款成功,等待卖家发货') }}</span>
            <div class="btnsty">
              <Popconfirm :title="L('该订单为虚拟商品订单，确认执行该操作吗?')"
                @confirm="virturalGoodsOrderDeliverConfirm" v-if="order_detail.isVirtualGoods == 2">
                <div class="deliver_btn">发货</div>
              </Popconfirm>
              <div class="deliver_btn" v-else @click="agreeReturn('deliver')">{{ L('发货') }}</div>

            </div>
          </div>
          <div class="state_part flex_column_start_center" v-if="order_detail.orderState == 30">
            <span class="title">{{ L('商品已发出,等待买家收货') }}</span>
            <div class="btnsty" v-if="order_detail.isVirtualGoods == 1">
              <div class="cancle_btn" @click="agreeReturn('flow')">{{ L('查看物流') }}</div>
              <div class="cancle_btn" @click="printExterFaceSheetBtn">{{ L('打印电子面单') }}</div>
            </div>
          </div>
          <div class="state_part flex_column_start_center" v-if="order_detail.orderState == 50">
            <span class="title">{{ L('买家已确认收货,订单完成') }}</span>
            <div class="btnsty" v-if="order_detail.isVirtualGoods == 1">
              <div class="cancle_btn" @click="agreeReturn('flow')">{{ L('查看物流') }}</div>
            </div>
          </div>
          <!-- 订单信息 start -->
          <div class="sld_common_title">{{ L('订单信息:') }}</div>
          <Description
            size="middle"
            :useCollapse="true"
            :bordered="true"
            :column="2"
            :data="order_detail"
            :schema="order_info"
          />
          <!-- 订单信息 end -->
          <!-- 收货人信息 start -->
          <div class="sld_common_title" v-if="order_detail.isVirtualGoods == 1">{{ L('收货人信息:') }}</div>
          <Description
            v-if="order_detail.isVirtualGoods == 1"
            size="middle"
            :useCollapse="true"
            :bordered="true"
            :column="2"
            :data="order_detail"
            :schema="receiver_info"
          />
          <!-- 收货人信息 end -->
          <!-- 用户预留信息 start -->
          <div
            class="sld_common_title"
            v-if="order_detail.isVirtualGoods == 2 && order_detail.orderReserveList.length > 0"
            >{{ L('用户预留信息') }}</div
          >
          <Description
            v-if="order_detail.isVirtualGoods == 2 && order_detail.orderReserveList.length > 0"
            size="middle"
            :useCollapse="true"
            :bordered="true"
            :column="2"
            :data="order_detail"
            :schema="reserve_info"
          />
          <!-- 用户预留信息 end -->
          <!-- 发票信息 start -->
          <div class="sld_common_title">{{ L('发票信息:') }}</div>
          <Description
            :class="{ invoice_info_box: invoice_info.length == 1 }"
            size="middle"
            :useCollapse="true"
            :bordered="true"
            :column="2"
            :data="order_detail"
            :schema="invoice_info"
          />
          <!-- 发票信息 end -->
          
          <!-- 商品信息 start -->
          <div class="sld_common_title">{{ L('商品信息') }}</div>
          <BasicTable
            :columns="columns_order_goods"
            :bordered="true"
            :pagination="false"
            :dataSource="order_detail.orderProductList"
            :maxHeight="400"
            :canResize="false"
          >
            <template #bodyCell="{ column, record, text }">
              <template v-if="column.dataIndex == 'productImage'">
                <div class="goods_info com_flex_row_flex_start">
                  <div class="goods_img" style="border: none">
                    <Popover placement="rightTop">
                      <template #content>
                        <div style="width: 200px">
                          <img :src="text" alt="" style="max-width: 100%; max-height: 100%" />
                        </div>
                      </template>
                      <div class="business_load_img">
                        <img :src="text" alt="" />
                      </div>
                    </Popover>
                  </div>
                  <div class="com_flex_column_space_between goods_detail">
                    <div
                      class="goods_name"
                      style="width: 380px; margin-top: 6px; white-space: initial"
                      :title="record.goodsName"
                    >
                      {{ record.goodsName }}
                    </div>
                    <span class="goods_brief" :title="record.specValues">
                      {{ record.specValues }}
                    </span>
                  </div>
                </div>
              </template>
            </template>
          </BasicTable>
          <!-- 商品信息 end -->
        </div>
      </Spin>
    </div>
    <Modal :title="L('商品发货')" :width="modal_width" :visible="demodalVisible" @ok="deliverConfirm" @cancel="sldDeliverHandleCancle" :okText="order_detail_info.deliverType == 3 ? L('打印并发货') : L('确定')" :confirmLoading="confirmLoading">
      <div v-if="order_detail_info.deliverType == '1'">
        <div :style="{
          width: modal_width+'px',
          paddingLeft: (modal_width * 0.11)+'px',
          marginTop: '5px',
          marginBottom:'8px',
        }">
          <div :style="{
            color: '#FF711E',
            width: (18 / 24 * (modal_width ? modal_width : 416))+'px',
            fontSize: '12px',
            borderBottom:'1px solid #f2f2f2',
            lineHeight: '28px'
          }">
            * {{ L('请认真填写物流公司及快递单号') }}
          </div>
        </div>
      </div>
      <Form layout="horizontal" ref="formRef" class="form_detail" :model="order_detail_info" >
        <div v-if="order_detail_info.deliverType != '1'" style="width: 100%;height:10px;"></div>
        <Form.Item :label="L('收货人姓名')" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}">
          <span>{{order_detail_info.receiverName}}</span>
        </Form.Item>
        <Form.Item :label="L('收货人电话')" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}">
          <span>{{order_detail_info.receiverMobile}}</span>
        </Form.Item>
        <Form.Item :label="L('收货人地址')" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}">
          <span style="word-break: break-all;">{{order_detail_info.receiverAreaInfo}}{{order_detail_info.receiverAddress}}</span>
        </Form.Item>
        <Form.Item :label="L('发货方式')" name="deliverType" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}" :rules="[{required: true,message:L('请选择发货方式')}]">
          <RadioGroup
            size="small"
            v-model:value="order_detail_info.deliverType"
            @change="redioOnChange($event)"
          >
            <Radio :value="1" > {{ L('物流发货') }} </Radio>
            <Radio :value="3"> {{ L('电子面单') }} </Radio>
            <Radio :value="2"> {{ L('无需物流') }} </Radio>
          </RadioGroup>
        </Form.Item>
        <Form.Item :label="L('物流公司')" name="expressId" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}" :rules="[{required: true,message:L('请选择物流公司')}]" v-if="order_detail_info.deliverType == 3">
          <Select
            :placeholder="L('请选择物流公司')"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            v-model:value="order_detail_info.expressId"
            @change="(e)=>handleSelExpress(e)"
          >
            <Select.Option
              v-for="(item, index) in email_expressList"
              :key="index"
              :value="item.expressId"
              >{{ item.expressName }}</Select.Option
            >
          </Select> 
        </Form.Item>
        <Form.Item :label="L('物流公司')" name="expressId" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}" :rules="[{required: true,message:L('请选择物流公司')}]" v-if="order_detail_info.deliverType == 1">
          <Select
            :placeholder="L('请选择物流公司')"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            v-model:value="order_detail_info.expressId"
            @change="(e)=>handleSelExpress(e)"
          >
            <Select.Option
              v-for="(item, index) in expressList"
              :key="index"
              :value="item.expressId"
              >{{ item.expressName }}</Select.Option
            >
          </Select> 
        </Form.Item>
        <Form.Item :label="L('快递单号')" name="expressNumber" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}" :rules="[{required: true,message:L('请输入物流单号')},rule]" v-if="order_detail_info.deliverType == 1" :extra="L('可以直接输入物流单号，也可以点击获取物流单号通过电子面单自动获取物流单号')">
          <div class="flex_row_start_center" style="position: relative;">
            <Input :maxLength="20" :placeholder="L('请输入物流单号')" v-model:value="order_detail_info.expressNumber"/>
            <a v-if="showPrintExterFaceSheetBtn" class="free_freight get_exter_face_sheet_btn" @click="printExterFaceSheetBtn">{{ L('打印电子面单') }}</a>
            <a v-else-if="isAllowApplyExpressNum" class="free_freight get_exter_face_sheet_btn" @click="getExterFaceSheetBtn">{{ L('获取物流单号') }}</a>
          </div>
        </Form.Item>
        <Form.Item :label="L('联系人')" name="deliverName" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}" :rules="[{required: true,message:L('请输入联系人')}]" v-if="order_detail_info.deliverType == 2">
          <Input :maxLength="10" :placeholder="L('请输入联系人')" v-model:value="order_detail_info.deliverName"/>
        </Form.Item>
        <Form.Item :label="L('联系方式')" name="deliverMobile" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}" :rules="[{required: true,message:L('请输入联系方式')},{pattern: mobile_reg,message:L('请输入正确的手机号')}]" v-if="order_detail_info.deliverType == 2">
          <Input :maxLength="11" :placeholder="L('请输入联系方式')" v-model:value="order_detail_info.deliverMobile"/>
        </Form.Item>
      </Form>
    </Modal>
    <SldModal
      :width="modal_width"
      :title="titleName"
      :visible="modalVisible"
      :content="operateData"
      :showFoot="showFoot"
      :height="600"
      :confirmBtnLoading="modalConfirmBtnLoading"
      @cancle-event="sldHandleCancle"
      @confirm-event="sldHandleConfirm"
    />
  </div>
</template>
<script>
  export default {
    name: 'PointOrderListToDetail',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, reactive, onMounted } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { Spin, Popover,Popconfirm,Modal,Radio,Select,Input,Form,RadioGroup } from 'ant-design-vue';
  import { useRoute, useRouter } from 'vue-router';
  import SldModal from '/@/components/SldModal/index.vue';
  import { BasicTable } from '/@/components/Table';
  import StandardTableRow from '/@/components/StandardTableRow/index.vue';
  import { Description } from '/@/components/Description/index';
  import { getIntegralBillOrderDetailApi,getExpressListApi,getIntegralPrintApi,getIntegralLogisticCodeApi,getIntegralOrderDeliverApi,getIntegralOrderGetTraceApi } from '/@/api/point/order';
  import { list_com_page_more,sucTip, failTip } from '/@/utils/utils';
  import { h } from 'vue';
  import { useUserStore } from '/@/store/modules/user';

  const vm = getCurrentInstance();
  const userStore = useUserStore();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const route = useRoute();
  const router = useRouter();
  const showFoot = ref(true);
  const modalConfirmBtnLoading = ref(false)
  const modal_width = ref(550)
  const rule = ref({pattern: /^[0-9a-zA-Z]*$/g,message:L('请输入正确的单号')})
  const mobile_reg = ref(/(1[3-9]\d{9}$)/)
  const expressList = ref([])
  const exterFaceSheet = ref('');//通过电子面单获取的物流单号
  const isAllowApplyExpressNum = ref(false);//是否允许通过电子面单申请物流单号
  const demodalVisible = ref(false)
  const showPrintExterFaceSheetBtn = ref(false);//已经有物流单号的话直接显示
  const email_expressList = ref([])
  const formRef = ref();
  const confirmLoading = ref(false);

  const loading = ref(true);
  const reserve_info = ref([]); //用户预留信息
  const query = ref(route.query);
  const order_detail = ref({});
  const order_detail_info = ref({});
  const return_progress_data = ref([]); //退货进度条
  const invoice_info = ref([
    {
      field: 'invoiceTitle',
      label: L('单位名称'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'taxCode',
      label: L('税号'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'receiverEmail',
      label: L('收票邮箱'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
  ]); //公司——普通发票
  const receiver_info = ref([
    {
      field: 'memberName',
      label: L('会员名称'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'receiverName',
      label: L('收货人'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'receiverMobile',
      label: L('收货人手机号'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'receiverAddress',
      label: L('收货地址'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
  ]);
  const order_info = ref([
    //订单信息
    {
      field: 'orderTypeValue',
      label: L('订单类型'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'orderSn',
      label: L('订单号'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'paymentName',
      label: L('支付方式'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'tradeSn',
      label: '支付流水号',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'orderRemark',
      label: L('订单备注'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
  ]);
  const goodsInfoList = ref([]); //商品信息
  const orderLogList = ref([]);
  const operateData = ref([]); //弹框操作数据
  const resList = ref([]); // 取消原因数据
  const modalVisible = ref(false);
  const titleName = ref('');
  const submiting = ref(false);
  const show_foot = ref(true);
  const propType = ref('');
  const deliverModal = ref(false);
  const deliverType = ref(''); //发货方式
  const columns_order_goods = ref([
    {
      title: L('商品信息'),
      dataIndex: 'productImage',
      align: 'center',
      width: 500,
    },
    {
      title: L('单价(元)'),
      dataIndex: 'cashPrice',
      align: 'center',
      width: 100,
    },
    {
      title: L('数量'),
      dataIndex: 'productNum',
      align: 'center',
      width: 100,
    },
  ]);
  const invoice_info_other = ref([
    //不需要发票的情况
    {
      field: 'invoiceStatus',
      label: L('是否需要开票'),
      labelMinWidth: 10,
      contentMinWidth: 100,
      render: (val, data) => {
        return L('否');
      },
    },
  ]);
  const invoice_info_personal = ref([
    {
      field: 'invoiceTitle',
      label: L('发票抬头'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'receiverEmail',
      label: L('收票邮箱'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
  ]); //个人发票
  const invoice_info_VAT = ref([
    {
      field: 'invoiceTitle',
      label: L('单位名称'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'taxCode',
      label: L('税号'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'registerAddr',
      label: L('注册地址'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'registerPhone',
      label: L('注册电话'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'bankName',
      label: L('开户银行'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'bankAccount',
      label: L('银行账户'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'receiverName',
      label: L('收票人'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'receiverMobile',
      label: L('收票电话'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'receiverAddress',
      label: L('收票地址'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
  ]); //公司发票——增值税发票

  // 发货按钮打开弹窗
  const agreeReturn = async(type)=> {
    let cancleBool = false;
    propType.value = type
    if(type=='deliver'){
      order_detail_info.value = JSON.parse(JSON.stringify(order_detail.value))
      order_detail_info.value.deliverType = 1
      get_express_list()
    }else if(type == 'flow'){
      if(order_detail.value.deliverType==2){
        Modal.info({
          width:470,
          title: L('该订单是自行配送，您可以联系配送人了解具体进度'),
          content: h('div', {}, [
            h('p', `${L('配送人姓名：')}${order_detail.value.deliverName}`),
            h('p', `${L('配送人手机号：')}${order_detail.value.deliverMobile}`),
          ]),
          onOk() {
          },
        });
      }else{
        let res = await getIntegralOrderGetTraceApi({ orderSn: order_detail.value.orderSn })
        if(res.state == 200){
          operateData.value.push({
            type: 'show_express', //物流进度
            content: res.data,
          })
          showFoot.value = false
          modal_width.value = 550
          titleName.value = L('物流信息')
          modalVisible.value = true
        }
      }
    }
  }

  //弹框取消操作
  const sldHandleCancle = () => {
    modalVisible.value = false
    operateData.value = []
  };

  // 弹框确定操作
  const sldHandleConfirm = (val)=> {
    
  }

  //电子面单获取物流单号
  const getExterFaceSheetBtn = ()=> {
    formRef.value
        .validate(['expressId'])
        .then(async(values) => {
          values.orderSn = order_detail.value.orderSn;
          let res = await getIntegralLogisticCodeApi(values)
          if(res.state == 200){
            userStore.setDelKeepAlive(['PointOrderList'])
            get_order_detail({ orderSn: route.query.orderSn });
            showPrintExterFaceSheetBtn.value = true
            order_detail_info.value.exterFaceSheet = res.data.logisticCode;
          }else{
            failTip(res.msg)
          }
        }).catch((err)=>{
         
        })
  }

  
  // 发货确定
  const deliverConfirm = ()=>{
    formRef.value.validate().then(async(values) => {
      values.orderSn = order_detail.value.orderSn;
      let res = await getIntegralOrderDeliverApi(values)
      if(res.state == 200){
        sucTip(res.msg)
        order_detail_info.value = {}
        demodalVisible.value =  false
        userStore.setDelKeepAlive(['PointOrderList'])
        get_order_detail({ orderSn: route.query.orderSn });
      }
    })
  }

  // 发货确定
  const virturalGoodsOrderDeliverConfirm = async()=>{
    let res = await getIntegralOrderDeliverApi({orderSn: order_detail.value.orderSn})
    if(res.state == 200){
      sucTip(res.msg)
      demodalVisible.value = false;
      userStore.setDelKeepAlive(['PointOrderList'])
      get_order_detail({ orderSn: route.query.orderSn });
    }else{
      failTip(res.msg)
    }
  }

  //打印电子面单
  const printExterFaceSheetBtn = async() => {
    let res = await getIntegralPrintApi({orderSn:order_detail.value.orderSn})
    if(res.state == 200){
      sucTip(res.msg)
      if(res.data){
        window.open(res.data, '_blank');
      }
    }else{
      failTip(res.msg)
    }
  }

  // 请选择物流公司
  const handleSelExpress = (val)=> {
    let obj = JSON.parse(JSON.stringify(order_detail.value));
    if (val != obj.expressId) {
      order_detail.value.expressNumber = '';
      showPrintExterFaceSheetBtn.value = false;
    } else {
      order_detail.value.expressNumber = obj.expressNumber;
      showPrintExterFaceSheetBtn.value = true;
    }
  }


  // 请选择发货方式
  const redioOnChange = (e)=> {
    order_detail_info.value.expressId = undefined
  }

  // 发货弹框点击取消
  const sldDeliverHandleCancle = ()=> {
    demodalVisible.value = false
    formRef.value.resetFields();
  }

  const get_order_detail = async (params) => {
    loading.value = true;
    let res = await getIntegralBillOrderDetailApi(params);
    if (res.state == 200) {
      loading.value = false;
      order_detail.value = res.data;
      orderLogList.value = res.data.orderLogList;
      //收票信息
      if (order_detail.value.invoiceStatus == 1) {
        let invoice_type = '';
        if (order_detail.value.invoiceInfo.titleType == 1) {
          //个人发票
          invoice_info.value = invoice_info_personal.value;
          invoice_type = L('个人发票');
        } else {
          //公司发票
          if (order_detail.value.invoiceInfo.invoiceType != 1) {
            //增值税发票
            invoice_info.value = invoice_info_VAT.value;
            invoice_type = L('增值税专用发票');
          } else {
            invoice_type = L('普通发票');
          }
        }

        //需要发票
        for (let item in invoice_info.value) {
          let name = invoice_info.value[item].field;
          invoice_info.value[item].render = (val, data) => {
            return !order_detail.value['invoiceInfo'][name]
              ? '--'
              : order_detail.value['invoiceInfo'][name];
          };
        }
        let invoice_content =
          order_detail.value.invoiceInfo.invoiceContent == 1 ? L('商品明细') : L('商品类别');
        invoice_info.value = [
          {
            field: 'invoiceTypeCombine',
            label: L('发票类型'),
            labelMinWidth: 10,
            contentMinWidth: 100,
            render: (val, data) => {
              return invoice_type;
            },
          },
          {
            field: 'invoiceContent',
            label: L('发票内容'),
            labelMinWidth: 10,
            contentMinWidth: 100,
            render: (val, data) => {
              return invoice_content;
            },
          },
          ...invoice_info.value,
        ];
      } else {
        //不需要发票
        invoice_info.value = invoice_info_other.value;
      }
      //收货人信息
      for (let item in receiver_info.value) {
        if (receiver_info.value[item].field == 'receiverAddress') {
          receiver_info.value[item].render = (val, data) => {
            return order_detail.value.receiverAddress;
          };
        } else {
          receiver_info.value[item].render = (val, data) => {
            return order_detail.value[receiver_info.value[item].field];
          };
        }
      }
      //订单信息
      for (let item in order_info.value) {
        if (order_info.value[item].field == 'orderTypeValue') {
          order_info.value[item].render = (val, data) => {
            return order_detail.value.isVirtualGoods == 2 ? L('虚拟订单') : L('普通订单');
          };
        } else {
          order_info.value[item].render = (val, data) => {
            return order_detail.value[order_info.value[item].field] == ''||
            order_detail.value[order_info.value[item].field] == null
              ? '--'
              : order_detail.value[order_info.value[item].field];
          };
        }
      }
      //用户预留信息
      if (
        order_detail.value.isVirtualGoods == 2 &&
        order_detail.value.orderReserveList.length != undefined &&
        order_detail.value.orderReserveList.length
      ) {
        reserve_info.value = [];
        order_detail.value.orderReserveList.map((item) => {
          reserve_info.value.push({
            field: item.reserveId,
            label: item.reserveName,
            labelMinWidth: 10,
            contentMinWidth: 100,
            render: (val, data) => {
              return item.reserveValue;
            },
          });
        });
      }
      return_progress_data.value = [];
      if (order_detail.value.orderState == 0) {
        // 订单取消
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/submit_pass.png', import.meta.url).href,
          state: L('提交订单'),
          time:
            orderLogList.value.length > 0 && orderLogList.value[0].logTime != undefined
              ? orderLogList.value[0].logTime
              : '',
          state_color: 'rgba(255, 113, 30, .6)',
          time_color: 'rgba(255, 113, 30, .3)',
          line_color: 'rgba(255, 113, 30, .3)',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/fail_current.png', import.meta.url).href,
          state: L('订单取消'),
          time:
            orderLogList.value.length > 1 && orderLogList.value[1].logTime != undefined
              ? orderLogList.value[1].logTime
              : '',
          state_color: 'rgba(255, 113, 30,1)',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: 'rgba(255, 113, 30,1)',
        });
      } else if (order_detail.value.orderState == 10) {
        //未付款订单
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/submit_current.png', import.meta.url).href,
          state: L('提交订单'),
          time:
            orderLogList.value.length > 0 && orderLogList.value[0].logTime != undefined
              ? orderLogList.value[0].logTime
              : '',
          state_color: 'rgba(255, 113, 30,1)',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: 'rgba(255, 113, 30,1)',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/pay_future.png', import.meta.url).href,
          state: L('付款成功'),
          time: '',
          state_color: '#999999',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: '#eee',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/deliver_future.png', import.meta.url).href,
          state: L('商品发货'),
          time: '',
          state_color: '#999999',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: '#eee',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/suc_future.png', import.meta.url).href,
          state: L('订单完成'),
          time: '',
          state_color: '#999999',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: '#222',
        });
      } else if (order_detail.value.orderState == 20) {
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/submit_pass.png', import.meta.url).href,
          state: L('提交订单'),
          time:
            orderLogList.value.length > 0 && orderLogList.value[0].logTime != undefined
              ? orderLogList.value[0].logTime
              : '',
          state_color: 'rgba(255, 113, 30, .6)',
          time_color: 'rgba(255, 113, 30, .3)',
          line_color: 'rgba(255, 113, 30, .3)',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/pay_current.png', import.meta.url).href,
          state: L('付款成功'),
          time:
            orderLogList.value.length > 1 && orderLogList.value[1].logTime != undefined
              ? orderLogList.value[1].logTime
              : '',
          state_color: 'rgba(255, 113, 30,1)',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: 'rgba(255, 113, 30,1)',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/deliver_future.png', import.meta.url).href,
          state: L('商品发货'),
          time: '',
          state_color: '#999999',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: '#eee',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/suc_future.png', import.meta.url).href,
          state: L('订单完成'),
          time: '',
          state_color: '#999999',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: '#eee',
        });
      } else if (order_detail.value.orderState == 30) {
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/submit_pass.png', import.meta.url).href,
          state: L('提交订单'),
          time:
            orderLogList.value.length > 0 && orderLogList.value[0].logTime != undefined
              ? orderLogList.value[0].logTime
              : '',
          state_color: 'rgba(255, 113, 30, .6)',
          time_color: 'rgba(255, 113, 30, .3)',
          line_color: 'rgba(255, 113, 30, .3)',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/pay_pass.png', import.meta.url).href,
          state: L('付款成功'),
          time:
            orderLogList.value.length > 1 && orderLogList.value[1].logTime != undefined
              ? orderLogList.value[1].logTime
              : '',
          state_color: 'rgba(255, 113, 30, .6)',
          time_color: 'rgba(255, 113, 30, .3)',
          line_color: 'rgba(255, 113, 30, .3)',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/deliver_current.png', import.meta.url).href,
          state: L('商品发货'),
          time:
            orderLogList.value.length > 2 && orderLogList.value[2].logTime != undefined
              ? orderLogList.value[2].logTime
              : '',
          state_color: 'rgba(255, 113, 30,1)',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: 'rgba(255, 113, 30,1)',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/suc_future.png', import.meta.url).href,
          state: L('订单完成'),
          time: '',
          state_color: '#999999',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: '#eee',
        });
      } else if (order_detail.value.orderState == 40) {
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/submit_pass.png', import.meta.url).href,
          state: L('提交订单'),
          time:
            orderLogList.value.length > 0 && orderLogList.value[0].logTime != undefined
              ? orderLogList.value[0].logTime
              : '',
          state_color: 'rgba(255, 113, 30, .6)',
          time_color: 'rgba(255, 113, 30, .3)',
          line_color: 'rgba(255, 113, 30, .3)',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/pay_pass.png', import.meta.url).href,
          state: L('付款成功'),
          time:
            orderLogList.value.length > 0 && orderLogList.value[1].logTime != undefined
              ? orderLogList.value[1].logTime
              : '',
          state_color: 'rgba(255, 113, 30, .6)',
          time_color: 'rgba(255, 113, 30, .3)',
          line_color: 'rgba(255, 113, 30, .3)',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/deliver_pass.png', import.meta.url).href,
          state: L('商品发货'),
          time:
            orderLogList.value.length > 1 && orderLogList.value[2].logTime != undefined
              ? orderLogList.value[2].logTime
              : '',
          state_color: 'rgba(255, 113, 30, .6)',
          time_color: 'rgba(255, 113, 30, .3)',
          line_color: 'rgba(255, 113, 30, .3)',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/suc_current.png', import.meta.url).href,
          state: L('订单完成'),
          time:
            orderLogList.value.length > 2 && orderLogList.value[3].logTime != undefined
              ? orderLogList.value[3].logTime
              : '',
          state_color: 'rgba(255, 113, 30,1)',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: 'rgba(255, 113, 30,1)',
        });
      }
    } else {
      failTip(res.msg);
    }
  };

  // 生成本地路径
  const newImg = (img) => {
    return new URL(img, import.meta.url).href;
  };

  // 获取店铺启用的物流公司
  const get_express_list = async()=> {
    let cancleBool = false, deliverBool = false;
    let res = await getExpressListApi({pageSize:list_com_page_more,expressState: 1 })
    if(res.state == 200){
      deliverBool = true;
      expressList.value = JSON.parse(JSON.stringify(res.data.list));
      email_expressList.value = JSON.parse(JSON.stringify(res.data.list.filter(item => item.isSupportFaceSheet == 1)));
      isAllowApplyExpressNum.value =  order_detail.value.expressNumber ? false : true//是否允许通过电子面单申请物流单号
      showPrintExterFaceSheetBtn.value = order_detail.value.expressNumber ? true : false//已经有物流单号的话直接显示
      demodalVisible.value = deliverBool && !demodalVisible.value
    }else{
      failTip(res.msg)
    }
  }

  onMounted(() => {
    get_order_detail({ orderSn: route.query.orderSn });
  });
</script>
<style lang="less">
  @import './style/order.less';

  .business_load_img {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    margin: auto;

    img {
      max-width: 100%;
      max-height: 100%;
    }
  }
  p{
    margin-bottom: 0;
  }
</style>
