<template>
  <div class="section_padding select_mall_goods seckill">
    <div class="section_padding_back full_activity">
      <SldComHeader :title="L('导入商城商品')" />
      <div class="com_line" style="height: 1px"></div>
      <Spin :spinning="loading">
        <div class="add_coupon_height">
          <ShowMoreHelpTip :tipData="goodsTip"></ShowMoreHelpTip>
          <Form layout="inline" ref="formRef" :model="detail">
            <!-- 活动基本信息 start -->
            <CommonTitle :text="L('基本信息')"  style="margin-top: 10px" />
            <div class="full_acm_activity flex_column_start_start">
              <!-- 使用时间 start -->
              <div class="item flex_row_start_start">
                  <div class="left"> <span style="color: red">*</span>{{ L('商品发布') }} </div>
                  <div class="right">
                    <Form.Item name="sellNow" style="width: 400px">
                      <RadioGroup size="small" v-model:value="detail.sellNow">
                        <Radio :value="true">{{ L('立即售卖') }}</Radio>
                        <Radio :value="false">{{ L('暂不售卖，放入仓库中') }}</Radio>
                      </RadioGroup>
                    </Form.Item>
                  </div>
                </div>
                <!-- 使用时间 end -->
              <!-- 活动标签 start -->
              <div class="item flex_row_start_center">
                <div class="left"> <span style="color: red">*</span>{{ L('积分商品标签') }}</div>
                <div class="right">
                  <Form.Item name="labelId" style="width: 400px" :rules="[{
                    required: true,
                    message: L('请选择积分商品标签'),
                  }]">
                    <TreeSelect
                      v-model:value="detail.labelId"
                      :style="{ width: '100%' }"
                      :treeData="point_label_list"
                      allowClear
                      :treeDefaultExpandAll="true"
                      :treeCheckable="true"
                      showCheckedStrategy="SHOW_PARENT"
                      :placeholder="L('请选择积分商品标签')"
                      :dropdownStyle="{ maxHeight: '300px' }"
                      @change="handleSelLabel"
                      :getPopupContainer="triggerNode => triggerNode.parentNode"
                    />
                  </Form.Item>
                </div>
              </div>
              <!-- 活动标签 end -->
            </div>
            <!-- 活动基本信息 end -->
            <CommonTitle describeColor="#FF7F40" style="margin-top: 10px" :describe="L('提醒：至少添加一个商品')">
              <template #button>
                  <div class="toolbar_btn" @click="addGoods">
                    <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" :fillColor="'rgb(255, 89, 8)'" />
                    <span style="margin-left: 4px">{{ L('添加商城商品') }}</span>
                  </div>
                </template>
            </CommonTitle>
            <div class="sele_goods" v-for="(item,indexs) in detail.selectedRows" :key="indexs">
              <img :src="del_seckill_goods" alt="" class="del_spu" @click="delSpu(item.goodsId)">
              <div class="goods_info flex_row_between_start">
                <div class="goods_info_left flex_row_start_start">
                  <div class="goods_img_wrap flex_row_center_center">
                    <img :src="item.mainImage" alt="" class="goods_img">
                  </div>
                  <p class="goods_name">{{ item.goodsName }}</p>
                </div>
                <div class="goods_info_right flex_row_end_end">
                  <Popconfirm @confirm="(e) => {
                              batchConfirm(e, 'integralPrice', item,indexs);
                            }">
                    <template #title>
                      <InputNumber :min="1" :max="99999999" :precision="0" style="width: 100%;" v-model:value="battchVal"  @change="(e) => {handleFieldBattchChange(e, 'integralPrice', item)}"></InputNumber>
                    </template>
                    <div class="batch_btn flex_row_center_center">
                      {{ L('修改积分') }}
                    </div>
                  </Popconfirm>
                  <Popconfirm @confirm="(e) => {
                              batchConfirm(e, 'cashPrice', item,indexs);
                            }">
                    <template #title>
                      <InputNumber :min="0.01" :max="9999999" :precision="2" style="width: 100%;" v-model:value="battchVal" @change="(e) => {handleFieldBattchChange(e, 'cashPrice', item)}"></InputNumber>
                    </template>
                    <div class="batch_btn flex_row_center_center">
                      {{ L('修改现金') }}
                    </div>
                  </Popconfirm>
                  <Popconfirm @confirm="(e) => {
                              batchConfirm(e, 'productStock', item,indexs);
                            }">
                    <template #title>
                      <InputNumber :min="1" :max="99999999" :precision="0" style="width: 100%;" v-model:value="battchVal" @change="(e) => {handleFieldBattchChange(e, 'productStock', item)}"></InputNumber>
                    </template>
                    <div class="batch_btn flex_row_center_center">
                      {{ L('修改库存') }}
                    </div>
                  </Popconfirm>
                  <Popconfirm @confirm="(e) => {
                              batchConfirm(e, 'productStockWarning', item,indexs);
                            }">
                    <template #title>
                      <InputNumber :min="0" :max="99999999" :precision="0" style="width: 100%;" v-model:value="battchVal" @change="(e) => {handleFieldBattchChange(e, 'productStockWarning', item)}"></InputNumber>
                    </template>
                    <div class="batch_btn flex_row_center_center">
                      {{ L('修改预警值') }}
                    </div>
                  </Popconfirm>
                  <div class="batch_btn flex_row_center_center">
                    <Checkbox @change="setAll($event, item)">
                      <span class="sel_all">{{ L('全部参与') }}</span>
                    </Checkbox>
                  </div>
                </div>
              </div>
              <BasicTable
                :maxHeight="300"
                rowKey="productId"
                :columns="columns_spec"
                :dataSource="item.productList"
                :pagination="false"
                :bordered="true"
                :ellipsis="false"
              >
                <template #headerCell="{ column }">
                  <template v-if="column.dataIndex == 'productPrice'">
                    <span><span style="color: #FF2929;font-size:13px;">*</span>{{ L('商品原价(¥)') }}</span>
                  </template>
                  <template v-else-if="column.dataIndex == 'integralPrice'">
                    <span><span style="color: #FF2929;font-size:13px;">*</span>{{ L('积分') }}</span>
                  </template>
                  <template v-else-if="column.dataIndex == 'cashPrice'">
                    <span><span style="color: #FF2929;font-size:13px;">*</span>{{ L('现金(¥)') }}</span>
                  </template>
                  <template v-else-if="column.dataIndex == 'productStock'">
                    <span><span style="color: #FF2929;font-size:13px;">*</span>{{ L('库存') }}</span>
                  </template>
                  <template v-else-if="column.dataIndex == 'firstExpand'">
                    <div class="flex_row_center_center">
                      <span>{{ L('定金膨胀') }}</span>
                    </div>
                  </template>
                  <template v-else>
                    {{ column.customTitle }}
                  </template>
                </template>
                <template #bodyCell="{ column, text, record,index }">
                  <template v-if="column.dataIndex === 'productPrice'">
                    <Form.Item
                      :name="['selectedRows', indexs, 'productList',index,'productPrice']"
                      :rules="record.state == 1?[
                        {
                          required: true,
                          message: L('该项必填'),
                        },
                        { validator: (rule, value) => validatorMarketPrice(rule, value, record.cashPrice) }
                      ]:[]"
                    >
                      <InputNumber
                        :max="9999999"
                        :min="0.01"
                        :precision="2"
                        v-model:value="record.productPrice"
                      />
                    </Form.Item>
                  </template>
                  <template v-if="column.dataIndex === 'integralPrice'">
                    <Form.Item
                      :name="['selectedRows', indexs, 'productList',index,'integralPrice']"
                      :rules="(record.cashPrice != undefined && record.cashPrice || record.state != 1)?[{ validator: (rule, value) => validatorIntegral(rule, value)}]:[
                        {
                          required: true,
                          message: L('该项必填'),
                        },
                        { validator: (rule, value) => validatorIntegral(rule, value)}
                      ]"
                    >
                      <InputNumber
                        :max="99999999"
                        :min="record.cashPrice != undefined && record.cashPrice ? 0 : 1"
                        :precision="0"
                        v-model:value="record.integralPrice"
                      />
                    </Form.Item>
                  </template>
                  <template v-if="column.dataIndex === 'cashPrice'">
                    <Form.Item
                      :name="['selectedRows', indexs, 'productList', index, 'cashPrice']"
                      :rules="(record.integralPrice || record.state != 1)?[]:[
                        {
                          required: true,
                          message: L('该项必填'),
                        },
                      ]"
                    >
                      <InputNumber
                        :max="9999999"
                        :min="record.integralPrice ? 0 : 0.01"
                        :precision="2"
                        v-model:value="record.cashPrice"
                      />
                    </Form.Item>
                  </template>
                  <template v-if="column.dataIndex === 'productStock'">
                    <Form.Item
                      :name="['selectedRows', indexs, 'productList', index, 'productStock']"
                      :rules="[
                        {
                          required: true,
                          message: L('该项必填'),
                        },
                      ]"
                    >
                      <InputNumber
                        :max="99999999"
                        :min="1"
                        :precision="0"
                        v-model:value="record.productStock"
                      />
                    </Form.Item>
                  </template>
                  <template v-if="column.dataIndex === 'presellStock'">
                    <Form.Item
                      :name="['selectedRows', indexs, 'productList', index, 'presellStock']"
                      :rules="record.state == 1?[
                        {
                          required: true,
                          message: L('该项必填'),
                        },
                      ]:[]"
                    >
                      <InputNumber
                        :max="99999999"
                        :min="1"
                        :precision="0"
                        v-model:value="record.presellStock"
                      />
                    </Form.Item>
                  </template>
                  <template v-if="column.dataIndex === 'productStockWarning'">
                    <Form.Item
                      :name="['selectedRows', indexs, 'productList', index, 'productStockWarning']"
                    >
                      <InputNumber
                        :max="300"
                        :min="0"
                        :precision="0"
                        v-model:value="record.productStockWarning"
                      />
                    </Form.Item>
                  </template>
                  <template v-if="column.dataIndex === 'productCode'">
                    <Form.Item
                      :name="['selectedRows', indexs, 'productList', index, 'productCode']"
                    >
                      <Input
                        :maxLength="20"
                        v-model:value="record.productCode"
                      />
                    </Form.Item>
                  </template>
                  <template v-if="column.dataIndex === 'barCode'">
                    <Form.Item
                      :name="['selectedRows', indexs, 'productList', index, 'barCode']"
                    >
                      <Input
                        :maxLength="20"
                        v-model:value="record.barCode"
                      />
                    </Form.Item>
                  </template>
                  <template v-if="column.dataIndex === 'state'">
                    <Switch
                      @change="
                        (e) =>
                        handleFieldChange(e ? 1 : 2, 'state', record,index,indexs)
                      "
                      :checked="text == 1 ? true : false"
                    />
                  </template>
                </template>
              </BasicTable>
            </div>
            <div style="width: 100%; height: 60px; background: #fff"></div>
          </Form>
          <div style="width: 100%; height: 20px; background: #fff"></div>
        </div>
        <div
          class="m_diy_bottom_wrap"
          :style="{ position: 'fixed', left: getRealWidth + 10 + 'px' }"
        >
          <div class="add_goods_bottom_btn" @click="goBack"> {{ L('返回') }} </div>
          <div class="add_goods_bottom_btn add_goods_bottom_btn_sel" @click="handleSaveAllData">
            {{ L('保存') }}
          </div>
        </div>
      </Spin>
    </div>
    <SldSelMoreLeftRightGoods 
      @confirm-event="seleGoods"
      @cancle-event="sldHandleCancle" 
      :width="1000"
      :selectedRow="sele_more_goods.info"
      :selectedRowKey="sele_more_goods.ids"
      :title="L('请选择商品(至少选择一个)')"
      :height='height - 400'
      :modalVisible="modalVisibleGoods"
      :extra="sele_more_goods"
      >

    </SldSelMoreLeftRightGoods>
  </div>
</template>
<script>
  export default {
    name: 'goodsListToImport',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, reactive, onMounted } from 'vue';
  import { Spin,Form,Popconfirm,InputNumber,Checkbox,Switch,RadioGroup,Radio,TreeSelect,Tooltip,Input } from 'ant-design-vue';
  import { sucTip, failTip } from '@/utils/utils';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import CommonTitle from '/@/components/CommonTitle/index.vue';
  import SldSelMoreLeftRightGoods from '/@/components/SldSelMoreLeftRightGoods/index.vue';
  import { BasicTable } from '/@/components/Table';
  import del_seckill_goods from '/src/assets/images/del_seckill_goods.png';
  import {
    getIntegralGoodsLabelListApi,
    getIntegralGoodsImportGoodsApi
  } from '/@/api/point/goods';
  import { getSettingListApi } from '/@/api/common/common';
  import { useRoute, useRouter } from 'vue-router';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import { list_com_page_more } from '/@/utils/utils';
  import ShowMoreHelpTip from '/@/components/ShowMoreHelpTip/index.vue'
  import { pageClose } from '/@/utils/utils';
  import { useMultipleTabStore } from '/@/store/modules/multipleTab';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const loading = ref(false);
  const tabStore = useMultipleTabStore();
  const route = useRoute();
  const router = useRouter();
  const { getRealWidth } = useMenuSetting();
  const goodsTip = ref([]);
  const battchVal = ref('');//批量设置里面的值
  const point_label_list = ref([])//活动标签
  const modalVisibleGoods = ref(false);
  const formRef = ref();
  const height = ref(document.body.clientHeight)
  const sele_more_goods = ref({
    info: [],//选择的商品数组
    ids: [],//选择的商品id数组
    min_num: 1,//最小数量，0为不限制
  })
  const detail = ref({
    selectedRows:[],
    selectedRowKeys:[],
    sellNow:true
  })

  const sel_label_data = ref([])//选择的积分标签列表
  const convert_rate = ref(0)//兑换比例
  // 定金预售
  const columns_spec = ref([
    {
      title: L('SKU规格'),
      dataIndex: 'specValues',
      align: 'center',
      width: 150,
      customRender: ({ text, record, index }) => {
        return text ? text : L('默认');
      },
    },
    {
      title: L('商品原价(¥)'),
      dataIndex: 'productPrice',
      align: 'center',
      width: 150,
    },
    {
      title: L('积分'),
      dataIndex: 'integralPrice',
      align: 'center',
      width: 150,
    },
    {
      title: L('现金(¥)'),
      dataIndex: 'cashPrice',
      align: 'center',
      width: 150,
    },
    {
      title: L('库存'),
      dataIndex: 'productStock',
      align: 'center',
      width: 150,
    },
    {
      title: L('重量(KG)'),
      dataIndex: 'weight',
      align: 'center',
      width: 150,
    },
    {
      title: L('长(CM)'),
      dataIndex: 'length',
      align: 'center',
      width: 150,
    },
    {
      title: L('宽(CM)'),
      dataIndex: 'width',
      align: 'center',
      width: 150,
    },
    {
      title: L('高(CM)'),
      dataIndex: 'height',
      align: 'center',
      width: 150,
    },
    {
      title: L('预警值'),
      dataIndex: 'productStockWarning',
      align: 'center',
      width: 150,
    },
    {
      title: L('货号'),
      dataIndex: 'productCode',
      align: 'center',
      width: 150,
    },
    {
      title: L('条形码'),
      dataIndex: 'barCode',
      align: 'center',
      width: 150,
    },
    {
      title: L('参与'),
      dataIndex: 'state',
      align: 'center',
      width: 150,
    },
  ])
  
  // 
  const handleSelLabel = (value, label, extra)=> {
    let tmp_label_ids = [];
    if (value.length > 0) {
      if (extra.allCheckedNodes.length != undefined && extra.allCheckedNodes.length) {
        extra.allCheckedNodes.map(item => {
          if (item.children != undefined && item.children.length) {
            item.children.map(child => {
              let target = child.node != undefined ? child.node.key : child.key;
              tmp_label_ids.push(target);
            });
          } else {
            let target = item.node != undefined ? item.node.key : item.key;
            tmp_label_ids.push(target);
          }
        });
      }
    }
    sel_label_data.value = tmp_label_ids;
  }

  //验证商品原价，必须比现金价大才可以
  const validatorMarketPrice = (rule, value, cashPrice) => {
    if (value && value <= cashPrice) {
      return Promise.reject(L('应大于现金价'));
    }
    return Promise.resolve();
  };

  //验证积分数需为平台设置的兑换比例积分的整数倍
  const validatorIntegral = (rule, value) => {
    if (value % convert_rate.value > 0) {
      return Promise.reject(`${L('需为')}${convert_rate.value}${L('的整数倍')}`);
    }
    return Promise.resolve();
  };

  //获取活动标签
  const getPointLabels = async(id)=> {
    let res = await getIntegralGoodsLabelListApi({ pageSize: list_com_page_more })
    if(res.state == 200){
      for (let i in res.data.list) {
        res.data.list[i].key = res.data.list[i].labelId;
        res.data.list[i].value = res.data.list[i].labelId;
        res.data.list[i].title = res.data.list[i].labelName;
        if (res.data.list[i].children != null && res.data.list[i].children.length > 0) {
          res.data.list[i].children.map(item => {
            item.key = item.labelId;
            item.value = item.labelId;
            item.title = item.labelName;
          });
        } else {
          res.data.list[i].disableCheckbox = true;
        }
      }
      point_label_list.value = res.data.list
    }
  }

  // 打开商品弹窗
  const addGoods = ()=> {
    modalVisibleGoods.value = true
  }

  // 关闭弹窗
  const sldHandleCancle = ()=>{
    modalVisibleGoods.value = false
  }

  // 弹窗点击确定
  const seleGoods = (selectedRowsP,selectedRowKeysP)=>{
    selectedRowsP = JSON.parse(JSON.stringify(selectedRowsP))
    selectedRowsP.map((item, index) => {
      item.productList.map((child_item) => {
        child_item.goodsId = item.goodsId;
        child_item.state = 1;
        if(child_item.productStockWarning<0&&child_item.productStockWarning!=null){
          child_item.productStockWarning = 0
        }
      });
    });
    //如果多次选择的话，数据要保留之前的
    detail.value.selectedRowKeys.map((item) => {
      if (selectedRowKeysP.indexOf(item) > -1) {
        let pre_item_data = detail.value.selectedRows.filter(val => val.goodsId == item)[0];
        for (let i = 0; i < selectedRowsP.length; i++) {
          if (selectedRowsP[i].goodsId == item) {
            selectedRowsP[i] = { ...pre_item_data };
            break;
          }
        }
      }
    });
    sele_more_goods.value.ids = [...selectedRowKeysP];
    sele_more_goods.value.info = JSON.parse(JSON.stringify(selectedRowsP));
    detail.value.selectedRows = selectedRowsP
    detail.value.selectedRowKeys = selectedRowKeysP
    sldHandleCancle()
  }

  // 商品批量设置
  const batchConfirm = (e, type, val,indexs)=> {
    let cur_goods = detail.value.selectedRows.filter(item=>item.goodsId == val.goodsId)[0];
    if(!battchVal.value){
      return false
    }

    let sku_product_id = [];
    cur_goods.productList.map((item,index) => {
      item[type] = battchVal.value;
      sku_product_id.push(item.productId);
      formRef.value.clearValidate([['selectedRows', indexs, 'productList',index,type]]);
    });
    battchVal.value = ''
   
  }

  //批量设置
  const handleFieldBattchChange = (e, type, item)=> {
    battchVal.value = e
  }

  //全部参与事件
  const setAll = (e, val) => {
    let states = [];
    for (let i in detail.value.selectedRows) {
      if (detail.value.selectedRows[i].goodsId == val.goodsId) {
        detail.value.selectedRows[i].productList.map(item => {
          item.state = e.target.checked ? 1 : 0;
          states.push('state'+item.productId);
        });
        break;
      }
    }
    if(!e.target.checked){
      detail.value.selectedRows.forEach((item,indexs)=>{
        item.productList.forEach((it,index)=>{
          formRef.value.clearValidate([['selectedRows', indexs, 'productList',index,'productPrice'],['selectedRows', indexs, 'productList',index,'integralPrice'],['selectedRows', indexs, 'productList',index,'cashPrice'],['selectedRows', indexs, 'productList',index,'productStock']]);
        })
      })
    }
  };

  const handleFieldBlur = (val, fieldName, record,min,max)=>{
    val = Number(val.target.value)
    if(typeof(val)=='number'&&!Object.is(val,NaN)){
      if(val<min&&val.length!=0){
        val = min
      }
      if(val>max&&val.length!=0){
        val = max
      }
      if(val.length!=0){
        handleFiledContent(val*1,fieldName, record);
      }
    }else if(Object.is(val,NaN)){
      val = min
      handleFiledContent(val*1,fieldName, record);
    }
  }

  const handleFieldChange = (val, fieldName, record,index,indexs)=> {
    handleFiledContent(val, fieldName, record);
    formRef.value.clearValidate([['selectedRows', indexs, 'productList',index,'productPrice'],['selectedRows', indexs, 'productList',index,'integralPrice'],['selectedRows', indexs, 'productList',index,'cashPrice'],['selectedRows', indexs, 'productList',index,'productStock']]);
  }

  //spec_data_table 表格编辑事件
  const handleFiledContent = (val, fieldName, record,index,indexs)=> {
    let tar_sku_list = detail.value.selectedRows.filter(item => item.goodsId == record.goodsId);
    if (tar_sku_list.length > 0) {
      let tar_data = tar_sku_list[0].productList.filter(item => item.productId == record.productId);
      if (tar_data.length > 0) {
        tar_data[0][fieldName] = val;
      }
    }
  }

  //删除添加的商品spu
  const delSpu = (goodsId) => {
    detail.value.selectedRows = detail.value.selectedRows.filter(item => item.goodsId != goodsId);
    detail.value.selectedRowKeys = detail.value.selectedRowKeys.filter(item => item != goodsId);
    sele_more_goods.value.ids = [...detail.value.selectedRowKeys];
    sele_more_goods.value.info = JSON.parse(JSON.stringify(detail.value.selectedRows));
  };

  // 返回
  const goBack = () => {
    if (window.history && window.history.length == 1) {
      pageClose();
    } else {
      const { fullPath } = route;
      tabStore.closeTabByKey(fullPath, router);
      router.back();
    }
  };

  // 保存
  const handleSaveAllData = async()=> {
    try {
      formRef.value.validate().then(async (values) => {
        let params = [];
        detail.value.selectedRows.forEach(item=>{
          let joined_sku_array = [];
          item.productList.map((child) => {
            if (child.state == 1) {
              joined_sku_array.push({
                productId: child.productId,
                barCode: child.barCode,
                cashPrice: child.cashPrice,
                integralPrice: child.integralPrice,
                marketPrice: child.productPrice,
                productCode: child.productCode,
                productStock: child.productStock,
                productStockWarning: child.productStockWarning,
              });
            }
          });
          if (joined_sku_array.length > 0) {
            params.push({
              goodsId: item.goodsId,
              productList: joined_sku_array,
              labelIds: sel_label_data.value.join(','),
              sellNow: values.sellNow,
            });
          }
        })
        if (params.length == 0) {
          failTip(L('请选择要参与活动的商品'));
          return false;
        }
        //商品数据-end
        loading.value = true
        let res = await getIntegralGoodsImportGoodsApi(params)
        if(res.state == 200){
          sucTip(res.msg)
          setTimeout(() => {
            loading.value = false
            goBack();
          }, 500);
        }else{
          loading.value = false
          failTip(res.msg)
        }
      })
    } catch (error) {
      console.info(error,'error')
    }
  }

  //获取系统配置(预售活动是否开启)
  const getSetting = async () => {
    try {
      let res = await getSettingListApi({ str: 'integral_conversion_ratio' });
      if (res.state == 200) {
        convert_rate.value = res.data[0].value*1
        goodsTip.value = [
          `${L('积分兑换现金比例为')}${convert_rate.value}：1，${L('即')}${convert_rate.value}${L('积分')} = 1${L('元')}。`,
          `${L('设置积分数需为平台设置的兑换比例积分')}${convert_rate.value}${L('的整数倍')}。`,
          `${L('会员兑换时，可以根据平台设置的积分与现金兑换比例自由选择使用积分数。')}`,
        ]
      }
    } catch (error) {}
  };

  onMounted(() => {
    getPointLabels()
    getSetting()
  });
</script>
<style lang="less">
@import './style/select_mall_goods.less';
</style>
