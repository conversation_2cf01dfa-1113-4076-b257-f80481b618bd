.point_add_goods {
  .tabs_nav {
    .ant-tabs-nav {
      margin-bottom: 0;
    }
  }
  .plateform_add {
    .plateform_add_step {
      position: relative;
      height: 70px;
      color: #323233;
      font-size: 14px;
      font-weight: 600;
      line-height: 70px;
      text-align: center;

      .plateform_add_step_item {
        position: relative;
        flex-shrink: 0;
        width: 49%;
        padding: 0 10px;
        background-color: #eee;
        cursor: pointer;

        &::before {
          content: ' ';
          display: block;
          position: absolute;
          top: 50%;
          left: -13px;
          width: 0;
          height: 0;
          margin-top: -35px;
          border-top: 36px solid #eee;
          border-bottom: 35px solid #eee;
          border-left: 14px solid transparent;
        }

        &::after {
          content: ' ';
          display: block;
          position: absolute;
          top: 50%;
          right: -13px;
          width: 0;
          height: 0;
          margin-top: -35px;
          border-top: 36px solid transparent;
          border-bottom: 35px solid transparent;
          border-left: 14px solid #eee;
        }

        &:nth-child(1) {
          &::before {
            display: none;
          }
        }

        &:nth-child(2) {
          &::after {
            display: none;
          }
        }

        &.active {
          background: #ff9455;

          &::before {
            border-top-color: #ff9455;
            border-bottom-color: #ff9455;
          }

          &::after {
            border-left-color: #ff9455;
          }

          .plateform_add_step_item_num {
            border-color: #fff;
            color: #fff;
          }

          .plateform_add_step_item_info {
            .plateform_add_step_item_title,
            .plateform_add_step_item_desc {
              color: #fff;
            }
          }
        }

        .plateform_add_step_item_num {
          flex-shrink: 0;
          width: 50px;
          height: 50px;
          margin-right: 10px;
          margin-left: 3px;
          border: 2px solid #666;
          border-radius: 50%;
          color: #333;
          font-size: 34px;
          padding-bottom: 2px;
        }

        .plateform_add_step_item_info {
          height: 70px;

          .plateform_add_step_item_title {
            color: #333;
            font-size: 18px;
            font-weight: 500;
            line-height: 30px;
          }

          .plateform_add_step_item_desc {
            display: inline-block;
            width: 100%;
            overflow: hidden;
            color: #999;
            font-size: 13px;
            font-weight: 500;
            line-height: 20px;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }

    .plateform_add_category {
      .plateform_add_category_list {
        width: 100%;
        height: 236px;
        margin-top: 13px;
        margin-bottom: 13px;

        .plateform_add_category_item {
          flex: 1;
          height: 100%;
          margin-left: 16px;
          padding-top: 10px;
          overflow-x: hidden;
          overflow-y: auto;
          border: 1px dotted #e6e9ee;
          background: #fff;

          &:nth-child(1) {
            margin: 0;
          }

          div {
            height: 35px;
            margin: 0 20px;
            padding: 0 20px;
            overflow: hidden;
            border: 1px solid #fff;
            color: #666;
            font-size: 13px;
            line-height: 35px;
            cursor: pointer;

            &.active {
              background: rgb(255 113 30 / 10%);
              color: #ff9455;
            }
          }
        }
      }

      .plateform_add_category_path {
        display: inline-block;
        width: 100%;
        height: 40px;
        margin-bottom: 15px;
        padding-left: 20px;
        border: 1px dotted #ffad2b;
        background: #fffadf;
        color: #ff3710;
        font-size: 12px;
        line-height: 40px;

        &.active {
          border-color: #ff9455;
          background: rgb(255 113 30 / 10%);
          color: #ff9455;
        }
      }

      .plateform_add_category_next {
        display: inline-block;
        margin-top: 16px;
        margin-bottom: 20px;
        padding: 12px 40px;
        border-radius: 2px;
        background: #ff9455;
        color: #fff;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
      }
    }

    .plateform_add_bottom {
      position: fixed;
      z-index: 999;
      right: 10px;
      bottom: 0;
      left: 0;
      height: 60px;
      background: #fff;
      box-shadow: 0 0 20px 0 rgb(187 187 187 / 15%);

      .plateform_add_bottom_next,
      .plateform_add_bottom_submit {
        width: 80px;
        border: 1px solid #fc701e;
        border-radius: 2px;
        font-size: 13px;
        line-height: 30px;
        text-align: center;
        cursor: pointer;
      }

      .plateform_add_bottom_next {
        margin-right: 20px;
        color: #fc701e;
      }

      .plateform_add_bottom_submit {
        background: #fc701e;
        color: #fff;
      }
    }

    .table_header {
      .table_header_xing {
        margin-right: 1px;
        color: #ff2929;
        font-size: 13px;
      }

      svg {
        position: relative;
        top: 2px;
        margin-left: 1px;
      }
    }

    .vben-basic-table .ant-spin-container .ant-table-container .ant-table-body {
      height: auto !important;
    }

    .plateform_add_info {
      .ant-col .ant-form-item-explain {
        position: absolute;
        z-index: 9;
        top: -12px;
        right: 8px;
      }

      .ant-form-item {
        margin-bottom: 0;
      }
    }
  }
}