.select_mall_goods {
  .ant-table-body{
    height: auto !important;
    max-height: 300px !important;
    overflow: auto !important;
  }
  .full_activity {
    .ant-form-inline {
      display: block;
    }
    .right_show_content{
      height: 32px;
      line-height: 32px;
      font-size: 13px;
    }
    .ant-form-inline .ant-form-item{
      margin-right: 0;
    }

    .ant-form-item-extra {
      font-size: 12px;
    }

    .ant-form-inline .ant-form-item-with-help {
      margin-bottom: 0;
    }

    .ant-form-item-control .ant-form-item-explain {
      position: absolute;
      z-index: 2;
      top: -10px;
      right: 4%;
      min-height: 20px;
      background: #fff;
      font-size: 13px;
      text-align: left;
    }

    .requiredNum_box {
      .ant-form-item-control .ant-form-item-explain {
        right: 20%;
      }
    }
    .cycle_box {
      .ant-form-item-control .ant-form-item-explain {
        right: 64%;
      }
    }
    .buyLimit_box {
      .ant-form-item-control .ant-form-item-explain {
        right: 53%;
      }
    }

    .business_load_img {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 50px;
      height: 50px;
      margin: auto;

      img {
        max-width: 100%;
        max-height: 100%;
      }
    }

    .extra_box {
      box-sizing: border-box;
      min-height: 24px;
      clear: both;
      transition: color 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
      color: rgb(0 0 0 / 45%);
      font-size: 12px;
      line-height: 1.5715;
    }

    .coupon_goods_operate {
      flex-shrink: 0;
      height: 60px;
      text-align: center;
      cursor: pointer;
    }

    .coupon_goods_operate svg:hover {
      fill: #77b9fe !important;
    }

    .add_coupon_height {
      max-height: calc(100vh - @header-height - 20px - 53px);
      overflow: auto;
    }

    .full_acm_activity {
      .item {
        padding-top: 15px;

        .left {
          width: 200px;
          height: 32px;
          padding-right: 20px;
          font-size: 13px;
          line-height: 32px;
          text-align: right;
        }

        .right {
          .reset_sel {
            display: inline-block;
            margin-top: 5px;
            margin-bottom: 10px;
            margin-left: 0;
            color: #ff711e;
            font-size: 13px;
            cursor: pointer;
          }

          .scroll_bars_table {
            max-height: 300px;
            overflow: auto;
          }

          .scroll_bars_tree {
            min-height: 100px;
            max-height: 300px;
            overflow-y: scroll;
          }
        }
      }
    }

    .flex_zi {
      flex: 1;
      align-items: center;
      justify-content: center;
    }

    .add_new {
      width: 100%;
      height: 40px;
      margin-top: 15px;
      border-radius: 3px;
      background: #fffaf7;

      .add_new_tip {
        margin-left: 10px;
        color: #ff7f40;
        font-size: 12px;
      }
    }

    .sel_goods {
      padding-left: 24px;

      .sel_tip {
        color: #999;
        font-size: 12px;
      }

      .reset_sel_goods {
        color: #ff7f40;
        font-size: 12px;
        cursor: pointer;
      }

      .goods_info {
        min-width: 260px;
        max-width: 700px;
        height: 60px;
        margin-top: 5px;
        margin-left: 10px;
        padding: 10px;
        border-radius: 3px;
        background: #f8f8f8;

        .left {
          flex-shrink: 0;
          width: 40px !important;
          height: 40px !important;
          margin-right: 10px;
          padding-right: 0 !important;
          overflow: hidden;
          border-radius: 3px;

          img {
            max-width: 100%;
            max-height: 100%;
            border-radius: 3px;
          }
        }

        .goods_name {
          color: #333;
          font-size: 13px;
        }

        .goods_price {
          margin-top: 3px;
          color: #666;
          font-size: 12px;
          white-space: nowrap;
        }
      }
    }
  }
}
.seckill{
  .right_show_content{
    height: 32px;
    line-height: 32px;
    font-size: 13px;
  }
  .sele_goods{
    border: 1px solid #E6E6E6;
    border-radius: 5px;
    margin: 10px 0;
    padding: 10px;
    position: relative;
    .del_spu{
      position: absolute;
      z-index: 2;
      top: 0;
      right: 0;
      width: 28px;
      height: 30px;
      cursor: pointer;
    }
    .goods_info{
      height: 70px;
      margin-bottom: 10px;
      border-bottom: 1px solid #F4F3F8;
      .goods_info_left{
        .goods_img_wrap{
          width: 60px;
          height: 60px;
          border-radius: 5px;
          overflow: hidden;
          flex-shrink: 0;
          .goods_img{
            max-width: 100%;
            max-height: 100%;
          }
        }
        .goods_name{
          color: #333;
          font-size: 12px;
          margin-left: 10px;
          padding-top: 5px;
          max-width: 320px;
        }
      }
      .goods_info_right{
        height: 70px;
        padding-bottom: 10px;
        .batch_btn{
          padding: 0 15px;
          background: #FF711E;
          border-radius: 3px;
          height: 30px;
          color: #fff;
          margin-left: 10px;
          font-size: 13px;
          cursor: pointer;
          .sel_all{
            color: #fff;
          }
        }
      }
    }
    .ladder_part{
      padding: 10px;
      background: #F8F7F9;
      margin-bottom: 10px;
      .deposit_part{
        .tip{
          display: inline-block;
          color: #999;
          font-size: 12px;
          margin-left: 20px;
        }
      }
      .ladder_item{
        margin-top: 10px;
        .left_title{
          display: inline-block;
          font-weight: bold;
          color: #333;
          margin-right: 20px;
        }
        .del{
          margin-left: 10px;
          display: flex;
        }
      }
      .add_ladder{
        margin-left: 72px;
        margin-top: 10px;
          .btn{
            width: 84px;
            height: 30px;
            background: #FF711E;
            border-radius: 2px;
            font-size: 13px;
            font-weight: bold;
            color: #FFF;
            margin-right: 20px;
          }
          .tip{
            color: #999;
            font-size: 12px;
          }
      }
    }
  }
}
