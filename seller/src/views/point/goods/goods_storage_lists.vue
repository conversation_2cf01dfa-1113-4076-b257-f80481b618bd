<template>
  <div class="goods_online_lists">
    <BasicTable @register="registerTable" style="padding: 0">
      <template #tableTitle>
        <div class="toolbar">
          <Popconfirm :title="L('确认上架选中的商品吗？')"
                    @confirm="operateGoods(null, 'up')" :disabled="selectedRowKeys.length==0">
            <div class="toolbar_btn" @click="()=>selectedRowKeys.length==0?operateGoods(null, 'up'):null">
              <AliSvgIcon iconName="iconziyuan25" width="15px" height="15px" fillColor="#0bac6a" />
              <span>{{ L('上架') }}</span>
            </div>
          </Popconfirm>
          <Popconfirm :title="L('确认删除选中的商品吗？')"
                    @confirm="operateGoods(null, 'del')" :disabled="selectedRowKeys.length==0">
            <div class="toolbar_btn" @click="()=>selectedRowKeys.length==0?operateGoods(null, 'del'):null">
              <AliSvgIcon iconName="iconpiliangshanchu" width="15px" height="15px" fillColor="#F21414" />
              <span>{{ L('删除') }}</span>
            </div>
          </Popconfirm>
        </div>
      </template>
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.key == 'action'">
          <TableAction
            :actions="[
              {
                onclick: handleClick.bind(null, record, 'look'),
                label: L('查看规格'),
              },
              {
                onclick: go_edit.bind(null, record),
                label: L('编辑'),
              },
            ]"
          />
        </template>
        <template v-else-if="column.key == 'mainImage'">
          <div class="goods_list_mainIamge">
            <Popover placement="right">
              <template #content>
                <div class="goods_list_mainIamge_pop">
                  <img :src="text" />
                </div>
              </template>
              <div
                class="goods_list_leftImage"
                :style="{ backgroundImage: `url('${text}')` }"
              ></div>
            </Popover>
            <div class="goods_list_online_rightInfo">
              <div class="goods_list_goodsname" :title="record.goodsName">{{ record.goodsName }}</div>
              <div class="goods_list_extraninfo">
                <div v-if="record.goodsBrief" :title="record.goodsBrief">{{ record.goodsBrief }}</div>
              </div>
            </div>
          </div>
        </template>
        <template v-else-if="column.key">
          {{ text !== undefined && text !== null ? text : '--' }}
        </template>
      </template>
    </BasicTable>
    <SldModal
      :width="700"
      :title="title"
      :visible="modalVisible"
      :content="content"
      :confirmBtnLoading="confirmBtnLoading"
      :showFoot="showFoot"
      :parentPage="'good_list'"
      @cancle-event="handleCancle"
      @confirm-event="handleConfirm"
    />
  </div>
</template>
<script setup>
  import { getCurrentInstance, ref, onMounted, toRaw } from 'vue';
  import { Popover,Popconfirm } from 'ant-design-vue';
  import { sucTip, failTip } from '@/utils/utils';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { getIntegralGoodsListApi, getIntegralGoodsDeleteGoodsApi,getIntegralGoodsUpperShelfApi } from '/@/api/point/goods';
  import { useRouter } from 'vue-router';
  import SldModal from '@/components/SldModal/index.vue';
  import { selectRadio, SelectAll } from '/@/utils/utils';
  import { useUserStore } from '/@/store/modules/user';

  const vm = getCurrentInstance();
  const userStore = useUserStore();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const search_form_schema = ref([
  {
      field: 'goodsName',
      component: 'Input',
      colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
      componentProps: {
        minWidth: 300,
        placeholder: L('请输入商品名称'),
        size: 'default',
      },
      label: L('商品名称'),
      labelWidth: 80,
    },
    {
      field: 'goodsCode',
      component: 'Input',
      colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
      componentProps: {
        minWidth: 300,
        placeholder: L('请输入商品货号'),
        size: 'default',
      },
      label: L('商品货号'),
      labelWidth: 80,
    },
    {
      field: 'barCode',
      component: 'Input',
      colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
      componentProps: {
        minWidth: 300,
        placeholder: L('请输入商品条形码'),
        size: 'default',
      },
      label: L('条形码'),
      labelWidth: 80,
    },
    {
      field: 'isVirtualGoods',
      component: 'Select',
      colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
      componentProps: {
        placeholder: L('请选择商品类型'),
        minWidth: 300,
        size: 'default',
        options: [
          { label: L('全部'), value: '' },
          { label: L('实物商品'), value: 1 },
          { label: L('虚拟商品'), value: 2 },
        ],
      },
      label: L('商品类型'),
      labelWidth: 80,
    },
  ]);

  const columns = ref([
    {
      title: L('商品信息'),
      dataIndex: 'mainImage',
      width: 250,
    },
    {
      title: L('商品价格'),
      dataIndex: 'cashPrice',
      width: 100,
      customRender: ({ text, record }) => {
        return `${record.integralPrice}${L('积分')}${text ? `${' + ¥'}` + text : ''}`;
      },
    },
    {
      title: L('商品库存'),
      dataIndex: 'goodsStock',
      width: 100,
    },
    {
      title: L('销量'),
      dataIndex: 'actualSales',
      width: 100,
    },
    {
      title: L('发布时间'),
      dataIndex: 'createTime',
      width: 120,
    },
  ]);

  const content_columns = ref([
    {
      title: ' ',
      align: 'center',
      width: 100,
      minWidth: 100,
      customRender: ({ text, record, index }) => {
        return `${index + 1}`;
      },
    },
    {
      title: L('商品规格'),
      dataIndex: 'specValues',
      align: 'center',
      width: 200,
      customRender: ({ text }) => {
        return text ? text : L('默认');
      },
      minWidth: 200,
    },
    {
      title: L('价格'),
      dataIndex: 'cashPrice',
      align: 'center',
      width: 200,
      minWidth: 200,
      customRender: ({ text,record }) => {
        return `${record.integralPrice}${L('积分')}${text ? (' + ¥' + text) : ''}`;
      },
    },
    {
      title: L('库存'),
      dataIndex: 'productStock',
      align: 'center',
      width: 200,
      minWidth: 200,
    },
    {
      title: L('货号'),
      dataIndex: 'productCode',
      align: 'center',
      width: 200,
      minWidth: 200,
      customRender: ({ text }) => {
        return text ? text : '--';
      },
    },
    {
      title: L('条形码'),
      dataIndex: 'barCode',
      align: 'center',
      width: 200,
      minWidth: 200,
      customRender: ({ text }) => {
        return text ? text : '--';
      },
    },
  ])
  
  const router = useRouter();
  const selectedRowKeys = ref([]);
  const selectedRows = ref([]);
  const modalVisible = ref(false);
  const confirmBtnLoading = ref(false);
  const title = ref(L('违规下架商品'));
  const showFoot = ref(true);
  const content = ref([]);
  const operate_type = ref('');
  const operate_item = ref({});

  const [registerTable, { reload }] = useTable({
    rowKey: 'integralGoodsId',
    api: (arg) => getIntegralGoodsListApi({ ...arg, state: 4 }),
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    columns: columns,
    actionColumn: {
      width: 120,
      title: L('操作'),
      dataIndex: 'action',
    },
    useSearchForm: true,
    formConfig: {
      schemas: search_form_schema,
    },
    rowSelection: {
      type: 'checkbox',
      selectedRowKeys: selectedRowKeys,
      onSelect: onSelect,
      onSelectAll: onSelectAll,
    },
    bordered: true,
    striped: false,
    clickToRowSelect: false,
  });

  //表格点击回调事件
  const handleClick = (item, type) => {
    operate_type.value = type;
    operate_item.value = item ? item : null;
    let obj = [];
    if(type == 'look'){
      title.value = L('查看规格');
      showFoot.value = false
      obj = [
        {
        type: 'show_content_table', //展示表格，没有分页，不能勾选
        name: 'bind_goods',
        width: 880,
        wrapperCol: { flex: 1, style: 'margin-bottom:-19px' },
        label: ``,
        content: '',
        data: item.productList,
        scrollHeight: false, // 设置纵向滚动，也可用于指定滚动区域的高，可以设置为像素值
        columns: content_columns.value,
        rowKey: 'goodsId',
        scroll: false,
        }
      ]
    }
    content.value = obj;
    modalVisible.value = true;
  };

  // 操作：del批量删除
  const operateGoods = async(record,type) => {
    let res;
    if(type=='del'){
      if(selectedRowKeys.value.length==0){
        failTip(L('请先选中数据'))
        return
      }
      res = await getIntegralGoodsDeleteGoodsApi({integralGoodsIds:selectedRowKeys.value.join(',')})
    }else if(type =='up'){
      if(selectedRowKeys.value.length==0){
        failTip(L('请先选中数据'))
        return
      }
      res = await getIntegralGoodsUpperShelfApi({ integralGoodsIds: selectedRowKeys.value.join(',') })
    }
    if(res.state == 200){
      sucTip(res.msg)
      selectedRowKeys.value = []
      selectedRows.value = []
      reload()
    }else{
      failTip(res.msg)
    }
  }
  
  // 单选多选事件
  function onSelect(record, selected) {
    let rows = selectRadio(
      selectedRowKeys.value,
      selectedRows.value,
      'integralGoodsId',
      record,
      selected,
    );
    selectedRowKeys.value = rows.selectedRowKeys;
    selectedRows.value = rows.selectedRows;
  }

  // 全选按钮事件
  function onSelectAll(selected, rows, changeRows) {
    let rowAll = SelectAll(
      selectedRowKeys.value,
      selectedRows.value,
      'integralGoodsId',
      selected,
      rows,
      changeRows,
    );
    selectedRowKeys.value = rowAll.selectedRowKeys;
    selectedRows.value = rowAll.selectedRows;
  }

  //弹窗取消事件
  const handleCancle = () => {
    modalVisible.value = false;
    content.value = [];
    operate_item.value = {};
  };

  //弹窗确认事件
  const handleConfirm = async (val) => {
    modalVisible.value = false;
    confirmBtnLoading.value = false;
  };

  const go_edit = (item)=> {
    userStore.setDelKeepAlive(['PointGoodsListToEdit'])
    router.push('/point/goods_list_to_edit?id='+item.integralGoodsId)
  }


  onMounted(() => {
  });
</script>
<style lang="less">
  .goods_online_lists {
    .goods_list_online_rightInfo {
      display: flex;
      flex: 1;
      flex-direction: column;
      align-items: flex-start;
      justify-content: space-between;
      margin-left: 10px;
      padding: 1px 0;

      .goods_list_goodsname {
        display: -webkit-box;
        flex-shrink: 0;
        height: 34px;
        overflow: hidden;
        color: #333;
        font-size: 14px;
        line-height: 17px;
        text-align: left;
        text-overflow: ellipsis;
        word-break: break-word;
        white-space: normal;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .goods_list_extraninfo {
        color: #666;
        font-size: 12px;
        text-align: left;

        div {
          display: -webkit-box;
          flex-shrink: 0;
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: break-word;
          white-space: normal;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }
      }
    }
  }
</style>
