<template>
  <div class="goods_online_lists">
    <BasicTable @register="registerTable" style="padding: 0">
      <template #tableTitle>
        <div class="toolbar">
          <div class="toolbar_btn" @click="go_add">
            <AliSvgIcon iconName="iconfabu1" width="15px" height="15px" fillColor="#08A9B7" />
            <span>{{ L('发布商品') }}</span>
          </div>
          <div class="toolbar_btn" @click="go_import">
            <AliSvgIcon iconName="icondaoru" width="17px" height="17px" fillColor="#fa6f1e" />
            <span>{{ L('导入商城商品') }}</span>
          </div>
          <Popconfirm :title="L('确认下架选中的商品吗？')"
                    @confirm="handleClick(null, 'lockOff')" :disabled="selectedRowKeys.length==0">
            <div class="toolbar_btn" @click="()=> selectedRowKeys.length==0 ? handleClick(null, 'lockOff') : null">
              <AliSvgIcon iconName="iconziyuan31" width="15px" height="15px" fillColor="#f9a006" />
              <span>{{ L('下架') }}</span>
            </div>
          </Popconfirm>
          <Popconfirm :title="L('确认删除选中的商品吗？')" @confirm="operateGoods(null, 'del')" :disabled=" selectedRowKeys.length==0 ">
            <div class="toolbar_btn" @click="()=> selectedRowKeys.length==0 ? operateGoods(null, 'del') : null">
              <AliSvgIcon iconName="iconpiliangshanchu" width="15px" height="15px" fillColor="#F21414" />
              <span>{{ L('删除') }}</span>
            </div>
          </Popconfirm>
        </div>
      </template>
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.key == 'action'">
          <TableAction
            :actions="[
              {
                onclick: handleClick.bind(null, record, 'look'),
                label: L('查看规格'),
              },
              {
                onclick: go_edit.bind(null, record),
                label: L('编辑'),
              },
            ]"
          />
        </template>
        <template v-else-if="column.key == 'mainImage'">
          <div class="goods_list_mainIamge">
            <Popover placement="right">
              <template #content>
                <div class="goods_list_mainIamge_pop">
                  <img :src="text" />
                </div>
              </template>
              <div
                class="goods_list_leftImage"
                :style="{ backgroundImage: `url('${text}')` }"
              ></div>
            </Popover>
            <div class="goods_list_online_rightInfo">
              <div class="goods_list_goodsname" :title="record.goodsName">{{ record.goodsName }}</div>
              <div class="goods_list_extraninfo">
                <div v-if="record.goodsBrief" :title="record.goodsBrief">{{ record.goodsBrief }}</div>
              </div>
            </div>
          </div>
        </template>
        <template v-else-if="column.key">
          {{ text !== undefined && text !== null ? text : '--' }}
        </template>
      </template>
    </BasicTable>
    <SldModal
      :width="width"
      :title="title"
      :visible="modalVisible"
      :content="content"
      :confirmBtnLoading="confirmBtnLoading"
      :showFoot="showFoot"
      :parentPage="'good_list'"
      @cancle-event="handleCancle"
      @confirm-event="handleConfirm"
    />
  </div>
</template>
<script setup>
  import { getCurrentInstance, ref, onMounted, computed, watch } from 'vue';
  import { Popover,Popconfirm } from 'ant-design-vue';
  import { sucTip, failTip } from '@/utils/utils';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { getIntegralGoodsListApi, getIntegralGoodsLockUpApi,getIntegralGoodsDeleteGoodsApi } from '/@/api/point/goods';
  import { useRouter } from 'vue-router';
  import { getSettingReasonListApi } from '/@/api/common/common';
  import { selectRadio, SelectAll } from '/@/utils/utils';
  import SldModal from '@/components/SldModal/index.vue';
  import { useUserStore } from '/@/store/modules/user';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const props = defineProps({
    canResize: { type: Boolean, default: true },
  });

  const canResize = computed(() => {
    return props.canResize;
  });

  watch(canResize, () => {
    redoHeight();
  });

  const userStore = useUserStore();

  const content_columns = ref([
    {
      title: ' ',
      align: 'center',
      width: 100,
      minWidth: 100,
      customRender: ({ text, record, index }) => {
        return `${index + 1}`;
      },
    },
    {
      title: L('商品规格'),
      dataIndex: 'specValues',
      align: 'center',
      width: 200,
      customRender: ({ text }) => {
        return text ? text : L('默认');
      },
      minWidth: 200,
    },
    {
      title: L('价格'),
      dataIndex: 'cashPrice',
      align: 'center',
      width: 200,
      minWidth: 200,
      customRender: ({ text,record }) => {
        return `${record.integralPrice}${L('积分')}${text ? (' + ¥' + text) : ''}`;
      },
    },
    {
      title: L('库存'),
      dataIndex: 'productStock',
      align: 'center',
      width: 200,
      minWidth: 200,
    },
    {
      title: L('货号'),
      dataIndex: 'productCode',
      align: 'center',
      width: 200,
      minWidth: 200,
      customRender: ({ text }) => {
        return text ? text : '--';
      },
    },
    {
      title: L('条形码'),
      dataIndex: 'barCode',
      align: 'center',
      width: 200,
      minWidth: 200,
      customRender: ({ text }) => {
        return text ? text : '--';
      },
    },
  ])

  const search_form_schema = ref([
  {
      field: 'goodsName',
      component: 'Input',
      colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
      componentProps: {
        minWidth: 300,
        placeholder: L('请输入商品名称'),
        size: 'default',
      },
      label: L('商品名称'),
      labelWidth: 80,
    },
    {
      field: 'goodsCode',
      component: 'Input',
      colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
      componentProps: {
        minWidth: 300,
        placeholder: L('请输入商品货号'),
        size: 'default',
      },
      label: L('商品货号'),
      labelWidth: 80,
    },
    {
      field: 'barCode',
      component: 'Input',
      colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
      componentProps: {
        minWidth: 300,
        placeholder: L('请输入商品条形码'),
        size: 'default',
      },
      label: L('条形码'),
      labelWidth: 80,
    },
    {
      field: 'isVirtualGoods',
      component: 'Select',
      colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
      componentProps: {
        placeholder: L('请选择商品类型'),
        minWidth: 300,
        size: 'default',
        options: [
          { label: L('全部'), value: '' },
          { label: L('实物商品'), value: 1 },
          { label: L('虚拟商品'), value: 2 },
        ],
      },
      label: L('商品类型'),
      labelWidth: 80,
    },
  ]);

  const columns = ref([
    {
      title: L('商品信息'),
      dataIndex: 'mainImage',
      width: 250,
    },
    {
      title: L('商品价格'),
      dataIndex: 'cashPrice',
      width: 100,
      customRender: ({ text, record }) => {
        return `${record.integralPrice}${L('积分')}${text ? `${' + ¥'}` + text : ''}`;
      },
    },
    {
      title: L('商品库存'),
      dataIndex: 'goodsStock',
      width: 100,
    },
    {
      title: L('销量'),
      dataIndex: 'actualSales',
      width: 100,
    },
    {
      title: L('发布时间'),
      dataIndex: 'createTime',
      width: 120,
    },
  ]);
  const router = useRouter();
  const selectedRowKeys = ref([]);
  const selectedRows = ref([]);
  const modalVisible = ref(false);
  const confirmBtnLoading = ref(false);
  const title = ref(L('违规下架商品'));
  const content = ref([]);
  const operate_type = ref('');
  const operate_item = ref({});
  const reason_list = ref([]);
  const operate_lockoff_data = ref([
    {
      type: 'select',
      label: L('下架理由'),
      name: 'offlineReason',
      placeholder: L('请选择下架理由'),
      width: 353,
      selData: [],
      rules: [
        {
          required: true,
          message: L('请选择下架理由'),
        },
      ],
    },
    {
      type: 'textarea',
      label: L('备注'),
      name: 'offlineComment',
      placeholder: L('请输入备注'),
      extra: L('最多输入100个字'),
      maxLength: 100,
    },
  ]);
  const width = ref(500)
  const showFoot = ref(true)

  const [registerTable, { reload, redoHeight }] = useTable({
    rowKey: 'integralGoodsId',
    api: (arg) => getIntegralGoodsListApi({ ...arg, state: 3 }),
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    columns: columns,
    actionColumn: {
      width: 120,
      title: L('操作'),
      dataIndex: 'action',
    },
    useSearchForm: true,
    formConfig: {
      schemas: search_form_schema,
    },
    bordered: true,
    striped: false,
    rowSelection: {
      type: 'checkbox',
      selectedRowKeys: selectedRowKeys,
      onSelect: onSelect,
      onSelectAll: onSelectAll,
    },
    clickToRowSelect: false,
  });

  // 操作：del批量删除
  const operateGoods = async(record,type) => {
    let res;
    if(type=='del'){
      if(selectedRowKeys.value.length==0){
        failTip(L('请先选中数据'))
        return
      }
      res = await getIntegralGoodsDeleteGoodsApi({integralGoodsIds:selectedRowKeys.value.join(',')})
    }
    if(res.state == 200){
      sucTip(res.msg)
      selectedRowKeys.value = []
      selectedRows.value = []
      reload()
    }else{
      failTip(res.msg)
    }
  }

  // 导入商城商品
  const go_import = ()=> {
    userStore.setDelKeepAlive(['goodsListToImport'])
    setTimeout(()=>{
      router.push({
        path: `/point/goods_list_to_import`,
      });
    },100)
  }

  //表格点击回调事件
  const handleClick = async(item, type) => {
    operate_type.value = type;
    operate_item.value = item ? item : null;
    let data = [];
    if (type == 'lockOff') {
      if (item == null && selectedRowKeys.value.length == 0) {
        failTip(L('请先选中数据'));
        return;
      }
      let val = {}
      val.integralGoodsIds =
        operate_item.value != null
          ? operate_item.value.integralGoodsId
          : selectedRowKeys.value.join(',');
      let res = await getIntegralGoodsLockUpApi(val);
      if (res.state == 200) {
        sucTip(res.msg);
        modalVisible.value = false;
        confirmBtnLoading.value = false;
        selectedRows.value = [];
        selectedRowKeys.value = [];
        reload();
      } else {
        failTip(res.msg);
        confirmBtnLoading.value = false;
      }
      return
    }else if(type == 'look'){
      title.value = L('查看规格');
      width.value = 700
      showFoot.value = false
      let obj = [
        {
        type: 'show_content_table', //展示表格，没有分页，不能勾选
        name: 'bind_goods',
        width: 880,
        wrapperCol: { flex: 1, style: 'margin-bottom:-19px' },
        label: ``,
        content: '',
        data: item.productList,
        scrollHeight: false, // 设置纵向滚动，也可用于指定滚动区域的高，可以设置为像素值
        columns: content_columns.value,
        rowKey: 'goodsId',
        scroll: false,
        }
      ]
      data = obj
    }
    content.value = data;
    modalVisible.value = true;
  };

  //弹窗取消事件
  const handleCancle = () => {
    modalVisible.value = false;
    content.value = [];
    operate_item.value = {};
  };

  //获取违规下架理由
  const get_reason_list = async () => {
    let res = await getSettingReasonListApi({ type: 101, isShow: 1 });
    if (res.state == 200) {
      let selData = [];
      res.data.map((item) => {
        selData.push({
          key: item.reasonId,
          name: item.content,
        });
      });
      reason_list.value = res.data;
      operate_lockoff_data.value.forEach((item) => {
        if (item.name == 'offlineReason') {
          item.selData = selData;
        }
      });
    }
  };

  // 单选多选事件
  function onSelect(record, selected) {
    let rows = selectRadio(
      selectedRowKeys.value,
      selectedRows.value,
      'integralGoodsId',
      record,
      selected,
    );
    selectedRowKeys.value = rows.selectedRowKeys;
    selectedRows.value = rows.selectedRows;
  }

  // 全选按钮事件
  function onSelectAll(selected, rows, changeRows) {
    let rowAll = SelectAll(
      selectedRowKeys.value,
      selectedRows.value,
      'integralGoodsId',
      selected,
      rows,
      changeRows,
    );
    selectedRowKeys.value = rowAll.selectedRowKeys;
    selectedRows.value = rowAll.selectedRows;
  }

  //弹窗确认事件
  const handleConfirm = async (val) => {
    if (operate_type.value == 'lockOff') {
      val.integralGoodsIds =
        operate_item.value != null
          ? operate_item.value.integralGoodsId
          : selectedRowKeys.value.join(',');
    }
    confirmBtnLoading.value = true;
    let res = await getIntegralGoodsLockUpApi(val);
    if (res.state == 200) {
      sucTip(res.msg);
      modalVisible.value = false;
      confirmBtnLoading.value = false;
      selectedRows.value = [];
      selectedRowKeys.value = [];
      reload();
    } else {
      failTip(res.msg);
      confirmBtnLoading.value = false;
    }
  };

  const go_add = ()=> {
    userStore.setDelKeepAlive(['PointGoodsListToAdd'])
    router.push('/point/goods_list_to_add?source=/point/goods_list')
  }

  const go_edit = (item)=> {
    userStore.setDelKeepAlive(['PointGoodsListToEdit'])
    router.push('/point/goods_list_to_edit?id='+item.integralGoodsId)
  }

  onMounted(() => {
    get_reason_list();
  });
</script>
<style lang="less">
  .goods_online_lists {
    .goods_list_online_rightInfo {
      display: flex;
      flex: 1;
      flex-direction: column;
      align-items: flex-start;
      justify-content: space-between;
      margin-left: 10px;
      padding: 1px 0;

      .goods_list_goodsname {
        display: -webkit-box;
        flex-shrink: 0;
        height: 34px;
        overflow: hidden;
        color: #333;
        font-size: 14px;
        line-height: 17px;
        text-align: left;
        text-overflow: ellipsis;
        word-break: break-word;
        white-space: normal;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .goods_list_extraninfo {
        color: #666;
        font-size: 12px;
        text-align: left;

        div {
          display: -webkit-box;
          flex-shrink: 0;
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: break-word;
          white-space: normal;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }
      }
    }
  }
</style>
