<template>
  <Spin :spinning="spinning">
    <div class="section_padding point_add_goods">
      <div class="section_padding_back">
        <SldComHeader :title="(id !== null && id) ? L('编辑商品') : L('发布商品')"/>
        <div class="plateform_add">
          <div class="flex_row_between_center plateform_add_step">
            <div
              class="flex_row_start_center plateform_add_step_item"
              :class="step >= 0 ? 'active' : ''"
              @click="nextStep(0)"
            >
              <div class="flex_row_center_center plateform_add_step_item_num">1</div>
              <div class="flex_column_center_start plateform_add_step_item_info">
                <div class="plateform_add_step_item_title">基本信息</div>
                <div class="plateform_add_step_item_desc">填写商品基本信息,发票信息以及其他信息</div>
              </div>
            </div>
            <div
              class="flex_row_start_center plateform_add_step_item"
              :class="step >= 1 ? 'active' : ''"
              @click="nextStep(1)"
            >
              <div class="flex_row_center_center plateform_add_step_item_num">2</div>
              <div class="flex_column_center_start plateform_add_step_item_info">
                <div class="plateform_add_step_item_title">商品详情</div>
                <div class="plateform_add_step_item_desc">设置规格信息,上传商品图片并完善商品详情</div>
              </div>
            </div>
          </div>
          <!-- 基本信息 -->
          <div :style="{ display: step == 0 ? 'block' : 'none', height: scrollHeight }" class="plateform_add_base">
            <ScrollContainer ref="scrollBase">
              <!-- 基本信息 -->
              <StandardFormRow
                :width="'100%'"
                :left_width="160"
                :data="baseData"
                :valida="baseValida"
                @valida-event="handleValida"
                @tree-select-change-event="handleTreeSelect"
                @callback-event="(e) => handleChange(e, 'baseData')"
              />
              <!-- 发票信息 -->
              <StandardFormRow
                :width="'100%'"
                :left_width="160"
                :data="invoiceData"
                @callback-event="(e) => handleChange(e, 'invoiceData')"
              />
              <!-- 其他信息 -->
              <StandardFormRow
                :width="'100%'"
                :left_width="160"
                :data="extraData"
                :valida="extraValida"
                @valida-event="handleExtraValida"
                @callback-event="(e) => handleChange(e, 'extraData')"
                @reserve-Info-Event="handleReserveInfo"
              />
            </ScrollContainer>
          </div>
          <!-- 商品详情 -->
          <div :style="{ display: step == 1 ? 'block' : 'none', height: scrollHeight }" class="plateform_add_info">
            <ScrollContainer ref="scrollGood">
              <!-- 商品规格 -->
              <StandardFormRow
                :width="'100%'"
                :left_width="160"
                :data="specData"
                @callback-event="(e) => handleChange(e, 'specData')"
                @goods-spec-event="handleSpec"
              />
              <ShowMoreHelpTip :tipData="goodsTip"></ShowMoreHelpTip>
              <div style="width: 100%;height: 10px;margin-bottom: 0.5rem;"></div>
              <Form id="goodsTable" ref="formRef" :model="dataSourceForm">
                <BasicTable @register="standardTable">
                  <template #headerCell="{ column }">
                    <template v-if="column.key == 'productPrice'">
                      <div class="table_header">
                        <span class="table_header_xing">*</span>
                        <span>{{ column.customTitle }}</span>
                      </div>
                    </template>
                    <template v-else-if="column.key == 'integralPrice'">
                      <div class="table_header">
                        <span class="table_header_xing">*</span>
                        <span>{{ column.customTitle }}</span>
                      </div>
                    </template>
                    <template v-else-if="column.key == 'marketPrice'">
                      <div class="table_header">
                        <span class="table_header_xing">*</span>
                        <span>{{ column.customTitle }}</span>
                      </div>
                    </template>
                    <template v-else-if="column.key == 'productStock'">
                      <div class="table_header">
                        <span class="table_header_xing">*</span>
                        <span>{{ column.customTitle }}</span>
                      </div>
                    </template>
                    <template v-else-if="column.key == 'barCode'">
                      <div class="table_header">
                        <span>{{ column.customTitle }}</span>
                        <Tooltip placement="bottom" title="虚拟商品无需填写">
                          <AliSvgIcon iconName="iconwen" width="14px" height="14px" fillColor="#bfbbba" />
                        </Tooltip>
                      </div>
                    </template>
                    <template v-else>
                      {{ column.customTitle }}
                    </template>
                  </template>
                  <template #bodyCell="{ column, record, text, index }">
                    <template v-if="column.key == 'state'">
                      <Switch
                        :disabled="record.isDefault ? true : false"
                        :checked="text == 1 ? true : false"
                        checked-children="开启"
                        un-checked-children="关闭"
                        @change="(e) => handleClick(e ? 1 : 0, index, column.key)"
                      />
                    </template>
                    <template v-else-if="column.key == 'isDefault'">
                      <Checkbox
                        :checked="text == 1 ? true : false"
                        @change="(e) => handleClick(e.target.checked ? 1 : 0, index, column.key)"
                      />
                    </template>
                    <template v-else-if="column.key == 'marketPrice'">
                      <FormItem
                        :validateFirst="true"
                        :name="['list', index, 'marketPrice']"
                        :rules="[{ required: true, message: '该项必填' },{validator: async (rule, value) => await validatorMarketPrice(rule, value,record.productPrice)}]"
                      >
                          <InputNumber
                            :min="0.01"
                            :max="999999"
                            :precision="2"
                            placeholder=""
                            :value="text ? text : undefined"
                            @change="(e) => handleClick(e, index, column.key)"
                          />
                      </FormItem>
                    </template>
                    <template v-else-if="column.key == 'integralPrice'">
                      <FormItem
                        :validateFirst="true"
                        :name="['list', index, 'integralPrice']"
                        :rules="record.productPrice ? [{validator: async (rule, value) => await validatorIntegral(rule, value)}] :[{ required: true, message: '该项必填' },{validator: async (rule, value) => await validatorIntegral(rule, value)}]"
                      >
                          <InputNumber
                            :min="record.productPrice ? 0 : 1"
                            :max="99999999"
                            :precision="0"
                            placeholder=""
                            :value="text ? text : undefined"
                            @change="(e) => handleClick(e, index, column.key)"
                          />
                      </FormItem>
                    </template>
                    <template v-else-if="column.key == 'productPrice'">
                      <FormItem
                        :name="['list', index, 'productPrice']"
                        :rules="record.integralPrice ? [] : [{ required: true, message: '该项必填' }]"
                      >
                        <InputNumber
                          :min="record.integralPrice ? 0 : 0.01"
                          :max="999999"
                          :precision="2"
                          placeholder=""
                          :value="text"
                          @change="(e) => handleClick(e, index, column.key)"
                        />
                      </FormItem>
                    </template>
                    <template v-else-if="column.key == 'productStock'">
                      <FormItem
                        :name="['list', index, 'productStock']"
                        :rules="[{ required: true, message: '该项必填' }]"
                      >
                        <InputNumber
                          :min="0"
                          :max="99999999"
                          :precision="0"
                          placeholder=""
                          :value="text !== undefined ? text : undefined"
                          @change="(e) => handleClick(e, index, column.key)"
                        />
                      </FormItem>
                    </template>
                    <template
                      v-else-if="
                        column.key == 'weight' ||
                        column.key == 'length' ||
                        column.key == 'width' ||
                        column.key == 'height'
                      "
                    >
                      <InputNumber
                        :min="0.001"
                        :max="999999"
                        :precision="3"
                        placeholder=""
                        :value="text"
                        @change="(e) => handleClick(e, index, column.key)"
                      />
                    </template>
                    <template v-else-if="column.key == 'productStockWarning'">
                      <InputNumber
                        :min="0"
                        :max="300"
                        :precision="0"
                        placeholder=""
                        :value="text"
                        @change="(e) => handleClick(e, index, column.key)"
                      />
                    </template>
                    <template v-else-if="column.key == 'productCode' || column.key == 'barCode'">
                      <FormItem
                      :name="['list', index, `${column.key}`]"
                      :rules="[{validator: async (rule, value) => {await validatorEmoji(rule, value);}}]"
                      >
                        <Input
                          :value="text !== undefined ? text : undefined"
                          placeholder=""
                          @change="(e) => handleClick(e.target.value, index, column.key)"
                        />
                      </FormItem>
                    </template>
                    <template v-else-if="column.key">
                      {{ text ? text : '--' }}
                    </template>
                  </template>
                </BasicTable>
              </Form>
              <!-- 商品图片 - spu  -->
              <StandardFormRow
                id="goodsImg"
                v-if="!skuImgFlag"
                :width="'100%'"
                :left_width="160"
                :data="goodsImgData"
                @callback-event="(e) => handleChange(e, 'goodsImgData')"
                @material-event="handleMaterial"
                @change-upload="handleChangeUpload"
              />
              <!-- 商品图片 - sku -->
              <StandardFormRow
                id="goodsImg"
                v-else
                :width="'100%'"
                :left_width="160"
                :data="goodsSkuImgData"
                @callback-event="(e) => handleChange(e, 'goodsSkuImgData')"
                @material-event="handleMaterial"
                @change-upload="handleChangeUpload"
              />
              <!-- 商品视频 -->
              <StandardFormRow
                :width="'100%'"
                :left_width="160"
                :data="videoData"
                @callback-event="(e) => handleChange(e, 'videoData')"
                @material-event="handleMaterial"
                @material-delete="deleteMaterial"
                @change-upload="handleChangeUpload"
              />
              <!-- 商品详情描述 -->
              <StandardFormRow
                :width="'100%'"
                :left_width="160"
                :data="descData"
                @callback-event="(e) => handleChange(e, 'descData')"
              />
              <div style="margin: -6px 8px 0">
                <SldUEditor
                  v-if="editorFlag"
                  :id="'goodsInfo'"
                  :initEditorContent="initEditorContent"
                  :getContentFlag="getEditorContentFlag"
                  :getEditorContent="getEditorContent"
                />
              </div>
  
              <!-- 图片素材选择 start  -->
              <SldMaterialImgs
                :visibleModal="chooseFile == 1 ? true : false"
                :maxUploadNum="operate_type == 'goods' ? img_num : 1"
                :allowRepeat="true"
                :selectedData="selectImageData"
                @closeMaterial="() => closeMaterial()"
                @confirmMaterial="(val) => confirmMaterial('image', val)"
              />
              <!-- 图片素材选择 end  -->
  
              <!-- 视频素材选择 start  -->
              <SldMaterialVideos
                :visibleModal="chooseFile == 2 ? true : false"
                :maxUploadNum="1"
                :selectedData="selectVideoData"
                @closeMaterial="() => closeMaterial()"
                @confirmMaterial="(val) => confirmMaterial('video', val)"
              />
              <!-- 视频素材选择 end  -->
            </ScrollContainer>
          </div>
          <div
            v-if="step == 0 || step == 1"
            class="flex_row_center_center plateform_add_bottom"
            :style="{ left: getRealWidth + 'px' }"
          >
            <div class="plateform_add_bottom_next" @click="goBack">返回</div>
            <div v-if="step == 0" class="plateform_add_bottom_next" @click="nextStep(1)">下一步</div>
            <div v-else class="plateform_add_bottom_next" @click="nextStep(0)">上一步</div>
            <div class="plateform_add_bottom_submit" @click="saveAllData">发布</div>
          </div>
        </div>
      </div>
    </div>
  </Spin>
  </template>
  <script lang="ts" setup>
    import SldMaterialImgs from '@/components/SldMaterialFiles/sldMaterialImgs.vue';
import SldMaterialVideos from '@/components/SldMaterialFiles/sldMaterialVideos.vue';
import { Checkbox, Form, FormItem, Input, InputNumber, Spin, Switch, Tooltip } from 'ant-design-vue';
import { getCurrentInstance, onMounted, onUnmounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getSettingListApi } from '/@/api/common/common';
import {
getIntegralGoodsAddApi,
getIntegralGoodsDetailApi,
getIntegralGoodsEditApi,
getIntegralGoodsLabelListApi,
getIntegralGoodsSpecAddSpecAddApi,
getIntegralGoodsSpecAddSpecValueApi,
getIntegralGoodsSpecListApi
} from '/@/api/point/goods';
import { ScrollContainer } from '/@/components/Container/index';
import ShowMoreHelpTip from '/@/components/ShowMoreHelpTip/index.vue';
import SldComHeader from '/@/components/SldComHeader/index.vue';
import SldUEditor from '/@/components/SldUEditor/index.vue';
import StandardFormRow from '/@/components/StandardFormRow/index.vue';
import { BasicTable, useTable } from '/@/components/Table';
import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
import { useMultipleTabStore } from '/@/store/modules/multipleTab';
import { useUserStore } from '/@/store/modules/user';
import { calcDescartes, determineEmoji, failTip, pageClose, quillEscapeToHtml, sucTip } from '/@/utils/utils';
import { validatorEmoji } from '/@/utils/validate';
  
    const vm = getCurrentInstance();
    const L = vm?.appContext.config.globalProperties.$sldComLanguage;
    const route = useRoute();
    const router = useRouter();
    const id: any = ref(route.query.id ? route.query.id : null);
    const tabStore = useMultipleTabStore();
    const { getRealWidth } = useMenuSetting();
    const convert_rate = ref(0)//兑换比例
  
    const spinning = ref(true);
    const userStore = useUserStore();
    const step = ref(0); //发布步骤
    const scrollBase = ref(null); //滚动组件
    const scrollGood = ref(null); //滚动组件
    const scrollHeight = ref(document.body.clientHeight - 48 - 40 - 32 - 60 - 70 - 20 + 'px'); //滚动区域高度
    const click_event = ref(false); //事件防抖动
    const editorFlag = ref(false);
    const initEditorContent = ref(''); //百度编辑器内容
    const getEditorContentFlag = ref(false); //获取百度编辑器内容标识

    const img_num = ref(6)
  
    const isVirtualGoods = ref(2); //商品类型 1-实物 2-虚拟
    const reserve_info_key = ref(0);
    const baseValida = ref(false);
    const validaFlag = ref(true);
    const baseData = ref([
      //基本信息
      {
        type: 'title',
        label: '基本信息',
      },
      {
        type: 'tree_select',
        require: true,
        label: '积分商品标签',
        placeholder: '请选择积分商品标签',
        key: 'labelIds',
        value: undefined,
        data: [],
        list: [],
        treeCheckable: true,
        treeDefaultExpandAll: true,
        showSearch: true,
        treeNodeFilterProp: 'title',
        allowClear: true,
        rules: [
          {
            required: true,
            message: '请选择积分商品标签',
          },
        ],
        right_width: '80%',
        callback: true,
      },
      {
        type: 'radio',
        label: '商品类型',
        key: 'isVirtualGoods',
        desc: '编辑商品的时候无法更改商品类型，请谨慎选择',
        options: [
          { label: '虚拟商品(无需物流)', value: 2 },
          // { label: '实物商品(需要物流)', value: 1 },
        ],
        value: 2,
        item_width: '100%',
        callback: true,
      },
      {
        type: 'input',
        require: true,
        label: '积分商品名称',
        key: 'goodsName',
        placeholder: '请输入积分商品名称',
        maxlength: 50,
        desc: '最多输入50个字',
        value: undefined,
        right_width: '80%',
        rules: [
          {
            required: true,
            whitespace: true,
            message: '请输入商品名称',
          },
        ],
        callback: true,
      },
      {
        type: 'input',
        require: true,
        label: '积分商品广告语',
        key: 'goodsBrief',
        placeholder: '请输入积分商品广告语',
        maxlength: 50,
        desc: '最多输入50个字',
        value: undefined,
        right_width: '80%',
        rules: [
          {
            required: true,
            whitespace: true,
            message: '请输入积分商品广告语',
          },
        ],
        callback: true,
      },
    ]);
    const invoiceData = ref([
      //发票信息
      {
        type: 'title',
        label: '发票信息',
      },
      {
        type: 'radio',
        require: true,
        label: '是否开专票',
        key: 'isVatInvoice',
        desc: '',
        options: [
          { label: '否', value: 0 },
          { label: '是', value: 1 },
        ],
        value: 0,
        item_width: '100%',
        callback: true,
      },
    ]);
    const extraValida = ref(false);
    const extraData = ref([
      {
        type: 'title',
        label: '其他信息',
      },
      {
        type: 'radio',
        label: '发布状态',
        key: 'sellNow',
        options: [
          { label: '立即售卖', value: true },
          { label: '暂不售卖，放入仓库中', value: false },
        ],
        value: true,
        item_width: '100%',
        callback: true,
      },
    ]);
    const extraVirtualData = ref([
      {
        type: 'add_reserve_info',
        label: '用户预留信息',
        key: 'reserveInfoList',
        item_width: '100%',
        select_list: [
          { label: '手机号', value: 1 },
          { label: '身份证号', value: 2 },
          { label: '数字格式', value: 3 },
          { label: '文本格式', value: 4 },
          { label: '邮箱格式', value: 5 }
        ],
        data: [],
        reserveInfoBack: true,
      }
    ]);
    const specData = ref([
      {
        type: 'title',
        label: '价格库存',
      },
      {
        type: 'goods_spec',
        key: 'specValue',
        label: '规格选择',
        specList: [], //规格列表数据
        selectSpecList: [], //已选规格列表
        spec_diy: true,
        spec_diy_value: 'specId',
        spec_diy_key: 'specName',
        spec_val_diy: true,
        spec_val_diy_value: 'specValueId',
        spec_val_diy_key: 'specValue',
        selectSpecIds: [], //已选规格id列表
        value: [],
      },
    ]);
    const imgDataTitle: any = ref({
      type: 'title',
      label: '商品图片',
    });
    const imgData: any = ref({
      type: 'material_file',
      require: true,
      label: '图片',
      key: 'img',
      right_width: 675,
      height: 'auto',
      draggable:true,
      upload_text: '上传图片',
      desc: '建议尺寸800px*800px的方形图片,最大限制20M,在保证图片质量的情况下图片越小加载效果越好,最多可上传6张',
      limit: 6,
      fileList: [],
      specId: '', //规格id
      specValueId: '', //规格值id
      specValue: '', //规格值
      extra_param: '', //其他数据
      item_width: '100%',
      desc_width: '100%',
      callback: true,
    });
    const goodsImgData: any = ref([]);
    const goodsSkuImgData: any = ref([]);
    const videoData: any = ref([
      {
        type: 'title',
        label: '商品视频',
      },
      {
        type: 'material_video',
        label: '商品视频',
        key: 'video',
        height: 150,
        upload_text: '上传视频',
        desc: '最大限制20M,支持mp4格式,推荐时长不低于6s,不超过90s',
        fileList: [],
        item_width: '100%',
        desc_width: '100%',
        callback: true,
      },
    ]);
    const descData = ref([
      //商品详情描述
      {
        type: 'title',
        label: '商品详情描述',
      },
    ]);
  
    const dataSourceForm = ref({
      list: []
    })
    const dataSource = ref([
      {
        specInfoList: [],
        marketPrice: undefined,
        productPrice: undefined,
        integralPrice: undefined,
        productStock: undefined,
        weight: 1,
        length: 1,
        width: 1,
        height: 1,
        productStockWarning: undefined,
        productCode: undefined,
        barCode: undefined,
        state: 1,
        isDefault: 1,
      },
    ]);
    const base_columns = ref([
      {
        title: '商品原价',
        dataIndex: 'marketPrice',
        width: 160,
      },
      {
        title: '积分',
        dataIndex: 'integralPrice',
        width: 160,
      },
      {
        title: '现金(¥)',
        dataIndex: 'productPrice',
        width: 160,
      },
      {
        title: '库存',
        dataIndex: 'productStock',
        width: 160,
      },
      {
        title: '重量(KG)',
        dataIndex: 'weight',
        width: 160,
      },
      {
        title: '长(CM)',
        dataIndex: 'length',
        width: 160,
      },
      {
        title: '宽(CM)',
        dataIndex: 'width',
        width: 160,
      },
      {
        title: '高(CM)',
        dataIndex: 'height',
        width: 160,
      },
      {
        title: '预警值',
        dataIndex: 'productStockWarning',
        width: 160,
      },
      {
        title: '货号',
        dataIndex: 'productCode',
        width: 200,
      },
      {
        title: '条形码',
        dataIndex: 'barCode',
        width: 200,
      },
    ]);
    const spec_columns = ref([
      {
        title: '启用',
        dataIndex: 'state',
        width: 120,
      },
      {
        title: '默认选中',
        dataIndex: 'isDefault',
        width: 120,
      },
    ]);
    const show_columns: any = ref([...base_columns.value]);
    const pagination = ref({
      current: 1,
      pageSize: 10,
      total: 0,
      hideOnSinglePage: true,
      showQuickJumper: false,
      showSizeChanger: false,
    });
    const [standardTable, { updateTableData, getPaginationRef }] = useTable({
      dataSource: dataSource,
      columns: show_columns,
      pagination: pagination,
      maxHeight: 320,
      canResize: false,
    });
    
    const goodsTip = ref([]);//提示语
    const chooseFile = ref(0);
    const operate_type = ref(''); //素材中心操作类型
    const selectImageData = ref({ ids: [], data: [] }); //素材中心图片数据
    const selectImageData_goods = ref({ ids: [], data: [] }); //产品图片数据
    const selectVideoData: any = ref({ ids: [], data: [] }); //产品视频数据
    const file_index = ref(0); //用于区分同一个文件重复上传
    const skuImgFlag = ref(false); //是否设置多规格图片
    const operate_sku_id = ref(''); //当前操作的多规格图片id
    const operate_sku_index = ref(-1); //当前操作的多规格图片下标
  
    const formRef = ref();
  
    //校验事件回调
    function handleValida(res) {
      if (!validaFlag.value) {
        return;
      } else if (!res) {
        validaFlag.value = true;
        baseValida.value = false;
        step.value = 0;
        click_event.value = false;
        scrollBase.value?.scrollTo(0);
        return;
      }
  
      baseValida.value = false;
      if (isVirtualGoods.value == 2) {
        extraValida.value = true;
      } else {
        try {
          formRef.value.validate().then(() => {
            getEditorContentFlag.value = true;
          }).catch(() => {
            validaFlag.value = true;
            step.value = 1;
            click_event.value = false;
            setTimeout(() => {
              scrollGood.value?.scrollTo(Number(document.getElementById('goodsTable') ? document.getElementById('goodsTable').offsetTop : 0));
            }, 50)
            return;
          })
        } catch (err) {
          validaFlag.value = true;
          step.value = 1;
          click_event.value = false;
          setTimeout(() => {
            scrollGood.value?.scrollTo(Number(document.getElementById('goodsTable') ? document.getElementById('goodsTable').offsetTop : 0));
          }, 50)
          return;
        }
      }
    }
  
    function handleExtraValida(res) {
      if (!validaFlag.value) {
        return;
      } else if (!res) {
        validaFlag.value = true;
        extraValida.value = false;
        step.value = 0;
        click_event.value = false;
        scrollBase.value?.scrollBottom();
        return;
      }
  
      extraValida.value = false;
      try {
        formRef.value.validate().then(() => {
          getEditorContentFlag.value = true;
        }).catch(() => {
          validaFlag.value = true;
          step.value = 1;
          click_event.value = false;
          setTimeout(() => {
            scrollGood.value?.scrollTo(Number(document.getElementById('goodsTable') ? document.getElementById('goodsTable').offsetTop : 0));
          }, 50)
          return;
        })
      } catch (err) {
        validaFlag.value = true;
        step.value = 1;
        click_event.value = false;
        setTimeout(() => {
          scrollGood.value?.scrollTo(Number(document.getElementById('goodsTable') ? document.getElementById('goodsTable').offsetTop : 0));
        }, 50)
        return;
      }
    }
  
    //表单编辑回调
    function handleChange(item, key) {
      if (key == 'baseData') {
        let temp = baseData.value.filter(items => items.key == item.contentItem.key);
        if (temp.length > 0) {
          temp[0].value = item.val;
        }
        if (item.contentItem.key == 'isVirtualGoods') {
          isVirtualGoods.value = item.val;
          extraData.value = extraData.value.filter(items =>  items.key != 'reserveInfoList');
          if (item.val == 2) {
            extraData.value = JSON.parse(JSON.stringify([ ...extraData.value, ...extraVirtualData.value]));
          }
        }
      }else if (key == 'extraData') {
        let temp = extraData.value.filter(items => items.key == item.contentItem.key);
        if (temp.length > 0) {
          temp[0].value = item.val;
        }
      }else if (key == 'invoiceData') {
        let temp = invoiceData.value.filter(items => items.key == item.contentItem.key);
        if (temp.length > 0) {
          temp[0].value = item.val;
        }
      }
    }
  
    function handleTreeSelect(value, label, extra, index, item) {
      let tmp_label_ids = [];
      if (value.length > 0) {
        if (extra.allCheckedNodes.length != undefined && extra.allCheckedNodes.length) {
          extra.allCheckedNodes.map(item => {
            if (item.children != undefined && item.children.length) {
              item.children.map(child => {
                let target = child.node != undefined ? child.node.key : child.key;
                tmp_label_ids.push(target);
              });
            } else {
              let target = item.node != undefined ? item.node.key : item.key;
              tmp_label_ids.push(target);
            }
          });
        }
      }
      baseData.value[index].value = tmp_label_ids;
    }
  
    function handleReserveInfo(item) {
      let temp: any = extraData.value.filter(items => items.key == 'reserveInfoList');
      if (item.type == 'add') {
        if (temp.length > 0) {
          temp[0].data.push({
            key: reserve_info_key.value,
            reserveName: '',
            reserveType: 1,
            isRequired: true,
          })
          reserve_info_key.value += 1;
        }
      } else if (item.type == 'del') {
        if (temp.length > 0) {
          temp[0].data.splice(item.index, 1);
        }
      } else if (item.type == 'edit') {
        if (temp.length > 0) {
          temp[0].data[item.index][item.key] = item.val;
        }
      }
    }
  
    //图片删除回调
    function handleChangeUpload(val) {
      if (val.contentItem.key == 'img') {
        if(skuImgFlag.value && val.contentItem.index !== undefined && val.contentItem.index !== null){
          operate_sku_index.value = val.contentItem.index-1
        }else{
          operate_sku_index.value = -1
        }
        operate_sku_id.value = (val.contentItem.specValueId !== undefined && val.contentItem.specValueId !== null) ? val.contentItem.specValueId : '';
        if (operate_sku_index.value !== -1) {
          let temp = goodsSkuImgData.value.filter((item) => item.specValueId == operate_sku_id.value);
          if (temp.length > 0) {
            temp[0].fileList = val.file.fileList;
          }
        } else {
          let temp = goodsImgData.value.filter((item) => item.specValueId == operate_sku_id.value);
          if (temp.length > 0) {
            temp[0].fileList = val.file.fileList;
          }
        }
        if (val.file.fileList && val.file.fileList.length == 0) {
          if (operate_sku_index.value !== -1) {
            selectImageData_goods.value['selectImageData_goods'+operate_sku_index.value] = { ids:[], data:[] };
          } else {
            selectImageData_goods.value = { ids:[], data:[] };
          }
        } else if (val.file.fileList && val.file.fileList.length) {
          let ids: any = [];
          val.file.fileList.forEach(item => {
            if (item.uid.indexOf('?bindId=') != -1) {
              ids.push(Number(item.uid.split('?bindId=')[1].split('-')[0]));
            }
          })
          let arr: any = { ids:[], data:[] };
          if (operate_sku_index.value !== -1) {
            selectImageData_goods.value['selectImageData_goods'+operate_sku_index.value].ids.forEach((item,index) => {
              if (ids.indexOf(item) != -1) {
                arr.ids.push(selectImageData_goods.value['selectImageData_goods'+operate_sku_index.value].ids[index]);
                arr.data.push(selectImageData_goods.value['selectImageData_goods'+operate_sku_index.value].data[index]);
              }
            })
            selectImageData_goods.value['selectImageData_goods'+operate_sku_index.value] = arr;
          } else {
            selectImageData_goods.value.ids.forEach((item,index) => {
              if (ids.indexOf(item) != -1) {
                arr.ids.push(selectImageData_goods.value.ids[index]);
                arr.data.push(selectImageData_goods.value.data[index]);
              }
            })
            selectImageData_goods.value = arr;
          }
        }
      } else if (val.contentItem.key == 'video') {
        let temp = videoData.value.filter(item => item.key == 'video');
        if (temp.length > 0) {
          temp[0].fileList = val.file.fileList;
        }
        if (val.file.fileList && val.file.fileList.length == 0) {
          selectVideoData.value = { ids:[], data:[] };
        } else if (val.file.fileList && val.file.fileList.length) {
          let ids: any = [];
          val.file.fileList.forEach(item => {
            if (item.uid.indexOf('?bindId=') != -1) {
              ids.push(Number(item.uid.split('?bindId=')[1].split('-')[0]));
            }
          })
          let arr: any = { ids:[], data:[] };
          selectVideoData.value.ids.forEach((item,index) => {
            if (ids.indexOf(item) != -1) {
              arr.ids.push(selectVideoData.value.ids[index]);
              arr.data.push(selectVideoData.value.data[index]);
            }
          })
          selectVideoData.value = arr;
        }
      }
    }
  
    //表单素材回调
    function handleMaterial(item) {
      if (item.key == 'img') {
        if(item.fileList&&item.fileList.length>0){
          img_num.value = item.limit?Number(item.limit)-Number(item.fileList.length):6
        }else{
          img_num.value = item.limit?item.limit:6
        }
        operate_sku_index.value = skuImgFlag.value && (item.index !== undefined && item.index !== null) ? item.index-1 : -1;
        operate_sku_id.value = (item.specValueId !== undefined && item.specValueId !== null) ? item.specValueId : '';
        chooseFile.value = 1;
        operate_type.value = 'goods';
        if (operate_sku_index.value != -1) {
          selectImageData.value =  selectImageData_goods.value['selectImageData_goods' + operate_sku_index.value];
        } else {
          selectImageData.value =  selectImageData_goods.value;
        }
      } else {
        chooseFile.value = 2;
      }
    }
  
    //商品视频删除回调
    function deleteMaterial() {
      let temp = videoData.value.filter(item => item.key == 'video');
      if (temp.length > 0) {
        temp[0].fileList = [];
      }
      selectVideoData.value = { ids: [], data: [] };
    }
  
    //切换步骤
    function nextStep(index) {
      if (index == 0) {
        scrollBase.value?.scrollTo(0);
      } else if (index == 1) {
        scrollGood.value?.scrollTo(0);
      }
      step.value = index;
    }
  
    //发布
    function saveAllData() {
      if (click_event.value) return;
      click_event.value = true;
      baseValida.value = true;
    }
  
    onMounted(() => {
      if (route.query.id !== null && route.query.id) {
        id.value = route.query.id;
        step.value = 0;
      } else {
        get_store_category_list(null);
        spinning.value = false;
      }
      getSetting()
      goodsImgData.value = JSON.parse(JSON.stringify([ imgDataTitle.value, imgData.value ]));
      get_spec_list();
      window.addEventListener('resize', resizeScrollHeight, true);
    });
  
    onUnmounted(() => {
      window.removeEventListener('resize', resizeScrollHeight, true);
    });
  
    function resizeScrollHeight() {
      scrollHeight.value = document.body.clientHeight - 48 - 40 - 32 - 60 - 70 + 'px';
    }

      //获取系统配置
    const getSetting = async () => {
      try {
        let res = await getSettingListApi({ str: 'integral_conversion_ratio' });
        if (res.state == 200) {
          convert_rate.value = res.data[0].value * 1
          goodsTip.value = [`积分兑换现金比例为${convert_rate.value} ：1，即${convert_rate.value}积分 = 1元。`,`设置积分数需为平台设置的兑换比例积分${convert_rate.value}的整数倍。`,`会员兑换时，可以根据平台设置的积分与现金兑换比例自由选择使用积分数。`]
        }
      } catch (error) {}
    };
  
    //获取积分商品标签数据
    const get_store_category_list = async (innerLabel) => {
      const res: any = await getIntegralGoodsLabelListApi({ pageSize: 10000 });
      if (res.state == 200) {
        for (let i in res.data.list) {
          res.data.list[i].key = res.data.list[i].labelId;
          res.data.list[i].value = res.data.list[i].labelId;
          res.data.list[i].title = res.data.list[i].labelName;
          if (res.data.list[i].children != null && res.data.list[i].children.length > 0) {
            res.data.list[i].children.map(item => {
              item.key = item.labelId;
              item.value = item.labelId;
              item.title = item.labelName;
            });
          } else {
            res.data.list[i].disableCheckbox = true;
          }
        }
        let temp: any = baseData.value.filter((item) => item.key == 'labelIds');
        if (temp.length > 0) {
          temp[0].data = res.data.list;
          if (innerLabel && innerLabel.length > 0) {
            temp[0].value = [];
            innerLabel.map((label_item) => {
              temp[0].value?.push(Number(label_item.labelId));
            })
          }
        }
      }
    };
  
    //获取规格数据
    const get_spec_list = async () => {
      const res: any = await getIntegralGoodsSpecListApi({ pageSize: 10000 });
      if (res.state == 200 && res.data) {
        let temp = specData.value.filter((item) => item.key == 'specValue');
        if (temp.length > 0) {
          res.data.list.map((item) => {
            item.value = item.specId;
            item.label = item.specName;
          });
          temp[0].specList = res.data.list;
        }
        if (id.value !== null && id.value) {
          get_goods_detail();
        } else {
          editorFlag.value = true;
        }
      } else {
        failTip(res.msg);
      }
    };
  
    //获取商品数据
    const get_goods_detail = async () => {
      const res: any = await getIntegralGoodsDetailApi({ integralGoodsId: id.value });
      if (res.state == 200 && res.data) {
        let result = res.data;
        if(result.productList.length>0){
          result.productList.forEach(item=>{
            item.productPrice = item.cashPrice
          })
        }
        if (result.labelList && result.labelList.length > 0) {
          get_store_category_list(result.labelList);
        } else {
          get_store_category_list(null);
        }
        
  
        isVirtualGoods.value = result.isVirtualGoods;
        if (isVirtualGoods.value == 2) {
          extraData.value = JSON.parse(JSON.stringify([ ...extraData.value, ...extraVirtualData.value]));
        }

        baseData.value.map((item) => {
          if (item.type !== 'title' && item.key !== 'brandId') {
            if (item.key == 'categoryName') {
              item.value = result.categoryPath.split('->').join(' > ');
            } else if (item.key == 'isVirtualGoods') {
              item.value = (result[item.key] !== undefined && result[item.key] !== null) ? result[item.key] : undefined;
              item.disabled = true;
            } else if (item.key) {
              item.value = (result[item.key] !== undefined && result[item.key] !== null) ? result[item.key] : undefined;
            }
          }
        })
  
        invoiceData.value[1].value = (result['isVatInvoice'] !== undefined && result['isVatInvoice'] !== null) ? result['isVatInvoice'] : undefined;
  
        extraData.value.map((item) => {
          if (item.type !== 'title') {
            if (item.key == 'reserveInfoList') {
              item.data = [];
              if (result.reserveList && result.reserveList.length > 0) {
                result.reserveList.map((reserve_item, reserve_index) => {
                  let reserve_obj: any = {};
                  reserve_obj.key = reserve_index;
                  reserve_obj.reserveName = reserve_item.reserveName;
                  reserve_obj.reserveType = reserve_item.reserveType;
                  reserve_obj.isRequired = reserve_item.isRequired;
                  item.data.push(reserve_obj);
                })
              }
            } else if (item.key!='sellNow') {
              item.value = (result[item.key] !== undefined && result[item.key] !== null) ? result[item.key] : undefined;
            }
          }
        })
        
        if (result.specInfoList && result.specInfoList.length > 0) {
          //多规格规格、商品信息、图片
          let skuImgs: any = [];
          for (let i=0; i<result.specInfoList.length; i++) {
            let spec_temp: any = specData.value[1].specList?.filter((spec_item) => spec_item.specId == result.specInfoList[i].specId);
            if (spec_temp.length > 0) {
              let specObj: any = {
                selectedSpec: spec_temp[0],
                selectedValueSpec: [],
                setImg: (result.specInfoList[i].specValueList.length>0 && result.specInfoList[i].specValueList[0].imageList.length>0) ? true : false,
                specValueList: JSON.parse(JSON.stringify(spec_temp[0].valueList)),
              }
              if (result.specInfoList[i].specValueList.length > 0) {
                result.specInfoList[i].specValueList.map((spec_item,spec_index) => {
                  let sel_spec_temp = specObj.specValueList.filter((spec_obj_item) => spec_obj_item.specValueId == spec_item.specValueId);
                  if (sel_spec_temp.length > 0) {
                    specObj.selectedValueSpec.push(sel_spec_temp[0]);
                    specObj.specValueList = specObj.specValueList.filter((spec_obj_item) => spec_obj_item.specValueId != spec_item.specValueId);
                  }
                  let ids = []
                  if(spec_item.imageList){
                    spec_item.imageList.forEach(it=>{
                      let obj = it.image.split('bindId=')
                      if(obj.length>1){
                        ids.push(Number(it.image.split('bindId=')[1]))
                      }
                    })
                  }
                  selectImageData_goods.value['selectImageData_goods'+spec_index] = { ids:ids?ids:[], data:spec_item.imageList?spec_item.imageList:[] }; //多规格的图片数据
                })
              }
              specData.value[1].selectSpecList?.push(specObj);
              specData.value[1].specList = specData.value[1].specList?.filter((spec_item) => spec_item.specId != result.specInfoList[i].specId);
            }
  
            if (result.specInfoList[i].specValueList && result.specInfoList[i].specValueList.length > 0) {
              for (let j=0; j<result.specInfoList[i].specValueList.length; j++) {
                if (result.specInfoList[i].specValueList[j].imageList && result.specInfoList[i].specValueList[j].imageList.length > 0) {
                  if (!skuImgFlag.value) {
                    skuImgFlag.value = true;
                  }
                  let sku_itm_obj = JSON.parse(JSON.stringify(imgData.value));
                  sku_itm_obj.fileList = [];
                  result.specInfoList[i].specValueList[j].imageList.forEach((spec_img_item) => {
                    sku_itm_obj.fileList.push({
                      uid: spec_img_item.image + '-' + file_index.value,
                      thumbUrl: spec_img_item.imageUrl,
                      name: spec_img_item.image + '-' + file_index.value,
                      status: 'done',
                      response: {
                        state: 200,
                        data: {
                          path: spec_img_item.image,
                          url: spec_img_item.imageUrl,
                        },
                      },
                    });
                    file_index.value++;
                  });
                  sku_itm_obj.label = result.specInfoList[i].specValueList[j].specValue;
                  sku_itm_obj.specId = result.specInfoList[i].specId;
                  sku_itm_obj.specValue = result.specInfoList[i].specValueList[j].specValue;
                  sku_itm_obj.specValueId = result.specInfoList[i].specValueList[j].specValueId;
                  skuImgs.push(sku_itm_obj);
                }
              }
            }
          }
          if (!skuImgFlag.value && result.imageList.length > 0) {
            let img_fileList: any = [];
            result.imageList.map((img_item) => {
              img_fileList.push({
                uid: img_item.image + '-' + file_index.value,
                thumbUrl: img_item.imageUrl,
                name: img_item.image + '-' + file_index.value,
                status: 'done',
                response: {
                  state: 200,
                  data: {
                    path: img_item.image,
                    url: img_item.imageUrl,
                  },
                }
              });
              file_index.value++;
            });
            goodsImgData.value[1].fileList = img_fileList;
          }
          goodsSkuImgData.value = [ imgDataTitle.value, ...skuImgs ];
          setColumnHeader(result.productList);
        } else {
          //单规格图片、商品信息
          if (result.imageList.length > 0) {
            let img_fileList: any = [];
            result.imageList.map((img_item) => {
              img_fileList.push({
                uid: img_item.image + '-' + file_index.value,
                thumbUrl: img_item.imageUrl,
                name: img_item.image + '-' + file_index.value,
                status: 'done',
                response: {
                  state: 200,
                  data: {
                    path: img_item.image,
                    url: img_item.imageUrl,
                  },
                }
              });
              file_index.value++;
            });
            goodsImgData.value[1].fileList = img_fileList;
          }
          dataSource.value = result.productList;
          dataSourceForm.value.list = JSON.parse(JSON.stringify(dataSource.value));
        }
  
        let video_temp = videoData.value.filter((item) => item.key == 'video')[0];
        selectVideoData.value = { ids:[], data:[] };
        if (result.goodsVideo && result.goodsVideoUrl) {
          let video_info: any = {};
          video_info.uid = result.goodsVideo;
          video_info.thumbUrl = result.goodsVideoUrl;
          video_info.status = 'done';
          video_info.response = {};
          video_info.response.state = 200;
          video_info.response.data = {
            path: result.goodsVideo,
            url: result.goodsVideoUrl,
          };
          video_temp.fileList = [video_info];
          if (result.goodsVideo) {
            selectVideoData.value.ids = [Number(result.goodsVideo.split('bindId=')[1])];
            selectVideoData.value.data = ({
              bindId: result.goodsVideo.split('bindId=')[1],
              fileType: 2,
              filePath: result.goodsVideo,
              fileUrl: result.goodsVideoUrl,
            })
          }
        } else {
          video_temp.fileList = [];
        }
  
        if (res.data.goodsDetails) {
          initEditorContent.value = quillEscapeToHtml(res.data.goodsDetails); //商品详情
        }
      } else {
        failTip(res.msg);
      }
      spinning.value = false;
      editorFlag.value = true;
    };
  
    //获取编辑器内容
    const getEditorContent = async (con) => {
      let params: any = {};
      if (id.value !== null && id.value) {
        params.integralGoodsId = id.value; //编辑商品id
      }
      baseData.value.map((item) => { //基本信息
        if(item.key=='labelIds'){
          params[item.key] = item.value.join(',');
        }else if (item.type != 'title' && item.key != 'categoryName' && item.key) {
          params[item.key] = item.value;
        }
      })
      invoiceData.value.map((item) => { //发票信息
        if (item.type != 'title' && item.key) {
          params[item.key] = item.value;
        }
      })
      extraData.value.map((item) => { //其他信息
        if (item.type != 'title') {
          if (item.key == 'reserveInfoList') {
            params.reserveInfoList = [];
            if (item.data && item.data.length > 0) {
              item.data.map((reserve_item) => {
                params.reserveInfoList.push({
                  isRequired: reserve_item.isRequired ? 1 : 0,
                  reserveName: reserve_item.reserveName,
                  reserveType: reserve_item.reserveType,
                })
              })
            }
          } else if (item.key != undefined) {
            params[item.key] = item.value;
          }
        }
      })
      params.specInfoList = []; //商品规格
      params.productList = []; //商品列表
      let temp_spec: any = specData.value.filter((item) => item.key == 'specValue');
      if (temp_spec.length > 0 && temp_spec[0].selectSpecList.length > 0) {
        let productDefaultFlag = false; //多规格是否有默认选中项
        for (let k=0; k<temp_spec[0].selectSpecList.length; k++) {
          let obj: any = {};
          obj.specId = temp_spec[0].selectSpecList[k].selectedSpec.specId;
          obj.specName = temp_spec[0].selectSpecList[k].selectedSpec.specName;
          obj.isMainSpec = temp_spec[0].selectSpecList[k].setImg ? 1 : 2;
          obj.specValueList = temp_spec[0].selectSpecList[k].selectedValueSpec.filter((select_spec_item) => select_spec_item.specValueId);
          if (obj.specValueList.length == 0) {
            continue;
          } else if (obj.specValueList.length > 0 && temp_spec[0].selectSpecList[k].setImg) {
            for (let i=0; i<obj.specValueList.length; i++) {
              obj.specValueList[i].imageList = [];
              let temp_item = goodsSkuImgData.value.filter((sku_item) => sku_item.specValueId == obj.specValueList[i].specValueId);
              if (temp_item.length > 0 && temp_item[0].fileList && temp_item[0].fileList.length > 0) {
                temp_item[0].fileList.map((file_item,file_index) => {
                  obj.specValueList[i].imageList.push({
                    image: file_item.response.data.path,
                    isMain: file_index == 0 ? 1 : 2,
                  })
                })
              } else {
                step.value = 1;
                failTip('规格值为' + obj.specValueList[i].specValue + '的图片组,至少上传一张商品图片');
                click_event.value = false;
                getEditorContentFlag.value = false;
                setTimeout(() => {
                  scrollGood.value?.scrollTo(Number(document.getElementById('goodsImg') ? document.getElementById('goodsImg').offsetTop : 0));
                }, 50)
                return;
              }
            }
          }
          params.specInfoList.push(obj);
        }
        for (let j=0; j<dataSource.value.length; j++) {
          let obj: any = {};
          obj.marketPrice = dataSource.value[j].marketPrice;
          obj.integralPrice = dataSource.value[j].integralPrice;
          obj.cashPrice = dataSource.value[j].productPrice;
          obj.productStock = dataSource.value[j].productStock;
          obj.weight = dataSource.value[j].weight;
          obj.length = dataSource.value[j].length;
          obj.width = dataSource.value[j].width;
          obj.height = dataSource.value[j].height;
          obj.productStockWarning = (dataSource.value[j].productStockWarning !== undefined && dataSource.value[j].productStockWarning !== null
            && dataSource.value[j].productStockWarning !== '') ? Number(dataSource.value[j].productStockWarning) : '';
          obj.productCode = dataSource.value[j].productCode;
          obj.barCode = dataSource.value[j].barCode;
          obj.state = dataSource.value[j].state;
          obj.isDefault = dataSource.value[j].isDefault;
          obj.specInfoList = dataSource.value[j].specInfoList;
          params.productList.push(obj);
          if (dataSource.value[j].isDefault == 1) {
            productDefaultFlag = true;
          }
        }
        if (!productDefaultFlag) {
          step.value = 1;
          click_event.value = false;
          getEditorContentFlag.value = false;
          failTip('多规格商品需要设置默认选中数据');
          setTimeout(() => {
            scrollGood.value?.scrollTo(Number(document.getElementById('goodsTable') ? document.getElementById('goodsTable').offsetTop : 0));
          }, 50)
          return;
        }
      }
      if (!(temp_spec.length > 0 && temp_spec[0].selectSpecList.length > 0) || params.specInfoList.length == 0) {
        delete params.specInfoList;
        params.marketPrice = dataSource.value[0].marketPrice;
        params.integralPrice = dataSource.value[0].integralPrice;
        params.cashPrice = dataSource.value[0].productPrice;
        params.productStock = dataSource.value[0].productStock;
        params.weight = dataSource.value[0].weight;
        params.length = dataSource.value[0].length;
        params.width = dataSource.value[0].width;
        params.height = dataSource.value[0].height;
        params.productStockWarning = (dataSource.value[0].productStockWarning !== undefined && dataSource.value[0].productStockWarning !== null
          && dataSource.value[0].productStockWarning !== '') ? Number(dataSource.value[0].productStockWarning) : '';
        params.productCode = dataSource.value[0].productCode;
        params.barCode = dataSource.value[0].barCode;
      }
      params.imageList = []; //商品图片
      if (!skuImgFlag.value) {
        if (goodsImgData.value[1].fileList.length > 0) {
          goodsImgData.value[1].fileList.map((item,index) => {
            params.imageList.push({
              image: item.response.data.path,
              isMain: index == 0 ? 1 : 2,
            })
          })
        } else {
          step.value = 1;
          click_event.value = false;
          getEditorContentFlag.value = false;
          failTip('至少上传一张商品图片');
          setTimeout(() => {
            scrollGood.value?.scrollTo(Number(document.getElementById('goodsImg') ? document.getElementById('goodsImg').offsetTop : 0));
          }, 50)
          return false;
        }
      }
      if (videoData.value[1].fileList.length > 0) { //商品视频
        params.goodsVideo = videoData.value[1].fileList[0].response.data.path;
      }
      params.goodsDetails = con; //商品详情描述
      let res: any = '';
      if (id.value !== null && id.value) {
        res = await getIntegralGoodsEditApi(params);
      } else {
        res = await getIntegralGoodsAddApi(params);
      }
      if (res.state == 200) {
        sucTip(res.msg);
        userStore.setDelKeepAlive([route.name,'PointGoodsList'])
        setTimeout(() => {
          goBack();
          click_event.value = false;
          getEditorContentFlag.value = false;
        }, 500);
      } else {
        failTip(res.msg);
        click_event.value = false;
        getEditorContentFlag.value = false;
      }
    }
  
    //表格回调事件
    function handleClick(val, index, key) {
      index = index + ((getPaginationRef().current-1) * getPaginationRef().pageSize);
      if (val == 1 && key == 'isDefault' && dataSource.value.length>1) {
        for (let i=0; i<dataSource.value.length; i++) {
          if (i == index && val && !dataSource.value[i]['state']) {
            dataSource.value[i]['state'] = 1;
            updateTableData(i, 'state', 1);
          }
          dataSource.value[i][key] = i == index ? 1 : 0; 
          updateTableData(i, key, dataSource.value[i][key]);
        }
      } else {
        dataSource.value[index][key] = val;
        updateTableData(index, key, val);
      }
      dataSourceForm.value.list = JSON.parse(JSON.stringify(dataSource.value));
    }
  
    //商品规格操作
    const handleSpec = async (type, val, index, indexs) => {
      let temp: any = specData.value.filter((item) => item.key == 'specValue');
      if (temp.length > 0) {
        if (type == 'add') {
          //添加规格
          temp[0].selectSpecList.push({
            setImg: false, //是否设置图片规格
            selectedSpec: {}, //已选规格
            specValueList: [], //规格值列表
            selectedValueSpec: [], //已选规格值
          });
        } else if (type == 'selectSpec') {
          //选择规格
          let spec_item = temp[0].specList.filter((item) => item.specId == val)[0];
          // 去除已选规格名-start
          temp[0].specList = temp[0].specList.filter((item) => item.specId != val);
          // 去除已选规格名-end
          // 添加原有选择的规格名-start
          if (
            temp[0].selectSpecList[index].selectedSpec &&
            temp[0].selectSpecList[index].selectedSpec.specId
          ) {
            temp[0].specList.unshift(temp[0].selectSpecList[index].selectedSpec);
          }
          // 添加原有选择的规格名-end
          temp[0].selectSpecList[index].selectedSpec = spec_item;
          temp[0].selectSpecList[index].specValueList = spec_item.valueList;
          temp[0].selectSpecList[index].selectedValueSpec = [
            {
              specValue: '',
              specValueId: '',
            },
          ];
          setColumnHeader();
        } else if (type == 'setSpecImg') {
          //设置规格图片
          if (val) {
            temp[0].selectSpecList.map((items, indexs) => {
              items.setImg = indexs == index ? true : false;
            });
          } else {
            temp[0].selectSpecList[index].setImg = false;
          }
          setSkuImgs(temp[0].selectSpecList);
        } else if (type == 'addSpecVal') {
          //添加规格值
          if (
            temp[0].selectSpecList[index].selectedValueSpec.length > 0 &&
            !temp[0].selectSpecList[index].selectedValueSpec[
              temp[0].selectSpecList[index].selectedValueSpec.length - 1
            ].specValue
          )
            return;
          temp[0].selectSpecList[index].selectedValueSpec.push({
            specValue: '',
            specValueId: '',
          });
          setColumnHeader();
        } else if (type == 'selectSpecVal') {
          //选择规格值
          let spec_val_item = temp[0].selectSpecList[index].specValueList.filter(
            (item) => item.specValueId == val,
          )[0];
          // 去除已选规格值-start
          temp[0].selectSpecList[index].specValueList = temp[0].selectSpecList[
            index
          ].specValueList.filter((item) => item.specValueId != val);
          // 去除已选规格值-end
          // 添加原有选择的规格值-start
          if (
            temp[0].selectSpecList[index].selectedValueSpec[indexs] &&
            temp[0].selectSpecList[index].selectedValueSpec[indexs].specValueId
          ) {
            temp[0].specList.unshift(temp[0].selectSpecList[index].selectedValueSpec[indexs]);
          }
          // 添加原有选择的规格值-end
          temp[0].selectSpecList[index].selectedValueSpec[indexs] = spec_val_item;
          setColumnHeader();
          setSkuImgs(temp[0].selectSpecList);
        } else if (type == 'addSpecDown') {
          //规格名回车事件
          if(determineEmoji(val)){
            failTip('不能输入表情');
            return
          }
          if (temp[0].selectSpecList.filter(item => item.selectedSpec.specName == val).length > 0) {
            failTip('规格名已添加');
          } else if (temp[0].selectSpecList[index].selectedSpec.specName != val) {
            let spec_temp = temp[0].specList.filter(item => item.specName == val);
            if (spec_temp.length == 0) {
              const res: any = await getIntegralGoodsSpecAddSpecAddApi({ specName: val, });
              if (res.state == 200) {
                temp[0].selectSpecList[index] = {
                  selectedSpec: {
                    label: val,
                    sort: 1,
                    specId: res.data,
                    specName: val,
                    state: 1,
                    storeId: '',
                    value: res.data,
                    valueList: [],
                  },
                  selectedValueSpec: [{
                    specValue: '',
                    specValueId: '',
                  }],
                  setImg: false,
                  specValueList: [],
                };
                setColumnHeader();
              } else {
                failTip(res.msg);
              }
            } else {
              handleSpec('selectSpecDown', spec_temp[0].specId, index, indexs);
            }
          }
        } else if (type == 'addSpecValDown') {
          //规格值回车事件
          if(determineEmoji(val)){
            failTip('不能输入表情');
            return
          }
          if (temp[0].selectSpecList[index].selectedValueSpec.filter(item => item.specValue == val).length > 0) {
            failTip('规格值已添加');
          } else if (temp[0].selectSpecList[index].selectedValueSpec[indexs].specName != val) {
            let spec_val_temp = temp[0].selectSpecList[index].specValueList.filter(item => item.specValue == val);
            if (spec_val_temp.length == 0) {
              const res: any = await getIntegralGoodsSpecAddSpecValueApi({ specId: temp[0].selectSpecList[index].selectedSpec.specId, specValue: val });
              if (res.state == 200) {
                temp[0].selectSpecList[index].selectedValueSpec[indexs] = {
                  specValue: val,
                  specValueId: res.data,
                };
                setColumnHeader();
              } else {
                failTip(res.msg);
              }
            } else {
              handleSpec('selectSpecVal', spec_val_temp[0].specValueId, index, indexs);
            }
          }
        } else if (type == 'deleteSpec') {
          let del_temp = temp[0].selectSpecList.filter(
            (del_item, del_index) => del_index === index,
          );
          temp[0].selectSpecList = temp[0].selectSpecList.filter(
            (del_item, del_index) => del_index !== index,
          );
          if (Object.keys(del_temp[0].selectedSpec).length > 0) {
            temp[0].specList.push(del_temp[0].selectedSpec); 
            setColumnHeader();
            setSkuImgs(temp[0].selectSpecList);
          }
        } else if (type == 'deleteSpecVal') {
          let del_temp = temp[0].selectSpecList[index].selectedValueSpec.filter(
            (del_item, del_index) => del_index === indexs,
          );
          temp[0].selectSpecList[index].selectedValueSpec = temp[0].selectSpecList[
            index
          ].selectedValueSpec.filter((del_item, del_index) => del_index !== indexs);
          if (del_temp[0].specValue) {
            temp[0].selectSpecList[index].specValueList.push(del_temp[0]);
            setColumnHeader();
            setSkuImgs(temp[0].selectSpecList);
          }
        }
      }
    }
  
    //根据规格设置表格头数据
    function setColumnHeader(dataSource = null) {
      let temp: any = specData.value.filter((item) => item.key == 'specValue');
      let extra_columns: any = [];
      let spec_all_data: any = [];
      if (temp[0].selectSpecList && temp[0].selectSpecList.length > 0) {
        for (let i = 0; i < temp[0].selectSpecList.length; i++) {
          let item = temp[0].selectSpecList[i];
          if (
            item.selectedValueSpec &&
            item.selectedValueSpec.length > 0 &&
            item.selectedValueSpec[0].specValueId
          ) {
            extra_columns.push({
              title: item.selectedSpec.specName,
              dataIndex: `spec_info[${i}]`,
              width: 100,
            });
            let spec_all_data_item: any = [];
            for (let j = 0; j < item.selectedValueSpec.length; j++) {
              let items = item.selectedValueSpec[j];
              if (items.specValueId) {
                spec_all_data_item.push(items);
              } else {
                break;
              }
            }
            spec_all_data.push(spec_all_data_item);
          } else {
            break;
          }
        }
      }
      if (extra_columns.length > 0) {
        show_columns.value = [...extra_columns, ...base_columns.value, ...spec_columns.value];
      } else {
        show_columns.value = [...base_columns.value];
      }
      setColumnData(calcDescartes(spec_all_data), dataSource);
    }
  
    //根据规格设置商品图片
    function setSkuImgs(selectSpecList) {
      if (selectSpecList && selectSpecList.length > 0) {
        let skuImgs: any = [];
        for (let i=0; i<selectSpecList.length; i++) {
          if (selectSpecList[i].setImg && selectSpecList[i].selectedValueSpec && selectSpecList[i].selectedValueSpec.length > 0) {
            for (let j=0; j<selectSpecList[i].selectedValueSpec.length; j++) {
              if (selectSpecList[i].selectedValueSpec[j].specValueId) {
                let obj = JSON.parse(JSON.stringify(imgData.value));
                obj.label = selectSpecList[i].selectedValueSpec[j].specValue;
                obj.name = selectSpecList[i].selectedValueSpec[j].specValueId;
                obj.specId = selectSpecList[i].selectedSpec.specId;
                obj.specValue = selectSpecList[i].selectedValueSpec[j].specValue;
                obj.specValueId = selectSpecList[i].selectedValueSpec[j].specValueId;
                selectImageData_goods.value['selectImageData_goods'+j] = { ids:[], data:[] }; //多规格的图片数据
                skuImgs.push(obj);
              }
            }
          }
        }
        if (skuImgs.length > 0) {
          skuImgFlag.value = true;
          goodsSkuImgData.value = [ imgDataTitle.value, ...skuImgs ];
        } else {
          skuImgFlag.value = false;
          goodsSkuImgData.value = [];
        }
      } else if (skuImgFlag.value) {
        skuImgFlag.value = false;
        goodsSkuImgData.value = [];
      }
    }
  
    //根据规格设置表格列表数据
    function setColumnData(spec_data, data_source = null) {
      let old_data: any = [];
      if (data_source) {
        old_data = JSON.parse(JSON.stringify(data_source));
      } else {
        old_data = JSON.parse(JSON.stringify(dataSource.value));
      }
      let data: any = [];
      if (spec_data && spec_data.length > 0) {
        for (let i = 0; i < spec_data.length; i++) {
          if (spec_data[i] && Array.isArray(spec_data[i]) && spec_data[i].length > 0) {
            let item: any = {};
            item.specInfoList = [];
            item.marketPrice = undefined;
            item.integralPrice = undefined;
            item.productPrice = undefined;
            item.productStock = undefined;
            item.weight = 1;
            item.length = 1;
            item.width = 1;
            item.height = 1;
            item.productStockWarning = undefined;
            item.productCode = undefined;
            item.barCode = undefined;
            item.state = 1;
            item.isDefault = i == 0 ? 1 : 0;
            item.specValueIdArray = [];
            for (let j = 0; j < spec_data[i].length; j++) {
              item.specValueIdArray.push(spec_data[i][j].specValueId.toString());
              item[`spec_info[${j}]`] = spec_data[i][j].specValue;
              item.specInfoList.push(spec_data[i][j]);
            }
            item.specValueIdArray = item.specValueIdArray.join(',');
            data.push(item);
          } else if (spec_data[i] && Object.keys(spec_data[i]).length > 0) {
            let item: any = {};
            item.specInfoList = [];
            item.marketPrice = undefined;
            item.productPrice = undefined;
            item.integralPrice = undefined;
            item.productStock = undefined;
            item.weight = 1;
            item.length = 1;
            item.width = 1;
            item.height = 1;
            item.productStockWarning = undefined;
            item.productCode = undefined;
            item.barCode = undefined;
            item.state = 1;
            item.isDefault = i == 0 ? 1 : 0;
            item.specValueIdArray = spec_data[i].specValueId.toString();
            item[`spec_info[0]`] = spec_data[i].specValue;
            item.specInfoList.push(spec_data[i]);
            data.push(item);
          }
        }
        if (data.length > 0) {
          if (old_data && old_data.length > 0) {
            for (let old_index = 0; old_index < old_data.length; old_index++) {
              if ((data_source && old_data[old_index].specValueIds) || (!data_source && old_data[old_index].specValueIdArray)) {
                let data_temp: any = []
                if (data_source) {
                  data_temp = data.filter((data_item) => data_item.specValueIdArray == old_data[old_index].specValueIds);
                } else {
                  data_temp = data.filter((data_item) => data_item.specValueIdArray == old_data[old_index].specValueIdArray);
                }
                if (data_temp.length > 0) {
                  data_temp[0].marketPrice = old_data[old_index].marketPrice;
                  data_temp[0].productPrice = old_data[old_index].productPrice;
                  data_temp[0].integralPrice = old_data[old_index].integralPrice;
                  data_temp[0].productStock = old_data[old_index].productStock;
                  data_temp[0].weight = old_data[old_index].weight;
                  data_temp[0].length = old_data[old_index].length;
                  data_temp[0].width = old_data[old_index].width;
                  data_temp[0].height = old_data[old_index].height;
                  data_temp[0].productStockWarning = old_data[old_index].productStockWarning;
                  data_temp[0].productCode = old_data[old_index].productCode;
                  data_temp[0].barCode = old_data[old_index].barCode;
                  data_temp[0].state = old_data[old_index].state;
                  data_temp[0].isDefault = old_data[old_index].isDefault;
                }
              }
            }
          }
          dataSource.value = data;
          dataSourceForm.value.list = JSON.parse(JSON.stringify(data));
        }
      }
    }
  
    function closeMaterial() {
      chooseFile.value = 0;
      operate_type.value = '';
    }
  
    function confirmMaterial(type, val) {
      if (type == 'image' && operate_type.value == 'goods') {
        if (operate_sku_index.value !== -1) {
          let temp = goodsSkuImgData.value.filter((item) => item.specValueId == operate_sku_id.value);
          if (temp && temp.length && val.data.length) {
            val.data.forEach((item) => {
              temp[0].fileList.push({
                uid: item.filePath + '-' + file_index.value,
                thumbUrl: item.fileUrl,
                name: item.fileName,
                status: 'done',
                response: {
                  state: 200,
                  data: {
                    path: item.filePath,
                    url: item.fileUrl,
                  },
                },
              });
              file_index.value++;
            });
          }
          selectImageData_goods.value['selectImageData_goods' + operate_sku_index.value].ids =
            selectImageData_goods.value[
              'selectImageData_goods' + operate_sku_index.value
            ].ids.concat(val.ids);
          selectImageData_goods.value['selectImageData_goods' + operate_sku_index.value].data =
            selectImageData_goods.value[
              'selectImageData_goods' + operate_sku_index.value
            ].data.concat(val.data);
        } else {
          let temp = goodsImgData.value.filter((item) => item.specValueId == operate_sku_id.value);
          if (temp && temp.length && val.data.length) {
            val.data.forEach((item) => {
              temp[0].fileList.push({
                uid: item.filePath + '-' + file_index.value,
                thumbUrl: item.fileUrl,
                name: item.fileName,
                status: 'done',
                response: {
                  state: 200,
                  data: {
                    path: item.filePath,
                    url: item.fileUrl,
                  },
                },
              });
              file_index.value++;
            });
          }
          selectImageData_goods.value['ids'] = selectImageData_goods.value['ids'].concat(val.ids);
          selectImageData_goods.value['data'] = selectImageData_goods.value['data'].concat(
            val.data,
          );
        }
        operate_type.value = '';
        chooseFile.value = 0;
        selectImageData.value = selectImageData_goods.value;
      } else if (type == 'video') {
        videoData.value[1].fileList = [];
        if (val.data.length) {
          val.data.forEach((item) => {
            videoData.value[1].fileList.push({
              uid: item.filePath + '-' + file_index.value,
              thumbUrl: item.fileUrl,
              name: item.fileName,
              status: 'done',
              response: {
                state: 200,
                data: {
                  path: item.filePath,
                  url: item.fileUrl,
                },
              },
            });
            file_index.value++;
          });
        }
        chooseFile.value = 0;
        selectVideoData.value = val;
      }
    }
  
    function goBack() {
      if (window.history && window.history.length == 1) {
        pageClose();
      } else {
        const { fullPath } = route;
        tabStore.closeTabByKey(fullPath, router);
        router.back();
      }
    }

  //验证积分数需为平台设置的兑换比例积分的整数倍
  const validatorIntegral = (rule, value) => { 
    if (value % convert_rate.value > 0) {
      return Promise.reject(`需为${convert_rate.value}的整数倍`);
    }
    return Promise.resolve();
  }; 

  //验证商品原价，必须比现金价大才可以
  const validatorMarketPrice = (rule, value, cashPrice) => {
    if (value && value <= cashPrice) {
      return Promise.reject(`应大于现金价`);
    }
    return Promise.resolve();
  };
  </script>
<style lang="less">
  @import './style/add_goods.less';
</style>
