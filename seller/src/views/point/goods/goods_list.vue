<template>
  <div class="section_padding point_goods_list">
    <div class="section_padding_back">
      <SldComHeader
        :title="L('商品管理')"
      />
      <Tabs type="card" v-model:activeKey="activeKey" class="tabs_nav">
        <TabPane key="1" :tab="L('在售列表')">
          <ShowMoreHelpTip :tipData="goodsTip" v-if="sld_show_tip"></ShowMoreHelpTip>
          <GoodsOnlineLists :canResize="canResize" v-if="activeKey=='1'"></GoodsOnlineLists>
        </TabPane>
        <TabPane key="2" :tab="L('仓库中商品')">
          <GoodsStorageLists  v-if="activeKey=='2'"></GoodsStorageLists>
        </TabPane>
        <TabPane key="3" :tab="L('待审核商品')">
          <GoodsCheckLists :canResize="canResize"  v-if="activeKey=='3'"></GoodsCheckLists>
        </TabPane>
        <TabPane key="4" :tab="L('违规下架商品')">
          <GoodsOfflineLists  v-if="activeKey=='4'"></GoodsOfflineLists>
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'PointGoodsList',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, onMounted } from 'vue';
  import { Tabs, TabPane } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import ShowMoreHelpTip from '/@/components/ShowMoreHelpTip/index.vue';
  import GoodsOnlineLists from './goods_online_lists.vue';
  import GoodsCheckLists from './goods_check_lists.vue';
  import GoodsStorageLists from './goods_storage_lists.vue';
  import GoodsOfflineLists from './goods_offline_lists.vue';
  import { getSettingListApi } from '/@/api/common/common';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const activeKey = ref('1');

  const canResize = ref(true);

  const goodsTip = ref([]);

  const sld_show_tip = ref(true);

  //获取系统配置
  const get_common_setting = async()=> {
    const res = await getSettingListApi({
      str: 'integral_conversion_ratio',
    });
    if (res.state == 200 && res.data) {
      goodsTip.value[0] = L('积分兑换现金比例为')+res.data[0].value+`：1，${L('即')}`+res.data[0].value+`${L('积分')} = 1${L('元')}。`
    }
  }

  onMounted(() => {
    get_common_setting()
  });
</script>
<style lang="less">
  .point_goods_list {
    .tabs_nav {
      .ant-tabs-nav {
        margin-bottom: 0;
      }
    }
  }
</style>
