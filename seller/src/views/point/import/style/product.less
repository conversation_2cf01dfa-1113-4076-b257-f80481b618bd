.right_nav {
  position: fixed;
  top: 20vh;
  right: 10px;
  width:120px;
  background:rgba(255,255,255,1);
  box-shadow:0 0 15px 0 rgba(153,153,153,0.15);
  .nav_title{
    height:38px;
    line-height: 38px;
    width: 100%;
    display: inline-block;
    background:rgba(236,245,255,1);
    text-align:center;
    color: #08A9B7;
    font-size: 14px;
    font-weight: 700;
  }
  .nav_item{
    color: #999;
    font-size: 12px;
    font-weight: 700;
    padding: 9px 35px 9px 26px;
    width:111px;
    height:30px;
    line-height: 12px;
    background:#fff;
    border-radius:15px 0 0 15px;
    display: block;
    margin: 10px 0 10px 9px;
  }
  .nav_item:hover,.nav_item.nav_sel{
    color: #333;
    background:rgba(65,157,253,.1);
  }
}

.goods_detail_body{
  border: 1px solid #F2F2F2;
  padding: 15px;
}

.goods_info{
  position: relative;
  .virtual_goods_flag{
    position: absolute;
    left: 1px;
    top: 1px;
    font-size: 12px;
    color: #fff;
    background: linear-gradient(90deg, #F7D47E 0%, #E6B845 100%);
    border-radius: 3px 0 3px 0;
    padding: 0 3px;
  }
  .goods_detail{
    height: 80px;
    margin-left: 10px;
    flex: 1;
  }
  .goods_img{
    width:80px;
    height:80px;
    background:rgba(248,248,248,1);
    border:1px solid rgba(226,229,246,1);
    border-radius:3px;
    overflow: hidden;
    display: inline-block;
  }
  .goods_name{
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    /*! autoprefixer: off */
    -webkit-box-orient: vertical;
    word-break: break-word;
    color:#333;
    font-size: 14px;
    line-height: 17px;
    text-align: left;
    height: 34px;
    flex-shrink: 0;
  }
  .goods_brief{
    color: #666;
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: left;
    width: 100%;
    display: inline-block;
  }
}

.goods_import_title{
  font-size: 18px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: #404040;
  margin-left: 10px;
  margin-top: 10px;
  margin-bottom: 25px;
}
.goods_import_con{
  width: 100%;
  padding-left: 15px;
  flex-wrap: wrap;
  .item{
    cursor: pointer;
    width: 466px;
    min-height: 150px;
    height: auto;
    background: #FFF;
    box-shadow: 0 0 3px 0 rgba(0,0,0,0.09);
    border-radius: 4px;
    padding: 25px 20px;
    box-sizing: border-box;
    .icon{
      width: 100px;
      height: 100px;
    }
    .right_con{
      margin-left: 20px;
      flex: 1;
      min-height: 100px;
      height: 100%;
      .top{
        .title{
          font-size: 15px;
          line-height: 27px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #404040;
        }
        .desc{
          font-size: 13px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #666;
          margin-top: 1px;
        }
      }
      .import_btn{
        width: 86px;
        height: 28px;
        line-height: 28px;
        background: #FF701E;
        border-radius: 4px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #FFF;
        text-align: center;
      }
    }
  }
}

