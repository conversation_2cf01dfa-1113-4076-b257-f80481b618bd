<template>
  <div class="section_padding goods_list point_excel_import">
    <div class="section_padding_back">
      <SldComHeader title="Excel导入" />
      <ShowMoreHelpTip tipTitle="操作提示" :tipData="goodsTip" v-if="sld_show_tip"></ShowMoreHelpTip>
      <Spin :spinning="initLoading">
        <div class="point_excel_import_height">
          <div style="padding: 40px 200px;">
            <Steps :current="curStep">
              <Step title="下载商品导入模板"></Step>
              <Step title="上传文件"></Step>
              <Step title="完成"></Step>
            </Steps>
          </div>
          <div class="goods_import excel_goods_import_box">
            <template v-if="curStep == 0">
              <TreeSelect
                v-model:value="labelIds"
                :style="{ width: '400px' }"
                :treeData="labelList"
                allowClear
                :treeDefaultExpandAll="true"
                :treeCheckable="true"
                showCheckedStrategy="SHOW_PARENT"
                placeholder="请选择积分商品标签"
                :dropdownStyle="{ maxHeight: '300px' }"
                @change="treeSelectChange"
                :getPopupContainer="triggerNode => triggerNode.parentNode"
              />
              <div style="width: 100%;height: 30px;"></div>
              <Button type="primary" size='large' @click="downLoadMould" v-if="labelIds.length>0">
                <template #icon>
                  <DownloadOutlined />
                </template>
                下载商品导入模板
              </Button>
              <div style="width: 100%;height: 40px;"></div>
              <Button type="primary" size='large' @click="goUpload" v-if="labelIds.length>0">下一步</Button>
            </template>
            <template v-if="curStep == 1">
              <UploadDragger listType="picture-card" name="file" accept='.xls' :data="{labelIds:labelIds.join(',')}"
                :action="`${apiUrl}${apiInterface}`" :beforeUpload="(file) => beforeUpload(file, ' .xls')"
                @change="(e) => handleFileChange(e)" :showUploadList="false" :headers="{
                  Authorization: imgToken,
                }"
                >
                <div style="margin: 16px 0;height: 116px;vertical-align:middle;display: table-cell;">
                  <PlusOutlined style="font-size: 32px;color: #999;" />
                  <p style="margin-top: 5px;">点击上传或者拖拽文件到该区域即可</p>
                </div>
              </UploadDragger>
              <div style="width: 100%;height: 40px;"></div>
              <Button type="primary" size='large' @click="previousStep">上一步</Button>
              <div v-if="errorFileUrl || waringFile" style="margin-top: 12px;color: rgb(51, 51, 51);text-align: center;">
                <template v-if="waringFile">
                  <span v-if="errorFileReason"
                    style="color: rgb(225, 0, 0);">上传失败！{{ errorFileReason ? errorFileReason : '' }}</span>
                  <span v-else>数据正在导入，导入需要一定的时间，请耐心等待～</span>
                </template>
                <template v-else>
                  <span style="color: rgb(225, 0, 0);">上传失败！</span>
                  <span>您可以</span>
                  <a download="错误表格.xls" :href="errorFileUrl" style="color: #2878FF;">下载错误表格</a>
                  <span>，查看错误原因，修改后重新上传。</span>
                </template>
              </div>
            </template>
            <template v-if="curStep == 2">
              <p class="import_success_con">导入成功！</p>
              <p class="import_success_tip">您可以前往商品列表查看已导入的商品，或是继续导入。</p>
              <div style="width: 100%;height: 2px;background: #fff;"></div>
              <Button type="primary" size='large' @click="nextUpload">继续导入</Button>
            </template>
          </div>
        </div>
      </Spin>
    </div>
  </div>
</template>
<script>
export default {
  name: 'PointGoodsImport',
};
</script>
<script setup>
import { ref, onMounted,computed } from 'vue';
import { Spin, Step, Steps, Button, UploadDragger,TreeSelect } from 'ant-design-vue';
import {
  DownloadOutlined
} from '@ant-design/icons-vue';
import SldComHeader from '/@/components/SldComHeader/index.vue';
import ShowMoreHelpTip from '/@/components/ShowMoreHelpTip/index.vue';
import { useGlobSetting } from '/@/hooks/setting';
import { sucTip, failTip } from '@/utils/utils';
import { useUserStore } from '/@/store/modules/user';
const { SHOW_PARENT } = TreeSelect;
import {
  PlusOutlined
} from '@ant-design/icons-vue';
import { getToken } from '/@/utils/auth';
import {
  getPointGoodsDownLoadApi,
  getPointGoodsProcessApi,
  getIntegralGoodsLabelListApi
} from '/@/api/point/import';
const { apiUrl } = useGlobSetting();

const goodsTip = ref(['请先下载商品导入模板，并按照批注中的要求填写商品数据，未按要求填写将会导致商品导入失败', '请选择.xls文件，每次只能导入一个文件，建议每次导入不超过6000条商品数据']);
const sld_show_tip = ref(true);
const initLoading = ref(false)
const curStep = ref(0)//步骤条当前步骤
const waringFile = ref(false)//显示导入中提示
const errorFileReason = ref('')//错误提示内容
const errorFileUrl = ref('')//错误文件地址
const userStore = useUserStore();

const tl_flag = ref(false)

const labelIds = ref([]) //积分标签ids

const labelList = ref([])

const apiInterface = ref('/v3/integral/seller/integral/goods/import')

const timeoutEvent = ref(null)//产品导入定时任务(5s)
const timeoutEventKey = ref('')//产品导入定时接口传参

const imgToken = ref('Bearer ' + getToken())

//下载商品导入模板
const downLoadMould = async () => {
  let paramData = {};
  paramData.fileName = `商品导入模板`;
  initLoading.value = true
  let res = await getPointGoodsDownLoadApi(paramData)
  if (res.state != undefined && res.state == 255) {
    failTip(res.msg)
  } else {
    goUpload();//下载成功后自动到下一步
  }
  initLoading.value = false
}

//文件上传前处理数据
const beforeUpload = async(file, accept, limit) => {
  imgToken.value = 'Bearer ' + userStore.getToken
  if(userStore.getToken){
    let res = await userStore.getSldCommonService()
    if(res.state == 200){
      imgToken.value = 'Bearer ' + res.token
    }
  }
  if (accept != undefined && accept != null && accept) {
    //校验文件格式类型
    let accept_list = accept
      .replaceAll(' ', '')
      .split(',')
      .filter((item) => item && item.trim());
    let file_type = file.name ? '.' + file.name.split('.').at(-1) : '';
    if (accept_list.length > 0 && file_type != '' && accept_list.indexOf(file_type) == -1) {
      failTip('上传文件格式有误');
      return false;
    }
  }
  initLoading.value = true
  errorFileUrl.value = ''
  errorFileReason.value = ''
  waringFile.value = false
}

//数据变化事件
const handleFileChange = (info) => {
  if (info.file.status != undefined && info.file.status != 'error') {
    if (info.file != undefined && info.file.response != undefined) {
      if (info.file.response.state == 200) {
        waringFile.value = true;
        timeoutEventKey.value = info.file.response.data;
        getUoloadFileState();
        timeoutEvent.value = setInterval(() => {
          getUoloadFileState();
        }, 5000)
      } else if (info.file.response.state == 267) {
        // dev_tonglian-start
        if(tl_flag.value){
          failTip(info.file.response.msg);
        }
        // dev_tonglian-end
        errorFileUrl.value = info.file.response.data;
        initLoading.value = false;
      } else {
        failTip(info.file.response.msg);
        initLoading.value = false;
      } 
    }
  }
}

//定时获取上传结果
const getUoloadFileState = async () => {
  if (!timeoutEventKey.value) {
    return;
  }
  let res = await getPointGoodsProcessApi({ key: timeoutEventKey.value })
  if (res.state == 200) {
    if (res.data.process == 2) { //成功
      waringFile.value = false
      curStep.value = 2
      errorFileReason.value = ''
      initLoading.value = false
    } else if (res.data.process == 3) { //失败
      errorFileReason.value = res.data.failReason
      initLoading.value = false
    } else {
      return;
    }
    // @ts-ignore
    clearInterval(timeoutEvent.value);
    timeoutEvent.value = null
    timeoutEventKey.value = '';
  }
}

//继续导入事件
const nextUpload = () => {
  curStep.value = 1
  errorFileUrl.value = ''
  waringFile.value = false
  errorFileReason.value = ''
}

//下一步的点击事件
const goUpload = () => {
  curStep.value = 1
};

// 上一步的点击事件
const previousStep = ()=> {
  curStep.value = 0
}

const treeSelectChange = (value, label, extra)=> {
  let tmp_label_ids = [];
    if (value.length > 0) {
      if (extra.allCheckedNodes.length != undefined && extra.allCheckedNodes.length) {
        extra.allCheckedNodes.map(item => {
          if (item.children != undefined && item.children.length) {
            item.children.map(child => {
              let target = child.node != undefined ? child.node.key : child.key;
              tmp_label_ids.push(target);
            });
          } else {
            let target = item.node != undefined ? item.node.key : item.key;
            tmp_label_ids.push(target);
          }
        });
      }
    }
    labelIds.value = tmp_label_ids;
}


//获取积分商品标签数据
const get_store_category_list = async (innerLabel) => {
  const res = await getIntegralGoodsLabelListApi({ pageSize: 10000 });
  if (res.state == 200) {
    for (let i in res.data.list) {
      res.data.list[i].key = res.data.list[i].labelId;
      res.data.list[i].value = res.data.list[i].labelId;
      res.data.list[i].title = res.data.list[i].labelName;
      if (res.data.list[i].children != null && res.data.list[i].children.length > 0) {
        res.data.list[i].children.map(item => {
          item.key = item.labelId;
          item.value = item.labelId;
          item.title = item.labelName;
        });
      } else {
        res.data.list[i].disableCheckbox = true;
      }
    }
    labelList.value = res.data.list;
  }
};

onMounted(() => {
  get_store_category_list()
  
});
</script>
<style lang="less">
@import '/@/assets/css/goods.less';

.point_excel_import {
  .excel_goods_import_box {
    text-align: center;
    .ant-select-selection-placeholder{
      right: auto;
    }
    .ant-select-tree-node-content-wrapper{
      flex: none !important;
    }
  }

  .point_excel_import_height{
    height: calc(100vh - @header-height - 54px - 40px - 104px);
    overflow: auto;
  }

  .ant-steps-item-finish .ant-steps-item-icon {
    line-height: 38px;
  }

  .ant-steps .ant-steps-item:not(.ant-steps-item-active)>.ant-steps-item-container[role='button'] {
    cursor: auto;
  }
}
</style>
