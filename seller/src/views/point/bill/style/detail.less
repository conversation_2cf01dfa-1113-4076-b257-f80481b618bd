/* 账单详情 start */
.detail_total_part {
  width: 100%;
  height: 60px;
  border-radius: 6px;
  background: rgb(255 255 255 / 100%);
  box-shadow: 0 0 8px 0 rgb(153 153 153 / 5%), 0 0 8px 0 rgb(153 153 153 / 5%);
  font-size: 16px;

  img {
    width: 36px;
    height: 36px;
    margin-right: 15px;
    margin-left: 20px;
  }

  .amount_detail {
    color: #333;
  }

  .amount_total {
    color: #ff2b4e;
    white-space: nowrap;
  }
}

.invoice_btn {
  width: 120px;
  height: 36px;
  margin-top: 15px;
  border-radius: 3px;
  background: #fb6f1e;
  color: rgb(255 255 255 / 100%);
  line-height: 36px;
  text-align: center;
  cursor: pointer;
}

/* 账单详情 end */
.point_bill_detail {
  .ant-descriptions-item-label {
    width: 20%;
  }

  .ant-descriptions-item-content {
    width: 30%;
  }

  .upload_style {
    .ant-descriptions-item-label {
      width: 20%;
    }

    .ant-descriptions-item-content {
      width: 80%;
    }
  }

  /* 退货详情样式-start */
  .height_detail {
    box-sizing: border-box;
    min-height: 300px;
    padding-top: 50px;
  }

  .progress {
    .item {
      .top {
        position: relative;
        width: 215px;

        img {
          width: 75px;
          height: 75px;
        }

        .left_line,
        .right_line {
          content: '';
          position: absolute;
          top: 50%;
          width: 50px;
          height: 0;
          border-top: 1px solid rgb(255 109 31 / 30%);
        }

        .left_line {
          left: 0;
        }

        .right_line {
          right: 0;
        }
      }

      .state {
        margin-top: 10px;
        font-size: 14px;
      }

      .time {
        font-size: 14px;
      }
    }

    .item.cur {
      .state {
        color: #fb6f1e;
      }

      .time {
        color: rgb(251 111 30 / 50%);
      }

      .left_line,
      .right_line {
        border-color: #fb6f1e;
      }
    }

    .item.no {
      .state {
        color: #999;
      }

      .left_line,
      .right_line {
        border-color: #eee;
      }
    }

    .item.pass {
      .state {
        color: rgb(251 111 30 / 50%);
      }

      .time {
        color: rgb(251 111 30 / 30%);
      }

      .left_line,
      .right_line {
        border-color: rgb(251 111 30 / 30%);
      }
    }
  }

  .state_part {
    margin-top: 50px;
    margin-bottom: 40px;

    .title {
      color: #333;
      font-size: 26px;
    }

    .tip {
      color: #999;
      font-size: 14px;
    }
  }

  .agree_btn {
    width: 120px;
    height: 36px;
    margin-top: 15px;
    border: 1px solid #fb6f1e;
    border-radius: 3px;
    color: #fb6f1e;
    font-size: 14px;
    line-height: 36px;
    text-align: center;
  }

  /* 退货详情样式-end */
}

.sld_common_title {
  margin-top: 15px;
  margin-bottom: 15px;
  margin-left: 5px;
  color: rgb(51 51 51);
  font-size: 14px;
  font-weight: bold;
}
