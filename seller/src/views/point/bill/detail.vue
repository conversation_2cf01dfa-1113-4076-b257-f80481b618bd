<template>
  <div class="section_padding point_bill_detail">
    <div class="section_padding_back">
      <SldComHeader back :title="L('结算详情')" />
      <div class="com_line" style="height: 1px"></div>
      <Spin :spinning="loading">
        <div class="height_detail">
          <div class="flex_row_center_start progress">
            <div
              class="flex_column_start_center item"
              v-for="(item, index) in bill_progress_data"
              :key="index"
            >
              <div class="top flex_row_center_center">
                <span class="left_line"></span>
                <img
                  :src="item.img"
                  alt=""
                />
                <span class="right_line"></span>
              </div>
              <span class="state">{{ item.state }}</span>
              <span class="time">{{ item.time }}</span>
            </div>
          </div>
          <div class="state_part flex_column_start_center" v-if="bill_detail_data.state == 1">
            <span class="title">{{ L('等待店铺确认') }}</span>
            <Popconfirm @confirm="checkConfirm" :title="L('我已确认核对该账单，确认无误')">
              <div class="invoice_btn">{{ L('确认结算单') }}</div>
            </Popconfirm>
          </div>
          <div class="state_part flex_column_start_center" v-if="bill_detail_data.state == 2">
            <span class="title">{{ L('等待平台审核') }}</span>
          </div>
          <div class="state_part flex_column_start_center" v-if="bill_detail_data.state == 3">
            <span class="title">{{ L('等待平台结算') }}</span>
          </div>
          <div class="state_part flex_column_start_center" v-if="bill_detail_data.state == 4">
            <span class="title">{{ L('结算完成') }}</span>
          </div>
          <!-- 结算信息 start -->
          <div class="sld_common_title">{{ L('结算信息') }}</div>
          <div class="detail_total_part flex_row_start_center">
            <img src="@/assets/images/bill/detail_total_icon.png" alt="" />
            <span class="amount_total"
              >&nbsp;{{ L('结算金额') }}{{ L('￥') }}{{ bill_detail_data.settleAmount }} = &nbsp;</span
            >
            <span class="amount_detail"
              >{{ L('现金使用金额') }}{{ L('￥') }}{{ bill_detail_data.cashAmount }} + {{ L('积分抵扣金额') }}{{ L('￥') }}{{
                bill_detail_data.integralCashAmount
              }}</span
            >
          </div>
          <!-- 结算信息 end -->
          <!-- 结算信息 start -->
          <div class="sld_common_title">{{ L('结算信息') }}</div>
          <Description
            size="middle"
            :useCollapse="true"
            :bordered="true"
            :column="2"
            :data="bill_detail_data"
            :schema="bill_base_data"
          />
          <!-- 结算信息 end -->
          <!-- 结算凭证 start -->
          <div class="sld_common_title" v-if="bill_detail_data.state == 4">{{ L('结算凭证') }}</div>
          <Description
            v-if="bill_detail_data.state == 4"
            size="middle"
            class="upload_style"
            :useCollapse="true"
            :bordered="true"
            :column="2"
            :data="bill_detail_data"
            :schema="bill_settle_img"
          />
          <!-- 结算凭证 end -->
          <!-- 结算订单信息 start -->
          <div class="sld_common_title">{{ L('结算订单信息') }}</div>
          <BasicTable
            :columns="columns_order"
            :bordered="true"
            :pagination="false"
            :dataSource="bill_detail_data.orderList"
            :maxHeight="400"
            :canResize="false"
            :actionColumn="{
              width: 80,
              title: L('操作'),
              dataIndex: 'action',
            }"
          >
            <template #bodyCell="{ column, record, text }">
              <template v-if="column.key == 'action'">
                <TableAction
                  :actions="[
                    {
                      onClick: lookDetail.bind(null, record),
                      label: L('查看'),
                    },
                  ]"
                />
              </template>
            </template>
          </BasicTable>
          <!-- 结算订单信息 end -->
        </div>
      </Spin>
    </div>
    <SldModal
      :width="modal_width"
      :title="title"
      :visible="modalVisible"
      :content="addData"
      :confirmBtnLoading="confirmBtnLoading"
      :showFoot="true"
      @cancle-event="handleCancle"
      @confirm-event="handleConfirm"
    />
  </div>
</template>
<script>
  export default {
    name: 'PointBillListToDetail',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, onMounted, h } from 'vue';
  import { Popconfirm, Spin, Modal } from 'ant-design-vue';
  import { sucTip, failTip } from '@/utils/utils';
  import SldModal from '@/components/SldModal/index.vue';
  import { BasicTable, TableAction } from '/@/components/Table';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { list_com_page_size_10 } from '/@/utils/utils';
  import {
    getIntegralBillDetailApi,
    getIntegralBillConfirmApi,
    getIntegralBillConfirmPaymentApi,
  } from '/@/api/point/bill';
  import { useRoute, useRouter } from 'vue-router';
  import { Description } from '/@/components/Description/index';
  import { useUserStore } from '/@/store/modules/user';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const route = useRoute();
  const router = useRouter();
  const userStore = useUserStore();

  const preview_img = ref('');
  const show_preview_modal = ref(false);
  const modal_width = ref(500); //modal框的宽度
  const show_foot = ref(true); //是否展示modal框的底部操作按钮
  const title = ref(''); //modal框title
  const submiting = ref(false);
  const loading = ref(false);
  const modalVisible = ref(false);
  const confirmBtnLoading = ref(false);
  const query = ref(route.query);
  const params = ref({ pageSize: list_com_page_size_10 });
  const settle_account = ref([]); //供应商信息
  const bill_base_data = ref([
    {
      field: 'billSn',
      label: L('结算单号'),
      labelMinWidth: 10,
      contentMinWidth: 100,
      render: (val, data) => {
        return val ? val : '--';
      },
    },
    {
      field: 'startTime',
      label: L('结算起止时间'),
      labelMinWidth: 10,
      contentMinWidth: 100,
      render: (val, data) => {
        return val + '~' + data.endTime;
      },
    },
    {
      field: 'storeName',
      label: L('店铺名称'),
      labelMinWidth: 10,
      contentMinWidth: 100,
      render: (val, data) => {
        return val ? val : '--';
      },
    },
    {
      field: 'contactName',
      label: L('店铺联系人'),
      labelMinWidth: 10,
      contentMinWidth: 100,
      render: (val, data) => {
        return val ? val : '--';
      },
    },
    {
      field: 'contactPhone',
      label: L('店铺联系电话'),
      labelMinWidth: 10,
      contentMinWidth: 100,
      render: (val, data) => {
        return val ? val : '--';
      },
    },
  ]); //结算信息
  const bill_detail_data = ref({}); //结算单详情数据
  const operateData = ref([]); //modal框数据
  const bill_progress_data = ref([
    {
      code: 'createBill',
      state: L('生成结算单'),
      time: '',
      state_code: 1,
      cur_state: 'cur',
      img:'',
    },
    {
      code: 'storeConfirm',
      state: L('店铺确认'),
      time: '',
      state_code: 2,
      img:'',
      cur_state: 'no',
    },
    {
      code: 'systemCheck',
      state: L('平台审核'),
      time: '',
      state_code: 3,
      img:'',
      cur_state: 'no',
    },
    {
      code: 'finish',
      state: L('结算完成'),
      time: '',
      state_code: 4,
      img:'',
      cur_state: 'no',
    },
  ]); //结算进度数据
  const bill_settle_img = ref([
    {
      field: 'paymentRemark',
      label: L('打款备注'),
      labelMinWidth: 10,
      contentMinWidth: 100,
      span: 2,
    },
    {
      field: 'paymentEvidence',
      label: L('结算凭证'),
      labelMinWidth: 10,
      contentMinWidth: 100,
      render: (val) => {
        return h('div', { class: 'form_list_img_item' }, [
          h('img', { src: val, class: 'form_list_img_item_img' }),
          h('div', { class: 'form_list_img_item_right' }, [
            h('div', { class: 'form_list_img_item_pre' }, [
              h('div', { class: 'form_list_img_item_pre_arrow' }),
              h('img', { src: val, class: 'form_list_img_item_pre_img' }),
            ]),
          ]),
        ]);
      },
    },
  ]); //结算凭证
  const addData = ref([
    {
      type: 'textarea',
      label: L('打款备注'), //textarea输入框
      name: 'paymentRemark',
      placeholder: L('请输入汇款单号、支付方式等付款凭证信息，最多输入200字'),
      maxLength: 200, //最多字数
      initialValue: '',
      disable: false, //是否禁用
      showCount: true, //是否展示字数
    },
    {
      type: 'upload_img_upload',
      label: L('打款凭证'), //图片上传 一张
      name: 'image',
      star: true,
      initialValue: [], //控件里file-list 回显也用这个 数组格式
      upload_name: 'file', //upload_name
      upload_url: `/v3/oss/seller/upload?source=setting`, //接口
      limit: 20, //文件最大
      accept: ' .gif, .jpeg, .png, .jpg,', //文件格式
    },
  ]); //确认打款数据
  const columns_order = ref([
    {
      title: L('订单号'),
      dataIndex: 'orderSn',
      align: 'center',
      width: 120,
    },
    {
      title: L('使用积分'),
      dataIndex: 'integral',
      align: 'center',
      width: 100,
    },
    {
      title: L('积分抵扣金额(元)'),
      dataIndex: 'cashAmount',
      align: 'center',
      width: 100,
    },
    {
      title: L('支付现金(元)'),
      dataIndex: 'cashAmount',
      align: 'center',
      width: 100,
    },
    {
      title: L('兑换日期'),
      dataIndex: 'createTime',
      align: 'center',
      width: 120,
    },
    {
      title: L('完成日期'),
      dataIndex: 'finishTime',
      align: 'center',
      width: 120,
    },
  ]);

  //获取结算详情
  const get_bill_detail = async (id) => {
    loading.value = true;
    let res = await getIntegralBillDetailApi({ billId: id });
    if (res.state == 200) {
      bill_detail_data.value = res.data;
      loading.value = false;
      //结算账户信息 账户类型1-银行账号；2-支付宝账号
      settle_account.value = [];
      let settleAccount = [];
      if (bill_detail_data.value.accountType == 1) {
        settleAccount = [
          {
            label: L('银行开户名'),
            labelMinWidth: 10,
            contentMinWidth: 100,
            code: 'bankAccountName',
          },
          {
            label: L('公司银行账号'),
            labelMinWidth: 10,
            contentMinWidth: 100,
            code: 'bankAccountNumber',
          },
          {
            label: L('开户银行'),
            labelMinWidth: 10,
            contentMinWidth: 100,
            code: 'bankBranch',
          },
          {
            label: L('开户行所在地'),
            labelMinWidth: 10,
            contentMinWidth: 100,
            code: 'addressAll',
          },
        ];
      } else {
        settleAccount = [
          {
            label: L('支付宝账号'),
            labelMinWidth: 10,
            contentMinWidth: 100,
            code: 'alipayAccount',
          },
          {
            label: L('支付宝姓名'),
            labelMinWidth: 10,
            contentMinWidth: 100,
            code: 'alipayName',
          },
        ];
      }
      settleAccount.map((item) => {
        settle_account.value.push({
          field: `${item.code}`,
          label: `${item.label}`,
          labelMinWidth: 10,
          contentMinWidth: 100,
          render: (val, data) => {
            return val ? val : '--';
          },
        });
      });
      for (let pro in bill_progress_data.value) {
        if (pro < bill_detail_data.value.logList.length) {
          bill_progress_data.value[pro].time = bill_detail_data.value.logList[pro].createTime;
        }
        if (bill_detail_data.value.state < bill_progress_data.value[pro].state_code) {
          bill_progress_data.value[pro].cur_state = 'no';
          if(bill_progress_data.value[pro].state_code==1){
            bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/1_no.png', import.meta.url).href;
          }else if(bill_progress_data.value[pro].state_code==2){
            bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/2_no.png', import.meta.url).href;
          }else if(bill_progress_data.value[pro].state_code==3){
            bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/3_no.png', import.meta.url).href;
          }else if(bill_progress_data.value[pro].state_code==4){
            bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/4_no.png', import.meta.url).href;
          }
        } else if (bill_detail_data.value.state == bill_progress_data.value[pro].state_code) {
          bill_progress_data.value[pro].cur_state = 'cur';
          if(bill_progress_data.value[pro].state_code==1){
            bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/1_cur.png', import.meta.url).href;
          }else if(bill_progress_data.value[pro].state_code==2){
            bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/2_cur.png', import.meta.url).href;
          }else if(bill_progress_data.value[pro].state_code==3){
            bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/3_cur.png', import.meta.url).href;
          }else if(bill_progress_data.value[pro].state_code==4){
            bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/4_cur.png', import.meta.url).href;
          }
        } else {
          bill_progress_data.value[pro].cur_state = 'pass';
          if(bill_progress_data.value[pro].state_code==1){
            bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/1_pass.png', import.meta.url).href;
          }else if(bill_progress_data.value[pro].state_code==2){
            bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/2_pass.png', import.meta.url).href;
          }else if(bill_progress_data.value[pro].state_code==3){
            bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/3_pass.png', import.meta.url).href;
          }else if(bill_progress_data.value[pro].state_code==4){
            bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/4_pass.png', import.meta.url).href;
          }
        }
      }
    } else {
      failTip(res.msg);
    }
  };

  // 生成本地路径
  const newImg = (img) => {
    return new URL(img, import.meta.url).href;
  };

  //审核结算单操作
  const checkConfirm = async () => {
    let res = await getIntegralBillConfirmApi({ billId: route.query.id });
    if (res.state == 200) {
      sucTip(res.msg);
      userStore.setDelKeepAlive(['PointBillList'])
      get_bill_detail(route.query.id);
    } else if (res.state == 255) {
      failTip(res.msg);
    } else if (res.state == 267) {
      Modal.confirm({
        title: L('温馨提示'),
        content: res.msg,
        onOk() {
          router.push({
            path: '/bill/account',
          });
        },
        onCancel() {
        },
        class: 'test',
      });
    }
  };

  //确认打款
  const pay = () => {
    for (let i in addData.value) {
      if (addData.value[i].type == 'upload_img_upload') {
        addData.value[i].initialValue = [];
      } else {
        addData.value[i].initialValue = undefined;
      }
    }
    title.value = L('确认打款');
    modalVisible.value = true;
  };

  // 关闭弹窗
  const handleCancle = () => {
    modalVisible.value = false;
  };

  // 弹窗确认
  const handleConfirm = async (val) => {
    let param = {};
    param.billId = route.query.id;
    param.paymentRemark = val.paymentRemark;
    if (
      val.image.length == 0 ||
      !val.image[0].response ||
      val.image[0].response.data.path == undefined
    ) {
      param.paymentEvidence = '';
      failTip(L('请上传打款凭证～'));
      return false;
    } else {
      if (val.image[0].response) {
        param.paymentEvidence = val.image[0].response.data.path;
      }
    }
    confirmBtnLoading.value = true;
    let res = await getIntegralBillConfirmPaymentApi(param);
    if (res.state == 200) {
      sucTip(res.msg);
      modalVisible.value = false;
      confirmBtnLoading.value = false;
      userStore.setDelKeepAlive(['PointBillList'])
      get_bill_detail(route.query.id);
    } else {
      confirmBtnLoading.value = false;
      failTip(res.msg);
    }
  };

  // 查看订单详情
  const lookDetail = (record) => {
    router.push({
      path: '/point/order_list_to_detail',
      query: {
        orderSn: record.orderSn,
      },
    });
  };

  onMounted(() => {
    get_bill_detail(route.query.id);
  });
</script>
<style lang="less">
  @import './style/detail.less';
</style>
