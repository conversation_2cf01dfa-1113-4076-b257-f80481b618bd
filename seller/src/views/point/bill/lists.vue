<template>
  <div class="section_padding goods_bill_lists">
    <div class="section_padding_back">
      <SldComHeader
        :type="2"
        :clickFlag="true"
        :title="L('结算账单管理')"
        :tipTitle="L('温馨提示')"
        :tipData="[
          L('计算公式：结算金额 = 现金使用金额 + 积分抵扣金额'),
          L(' 结算流程：生成结算单 > 店铺确认 > 平台审核 > 结算完成'),
        ]"
        @handle-toggle-tip="handleToggleTip"
      />
      <BasicTable @register="registerTable" style="padding: 0">
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex == 'startTime'">
            <div>
              <div>{{ text }}</div>
              <div>~</div>
              <div>{{ record.endTime }}</div>
            </div>
          </template>
          <template v-if="column.dataIndex == 'action'">
            <TableAction
              :actions="[
                {
                  onClick: lookDetail.bind(null, record),
                  label: L('查看'),
                },
              ]"
            />
          </template>
        </template>
      </BasicTable>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'PointBillList',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, onMounted } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { useRouter } from 'vue-router';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { getIntegralBillListApi } from '/@/api/point/bill';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const search_form_schema = ref([
    {
      field: 'billSn',
      component: 'Input',
      componentProps: {
        minWidth: 300,
        placeholder: L('请输入结算单号'),
        size: 'default',
      },
      label: L('结算单号'),
    },
    {
      field: 'state',
      component: 'Select',
      componentProps: {
        placeholder: L('请选择结算状态'),
        minWidth: 300,
        size: 'default',
        options: [
          { label: L('全部'), value: '' },
          { label: L('待确认'), value: 1 },
          { label: L('待审核'), value: 2 },
          { label: L('待结算'), value: 3 },
          { label: L('结算完成'), value: 4 },
        ],
      },
      label: L('结算状态'),
    },
    {
      field: '[startTime,endTime]',
      component: 'RangePicker',
      componentProps: {
        format: 'YYYY-MM-DD',
        placeholder: [L('开始时间'), L('结束时间')],
      },
      label: `结算时间`,
    },
  ]);

  const selectedRowKeys = ref([]);
  const selectedRows = ref([]);
  const router = useRouter();

  const columns = ref([
    {
      title: L('结算单号'),
      dataIndex: 'billSn',
      width: 100,
      customRender: ({ text, record }) => {
        return text ? text : '--';
      },
    },
    {
      title: L('出账时间'),
      dataIndex: 'createTime',
      width: 130,
    },
    {
      title: L('本期应收(元)'),
      dataIndex: 'settleAmount',
      width: 130,
    },
    {
      title: L('结算日'),
      dataIndex: 'startTime',
      width: 150,
    },
    {
      title: L('结算状态'),
      dataIndex: 'stateValue',
      width: 100,
    },
  ]);

  const [registerTable, { redoHeight, getForm }] = useTable({
    rowKey: 'billSn',
    ellipsis: false,
    api: (arg) => getIntegralBillListApi({ ...arg }),
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    actionColumn: {
      width: 80,
      title: L('操作'),
      dataIndex: 'action',
    },
    columns: columns,
    useSearchForm: true,
    formConfig: {
      schemas: search_form_schema,
    },
    beforeFetch(values) {
      values.startTime = values.startTime
        ? values.startTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
      return values;
    },
    bordered: true,
    striped: false,
  });

  // 更新表格高度
  const handleToggleTip = () => {
    redoHeight();
  };

  const lookDetail = (record) => {
    router.push({
      path: '/point/bill_list_to_detail',
      query: {
        id: record.billId,
      },
    });
  };
  onMounted(() => {});
</script>
<style lang="less">
  .goods_bill_lists {
    .goods_list_online_rightInfo {
      display: flex;
      flex: 1;
      flex-direction: column;
      align-items: flex-start;
      justify-content: space-between;
      margin-left: 10px;
      padding: 1px 0;

      .goods_list_goodsname {
        display: -webkit-box;
        flex-shrink: 0;
        height: 34px;
        overflow: hidden;
        color: #333;
        font-size: 14px;
        line-height: 17px;
        text-align: left;
        text-overflow: ellipsis;
        word-break: break-word;
        white-space: normal;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .goods_list_extraninfo {
        color: #666;
        font-size: 12px;
        text-align: left;

        div {
          display: -webkit-box;
          flex-shrink: 0;
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: break-word;
          white-space: normal;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }
      }
    }
  }
</style>
