<template>
  <div class="section_padding goods_import goods_import_box">
    <div class="section_padding_back">
      <div class="goods_import_title flex_row_start_start">
        {{ L('商品导入') }}
      </div>
      <div class="flex_row_start_start goods_import_con" style="flex-wrap: wrap;">
        <div class="item flex_row_start_center" v-for="(item,index) in data" :key="index" style="margin-bottom: 20px;" :style="{width:'48%',marginLeft:index%2==0?'0px':'30px'}" @click="toImport(item.path)">
          <img class="icon" :src="item.icon" alt="">
          <div class="right_con flex_column_between_start">
            <div class="flex_column_start_start top">
              <span class="title">
                {{ item.title }}
                <img src="@/assets/images/must_extra.png" alt="" v-if="item.extra&&specialFlag > -3" >
              </span>
              <span class="desc">{{ item.desc }}</span>
            </div>
            <a class="import_btn">{{ L('立即导入') }}</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'GoodsImport',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, onMounted,computed } from 'vue';
  import platform_goods_icon from '/src/assets/images/goods/platform_goods_icon.png';
  import excel_import_icon from '/src/assets/images/goods/excel_import_icon.png';
  import vop_goods_icon from '/src/assets/images/goods/vop_goods_icon.png';
  import supplier_goods_icon from '/src/assets/images/goods/supplier_goods_icon.png';
  import { useRouter } from 'vue-router';
  import { useUserStore } from '/@/store/modules/user';
  import { specialFlag } from '/@/utils/utils';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const userStore = useUserStore();

  const getUserInfo = computed(() => {
    return userStore.getStoreInfo;
  });

  const router = useRouter();
  const data = ref([
    {
      icon: platform_goods_icon,
      title: L('商品资料库导入'),
      desc: L('商品资料库是由平台运营人员维护的商品数据库，店铺可挑选计划售卖的商品一键导入进行售卖。'),
      path: '/goods/goods_import_to_platform',
    },
    {
      icon: excel_import_icon,
      title: L('Excel导入'),
      desc: L('通过Excel文件批量导入商品数据。'),
      path: '/goods/goods_import_to_excel',
    },
    // dev_supplier-start
    {
      key: 'supplier',
      icon: supplier_goods_icon,
      title: L('供应链产品库'),
      desc: L('供应链产品库汇集有众多优质供应商货源，商家可一键上架商品进行售卖。商家无需发货，供应商一键代发，解决供应链和仓储物流痛点。'),
      extra:true,
      path: '/goods/goods_import_to_supplier',
    },
    // dev_supplier-end
  ]);
  // 查看详情
  const toImport = (path)=> {
    router.push(path)
  }

  onMounted(() => {
    // dev_supplier-start
    if(getUserInfo.value.shopType&&getUserInfo.value.shopType=='3'){
      data.value = [
        {
          icon: excel_import_icon,
          title: L('Excel导入'),
          desc: L('通过Excel文件批量导入商品数据。'),
          path: '/goods/goods_import_to_excel',
        }
      ]
      return
    }
    // dev_supplier-end
    
    });
</script>
<style lang="less">
@import './style/product.less';
.goods_import_box{
  .right_con{
    .title{
      position: relative;
      img{
        width: 43px;
        height: 22px;
        position: absolute;
        top: -2px;
        right: -46px;
      }
    }
  }
}
</style>
