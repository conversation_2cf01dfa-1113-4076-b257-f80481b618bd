<template>
  <div class="section_padding related_template">
    <div class="section_padding_back">
      <SldComHeader type="1" :title="L('关联版式')" />
      <BasicTable @register="standardTable" style="padding: 0">
        <template #tableTitle>
          <div class="toolbar">
            <div class="toolbar_btn" @click="handleClick(null, 'add')">
              <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#ff5908" />
              <span>{{ L('新增关联版式') }}</span>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex == 'action'">
            <TableAction
              class="TableAction"
              :actions="[
              {
                label: L('编辑'),
                onClick: handleClick.bind(null, record, 'edit'),
              },
              {
                label: L('删除'),
                popConfirm: {
                  title: L('删除后不可恢复，是否确定删除？'),
                  placement: 'left',
                  confirm: handleClick.bind(null, record, 'del'),
                },
              },
              ]"
            />
          </template>
          <template v-else-if="column.dataIndex == 'templatePosition'">
            {{ text == 1 ? L('顶部') : L('底部') }}
          </template>
          <template v-else-if="column.dataIndex">
            {{ text !== undefined && text !== null && text != '' ? text : '--' }}
          </template>
        </template>
      </BasicTable>
    </div>
  </div>
</template>
<script>
export default {
  name: 'StoreSetting',
};
</script>
<script setup>
import { getCurrentInstance, ref } from 'vue';
import { Tabs, TabPane } from 'ant-design-vue';
import SldComHeader from '/@/components/SldComHeader/index.vue';
import { Popconfirm } from 'ant-design-vue';
import { sucTip, failTip } from '@/utils/utils';
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import {
  getGoodsRelatedTemplateListApi,
  delGoodsRelatedTemplateApi,
} from '/@/api/goods/goods';
import { useGo } from '/@/hooks/web/usePage';

const go = useGo();
const vm = getCurrentInstance();
const L = vm?.appContext.config.globalProperties.$sldComLanguage;

const [standardTable, { reload }] = useTable({
  api: (arg) => getGoodsRelatedTemplateListApi({ ...arg }),
  fetchSetting: {
    pageField: 'current',
    sizeField: 'pageSize',
    listField: 'data.list',
    totalField: 'data.pagination.total',
  },
  ellipsis: false,
  columns: [
    {
      title: L('版式名称'),
      dataIndex: 'templateName',
      width: 200,
    },
    {
      title: L('版式位置'),
      dataIndex: 'templatePosition',
      width: 100,
    },
  ],
  actionColumn: {
    width: 100,
    title: L('操作'),
    dataIndex: 'action',
  },
  useSearchForm: true,
  formConfig: {
    schemas: [
      {
        field: 'templateName',
        component: 'Input',
        colProps: { span: 6, style: 'width:260px;max-width:100%;flex:none' },
        componentProps: {
          minWidth: 300,
          placeholder: L('请输入版式名称'),
          size: 'default',
        },
        label: L('版式名称'),
        labelWidth: 80,
      },
      {
        field: 'templatePosition',
        component: 'Select',
        colProps: { span: 6, style: 'width:260px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: L('请选择版式位置'),
          options: [
            { value: '', label: L('全部') },
            { value: '1', label: L('顶部') },
            { value: '2', label: L('底部') },
          ],
        },
        label: L('版式位置'),
        labelWidth: 80,
      },
    ],
  },
  bordered: true,
  striped: false,
  showIndexColumn: true,
});

const click_event = ref(false);

//表格点击回调事件
function handleClick (item, type) {
  if (click_event.value) return;
  let params = {};
  if (type == 'add') {
    go(`/goods/related_template_to_add`);
  } else if (type == 'edit') {
    go(`/goods/related_template_to_edit?id=${item.templateId}`);
  } else if (type == 'del') {
    params.templateIds = item.templateId;
    click_event.value = true;
    operate_role(params);
  }
}

// 删除操作
const operate_role = async (params) => {
  let res = await delGoodsRelatedTemplateApi(params)
  click_event.value = false;
  if (res.state == 200) {
    sucTip(res.msg)
    reload()
  } else {
    failTip(res.msg)
  }
}
</script>
<style lang="less">
.related_template {
  .tabs_nav {
    .ant-tabs-nav {
      margin-bottom: 0;
    }
  }
}
</style>
