<template>
  <div class="section_padding point_goods_list">
    <div class="section_padding_back section_padding_back_scroll">
      <SldComHeader
        back
        customBack
        @back-click="goBack"
        :title="L('商品资料库导入')"

      />
      <div class="com_line" style="height: 1px;"></div>
      
      <Spin :spinning="initLoading">
        <div class="spin_height">
          <div class="tableListForm">
            <BasicForm :tableFlag="true" :searchFlag="true" ref="formRef" submitOnReset @register="registerForm" class="basic-form">
            </BasicForm>
          </div>
          
          <div class="flex_row_start_start import_store_goods">
            <div class="flex_column_start_start left_part">
              <p class="title flex_row_start_center">{{ L('商品分类') }}</p>
              <div class="SldScrollbars">
                <div class="goods_cat" @mouseenter="isShowMoreCat(true)">
                  <div v-for="(item,index) in goodsOneCat" :key="index" class="cat_item flex_row_between_center" :class="{selected_cat_item:curSelCatId[0] == item.categoryId}" @mouseenter="selCat(item.categoryId)" @mouseleave="isShowMoreCat(false)" @click="selCatSearch(item.categoryId)">
                    <span class="cat_name">{{ item.categoryName }}</span>
                    <div class="to_right_icon">
                      <AliSvgIcon iconName="icongengduo2" width="14px" height="14px" :fillColor="curSelCatId[0] == item.categoryId ? '#FF701E' : '#101010'" />
                    </div>
                  </div>
                </div>
              </div>
              <div class="more_cat flex_column_start_start" v-if="showMoreCat" @mouseenter="isShowMoreCat(true)" @mouseleave="isShowMoreCat(false)">
                <template v-if="goodsOneToAllCat.length>0">
                  <div v-if="submiting" class="more_cat_spin">
                    <Spin :spinning="submiting">
                    </Spin>
                  </div>
                  <div class="SldScrollbars more_cat_box">
                    <div class="item flex_row_start_start" v-for="(second_item, second_index) in goodsOneToAllCat" :key="'2_' + second_index">
                      <div class="second_cat flex_row_end_center" @click="selCatSearch(second_item.categoryId, 2)">
                        <span class="cat_name" :style="{color: curSelCatId[1] == second_item.categoryId ? '#FF701E' : '#4C4C4C'}">{{ second_item.categoryName }}</span>
                        <div class="to_right_icon">
                          <AliSvgIcon iconName="icongengduo2" width="14px" height="14px" :fillColor="curSelCatId[1] == second_item.categoryId ? '#FF701E' : '#101010'" />
                        </div>
                      </div>
                      <div class="flex_row_start_start third_cat">
                        <template v-if="second_item.children.length > 0">
                          <a v-for="(third_item, third_index) in second_item.children" :key="'3_' + third_index" class="item" :style="{color: curSelCatId[2] == third_item.categoryId ? '#FF701E' : '#999' }" @click="selCatSearch(third_item.categoryId, 3, third_item.pid)">
                            {{ third_item.categoryName }}
                          </a>
                        </template>
                      </div>
                    </div>
                  </div>
                </template>
                <template v-else>
                  <div class="flex_row_center_center" style="width: 100%;flex:1;">
                    <Empty :image="moudle_disable" :image-style="{ height: '80px' }" :description="L('暂无下级分类')"/>
                  </div>
                </template>
              </div>
            </div>
            <div class="right_goods flex_row_start_start" style="height: 100%;">
              <template v-if="data.list != undefined && data.list.length != undefined && data.list.length > 0">
                <div class="SldScrollbars" id="SldScrollbars_right" @scroll="handleScrollLeft">
                  <div class="right_goods flex_row_start_start">
                    <div class="flex_column_start_start item item_width" v-for="(item,index) in data.list" :key="index" style="margin-left: 5px;margin-bottom: 5px;" :id="index==0?'platform_0':''" @click="goDetail(item.platformGoodsId)">
                      <span class="virtual_goods_flag" v-if="item.isVirtualGoods == 2">{{ L('虚拟') }}</span>
                      <div class="flex_row_center_center img_wrap item_width_warp" >
                        <!-- :style="platform_goods_img_height" -->
                        <img :src="item.mainImage" alt="" style="max-width: 100%;max-height: 100%;">
                      </div>
                      <p class="goods_name" style="word-break: break-all;width: 100%;" :title="item.goodsName">{{ item.isVirtualGoods == 2 ? L('【虚拟】') : '' }}{{ item.goodsName }}</p>
                      <span class="price">¥{{ item.goodsPrice }}</span>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </div>
      </Spin>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'PlatformGoodsImport',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, onMounted, computed,watch } from 'vue';
  import { Tabs, TabPane,Spin,Empty } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { sucTip, failTip } from '@/utils/utils';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import { list_com_page_size_20 } from '/@/utils/utils';
  import moudle_disable from '/src/assets/images/moudle_disable.png';
  import { useRouter,useRoute } from 'vue-router';
  import {
    getPlatformGoodsListsApi,
    getGoodsCategoryListsApi,
    getGoodsCategoryBottomCateGoryApi
  } from '/@/api/goods/import';
  import { pageClose } from '/@/utils/utils';
  import { useMultipleTabStore } from '/@/store/modules/multipleTab';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;
  const router = useRouter();
  const route = useRoute();


  const { getRealWidth } = useMenuSetting();
  const tabStore = useMultipleTabStore();
  const initLoading = ref(false)
  const showMoreCat = ref(false)//是否展示二三级分类的标识，默认不显示
  const submiting = ref(false)
  const goodsOneCat = ref([])//一级分类数据
  const formRefs = ref();
  const data = ref({});//列表数据
  const title = ref(L('商品规格'));
  const pageSize = ref(list_com_page_size_20);
  const params = ref({pageSize:list_com_page_size_20});//搜索条件
  const formValues = ref({});//搜索条件
  const loading_pagination_flag = ref(false)//分页加载标识，防止分页重复加载
  const curSelCatId = ref([])//当前选中的一、二、三级分类id
  const goodsOneToAllCat = ref([])//一级分类下的所有二三级数据
  const screenW = ref(document.body.clientWidth);//屏幕宽度
  const platform_goods_img_height = ref({});
  //表单
  const [registerForm, { validate, getFieldsValue }] = useForm({
    schemas: [
      {
        field: 'goodsName',
        component: 'Input',
        componentProps: {
          minWidth: 300,
          placeholder: L('请输入商品名称'),
          size: 'default',
        },
        label: L('商品名称'),
      },
      {
        field: 'brandName',
        component: 'Input',
        componentProps: {
          minWidth: 300,
          placeholder: L('请输入商品品牌'),
          size: 'default',
        },
        label: L('商品品牌'),
      },
      {
        component: 'Select',
        label: L('商品类型'),
        field: 'isVirtualGoods',
        componentProps: {
          placeholder: L('请选择商品类型'),
          options: [
            { value: '', label: L('全部') },
            { value: '1', label: L('实物商品') },
            { value: '2', label: L('虚拟商品') },
          ],
        },
      },
    ],
    fieldMapToTime: [['fieldTime', ['startTime', 'endTime'], 'YYYY-MM-DD']],
    labelWidth: 80,
    baseColProps: { span: 6 },
    actionColOptions: { span: 24 },
    autoSubmitOnEnter: true,
    showAdvancedButton: true,
    submitFunc: search,
    resetFunc: reset,
  });

  // 搜索
  async function search() {
    await validate();
    let values = getFieldsValue();
   if(selCatSearchId.value){
    values.categoryId = selCatSearchId.value
   }
    for (let i in values) {
      if (values[i] == undefined) {
        delete values[i];
      }
    }
    formValues.value = values;
    if(document.getElementById('SldScrollbars_right')){
      document.getElementById('SldScrollbars_right')?.scrollTo(0,0)
    }
    get_list({ ...params.value, ...formValues.value });
  }

  // 重置
  async function reset() {
    selCatSearchId.value = 0
    if(document.getElementById('SldScrollbars_right')){
      document.getElementById('SldScrollbars_right')?.scrollTo(0,0)
    }
    curSelCatId.value = [0,0,0]
    formValues.value = {}
    params.value.current = 1;
  }

  //是否显示更多分类事件
  const isShowMoreCat = (flag) => {
    showMoreCat.value = flag
   
  };

  const realWidth = computed(() => {
    return getRealWidth.value;
  });

  const goods_item_width = ref(0)

   //查看资料详情
  const goDetail = (id)=> {
    window.open('/goods/goods_import_to_add?id=' + id, '_blank');
  }

   //返回上级页面
  const goBack = () => {
    if (window.history.length <= 1) {
      //没有上级路由，直接进入商品导入页面
      const { fullPath } = route;
      tabStore.closeTabByKey(fullPath, router);
      router.replace('/goods/goods_import');
    } else {
      if (window.history && window.history.length == 1) {
        pageClose();
      } else {
        const { fullPath } = route;
        tabStore.closeTabByKey(fullPath, router);
        router.back();
      }
    }
  };

  watch(
    realWidth,
    () => {
      screenW.value = document.body.clientWidth
      let goods_total_width = screenW.value - realWidth.value - 20 - 220 - 20;
      goods_item_width.value = (goods_total_width - 50) / 5;//每个商品的宽度
      if (document.getElementById('platform_0')) {
        platform_goods_img_height.value = { height: document.getElementById('platform_0')?.clientWidth + 'px' };
      }
    },
    { deep: true },
  );


  // 获取数据列表
  const get_list = async(params, flag)=> {
    initLoading.value = true
    let res = await getPlatformGoodsListsApi(params)
    if(res.state == 200){
      if (res.data.pagination != undefined) {
        if (res.data.pagination.current == 1) {
          data.value = res.data;
        } else {
          data.value.list = data.value.list.concat(res.data.list);
          data.value.pagination = res.data.pagination;
        }
      }
    }
    loading_pagination_flag.value = false
    initLoading.value = false
    if (flag) {
      setTimeout(() => {
        if (document.getElementById('platform_0')) {
          platform_goods_img_height.value = { height: document.getElementById('platform_0')?.clientWidth + 'px' };
        }
      }, 50)
    }
  }
  
  //获取一级分类
  const get_one_cat = async()=> {
    let res = await getGoodsCategoryListsApi({ grade: 1 })
    if(res.state == 200){
      goodsOneCat.value = res.data;
    }else{
      failTip(res.data.msg)
    }
  }

  const selCatId = ref(0)
  //选中一级分类事件
  const selCat = (id) => {
    showMoreCat.value = true
    //获取该一级分类下的所有分类
    selCatId.value = id
    getAllCat(id);
  };

  //根据一级分类id获取该一级分类下的所有分类
  const getAllCat = async(id)=> {
    submiting.value = true
    let res = await getGoodsCategoryBottomCateGoryApi({categoryId1:id})
    if(res.state == 200){
      submiting.value = false
      if(res.state == 200){
        goodsOneToAllCat.value = res.data
      }else{
        failTip(res.msg)
      }
    }
  }

  const selCatSearchId = ref(0)
  //选中分类进行搜索
  const selCatSearch = (id, grade = 1, second = 0)=> {
    get_list({ ...params.value, ...formValues.value, categoryId: id }, 0);
    selCatSearchId.value = id
    curSelCatId.value[0] = 0;
    [1, 2, 3].map((item, index) => {
      if (index == grade - 1) {
        curSelCatId.value[index] = id;
      } else if (index > grade - 1) {
        curSelCatId.value[index] = 0;
      }
      if(grade == 2){
        curSelCatId.value[0] = selCatId.value;
      }
      if (grade == 3) {
        curSelCatId.value[1] = second;
        curSelCatId.value[0] = selCatId.value;
      }
    });
    showMoreCat.value = false
    formValues.value = { ...formValues.value,categoryId: id }
  }

  const onResize = ()=> {
    screenW.value = document.body.clientWidth
    let goods_total_width = screenW.value - realWidth.value - 20 - 220 - 20;
    goods_item_width.value = (goods_total_width - 50) / 5;//每个商品的宽度
    if (document.getElementById('platform_0')) {
      platform_goods_img_height.value = { height: document.getElementById('platform_0')?.clientWidth + 'px' };
    }
  }

  const handleScrollLeft = (e)=> {
    //e.srcElement.scrollTop: 滚动条距离页面顶部距离
    //e.srcElement.clientHeight: 滚动页面高度
    //e.srcElement.scrollHeight: 滚动条高度
    // if (e.srcElement.scrollTop + e.srcElement.clientHeight > e.srcElement.scrollHeight - 10) {//-50距离底部50px是触发以下内容
    // }
    if (data.value.pagination.current * pageSize.value < data.value.pagination.total && !loading_pagination_flag.value) {
      //请求分页数据
      loading_pagination_flag.value = true;
      get_list({ pageSize: pageSize.value, current: data.value.pagination.current * 1 + 1 },0);
    }
  }

  onMounted(() => {
    get_list({ pageSize: pageSize.value }, 1)
    get_one_cat()
    // window.addEventListener('resize', onResize,true);
  });
</script>
<style lang="less">
@import '/@/assets/css/goods.less';
  .point_goods_list {
    .tabs_nav {
      .ant-tabs-nav {
        margin-bottom: 0;
      }
    }
  }
</style>
