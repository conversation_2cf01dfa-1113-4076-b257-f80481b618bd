<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader
        title="代销商品变更"
      />
      <BasicTable @register="standardTable" style="padding: 0">
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex == 'action'">
            <span class="common_page_edit" @click="goodsDetail(record)">查看详情</span>
          </template>
          <template v-else-if="column.dataIndex == 'goodsImage'">
            <div class="goods_list_mainIamge">
              <Popover placement="right">
                <template #content>
                  <div class="goods_list_mainIamge_pop">
                    <img :src="text" />
                  </div>
                </template>
                <div
                  class="goods_list_leftImage"
                  :style="{ backgroundImage: `url('${text}')` }"
                ></div>
              </Popover>
              <div class="goods_list_rightInfo">
                <div class="goods_list_goodsname">{{ record.goodsName }}</div>
                <div class="goods_list_extraninfo">
                  <span v-if="record.categoryPath">{{ record.categoryPath }}</span>
                </div>
              </div>
            </div>
          </template>
          <template v-else-if="column.dataIndex == 'changeContent'">
            <div v-if="!record.changeArr||record.changeArr.length==0">
              --
            </div>
            <div v-if="record.changeArr&&record.changeArr.length>2">
                <Tooltip placement="bottom">
                <template #title>
                    <div v-for="(item,ind) in record.changeArr" :key="ind" >{{ item }}</div>
                </template>
                <div style="margin-left: 5px;margin-top: 3px;text-align: left;">
                    <div>{{record.changeArr[0]}}</div>
                    <div>
                        {{record.changeArr[1]}}
                        <AliSvgIcon style="position: relative;top: 3px;left:2px" fillColor="primaryColor" width="15px" height="15px" iconName="iconxiaji"></AliSvgIcon>
                    </div>
                </div>
                </Tooltip>
            </div>
            <div v-else style="text-align: left;">
                <div v-for="(item,ind) in record.changeArr" :key="ind" >{{ item }}</div>
            </div>
        </template>
          <template v-else-if="column.dataIndex">
            {{ (text !== undefined && text !== null && text !== '') ? text : '--' }}
          </template>
        </template>
      </BasicTable>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'GoodsChangedNotice',
  };
</script>
<script setup>
  import { Popover,Tooltip } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import { getPushGoodsMsgList } from '/@/api/manage/manage';
  import { useRouter } from 'vue-router';

  const router = useRouter();
  const [standardTable] = useTable({
    api: (arg) => getPushGoodsMsgList({ ...arg }),
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    columns: [
      {
        title: '商品信息',
        dataIndex: 'goodsImage',
        width: 280,
        align: 'left',
      },
      {
        title: '商品来源',
        dataIndex: 'supplierName',
        width: 100,
      },
      {
        title: '商品编码',
        dataIndex: 'thirdProductId',
        width: 120,
      },
      {
        title: '变更内容',
        dataIndex: 'changeContent',
        width: 200,
      },
      {
        title: '变更时间',
        dataIndex: 'pushTime',
        width: 150,
      },
    ],
    actionColumn: {
      title: '操作',
      dataIndex: 'action',
      width: 100,
    },
    bordered: true,
    striped: false,
    ellipsis:false,
    showIndexColumn: true,
    afterFetch: (res) => {
      res.forEach(item=>{
        if(item.changeContent){
          item.changeArr = item.changeContent.split('\n')
        }else{
          item.changeArr = []
        }
      })
      return res
    },
  });

  //查看详情
  function goodsDetail(item) {
    router.push(`/goods/goods_changed_notice_detail?id=${item.goodsId}`);
  };
</script>
<style lang="less" scoped></style>
