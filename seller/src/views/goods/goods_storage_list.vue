<template>
    <div class="section_padding point_goods_list">
        <div class="section_padding_back">
            <SldComHeader :title="L('仓库中商品')" />
            <div class="goods_storage_lists">
                <BasicTable @register="registerTable" style="padding: 0">
                    <template #tableTitle>
                        <div class="toolbar">
                            <Popconfirm :title="L('确认上架选中的商品吗？')" @confirm="handleClick(null, 'upGoods')"
                                :disabled="selectedRowKeys.length == 0">
                                <div class="toolbar_btn"
                                    @click="() => selectedRowKeys.length == 0 ? handleClick(null, 'upGoods') : null">
                                    <AliSvgIcon iconName="iconziyuan25" width="15px" height="15px" fillColor="#39b55f" />
                                    <span>{{ L('上架') }}</span>
                                </div>
                            </Popconfirm>
                            <Popconfirm :title="L('确认删除选中的商品吗？')" @confirm="operateGoods(null, 'del')"
                                :disabled="selectedRowKeys.length == 0">
                                <div class="toolbar_btn"
                                    @click="() => selectedRowKeys.length == 0 ? operateGoods(null, 'del') : null">
                                    <AliSvgIcon iconName="iconpiliangshanchu" width="15px" height="15px" fillColor="#F21414" />
                                    <span>{{ L('删除') }}</span>
                                </div>
                            </Popconfirm>
                            <Popconfirm :title="L('确认将选中的商品设置为推荐商品吗？')" @confirm="goodsRecommend(null, 'recommend')"
                                :disabled="selectedRowKeys.length == 0">
                                <div class="toolbar_btn"
                                    @click="() => selectedRowKeys.length == 0 ? goodsRecommend(null, 'recommend') : null">
                                    <AliSvgIcon iconName="iconnav-tuijian" width="15px" height="15px" fillColor="#FFA70F" />
                                    <span>{{ L('设置推荐') }}</span>
                                </div>
                            </Popconfirm>
                            <Popconfirm :title="L('确认将选中的商品取消推荐吗？')" @confirm="goodsRecommend(null, 'cancelRecommend')"
                                :disabled="selectedRowKeys.length == 0">
                                <div class="toolbar_btn"
                                    @click="() => selectedRowKeys.length == 0 ? goodsRecommend(null, 'cancelRecommend') : null">
                                    <AliSvgIcon iconName="iconquxiaotuijian2" width="15px" height="15px" fillColor="#0F419C" />
                                    <span>{{ L('取消推荐') }}</span>
                                </div>
                            </Popconfirm>
                            <div class="toolbar_btn" @click="handleClick(null, 'setRelatedTemplate')">
                                <AliSvgIcon iconName="iconglsz" width="15px" height="15px" fillColor="#0C93F2" />
                                <span>{{ L('设置关联版式') }}</span>
                            </div>
                        </div>
                    </template>
                    <template #bodyCell="{ column, record, text }">
                        <template v-if="column.key == 'action'">
                            <div class="goods_list_action">
                                <span class="common_page_edit cursor-pointer hover:text-[#FF6A12]"
                                    @click="handleClick(record, 'look')">{{ L('查看规格') }}</span>
                                <span class="goods_edit cursor-pointer hover:text-[#FF6A12]"
                                    @click="handleClick(record, 'edit')">{{ L('编辑') }}</span>
                            </div>
                        </template>
                        <template v-else-if="column.key == 'mainImage'">
                            <div class="goods_list_mainIamge">
                                <div v-if="record.isVirtualGoods == 2" class="isVirtua_flag">虚拟</div> 
                                <Popover placement="right">
                                    <template #content>
                                        <div class="goods_list_mainIamge_pop">
                                            <img :src="text" />
                                        </div>
                                    </template>
                                    <div class="goods_list_leftImage" :style="{ backgroundImage: `url('${text}')` }"></div>
                                </Popover>
                                <div class="goods_list_online_rightInfo">
                                    <div class="goods_list_goodsname" :title="record.goodsName">{{ record.goodsName }}</div>
                                    <div class="goods_list_extraninfo">
                                        <div v-if="record.categoryPath" :title="record.categoryPath">{{ record.categoryPath
                                        }}</div>
                                        <!-- dev_supplier-start -->
                                        <div v-if="record.productCode&&getUserInfo.shopType&&getUserInfo.shopType=='3'">商品编码：{{ record.productCode }}</div>
                                        <!-- dev_supplier-end -->
                                    </div>
                                </div>
                            </div>
                        </template>
                        <!-- dev_supplier-start -->
                        <template v-else-if="column.key == 'wholesalePrice'">
                            <div v-if="record.isRequireInquire == 1 || !record.ladderPrice">
                                {{  text||text==0 ? '￥'+Number(text).toFixed(2) : '--' }}
                            </div>
                            <div v-else>
                                <template v-for="(item,index) in record.ladderPrice.split(',')" :key="index">
                                <div>¥{{ item.split('-')[1] ? Number(item.split('-')[1]).toFixed(2) : 0 }}(≥{{ item.split('-')[0] }})</div>
                                </template>
                            </div>
                        </template>
                        <!-- dev_supplier-end -->
                        <template v-else-if="column.key == 'innerLabelList'">
                            <div class="goods_list_innerLabelList" v-if="record.innerLabelList.length > 0">
                                <div class="goods_list_innerLabelItem" v-for="(item, index) in record.innerLabelList"
                                    :key="index">
                                    {{ item }}</div>
                            </div>
                            <div class="goods_list_innerLabelList" v-else>
                                <div class="goods_list_innerLabelItem">--</div>
                            </div>
                        </template>
                        <template v-else-if="column.key == 'storeIsRecommend'">
                            <div>{{ record.storeIsRecommend == 1 ? L('推荐') : L('不推荐') }}</div>
                        </template>
                        <template v-else-if="column.key">
                            {{ text !== undefined && text !== null ? text : '--' }}
                        </template>
                    </template>
                </BasicTable>
            </div>
        </div>
        <SldModal :width="width" :title="title" :visible="modalVisible" :content="content"
            :confirmBtnLoading="confirmBtnLoading" :showFoot="showFoot" :parentPage="'good_list'"
            @cancle-event="handleCancle" @confirm-event="handleConfirm" >
            <!-- dev_supplier-start -->
            <template #bodyCell="{ column, record, text }">
                <template v-if="column.dataIndex == 'wholesalePrice'">
                    <div v-if="record.isRequireInquire == 1 || !record.ladderPrice">
                        {{  text||text==0 ? '￥'+Number(text).toFixed(2) : '--' }}
                    </div>
                    <div v-else>
                        <template v-for="(item,index) in record.ladderPrice.split(',')" :key="index">
                        <div>¥{{ item.split('-')[1] ? Number(item.split('-')[1]).toFixed(2) : 0 }}(≥{{ item.split('-')[0] }})</div>
                        </template>
                    </div>
                </template>
            </template>
            <!-- dev_supplier-end -->
        </SldModal>
    </div>
</template>
<script>
  export default {
    name: 'GoodsStorageList',
  };
</script>
<script setup>
import SldModal from '@/components/SldModal/index.vue';
import { failTip, sucTip } from '@/utils/utils';
import { Popconfirm, Popover } from 'ant-design-vue';
import { computed, getCurrentInstance, onMounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { getGoodsDeleteGoodsApi, getGoodsIsRecommendApi, getGoodsListApi, getGoodsRelatedTemplateListApi, getStoreCategoryApi, goodsUpperShelf, setRelatedTemplate } from '/@/api/goods/goods';
import SldComHeader from '/@/components/SldComHeader/index.vue';
import { BasicTable, useTable } from '/@/components/Table';
import { useUserStore } from '/@/store/modules/user';
import { SelectAll, selectRadio } from '/@/utils/utils';

const vm = getCurrentInstance();
const L = vm?.appContext.config.globalProperties.$sldComLanguage;

const props = defineProps({
    canResize: { type: Boolean, default: true },
});

const canResize = computed(() => {
    return props.canResize;
});

const userStore = useUserStore();
const getUserInfo = computed(() => {
    return userStore.getStoreInfo;
});

watch(canResize, () => {
    redoHeight();
});

const content_columns = ref([
    {
        title: ' ',
        align: 'center',
        width: 100,
        minWidth: 100,
        customRender: ({ text, record, index }) => {
            return `${index + 1}`;
        },
    },
    {
        title: L('商品规格'),
        dataIndex: 'specValues',
        align: 'center',
        width: 200,
        customRender: ({ text }) => {
            return text ? text : L('默认');
        },
        minWidth: 200,
    },
    {
        title: L('供货价'),
        dataIndex: 'supplyPrice',
        align: 'center',
        width: 100,
        minWidth: 100,
        customRender: ({ text, record }) => {
            return `${record.supplyPrice||record.supplyPrice=='0' ? '￥' + record.supplyPrice.toFixed(2) : '--'}`;
        },
    },
    {
        title: L('价格'),
        dataIndex: 'productPrice',
        align: 'center',
        width: 100,
        minWidth: 100,
        customRender: ({ text, record }) => {
            return `${record.productPrice ? '￥' + record.productPrice : '--'}`;
        },
    },
    {
        title: L('库存'),
        dataIndex: 'productStock',
        align: 'center',
        width: 100,
        minWidth: 100,
    },
    {
        title: L('货号'),
        dataIndex: 'productCode',
        align: 'center',
        width: 150,
        minWidth: 150,
        customRender: ({ text }) => {
            return text ? text : '--';
        },
    },
    {
        title: L('条形码'),
        dataIndex: 'barCode',
        align: 'center',
        width: 150,
        minWidth: 150,
        customRender: ({ text }) => {
            return text ? text : '--';
        },
    },
])

const search_form_schema = ref([
    {
        field: 'goodsName',
        component: 'Input',
        colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
        componentProps: {
            minWidth: 300,
            placeholder: L('请输入商品名称'),
            size: 'default',
        },
        label: L('商品名称'),
        labelWidth: 80,
    },
    {
        field: 'goodsCode',
        component: 'Input',
        colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
        componentProps: {
            minWidth: 300,
            placeholder: L('请输入商品货号'),
            size: 'default',
        },
        label: L('商品货号'),
        labelWidth: 80,
    },
    {
        field: 'barCode',
        component: 'Input',
        colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
        componentProps: {
            minWidth: 300,
            placeholder: L('请输入商品条形码'),
            size: 'default',
        },
        label: L('条形码'),
        labelWidth: 80,
    },
    {
        field: 'storeCategoryId',
        component: 'TreeSelect',
        colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
        componentProps: {
            placeholder: L('请选择店铺分类'),
            minWidth: 300,
            size: 'default',
            treeData: [],

        },
        label: L('店铺分类'),
        labelWidth: 80,
    },
    {
        field: 'isVirtualGoods',
        component: 'Select',
        colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
        componentProps: {
            placeholder: L('请选择商品类型'),
            minWidth: 300,
            size: 'default',
            options: [
                { label: L('全部'), value: '' },
                { label: L('实物商品'), value: 1 },
                { label: L('虚拟商品'), value: 2 },
            ],
        },
        label: L('商品类型'),
        labelWidth: 80,
    },
    {
        field: '[startTime,endTime]',
        component: 'RangePicker',
        colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
        componentProps: {
            format: 'YYYY-MM-DD',
            placeholder: [L('开始时间'), L('结束时间')],
        },
        label: L('发布时间'),
        labelWidth: 80,
    },
]);

const columns = ref([
    {
        title: L('商品信息'),
        dataIndex: 'mainImage',
        width: 300,
    },
    {
        title: L('店铺分类'),
        dataIndex: 'innerLabelList',
        width: 150,
    },
     {
        title: L('供货价'),
        dataIndex: 'supplyPrice',
        width: 100,
        customRender: ({ text, record }) => {
            return `${record.supplyPrice ? '￥' + record.supplyPrice : '--'}`;
        },
    },
    {
        title: L('价格'),
        dataIndex: 'goodsPrice',
        width: 100,
        customRender: ({ text, record }) => {
            return `${record.goodsPrice ? '￥' + record.goodsPrice : '--'}`;
        },
    },
    {
        title: L('市场价'),
        dataIndex: 'marketPrice',
        width: 100,
        customRender: ({ text, record }) => {
            return `${record.marketPrice ? '￥' + record.marketPrice : '--'}`;
        },
    },
    {
        title: L('商品库存'),
        dataIndex: 'goodsStock',
        width: 100,
    },
    {
        title: L('实际/虚拟销量'),
        dataIndex: 'actualSales',
        width: 180,
        customRender: ({ text, record }) => {
            return `${record.actualSales}/${record.virtualSales}`;
        },
    },
    {
        title: L('是否推荐'),
        dataIndex: 'storeIsRecommend',
        width: 100,
    },
    {
        title: L('发布时间'),
        dataIndex: 'createTime',
        width: 180,
    },
]);

// dev_supplier-start
const sales_model = ref('')
if(getUserInfo.value.shopType&&getUserInfo.value.shopType==3){
    columns.value = [
        {
            title: L('商品信息'),
            dataIndex: 'mainImage',
            width: 300,
        },
        {
            title: L('店铺分类'),
            dataIndex: 'innerLabelList',
            width: 150,
        },
        {
            title: L('销售模式'),
            dataIndex: 'saleModel',
            width: 100,
            customRender: ({ text, record }) => {
                return text==3?'代发、批发':text==2?'批发':'代发';
            },
        },
        {
            title: L('批发价'),
            dataIndex: 'wholesalePrice',
            width: 100,
        },
        {
            title: L('供货价'),
            dataIndex: 'goodsPrice',
            width: 100,
            customRender: ({ text, record }) => {
                return `${record.goodsPrice ? '￥' + record.goodsPrice : '--'}`;
            },
        },
        {
            title: L('建议零售价'),
            dataIndex: 'retailPrice',
            width: 100,
            customRender: ({ text, record }) => {
                return `${record.retailPrice ? '￥' + record.retailPrice : '--'}`;
            },
        },
        {
            title: L('市场价'),
            dataIndex: 'marketPrice',
            width: 100,
            customRender: ({ text, record }) => {
                return `${record.marketPrice ? '￥' + record.marketPrice : '--'}`;
            },
        },
        {
            title: L('商品库存'),
            dataIndex: 'goodsStock',
            width: 100,
        },
        {
            title: L('实际/虚拟销量'),
            dataIndex: 'actualSales',
            width: 180,
            customRender: ({ text, record }) => {
                return `${record.actualSales}/${record.virtualSales}`;
            },
        },
        {
            title: L('是否推荐'),
            dataIndex: 'storeIsRecommend',
            width: 100,
        },
        {
            title: L('发布时间'),
            dataIndex: 'createTime',
            width: 180,
        },
    ]
    search_form_schema.value = search_form_schema.value.filter(items => items.field != 'saleModel');
    let sale_model ={
        field: 'saleModel',
        component: 'Select',
        colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
        componentProps: {
            placeholder: L('请选择销售模式'),
            minWidth: 300,
            size: 'default',
            options: [
                { label: L('全部'), value: '' },
                { label: L('代发'), value: 1 },
                { label: L('批发'), value: 2 },
                { label: L('代发、批发'), value: 3 },
            ],
        },
        label: L('销售模式'),
        labelWidth: 80,
    }
    
    let product_code = {
        field: 'productCode',
        component: 'Input',
        componentProps: {
            placeholder: L('请输入商品编码'),
            size: 'default',
        },
        label: L('商品编码'),
    }
    
    for (let i=0; i<search_form_schema.value.length; i++) {
        if (search_form_schema.value[i].field == 'goodsCode') {
            search_form_schema.value.splice(i, 0, product_code);
            break;
        }
    }
    for (let i=0; i<search_form_schema.value.length; i++) {
        if (search_form_schema.value[i].field == 'isVirtualGoods') {
            search_form_schema.value.splice(i+1, 0, sale_model);
            break;
        }
    }
}
// dev_supplier-end

const router = useRouter();
const selectedRowKeys = ref([]);
const selectedRows = ref([]);
const modalVisible = ref(false);
const confirmBtnLoading = ref(false);
const title = ref(L('设置关联版式'));
const content = ref([]);
const operate_type = ref('');
const operate_item = ref({});
const goodsRelatedTemplateList = ref([]);
const goodsRelatedTemplate_data = ref([
    {
        type: 'select',
        label: L('顶部版式'),
        name: 'topTemplateId',
        placeholder: L('请选择顶部关联版式'),
        width: 353,
        selData: [],
    },
    {
        type: 'select',
        label: L('底部版式'),
        name: 'bottomTemplateId',
        placeholder: L('请选择底部关联版式'),
        width: 353,
        selData: [],
    },
]);
const width = ref(500)
const showFoot = ref(true)

const [registerTable, { reload, redoHeight }] = useTable({
    rowKey: 'goodsId',
    api: (arg) => getGoodsListApi({ ...arg, state: 4 }),
    fetchSetting: {
        pageField: 'current',
        sizeField: 'pageSize',
        listField: 'data.list',
        totalField: 'data.pagination.total',
    },
    columns: columns,
    actionColumn: {
        width: 120,
        title: L('操作'),
        dataIndex: 'action',
    },
    useSearchForm: true,
    formConfig: {
        schemas: search_form_schema,
    },
    beforeFetch(values) {
        values.startTime = values.startTime
            ? values.startTime.split(' ')[0] + ' 00:00:00'
            : undefined;
        values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
        return values;
    },
    // bordered: true,
    striped: false,
    rowSelection: {
        type: 'checkbox',
        selectedRowKeys: selectedRowKeys,
        onSelect: onSelect,
        onSelectAll: onSelectAll,
        getCheckboxProps(record) {
            // 活动商品不允许修改
            if (record.isLock === 1) {
                return { disabled: true };
            } else {
                return { disabled: false };
            }
        },
    },
    clickToRowSelect: false,
});

// 获取店铺分类列表
const getStoreCategoryList = async () => {
    let res = await getStoreCategoryApi({ pageSize: 10000 });
    if (res.state == 200) {
        let TreeSelectData = []
        res.data.map(v => {
            let itemData = {
                label: v.innerLabelName,
                value: v.innerLabelId,
            }
            if (v.children && v.children.length > 0) {
                itemData.children = v.children.map(x => {
                    return {
                        label: x.innerLabelName,
                        value: x.innerLabelId,
                    }
                })
            }
            TreeSelectData.push(itemData)
        })
        search_form_schema.value.map(v => {
            if (v.field == 'storeCategoryId') {
                v.componentProps.treeData = JSON.parse(JSON.stringify(TreeSelectData))
            }
        })
    }
}

// 操作：del批量删除
const operateGoods = async (record, type) => {
    let res;
    if (type == 'del') {
        if (selectedRowKeys.value.length == 0) {
            failTip(L('请先选中数据'))
            return
        }
        res = await getGoodsDeleteGoodsApi({ goodsIds: selectedRowKeys.value.join(',') })
    }
    if (res.state == 200) {
        sucTip(res.msg)
        selectedRowKeys.value = []
        selectedRows.value = []
        reload()
    } else {
        failTip(res.msg)
    }
}

// 设置推荐或者取消推荐
const goodsRecommend = async (record, type) => {
    let res;
    if (selectedRowKeys.value.length == 0) {
        failTip(L('请先选中数据'))
        return
    }
    let isRecommend = type == 'recommend' ? 1 : 0
    res = await getGoodsIsRecommendApi({ goodsIds: selectedRowKeys.value.join(','), isRecommend })
    if (res.state == 200) {
        sucTip(res.msg)
        selectedRowKeys.value = []
        selectedRows.value = []
        reload()
    } else {
        failTip(res.msg)
    }
}

//表格点击回调事件
const handleClick = async (item, type) => {
    operate_type.value = type;
    operate_item.value = item ? item : null;
    if (type == 'upGoods') {
        if (selectedRowKeys.value.length == 0) {
            failTip(L('请先选中数据'));
            return;
        }
        let val = {}
        val.goodsIds = selectedRowKeys.value.join(',');
        let res = await goodsUpperShelf(val);
        if (res.state == 200) {
            sucTip(res.msg);
            modalVisible.value = false;
            confirmBtnLoading.value = false;
            selectedRows.value = [];
            selectedRowKeys.value = [];
            reload();
        } else {
            failTip(res.msg);
            confirmBtnLoading.value = false;
        }
        return
    } else if (type == 'look') {
        title.value = L('查看规格');
        width.value = 850
        showFoot.value = false
        // dev_supplier-start
        if(getUserInfo.value.shopType&&getUserInfo.value.shopType=='3'){
            content_columns.value = content_columns.value.filter(items => items.dataIndex != 'wholesalePrice');
            content_columns.value = content_columns.value.filter(items => items.dataIndex != 'productPrice');
            content_columns.value = content_columns.value.filter(items => items.dataIndex != 'supplyPrice');
            content_columns.value = content_columns.value.filter(items => items.dataIndex != 'retailPrice');
            let wholesale_price = {
                title: L('批发价'),
                align: 'center',
                dataIndex: 'wholesalePrice',
                width: 150,
                minWidth: 150,
            }
            let product_price = {
                title: L('供货价'),
                align: 'center',
                dataIndex: 'productPrice',
                width: 100,
                minWidth: 100,
                customRender: ({ text, record }) => {
                    return `${record.productPrice ? '￥' + record.productPrice : '--'}`;
                },
            }
            let retail_price = {
                title: L('建议零售价'),
                align: 'center',
                dataIndex: 'retailPrice',
                width: 150,
                minWidth: 150,
                customRender: ({ text, record }) => {
                    return `${record.retailPrice ? '￥' + record.retailPrice : '--'}`;
                },
            }
            for (let i=0; i<content_columns.value.length; i++) {
                if (content_columns.value[i].dataIndex == 'productStock') {
                    content_columns.value.splice(i, 0, wholesale_price,product_price,retail_price);
                    break;
                }
            }
        }
        // dev_supplier-end
        let obj = [
            {
                type: 'show_content_table', //展示表格，没有分页，不能勾选
                name: 'bind_goods',
                width: 880,
                wrapperCol: { flex: 1, style: 'margin-bottom:-19px' },
                label: ``,
                content: '',
                data: item.productList,
                scrollHeight: false, // 设置纵向滚动，也可用于指定滚动区域的高，可以设置为像素值
                columns: content_columns.value,
                rowKey: 'goodsId',
                scroll: false,
            }
        ]
        showFoot.value = false
        content.value = obj;
        modalVisible.value = true;

    } else if (type == 'setRelatedTemplate') {
        if (selectedRowKeys.value.length == 0) {
            failTip(L('请先选中数据'));
            return;
        }
        width.value = 500
        title.value = L('设置关联版式');
        content.value = JSON.parse(JSON.stringify(goodsRelatedTemplate_data.value));
        modalVisible.value = true;
    } else if (type == 'edit') {
         // dev_supplier-start
         if(getUserInfo.value.shopType&&getUserInfo.value.shopType=='3'){
            get_system_info(item)
            return
        }
        // dev_supplier-end
        userStore.setDelKeepAlive(['GoodsListToEdit'])
        router.push(`/goods/goods_list_to_edit?id=${item.goodsId}`);
    }
};

//弹窗取消事件
const handleCancle = () => {
    modalVisible.value = false;
    content.value = [];
    operate_item.value = {};
};

//获取关联样式列表
const getGoodsRelatedTemplateList = async () => {
    let res = await getGoodsRelatedTemplateListApi({ pageSize: 10000 });
    if (res.state == 200) {
        let topSelData = [];
        let bottomSelData = [];
        res.data.list.map((item) => {
            if (item.templatePosition == 1) {
                topSelData.push({
                    key: item.templateId,
                    name: item.templateName,
                });
            } else {
                bottomSelData.push({
                    key: item.templateId,
                    name: item.templateName,
                });
            }

        });
        goodsRelatedTemplateList.value = res.data;
        goodsRelatedTemplate_data.value.forEach((item) => {
            if (item.name == 'topTemplateId') {
                item.selData = JSON.parse(JSON.stringify(topSelData));
            }
            if (item.name == 'bottomTemplateId') {
                item.selData = JSON.parse(JSON.stringify(bottomSelData));
            }
        });
    }
};

// 单选多选事件
function onSelect(record, selected) {
    let rows = selectRadio(
        selectedRowKeys.value,
        selectedRows.value,
        'goodsId',
        record,
        selected,
    );
    selectedRowKeys.value = rows.selectedRowKeys;
    selectedRows.value = rows.selectedRows;
}

// 全选按钮事件
function onSelectAll(selected, rows, changeRows) {
    let rowAll = SelectAll(
        selectedRowKeys.value,
        selectedRows.value,
        'goodsId',
        selected,
        rows,
        changeRows,
    );
    selectedRowKeys.value = rowAll.selectedRowKeys;
    selectedRows.value = rowAll.selectedRows;
}

//弹窗确认事件
const handleConfirm = async (val) => {
    if (!val.topTemplateId && !val.bottomTemplateId) {
        failTip(L('请至少选择一个模版'));
        return
    }
    let params = {}
    params.goodsIds = selectedRowKeys.value.join(',');
    if (val.topTemplateId) {
        params.topTemplateId = val.topTemplateId
    }
    if (val.bottomTemplateId) {
        params.bottomTemplateId = val.bottomTemplateId
    }
    let res = await setRelatedTemplate(params);
    if (res.state == 200) {
        sucTip(res.msg);
        modalVisible.value = false;
        confirmBtnLoading.value = false;
        selectedRows.value = [];
        selectedRowKeys.value = [];
        reload();
    } else {
        failTip(res.msg);
        confirmBtnLoading.value = false;
    }
};

 // dev_supplier-start
 const get_system_info = async (item) => {
    const res = await getSettingListApi({ str: 'supplier_sales_model' });
    if (res.state == 200) {
      if(res.data.length>0){
        if(res.data[0].value != item.saleModel&&res.data[0].value!='3'){
            failTip(`${res.data[0].value==1?'批发':'代发'}的销售模式已关闭，所以该商品不支持编辑`)
            return
        }else{
            userStore.setDelKeepAlive(['GoodsListToEdit'])
            router.push(`/goods/goods_list_to_edit?id=${item.goodsId}`);
        }
        sales_model.value = res.data[0].value
      }
    }
  };
  // dev_supplier-end

onMounted(() => {
    getStoreCategoryList();
    getGoodsRelatedTemplateList();

});
</script>
<style lang="less">
.point_goods_list {
    .tabs_nav {
        .ant-tabs-nav {
            margin-bottom: 0;
        }
    }
}

.goods_storage_lists {
    .goods_list_action {
        display: flex;
        align-items: center;
        justify-content: center;

        .goods_lock {
            color: rgb(255, 255, 255);
            background: rgb(153, 153, 153);
            white-space: nowrap;
            display: inline-block;
            margin-top: 1px;
            margin-left: 5px;
            padding: 3px 5px 3px 6px;
            border-radius: 3px;
            cursor: pointer;
        }

        .goods_edit {
            margin-left: 5px;
            cursor: pointer;
        }
    }

    .goods_list_online_rightInfo {
        display: flex;
        flex: 1;
        flex-direction: column;
        align-items: flex-start;
        justify-content: space-between;
        margin-left: 10px;
        padding: 1px 0;

        .goods_list_goodsname {
            display: -webkit-box;
            flex-shrink: 0;
            height: 34px;
            overflow: hidden;
            color: #333;
            font-size: 14px;
            line-height: 17px;
            text-align: left;
            text-overflow: ellipsis;
            word-break: break-word;
            white-space: normal;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        .goods_list_extraninfo {
            color: #666;
            font-size: 12px;
            text-align: left;

            div {
                display: -webkit-box;
                flex-shrink: 0;
                overflow: hidden;
                text-overflow: ellipsis;
                word-break: break-word;
                white-space: normal;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
            }
        }
    }
}
</style>
