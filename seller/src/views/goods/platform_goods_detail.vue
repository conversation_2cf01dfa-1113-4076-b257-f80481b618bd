<template>
  <div class="section_padding platform_goods_detail">
    <div class="section_padding_back">
    <SldComHeader
      back
      customBack
      @back-click="goBack"
      title="商品资料导入"
    />
    <div class="com_line" style="height: 1px;"></div>
      <Spin :spinning="initLoading">
        <div class="flex_row_center_start platform_goods_detail platform_goods_detail_overflow">
          <div class="flex_column_start_start content">
            <div class="flex_row_start_start">
              <div class="flex_column_start_start top_left">
                <div class="flex_row_center_center main_img">
                  <img :src="bigImg"/>
                </div>
                <div class="small_img" :class="{flex_row_start_start:detail.imageList != undefined && detail.imageList.length <= 5 }">
                  <template v-if="detail!=undefined">
                    <template v-if="detail.imageList != undefined && detail.imageList.length > 5">
                      <Carousel :infinite="false" arrows :dots="false">
                        <template #prevArrow>
                          <div class="custom-slick-arrow first" style="left: 10px; z-index: 1">
                            <LeftCircleOutlined style="font-size: 20px;color: #a3a3a3;"/>
                          </div>
                        </template>
                        <template #nextArrow>
                          <div class="custom-slick-arrow next" style="right: 10px">
                            <RightCircleOutlined style="font-size: 20px;color: #a3a3a3;"/>
                          </div>
                        </template>
                        <div class="item_one flex_row_start_start" :key="index" v-for="(item,index) in detail.imageListCar">
                          <img :src="it" alt="" @mouseover="showCurImgCar(index,ind)" :key="ind" v-for="(it,ind) in item" style="margin-right: 10px;">
                        </div>
                      </Carousel>
                    </template>
                    <template v-else>
                      <div class="item flex_row_center_center" :key="index" v-for="(item,index) in detail.imageList" @mouseover="showCurImg(index)">
                        <img :src="item" alt="">
                      </div>
                    </template>
                  </template>
                </div>
              </div>
              <div class="flex_column_start_start top_right">
                <p class="name">{{ detail.goodsName }}</p>
                <p class="desc">{{ detail.goodsBrief }}</p>
                <div class="flex_column_start_start spec">
                  <div class="flex_row_start_center price" :style="{ backgroundImage: `url('${price_bg}')` }">
                    <span class="tip">价格：</span>
                    <div class="price_detail">
                      <span class="unit">¥</span>
                      <span class="number">{{detail.minPrice && Number(detail.minPrice).toFixed(2)}}</span>
                      <template v-if="detail.maxPrice">
                        <span class="from_to">～</span>
                        <span class="unit">¥</span>
                        <span class="number">{{Number(detail.maxPrice).toFixed(2)}}</span>
                      </template>
                    </div>
                  </div>
                  <div class="flex_row_start_start spec_detail">
                    <span class="tip">规格：</span>
                    <div class="flex_row_start_start" style="flex-wrap: wrap;margin-left: -10px;">
                      <template v-if="detail.productList != undefined">
                        <div class="flex_row_center_center spec_item" :key="index" :class="{selected_spec_item:selectedGoodsId.indexOf(item.platformProductId) > -1}" v-for="(item,index) in detail.productList" @click="specSel(item.platformProductId)">
                          <span class="spec_name">{{ item.specValues }}</span>
                          <span class="spec_price">¥{{item.productPrice && Number(item.productPrice).toFixed(2)}}</span>
                          <div class="selected_spec_item_icon" :class="{selected_spec_item:selectedGoodsId.indexOf(item.platformProductId) > -1}">
                            <AliSvgIcon
                              iconName="xuanzhongjiaobiao"
                              width="16px"
                              height="16px"
                              fillColor="#FE7F2D"
                            />
                          </div>
                        </div>
                      </template>
                    </div>
                  </div>
                </div>
                <div class="flex_row_start_start" style="margin-bottom: 30px;">
                  <template v-if="optSuccess">
                    <a class="flex_row_center_center add_to_store" @click="goToAdd">继续添加商品</a>
                  </template>
                  <template v-else>
                    <a class="flex_row_center_center add_to_store" @click="add(true)">上架到店铺</a>
                    <a class="flex_row_center_center add_to_storage" @click="add(false)">添加到仓库</a>
                  </template>
                </div>
              </div>
            </div>
            <div class="body_title">
              <span class="flex_row_center_center con">产品介绍</span>
            </div>
            <div class="body">
              <template v-if="detail.goodsDetails != ''">
                <div v-html="detail.goodsDetails">
                  
                </div>
              </template>
              <Empty v-else :image="moudle_disable" :image-style="{ height: '80px',marginTop:'90px' }" :description="L('暂无详情')"/>
            </div>
          </div>
        </div>
      </Spin>
    </div>
    <Modal :title="modalTitle" :width="500" :visible="modalVisible" @ok="sldConfirm" @cancel="sldCancle" :confirmLoading="submiting">
      <div class="goods_sku_tab add_goods_wrap full_activity" style="margin-bottom: 15px;">
        <Form layout="inline" ref="formRef" :model="modal_info">  
          <div class="full_acm_activity flex_column_start_start">
            <div class="item flex_row_start_start">
              <div class="left" style="width: 140px;">
                价格变动
              </div>
              <div class="right flex_column_start_start">
                <div>
                  <Form.Item
                    :extra="`最大值为：${maxChangeValue}，商品为多规格商品时，价格变动对所有sku有同等影响。`"
                    name="changeValue"
                    style="width: 245px;margin-right:0;"
                  >
                   <div class="flex_row_start_center">
                    <FormItemRest>

                      <Select style="width: 100px;" placeholder=""  v-model:value="modal_info.changeType">
                        <Select.Option  :key="1" :value="1">+</Select.Option>
                        <Select.Option  :key="2" :value="2">-</Select.Option>
                        <Select.Option  :key="3" :value="3">*</Select.Option>
                        <Select.Option  :key="4" :value="4">/</Select.Option>
                      </Select>
                    </FormItemRest>
                    <div style="margin-left:8px;">
                      <InputNumber :max="maxChangeValue" :min="0" :precision="2" style="width: 140px;" placeholder=""  v-model:value="modal_info.changeValue"/>
                    </div>
                   </div>
                  </Form.Item>
                </div>
              </div>
            </div>
            <div class="item flex_row_start_start">
              <div class="left" style="width: 140px;">
                <span style="color:red;">*</span>库存
              </div>
              <div class="right flex_column_start_start">
                <div>
                  <Form.Item
                    :extra="`商品为多规格商品时，多个sku的库存将被设置为同一库存值`"
                    name="productStock"
                    :rules="[
                        {
                          required: true,
                          message:'请输入库存',
                        },
                      ]"
                    style="width: 245px"
                  >
                  <InputNumber
                      :max="99999999"
                      :min="1"
                      style="width: 245px !important"
                      :precision="0"
                      placeholder="请输入库存"
                      v-model:value="modal_info.productStock"
                    />
                  </Form.Item>
                </div>
              </div>
            </div>
            <div class="item flex_row_start_start" v-if="detail.isVirtualGoods == 1 ">
              <div class="left" style="width: 140px;">
                <span style="color:red;">*</span>运费类型
              </div>
              <div class="right flex_column_start_start">
                <div>
                  <Form.Item
                    name="expressFeeType"
                    style="width: 245px"
                  >
                    <RadioGroup @change="handleExpressFeeType" size="small" v-model:value="modal_info.expressFeeType">
                      <Radio :value="1">固定运费</Radio>
                      <Radio :value="2">运费模板</Radio>
                    </RadioGroup>
                  </Form.Item>
                </div>
              </div>
            </div>
            <div class="item flex_row_start_start" v-if="modal_info.expressFeeType==1">
              <div class="left" style="width: 140px;">
                <span style="color:red;">*</span>固定运费
              </div>
              <div class="right flex_column_start_start">
                <div>
                  <Form.Item
                    name="freightFee"
                    :rules="[
                        {
                          required: true,
                          message: '请输入固定运费',
                        },
                      ]"
                    style="width: 245px"
                  >
                  <InputNumber
                      :max="99999999"
                      :min="0"
                      style="width: 245px !important"
                      :precision="0"
                      placeholder="请输入固定运费"
                      v-model:value="modal_info.freightFee"
                    />
                  </Form.Item>
                </div>
              </div>
            </div>
            <div class="item flex_row_start_start" v-else>
              <div class="left" style="width: 140px;">
                <span style="color:red;">*</span>运费模板
              </div>
              <div class="right flex_column_start_start">
                <div>
                  <Form.Item
                    name="freightId"
                    :rules="[
                        {
                          required: true,
                          message: '请选择运费模板',
                        },
                      ]"
                    style="width: 245px"
                  >
                    <Select
                      placeholder="请选择运费模板"
                      v-model:value="modal_info.freightId"
                    >
                      <Select.Option
                        v-for="item in transport_lists"
                        :key="item.freightTemplateId"
                        :value="item.freightTemplateId"
                        >{{item.templateName}}</Select.Option
                      >
                    </Select> 
                  </Form.Item>
                </div>
              </div>
            </div>
          </div>
        </Form>
      </div>
    </Modal>
  </div>
</template>
<script>
  export default {
    name: 'GoodsImportToAdd',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, onMounted } from 'vue';
  import { Spin,Carousel,Empty,Modal,Form,Select,InputNumber,RadioGroup,Radio,FormItemRest } from 'ant-design-vue';
  import { sucTip, failTip } from '@/utils/utils';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { useRouter,useRoute } from 'vue-router';
  import { list_com_page_more } from '/@/utils/utils';
  import { LeftCircleOutlined, RightCircleOutlined } from '@ant-design/icons-vue';
  import price_bg from '/src/assets/images/goods/price_bg.png';
  import moudle_disable from '/src/assets/images/moudle_disable.png';
  import {
    getGoodsPlatformDetailApi,
    getGoodsPlatformGoodsImportApi
  } from '/@/api/goods/import';
  import {
    getFreightTemplateListApi
  } from '/@/api/common/common';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;
  const router = useRouter();
  const route = useRoute();

  const modalTitle = ref('');//弹窗标题
  const initLoading = ref(false);//弹窗标题
  const id = ref('');//商品id
  const formRef = ref();
  const detail = ref({});//平台商品资料详情
  const selectedGoodsId = ref([]);//选中的sku id数组
  const modal_info = ref({
    changeType:1,
    expressFeeType:1
  })//弹窗数据
  const bigImg = ref('');//大图
  const optSuccess = ref(false);//将商品添加成功标识
  const modalVisible = ref(false);//显示弹框标识
  const submiting = ref(false);//modal框确认按钮
  const expressFeeType = ref(1);//运费类型 1为固定运费 2为运费模板
  const addType = ref(true);//操作类型  true-上架到店铺，false-添加到仓库
  const maxChangeValue = ref(9999999.98);//价格变化最大值
  const transport_lists = ref([]);//运费模版

  // 返回上一页
  const goBack = ()=> {
    router.replace('/goods/goods_import_to_platform');
  }

  // 获取详情数据
  const get_detail = async()=> {
    initLoading.value = true
    let res = await getGoodsPlatformDetailApi({platformGoodsId: id.value })
    if(res.state == 200){
      selectedGoodsId.value = [];
      // @ts-ignore
      bigImg.value = res.data.imageList[0];
      if(res.data.imageList.length>5){
        let array = []
        let obj = []
        if(res.data.imageList)
        res.data.imageList.forEach((item,index)=>{
          obj.push(item)
          if((index+1)%5==0){
            array.push(obj)
            obj = []
          }
          if(res.data.imageList.length==index+1&&res.data.imageList.length%5!=0){
            array.push(obj)
            obj = []
          }
        })
        res.data.imageListCar = array
        bigImg.value = res.data.imageListCar[0][0]
      }
      detail.value = res.data;
      detail.value.productList.map(item => {
        selectedGoodsId.value.push(item.platformProductId);
      });
      
      if (detail.value.maxPrice) {
        maxChangeValue.value = (9999999.99 - detail.value.maxPrice).toFixed(2);
      } else {
        maxChangeValue.value = (9999999.99 - detail.value.minPrice).toFixed(2);
      }
    }else{
      failTip(res.msg)
    }
    initLoading.value = false;
  }

  // 展示当前商品图片
  const showCurImg = (index)=> {
    bigImg.value =  detail.value.imageList[index]
  }

  // 展示当前商品图片 轮播
  const showCurImgCar = (index,ind)=> {
    bigImg.value =  detail.value.imageListCar[index][ind]
  }

  // 规格选择事件
  const specSel = (sku_id)=> {
    if(selectedGoodsId.value.indexOf(sku_id) > -1){
      if (selectedGoodsId.value.length == 1) {
        failTip(`至少选择一个规格～`);
      } else {
        selectedGoodsId.value = selectedGoodsId.value.filter(item => item != sku_id);
      }
    } else {
      selectedGoodsId.value.push(sku_id);
    }
  }

  //modal弹框确定事件
  const sldConfirm = ()=> {
    formRef.value.validate().then(async (values) => {
      values.changeType = modal_info.value.changeType
      values.importState = addType.value;//导入状态，true-上架到店铺，false-添加到仓库
      values.platformGoodsId = detail.value.platformGoodsId;//平台库商品id
      values.platformProductIds = selectedGoodsId.value.join(',');//平台库货品id集合,用英文逗号隔开
      if (!values.changeValue) {
        delete values.changeValue;
        delete values.changeType;
      }
      delete values.expressFeeType;
      submiting.value = true
      let res = await getGoodsPlatformGoodsImportApi(values)
      if(res.state == 200){
        sucTip(res.msg)
        optSuccess.value = true
        sldCancle()
      }else{
        failTip(res.msg)
      }
      submiting.value = false
    }).catch((value)=>{
    })
  }

  const sldCancle = ()=>{
    modalVisible.value = false
    expressFeeType.value = 1
    formRef.value.resetFields()
    modal_info.value = {
    changeType:1,
    expressFeeType:1
  }//弹窗数据
  }

   //继续添加商品
  const goToAdd = ()=> {
    router.replace('/goods/goods_import_to_platform');
  }

  //添加商品  type为导入状态，true-上架到店铺，false-添加到仓库
  const add = async(type)=> {
    if(transport_lists.value.length==0){
      await get_transport_lists()
    }
    modalTitle.value = type ? '上架到店铺' : '添加到仓库'
    addType.value = type
    modalVisible.value = true
  }
  
  //获取运费模板列表
  const get_transport_lists = async()=> {
    let res = await getFreightTemplateListApi({pageSize: list_com_page_more })
    if(res.state == 200){
      transport_lists.value = res.data.list;
    }
  }
  // 运费类型
  const handleExpressFeeType = (e)=> {
    if(e.target.value==1){
      modal_info.value.freightId = undefined
    }else{
      modal_info.value.freightFee = ''
    }
  }

  onMounted(() => {
    if(route.query&&route.query.id){
      id.value = route.query.id
    }
    get_detail();
  });
</script>
<style lang="less">
  @import '/@/assets/css/goods.less';
  @import '/@/assets/css/promotion.less';
  .custom-slick-arrow{
    width: 25px;
    height: 25px;
    font-size: 25px;
    color: #fff;
    background-color: rgba(31, 45, 61, 0.11);
    transition: ease all 0.3s;
    opacity: 0.3;
    z-index: 1;
  }
  .Carousel{
    .slick-track{
      width: 239px !important;
    }
    .slick-slide{
      width: 50px !important;
    height: 50px;
    }
  }
  .platform_goods_detail{
    .first{
      left: -23px !important;
    }
    .next{
      right: -23px !important;
    }
  }
</style>
