<template>
  <div class="section_padding goods_import">
    <div class="section_padding_back supplier_goods">
      <SldComHeader :type="1" :title="'供应链产品库'" />
      <div class="goods_search">
        <BasicForm
          :tableFlag="true"
          @register="register"
          @submit="handleSubmit"
          @reset="handleReset"
          @advanced-change="advancedChange" 
          showAdvancedButton
        />
      </div>
      <div class="goods_main">
        <div class="goods_category">
          <div class="goods_category_title">商品分类</div>
          <div class="goods_category_list goods_category_list_height"  :class="{'goods_category_list_height_flag':right_goods_height_flag}">
            <!-- category_height -->
            <div
              v-for="(item, index) in categoryList"
              :key="index"
              class="goods_category_item"
              :class="selectedCategoryId == item.categoryId ? 'active' : ''"
              @click="selectCategory(item.categoryId, 0)"
              @mouseenter="() => get_category_list(item, 1)"
            >
              <span>{{ item.categoryName }}</span>
              <AliSvgIcon
                iconName="icongengduo2"
                width="14px"
                height="14px"
                :fillColor="'#434343'"
              />
            </div>
            <div
              v-if="showMoreCat"
              class="goods_category_children goods_category_list_height"
              :class="{'goods_category_list_height_flag':right_goods_height_flag}"
            >
              <div
                v-for="(item, index) in categoryChildrenList"
                :key="index"
                class="flex_row_start_start goods_category_children_list"
              >
                <div
                  class="goods_category_children_list_title"
                  :class="
                    selectedCategoryPid == item.categoryId || selectedCategoryId == item.categoryId
                      ? 'active'
                      : ''
                  "
                  @click="selectCategory(item.categoryId, 0)"
                  >{{ item.categoryName
                  }}<span
                    ><AliSvgIcon
                      iconName="icongengduo2"
                      width="14px"
                      height="14px"
                      fillColor="#101010" /></span
                ></div>
                <div
                  v-if="item.children"
                  class="flex_row_start_center goods_category_children_list_main"
                >
                  <span
                    v-for="(items, indexs) in item.children"
                    :key="indexs"
                    class="goods_category_children_item"
                    :class="selectedCategoryId == items.categoryId ? 'active' : ''"
                    @click="selectCategory(items.categoryId, item.categoryId)"
                    >{{ items.categoryName }}</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="goods_list flex_column_start_start">
          <div class="flex_row_start_center operate_bg toolbar">
            <Checkbox :checked="checkAll" @change="ChangeCheck" style="margin-left: 10px">全选</Checkbox>
            <div class="toolbar_btn" @click="operate(null, 'set_price')">上架选中商品</div>
            <div class="toolbar_btn" style="background-color: orange;" @click="openSgAddCategoryModal">找不到？添加类目</div>
            <div class="toolbar_btn" style="background-color: gray;" @click="operate(null, 'set_price_search')">上架筛选商品</div>
            <div class="toolbar_btn" style="background-color: gray;" @click="operate(null, 'set_price_all')">上架全部商品</div>
          </div>
          <div
            v-if="dataSource.list != undefined && dataSource.list.length != undefined && dataSource.list.length > 0"
            class="right_goods flex_row_start_start right_goods_height"
            :class="{'right_goods_height_flag':right_goods_height_flag}"
            :style="{ width: '100%', zIndex: 1, minHeight: '100px' }"
          >
            <template v-for="(item,index) in dataSource.list" :key="index">
              <div
                class="flex_column_start_start item"
                @click="goDetail(item.goodsId)"
              >
                <img
                  v-if="item.checked"
                  class="checked"
                  @click="(e) => selectGoods(e, index)"
                  src="@/assets/images/vop_goods_checked.png"
                />
                <img 
                  v-else
                  class="checked"
                  @click="(e) => selectGoods(e, index)"
                  src="@/assets/images/vop_goods_check.png"
                />
                <span v-if="item.isVirtualGoods == 2" class="virtual_goods_flag">虚拟</span>
                <div
                  class="flex_row_center_center img_wrap"
                >
                  <img :src="item.mainImage" />
                </div>
                <p
                  class="goods_name"
                  :title="item.goodsName"
                >{{ item.isVirtualGoods == 2 ? '【虚拟】' : '' }}{{ item.goodsName }}</p>
                <span class="supplier_price flex_row_start_center">
                  <span class="price_title">供货价</span>{{ (item.goodsPrice !== undefined && item.goodsPrice !== null) ? '¥'+item.goodsPrice : '--' }}
                </span>
                <span class="supplier_price flex_row_start_center">
                  <span class="price_title">建议零售价</span>{{ item.retailPrice > 0 ? '¥'+item.retailPrice : '--' }}
                </span>
                <span class="supplier_price flex_row_start_center" style="color: #AAA; font-weight: 400">
                  <span class="price_title">利润</span>{{ (item.storeProfit !== null && item.storeProfit !== undefined)
                    ? ('¥' + item.storeProfit + ((item.storeProfitMargin !== null && item.storeProfitMargin !== undefined)
                    ? '(' + item.storeProfitMargin + ')' : '')) : '--' }}
                </span>
                <span class="supplier_price flex_row_start_center">
                  <span class="price_title">{{ item.storeName }}</span>
                </span>
              </div>
            </template>
          </div>
          <div
            v-else
            class="flex_row_center_center"
            :style="{ width: '100%', zIndex: 1, minHeight: '100px', maxHeight: right_goods_height + 'px' }"
          >
            <Empty
              :image="Empty.PRESENTED_IMAGE_SIMPLE"
              imageStyle="height: 80px"
              description="暂无商品数据~"
            ></Empty>
          </div>
          <div style="width: 100%; padding-top: 5px; padding-right: 20px;" class="flex_row_end_center">
            <Pagination
              size="small"
              showSizeChanger
              :pageSizeOptions="['10', '20', '50', '100']"
              :current="pagination.current"
              :pageSize="pagination.pageSize"
              :total="pagination.total"
              @change="(page, pageSize) => onChangePage(page, pageSize)"
              @showSizeChange="(current, size) => onChangePage(current, size)"
            />
          </div>
        </div>
      </div>
  
      <!-- 上架弹窗 start -->
      <Modal
        :width="600"
        :title="title"
        :visible="modalPriceVisible"
        :confirmLoading="confirmBtnLoading"
        @cancel="sldCancle"
        @ok="sldConfirm"
      >
        <div style=" padding: 20px 50px">
          <div class="flex_row_start_center">
            <div style="width: 76px; text-align: right; margin-right: 10px;"><span style="color: #ff0000;">*</span>销售价：</div>
            <span style="margin-right: 8px">在供货价的基础上</span>
            <Select
              size="small"
              style="width: 70px"
              :value="modalDetail.addType"
              placeholder=""
              @select="(e) => changeValue(e, 'addType')"
              :getPopupContainer="triggerNode => triggerNode.parentNode"
            >
              <Select.Option :value="1">+</Select.Option>
              <Select.Option :value="2">-</Select.Option>
            </Select>
            <InputNumber
              size="small" :value="modalDetail.addPrice" :min="0" :max="100" :precision="2"
              style="width: 80px !important; margin-right: 8px; margin-left: 8px;" @change="(e) => changeValue(e, 'addPrice')"
            />
            <span>%作为店铺销售价</span>
          </div>
        </div>
      </Modal>
      <!-- 上架弹窗 end -->
      <SgAddCategory :visible="sgAddCategoryVisible" :closeCb="closeSgAddCategoryModal"></SgAddCategory>
    </div>
  </div>
  </template>
  <script>
    export default {
      name: 'GoodsImportToSupplier',
    };
  </script>
  <script setup>
    import { Checkbox, Empty, InputNumber, Modal, Pagination, Select } from 'ant-design-vue';
import { onMounted, onUnmounted, reactive, ref } from 'vue';
import {
getSupplierCategoryTree,
getSupplierGoodsLists,
getSupplierGoodsProcess,
importSupplierGoods,
} from '/@/api/goods/goods';
import { BasicForm, useForm } from '/@/components/Form';
import SldComHeader from '/@/components/SldComHeader/index.vue';
import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
import { useGo } from '/@/hooks/web/usePage';
import { failTip, sucTip } from '/@/utils/utils';

// sg-start
import SgAddCategory from '@/components/SgAddCategory/index.vue';
// sg-end
  
    const go = useGo();
    const { getRealWidth } = useMenuSetting();
    const category_height = ref(document.body.clientHeight - 273 + 'px'); //商品分类模块高度
    const showMoreCat = ref(false);
    const selectedCategoryId = ref('');
    const selectedCategoryPid = ref('');
    const categoryList = ref([]);
    const categoryChildrenList = ref([]);
    const searchValue = ref({});

    const timeoutEvent = ref(null)//产品导入定时任务(5s)
    const timeoutEventKey = ref('')//产品导入定时接口传参

    const [register, { getFieldsValue }] = useForm({
      labelWidth: 120,
      schemas: [
        {
          field: 'goodsName',
          component: 'Input',
          componentProps: {
            minWidth: 300,
            placeholder: '请输入商品名称',
            size: 'default',
          },
          label: '商品名称',
          labelWidth: 80,
        },
        {
          field: 'goodsPriceStart',
          component: 'DoubleInputNumber',
          componentProps: {
            fields: ['goodsPriceStart','goodsPriceEnd'],
            precision: 0,
          },
          label: '供货价',
        },
        {
          field: 'retailPriceStart',
          component: 'DoubleInputNumber',
          componentProps: {
            fields: ['retailPriceStart','retailPriceEnd'],
            precision: 0,
          },
          label: '建议零售价',
        },
        {
          field: 'profitStart',
          component: 'DoubleInputNumber',
          componentProps: {
            fields: ['profitStart','profitEnd'],
            precision: 0,
          },
          label: '利润',
        },
        {
          field: 'profitMarginStart',
          component: 'DoubleInputNumber',
          componentProps: {
            fields: ['profitMarginStart','profitMarginEnd'],
            precision: 0,
          },
          label: '利润率',
        },
        {
          field: 'brandName',
          component: 'Input',
          componentProps: {
            minWidth: 300,
            placeholder: '请输入商品品牌',
            size: 'default',
          },
          label: '商品品牌',
          labelWidth: 80,
        },
        {
          field: 'storeName',
          component: 'Input',
          componentProps: {
            minWidth: 300,
            placeholder: '请输入供应商名称',
            size: 'default',
          },
          label: '供应商名称',
          labelWidth: 80,
        },
      ],
      actionColOptions: { span: 24 },
    });
    // sg-start
    // 商品分类弹窗
    const sgAddCategoryVisible = ref(false);
    const openSgAddCategoryModal = () => { sgAddCategoryVisible.value = true }
    const closeSgAddCategoryModal = () => { sgAddCategoryVisible.value = false }


    // sg-end
    const dataSource = reactive({ list: [] });
    const pagination = reactive({ current: 1, pageSize: 10, total: 0 });
    const clientWidth = ref(document.body.clientWidth);
    const right_goods_height = ref(document.body.clientHeight - 300);
    const right_goods_height_flag = ref(false)
    const checkAll = ref(false);
    const selectRowData = reactive({ ids: [], data: [] });
    const width = ref(550);
    const title = ref('');
    const confirmBtnLoading = ref(false);
    const operate_type = ref('');
    const modalPriceVisible = ref(false);
    const modalDetail = reactive({
      addType: 1,
      addPrice: 10,
    })
  
    //获取产品列表
    const get_goods_list = async () => {
      let params = {};
      params.current = pagination.current;
      params.pageSize = pagination.pageSize;
      if (selectedCategoryId.value) {
        params.categoryId = selectedCategoryId.value;
      }
      const res = await getSupplierGoodsLists({ ...params, ...searchValue.value });
      if (res.state == 200) {
        dataSource.list = res.data.list;
        if (pagination.total != res.data.pagination.total) {
          pagination.total = res.data.pagination.total;
        }
      } else {
        failTip(res.msg);
      }
    };
  
    //获取左侧商品分类
    const get_category_list = async (params, grade) => {
      let res;
      if (grade === 0) {
        res = await getSupplierCategoryTree({...params,goodsSource:2});
        if (res.state == 200) {
          categoryList.value = res.data;
        } else {
          failTip(res.msg);
        }
      } else if (grade === 1) {
        categoryChildrenList.value = params.children;
        showMoreCat.value = true;
      }
    };
  
    //分类搜素
    function selectCategory(categoryId, categoryPid) {
      selectedCategoryId.value = categoryId;
      selectedCategoryPid.value = categoryPid;
      showMoreCat.value = false;
      get_goods_list();
    };

    // 
    const advancedChange = (e)=> {
      right_goods_height_flag.value = e
    }
  
    //筛选搜索
    function handleSubmit(val) {
      for (let key in val) {
        if (val[key] === undefined || val[key] === null  || val[key] === '') {
          delete val[key];
        }
      }
      if (val.goodsPriceStart) {
        if (val.goodsPriceStart.split(',')[1]) {
          val.goodsPriceEnd = val.goodsPriceStart.split(',')[1]
        }
        if (val.goodsPriceStart.split(',')[0]) {
          val.goodsPriceStart = val.goodsPriceStart.split(',')[0]
        }
      }
      if (val.retailPriceStart) {
        if (val.retailPriceStart.split(',')[1]) {
          val.retailPriceEnd = val.retailPriceStart.split(',')[1]
        }
        if (val.retailPriceStart.split(',')[0]) {
          val.retailPriceStart = val.retailPriceStart.split(',')[0]
        }
      }
      if (val.profitStart) {
        if (val.profitStart.split(',')[1]) {
          val.profitEnd = val.profitStart.split(',')[1]
        }
        if (val.profitStart.split(',')[0]) {
          val.profitStart = val.profitStart.split(',')[0]
        }
      }
      if (val.profitMarginStart) {
        if (val.profitMarginStart.split(',')[1]) {
          val.profitMarginEnd = val.profitMarginStart.split(',')[1]
        }
        if (val.profitMarginStart.split(',')[0]) {
          val.profitMarginStart = val.profitMarginStart.split(',')[0]
        }
      }
      searchValue.value = val;
      get_goods_list();
    };
  
    //筛选重置
    function handleReset() {
      searchValue.value = {};
      selectedCategoryId.value = '';
      selectRowData.ids = [];
      selectRowData.data = [];
      get_goods_list();
    };
  
    //改变页码
    const onChangePage = (page, pageSize) => {
      pagination.current = page;
      pagination.pageSize = pageSize;
      get_goods_list();
    };
  
    onMounted(() => {
      get_category_list({ pId: 0, grade: 1 }, 0);
      get_goods_list();
      window.addEventListener('resize', resizeCategoryHeight, true);
    });
  
    onUnmounted(() => {
      window.removeEventListener('resize', resizeCategoryHeight, true);
    });
  
    function resizeCategoryHeight() {
      category_height.value = document.body.clientHeight - 273 + 'px';
    };
  
    //查看资料详情
    function goDetail(id) {
      go('/goods/goods_import_supplier_to_add?id=' + id);
    };
  
    //全选
    function ChangeCheck(flag) {
      checkAll.value = (flag===true || flag===false) ? flag : !checkAll.value;
      selectRowData.ids = [];
      selectRowData.data = [];
      if (checkAll.value) {
        dataSource.list.forEach((item) => {
          item.checked = true;
          selectRowData.ids.push(item.goodsId);
          selectRowData.data.push(item)
        })
      } else {
        dataSource.list.forEach((item) => {
          item.checked = false;
        })
      }
    };
  
    //选择商品
    function selectGoods(e, index) {
      e.stopPropagation();
      let item = dataSource.list[index];
      item.checked = !item.checked;
      let itemIndex = selectRowData.ids.indexOf(item.goodsId);
      if (itemIndex == -1 && item.checked) { //添加
        selectRowData.ids.push(item.goodsId);
        selectRowData.data.push(item)
      } else if (itemIndex >= 0 && !item.checked) { //删除
        selectRowData.ids.splice(itemIndex, 1);
        selectRowData.data.splice(itemIndex, 1);
      }
      if (!checkAll.value && (dataSource.list.length == selectRowData.data.length)) {
        checkAll.value = true;
      } else if (checkAll.value && (dataSource.list.length > selectRowData.data.length)) {
        checkAll.value = false;
      }
    };
  
    //操作 set_price-上架/设置供货价 set_price_search-上架筛选/设置筛选供货价 set_price_all-一键上架
    const operate = async (item, type) => {
      if (type == 'set_price' && selectRowData.ids.length == 0) {
        failTip('请先选中数据');
        return;
      }
      operate_type.value = type;
      if (type == 'set_price' || type == 'set_price_search' || type == 'set_price_all') {
        if (type == 'set_price') {
          title.value = '批量上架';
        } else if (type == 'set_price_search') {
          if (Object.keys(getFieldsValue()).length == 0) {
            failTip('请输入筛选条件并搜索后操作');
            return;
          } else if (!dataSource.list || dataSource.list.length == 0) {
            failTip('暂无可上架商品');
            return;
          }
          title.value = '批量上架筛选结果商品';
        } else {
          if (!dataSource.list || dataSource.list.length == 0) {
            failTip('暂无可上架商品');
            return;
          }
          title.value = '一键上架全部商品';
        }
        modalPriceVisible.value = true;
      }
    };
  
    //上架弹窗赋值
    function changeValue(e, key) {
      modalDetail[key] = e;
    };
  
    //上架弹窗取消
    function sldCancle() {
      modalPriceVisible.value = false;
      modalDetail.addType = 1;
      modalDetail.addPrice = 0;
    };
  
    //上架弹窗确定
    const sldConfirm = async () => {
      let param = {};
      if (modalDetail.addPrice == 0) {
        param.addType = 0;
        param.addPrice = 0;
      } else {
        param.addType = modalDetail.addType;
        param.addPrice = modalDetail.addPrice;
      }
      param.importState = true; //导入状态，true-上架到店铺，false-添加到仓库
      if (operate_type.value == 'set_price' || operate_type.value == 'set_price_all') {
        param.type = (operate_type.value == 'set_price' ? 1 : 3); //上架类型: 1-批量上架 2-批量上架筛选结果商品 3-一键上架全部商品
        if (operate_type.value == 'set_price') {
          param.goodsIds = selectRowData.ids.join();
        }
      } else if (operate_type.value == 'set_price_search') {
        param = {  ...param, ...getFieldsValue() };
        param.current = pagination.current;
        param.pageSize = pagination.pageSize;
        param.type = 2;
      }
      confirmBtnLoading.value = true;
      const res = await importSupplierGoods(param);
      if (res.state == 200) {
        timeoutEventKey.value = res.data;
        getFileState();
        timeoutEvent.value = setInterval(() => {
          getFileState();
        }, 5000)
      } else {
        confirmBtnLoading.value = false;
        failTip(res.msg);
      }
    };


    //定时获取导入结果
    const getFileState = async () => {
      if (!timeoutEventKey.value) {
        // @ts-ignore
        clearInterval(timeoutEvent.value);
        timeoutEvent.value = null
        confirmBtnLoading.value = false;
        timeoutEventKey.value = '';
        return;
      }
      let res = await getSupplierGoodsProcess({ key: timeoutEventKey.value })
      if (res.state == 200) {
        if (res.data.process == 2) { //成功
          sucTip(res.msg);
          sldCancle();
          pagination.current = 1;
          pagination.pageSize = 10;
          pagination.total = 0;
          get_goods_list();
          confirmBtnLoading.value = false;
          ChangeCheck(false);
        } else if (res.data.process == 3) { //失败
          confirmBtnLoading.value = false;
          failTip(res.data.failReason);
        } else {
          return;
        }
        // @ts-ignore
        clearInterval(timeoutEvent.value);
        timeoutEvent.value = null
        timeoutEventKey.value = '';
      }else{
        failTip(res.msg);
        // @ts-ignore
        clearInterval(timeoutEvent.value);
        timeoutEvent.value = null
        confirmBtnLoading.value = false;
        timeoutEventKey.value = '';
      }
    }
  </script>
  <style lang="less" scoped>
    .supplier_goods {
      .goods_main {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
  
        .goods_category {
          position: relative;
          flex-shrink: 0;
          width: 200px;
          margin-right: 10px;
          border: 1px solid #ebedf0;
          border-radius: 2px;
          background: #fff;
  
          .goods_category_title {
            width: 100%;
            height: 49px;
            padding-left: 20px;
            border-bottom: 1px solid #d8d8d8;
            color: #323233;
            font-family: PingFangSC-Medium, 'PingFang SC';
            font-size: 14px;
            font-weight: 500;
            line-height: 49px;
          }
          .goods_category_list_height{
            height: calc(100vh - 315px);
          }
          .goods_category_list_height_flag{
            height: calc(100vh - 355px);
          }
          .goods_category_list {
            width: 100%;
            overflow-x: hidden;
            overflow-y: auto;
  
            .goods_category_item {
              display: flex;
              position: relative;
              align-items: center;
              justify-content: space-between;
              height: 44px;
              padding-left: 20px;
              cursor: pointer;
  
              span {
                color: #323233;
                font-family: PingFangSC-Regular, 'PingFang SC';
                font-size: 14px;
                font-weight: 400;
                line-height: 20px;
              }
  
              svg {
                margin-right: 76px;
              }
  
              &:hover,
              &.active {
                span {
                  color: @primary-color;
                }
  
                svg {
                  fill: #ff701e !important;
                }
              }
            }
  
            &:hover {
              .goods_category_children {
                display: block;
              }
            }
  
            .goods_category_children {
              display: none;
              position: absolute;
              z-index: 999;
              top: 49px;
              bottom: 0;
              left: 205px;
              width: 800px;
              padding: 25px;
              border: 1px solid #dfdfdf;
              background: #f9f9f9;
  
              .goods_category_children_list {
                .goods_category_children_list_title {
                  flex-shrink: 0;
                  width: 108px;
                  height: 20px;
                  color: #4c4c4c;
                  font-family: PingFangSC-Semibold, 'PingFang SC';
                  font-size: 14px;
                  font-weight: 600;
                  line-height: 20px;
                  text-align: right;
                  white-space: nowrap;
                  cursor: pointer;
  
                  span {
                    position: relative;
                    top: 2px;
                    margin-left: 6px;
                  }
  
                  &:hover,
                  &.active {
                    color: @primary-color;
  
                    span {
                      svg {
                        fill: @primary-color !important;
                      }
                    }
                  }
                }
  
                .goods_category_children_list_main {
                  flex-wrap: wrap;
                  margin-left: 20px;
  
                  .goods_category_children_item {
                    margin-right: 20px;
                    margin-bottom: 15px;
                    font-family: PingFangSC-Regular, 'PingFang SC';
                    font-size: 14px;
                    font-weight: 400;
                    line-height: 20px;
                    cursor: pointer;
  
                    &:hover,
                    &.active {
                      color: @primary-color;
                    }
                  }
                }
              }
            }
          }
        }
  
        .goods_list {
          width: calc(100% - 210px);
  
          .toolbar {
            margin-top: 0;
            margin-bottom: 10px;
  
            .toolbar_btn {
              background-color: @primary-color;
              color: #fff;
            }
          }
          .right_goods_height{
            max-height: calc(100vh - 340px);
          }
          .right_goods_height_flag{
            max-height: calc(100vh - 380px) !important;
          }
  
          .right_goods {
            flex: 1;
            flex-wrap: wrap;
            overflow-x: hidden;
            overflow-y: auto;
        
            .item {
              width: 19% !important;
              position: relative;
              margin-bottom: 10px;
              margin-left: 1%;
              padding-bottom: 10px;
              overflow: hidden;
              border: 1px solid #EAEAEA;
              border-radius: 2px;
              background: #FFF;
              cursor: pointer;
  
              &:hover,
              &.active {
                border-color: #FF9147;
              }
  
              .checked {
                position: absolute;
                z-index: 99;
                top: 0;
                right: 0;
                width: 18px;
                height: 18px;
              }
  
              .virtual_goods_flag {
                position: absolute;
                top: -1px;
                left: -1px;
                padding: 0 3px;
                border-radius: 3px 0;
                background: linear-gradient(90deg, #F7D47E 0%, #E6B845 100%);
                color: #fff;
                font-size: 12px;
              }
  
              .img_wrap {
                border-radius: 2px 2px 0 0;
                background: #F8F8F8;
                width: 100% !important;
                height: 0;
                padding-bottom: 100%;
                position: relative;
                img {
                  width: 100%;
                  max-height: 100%;
                  position: absolute;
                  left: 0;
                  top: 50%;
                  transform: translateY(-50%);
                }
              }
        
              .goods_name {
                display: -webkit-box;
                height: 40px;
                margin-top: 10px;
                padding: 0 10px;
                overflow: hidden;
                color: #333;
                font-family: PingFangSC-Regular, "PingFang SC";
                font-size: 13px;
                font-weight: 400;
                line-height: 20px;
                text-overflow: ellipsis;
                word-break: break-word;
                -webkit-line-clamp: 2;
  
                /*! autoprefixer: off */
                -webkit-box-orient: vertical;
              }
        
              .price {
                margin: 5px 10px 10px;
                color: #FC1C00;
                font-family: PingFangSC-Semibold, "PingFang SC";
                font-size: 16px;
                font-weight: 600;
                line-height: 22px;
        
                .price_title {
                  margin-right: 5px;
                  color: #AAA;
                  font-size: 13px;
                  font-weight: 400;
                  white-space: nowrap;
                }
              }
        
              .supplier_price {
                margin: 5px 10px 0;
                color: #FC1C00;
                font-family: PingFangSC-Semibold, "PingFang SC";
                font-size: 13px;
                font-weight: 600;
                line-height: 22px;
        
                .supplier_title,
                .price_title {
                  margin-right: 5px;
                  color: #AAA;
                  font-size: 12px;
                  font-weight: 400;
                  white-space: nowrap;
                }
  
                .supplier_title {
                  margin-left: 6px;
                }
              }
            }
          }
        }
      }
    }
  </style>
  