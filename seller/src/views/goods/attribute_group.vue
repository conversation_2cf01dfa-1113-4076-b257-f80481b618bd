<template>
  <div class="section_padding attribute_group">
    <div class="section_padding_back">
      <SldComHeader type="1" :title="L('自定义属性')" />
      <BasicTable @register="standardTable" style="padding: 0">
        <template #tableTitle>
          <div class="toolbar">
            <div class="toolbar_btn" @click="handleClick(null, 'add')">
              <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#ff5908" />
              <span>{{ L('添加属性分组') }}</span>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex == 'action'">
            <TableAction
              class="TableAction"
              :actions="[
              {
                label: L('查看'),
                onClick: handleClick.bind(null, record, 'view'),
              },
              {
                label: L('编辑'),
                onClick: handleClick.bind(null, record, 'edit'),
              },
              {
                label: L('删除'),
                popConfirm: {
                  title: L('删除后不可恢复，是否确定删除？'),
                  placement: 'left',
                  confirm: handleClick.bind(null, record, 'del'),
                },
              },
              ]"
            />
          </template>
          <template v-else-if="column.dataIndex == 'isShow'">
            <Switch :checked="text == 1" @change="(e) => handleClick({ ...record, value: e }, 'switch')"></Switch>
          </template>
          <template v-else-if="column.dataIndex">
            {{ text !== undefined && text !== null && text !== '' ? text : '--' }}
          </template>
        </template>
      </BasicTable>
    </div>
    <SldModal
      :width="width"
      :title="title"
      :visible="visible"
      :content="content"
      :confirmBtnLoading="confirmBtnLoading"
      :showFoot="true"
      @cancle-event="handleCancle"
      @confirm-event="handleConfirm"
    />
  </div>
</template>
<script setup>
import { getCurrentInstance, ref } from 'vue';
import { Tabs, TabPane, Switch, Popconfirm } from 'ant-design-vue';
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { sucTip, failTip } from '@/utils/utils';
import {
  getParameterGroupList,
  addParameterGroup,
  editParameterGroup,
  delParameterGroup,
} from '/@/api/goods/goods';
import SldComHeader from '/@/components/SldComHeader/index.vue';
import SldModal from '@/components/SldModal/index.vue';
import { useGo } from '/@/hooks/web/usePage';

const go = useGo();
const vm = getCurrentInstance();
const L = vm?.appContext.config.globalProperties.$sldComLanguage;

const [standardTable, { reload }] = useTable({
  api: (arg) => getParameterGroupList({ ...arg }),
  fetchSetting: {
    pageField: 'current',
    sizeField: 'pageSize',
    listField: 'data.list',
    totalField: 'data.pagination.total',
  },
  ellipsis: false,
  columns: [
    {
      title: L('分组名称'),
      dataIndex: 'groupName',
      width: 100,
    },
    {
      title: L('排序'),
      dataIndex: 'sort',
      width: 100,
    },
    {
      title: L('启用状态'),
      dataIndex: 'isShow',
      width: 100,
    },
  ],
  actionColumn: {
    width: 100,
    title: L('操作'),
    dataIndex: 'action',
  },
  useSearchForm: true,
  formConfig: {
    schemas: [
      {
        field: 'groupName',
        component: 'Input',
        colProps: { span: 6, style: 'width:260px;max-width:100%;flex:none' },
        componentProps: {
          minWidth: 300,
          maxlength: 6,
          placeholder: L('请输入属性分组名称'),
          size: 'default',
        },
        label: L('分组名称'),
        labelWidth: 80,
      },
    ],
  },
  bordered: true,
  striped: false,
  showIndexColumn: true,
});

const width = ref(500);
const title = ref('添加属性分组');
const visible = ref(false);
const content = ref([]);
const add_edit_content = ref([
  {
    type: 'input',
    label: L('分组名称'),
    name: 'groupName',
    placeholder: L('请输入分组名称'),
    extra: L('最多输入6个字'),
    maxLength: 6,
    initialValue: '',
    require: true,
    rules: [
      {
        required: true,
        whitespace: true,
        message: '请输入分组名称',
      },
    ],
  }, {
    type: 'inputnum',
    label: L('排序'),
    name: 'sort',
    placeholder: L('请输入排序'),
    extra: L('请输入0~255的数字,数字越小顺序越靠前'),
    min: 0,
    max: 255,
    precision: 0,
    initialValue: '',
    require: true,
    rules: [
      {
        required: true,
        message: L('请输入排序'),
      },
    ],
  }, {
    type: 'switch',
    label: L('启用'),
    name: 'isShow',
    initialValue: '',
  }
]);
const confirmBtnLoading = ref(false);
const operate_type = ref('');
const operate_item = ref({});
const click_event = ref(false);

//表格点击回调事件
const handleClick = (item, type) => {
  if (click_event.value) return;
  operate_type.value = type;
  operate_item.value = item ? item : {};
  if (type == 'view') {
    go(`/goods/attribute_group_to_detail?id=${item.groupId}&name=${item.groupName}`);
  } else if (type == 'add') {
    width.value = 500;
    title.value = '添加属性分组';
    content.value = JSON.parse(JSON.stringify(add_edit_content.value));
    visible.value = true;
  } else if (type == 'edit') {
    width.value = 500;
    title.value = '编辑属性分组';
    content.value = JSON.parse(JSON.stringify(add_edit_content.value));
    content.value.map(items => {
      if (items.name == 'isShow') {
        items.initialValue = !item[items.name] ? false : true;
      } else {
        items.initialValue = (item[items.name] !== undefined && item[items.name] !== null) ? item[items.name] : undefined;
      }
    })
    visible.value = true;
  } else if (type == 'switch') {
    handleConfirm({
      groupId: item.groupId,
      groupName: item.groupName,
      sort: item.sort,
      isShow: item.value,
    });
  } else if (type == 'del') {
    click_event.value = true;
    operate_role({ groupIds: item.groupId });
  }
}

// 删除操作
const operate_role = async (params) => {
  let res = await delParameterGroup(params)
  click_event.value = false;
  if (res.state == 200) {
    sucTip(res.msg)
    reload()
  } else {
    failTip(res.msg)
  }
}

const handleCancle = () => {
  visible.value = false;
  content.value = [];
  operate_type.value = '';
  operate_item.value = {};
};

const handleConfirm = async (val) => {
  if (click_event.value) return;
  click_event.value = true;
  val.isShow = val.isShow ? 1 : 0;
  let res = null;
  if (operate_type.value == 'add') {
    res = await addParameterGroup(val);
  } else if (operate_type.value == 'edit') {
    val.groupId = operate_item.value.groupId;
    res = await editParameterGroup(val);
  } else if (operate_type.value == 'switch') {
    res = await editParameterGroup(val);
  } else {
    click_event.value = false;
    return;
  }
  click_event.value = false;
  if (res.state == 200) {
    sucTip(res.msg);
    handleCancle();
    reload();
  } else {
    failTip(res.msg);
  }
};
</script>
<style lang="less">
.attribute_group {
  .tabs_nav {
    .ant-tabs-nav {
      margin-bottom: 0;
    }
  }
}
</style>
