<template>
    <div class="course_type">
        <div class="toolbar">
            <div class="toolbar_btn" @click="modalVisible = true">
                <AliSvgIcon iconName="icondaoru" width="17px" height="17px" fillColor="red" />
                <span>{{ L('添加题目模块') }}</span>
            </div>
        </div>
        <StandardFormRow :width="'100%'" :left_width="130" :data="extraData" :valida="extraValida"
            @valida-event="handleValida" @callback-event="(e) => handleChange(e, 'extraData')" hideBorder="bottom" />

        <SldModal :width="800" :title="'模块管理'" :visible="modalVisible" :content="content" :showFoot="true"
            :confirmBtnText="'新增'" @confirm-event="handleConfirm" @cancle-event="modalVisible = false">
            <!-- dev_supplier-start -->
            <template #bodyCell="{ column, record, text }">
                <template v-if="column.dataIndex == 'action'">
                    <div class="goods_course_action">
                        <span class="common_page_edit cursor-pointer hover:text-[#FF6A12]"
                            @click="handleClick(record, 'edit')">{{ L('编辑') }}</span>
                        <Popconfirm :title="L('确认删除选中的模块吗？')" @confirm="handleClick(record, 'delete')">
                            <span class="goods_edit cursor-pointer hover:text-[#FF6A12]">{{
                                L('删除') }}</span>
                        </Popconfirm>
                    </div>
                </template>
                <template v-if="column.dataIndex == 'access_token' || column.dataIndex == 'bot_id'">
                    <div class="whitebreak">
                        {{ text }}
                    </div>
                </template>
            </template>
            <!-- dev_supplier-end -->
        </SldModal>

        <!-- 编辑 -->
        <SldModal :content="editModalContent" :visible="sldModalVisible" title="模块" :width="600"
            @cancle-event="sldModalVisible = false" @confirmEvent="editConfirm"></SldModal>
    </div>
</template>
<script>
export default {
    name: 'CourseTypeConsult',
};
</script>
<script setup>
import { getCurrentInstance, ref, onMounted, watch, computed } from 'vue';
import { sldToNormalDate, failTip } from '@/utils/utils';
import { Popconfirm } from 'ant-design-vue';
import { aiModuleList, aiModuleDelete, aiModuleUpdate, aiModuleCreate } from '/@/api/goods/course'
import StandardFormRow from '/@/components/StandardFormRow/index.vue';
import SldModal from '@/components/SldModal/index.vue';

const vm = getCurrentInstance();
const L = vm?.appContext.config.globalProperties.$sldComLanguage;

const props = defineProps({
    result: null
});

const extraValida = ref(false);
const extraData = ref([
    {
        type: 'title',
        label: '其他信息',
    },
    {
        type: 'select',
        label: '题目难度',
        placeholder: '请选择题目难度',
        key: 'level',
        value: 1,
        data: [
            {
                level: 1, name: '初级'
            },
            {
                level: 2, name: '中级'
            }, {
                level: 3, name: '高级'
            }
        ],
        allowClear: true,
        right_width: '89%',
        diy: true,
        diy_key: 'name',
        diy_value: 'level',
        callback: true,
    },
    {
        type: 'select',
        label: '题目模块',
        placeholder: '请选择题目模块',
        key: 'module',
        value: undefined,
        data: [],
        allowClear: true,
        right_width: '89%',
        diy: true,
        diy_key: 'name',
        diy_value: 'id',
        callback: true,
    },
    {
        type: 'inputnum',
        label: '权重',
        key: 'weight',
        placeholder: '请输入权重',
        maxlength: 3,
        desc: '数字越大优先级越高',
        value: 0,
        right_width: '89%',
        callback: true,
    },
    {
        type: 'textarea',
        require: true,
        label: '引导词',
        key: 'guide_words',
        placeholder: '请输入课程引导词',
        maxlength: 800,
        desc: '最多输入800个字',
        value: undefined,
        right_width: '89%',
        showCount: true,
        allowClear: true,
        rows: 5,
        rules: [
            {
                required: true,
                whitespace: true,
                message: '请输入课程引导词',
            },
        ],
        callback: true,
    },
    {
        type: 'ueditor',
        require: true,
        label: '背景',
        key: 'background',
        placeholder: '请输入课程背景',
        value: undefined,
        right_width: '89%',
        height:'600px',
        rules: [
            {
                required: true,
                whitespace: true,
                message: '请输入课程背景',
            },
        ],
        callback: true
    },
    {
        type: 'textarea',
        require: true,
        label: '内容',
        key: 'content',
        placeholder: '请输入课程内容',
        maxlength: 800,
        desc: '最多输入800个字',
        value: undefined,
        right_width: '89%',
        rules: [
            {
                required: true,
                whitespace: true,
                message: '请输入课程内容',
            },
        ],
        showCount: true,
        allowClear: true,
        callback: true,
    },
    {
        type: 'textarea',
        require: true,
        label: '指导答案',
        key: 'reference_answer',
        placeholder: '请输入课程指导答案',
        maxlength: 800,
        desc: '最多输入800个字',
        value: undefined,
        right_width: '89%',
        rules: [
            {
                required: true,
                whitespace: true,
                message: '请输入课程指导答案',
            },
        ],
        showCount: true,
        allowClear: true,
        callback: true,
    },
    {
        type: 'input',
        require: true,
        label: '出题人',
        key: 'questioner',
        placeholder: '请输入课程出题人',
        maxlength: 20,
        desc: '最多输入20个字',
        value: undefined,
        right_width: '89%',
        rules: [
            {
                required: true,
                whitespace: true,
                message: '请输入课程出题人',
            },
        ],
        callback: true,
    },
])
function handleChange(item, key) {

    let temp = extraData.value.filter(items => items.key == item.contentItem.key);
    if (temp.length > 0) {
        temp[0].value = item.val;
    }
    console.log(item)
    emits('change', extraData.value)
  
}
//校验事件回调
function handleValida(res) {

}
const emits = defineEmits([
    "change"
])

const modalVisible = ref(false);
const content = ref([])
const sldModalVisible = ref(false)

//表单展示字段
const content_columns = ref([
    {
        title: L('ID'),
        dataIndex: 'id',
        width: 40,
    },
    {
        title: L('模块名称'),
        dataIndex: 'name',
        width: 80,
    },
    {
        title: L('bot_id'),
        dataIndex: 'bot_id',
        width: 180,
    },
    {
        title: L('access_token'),
        dataIndex: 'access_token',
        width: 220,
    },
    {
        title: L('更新时间'),
        dataIndex: 'updated_at',
        width: 120,
        customRender: ({ text }) => {
            return sldToNormalDate(text * 1000);
        },
    },
    {
        title: L('操作'),
        dataIndex: 'action',
        width: 120,
    },
]);
const editModalContent = ref(
    [
        {
            name: 'name',
            type: 'input',
            placeholder: '请输入模块名称',
            label: '模块名称',
            labelWidth: 70,
            maxLength: 20,
            rules: [
                {
                    whiteSpace: true,
                    trigger: 'blur',
                    required: true,
                    message: '请输入模块名称',
                },
            ],
        },
        {
            name: 'bot_id',
            type: 'input',
            placeholder: '请输入bot_id',
            label: 'bot_id',
            labelWidth: 70,
            maxLength: 200,
            rules: [
                {
                    whiteSpace: true,
                    trigger: 'blur',
                    required: true,
                    message: '请输入bot_id',
                },
            ],
        },
        {
            name: 'access_token',
            type: 'input',
            placeholder: '请输入access_token',
            label: 'access_token',
            labelWidth: 70,
            maxLength: 20,
            rules: [
                {
                    whiteSpace: true,
                    trigger: 'blur',
                    required: true,
                    message: '请输入access_token',
                },
            ],
        },
        {
            type: 'upload_img_upload',
            label: '上传文件',
            name: 'images',
            extra: '',
            multiple: true,
            maxCount: 5,
            initialValue: [],
            upload_name: 'file',
            upload_url: '/v3/oss/seller/upload?source=goodsQrCode',
            fileList: [],
            limit: 20,
            accept: ' .jpg, .jpeg, .png, .gif',
            callback: true,
            rules: [],
        },
    ]
)
const getModules = async () => {
    const res = await aiModuleList({});
    let temp = extraData.value.filter(items => items.key == 'module');
    temp[0]['data'] = res.data.list.map(item => {
        return {
            id: item.id,
            name: item.name
        }
    })
    content.value = [
        // @ts-ignore
        {
            type: 'show_content_table', //展示表格，没有分页，不能勾选
            name: 'bind_module',
            width: 880,
            wrapperCol: { flex: 1, style: 'margin-bottom:-19px' },
            label: ``,
            content: '',
            data: res.data.list,
            scrollHeight: 300, // 设置纵向滚动，也可用于指定滚动区域的高，可以设置为像素值
            columns: content_columns.value,
            rowKey: 'id',
            scroll: false,
        }
    ];
    console.log(res)
}
const id = ref(null)
// toAddModule
const handleConfirm = () => {
    sldModalVisible.value = true
}
const editConfirm = async (values, resetFields) => {
    console.log(resetFields)
    let res, images = '';
    if (values.images) {
        images = values.images.map(item => item.url).join(',')
    }
    if (id.value) {
        res = await aiModuleUpdate({ ...values, id: id.value, images });
    } else {
        res = await aiModuleCreate({ ...values, images });
    }

    if (res.code == 0) {
        failTip(res.msg);
        sldModalVisible.value = false;
        getModules()
    } else {
        failTip(res.msg);
    }
    editModalContent.value.forEach(item => {
        if (item['name'] == 'images') item['initialValue'] = []
        else item['initialValue'] = undefined

    })
};
const handleClick = async (record, type) => {
    if (type == 'delete') {
        const res = await aiModuleDelete({ id: record.id })
        if (res.code == 0) {
            failTip(res.msg)
            getModules()
        } else {
            failTip(res.msg)
        }
    } else {
        id.value = record.id
        editModalContent.value.forEach(item => {
            const key = item['name']
            if (record[key]) {
                if (key == 'images') {
                    let arr = []
                    if (record[key].indexOf(',') > 0) {
                        arr = record[key].split(',')
                    } else {
                        arr = [record[key]]
                    }
                    item['initialValue'] = arr.map((item) => {
                        return {
                            uid: item,
                            name: item,
                            status: 'done',
                            url: item,
                        }
                    })
                } else {
                    item['initialValue'] = record[key]
                }
            }
        })
        console.log(editModalContent.value)
        sldModalVisible.value = true
    }
}
const resultData = computed(() => {
    return props.result;
});

watch(resultData, () => {
    for (let i = 0; i < extraData.value.length; i++) {
        const { key } = extraData.value[i]
        if (key && props.result[key]) {
            extraData.value[i]['value'] = props.result[key]
        }
    }
});
onMounted(() => {
    getModules()
});
</script>
<style lang="less">
.course_type {
    margin-top: 10px;
}

.whitebreak {
    word-wrap: break-word;
    width: 200px;
}
</style>