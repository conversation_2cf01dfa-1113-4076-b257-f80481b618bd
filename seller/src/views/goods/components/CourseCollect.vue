<template>
    <div class="course_type">

        <SldModal :width="800" :title="'合集管理'" :visible="modalVisible" :content="content" :showFoot="true"
            :confirmBtnText="'新增'" @confirm-event="handleConfirm" @cancle-event="modalVisible = false">
            <!-- dev_supplier-start -->
            <template #bodyCell="{ column, record, text }">
                <template v-if="column.dataIndex == 'image_url_list'">
                    <img :src="record.image_url_list" style="width:80px;height:80px;">
                </template>
                <template v-if="column.dataIndex == 'action'">
                    <div class="goods_course_action">
                        <span class="common_page_edit cursor-pointer hover:text-[#FF6A12]"
                            @click="handleClick(record, 'edit')">{{ L('编辑') }}</span>
                        <Popconfirm :title="L('确认删除选中的模块吗？')" @confirm="handleClick(record, 'delete')">
                            <span class="goods_edit cursor-pointer hover:text-[#FF6A12]">{{
                                L('删除') }}</span>
                        </Popconfirm>
                    </div>
                </template>
            </template>
            <!-- dev_supplier-end -->
        </SldModal>

        <!-- 编辑 -->
        <SldModal :content="editModalContent" :visible="sldModalVisible" title="合集" :width="600"
            @cancle-event="sldModalVisible = false" :confirmBtnLoading="btnLoading" @confirmEvent="editConfirm"
            @ueditorEvent="handleEditor">
        </SldModal>
    </div>
</template>
<script>
export default {
    name: 'CourseCollect',
};
</script>
<script setup>
import { getCurrentInstance, ref, onMounted } from 'vue';
import { sldToNormalDate, failTip } from '@/utils/utils';
import { Popconfirm } from 'ant-design-vue';
import { goodsList, goodsDelete, goodsUpdate, goodsCreate } from '/@/api/goods/course'
import SldModal from '@/components/SldModal/index.vue';

const vm = getCurrentInstance();
const L = vm?.appContext.config.globalProperties.$sldComLanguage;

const props = defineProps({
    result: null
});
const emits = defineEmits([
    "getData"
])

const modalVisible = ref(false);
const content = ref([])
const sldModalVisible = ref(false)

//表单展示字段
const content_columns = ref([
    {
        title: L('ID'),
        dataIndex: 'id',
        width: 40,
    },
    {
        title: L('合集名称'),
        dataIndex: 'goods_name',
        width: 80,
    },
    {
        title: L('封面'),
        dataIndex: 'image_url_list',
        width: 100,
    },
    {
        title: L('价格'),
        dataIndex: 'goods_price',
        width: 60,
    },
    {
        title: L('库存'),
        dataIndex: 'goods_stock',
        width: 60,
    },
    {
        title: L('状态'),
        dataIndex: 'goods_state',
        width: 40,
    },
    {
        title: L('更新时间'),
        dataIndex: 'updated_at',
        width: 140,
        customRender: ({ text }) => {
            return sldToNormalDate(text * 1000);
        },
    },
    {
        title: L('操作'),
        dataIndex: 'action',
        width: 120,
    },
]);
const editModalContent = ref(
    [
        {
            name: 'goods_name',
            type: 'input',
            placeholder: '请输入合集名称',
            label: '合集名称',
            labelWidth: 70,
            maxLength: 20,
            rules: [
                {
                    whiteSpace: true,
                    trigger: 'blur',
                    required: true,
                    message: '请输入合集名称',
                },
            ],
        },
        {
            type: 'upload_img_upload',
            label: '封面图',
            name: 'image_url_list',
            extra: '',
            multiple: true,
            maxCount: 1,
            initialValue: [],
            upload_name: 'file',
            upload_url: '/v3/oss/seller/upload?source=goodsQrCode',
            fileList: [],
            limit: 20,
            accept: ' .jpg, .jpeg, .png, .gif',
            callback: true,
            rules: [],
        },
        {
            name: 'goods_price',
            type: 'input',
            placeholder: '请输入价格',
            label: '价格',
            labelWidth: 70,
            maxLength: 200
        },
        {
            name: 'goods_stock',
            type: 'inputnum',
            placeholder: '请输入库存',
            label: '库存',
            labelWidth: 70,
            maxLength: 200
        },
        {
            type: 'radio_orignal',
            label: '上下架',
            name: 'goods_state',
            data: [
                { value: '上架', key: 1 },
                { value: '下架', key: 2 },
            ],
            initialValue: 1,
            labelWidth: 70
        },
        {
            name: 'goods_detail',
            type: 'ueditor',
            label: '详情',
            labelWidth: 70,
            getflag: false
        },
    ]
)
const getModules = async () => {
    const res = await goodsList({});
    content.value = [
        // @ts-ignore
        {
            type: 'show_content_table', //展示表格，没有分页，不能勾选
            name: 'bind_module',
            width: 880,
            wrapperCol: { flex: 1, style: 'margin-bottom:-19px' },
            label: ``,
            content: '',
            data: res.data.list,
            scrollHeight: 300, // 设置纵向滚动，也可用于指定滚动区域的高，可以设置为像素值
            columns: content_columns.value,
            rowKey: 'id',
            scroll: false,
        }
    ];
    emits('getData',res.data.list)
    console.log(res)
}
const id = ref(null)
const btnLoading = ref(false)
// toAddModule
const handleConfirm = () => {
    sldModalVisible.value = true
}
const handleEditor = async (values) => {
    console.log(values)
    let res, image_url_list = [] , params = {};
    if (values.image_url_list) {
        image_url_list = values.image_url_list.map(item => item.url)
    }
    params = {
        ...values, 
        image_url_list,
        goods_price: Number(values.goods_price)
    }
    btnLoading.value = true
    try {
        if (id.value) {
            res = await goodsUpdate({ ...params, id: id.value });
        } else {
            res = await goodsCreate({ ...params });
        }
        if (res.code == 0) {
            failTip(res.msg);
            sldModalVisible.value = false;
            getModules()
        } else {
            failTip(res.msg);
        }
        btnLoading.value = false
        resetField()
    } catch (error) {
        btnLoading.value = false
    }
}
const editConfirm = async (values) => {
    editModalContent.value.forEach(item => {
        const key = item['name']
        if (key == 'goods_detail') {
            item['getflag'] = true
            setTimeout(() => {
                item['getflag'] = false
            }, 1000)
        }
    })
};
const handleClick = async (record, type) => {
    if (type == 'delete') {
        const res = await goodsDelete({ id: record.id })
        if (res.code == 0) {
            failTip(res.msg)
            getModules()
        } else {
            failTip(res.msg)
        }
    } else {
        id.value = record.id
        editModalContent.value.forEach(item => {
            const key = item['name']
            if (record[key]) {
                if (key == 'image_url_list') {
                    item['initialValue'] = record[key].map((item) => {
                        return {
                            uid: item,
                            name: item,
                            status: 'done',
                            url: item,
                        }
                    })
                } else {
                    item['initialValue'] = record[key]
                }
            }
        })
        console.log(editModalContent.value)
        sldModalVisible.value = true
    }
}
const open = () => {
    modalVisible.value = true
}
const resetField = () => {
    editModalContent.value.forEach(item => {
        if (item['name'] == 'image_url_list') {
            item['initialValue'] = []
        } else if (item['name'] == 'goods_detail') {
            item['getflag'] = false
        }
        else {
            item['initialValue'] = undefined
        }
    })
    console.log(editModalContent.value)
}
onMounted(() => {
    getModules()
})

defineExpose({ open })
</script>
<style lang="less">
.course_type {
    margin-top: 10px;
}

.whitebreak {
    word-wrap: break-word;
    width: 200px;
}
</style>