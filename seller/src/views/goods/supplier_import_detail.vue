  <template>
    <div class="section_padding">
      <div class="section_padding_back">
        <SldComHeader :type="1" :title="'商品详情'" :back="true" />
        <Spin :spinning="initLoading">
          <div class="flex_row_center_start supplier_detail">
            <div class="flex_column_start_start content">
              <div class="flex_row_start_start">
                <div class="flex_column_start_start top_left">
                  <div class="flex_row_center_center main_img">
                    <img :src="bigImg" />
                  </div>
                  <div
                    class="small_img"
                    :class="detail.imageList != undefined && detail.imageList.length > 5 ? '' : 'flex_row_start_start'"
                  >
                    <template v-if="detail.imageList != undefined && detail.imageList.length > 5">
                      <template v-if="detail.imageList != undefined">
                        <template v-for="(item, index) in detail.imageList"  :key="index">
                          <div class="item flex_row_center_center" @mouseover="showCurImg(index)">
                            <img :src="item" />
                          </div>
                        </template>
                      </template>
                    </template>
                    <template v-else-if="detail.imageList != undefined">
                      <template v-for="(item, index) in detail.imageList" :key="index">
                        <div class="item flex_row_center_center" @mouseover="showCurImg(index)" >
                          <img :src="item" />
                        </div>
                      </template>
                    </template>
                  </div>
                </div>
                <div class="flex_column_start_start top_right">
                    <p class="name">{{ detail.goodsName }}</p>
                    <p class="desc">{{ detail.goodsBrief }}</p>
                    <div class="flex_column_start_start spec">
                      <div class="flex_row_start_center price" :style="{ backgroundImage: `url('${priceBgImg}')` }">
                        <span class="tip">供货价：</span>
                        <div class="price_detail">
                          <span class="unit">¥</span>
                          <span class="number">{{ detail.goodsPrice ? detail.goodsPrice.toFixed(2) : 0 }}</span>
                        </div>
                        <span v-if="detail.retailPrice>0" class="tip" style="margin-left: 20px">建议零售价：</span>
                        <div v-if="detail.retailPrice>0" class="price_detail">
                          <span class="unit">¥</span>
                          <span class="number">{{ detail.retailPrice.toFixed(2) }}</span>
                        </div>
                        <template v-if="detail.productList && detail.productList[0].storeProfit">
                          <span class="tip" style="margin-left: 20px">利润：</span>
                          <div class="price_detail">
                            <span class="unit">¥</span>
                            <span class="number">{{ detail.productList[0].storeProfit.toFixed(2) }}</span>
                          </div>
                        </template>
                        <span v-if="detail.jdPrice>0" class="tip" style="margin-left: 20px">主站参考价：</span>
                        <div v-if="detail.jdPrice>0" class="price_detail">
                          <span class="unit">¥</span>
                          <span class="number">{{ detail.jdPrice.toFixed(2) }}</span>
                        </div>
                      </div>
                      <div v-if="detail.goodsId" class="flex_row_start_center freight_detail">
                        <span class="tip" style="width: 75px">商品编码：</span>
                        <span class="title">{{ detail.goodsId }}</span>
                      </div>
                      <div v-if="freightInfo.type" class="flex_row_start_center freight_detail">
                        <span class="tip">运费：</span>
                        <span class="title">{{ freightInfo.typeValue }}</span>
                        <div v-if="freightInfo.type==3" class="flex_row_center_center more">
                          <img class="more_img" src="@/assets/images/arrow_right.png" />
                          <div class="more_modal">
                            <template v-for="(item,index) in freightInfo.cityInfoList" :key="index">
                              <div class="flex_row_between_center more_modal_list">
                                <div class="flex_row_start_start more_modal_city">
                                  <template v-for="(items,indexs) in item.cityList" :key="indexs">
                                    <div class="more_modal_city_item">{{ items.cityName }}</div>
                                  </template>
                                </div>
                                <div class="more_modal_desc">{{ item.description }}</div>
                              </div>
                            </template>
                          </div>
                        </div>
                      </div>
                      <div class="flex_row_start_start spec_detail">
                        <span class="tip">规格：</span>
                        <div class="flex_row_start_start" style="flex-wrap: wrap; margin-left: -10px">
                          <template v-if="detail.productList != undefined">
                            <template v-for="(item,index) in detail.productList" :key="index">
                                <div class="flex_row_center_center spec_item"
                                    :class="selectedGoodsId.indexOf(item.productId) > -1 ? 'selected_spec_item' : ''"
                                    @click="specSel(item.productId)">
                                <span class="spec_name">{{ item.specValues }}</span>
                                <span class="spec_price">{{ '¥' + (item.retailPrice ? item.productPrice.toFixed(2) : (item.productPrice ? item.productPrice.toFixed(2) : 0)) }}</span>
                                <div class="selected_spec_item_icon"
                                    :class="selectedGoodsId.indexOf(item.productId) > -1 ? 'selected_spec_item' : ''">
                                    <!-- {sldSvgIcon('#FF711E', 16, 16, 'xuanzhongjiaobiao')} -->
                                </div>
                                </div>
                            </template>
                          </template>
                        </div>
                      </div>
                    </div>
                    <div class="flex_row_start_start" style="margin-bottom: 30px">
                        <a v-if="optSuccess" class="flex_row_center_center add_to_store" @click="goBack">继续添加商品</a>
                        <a v-else class="flex_row_center_center add_to_store" @click="add(true)">上架到店铺</a>
                    </div>
                </div>
              </div>
              <div class="body_title">
                <span class="flex_row_center_center con">产品介绍</span>
              </div>
              <div class="body">
                <div v-if="detail.goodsDetails != ''" v-html="quillEscapeToHtml(detail.goodsDetails)" />
                <Empty
                  v-else
                  :image="Empty.PRESENTED_IMAGE_SIMPLE"
                  imageStyle="height: 80px"
                  description="暂无详情~"
                ></Empty>
              </div>
            </div>
          </div>
        </Spin>
      </div>
      <!-- 上架弹窗 start -->
      <Modal
        :width="600"
        :title="title"
        :visible="modalPriceVisible"
        :confirmLoading="confirmBtnLoading"
        @cancel="sldCancle"
        @ok="sldConfirm"
      >
        <div style="padding: 20px 50px">
          <div class="flex_row_start_center">
            <div style="width: 76px; text-align: right; margin-right: 10px;"><span style="color: #ff0000;">*</span>销售价：</div>
            <span style="margin-right: 8px">在供货价的基础上</span>
            <Select
              size="small"
              style="width: 70px"
              :value="modalDetail.addType"
              placeholder=""
              @select="(e) => changeValue(e, 'addType')"
              :getPopupContainer="triggerNode => triggerNode.parentNode"
            >
              <Select.Option :value="1">+</Select.Option>
              <Select.Option :value="2">-</Select.Option>
            </Select>
            <InputNumber
              size="small" :value="modalDetail.addPrice" :min="0" :max="100" :precision="2"
              style="width: 80px !important; margin-right: 8px; margin-left: 8px;" @change="(e) => changeValue(e, 'addPrice')"
            />
            <span>%作为店铺销售价</span>
          </div>
        </div>
      </Modal>
      <!-- 上架弹窗 end -->
    </div>
  </template>
  <script>
    export default {
      name: 'GoodsImportToSupplierToAdd',
    };
  </script>
  <script setup>
    import { Empty, InputNumber, Modal, Select, Spin } from 'ant-design-vue';
import { onMounted, reactive, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {
getSupplierGoodsDetail,
getSupplierGoodsFreightInfo,
getSupplierGoodsProcess,
importSupplierGoods,
} from '/@/api/goods/goods';
import priceBgImg from '/@/assets/images/goods/price_bg.png';
import SldComHeader from '/@/components/SldComHeader/index.vue';
import { useUserStore } from '/@/store/modules/user';
import { failTip, quillEscapeToHtml, sucTip } from '/@/utils/utils';
  
    const route = useRoute();
    const router = useRouter();
    const userStore = useUserStore();
    const initLoading = ref(true);
    const detail = ref({}); //平台商品资料详情
    const selectedGoodsId = ref([]); //选中的sku id数组
    const bigImg = ref(''); //大图
    const freightInfo = ref({});  //运费数据
    const optSuccess = ref(false); //将商品添加成功标识
    const width = ref(550);
    const title = ref('');
    const confirmBtnLoading = ref(false);
    const operate_type = ref('');
    const modalPriceVisible = ref(false);
    const modalDetail = reactive({
      addType: 1,
      addPrice: 10,
    })

    const timeoutEvent = ref(null)//产品导入定时任务(5s)
    const timeoutEventKey = ref('')//产品导入定时接口传参
  
    onMounted(() => {
      get_detail();
      // getFreightInfo();
    });
    
  
    //获取商品详情
    const get_detail = async () => {
      let res = await getSupplierGoodsDetail({ goodsId: route.query.id });
      initLoading.value = false;
      if (res.state == 200) {
        detail.value = res.data;
        let ids = [];
        res.data.productList.map((item) => {
          if (item.isDefault == 1) {
            ids.push(item.productId);
          }
        });
        if (ids.length == 0 && res.data.productList.length > 0) {
          ids.push(res.data.productList[0].productId);
        }
        bigImg.value = res.data.imageList[0];
        selectedGoodsId.value = ids;
      } else {
        failTip(res.msg);
      }
    };
  
    //获取运费详情
    const getFreightInfo = async () => {
      const res = await getSupplierGoodsFreightInfo({ goodsId: route.query.id });
      if (res.state == 200 && res.data) {
        freightInfo.value = res.data;
      } else {
        failTip(res.msg);
      }
    };

  
    //展示当前商品图片
    function showCurImg(index) {
      bigImg.value = detail.value.imageList[index];
    };

    //添加商品  type为导入状态，true-上架到店铺，false-添加到仓库
    function add(type) {
      title.value = type ? '上架到店铺' : '添加到仓库';
      modalPriceVisible.value = true;
    };

    //返回
    function goBack() {
      router.back();
    };

    //规格选择事件
    function specSel(sku_id) {
      if (selectedGoodsId.value.indexOf(sku_id) > -1) {
        if (selectedGoodsId.value.length == 1) {
          failTip('至少选择一个规格～');
        } else {
          selectedGoodsId.value = selectedGoodsId.value.filter(item => item != sku_id);
        }
      } else {
        selectedGoodsId.value.push(sku_id);
      }
    };

    //上架弹窗赋值
    function changeValue(e, key) {
      modalDetail[key] = e;
    };
  
    //上架弹窗取消
    function sldCancle() {
      modalPriceVisible.value = false;
      modalDetail.addType = 1;
      modalDetail.addPrice = 0;
    };
  
    //上架弹窗确定
    const sldConfirm = async () => {
      let param = {};
      if (modalDetail.addPrice == 0) {
        param.addType = 0;
        param.addPrice = 0;
      } else {
        param.addType = modalDetail.addType;
        param.addPrice = modalDetail.addPrice;
      }
      param.importState = true; //导入状态，true-上架到店铺，false-添加到仓库
      param.type = 1; //上架类型: 1-批量上架 2-批量上架筛选结果商品 3-一键上架全部商品
      param.goodsIds = detail.value.goodsId;
      confirmBtnLoading.value = true;
      const res = await importSupplierGoods(param);
      if (res.state == 200) {
        timeoutEventKey.value = res.data;
        getFileState();
        timeoutEvent.value = setInterval(() => {
          getFileState();
        }, 5000)
      } else {
        confirmBtnLoading.value = false;
        failTip(res.msg);
      }
    };

     //定时获取导入结果
     const getFileState = async () => {
      if (!timeoutEventKey.value) {
        // @ts-ignore
        clearInterval(timeoutEvent.value);
        timeoutEvent.value = null
        confirmBtnLoading.value = false;
        timeoutEventKey.value = '';
        return;
      }
      let res = await getSupplierGoodsProcess({ key: timeoutEventKey.value })
      if (res.state == 200) {
        if (res.data.process == 2) { //成功
          sucTip(res.msg);
          sldCancle();
          optSuccess.value = true;
          userStore.setDelKeepAlive([route.name, 'GoodsImportToSupplier', 'GoodsGoodsList']);
          confirmBtnLoading.value = false;
        } else if (res.data.process == 3) { //失败
          confirmBtnLoading.value = false;
          failTip(res.data.failReason);
        } else {
          return;
        }
        // @ts-ignore
        clearInterval(timeoutEvent.value);
        timeoutEvent.value = null
        timeoutEventKey.value = '';
      }else{
        failTip(res.msg);
        // @ts-ignore
        clearInterval(timeoutEvent.value);
        timeoutEvent.value = null
        confirmBtnLoading.value = false;
        timeoutEventKey.value = '';
      }
    }
  </script>
  <style lang="less">
    .supplier_detail {
      width: 100%;
      height: calc(100vh - 170px);
      margin-top: 20px;
      overflow-x: hidden;
      overflow-y: auto;
  
      .content{
        width: 1000px;
  
        .top_left{
          .main_img{
            width: 340px;
            height: 340px;
            border: 1px solid #eee;
  
            img{
              max-width: 100%;
              max-height: 100%;
            }
          }
  
          .small_img{
            width: 293px;
            margin-top: 10px;
            margin-bottom: 45px;
            margin-left:25px;
            overflow-x: auto;
            overflow-y: auto;
  
            .item{
              flex-shrink: 0;
              width: 50px !important;
              height: 50px;
              margin-left: 9px;
              border: 1px solid #eee;
  
              &:first-child{
                margin-left: 0;
              }
  
              img{
                max-width: 100%;
                max-height: 100%;
              }
            }
          }
        }
  
        .top_right{
          width: 640px;
          margin-left: 20px;
  
          .name{
            margin-top: 10px;
            margin-bottom: 20px;
            color: #404040;
            font-family: PingFangSC-Semibold, "PingFang SC";
            font-size: 16px;
            font-weight: 600;
            line-height: 25px;
          }
  
          .desc{
            margin-bottom: 20px;
            color: #404040;
            font-family: PingFangSC-Regular, "PingFang SC";
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;
          }
  
          .spec{
            .price{
              width: 640px;
              height: 81px;
              padding-left: 20px;
              background-repeat:no-repeat;
              background-size: cover;
  
              .tip{
                color: #666;
                font-family: PingFangSC-Regular, "PingFang SC";
                font-size: 14px;
                font-weight: 400;
                line-height: 21px;
              }
  
              .price_detail{
                color: #ED3142;
                font-family: PingFangSC-Medium, "PingFang SC";
                font-weight: 500;
  
                .unit{
                  font-size: 18px;
                  letter-spacing: 1px;
                }
  
                .number{
                  font-size: 25px;
                }
  
                .from_to{
                  margin: 0 6px;
                  font-size: 20px;
                ;
                }
              }
            }
  
            .spec_detail{
              width: 640px;
              margin-top: -1px;
              margin-bottom: 24px;
              padding: 15px 15px 0;
              border: 1px solid #EBEBEB;
              border-top: 0;
  
              .tip{
                flex-shrink: 0;
                width: 44px;
                height: 20px;
                margin-top: 11px;
                color: #404040;
                font-family: PingFangSC-Regular, "PingFang SC";
                font-size: 14px;
                font-weight: 400;
                line-height: 20px;
              }
  
              .spec_item{
                position: relative;
                flex-shrink: 0;
                height: 42px;
                margin-bottom: 15px;
                margin-left: 10px;
                padding: 0 10px;
                border: 1px solid #DFDFDF;
                border-radius: 2px;
                background: #FFF;
                cursor: pointer;
  
                .spec_name{
                  height: 20px;
                  color: #666;
                  font-family: PingFangSC-Regular, "PingFang SC";
                  font-size: 14px;
                  font-weight: 400;
                  line-height: 20px;
                }
  
                .spec_price{
                  height: 20px;
                  margin-left: 30px;
                  color: #333;
                  font-family: PingFangSC-Medium, "PingFang SC";
                  font-size: 14px;
                  font-weight: 500;
                  line-height: 20px;
                }
  
                .selected_spec_item_icon{
                  display: none;
                  position: absolute;
                  z-index: 2;
                  right: -1px;
                  bottom: -1px;
                  line-height: 0;
                }
              }
  
              .selected_spec_item{
                display: flex !important;
                border-color: #FA6F1E;
              }
            }
  
            .freight_detail {
              width: 640px;
              margin-top: -1px;
              padding: 26px 15px 15px;
              border: 1px solid #EBEBEB;
              border-top: none;
              border-bottom: none;
              font-family: PingFangSC-Regular, "PingFang SC";
              font-size: 14px;
              font-weight: 400;
  
              .tip {
                flex-shrink: 0;
                width: 44px;
                height: 20px;
                color: #404040;
                line-height: 20px;
              }
  
              .title {
                color: #333;
                line-height: 20px;
              }
  
              .more {
                position: relative;
                
                &:hover {
                  .more_modal {
                    display: block;
                  }
                }
  
                .more_img {
                  width: 25px;
                  height: 25px;
                  margin-left: 10px;
                  padding: 5px;
                  cursor: pointer;
                }
  
                .more_modal {
                  display: none;
                  position: absolute;
                  z-index: 99999;
                  top: 26px;
                  left: 10px;
                  width: 600px;
                  max-height: 400px;
                  overflow-x: auto;
                  background: #FFF;
                  box-shadow: 0 0 9px 1px rgb(157 157 157 / 10%);
  
                  .more_modal_list {
                    padding: 16px 15px;
                    border-top: 1px solid #DFDFDF;
  
                    &:nth-child(1) {
                      border-top: none;
                    }
  
                    .more_modal_city {
                      flex: 1;
                      flex-wrap: wrap;
                      padding-right: 10px;
                      color: #666;
                      font-family: "Microsoft YaHei";
                      font-size: 12px;
                      font-weight: 400;
  
                      .more_modal_city_item {
                        margin: 2px 8px 2px 0;
                        padding: 3px;
                        border-radius: 3px;
                        cursor: pointer;
  
                        &:hover {
                          background: #FA6F1E;
                          color: #fff;
                        }
                      }
                    }
  
                    .more_modal_desc {
                      position: relative;
                      flex-shrink: 0;
                      width: 144px;
                      padding-right: 10px;
                      padding-left: 25px;
                      color: #666;
                      font-family: "Microsoft YaHei";
                      font-size: 12px;
                      font-weight: 400;
                      line-height: 20px;
                      text-align: center;
  
                      &::before {
                        content: '';
                        display: block;
                        position: absolute;
                        top: 50%;
                        left: 0;
                        width: 1px;
                        height: 20px;
                        margin-top: -10px;
                        background: #DFDFDF;
                      }
                    }
                  }
                }
              }
            }
          }
  
          .add_to_store{
            height: 40px;
            padding: 0 18px;
            border-radius: 2px;
            background: #FA6F1E;
            color: #FFF;
            font-family: PingFangSC-Medium, "PingFang SC";
            font-size: 15px;
            font-weight: 500;
            line-height: 22px;
          }
  
          .add_to_storage{
            height: 40px;
            margin-left: 13px;
            padding: 0 18px;
            border: 1px solid #F6BD98;
            border-radius: 2px;
            background: #FFF2EA;
            color: #FA6F1E;
            font-family: PingFangSC-Medium, "PingFang SC";
            font-size: 15px;
            font-weight: 500;
            line-height: 22px;
          }
        }
  
        .body_title{
          width: 100%;
          height: 50px;
          border: 1px solid #E2E2E2;
          background: #F8F8F8;
  
          .con{
            width: 160px;
            height: 48px;
            background: #FA6F1E;
            color: #FFF;
            font-family: PingFangSC-Medium, "PingFang SC";
            font-size: 14px;
            font-weight: 500;
          }
        }
  
        .body{
          width: 100%;
          min-height: 300px;
        }
      }
    }
  </style>
  