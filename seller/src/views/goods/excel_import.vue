<template>
  <div class="section_padding goods_list excel_import">
    <div class="section_padding_back">
      <SldComHeader back title="Excel导入" />
      <ShowMoreHelpTip tipTitle="操作提示" :tipData="goodsTip" v-if="sld_show_tip"></ShowMoreHelpTip>
      <Spin :spinning="initLoading">
        <div style="padding: 40px 200px;">
          <Steps :current="curStep">
            <Step title="下载商品导入模板"></Step>
            <Step title="上传文件"></Step>
            <Step title="完成"></Step>
          </Steps>
        </div>
        <div class="goods_import excel_goods_import_box">
          <template v-if="curStep == 0">
            <div style="width: 100%;height: 10px;"></div>
            <Button type="primary" size='large' @click="downLoadMould">
              <template #icon>
                <DownloadOutlined />
              </template>
              下载商品导入模板
            </Button>
            <div style="width: 100%;height: 40px;"></div>
            <Button type="primary" size='large' @click="goUpload">下一步</Button>
          </template>
          <template v-if="curStep == 1">
            <UploadDragger listType="picture-card" name="file" accept='.xls'
              :action="`${apiUrl}${apiInterface}`" :beforeUpload="(file) => beforeUpload(file, ' .xls')"
              @change="(e) => handleFileChange(e)" :showUploadList="false" :headers="{
                Authorization: imgToken,
              }"
              >
              <div style="margin: 16px 0;height: 116px;vertical-align:middle;display: table-cell;">
                <PlusOutlined style="font-size: 32px;color: #999;" />
                <p style="margin-top: 5px;">点击上传或者拖拽文件到该区域即可</p>
              </div>
            </UploadDragger>
            <div v-if="errorFileUrl || waringFile" style="margin-top: 12px;color: rgb(51, 51, 51);text-align: center;">
              <template v-if="waringFile">
                <span v-if="errorFileReason"
                  style="color: rgb(225, 0, 0);">上传失败！{{ errorFileReason ? errorFileReason : '' }}</span>
                <span v-else>数据正在导入，导入需要一定的时间，请耐心等待～</span>
              </template>
              <template v-else>
                <span style="color: rgb(225, 0, 0);">上传失败！</span>
                <span>您可以</span>
                <a download="错误表格.xls" :href="errorFileUrl" style="color: #2878FF;">下载错误表格</a>
                <span>，查看错误原因，修改后重新上传。</span>
              </template>
            </div>
          </template>
          <template v-if="curStep == 2">
            <p class="import_success_con">导入成功！</p>
            <p class="import_success_tip">您可以前往商品列表查看已导入的商品，或是继续导入。</p>
            <div style="width: 100%;height: 2px;background: #fff;"></div>
            <Button type="primary" size='large' @click="nextUpload">继续导入</Button>
          </template>
        </div>
      </Spin>
    </div>
  </div>
</template>
<script>
export default {
  name: 'GoodsImportToExcel',
};
</script>
<script setup>
import { ref, onMounted,computed } from 'vue';
import { Spin, Step, Steps, Button, UploadDragger } from 'ant-design-vue';
import {
  DownloadOutlined
} from '@ant-design/icons-vue';
import SldComHeader from '/@/components/SldComHeader/index.vue';
import ShowMoreHelpTip from '/@/components/ShowMoreHelpTip/index.vue';
import { useGlobSetting } from '/@/hooks/setting';
import { sucTip, failTip } from '@/utils/utils';
import { useUserStore } from '/@/store/modules/user';
import {
  PlusOutlined
} from '@ant-design/icons-vue';
import { getToken } from '/@/utils/auth';
import {
  getGoodsDownLoadApi,
  getGoodsProcessApi
} from '/@/api/goods/import';
const { apiUrl } = useGlobSetting();

const goodsTip = ref(['请先下载商品导入模板，并按照批注中的要求填写商品数据，未按要求填写将会导致商品导入失败', '请选择.xls文件，每次只能导入一个文件，建议每次导入不超过6000条商品数据']);
const sld_show_tip = ref(true);
const initLoading = ref(false)
const curStep = ref(0)//步骤条当前步骤
const search_con = ref('')//搜索框内容
const waringFile = ref(false)//显示导入中提示
const errorFileReason = ref('')//错误提示内容
const errorFileUrl = ref('')//错误文件地址
const userStore = useUserStore();

const tl_flag = ref(false)

const getUserInfo = computed(() => {
  return userStore.getStoreInfo;
});

const apiInterface = ref('/v3/goods/seller/goods/import')

const timeoutEvent = ref(null)//产品导入定时任务(5s)
const timeoutEventKey = ref('')//产品导入定时接口传参

const imgToken = ref('Bearer ' + getToken())

//下载商品导入模板
const downLoadMould = async () => {
  let paramData = {};
  paramData.fileName = `商品导入模板`;
  initLoading.value = true
  let res = await getGoodsDownLoadApi(paramData)
  if (res.state != undefined && res.state == 255) {
    failTip(res.msg)
  } else {
    goUpload();//下载成功后自动到下一步
  }
  initLoading.value = false
}

//文件上传前处理数据
const beforeUpload = async(file, accept, limit) => {
  // dev_tong-start
  await toPublishProduct()
  // dev_tong-end
  imgToken.value = 'Bearer ' + userStore.getToken
  if(userStore.getToken){
    let res = await userStore.getSldCommonService()
    if(res.state == 200){
      imgToken.value = 'Bearer ' + res.token
    }
  }
  if (accept != undefined && accept != null && accept) {
    //校验文件格式类型
    let accept_list = accept
      .replaceAll(' ', '')
      .split(',')
      .filter((item) => item && item.trim());
    let file_type = file.name ? '.' + file.name.split('.').at(-1) : '';
    if (accept_list.length > 0 && file_type != '' && accept_list.indexOf(file_type) == -1) {
      failTip('上传文件格式有误');
      return false;
    }
  }
  initLoading.value = true
  errorFileUrl.value = ''
  errorFileReason.value = ''
  waringFile.value = false
}

//数据变化事件
const handleFileChange = (info) => {
  if (info.file.status != undefined && info.file.status != 'error') {
    if (info.file != undefined && info.file.response != undefined) {
      if (info.file.response.state == 200) {
        waringFile.value = true;
        timeoutEventKey.value = info.file.response.data;
        getUoloadFileState();
        timeoutEvent.value = setInterval(() => {
          getUoloadFileState();
        }, 5000)
      } else if (info.file.response.state == 267) {
        errorFileUrl.value = info.file.response.data;
        initLoading.value = false;
      } else {
        failTip(info.file.response.msg);
        initLoading.value = false;
      } 
    }
    // e.fileList.forEach((item) => {
    //   if (item.response && item.response.data) {
    //     item.url = item.response.data.url;
    //   }
    // });
    // bgFileList.value = e.fileList;
  }
}

//定时获取上传结果
const getUoloadFileState = async () => {
  if (!timeoutEventKey.value) {
    return;
  }
  let res = await getGoodsProcessApi({ key: timeoutEventKey.value })
  if (res.state == 200) {
    if (res.data.process == 2) { //成功
      waringFile.value = false
      curStep.value = 2
      errorFileReason.value = ''
      initLoading.value = false
    } else if (res.data.process == 3) { //失败
      errorFileReason.value = res.data.failReason
      initLoading.value = false
    } else {
      return;
    }
    // @ts-ignore
    clearInterval(timeoutEvent.value);
    timeoutEvent.value = null
    timeoutEventKey.value = '';
  }
}

//继续导入事件
const nextUpload = () => {
  curStep.value = 1
  errorFileUrl.value = ''
  waringFile.value = false
  errorFileReason.value = ''
}

//下一步的点击事件
const goUpload = () => {
  curStep.value = 1
};

onMounted(() => {
  // dev_supplier-start
  if(userStore.getStoreInfo.shopType&&userStore.getStoreInfo.shopType=='3'){
    apiInterface.value = '/v3/supplier/seller/supplierGoods/import'
  }else{
    apiInterface.value = '/v3/goods/seller/goods/import'
  }
  // dev_supplier-end
});
</script>
<style lang="less">
@import '/@/assets/css/goods.less';

.excel_import {
  .excel_goods_import_box {
    text-align: center;

  }

  .ant-steps-item-finish .ant-steps-item-icon {
    line-height: 38px;
  }

  .ant-steps .ant-steps-item:not(.ant-steps-item-active)>.ant-steps-item-container[role='button'] {
    cursor: auto;
  }
}
</style>
