<template>
  <div class="section_padding goods_list">
    <div class="section_padding_back">
      <SldComHeader
        :title="L('商品管理')"
      />
      <Tabs type="card" v-model:activeKey="activeKey" class="tabs_nav">
        <TabPane key="1" :tab="L('在售列表')">
          <GoodsOnlineLists v-if="activeKey=='1'"></GoodsOnlineLists>
        </TabPane>
        <TabPane key="4" :tab="L('违规下架商品')">
          <GoodsOfflineLists  v-if="activeKey=='4'"></GoodsOfflineLists>
        </TabPane>

      </Tabs>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'GoodsGoodsList',
  };
</script>
<script setup>
  import { getCurrentInstance, ref } from 'vue';
  import { Tabs, TabPane } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import GoodsOnlineLists from './goods_online_lists.vue';
  import GoodsOfflineLists from './goods_offline_lists.vue';
  import { useRoute } from 'vue-router';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;
  const route = useRoute();
  const activeKey = ref(route.query.tab ? route.query.tab : '1');
</script>
<style lang="less">
  .goods_list {
    .tabs_nav {
      .ant-tabs-nav {
        margin-bottom: 0;
      }
    }
  }
</style>
