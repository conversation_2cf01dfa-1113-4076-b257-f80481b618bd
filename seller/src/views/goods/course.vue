<template>
  <div class="section_padding point_goods_list">
    <div class="section_padding_back">
      <SldComHeader :title="L('课程管理')" />
      <div class="goods_course">
        <BasicTable @register="registerTable" style="padding: 0">
          <template #tableTitle>
            <div class="toolbar">
              <div class="toolbar_btn" @click="toPublishProduct">
                <AliSvgIcon iconName="icondaoru" width="17px" height="17px" fillColor="red" />
                <span>{{ L('发布') }}</span>
              </div>
              <Popconfirm :title="L('确认删除选中的课程吗？')" @confirm="operateGoods(null, 'del')"
                :disabled="selectedRowKeys.length == 0">
                <div class="toolbar_btn" @click="() => selectedRowKeys.length == 0 ? operateGoods(null, 'del') : null">
                  <AliSvgIcon iconName="iconpiliangshanchu" width="15px" height="15px" fillColor="#F21414" />
                  <span>{{ L('删除') }}</span>
                </div>
              </Popconfirm>
              <!-- <Popconfirm :title="L('确认将选中的课程设置为推荐课程吗？')" @confirm="goodsRecommend(null, 'recommend')"
                :disabled="selectedRowKeys.length == 0">
                <div class="toolbar_btn"
                  @click="() => selectedRowKeys.length == 0 ? goodsRecommend(null, 'recommend') : null">
                  <AliSvgIcon iconName="iconnav-tuijian" width="15px" height="15px" fillColor="#FFA70F" />
                  <span>{{ L('设置推荐') }}</span>
                </div>
              </Popconfirm>
              <Popconfirm :title="L('确认将选中的课程取消推荐吗？')" @confirm="goodsRecommend(null, 'cancelRecommend')"
                :disabled="selectedRowKeys.length == 0">
                <div class="toolbar_btn"
                  @click="() => selectedRowKeys.length == 0 ? goodsRecommend(null, 'cancelRecommend') : null">
                  <AliSvgIcon iconName="iconquxiaotuijian2" width="15px" height="15px" fillColor="#0F419C" />
                  <span>{{ L('取消推荐') }}</span>
                </div>
              </Popconfirm> -->
              <!-- <div class="toolbar_btn" @click="openSgAddCategoryModal">
                <AliSvgIcon iconName="icondaoru" width="17px" height="17px" fillColor="orange" />
                <span>{{ L('添加类型') }}</span>
              </div> -->
              <div class="toolbar_btn" @click="openCollect">
                <AliSvgIcon iconName="icondaoru" width="17px" height="17px" fillColor="#67c23a" />
                <span>{{ L('添加合集') }}</span>
              </div>
              <!-- <div class="toolbar_btn" @click="handleClick(null, 'export')">
                <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#67c23a" />
                <span>{{ L('导入课程') }}</span>
              </div> -->
            </div>
          </template>
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.key == 'action'">
              <div class="goods_course_action">
                <span class="common_page_edit cursor-pointer hover:text-[#FF6A12]"
                  @click="handleClick(record, 'look')">{{ L('查看详细') }}</span>
                <span class="goods_edit cursor-pointer hover:text-[#FF6A12]" @click="handleClick(record, 'edit')">{{
                  L('编辑') }}</span>
              </div>
            </template>
            <template v-else-if="column.key == 'image'">
              <div class="goods_list_mainIamge">
                <Popover placement="right">
                  <template #content>
                    <div class="goods_list_mainIamge_pop">
                      <img :src="text" />
                    </div>
                  </template>
                  <div class="goods_list_leftImage" :style="{ backgroundImage: `url('${text}')` }"></div>
                </Popover>
              </div>
            </template>
            <template v-else-if="column.key == 'content'">
              <div class="">
                <Popover placement="right" trigger="click">
                  <template #content>
                    <div class="">
                      {{ text }}
                    </div>
                  </template>
                  <div class="">{{ text }}</div>
                </Popover>
              </div>
            </template>
            <template v-else-if="column.key == 'recommend_strength'">
              {{ text > 0 ? '是' : '否' }}
            </template>
            <template v-else-if="column.key == 'goods_id'">
              {{ text ? filterGoods(text) : '-' }}
            </template>
            <template v-else-if="column.key == 'updated_at'">
              {{ sldToNormalDate(text * 1000) }}
            </template>
            <template v-else-if="column.key">
              {{ text !== undefined && text !== null ? text : '--' }}
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <!-- 课程信息 -->
    <BasicDrawer width="600" :visible="drawerVisible" @close="closeDrawer">
      <template #title>
        基本信息
      </template>
      <div class="course_detail_cont">
        <Descriptions :bordered="true" :labelStyle="{ width: '150px' }" :column="1" size="middle">
          <DescriptionsItem :label='item.label' v-for="(item, index) in courseBase1" :key="index">
            {{ item.keyAlias ? filterAlias(item.key, record[item.key], item.keyAlias) : record[item.key] }}
          </DescriptionsItem>
        </Descriptions>
      </div>
    </BasicDrawer>

    <!-- 文件导入 -->
    <Modal v-model:visible="importVisible" :width="600" @cancel="importVisible = false" @ok="handleUplad">
      <template #title>导入课程</template>
      <div class="course-upload">
        <UploadDragger name="file" v-model:fileList="fileList" :maxCount="1" :before-upload="beforeUpload">
          <div class="flex_column_center_center" style="height: 120px">
            <PlusOutlined :style="{ fontSize: '32px', color: '#999999' }" />
            <p style="margin-top: 4px">点击上传或者拖拽文件到该区域即可</p>
          </div>
        </UploadDragger>
      </div>
    </Modal>

    <!-- 合集 -->
    <CourseCollect ref="courseCollect" @getData="getCollect"></CourseCollect>
  </div>
</template>
<script>
export default {
  name: 'Course',
};
</script>
<script setup>
import { getCurrentInstance, ref, onMounted, computed, watch } from 'vue';
import { Popover, Popconfirm, Modal, UploadDragger } from 'ant-design-vue';
import { sldToNormalDate, failTip } from '@/utils/utils';
import { BasicTable, useTable } from '/@/components/Table';
import { aiQuestionList, aiQuestionBatchDelete, aiModuleList, aiQuestionImport } from '/@/api/goods/course'

import { useRouter, useRoute } from 'vue-router';
import { useUserStore } from '/@/store/modules/user';
import { selectRadio, SelectAll } from '/@/utils/utils';
import SldComHeader from '/@/components/SldComHeader/index.vue';
import BasicDrawer from '/@/components/Drawer/src/BasicDrawer.vue';
import CourseCollect from './components/CourseCollect.vue'

import { Descriptions, DescriptionsItem } from 'ant-design-vue';

const vm = getCurrentInstance();
const L = vm?.appContext.config.globalProperties.$sldComLanguage;

const props = defineProps({
  canResize: { type: Boolean, default: true },
});
const route = useRoute();
const canResize = computed(() => {
  return props.canResize;
});

watch(canResize, () => {
  redoHeight();
});

watch(
  () => route.path,
  (to, from) => {
    if (from == '/goods/course_detail') {
      reload()
    }
  }
);
// 搜索区域字段配置
const search_form_schema = ref([
  {
    field: 'topic',
    component: 'Input',
    colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
    componentProps: {
      minWidth: 300,
      placeholder: L('请输入课程名称'),
      size: 'default',
    },
    label: L('课程名称'),
    labelWidth: 80,
  },
  {
    field: 'up_down',
    component: 'Select',
    colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
    componentProps: {
      placeholder: L('请选择上架状态'),
      minWidth: 300,
      size: 'default',
      options: [
        { label: L('全部'), value: '' },
        { label: L('是'), value: 1 },
        { label: L('否'), value: 2 },
      ],
    },
    label: L('上架状态'),
    labelWidth: 80,
  }, {
    field: 'is_recommend',
    component: 'Select',
    colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
    componentProps: {
      placeholder: L('请选择推荐状态'),
      minWidth: 300,
      size: 'default',
      options: [
        { label: L('全部'), value: '' },
        { label: L('是'), value: 1 },
        { label: L('否'), value: 0 },
      ],
    },
    label: L('是否推荐'),
    labelWidth: 80,
  },
]);
//表单展示字段
const columns = ref([
  // {
  //   title: L('课程类型'),
  //   dataIndex: 'course_type',
  //   width: 80,
  // },
  {
    title: L('封面'),
    dataIndex: 'image',
    width: 100,
  },
  {
    title: L('课程名称'),
    dataIndex: 'topic',
    width: 250,
  },
  {
    title: L('课程合集'),
    dataIndex: 'goods_id',
    width: 150,
  },
  {
    title: L('是否推荐'),
    dataIndex: 'recommend_strength',
    width: 80,
  },
  {
    title: L('期数'),
    dataIndex: 'issue',
    width: 80,
  },
  {
    title: L('商品ID'),
    dataIndex: 'product_id',
    width: 140,
  },
  {
    title: L('价格'),
    dataIndex: 'price',
    width: 80,
  },
  {
    title: L('库存'),
    dataIndex: 'stock',
    width: 80,
  },
  {
    title: L('更新时间'),
    dataIndex: 'updated_at',
    width: 180,
  },
]);
const userStore = useUserStore();
const router = useRouter();
const selectedRowKeys = ref([]);
const selectedRows = ref([]);

const [registerTable, { reload, redoHeight }] = useTable({
  rowKey: 'id',
  api: (arg) => aiQuestionList({ ...arg }),
  fetchSetting: {
    pageField: 'index',
    sizeField: 'size',
    listField: 'data.list',
    totalField: 'data.total',
  },
  columns: columns,
  actionColumn: {
    width: 120,
    title: L('操作'),
    dataIndex: 'action',
  },
  useSearchForm: true,
  formConfig: {
    // @ts-ignore
    schemas: search_form_schema,
  },
  // bordered: true,
  striped: false,
  rowSelection: {
    type: 'checkbox',
    selectedRowKeys: selectedRowKeys,
    onSelect: onSelect,
    onSelectAll: onSelectAll
  },
  clickToRowSelect: false,
});


// 操作：del批量删除
const operateGoods = async (record, type) => {
  let res;
  if (type == 'del') {
    if (selectedRowKeys.value.length == 0) {
      failTip(L('请先选中数据'))
      return
    }
    res = await aiQuestionBatchDelete({ question_ids: selectedRowKeys.value })
  }
  if (res.code == 0) {
    failTip(res.msg)
    selectedRowKeys.value = []
    selectedRows.value = []
    reload()
  } else {
    failTip(res.msg)
  }
}
const fileList = ref([])
const importVisible = ref(false)
const drawerVisible = ref(false)
const record = ref({})
const modulesList = ref([])
const courseBase1 = ref(
  [
    {
      label: '所属级别', key: 'level', keyAlias: [{
        id: 1, name: '初级'
      },
      {
        id: 2, name: '中级'
      }, {
        id: 3, name: '高级'
      }]
    },
    { label: '所属模块', key: 'module', keyAlias: [] },
    { label: '引导词', key: 'guide_words' },
    { label: '内容', key: 'content' },
    { label: '督导师指导答案', key: 'reference_answer' },
    { label: '出题人', key: 'questioner' }
  ]
)
const filterAlias = (key, value, keys) => {
  return keys.filter(item => item['id'] == value)[0]['name']
}
//表格点击回调事件
const handleClick = async (item, type) => {
  if (type == 'edit') {
    router.push(`/goods/course_detail?id=${item.id}`);
  } else if ((type == 'look')) {
    drawerVisible.value = true
    record.value = item
  } else if ((type == 'export')) {
    importVisible.value = true
  }
};
const closeDrawer = () => {
  drawerVisible.value = false
}
// 上传
const beforeUpload = (file) => {
  fileList.value = [...fileList.value, file];
  return false;
}
const handleUplad = async () => {
  const formdata = new FormData();
  console.log(fileList.value[0])
  formdata.append("file", fileList.value[0]['originFileObj']);
  aiQuestionImport(formdata).then(res => {
    failTip('上传成功')
    reload()
    importVisible.value = false
  })
}
// 单选多选事件
function onSelect(record, selected) {
  let rows = selectRadio(
    selectedRowKeys.value,
    selectedRows.value,
    'id',
    record,
    selected,
  );
  selectedRowKeys.value = rows.selectedRowKeys;
  selectedRows.value = rows.selectedRows;
}

// 全选按钮事件
function onSelectAll(selected, rows, changeRows) {
  let rowAll = SelectAll(
    selectedRowKeys.value,
    selectedRows.value,
    'id',
    selected,
    rows,
    changeRows,
  );
  selectedRowKeys.value = rowAll.selectedRowKeys;
  selectedRows.value = rowAll.selectedRows;
}

// 去发布商品
const toPublishProduct = async () => {
  userStore.setDelKeepAlive(['GoodsListToAdd'])
  router.push({
    path: '/goods/course_detail'
  })
}
const courseCollect = ref(null)
const openCollect = () => {
  courseCollect.value?.open()
}
const getModules = async () => {
  const res = await aiModuleList({});
  modulesList.value = res.data.list
  courseBase1.value.forEach(item => {
    if (item.key == 'module') {
      item.keyAlias = modulesList.value
    }
  })
  console.log(modulesList.value)
}
const collectData = ref([])
const getCollect = (data) => {
  collectData.value = data
}
const filterGoods = (value) => {
  return collectData.value.filter(item => value == item.id)[0]['goods_name']
}
onMounted(() => {
  getModules()
});


</script>
<style lang="less">
.goods_course {
  .goods_course_action {
    display: flex;
    align-items: center;
    justify-content: center;

    .goods_edit {
      margin-left: 5px;
      cursor: pointer;
    }
  }
}

.course-upload {
  padding: 15px;
}
</style>
