<template>
  <div class="goods_list_to_add">
    <Spin :spinning="spinning">
      <div class="section_padding goods_list">
        <div class="section_padding_back">
          <SldComHeader :title="(id !== null && id) ? L('编辑商品') : L('发布商品')" />
          <div class="plateform_add">
            <div class="flex_row_between_center plateform_add_step">
              <div class="flex_row_start_center plateform_add_step_item" :class="step >= 0 ? 'active' : ''"
                @click="nextStep(0)">
                <div class="flex_row_center_center plateform_add_step_item_num">1</div>
                <div class="flex_column_center_start plateform_add_step_item_info">
                  <div class="plateform_add_step_item_title">商品分类</div>
                  <div class="plateform_add_step_item_desc">请选择商品分类,只有三级分类才能发布商品</div>
                </div>
              </div>
              <div class="flex_row_start_center plateform_add_step_item" :class="step >= 1 ? 'active' : ''"
                @click="nextStep(1)">
                <div class="flex_row_center_center plateform_add_step_item_num">2</div>
                <div class="flex_column_center_start plateform_add_step_item_info">
                  <div class="plateform_add_step_item_title">基本信息</div>
                  <div class="plateform_add_step_item_desc">填写商品基本信息,品牌信息以及筛选属性</div>
                </div>
              </div>
              <div class="flex_row_start_center plateform_add_step_item" :class="step >= 2 ? 'active' : ''"
                @click="nextStep(2)">
                <div class="flex_row_center_center plateform_add_step_item_num">3</div>
                <div class="flex_column_center_start plateform_add_step_item_info">
                  <div class="plateform_add_step_item_title">商品详情</div>
                  <div class="plateform_add_step_item_desc">设置规格信息,上传商品图片并完善商品详情</div>
                </div>
              </div>
            </div>
            <!-- sg-start -->
            <div class="toolbar flex flex-row">
              <div style="color:#f09962;margin-left: 10px;margin-right: 10px;">没有想要的类目?👉</div>
              <div class="toolbar_btn" @click="openSgAddCategoryModal">
                <AliSvgIcon iconName="iconxinzeng" width="20px" height="20px" fillColor="#fff" />
                <span style="color:red">{{ L('申请经营类目') }}</span>
              </div>
            </div>
            <SgAddCategory :visible="sgAddCategoryVisible" :closeCb="closeSgAddCategoryModal"></SgAddCategory>
            <!-- sg-start -->
            <!-- 商品分类 -->
            <div :style="{ display: step == 0 ? 'flex' : 'none' }" class="flex_column_start_center plateform_add_category">
              <div class="flex_row_between_center plateform_add_category_list">
                <div v-for="(item, index) in categoryArr" :key="index" class="plateform_add_category_item">
                  <template v-if="item.length > 0">
                    <div v-for="(items, indexs) in item" :key="indexs" 
                      :class="categorySelect[index] && categorySelect[index].categoryId == items.categoryId ? 'active' : ''" 
                      @click="selectCategory(items, index)">
                      <div class="category-item-content">
                        <span>{{ items.categoryName }}</span>
                        <div v-if="index === 2" class="commission-rate">
                          <span class="commission-label">类目服务费</span>
                          <span :class="getScalingClass(items.scaling)">
                            {{ formatScaling(items.scaling) }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </template>
                </div>
              </div>
              <div class="plateform_add_category_path" :class="categorySelect[0] ? 'active' : ''">您选择商品类别是：<span
                  v-if="categorySelect[0]">{{ categorySelect[0].categoryName }}</span><span v-if="categorySelect[1]"> > {{
                    categorySelect[1].categoryName }}</span><span v-if="categorySelect[2]"> > {{
      categorySelect[2].categoryName }}</span></div>
              <div class="plateform_add_category_next" @click="nextStep(1)">下一步，填写商品信息</div>
            </div>
            <!-- 基本信息 -->
            <div :style="{ display: step == 1 ? 'block' : 'none', height: scrollHeight }" class="plateform_add_base">
              <ScrollContainer ref="scrollBase">
             <!-- 基本信息 -->
                <StandardFormRow :width="'100%'" :left_width="130" :data="baseData" :valida="baseValida"
                  @valida-event="handleValida" @callback-event="(e) => handleChange(e, 'baseData')"
                  @category-select-event="nextStep(0)" hideBorder="bottom" />

                <!-- 物流信息 -->
                <StandardFormRow v-if="isVirtualGoods == 1" id="logistics" :width="'100%'" :left_width="130" :data="
                  // dev_supplier-start
                  goodsSource == 2 ? showLogisticsData :
                    // dev_supplier-end
                    logisticsData" :valida="logisticsValida" @valida-event="handleLogisticsValida"
                  @callback-event="(e) => handleChange(e, 'logisticsData')" hideBorder="bottom" />
                <!-- sg-start -->
                <!-- 新增部分：当选择运费类型为2(设置运费模板)时显示添加运费模板按钮 -->
                <div
                  v-if="isVirtualGoods == 1 && logisticsData.find(item => item.key === 'freightType' && item.value === 2)"
                  style="margin: 10px 0 10px 130px;">
                  <button class="toolbar_btn" style="color: #fc701e; border: 1px solid #fc701e; margin-left: 26px;"
                    @click="goAddFreightTemplate">
                  去添加运费模板
                  </button>
                </div>
                <!-- sg-end -->

                <!-- 其他信息 -->
                <StandardFormRow :width="'100%'" :left_width="130" :data="extraData" :valida="extraValida"
                  @valida-event="handleExtraValida" @callback-event="(e) => handleChange(e, 'extraData')"
                  @tree-select-change-event="handleTreeSelect" @reserve-Info-Event="handleReserveInfo"
                  hideBorder="bottom" />

                <!-- 检索属性 -->
                <StandardFormRow v-if="attrData.length > 1" :width="'50%'" :left_width="130" :data="attrData"
                  @callback-event="(e) => handleChange(e, 'attrData')" hideBorder="right" />
                <!-- 店铺自定义属性 -->
                <StandardFormRow :width="'100%'" :left_width="130" :data="customData"
                  @callback-event="(e) => handleChange(e, 'customData')" hideBorder="bottom" />
                <!-- 发票信息 -->
                <StandardFormRow :width="'100%'" :left_width="130" :data="invoiceData"
                  @callback-event="(e) => handleChange(e, 'invoiceData')" hideBorder="bottom" />
                <!-- bottom占位 -->
                <div style="width: 100%;height: 80px;"></div>
              </ScrollContainer>
            </div>
            <!-- 商品详情 -->
            <div :style="{ display: step == 2 ? 'block' : 'none', height: scrollHeight }" class="plateform_add_info">
              <ScrollContainer ref="scrollGood">
                <!-- 商品规格 -->
                <StandardFormRow :width="'100%'" :left_width="160" :data="
                  // dev_supplier-start
                  goodsSource == 2 ? [{ type: 'title', label: '商品规格' }] :
                    // dev_supplier-end
                    specData" @callback-event="(e) => handleChange(e, 'specData')" @goods-spec-event="handleSpec" />
                <!-- dev_supplier-start -->
                <!-- 销售信息 -->
                <template v-if="getUserInfo.shopType && getUserInfo.shopType == '3'">
                  <StandardFormRow :width="'100%'" :left_width="160" :data="levelPriceData"
                    @callback-event="(e) => handleChange(e, 'levelPriceData')" @goods-spec-event="handleSpec"
                    @level-add="addLevelPrice" @level-delete="delLevelPrice" @level-change="handleChangePrice"
                    @level-blur="handleBlurPrice" />
                </template>
                <!-- dev_supplier-end -->
                <Form id="goodsTable" ref="formRef" :model="dataSourceForm">
                  <BasicTable @register="standardTable" @change="paginationChange">
                    <template #headerCell="{ column }">
                      <template v-if="column.key == 'productPrice'">
                        <div class="table_header">
                          <span class="table_header_xing">*</span>
                          <span>{{ column.customTitle }}</span>
                          <Tooltip placement="bottom" title="商品价格须低于市场价"
                            v-if="(getUserInfo.shopType && getUserInfo.shopType != 3) || !getUserInfo.shopType">
                            <AliSvgIcon iconName="iconwen" width="14px" height="14px" fillColor="#bfbbba" />
                          </Tooltip>
                        </div>
                      </template>
                      <template
                        v-else-if="column.key == 'productStock' || column.key == 'wholesalePrice' || column.key == 'minBuyNum' || column.key == 'takeOffNum'">
                        <div class="table_header">
                          <span class="table_header_xing">*</span>
                          <span>{{ column.customTitle }}</span>
                        </div>
                      </template>
                      <template v-else-if="column.key == 'barCode'">
                        <div class="table_header">
                          <span>{{ column.customTitle }}</span>
                          <Tooltip placement="bottom" title="虚拟商品无需填写">
                            <AliSvgIcon iconName="iconwen" width="14px" height="14px" fillColor="#bfbbba" />
                          </Tooltip>
                        </div>
                      </template>
                      <template v-else>
                        {{ column.customTitle }}
                      </template>
                    </template>
                    <template #bodyCell="{ column, text, record, index }">
                      <template v-if="column.key === 'state'">
                        <Switch :disabled="record.isDefault" :checked="text == 1 ? true : false"
                          @change="(e) => handleClick(e ? 1 : 2, index, column.key)" />
                      </template>
                      <!-- dev_supplier-start -->
                      <template v-else-if="column.key == 'wholesalePrice'">
                        <template v-if="ladderType == 3">
                          <div class="flex_row_center_center">
                            <div
                              v-if="dataSourceForm.list && dataSourceForm.list[Number(index) + currentNum]
                                && dataSourceForm.list[Number(index) + currentNum].ladderPrice && dataSourceForm.list[Number(index) + currentNum].ladderPrice.length > 0"
                              style="flex: 1">
                              <template
                                v-for="(ladder_item, ladder_index) in dataSourceForm.list[Number(index) + currentNum].ladderPrice"
                                :key="ladder_index">
                                <div
                                  v-if="(ladder_item.level_price === 0 || ladder_item.level_price > 0) && (ladder_item.level_num === 0 || ladder_item.level_num > 0)"
                                  style="font-size: 12px; color: #ff0000;">
                                  ￥{{ ladder_item.level_price }}<span style="margin-left: 4px;">(≥{{ ladder_item.level_num
                                  }})</span>
                                </div>
                                <div v-else-if="ladder_index == 0">--</div>
                              </template>
                            </div>
                            <div v-else style="flex: 1">--</div>
                            <div style="width: 30px; color: #0f60cd; font-size: 13px; text-align: center; cursor: pointer;"
                              @click="changeLadderPrice(Number(index) + currentNum)">修改</div>
                          </div>
                        </template>
                        <template v-else>
                          <FormItem :name="['list', Number(index) + currentNum, 'wholesalePrice']"
                            :rules="isRequireInquire == 1 ? [] : [{ required: true, message: '该项必填' }]">
                            <InputNumber :disabled="isRequireInquire == 1 ? true : false" :min="0.01" :max="999999"
                              :precision="2" placeholder="" :value="text > 0 ? text : undefined"
                              @change="(e) => handleClick(e, index, column.key)" />
                          </FormItem>
                        </template>
                      </template>
                      <template v-else-if="column.key == 'minBuyNum' || column.key == 'takeOffNum'">
                        <FormItem :name="['list', Number(index) + currentNum, `${column.key}`]"
                          :rules="[{ required: true, message: '该项必填' }]">
                          <InputNumber :min="1" :max="99999999" :precision="0" placeholder=""
                            :value="text !== undefined ? text : undefined"
                            @change="(e) => handleClick(e, index, column.key)"
                            @blur="handleBlur(Number(index) + currentNum, column.key)" />
                        </FormItem>
                      </template>
                      <template v-else-if="column.key == 'retailPrice'">
                        <FormItem :name="['list', Number(index) + currentNum, 'retailPrice']">
                          <InputNumber :min="0.01" :max="999999" :precision="2" placeholder="" :value="text"
                            @change="(e) => handleClick(e, index, column.key)" />
                        </FormItem>
                      </template>
                      <template v-else-if="column.key == 'costPrice'">
                        <FormItem :name="['list', Number(index) + currentNum, 'costPrice']">
                          <InputNumber :min="0.01" :max="999999" :precision="2" placeholder="" :value="text"
                            @change="(e) => handleClick(e, index, column.key)" />
                        </FormItem>
                      </template>
                      <!-- dev_supplier-end -->
                      <template v-else-if="column.key == 'isDefault'">
                        <Checkbox :checked="text == 1 ? true : false"
                          @change="(e) => handleClick(e.target.checked ? 1 : 0, index, column.key)" />
                      </template>
                      <template v-else-if="column.key == 'marketPrice'">
                        <InputNumber :min="0.01" :max="999999" :precision="2" placeholder=""
                          :value="text ? text : undefined" @change="(e) => handleClick(e, index, column.key)" />
                      </template>
                      <template v-else-if="column.key == 'productPrice'">
                        <FormItem :name="['list', Number(index) + currentNum, 'productPrice']"
                          :rules="[{ required: true, message: '该项必填' }]">
                          <InputNumber :min="0.01" :max="999999" :precision="2" placeholder="" :value="text"
                            @change="(e) => handleClick(e, index, column.key)" />
                        </FormItem>
                      </template>
                      <template v-else-if="column.key == 'productStock'">
                        <FormItem :name="['list', Number(index) + currentNum, 'productStock']"
                          :rules="[{ required: true, message: '该项必填' }]">
                          <InputNumber :disabled="goodsSource == 2 ? true : false" :min="0" :max="99999999" :precision="0"
                            placeholder="" :value="text !== undefined ? text : undefined"
                            @change="(e) => handleClick(e, index, column.key)" />
                        </FormItem>
                      </template>

                      <template v-else-if="column.key == 'weight' ||
                        column.key == 'length' ||
                        column.key == 'width' ||
                        column.key == 'height'
                        ">
                        <InputNumber :min="0.001" :max="999999" :precision="3" placeholder="" :value="text"
                          @change="(e) => handleClick(e, index, column.key)" />
                      </template>
                      <template v-else-if="column.key == 'productStockWarning'">
                        <InputNumber :min="0" :max="300" :precision="0" placeholder=""
                          :value="(text > 0 || text === 0) ? text : undefined"
                          @change="(e) => handleClick(e, index, column.key)" />
                      </template>
                      <template v-else-if="column.key == 'barCode'">
                        <Input :value="text !== undefined ? text : undefined" placeholder=""
                          @change="(e) => handleClick(e.target.value, index, column.key)" />
                      </template>
                      <template v-else-if="column.key == 'goodsCode'">
                        <Input :value="text !== undefined ? text : undefined" placeholder=""
                          @change="(e) => handleClick(e.target.value, index, column.key)" />
                      </template>
                      <template v-else-if="column.key">
                        {{ text ? text : '--' }}
                      </template>
                    </template>
                  </BasicTable>
                </Form>
                <!-- 商品图片 - spu  -->
                <StandardFormRow id="goodsImg" v-if="!skuImgFlag" :width="'100%'" :left_width="160" :data="goodsImgData"
                  @callback-event="(e) => handleChange(e, 'goodsImgData')" @material-event="handleMaterial"
                  @change-upload="handleChangeUpload" />
                <!-- 商品图片 - sku -->
                <StandardFormRow id="goodsImg" v-else :width="'100%'" :left_width="160" :data="goodsSkuImgData"
                  @callback-event="(e) => handleChange(e, 'goodsSkuImgData')" @material-event="handleMaterial"
                  @change-upload="handleChangeUpload" />
                <!-- 商品视频 -->
                <StandardFormRow :width="'100%'" :left_width="160" :data="videoData"
                  @callback-event="(e) => handleChange(e, 'videoData')" @material-event="handleMaterial"
                  @material-delete="deleteMaterial" @change-upload="handleChangeUpload" />
                <!-- 商品详情描述 -->
                <StandardFormRow :width="'100%'" :left_width="160" :data="descData"
                  @callback-event="(e) => handleChange(e, 'descData')" hideBorder="bottom" />
                <div style="margin: -6px 8px 12px">
                  <SldUEditor v-if="goodsSource != 2 && editorFlag" :id="'goodsInfo'" :initEditorContent="initEditorContent"
                    :getContentFlag="getEditorContentFlag" :getEditorContent="getEditorContent" />
                  <!-- dev_supplier-start -->
                  <div v-else v-html="quillEscapeToHtml(initEditorContent)"></div>
                  <!-- dev_supplier-end -->
                </div>

                <!-- 图片素材选择 start  -->
                <SldMaterialImgs :visibleModal="chooseFile == 1 ? true : false"
                  :maxUploadNum="operate_type == 'goods' ? img_num : 1" :allowRepeat="true" :selectedData="selectImageData"
                  @closeMaterial="() => closeMaterial()" @confirmMaterial="(val) => confirmMaterial('image', val)" />
                <!-- 图片素材选择 end  -->

                <!-- 视频素材选择 start  -->
                <SldMaterialVideos :visibleModal="chooseFile == 2 ? true : false" :maxUploadNum="1"
                  :selectedData="selectVideoData" @closeMaterial="() => closeMaterial()"
                  @confirmMaterial="(val) => confirmMaterial('video', val)" />
                <!-- 视频素材选择 end  -->
              </ScrollContainer>
            </div>
            <div v-if="step == 1 || step == 2" class="flex_row_center_center plateform_add_bottom"
              :style="{ left: getRealWidth + 'px' }">
              <div class="plateform_add_bottom_next" @click="goBack">返回</div>
              <div v-if="step == 1" class="plateform_add_bottom_next" @click="nextStep(2)">下一步</div>
              <div v-else class="plateform_add_bottom_next" @click="nextStep(1)">上一步</div>
              <div class="plateform_add_bottom_submit" @click="saveAllData">发布</div>
            </div>
          </div>
        </div>
      </div>

      <!-- dev_supplier-start -->
      <!-- 修改阶梯价-start -->
      <Modal :destroyOnClose="true" :zIndex="999" :title="L('修改阶梯价')" :width="500" :visible="ladder_price_modal"
        @cancel="handleLadderCancle" @ok="handleLadderConfirm">
        <div class="ladder_price_modal">
          <div class="flex_row_center_center ladder_price_modal_title">
            <div class="ladder_price_modal_title_item">下单数量</div>
            <div class="ladder_price_modal_title_item">阶梯价</div>
          </div>
          <template v-for="(item, index) in ladder_price_data.data" :key="index">
            <div class="flex_row_center_center ladder_price_modal_title">
              <div class="flex_row_start_center ladder_price_modal_title_item">
                <span class="ladder_price_modal_title_item_title">≥</span>
                <InputNumber :min="0" :max="9999999" :precision="0" :disabled="index == 0 ? true : false"
                  :value="index == 0 ? 1 : (item.level_num !== undefined ? item.level_num : undefined)"
                  @change="(e) => onchangeLadderPrice(e, index, 'level_num')" style="width: 150px; height: 32px;" />
              </div>
              <div class="flex_row_start_center ladder_price_modal_title_item">
                <span class="ladder_price_modal_title_item_title">￥</span>
                <InputNumber :min="0" :max="9999999" :precision="2"
                  :value="item.level_price !== undefined ? item.level_price : undefined"
                  @change="(e) => onchangeLadderPrice(e, index, 'level_price')" style="width: 150px; height: 32px;" />
              </div>
              <div v-if="index > 0" class="ladder_price_modal_title_item_del" @click="delLadderPrice(index)">
                <AliSvgIcon fillColor="#e20e0e" width="15px" height="15px" iconName="iconshanchu7"
                  style="cursor: pointer" />
              </div>
            </div>
          </template>
          <div class="ladder_price_modal_add" @click="addLadderPrice">新增区间</div>
        </div>
      </Modal>
      <!-- 修改阶梯价-end -->
      <!-- dev_supplier-end -->
    </Spin>
  </div>
</template>
<script lang="ts" setup>
import { failTip, sucTip } from '@/utils/utils';
import { Checkbox, Form, FormItem, Input, InputNumber, Modal, Spin, Switch, Tooltip } from 'ant-design-vue';
import { computed, getCurrentInstance, onMounted, onUnmounted, reactive, ref, h } from 'vue';
import {
addPlatformGood,
dictDataListApi,
editPlatformGood,
getPlatformGoodDetail,
} from '/@/api/goods/goods';
import {
addGoodsSpec,
addGoodsSpecVal,
getCategoryAttrBrand,
getCategoryList,
getFreightTemplateList,
getGoodsSpecList,
getLabelList,
getRelatedTemplate,
getStoreAttrInfo,
getStoreAttrList,
getStoreCategoryList,
} from '/@/api/manage/manage';
import SldComHeader from '/@/components/SldComHeader/index.vue';
import { BasicTable, useTable } from '/@/components/Table';
// dev_supplier-start
import {
getSupplierGoodsFreightInfo,
} from '/@/api/goods/goods';
// dev_supplier-end
import SldMaterialImgs from '@/components/SldMaterialFiles/sldMaterialImgs.vue';
import SldMaterialVideos from '@/components/SldMaterialFiles/sldMaterialVideos.vue';
import { useGlobSetting } from "@/hooks/setting";
import { useRoute, useRouter } from 'vue-router';
import { getSettingListApi } from '/@/api/common/common';
import { ScrollContainer } from '/@/components/Container/index';
import SldUEditor from '/@/components/SldUEditor/index.vue';
import StandardFormRow from '/@/components/StandardFormRow/index.vue';
import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
import { useMultipleTabStore } from '/@/store/modules/multipleTab';
import { useUserStore } from '/@/store/modules/user';



import { calcDescartes, determineEmoji, pageClose, quillEscapeToHtml } from '/@/utils/utils';

// sg-start
import SgAddCategory from '@/components/SgAddCategory/index.vue';
// sg-end
const { apiUrl } = useGlobSetting();

const vm = getCurrentInstance();
const L = vm?.appContext.config.globalProperties.$sldComLanguage;
const route = useRoute();
const router = useRouter();
const id: any = ref(route.query.id ? route.query.id : null);
const tabStore = useMultipleTabStore();
const { getRealWidth } = useMenuSetting();
const userStore = useUserStore();

const img_num = ref(6)

const getUserInfo = computed(() => {
  return userStore.getStoreInfo;
});

const spinning = ref(true);
const step = ref(0); //发布步骤
const scrollBase = ref(null); //滚动组件
const scrollGood = ref(null); //滚动组件
const scrollHeight = ref(document.body.clientHeight - 48 - 40 - 32 - 60 - 70 - 14 + 'px'); //滚动区域高度
const click_event = ref(false); //事件防抖动
const categoryArr: any = ref([[], [], []]); //商品分类数据
const categorySelect: any = ref([null, null, null]); //已选分类
const editorFlag = ref(false);
const initEditorContent = ref(''); //百度编辑器内容
const getEditorContentFlag = ref(false); //获取百度编辑器内容标识

const goodsSource = ref(0); //商品来源：0-商家自有；1-vop商品库；2-供应商

const isVirtualGoods = ref(2); //商品类型 1-实物 2-虚拟
const reserve_info_key = ref(0);
const baseValida = ref(false);
const validaFlag = ref(true);

// sg-start
// 商品分类弹窗
const sgAddCategoryVisible = ref(false);
const openSgAddCategoryModal = () => { sgAddCategoryVisible.value = true }
const closeSgAddCategoryModal = () => { sgAddCategoryVisible.value = false }

function goAddFreightTemplate() {
  router.push('/order/express_transport_to_add');
}
// sg-end

// dev_supplier-start
const showLogisticsData = ref([
  //物流信息
  {
    type: 'title',
    label: '物流信息',
  },
  {
    type: 'show_logistics',
    label: '运费',
    value: {},
  },
]);
// dev_supplier-end

const baseData = ref([
  //基本信息
  {
    type: 'title',
    label: '基本信息',
  },
  {
    type: 'goods_category',
    require: true,
    label: '商品分类',
    key: 'categoryName',
    value: '',
    btnText: '选择商品分类',
    callback: true,
  },
  {
    type: 'radio',
    require: true,
    label: '商品类型',
    key: 'isVirtualGoods',
    desc: '编辑商品的时候无法更改商品类型，请谨慎选择',
    options: [
      { label: '虚拟商品(无需物流)', value: 2 },
      { label: '实物商品(需要物流)', value: 1 },
    ],
    value: 2,
    item_width: '100%',
    callback: true,
  },
  {
    type: 'input',
    require: true,
    label: '商品名称',
    key: 'goodsName',
    placeholder: '请输入商品名称',
    maxlength: 50,
    desc: '最多输入50个字',
    value: undefined,
    right_width: '89%',
    rules: [
      {
        required: true,
        whitespace: true,
        message: '请输入商品名称',
      },
    ],
    callback: true,
  },
  {
    type: 'input',
    label: '商品广告语',
    key: 'goodsBrief',
    placeholder: '请输入商品广告语',
    maxlength: 50,
    desc: '最多输入50个字',
    value: undefined,
    right_width: '89%',
    callback: true,
  },
  {
    type: 'select',
    label: '品牌',
    placeholder: '请选择品牌',
    key: 'brandId',
    value: undefined,
    data: [],
    allowClear: true,
    right_width: '89%',
    diy: true,
    diy_key: 'brandName',
    diy_value: 'brandId',
    callback: true,
  },
]);
const attrData = ref([
  //检索属性
  {
    type: 'title',
    label: '检索属性',
  },
]);
const customData: any = ref([]); //店铺自定义属性
const customTitleData: any = ref({
  type: 'title',
  label: '店铺自定义属性',
});
const customGroupData = ref({
  type: 'select',
  label: '属性分组',
  placeholder: '请选择属性分组',
  key: 'groupId',
  value: undefined,
  data: [],
  allowClear: true,
  item_width: '50%',
  right_width: '95%',
  diy: true,
  diy_key: 'groupName',
  diy_value: 'groupId',
  callback: true,
});
const logisticsValida = ref(false);
const logisticsData = ref([
  //物流信息
  {
    type: 'title',
    label: '物流信息',
  },
  {
    type: 'radio_button',
    label: '快递运费',
    key: 'freightType',
    value: 1,
    options: [
      { label: '设置固定运费', value: 1 },
      { label: '设置运费模板', value: 2 }
    ],
    right_width: '95%',
    callback: true,
  },
  {
    type: 'inputnum',
    label: '设置固定运费(元)',
    placeholder: '请输入运费金额',
    key: 'freightFee',
    value: undefined,
    min: 0,
    max: 999999.99,
    precision: 2,
    require: true,
    rules: [{ required: true, message: '请输入运费金额' }],
    right_width: '89%',
    callback: true,
  }
]);
const logisticsPriceData = ref({
  type: 'inputnum',
  label: '设置固定运费(元)',
  placeholder: '请输入运费金额',
  key: 'freightFee',
  value: undefined,
  min: 0,
  max: 999999.99,
  precision: 2,
  require: true,
  rules: [{ required: true, message: '请输入运费金额' }],
  right_width: '89%',
  callback: true,
});
const logisticsTemplateData = ref({
  type: 'select',
  label: '运费模板',
  placeholder: '请选择运费模板',
  key: 'freightId',
  value: undefined,
  data: [],
  right_width: '89%',
  desc_width: 350,
  allowClear: true,
  require: true,
  rules: [{ required: true, message: '请选择运费模板' }],
  desc: '商品运费计算模板，如果没有该类型模板，请先去维护运费模板',
  diy: true,
  diy_key: 'templateName',
  diy_value: 'freightTemplateId',
  callback: true,
});
const invoiceData = ref([
  //发票信息
  {
    type: 'title',
    label: '发票信息',
  },
  {
    type: 'radio',
    require: true,
    label: '是否开专票',
    key: 'isVatInvoice',
    desc: '',
    options: [
      { label: '否', value: 0 },
      { label: '是', value: 1 },
    ],
    value: 0,
    item_width: '100%',
    callback: true,
  },
]);
const extraValida = ref(false);
const extraData = ref([
  {
    type: 'title',
    label: '其他信息',
  },
  {
    type: 'upload_img',
    label: '群码',
    note: '个人或群二维码',
    key: 'goodsQRCode',
    accept: '.jpg, .jpeg, .png, .JPG, .JPEG, PNG',
    action: `${apiUrl}/v3/oss/seller/upload?source=goodsQrCode`,
    method: 'post',
    data: {},
    disabled: false,
    fileList: [],
    multiple: false,
    desc: '支持JPG/PNG,大小不超过20M',
    item_width: '100%',
    height: 150,
    callback: true,
  },
  {
    type: 'select',
    label: '商品标签',
    placeholder: '请选择商品标签',
    key: 'labelId',
    value: undefined,
    data: [],
    mode: 'multiple',
    allowClear: true,
    right_width: '89%',
    diy: true,
    diy_key: 'labelName',
    diy_value: 'labelId',
    callback: true,
  },
  {
    type: 'tree_select',
    label: '店铺分类',
    placeholder: '请选择店铺分类',
    key: 'innerLabelId',
    value: undefined,
    data: [],
    list: [],
    treeCheckable: true,
    treeDefaultExpandAll: true,
    showSearch: true,
    treeNodeFilterProp: 'title',
    allowClear: true,
    fieldNames: {
      children: 'children',
      label: 'innerLabelName',
      value: 'innerLabelId'
    },
    right_width: '89%',
    callback: true,
  },

  // {
  //   type: 'inputnum',
  //   label: '虚拟销量',
  //   placeholder: '请输入虚拟销量',
  //   key: 'virtualSales',
  //   value: 0,
  //   min: 0,
  //   max: 999999999,
  //   precision: 0,
  //   desc: '0~999999999之间的整数，默认为0',
  //   right_width: '89%',
  //   callback: true,
  // },
  {
    type: 'radio',
    label: '发布状态',
    key: 'sellNow',
    options: [
      { label: '立即售卖', value: true },
      { label: '暂不售卖，放入仓库中', value: false },
    ],
    value: true,
    item_width: '100%',
    callback: true,
  },
  {
    type: 'radio',
    label: '商品推荐',
    key: 'storeIsRecommend',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 0 },
    ],
    value: 1,
    item_width: '100%',
    callback: true,
  }
]);
const extraVirtualData = ref([
  {
    type: 'radio',
    label: '售后服务',
    key: 'afterSaleService',
    options: [
      { label: '支持退款', value: 1 },
      { label: '不支持退款', value: 0 },
    ],
    value: 1,
    item_width: '100%',
    callback: true,
  },
  {
    type: 'add_reserve_info',
    label: '用户预留信息',
    key: 'reserveInfoList',
    item_width: '100%',
    select_list: [
      { label: '手机号', value: 1 },
      { label: '身份证号', value: 2 },
      { label: '数字格式', value: 3 },
      { label: '文本格式', value: 4 },
      { label: '邮箱格式', value: 5 }
    ],
    data: [],
    reserveInfoBack: true,
  }
]);
const specData = ref([
  {
    type: 'title',
    label: '商品规格',
  },
  {
    type: 'goods_spec',
    key: 'specValue',
    label: '商品规格',
    specList: [], //规格列表数据
    selectSpecList: [], //已选规格列表
    spec_diy: true,
    spec_diy_value: 'specId',
    spec_diy_key: 'specName',
    spec_val_diy: true,
    spec_val_diy_value: 'specValueId',
    spec_val_diy_key: 'specValue',
    selectSpecIds: [], //已选规格id列表
    value: [],
  },
]);


// dev_supplier-start
const ladderType = ref(1); //阶梯价类型
const saleModel = ref(1); //销售模式
const lever_price_info_key = ref(1); //阶梯价数据下标
const ladder_price_modal = ref(false); //修改阶梯价弹窗是否显示
const ladder_price_data = reactive({ data: [], index: 0 }); //修改阶梯价弹窗数据
const levelPriceData = ref([
  {
    type: 'title',
    label: '销售信息',
  },
  {
    type: 'radio',
    key: 'saleModel',
    diy: true,
    diy_value: 'dictValue',
    diy_key: 'dictKey',
    label: '销售模式',
    height: 100,
    options: [],
    value: [],
    callback: true,
    desc_width: 400,
    disabled: false,
    desc: ['代发模式不支持设置阶梯价格，批发模式下可设置阶梯价格。', '当产品同时支持代发和批发时，代发价格为第一个阶梯对应的价格。'],
  },
  {
    type: 'radio',
    label: '阶梯价',
    key: 'ladderType',
    options: [
      { label: '不设置阶梯价', value: 1 },
      { label: '设置阶梯价，所有SKU价格都一样，以SKU选择的数量总和计算阶梯价', value: 2 },
      { label: '设置阶梯价，各个SKU单独设价，单独计算阶梯价', value: 3 },
    ],
    value: 1,
    callback: true,
    height: 130,
    hidden: true,
    flex_type: 'column',
  },
  {
    type: 'set_level_price',
    label: '设置阶梯价',
    key: 'lever_price_info',
    precision: 2,
    data: [{
      level_key: 0,
      level_num: 1,
      level_price: '',
    }],
    value: [],
    callback: true,
    hidden: true,
  }

]);

// dev_supplier-end
const imgDataTitle: any = ref({
  type: 'title',
  label: '商品图片',
});
const imgData: any = ref({
  type: 'material_file',
  require: true,
  label: '图片',
  key: 'img',
  right_width: 675,
  height: 'auto',
  upload_text: '上传图片',
  desc: '建议尺寸800px*800px的方形图片,最大限制20M,在保证图片质量的情况下图片越小加载效果越好,最多可上传6张',
  limit: 6,
  draggable: true,
  fileList: [],
  specId: '', //规格id
  specValueId: '', //规格值id
  specValue: '', //规格值
  extra_param: '', //其他数据
  item_width: '100%',
  desc_width: '100%',
  callback: true,
});
const goodsImgData: any = ref([]);
const goodsSkuImgData: any = ref([]);
const videoData: any = ref([
  {
    type: 'title',
    label: '商品视频',
  },
  {
    type: 'material_video',
    label: '商品视频',
    key: 'video',
    height: 150,
    upload_text: '上传视频',
    desc: '最大限制20M,支持mp4格式,推荐时长不低于6s,不超过90s',
    fileList: [],
    item_width: '100%',
    desc_width: '100%',
    callback: true,
  },
]);
const descData = ref([
  //商品详情描述
  {
    type: 'title',
    label: '商品详情描述',
    require: true,
    desc: '平台要求服务上新必须注明服务内容',
    desc_width: '100%'
  },
  {
    type: 'select',
    label: '顶部关联版式',
    placeholder: '--请选择--',
    key: 'relatedTemplateIdTop',
    value: undefined,
    data: [],
    allowClear: true,
    right_width: '89%',
    diy: true,
    diy_key: 'templateName',
    diy_value: 'templateId',
    callback: true,
  },
  {
    type: 'select',
    label: '底部关联版式',
    placeholder: '--请选择--',
    key: 'relatedTemplateIdBottom',
    value: undefined,
    data: [],
    allowClear: true,
    right_width: '89%',
    diy: true,
    diy_key: 'templateName',
    diy_value: 'templateId',
    callback: true,
  },
]);

const dataSourceForm = ref({
  list: [
    {
      specInfoList: [],
      // dev_supplier-start
      ladderPrice: [{
        level_key: 0,
        level_num: 1,
        level_price: '',
      }],
      minBuyNum: 1,
      takeOffNum: 1,
      // dev_supplier-end
      marketPrice: undefined,
      productPrice: undefined,
      productStock: undefined,
      weight: 1,
      length: 1,
      width: 1,
      height: 1,
      productStockWarning: undefined,
      goodsCode: undefined,
      barCode: undefined,
      state: 1,
      isDefault: 1,
    },
  ]
})
const dataSource = ref([
  {
    specInfoList: [],
    // dev_supplier-start
    ladderPrice: [{
      level_key: 0,
      level_num: 1,
      level_price: '',
    }],
    minBuyNum: 1,
    takeOffNum: 1,
    // dev_supplier-end
    marketPrice: undefined,
    productPrice: undefined,
    productStock: undefined,
    weight: 1,
    length: 1,
    width: 1,
    height: 1,
    productStockWarning: undefined,
    goodsCode: undefined,
    barCode: undefined,
    state: 1,
    isDefault: 1,
  },
]);
const base_columns = ref([
  {
    title: '市场价',
    dataIndex: 'marketPrice',
    width: 160,
  },
  {
    title: '价格',
    dataIndex: 'productPrice',
    width: 160,
  },
  {
    title: '库存',
    dataIndex: 'productStock',
    width: 160,
  },
  {
    title: '启用',
    dataIndex: 'state',
    width: 120,
  },
  {
    title: '默认选中',
    dataIndex: 'isDefault',
    width: 120,
  },
  {
    title: '重量(KG)',
    dataIndex: 'weight',
    width: 160,
  },
  {
    title: '长(CM)',
    dataIndex: 'length',
    width: 160,
  },
  {
    title: '宽(CM)',
    dataIndex: 'width',
    width: 160,
  },
  {
    title: '高(CM)',
    dataIndex: 'height',
    width: 160,
  },
  {
    title: '预警值',
    dataIndex: 'productStockWarning',
    width: 160,
  },
  {
    title: '货号',
    dataIndex: 'goodsCode',
    width: 200,
  },
  {
    title: '条形码',
    dataIndex: 'barCode',
    width: 200,
  },
]);

// dev_supplier-start
const base_supplier_columns_dai = ref([
  {
    title: '供货价',
    dataIndex: 'productPrice',
    width: 160,
  },
  {
    title: '建议零售价',
    dataIndex: 'retailPrice',
    width: 160,
  },
  {
    title: '市场价',
    dataIndex: 'marketPrice',
    width: 160,
  },
  {
    title: '成本价',
    dataIndex: 'costPrice',
    width: 160,
  },
  {
    title: '库存',
    dataIndex: 'productStock',
    width: 160,
  },
  {
    title: '启用',
    dataIndex: 'state',
    width: 120,
    slots: {
      customRender: 'state'
    }
  },
  {
    title: '默认选中',
    dataIndex: 'isDefault',
    width: 120,
  },
  {
    title: '重量(KG)',
    dataIndex: 'weight',
    width: 160,
  },
  {
    title: '长(CM)',
    dataIndex: 'length',
    width: 160,
  },
  {
    title: '宽(CM)',
    dataIndex: 'width',
    width: 160,
  },
  {
    title: '高(CM)',
    dataIndex: 'height',
    width: 160,
  },
  {
    title: '预警值',
    dataIndex: 'productStockWarning',
    width: 160,
  },
  {
    title: '供应商货号',
    dataIndex: 'goodsCode',
    width: 200,
  },
  {
    title: '条形码',
    dataIndex: 'barCode',
    width: 200,
  },
]);
// dev_supplier-end

const spec_columns = ref([]);

// 当前数据从第几条开始
const currentNum = ref(0);

const show_columns: any = ref([]);
show_columns.value = [...base_columns.value]
// dev_supplier-start
if (getUserInfo.value.shopType && getUserInfo.value.shopType == 3) {
  show_columns.value = [...base_supplier_columns_dai.value]
}
// dev_supplier-end
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  hideOnSinglePage: true,
  showQuickJumper: false,
  showSizeChanger: false,
});
const [standardTable, { updateTableData, getPaginationRef, reload }] = useTable({
  dataSource: dataSource,
  columns: show_columns,
  pagination: pagination,
  maxHeight: 320,
  canResize: false,
});

const chooseFile = ref(0);
const operate_type = ref(''); //素材中心操作类型
const selectImageData = ref({ ids: [], data: [] }); //素材中心图片数据
const selectImageData_goods = ref({ ids: [], data: [] }); //产品图片数据
const selectVideoData: any = ref({ ids: [], data: [] }); //产品视频数据
const file_index = ref(0); //用于区分同一个文件重复上传
const skuImgFlag = ref(false); //是否设置多规格图片
const operate_sku_id = ref(''); //当前操作的多规格图片id
const operate_sku_index = ref(-1); //当前操作的多规格图片下标

const formRef = ref();

//校验事件回调
function handleValida(res) {
  if (!validaFlag.value) {
    return;
  } else if (!res) {
    validaFlag.value = true;
    baseValida.value = false;
    step.value = 1;
    click_event.value = false;
    scrollBase.value?.scrollTo(0);
    return;
  }

  baseValida.value = false;
  if (isVirtualGoods.value == 1 && goodsSource.value != 2) {
    logisticsValida.value = true;
  } else if (isVirtualGoods.value == 2) {
    extraValida.value = true;
    // dev_supplier-start
  } else if (goodsSource.value == 2) {
    getEditorContent(initEditorContent.value);
    // dev_supplier-end
  } else {
    try {
      formRef.value.validate().then(() => {
        getEditorContentFlag.value = true;
      }).catch(() => {
        validaFlag.value = true;
        step.value = 2;
        click_event.value = false;
        setTimeout(() => {
          scrollGood.value?.scrollTo(Number(document.getElementById('goodsTable') ? document.getElementById('goodsTable').offsetTop : 0));
        }, 50)
        return;
      })
    } catch (err) {
      validaFlag.value = true;
      step.value = 2;
      click_event.value = false;
      setTimeout(() => {
        scrollGood.value?.scrollTo(Number(document.getElementById('goodsTable') ? document.getElementById('goodsTable').offsetTop : 0));
      }, 50)
      return;
    }
  }
}

function handleLogisticsValida(res) {
  if (!validaFlag.value) {
    return;
  } else if (!res) {
    validaFlag.value = true;
    logisticsValida.value = false;
    step.value = 1;
    click_event.value = false;
    scrollGood.value?.scrollTo(Number(document.getElementById('logistics') ? document.getElementById('logistics').offsetTop : 0));
    return;
  }

  logisticsValida.value = false;
  if (isVirtualGoods.value) {
    extraValida.value = true;
    // dev_supplier-start
  } else if (goodsSource.value == 2) {
    getEditorContent(initEditorContent.value);
    // dev_supplier-end
  } else {
    try {
      formRef.value.validate().then(() => {
        getEditorContentFlag.value = true;
      }).catch(() => {
        validaFlag.value = true;
        step.value = 2;
        click_event.value = false;
        setTimeout(() => {
          scrollGood.value?.scrollTo(Number(document.getElementById('logistics') ? document.getElementById('logistics').offsetTop : 0));
        }, 50)
        return;
      })
    } catch (err) {
      validaFlag.value = true;
      step.value = 2;
      click_event.value = false;
      setTimeout(() => {
        scrollGood.value?.scrollTo(Number(document.getElementById('logistics') ? document.getElementById('logistics').offsetTop : 0));
      }, 50)
      return;
    }
  }
}

function handleExtraValida(res) {
  if (!validaFlag.value) {
    return;
  } else if (!res) {
    validaFlag.value = true;
    extraValida.value = false;
    step.value = 1;
    click_event.value = false;
    scrollBase.value?.scrollBottom();
    return;
  }

  extraValida.value = false;
  // dev_supplier-start
  if (goodsSource.value == 2) {
    getEditorContent(initEditorContent.value);
  }
  // dev_supplier-end
  try {
    formRef.value.validate().then(() => {
      getEditorContentFlag.value = true;
    }).catch(() => {
      validaFlag.value = true;
      step.value = 2;
      click_event.value = false;
      setTimeout(() => {
        scrollGood.value?.scrollTo(Number(document.getElementById('goodsTable') ? document.getElementById('goodsTable').offsetTop : 0));
      }, 50)
      return;
    })
  } catch (err) {
    validaFlag.value = true;
    step.value = 2;
    click_event.value = false;
    setTimeout(() => {
      scrollGood.value?.scrollTo(Number(document.getElementById('goodsTable') ? document.getElementById('goodsTable').offsetTop : 0));
    }, 50)
    return;
  }
}

//表单编辑回调
function handleChange(item, key) {
  if (key == 'baseData') {
    let temp = baseData.value.filter(items => items.key == item.contentItem.key);
    if (temp.length > 0) {
      temp[0].value = item.val;
    }
    if (item.contentItem.key == 'isVirtualGoods') {
      isVirtualGoods.value = item.val;
      extraData.value = extraData.value.filter(items => items.key != 'afterSaleService' && items.key != 'reserveInfoList');
      if (item.val == 2) {
        extraData.value = JSON.parse(JSON.stringify([...extraData.value, ...extraVirtualData.value]));
      }
    }
  } else if (key == 'attrData') {
    let temp: any = attrData.value.filter(items => items.key == item.contentItem.key);
    if (temp.length > 0) {
      temp[0].value = item.val;
      temp[0].valueData = {
        attributeId: temp[0].attributeId,
        attributeName: temp[0].label,
        attributeValue: temp[0].data.filter(items => items.valueId == item.val)[0].attributeValue,
        attributeValueId: temp[0].data.filter(items => items.valueId == item.val)[0].valueId,
      };
    }
  } else if (key == 'customData') {
    if (item.contentItem.key == 'groupId') {
      customGroupData.value.value = item.val;
      if (item.val) {
        get_store_attr_info({ groupId: item.val, pageSize: 10000 }, null);
      } else {
        customData.value = JSON.parse(JSON.stringify([customTitleData.value, customGroupData.value]));
      }
    } else {
      let temp = customData.value.filter(items => items.key == item.contentItem.key);
      if (temp.length > 0) {
        temp[0].value = item.val;
      }
    }
  } else if (key == 'logisticsData') {
    let temp = logisticsData.value.filter(items => items.key == item.contentItem.key);
    if (temp.length > 0) {
      temp[0].value = item.val;
    }
    if (item.contentItem.key == 'freightType') {
      let data = logisticsData.value.filter((items) => items.key != 'freightFee' && items.key != 'freightId');
      if (item.val == 1) {
        logisticsData.value = JSON.parse(JSON.stringify([...data, logisticsPriceData.value]));
      } else {
        logisticsData.value = JSON.parse(JSON.stringify([...data, logisticsTemplateData.value]));
      }
    }
  } else if (key == 'extraData') {
    let temp = extraData.value.filter(items => items.key == item.contentItem.key);
    if (item.contentItem.key == 'goodsQRCode') {
      // 检查上传响应
      if(item.val.fileList && item.val.fileList.length > 0) {
        const file = item.val.fileList[0];
        const response = file.response;
        
        // 检查文件格式
        const allowedFormats = ['.jpg', '.jpeg', '.png', '.JPG', '.JPEG', '.PNG'];
        const fileName = file.name || '';
        const fileExt = fileName.substring(fileName.lastIndexOf('.')).toLowerCase();
        if(!allowedFormats.includes(fileExt)) {
          // 显示错误信息
          failTip('不支持的文件格式，请上传 JPG/PNG 格式的图片');
          // 清除已上传的图片
          temp[0].fileList = [];
          return;
        }
        
        // 检查上传响应
        if(response && response.state === 255) {
          // 显示错误信息
          failTip(response.msg);
          // 清除已上传的图片
          temp[0].fileList = [];
          return;
        }
      }
      temp[0].fileList = item.val.fileList;
    } else {
      if (temp.length > 0) {
        temp[0].value = item.val;
      }
    }
  } else if (key == 'descData') {
    let temp = descData.value.filter(items => items.key == item.contentItem.key);
    if (temp.length > 0) {
      temp[0].value = item.val;
    }
  } else if (key == 'invoiceData') {
    let temp = invoiceData.value.filter(items => items.key == item.contentItem.key);
    if (temp.length > 0) {
      temp[0].value = item.val;
    }
  }
  // dev_supplier-start
  else if (key == 'levelPriceData') {
    let temp = levelPriceData.value.filter(items => items.key == item.contentItem.key);
    let temps = levelPriceData.value.filter(items => items.key == 'ladderType');
    if (temp.length > 0) {
      temp[0].value = item.val;
      if (temps.length > 0 && item.contentItem.key == 'saleModel') {
        saleModel.value = item.val
        if ((item.val == 2 || item.val == 3)) {
          temps[0].hidden = false
        } else {
          temps[0].hidden = true
        }
        sale_type_cilck(ladderType.value)
      }
      if (item.contentItem.key == 'ladderType') {
        ladderType.value = item.val;
        sale_type_cilck(item.val)
      }
    }
  }
  // dev_supplier-end
}

function handleTreeSelect(val, label, extra, index, item) {
  if (item.key == 'innerLabelId') {
    extraData.value[index].list = label;
  }
  extraData.value[index].value = val;
}

function handleReserveInfo(item) {
  let temp: any = extraData.value.filter(items => items.key == 'reserveInfoList');
  if (item.type == 'add') {
    if (temp.length > 0) {
      temp[0].data.push({
        key: reserve_info_key.value,
        reserveName: '',
        reserveType: 1,
        isRequired: true,
      })
      reserve_info_key.value += 1;
    }
  } else if (item.type == 'del') {
    if (temp.length > 0) {
      temp[0].data.splice(item.index, 1);
    }
  } else if (item.type == 'edit') {
    if (temp.length > 0) {
      temp[0].data[item.index][item.key] = item.val;
    }
  }
}

//图片删除回调
function handleChangeUpload(val) {
  if (val.contentItem.key == 'img') {
    if (skuImgFlag.value && val.contentItem.index !== undefined && val.contentItem.index !== null) {
      operate_sku_index.value = val.contentItem.index - 1
    } else {
      operate_sku_index.value = -1
    }
    operate_sku_id.value = (val.contentItem.specValueId !== undefined && val.contentItem.specValueId !== null) ? val.contentItem.specValueId : '';
    if (operate_sku_index.value !== -1) {
      let temp = goodsSkuImgData.value.filter((item) => item.specValueId == operate_sku_id.value);
      if (temp.length > 0) {
        temp[0].fileList = val.file.fileList;
      }
    } else {
      let temp = goodsImgData.value.filter((item) => item.specValueId == operate_sku_id.value);
      if (temp.length > 0) {
        temp[0].fileList = val.file.fileList;
      }
    }
    if (val.file.fileList && val.file.fileList.length == 0) {
      if (operate_sku_index.value !== -1) {
        selectImageData_goods.value['selectImageData_goods' + operate_sku_index.value] = { ids: [], data: [] };
      } else {
        selectImageData_goods.value = { ids: [], data: [] };
      }
    } else if (val.file.fileList && val.file.fileList.length) {
      let ids: any = [];
      val.file.fileList.forEach(item => {
        if (item.uid.indexOf('?bindId=') != -1) {
          ids.push(Number(item.uid.split('?bindId=')[1].split('-')[0]));
        }
      })
      let arr: any = { ids: [], data: [] };
      if (operate_sku_index.value !== -1) {
        selectImageData_goods.value['selectImageData_goods' + operate_sku_index.value].ids.forEach((item, index) => {
          if (ids.indexOf(item) != -1) {
            arr.ids.push(selectImageData_goods.value['selectImageData_goods' + operate_sku_index.value].ids[index]);
            arr.data.push(selectImageData_goods.value['selectImageData_goods' + operate_sku_index.value].data[index]);
          }
        })
        selectImageData_goods.value['selectImageData_goods' + operate_sku_index.value] = arr;
      } else {
        selectImageData_goods.value.ids.forEach((item, index) => {
          if (ids.indexOf(item) != -1) {
            arr.ids.push(selectImageData_goods.value.ids[index]);
            arr.data.push(selectImageData_goods.value.data[index]);
          }
        })
        selectImageData_goods.value = arr;
      }
    }
  } else if (val.contentItem.key == 'video') {
    let temp = videoData.value.filter(item => item.key == 'video');
    if (temp.length > 0) {
      temp[0].fileList = val.file.fileList;
    }
    if (val.file.fileList && val.file.fileList.length == 0) {
      selectVideoData.value = { ids: [], data: [] };
    } else if (val.file.fileList && val.file.fileList.length) {
      let ids: any = [];
      val.file.fileList.forEach(item => {
        if (item.uid.indexOf('?bindId=') != -1) {
          ids.push(Number(item.uid.split('?bindId=')[1].split('-')[0]));
        }
      })
      let arr: any = { ids: [], data: [] };
      selectVideoData.value.ids.forEach((item, index) => {
        if (ids.indexOf(item) != -1) {
          arr.ids.push(selectVideoData.value.ids[index]);
          arr.data.push(selectVideoData.value.data[index]);
        }
      })
      selectVideoData.value = arr;
    }
  }
}

//表单素材回调
function handleMaterial(item) {
  if (item.key == 'img') {
    if (item.fileList && item.fileList.length > 0) {
      img_num.value = item.limit ? Number(item.limit) - Number(item.fileList.length) : 6
    } else {
      img_num.value = item.limit ? item.limit : 6
    }
    operate_sku_index.value = skuImgFlag.value && (item.index !== undefined && item.index !== null) ? item.index - 1 : -1;
    operate_sku_id.value = (item.specValueId !== undefined && item.specValueId !== null) ? item.specValueId : '';
    chooseFile.value = 1;
    operate_type.value = 'goods';
    if (operate_sku_index.value != -1) {
      selectImageData.value = selectImageData_goods.value['selectImageData_goods' + operate_sku_index.value];
    } else {
      selectImageData.value = selectImageData_goods.value;
    }
  } else {
    chooseFile.value = 2;
  }
}

//商品视频删除回调
function deleteMaterial() {
  let temp = videoData.value.filter(item => item.key == 'video');
  if (temp.length > 0) {
    temp[0].fileList = [];
  }
  selectVideoData.value = { ids: [], data: [] };
}

//切换步骤
function nextStep(index) {
  if (step.value === 0 && index > 0 && !categorySelect.value[2]) {
    failTip('分类必须选到第三级才可以发布商品');
    return;
  } else if (index == 1) {
    scrollBase.value?.scrollTo(0);
  } else if (index == 2) {
    scrollGood.value?.scrollTo(0);
  }
  step.value = index;
}

//发布
function saveAllData() {
  // if (click_event.value) return;
  // click_event.value = true;
  baseValida.value = true;
}

// dev_supplier-start
const dictDataList = async (val) => {
  let res = await dictDataListApi({ dictCode: 'sale_model' })
  if (res.state == 200) {

    levelPriceData.value.forEach(item => {
      if (item.key == 'saleModel') {
        let obj = []
        if (val != '3' && val) {
          obj = res.data.filter(it => it.dictKey == val)
        } else if (val == '3') {
          obj = res.data
        }
        item.options = obj
        if (!route.query.id) {
          item.value = obj[0].dictKey
          if (obj[0].dictKey != '1') {
            handleChange({ contentItem: { key: 'saleModel' }, val: obj[0].dictKey }, 'levelPriceData')
          }
        }
      }
    })
  }
}

const get_system_info = async () => {
  const res = await getSettingListApi({ str: 'supplier_sales_model' });
  if (res.state == 200) {
    if (res.data.length > 0) {
      dictDataList(res.data[0].value)
    }
  }
};
// dev_supplier-end

onMounted(() => {
  // dev_supplier-start
  if (getUserInfo.value.shopType && getUserInfo.value.shopType == 3) {
    get_system_info()
  }
  // dev_supplier-end

  if (route.query.id !== null && route.query.id) {
    id.value = route.query.id;
    step.value = 1;
    // dev_supplier-start
    levelPriceData.value.forEach(item => {
      if (item.key == 'saleModel') {
        item.disabled = true
      }
    })
    // dev_supplier-end
  } else {
    get_store_attr(null);
    get_freight_list(null, null);
    get_store_category_list(null);
    get_label_list(null);
    get_related_template(null, null);
    // sg-start
    // 新建商品时，如果是虚拟商品，追加预留信息配置
    if (isVirtualGoods.value == 2) {
      extraData.value = JSON.parse(JSON.stringify([...extraData.value, ...extraVirtualData.value]));
    }
    // sg-end

    spinning.value = false;
  }
  goodsImgData.value = JSON.parse(JSON.stringify([imgDataTitle.value, imgData.value]));
  get_category_list({ grade: 1 }, 0);
  get_spec_list();
  window.addEventListener('resize', resizeScrollHeight, true);
});

onUnmounted(() => {
  window.removeEventListener('resize', resizeScrollHeight, true);
});

function resizeScrollHeight() {
  scrollHeight.value = document.body.clientHeight - 48 - 40 - 32 - 60 - 70 - 14 + 'px';
}

//获取分类数据
const get_category_list = async (params, grade) => {
  const res: any = await getCategoryList(params);
  if (res.state == 200) {
    if (grade == 0) {
      categoryArr.value[0] = res.data;
      categoryArr.value[1] = [];
      categoryArr.value[2] = [];
    } else if (grade == 1) {
      categoryArr.value[1] = res.data;
      categoryArr.value[2] = [];
    } else {
      categoryArr.value[2] = res.data;
    }
  } else {
    failTip(res.msg);
  }
};

//获取分类下的属性和品牌数据
const get_category_attr_brand = async (params, detail) => {
  const res: any = await getCategoryAttrBrand(params);
  if (res.state == 200) {
    let brand_temp = baseData.value.filter((items) => items.key == 'brandId');
    if (brand_temp.length > 0) {
      brand_temp[0].value = detail && detail.brandId ? detail.brandId : undefined;
      brand_temp[0].data = res.data.goodsBrandList;
    }
    let newAttrData: any = [
      {
        type: 'title',
        label: '检索属性',
      },
    ];
    if (res.data.goodsAttributeList && res.data.goodsAttributeList.length > 0) {
      res.data.goodsAttributeList.map((items) => {
        let obj: any = {};
        obj.type = 'select';
        obj.label = items.attributeName;
        obj.placeholder = '请选择' + items.attributeName;
        obj.key = 'attributeId' + items.attributeId;
        obj.attributeId = items.attributeId;
        obj.data = items.attributeValueList;
        obj.allowClear = true;
        obj.right_width = '89%';
        obj.diy = true;
        obj.diy_key = 'attributeValue';
        obj.diy_value = 'valueId';
        obj.callback = true;
        if (detail) {
          let obj_temp = detail.attributeList ? detail.attributeList.filter((obj_item) => obj_item.attributeId == obj.attributeId) : [];
          if (obj_temp.length > 0) {
            obj.value = obj_temp[0].attributeValueId;
            obj.valueData = obj_temp[0];
          } else {
            obj.value = undefined;
            obj.valueData = {};
          }
        } else {
          obj.value = undefined;
        }
        newAttrData.push(obj);
      });
    }
    attrData.value = newAttrData;
  } else {
    failTip(res.msg);
  }
};

//获取店铺自定义属性
const get_store_attr = async (groupData) => {
  const res: any = await getStoreAttrList({ pageSize: 10000 });
  if (res.state == 200) {
    customGroupData.value.data = res.data.list;
    customData.value = JSON.parse(JSON.stringify([customTitleData.value, customGroupData.value]));
    if (groupData && groupData.groupId) {
      get_store_attr_info({ groupId: groupData.groupId, pageSize: 10000 }, groupData);
    }
  }
};

//获取运费模板数据
const get_freight_list = async (freightFee, freightId) => {
  const res: any = await getFreightTemplateList({ pageSize: 10000 });
  if (res.state == 200) {
    logisticsTemplateData.value.data = res.data.list;
    if ((freightFee || freightFee === 0) || freightId) {
      let data = logisticsData.value.filter((items) => items.key != 'freightFee' && items.key != 'freightId');
      if (freightId) {
        logisticsData.value = JSON.parse(JSON.stringify([...data, logisticsTemplateData.value]));
        logisticsData.value[1].value = 2;
        logisticsData.value[2].value = freightId;
      } else if (freightFee || freightFee === 0) {
        logisticsData.value = JSON.parse(JSON.stringify([...data, logisticsPriceData.value]));
        logisticsData.value[1].value = 1;
        logisticsData.value[2].value = freightFee;
      }
    }
  }
};

//获取店铺自定义属性详情
const get_store_attr_info = async (params, groupData) => {
  const res: any = await getStoreAttrInfo(params);
  if (res.state == 200) {
    let arr: any = [];
    res.data.list.map((item, index) => {
      let data: any = [];
      if (item.parameterValue && item.parameterValue.split(',').length) {
        item.parameterValue.split(',').map((items) => {
          data.push({
            parameterId: item.parameterId,
            parameterName: item.parameterName,
            parameterValue: items,
            title: items,
            value: items
          });
        })
      }
      arr.push({
        type: 'select',
        label: item.parameterName,
        placeholder: '请选择' + item.parameterName,
        key: 'groupId' + index,
        value: undefined,
        data: data,
        allowClear: true,
        item_width: '50%',
        right_width: '95%',
        callback: true,
      });
    })
    customData.value = JSON.parse(JSON.stringify([customTitleData.value, customGroupData.value, ...arr]));
    if (groupData && groupData.groupId) {
      customData.value.map((item) => {
        if (item.type !== 'title') {
          if (item.key == 'groupId') {
            item.value = groupData.groupId;
          } else {
            let group_temp = groupData.parameterList.filter((group_item) => group_item.parameterName == item.label)
            if (group_temp.length > 0) {
              item.value = group_temp[0].parameterValue;
            }
          }
        }
      })
    }
  }
};

//获取店铺分类数据
const get_store_category_list = async (innerLabel) => {
  const res: any = await getStoreCategoryList({ pageSize: 10000 });
  if (res.state == 200) {
    let temp: any = extraData.value.filter((item) => item.key == 'innerLabelId');
    if (temp.length > 0) {
      temp[0].data = res.data;
      if (innerLabel && innerLabel.length > 0) {
        temp[0].list = [];
        temp[0].value = [];
        innerLabel.map((label_item) => {
          temp[0].list?.push(label_item.innerLabelPath);
          temp[0].value?.push(Number(label_item.innerLabelId));
        })
      }
    }
  }
};

//获取商品标签
const get_label_list = async (labelData) => {
  const res: any = await getLabelList({ pageSize: 10000 });
  if (res.state == 200) {
    let temp: any = extraData.value.filter((item) => item.key == 'labelId');
    if (temp.length > 0) {
      temp[0].data = res.data.list;
      if (labelData && labelData.length > 0) {
        temp[0].value = [];
        labelData.map((label_item) => {
          temp[0].value.push(Number(label_item.labelId));
        })
      }
    }
  }
};

//选择分类
function selectCategory(item, index) {
  categorySelect.value[index] = item;
  if (index == 0) {
    categorySelect.value[1] = null;
    categorySelect.value[2] = null;
  } else if (index == 1) {
    categorySelect.value[2] = null;
  } else {
    let temp = baseData.value.filter((items) => items.key == 'categoryName');
    if (temp.length > 0) {
      temp[0].value =
        categorySelect.value[0].categoryName +
        ' > ' +
        categorySelect.value[1].categoryName +
        ' > ' +
        categorySelect.value[2].categoryName;
    }
  }
  if (index < 2) {
    get_category_list(
      { grade: index + 2, categoryId: item.categoryId },
      index + 1,
    );
  } else {
    get_category_attr_brand({ categoryId: item.categoryId, pageSize: 10000 }, null);
  }
};

//获取规格数据
const get_spec_list = async () => {
  const res: any = await getGoodsSpecList({ pageSize: 10000 });
  if (res.state == 200 && res.data) {
    let temp = specData.value.filter((item) => item.key == 'specValue');
    if (temp.length > 0) {
      res.data.list.map((item) => {
        item.value = item.specId;
        item.label = item.specName;
      });
      temp[0].specList = res.data.list;
    }
    if (id.value !== null && id.value) {
      get_goods_detail();
    } else {
      editorFlag.value = true;
    }
  } else {
    failTip(res.msg);
  }
};

//获取关联版式
const get_related_template = async (top, bottom) => {
  const res: any = await getRelatedTemplate({ pageSize: 10000 });
  if (res.state == 200 && res.data) {
    let top_temp: any = descData.value.filter((item) => item.key == 'relatedTemplateIdTop');
    if (top_temp.length > 0) {
      top_temp[0].data = res.data.list.filter((item) => item.templatePosition == 1);
      top_temp[0].value = top ? top : undefined;
    }
    let bottom_temp: any = descData.value.filter((item) => item.key == 'relatedTemplateIdBottom');
    if (bottom_temp.length > 0) {
      bottom_temp[0].data = res.data.list.filter((item) => item.templatePosition == 2);
      bottom_temp[0].value = bottom ? bottom : undefined;
    }
  } else {
    failTip(res.msg);
  }
};

//获取商品数据
const get_goods_detail = async () => {
  const res: any = await getPlatformGoodDetail({ goodsId: id.value });
  if (res.state == 200 && res.data) {
    let result = res.data;

    get_category_attr_brand({ categoryId: res.data.categoryId3, pageSize: 10000 }, result);

    if (result.parameterGroup && result.parameterGroup.groupId) {
      get_store_attr(result.parameterGroup);
    } else {
      get_store_attr(null);
    }

    if (result.storeInnerLabelList && result.storeInnerLabelList.length > 0) {
      get_store_category_list(result.storeInnerLabelList);
    } else {
      get_store_category_list(null);
    }

    get_freight_list(result.freightFee, result.freightId);

    if (result.goodsLabelList && result.goodsLabelList.length > 0) {
      get_label_list(result.goodsLabelList);
    } else {
      get_label_list(null);
    }

    get_related_template(result.relatedTemplateIdTop || null, result.relatedTemplateIdBottom || null);

    goodsSource.value = res.data.goodsSource; //商品来源
    // dev_supplier-start
    if (goodsSource.value == 2 && res.data.supplierGoodsId) {
      get_supplier_goods_freight(res.data.supplierGoodsId);
    }
    // dev_supplier-end

    isVirtualGoods.value = result.isVirtualGoods;
    if (isVirtualGoods.value == 2) {
      extraData.value = JSON.parse(JSON.stringify([...extraData.value, ...extraVirtualData.value]));
    }

    categorySelect.value = [
      { categoryId: result.categoryId1, categoryName: result.categoryPath.split('->')[0] },
      { categoryId: result.categoryId2, categoryName: result.categoryPath.split('->')[1] },
      { categoryId: result.categoryId3, categoryName: result.categoryPath.split('->')[2] },
    ]

    baseData.value.map((item) => {
      if (item.type !== 'title' && item.key !== 'brandId') {
        // dev_supplier-start
        if (item.key == 'goodsName') {
          item.value = (result[item.key] !== undefined && result[item.key] !== null) ? result[item.key] : undefined;
          item.disable = result.goodsSource == 2 ? true : false;
        }
        // dev_supplier-end
        if (item.key == 'categoryName') {
          item.value = result.categoryPath.split('->').join(' > ');
        } else if (item.key == 'isVirtualGoods') {
          item.value = (result[item.key] !== undefined && result[item.key] !== null) ? result[item.key] : undefined;
          item.disabled = true;
        } else if (item.key) {
          item.value = (result[item.key] !== undefined && result[item.key] !== null) ? result[item.key] : undefined;
        }
      }
    })

    invoiceData.value[1].value = (result['isVatInvoice'] !== undefined && result['isVatInvoice'] !== null) ? result['isVatInvoice'] : 0;

    extraData.value.map((item) => {
      if (item.type !== 'title') {
        if (item.key == 'reserveInfoList') {
          item.data = [];
          if (result.reserveList && result.reserveList.length > 0) {
            result.reserveList.map((reserve_item, reserve_index) => {
              let reserve_obj: any = {};
              reserve_obj.key = reserve_index;
              reserve_obj.reserveName = reserve_item.reserveName;
              reserve_obj.reserveType = reserve_item.reserveType;
              reserve_obj.isRequired = reserve_item.isRequired;
              item.data.push(reserve_obj);
            })
          }
        } else if (item.key == 'goodsQRCode') {
          if (result.goodsQRCode && result.goodsQRCode != '') {
            item.fileList = [{
              uid: result.goodsQRCode,
              name: result.goodsQRCode,
              status: 'done',
              url: result.goodsQRCode,
              response: {
                data: {
                  path: result.goodsQRCode,
                  url: result.goodsQRCode,
                }
              }
            }]
          }
        } else if (item.key) {
          item.value = (result[item.key] !== undefined && result[item.key] !== null) ? result[item.key] : undefined;
        }
      }
    })

    // dev_supplier-start
    if (getUserInfo.value.shopType && getUserInfo.value.shopType == 3) {
      ladderType.value = result.ladderType
      saleModel.value = result.saleModel
      //阶梯价设置
      levelPriceData.value.forEach(item => {
        if (item.key == 'ladderType' && result.saleModel > 1) {
          item.hidden = false
        }
      })

      levelPriceData.value.map((item) => {
        if (item.type !== 'title') {
          if (item.key == 'ladderType' && result.saleModel > 1) {
            item.hidden = false
          }
          if (item.key == 'lever_price_info') {
            if (result.ladderPrice) {
              let level_price_data = [];
              result.ladderPrice.split(',').forEach(item => {
                level_price_data.push({
                  level_key: lever_price_info_key.value,
                  level_num: item.split('-')[0],
                  level_price: item.split('-')[1],
                })
                lever_price_info_key.value++;
              })
              item.data = level_price_data;
            }
            if (result.ladderType > 1) {
              item.hidden = false
            }
          } else {
            if (item.key == 'saleModel') {
              item.value = (result[item.key] !== undefined && result[item.key] !== null) ? String(result[item.key]) : undefined;
            } else {
              item.value = (result[item.key] !== undefined && result[item.key] !== null) ? result[item.key] : undefined;

            }
            if (item.key == 'ladderType') {
              handleChange({ contentItem: { key: 'ladderType' }, val: item.value }, 'levelPriceData');
            }
          }
        }
      })
    }
    // dev_supplier-end
    if (result.specInfoList && result.specInfoList.length > 0) {
      //多规格规格、商品信息、图片
      let skuImgs: any = [];
      for (let i = 0; i < result.specInfoList.length; i++) {
        let spec_temp: any = specData.value[1].specList?.filter((spec_item) => spec_item.specId == result.specInfoList[i].specId);
        if (spec_temp.length > 0) {
          let specObj: any = {
            selectedSpec: spec_temp[0],
            selectedValueSpec: [],
            setImg: (result.specInfoList[i].specValueList.length > 0 && result.specInfoList[i].specValueList[0].imageList.length > 0) ? true : false,
            specValueList: JSON.parse(JSON.stringify(spec_temp[0].valueList)),
          }
          if (result.specInfoList[i].specValueList.length > 0) {
            result.specInfoList[i].specValueList.map((spec_item, spec_index) => {
              let sel_spec_temp = specObj.specValueList.filter((spec_obj_item) => spec_obj_item.specValueId == spec_item.specValueId);
              if (sel_spec_temp.length > 0) {
                specObj.selectedValueSpec.push(sel_spec_temp[0]);
                specObj.specValueList = specObj.specValueList.filter((spec_obj_item) => spec_obj_item.specValueId != spec_item.specValueId);
              }
              let ids = []
              if (spec_item.imageList) {
                spec_item.imageList.forEach(it => {
                  let obj = it.image.split('bindId=')
                  if (obj.length > 1) {
                    ids.push(Number(it.image.split('bindId=')[1]))
                  }
                })
              }
              selectImageData_goods.value['selectImageData_goods' + spec_index] = { ids: ids ? ids : [], data: spec_item.imageList ? spec_item.imageList : [] }; //多规格的图片数据
            })
          }
          specData.value[1].selectSpecList?.push(specObj);
          specData.value[1].specList = specData.value[1].specList?.filter((spec_item) => spec_item.specId != result.specInfoList[i].specId);
        }

        if (result.specInfoList[i].specValueList && result.specInfoList[i].specValueList.length > 0) {
          for (let j = 0; j < result.specInfoList[i].specValueList.length; j++) {
            if (result.specInfoList[i].specValueList[j].imageList && result.specInfoList[i].specValueList[j].imageList.length > 0) {
              if (!skuImgFlag.value) {
                skuImgFlag.value = true;
              }
              let sku_itm_obj = JSON.parse(JSON.stringify(imgData.value));
              sku_itm_obj.fileList = [];
              result.specInfoList[i].specValueList[j].imageList.forEach((spec_img_item) => {
                sku_itm_obj.fileList.push({
                  uid: spec_img_item.image + '-' + file_index.value,
                  thumbUrl: spec_img_item.imageUrl,
                  name: spec_img_item.image + '-' + file_index.value,
                  status: 'done',
                  response: {
                    state: 200,
                    data: {
                      path: spec_img_item.image,
                      url: spec_img_item.imageUrl,
                    },
                  },
                });
                file_index.value++;
              });
              sku_itm_obj.label = result.specInfoList[i].specValueList[j].specValue;
              sku_itm_obj.specId = result.specInfoList[i].specId;
              sku_itm_obj.specValue = result.specInfoList[i].specValueList[j].specValue;
              sku_itm_obj.specValueId = result.specInfoList[i].specValueList[j].specValueId;
              skuImgs.push(sku_itm_obj);
            }
          }
        }
      }
      if (!skuImgFlag.value && result.imageList.length > 0) {
        let img_fileList: any = [];
        result.imageList.map((img_item) => {
          img_fileList.push({
            uid: img_item.image + '-' + file_index.value,
            thumbUrl: img_item.imageUrl,
            name: img_item.image + '-' + file_index.value,
            status: 'done',
            response: {
              state: 200,
              data: {
                path: img_item.image,
                url: img_item.imageUrl,
              },
            }
          });
          file_index.value++;
        });
        goodsImgData.value[1].fileList = img_fileList;
      }
      goodsSkuImgData.value = [imgDataTitle.value, ...skuImgs];
      setColumnHeader(result.productList);
    } else {
      //单规格图片、商品信息
      if (result.imageList.length > 0) {
        let img_fileList: any = [];
        result.imageList.map((img_item) => {
          img_fileList.push({
            uid: img_item.image + '-' + file_index.value,
            thumbUrl: img_item.imageUrl,
            name: img_item.image + '-' + file_index.value,
            status: 'done',
            response: {
              state: 200,
              data: {
                path: img_item.image,
                url: img_item.imageUrl,
              },
            }
          });
          file_index.value++;
        });
        goodsImgData.value[1].fileList = img_fileList;
      }
      // dev_supplier-start
      // 商品规格-阶梯价-start
      if (result.productList.length > 0 && result.productList[0].ladderPrice !== undefined && result.productList[0].ladderPrice !== null && getUserInfo.value.shopType && getUserInfo.value.shopType == 3) {
        let level_price_data_obj = [];
        result.productList[0].ladderPrice.split(',').forEach(itemLadder => {
          level_price_data_obj.push({
            level_key: lever_price_info_key.value,
            level_num: itemLadder.split('-')[0],
            level_price: itemLadder.split('-')[1],
          })
          lever_price_info_key.value++;
        })
        result.productList[0].ladderPrice = level_price_data_obj;
      }
      // 商品规格-阶梯价-end
      // dev_supplier-end
      dataSource.value = result.productList;
      dataSourceForm.value.list = JSON.parse(JSON.stringify(dataSource.value));
    }

    let video_temp = videoData.value.filter((item) => item.key == 'video')[0];
    selectVideoData.value = { ids: [], data: [] };
    if (result.goodsVideo && result.goodsVideoUrl) {
      let video_info: any = {};
      video_info.uid = result.goodsVideo;
      video_info.thumbUrl = result.goodsVideoUrl;
      video_info.status = 'done';
      video_info.response = {};
      video_info.response.state = 200;
      video_info.response.data = {
        path: result.goodsVideo,
        url: result.goodsVideoUrl,
      };
      video_temp.fileList = [video_info];
      if (result.goodsVideo) {
        selectVideoData.value.ids = [Number(result.goodsVideo.split('bindId=')[1])];
        selectVideoData.value.data = ({
          bindId: result.goodsVideo.split('bindId=')[1],
          fileType: 2,
          filePath: result.goodsVideo,
          fileUrl: result.goodsVideoUrl,
        })
      }
    } else {
      video_temp.fileList = [];
    }

    if (res.data.goodsDetails) {
      initEditorContent.value = quillEscapeToHtml(res.data.goodsDetails); //商品详情
    }
  } else {
    failTip(res.msg);
  }
  spinning.value = false;
  editorFlag.value = true;
};

// dev_supplier-start
//获取供应链产品详情
const get_supplier_goods_freight = async (supplierGoodsId) => {
  const res = await getSupplierGoodsFreightInfo({ supplierGoodsId });
  if (res.state == 200) {
    showLogisticsData.value[1].value = res.data;
  }
};
// dev_supplier-end

//获取编辑器内容
const getEditorContent = async (con) => {
  baseValida.value = false;
  getEditorContentFlag.value = false;
  let params: any = {};
  if (id.value !== null && id.value) {
    params.goodsId = id.value; //编辑商品id
  }
  params.categoryId3 = categorySelect.value[2].categoryId; //商品分类
  baseData.value.map((item) => { //基本信息
    if (item.type != 'title' && item.key != 'categoryName' && item.key) {
      params[item.key] = item.value;
    }
  })
  params.attributeAndParameter = {};
  params.attributeAndParameter.attributeList = []; //检索属性
  attrData.value.map((item) => {
    if (item.type != 'title' && item.valueData && item.valueData.attributeId && item.valueData.attributeValueId) {
      params.attributeAndParameter.attributeList.push({
        attributeId: item.valueData.attributeId,
        attributeName: item.valueData.attributeName,
        attributeValue: item.valueData.attributeValue,
        attributeValueId: item.valueData.attributeValueId,
      })
    }
  })
  params.attributeAndParameter.parameterGroup = {}; //店铺自定义属性信息
  for (let m = 0; m < customData.value.length; m++) {
    if (customData.value[m].type != 'title' && customData.value[m].key != undefined) {
      if (customData.value[m].key == 'groupId' && customData.value[m].value) {
        let cust_temp: any = customData.value[m].data.filter((item) => item.groupId === customData.value[m].value)[0];
        params.attributeAndParameter.parameterGroup.groupId = cust_temp.groupId;
        params.attributeAndParameter.parameterGroup.groupName = cust_temp.groupName;
        params.attributeAndParameter.parameterGroup.parameterList = [];
      } else if (customData.value[m].key.indexOf('groupId') != -1) {
        if (!customData.value[m].value) {
          continue;
        } else {
          let cust_item_temp: any = customData.value[m].data.filter((item) => item.value === customData.value[m].value)[0];
          params.attributeAndParameter.parameterGroup.parameterList.push({
            parameterId: cust_item_temp.parameterId,
            parameterName: cust_item_temp.parameterName,
            parameterValue: cust_item_temp.parameterValue,
          });
        }
      } else {
        break;
      }
    } else {
      continue;
    }
  }
  if (isVirtualGoods.value == 1) { //物流信息
    logisticsData.value.map((item) => {
      if (item.type != 'title' && item.key != undefined) {
        params[item.key] = item.value;
      }
      if (!params.freightFee && params.freightFee !== 0) {
        delete params.freightFee;
      }
      if (!params.freightId) {
        delete params.freightId;
      }
    })
    delete params.freightType;
  }
  invoiceData.value.map((item) => { //发票信息
    if (item.type != 'title' && item.key) {
      params[item.key] = item.value;
    }
  })
  extraData.value.map((item) => { //其他信息
    if (item.type != 'title') {
      if (item.key == 'innerLabelId') {
        params.storeInnerLabelList = []; //店铺分类
        if (item.value && item.value.length > 0) {
          item.value.map((items, indexs) => {
            params.storeInnerLabelList.push({
              innerLabelId: items,
              goodsLabelName: item.list[indexs],
            })
          })
        }
      } else if (item.key == 'labelId') {
        params.goodsLabelList = []; //商品标签
        if (item.value && item.value.length > 0) {
          item.value.map((items) => {
            let label_temp = item.data.filter((label_item) => label_item.labelId == items);
            if (label_temp.length > 0) {
              params.goodsLabelList.push({
                goodsLabelId: label_temp[0].labelId,
                goodsLabelName: label_temp[0].labelName,
              })
            }
          })
        }
      } else if (item.key == 'reserveInfoList') {
        params.reserveInfoList = [];
        if (item.data && item.data.length > 0) {
          item.data.map((reserve_item) => {
            params.reserveInfoList.push({
              isRequired: reserve_item.isRequired ? 1 : 0,
              reserveName: reserve_item.reserveName,
              reserveType: reserve_item.reserveType,
            })
          })
        }
      } else if (item.key == 'goodsQRCode') {
        params.goodsQRCode = '';
        if (item.fileList && item.fileList.length > 0) {
          params.goodsQRCode = item.fileList[0].response.data.url;
        }
      } else if (item.key != undefined) {
        params[item.key] = item.value;
      }
    }
  })
  // dev_supplier-start
  // 阶梯价设置
  let minPriceFlag = false; //是否提示价格红线
  let minPrice = null; //价格最小值
  if (getUserInfo.value.shopType && getUserInfo.value.shopType == 3) {

    for (let p = 0; p < levelPriceData.value.length; p++) { //阶梯价设置
      if (levelPriceData.value[p].type != 'title') {
        if (levelPriceData.value[p].key == 'lever_price_info') {
          if (ladderType.value > 1) {
            if (setLadderPrice(levelPriceData.value[p].data, ladderType.value == 3 ? true : false).value === 'false') {
              step.value = 2;
              click_event.value = false;
              getEditorContentFlag.value = false;
              showErrorFlag = false;
              return;
            }
            params.ladderPrice = setLadderPrice(levelPriceData.value[p].data, ladderType.value == 3 ? true : false).value; //阶梯价
            if (minPrice === null || minPrice > setLadderPrice(levelPriceData.value[p].data, ladderType.value == 3 ? true : false).minPrice) {
              minPrice = setLadderPrice(levelPriceData.value[p].data, ladderType.value == 3 ? true : false).minPrice;
            }
          } else {
            params.ladderPrice = '';
          }
        } else {
          params[levelPriceData.value[p].key] = levelPriceData.value[p].value;
        }
      }
    }

  }
  // dev_supplier-end
  params.specInfoList = []; //商品规格
  params.productList = []; //商品列表
  let temp_spec: any = specData.value.filter((item) => item.key == 'specValue');
  if (temp_spec.length > 0 && temp_spec[0].selectSpecList.length > 0) {
    let productDefaultFlag = false; //多规格是否有默认选中项
    for (let k = 0; k < temp_spec[0].selectSpecList.length; k++) {
      let obj: any = {};
      obj.specId = temp_spec[0].selectSpecList[k].selectedSpec.specId;
      obj.specName = temp_spec[0].selectSpecList[k].selectedSpec.specName;
      obj.isMainSpec = temp_spec[0].selectSpecList[k].setImg ? 1 : 2;
      obj.specValueList = temp_spec[0].selectSpecList[k].selectedValueSpec.filter((select_spec_item) => select_spec_item.specValueId);
      if (obj.specValueList.length == 0) {
        continue;
      } else if (obj.specValueList.length > 0 && temp_spec[0].selectSpecList[k].setImg) {
        for (let i = 0; i < obj.specValueList.length; i++) {
          obj.specValueList[i].imageList = [];
          let temp_item = goodsSkuImgData.value.filter((sku_item) => sku_item.specValueId == obj.specValueList[i].specValueId);
          if (temp_item.length > 0 && temp_item[0].fileList && temp_item[0].fileList.length > 0) {
            temp_item[0].fileList.map((file_item, file_index) => {
              obj.specValueList[i].imageList.push({
                image: file_item.response.data.path,
                isMain: file_index == 0 ? 1 : 2,
              })
            })
          } else {
            step.value = 2;
            failTip('规格值为' + obj.specValueList[i].specValue + '的图片组,至少上传一张商品图片');
            click_event.value = false;
            getEditorContentFlag.value = false;
            setTimeout(() => {
              scrollGood.value?.scrollTo(Number(document.getElementById('goodsImg') ? document.getElementById('goodsImg').offsetTop : 0));
            }, 50)
            return;
          }
        }
      }
      params.specInfoList.push(obj);
    }
    for (let j = 0; j < dataSource.value.length; j++) {
      let obj: any = {};
      obj.marketPrice = dataSource.value[j].marketPrice;
      obj.productPrice = dataSource.value[j].productPrice;
      obj.productStock = dataSource.value[j].productStock;
      obj.weight = dataSource.value[j].weight;
      obj.length = dataSource.value[j].length;
      obj.width = dataSource.value[j].width;
      obj.height = dataSource.value[j].height;
      obj.productStockWarning = (dataSource.value[j].productStockWarning !== undefined && dataSource.value[j].productStockWarning !== null
        && dataSource.value[j].productStockWarning !== '') ? Number(dataSource.value[j].productStockWarning) : '';
      obj.goodsCode = dataSource.value[j].goodsCode;
      obj.barCode = dataSource.value[j].barCode;
      obj.state = dataSource.value[j].state;
      obj.isDefault = dataSource.value[j].isDefault;
      obj.specInfoList = dataSource.value[j].specInfoList;
      // dev_supplier-start
      if (getUserInfo.value.shopType && getUserInfo.value.shopType == 3) {
        obj.minBuyNum = '';
        obj.takeOffNum = '';
        obj.wholesalePrice = '';
        obj.costPrice = dataSource.value[j].costPrice;
        obj.productPrice = ''
        obj.retailPrice = ''
        obj.ladderPrice = ''
        if (saleModel.value == 1 || saleModel.value == 3) {
          obj.productPrice = dataSource.value[j].productPrice;
          obj.retailPrice = dataSource.value[j].retailPrice;
        }
        if (saleModel.value == 2 || saleModel.value == 3) {
          obj.wholesalePrice = dataSource.value[j].wholesalePrice;
          obj.minBuyNum = dataSource.value[j].minBuyNum;
          obj.takeOffNum = dataSource.value[j].takeOffNum;
          if (ladderType.value == 2) {
            obj.ladderPrice = params.ladderPrice
          } else if (ladderType.value == 3) {
            if (setLadderPrice(dataSource.value[j].ladderPrice).value === 'false') {
              step.value = 2;
              click_event.value = false;
              getEditorContentFlag.value = false;
              showErrorFlag = false;
              return false;
            }
            obj.ladderPrice = setLadderPrice(dataSource.value[j].ladderPrice).value
            if (minPrice === null || minPrice > setLadderPrice(dataSource.value[j].ladderPrice).minPrice) {
              minPrice = setLadderPrice(dataSource.value[j].ladderPrice).minPrice;
            }
          } else {
            if (minPrice === null || minPrice > dataSource.value[j].productPrice) {
              minPrice = dataSource.value[j].productPrice;
            }
          }
        }
      }
      // dev_supplier-end
      params.productList.push(obj);
      if (dataSource.value[j].isDefault == 1) {
        productDefaultFlag = true;
      }
    }
    if (!productDefaultFlag) {
      step.value = 2;
      click_event.value = false;
      getEditorContentFlag.value = false;
      failTip('多规格商品需要设置默认选中数据');
      setTimeout(() => {
        scrollGood.value?.scrollTo(Number(document.getElementById('goodsTable') ? document.getElementById('goodsTable').offsetTop : 0));
      }, 50)
      return;
    }
  }
  if (!(temp_spec.length > 0 && temp_spec[0].selectSpecList.length > 0) || params.specInfoList.length == 0) {
    delete params.specInfoList;
    params.marketPrice = dataSource.value[0].marketPrice;
    params.productPrice = dataSource.value[0].productPrice;
    params.productStock = dataSource.value[0].productStock;
    params.weight = dataSource.value[0].weight;
    params.length = dataSource.value[0].length;
    params.width = dataSource.value[0].width;
    params.height = dataSource.value[0].height;
    params.productStockWarning = (dataSource.value[0].productStockWarning !== undefined && dataSource.value[0].productStockWarning !== null
      && dataSource.value[0].productStockWarning !== '') ? Number(dataSource.value[0].productStockWarning) : '';
    params.goodsCode = dataSource.value[0].goodsCode;
    params.barCode = dataSource.value[0].barCode;
    // dev_supplier-start
    if (getUserInfo.value.shopType && getUserInfo.value.shopType == 3) {
      params.wholesalePrice = '';
      params.wholesalePrice = dataSource.value[0].wholesalePrice;
      params.productPrice = ''
      params.retailPrice = ''
      params.costPrice = dataSource.value[0].costPrice;
      params.minBuyNum = ''
      params.takeOffNum = ''
      if (saleModel.value == 1 || ladderType.value == 1) {
        params.ladderPrice = ''
      }
      if (saleModel.value == 1 || saleModel.value == 3) {
        params.productPrice = dataSource.value[0].productPrice;
        params.retailPrice = dataSource.value[0].retailPrice;
      }
      if (saleModel.value == 2 || saleModel.value == 3) {
        params.minBuyNum = dataSource.value[0].minBuyNum;
        params.takeOffNum = dataSource.value[0].takeOffNum;
        if (ladderType.value == 3) {
          if (setLadderPrice(dataSource.value[0].ladderPrice).value === 'false') {
            step.value = 2;
            click_event.value = false;
            getEditorContentFlag.value = false;
            showErrorFlag = false;
            return false;
          }
          params.ladderPrice = setLadderPrice(dataSource.value[0].ladderPrice).value
          if (minPrice === null || minPrice > setLadderPrice(dataSource.value[0].ladderPrice).minPrice) {
            minPrice = setLadderPrice(dataSource.value[j].ladderPrice).minPrice;
          }
        } else {
          if (minPrice === null || minPrice > dataSource.value[0].productPrice) {
            minPrice = dataSource.value[0].productPrice;
          }
        }
      }
    }
    // dev_supplier-end
  }
  params.imageList = []; //商品图片
  if (!skuImgFlag.value) {
    if (goodsImgData.value[1].fileList.length > 0) {
      goodsImgData.value[1].fileList.map((item, index) => {
        params.imageList.push({
          image: item.response.data.path,
          isMain: index == 0 ? 1 : 2,
        })
      })
    } else {
      step.value = 2;
      click_event.value = false;
      getEditorContentFlag.value = false;
      failTip('至少上传一张商品图片');
      setTimeout(() => {
        scrollGood.value?.scrollTo(Number(document.getElementById('goodsImg') ? document.getElementById('goodsImg').offsetTop : 0));
      }, 50)
      return false;
    }
  }
  if (videoData.value[1].fileList.length > 0) { //商品视频
    params.goodsVideo = videoData.value[1].fileList[0].response.data.path;
  }
  descData.value.map((item) => { //关联版式信息
    if (item.type != 'title' && item.key) {
      params[item.key] = item.value;
    }
  })
  params.goodsDetails = con; //商品详情描述
  let res: any = '';
  if (id.value !== null && id.value) {
    res = await editPlatformGood(params);
  } else {
    res = await addPlatformGood(params);
  }
  if (res.state == 200) {
    sucTip(res.msg);
    userStore.setDelKeepAlive([route.name, 'GoodsGoodsList', 'GoodsStorageList'])
    setTimeout(() => {
      goBack();
      click_event.value = false;
      getEditorContentFlag.value = false;
    }, 500);
  } else {
    failTip(res.msg);
    click_event.value = false;
    getEditorContentFlag.value = false;
  }
}

//表格回调事件
function handleClick(val, index, key) {
  index = index + ((getPaginationRef().current - 1) * getPaginationRef().pageSize);
  if (val == 1 && key == 'isDefault' && dataSource.value.length > 1) {
    for (let i = 0; i < dataSource.value.length; i++) {
      if (i == index && val && !dataSource.value[i]['state']) {
        dataSource.value[i]['state'] = 1;
        updateTableData(i, 'state', 1);
      }
      dataSource.value[i][key] = i == index ? 1 : 0;
      updateTableData(i, key, dataSource.value[i][key]);
    }
  } else {
    // 如果是库存字段
    if (key === 'productStock') {
      // 如果库存为0，自动关闭商品
      if (val === 0) {
        dataSource.value[index]['state'] = 2;
        updateTableData(index, 'state', 2);
      } else if (val > 0 && dataSource.value[index]['state'] === 2) {
        // 如果库存从0变为大于0，且商品是关闭状态，则自动开启
        dataSource.value[index]['state'] = 1;
        updateTableData(index, 'state', 1);
      }
    } else if (key === 'state' && val === 1 && dataSource.value[index].productStock === 0) {
      // 如果是启用按钮且库存为0，则自动设置为关闭状态
      val = 2;
      failTip('库存为0，不可开启');
    }
    dataSource.value[index][key] = val;
    updateTableData(index, key, val);
  }
  dataSourceForm.value.list = JSON.parse(JSON.stringify(dataSource.value));
}

//商品规格操作
const handleSpec = async (type, val, index, indexs) => {

  let temp: any = specData.value.filter((item) => item.key == 'specValue');
  if (temp.length > 0) {
    if (type == 'add') {
      //添加规格
      temp[0].selectSpecList.push({
        setImg: false, //是否设置图片规格
        selectedSpec: {}, //已选规格
        specValueList: [], //规格值列表
        selectedValueSpec: [], //已选规格值
      });
    } else if (type == 'selectSpec') {
      //选择规格
      let spec_item = temp[0].specList.filter((item) => item.specId == val)[0];
      // 去除已选规格名-start
      temp[0].specList = temp[0].specList.filter((item) => item.specId != val);
      // 去除已选规格名-end
      // 添加原有选择的规格名-start
      if (
        temp[0].selectSpecList[index].selectedSpec &&
        temp[0].selectSpecList[index].selectedSpec.specId
      ) {
        temp[0].specList.unshift(temp[0].selectSpecList[index].selectedSpec);
      }
      // 添加原有选择的规格名-end
      temp[0].selectSpecList[index].selectedSpec = spec_item;
      temp[0].selectSpecList[index].specValueList = spec_item.valueList;
      temp[0].selectSpecList[index].selectedValueSpec = [
        {
          specValue: '',
          specValueId: '',
        },
      ];
      setColumnHeader();
    } else if (type == 'setSpecImg') {
      //设置规格图片
      if (val) {
        temp[0].selectSpecList.map((items, indexs) => {
          items.setImg = indexs == index ? true : false;
        });
      } else {
        temp[0].selectSpecList[index].setImg = false;
      }
      setSkuImgs(temp[0].selectSpecList);
    } else if (type == 'addSpecVal') {
      //添加规格值
      // 获取当前最后一个输入框的值
      const lastInputIndex = temp[0].selectSpecList[index].selectedValueSpec.length - 1;
      const lastInputValue = temp[0].selectSpecList[index].selectedValueSpec[lastInputIndex].specValue;
      
      // 如果最后一个输入框有值，先处理这个值
      if (lastInputValue) {
        // 复用addSpecValDown的逻辑处理当前输入
        await handleSpec('addSpecValDown', lastInputValue, index, lastInputIndex);
      }

      // 检查是否有未填写的输入框
      if (
        temp[0].selectSpecList[index].selectedValueSpec.length > 0 &&
        !temp[0].selectSpecList[index].selectedValueSpec[
          temp[0].selectSpecList[index].selectedValueSpec.length - 1
        ].specValue
      ) return;

      // 添加新的输入框
      temp[0].selectSpecList[index].selectedValueSpec.push({
        specValue: '',
        specValueId: '',
      });
      setColumnHeader();
    } else if (type == 'selectSpecVal') {
      //选择规格值
      // 如果是搜索输入，保存当前输入值
      if (typeof val === 'string' && val.trim() !== '') {
        temp[0].selectSpecList[index].selectedValueSpec[indexs] = {
          specValue: val.trim(),
          specValueId: '',
          isTemp: true // 标记为临时输入值
        } as any; // 使用类型断言避免类型错误
        return;
      }
      
      let spec_val_item = temp[0].selectSpecList[index].specValueList.filter(
        (item) => String(item.specValueId) === String(val),
      )[0];
      
      // 如果是手动输入的值，不清空输入框
      if (!spec_val_item) {
        return;
      }
      
      // 去除已选规格值-start
      temp[0].selectSpecList[index].specValueList = temp[0].selectSpecList[
        index
      ].specValueList.filter((item) => String(item.specValueId) !== String(val));
      // 去除已选规格值-end
      
      // 添加原有选择的规格值-start
      if (
        temp[0].selectSpecList[index].selectedValueSpec[indexs] &&
        temp[0].selectSpecList[index].selectedValueSpec[indexs].specValueId &&
        !temp[0].selectSpecList[index].selectedValueSpec[indexs].isTemp // 不处理临时输入值
      ) {
        temp[0].specList.unshift(temp[0].selectSpecList[index].selectedValueSpec[indexs]);
      }
      // 添加原有选择的规格值-end
      
      temp[0].selectSpecList[index].selectedValueSpec[indexs] = spec_val_item;
      setColumnHeader();
      setSkuImgs(temp[0].selectSpecList);
    } else if (type == 'addSpecDown') {
      //规格名回车事件
      if (determineEmoji(val)) {
        failTip('不能输入表情');
        return
      }
      if (temp[0].selectSpecList.filter(item => item.selectedSpec.specName == val).length > 0) {
        failTip('规格名已添加');
      } else if (temp[0].selectSpecList[index].selectedSpec.specName != val) {
        let spec_temp = temp[0].specList.filter(item => item.specName == val);
        if (spec_temp.length == 0) {
          const res: any = await addGoodsSpec({ specName: val, state: 1, sort: 1 });
          if (res.state == 200) {
            temp[0].selectSpecList[index] = {
              selectedSpec: {
                label: val,
                sort: 1,
                specId: res.data,
                specName: val,
                state: 1,
                storeId: '',
                value: res.data,
                valueList: [],
              },
              selectedValueSpec: [{
                specValue: '',
                specValueId: '',
              }],
              setImg: false,
              specValueList: [],
            };
            setColumnHeader();
          } else {
            failTip(res.msg);
          }
        } else {
          handleSpec('selectSpec', spec_temp[0].specId, index, indexs);
        }
      }
    } else if (type == 'addSpecValDown') {
      console.log('addSpecValDown triggered:', { val, index, indexs });
      //添加规格值-回车
      if (!val || val == '') {
        console.log('输入为空，返回');
        return;
      }

      if (determineEmoji(val)) {
        failTip('不能输入表情');
        return;
      }

      // 检查当前输入框的状态
      const currentInput = temp[0].selectSpecList[index].selectedValueSpec[indexs];
      
      // 如果是从下拉列表选择的已有值(有specValueId且不是临时输入),直接添加新输入框
      if (currentInput && currentInput.specValueId && !currentInput.isTemp) {
        temp[0].selectSpecList[index].selectedValueSpec.push({
          specValue: '',
          specValueId: '',
          isTemp: true
        });
        setColumnHeader();
        setSkuImgs(temp[0].selectSpecList);
        return;
      }

      // 检查是否已存在相同的规格值
      if (temp[0].selectSpecList[index].selectedValueSpec.filter(item => item.specValue == val && !item.isTemp).length > 0) {
        failTip('规格值已存在，请重新输入');
        // 清空当前输入框
        temp[0].selectSpecList[index].selectedValueSpec[indexs].specValue = '';
        return;
      }

      // 查找现有规格值
      let spec_val_temp = temp[0].selectSpecList[index].specValueList.filter(item => item.specValue == val);
      console.log('现有规格值检查:', spec_val_temp);

      if (spec_val_temp.length == 0) {
        console.log('通过API新添加规格值');
        // 添加新规格值
        const res: any = await addGoodsSpecVal({ 
          specId: temp[0].selectSpecList[index].selectedSpec.specId, 
          specValues: val 
        });
        
        if (res.state == 200) {
          console.log('API 添加规格值成功:', res);
          // 更新当前输入框的值
          temp[0].selectSpecList[index].selectedValueSpec[indexs] = {
            specValue: val,
            specValueId: res.data,
            isTemp: false
          };
          // 暂不处理请求接口后新增输入框，保留
          // temp[0].selectSpecList[index].selectedValueSpec.push({
          //   specValue: '',
          //   specValueId: '',
          //   isTemp: true
          // });

          setColumnHeader();
          setSkuImgs(temp[0].selectSpecList);
        } else {
          failTip(res.msg);
        }
      } else {
        console.log('使用现有规格值');
        // 使用现有规格值
        handleSpec('selectSpecVal', spec_val_temp[0].specValueId, index, indexs);
      }
    } else if (type == 'deleteSpec') {
      let del_temp = temp[0].selectSpecList.filter(
        (del_item, del_index) => del_index === index,
      );
      temp[0].selectSpecList = temp[0].selectSpecList.filter(
        (del_item, del_index) => del_index !== index,
      );
      if (Object.keys(del_temp[0].selectedSpec).length > 0) {
        temp[0].specList.push(del_temp[0].selectedSpec);
        setColumnHeader();
        setSkuImgs(temp[0].selectSpecList);
        dataSource.value = [
          {
            specInfoList: [],
            // dev_supplier-start
            ladderPrice: [{
              level_key: 0,
              level_num: 1,
              level_price: '',
            }],
            minBuyNum: 1,
            takeOffNum: 1,
            // dev_supplier-end
            marketPrice: undefined,
            productPrice: undefined,
            productStock: undefined,
            weight: 1,
            length: 1,
            width: 1,
            height: 1,
            productStockWarning: undefined,
            goodsCode: undefined,
            barCode: undefined,
            state: 1,
            isDefault: 1,
          }
        ]
      }
    } else if (type == 'deleteSpecVal') {
      // 如果只剩一个规格值，不允许删除
      if (temp[0].selectSpecList[index].selectedValueSpec.length <= 1) {
        failTip('至少需要保留一个规格值');
        return;
      }

      let del_temp = temp[0].selectSpecList[index].selectedValueSpec.filter(
        (del_item, del_index) => del_index === indexs,
      );
      temp[0].selectSpecList[index].selectedValueSpec = temp[0].selectSpecList[
        index
      ].selectedValueSpec.filter((del_item, del_index) => del_index !== indexs);
      if (del_temp[0].specValue) {
        temp[0].selectSpecList[index].specValueList.push(del_temp[0]);
        setColumnHeader();
        setSkuImgs(temp[0].selectSpecList);
      }
    } else if (type == 'addSpecVal') {
      // 添加规格值按钮点击 - 直接添加新输入框，不做任何检查
      temp[0].selectSpecList[index].selectedValueSpec.push({
        specValue: '',
        specValueId: '',
        isTemp: true
      });
      setColumnHeader();
      setSkuImgs(temp[0].selectSpecList);
    } else if (type == 'addSpecValDown') {
      //添加规格值-回车
      if (!val || val === '') {
        return;
      }

      if (determineEmoji(val)) {
        failTip('不能输入表情');
        return;
      }

      // 检查当前输入框的状态
      const currentInput = temp[0].selectSpecList[index].selectedValueSpec[indexs];
      
      // 如果当前输入框已经有值且不是临时输入，直接返回
      if (currentInput && currentInput.specValueId && !currentInput.isTemp) {
        return;
      }

      // 查找现有规格值
      let spec_val_temp = temp[0].selectSpecList[index].specValueList.filter(item => 
        String(item.specValue) === String(val)
      );
      
      if (spec_val_temp.length > 0) {
        // 使用现有规格值
        temp[0].selectSpecList[index].selectedValueSpec[indexs] = {
          specValue: spec_val_temp[0].specValue,
          specValueId: spec_val_temp[0].specValueId,
          isTemp: false
        };
        
        // 从可选列表中移除
        temp[0].selectSpecList[index].specValueList = temp[0].selectSpecList[index].specValueList.filter(
          item => String(item.specValueId) !== String(spec_val_temp[0].specValueId)
        );
        
        // 只有当前是最后一个输入框时才添加新的空输入框
        const isLastInput = indexs === temp[0].selectSpecList[index].selectedValueSpec.length - 1;
        if (isLastInput) {
          temp[0].selectSpecList[index].selectedValueSpec.push({
            specValue: '',
            specValueId: '',
            isTemp: true
          });
        }

        setColumnHeader();
        setSkuImgs(temp[0].selectSpecList);
      } else {
        // 添加新规格值
        const res: any = await addGoodsSpecVal({ 
          specId: temp[0].selectSpecList[index].selectedSpec.specId, 
          specValues: val 
        });
        
        if (res.state == 200) {
          // 更新当前输入框的值
          temp[0].selectSpecList[index].selectedValueSpec[indexs] = {
            specValue: val,
            specValueId: res.data,
            isTemp: false
          };

          // 只有当前是最后一个输入框时才添加新的空输入框
          const isLastInput = indexs === temp[0].selectSpecList[index].selectedValueSpec.length - 1;
          if (isLastInput) {
            temp[0].selectSpecList[index].selectedValueSpec.push({
              specValue: '',
              specValueId: '',
              isTemp: true
            });
          }

          setColumnHeader();
          setSkuImgs(temp[0].selectSpecList);
        } else {
          failTip(res.msg);
        }
      }
    } else if (type == 'deleteSpec') {
      let del_temp = temp[0].selectSpecList.filter(
        (del_item, del_index) => del_index === index,
      );
      temp[0].selectSpecList = temp[0].selectSpecList.filter(
        (del_item, del_index) => del_index !== index,
      );
      if (Object.keys(del_temp[0].selectedSpec).length > 0) {
        temp[0].specList.push(del_temp[0].selectedSpec);
        setColumnHeader();
        setSkuImgs(temp[0].selectSpecList);
        dataSource.value = [
          {
            specInfoList: [],
            // dev_supplier-start
            ladderPrice: [{
              level_key: 0,
              level_num: 1,
              level_price: '',
            }],
            minBuyNum: 1,
            takeOffNum: 1,
            // dev_supplier-end
            marketPrice: undefined,
            productPrice: undefined,
            productStock: undefined,
            weight: 1,
            length: 1,
            width: 1,
            height: 1,
            productStockWarning: undefined,
            goodsCode: undefined,
            barCode: undefined,
            state: 1,
            isDefault: 1,
          }
        ]
      }
    }
  }
}

//根据规格设置表格头数据
function setColumnHeader(dataSource = null) {
  let temp: any = specData.value.filter((item) => item.key == 'specValue');
  let extra_columns: any = [];
  let spec_all_data: any = [];
  if (temp[0].selectSpecList && temp[0].selectSpecList.length > 0) {
    for (let i = 0; i < temp[0].selectSpecList.length; i++) {
      let item = temp[0].selectSpecList[i];
      if (
        item.selectedValueSpec &&
        item.selectedValueSpec.length > 0 &&
        item.selectedValueSpec[0].specValueId
      ) {
        extra_columns.push({
          title: item.selectedSpec.specName,
          dataIndex: `spec_info[${i}]`,
          width: 100,
        });
        let spec_all_data_item: any = [];
        for (let j = 0; j < item.selectedValueSpec.length; j++) {
          let items = item.selectedValueSpec[j];
          if (items.specValueId) {
            spec_all_data_item.push(items);
          } else {
            break;
          }
        }
        spec_all_data.push(spec_all_data_item);
      } else {
        break;
      }
    }
  }
  if (extra_columns.length > 0) {
    show_columns.value = [...extra_columns, ...base_columns.value];
    // dev_supplier-start
    if (getUserInfo.value.shopType && getUserInfo.value.shopType == 3) {
      show_columns.value = [...extra_columns, ...base_supplier_columns_dai.value];
    }
    // dev_supplier-end
  } else {
    show_columns.value = [...base_columns.value];
    // dev_supplier-start
    if (getUserInfo.value.shopType && getUserInfo.value.shopType == 3) {
      show_columns.value = [...base_supplier_columns_dai.value];
    }
    // dev_supplier-end
  }

  // dev_supplier-start
  if (getUserInfo.value.shopType && getUserInfo.value.shopType == 3) {
    sale_type_cilck(ladderType.value)
  }
  // dev_supplier-end

  setColumnData(calcDescartes(spec_all_data), dataSource);
}

//根据规格设置商品图片
function setSkuImgs(selectSpecList) {
  if (selectSpecList && selectSpecList.length > 0) {
    let skuImgs: any = [];
    for (let i = 0; i < selectSpecList.length; i++) {
      if (selectSpecList[i].setImg && selectSpecList[i].selectedValueSpec && selectSpecList[i].selectedValueSpec.length > 0) {
        for (let j = 0; j < selectSpecList[i].selectedValueSpec.length; j++) {
          if (selectSpecList[i].selectedValueSpec[j].specValueId) {
            let obj = JSON.parse(JSON.stringify(imgData.value));
            obj.label = selectSpecList[i].selectedValueSpec[j].specValue;
            obj.name = selectSpecList[i].selectedValueSpec[j].specValueId;
            obj.specId = selectSpecList[i].selectedSpec.specId;
            obj.specValue = selectSpecList[i].selectedValueSpec[j].specValue;
            obj.specValueId = selectSpecList[i].selectedValueSpec[j].specValueId;
            selectImageData_goods.value['selectImageData_goods' + j] = { ids: [], data: [] }; //多规格的图片数据
            let temp = goodsSkuImgData.value.filter(item=>item.specValueId&&item.specValueId==selectSpecList[i].selectedValueSpec[j].specValueId)
            if(temp.length>0){
              if(temp[0].fileList&&temp[0].fileList.length>0){
                obj.fileList = temp[0].fileList
              }
            }
            skuImgs.push(obj);
          }
        }
      }
    }
    if (skuImgs.length > 0) {
      skuImgFlag.value = true;
      goodsSkuImgData.value = [imgDataTitle.value, ...skuImgs];
    } else {
      skuImgFlag.value = false;
      goodsSkuImgData.value = [];
    }
  } else if (skuImgFlag.value) {
    skuImgFlag.value = false;
    goodsSkuImgData.value = [];
  }
}
const table_num = ref(0)
//根据规格设置表格列表数据
function setColumnData(spec_data, data_source = null) {
  let old_data: any = [];
  if (data_source) {
    old_data = JSON.parse(JSON.stringify(data_source));
  } else {
    old_data = JSON.parse(JSON.stringify(dataSource.value));
  }

  // dev_supplier-start
  let price_data = []
  if (getUserInfo.value.shopType && getUserInfo.value.shopType == 3) {
    let price_temp = levelPriceData.value.filter(items => items.key == 'lever_price_info');
    price_data = [{ level_key: 0, level_num: 1, level_price: '' }];
    if (price_temp.length > 0) {
      price_data = JSON.parse(JSON.stringify(price_temp[0].data));
    }
  }
  // dev_supplier-end
  let data: any = [];
  if (spec_data && spec_data.length > 0) {
    for (let i = 0; i < spec_data.length; i++) {
      if (spec_data[i] && Array.isArray(spec_data[i]) && spec_data[i].length > 0) {
        let item: any = {};
        item.specInfoList = [];
        item.marketPrice = undefined;
        item.productPrice = undefined;
        item.productStock = undefined;
        item.weight = 1;
        item.length = 1;
        item.width = 1;
        item.height = 1;
        item.productStockWarning = undefined;
        item.goodsCode = undefined;
        item.barCode = undefined;
        item.state = 1;
        item.isDefault = i == 0 ? 1 : 0;
        item.specValueIdArray = [];
        for (let j = 0; j < spec_data[i].length; j++) {
          item.specValueIdArray.push(spec_data[i][j].specValueId.toString());
          item[`spec_info[${j}]`] = spec_data[i][j].specValue;
          item.specInfoList.push(spec_data[i][j]);
        }
        item.specValueIdArray = item.specValueIdArray.join(',');
        if (route.query.id !== null && route.query.id && table_num.value == 0) {
          item.state = item.productPrice && item.productStock ? 1 : 2
        }
        // dev_supplier-start
        if (getUserInfo.value.shopType && getUserInfo.value.shopType == 3) {
          item.minBuyNum = 1;
          item.takeOffNum = 1;
          item.wholesalePrice = undefined;
          item.productPrice = undefined;
          item.retailPrice = undefined;
          item.costPrice = undefined;
          item.ladderPrice = price_data;
        }
        // dev_supplier-end
        data.push(item);
      } else if (spec_data[i] && Object.keys(spec_data[i]).length > 0) {
        let item: any = {};
        item.specInfoList = [];
        item.marketPrice = undefined;
        item.productPrice = undefined;
        item.productStock = undefined;
        item.weight = 1;
        item.length = 1;
        item.width = 1;
        item.height = 1;
        item.productStockWarning = undefined;
        item.goodsCode = undefined;
        item.barCode = undefined;
        if (route.query.id !== null && route.query.id && table_num.value == 0) {
          item.state = 2;
        } else {
          item.state = 1;
        }
        item.isDefault = i == 0 ? 1 : 0;
        item.specValueIdArray = spec_data[i].specValueId.toString();
        item[`spec_info[0]`] = spec_data[i].specValue;
        item.specInfoList.push(spec_data[i]);
        // dev_supplier-start
        if (getUserInfo.value.shopType && getUserInfo.value.shopType == 3) {
          item.minBuyNum = 1;
          item.takeOffNum = 1;
          item.wholesalePrice = undefined;
          item.productPrice = undefined;
          item.retailPrice = undefined;
          item.costPrice = undefined;
          item.ladderPrice = price_data;
        }
        // dev_supplier-end
        data.push(item);
      }
    }
    if (data.length > 0) {
      if (old_data && old_data.length > 0) {
        for (let old_index = 0; old_index < old_data.length; old_index++) {
          if ((data_source && old_data[old_index].specValueIds) || (!data_source && old_data[old_index].specValueIdArray)) {
            let data_temp: any = []
            if (data_source) {
              data_temp = data.filter((data_item) => data_item.specValueIdArray == old_data[old_index].specValueIds);
            } else {
              data_temp = data.filter((data_item) => data_item.specValueIdArray == old_data[old_index].specValueIdArray);
            }
            if (data_temp.length > 0) {
              data_temp[0].marketPrice = old_data[old_index].marketPrice;
              data_temp[0].productPrice = old_data[old_index].productPrice;
              data_temp[0].productStock = old_data[old_index].productStock;
              data_temp[0].weight = old_data[old_index].weight;
              data_temp[0].length = old_data[old_index].length;
              data_temp[0].width = old_data[old_index].width;
              data_temp[0].height = old_data[old_index].height;
              data_temp[0].productStockWarning = old_data[old_index].productStockWarning;
              data_temp[0].goodsCode = old_data[old_index].goodsCode;
              data_temp[0].barCode = old_data[old_index].barCode;
              data_temp[0].state = old_data[old_index].state;
              data_temp[0].isDefault = old_data[old_index].isDefault;
              // dev_supplier-start
              if (getUserInfo.value.shopType && getUserInfo.value.shopType == 3) {
                data_temp[0].minBuyNum = old_data[old_index].minBuyNum;
                data_temp[0].takeOffNum = old_data[old_index].takeOffNum;
                data_temp[0].wholesalePrice = old_data[old_index].wholesalePrice;
                data_temp[0].productPrice = old_data[old_index].productPrice;
                data_temp[0].retailPrice = old_data[old_index].retailPrice;
                data_temp[0].costPrice = old_data[old_index].costPrice;
                if (old_data[old_index].ladderPrice) {
                  if (typeof old_data[old_index].ladderPrice == 'string') {
                    let level_price_data_obj = [];
                    old_data[old_index].ladderPrice.split(',').forEach(itemLadder => {
                      level_price_data_obj.push({
                        level_key: lever_price_info_key.value,
                        level_num: itemLadder.split('-')[0],
                        level_price: itemLadder.split('-')[1],
                      })
                      lever_price_info_key.value++;
                    })
                    data_temp[0].ladderPrice = level_price_data_obj;
                  } else {
                    data_temp[0].ladderPrice = old_data[old_index].ladderPrice;
                  }
                } else {
                  data_temp[0].ladderPrice = price_data;
                }
              }
              // dev_supplier-end
            }
          }
        }
      }
      dataSource.value = data;
      dataSourceForm.value.list = JSON.parse(JSON.stringify(data));
    }
  }
  table_num.value = 1
}

function closeMaterial() {
  chooseFile.value = 0;
  operate_type.value = '';
}

function confirmMaterial(type, val) {
  if (type == 'image' && operate_type.value == 'goods') {
    if (operate_sku_index.value !== -1) {
      let temp = goodsSkuImgData.value.filter((item) => item.specValueId == operate_sku_id.value);
      if (temp && temp.length && val.data.length) {
        val.data.forEach((item) => {
          temp[0].fileList.push({
            uid: item.filePath + '-' + file_index.value,
            thumbUrl: item.fileUrl,
            name: item.fileName,
            status: 'done',
            response: {
              state: 200,
              data: {
                path: item.filePath,
                url: item.fileUrl,
              },
            },
          });
          file_index.value++;
        });
      }
      selectImageData_goods.value['selectImageData_goods' + operate_sku_index.value].ids =
        selectImageData_goods.value[
          'selectImageData_goods' + operate_sku_index.value
        ].ids.concat(val.ids);
      selectImageData_goods.value['selectImageData_goods' + operate_sku_index.value].data =
        selectImageData_goods.value[
          'selectImageData_goods' + operate_sku_index.value
        ].data.concat(val.data);
    } else {
      let temp = goodsImgData.value.filter((item) => item.specValueId == operate_sku_id.value);
      if (temp && temp.length && val.data.length) {
        val.data.forEach((item) => {
          temp[0].fileList.push({
            uid: item.filePath + '-' + file_index.value,
            thumbUrl: item.fileUrl,
            name: item.fileName,
            status: 'done',
            response: {
              state: 200,
              data: {
                path: item.filePath,
                url: item.fileUrl,
              },
            },
          });
          file_index.value++;
        });
      }
      selectImageData_goods.value['ids'] = selectImageData_goods.value['ids'].concat(val.ids);
      selectImageData_goods.value['data'] = selectImageData_goods.value['data'].concat(
        val.data,
      );
    }
    operate_type.value = '';
    chooseFile.value = 0;
    selectImageData.value = selectImageData_goods.value;
  } else if (type == 'video') {
    videoData.value[1].fileList = [];
    if (val.data.length) {
      val.data.forEach((item) => {
        videoData.value[1].fileList.push({
          uid: item.filePath + '-' + file_index.value,
          thumbUrl: item.fileUrl,
          name: item.fileName,
          status: 'done',
          response: {
            state: 200,
            data: {
              path: item.filePath,
              url: item.fileUrl,
            },
          },
        });
        file_index.value++;
      });
    }
    chooseFile.value = 0;
    selectVideoData.value = val;
  }
}

function goBack() {
  if (window.history && window.history.length == 1) {
    pageClose();
  } else {
    const { fullPath } = route;
    tabStore.closeTabByKey(fullPath, router);
    router.back();
  }
}

// dev_supplier-start
//设置保存阶梯价数据
let showErrorFlag = false;
function setLadderPrice(data, flag = false) {
  let ladderPrice = [];
  let minPrice = null;
  for (var i in data) {
    let lever_price_data = data[i];
    if ((lever_price_data.level_num != null && lever_price_data.level_num >= 0) &&
      (lever_price_data.level_price && lever_price_data.level_price >= 0)) {
      // 数量-价格
      ladderPrice.push(lever_price_data.level_num + '-' + lever_price_data.level_price);
    } else if (!flag) {
      if (!showErrorFlag) {
        showErrorFlag = true;
        failTip(L('请填写批发数量与阶梯价'));
        step.value = 2;
        click_event.value = false;
        getEditorContentFlag.value = false;
      }
      return { value: 'false' };
    }
    if ((lever_price_data.level_price > 0 || lever_price_data.level_price === 0) && (minPrice === null || lever_price_data.level_price < minPrice)) {
      minPrice = lever_price_data.level_price;
    }
  }
  return {
    value: ladderPrice.join(','),
    minPrice: minPrice,
  };
};

//商品规格-设置阶梯价-添加阶梯价
const addLevelPrice = () => {
  let temp = levelPriceData.value.filter(items => items.key == 'lever_price_info')[0];
  if (temp.data.length >= 10) {
    failTip(`${L('阶梯价最多设置10级')}`);
    return;
  }
  temp.data.push({
    level_key: lever_price_info_key.value,
    level_num: '',
    level_price: '',
  });
  lever_price_info_key.value += 1;
};

//商品规格-设置阶梯价-删除阶梯价
const delLevelPrice = (level_key) => {
  let temp = levelPriceData.value.filter(items => items.key == 'lever_price_info')[0];
  temp.data = temp.data.filter(item => item.level_key != level_key);
  dataSource.value.map(items => {
    items.ladderPrice = temp.data;
  })
  dataSourceForm.value.list = JSON.parse(JSON.stringify(dataSource.value));
};

//商品规格-设置阶梯价-输入阶梯价
const handleChangePrice = (e, type, index) => {
  let temp = levelPriceData.value.filter(items => items.key == 'lever_price_info')[0];
  temp.data[index][type] = e;
};

//商品规格-设置阶梯价-失焦校验
const handleBlurPrice = () => {
  let arr = [];
  let flag = '';
  let temp = levelPriceData.value.filter(items => items.key == 'lever_price_info')[0];
  let data = temp.data;
  for (var i = 0; i < data.length; i++) {
    if (data[i].level_num && arr.indexOf(data[i].level_num) == -1) {
      arr.push(data[i].level_num);
    } else if (data[i].level_num && arr.indexOf(data[i].level_num) != -1 && !flag) {
      flag = data[i].level_num;
      data[i].level_num = '';
      break;
    }
  }
  if (flag) {
    failTip('批发数量范围 ≥' + flag + '已存在，请重新设置');
    temp.data = data;
  } else {
    dataSource.value.map((items, indexs) => {
      items.ladderPrice = data;
      if ((saleModel.value == 3 || saleModel.value == 2) && (ladderType.value == 3 || ladderType.value == 2)) {
        items.productPrice = data && data.length > 0 && data[0].level_price ? data[0].level_price : ''
        updateTableData(indexs, 'productPrice', items.productPrice);
      }
    })
    dataSourceForm.value.list = JSON.parse(JSON.stringify(dataSource.value));
  }
};

//编辑多规格表单数据的阶梯价
const changeLadderPrice = (index) => {
  ladder_price_data.data = (dataSourceForm.value.list && dataSourceForm.value.list[index].ladderPrice)
    ? JSON.parse(JSON.stringify(dataSourceForm.value.list[index].ladderPrice)) : [];
  ladder_price_data.index = index;
  ladder_price_modal.value = true;
};

//修改阶梯价弹窗-编辑数据
const onchangeLadderPrice = (val, index, key) => {
  ladder_price_data.data[index][key] = val;
};

//修改阶梯价弹窗-删除区间
const delLadderPrice = (index) => {
  ladder_price_data.data.splice(index, 1);
};

//修改阶梯价弹窗-新增区间
const addLadderPrice = () => {
  if (ladder_price_data.data.length >= 10) {
    failTip(`${L('阶梯价最多设置10级')}`);
    return;
  }
  ladder_price_data.data.push({
    level_key: lever_price_info_key.value,
    level_num: '',
    level_price: '',
  });
  lever_price_info_key.value++;
};

//修改阶梯价弹窗-关闭
const handleLadderCancle = () => {
  ladder_price_modal.value = false;
};

//修改阶梯价弹窗-确认
const handleLadderConfirm = () => {
  let pre_num = 0;
  for (let i = 0; i < ladder_price_data.data.length; i++) {
    if (!ladder_price_data.data[i].level_num && ladder_price_data.data[i].level_num !== 0) {
      failTip('下单数量不能为空');
      return;
    } else if (ladder_price_data.data[i].level_num <= pre_num) {
      failTip('下单数量[' + ladder_price_data.data[i].level_num + ']区间不正确');
      return;
    } else if (!ladder_price_data.data[i].level_price && ladder_price_data.data[i].level_price !== 0) {
      failTip('请输入下单数量为[' + ladder_price_data.data[i].level_num + ']的阶梯价');
      return;
    }
    pre_num = ladder_price_data.data[i].level_num;
  }
  dataSource.value[ladder_price_data.index].ladderPrice = ladder_price_data.data;
  dataSource.value[ladder_price_data.index].productPrice = ladder_price_data.data && ladder_price_data.data.length > 0 ? ladder_price_data.data[0].level_price : '';
  updateTableData(ladder_price_data.index, 'ladderPrice', ladder_price_data.data);
  updateTableData(ladder_price_data.index, 'productPrice', ladder_price_data.data && ladder_price_data.data.length > 0 ? ladder_price_data.data[0].level_price : '');
  dataSourceForm.value.list = JSON.parse(JSON.stringify(dataSource.value));
  ladder_price_modal.value = false;
};

const sale_type_cilck = (val) => {
  let wholesale_price_obj = {
    title: '批发价',
    dataIndex: 'wholesalePrice',
    width: 160,
  };
  let product_price_obj = {
    title: '供货价',
    dataIndex: 'productPrice',
    width: 160,
  };
  let retail_price_obj = {
    title: '建议零售价',
    dataIndex: 'retailPrice',
    width: 160,
  };
  let min_num_obj = {
    title: '起订量',
    dataIndex: 'minBuyNum',
    width: 160,
  };
  let take_num_obj = {
    title: '起跳量',
    dataIndex: 'takeOffNum',
    width: 160,
  };
  show_columns.value = show_columns.value.filter(items => items.dataIndex != 'productPrice');
  show_columns.value = show_columns.value.filter(items => items.dataIndex != 'retailPrice');
  show_columns.value = show_columns.value.filter(items => items.dataIndex != 'wholesalePrice');
  show_columns.value = show_columns.value.filter(items => items.dataIndex != 'minBuyNum');
  show_columns.value = show_columns.value.filter(items => items.dataIndex != 'takeOffNum');
  if (saleModel.value == 1) {
    for (let i = 0; i < show_columns.value.length; i++) {
      if (show_columns.value[i].dataIndex == 'marketPrice') {
        show_columns.value.splice(i, 0, product_price_obj, retail_price_obj);
        break;
      }
    }
    let temp = levelPriceData.value.filter(items => items.key == 'lever_price_info')
    temp[0].hidden = true
  } else {
    if (val == 1) {
      for (let i = 0; i < show_columns.value.length; i++) {
        if (show_columns.value[i].dataIndex == 'productStock') {
          show_columns.value.splice(i + 1, 0, min_num_obj, take_num_obj);
          if (saleModel.value == 2) {
            show_columns.value.splice(i - 2, 0, wholesale_price_obj);
          } else if (saleModel.value == 3) {
            show_columns.value.splice(i - 2, 0, wholesale_price_obj, product_price_obj, retail_price_obj);
          }
          break;
        }
      }
      let temp = levelPriceData.value.filter(items => items.key == 'lever_price_info')
      temp[0].hidden = true
    } else if (val == 2) {
      for (let i = 0; i < show_columns.value.length; i++) {
        if (show_columns.value[i].dataIndex == 'productStock') {
          show_columns.value.splice(i + 1, 0, min_num_obj, take_num_obj);
          if (saleModel.value == 3) {
            show_columns.value.splice(i - 2, 0, product_price_obj, retail_price_obj);
          }
          break;
        }
      }
      let temp = levelPriceData.value.filter(items => items.key == 'lever_price_info')
      temp[0].hidden = false
    } else if (val == 3) {
      if (show_columns.value.filter(items => items.dataIndex == 'wholesalePrice').length == 0) {
        for (let i = 0; i < show_columns.value.length; i++) {
          if (show_columns.value[i].dataIndex == 'productStock') {
            show_columns.value.splice(i + 1, 0, min_num_obj, take_num_obj);
            if (saleModel.value == 2) {
              show_columns.value.splice(i - 2, 0, wholesale_price_obj);
            } else if (saleModel.value == 3) {
              show_columns.value.splice(i - 2, 0, wholesale_price_obj, product_price_obj, retail_price_obj);
            }
            break;
          }
        }
      }
      let temp = levelPriceData.value.filter(items => items.key == 'lever_price_info')
      temp[0].hidden = false
    }

  }
}

//表格blur事件
function handleBlur(index, key) {
  if (key == 'minBuyNum') {
    //校验起订量 大于1级阶梯数量 且 小于2级阶梯数量
    let buy_num = dataSource.value[index].minBuyNum;
    if (buy_num !== undefined && buy_num !== null && buy_num !== '') {
      let min_level_num = 0;
      let max_level_num = 0;
      if (ladderType.value == 2) {
        let level_price_temp = levelPriceData.value.filter(item => item.key == 'lever_price_info');
        if (level_price_temp.length > 0 && level_price_temp[0].data) {
          if (level_price_temp[0].data.length > 0) {
            min_level_num = level_price_temp[0].data[0].level_num;

          }
          if (level_price_temp[0].data.length > 1) {
            max_level_num = level_price_temp[0].data[1].level_num;
          }
        }
      } else if (ladderType.value == 3) {
        if (dataSource.value[index].ladderPrice) {
          if (dataSource.value[index].ladderPrice.length > 0) {
            min_level_num = dataSource.value[index].ladderPrice[0].level_num;
          }
          if (dataSource.value[index].ladderPrice.length > 1) {
            max_level_num = dataSource.value[index].ladderPrice[1].level_num;
          }
        }
      }
      if (min_level_num > 0 && buy_num < min_level_num) {
        dataSource.value[index][key] = min_level_num;
        updateTableData(index, key, min_level_num);
        dataSourceForm.value.list[index][key] = min_level_num;
      } else if (max_level_num > 0 && buy_num >= max_level_num) {
        dataSource.value[index][key] = max_level_num - 1;
        updateTableData(index, key, max_level_num - 1);
        dataSourceForm.value.list[index][key] = max_level_num - 1;
      }
    }
  }
}
// dev_supplier-end

// 表格分页
const paginationChange = () => {
  currentNum.value = (getPaginationRef().current - 1) * 10
}

// 格式化百分比
const formatScaling = (scaling?: number): string => {
  if (scaling === undefined || scaling === null) return '';
  return `${(scaling * 100).toFixed(1)}%`;
};

// 获取百分比显示的样式类
const getScalingClass = (scaling?: number): string => {
  if (scaling === undefined || scaling === null) return '';
  const percentage = scaling * 100;
  if (percentage <= 10) return 'scaling-green';
  if (percentage <= 20) return 'scaling-orange';
  return 'scaling-red';
};
</script>
<style lang="less" scoped>
.goods_list {
  .tabs_nav {
    .ant-tabs-nav {
      margin-bottom: 0;
    }
  }

  .plateform_add {
    .plateform_add_step {
      position: relative;
      height: 70px;
      color: #323233;
      font-size: 14px;
      font-weight: 600;
      line-height: 70px;
      text-align: center;

      .plateform_add_step_item {
        position: relative;
        flex-shrink: 0;
        width: 32.2%;
        padding: 0 10px;
        background-color: #eee;
        cursor: pointer;

        &::before {
          content: ' ';
          display: block;
          position: absolute;
          top: 50%;
          left: -13px;
          width: 0;
          height: 0;
          margin-top: -35px;
          border-top: 36px solid #eee;
          border-bottom: 35px solid #eee;
          border-left: 14px solid transparent;
        }

        &::after {
          content: ' ';
          display: block;
          position: absolute;
          top: 50%;
          right: -13px;
          width: 0;
          height: 0;
          margin-top: -35px;
          border-top: 36px solid transparent;
          border-bottom: 35px solid transparent;
          border-left: 14px solid #eee;
        }

        &:nth-child(1) {
          &::before {
            display: none;
          }
        }

        &:nth-child(3) {
          &::after {
            display: none;
          }
        }

        &.active {
          background: #ff9455;

          &::before {
            border-top-color: #ff9455;
            border-bottom-color: #ff9455;
          }

          &::after {
            border-left-color: #ff9455;
          }

          .plateform_add_step_item_num {
            border-color: #fff;
            color: #fff;
          }

          .plateform_add_step_item_info {

            .plateform_add_step_item_title,
            .plateform_add_step_item_desc {
              color: #fff;
            }
          }
        }

        .plateform_add_step_item_num {
          flex-shrink: 0;
          width: 50px;
          height: 50px;
          margin-right: 10px;
          margin-left: 3px;
          border: 2px solid #666;
          border-radius: 50%;
          color: #333;
          font-size: 34px;
        }

        .plateform_add_step_item_info {
          height: 70px;

          .plateform_add_step_item_title {
            color: #333;
            font-size: 18px;
            font-weight: 500;
            line-height: 30px;
          }

          .plateform_add_step_item_desc {
            display: inline-block;
            width: 100%;
            overflow: hidden;
            color: #999;
            font-size: 13px;
            font-weight: 500;
            line-height: 20px;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }

    .plateform_add_category {
      .plateform_add_category_list {
        width: 100%;
        height: 236px;
        margin-top: 13px;
        margin-bottom: 13px;

        .plateform_add_category_item {
          flex: 1;
          height: 100%;
          background: #fff;
          margin-right: 12px;
          border-radius: 4px;
          overflow-y: auto;

          &:last-child {
            margin-right: 0;
          }

          > div {
            width: 100%;
            height: 35px;
            padding: 0 12px;
            cursor: pointer;
            transition: all 0.3s;

            &:hover {
              background: #fff6f2;
            }

            &.active {
              color: #f09962;
              background: #fff6f2;
            }
          }
        }
      }

      .plateform_add_category_path {
        display: inline-block;
        width: 100%;
        height: 40px;
        margin-bottom: 15px;
        padding-left: 20px;
        border: 1px dotted #ffad2b;
        background: #fffadf;
        color: #ff3710;
        font-size: 12px;
        line-height: 40px;

        &.active {
          border-color: #ff9455;
          background: rgb(255 113 30 / 10%);
          color: #ff9455;
        }
      }

      .plateform_add_category_next {
        display: inline-block;
        margin-top: 16px;
        margin-bottom: 20px;
        padding: 12px 40px;
        border-radius: 2px;
        background: #ff9455;
        color: #fff;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
      }
    }

    .plateform_add_bottom {
      position: fixed;
      z-index: 999;
      right: 10px;
      bottom: 0;
      left: 0;
      height: 60px;
      background: #fff;
      box-shadow: 0 0 20px 0 rgb(187 187 187 / 15%);

      .plateform_add_bottom_next,
      .plateform_add_bottom_submit {
        width: 80px;
        border: 1px solid #fc701e;
        border-radius: 2px;
        font-size: 13px;
        line-height: 30px;
        text-align: center;
        cursor: pointer;
      }

      .plateform_add_bottom_next {
        margin-right: 20px;
        color: #fc701e;
      }

      .plateform_add_bottom_submit {
        background: #fc701e;
        color: #fff;
      }
    }

    .table_header {
      .table_header_xing {
        margin-right: 1px;
        color: #ff2929;
        font-size: 13px;
      }

      svg {
        position: relative;
        top: 2px;
        margin-left: 1px;
      }
    }

    .vben-basic-table .ant-spin-container .ant-table-container .ant-table-body {
      height: auto !important;
    }

    .plateform_add_info {
      .ant-col .ant-form-item-explain {
        position: absolute;
        z-index: 9;
        top: -12px;
        right: 8px;
      }

      .ant-form-item {
        margin-bottom: 0;
      }
    }
  }
}

.ladder_price_modal {
  .ladder_price_modal_title {
    position: relative;
    margin: 10px 0;

    .ladder_price_modal_title_item {
      width: 220px;
      text-align: center;

      .ladder_price_modal_title_item_title {
        display: inline-block;
        width: 20px;
      }
    }

    .ladder_price_modal_title_item_del {
      position: absolute;
      top: 8px;
      right: 8px;
    }
  }

  .ladder_price_modal_add {
    width: 76px;
    line-height: 30px;
    font-size: 13px;
    text-align: center;
    color: @primary-color;
    border: 1px solid @primary-color;
    border-radius: 4px;
    margin-top: 15px;
    margin-left: 60px;
    margin-bottom: 15px;
    cursor: pointer;
  }
}

.scaling-green {
  color: #52c41a;
  font-size: 12px;
}

.scaling-orange {
  color: #faad14;
  font-size: 12px;
}

.scaling-red {
  color: #ff4d4f;
  font-size: 12px;
}

.commission-rate {
  display: flex;
  align-items: center;
  gap: 4px;
}

.commission-label {
  color: #999;
  font-size: 12px;
}

.category-item-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
}
</style>
  