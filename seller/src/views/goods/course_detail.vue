<template>
  <div class="section_padding point_goods_list">
    <Spin :spinning="spinning">
      <div class="section_padding_back">
        <SldComHeader :title="(id !== null && id) ? L('编辑课程') : L('发布课程')" />
        <div class="plateform_add">
          <div class="flex_row_between_center plateform_add_step">
            <div class="flex_row_start_center plateform_add_step_item" :class="step >= 0 ? 'active' : ''"
              @click="nextStep(0)">
              <div class="flex_row_center_center plateform_add_step_item_num">1</div>
              <div class="flex_column_center_start plateform_add_step_item_info">
                <div class="plateform_add_step_item_title">基础信息</div>
                <div class="plateform_add_step_item_desc">请填写课程基础信息</div>
              </div>
            </div>
            <div class="flex_row_start_center plateform_add_step_item" :class="step >= 1 ? 'active' : ''"
              @click="nextStep(1)">
              <div class="flex_row_center_center plateform_add_step_item_num">2</div>
              <div class="flex_column_center_start plateform_add_step_item_info">
                <div class="plateform_add_step_item_title">其他信息</div>
                <div class="plateform_add_step_item_desc">请填写课程其余信息</div>
              </div>
            </div>
          </div>
          <!-- 基础信息 -->
          <div class="course_detail_cont" v-show="step == 0" :style="{ height: scrollHeight }">
            <StandardFormRow :width="'100%'" :left_width="130" :data="baseData" :valida="baseValida"
              @valida-event="handleValida" @callback-event="(e) => handleChange(e, 'baseData')" hideBorder="bottom" />
          </div>
          <div class="course_detail_cont" v-show="step == 1" :style="{ height: scrollHeight }">
            <template v-if="baseData[1]['value'] == 1">
              <CourseTypeConsult @change="extraChange" :result="resultData"></CourseTypeConsult>
            </template>
          </div>
        </div>
        <div class="flex_row_center_center plateform_add_bottom">
          <div class="plateform_add_bottom_next" @click="goBack">返回</div>
          <div v-if="step == 0" class="plateform_add_bottom_next" @click="nextStep(1)">下一步</div>
          <div v-else class="plateform_add_bottom_next" @click="nextStep(0)">上一步</div>
          <div class="plateform_add_bottom_submit" @click="saveAllData" v-if="step == 1">发布</div>
        </div>
      </div>
    </Spin>
  </div>
</template>
<script>
export default {
  name: 'CourseDetail',
};
</script>
<script setup>
import { getCurrentInstance, ref, onMounted } from 'vue';
import { pageClose, failTip } from '@/utils/utils';
import { aiQuestionDetail, createOrUpdateQuestion, goodsList } from '/@/api/goods/course'
import { useGlobSetting } from "@/hooks/setting";
import { useRouter, useRoute } from 'vue-router';
import { useMultipleTabStore } from '/@/store/modules/multipleTab';
import SldComHeader from '/@/components/SldComHeader/index.vue';
import StandardFormRow from '/@/components/StandardFormRow/index.vue';
import CourseTypeConsult from './components/CourseTypeConsult.vue';
import { Spin } from 'ant-design-vue';

const vm = getCurrentInstance();
const L = vm?.appContext.config.globalProperties.$sldComLanguage;
const route = useRoute();
const router = useRouter();
const id = ref(route.query.id ? route.query.id : null);
const tabStore = useMultipleTabStore();
const step = ref(0)

const spinning = ref(false)
const scrollHeight = ref(document.body.clientHeight - 48 - 40 - 32 - 60 - 70 - 14 + 'px'); //滚动区域高度
const { apiUrl } = useGlobSetting();
const baseValida = ref(false);
const validateFlag = ref(false)
const baseData = ref([
  {
    type: 'title',
    label: '基本信息',
  },
  {
    type: 'select',
    label: '课程类型',
    placeholder: '请选择课程类型',
    key: 'course_type',
    value: 1,
    data: [
      {
        id: 1, name: '咨询题目'
      }
    ],
    allowClear: true,
    right_width: '89%',
    diy: true,
    diy_key: 'name',
    diy_value: 'id',
    callback: true,
    require: true,
    rules: [{
      required: true,
      message: '请选择课程类型'
    }
    ],
  },
  {
    type: 'select',
    label: '课程合集',
    placeholder: '请选择合集',
    key: 'goods_id',
    value: undefined,
    data: [],
    allowClear: true,
    right_width: '89%',
    diy: true,
    diy_key: 'name',
    diy_value: 'id',
    callback: true,
    require: true,
    rules: [{ required: true, message: '请选择合集' }],
  },
  {
    type: 'upload_img',
    label: '封面',
    note: '课程封面图片',
    key: 'image',
    accept: '.jpg, .jpeg, .png, .JPG, .JPEG, PNG',
    action: `${apiUrl}/v3/oss/seller/upload?source=goodsQrCode`,
    method: 'post',
    data: {},
    disabled: false,
    fileList: [],
    multiple: false,
    desc: '支持JPG/PNG,大小不超过20M',
    item_width: '100%',
    height: 150,
    callback: true,
  },
  {
    type: 'input',
    require: true,
    label: '课程名称',
    key: 'topic',
    placeholder: '请输入课程名称',
    maxlength: 50,
    desc: '最多输入50个字',
    value: undefined,
    right_width: '89%',
    rules: [
      {
        required: true,
        whitespace: true,
        message: '请输入课程名称',
      },
    ],
    callback: true,
  },
  {
    type: 'input',
    label: '课程标签',
    key: 'tag',
    placeholder: '请输入课程标签',
    maxlength: 50,
    desc: '最多输入50个字',
    value: undefined,
    right_width: '89%',
    callback: true,
  },
  {
    type: 'inputnum',
    label: '期数',
    key: 'issue',
    placeholder: '请输入第几期',
    maxlength: 3,
    desc: '课程为第几期',
    value: undefined,
    right_width: '89%',
    callback: true,
  },
  {
    type: 'radio',
    require: true,
    label: '关联商品',
    key: 'is_bind_product',
    desc: '关联则为课程生成商品ID',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 },
    ],
    value: 1,
    item_width: '100%',
    callback: true,
  },
  {
    type: 'input',
    label: '价格',
    key: 'price',
    placeholder: '请输入价格',
    maxlength: 10,
    desc: '不填写价格默认不用购买',
    value: undefined,
    right_width: '89%',
    callback: true,
    require: true
  },
  {
    type: 'inputnum',
    label: '库存',
    key: 'stock',
    placeholder: '请输入库存',
    maxlength: 10,
    desc: '库存为整数',
    value: undefined,
    right_width: '89%',
    callback: true,
    require: true
  },
  {
    type: 'radio',
    require: true,
    label: '推荐课程',
    key: 'recommend',
    desc: '将课程推荐到榜单',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 },
    ],
    value: 1,
    item_width: '100%',
    callback: true,
  },
  {
    type: 'inputnum',
    label: '推荐权重',
    key: 'recommend_strength',
    placeholder: '请输入推荐权重',
    maxlength: 10,
    desc: '权重越大排名靠前',
    value: undefined,
    right_width: '89%',
    callback: true,
    disable: false
  },
  {
    type: 'radio',
    require: true,
    label: '上下架',
    key: 'up_down',
    desc: '课程上下架',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 },
    ],
    value: 1,
    item_width: '100%',
    callback: true,
  }
])
const nextStep = (e) => {
   step.value = e
}
function goBack() {
  if (window.history && window.history.length == 1) {
    pageClose();
  } else {
    const { fullPath } = route;
    tabStore.closeTabByKey(fullPath, router);
    router.back();
  }
}
function handleChange(item, key) {
  let temp = baseData.value.filter(items => items.key == item.contentItem.key);
  if (item.contentItem.key == 'image') {
    // 检查上传响应
    if (item.val.fileList && item.val.fileList.length > 0) {
      const file = item.val.fileList[0];
      const response = file.response;

      // 检查文件格式
      const allowedFormats = ['.jpg', '.jpeg', '.png', '.JPG', '.JPEG', '.PNG'];
      const fileName = file.name || '';
      const fileExt = fileName.substring(fileName.lastIndexOf('.')).toLowerCase();
      if (!allowedFormats.includes(fileExt)) {
        // 显示错误信息
        failTip('不支持的文件格式，请上传 JPG/PNG 格式的图片');
        // 清除已上传的图片
        temp[0].fileList = [];
        return;
      }

      // 检查上传响应
      if (response && response.state === 255) {
        // 显示错误信息
        failTip(response.msg);
        // 清除已上传的图片
        temp[0].fileList = [];
        return;
      }
    }
    temp[0].fileList = item.val.fileList;
  } else if (item.contentItem.key == 'recommend') {
    temp[0].value = item.val;
    baseData.value.forEach(element => {
      if (element.key == 'recommend_strength') {
        if (item.val == 2) {
          element.value = 0
          element.disable = true
        } else {
          element.value = 1
          element.disable = false
        }
      }
    });

  } else if (item.contentItem.key == 'is_bind_product') {
    temp[0].value = item.val;
    baseData.value.forEach(element => {
      if (element.key == 'price' || element.key == 'stock') {
        if (item.val == 1) {
          element.require = true
        } else {
          element.require = false
        }
      }
    });
  } else {
    if (temp.length > 0) {
      temp[0].value = item.val;
    }
  }
  console.log(baseData.value)
}
//校验事件回调
function handleValida(res) {
  console.log(res)
}
const extraData = ref([])
const resultData = ref({}) //detail
//额外数据回调
const extraChange = (data) => {
  console.log('额外数据：---', data)
  extraData.value = data
}
//发布
const saveAllData = async () => {

  const temp = baseData.value.concat(extraData.value)
  let params = {}
  temp.filter(item => item.key).map(item => {
    if (item.type == "upload_img") {
      params[item.key] = item?.fileList[0]['response']['data']['url']
    } else {
      params[item.key] = item.value
    }
  })
  if (id.value) {
    params.id = Number(id.value)
  }
  spinning.value = true
  const { code, msg } = await createOrUpdateQuestion({ ...params, price: Number(params.price) })
  spinning.value = false
  failTip(msg);
  if (code == 0) {
    goBack()
  }
  console.log('最终数据----', params)
}
const getCourseDetail = async () => {
  spinning.value = true
  const { data } = await aiQuestionDetail({ id: Number(id.value) })
  spinning.value = false
  resultData.value = data
  for (let i = 0; i < baseData.value.length; i++) {
    const { key } = baseData.value[i]
    if (key == 'image' && data.image != '') {
      baseData.value[i]['fileList'] = [{
        uid: data[key],
        name: data[key],
        status: 'done',
        url: data[key],
        response: {
          data: {
            path: data[key],
            url: data[key],
          }
        }
      }]
    }
    else if (key && data[key]) {
      baseData.value[i]['value'] = data[key]
    }
  }
}
const getGoodsCollet = async () => {
  const res = await goodsList({});
  let temp = baseData.value.filter(items => items.key == 'goods_id');
  temp[0]['data'] = res.data.list.map(item => {
    return {
      id: item.id,
      name: item.goods_name
    }
  })
  console.log(baseData.value)
}
onMounted(() => {
  getGoodsCollet()
  if (id.value) {
    getCourseDetail()
  }
});
</script>
<style lang="less">
.plateform_add {
  .plateform_add_step {
    position: relative;
    height: 70px;
    color: #323233;
    font-size: 14px;
    font-weight: 600;
    line-height: 70px;
    text-align: center;
    margin-right: 20px;

    .plateform_add_step_item {
      position: relative;
      flex-shrink: 0;
      width: 47%;
      padding: 0 10px;
      background-color: #eee;
      cursor: pointer;

      &::before {
        content: ' ';
        display: block;
        position: absolute;
        top: 50%;
        left: -13px;
        width: 0;
        height: 0;
        margin-top: -35px;
        border-top: 36px solid #eee;
        border-bottom: 35px solid #eee;
        border-left: 14px solid transparent;
      }

      &::after {
        content: ' ';
        display: block;
        position: absolute;
        top: 50%;
        right: -13px;
        width: 0;
        height: 0;
        margin-top: -35px;
        border-top: 36px solid transparent;
        border-bottom: 35px solid transparent;
        border-left: 14px solid #eee;
      }

      &:nth-child(1) {
        &::before {
          display: none;
        }
      }

      &:nth-child(3) {
        &::after {
          display: none;
        }
      }

      &.active {
        background: #ff9455;

        &::before {
          border-top-color: #ff9455;
          border-bottom-color: #ff9455;
        }

        &::after {
          border-left-color: #ff9455;
        }

        .plateform_add_step_item_num {
          border-color: #fff;
          color: #fff;
        }

        .plateform_add_step_item_info {

          .plateform_add_step_item_title,
          .plateform_add_step_item_desc {
            color: #fff;
          }
        }
      }

      .plateform_add_step_item_num {
        flex-shrink: 0;
        width: 50px;
        height: 50px;
        margin-right: 10px;
        margin-left: 3px;
        border: 2px solid #666;
        border-radius: 50%;
        color: #333;
        font-size: 34px;
      }

      .plateform_add_step_item_info {
        height: 70px;

        .plateform_add_step_item_title {
          color: #333;
          font-size: 18px;
          font-weight: 500;
          line-height: 30px;
        }

        .plateform_add_step_item_desc {
          display: inline-block;
          width: 100%;
          overflow: hidden;
          color: #999;
          font-size: 13px;
          font-weight: 500;
          line-height: 20px;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }

  .plateform_add_category {
    .plateform_add_category_list {
      width: 100%;
      height: 236px;
      margin-top: 13px;
      margin-bottom: 13px;

      .plateform_add_category_item {
        flex: 1;
        height: 100%;
        background: #fff;
        margin-right: 12px;
        border-radius: 4px;
        overflow-y: auto;

        &:last-child {
          margin-right: 0;
        }

        >div {
          width: 100%;
          height: 35px;
          padding: 0 12px;
          cursor: pointer;
          transition: all 0.3s;

          &:hover {
            background: #fff6f2;
          }

          &.active {
            color: #f09962;
            background: #fff6f2;
          }
        }
      }
    }

    .plateform_add_category_path {
      display: inline-block;
      width: 100%;
      height: 40px;
      margin-bottom: 15px;
      padding-left: 20px;
      border: 1px dotted #ffad2b;
      background: #fffadf;
      color: #ff3710;
      font-size: 12px;
      line-height: 40px;

      &.active {
        border-color: #ff9455;
        background: rgb(255 113 30 / 10%);
        color: #ff9455;
      }
    }

    .plateform_add_category_next {
      display: inline-block;
      margin-top: 16px;
      margin-bottom: 20px;
      padding: 12px 40px;
      border-radius: 2px;
      background: #ff9455;
      color: #fff;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
    }
  }
}

.section_padding_back {
  position: relative;
  padding-bottom: 80px;
}

.plateform_add_bottom {
  position: absolute;
  z-index: 999;
  right: 10px;
  bottom: 0;
  left: 0;
  height: 60px;
  background: #fff;
  box-shadow: 0 0 20px 0 rgb(187 187 187 / 25%);

  .plateform_add_bottom_next,
  .plateform_add_bottom_submit {
    width: 80px;
    border: 1px solid #fc701e;
    border-radius: 2px;
    font-size: 13px;
    line-height: 30px;
    text-align: center;
    cursor: pointer;
  }

  .plateform_add_bottom_next {
    margin-right: 20px;
    color: #fc701e;
  }

  .plateform_add_bottom_submit {
    background: #fc701e;
    color: #fff;
  }
}

.course_detail_cont {
  overflow-y: auto;
}
</style>
