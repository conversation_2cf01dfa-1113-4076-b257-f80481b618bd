<template>
  <div class="section_padding related_template_to_add">
    <div class="section_padding_back">
      <div class="related_template_page">
        <div class="common_page_title">基本信息</div>
        <BasicForm @register="register" style="margin-top: 15px" />
        <div class="common_page_title">内容编辑</div>
        <div style="margin-top: 20px">
          <SldUEditor
            v-if="editorFlag"
            :id="'agreement'"
            :initEditorContent="initEditorContent"
            :getContentFlag="getEditorContentFlag"
            :getEditorContent="getEditorContent"
          />
        </div>
      </div>
      <div style="width: 100%; height: 50px"></div>
      <div class="m_diy_bottom_wrap" :style="{ position: 'fixed', left: getRealWidth + 10 + 'px' }">
        <div class="add_goods_bottom_btn" @click="handleBack">返回</div>
        <div class="add_goods_bottom_btn add_goods_bottom_btn_sel" @click="handleConfirm">保存并返回</div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
  import { defineComponent, ref, onMounted } from 'vue';
  import { Input } from 'ant-design-vue';
  import { useRoute, useRouter } from 'vue-router';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { failTip, quillEscapeToHtml, sucTip } from '/@/utils/utils';
  import {
    getGoodsRelatedTemplateDetailApi,
    addGoodsRelatedTemplateApi,
    editGoodsRelatedTemplateApi
  } from '/@/api/goods/goods';
  import SldUEditor from '/@/components/SldUEditor/index.vue';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import { pageClose } from '/@/utils/utils';
  import { useMultipleTabStore } from '/@/store/modules/multipleTab';

  
  export default defineComponent({
    components: {
      BasicForm,
      Input,
      SldUEditor,
    },
    setup() {
      const { getRealWidth } = useMenuSetting();
      const tabStore = useMultipleTabStore();
      const route = useRoute();
      const router = useRouter();
      const editorFlag = ref(false);
      const [register, { validate, getFieldsValue, setFieldsValue }] = useForm({
        labelWidth: 90,
        baseRowStyle: {
          display: 'block',
        },
        schemas: [
          {
            field: 'templateName',
            component: 'Input',
            label: '版式名称',
            componentProps: {
              placeholder: '请输入版式名称',
              maxLength: 10,
            },
            colProps: {
              span: 8,
            },
            required: true,
            rules: [
              {
                required: true,
                whitespace: true,
                message: '请输入版式名称',
              },
            ],
          },
          {
            field: 'templatePosition',
            component: 'RadioGroup',
            label: '版式位置',
            componentProps: {
              options: [
                { label: '顶部', value: 1 },
                { label: '底部', value: 2 },
              ],
            },
            colProps: {
              span: 8,
            },
            required: true,
          }
        ],
        showActionButtonGroup: false,
      });

      const initEditorContent = ref(''); //百度编辑器内容
      const getEditorContentFlag = ref(false); //获取百度编辑器内容标识

      onMounted(() => {
        if (route.query.id) {
          editorFlag.value = false;
          get_detail({ templateId: route.query.id });
        } else {
          editorFlag.value = true;
          setFieldsValue({
            templatePosition: 1
          });
        }
      });

      //获取详情
      const get_detail = async (params) => {
        const res = await getGoodsRelatedTemplateDetailApi(params);
        if (res.state == 200) {
          initEditorContent.value = quillEscapeToHtml(res.data.templateContent);
          editorFlag.value = true;
          setFieldsValue({
            templateName: res.data.templateName,
            templatePosition: res.data.templatePosition ? res.data.templatePosition : undefined,
          });
        } else {
          failTip(res.msg);
        }
      };

      //获取编辑器内容
      function getEditorContent(con) {
        getEditorContentFlag.value = false;
        save_template({
          templateName: getFieldsValue().templateName,
          templatePosition: getFieldsValue().templatePosition ? getFieldsValue().templatePosition : '',
          templateContent: con,
        });
      }

      function handleBack() {
        if (window.history && window.history.length == 1) {
          pageClose();
        } else {
          const { fullPath } = route;
          tabStore.closeTabByKey(fullPath, router);
          router.back();
        }
      }

      function handleConfirm() {
        const data = validate();
        if (!getFieldsValue().templateName || !getFieldsValue().templateName.trim()) {
          return;
        }
        getEditorContentFlag.value = true;
      }

      //更新协议数据
      const save_template = async (params) => {
        let res = null;
        if (route.query.id) {
          params.templateId = route.query.id;
          res = await editGoodsRelatedTemplateApi(params);
        } else {
          res = await addGoodsRelatedTemplateApi(params);
        }
        if (res.state == 200) {
          sucTip(res.msg);
          setTimeout(() => {
            handleBack();
          }, 1500);
        } else {
          failTip(res.msg);
        }
      };

      return {
        editorFlag,
        register,
        getRealWidth,
        get_detail,
        initEditorContent,
        getEditorContentFlag,
        getEditorContent,
        handleBack,
        handleConfirm,
        save_template,
        tabStore
      };
    },
  });
</script>
<style lang="less">
  .related_template_to_add {
    height: 100%;

    .common_page_title {
      display: flex;
      position: relative;
      align-items: center;
      justify-content: flex-start;
      height: 22px;
      padding-left: 10px;
      color: #101010;
      font-size: 14px;
      font-weight: 700;
      line-height: 22px;

      &::before {
        content: '';
        display: inline-block;
        position: absolute;
        top: 50%;
        left: 0;
        width: 4px;
        height: 14px;
        margin-top: -6px;
        background: #fa6f1e;
      }
    }
  }
  .related_template_to_add{
    .related_template_page{
      max-height: calc(100vh - 51px - 122px);
      overflow: auto;
    }
  }
  .related_template_page {
    .vben-basic-form .ant-form-item:not(.ant-form-item-with-help),
    .vben-basic-form .ant-form-item {
      height: 50px !important;
      margin-bottom: 0px !important;
    }
    .ant-form-item-with-help .ant-form-item-explain {
      position: absolute;
      right: 10px;
      top: -11px;
      z-index: 9;
      background: #fff;
    }
  }
</style>
