<template>
  <div class="section_padding">
    <div class="section_padding_home_scroll">
      <!-- 店铺信息start -->
      <Spin :spinning="storeInfoLoading">
        <div class="flex flex-row justify-start bg-white pl-35px pr-35px pt-20px pb-30px">
          <div class="flex flex-row items-center justify-start min-w-1/2 sm300">
            <div class="main_left_info_main">
              <Image
                :src="storeInfo.storeLogoUrl"
                class="block w-full"
                :height="115"
                :width="115"
              />
            </div>
            <div class="stat-base_info ml-23px">
              <div class="stat-title text_color_bl flex_row_start_center">
                <span>{{ storeInfo.storeName || '--' }}</span>
                <!-- dev_o2o1-start -->
                <div class="o2o_shop_type flex_row_center_center" v-if="storeInfo.shopType==2">O2O门店</div>
                <!-- dev_o2o1-end -->
                <!-- dev_supplier-start -->
                <div class="o2o_shop_type flex_row_center_center" v-if="storeInfo.shopType==3">供应商店铺</div>
                <!-- dev_supplier-end -->
              </div>
              <div class="mt-4 text-sm stat-username text_color_bl">
                <span>{{ L('登录账号：') }}</span>
                <span class="text-[#2878FF] text_vendorName">{{ storeInfo.vendorName || '--' }}</span>
              </div>
              <div class="mt-1.5 flex">
                <div class="text_color_bl text-sm"
                  >{{ L('店铺类型：') }}{{ storeInfo.isOwnStoreValue || '--' }}</div
                >
                <div class="text_color_bl text-sm ml-10"
                  >{{ L('店铺权限：') }}{{ storeInfo.rolesName || '--' }}</div
                >
              </div>
              <div class="mt-1.5 text_color_bl text-sm"
                >{{ L('店铺有效期限：')
                }}{{ storeInfo.isOwnStore == 1 ? L('永久有效') : storeInfo.storeExpireTime }}</div
              >
            </div>
          </div>
  
          <div class="store_score flex_row_start_center">
            <div class="all_store_panel flex_column_center_center">
              <img src="@/assets/images/star_icon.png" />
              <span class="title">{{ L('店铺评分') }}</span>
              <span class="num">{{ storeInfo.comprehensiveScore }}</span>
            </div>
            <div class="right_store_level">
              <ul>
                <li class="flex_row_start_center">
                  <div class="label">{{ L('描述相符：') }}</div>
                  <div class="level">
                    <Rate allowHalf disabled :value="storeInfo.descriptionScore" />
                  </div>
                  <div class="score">{{ storeInfo.descriptionScore }}</div>
                </li>
                <li class="flex_row_start_center">
                  <div class="label">{{ L('服务态度：') }}</div>
                  <div class="level">
                    <Rate allowHalf disabled :value="Number(storeInfo.serviceScore)" />
                  </div>
                  <div class="score">{{ storeInfo.serviceScore }}</div>
                </li>
                <li class="flex_row_start_center">
                  <div class="label">{{ L('物流服务：') }}</div>
                  <div class="level">
                    <Rate allowHalf disabled :value="storeInfo.deliverScore" />
                  </div>
                  <div class="score">{{ storeInfo.deliverScore }}</div>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </Spin>
      <!-- 店铺信息end -->
  
      <div class="info_count bg-white flex_row_center_center">
        <div
          class="info_count_unit flex_column_center_center cursor-pointer"
          @click="go('/order/order_deliver')"
        >
          <div class="flex flex-row items-center">
            <i class="count_dot"></i>
            <span class="text-black text-base">{{ L('待发货') }}</span>
          </div>
          <div class="">
            <CountTo
              :endVal="sellerWaitDeal.deliverOrderNum || 0"
              style="font-size: 34px; font-weight: 700"
            />
          </div>
        </div>
        <div
          class="info_count_unit flex_column_center_center cursor-pointer"
          @click="go('/order/service')"
        >
          <div class="flex flex-row items-center">
            <i class="count_dot"></i>
            <span class="text-black text-sm">{{ L('售后退货单') }}</span>
          </div>
          <div class="">
            <CountTo
              :endVal="sellerWaitDeal.orderReturnNum || 0"
              style="font-size: 34px; font-weight: 700"
            />
          </div>
        </div>
        <div
          class="info_count_unit flex_column_center_center cursor-pointer"
          @click="go('/goods/goods_list?tab=4')"
        >
          <div class="flex flex-row items-center">
            <i class="count_dot"></i>
            <span class="text-black text-sm">{{ L('违规商品') }}</span>
          </div>
          <div class="">
            <CountTo
              :endVal="sellerWaitDeal.sysLowerGoodsNum || 0"
              style="font-size: 34px; font-weight: 700"
            />
          </div>
        </div>
        <div
          class="info_count_unit flex_column_center_center cursor-pointer"
          @click="go('/bill/lists')"
        >
          <div class="flex flex-row items-center">
            <i class="count_dot"></i>
            <span class="text-black text-sm">{{ L('待确定结算单') }}</span>
          </div>
          <div class="">
            <CountTo
              :endVal="sellerWaitDeal.confirmBillNum || 0"
              style="font-size: 34px; font-weight: 700"
            />
          </div>
        </div>
      </div>
  
      <!-- 今日数据start -->
      <div class="flex flex-row w-full">
        <div
          class="relative mt-[10px] today_info_panel lg:min-w-3/10 flex-1"
          v-for="(item, index) in todayStatData"
          :key="index"
        >
          <div class="flex justify-between items-center mx-16px !ml-0">
            <div class="position-title">{{ item.label }}</div>
            <div class="update_time">{{ item.statsTime }}</div>
          </div>
          <Spin :spinning="item.loading">
            <div class="flex flex-wrap mt-45px ml-2/14">
              <div class="w-2/4 h-103px" v-for="(children, cIndex) in item.children" :key="cIndex">
                <SldStatCard
                  :label="children.key"
                  :tip="children.tip"
                  :value="children.value"
                  :is-bold="false"
                  :is-money="children.isMoney"
                />
              </div>
            </div>
          </Spin>
        </div>
      </div>
      <!-- 今日数据end -->
  
      <!-- 支付下单趋势---流量趋势start -->
      <div class="flex flex-row justify-center">
        <SldStatCharts
          :title="L('支付/下单金额趋势')"
          mode="linear"
          :date-picker="true"
          :api-url="API.GET_PAYTREND"
          :data-handler="payTrendDataHandler"
        />
        <div class="min-w-2"></div>
        <SldStatCharts
          mode="linear"
          :title="L('流量趋势')"
          name="flowTrend"
          :date-picker="true"
          :api-url="API.GET_VIEWTREND"
          :data-handler="flowTrendDataHandler"
        />
      </div>
      <!-- 支付下单趋势---流量趋势end -->
  
      <!-- 店铺销售排行-商品销售排行start -->
      <div class="flex flex-row">
        <SldStatRank
          :options="goodsRankOption"
          :title="L('商品销售排行 - TOP10')"
          :date-picker="true"
          :is-index="true"
          @register="goodsRank_register"
        />
        <div class="min-w-2"></div>
        <SldStatRank
          :options="seckillRankOption"
          :title="L('秒杀活动')"
          :date-picker="false"
          :is-index="true"
          @register="seckillRank_register"
        >
          <template #rightSlot>
            <div class="cursor-pointer text-xs text-[#ff7324]" @click="goCheckSeckill('more')">{{
              L('查看更多')
            }}</div>
          </template>
          <template #action="{ seckillId }">
            <span
              class="cursor-pointer rounded-sm p-1 hover:bg-[#ff7324] hover:text-white"
              @click="goCheckSeckill('join', seckillId)"
              >{{ L('立即参加') }}</span
            >
          </template>
        </SldStatRank>
      </div>
      <!-- 店铺销售排行-商品销售排行end -->
    </div>
  </div>
</template>

<script setup>
  import { API, getTodaySummaryApi } from '@/api/sys/statAnalysis';
  import { getStoreInfoApi } from '@/api/store';
  import { Api as SeckillApi } from '@/api/promotion/seckill';

  import { SldStatCharts, SldStatRank, useStatRank, SldStatCard } from '/@/components/SldStat';
  import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
  import { handleTodayData } from './stat_action';
  import { Spin, Image, Rate } from 'ant-design-vue';
  import { CountTo } from '@/components/CountTo';
  import { useGo } from '/@/hooks/web/usePage';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const todayStatData = ref([]);
  const loadState = reactive({
    brandSale: false,
    payTrend: false,
    newTrend: false,
    region: false,
    flowTrend: false,
  });
  const payTrendOption = reactive({});
  const flowTrendOption = reactive({});

  const storeInfoLoading = ref(true);
  const storeInfo = ref({});
  const sellerWaitDeal = ref({});

  //支付下单趋势start--------------------------------------------
  const payTrendDataHandler = (return_data) => {
    const { orderAmountList, payAmountList } = return_data;
    const orderSubmitAmountList = orderAmountList.map((or) => or.orderSubmitAmount);
    const orderPayAmountList = payAmountList.map((or) => or.orderPayAmount);
    loadState.payTrend = false;
    payTrendOption.xAxisData = orderAmountList.map((item) => item.statsTime);
    payTrendOption.series = [
      {
        name: L('下单金额'),
        data: orderSubmitAmountList,
        type: 'line',
      },
      {
        name: L('支付金额'),
        data: orderPayAmountList,
        type: 'line',
      },
    ];
    payTrendOption.maxMount = Math.max.apply(null, [
      ...orderSubmitAmountList,
      ...orderPayAmountList,
    ]);
    return payTrendOption;
  };
  //支付下单趋势end--------------------------------------------

  //流量趋势start-------------------------------------------
  const flowTrendDataHandler = (return_data) => {
    const { viewNumList, visitorNumList } = return_data;
    const viewNumLists = viewNumList.map((or) => or.viewNum);
    const visitorNumLists = visitorNumList.map((or) => or.visitorNum);
    loadState.flowTrend = false;
    flowTrendOption.xAxisData = viewNumList.map((item) => item.statsTime);
    flowTrendOption.series = [
      {
        name: L('访问量'),
        data: viewNumLists,
        type: 'line',
      },
      {
        name: L('访客数'),
        data: visitorNumLists,
        type: 'line',
      },
    ];
    flowTrendOption.maxMount = Math.max.apply(null, [...viewNumLists, ...viewNumLists]);
    return flowTrendOption;
  };
  //流量趋势end ---------------------------------------------

  // 商品销售排行start-------------------------------------
  const goodsRankOption = [
    {
      title: L('商品名称'),
      dataIndex: 'goodsName',
      customRenderType: 'goodsRender',
    },
    {
      title: L('销售额'),
      dataIndex: 'saleAmount',
      sorter: true,
      sortDirections: ['descend'],
      customRenderType: 'priceRender',
    },
    {
      title: L('销量'),
      dataIndex: 'saleNum',
      sorter: true,
      sortDirections: ['descend'],
    },
  ];
  const goodsRankParamHandler = (dateParam, sortParam) => {
    let target = {
      ...dateParam,
    };
    if (sortParam && sortParam.order) {
      target.sort = sortParam.field == 'saleAmount' ? 1 : 2;
    }
    return target;
  };
  // @ts-ignore
  const { register: goodsRank_register } = useStatRank({
    apiUrl: API.GET_GOODS_RANK,
    paramHandler: goodsRankParamHandler,
  });
  // 商品销售排行end---------------------------------------

  // 秒杀活动start-------------------------------------
  const go = useGo();
  const seckillRankOption = [
    {
      title: L('活动名称'),
      dataIndex: 'seckillName',
    },
    {
      title: L('活动时间'),
      dataIndex: 'startTime',
    },
    {
      title: L('活动状态'),
      dataIndex: 'stateValue',
    },
    {
      title: L('操作'),
      dataIndex: 'action',
    },
  ];
  const { register: seckillRank_register } = useStatRank({
    apiUrl: SeckillApi.GET_SECKILL_LIST,
    paramHandler: () => ({ pageSize: 10 }),
    dataHandler: (return_data) => return_data.list,
  });

  const goCheckSeckill = (type, id) => {
    if (type == 'more') {
      go('/marketing/seckill');
    } else if (type == 'join') {
      go({
        path: '/marketing/seckill_to_add',
        query: {
          id,
        },
      });
    }
  };

  //秒杀活动end----------------------------

  //获取店铺信息
  const fetchStoreInfo = async () => {
    const result = await getStoreInfoApi();
    if (result?.state == 200) {
      storeInfoLoading.value = false;
      storeInfo.value = result.data;
    }
  };

  const { formList, actions } = handleTodayData();
  const fetchTodaySummary = async () => {
    const res = await getTodaySummaryApi();
    if (res.state == 200) {
      sellerWaitDeal.value = res.data.sellerWaitDeal;
      todayStatData.value = formList.value;
      actions({
        statsTime: res.data.statsTime,
        ...res.data.sellerSummary,
        ...res.data.sellerTodaySummary,
      });
    }
  };

  onMounted(() => {
    fetchStoreInfo();
    fetchTodaySummary();
  });
</script>

<style>
  @media (width <=768px) {
    .sm300 {
      min-width: 300px;
    }

    .small_screen {
      width: 1200px;
    }
  }

  .main_left_info_main .ant-image{
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
<style scoped lang="less">
  .position-title {
    position: relative;
    padding-left: 8px;
    border-left: 4px solid @primary-color;
    color: #3a3e43;
    font-size: 14px;
    font-weight: 700;
    line-height: 18px;
    white-space: nowrap;
  }

  .today_info_panel {
    position: relative;
    min-width: auto;
    height: 296px;
    margin-right: 10px;
    padding: 15px 10px 10px;
    background-color: #fff;

    &:last-child {
      margin-right: 0;
    }

    .update_time {
      color: @primary-color;
      font-size: 13px;
    }

    .position-title {
      position: relative;
      height: 18px;
      padding-left: 8px;
      border-left: 4px solid @primary-color;
      color: #3a3e43;
      font-size: 14px;
      font-weight: 700;
      line-height: 18px;
      white-space: nowrap;
    }
  }

  .basic-goods {
    width: 20%;
    height: 130px;
    margin-top: 20px;
    margin-right: 16px;
    margin-bottom: 19px;
    background-color: #fff;
    box-shadow: 0 2px 22px 0 rgb(0 0 0 / 3%);
    cursor: pointer;
  }

  .basic-item_num {
    margin-top: 9px;
    color: #333;
    font-size: 26px;
    font-weight: 700;
    line-height: 26px;
  }

  .basic-item_title {
    opacity: 0.72;
    color: #333;
    font-size: 15px;
  }

  .main_left_info_main {
    width: 115px;
    height: 115px;
    border-radius: 6px;
    background: #fff;
    box-shadow: 0 0 2px 0 rgb(196 196 196 / 50%);
  }

  .stat-base_info {
    .stat-title {
      font-size: 18px;
      // dev_o2o2-start
      .o2o_shop_type{
        padding: 0 5px;
        background: linear-gradient(90deg, #04B422, #5FCA5E);
        border-radius: 10px 10px 10px 0px;
        margin-left: 10px;
        font-size: 13px;
        color: #FFFFFF;
      }
      // dev_o2o2-end
    }
  }

  .text_color_bl {
    color: #404040;

    .text_vendorName {
      color: @primary-color;
    }
  }

  .store_score {
    padding-left: 32px;

    .right_store_level {
      margin-left: 30px;

      ul {
        li {
          .label {
            color: #404040;
            font-size: 14px;
            position: relative;
            bottom: 4px;
          }

          .level {
            transform: scale(0.8);
          }

          .score {
            color: #404040;
            font-size: 14px;
            position: relative;
            bottom: 4px;
          }
        }
      }
    }

    .all_store_panel {
      width: 117px;
      height: 117px;
      background: #ffffff;
      box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.05);
      border-radius: 8px;

      img {
        width: 44px;
        height: 44px;
      }

      .title {
        color: #404040;
        font-size: 14px;
        margin-top: 2px;
      }

      .num {
        color: #262626;
        font-size: 20px;
      }
    }
  }

  .count_dot {
    display: block;
    width: 10px;
    height: 10px;
    background-color: #f0be35;
    border-radius: 10px;
    margin-right: 12px;
  }

  .info_count {
    border-top: 2px solid #f7f7f7;
    padding: 37px 0;
    .info_count_unit {
      border-right: 1px solid hsla(0, 0%, 84.7%, 0.5);
      flex: 1;
      &:last-child{
        border-right: none;
      }
    }
  }
</style>
