import { ref } from 'vue';
import {
  getStat_UserInfo,
} from '@/api/sys/statAnalysis';
import { sldComLanguage } from '@/utils/utils';

function handlerApi (data) {
  this.form.loading = false;
  this.form.children.map((item) => {
    item.value = data[item.mapKey];
  });
  this.form.statsTime = data.statsTime;
}
const todayTradeData = {
  form: {
    update_time: '',
    label: sldComLanguage('今日交易概况'),
    loading: true,
    key: 'todayTrade',
    statsTime: '',
    children: [
      {
        key: sldComLanguage('下单数'),
        value: '',
        tip: sldComLanguage('统计时间内，客户成功提交订单的笔数。'),
        mapKey: 'orderSubmitNum',
      },
      {
        key: sldComLanguage('下单金额(元)'),
        value: '',
        tip: sldComLanguage('统计时间内，客户成功提交订单的总金额。'),
        mapKey: 'orderSubmitAmount',
        isMoney: true,
      },
      {
        key: sldComLanguage('支付订单数'),
        value: '',
        tip: sldComLanguage('统计时间内，该店铺下所有已支付订单的总数量。'),
        mapKey: 'orderPayNum',
      },
      {
        key: sldComLanguage('支付金额(元)'),
        value: '',
        tip: sldComLanguage('统计时间内，所有支付订单金额之和，会员储值不计算在内。拼团在成团时计入支付金额；定金预售在尾款支付时计入支付金额。'),
        mapKey: 'orderPayAmount',
        isMoney: true,
      },
    ],
  },
  handlerApi,
}; //今日交易信息
const todayFlowData = {
  form: {
    update_time: '',
    label: sldComLanguage('今日流量概况'),
    loading: true,
    key: 'todayFlow',
    statsTime: '',
    children: [
      {
        key: sldComLanguage('店铺访客数'),
        value: '',
        tip: sldComLanguage('统计时间内，店铺内所有页面被访问的去重人数。'),
        mapKey: 'visitorNum',
      },
      {
        key: sldComLanguage('店铺浏览量'),
        value: '',
        tip: sldComLanguage('统计时间内，店铺内所有页面被访问的次数。'),
        mapKey: 'viewNum',
      },
      {
        key: sldComLanguage('商品访客数'),
        value: '',
        tip: sldComLanguage('统计时间内，访问店铺内商品详情页的去重人数。'),
        mapKey: 'goodsVisitorNum',
      },
      {
        key: sldComLanguage('商品浏览量'),
        value: '',
        tip: sldComLanguage('统计时间内，访问店铺内商品详情页的次数。'),
        mapKey: 'goodsViewNum',
      },
    ],
  },
  handlerApi,
}; //今日流量信息
const todayGoodsData = {
  form: {
    update_time: '',
    label: sldComLanguage('今日商品概况'),
    loading: true,
    key: 'todayGoods',
    statsTime: '',
    children: [
      {
        key: sldComLanguage('商品总数'),
        value: '',
        tip: sldComLanguage('截止至当前时间，店铺内商品spu总数。'),
        mapKey: 'goodsNum',
      },
      {
        key: sldComLanguage('新增商品数'),
        value: '',
        tip: sldComLanguage('统计时间内，店铺新增商品spu数。'),
        mapKey: 'newGoodsNum',
      },
      {
        key: sldComLanguage('在售商品数'),
        value: '',
        tip: sldComLanguage('截止至当前时间状态为在售的商品数量。'),
        mapKey: 'saleGoodsNum',
      },
      {
        key: sldComLanguage('动销商品数'),
        value: '',
        tip: sldComLanguage('统计时间内，销量不为0的商品数。'),
        mapKey: 'salingGoodsNum',
      },
    ],
  },
  handlerApi,
}; //今日商品信息
const todayMemberData = {
  form: {
    update_time: '',
    label: sldComLanguage('今日用户概况'),
    loading: true,
    key: 'todayMember',
    statsTime: '',
    children: [
      {
        key: sldComLanguage('用户总数'),
        value: '',
        tip: sldComLanguage('在本店铺有过浏览行为的历史去重人数总和。'),
        mapKey: 'memberNum',
      },
      {
        key: sldComLanguage('新增用户数'),
        value: '',
        tip: sldComLanguage('统计时间内，注册并首次访问店铺的用户数。'),
        mapKey: 'newMemberNum',
      },
      {
        key: sldComLanguage('支付客单价'),
        value: '',
        tip: sldComLanguage('统计时间内，支付金额/支付人数。'),
        mapKey: 'orderPayAtv',
        isMoney: true,
      },
      {
        key: sldComLanguage('支付人数'),
        value: '',
        tip: sldComLanguage('统计时间内，成功付款的去重人数，拼团在成团时计入支付人数；定金预售在尾款支付时计入支付人数。'),
        mapKey: 'orderPayMemberNum',
      },
    ],
  },
  apiFunction: getStat_UserInfo,
  handlerApi,
}; //今日会员信息
export const handleTodayData = () => {
  const formList = ref([
    todayTradeData.form,
    todayFlowData.form,
    todayGoodsData.form,
    todayMemberData.form,
  ]);
  const actions = (data) => {
    [todayTradeData, todayFlowData, todayGoodsData, todayMemberData].forEach((item) =>
      item.handlerApi(data),
    );
  };
  return { formList, actions };
};
