<template>
  <div class="section_padding">
    <div class="section_padding_back" style="display:flex;flex-direction:column">
      <SldComHeader :title="L('消息接收设置')" />
      <div class="msg_setting">
        <BasicTable 
          :columns="columns"
          :bordered="true"
          :pagination="false"
          :clickToRowSelect="false"
          :canResize="false"
          rowKey="tplCode"
          :loading="loading"
          :dataSource="goods_table" style="padding: 0">
          <template #bodyCell="{ column, record, text, index }">
            <template v-if="column.key=='isReceive'">
              <Switch
                :checked="text == '1' ? true : false"
                @change="(checked) => handleChange({
                tplCode: record.tplCode,
                isReceive: checked ? 1 : 0,
              })"
              />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'StoreMsgSetting',
  };
</script>
<script setup>
  import { getCurrentInstance, onMounted, ref } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { BasicTable } from '/@/components/Table';
  import { Switch } from 'ant-design-vue';
  import { sucTip, failTip } from '/@/utils/utils';
  import {
    getMsgSettingListApi,
    getMsgSettingIsReceiveApi
  } from '/@/api/store/msg_lists';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const loading = ref(false)
  const columns = ref([
    {
    title: L('消息模板'),
    dataIndex:'tplName',
    width: 250,
    },
    {
      title: L('是否接收'),
      dataIndex: 'isReceive',
      width: 100,
    },
  ])

  const goods_table = ref([])

  const  getList = async()=> {
    loading.value = true
    let res = await getMsgSettingListApi()
    if(res.state == 200){
      loading.value = false
      goods_table.value = res.data
    }
  }

  // 是否接收
  const handleChange = async(id)=> {
    let res = await getMsgSettingIsReceiveApi(id)
    if(res.state == 200){
      sucTip(res.msg)
      getList()
    }else{
      failTip(res.msg)
    }
  }

  onMounted(() => {
    getList()
  });
</script>
<style lang="less">
  .msg_setting{
    flex: 1;
    overflow-y: auto;
  }
</style>
