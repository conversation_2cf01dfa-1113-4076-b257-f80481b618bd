<template>
  <div class="section_padding point_goods_list">
    <div class="section_padding_back">
      <SldComHeader
        :title="L('品牌管理')"
        type="2"
        :clickFlag="true"
        :showTipBtnData="true"
        :tipData="tipData"
        @handle-toggle-tip="handleToggleTip"
      />
      <BasicTable @register="standardTable" style="padding: 0">
        <template #tableTitle>
          <div class="toolbar">
            <div class="toolbar_btn" @click="handleClick(null, 'add')">
              <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#ff5908" />
              <span>{{ L('新增品牌') }}</span>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex == 'action'">
            <TableAction
              class="TableAction"
              :actions="[
              {
                label: L('编辑'),
                onClick: handleClick.bind(null, record, 'edit'),
              },
              {
                label: L('删除'),
                popConfirm: {
                  title: L('删除后不可恢复，是否确定删除？'),
                  placement: 'left',
                  confirm: handleClick.bind(null, record, 'del'),
                },
              },
              ]"
            />
          </template>
          <template v-else-if="column.dataIndex == 'imageUrl'">
            <Popover placement="right">
              <template #content>
                <div class="goods_list_mainIamge_pop">
                  <img :src="text" />
                </div>
              </template>
              <img :src="text" style="max-height: 30px" />
            </Popover>
          </template>
          <template v-else-if="column.dataIndex == 'onSaleGoodsNum'">
            {{ text || 0 }}/{{ record.totalGoodsNum || 0 }}
          </template>
          <template v-else-if="column.dataIndex">
            {{ text !== undefined && text !== null && text != '' ? text : '--' }}
          </template>
        </template>
      </BasicTable>
    </div>

    <SldModal
      :width="width"
      :title="title"
      :visible="visible"
      :content="content"
      :confirmBtnLoading="confirmBtnLoading"
      :showFoot="true"
      @cancle-event="handleCancle"
      @confirm-event="handleConfirm"
      @loadData-event="loadData"
    />
  </div>
</template>
<script>
export default {
  name: 'StoreSetting',
};
</script>
<script setup>
import { getCurrentInstance, ref, onMounted } from 'vue';
import { Tabs, TabPane } from 'ant-design-vue';
import SldComHeader from '/@/components/SldComHeader/index.vue';
import { Popconfirm, Popover } from 'ant-design-vue';
import { sucTip, failTip } from '@/utils/utils';
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import {
  getBrandList,
  getVendorGoodsCategoryByIdApi,
  getGoodsBrandApplyApi,
  delBrandApi,
  getBrandDetailApi,
  getBrandEditApi
} from '/@/api/store/brand_lists';
import SldModal from '@/components/SldModal/index.vue';

const vm = getCurrentInstance();
const L = vm?.appContext.config.globalProperties.$sldComLanguage;

const showTip = ref(true)
const tipData = ref([L('审核通过后，申请品牌和分类的绑定关系生效，发布商品的时候选择该分类即可选择该品牌。分类需绑定到第三级。')]);

const [standardTable, { reload, redoHeight }] = useTable({
  api: (arg) => getBrandList({ ...arg }),
  fetchSetting: {
    pageField: 'current',
    sizeField: 'pageSize',
    listField: 'data.list',
    totalField: 'data.pagination.total',
  },
  ellipsis: false,
  columns: [
    {
      title: L('品牌名称'),
      dataIndex: 'brandName',
      width: 100,
    },
    {
      title: L('品牌LOGO'),
      dataIndex: 'imageUrl',
      width: 100,
    },
    {
      title: L('品牌分类'),
      dataIndex: 'goodsCategoryPath',
      width: 100,
    },
    {
      title: L('品牌描述'),
      dataIndex: 'brandDesc',
      width: 100,
    },
    {
      title: L('审核状态'),
      dataIndex: 'stateValue',
      width: 100,
    },
    {
      title: L('拒绝理由'),
      dataIndex: 'failReason',
      width: 100,
    },
  ],
  actionColumn: {
    width: 100,
    title: L('操作'),
    dataIndex: 'action',
  },
  useSearchForm: true,
  formConfig: {
    schemas: [
      {
        field: 'brandName',
        component: 'Input',
        colProps: { span: 6, style: 'width:260px;max-width:100%;flex:none' },
        componentProps: {
          minWidth: 300,
          maxlength: 6,
          placeholder: L('请输入品牌名称'),
          size: 'default',
        },
        label: L('品牌名称'),
        labelWidth: 80,
      },
    ],
  },
  bordered: true,
  striped: false,
  showIndexColumn: true,
});

const width = ref(550);
const title = ref('');
const visible = ref(false);
const content = ref([]);
const confirmBtnLoading = ref(false);
const operate_id = ref('');
const operate_type = ref('');
const click_event = ref(false);

const addData = ref([
  {
    type: 'input',
    label: L('品牌名称'),
    name: 'brandName',
    placeholder: L('请输入品牌名称'),
    extra: L('最多输入20个字'),
    maxLength: 20,
    rules: [
      {
        required: true,
        message: L('请输入品牌名称'),
      },
    ],
  },
  {
    type: 'upload_img_upload',
    label: L('品牌LOGO'),
    name: 'image',
    extra: L('建议上传宽153*高54的图片'),
    initialValue: [],
    upload_name: 'file',
    upload_url: '/v3/oss/seller/upload?source=setting',
    limit: 10,
    accept: '.jpg, .jpeg, .png',
    rules: [
      {
        required: true,
        message: L('请上传品牌LOGO'),
      },
    ],
  },
  {
    type: 'cascader_common_load',
    label: L('品牌分类'),
    name: 'category',
    placeholder: L('请选择要绑定的商品分类'),
    extra: L('必须选到第三级分类'),
    loadData: true,
    maxLength: 200,
    fieldNames: {
      label: 'categoryName',
      value: 'categoryId',
      children: 'children',
    }, //自定义 options 中 label name 
    data: [],
    rules: [{
      required: true,
      message: L('请选择要绑定的商品分类'),//请选择要绑定的商品分类
    }],
  },
  {
    type: 'textarea',
    label: L('品牌描述'),
    name: 'brandDesc',
    placeholder: L('请输入品牌描述'),
    extra: L('最多输入200个字'),
    maxLength: 200,
  },
]);

//获取商户绑定的商品分类
const getGoodsCategroy = async (id, grade = 0) => {
  let res = await getVendorGoodsCategoryByIdApi({ categoryId: id, grade: grade })
  if (res.state == 200) {
    res.data.map(item => {
      if (item.children == null) {
        item.isLeaf = true
      } else {
        item.isLeaf = false
      }
      delete item.children;
    })
    for (let i in addData.value) {
      if (addData.value[i].name == 'category') {
        if (grade == 1) {
          addData.value[i].data = res.data;
        } else if (grade == 2) {
          let tmpData = addData.value[i].data.filter(item => item.categoryId == id)[0];
          tmpData.children = res.data;
        } else if (grade == 3) {
          let tmpData = addData.value[i].data.filter(item => item.categoryId == id)[0];
          tmpData.children = res.data;
        }
        break;
      }
    }
  }
}

const loadData = async (selectedOptions, curFormData) => {
  content.value.forEach(item => {
    for (let i in curFormData) {
      if (i == item.name) {
        item.initialValue = curFormData[i]
      }
    }
  })
  const targetOption = selectedOptions[selectedOptions.length - 1];
  let target_grade = selectedOptions[selectedOptions.length - 1].grade * 1 + 1;
  targetOption.loading = true;
  let res = await getVendorGoodsCategoryByIdApi({ categoryId: targetOption.categoryId, grade: target_grade })
  if (res.state == 200) {
    if (res.data.length > 0) {
      res.data.map(item => {
        if (item.children == null) {
          item.isLeaf = true
        } else {
          item.isLeaf = false
        }
        delete item.children;
      })
      targetOption.children = res.data
    } else {
      targetOption.isLeaf = true
    }
    targetOption.loading = false;
  }
}

// 弹窗确认事件
const handleConfirm = async (val) => {
  let _this = this;
  let params = {}
  params.brandName = val.brandName;
  params.brandDesc = val.brandDesc == undefined ? '' : val.brandDesc;
  if (val.category != undefined && val.category.length < 3) {
    failTip(L('分类必须选到三级才可以'))
    return
  }
  params.categoryId = val.category[2]
  params.image = val.image[0].response.data.path;
  confirmBtnLoading.value = true
  let res;
  if (operate_type.value == 'add') {
    res = await getGoodsBrandApplyApi(params)
  } else {
    params.brandId = operate_id.value;
    res = await getBrandEditApi(params)
  }
  confirmBtnLoading.value = false
  click_event.value = false;
  if (res.state == 200) {
    sucTip(res.msg)
    reload()
    handleCancle()
  } else {
    failTip(res.msg)
  }
}

//弹窗取消事件
function handleCancle () {
  visible.value = false;
  content.value = [];
  operate_id.value = '';
}

// 更新表格高度
const handleToggleTip = () => {
  redoHeight();
};

//表格点击回调事件
function handleClick (item, type) {
  if (click_event.value) return;
  operate_type.value = type;
  if (item && item.brandId) {
    operate_id.value = item.brandId;
  }
  let params = {};
  if (type == 'add') {
    content.value = JSON.parse(JSON.stringify(addData.value));
    width.value = 550;
    title.value = L('新增品牌');
    visible.value = true;
  } else if (type == 'edit') {
    editBrand(item, type)
  } else if (type == 'del') {
    params.brandId = item.brandId;
    click_event.value = true;
    operate_role(params);
  }
}

// 编辑品牌
const editBrand = async (val, type) => {
  let res = await getBrandDetailApi({ brandId: val.brandId })
  if (res.state == 200) {
    let data = JSON.parse(JSON.stringify(addData.value));
    data.forEach((item) => {
      if (item.name == 'image') {
        //初始化图片数据
        let fileList = [];
        let tmp_data = {};
        tmp_data.uid = res.data.brandId;
        tmp_data.name = res.data.imageUrl;
        tmp_data.status = 'done';
        tmp_data.url = res.data.imageUrl;
        tmp_data.response = {
          data: {
            url: res.data.imageUrl,
            path: res.data.image,
          },
        };
        fileList.push(tmp_data);
        item.initialValue = fileList;
      } else if (item.name == 'category') {
        let tar_data = res.data.brandAndCateVOList.filter(item => item.categoryId == res.data.goodsCateId1)[0];
        if (tar_data) {
          tar_data.children.map(items => {
            items.isLeaf = false;
            items.grade = 2;
          })
          let car_2 = tar_data.children.filter(item => item.categoryId == res.data.goodsCateId2)[0];
          car_2.isLeaf = false;
          let car_3 = car_2.children.filter(item => item.categoryId == res.data.goodsCateId3)[0];
          car_3.isLeaf = true;
          item.initialValue = [res.data.goodsCateId1, res.data.goodsCateId2, res.data.goodsCateId3];
          res.data.brandAndCateVOList.map(item => {
            item.isLeaf = false;
            item.grade = 1;
          });
          item.data = res.data.brandAndCateVOList;
        };
      } else {
        item.initialValue = res.data[item.name] ? res.data[item.name] : undefined;
      }
    });
    content.value = data;
    width.value = 550;
    title.value = L('编辑品牌');
    visible.value = true;
  } else {
    failTip(res.msg)
  }
}

// 删除操作
const operate_role = async (params) => {
  let res = await delBrandApi(params)
  click_event.value = false;
  if (res.state == 200) {
    sucTip(res.msg)
    reload()
  } else {
    failTip(res.msg)
  }
}




onMounted(() => {
  getGoodsCategroy(0, 1)
});
</script>
<style lang="less">
.point_goods_list {
  .tabs_nav {
    .ant-tabs-nav {
      margin-bottom: 0;
    }
  }
}
</style>
