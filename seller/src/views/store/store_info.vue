<template>
  <div class="common_page_toolbar common_description store_info" v-if="detail.storeId">
    <Description size="middle" title="店铺信息" :bordered="true" :column="2" :data="detail"
      :schema="detail.enterType == 1 ? companySchema : storeSchema" />
    <Description v-if="detail.enterType == 1" size="middle" title="营业执照信息" :bordered="true" :column="2"
      :data="detail.businessImg" :schema="businessImgSchema" />
    <Description size="middle" :title="detail.enterType == 1 ? '法人身份信息' : '身份证信息'" :bordered="true" :column="1"
      :data="detail.cardImg" :schema="cardImgSchema" />
      <Description
    v-if="detail.qualificationImg && detail.enterType == 1"
    size="middle" 
    title="补充认证信息"
    :bordered="true"
    :column="1"
    :data="detail.qualificationImg"
    :schema="qualificationImgSchema"
  />
    <Description size="middle" title="店铺经营信息" :bordered="true" :column="2" :data="detail" :schema="extraSchema" />
    <div class="goods_detail_title">经营类目</div>
    <BasicTable @register="standardTable">
      <template #headerCell="{ column }">
        <template v-if="column.key == 'scaling'">
          <div class="table_header">
            <span class="table_header_xing">*</span>
            <span>{{ column.customTitle }}</span>
            <Tooltip placement="bottom" title="商品价格须低于市场价">
              <AliSvgIcon iconName="iconwen" width="14px" height="14px" fillColor="#bfbbba" />
            </Tooltip>
          </div>
        </template>
        <template v-else>
          {{ column.customTitle }}
        </template>
      </template>
    </BasicTable>

    <div class="fixed-bottom-button" :style="buttonStyle">
      <div class="button-container">
        <Tooltip placement="top" title="保存当前店铺信息设置">
          <a-button type="primary" @click="handleSave">保存当前信息</a-button>
        </Tooltip>
        <span v-if="saveTimeText" class="save-time-text">{{ saveTimeText }}</span>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { Tooltip, Input, Cascader } from 'ant-design-vue';
import { defineComponent, getCurrentInstance, h, onMounted, ref, computed, unref, watch } from 'vue';
import { getStoreDetail } from '/@/api/manage/manage';
import { saveSetting } from '/@/api/sysset/sysset';
import { DescItem, Description } from '/@/components/Description/index';
import StandardTableRow from '/@/components/StandardTableRow/index.vue';
import { BasicTable, useTable } from '/@/components/Table';
import { failTip, sucTip } from '/@/utils/utils';
import ImagePreview from '/@/components/ImagePreview/index.vue';
import { updateCertification } from '/@/api/manage/manage';
import areaData from '@/assets/json/area.json';
import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';

export default defineComponent({
  components: {
    Tooltip,
    Description,
    StandardTableRow,
    BasicTable,
    ImagePreview,
  },
  setup() {
    const vm = getCurrentInstance();
    const L = vm?.appContext.config.globalProperties.$sldComLanguage;

    const detail: any = ref({});
    const storeBasicInfo = ref<any[]>([]);
    const companySchema: DescItem[] = [
      {
        field: 'enterTypeValue',
        label: '入驻类型：',
        render: (val) => val || '--'
      },
      {
        field: 'companyName',
        label: '公司名称：',
        render: (val, data) => {
          return h(Input, {
            value: val,
            placeholder: '请输入公司名称',
            style: { width: '300px' },
            onChange: (e: any) => {
              data.companyName = e.target.value;
            }
          });
        }
      },
      {
        field: 'areaInfo',
        label: '所在地：',
        render: (val, data) => {
          const areaValue = [
            data.companyProvinceCode,
            data.companyCityCode,
            data.companyAreaCode
          ].filter(Boolean);

          return h(Cascader, {
            options: areaData,
            value: areaValue,
            style: { width: '300px' },
            fieldNames: {
              label: 'regionName',
              value: 'regionCode',
              children: 'children'
            },
            onChange: (values: string[]) => {
              if (values.length === 3) {
                data.companyProvinceCode = values[0];
                data.companyCityCode = values[1];
                data.companyAreaCode = values[2];
                data.areaInfo = values.join(',');
              }
            },
            placeholder: '请选择所在地'
          });
        }
      },
      {
        field: 'companyAddress',
        label: '详细地址：',
        render: (val, data) => {
          return h(Input.TextArea, {
            value: val,
            placeholder: '请输入详细地址',
            style: { width: '300px' },
            maxLength: 40,
            autoSize: { minRows: 1, maxRows: 4 },
            onChange: (e: any) => {
              data.companyAddress = e.target.value;
            }
          });
        }
      },
      {
        field: 'contactName',
        label: '联系人：',
        render: (val, data) => {
          return h(Input, {
            value: val,
            placeholder: '请输入联系人姓名',
            style: { width: '300px' },
            onChange: (e: any) => {
              data.contactName = e.target.value;
            }
          });
        }
      },
      {
        field: 'contactPhone',
        label: '联系人手机号：',
        render: (val, data) => {
          return h(Input, {
            value: val,
            placeholder: '请输入联系人手机号',
            style: { width: '300px' },
            maxLength: 11,
            onChange: (e: any) => {
              data.contactPhone = e.target.value;
            },
            onBlur: (e: any) => {
              const phoneReg = /^1[3-9]\d{9}$/;
              if (!phoneReg.test(e.target.value) && e.target.value) {
                failTip('请输入正确的手机号码');
              }
            }
          });
        }
      }
    ];
    const storeSchema: DescItem[] = [
      {
        field: 'enterTypeValue',
        label: '入驻类型：',
        render: function (val) {
          return val ? val : '--';
        },
      },

      {
        field: 'areaInfo',
        label: '所在地：',
        render: function (val) {
          return val ? val : '--';
        },
      },
      {
        field: 'companyAddress',
        label: '详细地址：',
        render: function (val) {
          return val ? val : '--';
        },
      },
      {
        field: 'contactName',
        label: '联系人：',
        render: function (val) {
          return val ? val : '--';
        },
      },
      {
        field: 'contactPhone',
        label: '联系人手机号：',
        render: function (val) {
          return val ? val : '--';
        },
      },
    ];
    const businessImgSchema: DescItem[] = [
      {
        field: 'businessLicenseImagePath',
        label: '营业执照：',
        render: (val, data) => {
          return h(ImagePreview, {
            imageList: data.businessLicenseImagePath,
            onDelete: (index: number) => {
              data.businessLicenseImagePath.splice(index, 1);
            },
            'onUpload-success': (newImage: any) => {
              data.businessLicenseImagePath.push(newImage);
            }
          });
        },
      },
    ];
    const qualificationImgSchema = ref<DescItem[]>([]);
    const cardImgSchema: DescItem[] = [
      {
        field: 'personCardUpPath',
        label: '身份证正面：',
        render: (val, data) => {
          return h(ImagePreview, {
            imageList: data.personCardUpPath,
            onDelete: (index: number) => {
              data.personCardUpPath.splice(index, 1);
            },
            'onUpload-success': (newImage: any) => {
              data.personCardUpPath.push(newImage);
            }
          });
        },
      },
      {
        field: 'personCardDownPath',
        label: '身份证反面：',
        render: (val, data) => {
          return h(ImagePreview, {
            imageList: data.personCardDownPath,
            onDelete: (index: number) => {
              data.personCardDownPath.splice(index, 1);
            },
            'onUpload-success': (newImage: any) => {
              data.personCardDownPath.push(newImage);
            }
          });
        },
      },
    ];
    const extraSchema: DescItem[] = [
      {
        field: 'storeName',
        label: '店铺名称：',
        render: function (val) {
          return val ? val : '--';
        },
      },
      {
        field: 'storeGradeName',
        label: '店铺等级：',
        render: function (val) {
          return val ? val : '--';
        },
      },
      {
        field: 'openTime',
        label: '开店时长：',
        render: function (val) {
          return (val !== undefined && val !== null) ? val + '年' : 0;
        },
      },
      {
        field: 'payAmount',
        label: '已付年费：',
        render: function (val) {
          return (val !== undefined && val !== null) ? val + '元' : 0;
        },
      },
      {
        field: 'paymentName',
        label: '付款方式：',
        render: function (val) {
          return val ? val : '--';
        },
      },
      {
        field: 'billCycle',
        label: '结算日：',
        render: function (val) {
          return val ? val : '系统默认';
        },
      },
    ];
    const dataSource = ref([]);
    const [standardTable] = useTable({
      columns: [
        {
          title: '一级类目',
          dataIndex: 'goodsCateName1',
          width: 100,
        },
        {
          title: '二级类目',
          dataIndex: 'goodsCateName2',
          width: 100,
        },
        {
          title: '三级类目',
          dataIndex: 'goodsCateName3',
          width: 100,
        },
        {
          title: '佣金比例',
          dataIndex: 'scaling',
          width: 100,
        },
      ],
      dataSource: dataSource,
      showIndexColumn: true,
      pagination: false,
      bordered: true,
      striped: false,
      canResize: false,
    });

    const { getCollapsed } = useMenuSetting();

    const buttonStyle = computed(() => {
      return {
        left: unref(getCollapsed) ? '48px' : '150px'
      }
    });

    const lastSaveTime = ref(null);
    const autoSaveTimer = ref(null);

    // 格式化时间差
    const formatTimeAgo = () => {
      return '1秒前';
    };

    const saveTimeText = computed(() => {
      if (!lastSaveTime.value) return '';
      return `${formatTimeAgo()}自动保存`;
    });

    const autoSave = async () => {
      try {
        await handleSave();
        lastSaveTime.value = new Date();
      } catch (error) {
        console.error('自动保存失败:', error);
      }
    };

    watch(
      () => detail.value,
      () => {
        if (autoSaveTimer.value) {
          clearTimeout(autoSaveTimer.value);
        }
        autoSaveTimer.value = setTimeout(() => {
          autoSave();
        }, 1000); // 1秒后自动保存
      },
      { deep: true, immediate: false }
    );

    const validatePhone = (phone) => {
      const phoneReg = /^1[3-9]\d{9}$/;
      return phoneReg.test(phone);
    };

    const handleSave = async () => {
      try {
        // 在保存前验证手机号
        if (detail.value.contactPhone && !validatePhone(detail.value.contactPhone)) {
          failTip('请输入正确的手机号码');
          return;
        }
        await updateCertification(detail.value);
        sucTip('保存成功');
        lastSaveTime.value = new Date();
      } catch (error) {
        failTip('保存失败');
      }
    };

    onMounted(() => {
      get_detail();
    });

    const submitEvent = async (data: any) => {
      console.log('[submitEvent]', data)
      const res = await saveSetting(data)
      if (res.state == 200) {
        sucTip(res.msg);
      } else {
        failTip(res.msg);
      }
    }

    //获取店铺详情
    const get_detail = async () => {
      const res: any = await getStoreDetail();
      if (res.state == 200 && res.data) {
        const storeData = {
          ...res.data,
          areaInfo: [
            res.data.companyProvinceCode,
            res.data.companyCityCode,
            res.data.companyAreaCode
          ].filter(Boolean).join(',')
        };

        if (res.data.enterType == 1) {
          storeData.businessImg = {
            businessLicenseImagePath: [{
              image: res.data.businessLicenseImage,
              imageUrl: res.data.businessLicenseImagePath,
            }]
          };
          storeData.qualificationImg = {
            moreQualification1Path: res.data.moreQualification1Path ? [{
              image: res.data.moreQualification1,
              imageUrl: res.data.moreQualification1Path,
            }] : [],
            moreQualification2Path: res.data.moreQualification2Path ? [{
              image: res.data.moreQualification2, 
              imageUrl: res.data.moreQualification2Path,
            }] : [],
            moreQualification3Path: res.data.moreQualification3Path ? [{
              image: res.data.moreQualification3,
              imageUrl: res.data.moreQualification3Path, 
            }] : []
          };

          qualificationImgSchema.value = [
            {
              field: 'moreQualification1Path',
              label: '补充认证信息一：',
              render: (val, data) => {
                return h(ImagePreview, {
                  imageList: data.moreQualification1Path,
                  onDelete: (index: number) => {
                    data.moreQualification1Path.splice(index, 1);
                  },
                  'onUpload-success': (newImage: any) => {
                    data.moreQualification1Path.push(newImage);
                  }
                });
              },
            },
            {
              field: 'moreQualification2Path', 
              label: '补充认证信息二：',
              render: (val, data) => {
                return h(ImagePreview, {
                  imageList: data.moreQualification2Path,
                  onDelete: (index: number) => {
                    data.moreQualification2Path.splice(index, 1);
                  },
                  'onUpload-success': (newImage: any) => {
                    data.moreQualification2Path.push(newImage);
                  }
                });
              },
            },
            {
              field: 'moreQualification3Path',
              label: '补充认证信息三：', 
              render: (val, data) => {
                return h(ImagePreview, {
                  imageList: data.moreQualification3Path,
                  onDelete: (index: number) => {
                    data.moreQualification3Path.splice(index, 1);
                  },
                  'onUpload-success': (newImage: any) => {
                    data.moreQualification3Path.push(newImage);
                  }
                });
              },
            }
          ];
        }
        storeData.cardImg = {
          personCardUpPath: [{
            image: res.data.personCardUp,
            imageUrl: res.data.personCardUpPath,
          }],
          personCardDownPath: [{
            image: res.data.personCardDown,
            imageUrl: res.data.personCardDownPath,
          }],
        };
        detail.value = storeData;
        dataSource.value = res.data.storeGoodsCateVOList ? res.data.storeGoodsCateVOList : [];

        const storeName = res.data?.storeName;
        console.log('storeName:', storeName)
        let formData = [{
          type: 'input',
          key: 'storeName',
          label: L('店铺名'),
          placeholder:L(storeName),
          width: 300,
          value: storeName,
          rules: [{ min: 4, max: 50, message: L('长度 4-50 字符') }],
        }]
        formData.push({
          type: 'button',
          width: 300,
          showSubmit: true,
          submitText: L('保存'),
          showCancle: false,
        })
        storeBasicInfo.value=formData;

      } else {
        failTip(res.msg);
      }

    };

    return {
      submitEvent,
      storeBasicInfo,
      detail,
      companySchema,
      storeSchema,
      businessImgSchema,
      qualificationImgSchema,
      cardImgSchema,
      extraSchema,
      standardTable,
      handleSave,
      buttonStyle,
      saveTimeText,
    };
  },
});
</script>
<style lang="less" scoped>
.store_info {
  overflow: auto;
  height: calc(100vh - @header-height - 140px);
  padding-bottom: 60px;

  .slodon-basic-table {
    height: auto !important;
  }

  .ant-spin-container {
    .ant-table-container {
      .ant-table-body {
        height: auto !important;
      }
    }
  }

  .goods_detail_title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 32px;
    margin-bottom: 10px;
    padding-left: 14px;
    color: rgb(0 0 0 / 85%);
    font-size: 14px;
    font-weight: 500;
    line-height: 24px;
    cursor: default;
  }

  .table_header {
    .table_header_xing {
      margin-right: 1px;
      color: #ff2929;
      font-size: 13px;
    }

    svg {
      position: relative;
      top: 2px;
      margin-left: 2px;
    }
  }

  .ant-descriptions-item-label {
    white-space: nowrap;
  }

  :deep(.ant-descriptions-bordered.ant-descriptions-middle) {
    .ant-descriptions-item-label,
    .ant-descriptions-item-content {
      padding: 8px 12px;
    }
    
    .ant-descriptions-item-label {
      white-space: nowrap;
      min-width: 120px;
    }

    .ant-descriptions-item-content {
      width: 100%;
    }
  }
}

.fixed-bottom-button {
  position: fixed;
  bottom: 0;
  right: 0;
  width: calc(100% - 150px);
  padding: 10px 24px;
  text-align: left;
  background: white;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.15);
  z-index: 100;

  .button-container {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .save-time-text {
    color: #999;
    font-size: 14px;
  }

  .ant-btn-primary {
    min-width: 88px;
    height: 32px;
    border-radius: 4px;
    background: #ff7e28;
    border-color: #ff7e28;

    &:hover {
      background: #ff9d52;
      border-color: #ff9d52;
    }

    &:active {
      background: #d95f18;
      border-color: #d95f18;
    }
  }
}
</style>