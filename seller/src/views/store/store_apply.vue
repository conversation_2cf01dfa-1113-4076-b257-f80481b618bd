<template>
    <div>
      <ShowMoreHelpTip v-if="helpTip[0]" :tipData="helpTip"></ShowMoreHelpTip>
      <div style="width: 100%;height: 10px;"></div>
      <BasicTable @register="standardTable">
        <template #tableTitle>
          <div class="toolbar flex flex-row">
            <div class="toolbar_btn" @click="handleClick(null, 'apply')">
              <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#fff" />
              <span>{{ L('申请续签') }}</span>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.key == 'action'">
            <div v-if="record.state == 1" class="flex flex-row items-center justify-center" style="flex-wrap: wrap">
              <span
                @click="handleClick(record, 'pay')"
                class="common_page_edit cursor-pointer hover:text-[#FF6A12]">{{ L('付款') }}</span>
              <Popconfirm
                title="删除后不可恢复，是否确定删除？"
                @confirm="handleClick(record, 'del')"
              >
                <span class="common_page_edit cursor-pointer hover:text-[#FF6A12]">{{ L('删除') }}</span>
              </Popconfirm>
            </div>
            <span v-else>--</span>
          </template>
          <template v-else-if="column.key == 'startTime'">
            <div>{{ text }}</div>
            <div>~</div>
            <div>{{ record.endTime }}</div>
          </template>
          <template v-else-if="column.key == 'paymentName'">
            {{ text ? text : '--' }}
          </template>
        </template>
      </BasicTable>
      <Modal
        destroyOnClose
        :maskClosable="false"
        :width="width"
        :title="title"
        :visible="visible"
        :confirmLoading="confirmBtnLoading"
        @cancel="handleCancle"
        @ok="handleConfirm"
        :ok-text="operate_type == 'wx_pay' ? '已支付' : '去付款'"
      >
        <div class="store_apply_modal">
            <template v-if="operate_type == 'apply' || operate_type == 'pay'">
                <template v-if="operate_type == 'apply'">
                    <div class="item flex_row_start_center">
                        <div class="left"><span style="color: red;">*</span>店铺等级</div>
                        <div class="right">
                            <Table
                                rowKey="gradeId"
                                size='small'
                                :maxHeight="400"
                                :pagination="false"
                                :columns="modal_columns"
                                :dataSource="gradeList"
                                :bordered="true"
                                :canResize="false"
                                :rowSelection="{
                                    type: 'radio',
                                    onChange: (rowKey, row) => handleChange({rowKey, row}, 'grade'),
                                }"
                            >
                                <template #bodyCell="{ column, text }">
                                    <template v-if="column.dataIndex">
                                        {{ (text !== undefined && text !== null) ? text : '--' }}
                                    </template>
                                </template>
                            </Table>
                        </div>
                    </div>
                    <div class="item flex_row_start_center">
                        <div class="left">应付金额</div>
                        <div class="right">{{ modaldata.payAmount !== undefined && modaldata.payAmount !== null
                            && modaldata.payAmount !== '' ? modaldata.payAmount : '--' }}</div>
                    </div>
                    <div class="item flex_row_start_center">
                        <div class="left"><span style="color: red;">*</span>开店时长</div>
                        <div class="right">
                            <Select
                                :placeholder="L('请选择开店时长')"
                                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                                v-model:value="modaldata.duration"
                                @change="(e) => handleChange(e, 'duration')"
                                style="width: 220px;"
                            >
                                <Select.Option
                                v-for="(item, index) in openTime"
                                :key="index"
                                :value="item.value"
                                >{{ item.name }}</Select.Option
                                >
                            </Select>
                        </div>
                    </div>
                </template>
                <div class="item flex_row_start_center">
                    <div class="left">支付方式</div>
                    <div class="right">
                        <RadioGroup
                            size="small"
                            v-model:value="modaldata.payMethod"
                            @change="(e) => handleChange(e.target.value, 'payMethod')"
                        >
                            <Radio v-for="(item,index) in payMethod" :key="index" :value="item.value" > {{ item.label }} </Radio>
                        </RadioGroup>
                    </div>
                </div>
            </template>
            <template v-else-if="operate_type == 'wx_pay'">
                <div class="flex_column_center_center wx_pay_qrcode">
                    <img :src="wx_pay_qrcode" />
                    <p class="pay_tip">使用<span style="color: #228AFF">微信APP</span>扫码支付</p>
                    <a class="wx_refresh" @click="refreshWxQrcode">刷新</a>
                </div>
            </template>
        </div>
      </Modal>
    </div>
  </template>
  
  <script setup>
    import { getCurrentInstance, onMounted, ref } from 'vue';
    import { Popconfirm, Modal, Table, Select, RadioGroup, Radio } from 'ant-design-vue';
    import { BasicTable, useTable } from '/@/components/Table';
    import SldComHeader from '/@/components/SldComHeader/index.vue';
    import ShowMoreHelpTip from '/@/components/ShowMoreHelpTip/index.vue';
    import {
      getStoreExpiretime,
      getStoreRenewList,
      delStoreRenew,
      doStoreRenew,
      payStoreRenew,
      getStoreRenewDetail,
    } from '@/api/decorate/deco';
    import { getStoreGrade } from '@/api/manage/manage';
    import { getStoreOpenTimeApi } from '@/api/settled/settled';
    import { getSettingListApi } from '@/api/common/common';
    import { sucTip, failTip } from '@/utils/utils';
  
    const vm = getCurrentInstance();
    const L = vm?.appContext.config.globalProperties.$sldComLanguage;
    const helpTip = ref([]);
    const columns = [
        { title: L('店铺等级'), dataIndex: 'gradeName', width: 100 },
        { title: L('收费标准(元/年)'), dataIndex: 'price', width: 100 },
        { title: L('续签时长(年)'), dataIndex: 'duration', width: 100 },
        { title: L('付款金额(元)'), dataIndex: 'payAmount', width: 100 },
        { title: L('续签起止有效期'), dataIndex: 'startTime', width: 150 },
        { title: L('状态'), dataIndex: 'stateValue', width: 100 },
        { title: L('付款方式'), dataIndex: 'paymentName', width: 100 },
    ];
    const [standardTable, { reload, setLoading }] = useTable({
      api: getStoreRenewList,
      columns: columns,
      fetchSetting: {
        pageField: 'current',
        sizeField: 'pageSize',
        listField: 'data.list',
        totalField: 'data.pagination.total',
      },
      actionColumn: {
        width: 100,
        title: L('操作'),
        dataIndex: 'action',
        align: 'center',
      },
      pagination: {
        pageSize: 10,
      },
      bordered: true,
      striped: false,
    });
    const modal_columns = [
        { title: L('店铺等级'), dataIndex: 'gradeName', align: 'center', width: 100 },
        { title: L('可发布商品)'), dataIndex: 'goodsLimit', align: 'center', width: 100 },
        { title: L('可推荐商品'), dataIndex: 'recommendLimit', align: 'center', width: 100 },
        { title: L('收费标准'), dataIndex: 'price', align: 'center', width: 100 },
        { title: L('申请说明'), dataIndex: 'description', align: 'center', width: 140 },
    ];
    const gradeList = ref([]); //店铺等级列表数据
    const openTime = ref([]); //开店时长数据
    const payMethod = ref([]); //支付方式
    const width = ref(500);
    const title = ref('');
    const visible = ref(false);
    const confirmBtnLoading = ref(false);
    const operate_type = ref(''); //当前操作类型
    const operate_item = ref({}); //当前操作数据
    const modaldata = ref({
        grade: null,
        gradePrice: null,
        payAmount: null,
        duration: null,
        payMethod: '',
    });
    const wx_pay_qrcode = ref(''); //微信支付二维码
    const timer = ref(''); //定时事件

    const handleClick = (record, type) => {
      operate_type.value = type;
      operate_item.value = record;
      if (type == 'apply') {
        width.value = 800;
        title.value = '申请续签';
        visible.value = true;
      } else if (type == 'pay') {
        width.value = 400;
        title.value = '续签付款';
        visible.value = true;
      } else if (type == 'del') {
        setLoading(true);
        delStoreRenew({ renewId: record.renewId }).then((res) => {
          setLoading(false);
          if (res.state == 200) {
            reload();
            sucTip(res.msg);
          } else {
            failTip(res.msg);
          }
        });
      }
    };

    const handleCancle = () => {
        if (timer.value) {
            clearInterval(timer.value);
            timer.value = '';
        }
        visible.value = false;
        operate_type.value = '';
        operate_item.value = {};
        modaldata.value = {
            grade: null,
            gradePrice: null,
            payAmount: null,
            duration: null,
            payMethod: '',
        };
        if(wx_pay_qrcode.value){
          reload()
          wx_pay_qrcode.value = '';
        }
    };

    const handleConfirm = async (flag = false) => {
        if (operate_type.value == 'apply') {
            if (!modaldata.value.grade || modaldata.value.grade.length == 0) {
                failTip('请选择店铺等级');
                return;
            } else if (!modaldata.value.duration) {
                failTip('请选择开店时长');
                return;
            }
        }
        if (!modaldata.value.payMethod) {
            failTip('请选择支付方式');
            return;
        }
        if (!flag) {
            confirmBtnLoading.value = true;
        }
        let res = null;
        if (operate_type.value == 'apply') {
            res = await doStoreRenew({
                gradeId: modaldata.value.grade[0],
                duration: modaldata.value.duration,
            });
        } else {
            res = await payStoreRenew({
                payMethod: modaldata.value.payMethod,
                paySn: operate_item.value.paySn,
                payType:'NATIVE',
            });
        }
        if (res?.state == 200) {
            if (operate_type.value == 'apply' || operate_type.value == 'pay'||operate_type.value == 'wx_pay') {
                confirmBtnLoading.value = false;
                if (!res.data || !res.data.payData) {
                  if (operate_type.value == 'apply') {
                    if (!operate_item.value) {
                      operate_item.value = {};
                    }
                    operate_item.value.renewId = res.data.renewId;
                    operate_item.value.paySn = res.data.paySn;
                    const response = await payStoreRenew({
                        payMethod: modaldata.value.payMethod,
                        paySn: res.data.paySn,
                        payType:'NATIVE',
                    });
                    if (!response.data || !response.data.payData) {
                      handleCancle();
                      reload();
                    } else if (modaldata.value.payMethod == 'alipay') {
                      document.write(response.data.payData); //自动提交表单数据
                    } else if (modaldata.value.payMethod == 'wx') {
                      operate_type.value = 'wx_pay';
                      width.value = '400px';
                      title.value = '微信支付';
                      wx_pay_qrcode.value = 'data:image/png;base64,' + response.data.payData; //微信支付二维码
                      visible.value = true;
                      // 定时查询是否支付成功
                      timer.value = setInterval(() => {
                          get_renew_detail();
                      }, 3000);
                    }
                  } else {
                    handleCancle();
                    reload();
                  }
                } else if (modaldata.value.payMethod == 'alipay') {
                    document.write(res.data.payData); //自动提交表单数据
                } else if (modaldata.value.payMethod == 'wx') {
                    operate_type.value = 'wx_pay';
                    width.value = '400px';
                    title.value = '微信支付';
                    wx_pay_qrcode.value = 'data:image/png;base64,' + res.data.payData; //微信支付二维码
                    visible.value = true;
                    // 定时查询是否支付成功
                    timer.value = setInterval(() => {
                        get_renew_detail();
                    }, 3000);
                }
            }
        } else {
            confirmBtnLoading.value = false;
            failTip(res.msg);
        }
    };

    //获取店铺续约列表信息
    const get_store_expiretime = async () => {
      const res = await getStoreExpiretime();
      if (res.state == 200 && res.data.storeExpireTime) {
        helpTip.value = ['您的店铺已签约至' + res.data.storeExpireTime];
      }
    };

    //获取店铺等级
    const get_grade_list = async () => {
      const res = await getStoreGrade({ pageSize: 10000 });
      if (res.state == 200) {
        gradeList.value = res.data.list;
      }
    };

    //获取开店时长
    const get_category_list = async () => {
      const res = await getStoreOpenTimeApi();
      if (res.state == 200) {
        let data = [];
        res.data.map(item => {
            data.push({
                value: item,
                name: item + '年'
            })
        })
        openTime.value = data;
      }
    };

    //获取支付方式
    const get_pay_method = async () => {
      const res = await getSettingListApi({ str: 'alipay_is_enable_pc,wxpay_is_enable_pc' });
      if (res.state == 200) {
        let data = [];
        let key = '';
        res.data.map(item => {
            if (item.name == 'alipay_is_enable_pc' && item.value == 1) {
                data.push({
                    label: '支付宝',
                    value: 'alipay',
                })
                key = key ? key : 'alipay';
            } else if (item.name == 'wxpay_is_enable_pc' && item.value == 1) {
                data.push({
                    label: '微信',
                    value: 'wx',
                })
                key = key ? key : 'wx';
            }
        })
        payMethod.value = data;
        modaldata.value.payMethod = key;
      }
    };

    const handleChange = (val, key) => {
        if (key == 'grade') {
            modaldata.value[key] = val.rowKey;
            modaldata.value.gradePrice = val.row[0].price;
        } else {
            modaldata.value[key] = val;
        }
        if ((modaldata.value.gradePrice !== null && modaldata.value.gradePrice !== undefined)
         && (modaldata.value.duration !== null && modaldata.value.duration !== undefined)) {
            modaldata.value.payAmount = (Number(modaldata.value.gradePrice * modaldata.value.duration).toFixed(2) || 0) + '元';
        } else if (modaldata.value.payAmount) {
            modaldata.value.payAmount = '--'
        }
    };

    //获取支付进度
    const get_renew_detail = async () => {
        const res = await getStoreRenewDetail({ renewId: operate_item.value.renewId });
        if (res.state == 200) {
            if (res.data.state == 2) {
                if (timer.value) {
                    clearInterval(timer.value);
                    timer.value = '';
                }
                handleCancle();
                reload();
            }
        }
    };

    //刷新微信支付二维码
    const refreshWxQrcode = () => {
        if (timer.value) {
            clearInterval(timer.value);
            timer.value = '';
        }
        handleConfirm(true);
    };
 
    onMounted(() => {
        get_store_expiretime();
        get_grade_list();
        get_category_list();
        get_pay_method();
    })
  </script>
  
  <style lang="less" scoped>
    .common_header_tip {
        margin-top: 0;
    }
    .store_apply_modal {
        padding: 15px 0;
        .item {
            min-height: 56px;
            .left {
                width: 120px;
                text-align: right;
                margin-right: 30px;;
            }
            .right {

            }
        }
        .wx_pay_qrcode{
            img{
                width: 170px;
                height: 170px;
                margin-top: 25px;
                margin-bottom: 20px;
                border: 1px solid #EEE;
                box-shadow: 0 0 20px 0 rgba(153, 153, 153, 0.2);
                border-radius: 3px;
            }
            .pay_tip{
                color: #999;
                font-size: 14px;
            }
            .wx_refresh{
                display: inline-block;
                width: 97px;
                height: 25px;
                line-height: 25px;
                background: #FF4C4C;
                border-radius: 3px;
                color: #fff;
                font-size: 12px;
                margin-top: 10px;
                outline: none;
                text-align: center;
                margin-bottom: 25px;
            }
        }
    }
  </style>
  