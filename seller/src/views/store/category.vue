<template>
  <div class="section_padding point_label_body">
    <div class="section_padding_back">
      <SldComHeader :title="L('店铺分类管理')" />
      <BasicTable @register="standardTable" style="padding: 0">
        <template #tableTitle>
          <div class="toolbar">
            <div class="toolbar_btn" @click="addLabel">
              <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#ff5908" />
              <span>{{ L('新增分类') }}</span>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex == 'isShow'">
            <div>
              <Switch
                @change="
                  (checked) =>
                    handleClick(
                      {
                        innerLabelId: record.innerLabelId,
                        isShow: checked ? 1 : 0,
                      },
                      'show',
                    )
                "
                :checked="text == 1 ? true : false"
              />
            </div>
          </template>
          <template v-if="column.key == 'action'">
            <TableAction
              class="TableAction"
              :actions="[
                {
                  label: L('编辑'),
                  onClick: editLabel.bind(null, record),
                },
                {
                  label: L('添加下级分类'),
                  ifShow: record.parentInnerLabelId == 0 ,
                  onClick: addNextLabel.bind(null, record),
                },
                {
                  label: L('删除'),
                  ifShow: (record.children == undefined || (record.children != undefined && (record.children == null || record.children.length == 0))),
                  popConfirm: {
                    title: L('删除后不可恢复，是否确定删除？'),
                    placement: 'left',
                    confirm: handleClick.bind(
                      null,
                      { innerLabelId: record.innerLabelId, parentInnerLabelId: record.parentInnerLabelId },
                      'del',
                    ),
                  },
                },
              ]"
            />
          </template>
        </template>
      </BasicTable>
    </div>
    <SldModal
      :width="width"
      :title="title"
      :visible="modalVisible"
      :content="addData"
      :confirmBtnLoading="confirmBtnLoading"
      :showFoot="true"
      @tree-select-change-event="handleTreeSelect"
      @cancle-event="handleCancle"
      @confirm-event="handleConfirm"
    />
  </div>
</template>
<script>
  export default {
    name: 'StoreCategory',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, onMounted } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { Switch } from 'ant-design-vue';
  import { sucTip, failTip } from '@/utils/utils';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import {
    getLabelListApi,
    getIsShowApi,
    getAddApi,
    getUpdateApi,
    getLabelDelApi,
  } from '/@/api/store/category';
  import SldModal from '@/components/SldModal/index.vue';
  import { list_com_page_more } from '/@/utils/utils';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const dataSource = ref([]);
  const expandedRowKeys = ref([]);
  const parentInnerLabelId = ref('');
  const operate_item = ref({});
  const [standardTable, { collapseAll, reload }] = useTable({
    isTreeTable: true,
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
    },
    columns: [
      {
        title: L('分类名称'),
        dataIndex: 'innerLabelName',
        width: 150,
      },
      {
        title: L('排序'),
        dataIndex: 'innerLabelSort',
        width: 100,
      },
      {
        title: L('显示状态'),
        dataIndex: 'isShow',
        width: 100,
      },
    ],
    actionColumn: {
      width: 100,
      title: L('操作'),
      dataIndex: 'action',
    },
    dataSource: dataSource,
    bordered: true,
    striped: false,
    rowKey: 'innerLabelId',
    onExpand: (expande, record) => onExpandTable(expande, record),
    pagination: false,
  });
  const click_event = ref(false);
  const width = ref(550);
  const title = ref('');
  const type = ref('');
  const modalVisible = ref(false);
  const addData = ref([]);
  const curData = ref({});
  const content = ref([
    {
      type: 'input',
      label: L('分类名称'),
      name: 'innerLabelName',
      extra: L('最多输入6个字'),
      placeholder: L('请输入分类名称'),
      maxLength: 6, //最多字数
      initialValue: '',
      showCount: false, //是否展示字数
      rules: [
        {
          whitespace: true,
          required: true,
          message: L('请输入分类名称'), //请输入标签名称
        },
      ],
    },
    {
      type: 'inputnum',
      label: L('排序'), //数字输入框
      name: 'innerLabelSort',
      placeholder: L('请输入排序'),
      extra: L('请输入0~255的数字,值越小,显示越靠前'),
      disable: false, //是否禁用
      min: 0, //最小值
      initialValue: '',
      max: 255, //最大值
      rules: [
        {
          required: true,
          message: L('请输入排序'), //请输入标签名称
        },
      ],
    },
    {
      type: 'switch',
      label: L('启用'), //开关
      name: 'isShow',
      initialValue: 1, //默认值
      unCheckedValue: 0, //非选中的值 不传返回值为false
      checkedValue: 1, //选中的值 不传返回值为true
    },
  ]);

  const label_data = ref({
      type: 'select',
      label: L('上级分类'),
      name: 'parentInnerLabelId',
      placeholder: L('请选择上级分类'),
      selData: [],
      selKey:'innerLabelId',
      selName:'innerLabelName',
      diy:true,
  }
  );

  const confirmBtnLoading = ref(false);

  //获取数据列表
  const get_list = async (params = {}) => {
    let res = await getLabelListApi({ ...params, pageSize: list_com_page_more });
    if (res.state == 200) {
      dataSource.value = res.data;
      label_data.value.selData = res.data
    } else {
      failTip(res.msg);
    }
  };

  // 点击展开
  const onExpandTable = (expanded, record) => {
    if (expanded) {
      expandedRowKeys.value.push(record.innerLabelId);
      get_list();
    } else {
      expandedRowKeys.value = expandedRowKeys.value.filter((item) => item != record.innerLabelId);
    }
  };

  //实现深拷贝，防止对象相互影响
  const getNewData = (type) => {
    let tar_data = {};
    if (type == 'parentInnerLabelId') {
      tar_data = JSON.parse(JSON.stringify(label_data.value));
    }
    return tar_data;
  };

  // 打开新增标签弹窗
  const addLabel = () => {
    let obj = JSON.parse(JSON.stringify(content.value.filter(
      (item) => item.name != 'parentInnerLabelId',
    )));
    for (let i = 0; i < obj.length; i++) {
      if (obj[i].name == 'innerLabelName') {
        obj.splice(i + 1, 0, getNewData('parentInnerLabelId'));
        obj[i].initialValue = undefined;
      }else if(obj[i].name == 'isShow'){
        obj[i].initialValue = 1;
      } else {
        obj[i].initialValue = undefined;
      }
    }
    addData.value = obj
    parentInnerLabelId.value = 0;
    title.value = L('新增店铺分类');
    type.value = 'add';
    modalVisible.value = true;
  };

  //编辑商品标签
  const editLabel = (val) => {
    let obj = JSON.parse(JSON.stringify(content.value.filter(
      (item) => item.name != 'parentInnerLabelId',
    )));
    for (let i = 0; i < obj.length; i++) {
      obj[i].initialValue = val[obj[i].name];
    }
    addData.value = obj
    operate_item.value = val;
    parentInnerLabelId.value = val.parentInnerLabelId;
    type.value = 'edit';
    title.value = L('编辑店铺分类');
    modalVisible.value = true;
    curData.value = val;
  };

  //添加子标签功能
  const addNextLabel = (val) => {
    let obj = JSON.parse(JSON.stringify(content.value.filter(
      (item) => item.name != 'parentInnerLabelId',
    )));
    for (let i = 0; i < obj.length; i++) {
      if (obj[i].name == 'innerLabelName') {
        obj.splice(i + 1, 0, getNewData('parentInnerLabelId'));
        obj[i].initialValue = undefined;
      } else if (obj[i].name == 'parentInnerLabelId') {
        obj[i].initialValue = val.innerLabelId;
        obj[i].disable = true;
      } else if (obj[i].name == 'innerLabelSort') {
        obj.splice(i + 2, 0, getNewData('image'));
        obj[i].initialValue = undefined;
      }
    }
    addData.value = obj
    operate_item.value = val;
    parentInnerLabelId.value = val.innerLabelId;
    title.value = L('添加下级分类');
    type.value = 'add';
    modalVisible.value = true;
  };

  // 关闭弹窗
  const handleCancle = () => {
    addData.value = [];
    modalVisible.value = false;
  };

  // 弹窗点击确定
  const handleConfirm = async (val) => {
    let sld_params = {};
    sld_params.innerLabelName = val.innerLabelName;
    sld_params.innerLabelSort = val.innerLabelSort;
    sld_params.isShow = val.isShow
    if (type.value != 'edit') {
      sld_params.parentInnerLabelId = val.parentInnerLabelId ? val.parentInnerLabelId : 0; //父分类id,一级分类==0
    }
    handleClick(sld_params, type.value);
  };

  const handleTreeSelect = (val, label, extra, index, from_info) => {
    let info = from_info;
    if (label.length == 0) {
      info = info.filter((item) => item.name != 'image');
      parentInnerLabelId.value = 0;
    } else {
      //如果已有图片上传控件，则替换掉
      if (info[info.length - 1].type === 'upload_img_upload') {
        info.splice(info.length - 1, 1, getNewData('image'));
      } else {
        //如果没有图片上传控件，则新增
        info.splice(info.length, 0, getNewData('image'));
      }
      parentInnerLabelId.value = extra.triggerValue;
    }
    addData.value = info;
  };

  //积分标签操作事件 type add:添加 edit:编辑 del:删除 show:是否显示 adv:设置广告
  const handleClick = async (record, type) => {
    let res;
    let params = {};
    if (type == 'show') {
      parentInnerLabelId.value = record.parentInnerLabelId > 0 ? record.parentInnerLabelId : '';
      delete record.parentInnerLabelId;
      params = record;
      res = await getIsShowApi(params);
    } else if (type == 'add') {
      confirmBtnLoading.value = true;
      params = record;
      res = await getAddApi(params);
    } else if (type == 'edit') {
      record.innerLabelId = curData.value.innerLabelId;
      confirmBtnLoading.value = true;
      params = record;
      res = await getUpdateApi(params);
    } else if (type == 'del') {
      parentInnerLabelId.value = record.parentInnerLabelId > 0 ? record.parentInnerLabelId : '';
      delete record.parentInnerLabelId;
      params = record;
      res = await getLabelDelApi(params);
    }
    if (res.state == 200) {
      sucTip(res.msg);
      get_list();
      confirmBtnLoading.value = false;
      modalVisible.value = false;
      addData.value = [];
    } else {
      confirmBtnLoading.value = false;
      failTip(res.msg);
    }
  };

  onMounted(() => {
    get_list(); //grade为1表示获取一级数据
  });
</script>
<style lang="less">
  .point_label_body {
    .business_load_img {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 50px;
      height: 50px;
      margin: auto;

      img {
        max-width: 100%;
        max-height: 100%;
      }
    }

    .ant-table-body {
      height: auto !important;
    }
  }
</style>
