<!-- 公告组件 -->
<template>
  <div
    :style="{
      background: data.color ? data.color : '#fff',
      paddingTop: data.top_margin + 'px',
      paddingBottom: data.bottom_margin + 'px',
    }"
  >
    <div v-if="data.show_style == 'one'" class="gonggao">
      <img class="left_img_1" :src="sld_m_diy_notice_style.filter((item) => item.sele_style == data.show_style)[0]['left_img']"/>
      <marquee class="show_style_1">
        <span class="left_img_1_text">{{ data.text ? data.text : '公告：请填写内容,将会在手机上滚动显示!!!' }}</span>
      </marquee>
      <span class="more_text_1">&gt;&gt;</span>
    </div>
    <div v-else class="gonggao">
      <img class="left_img_2" :src="sld_m_diy_notice_style.filter((item) => item.sele_style == props.data.show_style)[0]['left_img']"/>
      <marquee class="show_style_2">
        <span class="left_img_2_text">{{ data.text ? data.text : '公告：请填写内容,将会在手机上滚动显示!!!' }}</span>
      </marquee>
      <span class="more_text_2">&gt;&gt;</span>
    </div>
  </div>
</template>
<script setup>
  import { sld_m_diy_notice_style } from '/@/utils/utils';

  const props = defineProps({
    data: {
      type: Object,
      default: {
        top_margin: 10,
        bottom_margin: 0,
        text: 30, //辅助空白的高度
        color: '#fff', //颜色
      }
    },
  });
</script>
<style lang="less" scoped>
.gonggao {
  position: relative;
  width: 100%;
  height: 40px;
  overflow: hidden;
  line-height: 40px;

  .icon {
    position: absolute;
    z-index: 2;
    top: 0;
    left: 0;
    width: 25px;
    height: 40px;
    padding-top: 4px;
    background: #fff;
    text-align: center;
  }

  img{
    position: absolute;
    z-index: 2;
    top: 0;
    left: 0;
    height: 40px;
  }

  .left_img_1{
    width: 63px;
  }

  .left_img_2{
    width: 69px;
  }

  span {
    font-size: 12px;
  }

  .left_img_1_text{
    color: #333;
  }

  .left_img_2_text{
    color: #fff;
  }

  .show_style_2{
    background: #3A3A3A;
  }

  .show_style_1{
    height: 22px;
    margin: 9px 0;
    background: linear-gradient(90deg, #FFF4F4 0%, #FFF 100%);
    line-height: 22px;
  }

  .more_text_1{
    position: absolute;
    z-index: 2;
    top: 12px;
    right: 0;
    height: 16px;
    padding: 0 10px;
    border-left: 1px solid rgb(0 0 0 / 10%);
    background: #fff;
    color: #2D2D2D;
    font-size: 14px;
    line-height: 16px;
  }

  .more_text_2{
    position: absolute;
    z-index: 2;
    top: 12px;
    right: 0;
    height: 16px;
    padding: 0 10px;
    border-left: 1px solid #fff;
    background: #3A3A3A;
    color: #fff;
    font-size: 14px;
    line-height: 16px;
  }
}
</style>