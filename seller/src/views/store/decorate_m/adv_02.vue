<!-- 短视频组件 -->
<template>
  <div
    class="video flex_column_start_start"
    :style="{
      background: data.color ? data.color : data.show_style == 'three' ? '#f5f5f5' : '#fff',
      paddingTop: data.top_margin + 'px',
      paddingBottom: data.bottom_margin + 'px',
    }"
  >
    <div class="title flex_row_between_center">
      <span class="left_con">{{ data.title }}</span>
      <span class="right_con">查看更多&nbsp;&nbsp;</span>
    </div>

    <div v-if="(data.data.info.length == 0 && (data.show_style == 'one' || data.show_style == 'two' || data.show_style == 'three'))"
         class="flex_row_start_start video_list"
         :style="{
          height: data.show_style == 'two' ? 111 : (data.show_style == 'one' ? 171 : 230),
          width: '100%',
          overflow: 'hidden',
        }">
      <template v-if="data.data.info.length == 0">
        <div v-for="(item_video, index_video) in sld_com_empty_arrar_4"
            :key="index_video"
            :class="'item_' + data.show_style"
            :style="{
              backgroundImage: 'url(' + video_defalut_img()[data.show_style] + ')',
              borderRadius: data.border_radius + 'px',
            }"></div>
      </template>
    </div>

    <div v-if="data.data.info.length > 0 && data.show_style == 'one'"
         class="flex_row_start_start video_list"
         style=" width: 100%;height: 171px; overflow: hidden">
      <div v-for="(item_video, index_video) in data.data.info"
           :key="index_video"
           :class="'item_' + data.show_style"
           class="flex_column_between_start"
           :style="{
             backgroundImage: 'url(' + item_video.videoImage + ')',
             borderRadius: data.border_radius + 'px'
           }">
        <div class="video_click flex_row_start_center">
          <AliSvgIcon
            width="11px"
            height="11px"
            fillColor="#fff"
            iconName="iconbofang11"
            style="margin-left: 3px"
          />
          <span class="video_click_num">{{ item_video.clickNum }}人观看</span>
        </div>
        <span class="video_name">{{ item_video.videoName }}</span>
      </div>
    </div>

    <div v-if="data.data.info.length > 0 && data.show_style == 'two'"
         class="flex_row_start_start video_list"
         style=" width: 100%;height: 111px; overflow: hidden">
      <div v-for="(item_video, index_video) in data.data.info"
           :key="index_video"
           :class="'item_' + data.show_style"
           class="flex_column_between_start"
           :style="{
            backgroundImage: 'url(' + item_video.videoImage + ')',
            borderRadius: data.border_radius + 'px'
           }">
        <div class="video_click flex_row_start_center">
          <AliSvgIcon
            width="11px"
            height="11px"
            fillColor="#fff"
            iconName="iconbofang11"
            style="margin-left: 3px"
          />
          <span class="video_click_num">{{ item_video.clickNum }}人观看</span>
        </div>
        <span
          class="video_name"
          :style="{
            borderBottomRightRadius: data.border_radius + 'px',
            borderBottomLeftRadius: data.border_radius + 'px',
          }"
        >{{ item_video.videoName }}</span>
      </div>
    </div>

    <div v-if="data.data.info.length > 0 && data.show_style == 'three'"
         class="flex_row_start_start video_list"
         style=" width: 100%;height: 230px; overflow: hidden">
      <div v-for="(item_video, index_video) in data.data.info"
           :key="index_video"
           :class="'item_' + data.show_style"
           class="flex_column_between_start"
           :style="{ borderRadius: data.border_radius + 'px' }">
        <img class="bg_img" :src="item_video.videoImage"
             :style="{
              borderTopLeftRadius: data.border_radius + 'px',
              borderTopRightRadius: data.border_radius + 'px'
             }"/>
        <span
          class="bg_color"
          :style="{
            borderTopLeftRadius: data.border_radius + 'px',
            borderTopRightRadius: data.border_radius + 'px',
            backgroundColor: videoStyelThreeBgColor[index_video],
          }"
        ></span>
        <span
          class="bottom_bg_color"
          :style="{
            borderBottomRightRadius: data.border_radius + 'px',
            borderBottomLeftRadius: data.border_radius + 'px',
          }"
        ></span>
        <div class="video_info flex_column_center_center">
          <span class="video_click_num">{{ item_video.clickNum }}人观看</span>
          <div class="video_img flex_row_center_center"
               :style="{ backgroundImage: 'url(' + item_video.videoImage + ')' }">
            <AliSvgIcon
              width="20px"
              height="20px"
              fillColor="#fff"
              iconName="icon2"
              style="margin-left: 3px"
            />
          </div>
          <span class="video_name">{{ item_video.videoName }}</span>
          <span class="video_desc">{{ item_video.introduction }}</span>
        </div>
      </div>
    </div>

    <template v-if="data.show_style == 'four'">
      <swiper
        :modules="modules"
        :autoplay="{ delay: 3000 }"
        :loop="true"
        :slides-per-view="3"
        :space-between="100"
        @slideChange="(e) => handleSlideChange(e)"
        style="width: 351px"
      >
        <template v-if="data.data.info.length == 0">
          <template v-for="(item, index) in sld_com_empty_arrar_6" :key="index">
            <swiper-slide>
              <div class="svideo_default_img" :class="active_index == index ? 'active' : ''">
                <img
                  :src="video_defalut_img()[data.show_style]"
                  :style="{ borderRadius: data.border_radius + 'px' }"
                />
              </div>
            </swiper-slide>
          </template>
        </template>
        <template v-else>
          <template v-for="(item_video, index_video) in data.data.info" :key="index_video">
            <swiper-slide>
              <div class="svideo_default_img" :class="active_index == index_video ? 'active' : ''">
                <div
                  style="
                    width: 100%;
                    height: 100%;
                    background-repeat: no-repeat;
                    background-position: center;
                    background-size: cover;
                  "
                  :style="{
                    backgroundImage: 'url('+item_video.videoImage+')',
                    borderRadius: data.border_radius + 'px'
                  }"
                ></div>
                <!-- <img
                  :src="item_video.videoImage"
                  :style="{ borderRadius: data.border_radius + 'px' }"
                /> -->
                <img
                  class="item_four_video_left_bg"
                  :style="{ borderTopLeftRadius: data.border_radius + 'px' }"
                  src="@/assets/images/m_diy_img/svideo/four_left_top_bg.png"
                />
                <div class="item_four_video_click">
                  <span class="video_click_num">{{ item_video.clickNum }}人观看</span>
                </div>
              </div>
            </swiper-slide>
          </template>
        </template>
      </swiper>
    </template>

    <template v-if="data.show_style == 'five'">
      <div class="flex_row_start_start video_list">
        <template v-if="data.data.info.length == 0">
          <div
            v-for="(item_video, index_video) in sld_com_empty_arrar_4"
            :key="index_video"
            class="item_five"
            :style="{
              backgroundImage: 'url(' + video_defalut_img()[data.show_style] + ')',
              borderRadius: data.border_radius + 'px',
            }"
          ></div>
        </template>
        <template v-else>
          <div
            v-for="(item_video, index_video) in data.data.info"
            :key="index_video"
            class="item_five"
            :style="{
              backgroundImage: 'url(' + item_video.videoImage + ')',
              borderRadius: data.border_radius + 'px',
            }">
            <div
              class="video_click_wrap flex_row_center_center"
              :style="{
                borderBottomRightRadius: data.border_radius + 'px',
                borderTopLeftRadius: data.border_radius + 'px',
              }"
            >
              <AliSvgIcon
                width="14px"
                height="14px"
                fillColor="#fff"
                iconName="iconbofang11"
                style="margin-left: 3px"
              />
              <span class="video_click_num">{{ item_video.clickNum }}人观看</span>
            </div>
            <img
              class="bottom_bg"
              :style="{
                borderBottomRightRadius: data.border_radius + 'px',
                borderBottomLeftRadius: data.border_radius + 'px',
              }"
              :src="get_front_bg_img(index_video)"/>
            <span class="video_name">{{ item_video.videoName }}</span>
          </div>
        </template>
      </div>
    </template>
  </div>
</template>
<script setup>
  import { ref } from 'vue';
  import { Swiper, SwiperSlide } from 'swiper/vue';
  import { Autoplay } from 'swiper/modules';
  import 'swiper/css/autoplay';
  import 'swiper/css';
  import {
    sld_com_empty_arrar_4,
    sld_com_empty_arrar_6,
    video_defalut_img,
    newImg,
  } from '@/utils/utils';

  const modules = [Autoplay];
  const props = defineProps({
    data: {
      type: Object,
      default: {
        top_margin: 10,
        bottom_margin: 0,
        color: '#fff',
        title: '商联达短视频',//标题名称
        view_more_url: '',//查看更多的链接
        show_style: 'one',//展示风格：one 只显示2个商品，不可滚动，two 只显示3个商品，不可滚动 three 只显示2个商品，不可滚动，长形显示   four 异形轮播 five 多余3个的轮播
        border_radius: 8,//短视频卡片边角，直角0、圆角5px
        data: {
          ids: [],//短视频id集合
          info: [],//短视频信息
        },//短视频数据
      }
    },
  });
  const active_index = ref(1); //当前滚动下标

  function get_front_bg_img(index_video) {
    return new URL('/@/assets/images/m_diy_img/svideo/front_bg_' + index_video + '.png', import.meta.url).href;
  }

  //轮播图滚动事件
  function handleSlideChange(e) {
    if (props.data.data.info.length == 0) {
      active_index.value = e.realIndex == 4 ? 0 : e.realIndex+1;
    } else {
      active_index.value = e.realIndex == props.data.data.info.length-1 ? 0 : e.realIndex+1;
    }
  }
</script>
<style lang="less" scoped>
  .video {
    width: 371px;
    padding: 0 10px 10px;

    .title {
      width: 100%;
      height: 36px;
      line-height: 36px;

      .left_con {
        color: #343434;
        font-size: 14px;
        font-weight: bold;
      }

      .right_con {
        color: #666;
        font-size: 12px;
      }
    }

    .video_list {
      flex-wrap: nowrap;
      width: 100%;
      height: 100%;
      overflow-x: auto;
      overflow-y: hidden;

      .item_one {
        position: relative;
        flex-shrink: 0;
        width: 171px;
        height: 171px;
        margin-right: 10px;
        background-repeat: no-repeat;
        background-position: center center;
        background-size: cover;

        .video_name {
          display: inline-block;
          width: 100%;
          padding: 7px 10px;
          overflow: hidden;
          color: #fff;
          font-size: 12px;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .video_click {
          height: 17px;
          margin: 5px;
          border-radius: 8px;
          background: linear-gradient(90deg, #666 0%, rgb(0 0 0 / 0%) 100%);
          font-size: 12px;

          .video_click_num {
            margin-left: -2px;
            transform: scale(0.85);
            color: #fff;
            font-size: 12px;
          }
        }
      }

      .item_two {
        position: relative;
        flex-shrink: 0;
        width: 111px;
        height: 111px;
        margin-right: 10px;
        background-repeat: no-repeat;
        background-position: center center;
        background-size: cover;

        .video_name {
          display: inline-block;
          width: 100%;
          padding: 2px 7px;
          overflow: hidden;
          background: rgb(0 0 0 / 30%);
          color: #fff;
          font-size: 12px;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .video_click {
          height: 17px;
          margin: 5px;
          border-radius: 8px;
          background: linear-gradient(90deg, #000 0%, rgb(0 0 0 / 0%) 100%);
          font-size: 12px;

          .video_click_num {
            margin-left: -2px;
            transform: scale(0.85);
            color: #fff;
            font-size: 12px;
          }
        }
      }

      .item_three {
        position: relative;
        flex-shrink: 0;
        width: 171px;
        height: 230px;
        margin-right: 10px;
        background: #fff;
        background-repeat: no-repeat;
        background-position: center center;
        background-size: cover;

        .bg_img {
          position: absolute;
          z-index: 2;
          top: 0;
          right: 0;
          left: 0;
          width: 100%;
          height: 171px;
        }

        .bg_color {
          position: absolute;
          z-index: 3;
          top: 0;
          right: 0;
          left: 0;
          flex-shrink: 0;
          width: 100%;
          height: 171px;
          opacity: 0.8;
        }

        .bottom_bg_color {
          position: absolute;
          z-index: 4;
          right: 0;
          bottom: 0;
          left: 0;
          height: 73px;
          background: #fff;
        }

        .video_info {
          position: absolute;
          z-index: 5;
          inset: 0;

          .video_click_num {
            display: inline-block;
            height: 18px;
            margin-top: 5px;
            padding: 0 8px;
            transform: scale(0.9);
            border-radius: 9px;
            background: rgb(0 0 0 / 30%);
            color: #fff;
            font-size: 12px;
            line-height: 18px;
          }

          .video_img {
            width: 115px;
            height: 115px;
            margin-top: 18px;
            margin-bottom: 10px;
            overflow: hidden;
            border-radius: 50%;
            background-repeat: no-repeat;
            background-position: center center;
            background-size: cover;
          }

          .video_name {
            display: inline-block;
            width: 100%;
            padding: 0 20px;
            overflow: hidden;
            color: #333;
            font-size: 13px;
            text-align: center;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .video_desc {
            display: inline-block;
            width: 100%;
            padding: 0 25px;
            overflow: hidden;
            color: #666;
            font-size: 12px;
            text-align: center;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }

      .item_four_video_click {
        position: absolute;
        z-index: 2;
        top: 0;
        bottom: 10px;
        left: 0;
        height: 24px;
        text-align: center;

        .video_click_num {
          display: inline-block;
          height: 24px;
          padding: 0 10px;
          border-radius: 12px;
          opacity: 0.4;
          background: rgb(0 0 0 / 40%);
          font-size: 13px;
          line-height: 24px;
        }
      }

      .item_five {
        position: relative;
        flex-shrink: 0;
        width: 150px;
        height: 150px;
        margin-right: 10px;
        background-repeat: no-repeat;
        background-position: center center;
        background-size: cover;

        .video_click_wrap {
          position: absolute;
          z-index: 2;
          left: 0;
          box-sizing: border-box;
          height: 20px;
          padding: 0 4px 0 3px;
          background: rgb(0 0 0 / 40%);
          line-height: 20px;

          .video_click_num {
            display: inline-block;
            margin-left: -1px;
            transform: scale(0.83);
            color: #fff;
            font-size: 13px;
          }
        }

        .bottom_bg {
          position: absolute;
          z-index: 2;
          right: 0;
          bottom: 0;
          left: 0;
          height: 45px;
        }

        .video_name {
          position: absolute;
          z-index: 3;
          right: 0;
          bottom: 0;
          left: 0;
          padding: 7px 14px;
          overflow: hidden;
          background: transparent;
          color: #fff;
          font-size: 12px;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .svideo_default_img {
      position: relative;
      right: 50px;
      width: 128px;
      height: 128px;
      margin-top: 15px;
      margin-right: 50px;
      overflow: hidden;
      transition: 300ms;

      &.active {
        right: 64px;
        width: 156px;
        height: 156px;
        margin-top: 0;
      }

      img {
        width: 100%;
        height: 100%;
      }

      .item_four_video_left_bg {
        position: absolute;
        z-index: 2;
        top: 0;
        left: 0;
        width: 18px;
        height: 18px;
      }

      .item_four_video_click {
        position: absolute;
        z-index: 2;
        right: 0;
        bottom: 10px;
        left: 0;
        text-align: center;

        .video_click_num {
          display: inline-block;
          height: 40px;
          padding: 0 20px;
          transform: scale(0.5);
          border-radius: 20px;
          background: rgb(0 0 0 / 40%);
          color: #fff;
          font-size: 22px;
          font-weight: bold;
          line-height: 40px;
          white-space: nowrap;
        }
      }
    }
  }
</style>