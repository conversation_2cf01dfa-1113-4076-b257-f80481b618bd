<!-- 轮播图组件 -->
<template>
  <div
    :style="{
      background: data.color ? data.color : '#fff',
      paddingTop: data.top_margin + 'px',
      paddingBottom: data.bottom_margin + 'px',
    }"
  >
    <div v-if="data.data.length == 0"
        class="empty_swiper_img flex_column_center_center"
        :style="{ height: data.height * 373 / 710 + 'px' }">
      <AliSvgIcon
        width="40.8px"
        height="33.6px"
        fillColor="#fff"
        iconName="icontupian1"
      />
      <span class="center_tip flex_row_common">宽710*高不限</span>
    </div>
    <div v-else style="width: 100%;">
      <Carousel
        autoplay
        :dots="false"
      >
        <div
          v-for="(item, index) in data.data"
          :key="index"
          class="lunbo_wrap flex_row_common"
          :style="{ height: data.height * 373 / 710 + 'px' }">
          <img v-if="item.img" :src="item.img"/>
          <div v-else
              :style="{ height: (item.img_path ? data.height * 373 / 710 : 120) + 'px' }"
              class="empty_swiper_img flex_column_center_center">
            <AliSvgIcon
              width="40.8px"
              height="33.6px"
              fillColor="#fff"
              iconName="icontupian1"
            />
            <span class="center_tip flex_row_common">宽710*高不限</span>
          </div>
        </div>
      </Carousel>
    </div>
  </div>
</template>
<script setup>
  import { Carousel } from 'ant-design-vue';
  const props = defineProps({
    data: {
      type: Object,
      default: {
        top_margin: 10,
        bottom_margin: 0,
        color: '#fff',
        width: 750, //整个轮播的宽度
        height: 285.5, //整个轮播的高度
        data: [], //模块数据
      }
    },
  });
</script>
<style lang="less" scoped>
  .empty_swiper_img{
    width: 100%;
    height: 140px;
    border-radius: 8px;
    background: #ECF5FF;

    .center_tip {
      padding: 8px;
      color: #666;
      font-size: 12px;
    }
  }

  .lunbo_wrap {
    display: flex !important;
    position: relative;
    width: 100%;
    margin: 0 auto;

    img {
      max-width: 100%;
      max-height: 100%;
    }
  }
</style>