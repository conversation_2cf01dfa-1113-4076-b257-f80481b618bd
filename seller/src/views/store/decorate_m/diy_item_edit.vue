<!-- 装修编辑组件 -->
<template>
  <div class="diy_item_edit">
    <Form
      ref="formRef"
      :model="curFormData"
      :scrollToFirstError="true"
    >
      <!-- 直播组件 -->
      <template v-if="curFormData.type == 'live'">
        <div class="sub_part">
          <div class="subtitle">上边距</div>
          <FormItem
            name="live_top_margin"
          >
            <Slider
              :min="0"
              :max="200"
              :defaultValue="curFormData.top_margin"
              @change="(e) => onChange(e, 'top_margin')"
            />
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">下边距</div>
          <FormItem
            name="live_bottom_margin"
          >
            <Slider
              :min="0"
              :max="200"
              :defaultValue="curFormData.bottom_margin"
              @change="(e) => onChange(e, 'bottom_margin')"
            />
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">背景色</div>
          <FormItem
            name="live_color"
          >
            <div class="flex_row_start_center fzx_color_show">
              <ColorPicker
                :color="curFormData.color"
                @update:color="(e) => onChange('rgba(' + e.r + ',' + e.g + ',' + e.b + ',' + e.a + ')', 'color')"
                :hideReset="true"
              ></ColorPicker>
              
            </div>
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">展示风格</div>
          <FormItem
            name="live_show_style"
          >
            <div class="com_default_styles flex_com_row_start_center">
              <div
                v-for="(item) in sld_m_diy_live_style"
                :key="item.sele_style"
                class="svideo_show_style_item flex_com_row_center"
                :class="curFormData.show_style == item.sele_style ? 'sel_svideo_show_style' : null"
                @click="handleLiveStyle(item.sele_style)">
                <img :src="item.img"/>
              </div>
            </div>
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">直播卡片边角</div>
          <FormItem
            name="live_border_radius"
          >
            <Radio.Group
              :value="curFormData.border_radius"
              @change="(e) => onChange(e.target.value, 'border_radius', undefined, undefined)"
            >
              <Radio :value="8">圆角</Radio>
              <Radio :value="0">直角</Radio>
            </Radio.Group>
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">设置标题</div>
          <FormItem
            name="live_title"
          >
            <Input
              :maxLength="10"
              style="width: 300px"
              placeholder="请输入标题，最多10个字"
              :value="curFormData.title"
              @change="(e) => onChange(e.target.value, 'title', undefined, undefined)"
            />
          </FormItem>
        </div>
        <div class="sub_part" style="border-bottom-width: 0">
          <div class="subtitle">添加直播</div>
          <FormItem
            name="live_list"
          >
            <div class="selected_svideo flex_column_start_start">
              <div class="add_svideo_wrap flex_row_start_center">
                <span class="required">*</span>
                <span class="title">直播添加:</span>
                <a
                  href="javascript:void(0)"
                  class="add_svideo_btn"
                  @click="selMoreLive(curFormData)"
                >+ 添加</a>
                <!-- <span class="tip">最多添加30个</span> -->
              </div>

              <template v-if="curFormData.data.info.length > 0">
                <div class="selected_svideo_list_title">
                  <span style="width: 50px">序号</span>
                  <span style="width: 176px">直播信息</span>
                  <span style="width: 80px">播放量</span>
                  <span style="width: 50px">操作</span>
                </div>
                <div class="selected_svideo_list">
                  <div
                    v-for="(svideo_item, svideo_index) in curFormData.data.info"
                    :key="svideo_index"
                    class="selected_svideo_item flex_row_start_center">
                    <span style="width: 50px">{{ svideo_index + 1 }}</span>
                    <div class="svideo_info flex_row_start_center">
                      <div class="left flex_row_center_center">
                        <img :src="svideo_item.liveCover"/>
                        <div class="play_icon">
                          <AliSvgIcon
                            width="22px"
                            height="22px"
                            fillColor="#fff"
                            iconName="iconbofang11"
                          />
                        </div>
                      </div>
                      <div class="right flex_column_start_start">
                        <span class="video_name">{{ svideo_item.liveName }}</span>
                        <span class="video_label">{{ svideo_item.labelName }}</span>
                      </div>
                    </div>
                    <span style="width: 80px">{{ svideo_item.viewingNum || 0 }}</span>
                    <div class="operate flex_row_center_center" @click="delLive(svideo_item.liveId)">
                      <AliSvgIcon
                        width="18px"
                        height="18px"
                        fillColor="#2d2d2d"
                        iconName="iconshanchu5"
                      />
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </FormItem>
        </div>
      </template>
      <!-- 短视频组件 -->
      <template v-else-if="curFormData.type == 'svideo'">
        <div class="sub_part">
          <div class="subtitle">上边距</div>
          <FormItem
            name="svideo_top_margin"
          >
            <Slider
              :min="0"
              :max="200"
              :defaultValue="curFormData.top_margin"
              @change="(e) => onChange(e, 'top_margin')"
            />
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">下边距</div>
          <FormItem
            name="svideo_bottom_margin"
          >
            <Slider
              :min="0"
              :max="200"
              :defaultValue="curFormData.bottom_margin"
              @change="(e) => onChange(e, 'bottom_margin')"
            />
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">背景色</div>
          <FormItem
            name="svideo_color"
          >
            <div class="flex_row_start_center fzx_color_show">
              <ColorPicker
                :color="curFormData.color"
                @update:color="(e) => onChange('rgba(' + e.r + ',' + e.g + ',' + e.b + ',' + e.a + ')', 'color')"
                :hideReset="true"
              ></ColorPicker>
              
            </div>
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">展示风格</div>
          <FormItem
            name="svideo_show_style"
          >
            <div class="com_default_styles flex_com_row_start_center">
              <div
                v-for="(item) in sld_m_diy_svideo_style"
                :key="item.sele_style"
                class="svideo_show_style_item flex_com_row_center"
                :class="curFormData.show_style == item.sele_style ? 'sel_svideo_show_style' : null"
                @click="handleVideoStyle(item.sele_style)"
              >
                <img :src="item.img"/>
              </div>
            </div>
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">短视频卡片边角</div>
          <FormItem
            name="svideo_border_radius"
          >
            <Radio.Group
              :value="curFormData.border_radius"
              @change="(e) => onChange(e.target.value, 'border_radius')">
              <Radio :value="8">圆角</Radio>
              <Radio :value="0">直角</Radio>
            </Radio.Group>
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">设置标题</div>
          <FormItem
            name="svideo_title"
          >
            <Input
              :maxLength="10"
              style="width: 300px"
              placeholder="请输入标题，最多10个字"
              :value="curFormData.title"
              @change="(e) => onChange(e.target.value, 'title')"
            />
          </FormItem>
        </div>
        <div class="sub_part" style="border-bottom-width: 0">
          <div class="subtitle">添加短视频</div>
          <FormItem
            name="svideo_list"
          >
            <div class="selected_svideo flex_column_start_start">
              <div class="add_svideo_wrap flex_row_start_center">
                <span class="required">*</span>
                <span class="title">视频添加:</span>
                <a
                  href="javascript:void(0)"
                  class="add_svideo_btn"
                  @click="selMoreSvideo(curFormData)"
                >+ 添加</a>
                <!-- <span class="tip">最多添加{{ sele_more_svideo.min_num }}个</span> -->
              </div>

              <template v-if="curFormData.data.info.length > 0">
                <div class="selected_svideo_list_title">
                  <span style="width: 50px">序号</span>
                  <span style="width: 176px">短视频信息</span>
                  <span style="width: 80px">播放量</span>
                  <span style="width: 50px">操作</span>
                </div>
                <div class="selected_svideo_list">
                  <div
                    v-for="(svideo_item, svideo_index) in curFormData.data.info"
                    :key="svideo_index"
                    class="selected_svideo_item flex_row_start_center"
                  >
                      <span style="width: 50px">{{ svideo_index + 1 }}</span>
                      <div class="svideo_info flex_row_start_center">
                        <div class="left flex_row_center_center">
                          <img :src="svideo_item.videoImage"/>
                          <div class="play_icon">
                            <AliSvgIcon
                              width="22px"
                              height="22px"
                              fillColor="#fff"
                              iconName="iconbofang11"
                            />
                          </div>
                        </div>
                        <div class="right flex_column_start_start">
                          <span class="video_name">{{ svideo_item.videoName }}</span>
                          <span class="video_label">{{ svideo_item.labelName }}</span>
                        </div>
                      </div>
                      <span style="width: 80px">{{ svideo_item.click_num || 0 }}</span>
                      <div class="operate flex_row_center_center" @click="delSvideo(svideo_item.videoId)">
                        <AliSvgIcon
                          width="18px"
                          height="18px"
                          fillColor="#2d2d2d"
                          iconName="iconshanchu5"
                        />
                      </div>
                    </div>
                </div>
              </template>
            </div>
          </FormItem>
        </div>
      </template>
      <!-- TAB切换组件 -->
      <template v-else-if="curFormData.type == 'more_tab'">
        <div class="sub_part">
          <div class="subtitle">上边距</div>
          <FormItem
            name="more_tab_top_margin"
          >
            <Slider
              :min="0"
              :max="200"
              :defaultValue="curFormData.top_margin"
              @change="(e) => onChange(e, 'top_margin')"
            />
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">下边距</div>
          <FormItem
            name="more_tab_bottom_margin"
          >
            <Slider
              :min="0"
              :max="200"
              :defaultValue="curFormData.bottom_margin"
              @change="(e) => onChange(e, 'bottom_margin')"
            />
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">背景色</div>
          <FormItem
            name="more_tab_color"
          >
            <div class="flex_row_start_center fzx_color_show">
              <ColorPicker
                :color="curFormData.color"
                @update:color="(e) => onChange('rgba(' + e.r + ',' + e.g + ',' + e.b + ',' + e.a + ')', 'color')"
                :hideReset="true"
              ></ColorPicker>
              
            </div>
          </FormItem>
        </div>
        <div class="sub_part" style="border-bottom-width: 0">
          <div class="subtitle">数据卡片边角</div>
          <FormItem
            name="more_tab_border_radius"
            style="margin-bottom: 12px;"
          >
            <Radio.Group
              :value="curFormData.border_radius"
              @change="(e) => onChange(e.target.value, 'border_radius')"
            >
              <Radio :value="8">圆角</Radio>
              <Radio :value="0">直角</Radio>
            </Radio.Group>
          </FormItem>
        </div>
        <template v-if="curFormData.data.length > 0">
          <template v-for="(more_tab_item, more_tab_index) in curFormData.data" :key="more_tab_index">
            <div class="selected_more_tab flex_column_start_start">
            <div
              class="flex_com_column_flex_end del_sld_more_tab_item"
              @click="delSldComImg(more_tab_index)"
            >
              <AliSvgIcon
                width="16px"
                height="16px"
                fillColor="#666"
                iconName="iconqingchu"
              />
            </div>
            <div
              class="add_svideo_wrap flex_row_start_center"
              style="padding-top: 15px; padding-bottom: 5px"
            >
              <span class="required">*</span>
              <span class="title">分类标题:</span>
              <FormItem
                name="gonggao_text"
                style="margin-bottom: 10px"
              >
                <Input
                  :maxLength="4"
                  placeholder="请输入分类标题，最多4个字"
                  :value="more_tab_item.title"
                  @change="(e) => onChange(e.target.value, 'title', more_tab_index)"
                  style="width: 263px; margin-left: 9px"
                />
              </FormItem>
            </div>
            <div class="add_svideo_wrap flex_row_start_center" style="padding-bottom: 5px">
              <span class="required" style="margin-left: 29px">*</span>
              <span class="title">子标题:</span>
              <FormItem
                name="gonggao_sub_title"
                style="margin-bottom: 2px"
              >
                <Input
                  :maxLength="6"
                  placeholder="请输入分类子标题，最多6个字"
                  :value="more_tab_item.sub_title"
                  @change="(e) => onChange(e.target.value, 'sub_title', more_tab_index)"
                  style="width: 263px; margin-left: 9px"
                />
              </FormItem>
            </div>
            <div class="add_svideo_wrap flex_row_start_center" v-if="diy_type != 'integral' && diy_type != 'spreader'">
              <span class="required">*</span>
              <span class="title">数据类型:</span>
              <FormItem
                :name="'more_tab_data_type_' + more_tab_index"
                style="margin-bottom: 10px"
              >
                <Radio.Group
                  :value="more_tab_item.data_type"
                  @change="(e) => onChange(e.target.value, 'data_type', more_tab_index)"
                  style="margin-left: 10px"
                >
                  <Radio value="goods">商品</Radio>
                </Radio.Group>
              </FormItem>
            </div>
            <div
              v-if="diy_type != 'integral' && diy_type != 'spreader' && more_tab_item.data_type == 'goods'"
              class="add_svideo_wrap flex_row_start_center add_cart_icon_special"
            >
              <span class="required">*</span>
              <span class="title" style="margin-right: 10px">加车图标:</span>
              <template v-for="(icon_item, icon_index) in cart_icon_data()" :key="icon_index">
                <div
                  class="cart_icon_wrap"
                  :class="more_tab_item.cart_icon_type == icon_item.type ? 'current' : null"
                  @click="onChange(icon_item.type, 'cart_icon_type', more_tab_index)"
                  :style="{ padding: icon_item.padding + 'px' }"
                >
                  <AliSvgIcon
                    :width="icon_item.width + 'px'"
                    :height="icon_item.width + 'px'"
                    fillColor="#F10D3B"
                    :iconName="'icon' + icon_item.icon"
                  />
                </div>
              </template>
            </div>
            <div class="add_svideo_wrap flex_row_start_center add_data_special"
                 :style="{
                  height: more_tab_item.data_type == 'goods' ? '65px' : '45px',
                  paddingBottom: more_tab_item.data_type == 'goods' ? 0 : '12px'
                 }">
              <span class="required">*</span>
              <span class="title">数据添加:</span>
              <a href="javascript:void(0)" class="add_svideo_btn"
                 @click="selMoreTabData(data, more_tab_index)">+ 添加</a>
              <span class="tip">需要选择偶数个,最少2个</span>
            </div>
            <template v-if="more_tab_item.info.length > 0">
              <MoreTabSeleData
                :data="more_tab_item.info"
                :type="more_tab_item.data_type"
                :tar_index="more_tab_index"
                :modalType="curFormData.type"
                :extra="curFormData"
                :diy_type="diy_type"
              ></MoreTabSeleData>
            </template>
          </div>
          </template>
        </template>
        <div class="add_goods_wrap flex_row_center_center"
             style="margin-bottom: 20px;"
             @click="addMoreTabNav(data)">+ 添加分类导航</div>
      </template>
      <!-- 辅助线组件 -->
      <template v-else-if="curFormData.type == 'fzx'">
        <div class="sub_part">
          <div class="subtitle">上边距</div>
          <FormItem
            name="fzx_top_margin"
          >
            <Slider
              :min="0"
              :max="200"
              :defaultValue="curFormData.top_margin"
              @change="(e) => onChange(e, 'top_margin')"
            />
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">下边距</div>
          <FormItem
            name="fzx_bottom_margin"
          >
            <Slider
              :min="0"
              :max="200"
              :defaultValue="curFormData.bottom_margin"
              @change="(e) => onChange(e, 'bottom_margin')"
            />
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">背景色</div>
          <FormItem
            name="fzx_bg_color"
          >
            <div class="flex_row_start_center fzx_color_show">
              <ColorPicker
                :color="curFormData.bg_color"
                @update:color="(e) => onChange('rgba(' + e.r + ',' + e.g + ',' + e.b + ',' + e.a + ')', 'bg_color')"
                :hideReset="true"
              ></ColorPicker>
              
            </div>
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">颜色</div>
          <FormItem
            name="fzx_color"
          >
            <div class="flex_row_start_center fzx_color_show">
              <ColorPicker
                :color="curFormData.color"
                @update:color="(e) => onChange('rgba(' + e.r + ',' + e.g + ',' + e.b + ',' + e.a + ')', 'color')"
                :hideReset="true"
              ></ColorPicker>
              
            </div>
          </FormItem>
        </div>
        <div class="sub_part" :style="{ borderBottomWidth: 0 }">
          <div class="subtitle">样式</div>
          <FormItem
            name="fzx_val"
          >
            <Radio.Group
              :value="curFormData['val']"
              @change="(e) => onChange(e.target.value, 'val')"
            >
              <Radio value="solid">实线</Radio>
              <Radio value="dashed">虚线</Radio>
              <Radio value="dotted">点线</Radio>
            </Radio.Group>
          </FormItem>
        </div>
      </template>
      <!-- 辅助空白组件 -->
      <template v-else-if="curFormData.type == 'fzkb'">
        <div class="sub_part">
          <div class="subtitle">背景色</div>
          <FormItem
            name="fzkb_color"
          >
            <div class="flex_row_start_center fzx_color_show">
              <ColorPicker
                :color="curFormData.color"
                @update:color="(e) => onChange('rgba(' + e.r + ',' + e.g + ',' + e.b + ',' + e.a + ')', 'color')"
                :hideReset="true"
              ></ColorPicker>
              
            </div>
          </FormItem>
        </div>
        <div class="sub_part" :style="{ borderBottomWidth: 0 }">
          <div class="subtitle">空白高度</div>
          <FormItem
            name="fzkb_text"
          >
            <Slider
              :min="10"
              :defaultValue="curFormData.text"
              @change="(e) => onChange(e, 'text')"
            />
          </FormItem>
        </div>
      </template>
      <!-- 公告组件 -->
      <template v-else-if="curFormData.type == 'gonggao'">
        <div class="sub_part">
          <div class="subtitle">上边距</div>
          <FormItem
            name="gonggao_top_margin"
          >
            <Slider
              :min="0"
              :max="200"
              :defaultValue="curFormData.top_margin"
              @change="(e) => onChange(e, 'top_margin')"
            />
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">下边距</div>
          <FormItem
            name="gonggao_bottom_margin"
          >
            <Slider
              :min="0"
              :max="200"
              :defaultValue="curFormData.bottom_margin"
              @change="(e) => onChange(e, 'bottom_margin')"
            />
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">背景色</div>
          <FormItem
            name="gonggao_color"
          >
            <div class="flex_row_start_center fzx_color_show">
              <ColorPicker
                :color="curFormData.color"
                @update:color="(e) => onChange('rgba(' + e.r + ',' + e.g + ',' + e.b + ',' + e.a + ')', 'color')"
                :hideReset="true"
              ></ColorPicker>
              
            </div>
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">展示风格</div>
          <FormItem
            name="svideo_show_style"
          >
            <div class="com_default_styles flex_com_row_start_center">
              <div
                v-for="(item) in sld_m_diy_notice_style"
                :key="item.sele_style"
                class="notice_show_style_item flex_com_row_center"
                :class="curFormData.show_style == item.sele_style ? 'sel_svideo_show_style' : null"
                @click="handleNoticeStyle(item.sele_style)"
              >
                <img :src="item.img"/>
              </div>
            </div>
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">公告内容</div>
          <FormItem
            name="gonggao_text"
          >
            <Input
              :maxLength="200"
              placeholder="请输入公告内容,最多200字"
              :value="curFormData.text"
              @change="(e) => onChange(e.target.value, 'text')"
              style="width: 300px"
            />
          </FormItem>
        </div>
        <div class="sub_part" :style="{ borderBottomWidth: 0 }">
          <div class="subtitle">公告链接</div>
          <FormItem
            name="gonggao_url"
          >
            <Select
              :value="curFormData.url_type"
              placeholder="请选择链接类型"
              @select="sldHandSeleLink"
              style="width: 300px"
            >
              <template v-if="diy_type == 'integral'">
                <Select.Option
                  v-for="(item_link, index_link) in m_diy_point_link_type()"
                  :key="index_link"
                  :value="item_link.key"
                >{{ item_link.name }}</Select.Option>
              </template>
              <template v-else-if="diy_type == 'spreader'">
                <Select.Option
                  v-for="(item_link, index_link) in m_diy_spreader_link_type()"
                  :key="index_link"
                  :value="item_link.key"
                >{{ item_link.name }}</Select.Option>
              </template>
              <template v-else>
                <Select.Option
                  v-for="(item_link, index_link) in m_diy_link_type()"
                  :key="index_link"
                  :value="item_link.key"
                >{{ item_link.name }}</Select.Option>
              </template>
            </Select>
          </FormItem>
        </div>
        <SldDiyItemSelectLink
          :data="curFormData"
          :index="0"
          :showBorderTop="true"
          @handleChange="onChange"
          style="margin-bottom: 30px"
        />
      </template>
      <!-- 客服组件 -->
      <template v-else-if="curFormData.type == 'kefu'">
        <div class="sub_part">
          <div class="subtitle">上边距</div>
          <FormItem
            name="kefu_top_margin"
          >
            <Slider
              :min="0"
              :max="200"
              :defaultValue="curFormData.top_margin"
              @change="(e) => onChange(e, 'top_margin')"
            />
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">下边距</div>
          <FormItem
            name="kefu_bottom_margin"
          >
            <Slider
              :min="0"
              :max="200"
              :defaultValue="curFormData.bottom_margin"
              @change="(e) => onChange(e, 'bottom_margin')"
            />
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">背景色</div>
          <FormItem
            name="kefu_color"
          >
            <div class="flex_row_start_center fzx_color_show">
              <ColorPicker
                :color="curFormData.color"
                @update:color="(e) => onChange('rgba(' + e.r + ',' + e.g + ',' + e.b + ',' + e.a + ')', 'color')"
                :hideReset="true"
              ></ColorPicker>
              
            </div>
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">客服文本</div>
          <FormItem
            name="kefu_text"
          >
            <Input
              :maxLength="10"
              placeholder="请输入客服文本，最多10个字"
              :value="curFormData.text"
              @change="(e) => onChange(e.target.value, 'text')"
              style="width: 300px"
            />
          </FormItem>
        </div>
        <div class="sub_part" :style="{ borderBottomWidth: 0 }">
          <div class="subtitle">客服电话</div>
          <FormItem
            name="kefu_tel"
          >
            <Input
              :maxLength="13"
              placeholder="请输入客服电话"
              :value="curFormData.tel"
              @change="(e) => onChange(e.target.value, 'tel')"
              style="width: 300px"
            />
          </FormItem>
        </div>
      </template>
      <!-- 富文本组件 -->
      <template v-else-if="curFormData.type == 'fuwenben'">
        <div class="sub_part">
          <div class="subtitle">上边距</div>
          <FormItem
            name="fuwenben_top_margin"
          >
            <Slider
              :min="0"
              :max="200"
              :defaultValue="curFormData.top_margin"
              @change="(e) => onChange(e, 'top_margin')"
            />
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">下边距</div>
          <FormItem
            name="fuwenben_bottom_margin"
          >
            <Slider
              :min="0"
              :max="200"
              :defaultValue="curFormData.bottom_margin"
              @change="(e) => onChange(e, 'bottom_margin')"
            />
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">背景色</div>
          <FormItem
            name="fuwenben_color"
          >
            <div class="flex_row_start_center fzx_color_show">
              <ColorPicker
                :color="curFormData.color"
                @update:color="(e) => onChange('rgba(' + e.r + ',' + e.g + ',' + e.b + ',' + e.a + ')', 'color')"
                :hideReset="true"
              ></ColorPicker>
              
            </div>
          </FormItem>
        </div>
        <div
          class="goods_sku_tab"
          style=" display: flex; position: relative; flex: 1; margin-top: 20px;margin-bottom: 25px;"
        >
          <SldQuill
            :value="quillEscapeToHtml(data.text)"
            :toolbarOptions="toolbarOptions"
            @getRQContent="(con) => onChange(con, 'text')"
          />
        </div>
      </template>
      <!-- 搭配组件 -->
      <template v-else-if="curFormData.type == 'dapei'">
        <div class="sub_part">
          <div class="subtitle">上边距</div>
          <FormItem
            name="dapei_top_margin"
          >
            <Slider
              :min="0"
              :max="200"
              :defaultValue="curFormData.top_margin"
              @change="(e) => onChange(e, 'top_margin')"
            />
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">下边距</div>
          <FormItem
            name="dapei_bottom_margin"
          >
            <Slider
              :min="0"
              :max="200"
              :defaultValue="curFormData.bottom_margin"
              @change="(e) => onChange(e, 'bottom_margin')"
            />
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">背景色</div>
          <FormItem
            name="dapei_color"
          >
            <div class="flex_row_start_center fzx_color_show">
              <ColorPicker
                :color="curFormData.color"
                @update:color="(e) => onChange('rgba(' + e.r + ',' + e.g + ',' + e.b + ',' + e.a + ')', 'color')"
                :hideReset="true"
              ></ColorPicker>
              
            </div>
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">搭配图片</div>
          <FormItem
            name="dapei_img"
          >
            <div class="dape_img flex_com_column_start_start">
              <div v-if="curFormData.dapei_img !== ''" class="upload_img flex_com_row_center">
                <img :src="curFormData.dapei_img" />
                <div class="img_mask">
                  <span class="img_del" @click="delImg(curFormData, 'dapei_img')">
                    <AliSvgIcon
                      width="15px"
                      height="15px"
                      fillColor="#fff"
                      iconName="iconshanchu4"
                    />
                  </span>
                </div>
              </div>
              <Upload
                name="file"
                :withCredentials="true"
                accept=".gif, .jpeg, .png, .jpg"
                :showUploadList="false"
                :openFileDialogOnClick="false"
              >
                <div class="flex_row_center_center upload_btn" @click="openMaterial(curFormData, 'dapei_img')">
                  <AliSvgIcon
                    width="14px"
                    height="14px"
                    fillColor="#666"
                    iconName="iconshangsheng"
                    :extra="{ position: 'relative', top: 1, right: 2 }"
                  />
                  上传图片
                </div>
              </Upload>
              <span class="modal_tip_color">此处建议上传宽度为750，高度不限制的图片；支持格式gif，jpg，png。</span>
            </div>
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">图片标题</div>
          <FormItem
            name="dapei_title"
          >
            <Input
              :maxLength="15"
              placeholder="请输入图片标题"
              :value="curFormData.dapei_title"
              @change="(e) => onChange(e.target.value, 'dapei_title')"
              style="width: 300px"
            />
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">图片描述</div>
          <FormItem
            name="dapei_desc"
          >
            <Input
              :maxLength="24"
              placeholder="请输入图片描述"
              :value="curFormData.dapei_desc"
              @change="(e) => onChange(e.target.value, 'dapei_desc')"
              style="width: 300px"
            />
          </FormItem>
        </div>
        <div class="sub_part" style="border-bottom-width: 0">
          <div class="subtitle">选择商品</div>
          <FormItem
            name="dapei_goods"
          >
            <div class="selected_svideo flex_column_start_start">
              <div class="add_svideo_wrap flex_row_start_center">
                <span class="required">*</span>
                <span class="title">商品添加:</span>
                <a href="javascript:void(0)"
                   class="add_svideo_btn"
                   @click="selMoreGoods(curFormData)"
                >+ 添加</a>
                <span class="tip">最少添加3个，最多添加9个</span>
              </div>
              <template v-if="curFormData.data.info.length > 0">
                <div class="selected_svideo_list_title">
                  <span style="width: 50px">序号</span>
                  <span style="width: 176px">商品信息</span>
                  <span style="width: 80px">价格</span>
                  <span style="width: 50px">操作</span>
                </div>
                <div class="selected_svideo_list">
                  <div
                    v-for="(svideo_item, svideo_index) in curFormData.data.info"
                    :key="svideo_index"
                    class="selected_svideo_item flex_row_start_center"
                  >
                      <span style="width: 50px">{{ svideo_index + 1 }}</span>
                      <div class="svideo_info flex_row_start_center">
                        <div class="left flex_row_center_center">
                          <img :src="diy_type == 'spreader' ? svideo_item.goodsImage : svideo_item.mainImage"/>
                        </div>
                        <div class="right flex_column_start_start">
                          <span class="video_name">{{ svideo_item.goodsName }}</span>
                        </div>
                      </div>
                      <span style="width: 80px">{{ diy_type == 'spreader' ? svideo_item.productPrice * 1 : svideo_item.goodsPrice * 1 }}</span>
                      <div @click="delGoods(svideo_item.goodsId)"
                           class="operate flex_row_center_center">
                        <AliSvgIcon
                          width="18px"
                          height="18px"
                          fillColor="#2d2d2d"
                          iconName="iconshanchu5"
                        />
                      </div>
                    </div>
                </div>
              </template>
            </div>
          </FormItem>
        </div>
      </template>
       <!-- 推荐商品组件 -->
       <template v-else-if="curFormData.type == 'tuijianshangpin'">
        <div class="sub_part">
          <div class="subtitle">上边距</div>
          <FormItem
            name="tuijianshangpin_top_margin"
          >
            <Slider
              :min="0"
              :max="200"
              :defaultValue="curFormData.top_margin"
              @change="(e) => onChange(e, 'top_margin')"
            />
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">下边距</div>
          <FormItem
            name="tuijianshangpin_bottom_margin"
          >
            <Slider
              :min="0"
              :max="200"
              :defaultValue="curFormData.bottom_margin"
              @change="(e) => onChange(e, 'bottom_margin')"
            />
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">背景色</div>
          <FormItem
            name="tuijianshangpin_color"
          >
            <div class="flex_row_start_center fzx_color_show">
              <ColorPicker
                :color="curFormData.color"
                @update:color="(e) => onChange('rgba(' + e.r + ',' + e.g + ',' + e.b + ',' + e.a + ')', 'color')"
                :hideReset="true"
              ></ColorPicker>
              
            </div>
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">商品销量</div>
          <FormItem
            name="tjsp_isshow_sales"
          >
            <Radio.Group
              :value="curFormData.isshow_sales"
              @change="(e) => onChange(e.target.value, 'isshow_sales', undefined, undefined)"
            >
              <Radio :value="1">展示</Radio>
              <Radio :value="0">隐藏</Radio>
            </Radio.Group>
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">展示类型</div>
          <FormItem
            name="tjsp_show_style"
          >
            <Radio.Group
              :value="curFormData.show_style"
              @change="(e) => onChange(e.target.value, 'show_style', undefined, undefined)"
            >
              <Radio :value="'big'">大图</Radio>
              <Radio :value="'small'">一行两个</Radio>
              <Radio :value="'list'">列表</Radio>
            </Radio.Group>
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">商品边角</div>
          <FormItem
            name="tjsp_border_radius"
          >
            <Radio.Group
              :value="curFormData.border_radius"
              @change="(e) => onChange(e.target.value, 'border_radius', undefined, undefined)"
            >
              <Radio :value="10">圆角</Radio>
              <Radio :value="0">直角</Radio>
            </Radio.Group>
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">商品样式</div>
          <FormItem
            name="tjsp_border_style"
          >
            <Radio.Group
              :value="curFormData.border_style"
              @change="(e) => onChange(e.target.value, 'border_style', undefined, undefined)"
            >
              <Radio :value="'border_none'">无边白底</Radio>
              <Radio :value="'card-shadow'">卡片投影</Radio>
              <Radio :value="'border_eee'">描边白底</Radio>
              <Radio :value="'border_none_grey_bg'">无边灰底</Radio>
            </Radio.Group>
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">页面边距</div>
          <FormItem
            name="tjsp_page_margin"
          >
            <Slider
              :min="0"
              :max="30"
              :defaultValue="curFormData.page_margin"
              :value="curFormData.page_margin"
              @change="(e) => onChange(e, 'page_margin')"
            />
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">商品间距</div>
          <FormItem
            name="tjsp_goods_margin"
          >
            <Slider
              :min="0"
              :max="20"
              :defaultValue="curFormData.goods_margin"
              :value="curFormData.goods_margin"
              @change="(e) => onChange(e, 'goods_margin')"
            />
          </FormItem>
        </div>
        <div class="sub_part" v-if="diy_type != 'integral' && diy_type != 'spreader'">
          <div class="subtitle">加车图标</div>
          <div class="tjsp">
            <div class="add_svideo_wrap flex_row_start_center">
              <template v-for="(icon_item, icon_index) in cart_icon_data()" :key="icon_index">
                <div
                  class="cart_icon_wrap"
                  :class="curFormData.cart_icon_type == icon_item.type ? 'current' : null"
                  @click="onChange(icon_item.type, 'cart_icon_type', icon_index)"
                  :style="{ padding: icon_item.padding + 'px' }"
                >
                  <AliSvgIcon
                    :width="icon_item.width + 'px'"
                    :height="icon_item.width + 'px'"
                    fillColor="#F10D3B"
                    :iconName="'icon' + icon_item.icon"
                  />
                </div>
              </template>
            </div>
          </div>
        </div>
        <div class="sub_part" style="border-bottom-width: 0">
          <div class="subtitle">选择商品</div>
          <FormItem
            name="dapei_goods"
          >
            <div class="selected_svideo flex_column_start_start">
              <div class="add_svideo_wrap flex_row_start_center">
                <span class="required">*</span>
                <span class="title">商品添加:</span>
                <a href="javascript:void(0)"
                   class="add_svideo_btn"
                   @click="selMoreGoods(curFormData)"
                >+ 添加</a>
                <span class="tip">最少添加1个</span>
              </div>
              <template v-if="curFormData.data.info.length > 0">
                <div class="selected_svideo_list_title">
                  <span style="width: 50px">序号</span>
                  <span :style="{ width: diy_type == 'integral' ? '126px' : '176px'}">商品信息</span>
                  <span :style="{ width: diy_type == 'integral' ? '130px' : '80px'}">价格</span>
                  <span style="width: 50px">操作</span>
                </div>
                <div class="selected_svideo_list">
                  <div
                    v-for="(svideo_item, svideo_index) in curFormData.data.info"
                    :key="svideo_index"
                    class="selected_svideo_item flex_row_start_center"
                  >
                      <span style="width: 50px">{{ svideo_index + 1 }}</span>
                      <div class="svideo_info flex_row_start_center" :style="{ width: diy_type == 'integral' ? '126px' : '176px'}">
                        <div class="left flex_row_center_center">
                          <img :src="diy_type == 'spreader' ? svideo_item.goodsImage : svideo_item.mainImage"/>
                        </div>
                        <div class="right flex_column_start_start">
                          <span class="video_name">{{ svideo_item.goodsName }}</span>
                        </div>
                      </div>
                      <span
                        :style="{
                          width: diy_type == 'integral' ? '130px' : '80px',
                          fontSize: diy_type == 'integral' ? '13px' : '14px',
                        }"
                      >{{ diy_type == 'integral' ? svideo_item.integralPrice + '积分+¥' + (svideo_item.cashPrice * 1)
                        : diy_type == 'spreader' ? (svideo_item.productPrice * 1) : (svideo_item.goodsPrice * 1) }}</span>
                      <div @click="delGoods(diy_type == 'integral' ? svideo_item.integralGoodsId : svideo_item.goodsId)"
                           class="operate flex_row_center_center">
                        <AliSvgIcon
                          width="18px"
                          height="18px"
                          fillColor="#2d2d2d"
                          iconName="iconshanchu5"
                        />
                      </div>
                    </div>
                </div>
              </template>
            </div>
          </FormItem>
        </div>
      </template>
      <!-- 图片组合组件 -->
      <template v-else-if="curFormData.type == 'tupianzuhe'">
        <div class="sub_part">
          <div class="subtitle">上边距</div>
          <FormItem
            name="tupianzuhe_top_margin"
          >
            <Slider
              :min="0"
              :max="200"
              :defaultValue="curFormData.top_margin"
              @change="(e) => onChange(e, 'top_margin')"
            />
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">下边距</div>
          <FormItem
            name="tupianzuhe_bottom_margin"
          >
            <Slider
              :min="0"
              :max="200"
              :defaultValue="curFormData.bottom_margin"
              @change="(e) => onChange(e, 'bottom_margin')"
            />
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">背景色</div>
          <FormItem
            name="tupianzuhe_color"
          >
            <div class="flex_row_start_center fzx_color_show">
              <ColorPicker
                :color="curFormData.color"
                @update:color="(e) => onChange('rgba(' + e.r + ',' + e.g + ',' + e.b + ',' + e.a + ')', 'color')"
                :hideReset="true"
              ></ColorPicker>
              
            </div>
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">展示风格</div>
          <FormItem
            name="svideo_show_style"
          >
            <div class="com_default_styles flex_com_row_start_center">
              <template v-for="(item) in sld_m_diy_tpzh_style" :key="item.sele_style">
                <div
                  class="svideo_show_style_item flex_com_row_center"
                  :class="curFormData.sele_style == item.sele_style ? 'sel_svideo_show_style' : ''"
                  @click="handleTuPianZuHeStyle(item.sele_style)"
                  >
                  <img :src="item.img" />
                </div>
              </template>
            </div>
          </FormItem>
        </div>
        <template v-if="curFormData.sele_style == 8">
          <template v-if="curFormData.data.length > 0">
            <template v-for="(item, index) in curFormData.data" :key="index">
              <div class="sub_part" style="border-bottom-width: 0">
                <div class="subtitle">{{ index % 2 == 0 ? '左' : '右' }}{{ index < 2 ? 1 : 2 }}模块数据</div>
                <div class="flex_column_start_start">
                  <FormItem
                    name="'tupianzuhe8_main' + index"
                  >
                    <Input
                      :maxLength="6"
                      placeholder="请输入主标题,最多6个字"
                      :value="item.main_title"
                      @change="(e) => onChange(e.target.value, 'main_title', index)"
                      style="width: 300px"
                    />
                  </FormItem>
                  <FormItem
                    name="'tupianzuhe8_sub' + index"
                  >
                    <Input
                      :maxLength="10"
                      placeholder="请输入子标题,最多10个字"
                      :value="item.sub_title"
                      @change="(e) => onChange(e.target.value, 'sub_title', index)"
                      style="width: 300px"
                    />
                  </FormItem>
                  <FormItem
                    name="'tupianzuhe8_url' + index"
                  >
                    <Select
                      placeholder="请选择链接类型"
                      :value="item.url_type"
                      @select="(e) => sldHandSeleLink(e,index)"
                      style="width: 300px"
                    >
                      <template v-if="diy_type == 'integral'">
                        <Select.Option
                          v-for="(item_link, index_link) in m_diy_point_link_type()"
                          :key="index_link"
                          :value="item_link.key"
                        >{{ item_link.name }}</Select.Option>
                      </template>
                      <template v-else-if="diy_type == 'spreader'">
                        <Select.Option
                          v-for="(item_link, index_link) in m_diy_spreader_link_type()"
                          :key="index_link"
                          :value="item_link.key"
                        >{{ item_link.name }}</Select.Option>
                      </template>
                      <template v-else>
                        <Select.Option
                          v-for="(item_link, index_link) in m_diy_link_type()"
                          :key="index_link"
                          :value="item_link.key"
                        >{{ item_link.name }}</Select.Option>
                      </template>
                    </Select>
                  </FormItem>
                  <SldDiyItemSelectLinkHideLabel
                    :data="item"
                    :index="index"
                    @handleChange="onChange"
                  />
                </div>
                <FormItem
                  name="nav_img"
                >
                  <div className={global.flex_com_column_start_start}>
                    <template v-if="item.img.length > 0">
                      <template v-for="(child, child_index) in item.img">
                        <TPZH8Imgs
                          :data="child"
                          :index="child_index"
                          :parent_index="index"
                          upload_img_tip="宽154*高188"
                          :list="curFormData"
                          @handleChange="onChange"
                          @handSeleLink="sldHandSeleLink"
                          :label_width="232"
                          :label_top="10"
                          :label_bottom="0"
                        />
                      </template>
                    </template>
                  </div>
                </FormItem>
              </div>
            </template>
          </template>
        </template>
        <div v-else class="sub_part" style="border-bottom-width: 0">
          <div class="subtitle">上传图片</div>
          <FormItem
            name="nav_img"
          >
            <div class="flex_com_column_start_start">
              <template v-if="curFormData.data.length > 0">
                <template v-for="(item, index) in curFormData.data">
                  <SldDiyItemImgLinkGroups
                    type="tupianzuhe"
                    :data="item"
                    :list="curFormData"
                    :index="index"
                    :label_width="232"
                    :label_top="10"
                    :label_bottom="0"
                    :diy_type="diy_type"
                    @handleCurSelData="handleCurSelData"
                    @handSeleLink="sldHandSeleLink"
                    @handleChange="onChange"
                  />
                </template>
              </template>
            </div>
            <div
              v-if="curFormData.sele_style < 4"
              class="add_goods_wrap flex_row_center_center"
              @click="addNav(curFormData)"
            >+ 添加图片</div>
          </FormItem>
        </div>
      </template>
      <!-- 导航组件 -->
      <template v-else-if="curFormData.type == 'nav'">
        <div class="sub_part">
          <div class="subtitle">分屏没置</div>
          <FormItem
            name="nav_page_set"
          >
            <Radio.Group
              class="flex_column_start_start"
              :defaultValue="curFormData.page_set"
              :value="curFormData.page_set"
              @change="(e) => onChange(e.target.value, 'page_set')"
            >
              <Radio value="single">单屏固定展示</Radio>
              <Radio value="scroll" style="margin-top: 10px;">
                <div style="margin-bottom: 10px;">多屏横向滚动</div>
                <div v-if="curFormData.page_set == 'scroll'" class="flex_row_start_center">一屏显示<InputNumber
                  v-model:value="curFormData.page_num"
                  :min="1"
                  :max="100"
                  :precision="0"
                  @change="(e) => onChange(e, 'page_num')"
                  style="width: 80px !important;margin: 0 15px;" />个导航</div>
              </Radio>
            </Radio.Group>
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">上边距</div>
          <FormItem
            name="nav_top_margin"
          >
            <Slider
              :min="0"
              :max="200"
              :defaultValue="curFormData.top_margin"
              @change="(e) => onChange(e, 'top_margin')"
            />
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">下边距</div>
          <FormItem
            name="nav_bottom_margin"
          >
            <Slider
              :min="0"
              :max="200"
              :defaultValue="curFormData.bottom_margin"
              @change="(e) => onChange(e, 'bottom_margin')"
            />
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">背景色</div>
          <FormItem
            name="nav_color"
          >
            <div class="flex_row_start_center fzx_color_show">
              <ColorPicker
                :color="curFormData.color"
                @update:color="(e) => onChange('rgba(' + e.r + ',' + e.g + ',' + e.b + ',' + e.a + ')', 'color')"
                :hideReset="true"
              ></ColorPicker>
              
            </div>
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">显示风格</div>
          <FormItem
            name="nav_style_set"
          >
            <Radio.Group
              :defaultValue="curFormData.style_set"
              :value="curFormData.style_set"
              @change="(e) => onChange(e.target.value, 'style_set')"
            >
              <Radio value="nav">导航</Radio>
              <Radio value="tag-nav">分组</Radio>
            </Radio.Group>
          </FormItem>
        </div>
        <div v-if="curFormData.style_set == 'nav'" class="sub_part">
          <div class="subtitle">显示图标</div>
          <FormItem
            name="nav_icon_set"
          >
            <Radio.Group
              :defaultValue="curFormData.icon_set"
              :value="curFormData.icon_set"
              @change="(e) => onChange(e.target.value, 'icon_set')"
            >
              <Radio value="up">图标居上</Radio>
              <Radio value="left">图标居左</Radio>
              <Radio value="no-icon">不显示图标</Radio>
            </Radio.Group>
          </FormItem>
        </div>
        <div v-if="curFormData.style_set == 'nav'" class="sub_part">
          <div class="subtitle">图标大小</div>
          <FormItem
            name="slide"
          >
            <Slider
              :min="30"
              :max="80"
              :defaultValue="curFormData.slide"
              :value="curFormData.slide"
              @change="(e) => onChange(e, 'slide')"
            />
          </FormItem>
        </div>
        <div class="sub_part" :style="{ borderBottomWidth: 0 }">
          <div class="subtitle">导航图片</div>
          <FormItem
            name="nav_img"
          >
            <div class="flex_com_column_start_start">
              <template v-if="curFormData.data.length > 0">
                <template
                  v-for="(item, index) in curFormData.data"
                  :key="item.type + '_' + item.id + '_img' + index">
                  <SldDiyItemImgLinkGroups
                    v-if="(curFormData.page_set == 'single' && index < 5) || curFormData.page_set == 'scroll'"
                    type="nav"
                    :data="item"
                    :list="curFormData"
                    :index="index"
                    :label_width="232"
                    :label_top="10"
                    :label_bottom="0"
                    :diy_type="diy_type"
                    @handleChange="onChange"
                    @handSeleLink="sldHandSeleLink"
                  />
                </template>
              </template>
              <div class="add_goods_wrap flex_row_center_center"
                   @click="addNav(curFormData)">+ 添加导航</div>
            </div>
          </FormItem>
        </div>
      </template>
      <!-- 轮播图组件 -->
      <template v-else-if="curFormData.type == 'lunbo'">
        <div class="sub_part">
          <div class="subtitle">上边距</div>
          <FormItem
            name="lunbo_top_margin"
          >
            <Slider
              :min="0"
              :max="200"
              :defaultValue="curFormData.top_margin"
              @change="(e) => onChange(e, 'top_margin')"
            />
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">下边距</div>
          <FormItem
            name="lunbo_bottom_margin"
          >
            <Slider
              :min="0"
              :max="200"
              :defaultValue="curFormData.bottom_margin"
              @change="(e) => onChange(e, 'bottom_margin')"
            />
          </FormItem>
        </div>
        <div class="sub_part">
          <div class="subtitle">背景色</div>
          <FormItem
            name="lunbo_color"
          >
            <div class="flex_row_start_center fzx_color_show">
              <ColorPicker
                :color="curFormData.color"
                @update:color="(e) => onChange('rgba(' + e.r + ',' + e.g + ',' + e.b + ',' + e.a + ')', 'color')"
                :hideReset="true"
              ></ColorPicker>
              
            </div>
          </FormItem>
        </div>
        <div class="sub_part" style="border-bottom-width: 0">
          <div class="subtitle">轮播图片</div>
          <FormItem
            name="lunbo_img"
            style="width: 100%"
          >
            <div class="flex_com_column_start_start">
              <template v-if="curFormData.data.length > 0">
                <template v-for="(item, index) in curFormData.data" :key="index">
                  <SldDiyItemImgLinkGroups
                    type="lunbo"
                    :data="item"
                    :list="curFormData"
                    :index="index"
                    :label_width="232"
                    :label_top="10"
                    :label_bottom="0"
                    upload_img_tip="宽710,高不限制"
                    :diy_type="diy_type"
                    @handleCurSelData="handleCurSelData"
                    @handSeleLink="sldHandSeleLink"
                    @handleChange="onChange"
                  />
                </template>
              </template>
              <div class="add_goods_wrap flex_row_center_center"
                   @click="addLunbo(curFormData)"
              >+ 添加图片</div>
            </div>
          </FormItem>
        </div>
      </template>
    </Form>
  </div>

  <!-- 添加直播 - start -->
  <SldSelMoreLeftRightLive
    :modalVisible="modalVisibleLive"
    :width="1000"
    :height="height - 400"
    :title="sle_more_title"
    :selectedRow="sele_more_live.info"
    :selectedRowKey="sele_more_live.ids"
    :extra="sele_more_live"
    @confirm-event="(rows, rowkeys) => handleConfirm(rows, rowkeys, curFormData.type)"
    @cancle-event="handleCancle(curFormData.type)"
  />
  <!-- 添加直播 - end -->

  <!-- 添加短视频 - start -->
  <SldSelMoreLeftRightSvideo
    :modalVisible="modalVisibleSvideo"
    :width="1000"
    :height="height - 400"
    :title="sle_more_title"
    :selectedRow="sele_more_svideo.info"
    :selectedRowKey="sele_more_svideo.ids"
    :extra="sele_more_svideo"
    @confirm-event="(rows, rowkeys) => handleConfirm(rows, rowkeys, curFormData.type)"
    @cancle-event="handleCancle(curFormData.type)"
    :decorateM="true"
  />
  <!-- 添加短视频 - end -->

  <!-- 商品多选的modal框-start -->
  <SldSelMoreLeftRightGoods
    :width="1000"
    :height="height - 400"
    :title="sle_more_title"
    :formValue="formValue"
    client='mobile'
    :modalVisible="modalVisibleGoods"
    :extra="sele_more_goods"
    :selectedRow="sele_more_goods.info"
    :selectedRowKey="sele_more_goods.ids"
    :diy_type="diy_type"
    @confirm-event="(rows, rowkeys) => handleConfirm(rows, rowkeys, curFormData.type)"
    @cancle-event="handleCancle(curFormData.type)"
  ></SldSelMoreLeftRightGoods>
  <!-- 商品多选的modal框-end -->

  <!-- 链接选择弹窗 - start -->
  <SldSelGoodsSingleDiy
    client="mobile"
    :link_type="link_type"
    :modalVisible="modalVisibleSelLink"
    :diy_type="diy_type"
    @confirm-event="seleSku"
    @cancle-event="sldHandleLinkCancle"
  />
  <!-- 链接选择弹窗 - end -->

  <!-- 图片素材选择 start -->
  <SldMaterialImgs
    ref="sldMaterialImgsRef"
    :visibleModal="chooseFile == 1 ? true : false"
    :maxUploadNum="1"
    :allowRepeat="false"
    :selectedData="selectImageData"
    @closeMaterial="() => closeMaterial()"
    @confirmMaterial="(val) => confirmMaterial(val)"
  ></SldMaterialImgs>
  <!-- 图片素材选择 end -->
</template>
<script setup>
  import { reactive, computed, watch, ref } from 'vue';
  import {
    Form,
    FormItem,
    RadioGroup,
    Radio,
    Input,
    Slider,
    Select,
    Upload,
    Button,
    InputNumber,
  } from 'ant-design-vue';
  import Icon from '@/components/Icon/Icon.vue';
  import {
    failTip,
    sld_m_diy_live_style,
    sld_m_diy_svideo_style,
    sld_m_diy_notice_style,
    m_diy_link_type,
    m_diy_point_link_type,
    m_diy_spreader_link_type,
    sld_m_diy_tpzh_style,
    cart_icon_data,
    sld_com_empty_arrar_2,
    sld_com_empty_arrar_3,
    sld_com_empty_arrar_4,
    sld_com_empty_arrar_5,
    quillEscapeToHtml,
  } from '@/utils/utils';
  import ColorPicker from '/@/components/ColorPicker/index.vue';
  import SldSelMoreLeftRightLive from '@/components/SldSelMoreLeftRightLive/index.vue';
  import SldSelMoreLeftRightSvideo from '@/components/SldSelMoreLeftRightSvideo/index.vue';
  import SldDiyItemSelectLink from '@/components/SldDiyItemEdit/index.vue';
  import SldDiyItemSelectLinkHideLabel from '@/components/SldDiyItemEdit/indexHideLabel.vue';
  import SldDiyItemImgLinkGroups from '@/components/SldDiyItemEdit/imgLinkGroups.vue';
  import SldSelGoodsSingleDiy from '@/components/SldSelGoodsSingleDiy/index.vue';
  import SldMaterialImgs from '@/components/SldMaterialFiles/sldMaterialImgs.vue';
  import SldSelMoreLeftRightGoods from '/@/components/SldSelMoreLeftRightGoods/index.vue';
  import MoreTabSeleData from './more_tab_sele_data.vue';
  import BottomNavItem from './bottom_nav_item.vue';
  import TPZH8Imgs from './tupianzuhe_8_imgs.vue';
  import SldQuill from '@/components/SldQuill/index.vue';

  //设置富文本可以有的内容
  const toolbarOptions = {
    placeholder: '',
    modules: {
      toolbar: [
        ['bold', 'italic', 'underline', 'strike'],                      // 加粗 斜体 下划线 删除线
        [{ 'header': 1 }, { 'header': 2 }],                             // 1、2 级标题
        [{ 'list': 'ordered' }, { 'list': 'bullet' }],                  // 有序、无序列表
        [{ 'script': 'sub' }, { 'script': 'super' }],                   // 上标/下标
        [{ 'indent': '-1' }, { 'indent': '+1' }],                       // 缩进
        [{ 'direction': 'rtl' }],                                       // 文本方向
        [{ 'size': ['small', false, 'large', 'huge'] }],                // 字体大小
        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],                      // 标题
        [                                                               // 字体颜色、字体背景颜色
          { 'color': ['#000', '#e60000', '#ff9900', '#ffff00', '#008a00', '#0066cc', '#9933ff', '#fff', '#facccc', '#ffebcc', '#ffffcc', '#cce8cc', '#cce0f5', '#ebd6ff', '#bbbbbb', '#f06666', '#ffc266', '#ffff66', '#66b966', '#66a3e0', '#c285ff', '#888888', '#a10000', '#b26b00', '#b2b200', '#006100', '#0047b2', '#6b24b2', '#444444', '#5c0000', '#663d00', '#666600', '#003700', '#002966', '#3d1466'] },
          { 'background': [] }
        ],
        [{ 'font': [] }],                                                // 字体种类
        [{ 'align': [] }],                                               // 对齐方式
        ['clean'],                                                       // 清除文本格式
      ]
    }
  };

  const props = defineProps({
    data: {
      type: Object,
      default: {
        title: '',
        show_style: '',
        border_radius: 8,
        data: {
          ids: [],
          info: [],
        },
      }
    },
    diy_type: { //装修来源类型: home-首页手机装修 topic-专题装修 integral-积分商城装修 spreader-推手装修
      type: String,
      default: 'home',
    }
  })
  let curFormData = reactive({
    type: '',
    show_style: '',
    sele_style: 0,
    data: [],
  });
  const formData = computed(() => {
    return props.data;
  });

  const formValue = ref({})
  let sele_more_live = reactive({
    info: [],//选择的直播数组
    ids: [],//选择的直播id数组
    min_num: undefined,//最小数量，0为不限制
    total_num: undefined,//选择总数
    max_num: undefined,//最多选择30个
  });
  let sele_more_svideo = reactive({
    info: [],//选择的短视频数组
    ids: [],//选择的短视频id数组
    min_num: undefined,//最小数量，0为不限制
    max_num: undefined,//最多选择30个
    total_num: null,//选择总数
  });
  let sele_more_goods = reactive({
    info: [],//选择的商品数组
    ids: [],//选择的商品id数组
    min_num: undefined,//最小数量，0为不限制
    max_num: undefined,//最多选择30个
    total_num: null,//选择总数
  });
  const height = ref(document.body.clientHeight);
  const sle_more_title = ref(''); //多选组件title
  const modalVisibleLive = ref(false); //是否展示直播多选modal框，默认不显示
  const modalVisibleSvideo = ref(false); //是否展示短视频多选modal框，默认不显示
  const selectedRows = ref([]);
  const selectedRowKeys = ref([]);
  const link_type = ref(''); //链接选择的类型，用于选择商品/分类/专题
  const modalVisibleSelLink = ref(false); //是否展示选择链接弹窗
  const cur_index = ref(-1);//当前操作数据的序号
  const parent_index = ref(-1);//当前操作数据的父级的序号
  const chooseFile = ref(0); //上传素材弹窗显示 1:图片 2:视频
  let selectImageData = reactive({ ids:[], data:[] }); //产品图片数据
  
  const activityType = ref(''); //促销互动类型
  const modalVisibleGoods = ref(false); //是否展示商品多选modal框，默认不显示
  const modalVisibleActivityGoods = ref(false); //是否展示促销商品多选modal框，默认不显示
  const oprate_more_tab_index = ref(0);//TAB切换当前操作的数据index

  const emit = defineEmits(['handleCurSelData']);

  watch(
    formData,
    () => {
      curFormData = props.data;
    },
    { deep: true, immediate: true },
  );

  function handleCurSelData(data) {
    emit('handleCurSelData', data);
  };

  //添加顶部分类导航轮播
  function addTopCatNav(data) {
    if (data.data.length == 8) {
      failTip('最多支持8张轮播图');
      return false;
    }
    data.data.push({
      img: '',//图片绝对地址
      img_path: '',//图片相对地址
      imgPath: '',//图片相对地址
      title: '',//图片标题
      url: '', //链接值
      url_type: '',//链接类型
      info: '',//用于存放额外信息
      width: '355',
      height: 140,
      bg_color: '#FC1C1C',//轮播背景色
      show_color_picker: false,//是否显示颜色选择器，默认不显示
      swiper_bg_style: 1,//轮播背景风格，1为纯色弧度，2为渐变
    });
    emit('handleCurSelData', data);
  };

  //直播组件-展示风格选择事件
  function handleLiveStyle(val) {
    curFormData.show_style = val;
    //清空选择的商品
    sele_more_live.ids = [];
    sele_more_live.info = [];
    curFormData.data.ids = [];
    curFormData.data.info = [];
    emit('handleCurSelData', curFormData);
  };

  //直播组件-添加事件
  function selMoreLive(data) {
    sele_more_live.info = data.data.info;
    sele_more_live.ids = data.data.ids;
    if (data.show_style == 'one' || data.show_style == 'five') {
      sele_more_live = {
        info: sele_more_live.info,
        ids: sele_more_live.ids,
        total_num: 2
      }
      sle_more_title.value = '选择直播(只能选择2个)';
    } else if (data.show_style == 'two') {
      sele_more_live = {
        info: sele_more_live.info,
        ids: sele_more_live.ids,
        min_num: 3,
        max_num: 15,
      };
      sle_more_title.value = '选择直播(最少选择3个，最多选择15个)';
    }
    modalVisibleLive.value = true;
  };

  //直播组件-删除事件
  function delLive(liveId, tar_index = 0) {
    let curFormData = { ...props.data };
    if (curFormData.type == 'more_tab') {
      let tar_data = curFormData.data.filter((item, index) => index == tar_index)[0];
      tar_data.ids = tar_data.ids.filter(item => item != liveId);
      tar_data.info = tar_data.info.filter(item => item.liveId != liveId);
    } else {
      curFormData.data.ids = curFormData.data.ids.filter(item => item != liveId);
      curFormData.data.info = curFormData.data.info.filter(item => item.liveId != liveId);
    }
    emit('handleCurSelData', data);
  };

  //短视频组件-展示风格选择事件
  function handleVideoStyle(val) {
    curFormData.show_style = val;
    //清空选择的商品
    sele_more_svideo.ids = [];
    sele_more_svideo.info = [];
    curFormData.data.ids = [];
    curFormData.data.info = [];
    emit('handleCurSelData', curFormData);
  };

  //短视频组件-添加事件
  function selMoreSvideo(data) {
    sele_more_svideo.info = data.data.info;
    sele_more_svideo.ids = data.data.ids;
    if (data.show_style == 'four' || data.show_style == 'five') {
      sele_more_svideo = {
        info: sele_more_svideo.info,
        ids: sele_more_svideo.ids,
        min_num: 3,//最小数量，0为不限制
        max_num: 15,//最多选择15个
      };
      sle_more_title.value = '选择短视频(最少选择3个，最多选择15个)';
    } else if (data.show_style == 'two') {
      sele_more_svideo = {
        info: sele_more_svideo.info,
        ids: sele_more_svideo.ids,
        total_num: 3,//只能选择3个
      };
      sle_more_title.value = '选择短视频(只能选择3个)';
    } else {
      sele_more_svideo = {
        info: sele_more_svideo.info,
        ids: sele_more_svideo.ids,
        total_num: 2,//只能选择2个
      };
      sle_more_title.value = '选择短视频(只能选择2个)';
    }
    modalVisibleSvideo.value = true;
  };

  //短视频组件-删除事件
  function delSvideo(svideoId, tar_index = 0) {
    let curFormData = { ...props.data };
    if (curFormData.type == 'more_tab') {
      let tar_data = curFormData.data.filter((item, index) => index == tar_index)[0];
      tar_data.ids = tar_data.ids.filter(item => item != svideoId);
      tar_data.info = tar_data.info.filter(item => item.videoId != svideoId);
    } else {
      curFormData.data.ids = curFormData.data.ids.filter(item => item != svideoId);
      curFormData.data.info = curFormData.data.info.filter(item => item.videoId != svideoId);
    }
    emit('handleCurSelData', curFormData);
  };

  //添加TAB切换分类导航，最多15个
  function addMoreTabNav(data) {
    if (data.data.length >= 15) {
      failTip('最多添加15个tab导航');
    } else {
      if (data.data.length == 0) {
        data.nav_current = 0;
      }
      data.data.push({
        title: '',//分类标题
        sub_title: '',//子标题
        data_type: 'goods',//商品类型
        cart_icon_type: 1,//商品的话显示的购物车图标
        ids: [],//数据id集合
        info: [],//数据信息
      });
      emit('handleCurSelData', data);
    }
  };

  //删除图片item,针对于多条数据的处理（用于轮播/导航/图片组合）
  function delSldComImg(tar_index) {
    let data = props.data.data.filter((item, index) => index != tar_index);
    if (data) {
      props.data.data = data;
      emit('handleCurSelData', props.data);
    }
  };

  //TAB切换选择数据功能
  function selMoreTabData(data, tar_index) {
    sle_more_title.value = '选择商品(最少选择2个)';
    let tar_data = data.data.filter((item, index) => index == tar_index)[0];
    if (tar_data.data_type == 'goods') {
      sele_more_goods = {
        info: tar_data.info,//选择的商品数组
        ids: tar_data.ids,//选择的商品id数组
        min_num: 2,//最少选择2个
      };
      modalVisibleGoods.value = true;
    } else if (tar_data.data_type == 'live') {
      sle_more_title.value = '选择直播(最少选择2个)';
      sele_more_live = {
        info: tar_data.info,//选择的直播数组
        ids: tar_data.ids,//选择的直播id数组
        min_num: 2,//最少选择2个
      };
      modalVisibleLive.value = true;
    } else if (tar_data.data_type == 'svideo') {
      sle_more_title.value = '选择短视频(最少选择2个)';
      sele_more_svideo = {
        info: tar_data.info,//选择的短视频数组
        ids: tar_data.ids,//选择的短视频id数组
        min_num: 2,//最少选择2个
      };
      modalVisibleSvideo.value = true;
    }
    oprate_more_tab_index.value = tar_index;//TAB切换当前操作的数据index
  };

  //公告展示风格选择事件
  function handleNoticeStyle(val) {
    curFormData.show_style = val;
    emit('handleCurSelData', curFormData);
  };

  //图片组合选择样式事件
  function handleTuPianZuHeStyle(val) {
    //图片组合里面如果是选择了图片的展现形式,需要重新组装数据
    let tmp_data = [];
    if (val == 0) {
      tmp_data = sld_com_empty_arrar_2;
    } else if (val == 1) {
      tmp_data = sld_com_empty_arrar_2;
    } else if (val == 2) {
      tmp_data = sld_com_empty_arrar_2;
    } else if (val == 3) {
      tmp_data = sld_com_empty_arrar_3;
    } else if (val == 4) {
      tmp_data = sld_com_empty_arrar_3;
    } else if (val == 5) {
      tmp_data = sld_com_empty_arrar_4;
    } else if (val == 6) {
      tmp_data = sld_com_empty_arrar_4;
    } else if (val == 7) {
      tmp_data = sld_com_empty_arrar_5;
    } else if (val == 8) {
      tmp_data = sld_com_empty_arrar_4;
    }
    curFormData.data = [];
    if(val < 8){
      for (let i in tmp_data) {
        curFormData.data.push({
          img: '',
          img_path: '',
          imgPath: '',
          title: '',
          url: '',
          url_type: '',
          info: '',
          width: 750,
          height: 150,
        });
      }
    }else{
      for (let i in sld_com_empty_arrar_4) {
        let temp = []
        for(let j in sld_com_empty_arrar_2){
          temp.push({
            img: '',
            img_path: '',
            imgPath: '',
            name: '',
            url: '',
            url_type: '',
            info: '',
          });
        }
        curFormData.data.push({
          main_title: '',
          sub_title: '',
          url: '',
          url_type: '',
          info: '',
          img:temp
        });
      }
    }
    curFormData.sele_style = val;
    emit('handleCurSelData', curFormData);
  };
  
  /*
   * 链接选择器选择事件
   * tar_index为多条数据的角标，（用于轮播/导航/图片组合）
   * tab_parent_index为父级的index
   * */
  function sldHandSeleLink(val, tar_index = 0, tab_parent_index = -1) {
    let flag = false; //后续是否展开选择弹窗
    let data = {};
    if (curFormData.type == 'lunbo' || curFormData.type == 'nav' || curFormData.type == 'tupianzuhe') {
      if(tab_parent_index>-1){
        if(curFormData.type == 'tupianzuhe'&&curFormData.sele_style == 8){
          data = curFormData.data[tab_parent_index].img.filter((item, index) => index == tar_index)[0];
        }
      }else{
        data = curFormData.data.filter((item, index) => index == tar_index)[0];
      }
    }
    if (val == '') { //无操作
      data.url = '';//公告链接
      data.url_type = '';//公告链接类型
      data.info = '';//用于存放额外信息
    } else if (val == 'url') { //链接地址
      data.url = '';//公告链接
      data.url_type = 'url';//公告链接类型
      data.info = '';//用于存放额外信息
    } else if (val == 'keyword') { //关键词
      data.url = '';//关键词
      data.url_type = 'keyword';//关键词类型
      data.info = '';//用于存放额外信息
    } else if (val == 'goods') { //商品
      flag = true;
      data.url = '';//商品gid
      data.url_type = 'goods';//商品类型
      data.info = '';//用于存放额外信息
    } else if (val == 'category') { //分类
      flag = true;
      data.url = '';//分类id
      data.url_type = 'category';//分类类型
      data.info = '';//用于存放额外信息
    } else if (val == 'topic') { //专题
      flag = true;
      data.url = '';//专题id
      data.url_type = 'topic';//专题类型
      data.info = '';//用于存放额外信息
    } else if (val == 'seckill') { //秒杀
      flag = true;
      data.url = '';//秒杀id
      data.url_type = 'seckill';//秒杀类型
      data.info = '';//用于存放额外信息
    } else if (val == 'store') { //店铺
      flag = true;
      data.url = '';//店铺id
      data.url_type = 'store';//店铺类型
      data.info = '';//用于存放额外信息
    } else if (val == 'voucher') { //优惠券
      data.url = '';//优惠券id
      data.url_type = 'voucher';//优惠券类型
      data.info = '';//用于存放额外信息
    } else if (val == 'live') { //直播
      data.url = '';//直播id
      data.url_type = 'live';//直播类型
      data.info = '';//用于存放额外信息
    } else if (val == 'svideo') { //短视频
      data.url = '';//短视频id
      data.url_type = 'svideo';//短视频类型
      data.info = '';//用于存放额外信息
    } else if (val == 'draw') { //抽奖活动
      flag = true;
      data.url = '';//抽奖活动id
      data.url_type = 'draw';//抽奖活动
      data.info = '';//用于存放额外信息
    } else { //签到、店铺街、领券中心、推手系统、O2O、短视频、直播、积分商城、【促销】团购、【促销】限时折扣、【促销】拼团、【促销】预售、【促销】阶梯团
      data.url = '';
      data.url_type = val;//链接类型
      data.info = '';//用于存放额外信息
    }
    cur_index.value = tar_index;//当前操作数据的序号
    parent_index.value = tab_parent_index;//当前操作数据的父级的序号
    link_type.value = val;
    modalVisibleSelLink.value = flag;
    if (curFormData.type == 'lunbo' || curFormData.type == 'nav' || curFormData.type == 'tupianzuhe') {
      //轮播导航
      emit('handleCurSelData', curFormData);
    } else {
      //目前用于搭配
      emit('handleCurSelData', data);
    }
  };

  //商品或分类选中事件
  function seleSku(val) {
    let data = {};
    if (curFormData.type == 'lunbo' || curFormData.type == 'nav' || curFormData.type == 'tupianzuhe') {
      if(parent_index.value !== undefined && parent_index.value > -1){
        data = curFormData.data[parent_index.value].img.filter((item, index) => index == cur_index.value)[0];
      }else{
        data = curFormData.data.filter((item, index) => index == cur_index.value)[0];
      }
    } else {
      data = curFormData;
    }
    if (data.url_type == 'goods') {
      data.url = val.goodsId;
      data.info = val;
    } else if (data.url_type == 'category') {
      data.url = val.categoryId != undefined ? val.categoryId : val.labelId;
      data.info = val;
    } else if (data.url_type == 'topic') {
      data.url = val.id;
      data.info = val;
    } else if (data.url_type == 'seckill') {
      data.url = val.seckillId;
      data.info = val;
    } else if (data.url_type == 'store') {
      data.url = val.storeId;
      data.info = val;
    } else if (data.url_type == 'voucher') {
      data.url = val.id;
      data.info = val;
    } else if (data.url_type == 'live') {
      data.url = val.liveId;
      data.info = val;
    } else if (data.url_type == 'svideo') {
      data.url = val.videoId;
      data.info = val;
    } else if (data.url_type == 'draw') {
      data.url = val.drawId;
      data.info = val;
    }
    if (curFormData.type == 'lunbo' || curFormData.type == 'nav') {
      emit('handleCurSelData', curFormData);
    } else {
      emit('handleCurSelData', data);
    }
    link_type.value = '';
    modalVisibleSelLink.value = false;
  };

  //选择商品或者分类取消事件
  function sldHandleLinkCancle() {
    let data = { ...curFormData };
    if (data.type == 'lunbo' || data.type == 'nav') {
      //轮播
      let tar_data = data.data.filter((item, index) => index == cur_index.value)[0];
      tar_data.url = '';
      tar_data.url_type = '';
      tar_data.info = '';
    } else {
      if (data.data && data.data.length > 0) {
        let tar_data = data.data.filter((item, index) => index == cur_index.value)[0];
        tar_data.url = '';
        tar_data.url_type = '';
        tar_data.info = '';
      }
      data.url = '';//公告链接
      data.url_type = '';//公告链接类型
      data.info = '';//用于存放额外信息
    }
    emit('handleCurSelData', data);
    link_type.value = '';
    modalVisibleSelLink.value = false;
  };

  //添加导航item
  function addNav(data) {
    if (data.type == 'nav' && data.page_set == 'single' && data.data.length >= 5) {
      failTip('最多添加5个导航，否则页面展示效果不佳');
      return;
    } else if (data.type == 'tupianzuhe' && data.data.length >= 30) {
      failTip('最多添加30张图片');
      return;
    } else {
      data.data.push({
        img: '',//图片绝对地址
        img_path: '',//图片相对地址
        imgPath: '',//图片相对地址
        name: '',//导航名称
        url: '', //链接值
        url_type: '',//链接类型
        info: '',//用于存放额外信息
        width: 750,
        height: 150,
      });
      emit('handleCurSelData', data);
    }
  };

  //添加轮播图片
  function addLunbo(data) {
    data.data.push({
      img: '',//图片绝对地址
      img_path: '',//图片相对地址
      imgPath: '',//图片相对地址
      title: '',//图片标题
      url: '', //链接值
      url_type: '',//链接类型
      info: '',//用于存放额外信息
      width: 750,
      height: 285.5,
    });
    emit('handleCurSelData', data);
  };

  //删除搭配图片
  function delImg() {
    let data = { ...curFormData };
    data.dapei_img = '';
    data.img_path = '';
    data.imgPath = '';
    emit('handleCurSelData', data);
  };
  
  //删除单个商品
  function delGoods(goodsId, tar_index = 0) {
    let key = props.diy_type == 'integral' ? 'integralGoodsId' : 'goodsId';
    let data = { ...curFormData };
    if (data.type == 'dapei' || data.type == 'tuijianshangpin' || data.type == 'activity') {
      data.data.ids = data.data.ids.filter(item => item != goodsId);
      data.data.info = data.data.info.filter(item => item[key] != goodsId);
    } else if (data.type == 'more_tab') {
      let tar_data = data.data.filter((item, index) => index == tar_index)[0];
      tar_data.ids = tar_data.ids.filter(item => item != goodsId);
      tar_data.info = tar_data.info.filter(item => item[key] != goodsId);
    }
    emit('handleCurSelData', data);
  };

  //选择商品事件-多选
  function selMoreGoods(data) {
    formValue.value = {}
    if (data.type == 'dapei') {
      //搭配
      sele_more_goods = {
        info: data.data.info,
        ids: data.data.ids,
        min_num: 3,
        max_num: 9,
      };
      sle_more_title.value = '选择商品(最少选择3个，最多选择9个)';
      modalVisibleGoods.value = true;
    } else if (data.type == 'tuijianshangpin') {
      //商品推荐
      sele_more_goods = {
        info: data.data.info,
        ids: data.data.ids,
        min_num: 1,
      };
      formValue.value = {state:3} 
      sle_more_title.value = '选择商品(最少选择1个)';
      modalVisibleGoods.value = true;
    } else if (data.type == 'activity') {
      if (data.show_style == 'group_buy') {
        //团购
        sele_more_goods = {
          info: data.data.info,
          ids: data.data.ids,
          total_num: 2,//只能选择2个
        };
        sle_more_title.value = '选择团购商品(只能选择2个)';
      } else {
        //拼团、限时折扣
        sele_more_goods = {
          info: data.data.info,
          ids: data.data.ids,
          min_num: 3,
          max_num: 15,
        };
        sle_more_title.value = '选择' + (data.show_style == 'pin' ? '拼团' : '限时折扣') + '商品(最少选择3个，最多选择15个)';
      }
      activityType.value = data.show_style;
      modalVisibleActivityGoods.value = true;
    }
  };

  //多选-回调事件
  const handleConfirm = (selectedRowsP, selectedRowKeysP, type) => {
    let data = { ...curFormData };
    sele_more_goods.ids = [...selectedRowKeysP];
    sele_more_goods.info = JSON.parse(JSON.stringify(selectedRowsP));
    if (data.type == 'svideo' || data.type == 'live' || type == 'dapei' || type == 'activity' || type == 'tuijianshangpin') {
      data.data.ids = JSON.parse(JSON.stringify(selectedRowKeysP));
      data.data.info = JSON.parse(JSON.stringify(selectedRowsP));
    } else if (type == 'more_tab') {
      let tar_data = data.data.filter((item, index) => index == oprate_more_tab_index.value)[0];
      tar_data.ids = JSON.parse(JSON.stringify(selectedRowKeysP));
      tar_data.info = JSON.parse(JSON.stringify(selectedRowsP));
    }
    emit('handleCurSelData', curFormData);
    selectedRows.value = selectedRowsP;
    selectedRowKeys.value = selectedRowKeysP;
    handleCancle(type);
  };

  //多选-关闭弹框
  const handleCancle = (type) => {
    modalVisibleLive.value = false;
    modalVisibleSvideo.value = false;
    modalVisibleGoods.value = false;
  };

  /*
   * 输入框内容更改事件
   * val 组件传回来的值
   * type 修改值对应的键名
   * index 多个数据的序号,主要用于轮播/导航/图片组合
   * parent_index 父级的index。默认为-1
   * */
  function onChange(val, type, tar_index = 0, parent_index = -1) {
    if ((curFormData.type == 'lunbo' && type != 'top_margin' && type != 'bottom_margin' && type != 'color')
     || (curFormData.type == 'tupianzuhe' && type != 'top_margin' && type != 'bottom_margin' && type != 'color')
     || (curFormData.type == 'nav' && (type != 'page_set' && type != 'icon_set' && type != 'style_set' && type != 'slide' && type != 'top_margin' && type != 'bottom_margin' && type != 'color'))
     || (curFormData.type == 'more_tab' && type != 'border_radius' && type != 'top_margin' && type != 'bottom_margin' && type != 'color')) {
      if (curFormData.type == 'more_tab') {
        if (type == 'data_type') {
          //TAB切换数据类型的时候需要清空商品信息
          saveCurSelData([], 'info', tar_index, 'more');
          saveCurSelData([], 'ids', tar_index, 'more');
        }
      }
      saveCurSelData(val, type, tar_index, 'more', parent_index);
      return false;
    }
    if (curFormData.type == 'lunbo' || curFormData.type == 'live' || curFormData.type == 'svideo' || curFormData.type == 'fzx' || curFormData.type == 'fzkb'
     || curFormData.type == 'kefu' || curFormData.type == 'gonggao' || curFormData.type == 'activity' || curFormData.type == 'fuwenben' || curFormData.type == 'dapei'
     || (curFormData.type == 'nav' && (type == 'page_set' || type == 'icon_set' || type == 'style_set' || type == 'slide' || type == 'top_margin' || type == 'bottom_margin' || type == 'color'))
     || curFormData.type == 'tuijianshangpin' || curFormData.type == 'tupianzuhe' || curFormData.type == 'more_tab') {
      //辅助线/辅助空白/客服/富文本/图片组合的展现样式
      saveCurSelData(val, type, tar_index, 'single');
    }
  };

  //修改内容，数据根节点的操作,parent_index为父级的index
  function saveCurSelData(val, type, tar_index = 0, flag, parent_index = -1) {
    if (flag == 'single') {
      //辅助线/辅助空白/富文本/客服操作, 导航的根节点样式
      curFormData[type] = val;
    } else if (flag == 'more') {
      //每个数组下面的操作
      let tar_data = [];
      if (parent_index > -1) {
        if (curFormData.type == 'tupianzuhe' && curFormData.sele_style == 8) {
          tar_data = curFormData.data[parent_index].img.filter((item, index) => index == tar_index)[0];
        }
      } else {
        tar_data = curFormData.data.filter((item, index) => index == tar_index)[0];
      }
      if (tar_data) {
        tar_data[type] = val;
      }
    } else {
      return;
    }
    emit('handleCurSelData', curFormData);
  };

  //打开上传素材文件
  function openMaterial(item, type) {
    let selectImageDataVal = { ids: [], data: [] };
    if (type == 'dapei_img' && item.dapei_img) {
      selectImageDataVal = {
        ids:[Number(item.dapei_img.split('?bindId=')[1])],
        data:[{
          bindId: item.dapei_img.split('?bindId=')[1],
          checked: true,
          filePath: item.dapei_img,
          fileUrl: item.img_path,
          fileType: 1,
        }]
      };
    } else if (type == 'img_path' && item.img_path) {
      selectImageDataVal = {
        ids:[Number(item.img_path.split('?bindId=')[1])],
        data:[{
          bindId: item.img_path.split('?bindId=')[1],
          checked: true,
          filePath: item.img_path,
          fileUrl: item.img,
          fileType: 1,
        }]
      };
    }
    chooseFile.value = 1;
    selectImageData = selectImageDataVal;
  };
    
  //关闭上传素材文件
  function closeMaterial() {
    chooseFile.value = 0;
  };
  
  //确认上传素材文件
  function confirmMaterial(val) {
    let data = { ...curFormData };
    if (data.type == 'dapei') {
      data.dapei_img = val.ids.length ? val.data[0].fileUrl : '';
      data.img_path = val.ids.length ? val.data[0].filePath : '';
      data.imgPath = val.ids.length ? val.data[0].filePath : '';
      data.width = val.ids.length ? val.data[0].width : '';
      data.height = val.ids.length ? val.data[0].height : '';
    } else {
      data.data.img = val.ids.length ? val.data[0].fileUrl : '';
      data.data.img_path = val.ids.length ? val.data[0].filePath : '';
      data.data.imgPath = val.ids.length ? val.data[0].filePath : '';
      data.data.width = val.ids.length ? val.data[0].width : '';
      data.data.height = val.ids.length ? val.data[0].height : '';
      data.width = val.ids.length ? val.data[0].width : '';
      data.height = val.ids.length ? val.data[0].height : '';
    }
    chooseFile.value = 0;
    selectImageData = val;
    emit('handleCurSelData', data);
  };
</script>
<style lang="less" scoped>
  .diy_item_edit {
    width: 400px;
    padding: 0 20px 20px;

    .sub_part {
      border-bottom: 1px solid #eee;

      .subtitle {
        color: #343434;
        font-size: 14px;
        line-height: 40px;
      }
    }

    .selected_svideo {
      width: 100%;
      border: 1px solid rgb(238 238 238 / 100%);
      border-radius: 3px;

      .add_svideo_wrap {
        width: 100%;
        height: 70px;
        background: #F8F8F8;
        font-size: 12px;

        .required {
          margin-left: 17px;
          color: #FC1C1C;
        }

        .title {
          color: #333;
        }

        .tip {
          color: #9A9A9A;
        }

        .add_svideo_btn {
          display: inline-block;
          width: 80px;
          height: 30px;
          margin: 0 10px;
          border-radius: 3px;
          background: #FC701E;
          color: #fff;
          line-height: 30px;
          text-align: center;
        }
      }

      .selected_svideo_list_title {
        width: 100%;
        height: 32px;
        border: 1px solid rgb(238 238 238 / 100%);

        span {
          display: inline-block;
          color: #333;
          font-size: 12px;
          line-height: 32px;
          text-align: center;
        }
      }

      .selected_svideo_list {
        .selected_svideo_item {
          position: relative;
          width: 358px;
          height: 60px;

          span {
            flex-shrink: 0;
            color: #2d2d2d;
            text-align: center;
          }

          .svideo_info {
            flex-shrink: 0;
            width: 176px;
            height: 60px;
            overflow: hidden;

            .left {
              position: relative;
              flex-shrink: 0;
              width: 50px;
              height: 50px;
              overflow: hidden;
              border-radius: 3px;

              img {
                max-width: 100%;
                max-height: 100%;
              }

              .play_icon {
                position: absolute;
                z-index: 2;
                top: 50%;
                left: 50%;
                width: 16px;
                height: 16px;
                margin-top: -8px;
                margin-left: -8px;
              }
            }

            .right {
              width: 126px;
              padding-left: 5px;
              color: #2d2d2d;

              .video_name {
                display: -webkit-box;
                width: 100%;
                height: 32px;
                margin-top: 2px;
                overflow: hidden;
                font-size: 12px;
                line-height: 16px;
                text-align: left;
                text-overflow: ellipsis;
                word-break: break-word;
                -webkit-line-clamp: 2;

                /*! autoprefixer: off */
                -webkit-box-orient: vertical;
              }

              .video_label {
                display: inline-block;
                width: 86%;
                height: 14px;
                margin-top: 6px;
                overflow: hidden;
                color: #666;
                line-height: 14px;
                text-align: left;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }

          .operate {
            flex-shrink: 0;
            width: 50px;
            height: 60px;
            text-align: center;
            cursor: pointer;
          }

          .operate svg:hover {
            fill: #77B9FE !important;
          }
        }

        .selected_svideo_item::after {
          content: ' ';
          position: absolute;
          z-index: 2;
          right: 14px;
          bottom: 0;
          left: 14px;
          height: 1px;
          background: #EEE;
        }

        .selected_svideo_item:last-child::after {
          height: 0 !important;
        }
      }
    }

    .selected_more_tab {
      position: relative;
      width: 100%;
      margin-bottom: 10px;
      border: 1px solid rgb(238 238 238 / 100%);
      border-radius: 3px;

      .del_sld_more_tab_item {
        position: absolute;
        z-index: 2;
        top: -8px;
        right: -8px;
        margin-left: 20px;
        cursor: pointer;
      }

      .add_svideo_wrap {
        width: 100%;
        background: #F8F8F8;
        font-size: 12px;

        .required {
          margin-left: 17px;
          color: #F10D3B;
        }

        .title {
          color: #333;
        }

        .tip {
          color: #9A9A9A;
        }

        .add_svideo_btn {
          display: inline-block;
          width: 80px;
          height: 30px;
          margin: 0 10px;
          border-radius: 3px;
          background: #FC701E;
          color: #fff;
          line-height: 30px;
          text-align: center;
        }
      }

      .cart_icon_wrap {
        margin-right: 10px;
        padding: 5px;
        border: 1px solid rgb(0 0 0 / 5%);
        border-radius: 3px;
        line-height: 0;
        cursor: pointer;
      }

      .cart_icon_wrap.current {
        border-color: #FC701E !important;
      }

      .selected_svideo_list_title {
        width: 100%;
        height: 32px;
        border: 1px solid rgb(238 238 238 / 100%);

        span {
          display: inline-block;
          color: #333;
          font-size: 12px;
          line-height: 32px;
          text-align: center;
        }
      }

      .selected_svideo_list {
        .selected_svideo_item {
          position: relative;
          width: 358px;
          height: 60px;

          span {
            flex-shrink: 0;
            color: #2d2d2d;
            font-size: 12px;
            text-align: center;
          }

          .svideo_info {
            flex-shrink: 0;
            width: 176px;
            height: 60px;
            overflow: hidden;

            .left {
              position: relative;
              flex-shrink: 0;
              width: 50px;
              height: 50px;
              overflow: hidden;
              border-radius: 3px;

              img {
                max-width: 100%;
                max-height: 100%;
              }

              .play_icon {
                position: absolute;
                z-index: 2;
                top: 50%;
                left: 50%;
                width: 16px;
                height: 16px;
                margin-top: -8px;
                margin-left: -8px;
              }
            }

            .right {
              width: 126px;
              padding-left: 5px;
              color: #2d2d2d;

              .video_name {
                display: -webkit-box;
                width: 100%;
                height: 32px;
                margin-top: 2px;
                overflow: hidden;
                font-size: 12px;
                line-height: 16px;
                text-align: left;
                text-overflow: ellipsis;
                word-break: break-word;
                -webkit-line-clamp: 2;

                /*! autoprefixer: off */
                -webkit-box-orient: vertical;
              }

              .video_label {
                display: inline-block;
                width: 86%;
                height: 14px;
                margin-top: 6px;
                overflow: hidden;
                color: #666;
                line-height: 14px;
                text-align: left;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }

          .operate {
            flex-shrink: 0;
            width: 50px;
            height: 60px;
            text-align: center;
            cursor: pointer;
          }

          .operate svg:hover {
            fill: #77B9FE !important;
          }
        }

        .selected_svideo_item::after {
          content: ' ';
          position: absolute;
          z-index: 2;
          right: 14px;
          bottom: 0;
          left: 14px;
          height: 1px;
          background: #EEE;
        }

        .selected_svideo_item:last-child::after {
          height: 0 !important;
        }
      }
    }
  }

  .add_goods_wrap {
    width: 100%;
    height: 30px;
    margin-top: 20px;
    border: 1px solid rgb(238 238 238 / 100%);
    border-radius: 3px;
    background: rgb(255 255 255 / 100%);
    color: #343434;
    cursor: pointer;
  }

  .dape_img {
    .upload_img {
      position: relative;
      width: 100px;
      height: 100px;
      margin-bottom: 10px;
      background: #EFEFEF;

      img {
        max-width: 100%;
        max-height: 100%;
      }

      .img_mask {
        display: none;
        position: absolute;
        z-index: 2;
        inset: 0;
        background: rgb(0 0 0 / 50%);

        .img_del {
          position: absolute;
          top: 0;
          right: 4px;
          cursor: pointer;
        }
      }
    }

    .upload_img:hover {
      .img_mask {
        display: inline-block !important;
      }
    }

    .modal_tip_color {
      display: block;
      margin-bottom: -15px;
      padding: 10px 0;
      color: #136cd8;
      font-size: 12px;
      line-height: 15px;
    }
      
    .upload_btn {
      height: 32px;
      padding: 0 15px;
      transition: all .1s ease;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      cursor: pointer;

      &:hover {
        border-color: #ff9d52;
        color: #ff9d52;

        svg {
          fill: #ff9d52 !important;
        }
      }
    }
  }

  .tjsp {
    .add_svideo_wrap {
      width: 100%;
      height: 55px;
      font-size: 12px;

      .add_svideo_btn {
        display: inline-block;
        width: 80px;
        height: 30px;
        margin: 0 10px;
        border-radius: 3px;
        background: #FC701E;
        color: #fff;
        line-height: 30px;
        text-align: center;
      }
    }

    .cart_icon_wrap {
      margin-right: 10px;
      padding: 5px;
      border: 1px solid rgb(0 0 0 / 5%);
      border-radius: 3px;
      line-height: 0;
      cursor: pointer;
    }

    .cart_icon_wrap.current {
      border-color: #FA6F1E !important;
    }
  }
</style>