<!-- 客服组件 -->
<template>
  <div
    :style="{
      background: data.color ? data.color : '#fff',
      paddingTop: data.top_margin + 'px',
      paddingBottom: data.bottom_margin + 'px',
    }"
  >
    <div class="kefu">
      <AliSvgIcon
        width="15px"
        height="15px"
        fillColor="#666"
        iconName="iconphone"
      />
      <span class="text">{{ data.text }}</span>
      <span>{{ data.tel }}</span>
    </div>
  </div>
</template>
<script setup>
  import { sld_m_diy_notice_style } from '/@/utils/utils';

  const props = defineProps({
    data: {
      type: Object,
      default: {
        top_margin: 10,
        bottom_margin: 0,
        color: '#fff',
        tel: 15288888888, //联系方式
        text: '客服电话：', //文本内容
      }
    },
  });
</script>
<style lang="less" scoped>
.kefu {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  height: 30px;
  padding-left: 7px;

  span {
    color: #333;
    font-size: 12px;
  }

  span.text {
    margin-left: 5px;
  }
}
</style>