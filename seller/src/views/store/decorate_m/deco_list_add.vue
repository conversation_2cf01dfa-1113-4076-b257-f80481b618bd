<template>
  <PageWrapper>
    <div class="mobile_diy_page">
      <SldComHeader :type="1" :title="title" :back="true" />
      <div class="flex_row_between_start mobile_diy_page_main">
        <div class="left_menu">
          <VueDraggable
            v-model="detail.menu"
            animation="150"
            :group="{ name: 'people', pull: 'clone', put: false }"
            :removeCloneOnHide="true"
            :sort="false"
            class="flex_row_start_start left_menu_body"
            filter=".unDrag"
          >
            <div
              v-for="item in detail.menu"
              :key="item.tplId"
              class="flex_column_center_center left_menu_item"
            >
              <div class="left_menu_item_icon">
                <AliSvgIcon
                  width="35px"
                  height="35px"
                  fillColor="#fc701e"
                  :iconName="'icon' + item.icon"
                />
              </div>
              <span>{{ item.name }}</span>
            </div>
          </VueDraggable>
        </div>
        <div class="main_data">
          <div class="flex_row_center_start main_data_body">
            <div class="main_data_body_main">
              <img class="fixed_top_img" src="@/assets/images/m_diy_img/m_top_status_bar.png" />
              <VueDraggable
                v-model="diy_data"
                animation="150"
                group="people"
                @add="dataAdd"
                class="main_data_body_list"
              >
                <div
                  v-for="(item, index) in diy_data"
                  :key="index"
                  class="main_data_body_item"
                  :class="select_data.id !== undefined && select_data.id == item.id ? 'active' : ''"
                  @click="handleClick(item)"
                >
                  <adv_01 v-if="item.type == 'live'" :data="item"></adv_01>
                  <adv_02 v-else-if="item.type == 'svideo'" :data="item"></adv_02>
                  <adv_03 v-else-if="item.type == 'more_tab'" :data="item"></adv_03>
                  <adv_04 v-else-if="item.type == 'fzx'" :data="item"></adv_04>
                  <adv_05 v-else-if="item.type == 'fzkb'" :data="item"></adv_05>
                  <adv_06 v-else-if="item.type == 'gonggao'" :data="item"></adv_06>
                  <adv_07 v-else-if="item.type == 'kefu'" :data="item"></adv_07>
                  <adv_08 v-else-if="item.type == 'fuwenben'" :data="item"></adv_08>
                  <adv_09 v-else-if="item.type == 'dapei'" :data="item"></adv_09>
                  <adv_10 v-else-if="item.type == 'tuijianshangpin'" :data="item"></adv_10>
                  <adv_11 v-else-if="item.type == 'tupianzuhe'" :data="item"></adv_11>
                  <adv_12 v-else-if="item.type == 'nav'" :data="item"></adv_12>
                  <adv_13 v-else-if="item.type == 'lunbo'" :data="item"></adv_13>
                  <CubeDisplay v-else-if="item.type == 'cube'" :data="item"></CubeDisplay>
                  <HotAreaDisplay v-else-if="item.type == 'hot_area'" :data="item"></HotAreaDisplay>
                  <div v-else>{{ item }}</div>
                  <div class="operate_wrap">
                    <div
                      class="mdiy_operate_a flex_row_center_center"
                      style="height: 30px; cursor: pointer"
                      @click="(e) => handleItem(e, item.id, 'up')"
                    >
                      <AliSvgIcon
                        iconName="iconmove-up"
                        width="15px"
                        height="15px"
                        fillColor="#fff"
                      />
                    </div>
                    <div
                      class="mdiy_operate_a flex_row_center_center"
                      style="height: 30px; cursor: pointer"
                      @click="(e) => handleItem(e, item.id, 'down')"
                    >
                      <AliSvgIcon iconName="iconxia1" width="15px" height="15px" fillColor="#fff" />
                    </div>
                    <div
                      v-if="item.is_show"
                      class="mdiy_operate_a flex_row_center_center"
                      style="height: 30px; cursor: pointer"
                      @click="(e) => handleItem(e, item.id, 'is_show')"
                    >
                      <AliSvgIcon
                        iconName="iconkejian"
                        width="16px"
                        height="16px"
                        fillColor="#fff"
                      />
                    </div>
                    <div
                      v-else
                      class="mdiy_operate_a flex_row_center_center"
                      style="height: 30px; cursor: pointer"
                      @click="(e) => handleItem(e, item.id, 'is_show')"
                    >
                      <AliSvgIcon
                        iconName="iconbukejian11"
                        width="16px"
                        height="16px"
                        fillColor="#fff"
                      />
                    </div>
                    <div
                      class="mdiy_operate_a flex_row_center_center"
                      style="height: 30px; cursor: pointer"
                      @click="(e) => handleItem(e, item.id, 'del')"
                    >
                      <AliSvgIcon
                        iconName="iconguanbi3"
                        width="16px"
                        height="16px"
                        fillColor="#fff"
                      />
                    </div>
                  </div>
                </div>
              </VueDraggable>
            </div>
          </div>
        </div>
        <div
          class="right_wrap"
          :style="{ background: select_data.type !== undefined ? '#fff' : '#f8f8f8' }"
        >
          <div class="right_wrap_body">
            <template v-if="select_data.type !== undefined">
              <div class="flex_row_start_center r_title">
                <AliSvgIcon
                  width="22px"
                  height="22px"
                  fillColor="#FC701E"
                  :iconName="
                    'icon' + (select_data.icon ? select_data.icon : select_data.admin_icon)
                  "
                />
                <span class="r_title_text"
                  >{{ select_data.name ? select_data.name : select_data.admin_text }}设置</span
                >
              </div>
              <div class="r_item">
                <CubeEdit
                  v-if="select_data.type == 'cube'"
                  :data="select_data"
                  @handleCurSelData="handleCurSelData"
                />
                <HotAreaEditor
                  v-if="select_data.type == 'hot_area'"
                  :data="select_data"
                  @handleCurSelData="handleCurSelData"
                />
                <diyItemEdit :data="select_data" @handleCurSelData="handleCurSelData" v-else />
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
    <div class="m_diy_bottom_wrap" :style="{ position: 'fixed', left: getRealWidth + 10 + 'px' }">
      <div class="add_goods_bottom_btn add_goods_bottom_btn_sel" @click="handleSave">保存装修</div>
    </div>
  </PageWrapper>
</template>
<script setup>
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { VueDraggable } from 'vue-draggable-plus';
  import { ref, reactive, onMounted, provide } from 'vue';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import { useRoute, useRouter } from 'vue-router';
  import { failTip, newImg, sld_com_empty_arrar_2, sld_com_empty_arrar_4, sucTip } from '/@/utils/utils';
  import {
    getMobleDecoDetailApi,
    getMobleDecoMenuApi,
    updateMobileDeco,
  } from '@/api/decorate/deco_m';
  import adv_01 from './adv_01.vue';
  import adv_02 from './adv_02.vue';
  import adv_03 from './adv_03.vue';
  import adv_04 from './adv_04.vue';
  import adv_05 from './adv_05.vue';
  import adv_06 from './adv_06.vue';
  import adv_07 from './adv_07.vue';
  import adv_08 from './adv_08.vue';
  import adv_09 from './adv_09.vue';
  import adv_10 from './adv_10.vue';
  import adv_11 from './adv_11.vue';
  import adv_12 from './adv_12.vue';
  import adv_13 from './adv_13.vue';
  import diyItemEdit from './diy_item_edit.vue';
  import { CubeDisplay, CubeEdit } from '@/components/Cube';
  import { HotAreaEditor, HotAreaDisplay } from '@/components/HotArea';
  import { pageClose } from '/@/utils/utils';
  import { useMultipleTabStore } from '/@/store/modules/multipleTab';

  const { getRealWidth } = useMenuSetting();
  const router = useRouter();
  const tabStore = useMultipleTabStore();
  const route = useRoute();
  const title = ref('');
  const index = ref(0); //添加的模块数量
  const click_type = ref(false);
  const detail = reactive({
    data: [], //数据
    menu: [], //菜单
    category: [], //顶部分类
  }); //装修详情
  const diy_data = ref([]); //中间装修数据
  const select_data = ref({}); //选中的当前数据
  const refresh_center_flag = ref(0); //中间组件是否刷新标识

  onMounted(() => {
    if (route.query.id) {
      get_detail();
    }
    get_menu();
  });

  // fake data generator
  function getItems(count, offset = 0) {
    Array.from({ length: count }, (v, k) => k).map((k) => ({
      id: `item-${k + offset}`,
      content: `item ${k + offset}`,
    }));
  }

  //获取装修详情
  const get_detail = async () => {
    const res = await getMobleDecoDetailApi({ decoId: route.query.id });
    if (res.state == 200) {
      //空处理
      if (res.data.data) {
        let tmp_data = JSON.parse(res.data.data.replace(/&quot;/g, '"'));
        tmp_data = tmp_data.filter((item) => item.type != 'top_cat_nav');
        for (let i in tmp_data) {
          if(tmp_data[i].type == 'tuijianshangpin'){
            if(tmp_data[i].data.info&&Array.isArray(tmp_data[i].data.info)){
              let key = []
              let keyRows = []
              tmp_data[i].data.info.forEach(item=>{
                if(!item.state||(item.state&&item.state==3)){
                  keyRows.push(item)
                  key.push(item.goodsId)
                }
              })
              tmp_data[i].data.info = keyRows
              tmp_data[i].data.ids = key
            }
          }else if(tmp_data[i].type == 'more_tab'){
            tmp_data[i].data.forEach(item=>{
              if(item.info&&item.info.length>0&&item.data_type=='goods'){
                let key = []
                let keyRows = []
                item.info.forEach(it=>{
                  if(!item.state||(item.state&&item.state==3)){
                    keyRows.push(it)
                    key.push(it.goodsId)
                  }
                })
                item.info = keyRows
                item.ids = key
              }
            })
          }
          index.value++;
          tmp_data[i].id = index.value;
        }
        diy_data.value = tmp_data;
      } else {
        diy_data.value = [];
      }
      detail.data = res.data.data ? JSON.parse(res.data.data) : [];
      title.value = res.data.name;
    } else {
      failTip(res.msg);
    }
  };

  //获取开启的装修菜单
  const get_menu = async () => {
    const res = await getMobleDecoMenuApi({ isEnable: 1, apply: route.query.type });
    if (res.state == 200 && res.data) {
      detail.menu = res.data.filter((item) => item.type != 'top_cat');
      let cubeAndhotArea = [
        {
          isUse: 1,
          icon: 'cube-line',
          name: '魔方',
          type: 'cube',
        },
        {
          isUse: 1,
          icon: 'tupianrequ',
          name: '图片热区',
          type: 'hot_area',
        },
      ];
      detail.menu.push(...cubeAndhotArea);
    } else {
      failTip(res.msg);
    }
  };

  //选择组件
  function handleClick(item) {
    select_data.value = item;
  }

  //增加组件
  function dataAdd(e) {
    let menu_item = detail.menu[e.oldDraggableIndex];
    let diy_item = diy_data.value[e.newDraggableIndex];
    diy_item.is_show = true;
    if (menu_item.type == 'live') {
      //直播组件
      diy_item.title = '商联达直播';
      diy_item.top_margin = 10;
      diy_item.bottom_margin = 0;
      diy_item.color = '#fff';
      diy_item.view_more_url = '';
      diy_item.show_style = 'one';
      diy_item.border_radius = 8;
      diy_item.data = {
        ids: [],
        info: [],
      };
    } else if (menu_item.type == 'svideo') {
      //短视频组件
      diy_item.title = '商联达短视频';
      diy_item.top_margin = 10;
      diy_item.bottom_margin = 0;
      diy_item.color = '#fff';
      diy_item.view_more_url = '';
      diy_item.show_style = 'one';
      diy_item.border_radius = 8;
      diy_item.data = {
        ids: [],
        info: [],
      };
    } else if (menu_item.type == 'more_tab') {
      //TAB切换组件
      diy_item.top_margin = 10;
      diy_item.bottom_margin = 0;
      diy_item.color = '#fff';
      diy_item.border_radius = 8;
      diy_item.nav_current = 0;
      diy_item.data = [];
    } else if (menu_item.type == 'fzx') {
      //辅助线组件
      diy_item.val = 'solid';
      diy_item.top_margin = 10;
      diy_item.bottom_margin = 10;
      diy_item.bg_color = '#fff';
      diy_item.color = '#e3e5e9';
    } else if (menu_item.type == 'fzkb') {
      //辅助空白组件
      diy_item.top_margin = 0;
      diy_item.bottom_margin = 0;
      diy_item.color = '#fff';
      diy_item.text = 30;
    } else if (menu_item.type == 'gonggao') {
      //公告组件
      diy_item.top_margin = 10;
      diy_item.bottom_margin = 0;
      diy_item.color = '#fff';
      diy_item.show_style = 'one'; //公告展示风格
      diy_item.text = '';
      diy_item.url = '';
      diy_item.url_type = '';
      diy_item.info = '';
    } else if (menu_item.type == 'kefu') {
      //客服组件
      diy_item.top_margin = 10;
      diy_item.bottom_margin = 0;
      diy_item.color = '#fff';
      diy_item.tel = 15288888888;
      diy_item.text = '客服电话：';
    } else if (menu_item.type == 'fuwenben') {
      //富文本组件
      diy_item.top_margin = 10;
      diy_item.bottom_margin = 0;
      diy_item.color = '#fff';
      diy_item.text = '';
    } else if (menu_item.type == 'nav') {
      //导航组件
      diy_item.page_set = 'single';
      diy_item.page_num = 5;
      diy_item.top_margin = 10;
      diy_item.bottom_margin = 0;
      diy_item.color = '#fff';
      diy_item.data = [];
      diy_item.icon_set = 'up';
      diy_item.slide = 35;
      diy_item.style_set = 'nav';
      sld_com_empty_arrar_4.map((item) => {
        diy_item.data.push({
          img: '',
          img_path: '',
          name: '',
          url: '',
          url_type: '',
          info: '',
        });
      });
    } else if (menu_item.type == 'tupianzuhe') {
      //图片组件
      diy_item.top_margin = 10;
      diy_item.bottom_margin = 0;
      diy_item.color = '#fff';
      diy_item.data = [];
      for (let i in sld_com_empty_arrar_4) {
        let temp = [];
        for (let j in sld_com_empty_arrar_2) {
          temp.push({
            img: '',
            img_path: '',
            imgPath: '',
            name: '',
            url: '',
            url_type: '',
            info: '',
          });
        }
        diy_item.data.push({
          main_title: '',
          sub_title: '',
          url: '',
          url_type: '',
          info: '',
          img: temp,
        });
      } //数据
      diy_item.sele_style = 8;
    } else if (menu_item.type == 'dapei') {
      //搭配组件
      diy_item.top_margin = 10;
      diy_item.bottom_margin = 0;
      diy_item.color = '#fff';
      diy_item.dapei_desc = ''; //搭配图片描述
      diy_item.dapei_img = ''; //搭配图片
      diy_item.width = ''; //搭配图片宽度
      diy_item.height = ''; //搭配图片高度
      diy_item.dapei_title = ''; //搭配标题
      diy_item.img_path = ''; //图片相对路径
      diy_item.imgPath = ''; //图片相对路径
      diy_item.data = {
        ids: [],
        info: [],
      }; //商品数据
    } else if (menu_item.type == 'lunbo') {
      //轮播图组件
      diy_item.top_margin = 10;
      diy_item.bottom_margin = 0;
      diy_item.color = '#fff';
      diy_item.width = 750;
      diy_item.height = 285.5;
      diy_item.data = [];
    } else if (menu_item.type == 'tuijianshangpin') {
      //拖动的类型为推荐商品
      diy_item.top_margin = 10;
      diy_item.bottom_margin = 0;
      diy_item.color = '#fff';
      diy_item.isshow_sales = 0; //是否展示销量
      diy_item.cart_icon_type = 1; //购物车图标样式
      diy_item.show_style = 'small'; //展示类型：big 大图 small 一行两个 list 列表 一行一个
      diy_item.border_radius = 10; //商品圆角 0表示直角
      diy_item.border_style = 'card-shadow'; //商品样式  border_none无边白底  card-shadow卡片投影  border_eee描边白底 border_none_grey_bg 无边灰底
      diy_item.page_margin = 10; //距离页面两边的距离
      diy_item.goods_margin = 10; //商品之间的距离
      diy_item.text_align = 'flex-start'; //文本对齐方式，flex-start 左对齐 center 居中对齐
      diy_item.text_style = 'normal'; //文本样式，normal常规体 bold 加粗体
      diy_item.data = {
        ids: [],
        info: [],
      }; //商品数据
    } else if (menu_item.type == 'cube') {
      diy_item.top_margin = 10;
      diy_item.bottom_margin = 0;
      diy_item.color = '#fff';
      diy_item.data = [];
      diy_item.size = 4;
    } else if (menu_item.type == 'hot_area') {
      diy_item.area_list = [];
      diy_item.style = {
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        borderRadius: { top: 0, bottom: 0 },
        bgColor: '',
      };
      diy_item.img_info = {};
    }
    index.value++;
    diy_item.id = index.value;
  }

  //保存装修
  const handleSave = async () => {
   
    if (click_type.value) return;
    let params = {};
    params.decoId = route.query.id;
    let tar_data = [...diy_data.value];
    params.data = JSON.stringify(tar_data);
    click_type.value = true;
    let res = await updateMobileDeco(params);
    if (res.state == 200) {
      sucTip(res.msg);
      // 2s之后返回上一页
      setTimeout(() => {
        click_type.value = false;
        goBack();
      }, 2000);
    } else {
      click_type.value = false;
      failTip(res.msg);
    }
  };

  const goBack = () => {
    if (window.history && window.history.length == 1) {
      pageClose();
    } else {
      const { fullPath } = route;
      tabStore.closeTabByKey(fullPath, router);
      router.back();
    }
  };

  //编辑组件更新数据
  function handleCurSelData(data) {
    select_data.value = { ...select_data.value, ...data };
    for (let i in diy_data.value) {
      if (diy_data.value[i].id == select_data.value.id) {
        diy_data.value[i] = select_data.value;
        break;
      }
    }
    refresh_center_flag.value += 1;
  }

  const handleItem = (e, index, type) => {
    //装修模块的操作
    e.stopPropagation();
    e.preventDefault();
    if (type == 'del') {
      diy_data.value = diy_data.value.filter((item) => item.id != index); //删除操作
      select_data.value = {};
    } else {
      for (let i = 0; i < diy_data.value.length; i++) {
        if (diy_data.value[i].id == index) {
          let tmp = {};
          if (type == 'down') {
            //向下移动,最后一个不处理
            if (i < diy_data.value.length - 1) {
              tmp = { ...diy_data.value[i] };
              diy_data.value[i] = { ...diy_data.value[i + 1] };
              diy_data.value[i + 1] = { ...tmp };
            }
          } else if (type == 'up') {
            //向上移动，第一个不处理
            if (i != 0) {
              tmp = { ...diy_data.value[i] };
              diy_data.value[i] = { ...diy_data.value[i - 1] };
              diy_data.value[i - 1] = { ...tmp };
            }
          } else if (type == 'is_show') {
            diy_data.value[i].is_show = !diy_data.value[i].is_show; //是否显示
          } else if (type == 'edit') {
            select_data.value = diy_data.value[i]; //编辑
          }
          break;
        }
      }
    }
  };
</script>
<style lang="less" scoped>
  .mobile_diy_page {
    .mobile_diy_page_main {
      .left_menu {
        position: relative;
        flex-shrink: 0;
        width: 220px;
        height: 100%;
        padding: 15px;

        .left_menu_body {
          position: absolute;
          flex-wrap: wrap;
          width: 100%;
          height: 100%;
          padding-bottom: 65px;
          overflow-x: hidden;
          overflow-y: auto;

          .left_menu_item {
            position: relative;
            width: 90px;
            height: 90px;
            margin-right: 10px;
            margin-bottom: 10px;
            border-radius: 3px;
            background: #fff5f0;
            cursor: pointer;

            .left_menu_item_icon {
              width: 35px;
              height: 35px;
              margin-top: 7px;
              margin-bottom: 3px;
            }

            .left_menu_item_active {
              position: absolute;
              inset: 0;
              background: rgb(0 0 0 / 70%);
              color: #fff;
              font-size: 14px;
            }

            &:nth-child(2n) {
              margin-right: 0;
            }
          }
        }
      }

      .main_data {
        position: relative;
        flex: 1;
        height: 100%;

        .main_data_body {
          position: absolute;
          width: 100%;
          height: 100%;
          padding-top: 20px;
          padding-bottom: 65px;
          overflow-x: hidden;
          overflow-y: auto;

          .main_data_body_main {
            position: relative;
            width: 401px;
            padding: 13px;
            background: #f8f8f8;

            .fixed_top_img {
              margin-bottom: -1px;
            }

            .main_data_body_list {
              width: 375px;
              height: 100%;
              min-height: 600px;
              border: 1px solid #eee;
              background: #fff;

              .main_data_body_item {
                position: relative;

                &:hover {
                  border-color: @primary-color;

                  .operate_wrap {
                    display: flex !important;
                  }
                }

                &.active {
                  border: 1px solid @primary-color;
                }

                .operate_wrap {
                  display: none;
                  position: absolute;
                  z-index: 99;
                  right: 0;
                  bottom: 0;
                  width: 135px;
                  height: 30px;
                  border-radius: 12px 0 0;
                  background: #fc701e;
                }

                .mdiy_operate_a {
                  margin-left: 15px;
                }
              }
            }
          }
        }
      }

      .right_wrap {
        position: relative;
        flex-shrink: 0;
        width: 400px;
        height: 100%;
        background: #f8f8f8;

        .right_wrap_body {
          position: absolute;
          width: 100%;
          height: 100%;

          .r_title {
            height: 45px;
            padding: 10px 20px;
            background: #f8f8f8;

            .r_title_left_border {
              display: inline-block;
              width: 4px;
              height: 14px;
              background: #397ed3;
            }

            .r_title_text {
              display: inline-block;
              margin-left: 10px;
              color: #fc701e;
              font-size: 16px;
              font-weight: bold;
            }
          }

          .r_item {
            position: relative;
            width: 400px;
            height: calc(100% - 66px);
            overflow-x: hidden;
            overflow-y: auto;
          }
        }
      }
    }
  }
</style>
<style>
  .slodon-layout-content > div,
  .slodon-layout-content > div > .bg-white,
  .mobile_diy_page,
  .mobile_diy_page > .mobile_diy_page_main {
    height: 96.1%;
  }
</style>
