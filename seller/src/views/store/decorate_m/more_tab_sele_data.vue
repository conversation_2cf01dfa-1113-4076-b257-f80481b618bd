<!-- TAB切换组件数据添加编辑组件 | 商品、直播、短视频 -->
<template>
  <template v-if="type == 'goods'">
    <div class="selected_svideo_list_title">
      <span style="width: 50px">序号</span>
      <span :style="{ width: diy_type == 'integral' ? '126px' : '176px'}">商品信息</span>
      <span :style="{ width: diy_type == 'integral' ? '130px' : '80px'}">价格</span>
      <span style="width: 50px">操作</span>
    </div>
    <div class="selected_svideo_list">
      <div
        v-for="(svideo_item, svideo_index) in data"
        :key="svideo_index"
        class="selected_svideo_item flex_row_start_center">
        <span style="width: 50px">{{ svideo_index + 1 }}</span>
        <div class="svideo_info flex_row_start_center" :style="{ width: diy_type == 'integral' ? '126px' : '176px'}">
          <div class="left flex_row_center_center">
            <img :src="diy_type == 'spreader' ? svideo_item.goodsImage : svideo_item.mainImage" />
          </div>
          <div class="right flex_column_start_start">
            <span class="video_name">{{ svideo_item.goodsName }}</span>
          </div>
        </div>
        <span
          :style="{
            width: diy_type == 'integral' ? '130px' : '80px',
            fontSize: diy_type == 'integral' ? '13px' : '14px',
          }"
        >{{ diy_type == 'integral' ? svideo_item.integralPrice + '积分+¥' + (svideo_item.cashPrice * 1)
          : diy_type == 'spreader' ? (svideo_item.productPrice * 1) : (svideo_item.goodsPrice * 1) }}</span>
        <div
          class="operate flex_row_center_center"
          @click="delGoods(diy_type == 'integral' ? svideo_item.integralGoodsId : svideo_item.goodsId, tar_index)"
        >
          <AliSvgIcon
            width="18px"
            height="18px"
            fillColor="#2d2d2d"
            iconName="iconshanchu5"
          />
        </div>
      </div>
    </div>
  </template>
  <template v-else-if="type == 'svideo'">
    <div class="selected_svideo_list_title">
      <span style="width: 50px">序号</span>
      <span style="width: 176px">短视频信息</span>
      <span style="width: 80px">播放量</span>
      <span style="width: 50px">操作</span>
    </div>
    <div class="selected_svideo_list">
      <div
        v-for="(svideo_item, svideo_index) in data"
        :key="svideo_index"
        class="selected_svideo_item flex_row_start_center"
      >
        <span style="width: 50px">{{ svideo_index + 1 }}</span>
        <div class="svideo_info flex_row_start_center">
          <div class="left flex_row_center_center">
            <img :src="svideo_item.videoImage" />
            <div class="play_icon">
              <AliSvgIcon
                width="22px"
                height="22px"
                fillColor="#fff"
                iconName="iconbofang11"
              />
            </div>
          </div>
          <div class="right flex_column_start_start">
            <span class="video_name">{{ svideo_item.videoName }}</span>
            <span class="video_label">{{ svideo_item.labelName }}</span>
          </div>
        </div>
        <span style="width: 80px">{{ svideo_item.click_num }}</span>
        <div
          class="operate flex_row_center_center"
          @click="delSvideo(svideo_item.videoId, tar_index)"
        >
          <AliSvgIcon
            width="18px"
            height="18px"
            fillColor="#2d2d2d"
            iconName="iconshanchu5"
          />
        </div>
      </div>
    </div>
  </template>
  <template v-else-if="type == 'live'">
    <div class="selected_svideo_list_title">
      <span style="width: 50px">序号</span>
      <span style="width: 176px">直播信息</span>
      <span style="width: 80px">播放量</span>
      <span style="width: 50px">操作</span>
    </div>
    <div class="selected_svideo_list">
      <div
        v-for="(svideo_item, svideo_index) in data"
        :key="svideo_index"
        class="selected_svideo_item flex_row_start_center"
      >
        <span style="width: 50px">{{ svideo_index + 1 }}</span>
        <div class="svideo_info flex_row_start_center">
          <div class="left flex_row_center_center">
            <img :src="svideo_item.liveCover" />
            <div class="play_icon">
              <AliSvgIcon
                width="22px"
                height="22px"
                fillColor="#fff"
                iconName="iconbofang11"
              />
            </div>
          </div>
          <div class="right flex_column_start_start">
            <span class="video_name">{{ svideo_item.liveName }}</span>
            <span class="video_label">{{ svideo_item.labelName }}</span>
          </div>
        </div>
        <span style="width: 80px">{{ svideo_item.viewingNum }}</span>
        <div
          class="operate flex_row_center_center"
          @click="delLive(svideo_item.liveId, tar_index)"
        >
          <AliSvgIcon
            width="18px"
            height="18px"
            fillColor="#2d2d2d"
            iconName="iconshanchu5"
          />
        </div>
      </div>
    </div>
  </template>
</template>
<script setup>
  import { ref, computed, watch } from "vue";

  const props = defineProps({
    data: { //当前组件数据
      type: Array,
      default: []
    },
    type: { //数据类型
      type: String,
      default: undefined
    },
    tar_index: { //当前组件下标
      type: Number,
      default: undefined
    },
    modalType: { //组件类型
      type: String,
      default: undefined
    },
    extra: { //当前组件整体数据
      type: Object,
      default: undefined
    },
    diy_type: { type: String, default: 'home' }, //装修来源类型: home-首页手机装修 topic-专题装修 integral-积分商城装修 spreader-推手装修
  });

  const curFormData = ref({});
  const formData = computed(() => {
    return props.data;
  })

  const emit = defineEmits(['handleCurSelData']);

  watch(
    formData,
    () => {
      if (props.data) {
        curFormData.value = props.data;
      }
    },
    { deep: true, immediate: true },
  );

  //删除单个商品
  function delGoods(goodsId, tar_index = 0) {
    let key = props.diy_type == 'integral' ? 'integralGoodsId' : 'goodsId';
    let data = { ...props.extra };
    if (props.modalType == 'dapei' || props.modalType == 'tuijianshangpin' || props.modalType == 'activity') {
      data.data.ids = data.data.ids.filter(item => item != goodsId);
      data.data.info = data.data.info.filter(item => item[key] != goodsId);
    } else if (props.modalType == 'more_tab') {
      let tar_data = data.data.filter((item, index) => index == tar_index)[0];
      tar_data.ids = tar_data.ids.filter(item => item != goodsId);
      tar_data.info = tar_data.info.filter(item => item[key] != goodsId);
    }
    emit('handleCurSelData', data);
  };

  //删除选中的直播
  function delLive(liveId, tar_index = 0) {
    let data = { ...props.extra };
    if (props.modalType == 'more_tab') {
      let tar_data = data.data.filter((item, index) => index == tar_index)[0];
      tar_data.ids = tar_data.ids.filter(item => item != liveId);
      tar_data.info = tar_data.info.filter(item => item.liveId != liveId);
    } else {
      data.data.ids = data.data.ids.filter(item => item != liveId);
      data.data.info = data.data.info.filter(item => item.liveId != liveId);
    }
    emit('handleCurSelData', data);
  };

  //删除选中的短视频
  function delSvideo(svideoId, tar_index = 0) {
    let data = { ...props.extra };
    if (props.modalType == 'more_tab') {
      let tar_data = data.data.filter((item, index) => index == tar_index)[0];
      tar_data.ids = tar_data.ids.filter(item => item != svideoId);
      tar_data.info = tar_data.info.filter(item => item.videoId != svideoId);
    } else {
      data.data.ids = data.data.ids.filter(item => item != svideoId);
      data.data.info = data.data.info.filter(item => item.videoId != svideoId);
    }
    emit('handleCurSelData', data);
  };
</script>
<style lang="less" scoped>
  .selected_svideo_list_title {
    width: 100%;
    height: 32px;
    border: 1px solid rgb(238 238 238 / 100%);

    span {
      display: inline-block;
      color: #333;
      font-size: 12px;
      line-height: 32px;
      text-align: center;
    }
  }

  .selected_svideo_list {
    .selected_svideo_item {
      position: relative;
      width: 358px;
      height: 60px;

      span {
        flex-shrink: 0;
        color: #2d2d2d;
        font-size: 12px;
        text-align: center;
      }

      .svideo_info {
        flex-shrink: 0;
        width: 176px;
        height: 60px;
        overflow: hidden;

        .left {
          position: relative;
          flex-shrink: 0;
          width: 50px;
          height: 50px;
          overflow: hidden;
          border-radius: 3px;

          img {
            max-width: 100%;
            max-height: 100%;
          }

          .play_icon {
            position: absolute;
            z-index: 2;
            top: 50%;
            left: 50%;
            width: 16px;
            height: 16px;
            margin-top: -8px;
            margin-left: -8px;
          }
        }

        .right {
          width: 126px;
          padding-left: 5px;
          color: #2d2d2d;

          .video_name {
            display: -webkit-box;
            width: 100%;
            height: 32px;
            margin-top: 2px;
            overflow: hidden;
            font-size: 12px;
            line-height: 16px;
            text-align: left;
            text-overflow: ellipsis;
            word-break: break-word;
            -webkit-line-clamp: 2;

            /*! autoprefixer: off */
            -webkit-box-orient: vertical;
          }

          .video_label {
            display: inline-block;
            width: 86%;
            height: 14px;
            margin-top: 6px;
            overflow: hidden;
            color: #666;
            line-height: 14px;
            text-align: left;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }

      .operate {
        flex-shrink: 0;
        width: 50px;
        height: 60px;
        text-align: center;
        cursor: pointer;
      }

      .operate svg:hover {
        fill: #77B9FE !important;
      }
    }

    .selected_svideo_item::after {
      content: ' ';
      position: absolute;
      z-index: 2;
      right: 14px;
      bottom: 0;
      left: 14px;
      height: 1px;
      background: #EEE;
    }

    .selected_svideo_item:last-child::after {
      height: 0 !important;
    }
  }
</style>