<!-- 直播组件 -->
<template>
  <div
    class="video flex_column_start_start"
    :style="{
      paddingTop: data.top_margin + 'px',
      paddingBottom: data.bottom_margin + 'px',
      background: data.color ? data.color : '#fff',
    }"
  >
    <div class="title flex_row_between_center">
      <span class="left_con">{{ data.title }}</span>
      <span class="right_con">查看更多&nbsp;&nbsp;</span>
    </div>
    <div
      v-if="data.show_style == 'one'"
      class="flex_row_start_start live_list"
      style=" width: 100%;height: 171px; overflow: hidden"
    >
      <template v-if="data.data.info.length == 0">
        <div
          v-for="(item_video, index_video) in sld_com_empty_arrar_2"
          :key="index_video"
          :class="'item_' + data.show_style"
          :style="{
            backgroundImage: 'url(' + live_defalut_img()[data.show_style] + ')',
            borderRadius: data.border_radius + 'px',
          }"
        ></div>
      </template>
      <template v-else>
        <div
          v-for="(item_video, index_video) in data.data.info"
          :key="index_video"
          :class="'item_' + data.show_style"
          class="flex_column_between_start"
          :style="{
            backgroundImage: 'url(' + item_video.liveCover + ')',
            borderRadius: data.border_radius + 'px',
          }">
          <div class="live_click flex_row_start_center"
               :style="{
                borderBottomRightRadius: data.border_radius + 'px',
                borderTopLeftRadius: data.border_radius + 'px',
               }"
          >
            <img class="play_icon" src="@/assets/images/m_diy_img/live/living_icon.png"/>
            <span class="live_click_num">{{ item_video.viewingNum }}人观看</span>
          </div>
          <span class="live_name">{{ item_video.liveName }}</span>
          <img class="right_bottom_icon"
               src="@/assets/images/m_diy_img/live/live_list_heart.gif"/>
        </div>
      </template>
    </div>
    <div
      v-else-if="data.show_style == 'two'"
      class="flex_row_start_start live_list"
    >
      <template v-if="data.data.info.length == 0">
        <div
          v-for="(item_video, index_video) in sld_com_empty_arrar_4"
          :key="index_video"
          class="item_two"
          :style="{
            backgroundImage: 'url(' + live_defalut_img()[data.show_style] + ')',
            borderRadius: data.border_radius + 'px',
          }"
        ></div>
      </template>
      <template v-else>
        <div
          v-for="(item_video, index_video) in data.data.info"
          :key="index_video"
          class="item_two"
          :style="{
            backgroundImage: 'url(' + item_video.liveCover + ')',
            borderRadius: data.border_radius + 'px',
          }"
        >
          <div class="live_click_wrap flex_row_center_center"
                :style="{
                borderBottomRightRadius: data.border_radius + 'px',
                borderTopLeftRadius: data.border_radius + 'px',
                }"
          >
            <img class="play_icon" src="@/assets/images/m_diy_img/live/living_icon.png"/>
            <span class="live_click_num">{{ item_video.viewingNum }}人观看</span>
          </div>
          <img class="right_bottom_icon"
               src="@/assets/images/m_diy_img/live/live_list_heart.gif"/>
          <span class="live_name">{{ item_video.liveName }}</span>
        </div>
      </template>
    </div>
  </div>
</template>
<script setup>
  import {
    sld_com_empty_arrar_2,
    sld_com_empty_arrar_4,
    live_defalut_img,
    newImg,
  } from '@/utils/utils';

  const props = defineProps({
    data: {
      type: Object,
      default: {
        top_margin: 10,
        bottom_margin: 0,
        color: '#fff',
        title: '商联达直播',//标题名称
        view_more_url: '',//查看更多的链接
        show_style: 'one',//展示风格：one 只显示2个商品，不可滚动，two 显示多个商品，最多9个，可滚动
        border_radius: 8,//短视频卡片边角，直角0、圆角5px
        data: {
          ids: [],//直播id集合
          info: [],//直播信息
        },//直播数据
      }
    },
  })

</script>
<style lang="less" scoped>
  .video {
    padding: 0 10px 10px;

    .title {
      width: 100%;
      height: 36px;
      line-height: 36px;

      .left_con {
        color: #343434;
        font-size: 14px;
        font-weight: bold;
      }

      .right_con {
        color: #666;
        font-size: 12px;
      }
    }

    .live_list {
      flex-wrap: nowrap;
      width: 100%;
      height: 100%;
      overflow-x: auto;
      overflow-y: hidden;

      .item_one {
        position: relative;
        flex-shrink: 0;
        width: 171px;
        height: 171px;
        margin-right: 10px;
        background-repeat: no-repeat;
        background-position: center center;
        background-size: cover;

        .live_name {
          display: inline-block;
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          padding: 7px 40px 7px 10px;
          overflow: hidden;
          color: #fff;
          font-size: 12px;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .right_bottom_icon {
          position: absolute;
          z-index: 2;
          right: 3px;
          bottom: 6px;
          width: 50px;
        }

        .live_click {
          position: absolute;
          z-index: 2;
          top: 0;
          left: 0;
          height: 18px;
          padding: 0 5px 0 7px;
          background: rgb(0 0 0 / 20%);
          font-size: 12px;

          .play_icon {
            width: 14px;
            height: 11px;
          }

          .live_click_num {
            margin-left: -1px;
            transform: scale(0.9);
            color: #fff;
            font-size: 12px;
          }
        }
      }

      .item_two {
        position: relative;
        flex-shrink: 0;
        width: 120px;
        height: 120px;
        margin-right: 10px;
        background-repeat: no-repeat;
        background-position: center center;
        background-size: cover;

        .live_click_wrap {
          position: absolute;
          z-index: 2;
          left: 0;
          box-sizing: border-box;
          height: 18px;
          padding: 0 4px 0 3px;
          background: rgb(0 0 0 / 20%);
          line-height: 18px;

          .play_icon {
            width: 14px;
            height: 11px;
          }

          .live_click_num {
            display: inline-block;
            margin-left: -1px;
            transform: scale(0.9);
            color: #fff;
            font-size: 12px;
          }
        }
      }

      .right_bottom_icon {
        position: absolute;
        z-index: 4;
        right: 0;
        bottom: 4px;
        width: 50px;
      }

      .live_name {
        position: absolute;
        z-index: 3;
        right: 0;
        bottom: 0;
        left: 0;
        padding: 5px 30px 5px 8px;
        overflow: hidden;
        background: transparent;
        color: #fff;
        font-size: 12px;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
</style>