<!-- 富文本组件 -->
<template>
  <div
    class="ql-container ql-snow fuwenben"
    :style="{
      background: data.color ? data.color : '#fff',
      paddingTop: data.top_margin + 'px',
      paddingBottom: data.bottom_margin + 'px',
    }"
  >
    <div
      v-if="curData.text"
      class="ql-editor"
      v-html="curData.text"
    ></div>
    <span v-else>点此编辑『富文本』内容:你可以对文字进行加粗、斜体、下划线、删除线、文字颜色、背景色、以及字号大小等简单排版操作。</span>
  </div>
</template>
<script setup>
  import { ref, computed, watch } from 'vue';

  const props = defineProps({
    data: {
      type: Object,
      default: {
        top_margin: 10,
        bottom_margin: 0,
        color: '#fff',
        text: '', //富文本的内容
      }
    },
  });

  const curData = ref({
    data: '',
  });

  const comData = computed(() => {
      return props.data;
  })

  watch(
      comData,
      () => {
        curData.value['text'] = props.data.text;
      },
      { deep: true, immediate: true },
  )
</script>
<style lang="less" scoped>
.fuwenben {
  padding: 5px;
  font-size: 13px;
  line-height: 26px;
  word-break: break-all;
}
</style>