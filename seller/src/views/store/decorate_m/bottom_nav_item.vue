<template>
  <div class="flex_com_row_start_center sld_com_img">
    <div class="common_img_part nav_bottom_img_part flex_com_column_center_center">
      <div class="upload_img flex_column_center_center">
        <Upload
          name="file"
          accept=".gif, .jpeg, .png, .jpg"
          :withCredentials="true"
          :showUploadList="false"
          :openFileDialogOnClick="false"
        >
          <div
            class="flex_column_center_center"
            @click="openMaterial(data, index, -1, 'autoImg')"
          >
            <img v-if="data.autoImg.img" :src="data.autoImg.img" />
            <AliSvgIcon
              v-else
              width="40px"
              height="40px"
              fillColor="#429EFE"
              iconName="iconziyuan110"
            />
            <span class="upload_btn">选择图片</span>
            <span class="upload_btn_select">未选中</span>
          </div>
        </Upload>
      </div>
      <span v-if="upload_img_tip" class="upload_img_tip">{{ upload_img_tip }}</span>
    </div>
    <div class="common_img_part nav_bottom_img_part flex_com_column_center_center">
      <div class="upload_img flex_column_center_center">
        <Upload
          name="file"
          accept=".gif, .jpeg, .png, .jpg"
          :withCredentials="true"
          :showUploadList="false"
          :openFileDialogOnClick="false"
        >
          <div
            class="flex_column_center_center"
            @click="openMaterial(data, index, -1, 'selectImg')"
          >
            <img v-if="data.selectImg.img" :src="data.selectImg.img" />
            <AliSvgIcon
              v-else
              width="40px"
              height="40px"
              fillColor="#429EFE"
              iconName="iconziyuan110"
            />
            <span class="upload_btn">选择图片</span>
            <span class="upload_btn_select">已选中</span>
          </div>
        </Upload>
      </div>
      <span v-if="upload_img_tip" class="upload_img_tip">{{ upload_img_tip }}</span>
    </div>
    <div class="img_con nav_bottom_img_con flex_column_center_start">
      <Input
        class="title"
        :maxLength="4"
        :disabled="data.editState == 1 ? false : true"
        :defaultValue="data.title"
        :value="data.title"
        @change="(e) => onChange(e.target.value, 'title', index)"
      />
    </div>
  </div>
  
  <!-- 图片素材选择 start -->
  <SldMaterialImgs
    ref="sldMaterialImgsRef"
    :visibleModal="chooseFile == 1 ? true : false"
    :maxUploadNum="1"
    :allowRepeat="false"
    :selectedData="selectImageData"
    @closeMaterial="() => closeMaterial()"
    @confirmMaterial="(val) => confirmMaterial(val)"
  ></SldMaterialImgs>
  <!-- 图片素材选择 end -->
</template>
<script setup>
  import { ref, computed, watch, reactive } from 'vue';
  import { Upload, Input } from 'ant-design-vue';
  import SldMaterialImgs from '@/components/SldMaterialFiles/sldMaterialImgs.vue';

  const props = defineProps({
    data: {
      type: Object,
      default: {}
    },
    index: {
      type: Number,
      default: 0
    },
    upload_img_tip: {
      type: String,
      default: ''
    },
    parent_index: {
      type: Number,
      default: -1
    },
    list: {
      type: Object,
      default: {}
    },
  });

  const curFormData = ref({});
  const formData = computed(() => {
    return props.data;
  });
  const chooseFile = ref(0); //上传素材弹窗显示 1:图片 2:视频
  const operate_bottom_nav_type = ref(''); //当前操作的底部tabbar图片类型:  未选择 autoImg  已选择 selectImg
  const operate_files_index = ref(''); //当前操作选择图片下标
  const operate_files_index_parent = ref(''); //当前操作选择图片上级下标
  let selectImageData = reactive({ ids:[], data:[] }); //产品图片数据

  const emit = defineEmits(['handleCurSelData','handleChange']);

  watch(
    formData,
    () => {
      if (props.data) {
        curFormData.value = props.data;
      }
    },
    { deep: true, immediate: true },
  );

  function onChange(val, type, tar_index = 0, parent_index = -1) {
    emit('handleChange', val, type, tar_index, parent_index);
  };

  //打开上传素材文件
  function openMaterial(item, index, indexs = -1, bottom_index) {
    if (indexs !== -1) {
      operate_files_index_parent.value = indexs;
    } else {
      operate_files_index_parent.value = '';
    }
    operate_bottom_nav_type.value = bottom_index ? bottom_index : '';
    operate_files_index.value = index;
    let selectImageDataVal = { ids: [], data: [] };
    if (item.img_path) {
      selectImageDataVal = {
        ids:[Number(item.img_path.split('?bindId=')[1])],
        data:[{
          bindId: item.img_path.split('?bindId=')[1],
          checked: true,
          filePath: item.img_path,
          fileUrl: item.img,
          fileType: 1,
        }]
      };
    }
    selectImageData = selectImageDataVal;
    chooseFile.value = 1;
  };
    
  //关闭上传素材文件
  function closeMaterial() {
    operate_files_index.value = '';
    chooseFile.value = 0;
  };
  
  //确认上传素材文件
  function confirmMaterial(val) {
    let data = { ...props.list };
    if (operate_files_index_parent.value !== '') {
      data.data[operate_files_index_parent.value].img[operate_files_index.value].img = val.ids.length ? val.data[0].fileUrl : '';
      data.data[operate_files_index_parent.value].img[operate_files_index.value].img_path = val.ids.length ? val.data[0].filePath : '';
      data.data[operate_files_index_parent.value].img[operate_files_index.value].imgPath = val.ids.length ? val.data[0].filePath : '';
      data.data[operate_files_index_parent.value].img[operate_files_index.value].width = val.ids.length ? val.data[0].width : '';
      data.data[operate_files_index_parent.value].img[operate_files_index.value].height = val.ids.length ? val.data[0].height : '';
      data.width = val.ids.length ? val.data[0].width : '';
      data.height = val.ids.length ? val.data[0].height : '';
    } else if (operate_bottom_nav_type.value) {
      data.data[operate_files_index.value][operate_bottom_nav_type.value].img = val.ids.length ? val.data[0].fileUrl : '';
      data.data[operate_files_index.value][operate_bottom_nav_type.value].img_path = val.ids.length ? val.data[0].filePath : '';
      data.data[operate_files_index.value][operate_bottom_nav_type.value].imgPath = val.ids.length ? val.data[0].filePath : '';
      data.data[operate_files_index.value][operate_bottom_nav_type.value].width = val.ids.length ? val.data[0].width : '';
      data.data[operate_files_index.value][operate_bottom_nav_type.value].height = val.ids.length ? val.data[0].height : '';
    } else {
      data.data[operate_files_index.value].img = val.ids.length ? val.data[0].fileUrl : '';
      data.data[operate_files_index.value].img_path = val.ids.length ? val.data[0].filePath : '';
      data.data[operate_files_index.value].imgPath = val.ids.length ? val.data[0].filePath : '';
      data.data[operate_files_index.value].width = val.ids.length ? val.data[0].width : '';
      data.data[operate_files_index.value].height = val.ids.length ? val.data[0].height : '';
      data.width = val.ids.length ? val.data[0].width : '';
      data.height = val.ids.length ? val.data[0].height : '';
    }
    chooseFile.value = 0;
    selectImageData = val;
    emit('handleCurSelData', data);
    operate_files_index_parent.value = '';
    operate_files_index.value = '';
  };
</script>
<style lang="less" scoped>
.sld_com_img {
  position: relative;
  margin-top: 10px;
  padding: 10px 0;
  border: 1px solid #eee;
  background: #f8f8f8;

  .common_img_part {
    .upload_img_tip {
      margin-top: -20px;
      margin-bottom: 21px;
      transform: scale(0.9);
    }

    .upload_img {
      position: relative;
      margin: 22px 0 22px 22px;
      padding-right: 22px;
      border-right: 1px solid #eeee;

      img {
        max-width: 48px;
        max-height: 48px;
        cursor: pointer;
      }

      .upload_btn {
        margin-top: 5px;
        color: #666;
        font-size: 12px;
        line-height: 12px;
        text-align: center;
        white-space: nowrap;
        cursor: pointer;
      }
    }

    .upload_img:hover {
      .img_mask {
        display: inline-block !important;
      }
    }

    &.nav_bottom_img_part {
      &:nth-child(2) {
        .upload_img {
          border-right: none;
        }
      }

      .upload_img  {
        margin: 10px 0 10px 17px;
        padding-right: 17px;

        img {
          max-width: 38px;
          max-height: 38px;
        }

        .upload_btn {
          margin-top: 10px;
        }

        .upload_btn_select {
          margin-top: 2px;
          color: #ABAAAA;
          font-family: "Microsoft YaHei";
          font-size: 12px;
          font-weight: 400;
        }
      }
    }
  }

  .img_con {
    margin-right: 15px;
    margin-left: 15px;

    .title {
      width: 232px;
      margin-bottom: 8px;
    }

    .bg_color_current {
      border-color: #429DFD !important;
      cursor: pointer;
    }

    &.nav_bottom_img_con {
      height: 74px;
      margin-right: 16px;
      margin-left: 0;

      .title {
        width: 180px;
      }
    }
  }

  .del_sld_com_img {
    position: absolute;
    top: -8px;
    right: -8px;
    margin-left: 20px;
    cursor: pointer;
  }
}
</style>