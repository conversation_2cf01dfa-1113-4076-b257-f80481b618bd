<!-- 搭配组件 -->
<template>
  <div
    class="dapei"
    :style="{
      background: data.color ? data.color : '#fff',
      paddingTop: data.top_margin + 'px',
      paddingBottom: data.bottom_margin + 'px',
    }"
  >
    <div v-if="data.dapei_title" class="dapei_title">{{ data.dapei_title }}</div>
    <div class="img_wrap">
      <img v-if="data.dapei_img" :src="data.dapei_img" />
      <div v-else class="flex_column_center_center" style="padding-top: 10px">
        <AliSvgIcon
          width="40.8px"
          height="33.6px"
          fillColor="#fff"
          iconName="icontupian1"
        />
        <span class="center_tip">宽750*高不限</span>
      </div>
    </div>
    <div v-if="data.dapei_desc" class="dapei_desc">{{ data.dapei_desc }}</div>
    <div class="dapei_goods_list">
      <div class="goods_info">
        <template v-if="data.data.info.length > 0">
          <div
            v-for="(item, index) in data.data.info"
            :key="item.goodsId"
            class="flex_com_column_start_start item"
            :style="{ marginRight: (index == data.data.info.length - 1 ? 10 : 0) + 'px' }">
            <div class="flex_row_common img">
              <img :src="diy_type == 'spreader' ? item.goodsImage : item.mainImage" />
            </div>
            <span class="name">{{ item.goodsName }}</span>
            <span class="price">¥{{ diy_type == 'spreader' ? item.productPrice : item.goodsPrice }}</span>
          </div>
        </template>
        <template v-else>
          <div
            v-for="(item) in sld_com_empty_arrar_4"
            :key="item"
            class="flex_com_column_start_start item"
          >
            <div class="flex_row_common img">
              <img :src="default_goods_img" />
            </div>
            <span class="name">商品名称</span>
            <span class="price">¥0</span>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>
<script setup>
  import { default_goods_img, sld_com_empty_arrar_4 } from '@/utils/utils';
  const props = defineProps({
    data: {
      type: Object,
      default: {
        top_margin: 10,
        bottom_margin: 0,
        color: '#fff',
        dapei_desc: '', //搭配图片描述
        dapei_img: '', //搭配图片
        width: '', //搭配图片宽度
        height: '', //搭配图片高度
        dapei_title: '', //搭配标题
        img_path: '', //图片相对路径
        imgPath: '', //图片相对路径
        data: {
          ids: [],
          info: [],
        }, //商品数据
      }
    },
    diy_type: { type: String, default: 'home' }, //装修来源类型: home-首页手机装修 topic-专题装修 integral-积分商城装修 spreader-推手装修
  });
</script>
<style lang="less" scoped>
.dapei {
  background: #f5f5f5;

  .dapei_title {
    height: 40px;
    padding: 0 30px;
    overflow: hidden;
    background: #fff;
    color: #2E2E2E;
    font-size: 14px;
    line-height: 40px;
    text-align: center;
    white-space: nowrap;
  }

  .dapei_desc {
    height: 33px;
    padding: 0 10px;
    overflow: hidden;
    background: #fff;
    color: #2E2E2E;
    font-size: 12px;
    line-height: 33px;
    text-align: left;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .img_wrap {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 100%;
    min-height: 40px;
    background: rgb(232 243 254 / 100%);

    img {
      max-width: 100%;
      max-height: 100%;
    }

    .center_tip {
      padding: 8px;
      color: #666;
      font-size: 12px;
    }
  }

  .dapei_goods_list {
    width: 100%;
    height: 100%;
    overflow-x: auto;
    overflow-y: hidden;

    .goods_info {
      display: flex;
      flex-flow: row nowrap;
      width: 100%;
      margin-top: 8px;

      .item {
        flex-shrink: 0;
        width: 111px;
        margin-left: 10px;
        overflow: hidden;
        border-radius: 8px;
        background: #fff;

        .img {
          width: 111px;
          height: 111px;

          img {
            max-width: 100%;
            max-height: 100%;
          }
        }

        .name {
          display: -webkit-box;
          height: 36px;
          margin: 6px 9px 5px;
          overflow: hidden;
          color: #333;
          font-size: 12px;
          line-height: 18px;
          text-overflow: ellipsis;
          word-break: break-word;
          -webkit-line-clamp: 2;

          /*! autoprefixer: off */
          -webkit-box-orient: vertical;
        }

        .price {
          margin-bottom: 5px;
          margin-left: 9px;
          color: #FF2B20;
          font-size: 15px;
          font-weight: 500;
        }
      }
    }
  }
}
</style>