<!-- TAB切换组件数据展示组件 -->
<template>
  <template v-if="empty">
    <div
      v-if="type == 'goods'"
      class="goods_item flex_column_start_start"
      :style="{ borderRadius: border_radius + 'px' }"
    >
      <span
        class="goods_img"
        :style="{
          backgroundImage: 'url(' + more_tab_empty_goods_img + ')',
          borderTopLeftRadius: border_radius + 'px',
          borderTopRightRadius: border_radius + 'px',
        }"
      ></span>
      <span class="goods_name">商品名称商品名称商品名称商品名称商品名商品名称商品名称商品名称商品名称商品名</span>
      <div class="bottom_part flex_row_between_center">
        <div class="price flex_row_start_end">
          <template v-if="diy_type == 'integral'">
            <span class="unit">0积分+¥.00</span>
          </template>
          <template v-else>
            <span class="unit">¥</span>
            <span class="price_int">0</span>
            <span class="price_decimal">.00</span>
          </template>
        </div>
        <AliSvgIcon
          v-if="diy_type != 'integral' && diy_type != 'spreader' && cart_index < 4"
          :width="show_cart_icon_data()[cart_index]['width'] + 'px'"
          :height="show_cart_icon_data()[cart_index]['width'] + 'px'"
          fillColor="#F10D3B"
          :iconName="'icon' + show_cart_icon_data()[cart_index]['icon']"
        />
      </div>
    </div>
    <div
      v-else-if="type == 'live'"
      class="live_item flex_column_start_start"
      :style="{ borderRadius: border_radius + 'px' }"
    >
      <img
        class="empty_live_img"
        src="@/assets/images/m_diy_img/more_tab/empty_live.png"
        :style="{ borderRadius: border_radius + 'px' }"
      />
    </div>
    <div
      v-else-if="type == 'svideo'"
      class="svideo_item flex_column_start_start"
      :style="{ borderRadius: border_radius + 'px' }"
    >
      <img
        class="empty_svideo_img"
        src="@/assets/images/m_diy_img/more_tab/empty_svideo.png"
        :style="{ borderRadius: border_radius + 'px' }"
      />
    </div>
  </template>
  <template v-else>
    <div
      v-if="curFormData.data_type == 'goods'"
      class="goods_item flex_column_start_start"
      :style="{ borderRadius: border_radius + 'px' }"
    >
      <span
        class="goods_img"
        :style="{
          backgroundImage: 'url(' + (diy_type == 'spreader' ? info.goodsImage : info.mainImage) + ')',
          borderTopLeftRadius: border_radius + 'px',
          borderTopRightRadius: border_radius + 'px',
        }"
      ></span>
      <span class="goods_name"
        >{{ info.goodsName.length > 19 && isIE ? (info.goodsName.substring(0, 19) + '...') : info.goodsName }}</span>
      <div class="bottom_part flex_row_between_center">
        <div class="price flex_row_start_end">
          <template v-if="diy_type == 'integral'">
            <span class="unit">{{ info.integralPrice }}积分{{ info.cashPrice !== undefined ? '+¥' + info.cashPrice : '' }}</span>
          </template>
          <template v-else-if="diy_type == 'spreader'">
            <span class="unit">¥</span>
            <span class="price_int">{{ type == 'empty' ? 0 : getPartNumber(info.productPrice, 'int') }}</span>
            <span class="price_decimal">{{ type == 'empty' ? '.00' : getPartNumber(info.productPrice, 'decimal') }}</span>
          </template>
          <template v-else>
            <span class="unit">¥</span>
            <span class="price_int">{{ type == 'empty' ? 0 : getPartNumber(info.goodsPrice, 'int') }}</span>
            <span class="price_decimal">{{ type == 'empty' ? '.00' : getPartNumber(info.goodsPrice, 'decimal') }}</span>
          </template>
        </div>
        <AliSvgIcon
          v-if="diy_type != 'integral' && diy_type != 'spreader' && data.cart_icon_type < 5"
          :width="show_cart_icon_data()[data.cart_icon_type - 1]['width'] + 'px'"
          :height="show_cart_icon_data()[data.cart_icon_type - 1]['width'] + 'px'"
          fillColor="#F10D3B"
          :iconName="'icon' + show_cart_icon_data()[data.cart_icon_type - 1]['icon']"
        />
      </div>
    </div>
    <div
      v-else-if="curFormData.data_type == 'live'"
      class="live_item flex_column_start_start"
      :style="{ borderRadius: border_radius + 'px' }"
    >
      <div
        class="live_img"
        :style="{
          backgroundImage: 'url(' + info.liveCover + ')',
          borderTopLeftRadius: border_radius + 'px',
          borderTopRightRadius: border_radius + 'px',
        }"
      >
        <div
          class="live_click flex_row_start_center"
          :style="{
            borderBottomRightRadius: border_radius + 'px',
            borderTopLeftRadius: border_radius + 'px',
          }"
        >
          <img
            v-if="info.liveState == 4"
            class="back_icon"
            src="@/assets/images/m_diy_img/live/back_icon.png"
          />
          <img
            v-else
            class="play_icon"
            src="@/assets/images/m_diy_img/live/living_icon.png"
          />
          <span class="live_click_num">{{ info.viewingNum }}人观看</span>
        </div>
        <img
          class="right_bottom_icon"
          src="@/assets/images/m_diy_img/live/live_list_heart.gif"
        />
      </div>
      <span class="live_name">{{ info.liveName }}</span>
      <div class="bottom_part flex_row_start_center">
        <div class="left flex_row_start_center">
          <img class="author_avator" :src="info.memberAvatar" />
          <span class="author_nick_name">{{ info.memberNickname }}</span>
        </div>
        <span class="live_status"
              :style="{ backgroundColor: info.liveState == 4 ? '#BCAEFE' : '#FF1F1F' }">
          {{ info.liveState == 4 ? '回放' : '直播中' }}
        </span>
      </div>
    </div>
    <div
      v-else-if="curFormData.data_type == 'svideo'"
      class="svideo_item flex_column_start_start"
      :style="{ borderRadius: border_radius + 'px' }"
    >
      <div
        class="svideo_img"
        :style="{
          backgroundImage: 'url(' + info.videoImage + ')',
          borderTopLeftRadius: border_radius + 'px',
          borderTopRightRadius: border_radius + 'px',
        }"
      >
        <div class="video_click flex_row_start_center">
          <img class="play_icon" src="@/assets/images/m_diy_img/svideo/play_icon.png"/>
          <span class="video_click_num">{{ info.clickNum }}人观看</span>
        </div>
      </div>
      <span class="svideo_name">{{ info.videoName }}</span>
      <div class="bottom_part flex_row_start_center">
        <div class="left flex_row_start_center">
          <img class="author_avator" :src="info.memberAvatar" />
          <span class="author_nick_name">{{ info.videoName }}</span>
        </div>
      </div>
    </div>
  </template>
</template>
<script setup>
  import { ref, computed, watch, onMounted } from "vue";
  import {
    show_cart_icon_data,
    getPartNumber,
    more_tab_empty_goods_img,
    newImg,
  } from '@/utils/utils';

  const props = defineProps({
    type: {
      type: String,
      default: undefined
    },
    empty: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: {}
    },
    index: {
      type: Number,
      default: undefined
    },
    data: {
      type: Array,
      default: []
    },
    border_radius: {
      type: String,
      default: 8
    },
    diy_type: { type: String, default: 'home' }, //装修来源类型: home-首页手机装修 topic-专题装修 integral-积分商城装修 spreader-推手装修
  });

  const cart_index =  ref(0);
  const curFormData = ref({});
  const formData = computed(() => {
    return props.data;
  })

  watch(
    formData,
    () => {
      if (props.data) {
        if (props.data.cart_icon_type) {
          cart_index.value = props.data.cart_icon_type * 1 - 1;
        } else if (props.data.data != undefined && props.data.data.length != undefined && props.data.data.length > 0 && props.data.data.length > props.data.nav_current) {
          cart_index.value = props.data.data[props.data.nav_current].cart_icon_type * 1 - 1;
        }
        curFormData.value = props.data;
      }
    },
    { deep: true, immediate: true },
  );

  const isIE = ref(false);

  onMounted(() => {
    isIE.value = settleIsIE();
  })

  function settleIsIE() {
    if (!!window.ActiveXObject || 'ActiveXObject' in window) {
      return true;
    } else {
      return false;
    }
  }
</script>
<style lang="less" scoped>
  .goods_item {
    width: 171px;
    height: 252px;
    margin-top: 10px;
    margin-left: 10px;
    background: #fff;

    .goods_img {
      width: 171px;
      height: 171px;
      background-repeat: no-repeat;
      background-position: center center;
      background-size: cover;
    }

    .goods_name {
      display: -webkit-box;
      width: 100%;
      height: 36px;
      margin-top: 10px;
      padding: 0 10px;
      overflow: hidden;
      color: #2E2E2E;
      font-size: 14px;
      line-height: 18px;
      text-overflow: ellipsis;
      word-break: break-word;
      -webkit-line-clamp: 2;

      /*! autoprefixer: off */
      -webkit-box-orient: vertical;

    }

    .bottom_part {
      width: 100%;
      padding: 0 10px;

      .price {
        align-items: baseline;
        margin-top: 9px;
        color: #FF2C20;

        .unit {
          font-size: 12px;
        }

        .price_int {
          margin-left: 1px;
          font-size: 16px;
          line-height: 16px;
        }

        .price_decimal {
          font-size: 12px;
        }
      }
    }
  }

  .live_item {
    width: 171px;
    height: 252px;
    margin-top: 10px;
    margin-left: 10px;
    background: #fff;

    .empty_live_img {
      width: 100%;
      height: 100%;
    }

    .live_img {
      position: relative;
      width: 171px;
      height: 171px;
      background-repeat: no-repeat;
      background-position: center center;
      background-size: cover;

      .live_click {
        position: absolute;
        z-index: 2;
        top: 0;
        left: 0;
        height: 18px;
        padding: 0 5px 0 7px;
        background: rgb(0 0 0 / 20%);
        font-size: 12px;

        .play_icon {
          width: 14px;
          height: 11px;
        }

        .back_icon {
          width: 12px;
          height: 12px;
        }

        .live_click_num {
          margin-left: -1px;
          transform: scale(0.9);
          color: #fff;
          font-size: 12px;
        }
      }

      .right_bottom_icon {
        position: absolute;
        z-index: 2;
        right: 2px;
        bottom: 4px;
        width: 50px;
      }
    }

    .live_name {
      display: -webkit-box;
      height: 36px;
      margin-top: 10px;
      padding: 0 10px;
      overflow: hidden;
      color: #2E2E2E;
      font-size: 14px;
      line-height: 18px;
      text-overflow: ellipsis;
      word-break: break-word;
      -webkit-line-clamp: 2;

      /*! autoprefixer: off */
      -webkit-box-orient: vertical;

    }

    .bottom_part {
      position: relative;
      width: 100%;
      margin-top: 7px;
      padding: 0 10px;

      .left {
        .author_avator {
          width: 20px;
          height: 20px;
          margin-right: 6px;
          border-radius: 50%;
        }

        .author_nick_name {
          width: 80px;
          overflow: hidden;
          color: #999;
          font-size: 12px;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .live_status {
        position: absolute;
        right: -12px;
        bottom: -6px;
        flex-shrink: 0;
        padding: 0 9px;
        transform: scale(0.5);
        border-radius: 16px;
        color: #fff;
        font-size: 22px;
      }
    }
  }

  .svideo_item {
    width: 171px;
    height: 253px;
    margin-top: 10px;
    margin-left: 10px;
    background: #fff;

    .empty_svideo_img {
      width: 100%;
      height: 100%;
    }

    .svideo_img {
      position: relative;
      width: 171px;
      height: 171px;
      background-repeat: no-repeat;
      background-position: center center;
      background-size: cover;

      .video_click {
        display: inline-block;
        height: 17px;
        margin: 5px;
        border-radius: 8px;
        background: linear-gradient(90deg, #666 0%, rgb(0 0 0 / 0%) 100%);
        font-size: 12px;

        .play_icon {
          width: 11px;
          height: 11px;
          margin-top: -2px;
          margin-left: 3px;
        }

        .video_click_num {
          display: inline-block;
          margin-left: -2px;
          transform: scale(0.85);
          color: #fff;
          font-size: 12px;
        }
      }
    }

    .svideo_name {
      display: -webkit-box;
      height: 36px;
      margin-top: 10px;
      padding: 0 10px;
      overflow: hidden;
      color: #2E2E2E;
      font-size: 14px;
      line-height: 18px;
      text-overflow: ellipsis;
      word-break: break-word;
      -webkit-line-clamp: 2;

      /*! autoprefixer: off */
      -webkit-box-orient: vertical;

    }

    .bottom_part {
      position: relative;
      width: 100%;
      margin-top: 7px;
      padding: 0 10px;

      .left {
        .author_avator {
          width: 20px;
          height: 20px;
          margin-right: 6px;
          border-radius: 50%;
        }

        .author_nick_name {
          width: 80px;
          overflow: hidden;
          color: #999;
          font-size: 12px;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
</style>