<!-- 辅助空白组件 -->
<template>
  <div class="fzkb"
       :style="{ height: data.text + 'px', backgroundColor: data.color }">
  </div>
</template>
<script setup>
  const props = defineProps({
    data: {
      type: Object,
      default: {
        top_margin: 10,
        bottom_margin: 0,
        text: 30, //辅助空白的高度
        color: '#fff', //颜色
      }
    },
  });
</script>
<style lang="less" scoped>
.fzkb {
  width: 100%;
  background: #fff;
}
</style>