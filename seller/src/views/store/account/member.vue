<template>
  <div class="">
    <div style="width: 100%; height: 5px"></div>
    <BasicTable @register="standardTable" style="padding: 0">
      <template #tableTitle>
        <div class="toolbar">
          <div class="toolbar_btn" @click="handleClick(null, 'add')">
            <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#fff" />
            <span>{{ L('新增管理员') }}</span>
          </div>
        </div>
      </template>
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.key == 'action'">
          <template v-if="record.isStoreAdmin != 0"> -- </template>
          <template v-else>
            <TableAction
            class="TableAction"
            :actions="[
              {
                label: L('编辑'),
                onClick: handleClick.bind(null, record, 'edit'),
              },
              {
                label: record.isAllowLogin == 1 ? L('冻结') : L('解冻'),
                onClick: handleClick.bind(null, record, 'freeze'),
              },
              {
                label: L('重置密码'),
                onClick: handleClick.bind(null, record, 'reset'),
              },
              {
                label: L('删除'),
                popConfirm: {
                  title: L('删除后不可恢复，是否确定删除？'),
                  placement: 'left',
                  confirm: handleClick.bind(null, record, 'del'),
                },
              },
            ]"
          />
          </template>
        </template>
        <template v-else-if="column.key">
          {{ text ? text : '--' }}
        </template>
      </template>
    </BasicTable>
    <SldModal
      :width="width"
      :title="title"
      :visible="visible"
      :content="content"
      :confirmBtnLoading="confirmBtnLoading"
      :showFoot="true"
      @cancle-event="handleCancle"
      @click-sms-event="handleSmsCancle"
      @confirm-event="handleConfirm"
    />
  </div>
</template>
<script>
  import { getCurrentInstance, defineComponent, ref, onMounted } from 'vue';
  import { Popconfirm } from 'ant-design-vue';
  import { sucTip, failTip } from '@/utils/utils';
  import { BasicTable, useTable,TableAction } from '/@/components/Table';
  import {
    getAdminUserList,
    getRoleManageList,
    addAdminUser,
    updateAdminUser,
    freezeAdminUser,
    resetAdminUser,
    delAdminUser,
  } from '/@/api/store/account';
  import SldModal from '@/components/SldModal/index.vue';
  import { validatorVendorEmail, mobile_reg ,validatorMemPwd,checkSmsCode} from '/@/utils/validate';
  import base64Encrypt from '/@/utils/base64Encrypt';
  import { useUserStore } from '/@/store/modules/user';

  export default defineComponent({
    components: {
      BasicTable,
      Popconfirm,
      SldModal,
      TableAction
    },
    setup() {

      const userStore = useUserStore();
      const vm = getCurrentInstance();
      const L = vm?.appContext.config.globalProperties.$sldComLanguage;

      const [standardTable, { reload }] = useTable({
        api: (arg) => getAdminUserList({ ...arg }),
        rowKey:'vendorId',
        fetchSetting: {
          pageField: 'current',
          sizeField: 'pageSize',
          listField: 'data.list',
          totalField: 'data.pagination.total',
        },
        columns: [
          {
            title: L('账号'),
            dataIndex: 'vendorName',
            width: 80,
          },
          {
            title: L('所属权限组'),
            dataIndex: 'rolesName',
            width: 80,
          },
          {
            title: L('手机号'),
            dataIndex: 'vendorMobile',
            width: 80,
          },
          {
            title: L('邮箱'),
            dataIndex: 'vendorEmail',
            width: 80,
          },
          {
            title: L('创建时间'),
            dataIndex: 'registerTime',
            width: 100,
          },
          {
            title: L('状态'),
            dataIndex: 'isAllowLoginValue',
            width: 80,
          },
        ],
        actionColumn: {
          width: 120,
          title: L('操作'),
          dataIndex: 'action',
        },
        useSearchForm: true,
        formConfig: {
          schemas: [
            {
              field: 'vendorName',
              component: 'Input',
              colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                maxlength: 10,
                placeholder: L('请输入账号名称'),
                size: 'default',
              },
              label: L('账号名称'),
            },
          ],
        },
        bordered: true,
        striped: false,
        showIndexColumn: true,
      });

      const width = ref(550);
      const title = ref('');
      const visible = ref(false);
      const content = ref([]);
      const confirmBtnLoading = ref(false);
      const operate_id = ref('');
      const operate_type = ref('');

      const operate_data = ref([
        {
          type: 'input',
          label: L('商户帐号'),
          name: 'vendorName',
          placeholder: L('请输入商户帐号'),
          extra: L('最多输入10个字'),
          maxLength: 10,
          rules: [
            {
              required: true,
              whitespace: true,
              message: L('请输入商户帐号'),
            },
          ],
        },
        {
          type: 'input',
          input_type: 'password',
          label: L('登录密码'),
          name: 'vendorPassword',
          placeholder: L('请设置6-20位的登录密码'),
          maxLength: 20,
          rules: [
            {
              required: true,
              message: L('请设置登录密码'),
            },
            {
              validator: async(rule, value) =>  await validatorMemPwd(rule, value),
            },
          ],
        },
        {
          type: 'input',
          input_type: 'password',
          label: L('确认密码'),
          name: 'confirmPassword',
          placeholder: L('确认密码需要与密码一致'),
          maxLength: 20,
          rules: [
            {
              required: true,
              whitespace: true,
              message: L('请输入确认密码'),
            },
            {
              validator: (rule, value) => validatorMemPwd(rule, value),
            },
          ],
        },
        {
          type: 'input',
          label: L('手机号'),
          name: 'vendorMobile',
          placeholder: L('请输入手机号'),
          maxLength: 11,
          rules: [
            {
              required: true,
              whitespace: true,
              message: L('请输入手机号'),
            },
            {
              pattern: mobile_reg,
              message: L('请输入正确的手机号'),
            },
          ],
        },
        {
          type: 'input',
          label: L('邮箱'),
          name: 'vendorEmail',
          placeholder: L('请输入邮箱'),
          extra:L('请输入正确的邮箱'),
          rules: [
            {
              required: true,
              whitespace: true,
              message: L('请输入邮箱'),
            },
            {
              validator: async (rule, value) => await validatorVendorEmail(rule, value),
            },
          ],
        },
        {
          type: 'select',
          label: L('权限组'),
          name: 'rolesId',
          placeholder: L('请选择权限组'),
          selData: [],
          selKey: 'rolesId',
          selName: 'rolesName',
          diy: true,
          rules: [
            {
              required: true,
              message: L('请选择权限组'),
            },
          ],
          wrapperCol: { span: 16 },
        },
      ]); //新增、编辑、重置密码弹窗数据

      const operate_auth = ref([]); //授权弹窗数据

      //表格点击回调事件
      function handleClick(item, type) {
        operate_type.value = type;
        if (item) {
          operate_id.value = item.vendorId;
        }
        operate_data.value = operate_data.value.filter(it=>it.name!='code')
        if (type == 'add') {
          title.value = L('添加管理员');
          visible.value = true;
          let data = operate_data.value
          data.map((items) => {
            items.initialValue = undefined;
          });
          content.value = data
          let obj = {
            type: 'verify_code', //验证码
            label: '短信验证码',
            name: 'code',
            verification:true, //为true的话调的是另一个验证码接口
            mobile: 'vendorMobile', //关联的手机号字段
            placeholder: '请输入短信验证码',
            extra: ``,
            initialValue: '',
            maxLength: 6, //最多字数
            rules: [
              {
                required: true,
                whitespace: true,
                message: '请输入短信验证码',
              },
              {
                // @ts-ignore
                validator: async (rule, value) => {
                  await checkSmsCode(rule, value);
                },
                trigger: 'change',
              },
            ],
            callback: true,
          }
          for(let i in content.value){
            if(content.value[i].name == 'vendorMobile'){
              content.value.splice(Number(i)+1,0,obj)
              break
            }
          }
          let email_temp = content.value.filter((items) => items.name == 'email');
          if (email_temp.length > 0) {
            email_temp[0].rules = [
              {
                required: true,
                whitespace: true,
                message: L('请输入邮箱'),
              },
              {
                validator: (rule, value, callback) => validatorVendorEmail(rule, value, callback),
              },
            ];
          }
        } else if (type == 'edit') {
          title.value = L('编辑管理员');
          visible.value = true;
          let data = operate_data.value
          data = data.filter((item) => item.name != 'vendorPassword' && item.name != 'confirmPassword');
          data.map((items) => {
            items.initialValue = item[items.name] ? item[items.name] : undefined;
          });
          content.value = data;
        } else if (type == 'freeze') {
          operate_role({
            vendorId: item.vendorId,
            isFreeze: item.isAllowLogin == 1 ? true : false,
          });
        } else if (type == 'reset') {
          title.value = L('重置密码');
          visible.value = true;
          let data = operate_data.value
          data = data.filter((item) => item.name == 'vendorPassword' || item.name == 'confirmPassword');
          content.value = data;
        } else if (type == 'del') {
          operate_role({ vendorId: item.vendorId });
        }
      }

      //弹窗取消事件
      function handleCancle() {
        visible.value = false;
        content.value = [];
        operate_id.value = '';
        }

      //弹窗确认事件
      function handleConfirm(val) {
        if (val.vendorPassword && val.confirmPassword && val.vendorPassword != val.confirmPassword) {
          failTip(L('两次密码不一致，请重新输入'));
          return;
        }
        if (operate_type.value == 'add') {
          val.vendorPassword = base64Encrypt(val.vendorPassword);
          val.confirmPassword = base64Encrypt(val.confirmPassword);
          } else if (operate_type.value == 'edit') {
          val.vendorId = operate_id.value;
        } else if (operate_type.value == 'reset') {
          val.vendorId = operate_id.value;
          val.newPassword = base64Encrypt(val.vendorPassword);
          val.newPasswordCfm = base64Encrypt(val.confirmPassword);
          delete val.vendorPassword;
          delete val.confirmPassword;
        }
        operate_role(val);
      }

      //权限组操作
      const operate_role = async (params) => {
        confirmBtnLoading.value = true;
        let res = {};
        if (operate_type.value == 'add') {
          res = await addAdminUser(params);
        } else if (operate_type.value == 'edit') {
          res = await updateAdminUser(params);
        } else if (operate_type.value == 'freeze') {
          res = await freezeAdminUser(params);
        } else if (operate_type.value == 'reset') {
          res = await resetAdminUser(params);
        } else if (operate_type.value == 'del') {
          res = await delAdminUser(params);
        }
        confirmBtnLoading.value = false;
        if (res.state == 200) {
          handleCancle();
          sucTip(res.msg);
          reload();
        } else {
          failTip(res.msg);
        }
      };

      onMounted(() => {
        get_role_list();
      });

      //获取权限组列表数据
      const get_role_list = async () => {
        const res = await getRoleManageList({ pageSize: 10000 });
        if (res.state == 200) {
          let temp = operate_data.value.filter((item) => item.name == 'rolesId');
          if (temp.length > 0) {
            temp[0].selData = res.data.list;
          }
        } else {
          failTip(res.msg);
        }
      };

      
    const handleSmsCancle = (info)=> {
      }

      return {
        standardTable,
        width,
        title,
        visible,
        content,
        confirmBtnLoading,
        operate_id,
        operate_type,
        operate_data,
        operate_auth,
        handleClick,
        handleCancle,
        handleConfirm,
        get_role_list,
        L,
        handleSmsCancle,
        userStore,
        };
    },
  });
</script>
<style lang="less" scoped></style>
