<template>
  <div class="section_padding point_goods_list">
    <div class="section_padding_back">
      <SldComHeader
        :title="L('账号管理')"
      />
      <Tabs type="card" v-model:activeKey="activeKey" class="tabs_nav">
        <TabPane key="1" :tab="L('权限组')">
          <AccountGroup></AccountGroup>
        </TabPane>
        <TabPane key="2" :tab="L('子账号管理')">
          <AccountMember/>
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'StoreAccount',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, onMounted } from 'vue';
  import { Tabs, TabPane } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import AccountGroup from './group.vue';
  import AccountMember from './member.vue';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const activeKey = ref('1');


  onMounted(() => {
  });
</script>
<style lang="less">
  .point_goods_list {
    .tabs_nav {
      .ant-tabs-nav {
        margin-bottom: 0;
      }
    }
  }
</style>
