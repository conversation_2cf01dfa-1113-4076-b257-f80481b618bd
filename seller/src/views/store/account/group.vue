<template>
  <div class="">
    <div style="width: 100%; height: 5px"></div>
    <BasicTable @register="standardTable" style="padding: 0">
      <template #tableTitle>
        <div class="toolbar">
          <div class="toolbar_btn" @click="handleClick(null, 'add')">
            <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#fff" />
            <span>{{ L('新增权限组') }}</span>
          </div>
        </div>
      </template>
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex == 'action'">
          <TableAction
            class="TableAction"
            :actions="[
              {
                label: L('编辑'),
                onClick: handleClick.bind(null, record, 'edit'),
              },
              {
                label: L('授权'),
                onClick: handleClick.bind(null, record, 'auth'),
              },
              {
                label: L('接收消息'),
                onClick: setMsg.bind(null, record),
              },
              {
                label: L('删除'),
                popConfirm: {
                  title: L('删除后不可恢复，是否确定删除？'),
                  placement: 'left',
                  confirm: handleClick.bind(null, record, 'del'),
                },
              },
            ]"
          />
        </template>
        <template v-else-if="column.key">
          {{ text ? text : '--' }}
        </template>
      </template>
    </BasicTable>
    <SldModal
      :width="width"
      :title="title"
      :visible="visible"
      :visible_one="visible_one"
      :content="content"
      :conType="conType"
      :confirmBtnLoading="confirmBtnLoading"
      :showFoot="true"
      @cancle-event="handleCancle"
      @confirm-event="handleConfirm"
      @tree-select-event="handleTreeSelect"
    />
  </div>
</template>
<script>
  import { getCurrentInstance, defineComponent, ref, onMounted } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { Popconfirm } from 'ant-design-vue';
  import { sucTip, failTip } from '@/utils/utils';
  import {
    getRoleResource,
    getRoleManageList,
    addRole,
    updateRole,
    authRole,
    delRole,
    getSettingReceiveList,
    getSettingReceiveTypeApi,
  } from '/@/api/store/account';
  import SldModal from '@/components/SldModal/index.vue';

  export default defineComponent({
    components: {
      SldComHeader,
      BasicTable,
      Popconfirm,
      SldModal,
      TableAction
    },
    setup() {

      const vm = getCurrentInstance();
      const L = vm?.appContext.config.globalProperties.$sldComLanguage;

      const [standardTable, { reload }] = useTable({
        api: (arg) => getRoleManageList({ ...arg }),
        fetchSetting: {
          pageField: 'current',
          sizeField: 'pageSize',
          listField: 'data.list',
          totalField: 'data.pagination.total',
        },
        columns: [
          {
            title: L('权限组名称'),
            dataIndex: 'rolesName',
            width: 100,
          },
          {
            title: L('权限组描述'),
            dataIndex: 'description',
            width: 100,
          },
          {
            title: L('创建时间'),
            dataIndex: 'createTime',
            width: 100,
          },
          {
            title: L('更新时间'),
            dataIndex: 'updateTime',
            width: 100,
          },
        ],
        actionColumn: {
          width: 150,
          title: L('操作'),
          dataIndex: 'action',
        },
        useSearchForm: true,
        formConfig: {
          schemas: [
            {
              field: 'rolesName',
              component: 'Input',
              colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                maxlength: 10,
                placeholder: L('请输入权限组名称'),
                size: 'default',
              },
              label: L('权限组名称'),
              labelWidth: 90,
            },
          ],
        },
        bordered: true,
        striped: false,
        showIndexColumn: true,
        rowKey:'rolesId',
      });
      const width = ref(500);
      const title = ref('');
      const visible = ref(false);
      const content = ref([]);
      const confirmBtnLoading = ref(false);
      const operate_id = ref('');
      const operate_type = ref('');
      const visible_one = ref(false);
      const conType = ref(undefined);
      const permissionList = ref([]);

      const operate_add_edit = ref([
        {
          type: 'input',
          label: L('权限组名称'),
          name: 'rolesName',
          placeholder: L('请输入权限组名称'),
          extra: L('最多输入10个字'),
          maxLength: 10,
          showCount: true,
          rules:[
            {
              required: true,
              whitespace: true,
              message: L('请输入权限组名称'),
            },
          ]

        },
        {
          type: 'textarea',
          label: L('权限组描述'),
          name: 'description',
          placeholder: L('请输入权限组描述'),
          extra: L('最多输入50个字'),
          maxLength: 50,
          showCount: true,
          rules:[
            {
              required: true,
              whitespace: true,
              message: L('请输入权限组描述'),
            },
          ]
        },
      ]); //添加、编辑权限组弹窗数据

      const operate_auth = ref([
        {
          type: 'tree',
          label: '',
          name: 'resourceIds',
          checkable: true,
          treeData: [],
          fieldNames: {
            key: 'resourcesId',
            title: 'content',
            children: 'children',
          },
          initialValue: [],
          maxHeight: 400,
          callback: true,
        },
      ]); //授权弹窗数据

      //表格点击回调事件
      function handleClick(item, type) {
        operate_type.value = type;
        if (item) {
          operate_id.value = item.rolesId;
        }
        width.value = 500;
        if (type == 'add') {
          title.value = L('添加权限组');
          visible.value = true;
          content.value = JSON.parse(JSON.stringify(operate_add_edit.value));
        } else if (type == 'edit') {
          title.value = L('编辑权限组');
          visible.value = true;
          let data = JSON.parse(JSON.stringify(operate_add_edit.value));
          data.map((items) => {
            items.initialValue = item[items.name] ? item[items.name] : undefined;
          });
          content.value = data;
        } else if (type == 'auth') {
          title.value = L('授权');
          visible.value = true;
          let data = JSON.parse(JSON.stringify(operate_auth.value));
          data[0].initialValue = item.resourcesList;
          content.value = data;
        } else if (type == 'del') {
          operate_role({ rolesId: item.rolesId });
        }
      }

      //弹窗取消事件
      function handleCancle() {
        visible.value = false;
        content.value = [];
        visible_one.value = false
        operate_id.value = '';
        conType.value  = undefined
      }

      //弹窗确认事件
      function handleConfirm(val) {
        if (operate_type.value == 'add') {
          operate_role(val);
        } else if (operate_type.value == 'edit') {
          val.rolesId = operate_id.value;
          operate_role(val);
        } else if (operate_type.value == 'auth') {
          if (!val.resourceIds || val.resourceIds.length == 0) {
            failTip(L('请选择数据'));
          } else {
            val.roleId = operate_id.value;
            val.resourceIds = val.resourceIds.join(',');
            operate_role(val);
          }
        } else if (operate_type.value == 'setMsg') {
          let param = {}
          param.roleId = operate_id.value;
          let tplCodes = []
          let num = 0
          content.value.forEach(item=>{
            if(item.checkList.length==0){
              num+=1
            }
            tplCodes = [...tplCodes,...item.checkList]
          })
          if(num==content.value.length){
            failTip(L('请选择至少一个模板'));
            return
          }
          param.tplCodes = tplCodes.join(',')
          operate_role(param);
          return
        }
      }

      //权限组操作
      const operate_role = async (params) => {
        confirmBtnLoading.value = true;
        let res = {};
        if (operate_type.value == 'add') {
          res = await addRole(params);
        } else if (operate_type.value == 'edit') {
          res = await updateRole(params);
        } else if (operate_type.value == 'auth') {
          res = await authRole(params);
        } else if (operate_type.value == 'del') {
          res = await delRole(params);
        } else if (operate_type.value == 'setMsg') {
          res = await getSettingReceiveTypeApi(params);
        }
        confirmBtnLoading.value = false;
        if (res.state == 200) {
          sucTip(res.msg);
          handleCancle();
          reload();
        } else {
          failTip(res.msg);
        }
      };

      onMounted(() => {
        get_role_resource();
        get_receive_list();
      });

      //获取资源列表数据
      const get_role_resource = async () => {
        const res = await getRoleResource();
        if (res.state == 200) {
          operate_auth.value[0].treeData = res.data.list;
        }
      };

      //获取接收消息列表数据
    const get_receive_list = async () => {
      let res = await getSettingReceiveList({})
      if(res.state == 200){
        permissionList.value = []
        let init_com_data = []
        res.data.forEach((item,index)=>{
          let tmp_info = {};
          tmp_info.key = index;
          tmp_info.id = index;
          tmp_info.indeterminate = true;//有选中的为true
          tmp_info.checkList = [];
          tmp_info.sldchild = [];//下级数据
          tmp_info.name = item.tplName;
          //查看子元素是否有选中的
          tmp_info.checkAll = true;//是否全部选中
          item.storeTplList.forEach((cur_data,ind)=>{
            tmp_info.sldchild.push({ id: ind, key: ind, label: cur_data.tplName, value: cur_data.tplCode });
          })
          init_com_data.push(tmp_info)
        })
        permissionList.value = init_com_data
      }
    };

      function handleTreeSelect(val) {
        content.value[0].initialValue = val;
      }

      // 打开接收消息弹窗
      const setMsg = (val) => {
        operate_id.value = val.rolesId;
        let init_com_data = JSON.parse(JSON.stringify(permissionList.value));
        init_com_data.forEach((item) => {
          item.sldchild.forEach((it) => {
            if (!val.msgList || val.msgList.length == 0) {
              item.checkAll = false; //是否全部选中
            } else {
              if (val.msgList.indexOf(it.value) == -1) {
                item.checkAll = false;
              } else {
                item.checkList.push(it.value);
              }
            }
          });
          if (item.checkList.length == 0) {
            item.indeterminate = false;
          }
        });
        content.value = JSON.parse(JSON.stringify(init_com_data));
        conType.value = 'moreCheck';
        operate_type.value = 'setMsg';
        width.value = 700;
        title.value = '接收消息设置';
        visible.value = true;
        visible_one.value = true;
      };

      return {
        standardTable,
        get_role_resource,
        width,
        title,
        visible,
        content,
        confirmBtnLoading,
        operate_id,
        operate_type,
        operate_add_edit,
        operate_auth,
        handleClick,
        handleCancle,
        handleConfirm,
        handleTreeSelect,
        L,
        get_receive_list,
        permissionList,
        conType,
        visible_one,
        setMsg
      };
    },
  });
</script>
<style lang="less" scoped></style>
