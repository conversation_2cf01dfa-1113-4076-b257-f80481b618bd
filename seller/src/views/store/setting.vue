<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader :title="L('店铺设置')" />

      <Tabs type="card" v-model:activeKey="activeKey" class="tabs_nav" @change="tabsChange">
        <TabPane key="1" :tab="L('基本信息')">
          <div class="site_info_scroll" style="padding-top: 5px">
            <StandardTableRow
              v-if="activeKey==='1'"
              width="100%"
              :data="baseData"
              @submit-event="submitEvent"
              :showButton="true"
              buttonFixed="true"
            />
          </div>
        </TabPane>
        <TabPane key="2" :tab="L('店铺二维码')">
          <StandardTableRow
            v-if="activeKey==='2'"
            width="100%"
            :data="baseDataT"
            @submit-event="submitEvent"
            :showButton="true"
            buttonFixed="true"
          />
        </TabPane>
      </Tabs>

    </div>
  </div>
</template>
<script>
import { failTip, sucTip } from '@/utils/utils';
import { TabPane, Tabs } from 'ant-design-vue';
import { defineComponent, getCurrentInstance, onMounted, ref } from 'vue';
import { getStoreDetail, getStoreLabelList } from '/@/api/manage/manage';
import { getSettingDetailApi, get_store_QR, saveSetting, saveSettingQR } from '/@/api/sysset/sysset';
import SldComHeader from '/@/components/SldComHeader/index.vue';
import StandardTableRow from '/@/components/StandardTableRow/index.vue';
import { useGlobSetting } from '/@/hooks/setting';
import areaData from '@/assets/json/area.json';

export default defineComponent({
  components: {
    SldComHeader,
    StandardTableRow,
    Tabs,
    TabPane
  },
  setup () {

    const vm = getCurrentInstance();
    const L = vm?.appContext.config.globalProperties.$sldComLanguage;

    const { apiUrl } = useGlobSetting();
    const tabIndex = ref('1'); //tab下标
    const baseData = ref([]); //基本图片
    const defaultData = ref([]); //默认图片
    const storeLabelList = ref([]); //店铺标签列表

    const baseDataT = ref([
      {
        accept: ".jpg, .jpeg, .png, .gif",
        action: `${apiUrl}/v3/oss/seller/upload?source=logo`,
        fileList: [],
        desc: "",
        desc_width: 350,
        key: "QRCode",
        label: "店铺二维码",
        type: "upload_img",
        width: 300,
      },
      {
        type: 'button',
        width: 300,
        showSubmit: true,
        submitText: L('保存'),
        showCancle: false,
      }
    ]);

    const activeKey = ref('1');
    
    const storeNameFromApi = ref('');
    const storeDetail = ref(null);

    //获取店铺标签列表
    const get_store_label_list = async () => {
      const res = await getStoreLabelList();
      console.log('标签列表接口返回：', res);
      if (res.state == 200 && res.data?.list) {
        storeLabelList.value = res.data.list.map(item => ({
          key: item.labelId,
          title: item.labelName,
          value: item.labelId.toString()
        }));
        console.log('处理后的标签列表：', storeLabelList.value);
      }
    };

    //获取基本图片数据
    const get_vendor_base_info = async () => {
      const res = await getSettingDetailApi();
      console.log('店铺设置详情接口返回：', res.data);
      let settingMap = {
        storeName: {
          type: 'input',
          label: L('店铺名称'),
          width: 300,
          placeholder: L('请输入店铺名称'),
          desc: L('店铺名称长度为4-50个字符'),
          rules: [{ min: 4, max: 50, message: L('店铺名称长度为4-50个字符') }],
        },
        storeLabels: {
          type: 'select',
          label: L('店铺标签'),
          width: 300,
          placeholder: L('请选择店铺标签'),
          desc: L('可以选择多个标签'),
          mode: 'multiple',
          data: storeLabelList.value,
          value: res.data.storeLabelList?.map(item => item.labelId.toString()) || [],
        },
        mainBusiness: {
          type: 'textarea', 
          label: L('主营商品'),
          maxLength: 50,
          width: 300,
          autoSize: { minRows: 2, maxRows: 6 },
          placeholder: L('请输入店铺主营产品关键字'),
          desc: L('请输入店铺主营商品关键字，如需填写多个商品，请用英文符号","进行分割，如"戒指,项链,耳环"，最多可输入50字。主要在移动端店铺信息页展示'),
        },
        storeLogo: {
          type: 'upload_img',
          label: L('店铺Logo'),
          width: 300,
          desc: L('建议上传宽130像素*高130像素的图片'),
        },
        
        // dev_pc-start
        storeBannerPc: {
          type: 'upload_img',
          label: L('PC店铺横幅'),
          width: 300,
          desc: L('请上传1920*104的图片'),
        },
        // dev_pc-end
        // dev_mobile-start
        storeBannerMobile: {
          type: 'upload_img',
          label: L('移动端店铺横幅'),
          width: 300,
          desc: L('请上传750*253的图片'),
        },
        // dev_mobile-end
        storeThemeColor: {
          type: 'color',
          label: L('头部文字颜色设置'),
          width: 300,
          value: '#FFFFFF',
          defaultValue: '#FFFFFF',
          desc: L('用于设置移动端店铺头部的文字颜色'),
        },
        servicePhone: {
          type: 'input',
          label: L('店铺客服电话'),
          width: 300,
          placeholder: L('请输入店铺客服电话'),
          desc: L('请输入于交易联系的电话号码，方便买家进行咨询沟通'),
          rules: [{ pattern: /(1[3-9]\d{9}$)/, max: 11, message: L('请输入正确的电话号码') }],
        },
        vendorEmail: {
          type: 'input',
          label: L('店铺邮箱'),
          width: 300,
          placeholder: L('请输入店铺邮箱'),
          desc: L('主要用于接收消息通知'),
          rules: [{ pattern: /^(\w-*\.*)+@(\w-?)+(\.\w{2,})+$/, max: 11, message: L('请输入正确的邮箱') }],
        },
        
      };
    

      if (res.state == 200 && res.data) {
        let data = [];
        if(!res.data.shopType||res.data.shopType!=2){
          settingMap.defaultDeliveryMethod = {
            type: 'radio',
            label: '默认配送方式',
            width: 300,
            desc: L('确认订单页将默认选中您选择的配送方式'),
          }
          
            settingMap.defaultDeliveryMethod.data = [
              { key: 0, value: '1', title: '快递发货' },
            ]
          
        }

        for(let key in settingMap){
          // dev_supplier-start
          if(key == 'storeBannerMobile'&&res.data.shopType==3){
            continue
          }
          if(key == 'defaultDeliveryMethod'&&res.data.shopType==3){
            continue
          }
          // dev_supplier-end
          let itemObject = settingMap[key];
          let itemValue;
          
          if(key === 'storeName') {
            itemValue = storeNameFromApi.value;
          } else if(key === 'mainBusiness') {
            itemValue = storeDetail.value?.mainBusiness || '';
          } else if(key === 'storeLogo') {
            itemValue = res.data.storeLogo || '';
          } else {
            itemValue = res.data[key];
          }
          
          let obj = { desc_width: 350, key };
          if (itemObject.type == 'upload_img') {
            obj = Object.assign({}, obj, itemObject, {
              accept: '.jpg, .jpeg, .png, .gif',
              action: `${apiUrl}/v3/oss/seller/upload?source=logo`,
              fileList: [],
            });
            
             
              if (itemValue) {
                obj.fileList.push({
                  uid: res.data[`${key}Path`],
                  name: itemValue,
                  status: 'done',
                  url: res.data[`${key}Path`],
                });
              }
              
          } else {
            
                if(key=='defaultDeliveryMethod'){
                  obj = Object.assign({}, obj, itemObject);
                  obj.value = String(itemValue);
                } else if(key=='storeLabels') {
                  obj = Object.assign({}, obj, itemObject);
                  console.log('设置标签值：', {
                    storeLabelList: storeLabelList.value,
                    selectedLabels: itemValue,
                    finalObj: obj
                  });
                } else {
                  obj = Object.assign({}, obj, itemObject);
                  obj.value = itemValue;
                }
            
          }
          data.push(obj);
        }
        
        if (data.length > 0) {
          data.push({
            type: 'button',
            width: 300,
            showSubmit: true,
            submitText: L('保存'),
            showCancle: false,
          });
        }
        console.warn('baseData:',data)
        baseData.value = data;
      } else {
        failTip(res.msg);
      }
    };

    
   //获取站点基本配置
   const getSetting = async () => {
     await get_store_label_list(); // 先获取标签列表
     await getStoreInfo();
     await get_vendor_base_info();
   };

   const getSettingQR=async ()=>{
     const res = await get_store_QR();
     baseDataT.value=[
       {
         accept: ".jpg, .jpeg, .png, .gif",
         action: `${apiUrl}/v3/oss/seller/upload?source=logo`,
         fileList: [],
         desc: "",
         desc_width: 350,
         key: "QRCode",
         label: "店铺二维码",
         type: "upload_img",
         width: 300,
       },
       {
         type: 'button',
         width: 300,
         showSubmit: true,
         submitText: L('保存'),
         showCancle: false,
       }
     ]
     if (res.state == 200) {
       if( res.data.qrcode && res.data.qrcode!='' && res.data.qrcode!=null ){
         baseDataT.value[0].fileList=[{
           uid: res.data.qrcode,
           name: res.data.qrcode,
           status: 'done',
           url: res.data.qrcode,
           response:{
             data:{
               path:res.data.qrcode,
               url:res.data.qrcode,
             }
           }
         }]
       }
     }
   }

    onMounted(() => {
      getSetting();
    });

    const submitEvent = (data) => {
      let params = {};
      for (let key in data) {
        if (key === 'storeLabels') {
          params.storeLabelIds = data[key].join(',');
        } else if (typeof data[key] === 'object' && data[key] != null) {
          if(activeKey.value === '1'){
            params[key] = data[key].response?.data.path || data[key].name || '';
          } else {
            params[key] = data[key].response?.data.url || data[key].name || '';
          }
        } else {
          if(key === 'storeThemeColor' && data[key]) {
            params[key] = data[key].startsWith('#') ? data[key] : `#${data[key]}`;
          } else {
            params[key] = data[key] || '';
          }
        }
      }
      submitUpdateSetting(params);
    };

    //保存提交
    const submitUpdateSetting = async (params) => {
      let res
      if(activeKey.value==='1'){
        res = await saveSetting(params);
      }else{
        res = await saveSettingQR(params);
      }
      if (res.state == 200) {
        sucTip(res.msg);
      } else {
        failTip(res.msg);
      }
    };

    const tabsChange=(e)=>{
      if(e==1){
        getSetting();
      }else{
        getSettingQR();
      }
    }

    const getStoreInfo = async () => {
      const res = await getStoreDetail();
      if (res.state === 200 && res.data) {
        storeNameFromApi.value = res.data.storeName || '';
      }
    };

    const handleSubmit = () => {
      // 获取当前表单数据
      const formData = activeKey.value === '1' ? baseData.value : baseDataT.value;
      
      // 获取 StandardTableRow 组件的引用
      const tableRowRef = getCurrentInstance()?.refs.standardTableRow;
      
      // 从组件获取最新的表单值
      const formValues = tableRowRef?.getFormValues() || {};
      
      // 移除最后的按钮配置
      const data = formData.slice(0, -1).reduce((acc, item) => {
        const key = item.key;
        if(item.type === 'upload_img') {
          // 处理上传图片类型
          const fileList = item.fileList || [];
          acc[key] = fileList[0]?.response?.data?.path || fileList[0]?.url || '';
        } else {
          // 使用组件中最新的表单值
          acc[key] = formValues[key] || item.value || '';
        }
        return acc;
      }, {});

      submitEvent(data);
    };

    return {
      tabIndex,
      baseData,
      defaultData,
      submitEvent,
      submitUpdateSetting,
      activeKey,
      L,
      baseDataT,
      tabsChange,
      storeNameFromApi,
      handleSubmit,
      storeLabelList,
    };
  },
});
</script>
<style lang="less" scoped>
.section_padding_back {
  overflow-y: auto;
  padding-bottom: 60px;
}

.site_info_scroll {
  height: calc(100vh - 220px);
  overflow-y: auto;
}
</style>
