<template>
    <div class="p-2">
      <div class="bg-white p-2">
        <BasicTable @register="standardTable">
          <template #tableTitle>
            <div class="toolbar flex flex-row">
              <div class="toolbar_btn" @click="handleClick(null, 'add')">
                <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#fff" />
                <span>{{ L('申请经营类目') }}</span>
              </div>
            </div>
          </template>
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.key == 'action'">
              <div v-if="record.state != 2" class="flex flex-row items-center justify-center" style="flex-wrap: wrap">
                <Popconfirm
                  title="删除后不可恢复，是否确定删除？"
                  @confirm="handleClick(record, 'del')"
                >
                  <span class="common_page_edit cursor-pointer hover:text-[#FF6A12]">{{
                    L('删除')
                  }}</span>
                </Popconfirm>
              </div>
              <span v-else>--</span>
            </template>
            <template v-else-if="column.key == 'refuseReason'">
              {{ text ? text : '--' }}
            </template>
          </template>
        </BasicTable>
      </div>
      <Modal
        destroyOnClose
        :maskClosable="false"
        :width="500"
        :title="L('申请经营类目')"
        :visible="visible"
        :confirmLoading="confirmBtnLoading"
        @cancel="handleCancle"
        @ok="handleConfirm"
      >
        <div style="margin: 10px;max-height: 460px;overflow-y: auto;">
            <Tree
                checkable
                :tree-data="categoryList"
                :fieldNames="{ children: 'children', title: 'categoryName', key: 'categoryId' }"
                @check="treeCheck"
            ></Tree>
        </div>
      </Modal>
    </div>
  </template>
  
  <script setup>
    import { getCurrentInstance, onMounted, ref } from 'vue';
    import { Popconfirm, Modal, Tree } from 'ant-design-vue';
    import { BasicTable, useTable } from '/@/components/Table';
    import SldComHeader from '/@/components/SldComHeader/index.vue';
    import {
      getBindStoreCategory,
      applyBindStoreCategory,
      delBindStoreCategory,
    } from '@/api/decorate/deco';
    import { getApplyCategoryApi } from '@/api/settled/settled';
    import { sucTip, failTip } from '@/utils/utils';
  
    const vm = getCurrentInstance();
    const L = vm?.appContext.config.globalProperties.$sldComLanguage;
    const visible = ref(false);
    const confirmBtnLoading = ref(false);
    const columns = [
        { title: L('分类信息'), dataIndex: 'goodsCateName', width: 100 },
        { title: L('分佣比例'), dataIndex: 'scaling', width: 100 },
        { title: L('状态'), dataIndex: 'stateValue', width: 100 },
        { title: L('拒绝理由'), dataIndex: 'refuseReason', width: 100 },
    ];
    const categoryList = ref([]); //经营类目数据
    const categorySelect = ref([]); //已选经营类目数据
  
    const [standardTable, { reload, setLoading }] = useTable({
      api: getBindStoreCategory,
      columns: columns,
      fetchSetting: {
        pageField: 'current',
        sizeField: 'pageSize',
        listField: 'data.list',
        totalField: 'data.pagination.total',
      },
      actionColumn: {
        width: 80,
        title: L('操作'),
        dataIndex: 'action',
        align: 'center',
      },
      pagination: {
        pageSize: 10,
      },
      bordered: true,
      striped: false,
      useSearchForm: true,
      formConfig: {
        schemas: [
          {
            field: 'state',
            component: 'Select',
            colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
            componentProps: {
                placeholder: '请选择申请状态',
                minWidth: 300,
                maxlength: 6,
                size: 'default',
                options: [
                { label: '全部', value: '' },
                { label: '待审核', value: 1 },
                { label: '审核通过', value: 2 },
                { label: '审核失败', value: 3 },
                ],
            },
            label: '申请状态',
            labelWidth: 80,
          },
        ],
      },
    });
  
    const handleClick = (record, type) => {
      if (type == 'add') {
        visible.value = true;
      } else if (type == 'del') {
        setLoading(true);
        delBindStoreCategory({ bindId: record.bindId }).then((res) => {
          setLoading(false);
          if (res.state == 200) {
            reload();
            sucTip(res.msg);
          } else {
            failTip(res.msg);
          }
        });
      }
    };

    const treeCheck = (checkedKeys, e) => {
      let select_cat_id = [];
      if (e.checkedNodes.length > 0) {
        e.checkedNodes.map(item_one => {
          if (item_one.grade == 3) {
            let tmp_data = item_one.path.split('/');
            select_cat_id.push(`${tmp_data[1]}-${tmp_data[2]}-${item_one.categoryId}`);
          }
        })
      }
      categorySelect.value = select_cat_id;
    }

    const handleCancle = () => {
        visible.value = false;
        categorySelect.value = [];
    };

    const handleConfirm = async () => {
      if (categorySelect.value.length == 0) {
        failTip(L('请选择经营类目'));
        return;
      }
      confirmBtnLoading.value = true;
      const res = await applyBindStoreCategory({ goodsCateIds: categorySelect.value.join(',') });
      confirmBtnLoading.value = false;
      if (res?.state == 200) {
        handleCancle();
        reload();
      } else {
        failTip(res.msg);
      }
    };

    //获取经营类目数据
    const get_category_list = async () => {
      const res = await getApplyCategoryApi();
      if (res.state == 200) {
        categoryList.value = res.data;
      }
    };

    onMounted(() => {
        get_category_list();
    })
  </script>
  
  <style lang="less" scoped></style>
  