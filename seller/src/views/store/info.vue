<template>
    <div class="section_padding point_goods_list">
      <div class="section_padding_back">
        <SldComHeader
          :title="L('店铺信息管理')"
        />
        <Tabs type="card" v-model:activeKey="activeKey" class="tabs_nav">
          <TabPane key="1" :tab="L('店铺信息')">
            <StoreInfo />
          </TabPane>
          <TabPane key="2" :tab="L('申请续签')">
            <StoreApply />
          </TabPane>
          <TabPane key="3" :tab="L('经营类目')">
            <StoreCategory />
          </TabPane>
        </Tabs>
      </div>
    </div>
  </template>
  <script>
    export default {
      name: 'StoreInfo',
    };
  </script>
  <script setup>
    import { getCurrentInstance, ref, onMounted } from 'vue';
    import { Tabs, TabPane } from 'ant-design-vue';
    import SldComHeader from '/@/components/SldComHeader/index.vue';
    import StoreInfo from './store_info.vue';
    import StoreApply from './store_apply.vue';
    import StoreCategory from './store_category.vue';
  
    const vm = getCurrentInstance();
    const L = vm?.appContext.config.globalProperties.$sldComLanguage;
  
    const activeKey = ref('1');
  
    onMounted(() => {

    });
  </script>
  <style lang="less">
    .point_goods_list {
      .tabs_nav {
        .ant-tabs-nav {
          margin-bottom: 0;
        }
      }
    }
  </style>
  