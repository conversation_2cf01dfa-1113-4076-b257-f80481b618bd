<template>
  <div class="section_padding point_goods_list">
    <div class="section_padding_back">
      <SldComHeader :title="L('消息列表')" />
      <BasicTable @register="standardTable" style="padding: 0">
        <template #tableTitle>
          <div class="toolbar">
            <Popconfirm
              :title="L('确认删除选中的消息吗？')"
              @confirm="handleClick(record, 'del')"
              :disabled="selectedRowKeys.length == 0"
            >
              <div
                class="toolbar_btn"
                @click="() => (selectedRowKeys.length == 0 ? handleClick(record, 'del') : null)"
              >
                <AliSvgIcon
                  iconName="iconshenhejujue"
                  width="15px"
                  height="15px"
                  fillColor="#fa0920"
                />
                <span>{{ L('批量删除') }}</span>
              </div>
            </Popconfirm>
            <Popconfirm
              :title="L('确认将选中的消息标为已读吗？')"
              @confirm="handleClick(record, 'read')"
              :disabled="selectedRowKeys.length == 0"
            >
              <div
                class="toolbar_btn"
                @click="() => (selectedRowKeys.length == 0 ? handleClick(record, 'read') : null)"
              >
                <AliSvgIcon
                  iconName="iconshenhetongguo"
                  width="19px"
                  height="19px"
                  fillColor="#0fb39a"
                />
                <span>{{ L('批量标为已读') }}</span>
              </div>
            </Popconfirm>
            <Popconfirm
              :title="L('确认将全部的消息标为已读吗？')"
              @confirm="handleClick(record, 'allRead')"
              :disabled="list.length == 0"
            >
              <div
                class="toolbar_btn"
                @click="() => (list.length == 0 ? handleClick(record, 'allRead') : null)"
              >
                <img
                  src="/src/assets/images/message_read.png"
                  alt=""
                  style="width: 15px; height: 15px"
                />
                <span>{{ L('全部标为已读') }}</span>
              </div>
            </Popconfirm>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex == 'action'">
            <TableAction
              class="TableAction"
              :actions="[
                {
                  onClick: goNextPage.bind(null, record, index),
                  label: L('查看'),
                },
              ]"
            />
          </template>
          <template v-else-if="column.key">
            {{ text ? text : '--' }}
          </template>
        </template>
      </BasicTable>
    </div>
  </div>
</template>
<script>
export default {
  name: 'StoreMsgLists',
};
</script>
<script setup>
import { getCurrentInstance, ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import SldComHeader from '/@/components/SldComHeader/index.vue';
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { Popconfirm } from 'ant-design-vue';
import { selectRadio, SelectAll, sucTip, failTip } from '/@/utils/utils';
import {
  getMsgListApi,
  getMsgDelApi,
  getMsgReadApi,
  getMsgTplTypeListApi,
} from '/@/api/store/msg_lists';

const vm = getCurrentInstance();
const L = vm?.appContext.config.globalProperties.$sldComLanguage;

const selectedRowKeys = ref([]);
const selectedRows = ref([]);
const list = ref([]);
const router = useRouter();

const [standardTable, { reload, getRawDataSource,getForm }] = useTable({
  api: (arg) => getMsgListApi({ ...arg }),
  clickToRowSelect: false,
  ellipsis: false,
  fetchSetting: {
    pageField: 'current',
    sizeField: 'pageSize',
    listField: 'data.list',
    totalField: 'data.pagination.total',
  },
  // 点击搜索前处理的参数
  beforeFetch(values) {
    selectedRowKeys.value = [];
    selectedRows.value = [];
    values.startTime = values.startTime ? values.startTime.split(' ')[0] + ' 00:00:00' : undefined;
    values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
    return values;
  },
  afterFetch: () => {
    if (
      getRawDataSource() &&
      getRawDataSource().data &&
      getRawDataSource().data.list &&
      getRawDataSource().data.list.length > 0
    ) {
      list.value = getRawDataSource().data.list;
    }
  },
  columns: [
    {
      title: L('通知内容'),
      dataIndex: 'msgContent',
      width: 200,
    },
    {
      title: L('通知类型'),
      dataIndex: 'tplTypeName',
      width: 100,
    },
    {
      title: L('通知状态'),
      dataIndex: 'msgStateValue',
      width: 100,
    },
    {
      title: L('通知时间'),
      dataIndex: 'msgSendTime',
      width: 100,
    },
  ],
  actionColumn: {
    width: 100,
    title: L('操作'),
    dataIndex: 'action',
  },
  useSearchForm: true,
  formConfig: {
    schemas: [
      {
        component: 'Select',
        label: L('通知类型'),
        field: 'tplType',
        componentProps: {
          placeholder: L('请选择通知类型'),
          options: [],
        },
      },
      {
        component: 'Select',
        label: L('通知状态'),
        field: 'msgState',
        componentProps: {
          placeholder: L('请选择通知状态'),
          options: [
            { value: '', label: L('全部') },
            { value: '1', label: L('已读') },
            { value: '2', label: L('未读') },
          ],
        },
      },
      {
        component: 'RangePicker',
        label: L('通知时间'),
        field: '[startTime,endTime]',
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: [L('开始时间'), L('结束时间')],
        },
      },
    ],
  },
  bordered: true,
  striped: false,
  showIndexColumn: true,
  rowKey: 'receiveId',
  rowSelection: {
    type: 'checkbox',
    selectedRowKeys: selectedRowKeys,
    onSelect: onSelect,
    onSelectAll: onSelectAll,
  },
});
const operate_type = ref('');

//表格点击回调事件
const handleClick = async (item, type) => {
  if (selectedRowKeys.value.length == 0 && type != 'allRead') {
    failTip(L('请先选中数据'));
    return;
  }
  if (type == 'allRead' && list.value == 0) {
    failTip(L('暂无消息'));
    return;
  }
  operate_type.value = type;
  let param = {};
  let res;
  if (type == 'del') {
    param.receiveIds = selectedRowKeys.value.join(',');
    res = await getMsgDelApi(param);
  } else if (type == 'read') {
    param.receiveIds = selectedRowKeys.value.join(',');
    res = await getMsgReadApi(param);
  } else if (type == 'allRead') {
    param.isAllRead = true;
    res = await getMsgReadApi(param);
  }
  if (res.state == 200) {
    sucTip(res.msg);
    reload();
    selectedRowKeys.value = [];
    selectedRows.value = [];
  } else {
    failTip(res.msg);
  }
};

// 单选多选事件
function onSelect(record, selected) {
  let rows = selectRadio(selectedRowKeys.value, selectedRows.value, 'receiveId', record, selected);
  selectedRowKeys.value = rows.selectedRowKeys;
  selectedRows.value = rows.selectedRows;
}

// 全选按钮事件
function onSelectAll(selected, rows, changeRows) {
  let rowAll = SelectAll(
    selectedRowKeys.value,
    selectedRows.value,
    'receiveId',
    selected,
    rows,
    changeRows,
  );
  selectedRowKeys.value = rowAll.selectedRowKeys;
  selectedRows.value = rowAll.selectedRows;
}

const goNextPage = async (record, index) => {
  let msgLinkInfo = JSON.parse(record.msgLinkInfo.replace(/&quot;/g, '"'));
  let afsSn = msgLinkInfo.afsSn;
  let type = msgLinkInfo.type;
  let orderSn = msgLinkInfo.orderSn;
  let _url = '';
  if (record.msgState == 1) {
    switch (type) {
      case 'refund_news':
        _url = '/order/service_refund_lists_to_detail?afsSn=' + afsSn;
        break;
      case 'return_news':
        _url = '/order/service_return_lists_to_detail?afsSn=' + afsSn;
        break;
      case 'goods_violation_news':
        _url = '/goods/goods_list';
        break;
      case 'goods_news':
        _url = '/goods/goods_list';
        break;
      case 'goods_audit_news':
        _url = '/goods/goods_check_list';
        break;
      case 'order_news':
        _url = '/order/order_lists_to_detail?orderSn=' + orderSn;
        break;
      case 'bill_news':
        _url = '/bill/lists_to_detail?id=' + msgLinkInfo.billId;
        break;
    }
    if (_url) {
      router.push(_url);
    }
  } else {
    let param_data = { receiveIds: record.receiveId };
    let res = await getMsgReadApi(param_data);
    if (res.state == 200) {
      switch (type) {
        case 'refund_news':
          _url = '/order/service_refund_lists_to_detail?afsSn=' + afsSn;
          break;
        case 'return_news':
          _url = '/order/service_return_lists_to_detail?afsSn=' + afsSn;
          break;
        case 'goods_violation_news':
          _url = '/goods/goods_list';
          break;
        case 'goods_news':
          _url = '/goods/goods_list';
          break;
        case 'goods_audit_news':
          _url = '/goods/goods_check_list';
          break;
        case 'order_news':
          _url = '/order/order_lists_to_detail?orderSn=' + orderSn;
          break;
        case 'bill_news':
          _url = '/bill/lists_to_detail?id=' + msgLinkInfo.billId;
          break;
      }
      if (_url) {
        router.push(_url);
      }
    } else {
      failTip(res.msg);
    }
  }
};

const getMsgTplTypeList = async()=> {
  const { updateSchema } = getForm();
  let res = await getMsgTplTypeListApi({})
  if(res.state == 200){
    res.data.map(item=>{
      item.value = item.tplTypeCode
      item.label = item.tplName
    })
    await updateSchema({
      field: 'tplType',
      componentProps: {
        options: [
          { value: '', label: L('全部') },
          ...res.data
        ],
      },
    });
  }
}

onMounted(() => {
  getMsgTplTypeList()
});
</script>
<style lang="less">
.point_goods_list {
  .tabs_nav {
    .ant-tabs-nav {
      margin-bottom: 0;
    }
  }
}
</style>
