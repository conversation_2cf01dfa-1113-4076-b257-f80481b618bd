<template>
  <div class="adv_13_wrap">
    <div class="item right">
      <MaskEdit :config="adv13LeftTitleConfig">
        <div class="title_wrap">
          <a class="title">
            {{
              left.title_info.title.initialValue
                ? left.title_info.title.initialValue
                : $sldComLanguage('添加标题')
            }}
          </a>
          <span>》</span>
          <a class="subtitle">
            {{
              left.title_info.title.initialValue
                ? left.title_info.sub_title.initialValue
                : $sldComLanguage('添加子标题')
            }}
          </a>
        </div>
      </MaskEdit>

      <MaskEdit :config="adv13LeftTopConfig">
        <div class="img_top">
          <ImgWrap
            v-for="(item, index) in left.top.data"
            :key="index"
            :src="item.imgUrl"
            :width="left.top.width"
            :height="left.top.height"
            mode="contain"
          ></ImgWrap>
        </div>
      </MaskEdit>

      <MaskEdit :config="adv13LeftBottomConfig">
        <div class="img_bottom flex_row_between_center">
          <ImgWrap
            v-for="(item, index) in left.bottom.data"
            :key="index"
            :src="item.imgUrl"
            :width="left.bottom.width"
            :height="left.bottom.height"
            mode="contain"
          ></ImgWrap>
        </div>
      </MaskEdit>
    </div>

    <div class="item right">
      <MaskEdit :config="adv13CenterTitleConfig">
        <div class="title_wrap">
          <a class="title">
            {{
              center.title_info.title.initialValue
                ? center.title_info.title.initialValue
                : $sldComLanguage('添加标题')
            }}
          </a>
          <span>》</span>
          <a class="subtitle">
            {{
              center.title_info.title.initialValue
                ? center.title_info.sub_title.initialValue
                : $sldComLanguage('添加子标题')
            }}</a
          >
        </div>
      </MaskEdit>

      <MaskEdit :config="adv13CenterTopConfig">
        <div class="img_top">
          <ImgWrap
            v-for="(item, index) in center.top.data"
            :key="index"
            :src="item.imgUrl"
            :width="center.top.width"
            :height="center.top.height"
            mode="contain"
          ></ImgWrap>
        </div>
      </MaskEdit>

      <MaskEdit :config="adv13CenterBottomConfig">
        <div class="img_bottom flex_row_between_center">
          <ImgWrap
            v-for="(item, index) in center.bottom.data"
            :key="index"
            :src="item.imgUrl"
            :width="center.bottom.width"
            :height="center.bottom.height"
            mode="contain"
          ></ImgWrap>
        </div>
      </MaskEdit>
    </div>

    <div class="item left clear_padding">
      <MaskEdit :config="adv13rightConfig">
        <ImgWrap
          v-for="(item, index) in right.data"
          :key="index"
          :src="item.imgUrl"
          :width="right.width"
          :height="right.height"
          mode="contain"
        ></ImgWrap>
      </MaskEdit>
    </div>
  </div>
</template>

<script setup>
  import { getCurrentInstance, provide, reactive, toRefs, unref } from 'vue';
  import {
    createTplContext,
    dfEmit,
    dfProps,
    formMoreImgConfig,
    formSingleImgConfig,
    formTitleLinkConfig,
  } from './actions/edit_tpl';
  import MaskEdit from './components/MaskEdit.vue';
  import ImgWrap from './components/ImgWrap.vue';

  const vm = getCurrentInstance();
  const { $sldComLanguage } = vm?.appContext.config.globalProperties;

  const props = defineProps(dfProps);
  const emit = defineEmits(dfEmit);

  const { left, center, right } = toRefs(props.tpl_info);

  //左侧标题设置
  const adv13LeftTitleConfig = formTitleLinkConfig('左侧标题设置', 'left_title', {
    extra: { title_limit: 5, sub_title_limit: 10 },
    modal_tip: ['标题不能为空，最多输入5个字，子标题不能为空，最多输入10个字'],
    content: {
      ...unref(left).title_info,
    },
  });

  // 左侧上部图片
  const adv13LeftTopConfig = formSingleImgConfig('左侧上部图片设置', 'left_top', {
    width: 376,
    height: 180,
    show_width: 376,
    show_height: 180,
    data: unref(left).top.data[0],
  });

  const adv13LeftBottomConfig = formMoreImgConfig(
    '左侧下部图片设置',
    'left_bottom',
    {
      show_width: 183,
      show_height: 180,
      width: 183,
      height: 180,
      data: unref(left).bottom.data,
    },
    [],
    800,
    {totalNum:2}
  );

  //中间标题设置
  const adv13CenterTitleConfig = formTitleLinkConfig('中间标题设置', 'center_title', {
    modal_tip: ['标题不能为空，最多输入5个字，子标题不能为空，最多输入10个字'],
    extra: { title_limit: 5, sub_title_limit: 10 },
    content: {
      ...unref(center).title_info,
    },
  });

  // 中间上部图片
  const adv13CenterTopConfig = formSingleImgConfig('中间上部图片设置', 'center_top', {
    width: 376,
    height: 180,
    data: unref(center).top.data[0],
  });

  const adv13CenterBottomConfig = formMoreImgConfig(
    '中间下部图片设置',
    'center_bottom',
    {
      show_width: 183,
      show_height: 180,
      width: 183,
      height: 180,
      data: unref(center).bottom.data,
    },
    [],
    800,
    {totalNum:2}
  );

  //右侧图片设置
  const adv13rightConfig = formSingleImgConfig('右侧图片设置', 'right', {
    width: 396,
    height: 450,
    show_width: 396,
    show_height: 450,
    data: unref(right).data[0],
  });

  const editConfirmHandler = (values, { position }) => {
    switch (position) {
      case 'left_title': {
        left.value.title_info.title.initialValue = values[0].title;
        left.value.title_info.sub_title.initialValue = values[0].sub_title;
        left.value.title_info.link_type = values[0].link_type;
        left.value.title_info.link_value = values[0].link_value;
        left.value.title_info.info = values[0].info;
        adv13LeftTitleConfig.content = { ...left.value.title_info };
        break;
      }

      case 'left_top': {
        unref(left).top.data = values;
        adv13LeftTopConfig.content.data = values[0];
        break;
      }

      case 'left_bottom': {
        unref(left).bottom.data = values[0].parent_data;
        adv13LeftBottomConfig.content.data = values[0].parent_data;
        break;
      }

      case 'center_title': {
        center.value.title_info.title.initialValue = values[0].title;
        center.value.title_info.sub_title.initialValue = values[0].sub_title;
        center.value.title_info.link_type = values[0].link_type;
        center.value.title_info.link_value = values[0].link_value;
        center.value.title_info.info = values[0].info;
        adv13CenterTitleConfig.content = { ...center.value.title_info };
        break;
      }

      case 'center_top': {
        unref(center).top.data = values;
        adv13CenterTopConfig.content.data = values[0];
        break;
      }

      case 'center_bottom': {
        unref(center).bottom.data = values[0].parent_data;
        adv13CenterBottomConfig.content.data = values[0].parent_data;
      }

      case 'right': {
        unref(right).data = values;
        adv13rightConfig.content.data = values[0];
        break;
      }
    }
    emit('save_tpl_data', props.tpl_info);
  };

  createTplContext(props.mode, editConfirmHandler);
</script>

<style lang="less" scoped>
  /* adv_13-start */
  .adv_13_wrap {
    position: relative;
    width: 100%;
    width: 1210px;
    margin: 0 auto;
    clear: both;
    overflow: hidden;
  }

  .adv_13_wrap .item {
    position: relative;
    width: 396px;
    height: 450px;
    padding: 10px;
    float: left;
    background-color: #fff;
  }

  .adv_13_wrap .item:first-child {
    margin-right: 10px;
  }

  .adv_13_wrap .item:last-child {
    margin-left: 10px;
  }

  .adv_13_wrap .item.clear_padding {
    padding: 0;
  }

  .adv_13_wrap .item .l_img {
    display: block;
    position: relative;
    width: 396px;
    height: 450px;
    overflow: hidden;
  }

  .adv_13_wrap .item .l_img::before {
    content: '';
    position: absolute;
    top: 0;
    left: -150px;
    width: 80px;
    height: 450px;
    overflow: hidden;
    transform: skewX(-25deg);
    background: -webkit-gradient(
      linear,
      left top,
      right top,
      color-stop(0%, rgb(255 255 255 / 0%)),
      color-stop(50%, rgb(255 255 255 / 20%)),
      color-stop(100%, rgb(255 255 255 / 0%))
    );
    background: linear-gradient(
      left,
      rgb(255 255 255 / 0%) 0,
      rgb(255 255 255 / 20%) 50%,
      rgb(255 255 255 / 0%) 100%
    );
  }

  .adv_13_wrap .item.left a:hover {
    color: #0c0c0c;
  }

  .adv_13_wrap .item.left a:hover::before {
    left: 450px;
    transition: left 0.5s;
  }

  .adv_13_wrap .item a {
    display: inline-block;
    position: relative;
    width: 100%;
    height: 100%;
    background: #eee !important;
    text-align: center;
  }

  .adv_13_wrap .item a img {
    max-width: 100%;
    max-height: 100%;
  }

  .adv_13_wrap .item a span {
    display: block;
    position: absolute;
    top: 50%;
    width: 100%;
    margin-top: -12px;
    color: #777;
    font-size: 13px !important;
    line-height: 24px;
  }

  .adv_13_wrap .item.center a {
    display: block;
    width: 183px;
    height: 210px;
    margin-bottom: 10px;
    float: left;
  }

  .adv_13_wrap .item.center a,
  .adv_13_wrap .item.right .img_top a,
  .adv_13_wrap .item.right .img_bottom a {
    transition: transform 0.5s, -moz-transform 0.5s;
    transition: -webkit-transform 0.5s;
    transition: transform 0.5s;
    transition: transform 0.5s, -webkit-transform 0.5s, -moz-transform 0.5s;
  }

  .adv_13_wrap .item.center a.l_b_margin {
    margin-bottom: 10px;
    margin-left: 10px;
  }

  .adv_13_wrap .item.center a:hover,
  .adv_13_wrap .item.right .img_top a:hover,
  .adv_13_wrap .item.right .img_bottom a:hover {
    transform: scale(1.05);
  }

  .adv_13_wrap .item.right .title_wrap {
    position: relative;
    height: 50px;
    line-height: 50px;
  }

  .adv_13_wrap .item.right .title_wrap .title {
    display: inline;
    color: #666;
    font-size: 19px;
  }

  .adv_13_wrap .item.right .title_wrap .title span {
    margin: 0 18px 0 5px;
  }

  .adv_13_wrap .item.right .title_wrap .subtitle {
    display: inline;
    color: #666;
    font-size: 12px;
  }

  .adv_13_wrap .item.right .img_top {
    position: relative;
    width: 376px;
    height: 180px;
    margin: 10px 0;
  }

  .adv_13_wrap .item.right .img_bottom {
    position: relative;
    width: 376px;
    height: 180px;
  }

  .adv_13_wrap .item.right .img_bottom a {
    position: relative;
    width: 183px;
    height: 180px;
    float: left;
    text-align: center;
  }

  .adv_13_wrap .item.right .img_bottom a:last-child {
    margin-left: 10px;
  }

  /* adv_13-end */
</style>
