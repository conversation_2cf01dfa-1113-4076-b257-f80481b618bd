<template>
  <div class="adv_11_wrap flex_row_between_center">
    <MaskEdit :config="adv11LeftConfig">
      <div class="item row_left">
        <ImgWrap
          v-for="(item, index) in row_left.data"
          :key="index"
          :src="item.imgUrl"
          :width="row_left.width"
          :height="row_left.height"
          class="row_img"
        ></ImgWrap>
      </div>
    </MaskEdit>

    <div class="item row_right">
      <MaskEdit :config="adv11RightTopConfig">
        <div class="top">
          <ImgWrap
            v-for="(item, index) in row_right.top.data"
            :key="index"
            :src="item.imgUrl"
            :width="row_right.top.width"
            :height="row_right.top.height"
          ></ImgWrap>
        </div>
      </MaskEdit>
      <MaskEdit :config="adv11RightBottomConfig">
        <div class="bottom">
          <ImgWrap
            v-for="(item, index) in row_right.bottom.data"
            :key="index"
            :src="item.imgUrl"
            :width="row_right.bottom.width"
            :height="row_right.bottom.height"
          ></ImgWrap>
        </div>
      </MaskEdit>
    </div>
  </div>
</template>

<script setup>
  import { provide, toRefs, unref, reactive } from 'vue';
  import {
    createTplContext,
    dfEmit,
    dfProps,
    formMoreImgConfig,
    formSingleImgConfig,
  } from './actions/edit_tpl';
  import ImgWrap from './components/ImgWrap.vue';
  import MaskEdit from './components/MaskEdit.vue';
  const props = defineProps(dfProps);
  const emit = defineEmits(dfEmit);

  const { row_right, row_left } = toRefs(props.tpl_info);

  const formMultiImgConfig = (title, position, variant, width) => {
    let content = {
      show_height: variant.height,
      show_width: variant.width,
      height: variant.height,
      width: variant.width,
      data: variant.data,
    };
    return formMoreImgConfig(title, position, content, [], 1400);
  };

  const adv11LeftConfig = formMultiImgConfig('左侧图片设置', 'left', { ...unref(row_left) }, 1400);

  const adv11RightTopConfig = formSingleImgConfig('右侧上部图片设置', 'right_top', {
    width: 400,
    height: 350,
    data: unref(row_right).top.data[0],
  });

  const adv11RightBottomConfig = formSingleImgConfig('右侧下部图片设置', 'right_bottom', {
    width: 400,
    height: 170,
    data: unref(row_right).bottom.data[0],
  });

  const editConfirmHandler = (values, { position }) => {
    let [value] = values;
    switch (position) {
      case 'left': {
        unref(row_left).data = value.parent_data;
        break;
      }

      case 'right_top': {
        unref(row_right).top.data = values;
        adv11RightTopConfig.content.data = values[0];
        break;
      }

      case 'right_bottom': {
        unref(row_right).bottom.data = values;
        adv11RightBottomConfig.content.data = values[0];
        break;
      }
    }
    emit('save_tpl_data', props.tpl_info);
  };
  createTplContext(props.mode, editConfirmHandler);
</script>

<style lang="scss" scoped>
  /* adv_11-start */
  .adv_11_wrap {
    position: relative;
    width: 100%;
    width: 1210px;
    margin: 0 auto;
    clear: both;
    overflow: hidden;
  }

  .item .row_img {
    position: relative;
    transition: transform 0.5s;
    background: #fff;

    &:hover {
      transform: translateX(-4px);
    }
  }

  .row_left {
    display: grid;
    grid-template-columns: auto auto;

    .row_img {
      margin: 2px;
      margin-bottom: 8px;

      &:nth-last-child(1),
      &:nth-last-child(2) {
        margin-bottom: 0;
      }
    }
  }

  .adv_11_wrap .row_right {
    margin-left: 5px;
  }

  .adv_11_wrap .row_left a {
    display: block;
    width: 395px;
    height: 170px;
    float: left;
  }

  .adv_11_wrap .row_right .top {
    position: relative;
    height: 350px;
  }

  .adv_11_wrap .row_right .top a {
    display: block;
    width: 400px;
    height: 350px;
  }

  .adv_11_wrap .row_right .bottom {
    position: relative;
    height: 170px;
    margin-top: 10px;
  }

  .adv_11_wrap .row_right .bottom a {
    display: block;
    width: 400px;
    height: 170px;
  }

  /* adv_11-end */
</style>
