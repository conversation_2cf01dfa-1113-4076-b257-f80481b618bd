<template>
  <div class="adv_04_wrap">
    <MaskEdit :config="formTitleConfig('顶部标题设置', 'top_title', adv04TitleContent)">
      <div class="floor_title">
        <h2>
          <font :style="{ background: title_info.title_color }">&nbsp;</font>
          <span :style="{ color: title_info.title_color }">{{
            title_info.title_name || $sldComLanguage('请填写标题')
          }}</span>
          <font :style="{ background: title_info.title_color }">&nbsp;</font>
        </h2>
      </div>
    </MaskEdit>

    <MaskEdit
      :config="
       adv04GoodsContent()
      "
    >
      <div class="floor_goods flex_row_start_center">
        <div v-for="(item, index) in data.goods_data" :key="index" class="adv_04_goods">
          <Goods
            :goods-info="item"
            mode="vertical"
            :imgInfo="imgInfo"
            :gap="26"
            :name-style="goodsNameStyle"
            :price-style="goodsPriceStyle"
          ></Goods>
        </div>
      </div>
    </MaskEdit>
  </div>
</template>

<script setup>
  import { getCurrentInstance, provide, reactive, toRefs, unref } from 'vue';
  import Goods from './components/GoodsWrap.vue';
  import MaskEdit from './components/MaskEdit.vue';
  import {
    dfProps,
    dfEmit,
    formTitleConfig,
    formGoodsConfig,
    createTplContext,
  } from './actions/edit_tpl';

  const vm = getCurrentInstance();
  const { $sldComLanguage } = vm?.appContext.config.globalProperties;

  const props = defineProps(dfProps);
  const emit = defineEmits(dfEmit);

  const { title_info, data } = toRefs(props.tpl_info);

  const flr_default = reactive({
    goods_list: Array(5).fill({
      goodsName: $sldComLanguage('商品名称'),
      goodsPrice: 0,
      goodsImage: '',
    }),
  });

  if (!unref(data).goods_data.length) {
    unref(data).goods_data = flr_default.goods_list;
  }

  const imgInfo = {
    width: 172,
    height: 170,
    cover: 'cover',
  };

  const goodsNameStyle = {
    height: '38px',
    maxHeight: '38px',
    marginBottom: '18px',
    textAlign: 'center',
    color: '#000',
    fontSize: '12px',
  };

  const goodsPriceStyle = {
    textAlign: 'center',
    width: '100%',
    fontSize: '17px',
  };

  const adv04GoodsContent = () =>
    formGoodsConfig('产品选择(至少选择5个产品，建议是5的倍数)', 'bottom_goods', {
    selectedRow: unref(data).goods_ids.length ? unref(data).goods_data : [],
    selectedRowKey: unref(data).goods_ids,
    extra: {
      min_num: 5,
    },
    client: props.client,
  });

  const adv04TitleContent = [
    {
      type: 'input',
      label: $sldComLanguage('标题名称'),
      placeholder: $sldComLanguage('请输入标题名称'),
      extra: $sldComLanguage('标题名称不能为空，10个字符以内'),
      maxlength: 10,
      initialValue: unref(title_info).title_name,
      name: 'title_name',
      rules: [{ required: true }],
    },
    {
      type: 'more_color_picker',
      label: $sldComLanguage('颜色'),
      initialValue: unref(title_info).title_color || '#ffffff',
      name: 'title_color',
    },
  ];

  const editConfirmHandler = (values, { modal, position }) => {
    switch (position) {
      case 'top_title': {
        //
        title_info.value = values[0];
        vm?.proxy?.$forceUpdate();
        break;
      }

      case 'bottom_goods': {
        unref(data).goods_data = values[0];
        unref(data).goods_ids = values[1];
        vm?.proxy?.$forceUpdate();
        break;
      }
    }
    emit('save_tpl_data', props.tpl_info);
  };

  createTplContext(props.mode, editConfirmHandler);
</script>

<style lang="less">
  .adv_04_wrap {
    width: 1210px;
    margin: 0 auto;
  }

  .adv_04_wrap .floor_title {
    position: relative;
    height: 35px;
    margin-bottom: 20px;
    overflow: hidden;
    line-height: 35px;
  }

  .adv_04_wrap .floor_title h2 {
    width: 100%;
    margin: 0 auto;
    padding-left: 10px;
    color: #333;
    font-size: 28px;
    line-height: 35px;
    text-align: center;
  }

  .adv_04_wrap .floor_title h2 font {
    display: inline-block;
    position: relative;
    top: 20px;
    width: 80px;
    height: 1px;
    background: red;
  }

  .adv_04_wrap .floor_title h2 span {
    display: inline-block;
    width: auto;
    min-width: 30px;
    height: 35px;
    margin: 0 20px;
    color: red;
    font-size: 24px;
    font-weight: normal;
    line-height: 35px;
    text-indent: 3px;
    vertical-align: middle;
  }

  .adv_04_wrap .floor_goods {
    position: relative;
    flex-wrap: wrap;
    width: 100%;
  }

  .adv_04_goods {
    width: 234px;
    margin: 0 10px 10px 0;
    padding: 24px 31px;

    &:nth-child(5n + 5) {
      margin-right: 0 !important;
    }
  }

  .adv_04_wrap .floor_goods .item {
    width: 234px;
    height: 310px;
    padding: 24px 31px 26px;
    float: left;
    background-color: #fff;
  }

  .adv_04_wrap .floor_goods .item .adv_04_wrap .floor_goods .item .wrap {
    position: relative;
    width: 172px;
    margin: 0 auto;
    font-size: 14px;
    text-align: center;
  }

  .adv_04_wrap .floor_goods .item .wrap img {
    display: block;
    width: 170px;
    height: 170px;
  }

  .adv_04_wrap .floor_goods .item .wrap .example_text {
    display: block;
    position: relative;
    width: 170px;
    height: 170px;
    background: #eee !important;
    color: #777;
    font-weight: 300;
    text-align: center;
  }

  .adv_04_wrap .floor_goods .item .wrap .example_text span {
    display: block;
    position: absolute;
    top: 50%;
    width: 100%;
    margin-top: -24px;
    font-size: 15px !important;
    line-height: 24px;
  }

  .adv_04_wrap .floor_goods .item .wrap .title {
    display: -webkit-box;
    display: inline-block;
    height: 38px;
    max-height: 38px;
    margin: 26px 0 18px;
    overflow: hidden;
    text-overflow: ellipsis;
    word-wrap: break-word;
    word-break: normal;
    -webkit-line-clamp: 2;

    /*! autoprefixer: off */
    -webkit-box-orient: vertical;
  }

  .adv_04_wrap .floor_goods .item .wrap .title a {
    color: #000;
    font-size: 12px;
    line-height: 19px !important;
  }

  .adv_04_wrap .floor_goods .item .wrap .price {
    color: red;
    font-size: 12px;
    font-weight: bold;
  }

  .adv_04_wrap .floor_goods .item .wrap .price .money_number {
    margin: 0 0 0 3px;
    font-family: Arial, Helvetica, sans-serif;
    font-size: 17px;
    font-weight: bold;
  }
</style>
