
import { TplUpdateApi } from '/@/api/decorate/deco_pc';
import { createContext, useContext } from '/@/hooks/core/useContext';


//保存装修模板请求接口
export const save_tpl_request = (tpl_obj) => {
  return new Promise((resolve, reject) => {
    TplUpdateApi(tpl_obj).then(res => {
      if (res.state == 200) {
        resolve({})
      } else {
        reject()
      }
    });
  })
}

//通用的模板编辑的传给模板组件的props
export const dfProps = ({
  mode: {
    type: String,
    default: 'edit',
  },
  tpl_info: {
    type: Object,
    default: () => { },
  },
  client: {
    type: String,
    default: 'pc',
  },
});
//通用的模板编辑的emit
export const dfEmit = ['save_tpl_data'];


//模板编辑中title的通用编辑配置
export const formTitleConfig = (title, position, content) => ({
  component: 'SldModal',
  width: 600,
  title,
  position,
  content
})

//模板编辑中商品的通用编辑配置
export const formGoodsConfig = (title, position, content) => ({
  component: 'SldSelMoreLeftRightGoods',
  width: 1000,
  title,
  position,
  ...content,
  height: window.innerHeight - 400,

})

//模板编辑中筛选商品的通用编辑配置
export const formMultipleGoodsConfig = (title, position, content) => ({
  component: 'SldSelMoreMultipleGoods',
  width: 1000,
  title,
  position,
  ...content,
  height: window.innerHeight - 400,
})


//模板编辑中多图的通用编辑配置
export const formMoreImgConfig = (title, position, content, modalTip = [], width = 1000,prop_info) => ({
  component: 'SldMoreImgModal',
  title,
  position,
  modalTip,
  content,
  width,
  ...prop_info,
})

//模板编辑中单图的通用编辑配置
export const formSingleImgConfig = (title, position, content, modalTips = [], width = 1000) => ({
  component: 'SldDiySingleImgModal',
  title,
  position,
  modalTips,
  content,
  width
})

//模板编辑中单图的通用编辑配置
export const formTitleLinkConfig = (title, position, mixContent, width = 1000) => ({
  component: 'SldDiyTitleLinkModal',
  title,
  position,
  ...mixContent,
  width
})


//创建模板组件和蒙层编辑组件链接的上下文
const key = Symbol();
export function createTplContext (mode, handler) {
  let context = {
    isMaskEdit: mode == 'edit',
    editConfirmHandler: handler
  }
  return createContext(context, key);
}
export function useTplContext () {
  return useContext(key);
}