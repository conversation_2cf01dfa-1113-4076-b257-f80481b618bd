import { ref, unref, computed } from 'vue'
import { tplPcListApi } from '@/api/decorate/deco_pc';
import { isUnDef } from '/@/utils/is';

//通用的获取模板列表动作
export const tpl_list_handler = (initialParam = {}) => {
  const tplList = ref([]);
  const tplType = ref('all');
  const getTplLoading = ref(false)
  const tplId = ref(0)

  const fallTplList = computed(() => {
    let list = [[], []];
    tplList.value.forEach((item, index) => {
      list[index % 2].push(item);
    });
    return list;
  });

  const getTemplateList = async (values = {}) => {
    let params = { ...values };

    if (!isUnDef(initialParam.isEnable)) {
      params.isEnable = initialParam.isEnable
    }

    if (unref(tplId) > 0) {
      params.tplId = unref(tplId)
    }

    if (unref(tplType) !== 'all') params.tplType = unref(tplType);
    getTplLoading.value = true;
    const res = await tplPcListApi({ ...params, pageSize: 1000 });
    if (res?.state == 200) {
      tplList.value = res.data.list.map((item) => {
        let data_json = typeof item.json == 'string' ? JSON.parse(item.json.replace(/&quot;/g, '"')) : item.json;
        return {
          ...item,
          data_json,
        };
      });
      getTplLoading.value = false;
    }
  };

  return {
    tplList,
    tplType,
    getTplLoading,
    fallTplList,
    getTemplateList,
    tplId
  }
}

//数据中json的 type 例如“adv_01”  转换成 "Adv01"
export const transType2ComponentName = (name) => {
  let splitLineName = name.split('_').join('');
  return splitLineName.replace(splitLineName[0], splitLineName[0].toUpperCase());
};