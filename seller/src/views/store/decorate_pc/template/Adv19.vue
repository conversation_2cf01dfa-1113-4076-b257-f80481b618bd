<template>
  <div class="adv_19_wrap">
    <div class="item left" v-for="(item, index) in data" :key="index">
      <div class="top_title" :style="{ backgroundColor: item.top.title_info.title_bg_color }">
        <div class="l_title" :style="{ color: item.top.title_info.title_color }">
          <MaskEdit
            :config="formAdv19TitleConfig('顶部标题设置', `top:${index}`, item.top.title_info)"
          >
            <span>{{
              item.top.title_info.title_name
                ? item.top.title_info.title_name
                : $sldComLanguage('添加标题')
            }}</span>
          </MaskEdit>
        </div>
        <div class="r_title">
          <ul>
            <li
              @mouseover="selTab(index, key)"
              v-for="(val, key) in item.center.right"
              :key="key"
              :class="{ sel_tab: key == tab_index[index] }"
            >
              <MaskEdit
                :config="formAdv19MultiImgConfig('标题tab设置', `right:${index}:${key}`, val)"
              >
                <span class="con">
                  {{
                    val.title_info.title_name
                      ? val.title_info.title_name
                      : $sldComLanguage('添加内容')
                  }}</span
                >
              </MaskEdit>
            </li>
          </ul>
        </div>
      </div>
      <div class="center">
        <div class="l_center">
          <MaskEdit :config="formAdv19SingleImgConfig('中间左侧图片设置', `center_left:${index}`, item.center.left)">
            <ImgWrap
              :src="item.center.left.data[0].imgUrl"
              :width="item.center.left.width"
              :height="item.center.left.height"
            >
            </ImgWrap>
          </MaskEdit>
        </div>

        <div class="r_center">
          <div class="tabs_panel" v-for="(vals, keys) in item.center.right" :key="keys">
            <template v-if="keys == tab_index[index]">
              <div class="item" v-for="(val, key) in vals.data" :key="key">
                <div class="title_wrap">
                  <a class="main_title" href="javascript:;">
                    {{ val.main_title ? val.main_title : $sldComLanguage('图片标题') }}
                  </a>
                  <a class="sub_title" href="javascript:;">
                    {{ val.sub_title ? val.sub_title : $sldComLanguage('图片子标题') }}
                  </a>
                </div>
                <div class="bottom_img">
                  <ImgWrap :src="val.imgUrl" :width="vals.width" :height="vals.height"></ImgWrap>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>

      <MaskEdit :config="formAdv19MultiImgConfig('底部图片设置', `bottom:${index}`, item.bottom)">
        <div class="bottom flex justify-between">
          <ImgWrap
            :src="val.imgUrl"
            :width="item.bottom.width"
            :height="item.bottom.height"
            v-for="(val, key) in item.bottom.data"
            :key="key"
          ></ImgWrap>
        </div>
      </MaskEdit>
    </div>
  </div>
</template>

<script setup>
  import { computed, getCurrentInstance, provide, reactive, ref, toRef, toRefs, unref } from 'vue';
  import {
    createTplContext,
    dfEmit,
    dfProps,
    formMoreImgConfig,
    formTitleConfig,
    formSingleImgConfig
  } from './actions/edit_tpl';
  import MaskEdit from './components/MaskEdit.vue';
  import ImgWrap from './components/ImgWrap.vue';

  const vm = getCurrentInstance();
  const { $sldComLanguage } = vm?.appContext.config.globalProperties;

  const props = defineProps(dfProps);
  const emit = defineEmits(dfEmit);
  const data = toRef(props.tpl_info, 'data');

  const tab_index = ref([0, 0]);

  const selTab = (index, key) => {
    tab_index.value[index] = key;
  };

  const formAdv19SingleImgConfig = (title, position, variant) => {
    let content = {
      type: 'adv_19',
      show_height: variant.height,
      show_width: variant.width,
      height: variant.height,
      width: variant.width,
      data: variant.data&&variant.data.length>0?variant.data[0]:[],
      title_info: variant.title_info,
    };
    return formSingleImgConfig(title, position, content);
  };

  const formAdv19MultiImgConfig = (title, position, variant) => {
    let content = {
      show_height: variant.height,
      show_width: variant.width,
      height: variant.height,
      width: variant.width,
      data: variant.data,
      title_info: variant.title_info,
    };
    return formMoreImgConfig(title, position, content);
  };

  const formAdv19TitleConfig = (title, position, variant) => {
    let content = [
      {
        type: 'input',
        label: $sldComLanguage('标题名称'),
        placeholder: $sldComLanguage('请输入标题名称'),
        extra: $sldComLanguage('最多5个字'),
        maxlength: 5,
        initialValue: variant.title_name,
        name: 'title_name',
        rules: [{ required: true }],
      },
      {
        type: 'more_color_picker',
        label: $sldComLanguage('标题颜色'),
        initialValue: variant.title_color,
        name: 'title_color',
      },
      {
        type: 'more_color_picker',
        label: $sldComLanguage('背景色'),
        initialValue: variant.title_bg_color,
        name: 'title_bg_color',
      },
    ];

    return formTitleConfig(title, position, content);
  };

  const editConfirmHandler = (values, { position }) => {
    let [pos, index, key] = position.split(':');
    let [value] = values;
    switch (pos) {
      case 'right':
        let target = unref(data)[Number(index)].center.right;
        target[Number(key)].title_info.title_name = value.title_name;
        target[Number(key)].data = value.parent_data;
        break;
      case 'bottom': {
        let target = unref(data)[Number(index)].bottom;
        target.data = value.parent_data;
        break;
      }

      case 'top': {
        let target = unref(data)[Number(index)].top;
        target.title_info = value;
        break;
      }
      case 'center_left': {
        let target = unref(data)[Number(index)].center.left;
        let obj = []
        if(value.width){
          delete value.width
        }
        if(value.height){
          delete value.height
        }
        obj.push(value)
        target.data = obj;
        break;
      }

      default:
        break;
    }
    emit('save_tpl_data', props.tpl_info);
    // 确保页面发送更新
    vm?.proxy?.$forceUpdate();
  };

  createTplContext(props.mode, editConfirmHandler);
</script>

<style lang="less" scoped>
  /* adv_19-start */
  .adv_19_wrap {
    position: relative;
    width: 1210px;
    margin: 0 auto;
    clear: both;
    overflow: hidden;
    background: #fff;
  }

  .adv_19_wrap .item {
    overflow: hidden;
  }

  .adv_19_wrap .item:last-child {
    margin-left: 10px !important;
  }

  .adv_19_wrap .item.left {
    display: inline-block;
    width: 600px;
    height: 546px;
    margin: 10px auto;
    background-color: #fff;
  }

  .adv_19_wrap .item.left .top_title {
    width: 100%;
    height: 56px;
    padding: 0 5px 0 22px;
    overflow: hidden;
    background: rgb(248 154 63);
    color: #fff;
    line-height: 56px;
  }

  .adv_19_wrap .item.left .top_title .l_title {
    position: relative;
    width: 200px;
    height: 56px;
    float: left;
    font-size: 20px;
  }

  .adv_19_wrap .item.left .top_title .r_title {
    width: 373px;
    height: 57px;
    float: left;
    border-width: 0;
    border-style: solid;
    border-color: rgb(239 240 241);
    line-height: 57px;
    text-align: right;
  }

  .adv_19_wrap .item.left .top_title .r_title ul {
    float: right;
  }

  .adv_19_wrap .item.left .top_title .r_title ul li {
    position: relative;
    min-width: 60px;
    margin: 0 5px;
    float: left;
    text-align: center;
    cursor: pointer;
  }

  .adv_19_wrap .item.left .top_title .r_title ul li a {
    visibility: hidden;
    position: absolute;
    z-index: 33;
    top: 0;
    left: 0;
    margin: 0;
    padding: 2px 5px 2px 1px;
    background: #4da2fd;
    color: #fff;
    font-size: 12px;
    line-height: 20px;
  }

  .adv_19_wrap .item.left .top_title .r_title ul li .con {
    padding: 9px 29px;
    border: 1px solid rgb(253 224 194);
    background-color: rgb(235 174 99);
    font-size: 13px;
  }

  .adv_19_wrap .item.left .top_title .r_title ul li.sel_tab .con {
    background: rgb(248 154 63);
  }

  .adv_19_wrap .item.left .center {
    height: auto;
    padding: 10px;
    overflow: hidden;
  }

  .adv_19_wrap .item.left .center .l_center {
    display: block;
    position: relative;
    width: 186px;
    height: 340px;
    float: left;
  }

  .adv_19_wrap .item.left .center .l_center a {
    display: block;
    position: relative;
    width: 186px;
    height: 340px;
    overflow: hidden;
    background: #eee !important;
    font-weight: 300;
    text-align: center;
  }

  .adv_19_wrap .item.left .center .l_center a span {
    display: block;
    position: absolute;
    top: 50%;
    width: 100%;
    margin-top: -12px;
    color: #777;
    font-size: 13px !important;
    line-height: 24px;
  }

  .adv_19_wrap .item.left .center .l_center a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -150px;
    width: 80px;
    height: 409px;
    overflow: hidden;
    transform: skewX(-25deg);
    background: -webkit-gradient(
      linear,
      left top,
      right top,
      color-stop(0%, rgb(255 255 255 / 0%)),
      color-stop(50%, rgb(255 255 255 / 20%)),
      color-stop(100%, rgb(255 255 255 / 0%))
    );
    background: linear-gradient(
      left,
      rgb(255 255 255 / 0%) 0,
      rgb(255 255 255 / 20%) 50%,
      rgb(255 255 255 / 0%) 100%
    );
  }

  .adv_19_wrap .item.left .center .l_center a:hover::before {
    left: 260px;
    transition: left 0.5s;
  }

  .adv_19_wrap .item.left .center .r_center {
    display: block;
    width: 394px;
    float: left;
  }

  .adv_19_wrap .item.left .center .r_center .tabs_panel {
    width: 100%;
    overflow: hidden;
  }

  .adv_19_wrap .item.left .center .r_center .tabs_panel .item {
    display: block;
    width: 197px;
    height: 170px;
    padding: 12px;
    float: left;
    border-width: 0 0 1px 1px;
    border-style: solid;
    border-color: rgb(239 240 241);
  }

  .adv_19_wrap .item.left .center .r_center .tabs_panel .item .title_wrap {
    margin: 0;
    text-align: center;
  }

  .adv_19_wrap .item.left .center .r_center .tabs_panel .item .title_wrap a {
    display: block;
    height: 20px;
    overflow: hidden;
    line-height: 20px;
    text-overflow: ellipsis;
    word-wrap: break-word;
    word-break: normal;
    -webkit-line-clamp: 1;

    /*! autoprefixer: off */
    -webkit-box-orient: vertical;
  }

  .adv_19_wrap .item.left .center .r_center .tabs_panel .item .title_wrap a.main_title {
    color: #000;
    font-size: 14px;
  }

  .adv_19_wrap .item.left .center .r_center .tabs_panel .item {
    margin-left: 0 !important;
  }

  .adv_19_wrap .item.left .center .r_center .tabs_panel .item .title_wrap a.sub_title {
    color: rgb(55 155 201);
    font-size: 12px;
  }

  .adv_19_wrap .item.left .center .r_center .tabs_panel .item .bottom_img {
    transition: transform 0.5s, -moz-transform 0.5s;
    transition: -webkit-transform 0.5s;
    transition: transform 0.5s;
    transition: transform 0.5s, -webkit-transform 0.5s, -moz-transform 0.5s;
  }

  .adv_19_wrap .item.left .center .r_center .tabs_panel .item .bottom_img:hover {
    transform: scale(1.05);
  }

  .adv_19_wrap .item.left .center .r_center .tabs_panel .item .bottom_img:hover a span {
    color: #777;
  }

  .adv_19_wrap .item.left .center .r_center .tabs_panel .item .bottom_img a {
    display: block;
    width: 172px;
    height: 106px;
  }

  .adv_19_wrap .item.left .bottom {
    position: relative;
    width: 600px;
    padding: 0 10px;
    overflow: hidden;
  }

  .adv_19_wrap .item.left .bottom a {
    display: block;
    width: 187px;
    height: 120px;
    margin-left: 10px;
    float: left;
    overflow: hidden;
    position: relative;
  }

  .adv_19_wrap img {
    max-width: 100%;
    max-height: 100%;
  }

  /* adv_19-end */
</style>
