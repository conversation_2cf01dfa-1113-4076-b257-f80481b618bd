<template>
  <div class="adv_20">
    <div class="adv_20_wrap flex_column_start_center">
      <div class="adv_20_wrap_row" v-for="(item, index) in imageData" :key="index">
        <MaskEdit :config="imgConfig(index)">
          <div class="flex_row_around_center">
            <div
              class="flex_column_center_center adv_20_wrap_item"
              v-for="(child, child_index) in item"
              :key="child_index"
            >
              <div
                class="flex_row_center_center adv_20_wrap_item_img"
                :style="{
                  borderRadius: child.imgUrl ? 0 : 35,
                  background: child.imgUrl ? '#fff' : '#E8E8E8',
                }"
              >
                <img :src="child.imgUrl" />
              </div>
              <span class="main_title">{{ child.main_title }}</span>
              <span class="sub_title">{{ child.sub_title }}</span>
            </div>
          </div>
        </MaskEdit>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { cloneDeep } from 'lodash-es';
  import { getCurrentInstance, provide, reactive, ref, toRefs, unref } from 'vue';
  import MaskEdit from './components/MaskEdit.vue';
  import { createTplContext, dfEmit, dfProps, formMoreImgConfig } from './actions/edit_tpl';
  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const emit = defineEmits(dfEmit);
  const props = defineProps(dfProps);

  const imageData = ref(props.tpl_info.data);

  const flr_default = Array(5).fill({
    main_title: L('导航标题'),
    sub_title: L('导航子标题'),
    imgUrl: '',
    imgPath: '',
    title: '',
    link_type: '',
    link_value: '',
    info: {},
  });
  if (!unref(imageData).length) {
    imageData.value = [cloneDeep(flr_default), cloneDeep(flr_default)];
  }

  const imgConfig = (index) => {
    let content = {
      width: 70,
      height: 70,
      data: unref(imageData)[index],
    };
    return formMoreImgConfig(L('设置导航'), index, content);
  };

  const editConfirmHandler = (values, { modal, position }) => {
    imageData.value[Number(position)] = values[0].parent_data;
    emit('save_tpl_data', props.tpl_info);
  };

  createTplContext(props.mode, editConfirmHandler);
</script>

<style lang="less" scoped>
  /* adv_20-start */
  .adv_20 .adv_20_wrap {
    position: relative;
    flex-wrap: wrap;
    width: 100%;
    padding: 18px 0;
    clear: both;
    overflow: hidden;
    background: #fbfbfd;
  }

  .adv_20 .adv_20_wrap .adv_20_wrap_row {
    position: relative;
    width: 100%;
    padding: 0 35px;
  }

  .adv_20 .adv_20_wrap .adv_20_wrap_item {
    flex-shrink: 0;
    width: 190px;
    margin: 18px 0;
  }

  .adv_20 .adv_20_wrap .adv_20_wrap_item .adv_20_wrap_item_img {
    width: 70px;
    height: 70px;

    img {
      max-width: 100%;
      max-height: 100%;
    }
  }

  .adv_20 .adv_20_wrap .adv_20_wrap_item .main_title {
    margin-top: 17px;
    color: #333;
    font-family: PingFangSC-Semibold, 'PingFang SC';
    font-size: 14px;
    font-weight: 600;
    line-height: 20px;
  }

  .adv_20 .adv_20_wrap .adv_20_wrap_item .sub_title {
    margin-top: 12px;
    color: #333;
    font-family: PingFangSC-Regular, 'PingFang SC';
    font-size: 12px;
    font-weight: 400;
    line-height: 17px;
  }

  /* adv_20-end */
</style>
