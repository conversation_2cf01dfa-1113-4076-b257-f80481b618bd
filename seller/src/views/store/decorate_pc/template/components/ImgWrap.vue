<!-- 模板编辑---通用的图片展示组件 -->
<template>
  <div
    class="diy_img_wrap relative"
    :class="{ flex_row_center_center: mode == 'contain' }"
    :style="{ width: realWidth, height: realHeight, background }"
  >
    <img
      :src="src"
      class="diy_img"
      v-show="src"
      :class="{ mode_cover: mode == 'cover', mode_contain: mode == 'contain' }"
    />

    <div v-show="!src" class="h-full">
      <div class="diy_img_placeholder flex_row_center_center" v-if="placeholder == 'size'">
        <span>
          此处添加
          <span class="font-bold">【{{ widthText }}*{{ heightText }}】</span>
          图片
        </span>
      </div>

      <div
        class="diy_img_placeholder with_back_ground flex_row_center_center"
        v-if="placeholder == 'icon'"
      >
        <AliSvgIcon
          iconName="icontupian_huaban"
          width="50px"
          height="50px"
          fillColor="rgb(229 230 222)"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
  import { AliSvgIcon } from '@/components/SvgIcon';
  import { isNumber } from '/@/utils/is';
  import { computed } from 'vue';
  const props = defineProps({
    //空占位符模式：size:展示尺寸文本，icon:展示图标
    placeholder: {
      type: String,
      default: 'size',
    },
    //图片
    src: String,
    //宽高
    width: [Number, String],
    height: [Number, String],
    //背景颜色
    background: {
      type: String,
      default: '#eee',
    },
    //img 的object-fit属性 默认cover
    mode: {
      type: String,
      default: 'cover',
    },
  });

  const realWidth = computed(() => {
    if (isNumber(props.width)) {
      return props.width + 'px';
    }
    return 'auto';
  });

  const realHeight = computed(() => {
    if (isNumber(props.width)) {
      return props.height + 'px';
    }
    return 'auto';
  });

  const widthText = computed(() => {
    if (isNumber(props.width)) {
      return props.width;
    }
    return '宽度不限';
  });

  const heightText = computed(() => {
    if (isNumber(props.height)) {
      return props.height;
    }
    return '高度不限';
  });
</script>

<style lang="less" scoped>
  .diy_img_wrap {
    text-align: center;
  }

  .diy_img_placeholder {
    width: 100%;
    height: 100%;
    color: #777;
    font-size: 13px;
  }

  .diy_img {
    &.mode_cover {
      max-width: 100%;
      max-height: 100%;
      object-fit: cover;
    }

    &.mode_contain {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
    }
  }
</style>
