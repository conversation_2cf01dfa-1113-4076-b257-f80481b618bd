<template>
  <MaskEdit :config="bannerConfig">
    <Carousel autoplay :duration="3000">
      <div
        class="adv_01_wrap main_banner_item_wrap"
        v-for="(item, index) in bannerData.data"
        :key="index"
      >
        <div class="adv_01_img_show" :style="{ backgroundImage: `url('${item.imgUrl}')` }"></div>
      </div>
    </Carousel>
  </MaskEdit>
</template>

<script setup>
  import MaskEdit from './components/MaskEdit.vue';
  import { Carousel } from 'ant-design-vue';
  import { cloneDeep } from 'lodash-es';
  import { getCurrentInstance, provide, ref, unref } from 'vue';
  import { createTplContext, dfEmit, dfProps, formMoreImgConfig } from './actions/edit_tpl';

  const props = defineProps(dfProps);
  const emit = defineEmits(dfEmit);

  const flr_default = {
    img_list: Array(6).fill({
      imgUrl: '',
      imgPath: '',
      title: '',
      link_type: '',
      link_value: '',
      info: {},
    }),
  };

  const vm = getCurrentInstance();
  const { $sldComLanguage } = vm?.appContext.config.globalProperties;

  const bannerData = ref(props.tpl_info);

  const bannerSize = {
    type: 'main_banner',
    width: 1920,
    height: 457,
    show_width: 320,
    show_height: 76,
  };

  if (props.tpl_info.data.length == 0) {
    unref(bannerData).data = cloneDeep(flr_default.img_list);
  }

  const bannerConfig = formMoreImgConfig($sldComLanguage('编辑轮播图'), '', {
    show_width: bannerSize.show_width,
    show_height: bannerSize.show_height,
    width: props.tpl_info.width,
    height: props.tpl_info.height,
    data: unref(bannerData).data,
  });

  const editConfirmHandler = (values) => {
    let [value] = values;
    unref(bannerData).data = value.parent_data;
    vm?.proxy?.$forceUpdate();
    emit('save_tpl_data', props.tpl_info);
  };

  createTplContext(props.mode, editConfirmHandler);
</script>

<style lang="less" scoped>
  .adv_01_wrap {
    display: flex;
    position: relative;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 1210px;
    background: #efefef;
  }

  .main_banner_item_wrap {
    height: 457px;
    overflow: hidden;
  }

  .adv_01_img_show {
    position: absolute;
    top: 0;
    left: 50%;
    width: 1920px;
    height: 457px;
    transform: translateX(-50%);
    background-repeat: no-repeat;
    background-position: 50%;
    background-size: contain;
  }

  .adv_01_img_thumb .del_img {
    display: inline-block;
    position: absolute;
    top: -1px;
    right: 0;
    padding: 0 4px;
    background: #fe9700;
    color: #fff;
    font-size: 12px;
    cursor: pointer;
  }
</style>
