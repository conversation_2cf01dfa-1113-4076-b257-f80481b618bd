<template>
  <MaskEdit :config="adv01ImgConfig">
    <div class="adv_01_wrap flex_row_center_center fixed_height">
      <ImgWrap :src="imageData.imgUrl" :width="1200" height="auto"></ImgWrap>
    </div>
  </MaskEdit>
</template>

<script setup>
  import { provide, reactive, ref, toRef, unref } from 'vue';
  import { createTplContext, dfEmit, dfProps, formSingleImgConfig } from './actions/edit_tpl';
  import ImgWrap from './components/ImgWrap.vue';
  import MaskEdit from './components/MaskEdit.vue';
  const props = defineProps(dfProps);
  const emit = defineEmits(dfEmit);
  const imageData = toRef(props.tpl_info, 'data');

  //广告图设置
  const adv01ImgConfig = formSingleImgConfig('广告图设置', 'center_img', {
    type: 'adv_01',
    width: 1200,
    height: 0, //高度为0的话表示不限制
    show_width: 600,
    show_height: 200,
    data: unref(imageData),
  });

  const editConfirmHandler = (values, { modal, position }) => {
    switch (position) {
      case 'center_img': {
        imageData.value = values[0];
        adv01ImgConfig.content.data = values[0];
        break;
      }
    }
    emit('save_tpl_data', props.tpl_info);
  };

  createTplContext(props.mode, editConfirmHandler);
</script>

<style lang="scss" scoped>
  .adv_01_wrap {
    width: 1210px;
    margin: 0 auto;
  }

  .fixed_height {
    height: 350px;

    .diy_img_wrap {
      height: 100%;
    }
  }
</style>
