<template>
  <div class="adv_27_wrap flex_row_between_center">
    <div class="adv_27_warp_box">
      <MaskEdit :config="adv27LeftTitleConfig">
        <div class="adv_27_warp_box_title" :style="{background:left.background.initialValue?`linear-gradient(0deg, #FFFFFF, ${left.background.initialValue})`:'#EEEEEE'}">
          <div class="adv_27_title" :style="{color:left.title_color.initialValue?left.title_color.initialValue:'#333333'}">{{ left.title.initialValue?left.title.initialValue:'栏目标题' }}</div>
          <AliSvgIcon
              iconName="iconlouceng-youjiantou"
              width="18px"
              height="18px"
              fillColor="#E2231A"
            />  
        </div>
      </MaskEdit>
      <MaskEdit
      :config="
        formGoodsConfig(
          $sldComLanguage('商品选择(至少选择4个商品，最多选择20个商品)'),
          'left_goods',
          adv27LeftGoodsContent,
        )
      ">
      <div class="adv_27_warp_box_sur" v-if="loading">
          <Carousel3d
            @before-slide-change="onSlideChange"
            :autoplayTimeout="3000"
            :perspective="0"
            :display="3"
            :animationSpeed="500"
            :width="145"
            :border="0"
            :inverseScaling="70"
            :controlsVisible="true"
            :height="145"
            :space="100"
            :loop="true"
            v-if="left.data.goods_data.length>0"
        >
          <slide v-for="(item, i) in left.data.goods_data" :index="i" :key="i">
            <img :src="item.mainImage" alt="">
          </slide>
        </Carousel3d>
         <div v-else class="adv_27_warp_box_sur_o">
          <Carousel3d
            :autoplayTimeout="3000"
            :perspective="0"
            :display="3"
            :animationSpeed="500"
            :width="145"
            :border="0"
            :inverseScaling="70"
            :controlsVisible="true"
            :height="145"
            :space="100"
            :loop="true"
        >
          <slide v-for="(item, i) in 4" :index="i" :key="i">
            <AliSvgIcon
              iconName="iconshangpin-"
              width="34px"
              height="34px"
              fillColor="#D2D2D2"
            />
          </slide>
        </Carousel3d>
         </div>
        <div class="adv_27_warp_box_shop_cont">
          <!-- carouselIndex.value -->
          <div class="adv_27_warp_box_shop_title">{{ left.data.goods_data.length>0?left.data.goods_data[carouselIndex].goodsName:'商品标题商品标题' }}</div>
          <div class="adv_27_warp_box_shop_price"><span>￥</span><span>{{ left.data.goods_data.length>0?left.data.goods_data[carouselIndex].goodsPrice:999 }}</span></div>
        </div>
      </div>
      </MaskEdit>
    </div>
    <div class="adv_27_warp_box">
      <MaskEdit :config="adv27LeftCenterTitleConfig">
        <div class="adv_27_warp_box_title" :style="{background:left_center.background.initialValue?`linear-gradient(0deg, #FFFFFF, ${left_center.background.initialValue})`:'#EEEEEE'}">
          <div class="adv_27_title" :style="{color:left_center.title_color.initialValue?left_center.title_color.initialValue:'#333333'}">{{ left_center.title.initialValue?left_center.title.initialValue:'栏目标题' }}</div>
          <AliSvgIcon
              iconName="iconlouceng-youjiantou"
              width="18px"
              height="18px"
              fillColor="#E2231A"
            />  
        </div>
      </MaskEdit>
      <MaskEdit
      :config="
        formGoodsConfig(
          $sldComLanguage('商品选择(至少选择4个商品，最多选择20个商品)'),
          'left_center_goods',
          adv27LeftCenterGoodsContent,
        )
      ">
      <div class="adv_27_warp_box_goods">
        <div class="adv_27_warp_item" v-for="(item,index) in left_center.data.goods_ids.length>0?left_center.data.goods_data[0]:4" :key="index">
          <div class="adv_27_warp_item_img" :class="{adv_27_warp_item_img_one:left_center.data.goods_ids.length==0}">
            <AliSvgIcon
              v-if="!item.mainImage"
              iconName="iconshangpin-"
              width="34px"
              height="34px"
              fillColor="#D2D2D2"
            />
            <img v-else :src="item.mainImage" alt="">
          </div>
          <div class="adv_27_warp_item_title">{{ item.goodsName?item.goodsName:'商品标题商品标题' }}</div>
        </div>
      </div>
    </MaskEdit>
    </div>
    <div class="adv_27_warp_box">
      <MaskEdit :config="adv27RightCenterTitleConfig">
        <div class="adv_27_warp_box_title" :style="{background:right_center.background.initialValue?`linear-gradient(0deg, #FFFFFF, ${right_center.background.initialValue})`:'#EEEEEE'}">
          <div class="adv_27_title" :style="{color:right_center.title_color.initialValue?right_center.title_color.initialValue:'#333333'}">{{ right_center.title.initialValue?right_center.title.initialValue:'栏目标题' }}</div>
          <AliSvgIcon
              iconName="iconlouceng-youjiantou"
              width="18px"
              height="18px"
              fillColor="#E2231A"
            />  
        </div>
      </MaskEdit>
        <MaskEdit
      :config="
        formGoodsConfig(
          $sldComLanguage('商品选择(至少选择4个商品，最多选择20个商品)'),
          'right_center_goods',
          adv27RightCenterGoodsContent,
        )
      ">
        <div class="adv_27_warp_box_goods">
          <div class="adv_27_warp_item" v-for="(item,index) in right_center.data.goods_ids.length>0?right_center.data.goods_data[0]:4" :key="index">
            <div class="adv_27_warp_item_img" :class="{adv_27_warp_item_img_one:right_center.data.goods_ids.length==0}">
              <AliSvgIcon
                v-if="!item.mainImage"
                iconName="iconshangpin-"
                width="34px"
                height="34px"
                fillColor="#D2D2D2"
              />
              <img v-else :src="item.mainImage" alt="">
            </div>
            <div class="adv_27_warp_item_title">{{ item.goodsName?item.goodsName:'商品标题商品标题' }}</div>
          </div>
        </div>
      </MaskEdit>
    </div>
    <div class="adv_27_warp_box">
      <MaskEdit :config="adv27RightTitleConfig">
        <div class="adv_27_warp_box_title" :style="{background:right.background.initialValue?`linear-gradient(0deg, #FFFFFF, ${right.background.initialValue})`:'#EEEEEE'}">
          <div class="adv_27_title" :style="{color:right.title_color.initialValue?right.title_color.initialValue:'#333333'}">{{ right.title.initialValue?right.title.initialValue:'栏目标题' }}</div>
          <AliSvgIcon
              iconName="iconlouceng-youjiantou"
              width="18px"
              height="18px"
              fillColor="#E2231A"
            />  
        </div>
      </MaskEdit>
        <MaskEdit
      :config="
        formGoodsConfig(
          $sldComLanguage('商品选择(至少选择4个商品，最多选择20个商品)'),
          'right_goods',
          adv27RightGoodsContent,
        )
      ">
      <div class="adv_27_warp_box_goods">
     
        <div class="adv_27_warp_item" v-for="(item,index) in right.data.goods_ids.length>0?right.data.goods_data[0]:4" :key="index">
          <div class="adv_27_warp_item_img" :class="{adv_27_warp_item_img_one:right.data.goods_ids.length==0}">
            <AliSvgIcon
              v-if="!item.mainImage"
              iconName="iconshangpin-"
              width="34px"
              height="34px"
              fillColor="#D2D2D2"
            />
            <img v-else :src="item.mainImage" alt="">
          </div>
          <div class="adv_27_warp_item_title">{{ item.goodsName?item.goodsName:'商品标题商品标题' }}</div>
        </div>
      </div>
    </MaskEdit>
    </div>
  </div>
</template>

<script setup>
  import { provide, reactive, ref, toRefs, unref,getCurrentInstance } from 'vue';
  import { createTplContext, dfEmit, dfProps, formTitleLinkConfig,formGoodsConfig} from './actions/edit_tpl';
  import MaskEdit from './components/MaskEdit.vue';
  import "vue3-carousel-3d/dist/index.css"
  import {Carousel3d,Slide } from 'vue3-carousel-3d';

  const vm = getCurrentInstance();
  const { $sldComLanguage } = vm?.appContext.config.globalProperties;
  const props = defineProps(dfProps);
  const emit = defineEmits(dfEmit);
  const {left,left_center,right_center,right} = toRefs(props.tpl_info);
  const loading = ref(true)

  const carouselIndex = ref(0)

  //顶部标题设置
  const adv27LeftTitleConfig = formTitleLinkConfig('顶部标题设置', 'left_title', {
    content: {
      ...unref(left),
    },
    title_num:10,
    client:props.client,
  });
  //顶部标题设置
  const adv27LeftCenterTitleConfig = formTitleLinkConfig('顶部标题设置', 'left_center_title', {
    content: {
      ...unref(left_center),
    },
    title_num:10,
    client:props.client,
  });
  //顶部标题设置
  const adv27RightCenterTitleConfig = formTitleLinkConfig('顶部标题设置', 'right_center_title', {
    content: {
      ...unref(right_center),
    },
    title_num:10,
    client:props.client,
  });
  //顶部标题设置
  const adv27RightTitleConfig = formTitleLinkConfig('顶部标题设置', 'right_title', {
    content: {
      ...unref(right),
    },
    title_num:10,
    client:props.client,
  });


  const adv27LeftCenterGoodsContent =  ref({
    selectedRow:  unref(left_center).data.goods_data,
    selectedRowKey: unref(left_center).data.goods_ids,
    extra: {
      min_num: 4,
      max_num: 20,
    },
    arrType:'array',
    client:props.client
  });

  const adv27LeftGoodsContent =  ref({
    selectedRow:  unref(left).data.goods_data,
    selectedRowKey: unref(left).data.goods_ids,
    extra: {
      min_num: 4,
      max_num: 20,
    },
    client:props.client
  });

  const adv27RightCenterGoodsContent =  ref({
    selectedRow:  unref(right_center).data.goods_data,
    selectedRowKey: unref(right_center).data.goods_ids,
    extra: {
      min_num: 4,
      max_num: 20,
    },
    arrType:'array',
    client:props.client
  });

  const adv27RightGoodsContent =  ref({
    selectedRow:  unref(right).data.goods_data,
    selectedRowKey: unref(right).data.goods_ids,
    extra: {
      min_num: 4,
      max_num: 20,
    },
    arrType:'array',
    client:props.client
  });

  const getList = (array)=> {
    let obj = [];
    let startX = 0;
    let dataList = array;
    while (startX <= dataList?.length) {
      let tmpList = dataList.slice(startX, (startX += 4));
      if (tmpList.length) {
        obj.push(tmpList);
      }
    }
    return obj
  }

  const onSlideChange = (e)=> {
    carouselIndex.value = e
  }


  const editConfirmHandler = (values, { modal, position }) => {
    switch (position) {
      case 'left_title': {
        unref(left).title.initialValue = values[0].title;
        unref(left).title_color.initialValue = values[0].title_color;
        unref(left).background.initialValue = values[0].background;
        unref(left).link_type = values[0].link_type;
        unref(left).link_value = values[0].link_value;
        unref(left).info = values[0].info;
        break;
      }
      case 'left_center_title': {
        unref(left_center).title.initialValue = values[0].title;
        unref(left_center).title_color.initialValue = values[0].title_color;
        unref(left_center).background.initialValue = values[0].background;
        unref(left_center).link_type = values[0].link_type;
        unref(left_center).link_value = values[0].link_value;
        unref(left_center).info = values[0].info;
        break;
      }
      case 'right_center_title': {
        unref(right_center).title.initialValue = values[0].title;
        unref(right_center).title_color.initialValue = values[0].title_color;
        unref(right_center).background.initialValue = values[0].background;
        unref(right_center).link_type = values[0].link_type;
        unref(right_center).link_value = values[0].link_value;
        unref(right_center).info = values[0].info;
        break;
      }
      case 'right_title': {
        unref(right).title.initialValue = values[0].title;
        unref(right).title_color.initialValue = values[0].title_color;
        unref(right).background.initialValue = values[0].background;
        unref(right).link_type = values[0].link_type;
        unref(right).link_value = values[0].link_value;
        unref(right).info = values[0].info;
        break;
      }
      case 'left_goods': {
        // let obj = getList(values[0])
        loading.value = false
        unref(left).data = {
          goods_data:values[0],
          goods_ids:values[1]
        }
        adv27LeftGoodsContent.value.selectedRow = values[0]
        adv27LeftGoodsContent.value.selectedRowKey = values[1]
        setTimeout(()=>{
          carouselIndex.value = 0
          loading.value = true
        })
        break;
      }
      case 'left_center_goods': {
        let obj = getList(values[0])
        unref(left_center).data = {
          goods_data:obj,
          goods_ids:values[1]
        }
        adv27LeftCenterGoodsContent.value.selectedRow = obj
        adv27LeftCenterGoodsContent.value.selectedRowKey = values[1]
        break;
      }
      case 'right_center_goods': {
        let obj = getList(values[0])
        unref(right_center).data = {
          goods_data:obj,
          goods_ids:values[1]
        }
        adv27RightCenterGoodsContent.value.selectedRow = obj
        adv27RightCenterGoodsContent.value.selectedRowKey = values[1]
        break;
      }
      case 'right_goods': {
        let obj = getList(values[0])
        unref(right).data = {
          goods_data:obj,
          goods_ids:values[1]
        }
        adv27RightGoodsContent.value.selectedRow = obj
        adv27RightGoodsContent.value.selectedRowKey = values[1]
        break;
      }
    }
    emit('save_tpl_data', props.tpl_info);
  };

  createTplContext(props.mode, editConfirmHandler);
</script>




<style lang="less">
  .adv_27_wrap {
    width: 1210px;
    margin: 10px auto;
    height: 362px;
    .adv_27_warp_box_shop_cont{
      margin-top: 25px;
    }
    .adv_27_warp_box_shop_title{
      width: 243px;
      height: 39px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #333333;
      font-size: 13px;
      line-height: 20px;
      word-break: break-all;
      overflow: hidden;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      text-align: center;
      margin-left: 26px;
      margin-right: 26px;
      margin-bottom: 21px;
    }
    .adv_27_warp_box_shop_price{
      text-align: center;
      padding: 0 26px;
      span{
        color: #E2231A;
        font-weight: bold;
        &:first-child{
          font-size: 14px;
        }
        &:last-child{
          font-size: 18px;
        }
      }
    }.adv_27_warp_box_shop_cont{
      margin-top: 25px;
    }
    .adv_27_warp_box_shop_title{
      width: 243px;
      height: 39px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #333333;
      font-size: 13px;
      line-height: 20px;
      word-break: break-all;
      overflow: hidden;
      -webkit-box-orient: vertical;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      text-align: center;
      margin-left: 26px;
      margin-right: 26px;
      margin-bottom: 21px;
    }
    .adv_27_warp_box_shop_price{
      text-align: center;
      padding: 0 26px;
      span{
        color: #E2231A;
        font-weight: bold;
        &:first-child{
          font-size: 14px;
        }
        &:last-child{
          font-size: 18px;
        }
      }
    }
    .adv_27_warp_box_sur{
      width: 295px;
      height: 312px;
      background: #FFFFFF;
      display: flex;
      flex-direction: column;
      padding-top: 35px;
      .carousel-3d-container{
        z-index: auto !important;
        margin: 0 auto;
      }
      .left-1{
        opacity: .24 !important;
      }
      .right-1{
        opacity: .24 !important;
      }
      .carousel-3d-slide{
        background: #fff;
      }
      .prev{
        width: 26px !important;
        height: 35px !important;
        line-height: 25px !important;
        border-radius: 0;
        background-color: rgba(0, 0, 0, 0.15);
        font-size: 38px !important;
        border-top-right-radius: 18px;
        border-bottom-right-radius: 18px;
        padding-right: 5px;
        display: flex;
        padding-left: 6px;
        color: #fff;
        font-weight: 100;
        left: 0;
        top: 9px;
      }
      .next{
        width: 26px !important;
        height: 35px !important;
        line-height: 25px !important;
        border-radius: 0;
        background-color: rgba(0, 0, 0, 0.15);
        font-size: 38px !important;
        border-top-left-radius: 18px;
        border-bottom-left-radius: 18px;
        display: flex;
        padding-left: 10px;
        font-weight: 100;
        color: #fff;
        right: 0;
        top: 9px;
      }
      .adv_27_warp_box_sur_o{
        .carousel-3d-slide{
          background-color: #eee;
          text-align: center;
          line-height: 145px;
        }
        .current{
          line-height: 160px;
        }
      }
    }
    .adv_27_warp_box{
      width: 295px;
      height: 362px;
      background: #FFFFFF;
      .adv_27_warp_box_title{
        width: 100%;
        padding: 0 10px;
        height: 50px;
        background: #EEEEEE;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .adv_27_title{
          font-size: 18px;
          font-weight: bold;
          color: #333333;
        }
        .adv_27_icon{
          font-size: 18px;
          color: var(--color_halo_bg);
          cursor: pointer;
        }
      }
      .adv_27_warp_box_goods{
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        height: 312px;
        position: relative;
        &::after{
          content: '';
          height: 312px;
          width: 1px;
          background: #F4F4F4;
          position: absolute;
          left: 50%;
        }
        &::before{
          content: '';
          height: 1px;
          width: 312px;
          background: #F4F4F4;
          position: absolute;
          top: 50%;
        }
        .adv_27_warp_item{
          width: 50%;
          height: 50%;
          padding: 10px;
          .adv_27_warp_item_img{
            width: 110px;
            height: 110px;
            margin: 0 auto;
            img{
              max-width: 100%;
              max-height: 100%;
            }
          }
          .adv_27_warp_item_img_one{
            background: #EEEEEE;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .adv_27_warp_item_title{
            width: 119px;
            font-size: 12px;
            margin-top: 10px;
            font-family: Microsoft YaHei;
            padding-left: 2px;
            font-weight: 400;
            text-align: center;
            color: #333333;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            transition: color ease 0.5s;
          }
        }
      }
    }



  }
</style>
