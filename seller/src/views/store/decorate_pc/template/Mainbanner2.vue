<template>
  <div class="main_banner_2">
    <div>
      <MaskEdit :config="categoryConfig">
        <div class="side_wrapper">
          <div
            class="category_unit flex_row_start_start"
            v-for="(leftItem, leftIdx) in left"
            :key="leftIdx"
          >
            <div class="unit_image">
              <img :src="leftItem.img.imgUrl" alt="" v-show="leftItem.img.imgUrl" />
            </div>
            <div class="unit_text">
              <p>{{ leftItem.main_category.title || '一级分类名称' }}</p>
              <div class="truncate w-[145px]">
                <span
                  v-for="(sub, subIdx) in leftItem.sub_category"
                  :key="subIdx"
                  class="sub_category"
                >
                  <a :class="{ text_active: sub.title }">{{ sub.title || '二级分类名称' }}</a>
                  <span class="mx-[1px] text_seperator">/</span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </MaskEdit>
    </div>
    <div class="carousel_area" :style="computedStyle">
      <MaskEdit :config="bannerConfig">
        <Carousel arrows>
          <div
            class="relative main_banner_item_wrap"
            v-for="(citem, cindex) in center.data"
            :key="cindex"
          >
            <div
              class="banner_img_show"
              :style="{ backgroundImage: `url('${citem.imgUrl}')` }"
            ></div>
          </div>
        </Carousel>
      </MaskEdit>
    </div>
    <div>
      <div class="side_wrapper p-[10px]">
        <div class="login_area flex_column_center_center">
          <div class="avatar"></div>
          <div class="welcome_intro"> Hi~ 欢迎来到{{ basic_site_name }}！</div>
          <div class="flex mt-1">
            <div class="btn_lgn but cursor-pointer">登录</div>
            <span>&nbsp;&nbsp;&nbsp;&nbsp;</span>
            <div class="btn_reg but cursor-pointer">注册</div>
          </div>
        </div>
        <div class="h-px bg-[#EBECED] w-full"></div>
        <div class="my-[10px] link_article">
          <div class="flex justify-between items-center">
            <p class="title">资讯快报</p>
            <div class="more">
              <span>更多</span>
              <span>></span>
            </div>
          </div>
          <div class="mt-[10px]">
            <p v-for="(art, artIdx) in article_list" class="article_item" :key="artIdx">
              <span class="hot_tag">Hot</span>
              <span class="article_title">{{ art.title }}</span>
            </p>
          </div>
        </div>
        <div class="h-px bg-[#EBECED] w-full"></div>
        <div class="mt-[10px]">
          <MaskEdit :config="rightInfoConfig()">
            <div class="flex flex-wrap justify-between">
              <div
                v-for="(item, index) in right"
                class="link_item flex_column_center_center"
                :key="index"
              >
                <div class="info_image">
                  <img :src="item.imgUrl" alt="" v-show="item.imgUrl" />
                </div>
                <p>{{ item.main_title || L('标题名称') }}</p>
              </div>
            </div>
          </MaskEdit>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import MaskEdit from './components/MaskEdit.vue';
  import { Carousel } from 'ant-design-vue';
  import { computed, getCurrentInstance, onMounted, ref, toRefs, unref } from 'vue';
  import { createTplContext, dfEmit, dfProps, formMoreImgConfig } from './actions/edit_tpl';
  import {
    carousel_default_data,
    left_category_default_data,
    pic_title_default_data,
  } from './actions/data';
  import { getBasicSiteSetting } from '/@/api/sysset/sysset';
  import { getArticleList } from '/@/api/manage/manage';
  import { cloneDeep } from 'lodash-es';

  const props = defineProps(dfProps);
  const emit = defineEmits(dfEmit);

  const vm = getCurrentInstance();
  const { $sldComLanguage: L } = vm?.appContext.config.globalProperties;
  const { left, right, style } = toRefs(props.tpl_info);
  const center = ref(props.tpl_info.center);

  const computedStyle = computed(() => {
    let style_s = {};
    Object.keys(unref(style)).forEach((key) => {
      style_s[key] = unref(style)[key] + 'px';
    });
    return style_s;
  });

  const bannerSize = {
    type: 'main_banner',
    width: 780,
    height: 457,
    show_width: 150,
    show_height: 76,
  };

  unref(center).data = unref(center).data.concat(
    carousel_default_data(6 - unref(center).data.length),
  );

  left.value = left.value.concat(left_category_default_data(7 - unref(left).length, 4));

  right.value = right.value.concat(pic_title_default_data(6 - unref(right).length));

  const bannerConfig = formMoreImgConfig(L('中间轮播图设置'), 'center', {
    show_width: bannerSize.show_width,
    show_height: bannerSize.show_height,
    width: props.tpl_info.center.width,
    height: props.tpl_info.center.height,
    data: cloneDeep(unref(center).data),
  });

  const categoryConfig = {
    component: 'SldDiyCategory',
    title: '左侧分类设置',
    position: 'left',
    width: 1000,
    client: 'pc',
    imgLayout: {
      width: 19,
      height: 19,
      show_width: 120,
      show_height: 120,
    },
    content: cloneDeep(unref(left)),
  };

  const rightInfoConfig = () => {
    let content = {
      width: 28,
      height: 28,
      show_width: 120,
      show_height: 120,
      data: cloneDeep(unref(right)),
    };
    return formMoreImgConfig(L('快捷入口设置'), 'right', content);
  };

  const editConfirmHandler = (values, { position }) => {
    let [value] = values;
    switch (position) {
      case 'left': {
        left.value = value;
        break;
      }
      case 'right': {
        right.value = value.parent_data;
        break;
      }

      case 'center': {
        unref(center).data = value.parent_data;
        vm?.proxy?.$forceUpdate();
        break;
      }
    }
    emit('save_tpl_data', props.tpl_info);
  };

  const basic_site_name = ref('');

  const article_list = ref([]);

  //获取站点基本配置
  const get_base_site = async () => {
    const res = await getBasicSiteSetting();
    if (res.state == 200 && res.data) {
      let basicSiteName = res.data.find((item) => item.name == 'basic_site_name');
      basic_site_name.value = basicSiteName.value;
    }
  };

  const get_article_list = async () => {
    const res = await getArticleList({ pageSize: 20 });
    if (res.state == 200) {
      let list = res.data.list.filter((li) => li.state == 1);
      article_list.value = list.toSorted((a, b) => a.sort - b.sort).slice(0, 4);
    }
  };

  createTplContext(props.mode, editConfirmHandler);
  onMounted(() => {
    get_article_list();
    get_base_site();
  });
</script>

<style lang="less" scoped>
  .main_banner_2 {
    display: flex;
    position: relative;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 1210px;
    margin: 0 auto;
  }

  .carousel_area {
    width: 780px;
    margin: 0 15px;
    background: #eee;
  }

  .side_wrapper {
    width: 200px;
    height: 457px;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0 0 10px 0 rgb(120 118 118 / 20%);
  }

  .category_unit {
    padding: 10px;

    .unit_image {
      width: 20px;
      min-width: 20px;
      height: 20px;
      border-radius: 50%;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .unit_text {
      flex: 1;
      margin-left: 6px;
      padding-top: 2px;

      p {
        color: #333;
        font-family: 'PingFang SC';
        font-size: 15px;
        font-weight: bold;
        line-height: 21px;
      }

      span {
        height: 13px;
        overflow: hidden;
        color: #888;
        font-family: 'PingFang SC';
        font-size: 13px;
        font-weight: 500;
        text-overflow: ellipsis;
        white-space: nowrap;

        &:last-child {
          .text_seperator {
            display: none;
          }
        }

        a {
          color: #999;
          font-style: normal;

          &.text_active {
            color: #777;
          }
        }
      }
    }
  }

  .login_area {
    margin-bottom: 20px;

    .avatar {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background-color: #efefef;
    }

    .welcome_intro {
      height: 36px;
      margin-top: 11px;
      padding: 0 10px;
      color: #666;
      font-family: 'Microsoft YaHei';
      font-size: 12px;
      font-weight: 400;
      line-height: 15px;
      text-align: center;
      word-break: break-all;
    }

    .but {
      width: 67px;
      height: 24px;
      border-radius: 12px;
      color: #fff;
      font-family: 'Microsoft YaHei';
      font-size: 12px;
      font-weight: 400;
      line-height: 24px;
      text-align: center;

      &.btn_lgn {
        background: #e2231a;
      }

      &.btn_reg {
        background: #fe8c1d;
      }
    }
  }

  .link_article {
    .title {
      color: #333;
      font-family: 'Microsoft YaHei';
      font-size: 14px;
      font-weight: bold;
    }

    .more {
      color: #999;
      font-family: 'Microsoft YaHei';
      font-size: 12px;
      font-weight: 400;
      cursor: pointer;
    }

    .article_item {
      margin: 5px 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      .hot_tag {
        display: inline-block;
        width: 30px;
        min-width: 30px;
        height: 16px;
        border-radius: 3px;
        background: #feebed;
        color: #ed544d;
        font-family: 'Microsoft YaHei';
        font-size: 11px;
        font-weight: 400;
        line-height: 16px;
        text-align: center;
      }

      .article_title {
        color: #666;
        font-family: 'Microsoft YaHei';
        font-size: 12px;
        font-weight: 400;
      }
    }
  }

  .link_item {
    width: 48px;
    margin-bottom: 10px;

    .info_image {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      background: #eee;

      img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }
    }

    p {
      margin-top: 10px;
      color: #333;
      font-family: 'Microsoft YaHei';
      font-size: 12px;
      font-weight: 400;
    }
  }

  .main_banner_item_wrap {
    width: 780px;
    height: 457px;
    overflow: hidden;
  }

  .banner_img_show {
    position: absolute;
    top: 0;
    left: 50%;
    width: 780px;
    height: 457px;
    transform: translateX(-50%);
    background-repeat: no-repeat;
    background-position: 50%;
    background-size: contain;
  }
</style>
