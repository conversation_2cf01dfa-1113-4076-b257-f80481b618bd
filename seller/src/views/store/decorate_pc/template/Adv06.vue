<template>
  <MaskEdit :config="adv06Config">
    <div class="adv_06_wrap flex_row_center_start">
      <div v-for="(item, index) in imageData" :key="index">
        <ImgWrap :width="302" height="auto" :src="item.imgUrl" mode="cover" />
      </div>
    </div>
  </MaskEdit>
</template>

<script setup>
  import { provide, toRef, unref } from 'vue';
  import { createTplContext, dfEmit, dfProps, formMoreImgConfig } from './actions/edit_tpl';
  import ImgWrap from './components/ImgWrap.vue';
  import MaskEdit from './components/MaskEdit.vue';
  const props = defineProps(dfProps);
  const emit = defineEmits(dfEmit);
  const imageData = toRef(props.tpl_info, 'data');

  const adv06Config = formMoreImgConfig(
    '编辑四栏广告',
    '',
    {
      height: 0,
      width: 302,
      show_width: 302,
      data: unref(imageData),
    },
    [
      '一行4张图片，宽度按照指定要求传，高度不限，建议4张图的高度一致',
      '请严格根据提示要求上传规定尺寸的广告图片',
      '编辑项中的“操作”指点击该内容所产生的链接地址，可通过下拉选项选择不同的方式',
    ],
  );

  const editConfirmHandler = (values) => {
    let [value] = values;
    imageData.value = value.parent_data;
    emit('save_tpl_data', props.tpl_info);
  };
  createTplContext(props.mode, editConfirmHandler);
</script>

<style lang="scss" scoped>
  .diy_img_wrap {
    height: 350px;
  }
</style>
