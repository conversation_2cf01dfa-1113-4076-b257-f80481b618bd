<template>
  <div class="adv_09_wrap">
    <!-- {/*左侧--start*/} -->
    <div class="item left">
      <MaskEdit :config="adv09LeftTitleConfig">
        <div class="top_title" :style="leftTitleStyle()">
          {{
            left.title_info.title_name ? left.title_info.title_name : $sldComLanguage('请输入标题')
          }}
        </div>
      </MaskEdit>
      <MaskEdit :config="adv09LeftImgConfig">
        <div class="main_con">
          <div v-for="(item, index) in left.data" :key="index" class="show_tip">
            <ImgWrap
              :src="item.imgUrl"
              :width="left.width"
              :height="left.height"
              mode="contain"
              background="#fff"
            ></ImgWrap>
          </div>
        </div>
      </MaskEdit>
    </div>
    <!-- {/*左侧--end*/} -->
    <!-- {/*中间--start*/} -->
    <div class="item center">
      <MaskEdit :config="adv09CenterTitleConfig">
        <div class="top_title" :style="centerTitleStyle()">
          {{
            center.title_info.title_name
              ? center.title_info.title_name
              : $sldComLanguage('请输入标题')
          }}
        </div>
      </MaskEdit>

      <MaskEdit :config="adv09CenterImgConfig">
        <div class="main_con">
          <div v-for="(item, index) in center.data" :key="index" class="show_tip">
            <ImgWrap
              :src="item.imgUrl"
              :width="center.width"
              :height="center.height"
              mode="contain"
              background="#fff"
            ></ImgWrap>
          </div>
        </div>
      </MaskEdit>
    </div>
    <!-- {/*中间--end*/} -->
    <!-- {/*右侧--start*/} -->
    <div class="item right">
      <MaskEdit :config="adv09RightTitleConfig">
        <div class="top_title" :style="rightTitleStyle()">
          {{
            right.title_info.title_name
              ? right.title_info.title_name
              : $sldComLanguage('请输入标题')
          }}
        </div>
      </MaskEdit>

      <MaskEdit :config="adv09RightImgConfig">
        <div class="main_con">
          <div v-for="(item, index) in right.data" :key="index" class="show_tip">
            <ImgWrap
              :src="item.imgUrl"
              :width="right.width"
              :height="right.height"
              mode="contain"
              background="#fff"
            ></ImgWrap>
          </div>
        </div>
      </MaskEdit>
    </div>
    <!-- {/*右侧--end*/} -->
  </div>
</template>

<script setup>
  import { getCurrentInstance, provide, toRefs, unref } from 'vue';
  import {
    createTplContext,
    dfEmit,
    dfProps,
    formMoreImgConfig,
    formTitleConfig,
  } from './actions/edit_tpl';
  import ImgWrap from './components/ImgWrap.vue';
  import MaskEdit from './components/MaskEdit.vue';
  const props = defineProps(dfProps);
  const emit = defineEmits(dfEmit);

  const { right, left, center } = toRefs(props.tpl_info);

  const vm = getCurrentInstance();
  const { $sldComLanguage } = vm?.appContext.config.globalProperties;

  const leftTitleStyle = () => {
    let { title_info } = unref(left);
    return {
      backgroundColor: title_info.title_bg_color,
      color: title_info.title_color,
    };
  };

  const centerTitleStyle = () => ({
    backgroundColor: unref(center).title_info.title_bg_color,
    color: unref(center).title_info.title_color,
  });

  const rightTitleStyle = () => ({
    backgroundColor: unref(right).title_info.title_bg_color,
    color: unref(right).title_info.title_color,
  });

  const formAdv09TitleConfig = (title, position, variant) => {
    let content = [
      {
        type: 'input',
        label: $sldComLanguage('标题名称'),
        placeholder: $sldComLanguage('请输入标题名称'),
        extra: $sldComLanguage('最多5个字'),
        maxlength: 5,
        initialValue: variant.title_info.title_name,
        name: 'title_name',
        rules: [{ required: true }],
      },
      {
        type: 'more_color_picker',
        label: $sldComLanguage('标题颜色'),
        initialValue: variant.title_info.title_color,
        name: 'title_color',
      },
      {
        type: 'more_color_picker',
        label: $sldComLanguage('背景色'),
        initialValue: variant.title_info.title_bg_color,
        name: 'title_bg_color',
      },
    ];

    return formTitleConfig(title, position, content);
  };

  const formAdv09ImgConfig = (title, position, variant) => {
    let content = {
      width: variant.width,
      height: variant.height,
      show_width: variant.width,
      show_height: variant.height,
      data: variant.data,
    };

    return formMoreImgConfig(title, position, content);
  };

  const adv09LeftImgConfig = formAdv09ImgConfig(
    $sldComLanguage('左侧图片设置'),
    'left_img',
    unref(left),
  );

  const adv09LeftTitleConfig = formAdv09TitleConfig(
    $sldComLanguage('左侧标题设置'),
    'left_title',
    unref(left),
  );

  const adv09CenterImgConfig = formAdv09ImgConfig(
    $sldComLanguage('中间图片设置'),
    'center_img',
    unref(center),
  );

  const adv09CenterTitleConfig = formAdv09TitleConfig(
    $sldComLanguage('中间标题设置'),
    'center_title',
    unref(center),
  );

  const adv09RightImgConfig = formAdv09ImgConfig(
    $sldComLanguage('右侧图片设置'),
    'right_img',
    unref(right),
  );

  const adv09RightTitleConfig = formAdv09TitleConfig(
    $sldComLanguage('右侧标题设置'),
    'right_title',
    unref(right),
  );

  const editConfirmHandler = (values, { position }) => {
    let [value] = values;
    switch (position) {
      case 'left_img': {
        unref(left).data = value.parent_data;
        break;
      }

      case 'left_title': {
        left.value.title_info = value;
        break;
      }

      case 'center_img': {
        unref(center).data = value.parent_data;
        break;
      }

      case 'center_title': {
        center.value.title_info = value;
        break;
      }

      case 'right_img': {
        unref(right).data = value.parent_data;
        break;
      }

      case 'right_title': {
        right.value.title_info = value;
        break;
      }
    }
    emit('save_tpl_data', props.tpl_info);
  };
  createTplContext(props.mode, editConfirmHandler);
</script>

<style lang="less" scoped>
  /* adv_09-start */
  .adv_09_wrap {
    position: relative;
    width: 100%;
    width: 1210px;
    margin: auto;
    clear: both;
    overflow: hidden;
  }

  .adv_09_wrap .item {
    width: 396px;
    height: 450px;
    float: left;
    background-color: #fff;
  }

  .adv_09_wrap .item .top_title {
    position: relative;
    height: 58px;
    font-size: 23px;
    line-height: 58px;
    text-align: center;
    cursor: pointer;
  }

  .main_con {
    position: relative;
    height: 372px;
    margin: 10px;
    overflow: hidden;
    background-color: #fff;
  }

  .left {
    .show_tip {
      position: relative;
      width: 187px;
      height: 123px;
      float: left;
      overflow: hidden;
      border-width: 0 0 1px 1px;
      border-style: solid;
      border-color: rgb(200 201 202);
      text-align: center;

      &:nth-child(2n + 1) {
        border-left: none;
      }

      &:last-child,
      &:nth-last-child(2) {
        border-bottom: none;
      }
    }
  }

  .adv_09_wrap .item .main_con a span {
    display: block;
    position: absolute;
    top: 50%;
    width: 100%;
    margin-top: -12px;
    color: #777;
    font-size: 13px !important;
    line-height: 24px;
  }

  .center {
    margin: 0 10px;

    .show_tip {
      display: block;
      position: relative;
      width: 376px;
      height: 123px;
      overflow: hidden;
      border-bottom: 1px solid #c8c9ca;
      border-width: 0 0 1px;
      border-style: solid;
      border-color: rgb(200 201 202);
      text-align: center;

      &:last-child {
        border-bottom: none;
      }
    }
  }

  .right {
    .show_tip {
      position: relative;
      width: 124px;
      height: 185px;
      float: left;
      overflow: hidden;
      border-width: 0 0 1px 1px;
      border-style: solid;
      border-color: rgb(200 201 202);
      text-align: center;

      &:nth-child(n + 4) {
        border-bottom: none;
      }

      &:nth-child(3n + 1) {
        border-left: none;
      }
    }
  }

  /* adv_09-end */
</style>
