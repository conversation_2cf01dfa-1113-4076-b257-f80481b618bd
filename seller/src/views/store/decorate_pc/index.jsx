import { TabPane, Tabs } from 'ant-design-vue';
import { defineComponent, ref, unref } from 'vue';
import SldComHeader from '@/components/SldComHeader/index.vue';
import HomePageList from './diy_page_lists.vue';
import AdvTemplate from './template_list.vue';
import './index.less'

export default defineComponent({
  setup() {
    const activeKey = ref(1);

    return () => (
      <div className="p-2 relative h-full">
        <div className="bg-white p-2 h-full flex flex-col">
          <SldComHeader title="PC装修"></SldComHeader>
          <Tabs
            type="card"
            activeKey={unref(activeKey)}
            onChange={(key) => (activeKey.value = key)}
            className="h-full"
          >
            <TabPane tab="实例化模板" key={1} className="h-full">
              <AdvTemplate/>
            </TabPane>
            <TabPane tab="首页装修" key={2}>
              <HomePageList/>
            </TabPane>
          </Tabs>
        </div>
      </div>
    );
  },
});
