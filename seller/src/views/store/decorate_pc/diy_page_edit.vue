<template>
  <div class="diy_page_edit flex flex-col">
    <FloorHeader @loadFloor="loadFloor" :decoDataFn="getDecoData" :decoInfo="otherDecoInfo" />
    <FloorMainTop />
    <div class="relative content_area">
      <FloorBannerItem
        :tpl_info="main_swiper_data.json"
        :floor-data="main_swiper_data"
        @loadFloor="(e) => loadFloor(e, '')"
        v-if="main_swiper_data.json"
        @updateFloor="updateFloor"
      ></FloorBannerItem>

      <div class="pc_main_diy flex-1 relative">
        <VueDraggable
          v-model="diyQueList"
          :animation="150"
          :group="{ name: 'dragGroup' }"
          :sort="true"
          @add="dataAdd"
          class="h-full"
        >
          <div v-for="item in diyQueList" :key="item.id" :id="item.id">
            <FloorItem
              :tpl_info="item.json"
              :floor-data="item"
              @handle-move-floor="handleMoveFloor"
              @loadFloor="(e) => loadFloor(e, item.id)"
            ></FloorItem>
          </div>
        </VueDraggable>
      </div>
      <FloorSelectModal
        :visible="floorSelectVisible"
        :type="floorSelectType"
        :tpl-id="floorSelectTplId"
        @cancel="floorSelectVisible = false"
        @confirm="floorLoadConfirm"
      />
    </div>
  </div>
</template>

<script setup>
  import { onMounted, reactive, ref, unref, watch, getCurrentInstance } from 'vue';
  import FloorHeader from './template/components/FloorHeader.vue';
  import { VueDraggable } from 'vue-draggable-plus';
  import FloorItem from './template/components/FloorItem.vue';
  import FloorMainTop from './template/components/FloorMainTop.vue';
  import FloorSelectModal from './template/components/FloorSelectModal.vue';
  import FloorBannerItem from './template/components/FloorBannerItem.vue';
  import { failTip } from '/@/utils/utils';
  import { extraProperty } from '/@/utils';
  import lodash from 'lodash-es';
  import { TplDetailApi } from '/@/api/decorate/deco_pc';
  import { useRoute } from 'vue-router';
  import { buildShortUUID } from '/@/utils/uuid';
  const route = useRoute();
  let decoInfo = {};
  const otherDecoInfo = ref({});
  const diyQueList = ref([]);
  const main_swiper_data = ref({});
  const floorSelectVisible = ref(false);
  const floorSelectType = ref('all');
  const floorSelectTplId = ref(0);

  const vm = getCurrentInstance();
  const { $sldComLanguage } = vm?.appContext.config.globalProperties;

  const getDecoData = () => {
    let floorData = unref(diyQueList);
    let swiperData = unref(main_swiper_data);
    return {
      floorData,
      decoInfo,
      swiperData,
    };
  };

  //获取装修页面数据
  const get_diy_page_detial = async () => {
    const { id } = route.query;
    const res = await TplDetailApi({ decoId: id });
    if (res.state == 200) {
      decoInfo = res.data;
      otherDecoInfo.value = extraProperty(res.data, ['decoId', 'isEnable']);
      let tmp = res.data.data ? JSON.parse(res.data.data.replace(/&quot;/g, '"')) : [];
      for (let i in tmp) {
        // 四栏商品推荐下架商品过滤
        if (tmp[i].json && tmp[i].json.type && tmp[i].json.type == 'adv_27') {
          for (let j in tmp[i].json) {
            if (j == 'left') {
              if (tmp[i].json[j] && tmp[i].json[j].data && tmp[i].json[j].data.goods_data) {
                let info = deconstruction(tmp[i].json[j].data.goods_data, 'goodsId', 1);
                tmp[i].json[j].data.goods_data = info.obj;
                tmp[i].json[j].data.goods_ids = info.ids;
              }
            } else if (j == 'right_center' || j == 'left_center' || j == 'right') {
              if (tmp[i].json[j] && tmp[i].json[j].data && tmp[i].json[j].data.goods_data) {
                let info = deconstruction(tmp[i].json[j].data.goods_data, 'goodsId', 2);
                tmp[i].json[j].data.goods_ids = info.ids;
                tmp[i].json[j].data.goods_data = JSON.parse(
                  JSON.stringify(arraySlice(info.obj, 4)),
                );
              }
            }
          }
        } else if (tmp[i].json && tmp[i].json.type && tmp[i].json.type == 'adv_26') {
          // 多屏商品推荐下架商品过滤
          let info = deconstruction(tmp[i].json.data.goods_data, 'goodsId', 2);
          tmp[i].json.data.goods_ids = info.ids;
          tmp[i].json.data.goods_data = JSON.parse(JSON.stringify(arraySlice(info.obj, 5)));
        } else if (tmp[i].json && tmp[i].json.type && tmp[i].json.type == 'adv_04') {
          // 猜你喜欢下架商品过滤
          if (
            tmp[i].json.data &&
            Array.isArray(tmp[i].json.data.goods_ids) &&
            tmp[i].json.data.goods_ids.length
          ) {
            if (tmp[i].json && tmp[i].json.data && tmp[i].json.data.goods_data) {
              let info = deconstruction(tmp[i].json.data.goods_data, 'goodsId', 1);
              tmp[i].json.data.goods_ids = info.ids;
              tmp[i].json.data.goods_data = JSON.parse(JSON.stringify(info.obj));
            }
          }
        } else if (tmp[i].json && tmp[i].json.type && tmp[i].json.type == 'adv_25') {
          // 多tab切换下架固定商品过滤
          if (tmp[i].json && tmp[i].json.data && Array.isArray(tmp[i].json.data)) {
            tmp[i].json.data.forEach((item) => {
              if (item.qc && !item.qc.showGoods) {
                let info = deconstruction(item.info, 'goodsId', 1);
                item.ids = info.ids;
                item.info = JSON.parse(JSON.stringify(info.obj));
              }
            });
          }
        }
        if (tmp[i].tplPcId == 28) {
          tmp[i].more_tab_index = 1;
          break;
        }
      }

      main_swiper_data.value = {
        json: {
          data: [],
          type: 'main_banner',
          width: 1920,
          height: 457, //高度为0的话表示不限制
          style: {
            borderRadius: 15,
          },
        },
      };

      if (tmp.length > 0) {
        for (let item of tmp) {
          if (/main_banner/.test(item.json.type)) {
            main_swiper_data.value = item;
          } else {
            diyQueList.value.push(item);
          }
        }
      }
    }
  };

  // 重组数组里套数组的结构 obj：一维数组 num：数组的数组里最多存入几条
  const arraySlice = (obj, num) => {
    let startX = 0;
    let dataList = [];
    while (startX <= obj?.length) {
      let tmpList = obj.slice(startX, (startX += num));
      if (tmpList.length) {
        dataList.push(tmpList);
      }
    }
    return dataList;
  };

  const deconstruction = (data, id, type) => {
    let obj = [];
    let ids = [];
    if (type == 2) {
      data.forEach((it) => {
        let arr = [];
        it.forEach((items) => {
          if (!items.state || (items.state && items.state == 3)) {
            arr.push(items);
            ids.push(items[id]);
          }
        });
        obj = [...obj, ...arr];
      });
    } else if (type == 1) {
      data.forEach((it) => {
        let arr = [];
        if (!it.state || (it.state && it.state == 3)) {
          arr.push(it);
          ids.push(it[id]);
        }
        obj = [...obj, ...arr];
      });
    }
    return {
      obj: JSON.parse(JSON.stringify(obj)),
      ids: JSON.parse(JSON.stringify(ids)),
    };
  };

  const updateFloor = (val) => {
    if (
      main_swiper_data.value &&
      main_swiper_data.value.json &&
      main_swiper_data.value.json.style
    ) {
      main_swiper_data.value.json.style.borderRadius = val.borderStyle ? 0 : 15;
    }
  };

  //上移，下移，删除
  const handleMoveFloor = (type, id) => {
    if (type == 'up') {
      for (let i = 0; i < unref(diyQueList).length; i++) {
        if (unref(diyQueList)[i].id == id) {
          let temp = lodash.cloneDeep(unref(diyQueList)[i - 1]);
          unref(diyQueList)[i - 1] = lodash.cloneDeep(unref(diyQueList)[i]);
          unref(diyQueList)[i] = temp;
          break;
        }
      }
    } else if (type == 'down') {
      for (let i = 0; i < unref(diyQueList).length; i++) {
        if (unref(diyQueList)[i].id == id) {
          let temp = lodash.cloneDeep(unref(diyQueList)[i]);
          unref(diyQueList)[i] = lodash.cloneDeep(unref(diyQueList)[i + 1]);
          unref(diyQueList)[i + 1] = temp;
          break;
        }
      }
    } else if (type == 'del') {
      diyQueList.value = diyQueList.value.filter((item) => item.id != id);
    }
  };

  // 装载，添加楼层
  let currentPosition;
  let currentId;
  const loadFloor = ({ position, type, tplId }, id) => {
    currentPosition = position;
    currentId = id;
    floorSelectTplId.value = tplId;
    floorSelectType.value = type;
    floorSelectVisible.value = true;
  };

  const dataAdd = () => {
    let more_tab = diyQueList.value.filter((item) => item.tplPcId == 28);
    if (more_tab.length > 1) {
      failTip(`${$sldComLanguage('已添加多TAB切换模块，不可再添加多TAB切换模块')}～`);
      for (let i in diyQueList.value) {
        if (diyQueList.value[i].tplPcId == 28 && !diyQueList.value[i].more_tab_index) {
          diyQueList.value.splice(i, 1);
          break;
        }
      }
      return;
    } else {
      if (more_tab.length == 1) {
        more_tab[0].more_tab_index = 1;
      }
    }
  };

  // 装载，添加楼层---确认事件
  const floorLoadConfirm = (val) => {
    if (val.tplPcId == 28 && currentPosition == 'first') {
      let more_tab = diyQueList.value.filter((item) => item.tplPcId == val.tplPcId);
      if (more_tab.length > 0) {
        failTip(`${$sldComLanguage('已添加多TAB切换模块，不可再添加多TAB切换模块')}～`);
        return;
      }
    }
    floorSelectVisible.value = false;
    let temp_data = JSON.parse(val.json.replace(/&quot;/g, '"'));
    temp_data.insTplId = val.dataId;
    temp_data.insTplName = val.name;
    if (val.tplPcType == 'main_banner') {
      unref(main_swiper_data).json = temp_data;
      unref(main_swiper_data).tplPcId = val.tplPcId;
    } else {
      let target = {
        id: buildShortUUID('diy'),
        html: val.html,
        key: '',
        json: temp_data,
        tplPcId: val.tplPcId,
      };
      if (!currentPosition) {
        //在最后添加楼层
        diyQueList.value.push(target);
      } else if (currentPosition == 'first') {
        diyQueList.value.unshift(target);
      } else {
        for (let i = 0; i < diyQueList.value.length; i++) {
          if (diyQueList.value[i].id == currentId) {
            if (currentPosition == 'top') {
              diyQueList.value.splice(i, 0, target);
            } else if (currentPosition == 'bottom') {
              diyQueList.value.splice(i + 1, 0, target);
            } else if (currentPosition == 'cur') {
              diyQueList.value[i] = target;
            }
            break;
          }
        }
      }
    }
  };

  onMounted(() => {
    get_diy_page_detial();
  });
</script>

<style lang="less" scoped>
  .diy_page_edit {
    height: 100%;
    padding-top: 48px;
    background-color: #fff;
  }

  .content_area {
    width: 100%;
    background-repeat: no-repeat;
    background-position: 50% 0;
  }

  .pc_main_diy {
    min-height: 500px;
  }
</style>
