<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader :title="L('结算账号')" />
      <BasicTable @register="standardTable">
        <template #tableTitle>
          <div class="toolbar flex flex-row">
            <div class="toolbar_btn" @click="handleClick(null, 'add')">
              <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#fff" />
              <span>{{ L('新增结算账号') }}</span>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key == 'default_account'">
            <Switch
              :checkedValue="1"
              :unCheckedValue="0"
              v-model:checked="record.isDefault"
              @change="switchChange(record)"
            ></Switch>
          </template>

          <template v-if="column.key == 'accountNumber'">
            <span>{{
              record.accountType == 1 ? record.bankAccountNumber : record.alipayAccount
            }}</span>
          </template>

          <template v-if="column.key == 'action'">
            <div class="flex flex-row items-center justify-center">
              <span
                class="common_page_edit cursor-pointer hover:text-[#FF6A12]"
                @click="handleClick(record, 'edit')"
                >{{ L('编辑') }}</span
              >
              <Popconfirm
                :title="L('删除后不可恢复，是否确定删除？')"
                @confirm="handleClick(record, 'del')"
              >
                <span class="common_page_edit cursor-pointer hover:text-[#FF6A12]">{{
                  L('删除')
                }}</span>
              </Popconfirm>
            </div>
          </template>
        </template>
      </BasicTable>

      <SldModal
        :content="modalContent"
        v-bind="modalState"
        @confirm-event="(e) => modalEvent('confirm', e)"
        @cancle-event="modalEvent('cancel')"
        @cascaderChangeEvent="cascaderChangeEvent"
        @radioChangeEvent="radioChangeEvent"
      ></SldModal>
    </div>
  </div>
</template>

<script setup>
  import SldComHeader from '@/components/SldComHeader/index.vue';
  import { useTable, BasicTable } from '@/components/Table';
  import { Switch, Popconfirm } from 'ant-design-vue';
  import { sucTip, failTip } from '@/utils/utils';
  import {
    getAccountListApi,
    postAccountDefaultApi,
    postAccountAddApi,
    postAccountUpdate,
    postAccountDelete,
  } from '@/api/bill/account';
  import SldModal from '@/components/SldModal/index.vue';
  import { getCurrentInstance, reactive, ref } from 'vue';
  import areaData from '@/assets/json/area.json';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const columns = [
    {
      title: L('结算账号'),
      dataIndex: 'accountNumber',
    },
    {
      title: L('账户类型'),
      dataIndex: 'accountTypeValue',
    },
    {
      title: L('默认账号'),
      dataIndex: 'default_account',
    },
  ];

  const modalState = reactive({
    width: 600,
    title: L('新增结算账号'),
    visible: false,
    confirmBtnLoading: false,
    showFoot: true,
  });

  const variant_memo = {
    accountId: 0,
    cascaderName: '',
  };

  const accountTypeContent = {
    type: 'radio_orignal',
    label: L('账号类型'),
    name: 'accountType',
    callback: true,
    data: [
      {
        value: L('银行账号'),
        key: 1,
      },
      {
        value: L('支付宝账号'),
        key: 2,
      },
    ],
    initialValue: 1,
  };

  const bankAccountContent = [
    {
      type: 'input',
      label: '银行开户名',
      name: 'bankAccountName',
      placeholder: L('请输入银行开户名'),
      extra: L('最多输入50个字'),
      maxLength: 50,
      rules: [
        {
          required: true,
          whitespace: true,
          message: L('请输入银行开户名'),
        },
      ],
      initialValue: '',
    },
    {
      type: 'input',
      label: L('公司银行账号'),
      name: 'bankAccountNumber',
      placeholder: L('请输入公司银行账号'),
      extra: L('最多输入30位数字'),
      maxLength: 30,
      rules: [
        {
          required: true,
          whitespace: true,
          message: L('请输入公司银行账号'),
        }, {
          pattern: /^[0-9]{1,30}$/,
          message: '请输入纯数字',
        },
      ],
      initialValue: '',
    },
    {
      type: 'input',
      label: L('开户银行'),
      name: 'bankBranch',
      placeholder: L('请输入开户银行名称'),
      extra: L('最多输入30字'),
      maxLength: 30,
      rules: [
        {
          required: true,
          whitespace: true,
          message: L('请输入开户银行名称'),
        },
      ],
      initialValue: '',
    },
    {
      type: 'cascader_common',
      label: L('开户行所在地'),
      name: 'area',
      placeholder: L('请选择开户行所在地'),
      data: areaData,
      fieldNames: {
        children: 'children',
        label: 'regionName',
        value: 'regionCode',
      },
      rules: [
        {
          required: true,
          message: L('请选择开户行所在地'),
        },
      ],
      initialValue: [],
      returnName: true,
    },
    {
      type: 'switch',
      label: L('设为默认'),
      name: 'isDefault',
      unCheckedValue: 0,
      checkedValue: 1,
      initialValue: 1,
    },
  ];

  const aliPayContent = [
    {
      type: 'input',
      label: L('支付宝姓名'),
      name: 'alipayName',
      placeholder: L('请输入支付宝姓名'),
      extra: L('最多输入10字'),
      maxLength: 10,
      rules: [
        {
          required: true,
          message: L('请输入支付宝姓名'),
        },
      ],
      initialValue: '',
    },
    {
      type: 'input',
      label: L('支付宝账号'),
      name: 'alipayAccount',
      placeholder: L('请输入支付宝账号'),
      extra: L('最多输入30字'),
      maxLength: 30,
      rules: [
        {
          required: true,
          message: L('请输入支付宝账号'),
        },
      ],
      initialValue: '',
    },
    {
      type: 'switch',
      label: L('设为默认'),
      name: 'isDefault',
      unCheckedValue: 0,
      checkedValue: 1,
      initialValue: 1,
    },
  ];

  const modalContent = ref([]);

  const [standardTable, { reload }] = useTable({
    api: getAccountListApi,
    columns,
    fetchSetting: {
      listField: 'data.list',
      pageField: 'current',
    },
    actionColumn: {
      title: L('操作'),
      width: 200,
      dataIndex: 'action',
    },
    indexColumnProps: {
      width: 150,
    },
  });

  const switchChange = async (e) => {
    const res = await postAccountDefaultApi({
      accountId: e.accountId,
      isDefault: e.isDefault,
    });

    if (res.state == 200) {
      sucTip(res.msg);
      reload();
    } else {
      failTip(res.msg);
    }
  };

  const handleClick = (obj, type) => {
    if (type == 'add') {
      modalContent.value = JSON.parse(JSON.stringify([{ ...accountTypeContent }, ...bankAccountContent]));
      let temp = modalContent.value.filter(item => item.name == 'bankAccountNumber');
      if (temp.length > 0) {
        temp[0].rules = [
          {
            required: true,
            whitespace: true,
            message: L('请输入公司银行账号'),
          }, {
            pattern: /^[0-9]{1,30}$/,
            message: '请输入纯数字',
          },
        ];
      }
      modalState.visible = true;
    } else if (type == 'edit') {
      radioChangeEvent(obj.accountType);
      modalContent.value.map((val) => {
        if (val.name == 'area') {
          val.initialValue = [obj.provinceCode, obj.cityCode];
          obj.districtCode && val.initialValue.push(obj.districtCode);
        } else {
          val.initialValue = obj[val.name];
        }
      });
      variant_memo.accountId = obj.accountId;
      variant_memo.areaName = obj.addressAll;
      modalState.title = L('编辑结算账号');
      modalState.visible = true;
    } else if (type == 'del') {
      postAccountDelete({ accountId: obj.accountId }).then((res) => {
        if (res.state == 200) {
          sucTip(res.msg);
          reload();
        } else {
          failTip(res.msg);
        }
      });
    }
  };

  const modalEvent = async (type, e) => {
    if (type == 'cancel') {
      modalState.visible = false;
      modalContent.value = [];
    } else {
      let params = JSON.parse(JSON.stringify(e));
      if (params.accountType == 1) {
        params.provinceCode = e.area[0];
        params.cityCode = e.area[1];
        if (e.area[2]) {
          params.districtCode = e.area[2];
        }
        delete params.area;
        params.addressAll = variant_memo.cascaderName;
      }
      const res = variant_memo.accountId
        ? await postAccountUpdate({ ...params, accountId: variant_memo.accountId })
        : await postAccountAddApi(params);
      if (res.state == 200) {
        sucTip(res.msg);
        modalEvent('cancel');
        variant_memo.accountId = 0;
        reload();
      } else {
        failTip(res.msg);
      }
    }
  };

  const cascaderChangeEvent = (opt) => {
    variant_memo.cascaderName = opt.map((v) => v.regionName).join('');
  };

  const radioChangeEvent = (value) => {
    if (value == 2) {
      modalContent.value = JSON.parse(JSON.stringify([{ ...accountTypeContent, initialValue: 2 }, ...aliPayContent]));
    } else {
      modalContent.value = JSON.parse(JSON.stringify([{ ...accountTypeContent, initialValue: 1 }, ...bankAccountContent]));
      let temp = modalContent.value.filter(item => item.name == 'bankAccountNumber');
      if (temp.length > 0) {
        temp[0].rules = [
          {
            required: true,
            whitespace: true,
            message: L('请输入公司银行账号'),
          }, {
            pattern: /^[0-9]{1,30}$/,
            message: '请输入纯数字',
          },
        ];
      }
    }
  };
</script>

<style lang="scss" scoped>
</style>
