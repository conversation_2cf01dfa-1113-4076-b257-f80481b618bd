<template>
  <div class="section_padding bill_list_detail">
    <div class="section_padding_back">
      <SldComHeader :title="L('结算详情')" :back="true" />
      <div class="height_detail">
        <section>
          <div class="flex_row_center_start progress">
            <div
              v-for="(item, index) in bill_progress_data"
              :key="index"
              class="flex_column_start_center item"
            >
              <div class="top flex_row_center_center">
                <span class="left_line" />
                <img :src="getImagePath(`images/bill/${index + 1}_${item.cur_state}.png`)" />
                <span class="right_line" />
              </div>
              <span class="state">{{ item.state }}</span>
              <span class="time">{{ item.time }}</span>
            </div>
          </div>
          <div class="state_part flex_column_start_center" v-if="bill_detail_data.state == 1">
            <span class="title">{{ L('等待店铺确认') }}</span>

            <Popconfirm
              placement="leftBottom"
              :title="L('我已确认核对该账单，确认无误')"
              @confirm="checkConfirm"
              :ok-text="L('确定')"
              :cancel-text="L('取消')"
            >
              <div class="invoice_btn">{{ L('确认结算单') }}</div>
            </Popconfirm>
          </div>
          <div class="state_part flex_column_start_center" v-if="bill_detail_data.state == 2">
            <span class="title">{{ L('等待平台审核') }}</span>
          </div>
          <div class="state_part flex_column_start_center" v-if="bill_detail_data.state == 3">
            <span class="title">{{ L('等待平台结算') }}</span>
          </div>
          <div class="state_part flex_column_start_center" v-if="bill_detail_data.state == 4">
            <span class="title">{{ L('结算完成') }}</span>
          </div>
        </section>

        <section class="mt-10">
          <SldComHeader :title="L('结算信息')" />
          <div class="detail_total_part flex_row_start_center">
            <img :src="getImagePath('images/bill/detail_total_icon.png')" />
            <span class="amount_total"
              >&nbsp;{{ L('结算金额') }}{{ L('￥') }}{{ Number(bill_detail_data.settleAmount).toFixed(2) }} =
              &nbsp;</span
            >
            <span class="amount_detail">
              {{ L('订单金额') }}{{ L('￥') }}{{ Number(bill_detail_data.orderAmount).toFixed(2) }}- {{ L('支付平台手续费') }}{{ L('￥')
              }}{{ Number(bill_detail_data.payFeeAmount).toFixed(2) }} - {{ L('平台佣金')
              }}{{ L('￥') }}{{ Number(bill_detail_data.commission).toFixed(2) }} + {{ L('退还佣金') }}{{ L('￥')
              }}{{ Number(bill_detail_data.refundCommission).toFixed(2) }} - {{ L('退单金额') }}{{ L('￥')
              }}{{ Number(bill_detail_data.refundAmount).toFixed(2) }} 
              <span v-if="!getUserInfo.shopType||(getUserInfo.shopType&&getUserInfo.shopType!='3')">
                + {{ L('平台优惠券') }}{{ L('￥')
                }}{{ bill_detail_data.platformVoucherAmount }} + {{ L('积分抵扣金额') }}{{ L('￥')
                }}{{ bill_detail_data.integralCashAmount }} - {{ L('定金赔偿金额') }}{{ L('￥')
                }}{{ bill_detail_data.compensationAmount }} - {{ L('供应商结算金额') }}{{ L('￥')
                }}{{ bill_detail_data.thirdOrderAmount }}
              </span>
              <!-- spreader-1-start -->
              <span v-if="spreaderFlag">
                - {{ L('推手佣金') }}{{ L('￥') }}{{ bill_detail_data.attachCommission }}
              </span>
              <!-- spreader-1-end -->
              
            </span>
          </div>
        </section>

        <section class="mt-10 pr-5">
          <SldComHeader :title="L('结算信息')" />
          <div>
            <Row type="flex" class="row">
              <Col flex="1">
                <div class="text-right col_title">{{ L('结算单号') }}</div>
              </Col>
              <Col flex="2">
                <div class="col_value">{{ bill_base_data.billSn }}</div>
              </Col>
              <Col flex="1">
                <div class="text-right col_title">{{ L('结算起止时间') }}</div>
              </Col>
              <Col flex="2">
                <div class="col_value">{{ bill_base_data.billTime }}</div>
              </Col>
            </Row>
            <Row type="flex" class="row">
              <Col flex="1">
                <div class="text-right col_title">{{ L('店铺名称') }}</div>
              </Col>
              <Col flex="2">
                <div class="col_value">{{ bill_base_data.storeName }}</div>
              </Col>
              <Col flex="1">
                <div class="text-right col_title">{{ L('店铺联系人') }}</div>
              </Col>
              <Col flex="2">
                <div class="col_value">{{ bill_base_data.contactName }}</div>
              </Col>
            </Row>
            <Row class="row">
              <Col :span="4">
                <div class="text-right col_title">{{ L('店铺联系电话') }}</div>
              </Col>
              <Col span="8">
                <div class="col_value the_last">{{ bill_base_data.contactPhone }}</div>
              </Col>
            </Row>
          </div>
        </section>

        <section
          class="mt-10 pr-5"
          v-if="
            bill_detail_data.state == 4 &&
            (bill_settle_img.paymentEvidence || bill_settle_img.paymentRemark)
          "
        >
          <SldComHeader :title="L('结算凭证')" />
          <div>
            <Row type="flex" class="row">
              <Col flex="1">
                <div
                  class="text-right col_title"
                  :style="{
                    height: bill_settle_img.paymentEvidence ? '100px' : '44px',
                    lineHeight: bill_settle_img.paymentEvidence ? '100px' : '44px',
                  }"
                  >{{ L('打款备注') }}</div
                >
              </Col>
              <Col flex="5">
                <div
                  class="col_value paymentRemark flex_row_start_center"
                  :style="{
                    height: bill_settle_img.paymentEvidence ? '100px' : '44px',
                  }"
                >
                  <span>{{ bill_settle_img.paymentRemark }}</span>
                </div>
              </Col>
            </Row>
            <Row class="row">
              <Col flex="1">
                <div
                  class="text-right col_title"
                  :style="{
                    height: bill_settle_img.paymentEvidence ? '100px' : '44px',
                    lineHeight: bill_settle_img.paymentEvidence ? '100px' : '44px',
                  }"
                  >{{ L('结算凭证') }}</div
                >
              </Col>
              <Col flex="5">
                <div
                  class="col_value flex_row_start_center"
                  :style="{
                    height: bill_settle_img.paymentEvidence ? '100px' : '44px',
                    lineHeight: bill_settle_img.paymentEvidence ? '100px' : '44px',
                  }"
                >
                  <Image
                    v-if="bill_settle_img.paymentEvidence"
                    :src="bill_settle_img.paymentEvidence"
                    style="width: 86px; margin-bottom: 4px"
                  ></Image>
                  <span v-else>--</span>
                </div>
              </Col>
            </Row>
          </div>
        </section>

        <section>
          <SldComHeader 
            :title="L('结算订单信息')" 
            :tipBtn="L('导出结算单')"
            tipBtnIcon="iconziyuan23"
            @tip-btn-click="handleExport"
            style="margin-top: 10px;"
          />
          <BasicTable @register="standardTable" class="bill_table">
            <template #bodyCell="{ column, record,text }">
              <template v-if="column.key == 'finishTime'">
                {{ text?text:'--' }}
              </template>
              <template v-if="column.key == 'center'">
                <RouterLink
                  :to="{
                    path: `/order/order_lists_to_detail`,
                    query: {
                      orderSn: record.orderSn,
                    },
                  }"
                >
                  {{ L('查看') }}
                </RouterLink>
              </template>
            </template>
          </BasicTable>
        </section>
      </div>
    </div>
  </div>
</template>

<script setup>
  import SldComHeader from '@/components/SldComHeader/index.vue';
  import { getImagePath } from '@/utils';
  import { Popconfirm, Row, Col, Image, Modal } from 'ant-design-vue';
  import { sucTip, failTip } from '@/utils/utils';
  import { getCurrentInstance, onMounted, reactive, ref, computed } from 'vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import { RouterLink } from 'vue-router';
  import { getSettingListApi } from '/@/api/common/common';
  import { useRoute, useRouter } from 'vue-router';
  import { getBillDetailApi, postBillConfirmApi, exportBillDetailApi } from '@/api/bill/list';
  import { useUserStore } from '/@/store/modules/user';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;
  const userStore = useUserStore();

  const getUserInfo = computed(() => {
    return userStore.getStoreInfo;
  });

  const bill_detail_data = reactive({
    set value(data) {
      Object.keys(data).forEach((key) => {
        this[key] = data[key];
      });
    },
  });

  const bill_base_data = reactive({
    billSn: '',
    billTime: '',
    storeName: '',
    contactName: '',
    contactPhone: '',
    set value(data) {
      Object.keys(this).forEach((key) => {
        if (key !== 'value') {
          if (key == 'billTime') {
            this.billTime = data.startTime + '~' + data.endTime;
          } else {
            this[key] = data[key];
          }
        }
      });
    },
  });

  const router = useRouter();

  const bill_settle_img = reactive({
    paymentEvidence: '',
    paymentRemark: '',
    setData(data) {
      this.paymentEvidence = data.paymentEvidence;
      this.paymentRemark = data.paymentRemark;
    },
  });

  const bill_progress_data = [
    {
      code: 'createBill',
      state: L('生成结算单'),
      time: '',
      state_code: 1,
      cur_state: 'cur',
    },
    {
      code: 'storeConfirm',
      state: L('店铺确认'),
      time: '',
      state_code: 2,
      cur_state: 'no',
    },
    {
      code: 'systemCheck',
      state: L('平台审核'),
      time: '',
      state_code: 3,
      cur_state: 'no',
    },
    {
      code: 'finish',
      state: L('结算完成'),
      time: '',
      state_code: 4,
      cur_state: 'no',
    },
  ]; //结算进度数据
  //spreader-2-start
  const spreaderFlag = ref(false);
  //spreader-2-end

  const columns_info = ref([
    {
      title: L('订单号'),
      dataIndex: 'orderSn',
      align: 'center',
      width: 120,
    },
    {
      title: L('商品名称'),
      dataIndex: 'goodsNames',
      align: 'center',
      width: 150,
    },
    {
      title: L('商品ID'),
      dataIndex: 'goodsIds',
      align: 'center',
      width: 150,
    },
    {
      title: L('商品数'),
      dataIndex: 'productNum',
      align: 'center',
      width: 100,
      customRender: ({ text }) => Number(text || 0)
    },
    {
      title: L('订单金额(元)'),
      dataIndex: 'orderAmount',
      align: 'center',
      width: 120,
      customRender: ({ text }) => Number(text).toFixed(2)
    },
    {
      title: L('支付平台手续费(元)'),
      helpMessage: L('微信/支付宝等支付平台手续费'),
      dataIndex: 'payFeeAmount',
      align: 'center',
      width: 170,
      customRender: ({ text }) => Number(text).toFixed(2)
    },
    {
      title: L('佣金(元)'),
      helpMessage: L('平台抽取的分类佣金'),
      dataIndex: 'commission',
      align: 'center',
      width: 120,
      customRender: ({ text }) => Number(text).toFixed(2)
    },
    {
      title: L('退还佣金(元)'),
      helpMessage: L('订单发生退款退货时，平台将不抽取这部分佣金'),
      dataIndex: 'refundCommission',
      align: 'center',
      width: 150,
      customRender: ({ text }) => Number(text).toFixed(2)
    },
    {
      title: L('退单金额(元)'),
      dataIndex: 'refundAmount',
      align: 'center',
      width: 120,
      customRender: ({ text }) => Number(text).toFixed(2)
    },
    {
      title: L('平台优惠券'),
      dataIndex: 'platformVoucherAmount',
      align: 'center',
      width: 120,
      customRender: ({ text }) => Number(text).toFixed(2)
    },
    {
      title: L('积分抵扣金额'),
      dataIndex: 'integralCashAmount',
      align: 'center',
      width: 120,
      customRender: ({ text }) => Number(text).toFixed(2)
    },
    {
      title: L('定金赔偿金额(元)'),
      helpMessage: L('对于定金预售活动，因商家问题导致无法发货，对会员赔偿的金额'),
      dataIndex: 'compensationAmount',
      align: 'center',
      width: 150,
      customRender: ({ text }) => Number(text).toFixed(2)
    },
    {
      title: L('供应商结算金额(元)'),
      dataIndex: 'thirdOrderAmount',
      align: 'center',
      width: 150,
      customRender: ({ text }) => Number(text).toFixed(2)
    },
    {
      title: L('下单日期'),
      dataIndex: 'createTime',
      align: 'center',
      width: 150,
    },
    {
      title: L('完成日期'),
      dataIndex: 'finishTime',
      align: 'center',
      width: 150,
    },
    {
      title: L('操作'),
      dataIndex: 'center',
      align: 'center',
      width: 100,
      fixed: 'right'
    },
  ]); //订单商品规格表头

  // dev_supplier-start
  if(getUserInfo.value.shopType&&getUserInfo.value.shopType=='3'){
    columns_info.value = [
      {
        title: L('订单号'),
        dataIndex: 'orderSn',
        align: 'center',
        width: 120,
      },
      {
        title: L('商品名称'),
        dataIndex: 'goodsNames',
        align: 'center',
        width: 150,
      },
      {
        title: L('商品ID'),
        dataIndex: 'goodsIds',
        align: 'center',
        width: 150,
      },
      {
        title: L('商品数'),
        dataIndex: 'productNum',
        align: 'center',
        width: 100,
        customRender: ({ text }) => Number(text || 0)
      },
      {
        title: L('订单金额(元)'),
        dataIndex: 'orderAmount',
        align: 'center',
        width: 120,
        customRender: ({ text }) => Number(text).toFixed(2)
      },
      {
        title: L('平台手续费(元)'),
        helpMessage: L('微信/支付宝等支付平台手续费'),
        dataIndex: 'payFeeAmount',
        align: 'center',
        width: 170,
        customRender: ({ text }) => Number(text).toFixed(2)
      },
      {
        title: L('佣金(元)'),
        helpMessage: L('平台抽取的分类佣金'),
        dataIndex: 'commission',
        align: 'center',
        width: 120,
        customRender: ({ text }) => Number(text).toFixed(2)
      },
      {
        title: L('退还佣金(元)'),
        helpMessage: L('订单发生退款退货时，平台将不抽取这部分佣金'),
        dataIndex: 'refundCommission',
        align: 'center',
        width: 150,
        customRender: ({ text }) => Number(text).toFixed(2)
      },
      {
        title: L('退单金额(元)'),
        dataIndex: 'refundAmount',
        align: 'center',
        width: 120,
        customRender: ({ text }) => Number(text).toFixed(2)
      },
      {
        title: L('平台优惠券'),
        dataIndex: 'platformVoucherAmount',
        align: 'center',
        width: 120,
        customRender: ({ text }) => Number(text).toFixed(2)
      },
      {
        title: L('积分抵扣金额'),
        dataIndex: 'integralCashAmount',
        align: 'center',
        width: 120,
        customRender: ({ text }) => Number(text).toFixed(2)
      },
      {
        title: L('定金赔偿金额(元)'),
        helpMessage: L('对于定金预售活动，因商家问题导致无法发货，对会员赔偿的金额'),
        dataIndex: 'compensationAmount',
        align: 'center',
        width: 150,
        customRender: ({ text }) => Number(text).toFixed(2)
      },
      {
        title: L('供应商结算金额(元)'),
        dataIndex: 'thirdOrderAmount',
        align: 'center',
        width: 150,
        customRender: ({ text }) => Number(text).toFixed(2)
      },
      {
        title: L('下单日期'),
        dataIndex: 'createTime',
        align: 'center',
        width: 150,
      },
      {
        title: L('完成日期'),
        dataIndex: 'finishTime',
        align: 'center',
        width: 150,
      },
      {
        title: L('操作'),
        dataIndex: 'center',
        align: 'center',
        width: 100,
        fixed: 'right'
      },
      {
        title: L('完成日期'),
        dataIndex: 'finishTime',
        align: 'center',
        width: 150,
      },
      {
        title: L('操作'),
        dataIndex: 'center',
        align: 'center',
        width: 100,
      },
    ]
  }
  // dev_supplier-end

  

  const route = useRoute();
  const [standardTable, { setTableData, getColumns, setColumns }] = useTable({
    pagination: false,
    columns: columns_info,
    scroll: { x: 2300 },
  });

  const checkConfirm = async () => {
    let { id } = route.query;
    const res = await postBillConfirmApi({ billId: id });
    if (res.state == 200) {
      sucTip(res.msg);
      userStore.setDelKeepAlive(['Lists']);
      get_bill_detail();
    } else if (res.state == 267) {
      Modal.info({
        style: { padding: 24, background: '#fff', borderRadius: 5 },
        title: L('温馨提示'),
        content: res.msg,
        okText: L('确定'),
        okType: 'primary',
        cancelText: L('取消'),
        onOk() {
          //跳转到结算账号页面
          router.push('/bill/account');
        },
        onCancel() {},
      });
    } else {
      failTip(res.msg);
    }
  };

  //spreader-3-start
  const get_sys_info = async () => {
    const res = await getSettingListApi({ str: 'spreader_is_enable' });
    if (res.state == 200) {
      spreaderFlag.value = res.data[0].value == 1;
      //推手开启，订单列表增加推手佣金列
      if (spreaderFlag.value) {
        let columns_order = getColumns();
        for (let i = 0; i < columns_order.length; i++) {
          if (columns_order[i].dataIndex == 'compensationAmount') {
            columns_order.splice(i + 1, 0, {
              title: L('推手佣金(元)'),
              dataIndex: 'attachCommission',
              align: 'center',
              width: 100,
            });
            setColumns(columns_order);
            break;
          }
        }
      }
    }
  };
  //spreader-3-end

  const get_bill_detail = async () => {
    let { id } = route.query;
    const res = await getBillDetailApi({ billId: id });
    if (res.state == 200) {
      bill_detail_data.value = res.data;

      //结算账户信息
      bill_base_data.value = res.data;

      bill_settle_img.setData(res.data);

      setTableData(res.data.orderList);

      for (let pro in bill_progress_data) {
        if (pro < bill_detail_data.logList.length) {
          bill_progress_data[pro].time = bill_detail_data.logList[pro].createTime;
        }
        if (bill_detail_data.state < bill_progress_data[pro].state_code) {
          bill_progress_data[pro].cur_state = 'no';
        } else if (bill_detail_data.state == bill_progress_data[pro].state_code) {
          bill_progress_data[pro].cur_state = 'cur';
        } else {
          bill_progress_data[pro].cur_state = 'pass';
        }
      }
    } else {
      failTip(res.msg);
    }
  };

  const handleExport = async () => {
    try {
      loading.value = true;
      const res = await exportBillDetailApi({ 
        billSns: bill_detail_data.billSn,
        fileName: `结算账单${bill_detail_data.billSn}`
      });
      if(res.state != undefined && res.state == 255){
        failTip(res.msg || L('导出失败'));
      }
    } catch (error) {
      failTip(L('导出失败'));
    } finally {
      loading.value = false;
    }
  };

  const loading = ref(false);

  onMounted(() => {
    // dev_supplier-start
    if(getUserInfo.value.shopType&&getUserInfo.value.shopType=='3'){
      get_bill_detail();
      return
    }
    // dev_supplier-end
    //spreader-4-start
    get_sys_info();
    //spreader-4-end
    get_bill_detail();
  });
</script>

<style lang="less">
  .vben-basic-help {
    display: inline;
  }
  .bill_list_detail{
    .bill_table{
      .ant-table-body{
        max-height: 400px !important;
        height: auto !important;
        overflow-x: auto !important;
      }
    }
  }
</style>
<style lang="less" scoped>
  .paymentRemark {
    line-height: unset !important;
  }

  .progress {
    margin-top: 50px;

    .item {
      .top {
        position: relative;
        width: 215px;

        img {
          width: 75px;
          height: 75px;
        }

        .left_line,
        .right_line {
          position: absolute;
          content: '';
          width: 50px;
          height: 0;
          border-top: 1px solid rgba(255, 113, 30, 0.3);
          top: 50%;
        }

        .left_line {
          left: 0;
        }

        .right_line {
          right: 0;
        }
      }

      .state {
        font-size: 14px;
        margin-top: 10px;
      }

      .time {
        font-size: 14px;
      }
    }

    .item.cur {
      .state {
        color: #ff711e;
      }

      .time {
        color: rgba(255, 113, 30, 0.5);
      }

      .left_line,
      .right_line {
        border-color: #ff711e;
      }
    }

    .item.no {
      .state {
        color: #999;
      }

      .left_line,
      .right_line {
        border-color: #eee;
      }
    }

    .item.pass {
      .state {
        color: rgba(255, 113, 30, 0.5);
      }

      .time {
        color: rgba(255, 113, 30, 0.3);
      }

      .left_line,
      .right_line {
        border-color: rgba(255, 113, 30, 0.3);
      }
    }
  }

  .invoice_btn {
    width: 120px;
    height: 36px;
    background: #ff711e;
    border-radius: 3px;
    color: rgba(255, 255, 255, 1);
    line-height: 36px;
    text-align: center;
    margin-top: 15px;
    cursor: pointer;
  }

  .state_part {
    margin-top: 50px;
    margin-bottom: 40px;

    .title {
      color: #333;
      font-size: 26px;
    }

    .tip {
      color: #999;
      font-size: 14px;
    }
  }

  /* 账单详情 start */
  .detail_total_part {
    width: 100%;
    height: 60px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 0 8px 0 rgba(153, 153, 153, 0.05), 0 0 8px 0 rgba(153, 153, 153, 0.05);
    border-radius: 6px;
    font-size: 16px;

    img {
      width: 36px;
      height: 36px;
      margin-left: 20px;
      margin-right: 15px;
    }

    .amount_detail {
      color: #333;
    }

    .amount_total {
      color: #ff2b4e;
      white-space: nowrap;
    }
  }

  .row {
    &:nth-child(n + 2) {
      .col_title,
      .col_value {
        border-top: none;
      }
    }

    .col_title {
      height: 44px;
      border: 1px solid #f0f0f0;
      padding: 0 10px;
      line-height: 44px;
      color: rgb(153, 153, 153);
    }

    .col_value {
      height: 44px;
      border: 1px solid #f0f0f0;
      border-left: 0;
      padding: 0 10px;
      line-height: 44px;
      color: rgb(153, 153, 153);
    }
  }

  .the_last {
    border-right: 1px solid #f0f0f0;
  }
</style>
