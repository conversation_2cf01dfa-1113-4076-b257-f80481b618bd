<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader
        :type="2"
        :clickFlag="true"
        :title="L('结算账单管理')"
        :tipTitle="L('温馨提示')"
        @handle-toggle-tip="handleToggleTip"
        :tipData="[
          // dev_supplier-start
          getUserInfo.shopType&&getUserInfo.shopType=='3'?
          L('计算公式：结算金额 = 订单金额 - 平台佣金 + 退还佣金 - 退单金额')
          :
          // dev_supplier-end
          L('计算公式：结算金额 = 订单金额 - 支付平台手续费 - 平台佣金 + 退还佣金 - 退单金额 + 平台优惠券 + 积分抵现金额 - 定金赔偿金额'
          //spreader-start
          +' - 推手佣金'
          //spreader-end
          +' - 供应商结算金额'
          // dev_o2o1-start
          + (getUserInfo.shopType==2 ? ' - 同城配送(三方配送)金额': '')
          // dev_o2o1-end
          ),
          L('结算流程：生成结算单 > 店铺确认 > 平台审核 > 结算完成'),
        ]"
      />
      <div style="width: 100%; height: 5px"></div>
      <div>
        <BasicTable @register="standardTable">
          <template #tableTitle>
            <div class="toolbar" style="justify-content: flex-end; margin-bottom: 0;">
              <div class="toolbar_btn" style="margin-right: 8px;" @click="handleBatchExport">
                <span>{{ L('批量导出结算单') }}</span>
              </div>
            </div>
          </template>
          <template #bodyCell="{ record, column }">
            <template v-if="column.key == 'billDate'">
              <div>{{ record.startTime }}</div>
              <div>~</div>
              <div>{{ record.endTime }}</div>
            </template>
            <template v-if="column.key == 'action'">
              <div class="flex items-center">
                <router-link :to="`/bill/lists/detail?id=${record.billId}&source=list`">
                  <span class="cursor-pointer rounded-sm p-1 hover:text-[#ff7324]">{{ L('查看') }}</span>
                </router-link>
                <span class="cursor-pointer rounded-sm p-1 hover:text-[#ff7324] ml-2" @click="handleExport(record)">{{ L('导出结算单') }}</span>
              </div>
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    
    <!-- 批量导出时间选择弹窗 -->
    <Modal
      v-model:visible="batchExportVisible"
      :title="L('批量导出结算单')"
      @ok="handleBatchExportConfirm"
      @cancel="batchExportVisible = false"
      :confirmLoading="loading"
    >
      <div style="padding: 24px 24px 0 24px">
        <div class="mb-4">
        <label class="block mb-2">{{ L('请选择导出时间范围') }}：</label>
        <DatePicker.RangePicker
          v-model:value="batchExportTime"
          :placeholder="[L('开始时间'), L('结束时间')]"
          format="YYYY-MM-DD"
          style="width: 100%"
        />
        </div>
        <div class="mb-4">
          <label class="block mb-2">{{ L('结算状态') }}：</label>
          <Select
            v-model:value="batchExportState"
            :placeholder="L('请选择结算状态')"
            style="width: 100%"
            allowClear
            :options="[
              { label: L('全部'), value: '' },
              { label: L('待确认'), value: '1' },
              { label: L('待审核'), value: '2' },
              { label: L('待结算'), value: '3' },
              { label: L('结算完成'), value: '4' }
            ]"
          />
        </div>
      </div>
    </Modal>
  </div>
</template>

<script setup>
  import { getCurrentInstance,computed, ref } from 'vue'
  import { Modal, Button, DatePicker, Select } from 'ant-design-vue';
  import SldComHeader from '@/components/SldComHeader/index.vue';
  import { useTable, BasicTable } from '@/components/Table';
  import { getBillListApi, exportBillDetailApi, exportBillListApi } from '@/api/bill/list';
  import { useUserStore } from '/@/store/modules/user';
  import { failTip } from '@/utils/utils';
  import { ContentTypeEnum } from '/@/enums/httpEnum';

  const loading = ref(false);
  const batchExportVisible = ref(false);
  const batchExportTime = ref([]);
  const batchExportState = ref('');
  const userStore = useUserStore();

  const getUserInfo = computed(() => {
    return userStore.getStoreInfo;
  });

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const [standardTable, {redoHeight, getDataSource, getForm}] = useTable({
    api: getBillListApi,
    columns: [
      { title: L('结算单号'), dataIndex: 'billSn' },
      { title: L('出帐时间'), dataIndex: 'createTime' },
      { title: L('本期应收'), dataIndex: 'settleAmount' },
      { title: L('结算日'), dataIndex: 'billDate' },
      { title: L('结算状态'), dataIndex: 'stateValue' },
    ],
    fetchSetting: {
      listField: 'data.list',
      pageField: 'current',
      sizeField: 'pageSize',
    },
    pagination: {
      pageSize: 10,
    },
    showIndexColumn: false,
    useSearchForm: true,
    formConfig: {
      schemas: [
        {
          colProps: { span: 6, style: 'width:280px !important;max-width:100%;flex:none' },
          component: 'Input',
          label: L('结算单号'),
          componentProps: {
            placeholder: L('请输入结算单号'),
          },
          field: 'billSn',
        },
        {
          colProps: { span: 6, style: 'width:280px !important;max-width:100%;flex:none' },
          component: 'Select',
          label: L('结算状态'),
          componentProps: {
            placeholder: L('请选择结算状态'),
            options: [
              { label: L('全部'), value: 0 },
              { label: L('待确认'), value: 1 },
              { label: L('待审核'), value: 2 },
              { label: L('待结算'), value: 3 },
              { label: L('结算完成'), value: 4 },
            ],
          },
          field: 'state',
        },
        {
          colProps: { span: 6, style: 'width:280px !important;max-width:100%;flex:none' },
          component: 'RangePicker',
          field: '[startTime, endTime]',
          label: L('结算日'),
          componentProps: {
            placeholder: [L('开始时间'), L('结束时间')],
            allowClear: true,
            format: 'YYYY-MM-DD',
          },
        },
      ],
      labelWidth: 100,
    },
    actionColumn: {
      dataIndex: 'action',
      title: L('操作'),
    },
    beforeFetch({ startTime, endTime, ...params }) {
      if (startTime && endTime) {
        startTime = startTime.split(' ')[0] + ' 00:00:00';
        endTime = endTime.split(' ')[0] + ' 23:59:59';
      }
      return { startTime, endTime, ...params };
    },
  });

  const handleToggleTip = ()=> {
    redoHeight()
  }

  // 导出结算单
  const handleExport = async (record) => {
    try {
      loading.value = true;
      const res = await exportBillDetailApi({ 
        billSns: record.billSn,
        fileName: `结算账单${record.billSn}`
      });
      if(res.state != undefined && res.state == 255){
        failTip(res.msg || L('导出失败'));
      }
    } catch (error) {
      failTip(L('导出失败'));
    } finally {
      loading.value = false;
    }
  };

  // 批量导出结算单
  const handleBatchExport = () => {
    batchExportVisible.value = true;
  };

  // 确认批量导出
  const handleBatchExportConfirm = async () => {
    if (!batchExportTime.value || batchExportTime.value.length !== 2) {
      failTip(L('请选择时间范围'));
      return;
    }

    try {
      loading.value = true;
      const userStartTime = batchExportTime.value[0].format('YYYY-MM-DD') + ' 00:00:00';
      const userEndTime = batchExportTime.value[1].format('YYYY-MM-DD') + ' 23:59:59';
      
      // 获取当前表格数据中的所有结算单号
      const dataSource = getDataSource();
      
      // 根据用户选择的时间范围筛选符合条件的结算单
      const filteredData = dataSource.filter(item => {
        if (!item.startTime || !item.endTime) return false;
        
        const itemStartTime = new Date(item.startTime).getTime();
        const itemEndTime = new Date(item.endTime).getTime();
        const userStart = new Date(userStartTime).getTime();
        const userEnd = new Date(userEndTime).getTime();
        
        // 检查结算日期区间是否与用户选择的时间范围有重叠
        return itemStartTime <= userEnd && itemEndTime >= userStart;
      });
      
      // 根据状态进一步筛选
      const finalFilteredData = batchExportState.value 
        ? filteredData.filter(item => item.state.toString() === batchExportState.value)
        : filteredData;
      
      if (finalFilteredData.length === 0) {
        failTip(L('所选时间范围和状态下没有符合条件的结算单'));
        return;
      }
      
      const billSnsList = finalFilteredData.map(item => item.billSn).filter(Boolean);
      
      const params = {
        billSns: billSnsList.join(','),
        startTime: userStartTime,
        endTime: userEndTime,
        fileName: `结算单导出_${userStartTime.split(' ')[0]}_${userEndTime.split(' ')[0]}`
      };
      
      // 只有选择了具体状态才传递state参数
      if (batchExportState.value) {
        params.state = batchExportState.value;
      }

      console.log('batchExportState.value:', batchExportState.value);
      console.log('批量导出参数:', params);
      const res = await exportBillListApi(params);
      console.log('导出结果:', res);
      
      // axios拦截器会自动处理octet-stream类型的下载
      // 这里只需要处理错误情况
      if (res instanceof Blob && res.type === 'application/json') {
        const text = await res.text();
        console.log('返回的JSON内容:', text);
        try {
          const errorData = JSON.parse(text);
          console.log('解析后的数据:', errorData);
          if (errorData.state === 255) {
            failTip(errorData.msg || L('导出失败'));
            return;
          }
        } catch (e) {
          console.log('JSON解析失败');
        }
      }
      
      // 成功情况下关闭弹窗
      batchExportVisible.value = false;
      batchExportTime.value = [];
      batchExportState.value = '';
    } catch (error) {
      console.error('导出错误:', error);
      failTip(L('导出失败'));
    } finally {
      loading.value = false;
    }
  };
</script>

<style lang="scss" scoped></style>
