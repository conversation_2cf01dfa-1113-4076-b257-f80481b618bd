.account_info {
  color: #333;
  margin-right: 100px;
  span {
    &:nth-child(2) {
      font-size: 20px;
      font-weight: 500;
      font-family: PingFangSC-Medium, PingFang SC;
      margin-left: 6px;
      margin-right: 8px;
    }
    &:nth-child(3) {
      color: #fd6918;
      font-size: 13px;
      cursor: pointer;
    }
  }
}

.account_btn {
  span {
    color: #333;
  }
  img {
    margin-left: 4px;
    margin-right: 4px;
  }
}

.bank_list {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  flex-wrap: wrap;

  .bank_item {
    width: 370px;
    min-height: 140px;
    border: 1px solid #dbdbdb;
    border-radius: 8px;
    box-shadow: 1px 0px 10px #dbdbdb;
    margin: 10px;
    padding: 10px 15px;
    transition: all .3s ease;
    &:hover {
      box-shadow: 1px 0px 10px #cecece;
    }
    .bank_top {
      width: 100%;
      .bank_title{
        width: 74%;
      }
      span {
        color: #666;
        font-size: 13px;
        line-height: 22px;
        margin-right: 6px;
      }
    }
    .bank_bottom {
      width: 100%;
      span {
        &:nth-child(2) {
          cursor: pointer;
          &:hover {
            color: #fd6918;
          }
        }
      }
    }
  }
}

.unbind_tips {
  height: 140px;
  line-height: 140px;
  text-align: center;
}
.modal_bank_form{
  .ant-form{
    .ant-form-item{
      margin-top: 14px;
      margin-bottom: 6px;
      margin-right: 0;

    }
  }
  .ant-form-item-label{
    width: 25%;
  }
  .ant-form-item-control {
    display: block;
    width: 58.********%;
  }
}

.modal_bank {
  .modal_tips {
    width: 100%;
    line-height: 30px;
    text-indent: 2rem;
    background: rgba(236, 128, 141, .6);
  }
  .modal_bank_money {
    span {
      flex-shrink: 0;
      margin-left: 8px;
      cursor: pointer;
      font-size: 14px;
      color: @primary-color;
    }
  } 
  .modal_bank_list {
    flex-wrap: wrap;
    max-height: 225px;
    overflow-y: auto;
    .modal_bank_item {
      flex-shrink: 0;
      margin-right: 15px;
      margin-bottom: 15px;
      .ant-radio-wrapper{
        align-items: center;
        .ant-radio{
          top: auto;
        }
      }
      .modal_bank_info {
        background: #f3f3f396;
        border-radius: 6px;
        padding: 0 10px;
        .modal_bank_title {
          position: relative;
          padding-right: 28px;
          span {
            position: absolute;
            right: -3px;
            top: 1px;
            color: #ff9147;
            font-size: 12px;
          }
        }
        .modal_bank_account {}
      }
    }
  }
}