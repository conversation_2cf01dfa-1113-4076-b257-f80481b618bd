<template>
  <div>
    <Spin :spinning="loading">
      <div class="comm_line_sperator">
        <div style="width: 100%; height: 15px; background: rgb(255, 255, 255);"></div>
        <StandardTableRow
          width="100%"
          :data="info_data"
          @callback-event="callbackEvent"
          @submit-event="submitEvent"
        />
      </div>
    </Spin>
  </div>
</template>
<script>
  export default {
    name: 'ExpressSetting',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, onMounted } from 'vue';
  import { Spin } from 'ant-design-vue';
  import { sucTip, failTip } from '@/utils/utils';
  import StandardTableRow from '/@/components/StandardTableRow/index.vue';
  import {
    getGoodsSettingDetailApi,
    getGoodsSetFreeFreightApi
  } from '/@/api/order/express';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const info_data = ref([
    {
      type: 'inputnum',
      label: L('免运费额度(元)'),
      key: 'freeFreightLimit',
      placeholder: '',
      initValue: '',
      min: 0,
      max: 999999,
      precision: 0,
      desc: L('默认为“0”，不填或0表示不设置免运费额度，大于0则表示购买金额达到该额度时免运费。'),
      width: 300,
    },
  ])

  const loading = ref(true);

  // 更新数据
  const callbackEvent = (item) => {
    let temp = info_data.value.filter((item) => item.key == item.key);
    if (temp.length > 0) {
      temp[0].value = item.val1;
    }
  };

  // 点击保存
  const submitEvent = async (val) => {
    try {
      loading.value = true;
      let res = await getGoodsSetFreeFreightApi(val);
      if (res.state == 200) {
        loading.value = false;
        sucTip(res.msg);
      }else{
        failTip(res.msg)
      }
    } catch (error) {}
  };

  // 获取详情
  const get_vendor_base_info = async()=> {
    let res = await getGoodsSettingDetailApi({})
    if(res.state == 200){
      loading.value = false
      for (let i in info_data.value) {
        info_data.value[i].value = res.data[info_data.value[i].key]?res.data[info_data.value[i].key]:0;
      }
      if (info_data.value.length > 0) {
        info_data.value.push({
          type: 'button',
          showSubmit: true,
          submitText: L('保存'),
          showCancle: false,
          width: 300,
        });
      }
    }
  }


  onMounted(() => {
    get_vendor_base_info()
  });
</script>
<style lang="less">
  .point_goods_list {
    .tabs_nav {
      .ant-tabs-nav {
        margin-bottom: 0;
      }
    }
  }
</style>
