<template>
  <div class="section_padding add_transport">
    <div class="section_padding_back">
      <SldComHeader
        :title="L('基本信息')"
      />
      <Spin :spinning="loading">
        <div class="tableListFormAdd add_coupon_height" style="padding-right: 10px;">
          <Form layout="inline" ref="formRef" :model="detail_info">
            <SldEditFormCom :search_data="search_data" :curForm_data="detail_info" v-if="show_flag"></SldEditFormCom>
            <template v-if="show_flag">
              <template v-for="(item,index) in express_info" :key="index">
                <SldExpressCom v-if="item.is_show" :key="index" :data_table="item.data_table" :curForm_data="detail_info" :type="item.type" @save-sele-area="save_sele_area"></SldExpressCom>
              </template>
            </template>
            <div style="width: 100%; height: 60px; background: #fff"></div>
          </Form>
          <div
            class="m_diy_bottom_wrap"
            :style="{ position: 'fixed', left: getRealWidth + 10 + 'px' }"
          >
            <div class="add_goods_bottom_btn" @click="goBack"> {{ L('返回') }} </div>
            <div class="add_goods_bottom_btn add_goods_bottom_btn_sel" @click="handleSaveAllData">
              {{ L('保存并返回') }}
            </div>
          </div>
        </div>
      </Spin>
    </div>
  </div>
</template>
<script setup>
  import { getCurrentInstance, ref, onMounted } from 'vue';
  import { Spin,Form } from 'ant-design-vue';
  import { sucTip, failTip } from '@/utils/utils';
  import { useRoute, useRouter } from 'vue-router';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import SldEditFormCom from '/@/components/SldEditFormCom/SldEditFormCom.vue';
  import SldExpressCom from '/@/components/SldExpressCom/SldExpressCom.vue';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import { useUserStore } from '/@/store/modules/user';
  import {
    getSellerExpressFreightAddApi,
    getSellerExpressFreightUpdateApi,
    getSellerExpressFreightDetailApi
  } from '/@/api/order/express';
  import { pageClose } from '/@/utils/utils';
  import { useMultipleTabStore } from '/@/store/modules/multipleTab';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const { getRealWidth } = useMenuSetting();
  const userStore = useUserStore();
  const route = useRoute();
  const tabStore = useMultipleTabStore();
  const router = useRouter();

  const loading = ref(false)
  const show_flag = ref(route.query!=undefined&&route.query.id!=undefined?false:true)//是否展示内容
  
  const formRef = ref()

  const detail_info = ref({
    chargeType:1,
    data_table:[],
  })

  const search_data = ref([
    {
      type: 'input',
      label: L('模板名称'),
      name: 'templateName',
      placeholder: L('请输入模板名称'),
      initialValue: '',
      maxLength: 10,
      rules: [{
        required: true,
        whitespace: true,
        message: L('请输入模板名称'),
      }],
    }, {
      type: 'radio',
      label: L('计价方式'),
      name: 'chargeType',
      width: 270,
      placeholder: L('请选择计价方式'),
      sel_data: [
        { name: L('按件'), key: 1 },
        { name: L('按重量'), key: 2 },
        { name: L('按体积'), key: 3 },
      ],
      initialValue: 1,
    }, 
  ])

  const express_info = ref([ //物流配送方式和地址信息
    {
      type: 'transExpress',
      title: L('快递设置'),
      is_show: true,
      data_table: [],
      base_info: {},//基础运费信息
    }
  ])

  const handleSaveAllData = ()=> {
    formRef.value.validate().then(async (val) => {
      let values = detail_info.value
      let params = {};
      //保存数据
      params.templateName = values.templateName;
      params.chargeType = values.chargeType;
      //运费信息
      for (let i in express_info.value) {
        let tmp_data = [];
        //基础运费
        let trans_add_fee = values['trans_com_add_fee'];//续费
        let trans_fee = values['trans_com_fee'];//首费
        let trans_add_weight = values['trans_com_add_weight'];//续重
        let trans_weight = values['trans_com_weight'];//首重
        let tmp_base_info = {
          cityCode: 'CN',
          cityName: L('全国'),
          baseNumber: trans_weight,
          basePrice: trans_fee,
          addNumber: trans_add_weight,
          addPrice: trans_add_fee,
        };
        if(route.query.id != undefined){
          tmp_base_info.freightExtendId = detail_info.value['freightExtendId'];;
        }
        tmp_data.push(tmp_base_info);
        //针对特定地区的运费
        for (let j in values.data_table) {
          let tmp_fri_info = {};
          let area_tmp_data = values.data_table[j];
          let city_id_str = area_tmp_data.sele_area_id_array.join(',');
          let city_name_str = area_tmp_data.deliver_areas;
          tmp_fri_info = {
            cityCode: city_id_str,
            cityName: city_name_str,
            baseNumber: area_tmp_data.trans_weight,
            basePrice: area_tmp_data.trans_fee,
            addNumber: area_tmp_data.trans_add_weight,
            addPrice: area_tmp_data.trans_add_fee,
          };
          if(route.query.id != undefined){
            tmp_fri_info.freightExtendId = area_tmp_data.freightExtendId;
          }
          tmp_data.push({...tmp_fri_info});
        }
        params['freightExtendList'] = JSON.stringify(tmp_data);
      }
      //如果有id，则编辑该条数据信息
      let res;
      loading.value = true
      if (route.query.id != undefined && Number(route.query.id) > 0 && route.query.type == 'edit') {
        params.freightTemplateId = route.query.id;
        res = await getSellerExpressFreightUpdateApi(params)
      }else{
        res = await getSellerExpressFreightAddApi(params)
      }
      if(res.state == 200){
        sucTip(res.msg)
        userStore.setDelKeepAlive([route.name,'OrderExpress'])
        setTimeout(() => {
          goBack();
          loading.value = false
        }, 500);
      }else{
        loading.value = false
        failTip(res.msg)
      }
    })
  }

  
  // 返回
  const goBack = () => {
    if (window.history && window.history.length == 1) {
      pageClose();
    } else {
      const { fullPath } = route;
      tabStore.closeTabByKey(fullPath, router);
      router.back();
    }
  };

  // 
  const save_sele_area = (data)=>{
    detail_info.value = data
  }

  
  const getDetail = async()=> {
    let res = await getSellerExpressFreightDetailApi({freightTemplateId:route.query.id})
    if(res.state == 200){
      //渲染基本信息
      let data = res.data;
      for (let i in search_data.value) {
        if(search_data.value[i].name == 'chargeType'){
          detail_info.value[search_data.value[i].name] = data[search_data.value[i].name]*1;
        }else{
          detail_info.value[search_data.value[i].name] = data[search_data.value[i].name];
          if(search_data.value[i].name == 'templateName'&&route.query.type == 'copy'){
            detail_info.value[search_data.value[i].name] = '';
          }
        }
      }
      let tmp_info = data.freightExtendList;
      let s = 0;
      for(let j in express_info.value){
        for(let j_detail in tmp_info) {
          if (tmp_info[j_detail].cityCode == 'CN') {
            detail_info.value['trans_com_fee'] = tmp_info[j_detail].basePrice;
            detail_info.value['trans_com_weight'] = tmp_info[j_detail].baseNumber;
            detail_info.value['trans_com_add_weight'] = tmp_info[j_detail].addNumber;
            detail_info.value['trans_com_add_fee'] = tmp_info[j_detail].addPrice;
            detail_info.value['freightExtendId'] = tmp_info[j_detail].freightExtendId;
          } else {
            detail_info.value.data_table.push({
              key: s,
              deliver_areas: tmp_info[j_detail].cityName,
              trans_weight: tmp_info[j_detail].baseNumber,
              trans_fee: tmp_info[j_detail].basePrice,
              trans_add_weight: tmp_info[j_detail].addNumber,
              trans_add_fee: tmp_info[j_detail].addPrice,
              sele_area_id_array: tmp_info[j_detail].cityCode.split(','),
              freightExtendId: tmp_info[j_detail].freightExtendId,
            });
            s++;
          }
        }
      }
    }
    show_flag.value = true
  }

  onMounted(() => {
    loading.value = false
    if (Number(route.query.id) > 0) {
			getDetail();
		}
  });
  
</script>
<style lang="less">
  .add_transport {
    .add_coupon_height {
      max-height: calc(100vh - @header-height - 20px - 53px);
      overflow: auto;
    }
  }
</style>
