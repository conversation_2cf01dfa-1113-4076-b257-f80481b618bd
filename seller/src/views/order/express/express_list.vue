<template>
  <div class="seckill_label_lists">
    <div>
      <BasicTable @register="standardTable" style="padding: 0">
        <template #tableTitle>
          <div class="toolbar">
            <div class="toolbar_btn" @click="addExpress">
              <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#fff" />
              <span>{{ L('添加物流公司') }}</span>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex == 'expressState'">
            <div>
              <Switch
              @change="(checked) => operate_role({
                bindId: record.bindId,
                expressState: checked ? 1 : 0,
              }, 'switch')"
              :checked="text == 1 ? true : false"
              />
            </div>
          </template>
          <template v-if="column.key == 'action'">
            <TableAction
              class="TableAction"
              :actions="[
                {
                  label: L('电子面单配置'),
                  ifShow: JSON.stringify(record.needFields) != '{}',
                  onClick: operateExterfaceSheet.bind(null, record),
                },
                {
                  label: L('删除'),
                  popConfirm: {
                    title: L('删除后不可恢复，是否确定删除？'),
                    placement: 'left',
                    confirm: operate_role.bind(null, record, 'del'),
                  },
                },
              ]"
            />
          </template>
        </template>
      </BasicTable>
    </div>
    <SldSelGoodsSingleDiy
      @confirm-event="handleConfirm"
      @cancle-event="handleCancle"
      :confirmLoading="confirmLoading"
      link_type="express_company"
      :checkedType="true"
      :modalVisible="visibleModal"
    >
    </SldSelGoodsSingleDiy>
    <SldModal
      :width="500"
      :title="L('电子面单配置')"
      :visible="modalVisible"
      :content="operateData"
      :confirmBtnLoading="confirmBtnLoading_one"
      :showFoot="true"
      @cancle-event="handleCancle"
      @confirm-event="sldHandleConfirm"
    />
  </div>
</template>
<script>
  export default {
    name: 'ExpressList',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, reactive, onMounted } from 'vue';
  import SldModal from '@/components/SldModal/index.vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { Switch } from 'ant-design-vue';
  import { sucTip, failTip } from '@/utils/utils';
  import {
    getSellerExpressListApi,
    getSellerExpressDelApi,
    getSellerExpressOpenCloseApi,
    getSellerExpressAddApi,
    getSellerExpressSheetConfigApi
  } from '/@/api/order/express';
  import SldSelGoodsSingleDiy from '/@/components/SldSelGoodsSingleDiy/index.vue';
  import { getSettingListApi } from '/@/api/common/common';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const visibleModal = ref(false)
  const expressType = ref(2)//快递类型，1-快递鸟，2-快递100
  const confirmLoading = ref(false)
  const confirmBtnLoading_one = ref(false)
  const operateData = ref([]);
  const cur_operate_data = ref({});
  const modalVisible = ref(false);
  const correspondenceData = ref({'partnerId':'customerName','partnerKey':'customerPwd','net':'sendSite','checkMan':'sendStaff'})
  const [standardTable, { reload }] = useTable({
    api: (arg) => getSellerExpressListApi({ ...arg }),
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    columns: [
      {
        title: L('物流名称'),
        dataIndex: 'expressName',
        width: 120,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: L('显示状态'),
        dataIndex: 'expressState',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: L('添加时间'),
        dataIndex: 'createTime',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: L('电子面单'),
        dataIndex: 'isSupportFaceSheet',
        width: 100,
        customRender: ({ text }) => {
          return text ? L('支持') : L('不支持');
        },
      },
    ],
    actionColumn: {
      width: 80,
      title: L('操作'),
      dataIndex: 'action',
    },
    useSearchForm: true,
    formConfig: {
      schemas: [
        {
          field: 'expressName',
          component: 'Input',
          componentProps: {
            minWidth: 300,
            placeholder: L('请输入物流名称'),
            size: 'default',
          },
          label: L('物流名称'),
        },
      ],
    },
    bordered: true,
    striped: false,
    showIndexColumn: true,
    rowKey:'bindId',
  });

  // 弹框取消
  const handleCancle = () => {
    visibleModal.value = false;
    modalVisible.value = false;
    operateData.value = [];
  };

  //电子面单配置
  const sldHandleConfirm = async(val)=> {
    let curParam = {bindId: cur_operate_data.value.bindId};
    if(expressType.value == 1){
      //快递类型，1-快递鸟，2-快递100
      curParam = {...curParam,...val};
    }else{
      for(let i in val){
        if(i == 'code'||i == 'expType'||i == 'monthCode'||i == 'partnerName'||i == 'payType'||i == 'templateId'||i == 'templateSize'){
          curParam[i] = val[i]
        }else if(i == 'partnerId'||i == 'partnerKey'||i == 'net'||i == 'checkMan'){
          curParam[correspondenceData.value[i]] = val[i];
        }
      }
    }
    confirmBtnLoading_one.value = true
    let res = await getSellerExpressSheetConfigApi(curParam)
    if(res.state == 200){
      confirmBtnLoading_one.value = false
      sucTip(res.msg)
      handleCancle()
      reload()
    }else{
      confirmBtnLoading_one.value = false
      failTip(res.msg)
    }
  }

  // 物流公司弹框确定
  const handleConfirm = async(record, ids) => {
    if (ids.length == 0) {
      failTip(L('请选择要添加的物流公司'));
      return;
    }
    confirmLoading.value = true
    let res = await getSellerExpressAddApi({expressIds:ids.join(',')})
    if(res.state == 200){
      sucTip(res.msg)
      visibleModal.value = false
      confirmLoading.value = false
      reload()
    }else{
      confirmLoading.value = false
      failTip(res.msg)
    }
  };

  //添加物流公司
  const addExpress = ()=> {
    visibleModal.value = true;
  }

  const operateExterfaceSheet = (val)=> {
    operateData.value = []
    for (let i in val.needFields) {
      let temp = val[i] ? val[i] : '';
      if(i == 'partnerId'||i == 'partnerKey'||i == 'net'||i == 'checkMan'){
        temp = val[correspondenceData.value[i]] ? val[correspondenceData.value[i]] : '';
      }
      operateData.value.push({
        type: 'input',
        label: val.needFields[i],
        name: i,
        extra: ``,
        placeholder: `${L('请输入')}${val.needFields[i]}`,
        initialValue: temp,
        maxLength: 50,
      });
    }
    if(val.expType!=undefined){
      operateData.value.push({
        type: 'input',
        label: L('产品类型'),
        name: 'expType',
        extra: L('默认为标准快递，可根据实际情况修改'),
        placeholder: L('请输入产品类型'),
        initialValue: val.expType,
        maxLength: 10,
      });
    }
    cur_operate_data.value = val
    modalVisible.value = true
  }

  //商品标签操作
  const operate_role = async (item,type) => {
    let res = {};
    if(type=='del'){
      res = await getSellerExpressDelApi({bindId:item.bindId})
    }else if(type == 'switch'){
      res = await getSellerExpressOpenCloseApi(item)
    }
    if (res.state == 200) {
      sucTip(res.msg);
      reload();
    } else {
      failTip(res.msg);
    }
  };

   //获取系统配置
   const getSetting = async () => {
    try {
      let res = await getSettingListApi({ str: 'express_type' });
      if (res.state == 200) {
        expressType.value = res.data[0].value
      }
    } catch (error) {}
  };

  onMounted(() => {
    getSetting()
  });
</script>
<style lang="less" scoped></style>
