<template>
  <div class="seckill_label_lists">
    <div>
      <BasicTable @register="standardTable" style="padding: 0">
        <template #tableTitle>
          <div class="toolbar">
            <div class="toolbar_btn" @click="transport_to_add(null,'add')">
              <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#fff" />
              <span>{{ L('新增运费模板') }}</span>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.key == 'action'">
            <TableAction
            class="TableAction"
              :actions="[
                {
                  label: L('复制'),
                  onClick: transport_to_add.bind(null, record, 'copy'),
                },
                {
                  label: L('编辑'),
                  onClick: transport_to_add.bind(null, record, 'edit'),
                },
                {
                  label: L('删除'),
                  popConfirm: {
                    title: L('删除后不可恢复，是否确定删除？'),
                    placement: 'left',
                    confirm: operate_role.bind(null, record, 'del'),
                  },
                },
              ]"
            />
          </template>
        </template>
      </BasicTable>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'ReturnAddress',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, reactive, onMounted } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useRouter } from 'vue-router';
  import { sucTip, failTip } from '@/utils/utils';
  import {
    getGoodsFreightTemplateListApi,
    getGoodsFreightTemplateDelApi,
  } from '/@/api/order/express';
  import { useUserStore } from '/@/store/modules/user';


  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;
  const userStore = useUserStore();

  const router = useRouter();

  const [standardTable, { reload }] = useTable({
    api: (arg) => getGoodsFreightTemplateListApi({ ...arg }),
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    columns: [
      {
        title: L('模板名称'),
        dataIndex: 'templateName',
        width: 120,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: L('计费方式'),
        dataIndex: 'chargeTypeValue',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: L('操作时间'),
        dataIndex: 'updateTime',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
    ],
    actionColumn: {
      width: 80,
      title: L('操作'),
      dataIndex: 'action',
    },
    useSearchForm: true,
    formConfig: {
      schemas: [
        {
          field: 'templateName',
          component: 'Input',
          componentProps: {
            minWidth: 300,
            placeholder: L('请输入模板名称'),
            size: 'default',
          },
          label: L('模板名称'),
        },
      ],
    },
    bordered: true,
    striped: false,
    showIndexColumn: true,
    rowKey:'freightTemplateId',
  });

  //商品标签操作
  const operate_role = async (item,type) => {
    let res = {};
    if(type=='del'){
      res = await getGoodsFreightTemplateDelApi({freightTemplateId:item.freightTemplateId})
    }
    if (res.state == 200) {
      sucTip(res.msg);
      reload();
    } else {
      failTip(res.msg);
    }
  };

  const transport_to_add = (record,type)=> {
    if(type=='add'){
      userStore.setDelKeepAlive(['OrderExpressTransportToAdd'])
      router.push({
        path: `/order/express_transport_to_add`,
      });
    }else if(type == 'edit'){
      userStore.setDelKeepAlive(['OrderExpressTransportToEdit'])
      router.push({
        path: `/order/express_transport_to_edit`,
        query:{
          id: record.freightTemplateId,
          info: JSON.stringify(record),
          type:'edit',
        }
      });
    }else if(type == 'copy'){
      userStore.setDelKeepAlive(['OrderExpressTransportToCopy'])
      router.push({
        path: `/order/express_transport_to_copy`,
        query:{
          id: record.freightTemplateId,
          info: JSON.stringify(record),
          type:'copy',
        }
      });
    }
  }
</script>
<style lang="less" scoped></style>
