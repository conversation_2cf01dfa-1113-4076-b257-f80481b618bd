<template>
  <div class="section_padding point_goods_list">
    <div class="section_padding_back">
      <SldComHeader
        :title="L('物流管理')"
      />
      <Tabs type="card" v-model:activeKey="activeKey" class="tabs_nav">
        <TabPane key="1" :tab="L('运费模板')">
          <Transport/>
        </TabPane>
        <TabPane key="2" :tab="L('免运费设置')">
          <ExpressSetting/>
        </TabPane>
        <TabPane key="3" :tab="L('物流公司')">
          <ExpressList/>
        </TabPane>
        
      </Tabs>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'OrderExpress',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, onMounted } from 'vue';
  import { Tabs, TabPane } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import Transport from './transport.vue';
  import ExpressSetting from './setting.vue';
  
  import ExpressList from './express_list.vue';
  import { useUserStore } from '/@/store/modules/user';

  const userStore = useUserStore();
  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const activeKey = ref('1');

  onMounted(() => {
  });
  
</script>
<style lang="less">
  .point_goods_list {
    .tabs_nav {
      .ant-tabs-nav {
        margin-bottom: 0;
      }
    }
  }
</style>
