<template>
  <div class="seckill_label_lists">
    <div>
      <BasicTable @register="standardTable" style="padding: 0">
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex == 'geval_explain'">
            <div class="eva_part">
              <div>{{ L('商品评分：') }}<Rate disabled  v-model:value="record.score"/>{{record.createTime}}</div>
              <div class="text_overflow_hidden" :title="record.content">{{ L('评价内容：') }}{{ record.content }}</div>
              <div v-if="record.imageValue.length > 0">
                <span class="img_name">{{ L('晒单图片：') }}</span>
                <ul class="eval_pic_ul">
                  <li class="eval_pic_li" v-for="(item,index) in record.imageValue" :key="index" @click="() => handlePreview(true,item)">
                    <img :src="item" alt="">
                  </li>
                </ul>
              </div>
              <div class="text_overflow_hidden" :title="record.replyContent
                ? (L('商家回复：') + record.replyContent) : ''">{{ record.replyContent ? (L('商家回复：') + record.replyContent) : '' }}</div>
            </div>
          </template>
          <template v-if="column.key == 'action'">
            <TableAction
            class="TableAction"
              :actions="[
                {
                  label: record.replyContent ? L('修改回复'): L('回复'),
                  onClick: edit.bind(null, record),
                },
              ]"
            />
          </template>
        </template>
      </BasicTable>
    </div>
    <!-- 图片浏览 -->
    <Image v-if="visible" :style="{ display: 'none' }" :preview="{
        visible,
        onVisibleChange: setVisible,
      }" :src="visibleImg"/>
    <SldModal
      :width="500"
      :title="title"
      :visible="modalVisible"
      :content="operateData"
      :confirmBtnLoading="confirmBtnLoading"
      :showFoot="true"
      @cancle-event="handleCancle"
      @confirm-event="handleConfirm"
    />
  </div>
</template>
<script>
  export default {
    name: 'EvaluateGoods',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, reactive, onMounted } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { Rate,Image } from 'ant-design-vue';
  import { sucTip, failTip } from '@/utils/utils';
  import SldModal from '@/components/SldModal/index.vue';
  import {
    getSellerGoodsCommentListApi,
    getSellerGoodsCommentEditReplyApi
  } from '/@/api/order/evaluation';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const visible = ref(false)
  const visibleImg = ref('')
  const curData = ref({})
  const title = ref('')
  const confirmBtnLoading = ref(false)
  const modalVisible = ref(false)
  const operateData = ref([])
  const [standardTable, { reload }] = useTable({
    api: (arg) => getSellerGoodsCommentListApi({ ...arg }),
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    columns: [
      {
        title: L('商品名称'),
        dataIndex: 'goodsName',
        width: 120,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: L('评价描述'),
        dataIndex: 'geval_explain',
        width: 300,
        align:'left'
      },
      {
        title: L('评价人'),
        dataIndex: 'memberName',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: L('评价时间'),
        dataIndex: 'createTime',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
    ],
    actionColumn: {
      width: 80,
      title: L('操作'),
      dataIndex: 'action',
    },
    useSearchForm: true,
    formConfig: {
      schemas: [
        {
          field: 'goodsName',
          component: 'Input',
          componentProps: {
            placeholder: L('请输入商品名称'),
            size: 'default',
          },
          label: L('商品名称'),
        },
        {
          field: 'memberName',
          component: 'Input',
          componentProps: {
            placeholder: L('请输入评价人'),
            size: 'default',
          },
          label: L('评价人'),
        },
        {
      field: '[startTime,endTime]',
      component: 'RangePicker',
      componentProps: {
        format: 'YYYY-MM-DD',
        placeholder: [L('开始时间'), L('结束时间')],
      },
      label: L('评价时间'),
    },
      ],
    },
    beforeFetch(values) {
      values.startTime = values.startTime
        ? values.startTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
      return values;
    },
    bordered: true,
    striped: false,
    showIndexColumn: true,
    rowKey:'commentId',
  });

  // 图片浏览
  const handlePreview = (type,img)=> {
    visibleImg.value = img
    visible.value = type;
  }
  // 图片浏览
  const setVisible = (type)=> {
    visible.value = type;
  }

  const handleCancle = ()=> {
    operateData.value = []
    modalVisible.value = false
  }

  //回复评价
  const edit = (val)=> {
    operateData.value = [
      // @ts-ignore
      {
        type: 'textarea',
        label: L('回复评价'),
        name: 'replyContent',
        placeholder: L('请输入回复内容'),
        extra: L('最多输入200字'),
        maxLength: 200,
        rules: [
          {
            required: true,
            whitespace: true,
            message: L('请输入回复内容'),
          },
        ],
      }
    ]
    operateData.value.forEach(item=>{
      if(val[item.name]!=null){
        item.initialValue = val[item.name];
      }else{
        item.initialValue = ''
      }
    })
    title.value = L('回复评价')
    modalVisible.value = true
    curData.value = val
  }



  //回复弹框点击确定
  const handleConfirm = async (val) => {
    let res = {};
    val.commentId = curData.value.commentId
    res = await getSellerGoodsCommentEditReplyApi(val)
    if (res.state == 200) {
      sucTip(res.msg);
      handleCancle()
      reload();
    } else {
      failTip(res.msg);
    }
  };
</script>
<style lang="less">
@import '../style/order_eval.less';
</style>
