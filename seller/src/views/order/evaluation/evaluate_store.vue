<template>
  <div class="seckill_label_lists">
    <div>
      <BasicTable @register="standardTable" style="padding: 0">
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex == 'deliverSpeed'">
            <div class="eva_part">
              <div>{{ L('商品评分：') }}<Rate disabled  v-model:value="record.description"/></div>
              <div>{{ L('服务态度：') }}<Rate disabled  v-model:value="record.serviceAttitude"/></div>
              <div>{{ L('发货速度：') }}<Rate disabled  v-model:value="record.deliverSpeed"/></div>
            </div>
          </template>
        </template>
      </BasicTable>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'EvaluateStore',
  };
</script>
<script setup>
  import { getCurrentInstance } from 'vue';
  import { BasicTable, useTable, } from '/@/components/Table';
  import { Rate } from 'ant-design-vue';
  import {
    getSellerStoreCommentListApi,
  } from '/@/api/order/evaluation';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const [standardTable, { reload }] = useTable({
    api: (arg) => getSellerStoreCommentListApi({ ...arg }),
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    columns: [
      {
        title: L('订单号'),
        dataIndex: 'orderSn',
        width: 120,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: L('评价人'),
        dataIndex: 'memberName',
        width: 100,
      },
      {
        title: L('评分'),
        dataIndex: 'deliverSpeed',
        width: 150,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: L('评价时间'),
        dataIndex: 'createTime',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
    ],
    useSearchForm: true,
    formConfig: {
      schemas: [
        {
          field: 'memberName',
          component: 'Input',
          componentProps: {
            placeholder: L('请输入评价人'),
            size: 'default',
          },
          label: L('评价人'),
        },
        {
      field: '[startTime,endTime]',
      component: 'RangePicker',
      componentProps: {
        format: 'YYYY-MM-DD',
        placeholder: [L('开始时间'), L('结束时间')],
      },
      label: L('评价时间'),
    },
      ],
    },
    beforeFetch(values) {
      values.startTime = values.startTime
        ? values.startTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
      return values;
    },
    bordered: true,
    striped: false,
    showIndexColumn: true,
    rowKey:'commentId',
  });

 
</script>
<style lang="less">
@import '../style/order_eval.less';
</style>
