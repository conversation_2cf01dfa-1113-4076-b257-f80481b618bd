<template>
  <div class="common_page order_print" style="flex: 1;" id="test">
    <div class="title">{{ L('发货明细单') }}</div>
    <div class="flex_row_start_center base_info">
      <div class="item" v-for="(item,index) in base_data" :key="index">
        <span>{{ item.name }}：</span>
        <span>{{ item.value }}</span>
      </div>
    </div>
    <BasicTable
    class="vben-basic-table-one"
      v-if="goods_data.length>0"
      :canResize="false"
      :columns="columns"
      :bordered="true"
      :ellipsis="false"
      rowKey="productId"
      :pagination="false"
      :dataSource="goods_data"
    >
    </BasicTable>
  </div>
</template>
<script setup>
  import { getCurrentInstance, ref, onMounted,computed,watch } from 'vue';
  import { Tabs, TabPane } from 'ant-design-vue';
  import { BasicTable } from '/@/components/Table';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;


  const props = defineProps({
    data: { type: Object }, //表单数据
  })

   //由于watch无法监听props.modalVisible，所以需要计算属性转为ref对象
   const data = computed(() => {
    return props.data;
  });

  watch(
    data,
    () => {
      if(data.value){
        for (let i in base_data.value) {
          base_data.value[i].value = data.value[base_data.value[i].code];
        }
        goods_data.value = data.value.orderProductListVOList
      }
    }
  );
  
  const goods_data = ref([])
  const base_data = ref([
    {
      code: 'storeName',
      name: L('店铺名称'),
      value: '',
    }, {
      code: 'orderSn',
      name: L('订单编号'),
      value: '',
    }, {
      code: 'receiverName',
      name: L('收货人姓名'),
      value: '',
    }, {
      code: 'receiverMobile',
      name: L('收货人手机号'),
      value: '',
    }, {
      code: 'receiverAddress',
      name: L('收货地址'),
      value: '',
    }, {
      code: 'createTime',
      name: L('下单时间'),
      value: '',
    },
  ])//基本信息数据
  
  const columns = ref([
    {
      title: '',
      dataIndex: 'productId',
      align: 'center',
      width: 40,
      customRender: ({ text, record,index }) => {
        return index+1;
      },
    },
    {
      title: L('商品名称'),
      align: 'center',
      dataIndex: 'goodsName',
      width: 220,
    }, {
      title: L('商品规格'),
      align: 'center',
      dataIndex: 'specValues',
      customRender: ({ text, record,index }) => {
        return text?text:'--';
      },
    },
    {
      title: L('数量'),
      align: 'center',
      dataIndex: 'productNum',
      width: 100,
      customRender: ({ text, record,index }) => {
        return text - (record.returnNumber != undefined ? record.returnNumber : 0);
      },
    }
  ])
</script>
<style lang="less">
  @import './style/print_order_detail.less';
  .vben-basic-table-one{
    height: auto;
  }
</style>
