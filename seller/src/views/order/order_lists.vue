<template>
  <div class="section_padding order_lists_page">
    <div class="section_padding_back section_padding_back_flex">
      <SldComHeader
        :title="L('订单管理')"
        :tipBtn="L('订单导出')"
        tipBtnIcon="iconziyuan23"
        @tip-btn-click="handleSldExcel"
      />
      <Spin :spinning="loading" class="Spin_box">
        <div class="spin_height_order_deliver">
          <BasicForm :tableFlag="true" ref="formRef" submitOnReset @register="registerForm" class="basic-form">
          </BasicForm>
          <div style="margin-bottom: 10px">
            <RadioGroup size="small" v-model:value="filter_code" @change="clickFilter">
              <RadioButton
                :value="item.filter_code"
                v-for="(item, index) in filter_data"
                :key="index"
                >{{ item.filter_name }}</RadioButton
              >
            </RadioGroup>
          </div>
          <!-- dev_supplier-start -->
          <template v-if="userStore.getStoreInfo.shopType&&userStore.getStoreInfo.shopType=='3'">
            <div class="order_list">
              <ul class="header">
                <li class="width_40 pl_100">{{ L('商品信息') }}</li>
                <li class="width_10 center">{{ L('单价(元)') }}</li>
                <li class="width_10 center">{{ L('数量') }}</li>
                
                <li class="width_10 center">{{ L('店铺信息') }}</li>
                <li class="width_10 center">{{ L('实付金额') }}</li>
                <li class="width_10 center">{{ L('订单状态') }}</li>
                <li class="width_10 center">
                  <div class="flex_row_center_center">
                    {{ L('结算状态') }}
                    <Tooltip placement="top">
                      <template #title>
                        <div>{{ L('可结算：用户收货7天后可结算') }}</div>
                      </template>
                      <QuestionCircleOutlined style="margin-left: 4px;cursor: pointer;"/>
                    </Tooltip>
                  </div>
                </li>
                <li class="width_10 center">{{ L('操作') }}</li>
              </ul>
              <div class="order_content">
                <Empty
                  :image="Empty.PRESENTED_IMAGE_SIMPLE"
                  v-if="data.list != undefined && data.list.length == 0"
                />
                <div
                  class="order_content_height" id="order_content_height_id"
                  v-if="data.list != undefined && data.list.length > 0"
                >

                  <div class="item" v-for="(item, index) in data.list" :key="index">
                    <div class="order_info flex_row_between_center">
                      <div class="left flex_row_start_center">
                        <span class="order_sn" style="margin-left:8px">{{ L('订单号：') }}{{ item.orderSn }}</span>
                        <div class="order_type" v-if="item.orderTypeValue">
                          <img :src="order_type_icon[item.orderTypeValue]" alt="" class="order_type_icon">
                          <span class="order_type_text">{{ item.orderTypeValue }}</span>
                        </div>
                        <div class="order_type" style="margin-left: 8px;" v-if="item.isVirtualGoods == 2">
                          <img :src="virtural_goods_order_icon" alt="" class="order_type_icon">
                          <span class="order_type_text">{{ L('虚拟商品订单') }}</span>
                        </div>
                        <div class="order_type" style="margin-left: 8px;" v-if="item.saleModel">
                          <img :src="virtural_goods_order_icon" alt="" style="width:56px;" class="order_type_icon">
                          <span class="order_type_text">{{ item.saleModel==1?'代发订单':'批发订单' }}</span>
                        </div>
                      </div>
                      <div class="flex_row_end_center">
                        <div class="flex_row_start_center star_part">
                          <div class="star add_star" v-if="item.star">
                            <Rate :value="item.star" style="font-size: 18px;margin-right: 5px;"/>
                          </div>
                          <span v-else class="operate_btn add_star">{{ L('加星') }}</span>
                          <div class="flex_row_start_center star">
                            <img src="@/assets/images/order/clear_star.png" alt="" style="width: 18px;height: 18px;margin-right: 5px;margin-top: -1px;cursor: pointer;" @click="addStar(0,item)">
                            <Rate :value="order_detail[item.field]" style="font-size: 18px;margin-right: 5px;" @change="(e) => addStar(e,item)"/>
                          </div>
                        </div>
                        <span class="operate_btn" @click="addOrderRemark(item)">{{ L('备注') }}</span>
                        <div class="order_type" style="margin-left: 8px;" v-if="item.isVirtualGoods == 2">
                          <img :src="virtural_goods_order_icon" alt="" class="order_type_icon">
                          <span class="order_type_text">{{ L('虚拟商品订单') }}</span>
                        </div>
                        <span class="create_time">{{ L('下单时间：') }}{{ item.createTime }}</span>
                        <span class="create_time" v-if="item.canSettleTime" style="margin-left: 15px;">{{ L('可结算时间：') }}{{item.canSettleTime}}</span>
                        <span class="create_time" v-else style="margin-left: 15px;">{{ L('可结算时间：-') }}</span>
                      </div>
                    </div> 
                    <div class="order_goods_part flex_row_start_center">
                      <div class="goods flex_column_start_start width_60" :class="{goods_split:item.orderProductListVOList != undefined && item.orderProductListVOList.length > 1}">
                        <template v-if="item.orderProductListVOList != undefined && item.orderProductListVOList.length > 0">
                          <div class="goods_item flex_row_start_center" style="width: 100%;" v-for="(item_goods,index_goods) in item.orderProductListVOList" :key="index_goods">
                            <div class="flex_row_start_center" :style="{ width: (400 / 600) * 100 + '%' }">
                              <div class="goods_img_wrap flex_row_center_center">
                                <img :src="item_goods.productImage" alt="">
                              </div>
                              <div class="goods_info flex_column_start_start">
                                <span class="goods_name">{{ item_goods.goodsName }}</span>
                                <span class="goods_spec">{{ item_goods.specValues?'规格：'+item_goods.specValues:'' }}</span>
                              </div>
                            </div>
                            <span class="goods_price width_10 center"  :style="{ width: (100 / 600) * 100 + '%' }">￥{{item_goods.productShowPrice}}</span>
                            <span class="buy_num width_10 center"  :style="{ width: (100 / 600) * 100 + '%' }">{{item_goods.productNum}}</span>
                          </div>
                        </template>

                      </div>
                      <div class="member_info flex_column_center_center width_10">
                        <span class="mem_name" v-if="item.storeName">{{ item.storeName }}</span>
                        <span class="mem_name" v-if="item.vendorName">{{ item.vendorName }}</span>
                        <span class="mem_name" v-if="item.vendorMobile">{{ item.vendorMobile }}</span>
                        <span class="mem_name" v-if="!item.storeName&&!item.vendorMobile&&!item.vendorName">--</span>
                      </div>
                      <div class="pay_amount flex_column_center_center width_10 center">
                        <span class="mem_name"
                        >￥{{ item.orderAmount ? Number(item.orderAmount).toFixed(2) : 0 }}</span
                      >
                        <span class="mem_name" v-if="item.isFreeShipping==1" style="color:#666;font-weight: normal;">（免运费）</span>
                        <span class="mem_name" v-if="item.isFreeShipping==0" style="color:#666;font-weight: normal;">（含运费<span style="color:#ff1818;font-weight: bold;">￥{{ item.expressFee ? Number(item.expressFee).toFixed(2) : 0 }}</span>）</span
                        >
                        </div>
                      <div class="order_state width_10 center flex_column_center_center">
                        {{item.orderStateValue}}
                      </div>
                      <div class="order_state width_10 center flex_column_center_center">
                        <div>{{item.settlementStateValue}}</div>
                        <div v-if="item.canSettleTime" style="font-size: 12px;color: #999;">{{ L('可结算时间：') }}{{item.canSettleTime}}</div>
                        <div v-else style="font-size: 12px;color: #999;">{{ L('可结算时间：-') }}</div>
                      </div>
                      <div class="operate width_10 center" style="padding: 15px 5px;">
                        <div class="operate_btn" @click="goDetail(item)">{{ L('查看详情') }}</div>
                        <div class="operate_btn" v-if="item.orderState == 10 && item.orderType == 1&&item.saleModel!=1" @click="changeOrderPrice(item)">{{ L('修改价格') }}</div>
                        <div v-if="item.isShowCancelButton" class="operate_btn" @click="agreeReturn(item)">
                          {{ L('取消订单') }}
                        </div>
                        <div class="operate_btn" v-if="item.isVirtualGoods == 1 && (item.isShowDeliverButton == undefined || (item.isShowDeliverButton != undefined && item.isShowDeliverButton))" @click="printDetail(item)">{{ L('打印发货单') }}</div>
                        <template v-if="(item.lockState == 0 && (item.isShowDeliverButton == undefined || (item.isShowDeliverButton != undefined && item.isShowDeliverButton)))&&item.deliverMethod!=2&&item.deliverMethod!=3">
                          <Popconfirm v-if="item.isVirtualGoods == 2" placement="topRight" :title="L('该订单为虚拟商品订单，确认执行该操作吗?')"
                          @confirm="virturalGoodsOrderDeliverConfirm(item.orderSn)">
                            <div class="operate_btn">
                              <a style="color: #666;" href="javascript:void(0)">{{ L('发货') }}</a>
                            </div>
                          </Popconfirm> 
                        </template>
                        <div
                          v-if="(item.isShowDeliverButton == undefined || (item.isShowDeliverButton != undefined && item.isShowDeliverButton))
                            && ((item.orderState == 20 || item.orderState == 31) && item.lockState == 0)
                            && item.isVirtualGoods == 1 && item.deliverMethod != 2 && item.deliverMethod != 3"
                          class="operate_btn"
                          @click="agreeDeliver(item,null)"
                        >{{ L('发货') }}</div>
                      </div>
                    </div>
                    <div class="flex_row_start_center remark" v-if="item.storeRemark">
                      <span class="remark_tip">{{ L('商家备注：') }}</span>
                      <span class="remark_con">{{ item.storeRemark }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="pagination">
                <Pagination
                  v-if="
                    data.list != undefined && data.list.length > 0 && data.pagination != undefined
                  "
                  v-model:current="data.pagination.current"
                  size="small"
                  show-quick-jumper
                  show-less-items
                  show-size-changer
                  :show-total="(total) => `${L('共')} ${total} ${L('条数据')}`"
                  :total="data.pagination.total"
                  :defaultPageSize="PAGE_SIZE"
                  :pageSizeOptions="PAGE_SIZE_OPTIONS"
                  @change="onPageChange"
                />
              </div>
            </div>
          </template>
          <!-- dev_supplier-end -->
          <!-- dev_supplier-start -->
          <template v-if="!userStore.getStoreInfo.shopType||(userStore.getStoreInfo.shopType&&userStore.getStoreInfo.shopType!='3')">
          <!-- dev_supplier-end -->
            <div class="order_list">
              <ul class="header">
                <li class="width_40 pl_100">{{ L('商品信息') }}</li>
                <li class="width_8 center">{{ L('单价(元)') }}</li>
                <li class="width_8 center">{{ L('数量') }}</li>
                <li class="width_8 center">{{ L('商品来源') }}</li>
                <li class="width_10 center">{{ L('用户昵称') }}</li>
                <li class="width_10 center">{{ L('手机号') }}</li>
                <li class="width_8 center">{{ L('实付金额') }}</li>
                <li class="width_8 center">{{ L('订单状态') }}</li>
                <li class="width_8 center">
                  <div class="flex_row_center_center">
                    {{ L('结算状态') }}
                    <Tooltip placement="top">
                      <template #title>
                        <div>{{ L('可结算：用户收货+7天售后期') }}</div>
                        <div>{{ L('用户确认售后7天后可结算') }}</div>
                      </template>
                      <QuestionCircleOutlined style="margin-left: 4px;cursor: pointer;"/>
                    </Tooltip>
                  </div>
                </li>
                <li class="width_10 center">{{ L('操作') }}</li>
              </ul>
              <div class="order_content">
                <Empty
                  :image="Empty.PRESENTED_IMAGE_SIMPLE"
                  v-if="data.list != undefined && data.list.length == 0"
                />
                <div
                  class="order_content_height" id="order_content_height_id"
                  v-if="data.list != undefined && data.list.length > 0"
                >

                  <div class="item" v-for="(item, index) in data.list" :key="index">
                    <div class="order_info flex_row_between_center">
                      <div class="left flex_row_start_start">
                        <span class="order_sn" style="margin-left:8px">{{ L('订单号：') }}{{ item.orderSn }}</span>
                        <div class="order_type" v-if="item.orderTypeValue">
                          <img :src="order_type_icon[item.orderTypeValue]" alt="" class="order_type_icon">
                          <span class="order_type_text">{{ item.orderTypeValue }}</span>
                        </div>
                        <div class="order_type" style="margin-left: 8px;" v-if="item.isVirtualGoods == 2">
                          <img :src="virtural_goods_order_icon" alt="" class="order_type_icon">
                          <span class="order_type_text">{{ L('虚拟商品订单') }}</span>
                        </div>
                      </div>
                      <div class="flex_row_end_center">
                        <div class="flex_row_start_center star_part">
                          <div class="star add_star" v-if="item.star">
                            <Rate :value="item.star" style="font-size: 18px;margin-right: 5px;"/>
                          </div>
                          <span v-else class="operate_btn add_star">{{ L('加星') }}</span>
                          <div class="flex_row_start_center star">
                            <img src="@/assets/images/order/clear_star.png" alt="" style="width: 18px;height: 18px;margin-right: 5px;margin-top: -1px;cursor: pointer;" @click="addStar(0,item)">
                            <Rate :value="order_detail[item.field]" style="font-size: 18px;margin-right: 5px;" @change="(e) => addStar(e,item)"/>
                          </div>
                        </div>
                        <span class="operate_btn" @click="addOrderRemark(item)">{{ L('备注') }}</span>
                        
                        <span class="create_time" style="margin-right: 10px;" v-if="item.deliverMethod==1">快递发货</span>
                        
                        <span class="create_time">{{ L('下单时间：') }}{{ item.createTime }}</span>
                        <span class="create_time" v-if="item.canSettleTime" style="margin-left: 15px;">{{ L('可结算时间：') }}{{item.canSettleTime}}</span>
                        <span class="create_time" v-else style="margin-left: 15px;">{{ L('可结算时间：-') }}</span>
                      </div>
                    </div> 
                    <div class="order_goods_part flex_row_start_center">
                      <div class="goods flex_column_start_start width_64" :class="{goods_split:item.orderProductListVOList != undefined && item.orderProductListVOList.length > 1}">
                        <template v-if="item.orderProductListVOList != undefined && item.orderProductListVOList.length > 0">
                          <div class="goods_item flex_row_start_center" style="width: 100%;" v-for="(item_goods,index_goods) in item.orderProductListVOList" :key="index_goods">
                            <div class="flex_row_start_center" style="width: 63%;">
                              <div class="goods_img_wrap flex_row_center_center">
                                <img :src="item_goods.productImage" alt="">
                              </div>
                              <div class="goods_info flex_column_start_start">
                                <span class="goods_name">{{ item_goods.goodsName }}</span>
                                <span class="goods_spec">{{ item_goods.specValues }}</span>
                              </div>
                            </div>
                            <span class="goods_price width_10 center" style="width: 13%;">￥{{item_goods.productShowPrice}}</span>
                            <span class="buy_num width_10 center" style="width: 12%;">{{item_goods.productNum}}</span>
                            <span class="buy_num width_10 center" style="width: 12%;">{{item_goods.supplierName}}</span>
                          </div>
                        </template>

                      </div>
                      <div class="member_info flex_column_center_center width_10">
                        <span class="mem_name">{{item?.memberNickName||item?.memberName}}</span>
                      </div>
                        <div class="member_info flex_column_center_center width_10">
                        <span class="mem_name">{{item.memberMobile}}</span>
                      </div>
                      <div class="pay_amount width_8 center">￥{{item.orderAmount}}</div>
                      <div class="order_state width_8 center flex_column_center_center">
                        {{item.orderStateValue}}
                      </div>
                      <div class="order_state width_8 center flex_column_center_center">
                        {{item.settlementStateValue}}
                      </div>
                      <div class="operate width_10 center" style="padding: 15px 5px;">
                        
                        
                        <div class="operate_btn" @click="goDetail(item)">{{ L('查看详情') }}</div>
                        <template v-if="(item.orderState == 20 && item.lockState == 0 && (item.isShowDeliverButton == undefined || (item.isShowDeliverButton != undefined && item.isShowDeliverButton)))&&item.deliverMethod!=2&&item.deliverMethod!=3">
                          <Popconfirm v-if="item.isVirtualGoods == 2" placement="topRight" :title="L('该订单为虚拟商品订单，确认执行该操作吗?')"
                          @confirm="virturalGoodsOrderDeliverConfirm(item.orderSn)">
                            <div class="operate_btn">
                              <a style="color:#ed803f;font-weight: bold;" href="javascript:void(0)">{{ L('发货') }}</a>
                            </div>
                          </Popconfirm> 
                        </template>
                        <!-- 锁定说明需要退款 -->
                        <div  style="color:red" v-if="item.orderState == 20 && item.lockState == 1" class="operate_btn" @click="goServiceDetail(item.orderSn)">处理退款</div>
                        <div class="operate_btn" v-if="item.orderState == 10 && item.orderType == 1" @click="changeOrderPrice(item)">{{ L('修改价格') }}</div>
                        <div v-if="(item.orderState == 10 || item.orderState == 20) && item.isShowCancelButton && item.riderState!=10 && item.riderState!=20 && item.riderState!=30 && item.riderState!=40 && item.riderState!=50" class="operate_btn" @click="agreeReturn(item)">
                          {{ L('主动退款') }}
                        </div>
                        <div class="operate_btn" v-if="item.isVirtualGoods == 1 && item.orderState == 20 && (item.isShowDeliverButton == undefined || (item.isShowDeliverButton != undefined && item.isShowDeliverButton))" @click="printDetail(item)">{{ L('打印发货单') }}</div>
                
                        <div
                          v-if="(item.isShowDeliverButton == undefined || (item.isShowDeliverButton != undefined && item.isShowDeliverButton))
                            && ((item.orderState == 20 || item.orderState == 31) && item.lockState == 0)
                            && item.isVirtualGoods == 1 && item.deliverMethod != 2 && item.deliverMethod != 3"
                          class="operate_btn"
                          @click="agreeDeliver(item,null)"
                        >{{ L('发货') }}</div>
                      </div>
                    </div>
                    <div class="flex_row_start_center remark" v-if="item.storeRemark">
                      <span class="remark_tip">{{ L('商家备注：') }}</span>
                      <span class="remark_con">{{ item.storeRemark }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="pagination">
                <Pagination
                  v-if="
                    data.list != undefined && data.list.length > 0 && data.pagination != undefined
                  "
                  v-model:current="data.pagination.current"
                  size="small"
                  show-quick-jumper
                  show-less-items
                  show-size-changer
                  :show-total="(total) => `${L('共')} ${total} ${L('条数据')}`"
                  :total="data.pagination.total"
                  :defaultPageSize="PAGE_SIZE"
                  :pageSizeOptions="PAGE_SIZE_OPTIONS"
                  @change="onPageChange"
                />
              </div>
            </div>
            <!-- dev_supplier-start -->
            </template>
            <!-- dev_supplier-end -->
        </div>
      </Spin>
      <!-- 打印发货单-start -->
      <div style="display: none;">
        <div id="send_detail">
          <PrintOrderDetail :data="print_data"/>
        </div>
      </div>
      <!-- 打印发货单-end -->
    </div>
    <!-- 商品发货-start -->
    <Modal title="商品发货" :width="modal_width" :visible="deliverModal" @ok="deliverConfirm" @cancel="sldDeliverHandleCancle" :okText="deliverType == 3 ? '打印并发货' : '确定'" :confirmLoading="confirmLoading">
      <div v-if="deliverType==1" class="sld_modal_top_tip_box">
        <div class="flex_row_start_center sld_modal_top_tip">
          <div :style="{ lineHeight: '0px' }">
            <AliSvgIcon iconName="icontishi3" width="15px" height="15px" fillColor="#333333" />
          </div>
          <span :style="{ fontSize: '13px', marginLeft: '6px', color: 'rgba(0,0,0,.65)' }">{{ L('请认真填写物流公司及快递单号') }}</span>
        </div>
      </div>
      <Form layout="horizontal" ref="formRefs" class="form_detail" :model="order_detail" >
        <div v-if="deliverType==1" style="width: 100%;height:32px;"></div>
        <BasicTable 
          :columns="columnDeliverData"
          :bordered="true"
          :ellipsis="false"
          :pagination="false"
          :clickToRowSelect="false"
          :canResize="false"
          :rowSelection="{ 
            type: 'checkbox', 
            selectedRowKeys: selectedRowKeysTable,
            onSelect: onSelect,
            onSelectAll: onSelectAll,
          }"
          rowKey="orderProductId"
          :dataSource="deliver_goods_table"
          :maxHeight="400">
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.dataIndex == 'deliverNum'">
              <InputNumber v-model:value="record.deliverNum" :min="selectedRowKeysTable.indexOf(record.orderProductId) > -1 ? 1 : 0" :max="record.productNum - record.shippedNum" :precision="0"></InputNumber>
            </template>
          </template>
        </BasicTable>
        <Form.Item :label="L('收货人姓名')" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}">
          <span>{{order_detail.receiverName}}</span>
        </Form.Item>
        <Form.Item :label="L('收货人电话')" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}">
          <span>{{order_detail.receiverMobile}}</span>
        </Form.Item>
        <Form.Item :label="L('收货人地址')" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}">
          <span style="word-break: break-all;">{{order_detail.receiverAreaInfo}}{{order_detail.receiverAddress}}</span>
        </Form.Item>
        <Form.Item :label="L('发货方式')" name="deliverType" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}" :rules="[{required: true,message:L('请选择发货方式')}]">
          <RadioGroup
            size="small"
            v-model:value="order_detail.deliverType"
            @change="redioOnChange($event)"
          >
            <Radio :value="1" > {{ L('物流发货') }} </Radio>
            <Radio :value="3"> {{ L('电子面单') }} </Radio>
            <Radio :value="2"> {{ L('无需物流') }} </Radio>
          </RadioGroup>
        </Form.Item>
        <Form.Item :label="L('物流公司')" name="expressId" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}" :rules="[{required: true,message:L('请选择物流公司')}]" v-if="deliverType == 1">
          <ExpressSelect
            v-model:value="order_detail.expressId"
            :expressList="expressList"
            @change="handleSelExpress"
            @refresh="get_express_list"
          />
        </Form.Item>
        <Form.Item :label="L('物流公司')" name="expressId" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}" :rules="[{required: true,message:L('请选择物流公司')}]" v-if="deliverType == 3">
          <ExpressSelect
            v-model:value="order_detail.expressId"
            :expressList="email_expressList"
            @change="handleSelExpress"
            @refresh="get_express_list"
          />
        </Form.Item>
        <Form.Item :label="L('快递单号')" name="expressNumber" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}" :rules="[{required: true,message:L('请输入物流单号')},rule]" v-if="deliverType == 1" :extra="L('请输入正确的物流单号')">
          <Input :maxLength="20" :placeholder="L('请输入物流单号')" v-model:value="order_detail.expressNumber"/>
        </Form.Item>
        <Form.Item :label="L('联系人')" name="deliverName" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}" :rules="[{required: true,message:L('请输入联系人')}]" v-if="deliverType == 2">
          <Input :maxLength="10" :placeholder="L('请输入联系人')" v-model:value="order_detail.deliverName"/>
        </Form.Item>
        <Form.Item :label="L('联系方式')" name="deliverMobile" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}" :rules="[{required: true,message:L('请输入联系方式')},{pattern: mobile_reg,message:L('请输入正确的手机号')}]" v-if="deliverType == 2">
          <Input :maxLength="11" :placeholder="L('请输入联系方式')" v-model:value="order_detail.deliverMobile"/>
        </Form.Item>
      </Form>
    </Modal>
    <!-- 商品发货-end -->
    <SldModal
      :width="width"
      :title="title"
      :visible="modalVisible"
      :height="600"
      :content="operateData"
      :showFoot="showFoot"
      :confirmBtnLoading="modalConfirmBtnLoading"
      @cancle-event="handleModalCancle"
      @confirm-event="sldConfirm"
      :showTopTip="showTopTip"
    />
    <!-- 电子面单批量发货-start -->
    <CreateExterFaceSheet @cancle-event="operateExterFaceSheetOptShowFlag" v-if="exterFaceSheetOptType=='create'" :exterFaceSheetOptShowFlag="exterFaceSheetOptShowFlag" :orderData="selectedRowKeys"
      :expressList="expressList"/>
      <!-- 电子面单批量发货-end -->
  </div>
</template>
<script>
  export default {
    name: 'OrderOrderLists',
  };
</script>
<script setup>
  import { failTip, sucTip } from '@/utils/utils';
import { Empty, Form, Input, InputNumber, Modal, Pagination, Popconfirm, Radio, RadioButton, RadioGroup, Rate, Spin, Tooltip } from 'ant-design-vue';
import { getCurrentInstance, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import CreateExterFaceSheet from './create_exter_face_sheet.vue';
import PrintOrderDetail from './print_order_detail.vue';
import {
getOrderInfoCancelApi,
getOrderInfoDeliverApi,
getOrderInfoExportApi,
getOrderInfoExpressListApi,
getOrderInfoMergeDeliverApi,
getOrderInfoOrderProductListApi,
getOrderListApi,
getOrderRemarkApi,
getOrderStarApi,
getOrderUpdatePriceApi,
getSettingReasonListApi,
} from '/@/api/order/order';
import ExpressSelect from '/@/components/ExpressSelect/index.vue';
import { BasicForm, useForm } from '/@/components/Form/index';
import SldComHeader from '/@/components/SldComHeader/index.vue';
import SldModal from '/@/components/SldModal/index.vue';
import { BasicTable } from '/@/components/Table';
import { PAGE_SIZE, PAGE_SIZE_OPTIONS } from '/@/components/Table/src/const.ts';
import { useUserStore } from '/@/store/modules/user';
import { SelectAll, list_com_page_more, selectRadio, sldPrint } from '/@/utils/utils';
import virtural_goods_order_icon from '/src/assets/images/order/virtural_goods_order_icon.png';
import { QuestionCircleOutlined } from '@ant-design/icons-vue';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;


  const router = useRouter();
  const loading = ref(false)
  const userStore = useUserStore();
  const exterFaceSheetOptType = ref('create')//批量生成、打印电子面单类型， create 生成  print 打印
  const exterFaceSheetOptShowFlag = ref(false)//批量生成、打印电子面单弹框是否显示
  const pageSize = ref(PAGE_SIZE);
  const rule = ref({pattern: /^[0-9a-zA-Z]*$/g,message:L('请输入正确的单号')})
  const mobile_reg = ref(/(1[3-9]\d{9}$)/)
  const params = ref({ pageSize: pageSize.value });
  const formValues = ref({}); //搜索条件
  const data = ref({});
  const selectedRowKeys = ref([]);
  const selectedRowKeysTable = ref([]);
  const selectedRowsTable = ref([]);
  const selectedRows = ref([]);
  const print_data = ref({})//发货明细单数据
  const order_type_icon = ref({ // 多语言暂不处理
    '秒杀': new URL('/@/assets/images/order/seckill_order_icon.png', import.meta.url).href,
    '拼团': new URL('/@/assets/images/order/spell_group_order_icon.png', import.meta.url).href,
    '定金预售': new URL('/@/assets/images/order/deposit_presale_order_icon.png', import.meta.url).href,
    '全款预售': new URL('/@/assets/images/order/full_presale_order_icon.png', import.meta.url).href,
    '阶梯团': new URL('/@/assets/images/order/ladder_grooup_order_icon.png', import.meta.url).href,
  });//订单类型图标
  const modal_width = ref(700);
  const show_foot = ref(true);
  const propType = ref('');
  const operateType = ref('')
  const formRefs = ref();
  const allowPrintOrderSnArray = ref([]);//允许打印电子面单的订单号数组
  const resList = ref([]); // 取消原因数据
  const deliverModal = ref(false);
  const expressList = ref([]); //快递公司数据
  const deliverType = ref(1); //发货方式
  const deliver_goods_table = ref([]);
  const email_expressList = ref([]);//电子面单物流公司列表
  const modalVisible = ref(false);
  const btnLoading = ref(false);
  const showFoot = ref(true);
  const width = ref(500)
  const showTopTip = ref('')
  const modalConfirmBtnLoading = ref(false);
  const title = ref('')
  const type_info = ref('')
  const operateData = ref([]); //弹框操作数据
  const confirmLoading = ref(false);//发货弹窗确认按钮loading
  const filter_code = ref(''); //过滤器默认值
  const filter_data = ref([
    { filter_code: '', filter_name: L('全部订单') },
    { filter_code: '10', filter_name: L('待付款订单') },
    { filter_code: '20', filter_name: L('待发货订单') },
    { filter_code: '31', filter_name: L('部分发货订单') },
    { filter_code: '30', filter_name: L('待收货订单') },
    { filter_code: '40', filter_name: L('已完成订单') },
    { filter_code: '0', filter_name: L('已取消订单') },
    { filter_code: '50', filter_name: L('交易关闭订单') },
  ]);

  const columnDeliverData = ref([
    {
      title: L('规格型号'),
      dataIndex: 'specValues',
      align: 'center',
      width: 100,
      customRender: ({ text }) => {
        return text ? text : L('默认')
      },
    },
    {
      title: L('产品名称'),
      dataIndex: 'goodsName',
      align: 'center',
      width: 100,
    },
    {
      title: L('下单数量'),
      dataIndex: 'productNum',
      align: 'center',
      width: 100,
    },
    {
      title: L('已发数量'),
      dataIndex: 'shippedNum',
      align: 'center',
      width: 100,
    },
    {
      title: L('本次发货数量'),
      dataIndex: 'deliverNum',
      align: 'center',
      width: 100,
    },
  ])

  const order_detail_info = ref({})
  const order_detail = ref({})

    //前往售后列表页
  const goServiceDetail = (orderSn) => {
    router.push(`/order/service?tab=1&orderSn=${orderSn}`);
  };

  const schemas_info = ref([
      {
        field: 'orderSn',
        component: 'Input',
        componentProps: {
          minWidth: 300,
          placeholder: L('请输入订单号'),
          size: 'default',
        },
        label: L('订单号'),
        labelWidth: 70,
      },
      {
        field: 'memberName',
        component: 'Input',
        componentProps: {
          minWidth: 300,
          placeholder: L('请输入会员名称'),
          size: 'default',
        },
        label: L('会员名称'),
        labelWidth: 70,
      },
      {
        field: 'memberMobile',
        component: 'Input',
        componentProps: {
          minWidth: 300,
          placeholder: L('请输入会员手机号'),
          size: 'default',
        },
        label: L('会员手机号'),
        labelWidth: 70,
      },
      {
        field: 'goodsName',
        component: 'Input',
        componentProps: {
          minWidth: 300,
          placeholder: L('请输入商品名称'),
          size: 'default',
        },
        label: L('商品名称'),
        labelWidth: 70,
      },
      {
        component: 'RangePicker',
        label: L('下单时间'),
        field: 'fieldTime',
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: [L('开始时间'), L('结束时间')],
        },
      },
      
      {
        component: 'Select',
        label: L('订单星级'),
        field: 'star',
        componentProps: {
          placeholder: L('请选择订单星级'),
          options: [
            { value: '', label: L('全部') },
            { value: '5', label: L('五星') },
            { value: '4', label: L('四星') },
            { value: '3', label: L('三星') },
            { value: '2', label: L('二星') },
            { value: '1', label: L('一星') },
          ],
        },
      },
      {
        component: 'Select',
        label: L('订单类型'),
        field: 'isVirtualGoods',
        componentProps: {
          placeholder: L('请选择订单类型'),
          options: [
            { value: '', label: L('全部') },
            { value: '1', label: L('实物商品订单') },
            { value: '2', label: L('虚拟商品订单') },
          ],
        },
      },
      {
        field: 'tradeSn',
        component: 'Input',
        colProps: { span: 6, style: 'width:250px;max-width:100%;flex:none' },
        componentProps: {
          minWidth: 300,
          placeholder: '请输入支付流水号',
          size: 'default',
        },
        label: '支付流水号',
        labelWidth: 75,
      },
    ])

  // dev_supplier-start
  if(userStore.getStoreInfo.shopType&&userStore.getStoreInfo.shopType=='3'){
    schemas_info.value = [
      {
        field: 'orderSn',
        component: 'Input',
        componentProps: {
          minWidth: 300,
          placeholder: L('请输入订单号'),
          size: 'default',
        },
        label: L('订单号'),
        labelWidth: 70,
      },
      {
        field: 'goodsName',
        component: 'Input',
        componentProps: {
          minWidth: 300,
          placeholder: L('请输入商品名称'),
          size: 'default',
        },
        label: L('商品名称'),
        labelWidth: 70,
      },
      {
        field: 'storeName',
        component: 'Input',
        componentProps: {
          minWidth: 300,
          placeholder: L('请输入店铺名称'),
          size: 'default',
        },
        label: L('店铺名称'),
        labelWidth: 70,
      },
      {
        field: 'memberName',
        component: 'Input',
        componentProps: {
          minWidth: 300,
          placeholder: L('请输入店铺账号'),
          size: 'default',
        },
        label: L('店铺账号'),
        labelWidth: 70,
      },
      {
        field: 'userMobile',
        component: 'Input',
        componentProps: {
          minWidth: 300,
          placeholder: L('请输入店铺手机号'),
          size: 'default',
        },
        label: L('店铺手机号'),
        labelWidth: 70,
      },
      {
        component: 'Select',
        label: L('订单类型'),
        field: 'isVirtualGoods',
        componentProps: {
          placeholder: L('请选择订单类型'),
          options: [
            { value: '', label: L('全部') },
            { value: '1', label: L('实物商品订单') },
            { value: '2', label: L('虚拟商品订单') },
          ],
        },
      },
      {
        component: 'Select',
        label: L('销售模式'),
        field: 'saleModel',
        componentProps: {
          placeholder: L('请选择销售模式'),
          options: [
            { value: '', label: L('全部') },
            { value: '1', label: L('代发订单') },
            { value: '2', label: L('批发订单') },
          ],
        },
      },
      {
        component: 'Select',
        label: L('订单星级'),
        field: 'star',
        componentProps: {
          placeholder: L('请选择订单星级'),
          options: [
            { value: '', label: L('全部') },
            { value: '5', label: L('五星') },
            { value: '4', label: L('四星') },
            { value: '3', label: L('三星') },
            { value: '2', label: L('二星') },
            { value: '1', label: L('一星') },
          ],
        },
      },
      {
        component: 'RangePicker',
        label: L('下单时间'),
        field: 'fieldTime',
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: [L('开始时间'), L('结束时间')],
        },
      },
      {
        field: 'tradeSn',
        component: 'Input',
        colProps: { span: 6, style: 'width:250px;max-width:100%;flex:none' },
        componentProps: {
          minWidth: 300,
          placeholder: '请输入支付流水号',
          size: 'default',
        },
        label: '支付流水号',
        labelWidth: 75,
      },
    ]
  }
  // dev_supplier-end

  //表单
  const [registerForm, { validate, getFieldsValue }] = useForm({
    schemas: schemas_info.value,
    fieldMapToTime: [['fieldTime', ['startTime', 'endTime'], 'YYYY-MM-DD']],
    labelWidth: 80,
    baseColProps: { span: 6 },
    actionColOptions: { span: 24 },
    autoSubmitOnEnter: true,
    showAdvancedButton: true,
    submitFunc: search,
    resetFunc: reset,
  });

  // 搜索重置
  async function reset() {
    filter_code.value = ''
    params.value.current = 1;
  }

  // 搜索确定
  async function search() {
    await validate();
    let values = getFieldsValue();
    values.startTime = values.startTime ? values.startTime.split(' ')[0] + ' 00:00:00' : undefined;
    values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
    for (let i in values) {
      if (values[i] == undefined) {
        delete values[i];
      }
    }
    formValues.value = values;
    let curParams = { ...params.value, ...formValues.value };
    if (filter_code.value) {
      curParams.orderState = filter_code.value;
    }
    get_list(curParams);
  }

  // 单选多选事件
  function onSelect(record, selected) {
    let rows = selectRadio(
      selectedRowKeysTable.value,
      selectedRowsTable.value,
      'orderProductId',
      record,
      selected,
    );
    selectedRowKeysTable.value = rows.selectedRowKeys;
    selectedRowsTable.value = rows.selectedRows;
    deliver_goods_table.value.forEach(item=>{
      let index =  selectedRowKeysTable.value.indexOf(item.orderProductId)
      if(index!=-1){
        item.deliverNum = item.deliverNum<1 ? 1 :item.deliverNum
      }
    })
  }

  // 全选按钮事件
  function onSelectAll(selected, rows, changeRows) {
    let rowAll = SelectAll(
      selectedRowKeysTable.value,
      selectedRowsTable.value,
      'orderProductId',
      selected,
      rows,
      changeRows,
    );
    selectedRowKeysTable.value = rowAll.selectedRowKeys;
    selectedRowsTable.value = rowAll.selectedRows;
    deliver_goods_table.value.forEach(item=>{
      let index =  selectedRowKeysTable.value.indexOf(item.orderProductId)
      if(index!=-1){
        item.deliverNum = item.deliverNum<1 ? 1 :item.deliverNum
      }
    })
  }
  
  //改变页码
  const onPageChange = (page, pageSize) => {
    let curParams = {...formValues.value };
    if (filter_code.value) {
      curParams.orderState = filter_code.value;
    }
    formValues.value = curParams
    params.value = { pageSize: pageSize, current: page};
    get_list({...params.value,...curParams});
  };

  //获取数据列表
  const get_list = async (param) => {
    loading.value = true;
    let res = await getOrderListApi({ ...param});
    if (res.state == 200) {
      loading.value = false;
      if (param.current > 1 && res.data.list.length == 0 && params.value.current > 1) {
        param.current = param.current - 1;
        get_list(params);
      } else {
        data.value = res.data;
      }
    }
  };

  //获取店铺启用的物流公司
  const get_express_list = async()=> {
    let res = await getOrderInfoExpressListApi({pageSize: list_com_page_more, expressState: 1 })
    if(res.state == 200){
      expressList.value = JSON.parse(JSON.stringify(res.data.list));
      email_expressList.value = JSON.parse(JSON.stringify(res.data.list.filter(item => item.isSupportFaceSheet == 1)));
    }
  }
  //获取违规下架由
  const get_reason_list = async()=> {
    let res = await getSettingReasonListApi({ type: 108, pageSize: list_com_page_more })
    if(res.state == 200){
      resList.value = res.data
    }
  }

  // 打印明细单
  const printDetail = (val)=> {
    print_data.value = {}
    let val_data = JSON.parse(JSON.stringify(val));
    if (val_data.orderProductListVOList && val_data.orderProductListVOList.length) {
      val_data.orderProductListVOList = val_data.orderProductListVOList.filter(item =>
        item.goodsSource != 2 && (item.productNum - (item.returnNumber != undefined ? item.returnNumber : 0) > 0));
    }
    print_data.value = val_data
    setTimeout(()=>{
      sldPrint('send_detail', 'html');
    })
  }

  const redioOnChange = (e)=> {
    deliverType.value = e.target.value
    order_detail.value.expressId = undefined
  }

  const handleSelExpress = (val)=> {
    let obj = JSON.parse(JSON.stringify(order_detail_info.value));
    if (val != obj.expressId) {
      order_detail.value.expressNumber = '';
    } else {
      order_detail.value.expressNumber = obj.expressNumber;
    }
  }

  // 发货弹窗
  const agreeDeliver = async(item,type)=>{
    if(type == 'select'){
      if(selectedRowKeys.value.length<2){
        failTip(L('请至少选择2个订单'))
        return
      }
      item = selectedRows.value[0]
    }
    type_info.value = type
    // modalItem
    modal_width.value = 900
    console.log(item)
    order_detail_info.value = JSON.parse(JSON.stringify(item))
    let obj = JSON.parse(JSON.stringify(item))
    obj.expressId = null
    obj.expressName = null
    obj.expressNumber = null
    order_detail.value = obj
    order_detail.value.deliverType = deliverType.value
    let res = await getOrderInfoOrderProductListApi({orderSns:type == 'select'?selectedRowKeys.value.join(','):item.orderSn})
    if(res.state == 200){
      deliver_goods_table.value = JSON.parse(JSON.stringify(res.data));
      deliverModal.value = true
    }else{
      failTip(res.msg)
    }
  }

   // 弹窗
   const agreeReturn = async(item)=> {
    operateData.value = []
    modalConfirmBtnLoading.value = false
    showTopTip.value = L('取消订单后，订单将自动关闭')
    // @ts-ignore
    operateData.value.push({
      type: 'select',
      label: L('取消理由'),
      name: 'reasonId',
      placeholder: L('请选择取消理由'),
      selData: resList.value,
      selKey:'reasonId',
      selName:'content',
      diy:true,
      rules: [
        {
          required: true,
          message: L('请选择取消理由'),
        },
      ]
    })
    operateData.value.push({
      type: 'textarea',
      label: L('取消备注'),
      name: 'remark',
      placeholder: L('请输入取消备注，最多50个字'),
      maxLength: 50,
      initialValue: '',
    })
    title.value = L('取消订单')
    width.value = 600
    showFoot.value = true
    modalVisible.value = true
    order_detail.value = item
    operateType.value = 'cancleOrder'
  }

  //修改价格
  const changeOrderPrice = (item)=> {
    operateData.value = []
    modalConfirmBtnLoading.value = false
    showTopTip.value = L('只有订单未付款时才支持改价')
    // @ts-ignore
    operateData.value.push({
      type: 'inputnum',
      label: L('订单价格(¥)'),
      name: 'moneyAmount',
      extra: L('不含运费'),
      min: 0.01,
      max: 9999999.99,
      precision: 2,
      initialValue: item.moneyAmount,
      rules: [
        {
          required: true,
          message: L('订单价格不可为空'),
        },
      ],
    },
    {
      type: 'inputnum',
      label: L('运费(¥)'),
      name: 'expressFee',
      min: 0,
      max: 9999999.99,
      precision: 2,
      initialValue: item.expressFee,
      avoid:L('免运费'),
      avoid_width:'80px'
    })
    title.value = L('修改价格')
    width.value = 400
    showFoot.value = true
    modalVisible.value = true
    operateType.value = 'modifyPrice'
    order_detail.value = item
  }

  //备注事件 
  const addOrderRemark = (item)=> {
    operateData.value = []
    modalConfirmBtnLoading.value = false
    showTopTip.value = ''
    // @ts-ignore
    operateData.value.push({
      type: 'textarea',
      label: L('订单备注'),
      name: 'remark',
      placeholder: L('请输入订单备注信息'),
      extra: L('最多输入100字'),
      maxLength: 100,
      initialValue: item.storeRemark!=null&&item.storeRemark!='' ? item.storeRemark : '',
    })
    width.value = 500
    title.value = L('商家订单备注')
    showFoot.value = true
    modalVisible.value = true
    operateType.value = 'remark'
    order_detail.value = item
  }

  // 发货弹框点击取消
  const sldDeliverHandleCancle = ()=> {
    deliverModal.value = false
    selectedRowKeysTable.value = []
    selectedRowsTable.value = []
    formRefs.value.resetFields();
  }

   // 发货弹框点击确定
   const deliverConfirm = ()=> {
    if (selectedRowKeysTable.value.length == 0) {
      //需要验证是否选中产品
      failTip(L('请选择本次发货的产品～'));
      return false;
    }
    formRefs.value
      .validate()
      .then(async(values) => {
        let res
        if(type_info.value=='select'){
          values.deliverType = deliverType.value
          values.orderSns = selectedRowKeysTable.value.join(',')
          let productList = [];
          deliver_goods_table.value.forEach(item => {
            if (selectedRowKeysTable.value.indexOf(item.orderProductId) > -1) {
              productList.push({
                orderSn: item.orderSn,
                orderProductId: item.orderProductId,
                deliverNum: item.deliverNum,
              })
            }
          });
          values.productList = productList;
          res = await getOrderInfoMergeDeliverApi(values)
        }else{
          values.deliverType = deliverType.value
          values.orderSn = order_detail.value.orderSn
          let orderProduct = [];
          deliver_goods_table.value.forEach(item => {
            if (selectedRowKeysTable.value.indexOf(item.orderProductId) > -1) {
              orderProduct.push(`${item.orderProductId}-${item.deliverNum}`);
            }
          });
          values.orderProductInfo = orderProduct.join(',');
          confirmLoading.value = true
          res = await getOrderInfoDeliverApi(values)
        }
        if(res.state == 200){
          confirmLoading.value = false
          sucTip(res.msg)
          if (values.deliverType == 3 && res.data.url) {
            //电子面单发货成功需要打印电子面单
            window.open(res.data.url, '_blank');
          }
          let obj = { ...formValues.value, ...params.value }
          if (filter_code.value) {
            obj.orderState = filter_code.value;
          }
          get_list(obj);
          sldDeliverHandleCancle()
        }else{
          confirmLoading.value = false
          failTip(res.msg)
        }
      })
  }

  //关闭弹框
  const handleModalCancle = () => {
    operateData.value = [];
    modalVisible.value = false;
    title.value = ''
    operateType.value = ''
    modalConfirmBtnLoading.value = false
  };

  // 弹框点击确定
  const sldConfirm = (val)=> {
    val.orderSn = order_detail.value.orderSn;
    operate(val)
  }

  //加星事件
  const addStar = (e,item)=> {
    operateType.value = 'star'
    if (e * 1 != (item.star ? item.star * 1 : 0)) {
      operate({ orderSn: item.orderSn, star: e });
    }
  }

  //操作  remark:添加订单备注 star:设置订单星级 modifyPrice:未支付修改价格
  const operate = async(id)=> {
    // modalConfirmBtnLoading.value = true
    let res;
    let param_data = {};
    param_data = id;
    if(operateType.value == 'modifyPrice'){
      res = await getOrderUpdatePriceApi(param_data);   
    }else if(operateType.value == 'cancleOrder'){
      res = await getOrderInfoCancelApi(param_data);   
    }else if (operateType.value == 'star') {
      param_data = id;
      res = await getOrderStarApi(param_data);
    }else if(operateType.value == 'remark') {
      param_data = id;
      res = await getOrderRemarkApi(param_data);
    }
    
    if(res.state == 200){
      sucTip(res.msg)
      modalConfirmBtnLoading.value = false
      showTopTip.value = ''
      let obj = { ...formValues.value, ...params.value }
      if (filter_code.value) {
        obj.orderState = filter_code.value;
      }
      get_list(obj);
      handleModalCancle()
    }else{
      failTip(res.msg)
      modalConfirmBtnLoading.value = false
    }
  }

  // 订单导出
  const handleSldExcel = async()=> {
    let paramData = {
      ...params.value,
      ...formValues.value,
    };
    if(filter_code.value){
      paramData.orderState = filter_code.value;
    }
    paramData.fileName = L('订单导出');
    loading.value = true
    let res = await getOrderInfoExportApi(paramData)
    if(res.state != undefined && res.state == 255){
      failTip(res.msg)
    }
    loading.value = false
  }

  //电子面单批量发货的校验
  const checkOrderCreateKDNState = ()=> {
    if(selectedRowKeys.value.length==0){
      failTip(L('请先选中数据'))
      return
    }
    exterFaceSheetOptType.value = 'create'
    exterFaceSheetOptShowFlag.value = true
  }

  //批量生成、打印电子面单弹框取消操作
  const operateExterFaceSheetOptShowFlag = (flag)=>{
    if(flag){
      updateOrderList()
      exterFaceSheetOptShowFlag.value = false
      selectedRowKeys.value = []
      selectedRows.value = []
      allowPrintOrderSnArray.value = []
    }else{
      exterFaceSheetOptShowFlag.value = false
      allowPrintOrderSnArray.value = []
    }
  }

  //更新当前数据
  const updateOrderList = ()=> {
    let obj = { ...formValues.value, ...params.value }
      if (filter_code.value) {
        obj.orderState = filter_code.value;
      }
      get_list(obj);
  }

  // 订单详情
  const goDetail = (record)=> {
    userStore.setDelKeepAlive(['OrderListsToDetail'])
    router.push({
      path: `/order/order_lists_to_detail`,
      query: { orderSn: record.orderSn },
    });
  }
  

  //虚拟订单发货
  const virturalGoodsOrderDeliverConfirm = async(orderSn)=> {
    let res = await getOrderInfoDeliverApi({orderSn: orderSn})
    if(res.state == 200){
      sucTip(res.msg)
      userStore.setDelKeepAlive(['OrderListsToDetail'])
      let obj = { ...formValues.value, ...params.value }
      if (filter_code.value) {
        obj.orderState = filter_code.value;
      }
      get_list(obj);
    }else{
      failTip(res.msg)
    }
  }

  

  //订单条件过滤器
  const clickFilter = (e) => {
    filter_code.value = e.target.value;
    let param = { pageSize: pageSize.value, current: 1, ...formValues.value };
    if (e.target.value) {
      param.orderState = e.target.value;
    }
    get_list(param);
  };

  



  onMounted(() => {
    get_list({ pageSize: pageSize.value });
    get_express_list();
    get_reason_list();
    
  });
</script>
<style lang="less">
  @import './style/order_list.less';
</style>
