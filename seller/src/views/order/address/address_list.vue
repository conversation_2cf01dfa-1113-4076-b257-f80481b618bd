<template>
  <div class="section_padding point_goods_list">
    <div class="section_padding_back">
      <SldComHeader
        :title="L('地址管理')"
      />
      <Tabs type="card" v-model:activeKey="activeKey" class="tabs_nav" @change="activeKey_tab">
        <TabPane key="1" :tab="L('发货地址')">
          <ShowMoreHelpTip :tipData="goodsTip"></ShowMoreHelpTip>
          <SendAddress></SendAddress>
        </TabPane>
        <TabPane key="2" :tab="L('退货地址')">
          <ShowMoreHelpTip :tipData="goodsTip"></ShowMoreHelpTip>
          <ReturnAddress></ReturnAddress>
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'OrderAddressList',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, onMounted } from 'vue';
  import { Tabs, TabPane } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import SendAddress from './send_address.vue';
  import ReturnAddress from './return_address.vue';
  import ShowMoreHelpTip from '/@/components/ShowMoreHelpTip/index.vue';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const activeKey = ref('1');

  const canResize = ref(true);

  const goodsTip = ref([L('发货地址取下面的默认地址')]);

  const activeKey_tab = ()=> {
    goodsTip.value = activeKey.value=='1'?[L('发货地址取下面的默认地址')]:[L('退货地址取下面的默认地址')]
  }

  onMounted(() => {
    goodsTip.value = activeKey.value=='1'?[L('发货地址取下面的默认地址')]:[L('退货地址取下面的默认地址')]
  });
</script>
<style lang="less">
  .point_goods_list {
    .tabs_nav {
      .ant-tabs-nav {
        margin-bottom: 0;
      }
    }
  }
</style>
