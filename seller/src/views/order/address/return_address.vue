<template>
  <div class="seckill_label_lists">
    <div>
      <BasicTable @register="standardTable" style="padding: 0">
        <template #tableTitle>
          <div class="toolbar">
            <div class="toolbar_btn" @click="handleClick(null, 'add', null)">
              <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#fff" />
              <span>{{ L('添加退货地址') }}</span>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex == 'isDefault'">
            <div>
              <Switch
                @change="(checked) => handleClick(record, 'switch', checked ? 1 : 0)"
                :checked="text == 1 ? true : false"
              />
            </div>
          </template>
          <template v-if="column.key == 'action'">
            <TableAction
              :actions="[
                {
                  label: L('编辑'),
                  onClick: handleClick.bind(null, record, 'edit'),
                },
                {
                  label: L('删除'),
                  popConfirm: {
                    title: L('删除后不可恢复，是否确定删除？'),
                    placement: 'left',
                    confirm: handleClick.bind(null, record, 'del'),
                  },
                },
              ]"
            />
          </template>
        </template>
      </BasicTable>
    </div>
    <SldModal
      :width="width"
      :title="title"
      :visible="visible"
      :content="content"
      :confirmBtnLoading="confirmBtnLoading"
      :showFoot="true"
      @cancle-event="handleCancle"
      @confirm-event="handleConfirm"
      @cascader-change-event="getAreaInfo"
    />
  </div>
</template>
<script>
  export default {
    name: 'ReturnAddress',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, reactive, onMounted } from 'vue';
  import SldModal from '@/components/SldModal/index.vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { Switch } from 'ant-design-vue';
  import { sucTip, failTip } from '@/utils/utils';
  import AreaJson from '/@/assets/json/area.json';
  import {
    getSellerAddressListApi,
    getSellerAddressAddApi,
    getSellerAddressEditApi,
    getSellerAddressDelApi,
    getSellerAddressIsDefaultApi,
  } from '/@/api/order/address';
  import { mobile_reg, validatorSpecialString } from '/@/utils/validate';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const [standardTable, { reload }] = useTable({
    api: (arg) => getSellerAddressListApi({ ...arg,type:2 }),
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    columns: [
      {
        title: L('联系人'),
        dataIndex: 'contactName',
        width: 120,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: L('手机号'),
        dataIndex: 'telphone',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: L('退货地址'),
        dataIndex: 'address',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: L('设为默认'),
        dataIndex: 'isDefault',
        width: 60,
      },
    ],
    actionColumn: {
      width: 80,
      title: L('操作'),
      dataIndex: 'action',
    },
    useSearchForm: true,
    formConfig: {
      schemas: [
        {
          field: 'contactName',
          component: 'Input',
          componentProps: {
            minWidth: 300,
            placeholder: L('请输入联系人'),
            size: 'default',
          },
          label: L('联系人'),
        },
        {
          field: 'telphone',
          component: 'Input',
          componentProps: {
            minWidth: 300,
            placeholder: L('请输入手机号'),
            size: 'default',
          },
          label: L('手机号'),
        },
      ],
    },
    bordered: true,
    striped: false,
    showIndexColumn: true,
    rowKey:'addressId',
  });
  const width = ref(500);
  const title = ref('');
  const visible = ref(false);
  const content = ref([]);
  const confirmBtnLoading = ref(false);
  const operate_id = ref('');
  const operate_type = ref('');
  const sel_area_name = ref('')

  const addressId = ref('');

  const operate_add_edit = ref([
    {
      type: 'input',
      label: L('联系人'),
      name: 'contactName',
      placeholder: L('请输入联系人'),
      extra: L('最多输入6个字'),
      maxLength: 6,
      rules: [
        {
          required: true,
          whitespace: true,
          message: L('请输入联系人'),
        },
        {
          validator: async (rule, value) => {
            await validatorSpecialString(rule, value);
          },
        },
      ],
    },
    {
      type: 'input',
      label: L('手机号'),
      name: 'telphone',
      placeholder: L('请输入手机号'),
      maxLength: 11,
      rules: [
        {
          required: true,
          whitespace: true,
          message: L('请输入手机号'),
        },
        {
          pattern: mobile_reg,
          message: L('请输入正确的手机号'),
        },
      ],
    },
    {
      type: 'cascader_common',
      label: L('退货地址'),
      name: 'area',
      data: AreaJson,
      fieldNames: { label: 'regionName', value: 'regionCode', children: 'children' },
      placeholder: L('请选择退货地址'),
      initialValue: [],
      returnName:true,
      rules: [
        {
          required: true,
          message: L('请选择退货地址'),
        },
      ],
      callback: true,
    },
    {
      type: 'textarea',
      label: L('详细地址'),
      name: 'address',
      placeholder: L('请输入详细地址'),
      extra: L('最多40个字'),
      maxLength: 40,
      rules: [
        {
          required: true,
          whitespace: true,
          message: L('请输入详细地址'),
        },
      ],
      callback: true,
    },
    {
      type: 'switch',
      label: L('设为默认地址'), //开关
      name: 'isDefault',
      initialValue: 1, //默认值
      unCheckedValue: 0, //非选中的值 不传返回值为false
      checkedValue: 1, //选中的值 不传返回值为true
    },
  ]); //modal框的数据

  //表格点击回调事件
  function handleClick(item, type, check) {
    operate_type.value = type;
    if (item) {
      operate_id.value = item.addressId;
    }
    if (type == 'add' || type == 'edit') {
      content.value.forEach((it) => {
        it.initialValue = '';
      });
      content.value = [];
      operate_add_edit.value.forEach((it) => {
        if(it.type!='switch'){
          it.initialValue = '';
        }else{
          it.initialValue = 1;
        }
      });
    }
    if (type == 'add') {
      content.value = [];
      title.value = L('添加退货地址');
      content.value = operate_add_edit.value;
      visible.value = true;
    } else if (type == 'edit') {
      title.value = L('编辑退货地址');
      visible.value = true;
      let data = operate_add_edit.value;
      data.map((items) => {
        if(items.name == 'area'){
          items.initialValue = [item.provinceCode,item.cityCode,item.areaCode]
        }else{
          items.initialValue = item[items.name] ? item[items.name] : undefined;
        }
      });
      sel_area_name.value = item.areaInfo
      content.value = data;
      addressId.value = item.addressId;
    } else if (type == 'del') {
      operate_role({ addressId: item.addressId });
    } else if (type == 'switch') {
      operate_role({ addressId: item.addressId, isDefault: check });
    }
  }

  //弹窗取消事件
  function handleCancle() {
    visible.value = false;
    content.value = [];
    addressId.value = '';
  }

  //弹窗确认事件
  function handleConfirm(val) {
    if (operate_type.value == 'edit') {
      val.addressId = addressId.value;
    }
    val.areaInfo = sel_area_name.value
    if (val.area) {
      val.provinceCode = val.area[0] != undefined ? val.area[0] : '';
      val.cityCode = val.area[1] != undefined ? val.area[1] : '';
      val.areaCode = val.area[2] != undefined ? val.area[2] : '';
      delete val.area;
    }
    val.type = 2
    operate_role(val);
  }

  //获取地址信息
  const getAreaInfo = (area) => {
    sel_area_name.value = '';
    for (let i in area) {
      sel_area_name.value += area[i].regionName;
    }
  };

  //商品标签操作
  const operate_role = async (params) => {
    confirmBtnLoading.value = true;
    let res = {};
    if (operate_type.value == 'add') {
      res = await getSellerAddressAddApi(params);
    } else if (operate_type.value == 'edit') {
      res = await getSellerAddressEditApi(params);
    } else if (operate_type.value == 'del') {
      res = await getSellerAddressDelApi(params);
    } else if (operate_type.value == 'switch') {
      res = await getSellerAddressIsDefaultApi(params);
    }
    confirmBtnLoading.value = false;
    if (res.state == 200) {
      sucTip(res.msg);
      handleCancle();
      reload();
    } else {
      failTip(res.msg);
    }
  };
</script>
<style lang="less" scoped></style>
