<template>
  <div class="seckill_label_lists">
    <div>
      <BasicTable @register="standardTable" style="padding: 0">
        <template #tableTitle>
          <div class="toolbar">
            <div class="toolbar_btn" @click="handleSldExcel">
              <AliSvgIcon iconName="iconziyuan23" width="15px" height="15px" fillColor="#ff5908" />
              <span>{{ L('导出') }}</span>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex == 'productImage'">
            <div class="goods_info com_flex_row_flex_start">
              <div class="goods_img">
                <Popover placement="rightTop">
                  <template #content>
                    <div style="width: 200px;height:200px;display: flex;align-items: center;justify-content: center;">
                      <img :src="text" alt="" style="max-width: 100%; max-height: 100%" />
                    </div>
                  </template>
                  <div class="business_load_img">
                    <img :src="text" alt="" />
                  </div>
                </Popover>
              </div>
              <div class="com_flex_column_space_between goods_detail">
                <span class="goods_name">{{ record.goodsName }}</span>
                <span class="goods_brief" style="margin-bottom:0">
                  {{ L('订单编号：') }}{{ record.orderSn }}
                </span>
                <span class="goods_brief" style="margin-bottom:0">
                  {{ L('退款编号：') }}{{ record.afsSn }}
                </span>
              </div>
            </div>
          </template>
          <!-- dev_supplier-start -->
          <template v-if="column.dataIndex=='saleStoreName'">
            <div class="mem_name" v-if="record.saleStoreName&&!record.memberName">{{ record.saleStoreName }}</div>
            <div class="mem_name" v-if="record.vendorName&&!record.memberName">{{ record.vendorName }}</div>
            <div class="mem_name" v-if="record.vendorMobile&&!record.memberName">{{ record.vendorMobile }}</div>
            <div class="mem_name" v-if="(!record.saleStoreName&&!record.vendorMobile&&!record.vendorName)||record.memberName">--</div>
          </template>
          <!-- dev_supplier-end -->
          <template v-if="column.key == 'action'">
            <TableAction
              :actions="[
                {
                  onClick: view.bind(null, record),
                  label: L('查看详情'),
                },
              ]"
            />
          </template>
        </template>
      </BasicTable>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'RefundLists',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, reactive, onMounted } from 'vue';
  import { BasicTable, useTable,TableAction } from '/@/components/Table';
  import { Popover } from 'ant-design-vue';
  import { useUserStore } from '/@/store/modules/user';
  import { sucTip, failTip } from '@/utils/utils';
  import {
    getSellerAfterSaleListApi,
    getSellerAfterSaleExportApi
  } from '/@/api/order/service';
  import { useRouter } from 'vue-router';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const router = useRouter();
  const userStore = useUserStore();

  const [standardTable, { reload, getForm, getPaginationRef, getDataSource,getColumns,setColumns }] = useTable({
    api: (arg) => getSellerAfterSaleListApi({ ...arg, returnType: 2 }),
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    columns: [
      {
        title: L('商品信息'),
        dataIndex: 'productImage',
        width: 250,
      },
      {
        title: L('退款金额(元)'),
        dataIndex: 'returnMoneyAmount',
        width: 100,
      },
      {
        title: L('买家会员名'),
        dataIndex: 'memberName',
        width: 150,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: L('申请时间'),
        dataIndex: 'applyTime',
        width: 150,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: L('退款状态'),
        dataIndex: 'stateValue',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
    ],
    useSearchForm: true,
    formConfig: {
      schemas: [
        {
          field: 'orderSn',
          component: 'Input',
          componentProps: {
            placeholder: L('请输入订单号'),
            size: 'default',
          },
          label: L('订单号'),
        },
        {
          field: 'afsSn',
          component: 'Input',
          componentProps: {
            placeholder: L('请输入退款编号'),
            size: 'default',
          },
          label: L('退款编号'),
        },
        {
          field: 'memberName',
          component: 'Input',
          componentProps: {
            placeholder: L('请输入会员名'),
            size: 'default',
          },
          label: L('会员名'),
        },
        {
          field: '[startTime,endTime]',
          component: 'RangePicker',
          componentProps: {
            format: 'YYYY-MM-DD',
            placeholder: [L('开始时间'), L('结束时间')],
          },
          label: L('申请时间'),
        },
        {
        component: 'Select',
        label: L('退款状态'),
        field: 'state',
        componentProps: {
          placeholder: L('请选择退款状态'),
          options: [
            { value: '', label: L('全部') },
            { value: '101', label: L('待商家审核') },
            { value: '201', label: L('待买家发货') },
            { value: '102', label: L('待商家收货') },
            { value: '202', label: L('售后关闭') },
            { value: '203', label: L('待平台处理') },
            { value: '300', label: L('退款成功') },
          ],
        },
      },
      ],
    },
    actionColumn: {
      width: 80,
      title: L('操作'),
      dataIndex: 'action',
    },
    beforeFetch(values) {
      values.startTime = values.startTime
        ? values.startTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
      return values;
    },
    bordered: true,
    striped: false,
    showIndexColumn: true,
    rowKey:'afsSn',
  });

  // 导出
  const handleSldExcel = async () => {
    if (getDataSource().length == 0) {
      failTip('暂无数据可导出');
      return;
    }
    let paramData = getForm().getFieldsValue()
    paramData.pageSize = getPaginationRef().pageSize
    if(Number(getPaginationRef().current)>1){
      paramData.current = getPaginationRef().current
    }
    for(let i in paramData){
      if(paramData[i]==undefined){
        delete paramData[i]
      }
    }
    paramData.fileName = L('售后数据导出')
    paramData.returnType = 2;
    let res = await getSellerAfterSaleExportApi(paramData)
    if(res.state == 200){
      if(res.state!=undefined && res.state == 255){
          failTip(res.msg);
        }
    }
  }

  const view = (record) => {
    router.push({
      path: `/order/service_refund_lists_to_detail`,
      query: { afsSn: record.afsSn },
    });
  };


  onMounted(() => {
    // dev_supplier-start
    if(userStore.getStoreInfo.shopType&&userStore.getStoreInfo.shopType=='3'){
      let columns_order = getColumns();
      let temp = columns_order.filter(item=>item.dataIndex == 'saleStoreName')
      if(temp.length==0){
        for (let i = 0; i < columns_order.length; i++) {
          if (columns_order[i].dataIndex == 'memberName') {
              columns_order.splice(i + 1, 0,  {
              title: L('代销店铺'),
              dataIndex: 'saleStoreName',
              width: 150,
            });
            setColumns(columns_order);
            break;
          }
        }
      }
    }
    // dev_supplier-end
  });
</script>
<style lang="less">
@import '../style/order.less';
.business_load_img {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    margin: auto;

    img {
      max-width: 100%;
      max-height: 100%;
    }
  }
</style>
