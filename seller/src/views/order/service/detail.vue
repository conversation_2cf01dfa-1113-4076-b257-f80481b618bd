<template>
  <div class="section_padding spell_order_detail">
    <div class="section_padding_back">
      <SldComHeader :title="L('订单详情')" back />
      <div style="width: 100%; height: 10px"></div>
      <Spin :spinning="loading">
        <div class="height_detail">
          <div class="flex_row_center_start progress">
            <div
              class="flex_column_start_center item"
              v-for="(item, index) in return_progress_data"
              :key="index"
            >
              <div class="top flex_row_center_center">
                <span class="left_line" :style="{ borderColor: item.line_color }"></span>
                <img :src="item.icon" alt="" />
                <span class="right_line" :style="{ borderColor: item.line_color }"></span>
              </div>
              <span class="state" :style="{ color: item.state_color }">{{ item.state }}</span>
              <span class="time" :style="{ color: item.time_color }">{{ item.time }}</span>
            </div>
          </div>
          <div class="state_part flex_column_start_center" v-if="goods_detail_data.returnType == 1 && goods_detail_data.state == 100">
            <span class="title">{{ goods_detail_data.stateValue }}</span>
            <span class="tip"
              >{{ L('收到买家仅退款申请，请尽快处理') }}</span
            >
            <div v-if="isSupplier == 2" class="btnsty">
              <div class="agree_btn" @click="agreeReturn('agreegoods', goods_detail_data.returnType)">{{ L('同意退款申请') }}</div>
              <div class="cancle_btn"  @click="agreeReturn('refusegoods',goods_detail_data.returnType)">{{ L('拒绝退款申请') }}</div>
            </div>
          </div>
          <div class="state_part flex_column_start_center" v-if="(goods_detail_data.returnType == 1 || goods_detail_data.returnType == 2) && goods_detail_data.state == 300">
            <span class="title">{{ L('退款成功') }}</span>
            <span class="tip"
              >{{ L('退款金额：') }}<span style="color: #ff1818;">¥{{ goods_detail_data.returnMoneyAmount }}</span></span
            >
            <span class="tip"
              >{{ L('平台审核备注：') }}{{ goods_detail_data.platformRemark }}</span
            >
          </div>
          <div class="state_part flex_column_start_center" v-if="(goods_detail_data.returnType == 1 || goods_detail_data.returnType == 2) && goods_detail_data.state == 202">
            <span class="title">{{ goods_detail_data.stateValue }}</span>
            <span class="tip"
              >{{ L('拒绝原因：') }}{{ goods_detail_data.storeRemark }}{{ goods_detail_data.refuseReason }}</span
            >
          </div>
          <div class="state_part flex_column_start_center" v-if="goods_detail_data.returnType == 1 && goods_detail_data.state == 200">
            <span class="title">{{ L('待平台审核') }}</span>
          </div>
          <div class="state_part flex_column_start_center" v-if="goods_detail_data.returnType == 2 && goods_detail_data.state == 101">
            <span class="title">{{goods_detail_data.stateValue}}</span>
            <span class="tip">{{ L('收到买家退货退款申请，请尽快处理') }}</span>
            <div class="btnsty" v-if="isSupplier == 2">
              <div class="agree_btn" @click="agreeReturn('agreelocal',goods_detail_data.returnType)">{{ L('同意退款申请，发送退货地址') }}</div>
              <div class="refuse_btn" @click="agreeReturn('refusegoods',goods_detail_data.returnType)">{{ L('拒绝退款申请') }}</div>
            </div>
          </div>
          <div class="state_part flex_column_start_center" v-if="goods_detail_data.returnType == 2 && goods_detail_data.state == 201">
            <span class="title">{{showName}}{{ L('已同意退货申请，等待买家退货') }}</span>
            <span class="tip">{{ isSupplier == 1 ? L('供应商') : L('您')}}{{ L('已同意本次退货退款申请，等待买家发货') }}</span>
            <span class="tip">{{ L('买家需') }}<span>{{ goods_detail_data.deadline }}</span>{{ L('之前发货,否则退款申请将自动撤销。') }}</span>
            <div class="btnsty"></div>
          </div>
          <div class="state_part flex_column_start_center" v-if="goods_detail_data.returnType == 2 && goods_detail_data.state == 102">
            <span class="title">{{ L('买家已退货，等待') }}{{showName}}{{ L('确认收货') }}</span>
            <span class="tip">{{ L('买家已退货，退货物流公司') }}{{goods_detail_data.buyerExpressName}}，</span>
            <span class="tip">{{ L('退货物流单号:') }}{{ goods_detail_data.buyerExpressNumber }} <a src="javascript: void(0);" style="color: #FF711E;" @click="checkFlow">{{ L('查看物流') }}</a></span>
            <div class="btnsty">
              <template v-if="isSupplier == 2">
                <div class="cancle_btn"  @click="agreeReturn('agreegoods', goods_detail_data.returnType)" style="width: auto;">
                  {{ L('已收到货,同意退款') }}
                </div>
                <div class="refuse_btn" @click="agreeReturn('refusegoods',goods_detail_data.returnType)">
                  {{ L('拒绝退款申请') }}
                </div>
              </template>
            </div>
          </div>
          <div class="state_part flex_column_start_center" v-if="goods_detail_data.returnType == 2 && goods_detail_data.state == 203">
            <span class="title">{{ L('待平台处理') }}</span>
          </div>
         
          <!-- 退款信息 start -->
          <div class="sld_common_title">{{ L('退款信息') }}</div>
          <Descriptions :bordered="true" :column="2" size="middle">
            <DescriptionsItem :label='item.label' v-for="(item,index) in order_return_data" :key="index">
              <div v-if="item.type=='show_text'">
                <div v-if="item.text!=null&&item.text.length>62">
                  <Tooltip placement="bottom">
                    <template #title>{{ item.text }}</template>
                    <div style="margin-left: 5px;margin-top: 3px;">
                     <span>{{ item.text.substring(0, 61) }}...</span> 
                    </div>
                  </Tooltip>
                </div>
                <div v-else>
                  {{ item.text!=null ? item.text : '--' }}
                </div>
              </div>
              <div v-else-if="item.type=='show_text_edit'" class="flex_row_start_start">
                <div v-if="item.text!=null&&item.text.length>62">
                  <Tooltip placement="bottomRight">
                    <template #title>{{ item.text }}</template>
                    <div style="margin-left: 5px;margin-top: 3px;">
                     <span>{{ item.text.substring(0, 61) }}...</span> 
                     <img src="@/assets/images/order/edit_store_remark.png" alt="" style="width: 15px;height: 15px;margin-left: 4px;cursor: pointer;" @click="addOrderRemark">
                    </div>
                  </Tooltip>
                </div>
                <div v-else>
                  {{ item.text!=null ? item.text : '--' }}
                  <img src="@/assets/images/order/edit_store_remark.png" alt="" style="width: 15px;height: 15px;margin-left: 4px;cursor: pointer;" @click="addOrderRemark">
                </div>
              </div>
              <div class="flex_row_start_center star_part" v-if="item.type=='show_order_star'">
                <template v-if="item.text">
                  <div class="star add_star">
                    <Rate :value="item.text" style="font-size: 18px;"/>
                  </div>
                </template>
                <span v-else class="operate_btn add_star">{{ L('加星') }}</span>
                <div class="flex_row_start_center star">
                  <img src="@/assets/images/order/clear_star.png" alt="" style="width: 18px;height: 18px;margin-right: 5px;margin-top: -1px;cursor: pointer;" @click="addStar(0)">
                  <Rate :value="item.text" style="font-size: 18px;" @change="addStar"/>
                </div>
              </div>
            </DescriptionsItem>
          </Descriptions>
          <!-- 退款信息 end -->
          <!-- dev_supplier-start -->
          <!-- 代销店铺信息-start -->
          <div class="sld_common_title" v-if="userStore.getStoreInfo.shopType&&userStore.getStoreInfo.shopType=='3'&&!goods_detail_data.memberName">{{ L('代销店铺信息') }}</div>
          <Descriptions v-if="userStore.getStoreInfo.shopType&&userStore.getStoreInfo.shopType=='3'&&!goods_detail_data.memberName" :bordered="true" :column="2" size="middle">
            <DescriptionsItem :label='item.label' v-for="(item,index) in order_supplier_data" :key="index">
              <div v-if="item.type=='show_text'">
                <div v-if="item.text!=null&&item.text.length>62">
                  <Tooltip placement="bottom">
                    <template #title>{{ item.text }}</template>
                    <div style="margin-left: 5px;margin-top: 3px;">
                     <span>{{ item.text.substring(0, 61) }}...</span> 
                    </div>
                  </Tooltip>
                </div>
                <div v-else>
                  {{ item.text!=null ? item.text : '--' }}
                </div>
              </div>
            </DescriptionsItem>
          </Descriptions>
          <!-- 代销店铺信息-end -->
          <!-- dev_supplier-end -->
          <!-- 退款凭证信息 start -->
          <div class="sld_common_title">{{ L('退款凭证信息') }}</div>
          <Descriptions :bordered="true" :column="4" size="middle" class="invoice_info_box">
            <DescriptionsItem :label='item.label' v-for="(item,index) in order_return_img_data" :key="index">
              <div class="flex_row_start_center">
                <template v-if="item.data.length>0">
                  <ImagePreviewGroup>
                  <div class="flex_row_center_center invoice_info_box_img" v-for="(it,ind) in item.data" :key="ind">
                      <Image :src="it.imageUrl" style="max-width: 100%;max-height: 100%;"></Image>
                    </div>
                  </ImagePreviewGroup>
                </template>
                <div v-else>--</div>
              </div>
            </DescriptionsItem>
          </Descriptions>
          <!-- 退款凭证信息 end -->
          <!-- 商品信息 start -->
          <div class="sld_common_title">{{ L('商品信息') }}</div>
          <BasicTable
            :columns="columns_order_goods"
            :bordered="true"
            :pagination="false"
            :dataSource="[goods_detail_data]"
            :canResize="false"
          >
            <template #bodyCell="{ column, record, text }">
              <template v-if="column.dataIndex == 'productImage'">
                <div class="goods_info com_flex_row_flex_start">
                  <div class="goods_img" style="border: none">
                    <Popover placement="rightTop">
                      <template #content>
                        <div style="width: 200px">
                          <img :src="text" alt="" style="max-width: 100%; max-height: 100%" />
                        </div>
                      </template>
                      <div class="business_load_img">
                        <img :src="text" alt="" />
                      </div>
                    </Popover>
                  </div>
                  <div class="com_flex_column_space_between goods_detail">
                    <div
                      class="goods_name"
                      style="max-width: 380px; margin-top: 6px; white-space: initial"
                      :title="record.goodsName"
                    >
                      {{ record.goodsName }}
                    </div>
                    <span class="goods_brief" :title="record.specValues">
                      {{ record.specValues }}
                    </span>
                  </div>
                </div>
              </template>
            </template>
          </BasicTable>
          <!-- 商品信息 end -->
        </div>
      </Spin>
    </div>
    <SldModal
      :width="width"
      :title="title"
      :visible="modalVisible"
      :height="600"
      :content="operateData"
      :showFoot="showFoot"
      :confirmBtnLoading="modalConfirmBtnLoading"
      @cancle-event="handleModalCancle"
      @confirm-event="sldConfirm"
      :showTopTip="showTopTip"
    />
    <Modal
      centered
      :destroyOnClose="true"
      :zIndex="999"
      :title="L('选择退货地址')"
      :visible="islocalModalVisible"
      @ok="sldConfirmAddress"
      @cancel="onhandleModalVisible"
    >
      <div style="margin: 20px;" class="return_of_goods">
        <BasicTable
          @register="registerTable"
          :columns="addressColu"
          :dataSource="addressList"
          :clickToRowSelect="true"
          :showIndexColumn="false"
          :pagination="false"
          :bordered="true"
          :ellipsis="false"
          :maxHeight="400"
          style="padding: 0">
        </BasicTable>
      </div>
    </Modal>
  </div>
</template>
<script>
  export default {
    name: 'SpreaderOrderToDetail',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, reactive, onMounted } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { Spin, Popover,Descriptions,DescriptionsItem,Tooltip,Rate,Image,ImagePreviewGroup ,Modal} from 'ant-design-vue';
  import { sucTip, failTip } from '@/utils/utils';
  import { useRoute, useRouter } from 'vue-router';
  import { BasicTable,useTable } from '/@/components/Table';
  import SldModal from '/@/components/SldModal/index.vue';
  import { getSellerAfterSaleDetailApi,getSellerAfterSaleAuditApi,getSellerAfterSaleConfirmReceiveApi,getSellerAddressListApi,getSellerAfsGetTraceApi } from '/@/api/order/service';
  import { list_com_page_more } from '/@/utils/utils';
  import { useUserStore } from '/@/store/modules/user';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const route = useRoute();
  const userStore = useUserStore();
  const router = useRouter();
  const loading = ref(true);
  const query = ref(route.query);
  const goods_detail_data = ref({
    invoiceInfo:{}
  });
  const addressColu = ref([
    {
      title: L('收货地址'),
      dataIndex: 'areaInfo',
      align: 'left',
      width: 300,
      customRender: ({ text, record, index }) => {
        return text + record.address
      },
    },
  ])
  const [registerTable, { getSelectRowKeys,setSelectedRowKeys }] = useTable({
    rowKey:"addressId",
    rowSelection: {
      type: 'radio',
    },
  });
  const return_progress_data = ref([]); //退货进度条
  const modalVisible = ref(false);
  const showFoot = ref(true);
  const width = ref(700)
  const showTopTip = ref('')
  const modalConfirmBtnLoading = ref(false);
  const title = ref('')
  const order_detail_info = ref({})
  const operateData = ref([]); //弹框操作数据
  const type = ref('')
  const returnType = ref(0)

  const selectedRowKeys = ref([])
  const selectedRows = ref([])
  const addressList = ref([])
  const islocalModalVisible = ref(false)

  const order_return_img_data = ref([
    {
      type: 'show_goods_img_more',
      name: 'imgMore',
      label: L('退款凭证'),
      labelMinWidth: 10,
      contentMinWidth: 100,
      data: [],
      extra: ``,
      item_height: 140,
    },
  ])
  
  // dev_supplier-start
  const order_supplier_data = ref([
    {
      type: 'show_text',
      name: 'saleStoreName',
      label: L('店铺名称'),
      labelMinWidth: 10,
      contentMinWidth: 100,
      text:'',
    },
    {
      type: 'show_text',
      name: 'vendorName',
      label: L('店铺账号'),
      labelMinWidth: 10,
      contentMinWidth: 100,
      text:'',
    },
    {
      type: 'show_text',
      name: 'vendorMobile',
      label: L('联系人电话'),
      labelMinWidth: 10,
      contentMinWidth: 100,
      text:'',
    },
  ])
  // dev_supplier-end

  const order_return_data = ref([
    {
      type: 'show_text',
      name: 'orderSn',
      label: L('订单号'),
      labelMinWidth: 10,
      contentMinWidth: 100,
      text:'',
    },
    {
      type: 'show_text',
      name: 'afsSn',
      label: L('退款编码'),
      labelMinWidth: 10,
      contentMinWidth: 100,
      text:'',
    },
    {
      type: 'show_text',
      name: 'returnMoneyAmount',
      label: L('退款金额(元)'),
      labelMinWidth: 10,
      contentMinWidth: 100,
      text:'',
    },
    {
      type: 'show_text',
      name: 'applyReasonContent',
      label: L('退款原因'),
      labelMinWidth: 10,
      contentMinWidth: 100,
      text:'',
    },
    {
      type: 'show_text',
      name: 'returnTypeValue',
      label: L('退款类型'),
      labelMinWidth: 10,
      text:'',
      contentMinWidth: 100,
    },
    {
      type: 'show_text',
      name: 'memberName',
      label: L('会员'),
      labelMinWidth: 10,
      text:'',
      contentMinWidth: 100,
    },
    {
      type: 'show_text',
      name: 'afsDescription',
      label: L('退款说明'),
      labelMinWidth: 10,
      text:'',
      contentMinWidth: 100,
    },
  ])
  
  const columns_order_goods = ref([
    {
      type: 'show_text',
      title: L('商品信息'),
      dataIndex: 'productImage',
      align: 'center',
      width: 500,
    },
    {
      type: 'show_text',
      title: L('商品单价(元)'),
      dataIndex: 'productShowPrice',
      align: 'center',
      width: 100,
    },
    {
      type: 'show_text',
      title: L('退款数量'),
      dataIndex: 'returnNum',
      align: 'center',
      width: 100,
    },
    {
      type: 'show_text',
      title: L('退款金额(元)'),
      dataIndex: 'returnMoneyAmount',
      align: 'center',
      width: 100,
    },
  ]);

  const showName = ref(L('商家'))//用于统一展示操作方是供应商还是商家
  const isSupplier = ref(2)//1为商品来源于供应商，2为商家自营
  const isagress = ref(false)

  // 详情数据
  const get_order_detail = async (params) => {
    let res = await getSellerAfterSaleDetailApi(params);
    if (res.state == 200) {
      loading.value = false;
      goods_detail_data.value = res.data;
      // orderLogList.value = res.data.orderLogs;
      let goods_detail_data_data = JSON.parse(JSON.stringify(goods_detail_data.value))
      showName.value = goods_detail_data_data.thirdOrderSn != undefined && goods_detail_data_data.thirdOrderSn ? L('供应商') : L('商家');
      isSupplier.value = goods_detail_data_data.thirdOrderSn != undefined && goods_detail_data_data.thirdOrderSn ? 1 : 2;
      let logInfo = res.data.returnLogList;
      //订单信息
      for (let reagent in order_return_data.value) {
        order_return_data.value[reagent].text = goods_detail_data_data[order_return_data.value[reagent].name]?goods_detail_data_data[order_return_data.value[reagent].name]:'--';
      }

      // dev_supplier-start
      // 代销店铺信息
      if(userStore.getStoreInfo.shopType&&userStore.getStoreInfo.shopType=='3'&&!res.data.memberName){
        for (let reagent_supp in order_supplier_data.value) {
          order_supplier_data.value[reagent_supp].text = goods_detail_data_data[order_supplier_data.value[reagent_supp].name]?goods_detail_data_data[order_supplier_data.value[reagent_supp].name]:'--';
        }
      }
      // dev_supplier-end
      order_return_img_data.value[0].data = [];//清空数据，防止操作完成之后数据追加
      if (res.data.applyImageList.length > 0) {
        res.data.applyImageList.map(item => {
          order_return_img_data.value[0].data.push({ imageUrl: item });
        });
      }
      return_progress_data.value = [];
      //退货状态数据处理
      if (goods_detail_data_data.returnType == 1) { //仅退款
        if (goods_detail_data_data.state == 100) {
          //买家申请仅退款
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/tui_current.png', import.meta.url).href,
            state: L('买家申请仅退款'),
            time: (logInfo.length > 0 && logInfo[0].createTime != undefined) ? logInfo[0].createTime : '',
            state_color: '#FF711E',
            time_color: '#FF711E',
            line_color: '#FF711E',
          });
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/shoper_pass.png', import.meta.url).href,
            state: L('商家处理仅退款申请'),
            time: '',
            state_color: '#999999',
            time_color: 'rgba(153, 153, 153, .5)',
            line_color: '#eee',
          });
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/complete_pass.png', import.meta.url).href,
            state: L('退款完成'),
            time: '',
            state_color: '#999999',
            time_color: 'rgba(153, 153, 153, .5)',
            line_color: '#eee',
          });
        } else if (goods_detail_data_data.state == 200) {
          //退款完成
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/tui_future.png', import.meta.url).href,
            state: L('买家申请仅退款'),
            time: (logInfo.length > 0 && logInfo[0].createTime != undefined) ? logInfo[0].createTime : '',
            state_color: 'rgba(255, 109, 31, .6)',
            time_color: 'rgba(255, 109, 31, .3)',
            line_color: 'rgba(255, 109, 31, .3)',
          });
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/shoper_current.png', import.meta.url).href,
            state: L('商家处理仅退款申请'),
            time: (logInfo.length > 1 && logInfo[1].createTime != undefined) ? logInfo[1].createTime : '',
            state_color: '#FF711E',
            time_color: '#FF711E',
            line_color: '#FF711E',
          });
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/complete_pass.png', import.meta.url).href,
            state: L('退款完成'),
            time: '',
            state_color: '#999999',
            time_color: 'rgba(255, 109, 31, .5)',
            line_color: '#eee',
          });
        } else if (goods_detail_data_data.state == 300) {
          //退款完成
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/tui_future.png', import.meta.url).href,
            state: L('买家申请仅退款'),
            time: (logInfo.length > 0 && logInfo[0].createTime != undefined) ? logInfo[0].createTime : '',
            state_color: 'rgba(255, 109, 31, .6)',
            time_color: 'rgba(255, 109, 31, .3)',
            line_color: 'rgba(255, 109, 31, .3)',
          });
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/shoper_future.png', import.meta.url).href,
            state: L('商家处理仅退款申请'),
            time: (logInfo.length > 1 && logInfo[1].createTime != undefined) ? logInfo[1].createTime : '',
            state_color: 'rgba(255, 109, 31, .6)',
            time_color: 'rgba(255, 109, 31, .3)',
            line_color: 'rgba(255, 109, 31, .3)',
          });
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/complete_current.png', import.meta.url).href,
            state: L('退款完成'),
            time: (logInfo.length > 2 && logInfo[2].createTime != undefined) ? logInfo[2].createTime : '',
            state_color: 'rgba(255, 109, 31, 1)',
            time_color: 'rgba(255, 109, 31, 1)',
            line_color: '#FF711E',
          });
        } else if (goods_detail_data_data.state == 202) {
          //退款关闭
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/tui_future.png', import.meta.url).href,
            state: L('买家申请仅退款'),
            time: (logInfo.length > 0 && logInfo[0].createTime != undefined) ? logInfo[0].createTime : '',
            state_color: 'rgba(255, 109, 31, .6)',
            time_color: 'rgba(255, 109, 31, .3)',
            line_color: 'rgba(255, 109, 31, .3)',
          });
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/refu_current.png', import.meta.url).href,
            state: L('退款关闭'),
            time: (logInfo.length > 1 && logInfo[1].createTime != undefined) ? logInfo[1].createTime : '',
            state_color: '#FF711E',
            time_color: '#FF711E',
            line_color: '#FF711E',
          });
        }
      } else { // 退货退款
        if (goods_detail_data_data.state == 101) {
          //买家申请退货退款
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/tui_current.png', import.meta.url).href,
            state: L('买家申请退货退款'),
            time: (logInfo.length > 0 && logInfo[0].createTime != undefined) ? logInfo[0].createTime : '',
            state_color: '#FF711E',
            time_color: '#FF711E',
            line_color: '#FF711E',
          });
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/shoper_pass.png', import.meta.url).href,
            state: L('商家同意退货退款申请'),
            time: '',
            state_color: '#999999',
            time_color: 'rgba(153, 153, 153, .5)',
            line_color: '#eee',
          });
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/buyer_pass.png', import.meta.url).href,
            state: L('买家退货给商家'),
            time: '',
            state_color: '#999999',
            time_color: 'rgba(153, 153, 153, .5)',
            line_color: '#eee',
          });
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/received_pass.png', import.meta.url).href,
            state: L('商家确认收货'),
            time: '',
            state_color: '#999999',
            time_color: 'rgba(153, 153, 153, .5)',
            line_color: '#eee',
          });
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/complete_pass.png', import.meta.url).href,
            state: L('退款完成'),
            time: '',
            state_color: '#999999',
            time_color: 'rgba(153, 153, 153, .5)',
            line_color: '#eee',
          });
        } else if (goods_detail_data_data.state == 201) {
          //买家申请退货退款
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/tui_future.png', import.meta.url).href,
            state: L('买家申请退货退款'),
            time: (logInfo.length > 0 && logInfo[0].createTime != undefined) ? logInfo[0].createTime : '',
            state_color: 'rgba(255, 109, 31, .6)',
            time_color: 'rgba(255, 109, 31, .5)',
            line_color: 'rgba(255, 109, 31, .3)',
          });
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/shoper_current.png', import.meta.url).href,
            state: L('商家处理退货退款申请'),
            time: (logInfo.length > 1 && logInfo[1].createTime != undefined) ? logInfo[1].createTime : '',
            state_color: '#FF711E',
            time_color: '#FF711E',
            line_color: '#FF711E',
          });
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/buyer_pass.png', import.meta.url).href,
            state: L('买家退货给商家'),
            time: '',
            state_color: '#999999',
            time_color: 'rgba(153, 153, 153, .5)',
            line_color: '#eee',
          });
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/received_pass.png', import.meta.url).href,
            state: L('商家确认收货'),
            time: '',
            state_color: '#999999',
            time_color: 'rgba(153, 153, 153, .5)',
            line_color: '#eee',
          });
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/complete_pass.png', import.meta.url).href,
            state: L('退款完成'),
            time: '',
            state_color: '#999999',
            time_color: 'rgba(153, 153, 153, .5)',
            line_color: '#eee',
          });
        } else if (goods_detail_data_data.state == 202) {
          //退款退货申请关闭
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/tui_future.png', import.meta.url).href,
            state: L('买家申请退货退款'),
            time: (logInfo.length > 0 && logInfo[0].createTime != undefined) ? logInfo[0].createTime : '',
            state_color: 'rgba(255, 109, 31, .6)',
            time_color: 'rgba(255, 109, 31, .3)',
            line_color: 'rgba(255, 109, 31, .3)',
          });
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/refu_current.png', import.meta.url).href,
            state: L('退款退货申请关闭'),
            time: (logInfo.length > 1 && logInfo[1].createTime != undefined) ? logInfo[1].createTime : '',
            state_color: 'rgba(255, 109, 31, 1)',
            time_color: 'rgba(255, 109, 31, 1)',
            line_color: '#FF711E',
          });
        } else if (goods_detail_data_data.state == 102) {
          //买家申请退货退款
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/tui_future.png', import.meta.url).href,
            state: L('买家申请退货退款'),
            time: (logInfo.length > 0 && logInfo[0].createTime != undefined) ? logInfo[0].createTime : '',
            state_color: 'rgba(255, 109, 31, .6)',
            time_color: 'rgba(255, 109, 31, .5)',
            line_color: 'rgba(255, 109, 31, .3)',
          });
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/shoper_future.png', import.meta.url).href,
            state: L('商家处理退货退款申请'),
            time: (logInfo.length > 1 && logInfo[1].createTime != undefined) ? logInfo[1].createTime : '',
            state_color: 'rgba(255, 109, 31, .6)',
            time_color: 'rgba(255, 109, 31, .5)',
            line_color: 'rgba(255, 109, 31, .3)',
          });
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/buyer_current.png', import.meta.url).href,
            state: L('买家退货给商家'),
            time: (logInfo.length > 2 && logInfo[2].createTime != undefined) ? logInfo[2].createTime : '',
            state_color: '#FF711E',
            time_color: '#FF711E',
            line_color: '#FF711E',
          });
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/received_pass.png', import.meta.url).href,
            state: L('商家确认收货'),
            time: '',
            state_color: '#999999',
            time_color: 'rgba(153, 153, 153, .5)',
            line_color: '#eee',
          });
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/complete_pass.png', import.meta.url).href,
            state: L('退款完成'),
            time: '',
            state_color: '#999999',
            time_color: 'rgba(153, 153, 153, .5)',
            line_color: '#eee',
          });
        } else if (goods_detail_data_data.state == 300) {
          //买家申请退货退款
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/tui_future.png', import.meta.url).href,
            state: L('买家申请退货退款'),
            time: (logInfo.length > 0 && logInfo[0].createTime != undefined) ? logInfo[0].createTime : '',
            state_color: 'rgba(255, 109, 31, .6)',
            time_color: 'rgba(255, 109, 31, .5)',
            line_color: 'rgba(255, 109, 31, .3)',
          });
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/shoper_future.png', import.meta.url).href,
            state: L('商家处理退货退款申请'),
            time: (logInfo.length > 1 && logInfo[1].createTime != undefined) ? logInfo[1].createTime : '',
            state_color: 'rgba(255, 109, 31, .6)',
            time_color: 'rgba(255, 109, 31, .5)',
            line_color: 'rgba(255, 109, 31, .3)',
          });
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/buyer_future.png', import.meta.url).href,
            state: L('买家退货给商家'),
            time: (logInfo.length > 2 && logInfo[2].createTime != undefined) ? logInfo[2].createTime : '',
            state_color: 'rgba(255, 109, 31, .6)',
            time_color: 'rgba(255, 109, 31, .5)',
            line_color: 'rgba(255, 109, 31, .3)',
          });
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/received_future.png', import.meta.url).href,
            state: L('商家确认收货'),
            time: (logInfo.length > 3 && logInfo[3].createTime != undefined) ? logInfo[3].createTime : '',
            state_color: 'rgba(255, 109, 31, .6)',
            time_color: 'rgba(255, 109, 31, .5)',
            line_color: 'rgba(255, 109, 31, .3)',
          });
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/complete_current.png', import.meta.url).href,
            state: L('退款完成'),
            time: (logInfo.length > 4 && logInfo[4].createTime != undefined) ? logInfo[4].createTime : '',
            state_color: '#FF711E',
            time_color: '#FF711E',
            line_color: '#FF711E',
          });
        } else if (goods_detail_data_data.state == 203) {
          //待平台处理
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/tui_future.png', import.meta.url).href,
            state: L('买家申请退货退款'),
            time: (logInfo.length > 0 && logInfo[0].createTime != undefined) ? logInfo[0].createTime : '',
            state_color: 'rgba(255, 109, 31, .6)',
            time_color: 'rgba(255, 109, 31, .5)',
            line_color: 'rgba(255, 109, 31, .3)',
          });
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/shoper_future.png', import.meta.url).href,
            state: L('商家处理退货退款申请'),
            time: (logInfo.length > 1 && logInfo[1].createTime != undefined) ? logInfo[1].createTime : '',
            state_color: 'rgba(255, 109, 31, .6)',
            time_color: 'rgba(255, 109, 31, .5)',
            line_color: 'rgba(255, 109, 31, .3)',
          });
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/buyer_future.png', import.meta.url).href,
            state: L('买家退货给商家'),
            time: (logInfo.length > 2 && logInfo[2].createTime != undefined) ? logInfo[2].createTime : '',
            state_color: 'rgba(255, 109, 31, .6)',
            time_color: 'rgba(255, 109, 31, .5)',
            line_color: 'rgba(255, 109, 31, .3)',
          });
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/received_current.png', import.meta.url).href,
            state: L('商家确认收货'),
            time: (logInfo.length > 3 && logInfo[3].createTime != undefined) ? logInfo[3].createTime : '',
            state_color: '#FF711E',
            time_color: '#FF711E',
            line_color: '#FF711E',
          });
          return_progress_data.value.push({
            icon: new URL('@/assets/images/return/complete_pass.png', import.meta.url).href,
            state: L('退款完成'),
            time: '',
            state_color: '#999999',
            time_color: 'rgba(153, 153, 153, .5)',
            line_color: '#eee',
          });
        }
      }
      order_detail_info.value = JSON.parse(JSON.stringify(goods_detail_data.value));
    } else {
      failTip(res.msg);
    }
  };

  //关闭弹框
  const handleModalCancle = () => {
    operateData.value = [];
    modalVisible.value = false;
    title.value = ''
    type.value = ''
    modalConfirmBtnLoading.value = false
  };

  // 物流弹窗
  const checkFlow = async()=> {
    loading.value = true
    let res = await getSellerAfsGetTraceApi({ afsSn: goods_detail_data.value.afsSn })
    if(res.state == 200){
      loading.value = false
      operateData.value.push({
        type: 'show_express', //物流进度
        content: res.data,
      })
      showFoot.value = false
      width.value = 550
      title.value = L('物流信息')
      modalVisible.value = true
    }else{
      loading.value = false
      failTip(res.msg)
    }
  }

  // 退货地址弹框确定
  const sldConfirmAddress = async()=> {
    if(getSelectRowKeys().length==0){
      failTip(L('请选择收货地址'))
      return
    }
    let params = {};
    params.afsSn = route.query.afsSn;
    params.isPass = true;
    params.remark = '';
    params.storeAddressId = getSelectRowKeys()[0];
    let res = await getSellerAfterSaleAuditApi(params)
    if(res.state == 200){
      sucTip(res.msg)
      islocalModalVisible.value = false
      get_order_detail({ afsSn: route.query.afsSn })
    }else{{
      failTip(res.msg)
    }}
  }
  
  // 弹框点击确定
  const sldConfirm = async(val)=> {
    if(returnType.value == 1 || (returnType.value == 2 && !isagress.value)){
      let params = {};
      params.afsSn = route.query.afsSn;
      params.isPass = isagress.value;
      params.remark = val.remark != undefined ? val.remark : '';
      let res = await getSellerAfterSaleAuditApi(params)
      if(res.state == 200){
        sucTip(res.msg)
        modalVisible.value = false
        get_order_detail({ afsSn: route.query.afsSn })
      }else{
        failTip(res.msg)
      }
    }else if(returnType.value==2 && isagress.value){
      let params = {};
      params.afsSn = route.query.afsSn;
      params.isReceive = true;
      let res = await getSellerAfterSaleConfirmReceiveApi(params)
      if(res.state == 200){
        sucTip(res.msg)
        modalVisible.value = false
        get_order_detail({ afsSn: route.query.afsSn })
      }else{
        failTip(res.msg)
      }
    }
  }

  //加星事件
  const addStar = (e)=> {
    if (e * 1 != (goods_detail_data.value.star ? goods_detail_data.value.star * 1 : 0)) {
      operate({ orderSn: goods_detail_data.value.orderSn, star: e }, 'star');
    }
  }

  // 退款申请 type：refusegoods 拒绝 agreegoods同意
  const agreeReturn = (type,return_type)=> {
    operateData.value = []
    let agressNum = 0, isunlocal = false;
    if(type == 'agreegoods'){
      let obj = []
      obj.push({
        type: 'show_content', //展示文本内容
        name: '',
        label: L('退款方式'), //文本内容
        content: goods_detail_data.value.returnTypeValue,
      })
      obj.push({
        type: 'show_content', //展示文本内容
        name: '',
        label: L('退款金额(元)'), //文本内容
        content:goods_detail_data.value.returnMoneyAmount + '元',
      })
      obj.push({
        type: 'onlytxt', //提示
        label: '', //
        content: `*${goods_detail_data.value.returnMethod}`,
        fontSize: '12px', //字体大小
        fontColor: 'rgba(255, 109, 31, 0.8)', //字体颜色
        right: 18,
        bgcColor: '#fff',
      })
      operateData.value = obj
      agressNum = 1
      isunlocal = true;
      title.value = L('处理退款申请')
      modalVisible.value = isunlocal && !modalVisible.value
      showFoot.value = true
      isagress.value = true
      returnType.value = return_type
    }else if(type == 'refusegoods'){
      let obj = []
      obj.push({
        type: 'show_content', //展示文本内容
        name: '',
        label: L('退款方式'), //文本内容
        content: goods_detail_data.value.returnTypeValue,
      })
      obj.push({
        type: 'show_content', //展示文本内容
        name: '',
        label: L('退款金额(元)'), //文本内容
        content:goods_detail_data.value.returnMoneyAmount,
      })
      obj.push({
        type: 'textarea',
        label: L('拒绝理由'), //textarea输入框
        name: 'remark',
        placeholder: L('请输入拒绝理由，最多20个字'),
        maxLength: 20, //最多字数
        disable: false, //是否禁用
        showCount: false, //是否展示字数
        rules: [{
          required: true,
          whitespace: true,
          message: L('请输入拒绝理由'),
        }],
      })
      operateData.value = obj
      agressNum = 0;
      isunlocal = true;
      isagress.value = false
      title.value = L('处理退款申请')
      modalVisible.value = isunlocal && !modalVisible.value
      showFoot.value = true
      returnType.value = return_type
    }else if(type == 'agreelocal'){
      islocalModalVisible.value = true
      setTimeout(()=>{
        setSelectedRowKeys(selectedRowKeys.value)
      })
    }
  }

  // 退货地址弹框取消
  const onhandleModalVisible = ()=>{
    islocalModalVisible.value = false
  }

  //备注事件
  const addOrderRemark = ()=> {
    operateData.value = order_return_data.value
    if (goods_detail_data.value.storeRemark) {
      operateData.value[0].initialValue = goods_detail_data.value.storeRemark;
    }
    width.value = 500
    title.value = L('商家订单备注')
    type.value = 'remark'
    modalVisible.value = true
  }

  //操作  remark:添加订单备注 star:设置订单星级 modifyPrice:未支付修改价格
  const operate = async(id, type)=> {
    
  }

  //获取买家退货地址
  const get_address_list = async (params) => {
    let res = await getSellerAddressListApi({ ...params, type: 2});
    if (res.state == 200) {
       //设置默认选中的数据
       for (let i in res.data.list) {
          if (res.data.list[i].isDefault == 1) {
            selectedRows.value = [res.data.list[i]];
            selectedRowKeys.value = [res.data.list[i].addressId];
            break;
          }
        }
        addressList.value = res.data.list
    }else{
      failTip(res.msg)
    }
  };

  onMounted(() => {
    loading.value = true;
    get_order_detail({ afsSn: route.query.afsSn });
    get_address_list({ pageSize: list_com_page_more })
  });
</script>
<style lang="less">
  @import './style/order.less';

  .business_load_img {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    margin: auto;

    img {
      max-width: 100%;
      max-height: 100%;
    }
  }
  .sld_modal_top_tip_box{
    .sld_modal_top_tip {
      position: absolute;
      z-index: 9999;
      top: 42px;
      left: 0;
      width: 100%;
      margin-bottom: 8px;
      padding: 6px 15px;
      background-color: #fff9e2;
    }
  }
  .form_detail{
    .ant-form-item{
      margin-bottom: 6px !important;
    }
    .ant-form-item-explain{
      position: absolute;
      z-index: 2;
      top: -10px;
      right: 5%;
      min-height: 20px;
      background: #fff;
      font-size: 13px;
      text-align: left;
    }
  }

</style>
