.order_list {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  flex: 1;

  .pagination {
    text-align: right;
    width: 100%;
    margin-top: 15px;
  }

  .header {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    height: 40px;
    background: #F8FAFF;
    border-radius: 3px;
    border: 1px solid rgba(0, 0, 0, .05);
    margin-bottom: 20px;

    li {
      color: #333;
      font-size: 14px;
    }
  }

  .order_content {
    width: 100%;

    .item:first-child {
      border-top-width: 1px;
    }

    .item:last-child {
      border-bottom-width: 1px;
    }

    .item {
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      border-color: rgba(0, 0, 0, .05);
      border-width: 0 1px 0 1px;
      border-style: solid;

      .order_info {
        height: 40px;
        background: #F8FAFF;
        padding: 0 13px;
        width: 100%;

        .left {
          .num {
            color: #999;
            font-size: 12px;
            margin-right: 20px;
          }

          .order_sn {
            color: #666;
            font-size: 12px;
          }
        }

        .create_time {
          color: #999;
          font-size: 12px;
        }
      }

      .order_goods_part {
        width: 100%;

        .goods_split {
          position: relative;
        }

        .goods_split:after {
          position: absolute;
          z-index: 3;
          top: 0;
          right: 0;
          bottom: 0;
          content: '';
          transform: scaleY(0.85);
          border-right: 1px solid rgba(0, 0, 0, .05)
        }

        .goods {
          .goods_item {
            .goods_img_wrap {
              width: 82px;
              height: 82px;
              background: #fff;
              border: 1px solid rgba(0, 0, 0, .05);
              flex-shrink: 0;
              margin: 15px 10px 15px 15px;

              img {
                max-width: 100%;
                max-height: 100%;
              }
            }

            .goods_info {
              .goods_name {
                color: #333;
                font-size: 14px;
                line-height: 20px;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                /*! autoprefixer: off */
                -webkit-box-orient: vertical;
                word-break: break-word;
                height: 40px;
              }

              .goods_spec {
                color: #666;
                font-size: 12px;
                line-height: 14px;
                margin-top: 12px;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                /*! autoprefixer: off */
                -webkit-box-orient: vertical;
                white-space: nowrap;
              }
            }

            .goods_price {
              color: #333;
              font-size: 12px;
            }

            .buy_num {
              color: #333;
              font-size: 12px;
            }
          }
        }

        .member_info {
          .mem_name {
            color: #333;
            font-size: 12px;
            padding: 0 5px;
            word-break: break-word;
          }

          .mem_tel, .mem_email {
            color: #666;
            font-size: 12px;
            padding: 0 5px;
            word-break: break-word;
          }
        }

        .order_state {
          color: #666;
          font-size: 12px;
        }

        .pay_amount {
          color: #FF1818;
          font-size: 12px;
          font-weight: bold;
        }
      }
    }
  }

  .center {
    text-align: center;
  }

  .width_10 {
    width: 10%;
    padding: 5px;
  }

  .width_40 {
    width: 40%;
    padding: 5px;
  }

  .width_60 {
    width: 60%;
    padding: 5px;
  }

  .pl_100 {
    padding-left: 100px;
  }
}

.sld_common_title {
  margin-top: 15px;
  margin-bottom: 15px;
  margin-left: 5px;
  color: rgb(51 51 51);
  font-size: 14px;
  font-weight: bold;
}

.spell_order_detail {
  .ant-table-body{
    overflow: auto !important;
    height: auto !important;
  }
  .ant-descriptions-item-label {
    width: 20%;
  }

  .ant-descriptions-item-content {
    width: 30%;
  }

  .invoice_info_box {
    .ant-descriptions-item-content {
      width: 80%;
    }
    .invoice_info_box_img{
      overflow: hidden;
      width: 100px;
      height: 100px;
      background: #f8f8f8;
      margin-right: 10px;
    }
  }

  .order_detail_total {
    width: 99%;
    height: 45px;
    border-radius: 6px;
    background: #fff;
    box-shadow: 0 0 8px 0 rgb(153 153 153 / 5%), 0 0 8px 0 rgb(153 153 153 / 5%);
    font-size: 14px;
    font-weight: bold;

    .amount_detail {
      color: #333;
    }

    .amount_total {
      color: #ff2b4e;
    }
  }
}


/*退货详情样式-start*/
.progress {
  margin-top: 50px;

  .item {
    .top {
      position: relative;
      width: 215px;

      img {
        width: 75px;
        height: 75px;
      }

      .left_line, .right_line {
        position: absolute;
        content: '';
        width: 50px;
        height: 0;
        border-top: 1px solid rgba(255, 109, 31, 1);
        top: 50%;
      }

      .center_line {
        width: 70px;
        height: 70px;
        border-radius: 35px;
      }

      .left_line {
        left: 0;
      }

      .right_line {
        right: 0;
      }
    }

    .state {
      font-size: 14px;
      margin-top: 10px;
    }

    .time {
      font-size: 14px;
    }
  }

  .item.cur {
    .state {
      color: rgba(255, 109, 31, 1);
    }

    .time {
      color: rgba(255, 109, 31, .5);
    }

    .left_line, .right_line {
      border-color: rgba(255, 109, 31, 1);
    }
  }

  .item.no {
    .state {
      color: #999;
    }

    .left_line, .right_line {
      border-color: #EEE;
    }
  }

  .item.pass {
    .state {
      color: rgba(255, 109, 31, .5);
    }

    .time {
      color: rgba(255, 109, 31, .3);
    }

    .left_line, .right_line {
      border-color: rgba(255, 109, 31, .3);
    }
  }
}

.state_part {
  margin-top: 50px;
  margin-bottom: 40px;

  .title {
    color: #333;
    font-size: 26px;
    margin-bottom: 5px;
  }

  .tip {
    color: #999;
    font-size: 14px;
  }
}

.btnsty {
  display: flex;
  flex-direction: row;
  justify-content: space-around;

  .agree_btn {
    height: 36px;
    line-height: 36px;
    text-align: center;
    border-radius: 3px;
    color: #fff;
    font-size: 14px;
    margin-top: 15px;
    padding: 0 10px;
    margin-right: 20px;
    background: #FF711E;
    cursor: pointer;
  }

  .refuse_btn {
    height: 36px;
    line-height: 36px;
    text-align: center;
    border: 1px solid #FF711E;
    border-radius: 3px;
    color: #FF711E;
    font-size: 14px;
    margin-top: 15px;
    padding: 0 10px;
    cursor: pointer;
  }

  .agree_btnnon {
    height: 36px;
    line-height: 36px;
    text-align: center;
    border: 1px solid #DDDDDD;
    border-radius: 3px;
    color: #BBBBBB;
    font-size: 14px;
    margin-top: 15px;
    padding: 0 10px;
    margin-right: 20px;
  }

  .lock_agree_btn {
    width: 120px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    border: 1px solid #DDDDDD;
    border-radius: 3px;
    color: #BBBBBB;
    font-size: 14px;
    margin-top: 15px;
    padding: 0 10px;
    margin-right: 20px;
  }

  .lock_refuse_btn {
    width: 120px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    border-radius: 3px;
    color: #fff;
    font-size: 14px;
    margin-top: 15px;
    padding: 0 10px;
    background: #DDDDDD;
  }

  .cancle_btn {
    height: 36px;
    line-height: 36px;
    text-align: center;
    border: 1px solid #FF711E;
    border-radius: 3px;
    color: #FF711E;
    font-size: 14px;
    margin-top: 15px;
    margin-right: 20px;
    padding: 0 10px;
    cursor: pointer;
  }

  .deliver_btn {
    width: 120px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    border-radius: 3px;
    color: #fff;
    font-size: 14px;
    margin-top: 15px;
    padding: 0 10px;
    background: #FF711E;
    cursor: pointer;
  }
}

/*退货详情样式-end*/

.operaBtn {
  width: 110px;
  height: 25px;
  text-align: center;
  line-height: 25px;
  color: #EB6100;
  border-radius: 5px;
  border: 1px solid #EB6100;
  margin-bottom: 5px;
}


.order_goods_part {
  width: 100%;

  .operate {
    .operate_btn {
      border: 1px solid rgba(0, 0, 0, .2);
      border-radius: 3px;
      color: #666;
      font-size: 12px;
      width: 85px;
      height: 26px;
      text-align: center;
      line-height: 26px;
      margin-top: 11px;
      cursor: pointer;
    }

    .operate_btn:first-child {
      margin-top: 0;
    }

    .operate_btn:hover, .operate_btn:active {
      border-color: #EB6100;
      color: #EB6100;
    }
  }
}

.goods_info {
  .goods_detail {
    height: 80px;
    margin-left: 10px;
    flex: 1;
  }

  .goods_img {
    width: 80px;
    height: 80px;
    background: rgba(248, 248, 248, 1);
    border: 1px solid rgba(226, 229, 246, 1);
    border-radius: 3px;
    overflow: hidden;
    display: inline-block;
  }

  .goods_name {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    /*! autoprefixer: off */
    -webkit-box-orient: vertical;
    word-break: break-word;
    color: #333;
    font-size: 14px;
    margin-top: 10px;
    line-height: 17px;
    text-align: left;
    height: 34px;
    flex-shrink: 0;
  }

  .goods_brief {
    color: #666;
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: left;
    width: 100%;
    display: inline-block;
  }
}

.return_of_goods{
  .ant-table-body{
    max-height: unset !important; 
    height: auto !important; 
    overflow: auto !important;
  }
}