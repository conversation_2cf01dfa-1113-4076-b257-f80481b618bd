p{
  margin-bottom: auto;
}
.order_lists_page{
  .spin_height {
    display: flex;
    flex-direction: column;
    height: calc(100vh - @header-height - 40px - 72px);
  }
  .spin_height_order_deliver {
    display: flex;
    flex-direction: column;
    height: calc(100vh - @header-height - 40px - 76px);
  }

  .ant-spin-nested-loading {
    flex: 1;

    .ant-spin-container {
      display: flex;
      flex-direction: column;
      height: 100%;
    }
  }
  .order_list {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    flex: 1;
    min-height: 200px;
    overflow-x: auto;
    overflow-y: hidden;
    .order_content_height {
      max-height: 100%;
      overflow: auto scroll;
    }
    .pagination{
      text-align: right;
      width: 100%;
      margin-top: 15px;
    }
    .header {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      width: 100%;
      height: 40px;
      background: #F5F5F5;
      border-radius: 3px;
      margin-bottom: 10px;
      position: relative;
      padding-right: 8px;
      .left_all{
        position: absolute;
        left: 13px;
        top: 12px;
      }
  
      li {
        color: #333;
        font-size: 14px;
      }
    }
  
    .order_content {
      width: 100%;
      flex: 1;
      width: 100%;
      overflow: auto;
      .item:first-child {
        margin-top: 0;
      }
  
      .item {
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        border: 1px solid #f0f0f0;
        margin-top: 10px;
  
        .order_info {
          height: 35px;
          background: #FFF1E8;
          padding: 0 13px;
          width: 100%;
  
          .left {
            margin-top: 1px;
            .num {
              color: #999;
              font-size: 12px;
              margin-right: 20px;
            }
  
            .order_sn {
              color: #666;
              font-size: 12px;
            }
            .order_type{
              position: relative;
              margin-left: 15px;
              .order_type_icon{
                height: 20px;
              }
              .order_type_text{
                position: absolute;
                top: 2px;
                left: 4px;
                font-size: 12px;
                color: #fff;
                line-height: 19px;
              }
            }
          }
  
          .create_time {
            color: #999;
            font-size: 12px;
          }
          .operate_btn{
            cursor: pointer;
            color: #FF711E;
            font-size: 12px;
            margin-right: 12px;
          }
          .star_part {
            margin-bottom: 2px;
            .star {
              display: none;
            }
          
            .add_star {
              display: inline-block;
              height: 18px;
            }
          
            &:hover {
              .star {
                display: flex;
              }
          
              .add_star {
                display: none;
              }
            }
          
            .operate_btn {
              cursor: pointer;
              color: #FF711E;
              font-size: 12px;
              margin-right: 12px;
            }
            .ant-rate{
              height: 18px;
            }
          }
          
        }
  
        .order_goods_part {
          width: 100%;
  
          .goods_split {
            position: relative;
          }
  
          .goods_split:after {
            position: absolute;
            z-index: 3;
            top: 0;
            right: 0;
            bottom: 0;
            content: '';
            transform: scaleY(0.85);
            border-right: 1px solid rgba(0, 0, 0, .05)
          }
  
          .goods {
            .goods_item {
              .goods_img_wrap {
                width: 82px;
                height: 82px;
                background: #fff;
                border: 1px solid rgba(0, 0, 0, .05);
                flex-shrink: 0;
                margin: 15px 10px 15px 15px;
  
                img {
                  max-width: 100%;
                  max-height: 100%;
                }
              }
  
              .goods_info {
                .goods_name {
                  color: #333;
                  font-size: 14px;
                  line-height: 20px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  display: -webkit-box;
                  -webkit-line-clamp: 2;
                  /*! autoprefixer: off */
                  -webkit-box-orient: vertical;
                  word-break: break-word;
                  height: 40px;
                }
  
                .goods_spec {
                  color: #666;
                  font-size: 12px;
                  line-height: 14px;
                  margin-top: 12px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  display: -webkit-box;
                  -webkit-line-clamp: 1;
                  /*! autoprefixer: off */
                  -webkit-box-orient: vertical;
                }
              }
  
              .goods_price {
                color: #333;
                font-size: 12px;
              }
  
              .buy_num {
                color: #333;
                font-size: 12px;
              }
            }
          }
  
          .member_info {
            .mem_name {
              color: black;
              font-size: 14px;
              padding: 0 5px;
              word-break: break-word;
            }
  
            .mem_tel, .mem_email {
              color: #666;
              font-size: 12px;
              padding: 0 5px;
              word-break: break-word;
            }
          }
  
          .order_state {
            color: #666;
            font-size: 12px;
          }
  
          .pay_amount {
            color: #FF1818;
            font-size: 12px;
            font-weight: bold;
          }
        }
  
        .remark {
          padding: 8px 13px;
          border-top: 1px solid #FFF1E8;
          color: #f90;
          font-size: 13px;
          width: 100%;
          .remark_tip{
            color: #7B7B7B;
            font-size: 13px;
            font-family: Microsoft YaHei;
          }
          .remark_con{
            color: #B9B9B9;
            font-size: 13px;
            font-family: Microsoft YaHei;
          }
        }
      }
    }
  
    .center{
      text-align: center;
    }
  
    .width_8 {
      width: 8%;
      padding:5px;
    }
  
    .width_10 {
      width: 10%;
      padding:5px;
    }
  
    .width_40 {
      width: 40%;
      padding:5px;
    }
  
    .width_60 {
      width: 60%;
      padding:5px;
    }
  
    .width_64 {
      width: 64%;
      padding:5px;
    }
  
    .pl_100 {
      padding-left: 100px;
    }
  }
}


/*退货详情样式-start*/
.progress {
    margin-top: 50px;

    .item {
      .top {
        position: relative;
        width: 215px;

        img {
          width: 75px;
          height: 75px;
        }

        .left_line, .right_line {
          position: absolute;
          content: '';
          width: 50px;
          height: 0;
          border-top: 1px solid rgba(255, 113, 30, 1);
          top: 50%;
        }
        .center_line{
            width: 70px;
            height: 70px;
            border-radius: 35px;
        }

        .left_line {
          left: 0;
        }

        .right_line {
          right: 0;
        }
      }

      .state {
        font-size: 14px;
        margin-top: 10px;
      }

      .time {
        font-size: 14px;
      }
    }
    .item.cur{
      .state {
        color: rgba(8, 169, 183, 1);
      }
      .time {
        color: rgba(8, 169, 183, .5);
      }
      .left_line, .right_line{
        border-color: rgba(8, 169, 183, 1);
      }
    }
    .item.no{
      .state {
        color: #999;
      }
      .left_line, .right_line{
        border-color: #EEE;
      }
    }
    .item.pass{
      .state {
        color: rgba(8, 169, 183, .5);
      }
      .time {
        color: rgba(8, 169, 183, .3);
      }
      .left_line, .right_line{
        border-color: rgba(8, 169, 183, .3);
      }
    }
  }

  .state_part {
    margin-top: 50px;
    margin-bottom: 40px;

    .title {
      color: #333;
      font-size: 26px;
    }

    .tip {
      color: #999;
      font-size: 14px;
    }
  }

  .btnsty{
      display: flex;
      flex-direction: row;
      justify-content: space-around;
      .agree_btn{
        height: 36px;
        line-height: 36px;
        text-align: center;
        border-radius: 3px;
        color: #fff;
        font-size: 14px;
        margin-top: 15px;
        padding: 0 10px;
        margin-right: 20px;
        background: rgba(8, 169, 183, 1);
      }
      .refuse_btn{
        height: 36px;
        line-height: 36px;
        text-align: center;
        border: 1px solid rgba(8, 169, 183, 1);
        border-radius: 3px;
        color: rgba(8, 169, 183, 1);
        font-size: 14px;
        margin-top: 15px;
        padding: 0 10px;
      }
      .agree_btnnon{
        height: 36px;
        line-height: 36px;
        text-align: center;
        border: 1px solid #DDDDDD;
        border-radius: 3px;
        color: #BBBBBB;
        font-size: 14px;
        margin-top: 15px;
        padding: 0 10px;
        margin-right: 20px;
      }
      .lock_agree_btn{
        width: 120px;
        height: 36px;
        line-height: 36px;
        text-align: center;
        border: 1px solid #DDDDDD;
        border-radius: 3px;
        color: #BBBBBB;
        font-size: 14px;
        margin-top: 15px;
        padding: 0 10px;
        margin-right: 20px;
      }
      .lock_refuse_btn{
        width: 120px;
        height: 36px;
        line-height: 36px;
        text-align: center;
        border-radius: 3px;
        color: #fff;
        font-size: 14px;
        margin-top: 15px;
        padding: 0 10px;
        background: #DDDDDD;
      }
      .cancle_btn{
        width: 120px;
        height: 36px;
        line-height: 36px;
        text-align: center;
        border: 1px solid rgba(255, 113, 30, 1);
        border-radius: 3px;
        color: rgba(255, 113, 30,1);
        font-size: 14px;
        margin-top: 15px;
        margin-right: 20px;
        padding: 0 10px;
        cursor: pointer;
      }
      .deliver_btn{
          width: 120px;
        height: 36px;
        line-height: 36px;
        text-align: center;
        border-radius: 3px;
        color: #fff;
        font-size: 14px;
        margin-top: 15px;
        padding: 0 10px;
        background: rgba(255, 113, 30, 1);
        cursor: pointer;
      }
  }




  /*退货详情样式-end*/


  .goods_info1{
    .goods_detail{
      height: 80px;
      margin-left: 10px;
      flex: 1;
    }
    .goods_img{
      width:80px;
      height:80px;
      background:rgba(248,248,248,1);
      border:1px solid rgba(226,229,246,1);
      border-radius:3px;
      overflow: hidden;
      display: inline-block;
    }
    .goods_name{
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      /*! autoprefixer: off */
      -webkit-box-orient: vertical;
      word-break: break-word;
      color:#333;
      font-size: 14px;
      margin-top: 10px;
      line-height: 17px;
      text-align: left;
      height: 34px;
      flex-shrink: 0;
    }
    .goods_brief{
      color: #666;
      font-size: 12px;
      margin-bottom: 11px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      text-align: left;
      width: 100%;
      display: inline-block;
    }
  }

  .operaBtn{
    width: 110px;
    height: 25px;
    text-align: center;
    line-height: 25px;
    color: #476AF0;
    border-radius: 5px;
    border: 1px solid #476AF0;
    margin-bottom: 5px;
  }


.order_goods_part {
  width: 100%;

  .operate {
    .operate_btn {
      margin: 0 auto;
      border: 1px solid rgba(0, 0, 0, .2);
      border-radius: 3px;
      color: #666;
      font-size: 12px;
      width: 85px;
      height: 26px;
      text-align: center;
      line-height: 26px;
      margin-top: 8px;
      cursor: pointer;
    }

    .operate_btn:first-child {
      margin-top: 0;
    }

    .operate_btn:hover, .operate_btn:active {
      border-color: #EB6100;
      color: #EB6100;
    }
  }
}

.goods_info{
  .goods_detail{
    height: 80px;
    margin-left: 10px;
    flex: 1;
  }
  .goods_img{
    width:80px;
    height:80px;
    background:rgba(248,248,248,1);
    border:1px solid rgba(226,229,246,1);
    border-radius:3px;
    overflow: hidden;
    display: inline-block;
  }
  .goods_name{
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    /*! autoprefixer: off */
    -webkit-box-orient: vertical;
    word-break: break-word;
    color:#333;
    font-size: 14px;
    margin-top: 10px;
    line-height: 17px;
    text-align: left;
    height: 34px;
    flex-shrink: 0;
  }
  .goods_brief{
    color: #666;
    font-size: 12px;
    margin-bottom: 11px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: left;
    width: 100%;
    display: inline-block;
  }
}

.order_detail_total{
  width: 99%;
  height: 45px;
  background: #fff;
  box-shadow:0 0 8px 0 rgba(153,153,153,0.05), 0 0 8px 0 rgba(153,153,153,0.05);
  border-radius: 6px;
  font-size: 14px;
  font-weight: bold;
  .amount_detail{
    color: #333;
  }
  .amount_total{
    color: #FF2B4E;
  }
}

:global{
  .ant-modal-confirm-info{
    .ant-modal-content{
      padding: 20px;
    }
  }
  .ant-modal-confirm-info .ant-modal-confirm-body > .anticon{
    color: #FF711E !important;
  }
}

.free_freight{
  color: @primary-color;
  margin-left: 5px;
  &:hover{
    color: @primary-color;
  }
}

.get_exter_face_sheet_btn{
  position: absolute;
  right: 10px;
}

.express_title {
  color: #333;
  font-size: 14px;
  margin-bottom: 6px;
  margin-top: 10px;
  font-weight: 600;
  padding: 10px 10px 0 20px;

  .content {
    font-size: 12px;
  }
}

.print_exterfacesheet {
  color: #ff711e;
  font-size: 13px;
  margin-bottom: -14px;
  margin-left: 10px;
  font-weight: bold;
}

.empty_express {
  width: 100%;
  height: 200px;
  margin-bottom: 70px;

  img {
    width: 80px;
  }

  p {
    color: #333;
    font-size: 14px;
    margin-top: 17px;
  }
}
.sld_modal_top_tip_box{
  .sld_modal_top_tip {
    position: absolute;
    z-index: 9999;
    top: 42px;
    left: 0;
    width: 100%;
    margin-bottom: 8px;
    padding: 6px 15px;
    background-color: #fff9e2;
  }
}