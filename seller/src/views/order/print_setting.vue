<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader
        :title="L('电子面单打印机设置')"
      />
      <Spin :spinning="loading">
        <StandardTableRow
          width="100%"
          :data="info_data"
          @callback-event="callbackEvent"
          @submit-event="submitEvent"
        />
      </Spin>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'OrderPrintSetting',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, onMounted } from 'vue';
  import { Spin } from 'ant-design-vue';
  import { sucTip, failTip } from '@/utils/utils';
  import StandardTableRow from '/@/components/StandardTableRow/index.vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { getSettingListApi } from '/@/api/common/common';
  import { getSellerExpressDetailApi,getSellerExpressSaveApi } from '/@/api/order/setting';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const expressType = ref(2)//快递类型，1-快递鸟，2-快递100

  const info_data = ref([
    {
      type: 'input',
      label: L('快递鸟id'),
      key: 'businessId',
      placeholder: L('请输入快递鸟id'),
      initValue: '',
      inputType: 'text',
      maxlength: 50,
      require:true,
      rules: [
        {
          required: true,
          whitespace: true,
          message: L('请输入快递鸟id'),
        },
      ],
    },
    {
      type: 'input',
      label: L('快递鸟key'),
      key: 'appKey',
      placeholder: L('请输入快递鸟key'),
      initValue: '',
      inputType: 'text',
      maxlength: 50,
      require:true,
      desc: L('快递鸟密钥'),
      rules: [
        {
          required: true,
          whitespace: true,
          message: L('请输入快递鸟id'),
        },
      ],
    },
    {
      type: 'radio',
      require: true,
      label: L('快递鸟接口类型'),
      key: 'requestType',
      initValue: '',
      callback: true,
      data: [{ key: 0, value: 0, title: L('免费接口') },{ key: 1, value: 1, title: L('付费接口') }],
      desc: L('若购买的套餐为免费，则选择免费接口，否则为付费接口'),
    },
    {
      type: 'input',
      label: L('打印机名称'),
      key: 'printerName',
      placeholder: L('请输入打印机名称'),
      initValue: '',
      inputType: 'text',
      maxlength: 50,
      require:true,
      desc: L('用于打印快递鸟电子面单'),
      rules: [
        {
          required: true,
          whitespace: true,
          message: L('请输入打印机名称'),
        },
      ],
    },
  ])

  const loading = ref(true);

  // 更新数据
  const callbackEvent = (item) => {
    let temp = info_data.value.filter((item) => item.key == item.key);
    if (temp.length > 0) {
      temp[0].value = item.val1;
    }
  };

  // 点击保存
  const submitEvent = async (val) => {
    try {
      loading.value = true;
      let res = await getSellerExpressSaveApi(val);
      if (res.state == 200) {
        loading.value = false;
        sucTip(res.msg);
        get_detail();
      }else{
        failTip(res.msg)
      }
    } catch (error) {}
  };

   //获取系统配置
   const getSetting = async () => {
    try {
      let res = await getSettingListApi({ str: 'express_type' });
      if (res.state == 200) {
        expressType.value = res.data[0].value
        get_detail()
      }
    } catch (error) {}
  };

  // 获取详情
  const get_detail = async()=> {
    let res = await getSellerExpressDetailApi({})
    if(res.state == 200){
      loading.value = false
      if(res.data.isHidden == 1){
        info_data.value = info_data.value.filter(item=>item.key == 'printerName');
        for (let i in info_data.value) {
          info_data.value[i].width = 300;
          info_data.value[i].value = res.data[info_data.value[i].key];
          if(expressType.value == 2 && info_data.value[i].key == 'printerName'){
            info_data.value[i].label = L('打印机设备码');
            info_data.value[i].desc = L('用于打印快递100电子面单');
          }
        }
        if (info_data.value.length > 0) {
          info_data.value.push({
            width: 300,
            type: 'button',
            showSubmit: true,
            submitText: L('保存'),
            showCancle: false,
          });
        }
      }
    }
  }


  onMounted(() => {
    getSetting()
  });
</script>
<style lang="less">
  .point_goods_list {
    .tabs_nav {
      .ant-tabs-nav {
        margin-bottom: 0;
      }
    }
  }
</style>
