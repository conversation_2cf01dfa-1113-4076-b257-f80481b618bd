<template>
  <div class="section_padding spell_order_detail">
    <div class="section_padding_back">
      <SldComHeader :title="L('订单详情')" back />
      <div style="width: 100%; height: 10px"></div>
      <Spin :spinning="loading">
        <div class="height_detail">
          <div class="flex_row_center_start progress">
            <div
              class="flex_column_start_center item"
              v-for="(item, index) in return_progress_data"
              :key="index"
            >
              <div class="top flex_row_center_center">
                <span class="left_line" :style="{ borderColor: item.line_color }"></span>
                <img :src="item.icon" alt="" />
                <span class="right_line" :style="{ borderColor: item.line_color }"></span>
              </div>
              <span class="state" :style="{ color: item.state_color }">{{ item.state }}</span>
              <span class="time" :style="{ color: item.time_color }">{{ item.time }}</span>
            </div>
          </div>
          <div class="state_part flex_column_start_center" v-if="order_detail.orderState == 0">
            <span class="title">{{ L('订单已取消') }}</span>
            <span class="tip"
              >{{ L('取消原因:') }}{{
                order_detail.refuseReason +
                (order_detail.refuseRemark ? ',' + order_detail.refuseRemark : '')
              }}</span
            >
          </div>
          <div class="state_part flex_column_start_center" v-if="order_detail.orderState == 10">
            <span class="title">{{ order_detail.orderStateValue }}</span>
            <span v-if="order_detail.pickupTime&&order_detail.deliverMethod!=1" class="time">送达时间：{{ order_detail.pickupTime }}</span>
            <div class="btnsty">
              <div class="cancle_btn" @click="agreeReturn('cancleOrder')">{{ L('取消订单') }}</div>
              <div class="deliver_btn" v-if="order_detail.orderType == 1&&order_detail.saleModel!=1" @click="agreeReturn('modifyPrice')">{{ L('修改价格') }}</div>
            </div>
          </div>
          <div
            class="state_part flex_column_start_center"
            v-if="(order_detail.orderState == 20 || order_detail.orderState == 31) && order_detail.lockState == 0"
          >
            <span class="title">{{ order_detail.orderStateValue }}</span>
            <!-- dev_o2o2-start -->
            <div class="flex_row_center_center">
              <span class="rider" v-if="order_detail.deliverMethod!=1">{{ order_detail.stockState == 1 ? '备货中' : ''}}</span>
              <span class="rider" v-if="(order_detail.isSelfDelivery == 1 || (order_detail.riderState && order_detail.riderStateValue))&&order_detail.deliverMethod!=1"
                >{{ order_detail.stockState == 1 ? '、' : '' }}{{ order_detail.isSelfDelivery == 1 ? '商家自送' : order_detail.riderStateValue }}</span>
            </div>
            <!-- dev_o2o2-end -->
            <span v-if="order_detail.pickupTime" class="time">送达时间：{{ order_detail.pickupTime }}</span>
            <div class="btnsty">
              <div class="cancle_btn" v-if="order_detail.orderState == 20 && order_detail.isShowCancelButton && order_detail.lockState == 0
              
               " @click="agreeReturn('cancleOrder')">{{ L('取消订单') }}</div>
              <div class="cancle_btn" style="color: #DDDDDD;border-color: #DDDDDD;cursor: default;"
                   v-else-if="order_detail.orderState == 20 && order_detail.isShowCancelButton && order_detail.lockState == 1 
                  
                   ">{{ L('取消订单') }}</div>
              <template v-if="order_detail.isShowDeliverButton == undefined || (order_detail.isShowDeliverButton != undefined && order_detail.isShowDeliverButton) && order_detail.lockState == 0&&order_detail.deliverMethod!=2&&order_detail.deliverMethod!=3">
                <Popconfirm
                  v-if="order_detail.isVirtualGoods == 2"
                  :title="L('该订单为虚拟商品订单，确认执行该操作吗？')"
                  @confirm="virturalGoodsOrderDeliverConfirm()"
                >
                  <div class="deliver_btn">
                    <a style="color: #fff" href="javascript:void(0)">{{ L('发货') }}</a>
                  </div>
                </Popconfirm>
                <div class="deliver_btn" v-else @click="agreeReturn('deliver')">
                  {{ L('发货') }}
                </div>
              </template>
              <template v-else-if="order_detail.isShowDeliverButton == undefined || (order_detail.isShowDeliverButton != undefined && order_detail.isShowDeliverButton) && order_detail.lockState == 1&&order_detail.deliverMethod!=2&&order_detail.deliverMethod!=3">
                <div class="deliver_btn" style="background: #DDDDDD;cursor: default;">
                  <a style="color: #fff;" href="javascript:void(0)">{{ L('发货') }}</a>
                </div>
              </template>
              
              
            </div>
          </div>
          <div
            class="state_part flex_column_start_center"
            v-if="order_detail.orderState == 20 && order_detail.lockState > 0"
          >
            <span class="title">{{ order_detail.orderStateValue }}</span>
            
          </div>
          <div class="state_part flex_column_start_center" v-if="order_detail.orderState == 30">
            <span class="title" v-if="order_detail.deliverMethod!=2&&order_detail.deliverMethod!=3">{{ L('商品已发出,等待买家收货') }}</span>
            
            <!-- dev_o2o7-start -->
            <span class="title" v-if="order_detail.deliverMethod==3">{{ order_detail.orderStateValue }}</span>
            <span v-if="order_detail.deliverMethod==3&&order_detail.riderStateValue" class="rider">{{ order_detail.riderStateValue }}</span>
            <!-- dev_o2o7-end -->
            <span v-if="order_detail.pickupTime" class="time">送达时间：{{ order_detail.pickupTime }}</span>
            
              
            
          </div>
          <div class="state_part flex_column_start_center" v-if="order_detail.orderState == 40">
            <span class="title" v-if="order_detail.deliverMethod!=3">{{ L('买家已确认收货,订单完成') }}</span>
            
            <span v-if="order_detail.pickupTime" class="time">送达时间：{{ order_detail.pickupTime }}</span>
          </div>
          <div class="state_part flex_column_start_center" v-if="order_detail.orderState == 50">
            <span class="title">{{ order_detail.orderStateValue }}</span>
            <span v-if="order_detail.pickupTime" class="time">送达时间：{{ order_detail.pickupTime }}</span>
          </div>
          <!-- 订单信息 start -->
          <div class="sld_common_title">{{ L('订单信息:') }}</div>
          <Descriptions :bordered="true" :column="2" size="middle">
            <DescriptionsItem :label='item.label' v-for="(item,index) in order_info" :key="index">
              <div v-if="item.type=='show_text'">
                <div v-if="order_detail[item.field]!=null&&order_detail[item.field].length>62">
                  <Tooltip placement="bottom">
                    <template #title>{{ order_detail[item.field] }}</template>
                    <div style="margin-left: 5px;margin-top: 3px;">
                     <span>{{ order_detail[item.field].substring(0, 61) }}...</span> 
                    </div>
                  </Tooltip>
                </div>
                <div v-else>
                  {{ order_detail[item.field]!=null&&order_detail[item.field]!='' ? order_detail[item.field] : '--' }}
                </div>
              </div>
              <div v-else-if="item.type=='show_text_edit'" class="flex_row_start_start">
                <div v-if="order_detail[item.field]!=null&&order_detail[item.field].length>62">
                  <Tooltip placement="bottomRight">
                    <template #title>{{ order_detail[item.field] }}</template>
                    <div style="margin-left: 5px;margin-top: 3px;">
                     <span>{{ order_detail[item.field].substring(0, 61) }}...</span> 
                     <img src="@/assets/images/order/edit_store_remark.png" alt="" style="width: 15px;height: 15px;margin-left: 4px;cursor: pointer;" @click="addOrderRemark">
                    </div>
                  </Tooltip>
                </div>
                <div v-else>
                  {{ order_detail[item.field]!=null ? order_detail[item.field] : '--' }}
                  <img src="@/assets/images/order/edit_store_remark.png" alt="" style="width: 15px;height: 15px;margin-left: 4px;cursor: pointer;" @click="addOrderRemark">
                </div>
              </div>
              <div class="flex_row_start_center star_part" v-if="item.type=='show_order_star'">
                <template v-if="order_detail[item.field]">
                  <div class="star add_star">
                    <Rate :value="order_detail[item.field]" style="font-size: 18px;"/>
                  </div>
                </template>
                <span v-else class="operate_btn add_star">{{ L('加星') }}</span>
                <div class="flex_row_start_center star">
                  <img src="@/assets/images/order/clear_star.png" alt="" style="width: 18px;height: 18px;margin-right: 5px;margin-top: -1px;cursor: pointer;" @click="addStar(0)">
                  <Rate :value="order_detail[item.field]" style="font-size: 18px;" @change="addStar"/>
                </div>
              </div>
            </DescriptionsItem>
          </Descriptions>
          <!-- 订单信息 end -->
          <!-- dev_supplier-start -->
          <!-- 代销&&批发店铺信息 start -->
          <div class="sld_common_title" v-if="userStore.getStoreInfo.shopType&&userStore.getStoreInfo.shopType=='3'">{{order_detail.saleModel==1?'代销':order_detail.saleModel==2 ? '批发' : ''}}店铺信息:</div>
          <Descriptions :bordered="true" :column="2" size="middle" v-if="userStore.getStoreInfo.shopType&&userStore.getStoreInfo.shopType=='3'">
            <DescriptionsItem :label='item.label' v-for="(item,index) in supplier_info" :key="index">
              <div v-if="item.type=='show_text'">
                {{ order_detail[item.field]!=null ? order_detail[item.field] : '--' }}
              </div>
            </DescriptionsItem>
          </Descriptions>
          <!-- 代销&&批发店铺信息 end -->
          <!-- dev_supplier-end -->
          <!-- 收货人信息 start -->
          <div class="sld_common_title" v-if="order_detail.isVirtualGoods == 1&&order_detail.deliverMethod!=2">{{ L('收货人信息:') }}</div>
          <Descriptions :bordered="true" :column="2" size="middle" v-if="order_detail.isVirtualGoods == 1&&order_detail.deliverMethod!=2">
            <DescriptionsItem :label='item.label' v-for="(item,index) in receiver_info" :key="index">
              <div v-if="item.type=='show_text'">
                <span v-if="item.field=='receiverAddress'">{{ order_detail.receiverAreaInfo }}&nbsp;</span>
                {{ order_detail[item.field]!=null ? order_detail[item.field] : '--' }}
              </div>
            </DescriptionsItem>
          </Descriptions>
          <!-- 收货人信息 end -->
          
          <!-- 用户预留信息 start -->
          <div
            class="sld_common_title"
            v-if="order_detail.isVirtualGoods == 2 && order_detail.orderReserveList && order_detail.orderReserveList.length > 0"
            >{{ L('用户预留信息') }}</div
          >
          <Descriptions :bordered="true" :column="2" size="middle" :class="{'invoice_info_box':reserve_info.length==1}"  v-if="order_detail.isVirtualGoods == 2 && order_detail.orderReserveList && order_detail.orderReserveList.length > 0">
            <DescriptionsItem :label='item.label' v-for="(item,index) in reserve_info" :key="index">
              <div v-if="item.type=='show_text'">
                {{ item.value }}
              </div>
            </DescriptionsItem>
          </Descriptions>
          <!-- 用户预留信息 end -->
          <!-- 发票信息 start -->
          <div class="sld_common_title" v-if="!order_detail.saleModel||(order_detail.saleModel&&order_detail.saleModel!=1)">{{ L('发票信息:') }}</div>
          <Descriptions :bordered="true" :column="2" size="middle" :class="{'invoice_info_box':invoice_info.length==1}" v-if="!order_detail.saleModel||(order_detail.saleModel&&order_detail.saleModel!=1)">
            <DescriptionsItem :label='item.label' v-for="(item,index) in invoice_info" :key="index">
              <div v-if="item.type=='show_text'">
                {{ order_detail.invoiceInfo && order_detail.invoiceInfo[item.field]!=null ? order_detail.invoiceInfo[item.field] : '--' }}
              </div>
            </DescriptionsItem>
          </Descriptions>
          <!-- 发票信息 end -->
          <!-- 更多操作日志 start -->
          <div v-if="order_detail.orderOperateList != undefined && order_detail.orderOperateList.length != undefined && order_detail.orderOperateList.length > 0 ">
            <div class="sld_common_title">{{ L('更多操作日志') }}</div>
            <BasicTable
              :maxHeight="400"
              :columns="columns_order_log"
              :bordered="true"
              :ellipsis="false"
              :pagination="false"
              :dataSource="order_detail.orderOperateList"
            >
            </BasicTable>
          </div>
          <!-- 更多操作日志 end -->
          <!-- 商品信息 start -->
          <div class="sld_common_title">{{ L('商品信息') }}</div>
          <BasicTable
            :columns="columns_order_goods"
            :bordered="true"
            :canResize="false"
            :pagination="false"
            :dataSource="order_detail.orderProductList"
            :maxHeight="400"
          >
            <template #bodyCell="{ column, record, text }">
              <template v-if="column.dataIndex == 'productImage'">
                <div class="goods_info com_flex_row_flex_start">
                  <div class="goods_img" style="border: none">
                    <Popover placement="rightTop">
                      <template #content>
                        <div style="width: 200px">
                          <img :src="text" alt="" style="max-width: 100%; max-height: 100%" />
                        </div>
                      </template>
                      <div class="business_load_img">
                        <img :src="text" alt="" />
                      </div>
                    </Popover>
                  </div>
                  <div class="com_flex_column_space_between goods_detail">
                    <div
                      class="goods_name"
                      style="max-width: 380px; margin-top: 6px; white-space: initial"
                      :title="record.goodsName"
                    >
                      {{ record.goodsName }}
                    </div>
                    <span class="goods_brief" :title="record.specValues">
                      {{ record.specValues }}
                    </span>
                  </div>
                </div>
              </template>
              <template v-else-if="column.dataIndex == 'action'">
                <TableAction
                  :actions="[
                    {
                      onClick: lookDetail.bind(null, record.afsSn),
                      ifShow:record.afsState != undefined && record.afsState,
                      label: record.afsStateValue,
                    },
                     {
                      ifShow:!(record.afsState != undefined && record.afsState),
                      label: '--',
                    },
                  ]"
                />
              </template>
            </template>
          </BasicTable>
          <div class="order_detail_total_num">
            <div class="amount_num_detail">
              <div class="amount_num_detail_item">
                <p>商品金额：</p>
                <span>{{`¥${order_detail.goodsAmount?Number(order_detail.goodsAmount).toFixed(2):'0.00'}`}}</span>
              </div>
              <template v-if="order_detail.promotionInfo != undefined && order_detail.promotionInfo.length > 0">
                <template v-for="(item, index) in order_detail.promotionInfo" :key="index">
                  <div class="amount_num_detail_item">
                    <p>
                      <span style="color: #000;">{{item.promotionName}}</span>
                      <template v-if="item.remark">
                        <Tooltip>
                          <template #title>
                            <div class="rule_list">
                              <div>该笔订单参与了：</div>
                              <div>{{item.remark}}</div>
                            </div>
                          </template>
                          <img src="/src/assets/images/pv_icon.png" alt="" style="padding: 0 3px;vertical-align: -4px;">
                        </Tooltip>
                      </template>
                      <span style="color: #000;">：</span>
                    </p>
                    <span>-{{`¥${item.discount?Number(item.discount).toFixed(2):'0.00'}`}}</span>
                  </div>
                </template>
              </template>
              <div class="amount_num_detail_item">
                <p>运费：</p>
                <span>{{`¥${order_detail.expressFee?Number(order_detail.expressFee).toFixed(2):'0.00'}`}}</span>
              </div>
            </div>
            <div class="amount_num_detail_item" style="margin-bottom: 0">
              <p class="amount_num_title">{{order_detail.orderState!=0&&order_detail.orderState!=20?'实付金额':'应付金额'}}：</p>
              <span class="amount_num_price">¥<span>{{`${order_detail.orderAmount?Number(order_detail.orderAmount).toFixed(2):'0.00'}`}}</span></span>
            </div>
          </div>
          <!-- 商品信息 end -->
          <template
            v-if="
              (order_detail.orderState == 31 ||
                order_detail.orderState == 30 ||
                order_detail.orderState == 40) &&
              order_detail.orderDeliverList != undefined &&
              order_detail.orderDeliverList.length > 0
            "
          >
            <div class="sld_common_title">{{ L('发货信息') }}</div>
            <OrderDeliverInfo :data="order_detail"></OrderDeliverInfo>
          </template>
        </div>
      </Spin>
    </div>
    <SldModal
      :width="width"
      :title="title"
      :visible="modalVisible"
      :height="600"
      :content="operateData"
      :showFoot="showFoot"
      :confirmBtnLoading="modalConfirmBtnLoading"
      @cancle-event="handleModalCancle"
      @confirm-event="sldConfirm"
      :showTopTip="showTopTip"
    />
    <Modal :title="L('商品发货')" :width="modal_width" :visible="deliverModal" @ok="deliverConfirm" @cancel="sldDeliverHandleCancle" :okText="deliverType == 3 ? L('打印并发货') : L('确定')" :confirmLoading="confirmLoading">
      <div v-if="deliverType==1" class="sld_modal_top_tip_box">
        <div class="flex_row_start_center sld_modal_top_tip">
          <div :style="{ lineHeight: '0px' }">
            <AliSvgIcon iconName="icontishi3" width="15px" height="15px" fillColor="#333333" />
          </div>
          <span :style="{ fontSize: '13px', marginLeft: '6px', color: 'rgba(0,0,0,.65)' }">{{ L('请认真填写物流公司及快递单号') }}</span>
        </div>
      </div>
      <Form layout="horizontal" ref="formRef" class="form_detail" :model="order_detail" >
        <div v-if="deliverType==1" style="width: 100%;height:32px;"></div>
        <BasicTable 
          :columns="columnDeliverData"
          :bordered="true"
          :ellipsis="false"
          :pagination="false"
          :clickToRowSelect="false"
          :canResize="false"
          :rowSelection="{ 
            type: 'checkbox', 
            selectedRowKeys: selectedRowKeys,
            onSelect: onSelect,
            onSelectAll: onSelectAll,
          }"
          rowKey="orderProductId"
          :dataSource="deliver_goods_table"
          :maxHeight="400">
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.dataIndex == 'deliverNum'">
              <InputNumber v-model:value="record.deliverNum" :min="selectedRowKeys.indexOf(record.orderProductId) > -1 ? 1 : 0" :max="record.productNum - record.shippedNum" :precision="0"></InputNumber>
            </template>
          </template>
        </BasicTable>
        <Form.Item :label="L('收货人姓名')" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}">
          <span>{{order_detail.receiverName}}</span>
        </Form.Item>
        <Form.Item :label="L('收货人电话')" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}">
          <span>{{order_detail.receiverMobile}}</span>
        </Form.Item>
        <Form.Item :label="L('收货人地址')" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}">
          <span style="word-break: break-all;">{{order_detail.receiverAreaInfo}}{{order_detail.receiverAddress}}</span>
        </Form.Item>
        <Form.Item :label="L('发货方式')" name="deliverType" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}" :rules="[{required: true,message:L('请选择发货方式')}]">
          <RadioGroup
            size="small"
            v-model:value="deliverType"
            @change="redioOnChange($event)"
          >
            <Radio :value="1" > {{ L('物流发货') }} </Radio>
            <Radio :value="3"> {{ L('电子面单') }} </Radio>
            <Radio :value="2"> {{ L('无需物流') }} </Radio>
          </RadioGroup>
        </Form.Item>
        <Form.Item :label="L('物流公司')" name="expressId" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}" :rules="[{required: true,message:L('请选择物流公司')}]" v-if="deliverType == 3">
          <Select
            :placeholder="L('请选择物流公司')"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            v-model:value="order_detail.expressId"
            @change="(e) => handleSelExpress(e)"
          >
            <Select.Option
              v-for="(item, index) in email_expressList"
              :key="index"
              :value="item.expressId"
              >{{ item.expressName }}</Select.Option
            >
          </Select> 
        </Form.Item>
        <Form.Item :label="L('物流公司')" name="expressId" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}" :rules="[{required: true,message:L('请选择物流公司')}]" v-if="deliverType == 1">
          <Select
            :placeholder="L('请选择物流公司')"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            v-model:value="order_detail.expressId"
            @change="(e) => handleSelExpress(e)"
          >
            <Select.Option
              v-for="(item, index) in expressList"
              :key="index"
              :value="item.expressId"
              >{{ item.expressName }}</Select.Option
            >
          </Select> 
        </Form.Item>
        <Form.Item :label="L('快递单号')" name="expressNumber" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}" :rules="[{required: true,message:L('请输入物流单号')},rule]" v-if="deliverType == 1" :extra="L('请输入正确的物流单号')">
          <Input :maxLength="20" :placeholder="L('请输入物流单号')" v-model:value="order_detail.expressNumber"/>
        </Form.Item>
        <Form.Item :label="L('联系人')" name="deliverName" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}" :rules="[{required: true,message:L('请输入联系人')}]" v-if="deliverType == 2">
          <Input :maxLength="10" :placeholder="L('请输入联系人')" v-model:value="order_detail.deliverName"/>
        </Form.Item>
        <Form.Item :label="L('联系方式')" name="deliverMobile" :labelCol="{span: 6,}" :wrapperCol="{span: 14,}" :rules="[{required: true,message:L('请输入联系方式')},{pattern: mobile_reg,message:L('请输入正确的手机号')}]" v-if="deliverType == 2">
          <Input :maxLength="11" :placeholder="L('请输入联系方式')" v-model:value="order_detail.deliverMobile"/>
        </Form.Item>
      </Form>
    </Modal>

    
  </div>
</template>
<script>
  export default {
    name: 'OrderListsToDetail',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, reactive, onMounted,nextTick,unref } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { Spin, Popover,Descriptions,DescriptionsItem,Tooltip,Rate,Popconfirm,Form,Modal,InputNumber,RadioGroup,Radio,Select,Input, } from 'ant-design-vue';
  import { sucTip, failTip } from '@/utils/utils';
  import { useRoute, useRouter } from 'vue-router';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { Description } from '/@/components/Description/index';
  import OrderDeliverInfo from './order_deliver_info.vue';
  import SldModal from '/@/components/SldModal/index.vue';
  import rider_icon from '/@/assets/images/order/rider.png';
  import rider_icon_icon from '/@/assets/images/order/rider_icon.png';
  import { useUserStore } from '/@/store/modules/user';
  import { selectRadio, SelectAll } from '/@/utils/utils';
  import { getAuthCache } from '/@/utils/auth';
  import { SLD_MENU_LIST_KEY } from '/@/enums/cacheEnum';
  import { h } from 'vue';
  import {
    getOrderDetailApi,
    getOrderRemarkApi,
    getOrderStarApi,
    getOrderUpdatePriceApi,
    getSettingReasonListApi,
    getOrderInfoCancelApi,
    getOrderInfoOrderProductListApi,
    getOrderInfoExpressListApi,
    getOrderInfoDeliverApi,
    
    
  } from '/@/api/order/order';
  import { list_com_page_more } from '/@/utils/utils';
  import { useGlobSetting } from '/@/hooks/setting';
  import { useScript } from '/@/hooks/web/useScript';

  const { apiUrl, 
    
   } = useGlobSetting();

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  


  const route = useRoute();
  const formRef = ref();
  const router = useRouter();
  const userStore = useUserStore();
  const rule = ref({pattern: /^[0-9a-zA-Z]*$/g,message:L('请输入正确的单号')})
  const mobile_reg = ref(/(1[3-9]\d{9}$)/)
  const loading = ref(true);
  const reserve_info = ref([]); //用户预留信息
  const query = ref(route.query);
  const order_detail = ref({
    invoiceInfo:{}
  });
  const confirmLoading = ref(false);//发货弹窗确认按钮loading
  const return_progress_data = ref([]); //退货进度条
  const invoice_info = ref([
    {
      type: 'show_text',
      field: 'invoiceTitle',
      label: L('单位名称'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      type: 'show_text',
      field: 'taxCode',
      label: L('税号'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      type: 'show_text',
      field: 'receiverEmail',
      label: L('收票邮箱'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
  ]); //公司——普通发票
  // dev_supplier-start
  // 代发&&批发店铺信息
  const supplier_info = ref([
    {
      type: 'show_text',
      field: 'storeName',
      label: L('店铺名称'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      type: 'show_text',
      field: 'vendorName',
      label: L('店铺账号'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      type: 'show_text',
      field: 'vendorMobile',
      label: L('联系人电话'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
  ]);
  // dev_supplier-end
  const receiver_info = ref([
    {
      type: 'show_text',
      field: 'memberName',
      label: L('会员名称'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      type: 'show_text',
      field: 'receiverName',
      label: L('收货人'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      type: 'show_text',
      field: 'receiverMobile',
      label: L('收货人手机号'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      type: 'show_text',
      field: 'receiverAddress',
      label: L('收货地址'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
  ]);
  
  const order_info = ref([
    //订单信息
    {
      type: 'show_text',
      field: 'orderTypeValue',
      label: L('订单类型'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      type: 'show_text',
      field: 'orderSn',
      label: L('订单号'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    
    {
      type: 'show_text',
      field: 'paymentName',
      label: L('支付方式'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      type: 'show_text',
      field: 'tradeSn',
      label: '支付流水号',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      type: 'show_text',
      field: 'orderRemark',
      label: L('订单备注'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      type: 'show_text_edit',
      field: 'storeRemark',
      label: L('商家备注'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      type: 'show_order_star',
      field: 'star',
      label: L('订单星级'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
  ]);
  const modalVisible = ref(false);
  const showFoot = ref(true);
  const width = ref(500)
  const showTopTip = ref('')
  const modalConfirmBtnLoading = ref(false);
  const title = ref('')
  const order_detail_info = ref({})
  const orderLogList = ref([]);
  const operateData = ref([]); //弹框操作数据
  const type = ref('')
  const resList = ref([]); // 取消原因数据
  const show_foot = ref(true);
  const modal_width = ref(700);
  const propType = ref('');
  const deliverModal = ref(false);
  const expressList = ref([]); //快递公司数据
  const deliverType = ref(1); //发货方式
  const deliver_goods_table = ref([]);
  const email_expressList = ref([]);//电子面单物流公司列表
  const exterFaceSheet = ref('');//通过电子面单获取的物流单号
  const isAllowApplyExpressNum = ref(true);//是否允许通过电子面单申请物流单号,默认允许
  const showPrintExterFaceSheetBtn = ref(false);//是否展示打印电子面单按钮
  const selectedRowKeys = ref([])
  const selectedRows = ref([])
  const columnDeliverData = ref([
    {
      title: L('规格型号'),
      dataIndex: 'specValues',
      align: 'center',
      width: 100,
      customRender: ({ text }) => {
        return text ? text : L('默认')
      },
    },
    {
      title: L('产品名称'),
      dataIndex: 'goodsName',
      align: 'center',
      width: 100,
    },
    {
      title: L('下单数量'),
      dataIndex: 'productNum',
      align: 'center',
      width: 100,
    },
    {
      title: L('已发数量'),
      dataIndex: 'shippedNum',
      align: 'center',
      width: 100,
    },
    {
      title: L('本次发货数量'),
      dataIndex: 'deliverNum',
      align: 'center',
      width: 100,
    },
  ])
  const columns_order_log = ref([
    {
      title: L('操作方'),
      dataIndex: 'logRole',
      align: 'center',
      width: 100,
      customRender: ({ text }) => {
        let con = '';
        if (text == 1) {
          con = L('系统管理员');
        } else if (text == 2) {
          con = L('商家');
        } else if (text == 2) {
          con = L('会员');
        }
        return con;
      },
    },
    {
      title: L('操作人'),
      dataIndex: 'logUserName',
      align: 'center',
      width: 100,
    },
    {
      title: L('操作时间'),
      dataIndex: 'logTime',
      align: 'center',
      width: 100,
    },
    {
      title: L('操作内容'),
      dataIndex: 'logContent',
      align: 'center',
      width: 100,
    },
  ])
  const orderRemarkData = ref([
    {
      type: 'textarea',
      label: L('订单备注'),
      name: 'remark',
      placeholder: L('请输入订单备注信息'),
      extra: L('最多输入100字'),
      maxLength: 100,
      initialValue: '',
    },
  ])
  const columns_order_goods = ref([
    {
      type: 'show_text',
      title: L('商品信息'),
      dataIndex: 'productImage',
      align: 'center',
      width: 500,
    },
    {
      type: 'show_text',
      title: L('单价(元)'),
      dataIndex: 'productShowPrice',
      align: 'center',
      width: 100,
    },
    {
      type: 'show_text',
      title: L('数量'),
      dataIndex: 'productNum',
      align: 'center',
      width: 100,
    },
  ]);
  const invoice_info_other = ref([
    //不需要发票的情况
    {
      type: 'show_text',
      field: 'invoiceStatus',
      label: L('是否需要开票'),
      labelMinWidth: 10,
      contentMinWidth: 100,
      render: (val, data) => {
        return L('否');
      },
    },
  ]);
  const invoice_info_personal = ref([
    {
      type: 'show_text',
      field: 'invoiceTitle',
      label: L('发票抬头'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      type: 'show_text',
      field: 'receiverEmail',
      label: L('收票邮箱'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
  ]); //个人发票
  const invoice_info_VAT = ref([
    {
      type: 'show_text',
      field: 'invoiceTitle',
      label: L('单位名称'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      type: 'show_text',
      field: 'taxCode',
      label: L('税号'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      type: 'show_text',
      field: 'registerAddr',
      label: L('注册地址'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      type: 'show_text',
      field: 'registerPhone',
      label: L('注册电话'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      type: 'show_text',
      field: 'bankName',
      label: L('开户银行'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      type: 'show_text',
      field: 'bankAccount',
      label: L('银行账户'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      type: 'show_text',
      field: 'receiverName',
      label: L('收票人'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      type: 'show_text',
      field: 'receiverMobile',
      label: L('收票电话'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      type: 'show_text',
      field: 'receiverAddress',
      label: L('收票地址'),
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
  ]); //公司发票——增值税发票

  const redioOnChange = (e)=> {
    order_detail.value.expressId = undefined
  }
  //关闭弹框
  const handleModalCancle = () => {
    operateData.value = [];
    modalVisible.value = false;
    title.value = ''
    type.value = ''
    modalConfirmBtnLoading.value = false
  };

  // 弹框点击确定
  const sldConfirm = (val)=> {
    val.orderSn = order_detail.value.orderSn;
    operate(val,type.value)
  }

  //加星事件
  const addStar = (e)=> {
    if (e * 1 != (order_detail.value.star ? order_detail.value.star * 1 : 0)) {
      operate({ orderSn: order_detail.value.orderSn, star: e }, 'star');
    }
  }

  //虚拟订单发货
  const virturalGoodsOrderDeliverConfirm = async()=> {
    let res = await getOrderInfoDeliverApi({ orderSn: order_detail.value.orderSn })
    if(res.state == 200){
      sucTip(res.msg)
      userStore.setDelKeepAlive(['OrderOrderLists'])
      get_order_detail({ orderSn: route.query.orderSn })
    }else{
      failTip(res.msg)
    }
  }

  

  // 发货弹框点击确定
  const deliverConfirm = ()=> {
    if (selectedRowKeys.value.length == 0) {
      //需要验证是否选中产品
      failTip(L('请选择本次发货的产品～'));
      return false;
    }
    formRef.value
      .validate()
      .then(async(values) => {
        values.deliverType = deliverType.value
        values.orderSn = order_detail.value.orderSn
        let orderProduct = [];
        deliver_goods_table.value.forEach(item => {
          if (selectedRowKeys.value.indexOf(item.orderProductId) > -1) {
            orderProduct.push(`${item.orderProductId}-${item.deliverNum}`);
          }
        });
        values.orderProductInfo = orderProduct.join(',');
        confirmLoading.value = true
        let res = await getOrderInfoDeliverApi(values)
        if(res.state == 200){
          confirmLoading.value = false
          userStore.setDelKeepAlive(['OrderOrderLists'])
          sucTip(res.msg)
          if (values.deliverType == 3 && res.data.url) {
            //电子面单发货成功需要打印电子面单
            window.open(res.data.url, '_blank');
          }
          get_order_detail({ orderSn: route.query.orderSn })
          sldDeliverHandleCancle()
        }else{
          confirmLoading.value = false
          failTip(res.msg)
        }
      })
  }

  // 发货弹框点击取消
  const sldDeliverHandleCancle = ()=> {
    deliverModal.value = false
    formRef.value.resetFields();
  }

  // 弹窗
  const agreeReturn = async(type_info)=> {
    operateData.value = []
    let cancleBool = false, deliverBool = false;
    if(type_info=='cancleOrder'){
      modalConfirmBtnLoading.value = false
      showTopTip.value = L('取消订单后，订单将自动关闭')
      // @ts-ignore
      operateData.value.push({
        type: 'select',
        label: L('取消理由'),
        name: 'reasonId',
        placeholder: L('请选择取消理由'),
        selData: resList.value,
        selKey:'reasonId',
        selName:'content',
        diy:true,
        rules: [
          {
            required: true,
            message: L('请选择取消理由'),
          },
        ]
      })
      operateData.value.push({
        type: 'textarea',
        label: L('取消备注'),
        name: 'remark',
        placeholder: L('请输入取消备注，最多50个字'),
        maxLength: 50,
        initialValue: '',
      })
      title.value = L('取消订单')
      width.value = 600
      showFoot.value = true
      modalVisible.value = true
      type.value = type_info
    }else if(type_info == 'deliver'){
      showTopTip.value = L('取消订单后，订单将自动关闭')
      modal_width.value = 900
      let res = await getOrderInfoOrderProductListApi({orderSns: order_detail.value.orderSn})
      if(res.state == 200){
        userStore.setDelKeepAlive(['OrderOrderLists'])
        deliver_goods_table.value = JSON.parse(JSON.stringify(res.data));
      }else{
        failTip(res.msg)
      }
      let res_one = await getOrderInfoExpressListApi({pageSize: list_com_page_more, expressState: 1 })
      if(res_one.state == 200){
        deliverBool = true;
        expressList.value  = JSON.parse(JSON.stringify(res_one.data.list));
        email_expressList.value =  JSON.parse(JSON.stringify(res_one.data.list.filter(item => item.isSupportFaceSheet == 1)));
        propType.value = type_info
        show_foot.value = true
        exterFaceSheet.value =  order_detail.value.expressNumber ? order_detail.value.expressNumber : '',
        deliverModal.value = deliverBool && !deliverModal.value
        isAllowApplyExpressNum.value = order_detail.value.expressNumber ? false : true//是否允许通过电子面单申请物流单号
        showPrintExterFaceSheetBtn.value  = order_detail.value.expressNumber ? true : false
      }
    }else if(type_info == 'modifyPrice'){
      modalConfirmBtnLoading.value = false
      showTopTip.value = L('只有订单未付款时才支持改价')
      // @ts-ignore
      operateData.value.push({
        type: 'inputnum',
        label: L('订单价格(¥)'),
        name: 'moneyAmount',
        extra: L('不含运费'),
        min: 0.01,
        max: 9999999.99,
        precision: 2,
        initialValue: order_detail.value.moneyAmount,
        rules: [
          {
            required: true,
            message: L('订单价格不可为空'),
          },
        ],
      },
      {
        type: 'inputnum',
        label: L('运费(¥)'),
        name: 'expressFee',
        min: 0,
        max: 9999999.99,
        precision: 2,
        initialValue: order_detail.value.expressFee,
        avoid:L('免运费'),
        avoid_width:'80px'
      })
      title.value = L('修改价格')
      width.value = 400
      showFoot.value = true
      modalVisible.value = true
      type.value = type_info
    }else if(type_info == 'flow'){
      Modal.info({
        width:470,
        title: L('配送信息'),
        content: h('div', {}, [
          h('p', `${L('配送人：')}${order_detail.value.deliverName}`),
          h('p', `${L('配送人手机号：')}${order_detail.value.deliverMobile}`),
        ]),
        onOk() {
        },
      });
    }
  }

  // 单选多选事件
  function onSelect(record, selected) {
    let rows = selectRadio(
      selectedRowKeys.value,
      selectedRows.value,
      'orderProductId',
      record,
      selected,
    );
    selectedRowKeys.value = rows.selectedRowKeys;
    selectedRows.value = rows.selectedRows;
    deliver_goods_table.value.forEach(item=>{
      let index =  selectedRowKeys.value.indexOf(item.orderProductId)
      if(index!=-1){
        item.deliverNum = item.deliverNum<1 ? 1 :item.deliverNum
      }
    })
  }

  // 全选按钮事件
  function onSelectAll(selected, rows, changeRows) {
    let rowAll = SelectAll(
      selectedRowKeys.value,
      selectedRows.value,
      'orderProductId',
      selected,
      rows,
      changeRows,
    );
    selectedRowKeys.value = rowAll.selectedRowKeys;
    selectedRows.value = rowAll.selectedRows;
    deliver_goods_table.value.forEach(item=>{
      let index =  selectedRowKeys.value.indexOf(item.orderProductId)
      if(index!=-1){
        item.deliverNum = item.deliverNum<1 ? 1 :item.deliverNum
      }
    })
  }

  //备注事件
  const addOrderRemark = ()=> {
    operateData.value = orderRemarkData.value
    if (order_detail.value.storeRemark) {
      operateData.value[0].initialValue = order_detail.value.storeRemark;
    }
    width.value = 500
    title.value = L('商家订单备注')
    type.value = 'remark'
    modalVisible.value = true
  }

  //操作  remark:添加订单备注 star:设置订单星级 modifyPrice:未支付修改价格
  const operate = async(id, type)=> {
    modalConfirmBtnLoading.value = true
    let res;
    let param_data = {};
    if (type == 'remark') {
      param_data = id;
      res = await getOrderRemarkApi(param_data);
    } else if (type == 'star') {
      param_data = id;
      res = await getOrderStarApi(param_data);
    } else if (type == 'modifyPrice') {
      param_data = id;
      res = await getOrderUpdatePriceApi(param_data);
    } else if (type == 'cancleOrder') {
      param_data = id;
      res = await getOrderInfoCancelApi(param_data);
    } 
    
    if(res.state == 200){
      userStore.setDelKeepAlive(['OrderOrderLists'])
      sucTip(res.msg)
      modalConfirmBtnLoading.value = false
      showTopTip.value = ''
      get_order_detail({ orderSn: route.query.orderSn })
      handleModalCancle()
    }else{
      failTip(res.msg)
      modalConfirmBtnLoading.value = false
    }
  }

  // 详情数据
  const get_order_detail = async (params) => {
    let res = await getOrderDetailApi(params);
    if (res.state == 200) {
      loading.value = false;
      order_detail.value = res.data;
      orderLogList.value = res.data.orderLogs;
      //收票信息
      if (order_detail.value.invoiceStatus == 1) {
        let invoice_type = '';
        if (order_detail.value.invoiceInfo.titleType == 1) {
          //个人发票
          invoice_info.value = invoice_info_personal.value;
          invoice_type = L('个人发票');
          order_detail.value.invoiceInfo.invoiceTypeCombine = L('个人发票')
        } else {
          //公司发票
          if (order_detail.value.invoiceInfo.invoiceType != 1) {
            //增值税发票
            invoice_info.value = invoice_info_VAT.value;
            invoice_type = L('增值税专用发票');
            order_detail.value.invoiceInfo.invoiceTypeCombine = L('增值税专用发票')
          } else {
            invoice_type = L('普通发票');
            order_detail.value.invoiceInfo.invoiceTypeCombine = L('普通发票')
          }
        }

        //需要发票
        for (let item in invoice_info.value) {
          let name = invoice_info.value[item].field;
          invoice_info.value[item].render = (val, data) => {
            return !order_detail.value['invoiceInfo'][name]
              ? '--'
              : order_detail.value['invoiceInfo'][name];
          };
        }
        let invoice_content =
          order_detail.value.invoiceInfo.invoiceContent == 1 ? L('商品明细') : L('商品类别');
          order_detail.value.invoiceInfo.invoiceContentValue = invoice_content
        invoice_info.value = [
          {
            type: 'show_text',
            field: 'invoiceTypeCombine',
            label: L('发票类型'),
            labelMinWidth: 10,
            contentMinWidth: 100,
          },
          {
            type: 'show_text',
            field: 'invoiceContentValue',
            label: L('发票内容'),
            labelMinWidth: 10,
            contentMinWidth: 100,
          },
          ...invoice_info.value,
        ];
      } else {
        //不需要发票
        invoice_info.value = invoice_info_other.value;
        order_detail.value.invoiceInfo = {
          invoiceStatus:L('否')
        }
      }
      //收货人信息
      for (let item in receiver_info.value) {
        if (receiver_info.value[item].field == 'receiverAddress') {
          receiver_info.value[item].render = (val, data) => {
            return order_detail.value.receiverAreaInfo + ' ' + order_detail.value.receiverAddress;
          };
        } else {
          receiver_info.value[item].render = (val, data) => {
            return order_detail.value[receiver_info.value[item].field];
          };
        }
      }
      
      //订单信息
      // dev_supplier-start
      if(userStore.getStoreInfo.shopType&&userStore.getStoreInfo.shopType=='3'){
        let order_info_temp = order_info.value.filter(item=>item.field == 'orderTypeValue')
        if(order_info_temp.length>0){
          order_info_temp[0].field = 'saleModelValue'
        }
        for(let item in order_info.value){
          if (order_info.value[item].field == 'deliverMethodValue') {
            order_info.value.splice(item,1)
            break
          }
        }
      }
      if( order_detail.value.saleModel&&order_detail.value.saleModel == 1){
        for(let item in order_info.value){
          if (order_info.value[item].field == 'paymentName') {
            order_info.value.splice(item,2)
            break
          }
        }   
      }
      // dev_supplier-end
      for (let item in order_info.value) {
        if (order_info.value[item].field == 'orderTypeValue') {
          let orderType = order_detail.value[order_info.value[item].field]
            ? `${order_detail.value[order_info.value[item].field]}${L('订单')}${
                order_detail.value.isVirtualGoods == 2 ? '、' + L('虚拟订单') : ''
              }`
            : order_detail.value.isVirtualGoods == 2
            ? L('虚拟订单')
            : L('普通订单');
          order_detail.value.orderTypeValue = orderType
        } else if(order_info.value[item].field == 'deliverMethodValue'){
          if (order_detail.value.deliverMethod == 1) {
            order_detail.value[order_info.value[item].field] = order_detail.value.deliverType == 2 || order_detail.value.deliverType == 0 ? '无需物流' : order_detail.value.deliverType == 3
              ? '电子面单' : order_detail.value.deliverType == 4 ? '供应商配送' : '物流发货'
          } else {
            order_detail.value[order_info.value[item].field] =
              order_detail.value.deliverMethod == 2 ? '上门自提' :
              
              '快递发货'
          }
        }
        // dev_supplier-start
        if(order_info.value[item].field == 'saleModelValue'){
          let val = order_detail.value.saleModel&&order_detail.value.saleModel == 1 ? '代发订单' : order_detail.value.saleModel&&order_detail.value.saleModel == 2 ? '批发订单' : '--'
          order_detail.value.saleModelValue =  order_detail.value.isVirtualGoods == 2
            ? `虚拟商品订单${val&&val!='--'?'、'+val:''}`
            : `实物商品订单${val&&val!='--'?'、'+val:''}`;
        }
        // dev_supplier-end
      }
      //用户预留信息
      if (
        order_detail.value.isVirtualGoods == 2 &&
        order_detail.value.orderReserveList &&
        order_detail.value.orderReserveList.length != undefined &&
        order_detail.value.orderReserveList.length
      ) {
        reserve_info.value = [];
        order_detail.value.orderReserveList.map((item) => {
          reserve_info.value.push({
            field: item.reserveId,
            label: item.reserveName,
            labelMinWidth: 10,
            type:'show_text',
            contentMinWidth: 100,
            value:item.reserveValue!=null&&item.reserveValue!=''?item.reserveValue:'--',
          });
        });
      }
      return_progress_data.value = [];
      if (order_detail.value.orderState == 0) {
        // 订单取消
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/submit_pass.png', import.meta.url).href,
          state: L('提交订单'),
          time:
            orderLogList.value.length > 0 && orderLogList.value[0].logTime != undefined
              ? orderLogList.value[0].logTime
              : '',
          state_color: 'rgba(255, 113, 30, .6)',
          time_color: 'rgba(255, 113, 30, .3)',
          line_color: 'rgba(255, 113, 30, .3)',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/fail_current.png', import.meta.url).href,
          state: L('订单取消'),
          time:
            orderLogList.value.length > 1 && orderLogList.value[1].logTime != undefined
              ? orderLogList.value[1].logTime
              : '',
          state_color: 'rgba(255, 113, 30,1)',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: 'rgba(255, 113, 30,1)',
        });
      } else if (order_detail.value.orderState == 10) {
        //未付款订单
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/submit_current.png', import.meta.url).href,
          state: L('提交订单'),
          time:
            orderLogList.value.length > 0 && orderLogList.value[0].logTime != undefined
              ? orderLogList.value[0].logTime
              : '',
          state_color: 'rgba(255, 113, 30,1)',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: 'rgba(255, 113, 30,1)',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/pay_future.png', import.meta.url).href,
          state: L('付款成功'),
          time: '',
          state_color: '#999999',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: '#eee',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/deliver_future.png', import.meta.url).href,
          state: res.data.deliverMethod==2 ? L('等待提货') : L('商品发货'),
          time: '',
          state_color: '#999999',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: '#eee',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/suc_future.png', import.meta.url).href,
          state: L('订单完成'),
          time: '',
          state_color: '#999999',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: '#222',
        });
      } else if (order_detail.value.orderState == 20) {
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/submit_pass.png', import.meta.url).href,
          state: L('提交订单'),
          time:
            orderLogList.value.length > 0 && orderLogList.value[0].logTime != undefined
              ? orderLogList.value[0].logTime
              : '',
          state_color: 'rgba(255, 113, 30, .6)',
          time_color: 'rgba(255, 113, 30, .3)',
          line_color: 'rgba(255, 113, 30, .3)',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/pay_current.png', import.meta.url).href,
          state: L('付款成功'),
          time:
            orderLogList.value.length > 1 && orderLogList.value[1].logTime != undefined
              ? orderLogList.value[1].logTime
              : '',
          state_color: 'rgba(255, 113, 30,1)',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: 'rgba(255, 113, 30,1)',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/deliver_future.png', import.meta.url).href,
          state: res.data.deliverMethod==2 ? L('等待提货') : L('商品发货'),
          time: '',
          state_color: '#999999',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: '#eee',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/suc_future.png', import.meta.url).href,
          state: L('订单完成'),
          time: '',
          state_color: '#999999',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: '#eee',
        });
      } else if (order_detail.value.orderState == 30||order_detail.value.orderState == 31) {
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/submit_pass.png', import.meta.url).href,
          state: L('提交订单'),
          time:
            orderLogList.value.length > 0 && orderLogList.value[0].logTime != undefined
              ? orderLogList.value[0].logTime
              : '',
          state_color: 'rgba(255, 113, 30, .6)',
          time_color: 'rgba(255, 113, 30, .3)',
          line_color: 'rgba(255, 113, 30, .3)',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/pay_pass.png', import.meta.url).href,
          state: L('付款成功'),
          time:
            orderLogList.value.length > 1 && orderLogList.value[1].logTime != undefined
              ? orderLogList.value[1].logTime
              : '',
          state_color: 'rgba(255, 113, 30, .6)',
          time_color: 'rgba(255, 113, 30, .3)',
          line_color: 'rgba(255, 113, 30, .3)',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/deliver_current.png', import.meta.url).href,
          state: res.data.deliverMethod==2 ? L('等待提货') : L('商品发货'),
          time:
            orderLogList.value.length > 2 && orderLogList.value[2].logTime != undefined
              ? orderLogList.value[2].logTime
              : '',
          state_color: 'rgba(255, 113, 30,1)',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: 'rgba(255, 113, 30,1)',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/suc_future.png', import.meta.url).href,
          state: L('订单完成'),
          time: '',
          state_color: '#999999',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: '#eee',
        });
      } else if (order_detail.value.orderState == 40) {
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/submit_pass.png', import.meta.url).href,
          state: L('提交订单'),
          time:
            orderLogList.value.length > 0 && orderLogList.value[0].logTime != undefined
              ? orderLogList.value[0].logTime
              : '',
          state_color: 'rgba(255, 113, 30, .6)',
          time_color: 'rgba(255, 113, 30, .3)',
          line_color: 'rgba(255, 113, 30, .3)',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/pay_pass.png', import.meta.url).href,
          state: L('付款成功'),
          time:
            orderLogList.value.length > 0 && orderLogList.value[1].logTime != undefined
              ? orderLogList.value[1].logTime
              : '',
          state_color: 'rgba(255, 113, 30, .6)',
          time_color: 'rgba(255, 113, 30, .3)',
          line_color: 'rgba(255, 113, 30, .3)',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/deliver_pass.png', import.meta.url).href,
          state: res.data.deliverMethod==2 ? L('等待提货') : L('商品发货'),
          time:
            orderLogList.value.length > 1 && orderLogList.value[2].logTime != undefined
              ? orderLogList.value[2].logTime
              : '',
          state_color: 'rgba(255, 113, 30, .6)',
          time_color: 'rgba(255, 113, 30, .3)',
          line_color: 'rgba(255, 113, 30, .3)',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/suc_current.png', import.meta.url).href,
          state: L('订单完成'),
          time:
            orderLogList.value.length > 2 && orderLogList.value[3].logTime != undefined
              ? orderLogList.value[3].logTime
              : '',
          state_color: 'rgba(255, 113, 30,1)',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: 'rgba(255, 113, 30,1)',
        });
      }else if (order_detail.value.orderState == 50) {
        // 订单取消
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/submit_pass.png', import.meta.url).href,
          state: L('提交订单'),
          time:
            orderLogList.value.length > 0 && orderLogList.value[0].logTime != undefined
              ? orderLogList.value[0].logTime
              : '',
          state_color: 'rgba(255, 113, 30, .6)',
          time_color: 'rgba(255, 113, 30, .3)',
          line_color: 'rgba(255, 113, 30, .3)',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/fail_current.png', import.meta.url).href,
          state: L('交易关闭'),
          time:
            orderLogList.value.length > 1 && orderLogList.value[1].logTime != undefined
              ? orderLogList.value[1].logTime
              : '',
          state_color: 'rgba(255, 113, 30,1)',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: 'rgba(255, 113, 30,1)',
        });
      } 
      order_detail_info.value = JSON.parse(JSON.stringify(order_detail.value));
      if (order_detail.value.orderProductList && order_detail.value.orderProductList.length > 0) {
        for (var order_index = 0; order_index < order_detail.value.orderProductList.length; order_index++) {
          if (order_detail.value.orderProductList[order_index].afsSn) {
            columns_order_goods.value.push({
              type: 'show_text',
              title: L('操作'),
              dataIndex: 'action',
              align: 'center',
              width: 100,
            })
            return
          }
        }
      }
    } else {
      failTip(res.msg);
    }
  };

  const handleSelExpress = (val)=> {
    let obj = JSON.parse(JSON.stringify(order_detail_info.value));
    if (val != obj.expressId) {
      order_detail.value.expressNumber = '';
      showPrintExterFaceSheetBtn.value = false;
    } else {
      order_detail.value.expressNumber = obj.expressNumber;
      showPrintExterFaceSheetBtn.value = true;
    }
  }

  //获取违规下架理由
  const get_reason_list = async () => {
    let res = await getSettingReasonListApi({ type: 108, pageSize: list_com_page_more });
    if (res.state == 200) {
      resList.value = res.data
    }else{
      failTip(res.msg)
    }
  };

  //查看售后详情
  const lookDetail = (afsSn) => {
    router.push({
      path: '/order/service_refund_lists_to_detail',
      query: {
        afsSn,
      },
    });
  };
  

  
  
  onMounted(() => {
    loading.value = true;
    get_order_detail({ orderSn: route.query.orderSn });
    get_reason_list()
  });

  

</script>
<style lang="less">
  @import './style/order.less';

  .business_load_img {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    margin: auto;

    img {
      max-width: 100%;
      max-height: 100%;
    }
  }
  .sld_modal_top_tip_box{
    .sld_modal_top_tip {
      position: absolute;
      z-index: 9999;
      top: 42px;
      left: 0;
      width: 100%;
      margin-bottom: 8px;
      padding: 6px 15px;
      background-color: #fff9e2;
    }
  }
  .form_detail{
    .ant-form-item{
      margin-bottom: 6px !important;
    }
    .ant-form-item-explain{
      position: absolute;
      z-index: 2;
      top: -10px;
      right: 5%;
      min-height: 20px;
      background: #fff;
      font-size: 13px;
      text-align: left;
    }
  }

  .rider_modal {
    padding: 18px 22px;

    .rider_state {
      margin-bottom: 18px;
      color: #333;
      font-size: 14px;
      line-height: 26px;
    }

    .rider_map {
      margin-bottom: 18px;
      .amap-marker-label{
        background-color: transparent;
        border: none;
      }
      .map_content{
        background: #fff;
        box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.25);
        border-radius: 16px;
        border: none;
        padding: 9px 15px;
        font-size: 14px;
        .map_content_color{
          color: @primary-color;
        }
        .map_content_xian{
          width: 1px;
          display: inline-block;
          height: 10px;
          background: #666666;
          margin: 0 6px;
        }
      }
      .map_content_shop{
        background: #fff;
        box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.25);
        border-radius: 16px;
        border: none;
        padding: 9px 15px;
        .map_content_dao{
          font-weight: bold;
          font-size: 30rpx;
          color: #333333;
          text-align: center;
        }
        .map_content_song{
          font-size: 24rpx;
          color: #666666;
          text-align: center;
        }
        
      }
      .custom-content-marker{
        position: relative;
        width: 40px;
        height: 49px;
        .custom-content-marker_back{
          width: 40px;
          height: 49px;
        }
        .custom-content-marker_img{
          width: 36px;
          height: 36px;
          position: absolute;
          left: 50%;
          transform: translateX(-49.5%);
          top:2px;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden;
          border-radius: 50%;
          img{
            max-width: 100% !important;
            max-height: 100% !important;
            border-radius: 50%;
          }
        }
      }
      .map_content_dian{
        position: absolute;
        left:50%;
        transform: translateX(-50%);
        bottom:-16px;
        width: 16px;
        height: 16px;
        background: #04B422;
        box-shadow: 0px 0px 7px 0px rgba(0,0,0,0.2);
        border-radius: 50%;
        border: 5px solid #FFFFFF;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .rider_list {
      flex-wrap: wrap;

      .rider_item {
        width: 50%;
        margin-top: 5px;
        margin-bottom: 5px;

        .rider_label {
          color: #888;
          font-size: 15px;
          width: 30%;
          text-align: right;
        }

        .rider_content {
          width: 70%;
          color: #333;
          font-size: 15px;
        }
        .rider_content_time{
          width: 50%;
        }

        .rider_btn {
          margin-left: 15px;
          color: #0E85FF;
          font-size: 15px;
          font-weight: 400;
          cursor: pointer; 
        }
      }
    }
  }

</style>
