<template>
  <div class="order_deliver_info common_page" style="flex: 1; padding: 0">
    <Tabs
      type="card"
      v-model:activeKey="activeKey"
      v-if="data.orderDeliverList != undefined && data.orderDeliverList.length > 0"
      @change="onHandleTabClick"
    >
      <TabPane
        :tab="`${L('包裹')}${index + 1}`"
        v-for="(item, index) in data.orderDeliverList"
        :key="(index+1).toString()"
      >
      <div style="width: 100%;height: 10px;"></div>
        <BasicTable
          :columns="columns_deliver_goods"
          :bordered="true"
          :pagination="false"
          :dataSource="item.productInfoList"
          :maxHeight="400"
          :canResize="false"
        >
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.dataIndex == 'productImage'">
              <div class="goods_info com_flex_row_flex_start">
                <div class="goods_img" style="border: none">
                  <Popover placement="rightTop">
                    <template #content>
                      <div style="width: 200px">
                        <img :src="text" alt="" style="max-width: 100%; max-height: 100%" />
                      </div>
                    </template>
                    <div class="business_load_img">
                      <img :src="text" alt="" />
                    </div>
                  </Popover>
                </div>
                <div class="com_flex_column_space_between goods_detail">
                  <div
                    class="goods_name"
                    style="width: 380px; margin-top: 6px; white-space: initial"
                    :title="record.goodsName"
                  >
                    {{ record.goodsName }}
                  </div>
                  <span class="goods_brief" :title="record.specValues">
                    {{ record.specValues }}
                  </span>
                </div>
              </div>
            </template>
          </template>
        </BasicTable>
        <div :key="index" style="display: flex; flex-direction: column; margin-top: 10px">
          <div class="flex_row_start_center">
            <template v-if="item.deliverType != 2">
              <p class="express_title" v-if="item.expressName && item.expressNumber">
                {{ item.expressName }}：{{ item.expressNumber }}
              </p>
              <p
                class="express_title"
                v-else-if="expressData.expressName && expressData.expressNumber"
              >
                {{ expressData.expressName }}：{{ expressData.expressNumber }}
              </p>
            </template>
            <template v-if="item.deliverType == 2">
              <div class="flex_column_start_start">
                <p class="express_title">{{ L('该订单是自行配送，您可以联系配送人了解具体进度') }}</p>
                <p class="express_title" style=" margin-bottom: 0;margin-left: 28px">
                  {{ L('配送人姓名：') }}{{ item.deliverName }}
                </p>
                <p class="express_title" style="margin-left: 28px">
                  {{ L('配送人手机号：') }}{{ item.deliverMobile }}
                </p>
              </div>
            </template>
          </div>
          <template v-if="item.deliverType != 2&&deliver_flag">
            <div v-if="expressData.routeList != undefined && expressData.routeList.length > 0">
              <div style=" margin-top: 25px; margin-right: 10px;margin-left: 20px">
                <Timeline>
                  <TimelineItem
                    v-for="(item, index) in expressData.routeList"
                    :key="index"
                    color="#2878FF'"
                  >
                    <span class="content"
                      >&nbsp;&nbsp;&nbsp;&nbsp;{{ item.acceptTime }}&nbsp;&nbsp;</span
                    >
                    <span class="content">{{ item.acceptStation || item.remark }}</span>
                  </TimelineItem>
                </Timeline>
              </div>
            </div>
            <div v-else class="flex_column_center_center empty_express">
              <img src="@/assets/images/express_empty.png" alt="" />
              <p>{{ L('暂无物流进度') }}</p>
            </div>
          </template>
        </div>
      </TabPane>
    </Tabs>
  </div>
</template>
<script>
  export default {
    name: 'OrderDeliverInfo',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, onMounted, computed } from 'vue';
  import { Tabs, TabPane, Timeline, TimelineItem } from 'ant-design-vue';
  import { sucTip, failTip } from '@/utils/utils';
  import { BasicTable } from '/@/components/Table';
  import { getOrderGetTraceApi } from '/@/api/order/order';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const props = defineProps({
    data: { type: Object, required: false, default: {} }, //数据
  });

  //由于watch无法监听props.content，所以需要计算属性转为ref对象
  const data = computed(() => {
    return props.data;
  });

  const expressData = ref({}); //物流信息
  const deliver_flag = ref(false)
  const activeKey = ref('1');

  const columns_deliver_goods = ref([
    {
      title: L('产品信息'),
      dataIndex: 'productImage',
      align: 'center',
      width: 500,
    },
    {
      title: L('单价(元)'),
      dataIndex: 'productPrice',
      align: 'center',
      width: 100,
    },
    {
      title: L('本次发货数量'),
      dataIndex: 'deliverNum',
      align: 'center',
      width: 100,
    },
  ]); //发货包裹产品表头

  const onHandleTabClick = (e) => {
    activeKey.value = e;
    let curData = data.value.orderDeliverList.filter((item, index) => index + 1 == e)[0];
    if (curData.deliverType != 2) {
      deliver_flag.value = false
      expressData.value = []
      //2为无需物流发货
      getExpressInfo(curData.deliverId);
    }
  };

  //获取物流信息
  const getExpressInfo = async (deliverId) => {
    let res = await getOrderGetTraceApi({ deliverId: deliverId });
    if (res.state == 200) {
      expressData.value = res.data;
      deliver_flag.value = true
    } else {
      deliver_flag.value = true
      failTip(res.msg);
    }
  };

  onMounted(() => {
    if (data.value.orderDeliverList != undefined && data.value.orderDeliverList.length > 0) {
      onHandleTabClick('1');
    }
  });
</script>
<style lang="less">
  @import './style/order.less';

  .order_deliver_info {
    .vben-basic-table {
      height: auto;
    }
    .free_freight{
  color: @primary-color;
  margin-left: 5px;
  &:hover{
    color: @primary-color;
  }
}

.get_exter_face_sheet_btn{
  position: absolute;
  right: 10px;
}

.express_title {
  color: #333;
  font-size: 14px;
  margin-bottom: 6px;
  margin-top: 10px;
  font-weight: 600;
  padding: 10px 10px 0 20px;

  .content {
    font-size: 12px;
  }
}

.print_exterfacesheet {
  color: #ff711e;
  font-size: 13px;
  margin-bottom: -14px;
  margin-left: 10px;
  font-weight: bold;
}

.empty_express {
  width: 100%;
  height: 200px;
  margin-bottom: 70px;

  img {
    width: 80px;
  }

  p {
    color: #333;
    font-size: 14px;
    margin-top: 17px;
  }
}
.sld_modal_top_tip_box{
  .sld_modal_top_tip {
    position: absolute;
    z-index: 9999;
    top: 42px;
    left: 0;
    width: 100%;
    margin-bottom: 8px;
    padding: 6px 15px;
    background-color: #fff9e2;
  }
}
  }
</style>
