<template>
  <div class="section_padding spell_order_list">
    <div class="section_padding_back">
      <SldComHeader
        :title="L('订单管理')"
      />
      <Spin :spinning="loading" class="Spin_box">
        <div class="spin_height">
          <BasicForm :tableFlag="true" ref="formRef" submitOnReset @register="registerForm" class="basic-form">
          </BasicForm>
          <div style="margin-bottom: 10px">
            <RadioGroup size="small" v-model:value="filter_code" @change="clickFilter">
              <RadioButton
                :value="item.filter_code"
                v-for="(item, index) in filter_data"
                :key="index"
                >{{ item.filter_name }}</RadioButton
              >
            </RadioGroup>
          </div>
          <div class="order_list">
            <ul class="header">
              <li class="width_15 center">{{ L('拼团商品') }}</li>
              <li class="width_35 pl_100">{{ L('多人拼团') }}</li>
              <li class="width_10 center">{{ L('单价') }}</li>
              <li class="width_10 center">{{ L('数量') }}</li>
              <li class="width_10 center">{{ L('订单金额') }}</li>
              <li class="width_10 center">{{ L('订单状态') }}</li>
              <li class="width_10 center">{{ L('操作') }}</li>
            </ul>
            <div class="order_content">
              <Empty
                :image="Empty.PRESENTED_IMAGE_SIMPLE"
                v-if="data.list != undefined && data.list.length == 0"
              />
              <div class="order_content_height"  v-if="data.list != undefined && data.list.length > 0">
                <div class="item" v-for="(item, index) in data.list" :key="index">
                  <div class="order_info_num flex_row_between_center">
                    <div class="left flex_row_start_center">
                      <div class="left_activity_info flex_row_start_center">
                        <img src="@/assets/images/sld_spell_group_order_item_icon.png" alt="" class="activity_icon">
                        <span class="num activity_name" :title="item.spellName">{{ item.spellName }}</span>
                      </div>
                      <div class="spell_group_num flex_row_start_center">
                        <img src="@/assets/images/spell_group_icon.png" alt="">
                        <span>{{ item.requiredNum }}{{ L('人团') }}（{{item.stateValue}}）</span>
                      </div>
                      <div class="spell_state">
                        <span class="spell_state_val">
                          <span class="simulate_group" v-if="item.finishType == 2">{{ item.finishTypeValue }}</span>
                        </span>
                      </div>
                    </div>
                    <div class="right flex_row_end_center">
                      <span class="spell_time">{{ L('开团时间：') }}{{item.createTime}} ～ {{item.endTime}}</span>
                    </div>
                  </div>

                  <div class="flex_row_start_start" style="width: 100%;align-items:stretch;">
                    <div class="spell_group_goods flex_column_center_center" style="width: 15%;padding:15px 0;">
                      <div class="left flex_column_center_center">
                        <div class="goods_img_wrap flex_row_center_center">
                          <img :src="item.goodsImage" alt="">
                        </div>
                        <span class="goods_name" :title="item.goodsName">{{ item.goodsName }}</span>
                      </div>
                    </div>

                    <div class="flex_column_start_start" style="width: 85%;">
                      <template v-if="item.memberList && item.memberList.length > 0">
                        <div v-for="(child, child_index) in item.memberList" :key="child_index" class="order_goods_part flex_row_start_center">
                          <div class="goods flex_column_start_start width_70" :class="{goods_split:item.orderProductListVOList != undefined && item.orderProductListVOList.length > 1}" style="padding: 10px 5px;width: 64.71%;">
                            <div class="goods_item flex_row_start_center" style="width: 100%;">
                              <div class="flex_column_between_start" style="width: 63.64%;overflow:hidden;">
                                <div class="flex_row_start_center">
                                  <div class="goods_img_wrap flex_row_center_center">
                                    <div class="member_avatar"  :style="{ backgroundImage: 'url(' + child.memberAvatar + ')' }">
                                      <span class="spell_leader_flag" v-if="child.isLeader">{{ L('团长') }}</span>
                                    </div>
                                  </div>
                                  <div class="goods_info flex_column_start_start">
                                    <span class="member_name">{{ child.memberName }}</span>
                                    <span class="goods_spec" :title="child.specValues">{{ child.specValues }}</span>
                                  </div>
                                </div>

                                <div class="member_order_info">
                                  <span class="tip">
                                    {{ L('订单号：') }}
                                    <span class="content">{{ child.orderSn }}</span>
                                  </span>
                                  <span class="tip" style="margin-left:25px;">
                                    {{ L('下单时间：') }}
                                    <span class="content">{{ child.createTime }}</span>
                                  </span>
                                </div>

                              </div>

                              <span class="goods_price width_10 center" style="width:18.18%;">
                                {{ L('￥') }}{{ child.spellPrice }}
                              </span>
                              <span class="buy_num width_10 center" style="width:18.18%;">
                                {{ child.productNum }}
                              </span>
                            </div>

                          </div>
                          <div class="pay_amount width_10 center" style="width: 11.76%;">
                            {{ L('￥') }}{{ child.orderAmount }}
                          </div>
                          <div class="order_state width_10 center" style="width: 11.76%;">
                            {{ child.orderStateValue }}
                          </div>
                          <div class="operate width_10 center flex_row_center_center" style="width: 11.76%;">
                            <div class="operate_btn" @click="goDetail(child)">
                              {{ L('查看详情') }}
                            </div>
                          </div>
                        </div>
                      </template>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="pagination">
              <Pagination
                v-if="
                  data.list != undefined && data.list.length > 0 && data.pagination != undefined
                "
                v-model:current="data.pagination.current"
                size="small"
                show-quick-jumper
                show-less-items
                show-size-changer
                :show-total="(total) => `${L('共')} ${total} ${L('条数据')}`"
                :total="data.pagination.total"
                :defaultPageSize="PAGE_SIZE"
                :pageSizeOptions="PAGE_SIZE_OPTIONS"
                @change="onPageChange"
              />
            </div>
          </div>
        </div>
      </Spin>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'OrderSpellGroupOrderLists',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, onMounted } from 'vue';
  import { message, RadioGroup, RadioButton, Spin, Empty, Pagination  } from 'ant-design-vue';
  import { useRouter, useRoute } from 'vue-router';
  import { list_com_page_size_10 } from '/@/utils/utils';
  import { PAGE_SIZE, PAGE_SIZE_OPTIONS } from '/@/components/Table/src/const.ts';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { getSellerSpellListApi } from '/@/api/order/spell_group';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const router = useRouter();
  const route = useRoute();

  const loading = ref(false)
  const pageSize = ref(list_com_page_size_10);
  const filter_code = ref(''); //过滤器默认值
  const data = ref({});
  const params = ref({ pageSize: pageSize.value });
  const formValues = ref({}); //搜索条件
  const filter_data = ref([
    { filter_code: '', filter_name: L('全部订单') },
    { filter_code: '1', filter_name: L('拼团中') },
    { filter_code: '2', filter_name: L('拼团成功') },
    { filter_code: '3', filter_name: L('拼团失败') },
  ]);

   //表单
   const [registerForm, { validate, getFieldsValue }] = useForm({
    schemas: [
      {
        field: 'spellName',
        component: 'Input',
        componentProps: {
          minWidth: 300,
          placeholder: L('请输入活动名称'),
          size: 'default',
        },
        label: L('活动名称'),
      },
      {
        field: 'orderSn',
        component: 'Input',
        componentProps: {
          minWidth: 300,
          placeholder: L('请输入订单号'),
          size: 'default',
        },
        label: L('订单号'),
      },
      {
        field: 'memberName',
        component: 'Input',
        componentProps: {
          minWidth: 300,
          placeholder: L('请输入会员名称'),
          size: 'default',
        },
        label: L('会员名称'),
        labelWidth: 70,
      },
      {
        component: 'RangePicker',
        label: L('开团时间'),
        field: 'search_spell_time',
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: [L('开始时间'), L('结束时间')],
        },
      },
      {
        field: 'goodsName',
        component: 'Input',
        componentProps: {
          minWidth: 300,
          placeholder: L('请输入商品名称'),
          size: 'default',
        },
        label: L('商品名称'),
        labelWidth: 70,
      },
      {
        component: 'RangePicker',
        label: L('下单时间'),
        field: 'fieldTime',
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: [L('开始时间'), L('结束时间')],
        },
      },
    ],
    fieldMapToTime: [['fieldTime', ['startTime', 'endTime'], 'YYYY-MM-DD'],['search_spell_time', ['spellStartTime', 'spellEndTime'], 'YYYY-MM-DD']],
    labelWidth: 80,
    baseColProps: { span: 6 },
    actionColOptions: { span: 24 },
    autoSubmitOnEnter: true,
    showAdvancedButton: true,
    submitFunc: search,
    resetFunc: reset,
  });

  async function search() {
    await validate();
    let values = getFieldsValue();
    values.startTime = values.startTime ? values.startTime.split(' ')[0] + ' 00:00:00' : undefined;
    values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
    for (let i in values) {
      if (values[i] == undefined) {
        delete values[i];
      }
    }
    formValues.value = values;
    let curParams = { ...params.value, ...formValues.value };
    if (filter_code.value) {
      curParams.state = filter_code.value;
    }
    get_list(curParams);
  }

  //改变页码
  const onPageChange = (page, pageSize) => {
    let curParams = {...formValues.value };
    if (filter_code.value) {
      curParams.state = filter_code.value;
    }
    formValues.value = curParams
    params.value = { pageSize: pageSize, current: page};
    get_list({...params.value,...curParams});
  };

  async function reset() {
    filter_code.value = ''
    params.value.current = 1;
  }

   //订单条件过滤器
   const clickFilter = (e) => {
    filter_code.value = e.target.value;
    let param = { pageSize: pageSize.value, current: 1, ...formValues.value };
    if (e.target.value) {
      param.state = e.target.value;
    }
    get_list(param);
  };

  //获取数据列表
  const get_list = async (param) => {
    loading.value = true;
    let res = await getSellerSpellListApi({ ...param});
    if (res.state == 200) {
      loading.value = false;
      if (param.current > 1 && res.data.list.length == 0 && params.value.current > 1) {
        param.current = param.current - 1;
        get_list(params);
      } else {
        data.value = res.data;
      }
    }
  };

  // 订单详情
  const goDetail = (record)=> {
    router.push({
      path: `/order/order_lists_to_detail`,
      query: { orderSn: record.orderSn },
    });
  }

  onMounted(() => {
    get_list({ pageSize: pageSize.value })
  });
</script>
<style lang="less">
  @import './style/order.less';
</style>
