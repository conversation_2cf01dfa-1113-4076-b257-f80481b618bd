<template>
  <div class="">
    <Modal
      destroyOnClose
      :maskClosable="false"
      :title="exterFaceSheetModalTitle"
      :visible="exterFaceSheetOptShowFlag"
      :width="exterFaceSheetModalWidth"
      @cancel="exterFaceSheetCancle(false)"
    >
      <template #footer>
        <Button v-if="step == 1" key="back" @click="exterFaceSheetCancle(false)">{{ L('取消') }}</Button>
        <Button v-if="step == 1" key="submit" type="primary" :loading="exterFaceSheetSubmiting" @click="exterFaceSheetConfirm">{{ L('打印并发货') }}</Button>
        <Button v-if="step != 1" key="submit" type="primary">{{ L('我知道了') }}</Button>
      </template>
      <div style="padding: 10px;">
        <div class="flex_row_start_center sld_modal_top_tip" v-if="step == 2">
          <div style="line-height: 0;">
            <AliSvgIcon iconName="icontishi3" width="15px" height="15px" fillColor="#333" />
          </div>
          <span style="font-size: 13px;margin-left: 6px;">{{ L('温馨提示：以下是电子面单批量发货出错的数据，请查看具体原因') }}</span>
        </div>
        <div style="width: 100%;height: 40px;" v-if="step == 2"></div>
        <Form class="form_exter" layout="horizontal" ref="formRef" v-if="step == 1" :model="order_info">
          <Form.Item 
          :label="L('物流公司')"
          name="expressId"
          :labelCol="{span: 6}" 
          :wrapperCol="{span: 14,}"
          :rules="[{
            required: true,
            message: L('请选择物流公司'),
          }]"
          >
          <Select
          allowClear
            :placeholder="L('请选择物流公司')"
            v-model:value="order_info.expressId"
          >
            <Select.Option
              v-for="(item,index) in expressList"
              :key="index"
              :value="item.expressId"
              >{{ item.expressName }}</Select.Option
            >
          </Select>
        </Form.Item>
        </Form>
        <!-- 标准表格-start -->
        <BasicTable
          class="vben-basic-table-one"
            v-if="step == 2 && data.length"
            :canResize="false"
            :columns="columns"
            :bordered="true"
            :ellipsis="false"
            rowKey="orderSn"
            :pagination="false"
            :dataSource="data"
          >
        </BasicTable>
        <!-- 标准表格-end -->
      </div>
    </Modal>
  </div>
</template>
<script>
  export default {
    name: 'CreateExterFaceSheet',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, onMounted,computed } from 'vue';
  import { Modal,Button,Form,Select } from 'ant-design-vue';
  import { sucTip, failTip } from '@/utils/utils';
  import { getOrderInfoBatchDeliverApi } from '/@/api/order/order';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;
  
  const props = defineProps({
    exterFaceSheetOptShowFlag: { type: Boolean, required: true }, //弹框是否显示
    orderData: { type: Array },
    expressList: { type: Array },
    // zIndex: { type: Number, default: 1000 }, //弹框的层级，默认为1000，最小值也为1000
    // confirmBtnText: { type: String, default: '确认' }, //弹框底部确认按钮显示文案，默认为确认
    // showTopTip: { type: String }, //sldmodal顶部提示
  });

  const exterFaceSheetOptShowFlag = computed(() => {
    return props.exterFaceSheetOptShowFlag;
  });
  const expressList = computed(() => {
    return props.expressList;
  });
  const orderData = computed(() => {
    return props.orderData;
  });

  const emit = defineEmits([
    'cancleEvent',
  ]);

  const step = ref(1)//1为选择物流公司，2为展示生成电子面单的结果
  const formRef = ref()
  const order_info = ref({})
  const exterFaceSheetModalTitle = ref(L('电子面单批量发货'))//批量生成、打印电子面单弹框标题
  const exterFaceSheetModalWidth = ref(500)//批量生成、打印电子面单弹框宽度
  const exterFaceSheetModalVisible = ref(false)//批量生成、打印电子面单弹框是否显示，默认不显示
  const exterFaceSheetSubmiting = ref(false)//提交按钮loading状态
  const data = ref([])//列表数据
  const title = ref('')
  const type = ref('add')
  const columns = ref([
    {
      title: L('订单号'),
      dataIndex: 'orderSn',
      align: 'center',
      width: 100,
    },
    {
      title: L('是否受限'),
      dataIndex: 'restrict',
      align: 'center',
      width: 150,
    },
    {
      title: L('受限原因'),
      dataIndex: 'restrictReason',
      align: 'center',
      width: 200,
    },
  ])

  const exterFaceSheetCancle = (flag)=> {
    emit('cancleEvent',flag);
    order_info.value = {}
    step.value =  1
    data.value =  []
  }

  // 电子面单批量发货弹框继续事件
  const exterFaceSheetConfirm = ()=> {
    formRef.value.validate(['expressId']).then(async (values) => {
      exterFaceSheetSubmiting.value = true
      let res = await getOrderInfoBatchDeliverApi({...values,orderSns: orderData.value.join(',')})
      if(res.state == 200){
        if(res.data.length != undefined && res.data.length){
          step.value = 2;
          data.value = res.data;
        }else{
          sucTip(res.msg)
          exterFaceSheetCancle(true)
        }
      }else{
        failTip(res.msg)
      }
      exterFaceSheetSubmiting.value = false
    })
  }

  onMounted(() => {
  });
</script>
<style lang="less">
 .form_exter{
  .ant-form-item{
    margin-bottom: 6px !important;
  }
 }
 .vben-basic-table-one{
    height: auto;
  }
</style>
