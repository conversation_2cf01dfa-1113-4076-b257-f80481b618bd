<template>
  <div class="flex items-center justify-center w-full h-full bg-center bg-no-repeat bg-cover login_container"
    :style="{ backgroundImage: `url(${default_login_img.admin_login_bg})` }">
    <div class="flex flex-col items-center justify-center">
      <div class="w-560px h-48px flex items-center justify-center">
        <img :src="default_login_img.main_top_logo" class="h-full">
      </div>
      <div class="mx-auto font-sans text-2xl text-white top-text mt-28px mb-32px title_login_text">欢迎登录商家中心后台</div>
      <div class="flex flex-col items-center justify-center bg-white w-600px h-420px" style="border-radius: 10px">
        <div class="flex flex-row items-center justify-center border border-white border-solid w-334px mb-26px pb-27px">
          <div class="w-120px text-[#333] text-center cursor-pointer font-normal relative login_text"
            :class="{ type_active: loginType === 1 }" @click="changeTab(1)">
            账号登录
          </div>
          <div class="w-0.5 h-4 bg-white opacity-30 mx-44px"></div>
          <div class="w-120px text-[#333] text-center cursor-pointer font-normal relative login_text"
            :class="{ type_active: loginType === 2 }" @click="changeTab(2)">
            手机号登录
          </div>
        </div>

        <section>
          <div class="input_area w-334px" v-if="loginType == 1">
            <Form ref="formRef" :model="formState" :rules="loginRules">
              <FormItem ref="username" name="username">
                <Input placeholder="请输入手机号或账号" v-model:value="formState.username" class="!w-334px !h-40px border"
                  @change="clearValidate('username')" :maxlength="20">
                <template #prefix>
                  <img :src="inputIcons.username" alt="" />
                </template>
                </Input>
              </FormItem>

              <FormItem ref="password" name="password" class="block mt-5">
                <Input placeholder="请输入登录密码" v-model:value="formState.password" class="!w-334px !h-40px border"
                  type="password" :maxlength="20" @change="clearValidate('password')">
                <template #prefix>
                  <img :src="inputIcons.loginPwd" alt="" />
                </template>
                </Input>
              </FormItem>

              <FormItem ref="verifyCode" name="verifyCode" class="block mt-5">
                <Input placeholder="请输入验证码" v-model:value="formState.verifyCode"
                  class="!w-334px !h-40px !pt-0 !pb-0 !pr-0" type="text" :maxlength="4"
                  @change="clearValidate('verifyCode')">
                <template #prefix>
                  <img :src="inputIcons.imageCode" alt="" />
                </template>
                <template #suffix>
                  <img :src="capchaImg" alt="" class="h-full" @click="getCapImage" />
                </template>
                </Input>
              </FormItem>
            </Form>
          </div>
          <!-- 账号登录end -->

          <div class="input_area w-334px" v-if="loginType == 2">
            <Form ref="phoneFormRef" :model="phoneFormState" :rules="phoneLoginRules">
              <FormItem ref="phone" name="phone">
                <Input placeholder="请输入手机号" v-model:value="phoneFormState.phone" type="number"
                  class="!w-334px !h-40px border" :maxlength="11" @change="clearValidatePhone('phone')"
                  @keydown="handleKeydown">
                <template #prefix>
                  <img :src="inputIcons.phone" alt="" />
                </template>
                </Input>
              </FormItem>

              <FormItem ref="phoneVerifyCode" name="phoneVerifyCode" class="block mt-5">
                <Input placeholder="请输入图形验证码" v-model:value="phoneFormState.phoneVerifyCode"
                  class="!w-334px !h-40px !pt-0 !pb-0 !pr-0" type="text" :maxlength="4"
                  @change="clearValidatePhone('phoneVerifyCode')">
                <template #prefix>
                  <img :src="inputIcons.imageCode" alt="" />
                </template>

                <template #suffix>
                  <img :src="capchaImg" alt="" class="h-full" @click="getCapImage" />
                </template>
                </Input>
              </FormItem>

              <FormItem ref="phonePassword" name="phonePassword" class="block mt-5">
                <Input placeholder="请输入短信验证码" v-model:value="phoneFormState.phonePassword" class="!w-334px !h-40px !pr-0"
                  type="number" :maxlength="6" @change="clearValidatePhone('phonePassword')">
                <template #prefix>
                  <img :src="inputIcons.imageCode" alt="" />
                </template>
                <template #suffix>
                  <div style="font-size: 13px"
                    class="border border-white border-l-[#2878FF] w-84px leading-4 text-center text-[#2878FF] cursor-pointer hover:text-opacity-50 transition ease-in-out style_color"
                    @click="startSms">
                    {{ countTime == 0 ? '获取验证码' : `${countTime}秒` }}
                  </div>
                </template>
                </Input>
              </FormItem>
            </Form>
          </div>
          <!-- 手机号登录end -->

          <div class="block">
            <a-button type="primary" class="!h-40px border_style" :block="true" @click="loginNow" :loading="loginLoading">
              <span class="font-bold text-19px">立即登录</span>
            </a-button>
            <div class="flex items-center justify-between w-full mt-10px" :style="loginType == 2 ? 'justify-content: flex-end;' : 'justify-content: space-between;'
              ">
              <div v-if="loginType == 1" class="cursor-pointer text-[#666] cursor-pointer-btn"
                @click="openModal('forget')">忘记密码</div>
              <div class="cursor-pointer text-[#666] cursor-pointer-btn"><span>没有账号？</span><span
                  @click="openModal('register')">立即注册</span></div>
            </div>
          </div>
        </section>
      </div>
    </div>

        <!-- 绑定手机号-start -->
        <bindMobileForm ref="bindModal"/>
    <!-- 绑定手机号-end -->
    <forgetPasswordForm ref="forgetModal" />
    <registerForm :enable="is_enable" ref="registerModal" />
  </div>
</template>

<script setup>
import { accountLogin, getSmsCode, phoneLogin } from '@/api/sys/account';
import { getCaptchaApi } from '@/api/sys/example';
import bindMobileForm from '@/components/UserLogin/bindMobileForm.vue';
import forgetPasswordForm from '@/components/UserLogin/forgetPasswordForm.vue';
import registerForm from '@/components/UserLogin/registerForm.vue';
import useCheck from '@/hooks/common/useCheck';
import { useImageStore } from '@/store/modules/images';
import { getImagePath } from '@/utils';
import { failTip, setDocumentIcon, sucTip } from '@/utils/utils';
import { Form, FormItem, Input } from 'ant-design-vue';
import { onMounted, onUnmounted, reactive, ref } from 'vue';
import { useEventListener } from '/@/hooks/event/useEventListener';
import { useUserStore } from '/@/store/modules/user';
import countDown from '/@/utils/countDown';
const default_login_img = reactive({
  main_top_logo: getImagePath('images/sld_login_top_logo.png'),//顶部logo
  admin_login_bg: getImagePath('images/sld_login_bg.png'),//登录背景
  admin_login_left_bg: getImagePath('images/sld_login_left.png'),//登录左侧图片
  main_seller_center_logo: getImagePath('images/sld_java_logo.png'),//登录页logo
  vendor_login_head_portrait: getImagePath('images/<EMAIL>'),//操作成功头像
});
const loginType = ref(2);
const loginLoading = ref(false);
const bindModal = ref();
const forgetModal = ref();
const registerModal = ref();
const capchaImg = ref('');
const capchaKey = ref('');
const formRef = ref();
const phoneFormRef = ref();
const checkUnite = useCheck();
const { startDown, countTime } = countDown();
const userStore = useUserStore();
const imageStore = useImageStore();
const is_enable = ref(1)

const inputIcons = {
  username: getImagePath('images/sld_login_username.png'),
  loginPwd: getImagePath('images/sld_login_pwd.png'),
  imageCode: getImagePath('images/sld_login_img_code.png'),
  phone: getImagePath('images/sld_login_phone.png'),
};

const formState = reactive({
  username: '',
  password: '',
  verifyCode: '',
});

const phoneFormState = reactive({
  phone: '',
  phonePassword: '',
  phoneVerifyCode: '',
});

const changeTab = (type) => {
  if (loginLoading.value) {
    return;
  }
  loginType.value = type;
  getCapImage();
};

const validatePass = async (rule, value, type) => {
  const checkMethod = checkUnite[type];
  const { checkState, stateValue } = checkMethod(value);
  if (checkState > 0) {
    return Promise.reject(stateValue);
  } else {
    return Promise.resolve();
  }
};

const loginRules = {
  username: [{ required: true, trigger: 'blur', message: '用户名不能为空' }],
  password: [{ required: true, message: '密码不能为空', trigger: 'blur' }],
  verifyCode: [
    { required: true, trigger: 'blur', validator: (r, v) => validatePass(r, v, 'checkImgCode') },
  ],
};

const phoneLoginRules = {
  phone: [
    {
      required: true,
      trigger: 'blur',
      max: 11,
      validator: (r, v) => validatePass(r, v, 'checkMobile'),
    },
  ],
  phonePassword: [
    {
      required: true,
      trigger: 'blur',
      validator: (r, v) => validatePass(r, v, 'checkSmsCode'),
    },
  ],
  phoneVerifyCode: [
    {
      required: true,
      trigger: 'blur',
      validator: (r, v) => validatePass(r, v, 'checkImgCode'),
    },
  ],
};

const openModal = (type) => {
  if (type === 'forget') {
    forgetModal.value.openModal();
  } else {
    registerModal.value.openModal();
  }
};

const getCapImage = async () => {
  const cap = await getCaptchaApi();
  if (cap?.data) {
    capchaImg.value = 'data:image/png;base64,' + cap?.['data']['captcha'];
    capchaKey.value = cap?.['data']['key'];
  }
};

const preventClick = ref(false);
const startSms = async () => {
  try {
    await phoneFormRef.value.validate(['phone', 'phoneVerifyCode']);
    if (preventClick.value) {
      return;
    }
    preventClick.value = true;
    const result = await getSmsCode({
      type: 'login',
      mobile: phoneFormState.phone,
      verifyCode: phoneFormState.phoneVerifyCode,
      verifyKey: capchaKey.value,
    });

    if (result?.state == 200) {
      sucTip(result?.msg);
      startDown(() => {
        preventClick.value = false;
      });
    } else {
      preventClick.value = false;
      failTip(result?.msg);
    }
  } catch (error) {
    preventClick.value = false;
  }
};

const loginUpdateInfo = (data) => {
  userStore.loginUpdateInfo(data);
};

const handleKeydown = (e) => {
  let prohibitInput = ['-', 'e', '+', 'E', '.'];
  if (prohibitInput.includes(e.key)) {
    e.preventDefault();
  }
};

const handleLoginData = (res) => {
  if(res.state == 267){
    if(res.data&&res.data.u){
      loginLoading.value = false;
      bindModal.value.openModal(res.data.u);
    }else{
      let cur_top_nav = ['apply'];//顶部菜单
      let cur_top_nav_info = [{
        top_nav: 'apply',
        name: '商户入驻',
        left_icon: new URL('/src/assets/images/nav/store.png', import.meta.url).href,
        path: '/apply/settled_protocol',
      }];//顶部菜单详细信息
      if (res.data.resourceList.length > 0) {
        localStorage.setItem('sld_menu_data', JSON.stringify(res.data.resourceList));
        let sld_all_routes = [];//所有页面的路由
        res.data.resourceList.map(item=>{
          item.children.map(child=>{
            sld_all_routes.push(child.frontPath)
          })   
        })
        localStorage.setItem('sld_all_routes', JSON.stringify(sld_all_routes));
      }
      localStorage.setItem('cur_top_nav', JSON.stringify(cur_top_nav));
      localStorage.setItem('cur_top_nav_info', JSON.stringify(cur_top_nav_info));
    }
   
  } else if (res.state === 200) {
    let cur_top_nav = []; //顶部菜单
    let cur_top_nav_info = []; //顶部菜单详细信息
    if (res.data.resourceList.length > 0) {
      localStorage.setItem('sld_menu_data', JSON.stringify(res.data.resourceList));
      let sld_all_routes = []; //所有页面的路由
      res.data.resourceList.map((item) => {
        item.children.map((child) => {
          sld_all_routes.push(child.frontPath);
        });
      });
      localStorage.setItem('sld_all_routes', JSON.stringify(sld_all_routes));
      let tmp_data = res.data.resourceList;
      for (let i in tmp_data) {
        let split_first = tmp_data[i].frontPath.split('/');
        let target = split_first[1];
        if (cur_top_nav.indexOf(target) == -1) {
          let target_data = {};
          target_data.top_nav = target;
          target_data.path = tmp_data[i].children[0].frontPath;
          if (target == 'basic') {
            target_data.name = '概况';
            target_data.left_icon = new URL('/src/assets/images/nav/basic.png', import.meta.url).href;
          } else if (target == 'goods') {
            target_data.name = '商品';
            target_data.left_icon = new URL('/src/assets/images/nav/goods.png', import.meta.url).href;
          } else if (target == 'order') {
            target_data.name = '订单';
            target_data.left_icon = new URL('/src/assets/images/nav/order.png', import.meta.url).href;
          } else if (target == 'store') {
            target_data.name = '店铺';
            target_data.left_icon = new URL('/src/assets/images/nav/store.png', import.meta.url).href;
          } else if (target == 'bill') {
            target_data.name = '结算';
            target_data.left_icon = new URL('/src/assets/images/nav/bill.png', import.meta.url).href;
          } else if (target == 'point') {
            target_data.name = '积分商城';
            target_data.left_icon = new URL('/src/assets/images/nav/point.png', import.meta.url).href;
          } else if (target == 'im') {
            target_data.name = '客服';
            target_data.left_icon = new URL('/src/assets/images/nav/point.png', import.meta.url).href;
          } else if (target == 'spreader') {
            target_data.name = '推手';
            target_data.left_icon = new URL('/src/assets/images/nav/spreader.png', import.meta.url).href;
          } else if (target == 'statistics') {
            target_data.name = '统计';
            target_data.left_icon = new URL('/src/assets/images/nav/statistics.png', import.meta.url).href;
          } else if (target == 'marketing') {
            target_data.name = '应用';
            target_data.left_icon = new URL('/src/assets/images/nav/marketing.png', import.meta.url).href;
          }
          if (target_data.name) {
            cur_top_nav.push(target);
            cur_top_nav_info.push(target_data);
          }
        }
      }
      localStorage.setItem('cur_top_nav', JSON.stringify(cur_top_nav));
      localStorage.setItem('cur_top_nav_info', JSON.stringify(cur_top_nav_info));
    }
  }
  if(!(res.state === 267&&res.data&&res.data.u)){
    setTimeout(() => {
      loginUpdateInfo(res.data);
    });
  }
};

const loginNow = async () => {
  loginLoading.value = true;
  if (loginType.value === 1) {
    try {
      await formRef.value.validate();
      const res = await accountLogin({
        ...formState,
        loginType: 1,
        verifyKey: capchaKey.value,
      });
      if (res.state == 200 || res.state == 267) {
        handleLoginData(res);
      } else {
        loginLoading.value = false;
        getCapImage();
        failTip(res.msg);
      }
    } catch (error) {
      loginLoading.value = false;
    }
  } else {
    try {
      await phoneFormRef.value.validate();
      const loginP = {
        username: phoneFormState.phone,
        password: phoneFormState.phonePassword,
        loginType: 2,
        verifyCode: phoneFormState.phoneVerifyCode,
        verifyKey: capchaKey.value,
      };
      const res = await phoneLogin(loginP);
      if (res?.state == 200 || res?.state == 267) {
        handleLoginData(res);
      } else {
        getCapImage();
        loginLoading.value = false;
        failTip(res?.msg);
      }
    } catch (error) {
      loginLoading.value = false;
    }
  }
};

const clearValidate = (name) => {
  formRef.value.clearValidate(name);
};

const clearValidatePhone = (name) => {
  if (name == 'phonePassword') {
    phoneFormState.phonePassword = phoneFormState.phonePassword.toString().substring(0, 6);
  }

  if (name == 'phone') {
    phoneFormState.phone = phoneFormState.phone.toString().substring(0, 11);
  }

  phoneFormRef.value.clearValidate(name);
};

const keyEvent = (e) => {
  if (e.keyCode == 13) {
    if (forgetModal.value.visible) {
      forgetModal.value.onSubmit();
    } else if (registerModal.value.visible) {
      registerModal.value.onSubmit();
    } else {
      loginNow();
    }
  }
};

const { removeEvent } = useEventListener({
  name: 'keydown',
  listener: keyEvent,
});

const getPcImage = async () => {
  await imageStore.fetchSetting();
  const res = imageStore.getImagesAll;
  res.map((item) => {
    if (item.name == 'background_browser_icon') {
      setDocumentIcon(item.imageUrl ? item.imageUrl : null);
    } else if (item.imageUrl) {
      default_login_img[item.name] = item.imageUrl;
      if (item.name == 'main_top_logo' || item.name == 'main_seller_center_logo') {
        default_login_img.main_top_logo = item.imageUrl;
      }
    }
  });
}

onMounted(() => {
  if (localStorage.getItem('not_show_message') == '1') {
    localStorage.setItem('not_show_message', '2');
  }
  localStorage.removeItem('store_info')
  getPcImage();
  getCapImage();
});

onUnmounted(removeEvent);
</script>

<style lang="less" scoped>
.ant-form-item-with-help {
  margin-bottom: 0 !important;
}

.login_container {
  flex: 1 1;

  .top-text {
    letter-spacing: 4px;
  }

  .login_text {
    font-size: 20px;
    line-height: 21px;
  }

  .type_active {
    color: #ff711e;
    font-size: 19px;
    font-weight: bold;

    &::after {
      content: '';
      display: block;
      position: absolute;
      bottom: -20px;
      left: 50%;
      width: 72px;
      height: 3px;
      margin-left: -36px;
      background: @primary-color;
      font-size: 22px;
      font-weight: bold;
    }
  }

  .style_color {
    border-left-color: @primary-color;
    color: @primary-color;
  }

  .title_login_text {
    color: #fff;
    font-family: 'Source Han Sans CN';
    font-size: 24px;
    font-weight: 500;
  }

  .border_style {
    border-radius: 3px;
  }

  .cursor-pointer-btn {
    color: #333;
    font-family: 'Microsoft YaHei';
    font-size: 13px;
    font-weight: 400;

    span {
      &:nth-child(1) {
        cursor: default;
      }

      &:nth-child(2) {
        color: #ff711e;
      }
    }
  }
}
</style>
<style lang="less">
.login_container {
  .input_area {
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
    }
    input[type="number"]{
        -moz-appearance: textfield;
    }
  }
}
</style>
