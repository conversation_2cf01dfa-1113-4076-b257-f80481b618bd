<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader :title="L('订单管理')" />
      <div class="spreader_goods_lists">
        <BasicTable @register="standardTable" style="padding: 0">
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.key == 'action'">
              <TableAction
                :actions="[
                  {
                    onClick: view.bind(null, record),
                    label: L('查看'),
                  },
                ]"
              />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'SpreaderOrderList',
  };
</script>
<script setup>
  import { getCurrentInstance, onMounted, unref } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { Popover } from 'ant-design-vue';
  import { useRouter } from 'vue-router';
  import { list_com_page_more } from '/@/utils/utils';
  import { getSpreaderExtendListApi } from '/@/api/spreader/order';
  import { validatorEmoji } from '/@/utils/validate';

  const vm = getCurrentInstance();
  const L = vm?.appContext.config.globalProperties.$sldComLanguage;

  const router = useRouter();

  const [standardTable, { getForm }] = useTable({
    api: (arg) => getSpreaderExtendListApi({ ...arg }),
    rowKey: 'orderSn',
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    columns: [
      {
        title: L('订单号'),
        dataIndex: 'orderSn',
        width: 120,
      },
      {
        title: L('会员名称'),
        dataIndex: 'memberName',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: L('推手佣金(¥)'),
        dataIndex: 'commission',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: L('下单时间'),
        dataIndex: 'createTime',
        width: 100,
      },
      {
        title: L('订单金额(¥)'),
        dataIndex: 'orderAmount',
        width: 100,
      },
      {
        title: L('订单状态'),
        dataIndex: 'orderStateValue',
        width: 100,
      },
      {
        title: L('佣金状态'),
        dataIndex: 'commissionStateValue',
        width: 100,
      },
    ],
    useSearchForm: true,
    beforeFetch(values) {
      values.startTime = values.startTime
        ? values.startTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.endTime = values.endTime
        ? values.endTime.split(' ')[0] + ' 23:59:59'
        : undefined;
      return values;
    },
    actionColumn: {
      width: 80,
      title: L('操作'),
      dataIndex: 'action',
    },
    formConfig: {
      schemas: [
        {
          field: 'orderSn',
          component: 'Input',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            placeholder: L('请输入订单号'),
            size: 'default',
          },
          label: L('订单号'),
          labelWidth: 70,
          rules: [
            {
              // @ts-ignore
              validator: async (rule, value) => {
                await validatorEmoji(rule, value);
              },
              trigger: 'change',
            },
          ],
        },
        {
          field: '[startTime,endTime]',
          component: 'RangePicker',
          colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
          componentProps: {
            format: 'YYYY-MM-DD',
            placeholder: [L('开始时间'), L('结束时间')],
          },
          label: L('下单时间'),
          labelWidth: 80,
        },
        {
          field: 'orderState',
          component: 'Select',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
          componentProps: {
            placeholder: L('请选择订单状态'),
            minWidth: 300,
            size: 'default',
            options: [
              { value: '', label: L('全部') },
              { value: '0', label: L('已取消') },
              { value: '10', label: L('待付款') },
              { value: '20', label: L('待发货') },
              { value: '30', label: L('待收货') },
              { value: '40', label: L('已完成') },
              { value: '50', label: L('已关闭') },
            ],
          },
          label: L('订单状态'),
          labelWidth: 80,
        },
        {
          field: 'commissionState',
          component: 'Select',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
          componentProps: {
            placeholder: L('请选择佣金状态'),
            minWidth: 300,
            size: 'default',
            options: [
              { value: '', label: L('全部') },
              { value: '1', label: L('冻结') },
              { value: '3', label: L('已结算') },
              { value: '2', label: L('失效') },
            ],
          },
          label: L('佣金状态'),
          labelWidth: 80,
        },
      ],
    },
    bordered: true,
    striped: false,
    showIndexColumn: true,
  });

  const view = (record) => {
    router.push({
      path: `/spreader/order_list_to_detail`,
      query: { orderSn: record.orderSn },
    });
  };

  onMounted(() => {});
</script>
<style lang="less" scoped>
  .business_load_img {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    margin: auto;

    img {
      max-width: 100%;
      max-height: 100%;
    }
  }
</style>
