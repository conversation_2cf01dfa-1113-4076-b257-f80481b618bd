import type { AppRouteModule } from '/@/router/types';

import { LAYOUT } from '/@/router/constant';
import { t } from '/@/hooks/web/useI18n';

const marketing: AppRouteModule = {
  path: '/spreader',
  name: 'Spreader',
  component: LAYOUT,
  redirect: '/spreader/goods_list',
  meta: {
    orderNo: 10,
    icon: '',
    title: t('routes.demo.spreader.spreader_title'),
  },
  children: [
    {
      path: 'goods_list',
      name: 'SpreaderGoodsList',
      component: () => import('/@/views/spreader/goods_list.vue'),
      meta: {
        icon: 'icons_tuishoushangpin',
        title: t('routes.demo.spreader.goods_list'),
      },
    },
    {
      path: 'order_list',
      name: 'SpreaderOrderList',
      component: () => import('/@/views/spreader/order_list.vue'),
      meta: {
        icon: 'icons_tuishoudingdan',
        title: t('routes.demo.spreader.order_list'),
      },
    },
    {
      path: 'order_list_to_detail',
      name: 'SpreaderOrderToDetail',
      component: () => import('/@/views/spreader/order_detail.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.spreader.order_lists_to_detail'),
        currentActiveMenu: '/spreader/order_list',
      },
    },
  ],
};

export default marketing;
