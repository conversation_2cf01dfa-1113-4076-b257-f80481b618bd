import type { AppRouteModule } from '/@/router/types';

import { LAYOUT } from '/@/router/constant';

const statistics: AppRouteModule = {
  path: '/statistics',
  name: 'Statistics',
  component: LAYOUT,
  redirect: '/statistics/realTime',
  meta: {
    orderNo: 10,
    title: '统计中心',
  },
  children: [
    {
      path: 'realtime',
      name: 'RealTime',
      component: () => import('/@/views/statistics/realTime.vue'),
      meta: {
        // affix: true,
        title: '实时分析',
        icon: 'icons_shishifenxi',
      },
    },
    {
      path: 'trade',
      name: 'Trade',
      component: () => import('/@/views/statistics/trade.vue'),
      meta: {
        title: '交易分析',
        icon: 'icons_jiaoyifenxi',
      },
    },
    {
      path: 'flow',
      name: 'Flow',
      component: () => import('/@/views/statistics/flow.vue'),
      meta: {
        title: '流量分析',
        icon: 'icons_liuliangfenxi',
      },
    },
    {
      path: 'goods_sale',
      name: 'Goods_sale',
      component: () => import('/@/views/statistics/goods_sale.vue'),
      meta: {
        title: '商品分析',
        icon: 'icons_shangpinfenxi',
      },
    },
    {
      path: 'member',
      name: 'Member',
      component: () => import('/@/views/statistics/member.vue'),
      meta: {
        title: '用户分析',
        icon: 'icons_yonghufenxi',
      },
    },
  ],
};

export default statistics;
