import type { AppRouteModule } from '/@/router/types';

import { LAYOUT } from '/@/router/constant';

const bill: AppRouteModule = {
  path: '/bill',
  name: 'Bill',
  component: LAYOUT,
  redirect: '/bill/account',
  meta: {
    orderNo: 15,
    title: '结算',
  },
  children: [
    {
      path: 'account',
      name: 'Account',
      redirect: '/bill/lists',
      meta: {
        icon: 'icons_jiesuanzhanghao',
        title: '结算账号',
        hideMenu: true,
      },
    },
    {
      path: 'lists',
      name: 'Lists',
      component: () => import('/@/views/bill/lists.vue'),
      meta: {
        title: '结算账单',
        icon: 'icons_jiesuanzhangdan',
      },
    },
    {
      path: 'lists/detail',
      name: 'ListsDetail',
      component: () => import('/@/views/bill/list_detail.vue'),
      meta: {
        hideMenu: true,
        title: '账单详情',
        currentActiveMenu: '/bill/lists',
      },
    },
  ],
};

export default bill;
