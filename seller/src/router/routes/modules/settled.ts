import type { AppRouteModule } from '/@/router/types';

import { LAYOUT } from '/@/router/constant';
import { t } from '/@/hooks/web/useI18n';

const settled: AppRouteModule = {
  path: '/apply',
  name: 'settled',
  component: LAYOUT,
  redirect: '/apply/settled_protocol',
  meta: {
    orderNo: 1,
    icon: '',
    title: '店铺入驻',
  },
  children: [
    {
      path: 'settled_protocol',
      name: 'protocol',
      component: () => import('/@/views/settled/settled_protocol.vue'),
      meta: {
        title: '入驻协议',
        icon: 'ant-design:file-done',
      },
    },
    {
      path: 'base_info',
      name: 'base_info',
      component: () => import('/@/views/settled/base_info.vue'),
      meta: {
        title: '店铺信息',
        icon: 'ant-design:trophy',
      },
    },
    {
      path: 'business_info',
      name: 'business_info',
      component: () => import('/@/views/settled/business_info.vue'),
      meta: {
        title: '经营信息',
        icon: 'ant-design:dollar',
      },
    },
    {
      path: 'open_up',
      name: 'open_up',
      component: () => import('/@/views/settled/open_up.vue'),
      meta: {
        title: '商家开通',
        icon: 'ant-design:control',
      },
    },
  ],
};

export default settled;
