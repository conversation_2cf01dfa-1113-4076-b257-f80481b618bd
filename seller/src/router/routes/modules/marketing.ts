import type { AppRouteModule } from '/@/router/types';

import { getParentLayout, LAYOUT } from '/@/router/constant';
import { t } from '/@/hooks/web/useI18n';

const marketing: AppRouteModule = {
  path: '/marketing',
  name: 'Marketing',
  component: LAYOUT,
  redirect: '/marketing/center',
  meta: {
    orderNo: 10,
    title: t('routes.demo.marketing.center'),
  },
  children: [
    {
      path: 'center',
      name: 'MarketingCenter',
      component: () => import('/@/views/marketing/center.vue'),
      meta: {
        icon: 'icons_yingyongzhongxin',
        title: t('routes.demo.marketing.marketing_center'),
      },
    },
    {
      path: 'coupon_list',
      name: 'MarketingCouponList',
      component: () => import('/@/views/marketing/coupon/coupon_list.vue'),
      meta: {
        icon: 'icons_youhuiquan',
        title: t('routes.demo.marketing.coupon_list'),
      },
    },
    {
      path: 'coupon_list_to_add',
      name: 'MarketingCouponListToAdd',
      component: () => import('/@/views/marketing/coupon/add_coupon_home.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.coupon_list_to_add'),
        currentActiveMenu: '/marketing/coupon_list',
      },
    },
    {
      path: 'coupon_list_to_edit',
      name: 'MarketingCouponListToEdit',
      component: () => import('/@/views/marketing/coupon/edit_coupon.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.coupon_list_to_edit'),
        currentActiveMenu: '/marketing/coupon_list',
      },
    },
    {
      path: 'coupon_list_to_copy',
      name: 'MarketingCouponListToCopy',
      component: () => import('/@/views/marketing/coupon/copy_coupon.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.coupon_list_to_copy'),
        currentActiveMenu: '/marketing/coupon_list',
      },
    },
    {
      path: 'coupon_list_to_view',
      name: 'MarketingCouponListToView',
      component: () => import('/@/views/marketing/coupon/view_coupon.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.coupon_list_to_view'),
        currentActiveMenu: '/marketing/coupon_list',
      },
    },
    {
      path: 'coupon_list_to_receive_list',
      name: 'CouponReceiveList',
      component: () => import('/@/views/marketing/coupon/member_receive_lists.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.coupon_list_to_receive_list'),
        currentActiveMenu: '/marketing/coupon_list',
      },
    },
    {
      path: 'full_acm',
      name: 'PromotionFullAcm',
      component: () => import('/@/views/marketing/full/full_acm_list.vue'),
      meta: {
        icon: 'icons_manjianhuodong',
        title: t('routes.demo.marketing.full_acm'),
      },
    },
    {
      path: 'full_acm_to_add',
      name: 'FullAcmToAdd',
      component: () => import('/@/views/marketing/full/add_full_acm_index.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.full_acm_to_add'),
        currentActiveMenu: '/marketing/full_acm',
      },
    },
    {
      path: 'full_acm_to_edit',
      name: 'FullAcmToEdit',
      component: () => import('/@/views/marketing/full/edit_full_acm.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.full_acm_to_edit'),
        currentActiveMenu: '/marketing/full_acm',
      },
    },
    {
      path: 'full_acm_to_copy',
      name: 'FullAcmToCopy',
      component: () => import('/@/views/marketing/full/copy_full_acm.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.full_acm_to_copy'),
        currentActiveMenu: '/marketing/full_acm',
      },
    },
    {
      path: 'full_acm_to_view',
      name: 'FullAcmToView',
      component: () => import('/@/views/marketing/full/view_full_acm.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.full_acm_to_view'),
        currentActiveMenu: '/marketing/full_acm',
      },
    },
    {
      path: 'full_asm',
      name: 'PromotionFullAsm',
      component: () => import('/@/views/marketing/full/full_asm_list.vue'),
      meta: {
        icon: 'icons_jietimanjian',
        title: t('routes.demo.marketing.full_asm'),
      },
    },
    {
      path: 'full_asm_to_add',
      name: 'FullAsmToAdd',
      component: () => import('/@/views/marketing/full/add_full_asm_index.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.full_asm_to_add'),
        currentActiveMenu: '/marketing/full_asm',
      },
    },
    {
      path: 'full_asm_to_edit',
      name: 'FullAsmToEdit',
      component: () => import('/@/views/marketing/full/edit_full_asm.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.full_asm_to_edit'),
        currentActiveMenu: '/marketing/full_asm',
      },
    },
    {
      path: 'full_asm_to_view',
      name: 'FullAsmToView',
      component: () => import('/@/views/marketing/full/view_full_asm.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.full_asm_to_view'),
        currentActiveMenu: '/marketing/full_asm',
      },
    },
    {
      path: 'full_asm_to_copy',
      name: 'FullAsmToCopy',
      component: () => import('/@/views/marketing/full/copy_full_asm.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.full_asm_to_copy'),
        currentActiveMenu: '/marketing/full_asm',
      },
    },
    {
      path: 'full_ald',
      name: 'promotionFullAld',
      component: () => import('/@/views/marketing/full/full_ald_list.vue'),
      meta: {
        icon: 'icons_manNyuanzhekou',
        title: t('routes.demo.marketing.full_ald'),
      },
    },
    {
      path: 'full_ald_to_add',
      name: 'FullAldToAdd',
      component: () => import('/@/views/marketing/full/add_full_ald_index.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.full_ald_to_add'),
        currentActiveMenu: '/marketing/full_ald',
      },
    },
    {
      path: 'full_ald_to_edit',
      name: 'FullAldToEdit',
      component: () => import('/@/views/marketing/full/edit_full_ald.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.full_ald_to_edit'),
        currentActiveMenu: '/marketing/full_ald',
      },
    },
    {
      path: 'full_ald_to_copy',
      name: 'FullAldToCopy',
      component: () => import('/@/views/marketing/full/copy_full_ald.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.full_ald_to_copy'),
        currentActiveMenu: '/marketing/full_ald',
      },
    },
    {
      path: 'full_ald_to_view',
      name: 'FullAldToView',
      component: () => import('/@/views/marketing/full/view_full_ald.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.full_ald_to_view'),
        currentActiveMenu: '/marketing/full_ald',
      },
    },
    {
      path: 'full_nld',
      name: 'PromotionFullNld',
      component: () => import('/@/views/marketing/full/full_nld_list.vue'),
      meta: {
        icon: 'icons_manNjianzhekou',
        title: t('routes.demo.marketing.full_nld'),
      },
    },
    {
      path: 'full_nld_to_add',
      name: 'FullNidToAdd',
      component: () => import('/@/views/marketing/full/add_full_nld_index.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.full_nld_to_add'),
        currentActiveMenu: '/marketing/full_nld',
      },
    },
    {
      path: 'full_nld_to_view',
      name: 'FullNidToView',
      component: () => import('/@/views/marketing/full/view_full_nld.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.full_nld_to_view'),
        currentActiveMenu: '/marketing/full_nld',
      },
    },
    {
      path: 'full_nld_to_edit',
      name: 'FullNidToEdit',
      component: () => import('/@/views/marketing/full/edit_full_nld.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.full_nld_to_edit'),
        currentActiveMenu: '/marketing/full_nld',
      },
    },
    {
      path: 'full_nld_to_copy',
      name: 'FullNidToCopy',
      component: () => import('/@/views/marketing/full/copy_full_nld.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.full_nld_to_copy'),
        currentActiveMenu: '/marketing/full_nld',
      },
    },
    {
      path: 'seckill',
      name: 'PromotionSeckill',
      component: () => import('/@/views/marketing/seckill/list.vue'),
      meta: {
        icon: 'icons_miaoshahuodong',
        title: t('routes.demo.marketing.promotion_seckill'),
      },
    },
    {
      path: 'seckill_bind_goods',
      name: 'SeckillJoinedGoodsList',
      component: () => import('/@/views/marketing/seckill/joined_goods_list.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.seckill_bind_goods'),
        currentActiveMenu: '/marketing/seckill',
      },
    },
    {
      path: 'seckill_to_add',
      name: 'SeckillToAdd',
      component: () => import('/@/views/marketing/seckill/add_seckill.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.seckill_to_add'),
        currentActiveMenu: '/marketing/seckill',
      },
    },
    {
      path: 'spell_group',
      name: 'PromotionSpellGroup',
      component: () => import('/@/views/marketing/spell_group/all_list.vue'),
      meta: {
        icon: 'icons_pintuanhuodong',
        title: t('routes.demo.marketing.spell_group'),
      },
    },
    {
      path: 'spell_group_to_view',
      name: 'SpellGroupToView',
      component: () => import('/@/views/marketing/spell_group/view_spell_group.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.spell_group_to_view'),
        currentActiveMenu: '/marketing/spell_group',
      },
    },
    {
      path: 'spell_group_bind_goods',
      name: 'SpellJoinedGoodsList',
      component: () => import('/@/views/marketing/spell_group/joined_goods_list.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.spell_group_bind_goods'),
        currentActiveMenu: '/marketing/spell_group',
      },
    },
    {
      path: 'spell_group_team_list',
      name: 'SpellGroupTeamList',
      component: () => import('/@/views/marketing/spell_group/team_list.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.spell_group_team_list'),
        currentActiveMenu: '/marketing/spell_group',
      },
    },
    {
      path: 'spell_group_order',
      name: 'SpellGroupOrder',
      component: () => import('/@/views/marketing/spell_group/order_lists.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.spell_group_order'),
        currentActiveMenu: '/marketing/spell_group',
      },
    },
    {
      path: 'spell_group_order_to_detail',
      name: 'SpellGroupOrderToDetail',
      component: () => import('/@/views/marketing/spell_group/order_detail.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.spell_group_order_to_detail'),
        currentActiveMenu: '/marketing/spell_group',
      },
    },
    {
      path: 'spell_group_to_add',
      name: 'AddSpellGroup',
      component: () => import('/@/views/marketing/spell_group/add_spell_group_home.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.spell_group_to_add'),
        currentActiveMenu: '/marketing/spell_group',
      },
    },
    {
      path: 'spell_group_to_edit',
      name: 'EditSpellGroup',
      component: () => import('/@/views/marketing/spell_group/edit_spell_group.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.spell_group_to_edit'),
        currentActiveMenu: '/marketing/spell_group',
      },
    },
    {
      path: 'spell_group_to_copy',
      name: 'CopySpellGroup',
      component: () => import('/@/views/marketing/spell_group/copy_spell_group.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.spell_group_to_copy'),
        currentActiveMenu: '/marketing/spell_group',
      },
    },
    {
      path: 'ladder_group',
      name: 'PromotionLadderGroup',
      component: () => import('/@/views/marketing/ladder_group/all_list.vue'),
      meta: {
        icon: 'icons_jietituanhuodong',
        title: t('routes.demo.marketing.ladder_group'),
      },
    },
    {
      path: 'ladder_group_to_view',
      name: 'LadderGroupToView',
      component: () => import('/@/views/marketing/ladder_group/view_ladder_group.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.ladder_group_to_view'),
        currentActiveMenu: '/marketing/ladder_group',
      },
    },
    {
      path: 'ladder_group_team_list',
      name: 'LadderGroupTeamList',
      component: () => import('/@/views/marketing/ladder_group/team_list.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.ladder_group_team_list'),
        currentActiveMenu: '/marketing/ladder_group',
      },
    },
    {
      path: 'ladder_group_to_add',
      name: 'AddLadderGroup',
      component: () => import('/@/views/marketing/ladder_group/add_ladder_group_home.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.ladder_group_to_add'),
        currentActiveMenu: '/marketing/ladder_group',
      },
    },
    {
      path: 'ladder_group_to_edit',
      name: 'EditLadderGroup',
      component: () => import('/@/views/marketing/ladder_group/edit_ladder_group.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.ladder_group_to_edit'),
        currentActiveMenu: '/marketing/ladder_group',
      },
    },
    {
      path: 'ladder_group_to_copy',
      name: 'CopyLadderGroup',
      component: () => import('/@/views/marketing/ladder_group/copy_ladder_group.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.ladder_group_to_copy'),
        currentActiveMenu: '/marketing/ladder_group',
      },
    },
    {
      path: 'presale',
      name: 'PromotionPresale',
      component: () => import('/@/views/marketing/presale/list.vue'),
      meta: {
        icon: 'icons_yushouhuodong',
        title: t('routes.demo.marketing.presale'),
      },
    },
    {
      path: 'presale_bind_goods',
      name: 'PresaleGoodsList',
      component: () => import('/@/views/marketing/presale/joined_goods_list.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.presale_bind_goods'),
        currentActiveMenu: '/marketing/presale',
      },
    },
    {
      path: 'presale_to_view',
      name: 'PresaleToView',
      component: () => import('/@/views/marketing/presale/view_presale.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.presale_to_view'),
        currentActiveMenu: '/marketing/presale',
      },
    },
    {
      path: 'presale_to_add',
      name: 'PresaleToAdd',
      component: () => import('/@/views/marketing/presale/add_presale_home.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.presale_to_add'),
        currentActiveMenu: '/marketing/presale',
      },
    },
    {
      path: 'presale_to_edit',
      name: 'PresaleToEdit',
      component: () => import('/@/views/marketing/presale/edit_presale.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.presale_to_edit'),
        currentActiveMenu: '/marketing/presale',
      },
    },
    {
      path: 'presale_to_copy',
      name: 'PresaleToCopy',
      component: () => import('/@/views/marketing/presale/copy_presale.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.marketing.presale_to_copy'),
        currentActiveMenu: '/marketing/presale',
      },
    },
    // dev_mobile-start
    {
      path: 'video',
      name: 'PromotionVideo',
      component: () => import('/@/views/marketing/video/member_list.vue'),
      meta: {
        icon: 'icons_shipindaihuo',
        title: t('routes.demo.marketing.video'),
      },
    },
    // dev_mobile-end
  ],
};

export default marketing;
