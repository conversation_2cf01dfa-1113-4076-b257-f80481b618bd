import type { AppRouteModule } from '/@/router/types';

import { getParentLayout, LAYOUT } from '/@/router/constant';
import { t } from '/@/hooks/web/useI18n';

const goods: AppRouteModule = {
  path: '/goods',
  name: 'Goods',
  component: LAYOUT,
  redirect: '/goods/goods_list',
  meta: {
    orderNo: 10,
    title: t('routes.demo.goods.goods'),
  },
  children: [
    {
      path: 'goods_list',
      name: 'GoodsGoodsList',
      component: () => import('@/views/goods/goods_list.vue'),
      meta: {
        icon: 'icons_shangpinliebiao',
        title: t('routes.demo.goods.goods_list'),
      },
    },
    {
      path: 'goods_storage_list',
      name: 'GoodsStorageList',
      component: () => import('@/views/goods/goods_storage_list.vue'),
      meta: {
        icon: 'icons_cangkuzhongshangpin',
        title: t('routes.demo.goods.goods_storage_list'),
      },
    },
    {
      path: 'goods_check_list',
      name: 'GoodsCheckList',
      component: () => import('@/views/goods/goods_check_list.vue'),
      meta: {
        icon: 'icons_daishenheshangpin',
        title: t('routes.demo.goods.goods_check_list'),
      },
    },
    {
      path: 'course',
      name: 'Course',
      component: () => import('@/views/goods/course.vue'),
      meta: {
        icon: 'icons_daishenheshangpin',
        title: t('routes.demo.goods.course'),
      },
    },
    {
      path: 'course_detail',
      name: 'CourseDetail',
      component: () => import('@/views/goods/course_detail.vue'),
      meta: {
        icon: 'ant-design:edit',
        title: t('routes.demo.goods.course_detail'),
        hideMenu: true,
        currentActiveMenu: '/goods/course',
      },
    },
    {
      path: 'goods_list_to_add',
      name: 'GoodsListToAdd',
      component: () => import('@/views/goods/goods_list_to_add_home.vue'),
      meta: {
        icon: 'ant-design:edit',
        title: t('routes.demo.goods.goods_list_to_add'),
        hideMenu: true,
        currentActiveMenu: '/goods/goods_list',
      },
    },
    {
      path: 'related_template',
      name: 'GoodsTemplate',
      component: () => import('@/views/goods/related_template.vue'),
      meta: {
        icon: 'icons_guanlianbanshi',
        title: t('routes.demo.goods.related_template'),
      },
    },
    {
      path: 'related_template_to_add',
      name: 'GoodsTemplateAdd',
      component: () => import('@/views/goods/related_template_to_add.vue'),
      meta: {
        icon: 'icons_guanlianbanshi',
        title: t('routes.demo.goods.related_template_to_add'),
        hideMenu: true,
      },
    },
    {
      path: 'related_template_to_edit',
      name: 'GoodsTemplateEdit',
      component: () => import('@/views/goods/related_template_to_edit.vue'),
      meta: {
        icon: 'icons_guanlianbanshi',
        title: t('routes.demo.goods.related_template_to_edit'),
        hideMenu: true,
      },
    },
    {
      path: 'attribute_group',
      name: 'GoodsAttribute',
      component: () => import('@/views/goods/attribute_group.vue'),
      meta: {
        icon: 'icons_zidingyishuxing',
        title: t('routes.demo.goods.attribute_group'),
      },
    },
    {
      path: 'attribute_group_to_detail',
      name: 'GoodsAttributeDetail',
      component: () => import('@/views/goods/attribute_group_to_detail.vue'),
      meta: {
        icon: 'icons_zidingyishuxing',
        title: t('routes.demo.goods.attribute_group_to_detail'),
        hideMenu: true,
      },
    },
    {
      path: 'goods_list_to_edit',
      name: 'GoodsListToEdit',
      component: () => import('@/views/goods/goods_list_to_edit.vue'),
      meta: {
        icon: 'ant-design:edit',
        title: t('routes.demo.goods.goods_list_to_edit'),
        hideMenu: true,
        currentActiveMenu: '/goods/goods_list',
      },
    },
    {
      path: 'goods_import',
      name: 'GoodsImport',
      component: () => import('@/views/goods/goods_import.vue'),
      meta: {
        icon: 'icons_shangpindaoru',
        title: t('routes.demo.goods.goods_import'),
      },
    },
    {
      path: 'goods_import_to_platform',
      name: 'PlatformGoodsImport',
      component: () => import('/@/views/goods/platform_goods_import.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.goods.platform_goods_import'),
        currentActiveMenu: '/goods/goods_import',
      },
    },
    //商品资料详情页面
    {
      path: 'goods_import_to_add',
      name: 'GoodsImportToAdd',
      component: () => import('@/views/goods/platform_goods_detail.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.goods.goods_import_to_add'),
        currentActiveMenu: '/goods/goods_import',
      },
    },
    {
      path: 'goods_import_to_excel',
      name: 'GoodsImportToExcel',
      component: () => import('@/views/goods/excel_import.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.goods.goods_import_to_excel'),
        currentActiveMenu: '/goods/goods_import',
      },
    },
    // dev_supplier-start
    // 供应链产品库
    {
      path: 'goods_import_to_supplier',
      name: 'GoodsImportToSupplier',
      component: () => import('@/views/goods/supplier_import.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.goods.goods_import_to_supplier'),
        currentActiveMenu: '/goods/goods_import',
      },
    },
    {
      path: 'goods_import_supplier_to_add',
      name: 'GoodsImportToSupplierToAdd',
      component: () => import('@/views/goods/supplier_import_detail.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.goods.goods_import_supplier_to_add'),
        currentActiveMenu: '/goods/goods_import',
      },
    },
    // dev_supplier-end
    // dev_supplier_vop-start
    //商品信息变更
    {
      path: 'goods_changed_notice',
      name: 'GoodsChangedNotice',
      component: () => import('@/views/goods/goods_changed_notice.vue'),
      meta: {
        icon: 'iconbianji2',
        title: t('routes.demo.goods.goods_changed_notice'),
      },
    },
    //商品信息变更_查看详情
    {
      path: 'goods_changed_notice_detail',
      name: 'GoodsChangedNoticeToDetail',
      component: () => import('@/views/goods/goods_list_to_add_home.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.goods.goods_changed_notice_detail'),
        currentActiveMenu: '/goods/goods_changed_notice',
      },
    },
    //商品信息变更_查看详情
    // dev_supplier_vop-end
    // dev_supplier-start
    {
      path: 'goods_purchase',
      name: 'GoodsPurchase',
      component: LAYOUT,
      meta: {
        icon: 'icongouwuche1',
        title: t('routes.demo.goods.goods_purchase'),
      },
    },
    // dev_supplier-end
  ],
};

export default goods;
