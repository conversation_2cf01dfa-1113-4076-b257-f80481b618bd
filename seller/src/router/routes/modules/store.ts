import type { AppRouteModule } from '/@/router/types';

import { t } from '/@/hooks/web/useI18n';
import { LAYOUT } from '/@/router/constant';

const marketing: AppRouteModule = {
  path: '/store',
  name: 'Store',
  component: LAYOUT,
  redirect: '/store/setting',
  meta: {
    orderNo: 10,
    title: t('routes.demo.store.store'),
  },
  children: [
    {
      path: 'setting',
      name: 'Store_setting',
      component: () => import('@/views/store/setting.vue'),
      meta: {
        title: '店铺设置',
        icon: 'icons_dianpushezhi',
      },
    },
    //店铺信息
    {
      path: 'info',
      name: 'StoreInfo',
      component: () => import('/@/views/store/info.vue'),
      meta: {
        icon: 'iconding-dianpuguanli',
        title: '店铺资质',
      },
    },    
    // dev_pc-start
    {
      path: 'decorate_pc',
      name: 'Decorate_pc',
      component: () => import('@/views/store/decorate_pc'),
      meta: {
        title: 'PC装修',
        icon: 'icons_PCzhuangxiu',
      },
    },
    {
      path: 'decorate_pc_instance_template_lists_to_edit',
      name: 'Decorate_pc_instance_template_lists_to_edit',
      component: () => import('@/views/store/decorate_pc/diy_template_edit.vue'),
      meta: {
        hideMenu: true,
        title: '模板编辑',
        currentActiveMenu: '/store/decorate_pc',
      },
    },
    {
      path: 'decorate_pc_home_to_edit',
      name: 'Decorate_pc_home_to_edit',
      component: () => import('@/views/store/decorate_pc/diy_page_edit.vue'),
      meta: {
        hideSideBar: true,
        title: '装修详情',
        currentActiveMenu: '/store/decorate_pc',
        hideMenu: true,
        hideHeader: true,
      },
    },
    // dev_pc-end
    // dev_mobile-start
    {
      path: 'decorate_mhome',
      name: 'Decorate_m',
      component: () => import('@/views/store/decorate_m/deco_list.vue'),
      meta: {
        title: '手机装修',
        icon: 'icons_shoujizhuangxiu',
      },
    },
    {
      path: 'decorate_mhome_to_diy',
      name: 'Decorate_m_to_diy',
      component: () => import('@/views/store/decorate_m/deco_list_add.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: '装修详情',
        icon: 'icons_shoujizhuangxiu',
        currentActiveMenu: '/store/decorate_mhome',
      },
    },
    // dev_mobile-end
    // 店铺分类
    {
      path: 'category',
      name: 'StoreCategory',
      component: () => import('/@/views/store/category.vue'),
      meta: {
        icon: 'icons_dianpufenlei',
        title: t('routes.demo.store.category'),
      },
    },
    //品牌申请
    {
      path: 'brand_lists',
      name: 'StoreBrandLists',
      component: () => import('/@/views/store/brand_lists.vue'),
      meta: {
        icon: 'icons_pinpaishenqing',
        title: t('routes.demo.store.brand_lists'),
      },
    },
    // 消息接收设置
    {
      path: 'msg_setting',
      name: 'StoreMsgSetting',
      component: () => import('/@/views/store/msg_setting.vue'),
      meta: {
        icon: 'icons_xiaoxishezhi',
        title: t('routes.demo.store.msg_setting'),
      },
    },
    // 消息接收设置
    {
      path: 'msg_lists',
      name: 'StoreMsgLists',
      component: () => import('/@/views/store/msg_lists.vue'),
      meta: {
        icon: 'icons_xiaoxiliebiao',
        title: t('routes.demo.store.msg_lists'),
      },
    },
    //账号管理
    {
      path: 'account',
      name: 'StoreAccount',
      component: () => import('/@/views/store/account/index.vue'),
      meta: {
        icon: 'icons_zhanghaoguanli',
        title: t('routes.demo.store.account'),
      },
    },
    //账号管理
    {
      path: 'material',
      name: 'StoreMaterial',
      component: () => import('/@/views/store/material/index.vue'),
      meta: {
        icon: 'icons_sucaizhongxin',
        title: t('routes.demo.store.material'),
      },
    },
  ],
};

export default marketing;
