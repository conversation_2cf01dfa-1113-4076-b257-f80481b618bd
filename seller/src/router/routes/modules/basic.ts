import type { AppRouteModule } from '/@/router/types';

import { LAYOUT } from '/@/router/constant';

const basic: AppRouteModule = {
  path: '/basic',
  name: 'Basics',
  component: LAYOUT,
  redirect: '/basic/simple_stat',
  meta: {
    orderNo: 10,
    title: '概况',
  },
  children: [
    {
      path: 'simple_stat',
      name: 'SimpleStat',
      component: () => import('/@/views/dashboard/simple_stat/index.vue'),
      meta: {
        title: '概况',
        icon: 'icons_gaikuang',
      },
    },
  ],
};

export default basic;
