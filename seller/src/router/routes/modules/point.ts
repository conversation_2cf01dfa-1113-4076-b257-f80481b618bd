import type { AppRouteModule } from '/@/router/types';

import { getParentLayout, LAYOUT } from '/@/router/constant';
import { t } from '/@/hooks/web/useI18n';

const marketing: AppRouteModule = {
  path: '/point',
  name: 'Point',
  component: LAYOUT,
  redirect: '/point/goods_list',
  meta: {
    orderNo: 10,
    title: t('routes.demo.point.point'),
  },
  children: [
    {
      path: 'goods_list',
      name: 'PointGoodsList',
      component: () => import('/@/views/point/goods/goods_list.vue'),
      meta: {
        icon: 'icons_jifenshangcheng-shangpinguanli',
        title: t('routes.demo.point.goods_list'),
      },
    },
    {
      path: 'goods_list_to_add',
      name: 'PointGoodsListToAdd',
      component: () => import('/@/views/point/goods/add_goods_home.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.point.goods_list_to_add'),
        currentActiveMenu: '/point/goods_list',
      },
    },
    {
      path: 'goods_list_to_edit',
      name: 'PointGoodsListToEdit',
      component: () => import('/@/views/point/goods/edit_goods.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.point.goods_list_to_edit'),
        currentActiveMenu: '/point/goods_list',
      },
    },
    {
      path: 'goods_list_to_import',
      name: 'goodsListToImport',
      component: () => import('/@/views/point/goods/select_mall_goods.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.point.goods_list_to_import'),
        currentActiveMenu: '/point/goods_list',
      },
    },
    {
      path: 'order_list',
      name: 'PointOrderList',
      component: () => import('/@/views/point/order/order_lists.vue'),
      meta: {
        icon: 'icons_jifenshangcheng-dingdanguanli',
        title: t('routes.demo.point.order_list'),
      },
    },
    {
      path: 'order_list_to_detail',
      name: 'PointOrderListToDetail',
      component: () => import('/@/views/point/order/order_detail.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.point.order_list_to_detail'),
        currentActiveMenu: '/point/order_list',
      },
    },
    {
      path: 'bill_list',
      name: 'PointBillList',
      component: () => import('/@/views/point/bill/lists.vue'),
      meta: {
        icon: 'icons_jifenshangcheng-jiesuanguanli',
        title: t('routes.demo.point.bill_list'),
      },
    },
    {
      path: 'bill_list_to_detail',
      name: 'PointBillListToDetail',
      component: () => import('/@/views/point/bill/detail.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.point.bill_list_to_detail'),
        currentActiveMenu: '/point/bill_list',
      },
    },
    {
      path: 'goods_import',
      name: 'PointGoodsImport',
      component: () => import('@/views/point/import/goods_import.vue'),
      meta: {
        icon: 'icons_shangpindaoru',
        title: t('routes.demo.point.goods_import'),
      },
    },
  ],
};

export default marketing;
