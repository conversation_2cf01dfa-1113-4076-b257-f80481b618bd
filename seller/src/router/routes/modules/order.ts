import type { AppRouteModule } from '/@/router/types';

import { getParentLayout, LAYOUT } from '/@/router/constant';
import { t } from '/@/hooks/web/useI18n';

const marketing: AppRouteModule = {
  path: '/order',
  name: 'Order',
  component: LAYOUT,
  redirect: '/order/order_lists',
  meta: {
    orderNo: 10,
    title: t('routes.demo.order.order'),
  },
  children: [
    {
      path: 'order_lists',
      name: 'OrderOrderLists',
      component: () => import('/@/views/order/order_lists.vue'),
      meta: {
        icon: 'icons_dingdanliebiao',
        title: t('routes.demo.order.order_lists'),
      },
    },
    {
      path: 'order_lists_to_detail',
      name: 'OrderListsToDetail',
      component: () => import('/@/views/order/order_detail.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.order.order_lists_to_detail'),
        currentActiveMenu: '/order/order_lists',
      },
    },
    {
      path: 'spell_group_order_lists',
      name: 'OrderSpellGroupOrderLists',
      component: () => import('/@/views/order/spell_group/order_lists.vue'),
      meta: {
        icon: 'icons_pintuandingdan',
        title: t('routes.demo.order.spell_group_order_lists'),
      },
    },
    {
      path: 'order_deliver',
      name: 'OrderOrderDeliver',
      component: () => import('/@/views/order/order_deliver.vue'),
      meta: {
        icon: 'icondingdanfahuo1',
        title: t('routes.demo.order.order_deliver'),
      },
    },
    {
      path: 'order_deliver_detail',
      name: 'OrderOrderDeliverDetail',
      component: () => import('/@/views/order/order_deliver_detail.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.order.order_deliver_detail'),
        currentActiveMenu: '/order/order_deliver',
      },
    },
    {
      path: 'service',
      name: 'OrderService',
      component: () => import('/@/views/order/service/index.vue'),
      meta: {
        icon: 'icons_shouhouguanli',
        title: t('routes.demo.order.service'),
      },
    },
    {
      path: 'service_refund_lists_to_detail',
      name: 'OrderServiceRefundListsToDetail',
      component: () => import('/@/views/order/service/detail.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.order.service_refund_lists_to_detail'),
        currentActiveMenu: '/order/service',
      },
    },
    {
      path: 'evaluation',
      name: 'OrderEvaluation',
      component: () => import('/@/views/order/evaluation/evaluation.vue'),
      meta: {
        icon: 'icons_pingjiaguanli',
        title: t('routes.demo.order.evaluation'),
      },
    },
    {
      path: 'express',
      name: 'OrderExpress',
      component: () => import('/@/views/order/express/index.vue'),
      meta: {
        icon: 'icons_wuliuguanli',
        title: t('routes.demo.order.express'),
      },
    },
    {
      path: 'express_transport_to_add',
      name: 'OrderExpressTransportToAdd',
      component: () => import('/@/views/order/express/add_transport_home.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.order.express_transport_to_add'),
        currentActiveMenu: '/order/express',
      },
    },
    {
      path: 'express_transport_to_edit',
      name: 'OrderExpressTransportToEdit',
      component: () => import('/@/views/order/express/edit_transport.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.order.express_transport_to_edit'),
        currentActiveMenu: '/order/express',
      },
    },
    {
      path: 'express_transport_to_copy',
      name: 'OrderExpressTransportToCopy',
      component: () => import('/@/views/order/express/copy_transport.vue'),
      meta: {
        hideMenu: true,
        showMenu: true,
        title: t('routes.demo.order.express_transport_to_copy'),
        currentActiveMenu: '/order/express',
      },
    },
    
    
    {
      path: 'address_list',
      name: 'OrderAddressList',
      component: () => import('/@/views/order/address/address_list.vue'),
      meta: {
        icon: 'icons_dizhiguanli',
        title: t('routes.demo.order.address_list'),
      },
    },
    {
      path: 'print_setting',
      name: 'OrderPrintSetting',
      component: () => import('/@/views/order/print_setting.vue'),
      meta: {
        icon: 'icons_dayinjishezhi',
        title: t('routes.demo.order.print_setting'),
      },
    },
  ],
};

export default marketing;
