<template>
  <RouterView>
    <template #default="{ Component, route }">
      <transition
        :name="
          getTransitionName({
            route,
            openCache,
            enableTransition: getEnableTransition,
            cacheTabs: getCaches,
            def: getBasicTransition,
          })
        "
        mode="out-in"
        appear
      >
        <keep-alive v-if="openCache" :include="getCaches">
          <component :is="Component" :key="route.fullPath" />
        </keep-alive>
        <component v-else :is="Component" :key="route.fullPath" />
      </transition>
    </template>
  </RouterView>
</template>

<script lang="ts">
  import { computed, defineComponent, unref } from 'vue';

  import { useRootSetting } from '/@/hooks/setting/useRootSetting';

  import { useTransitionSetting } from '/@/hooks/setting/useTransitionSetting';
  import { useMultipleTabSetting } from '/@/hooks/setting/useMultipleTabSetting';
  import { getTransitionName } from './transition';
  import { useUserStore } from '/@/store/modules/user';

  import { useMultipleTabStore } from '/@/store/modules/multipleTab';

  export default defineComponent({
    name: 'PageLayout',
    setup() {
      const { getShowMultipleTab } = useMultipleTabSetting();
      const tabStore = useMultipleTabStore();

      const { getOpenKeepAlive, getCanEmbedIFramePage } = useRootSetting();

      const userStore = useUserStore();

      const { getBasicTransition, getEnableTransition } = useTransitionSetting();

      const openCache = computed(() => unref(getOpenKeepAlive) && unref(getShowMultipleTab));

      const getCaches = computed((): string[] => {
        if (!unref(getOpenKeepAlive)) {
          return [];
        }
        let tabList = JSON.parse(JSON.stringify(tabStore.getCachedTabList))
        if(userStore.getDelKeepAlive&&userStore.getDelKeepAlive.length>0){
          let del = JSON.parse(JSON.stringify(userStore.getDelKeepAlive))
          del.forEach(item=>{
            let index = tabList.indexOf(item)
            if(index!=-1){
              tabList.splice(index,1)
            }
          })
          userStore.setDelKeepAlive([])
        }
        let tab =  JSON.parse(JSON.stringify(userStore.getUpdateTab))
        let tabListOne = JSON.parse(JSON.stringify(tabStore.getCachedTabList))
        tab.forEach(item=>{
          let index = tabListOne.findIndex(it=>it == item.name)
          if(index==-1){
            userStore.setDelTab(item.name)
          }
        })
        return tabList;
      });

      return {
        getTransitionName,
        openCache,
        getEnableTransition,
        getBasicTransition,
        getCaches,
        getCanEmbedIFramePage,
      };
    },
  });
</script>
