<template>
  <Dropdown placement="bottomLeft" :overlayClassName="`${prefixCls}-dropdown-overlay`">
    <span :class="[prefixCls, `${prefixCls}--${theme}`]" class="flex">
      <span :class="`${prefixCls}__info hidden md:block`">
        <span :class="`${prefixCls}__name  `" class="truncate">
          {{ getUserInfo&&getUserInfo.storeName ? getUserInfo.storeName : ''}}
          <!-- dev_o2o-start -->
          <template v-if="getUserInfo&&getUserInfo.shopType&&getUserInfo.shopType==2">【O2O门店】</template>
          <!-- dev_o2o-end -->
           <!-- dev_supplier-start -->
          <template v-if="getUserInfo&&getUserInfo.shopType&&getUserInfo.shopType==3">【供应商】</template>
          <!-- dev_supplier-end -->
        </span>
      </span>
    </span>

    <template #overlay>
      <Menu @click="handleMenuClick">
        <MenuItem key="password" :text="'修改密码'" icon="ant-design:setting" />
        <MenuItem
          key="logout"
          :text="t('layout.header.dropdownItemLoginOut')"
          icon="ion:power-outline"
        />
      </Menu>
    </template>
  </Dropdown>
  <LockAction @register="register" />
  <SldModal
    :title="'修改密码'"
    :visible="modalVisible"
    :confirmBtnLoading="modalConfirmBtnLoading"
    @cancle-event="handleModalCancle"
    @confirm-event="handleModalConfirm"
    :showFoot="true"
    :width="550"
    :content="operateData"
  />
</template>
<script lang="ts">
  // components
  import { Dropdown, Menu } from 'ant-design-vue';
  import type { MenuInfo } from 'ant-design-vue/lib/menu/src/interface';
  import { defineComponent, computed, ref } from 'vue';
  import { DOC_URL } from '/@/settings/siteSetting';
  import { useUserStore } from '/@/store/modules/user';
  import { useHeaderSetting } from '/@/hooks/setting/useHeaderSetting';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { useModal } from '/@/components/Modal';
  import { propTypes } from '/@/utils/propTypes';
  import { openWindow } from '/@/utils';
  import { createAsyncComponent } from '/@/utils/factory/createAsyncComponent';
  import SldModal from '@/components/SldModal/index.vue';
  import base64Encrypt from '/@/utils/base64Encrypt';
  import { updatePwdApi } from '/@/api/sys/account';
  import { sucTip, failTip } from '@/utils/utils';
  import { router } from '/@/router';
  import { PageEnum } from '/@/enums/pageEnum';
  import { logoutApi } from '/@/api/common/common';

  type MenuEvent = 'logout' | 'doc' | 'lock' | 'password';

  export default defineComponent({
    name: 'UserDropdown',
    components: {
      Dropdown,
      Menu,
      MenuItem: createAsyncComponent(() => import('./DropMenuItem.vue')),
      MenuDivider: Menu.Divider,
      LockAction: createAsyncComponent(() => import('../lock/LockModal.vue')),
      SldModal,
    },
    props: {
      theme: propTypes.oneOf(['dark', 'light']),
    },
    setup() {
      const { prefixCls } = useDesign('header-user-dropdown');
      const { t } = useI18n();
      const { getShowDoc } = useHeaderSetting();
      const userStore = useUserStore();

      const getUserInfo = computed(() => {
        return userStore.getStoreInfo;
      });

      const [register, { openModal }] = useModal();

      function handleLock() {
        openModal(true);
      }

      //  login out
      const handleLoginOut = async() => {
        // userStore.confirmLoginOut();
        const res = await logoutApi({ grant_type: 'refresh_token', refresh_token: userStore.getRefreshToken });
        if (res.state == 200) {
          userStore.setToken(undefined);
          userStore.setAccessToken(undefined);
          userStore.setRefreshToken(undefined);
          userStore.setSessionTimeout(false);
          userStore.setUserInfo(null);
          //增加redirect
          let browserUrl = window.location.href;
          if (browserUrl.indexOf('redirect=') != -1) {
            browserUrl = browserUrl.substring(0, browserUrl.indexOf('redirect=') - 1);
          }
          router.push(PageEnum.BASE_LOGIN + '?redirect=' + encodeURIComponent(browserUrl));
        }
      }

      // open doc
      function openDoc() {
        openWindow(DOC_URL);
      }

      function handleMenuClick(e: MenuInfo) {
        switch (e.key as MenuEvent) {
          case 'logout':
            handleLoginOut();
            break;
          case 'doc':
            openDoc();
            break;
          case 'lock':
            handleLock();
            break;
          case 'password':
            operateData.value = JSON.parse(JSON.stringify(addData.value));
            modalVisible.value = true;
            break;
        }
      }

      const operateData = ref([]);
      const addData = ref<any>([
        {
          type: 'input',
          label: '旧密码',
          input_type: 'password',
          name: 'oldPwd',
          placeholder: '请输入旧密码',
          initialValue: '',
          rules: [
            { required: true, message: '请输入旧密码' },
          ],
        },
        {
          type: 'input',
          label: '新密码',
          input_type: 'password',
          name: 'newPwd',
          placeholder: '请输入新密码',
          initialValue: '',
          maxLength: '16',
          rules: [
            { required: true, message: '请输入新密码' },
          ],
        },
        {
          type: 'input',
          label: '新密码确认',
          input_type: 'password',
          name: 'confirmPwd',
          placeholder: '请再次输入新密码',
          initialValue: '',
          maxLength: '16',
          rules: [
            { required: true, message: '请再次输入新密码' },
          ],
        },
      ]);
      // 密码弹窗开关
      const modalVisible = ref(false);
      // 修改密码按钮loading
      const modalConfirmBtnLoading = ref(false);
      const handleModalConfirm = async (val) => {
        if (modalConfirmBtnLoading.value) return;
        try {
          let res;
          if (val.newPassword != val.newPasswordCfm) {
            failTip(`两次密码不一致，请重新输入`);
            return false;
          }
          val.oldPwd = base64Encrypt(val.oldPwd);
          val.newPwd = base64Encrypt(val.newPwd);
          val.confirmPwd = base64Encrypt(val.confirmPwd);
          modalConfirmBtnLoading.value = true;
          res = await updatePwdApi(val);
          if (res && res.state === 200) {
            sucTip(res.msg);
            modalConfirmBtnLoading.value = false;
            handleModalCancle();
            userStore.setAccessToken(undefined);
            userStore.setRefreshToken(undefined);
            await userStore.logout();
          } else {
            modalConfirmBtnLoading.value = false;
            failTip(res.msg);
          }
        } catch (error) {
          modalConfirmBtnLoading.value = false;
        }
      };
      // 取消弹框
      const handleModalCancle = () => {
        modalVisible.value = false;
        operateData.value = [];
      };

      return {
        prefixCls,
        t,
        getUserInfo,
        handleMenuClick,
        getShowDoc,
        register,
        operateData,
        addData,
        modalVisible,
        modalConfirmBtnLoading,
        handleModalConfirm,
        handleModalCancle,
      };
    },
  });
</script>
<style lang="less">
  @prefix-cls: ~'@{namespace}-header-user-dropdown';

  .@{prefix-cls} {
    align-items: center;
    height: @header-height;
    padding: 0 0 0 10px;
    padding-right: 10px;
    overflow: hidden;
    font-size: 12px;
    cursor: pointer;

    img {
      width: 24px;
      height: 24px;
      margin-right: 12px;
    }

    &__header {
      border-radius: 50%;
    }

    &__name {
      font-size: 14px;
    }

    &--dark {
      &:hover {
        background-color: @header-dark-bg-hover-color;
      }
    }

    &--light {
      &:hover {
        background-color: #fff3;
      }

      .@{prefix-cls}__name {
        color: #fff;
      }

      .@{prefix-cls}__desc {
        color: @header-light-desc-color;
      }
    }

    &-dropdown-overlay {
      .ant-dropdown-menu-item {
        min-width: 160px;
      }
    }
  }
</style>
