@prefix-cls: ~'@{namespace}-multiple-tabs';

html[data-theme='dark'] {
  .@{prefix-cls} {
    .ant-tabs-tab {
      border-bottom: 1px solid @border-color-base;
    }
  }
}

html[data-theme='light'] {
  .@{prefix-cls} {
    .ant-tabs-tab:not(.ant-tabs-tab-active) {
      border: none !important; //1px solid #d9d9d9 !important;
    }
  }
}

.@{prefix-cls} {
  z-index: 10;
  height: @multiple-height;
  line-height: @multiple-height;

  .ant-tabs-small {
    height: @multiple-height + 2;
  }

  .ant-tabs.ant-tabs-card {
    .ant-tabs-nav {
      height: @multiple-height + 2;
      margin: 0;
      padding-top: 2px;
      border: 0;
      background-color: @component-background;
      box-shadow: none;

      .ant-tabs-tab-btn:focus,
      .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
        color: #000000a6;
      }

      .ant-tabs-nav-container {
        height: @multiple-height;
        padding-top: 2px;
      }

      .ant-tabs-tab {
        height: 30px !important; //calc(@multiple-height - 2px);
        padding-right: 15px !important;
        padding-left: 15px !important;
        transition: none;
        background-color: @component-background;
        color: @text-color-base;
        line-height: 24px; //calc(@multiple-height - 2px);

        .ant-tabs-tab-remove {
          width: auto;
          height: 20px;
          margin-right: -8px;
          margin-left: 2px;
          transition: none;
          color: inherit;
          font-size: 12px;
        }

        // > div {
        //   display: flex;
        //   justify-content: center;
        //   align-items: center;
        // }

        svg {
          fill: #acacac;
        }
      }



      .ant-tabs-tab-active {
        position: relative;
        padding-left: 18px;
        transition: none;
        border: 0;
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        background: #F0F2F5; //@primary-color;

        span {
          color: #111 !important; //@white !important;
        }

        .ant-tabs-tab-remove {
          opacity: 1;
        }

        svg {
          width: 11px;
          fill: #111;
        }
      }

      &:before {
        display: none;
      }
    }

    .ant-tabs-nav > div:nth-child(1) {
      padding: 0 6px;

      .ant-tabs-tab {
        margin-right: 3px !important;

        &:hover {
          background-color: #f8f8f9;
        }
        &.ant-tabs-tab-active {
          &:hover {
            background-color: #F0F2F5;
          }
        }
      }
    }
  }

  .ant-tabs-tab:not(.ant-tabs-tab-active) {
    .anticon-close {
      font-size: 12px;

      svg {
        width: 11px;
      }
    }
  }

  .ant-dropdown-trigger {
    display: inline-flex;
  }

  &--hide-close {
    .ant-tabs-tab-remove {
      display: none;
    }
  }

  &-content {
    &__extra-quick,
    &__extra-redo,
    &__extra-fold {
      display: inline-block;
      width: 36px;
      height: @multiple-height;
      border-left: 1px solid @border-color-base;
      color: @text-color-secondary;
      line-height: @multiple-height;
      text-align: center;
      cursor: pointer;

      &:hover {
        color: @text-color-base;
      }

      span[role='img'] {
        transform: rotate(90deg);
      }
    }

    &__extra-redo {
      span[role='img'] {
        transform: rotate(0deg);
      }
    }

    &__info {
      display: inline-block;
      width: 100%;
      line-height: 26px;
      height: 27px; //@multiple-height;
      padding-left: 0;
      font-size: 14px;
      cursor: pointer;
      user-select: none;
      span{
        margin-left: 0 !important;
      }
    }
  }
}

.ant-tabs-dropdown-menu {
  &-title-content {
    display: flex;
    align-items: center;

    .@{prefix-cls} {
      &-content__info {
        width: auto;
        margin-left: 0;
        line-height: 28px;
      }
    }
  }

  &-item-remove {
    margin-left: auto;
  }
}

.multiple-tabs__dropdown {
  .ant-dropdown-content {
    width: 172px;
  }
}
