import { Persistent, BasicKeys } from '/@/utils/cache/persistent';
import { SLD_ACCESS_TOKEN_KEY } from '/@/enums/cacheEnum';

export function getToken() {
  return getAuthCache(SLD_ACCESS_TOKEN_KEY);
}

export function getAuthCache<T>(key: BasicKeys) {
  const fn = Persistent.getLocal;
  return fn(key) as T;
}

export function setAuthCache(key: BasicKeys, value) {
  const fn = Persistent.setLocal;
  return fn(key, value, true);
}

export function clearAuthCache(immediate = true) {
  const fn = Persistent.clearLocal;
  return fn(immediate);
}
