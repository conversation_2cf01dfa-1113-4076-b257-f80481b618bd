import { ref } from 'vue';

export default function countDown(initailCount?: number) {
  const countTime = ref<number>(0);
  let timer: null | NodeJS.Timeout = null;
  const tmpFunction = ref<Function>();

  const timeCount = () => {
    if (countTime.value == 0) {
      tmpFunction.value && tmpFunction.value();
      clearTimeout(Number(timer));
      timer = null;
    } else {
      countTime.value--;
      timer = setTimeout(timeCount, 1000);
    }
  };

  const startDown = (callback?) => {
    tmpFunction.value = callback;
    countTime.value = initailCount || 60;
    timeCount();
  };

  const setCount = (count: number) => {
    countTime.value = count;
  };

  return {
    startDown,
    setCount,
    countTime,
  };
}
