/*
 * 去除字符串左右两端的空格
 * @param {String} str 去除空格的字符串
 * */
export function trimString(str) {
  return str.replace(/(^\s*)|(\s*$)/g, '');
}

//验证邮箱
export function sldCheckEmail(s) {
  const regu = /^(\w-*\.*)+@(\w-?)+(\.\w{2,})+$/;
  const re = new RegExp(regu);
  if (re.test(s)) {
    return true;
  } else {
    return false;
  }
}

// 验证空格
export function sldValidatorSpace(value) {
  if (value) {
    if (!/^\S*$/.test(value)) {
      return false;
    }
  }
  return true;
}

/**
 * 验证邮箱
 */
export function validatorVendorEmail(rule, value) {
  if (value && trimString(value)) {
    if (sldCheckEmail(value)) {
      return Promise.resolve();
    } else {
      return Promise.reject(`请输入正确的邮箱`);
    }
  } else {
    return Promise.resolve();
  }
}

/**
 * 验证物流公司信息排序 0-255
 */
export function validatorExpressSort(rule, value) {
  if (value >= 0 && value <= 255) {
    return Promise.resolve();
  } else {
    return Promise.reject(`请输入0~255的数字`);
  }
}

/**
 * 验证用户的密码，6～20位，由字母、数字或符号组成的验证
 */
export function validatorMemPwd(rule, value) {
  if (value) {
    if (value.length < 6 || value.length > 20) {
      return Promise.reject(`请输入6～20位的密码`);
    } else if (/[\u4E00-\u9FA5]/g.test(value)) {
      return Promise.reject(`密码不可以有中文`);
    } else if (!/^\S*$/.test(value)) {
      return Promise.reject(`密码中不可以有空格`);
    }
  }
  return Promise.resolve();
}

/**
 * 验证用户的密码，8-16位，由字母、数字、符号任意3种组成的验证
 */
export function validatorMemPwdOne(rule, value) {
  if (value) {
    if (value.length < 8 || value.length > 16) {
      return Promise.reject(`请输入8～16位的密码`);
    } else if (/[\u4E00-\u9FA5]/g.test(value)) {
      return Promise.reject(`密码不可以有中文`);
    } else if (!/^\S*$/.test(value)) {
      return Promise.reject(`密码中不可以有空格`);
    } else if (
      !/^(?![A-Za-z]+$)(?![A-Z\d]+$)(?![A-Z\W]+$)(?![a-z\d]+$)(?![a-z\W]+$)(?![\d\W]+$)\S{8,16}$/.test(
        value,
      )
    ) {
      return Promise.reject(`密码格式不正确`);
    }
  }
  return Promise.resolve();
}

/**
 * 验证输入字数
 */
export function validatorNum(rule, value, { min, max }) {
  if (value) {
    const emoji_reg =
      /(\ud83c[\udf00-\udfff])|(\ud83d[\udc00-\ude4f\ude80-\udeff])|[\u2600-\u2B55]/g;
    if (emoji_reg.test(value)) {
      return Promise.reject('不能包含表情');
    }
    if (min && max) {
      if (value.length < min || value.length > max) {
        return Promise.reject(`请输入${min}-${max}位的值`);
      }
    } else if (min && value.length < min) {
      return Promise.reject(`最少输入${min}位`);
    } else if (max && value.length > max) {
      return Promise.reject(`最大输入${max}位`);
    }
    return Promise.resolve();
  }
}

/**
 * 验证输入是否包含表情
 */
export function validatorEmoji(rule, value) {
  const emoji_reg =
  // eslint-disable-next-line no-misleading-character-class, no-useless-escape
  /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/gi;
  if (!emoji_reg.test(value)) {
    return Promise.resolve();
  } else {
    return Promise.reject('不能包含表情');
  }
}

/**
 * 验证输入只能是数字 type==1时  value为空不效验
 */
export function validatorMath(rule, value, plod, type) {
  const emoji_reg = /^[0-9]*$/;
  if (type == 1 && !value) {
    return Promise.resolve();
  }
  if (emoji_reg.test(value)) {
    return Promise.resolve();
  } else {
    return Promise.reject(plod ? plod : `只能输入数字`);
  }
}

/**
 * 验证空格
 */
export function validatorSpace(rule, value) {
  if (value) {
    if (!/^\S*$/.test(value)) {
      return Promise.reject('不可以输入空格');
    }
  }
  return Promise.resolve();
}

export const verify_code_reg = /^[0-9]{6}$/; //验证码的正则表达式

export const mobile_reg = /(1[3-9]\d{9}$)/; //手机号的正则表达式

/**
 * 验证输入的是否是特殊字符
 * @params  String str  验证的字符串
 */
export function validatorSpecialString(rule, value) {
  const reg =
    /[`~!@¥#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘’，。、]/im;
  if (value && reg.test(value)) {
    return Promise.reject('检测到有特殊字符，请重新输入');
  }
  return Promise.resolve();
}

export const validatorEmojiRules = {
  // @ts-ignore
  validator: async (rule, value) => {
    await validatorEmoji(rule, value);
  },
  trigger: 'change',
}; //验证输入是否包含表情


// 短信验证码验证
export function checkSmsCode(rule, value){
  if (value&&(value.length < 6 || !(/^[0-9]+$/.test(value)))) {
    return Promise.reject(`请输入正确的短信验证码`);
  }else {
    return Promise.resolve();
  }
};

/**
 * 验证店铺电话(可以是手机号也可以是固话)
 */
export function validatorVendorPhone(rule, value, desc = '请输入正确的电话号') {
  if (value) {
    if (sldCheckTel(value) || sldCheckMobile(value)) {
      return Promise.resolve();
    } else {
      return Promise.reject(desc);
    }
  } else {
    return Promise.resolve();
  }
}

//验证手机号
export function sldCheckMobile(s) {
  if (mobile_reg.test(s)) {
    return true;
  } else {
    return false;
  }
}

//验证固定电话
export function sldCheckTel(s) {
  let regu = /^(\(\d{3,4}\)|\d{3,4}-|\s)?\d{7,14}$/;
  let re = new RegExp(regu);
  if (re.test(s)) {
    return true;
  } else {
    return false;
  }
}

//验证身份证
export function validatorIdCard(rule, value, desc) {
  let reg_idcard = /^[1-9][0-9]{5}(19|20)[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))[0-9]{3}([0-9]|X)$/;
  if (value && !reg_idcard.test(value)) {
    return Promise.reject(`${desc ? desc : '请输入正确的身份证'}`);
  }
  return Promise.resolve();
}