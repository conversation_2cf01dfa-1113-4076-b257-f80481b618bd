/**
 * base64加密
 * @params data String 要加密的字符串
 * @zoucb-2023-05-25
 */
export default function base64Encrypt(data) {
  let b64_obj = {
    a: 'CABDE',
    b: 'FoHIJ',
    c: 'KLpNO',
    d: 'PQRyr',
    e: 'UVWXi',
    f: 'Zght6',
    g: 'efabYzS',
    h: 'jkmu012l',
    m: 'GMqTs345d',
    n: 'cnvwx789()=',
  };
  let position = [
    'a:0',
    'b:1',
    'c:2',
    'd:3',
    'd:4',
    'e:4',
    'f:1',
    'f:2',
    'f:3',
    'n:8',
    'a:1',
    'n:9',
    'g:6',
    'h:7',
    'b:1',
    'm:8',
    'b:1',
    'n:1',
  ];
  for (let i in position) {
    let [k, v] = position[i].split(':');
    data = data + `${b64_obj[k][v]}`;
  }

  let b64 = Object.values(b64_obj).join('');

  let o1,
    o2,
    o3,
    h1,
    h2,
    h3,
    h4,
    bits,
    i = 0,
    ac = 0,
    enc = '';
  const tmp_arr: string[] = [];
  if (!data) {
    return data;
  }
  data = utf8Encode(data);
  do {
    o1 = data.charCodeAt(i++);
    o2 = data.charCodeAt(i++);
    o3 = data.charCodeAt(i++);

    bits = (o1 << 16) | (o2 << 8) | o3;

    h1 = (bits >> 18) & 0x3f;
    h2 = (bits >> 12) & 0x3f;
    h3 = (bits >> 6) & 0x3f;
    h4 = bits & 0x3f;
    tmp_arr[ac++] = b64.charAt(h1) + b64.charAt(h2) + b64.charAt(h3) + b64.charAt(h4);
  } while (i < data.length);

  enc = tmp_arr.join('');

  switch (data.length % 3) {
    case 1:
      enc = enc.slice(0, -2) + '==';
      break;
    case 2:
      enc = enc.slice(0, -1) + '=';
      break;
  }

  return enc;
}

function utf8Encode(string) {
  string = (string + '').replace(/\r\n/g, '\n').replace(/\r/g, '\n');

  let utftext = '',
    start,
    end;
  let stringl = 0,
    n;

  start = end = 0;
  stringl = string.length;

  for (n = 0; n < stringl; n++) {
    const c1 = string.charCodeAt(n);
    let enc = '';

    if (c1 < 128) {
      end++;
    } else if (c1 > 127 && c1 < 2048) {
      enc = String.fromCharCode((c1 >> 6) | 192, (c1 & 63) | 128);
    } else {
      enc = String.fromCharCode((c1 >> 12) | 224, ((c1 >> 6) & 63) | 128, (c1 & 63) | 128);
    }
    if (enc !== null) {
      if (end > start) {
        utftext += string.substring(start, end);
      }
      utftext += enc;
      start = end = n + 1;
    }
  }

  if (end > start) {
    utftext += string.substring(start, string.length);
  }

  return utftext;
}
