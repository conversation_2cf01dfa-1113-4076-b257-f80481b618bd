import type { GlobConfig } from '/#/config';

import { getAppEnvConfig } from '/@/utils/env';

export const useGlobSetting = (): Readonly<GlobConfig> => {
  const {
    VITE_GLOB_APP_TITLE,
    VITE_GLOB_API_URL,
    VITE_GLOB_API_URL_PREFIX,
    VITE_GLOB_PC_URL,
    VITE_GLOB_IM_URL,
    VITE_GLOB_MAP_KEY,
    VITE_GLOB_MAP_SECURITY,
    // dev_supplier-start
    VITE_GLOB_SUPPLIER_URL,
    // dev_supplier-end
  } = getAppEnvConfig();

  // Take global configuration
  const glob: Readonly<GlobConfig> = {
    title: VITE_GLOB_APP_TITLE,
    apiUrl: VITE_GLOB_API_URL,
    shortName: VITE_GLOB_APP_TITLE.replace(/\s/g, '_').replace(/-/g, '_'),
    urlPrefix: VITE_GLOB_API_URL_PREFIX,
    pcUrl: VITE_GLOB_PC_URL,
    imUrl: VITE_GLOB_IM_URL,
    mapKey: VITE_GLOB_MAP_KEY,
    mapSecurity: VITE_GLOB_MAP_SECURITY,
    // dev_supplier-start
    supplierUrl: VITE_GLOB_SUPPLIER_URL,
    // dev_supplier-end
  };
  return glob as Readonly<GlobConfig>;
};
