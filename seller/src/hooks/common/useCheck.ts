import type { RuleObject } from 'ant-design-vue/lib/form/interface';

interface errStateType {
  checkState: number;
  stateValue: string;
}
//校验手机号
export function checkMobile(value: string) {
  const mobile_reg = /(1[3-9]\d{9}$)/;
  const errState: errStateType = { checkState: 0, stateValue: '' };
  if (!value.trim()) {
    errState.checkState = 1;
    errState.stateValue = '请输入手机号';
  } else if (!mobile_reg.test(value.toString())) {
    errState.checkState = 2;
    errState.stateValue = '请输入正确的手机号';
  }
  return errState;
}
//校验邮箱
export function checkEmail(value: string) {
  const emailReg = /^(\w-*\.*)+@(\w-?)+(\.\w{2,})+$/;
  const errState: errStateType = { checkState: 0, stateValue: '' };
  if (value.trim() && !emailReg.test(value)) {
    errState.checkState = 1;
    errState.stateValue = '请输入正确的邮箱';
  }
  return errState;
}
//校验输入名字
function checkVendorName(value: string) {
  const errState: errStateType = { checkState: 0, stateValue: '' };

  if (!value.trim()) {
    errState.checkState = 1;
    errState.stateValue = '请输入供应商账号';
  } else if (value.length < 6 || value.length > 20) {
    errState.checkState = 2;
    errState.stateValue = '请输入6-20位数字与字母组合的供应商账号';
  } else if (!/^[0-9a-zA-Z]+$/.test(value)) {
    errState.checkState = 3;
    errState.stateValue = '供应商账号只能输入数字和字母';
  } else if (/^[0-9]+$/.test(value)) {
    errState.checkState = 4;
    errState.stateValue = '供应商账号不能输入纯数字';
  } else if (/^[a-zA-Z]+$/.test(value)) {
    errState.checkState = 5;
    errState.stateValue = '供应商账号不能输入纯字母';
  }
  return errState;
}
//校验图形验证码
function checkImgCode(value: string) {
  const errState: errStateType = { checkState: 0, stateValue: '' };

  if (!value.trim()) {
    errState.checkState = 1;
    errState.stateValue = '请输入图形验证码';
  } else if (value.length < 4 || !/^[0-9a-zA-Z]+$/.test(value)) {
    errState.checkState = 2;
    errState.stateValue = '请输入正确的图形验证码';
  }

  return errState;
}
//校验短信验证码
function checkSmsCode(value: string) {
  const errState: errStateType = { checkState: 0, stateValue: '' };

  if (!value.trim()) {
    errState.checkState = 1;
    errState.stateValue = '请输入短信验证码';
  } else if (value.length < 6 || !/^[0-9]+$/.test(value)) {
    errState.checkState = 2;
    errState.stateValue = '请输入正确的短信验证码';
  }

  return errState;
}
//校验密码
function checkPassword(value: string) {
  const errState: errStateType = { checkState: 0, stateValue: '' };

  if (!value.trim()) {
    errState.checkState = 1;
    errState.stateValue = '请输入密码';
  }
  if (value.length < 8 || value.length > 16) {
    errState.checkState = 2;
    errState.stateValue = '请输入8～16位密码';
  } else {
    if (/[\u4E00-\u9FA5]/g.test(value)) {
      errState.checkState = 3;
      errState.stateValue = '密码不可以有中文';
    } else if (!/^\S*$/.test(value)) {
      errState.checkState = 4;
      errState.stateValue = '密码中不可以有空格';
    }
  }

  return errState;
}
//验证用户的密码，8-16位，由字母、数字、符号任意3种组成的验证
function checkPasswordNew(value: string) {
  const errState: errStateType = { checkState: 0, stateValue: '' };
  if (!value.trim()) {
    errState.checkState = 1;
    errState.stateValue = '请输入密码';
  } else if (value.length < 8 || value.length > 16) {
    errState.checkState = 2;
    errState.stateValue = '请输入8-16位的密码';
  } else if (/[\u4E00-\u9FA5]/g.test(value)) {
    errState.checkState = 3;
    errState.stateValue = '密码不可以有中文';
  } else if (!/^\S*$/.test(value)) {
    errState.checkState = 4;
    errState.stateValue = '密码中不可以有空格';
  } else if (
    !/^(?![A-Za-z]+$)(?![A-Z\d]+$)(?![A-Z\W]+$)(?![a-z\d]+$)(?![a-z\W]+$)(?![\d\W]+$)\S{8,16}$/.test(
      value,
    )
  ) {
    errState.checkState = 5;
    errState.stateValue = '密码格式不正确';
  }

  return errState;
}

//验证用户的密码，6-20位，由字母、数字、符号任意3种组成的验证
function checkPasswordNewOld(value: string) {
  const errState: errStateType = { checkState: 0, stateValue: '' };
  if (!value.trim()) {
    errState.checkState = 1;
    errState.stateValue = '请输入密码';
  } else if (value.length < 6 || value.length > 20) {
    errState.checkState = 2;
    errState.stateValue = '请输入6-20位的密码';
  } else if (/[\u4E00-\u9FA5]/g.test(value)) {
    errState.checkState = 3;
    errState.stateValue = '密码不可以有中文';
  } else if (!/^\S*$/.test(value)) {
    errState.checkState = 4;
    errState.stateValue = '密码中不可以有空格';
  }

  return errState;
}
//Form表单校验变量的类型
export type rulesCheckType = { [k: string]: RuleObject | RuleObject[] };

export default function useCheck() {
  return {
    checkPassword,
    checkPasswordNew,
    checkSmsCode,
    checkImgCode,
    checkVendorName,
    checkEmail,
    checkMobile,
    checkPasswordNewOld,
  };
}
