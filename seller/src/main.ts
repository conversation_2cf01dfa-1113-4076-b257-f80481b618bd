import 'uno.css';
import '@/design/index.less';

import '@/assets/global.less';
import 'ant-design-vue/dist/antd.less';
// Register icon sprite
import 'virtual:svg-icons-register';

import { createApp } from 'vue';

import { registerGlobComp } from '@/components/registerGlobComp';
import { setupGlobDirectives } from '@/directives';
import { setupI18n } from '@/locales/setupI18n';
import { setupErrorHandle } from '@/logics/error-handle';
import { initAppConfigStore } from '@/logics/initAppConfig';
import { router, setupRouter } from '@/router';
import { setupRouterGuard } from '@/router/guard';
import { setupStore } from '@/store';
import { AliSvgIcon } from '@/components/SvgIcon/index';
import { useI18n } from '/@/hooks/web/useI18n';

import App from './App.vue';

async function bootstrap() {
  const app = createApp(App);

  // Configure store
  // 配置 store
  setupStore(app);

  // Initialize internal system configuration
  // 初始化内部系统配置
  initAppConfigStore();

  // Register global components
  // 注册全局组件
  registerGlobComp(app);

  // Multilingual configuration
  // 多语言配置
  // Asynchronous case: language files may be obtained from the server side
  // 异步案例：语言文件可能从服务器端获取
  await setupI18n(app);

  // Configure routing
  // 配置路由
  setupRouter(app);

  // router-guard
  // 路由守卫
  setupRouterGuard(router);

  // Register global directive
  // 注册全局指令
  setupGlobDirectives(app);

  // Configure global error handling
  // 配置全局错误处理
  setupErrorHandle(app);

  // https://next.router.vuejs.org/api/#isready
  // await router.isReady();
  //全量注册阿里巴巴图标组件
  app.use(AliSvgIcon);

  app.mount('#app');

  //多语言翻译函数挂载 - 对应文件为 content.ts - start
  const { t } = useI18n();
  app.config.globalProperties.$sldComLanguage = (name) => {
    return t('content.' + name) !== '' && t('content.' + name).indexOf('content.') == -1
      ? t('content.' + name)
      : name;
  };
  //多语言翻译函数挂载 - 对应文件为 content.ts - end
}

bootstrap();
