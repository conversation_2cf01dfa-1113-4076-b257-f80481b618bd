import { defHttp } from '/@/utils/http/axios';

enum Api {
  STORE_DETAIL = '/seller/seller/store/detail',
  UPDATE_CERTIFICATION = '/seller/seller/store/updateCertification',
}

/**
 * 获取店铺详情
 */
export const getStoreDetail = () => {
  return defHttp.get(
    {
      url: Api.STORE_DETAIL,
    },
    {
      errorMessageMode: 'none',
    },
  );
};

/**
 * 更新店铺认证信息
 * @param params 
 */
export const updateCertification = (params: any) => {
  return defHttp.post(
    {
      url: Api.UPDATE_CERTIFICATION,
      params,
    },
    {
      errorMessageMode: 'none',
    },
  );
};
