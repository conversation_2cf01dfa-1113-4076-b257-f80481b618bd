import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
var Api;
(function (Api) {
  // 分类管理
  Api['GET_CATEGORY_TREE'] = '/goods/seller/goodsCategory/getCateTree';
  Api['GET_CATEGORY_LIST'] = '/goods/seller/goodsCategory/list';

  // 品类管理
  Api['GET_BRAND_LIST'] = '/goods/seller/Brand/list';

  // 属性管理
  Api['ATTR_LIST'] = '/goods/seller/goodsAttribute/list';

  // 商品资料库
  Api['GET_BOTTOM_CATEGORY'] = '/goods/seller/goodsCategory/bottomCategory';
  Api['GET_GOODS_SPEC_LIST'] = '/goods/seller/goodsSpec/list';
  Api['ADD_GOODS_SPEC'] = '/goods/seller/goodsSpec/add';
  Api['ADD_GOODS_SPEC_VAL'] = '/goods/seller/goodsSpec/addSpecValue';
  // dev_supplier_vop-start
  Api['GET_PUSH_GOODS_MSG_LIST'] = '/goods/seller/goods/push/msg/list';
  // dev_supplier_vop-end

  // 入驻店铺
  Api['GET_STORE_DETAIL'] = '/seller/seller/store/detail';
  Api['UPDATE_CERTIFICATION'] = '/seller/seller/store/updateCertification';
  Api['GET_STORE_LABEL_LIST'] = '/seller/seller/store/getStoreLabelList';

  // 店铺等级
  Api['STORE_GRADE_LIST'] = '/seller/seller/storeGrade/list';

  // 文章列表
  Api['GET_ARTICLE_LIST'] = '/cms/seller/article/list';

  // 发布编辑商品
  Api['GET_CATEGORY_ATTR_BRAND'] = '/goods/seller/goodsAttribute/listByCategoryId';
  Api['GET_STORE_ATTR_LIST'] = '/goods/seller/goodsParameterGroup/canUseList';
  Api['GET_STORE_ATTR_INFO'] = '/goods/seller/goodsParameter/canUseList';
  Api['GET_RELATED_TEMPLATE'] = '/goods/seller/goodsRelatedTemplate/list';
  Api['GET_STORE_CATEGORY_LIST'] = '/seller/seller/storeCategory/list';
  Api['GET_LABEL_LIST'] = '/goods/seller/goodsLabel/list';
  Api['GET_FREIGHT_TEMPLATE_LIST'] = '/goods/seller/goodsFreightTemplate/list';
  Api['ADD_PLATFORM_GOOD'] = '/goods/seller/goods/add';
  Api['EDIT_PLATFORM_GOOD'] = '/goods/seller/goods/update';
  Api['GET_PLATFORM_GOOD_DETAIL'] = '/goods/seller/goods/getGoodsInfo'; 


})(Api || (Api = {}));

//获取分类树数据
export const getCategoryTree = (params) => defHttp.get({ url: Api.GET_CATEGORY_TREE, params });

//获取品牌列表
export const getBrandList = (params) => defHttp.get({ url: Api.GET_BRAND_LIST, params });

//获取属性列表
export const getAttrList = (params) => defHttp.get({ url: Api.ATTR_LIST, params });

//获取商品资料库商品分类
export const getGoodsPlatformCategory = (params) =>
  defHttp.get({ url: Api.GET_BOTTOM_CATEGORY, params });

//获取商品规格数据
export const getGoodsSpecList = (params) => defHttp.get({ url: Api.GET_GOODS_SPEC_LIST, params });

//新增商品规格名
export const addGoodsSpec = (params) =>
  defHttp.post({
    url: Api.ADD_GOODS_SPEC,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//新增商品规格值
export const addGoodsSpecVal = (params) =>
  defHttp.post({
    url: Api.ADD_GOODS_SPEC_VAL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取店铺分类数据
export const getStoreCategoryList = (params) => defHttp.get({ url: Api.GET_STORE_CATEGORY_LIST, params });

//获取标签列表
export const getLabelList = (params) => defHttp.get({ url: Api.GET_LABEL_LIST, params });

//获取店铺标签列表
export const getStoreLabelList = (params) => defHttp.get({ url: Api.GET_STORE_LABEL_LIST, params });

// dev_supplier_vop-start
//获取商品信息变更通知列表
export const getPushGoodsMsgList = (params) => defHttp.get({ url: Api.GET_PUSH_GOODS_MSG_LIST, params });
// dev_supplier_vop-end


//获取入驻店铺详情
export const getStoreDetail = (params) => defHttp.get({ url: Api.GET_STORE_DETAIL, params });

//获取店铺等级
export const getStoreGrade = (params) => defHttp.get({ url: Api.STORE_GRADE_LIST, params });

//获取文章列表
export const getArticleList = (params) => defHttp.get({ url: Api.GET_ARTICLE_LIST, params });


//获取分类数据
export const getCategoryList = (params) => defHttp.get({ url: Api.GET_CATEGORY_LIST, params });

//根据分类获取属性和品牌数据
export const getCategoryAttrBrand = (params) =>
  defHttp.get({ url: Api.GET_CATEGORY_ATTR_BRAND, params });

//获取店铺自定义属性
export const getStoreAttrList = (params) =>
  defHttp.get({ url: Api.GET_STORE_ATTR_LIST, params });

//获取店铺自定义属性详情
export const getStoreAttrInfo = (params) =>
  defHttp.get({ url: Api.GET_STORE_ATTR_INFO, params });

//获取关联模板
export const getRelatedTemplate = (params) =>
  defHttp.get({ url: Api.GET_RELATED_TEMPLATE, params });

//获取运费模板数据
export const getFreightTemplateList = (params) => defHttp.get({ url: Api.GET_FREIGHT_TEMPLATE_LIST, params });

//新增商品资料
export const addPlatformGood = (params) =>
  defHttp.post({
    url: Api.ADD_PLATFORM_GOOD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });

//新增商品资料
export const editPlatformGood = (params) =>
  defHttp.post({
    url: Api.EDIT_PLATFORM_GOOD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });

//获取商品详情
export const getPlatformGoodDetail = (params) => defHttp.get({ url: Api.GET_PLATFORM_GOOD_DETAIL, params });

//更新店铺认证信息
export const updateCertification = (params) => 
  defHttp.post({ 
    url: Api.UPDATE_CERTIFICATION, 
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });
