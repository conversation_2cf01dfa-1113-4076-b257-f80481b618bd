import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
// dev_supplier-start
import { useUserStore } from '/@/store/modules/user';
const userStore = useUserStore();
// dev_supplier-end

const Api = {
  GET_ORDER_INFO_LIST: '/business/seller/orderInfo/list',
  GET_ORDER_INFO_DETAIL: '/business/seller/orderInfo/detail',
  GET_ORDER_INFO_GET_TRACE: '/business/seller/logistics/order/getTrace',
  GET_ORDER_INFO_REMARK: '/business/seller/orderInfo/remark',
  GET_ORDER_INFO_STAR: '/business/seller/orderInfo/star',
  GET_ORDER_INFO_UPDATE_PRICE: '/business/seller/orderInfo/updatePrice',
  GET_SETTING_REASON_LIST: '/system/seller/reason/list',
  GET_ORDER_INFO_CANCEL: '/business/seller/orderInfo/cancel',
  GET_ORDER_INFO_ORDER_PRODUCT_LIST: '/business/seller/orderInfo/orderProductList',
  GET_ORDER_INFO_EXPRESS_LIST: '/seller/seller/express/list',
  GET_ORDER_INFO_DELIVER: '/business/seller/orderInfo/deliver',
  GET_ORDER_INFO_MERGE_DELIVER: '/business/seller/orderInfo/mergeDeliver',
  GET_ORDER_INFO_EXPORT: '/business/seller/orderInfo/export',
  GET_ORDER_INFO_BATCH_DELIVER: '/business/seller/orderInfo/batchDeliver',
  // 自提点
 
  
 
};

// dev_supplier-start
if(userStore.getStoreInfo.shopType&&userStore.getStoreInfo.shopType=='3'){
  Api.GET_ORDER_INFO_LIST = '/supplier/seller/supplierOrder/list'
  Api.GET_ORDER_INFO_DETAIL = '/supplier/seller/supplierOrder/detail'
  Api.GET_ORDER_INFO_GET_TRACE = '/supplier/seller/supplierLogistics/order/getTrace'
  Api.GET_ORDER_INFO_REMARK = '/supplier/seller/supplierOrder/remark'
  Api.GET_ORDER_INFO_STAR = '/supplier/seller/supplierOrder/star'
  Api.GET_ORDER_INFO_UPDATE_PRICE = '/supplier/seller/supplierOrder/updatePrice'
  Api.GET_ORDER_INFO_CANCEL = '/supplier/seller/supplierOrder/cancel'
  Api.GET_ORDER_INFO_ORDER_PRODUCT_LIST = '/supplier/seller/supplierOrder/orderProductList'
  Api.GET_ORDER_INFO_DELIVER = '/supplier/seller/supplierOrder/deliver'
  Api.GET_ORDER_INFO_MERGE_DELIVER = '/supplier/seller/supplierOrder/mergeDeliver'
  Api.GET_ORDER_INFO_EXPORT = '/supplier/seller/supplierOrder/export'
  Api.GET_ORDER_INFO_BATCH_DELIVER = '/supplier/seller/supplierOrder/batchDeliver'
}
// dev_supplier-end


//slodon_获取订单列表
export const getOrderListApi = (params) =>
  defHttp.get({
    url: Api.GET_ORDER_INFO_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_获取订单详情
export const getOrderDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_ORDER_INFO_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取物流轨迹
export const getOrderGetTraceApi = (params) =>
  defHttp.get({
    url: Api.GET_ORDER_INFO_GET_TRACE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_添加订单备注
export const getOrderRemarkApi = (params) =>
  defHttp.post({
    url: Api.GET_ORDER_INFO_REMARK,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_订单设置星级
export const getOrderStarApi = (params) =>
  defHttp.post({
    url: Api.GET_ORDER_INFO_STAR,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_订单改价
export const getOrderUpdatePriceApi = (params) =>
  defHttp.post({
    url: Api.GET_ORDER_INFO_UPDATE_PRICE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取理由
export const getSettingReasonListApi = (params) =>
  defHttp.get({
    url: Api.GET_SETTING_REASON_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//取消订单接口
export const getOrderInfoCancelApi = (params) =>
  defHttp.post({
    url: Api.GET_ORDER_INFO_CANCEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取订单待发货产品列表
export const getOrderInfoOrderProductListApi = (params) =>
  defHttp.get({
    url: Api.GET_ORDER_INFO_ORDER_PRODUCT_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取快递公司
export const getOrderInfoExpressListApi = (params) =>
  defHttp.get({
    url: Api.GET_ORDER_INFO_EXPRESS_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//确认发货
export const getOrderInfoDeliverApi = (params) =>
  defHttp.post({
    url: Api.GET_ORDER_INFO_DELIVER,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_合并发货
export const getOrderInfoMergeDeliverApi = (params) =>
  defHttp.post({
    url: Api.GET_ORDER_INFO_MERGE_DELIVER,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });

//slodon_导出订单
export const getOrderInfoExportApi = (params) =>
  defHttp.get({
    url: Api.GET_ORDER_INFO_EXPORT,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
    responseType: 'blob',
  });

//slodon_批量发货，只针对已经生成电子面单的订单
export const getOrderInfoBatchDeliverApi = (params) =>
  defHttp.post({
    url: Api.GET_ORDER_INFO_BATCH_DELIVER,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });


