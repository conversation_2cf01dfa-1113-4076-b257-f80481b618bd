import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  GET_SELLER_GOODS_COMMENT_LIST: '/goods/seller/goodsComment/list',
  GET_SELLER_GOODS_COMMENT_EDIT_REPLY: '/goods/seller/goodsComment/editReply',
  GET_SELLER_STORE_COMMENT_LIST: '/seller/seller/storeComment/list',
};

//slodon_获取商品评价列表
export const getSellerGoodsCommentListApi = (params) =>
  defHttp.get({
    url: Api.GET_SELLER_GOODS_COMMENT_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_商家回复评价
export const getSellerGoodsCommentEditReplyApi = (params) =>
  defHttp.post({
    url: Api.GET_SELLER_GOODS_COMMENT_EDIT_REPLY,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取店铺评价列表
export const getSellerStoreCommentListApi = (params) =>
  defHttp.get({
    url: Api.GET_SELLER_STORE_COMMENT_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
