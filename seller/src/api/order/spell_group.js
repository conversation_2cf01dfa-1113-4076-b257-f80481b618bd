import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  GET_SELLER_SPELL_LIST: '/promotion/seller/spell/order/spellList',
};

//slodon_获取拼团订单列表
export const getSellerSpellListApi = (params) =>
  defHttp.get({
    url: Api.GET_SELLER_SPELL_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
