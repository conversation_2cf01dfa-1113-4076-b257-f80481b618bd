import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  GET_SELLER_ADDRESS_LIST: '/seller/seller/address/list',
  GET_SELLER_ADDRESS_ADD: '/seller/seller/address/add',
  GET_SELLER_ADDRESS_EDIT: '/seller/seller/address/update',
  GET_SELLER_ADDRESS_DEL: '/seller/seller/address/delete',
  GET_SELLER_ADDRESS_IS_DEFAULT: '/seller/seller/address/isDefault',
};

//slodon_获取发货/退货地址列表
export const getSellerAddressListApi = (params) =>
  defHttp.get({
    url: Api.GET_SELLER_ADDRESS_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_添加发货/退货地址
export const getSellerAddressAddApi = (params) =>
  defHttp.post({
    url: Api.GET_SELLER_ADDRESS_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_编辑发货/退货地址
export const getSellerAddressEditApi = (params) =>
  defHttp.post({
    url: Api.GET_SELLER_ADDRESS_EDIT,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_删除发货/退货地址
export const getSellerAddressDelApi = (params) =>
  defHttp.post({
    url: Api.GET_SELLER_ADDRESS_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_设置默认发货/退货地址
export const getSellerAddressIsDefaultApi = (params) =>
  defHttp.post({
    url: Api.GET_SELLER_ADDRESS_IS_DEFAULT,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
