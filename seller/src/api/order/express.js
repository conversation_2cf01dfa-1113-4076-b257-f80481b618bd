import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  GET_GOODS_FREIGHT_TEMPLATE: '/goods/seller/goodsFreightTemplate/list',
  GET_GOODS_FREIGHT_DEL: '/goods/seller/goodsFreightTemplate/delete',
  GET_GOODS_SETTING_DETAIL: '/seller/seller/store/settingDetail',
  GET_GOODS_SET_FREE_FREIGHT: '/seller/seller/logistics/setFreeFreight',
  GET_SELLER_EXPRESS_LIST: '/seller/seller/express/list',
  GET_SELLER_EXPRESS_DEL: '/seller/seller/express/delete',
  GET_SELLER_EXPRESS_OPEN_CLOSE: '/seller/seller/express/openOrCloseExpress',
  GET_SELLER_EXPRESS_ADD: '/seller/seller/express/add',
  GET_SELLER_EXPRESS_SHEET_CONFIG: '/seller/seller/express/sheetConfig',
  GET_SELLER_EXPRESS_FREIGHT_UPDATE: '/goods/seller/goodsFreightTemplate/update',
  GET_SELLER_EXPRESS_FREIGHT_ADD: '/goods/seller/goodsFreightTemplate/add',
  GET_SELLER_EXPRESS_FREIGHT_DETAIL: '/goods/seller/goodsFreightTemplate/detail',
  GET_SELLER_STORE_EXTEND_DETAIL: '/seller/seller/storeExtend/detail',
  GET_SELLER_STORE_EXTEND_UPDATE: '/seller/seller/storeExtend/update',
};

//slodon_获取店铺运费模板列表
export const getGoodsFreightTemplateListApi = (params) =>
  defHttp.get({
    url: Api.GET_GOODS_FREIGHT_TEMPLATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_删除运费模板
export const getGoodsFreightTemplateDelApi = (params) =>
  defHttp.post({
    url: Api.GET_GOODS_FREIGHT_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

// 获取运费设置
export const getGoodsSettingDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_GOODS_SETTING_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_设置免运费额度
export const getGoodsSetFreeFreightApi = (params) =>
  defHttp.post({
    url: Api.GET_GOODS_SET_FREE_FREIGHT,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_获取商户物流公司列表
export const getSellerExpressListApi = (params) =>
  defHttp.get({
    url: Api.GET_SELLER_EXPRESS_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_删除物流公司
export const getSellerExpressDelApi = (params) =>
  defHttp.post({
    url: Api.GET_SELLER_EXPRESS_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_开启/关闭物流公司
export const getSellerExpressOpenCloseApi = (params) =>
  defHttp.post({
    url: Api.GET_SELLER_EXPRESS_OPEN_CLOSE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_添加物流公司
export const getSellerExpressAddApi = (params) =>
  defHttp.post({
    url: Api.GET_SELLER_EXPRESS_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_电子面单设置
export const getSellerExpressSheetConfigApi = (params) =>
  defHttp.post({
    url: Api.GET_SELLER_EXPRESS_SHEET_CONFIG,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_编辑运费模板
export const getSellerExpressFreightUpdateApi = (params) =>
  defHttp.post({
    url: Api.GET_SELLER_EXPRESS_FREIGHT_UPDATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_添加运费模板
export const getSellerExpressFreightAddApi = (params) =>
  defHttp.post({
    url: Api.GET_SELLER_EXPRESS_FREIGHT_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取运费模板详情
export const getSellerExpressFreightDetailApi = (params) =>
  defHttp.post({
    url: Api.GET_SELLER_EXPRESS_FREIGHT_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_店铺设置详情
export const getSellerStoreExtendDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_SELLER_STORE_EXTEND_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

  //slodon_保存店铺设置
export const getSellerStoreExtendUpdateApi = (params) =>
defHttp.post({
  url: Api.GET_SELLER_STORE_EXTEND_UPDATE,
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.JSON,
  },
});