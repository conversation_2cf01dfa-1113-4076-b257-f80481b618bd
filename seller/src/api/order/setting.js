import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  GET_SELLER_EXPRESS_DETAIL: '/seller/seller/express/setting/detail',
  GET_SELLER_EXPRESS_SAVE: '/seller/seller/express/setting/save',
};

//获取快递鸟设置详情
export const getSellerExpressDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_SELLER_EXPRESS_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//保存快递鸟设置
export const getSellerExpressSaveApi = (params) =>
  defHttp.post({
    url: Api.GET_SELLER_EXPRESS_SAVE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
