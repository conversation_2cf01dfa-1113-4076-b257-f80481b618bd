import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
// dev_supplier-start
import { useUserStore } from '/@/store/modules/user';
const userStore = useUserStore();
// dev_supplier-end

const Api = {
  GET_SELLER_AFTER_SALE_LIST: '/business/seller/after/sale/list',
  GET_SELLER_AFTER_SALE_EXPORT: '/business/seller/after/sale/export',
  GET_SELLER_AFTER_SALE_DETAIL: '/business/seller/after/sale/detail',
  GET_SELLER_AFTER_SALE_AUDIT: '/business/seller/after/sale/audit',
  GET_SELLER_AFTER_SALE_CONFIRM_RECEIVE: '/business/seller/after/sale/confirmReceive',
  GET_SELLER_ADDRESS_LIST: '/seller/seller/address/list',
  GET_SELLER_AFS_GET_TRACE: '/business/seller/logistics/afs/getTrace',
};


// dev_supplier-start
if(userStore.getStoreInfo.shopType&&userStore.getStoreInfo.shopType=='3'){
  Api.GET_SELLER_AFTER_SALE_LIST = '/supplier/seller/supplierAfterSale/list'
  Api.GET_SELLER_AFTER_SALE_EXPORT = '/supplier/seller/supplierAfterSale/export'
  Api.GET_SELLER_AFTER_SALE_DETAIL = '/supplier/seller/supplierAfterSale/detail'
  Api.GET_SELLER_AFTER_SALE_AUDIT = '/supplier/seller/supplierAfterSale/audit'
  Api.GET_SELLER_AFTER_SALE_CONFIRM_RECEIVE = '/supplier/seller/supplierAfterSale/confirmReceive'
  Api.GET_SELLER_AFS_GET_TRACE = '/supplier/seller/supplierLogistics/afs/getTrace'
}
// dev_supplier-end

//slodon_退款退货列表
export const getSellerAfterSaleListApi = (params) =>
  defHttp.get({
    url: Api.GET_SELLER_AFTER_SALE_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//导出售后列表
export const getSellerAfterSaleExportApi = (params) =>
  defHttp.get({
    url: Api.GET_SELLER_AFTER_SALE_EXPORT,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
    responseType: 'blob',
  });

//退款/退货详情
export const getSellerAfterSaleDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_SELLER_AFTER_SALE_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//商家审核退款申请
export const getSellerAfterSaleAuditApi = (params) =>
  defHttp.post({
    url: Api.GET_SELLER_AFTER_SALE_AUDIT,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//退货退款 商家确认收货
export const getSellerAfterSaleConfirmReceiveApi = (params) =>
  defHttp.post({
    url: Api.GET_SELLER_AFTER_SALE_CONFIRM_RECEIVE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取发货/退货地址列表
export const getSellerAddressListApi = (params) =>
  defHttp.get({
    url: Api.GET_SELLER_ADDRESS_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取退货单物流轨迹
export const getSellerAfsGetTraceApi = (params) =>
  defHttp.get({
    url: Api.GET_SELLER_AFS_GET_TRACE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
