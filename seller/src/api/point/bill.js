import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  GET_INTEGRAL_BILL_LIST: '/integral/seller/integralBill/list',
  GET_INTEGRAL_BILL_DETAIL: '/integral/seller/integralBill/detail',
  GET_INTEGRAL_BILL_CONFIRM: '/integral/seller/integralBill/confirm',
  GET_INTEGRAL_BILL_CONFIRM_PAYMENT: '/integral/seller/integralBill/confirmPayment',
  GET_INTEGRAL_BILL_ORDER_DETAIL: '/integral/seller/integral/order/detail',
};

//slodon_获取结算单列表
export const getIntegralBillListApi = (params) =>
  defHttp.get({
    url: Api.GET_INTEGRAL_BILL_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取结算单详情
export const getIntegralBillDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_INTEGRAL_BILL_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_确认结算账单
export const getIntegralBillConfirmApi = (params) =>
  defHttp.post({
    url: Api.GET_INTEGRAL_BILL_CONFIRM,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_结算单_打款
export const getIntegralBillConfirmPaymentApi = (params) =>
  defHttp.post({
    url: Api.GET_INTEGRAL_BILL_CONFIRM_PAYMENT,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取订单详情
export const getIntegralBillOrderDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_INTEGRAL_BILL_ORDER_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
