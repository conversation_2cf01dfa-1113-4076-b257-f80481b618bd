import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  GET_GOODS_DOWNLOAD: '/integral/seller/integral/goods/download',
  GET_GOODS_PROCESS: '/integral/seller/integral/goods/process',
  GET_INTEGRAL_GOODS_LABEL_LIST: '/integral/seller/integral/goods/labelList',
};


//slodon_获取平台积分标签列表
export const getIntegralGoodsLabelListApi = (params) =>
  defHttp.get({
    url: Api.GET_INTEGRAL_GOODS_LABEL_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_下载积分商品导入模板
export const getPointGoodsDownLoadApi = (params) =>
  defHttp.get({
    url: Api.GET_GOODS_DOWNLOAD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
    responseType: 'blob',
  });

//slodon_获取积分上传产品文件结果
export const getPointGoodsProcessApi = (params) =>
  defHttp.get({
    url: Api.GET_GOODS_PROCESS,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });


