import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  GET_INTEGRAL_BILL_ORDER_DETAIL: '/integral/seller/integral/order/detail',
  GET_INTEGRAL_BILL_LIST: '/integral/seller/integral/order/list',
  GET_EXPRESS_LIST: '/seller/seller/express/list',
  GET_INTEGRAL_PRINT: '/integral/seller/integral/faceSheet/print',
  GET_INTEGRAL_GET_LOGISTIC_CODE: '/integral/seller/integral/faceSheet/getLogisticCode',
  GET_INTEGRAL_ORDER_DELIVER: '/integral/seller/integral/order/deliver',
  GET_INTEGRAL_ORDER_GET_TRACE: '/integral/seller/integral/order/getTrace',
};

//slodon_获取订单详情
export const getIntegralBillOrderDetailApi = (params) =>
  defHttp.post({
    url: Api.GET_INTEGRAL_BILL_ORDER_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取订单列表
export const getOrderPointListApi = (params) =>
  defHttp.get({
    url: Api.GET_INTEGRAL_BILL_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取快递公司
export const getExpressListApi = (params) =>
  defHttp.get({
    url: Api.GET_EXPRESS_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_打印电子面单
export const getIntegralPrintApi = (params) =>
  defHttp.post({
    url: Api.GET_INTEGRAL_PRINT,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_电子面单获取物流单号
export const getIntegralLogisticCodeApi = (params) =>
  defHttp.get({
    url: Api.GET_INTEGRAL_GET_LOGISTIC_CODE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_确认发货
export const getIntegralOrderDeliverApi = (params) =>
  defHttp.post({
    url: Api.GET_INTEGRAL_ORDER_DELIVER,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取物流轨迹
export const getIntegralOrderGetTraceApi = (params) =>
  defHttp.get({
    url: Api.GET_INTEGRAL_ORDER_GET_TRACE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
