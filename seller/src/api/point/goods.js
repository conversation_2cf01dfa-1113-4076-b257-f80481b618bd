import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  GET_INTEGRAL_GOODS_LIST: '/integral/seller/integral/goods/list',
  GET_INTEGRAL_GOODS_LOCK_UP: '/integral/seller/integral/goods/lockup',
  GET_INTEGRAL_GOODS_DELETE_GOODS: '/integral/seller/integral/goods/deleteGoods',
  GET_INTEGRAL_GOODS_UPPER_SHELF: '/integral/seller/integral/goods/upperShelf',
  GET_INTEGRAL_GOODS_LABEL_LIST: '/integral/seller/integral/goods/labelList',
  GET_INTEGRAL_GOODS_IMPORT_GOODS: '/integral/seller/integral/goods/importGoods',
  GET_INTEGRAL_GOODS_SPEC_LIST: '/integral/seller/integral/goodsSpec/list',
  GET_INTEGRAL_GOODS_SPEC_ADD_SPEC_VALUE: '/integral/seller/integral/goodsSpec/addSpecValue',
  GET_INTEGRAL_GOODS_SPEC_ADD: '/integral/seller/integral/goodsSpec/add',
  GET_INTEGRAL_GOODS_ADD: '/integral/seller/integral/goods/add',
  GET_INTEGRAL_GOODS_EDIT: '/integral/seller/integral/goods/update',
  GET_INTEGRAL_GOODS_DETAIL: '/integral/seller/integral/goods/detail',
};

//slodon_获取商品列表
export const getIntegralGoodsListApi = (params) =>
  defHttp.get({
    url: Api.GET_INTEGRAL_GOODS_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_违规下架商品
export const getIntegralGoodsLockUpApi = (params) =>
  defHttp.post({
    url: Api.GET_INTEGRAL_GOODS_LOCK_UP,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_删除商品
export const getIntegralGoodsDeleteGoodsApi = (params) =>
  defHttp.post({
    url: Api.GET_INTEGRAL_GOODS_DELETE_GOODS,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_上架商品
export const getIntegralGoodsUpperShelfApi = (params) =>
  defHttp.post({
    url: Api.GET_INTEGRAL_GOODS_UPPER_SHELF,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取平台积分标签列表
export const getIntegralGoodsLabelListApi = (params) =>
  defHttp.get({
    url: Api.GET_INTEGRAL_GOODS_LABEL_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_导入商城商品
export const getIntegralGoodsImportGoodsApi = (params) =>
  defHttp.post({
    url: Api.GET_INTEGRAL_GOODS_IMPORT_GOODS,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });

//slodon_规格列表
export const getIntegralGoodsSpecListApi = (params) =>
  defHttp.get({
    url: Api.GET_INTEGRAL_GOODS_SPEC_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_新增规格值
export const getIntegralGoodsSpecAddSpecValueApi = (params) =>
  defHttp.post({
    url: Api.GET_INTEGRAL_GOODS_SPEC_ADD_SPEC_VALUE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_新增规格
export const getIntegralGoodsSpecAddSpecAddApi = (params) =>
  defHttp.post({
    url: Api.GET_INTEGRAL_GOODS_SPEC_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_发布商品
export const getIntegralGoodsAddApi = (params) =>
  defHttp.post({
    url: Api.GET_INTEGRAL_GOODS_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });

//slodon_编辑商品
export const getIntegralGoodsEditApi = (params) =>
  defHttp.post({
    url: Api.GET_INTEGRAL_GOODS_EDIT,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });

//slodon_商品详情
export const getIntegralGoodsDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_INTEGRAL_GOODS_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });
