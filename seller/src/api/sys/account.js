import { defHttp } from '/@/utils/http/axios';
import base64Encrypt from '/@/utils/base64Encrypt';
var Api;
(function (Api) {
  Api['POST_LOGIN'] = '/sellerLogin/oauth/token';
  Api['POST_REGISTER'] = '/seller/seller/vendor/register';
  Api['POST_PWD'] = '/seller/seller/vendor/retrievePwd';
  Api['POST_SMS'] = '/msg/seller/commons/smsCode';
  Api['POST_UPDATE_PWD'] = '/seller/seller/vendor/updatePwd';
  Api['POST_BIND_MOBILE'] = '/seller/seller/vendor/bindMobile';
  })(Api || (Api = {}));
//账号登录
export const accountLogin = (params) => {
  params.password = base64Encrypt(params.password);
  return defHttp.post({ url: Api.POST_LOGIN, params });
};
//手机登录
export const phoneLogin = (params) => {
  return defHttp.post({ url: Api.POST_LOGIN, params });
};
//注册
export const registerPost = (params) => {
  params.vendorPassword = base64Encrypt(params.vendorPassword);
  params.confirmPassword = base64Encrypt(params.confirmPassword);
  return defHttp.post({ url: Api.POST_REGISTER, params });
};
//找回密码
export const retrievePwd = (params) => {
  params.newPwd = base64Encrypt(params.newPwd);
  params.confirmPwd = base64Encrypt(params.confirmPwd);
  return defHttp.post({ url: Api.POST_PWD, params });
};
//获取验证码
export const getSmsCode = (params) => {
  return defHttp.post({ url: Api.POST_SMS, params });
};
//修改密码
export const updatePwdApi = (params) => {
  return defHttp.post({ url: Api.POST_UPDATE_PWD, params });
};
//登录绑定手机号
export const bindMobileApi = (params) => {
  return defHttp.post({ url: Api.POST_BIND_MOBILE, params });
};
