import { defHttp } from '/@/utils/http/axios';
// dev_supplier-start
import { useUserStore } from '/@/store/modules/user';
const userStore = useUserStore();
// dev_supplier-end

const api = {
  GET_TODAY_USER: '/statistics/seller/overview/analysis/userInfo',
  GET_PRESENT_DATA: '/statistics/seller/present/analysis/presentData',
  GET_TRADE_VIEW: '/statistics/seller/trade/analysis/tradeOverview',
  GET_TRADE_REPORT: '/statistics/seller/trade/analysis/tradeReport',
  GET_FLOW_REPORT: '/statistics/seller/flow/analysis/flowReport',
  GET_FLOW_OVERVIEW: '/statistics/seller/flow/analysis/flowOverview',
  GET_GOODS_OVERVIEW: '/statistics/seller/goods/analysis/goodsOverview',
  GET_DAY_REPORT: '/statistics/seller/goods/analysis/dayReport',
  GET_GOODS_REPORT: '/statistics/seller/goods/analysis/goodsReport',
  GET_MEMBER_OVER_VIEW: '/statistics/seller/member/analysis/goodsOverview',
  GET_MEMBER_NUM: '/statistics/seller/member/analysis/memberNum', 
  GET_MEMBER_REPORT: '/statistics/seller/member/analysis/memberReport',
  GET_MEMBER_DAY_REPORT: '/statistics/seller/member/analysis/dayReport',
  GET_TOTAY_SUMMARY: '/statistics/seller/overview/analysis/presentData',
  GOODS_NUM: "/statistics/seller/goods/analysis/goodsNum",

  GET_PAYTREND: '/statistics/seller/overview/analysis/payOrderTrend',
  GET_VIEWTREND: '/statistics/seller/overview/analysis/flowTrend',
  GET_GOODS_RANK: '/statistics/seller/trade/analysis/goodsSalesRank',

  // 页面直接调用了api的
  GET_30_TREND: '/statistics/seller/trade/analysis/changeTrend',
  GET_30_FLOW_TREND: '/statistics/seller/flow/analysis/changeTrend',
  GET_GOODS_SALE_TREND: '/statistics/seller/goods/analysis/goodsSalesTrend',
  GET_MEMBER_30_TREND: '/statistics/seller/member/analysis/changeTrend',
  GET_PREFER_GOODS_RANK: '/statistics/seller/member/analysis/preferGoodsRank',
  GET_ORDER_TREND: '/statistics/seller/trade/analysis/orderTrend',
  GET_ORDER_PAY_TREND: '/statistics/seller/member/analysis/orderPayTrend',
  TRADE_EXPORT: "/statistics/seller/trade/analysis/export",
  FLOW_EXPORT: "/statistics/seller/flow/analysis/export",
  GOODS_DAY_EXPORT: "/statistics/seller/goods/analysis/dayExport",
  GOODS_EXPORT: "/statistics/seller/goods/analysis/goodsExport",
  MEMBER_DAY_EXPORT: "/statistics/seller/member/analysis/dayExport",
  MEMBER_EXPORT: "/statistics/seller/member/analysis/memberExport",
  GET_GOODS_SALE_RANK: '/statistics/seller/goods/analysis/goodsSalesRank',

}

// dev_supplier-start
if(userStore.getStoreInfo.shopType&&userStore.getStoreInfo.shopType=='3'){
  api.GET_PRESENT_DATA = '/supplier/seller/supplierPresentAnalysis/presentData'
  api.GET_30_TREND = '/supplier/seller/supplierTradeAnalysis/changeTrend'
  api.TRADE_EXPORT = '/supplier/seller/supplierTradeAnalysis/export'
  api.GET_GOODS_RANK = '/supplier/seller/supplierTradeAnalysis/goodsSalesRank'
  api.GET_ORDER_TREND = '/supplier/seller/supplierTradeAnalysis/orderTrend'
  api.GET_TRADE_VIEW = '/supplier/seller/supplierTradeAnalysis/tradeOverview'
  api.GET_TRADE_REPORT = '/supplier/seller/supplierTradeAnalysis/tradeReport'
  api.GET_30_FLOW_TREND = '/supplier/seller/supplierFlowAnalysis/changeTrend'
  api.FLOW_EXPORT = '/supplier/seller/supplierFlowAnalysis/export'
  api.GET_FLOW_OVERVIEW = '/supplier/seller/supplierFlowAnalysis/flowOverview'
  api.GET_FLOW_REPORT = '/supplier/seller/supplierFlowAnalysis/flowReport'
  api.GOODS_DAY_EXPORT = '/supplier/seller/supplierGoodsAnalysis/dayExport'
  api.GET_DAY_REPORT = '/supplier/seller/supplierGoodsAnalysis/dayReport'
  api.GOODS_EXPORT = '/supplier/seller/supplierGoodsAnalysis/goodsExport'
  api.GOODS_NUM = '/supplier/seller/supplierGoodsAnalysis/goodsNum'
  api.GET_GOODS_OVERVIEW = '/supplier/seller/supplierGoodsAnalysis/goodsOverview'
  api.GET_GOODS_REPORT = '/supplier/seller/supplierGoodsAnalysis/goodsReport'
  api.GET_GOODS_SALE_RANK = '/supplier/seller/supplierGoodsAnalysis/goodsSalesRank'
  api.GET_GOODS_SALE_TREND = '/supplier/seller/supplierGoodsAnalysis/goodsSalesTrend'
  api.GET_MEMBER_30_TREND = '/supplier/seller/supplierUserAnalysis/changeTrend'
  api.MEMBER_DAY_EXPORT = '/supplier/seller/supplierUserAnalysis/dayExport'
  api.GET_MEMBER_DAY_REPORT = '/supplier/seller/supplierUserAnalysis/dayReport'
  api.GET_MEMBER_OVER_VIEW = '/supplier/seller/supplierUserAnalysis/goodsOverview'
  api.MEMBER_EXPORT = '/supplier/seller/supplierUserAnalysis/memberExport'
  api.GET_MEMBER_NUM = '/supplier/seller/supplierUserAnalysis/memberNum'
  api.GET_MEMBER_REPORT = '/supplier/seller/supplierUserAnalysis/memberReport'
  api.GET_ORDER_PAY_TREND = '/supplier/seller/supplierUserAnalysis/orderPayTrend'
  api.GET_PREFER_GOODS_RANK = '/supplier/seller/supplierUserAnalysis/preferGoodsRank'
  api.GET_VIEWTREND = '/supplier/seller/supplierOverviewAnalysis/flowTrend'
  api.GET_PAYTREND = '/supplier/seller/supplierOverviewAnalysis/payOrderTrend'
  api.GET_TOTAY_SUMMARY = '/supplier/seller/supplierOverviewAnalysis/presentData'
}
// dev_supplier-end

export const API = api;

export const getStat_UserInfo = () => defHttp.get({ url: API.GET_TODAY_USER })
export const getPresentStat = (params) => defHttp.get({ url: API.GET_PRESENT_DATA, params });
export const getTradeViewStat = (params) => defHttp.get({ url: API.GET_TRADE_VIEW, params });
export const getFlowOverView = (params) => defHttp.get({ url: API.GET_FLOW_OVERVIEW, params });
export const getTradeReport = (params) => defHttp.get({ url: API.GET_TRADE_REPORT, params });
export const getFlowReport = (params) => defHttp.get({ url: API.GET_FLOW_REPORT, params });
export const getGoodsOverView = (params) => defHttp.get({ url: API.GET_GOODS_OVERVIEW, params });
export const getDayReport = (params) => defHttp.get({ url: API.GET_DAY_REPORT, params });
export const getGoodsReport = (params) => defHttp.get({ url: API.GET_GOODS_REPORT, params });
export const getMemberOverView = (params) => defHttp.get({ url: API.GET_MEMBER_OVER_VIEW, params });
export const getMemberNum = () => defHttp.get({ url: API.GET_MEMBER_NUM });
export const getMemberReport = (params) => defHttp.get({ url: API.GET_MEMBER_REPORT, params });
export const getMemberDayReport = (params) =>
  defHttp.get({ url: API.GET_MEMBER_DAY_REPORT, params });
export const getTodaySummaryApi = () => defHttp.get({ url: API.GET_TOTAY_SUMMARY });

export const getGoodsNumApi = () => defHttp.get({ url: API.GOODS_NUM });

