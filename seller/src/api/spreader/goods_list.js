import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  GET_SPREADER_GOODS_LIST: '/spreader/seller/spreaderGoods/list',
  GET_SPREADER_GOODS_LABEL_LIST: '/spreader/seller/spreaderGoodsLabel/list',
  GET_SPREADER_GOODS_ADD: '/spreader/seller/spreaderGoods/add',
  GET_SPREADER_GOODS_REMOVE_GOODS: '/spreader/seller/spreaderGoods/removeSpreaderGoods',
  GET_SPREADER_GOODS_BIND_LABEL: '/spreader/seller/spreaderGoods/bindLabel',
  GET_SPREADER_GOODS_SET_COMMISSION: '/spreader/seller/spreaderGoods/setCommission',
};

// slodon_获取商品列表
export const getSpreaderGoodsListApi = (params) =>
  defHttp.get({
    url: Api.GET_SPREADER_GOODS_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

// slodon_获取商品标签列表
export const getSpreaderGoodsLabelListApi = (params) =>
  defHttp.get({
    url: Api.GET_SPREADER_GOODS_LABEL_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_添加推手商品
export const getSpreaderGoodsAddApi = (params) =>
  defHttp.post({
    url: Api.GET_SPREADER_GOODS_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });

//slodon_不参与推手
export const getSpreaderGoodsRemoveGoodsApi = (params) =>
  defHttp.post({
    url: Api.GET_SPREADER_GOODS_REMOVE_GOODS,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_设置商品标签
export const getSpreaderGoodsBindLabelApi = (params) =>
  defHttp.post({
    url: Api.GET_SPREADER_GOODS_BIND_LABEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_设置商品佣金
export const getSpreaderGoodsSetCommissionApi = (params) =>
  defHttp.post({
    url: Api.GET_SPREADER_GOODS_SET_COMMISSION,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
