import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  GET_SPREADER_EXTEND_LIST: '/spreader/seller/spreaderOrder/list',
  GET_ORDER_INFO_DETAIL: '/business/seller/orderInfo/detail',
  GET_ORDER_INFO_GET_TRACE: '/business/seller/logistics/order/getTrace',
};

// slodon_获取订单列表
export const getSpreaderExtendListApi = (params) =>
  defHttp.get({
    url: Api.GET_SPREADER_EXTEND_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取订单详情
export const getOrderDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_ORDER_INFO_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取物流轨迹
export const getOrderGetTraceApi = (params) =>
  defHttp.get({
    url: Api.GET_ORDER_INFO_GET_TRACE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
