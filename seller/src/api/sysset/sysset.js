import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  BASIC_SITE_SETTING: '/system/seller/setting/getBasicSiteSetting',
  STORE_SETTING: '/system/seller/setting/getStoreSetting',
  SETTING_DETAIL: '/seller/seller/store/settingDetail',
  UPDATE_SETTING: '/seller/seller/store/updateSetting',
};
//获取站点基本配置
export const getBasicSiteSetting = () => defHttp.get({ url: Api.BASIC_SITE_SETTING });

export const getStoreSettingApi = () => defHttp.get({ url: Api.STORE_SETTING });

export const getSettingDetailApi = () => defHttp.get({ url: Api.SETTING_DETAIL });

export const get_store_QR = () => defHttp.get({ url: '/seller/seller/store/indexStoreInfor' });

//保存站点基本配置
export const saveSetting = (params) =>
  defHttp.post({
    url: Api.UPDATE_SETTING,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });

export const saveSettingQR = (params) =>
  defHttp.post({
    url: '/seller/seller/store/updateStoreQRCode',
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
