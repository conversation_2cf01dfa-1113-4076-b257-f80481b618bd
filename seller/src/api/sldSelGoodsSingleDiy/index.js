import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
import pinia from '@/store/modules/pinia';
// dev_supplier-start
import { useUserStore } from '/@/store/modules/user';
const userStore = useUserStore(pinia);
// dev_supplier-end
var Api;
(function (Api) {
  Api['GET_COUPON_NORMAL_LIST'] = '/promotion/seller/coupon/sendList';
  Api['GET_SELLER_GOODS_LIST'] = '/goods/seller/goods/list';
  Api['GET_SELLER_EXPRESS_LIST'] = '/system/seller/express/expressList';
  // dev_supplier-start
  if(userStore&&userStore.getStoreInfo&&userStore.getStoreInfo.shopType&&userStore.getStoreInfo.shopType=='3'){
    Api['GET_COUPON_NORMAL_LIST'] = '/supplier/seller/supplierCoupon/sendList';
    Api['GET_SELLER_GOODS_LIST'] = '/supplier/seller/supplierGoods/list';
  }
  // dev_supplier-end
})(Api || (Api = {}));
//获取优惠券列表（只获取未开始和进行中的）
export const getCouponNormalListApi = (params) =>
  defHttp.get({
    url: Api.GET_COUPON_NORMAL_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取商品列表
export const getSellerGoodsListApi = (params) =>
  defHttp.get({
    url: Api.GET_SELLER_GOODS_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取可以添加的平台物流公司列表
export const getSellerExpressListApi = (params) =>
  defHttp.get({
    url: Api.GET_SELLER_EXPRESS_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
