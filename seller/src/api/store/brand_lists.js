import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  GET_BRAND_LIST: '/goods/seller/Brand/list',
  GET_GOODS_CATEGORY_LIST_BY_PID: '/goods/seller/goodsCategory/listByPId',
  GET_GOODS_BRAND_APPLY: '/goods/seller/Brand/apply',
  GET_DEL_BRAND: '/goods/seller/Brand/delete',
  GET_BRAND_DETAIL: '/goods/seller/Brand/detail',
  GET_BRAND_EDIT: '/goods/seller/Brand/update',
};

//slodon_获取商户品牌列表
export const getBrandList = (params) =>
  defHttp.get({
    url: Api.GET_BRAND_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_逐级获取商户绑定的商品分类,如果有下级，children返回空数组，否则返回null
export const getVendorGoodsCategoryByIdApi = (params) =>
  defHttp.get({
    url: Api.GET_GOODS_CATEGORY_LIST_BY_PID,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_申请品牌
export const getGoodsBrandApplyApi = (params) =>
  defHttp.post({
    url: Api.GET_GOODS_BRAND_APPLY,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_删除品牌
export const delBrandApi = (params) =>
  defHttp.post({
    url: Api.GET_DEL_BRAND,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取品牌详情
export const getBrandDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_BRAND_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_编辑品牌
export const getBrandEditApi = (params) =>
  defHttp.post({
    url: Api.GET_BRAND_EDIT,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
