import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
// dev_supplier-start
import { useUserStore } from '/@/store/modules/user';
const userStore = useUserStore();
// dev_supplier-end

const Api = {
  GET_MSG_LIST: '/msg/seller/msg/list',
  GET_MSG_DEL: '/msg/seller/msg/delete',
  GET_MSG_READ: '/msg/seller/msg/read',
  GET_MSG_SETTING_LIST: '/msg/seller/msg/setting/list',
  GET_MSG_SETTING_IS_RECEIVE: '/msg/seller/msg/setting/isReceive',
  GET_MSG_TPL_TYPE_LIST: '/msg/seller/msg/msgTplTypeList',
};

// dev_supplier-start
if(userStore.getStoreInfo.shopType&&userStore.getStoreInfo.shopType=='3'){
  Api.GET_MSG_SETTING_LIST = '/supplier/seller/supplierMsgSetting/list';
}
// dev_supplier-end

//slodon_获取消息列表
export const getMsgListApi = (params) =>
  defHttp.get({
    url: Api.GET_MSG_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_删除消息
export const getMsgDelApi = (params) =>
  defHttp.post({
    url: Api.GET_MSG_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_设置消息为已读状态
export const getMsgReadApi = (params) =>
  defHttp.post({
    url: Api.GET_MSG_READ,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取消息接收设置列表
export const getMsgSettingListApi = (params) =>
  defHttp.get({
    url: Api.GET_MSG_SETTING_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_设置消息的接收状态
export const getMsgSettingIsReceiveApi = (params) =>
  defHttp.post({
    url: Api.GET_MSG_SETTING_IS_RECEIVE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_消息模板类型列表
export const getMsgTplTypeListApi = (params) =>
  defHttp.get({
    url: Api.GET_MSG_TPL_TYPE_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
