import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  GET_STORE_INFO: '/seller/seller/store/indexStoreInfor',
  GET_STORE_CATEGORY: '/seller/seller/storeCategory/list',
  GET_VIDEO_LABEL_LIST: '//video/seller/video/label/list',
  GET_LIVE_LABEL_LIST: '/video/seller/video/live/label/list',
  GET_VIDEO_LIVE_LIST: '/video/seller/video/live/list',
};

//保存系统设置信息
export const getStoreInfoApi = (params) =>
  defHttp.get({
    url: Api.GET_STORE_INFO,
    params,
  });

export const cateInitApi = (params) =>
  defHttp.get({
    url: Api.GET_STORE_CATEGORY,
    params,
  });


//获取短视频标签列表
export const getVideoLabelListApi = (params) =>
  defHttp.get({
    url: Api.GET_VIDEO_LABEL_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取直播标签列表
export const getLiveLabelListApi = (params) =>
  defHttp.get({
    url: Api.GET_LIVE_LABEL_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

// slodon_获取直播列表
export const getVideoLiveListApi = (params) =>
  defHttp.get({
    url: Api.GET_VIDEO_LIVE_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
