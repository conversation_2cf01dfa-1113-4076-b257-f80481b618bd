import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  GET_LABEL_LIST: '/seller/seller/storeCategory/list',
  GET_IS_SHOW: '/seller/seller/storeCategory/isShow',
  GET_ADD: '/seller/seller/storeCategory/add',
  GET_UPDATE: '/seller/seller/storeCategory/update',
  GET_DEL: '/seller/seller/storeCategory/delete',
};

//slodon_获取分类列表
export const getLabelListApi = (params) =>
  defHttp.get({
    url: Api.GET_LABEL_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_分类是否显示的开关切换
export const getIsShowApi = (params) =>
  defHttp.post({
    url: Api.GET_IS_SHOW,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_添加分类
export const getAddApi = (params) =>
  defHttp.post({
    url: Api.GET_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_编辑分类
export const getUpdateApi = (params) =>
  defHttp.post({
    url: Api.GET_UPDATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_删除分类
export const getLabelDelApi = (params) =>
  defHttp.post({
    url: Api.GET_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
