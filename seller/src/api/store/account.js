import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
// dev_supplier-start
import { useUserStore } from '/@/store/modules/user';
const userStore = useUserStore();
// dev_supplier-end


const Api = {
  RESOURCE_LIST: '/seller/seller/resource/list',
  ROLE_MANAGE_LIST: '/seller/seller/roles/list',
  ADD_ROLE: '/seller/seller/roles/add',
  UPDATE_ROLE: '/seller/seller/roles/update',
  AUTH_ROLE: '/seller/seller/roles/saveRoleResource',
  ADMIN_USER_LIST: '/seller/seller/vendor/list',
  ADD_ADMIN_USER: '/seller/seller/vendor/add',
  UPDATE_ADMIN_USER: '/seller/seller/vendor/update',
  FREEZE_ADMIN_USER: '/seller/seller/vendor/isFreeze',
  RESET_ADMIN_USER: '/seller/seller/vendor/resetPassword',
  DEL_ADMIN_USER: '/seller/seller/vendor/delete',
  DELETE_ROLE: '/seller/seller/roles/delete',
  GET_SETTING_RECEIVE_LIST: '/msg/seller/msg/setting/receiveList',
  GET_SETTING_RECEIVE_SETTING: '/msg/seller/msg/setting/roleReceiveSetting',
};

// dev_supplier-start
if(userStore.getStoreInfo.shopType&&userStore.getStoreInfo.shopType=='3'){
  Api.GET_SETTING_RECEIVE_LIST = '/supplier/seller/supplierMsgSetting/receiveList';
}
// dev_supplier-end

//slodon_获取权限组管理资源列表数据
export const getRoleResource = (params) =>
  defHttp.get({
    url: Api.RESOURCE_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_获取权限组列表数据
export const getRoleManageList = (params) =>
  defHttp.get({
    url: Api.ROLE_MANAGE_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_添加权限组
export const addRole = (params) =>
  defHttp.post({
    url: Api.ADD_ROLE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_编辑权限组
export const updateRole = (params) =>
  defHttp.post({
    url: Api.UPDATE_ROLE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_授权权限组
export const authRole = (params) =>
  defHttp.post({
    url: Api.AUTH_ROLE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_删除权限组
export const delRole = (params) =>
  defHttp.post({
    url: Api.DELETE_ROLE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_权限管理_获取操作员列表
export const getAdminUserList = (params) =>
  defHttp.get({
    url: Api.ADMIN_USER_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_权限管理_添加操作员
export const addAdminUser = (params) =>
  defHttp.post({
    url: Api.ADD_ADMIN_USER,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_权限管理_编辑操作员
export const updateAdminUser = (params) =>
  defHttp.post({
    url: Api.UPDATE_ADMIN_USER,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_权限管理_冻结/解冻操作员
export const freezeAdminUser = (params) =>
  defHttp.post({
    url: Api.FREEZE_ADMIN_USER,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_权限管理_重置密码
export const resetAdminUser = (params) =>
  defHttp.post({
    url: Api.RESET_ADMIN_USER,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_权限管理_删除操作员
export const delAdminUser = (params) =>
  defHttp.post({
    url: Api.DEL_ADMIN_USER,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });


//slodon_获取全部消息类型
  export const getSettingReceiveList = (params) =>
    defHttp.get({
      url: Api.GET_SETTING_RECEIVE_LIST,
      params,
      headers: {
        // @ts-ignore
        'Content-Type': ContentTypeEnum.FORM_URLENCODED,
      },
  });

  
//slodon_权限管理_接收消息设置
export const getSettingReceiveTypeApi = (params) =>
  defHttp.post({
    url: Api.GET_SETTING_RECEIVE_SETTING,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });