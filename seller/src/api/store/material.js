import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  GET_FILE_CAPACITY: '/system/seller/material/file/capacity',
  GET_FIRST_CATEGORY: '/system/seller/material/category/firstCategory',
  GET_MATERIAL_FILE_LIST: '/system/seller/material/file/list',
  GET_MATERIAL_CATEGORY_LIST: '/system/seller/material/category/list',
  ADD_MATERIAL_CATEGORY: '/system/seller/material/category/add',
  UPDATE_MATERIAL_CATEGORY: '/system/seller/material/category/update',
  DEL_MATERIAL_CATEGORY: '/system/seller/material/category/delete',
  RENAME_MATERIAL: '/system/seller/material/file/rename',
  MOVETO_MATERIAL: '/system/seller/material/file/moveTo',
  CLEAN_MATERIAL: '/system/seller/material/file/cleanMaterial',
  FILE_BIND_CATEGORY: '/system/seller/material/file/fileBindCategory',
};

//获取素材中心容量
export const getFileCapacity = (params) => defHttp.get({ url: Api.GET_FILE_CAPACITY, params });

//获取素材中心列表
export const getFirstCategory = (params) => defHttp.get({ url: Api.GET_FIRST_CATEGORY, params });

//获取素材中心一级分类列表
export const getMaterialFileList = (params) =>
  defHttp.get({ url: Api.GET_MATERIAL_FILE_LIST, params });

//获取分类下级分类数据
export const getMaterialCategoryList = (params) =>
  defHttp.get({ url: Api.GET_MATERIAL_CATEGORY_LIST, params });

//添加素材分类
export const addMaterialCategory = (params) =>
  defHttp.post({
    url: Api.ADD_MATERIAL_CATEGORY,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//编辑素材分类
export const updateMaterialCategory = (params) =>
  defHttp.post({
    url: Api.UPDATE_MATERIAL_CATEGORY,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//删除素材分类
export const delMaterialCategory = (params) =>
  defHttp.post({
    url: Api.DEL_MATERIAL_CATEGORY,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//重命名素材名称
export const renameMaterial = (params) =>
  defHttp.post({
    url: Api.RENAME_MATERIAL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//移动素材分类
export const moveToMaterial = (params) =>
  defHttp.post({
    url: Api.MOVETO_MATERIAL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//删除素材分类
export const cleanMaterial = (params) =>
  defHttp.post({
    url: Api.CLEAN_MATERIAL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//上传素材绑定分类
export const fileBindCategory = (params) =>
  defHttp.post({
    url: Api.FILE_BIND_CATEGORY,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
