import { defHttp } from '@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const API = {
  GET_MATERIAL_CATEGORY: '/system/seller/material/category/list',
  GET_MATERIAL_FILES: '/system/seller/material/file/list',
  POST_CATEGORY_ADD: '/system/seller/material/category/add',
  POST_UPDATE_CATEGORY: '/system/seller/material/category/update',
  POST_MATERIAL_RENAME: '/system/seller/material/file/rename',
  POST_MOVETO_MATERIAL: '/system/seller/material/file/moveTo',
  POST_FILE_BIND_CATEGORY: '/system/seller/material/file/fileBindCategory',
};

export const get_material_center_category = (params) =>
  defHttp.get({ url: API.GET_MATERIAL_CATEGORY, params });

export const getMaterialListApi = (params) => defHttp.get({ url: API.GET_MATERIAL_FILES, params });

export const cleanMaterialFilesApi = (params) =>
  defHttp.post({
    url: API.GET_MATERIAL_FILES,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const addCategoryApi = (params) =>
  defHttp.post({
    url: API.POST_CATEGORY_ADD,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const updateCategoryApi = (params) =>
  defHttp.post({
    url: API.POST_UPDATE_CATEGORY,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const renameMaterialFiles = (params) =>
  defHttp.post({
    url: API.POST_MATERIAL_RENAME,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const moveToMaterialApi = (params) =>
  defHttp.post({
    url: API.POST_MOVETO_MATERIAL,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const uploadMaterialFiles = (params) =>
  defHttp.post({
    url: API.POST_FILE_BIND_CATEGORY,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });
