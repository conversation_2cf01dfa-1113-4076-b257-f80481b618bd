import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
import { getAppEnvConfig } from '/@/utils/env';
const { VITE_GLOB_BUS_API_URL } = getAppEnvConfig()

// dev_supplier-end

//AI问题列表
export const aiQuestionList = (params) =>
    defHttp.post({
        url: `/api/v1/aiQuestion/list`,
        params,
        headers: {
            // @ts-ignore
            'Content-Type': ContentTypeEnum.JSON
        }
    }, {
        apiUrl: VITE_GLOB_BUS_API_URL,
        urlPrefix: '/quanxi',
        isTransformResponse: false
    });

export const aiQuestionImport = (params) =>
    defHttp.post({
        url: `/api/v1/aiQuestion/import`,
        params,
        headers: {
            // @ts-ignore
            'Content-Type': ContentTypeEnum.FORM_DATA
        }
    }, {
        apiUrl: VITE_GLOB_BUS_API_URL,
        urlPrefix: '/quanxi',
        isTransformResponse: false
    });


//AI问题批量删除
export const aiQuestionBatchDelete = (params) =>
    defHttp.post({
        url: `/api/v1/aiQuestion/batchDelete`,
        params,
        headers: {
            // @ts-ignore
            'Content-Type': ContentTypeEnum.JSON
        }
    }, {
        apiUrl: VITE_GLOB_BUS_API_URL,
        urlPrefix: '/quanxi',
        isTransformResponse: false
    });
//AI问题详情
export const aiQuestionDetail = (params) =>
    defHttp.post({
        url: `/api/v1/aiQuestion/detail`,
        params,
        headers: {
            // @ts-ignore
            'Content-Type': ContentTypeEnum.JSON
        }
    }, {
        apiUrl: VITE_GLOB_BUS_API_URL,
        urlPrefix: '/quanxi',
        isTransformResponse: false
    });
//AI问题创建
export const createOrUpdateQuestion = (params) =>
    defHttp.post({
        url: `/api/v1/mall/createOrUpdateQuestion`,
        params,
        headers: {
            // @ts-ignore
            'Content-Type': ContentTypeEnum.JSON
        }
    }, {
        apiUrl: VITE_GLOB_BUS_API_URL,
        urlPrefix: '/quanxi',
        isTransformResponse: false
    });
export const aiQuestionCreate = (params) =>
    defHttp.post({
        url: `/api/v1/aiQuestion/create`,
        params,
        headers: {
            // @ts-ignore
            'Content-Type': ContentTypeEnum.JSON
        }
    }, {
        apiUrl: VITE_GLOB_BUS_API_URL,
        urlPrefix: '/quanxi',
        isTransformResponse: false
    });
//AI问题修改
export const aiQuestionUpdate = (params) =>
    defHttp.post({
        url: `/api/v1/aiQuestion/update`,
        params,
        headers: {
            // @ts-ignore
            'Content-Type': ContentTypeEnum.JSON
        }
    }, {
        apiUrl: VITE_GLOB_BUS_API_URL,
        urlPrefix: '/quanxi',
        isTransformResponse: false
    });
//AI模块列表
export const aiModuleList = (params) =>
    defHttp.post({
        url: `/api/v1/aiModule/list`,
        params,
        headers: {
            // @ts-ignore
            'Content-Type': ContentTypeEnum.JSON
        }
    }, {
        apiUrl: VITE_GLOB_BUS_API_URL,
        urlPrefix: '/quanxi',
        isTransformResponse: false
    });
//AI模块创建
export const aiModuleCreate = (params) =>
    defHttp.post({
        url: `/api/v1/aiModule/create`,
        params,
        headers: {
            // @ts-ignore
            'Content-Type': ContentTypeEnum.JSON
        }
    }, {
        apiUrl: VITE_GLOB_BUS_API_URL,
        urlPrefix: '/quanxi',
        isTransformResponse: false
    });
//AI模块修改
export const aiModuleUpdate = (params) =>
    defHttp.post({
        url: `/api/v1/aiModule/update`,
        params,
        headers: {
            // @ts-ignore
            'Content-Type': ContentTypeEnum.JSON
        }
    }, {
        apiUrl: VITE_GLOB_BUS_API_URL,
        urlPrefix: '/quanxi',
        isTransformResponse: false
    });
//AI模块删除
export const aiModuleDelete = (params) =>
    defHttp.post({
        url: `/api/v1/aiModule/delete`,
        params,
        headers: {
            // @ts-ignore
            'Content-Type': ContentTypeEnum.JSON
        }
    }, {
        apiUrl: VITE_GLOB_BUS_API_URL,
        urlPrefix: '/quanxi',
        isTransformResponse: false
    });

//合集创建
export const goodsCreate = (params) =>
    defHttp.post({
        url: `/api/v1/goodsCollector/create`,
        params,
        headers: {
            // @ts-ignore
            'Content-Type': ContentTypeEnum.JSON
        }
    }, {
        apiUrl: VITE_GLOB_BUS_API_URL,
        urlPrefix: '/quanxi',
        isTransformResponse: false
    });
export const goodsUpdate = (params) =>
    defHttp.post({
        url: `/api/v1/goodsCollector/update`,
        params,
        headers: {
            // @ts-ignore
            'Content-Type': ContentTypeEnum.JSON
        }
    }, {
        apiUrl: VITE_GLOB_BUS_API_URL,
        urlPrefix: '/quanxi',
        isTransformResponse: false
    });
export const goodsList = (params) =>
    defHttp.post({
        url: `/api/v1/goodsCollector/list`,
        params,
        headers: {
            // @ts-ignore
            'Content-Type': ContentTypeEnum.JSON
        }
    }, {
        apiUrl: VITE_GLOB_BUS_API_URL,
        urlPrefix: '/quanxi',
        isTransformResponse: false
    });
export const goodsDetail = (params) =>
    defHttp.post({
        url: `/api/v1/goodsCollector/detail`,
        params,
        headers: {
            // @ts-ignore
            'Content-Type': ContentTypeEnum.JSON
        }
    }, {
        apiUrl: VITE_GLOB_BUS_API_URL,
        urlPrefix: '/quanxi',
        isTransformResponse: false
    });
export const goodsDelete = (params) =>
    defHttp.post({
        url: `/api/v1/goodsCollector/delete`,
        params,
        headers: {
            // @ts-ignore
            'Content-Type': ContentTypeEnum.JSON
        }
    }, {
        apiUrl: VITE_GLOB_BUS_API_URL,
        urlPrefix: '/quanxi',
        isTransformResponse: false
    });