import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
// dev_supplier-start
import { useUserStore } from '/@/store/modules/user';
const userStore = useUserStore();
// dev_supplier-end

const Api = {
  GET_PLATFORM_GOODS_LISTS: '/goods/seller/goods/platform/list',
  GET_GOODS_CATEGORY_LISTS: '/goods/seller/goodsCategory/list',
  GET_GOODS_CATEGORY_BOTTOM_CATEGORY: '/goods/seller/goodsCategory/bottomCategory',
  GET_GOODS_DOWNLOAD: '/goods/seller/goods/download',
  GET_GOODS_PROCESS: '/goods/seller/goods/process',
  GET_GOODS_PLATFORM_DETAIL: '/goods/seller/goods/platform/detail',
  GET_GOODS_PLATFORM_GOODS_IMPORT: '/goods/seller/goods/platform/goodsImport',
};

// dev_supplier-start
if(userStore.getStoreInfo.shopType&&userStore.getStoreInfo.shopType=='3'){
  Api.GET_GOODS_DOWNLOAD = '/supplier/seller/supplierGoods/download'
  Api.GET_GOODS_PROCESS = '/supplier/seller/supplierGoods/process'
}
// dev_supplier-end

//slodon_获取商品资料库列表
export const getPlatformGoodsListsApi = (params) =>
  defHttp.get({
    url: Api.GET_PLATFORM_GOODS_LISTS,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取分类列表_根据分类id获取下级分类
export const getGoodsCategoryListsApi = (params) =>
  defHttp.get({
    url: Api.GET_GOODS_CATEGORY_LISTS,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_根据一级分类id获取该分类下的所有分类数据
export const getGoodsCategoryBottomCateGoryApi = (params) =>
  defHttp.get({
    url: Api.GET_GOODS_CATEGORY_BOTTOM_CATEGORY,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_下载商品导入模板
export const getGoodsDownLoadApi = (params) =>
  defHttp.get({
    url: Api.GET_GOODS_DOWNLOAD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
    responseType: 'blob',
  });

//slodon_获取上传产品文件结果
export const getGoodsProcessApi = (params) =>
  defHttp.get({
    url: Api.GET_GOODS_PROCESS,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取平台商品详情
export const getGoodsPlatformDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_GOODS_PLATFORM_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_导入平台商品
export const getGoodsPlatformGoodsImportApi = (params) =>
  defHttp.post({
    url: Api.GET_GOODS_PLATFORM_GOODS_IMPORT,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
