import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
// dev_supplier-start
import { useUserStore } from '/@/store/modules/user';
const userStore = useUserStore();
// dev_supplier-end
let Api = {
  GET_STORE_CAREGORY_LIST: '/seller/seller/storeCategory/list',
  GET_GOODS_RELATED_TEMPLATE_LIST: '/goods/seller/goodsRelatedTemplate/list',
  GET_GOODS_RELATED_TEMPLATE_DETAIL: '/goods/seller/goodsRelatedTemplate/details',
  ADD_GOODS_RELATED_TEMPLATE: '/goods/seller/goodsRelatedTemplate/add',
  EDIT_GOODS_RELATED_TEMPLATE: '/goods/seller/goodsRelatedTemplate/update',
  DEL_GOODS_RELATED_TEMPLATE: '/goods/seller/goodsRelatedTemplate/delete',
  GET_PARAMETER_GROUP_LIST: '/goods/seller/goodsParameterGroup/list',
  ADD_PARAMETER_GROUP: '/goods/seller/goodsParameterGroup/add',
  EDIT_PARAMETER_GROUP: '/goods/seller/goodsParameterGroup/update',
  DEL_PARAMETER_GROUP: '/goods/seller/goodsParameterGroup/delete',
  GET_PARAMETER_LIST: '/goods/seller/goodsParameter/list',
  ADD_PARAMETER: '/goods/seller/goodsParameter/add',
  EDIT_PARAMETER: '/goods/seller/goodsParameter/update',
  DEL_PARAMETER: '/goods/seller/goodsParameter/delete',
  GET_STORE_CATEGORY_LIST: '/seller/seller/storeCategory/list',
  DICT_DATA_LIST: '/system/seller/dictData/list',
  GET_GOODS_LIST: '/goods/seller/goods/list',
  GET_GOODS_LOCK_UP: '/goods/seller/goods/lockup',
  GET_GOODS_DELETE_GOODS: '/goods/seller/goods/deleteGoods',
  GET_GOODS_ISRECOMMEND: '/goods/seller/goods/isRecommend',
  SET_RELATED_TEMPLATE: '/goods/seller/goods/setRelatedTemplate',
  GOODS__UPPERSHELF: '/goods/seller/goods/upperShelf',
  ADD_PLATFORM_GOOD: '/goods/seller/goods/add',
  EDIT_PLATFORM_GOOD:'/goods/seller/goods/update',
  GET_GOODS_ENABLE_PUBLISH: '/goods/seller/goods/enablePublish',
  GET_PLATFORM_GOOD_DETAIL: '/goods/seller/goods/getGoodsInfo',
  // dev_supplier-start
  GET_SUPPLIER_GOODS_LIST: '/supplier/seller/supplierProduct/list',
  IMPORT_SUPPLIER_GOODS: '/supplier/seller/supplierProduct/goodsImport',
  GET_SUPPLIER_CATEGORY_LIST: '/goods/seller/goodsCategory/getCateTree',
  GET_SUPPLIER_GOODS_FREIGHT_INFO: '/supplier/seller/supplierGoods/getFreightInfo',
  GET_SUPPLIER_CATEGORY_TREE: '/goods/seller/goodsCategory/getCateTree',
  GET_SUPPLIER_GOODS_DETAIL: '/supplier/seller/supplierProduct/detail',
  GET_SUPPLIER_GOODS_PROCESS: '/supplier/seller/supplierProduct/process',
  // dev_supplier-end
}

// dev_supplier-start
if(userStore.getStoreInfo.shopType&&userStore.getStoreInfo.shopType=='3'){
  Api.GET_GOODS_LIST = '/supplier/seller/supplierGoods/list'
  Api.GET_GOODS_LOCK_UP = '//supplier/seller/supplierGoods/lockup'
  Api.GET_GOODS_DELETE_GOODS = '/supplier/seller/supplierGoods/deleteGoods'
  Api.GET_GOODS_ISRECOMMEND = '/supplier/seller/supplierGoods/isRecommend'
  Api.SET_RELATED_TEMPLATE = '/supplier/seller/supplierGoods/setRelatedTemplate'
  Api.GOODS__UPPERSHELF = '/supplier/seller/supplierGoods/upperShelf'
  Api.ADD_PLATFORM_GOOD = '/supplier/seller/supplierGoods/add'
  Api.EDIT_PLATFORM_GOOD ='/supplier/seller/supplierGoods/update'
  Api.GET_PLATFORM_GOOD_DETAIL = '/supplier/seller/supplierGoods/detail'
}
// dev_supplier-end

//slodon_获取商品列表
export const getGoodsListApi = (params) =>
  defHttp.get({
    url: Api.GET_GOODS_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取店铺分类列表
export const getStoreCategoryApi = (params) =>
  defHttp.get({
    url: Api.GET_STORE_CAREGORY_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_违规下架商品
export const getGoodsLockUpApi = (params) =>
  defHttp.post({
    url: Api.GET_GOODS_LOCK_UP,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_设置推荐
export const getGoodsIsRecommendApi = (params) =>
  defHttp.post({
    url: Api.GET_GOODS_ISRECOMMEND,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_删除商品
export const getGoodsDeleteGoodsApi = (params) =>
  defHttp.post({
    url: Api.GET_GOODS_DELETE_GOODS,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取关联样式列表
export const getGoodsRelatedTemplateListApi = (params) =>
  defHttp.get({ url: Api.GET_GOODS_RELATED_TEMPLATE_LIST, params });

//slodon_获取关联样式详情
export const getGoodsRelatedTemplateDetailApi = (params) =>
  defHttp.get({ url: Api.GET_GOODS_RELATED_TEMPLATE_DETAIL, params });

//slodon_新增关联样式
export const addGoodsRelatedTemplateApi = (params) =>
  defHttp.post({
    url: Api.ADD_GOODS_RELATED_TEMPLATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_编辑关联样式
export const editGoodsRelatedTemplateApi = (params) =>
  defHttp.post({
    url: Api.EDIT_GOODS_RELATED_TEMPLATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_删除关联样式
export const delGoodsRelatedTemplateApi = (params) =>
  defHttp.post({
    url: Api.DEL_GOODS_RELATED_TEMPLATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_设置关联版式
export const setRelatedTemplate = (params) =>
  defHttp.post({
    url: Api.SET_RELATED_TEMPLATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取自定义属性列表
export const getParameterGroupList = (params) =>
  defHttp.get({ url: Api.GET_PARAMETER_GROUP_LIST, params });

//slodon_新增自定义属性列表
export const addParameterGroup = (params) =>
  defHttp.post({
    url: Api.ADD_PARAMETER_GROUP,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
  
//slodon_编辑自定义属性列表
export const editParameterGroup = (params) =>
defHttp.post({
  url: Api.EDIT_PARAMETER_GROUP,
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
  },
});

//slodon_删除自定义属性列表
export const delParameterGroup = (params) =>
  defHttp.post({
    url: Api.DEL_PARAMETER_GROUP,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取属性列表
export const getParameterList = (params) =>
  defHttp.get({ url: Api.GET_PARAMETER_LIST, params });
  
//slodon_新增自定义属性列表
export const addParameter = (params) =>
  defHttp.post({
    url: Api.ADD_PARAMETER,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
  
//slodon_编辑自定义属性列表
export const editParameter = (params) =>
  defHttp.post({
    url: Api.EDIT_PARAMETER,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_删除自定义属性列表
export const delParameter = (params) =>
  defHttp.post({
    url: Api.DEL_PARAMETER,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_上架商品
export const goodsUpperShelf = (params) =>
  defHttp.post({
    url: Api.GOODS__UPPERSHELF,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

  
//新增商品资料
export const addPlatformGood = (params) =>
  defHttp.post({
    url: Api.ADD_PLATFORM_GOOD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });

//编辑商品资料
export const editPlatformGood = (params) =>
  defHttp.post({
    url: Api.EDIT_PLATFORM_GOOD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });

//是否可以发布商品
export const getEnablePublish = (params) =>
  defHttp.get({
    url: Api.GET_GOODS_ENABLE_PUBLISH,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });



//获取商品详情
export const getPlatformGoodDetail = (params) => defHttp.get({ url: Api.GET_PLATFORM_GOOD_DETAIL, params });

//数据字典列表
export const dictDataListApi = (params) => 
  defHttp.get({
    url: Api.DICT_DATA_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

// dev_supplier-start
//slodon_获取供应链商品列表
export const getSupplierGoodsLists = (params) =>
  defHttp.get({ url: Api.GET_SUPPLIER_GOODS_LIST, params });
  
//slodon_导入供应链商品
export const importSupplierGoods = (params) =>
  defHttp.post({
    url: Api.IMPORT_SUPPLIER_GOODS,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_获取供应链产品分类数据
export const getSupplierCategoryList = (params) =>
  defHttp.get({ url: Api.GET_SUPPLIER_CATEGORY_LIST, params });
  
//slodon_获取供应链商品运费详情
export const getSupplierGoodsFreightInfo = (params) =>
  defHttp.get({ url: Api.GET_SUPPLIER_GOODS_FREIGHT_INFO, params });
  
//获取供应链商品分类树接口
export const getSupplierCategoryTree = (params) =>
  defHttp.get({ url: Api.GET_SUPPLIER_CATEGORY_TREE, params });

//slodon_获取供应链商品详情
export const getSupplierGoodsDetail = (params) =>
  defHttp.get({ url: Api.GET_SUPPLIER_GOODS_DETAIL, params });

//slodon_供应链上架导入结果查询
export const getSupplierGoodsProcess = (params) =>
  defHttp.get({ url: Api.GET_SUPPLIER_GOODS_PROCESS, params });
  
// dev_supplier-end