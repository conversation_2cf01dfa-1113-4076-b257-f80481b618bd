import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

enum Api {
  GET_PROTOCOL = '/system/seller/agreement/detail',
  GET_STORE_GRADE = '/seller/seller/storeGrade/list',
  GET_STORE_OPEN_TIME = '/seller/seller/apply/openTime',
  GET_APPLY_DETAIL = '/seller/seller/apply/applyDetail',
  GET_APPLY_PROGRESS = '/seller/seller/apply/process',
  GET_INDUSTRY = '/seller/seller/apply/industryList',
  SAVE_APPLY = '/seller/seller/apply/saveApply',
  CATE_LIST = '/goods/seller/goodsCategory/getCateList',
  REGISTER_PAY = '/seller/seller/pay/registerPay',
}

//获取入驻协议
export const getProtocolApi = (params: {}) => defHttp.get<void>({ url: Api.GET_PROTOCOL, params });

//获取供应商等级
export const getStoreGradeApi = () => defHttp.get<void>({ url: Api.GET_STORE_GRADE });

//获取开店时长列表
export const getStoreOpenTimeApi = () => defHttp.get<void>({ url: Api.GET_STORE_OPEN_TIME });

//获取入驻信息
export const getApplyDetailApi = () => defHttp.get<void>({ url: Api.GET_APPLY_DETAIL });

//获取入驻进度
export const getApplyProgressApi = () => defHttp.get<void>({ url: Api.GET_APPLY_PROGRESS });

export const getApplyCategoryApi = () => defHttp.get<void>({ url: Api.CATE_LIST });

//获取行业列表
export const getIndustryApi = () => defHttp.get<void>({ url: Api.GET_INDUSTRY });

//保存入驻信息
export const saveStoreApplyApi = (params: {}) =>
  defHttp.post<void>({
    url: Api.SAVE_APPLY,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//支付
export const registerPayApi = (params: {}) =>
  defHttp.post<void>({
    url: Api.REGISTER_PAY,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
