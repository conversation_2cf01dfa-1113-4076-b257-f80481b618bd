import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
var Api;
(function (Api) {
  Api['GET_SPELL_LIST'] = '/promotion/seller/spell/list';
  Api['GET_SPELL_INVALID'] = '/promotion/seller/spell/invalid';
  Api['GET_SPELL_DEL'] = '/promotion/seller/spell/delete';
  Api['GET_SPELL_DETAIL'] = '/promotion/seller/spell/detail';
  Api['GET_SPELL_GOOD_LIST'] = '/promotion/seller/spell/goodList';
  Api['GET_SPELL_ORDER_LIST'] = '/promotion/seller/spell/order/list';
  Api['GET_SPELL_TEAM_LIST'] = '/promotion/seller/spell/teamList';
  Api['GET_SPELL_LABEL_LIST'] = '/promotion/seller/spell/labelList';
  Api['GET_SPELL_PUBLISH'] = '/promotion/seller/spell/publish';
  Api['GET_SPELL_ADD'] = '/promotion/seller/spell/add';
  Api['GET_SPELL_UPDATE'] = '/promotion/seller/spell/update';
  Api['GET_SPELL_DEL_GOODS'] = '/promotion/seller/spell/delGoods';
})(Api || (Api = {}));
//获取拼团活动列表
export const getSpellListApi = (params) =>
  defHttp.get({
    url: Api.GET_SPELL_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//失效拼团活动
export const getSpellInvalidApi = (params) =>
  defHttp.post({
    url: Api.GET_SPELL_INVALID,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//删除拼团活动
export const getSpellDelApi = (params) =>
  defHttp.post({
    url: Api.GET_SPELL_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取拼团活动详情
export const getSpellDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_SPELL_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取拼团活动的商品
export const getSpellGoodListApi = (params) =>
  defHttp.get({
    url: Api.GET_SPELL_GOOD_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取拼团活动的订单
export const getSpellOrderListApi = (params) =>
  defHttp.get({
    url: Api.GET_SPELL_ORDER_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取拼团团队列表
export const getSpellTeamListApi = (params) =>
  defHttp.get({
    url: Api.GET_SPELL_TEAM_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取拼团活动标签
export const getSpellLabelListApi = (params) =>
  defHttp.get({
    url: Api.GET_SPELL_LABEL_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_video_发布拼团活动
export const getSpellPublishApi = (params) =>
  defHttp.post({
    url: Api.GET_SPELL_PUBLISH,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_video_新建拼团活动
export const getSpellAddApi = (params) =>
  defHttp.post({
    url: Api.GET_SPELL_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_video_编辑拼团活动
export const getSpellUpdateApi = (params) =>
  defHttp.post({
    url: Api.GET_SPELL_UPDATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//删除拼团商品
export const getSpellDelGoodsApi = (params) =>
  defHttp.post({
    url: Api.GET_SPELL_DEL_GOODS,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
