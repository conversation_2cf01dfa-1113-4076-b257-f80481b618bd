import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
var Api;
(function (Api) {
  Api['GET_VIDEO_MEMBER_LIST'] = '/video/seller/video/member/list';
  Api['GET_VIDEO_MEMBER_BIND_MEMBER'] = '/video/seller/video/member/bindMember';
  Api['GET_VIDEO_MEMBER_DEL'] = '/video/seller/video/member/delete';
})(Api || (Api = {}));

//slodon_video_获取会员列表
export const getVideoMemberListApi = (params) =>
  defHttp.get({
    url: Api.GET_VIDEO_MEMBER_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_video_绑定视频会员
export const getVideoMemberBindMemberApi = (params) =>
  defHttp.post({
    url: Api.GET_VIDEO_MEMBER_BIND_MEMBER,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_video_删除视频会员
export const getVideoMemberDelApi = (params) =>
  defHttp.post({
    url: Api.GET_VIDEO_MEMBER_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
