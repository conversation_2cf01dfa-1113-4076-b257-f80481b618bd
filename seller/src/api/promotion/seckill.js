import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
// dev_supplier-start
import { useUserStore } from '/@/store/modules/user';
const userStore = useUserStore();
// dev_supplier-end

let Apis  = {
  GET_SECKILL_LIST: '/promotion/seller/seckill/list',
  GET_SECKILL_LABEL_LIST: '/promotion/seller/seckillLabel/list',
  GET_SECKILL_STAGE_LIST: '/promotion/seller/seckillStage/list',
  GET_SECKILL_GOODS_JOIN_LIST: '/promotion/seller/joinSeckill/goodsList',
  GET_SECKILL_JOIN_LIST: '/promotion/seller/joinSeckill/list',
  GET_SECKILL_JOIN_PRODUCT_LIST: '/promotion/seller/joinSeckill/productList',
  GET_SECKILL_DETAIL: '/promotion/seller/seckill/detail',
  GET_SECKILL_JOIN_SECKILL: '/promotion/seller/seckill/joinSeckill',
}

// dev_supplier-start
if(userStore.getStoreInfo.shopType&&userStore.getStoreInfo.shopType=='3'){
  Apis.GET_SECKILL_LIST = '/supplier/seller/supplierSeckill/list';
  Apis.GET_SECKILL_LABEL_LIST = '/supplier/seller/supplierSeckillLabel/list';
  Apis.GET_SECKILL_STAGE_LIST = '/supplier/seller/supplierSeckill/stageList';
  Apis.GET_SECKILL_GOODS_JOIN_LIST = '/supplier/seller/supplierJoinSeckill/goodsList';
  Apis.GET_SECKILL_JOIN_LIST = '/supplier/seller/supplierJoinSeckill/list';
  Apis.GET_SECKILL_JOIN_PRODUCT_LIST = '/supplier/seller/supplierJoinSeckill/productList';
  Apis.GET_SECKILL_DETAIL = '/supplier/seller/supplierSeckill/detail';
  Apis.GET_SECKILL_JOIN_SECKILL = '/supplier/seller/supplierSeckill/joinSeckill';
}
// dev_supplier-end

export const Api = Apis

//秒杀活动列表
export const getSeckillListApi = (params) =>
  defHttp.get({
    url: Api.GET_SECKILL_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取秒杀标签列表
export const getSeckillLabelListApi = (params) =>
  defHttp.get({
    url: Api.GET_SECKILL_LABEL_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取活动场次列表
export const getSeckilStageListApi = (params) =>
  defHttp.get({
    url: Api.GET_SECKILL_STAGE_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取秒杀商品的sku列表
export const getSeckilGoodsJoinListApi = (params) =>
  defHttp.get({
    url: Api.GET_SECKILL_GOODS_JOIN_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取已经参加的秒杀活动
export const getSeckilJoinListApi = (params) =>
  defHttp.get({
    url: Api.GET_SECKILL_JOIN_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取参加秒杀活动的商品的sku
export const getSeckilJoinProductListApi = (params) =>
  defHttp.get({
    url: Api.GET_SECKILL_JOIN_PRODUCT_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取秒杀活动详情
export const getSeckilDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_SECKILL_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_参加秒杀活动
export const getSeckilJoinSeckillApi = (params) =>
  defHttp.post({
    url: Api.GET_SECKILL_JOIN_SECKILL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });
