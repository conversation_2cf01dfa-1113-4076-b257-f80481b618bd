import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
var Api;
(function (Api) {
  Api['GET_DRAW_LIST'] = '/promotion/seller/draw/list';
})(Api || (Api = {}));
// slodon_获取抽奖活动列表
export const getDrawListApi = (params) =>
  defHttp.get({
    url: Api.GET_DRAW_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });