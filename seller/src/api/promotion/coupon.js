import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
// dev_supplier-start
import { useUserStore } from '/@/store/modules/user';
const userStore = useUserStore();
// dev_supplier-end
var Api;
(function (Api) {
  Api['GET_COUPON_LIST'] = '/promotion/seller/coupon/list';
  Api['GET_COUPON_RECEIVE_DETAILS'] = '/promotion/seller/coupon/receiveDetails';
  Api['GET_COUPON_INVALID'] = '/promotion/seller/coupon/invalid';
  Api['GET_COUPON_DEL'] = '/promotion/seller/coupon/delete';
  Api['GET_COUPON_DETAIL'] = '/promotion/seller/coupon/detail';
  Api['GET_COUPON_GOODS_LIST'] = '/promotion/seller/coupon/goodsList';
  Api['GET_COUPON_ADD'] = '/promotion/seller/coupon/add';
  Api['GET_COUPON_UPDATE'] = '/promotion/seller/coupon/update';
  // dev_supplier-start
  if(userStore.getStoreInfo.shopType&&userStore.getStoreInfo.shopType=='3'){
    Api['GET_COUPON_LIST'] = '/supplier/seller/supplierCoupon/list';
    Api['GET_COUPON_ADD'] = '/supplier/seller/supplierCoupon/add';
    Api['GET_COUPON_DEL'] = '/supplier/seller/supplierCoupon/delete';
    Api['GET_COUPON_DETAIL'] = '/supplier/seller/supplierCoupon/detail';
    Api['GET_COUPON_GOODS_LIST'] = '/supplier/seller/supplierCoupon/goodsList';
    Api['GET_COUPON_INVALID'] = '/supplier/seller/supplierCoupon/invalid';
    Api['GET_COUPON_RECEIVE_DETAILS'] = '/supplier/seller/supplierCoupon/receiveDetails';
    Api['GET_COUPON_UPDATE'] = '/supplier/seller/supplierCoupon/update';
  }
  // dev_supplier-end
})(Api || (Api = {}));

//优惠券列表
export const getCouponListApi = (params) =>
  defHttp.get({
    url: Api.GET_COUPON_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取优惠券领取列表
export const getCouponReceiveListsApi = (params) =>
  defHttp.get({
    url: Api.GET_COUPON_RECEIVE_DETAILS,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//失效优惠券
export const getCouponInvalidApi = (params) =>
  defHttp.post({
    url: Api.GET_COUPON_INVALID,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//删除优惠券
export const getCouponDelApi = (params) =>
  defHttp.post({
    url: Api.GET_COUPON_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取优惠券详情
export const getCouponDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_COUPON_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取优惠券商品列表
export const getCouponGoodsListApi = (params) =>
  defHttp.get({
    url: Api.GET_COUPON_GOODS_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//新增优惠券
export const getCouponAddApi = (params) =>
  defHttp.post({
    url: Api.GET_COUPON_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//编辑优惠券
export const getCouponUpdateApi = (params) =>
  defHttp.post({
    url: Api.GET_COUPON_UPDATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
