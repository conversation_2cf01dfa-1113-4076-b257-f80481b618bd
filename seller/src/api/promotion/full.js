import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
import pinia from '@/store/modules/pinia';
// dev_supplier-start
import { useUserStore } from '/@/store/modules/user';
const userStore = useUserStore(pinia);
// dev_supplier-end
var Api;
(function (Api) {
  Api['GET_FULL_ACM_LIST'] = '/promotion/seller/fullAcm/list';
  Api['GET_FULL_ACM_DETAIL'] = '/promotion/seller/fullAcm/detail';
  Api['GET_FULL_ACM_INVALID'] = '/promotion/seller/fullAcm/invalid';
  Api['GET_FULL_ACM_UPDATE'] = '/promotion/seller/fullAcm/update';
  Api['GET_FULL_ACM_ADD'] = '/promotion/seller/fullAcm/add';
  Api['GET_FULL_ACM_DEL'] = '/promotion/seller/fullAcm/delete';
  Api['GET_FULL_ACM_PUBLISH'] = '/promotion/seller/fullAcm/publish';
  Api['GET_FULL_ASM_LIST'] = '/promotion/seller/fullAsm/list';
  Api['GET_FULL_ASM_DETAIL'] = '/promotion/seller/fullAsm/detail';
  Api['GET_FULL_ASM_INVALID'] = '/promotion/seller/fullAsm/invalid';
  Api['GET_FULL_ASM_PUBLISH'] = '/promotion/seller/fullAsm/publish';
  Api['GET_FULL_ASM_DEL'] = '/promotion/seller/fullAsm/delete';
  Api['GET_FULL_ASM_UPDATE'] = '/promotion/seller/fullAsm/update';
  Api['GET_FULL_ASM_ADD'] = '/promotion/seller/fullAsm/add';
  Api['GET_FULL_ALD_LIST'] = '/promotion/seller/fullAld/list';
  Api['GET_FULL_ALD_DETAIL'] = '/promotion/seller/fullAld/detail';
  Api['GET_FULL_ALD_ADD'] = '/promotion/seller/fullAld/add';
  Api['GET_FULL_ALD_UPDATE'] = '/promotion/seller/fullAld/update';
  Api['GET_FULL_ALD_INVALID'] = '/promotion/seller/fullAld/invalid';
  Api['GET_FULL_ALD_DEL'] = '/promotion/seller/fullAld/delete';
  Api['GET_FULL_ALD_PUBLISH'] = '/promotion/seller/fullAld/publish';
  Api['GET_FULL_NLD_LIST'] = '/promotion/seller/fullNld/list';
  Api['GET_FULL_NLD_DETAIL'] = '/promotion/seller/fullNld/detail';
  Api['GET_FULL_NLD_INVALID'] = '/promotion/seller/fullNld/invalid';
  Api['GET_FULL_NLD_DEL'] = '/promotion/seller/fullNld/delete';
  Api['GET_FULL_NLD_PUBLISH'] = '/promotion/seller/fullNld/publish';
  Api['GET_FULL_NLD_UPDATE'] = '/promotion/seller/fullNld/update';
  Api['GET_FULL_NLD_ADD'] = '/promotion/seller/fullNld/add';
  // dev_supplier-start
  if(userStore&&userStore.getStoreInfo&&userStore.getStoreInfo.shopType&&userStore.getStoreInfo.shopType=='3'){
    Api['GET_FULL_ACM_LIST'] = '/supplier/seller/supplierFullAcm/list';
    Api['GET_FULL_ACM_DETAIL'] = '/supplier/seller/supplierFullAcm/detail';
    Api['GET_FULL_ACM_INVALID'] = '/supplier/seller/supplierFullAcm/invalid';
    Api['GET_FULL_ACM_UPDATE'] = '/supplier/seller/supplierFullAcm/update';
    Api['GET_FULL_ACM_ADD'] = '/supplier/seller/supplierFullAcm/add';
    Api['GET_FULL_ACM_DEL'] = '/supplier/seller/supplierFullAcm/delete';
    Api['GET_FULL_ACM_PUBLISH'] = '/supplier/seller/supplierFullAcm/publish';
    Api['GET_FULL_ASM_LIST'] = '/supplier/seller/supplierFullAsm/list';
    Api['GET_FULL_ASM_DETAIL'] = '/supplier/seller/supplierFullAsm/detail';
    Api['GET_FULL_ASM_INVALID'] = '/supplier/seller/supplierFullAsm/invalid';
    Api['GET_FULL_ASM_PUBLISH'] = '/supplier/seller/supplierFullAsm/publish';
    Api['GET_FULL_ASM_DEL'] = '/supplier/seller/supplierFullAsm/delete';
    Api['GET_FULL_ASM_UPDATE'] = '/supplier/seller/supplierFullAsm/update';
    Api['GET_FULL_ASM_ADD'] = '/supplier/seller/supplierFullAsm/add';
    Api['GET_FULL_ALD_LIST'] = '/supplier/seller/supplierFullAld/list';
    Api['GET_FULL_ALD_DETAIL'] = '/supplier/seller/supplierFullAld/detail';
    Api['GET_FULL_ALD_ADD'] = '/supplier/seller/supplierFullAld/add';
    Api['GET_FULL_ALD_UPDATE'] = '/supplier/seller/supplierFullAld/update';
    Api['GET_FULL_ALD_INVALID'] = '/supplier/seller/supplierFullAld/invalid';
    Api['GET_FULL_ALD_DEL'] = '/supplier/seller/supplierFullAld/delete';
    Api['GET_FULL_ALD_PUBLISH'] = '/supplier/seller/supplierFullAld/publish';
    Api['GET_FULL_NLD_LIST'] = '/supplier/seller/supplierFullNld/list';
    Api['GET_FULL_NLD_DETAIL'] = '/supplier/seller/supplierFullNld/detail';
    Api['GET_FULL_NLD_INVALID'] = '/supplier/seller/supplierFullNld/invalid';
    Api['GET_FULL_NLD_DEL'] = '/supplier/seller/supplierFullNld/delete';
    Api['GET_FULL_NLD_PUBLISH'] = '/supplier/seller/supplierFullNld/publish';
    Api['GET_FULL_NLD_UPDATE'] = '/supplier/seller/supplierFullNld/update';
    Api['GET_FULL_NLD_ADD'] = '/supplier/seller/supplierFullNld/add';
  }
  // dev_supplier-end
})(Api || (Api = {}));
//slodon_循环满减列表
export const getFullAcmListApi = (params) =>
  defHttp.get({
    url: Api.GET_FULL_ACM_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_循环满减详情
export const getFullAcmDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_FULL_ACM_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_失效循环满减
export const getFullAcmInvalidApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_ACM_INVALID,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//编辑循环满减
export const getFullAcmUpdateApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_ACM_UPDATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//添加循环满减
export const getFullAcmAddApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_ACM_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_删除循环满减
export const getFullAcmDelApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_ACM_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//发布循环满减
export const getFullAcmPublishApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_ACM_PUBLISH,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_阶梯满减列表
export const getFullAsmListApi = (params) =>
  defHttp.get({
    url: Api.GET_FULL_ASM_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_阶梯满减详情
export const getFullAsmDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_FULL_ASM_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_失效阶梯满减
export const getFullAsmInvalidApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_ASM_INVALID,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//发布阶梯满减
export const getFullAsmPublishApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_ASM_PUBLISH,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_删除阶梯满减
export const getFullAsmDelApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_ASM_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//编辑阶梯满减
export const getFullAsmUpdateApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_ASM_UPDATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//添加阶梯满减
export const getFullAsmAddApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_ASM_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_满N元折扣列表
export const getFullAldListApi = (params) =>
  defHttp.get({
    url: Api.GET_FULL_ALD_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_满N元折扣详情
export const getFullAldDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_FULL_ALD_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//添加满N元折扣
export const getFullAldAddApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_ALD_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//编辑满N元折扣
export const getFullAldUpdateApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_ALD_UPDATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//发布满N元折扣
export const getFullAldPublishApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_ALD_PUBLISH,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_失效满N件折扣
export const getFullAldInvalidApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_ALD_INVALID,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_删除满N件折扣
export const getFullAldDelApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_ALD_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_失效满N元折扣
export const getFullNldInvalidApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_NLD_INVALID,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_删除满N元折扣
export const getFullNldDelApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_NLD_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_满N件折扣列表
export const getFullNldListApi = (params) =>
  defHttp.get({
    url: Api.GET_FULL_NLD_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_满N件折扣详情
export const getFullNldDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_FULL_NLD_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//发布满N件折扣
export const getFullNldPublishApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_NLD_PUBLISH,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//编辑满N件折扣
export const getFullNldUpdateApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_NLD_UPDATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//添加满N件折扣
export const getFullNldAddApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_NLD_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
