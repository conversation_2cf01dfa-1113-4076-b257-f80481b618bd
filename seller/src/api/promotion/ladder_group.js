import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
var Api;
(function (Api) {
  Api['GET_LADDER_GROUP_LIST'] = '/promotion/seller/ladder/group/list';
  Api['GET_LADDER_GROUP_INVALID'] = '/promotion/seller/ladder/group/invalid';
  Api['GET_LADDER_GROUP_DEL'] = '/promotion/seller/ladder/group/delete';
  Api['GET_LADDER_GROUP_DETAIL'] = '/promotion/seller/ladder/group/detail';
  Api['GET_LADDER_GROUP_GOOD_LIST'] = '/promotion/seller/ladder/group/teamList';
  Api['GET_LADDER_LABEL_LIST'] = '/promotion/seller/ladder/group/labelList';
  Api['GET_LADDER_PUBLISH'] = '/promotion/seller/ladder/group/publish';
  Api['GET_LADDER_ADD'] = '/promotion/seller/ladder/group/add';
  Api['GET_LADDER_UPDATE'] = '/promotion/seller/ladder/group/update';
})(Api || (Api = {}));
//获取阶梯团活动列表
export const getLadderGroupListApi = (params) =>
  defHttp.get({
    url: Api.GET_LADDER_GROUP_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//失效阶梯团活动
export const getLadderInvalidApi = (params) =>
  defHttp.post({
    url: Api.GET_LADDER_GROUP_INVALID,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//删除阶梯团活动
export const getLadderDelApi = (params) =>
  defHttp.post({
    url: Api.GET_LADDER_GROUP_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取阶梯团活动详情
export const getLadderDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_LADDER_GROUP_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取阶梯团团队列表
export const getLadderGoodListApi = (params) =>
  defHttp.get({
    url: Api.GET_LADDER_GROUP_GOOD_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_获取阶梯团活动标签
export const getLadderLabelListApi = (params) =>
  defHttp.get({
    url: Api.GET_LADDER_LABEL_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_video_发布阶梯团活动
export const getLadderPublishApi = (params) =>
  defHttp.post({
    url: Api.GET_LADDER_PUBLISH,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_video_新建阶梯团活动
export const getLadderAddApi = (params) =>
  defHttp.post({
    url: Api.GET_LADDER_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });

//slodon_video_编辑阶梯团活动
export const getLadderUpdateApi = (params) =>
  defHttp.post({
    url: Api.GET_LADDER_UPDATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });
