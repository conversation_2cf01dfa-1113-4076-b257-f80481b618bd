import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
var Api;
(function (Api) {
  Api['GET_PRESELL_LIST'] = '/promotion/seller/preSell/list';
  Api['GET_PRESELL_INVALID'] = '/promotion/seller/preSell/invalid';
  Api['GET_PRESELL_DEL'] = '/promotion/seller/preSell/delete';
  Api['GET_PRESELL_DETAIL'] = '/promotion/seller/preSell/detail';
  Api['GET_PRESELL_GOOD_LIST'] = '/promotion/seller/preSell/goodList';
  Api['GET_PRESELL_LABEL_LIST'] = '/promotion/seller/preSell/labelList';
  Api['GET_PRESELL_DEL_GOODS'] = '/promotion/seller/preSell/delGoods';
  Api['GET_PRESELL_PUBLISH'] = '/promotion/seller/preSell/publish';
  Api['GET_PRESELL_ADD'] = '/promotion/seller/preSell/add';
  Api['GET_PRESELL_UPDATE'] = '/promotion/seller/preSell/update';
})(Api || (Api = {}));
//获取预售活动列表
export const getPreSellListApi = (params) =>
  defHttp.get({
    url: Api.GET_PRESELL_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//失效预售活动
export const getPreSellInvalidApi = (params) =>
  defHttp.post({
    url: Api.GET_PRESELL_INVALID,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//删除预售活动
export const getPreSellDelApi = (params) =>
  defHttp.post({
    url: Api.GET_PRESELL_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取预售活动详情
export const getPreSellDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_PRESELL_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取预售活动的商品
export const getPreSellGoodListApi = (params) =>
  defHttp.get({
    url: Api.GET_PRESELL_GOOD_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取预售标签列表
export const getPreSellLabelListApi = (params) =>
  defHttp.get({
    url: Api.GET_PRESELL_LABEL_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//删除预售商品
export const getPreSellDelGoodsApi = (params) =>
  defHttp.post({
    url: Api.GET_PRESELL_DEL_GOODS,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//发布预售
export const getPreSellPublishApi = (params) =>
  defHttp.post({
    url: Api.GET_PRESELL_PUBLISH,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_添加预售活动
export const getPreSellAddApi = (params) =>
  defHttp.post({
    url: Api.GET_PRESELL_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });

//slodon_编辑预售活动
export const getPreSellUpdateApi = (params) =>
  defHttp.post({
    url: Api.GET_PRESELL_UPDATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });
