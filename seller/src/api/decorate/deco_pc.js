import { defHttp } from '@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
// dev_supplier-start
import { useUserStore } from '/@/store/modules/user';
const userStore = useUserStore();
// dev_supplier-end
export const API = {
  PC_DECO_LIST: '/system/seller/pcDeco/list',
  PC_DECO_UPDATE: '/system/seller/pcDeco/update',
  PC_DECO_ISENABLED: '/system/seller/pcDeco/isEnable',
  PC_DECO_DELETE: '/system/seller/pcDeco/delete',
  PC_DECO_COPY: '/system/seller/pcDeco/copy',
  PC_DECO_ADD: '/system/seller/pcDeco/add',
  GOODS_LIST: '/goods/seller/goods/list',
  STORE_LIST: '/seller/seller/store/list',
  CATE_LIST: '/goods/seller/goodsCategory/list',
  TPL_LIST: '/system/seller/tplPc/data/list',
  TPL_ENABLE: '/system/seller/tplPc/data/isEnable',
  TPL_UPDATE: '/system/seller/tplPc/data/update',
  TPL_DELETE: '/system/seller/tplPc/data/delete',
  TPL_TYPE_LIST: '/system/seller/tplPc/type/list',
  TPL_INS_LIST: "/system/seller/tplPc/list",
  TPL_DATA_ADD: '/system/seller/tplPc/data/add',
  TPL_DETAIL: '/system/seller/pcDeco/display',
};

// dev_supplier-start
if(userStore.getStoreInfo.shopType&&userStore.getStoreInfo.shopType=='3'){
  API.GOODS_LIST =  '/supplier/seller/supplierGoods/list';
  API.PC_DECO_LIST = '/supplier/seller/supplierPcDeco/list';
  API.PC_DECO_UPDATE = '/supplier/seller/supplierPcDeco/update';
  API.PC_DECO_ISENABLED = '/supplier/seller/supplierPcDeco/isEnable';
  API.PC_DECO_DELETE = '/supplier/seller/supplierPcDeco/delete';
  API.PC_DECO_COPY = '/supplier/seller/supplierPcDeco/copy';
  API.PC_DECO_ADD = '/supplier/seller/supplierPcDeco/add';
  API.TPL_LIST =  '/supplier/seller/supplierPcData/list';
  API.TPL_ENABLE =  '/supplier/seller/supplierPcData/isEnable';
  API.TPL_UPDATE =  '/supplier/seller/supplierPcData/update';
  API.TPL_DELETE =  '/supplier/seller/supplierPcData/delete';
  API.TPL_DATA_ADD = '/supplier/seller/supplierPcData/add';
  API.TPL_DETAIL =  '/supplier/seller/supplierPcDeco/detail';
}
// dev_supplier-end

export const getDecoList = (params) => defHttp.get({ url: API.PC_DECO_LIST, params });
export const UpdateDecoApi = (params) =>
  defHttp.post({
    url: API.PC_DECO_UPDATE,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });
export const updateIsEnabled = (params) =>
  defHttp.post({
    url: API.PC_DECO_ISENABLED,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });
export const decoDelete = (params) =>
  defHttp.post({
    url: API.PC_DECO_DELETE,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });
export const decoCopy = (params) =>
  defHttp.post({
    url: API.PC_DECO_COPY,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });
export const decoAdd = (params) =>
  defHttp.post({
    url: API.PC_DECO_ADD,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const getSellerGoodsList = (params) => defHttp.get({ url: API.GOODS_LIST, params });

export const getSellerStoreList = (params) => defHttp.get({ url: API.STORE_LIST, params });


export const getCategoryList = (params) => defHttp.get({ url: API.CATE_LIST, params });


export const tplPcListApi = (params) => defHttp.get({ url: API.TPL_LIST, params });

export const TplEnableApi = (params) =>
  defHttp.post({
    url: API.TPL_ENABLE,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });


export const TplUpdateApi = (params) =>
  defHttp.post({
    url: API.TPL_UPDATE,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const TplDeleteApi = (params) =>
  defHttp.post({
    url: API.TPL_DELETE,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const TplTypeListApi = (params) => defHttp.get({ url: API.TPL_TYPE_LIST, params });

export const TplInsListApi = (params) => defHttp.get({ url: API.TPL_INS_LIST, params });


export const TplAddListApi = (params) =>
  defHttp.post({
    url: API.TPL_DATA_ADD,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });


export const TplDetailApi = (params) => defHttp.get({ url: API.TPL_DETAIL, params });

