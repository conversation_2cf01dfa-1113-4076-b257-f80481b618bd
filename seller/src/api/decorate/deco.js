import { defHttp } from '@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
import pinia from '@/store/modules/pinia';
// dev_supplier-start
import { useUserStore } from '/@/store/modules/user';
const userStore = useUserStore(pinia);
// dev_supplier-end
export const API = {
  M_DECO_LIST: '/system/seller/mobileDeco/list',
  CATE_LIST: '/goods/seller/goodsCategory/list',
  GET_BIND_STORE_CATEGORY: '/seller/seller/bindCate/list',
  APPLY_BIND_STORE_CATEGORY: '/seller/seller/bindCate/apply',
  DEL_BIND_STORE_CATEGORY: '/seller/seller/bindCate/delBindCate',
  GET_STORE_EXPIRETIME: '/seller/seller/renew/getExpireTime',
  GET_STORE_RENEW_LIST: '/seller/seller/renew/list',
  DEL_STORE_RENEW: '/seller/seller/renew/delRenew',
  DO_STORE_RENEW: '/seller/seller/renew/doRenew',
  PAY_STORE_RENEW: '/seller/seller/pay/renewPay',
  GET_STORE_RENEW_DETAIL: '/seller/seller/renew/getDetail',
  PC_DECO_LIST: '/system/seller/pcDeco/list',
};


// dev_supplier-start
if(userStore.getStoreInfo.shopType&&userStore.getStoreInfo.shopType=='3'){
  API.PC_DECO_LIST = '/supplier/seller/supplierPcDeco/list';
}
// dev_supplier-end

// pc装修
export const getDecoList = (params) => defHttp.get({ url: API.PC_DECO_LIST, params });


// 首页装修列表
export const getMobleDecoListApi = (params) => defHttp.get({ url: API.M_DECO_LIST, params });
// 分类列表,获取当前分类的下级分类
export const getCategoryList = (params) => defHttp.get({ url: API.CATE_LIST, params })
// 获取店铺到期时间
export const getStoreExpiretime = () => defHttp.get({ url: API.GET_STORE_EXPIRETIME });
// 店铺续签列表
export const getStoreRenewList = (params) => defHttp.get({ url: API.GET_STORE_RENEW_LIST, params });
// 删除店铺续签
export const delStoreRenew = (params) =>
  defHttp.post({
    url: API.DEL_STORE_RENEW,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });
// 发起续签
export const doStoreRenew = (params) =>
  defHttp.post({
    url: API.DO_STORE_RENEW,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });
// 商户续签支付
export const payStoreRenew = (params) =>
  defHttp.post({
    url: API.PAY_STORE_RENEW,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });
// 获取续签详情
export const getStoreRenewDetail = (params) => defHttp.get({ url: API.GET_STORE_RENEW_DETAIL, params });
// 商户获取经营类目列表
export const getBindStoreCategory = (params) => defHttp.get({ url: API.GET_BIND_STORE_CATEGORY, params });
// 申请经营类目
export const applyBindStoreCategory = (params) =>
  defHttp.post({
    url: API.APPLY_BIND_STORE_CATEGORY,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });
// 删除经营类目
export const delBindStoreCategory = (params) =>
  defHttp.post({
    url: API.DEL_BIND_STORE_CATEGORY,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });















  