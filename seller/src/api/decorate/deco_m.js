import { defHttp } from '@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
// dev_supplier-start
import { useUserStore } from '/@/store/modules/user';
const userStore = useUserStore();
// dev_supplier-end
export const API = {
  M_DECO_LIST: '/system/seller/mobileDeco/list',
  M_DECO_DETAIL: '/system/seller/mobileDeco/detail',
  M_DECO_MENU: '/system/seller/tplMobile/menu',
  M_DECO_UPDATE: '/system/seller/mobileDeco/update',
  M_DECO_ISUSE: '/system/seller/mobileDeco/isUse',
  M_DECO_DELETE: '/system/seller/mobileDeco/delete',
  M_DECO_COPY: '/system/seller/mobileDeco/copy',
  M_DECO_ADD: '/system/seller/mobileDeco/add',
  GOODS_LIST: '/goods/seller/goods/list',
};

// dev_supplier-start
if(userStore.getStoreInfo.shopType&&userStore.getStoreInfo.shopType=='3'){
  API.GOODS_LIST =  '/supplier/seller/supplierGoods/list';
}
// dev_supplier-end

export const getMobleDecoListApi = (params) => defHttp.get({ url: API.M_DECO_LIST, params });
export const getMobleDecoDetailApi = (params) => defHttp.get({ url: API.M_DECO_DETAIL, params });
export const getMobleDecoMenuApi = (params) => defHttp.get({ url: API.M_DECO_MENU, params });
export const updateMobileDeco = (params) =>
  defHttp.post({
    url: API.M_DECO_UPDATE,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });
export const updateIsUse = (params) =>
  defHttp.post({
    url: API.M_DECO_ISUSE,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });
export const decoMobileDelete = (params) =>
  defHttp.post({
    url: API.M_DECO_DELETE,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });
export const decoMobileCopy = (params) =>
  defHttp.post({
    url: API.M_DECO_COPY,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });
export const decoMobileAddApi = (params) =>
  defHttp.post({
    url: API.M_DECO_ADD,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const getSellerGoodsList = (params) => defHttp.get({ url: API.GOODS_LIST, params });


  