import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  GET_SETTING_LIST: '/system/seller/setting/getSettingList',
  GET_SETTING_REASON_LIST: '/system/seller/reason/list',
  GET_SPREADER_GOODS_LIST: '/spreader/seller/spreaderGoods/goodsList',
  GET_VIDEO_SMS_CODE: '/video/seller/video/member/smsCode',
  GET_SELLER_VIDEO_LIST: '/video/seller/video/list',
  GET_FREIGHT_TEMPLATE_LIST: '/goods/seller/goodsFreightTemplate/list',
  LOGOUT_API: '/adminLogin/oauth/logout',
  LOGIN_API: '/sellerLogin/oauth/token',
  // dev_supplier_o2o-start
  DICT_DATA_LIST: '/system/seller/dictData/list',
  // dev_supplier_o2o-end
  GET_COMMON_SEND_TL_VERIFY: '/member/common/sendTlVerify',
};

//slodon_获取系统设置信息
export const getSettingListApi = (params) =>
  defHttp.get({
    url: Api.GET_SETTING_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
  
//slodon_获取理由
export const getSettingReasonListApi = (params) =>
  defHttp.get({
    url: Api.GET_SETTING_REASON_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });


//推手——获取店铺商品列表用于导入推手商品
export const getSpreaderGoodsListApi = (params) =>
  defHttp.get({
    url: Api.GET_SPREADER_GOODS_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取验证码 视频模块
export const getVideoSmsCodeApi = (params) =>
  defHttp.post({
    url: Api.GET_VIDEO_SMS_CODE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取短视频列表
export const getVideoListApi = (params) =>
  defHttp.get({
    url: Api.GET_SELLER_VIDEO_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取运费模板数据
export const getFreightTemplateListApi = (params) =>
  defHttp.get({ url: Api.GET_FREIGHT_TEMPLATE_LIST, params });


//退出登录
export const logoutApi = (params) => 
  defHttp.post({
    url: Api.LOGOUT_API,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// dev_supplier_o2o-start
//数据字典列表
export const dictDataListApi = (params) => 
  defHttp.get({
    url: Api.DICT_DATA_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// dev_supplier_o2o-end

  //刷新token
  export const loginRefreshApi = (params) => 
    defHttp.post({
      url: Api.LOGIN_API,
      params,
      headers: {
        // @ts-ignore
        'Content-Type': ContentTypeEnum.FORM_URLENCODED,
      },
    });


  //会员注册、授权登录认证手机号发送验证码
  export const getCommonSendTlVerifyApi = (params) => 
    defHttp.get({
      url: Api.GET_COMMON_SEND_TL_VERIFY,
      params,
      headers: {
        // @ts-ignore
        'Content-Type': ContentTypeEnum.FORM_URLENCODED,
      },
    });

