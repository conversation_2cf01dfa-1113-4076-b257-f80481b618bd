import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
// dev_supplier-start
import { useUserStore } from '/@/store/modules/user';
const userStore = useUserStore();
// dev_supplier-end

const Api = {
  GET_GOODS_LIST: '/goods/seller/goods/list',
  GET_SECKILL_GOODS_LIST: '/goods/seller/goodsSeckill/goodsList',
  GET_SELLER_GOODS_GOODS_LIST: '/goods/seller/goods/goodsList',
};

// dev_supplier-start
if(userStore.getStoreInfo.shopType&&userStore.getStoreInfo.shopType=='3'){
  Api.GET_GOODS_LIST =  '/supplier/seller/supplierGoods/list';
  Api.GET_SECKILL_GOODS_LIST =  '/supplier/seller/selectGoods/goodsList';
  Api.GET_SELLER_GOODS_GOODS_LIST = '/supplier/seller/supplierGoods/goodsList'
}
// dev_supplier-end

  //获取商品列表
export const getGoodsListApi = (params) =>
  defHttp.get({
    url: Api.GET_GOODS_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });


//slodon_获取商品列表（用于秒杀商品的选择）
export const getSeckillListApi = (params) =>
  defHttp.get({
    url: Api.GET_SECKILL_GOODS_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

  
//slodon_获取商品列表(es查询)
export const getGoodsGoodsListApi = (params) =>
  defHttp.get({
    url: Api.GET_SELLER_GOODS_GOODS_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });