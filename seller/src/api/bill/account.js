import { defHttp } from '@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const API = {
  GET_ACCOUNT_LIST: '/system/seller/bill/account/list',
  POST_SET_DEFAULT: '/system/seller/bill/account/setDefault',
  POST_ACCOUNT_ADD: '/system/seller/bill/account/add',
  POST_ACCOUNT_UPDATE: '/system/seller/bill/account/update',
  POST_ACCOUNT_DELETE: '/system/seller/bill/account/delete',
};

export const getAccountListApi = (params) => defHttp.get({ url: API.GET_ACCOUNT_LIST, params });
export const postAccountAddApi = (params) =>
  defHttp.post({
    url: API.POST_ACCOUNT_ADD,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });
export const postAccountDefaultApi = (params) =>
  defHttp.post({
    url: API.POST_SET_DEFAULT,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });
export const postAccountUpdate = (params) =>
  defHttp.post({
    url: API.POST_ACCOUNT_UPDATE,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });
export const postAccountDelete = (params) =>
  defHttp.post({
    url: API.POST_ACCOUNT_DELETE,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });
