import { defHttp } from '@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
// dev_supplier-start
import { useUserStore } from '/@/store/modules/user';
const userStore = useUserStore();
// dev_supplier-end

const API = {
  GET_BILL_LIST: '/system/seller/bill/list',
  GET_BILL_DETAIL: '/system/seller/bill/detail',
  POST_BILL_CONFIRM: '/system/seller/bill/confirm',
  EXPORT_BILL_DETAIL: '/system/seller/bill/exportBillDetail',
  EXPORT_BILL_LIST: '/system/seller/bill/exportBillList',
};

// dev_supplier-start
if(userStore.getStoreInfo.shopType&&userStore.getStoreInfo.shopType=='3'){
  API.GET_BILL_LIST = '/supplier/seller/supplierBill/list'
  API.GET_BILL_DETAIL = '/supplier/seller/supplierBill/detail'
  API.POST_BILL_CONFIRM = '/supplier/seller/supplierBill/confirm'
  API.EXPORT_BILL_DETAIL = '/supplier/seller/supplierBill/exportBillDetail'
  API.EXPORT_BILL_LIST = '/supplier/seller/supplierBill/exportBillList'
}
// dev_supplier-end

export const getBillListApi = (params) => defHttp.get({ url: API.GET_BILL_LIST, params });
export const getBillDetailApi = (params) => defHttp.get({ url: API.GET_BILL_DETAIL, params });
export const postBillConfirmApi = (params) =>
  defHttp.post({
    url: API.POST_BILL_CONFIRM,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });
export const exportBillDetailApi = (params) =>
  defHttp.get({
    url: API.EXPORT_BILL_DETAIL,
    params,
    headers: {
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
    responseType: 'blob',
  });

export const exportBillListApi = (params) =>
  defHttp.get({
    url: API.EXPORT_BILL_LIST,
    params,
    headers: {
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
    responseType: 'blob',
  });
