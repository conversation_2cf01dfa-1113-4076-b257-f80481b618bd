[data-theme='dark'] ::-webkit-scrollbar-track {
  background-color: rgb(0 0 0 / 5%);
}

[data-theme='dark'] ::-webkit-scrollbar-thumb {
  background-color: rgb(144 147 153 / 30%);
  box-shadow: inset 0 0 6px rgb(0 0 0 / 20%);
}

[data-theme='dark'] ::-webkit-scrollbar-thumb:hover {
  background-color: #b6b7b9;
}

[data-theme='dark'] #nprogress .bar {
  background-color: #0960bd;
}

html[data-theme='dark'] .ant-pagination.mini .ant-pagination-item,
html[data-theme='dark'] .ant-pagination.mini .ant-pagination-next,
html[data-theme='dark'] .ant-pagination.mini .ant-pagination-prev {
  background-color: rgb(255 255 255 / 4%) !important;
}

[data-theme='dark'] html[data-theme='dark'] .ant-pagination.mini .ant-pagination-item a,
[data-theme='dark'] html[data-theme='dark'] .ant-pagination.mini .ant-pagination-next a,
[data-theme='dark'] html[data-theme='dark'] .ant-pagination.mini .ant-pagination-prev a {
  color: #8b949e !important;
}

[data-theme='dark'] html[data-theme='dark'] .ant-pagination.mini .ant-select-arrow {
  color: #8b949e !important;
}

[data-theme='dark'] html[data-theme='dark'] .ant-pagination.mini .ant-pagination-item-active {
  background-color: #0960bd !important;
}

[data-theme='dark'] html[data-theme='dark'] .ant-pagination.mini .ant-pagination-item-active a {
  color: #fff !important;
}

[data-theme='dark'] .ant-pagination.mini .ant-pagination-next,
[data-theme='dark'] .ant-pagination.mini .ant-pagination-prev {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-pagination.mini .ant-pagination-item:focus a,
[data-theme='dark'] .ant-pagination.mini .ant-pagination-item:hover a,
[data-theme='dark'] .ant-pagination.mini .ant-pagination-next:hover a,
[data-theme='dark'] .ant-pagination.mini .ant-pagination-prev:hover a {
  color: #0960bd;
}

[data-theme='dark'] .ant-pagination.mini .ant-pagination-item,
[data-theme='dark'] .ant-pagination.mini .ant-pagination-next,
[data-theme='dark'] .ant-pagination.mini .ant-pagination-prev {
  background-color: #f4f4f5 !important;
}

[data-theme='dark'] .ant-pagination.mini .ant-pagination-item a,
[data-theme='dark'] .ant-pagination.mini .ant-pagination-next a,
[data-theme='dark'] .ant-pagination.mini .ant-pagination-prev a {
  color: #606266;
}

[data-theme='dark'] .ant-pagination.mini .ant-pagination-item-active {
  background-color: #0960bd !important;
}

[data-theme='dark'] .ant-pagination.mini .ant-pagination-item-active a {
  color: #fff !important;
}

[data-theme='dark'] .ant-pagination.mini .ant-select-arrow {
  color: #cececd;
}

[data-theme='dark'] .ant-btn-primary:not(.ant-btn-background-ghost):not([disabled]) {
  color: #fff;
}

[data-theme='dark'] .ant-btn-default {
  border-color: #cececd;
  background-color: #fff;
  color: #606266;
}

[data-theme='dark'] .ant-btn-default:focus,
[data-theme='dark'] .ant-btn-default:hover {
  border-color: #0960bd;
  background-color: #fff;
  color: #0960bd;
}

[data-theme='dark'] [data-theme='light'] .ant-btn.ant-btn-link.is-disabled {
  border-color: transparent !important;
  background-color: transparent !important;
  color: rgb(0 0 0 / 25%);
}

[data-theme='dark'] [data-theme='dark'] .ant-btn.ant-btn-link.is-disabled {
  border-color: transparent !important;
  background-color: transparent !important;
  color: rgb(255 255 255 / 25%) !important;
}

[data-theme='dark'] .ant-btn-success.ant-btn-link:not([disabled='disabled']) {
  color: #55d187;
}

[data-theme='dark'] .ant-btn-success.ant-btn-link:not([disabled='disabled']):focus,
[data-theme='dark'] .ant-btn-success.ant-btn-link:not([disabled='disabled']):hover {
  border-color: transparent;
  color: #7ddca3;
}

[data-theme='dark'] .ant-btn-success.ant-btn-link:not([disabled='disabled']):active {
  color: #34bf6c;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-link.ant-btn-loading::before,
[data-theme='dark'] .ant-btn-error.ant-btn-link.ant-btn-loading::before,
[data-theme='dark'] .ant-btn-success.ant-btn-link.ant-btn-loading::before,
[data-theme='dark'] .ant-btn-warning.ant-btn-link.ant-btn-loading::before,
[data-theme='dark'] .ant-btn.ant-btn-link.ant-btn-loading::before {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-success:not(.ant-btn-link, .is-disabled) {
  border-color: #55d187;
  background-color: #55d187;
  color: #fff;
}

[data-theme='dark'] .ant-btn-success:not(.ant-btn-link, .is-disabled):focus,
[data-theme='dark'] .ant-btn-success:not(.ant-btn-link, .is-disabled):hover {
  border-color: #7ddca3;
  background-color: #7ddca3;
  color: #fff;
}

[data-theme='dark'] .ant-btn-success:not(.ant-btn-link, .is-disabled):active {
  border-color: #34bf6c;
  background-color: #34bf6c;
}

[data-theme='dark'] .ant-btn-warning.ant-btn-link:not([disabled='disabled']) {
  color: #efbd47;
}

[data-theme='dark'] .ant-btn-warning.ant-btn-link:not([disabled='disabled']):focus,
[data-theme='dark'] .ant-btn-warning.ant-btn-link:not([disabled='disabled']):hover {
  border-color: transparent;
  color: #f3ce76;
}

[data-theme='dark'] .ant-btn-warning.ant-btn-link:not([disabled='disabled']):active {
  color: #ebac18;
}

[data-theme='dark'] .ant-btn-warning:not(.ant-btn-link, .is-disabled) {
  border-color: #efbd47;
  background-color: #efbd47;
  color: #fff;
}

[data-theme='dark'] .ant-btn-warning:not(.ant-btn-link, .is-disabled):focus,
[data-theme='dark'] .ant-btn-warning:not(.ant-btn-link, .is-disabled):hover {
  border-color: #f3ce76;
  background-color: #f3ce76;
  color: #fff;
}

[data-theme='dark'] .ant-btn-warning:not(.ant-btn-link, .is-disabled):active {
  border-color: #ebac18;
  background-color: #ebac18;
}

[data-theme='dark'] .ant-btn-error.ant-btn-link:not([disabled='disabled']) {
  color: #ed6f6f;
}

[data-theme='dark'] .ant-btn-error.ant-btn-link:not([disabled='disabled']):focus,
[data-theme='dark'] .ant-btn-error.ant-btn-link:not([disabled='disabled']):hover {
  border-color: transparent;
  color: #f39c9c;
}

[data-theme='dark'] .ant-btn-error.ant-btn-link:not([disabled='disabled']):active {
  color: #e74242;
}

[data-theme='dark'] .ant-btn-error:not(.ant-btn-link, .is-disabled) {
  border-color: #ed6f6f;
  background-color: #ed6f6f;
  color: #fff;
}

[data-theme='dark'] .ant-btn-error:not(.ant-btn-link, .is-disabled):focus,
[data-theme='dark'] .ant-btn-error:not(.ant-btn-link, .is-disabled):hover {
  border-color: #f39c9c;
  background-color: #f39c9c;
  color: #fff;
}

[data-theme='dark'] .ant-btn-error:not(.ant-btn-link, .is-disabled):active {
  border-color: #e74242;
  background-color: #e74242;
}

[data-theme='dark'] .ant-btn-background-ghost {
  background-color: transparent !important;
}

[data-theme='dark'] .ant-btn-background-ghost[disabled],
[data-theme='dark'] .ant-btn-background-ghost[disabled]:hover {
  border-color: rgb(255 255 255 / 40%) !important;
  background-color: transparent !important;
  color: rgb(255 255 255 / 40%) !important;
}

[data-theme='dark'] .ant-btn-dashed.ant-btn-background-ghost,
[data-theme='dark'] .ant-btn-default.ant-btn-background-ghost {
  border-color: #fff;
  color: #fff;
}

[data-theme='dark'] .ant-btn-dashed.ant-btn-background-ghost:focus,
[data-theme='dark'] .ant-btn-dashed.ant-btn-background-ghost:hover,
[data-theme='dark'] .ant-btn-default.ant-btn-background-ghost:focus,
[data-theme='dark'] .ant-btn-default.ant-btn-background-ghost:hover {
  border-color: #fff;
  color: #fff;
}

[data-theme='dark'] .ant-btn-dashed.ant-btn-background-ghost:active,
[data-theme='dark'] .ant-btn-default.ant-btn-background-ghost:active {
  border-color: #e6e6e6;
  color: #e6e6e6;
}

[data-theme='dark'] .ant-btn-dashed.ant-btn-background-ghost[disabled],
[data-theme='dark'] .ant-btn-dashed.ant-btn-background-ghost[disabled]:hover,
[data-theme='dark'] .ant-btn-default.ant-btn-background-ghost[disabled],
[data-theme='dark'] .ant-btn-default.ant-btn-background-ghost[disabled]:hover {
  border-color: rgb(255 255 255 / 40%) !important;
  color: rgb(255 255 255 / 40%) !important;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-success:not(.ant-btn-link) {
  border-color: #55d187;
  background-color: transparent;
  color: #55d187;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-success:not(.ant-btn-link):focus,
[data-theme='dark'] .ant-btn-background-ghost.ant-btn-success:not(.ant-btn-link):hover {
  border-color: #7ddca3;
  color: #7ddca3 !important;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-success:not(.ant-btn-link):active {
  border-color: #34bf6c;
  color: #34bf6c;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-warning:not(.ant-btn-link) {
  border-color: #efbd47;
  background-color: transparent;
  color: #efbd47;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-warning:not(.ant-btn-link):focus,
[data-theme='dark'] .ant-btn-background-ghost.ant-btn-warning:not(.ant-btn-link):hover {
  border-color: #f3ce76;
  color: #f3ce76 !important;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-warning:not(.ant-btn-link):active {
  border-color: #ebac18;
  color: #ebac18;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-error:not(.ant-btn-link) {
  border-color: #ed6f6f;
  background-color: transparent;
  color: #ed6f6f;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-error:not(.ant-btn-link):focus,
[data-theme='dark'] .ant-btn-background-ghost.ant-btn-error:not(.ant-btn-link):hover {
  border-color: #f39c9c;
  color: #f39c9c !important;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-error:not(.ant-btn-link):active {
  border-color: #e74242;
  color: #e74242;
}

[data-theme='dark'] .ant-btn-ghost.ant-btn-link:not([disabled='disabled']) {
  color: #fff;
}

[data-theme='dark'] .ant-btn-ghost.ant-btn-link:not([disabled='disabled']):focus,
[data-theme='dark'] .ant-btn-ghost.ant-btn-link:not([disabled='disabled']):hover {
  border-color: transparent;
  color: #fff;
}

[data-theme='dark'] .ant-popover-content {
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
}

[data-theme='dark'] .modal-icon-warning {
  color: #efbd47 !important;
}

[data-theme='dark'] .modal-icon-success {
  color: #55d187 !important;
}

[data-theme='dark'] .modal-icon-error {
  color: #ed6f6f !important;
}

[data-theme='dark'] .modal-icon-info {
  color: #0960bd !important;
}

[data-theme='dark'] .bg-white {
  background-color: #151515 !important;
}

[data-theme='dark'] html[data-theme='light'] .text-secondary {
  color: rgb(0 0 0 / 45%);
}

[data-theme='dark'] html[data-theme='light'] .ant-alert-success {
  border: 1px solid #b7eb8f;
  background-color: #f6ffed;
}

[data-theme='dark'] html[data-theme='light'] .ant-alert-error {
  border: 1px solid #ffccc7;
  background-color: #fff;
}

[data-theme='dark'] html[data-theme='light'] .ant-alert-warning {
  border: 1px solid #ffe58f;
  background-color: #fff;
}

[data-theme='dark'] html[data-theme='light'] :not(:root):fullscreen::backdrop {
  background-color: #000 !important;
}

[data-theme='dark'] [data-theme='dark'] .text-secondary {
  color: #8b949e;
}

[data-theme='dark'] [data-theme='dark'] .ant-card-grid {
  box-shadow: 1px 0 0 0 #434343;
}

[data-theme='dark'] [data-theme='dark'] .ant-calendar-selected-day .ant-calendar-date {
  color: rgb(0 0 0 / 80%);
}

[data-theme='dark']
  [data-theme='dark']
  .ant-select-tree
  li
  .ant-select-tree-node-content-wrapper.ant-select-tree-node-selected {
  color: rgb(0 0 0 / 90%);
}

[data-theme='dark'] html {
  -webkit-tap-highlight-color: rgb(0 0 0 / 0%);
}

[data-theme='dark'] body {
  background-color: #000;
  color: #c9d1d9;
}

[data-theme='dark'] h1,
[data-theme='dark'] h2,
[data-theme='dark'] h3,
[data-theme='dark'] h4,
[data-theme='dark'] h5,
[data-theme='dark'] h6 {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] a {
  background-color: transparent;
  color: #0960bd;
}

[data-theme='dark'] a:hover {
  color: #2a7dc9;
}

[data-theme='dark'] a:active {
  color: #004496;
}

[data-theme='dark'] a[disabled] {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] caption {
  color: #8b949e;
}

[data-theme='dark'] mark {
  background-color: #2c2712;
}

[data-theme='dark'] ::selection {
  background: #0960bd;
  color: #fff;
}

[data-theme='dark'] .ant-click-animating-node,
[data-theme='dark'] [ant-click-animating-without-extra-node='true']::after {
  box-shadow: 0 0 0 0 #0960bd;
}

[data-theme='dark'] .ant-btn {
  border: 1px solid transparent;
  border-color: #303030;
  background: 0 0;
  box-shadow: 0 2px 0 rgb(0 0 0 / 1.5%);
  color: #c9d1d9;
}

[data-theme='dark'] .ant-btn > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn:focus,
[data-theme='dark'] .ant-btn:hover {
  border-color: #2a7dc9;
  background: 0 0;
  color: #2a7dc9;
}

[data-theme='dark'] .ant-btn:focus > a:only-child::after,
[data-theme='dark'] .ant-btn:hover > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn:active {
  border-color: #004496;
  background: 0 0;
  color: #004496;
}

[data-theme='dark'] .ant-btn:active > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn[disabled],
[data-theme='dark'] .ant-btn[disabled]:active,
[data-theme='dark'] .ant-btn[disabled]:focus,
[data-theme='dark'] .ant-btn[disabled]:hover {
  border-color: #303030;
  background: rgb(255 255 255 / 8%);
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-btn[disabled]:active > a:only-child::after,
[data-theme='dark'] .ant-btn[disabled]:focus > a:only-child::after,
[data-theme='dark'] .ant-btn[disabled]:hover > a:only-child::after,
[data-theme='dark'] .ant-btn[disabled] > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn:active,
[data-theme='dark'] .ant-btn:focus,
[data-theme='dark'] .ant-btn:hover {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-primary {
  border-color: #0960bd;
  background: #0960bd !important;
  box-shadow: 0 2px 0 rgb(0 0 0 / 4.5%);
  color: #fff;
}

[data-theme='dark'] .ant-btn-primary > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-primary:focus,
[data-theme='dark'] .ant-btn-primary:hover {
  border-color: #004496;
  background: #004496;
  color: #fff;
}

[data-theme='dark'] .ant-btn-primary:focus > a:only-child::after,
[data-theme='dark'] .ant-btn-primary:hover > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-primary:active {
  border-color: #2a7dc9;
  background: #2a7dc9;
  color: #fff;
}

[data-theme='dark'] .ant-btn-primary:active > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-primary[disabled],
[data-theme='dark'] .ant-btn-primary[disabled]:active,
[data-theme='dark'] .ant-btn-primary[disabled]:focus,
[data-theme='dark'] .ant-btn-primary[disabled]:hover {
  border-color: #303030;
  background: rgb(255 255 255 / 8%);
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-btn-primary[disabled]:active > a:only-child::after,
[data-theme='dark'] .ant-btn-primary[disabled]:focus > a:only-child::after,
[data-theme='dark'] .ant-btn-primary[disabled]:hover > a:only-child::after,
[data-theme='dark'] .ant-btn-primary[disabled] > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-group .ant-btn-primary:not(:first-child):not(:last-child) {
  border-right-color: #2a7dc9;
  border-left-color: #2a7dc9;
}

[data-theme='dark'] .ant-btn-group .ant-btn-primary:not(:first-child):not(:last-child):disabled {
  border-color: #303030;
}

[data-theme='dark'] .ant-btn-group .ant-btn-primary:first-child:not(:last-child) {
  border-right-color: #2a7dc9;
}

[data-theme='dark'] .ant-btn-group .ant-btn-primary:first-child:not(:last-child)[disabled] {
  border-right-color: #303030;
}

[data-theme='dark'] .ant-btn-group .ant-btn-primary + .ant-btn-primary,
[data-theme='dark'] .ant-btn-group .ant-btn-primary:last-child:not(:first-child) {
  border-left-color: #2a7dc9;
}

[data-theme='dark'] .ant-btn-group .ant-btn-primary + .ant-btn-primary[disabled],
[data-theme='dark'] .ant-btn-group .ant-btn-primary:last-child:not(:first-child)[disabled] {
  border-left-color: #303030;
}

[data-theme='dark'] .ant-btn-ghost {
  border-color: #303030;
  background: 0 0;
  color: #c9d1d9;
}

[data-theme='dark'] .ant-btn-ghost > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-ghost:focus,
[data-theme='dark'] .ant-btn-ghost:hover {
  border-color: #2a7dc9;
  background: 0 0;
  color: #2a7dc9;
}

[data-theme='dark'] .ant-btn-ghost:focus > a:only-child::after,
[data-theme='dark'] .ant-btn-ghost:hover > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-ghost:active {
  border-color: #004496;
  background: 0 0;
  color: #004496;
}

[data-theme='dark'] .ant-btn-ghost:active > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-ghost[disabled],
[data-theme='dark'] .ant-btn-ghost[disabled]:active,
[data-theme='dark'] .ant-btn-ghost[disabled]:focus,
[data-theme='dark'] .ant-btn-ghost[disabled]:hover {
  border-color: #303030;
  background: rgb(255 255 255 / 8%);
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-btn-ghost[disabled]:active > a:only-child::after,
[data-theme='dark'] .ant-btn-ghost[disabled]:focus > a:only-child::after,
[data-theme='dark'] .ant-btn-ghost[disabled]:hover > a:only-child::after,
[data-theme='dark'] .ant-btn-ghost[disabled] > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-dashed {
  border-color: #303030;
  background: 0 0;
  color: #c9d1d9;
}

[data-theme='dark'] .ant-btn-dashed > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-dashed:focus,
[data-theme='dark'] .ant-btn-dashed:hover {
  border-color: #2a7dc9;
  background: 0 0;
  color: #2a7dc9;
}

[data-theme='dark'] .ant-btn-dashed:focus > a:only-child::after,
[data-theme='dark'] .ant-btn-dashed:hover > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-dashed:active {
  border-color: #004496;
  background: 0 0;
  color: #004496;
}

[data-theme='dark'] .ant-btn-dashed:active > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-dashed[disabled],
[data-theme='dark'] .ant-btn-dashed[disabled]:active,
[data-theme='dark'] .ant-btn-dashed[disabled]:focus,
[data-theme='dark'] .ant-btn-dashed[disabled]:hover {
  border-color: #303030;
  background: rgb(255 255 255 / 8%);
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-btn-dashed[disabled]:active > a:only-child::after,
[data-theme='dark'] .ant-btn-dashed[disabled]:focus > a:only-child::after,
[data-theme='dark'] .ant-btn-dashed[disabled]:hover > a:only-child::after,
[data-theme='dark'] .ant-btn-dashed[disabled] > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-danger {
  border-color: #ed6f6f;
  background: #ed6f6f;
  box-shadow: 0 2px 0 rgb(0 0 0 / 4.5%);
  color: #fff;
}

[data-theme='dark'] .ant-btn-danger > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-danger:focus,
[data-theme='dark'] .ant-btn-danger:hover {
  border-color: #c75457;
  background: #c75457;
  color: #fff;
}

[data-theme='dark'] .ant-btn-danger:focus > a:only-child::after,
[data-theme='dark'] .ant-btn-danger:hover > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-danger:active {
  border-color: #faa19d;
  background: #faa19d;
  color: #fff;
}

[data-theme='dark'] .ant-btn-danger:active > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-danger[disabled],
[data-theme='dark'] .ant-btn-danger[disabled]:active,
[data-theme='dark'] .ant-btn-danger[disabled]:focus,
[data-theme='dark'] .ant-btn-danger[disabled]:hover {
  border-color: #303030;
  background: rgb(255 255 255 / 8%);
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-btn-danger[disabled]:active > a:only-child::after,
[data-theme='dark'] .ant-btn-danger[disabled]:focus > a:only-child::after,
[data-theme='dark'] .ant-btn-danger[disabled]:hover > a:only-child::after,
[data-theme='dark'] .ant-btn-danger[disabled] > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-link {
  border-color: transparent;
  background: 0 0;
  color: #0960bd;
}

[data-theme='dark'] .ant-btn-link > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-link:focus,
[data-theme='dark'] .ant-btn-link:hover {
  border-color: transparent;
  background: 0 0;
  color: #2a7dc9;
}

[data-theme='dark'] .ant-btn-link:focus > a:only-child::after,
[data-theme='dark'] .ant-btn-link:hover > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-link:active {
  border-color: #004496;
  border-color: transparent;
  background: 0 0;
  color: #004496;
}

[data-theme='dark'] .ant-btn-link:active > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-link:hover {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-link[disabled],
[data-theme='dark'] .ant-btn-link[disabled]:active,
[data-theme='dark'] .ant-btn-link[disabled]:focus,
[data-theme='dark'] .ant-btn-link[disabled]:hover {
  border-color: transparent;
  background: 0 0;
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-btn-link[disabled]:active > a:only-child::after,
[data-theme='dark'] .ant-btn-link[disabled]:focus > a:only-child::after,
[data-theme='dark'] .ant-btn-link[disabled]:hover > a:only-child::after,
[data-theme='dark'] .ant-btn-link[disabled] > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-text {
  border-color: transparent;
  background: 0 0;
  color: #c9d1d9;
}

[data-theme='dark'] .ant-btn-text > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-text:focus > a:only-child::after,
[data-theme='dark'] .ant-btn-text:hover > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-text:active {
  border-color: transparent;
  background: rgb(255 255 255 / 4%);
  color: #c9d1d9;
}

[data-theme='dark'] .ant-btn-text:active > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-text:focus,
[data-theme='dark'] .ant-btn-text:hover {
  border-color: transparent;
  background: rgb(255 255 255 / 3%);
  color: #c9d1d9;
}

[data-theme='dark'] .ant-btn-text[disabled],
[data-theme='dark'] .ant-btn-text[disabled]:active,
[data-theme='dark'] .ant-btn-text[disabled]:focus,
[data-theme='dark'] .ant-btn-text[disabled]:hover {
  border-color: transparent;
  background: 0 0;
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-btn-text[disabled]:active > a:only-child::after,
[data-theme='dark'] .ant-btn-text[disabled]:focus > a:only-child::after,
[data-theme='dark'] .ant-btn-text[disabled]:hover > a:only-child::after,
[data-theme='dark'] .ant-btn-text[disabled] > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-dangerous {
  border-color: #ed6f6f;
  background: 0 0;
  color: #ed6f6f;
}

[data-theme='dark'] .ant-btn-dangerous > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-dangerous:focus,
[data-theme='dark'] .ant-btn-dangerous:hover {
  border-color: #c75457;
  background: 0 0;
  color: #c75457;
}

[data-theme='dark'] .ant-btn-dangerous:focus > a:only-child::after,
[data-theme='dark'] .ant-btn-dangerous:hover > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-dangerous:active {
  border-color: #faa19d;
  background: 0 0;
  color: #faa19d;
}

[data-theme='dark'] .ant-btn-dangerous:active > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-dangerous[disabled],
[data-theme='dark'] .ant-btn-dangerous[disabled]:active,
[data-theme='dark'] .ant-btn-dangerous[disabled]:focus,
[data-theme='dark'] .ant-btn-dangerous[disabled]:hover {
  border-color: #303030;
  background: rgb(255 255 255 / 8%);
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-btn-dangerous[disabled]:active > a:only-child::after,
[data-theme='dark'] .ant-btn-dangerous[disabled]:focus > a:only-child::after,
[data-theme='dark'] .ant-btn-dangerous[disabled]:hover > a:only-child::after,
[data-theme='dark'] .ant-btn-dangerous[disabled] > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-dangerous.ant-btn-primary {
  border-color: #ed6f6f;
  background: #ed6f6f !important;
  box-shadow: 0 2px 0 rgb(0 0 0 / 4.5%);
  color: #fff;
}

[data-theme='dark'] .ant-btn-dangerous.ant-btn-primary > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-dangerous.ant-btn-primary:focus,
[data-theme='dark'] .ant-btn-dangerous.ant-btn-primary:hover {
  border-color: #c75457;
  background: #c75457;
  color: #fff;
}

[data-theme='dark'] .ant-btn-dangerous.ant-btn-primary:focus > a:only-child::after,
[data-theme='dark'] .ant-btn-dangerous.ant-btn-primary:hover > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-dangerous.ant-btn-primary:active {
  border-color: #faa19d;
  background: #faa19d;
  color: #fff;
}

[data-theme='dark'] .ant-btn-dangerous.ant-btn-primary:active > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-dangerous.ant-btn-primary[disabled],
[data-theme='dark'] .ant-btn-dangerous.ant-btn-primary[disabled]:active,
[data-theme='dark'] .ant-btn-dangerous.ant-btn-primary[disabled]:focus,
[data-theme='dark'] .ant-btn-dangerous.ant-btn-primary[disabled]:hover {
  border-color: #303030;
  background: rgb(255 255 255 / 8%);
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-btn-dangerous.ant-btn-primary[disabled]:active > a:only-child::after,
[data-theme='dark'] .ant-btn-dangerous.ant-btn-primary[disabled]:focus > a:only-child::after,
[data-theme='dark'] .ant-btn-dangerous.ant-btn-primary[disabled]:hover > a:only-child::after,
[data-theme='dark'] .ant-btn-dangerous.ant-btn-primary[disabled] > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-dangerous.ant-btn-link {
  border-color: transparent;
  background: 0 0;
  color: #ed6f6f;
}

[data-theme='dark'] .ant-btn-dangerous.ant-btn-link > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-dangerous.ant-btn-link:active > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-dangerous.ant-btn-link:focus,
[data-theme='dark'] .ant-btn-dangerous.ant-btn-link:hover {
  border-color: transparent;
  background: 0 0;
  color: #c75457;
}

[data-theme='dark'] .ant-btn-dangerous.ant-btn-link:focus > a:only-child::after,
[data-theme='dark'] .ant-btn-dangerous.ant-btn-link:hover > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-dangerous.ant-btn-link:active {
  border-color: transparent;
  background: 0 0;
  color: #faa19d;
}

[data-theme='dark'] .ant-btn-dangerous.ant-btn-link[disabled],
[data-theme='dark'] .ant-btn-dangerous.ant-btn-link[disabled]:active,
[data-theme='dark'] .ant-btn-dangerous.ant-btn-link[disabled]:focus,
[data-theme='dark'] .ant-btn-dangerous.ant-btn-link[disabled]:hover {
  border-color: transparent;
  background: 0 0;
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-btn-dangerous.ant-btn-link[disabled]:active > a:only-child::after,
[data-theme='dark'] .ant-btn-dangerous.ant-btn-link[disabled]:focus > a:only-child::after,
[data-theme='dark'] .ant-btn-dangerous.ant-btn-link[disabled]:hover > a:only-child::after,
[data-theme='dark'] .ant-btn-dangerous.ant-btn-link[disabled] > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-dangerous.ant-btn-text {
  border-color: transparent;
  background: 0 0;
  color: #ed6f6f;
}

[data-theme='dark'] .ant-btn-dangerous.ant-btn-text > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-dangerous.ant-btn-text:active > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-dangerous.ant-btn-text:focus,
[data-theme='dark'] .ant-btn-dangerous.ant-btn-text:hover {
  border-color: transparent;
  background: rgb(255 255 255 / 3%);
  color: #c75457;
}

[data-theme='dark'] .ant-btn-dangerous.ant-btn-text:focus > a:only-child::after,
[data-theme='dark'] .ant-btn-dangerous.ant-btn-text:hover > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-dangerous.ant-btn-text:active {
  border-color: transparent;
  background: rgb(255 255 255 / 4%);
  color: #faa19d;
}

[data-theme='dark'] .ant-btn-dangerous.ant-btn-text[disabled],
[data-theme='dark'] .ant-btn-dangerous.ant-btn-text[disabled]:active,
[data-theme='dark'] .ant-btn-dangerous.ant-btn-text[disabled]:focus,
[data-theme='dark'] .ant-btn-dangerous.ant-btn-text[disabled]:hover {
  border-color: transparent;
  background: 0 0;
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-btn-dangerous.ant-btn-text[disabled]:active > a:only-child::after,
[data-theme='dark'] .ant-btn-dangerous.ant-btn-text[disabled]:focus > a:only-child::after,
[data-theme='dark'] .ant-btn-dangerous.ant-btn-text[disabled]:hover > a:only-child::after,
[data-theme='dark'] .ant-btn-dangerous.ant-btn-text[disabled] > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn::before {
  background: #151515;
}

[data-theme='dark']
  .ant-btn-group
  .ant-btn-primary
  + .ant-btn:not(.ant-btn-primary):not([disabled]) {
  border-left-color: transparent;
}

[data-theme='dark'] .ant-btn.ant-btn-background-ghost {
  border-color: rgb(255 255 255 / 25%);
  color: #c9d1d9;
}

[data-theme='dark'] .ant-btn.ant-btn-background-ghost,
[data-theme='dark'] .ant-btn.ant-btn-background-ghost:active,
[data-theme='dark'] .ant-btn.ant-btn-background-ghost:focus,
[data-theme='dark'] .ant-btn.ant-btn-background-ghost:hover {
  background: 0 0;
}

[data-theme='dark'] .ant-btn.ant-btn-background-ghost:focus,
[data-theme='dark'] .ant-btn.ant-btn-background-ghost:hover {
  border-color: #2a7dc9;
  color: #2a7dc9;
}

[data-theme='dark'] .ant-btn.ant-btn-background-ghost:active {
  border-color: #004496;
  color: #004496;
}

[data-theme='dark'] .ant-btn.ant-btn-background-ghost[disabled] {
  border-color: #303030;
  background: 0 0;
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-primary {
  border-color: #0960bd;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-primary > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-primary:focus,
[data-theme='dark'] .ant-btn-background-ghost.ant-btn-primary:hover {
  border-color: #004496;
  color: #004496;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-primary:focus > a:only-child::after,
[data-theme='dark'] .ant-btn-background-ghost.ant-btn-primary:hover > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-primary:active {
  border-color: #2a7dc9;
  color: #2a7dc9;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-primary:active > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-primary[disabled],
[data-theme='dark'] .ant-btn-background-ghost.ant-btn-primary[disabled]:active,
[data-theme='dark'] .ant-btn-background-ghost.ant-btn-primary[disabled]:focus,
[data-theme='dark'] .ant-btn-background-ghost.ant-btn-primary[disabled]:hover {
  border-color: #303030;
  background: rgb(255 255 255 / 8%);
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark']
  .ant-btn-background-ghost.ant-btn-primary[disabled]:active
  > a:only-child::after,
[data-theme='dark'] .ant-btn-background-ghost.ant-btn-primary[disabled]:focus > a:only-child::after,
[data-theme='dark'] .ant-btn-background-ghost.ant-btn-primary[disabled]:hover > a:only-child::after,
[data-theme='dark'] .ant-btn-background-ghost.ant-btn-primary[disabled] > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-danger {
  border-color: #ed6f6f;
  color: #ed6f6f;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-danger > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-danger:focus,
[data-theme='dark'] .ant-btn-background-ghost.ant-btn-danger:hover {
  border-color: #c75457;
  color: #c75457;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-danger:focus > a:only-child::after,
[data-theme='dark'] .ant-btn-background-ghost.ant-btn-danger:hover > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-danger:active {
  border-color: #faa19d;
  color: #faa19d;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-danger:active > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-danger[disabled],
[data-theme='dark'] .ant-btn-background-ghost.ant-btn-danger[disabled]:active,
[data-theme='dark'] .ant-btn-background-ghost.ant-btn-danger[disabled]:focus,
[data-theme='dark'] .ant-btn-background-ghost.ant-btn-danger[disabled]:hover {
  border-color: #303030;
  background: rgb(255 255 255 / 8%);
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-danger[disabled]:active > a:only-child::after,
[data-theme='dark'] .ant-btn-background-ghost.ant-btn-danger[disabled]:focus > a:only-child::after,
[data-theme='dark'] .ant-btn-background-ghost.ant-btn-danger[disabled]:hover > a:only-child::after,
[data-theme='dark'] .ant-btn-background-ghost.ant-btn-danger[disabled] > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-dangerous {
  border-color: #ed6f6f;
  color: #ed6f6f;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-dangerous > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-dangerous:focus,
[data-theme='dark'] .ant-btn-background-ghost.ant-btn-dangerous:hover {
  border-color: #c75457;
  color: #c75457;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-dangerous:focus > a:only-child::after,
[data-theme='dark'] .ant-btn-background-ghost.ant-btn-dangerous:hover > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-dangerous:active {
  border-color: #faa19d;
  color: #faa19d;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-dangerous:active > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-dangerous[disabled],
[data-theme='dark'] .ant-btn-background-ghost.ant-btn-dangerous[disabled]:active,
[data-theme='dark'] .ant-btn-background-ghost.ant-btn-dangerous[disabled]:focus,
[data-theme='dark'] .ant-btn-background-ghost.ant-btn-dangerous[disabled]:hover {
  border-color: #303030;
  background: rgb(255 255 255 / 8%);
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark']
  .ant-btn-background-ghost.ant-btn-dangerous[disabled]:active
  > a:only-child::after,
[data-theme='dark']
  .ant-btn-background-ghost.ant-btn-dangerous[disabled]:focus
  > a:only-child::after,
[data-theme='dark']
  .ant-btn-background-ghost.ant-btn-dangerous[disabled]:hover
  > a:only-child::after,
[data-theme='dark'] .ant-btn-background-ghost.ant-btn-dangerous[disabled] > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link {
  border-color: transparent;
  color: #ed6f6f;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link:focus,
[data-theme='dark'] .ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link:hover {
  border-color: transparent;
  color: #c75457;
}

[data-theme='dark']
  .ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link:focus
  > a:only-child::after,
[data-theme='dark']
  .ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link:hover
  > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link:active {
  border-color: transparent;
  color: #faa19d;
}

[data-theme='dark']
  .ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link:active
  > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link[disabled],
[data-theme='dark'] .ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link[disabled]:active,
[data-theme='dark'] .ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link[disabled]:focus,
[data-theme='dark'] .ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link[disabled]:hover {
  border-color: #303030;
  background: rgb(255 255 255 / 8%);
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark']
  .ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link[disabled]:active
  > a:only-child::after,
[data-theme='dark']
  .ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link[disabled]:focus
  > a:only-child::after,
[data-theme='dark']
  .ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link[disabled]:hover
  > a:only-child::after,
[data-theme='dark']
  .ant-btn-background-ghost.ant-btn-dangerous.ant-btn-link[disabled]
  > a:only-child::after {
  background: 0 0;
}

[data-theme='dark'] .ant-btn-group-rtl.ant-btn-group .ant-btn-primary + .ant-btn-primary,
[data-theme='dark'] .ant-btn-group-rtl.ant-btn-group .ant-btn-primary:last-child:not(:first-child) {
  border-right-color: #2a7dc9;
  border-left-color: #303030;
}

[data-theme='dark'] .ant-btn-group-rtl.ant-btn-group .ant-btn-primary + .ant-btn-primary[disabled],
[data-theme='dark']
  .ant-btn-group-rtl.ant-btn-group
  .ant-btn-primary:last-child:not(:first-child)[disabled] {
  border-right-color: #303030;
  border-left-color: #2a7dc9;
}

[data-theme='dark'] .ant-radio-group {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-radio-wrapper {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-radio {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-radio-input:focus + .ant-radio-inner,
[data-theme='dark'] .ant-radio-wrapper:hover .ant-radio,
[data-theme='dark'] .ant-radio:hover .ant-radio-inner {
  border-color: #0960bd;
}

[data-theme='dark'] .ant-radio-input:focus + .ant-radio-inner {
  box-shadow: 0 0 0 3px rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-radio-checked::after {
  border: 1px solid #0960bd;
}

[data-theme='dark'] .ant-radio-inner {
  border-color: #303030;
  background-color: transparent;
}

[data-theme='dark'] .ant-radio-inner::after {
  background-color: #0960bd;
}

[data-theme='dark'] .ant-radio-checked .ant-radio-inner {
  border-color: #0960bd;
}

[data-theme='dark'] .ant-radio-disabled .ant-radio-inner {
  border-color: #303030 !important;
  background-color: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-radio-disabled .ant-radio-inner::after {
  background-color: rgb(255 255 255 / 20%);
}

[data-theme='dark'] .ant-radio-disabled + span {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-radio-button-wrapper {
  border: 1px solid #303030;
  background: 0 0;
  color: #c9d1d9;
}

[data-theme='dark'] .ant-radio-button-wrapper a {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-radio-button-wrapper:not(:first-child)::before {
  background-color: #303030;
}

[data-theme='dark'] .ant-radio-button-wrapper:first-child {
  border-left: 1px solid #303030;
}

[data-theme='dark'] .ant-radio-button-wrapper:hover {
  color: #0960bd;
}

[data-theme='dark'] .ant-radio-button-wrapper:focus-within {
  box-shadow: 0 0 0 3px rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
  border-color: #0960bd;
  background: 0 0;
  color: #0960bd;
}

[data-theme='dark']
  .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)::before {
  background-color: #0960bd;
}

[data-theme='dark']
  .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):first-child {
  border-color: #0960bd;
}

[data-theme='dark']
  .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):hover {
  border-color: #2a7dc9;
  color: #2a7dc9;
}

[data-theme='dark']
  .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):hover::before {
  background-color: #2a7dc9;
}

[data-theme='dark']
  .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):active {
  border-color: #004496;
  color: #004496;
}

[data-theme='dark']
  .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):active::before {
  background-color: #004496;
}

[data-theme='dark']
  .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within {
  box-shadow: 0 0 0 3px rgb(255 255 255 / 8%);
}

[data-theme='dark']
  .ant-radio-group-solid
  .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
  border-color: #0960bd;
  background: #0960bd;
  color: #fff;
}

[data-theme='dark']
  .ant-radio-group-solid
  .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):hover {
  border-color: #2a7dc9;
  background: #2a7dc9;
  color: #fff;
}

[data-theme='dark']
  .ant-radio-group-solid
  .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):active {
  border-color: #004496;
  background: #004496;
  color: #fff;
}

[data-theme='dark']
  .ant-radio-group-solid
  .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within {
  box-shadow: 0 0 0 3px rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-radio-button-wrapper-disabled {
  border-color: #303030;
  background-color: rgb(255 255 255 / 8%);
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-radio-button-wrapper-disabled:first-child,
[data-theme='dark'] .ant-radio-button-wrapper-disabled:hover {
  border-color: #303030;
  background-color: rgb(255 255 255 / 8%);
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-radio-button-wrapper-disabled:first-child {
  border-left-color: #303030;
}

[data-theme='dark'] .ant-radio-button-wrapper-disabled.ant-radio-button-wrapper-checked {
  border-color: #303030;
  background-color: rgb(255 255 255 / 20%);
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark']
  .ant-radio-button-wrapper.ant-radio-button-wrapper-rtl.ant-radio-button-wrapper:first-child {
  border-right: 1px solid #303030;
}

[data-theme='dark']
  .ant-radio-button-wrapper-checked:not(
    [class*=' ant-radio-button-wrapper-disabled']
  ).ant-radio-button-wrapper:first-child {
  border-right-color: #2a7dc9;
}

[data-theme='dark']
  .ant-radio-button-wrapper.ant-radio-button-wrapper-rtl.ant-radio-button-wrapper-disabled:first-child {
  border-right-color: #303030;
}

[data-theme='dark'] .ant-checkbox {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-checkbox-input:focus + .ant-checkbox-inner,
[data-theme='dark'] .ant-checkbox-wrapper:hover .ant-checkbox-inner,
[data-theme='dark'] .ant-checkbox:hover .ant-checkbox-inner {
  border-color: #0960bd;
}

[data-theme='dark'] .ant-checkbox-checked::after {
  border: 1px solid #0960bd;
}

[data-theme='dark'] .ant-checkbox-inner {
  border: 1px solid #303030;
  background-color: transparent;
}

[data-theme='dark'] .ant-checkbox-inner::after {
  border: 2px solid #fff;
}

[data-theme='dark'] .ant-checkbox-checked .ant-checkbox-inner::after {
  border: 2px solid #fff;
}

[data-theme='dark'] .ant-checkbox-checked .ant-checkbox-inner {
  border-color: #0960bd;
  background-color: #0960bd;
}

[data-theme='dark'] .ant-checkbox-disabled.ant-checkbox-checked .ant-checkbox-inner::after {
  border-color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-checkbox-disabled .ant-checkbox-inner {
  border-color: #303030 !important;
  background-color: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-checkbox-disabled .ant-checkbox-inner::after {
  border-color: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-checkbox-disabled + span {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-checkbox-wrapper {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-checkbox-group {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-checkbox-indeterminate .ant-checkbox-inner {
  border-color: #303030;
  background-color: transparent;
}

[data-theme='dark'] .ant-checkbox-indeterminate .ant-checkbox-inner::after {
  background-color: #0960bd;
}

[data-theme='dark'] .ant-checkbox-indeterminate.ant-checkbox-disabled .ant-checkbox-inner::after {
  border-color: rgb(255 255 255 / 30%);
  background-color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-tag {
  border: 1px solid #303030;
  background: rgb(255 255 255 / 4%);
  color: #c9d1d9;
}

[data-theme='dark'] .ant-tag,
[data-theme='dark'] .ant-tag a,
[data-theme='dark'] .ant-tag a:hover {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-tag-close-icon {
  color: #8b949e;
}

[data-theme='dark'] .ant-tag-close-icon:hover {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .ant-tag-has-color {
  border-color: transparent;
}

[data-theme='dark'] .ant-tag-has-color,
[data-theme='dark'] .ant-tag-has-color .anticon-close,
[data-theme='dark'] .ant-tag-has-color .anticon-close:hover,
[data-theme='dark'] .ant-tag-has-color a,
[data-theme='dark'] .ant-tag-has-color a:hover {
  color: #fff;
}

[data-theme='dark'] .ant-tag-checkable {
  border-color: transparent;
  background-color: transparent;
}

[data-theme='dark'] .ant-tag-checkable:not(.ant-tag-checkable-checked):hover {
  color: #0960bd;
}

[data-theme='dark'] .ant-tag-checkable-checked,
[data-theme='dark'] .ant-tag-checkable:active {
  color: #fff;
}

[data-theme='dark'] .ant-tag-checkable-checked {
  background-color: #0960bd;
}

[data-theme='dark'] .ant-tag-checkable:active {
  background-color: #004496;
}

[data-theme='dark'] .ant-tag-pink {
  border-color: #551d3c;
  background: #2a1421;
  color: #e0529c;
}

[data-theme='dark'] .ant-tag-pink-inverse {
  border-color: #cb2b83;
  background: #cb2b83;
  color: #fff;
}

[data-theme='dark'] .ant-tag-magenta {
  border-color: #551d3c;
  background: #2a1421;
  color: #e0529c;
}

[data-theme='dark'] .ant-tag-magenta-inverse {
  border-color: #cb2b83;
  background: #cb2b83;
  color: #fff;
}

[data-theme='dark'] .ant-tag-red {
  border-color: #58191c;
  background: #2b1316;
  color: #e84749;
}

[data-theme='dark'] .ant-tag-red-inverse {
  border-color: #d32029;
  background: #d32029;
  color: #fff;
}

[data-theme='dark'] .ant-tag-volcano {
  border-color: #5a2817;
  background: #2c1712;
  color: #e87040;
}

[data-theme='dark'] .ant-tag-volcano-inverse {
  border-color: #d84b1b;
  background: #d84b1b;
  color: #fff;
}

[data-theme='dark'] .ant-tag-orange {
  border-color: #5a3915;
  background: #2c1e12;
  color: #e89a3c;
}

[data-theme='dark'] .ant-tag-orange-inverse {
  border-color: #d87a16;
  background: #d87a16;
  color: #fff;
}

[data-theme='dark'] .ant-tag-yellow {
  border-color: #5a5015;
  background: #2c2712;
  color: #e8d739;
}

[data-theme='dark'] .ant-tag-yellow-inverse {
  border-color: #d8bd14;
  background: #d8bd14;
  color: #fff;
}

[data-theme='dark'] .ant-tag-gold {
  border-color: #5a4315;
  background: #2c2112;
  color: #e8b339;
}

[data-theme='dark'] .ant-tag-gold-inverse {
  border-color: #d89614;
  background: #d89614;
  color: #fff;
}

[data-theme='dark'] .ant-tag-cyan {
  border-color: #144949;
  background: #122223;
  color: #33bcb7;
}

[data-theme='dark'] .ant-tag-cyan-inverse {
  border-color: #13a8a8;
  background: #13a8a8;
  color: #fff;
}

[data-theme='dark'] .ant-tag-lime {
  border-color: #3f5014;
  background: #202712;
  color: #aad134;
}

[data-theme='dark'] .ant-tag-lime-inverse {
  border-color: #8bbc12;
  background: #8bbc12;
  color: #fff;
}

[data-theme='dark'] .ant-tag-green {
  border-color: #274a17;
  background: #172412;
  color: #6abe39;
}

[data-theme='dark'] .ant-tag-green-inverse {
  border-color: #49aa19;
  background: #49aa19;
  color: #fff;
}

[data-theme='dark'] .ant-tag-blue {
  border-color: #163a5b;
  background: #121e2d;
  color: #3c9ae8;
}

[data-theme='dark'] .ant-tag-blue-inverse {
  border-color: #187edc;
  background: #187edc;
  color: #fff;
}

[data-theme='dark'] .ant-tag-geekblue {
  border-color: #1d2855;
  background: #14172a;
  color: #5274e0;
}

[data-theme='dark'] .ant-tag-geekblue-inverse {
  border-color: #2b4bcb;
  background: #2b4bcb;
  color: #fff;
}

[data-theme='dark'] .ant-tag-purple {
  border-color: #311d4d;
  background: #1a1426;
  color: #864eca;
}

[data-theme='dark'] .ant-tag-purple-inverse {
  border-color: #642ab5;
  background: #642ab5;
  color: #fff;
}

[data-theme='dark'] .ant-tag-success {
  border-color: #274a17;
  background: #172412;
  color: #55d187;
}

[data-theme='dark'] .ant-tag-processing {
  border-color: #78b7e3;
  background: rgb(255 255 255 / 8%);
  color: #0960bd;
}

[data-theme='dark'] .ant-tag-error {
  border-color: #58191c;
  background: #2b1316;
  color: #ed6f6f;
}

[data-theme='dark'] .ant-tag-warning {
  border-color: #5a3915;
  background: #2c1e12;
  color: #efbd47;
}

[data-theme='dark'] .ant-rate {
  color: #d8bd14;
}

[data-theme='dark'] .ant-rate-star > div:focus-visible {
  outline: 1px dashed #d8bd14;
}

[data-theme='dark'] .ant-rate-star-first,
[data-theme='dark'] .ant-rate-star-second {
  color: rgb(255 255 255 / 12%);
}

[data-theme='dark'] .ant-pagination {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-pagination-item {
  border: 1px solid #303030;
  background-color: transparent;
}

[data-theme='dark'] .ant-pagination-item a {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-pagination-item:hover {
  border-color: #0960bd;
}

[data-theme='dark'] .ant-pagination-item:hover a {
  color: #0960bd;
}

[data-theme='dark'] .ant-pagination-item:focus-visible {
  border-color: #0960bd;
}

[data-theme='dark'] .ant-pagination-item:focus-visible a {
  color: #0960bd;
}

[data-theme='dark'] .ant-pagination-item-active {
  border-color: #0960bd;
  background: 0 0;
}

[data-theme='dark'] .ant-pagination-item-active a {
  color: #0960bd;
}

[data-theme='dark'] .ant-pagination-item-active:hover {
  border-color: #2a7dc9;
}

[data-theme='dark'] .ant-pagination-item-active:focus-visible {
  border-color: #2a7dc9;
}

[data-theme='dark'] .ant-pagination-item-active:hover a {
  color: #2a7dc9;
}

[data-theme='dark'] .ant-pagination-item-active:focus-visible a {
  color: #2a7dc9;
}

[data-theme='dark']
  .ant-pagination-jump-next
  .ant-pagination-item-container
  .ant-pagination-item-link-icon,
[data-theme='dark']
  .ant-pagination-jump-prev
  .ant-pagination-item-container
  .ant-pagination-item-link-icon {
  color: #0960bd;
}

[data-theme='dark']
  .ant-pagination-jump-next
  .ant-pagination-item-container
  .ant-pagination-item-ellipsis,
[data-theme='dark']
  .ant-pagination-jump-prev
  .ant-pagination-item-container
  .ant-pagination-item-ellipsis {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-pagination-jump-next,
[data-theme='dark'] .ant-pagination-jump-prev,
[data-theme='dark'] .ant-pagination-next,
[data-theme='dark'] .ant-pagination-prev {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-pagination-next button,
[data-theme='dark'] .ant-pagination-prev button {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-pagination-next:hover button,
[data-theme='dark'] .ant-pagination-prev:hover button {
  border-color: #2a7dc9;
}

[data-theme='dark'] .ant-pagination-next .ant-pagination-item-link,
[data-theme='dark'] .ant-pagination-prev .ant-pagination-item-link {
  border: 1px solid #303030;
  background-color: transparent;
}

[data-theme='dark'] .ant-pagination-next:focus-visible .ant-pagination-item-link,
[data-theme='dark'] .ant-pagination-prev:focus-visible .ant-pagination-item-link {
  border-color: #0960bd;
  color: #0960bd;
}

[data-theme='dark'] .ant-pagination-next:hover .ant-pagination-item-link,
[data-theme='dark'] .ant-pagination-prev:hover .ant-pagination-item-link {
  border-color: #0960bd;
  color: #0960bd;
}

[data-theme='dark'] .ant-pagination-disabled .ant-pagination-item-link,
[data-theme='dark'] .ant-pagination-disabled:hover .ant-pagination-item-link {
  border-color: #303030;
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-pagination-disabled:focus-visible .ant-pagination-item-link {
  border-color: #303030;
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-pagination-options-quick-jumper input {
  border: 1px solid #303030;
  background-color: transparent;
  color: #c9d1d9;
}

[data-theme='dark'] .ant-pagination-options-quick-jumper input::placeholder {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-pagination-options-quick-jumper input:hover {
  border-color: #2a7dc9;
}

[data-theme='dark'] .ant-pagination-options-quick-jumper input-focused,
[data-theme='dark'] .ant-pagination-options-quick-jumper input:focus {
  border-color: #0960bd;
  box-shadow: 0 0 0 2px rgb(9 96 189 / 20%);
}

[data-theme='dark'] .ant-pagination-options-quick-jumper input-disabled {
  border-color: #303030;
  background-color: rgb(255 255 255 / 8%);
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-pagination-options-quick-jumper input-disabled:hover {
  border-color: #303030;
}

[data-theme='dark'] .ant-pagination-options-quick-jumper input[disabled] {
  border-color: #303030;
  background-color: rgb(255 255 255 / 8%);
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-pagination-options-quick-jumper input[disabled]:hover {
  border-color: #303030;
}

[data-theme='dark'] .ant-pagination-options-quick-jumper input-borderless,
[data-theme='dark'] .ant-pagination-options-quick-jumper input-borderless-disabled,
[data-theme='dark'] .ant-pagination-options-quick-jumper input-borderless-focused,
[data-theme='dark'] .ant-pagination-options-quick-jumper input-borderless:focus,
[data-theme='dark'] .ant-pagination-options-quick-jumper input-borderless:hover,
[data-theme='dark'] .ant-pagination-options-quick-jumper input-borderless[disabled] {
  background-color: transparent;
}

[data-theme='dark'] .ant-pagination-simple .ant-pagination-next .ant-pagination-item-link,
[data-theme='dark'] .ant-pagination-simple .ant-pagination-prev .ant-pagination-item-link {
  background-color: transparent;
}

[data-theme='dark'] .ant-pagination-simple .ant-pagination-simple-pager input {
  border: 1px solid #303030;
  background-color: transparent;
}

[data-theme='dark'] .ant-pagination-simple .ant-pagination-simple-pager input:hover {
  border-color: #0960bd;
}

[data-theme='dark'] .ant-pagination-simple .ant-pagination-simple-pager input:focus {
  border-color: #2a7dc9;
  box-shadow: 0 0 0 2px rgb(9 96 189 / 20%);
}

[data-theme='dark'] .ant-pagination-simple .ant-pagination-simple-pager input[disabled] {
  border-color: #303030;
  background: rgb(255 255 255 / 8%);
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-pagination.mini .ant-pagination-item:not(.ant-pagination-item-active) {
  border-color: transparent;
  background: 0 0;
}

[data-theme='dark'] .ant-pagination.mini .ant-pagination-next .ant-pagination-item-link,
[data-theme='dark'] .ant-pagination.mini .ant-pagination-prev .ant-pagination-item-link {
  border-color: transparent;
  background: 0 0;
}

[data-theme='dark'] .ant-pagination.ant-pagination-disabled .ant-pagination-item {
  border-color: #303030;
  background: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-pagination.ant-pagination-disabled .ant-pagination-item a {
  background: 0 0;
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-pagination.ant-pagination-disabled .ant-pagination-item-active {
  background: rgb(255 255 255 / 25%);
}

[data-theme='dark'] .ant-pagination.ant-pagination-disabled .ant-pagination-item-active a {
  color: #000;
}

[data-theme='dark'] .ant-pagination.ant-pagination-disabled .ant-pagination-item-link {
  border-color: #303030;
  background: rgb(255 255 255 / 8%);
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark']
  .ant-pagination-simple.ant-pagination.ant-pagination-disabled
  .ant-pagination-item-link {
  background: 0 0;
}

[data-theme='dark'] .ant-pagination.ant-pagination-disabled .ant-pagination-simple-pager {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-avatar {
  background: rgb(255 255 255 / 30%);
  color: #fff;
}

[data-theme='dark'] .ant-avatar-image {
  background: 0 0;
}

[data-theme='dark'] .ant-avatar-group .ant-avatar {
  border: 1px solid #fff;
}

[data-theme='dark'] .ant-badge {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-badge-count {
  background: #a71d25;
  box-shadow: 0 0 0 1px #151515;
  color: #fff;
}

[data-theme='dark'] .ant-badge-count a,
[data-theme='dark'] .ant-badge-count a:hover {
  color: #fff;
}

[data-theme='dark'] .ant-badge-dot {
  background: #a71d25;
  box-shadow: 0 0 0 1px #151515;
}

[data-theme='dark'] .ant-badge-status-success {
  background-color: #55d187;
}

[data-theme='dark'] .ant-badge-status-processing {
  background-color: #0960bd;
}

[data-theme='dark'] .ant-badge-status-processing::after {
  border: 1px solid #0960bd;
}

[data-theme='dark'] .ant-badge-status-default {
  background-color: #d9d9d9;
}

[data-theme='dark'] .ant-badge-status-error {
  background-color: #ed6f6f;
}

[data-theme='dark'] .ant-badge-status-warning {
  background-color: #efbd47;
}

[data-theme='dark'] .ant-badge-status-pink {
  background: #cb2b83;
}

[data-theme='dark'] .ant-badge-status-magenta {
  background: #cb2b83;
}

[data-theme='dark'] .ant-badge-status-red {
  background: #d32029;
}

[data-theme='dark'] .ant-badge-status-volcano {
  background: #d84b1b;
}

[data-theme='dark'] .ant-badge-status-orange {
  background: #d87a16;
}

[data-theme='dark'] .ant-badge-status-yellow {
  background: #d8bd14;
}

[data-theme='dark'] .ant-badge-status-gold {
  background: #d89614;
}

[data-theme='dark'] .ant-badge-status-cyan {
  background: #13a8a8;
}

[data-theme='dark'] .ant-badge-status-lime {
  background: #8bbc12;
}

[data-theme='dark'] .ant-badge-status-green {
  background: #49aa19;
}

[data-theme='dark'] .ant-badge-status-blue {
  background: #187edc;
}

[data-theme='dark'] .ant-badge-status-geekblue {
  background: #2b4bcb;
}

[data-theme='dark'] .ant-badge-status-purple {
  background: #642ab5;
}

[data-theme='dark'] .ant-badge-status-text {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-ribbon {
  background-color: #0960bd;
  color: #fff;
}

[data-theme='dark'] .ant-ribbon-text {
  color: #fff;
}

[data-theme='dark'] .ant-ribbon-corner::after {
  color: rgb(0 0 0 / 25%);
}

[data-theme='dark'] .ant-ribbon-color-pink {
  background: #cb2b83;
  color: #cb2b83;
}

[data-theme='dark'] .ant-ribbon-color-magenta {
  background: #cb2b83;
  color: #cb2b83;
}

[data-theme='dark'] .ant-ribbon-color-red {
  background: #d32029;
  color: #d32029;
}

[data-theme='dark'] .ant-ribbon-color-volcano {
  background: #d84b1b;
  color: #d84b1b;
}

[data-theme='dark'] .ant-ribbon-color-orange {
  background: #d87a16;
  color: #d87a16;
}

[data-theme='dark'] .ant-ribbon-color-yellow {
  background: #d8bd14;
  color: #d8bd14;
}

[data-theme='dark'] .ant-ribbon-color-gold {
  background: #d89614;
  color: #d89614;
}

[data-theme='dark'] .ant-ribbon-color-cyan {
  background: #13a8a8;
  color: #13a8a8;
}

[data-theme='dark'] .ant-ribbon-color-lime {
  background: #8bbc12;
  color: #8bbc12;
}

[data-theme='dark'] .ant-ribbon-color-green {
  background: #49aa19;
  color: #49aa19;
}

[data-theme='dark'] .ant-ribbon-color-blue {
  background: #187edc;
  color: #187edc;
}

[data-theme='dark'] .ant-ribbon-color-geekblue {
  background: #2b4bcb;
  color: #2b4bcb;
}

[data-theme='dark'] .ant-ribbon-color-purple {
  background: #642ab5;
  color: #642ab5;
}

[data-theme='dark'] .ant-ribbon.ant-ribbon-placement-end .ant-ribbon-corner {
  border-color: currentcolor transparent transparent;
}

[data-theme='dark'] .ant-ribbon.ant-ribbon-placement-start .ant-ribbon-corner {
  border-color: currentcolor currentcolor transparent transparent;
}

[data-theme='dark'] .ant-ribbon-rtl.ant-ribbon-placement-end .ant-ribbon-corner {
  border-color: currentcolor currentcolor transparent transparent;
}

[data-theme='dark'] .ant-ribbon-rtl.ant-ribbon-placement-end .ant-ribbon-corner::after {
  border-color: currentcolor currentcolor transparent transparent;
}

[data-theme='dark'] .ant-ribbon-rtl.ant-ribbon-placement-start .ant-ribbon-corner {
  border-color: currentcolor transparent transparent;
}

[data-theme='dark'] .ant-ribbon-rtl.ant-ribbon-placement-start .ant-ribbon-corner::after {
  border-color: currentcolor transparent transparent;
}

[data-theme='dark'] .ant-tabs-bottom > .ant-tabs-nav::before,
[data-theme='dark'] .ant-tabs-bottom > div > .ant-tabs-nav::before,
[data-theme='dark'] .ant-tabs-top > .ant-tabs-nav::before,
[data-theme='dark'] .ant-tabs-top > div > .ant-tabs-nav::before {
  border-bottom: 1px solid #303030;
}

[data-theme='dark'] .ant-tabs-left > .ant-tabs-content-holder,
[data-theme='dark'] .ant-tabs-left > div > .ant-tabs-content-holder {
  border-left: 1px solid #303030;
}

[data-theme='dark'] .ant-tabs-right > .ant-tabs-content-holder,
[data-theme='dark'] .ant-tabs-right > div > .ant-tabs-content-holder {
  border-right: 1px solid #303030;
}

[data-theme='dark'] .ant-tabs-dropdown {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-tabs-dropdown-menu {
  background-color: #1f1f1f;
}

[data-theme='dark'] .ant-tabs-dropdown-menu-item {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-tabs-dropdown-menu-item-remove {
  background: 0 0;
  color: #8b949e;
}

[data-theme='dark'] .ant-tabs-dropdown-menu-item-remove:hover {
  color: #2a7dc9;
}

[data-theme='dark'] .ant-tabs-dropdown-menu-item:hover {
  background: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-tabs-dropdown-menu-item-disabled,
[data-theme='dark'] .ant-tabs-dropdown-menu-item-disabled:hover {
  background: 0 0;
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-tabs-card > .ant-tabs-nav .ant-tabs-tab,
[data-theme='dark'] .ant-tabs-card > div > .ant-tabs-nav .ant-tabs-tab {
  border: 1px solid #303030;
  background: rgb(255 255 255 / 4%);
}

[data-theme='dark'] .ant-tabs-card > .ant-tabs-nav .ant-tabs-tab-active,
[data-theme='dark'] .ant-tabs-card > div > .ant-tabs-nav .ant-tabs-tab-active {
  background: #151515;
  color: #0960bd;
}

[data-theme='dark'] .ant-tabs-card.ant-tabs-top > .ant-tabs-nav .ant-tabs-tab-active,
[data-theme='dark'] .ant-tabs-card.ant-tabs-top > div > .ant-tabs-nav .ant-tabs-tab-active {
  border-bottom-color: #151515;
}

[data-theme='dark'] .ant-tabs-card.ant-tabs-bottom > .ant-tabs-nav .ant-tabs-tab-active,
[data-theme='dark'] .ant-tabs-card.ant-tabs-bottom > div > .ant-tabs-nav .ant-tabs-tab-active {
  border-top-color: #151515;
}

[data-theme='dark'] .ant-tabs-card.ant-tabs-left > .ant-tabs-nav .ant-tabs-tab-active,
[data-theme='dark'] .ant-tabs-card.ant-tabs-left > div > .ant-tabs-nav .ant-tabs-tab-active {
  border-right-color: #151515;
}

[data-theme='dark'] .ant-tabs-card.ant-tabs-right > .ant-tabs-nav .ant-tabs-tab-active,
[data-theme='dark'] .ant-tabs-card.ant-tabs-right > div > .ant-tabs-nav .ant-tabs-tab-active {
  border-left-color: #151515;
}

[data-theme='dark'] .ant-tabs {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-tabs > .ant-tabs-nav .ant-tabs-nav-more,
[data-theme='dark'] .ant-tabs > div > .ant-tabs-nav .ant-tabs-nav-more {
  background: 0 0;
}

[data-theme='dark'] .ant-tabs > .ant-tabs-nav .ant-tabs-nav-add,
[data-theme='dark'] .ant-tabs > div > .ant-tabs-nav .ant-tabs-nav-add {
  border: 1px solid #303030;
  background: rgb(255 255 255 / 4%);
}

[data-theme='dark'] .ant-tabs > .ant-tabs-nav .ant-tabs-nav-add:hover,
[data-theme='dark'] .ant-tabs > div > .ant-tabs-nav .ant-tabs-nav-add:hover {
  color: #2a7dc9;
}

[data-theme='dark'] .ant-tabs > .ant-tabs-nav .ant-tabs-nav-add:active,
[data-theme='dark'] .ant-tabs > .ant-tabs-nav .ant-tabs-nav-add:focus,
[data-theme='dark'] .ant-tabs > div > .ant-tabs-nav .ant-tabs-nav-add:active,
[data-theme='dark'] .ant-tabs > div > .ant-tabs-nav .ant-tabs-nav-add:focus {
  color: #004496;
}

[data-theme='dark'] .ant-tabs-ink-bar {
  background: #0960bd;
}

[data-theme='dark'] .ant-tabs-tab {
  background: 0 0;
}

[data-theme='dark'] .ant-tabs-tab-btn:active,
[data-theme='dark'] .ant-tabs-tab-btn:focus,
[data-theme='dark'] .ant-tabs-tab-remove:active,
[data-theme='dark'] .ant-tabs-tab-remove:focus {
  color: #004496;
}

[data-theme='dark'] .ant-tabs-tab-remove {
  background: 0 0;
  color: #8b949e;
}

[data-theme='dark'] .ant-tabs-tab-remove:hover {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .ant-tabs-tab:hover {
  color: #2a7dc9;
}

[data-theme='dark'] .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #0960bd;
}

[data-theme='dark'] .ant-tabs-tab.ant-tabs-tab-disabled {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-tabs-tab.ant-tabs-tab-disabled .ant-tabs-tab-btn:active,
[data-theme='dark'] .ant-tabs-tab.ant-tabs-tab-disabled .ant-tabs-tab-btn:focus,
[data-theme='dark'] .ant-tabs-tab.ant-tabs-tab-disabled .ant-tabs-tab-remove:active,
[data-theme='dark'] .ant-tabs-tab.ant-tabs-tab-disabled .ant-tabs-tab-remove:focus {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-input-affix-wrapper {
  border: 1px solid #303030;
  background-color: transparent;
  color: #c9d1d9;
}

[data-theme='dark'] .ant-input-affix-wrapper::placeholder {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-input-affix-wrapper:hover {
  border-color: #2a7dc9;
}

[data-theme='dark'] .ant-input-affix-wrapper-focused,
[data-theme='dark'] .ant-input-affix-wrapper:focus {
  border-color: #0960bd;
  box-shadow: 0 0 0 2px rgb(9 96 189 / 20%);
}

[data-theme='dark'] .ant-input-affix-wrapper-disabled {
  border-color: #303030;
  background-color: rgb(255 255 255 / 8%);
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-input-affix-wrapper-disabled:hover {
  border-color: #303030;
}

[data-theme='dark'] .ant-input-affix-wrapper[disabled] {
  border-color: #303030;
  background-color: rgb(255 255 255 / 8%);
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-input-affix-wrapper[disabled]:hover {
  border-color: #303030;
}

[data-theme='dark'] .ant-input-affix-wrapper-borderless,
[data-theme='dark'] .ant-input-affix-wrapper-borderless-disabled,
[data-theme='dark'] .ant-input-affix-wrapper-borderless-focused,
[data-theme='dark'] .ant-input-affix-wrapper-borderless:focus,
[data-theme='dark'] .ant-input-affix-wrapper-borderless:hover,
[data-theme='dark'] .ant-input-affix-wrapper-borderless[disabled] {
  background-color: transparent;
}

[data-theme='dark'] .ant-input-affix-wrapper:not(.ant-input-affix-wrapper-disabled):hover {
  border-color: #2a7dc9;
}

[data-theme='dark'] .ant-input-affix-wrapper-disabled .ant-input[disabled] {
  background: 0 0;
}

[data-theme='dark'] .ant-input-show-count-suffix {
  color: #8b949e;
}

[data-theme='dark'] .anticon.ant-input-clear-icon {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .anticon.ant-input-clear-icon:hover {
  color: #8b949e;
}

[data-theme='dark'] .anticon.ant-input-clear-icon:active {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-input {
  border: 1px solid #303030;
  background-color: transparent;
  color: #c9d1d9;
}

[data-theme='dark'] .ant-input::placeholder {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-input:hover {
  border-color: #2a7dc9;
}

[data-theme='dark'] .ant-input-focused,
[data-theme='dark'] .ant-input:focus {
  border-color: #0960bd;
  box-shadow: 0 0 0 2px rgb(9 96 189 / 20%);
}

[data-theme='dark'] .ant-input-disabled {
  border-color: #303030;
  background-color: rgb(255 255 255 / 8%);
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-input-disabled:hover {
  border-color: #303030;
}

[data-theme='dark'] .ant-input[disabled] {
  border-color: #303030;
  background-color: rgb(255 255 255 / 8%);
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-input[disabled]:hover {
  border-color: #303030;
}

[data-theme='dark'] .ant-input-borderless,
[data-theme='dark'] .ant-input-borderless-disabled,
[data-theme='dark'] .ant-input-borderless-focused,
[data-theme='dark'] .ant-input-borderless:focus,
[data-theme='dark'] .ant-input-borderless:hover,
[data-theme='dark'] .ant-input-borderless[disabled] {
  background-color: transparent;
}

[data-theme='dark'] .ant-input-group {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-input-group-addon {
  border: 1px solid #303030;
  background-color: rgb(255 255 255 / 4%);
  color: #c9d1d9;
}

[data-theme='dark']
  .ant-input-group-addon
  .ant-select.ant-select-single:not(.ant-select-customize-input)
  .ant-select-selector {
  border: 1px solid transparent;
}

[data-theme='dark'] .ant-input-group-addon .ant-select-focused .ant-select-selector,
[data-theme='dark'] .ant-input-group-addon .ant-select-open .ant-select-selector {
  color: #0960bd;
}

[data-theme='dark'] .ant-input-group-addon .ant-cascader-picker {
  background-color: transparent;
}

[data-theme='dark'] .ant-input-group-rtl .ant-input-group-addon:first-child {
  border-right: 1px solid #303030;
}

[data-theme='dark'] .ant-input-group-rtl .ant-input-group-addon:last-child {
  border-left: 1px solid #303030;
}

[data-theme='dark'] .ant-input-password-icon {
  color: #8b949e;
}

[data-theme='dark'] .ant-input-password-icon:hover {
  color: rgb(255 255 255 / 85%);
}

[data-theme='dark'] .ant-input-textarea-show-count::after {
  color: #8b949e;
}

[data-theme='dark'] .ant-input-search .ant-input:focus,
[data-theme='dark'] .ant-input-search .ant-input:hover {
  border-color: #2a7dc9;
}

[data-theme='dark']
  .ant-input-search
  .ant-input:focus
  + .ant-input-group-addon
  .ant-input-search-button:not(.ant-btn-primary),
[data-theme='dark']
  .ant-input-search
  .ant-input:hover
  + .ant-input-group-addon
  .ant-input-search-button:not(.ant-btn-primary) {
  border-left-color: #2a7dc9;
}

[data-theme='dark']
  .ant-input-search
  > .ant-input-group
  > .ant-input-group-addon:last-child
  .ant-input-search-button:not(.ant-btn-primary) {
  color: #8b949e;
}

[data-theme='dark']
  .ant-input-search-rtl
  .ant-input:focus
  + .ant-input-group-addon
  .ant-input-search-button:not(.ant-btn-primary),
[data-theme='dark']
  .ant-input-search-rtl
  .ant-input:hover
  + .ant-input-group-addon
  .ant-input-search-button:not(.ant-btn-primary) {
  border-right-color: #2a7dc9;
  border-left-color: #303030;
}

[data-theme='dark'] .ant-input-search-rtl > .ant-input-group > .ant-input-affix-wrapper-focused,
[data-theme='dark'] .ant-input-search-rtl > .ant-input-group > .ant-input-affix-wrapper:hover {
  border-right-color: #2a7dc9;
}

[data-theme='dark'] .ant-tooltip {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-tooltip-inner {
  background-color: #434343;
  color: #fff;
}

[data-theme='dark'] .ant-tooltip-arrow {
  background: 0 0;
}

[data-theme='dark'] .ant-tooltip-arrow-content {
  background-color: #434343;
}

[data-theme='dark'] .ant-tooltip-placement-top .ant-tooltip-arrow-content,
[data-theme='dark'] .ant-tooltip-placement-topLeft .ant-tooltip-arrow-content,
[data-theme='dark'] .ant-tooltip-placement-topRight .ant-tooltip-arrow-content {
  box-shadow: 3px 3px 7px rgb(0 0 0 / 7%);
}

[data-theme='dark'] .ant-tooltip-pink .ant-tooltip-inner {
  background-color: #cb2b83;
}

[data-theme='dark'] .ant-tooltip-pink .ant-tooltip-arrow-content {
  background-color: #cb2b83;
}

[data-theme='dark'] .ant-tooltip-magenta .ant-tooltip-inner {
  background-color: #cb2b83;
}

[data-theme='dark'] .ant-tooltip-magenta .ant-tooltip-arrow-content {
  background-color: #cb2b83;
}

[data-theme='dark'] .ant-tooltip-red .ant-tooltip-inner {
  background-color: #d32029;
}

[data-theme='dark'] .ant-tooltip-red .ant-tooltip-arrow-content {
  background-color: #d32029;
}

[data-theme='dark'] .ant-tooltip-volcano .ant-tooltip-inner {
  background-color: #d84b1b;
}

[data-theme='dark'] .ant-tooltip-volcano .ant-tooltip-arrow-content {
  background-color: #d84b1b;
}

[data-theme='dark'] .ant-tooltip-orange .ant-tooltip-inner {
  background-color: #d87a16;
}

[data-theme='dark'] .ant-tooltip-orange .ant-tooltip-arrow-content {
  background-color: #d87a16;
}

[data-theme='dark'] .ant-tooltip-yellow .ant-tooltip-inner {
  background-color: #d8bd14;
}

[data-theme='dark'] .ant-tooltip-yellow .ant-tooltip-arrow-content {
  background-color: #d8bd14;
}

[data-theme='dark'] .ant-tooltip-gold .ant-tooltip-inner {
  background-color: #d89614;
}

[data-theme='dark'] .ant-tooltip-gold .ant-tooltip-arrow-content {
  background-color: #d89614;
}

[data-theme='dark'] .ant-tooltip-cyan .ant-tooltip-inner {
  background-color: #13a8a8;
}

[data-theme='dark'] .ant-tooltip-cyan .ant-tooltip-arrow-content {
  background-color: #13a8a8;
}

[data-theme='dark'] .ant-tooltip-lime .ant-tooltip-inner {
  background-color: #8bbc12;
}

[data-theme='dark'] .ant-tooltip-lime .ant-tooltip-arrow-content {
  background-color: #8bbc12;
}

[data-theme='dark'] .ant-tooltip-green .ant-tooltip-inner {
  background-color: #49aa19;
}

[data-theme='dark'] .ant-tooltip-green .ant-tooltip-arrow-content {
  background-color: #49aa19;
}

[data-theme='dark'] .ant-tooltip-blue .ant-tooltip-inner {
  background-color: #187edc;
}

[data-theme='dark'] .ant-tooltip-blue .ant-tooltip-arrow-content {
  background-color: #187edc;
}

[data-theme='dark'] .ant-tooltip-geekblue .ant-tooltip-inner {
  background-color: #2b4bcb;
}

[data-theme='dark'] .ant-tooltip-geekblue .ant-tooltip-arrow-content {
  background-color: #2b4bcb;
}

[data-theme='dark'] .ant-tooltip-purple .ant-tooltip-inner {
  background-color: #642ab5;
}

[data-theme='dark'] .ant-tooltip-purple .ant-tooltip-arrow-content {
  background-color: #642ab5;
}

[data-theme='dark'] .ant-popover {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-popover::after {
  background: rgb(255 255 255 / 1%);
}

[data-theme='dark'] .ant-popover-inner {
  background-color: #1f1f1f;
  box-shadow: 0 0 8px rgb(0 0 0 / 45%);
}

[data-theme='dark'] .ant-popover-title {
  border-bottom: 1px solid #303030;
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .ant-popover-inner-content {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-popover-message {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-popover-message > .anticon {
  color: #efbd47;
}

[data-theme='dark'] .ant-popover-arrow {
  background: 0 0;
}

[data-theme='dark'] .ant-popover-arrow-content {
  background-color: #1f1f1f;
}

[data-theme='dark'] .ant-popover-placement-top .ant-popover-arrow-content,
[data-theme='dark'] .ant-popover-placement-topLeft .ant-popover-arrow-content,
[data-theme='dark'] .ant-popover-placement-topRight .ant-popover-arrow-content {
  box-shadow: 3px 3px 7px rgb(0 0 0 / 7%);
}

[data-theme='dark'] .ant-popover-pink .ant-popover-inner {
  background-color: #cb2b83;
}

[data-theme='dark'] .ant-popover-pink .ant-popover-arrow-content {
  background-color: #cb2b83;
}

[data-theme='dark'] .ant-popover-magenta .ant-popover-inner {
  background-color: #cb2b83;
}

[data-theme='dark'] .ant-popover-magenta .ant-popover-arrow-content {
  background-color: #cb2b83;
}

[data-theme='dark'] .ant-popover-red .ant-popover-inner {
  background-color: #d32029;
}

[data-theme='dark'] .ant-popover-red .ant-popover-arrow-content {
  background-color: #d32029;
}

[data-theme='dark'] .ant-popover-volcano .ant-popover-inner {
  background-color: #d84b1b;
}

[data-theme='dark'] .ant-popover-volcano .ant-popover-arrow-content {
  background-color: #d84b1b;
}

[data-theme='dark'] .ant-popover-orange .ant-popover-inner {
  background-color: #d87a16;
}

[data-theme='dark'] .ant-popover-orange .ant-popover-arrow-content {
  background-color: #d87a16;
}

[data-theme='dark'] .ant-popover-yellow .ant-popover-inner {
  background-color: #d8bd14;
}

[data-theme='dark'] .ant-popover-yellow .ant-popover-arrow-content {
  background-color: #d8bd14;
}

[data-theme='dark'] .ant-popover-gold .ant-popover-inner {
  background-color: #d89614;
}

[data-theme='dark'] .ant-popover-gold .ant-popover-arrow-content {
  background-color: #d89614;
}

[data-theme='dark'] .ant-popover-cyan .ant-popover-inner {
  background-color: #13a8a8;
}

[data-theme='dark'] .ant-popover-cyan .ant-popover-arrow-content {
  background-color: #13a8a8;
}

[data-theme='dark'] .ant-popover-lime .ant-popover-inner {
  background-color: #8bbc12;
}

[data-theme='dark'] .ant-popover-lime .ant-popover-arrow-content {
  background-color: #8bbc12;
}

[data-theme='dark'] .ant-popover-green .ant-popover-inner {
  background-color: #49aa19;
}

[data-theme='dark'] .ant-popover-green .ant-popover-arrow-content {
  background-color: #49aa19;
}

[data-theme='dark'] .ant-popover-blue .ant-popover-inner {
  background-color: #187edc;
}

[data-theme='dark'] .ant-popover-blue .ant-popover-arrow-content {
  background-color: #187edc;
}

[data-theme='dark'] .ant-popover-geekblue .ant-popover-inner {
  background-color: #2b4bcb;
}

[data-theme='dark'] .ant-popover-geekblue .ant-popover-arrow-content {
  background-color: #2b4bcb;
}

[data-theme='dark'] .ant-popover-purple .ant-popover-inner {
  background-color: #642ab5;
}

[data-theme='dark'] .ant-popover-purple .ant-popover-arrow-content {
  background-color: #642ab5;
}

[data-theme='dark'] .ant-menu-item-danger.ant-menu-item {
  color: #ed6f6f;
}

[data-theme='dark'] .ant-menu-item-danger.ant-menu-item-active,
[data-theme='dark'] .ant-menu-item-danger.ant-menu-item:hover {
  color: #ed6f6f;
}

[data-theme='dark'] .ant-menu-item-danger.ant-menu-item:active {
  background: #2b1316;
}

[data-theme='dark'] .ant-menu-item-danger.ant-menu-item-selected {
  color: #ed6f6f;
}

[data-theme='dark'] .ant-menu-item-danger.ant-menu-item-selected > a,
[data-theme='dark'] .ant-menu-item-danger.ant-menu-item-selected > a:hover {
  color: #ed6f6f;
}

[data-theme='dark']
  .ant-menu:not(.ant-menu-horizontal)
  .ant-menu-item-danger.ant-menu-item-selected {
  background-color: #2b1316;
}

[data-theme='dark'] .ant-menu-inline .ant-menu-item-danger.ant-menu-item::after {
  border-right-color: #ed6f6f;
}

[data-theme='dark'] .ant-menu-dark .ant-menu-item-danger.ant-menu-item,
[data-theme='dark'] .ant-menu-dark .ant-menu-item-danger.ant-menu-item:hover,
[data-theme='dark'] .ant-menu-dark .ant-menu-item-danger.ant-menu-item > a {
  color: #ed6f6f;
}

[data-theme='dark']
  .ant-menu-dark.ant-menu-dark:not(.ant-menu-horizontal)
  .ant-menu-item-danger.ant-menu-item-selected {
  background-color: #ed6f6f;
  color: #fff;
}

[data-theme='dark'] .ant-menu {
  background: #151515;
  color: #c9d1d9;
}

[data-theme='dark'] .ant-menu-item-group-title {
  color: #8b949e;
}

[data-theme='dark'] .ant-menu-submenu-selected {
  color: #0960bd;
}

[data-theme='dark'] .ant-menu-item:active,
[data-theme='dark'] .ant-menu-submenu-title:active {
  background: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-menu-item a {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-menu-item a:hover {
  color: #0960bd;
}

[data-theme='dark'] .ant-menu-item a::before {
  background-color: transparent;
}

[data-theme='dark'] .ant-menu-item > .ant-badge a {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-menu-item > .ant-badge a:hover {
  color: #0960bd;
}

[data-theme='dark'] .ant-menu-item-divider {
  border-color: #303030;
}

[data-theme='dark'] .ant-menu-horizontal > .ant-menu-item-active,
[data-theme='dark'] .ant-menu-horizontal > .ant-menu-item:hover,
[data-theme='dark'] .ant-menu-horizontal > .ant-menu-submenu .ant-menu-submenu-title:hover {
  background-color: transparent;
}

[data-theme='dark'] .ant-menu-item-selected {
  color: #0960bd;
}

[data-theme='dark'] .ant-menu-item-selected a,
[data-theme='dark'] .ant-menu-item-selected a:hover {
  color: #0960bd;
}

[data-theme='dark'] .ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
  background-color: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-menu-inline,
[data-theme='dark'] .ant-menu-vertical,
[data-theme='dark'] .ant-menu-vertical-left {
  border-right: 1px solid #303030;
}

[data-theme='dark'] .ant-menu-vertical-right {
  border-left: 1px solid #303030;
}

[data-theme='dark'] .ant-menu-item:focus-visible,
[data-theme='dark'] .ant-menu-submenu-title:focus-visible {
  box-shadow: 0 0 0 2px #a5d3f0;
}

[data-theme='dark'] .ant-menu-submenu-popup {
  background: 0 0;
}

[data-theme='dark'] .ant-menu-submenu > .ant-menu {
  background-color: #151515;
}

[data-theme='dark'] .ant-menu-submenu-popup > .ant-menu {
  background-color: #1f1f1f;
}

[data-theme='dark'] .ant-menu-submenu-arrow,
[data-theme='dark'] .ant-menu-submenu-expand-icon {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-menu-submenu:hover > .ant-menu-submenu-title > .ant-menu-submenu-arrow,
[data-theme='dark']
  .ant-menu-submenu:hover
  > .ant-menu-submenu-title
  > .ant-menu-submenu-expand-icon {
  color: #0960bd;
}

[data-theme='dark'] .ant-menu-vertical .ant-menu-submenu-selected,
[data-theme='dark'] .ant-menu-vertical-left .ant-menu-submenu-selected,
[data-theme='dark'] .ant-menu-vertical-right .ant-menu-submenu-selected {
  color: #0960bd;
}

[data-theme='dark'] .ant-menu-horizontal {
  border-bottom: 1px solid #303030;
}

[data-theme='dark'] .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-item-active,
[data-theme='dark'] .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-item-open,
[data-theme='dark'] .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-item-selected,
[data-theme='dark'] .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-item:hover,
[data-theme='dark'] .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-submenu-active,
[data-theme='dark'] .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-submenu-open,
[data-theme='dark'] .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-submenu-selected,
[data-theme='dark'] .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-submenu:hover {
  color: #0960bd;
}

[data-theme='dark'] .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-item-active::after,
[data-theme='dark'] .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-item-open::after,
[data-theme='dark'] .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-item-selected::after,
[data-theme='dark'] .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-item:hover::after,
[data-theme='dark'] .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-submenu-active::after,
[data-theme='dark'] .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-submenu-open::after,
[data-theme='dark'] .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-submenu-selected::after,
[data-theme='dark'] .ant-menu-horizontal:not(.ant-menu-dark) > .ant-menu-submenu:hover::after {
  border-bottom: 2px solid #0960bd;
}

[data-theme='dark'] .ant-menu-horizontal > .ant-menu-item::after,
[data-theme='dark'] .ant-menu-horizontal > .ant-menu-submenu::after {
  border-bottom: 2px solid transparent;
}

[data-theme='dark'] .ant-menu-horizontal > .ant-menu-item a {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-menu-horizontal > .ant-menu-item a:hover {
  color: #0960bd;
}

[data-theme='dark'] .ant-menu-horizontal > .ant-menu-item-selected a {
  color: #0960bd;
}

[data-theme='dark'] .ant-menu-inline .ant-menu-item::after,
[data-theme='dark'] .ant-menu-vertical .ant-menu-item::after,
[data-theme='dark'] .ant-menu-vertical-left .ant-menu-item::after,
[data-theme='dark'] .ant-menu-vertical-right .ant-menu-item::after {
  border-right: 3px solid #0960bd;
}

[data-theme='dark'] .ant-menu.ant-menu-inline-collapsed-tooltip a {
  color: rgb(255 255 255 / 85%);
}

[data-theme='dark'] .ant-menu-sub.ant-menu-inline {
  background: rgb(255 255 255 / 4%);
}

[data-theme='dark'] .ant-menu-item-disabled,
[data-theme='dark'] .ant-menu-submenu-disabled {
  color: rgb(255 255 255 / 30%) !important;
}

[data-theme='dark'] .ant-menu-item-disabled::after,
[data-theme='dark'] .ant-menu-submenu-disabled::after {
  border-color: transparent !important;
}

[data-theme='dark'] .ant-menu-item-disabled a,
[data-theme='dark'] .ant-menu-submenu-disabled a {
  color: rgb(255 255 255 / 30%) !important;
}

[data-theme='dark'] .ant-menu-item-disabled > .ant-menu-submenu-title,
[data-theme='dark'] .ant-menu-submenu-disabled > .ant-menu-submenu-title {
  color: rgb(255 255 255 / 30%) !important;
}

[data-theme='dark']
  .ant-menu-item-disabled
  > .ant-menu-submenu-title
  > .ant-menu-submenu-arrow::after,
[data-theme='dark']
  .ant-menu-item-disabled
  > .ant-menu-submenu-title
  > .ant-menu-submenu-arrow::before,
[data-theme='dark']
  .ant-menu-submenu-disabled
  > .ant-menu-submenu-title
  > .ant-menu-submenu-arrow::after,
[data-theme='dark']
  .ant-menu-submenu-disabled
  > .ant-menu-submenu-title
  > .ant-menu-submenu-arrow::before {
  background: rgb(255 255 255 / 30%) !important;
}

[data-theme='dark'] .ant-menu-inline-collapsed-tooltip a,
[data-theme='dark'] .ant-menu-inline-collapsed-tooltip a:hover {
  color: #fff;
}

[data-theme='dark'] .ant-menu-light .ant-menu-item-active,
[data-theme='dark'] .ant-menu-light .ant-menu-item:hover,
[data-theme='dark'] .ant-menu-light .ant-menu-submenu-active,
[data-theme='dark'] .ant-menu-light .ant-menu-submenu-title:hover,
[data-theme='dark'] .ant-menu-light .ant-menu:not(.ant-menu-inline) .ant-menu-submenu-open {
  color: #0960bd;
}

[data-theme='dark'] .ant-menu.ant-menu-root:focus-visible {
  box-shadow: 0 0 0 2px #004496;
}

[data-theme='dark'] .ant-menu-dark .ant-menu-item:focus-visible,
[data-theme='dark'] .ant-menu-dark .ant-menu-submenu-title:focus-visible {
  box-shadow: 0 0 0 2px #004496;
}

[data-theme='dark'] .ant-menu-dark .ant-menu-sub,
[data-theme='dark'] .ant-menu.ant-menu-dark,
[data-theme='dark'] .ant-menu.ant-menu-dark .ant-menu-sub {
  background: #1f1f1f;
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark']
  .ant-menu-dark
  .ant-menu-sub
  .ant-menu-submenu-title
  .ant-menu-submenu-arrow::after,
[data-theme='dark']
  .ant-menu-dark
  .ant-menu-sub
  .ant-menu-submenu-title
  .ant-menu-submenu-arrow::before,
[data-theme='dark']
  .ant-menu.ant-menu-dark
  .ant-menu-sub
  .ant-menu-submenu-title
  .ant-menu-submenu-arrow::after,
[data-theme='dark']
  .ant-menu.ant-menu-dark
  .ant-menu-sub
  .ant-menu-submenu-title
  .ant-menu-submenu-arrow::before,
[data-theme='dark'] .ant-menu.ant-menu-dark .ant-menu-submenu-title .ant-menu-submenu-arrow::after,
[data-theme='dark']
  .ant-menu.ant-menu-dark
  .ant-menu-submenu-title
  .ant-menu-submenu-arrow::before {
  background: #fff;
}

[data-theme='dark'] .ant-menu-dark.ant-menu-submenu-popup {
  background: 0 0;
}

[data-theme='dark'] .ant-menu-dark .ant-menu-inline.ant-menu-sub {
  background: #151515;
}

[data-theme='dark'] .ant-menu-dark.ant-menu-horizontal > .ant-menu-item,
[data-theme='dark'] .ant-menu-dark.ant-menu-horizontal > .ant-menu-submenu {
  border-color: #1f1f1f;
}

[data-theme='dark'] .ant-menu-dark.ant-menu-horizontal > .ant-menu-item:hover {
  background-color: #0960bd;
}

[data-theme='dark'] .ant-menu-dark .ant-menu-item,
[data-theme='dark'] .ant-menu-dark .ant-menu-item-group-title,
[data-theme='dark'] .ant-menu-dark .ant-menu-item > a,
[data-theme='dark'] .ant-menu-dark .ant-menu-item > span > a {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .ant-menu-dark .ant-menu-item-active,
[data-theme='dark'] .ant-menu-dark .ant-menu-item:hover,
[data-theme='dark'] .ant-menu-dark .ant-menu-submenu-active,
[data-theme='dark'] .ant-menu-dark .ant-menu-submenu-open,
[data-theme='dark'] .ant-menu-dark .ant-menu-submenu-selected,
[data-theme='dark'] .ant-menu-dark .ant-menu-submenu-title:hover {
  background-color: transparent;
  color: #fff;
}

[data-theme='dark'] .ant-menu-dark .ant-menu-item-active > a,
[data-theme='dark'] .ant-menu-dark .ant-menu-item-active > span > a,
[data-theme='dark'] .ant-menu-dark .ant-menu-item:hover > a,
[data-theme='dark'] .ant-menu-dark .ant-menu-item:hover > span > a,
[data-theme='dark'] .ant-menu-dark .ant-menu-submenu-active > a,
[data-theme='dark'] .ant-menu-dark .ant-menu-submenu-active > span > a,
[data-theme='dark'] .ant-menu-dark .ant-menu-submenu-open > a,
[data-theme='dark'] .ant-menu-dark .ant-menu-submenu-open > span > a,
[data-theme='dark'] .ant-menu-dark .ant-menu-submenu-selected > a,
[data-theme='dark'] .ant-menu-dark .ant-menu-submenu-selected > span > a,
[data-theme='dark'] .ant-menu-dark .ant-menu-submenu-title:hover > a,
[data-theme='dark'] .ant-menu-dark .ant-menu-submenu-title:hover > span > a {
  color: #fff;
}

[data-theme='dark']
  .ant-menu-dark
  .ant-menu-item-active
  > .ant-menu-submenu-title
  > .ant-menu-submenu-arrow::after,
[data-theme='dark']
  .ant-menu-dark
  .ant-menu-item-active
  > .ant-menu-submenu-title
  > .ant-menu-submenu-arrow::before,
[data-theme='dark']
  .ant-menu-dark
  .ant-menu-item:hover
  > .ant-menu-submenu-title
  > .ant-menu-submenu-arrow::after,
[data-theme='dark']
  .ant-menu-dark
  .ant-menu-item:hover
  > .ant-menu-submenu-title
  > .ant-menu-submenu-arrow::before,
[data-theme='dark']
  .ant-menu-dark
  .ant-menu-submenu-active
  > .ant-menu-submenu-title
  > .ant-menu-submenu-arrow::after,
[data-theme='dark']
  .ant-menu-dark
  .ant-menu-submenu-active
  > .ant-menu-submenu-title
  > .ant-menu-submenu-arrow::before,
[data-theme='dark']
  .ant-menu-dark
  .ant-menu-submenu-open
  > .ant-menu-submenu-title
  > .ant-menu-submenu-arrow::after,
[data-theme='dark']
  .ant-menu-dark
  .ant-menu-submenu-open
  > .ant-menu-submenu-title
  > .ant-menu-submenu-arrow::before,
[data-theme='dark']
  .ant-menu-dark
  .ant-menu-submenu-selected
  > .ant-menu-submenu-title
  > .ant-menu-submenu-arrow::after,
[data-theme='dark']
  .ant-menu-dark
  .ant-menu-submenu-selected
  > .ant-menu-submenu-title
  > .ant-menu-submenu-arrow::before,
[data-theme='dark']
  .ant-menu-dark
  .ant-menu-submenu-title:hover
  > .ant-menu-submenu-title
  > .ant-menu-submenu-arrow::after,
[data-theme='dark']
  .ant-menu-dark
  .ant-menu-submenu-title:hover
  > .ant-menu-submenu-title
  > .ant-menu-submenu-arrow::before {
  background: #fff;
}

[data-theme='dark'] .ant-menu-dark .ant-menu-item:hover {
  background-color: transparent;
}

[data-theme='dark'] .ant-menu-dark.ant-menu-dark:not(.ant-menu-horizontal) .ant-menu-item-selected {
  background-color: #0960bd;
}

[data-theme='dark'] .ant-menu-dark .ant-menu-item-selected {
  color: #fff;
}

[data-theme='dark'] .ant-menu-dark .ant-menu-item-selected > a,
[data-theme='dark'] .ant-menu-dark .ant-menu-item-selected > a:hover,
[data-theme='dark'] .ant-menu-dark .ant-menu-item-selected > span > a,
[data-theme='dark'] .ant-menu-dark .ant-menu-item-selected > span > a:hover {
  color: #fff;
}

[data-theme='dark'] .ant-menu-dark .ant-menu-item-selected .ant-menu-item-icon,
[data-theme='dark'] .ant-menu-dark .ant-menu-item-selected .anticon {
  color: #fff;
}

[data-theme='dark'] .ant-menu-dark .ant-menu-item-selected .ant-menu-item-icon + span,
[data-theme='dark'] .ant-menu-dark .ant-menu-item-selected .anticon + span {
  color: #fff;
}

[data-theme='dark'] .ant-menu-submenu-popup.ant-menu-dark .ant-menu-item-selected,
[data-theme='dark'] .ant-menu.ant-menu-dark .ant-menu-item-selected {
  background-color: #0960bd;
}

[data-theme='dark'] .ant-menu-dark .ant-menu-item-disabled,
[data-theme='dark'] .ant-menu-dark .ant-menu-item-disabled > a,
[data-theme='dark'] .ant-menu-dark .ant-menu-item-disabled > span > a,
[data-theme='dark'] .ant-menu-dark .ant-menu-submenu-disabled,
[data-theme='dark'] .ant-menu-dark .ant-menu-submenu-disabled > a,
[data-theme='dark'] .ant-menu-dark .ant-menu-submenu-disabled > span > a {
  color: rgb(255 255 255 / 30%) !important;
}

[data-theme='dark'] .ant-menu-dark .ant-menu-item-disabled > .ant-menu-submenu-title,
[data-theme='dark'] .ant-menu-dark .ant-menu-submenu-disabled > .ant-menu-submenu-title {
  color: rgb(255 255 255 / 30%) !important;
}

[data-theme='dark']
  .ant-menu-dark
  .ant-menu-item-disabled
  > .ant-menu-submenu-title
  > .ant-menu-submenu-arrow::after,
[data-theme='dark']
  .ant-menu-dark
  .ant-menu-item-disabled
  > .ant-menu-submenu-title
  > .ant-menu-submenu-arrow::before,
[data-theme='dark']
  .ant-menu-dark
  .ant-menu-submenu-disabled
  > .ant-menu-submenu-title
  > .ant-menu-submenu-arrow::after,
[data-theme='dark']
  .ant-menu-dark
  .ant-menu-submenu-disabled
  > .ant-menu-submenu-title
  > .ant-menu-submenu-arrow::before {
  background: rgb(255 255 255 / 30%) !important;
}

[data-theme='dark'] .ant-menu-rtl.ant-menu-inline,
[data-theme='dark'] .ant-menu-rtl.ant-menu-vertical {
  border-left: 1px solid #303030;
}

[data-theme='dark'] .ant-mentions {
  border: 1px solid #303030;
  background-color: transparent;
  color: #c9d1d9;
}

[data-theme='dark'] .ant-mentions::placeholder {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-mentions:hover {
  border-color: #2a7dc9;
}

[data-theme='dark'] .ant-mentions-focused,
[data-theme='dark'] .ant-mentions:focus {
  border-color: #0960bd;
  box-shadow: 0 0 0 2px rgb(9 96 189 / 20%);
}

[data-theme='dark'] .ant-mentions-disabled {
  border-color: #303030;
  background-color: rgb(255 255 255 / 8%);
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-mentions-disabled:hover {
  border-color: #303030;
}

[data-theme='dark'] .ant-mentions[disabled] {
  border-color: #303030;
  background-color: rgb(255 255 255 / 8%);
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-mentions[disabled]:hover {
  border-color: #303030;
}

[data-theme='dark'] .ant-mentions-borderless,
[data-theme='dark'] .ant-mentions-borderless-disabled,
[data-theme='dark'] .ant-mentions-borderless-focused,
[data-theme='dark'] .ant-mentions-borderless:focus,
[data-theme='dark'] .ant-mentions-borderless:hover,
[data-theme='dark'] .ant-mentions-borderless[disabled] {
  background-color: transparent;
}

[data-theme='dark'] .ant-mentions-disabled > textarea {
  border-color: #303030;
  background-color: rgb(255 255 255 / 8%);
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-mentions-disabled > textarea:hover {
  border-color: #303030;
}

[data-theme='dark'] .ant-mentions-focused {
  border-color: #0960bd;
  box-shadow: 0 0 0 2px rgb(9 96 189 / 20%);
}

[data-theme='dark'] .ant-mentions > textarea {
  background-color: transparent;
}

[data-theme='dark'] .ant-mentions > textarea::placeholder {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-mentions-measure {
  color: transparent;
}

[data-theme='dark'] .ant-mentions-dropdown {
  background-color: #1f1f1f;
  color: #c9d1d9;
}

[data-theme='dark'] .ant-mentions-dropdown-menu-item {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-mentions-dropdown-menu-item:hover {
  background-color: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-mentions-dropdown-menu-item-disabled {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-mentions-dropdown-menu-item-disabled:hover {
  background-color: #1f1f1f;
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-mentions-dropdown-menu-item-selected {
  background-color: rgb(255 255 255 / 4%);
  color: #c9d1d9;
}

[data-theme='dark'] .ant-mentions-dropdown-menu-item-active {
  background-color: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-dropdown-menu-item.ant-dropdown-menu-item-danger {
  color: #ed6f6f;
}

[data-theme='dark'] .ant-dropdown-menu-item.ant-dropdown-menu-item-danger:hover {
  background-color: #ed6f6f;
  color: #fff;
}

[data-theme='dark'] .ant-dropdown {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-dropdown-arrow {
  background: 0 0;
}

[data-theme='dark'] .ant-dropdown-placement-topCenter > .ant-dropdown-arrow,
[data-theme='dark'] .ant-dropdown-placement-topLeft > .ant-dropdown-arrow,
[data-theme='dark'] .ant-dropdown-placement-topRight > .ant-dropdown-arrow {
  border-color: transparent #1f1f1f;
  box-shadow: 3px 3px 7px rgb(0 0 0 / 7%);
}

[data-theme='dark'] .ant-dropdown-placement-bottomCenter > .ant-dropdown-arrow,
[data-theme='dark'] .ant-dropdown-placement-bottomLeft > .ant-dropdown-arrow,
[data-theme='dark'] .ant-dropdown-placement-bottomRight > .ant-dropdown-arrow {
  border-color: #1f1f1f;
}

[data-theme='dark'] .ant-dropdown-menu {
  background-color: #1f1f1f;
}

[data-theme='dark'] .ant-dropdown-menu-item-group-title {
  color: #8b949e;
}

[data-theme='dark'] .ant-dropdown-menu-submenu-popup {
  background: 0 0;
}

[data-theme='dark'] .ant-dropdown-menu-item,
[data-theme='dark'] .ant-dropdown-menu-submenu-title {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-dropdown-menu-item-selected,
[data-theme='dark'] .ant-dropdown-menu-submenu-title-selected {
  background-color: #111b26;
  color: #0960bd;
}

[data-theme='dark'] .ant-dropdown-menu-item:hover,
[data-theme='dark'] .ant-dropdown-menu-submenu-title:hover {
  background-color: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-dropdown-menu-item-disabled,
[data-theme='dark'] .ant-dropdown-menu-submenu-title-disabled {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-dropdown-menu-item-disabled:hover,
[data-theme='dark'] .ant-dropdown-menu-submenu-title-disabled:hover {
  background-color: transparent;
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-dropdown-menu-item-divider,
[data-theme='dark'] .ant-dropdown-menu-submenu-title-divider {
  background-color: #303030;
}

[data-theme='dark']
  .ant-dropdown-menu-item
  .ant-dropdown-menu-submenu-expand-icon
  .ant-dropdown-menu-submenu-arrow-icon,
[data-theme='dark']
  .ant-dropdown-menu-submenu-title
  .ant-dropdown-menu-submenu-expand-icon
  .ant-dropdown-menu-submenu-arrow-icon {
  color: #8b949e;
}

[data-theme='dark']
  .ant-dropdown-menu-submenu.ant-dropdown-menu-submenu-disabled
  .ant-dropdown-menu-submenu-title,
[data-theme='dark']
  .ant-dropdown-menu-submenu.ant-dropdown-menu-submenu-disabled
  .ant-dropdown-menu-submenu-title
  .ant-dropdown-menu-submenu-arrow-icon {
  background-color: transparent;
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-dropdown-menu-submenu-selected .ant-dropdown-menu-submenu-title {
  color: #0960bd;
}

[data-theme='dark'] .ant-dropdown-menu-dark,
[data-theme='dark'] .ant-dropdown-menu-dark .ant-dropdown-menu {
  background: #1f1f1f;
}

[data-theme='dark'] .ant-dropdown-menu-dark .ant-dropdown-menu-item,
[data-theme='dark'] .ant-dropdown-menu-dark .ant-dropdown-menu-item > .anticon + span > a,
[data-theme='dark'] .ant-dropdown-menu-dark .ant-dropdown-menu-item > a,
[data-theme='dark'] .ant-dropdown-menu-dark .ant-dropdown-menu-submenu-title {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark']
  .ant-dropdown-menu-dark
  .ant-dropdown-menu-item
  .ant-dropdown-menu-submenu-arrow::after,
[data-theme='dark']
  .ant-dropdown-menu-dark
  .ant-dropdown-menu-item
  > .anticon
  + span
  > a
  .ant-dropdown-menu-submenu-arrow::after,
[data-theme='dark']
  .ant-dropdown-menu-dark
  .ant-dropdown-menu-item
  > a
  .ant-dropdown-menu-submenu-arrow::after,
[data-theme='dark']
  .ant-dropdown-menu-dark
  .ant-dropdown-menu-submenu-title
  .ant-dropdown-menu-submenu-arrow::after {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .ant-dropdown-menu-dark .ant-dropdown-menu-item:hover,
[data-theme='dark'] .ant-dropdown-menu-dark .ant-dropdown-menu-item > .anticon + span > a:hover,
[data-theme='dark'] .ant-dropdown-menu-dark .ant-dropdown-menu-item > a:hover,
[data-theme='dark'] .ant-dropdown-menu-dark .ant-dropdown-menu-submenu-title:hover {
  background: 0 0;
  color: #fff;
}

[data-theme='dark'] .ant-dropdown-menu-dark .ant-dropdown-menu-item-selected,
[data-theme='dark'] .ant-dropdown-menu-dark .ant-dropdown-menu-item-selected:hover,
[data-theme='dark'] .ant-dropdown-menu-dark .ant-dropdown-menu-item-selected > a {
  background: #0960bd;
  color: #fff;
}

[data-theme='dark'] .ant-divider {
  border-top: 1px solid rgb(255 255 255 / 12%);
  color: #c9d1d9;
}

[data-theme='dark'] .ant-divider-vertical {
  border-left: 1px solid rgb(255 255 255 / 12%);
}

[data-theme='dark'] .ant-divider-horizontal.ant-divider-with-text {
  border-top-color: rgb(255 255 255 / 12%);
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .ant-divider-horizontal.ant-divider-with-text::after,
[data-theme='dark'] .ant-divider-horizontal.ant-divider-with-text::before {
  border-top: 1px solid transparent;
}

[data-theme='dark'] .ant-divider-dashed {
  border-color: rgb(255 255 255 / 12%);
}

[data-theme='dark'] .ant-divider-plain.ant-divider-with-text {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-card {
  background: #151515;
  color: #c9d1d9;
}

[data-theme='dark'] .ant-card-hoverable:hover {
  border-color: transparent;
}

[data-theme='dark'] .ant-card-bordered {
  border: 1px solid #303030;
}

[data-theme='dark'] .ant-card-head {
  border-bottom: 1px solid #303030;
  background: 0 0;
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .ant-card-head .ant-tabs-top {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-card-head .ant-tabs-top-bar {
  border-bottom: 1px solid #303030;
}

[data-theme='dark'] .ant-card-extra {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-card-grid {
  box-shadow: 1px 0 0 0 #303030;
}

[data-theme='dark'] .ant-card-actions {
  border-top: 1px solid #303030;
  background: #151515;
}

[data-theme='dark'] .ant-card-actions > li {
  color: #8b949e;
}

[data-theme='dark'] .ant-card-actions > li > span:hover {
  color: #0960bd;
}

[data-theme='dark'] .ant-card-actions > li > span a:not(.ant-btn),
[data-theme='dark'] .ant-card-actions > li > span > .anticon {
  color: #8b949e;
}

[data-theme='dark'] .ant-card-actions > li > span a:not(.ant-btn):hover,
[data-theme='dark'] .ant-card-actions > li > span > .anticon:hover {
  color: #0960bd;
}

[data-theme='dark'] .ant-card-actions > li:not(:last-child) {
  border-right: 1px solid #303030;
}

[data-theme='dark'] .ant-card-rtl .ant-card-actions > li:not(:last-child) {
  border-left: 1px solid #303030;
}

[data-theme='dark'] .ant-card-type-inner .ant-card-head {
  background: rgb(255 255 255 / 4%);
}

[data-theme='dark'] .ant-card-meta-title {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .ant-card-meta-description {
  color: #8b949e;
}

[data-theme='dark'] .ant-collapse {
  border: 1px solid #303030;
  background-color: rgb(255 255 255 / 4%);
  color: #c9d1d9;
}

[data-theme='dark'] .ant-collapse > .ant-collapse-item {
  border-bottom: 1px solid #303030;
}

[data-theme='dark'] .ant-collapse > .ant-collapse-item > .ant-collapse-header {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .ant-collapse-content {
  border-top: 1px solid #303030;
  background-color: #151515;
  color: #c9d1d9;
}

[data-theme='dark'] .ant-collapse-borderless {
  background-color: rgb(255 255 255 / 4%);
}

[data-theme='dark'] .ant-collapse-borderless > .ant-collapse-item {
  border-bottom: 1px solid #303030;
}

[data-theme='dark'] .ant-collapse-borderless > .ant-collapse-item > .ant-collapse-content {
  background-color: transparent;
}

[data-theme='dark'] .ant-collapse-ghost {
  background-color: transparent;
}

[data-theme='dark'] .ant-collapse-ghost > .ant-collapse-item > .ant-collapse-content {
  background-color: transparent;
}

[data-theme='dark'] .ant-collapse .ant-collapse-item-disabled > .ant-collapse-header,
[data-theme='dark'] .ant-collapse .ant-collapse-item-disabled > .ant-collapse-header > .arrow {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-carousel {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-carousel .slick-slider {
  -webkit-tap-highlight-color: transparent;
}

[data-theme='dark'] .ant-carousel .slick-next,
[data-theme='dark'] .ant-carousel .slick-prev {
  background: 0 0;
  color: transparent;
}

[data-theme='dark'] .ant-carousel .slick-next:focus,
[data-theme='dark'] .ant-carousel .slick-next:hover,
[data-theme='dark'] .ant-carousel .slick-prev:focus,
[data-theme='dark'] .ant-carousel .slick-prev:hover {
  background: 0 0;
  color: transparent;
}

[data-theme='dark'] .ant-carousel .slick-dots li button {
  background: #151515;
  color: transparent;
}

[data-theme='dark'] .ant-carousel .slick-dots li.slick-active button {
  background: #151515;
}

[data-theme='dark'] .ant-notification .ant-anchor-wrapper,
[data-theme='dark'] .ant-notification .ant-card,
[data-theme='dark'] .ant-notification .ant-collapse-content,
[data-theme='dark'] .ant-notification .ant-picker-clear,
[data-theme='dark'] .ant-notification .ant-slider-handle,
[data-theme='dark'] .ant-notification .ant-timeline-item-head {
  background-color: #1f1f1f;
}

[data-theme='dark'] .ant-notification .ant-transfer-list-header {
  border-bottom: 1px solid #3a3a3a;
  background: #1f1f1f;
}

[data-theme='dark']
  .ant-notification
  .ant-transfer-list-content-item:not(.ant-transfer-list-content-item-disabled):hover {
  background-color: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-notification tr.ant-table-expanded-row:hover > td,
[data-theme='dark'] .ant-notification tr.ant-table-expanded-row > td {
  background: #272727;
}

[data-theme='dark'] .ant-notification .ant-table.ant-table-small thead > tr > th {
  border-bottom: 1px solid #3a3a3a;
  background-color: #1f1f1f;
}

[data-theme='dark'] .ant-notification .ant-table {
  background-color: #1f1f1f;
}

[data-theme='dark'] .ant-notification .ant-table .ant-table-row-expand-icon {
  border: 1px solid #3a3a3a;
}

[data-theme='dark'] .ant-notification .ant-table tfoot > tr > td,
[data-theme='dark'] .ant-notification .ant-table tfoot > tr > th {
  border-bottom: 1px solid #3a3a3a;
}

[data-theme='dark'] .ant-notification .ant-table thead > tr > th {
  border-bottom: 1px solid #3a3a3a;
  background-color: #272727;
}

[data-theme='dark'] .ant-notification .ant-table tbody > tr > td {
  border-bottom: 1px solid #3a3a3a;
}

[data-theme='dark'] .ant-notification .ant-table tbody > tr > td.ant-table-cell-fix-left,
[data-theme='dark'] .ant-notification .ant-table tbody > tr > td.ant-table-cell-fix-right {
  background-color: #1f1f1f;
}

[data-theme='dark'] .ant-notification .ant-table tbody > tr.ant-table-row:hover > td {
  background: #303030;
}

[data-theme='dark'] .ant-notification .ant-table.ant-table-bordered .ant-table-title {
  border: 1px solid #3a3a3a;
}

[data-theme='dark'] .ant-notification .ant-table.ant-table-bordered tbody > tr > td,
[data-theme='dark'] .ant-notification .ant-table.ant-table-bordered tfoot > tr > td,
[data-theme='dark'] .ant-notification .ant-table.ant-table-bordered tfoot > tr > th,
[data-theme='dark'] .ant-notification .ant-table.ant-table-bordered thead > tr > th {
  border-right: 1px solid #3a3a3a;
}

[data-theme='dark']
  .ant-notification
  .ant-table.ant-table-bordered
  .ant-table-cell-fix-right-first::after {
  border-right: 1px solid #3a3a3a;
}

[data-theme='dark']
  .ant-notification
  .ant-table.ant-table-bordered
  table
  thead
  > tr:not(:last-child)
  > th {
  border-bottom: 1px solid #303030;
}

[data-theme='dark'] .ant-notification .ant-table.ant-table-bordered .ant-table-container {
  border: 1px solid #3a3a3a;
}

[data-theme='dark']
  .ant-notification
  .ant-table.ant-table-bordered
  .ant-table-expanded-row-fixed::after {
  border-right: 1px solid #3a3a3a;
}

[data-theme='dark'] .ant-notification .ant-table.ant-table-bordered .ant-table-footer {
  border: 1px solid #3a3a3a;
}

[data-theme='dark'] .ant-notification .ant-table .ant-table-filter-trigger-container-open {
  background-color: #525252;
}

[data-theme='dark'] .ant-notification .ant-picker-calendar-full {
  background-color: #1f1f1f;
}

[data-theme='dark'] .ant-notification .ant-picker-calendar-full .ant-picker-panel {
  background-color: #1f1f1f;
}

[data-theme='dark']
  .ant-notification
  .ant-picker-calendar-full
  .ant-picker-panel
  .ant-picker-calendar-date {
  border-top: 2px solid #3a3a3a;
}

[data-theme='dark']
  .ant-notification
  .ant-tabs.ant-tabs-card
  .ant-tabs-card-bar
  .ant-tabs-tab-active {
  border-bottom: 1px solid #1f1f1f;
  background-color: #1f1f1f;
}

[data-theme='dark'] .ant-notification .ant-badge-count {
  box-shadow: 0 0 0 1px #1f1f1f;
}

[data-theme='dark'] .ant-notification .ant-tree-show-line .ant-tree-switcher {
  background: #1f1f1f;
}

[data-theme='dark'] .ant-notification {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-notification-notice {
  background: #1f1f1f;
}

[data-theme='dark'] .ant-notification-notice-message {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .ant-notification-notice-message-single-line-auto-margin {
  background-color: transparent;
}

[data-theme='dark'] .anticon.ant-notification-notice-icon-success {
  color: #55d187;
}

[data-theme='dark'] .anticon.ant-notification-notice-icon-info {
  color: #0960bd;
}

[data-theme='dark'] .anticon.ant-notification-notice-icon-warning {
  color: #efbd47;
}

[data-theme='dark'] .anticon.ant-notification-notice-icon-error {
  color: #ed6f6f;
}

[data-theme='dark'] .ant-notification-notice-close {
  color: #8b949e;
}

[data-theme='dark'] .ant-notification-notice-close:hover {
  color: rgb(255 255 255 / 85%);
}

[data-theme='dark'] .ant-message {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-message-notice-content {
  background: #1f1f1f;
}

[data-theme='dark'] .ant-message-success .anticon {
  color: #55d187;
}

[data-theme='dark'] .ant-message-error .anticon {
  color: #ed6f6f;
}

[data-theme='dark'] .ant-message-warning .anticon {
  color: #efbd47;
}

[data-theme='dark'] .ant-message-info .anticon,
[data-theme='dark'] .ant-message-loading .anticon {
  color: #0960bd;
}

[data-theme='dark'] .ant-spin {
  color: #0960bd;
}

[data-theme='dark'] .ant-spin-nested-loading > div > .ant-spin .ant-spin-text {
  text-shadow: 0 1px 2px #151515;
}

[data-theme='dark'] .ant-spin-container::after {
  background: #151515;
}

[data-theme='dark'] .ant-spin-tip {
  color: #8b949e;
}

[data-theme='dark'] .ant-spin-dot-item {
  background-color: #0960bd;
}

@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  [data-theme='dark'] {
    background: #151515;
  }
}

[data-theme='dark'] .ant-select-single.ant-select-open .ant-select-selection-item {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-select-disabled.ant-select-multiple .ant-select-selector {
  background: #151515;
}

[data-theme='dark'] .ant-select-multiple .ant-select-selection-item {
  border: 1px solid #303030;
  background: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-select-disabled.ant-select-multiple .ant-select-selection-item {
  border-color: #1f1f1f;
  color: #595959;
}

[data-theme='dark'] .ant-select-multiple .ant-select-selection-item-remove {
  color: #8b949e;
}

[data-theme='dark'] .ant-select-multiple .ant-select-selection-item-remove:hover {
  color: rgb(255 255 255 / 75%);
}

[data-theme='dark'] .ant-select {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-select:not(.ant-select-customize-input) .ant-select-selector {
  border: 1px solid #303030;
  background-color: transparent;
}

[data-theme='dark']
  .ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input)
  .ant-select-selector {
  border-color: #0960bd;
  box-shadow: 0 0 0 2px rgb(9 96 189 / 20%);
}

[data-theme='dark']
  .ant-select-disabled.ant-select:not(.ant-select-customize-input)
  .ant-select-selector {
  background: rgb(255 255 255 / 8%);
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark']
  .ant-select-multiple.ant-select-disabled.ant-select:not(.ant-select-customize-input)
  .ant-select-selector {
  background: #151515;
}

[data-theme='dark']
  .ant-select:not(.ant-select-customize-input)
  .ant-select-selector
  .ant-select-selection-search-input {
  background: 0 0;
}

[data-theme='dark'] .ant-select:not(.ant-select-disabled):hover .ant-select-selector {
  border-color: #2a7dc9;
}

[data-theme='dark'] .ant-select-selection-placeholder {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-select-arrow {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-select-clear {
  background: #151515;
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-select-clear:hover {
  color: #8b949e;
}

[data-theme='dark'] .ant-select-dropdown {
  background-color: #1f1f1f;
  color: #c9d1d9;
}

[data-theme='dark'] .ant-select-dropdown-empty {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-select-item-empty {
  color: #c9d1d9;
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-select-item {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-select-item-group {
  color: #8b949e;
}

[data-theme='dark'] .ant-select-item-option-active:not(.ant-select-item-option-disabled) {
  background-color: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  background-color: rgb(255 255 255 / 8%);
  color: #c9d1d9;
}

[data-theme='dark']
  .ant-select-item-option-selected:not(.ant-select-item-option-disabled)
  .ant-select-item-option-state {
  color: #0960bd;
}

[data-theme='dark'] .ant-select-item-option-disabled {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-select-item-option-disabled.ant-select-item-option-selected {
  background-color: #151515;
}

[data-theme='dark'] .ant-select-borderless .ant-select-selector {
  border-color: transparent !important;
  background-color: transparent !important;
}

[data-theme='dark'] .ant-switch {
  background-color: rgb(255 255 255 / 30%);
  color: #c9d1d9;
}

[data-theme='dark'] .ant-switch:focus {
  box-shadow: 0 0 0 2px rgb(255 255 255 / 10%);
}

[data-theme='dark'] .ant-switch-checked:focus {
  box-shadow: 0 0 0 2px rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-switch-checked {
  background-color: #0960bd;
}

[data-theme='dark'] .ant-switch-inner {
  color: #fff;
}

[data-theme='dark'] .ant-switch-handle::before {
  background-color: #fff;
  box-shadow: 0 2px 4px 0 rgb(0 35 11 / 20%);
}

[data-theme='dark'] .ant-switch-loading-icon.anticon {
  color: rgb(0 0 0 / 65%);
}

[data-theme='dark'] .ant-switch-checked .ant-switch-loading-icon {
  color: #0960bd;
}

[data-theme='dark'] .ant-select-auto-complete {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-cascader-checkbox {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-cascader-checkbox-input:focus + .ant-cascader-checkbox-inner,
[data-theme='dark'] .ant-cascader-checkbox-wrapper:hover .ant-cascader-checkbox-inner,
[data-theme='dark'] .ant-cascader-checkbox:hover .ant-cascader-checkbox-inner {
  border-color: #0960bd;
}

[data-theme='dark'] .ant-cascader-checkbox-checked::after {
  border: 1px solid #0960bd;
}

[data-theme='dark'] .ant-cascader-checkbox-inner {
  border: 1px solid #303030;
  background-color: transparent;
}

[data-theme='dark'] .ant-cascader-checkbox-inner::after {
  border: 2px solid #fff;
}

[data-theme='dark'] .ant-cascader-checkbox-checked .ant-cascader-checkbox-inner::after {
  border: 2px solid #fff;
}

[data-theme='dark'] .ant-cascader-checkbox-checked .ant-cascader-checkbox-inner {
  border-color: #0960bd;
  background-color: #0960bd;
}

[data-theme='dark']
  .ant-cascader-checkbox-disabled.ant-cascader-checkbox-checked
  .ant-cascader-checkbox-inner::after {
  border-color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-cascader-checkbox-disabled .ant-cascader-checkbox-inner {
  border-color: #303030 !important;
  background-color: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-cascader-checkbox-disabled .ant-cascader-checkbox-inner::after {
  border-color: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-cascader-checkbox-disabled + span {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-cascader-checkbox-wrapper {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-cascader-checkbox-group {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-cascader-checkbox-indeterminate .ant-cascader-checkbox-inner {
  border-color: #303030;
  background-color: transparent;
}

[data-theme='dark'] .ant-cascader-checkbox-indeterminate .ant-cascader-checkbox-inner::after {
  background-color: #0960bd;
}

[data-theme='dark']
  .ant-cascader-checkbox-indeterminate.ant-cascader-checkbox-disabled
  .ant-cascader-checkbox-inner::after {
  border-color: rgb(255 255 255 / 30%);
  background-color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-cascader-menu {
  border-right: 1px solid #303030;
}

[data-theme='dark'] .ant-cascader-menu-item:hover {
  background: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-cascader-menu-item-disabled {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-cascader-menu-item-disabled:hover {
  background: 0 0;
}

[data-theme='dark'] .ant-cascader-menu-empty .ant-cascader-menu-item {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled),
[data-theme='dark'] .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled):hover {
  background-color: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-cascader-menu-item-expand .ant-cascader-menu-item-expand-icon,
[data-theme='dark'] .ant-cascader-menu-item-loading-icon {
  color: #8b949e;
}

[data-theme='dark']
  .ant-cascader-menu-item-disabled.ant-cascader-menu-item-expand
  .ant-cascader-menu-item-expand-icon,
[data-theme='dark'] .ant-cascader-menu-item-disabled.ant-cascader-menu-item-loading-icon {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-cascader-menu-item-keyword {
  color: #a71d25;
}

[data-theme='dark'] .ant-back-top {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-back-top-content {
  background-color: #8b949e;
  color: #fff;
}

[data-theme='dark'] .ant-back-top-content:hover {
  background-color: #c9d1d9;
}

[data-theme='dark'] .ant-modal {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-modal-mask {
  background-color: rgb(0 0 0 / 45%);
}

[data-theme='dark'] .ant-modal-title {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .ant-modal-content {
  background-color: #1f1f1f;
  box-shadow: 0 4px 8px 0 rgb(0 0 0 / 20%), 0 6px 20px 0 rgb(0 0 0 / 19%);
}

[data-theme='dark'] .ant-modal-close {
  background: 0 0;
  color: #8b949e;
}

[data-theme='dark'] .ant-modal-close:focus,
[data-theme='dark'] .ant-modal-close:hover {
  color: rgb(255 255 255 / 75%);
}

[data-theme='dark'] .ant-modal-header {
  border-bottom: 1px solid #303030;
  background: #1f1f1f;
  color: #c9d1d9;
}

[data-theme='dark'] .ant-modal-footer {
  border-top: 1px solid #303030;
  background: 0 0;
}

[data-theme='dark'] .ant-modal-confirm-body .ant-modal-confirm-title {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .ant-modal-confirm-body .ant-modal-confirm-content {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-modal-confirm-error .ant-modal-confirm-body > .anticon {
  color: #ed6f6f;
}

[data-theme='dark'] .ant-modal-confirm-confirm .ant-modal-confirm-body > .anticon,
[data-theme='dark'] .ant-modal-confirm-warning .ant-modal-confirm-body > .anticon {
  color: #efbd47;
}

[data-theme='dark'] .ant-modal-confirm-success .ant-modal-confirm-body > .anticon {
  color: #55d187;
}

[data-theme='dark'] .ant-modal .ant-anchor-wrapper,
[data-theme='dark'] .ant-modal .ant-card,
[data-theme='dark'] .ant-modal .ant-collapse-content,
[data-theme='dark'] .ant-modal .ant-picker-clear,
[data-theme='dark'] .ant-modal .ant-slider-handle,
[data-theme='dark'] .ant-modal .ant-timeline-item-head {
  background-color: #1f1f1f;
}

[data-theme='dark'] .ant-modal .ant-transfer-list-header {
  border-bottom: 1px solid #3a3a3a;
  background: #1f1f1f;
}

[data-theme='dark']
  .ant-modal
  .ant-transfer-list-content-item:not(.ant-transfer-list-content-item-disabled):hover {
  background-color: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-modal tr.ant-table-expanded-row:hover > td,
[data-theme='dark'] .ant-modal tr.ant-table-expanded-row > td {
  background: #272727;
}

[data-theme='dark'] .ant-modal .ant-table.ant-table-small thead > tr > th {
  border-bottom: 1px solid #3a3a3a;
  background-color: #1f1f1f;
}

[data-theme='dark'] .ant-modal .ant-table {
  background-color: #1f1f1f;
}

[data-theme='dark'] .ant-modal .ant-table .ant-table-row-expand-icon {
  border: 1px solid #3a3a3a;
}

[data-theme='dark'] .ant-modal .ant-table tfoot > tr > td,
[data-theme='dark'] .ant-modal .ant-table tfoot > tr > th {
  border-bottom: 1px solid #3a3a3a;
}

[data-theme='dark'] .ant-modal .ant-table thead > tr > th {
  border-bottom: 1px solid #3a3a3a;
  background-color: #272727;
}

[data-theme='dark'] .ant-modal .ant-table tbody > tr > td {
  border-bottom: 1px solid #3a3a3a;
}

[data-theme='dark'] .ant-modal .ant-table tbody > tr > td.ant-table-cell-fix-left,
[data-theme='dark'] .ant-modal .ant-table tbody > tr > td.ant-table-cell-fix-right {
  background-color: #1f1f1f;
}

[data-theme='dark'] .ant-modal .ant-table tbody > tr.ant-table-row:hover > td {
  background: #303030;
}

[data-theme='dark'] .ant-modal .ant-table.ant-table-bordered .ant-table-title {
  border: 1px solid #3a3a3a;
}

[data-theme='dark'] .ant-modal .ant-table.ant-table-bordered tbody > tr > td,
[data-theme='dark'] .ant-modal .ant-table.ant-table-bordered tfoot > tr > td,
[data-theme='dark'] .ant-modal .ant-table.ant-table-bordered tfoot > tr > th,
[data-theme='dark'] .ant-modal .ant-table.ant-table-bordered thead > tr > th {
  border-right: 1px solid #3a3a3a;
}

[data-theme='dark']
  .ant-modal
  .ant-table.ant-table-bordered
  .ant-table-cell-fix-right-first::after {
  border-right: 1px solid #3a3a3a;
}

[data-theme='dark']
  .ant-modal
  .ant-table.ant-table-bordered
  table
  thead
  > tr:not(:last-child)
  > th {
  border-bottom: 1px solid #303030;
}

[data-theme='dark'] .ant-modal .ant-table.ant-table-bordered .ant-table-container {
  border: 1px solid #3a3a3a;
}

[data-theme='dark'] .ant-modal .ant-table.ant-table-bordered .ant-table-expanded-row-fixed::after {
  border-right: 1px solid #3a3a3a;
}

[data-theme='dark'] .ant-modal .ant-table.ant-table-bordered .ant-table-footer {
  border: 1px solid #3a3a3a;
}

[data-theme='dark'] .ant-modal .ant-table .ant-table-filter-trigger-container-open {
  background-color: #525252;
}

[data-theme='dark'] .ant-modal .ant-picker-calendar-full {
  background-color: #1f1f1f;
}

[data-theme='dark'] .ant-modal .ant-picker-calendar-full .ant-picker-panel {
  background-color: #1f1f1f;
}

[data-theme='dark']
  .ant-modal
  .ant-picker-calendar-full
  .ant-picker-panel
  .ant-picker-calendar-date {
  border-top: 2px solid #3a3a3a;
}

[data-theme='dark'] .ant-modal .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
  border-bottom: 1px solid #1f1f1f;
  background-color: #1f1f1f;
}

[data-theme='dark'] .ant-modal .ant-badge-count {
  box-shadow: 0 0 0 1px #1f1f1f;
}

[data-theme='dark'] .ant-modal .ant-tree-show-line .ant-tree-switcher {
  background: #1f1f1f;
}

[data-theme='dark'] .ant-alert {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-alert-success {
  border: 1px solid #274916;
  background-color: #162312;
}

[data-theme='dark'] .ant-alert-success .ant-alert-icon {
  color: #49aa19;
}

[data-theme='dark'] .ant-alert-info {
  border: 1px solid #153450;
  background-color: #111b26;
}

[data-theme='dark'] .ant-alert-info .ant-alert-icon {
  color: #177ddc;
}

[data-theme='dark'] .ant-alert-warning {
  border: 1px solid #594214;
  background-color: #2b2111;
}

[data-theme='dark'] .ant-alert-warning .ant-alert-icon {
  color: #d89614;
}

[data-theme='dark'] .ant-alert-error {
  border: 1px solid #58181c;
  background-color: #2a1215;
}

[data-theme='dark'] .ant-alert-error .ant-alert-icon {
  color: #a61d24;
}

[data-theme='dark'] .ant-alert-close-icon {
  background-color: transparent;
}

[data-theme='dark'] .ant-alert-close-icon .anticon-close {
  color: #8b949e;
}

[data-theme='dark'] .ant-alert-close-icon .anticon-close:hover {
  color: rgb(255 255 255 / 75%);
}

[data-theme='dark'] .ant-alert-close-text {
  color: #8b949e;
}

[data-theme='dark'] .ant-alert-close-text:hover {
  color: rgb(255 255 255 / 75%);
}

[data-theme='dark'] .ant-alert-with-description .ant-alert-message {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .ant-alert-message {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .ant-steps {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-steps-item-icon {
  border: 1px solid rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-steps-item-icon .ant-steps-icon {
  color: #0960bd;
}

[data-theme='dark'] .ant-steps-item-tail::after {
  background: #303030;
}

[data-theme='dark'] .ant-steps-item-title {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-steps-item-title::after {
  background: #303030;
}

[data-theme='dark'] .ant-steps-item-subtitle {
  color: #8b949e;
}

[data-theme='dark'] .ant-steps-item-description {
  color: #8b949e;
}

[data-theme='dark'] .ant-steps-item-wait .ant-steps-item-icon {
  border-color: rgb(255 255 255 / 30%);
  background-color: transparent;
}

[data-theme='dark'] .ant-steps-item-wait .ant-steps-item-icon > .ant-steps-icon {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark']
  .ant-steps-item-wait
  .ant-steps-item-icon
  > .ant-steps-icon
  .ant-steps-icon-dot {
  background: rgb(255 255 255 / 30%);
}

[data-theme='dark']
  .ant-steps-item-wait
  > .ant-steps-item-container
  > .ant-steps-item-content
  > .ant-steps-item-title {
  color: #8b949e;
}

[data-theme='dark']
  .ant-steps-item-wait
  > .ant-steps-item-container
  > .ant-steps-item-content
  > .ant-steps-item-title::after {
  background-color: #303030;
}

[data-theme='dark']
  .ant-steps-item-wait
  > .ant-steps-item-container
  > .ant-steps-item-content
  > .ant-steps-item-description {
  color: #8b949e;
}

[data-theme='dark'] .ant-steps-item-wait > .ant-steps-item-container > .ant-steps-item-tail::after {
  background-color: #303030;
}

[data-theme='dark'] .ant-steps-item-process .ant-steps-item-icon {
  border-color: #0960bd;
  background-color: transparent;
}

[data-theme='dark'] .ant-steps-item-process .ant-steps-item-icon > .ant-steps-icon {
  color: #0960bd;
}

[data-theme='dark']
  .ant-steps-item-process
  .ant-steps-item-icon
  > .ant-steps-icon
  .ant-steps-icon-dot {
  background: #0960bd;
}

[data-theme='dark']
  .ant-steps-item-process
  > .ant-steps-item-container
  > .ant-steps-item-content
  > .ant-steps-item-title {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark']
  .ant-steps-item-process
  > .ant-steps-item-container
  > .ant-steps-item-content
  > .ant-steps-item-title::after {
  background-color: #303030;
}

[data-theme='dark']
  .ant-steps-item-process
  > .ant-steps-item-container
  > .ant-steps-item-content
  > .ant-steps-item-description {
  color: #c9d1d9;
}

[data-theme='dark']
  .ant-steps-item-process
  > .ant-steps-item-container
  > .ant-steps-item-tail::after {
  background-color: #303030;
}

[data-theme='dark'] .ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-icon {
  background: #0960bd;
}

[data-theme='dark']
  .ant-steps-item-process
  > .ant-steps-item-container
  > .ant-steps-item-icon
  .ant-steps-icon {
  color: #fff;
}

[data-theme='dark'] .ant-steps-item-finish .ant-steps-item-icon {
  border-color: #0960bd;
  background-color: transparent;
}

[data-theme='dark'] .ant-steps-item-finish .ant-steps-item-icon > .ant-steps-icon {
  color: #0960bd;
}

[data-theme='dark']
  .ant-steps-item-finish
  .ant-steps-item-icon
  > .ant-steps-icon
  .ant-steps-icon-dot {
  background: #0960bd;
}

[data-theme='dark']
  .ant-steps-item-finish
  > .ant-steps-item-container
  > .ant-steps-item-content
  > .ant-steps-item-title {
  color: #c9d1d9;
}

[data-theme='dark']
  .ant-steps-item-finish
  > .ant-steps-item-container
  > .ant-steps-item-content
  > .ant-steps-item-title::after {
  background-color: #0960bd;
}

[data-theme='dark']
  .ant-steps-item-finish
  > .ant-steps-item-container
  > .ant-steps-item-content
  > .ant-steps-item-description {
  color: #8b949e;
}

[data-theme='dark']
  .ant-steps-item-finish
  > .ant-steps-item-container
  > .ant-steps-item-tail::after {
  background-color: #0960bd;
}

[data-theme='dark'] .ant-steps-item-error .ant-steps-item-icon {
  border-color: #ed6f6f;
  background-color: transparent;
}

[data-theme='dark'] .ant-steps-item-error .ant-steps-item-icon > .ant-steps-icon {
  color: #ed6f6f;
}

[data-theme='dark']
  .ant-steps-item-error
  .ant-steps-item-icon
  > .ant-steps-icon
  .ant-steps-icon-dot {
  background: #ed6f6f;
}

[data-theme='dark']
  .ant-steps-item-error
  > .ant-steps-item-container
  > .ant-steps-item-content
  > .ant-steps-item-title {
  color: #ed6f6f;
}

[data-theme='dark']
  .ant-steps-item-error
  > .ant-steps-item-container
  > .ant-steps-item-content
  > .ant-steps-item-title::after {
  background-color: #303030;
}

[data-theme='dark']
  .ant-steps-item-error
  > .ant-steps-item-container
  > .ant-steps-item-content
  > .ant-steps-item-description {
  color: #ed6f6f;
}

[data-theme='dark']
  .ant-steps-item-error
  > .ant-steps-item-container
  > .ant-steps-item-tail::after {
  background-color: #303030;
}

[data-theme='dark'] .ant-steps-item.ant-steps-next-error .ant-steps-item-title::after {
  background: #ed6f6f;
}

[data-theme='dark']
  .ant-steps
  .ant-steps-item:not(.ant-steps-item-active)
  > .ant-steps-item-container[role='button']:hover
  .ant-steps-item-description,
[data-theme='dark']
  .ant-steps
  .ant-steps-item:not(.ant-steps-item-active)
  > .ant-steps-item-container[role='button']:hover
  .ant-steps-item-subtitle,
[data-theme='dark']
  .ant-steps
  .ant-steps-item:not(.ant-steps-item-active)
  > .ant-steps-item-container[role='button']:hover
  .ant-steps-item-title {
  color: #0960bd;
}

[data-theme='dark']
  .ant-steps
  .ant-steps-item:not(.ant-steps-item-active):not(.ant-steps-item-process)
  > .ant-steps-item-container[role='button']:hover
  .ant-steps-item-icon {
  border-color: #0960bd;
}

[data-theme='dark']
  .ant-steps
  .ant-steps-item:not(.ant-steps-item-active):not(.ant-steps-item-process)
  > .ant-steps-item-container[role='button']:hover
  .ant-steps-item-icon
  .ant-steps-icon {
  color: #0960bd;
}

[data-theme='dark']
  .ant-steps-item-custom.ant-steps-item-process
  .ant-steps-item-icon
  > .ant-steps-icon {
  color: #0960bd;
}

[data-theme='dark'] .ant-steps-small .ant-steps-item-description {
  color: #8b949e;
}

[data-theme='dark'] .ant-steps-dot .ant-steps-item-icon,
[data-theme='dark'] .ant-steps-dot.ant-steps-small .ant-steps-item-icon {
  background: 0 0;
}

[data-theme='dark'] .ant-steps-dot .ant-steps-item-icon .ant-steps-icon-dot::after,
[data-theme='dark'] .ant-steps-dot.ant-steps-small .ant-steps-item-icon .ant-steps-icon-dot::after {
  background: rgb(0 0 0 / 0.1%);
}

[data-theme='dark'] .ant-steps-navigation .ant-steps-item::after {
  border: 1px solid rgb(255 255 255 / 20%);
}

[data-theme='dark'] .ant-steps-navigation .ant-steps-item::before {
  background-color: #0960bd;
}

[data-theme='dark'] .ant-breadcrumb {
  color: #8b949e;
}

[data-theme='dark'] .ant-breadcrumb a {
  color: #8b949e;
}

[data-theme='dark'] .ant-breadcrumb a:hover {
  color: #2a7dc9;
}

[data-theme='dark'] .ant-breadcrumb > span:last-child {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-breadcrumb > span:last-child a {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-breadcrumb-separator {
  color: #8b949e;
}

[data-theme='dark'] .ant-picker-calendar {
  background: #151515;
  color: #c9d1d9;
}

[data-theme='dark'] .ant-picker-calendar .ant-picker-panel {
  border-top: 1px solid #303030;
  background: #151515;
}

[data-theme='dark'] .ant-picker-calendar-full .ant-picker-panel {
  background: #151515;
}

[data-theme='dark']
  .ant-picker-calendar-full
  .ant-picker-panel
  .ant-picker-cell:hover
  .ant-picker-calendar-date {
  background: rgb(255 255 255 / 8%);
}

[data-theme='dark']
  .ant-picker-calendar-full
  .ant-picker-panel
  .ant-picker-cell-selected
  .ant-picker-calendar-date,
[data-theme='dark']
  .ant-picker-calendar-full
  .ant-picker-panel
  .ant-picker-cell-selected
  .ant-picker-calendar-date-today,
[data-theme='dark']
  .ant-picker-calendar-full
  .ant-picker-panel
  .ant-picker-cell-selected:hover
  .ant-picker-calendar-date,
[data-theme='dark']
  .ant-picker-calendar-full
  .ant-picker-panel
  .ant-picker-cell-selected:hover
  .ant-picker-calendar-date-today {
  background: #111b26;
}

[data-theme='dark']
  .ant-picker-calendar-full
  .ant-picker-panel
  .ant-picker-cell-selected
  .ant-picker-calendar-date
  .ant-picker-calendar-date-value,
[data-theme='dark']
  .ant-picker-calendar-full
  .ant-picker-panel
  .ant-picker-cell-selected
  .ant-picker-calendar-date-today
  .ant-picker-calendar-date-value,
[data-theme='dark']
  .ant-picker-calendar-full
  .ant-picker-panel
  .ant-picker-cell-selected:hover
  .ant-picker-calendar-date
  .ant-picker-calendar-date-value,
[data-theme='dark']
  .ant-picker-calendar-full
  .ant-picker-panel
  .ant-picker-cell-selected:hover
  .ant-picker-calendar-date-today
  .ant-picker-calendar-date-value {
  color: #0960bd;
}

[data-theme='dark'] .ant-picker-calendar-full .ant-picker-panel .ant-picker-calendar-date {
  border-top: 2px solid #303030;
}

[data-theme='dark'] .ant-picker-calendar-full .ant-picker-panel .ant-picker-calendar-date-content {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-picker-calendar-full .ant-picker-panel .ant-picker-calendar-date-today {
  border-color: #0960bd;
}

[data-theme='dark']
  .ant-picker-calendar-full
  .ant-picker-panel
  .ant-picker-calendar-date-today
  .ant-picker-calendar-date-value {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-picker {
  border: 1px solid #303030;
  background: 0 0;
  color: #c9d1d9;
}

[data-theme='dark'] .ant-picker-focused,
[data-theme='dark'] .ant-picker:hover {
  border-color: #2a7dc9;
}

[data-theme='dark'] .ant-picker-focused {
  border-color: #0960bd;
  box-shadow: 0 0 0 2px rgb(9 96 189 / 20%);
}

[data-theme='dark'] .ant-picker.ant-picker-disabled {
  border-color: #303030;
  background: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-picker.ant-picker-disabled .ant-picker-suffix {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-picker.ant-picker-borderless {
  border-color: transparent !important;
  background-color: transparent !important;
}

[data-theme='dark'] .ant-picker-input > input {
  border: 1px solid #303030;
  background: 0 0;
  background-color: transparent;
  color: #c9d1d9;
}

[data-theme='dark'] .ant-picker-input > input::placeholder {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-picker-input > input:hover {
  border-color: #2a7dc9;
}

[data-theme='dark'] .ant-picker-input > input-focused,
[data-theme='dark'] .ant-picker-input > input:focus {
  border-color: #0960bd;
  box-shadow: 0 0 0 2px rgb(9 96 189 / 20%);
}

[data-theme='dark'] .ant-picker-input > input-disabled {
  border-color: #303030;
  background-color: rgb(255 255 255 / 8%);
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-picker-input > input-disabled:hover {
  border-color: #303030;
}

[data-theme='dark'] .ant-picker-input > input[disabled] {
  border-color: #303030;
  background: 0 0;
  background-color: rgb(255 255 255 / 8%);
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-picker-input > input[disabled]:hover {
  border-color: #303030;
}

[data-theme='dark'] .ant-picker-input > input-borderless,
[data-theme='dark'] .ant-picker-input > input-borderless-disabled,
[data-theme='dark'] .ant-picker-input > input-borderless-focused,
[data-theme='dark'] .ant-picker-input > input-borderless:focus,
[data-theme='dark'] .ant-picker-input > input-borderless:hover,
[data-theme='dark'] .ant-picker-input > input-borderless[disabled] {
  background-color: transparent;
}

[data-theme='dark'] .ant-picker-input-placeholder > input {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-picker-suffix {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-picker-clear {
  background: #151515;
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-picker-clear:hover {
  color: #8b949e;
}

[data-theme='dark'] .ant-picker-separator {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-picker-focused .ant-picker-separator {
  color: #8b949e;
}

[data-theme='dark'] .ant-picker-range .ant-picker-active-bar {
  background: #0960bd;
}

[data-theme='dark'] .ant-picker-dropdown {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-picker-ranges .ant-picker-preset > .ant-tag-blue {
  border-color: #78b7e3;
  background: rgb(255 255 255 / 8%);
  color: #0960bd;
}

[data-theme='dark'] .ant-picker-range-arrow::after {
  border: 5px solid #303030;
  border-color: #1f1f1f;
}

[data-theme='dark'] .ant-picker-panel-container {
  background: #1f1f1f;
}

[data-theme='dark'] .ant-picker-panel-container .ant-picker-panel {
  background: 0 0;
}

[data-theme='dark'] .ant-picker-panel-container .ant-picker-panel-focused {
  border-color: #303030;
}

[data-theme='dark'] .ant-picker-panel {
  border: 1px solid #303030;
  background: #1f1f1f;
}

[data-theme='dark'] .ant-picker-panel-focused {
  border-color: #0960bd;
}

[data-theme='dark'] .ant-picker-header {
  border-bottom: 1px solid #303030;
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .ant-picker-header button {
  background: 0 0;
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-picker-header > button:hover {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-picker-header-view button:hover {
  color: #0960bd;
}

[data-theme='dark'] .ant-picker-content th {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-picker-cell {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-picker-cell-in-view {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-picker-cell:hover:not(.ant-picker-cell-in-view) .ant-picker-cell-inner,
[data-theme='dark']
  .ant-picker-cell:hover:not(.ant-picker-cell-selected):not(.ant-picker-cell-range-start):not(
    .ant-picker-cell-range-end
  ):not(.ant-picker-cell-range-hover-start):not(.ant-picker-cell-range-hover-end)
  .ant-picker-cell-inner {
  background: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-picker-cell-in-view.ant-picker-cell-today .ant-picker-cell-inner::before {
  border: 1px solid #0960bd;
}

[data-theme='dark'] .ant-picker-cell-in-view.ant-picker-cell-in-range::before {
  background: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-picker-cell-in-view.ant-picker-cell-range-end .ant-picker-cell-inner,
[data-theme='dark'] .ant-picker-cell-in-view.ant-picker-cell-range-start .ant-picker-cell-inner,
[data-theme='dark'] .ant-picker-cell-in-view.ant-picker-cell-selected .ant-picker-cell-inner {
  background: #0960bd;
  color: #fff;
}

[data-theme='dark']
  .ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-range-end-single)::before,
[data-theme='dark']
  .ant-picker-cell-in-view.ant-picker-cell-range-start:not(
    .ant-picker-cell-range-start-single
  )::before {
  background: rgb(255 255 255 / 8%);
}

[data-theme='dark']
  .ant-picker-cell-in-view.ant-picker-cell-range-hover-end.ant-picker-cell-range-end-single::after,
[data-theme='dark']
  .ant-picker-cell-in-view.ant-picker-cell-range-hover-end.ant-picker-cell-range-start.ant-picker-cell-range-end.ant-picker-cell-range-start-near-hover::after,
[data-theme='dark']
  .ant-picker-cell-in-view.ant-picker-cell-range-hover-end:not(.ant-picker-cell-in-range):not(
    .ant-picker-cell-range-start
  ):not(.ant-picker-cell-range-end)::after,
[data-theme='dark']
  .ant-picker-cell-in-view.ant-picker-cell-range-hover-start.ant-picker-cell-range-start-single::after,
[data-theme='dark']
  .ant-picker-cell-in-view.ant-picker-cell-range-hover-start.ant-picker-cell-range-start.ant-picker-cell-range-end.ant-picker-cell-range-end-near-hover::after,
[data-theme='dark']
  .ant-picker-cell-in-view.ant-picker-cell-range-hover-start:not(.ant-picker-cell-in-range):not(
    .ant-picker-cell-range-start
  ):not(.ant-picker-cell-range-end)::after,
[data-theme='dark']
  .ant-picker-cell-in-view.ant-picker-cell-range-hover:not(.ant-picker-cell-in-range)::after {
  border-top: 1px dashed #042f5c;
  border-bottom: 1px dashed #042f5c;
}

[data-theme='dark']
  .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover::before,
[data-theme='dark']
  .ant-picker-cell-in-view.ant-picker-cell-range-end.ant-picker-cell-range-hover::before,
[data-theme='dark']
  .ant-picker-cell-in-view.ant-picker-cell-range-end:not(
    .ant-picker-cell-range-end-single
  ).ant-picker-cell-range-hover-end::before,
[data-theme='dark']
  .ant-picker-cell-in-view.ant-picker-cell-range-start.ant-picker-cell-range-hover::before,
[data-theme='dark']
  .ant-picker-cell-in-view.ant-picker-cell-range-start:not(
    .ant-picker-cell-range-start-single
  ).ant-picker-cell-range-hover-start::before,
[data-theme='dark']
  .ant-picker-panel
  > :not(.ant-picker-date-panel)
  .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-end::before,
[data-theme='dark']
  .ant-picker-panel
  > :not(.ant-picker-date-panel)
  .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-start::before {
  background: #010913;
}

[data-theme='dark']
  .ant-picker-date-panel
  .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-end
  .ant-picker-cell-inner::after,
[data-theme='dark']
  .ant-picker-date-panel
  .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-start
  .ant-picker-cell-inner::after {
  background: #010913;
}

[data-theme='dark']
  .ant-picker-cell-in-view.ant-picker-cell-range-hover-edge-start:not(
    .ant-picker-cell-range-hover-edge-start-near-range
  )::after,
[data-theme='dark'] .ant-picker-cell-in-view.ant-picker-cell-range-hover-start::after,
[data-theme='dark']
  .ant-picker-cell-in-view.ant-picker-cell-start.ant-picker-cell-range-hover-edge-start.ant-picker-cell-range-hover-edge-start-near-range::after,
[data-theme='dark']
  tr
  > .ant-picker-cell-in-view.ant-picker-cell-range-hover-end:first-child::after,
[data-theme='dark'] tr > .ant-picker-cell-in-view.ant-picker-cell-range-hover:first-child::after {
  border-left: 1px dashed #042f5c;
}

[data-theme='dark']
  .ant-picker-cell-in-view.ant-picker-cell-end.ant-picker-cell-range-hover-edge-end.ant-picker-cell-range-hover-edge-end-near-range::after,
[data-theme='dark']
  .ant-picker-cell-in-view.ant-picker-cell-range-hover-edge-end:not(
    .ant-picker-cell-range-hover-edge-end-near-range
  )::after,
[data-theme='dark'] .ant-picker-cell-in-view.ant-picker-cell-range-hover-end::after,
[data-theme='dark']
  tr
  > .ant-picker-cell-in-view.ant-picker-cell-range-hover-start:last-child::after,
[data-theme='dark'] tr > .ant-picker-cell-in-view.ant-picker-cell-range-hover:last-child::after {
  border-right: 1px dashed #042f5c;
}

[data-theme='dark'] .ant-picker-cell-disabled {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-picker-cell-disabled .ant-picker-cell-inner {
  background: 0 0;
}

[data-theme='dark'] .ant-picker-cell-disabled::before {
  background: #303030;
}

[data-theme='dark'] .ant-picker-cell-disabled.ant-picker-cell-today .ant-picker-cell-inner::before {
  border-color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-picker-footer {
  border-bottom: 1px solid transparent;
}

[data-theme='dark'] .ant-picker-panel .ant-picker-footer {
  border-top: 1px solid #303030;
}

[data-theme='dark'] .ant-picker-footer-extra:not(:last-child) {
  border-bottom: 1px solid #303030;
}

[data-theme='dark'] .ant-picker-today-btn {
  color: #0960bd;
}

[data-theme='dark'] .ant-picker-today-btn:hover {
  color: #2a7dc9;
}

[data-theme='dark'] .ant-picker-today-btn:active {
  color: #004496;
}

[data-theme='dark'] .ant-picker-today-btn.ant-picker-today-btn-disabled {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-picker-month-panel .ant-picker-cell-range-hover-start::after,
[data-theme='dark'] .ant-picker-quarter-panel .ant-picker-cell-range-hover-start::after,
[data-theme='dark'] .ant-picker-year-panel .ant-picker-cell-range-hover-start::after {
  border-left: 1px dashed #042f5c;
}

[data-theme='dark']
  .ant-picker-panel-rtl
  .ant-picker-month-panel
  .ant-picker-cell-range-hover-start::after,
[data-theme='dark']
  .ant-picker-panel-rtl
  .ant-picker-quarter-panel
  .ant-picker-cell-range-hover-start::after,
[data-theme='dark']
  .ant-picker-panel-rtl
  .ant-picker-year-panel
  .ant-picker-cell-range-hover-start::after {
  border-right: 1px dashed #042f5c;
}

[data-theme='dark'] .ant-picker-month-panel .ant-picker-cell-range-hover-end::after,
[data-theme='dark'] .ant-picker-quarter-panel .ant-picker-cell-range-hover-end::after,
[data-theme='dark'] .ant-picker-year-panel .ant-picker-cell-range-hover-end::after {
  border-right: 1px dashed #042f5c;
}

[data-theme='dark']
  .ant-picker-panel-rtl
  .ant-picker-month-panel
  .ant-picker-cell-range-hover-end::after,
[data-theme='dark']
  .ant-picker-panel-rtl
  .ant-picker-quarter-panel
  .ant-picker-cell-range-hover-end::after,
[data-theme='dark']
  .ant-picker-panel-rtl
  .ant-picker-year-panel
  .ant-picker-cell-range-hover-end::after {
  border-left: 1px dashed #042f5c;
}

[data-theme='dark'] .ant-picker-week-panel .ant-picker-cell .ant-picker-cell-inner,
[data-theme='dark'] .ant-picker-week-panel .ant-picker-cell-selected .ant-picker-cell-inner,
[data-theme='dark'] .ant-picker-week-panel .ant-picker-cell:hover .ant-picker-cell-inner {
  background: 0 0 !important;
}

[data-theme='dark'] .ant-picker-week-panel-row:hover td {
  background: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-picker-week-panel-row-selected td,
[data-theme='dark'] .ant-picker-week-panel-row-selected:hover td {
  background: #0960bd;
}

[data-theme='dark'] .ant-picker-week-panel-row-selected td.ant-picker-cell-week,
[data-theme='dark'] .ant-picker-week-panel-row-selected:hover td.ant-picker-cell-week {
  color: rgb(255 255 255 / 50%);
}

[data-theme='dark']
  .ant-picker-week-panel-row-selected
  td.ant-picker-cell-today
  .ant-picker-cell-inner::before,
[data-theme='dark']
  .ant-picker-week-panel-row-selected:hover
  td.ant-picker-cell-today
  .ant-picker-cell-inner::before {
  border-color: #fff;
}

[data-theme='dark'] .ant-picker-week-panel-row-selected td .ant-picker-cell-inner,
[data-theme='dark'] .ant-picker-week-panel-row-selected:hover td .ant-picker-cell-inner {
  color: #fff;
}

[data-theme='dark'] .ant-picker-datetime-panel .ant-picker-time-panel {
  border-left: 1px solid #303030;
}

[data-theme='dark'] .ant-picker-time-panel-column:not(:first-child) {
  border-left: 1px solid #303030;
}

[data-theme='dark'] .ant-picker-time-panel-column-active {
  background: rgb(17 27 38 / 20%);
}

[data-theme='dark']
  .ant-picker-time-panel-column
  > li.ant-picker-time-panel-cell
  .ant-picker-time-panel-cell-inner {
  color: #c9d1d9;
}

[data-theme='dark']
  .ant-picker-time-panel-column
  > li.ant-picker-time-panel-cell
  .ant-picker-time-panel-cell-inner:hover {
  background: rgb(255 255 255 / 8%);
}

[data-theme='dark']
  .ant-picker-time-panel-column
  > li.ant-picker-time-panel-cell-selected
  .ant-picker-time-panel-cell-inner {
  background: #111b26;
}

[data-theme='dark']
  .ant-picker-time-panel-column
  > li.ant-picker-time-panel-cell-disabled
  .ant-picker-time-panel-cell-inner {
  background: 0 0;
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark']
  .ant-picker-panel-rtl
  .ant-picker-cell-in-view.ant-picker-cell-range-hover-edge-start:not(
    .ant-picker-cell-range-hover-edge-start-near-range
  )::after,
[data-theme='dark']
  .ant-picker-panel-rtl
  .ant-picker-cell-in-view.ant-picker-cell-range-hover-start::after,
[data-theme='dark']
  .ant-picker-panel-rtl
  .ant-picker-cell-in-view.ant-picker-cell-start.ant-picker-cell-range-hover-edge-start.ant-picker-cell-range-hover-edge-start-near-range::after,
[data-theme='dark']
  .ant-picker-panel-rtl
  tr
  > .ant-picker-cell-in-view.ant-picker-cell-range-hover:not(
    .ant-picker-cell-selected
  ):first-child::after {
  border-right: 1px dashed #042f5c;
}

[data-theme='dark']
  .ant-picker-panel-rtl
  .ant-picker-cell-in-view.ant-picker-cell-end.ant-picker-cell-range-hover-edge-end.ant-picker-cell-range-hover-edge-end-near-range::after,
[data-theme='dark']
  .ant-picker-panel-rtl
  .ant-picker-cell-in-view.ant-picker-cell-range-hover-edge-end:not(
    .ant-picker-cell-range-hover-edge-end-near-range
  )::after,
[data-theme='dark']
  .ant-picker-panel-rtl
  .ant-picker-cell-in-view.ant-picker-cell-range-hover-end::after,
[data-theme='dark']
  .ant-picker-panel-rtl
  tr
  > .ant-picker-cell-in-view.ant-picker-cell-range-hover:not(
    .ant-picker-cell-selected
  ):last-child::after {
  border-left: 1px dashed #042f5c;
}

[data-theme='dark']
  .ant-picker-panel-rtl
  .ant-picker-cell-in-view.ant-picker-cell-end.ant-picker-cell-range-hover-start.ant-picker-cell-range-hover-edge-end:not(
    .ant-picker-cell-range-hover
  )::after,
[data-theme='dark']
  .ant-picker-panel-rtl
  .ant-picker-cell-in-view.ant-picker-cell-start.ant-picker-cell-range-hover-edge-start:not(
    .ant-picker-cell-range-hover
  )::after,
[data-theme='dark']
  .ant-picker-panel-rtl
  .ant-picker-cell-in-view.ant-picker-cell-start.ant-picker-cell-range-hover-end.ant-picker-cell-range-hover-edge-start:not(
    .ant-picker-cell-range-hover
  )::after,
[data-theme='dark']
  .ant-picker-panel-rtl
  tr
  > .ant-picker-cell-in-view.ant-picker-cell-end.ant-picker-cell-range-hover.ant-picker-cell-range-hover-edge-end:first-child::after,
[data-theme='dark']
  .ant-picker-panel-rtl
  tr
  > .ant-picker-cell-in-view.ant-picker-cell-range-hover-end:first-child::after,
[data-theme='dark']
  .ant-picker-panel-rtl
  tr
  > .ant-picker-cell-in-view.ant-picker-cell-range-hover-start:last-child::after,
[data-theme='dark']
  .ant-picker-panel-rtl
  tr
  > .ant-picker-cell-in-view.ant-picker-cell-start.ant-picker-cell-range-hover.ant-picker-cell-range-hover-edge-start:last-child::after {
  border-right: 1px dashed #042f5c;
  border-left: 1px dashed #042f5c;
}

[data-theme='dark'] .ant-slider {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-slider-rail {
  background-color: #262626;
}

[data-theme='dark'] .ant-slider-track {
  background-color: #78b7e3;
}

[data-theme='dark'] .ant-slider-handle {
  border: solid 2px #78b7e3;
  background-color: #151515;
}

[data-theme='dark']
  .ant-slider-handle-dragging.ant-slider-handle-dragging.ant-slider-handle-dragging {
  border-color: #3a80ca;
  box-shadow: 0 0 0 5px rgb(9 96 189 / 12%);
}

[data-theme='dark'] .ant-slider-handle:focus {
  border-color: #3a80ca;
  box-shadow: 0 0 0 5px rgb(9 96 189 / 12%);
}

[data-theme='dark'] .ant-slider-handle.ant-tooltip-open {
  border-color: #0960bd;
}

[data-theme='dark'] .ant-slider:hover .ant-slider-rail {
  background-color: #303030;
}

[data-theme='dark'] .ant-slider:hover .ant-slider-track {
  background-color: #4f99d6;
}

[data-theme='dark'] .ant-slider:hover .ant-slider-handle:not(.ant-tooltip-open) {
  border-color: #4f99d6;
}

[data-theme='dark'] .ant-slider-mark-text {
  color: #8b949e;
}

[data-theme='dark'] .ant-slider-mark-text-active {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-slider-step {
  background: 0 0;
}

[data-theme='dark'] .ant-slider-dot {
  border: 2px solid #303030;
  background-color: #151515;
}

[data-theme='dark'] .ant-slider-dot-active {
  border-color: #4f99d6;
}

[data-theme='dark'] .ant-slider-disabled .ant-slider-rail {
  background-color: #262626 !important;
}

[data-theme='dark'] .ant-slider-disabled .ant-slider-track {
  background-color: rgb(255 255 255 / 30%) !important;
}

[data-theme='dark'] .ant-slider-disabled .ant-slider-dot,
[data-theme='dark'] .ant-slider-disabled .ant-slider-handle {
  border-color: rgb(255 255 255 / 30%) !important;
  background-color: #151515;
}

[data-theme='dark'] .ant-table-small .ant-table-thead > tr > th {
  background-color: #1d1d1d;
}

[data-theme='dark'] .ant-table.ant-table-bordered > .ant-table-title {
  border: 1px solid #303030;
}

[data-theme='dark'] .ant-table.ant-table-bordered > .ant-table-container {
  border-left: 1px solid #303030;
}

[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-body
  > table
  > tbody
  > tr
  > td,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-body
  > table
  > tfoot
  > tr
  > td,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-body
  > table
  > tfoot
  > tr
  > th,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-body
  > table
  > thead
  > tr
  > th,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-content
  > table
  > tbody
  > tr
  > td,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-content
  > table
  > tfoot
  > tr
  > td,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-content
  > table
  > tfoot
  > tr
  > th,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-content
  > table
  > thead
  > tr
  > th,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-header
  > table
  > tbody
  > tr
  > td,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-header
  > table
  > tfoot
  > tr
  > td,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-header
  > table
  > tfoot
  > tr
  > th,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-header
  > table
  > thead
  > tr
  > th,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-summary
  > table
  > tbody
  > tr
  > td,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-summary
  > table
  > tfoot
  > tr
  > td,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-summary
  > table
  > tfoot
  > tr
  > th,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-summary
  > table
  > thead
  > tr
  > th {
  border-right: 1px solid #303030;
}

[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-body
  > table
  > thead
  > tr:not(:last-child)
  > th,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-content
  > table
  > thead
  > tr:not(:last-child)
  > th,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-header
  > table
  > thead
  > tr:not(:last-child)
  > th,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-summary
  > table
  > thead
  > tr:not(:last-child)
  > th {
  border-bottom: 1px solid #303030;
}

[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-body
  > table
  > thead
  > tr
  > th::before,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-content
  > table
  > thead
  > tr
  > th::before,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-header
  > table
  > thead
  > tr
  > th::before,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-summary
  > table
  > thead
  > tr
  > th::before {
  background-color: transparent !important;
}

[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-body
  > table
  > tbody
  > tr
  > .ant-table-cell-fix-right-first::after,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-body
  > table
  > tfoot
  > tr
  > .ant-table-cell-fix-right-first::after,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-body
  > table
  > thead
  > tr
  > .ant-table-cell-fix-right-first::after,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-content
  > table
  > tbody
  > tr
  > .ant-table-cell-fix-right-first::after,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-content
  > table
  > tfoot
  > tr
  > .ant-table-cell-fix-right-first::after,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-content
  > table
  > thead
  > tr
  > .ant-table-cell-fix-right-first::after,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-header
  > table
  > tbody
  > tr
  > .ant-table-cell-fix-right-first::after,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-header
  > table
  > tfoot
  > tr
  > .ant-table-cell-fix-right-first::after,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-header
  > table
  > thead
  > tr
  > .ant-table-cell-fix-right-first::after,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-summary
  > table
  > tbody
  > tr
  > .ant-table-cell-fix-right-first::after,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-summary
  > table
  > tfoot
  > tr
  > .ant-table-cell-fix-right-first::after,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-summary
  > table
  > thead
  > tr
  > .ant-table-cell-fix-right-first::after {
  border-right: 1px solid #303030;
}

[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-body
  > table
  > tbody
  > tr
  > td
  > .ant-table-expanded-row-fixed::after,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-content
  > table
  > tbody
  > tr
  > td
  > .ant-table-expanded-row-fixed::after,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-header
  > table
  > tbody
  > tr
  > td
  > .ant-table-expanded-row-fixed::after,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-summary
  > table
  > tbody
  > tr
  > td
  > .ant-table-expanded-row-fixed::after {
  border-right: 1px solid #303030;
}

[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-content
  > table,
[data-theme='dark']
  .ant-table.ant-table-bordered
  > .ant-table-container
  > .ant-table-header
  > table {
  border-top: 1px solid #303030;
}

[data-theme='dark'] .ant-table.ant-table-bordered > .ant-table-footer {
  border: 1px solid #303030;
}

[data-theme='dark'] .ant-table-cell-scrollbar {
  box-shadow: 0 1px 0 1px #1d1d1d;
}

[data-theme='dark'] .ant-table-resize-handle-line {
  background-color: #0960bd;
}

[data-theme='dark'] .ant-table {
  background: #151515;
  color: #c9d1d9;
}

[data-theme='dark'] .ant-table-footer {
  background: rgb(255 255 255 / 4%);
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .ant-table-thead > tr > th {
  border-bottom: 1px solid #303030;
  background: #1d1d1d;
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark']
  .ant-table-thead
  > tr
  > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not(
    [colspan]
  )::before {
  background-color: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-table-tbody > tr > td {
  border-bottom: 1px solid #303030;
}

[data-theme='dark'] .ant-table-tbody > tr.ant-table-row:hover > td,
[data-theme='dark'] .ant-table-tbody > tr > td.ant-table-cell-row-hover {
  background: #262626;
}

[data-theme='dark'] .ant-table-tbody > tr.ant-table-row-selected > td {
  border-color: rgb(0 0 0 / 3%);
  background: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-table-tbody > tr.ant-table-row-selected:hover > td {
  background: rgb(250 250 250 / 8%);
}

[data-theme='dark'] .ant-table-summary {
  background: #151515;
}

[data-theme='dark'] .ant-table-summary > tr > td,
[data-theme='dark'] .ant-table-summary > tr > th {
  border-bottom: 1px solid #303030;
}

[data-theme='dark'] .ant-table-thead th.ant-table-column-has-sorters:hover {
  background: #303030;
}

[data-theme='dark'] .ant-table-thead th.ant-table-column-has-sorters:hover::before {
  background-color: transparent !important;
}

[data-theme='dark'] .ant-table-thead th.ant-table-column-has-sorters.ant-table-cell-fix-left:hover,
[data-theme='dark']
  .ant-table-thead
  th.ant-table-column-has-sorters.ant-table-cell-fix-right:hover {
  background: #222;
}

[data-theme='dark'] .ant-table-thead th.ant-table-column-sort {
  background: #262626;
}

[data-theme='dark'] .ant-table-thead th.ant-table-column-sort::before {
  background-color: transparent !important;
}

[data-theme='dark'] td.ant-table-column-sort {
  background: rgb(255 255 255 / 1%);
}

[data-theme='dark'] .ant-table-column-sorter {
  color: #bfbfbf;
}

[data-theme='dark'] .ant-table-column-sorter-down.active,
[data-theme='dark'] .ant-table-column-sorter-up.active {
  color: #0960bd;
}

[data-theme='dark'] .ant-table-column-sorters:hover .ant-table-column-sorter {
  color: #a6a6a6;
}

[data-theme='dark'] .ant-table-filter-trigger {
  color: #bfbfbf;
}

[data-theme='dark'] .ant-table-filter-trigger:hover {
  background: #434343;
  color: #8b949e;
}

[data-theme='dark'] .ant-table-filter-trigger.active {
  color: #0960bd;
}

[data-theme='dark'] .ant-table-filter-dropdown {
  background-color: #1f1f1f;
  color: #c9d1d9;
}

[data-theme='dark'] .ant-table-filter-dropdown .ant-dropdown-menu:empty::after {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark']
  .ant-table-filter-dropdown-tree
  .ant-tree-treenode
  .ant-tree-node-content-wrapper:hover {
  background-color: rgb(255 255 255 / 8%);
}

[data-theme='dark']
  .ant-table-filter-dropdown-tree
  .ant-tree-treenode-checkbox-checked
  .ant-tree-node-content-wrapper,
[data-theme='dark']
  .ant-table-filter-dropdown-tree
  .ant-tree-treenode-checkbox-checked
  .ant-tree-node-content-wrapper:hover {
  background-color: #11263c;
}

[data-theme='dark'] .ant-table-filter-dropdown-search {
  border-bottom: 1px #303030;
}

[data-theme='dark'] .ant-table-filter-dropdown-search-input .anticon {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-table-filter-dropdown-btns {
  border-top: 1px solid #303030;
  background-color: #1f1f1f;
}

[data-theme='dark'] table tr th.ant-table-selection-column::after {
  background-color: transparent !important;
}

[data-theme='dark'] .ant-table-selection-extra .anticon {
  color: #bfbfbf;
}

[data-theme='dark'] .ant-table-selection-extra .anticon:hover {
  color: #a6a6a6;
}

[data-theme='dark'] .ant-table-row-expand-icon {
  border: 1px solid #303030;
  background: 0 0;
  color: #0960bd;
}

[data-theme='dark'] .ant-table-row-expand-icon:focus,
[data-theme='dark'] .ant-table-row-expand-icon:hover {
  color: #2a7dc9;
}

[data-theme='dark'] .ant-table-row-expand-icon:active {
  color: #004496;
}

[data-theme='dark'] .ant-table-row-expand-icon-spaced {
  background: 0 0;
}

[data-theme='dark'] tr.ant-table-expanded-row:hover > td,
[data-theme='dark'] tr.ant-table-expanded-row > td {
  background: #1d1d1d;
}

[data-theme='dark'] .ant-table-empty .ant-table-tbody > tr.ant-table-placeholder {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-table-tbody > tr.ant-table-placeholder:hover > td {
  background: #151515;
}

[data-theme='dark'] .ant-table-cell-fix-left,
[data-theme='dark'] .ant-table-cell-fix-right {
  background: #151515;
}

[data-theme='dark'] .ant-table-ping-left .ant-table-cell-fix-left-last::before {
  background-color: transparent !important;
}

[data-theme='dark'] .ant-table-sticky-holder {
  background: #151515;
}

[data-theme='dark'] .ant-table-sticky-scroll {
  border-top: 1px solid #303030;
  background: #fcfcfc;
}

[data-theme='dark'] .ant-table-sticky-scroll-bar {
  background-color: rgb(0 0 0 / 35%);
}

[data-theme='dark'] .ant-table-sticky-scroll-bar:hover {
  background-color: rgb(0 0 0 / 80%);
}

[data-theme='dark'] .ant-table-sticky-scroll-bar-active {
  background-color: rgb(0 0 0 / 80%);
}

[data-theme='dark'] .ant-progress {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-progress-steps-item {
  background: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-progress-steps-item-active {
  background: #0960bd;
}

[data-theme='dark'] .ant-progress-inner {
  background-color: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-progress-circle-trail {
  stroke: rgb(255 255 255 / 8%);
}

[data-theme='dark']
  .ant-progress-inner:not(.ant-progress-circle-gradient)
  .ant-progress-circle-path {
  stroke: #0960bd;
}

[data-theme='dark'] .ant-progress-bg,
[data-theme='dark'] .ant-progress-success-bg {
  background-color: #0960bd;
}

[data-theme='dark'] .ant-progress-success-bg {
  background-color: #55d187;
}

[data-theme='dark'] .ant-progress-text {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-progress-status-active .ant-progress-bg::before {
  background: #151515;
}

[data-theme='dark'] .ant-progress-status-exception .ant-progress-bg {
  background-color: #ed6f6f;
}

[data-theme='dark'] .ant-progress-status-exception .ant-progress-text {
  color: #ed6f6f;
}

[data-theme='dark']
  .ant-progress-status-exception
  .ant-progress-inner:not(.ant-progress-circle-gradient)
  .ant-progress-circle-path {
  stroke: #ed6f6f;
}

[data-theme='dark'] .ant-progress-status-success .ant-progress-bg {
  background-color: #55d187;
}

[data-theme='dark'] .ant-progress-status-success .ant-progress-text {
  color: #55d187;
}

[data-theme='dark']
  .ant-progress-status-success
  .ant-progress-inner:not(.ant-progress-circle-gradient)
  .ant-progress-circle-path {
  stroke: #55d187;
}

[data-theme='dark'] .ant-progress-circle .ant-progress-inner {
  background-color: transparent;
}

[data-theme='dark'] .ant-progress-circle .ant-progress-text {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-progress-circle.ant-progress-status-exception .ant-progress-text {
  color: #ed6f6f;
}

[data-theme='dark'] .ant-progress-circle.ant-progress-status-success .ant-progress-text {
  color: #55d187;
}

[data-theme='dark'] .ant-timeline {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-timeline-item-tail {
  border-left: 2px solid #303030;
}

[data-theme='dark'] .ant-timeline-item-pending .ant-timeline-item-head {
  background-color: transparent;
}

[data-theme='dark'] .ant-timeline-item-head {
  border: 2px solid transparent;
  background-color: #151515;
}

[data-theme='dark'] .ant-timeline-item-head-blue {
  border-color: #0960bd;
  color: #0960bd;
}

[data-theme='dark'] .ant-timeline-item-head-red {
  border-color: #ed6f6f;
  color: #ed6f6f;
}

[data-theme='dark'] .ant-timeline-item-head-green {
  border-color: #55d187;
  color: #55d187;
}

[data-theme='dark'] .ant-timeline-item-head-gray {
  border-color: rgb(255 255 255 / 30%);
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark']
  .ant-timeline.ant-timeline-pending
  .ant-timeline-item-last
  .ant-timeline-item-tail {
  border-left: 2px dotted #303030;
}

[data-theme='dark']
  .ant-timeline.ant-timeline-reverse
  .ant-timeline-item-pending
  .ant-timeline-item-tail {
  border-left: 2px dotted #303030;
}

[data-theme='dark'] .ant-timeline-rtl .ant-timeline-item-tail {
  border-right: 2px solid #303030;
}

[data-theme='dark']
  .ant-timeline-rtl.ant-timeline.ant-timeline-pending
  .ant-timeline-item-last
  .ant-timeline-item-tail {
  border-right: 2px dotted #303030;
}

[data-theme='dark']
  .ant-timeline-rtl.ant-timeline.ant-timeline-reverse
  .ant-timeline-item-pending
  .ant-timeline-item-tail {
  border-right: 2px dotted #303030;
}

[data-theme='dark'] .ant-input-number-affix-wrapper {
  border: 1px solid #303030;
  background-color: transparent;
  color: #c9d1d9;
}

[data-theme='dark'] .ant-input-number-affix-wrapper::placeholder {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-input-number-affix-wrapper:hover {
  border-color: #2a7dc9;
}

[data-theme='dark'] .ant-input-number-affix-wrapper-focused,
[data-theme='dark'] .ant-input-number-affix-wrapper:focus {
  border-color: #0960bd;
  box-shadow: 0 0 0 2px rgb(9 96 189 / 20%);
}

[data-theme='dark'] .ant-input-number-affix-wrapper-disabled {
  border-color: #303030;
  background-color: rgb(255 255 255 / 8%);
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-input-number-affix-wrapper-disabled:hover {
  border-color: #303030;
}

[data-theme='dark'] .ant-input-number-affix-wrapper[disabled] {
  border-color: #303030;
  background-color: rgb(255 255 255 / 8%);
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-input-number-affix-wrapper[disabled]:hover {
  border-color: #303030;
}

[data-theme='dark'] .ant-input-number-affix-wrapper-borderless,
[data-theme='dark'] .ant-input-number-affix-wrapper-borderless-disabled,
[data-theme='dark'] .ant-input-number-affix-wrapper-borderless-focused,
[data-theme='dark'] .ant-input-number-affix-wrapper-borderless:focus,
[data-theme='dark'] .ant-input-number-affix-wrapper-borderless:hover,
[data-theme='dark'] .ant-input-number-affix-wrapper-borderless[disabled] {
  background-color: transparent;
}

[data-theme='dark']
  .ant-input-number-affix-wrapper:not(.ant-input-number-affix-wrapper-disabled):hover {
  border-color: #2a7dc9;
}

[data-theme='dark'] .ant-input-number-affix-wrapper-disabled .ant-input-number[disabled] {
  background: 0 0;
}

[data-theme='dark'] .ant-input-number {
  border: 1px solid #303030;
  background-color: transparent;
  color: #c9d1d9;
}

[data-theme='dark'] .ant-input-number::placeholder {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-input-number-focused,
[data-theme='dark'] .ant-input-number:focus {
  border-color: #0960bd;
  box-shadow: 0 0 0 2px rgb(9 96 189 / 20%);
}

[data-theme='dark'] .ant-input-number[disabled] {
  border-color: #303030;
  background-color: rgb(255 255 255 / 8%);
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-input-number[disabled]:hover {
  border-color: #303030;
}

[data-theme='dark'] .ant-input-number-borderless,
[data-theme='dark'] .ant-input-number-borderless-disabled,
[data-theme='dark'] .ant-input-number-borderless-focused,
[data-theme='dark'] .ant-input-number-borderless:focus,
[data-theme='dark'] .ant-input-number-borderless:hover,
[data-theme='dark'] .ant-input-number-borderless[disabled] {
  background-color: transparent;
}

[data-theme='dark'] .ant-input-number-group {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-input-number-group-addon {
  border: 1px solid #303030;
  background-color: rgb(255 255 255 / 4%);
  color: #c9d1d9;
}

[data-theme='dark']
  .ant-input-number-group-addon
  .ant-select.ant-select-single:not(.ant-select-customize-input)
  .ant-select-selector {
  border: 1px solid transparent;
}

[data-theme='dark'] .ant-input-number-group-addon .ant-select-focused .ant-select-selector,
[data-theme='dark'] .ant-input-number-group-addon .ant-select-open .ant-select-selector {
  color: #0960bd;
}

[data-theme='dark'] .ant-input-number-group-addon .ant-cascader-picker {
  background-color: transparent;
}

[data-theme='dark'] .ant-input-number-handler {
  border-left: 1px solid #303030;
  color: #8b949e;
}

[data-theme='dark'] .ant-input-number-handler:active {
  background: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-input-number-handler:hover .ant-input-number-handler-down-inner,
[data-theme='dark'] .ant-input-number-handler:hover .ant-input-number-handler-up-inner {
  color: #2a7dc9;
}

[data-theme='dark'] .ant-input-number-handler-down-inner,
[data-theme='dark'] .ant-input-number-handler-up-inner {
  color: #8b949e;
}

[data-theme='dark'] .ant-input-number:hover {
  border-color: #2a7dc9;
}

[data-theme='dark'] .ant-input-number-focused {
  border-color: #0960bd;
  box-shadow: 0 0 0 2px rgb(9 96 189 / 20%);
}

[data-theme='dark'] .ant-input-number-disabled {
  border-color: #303030;
  background-color: rgb(255 255 255 / 8%);
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-input-number-disabled:hover {
  border-color: #303030;
}

[data-theme='dark'] .ant-input-number-input {
  background-color: transparent;
}

[data-theme='dark'] .ant-input-number-input::placeholder {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-input-number-handler-wrap {
  background: #151515;
}

[data-theme='dark'] .ant-input-number-handler-down {
  border-top: 1px solid #303030;
}

[data-theme='dark']
  .ant-input-number-handler-down-disabled:hover
  .ant-input-number-handler-down-inner,
[data-theme='dark'] .ant-input-number-handler-up-disabled:hover .ant-input-number-handler-up-inner {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-input-number-out-of-range input {
  color: #ed6f6f;
}

[data-theme='dark'] .ant-input-number-rtl .ant-input-number-handler {
  border-right: 1px solid #303030;
}

[data-theme='dark']
  .ant-transfer-customize-list
  .ant-table-wrapper
  .ant-table-small
  > .ant-table-content
  > .ant-table-body
  > table
  > .ant-table-thead
  > tr
  > th {
  background: #1d1d1d;
}

[data-theme='dark']
  .ant-transfer-customize-list
  .ant-table-wrapper
  .ant-table-small
  > .ant-table-content
  .ant-table-row:last-child
  td {
  border-bottom: 1px solid #303030;
}

[data-theme='dark'] .ant-transfer-customize-list .ant-input[disabled] {
  background-color: transparent;
}

[data-theme='dark'] .ant-transfer {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-transfer-disabled .ant-transfer-list {
  background: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-transfer-list {
  border: 1px solid #303030;
}

[data-theme='dark'] .ant-transfer-list-search .anticon-search {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-transfer-list-header {
  border-bottom: 1px solid #303030;
  background: #151515;
  color: #c9d1d9;
}

[data-theme='dark'] .ant-transfer-list-content-item-remove {
  color: #303030;
}

[data-theme='dark'] .ant-transfer-list-content-item-remove:focus,
[data-theme='dark'] .ant-transfer-list-content-item-remove:hover {
  color: #2a7dc9;
}

[data-theme='dark'] .ant-transfer-list-content-item-remove:active {
  color: #004496;
}

[data-theme='dark'] .ant-transfer-list-content-item-remove:hover {
  color: #2a7dc9;
}

[data-theme='dark']
  .ant-transfer-list-content-item:not(.ant-transfer-list-content-item-disabled):hover {
  background-color: #262626;
}

[data-theme='dark']
  .ant-transfer-list-content-item:not(
    .ant-transfer-list-content-item-disabled
  ).ant-transfer-list-content-item-checked:hover {
  background-color: #0e161f;
}

[data-theme='dark']
  .ant-transfer-list-content-show-remove
  .ant-transfer-list-content-item:not(.ant-transfer-list-content-item-disabled):hover {
  background: 0 0;
}

[data-theme='dark'] .ant-transfer-list-content-item-checked {
  background-color: #111b26;
}

[data-theme='dark'] .ant-transfer-list-content-item-disabled {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-transfer-list-pagination {
  border-top: 1px solid #303030;
}

[data-theme='dark'] .ant-transfer-list-body-not-found {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-transfer-list-footer {
  border-top: 1px solid #303030;
}

[data-theme='dark'] .ant-tree.ant-tree-directory .ant-tree-treenode:hover::before {
  background: rgb(255 255 255 / 8%);
}

[data-theme='dark']
  .ant-tree.ant-tree-directory
  .ant-tree-treenode
  .ant-tree-node-content-wrapper:hover {
  background: 0 0;
}

[data-theme='dark']
  .ant-tree.ant-tree-directory
  .ant-tree-treenode
  .ant-tree-node-content-wrapper.ant-tree-node-selected {
  background: 0 0;
  color: #fff;
}

[data-theme='dark'] .ant-tree.ant-tree-directory .ant-tree-treenode-selected::before,
[data-theme='dark'] .ant-tree.ant-tree-directory .ant-tree-treenode-selected:hover::before {
  background: #0960bd;
}

[data-theme='dark'] .ant-tree.ant-tree-directory .ant-tree-treenode-selected .ant-tree-switcher {
  color: #fff;
}

[data-theme='dark']
  .ant-tree.ant-tree-directory
  .ant-tree-treenode-selected
  .ant-tree-node-content-wrapper {
  background: 0 0;
  color: #fff;
}

[data-theme='dark'] .ant-tree-checkbox {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-tree-checkbox-input:focus + .ant-tree-checkbox-inner,
[data-theme='dark'] .ant-tree-checkbox-wrapper:hover .ant-tree-checkbox-inner,
[data-theme='dark'] .ant-tree-checkbox:hover .ant-tree-checkbox-inner {
  border-color: #0960bd;
}

[data-theme='dark'] .ant-tree-checkbox-checked::after {
  border: 1px solid #0960bd;
}

[data-theme='dark'] .ant-tree-checkbox-inner {
  border: 1px solid #303030;
  background-color: transparent;
}

[data-theme='dark'] .ant-tree-checkbox-inner::after {
  border: 2px solid #fff;
}

[data-theme='dark'] .ant-tree-checkbox-checked .ant-tree-checkbox-inner::after {
  border: 2px solid #fff;
}

[data-theme='dark'] .ant-tree-checkbox-checked .ant-tree-checkbox-inner {
  border-color: #0960bd;
  background-color: #0960bd;
}

[data-theme='dark']
  .ant-tree-checkbox-disabled.ant-tree-checkbox-checked
  .ant-tree-checkbox-inner::after {
  border-color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-tree-checkbox-disabled .ant-tree-checkbox-inner {
  border-color: #303030 !important;
  background-color: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-tree-checkbox-disabled .ant-tree-checkbox-inner::after {
  border-color: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-tree-checkbox-disabled + span {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-tree-checkbox-wrapper {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-tree-checkbox-group {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-tree-checkbox-indeterminate .ant-tree-checkbox-inner {
  border-color: #303030;
  background-color: transparent;
}

[data-theme='dark'] .ant-tree-checkbox-indeterminate .ant-tree-checkbox-inner::after {
  background-color: #0960bd;
}

[data-theme='dark']
  .ant-tree-checkbox-indeterminate.ant-tree-checkbox-disabled
  .ant-tree-checkbox-inner::after {
  border-color: rgb(255 255 255 / 30%);
  background-color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-tree {
  background: 0 0;
  color: #c9d1d9;
}

[data-theme='dark'] .ant-tree-focused:not(:hover):not(.ant-tree-active-focused) {
  background: rgb(255 255 255 / 8%);
}

[data-theme='dark']
  .ant-tree.ant-tree-block-node
  .ant-tree-list-holder-inner
  .ant-tree-treenode.dragging::after {
  border: 1px solid #0960bd;
}

[data-theme='dark'] .ant-tree .ant-tree-treenode-disabled .ant-tree-node-content-wrapper {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-tree .ant-tree-treenode-disabled .ant-tree-node-content-wrapper:hover {
  background: 0 0;
}

[data-theme='dark'] .ant-tree .ant-tree-treenode-active .ant-tree-node-content-wrapper {
  background: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-tree-switcher-loading-icon {
  color: #0960bd;
}

[data-theme='dark'] .ant-tree-switcher-leaf-line::before {
  border-right: 1px solid #d9d9d9;
}

[data-theme='dark'] .ant-tree-switcher-leaf-line::after {
  border-bottom: 1px solid #d9d9d9;
}

[data-theme='dark'] .ant-tree .ant-tree-node-content-wrapper {
  background: 0 0;
}

[data-theme='dark'] .ant-tree .ant-tree-node-content-wrapper:hover {
  background-color: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected {
  background-color: #11263c;
}

[data-theme='dark'] .ant-tree-unselectable .ant-tree-node-content-wrapper:hover {
  background-color: transparent;
}

[data-theme='dark'] .ant-tree-node-content-wrapper .ant-tree-drop-indicator {
  background-color: #0960bd;
}

[data-theme='dark'] .ant-tree-node-content-wrapper .ant-tree-drop-indicator::after {
  border: 2px solid #0960bd;
  background-color: transparent;
}

[data-theme='dark'] .ant-tree .ant-tree-treenode.drop-container > [draggable] {
  box-shadow: 0 0 0 2px #0960bd;
}

[data-theme='dark'] .ant-tree-show-line .ant-tree-indent-unit::before {
  border-right: 1px solid #303030;
}

[data-theme='dark'] .ant-tree-show-line .ant-tree-switcher {
  background: #151515;
}

[data-theme='dark'] .ant-tree-rtl.ant-tree-show-line .ant-tree-indent-unit::before {
  border-left: 1px solid #303030;
}

[data-theme='dark'] .ant-upload {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-upload.ant-upload-select-picture-card {
  border: 1px dashed #303030;
  background-color: rgb(255 255 255 / 4%);
}

[data-theme='dark'] .ant-upload.ant-upload-select-picture-card:hover {
  border-color: #0960bd;
}

[data-theme='dark'] .ant-upload-disabled.ant-upload.ant-upload-select-picture-card:hover {
  border-color: #303030;
}

[data-theme='dark'] .ant-upload.ant-upload-drag {
  border: 1px dashed #303030;
  background: rgb(255 255 255 / 4%);
}

[data-theme='dark'] .ant-upload.ant-upload-drag.ant-upload-drag-hover:not(.ant-upload-disabled) {
  border-color: #004496;
}

[data-theme='dark'] .ant-upload.ant-upload-drag:not(.ant-upload-disabled):hover {
  border-color: #2a7dc9;
}

[data-theme='dark'] .ant-upload.ant-upload-drag p.ant-upload-drag-icon .anticon {
  color: #2a7dc9;
}

[data-theme='dark'] .ant-upload.ant-upload-drag p.ant-upload-text {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .ant-upload.ant-upload-drag p.ant-upload-hint {
  color: #8b949e;
}

[data-theme='dark'] .ant-upload.ant-upload-drag .anticon-plus {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-upload.ant-upload-drag .anticon-plus:hover {
  color: #8b949e;
}

[data-theme='dark'] .ant-upload.ant-upload-drag:hover .anticon-plus {
  color: #8b949e;
}

[data-theme='dark'] .ant-upload-list {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-upload-list-item-card-actions .anticon {
  color: #8b949e;
}

[data-theme='dark'] .ant-upload-list-item-info .ant-upload-text-icon .anticon,
[data-theme='dark'] .ant-upload-list-item-info .anticon-loading .anticon {
  color: #8b949e;
}

[data-theme='dark'] .ant-upload-list-item .anticon-close {
  color: #8b949e;
}

[data-theme='dark'] .ant-upload-list-item .anticon-close:hover {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-upload-list-item:hover .ant-upload-list-item-info {
  background-color: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-upload-list-item-error,
[data-theme='dark'] .ant-upload-list-item-error .ant-upload-list-item-name,
[data-theme='dark'] .ant-upload-list-item-error .ant-upload-text-icon > .anticon {
  color: #ed6f6f;
}

[data-theme='dark'] .ant-upload-list-item-error .ant-upload-list-item-card-actions .anticon {
  color: #ed6f6f;
}

[data-theme='dark'] .ant-upload-list-picture .ant-upload-list-item,
[data-theme='dark'] .ant-upload-list-picture-card .ant-upload-list-item {
  border: 1px solid #303030;
}

[data-theme='dark'] .ant-upload-list-picture .ant-upload-list-item:hover,
[data-theme='dark'] .ant-upload-list-picture-card .ant-upload-list-item:hover {
  background: 0 0;
}

[data-theme='dark'] .ant-upload-list-picture .ant-upload-list-item-error,
[data-theme='dark'] .ant-upload-list-picture-card .ant-upload-list-item-error {
  border-color: #ed6f6f;
}

[data-theme='dark'] .ant-upload-list-picture .ant-upload-list-item:hover .ant-upload-list-item-info,
[data-theme='dark']
  .ant-upload-list-picture-card
  .ant-upload-list-item:hover
  .ant-upload-list-item-info {
  background: 0 0;
}

[data-theme='dark']
  .ant-upload-list-picture
  .ant-upload-list-item-error
  .ant-upload-list-item-thumbnail
  .anticon
  svg
  path[fill='#e6f7ff'],
[data-theme='dark']
  .ant-upload-list-picture-card
  .ant-upload-list-item-error
  .ant-upload-list-item-thumbnail
  .anticon
  svg
  path[fill='#e6f7ff'] {
  fill: #2b1316;
}

[data-theme='dark']
  .ant-upload-list-picture
  .ant-upload-list-item-error
  .ant-upload-list-item-thumbnail
  .anticon
  svg
  path[fill='#1890ff'],
[data-theme='dark']
  .ant-upload-list-picture-card
  .ant-upload-list-item-error
  .ant-upload-list-item-thumbnail
  .anticon
  svg
  path[fill='#1890ff'] {
  fill: #ed6f6f;
}

[data-theme='dark'] .ant-upload-list-picture-card .ant-upload-list-item-info::before {
  background-color: rgb(0 0 0 / 50%);
}

[data-theme='dark'] .ant-upload-list-picture-card .ant-upload-list-item-actions .anticon-delete,
[data-theme='dark'] .ant-upload-list-picture-card .ant-upload-list-item-actions .anticon-download,
[data-theme='dark'] .ant-upload-list-picture-card .ant-upload-list-item-actions .anticon-eye {
  color: rgb(255 255 255 / 85%);
}

[data-theme='dark']
  .ant-upload-list-picture-card
  .ant-upload-list-item-actions
  .anticon-delete:hover,
[data-theme='dark']
  .ant-upload-list-picture-card
  .ant-upload-list-item-actions
  .anticon-download:hover,
[data-theme='dark'] .ant-upload-list-picture-card .ant-upload-list-item-actions .anticon-eye:hover {
  color: #fff;
}

[data-theme='dark']
  .ant-upload-list-picture-card
  .ant-upload-list-item-uploading.ant-upload-list-item {
  background-color: rgb(255 255 255 / 4%);
}

[data-theme='dark'] .ant-layout {
  background: #000;
}

[data-theme='dark'] .ant-layout-header {
  background: #1f1f1f;
  color: #c9d1d9;
}

[data-theme='dark'] .ant-layout-footer {
  background: #000;
  color: #c9d1d9;
}

[data-theme='dark'] .ant-layout-sider {
  background: #1f1f1f;
}

[data-theme='dark'] .ant-layout-sider-trigger {
  background: #262626;
  color: #fff;
}

[data-theme='dark'] .ant-layout-sider-zero-width-trigger {
  background: #1f1f1f;
  color: #fff;
}

[data-theme='dark'] .ant-layout-sider-zero-width-trigger::after {
  background: 0 0;
}

[data-theme='dark'] .ant-layout-sider-zero-width-trigger:hover::after {
  background: rgb(255 255 255 / 10%);
}

[data-theme='dark'] .ant-layout-sider-light {
  background: #fff;
}

[data-theme='dark'] .ant-layout-sider-light .ant-layout-sider-trigger {
  background: #fff;
  color: #c9d1d9;
}

[data-theme='dark'] .ant-layout-sider-light .ant-layout-sider-zero-width-trigger {
  background: #fff;
  color: #c9d1d9;
}

[data-theme='dark'] .ant-anchor {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-anchor-wrapper {
  background-color: transparent;
}

[data-theme='dark'] .ant-anchor-ink::before {
  background-color: #303030;
}

[data-theme='dark'] .ant-anchor-ink-ball {
  border: 2px solid #0960bd;
  background-color: #151515;
}

[data-theme='dark'] .ant-anchor-link-title {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-anchor-link-active > .ant-anchor-link-title {
  color: #0960bd;
}

[data-theme='dark'] .ant-comment {
  background-color: transparent;
}

[data-theme='dark'] .ant-comment-content-author-name {
  color: #8b949e;
}

[data-theme='dark'] .ant-comment-content-author-name > * {
  color: #8b949e;
}

[data-theme='dark'] .ant-comment-content-author-name > :hover {
  color: #8b949e;
}

[data-theme='dark'] .ant-comment-content-author-time {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-comment-actions > li {
  color: #8b949e;
}

[data-theme='dark'] .ant-comment-actions > li > span {
  color: #8b949e;
}

[data-theme='dark'] .ant-comment-actions > li > span:hover {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .ant-drawer-title {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .ant-drawer-content {
  background-color: #1f1f1f;
}

[data-theme='dark'] .ant-drawer-close {
  background: 0 0;
  color: #8b949e;
}

[data-theme='dark'] .ant-drawer-close:focus,
[data-theme='dark'] .ant-drawer-close:hover {
  color: rgb(255 255 255 / 75%);
}

[data-theme='dark'] .ant-drawer-header {
  border-bottom: 1px solid #303030;
  background: #1f1f1f;
  color: #c9d1d9;
}

[data-theme='dark'] .ant-drawer-footer {
  border-top: 1px solid #303030;
}

[data-theme='dark'] .ant-drawer-mask {
  background-color: rgb(0 0 0 / 45%);
}

[data-theme='dark'] .ant-drawer .ant-picker-clear {
  background: #1f1f1f;
}

[data-theme='dark'] .ant-drawer .ant-anchor-wrapper,
[data-theme='dark'] .ant-drawer .ant-card,
[data-theme='dark'] .ant-drawer .ant-collapse-content,
[data-theme='dark'] .ant-drawer .ant-picker-clear,
[data-theme='dark'] .ant-drawer .ant-slider-handle,
[data-theme='dark'] .ant-drawer .ant-timeline-item-head {
  background-color: #1f1f1f;
}

[data-theme='dark'] .ant-drawer .ant-transfer-list-header {
  border-bottom: 1px solid #3a3a3a;
  background: #1f1f1f;
}

[data-theme='dark']
  .ant-drawer
  .ant-transfer-list-content-item:not(.ant-transfer-list-content-item-disabled):hover {
  background-color: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-drawer tr.ant-table-expanded-row:hover > td,
[data-theme='dark'] .ant-drawer tr.ant-table-expanded-row > td {
  background: #272727;
}

[data-theme='dark'] .ant-drawer .ant-table.ant-table-small thead > tr > th {
  border-bottom: 1px solid #3a3a3a;
  background-color: #1f1f1f;
}

[data-theme='dark'] .ant-drawer .ant-table {
  background-color: #1f1f1f;
}

[data-theme='dark'] .ant-drawer .ant-table .ant-table-row-expand-icon {
  border: 1px solid #3a3a3a;
}

[data-theme='dark'] .ant-drawer .ant-table tfoot > tr > td,
[data-theme='dark'] .ant-drawer .ant-table tfoot > tr > th {
  border-bottom: 1px solid #3a3a3a;
}

[data-theme='dark'] .ant-drawer .ant-table thead > tr > th {
  border-bottom: 1px solid #3a3a3a;
  background-color: #272727;
}

[data-theme='dark'] .ant-drawer .ant-table tbody > tr > td {
  border-bottom: 1px solid #3a3a3a;
}

[data-theme='dark'] .ant-drawer .ant-table tbody > tr > td.ant-table-cell-fix-left,
[data-theme='dark'] .ant-drawer .ant-table tbody > tr > td.ant-table-cell-fix-right {
  background-color: #1f1f1f;
}

[data-theme='dark'] .ant-drawer .ant-table tbody > tr.ant-table-row:hover > td {
  background: #303030;
}

[data-theme='dark'] .ant-drawer .ant-table.ant-table-bordered .ant-table-title {
  border: 1px solid #3a3a3a;
}

[data-theme='dark'] .ant-drawer .ant-table.ant-table-bordered tbody > tr > td,
[data-theme='dark'] .ant-drawer .ant-table.ant-table-bordered tfoot > tr > td,
[data-theme='dark'] .ant-drawer .ant-table.ant-table-bordered tfoot > tr > th,
[data-theme='dark'] .ant-drawer .ant-table.ant-table-bordered thead > tr > th {
  border-right: 1px solid #3a3a3a;
}

[data-theme='dark']
  .ant-drawer
  .ant-table.ant-table-bordered
  .ant-table-cell-fix-right-first::after {
  border-right: 1px solid #3a3a3a;
}

[data-theme='dark']
  .ant-drawer
  .ant-table.ant-table-bordered
  table
  thead
  > tr:not(:last-child)
  > th {
  border-bottom: 1px solid #303030;
}

[data-theme='dark'] .ant-drawer .ant-table.ant-table-bordered .ant-table-container {
  border: 1px solid #3a3a3a;
}

[data-theme='dark'] .ant-drawer .ant-table.ant-table-bordered .ant-table-expanded-row-fixed::after {
  border-right: 1px solid #3a3a3a;
}

[data-theme='dark'] .ant-drawer .ant-table.ant-table-bordered .ant-table-footer {
  border: 1px solid #3a3a3a;
}

[data-theme='dark'] .ant-drawer .ant-table .ant-table-filter-trigger-container-open {
  background-color: #525252;
}

[data-theme='dark'] .ant-drawer .ant-picker-calendar-full {
  background-color: #1f1f1f;
}

[data-theme='dark'] .ant-drawer .ant-picker-calendar-full .ant-picker-panel {
  background-color: #1f1f1f;
}

[data-theme='dark']
  .ant-drawer
  .ant-picker-calendar-full
  .ant-picker-panel
  .ant-picker-calendar-date {
  border-top: 2px solid #3a3a3a;
}

[data-theme='dark'] .ant-drawer .ant-tabs.ant-tabs-card .ant-tabs-card-bar .ant-tabs-tab-active {
  border-bottom: 1px solid #1f1f1f;
  background-color: #1f1f1f;
}

[data-theme='dark'] .ant-drawer .ant-badge-count {
  box-shadow: 0 0 0 1px #1f1f1f;
}

[data-theme='dark'] .ant-drawer .ant-tree-show-line .ant-tree-switcher {
  background: #1f1f1f;
}

[data-theme='dark'] .ant-empty-normal {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-empty-small {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-empty-img-default-ellipse {
  fill: #fff;
}

[data-theme='dark'] .ant-empty-img-default-path-1 {
  fill: #262626;
}

[data-theme='dark'] .ant-empty-img-default-path-3 {
  fill: #595959;
}

[data-theme='dark'] .ant-empty-img-default-path-4 {
  fill: #434343;
}

[data-theme='dark'] .ant-empty-img-default-path-5 {
  fill: #595959;
}

[data-theme='dark'] .ant-empty-img-default-g {
  fill: #434343;
}

[data-theme='dark'] .ant-empty-img-simple-ellipse {
  fill: #fff;
}

[data-theme='dark'] .ant-empty-img-simple-g {
  stroke: #434343;
}

[data-theme='dark'] .ant-empty-img-simple-path {
  stroke: #434343;
  fill: #262626;
}

[data-theme='dark'] .ant-statistic {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-statistic-title {
  color: #8b949e;
}

[data-theme='dark'] .ant-statistic-content {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .ant-result-success .ant-result-icon > .anticon {
  color: #55d187;
}

[data-theme='dark'] .ant-result-error .ant-result-icon > .anticon {
  color: #ed6f6f;
}

[data-theme='dark'] .ant-result-info .ant-result-icon > .anticon {
  color: #0960bd;
}

[data-theme='dark'] .ant-result-warning .ant-result-icon > .anticon {
  color: #efbd47;
}

[data-theme='dark'] .ant-result-title {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .ant-result-subtitle {
  color: #8b949e;
}

[data-theme='dark'] .ant-result-content {
  background-color: rgb(255 255 255 / 4%);
}

[data-theme='dark'] .ant-list .ant-card {
  background: 0 0;
}

[data-theme='dark'] .ant-list {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-list-empty-text {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-list-item {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-list-item-meta-content {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-list-item-meta-title {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-list-item-meta-title > a {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-list-item-meta-title > a:hover {
  color: #0960bd;
}

[data-theme='dark'] .ant-list-item-meta-description {
  color: #8b949e;
}

[data-theme='dark'] .ant-list-item-action > li {
  color: #8b949e;
}

[data-theme='dark'] .ant-list-item-action-split {
  background-color: #303030;
}

[data-theme='dark'] .ant-list-header {
  background: 0 0;
}

[data-theme='dark'] .ant-list-footer {
  background: 0 0;
}

[data-theme='dark'] .ant-list-empty {
  color: #8b949e;
}

[data-theme='dark'] .ant-list-split .ant-list-item {
  border-bottom: 1px solid #303030;
}

[data-theme='dark'] .ant-list-split .ant-list-header {
  border-bottom: 1px solid #303030;
}

[data-theme='dark'] .ant-list-split.ant-list-empty .ant-list-footer {
  border-top: 1px solid #303030;
}

[data-theme='dark']
  .ant-list-split.ant-list-something-after-last-item
  .ant-spin-container
  > .ant-list-items
  > .ant-list-item:last-child {
  border-bottom: 1px solid #303030;
}

[data-theme='dark'] .ant-list-vertical .ant-list-item-meta-title {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .ant-list-bordered {
  border: 1px solid #303030;
}

[data-theme='dark'] .ant-page-header {
  background-color: #151515;
  color: #c9d1d9;
}

[data-theme='dark'] .ant-page-header-ghost {
  background-color: transparent;
}

[data-theme='dark'] .ant-page-header-back-button {
  color: #0960bd;
}

[data-theme='dark'] .ant-page-header-back-button:focus,
[data-theme='dark'] .ant-page-header-back-button:hover {
  color: #2a7dc9;
}

[data-theme='dark'] .ant-page-header-back-button:active {
  color: #004496;
}

[data-theme='dark'] .ant-page-header-heading-title {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .ant-page-header-heading-sub-title {
  color: #8b949e;
}

[data-theme='dark'] .ant-form-item .ant-upload {
  background: 0 0;
}

[data-theme='dark'] .ant-form-item .ant-upload.ant-upload-drag {
  background: rgb(255 255 255 / 4%);
}

[data-theme='dark'] .ant-form-item-explain-error {
  color: #ed6f6f;
}

[data-theme='dark'] .ant-form-item-explain-warning {
  color: #efbd47;
}

[data-theme='dark']
  .ant-form-item-has-success.ant-form-item-has-feedback
  .ant-form-item-children-icon {
  color: #55d187;
}

[data-theme='dark'] .ant-form-item-has-warning .ant-form-item-split {
  color: #efbd47;
}

[data-theme='dark']
  .ant-form-item-has-warning
  :not(.ant-input-affix-wrapper-disabled):not(
    .ant-input-affix-wrapper-borderless
  ).ant-input-affix-wrapper,
[data-theme='dark']
  .ant-form-item-has-warning
  :not(.ant-input-affix-wrapper-disabled):not(
    .ant-input-affix-wrapper-borderless
  ).ant-input-affix-wrapper:hover,
[data-theme='dark']
  .ant-form-item-has-warning
  :not(.ant-input-disabled):not(.ant-input-borderless).ant-input,
[data-theme='dark']
  .ant-form-item-has-warning
  :not(.ant-input-disabled):not(.ant-input-borderless).ant-input:hover,
[data-theme='dark']
  .ant-form-item-has-warning
  :not(.ant-input-number-affix-wrapper-disabled):not(
    .ant-input-number-affix-wrapper-borderless
  ).ant-input-number-affix-wrapper,
[data-theme='dark']
  .ant-form-item-has-warning
  :not(.ant-input-number-affix-wrapper-disabled):not(
    .ant-input-number-affix-wrapper-borderless
  ).ant-input-number-affix-wrapper:hover {
  border-color: #efbd47;
  background-color: transparent;
}

[data-theme='dark']
  .ant-form-item-has-warning
  :not(.ant-input-affix-wrapper-disabled):not(
    .ant-input-affix-wrapper-borderless
  ).ant-input-affix-wrapper-focused,
[data-theme='dark']
  .ant-form-item-has-warning
  :not(.ant-input-affix-wrapper-disabled):not(
    .ant-input-affix-wrapper-borderless
  ).ant-input-affix-wrapper:focus,
[data-theme='dark']
  .ant-form-item-has-warning
  :not(.ant-input-disabled):not(.ant-input-borderless).ant-input-focused,
[data-theme='dark']
  .ant-form-item-has-warning
  :not(.ant-input-disabled):not(.ant-input-borderless).ant-input:focus,
[data-theme='dark']
  .ant-form-item-has-warning
  :not(.ant-input-number-affix-wrapper-disabled):not(
    .ant-input-number-affix-wrapper-borderless
  ).ant-input-number-affix-wrapper-focused,
[data-theme='dark']
  .ant-form-item-has-warning
  :not(.ant-input-number-affix-wrapper-disabled):not(
    .ant-input-number-affix-wrapper-borderless
  ).ant-input-number-affix-wrapper:focus {
  border-color: #efbd47;
  box-shadow: 0 0 0 2px rgb(239 189 71 / 20%);
}

[data-theme='dark']
  .ant-form-item-has-warning
  .ant-calendar-picker-open
  .ant-calendar-picker-input {
  border-color: #efbd47;
  box-shadow: 0 0 0 2px rgb(239 189 71 / 20%);
}

[data-theme='dark'] .ant-form-item-has-warning .ant-input-number-prefix,
[data-theme='dark'] .ant-form-item-has-warning .ant-input-prefix {
  color: #efbd47;
}

[data-theme='dark'] .ant-form-item-has-warning .ant-input-group-addon,
[data-theme='dark'] .ant-form-item-has-warning .ant-input-number-group-addon {
  border-color: #efbd47;
  color: #efbd47;
}

[data-theme='dark'] .ant-form-item-has-warning .has-feedback {
  color: #efbd47;
}

[data-theme='dark']
  .ant-form-item-has-warning.ant-form-item-has-feedback
  .ant-form-item-children-icon {
  color: #efbd47;
}

[data-theme='dark']
  .ant-form-item-has-warning
  .ant-select:not(.ant-select-disabled):not(.ant-select-customize-input)
  .ant-select-selector {
  border-color: #efbd47 !important;
  background-color: transparent;
}

[data-theme='dark']
  .ant-form-item-has-warning
  .ant-select:not(.ant-select-disabled):not(.ant-select-customize-input).ant-select-focused
  .ant-select-selector,
[data-theme='dark']
  .ant-form-item-has-warning
  .ant-select:not(.ant-select-disabled):not(.ant-select-customize-input).ant-select-open
  .ant-select-selector {
  border-color: #efbd47;
  box-shadow: 0 0 0 2px rgb(239 189 71 / 20%);
}

[data-theme='dark'] .ant-form-item-has-warning .ant-input-number,
[data-theme='dark'] .ant-form-item-has-warning .ant-picker {
  border-color: #efbd47;
  background-color: transparent;
}

[data-theme='dark'] .ant-form-item-has-warning .ant-input-number-focused,
[data-theme='dark'] .ant-form-item-has-warning .ant-input-number:focus,
[data-theme='dark'] .ant-form-item-has-warning .ant-picker-focused,
[data-theme='dark'] .ant-form-item-has-warning .ant-picker:focus {
  border-color: #efbd47;
  box-shadow: 0 0 0 2px rgb(239 189 71 / 20%);
}

[data-theme='dark'] .ant-form-item-has-warning .ant-input-number:not([disabled]):hover,
[data-theme='dark'] .ant-form-item-has-warning .ant-picker:not([disabled]):hover {
  border-color: #efbd47;
  background-color: transparent;
}

[data-theme='dark'] .ant-form-item-has-warning .ant-cascader-picker:focus .ant-cascader-input {
  border-color: #efbd47;
  box-shadow: 0 0 0 2px rgb(239 189 71 / 20%);
}

[data-theme='dark'] .ant-form-item-has-error .ant-form-item-split {
  color: #ed6f6f;
}

[data-theme='dark']
  .ant-form-item-has-error
  :not(.ant-input-affix-wrapper-disabled):not(
    .ant-input-affix-wrapper-borderless
  ).ant-input-affix-wrapper,
[data-theme='dark']
  .ant-form-item-has-error
  :not(.ant-input-affix-wrapper-disabled):not(
    .ant-input-affix-wrapper-borderless
  ).ant-input-affix-wrapper:hover,
[data-theme='dark']
  .ant-form-item-has-error
  :not(.ant-input-disabled):not(.ant-input-borderless).ant-input,
[data-theme='dark']
  .ant-form-item-has-error
  :not(.ant-input-disabled):not(.ant-input-borderless).ant-input:hover,
[data-theme='dark']
  .ant-form-item-has-error
  :not(.ant-input-number-affix-wrapper-disabled):not(
    .ant-input-number-affix-wrapper-borderless
  ).ant-input-number-affix-wrapper,
[data-theme='dark']
  .ant-form-item-has-error
  :not(.ant-input-number-affix-wrapper-disabled):not(
    .ant-input-number-affix-wrapper-borderless
  ).ant-input-number-affix-wrapper:hover {
  border-color: #ed6f6f;
  background-color: transparent;
}

[data-theme='dark']
  .ant-form-item-has-error
  :not(.ant-input-affix-wrapper-disabled):not(
    .ant-input-affix-wrapper-borderless
  ).ant-input-affix-wrapper-focused,
[data-theme='dark']
  .ant-form-item-has-error
  :not(.ant-input-affix-wrapper-disabled):not(
    .ant-input-affix-wrapper-borderless
  ).ant-input-affix-wrapper:focus,
[data-theme='dark']
  .ant-form-item-has-error
  :not(.ant-input-disabled):not(.ant-input-borderless).ant-input-focused,
[data-theme='dark']
  .ant-form-item-has-error
  :not(.ant-input-disabled):not(.ant-input-borderless).ant-input:focus,
[data-theme='dark']
  .ant-form-item-has-error
  :not(.ant-input-number-affix-wrapper-disabled):not(
    .ant-input-number-affix-wrapper-borderless
  ).ant-input-number-affix-wrapper-focused,
[data-theme='dark']
  .ant-form-item-has-error
  :not(.ant-input-number-affix-wrapper-disabled):not(
    .ant-input-number-affix-wrapper-borderless
  ).ant-input-number-affix-wrapper:focus {
  border-color: #ed6f6f;
  box-shadow: 0 0 0 2px rgb(237 111 111 / 20%);
}

[data-theme='dark'] .ant-form-item-has-error .ant-calendar-picker-open .ant-calendar-picker-input {
  border-color: #ed6f6f;
  box-shadow: 0 0 0 2px rgb(237 111 111 / 20%);
}

[data-theme='dark'] .ant-form-item-has-error .ant-input-number-prefix,
[data-theme='dark'] .ant-form-item-has-error .ant-input-prefix {
  color: #ed6f6f;
}

[data-theme='dark'] .ant-form-item-has-error .ant-input-group-addon,
[data-theme='dark'] .ant-form-item-has-error .ant-input-number-group-addon {
  border-color: #ed6f6f;
  color: #ed6f6f;
}

[data-theme='dark'] .ant-form-item-has-error .has-feedback {
  color: #ed6f6f;
}

[data-theme='dark']
  .ant-form-item-has-error.ant-form-item-has-feedback
  .ant-form-item-children-icon {
  color: #ed6f6f;
}

[data-theme='dark']
  .ant-form-item-has-error
  .ant-select:not(.ant-select-disabled):not(.ant-select-customize-input)
  .ant-select-selector {
  border-color: #ed6f6f !important;
  background-color: transparent;
}

[data-theme='dark']
  .ant-form-item-has-error
  .ant-select:not(.ant-select-disabled):not(.ant-select-customize-input).ant-select-focused
  .ant-select-selector,
[data-theme='dark']
  .ant-form-item-has-error
  .ant-select:not(.ant-select-disabled):not(.ant-select-customize-input).ant-select-open
  .ant-select-selector {
  border-color: #ed6f6f;
  box-shadow: 0 0 0 2px rgb(237 111 111 / 20%);
}

[data-theme='dark'] .ant-form-item-has-error .ant-select.ant-select-auto-complete .ant-input:focus {
  border-color: #ed6f6f;
}

[data-theme='dark'] .ant-form-item-has-error .ant-input-number,
[data-theme='dark'] .ant-form-item-has-error .ant-picker {
  border-color: #ed6f6f;
  background-color: transparent;
}

[data-theme='dark'] .ant-form-item-has-error .ant-input-number-focused,
[data-theme='dark'] .ant-form-item-has-error .ant-input-number:focus,
[data-theme='dark'] .ant-form-item-has-error .ant-picker-focused,
[data-theme='dark'] .ant-form-item-has-error .ant-picker:focus {
  border-color: #ed6f6f;
  box-shadow: 0 0 0 2px rgb(237 111 111 / 20%);
}

[data-theme='dark'] .ant-form-item-has-error .ant-input-number:not([disabled]):hover,
[data-theme='dark'] .ant-form-item-has-error .ant-picker:not([disabled]):hover {
  border-color: #ed6f6f;
  background-color: transparent;
}

[data-theme='dark'] .ant-form-item-has-error .ant-mention-wrapper .ant-mention-editor,
[data-theme='dark']
  .ant-form-item-has-error
  .ant-mention-wrapper
  .ant-mention-editor:not([disabled]):hover {
  border-color: #ed6f6f;
  background-color: transparent;
}

[data-theme='dark']
  .ant-form-item-has-error
  .ant-mention-wrapper
  .ant-mention-editor:not([disabled]):focus,
[data-theme='dark']
  .ant-form-item-has-error
  .ant-mention-wrapper.ant-mention-active:not([disabled])
  .ant-mention-editor {
  border-color: #ed6f6f;
  box-shadow: 0 0 0 2px rgb(237 111 111 / 20%);
}

[data-theme='dark']
  .ant-form-item-has-error
  .ant-cascader-picker:hover
  .ant-cascader-picker-label:hover
  + .ant-cascader-input.ant-input {
  border-color: #ed6f6f;
}

[data-theme='dark'] .ant-form-item-has-error .ant-cascader-picker:focus .ant-cascader-input {
  border-color: #ed6f6f;
  background-color: transparent;
  box-shadow: 0 0 0 2px rgb(237 111 111 / 20%);
}

[data-theme='dark'] .ant-form-item-has-error .ant-transfer-list {
  border-color: #ed6f6f;
}

[data-theme='dark'] .ant-form-item-has-error .ant-transfer-list-search:not([disabled]) {
  border-color: #303030;
}

[data-theme='dark'] .ant-form-item-has-error .ant-transfer-list-search:not([disabled]):hover {
  border-color: #2a7dc9;
}

[data-theme='dark'] .ant-form-item-has-error .ant-transfer-list-search:not([disabled]):focus {
  border-color: #0960bd;
  box-shadow: 0 0 0 2px rgb(9 96 189 / 20%);
}

[data-theme='dark'] .ant-form-item-has-error .ant-radio-button-wrapper {
  border-color: #ed6f6f !important;
}

[data-theme='dark'] .ant-form-item-has-error .ant-radio-button-wrapper:not(:first-child)::before {
  background-color: #ed6f6f;
}

[data-theme='dark'] .ant-form-item-has-error .ant-mentions {
  border-color: #ed6f6f !important;
}

[data-theme='dark'] .ant-form-item-has-error .ant-mentions-focused,
[data-theme='dark'] .ant-form-item-has-error .ant-mentions:focus {
  border-color: #ed6f6f;
  box-shadow: 0 0 0 2px rgb(237 111 111 / 20%);
}

[data-theme='dark']
  .ant-form-item-is-validating.ant-form-item-has-feedback
  .ant-form-item-children-icon {
  color: #0960bd;
}

[data-theme='dark'] .ant-form {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-form legend {
  border-bottom: 1px solid #303030;
  color: #8b949e;
}

[data-theme='dark'] .ant-form output {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-form-item {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-form-item-label > label {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark']
  .ant-form-item-label
  > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
  color: #a71d25;
}

[data-theme='dark'] .ant-form-item-label > label .ant-form-item-optional {
  color: #8b949e;
}

[data-theme='dark'] .ant-form-item-label > label .ant-form-item-tooltip {
  color: #8b949e;
}

[data-theme='dark'] .ant-form-item-explain,
[data-theme='dark'] .ant-form-item-extra {
  color: #8b949e;
}

[data-theme='dark'] .ant-skeleton-header .ant-skeleton-avatar {
  background: rgb(190 190 190 / 20%);
}

[data-theme='dark'] .ant-skeleton-content .ant-skeleton-title {
  background: rgb(190 190 190 / 20%);
}

[data-theme='dark'] .ant-skeleton-content .ant-skeleton-paragraph > li {
  background: rgb(190 190 190 / 20%);
}

[data-theme='dark'] .ant-skeleton-element .ant-skeleton-button {
  background: rgb(190 190 190 / 20%);
}

[data-theme='dark'] .ant-skeleton-element .ant-skeleton-avatar {
  background: rgb(190 190 190 / 20%);
}

[data-theme='dark'] .ant-skeleton-element .ant-skeleton-input {
  background: rgb(190 190 190 / 20%);
}

[data-theme='dark'] .ant-skeleton-element .ant-skeleton-image {
  background: rgb(190 190 190 / 20%);
}

[data-theme='dark'] .ant-skeleton-element .ant-skeleton-image-path {
  fill: #bfbfbf;
}

[data-theme='dark'] .ant-image-img-placeholder {
  background-color: #f5f5f5;
}

[data-theme='dark'] .ant-image-mask {
  background: rgb(0 0 0 / 50%);
  color: #fff;
}

[data-theme='dark'] .ant-image-preview-mask {
  background-color: rgb(0 0 0 / 45%);
}

[data-theme='dark'] .ant-image-preview-operations {
  background: rgb(0 0 0 / 10%);
  color: #c9d1d9;
  color: rgb(255 255 255 / 85%);
}

[data-theme='dark'] .ant-image-preview-operations-operation-disabled {
  color: rgb(255 255 255 / 25%);
}

[data-theme='dark'] .ant-image-preview-switch-left,
[data-theme='dark'] .ant-image-preview-switch-right {
  background: rgb(0 0 0 / 10%);
  color: rgb(255 255 255 / 85%);
}

[data-theme='dark'] .ant-image-preview-switch-left-disabled,
[data-theme='dark'] .ant-image-preview-switch-right-disabled {
  color: rgb(255 255 255 / 25%);
}

[data-theme='dark'] .ant-typography {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-typography.ant-typography-secondary {
  color: #8b949e;
}

[data-theme='dark'] .ant-typography.ant-typography-success {
  color: #55d187;
}

[data-theme='dark'] .ant-typography.ant-typography-warning {
  color: #efbd47;
}

[data-theme='dark'] .ant-typography.ant-typography-danger {
  color: #ed6f6f;
}

[data-theme='dark'] a.ant-typography.ant-typography-danger:active,
[data-theme='dark'] a.ant-typography.ant-typography-danger:focus,
[data-theme='dark'] a.ant-typography.ant-typography-danger:hover {
  color: #faa19d;
}

[data-theme='dark'] .ant-typography.ant-typography-disabled {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-typography h1,
[data-theme='dark'] h1.ant-typography {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .ant-typography h2,
[data-theme='dark'] h2.ant-typography {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .ant-typography h3,
[data-theme='dark'] h3.ant-typography {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .ant-typography h4,
[data-theme='dark'] h4.ant-typography {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .ant-typography h5,
[data-theme='dark'] h5.ant-typography {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .ant-typography a,
[data-theme='dark'] a.ant-typography {
  color: #0960bd;
}

[data-theme='dark'] .ant-typography a:focus,
[data-theme='dark'] .ant-typography a:hover,
[data-theme='dark'] a.ant-typography:focus,
[data-theme='dark'] a.ant-typography:hover {
  color: #2a7dc9;
}

[data-theme='dark'] .ant-typography a:active,
[data-theme='dark'] a.ant-typography:active {
  color: #004496;
}

[data-theme='dark'] .ant-typography a.ant-typography-disabled,
[data-theme='dark'] .ant-typography a[disabled],
[data-theme='dark'] a.ant-typography.ant-typography-disabled,
[data-theme='dark'] a.ant-typography[disabled] {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-typography a.ant-typography-disabled:active,
[data-theme='dark'] .ant-typography a.ant-typography-disabled:hover,
[data-theme='dark'] .ant-typography a[disabled]:active,
[data-theme='dark'] .ant-typography a[disabled]:hover,
[data-theme='dark'] a.ant-typography.ant-typography-disabled:active,
[data-theme='dark'] a.ant-typography.ant-typography-disabled:hover,
[data-theme='dark'] a.ant-typography[disabled]:active,
[data-theme='dark'] a.ant-typography[disabled]:hover {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-typography code {
  border: 1px solid rgb(100 100 100 / 20%);
  background: rgb(150 150 150 / 10%);
}

[data-theme='dark'] .ant-typography kbd {
  border: 1px solid rgb(100 100 100 / 20%);
  background: rgb(150 150 150 / 6%);
}

[data-theme='dark'] .ant-typography mark {
  background-color: #5a4315;
}

[data-theme='dark'] .ant-typography-copy,
[data-theme='dark'] .ant-typography-edit,
[data-theme='dark'] .ant-typography-expand {
  color: #0960bd;
}

[data-theme='dark'] .ant-typography-copy:focus,
[data-theme='dark'] .ant-typography-copy:hover,
[data-theme='dark'] .ant-typography-edit:focus,
[data-theme='dark'] .ant-typography-edit:hover,
[data-theme='dark'] .ant-typography-expand:focus,
[data-theme='dark'] .ant-typography-expand:hover {
  color: #2a7dc9;
}

[data-theme='dark'] .ant-typography-copy:active,
[data-theme='dark'] .ant-typography-edit:active,
[data-theme='dark'] .ant-typography-expand:active {
  color: #004496;
}

[data-theme='dark'] .ant-typography-copy-success,
[data-theme='dark'] .ant-typography-copy-success:focus,
[data-theme='dark'] .ant-typography-copy-success:hover {
  color: #55d187;
}

[data-theme='dark'] .ant-typography-edit-content-confirm {
  color: #8b949e;
}

[data-theme='dark'] .ant-typography pre {
  border: 1px solid rgb(100 100 100 / 20%);
  background: rgb(150 150 150 / 10%);
}

[data-theme='dark'] .ant-typography pre code {
  background: 0 0;
}

[data-theme='dark'] .ant-typography blockquote {
  border-left: 4px solid rgb(100 100 100 / 20%);
}

[data-theme='dark'] .ant-descriptions-title {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .ant-descriptions-extra {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-descriptions-item-label {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .ant-descriptions-item-content {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-descriptions-bordered .ant-descriptions-view {
  border: 1px solid #303030;
}

[data-theme='dark'] .ant-descriptions-bordered .ant-descriptions-item-content,
[data-theme='dark'] .ant-descriptions-bordered .ant-descriptions-item-label {
  border-right: 1px solid #303030;
}

[data-theme='dark'] .ant-descriptions-bordered .ant-descriptions-item-label {
  background-color: rgb(255 255 255 / 4%);
}

[data-theme='dark'] .ant-descriptions-bordered .ant-descriptions-row {
  border-bottom: 1px solid #303030;
}

[data-theme='dark'] .ant-descriptions-rtl.ant-descriptions-bordered .ant-descriptions-item-content,
[data-theme='dark'] .ant-descriptions-rtl.ant-descriptions-bordered .ant-descriptions-item-label {
  border-left: 1px solid #303030;
}

[data-theme='dark'] .ant-select-tree-checkbox {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-select-tree-checkbox-input:focus + .ant-select-tree-checkbox-inner,
[data-theme='dark'] .ant-select-tree-checkbox-wrapper:hover .ant-select-tree-checkbox-inner,
[data-theme='dark'] .ant-select-tree-checkbox:hover .ant-select-tree-checkbox-inner {
  border-color: #0960bd;
}

[data-theme='dark'] .ant-select-tree-checkbox-checked::after {
  border: 1px solid #0960bd;
}

[data-theme='dark'] .ant-select-tree-checkbox-inner {
  border: 1px solid #303030;
  background-color: transparent;
}

[data-theme='dark'] .ant-select-tree-checkbox-inner::after {
  border: 2px solid #fff;
}

[data-theme='dark'] .ant-select-tree-checkbox-checked .ant-select-tree-checkbox-inner::after {
  border: 2px solid #fff;
}

[data-theme='dark'] .ant-select-tree-checkbox-checked .ant-select-tree-checkbox-inner {
  border-color: #0960bd;
  background-color: #0960bd;
}

[data-theme='dark']
  .ant-select-tree-checkbox-disabled.ant-select-tree-checkbox-checked
  .ant-select-tree-checkbox-inner::after {
  border-color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-select-tree-checkbox-disabled .ant-select-tree-checkbox-inner {
  border-color: #303030 !important;
  background-color: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-select-tree-checkbox-disabled .ant-select-tree-checkbox-inner::after {
  border-color: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-select-tree-checkbox-disabled + span {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-select-tree-checkbox-wrapper {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-select-tree-checkbox-group {
  color: #c9d1d9;
}

[data-theme='dark'] .ant-select-tree-checkbox-indeterminate .ant-select-tree-checkbox-inner {
  border-color: #303030;
  background-color: transparent;
}

[data-theme='dark'] .ant-select-tree-checkbox-indeterminate .ant-select-tree-checkbox-inner::after {
  background-color: #0960bd;
}

[data-theme='dark']
  .ant-select-tree-checkbox-indeterminate.ant-select-tree-checkbox-disabled
  .ant-select-tree-checkbox-inner::after {
  border-color: rgb(255 255 255 / 30%);
  background-color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .ant-select-tree {
  background: 0 0;
  color: #c9d1d9;
}

[data-theme='dark'] .ant-select-tree-focused:not(:hover):not(.ant-select-tree-active-focused) {
  background: rgb(255 255 255 / 8%);
}

[data-theme='dark']
  .ant-select-tree.ant-select-tree-block-node
  .ant-select-tree-list-holder-inner
  .ant-select-tree-treenode.dragging::after {
  border: 1px solid #0960bd;
}

[data-theme='dark']
  .ant-select-tree
  .ant-select-tree-treenode-disabled
  .ant-select-tree-node-content-wrapper {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark']
  .ant-select-tree
  .ant-select-tree-treenode-disabled
  .ant-select-tree-node-content-wrapper:hover {
  background: 0 0;
}

[data-theme='dark']
  .ant-select-tree
  .ant-select-tree-treenode-active
  .ant-select-tree-node-content-wrapper {
  background: rgb(255 255 255 / 8%);
}

[data-theme='dark'] .ant-select-tree-switcher-loading-icon {
  color: #0960bd;
}

[data-theme='dark'] .ant-select-tree-switcher-leaf-line::before {
  border-right: 1px solid #d9d9d9;
}

[data-theme='dark'] .ant-select-tree-switcher-leaf-line::after {
  border-bottom: 1px solid #d9d9d9;
}

[data-theme='dark'] .ant-select-tree .ant-select-tree-node-content-wrapper {
  background: 0 0;
}

[data-theme='dark'] .ant-select-tree .ant-select-tree-node-content-wrapper:hover {
  background-color: rgb(255 255 255 / 8%);
}

[data-theme='dark']
  .ant-select-tree
  .ant-select-tree-node-content-wrapper.ant-select-tree-node-selected {
  background-color: #11263c;
}

[data-theme='dark'] .ant-select-tree-unselectable .ant-select-tree-node-content-wrapper:hover {
  background-color: transparent;
}

[data-theme='dark'] .ant-select-tree-node-content-wrapper .ant-tree-drop-indicator {
  background-color: #0960bd;
}

[data-theme='dark'] .ant-select-tree-node-content-wrapper .ant-tree-drop-indicator::after {
  border: 2px solid #0960bd;
  background-color: transparent;
}

[data-theme='dark'] .ant-select-tree .ant-select-tree-treenode.drop-container > [draggable] {
  box-shadow: 0 0 0 2px #0960bd;
}

[data-theme='dark'] .ant-select-tree-show-line .ant-select-tree-indent-unit::before {
  border-right: 1px solid #303030;
}

[data-theme='dark'] .ant-select-tree-show-line .ant-select-tree-switcher {
  background: #151515;
}

[data-theme='dark'] html[data-theme='dark'] .vben-login {
  background-color: #293146;
}

[data-theme='dark'] html[data-theme='dark'] .vben-login .ant-input,
[data-theme='dark'] html[data-theme='dark'] .vben-login .ant-input-password {
  background-color: #232a3b;
}

[data-theme='dark']
  html[data-theme='dark']
  .vben-login
  .ant-btn:not(.ant-btn-link):not(.ant-btn-primary) {
  border: 1px solid #4a5569;
}

[data-theme='dark'] html[data-theme='dark'] .vben-login-form {
  background: 0 0 !important;
}

[data-theme='dark'] html[data-theme='dark'] .vben-login .app-iconify {
  color: #fff;
}

[data-theme='dark'] html[data-theme='dark'] .fix-auto-fill input,
[data-theme='dark'] html[data-theme='dark'] input.fix-auto-fill {
  -webkit-text-fill-color: #c9d1d9 !important;
}

@media (max-width: 1200px) {
  [data-theme='dark'] {
    background-color: #293146;
  }
}

[data-theme='dark'] .vben-login .vben-login-form {
  background-color: #fff;
}

[data-theme='dark'] .vben-login .vben-app-logo__title {
  color: #fff;
}

[data-theme='dark'] .vben-login .container .vben-app-logo__title {
  color: #fff;
}

[data-theme='dark'] .vben-login-sign-in-way .anticon {
  color: #888;
}

[data-theme='dark'] .vben-login-sign-in-way .anticon:hover {
  color: #0960bd;
}

[data-theme='dark'] .vben-login .ant-divider-inner-text {
  color: #8b949e;
}

[data-theme='dark'] .vben-app-logo.light {
  border-bottom: 1px solid #303030;
}

[data-theme='dark'] .vben-app-logo.light .vben-app-logo__title {
  color: #0960bd;
}

[data-theme='dark'] .vben-app-logo.dark .vben-app-logo__title {
  color: #fff;
}

[data-theme='dark'] html[data-theme='dark'] .vben-dark-switch {
  border: 1px solid #c4bcbc;
}

[data-theme='dark'] .vben-dark-switch {
  background-color: #151515;
}

[data-theme='dark'] .vben-dark-switch-inner {
  background-color: #fff;
}

[data-theme='dark'] .scroll-wrap {
  background-color: #151515;
}

[data-theme='dark'] .virtual-scroll-demo-wrap {
  background-color: #151515;
}

[data-theme='dark'] .virtual-scroll-demo__item {
  border-bottom: 1px solid #303030;
}

[data-theme='dark'] .lazy-base-demo-wrap {
  background-color: #151515;
}

[data-theme='dark'] .demo-wrap {
  background-color: #151515;
}

[data-theme='dark'] .form-wrap {
  background-color: #151515;
}

[data-theme='dark'] .full-loading {
  background-color: rgb(240 242 245 / 40%);
}

[data-theme='dark'] html[data-theme='dark'] .full-loading:not(.light) {
  background-color: rgb(0 0 0 / 45%);
}

[data-theme='dark'] .full-loading.dark {
  background-color: rgb(0 0 0 / 45%);
}

[data-theme='dark'] .step-form-content {
  background-color: #151515;
}

[data-theme='dark'] .desc-wrap {
  background-color: #151515 !important;
}

[data-theme='dark'] .result-success {
  background-color: #151515;
}

[data-theme='dark'] .result-success__content {
  background-color: rgb(255 255 255 / 4%);
}

[data-theme='dark'] .result-error {
  background-color: #151515;
}

[data-theme='dark'] .result-error__content {
  background-color: rgb(255 255 255 / 4%);
}

[data-theme='dark'] .result-error__content-icon {
  color: #ff4d4f;
}

[data-theme='dark'] .account-center-col:not(:last-child):not(:last-child) {
  border-right: 1px dashed rgb(206 206 206 / 50%);
}

[data-theme='dark'] .account-center-top {
  background-color: #151515;
}

[data-theme='dark'] .account-center-bottom {
  background-color: #151515;
}

[data-theme='dark'] .account-setting {
  background-color: #151515;
}

[data-theme='dark'] .account-setting .ant-tabs-tab-active {
  background-color: #111b26;
}

[data-theme='dark'] .list-card__card-title {
  color: #c9d1d9;
}

[data-theme='dark'] .list-card__card-detail {
  color: #8b949e;
}

[data-theme='dark'] .demo {
  background-color: #151515;
}

[data-theme='dark'] .list-basic__top {
  background-color: #151515;
}

[data-theme='dark'] .list-basic__top-col:not(:last-child) {
  border-right: 1px dashed #303030;
}

[data-theme='dark'] .list-basic__top-col div {
  color: #c9d1d9;
}

[data-theme='dark'] .list-basic__top-col p {
  color: #c9d1d9;
}

[data-theme='dark'] .list-basic__content {
  background-color: #151515;
}

[data-theme='dark'] .list-basic__content .extra {
  color: #0960bd;
}

[data-theme='dark'] .list-search__container {
  background-color: #151515;
}

[data-theme='dark'] .list-search__content {
  color: #8b949e;
}

[data-theme='dark'] .list-search__action-item {
  color: #8b949e;
}

[data-theme='dark'] .list-search__action-item:first-child,
[data-theme='dark'] .list-search__action-item:nth-child(2) {
  border-right: 1px solid #303030;
}

[data-theme='dark'] .list-search__time {
  color: rgb(0 0 0 / 45%);
}

[data-theme='dark'] span.iconify {
  background-color: #555;
}

[data-theme='dark'] .vben-default-layout {
  background-color: #f4f7f9;
}

[data-theme='dark'] .vben-basic-table-header-cell__help {
  color: rgb(0 0 0 / 65%) !important;
}

[data-theme='dark'] .ant-modal-confirm-body .ant-modal-confirm-content > * {
  color: #909399;
}

[data-theme='dark'] .ant-modal-confirm-confirm.error .ant-modal-confirm-body > .anticon {
  color: #ed6f6f;
}

[data-theme='dark'] .ant-modal-confirm-info .ant-modal-confirm-body > .anticon {
  color: #efbd47;
}

[data-theme='dark'] .ant-modal-confirm-confirm.success .ant-modal-confirm-body > .anticon {
  color: #55d187;
}

[data-theme='dark'] .step1 h3 {
  color: #c9d1d9;
}

[data-theme='dark'] .step1 h4 {
  color: #c9d1d9;
}

[data-theme='dark'] .step1 p {
  color: #c9d1d9;
}

[data-theme='dark'] .vben-app-search-modal {
  background-color: rgb(0 0 0 / 25%);
}

[data-theme='dark'] .vben-app-search-modal-content {
  background-color: #151515;
}

[data-theme='dark'] .vben-app-search-modal-input {
  color: #1c1e21;
}

[data-theme='dark'] .vben-app-search-modal-input span[role='img'] {
  color: #999;
}

[data-theme='dark'] .vben-app-search-modal-cancel {
  color: #666;
}

[data-theme='dark'] .vben-app-search-modal-not-data {
  color: #969faf;
}

[data-theme='dark'] .vben-app-search-modal-list__item {
  background-color: #151515;
  box-shadow: 0 1px 3px 0 #d4d9e1;
  color: #c9d1d9;
}

[data-theme='dark'] .vben-app-search-modal-list__item--active {
  background-color: #0960bd;
  color: #fff;
}

[data-theme='dark'] .account-center-application__card-num {
  color: #8b949e;
}

[data-theme='dark'] .account-center-application__card-download {
  color: #0960bd;
}

[data-theme='dark'] [data-theme='dark'] .vben-form-design-sider {
  background-color: #1f1f1f;
}

[data-theme='dark'] [data-theme='light'] .vben-form-design-sider {
  background-color: #fff;
}

[data-theme='dark'] .vben-st-login {
  background: #151515;
}

[data-theme='dark'] .vben-iframe-page__main {
  background-color: #151515;
}

[data-theme='dark'] .vben-lock-page__hour,
[data-theme='dark'] .vben-lock-page__minute {
  background-color: #141313;
  color: #bababa;
}

[data-theme='dark'] .vben-lock-page-entry {
  background-color: rgb(0 0 0 / 50%);
}

[data-theme='dark'] .vben-lock-page-entry__header-name {
  color: #bababa;
}

[data-theme='dark'] .vben-lock-page-entry__err-msg {
  color: #ed6f6f;
}

[data-theme='dark'] ul li {
  border: 1px solid #ccc;
}

[data-theme='dark'] ul li:hover {
  border: 1px solid #0960bd;
  box-shadow: 0 2px 6px #0960bd;
  color: #0960bd;
}

[data-theme='dark'] .hidden-item {
  background-color: #f0bfc3;
}

[data-theme='dark'] .draggable-box :deep(.list-main) .moving::before {
  background-color: #0960bd;
}

[data-theme='dark'] .draggable-box :deep(.list-main) .drag-move-box:hover {
  background-color: rgb(9 96 189 / 20%);
}

[data-theme='dark'] .draggable-box :deep(.list-main) .drag-move-box::before {
  background-color: #0960bd;
}

[data-theme='dark'] .draggable-box :deep(.list-main) .drag-move-box.active {
  background-color: rgb(9 96 189 / 20%);
}

[data-theme='dark'] .draggable-box :deep(.list-main) .drag-move-box .show-key-box {
  color: #0960bd;
}

[data-theme='dark'] .draggable-box :deep(.list-main) .drag-move-box .copy,
[data-theme='dark'] .draggable-box :deep(.list-main) .drag-move-box .delete {
  color: #fff;
}

[data-theme='dark'] .draggable-box :deep(.list-main) .drag-move-box .copy {
  background-color: #0960bd;
}

[data-theme='dark'] .draggable-box :deep(.list-main) .drag-move-box .delete {
  background-color: #0960bd;
}

[data-theme='dark'] .draggable-box :deep(.list-main) .grid-box {
  background-color: rgb(152 103 247 / 12%);
}

[data-theme='dark'] .draggable-box :deep(.list-main) .grid-box .grid-row {
  background-color: rgb(152 103 247 / 12%);
}

[data-theme='dark'] .draggable-box :deep(.list-main) .grid-box .grid-row .grid-col .draggable-box {
  border: 1px #ccc;
}

[data-theme='dark']
  .draggable-box
  :deep(.list-main)
  .grid-box
  .grid-row
  .grid-col
  .draggable-box
  .list-main {
  border: 1px #ccc;
}

[data-theme='dark'] .draggable-box :deep(.list-main) .grid-box::before {
  background: 0 0;
}

[data-theme='dark'] .draggable-box :deep(.list-main) .grid-box.active {
  background-color: rgb(152 103 247 / 24%);
}

[data-theme='dark'] .draggable-box :deep(.list-main) .grid-box.active::before {
  background-color: #9867f7;
}

[data-theme='dark'] .draggable-box :deep(.list-main) .grid-box > .copy-delete-box > .copy,
[data-theme='dark'] .draggable-box :deep(.list-main) .grid-box > .copy-delete-box > .delete {
  color: #fff;
}

[data-theme='dark'] .draggable-box :deep(.list-main) .grid-box > .copy-delete-box > .copy {
  background-color: #9867f7;
}

[data-theme='dark'] .draggable-box :deep(.list-main) .grid-box > .copy-delete-box > .delete {
  background-color: #9867f7;
}

[data-theme='dark'] .form-panel .empty-text {
  color: #aaa;
}

[data-theme='dark'] .operating-area {
  border-bottom: 2px solid #ccc;
}

[data-theme='dark'] .operating-area a {
  color: #666;
}

[data-theme='dark'] .operating-area a.disabled,
[data-theme='dark'] .operating-area a.disabled:hover {
  color: #ccc;
}

[data-theme='dark'] .operating-area a:hover {
  color: #0960bd;
}

[data-theme='dark'] .vben-layout-header {
  background-color: #fff;
  color: #fff;
}

[data-theme='dark'] .vben-layout-header-left .vben-layout-header-trigger.light:hover {
  background-color: #f6f6f6;
}

[data-theme='dark'] .vben-layout-header-left .vben-layout-header-trigger.light svg {
  fill: #000;
}

[data-theme='dark'] .vben-layout-header--light {
  border-bottom: 1px solid #eee;
  border-left: 1px solid #eee;
  background-color: #fff !important;
}

[data-theme='dark'] .vben-layout-header--light .vben-layout-header-logo {
  color: #c9d1d9;
}

[data-theme='dark'] .vben-layout-header--light .vben-layout-header-logo:hover {
  background-color: #f6f6f6;
}

[data-theme='dark'] .vben-layout-header--light .vben-layout-header-action__item {
  color: #c9d1d9;
}

[data-theme='dark'] .vben-layout-header--light .vben-layout-header-action__item:hover {
  background-color: #f6f6f6;
}

[data-theme='dark'] .vben-layout-header--light .vben-layout-header-action span[role='img'],
[data-theme='dark'] .vben-layout-header--light .vben-layout-header-action-icon {
  color: #c9d1d9;
}

[data-theme='dark'] .vben-layout-header--dark {
  border-left: 1px solid #303030;
}

[data-theme='dark'] .vben-layout-header--dark .vben-layout-header-action__item .ant-badge span {
  color: #fff;
}

[data-theme='dark'] .vben-setting-drawer-feature {
  background-color: #0960bd;
  color: #fff;
}

[data-theme='dark'] .vben-layout-footer {
  color: #d9d9d9;
}

[data-theme='dark'] .vben-layout-footer__links a {
  color: #d9d9d9;
}

[data-theme='dark'] .vben-layout-footer__links a:hover {
  color: rgb(0 0 0 / 85%);
}

[data-theme='dark'] .vben-layout-footer__github:hover {
  color: rgb(0 0 0 / 85%);
}

[data-theme='dark'] .vben-page-footer {
  border-top: 1px solid #303030;
  background-color: #151515;
}

[data-theme='dark'] .vben-collapse-container {
  background-color: #151515;
}

[data-theme='dark'] .vben-collapse-container__header {
  border-bottom: 1px solid #303030;
}

[data-theme='dark'] .vben-collapse-container__footer {
  border-top: 1px solid #303030;
}

[data-theme='dark'] .vben-page-wrapper-content-bg {
  background-color: #151515;
}

[data-theme='dark'] .vben-basic-help {
  color: #909399;
}

[data-theme='dark'] .vben-basic-help:hover {
  color: #0960bd;
}

[data-theme='dark'] .vben-basic-title {
  color: #c9d1d9;
}

[data-theme='dark'] .vben-basic-title-show-span::before {
  background-color: #0960bd;
}

[data-theme='dark'] .vben-cropper-avatar-image-wrapper {
  border: 1px solid #303030;
  background: #151515;
}

[data-theme='dark'] .vben-cropper-avatar-image-mask {
  background: rgb(0 0 0 / 40%);
}

[data-theme='dark'] [data-theme='dark'] .ant-table-tbody > tr.ant-table-row-selected td,
[data-theme='dark'] [data-theme='dark'] .ant-table-tbody > tr:hover.ant-table-row-selected > td {
  background-color: #262626;
}

[data-theme='dark'] .vben-basic-table-row__striped td {
  background-color: #1e1e1e;
}

[data-theme='dark'] .vben-basic-table-form-container .ant-form {
  background-color: #151515;
}

[data-theme='dark'] .vben-basic-table .ant-table-wrapper {
  background-color: #151515;
}

[data-theme='dark'] .vben-tree {
  background-color: #151515;
}

[data-theme='dark'] .vben-tree-header {
  border-bottom: 1px solid #303030;
}

[data-theme='dark'] .darg-verify {
  border: 1px solid #ddd;
  background-color: #eee;
}

[data-theme='dark'] .darg-verify-bar {
  background-color: #55d187;
}

[data-theme='dark'] .darg-verify-content.success {
  -webkit-text-fill-color: #fff;
}

[data-theme='dark'] .darg-verify-content > * {
  -webkit-text-fill-color: #333;
}

[data-theme='dark'] .darg-verify-action {
  background-color: #fff;
}

[data-theme='dark'] .ir-dv-img__tip {
  color: #fff;
}

[data-theme='dark'] .ir-dv-img__tip.success {
  background-color: rgb(85 209 135 / 60%);
}

[data-theme='dark'] .ir-dv-img__tip.error {
  background-color: rgb(237 111 111 / 60%);
}

[data-theme='dark'] .ir-dv-img__tip.normal {
  background-color: rgb(0 0 0 / 30%);
}

[data-theme='dark'] .vben-basic-drawer .ant-drawer-close:hover {
  color: #ed6f6f;
}

[data-theme='dark'] .vben-basic-drawer .ant-drawer-body {
  background-color: #151515;
}

[data-theme='dark'] .vben-basic-drawer__detail .ant-drawer-header {
  border-top: 1px solid #303030;
}

[data-theme='dark'] .vben-strength-meter-bar {
  background-color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .vben-strength-meter-bar::after,
[data-theme='dark'] .vben-strength-meter-bar::before {
  border-color: #fff;
  background-color: transparent;
}

[data-theme='dark'] .vben-strength-meter-bar--fill {
  background-color: transparent;
}

[data-theme='dark'] .vben-strength-meter-bar--fill[data-score='0'] {
  background-color: #e74242;
}

[data-theme='dark'] .vben-strength-meter-bar--fill[data-score='1'] {
  background-color: #ed6f6f;
}

[data-theme='dark'] .vben-strength-meter-bar--fill[data-score='2'] {
  background-color: #efbd47;
}

[data-theme='dark'] .vben-strength-meter-bar--fill[data-score='3'] {
  background-color: rgb(85 209 135 / 50%);
}

[data-theme='dark'] .vben-strength-meter-bar--fill[data-score='4'] {
  background-color: #55d187;
}

[data-theme='dark'] .vben-image-preview .ant-image-preview-operations {
  background-color: rgb(0 0 0 / 40%);
}

[data-theme='dark'] .vben-app-search-footer {
  border-top: 1px solid #303030;
  background-color: #151515;
  color: #666;
}

[data-theme='dark'] html[data-theme='dark'] .vben-multiple-tabs .ant-tabs-tab {
  border-bottom: 1px solid #303030;
}

[data-theme='dark']
  html[data-theme='light']
  .vben-multiple-tabs
  .ant-tabs-tab:not(.ant-tabs-tab-active) {
  border: 1px solid #d9d9d9 !important;
}

[data-theme='dark'] .vben-multiple-tabs {
  border-bottom: 1px solid #303030;
  background-color: #151515;
}

[data-theme='dark'] .vben-multiple-tabs .ant-tabs.ant-tabs-card .ant-tabs-nav {
  background-color: #151515;
}

[data-theme='dark'] .vben-multiple-tabs .ant-tabs.ant-tabs-card .ant-tabs-nav .ant-tabs-tab {
  background-color: #151515;
  color: #c9d1d9;
}

[data-theme='dark'] .vben-multiple-tabs .ant-tabs.ant-tabs-card .ant-tabs-nav .ant-tabs-tab svg {
  fill: #c9d1d9;
}

[data-theme='dark']
  .vben-multiple-tabs
  .ant-tabs.ant-tabs-card
  .ant-tabs-nav
  .ant-tabs-tab:not(.ant-tabs-tab-active):hover {
  color: #0960bd;
}

[data-theme='dark'] .vben-multiple-tabs .ant-tabs.ant-tabs-card .ant-tabs-nav .ant-tabs-tab-active {
  background: #0960bd;
}

[data-theme='dark']
  .vben-multiple-tabs
  .ant-tabs.ant-tabs-card
  .ant-tabs-nav
  .ant-tabs-tab-active
  span {
  color: #fff !important;
}

[data-theme='dark']
  .vben-multiple-tabs
  .ant-tabs.ant-tabs-card
  .ant-tabs-nav
  .ant-tabs-tab-active
  svg {
  fill: #fff;
}

[data-theme='dark'] .vben-multiple-tabs-content__extra-fold,
[data-theme='dark'] .vben-multiple-tabs-content__extra-quick,
[data-theme='dark'] .vben-multiple-tabs-content__extra-redo {
  border-left: 1px solid #303030;
  color: #8b949e;
}

[data-theme='dark'] .vben-multiple-tabs-content__extra-fold:hover,
[data-theme='dark'] .vben-multiple-tabs-content__extra-quick:hover,
[data-theme='dark'] .vben-multiple-tabs-content__extra-redo:hover {
  color: #c9d1d9;
}

[data-theme='dark'] .vben-layout-mix-sider.light .vben-layout-mix-sider-logo {
  border-bottom: 1px solid #eee;
}

[data-theme='dark'] .vben-layout-mix-sider.light.open > .scrollbar {
  border-right: 1px solid #eee;
}

[data-theme='dark'] .vben-layout-mix-sider.light .vben-layout-mix-sider-module__item {
  color: rgb(0 0 0 / 65%);
}

[data-theme='dark'] .vben-layout-mix-sider.light .vben-layout-mix-sider-module__item--active {
  color: #0960bd;
}

[data-theme='dark'] .vben-layout-mix-sider.light .vben-layout-mix-sider-menu-list__content {
  box-shadow: 0 0 4px 0 rgb(0 0 0 / 10%);
}

[data-theme='dark'] .vben-layout-mix-sider.light .vben-layout-mix-sider-menu-list__title .pushpin {
  color: rgb(0 0 0 / 35%);
}

[data-theme='dark']
  .vben-layout-mix-sider.light
  .vben-layout-mix-sider-menu-list__title
  .pushpin:hover {
  color: rgb(0 0 0 / 85%);
}

[data-theme='dark'] .vben-layout-mix-sider.dark .vben-layout-mix-sider-menu-list__title {
  color: #fff;
}

[data-theme='dark'] .vben-layout-mix-sider-module__item {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .vben-layout-mix-sider-module__item:hover {
  color: #fff;
}

[data-theme='dark'] .vben-layout-mix-sider-module__item--active {
  color: #fff;
}

[data-theme='dark'] .vben-layout-mix-sider-module__item--active::before {
  background-color: #0960bd;
}

[data-theme='dark'] .vben-layout-mix-sider-trigger {
  background-color: rgb(255 255 255 / 10%);
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .vben-layout-mix-sider.light .vben-layout-mix-sider-trigger {
  border-top: 1px solid #eee;
  background-color: #fff;
  color: rgb(0 0 0 / 65%);
}

[data-theme='dark'] .vben-layout-mix-sider-menu-list {
  background-color: #fff;
}

[data-theme='dark'] .vben-layout-mix-sider-menu-list__title {
  border-bottom: 1px solid #eee;
  color: #0960bd;
}

[data-theme='dark'] .vben-layout-mix-sider-menu-list__title .pushpin {
  color: rgb(255 255 255 / 65%);
}

[data-theme='dark'] .vben-layout-mix-sider-menu-list__title .pushpin:hover {
  color: #fff;
}

[data-theme='dark'] .vben-layout-mix-sider-menu-list__content .ant-menu-inline,
[data-theme='dark'] .vben-layout-mix-sider-menu-list__content .ant-menu-vertical,
[data-theme='dark'] .vben-layout-mix-sider-menu-list__content .ant-menu-vertical-left {
  border-right: 1px solid transparent;
}

[data-theme='dark'] .vben-layout-mix-sider-drag-bar {
  background-color: #f8f8f9;
  box-shadow: 0 0 4px 0 rgb(28 36 56 / 15%);
}

[data-theme='dark'] .vben-layout-sideBar.ant-layout-sider-dark .ant-layout-sider-trigger {
  background-color: rgb(255 255 255 / 10%);
  color: #bfbfbf;
}

[data-theme='dark'] .vben-layout-sideBar.ant-layout-sider-dark .ant-layout-sider-trigger:hover {
  background-color: rgb(255 255 255 / 20%);
  color: #fff;
}

[data-theme='dark'] .vben-layout-sideBar:not(.ant-layout-sider-dark) .ant-layout-sider-trigger {
  border-top: 1px solid #303030;
  color: #c9d1d9;
}

[data-theme='dark'] .vben-cropper-am-cropper {
  background: #eee;
}

[data-theme='dark'] .vben-cropper-am-preview {
  border: 1px solid #303030;
}

[data-theme='dark'] .vben-cropper-am-group {
  border-top: 1px solid #303030;
}

[data-theme='dark'] .vben-basic-drawer-footer {
  border-top: 1px solid #303030;
  background-color: #151515;
}

[data-theme='dark'] .vben-basic-drawer-header__back:hover {
  color: #0960bd;
}

[data-theme='dark'] .vben-basic-modal-close span:first-child:hover {
  color: #0960bd;
}

[data-theme='dark'] .vben-basic-modal-close span:last-child:hover {
  color: #ed6f6f;
}

[data-theme='dark'] html[data-theme='dark'] .lf-dnd {
  background: #080808;
}

[data-theme='dark'] .vben-flow-chart-toolbar {
  border-bottom: 1px solid #303030;
  background-color: #1e1e1e;
}

[data-theme='dark'] .vben-flow-chart-toolbar .disabeld {
  color: rgb(255 255 255 / 30%);
}

[data-theme='dark'] .vben-flow-chart-toolbar__icon:hover {
  color: #0960bd;
}

[data-theme='dark'] .img-preview {
  background: rgb(0 0 0 / 50%);
}

[data-theme='dark'] .img-preview-content {
  color: #fff;
}

[data-theme='dark'] .img-preview__close {
  background-color: rgb(0 0 0 / 50%);
  color: #fff;
}

[data-theme='dark'] .img-preview__close:hover {
  background-color: rgb(0 0 0 / 80%);
}

[data-theme='dark'] .img-preview__index {
  background: rgb(109 109 109 / 60%);
}

[data-theme='dark'] .img-preview__controller {
  background: rgb(109 109 109 / 60%);
}

[data-theme='dark'] .img-preview__arrow {
  background-color: rgb(0 0 0 / 50%);
}

[data-theme='dark'] .img-preview__arrow:hover {
  background-color: rgb(0 0 0 / 80%);
}

[data-theme='dark'] .vben-darg-bar:hover {
  background-color: #0960bd;
  box-shadow: 0 0 4px 0 rgb(28 36 56 / 15%);
}

[data-theme='dark'] .vben-header-user-dropdown--light:hover {
  background-color: #f6f6f6;
}

[data-theme='dark'] .vben-header-user-dropdown--light .vben-header-user-dropdown__name {
  color: #c9d1d9;
}

[data-theme='dark'] .vben-header-user-dropdown--light .vben-header-user-dropdown__desc {
  color: #7c8087;
}

[data-theme='dark'] .vben-layout-breadcrumb--light .ant-breadcrumb-link {
  color: #999;
}

[data-theme='dark'] .vben-layout-breadcrumb--light .ant-breadcrumb-link a {
  color: rgb(0 0 0 / 65%);
}

[data-theme='dark'] .vben-layout-breadcrumb--light .ant-breadcrumb-link a:hover {
  color: #0960bd;
}

[data-theme='dark'] .vben-layout-breadcrumb--light .ant-breadcrumb-separator {
  color: #999;
}

[data-theme='dark'] .vben-layout-breadcrumb--dark .ant-breadcrumb-link {
  color: rgb(255 255 255 / 60%);
}

[data-theme='dark'] .vben-layout-breadcrumb--dark .ant-breadcrumb-link a {
  color: rgb(255 255 255 / 80%);
}

[data-theme='dark'] .vben-layout-breadcrumb--dark .ant-breadcrumb-link a:hover {
  color: #fff;
}

[data-theme='dark'] .vben-layout-breadcrumb--dark .ant-breadcrumb-separator,
[data-theme='dark'] .vben-layout-breadcrumb--dark .anticon {
  color: rgb(255 255 255 / 80%);
}

[data-theme='dark'] .file-table thead {
  background-color: rgb(255 255 255 / 4%);
}

[data-theme='dark'] .file-table table,
[data-theme='dark'] .file-table td,
[data-theme='dark'] .file-table th {
  border: 1px solid #303030;
}

[data-theme='dark'] .context-menu {
  border: 1px solid rgb(0 0 0 / 8%);
  background-color: #151515;
  box-shadow: 0 2px 2px 0 rgb(0 0 0 / 14%), 0 3px 1px -2px rgb(0 0 0 / 10%),
    0 1px 5px 0 rgb(0 0 0 / 6%);
}

[data-theme='dark'] .context-menu li:not(.ant-menu-item-disabled):hover {
  background-color: rgb(255 255 255 / 8%);
  color: #c9d1d9;
}

[data-theme='dark'] .context-menu__popup li:not(.ant-menu-item-disabled):hover {
  background-color: rgb(255 255 255 / 8%);
  color: #c9d1d9;
}

[data-theme='dark'] .vben-simple-menu-tag {
  color: #fff;
}

[data-theme='dark'] .vben-simple-menu-tag--primary {
  background-color: #0960bd;
}

[data-theme='dark'] .vben-simple-menu-tag--error {
  background-color: #ed6f6f;
}

[data-theme='dark'] .vben-simple-menu-tag--success {
  background-color: #55d187;
}

[data-theme='dark'] .vben-simple-menu-tag--warn {
  background-color: #efbd47;
}

[data-theme='dark'] .edit-cell-rule-popover .ant-popover-inner-content {
  color: #ed6f6f;
}

[data-theme='dark'] .vben-editable-cell__icon:hover svg {
  color: #0960bd;
}

[data-theme='dark'] .vben-setting-theme-picker__item {
  border: 1px solid #ddd;
}

[data-theme='dark'] .vben-setting-theme-picker__item--active {
  border: 1px solid #0b79ee;
}

[data-theme='dark'] .vben-setting-theme-picker__item--active svg {
  fill: #fff !important;
}

[data-theme='dark'] .light-border::after {
  background-color: #0960bd;
}

[data-theme='dark'] .vben-menu-menu-popover .vben-menu-item,
[data-theme='dark'] .vben-menu-menu-popover .vben-menu-submenu-title {
  color: rgb(255 255 255 / 70%);
}

[data-theme='dark'] .vben-menu-menu-popover .vben-menu-dark .vben-menu-item,
[data-theme='dark'] .vben-menu-menu-popover .vben-menu-dark .vben-menu-submenu-title {
  color: rgb(255 255 255 / 70%);
}

[data-theme='dark'] .vben-menu-menu-popover .vben-menu-dark .vben-menu-item:hover,
[data-theme='dark'] .vben-menu-menu-popover .vben-menu-dark .vben-menu-submenu-title:hover {
  color: #fff;
}

[data-theme='dark'] .vben-menu-menu-popover .vben-menu-dark .vben-menu-item-selected,
[data-theme='dark'] .vben-menu-menu-popover .vben-menu-dark .vben-menu-submenu-title-selected {
  background-color: #0960bd !important;
  color: #fff;
}

[data-theme='dark'] .vben-menu-menu-popover .vben-menu-light .vben-menu-item,
[data-theme='dark'] .vben-menu-menu-popover .vben-menu-light .vben-menu-submenu-title {
  color: #c9d1d9;
}

[data-theme='dark'] .vben-menu-menu-popover .vben-menu-light .vben-menu-item:hover,
[data-theme='dark'] .vben-menu-menu-popover .vben-menu-light .vben-menu-submenu-title:hover {
  color: #0960bd;
}

[data-theme='dark'] .vben-menu-menu-popover .vben-menu-light .vben-menu-item-selected,
[data-theme='dark'] .vben-menu-menu-popover .vben-menu-light .vben-menu-submenu-title-selected {
  background-color: rgb(9 96 189 / 10%);
  color: #0960bd;
}

[data-theme='dark'] .vben-menu-menu-popover .vben-menu-light .vben-menu-item-selected::after,
[data-theme='dark']
  .vben-menu-menu-popover
  .vben-menu-light
  .vben-menu-submenu-title-selected::after {
  background-color: #0960bd;
}

[data-theme='dark'] .vben-menu {
  color: #c9d1d9;
}

[data-theme='dark'] .vben-menu-light {
  background-color: #fff;
}

[data-theme='dark'] .vben-menu-light .vben-menu-submenu-active {
  color: #0960bd !important;
}

[data-theme='dark'] .vben-menu-light .vben-menu-submenu-active-border::after {
  background-color: #0960bd;
}

[data-theme='dark'] .vben-menu-dark .vben-menu-submenu-active {
  color: #fff !important;
}

[data-theme='dark'] .vben-menu-vertical .vben-menu-item:hover,
[data-theme='dark'] .vben-menu-vertical .vben-menu-submenu-title:hover {
  color: #0960bd;
}

[data-theme='dark']
  .vben-menu-light.vben-menu-vertical
  .vben-menu-item-active:not(.vben-menu-submenu) {
  background-color: rgb(9 96 189 / 10%);
  color: #0960bd;
}

[data-theme='dark']
  .vben-menu-light.vben-menu-vertical
  .vben-menu-item-active:not(.vben-menu-submenu)::after {
  background-color: #0960bd;
}

[data-theme='dark'] .vben-menu-light.vben-menu-vertical .vben-menu-item-active.vben-menu-submenu {
  color: #0960bd;
}

[data-theme='dark']
  .vben-menu-light.vben-menu-vertical.vben-menu-collapse
  .vben-menu-submenu-active,
[data-theme='dark']
  .vben-menu-light.vben-menu-vertical.vben-menu-collapse
  > li.vben-menu-item-active {
  background-color: rgb(9 96 189 / 5%);
}

[data-theme='dark']
  .vben-menu-light.vben-menu-vertical.vben-menu-collapse
  .vben-menu-submenu-active::before,
[data-theme='dark']
  .vben-menu-light.vben-menu-vertical.vben-menu-collapse
  > li.vben-menu-item-active::before {
  background-color: #0960bd;
}

[data-theme='dark'] .vben-menu-dark.vben-menu-vertical .vben-menu-item,
[data-theme='dark'] .vben-menu-dark.vben-menu-vertical .vben-menu-submenu-title {
  color: rgb(255 255 255 / 70%);
}

[data-theme='dark']
  .vben-menu-dark.vben-menu-vertical
  .vben-menu-item-active:not(.vben-menu-submenu),
[data-theme='dark']
  .vben-menu-dark.vben-menu-vertical
  .vben-menu-submenu-title-active:not(.vben-menu-submenu) {
  background-color: #0960bd !important;
  color: #fff !important;
}

[data-theme='dark'] .vben-menu-dark.vben-menu-vertical .vben-menu-item:hover,
[data-theme='dark'] .vben-menu-dark.vben-menu-vertical .vben-menu-submenu-title:hover {
  color: #fff;
}

[data-theme='dark'] .vben-menu-dark.vben-menu-vertical.vben-menu-collapse .vben-menu-submenu-active,
[data-theme='dark']
  .vben-menu-dark.vben-menu-vertical.vben-menu-collapse
  > li.vben-menu-item-active {
  color: #fff !important;
}

[data-theme='dark']
  .vben-menu-dark.vben-menu-vertical.vben-menu-collapse
  .vben-menu-submenu-active::before,
[data-theme='dark']
  .vben-menu-dark.vben-menu-vertical.vben-menu-collapse
  > li.vben-menu-item-active::before {
  background-color: #0960bd;
}

[data-theme='dark']
  .vben-menu-dark.vben-menu-vertical.vben-menu-collapse
  .vben-menu-submenu-active
  .vben-menu-submenu-collapse,
[data-theme='dark']
  .vben-menu-dark.vben-menu-vertical.vben-menu-collapse
  > li.vben-menu-item-active
  .vben-menu-submenu-collapse {
  background-color: transparent;
}

[data-theme='dark'] .vben-menu-dark.vben-menu-vertical .vben-menu-submenu .vben-menu-item-active,
[data-theme='dark']
  .vben-menu-dark.vben-menu-vertical
  .vben-menu-submenu
  .vben-menu-item-active:hover {
  color: #fff;
}

[data-theme='dark']
  .vben-menu-dark.vben-menu-vertical
  .vben-menu-child-item-active
  > .vben-menu-submenu-title {
  color: #fff;
}

[data-theme='dark']
  .vben-menu-dark.vben-menu-vertical
  .vben-menu-opened
  .vben-menu-submenu-has-parent-submenu
  .vben-menu-submenu-title {
  background-color: transparent;
}

[data-theme='dark'] .vben-basic-menu__sidebar-hor.ant-menu-horizontal.ant-menu-dark {
  background-color: transparent;
}

[data-theme='dark']
  .vben-basic-menu__sidebar-hor.ant-menu-horizontal.ant-menu-dark
  .ant-menu-item-active,
[data-theme='dark']
  .vben-basic-menu__sidebar-hor.ant-menu-horizontal.ant-menu-dark
  .ant-menu-item-open,
[data-theme='dark']
  .vben-basic-menu__sidebar-hor.ant-menu-horizontal.ant-menu-dark
  .ant-menu-item-selected,
[data-theme='dark']
  .vben-basic-menu__sidebar-hor.ant-menu-horizontal.ant-menu-dark
  .ant-menu-item:hover,
[data-theme='dark']
  .vben-basic-menu__sidebar-hor.ant-menu-horizontal.ant-menu-dark
  .ant-menu-submenu-active,
[data-theme='dark']
  .vben-basic-menu__sidebar-hor.ant-menu-horizontal.ant-menu-dark
  .ant-menu-submenu-open,
[data-theme='dark']
  .vben-basic-menu__sidebar-hor.ant-menu-horizontal.ant-menu-dark
  .ant-menu-submenu-selected,
[data-theme='dark']
  .vben-basic-menu__sidebar-hor.ant-menu-horizontal.ant-menu-dark
  .ant-menu-submenu-title:hover,
[data-theme='dark']
  .vben-basic-menu__sidebar-hor.ant-menu-horizontal.ant-menu-dark
  .ant-menu-submenu:hover,
[data-theme='dark']
  .vben-basic-menu__sidebar-hor.ant-menu-horizontal.ant-menu-dark
  .ant-menu:not(.ant-menu-inline)
  .ant-menu-submenu-open {
  color: #fff;
}

[data-theme='dark']
  .vben-basic-menu__sidebar-hor.ant-menu-horizontal.ant-menu-dark
  .vben-basic-menu-item__level1 {
  background-color: transparent;
}

[data-theme='dark'] .vben-setting-menu-type-picker__item {
  background-color: #f0f2f5;
}

[data-theme='dark'] .vben-setting-menu-type-picker__item--light::before,
[data-theme='dark'] .vben-setting-menu-type-picker__item--sidebar::before {
  background-color: #273352;
}

[data-theme='dark'] .vben-setting-menu-type-picker__item--light::after,
[data-theme='dark'] .vben-setting-menu-type-picker__item--sidebar::after {
  background-color: #fff;
}

[data-theme='dark'] .vben-setting-menu-type-picker__item--mix::before {
  background-color: #fff;
}

[data-theme='dark'] .vben-setting-menu-type-picker__item--mix::after {
  background-color: #273352;
}

[data-theme='dark'] .vben-setting-menu-type-picker__item--top-menu::after {
  background-color: #273352;
}

[data-theme='dark'] .vben-setting-menu-type-picker__item--dark {
  background-color: #273352;
}

[data-theme='dark'] .vben-setting-menu-type-picker__item--mix-sidebar::before {
  background-color: #273352;
}

[data-theme='dark'] .vben-setting-menu-type-picker__item--mix-sidebar::after {
  background-color: #fff;
}

[data-theme='dark'] .vben-setting-menu-type-picker__item--mix-sidebar .mix-sidebar {
  background-color: #fff;
}

[data-theme='dark'] .vben-setting-menu-type-picker__item--active,
[data-theme='dark'] .vben-setting-menu-type-picker__item:hover {
  border: 2px solid #0960bd;
}

[data-theme='dark'] .vben-basic-column-setting__check-item .ant-checkbox-wrapper:hover {
  color: #0960bd;
}

[data-theme='dark'] .vben-basic-column-setting__fixed-left,
[data-theme='dark'] .vben-basic-column-setting__fixed-right {
  color: rgb(0 0 0 / 45%);
}

[data-theme='dark'] .vben-basic-column-setting__fixed-left.active,
[data-theme='dark'] .vben-basic-column-setting__fixed-left:hover,
[data-theme='dark'] .vben-basic-column-setting__fixed-right.active,
[data-theme='dark'] .vben-basic-column-setting__fixed-right:hover {
  color: #0960bd;
}

[data-theme='dark'] .vben-basic-column-setting__fixed-left.disabled,
[data-theme='dark'] .vben-basic-column-setting__fixed-right.disabled {
  color: rgb(255 255 255 / 30%);
}
