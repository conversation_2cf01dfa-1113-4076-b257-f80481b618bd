import type { AppRouteRecordRaw, Menu } from '/@/router/types';

import { defineStore } from 'pinia';
import { store } from '/@/store';
import { useI18n } from '/@/hooks/web/useI18n';
import { getAuthCache, setAuthCache } from '/@/utils/auth';
import { SLD_MENU_LIST_KEY, SLD_ALL_ROUTE_KEY, SLD_STORE_INFO_KEY } from '/@/enums/cacheEnum';
import { useUserStore } from './user';
import { flatMultiLevelRoutes } from '/@/router/helper/routeHelper';
import { transformRouteToMenu } from '/@/router/helper/menuHelper';
import { PAGE_NOT_FOUND_ROUTE } from '/@/router/routes/basic'; //ERROR_LOG_ROUTE
import { toRaw } from 'vue';
import { filter } from '/@/utils/helper/treeHelper';
import { getStoreInfoApi } from '/@/api/store/index';

import { asyncRoutes } from '/@/router/routes';

import { useMessage } from '/@/hooks/web/useMessage';

interface PermissionState {
  // 权限代码列表
  permCodeList: string[] | number[];
  // 路由是否动态添加
  isDynamicAddedRoute: boolean;
  // 触发菜单更新
  lastBuildMenuTime: number;
  // 后台菜单列表
  backMenuList: Menu[];
  // 菜单列表
  frontMenuList: Menu[];
}

export const usePermissionStore = defineStore({
  id: 'app-permission',
  state: (): PermissionState => ({
    // 权限代码列表
    permCodeList: [],
    // 路由是否动态添加
    isDynamicAddedRoute: false,
    // 触发菜单更新
    lastBuildMenuTime: 0,
    // 后台菜单列表
    backMenuList: [],
    // 菜单列表
    frontMenuList: [],
  }),
  getters: {
    getPermCodeList(state): string[] | number[] {
      return state.permCodeList;
    },
    getBackMenuList(state) {
      // 如果状态为空，尝试从缓存恢复
      if (!state.backMenuList || state.backMenuList.length === 0) {
        // 1. 尝试从缓存获取转换后的菜单 路由切换后会出现空的情况只能重新构建
        const cachedMenu = getAuthCache<Menu[]>(SLD_MENU_LIST_KEY);
        if (cachedMenu && Array.isArray(cachedMenu) && cachedMenu.length > 0) {
          try {
            // 2. 获取路由配置
            const routes_info = filter(asyncRoutes, (route) => {
              const { meta } = route;
              const { roles } = meta || {};
              if (!roles) return true;
              return true; // 这里可以添加角色判断逻辑
            });

            // 3. 处理菜单数据
            const meun_list = cachedMenu.map((item) => {
              if (item.children) {
                const children_list = JSON.parse(JSON.stringify(item.children));
                children_list.map((it) => {
                  it.frontPath = it.frontPath.split(item.frontPath + '/')[1];
                });
                return { ...item, children: children_list };
              }
              return item;
            });

            // 4. 构建路由列表
            const routeList = [];
            for (let i = 0; i < meun_list.length; i++) {
              const items = routes_info.filter((item) => item.path === meun_list[i].frontPath);
              if (items.length > 0) {
                const it = items[0].children;
                const obj = [];
                for (let j = 0; j < it.length; j++) {
                  meun_list[i].children?.forEach((ite) => {
                    if (
                      it[j].path.indexOf(ite.frontPath) !== -1 &&
                      it[j].path.indexOf(ite.frontPath) === 0
                    ) {
                      obj.push(it[j]);
                    }
                  });
                }
                if (obj.length) {
                  items[0].children = obj;
                  items[0].redirect = items[0].path + '/' + items[0].children[0].path;
                }
                routeList.push(items[0]);
              }
            }

            // 5. 转换为菜单结构
            const backMenuList = transformRouteToMenu(routeList);
            state.backMenuList = backMenuList;
          } catch (error) {
            console.error('Error transforming menu data:', error);
          }
        }
      }
      return state.backMenuList;
    },
    getFrontMenuList(state): Menu[] {
      return state.frontMenuList;
    },
    getLastBuildMenuTime(state): number {
      return state.lastBuildMenuTime;
    },
    getIsDynamicAddedRoute(state): boolean {
      return state.isDynamicAddedRoute;
    },
  },
  actions: {
    setPermCodeList(codeList: string[]) {
      this.permCodeList = codeList;
    },

    setBackMenuList(list: Menu[]) {
      this.backMenuList = list;
      list?.length > 0 && this.setLastBuildMenuTime();
    },

    setFrontMenuList(list: Menu[]) {
      this.frontMenuList = list;
    },

    setLastBuildMenuTime() {
      this.lastBuildMenuTime = new Date().getTime();
    },

    setDynamicAddedRoute(added: boolean) {
      this.isDynamicAddedRoute = added;
    },
    resetState(): void {
      this.isDynamicAddedRoute = false;
      this.permCodeList = [];
      this.backMenuList = [];
      this.lastBuildMenuTime = 0;
    },
    // 构建路由
    async buildRoutesAction(): Promise<AppRouteRecordRaw[]> {
      const { t } = useI18n();
      const userStore = useUserStore();
      let result = await getStoreInfoApi()
      if (result.state == 200) {
        setAuthCache(SLD_STORE_INFO_KEY, {
          storeId: result.data.storeId,
          shopType: result.data.shopType ? result.data.shopType : 1,
          storeName: result.data.storeName ? result.data.storeName : result.data.vendorName,
          storeLogo: result.data.storeLogo,
          storeGradeName: result.data.storeGradeName,
        })
      }

      let routes: AppRouteRecordRaw[] = [];
      let routes_info: AppRouteRecordRaw[] = [];
      const roleList = toRaw(userStore.getRoleList) || [];
      const routeRemoveIgnoreFilter = (route: AppRouteRecordRaw) => {
        const { meta } = route;
        // ignoreRoute 为true 则路由仅用于菜单生成，不会在实际的路由表中出现
        const { ignoreRoute } = meta || {};
        // arr.filter 返回 true 表示该元素通过测试
        return !ignoreRoute;
      };

      // 路由过滤器 在 函数filter 作为回调传入遍历使用
      const routeFilter = (route: AppRouteRecordRaw) => {
        const { meta } = route;
        // 抽出角色
        const { roles } = meta || {};
        if (!roles) return true;
        // 进行角色权限判断
        return roleList.some((role) => roles.includes(role));
      };

      const { createMessage } = useMessage();
      if (!localStorage.getItem('menuLoading')) {
        createMessage.loading({
          content: t('sys.app.menuLoading'),
          duration: 1,
        });
        localStorage.setItem('menuLoading', '1');
        setTimeout(() => {
          localStorage.removeItem('menuLoading');
        }, 1000);
      }

      // 对非一级路由进行过滤
      routes_info = filter(asyncRoutes, routeFilter);
      // 对一级路由再次根据角色权限过滤
      routes_info = routes_info.filter(routeFilter);
      // 将路由转换成菜单
      const menuLists = transformRouteToMenu(routes_info, true);
      // 移除掉 ignoreRoute: true 的路由 非一级路由
      routes_info = filter(routes_info, routeRemoveIgnoreFilter);
      // 移除掉 ignoreRoute: true 的路由 一级路由；
      routes_info = routes_info.filter(routeRemoveIgnoreFilter);
      // 对菜单进行排序
      menuLists.sort((a, b) => {
        return (a.meta?.orderNo || 0) - (b.meta?.orderNo || 0);
      });

      // 设置菜单列表
      this.setFrontMenuList(menuLists);

      // Convert multi-level routing to level 2 routing
      // 将多级路由转换为 2 级路由
      routes_info = flatMultiLevelRoutes(routes_info);
      // routes_info.push({
      //   path: '/im',
      //   name: 'Im',
      //   component: LAYOUT,
      //   redirect: '',
      //   meta: {
      //     orderNo: 7,
      //     icon: '',
      //     title: '客服',
      //   },
      // });

      let routeList: AppRouteRecordRaw[] = [];
      try {
        let meun_list_info = []
        if (!getAuthCache<any>(SLD_MENU_LIST_KEY) || getAuthCache<any>(SLD_MENU_LIST_KEY) == undefined) {
          meun_list_info = JSON.parse(localStorage.getItem('sld_menu_data'))
        } else {
          meun_list_info = JSON.parse(JSON.stringify(getAuthCache<any>(SLD_MENU_LIST_KEY)));
        }
        const meun_list = meun_list_info
        meun_list.map((item) => {
          if (item.children) {
            const children_list = JSON.parse(JSON.stringify(item.children));
            children_list.map((it) => {
              it.frontPath = it.frontPath.split(item.frontPath + '/')[1];
            });
            item.children = children_list;
          }
        });
        let route_list: any = [];
        route_list = routes_info;
        routeList = [];
        for (let i = 0; i < meun_list.length; i++) {
          const items = route_list.filter((item) => item.path == meun_list[i].frontPath);
          if (items.length > 0) {
            const it = items[0].children;
            const obj: any = [];
            for (let j = 0; j < it.length; j++) {
              meun_list[i].children.map((ite) => {
                if (
                  it[j].path.indexOf(ite.frontPath) != -1 &&
                  it[j].path.indexOf(ite.frontPath) == 0
                ) {
                  let flag = true
                  // dev_supplier-start
                  if (it[j].path == 'goods_import_to_platform') {
                    if (userStore.getStoreInfo && userStore.getStoreInfo.shopType && userStore.getStoreInfo.shopType == '3') {
                      flag = false
                    } else {
                      flag = true
                    }
                  } else {
                    flag = true
                  }
                  if (it[j].path == 'goods_import_to_supplier') {
                    if (userStore.getStoreInfo && userStore.getStoreInfo.shopType && userStore.getStoreInfo.shopType == '1') {
                      flag = true
                    } else {
                      flag = false
                    }
                  } else {
                    flag = true
                  }
                  // dev_supplier-end
                  if (flag) {
                    obj.push(it[j]);
                  }
                }
              });
            }
            if (obj.length) {
              items[0].children = obj;
              items[0].redirect = items[0].path + '/' + items[0].children[0].path;
            }
            routeList.push(items[0]);
          }
        }
      } catch (error) {
        console.error(error);
      }
      routeList.sort((a, b) => a.meta.orderNo - b.meta.orderNo);

      //  后台路由到菜单结构
      const backMenuList = transformRouteToMenu(routeList);
      const sld_all_route = [];
      const menuList = JSON.parse(JSON.stringify(backMenuList));
      menuList.map((item) => {
        if (item.children) {
          item.children.map((child) => {
            sld_all_route.push(child.path);
          });
        }
      });
      setAuthCache(SLD_ALL_ROUTE_KEY, sld_all_route);
      this.setBackMenuList(menuList);

      // 删除 meta.ignoreRoute 项
      routeList = filter(routeList, routeRemoveIgnoreFilter);
      routeList = routeList.filter(routeRemoveIgnoreFilter);

      routeList = flatMultiLevelRoutes(routeList);
      routes = [PAGE_NOT_FOUND_ROUTE, ...routeList];
      return routes;
    },
  },
});

// 需要在设置之外使用
export function usePermissionStoreWithOut() {
  return usePermissionStore(store);
}
