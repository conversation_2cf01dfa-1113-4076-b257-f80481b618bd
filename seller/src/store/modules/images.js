import { defineStore } from 'pinia';
import { store } from '/@/store';
import { getStoreSettingApi } from '@/api/sysset/sysset';
import { Persistent } from '/@/utils/cache/persistent';

const object2string = {
  toString(i) {
    return JSON.stringify(i);
  },
  toParse(i) {
    return JSON.parse(i);
  },
};

export const useImageStore = defineStore({
  id: 'images',
  state: () => ({
    com_img_info: object2string.toParse(localStorage.getItem('com_img_info') ?? '[]'),
  }),
  getters: {
    getImages(state) {
      return (key) => state.com_img_info.find((i) => i.name == key);
    },
    getImagesAll(state) {
      return state.com_img_info;
    },
  },

  actions: {
    setImgInfoAll(payload) {
      this.com_img_info = payload;
      localStorage.setItem('com_img_info', object2string.toString(payload));
    },
    setImgInfo(key, value) {
      let target = this.com_img_info.find((i) => i.name == key);
      target = value;
      localStorage.setItem('com_img_info', object2string.toString(this.com_img_info));
    },
    resetAllImages() {
      Persistent.clearAll();
    },
    async fetchSetting() {
      const res = await getStoreSettingApi();
      this.setImgInfoAll(res.data);
    },
  },
});

// Need to be used outside the setup
export function useImageStoreWithOut() {
  return useImageStore(store);
}
