export default {
  charts: {
    baiduMap: '百度地图',
    aMap: '高德地图',
    googleMap: '谷歌地图',
    charts: '图表',
    map: '地图',
    line: '折线图',
    pie: '饼图',
  },
  comp: {
    comp: '组件',
    basic: '基础组件',
    transition: '动画组件',
    countTo: '数字动画',

    scroll: '滚动组件',
    scrollBasic: '基础滚动',
    scrollAction: '滚动函数',
    virtualScroll: '虚拟滚动',

    tree: 'Tree',
    treeBasic: '基础树',
    editTree: '可搜索/工具栏',
    actionTree: '函数操作示例',

    modal: '弹窗扩展',
    drawer: '抽屉扩展',
    desc: '详情组件',

    verify: '验证组件',
    verifyDrag: '拖拽校验',
    verifyRotate: '图片还原',

    qrcode: '二维码组件',
    strength: '密码强度组件',
    upload: '上传组件',

    loading: 'Loading',

    time: '相对时间',
    cropperImage: '图片裁剪',
    cardList: '卡片列表',
  },
  editor: {
    editor: '编辑器',
    jsonEditor: 'Json编辑器',
    markdown: 'markdown编辑器',

    tinymce: '富文本',
    tinymceBasic: '基础使用',
    tinymceForm: '嵌入form',
  },
  excel: {
    excel: 'Excel',
    customExport: '选择导出格式',
    jsonExport: 'JSON数据导出',
    arrayExport: 'Array数据导出',
    importExcel: '导入',
  },
  feat: {
    feat: '功能',
    icon: '图标',
    sessionTimeout: '登录过期',
    tabs: '标签页操作',
    tabDetail: '标签详情页',
    print: '打印',
    contextMenu: '右键菜单',
    download: '文件下载',
    clickOutSide: 'ClickOutSide组件',
    imgPreview: '图片预览',
    copy: '剪切板',
    msg: '消息提示',
    watermark: '水印',
    ripple: '水波纹',
    fullScreen: '全屏',
    errorLog: '错误日志',
    tab: 'Tab带参',
    tab1: 'Tab带参1',
    tab2: 'Tab带参2',
    menu: 'Menu带参',
    menu1: 'Menu带参1',
    menu2: 'Menu带参2',
    ws: 'websocket测试',
    breadcrumb: '面包屑导航',
    breadcrumbFlat: '平级模式',
    requestDemo: '测试请求重试',
    breadcrumbFlatDetail: '平级详情',
    breadcrumbChildren: '层级模式',
    breadcrumbChildrenDetail: '层级详情',
  },
  flow: {
    name: '图形编辑器',
    flowChart: '流程图',
  },
  form: {
    form: 'Form',
    basic: '基础表单',
    useForm: 'useForm',
    refForm: 'RefForm',
    advancedForm: '可收缩表单',
    ruleForm: '表单验证',
    dynamicForm: '动态表单',
    customerForm: '自定义组件',
    appendForm: '表单增删示例',
    tabsForm: '标签页+多级field',
  },
  iframe: {
    frame: '外部页面',
    antv: 'antVue文档(内嵌)',
    doc: '项目文档(内嵌)',
    docExternal: '项目文档(外链)',
  },
  level: { level: '多级菜单' },
  page: {
    page: '页面',

    form: '表单页',
    formBasic: '基础表单',
    formStep: '分步表单',
    formHigh: '高级表单',

    desc: '详情页',
    descBasic: '基础详情页',
    descHigh: '高级详情页',

    result: '结果页',
    resultSuccess: '成功页',
    resultFail: '失败页',

    account: '个人页',
    accountCenter: '个人中心',
    accountSetting: '个人设置',

    exception: '异常页',
    netWorkError: '网络错误',
    notData: '无数据',

    list: '列表页',
    listCard: '卡片列表',
    listBasic: '标准列表',
    listSearch: '搜索列表',
  },
  permission: {
    permission: '权限管理',

    front: '基于前端权限',
    frontPage: '页面权限',
    frontBtn: '按钮权限',
    frontTestA: '权限测试页A',
    frontTestB: '权限测试页B',

    back: '基于后台权限',
    backPage: '页面权限',
    backBtn: '按钮权限',
  },
  setup: {
    page: '引导页',
  },
  system: {
    moduleName: '系统管理',
    account: '账号管理',
    account_detail: '账号详情',
    password: '修改密码',
    dept: '部门管理',
    menu: '菜单管理',
    role: '角色管理',
  },
  table: {
    table: 'Table',
    basic: '基础表格',
    treeTable: '树形表格',
    fetchTable: '远程加载示例',
    fixedColumn: '固定列',
    customerCell: '自定义列',
    formTable: '开启搜索区域',
    useTable: 'UseTable',
    refTable: 'RefTable',
    multipleHeader: '多级表头',
    mergeHeader: '合并单元格',
    expandTable: '可展开表格',
    fixedHeight: '定高/头部自定义',
    footerTable: '表尾行合计',
    editCellTable: '可编辑单元格',
    editRowTable: '可编辑行',
    authColumn: '权限列',
    resizeParentHeightTable: '继承父元素高度',
    vxeTable: 'VxeTable',
  },
  sysset: {
    syssetnav: '系统配置',
    sysset_setting: '基本配置',
    site_info: '站点配置',
    pic_set: '图片配置',
    payment: '支付配置',
    order: '运营配置',
    app_set: 'APP配置',
    sysset_notice_set: '通知管理',
    sms: '短信配置',
    email: '邮件配置',
    msg_tpl: '消息模板',
    sysset_acount: '三方账号',
    union_login: '授权登录',
    sysset_authority: '权限管理',
    authority_group: '权限组',
    authority_member: '管理员管理',
    operate_log: '操作日志',
    sysset_agreement: '协议管理',
    agreement_lists: '协议管理',
    sysset_express: '物流管理',
    express_lists: '物流公司',
    express: '物流设置',
  },
  manage: {
    managenav: '商城管理',
    manage_product: '商品管理',
    goods_setting: '商品设置',
    goods_list: '商品管理',
    goods_list_to_detail: '商品详情',
    cate_lists: '分类管理',
    brand: '品牌管理',
    search_attr: '属性管理',
    goods_label: '标签管理',
    manage_goods_platform: '商品库管理',
    platform_list: '商品资料库',
    manage_store: '店铺管理',
    own_list: '自营店铺',
    settle_store_list: '入驻店铺',
    settle_store_list_view: '入驻店铺详情',
    settle_store_list_apply_detail: '入驻店铺审核',
    settle_store_list_to_edit: '编辑店铺信息',
    grade_list: '店铺等级',
    manage_bill: '结算管理',
    bill_lists: '结算账单',
    manage_article: '文章管理',
    article_cat_lists: '文章分类',
    article_lists: '文章管理',
  },
  marketing: {
    center: '应用',
    marketing_center: '应用中心',
    coupon_list: '优惠券',
    coupon_list_to_add: '发布优惠券',
    coupon_list_to_edit: '编辑优惠券',
    coupon_list_to_copy: '复制优惠券',
    coupon_list_to_view: '优惠券详情',
    coupon_list_to_receive_list: '优惠券领取列表',
    full_acm: '满减活动',
    full_acm_to_add: '发布满减活动',
    full_acm_to_edit: '编辑满减活动',
    full_acm_to_copy: '复制满减活动',
    full_acm_to_view: '查看满减活动',
    full_asm: '阶梯满减',
    full_asm_to_add: '发布阶梯满减活动',
    full_asm_to_edit: '编辑阶梯满减活动',
    full_asm_to_view: '查看阶梯满减活动',
    full_asm_to_copy: '复制阶梯满减活动',
    full_ald: '满N元折扣',
    full_ald_to_add: '发布满N元折扣活动',
    full_ald_to_edit: '编辑满N元折扣活动',
    full_ald_to_view: '查看满N元折扣活动',
    full_ald_to_copy: '复制满N元折扣活动',
    full_nld: '满N件折扣',
    full_nld_to_add: '发布满N件折扣活动',
    full_nld_to_edit: '编辑满N件折扣活动',
    full_nld_to_view: '查看满N件折扣活动',
    full_nld_to_copy: '复制满N件折扣活动',
    promotion_seckill: '秒杀活动',
    seckill_bind_goods: '秒杀活动商品',
    seckill_to_add: '参加秒杀活动',
    spell_group: '拼团活动',
    spell_group_to_view: '拼团详情',
    spell_group_bind_goods: '活动商品',
    spell_group_team_list: '活动团队',
    spell_group_order: '订单管理',
    spell_group_order_to_detail: '订单详情',
    spell_group_to_add: '新建拼团活动',
    spell_group_to_edit: '编辑拼团活动',
    spell_group_to_copy: '复制拼团活动',
    ladder_group: '阶梯团活动',
    ladder_group_to_view: '阶梯团详情',
    ladder_group_team_list: '团队列表',
    ladder_group_to_add: '新建阶梯团活动',
    ladder_group_to_edit: '编辑阶梯团活动',
    ladder_group_to_copy: '复制阶梯团活动',
    presale: '预售活动',
    presale_bind_goods: '活动商品',
    presale_to_view: '预售详情',
    presale_to_add: '新建预售活动',
    presale_to_edit: '编辑预售活动',
    presale_to_copy: '复制预售活动',
    video: '视频带货',
  },
  member: {
    management: '会员中心',
    manage: '会员管理',
    lists: '会员列表',
    list_to_detail: '会员详情',
    recharge: '充值管理',
    withdraw: '提现管理',
    balance_log: '资金明细',
    point_setting: '积分设置',
  },
  spreader: {
    spreader_title: '推手',
    goods_list: '推手商品',
    order_list: '订单管理',
    order_lists_to_detail: '订单详情',
  },
  point: {
    point: '积分商城',
    goods_list: '商品管理',
    goods_list_to_import: '导入商城商品',
    order_list: '订单管理',
    order_list_to_detail: '订单详情',
    bill_list: '结算管理',
    bill_list_to_detail: '结算详情',
    goods_list_to_add: '发布商品',
    goods_list_to_edit: '编辑商品',
    goods_import:'商品导入',
  },
  order: {
    order: '订单',
    order_lists: '订单列表',
    order_lists_to_detail: '订单详情',
    spell_group_order_lists: '拼团订单',
    order_deliver: '订单发货',
    order_deliver_detail: '订单详情',
    service: '售后管理',
    service_refund_lists_to_detail: '售后详情',
    evaluation: '评价管理',
    express: '物流管理',
    express_transport_to_add: '新增运费模板',
    express_transport_to_edit: '编辑运费模板',
    express_transport_to_copy: '复制运费模板',
    self_fetch: '自提点管理',
    self_fetch_to_add: '新增自提点',
    self_fetch_to_edit: '编辑自提点',
    verification: '自提点核销',
    address_list: '地址管理',
    print_setting: '打印机设置',
  },
  store: {
    store: '店铺',
    store_setting: '店铺设置',
    info: '店铺信息',
    category: '店铺分类',
    brand_lists: '品牌申请',
    msg_setting: '消息设置',
    msg_lists: '消息列表',
    account: '账号管理',
    material: '素材中心',
  },
  goods: {
    goods: '商品',
    goods_list: '商品列表',
    goods_storage_list: '仓库中商品',
    goods_check_list: '待审核商品',
    goods_list_to_add: '发布商品',
    goods_list_to_edit: '编辑商品',
    course: '课程列表',
    course_detail: '课程信息',
    related_template: '关联版式',
    related_template_to_add: '新增关联版式',
    related_template_to_edit: '编辑关联版式',
    attribute_group: '自定义属性',
    attribute_group_to_detail: '自定义属性管理',
    goods_import: '商品导入',
    platform_goods_import: '商品资料库导入',
    goods_import_to_excel: 'Excel导入',
    goods_import_to_add: '商品资料详情',
    goods_import_vop: 'VOP商品库',
    goods_import_vop_to_add: 'VOP商品详情',
    goods_import_to_supplier:'供应链商品市场',
    goods_import_supplier_to_add:'供应链商品详情',
    goods_changed_notice: '代销变更',
    goods_changed_notice_detail: '编辑商品',
    goods_purchase: '采购商品',
  },
};
