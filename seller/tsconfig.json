{"$schema": "https://json.schemastore.org/tsconfig", "extends": "@vben/ts-config/vue-app.json", "compilerOptions": {"baseUrl": ".", "declaration": false, "types": ["vite/client"], "allowJs": true, "checkJs": true, "paths": {"/@/*": ["src/*"], "/#/*": ["types/*"], "@/*": ["src/*"], "#/*": ["types/*"]}}, "include": ["tests/**/*.ts", "src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "types/**/*.d.ts", "types/**/*.ts", "build/**/*.ts", "build/**/*.d.ts", "mock/**/*.ts", "vite.config.ts"], "exclude": ["node_modules", "tests/server/**/*.ts", "dist"]}