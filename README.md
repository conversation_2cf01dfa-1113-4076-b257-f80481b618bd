
## 域名

- 移动端 https://m.shengu.shop
- PC 端 https://pc.shengu.shop
- 管理员 https://admin.shengu.shop
- 店长 https://seller.shengu.shop
- 供应商 https://scp.shengu.shop
- 聊天系统 https://im.shengu.shop

测试环境统一有 **test** 后缀 比如 **admin-test.shengu.shop**

## 环境
```bash
export SG_ENV=prod  #生产
export SG_ENV=test  #测试
export SG_ENV=dev   #本地开发
env|grep SG_ENV # 查看环境变量
```
项目启动会自动根据环境变量找到对应配置  
不配置默认是dev配置

## 本地开发
sh build.sh [项目名]
sh start.sh [项目名]
```bash
export SG_ENV=dev
sh build.sh seller # exp. 本地启动seller项目
```


## 部署
sh build.sh [项目名]
不同环境的机器上 SG_ENV已经配置到~/.zshrc里  
进入不同机器会自动部署，无需配置环境
```bash
sh build.sh seller # exp. 部署seller项目
```
