#!/bin/bash
# 检查是否提供了目录参数
if [ -z "$1" ]; then
  echo "请输入项目 比如sh deploy.sh seller"
  exit 1
fi

if [ ! -d "$1" ]; then
  echo "目录不存在"
  exit 1
fi

echo "git pull origin"
git pull origin
echo "git pull success"

# 进入工作目录
cd "$1" 
# 检查 SG_ENV 的值，并执行相应的命令
if [ "$SG_ENV" = "prod" ]; then
    echo "[Prod]"
    pnpm build
    echo "开始部署"
    cp -r dist/* "/www/wwwroot/$1.shengu.shop"
    echo "部署完成"
    cd ..
else
    echo "[Test]"
    pnpm build:test
    echo "开始部署"
    cp -r dist/* "/www/wwwroot/$1-test.shengu.shop"
    echo "部署完成"
    cd ..
fi
