@prefix-cls: ~'@{namespace}-multiple-tabs';

html[data-theme='dark'] {
  .@{prefix-cls} {
    .ant-tabs-tab {
      border-bottom: 1px solid @border-color-base;
    }
  }
}

html[data-theme='light'] {
  .@{prefix-cls} {
    .ant-tabs-tab:not(.ant-tabs-tab-active) {
      border: 1px solid transparent !important;
      border-bottom: none !important;
    }
  }
}

.@{prefix-cls} {
  z-index: 10;
  height: @multiple-height + 2;
  background-color: @component-background;
  border-top-left-radius: 8px;
  overflow: hidden;

  .ant-tabs-small {
    height: @multiple-height + 2;
  }

  .ant-tabs.ant-tabs-card {
    .ant-tabs-nav {
      height: @multiple-height + 2;
      margin: 0;
      border: 0;
      box-shadow: none;

      &::before {
        border-bottom: none !important;
      }

      .ant-tabs-nav-list {
        padding-top: 2px;
      }

      .ant-tabs-nav-container {
        height: @multiple-height + 2;
        padding-top: 2px;
      }

      .ant-tabs-tab-btn {
        height: calc(@multiple-height - 2px) !important;
      }

      .ant-tabs-tab {
        height: calc(@multiple-height) !important;
        padding: 0 15px !important;
        padding-right: 12px;
        transition: none;
        border-radius: 3px 3px 0 0;
        background-color: @component-background;
        color: @text-color-base;

        &:hover {
          .ant-tabs-tab-remove {
            opacity: 1;
          }
        }

        .ant-tabs-tab-remove {
          display: flex;
          align-items: center;
          margin-right: -8px;
          margin-left: 2px;

          svg {
            width: 10px;
            height: 10px;
            transition: all 0.2s;
            fill: #999;
          }

          &:hover {
            svg {
              transform: scale(1.3);
            }
          }
        }

        svg {
          fill: @text-color-base;
        }
      }

      .ant-tabs-tab:not(.ant-tabs-tab-active) {
        &:hover {
          color: @primary-color;
        }
      }

      .ant-tabs-tab-active {
        position: relative;
        padding-left: 18px;
        transition: none;
        border: 1px solid #EAEAEA !important;
        border-bottom: none !important;
        span {
          color: @primary-color !important;
          margin-left: 0px !important;
        }

        .ant-tabs-tab-remove {
          opacity: 1;
        }

        svg {
          width: 0.7em;
          fill:  @primary-color !important;
        }
      }
    }

    .ant-tabs-nav > div:nth-child(1) {
      padding: 0 6px;
    }
  }

  .ant-tabs-tab:not(.ant-tabs-tab-active) {
    .anticon-close {
      font-size: 12px;

      svg {
        width: 0.6em;
      }
    }
  }

  .ant-dropdown-trigger {
    display: inline-flex;
  }

  &--hide-close {
    .ant-tabs-tab-remove {
      display: none !important;
    }
  }

  &-content {
    &__extra-quick,
    &__extra-redo,
    &__extra-fold {
      display: inline-block;
      width: 36px;
      height: @multiple-height;
      border-left: 1px solid @border-color-base;
      color: @text-color-secondary;
      line-height: calc(@multiple-height + 3px);
      text-align: center;
      cursor: pointer;

      &:hover {
        color: @text-color-base;
      }

      span[role='img'] {
        transform: rotate(90deg);
      }
    }

    &__extra-redo {
      span[role='img'] {
        transform: rotate(0deg);
      }
    }

    &__info {
      display: inline-block;
      width: 100%;
      height: calc(@multiple-height - 3px);
      // height: 100%;
      padding-left: 0;
      color: #444;
      font-size: 13px;
      line-height: calc(@multiple-height - 3px);
      cursor: pointer;
      user-select: none;
      span{
        margin-left: 0px !important;
      }
    }
  }
}

.ant-tabs-dropdown-menu {
  &-title-content {
    display: flex;
    align-items: center;

    .@{prefix-cls} {
      &-content__info {
        width: auto;
        margin-left: 0;
        line-height: 28px;
      }
    }
  }

  &-item-remove {
    margin-left: auto;
  }
}

.multiple-tabs__dropdown {
  .ant-dropdown-content {
    width: 172px;
  }
}
