<template>
  <Layout :class="prefixCls" v-bind="lockEvents">
    <LayoutHeader fixed v-if="getIsShowHeaderFromRoute && getShowFullHeaderRef" />
    <Layout :class="[layoutClass]">
      <LayoutSideBar v-if="getIsShowSideBarFromRoute && (getShowSidebar || getIsMobile)" />
      <Layout :class="`${prefixCls}-main`">
        <LayoutMultipleHeader v-if="getIsShowHeaderFromRoute" />
        <LayoutContent />
        <LayoutFooter />
      </Layout>
    </Layout>
  </Layout>
</template>

<script lang="ts">
  import { defineComponent, computed, unref } from 'vue';
  import { Layout } from 'ant-design-vue';
  import { createAsyncComponent } from '/@/utils/factory/createAsyncComponent';
  import { useRouter } from 'vue-router';
  import LayoutHeader from './header/index.vue';
  import LayoutContent from './content/index.vue';
  import LayoutSideBar from './sider/index.vue';
  import LayoutMultipleHeader from './header/MultipleHeader.vue';

  import { useHeaderSetting } from '/@/hooks/setting/useHeaderSetting';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { useLockPage } from '/@/hooks/web/useLockPage';

  import { useAppInject } from '/@/hooks/web/useAppInject';

  export default defineComponent({
    name: 'DefaultLayout',
    components: {
      LayoutFooter: createAsyncComponent(() => import('/@/layouts/default/footer/index.vue')),
      LayoutHeader,
      LayoutContent,
      LayoutSideBar,
      LayoutMultipleHeader,
      Layout,
    },
    setup() {
      const { prefixCls } = useDesign('default-layout');
      const { getIsMobile } = useAppInject();
      const { getShowFullHeaderRef } = useHeaderSetting();
      const { getShowSidebar, getIsMixSidebar, getShowMenu } = useMenuSetting();
      const router = useRouter();

      //这个直接决定是否显示右侧bar,权重最高
      const getIsShowSideBarFromRoute = computed(() => {
        const { currentRoute } = router;
        const currentRouter = unref(currentRoute);
        if (!Reflect.has(currentRouter.meta, 'hideSideBar')) {
          return true;
        }
        return !currentRouter.meta.hideSideBar;
      });

      //这个直接决定是否显示header,权重最高
      const getIsShowHeaderFromRoute = computed(() => {
        const { currentRoute } = router;
        const currentRouter = unref(currentRoute);
        if (!Reflect.has(currentRouter.meta, 'hideHeader')) {
          return true;
        }
        return !currentRouter.meta.hideHeader;
      });

      // Create a lock screen monitor
      const lockEvents = useLockPage();

      const layoutClass = computed(() => {
        let cls: string[] = ['ant-layout'];
        if (unref(getIsMixSidebar) || unref(getShowMenu)) {
          cls.push('ant-layout-has-sider');
        }
        return cls;
      });

      return {
        getShowFullHeaderRef,
        getShowSidebar,
        getIsShowSideBarFromRoute,
        getIsShowHeaderFromRoute,
        prefixCls,
        getIsMobile,
        getIsMixSidebar,
        layoutClass,
        lockEvents,
      };
    },
  });
</script>
<style lang="less">
  @prefix-cls: ~'@{namespace}-default-layout';

  .@{prefix-cls} {
    display: flex;
    flex-direction: column;
    width: 100%;
    min-height: 100%;
    background-color: @content-bg;

    > .ant-layout {
      min-height: 100%;
    }

    &-main {
      width: 100%;
      margin-left: 1px;
    }
  }
</style>
