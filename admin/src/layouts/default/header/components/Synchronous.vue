<template>
  <Tooltip title="同步" placement="bottom" :mouseEnterDelay="0.5">
    <span @click="toggle">
      <SyncOutlined />
    </span>
  </Tooltip>
</template>
<script lang="ts">
  import { defineComponent, } from 'vue';
  import { Tooltip } from 'ant-design-vue';
  import { getSettingInitApi } from '/@/api/common/common';
  import { failTip, sucTip } from '/@/utils/utils';

  import { FullscreenExitOutlined, SyncOutlined } from '@ant-design/icons-vue';

  export default defineComponent({
    name: 'Synchronous',
    components: { FullscreenExitOutlined, SyncOutlined, Tooltip },

    setup() {
      //更新配置
      const toggle = async()=> {
        let res = await getSettingInitApi()
        if(res.state == 200){
          sucTip(res.msg)
        }else{
          failTip(res.msg)
        }
      }

      return {
        toggle
      };
    },
  });
</script>
