<template>
  <RouterView>
    <template #default="{ Component, route }">
      <transition
        :name="
          getTransitionName({
            route,
            openCache,
            enableTransition: getEnableTransition,
            cacheTabs: getCaches,
            def: getBasicTransition,
          })
        "
        mode="out-in"
        appear
      >
        <keep-alive v-if="openCache" :include="getCaches">
          <component :is="Component" :key="route.fullPath" />
        </keep-alive>
        <component v-else :is="Component" :key="route.fullPath" />
      </transition>
    </template>
  </RouterView>
  <FrameLayout v-if="getCanEmbedIFramePage" />
</template>

<script lang="ts">
  import { computed, defineComponent, unref } from 'vue';

  import FrameLayout from '/@/layouts/iframe/index.vue';

  import { useRootSetting } from '/@/hooks/setting/useRootSetting';
  import { useUserStore } from '/@/store/modules/user';

  import { useTransitionSetting } from '/@/hooks/setting/useTransitionSetting';
  import { useMultipleTabSetting } from '/@/hooks/setting/useMultipleTabSetting';
  import { getTransitionName } from './transition';

  import { useMultipleTabStore } from '/@/store/modules/multipleTab';

  export default defineComponent({
    name: 'PageLayout',
    components: { FrameLayout },
    setup() {
      const { getShowMultipleTab } = useMultipleTabSetting();
      const tabStore = useMultipleTabStore();

      const userStore = useUserStore();

      const { getOpenKeepAlive, getCanEmbedIFramePage } = useRootSetting();

      const { getBasicTransition, getEnableTransition } = useTransitionSetting();

      const openCache = computed(() => unref(getOpenKeepAlive) && unref(getShowMultipleTab));

      const getCaches = computed((): string[] => {
        if (!unref(getOpenKeepAlive)) {
          return [];
        }
        // 获取项目tab上的页面name 注：只是获取数据
        let tabList = JSON.parse(JSON.stringify(tabStore.getCachedTabList))
        // userStore.getDelKeepAlive 要删除的路由name数组
        // 判断是否有删除的路由name
        if(userStore.getDelKeepAlive&&userStore.getDelKeepAlive.length>0){
          let del = JSON.parse(JSON.stringify(userStore.getDelKeepAlive))
          // 判断tab上是否有删除的路由name 有的话直接从tabList里移除
          del.forEach(item=>{
            let index = tabList.indexOf(item)
            if(index!=-1){
              tabList.splice(index,1)
            }
          })
          // 清除要删除的路由name数组
          userStore.setDelKeepAlive([])
        }
        // 获取要更新的页面name 是有tab切换的页面 
        let tab =  JSON.parse(JSON.stringify(userStore.getUpdateTab))
        // 获取项目tab上的页面name 注：只是获取数据
        let tabListOne = JSON.parse(JSON.stringify(tabStore.getCachedTabList))
        tab.forEach(item=>{
          // 判断项目tab数组上是否有需要更新的tab切换的页面 
          let index = tabListOne.findIndex(it=>it == item.name)
          // -1表示不在项目tab上
          if(index==-1){
            // 调用vuex里的方法
            userStore.setDelTab(item.name)
          }
        })
        // 路由缓存页面
        return tabList;
      });

      return {
        getTransitionName,
        openCache,
        getEnableTransition,
        getBasicTransition,
        getCaches,
        getCanEmbedIFramePage,
      };
    },
  });
</script>
