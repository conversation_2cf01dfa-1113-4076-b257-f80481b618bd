import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  GET_SETTING_LIST: '/system/admin/setting/getSettingList',
  GET_SETTING_UPDATE: '/system/admin/setting/updateSettingList',
  GET_SETTING_ES_INIT: '/integral/admin/integral/search/esInit',
  GET_SETTING_PLAT_FORM_SHELF: '/integral/admin/integral/goods/platformShelf',
  GET_SETTING_REASON_LIST: '/system/admin/reason/list',
  GET_ADMIN_GOODS_LIST: '/goods/admin/goods/list',
  GET_ADMIN_VIDEO_LIST: '/video/admin/video/list',
  GET_ADMIN_GOODS_GOODS_LIST: '/goods/admin/goods/goodsList',
  GET_SYSTEM_SETTING_INIT: '/system/admin/setting/settingInit',
  LOGOUT_API: '/adminLogin/oauth/logout',
  GET_SYSTEM_DICT_DATA_API: '/system/admin/dictData/list',
  // dev_supplier-start
  GET_ADMIN_GOODS_GOODS_SUPPLIER_LIST: '/supplier/admin/supplierGoods/goodsList',
  GET_ADMIN_GOODS_SUPPLIER_LIST: '/supplier/admin/supplierGoods/list',
  // dev_supplier-end
  LOGIN_API: '/adminLogin/oauth/token',
  GET_COMMON_SEND_TL_VERIFY: '/member/common/sendTlVerify',
};

//保存系统设置信息
export const getSettingUpdateApi = (params) =>
  defHttp.post({
    url: Api.GET_SETTING_UPDATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取系统设置信息
export const getSettingListApi = (params) =>
  defHttp.get({
    url: Api.GET_SETTING_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_更新es积分商品数据
export const getSettingEsInitApi = () =>
  defHttp.post({
    url: Api.GET_SETTING_ES_INIT,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_更新积分商品积分换算比例
export const getSettingPlatFormShelfApi = () =>
  defHttp.post({
    url: Api.GET_SETTING_PLAT_FORM_SHELF,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取理由
export const getSettingReasonListApi = (params) =>
  defHttp.get({
    url: Api.GET_SETTING_REASON_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取商品列表
export const getGoodsListApi = (params) =>
  defHttp.get({
    url: Api.GET_ADMIN_GOODS_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
  
  //slodon_获取短视频列表
  export const getVideoListApi = (params) =>
  defHttp.get({
    url: Api.GET_ADMIN_VIDEO_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
  
  //slodon_获取商品列表(es查询)
  export const getGoodsGoodsListApi = (params) =>
    defHttp.get({
      url: Api.GET_ADMIN_GOODS_GOODS_LIST,
      params,
      headers: {
        // @ts-ignore
        'Content-Type': ContentTypeEnum.FORM_URLENCODED,
      },
    });

  //更新配置
  export const getSettingInitApi = () =>
    defHttp.get({
      url: Api.GET_SYSTEM_SETTING_INIT,
      headers: {
        // @ts-ignore
        'Content-Type': ContentTypeEnum.FORM_URLENCODED,
      },
    });

  //退出登录
  export const logoutApi = (params) => 
    defHttp.post({
      url: Api.LOGOUT_API,
      params,
      headers: {
        // @ts-ignore
        'Content-Type': ContentTypeEnum.FORM_URLENCODED,
      },
    });
  
  // dev_supplier-start
  //slodon_获取供应链商品列表(es查询)
  export const getGoodsGoodsSupplierListApi = (params) =>
    defHttp.get({
      url: Api.GET_ADMIN_GOODS_GOODS_SUPPLIER_LIST,
      params,
      headers: {
        // @ts-ignore
        'Content-Type': ContentTypeEnum.FORM_URLENCODED,
      },
    });
    //slodon_获取供应链商品列表
    export const getGoodsSupplierListApi = (params) =>
      defHttp.get({
        url: Api.GET_ADMIN_GOODS_SUPPLIER_LIST,
        params,
        headers: {
          // @ts-ignore
          'Content-Type': ContentTypeEnum.FORM_URLENCODED,
        },
      });
  // dev_supplier-end


  //数据字典列表
  export const getDictDataApi = (params) => 
    defHttp.get({
      url: Api.GET_SYSTEM_DICT_DATA_API,
      params,
      headers: {
        // @ts-ignore
        'Content-Type': ContentTypeEnum.FORM_URLENCODED,
      },
    });

  //刷新token
  export const loginRefreshApi = (params) => 
    defHttp.post({
      url: Api.LOGIN_API,
      params,
      headers: {
        // @ts-ignore
        'Content-Type': ContentTypeEnum.FORM_URLENCODED,
      },
    });

  //会员注册、授权登录认证手机号发送验证码
  export const getCommonSendTlVerifyApi = (params) => 
    defHttp.get({
      url: Api.GET_COMMON_SEND_TL_VERIFY,
      params,
      headers: {
        // @ts-ignore
        'Content-Type': ContentTypeEnum.FORM_URLENCODED,
      },
    });

