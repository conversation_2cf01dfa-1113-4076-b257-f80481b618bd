import { defHttp } from '@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const API = {
  GET_MATERIAL_CATEGORY: '/system/admin/material/category/list',
  GET_MATERIAL_FILES: '/system/admin/material/file/list',
  GET_MATERIAL_FIRST_CATE: '/system/admin/material/category/firstCategory',
  POST_CATEGORY_ADD: '/system/admin/material/category/add',
  POST_UPDATE_CATEGORY: '/system/admin/material/category/update',
  POST_MATERIAL_RENAME: '/system/admin/material/file/rename',
  POST_MOVETO_MATERIAL: '/system/admin/material/file/moveTo',
  POST_FILE_BIND_CATEGORY: '/system/admin/material/file/fileBindCategory',
};

export const get_material_center_category = (params) =>
  defHttp.get({ url: API.GET_MATERIAL_CATEGORY, params });

export const getMaterialListApi = (params) => defHttp.get({ url: API.GET_MATERIAL_FILES, params });

export const cleanMaterialFilesApi = (params) =>
  defHttp.post({
    url: API.GET_MATERIAL_FILES,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const getMaterialCategoryFirstApi = (params) =>
  defHttp.get({ url: API.GET_MATERIAL_FIRST_CATE, params });

export const addCategoryApi = (params) =>
  defHttp.post({
    url: API.POST_CATEGORY_ADD,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const updateCategoryApi = (params) =>
  defHttp.post({
    url: API.POST_UPDATE_CATEGORY,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const renameMaterialFiles = (params) =>
  defHttp.post({
    url: API.POST_MATERIAL_RENAME,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const moveToMaterialApi = (params) =>
  defHttp.post({
    url: API.POST_MOVETO_MATERIAL,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const uploadMaterialFiles = (params) =>
  defHttp.post({
    url: API.POST_FILE_BIND_CATEGORY,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });
