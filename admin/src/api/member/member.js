import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
var Api;
(function (Api) {
  Api['MEMBER_LIST'] = '/member/admin/member/list';
  Api['EDIT_USER_STATE'] = '/member/admin/member/editState';
  Api['ADD_USER'] = '/member/admin/member/add';
  Api['EDIT_USER'] = '/member/admin/member/update';
  Api['EDIT_PWD'] = '/member/admin/member/editPwd';
  Api['EDIT_MEMBER_INTEGRAL'] = '/member/admin/member/editMemberIntegral';
  Api['GET_DETAIL'] = '/member/admin/member/detail';
  Api['SETTING_LIST'] = '/system/admin/setting/getSettingList';
  Api['APDATE_SETTING_LIST'] = '/system/admin/setting/updateSettingList';
  Api['GET_RECHARGE_SUM'] = '/member/admin/balanceRecharge/getSum';
  Api['GET_RECHARGE_DETAIL_LIST'] = '/member/admin/balanceRecharge/list';
  Api['TO_PAY'] = '/member/admin/balanceRecharge/toPay';
  Api['DEL_RECHARGE'] = '/member/admin/balanceRecharge/delete';
  Api['WITHDRAW_LIST'] = '/member/admin/member/withdraw/list';
  Api['WITHDRAW_AUDIT'] = '/member/admin/member/withdraw/audit';
  Api['BALANCE_LOG'] = '/member/admin/balanceLog/list';
  Api['BALANCE_LOG_EXPORT'] = '/member/admin/balanceLog/memberBalanceLogExport';
  Api['EDIT_MEMBER_BALANCE'] = '/member/admin/member/editMemberBalance';
})(Api || (Api = {}));
//获取会员列表
export const getMemberList = (params) => defHttp.get({ url: Api.MEMBER_LIST, params });
//编辑会员状态
export const editUserState = (params) =>
  defHttp.post({
    url: Api.EDIT_USER_STATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// 新增会员
export const addUser = (params) =>
  defHttp.post({
    url: Api.ADD_USER,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// 修改会员信息
export const editUser = (params) =>
  defHttp.post({
    url: Api.EDIT_USER,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// 重置密码
export const editPwd = (params) =>
  defHttp.post({
    url: Api.EDIT_PWD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// 积分设置
export const editMemberIntegral = (params) =>
  defHttp.post({
    url: Api.EDIT_MEMBER_INTEGRAL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// 获取会员详情
export const getMemberDetail = (params) => defHttp.get({ url: Api.GET_DETAIL, params });
// 获取配置信息
export const getSettingList = (params) => defHttp.get({ url: Api.SETTING_LIST, params });
// 更新配置状态
export const updateSettingList = (params) =>
  defHttp.post({
    url: Api.APDATE_SETTING_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// 获取充值人数和充值金额
export const getRechargeDetailList = (params) =>
  defHttp.get({ url: Api.GET_RECHARGE_DETAIL_LIST, params });
// 获取充值明细列表
export const getRechargeNum = (params) => defHttp.get({ url: Api.GET_RECHARGE_SUM, params });
// 去付款
export const toPay = (params) =>
  defHttp.post({
    url: Api.TO_PAY,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// 删除充值项
export const delRecharge = (params) =>
  defHttp.post({
    url: Api.DEL_RECHARGE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// 获取资金明细列表
export const getBalanceLog = (params) => defHttp.get({ url: Api.BALANCE_LOG, params });
// 资金明细导出
export const balanceLogExport = (params) => defHttp.get({
  url: Api.BALANCE_LOG_EXPORT,
  params,
  responseType: 'blob',
});
// 获取提现明细列表
export const withdrawList = (params) => defHttp.get({ url: Api.WITHDRAW_LIST, params });
// 提现列表操作接口
export const withdrawAudit = (params) =>
defHttp.post({
  url: Api.WITHDRAW_AUDIT,
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
  },
});
// 会员余额变更
export const editMemberBalance = (params) =>
  defHttp.post({
    url: Api.EDIT_MEMBER_BALANCE,
    params: {
      memberId: parseInt(params.memberId),
      amount: params.amount.toString(),
      type: parseInt(params.type),
      rechargeDescription: params.rechargeDescription || ''
    },
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });


