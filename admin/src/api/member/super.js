import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
var Api;
(function (Api) {
  Api['GET_SUPER_MEMBER_LIST'] = '/member/admin/superMember/list';
  Api['GET_SUPER_MEMBER_SETTING'] = '/member/admin/superMemberSetting/getSetting';
  Api['GET_SUPER_MEMBER_UPDATE_SETTING'] = '/member/admin/superMemberSetting/updateSettingList';
  Api['GET_SUPER_MEMBER_SUPER_LIST'] = '/member/admin/superMemberGoods/superGoodsList';
  Api['GET_SUPER_MEMBER_GOODS_DEL'] = '/member/admin/superMemberGoods/delete';
  Api['GET_SUPER_MEMBER_GOODS_GOODS_LIST'] = '/member/admin/superMemberGoods/goodsList';
  Api['GET_SUPER_MEMBER_GOODS_ADD'] = '/member/admin/superMemberGoods/add';
  Api['GET_MEMBER_SETTING_GET_LIST'] = '/member/admin/memberSetting/getSettingList';
  Api['GET_MEMBER_SETTING_UPDATE_LIST'] = '/member/admin/memberSetting/updateSettingList';
  Api['GET_ADMIN_MEMBER_LEVEL_LIST'] = '/member/admin/memberLevel/list';
  Api['GET_ADMIN_MEMBER_LEVEL_DISABLE'] = '/member/admin/memberLevel/disable';
  Api['GET_MEMBER_LEVEL_UPDATE'] = '/member/admin/memberLevel/update';
  Api['GET_MEMBER_LEVEL_DETAIL'] = '/member/admin/memberLevel/detail';
})(Api || (Api = {}));
//获取会员列表
export const getSuperMemberList = (params) => defHttp.get({ url: Api.GET_SUPER_MEMBER_LIST, params });

//slodon_获取超级会员设置信息
export const getSuperMemberSettingList = (params) => defHttp.get({ url: Api.GET_SUPER_MEMBER_SETTING, params });

//slodon_保存超级会员设置信息
export const getSuperMemberUpdateSettingApi = (params) =>
defHttp.post({
  url: Api.GET_SUPER_MEMBER_UPDATE_SETTING,
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
  },
});

//slodon_获取会员价商品列表
export const getSuperMemberSuperListApi = (params) => defHttp.get({ url: Api.GET_SUPER_MEMBER_SUPER_LIST, params });

//slodon_删除会员价商品
export const getSuperMemberSuperDelApi = (params) =>
defHttp.post({
  url: Api.GET_SUPER_MEMBER_GOODS_DEL,
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
  },
});

//slodon_获取商品列表
export const getSuperMemberGoodsListApi = (params) => defHttp.get({ url: Api.GET_SUPER_MEMBER_GOODS_GOODS_LIST, params });

//slodon_添加会员价商品
export const getSuperMemberGoodsAddApi = (params) =>
defHttp.post({
  url: Api.GET_SUPER_MEMBER_GOODS_ADD,
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
  },
});


//slodon_获取会员设置
export const getMemberSettingGetListApi = (params) => defHttp.get({ url: Api.GET_MEMBER_SETTING_GET_LIST, params });

//slodon_更新会员设置
export const getMemberSettingUpdateListApi = (params) =>
defHttp.post({
  url: Api.GET_MEMBER_SETTING_UPDATE_LIST,
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
  },
});

//slodon_获取会员等级列表
export const getAdminMemberLevelListApi = (params) => defHttp.get({ url: Api.GET_ADMIN_MEMBER_LEVEL_LIST, params });

//slodon_停用会员等级
export const getAdminMemberLevelDisableApi = (params) =>
defHttp.post({
  url: Api.GET_ADMIN_MEMBER_LEVEL_DISABLE,
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
  },
});

//slodon_编辑会员等级
export const getMemberLevelUpdateApi = (params) =>
defHttp.post({
  url: Api.GET_MEMBER_LEVEL_UPDATE,
  params,
  headers: {
    // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
    
  //slodon_获取会员等级详情
  export const getAdminMemberLevelDetailApi = (params) => defHttp.get({ url: Api.GET_MEMBER_LEVEL_DETAIL, params });