import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
var Api;
(function (Api) {
  Api['GET_COUPON_NORMAL_LIST'] = '/promotion/admin/coupon/normalList';
  Api['GET_GOODS_BRAND_LIST'] = '/goods/admin/goodsBrand/list';
  Api['GET_COUPON_LIST'] = '/promotion/admin/coupon/list';
  Api['GET_OPERATE_LIST'] = '/operate/admin/cluster/list';
  Api['GET_LABEL_OPERATE_MEMBER'] = '/operate/admin/label/operateMember';
  Api['GET_MEMBER_LEVEL_COUPON_LIST'] = '/member/admin/memberLevel/couponList';
})(Api || (Api = {}));
//获取优惠券列表（只获取未开始和进行中的）
export const getCouponNormalListApi = (params) =>
  defHttp.get({
    url: Api.GET_COUPON_NORMAL_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
  // slodon_获取品牌列表
  export const getGoodsBrandListApi = (params) =>
  defHttp.get({
    url: Api.GET_GOODS_BRAND_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
  
  //获取优惠券列表接口
  export const getCouponListApi = (params) =>
    defHttp.get({
      url: Api.GET_COUPON_LIST,
      params,
      headers: {
        // @ts-ignore
        'Content-Type': ContentTypeEnum.FORM_URLENCODED,
      },
    });

//获取分群列表
export const getOperateListApi = (params) =>
  defHttp.get({
    url: Api.GET_OPERATE_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取人群运营会员列表
export const getLabelOperateMemberApi = (params) =>
  defHttp.get({
    url: Api.GET_LABEL_OPERATE_MEMBER,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取优惠券、运费券列表
export const getMemberLevelCouponListApi = (params) =>
  defHttp.get({
    url: Api.GET_MEMBER_LEVEL_COUPON_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

