import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
var Api;
(function (Api) {
  Api['GET_SIGN_ACTIVITY_LIST'] = '/promotion/admin/sign/activity/list';
  Api['GET_SIGN_ACTIVITY_ADD'] = '/promotion/admin/sign/activity/add';
  Api['GET_SIGN_ACTIVITY_UPDATE'] = '/promotion/admin/sign/activity/update';
  Api['GET_SIGN_ACTIVITY_DEL'] = '/promotion/admin/sign/activity/delete';
  Api['GET_SIGN_ACTIVITY_IS_REMIND'] = '/promotion/admin/sign/activity/isRemind';
  Api['GET_SIGN_ACTIVITY_IS_OPEN'] = '/promotion/admin/sign/activity/isOpen';
  Api['GET_SIGN_ACTIVITY_DETAIL'] = '/promotion/admin/sign/activity/detail';
  Api['GET_SIGN_STATISTICS_ACT_LIST'] = '/promotion/admin/sign/statistics/actList';
  Api['GET_SIGN_STATISTICS_MEMBER_LIST'] = '/promotion/admin/sign/statistics/memberList';
  Api['GET_SIGN_STATISTICS_BONUS_LIST'] = '/promotion/admin/sign/statistics/bonusList';
  Api['GET_SIGN_STATISTICS_ACT_DETAIL'] = '/promotion/admin/sign/statistics/actDetail';
})(Api || (Api = {}));
// slodon_添加签到活动列表
export const getSignActivityListApi = (params) =>
  defHttp.get({
    url: Api.GET_SIGN_ACTIVITY_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// slodon_添加签到活动
export const getSignActivityAddApi = (params) =>
  defHttp.post({
    url: Api.GET_SIGN_ACTIVITY_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// slodon_编辑签到活动
export const getSignActivityUpdateApi = (params) =>
  defHttp.post({
    url: Api.GET_SIGN_ACTIVITY_UPDATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// slodon_删除签到活动
export const getSignActivityDelApi = (params) =>
  defHttp.post({
    url: Api.GET_SIGN_ACTIVITY_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// slodon_是否提醒签到活动
export const getSignActivityIsRemindApi = (params) =>
  defHttp.post({
    url: Api.GET_SIGN_ACTIVITY_IS_REMIND,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// slodon_是否开启签到活动
export const getSignActivityIsOpenApi = (params) =>
  defHttp.post({
    url: Api.GET_SIGN_ACTIVITY_IS_OPEN,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// slodon_获取签到活动详情
export const getSignActivityDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_SIGN_ACTIVITY_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// slodon_获取活动签到统计列表
export const getSignStatisticsActListApi = (params) =>
  defHttp.get({
    url: Api.GET_SIGN_STATISTICS_ACT_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// slodon_获取会员签到统计列表
export const getSignStatisticsMemberListApi = (params) =>
  defHttp.get({
    url: Api.GET_SIGN_STATISTICS_MEMBER_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// slodon_获取会员签到统计详情
export const getSignStatisticsBonusListApi = (params) =>
  defHttp.get({
    url: Api.GET_SIGN_STATISTICS_BONUS_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// slodon_获取活动统计详情的数据
export const getSignStatisticsActDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_SIGN_STATISTICS_ACT_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
