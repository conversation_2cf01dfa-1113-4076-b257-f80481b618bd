import { defHttp } from '@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const API = {
  GET_WITHDRAWAL_TL_LIST: '/seller/admin/withdrawalTl/list',
  GET_WITHDRAWAL_ACCOUNT_INFO: '/seller/admin/withdrawalTl/accountInfo',
  GET_WITHDRAWAL_WITH_DRAW: '/seller/admin/withdrawalTl/autoWithdraw',
  GET_WITHDRAWAL_APPLY: '/seller/admin/withdrawalTl/withdrawalApply',
  GET_BANK_ACCOUNT_LIST: '/seller/admin/bankAccount/list',
  GET_BANK_ACCOUNT_IS_DEFAULT: '/seller/admin/bankAccount/isDefault',
  GET_BANK_ACCOUNT_COMPANY_BIND_CARD: '/seller/admin/bankAccount/companyBindCard',
  GET_BANK_ACCOUNT_UN_BIND: '/seller/admin/bankAccount/unBind',
};

// 获取提现记录列表
export const getWithdrawalTlListApi = (params) => defHttp.get({ 
  url: API.GET_WITHDRAWAL_TL_LIST, 
  params ,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
});

//slodon_获取提现信息
export const getWithdrawalTlAccountInfoApi = (params) => defHttp.get({ 
  url: API.GET_WITHDRAWAL_ACCOUNT_INFO,
   params,
   headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
   });

//slodon_开启/关闭自动提现
export const getWithdrawalTlWithdrawApi = (params) => defHttp.post({
   url: API.GET_WITHDRAWAL_WITH_DRAW,
    params ,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
      },
  });

//slodon_申请提现
export const getWithdrawalTlApplyApi = (params) => defHttp.post({ 
  url: API.GET_WITHDRAWAL_APPLY,
   params,
   headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取银行卡列表
export const getBankAccountListApi = (params) => defHttp.get({ 
  url: API.GET_BANK_ACCOUNT_LIST, 
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
});

//slodon_设置/取消银行卡默认
export const getBankAccountIsDefaultApi = (params) => defHttp.post({ 
  url: API.GET_BANK_ACCOUNT_IS_DEFAULT,
   params, 
   headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_企业绑定对公账户
export const getBankAccountCompanyBindCardApi = (params) => defHttp.post({ 
  url: API.GET_BANK_ACCOUNT_COMPANY_BIND_CARD, 
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
});

//slodon_解绑账户
export const getBankAccountUnBindApi = (params) => defHttp.post({
   url: API.GET_BANK_ACCOUNT_UN_BIND, 
   params,
   headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });


