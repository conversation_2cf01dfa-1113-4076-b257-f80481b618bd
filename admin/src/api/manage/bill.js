import { ContentTypeEnum } from '/@/enums/httpEnum';
import { defHttp } from '/@/utils/http/axios';

var Api;
(function (Api) {
  // 结算管理
  Api['QUICK_BILL'] = '/system/admin/bill/quickBill'; // 快速结算
})(Api || (Api = {}));

/**
 * 快速结算
 * @param {Object} params - 结算参数
 * @param {number} params.storeId - 店铺ID
 * @returns {Promise<any>}
 */
export const quickBill = (params) =>
  defHttp.post({
    url: Api.QUICK_BILL,
    params,
  }); 