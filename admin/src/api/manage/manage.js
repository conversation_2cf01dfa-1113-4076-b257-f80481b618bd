import { ContentTypeEnum } from '/@/enums/httpEnum';
import { defHttp } from '/@/utils/http/axios';
var Api;
(function (Api) {
  // 商品设置
  Api['PRODUCT_SETTING'] = '/system/admin/setting/getSettingList';
  Api['ESINIT_PRODUCT'] = '/system/admin/search/esInit';
  Api['UPDATE_SITE_SETTING'] = '/system/admin/setting/updateSettingList';

  // 商品管理
  Api['GET_GOODS_LIST'] = '/goods/admin/goods/list';
  Api['GET_GOODS_DETAIL'] = '/goods/admin/goods/detail';
  Api['LOCKOFF'] = '/goods/admin/goods/lockup';
  Api['AUDIT_GOOD'] = '/goods/admin/goods/audit';

  // 分类管理
  Api['GET_CATEGORY_TREE'] = '/goods/admin/goodsCategory/getCateTree';
  Api['GET_CATEGORY_LIST'] = '/goods/admin/goodsCategory/list';
  Api['ADD_CATEGORY'] = '/goods/admin/goodsCategory/add';
  Api['EDIT_CATEGORY'] = '/goods/admin/goodsCategory/update';
  Api['DEL_CATEGORY'] = '/goods/admin/goodsCategory/delete';
  Api['UPDATE_CATEGORY'] = '/goods/admin/goodsCategory/categoryInit';

  // 品类管理
  Api['GET_BRAND_LIST'] = '/goods/admin/goodsBrand/list';
  Api['ADD_BRAND'] = '/goods/admin/goodsBrand/add';
  Api['EDIT_BRAND'] = '/goods/admin/goodsBrand/update';
  Api['DEL_BRAND'] = '/goods/admin/goodsBrand/delete';
  Api['AUDIT_BRAND'] = '/goods/admin/goodsBrand/audit';
  Api['GET_APPLY_BRAND_LIST'] = '/goods/admin/goodsBrand/applyList';

  // 属性管理
  Api['ATTR_LIST'] = '/goods/admin/goodsAttribute/list';
  Api['ADD_ATTR'] = '/goods/admin/goodsAttribute/add';
  Api['EDIT_ATTR'] = '/goods/admin/goodsAttribute/update';
  Api['DEL_ATTR'] = '/goods/admin/goodsAttribute/delete';

  // 标签管理
  Api['LABEL_LIST'] = '/goods/admin/goodsLabel/list';
  Api['ADD_LABEL'] = '/goods/admin/goodsLabel/add';
  Api['EDIT_LABEL'] = '/goods/admin/goodsLabel/update';
  Api['DEL_LABEL'] = '/goods/admin/goodsLabel/delete';

  // 商品资料库
  Api['GET_GOODS_PALTFORM_LIST'] = '/goods/admin/goods/platform/list';
  Api['LOCKUP_GOODS_PALTFORM'] = '/goods/admin/goods/platform/lockup';
  Api['PUBLISH_GOODS_PALTFORM'] = '/goods/admin/goods/platform/publish';
  Api['DEL_GOODS_PALTFORM'] = '/goods/admin/goods/platform/deleteGoods';
  Api['GET_BOTTOM_CATEGORY'] = '/goods/admin/goodsCategory/bottomCategory';
  Api['GET_STORE_PALTFORM_LIST'] = '/goods/admin/goods/platform/storeGoodsList';
  Api['IMPORT_STORE_PALTFORM'] = '/goods/admin/goods/platform/storeGoodsImport';
  Api['DOWNLOAD_STORE_PALTFORM'] = '/goods/admin/goods/platform/download';
  Api['IMPORT_EXCEL_STORE_PALTFORM'] = '/goods/admin/goods/platform/import';
  Api['GET_GOODS_SPEC_LIST'] = '/goods/admin/goodsSpec/list';
  Api['ADD_GOODS_SPEC'] = '/goods/admin/goodsSpec/add',
  Api['ADD_GOODS_SPEC_VAL'] = '/goods/admin/goodsSpec/addSpecValue',
  Api['GET_CATEGORY_ATTR_BRAND'] = '/goods/admin/goodsCategory/getCategory';
  Api['ADD_PLATFORM_GOOD'] = '/goods/admin/goods/platform/add';
  Api['EDIT_PLATFORM_GOOD'] = '/goods/admin/goods/platform/update';
  Api['GET_PLATFORM_GOOD_DETAIL'] = '/goods/admin/goods/platform/detail';

  // 自营店铺
  Api['STORE_OWN_LIST'] = '/seller/admin/ownStore/list';
  Api['STORE_OWN_DETAIL'] = '/seller/admin/ownStore/detail';
  Api['ADD_STORE_OWN'] = '/seller/admin/ownStore/add';
  Api['EDIT_STORE_OWN'] = '/seller/admin/ownStore/update';
  Api['DEL_STORE_OWN'] = '/seller/admin/ownStore/delete';
  Api['LOCK_STORE_OWN'] = '/seller/admin/ownStore/lockUp';
  Api['RESET_STORE_OWN'] = '/seller/admin/store/resetPassword';
  Api['UPDATE_STORE_CAPACITYSIZE'] = '/seller/admin/ownStore/updateCapacitySize';

  // 入驻店铺
  Api['GET_STORE_LIST'] = '/seller/admin/store/list';
  Api['GET_STORE_DETAIL'] = '/seller/admin/store/detail';
  Api['EDIT_STORE'] = '/seller/admin/store/update';
  Api['RESET_STORE_PWD'] = '/seller/admin/store/resetPassword';
  Api['SET_STORE_PAY_DAY'] = '/seller/admin/store/setBillDate';
  Api['GET_APPLY_STORE_DETAIL'] = '/seller/admin/storeAudit/detail';
  Api['GET_EXPIRY_STORE_LIST'] = '/seller/admin/store/expiryList';
  Api['GET_AUDIT_STORE_LIST'] = '/seller/admin/storeAudit/list';
  Api['SET_AUDIT_STORE'] = '/seller/admin/storeAudit/audit';
  Api['GET_RENEW_STORE_LIST'] = '/seller/admin/storeRenew/list';
  Api['DEL_STORE_RENEW'] = '/seller/admin/storeRenew/delRenew';
  Api['GET_AUDIT_CATE_LIST'] = '/seller/admin/cateAudit/list';
  Api['AUDIT_STORE_CATE'] = '/seller/admin/cateAudit/audit';

  // 店铺等级
  Api['GET_OPEN_TIME'] = '/seller/admin/store/openTime';
  Api['STORE_GRADE_LIST'] = '/seller/admin/storeGrade/list';
  Api['ADD_STORE_GRADE'] = '/seller/admin/storeGrade/add';
  Api['EDIT_STORE_GRADE'] = '/seller/admin/storeGrade/update';
  Api['DEL_STORE_GRADE'] = '/seller/admin/storeGrade/delete';

  //订单管理
  Api['GET_ORDER_LIST'] = '/business/admin/orderInfo/list';
  Api['GET_ORDER_DETAIL'] = '/business/admin/orderInfo/detail';
  Api['EXPORT_ORDER'] = '/business/admin/orderInfo/export';
  Api['GET_SERVICE_LIST'] = '/business/admin/after/sale/list';
  Api['GET_SERVICE_DETAIL'] = '/business/admin/after/sale/detail';
  Api['EXPORT_SERVICE'] = '/business/admin/after/sale/export';
  Api['CONFIRM_SERVICE'] = '/business/admin/after/sale/confirmRefund';
  Api['GET_GOOD_EVALUATION'] = '/goods/admin/goodsComment/list';
  Api['DEL_GOOD_EVALUATION'] = '/goods/admin/goodsComment/delete';
  Api['GET_STORE_EVALUATION'] = '/seller/admin/storeComment/list';
  Api['DEL_STORE_EVALUATION'] = '/seller/admin/storeComment/delete';
  Api['GET_REASON_LIST'] = '/system/admin/reason/list';
  Api['SWITCH_REASON_SHOW'] = '/system/admin/reason/isShow';
  Api['ADD_REASON'] = '/system/admin/reason/add';
  Api['DEL_REASON'] = '/system/admin/reason/delete';
  Api['UPDATE_REASON'] = '/system/admin/reason/update';
  
  // 结算账单
  Api['GET_BILL_LIST'] = '/system/admin/bill/list';
  Api['GET_BILL_DETAIL'] = '/system/admin/bill/detail';
  Api['APPROVED_BILL'] = '/system/admin/bill/approved';
  Api['CONFIRM_PAYMENT_BILL'] = '/system/admin/bill/confirmPayment';
  Api['BATCH_CONFIRM_PAYMENT'] = '/system/admin/bill/batchConfirmPayment';
  Api['EXPORT_BILL_LIST'] = '/system/admin/bill/exportBillList';
  Api['EXPORT_BILL_DETAIL'] = '/system/admin/bill/exportBillDetail';
  Api['TRIGGER_BILL'] = '/system/common/bill'; // 手动触发账单
  Api['GET_ARTICLE_CATEGORY_LIST'] = '/cms/admin/articleCategory/list';
  Api['ADD_ARTICLE_CATEGORY'] = '/cms/admin/articleCategory/add';
  Api['EDIT_ARTICLE_CATEGORY'] = '/cms/admin/articleCategory/update';
  Api['DEL_ARTICLE_CATEGORY'] = '/cms/admin/articleCategory/delete';
  Api['GET_ARTICLE_LIST'] = '/cms/admin/article/list';
  Api['GET_ARTICLE_DETAIL'] = '/cms/admin/article/detail';
  Api['ADD_ARTICLE'] = '/cms/admin/article/add';
  Api['EDIT_ARTICLE'] = '/cms/admin/article/update';
  Api['DEL_ARTICLE'] = '/cms/admin/article/delete';

  //素材管理
  Api['GET_FIRST_CATEGORY'] = '/system/admin/material/category/firstCategory';
  Api['GET_MATERIAL_FILE_LIST'] = '/system/admin/material/file/list';
  Api['GET_MATERIAL_CATEGORY_LIST'] = '/system/admin/material/category/list';
  Api['ADD_MATERIAL_CATEGORY'] = '/system/admin/material/category/add';
  Api['UPDATE_MATERIAL_CATEGORY'] = '/system/admin/material/category/update';
  Api['DEL_MATERIAL_CATEGORY'] = '/system/admin/material/category/delete';
  Api['RENAME_MATERIAL'] = '/system/admin/material/file/rename';
  Api['MOVETO_MATERIAL'] = '/system/admin/material/file/moveTo';
  Api['CLEAN_MATERIAL'] = '/system/admin/material/file/cleanMaterial';
  Api['FILE_BIND_CATEGORY'] = '/system/admin/material/file/fileBindCategory';
})(Api || (Api = {}));

//获取商品设置配置
export const getProductSetting = (params) => defHttp.get({ url: Api.PRODUCT_SETTING, params });

//立即更新商品数据
export const updateProduct = () => defHttp.get({ url: Api.ESINIT_PRODUCT });

//保存站点基本配置
export const saveBasicSiteSetting = (params) =>
  defHttp.post({
    url: Api.UPDATE_SITE_SETTING,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取商品管理列表
export const getGoodsList = (params) => defHttp.get({ url: Api.GET_GOODS_LIST, params });

//获取商品详情
export const getGoodsDetail = (params) => defHttp.get({ url: Api.GET_GOODS_DETAIL, params });

//商品违规下架
export const lockOffGoods = (params) =>
  defHttp.post({
    url: Api.LOCKOFF,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//审核通过
export const auditGood = (params) =>
  defHttp.post({
    url: Api.AUDIT_GOOD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取分类树数据
export const getCategoryTree = (params) => defHttp.get({ url: Api.GET_CATEGORY_TREE, params });

//获取分类数据
export const getCategoryList = (params) => defHttp.get({ url: Api.GET_CATEGORY_LIST, params });

//添加分类
export const addCategory = (params) =>
  defHttp.post({
    url: Api.ADD_CATEGORY,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//编辑分类
export const editCategory = (params) =>
  defHttp.post({
    url: Api.EDIT_CATEGORY,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//更新分类
export const delCategory = (params) =>
  defHttp.post({
    url: Api.DEL_CATEGORY,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//更新分类
export const updateCategory = () => defHttp.get({ url: Api.UPDATE_CATEGORY });

//获取品牌列表
export const getBrandList = (params) => defHttp.get({ url: Api.GET_BRAND_LIST, params });

//新增品牌
export const addBrand = (params) =>
  defHttp.post({
    url: Api.ADD_BRAND,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//编辑品牌
export const editBrand = (params) =>
  defHttp.post({
    url: Api.EDIT_BRAND,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//删除品牌
export const delBrand = (params) =>
  defHttp.post({
    url: Api.DEL_BRAND,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
  
//审核品牌
export const auditBrand = (params) =>
defHttp.post({
  url: Api.AUDIT_BRAND,
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
  },
});

//获取待审核品牌列表
export const getApplyBrandList = (params) => defHttp.get({ url: Api.GET_APPLY_BRAND_LIST, params });

//获取属性列表
export const getAttrList = (params) => defHttp.get({ url: Api.ATTR_LIST, params });

//新增属性
export const addAttr = (params) =>
  defHttp.post({
    url: Api.ADD_ATTR,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//编辑属性
export const editAttr = (params) =>
  defHttp.post({
    url: Api.EDIT_ATTR,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//删除属性
export const delAttr = (params) =>
  defHttp.post({
    url: Api.DEL_ATTR,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取标签列表
export const getLabelList = (params) => defHttp.get({ url: Api.LABEL_LIST, params });

//新增标签
export const addLabel = (params) =>
  defHttp.post({
    url: Api.ADD_LABEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//编辑标签
export const editLabel = (params) =>
  defHttp.post({
    url: Api.EDIT_LABEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//删除标签
export const delLabel = (params) =>
  defHttp.post({
    url: Api.DEL_LABEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取商品资料库商品列表
export const getGoodsPlatformList = (params) =>
  defHttp.get({ url: Api.GET_GOODS_PALTFORM_LIST, params });

//下架商品资料库商品
export const lockupGoodsPlatform = (params) =>
  defHttp.post({
    url: Api.LOCKUP_GOODS_PALTFORM,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//登商品资料库商品
export const publishGoodsPlatform = (params) =>
  defHttp.post({
    url: Api.PUBLISH_GOODS_PALTFORM,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//删除商品资料库商品
export const delGoodsPlatform = (params) =>
  defHttp.post({
    url: Api.DEL_GOODS_PALTFORM,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取商品资料库商品分类
export const getGoodsPlatformCategory = (params) =>
  defHttp.get({ url: Api.GET_BOTTOM_CATEGORY, params });

//获取添加商品资料列表
export const getStorePlatformList = (params) =>
  defHttp.get({ url: Api.GET_STORE_PALTFORM_LIST, params });

//导入商品资料
export const importPlatform = (params) =>
  defHttp.post({
    url: Api.IMPORT_STORE_PALTFORM,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//下载商品导入模板
export const downloadPlatform = (params) =>
  defHttp.get({
    url: Api.DOWNLOAD_STORE_PALTFORM,
    params,
    responseType: 'blob',
  });

//商品资料库上传文件
export const importExcelPlateform = (params) =>
  defHttp.post({
    url: Api.IMPORT_EXCEL_STORE_PALTFORM,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取商品规格数据
export const getGoodsSpecList = (params) => defHttp.get({ url: Api.GET_GOODS_SPEC_LIST, params });

//新增商品规格名
export const addGoodsSpec = (params) =>
  defHttp.post({
    url: Api.ADD_GOODS_SPEC,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//新增商品规格值
export const addGoodsSpecVal = (params) =>
  defHttp.post({
    url: Api.ADD_GOODS_SPEC_VAL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//根据分类获取属性和品牌数据
export const getCategoryAttrBrand = (params) =>
  defHttp.get({ url: Api.GET_CATEGORY_ATTR_BRAND, params });

//新增商品资料
export const addPlatformGood = (params) =>
  defHttp.post({
    url: Api.ADD_PLATFORM_GOOD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });

//新增商品资料
export const editPlatformGood = (params) =>
  defHttp.post({
    url: Api.EDIT_PLATFORM_GOOD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });

//获取商品资料详情
export const getPlatformGoodDetail = (params) => defHttp.get({ url: Api.GET_PLATFORM_GOOD_DETAIL, params });

//获取自营店铺列表
export const getOwnList = (params) => defHttp.get({ url: Api.STORE_OWN_LIST, params });

//获取自营店铺详情
export const getOwnDetail = (params) => defHttp.get({ url: Api.STORE_OWN_DETAIL, params });

//新增自营店铺
export const addOwn = (params) =>
  defHttp.post({
    url: Api.ADD_STORE_OWN,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//编辑自营店铺
export const editOwn = (params) =>
  defHttp.post({
    url: Api.EDIT_STORE_OWN,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//删除自营店铺
export const delOwn = (params) =>
  defHttp.post({
    url: Api.DEL_STORE_OWN,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//自营店铺重置密码
export const resetOwn = (params) =>
  defHttp.post({
    url: Api.RESET_STORE_OWN,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//更新自营店铺状态
export const lockOwn = (params) =>
  defHttp.post({
    url: Api.LOCK_STORE_OWN,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//更新素材中心容量
export const updateCapacitySize = (params) =>
  defHttp.post({
    url: Api.UPDATE_STORE_CAPACITYSIZE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取入驻店铺列表
export const getStoreList = (params) => defHttp.get({ url: Api.GET_STORE_LIST, params });

//获取入驻店铺详情
export const getStoreDetail = (params) => defHttp.get({ url: Api.GET_STORE_DETAIL, params });

//编辑店铺信息
export const editStore = (params) =>
  defHttp.post({
    url: Api.EDIT_STORE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });

//重置店铺密码
export const resetStorePwd = (params) =>
  defHttp.post({
    url: Api.RESET_STORE_PWD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//设置结算日
export const setStorePayday = (params) =>
  defHttp.post({
    url: Api.SET_STORE_PAY_DAY,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取入驻店铺审核详情
export const getApplyStoreDetail = (params) =>
  defHttp.get({ url: Api.GET_APPLY_STORE_DETAIL, params });

//获取临效期店铺列表
export const getExpiryStoreList = (params) =>
  defHttp.get({ url: Api.GET_EXPIRY_STORE_LIST, params });

//获取入驻审核店铺列表
export const getAuditStoreList = (params) => defHttp.get({ url: Api.GET_AUDIT_STORE_LIST, params });

//入驻审核店铺审核
export const setAuditStore = (params) =>
  defHttp.post({
    url: Api.SET_AUDIT_STORE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取续签管理列表
export const getRenewStoreList = (params) => defHttp.get({ url: Api.GET_RENEW_STORE_LIST, params });

//删除续签数据
export const delStoreRenew = (params) =>
  defHttp.post({
    url: Api.DEL_STORE_RENEW,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取经营类目审核列表
export const getAuditCateList = (params) => defHttp.get({ url: Api.GET_AUDIT_CATE_LIST, params });

//经营类目审核
export const auditStoreCate = (params) =>
  defHttp.post({
    url: Api.AUDIT_STORE_CATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取开店时长
export const getOpenTime = () => defHttp.get({ url: Api.GET_OPEN_TIME });

//获取店铺等级
export const getStoreGrade = (params) => defHttp.get({ url: Api.STORE_GRADE_LIST, params });

//新增店铺等级
export const addStoreGrade = (params) =>
  defHttp.post({
    url: Api.ADD_STORE_GRADE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//编辑店铺等级
export const editStoreGrade = (params) =>
  defHttp.post({
    url: Api.EDIT_STORE_GRADE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//删除店铺等级
export const delStoreGrade = (params) =>
  defHttp.post({
    url: Api.DEL_STORE_GRADE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取订单列表
export const getOrderList = (params) => defHttp.get({ url: Api.GET_ORDER_LIST, params });

//获取订单详情
export const getOrderDetail = (params) => defHttp.get({ url: Api.GET_ORDER_DETAIL, params });

//导出订单列表
export const exportOrder = (params) =>
  defHttp.get({
    url: Api.EXPORT_ORDER,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
    responseType: 'blob',
  });

//获取售后管理列表
export const getServiceList = (params) => defHttp.get({ url: Api.GET_SERVICE_LIST, params });

//获取售后管理详情
export const getServiceDetail = (params) => defHttp.get({ url: Api.GET_SERVICE_DETAIL, params });

//导出售后管理列表数据
export const exportService = (params) => defHttp.get({ url: Api.EXPORT_SERVICE, params,responseType: 'blob', });

//确认售后管理退款
export const confirmService = (params) =>
  defHttp.post({
    url: Api.CONFIRM_SERVICE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取商品评价列表
export const getGoodEvaluation = (params) => defHttp.get({ url: Api.GET_GOOD_EVALUATION, params });

//删除商品评价
export const delGoodEvaluation = (params) =>
  defHttp.post({
    url: Api.DEL_GOOD_EVALUATION,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取店铺评价列表
export const getStoreEvaluation = (params) =>
  defHttp.get({ url: Api.GET_STORE_EVALUATION, params });

//删除店铺评价
export const delStoreEvaluation = (params) =>
  defHttp.post({
    url: Api.DEL_STORE_EVALUATION,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取原因列表
export const getReasonList = (params) => defHttp.get({ url: Api.GET_REASON_LIST, params });

//切换原因状态
export const switchReason = (params) =>
  defHttp.post({
    url: Api.SWITCH_REASON_SHOW,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//新增原因
export const addReason = (params) =>
  defHttp.post({
    url: Api.ADD_REASON,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//删除原因
export const delReason = (params) =>
  defHttp.post({
    url: Api.DEL_REASON,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//编辑原因
export const updateReason = (params) =>
  defHttp.post({
    url: Api.UPDATE_REASON,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取结算帐单列表
export const getBillList = (params) => defHttp.get({ url: Api.GET_BILL_LIST, params });

//获取结算帐单详情
export const getBillDetail = (params) => defHttp.get({ url: Api.GET_BILL_DETAIL, params });

//审核结算单
export const approvedBill = (params) =>
  defHttp.post({
    url: Api.APPROVED_BILL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//审核结算单
export const confirmPaymentBill = (params) =>
  defHttp.post({
    url: Api.CONFIRM_PAYMENT_BILL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//批量打款
export const batchConfirmPayment = (params) =>
  defHttp.post({
    url: Api.BATCH_CONFIRM_PAYMENT,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });

//手动结算账单
export const triggerBill = (params) => defHttp.get({ url: Api.TRIGGER_BILL, params });
  
//导出结算帐单列表
export const exportBillList = (params) => defHttp.get({
  url: Api.EXPORT_BILL_LIST,
  params,
  responseType: 'blob',
});

//导出结算单明细
export const exportBillDetail = (params) => defHttp.get({
  url: Api.EXPORT_BILL_DETAIL,
  params,
  responseType: 'blob',
});

//获取文章分类列表
export const getArticleCategoryList = (params) =>
  defHttp.get({ url: Api.GET_ARTICLE_CATEGORY_LIST, params });

//新增文章分类
export const addArticleCategory = (params) =>
  defHttp.post({
    url: Api.ADD_ARTICLE_CATEGORY,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//编辑文章分类
export const editArticleCategory = (params) =>
  defHttp.post({
    url: Api.EDIT_ARTICLE_CATEGORY,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//删除文章分类
export const delArticleCategory = (params) =>
  defHttp.post({
    url: Api.DEL_ARTICLE_CATEGORY,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取文章列表
export const getArticleList = (params) => defHttp.get({ url: Api.GET_ARTICLE_LIST, params });

//获取文章详情
export const getArticleDetail = (params) => defHttp.get({ url: Api.GET_ARTICLE_DETAIL, params });

//新增文章
export const addArticle = (params) =>
  defHttp.post({
    url: Api.ADD_ARTICLE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//编辑文章
export const editArticle = (params) =>
  defHttp.post({
    url: Api.EDIT_ARTICLE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//删除文章
export const delArticle = (params) =>
  defHttp.post({
    url: Api.DEL_ARTICLE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取素材中心一级分类列表
export const getFirstCategory = (params) => defHttp.get({ url: Api.GET_FIRST_CATEGORY, params });

//获取素材中心列表
export const getMaterialFileList = (params) =>
  defHttp.get({ url: Api.GET_MATERIAL_FILE_LIST, params });

//获取分类下级分类数据
export const getMaterialCategoryList = (params) =>
  defHttp.get({ url: Api.GET_MATERIAL_CATEGORY_LIST, params });

//添加素材分类
export const addMaterialCategory = (params) =>
  defHttp.post({
    url: Api.ADD_MATERIAL_CATEGORY,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//编辑素材分类
export const updateMaterialCategory = (params) =>
  defHttp.post({
    url: Api.UPDATE_MATERIAL_CATEGORY,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//删除素材分类
export const delMaterialCategory = (params) =>
  defHttp.post({
    url: Api.DEL_MATERIAL_CATEGORY,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//重命名素材名称
export const renameMaterial = (params) =>
  defHttp.post({
    url: Api.RENAME_MATERIAL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//移动素材分类
export const moveToMaterial = (params) =>
  defHttp.post({
    url: Api.MOVETO_MATERIAL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//删除素材分类
export const cleanMaterial = (params) =>
  defHttp.post({
    url: Api.CLEAN_MATERIAL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//上传素材绑定分类
export const fileBindCategory = (params) =>
  defHttp.post({
    url: Api.FILE_BIND_CATEGORY,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
