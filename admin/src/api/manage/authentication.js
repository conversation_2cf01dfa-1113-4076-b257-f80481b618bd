import { defHttp } from '@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const API = {
  GET_ACCOUNT_TL_DETAIL: '/seller/admin/openAccountTl/detail',
  GET_ACCOUNT_TL_SIGN: '/seller/admin/openAccountTl/sign',
  GET_ACCOUNT_TL_BIND_PHONE: '/seller/admin/openAccountTl/bindPhone',
  GET_ACCOUNT_TL_SEND_CODE: '/seller/admin/openAccountTl/sendCode',
  GET_ACCOUNT_TL_COMPANY_STEP_ONE: '/seller/admin/openAccountTl/companyStep1',
};

// 查看开户详情
export const getAccountTlDetailApi = (params) => defHttp.get({ 
  url: API.GET_ACCOUNT_TL_DETAIL,
  params,
  headers: {
  // @ts-ignore
  'Content-Type': ContentTypeEnum.FORM_URLENCODED,
  },
 });

// 签约
export const getAccountTlSignApi = (params) => defHttp.get({ 
  url: API.GET_ACCOUNT_TL_SIGN,
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
});

// 绑定手机号
export const getAccountTlBindPhoneApi = (params) => defHttp.post({ 
  url: API.GET_ACCOUNT_TL_BIND_PHONE, 
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
 });

// 绑定手机号发验证码
export const getAccountTlSendCodeApi = (params) => defHttp.post({ 
  url: API.GET_ACCOUNT_TL_SEND_CODE, 
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
});

// 企业认证
export const getAccountTlCompanyStepApi = (params) => defHttp.post({ 
  url: API.GET_ACCOUNT_TL_COMPANY_STEP_ONE, 
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
 });
