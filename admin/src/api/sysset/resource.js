import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  GET_RESOURCE_LIST: '/system/admin/resource/handel/list',
  GET_ROLE_BIND_RESOURCE: '/system/admin/resource/handel/roleBindResource',
  GET_RESOURCE_ADD: '/system/admin/resource/handel/add',
  GET_RESOURCE_UPDATE: '/system/admin/resource/handel/update',
  GET_RESOURCE_DELETE: '/system/admin/resource/handel/delete',
  GET_RESOURCE_CANCEL_BIND: '/system/admin/resource/handel/cancelBind',
  GET_RESOURCE_GRADE_LIST: '/system/admin/resource/handel/grade4List',
  GET_RESOURCE_BIND: '/system/admin/resource/handel/bindResource',
  GET_RESOURCE_PUT_URL_TO_DB: '/system/admin/resource/handel/putUrlToDb',
};

//slodon_获取资源树
export const getResourceListApi = (params) =>
  defHttp.get({
    url: Api.GET_RESOURCE_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_更新超级管理员权限
export const getRoleBindResourceApi = (params) =>
  defHttp.post({
    url: Api.GET_ROLE_BIND_RESOURCE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_添加资源
export const getResourceAddApi = (params) =>
  defHttp.post({
    url: Api.GET_RESOURCE_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });


//slodon_编辑资源
export const getResourceUpdateApi = (params) =>
  defHttp.post({
    url: Api.GET_RESOURCE_UPDATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_删除资源
export const getResourceDeleteApi = (params) =>
  defHttp.post({
    url: Api.GET_RESOURCE_DELETE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_四级资源解除绑定
export const getResourceCancelBindApi = (params) =>
  defHttp.post({
    url: Api.GET_RESOURCE_CANCEL_BIND,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取未绑定的4级资源树
export const getResourceGradeListApi = (params) =>
  defHttp.get({
    url: Api.GET_RESOURCE_GRADE_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_绑定四级资源
export const getResourceBindApi = (params) =>
  defHttp.post({
    url: Api.GET_RESOURCE_BIND,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_更新四级资源
export const getResourcePutToDbApi = (params) =>
  defHttp.post({
    url: Api.GET_RESOURCE_PUT_URL_TO_DB,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

