import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
var Api;
(function (Api) {
  Api['GET_SENSITIVE_WORD_LIST'] = '/system/admin/sensitiveWord/word/list';
  Api['GET_SENSITIVE_WORD_CATEGORY_LIST'] = '/system/admin/sensitiveWord/category/list';
  Api['GET_SENSITIVE_WORD_EDIT_STATE'] = '/system/admin/sensitiveWord/word/editState';
  Api['GET_SENSITIVE_WORD_DEL'] = '/system/admin/sensitiveWord/word/delete';
  Api['GET_SENSITIVE_WORD_ADD'] = '/system/admin/sensitiveWord/word/add';
  Api['GET_SENSITIVE_WORD_EDIT'] = '/system/admin/sensitiveWord/word/update';
  Api['GET_SENSITIVE_WORD_INIT'] = '/system/admin/sensitiveWord/word/wordInit';
  Api['GET_SENSITIVE_WORD_BATCH_ADD'] = '/system/admin/sensitiveWord/word/batchAdd';
  Api['GET_SENSITIVE_WORD_CATEGORY_ADD'] = '/system/admin/sensitiveWord/category/add';
  Api['GET_SENSITIVE_WORD_CATEGORY_EDIT'] = '/system/admin/sensitiveWord/category/update';
  Api['GET_SENSITIVE_WORD_CATEGORY_EDIT_STATE'] = '/system/admin/sensitiveWord/category/updateState';
  Api['GET_SENSITIVE_WORD_CATEGORY_DEL'] = '/system/admin/sensitiveWord/category/delete';
  Api['GET_SENSITIVE_WORD_LOG_LIST'] = '/system/admin/sensitiveWord/log/list';
  Api['GET_SENSITIVE_WORD_LOG_EXPORT'] = '/system/admin/sensitiveWord/log/export';
})(Api || (Api = {}));

//slodon_获取敏感词词库列表
export const getSensitiveWordListApi = (params) =>
  defHttp.get({
    url: Api.GET_SENSITIVE_WORD_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取敏感词分类列表
export const getSensitiveWordCategoryListApi = (params) =>
  defHttp.get({
    url: Api.GET_SENSITIVE_WORD_CATEGORY_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

 //slodon_编辑敏感词状态
export const getSensitiveWordEditStateApi = (params) =>
  defHttp.post({
    url: Api.GET_SENSITIVE_WORD_EDIT_STATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

 //slodon_删除敏感词
export const getSensitiveWordDelApi = (params) =>
  defHttp.post({
    url: Api.GET_SENSITIVE_WORD_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_添加敏感词
export const getSensitiveWordAddApi = (params) =>
  defHttp.post({
    url: Api.GET_SENSITIVE_WORD_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_编辑敏感词
export const getSensitiveWordEditApi = (params) =>
defHttp.post({
  url: Api.GET_SENSITIVE_WORD_EDIT,
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
  },
});

//slodon_更新敏感词词库列表
export const getSensitiveWordInitApi = (params) =>
defHttp.get({
  url: Api.GET_SENSITIVE_WORD_INIT,
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
  },
});

//slodon_批量添加敏感词
export const getSensitiveWordBatchAddApi = (params) =>
defHttp.post({
  url: Api.GET_SENSITIVE_WORD_BATCH_ADD,
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
  },
});

//slodon_新增分类
export const getSensitiveWordCategoryAddApi = (params) =>
defHttp.post({
  url: Api.GET_SENSITIVE_WORD_CATEGORY_ADD,
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
  },
});

//slodon_编辑分类
export const getSensitiveWordCategoryEditApi = (params) =>
defHttp.post({
  url: Api.GET_SENSITIVE_WORD_CATEGORY_EDIT,
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
  },
});

 //slodon_编辑分类状态
export const getSensitiveWordCategoryEditStateApi = (params) =>
defHttp.post({
  url: Api.GET_SENSITIVE_WORD_CATEGORY_EDIT_STATE,
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
  },
});

//slodon_删除分类
export const getSensitiveWordCategoryDelApi = (params) =>
defHttp.post({
  url: Api.GET_SENSITIVE_WORD_CATEGORY_DEL,
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
  },
});

//slodon_敏感词命中记录列表
export const getSensitiveWordLogListApi = (params) =>
defHttp.get({
  url: Api.GET_SENSITIVE_WORD_LOG_LIST,
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
  },
});

//slodon_敏感词命中记录列表导出
export const getSensitiveWordLogExportApi = (params) =>
defHttp.get({
  url: Api.GET_SENSITIVE_WORD_LOG_EXPORT,
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
  },
  responseType: 'blob',
});