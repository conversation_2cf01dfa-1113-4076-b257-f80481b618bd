import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  GET_RESOURCE_LIST: '/system/admin/vendor/resource/list',
  GET_ROLE_BIND_RESOURCE: '/system/admin/vendor/resource/roleBindResource',
  GET_RESOURCE_ADD: '/system/admin/vendor/resource/add',
  GET_RESOURCE_UPDATE: '/system/admin/vendor/resource/update',
  GET_RESOURCE_DELETE: '/system/admin/vendor/resource/delete',
  GET_RESOURCE_CANCEL_BIND: '/system/admin/vendor/resource/cancelBind',
  GET_RESOURCE_GRADE_LIST: '/system/admin/vendor/resource/grade4List',
  GET_RESOURCE_BIND: '/system/admin/vendor/resource/bindResource',
  GET_RESOURCE_PUT_URL_TO_DB: '/system/admin/vendor/resource/putUrlToDb',
};

//slodon_获取资源树
export const getVendorResourceListApi = (params) =>
  defHttp.get({
    url: Api.GET_RESOURCE_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_更新超级管理员权限
export const getVendorRoleBindResourceApi = (params) =>
  defHttp.post({
    url: Api.GET_ROLE_BIND_RESOURCE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_添加资源
export const getVendorResourceAddApi = (params) =>
  defHttp.post({
    url: Api.GET_RESOURCE_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });


//slodon_编辑资源
export const getVendorResourceUpdateApi = (params) =>
  defHttp.post({
    url: Api.GET_RESOURCE_UPDATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_删除资源
export const getVendorResourceDeleteApi = (params) =>
  defHttp.post({
    url: Api.GET_RESOURCE_DELETE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_四级资源解除绑定
export const getVendorResourceCancelBindApi = (params) =>
  defHttp.post({
    url: Api.GET_RESOURCE_CANCEL_BIND,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取未绑定的4级资源树
export const getVendorResourceGradeListApi = (params) =>
  defHttp.get({
    url: Api.GET_RESOURCE_GRADE_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_绑定四级资源
export const getVendorResourceBindApi = (params) =>
  defHttp.post({
    url: Api.GET_RESOURCE_BIND,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_更新四级资源
export const getVendorResourcePutToDbApi = (params) =>
  defHttp.post({
    url: Api.GET_RESOURCE_PUT_URL_TO_DB,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

