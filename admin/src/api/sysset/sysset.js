import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
var Api;
(function (Api) {
  Api['BASIC_SITE_SETTING'] = '/system/admin/setting/getBasicSiteSetting';
  Api['UPDATE_SITE_SETTING'] = '/system/admin/setting/updateSettingList';
  Api['PC_MAIN_IMAGE'] = '/system/admin/setting/getPcMainImage';
  Api['PC_DEFAULT_IMAGE'] = '/system/admin/setting/getPcDefaultImage';
  Api['PC_PAYMENT'] = '/system/admin/setting/getPcPaymentList';
  Api['MOBILE_PAYMENT'] = '/system/admin/setting/getMobilePayment';
  Api['PAY_SETTING'] = '/system/admin/setting/getPaySetting';
  Api['ORDER_LIST_CODE'] = '/system/admin/setting/getOrderListCode';
  Api['GET_SETTING_LIST'] = '/system/admin/setting/getSettingList';
  Api['SMS_SETTING'] = '/system/admin/setting/getSMSSetting';
  Api['EMAIL_SETTING'] = '/system/admin/setting/getSMTPSetting';
  Api['TEST_EMAIL'] = '/msg/admin/msg/push/testEmailSend';
  Api['MEMBER_TP_LIST'] = '/msg/admin/msg/tpl/memberTplList';
  Api['UPDATE_MEMBER_TP_LIST'] = '/msg/admin/msg/tpl/updateMemberTpl';
  Api['UPDATE_STORE_TP_LIST'] = '/msg/admin/msg/tpl/updateStoreTpl';
  Api['STORE_TO_LIST'] = '/msg/admin/msg/tpl/storeTplList';
  Api['WX_SETTING'] = '/system/admin/setting/getLoginWXSetting';
  Api['ROLE_MANAGE_LIST'] = '/system/admin/system/role/list';
  Api['ADD_ROLE'] = '/system/admin/system/role/add';
  Api['UPDATE_ROLE'] = '/system/admin/system/role/update';
  Api['DELETE_ROLE'] = '/system/admin/system/role/delete';
  Api['AUTH_ROLE'] = '/system/admin/system/role/saveRoleResource';
  Api['RESOURCE_LIST'] = '/system/admin/resource/list';
  Api['ADMIN_USER_LIST'] = '/system/admin/adminUser/list';
  Api['ADD_ADMIN_USER'] = '/system/admin/adminUser/add';
  Api['UPDATE_ADMIN_USER'] = '/system/admin/adminUser/update';
  Api['DEL_ADMIN_USER'] = '/system/admin/adminUser/delete';
  Api['FREEZE_ADMIN_USER'] = '/system/admin/adminUser/isFreeze';
  Api['RESET_ADMIN_USER'] = '/system/admin/adminUser/resetPassword';
  Api['ADMIN_LOG_LIST'] = '/system/admin/adminLog/list';
  Api['DEL_ADMIN_LOG'] = '/system/admin/adminLog/delete';
  Api['EXOIRT_ADMIN_LOG'] = '/system/admin/adminLog/export';
  Api['AGREEMENT_LIST'] = '/system/admin/agreement/list';
  Api['AGREEMENT_DETAIL'] = '/system/admin/agreement/detail';
  Api['UPDATE_AGREEMENT'] = '/system/admin/agreement/update';
  Api['EXPRESS_LIST'] = '/system/admin/express/list';
  Api['UPDATE_EXPRESS'] = '/system/admin/express/update';
  Api['DELETE_EXPRESS'] = '/system/admin/express/delete'; 
  Api['EXPRESS_SETTING'] = '/system/admin/setting/getExpressSetting';
})(Api || (Api = {}));
//获取站点基本配置
export const getBasicSiteSetting = () => defHttp.get({ url: Api.BASIC_SITE_SETTING });
//保存站点基本配置
export const saveBasicSiteSetting = (params) =>
  defHttp.post({
    url: Api.UPDATE_SITE_SETTING,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取基本图片设置
export const getBaseImageSet = () => defHttp.get({ url: Api.PC_MAIN_IMAGE });
//获取默认图片设置
export const getdefaultImageSet = () => defHttp.get({ url: Api.PC_DEFAULT_IMAGE });
//获取PC支付配置
export const getPcSetting = () => defHttp.get({ url: Api.PC_PAYMENT });
//获取移动端支付配置
export const getMobileSetting = () => defHttp.get({ url: Api.MOBILE_PAYMENT });
//获取支付配置
export const getPaySetting = (params) => defHttp.get({ url: Api.PAY_SETTING, params });
//获取订单导出字段
export const getOrderOutputCode = () => defHttp.get({ url: Api.ORDER_LIST_CODE });
//获取配置信息
export const getSettingList = (params) => defHttp.get({ url: Api.GET_SETTING_LIST, params });
//获取短信配置
export const getSmsSetting = () => defHttp.get({ url: Api.SMS_SETTING });
//获取邮件配置
export const getEmailSetting = () => defHttp.get({ url: Api.EMAIL_SETTING });
//发送测试邮件
export const testEmail = (params) =>
  defHttp.post({
    url: Api.TEST_EMAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取会员消息模板信息
export const getMemberTpList = () => defHttp.get({ url: Api.MEMBER_TP_LIST });
//更新会员消息模板信息
export const updateMemberTpList = (params) =>
  defHttp.post({
    url: Api.UPDATE_MEMBER_TP_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//更新商户消息模板信息
export const updateStoreTpList = (params) =>
  defHttp.post({
    url: Api.UPDATE_STORE_TP_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取商户消息模板信息
export const getStoreTpList = () => defHttp.get({ url: Api.STORE_TO_LIST });
//获取三方授权登录信息
export const getWxSetting = () => defHttp.get({ url: Api.WX_SETTING });
//获取权限组列表数据
export const getRoleManageList = (params) => defHttp.get({ url: Api.ROLE_MANAGE_LIST, params });
//添加权限组
export const addRole = (params) =>
  defHttp.post({
    url: Api.ADD_ROLE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//编辑权限组
export const updateRole = (params) =>
  defHttp.post({
    url: Api.UPDATE_ROLE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//删除权限组
export const delRole = (params) =>
  defHttp.post({
    url: Api.DELETE_ROLE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//授权权限组
export const authRole = (params) =>
  defHttp.post({
    url: Api.AUTH_ROLE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取权限组管理资源列表数据
export const getRoleResource = () => defHttp.get({ url: Api.RESOURCE_LIST });
//获取管理员管理列表数据
export const getAdminUserList = (params) => defHttp.get({ url: Api.ADMIN_USER_LIST, params });
//新增管理员
export const addAdminUser = (params) =>
  defHttp.post({
    url: Api.ADD_ADMIN_USER,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//编辑管理员
export const updateAdminUser = (params) =>
  defHttp.post({
    url: Api.UPDATE_ADMIN_USER,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//解冻/冻结管理员
export const freezeAdminUser = (params) =>
  defHttp.post({
    url: Api.FREEZE_ADMIN_USER,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//重置密码管理员
export const resetAdminUser = (params) =>
  defHttp.post({
    url: Api.RESET_ADMIN_USER,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//删除管理员
export const delAdminUser = (params) =>
  defHttp.post({
    url: Api.DEL_ADMIN_USER,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取管理员管理列表数据
export const getAdminLogList = (params) => defHttp.get({ url: Api.ADMIN_LOG_LIST, params });
//删除操作日志
export const delAdminLog = (params) =>
  defHttp.post({
    url: Api.DEL_ADMIN_LOG,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//导出操作日志
export const exportAdminLog = (params) => defHttp.get({
  url: Api.EXOIRT_ADMIN_LOG,
  params,
  responseType: 'blob',
});
//获取协议列表数据
export const getAgreementList = (params) => defHttp.get({ url: Api.AGREEMENT_LIST, params });
//获取协议详情数据
export const getAgreementDetail = (params) => defHttp.get({ url: Api.AGREEMENT_DETAIL, params });
//更新协议详情
export const updateAgreement = (params) =>
  defHttp.post({
    url: Api.UPDATE_AGREEMENT,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取物流公司列表数据
export const getExpressList = (params) => defHttp.get({ url: Api.EXPRESS_LIST, params });
//更新物流管理信息
export const updateExpress = (params) =>
  defHttp.post({
    url: Api.UPDATE_EXPRESS,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//删除物流管理信息
export const delExpress = (params) =>
  defHttp.post({
    url: Api.DELETE_EXPRESS,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取物流配置
export const getExpressSetting = () => defHttp.get({ url: Api.EXPRESS_SETTING });
