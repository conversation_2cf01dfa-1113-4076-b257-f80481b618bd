import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  SETTING_LIST: '/system/admin/setting/getSettingList',
  APDATE_SETTING_LIST: '/system/admin/setting/updateSettingList',

  MOBILE_DECO_LIST: '/system/admin/mobileDeco/list',
  ADD_MOBILE_DECO: '/system/admin/mobileDeco/add',
  DEL_MOBILE_DECO: '/system/admin/mobileDeco/delete',
  COPY_MOBILE_DECO: '/system/admin/mobileDeco/copy',
  UPDATE_MOBILE_DECO: '/system/admin/mobileDeco/update',
  IS_USE_MOBILE_DECO: '/system/admin/mobileDeco/isUse',
};

// 获取配置信息
export const getSettingList = (params) => defHttp.get({ url: Api.SETTING_LIST, params });

// 更新配置状态
export const updateSettingList = (params) =>
  defHttp.post({
    url: Api.APDATE_SETTING_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

// 获取首页装修数据
export const mobileDecoList = (params) => defHttp.get({ url: Api.MOBILE_DECO_LIST, params });

// 新增首页装修数据
export const addMobileDeco = (params) =>
  defHttp.post({
    url: Api.ADD_MOBILE_DECO,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

// 删除首页装修数据
export const delMobileDeco = (params) =>
  defHttp.post({
    url: Api.DEL_MOBILE_DECO,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

// 复制首页装修数据
export const copyMobileDeco = (params) =>
  defHttp.post({
    url: Api.COPY_MOBILE_DECO,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

// 编辑首页装修数据
export const updateMobileDeco = (params) =>
  defHttp.post({
    url: Api.UPDATE_MOBILE_DECO,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

// 是否使用
export const isUseMobileDeco = (params) =>
  defHttp.post({
    url: Api.IS_USE_MOBILE_DECO,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
