import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  GET_SPREADER_LABEL_LIST: '/spreader/admin/spreaderGoodsLabel/list',
  GET_SPREADER_LABEL_ADD: '/spreader/admin/spreaderGoodsLabel/add',
  GET_SPREADER_LABEL_EDIT: '/spreader/admin/spreaderGoodsLabel/update',
  GET_SPREADER_LABEL_DEL: '/spreader/admin/spreaderGoodsLabel/delete',
  GET_SPREADER_LABEL_SWITCH: '/spreader/admin/spreaderGoodsLabel/isShow',
};

//获取商品标签列表
export const getSpreaderLabelListApi = (params) =>
  defHttp.get({
    url: Api.GET_SPREADER_LABEL_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//添加商品标签
export const getSpreaderLabelAddApi = (params) =>
  defHttp.post({
    url: Api.GET_SPREADER_LABEL_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//编辑商品标签
export const getSpreaderLabelEditApi = (params) =>
  defHttp.post({
    url: Api.GET_SPREADER_LABEL_EDIT,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//删除商品标签
export const getSpreaderLabelDelApi = (params) =>
  defHttp.post({
    url: Api.GET_SPREADER_LABEL_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//更改商品标签状态
export const getSpreaderLabelSwitchApi = (params) =>
  defHttp.post({
    url: Api.GET_SPREADER_LABEL_SWITCH,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
