import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  GET_SPREADER_EXTEND_LIST: '/spreader/admin/spreaderOrderExtend/list',
};

// slodon_获取订单列表
export const getSpreaderExtendListApi = (params) =>
  defHttp.get({
    url: Api.GET_SPREADER_EXTEND_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
