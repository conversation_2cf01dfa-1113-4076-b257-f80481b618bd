import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  GET_SPREADER_PROBLEM_CATEGORY_LIST: '/spreader/admin/problemCategory/list',
  GET_SPREADER_PROBLEM_CATEGORY_ADD: '/spreader/admin/problemCategory/add',
  GET_SPREADER_PROBLEM_CATEGORY_UPDATE: '/spreader/admin/problemCategory/update',
  GET_SPREADER_PROBLEM_CATEGORY_DEL: '/spreader/admin/problemCategory/delete',
  GET_SPREADER_PROBLEM_LIST: '/spreader/admin/problem/list',
  GET_SPREADER_PROBLEM_IS_SHOW: '/spreader/admin/problem/isShow',
  GET_SPREADER_PROBLEM_DEL: '/spreader/admin/problem/delete',
  GET_SPREADER_PROBLEM_ADD: '/spreader/admin/problem/add',
  GET_SPREADER_PROBLEM_UPDATE: '/spreader/admin/problem/update',
  GET_SPREADER_PROBLEM_DETAIL: '/spreader/admin/problem/detail',
};

//slodon_获取问题分类列表
export const getSpreaderCategoryListApi = (params) =>
  defHttp.get({
    url: Api.GET_SPREADER_PROBLEM_CATEGORY_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_新增问题分类列表
export const getSpreaderCategoryAddApi = (params) =>
  defHttp.post({
    url: Api.GET_SPREADER_PROBLEM_CATEGORY_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_编辑问题分类列表
export const getSpreaderCategoryUpdateApi = (params) =>
  defHttp.post({
    url: Api.GET_SPREADER_PROBLEM_CATEGORY_UPDATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_删除问题分类列表
export const getSpreaderCategoryDelApi = (params) =>
  defHttp.post({
    url: Api.GET_SPREADER_PROBLEM_CATEGORY_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取问题列表
export const getSpreaderProblemListApi = (params) =>
  defHttp.get({
    url: Api.GET_SPREADER_PROBLEM_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_是否显示问题
export const getSpreaderProblemIsShowApi = (params) =>
  defHttp.post({
    url: Api.GET_SPREADER_PROBLEM_IS_SHOW,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_删除问题
export const getSpreaderProblemDelApi = (params) =>
  defHttp.post({
    url: Api.GET_SPREADER_PROBLEM_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_新增问题
export const getSpreaderProblemAddApi = (params) =>
  defHttp.post({
    url: Api.GET_SPREADER_PROBLEM_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_编辑问题
export const getSpreaderProblemUpdateApi = (params) =>
  defHttp.post({
    url: Api.GET_SPREADER_PROBLEM_UPDATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

// slodon_获取问题详情
export const getSpreaderProblemDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_SPREADER_PROBLEM_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
