import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  GET_SPREADER_LIST: '/spreader/admin/spreader/list',
  GET_SPREADER_INVITATION_LIST: '/spreader/admin/spreader/invitationList',
  GET_SPREADER_AUDIT_LIST: '/spreader/admin/auditSpreader/list',
  GET_SPREADER_AUDIT: '/spreader/admin/auditSpreader/audit',
  GET_SPREADER_DEL: '/spreader/admin/auditSpreader/delete',
  GET_SPREADER_GRADE_LIST: '/spreader/admin/spreaderGrade/list',
  GET_SPREADER_GRADE_ADD: '/spreader/admin/spreaderGrade/add',
  GET_SPREADER_GRADE_UPDATE: '/spreader/admin/spreaderGrade/update',
  GET_SPREADER_GRADE_DEL: '/spreader/admin/spreaderGrade/delete',
};

// slodon_获取推手列表
export const getSpreaderListApi = (params) =>
  defHttp.get({
    url: Api.GET_SPREADER_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

// slodon_获取下级推手列表
export const getSpreaderInvitationListApi = (params) =>
  defHttp.get({
    url: Api.GET_SPREADER_INVITATION_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

// slodon_获取推手审核列表
export const getSpreaderAuditListApi = (params) =>
  defHttp.get({
    url: Api.GET_SPREADER_AUDIT_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

// slodon_推手审核
export const getSpreaderAuditApi = (params) =>
  defHttp.post({
    url: Api.GET_SPREADER_AUDIT,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

// slodon_推手审核删除
export const getSpreaderDelApi = (params) =>
  defHttp.post({
    url: Api.GET_SPREADER_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

// slodon_获取等级列表
export const getSpreaderGradeListApi = (params) =>
  defHttp.get({
    url: Api.GET_SPREADER_GRADE_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

// slodon_添加推手等级
export const getSpreaderGradeAddApi = (params) =>
  defHttp.post({
    url: Api.GET_SPREADER_GRADE_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

// slodon_编辑推手等级
export const getSpreaderGradeUpdateApi = (params) =>
  defHttp.post({
    url: Api.GET_SPREADER_GRADE_UPDATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

// slodon_删除推手等级
export const getSpreaderGradeDelApi = (params) =>
  defHttp.post({
    url: Api.GET_SPREADER_GRADE_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
