import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  GET_SPREADER_GOODS_LIST: '/spreader/admin/spreaderGoods/list',
  GET_SPREADER_GOODS_LABEL_LIST: '/spreader/admin/spreaderGoodsLabel/list',
};

// slodon_获取商品列表
export const getSpreaderGoodsListApi = (params) =>
  defHttp.get({
    url: Api.GET_SPREADER_GOODS_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

// slodon_获取商品标签列表
export const getSpreaderGoodsLabelListApi = (params) =>
  defHttp.get({
    url: Api.GET_SPREADER_GOODS_LABEL_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
