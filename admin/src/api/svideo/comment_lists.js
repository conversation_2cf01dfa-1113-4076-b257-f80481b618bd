import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  GET_VIDEO_COMMENT_LIST: '/video/admin/video/comment/list',
  GET_VIDEO_COMMENT_DEL: '/video/admin/video/comment/delete',
  GET_VIDEO_COMMENT_COMMENT_LIST: '/video/admin/video/comment/commentList',
  GET_VIDEO_COMMENT_DEL_COMMENT: '/video/admin/video/comment/delComment',
  GET_VIDEO_COMMENT_REPLY_LIST: '/video/admin/video/comment/replyList',
  GET_VIDEO_COMMENT_DEL_REPLY: '/video/admin/video/comment/delReply',
};
// slodon_获取评论视频列表
export const getVideoCommentListApi = (params) =>
  defHttp.get({
    url: Api.GET_VIDEO_COMMENT_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

// slodon_删除视频评论
export const getVideoCommentDelApi = (params) =>
  defHttp.post({
    url: Api.GET_VIDEO_COMMENT_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取单个视频的所有评论
export const getVideoCommentCommentListApi = (params) =>
  defHttp.get({
    url: Api.GET_VIDEO_COMMENT_COMMENT_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

// slodon_删除一条评论的所有回复
export const getVideoCommentDelCommentApi = (params) =>
  defHttp.post({
    url: Api.GET_VIDEO_COMMENT_DEL_COMMENT,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

// slodon_获取评论的所有回复
export const getVideoCommentReplyApi = (params) =>
  defHttp.get({
    url: Api.GET_VIDEO_COMMENT_REPLY_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

// slodon_获取单条回复
export const getVideoCommentDelReplyApi = (params) =>
  defHttp.post({
    url: Api.GET_VIDEO_COMMENT_DEL_REPLY,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
