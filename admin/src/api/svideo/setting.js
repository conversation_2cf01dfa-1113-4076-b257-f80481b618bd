import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
var Api;
(function (Api) {
  Api['GET_VIDEO_SETTING_LIST'] = '/video/admin/video/setting/getSettingList';
  Api['GET_VIDEO_SETTING_UPDATE'] = '/video/admin/video/setting/updateSettingList';
  Api['GET_SETTING_LIST'] = '/system/admin/setting/getSettingList';
  Api['GET_SETTING_UPDATE'] = '/system/admin/setting/updateSettingList';
})(Api || (Api = {}));
//slodon_批量保存设置信息
export const getVideoSettingUpdateApi = (params) =>
  defHttp.post({
    url: Api.GET_VIDEO_SETTING_UPDATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// slodon_获取短视频配置
export const getVideoSettingListApi = () =>
  defHttp.get({
    url: Api.GET_VIDEO_SETTING_LIST,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//保存系统设置信息
export const getSettingUpdateApi = (params) =>
  defHttp.post({
    url: Api.GET_SETTING_UPDATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_获取系统设置信息
export const getSettingListApi = (params) =>
  defHttp.get({
    url: Api.GET_SETTING_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
