import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
var Api;
(function (Api) {
  Api['GET_VIDEO_LABEL_LIST'] = '/video/admin/video/label/list';
  Api['GET_VIDEO_LABEL_ADD'] = '/video/admin/video/label/add';
  Api['GET_VIDEO_LABEL_EDIT'] = '/video/admin/video/label/update';
  Api['GET_VIDEO_LABEL_DEL'] = '/video/admin/video/label/delete';
  Api['GET_VIDEO_LABEL_SWITCH'] = '/video/admin/video/label/isShow';
})(Api || (Api = {}));
//获取短视频标签列表
export const getVideoLabelListApi = (params) =>
  defHttp.get({
    url: Api.GET_VIDEO_LABEL_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//添加短视频标签
export const getVideoLabelAddApi = (params) =>
  defHttp.post({
    url: Api.GET_VIDEO_LABEL_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//编辑短视频标签
export const getVideoLabelEditApi = (params) =>
  defHttp.post({
    url: Api.GET_VIDEO_LABEL_EDIT,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//删除短视频标签
export const getVideoLabelDelApi = (params) =>
  defHttp.post({
    url: Api.GET_VIDEO_LABEL_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//更改短视频标签状态
export const getVideoLabelSwitchApi = (params) =>
  defHttp.post({
    url: Api.GET_VIDEO_LABEL_SWITCH,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
