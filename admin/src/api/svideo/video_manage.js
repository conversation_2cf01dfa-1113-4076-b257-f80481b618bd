import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  GET_VIDEO_LIST: '/video/admin/video/list',
  GET_VIDEO_DEL: '/video/admin/video/delete',
  GET_VIDEO_IS_SHOW: '/video/admin/video/isShow',
  GET_VIDEO_GOODS_LIST: '/video/admin/video/goodsList',
  GET_VIDEO_AUDIT: '/video/admin/video/audit',
};

// slodon_获取短视频列表
export const getVideoListApi = (params) =>
  defHttp.get({
    url: Api.GET_VIDEO_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

// slodon_删除短视频
export const getVideoApi = (params) =>
  defHttp.post({
    url: Api.GET_VIDEO_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

// slodon_更新短视频状态
export const getVideoIsShowApi = (params) =>
  defHttp.post({
    url: Api.GET_VIDEO_IS_SHOW,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

// slodon_视频审核
export const getVideoAuditApi = (params) =>
  defHttp.post({
    url: Api.GET_VIDEO_AUDIT,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

// slodon_获取视频绑定的商品
export const getVideoGoodsApi = (params) =>
  defHttp.get({
    url: Api.GET_VIDEO_GOODS_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
