import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  GET_VIDEO_AUTHOR_LIST: '/video/admin/video/author/list',
  GET_VIDEO_AUTHOR_IS_PUBLISH: '/video/admin/video/author/isPublish',
  GET_VIDEO_AUTHOR_AUDIT: '/video/admin/video/author/audit',
};

// slodon_获取作者列表
export const getVideoAuthorListApi = (params) =>
  defHttp.get({
    url: Api.GET_VIDEO_AUTHOR_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

// slodon_修改作者的发布状态
export const getVideoAuthorIsPubLishApi = (params) =>
  defHttp.post({
    url: Api.GET_VIDEO_AUTHOR_IS_PUBLISH,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

// slodon_审核作者
export const getVideoAuthorAuditApi = (params) =>
  defHttp.post({
    url: Api.GET_VIDEO_AUTHOR_AUDIT,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
