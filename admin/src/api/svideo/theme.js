import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
var Api;
(function (Api) {
  Api['GET_VIDEO_THEME_LIST'] = '/video/admin/video/theme/list';
  Api['GET_VIDEO_THEME_ADD'] = '/video/admin/video/theme/add';
  Api['GET_VIDEO_THEME_UPDATE'] = '/video/admin/video/theme/update';
  Api['GET_VIDEO_THEME_DETAIL'] = '/video/admin/video/theme/detail';
  Api['GET_VIDEO_THEME_DEL'] = '/video/admin/video/theme/delete';
  Api['GET_VIDEO_THEME_IS_SHOW'] = '/video/admin/video/theme/isShow';
  Api['GET_VIDEO_THEME_VIDEO_LIST'] = '/video/admin/video/theme/videoList';
  Api['GET_VIDEO_THEME_DEL_VIDEO'] = '/video/admin/video/theme/delVideo';
})(Api || (Api = {}));
// slodon_获取推荐主题列表
export const getVideoThemeListApi = (params) =>
  defHttp.get({
    url: Api.GET_VIDEO_THEME_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// slodon_添加推荐主题列表
export const getVideoThemeAddApi = (params) =>
  defHttp.post({
    url: Api.GET_VIDEO_THEME_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// slodon_编辑推荐主题列表
export const getVideoThemeUpdateApi = (params) =>
  defHttp.post({
    url: Api.GET_VIDEO_THEME_UPDATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// slodon_获取推荐主题详情
export const getVideoThemeDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_VIDEO_THEME_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// slodon_删除推荐主题
export const getVideoThemeDelApi = (params) =>
  defHttp.post({
    url: Api.GET_VIDEO_THEME_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// slodon_推荐主题开关
export const getVideoThemeIsShowApi = (params) =>
  defHttp.post({
    url: Api.GET_VIDEO_THEME_IS_SHOW,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// slodon_获取主题绑定的视频列表
export const getVideoThemeVideoListApi = (params) =>
  defHttp.get({
    url: Api.GET_VIDEO_THEME_VIDEO_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// slodon_删除主题绑定的视频
export const getVideoThemeDelVideoApi = (params) =>
  defHttp.post({
    url: Api.GET_VIDEO_THEME_DEL_VIDEO,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
