import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  GET_MOBILE_DECO_LIST: '/system/admin/mobileDeco/list',
  GET_MOBILE_DECO_ADD: '/system/admin/mobileDeco/add',
  GET_MOBILE_DECO_UPDATE: '/system/admin/mobileDeco/update',
  GET_MOBILE_DECO_DEL: '/system/admin/mobileDeco/delete',
  GET_MOBILE_DECO_IS_USE: '/system/admin/mobileDeco/isUse',
  GET_MOBILE_DECO_COPY: '/system/admin/mobileDeco/copy',
};

//slodon_手机装修_获取装修页列表
export const getMobileDecoListApi = (params) =>
  defHttp.get({
    url: Api.GET_MOBILE_DECO_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_手机装修_添加移动端装修页面
export const getMobileDecoAddApi = (params) =>
  defHttp.post({
    url: Api.GET_MOBILE_DECO_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_手机装修_编辑移动端装修页面
export const getMobileDecoUpdateApi = (params) =>
  defHttp.post({
    url: Api.GET_MOBILE_DECO_UPDATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_手机装修_删除移动端装修页面
export const getMobileDecoDelApi = (params) =>
  defHttp.post({
    url: Api.GET_MOBILE_DECO_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_手机装修_启用/停用移动端装修页面
export const getMobileDecoIsUseApi = (params) =>
  defHttp.post({
    url: Api.GET_MOBILE_DECO_IS_USE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_手机装修_复制移动端装修页面
export const getMobileDecoCopyApi = (params) =>
  defHttp.post({
    url: Api.GET_MOBILE_DECO_COPY,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
