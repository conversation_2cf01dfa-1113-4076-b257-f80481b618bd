import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  GET_INTEGRAL_GOODS_LIST: '/integral/admin/integral/goods/list',
  GET_INTEGRAL_GOODS_LOCK_UP: '/integral/admin/integral/goods/lockup',
  GET_INTEGRAL_GOODS_AUDIT: '/integral/admin/integral/goods/audit',
};

//slodon_获取商品列表
export const getIntegralGoodsListApi = (params) =>
  defHttp.get({
    url: Api.GET_INTEGRAL_GOODS_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_违规下架商品
export const getIntegralGoodsLockUpApi = (params) =>
  defHttp.post({
    url: Api.GET_INTEGRAL_GOODS_LOCK_UP,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_审核商品
export const getIntegralGoodsAuditApi = (params) =>
  defHttp.post({
    url: Api.GET_INTEGRAL_GOODS_AUDIT,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
