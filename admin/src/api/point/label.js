import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  GET_INTEGRAL_GET_LABEL_TREE: '/integral/admin/integral/goodsLabel/getLabelTree',
  GET_INTEGRAL_LABEL_LIST: '/integral/admin/integral/goodsLabel/list',
  GET_INTEGRAL_IS_SHOW: '/integral/admin/integral/goodsLabel/isShow',
  GET_INTEGRAL_ADD: '/integral/admin/integral/goodsLabel/add',
  GET_INTEGRAL_UPDATE: '/integral/admin/integral/goodsLabel/update',
  GET_INTEGRAL_DEL: '/integral/admin/integral/goodsLabel/delete',
  GET_INTEGRAL_SET_ADV: '/integral/admin/integral/goodsLabel/setAdv',
};

//slodon_获取积分树数据
export const getIntegralLabelTreeApi = (params) =>
  defHttp.get({
    url: Api.GET_INTEGRAL_GET_LABEL_TREE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取积分标签列表
export const getIntegralLabelListApi = (params) =>
  defHttp.get({
    url: Api.GET_INTEGRAL_LABEL_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_设置积分标签是否显示
export const getIntegralIsShowApi = (params) =>
  defHttp.post({
    url: Api.GET_INTEGRAL_IS_SHOW,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_添加积分标签
export const getIntegralAddApi = (params) =>
  defHttp.post({
    url: Api.GET_INTEGRAL_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_编辑积分标签
export const getIntegralUpdateApi = (params) =>
  defHttp.post({
    url: Api.GET_INTEGRAL_UPDATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_编辑积分标签
export const getIntegralLabelDelApi = (params) =>
  defHttp.post({
    url: Api.GET_INTEGRAL_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_设置广告
export const getIntegralSetAdvApi = (params) =>
  defHttp.post({
    url: Api.GET_INTEGRAL_SET_ADV,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
