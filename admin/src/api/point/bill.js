import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  GET_INTEGRAL_BILL_LIST: '/integral/admin/integralBill/list',
  GET_INTEGRAL_BILL_EXPORT: '/integral/admin/integralBill/export',
  GET_INTEGRAL_BILL_DETAIL: '/integral/admin/integralBill/detail',
  GET_INTEGRAL_BILL_APPROVED: '/integral/admin/integralBill/approved',
  GET_INTEGRAL_BILL_CONFIRM_PAYMENT: '/integral/admin/integralBill/confirmPayment',
  GET_INTEGRAL_BILL_ORDER_DETAIL: '/integral/admin/integral/order/detail',
};

//slodon_获取结算单列表
export const getIntegralBillListApi = (params) =>
  defHttp.get({
    url: Api.GET_INTEGRAL_BILL_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取结算单列表
export const getIntegralBillExportApi = (params) =>
  defHttp.get({
    url: Api.GET_INTEGRAL_BILL_EXPORT,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
    responseType: 'blob',
  });

//slodon_获取结算单详情
export const getIntegralBillDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_INTEGRAL_BILL_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_审核结算单
export const getIntegralBillApprovedApi = (params) =>
  defHttp.post({
    url: Api.GET_INTEGRAL_BILL_APPROVED,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_结算单_打款
export const getIntegralBillConfirmPaymentApi = (params) =>
  defHttp.post({
    url: Api.GET_INTEGRAL_BILL_CONFIRM_PAYMENT,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取订单详情
export const getIntegralBillOrderDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_INTEGRAL_BILL_ORDER_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
