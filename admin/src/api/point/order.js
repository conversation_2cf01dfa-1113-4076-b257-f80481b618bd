import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

const Api = {
  GET_INTEGRAL_BILL_ORDER_DETAIL: '/integral/admin/integral/order/detail',
  GET_INTEGRAL_BILL_LIST: '/integral/admin/integral/order/list',
  GET_INTEGRAL_BILL_EXPORT: '/integral/admin/integral/order/export',
  GET_INTEGRAL_ORDER_GET_TRACE: '/integral/admin/integral/order/getTrace',
};

//slodon_获取订单详情
export const getIntegralBillOrderDetailApi = (params) =>
  defHttp.post({
    url: Api.GET_INTEGRAL_BILL_ORDER_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_获取订单列表
export const getOrderPointListApi = (params) =>
  defHttp.get({
    url: Api.GET_INTEGRAL_BILL_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_订单导出
export const getOrderPointExportApi = (params) =>
  defHttp.get({
    url: Api.GET_INTEGRAL_BILL_EXPORT,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
    responseType: 'blob',
  });
  
//获取物流轨迹
export const getOrderPointTraceApi = (params) =>
defHttp.get({
  url: Api.GET_INTEGRAL_ORDER_GET_TRACE,
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
  },
});