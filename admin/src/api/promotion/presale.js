import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
var Api;
(function (Api) {
  Api['GET_PRESELL_LIST'] = '/promotion/admin/preSell/list';
  Api['GET_PRESELL_INVALID'] = '/promotion/admin/preSell/invalid';
  Api['GET_PRESELL_DEL'] = '/promotion/admin/preSell/delete';
  Api['GET_PRESELL_DETAIL'] = '/promotion/admin/preSell/detail';
  Api['GET_PRESELL_GOOD_LIST'] = '/promotion/admin/preSell/goodList';
  Api['GET_PRESELL_LABEL_LIST'] = '/promotion/admin/preSell/label/list';
  Api['GET_PRESELL_LABEL_ADD'] = '/promotion/admin/preSell/label/add';
  Api['GET_PRESELL_LABEL_EDIT'] = '/promotion/admin/preSell/label/update';
  Api['GET_PRESELL_LABEL_DEL'] = '/promotion/admin/preSell/label/delete';
  Api['GET_PRESELL_LABEL_SWITCH'] = '/promotion/admin/preSell/label/isShow';
})(Api || (Api = {}));
//获取预售活动列表
export const getPreSellListApi = (params) =>
  defHttp.get({
    url: Api.GET_PRESELL_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//失效预售活动
export const getPreSellInvalidApi = (params) =>
  defHttp.post({
    url: Api.GET_PRESELL_INVALID,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//删除预售活动
export const getPreSellDelApi = (params) =>
  defHttp.post({
    url: Api.GET_PRESELL_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取预售活动详情
export const getPreSellDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_PRESELL_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取预售活动的商品
export const getPreSellGoodListApi = (params) =>
  defHttp.get({
    url: Api.GET_PRESELL_GOOD_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取预售标签列表
export const getPreSellLabelListApi = (params) =>
  defHttp.get({
    url: Api.GET_PRESELL_LABEL_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//添加预售标签
export const getPreSellLabelAddApi = (params) =>
  defHttp.post({
    url: Api.GET_PRESELL_LABEL_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//编辑预售标签
export const getPreSellLabelEditApi = (params) =>
  defHttp.post({
    url: Api.GET_PRESELL_LABEL_EDIT,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//删除预售标签
export const getPreSellLabelDelApi = (params) =>
  defHttp.post({
    url: Api.GET_PRESELL_LABEL_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//更改预售标签状态
export const getPreSellLabelSwitchApi = (params) =>
  defHttp.post({
    url: Api.GET_PRESELL_LABEL_SWITCH,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
