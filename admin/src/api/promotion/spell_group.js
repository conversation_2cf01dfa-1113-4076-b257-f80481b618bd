import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
var Api;
(function (Api) {
  Api['GET_SPELL_LIST'] = '/promotion/admin/spell/list';
  Api['GET_SPELL_INVALID'] = '/promotion/admin/spell/invalid';
  Api['GET_SPELL_DEL'] = '/promotion/admin/spell/delete';
  Api['GET_SPELL_DETAIL'] = '/promotion/admin/spell/detail';
  Api['GET_SPELL_GOOD_LIST'] = '/promotion/admin/spell/goodList';
  Api['GET_SPELL_ORDER_LIST'] = '/promotion/admin/spell/order/list';
  Api['GET_SPELL_TEAM_LIST'] = '/promotion/admin/spell/teamList';
  Api['GET_SPELL_LABEL_LIST'] = '/promotion/admin/spell/label/list';
  Api['GET_SPELL_LABEL_ADD'] = '/promotion/admin/spell/label/add';
  Api['GET_SPELL_LABEL_EDIT'] = '/promotion/admin/spell/label/update';
  Api['GET_SPELL_LABEL_DEL'] = '/promotion/admin/spell/label/delete';
  Api['GET_SPELL_LABEL_SWITCH'] = '/promotion/admin/spell/label/isShow';
})(Api || (Api = {}));
//获取拼团活动列表
export const getSpellListApi = (params) =>
  defHttp.get({
    url: Api.GET_SPELL_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//失效拼团活动
export const getSpellInvalidApi = (params) =>
  defHttp.post({
    url: Api.GET_SPELL_INVALID,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//删除拼团活动
export const getSpellDelApi = (params) =>
  defHttp.post({
    url: Api.GET_SPELL_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取拼团活动详情
export const getSpellDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_SPELL_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取拼团活动的商品
export const getSpellGoodListApi = (params) =>
  defHttp.get({
    url: Api.GET_SPELL_GOOD_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取拼团活动的订单
export const getSpellOrderListApi = (params) =>
  defHttp.get({
    url: Api.GET_SPELL_ORDER_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取拼团团队列表
export const getSpellTeamListApi = (params) =>
  defHttp.get({
    url: Api.GET_SPELL_TEAM_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取拼团标签列表
export const getSpellLabelListApi = (params) =>
  defHttp.get({
    url: Api.GET_SPELL_LABEL_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//添加拼团标签
export const getSpellLabelAddApi = (params) =>
  defHttp.post({
    url: Api.GET_SPELL_LABEL_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//编辑拼团标签
export const getSpellLabelEditApi = (params) =>
  defHttp.post({
    url: Api.GET_SPELL_LABEL_EDIT,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//删除拼团标签
export const getSpellLabelDelApi = (params) =>
  defHttp.post({
    url: Api.GET_SPELL_LABEL_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//更改拼团标签状态
export const getSpellLabelSwitchApi = (params) =>
  defHttp.post({
    url: Api.GET_SPELL_LABEL_SWITCH,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
