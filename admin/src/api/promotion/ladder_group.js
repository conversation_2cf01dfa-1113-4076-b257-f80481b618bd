import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
var Api;
(function (Api) {
  Api['GET_LADDER_GROUP_LIST'] = '/promotion/admin/ladder/group/list';
  Api['GET_LADDER_GROUP_INVALID'] = '/promotion/admin/ladder/group/invalid';
  Api['GET_LADDER_GROUP_DEL'] = '/promotion/admin/ladder/group/delete';
  Api['GET_LADDER_GROUP_DETAIL'] = '/promotion/admin/ladder/group/detail';
  Api['GET_LADDER_GROUP_GOOD_LIST'] = '/promotion/admin/ladder/group/teamList';
  Api['GET_LADDER_LABEL_LIST'] = '/promotion/admin/ladderGroup/label/list';
  Api['GET_LADDER_LABEL_ADD'] = '/promotion/admin/ladderGroup/label/add';
  Api['GET_LADDER_LABEL_EDIT'] = '/promotion/admin/ladderGroup/label/update';
  Api['GET_LADDER_LABEL_DEL'] = '/promotion/admin/ladderGroup/label/delete';
  Api['GET_LADDER_LABEL_SWITCH'] = '/promotion/admin/ladderGroup/label/isShow';
})(Api || (Api = {}));
//获取阶梯团活动列表
export const getLadderGroupListApi = (params) =>
  defHttp.get({
    url: Api.GET_LADDER_GROUP_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//失效阶梯团活动
export const getLadderInvalidApi = (params) =>
  defHttp.post({
    url: Api.GET_LADDER_GROUP_INVALID,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//删除阶梯团活动
export const getLadderDelApi = (params) =>
  defHttp.post({
    url: Api.GET_LADDER_GROUP_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取阶梯团活动详情
export const getLadderDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_LADDER_GROUP_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取阶梯团团队列表
export const getLadderGoodListApi = (params) =>
  defHttp.get({
    url: Api.GET_LADDER_GROUP_GOOD_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取阶梯团标签列表
export const getLadderLabelListApi = (params) =>
  defHttp.get({
    url: Api.GET_LADDER_LABEL_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//添加阶梯团标签
export const getLadderLabelAddApi = (params) =>
  defHttp.post({
    url: Api.GET_LADDER_LABEL_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//编辑阶梯团标签
export const getLadderLabelEditApi = (params) =>
  defHttp.post({
    url: Api.GET_LADDER_LABEL_EDIT,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//删除阶梯团标签
export const getLadderLabelDelApi = (params) =>
  defHttp.post({
    url: Api.GET_LADDER_LABEL_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//更改阶梯团标签状态
export const getLadderLabelSwitchApi = (params) =>
  defHttp.post({
    url: Api.GET_LADDER_LABEL_SWITCH,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
