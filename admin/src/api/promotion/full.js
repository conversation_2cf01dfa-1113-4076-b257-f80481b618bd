import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
var Api;
(function (Api) {
  Api['GET_FULL_ACM_LIST'] = '/promotion/admin/fullAcm/list';
  Api['GET_FULL_ACM_DETAIL'] = '/promotion/admin/fullAcm/detail';
  Api['GET_FULL_ACM_INVALID'] = '/promotion/admin/fullAcm/invalid';
  Api['GET_FULL_ACM_DEL'] = '/promotion/admin/fullAcm/delete';
  Api['GET_FULL_ASM_LIST'] = '/promotion/admin/fullAsm/list';
  Api['GET_FULL_ASM_DETAIL'] = '/promotion/admin/fullAsm/detail';
  Api['GET_FULL_ASM_INVALID'] = '/promotion/admin/fullAsm/invalid';
  Api['GET_FULL_ASM_DEL'] = '/promotion/admin/fullAsm/delete';
  Api['GET_FULL_ALD_LIST'] = '/promotion/admin/fullAld/list';
  Api['GET_FULL_ALD_DETAIL'] = '/promotion/admin/fullAld/detail';
  Api['GET_FULL_ALD_INVALID'] = '/promotion/admin/fullAld/invalid';
  Api['GET_FULL_ALD_DEL'] = '/promotion/admin/fullAld/delete';
  Api['GET_FULL_NLD_LIST'] = '/promotion/admin/fullNld/list';
  Api['GET_FULL_NLD_DETAIL'] = '/promotion/admin/fullNld/detail';
  Api['GET_FULL_NLD_INVALID'] = '/promotion/admin/fullNld/invalid';
  Api['GET_FULL_NLD_DEL'] = '/promotion/admin/fullNld/delete';
})(Api || (Api = {}));
//slodon_循环满减列表
export const getFullAcmListApi = (params) =>
  defHttp.get({
    url: Api.GET_FULL_ACM_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_循环满减详情
export const getFullAcmDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_FULL_ACM_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_失效循环满减
export const getFullAcmInvalidApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_ACM_INVALID,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_删除循环满减
export const getFullAcmDelApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_ACM_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_阶梯满减列表
export const getFullAsmListApi = (params) =>
  defHttp.get({
    url: Api.GET_FULL_ASM_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_阶梯满减详情
export const getFullAsmDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_FULL_ASM_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_失效阶梯满减
export const getFullAsmInvalidApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_ASM_INVALID,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_删除阶梯满减
export const getFullAsmDelApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_ASM_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_满N元折扣列表
export const getFullAldListApi = (params) =>
  defHttp.get({
    url: Api.GET_FULL_ALD_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_满N元折扣详情
export const getFullAldDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_FULL_ALD_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_失效满N元折扣
export const getFullNldInvalidApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_NLD_INVALID,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_删除满N元折扣
export const getFullNldDelApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_NLD_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_满N件折扣列表
export const getFullNldListApi = (params) =>
  defHttp.get({
    url: Api.GET_FULL_NLD_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_满N件折扣详情
export const getFullNldDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_FULL_NLD_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_失效满N件折扣
export const getFullAldInvalidApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_ALD_INVALID,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_删除满N件折扣
export const getFullAldDelApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_ALD_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
