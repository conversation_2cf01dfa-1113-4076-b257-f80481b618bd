import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
var Api;
(function (Api) {
  Api['GET_CATE_TREE'] = '/goods/admin/goodsCategory/getCateTree';
  Api['GET_COUPON_LIST'] = '/promotion/admin/coupon/list';
  Api['GET_COUPON_IS_RECOMMEND'] = '/promotion/admin/coupon/isRecommend';
  Api['GET_COUPON_RECEIVE_DETAILS'] = '/promotion/admin/coupon/receiveDetails';
  Api['GET_COUPON_INVALID'] = '/promotion/admin/coupon/invalid';
  Api['GET_COUPON_DEL'] = '/promotion/admin/coupon/delete';
  Api['GET_COUPON_DETAIL'] = '/promotion/admin/coupon/detail';
  Api['GET_COUPON_GOODS_LIST'] = '/promotion/admin/coupon/goodsList';
  Api['GET_COUPON_ADD'] = '/promotion/admin/coupon/add';
  Api['GET_COUPON_UPDATE'] = '/promotion/admin/coupon/update';
})(Api || (Api = {}));
//slodon_获取分类树数据
export const getCateTreeApi = (params) =>
  defHttp.get({
    url: Api.GET_CATE_TREE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//平台优惠券列表
export const getCouponListApi = (params) =>
  defHttp.get({
    url: Api.GET_COUPON_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//推荐优惠券
export const getCouponIsRecommendApi = (params) =>
  defHttp.post({
    url: Api.GET_COUPON_IS_RECOMMEND,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取优惠券领取列表
export const getCouponReceiveListsApi = (params) =>
  defHttp.get({
    url: Api.GET_COUPON_RECEIVE_DETAILS,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//失效优惠券
export const getCouponInvalidApi = (params) =>
  defHttp.post({
    url: Api.GET_COUPON_INVALID,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//删除优惠券
export const getCouponDelApi = (params) =>
  defHttp.post({
    url: Api.GET_COUPON_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取优惠券详情
export const getCouponDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_COUPON_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取优惠券商品列表
export const getCouponGoodsListApi = (params) =>
  defHttp.get({
    url: Api.GET_COUPON_GOODS_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//新增优惠券
export const getCouponAddApi = (params) =>
  defHttp.post({
    url: Api.GET_COUPON_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//编辑优惠券
export const getCouponUpdateApi = (params) =>
  defHttp.post({
    url: Api.GET_COUPON_UPDATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
