import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
var Api;
(function (Api) {
  Api['GET_RANK_LIST'] = '/goods/admin/goods/rank/list';
  Api['GET_CATEGORY_LIST'] = '/goods/admin/goods/rank/category/categoryList';
  Api['POST_RANK_IS_ENABLE'] = '/goods/admin/goods/rank/isEnable';
  Api['POST_RANK_DEL'] = '/goods/admin/goods/rank/delete';
  Api['GET_CATEGORY_LABEL_LIST'] = '/goods/admin/goods/rank/category/list';
  Api['GET_RANK_CATEGORY_IS_ENABLE'] = '/goods/admin/goods/rank/category/isEnable';
  Api['POST_RANK_CATEGORY_ADD'] = '/goods/admin/goods/rank/category/add';
  Api['POST_RANK_CATEGORY_UPDATE'] = '/goods/admin/goods/rank/category/update';
  Api['POST_RANK_CATEGORY_DEL'] = '/goods/admin/goods/rank/category/delete';
  Api['POST_RANK_REFRESH'] = '/goods/admin/goods/rank/refresh';
  Api['POST_RANK_CLOSE_RANK'] = '/goods/admin/goods/rank/closeRank';
  Api['POST_GOODS_CATEGORY_LIST'] = '/goods/admin/goodsCategory/categoryList';
  Api['POST_RANK_LOAD_RANK_GOODS'] = '/goods/admin/goods/rank/loadRankGoods';
  Api['POST_RANK_UPDATE'] = '/goods/admin/goods/rank/update';
  Api['POST_RANK_ADD'] = '/goods/admin/goods/rank/add';
  Api['POST_RANK_DETAIL'] = '/goods/admin/goods/rank/detail';
  Api['POST_RANK_GOODS'] = '/goods/admin/goods/rank/rankGoods';
})(Api || (Api = {}));
//slodon_获取排行榜活动列表
export const getRankListApi = (params) =>
  defHttp.get({
    url: Api.GET_RANK_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_获取启用状态的排行榜分类列表
export const getCategoryListApi = (params) =>
  defHttp.get({
    url: Api.GET_CATEGORY_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_是否启用榜单
export const postSwitchRankApi = (params) =>
  defHttp.post({
    url: Api.POST_RANK_IS_ENABLE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_删除排行榜活动
export const postDelRankApi = (params) =>
  defHttp.post({
    url: Api.POST_RANK_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_获取排行榜分类列表
export const getCategoryLabelListApi = (params) =>
  defHttp.get({
    url: Api.GET_CATEGORY_LABEL_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_更改排行榜分类状态
export const getSwitchLabelApi = (params) =>
  defHttp.post({
    url: Api.GET_RANK_CATEGORY_IS_ENABLE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_添加排行榜分类
export const getAddLabelApi = (params) =>
  defHttp.post({
    url: Api.POST_RANK_CATEGORY_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_添加排行榜分类
export const getDelLabelApi = (params) =>
  defHttp.post({
    url: Api.POST_RANK_CATEGORY_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_编辑排行榜分类
export const getEditLabelApi = (params) =>
  defHttp.post({
    url: Api.POST_RANK_CATEGORY_UPDATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// slodon_更新榜单商品
export const getRankRefreshApi = (params) =>
  defHttp.get({
    url: Api.POST_RANK_REFRESH,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// slodon_关闭全部榜单
export const getRankCloseRankApi = () =>
  defHttp.post({
    url: Api.POST_RANK_CLOSE_RANK,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// slodon_获取商品的三级分类列表（只返回有3级分类的数据）
export const getGoodsCategoryListApi = () =>
  defHttp.get({
    url: Api.POST_GOODS_CATEGORY_LIST,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// slodon_加载榜单商品
export const getRankLoadGoodsApi = (params) =>
  defHttp.get({
    url: Api.POST_RANK_LOAD_RANK_GOODS,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// slodon_编辑榜单
export const getRankUpdateApi = (params) =>
  defHttp.post({
    url: Api.POST_RANK_UPDATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });
// slodon_新建榜单
export const getRankAddApi = (params) =>
  defHttp.post({
    url: Api.POST_RANK_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });
// slodon_获取榜单详情
export const getRankDetailApi = (params) =>
  defHttp.get({
    url: Api.POST_RANK_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// slodon_获取榜单绑定的商品
export const getRankGoodsApi = (params) =>
  defHttp.get({
    url: Api.POST_RANK_GOODS,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
