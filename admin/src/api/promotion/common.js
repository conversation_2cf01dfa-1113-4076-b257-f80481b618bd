import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
var Api;
(function (Api) {
  Api['GET_SETTING_LIST'] = '/system/admin/setting/getSettingList';
  Api['GET_SETTING_UPDATE'] = '/system/admin/setting/updateSettingList';
  Api['GET_ORDER_INFO_DETAIL'] = '/business/admin/orderInfo/detail';
  Api['GET_ORDER_INFO_GET_TRACE'] = '/business/admin/logistics/order/getTrace';
})(Api || (Api = {}));
//保存系统设置信息
export const getSettingUpdateApi = (params) =>
  defHttp.post({
    url: Api.GET_SETTING_UPDATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_获取系统设置信息
export const getSettingListApi = (params) =>
  defHttp.get({
    url: Api.GET_SETTING_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取订单详情
export const getOrderDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_ORDER_INFO_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//slodon_获取订单详情
export const getOrderGetTraceApi = (params) =>
  defHttp.get({
    url: Api.GET_ORDER_INFO_GET_TRACE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
