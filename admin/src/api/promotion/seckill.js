import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
var Api;
(function (Api) {
  Api['GET_SECKILL_LIST'] = '/promotion/admin/seckill/list';
  Api['GET_SECKILL_ADD'] = '/promotion/admin/seckill/addSeckill';
  Api['GET_SECKILL_LABEL_LIST'] = '/promotion/admin/seckillLabel/list';
  Api['GET_SECKILL_LABEL_ADD'] = '/promotion/admin/seckillLabel/addLabel';
  Api['GET_SECKILL_LABEL_EDIT'] = '/promotion/admin/seckillLabel/update';
  Api['GET_SECKILL_LABEL_DEL'] = '/promotion/admin/seckillLabel/deleteLabel';
  Api['GET_SECKILL_LABEL_SWITCH'] = '/promotion/admin/seckillLabel/setShowLabel';
  Api['GET_SECKILL_STAGE_LIST'] = '/promotion/admin/seckillStage/list';
  Api['GET_SECKILL_GOODS_PRODUCT_LIST'] = '/promotion/admin/seckillGoods/productList';
  Api['GET_SECKILL_GOODS_LIST'] = '/promotion/admin/seckillGoods/list';
  Api['GET_SECKILL_UNFINISHED_LIST'] = '/promotion/admin/seckill/unFinishedList';
  Api['GET_SECKILL_GOODS_AUDIT_LIST'] = '/promotion/admin/seckillGoods/auditList';
  Api['GET_SECKILL_GOODS_AUDIT_DEL'] = '/promotion/admin/seckillGoods/delete';
  Api['GET_SECKILL_GOODS_AUDIT'] = '/promotion/admin/seckillGoods/audit';
  Api['GET_SECKILL_GOODS_GET_SECKILL'] = '/promotion/admin/seckill/getSeckill';
  Api['GET_SECKILL_GOODS_SET_BANNER'] = '/promotion/admin/seckill/setBanner';
  Api['GET_SECKILL_GOODS_DELETE'] = '/promotion/admin/seckill/deleteSeckill';
})(Api || (Api = {}));
//秒杀活动列表
export const getSeckillListApi = (params) =>
  defHttp.get({
    url: Api.GET_SECKILL_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//新增活动
export const getSeckillAddApi = (params) =>
  defHttp.post({
    url: Api.GET_SECKILL_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取秒杀标签列表
export const getSeckillLabelListApi = (params) =>
  defHttp.get({
    url: Api.GET_SECKILL_LABEL_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//添加秒杀标签
export const getSeckillLabelAddApi = (params) =>
  defHttp.post({
    url: Api.GET_SECKILL_LABEL_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//编辑秒杀标签
export const getSeckillLabelEditApi = (params) =>
  defHttp.post({
    url: Api.GET_SECKILL_LABEL_EDIT,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//删除秒杀标签
export const getSeckillLabelDelApi = (params) =>
  defHttp.post({
    url: Api.GET_SECKILL_LABEL_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//更改秒杀标签状态
export const getSeckilLabelSwitchApi = (params) =>
  defHttp.post({
    url: Api.GET_SECKILL_LABEL_SWITCH,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取活动场次列表
export const getSeckilStageListApi = (params) =>
  defHttp.get({
    url: Api.GET_SECKILL_STAGE_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取当前场次的参与的商品列表（spu）
export const getSeckilGoodsListApi = (params) =>
  defHttp.get({
    url: Api.GET_SECKILL_GOODS_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取秒杀商品的sku列表
export const getSeckilGoodsProductListApi = (params) =>
  defHttp.get({
    url: Api.GET_SECKILL_GOODS_PRODUCT_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取秒杀商品的sku列表
export const getSeckilUnfinishedApi = (params) =>
  defHttp.get({
    url: Api.GET_SECKILL_UNFINISHED_LIST,
    params,
  });
  
// slodon_删除参与的活动商品
export const getSeckilGoodsAuditDelApi = (params) =>
  defHttp.post({
    url: Api.GET_SECKILL_GOODS_AUDIT_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

// slodon_审核参与的活动商品
export const getSeckilGoodsAuditApi = (params) =>
  defHttp.post({
    url: Api.GET_SECKILL_GOODS_AUDIT,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//  slodon_获取活动下待审核的spu列表
export const getSeckilGoodsAuditListApi = (params) =>
  defHttp.get({
    url: Api.GET_SECKILL_GOODS_AUDIT_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//  slodon_获取秒杀活动详情
export const getSeckilGoodsGetSeckillApi = (params) =>
  defHttp.get({
    url: Api.GET_SECKILL_GOODS_GET_SECKILL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//  slodon_设置轮播图
export const getSeckilGoodsSetBannerApi = (params) =>
  defHttp.post({
    url: Api.GET_SECKILL_GOODS_SET_BANNER,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//  slodon_删除活动
export const getSeckilGoodsDeleteApi = (params) =>
  defHttp.post({
    url: Api.GET_SECKILL_GOODS_DELETE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
