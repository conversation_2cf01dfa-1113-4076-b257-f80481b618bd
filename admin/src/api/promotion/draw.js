import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
var Api;
(function (Api) {
  Api['GET_DRAW_LIST'] = '/promotion/admin/draw/list';
  Api['GET_DRAW_DEL'] = '/promotion/admin/draw/delete';
  Api['GET_DRAW_RECORD_LIST'] = '/promotion/admin/draw/drawRecordList';
  Api['GET_DRAW_ADD'] = '/promotion/admin/draw/add';
  Api['GET_DRAW_UPDATE'] = '/promotion/admin/draw/update';
  Api['GET_DRAW_DETAIL'] = '/promotion/admin/draw/detail';
})(Api || (Api = {}));
// slodon_获取抽奖活动列表
export const getDrawListApi = (params) =>
  defHttp.get({
    url: Api.GET_DRAW_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// slodon_删除抽奖活动
export const getDrawDelApi = (params) =>
  defHttp.post({
    url: Api.GET_DRAW_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// slodon_获取中奖记录列表
export const getDrawRecordListApi = (params) =>
  defHttp.get({
    url: Api.GET_DRAW_RECORD_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
// slodon_新建活动
export const getDrawAddApi = (params) =>
  defHttp.post({
    url: Api.GET_DRAW_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });
// slodon_编辑活动
export const getDrawUpdateApi = (params) =>
  defHttp.post({
    url: Api.GET_DRAW_UPDATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });
// slodon_获取活动详情
export const getDrawDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_DRAW_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
