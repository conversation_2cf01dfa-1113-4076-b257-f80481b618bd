import { ContentTypeEnum } from '/@/enums/httpEnum';
import { defHttp } from '/@/utils/http/axios';
import { base64Encrypt } from '/@/utils/utils';

const API = {
  GET_CAPTCHA: '/captcha/common/getCaptcha',
  POST_LOGIN: '/adminLogin/oauth/token',
};

export const getCaptchaApi = () => defHttp.get({ url: API.GET_CAPTCHA });

//账号登录
export const accountLogin = (params) => {
  params.password = base64Encrypt(params.password);
  return defHttp.post({
    url: API.POST_LOGIN,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });
};
