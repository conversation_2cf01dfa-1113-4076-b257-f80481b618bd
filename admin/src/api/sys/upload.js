import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';
const { uploadUrl = '' } = useGlobSetting();
/**
 * @description: Upload interface
 */
export function uploadApi(params, onUploadProgress) {
  return defHttp.uploadFile(
    {
      url: uploadUrl,
      onUploadProgress,
    },
    params,
  );
}
//获取图片token
export const getTokenImageApi = () => defHttp.get({ url: '/oss/seller/token' });
