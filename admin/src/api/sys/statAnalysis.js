import { defHttp } from '/@/utils/http/axios';
export const API = {
  GET_WAIT: '/statistics/admin/overview/analysis/waitDeal',
  GET_TODAY_TRADE: '/statistics/admin/overview/analysis/tradeInfo',
  GET_TODAY_USER: '/statistics/admin/overview/analysis/userInfo',
  GET_TODAY_VIEW: '/statistics/admin/overview/analysis/viewInfo',
  GET_TODAY_GOODS: '/statistics/admin/overview/analysis/goodsInfo',
  GET_PAYTREND: '/statistics/admin/overview/analysis/payOrderTrend',
  GET_VIEWTREND: '/statistics/admin/overview/analysis/flowTrend',
  GET_NEWTREND: '/statistics/admin/overview/analysis/newTrend',
  GET_MEMBER_REGION: '/statistics/admin/member/analysis/regionDistribution',
  GET_STORE_REGION: '/statistics/admin/store/analysis/regionDistribution',
  GET_STORE_RANK: '/statistics/admin/trade/analysis/storeSalesRank',
  GET_GOODS_RANK: '/statistics/admin/trade/analysis/goodsSalesRank',
  GET_CATE_RANK: '/statistics/admin/overview/analysis/categoryRank',
  GET_BRAND_PER: '/statistics/admin/overview/analysis/brandSalesPercent',
  GET_PRESENT_DATA: '/statistics/admin/present/analysis/presentData',
  GET_TRADE_VIEW: '/statistics/admin/trade/analysis/tradeOverview',
  GET_30_TREND: '/statistics/admin/trade/analysis/changeTrend',
  GET_PROVINCE_TREND: '/statistics/admin/trade/analysis/provinceSalesTrend',
  GET_PROVINCE_PERCENT: '/statistics/admin/trade/analysis/provinceSalesPercent',
  GET_TERMINAL_TREND: '/statistics/admin/trade/analysis/terminalSalesTrend',
  GET_TERMINAL_PERCENT: '/statistics/admin/trade/analysis/terminalSalesPercent',
  GET_TRADE_REPORT: '/statistics/admin/trade/analysis/tradeReport',
  GET_TERNINAL_VISITNP: '/statistics/admin/flow/analysis/terminalVisitorNumPercent',
  GET_TERNINAL_VIEWNP: '/statistics/admin/flow/analysis/terminalViewNumPercent',
  GET_STORE_FLOW_RANK: '/statistics/admin/flow/analysis/storeFlowRank',
  GET_GOODS_FLOW_RANK: '/statistics/admin/flow/analysis/goodsFlowRank',
  GET_FLOW_REPORT: '/statistics/admin/flow/analysis/flowReport',
  GET_STORE_FLOW_REPORT: '/statistics/admin/flow/analysis/storeReport',
  GET_FLOW_OVERVIEW: '/statistics/admin/flow/analysis/flowOverview',
  GET_GOODS_OVERVIEW: '/statistics/admin/goods/analysis/goodsOverview',
  GET_GOODS_SALE_TREND: '/statistics/admin/goods/analysis/goodsSalesTrend',
  GET_BRAND_RANK: '/statistics/admin/goods/analysis/brandSalesRank',
  GET_GOODS_SALE_RANK: '/statistics/admin/goods/analysis/goodsSalesRank',
  GET_GOODS_COLLECTION_RANK: '/statistics/admin/goods/analysis/goodsCollectionRank',
  GET_DAY_REPORT: '/statistics/admin/goods/analysis/dayReport',
  GET_GOODS_REPORT: '/statistics/admin/goods/analysis/goodsReport',
  GET_CATE_SALE_RANK: '/statistics/admin/category/analysis/categorySalesRank',
  GET_CATE_SALE_PERCENT: '/statistics/admin/category/analysis/categorySaleAmountPercent',
  GET_CATE_RETURN_PERCENT: '/statistics/admin/category/analysis/categoryReturnNumPercent',
  GET_CATE_DAY_REPORT: '/statistics/admin/category/analysis/dayReport',
  GET_CATE_CATE_REPORT: '/statistics/admin/category/analysis/categoryReport',
  GET_MEMBER_30_TREND: '/statistics/admin/member/analysis/changeTrend',
  GET_MEMBER_PERCENT: '/statistics/admin/member/analysis/memberPercent',
  GET_MEMBER_REGION_PERCENT: '/statistics/admin/member/analysis/memberRegionPercent',
  GET_NEW_STORE_TREND: '/statistics/admin/store/analysis/newStoreTrend',
  GET_STORE_TYPE_PERCENT: '/statistics/admin/store/analysis/typePercent',
  GET_STORE_GRADE_PERCENT: '/statistics/admin/store/analysis/gradePercent',
  GET_PROVINCE_PAYAMOUNT_PERCENT: '/statistics/admin/region/analysis/payAmountPercent',
  GET_PROVINCE_RISE_TREND: '/statistics/admin/region/analysis/provinceRiseTrend',
  GET_PREFER_GOODS_RANK: '/statistics/admin/member/analysis/preferGoodsRank',
  GET_MEMBER_OVER_VIEW: '/statistics/admin/member/analysis/goodsOverview',
  GET_MEMBER_NUM: '/statistics/admin/member/analysis/memberNum',
  GET_STORE_OVER_VIEW: '/statistics/admin/store/analysis/storeOverview',
  GET_STORE_NUM: '/statistics/admin/store/analysis/storeNum',
  GET_MEMBER_REPORT: '/statistics/admin/member/analysis/memberReport',
  GET_MEMBER_DAY_REPORT: '/statistics/admin/member/analysis/dayReport',
  GET_STORE_REPORT: '/statistics/admin/store/analysis/storeReport',
  GET_REGION_REPORT: '/statistics/admin/region/analysis/regionReport',
  GET_GOODS_NUM: "/statistics/admin/goods/analysis/goodsBrandNum"
}
export const getWaitDeal = () => defHttp.get({ url: API.GET_WAIT });
export const getStat_TradeInfo = () => defHttp.get({ url: API.GET_TODAY_TRADE });
export const getStat_UserInfo = () => defHttp.get({ url: API.GET_TODAY_USER });
export const getStat_ViewInfo = () => defHttp.get({ url: API.GET_TODAY_VIEW });
export const getStat_GoodsInfo = () => defHttp.get({ url: API.GET_TODAY_GOODS });
export const getPayTrend = (params) => defHttp.get({ url: API.GET_PAYTREND, params });
export const getViewTrend = (params) => defHttp.get({ url: API.GET_VIEWTREND, params });
export const getNewTrend = (params) => defHttp.get({ url: API.GET_NEWTREND, params });
export const getMemberRegion = () => defHttp.get({ url: API.GET_MEMBER_REGION });
export const getStoreRegion = () => defHttp.get({ url: API.GET_STORE_REGION });
export const getStoreRank = (params) => defHttp.get({ url: API.GET_STORE_RANK, params });
export const getCateRank = (params) => defHttp.get({ url: API.GET_CATE_RANK, params });
export const getBrandPercent = (params) => defHttp.get({ url: API.GET_BRAND_PER, params });
export const getGoodsRank = (params) => defHttp.get({ url: API.GET_GOODS_RANK, params });
export const getPresentStat = params => defHttp.get({ url: API.GET_PRESENT_DATA, params });
export const getTradeViewStat = (params) => defHttp.get({ url: API.GET_TRADE_VIEW, params });
export const getTrend30Trend = (params) => defHttp.get({ url: API.GET_30_TREND, params });
export const getFlowOverView = (params) => defHttp.get({ url: API.GET_FLOW_OVERVIEW, params });
export const getProviceTrend = (params) => defHttp.get({ url: API.GET_PROVINCE_TREND, params });
export const getProvincePercent = (params) =>
  defHttp.get({ url: API.GET_PROVINCE_PERCENT, params });
export const getTerminalTrend = (params) => defHttp.get({ url: API.GET_TERMINAL_TREND, params });
export const getTerminalPercent = (params) =>
  defHttp.get({ url: API.GET_TERMINAL_PERCENT, params });
export const getTradeReport = (params) => defHttp.get({ url: API.GET_TRADE_REPORT, params });
export const getTerminalVisitorNumPercent = (params) =>
  defHttp.get({ url: API.GET_TERNINAL_VISITNP, params });
export const getTerminalViewNumPercent = (params) =>
  defHttp.get({ url: API.GET_TERNINAL_VIEWNP, params });
export const getStoreFlowRank = (params) => defHttp.get({ url: API.GET_STORE_FLOW_RANK, params });
export const getGoodsFlowRank = (params) => defHttp.get({ url: API.GET_GOODS_FLOW_RANK, params });
export const getFlowReport = (params) => defHttp.get({ url: API.GET_FLOW_REPORT, params });
export const getStoreFlowReport = (params) =>
  defHttp.get({ url: API.GET_STORE_FLOW_REPORT, params });
export const getGoodsOverView = (params) => defHttp.get({ url: API.GET_GOODS_OVERVIEW, params });
export const getGoodsSaleTrend = (params) => defHttp.get({ url: API.GET_GOODS_SALE_TREND, params });
export const getBrandRank = (params) => defHttp.get({ url: API.GET_BRAND_RANK, params });
export const getGoodsSaleRank = (params) => defHttp.get({ url: API.GET_GOODS_SALE_RANK, params });
export const getGoodsCollectionRank = (params) =>
  defHttp.get({ url: API.GET_GOODS_COLLECTION_RANK, params });
export const getDayReport = (params) => defHttp.get({ url: API.GET_DAY_REPORT, params });
export const getGoodsReport = (params) => defHttp.get({ url: API.GET_GOODS_REPORT, params });
export const getCateSaleRank = (params) => defHttp.get({ url: API.GET_CATE_SALE_RANK, params });
export const getCateSalePercent = (params) =>
  defHttp.get({ url: API.GET_CATE_SALE_PERCENT, params });
export const getCateReturnPercent = (params) =>
  defHttp.get({ url: API.GET_CATE_RETURN_PERCENT, params });
export const getCateDayReport = (params) => defHttp.get({ url: API.GET_CATE_DAY_REPORT, params });
export const getCateCateReport = (params) => defHttp.get({ url: API.GET_CATE_CATE_REPORT, params });
export const getMemberOverView = (params) => defHttp.get({ url: API.GET_MEMBER_OVER_VIEW, params });
export const getMemberNum = () => defHttp.get({ url: API.GET_MEMBER_NUM });
export const getStoreOverView = (params) => defHttp.get({ url: API.GET_STORE_OVER_VIEW, params });
export const getStoreNum = () => defHttp.get({ url: API.GET_STORE_NUM });
export const getMemberReport = (params) => defHttp.get({ url: API.GET_MEMBER_REPORT, params });
export const getMemberDayReport = (params) =>
  defHttp.get({ url: API.GET_MEMBER_DAY_REPORT, params });
export const getStoreReport = (params) => defHttp.get({ url: API.GET_STORE_REPORT, params });
export const getRegionReport = (params) => defHttp.get({ url: API.GET_REGION_REPORT, params });

export const getGoodsNumApi = () => defHttp.get({ url: API.GET_GOODS_NUM })
