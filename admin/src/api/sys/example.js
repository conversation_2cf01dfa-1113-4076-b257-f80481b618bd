import { defHttp } from '/@/utils/http/axios';
var Api;
(function (Api) {
  Api['GET_CAPTCHA'] = '/captcha/common/getCaptcha';
  Api['GET_SELLER_BASE_INFO'] = '/business/seller/orderInfo/list';
  Api['GET_SYS_ORDER_LIST'] = '/business/admin/orderInfo/list';
  Api['GET_SYS_ORDER_LISTS'] = '/system/admin/material/category/list';
})(Api || (Api = {}));
//获取登录页图形验证码
export const getCaptchaApi = () => defHttp.get({ url: Api.GET_CAPTCHA });
//获取供应商的基本信息
export const getSellerBaseInfoApi = () => defHttp.get({ url: Api.GET_SELLER_BASE_INFO });
//获取系统订单列表
export const getSysOrderListApi = (params) => defHttp.get({ url: Api.GET_SYS_ORDER_LIST, params });
//获取系统订单列表
export const getSysOrderListsApi = (params) =>
  defHttp.get({ url: Api.GET_SYS_ORDER_LISTS, params });
