import { defHttp } from '/@/utils/http/axios';
import { LoginParams, LoginResultModel } from './model/userModel';

import { ErrorMessageMode } from '/#/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
import { useUserStore } from '@/store/modules/user';

enum Api {
  Login = '/adminLogin/oauth/token',
  Logout = '/adminLogin/oauth/logout',
}

/**
 * @description: user login api
 */
export function loginApi(params: LoginParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<LoginResultModel>(
    {
      url: Api.Login,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

export function doLogout() {
  const { getRefreshToken } = useUserStore();
  let params = {
    grant_type: 'refresh_token',
    refresh_token: getRefreshToken
  };
  return defHttp.post({
    url: Api.Logout,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
}
