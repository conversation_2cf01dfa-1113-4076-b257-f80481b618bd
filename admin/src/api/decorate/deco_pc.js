import { defHttp } from '@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
export const API = {
  PC_DECO_LIST: '/system/admin/pcDeco/list',
  PC_DECO_UPDATE: '/system/admin/pcDeco/update',
  PC_DECO_ISENABLED: '/system/admin/pcDeco/isEnable',
  PC_DECO_DELETE: '/system/admin/pcDeco/delete',
  PC_DECO_COPY: '/system/admin/pcDeco/copy',
  PC_DECO_ADD: '/system/admin/pcDeco/add',
  NAV_LIST: '/system/admin/navigation/list',
  NAV_IS_SHOW: '/system/admin/navigation/isShow',
  NAV_DEL: '/system/admin/navigation/delete',
  GOODS_LIST: '/goods/admin/goods/list',
  STORE_LIST: '/seller/admin/store/list',
  ADD_NAV: '/system/admin/navigation/add',
  EDIT_NAV: '/system/admin/navigation/update',
  CATE_LIST: '/goods/admin/goodsCategory/list',
  LINK_LIST: '/cms/admin/friendLink/list',
  LINK_DEL: '/cms/admin/friendLink/delete',
  LINK_ADD: '/cms/admin/friendLink/add',
  LINK_EDIT: '/cms/admin/friendLink/update',
  ADV_GET: '/system/admin/pcFirstAdv/get',
  SAVE_ADV: '/system/admin/pcFirstAdv/update',
  TPL_LIST: '/system/admin/tplPc/data/list',
  TPL_ENABLE: '/system/admin/tplPc/data/isEnable',
  TPL_UPDATE: '/system/admin/tplPc/data/update',
  TPL_DELETE: '/system/admin/tplPc/data/delete',
  TPL_TYPE_LIST: '/system/admin/tplPc/type/list',
  TPL_INS_LIST: "/system/admin/tplPc/list",
  TPL_DATA_ADD: '/system/admin/tplPc/data/add',
  TPL_DETAIL: '/system/admin/pcDeco/display',
  PC_DECO_NAV_LIST: '/system/admin/navigation/list',
  POINT_LABEL_LIST: '/integral/admin/integral/goodsLabel/list'
};
export const getDecoList = (params) => defHttp.get({ url: API.PC_DECO_LIST, params });
export const UpdateDecoApi = (params) =>
  defHttp.post({
    url: API.PC_DECO_UPDATE,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });
export const updateIsEnabled = (params) =>
  defHttp.post({
    url: API.PC_DECO_ISENABLED,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });
export const decoDelete = (params) =>
  defHttp.post({
    url: API.PC_DECO_DELETE,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });
export const decoCopy = (params) =>
  defHttp.post({
    url: API.PC_DECO_COPY,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });
export const decoAdd = (params) =>
  defHttp.post({
    url: API.PC_DECO_ADD,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const navIsShow = (params) =>
  defHttp.post({
    url: API.NAV_IS_SHOW,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const navDel = (params) =>
  defHttp.post({
    url: API.NAV_DEL,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const getNavList = (params) => defHttp.get({ url: API.NAV_LIST, params });

export const getAdminGoodsList = (params) => defHttp.get({ url: API.GOODS_LIST, params });

export const getAdminStoreList = (params) => defHttp.get({ url: API.STORE_LIST, params });

export const navAdd = (params) =>
  defHttp.post({
    url: API.ADD_NAV,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const navEdit = (params) =>
  defHttp.post({
    url: API.EDIT_NAV,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const getCategoryList = (params) => defHttp.get({ url: API.CATE_LIST, params });
export const getLinkList = (params) => defHttp.get({ url: API.LINK_LIST, params });

export const delLink = (params) =>
  defHttp.post({
    url: API.LINK_DEL,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const addLink = (params) =>
  defHttp.post({
    url: API.LINK_ADD,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const editLink = (params) =>
  defHttp.post({
    url: API.LINK_EDIT,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const AdvGetApi = (params) => defHttp.get({ url: API.ADV_GET, params });

export const saveAdvApi = (params) =>
  defHttp.post({
    url: API.SAVE_ADV,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const tplPcListApi = (params) => defHttp.get({ url: API.TPL_LIST, params });

export const TplEnableApi = (params) =>
  defHttp.post({
    url: API.TPL_ENABLE,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });


export const TplUpdateApi = (params) =>
  defHttp.post({
    url: API.TPL_UPDATE,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const TplDeleteApi = (params) =>
  defHttp.post({
    url: API.TPL_DELETE,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const TplTypeListApi = (params) => defHttp.get({ url: API.TPL_TYPE_LIST, params });

export const TplInsListApi = (params) => defHttp.get({ url: API.TPL_INS_LIST, params });


export const TplAddListApi = (params) =>
  defHttp.post({
    url: API.TPL_DATA_ADD,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });


export const TplDetailApi = (params) => defHttp.get({ url: API.TPL_DETAIL, params });


export const PcDecoNavListApi = (params) => defHttp.get({ url: API.PC_DECO_NAV_LIST, params });

export const PointLabelListApi = (params) => defHttp.get({ url: API.POINT_LABEL_LIST, params });
