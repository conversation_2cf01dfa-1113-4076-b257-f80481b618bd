import { defHttp } from '@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
export const API = {
  M_DECO_LIST: '/system/admin/mobileDeco/list',
  M_DECO_DETAIL: '/system/admin/mobileDeco/detail',
  M_DECO_MENU: '/system/admin/tplMobile/menu',
  M_DECO_UPDATE: '/system/admin/mobileDeco/update',
  M_DECO_ISUSE: '/system/admin/mobileDeco/isUse',
  M_DECO_DELETE: '/system/admin/mobileDeco/delete',
  M_DECO_COPY: '/system/admin/mobileDeco/copy',
  M_DECO_ADD: '/system/admin/mobileDeco/add',
  NAV_LIST: '/system/admin/navigation/list',
  NAV_IS_SHOW: '/system/admin/navigation/isShow',
  NAV_DEL: '/system/admin/navigation/delete',
  GOODS_LIST: '/goods/admin/goods/list',
  STORE_LIST: '/seller/admin/store/list',
  ADD_NAV: '/system/admin/navigation/add',
  EDIT_NAV: '/system/admin/navigation/update',
  CATE_LIST: '/goods/admin/goodsCategory/list',
  LINK_LIST: '/cms/admin/friendLink/list',
  LINK_DEL: '/cms/admin/friendLink/delete',
  LINK_ADD: '/cms/admin/friendLink/add',
  LINK_EDIT: '/cms/admin/friendLink/update',
  CATE_EDIT: '/goods/admin/goodsCategory/update',
  CATE_INIT: '/goods/admin/goodsCategory/categoryInit',
  DRAW_LIST: '/promotion/admin/draw/drawList',
};

export const getMobleDecoListApi = (params) => defHttp.get({ url: API.M_DECO_LIST, params });

export const getMobleDecoDetailApi = (params) => defHttp.get({ url: API.M_DECO_DETAIL, params });

export const getMobleDecoMenuApi = (params) => defHttp.get({ url: API.M_DECO_MENU, params });

export const updateMobileDeco = (params) =>
  defHttp.post({
    url: API.M_DECO_UPDATE,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });
export const updateIsUse = (params) =>
  defHttp.post({
    url: API.M_DECO_ISUSE,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });
export const decoMobileDelete = (params) =>
  defHttp.post({
    url: API.M_DECO_DELETE,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });
export const decoMobileCopy = (params) =>
  defHttp.post({
    url: API.M_DECO_COPY,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });
export const decoMobileAddApi = (params) =>
  defHttp.post({
    url: API.M_DECO_ADD,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const navIsShow = (params) =>
  defHttp.post({
    url: API.NAV_IS_SHOW,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const navDel = (params) =>
  defHttp.post({
    url: API.NAV_DEL,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const getNavList = (params) => defHttp.get({ url: API.NAV_LIST, params });

export const getAdminGoodsList = (params) => defHttp.get({ url: API.GOODS_LIST, params });

export const getAdminStoreList = (params) => defHttp.get({ url: API.STORE_LIST, params });

export const navAdd = (params) =>
  defHttp.post({
    url: API.ADD_NAV,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const navEdit = (params) =>
  defHttp.post({
    url: API.EDIT_NAV,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const getCategoryList = (params) => defHttp.get({ url: API.CATE_LIST, params });
export const getLinkList = (params) => defHttp.get({ url: API.LINK_LIST, params });

export const delLink = (params) =>
  defHttp.post({
    url: API.LINK_DEL,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const addLink = (params) =>
  defHttp.post({
    url: API.LINK_ADD,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const editLink = (params) =>
  defHttp.post({
    url: API.LINK_EDIT,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const cateEdit = (params) =>
  defHttp.post({
    url: API.CATE_EDIT,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const cateInit = (params) =>
  defHttp.get({
    url: API.CATE_INIT,
    params,
  });

export const getDrawList = (params) =>
  defHttp.get({
    url: API.DRAW_LIST,
    params,
  });
