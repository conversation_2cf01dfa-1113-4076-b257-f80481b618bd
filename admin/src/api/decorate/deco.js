import { defHttp } from '@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
export const API = {
  PC_DECO_LIST: '/system/admin/pcDeco/list',
  TPL_UPDATE: '/system/admin/tplPc/data/update',
  TPL_LIST: '/system/admin/tplPc/data/list',

  DRAW_LIST: '/promotion/admin/draw/drawList',
  M_DECO_LIST: '/system/admin/mobileDeco/list',
  STORE_LIST: '/seller/admin/store/list',
  GOODS_LIST: '/goods/admin/goods/list',

};

export const getDecoList = (params) => defHttp.get({ url: API.PC_DECO_LIST, params });

export const TplUpdateApi = (params) =>
  defHttp.post({
    url: API.TPL_UPDATE,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const tplPcListApi = (params) => defHttp.get({ url: API.TPL_LIST, params });


export const getDrawList = (params) =>
  defHttp.get({
    url: API.DRAW_LIST,
    params,
  });

export const getMobleDecoListApi = (params) => defHttp.get({ url: API.M_DECO_LIST, params });

export const getAdminStoreList = (params) => defHttp.get({ url: API.STORE_LIST, params });

export const getAdminGoodsList = (params) => defHttp.get({ url: API.GOODS_LIST, params });