import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
export var Api;
(function (Api) {
  Api['GET_RECOMMEND_GOODS_LIST'] = '/goods/admin/goodsRecommend/goodsList';
  Api['DEL_RECOMMEND_GOODS'] = '/goods/admin/goodsRecommend/deleteHotGoods';
  Api['ADD_RELATION_GOODS'] = '/goods/admin/goodsRecommend/addRelationGoods';
  Api['DEL_RELATION_GOODS'] = '/goods/admin/goodsRecommend/deleteRelationMainGoods';
  Api['REMOVE_RELATION_GOODS'] = '/goods/admin/goodsRecommend/removeRelationGoods';
  Api['EDIT_HOT_WEIGHT'] = '/goods/admin/goodsRecommend/addHotGoods';
  Api['GET_SETTING'] = '/system/admin/setting/getSettingList';
  Api['ADD_RECOMMEND_GOODS_FORBID'] = '/goods/admin/goodsRecommend/addForbidGoods';
  Api['REMOVE_RECOMMEND_GOODS_FORBID'] = '/goods/admin/goodsRecommend/deleteForbidGoods';
  Api['UPDATE_SETTING'] = '/system/admin/setting/updateSettingList';
  Api['UPDATE_GET_RECOMMEND_SETTING'] = '/system/admin/setting/updateSettingList';
  Api['GET_OPERATE_CLUSTER_LIST'] = '/operate/admin/cluster/list';
  Api['GET_OPERATE_LABEL_LIST'] = '/operate/admin/label/list';
  Api['GET_EFFECT_REPORT_OVERVIEW'] = '/goods/admin/goodsRecommend/stats/overview';
  Api['GET_EFFECT_REPORT_TREND'] = '/goods/admin/goodsRecommend/stats/trend';
  Api['GET_EFFECT_REPORT_TABLE'] = '/goods/admin/goodsRecommend/stats/report';
  Api['EXPORT_EFFECT_REPORT_TABLE'] = '/goods/admin/goodsRecommend/stats/export';
  Api['GET_SIMPLE_CATETREE'] = '/goods/admin/goodsCategory/getSimpleCateTree';

})(Api || (Api = {}));

// 获取推荐商品列表
export const getRecommendGoodsList = (params) =>
  defHttp.get({ url: Api.GET_RECOMMEND_GOODS_LIST, params });

// 删除热门推荐商品
export const delRecommendGoods = (params) =>
  defHttp.post({
    url: Api.DEL_RECOMMEND_GOODS,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });

// 添加关联商品
export const addRelationGoods = (params) =>
  defHttp.post({
    url: Api.ADD_RELATION_GOODS,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });

// 删除关联主商品
export const delRelationGoods = (params) =>
  defHttp.post({
    url: Api.DEL_RELATION_GOODS,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

// 删除关联主商品的关联商品
export const removeRelationGoods = (params) =>
  defHttp.post({
    url: Api.REMOVE_RELATION_GOODS,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });

// 编辑推荐商品权重
export const editHotWeight = (params) =>
  defHttp.post({
    url: Api.EDIT_HOT_WEIGHT,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });

// 设置热门推荐商品禁推
export const addForbidGoods = (params) =>
  defHttp.post({
    url: Api.ADD_RECOMMEND_GOODS_FORBID,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });

// 解除热门推荐商品禁推
export const removeForbidGoods = (params) =>
  defHttp.post({
    url: Api.REMOVE_RECOMMEND_GOODS_FORBID,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });

// 获取配置
export const getSetting = (params) =>
  defHttp.get({ url: Api.GET_SETTING, params });

// 更新配置
export const saveSetting = (params) =>
  defHttp.post({
    url: Api.UPDATE_SETTING,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

// 更新热门商品规则设置
export const updateRecommendSetting = (params) =>
  defHttp.post({
    url: Api.UPDATE_GET_RECOMMEND_SETTING,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
  
// 获取会员分群列表
export const getOperateClusterList = (params) =>
  defHttp.get({ url: Api.GET_OPERATE_CLUSTER_LIST, params });
  
// 获取标签列表
export const getOperateLabelList = (params) =>
  defHttp.get({ url: Api.GET_OPERATE_LABEL_LIST, params });

// 获取推荐效果概况
export const getEffectReportOverview = (params) =>
  defHttp.get({ url: Api.GET_EFFECT_REPORT_OVERVIEW, params });

// 获取推荐效果趋势
export const getEffectReportTrend = (params) =>
  defHttp.get({ url: Api.GET_EFFECT_REPORT_TREND, params });

// 获取交易报表
export const getEffectReportTable = (params) =>
  defHttp.get({ url: Api.GET_EFFECT_REPORT_TABLE, params });

// 导出交易报表
export const exportEffectReportTable = (params) =>
  defHttp.get({
    url: Api.EXPORT_EFFECT_REPORT_TABLE,
    params,
    responseType: 'blob',
  });
  
// 获取商品品类数据
export const getSimpleCateTree = (params) =>
  defHttp.get({ url: Api.GET_SIMPLE_CATETREE, params });
