import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

var Api;
(function (Api) {
  Api['GET_ADMIN_PLAN_LIST'] = '/operate/admin/plan/list';
  Api['GET_ADMIN_PLAN_STOP'] = '/operate/admin/plan/stop';
  Api['GET_ADMIN_PLAN_END'] = '/operate/admin/plan/end';
  Api['GET_ADMIN_PLAN_DEL'] = '/operate/admin/plan/delete';
  Api['GET_ADMIN_SCENE_SALE_LIST'] = '/operate/admin/sceneSale/list';
  Api['GET_ADMIN_SCENE_SALE_CREATE_CHECK'] = '/operate/admin/sceneSale/createCheck';
  Api['GET_ADMIN_SCENE_SALE_STOP'] = '/operate/admin/sceneSale/stop';
  Api['GET_ADMIN_SCENE_SALE_END'] = '/operate/admin/sceneSale/end';
  Api['GET_ADMIN_SCENE_SALE_DEL'] = '/operate/admin/sceneSale/delete';
  Api['GET_ADMIN_PLAN_DETAIL'] = '/operate/admin/plan/detail';
  Api['GET_ADMIN_CLUSTER_LIST'] = '/operate/admin/cluster/list';
  Api['GET_ADMIN_PLAN_UPDATE'] = '/operate/admin/plan/update';
  Api['GET_ADMIN_PLAN_ADD'] = '/operate/admin/plan/add';
  Api['GET_ADMIN_SCENE_SALE_DETAIL'] = '/operate/admin/sceneSale/detail';
  Api['GET_ADMIN_SCENE_SALE_UPDATE'] = '/operate/admin/sceneSale/update';
  Api['GET_ADMIN_SCENE_SALE_ADD'] = '/operate/admin/sceneSale/add';
  Api['GET_ADMIN_PLAN_STATS_DATA'] = '/operate/admin/planStats/data';
  Api['GET_ADMIN_SCENE_STATS_DATA'] = '/operate/admin/sceneSaleStats/data';
  Api['GET_ADMIN_PLAN_STATS_TREND'] = '/operate/admin/planStats/trend';
  Api['GET_ADMIN_SCENE_STATS_TREND'] = '/operate/admin/sceneSaleStats/trend';
  Api['GET_ADMIN_PLAN_STATS_FORM'] = '/operate/admin/planStats/form';
  Api['GET_ADMIN_SCENE_STATS_FORM'] = '/operate/admin/sceneSaleStats/form';
  Api['GET_ADMIN_PLAN_STATS_EXPORT'] = '/operate/admin/planStats/export';
  Api['GET_ADMIN_SCENE_STATS_EXPORT'] = '/operate/admin/sceneSaleStats/export';

})(Api || (Api = {}));

//slodon_人群运营-获取运营计划列表
export const getAdminPlanListApi = (params) => defHttp.get({ url: Api.GET_ADMIN_PLAN_LIST, params });


//slodon_人群运营-暂停/继续运营计划列表
export const getAdminPlanStopApi = (params) =>
defHttp.post({
  url: Api.GET_ADMIN_PLAN_STOP,
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
  },
});

//slodon_人群运营-暂停/继续运营计划列表
export const getAdminPlanEndApi = (params) =>
defHttp.post({
  url: Api.GET_ADMIN_PLAN_END,
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
  },
});

//slodon_人群运营-删除运营计划列表
export const getAdminPlanDelApi = (params) =>
defHttp.post({
  url: Api.GET_ADMIN_PLAN_DEL,
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
  },
});

//slodon_场景营销-获取场景营销列表
export const getAdminSceneSaleListApi = (params) => defHttp.get({ url: Api.GET_ADMIN_SCENE_SALE_LIST, params });

//slodon_场景营销-获取场景营销是否可以创建
export const getAdminCreateCheckApi = (params) => defHttp.get({ url: Api.GET_ADMIN_SCENE_SALE_CREATE_CHECK, params });

//slodon_场景营销-暂停/继续场景营销
export const getAdminCreateStopApi = (params) =>
defHttp.post({
  url: Api.GET_ADMIN_SCENE_SALE_STOP,
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
  },
});

//slodon_场景营销-终止场景营销
export const getAdminCreateEndApi = (params) =>
defHttp.post({
  url: Api.GET_ADMIN_SCENE_SALE_END,
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
  },
});

//slodon_场景营销-删除场景营销
export const getAdminCreateDelApi = (params) =>
defHttp.post({
  url: Api.GET_ADMIN_SCENE_SALE_DEL,
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.FORM_URLENCODED,
  },
});

//slodon_人群运营-获取运营计划详情
export const getAdminPlanDetailApi = (params) => defHttp.get({ url: Api.GET_ADMIN_PLAN_DETAIL, params });

//slodon_会员分群-获取推荐分群列表数据
export const getAdminClusterListApi = (params) => defHttp.get({ url: Api.GET_ADMIN_CLUSTER_LIST, params });

//slodon_人群运营-编辑运营计划列表
export const getAdminPlanUpdateApi = (params) =>
defHttp.post({
  url: Api.GET_ADMIN_PLAN_UPDATE,
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.JSON,
  },
});

//slodon_人群运营-添加运营计划
export const getAdminPlanAddApi = (params) =>
defHttp.post({
  url: Api.GET_ADMIN_PLAN_ADD,
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.JSON,
  },
});

//slodon_场景营销-获取场景营销详情
export const getAdminSceneDetailApi = (params) => defHttp.get({ url: Api.GET_ADMIN_SCENE_SALE_DETAIL, params });

//slodon_场景营销-编辑场景营销
export const getAdminSaleUpdateApi = (params) =>
defHttp.post({
  url: Api.GET_ADMIN_SCENE_SALE_UPDATE,
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.JSON,
  },
});

//slodon_场景营销-添加场景营销
export const getAdminSaleAddApi = (params) =>
defHttp.post({
  url: Api.GET_ADMIN_SCENE_SALE_ADD,
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.JSON,
  },
});

//slodon_人群运营-数据统计
export const getAdminPlanStatsDataApi = (params) => defHttp.get({ url: Api.GET_ADMIN_PLAN_STATS_DATA, params });

//slodon_场景营销-数据统计
export const getAdminSceneStatsDataApi = (params) => defHttp.get({ url: Api.GET_ADMIN_SCENE_STATS_DATA, params });

//slodon_人群运营-数据统计趋势
export const getAdminPlanStatsTrendApi = (params) => defHttp.get({ url: Api.GET_ADMIN_PLAN_STATS_TREND, params });

//slodon_场景营销-数据统计趋势
export const getAdminSceneStatsTrendApi = (params) => defHttp.get({ url: Api.GET_ADMIN_SCENE_STATS_TREND, params });

//slodon_人群运营-数据统计 数据详情
export const getAdminPlanStatsFormApi = (params) => defHttp.get({ url: Api.GET_ADMIN_PLAN_STATS_FORM, params });

//slodon_场景营销-数据统计 数据详情
export const getAdminSceneStatsFormApi = (params) => defHttp.get({ url: Api.GET_ADMIN_SCENE_STATS_FORM, params });

 //slodon_人群运营-数据统计 数据导出
export const getAdminPlanStatsExportApi = (params) =>
defHttp.get({
  url: Api.GET_ADMIN_PLAN_STATS_EXPORT,
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.JSON,
  },
  responseType: 'blob',
});

//slodon_场景营销-数据统计 数据导出
export const getAdminSceneStatsExportApi = (params) =>
defHttp.get({
  url: Api.GET_ADMIN_SCENE_STATS_EXPORT,
  params,
  headers: {
    // @ts-ignore
    'Content-Type': ContentTypeEnum.JSON,
  },
  responseType: 'blob',
});