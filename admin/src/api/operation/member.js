import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';

var Api;
(function (Api) {
  Api['GET_OPERATE_CLUSTER_LIST'] = '/operate/admin/cluster/list';
  Api['GET_OPERATE_CLUSTER_REFRESH'] = '/operate/admin/cluster/refresh';
  Api['GET_OPERATE_CLUSTER_MEMBER_LIST'] = '/operate/admin/cluster/memberList';
  Api['GET_OPERATE_LABEL_MEMBER_LIST'] = '/operate/admin/label/memberList';
  Api['GET_OPERATE_LABEL_LIST'] = '/operate/admin/label/list';
  Api['GET_OPERATE_LABEL_REFRESH'] = '/operate/admin/label/refresh';
  Api['GET_OPERATE_LABEL_DEL'] = '/operate/admin/label/delete';
  Api['GET_RULE_TEMPLATE_LIST'] = '/operate/admin/ruleTemplate/list';
  Api['GET_LABEL_OPERATE_GOODS'] = '/operate/admin/label/operateGoods';
  Api['GET_GOODS_CATEGORY_CATE_TREE'] = '/goods/admin/goodsCategory/getCateTree';
  Api['GET_GOODS_CLUSTER_ADD'] = '/operate/admin/cluster/add';
  Api['GET_GOODS_CLUSTER_UPDATE'] = '/operate/admin/cluster/update';
  Api['GET_GOODS_CLUSTER_DELETE'] = '/operate/admin/cluster/delete';
  Api['GET_OPERATE_LABEL_UPDATE'] = '/operate/admin/label/update';
  Api['GET_OPERATE_LABEL_ADD'] = '/operate/admin/label/add';
  Api['GET_CLUSTER_DATA_STATS'] = '/operate/admin/cluster/dataStats';
  Api['GET_CLUSTER_GOODS_TOP'] = '/operate/admin/cluster/goodsTop';

})(Api || (Api = {}));

// 会员分群-获取推荐分群列表数据
export const getGroupIntroduceListApi = (params) => defHttp.get({ url: Api.GET_OPERATE_CLUSTER_LIST, params });


// slodon_会员分群-手动刷新标签
export const getGroupIntroduceRefreshApi = (params) =>
defHttp.post({
    url: Api.GET_OPERATE_CLUSTER_REFRESH,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
  
  // 会员分群-获取分群绑定会员列表
  export const getGroupIntroduceMemberListApi = (params) => defHttp.get({ url: Api.GET_OPERATE_CLUSTER_MEMBER_LIST, params });
  
  // 会员分群-获取分群绑定会员列表
  export const getGroupLabelMemberListApi = (params) => defHttp.get({ url: Api.GET_OPERATE_LABEL_MEMBER_LIST, params });
  
  // slodon_标签管理-获取标签列表
  export const getGroupLabelListApi = (params) => defHttp.get({ url: Api.GET_OPERATE_LABEL_LIST, params });
  
  // slodon_标签管理-手动刷新标签
  export const getGroupLabelRefreshApi = (params) =>
  defHttp.post({
    url: Api.GET_OPERATE_LABEL_REFRESH,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
  
  // slodon_标签管理-删除标签
  export const getGroupLabelDelApi = (params) =>
  defHttp.post({
    url: Api.GET_OPERATE_LABEL_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
  
  // slodon_标签管理-获取标签列表
  export const getTemplateListApi = (params) => defHttp.get({ url: Api.GET_RULE_TEMPLATE_LIST, params });
  
  // slodon_获取人群运营商品列表
  export const getOperateGoodsListsApi = (params) => defHttp.get({ url: Api.GET_LABEL_OPERATE_GOODS, params });
  
  // slodon_获取商品分类树接口
  export const getCategoryTreeListApi = (params) => defHttp.get({ url: Api.GET_GOODS_CATEGORY_CATE_TREE, params });
  
  
  // slodon_会员分群-添加分群
  export const getGroupClusterAddApi = (params) =>
  defHttp.post({
    url: Api.GET_GOODS_CLUSTER_ADD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });
  
  // slodon_会员分群-编辑分群
  export const getGroupClusterUpdateApi = (params) =>
  defHttp.post({
    url: Api.GET_GOODS_CLUSTER_UPDATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });
  
  // slodon_会员分群-删除分群
  export const getGroupClusterDeleteApi = (params) =>
  defHttp.post({
    url: Api.GET_GOODS_CLUSTER_DELETE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

  // slodon_标签管理-编辑标签
  export const getGroupLabelUpdateApi = (params) =>
  defHttp.post({
    url: Api.GET_OPERATE_LABEL_UPDATE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.JSON,
    },
  });
  
  // slodon_标签管理-添加标签
  export const getGroupLabelAddApi = (params) =>
  defHttp.post({
    url: Api.GET_OPERATE_LABEL_ADD,
    params,
    headers: {
       // @ts-ignore
       'Content-Type': ContentTypeEnum.JSON,
      },
    });

  //slodon_会员分群-获取分群数据变化趋势
  export const getClusterDataStatsApi = (params) => defHttp.get({ url: Api.GET_CLUSTER_DATA_STATS, params });

  //slodon_会员分群-获取成交商品排行
  export const getClusterGoodsTopApi = (params) => defHttp.get({ url: Api.GET_CLUSTER_GOODS_TOP, params });
    