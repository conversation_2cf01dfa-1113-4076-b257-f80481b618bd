import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
var Api;
(function (Api) {
  Api['UPDATE_SITE_SETTING'] = '/system/admin/setting/updateSettingList';
  Api['ORDER_LIST_CODE'] = '/system/admin/setting/getSupplierOrderListCode';
  Api['GET_SETTING_LIST'] = '/system/admin/setting/getSettingList';
  // 消息模板
  Api['SMS_SETTING'] = '/system/admin/setting/getSMSSetting';
  Api['SUPPLIER_TP_LIST'] = '/supplier/admin/supplierMsgTpl/list';
  Api['UPDATE_SUPPLIER_TP_LIST'] = '/supplier/admin/supplierMsgTpl/update';
})(Api || (Api = {}));
//保存基本配置
export const saveBasicSiteSetting = (params) =>
  defHttp.post({
    url: Api.UPDATE_SITE_SETTING,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取订单导出字段
export const getOrderOutputCode = () => defHttp.get({ url: Api.ORDER_LIST_CODE });
//获取配置信息
export const getSettingList = (params) => defHttp.get({ url: Api.GET_SETTING_LIST, params });

//获取短信配置
export const getSmsSetting = () => defHttp.get({ url: Api.SMS_SETTING });

//获取消息模板信息
export const getSupplierTpList = (params) => defHttp.get({ url: Api.SUPPLIER_TP_LIST,params, });
//更新消息模板信息
export const updateSupplierTpList = (params) =>
  defHttp.post({
    url: Api.UPDATE_SUPPLIER_TP_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });