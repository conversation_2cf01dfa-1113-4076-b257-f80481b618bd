import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
var Api;
(function (Api) {
  Api['GET_ARTICLE_CATEGORY_LIST'] = '/supplier/admin/supplierArticleCategory/list';
  Api['ADD_ARTICLE_CATEGORY'] = '/supplier/admin/supplierArticleCategory/add';
  Api['EDIT_ARTICLE_CATEGORY'] = '/supplier/admin/supplierArticleCategory/update';
  Api['DEL_ARTICLE_CATEGORY'] = '/supplier/admin/supplierArticleCategory/delete';
  Api['GET_ARTICLE_LIST'] = '/supplier/admin/supplierArticle/list';
  Api['GET_ARTICLE_DETAIL'] = '/supplier/admin/supplierArticle/detail';
  Api['ADD_ARTICLE'] = '/supplier/admin/supplierArticle/add';
  Api['EDIT_ARTICLE'] = '/supplier/admin/supplierArticle/update';
  Api['DEL_ARTICLE'] = '/supplier/admin/supplierArticle/delete';

})(Api || (Api = {}));

//获取文章分类列表
export const getArticleCategoryList = (params) =>
  defHttp.get({ url: Api.GET_ARTICLE_CATEGORY_LIST, params });

//新增文章分类
export const addArticleCategory = (params) =>
  defHttp.post({
    url: Api.ADD_ARTICLE_CATEGORY,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//编辑文章分类
export const editArticleCategory = (params) =>
  defHttp.post({
    url: Api.EDIT_ARTICLE_CATEGORY,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//删除文章分类
export const delArticleCategory = (params) =>
  defHttp.post({
    url: Api.DEL_ARTICLE_CATEGORY,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取文章列表
export const getArticleList = (params) => defHttp.get({ url: Api.GET_ARTICLE_LIST, params });

//获取文章详情
export const getArticleDetail = (params) => defHttp.get({ url: Api.GET_ARTICLE_DETAIL, params });

//新增文章
export const addArticle = (params) =>
  defHttp.post({
    url: Api.ADD_ARTICLE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//编辑文章
export const editArticle = (params) =>
  defHttp.post({
    url: Api.EDIT_ARTICLE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//删除文章
export const delArticle = (params) =>
  defHttp.post({
    url: Api.DEL_ARTICLE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

