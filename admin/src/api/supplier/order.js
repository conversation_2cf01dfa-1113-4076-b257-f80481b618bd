import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
var Api;
(function (Api) {

  //订单管理
  Api['GET_ORDER_LIST'] = '/supplier/admin/supplierOrder/list';
  Api['GET_ORDER_DETAIL'] = '/supplier/admin/supplierOrder/detail';
  Api['EXPORT_ORDER'] = '/supplier/admin/supplierOrder/export';
  Api['GET_SERVICE_LIST'] = '/supplier/admin/supplierAfterSale/list';
  Api['GET_SERVICE_DETAIL'] = '/supplier/admin/supplierAfterSale/detail';
  Api['EXPORT_SERVICE'] = '/supplier/admin/supplierAfterSale/export';
  Api['CONFIRM_SERVICE'] = '/supplier/admin/supplierAfterSale/confirmRefund'
  Api['GET_REASON_LIST'] = '/system/admin/reason/list';
  Api['GET_ORDER_INFO_GET_TRACE'] = '/supplier/admin/supplierLogistics/order/getTrace';
})(Api || (Api = {}));

//获取订单列表
export const getOrderList = (params) => defHttp.get({ url: Api.GET_ORDER_LIST, params });

//获取订单详情
export const getOrderDetail = (params) => defHttp.get({ url: Api.GET_ORDER_DETAIL, params });

//导出订单列表
export const exportOrder = (params) =>
  defHttp.get({
    url: Api.EXPORT_ORDER,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
    responseType: 'blob',
  });

//获取售后管理列表
export const getServiceList = (params) => defHttp.get({ url: Api.GET_SERVICE_LIST, params });

//获取售后管理详情
export const getServiceDetail = (params) => defHttp.get({ url: Api.GET_SERVICE_DETAIL, params });

//导出售后管理列表数据
export const exportService = (params) => defHttp.get({ url: Api.EXPORT_SERVICE, params,responseType: 'blob', });

//确认售后管理退款
export const confirmService = (params) =>
  defHttp.post({
    url: Api.CONFIRM_SERVICE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取原因列表
export const getReasonList = (params) => defHttp.get({ url: Api.GET_REASON_LIST, params });


//slodon_获取物流轨迹
export const getOrderGetTraceApi = (params) =>
  defHttp.get({
    url: Api.GET_ORDER_INFO_GET_TRACE,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
