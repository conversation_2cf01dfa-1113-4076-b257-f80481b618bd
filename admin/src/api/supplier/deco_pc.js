import { defHttp } from '@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
export const API = {
  PC_DECO_LIST: '/supplier/admin/supplierPcDeco/list',
  PC_DECO_UPDATE: '/supplier/admin/supplierPcDeco/update',
  PC_DECO_ISENABLED: '/supplier/admin/supplierPcDeco/isEnable',
  PC_DECO_DELETE: '/supplier/admin/supplierPcDeco/delete',
  PC_DECO_COPY: '/supplier/admin/supplierPcDeco/copy',
  PC_DECO_ADD: '/supplier/admin/supplierPcDeco/add',

  NAV_LIST: '//supplier/admin/supplierNavigation/list',
  NAV_IS_SHOW: '/supplier/admin/supplierNavigation/isShow',
  NAV_DEL: '/supplier/admin/supplierNavigation/delete',
  ADD_NAV: '/supplier/admin/supplierNavigation/add',
  EDIT_NAV: '/supplier/admin/supplierNavigation/update',

  GOODS_LIST: '/supplier/admin/supplierGoods/list',
  STORE_LIST: '/seller/admin/store/list',
  CATE_LIST: '/goods/admin/goodsCategory/list',

  LINK_LIST: '/supplier/admin/supplierFriendLink/list',
  LINK_DEL: '/supplier/admin/supplierFriendLink/delete',
  LINK_ADD: '/supplier/admin/supplierFriendLink/add',
  LINK_EDIT: '/supplier/admin/supplierFriendLink/update',
  ADV_GET: '/supplier/admin/supplierFirstAdv/get',
  SAVE_ADV: '/supplier/admin/supplierFirstAdv/update',
  TPL_LIST: '/supplier/admin/supplierPcData/list',
  TPL_ENABLE: '/supplier/admin/supplierPcData/isEnable',
  TPL_UPDATE: '/supplier/admin/supplierPcData/update',
  TPL_DELETE: '/supplier/admin/supplierPcData/delete',
  TPL_TYPE_LIST: '/system/admin/tplPc/type/list',
  TPL_INS_LIST: "/system/admin/tplPc/list",
  TPL_DATA_ADD: '/supplier/admin/supplierPcData/add',
  TPL_DETAIL: '/supplier/admin/supplierPcDeco/detail',

  PC_DECO_NAV_LIST: '/supplier/admin/supplierNavigation/list',
};
export const getSupplierDecoList = (params) => defHttp.get({ url: API.PC_DECO_LIST, params });
export const UpdateDecoApi = (params) =>
  defHttp.post({
    url: API.PC_DECO_UPDATE,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });
export const updateIsEnabled = (params) =>
  defHttp.post({
    url: API.PC_DECO_ISENABLED,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });
export const decoDelete = (params) =>
  defHttp.post({
    url: API.PC_DECO_DELETE,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });
export const decoCopy = (params) =>
  defHttp.post({
    url: API.PC_DECO_COPY,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });
export const decoAdd = (params) =>
  defHttp.post({
    url: API.PC_DECO_ADD,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const navIsShow = (params) =>
  defHttp.post({
    url: API.NAV_IS_SHOW,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const navDel = (params) =>
  defHttp.post({
    url: API.NAV_DEL,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const getNavList = (params) => defHttp.get({ url: API.NAV_LIST, params });

export const getAdminSupplierGoodsList = (params) => defHttp.get({ url: API.GOODS_LIST, params });

export const getAdminStoreList = (params) => defHttp.get({ url: API.STORE_LIST, params });

export const navAdd = (params) =>
  defHttp.post({
    url: API.ADD_NAV,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const navEdit = (params) =>
  defHttp.post({
    url: API.EDIT_NAV,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const getCategoryList = (params) => defHttp.get({ url: API.CATE_LIST, params });
export const getLinkList = (params) => defHttp.get({ url: API.LINK_LIST, params });

export const delLink = (params) =>
  defHttp.post({
    url: API.LINK_DEL,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const addLink = (params) =>
  defHttp.post({
    url: API.LINK_ADD,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const editLink = (params) =>
  defHttp.post({
    url: API.LINK_EDIT,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const AdvGetApi = (params) => defHttp.get({ url: API.ADV_GET, params });

export const saveAdvApi = (params) =>
  defHttp.post({
    url: API.SAVE_ADV,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const tplPcListApi = (params) => defHttp.get({ url: API.TPL_LIST, params });

export const TplEnableApi = (params) =>
  defHttp.post({
    url: API.TPL_ENABLE,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });


export const TplSupplierUpdateApi = (params) =>
  defHttp.post({
    url: API.TPL_UPDATE,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const TplDeleteApi = (params) =>
  defHttp.post({
    url: API.TPL_DELETE,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });

export const TplTypeListApi = (params) => defHttp.get({ url: API.TPL_TYPE_LIST, params });

export const TplInsListApi = (params) => defHttp.get({ url: API.TPL_INS_LIST, params });


export const TplAddListApi = (params) =>
  defHttp.post({
    url: API.TPL_DATA_ADD,
    params,
    headers: { 'Content-Type': ContentTypeEnum.FORM_URLENCODED },
  });


export const TplDetailApi = (params) => defHttp.get({ url: API.TPL_DETAIL, params });


export const PcDecoNavListApi = (params) => defHttp.get({ url: API.PC_DECO_NAV_LIST, params });

