import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
var Api;
(function (Api) {
  Api['GET_FULL_ACM_LIST'] = '/supplier/admin/supplierFullAcm/list';
  Api['GET_FULL_ACM_DETAIL'] = '/supplier/admin/supplierFullAcm/detail';
  Api['GET_FULL_ACM_INVALID'] = '/supplier/admin/supplierFullAcm/invalid';
  Api['GET_FULL_ACM_DEL'] = '/supplier/admin/supplierFullAcm/delete';
  Api['GET_FULL_ASM_LIST'] = '/supplier/admin/supplierFullAsm/list';
  Api['GET_FULL_ASM_DETAIL'] = '/supplier/admin/supplierFullAsm/detail';
  Api['GET_FULL_ASM_INVALID'] = '/supplier/admin/supplierFullAsm/invalid';
  Api['GET_FULL_ASM_DEL'] = '/supplier/admin/supplierFullAsm/delete';
  Api['GET_FULL_ALD_LIST'] = '/supplier/admin/supplierFullAld/list';
  Api['GET_FULL_ALD_DETAIL'] = '/supplier/admin/supplierFullAld/detail';
  Api['GET_FULL_ALD_INVALID'] = '/supplier/admin/supplierFullAld/invalid';
  Api['GET_FULL_ALD_DEL'] = '/supplier/admin/supplierFullAld/delete';
  Api['GET_FULL_NLD_LIST'] = '/supplier/admin/supplierFullNld/list';
  Api['GET_FULL_NLD_DETAIL'] = '/supplier/admin/supplierFullNld/detail';
  Api['GET_FULL_NLD_INVALID'] = '/supplier/admin/supplierFullNld/invalid';
  Api['GET_FULL_NLD_DEL'] = '/supplier/admin/supplierFullNld/delete';
})(Api || (Api = {}));
//slodon_循环满减列表
export const getFullAcmListApi = (params) =>
  defHttp.get({
    url: Api.GET_FULL_ACM_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_循环满减详情
export const getFullAcmDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_FULL_ACM_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_失效循环满减
export const getFullAcmInvalidApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_ACM_INVALID,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_删除循环满减
export const getFullAcmDelApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_ACM_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_阶梯满减列表
export const getFullAsmListApi = (params) =>
  defHttp.get({
    url: Api.GET_FULL_ASM_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_阶梯满减详情
export const getFullAsmDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_FULL_ASM_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_失效阶梯满减
export const getFullAsmInvalidApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_ASM_INVALID,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_删除阶梯满减
export const getFullAsmDelApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_ASM_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_满N元折扣列表
export const getFullAldListApi = (params) =>
  defHttp.get({
    url: Api.GET_FULL_ALD_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_满N元折扣详情
export const getFullAldDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_FULL_ALD_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_失效满N元折扣
export const getFullNldInvalidApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_NLD_INVALID,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_删除满N元折扣
export const getFullNldDelApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_NLD_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_满N件折扣列表
export const getFullNldListApi = (params) =>
  defHttp.get({
    url: Api.GET_FULL_NLD_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_满N件折扣详情
export const getFullNldDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_FULL_NLD_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_失效满N件折扣
export const getFullAldInvalidApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_ALD_INVALID,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//slodon_删除满N件折扣
export const getFullAldDelApi = (params) =>
  defHttp.post({
    url: Api.GET_FULL_ALD_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
