import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
var Api;
(function (Api) {
  // 结算账单
  Api['GET_BILL_LIST'] = '/supplier/admin/supplierBill/list';
  Api['GET_BILL_DETAIL'] = '/supplier/admin/supplierBill/detail';
  Api['APPROVED_BILL'] = '/supplier/admin/supplierBill/approved';
  Api['CONFIRM_PAYMENT_BILL'] = '/supplier/admin/supplierBill/confirmPayment';
  Api['EXPORT_BILL_LIST'] = '/supplier/admin/supplierBill/export';
})(Api || (Api = {}));


//获取结算帐单列表
export const getBillList = (params) => defHttp.get({ url: Api.GET_BILL_LIST, params });

//获取结算帐单详情
export const getBillDetail = (params) => defHttp.get({ url: Api.GET_BILL_DETAIL, params });

//审核结算单
export const approvedBill = (params) =>
  defHttp.post({
    url: Api.APPROVED_BILL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//审核结算单
export const confirmPaymentBill = (params) =>
  defHttp.post({
    url: Api.CONFIRM_PAYMENT_BILL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//导出结算帐单列表
export const exportBillList = (params) => defHttp.get({
  url: Api.EXPORT_BILL_LIST,
  params,
  responseType: 'blob',
});
