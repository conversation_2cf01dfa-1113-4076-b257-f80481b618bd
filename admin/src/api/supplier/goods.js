import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
var Api;
(function (Api) {
  // 商品设置
  Api['PRODUCT_SETTING'] = '/system/admin/setting/getSettingList';
  Api['ESINIT_PRODUCT'] = '/supplier/admin/supplierSearch/esInit';
  Api['UPDATE_SITE_SETTING'] = '/system/admin/setting/updateSettingList';

  // 商品管理
  Api['GET_GOODS_LIST'] = '/supplier/admin/supplierGoods/list';
  Api['GET_GOODS_DETAIL'] = '/supplier/admin/supplierGoods/detail';
  Api['LOCKOFF'] = '/supplier/admin/supplierGoods/lockup';
  Api['AUDIT_GOOD'] = '/supplier/admin/supplierGoods/audit';

  Api['GET_REASON_LIST'] = '/system/admin/reason/list';
})(Api || (Api = {}));

//获取商品设置配置
export const getProductSetting = (params) => defHttp.get({ url: Api.PRODUCT_SETTING, params });

//立即更新商品数据
export const updateProduct = () => defHttp.get({ url: Api.ESINIT_PRODUCT });

//保存站点基本配置
export const saveBasicSiteSetting = (params) =>
  defHttp.post({
    url: Api.UPDATE_SITE_SETTING,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//获取商品管理列表
export const getGoodsList = (params) => defHttp.get({ url: Api.GET_GOODS_LIST, params });

//获取商品详情
export const getGoodsDetail = (params) => defHttp.get({ url: Api.GET_GOODS_DETAIL, params });

//商品违规下架
export const lockOffGoods = (params) =>
  defHttp.post({
    url: Api.LOCKOFF,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });

//审核通过
export const auditGood = (params) =>
  defHttp.post({
    url: Api.AUDIT_GOOD,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
  //获取原因列表
export const getReasonList = (params) => defHttp.get({ url: Api.GET_REASON_LIST, params });