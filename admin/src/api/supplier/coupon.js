import { defHttp } from '/@/utils/http/axios';
import { ContentTypeEnum } from '/@/enums/httpEnum';
var Api;
(function (Api) {
  Api['GET_COUPON_LIST'] = '/supplier/admin/supplierCoupon/list';
  Api['GET_COUPON_RECEIVE_DETAILS'] = '/supplier/admin/supplierCoupon/receiveDetails';
  Api['GET_COUPON_INVALID'] = '/supplier/admin/supplierCoupon/invalid';
  Api['GET_COUPON_DEL'] = '/supplier/admin/supplierCoupon/delete';
  Api['GET_COUPON_DETAIL'] = '/supplier/admin/supplierCoupon/detail';
  Api['GET_COUPON_GOODS_LIST'] = '/supplier/admin/supplierCoupon/goodsList';
})(Api || (Api = {}));

//优惠券列表
export const getCouponListApi = (params) =>
  defHttp.get({
    url: Api.GET_COUPON_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//优惠券领取详情
export const getCouponReceiveListsApi = (params) =>
  defHttp.get({
    url: Api.GET_COUPON_RECEIVE_DETAILS,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//失效优惠券
export const getCouponInvalidApi = (params) =>
  defHttp.post({
    url: Api.GET_COUPON_INVALID,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//删除优惠券
export const getCouponDelApi = (params) =>
  defHttp.post({
    url: Api.GET_COUPON_DEL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取优惠券详情
export const getCouponDetailApi = (params) =>
  defHttp.get({
    url: Api.GET_COUPON_DETAIL,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });
//获取优惠券商品列表
export const getCouponGoodsListApi = (params) =>
  defHttp.get({
    url: Api.GET_COUPON_GOODS_LIST,
    params,
    headers: {
      // @ts-ignore
      'Content-Type': ContentTypeEnum.FORM_URLENCODED,
    },
  });