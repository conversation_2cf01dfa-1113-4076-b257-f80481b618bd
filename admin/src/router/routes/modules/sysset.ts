import type { AppRouteModule } from '/@/router/types';

import { LAYOUT } from '/@/router/constant';
import { t } from '/@/hooks/web/useI18n';

const sysset: AppRouteModule = {
  path: '/sysset',
  name: 'sysset',
  component: LAYOUT,
  redirect: '/sysset_home/basic',
  meta: {
    orderNo: 1,
    icon: 'iconv3_xitongshezhi',
    title: t('routes.demo.sysset.syssetnav'),
  },
  children: [
    {
      path: '/sysset_home',
      name: 'sysset_home',
      component: LAYOUT,
      redirect: '/sysset_home/basic',
      meta: {
        orderNo: 11,
        icon: 'iconxitong-shouye',
        title: t('routes.demo.sysset.sysset_home'),
      },
      children: [
        {
          path: 'basic',
          name: 'basic',
          component: () => import('/@/views/sysset/simple_stat/index.vue'),
          meta: {
            title: t('routes.demo.sysset.basic'),
          },
        },
      ],
    },
    {
      path: '/sysset_setting',
      name: 'sysset_setting',
      component: LAYOUT,
      redirect: '/sysset_setting/site_info',
      meta: {
        orderNo: 12,
        icon: 'iconjibenpeizhi',
        title: t('routes.demo.sysset.sysset_setting'),
      },
      children: [
        {
          path: 'site_info',
          name: 'site_info',
          component: () => import('/@/views/sysset/site_info.vue'),
          meta: {
            title: t('routes.demo.sysset.site_info'),
          },
        },
        {
          path: 'pic_set',
          name: 'pic_set',
          component: () => import('/@/views/sysset/pic_set.vue'),
          meta: {
            title: t('routes.demo.sysset.pic_set'),
          },
        },
        {
          path: 'payment',
          name: 'payment',
          component: () => import('/@/views/sysset/payment.vue'),
          meta: {
            title: t('routes.demo.sysset.payment'),
          },
        },
        {
          path: 'order',
          name: 'order',
          component: () => import('/@/views/sysset/order.vue'),
          meta: {
            title: t('routes.demo.sysset.order'),
          },
        },
        // dev_mobile-start
        // dev_mobile-end
      ],
    },
    {
      path: '/sysset_notice_set',
      name: 'sysset_notice_set',
      component: LAYOUT,
      redirect: '/sysset_notice_set/sms',
      meta: {
        orderNo: 13,
        icon: 'icontongzhiguanli',
        title: t('routes.demo.sysset.sysset_notice_set'),
      },
      children: [
        {
          path: 'sms',
          name: 'sms',
          component: () => import('/@/views/sysset/sms.vue'),
          meta: {
            title: t('routes.demo.sysset.sms'),
          },
        },
        {
          path: 'email',
          name: 'email',
          component: () => import('/@/views/sysset/email.vue'),
          meta: {
            title: t('routes.demo.sysset.email'),
          },
        },
        {
          path: 'msg_tpl',
          name: 'msgTpl',
          component: () => import('/@/views/sysset/msg_tpl.vue'),
          meta: {
            title: t('routes.demo.sysset.msg_tpl'),
          },
        },
      ],
    },
    {
      path: '/sysset_acount',
      name: 'sysset_acount',
      component: LAYOUT,
      redirect: '/sysset_acount/union_login',
      meta: {
        orderNo: 14,
        icon: 'iconsanfangzhanghu',
        title: t('routes.demo.sysset.sysset_acount'),
      },
      children: [
        {
          path: 'union_login',
          name: 'union_login',
          component: () => import('/@/views/sysset/union_login.vue'),
          meta: {
            title: t('routes.demo.sysset.union_login'),
          },
        },
      ],
    },
    {
      path: '/sysset_authority',
      name: 'sysset_authority',
      component: LAYOUT,
      redirect: '/sysset_authority/authority_group',
      meta: {
        orderNo: 15,
        icon: 'iconquanxianguanli',
        title: t('routes.demo.sysset.sysset_authority'),
      },
      children: [
        {
          path: 'authority_group',
          name: 'authority_group',
          component: () => import('/@/views/sysset/authority_group.vue'),
          meta: {
            title: t('routes.demo.sysset.authority_group'),
          },
        },
         // 平台资源
        // {
        //   path: 'sys_resource',
        //   name: 'sys_resource',
        //   component: () => import('/@/views/sysset/sys_resource.vue'),
        //   meta: {
        //     title: t('routes.demo.sysset.sys_resource'),
        //   },
        // },
        // 商户资源
        // {
        //   path: 'store_resource',
        //   name: 'store_resource',
        //   component: () => import('/@/views/sysset/store_resource.vue'),
        //   meta: {
        //     title: t('routes.demo.sysset.store_resource'),
        //   },
        // },
        {
          path: 'authority_member',
          name: 'authority_member',
          component: () => import('/@/views/sysset/authority_member.vue'),
          meta: {
            title: t('routes.demo.sysset.authority_member'),
          },
        },
        {
          path: 'operate_log',
          name: 'operate_log',
          component: () => import('/@/views/sysset/operate_log.vue'),
          meta: {
            title: t('routes.demo.sysset.operate_log'),
          },
        },
      ],
    },
    {
      path: '/sysset_agreement',
      name: 'sysset_agreement',
      component: LAYOUT,
      redirect: '/sysset_agreement/lists',
      meta: {
        orderNo: 16,
        icon: 'iconxieyiguanli',
        title: t('routes.demo.sysset.sysset_agreement'),
      },
      children: [
        {
          path: 'lists',
          name: 'agreement_lists',
          component: () => import('/@/views/sysset/lists.vue'),
          meta: {
            title: t('routes.demo.sysset.agreement_lists'),
          },
        },
        {
          path: 'lists_to_edit',
          name: 'lists_to_edit',
          component: () => import('/@/views/sysset/lists_to_edit.vue'),
          meta: {
            hideMenu: true,
            title: t('routes.demo.sysset.lists_to_edit'),
            showMenu: false,
          },
        },
      ],
    },
    {
      path: '/sysset_express',
      name: 'sysset_express',
      component: LAYOUT,
      redirect: '/sysset_express/express_lists',
      meta: {
        orderNo: 17,
        icon: 'iconwuliuguanli',
        title: t('routes.demo.sysset.sysset_express'),
      },
      children: [
        {
          path: 'express_lists',
          name: 'express_lists',
          component: () => import('/@/views/sysset/express_lists.vue'),
          meta: {
            title: t('routes.demo.sysset.express_lists'),
          },
        },
        {
          path: 'express',
          name: 'express',
          component: () => import('/@/views/sysset/express.vue'),
          meta: {
            title: t('routes.demo.sysset.express'),
          },
        },
      ],
    },
    {
      path: '/sysset_sensitive',
      name: 'SyssetSensitive',
      component: LAYOUT,
      redirect: '/sysset_sensitive/sensitive_list',
      meta: {
        orderNo: 18,
        icon: 'iconminganciguanli',
        title: t('routes.demo.sysset.sysset_sensitive'),
      },
      children: [
        {
          path: 'sensitive_list',
          name: 'SensitiveList',
          component: () => import('/@/views/sysset/sensitive/sensitive_list.vue'),
          meta: {
            title: t('routes.demo.sysset.sensitive_list'),
          },
        },
        {
          path: 'sensitive_category',
          name: 'SensitiveCategory',
          component: () => import('/@/views/sysset/sensitive/sensitive_category.vue'),
          meta: {
            title: t('routes.demo.sysset.sensitive_category'),
          },
        },
        {
          path: 'sensitive_history',
          name: 'SensitiveHistory',
          component: () => import('/@/views/sysset/sensitive/sensitive_history.vue'),
          meta: {
            title: t('routes.demo.sysset.sensitive_history'),
          },
        },
        {
          path: 'sensitive_setting',
          name: 'SensitiveSetting',
          component: () => import('/@/views/sysset/sensitive/sensitive_setting.vue'),
          meta: {
            title: t('routes.demo.sysset.sensitive_setting'),
          },
        },
      ],
    },
  ],
};

export default sysset;
