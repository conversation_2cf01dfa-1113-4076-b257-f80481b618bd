import type { AppRouteModule } from '/@/router/types';

import { getParentLayout, LAYOUT } from '/@/router/constant';
import { t } from '/@/hooks/web/useI18n';

const marketing: AppRouteModule = {
  path: '/marketing',
  name: 'Marketing',
  component: LAYOUT,
  redirect: '/marketing_promotion/center',
  meta: {
    orderNo: 4,
    title: t('routes.demo.marketing.center'),
    icon: 'iconv3_yingyongzhongxin',
  },
  children: [
    {
      path: '/marketing_promotion',
      name: 'MarketingPromotion',
      component: getParentLayout('MarketingPromotion'),
      redirect: '/marketing_promotion/center',
      meta: {
        orderNo: 41,
        icon: 'iconyingyongzhongxin',
        title: t('routes.demo.marketing.center'),
      },
      children: [
        {
          path: 'center',
          name: 'PromotionCenter',
          component: () => import('/@/views/marketing/promotion/center.vue'),
          meta: {
            title: t('routes.demo.marketing.center'),
          },
        },
        {
          path: 'coupon',
          name: 'CouponHome',
          component: () => import('/@/views/marketing/promotion/coupon/home.vue'),
          meta: {
            title: t('routes.demo.marketing.coupon'),
          },
        },
        {
          path: 'coupon_to_add',
          name: 'AddCoupon',
          component: () => import('/@/views/marketing/promotion/coupon/add_coupon_home.vue'),
          meta: {
            hideMenu: true,
            title: t('routes.demo.marketing.coupon_to_add'),
            showMenu: true,
            currentActiveMenu: '/marketing_promotion/coupon',
          },
        },
        {
          path: 'coupon_to_copy',
          name: 'CopyCoupon',
          component: () => import('/@/views/marketing/promotion/coupon/copy_coupon.vue'),
          meta: {
            hideMenu: true,
            title: t('routes.demo.marketing.coupon_to_copy'),
            showMenu: true,
            currentActiveMenu: '/marketing_promotion/coupon',
          },
        },
        {
          path: 'coupon_to_edit',
          name: 'EditCoupon',
          component: () => import('/@/views/marketing/promotion/coupon/edit_coupon.vue'),
          meta: {
            hideMenu: true,
            title: t('routes.demo.marketing.coupon_to_edit'),
            showMenu: true,
            currentActiveMenu: '/marketing_promotion/coupon',
          },
        },
        {
          path: 'coupon_to_receive_list',
          name: 'CouponReceiveList',
          component: () => import('/@/views/marketing/promotion/coupon/member_receive_lists.vue'),
          meta: {
            hideMenu: true,
            title: t('routes.demo.marketing.receiveCoupon'),
            showMenu: true,
            currentActiveMenu: '/marketing_promotion/coupon',
          },
        },
        {
          path: 'coupon_to_view',
          name: 'CouponToView',
          component: () => import('/@/views/marketing/promotion/coupon/view_coupon.vue'),
          meta: {
            hideMenu: true,
            title: '优惠券详情',
            showMenu: true,
            currentActiveMenu: '/marketing_promotion/coupon',
          },
        },
        {
          path: 'store_coupon',
          name: 'StoreCoupon',
          component: () => import('/@/views/marketing/promotion/coupon/store_coupon.vue'),
          meta: {
            title: t('routes.demo.marketing.storeCoupon'),
          },
        },
        {
          path: 'store_coupon_to_receive_list',
          name: 'StoreCouponReceiveList',
          component: () =>
            import('/@/views/marketing/promotion/coupon/store_member_receive_lists.vue'),
          meta: {
            hideMenu: true,
            title: t('routes.demo.marketing.receiveCoupon'),
            showMenu: true,
            currentActiveMenu: '/marketing_promotion/store_coupon',
          },
        },
        {
          path: 'store_coupon_to_view',
          name: 'StoreCouponToView',
          component: () => import('/@/views/marketing/promotion/coupon/store_view_coupon.vue'),
          meta: {
            hideMenu: true,
            title: '优惠券详情',
            showMenu: true,
            currentActiveMenu: '/marketing_promotion/store_coupon',
          },
        },
        {
          path: 'freight_coupon',
          name: 'StoreFreightCoupon',
          component: () => import('/@/views/marketing/promotion/coupon/freight_coupon.vue'),
          meta: {
            title: t('routes.demo.marketing.freight_coupon'),
          },
        },
        {
          path: 'freight_coupon_to_add',
          name: 'StoreFreightCouponToAdd',
          component: () => import('/@/views/marketing/promotion/coupon/add_freight_coupon_home.vue'),
          meta: {
            hideMenu: true,
            title: t('routes.demo.marketing.freight_coupon_to_add'),
            showMenu: true,
            currentActiveMenu: '/marketing_promotion/freight_coupon',
          },
        },
        {
          path: 'freight_coupon_to_copy',
          name: 'StoreFreightCouponToCopy',
          component: () => import('/@/views/marketing/promotion/coupon/copy_freight_coupon.vue'),
          meta: {
            hideMenu: true,
            title: t('routes.demo.marketing.freight_coupon_to_copy'),
            showMenu: true,
            currentActiveMenu: '/marketing_promotion/freight_coupon',
          },
        },
        {
          path: 'freight_coupon_to_edit',
          name: 'StoreFreightCouponToEdit',
          component: () => import('/@/views/marketing/promotion/coupon/edit_freight_coupon.vue'),
          meta: {
            hideMenu: true,
            title: t('routes.demo.marketing.freight_coupon_to_edit'),
            showMenu: true,
            currentActiveMenu: '/marketing_promotion/freight_coupon',
          },
        },
        {
          path: 'freight_coupon_to_view',
          name: 'StoreFreightCouponToView',
          component: () => import('/@/views/marketing/promotion/coupon/freight_view_coupon.vue'),
          meta: {
            hideMenu: true,
            title: t('routes.demo.marketing.freight_coupon_to_view'),
            showMenu: true,
            currentActiveMenu: '/marketing_promotion/freight_coupon',
          },
        },
        // dev_mobile-start
        {
          path: 'rank',
          name: 'RankIndex',
          component: () => import('/@/views/marketing/promotion/rank/index.vue'),
          meta: {
            title: t('routes.demo.marketing.rank'),
          },
        },
        {
          path: 'rank_to_add',
          name: 'RankToAdd',
          component: () => import('/@/views/marketing/promotion/rank/add_home.vue'),
          meta: {
            hideMenu: true,
            title: '新建排行榜',
            showMenu: true,
            currentActiveMenu: '/marketing_promotion/rank',
          },
        },
        {
          path: 'rank_to_edit',
          name: 'RankToEdit',
          component: () => import('../../../views/marketing/promotion/rank/edit.vue'),
          meta: {
            hideMenu: true,
            title: '编辑排行榜',
            showMenu: true,
            currentActiveMenu: '/marketing_promotion/rank',
          },
        },
        {
          path: 'rank_to_view',
          name: 'RankToView',
          component: () => import('/@/views/marketing/promotion/rank/view.vue'),
          meta: {
            hideMenu: true,
            title: '查看排行榜',
            showMenu: true,
            currentActiveMenu: '/marketing_promotion/rank',
          },
        },
        {
          path: 'rank_to_bind',
          name: 'RankToBind',
          component: () => import('/@/views/marketing/promotion/rank/bind_rank_lists.vue'),
          meta: {
            hideMenu: true,
            title: t('routes.demo.marketing.rank_bind'),
            showMenu: true,
            currentActiveMenu: '/marketing_promotion/rank',
          },
        },
        // dev_mobile-end
        {
          path: 'point_setting',
          name: 'PointSetting',
          component: () => import('/@/views/marketing/promotion/point_setting.vue'),
          meta: {
            title: t('routes.demo.marketing.point_setting'),
          },
        },
        {
          path: 'full_discount',
          name: 'FullDiscount',
          component: () => import('/@/views/marketing/promotion/full/discount.vue'),
          meta: {
            title: t('routes.demo.marketing.full_discount'),
          },
        },
        {
          path: 'seckill',
          name: 'SeckillIndexList',
          component: () => import('/@/views/marketing/promotion/seckill/lists.vue'),
          meta: {
            title: t('routes.demo.marketing.seckill'),
          },
        },
        {
          path: 'seckill_detail',
          name: 'SeckillDetail',
          component: () => import('/@/views/marketing/promotion/seckill/detail.vue'),
          meta: {
            title: t('routes.demo.marketing.seckill_detail'),
            hideMenu: true,
            showMenu: true,
            currentActiveMenu: '/marketing_promotion/seckill',
          },
        },
        {
          path: 'seckill_goods_list',
          name: 'SeckillGoodsList',
          component: () => import('/@/views/marketing/promotion/seckill/seckill_goods_lists.vue'),
          meta: {
            title: t('routes.demo.marketing.seckill_goods_list'),
            hideMenu: true,
            showMenu: true,
            currentActiveMenu: '/marketing_promotion/seckill',
          },
        },
        {
          path: 'spell_group',
          name: 'SpellGroup',
          component: () => import('/@/views/marketing/promotion/spell_group/lists.vue'),
          meta: {
            title: t('routes.demo.marketing.spell_group'),
          },
        },
        {
          path: 'spell_group_to_view',
          name: 'SpellGroupToView',
          component: () => import('/@/views/marketing/promotion/spell_group/view_spell_group.vue'),
          meta: {
            title: t('routes.demo.marketing.view_spell_group'),
            hideMenu: true,
            showMenu: true,
            currentActiveMenu: '/marketing_promotion/spell_group',
          },
        },
        {
          path: 'spell_group_order',
          name: 'SpellGroupOrder',
          component: () => import('/@/views/marketing/promotion/spell_group/order_lists.vue'),
          meta: {
            title: t('routes.demo.marketing.spell_group_order'),
            hideMenu: true,
            showMenu: true,
            currentActiveMenu: '/marketing_promotion/spell_group',
          },
        },
        {
          path: 'spell_group_order_to_detail',
          name: 'SpellGroupOrderToDetail',
          component: () => import('/@/views/marketing/promotion/spell_group/order_detail.vue'),
          meta: {
            title: t('routes.demo.marketing.spell_group_order_to_detail'),
            hideMenu: true,
            showMenu: true,
            currentActiveMenu: '/marketing_promotion/spell_group',
          },
        },
        {
          path: 'spell_group_bind_goods',
          name: 'JoinedGoodsList',
          component: () => import('/@/views/marketing/promotion/spell_group/joined_goods_list.vue'),
          meta: {
            title: t('routes.demo.marketing.joined_goods_list'),
            hideMenu: true,
            showMenu: true,
            currentActiveMenu: '/marketing_promotion/spell_group',
          },
        },
        {
          path: 'spell_group_team_list',
          name: 'SpellGroupTeamList',
          component: () => import('/@/views/marketing/promotion/spell_group/team_list.vue'),
          meta: {
            title: t('routes.demo.marketing.team_list'),
            hideMenu: true,
            showMenu: true,
            currentActiveMenu: '/marketing_promotion/spell_group',
          },
        },
        {
          path: 'ladder_group',
          name: 'LadderIndexGroup',
          component: () => import('/@/views/marketing/promotion/ladder_group/lists.vue'),
          meta: {
            title: t('routes.demo.marketing.ladder_group'),
          },
        },
        {
          path: 'ladder_group_to_view',
          name: 'LadderGroupToView',
          component: () =>
            import('/@/views/marketing/promotion/ladder_group/view_ladder_group.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.ladder_group_to_view'),
            currentActiveMenu: '/marketing_promotion/ladder_group',
          },
        },
        {
          path: 'ladder_group_team_list',
          name: 'LadderGroupTeamList',
          component: () => import('/@/views/marketing/promotion/ladder_group/team_list.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.ladder_group_team_list'),
            currentActiveMenu: '/marketing_promotion/ladder_group',
          },
        },
        {
          path: 'presale',
          name: 'PresaleGroup',
          component: () => import('/@/views/marketing/promotion/presale/lists_list.vue'),
          meta: {
            title: t('routes.demo.marketing.presale'),
          },
        },
        {
          path: 'presale_to_view',
          name: 'PresaleToView',
          component: () => import('/@/views/marketing/promotion/presale/view_presale.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.presale_to_view'),
            currentActiveMenu: '/marketing_promotion/presale',
          },
        },
        {
          path: 'presale_goods_list',
          name: 'PresaleGoodsList',
          component: () => import('/@/views/marketing/promotion/presale/presale_goods_lists.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.presale_goods_list'),
            currentActiveMenu: '/marketing_promotion/presale',
          },
        },
        // dev_mobile-start
        {
          path: 'sign',
          name: 'SignGroup',
          component: () => import('/@/views/marketing/sign/stat_list.vue'),
          meta: {
            title: t('routes.demo.marketing.sign'),
          },
        },
        {
          path: 'sign_to_add',
          name: 'SignToAdd',
          component: () => import('/@/views/marketing/sign/add_home.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.sign_to_add'),
            currentActiveMenu: '/marketing_promotion/sign',
          },
        },
        {
          path: 'sign_to_view',
          name: 'SignToView',
          component: () => import('/@/views/marketing/sign/view.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.sign_to_view'),
            currentActiveMenu: '/marketing_promotion/sign',
          },
        },
        {
          path: 'sign_to_edit',
          name: 'SignToEdit',
          component: () => import('/@/views/marketing/sign/edit.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.sign_to_edit'),
            currentActiveMenu: '/marketing_promotion/sign',
          },
        },
        {
          path: 'sign_to_member_detail',
          name: 'SignToMemberDetail',
          component: () => import('/@/views/marketing/sign/member_stat_detail.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.sign_to_member_detail'),
            currentActiveMenu: '/marketing_promotion/sign',
          },
        },
        {
          path: 'sign_to_activity_detail',
          name: 'SignToActivityDetail',
          component: () => import('/@/views/marketing/sign/activity_stat_detail.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.sign_to_activity_detail'),
            currentActiveMenu: '/marketing_promotion/sign',
          },
        },
        {
          path: 'lucky_draw_list',
          name: 'DrawLuckyList',
          component: () => import('/@/views/marketing/promotion/draw/lucky_list.vue'),
          meta: {
            title: t('routes.demo.marketing.lucky_draw_list'),
          },
        },
        {
          path: 'lucky_draw_list_to_add',
          name: 'DrawLuckyListToAdd',
          component: () => import('/@/views/marketing/promotion/draw/lucky_add.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.lucky_draw_list_to_add'),
            currentActiveMenu: '/marketing_promotion/lucky_draw_list',
          },
        },
        {
          path: 'lucky_draw_list_to_edit',
          name: 'DrawLuckyListToEdit',
          component: () => import('/@/views/marketing/promotion/draw/lucky_edit.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.lucky_draw_list_to_edit'),
            currentActiveMenu: '/marketing_promotion/lucky_draw_list',
          },
        },
        {
          path: 'lucky_draw_list_to_view',
          name: 'DrawLuckyListToView',
          component: () => import('/@/views/marketing/promotion/draw/lucky_view.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.lucky_draw_list_to_view'),
            currentActiveMenu: '/marketing_promotion/lucky_draw_list',
          },
        },
        {
          path: 'lucky_draw_list_to_copy',
          name: 'DrawLuckyListToCopy',
          component: () => import('/@/views/marketing/promotion/draw/lucky_copy.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.lucky_draw_list_to_copy'),
            currentActiveMenu: '/marketing_promotion/lucky_draw_list',
          },
        },
        {
          path: 'turnplate_list',
          name: 'DrawTurnPlateList',
          component: () => import('/@/views/marketing/promotion/draw/turnplate_list.vue'),
          meta: {
            title: t('routes.demo.marketing.turnplate_list'),
          },
        },
        {
          path: 'turnplate_list_to_add',
          name: 'TurnplateListToAdd',
          component: () => import('/@/views/marketing/promotion/draw/turnplate_add.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.turnplate_list_to_add'),
            currentActiveMenu: '/marketing_promotion/turnplate_list',
          },
        },
        {
          path: 'turnplate_list_to_edit',
          name: 'TurnplateListToEdit',
          component: () => import('/@/views/marketing/promotion/draw/turnplate_edit.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.turnplate_list_to_edit'),
            currentActiveMenu: '/marketing_promotion/turnplate_list',
          },
        },
        {
          path: 'turnplate_list_to_view',
          name: 'TurnplateListToView',
          component: () => import('/@/views/marketing/promotion/draw/turnplate_view.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.turnplate_list_to_view'),
            currentActiveMenu: '/marketing_promotion/turnplate_list',
          },
        },
        {
          path: 'turnplate_list_to_copy',
          name: 'TurnplateListToCopy',
          component: () => import('/@/views/marketing/promotion/draw/turnplate_copy.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.turnplate_list_to_copy'),
            currentActiveMenu: '/marketing_promotion/turnplate_list',
          },
        },
        {
          path: 'scratch_list',
          name: 'DrawScratchList',
          component: () => import('/@/views/marketing/promotion/draw/scratch_list.vue'),
          meta: {
            title: t('routes.demo.marketing.scratch_list'),
          },
        },
        {
          path: 'scratch_list_to_add',
          name: 'ScratchListToAdd',
          component: () => import('/@/views/marketing/promotion/draw/scratch_add.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.scratch_list_to_add'),
            currentActiveMenu: '/marketing_promotion/scratch_list',
          },
        },
        {
          path: 'scratch_list_to_edit',
          name: 'ScratchListToEdit',
          component: () => import('/@/views/marketing/promotion/draw/scratch_edit.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.scratch_list_to_edit'),
            currentActiveMenu: '/marketing_promotion/scratch_list',
          },
        },
        {
          path: 'scratch_list_to_view',
          name: 'ScratchListToView',
          component: () => import('/@/views/marketing/promotion/draw/scratch_view.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.scratch_list_to_view'),
            currentActiveMenu: '/marketing_promotion/scratch_list',
          },
        },
        {
          path: 'scratch_list_to_copy',
          name: 'ScratchListToCopy',
          component: () => import('/@/views/marketing/promotion/draw/scratch_copy.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.scratch_list_to_copy'),
            currentActiveMenu: '/marketing_promotion/scratch_list',
          },
        },
        {
          path: 'shake_list',
          name: 'DrawShakeList',
          component: () => import('/@/views/marketing/promotion/draw/shake_list.vue'),
          meta: {
            title: t('routes.demo.marketing.shake_list'),
          },
        },
        {
          path: 'shake_list_to_add',
          name: 'ShakeListToAdd',
          component: () => import('/@/views/marketing/promotion/draw/shake_add.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.shake_list_to_add'),
            currentActiveMenu: '/marketing_promotion/shake_list',
          },
        },
        {
          path: 'shake_list_to_edit',
          name: 'ShakeListToEdit',
          component: () => import('/@/views/marketing/promotion/draw/shake_edit.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.shake_list_to_edit'),
            currentActiveMenu: '/marketing_promotion/shake_list',
          },
        },
        {
          path: 'shake_list_to_copy',
          name: 'ShakeListToCopy',
          component: () => import('/@/views/marketing/promotion/draw/shake_copy.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.shake_list_to_copy'),
            currentActiveMenu: '/marketing_promotion/shake_list',
          },
        },
        {
          path: 'shake_list_to_view',
          name: 'ShakeListToView',
          component: () => import('/@/views/marketing/promotion/draw/shake_view.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.shake_list_to_view'),
            currentActiveMenu: '/marketing_promotion/shake_list',
          },
        },
        {
          path: 'turn_list',
          name: 'DrawTurnList',
          component: () => import('/@/views/marketing/promotion/draw/turn_list.vue'),
          meta: {
            title: t('routes.demo.marketing.turn_list'),
          },
        },
        {
          path: 'turn_list_to_add',
          name: 'TurnListToAdd',
          component: () => import('/@/views/marketing/promotion/draw/turn_add.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.turn_list_to_add'),
            currentActiveMenu: '/marketing_promotion/turn_list',
          },
        },
        {
          path: 'turn_list_to_edit',
          name: 'TurnListToEdit',
          component: () => import('/@/views/marketing/promotion/draw/turn_edit.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.turn_list_to_edit'),
            currentActiveMenu: '/marketing_promotion/turn_list',
          },
        },
        {
          path: 'turn_list_to_view',
          name: 'TurnListToView',
          component: () => import('/@/views/marketing/promotion/draw/turn_view.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.turn_list_to_view'),
            currentActiveMenu: '/marketing_promotion/turn_list',
          },
        },
        {
          path: 'turn_list_to_copy',
          name: 'TurnListToCopy',
          component: () => import('/@/views/marketing/promotion/draw/turn_copy.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.turn_list_to_copy'),
            currentActiveMenu: '/marketing_promotion/turn_list',
          },
        },
        // dev_mobile-end
      ],
    },
    // dev_mobile-start
    {
      path: '/marketing_svideo',
      name: 'MarketingSvideo',
      component: getParentLayout('MarketingSvideo'),
      meta: {
        orderNo: 42,
        icon: 'iconduanshipin',
        title: t('routes.demo.marketing.svideo_center'),
      },
      children: [
        {
          path: 'setting',
          name: 'SvideoSetting',
          component: () => import('/@/views/marketing/svideo/setting.vue'),
          meta: {
            title: t('routes.demo.marketing.svideo_setting'),
          },
        },
        {
          path: 'label',
          name: 'SvideoLabel',
          component: () => import('/@/views/marketing/svideo/label.vue'),
          meta: {
            title: t('routes.demo.marketing.svideo_label'),
          },
        },
        {
          path: 'video_theme',
          name: 'SvideoTheme',
          component: () => import('/@/views/marketing/svideo/theme.vue'),
          meta: {
            title: t('routes.demo.marketing.video_theme'),
          },
        },
        {
          path: 'video_theme_to_add',
          name: 'AddTheme',
          component: () => import('/@/views/marketing/svideo/add_theme_home.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.video_theme_to_add'),
            currentActiveMenu: '/marketing_svideo/video_theme',
          },
        },
        {
          path: 'video_theme_to_edit',
          name: 'EditTheme',
          component: () => import('/@/views/marketing/svideo/edit_theme.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.video_theme_to_edit'),
            currentActiveMenu: '/marketing_svideo/video_theme',
          },
        },
        {
          path: 'video_theme_bind_video',
          name: 'ViewThemeVideo',
          component: () => import('/@/views/marketing/svideo/view_theme_video.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.video_theme_bind_video'),
            currentActiveMenu: '/marketing_svideo/video_theme',
          },
        },
        {
          path: 'author_manage',
          name: 'AuthorManage',
          component: () => import('/@/views/marketing/svideo/author_manage.vue'),
          meta: {
            title: t('routes.demo.marketing.author_manage'),
          },
        },
        {
          path: 'video_manage',
          name: 'VideoManage',
          component: () => import('/@/views/marketing/svideo/video_manage.vue'),
          meta: {
            title: t('routes.demo.marketing.video_manage'),
          },
        },
        {
          path: 'video_manage_bind_goods',
          name: 'VideoGoods',
          component: () => import('/@/views/marketing/svideo/video_goods.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.video_manage_bind_goods'),
            currentActiveMenu: '/marketing_svideo/video_manage',
          },
        },
        {
          path: 'comment_lists',
          name: 'SvideoCommentLists',
          component: () => import('/@/views/marketing/svideo/comment_lists.vue'),
          meta: {
            title: t('routes.demo.marketing.comment_lists'),
          },
        },
        {
          path: 'comment_lists_to_view',
          name: 'CommentListsToView',
          component: () => import('/@/views/marketing/svideo/view_video_comments.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.comment_lists_to_view'),
            currentActiveMenu: '/marketing_svideo/comment_lists',
          },
        },
        
      ],
    },
    // dev_mobile-end
    
    {
      path: '/marketing_point',
      name: 'MarketingPoint',
      component: getParentLayout('MarketingPoint'),
      meta: {
        orderNo: 44,
        icon: 'iconjifenshangcheng',
        title: t('routes.demo.marketing.marketing_point'),
      },
      children: [
        // dev_mobile-start
        {
          path: 'diy_home',
          name: 'PointDiyHome',
          component: () => import('/@/views/marketing/point/mdiy/home.vue'),
          meta: {
            title: t('routes.demo.marketing.point_diy_home'),
          },
        },
        {
          path: 'diy_home_to_edit',
          name: 'PointDiyHomeEdit',
          component: () => import('/@/views/marketing/point/mdiy/home_edit.vue'),
          meta: {
            title: t('routes.demo.marketing.point_diy_home_edit'),
            hideMenu: true,
            showMenu: true,
            currentActiveMenu: '/marketing_point/diy_home',
          },
        },
        // dev_mobile-end
        {
          path: 'diy_style',
          name: 'PointDiyStyle',
          component: () => import('/@/views/marketing/point/diy_style.vue'),
          meta: {
            title: t('routes.demo.marketing.point_diy_style'),
          },
        },
        {
          path: 'setting',
          name: 'IntegralSetting',
          component: () => import('/@/views/marketing/point/setting.vue'),
          meta: {
            title: t('routes.demo.marketing.integral_setting'),
          },
        },
        {
          path: 'label',
          name: 'PointLabel',
          component: () => import('/@/views/marketing/point/label.vue'),
          meta: {
            title: t('routes.demo.marketing.point_label'),
          },
        },
        {
          path: 'goods_list',
          name: 'PointGoodsList',
          component: () => import('/@/views/marketing/point/goods/goods_list.vue'),
          meta: {
            title: t('routes.demo.marketing.point_goods_list'),
          },
        },
        {
          path: 'order_list',
          name: 'PointOrderList',
          component: () => import('/@/views/marketing/point/order/order_lists.vue'),
          meta: {
            title: t('routes.demo.marketing.point_order_list'),
          },
        },
        {
          path: 'order_list_to_detail',
          name: 'PointOrderListToDetail',
          component: () => import('/@/views/marketing/point/order/order_detail.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.point_order_list_to_detail'),
            currentActiveMenu: '/marketing_point/order_list',
          },
        },
        {
          path: 'bill_list',
          name: 'PointBillList',
          component: () => import('/@/views/marketing/point/bill/lists.vue'),
          meta: {
            title: t('routes.demo.marketing.point_bill_list'),
          },
        },
        {
          path: 'bill_list_to_detail',
          name: 'BillListToDetail',
          component: () => import('/@/views/marketing/point/bill/detail.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.bill_list_to_detail'),
            currentActiveMenu: '/marketing_point/bill_list',
          },
        },
      ],
    },
    //spreader-start
    {
      path: '/marketing_spreader',
      name: 'MarketingSpreader',
      component: getParentLayout('MarketingSpreader'),
      meta: {
        orderNo: 45,
        icon: 'icontuishou',
        title: t('routes.demo.marketing.spreader_system'),
      },
      children: [
        {
          path: 'setting',
          name: 'SpreaderSetting',
          component: () => import('/@/views/marketing/spreader/setting.vue'),
          meta: {
            title: t('routes.demo.marketing.spreader_setting'),
          },
        },
        {
          path: 'diy_home',
          name: 'SpreaderDiyHome',
          component: () => import('/@/views/marketing/spreader/diy_home.vue'),
          meta: {
            title: t('routes.demo.marketing.diy_home'),
          },
        },
        {
          path: 'diy_home_to_edit',
          name: 'SpreaderDiyHomeEdit',
          component: () => import('/@/views/marketing/spreader/diy_home_edit.vue'),
          meta: {
            title: t('routes.demo.marketing.diy_home_edit'),
            hideMenu: true,
            showMenu: true,
            currentActiveMenu: '/marketing_spreader/diy_home',
          },
        },
        {
          path: 'diy_style',
          name: 'SpreaderDiyStyle',
          component: () => import('/@/views/marketing/spreader/diy_style.vue'),
          meta: {
            title: t('routes.demo.marketing.spreader_diy_style'),
          },
        },
        {
          path: 'member_list',
          name: 'SpreaderMemberList',
          component: () => import('/@/views/marketing/spreader/member/list.vue'),
          meta: {
            title: t('routes.demo.marketing.spreader_member_list'),
          },
        },
        {
          path: 'member_grade',
          name: 'SpreaderMemberGrade',
          component: () => import('/@/views/marketing/spreader/member/grade.vue'),
          meta: {
            title: t('routes.demo.marketing.spreader_member_grade'),
          },
        },
        {
          path: 'goods_list',
          name: 'SpreaderGoodsList',
          component: () => import('/@/views/marketing/spreader/product/goods_list.vue'),
          meta: {
            title: t('routes.demo.marketing.spreader_goods_list'),
          },
        },
        {
          path: 'goods_label',
          name: 'SpreaderGoodsLabel',
          component: () => import('/@/views/marketing/spreader/product/label.vue'),
          meta: {
            title: t('routes.demo.marketing.spreader_goods_label'),
          },
        },
        {
          path: 'order_list',
          name: 'SpreaderOrderList',
          component: () => import('/@/views/marketing/spreader/order/list.vue'),
          meta: {
            title: t('routes.demo.marketing.spreader_order_list'),
          },
        },
        {
          path: 'order_list_to_detail',
          name: 'SpreaderOrderListToDetail',
          component: () => import('/@/views/marketing/spreader/order/order_detail.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.spreader_order_list_to_detail'),
            currentActiveMenu: '/marketing_spreader/order_list',
          },
        },
        {
          path: 'problem',
          name: 'SpreaderProblem',
          component: () => import('/@/views/marketing/spreader/problem/index.vue'),
          meta: {
            title: t('routes.demo.marketing.spreader_problem'),
          },
        },
        {
          path: 'problem_list_to_add',
          name: 'SpreaderProblemAdd',
          component: () => import('/@/views/marketing/spreader/problem/add_problem_home.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.spreader_problem_list_to_add'),
            currentActiveMenu: '/marketing_spreader/problem',
          },
        },
        {
          path: 'problem_list_to_edit',
          name: 'SpreaderProblemEdit',
          component: () => import('/@/views/marketing/spreader/problem/edit_problem.vue'),
          meta: {
            hideMenu: true,
            showMenu: true,
            title: t('routes.demo.marketing.spreader_problem_list_to_edit'),
            currentActiveMenu: '/marketing_spreader/problem',
          },
        },
      ],
    },
    //spreader-end
  ],
};

export default marketing;
