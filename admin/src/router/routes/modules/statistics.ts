import type { AppRouteModule } from '/@/router/types';

import { LAYOUT } from '/@/router/constant';
import { t } from '/@/hooks/web/useI18n';

const statistics: AppRouteModule = {
  path: '/stat',
  name: 'Statistics',
  component: LAYOUT,
  redirect: '/statistics/realtime',
  meta: {
    orderNo: 5,
    icon: 'iconv3_tongjizhongxin',
    title: '统计中心',
  },
  children: [
    {
      path: '/statistics',
      name: 'Stats_center',
      component: LAYOUT,
      redirect: '/statistics/realtime',
      meta: {
        orderNo: 51,
        icon: 'icontongjizhongxin1',
        title: '统计中心',
      },
      children: [
        {
          path: 'realtime',
          name: 'RealTime',
          component: () => import('/@/views/statistics/realTime.vue'),
          meta: {
            // affix: true,
            title: '实时分析',
          },
        },
        {
          path: 'trade',
          name: 'Trade',
          component: () => import('/@/views/statistics/trade.vue'),
          meta: {
            title: '交易分析',
          },
        },
        {
          path: 'flow',
          name: 'Flow',
          component: () => import('/@/views/statistics/flow.vue'),
          meta: {
            title: '流量分析',
          },
        },
        {
          path: 'goods_saling',
          name: 'Goods_sale',
          component: () => import('/@/views/statistics/goods_sale.vue'),
          meta: {
            title: '商品动销',
          },
        },
        {
          path: 'goods_category',
          name: 'Goods_category',
          component: () => import('/@/views/statistics/goods_category.vue'),
          meta: {
            title: '商品品类',
          },
        },
        {
          path: 'member',
          name: 'Member',
          component: () => import('/@/views/statistics/member.vue'),
          meta: {
            title: '会员分析',
          },
        },
        {
          path: 'store',
          name: 'Store',
          component: () => import('/@/views/statistics/store.vue'),
          meta: {
            title: '店铺分析',
          },
        },
        {
          path: 'region',
          name: 'Region',
          component: () => import('/@/views/statistics/region.vue'),
          meta: {
            title: '地域分析',
          },
        },
      ],
    },
  ],
};

export default statistics;
