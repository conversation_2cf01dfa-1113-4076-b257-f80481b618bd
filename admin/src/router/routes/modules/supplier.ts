import type { AppRouteModule } from '/@/router/types';

import { LAYOUT } from '/@/router/constant';
import { t } from '/@/hooks/web/useI18n';

const supplier: AppRouteModule = {
  path: '/supplier',
  name: 'Supplier',
  component: LAYOUT,
  redirect: '/supplier_setting/site_info',
  meta: {
    orderNo: 9,
    title: t('routes.demo.supplier.supplier_nav'),
  },
  children: [
    {
      path: '/supplier_setting',
      name: 'supplier_setting',
      component: LAYOUT,
      redirect: '/supplier_setting/site_info',
      meta: {
        orderNo: 1,
        icon: 'iconjibenpeizhi',
        title: t('routes.demo.supplier.supplier_setting'),
      },
      children: [
        {
          path: 'site_info',
          name: 'supplier_setting_site_info',
          component: () => import('/@/views/supplier/setting/site_info.vue'),
          meta: {
            title: t('routes.demo.supplier.site_info'),
          },
        },
        {
          path: 'pic_set',
          name: 'supplier_setting_pic_set',
          component: () => import('/@/views/supplier/setting/pic_set.vue'),
          meta: {
            title: t('routes.demo.supplier.pic_set'),
          },
        },
        {
          path: 'order',
          name: 'supplier_setting_order',
          component: () => import('/@/views/supplier/setting/order.vue'),
          meta: {
            title: t('routes.demo.supplier.order'),
          },
        },
        {
          path: 'msg_tpl',
          name: 'supplier_msgTpl',
          component: () => import('/@/views/supplier/setting/msg_tpl.vue'),
          meta: {
            title: t('routes.demo.supplier.msg_tpl'),
          },
        },
      ],
    },
    {
      path: '/supplier_product',
      name: 'supplier_product',
      component: LAYOUT,
      redirect: '/supplier_product/goods_setting',
      meta: {
        orderNo: 2,
        icon: 'iconshangpinguanli-v3',
        title: t('routes.demo.supplier.supplier_product'),
      },
      children: [
        {
          path: 'goods_setting',
          name: 'supplier_product_goods_setting',
          component: () => import('/@/views/supplier/goods/goods_setting.vue'),
          meta: {
            title: t('routes.demo.supplier.goods_setting'),
          },
        },
        {
          path: 'goods_list',
          name: 'SupplierProductGoodsList',
          component: () => import('/@/views/supplier/goods/goods_list.vue'),
          meta: {
            title: t('routes.demo.supplier.goods_list'),
          },
        },
        {
          path: 'goods_list_to_detail',
          name: 'supplier_product_goods_list_to_detail',
          component: () => import('/@/views/supplier/goods/goods_detail.vue'),
          meta: {
            hideMenu: true,
            title: t('routes.demo.manage.goods_list_to_detail'),
            showMenu: false,
            currentActiveMenu: '/supplier_product/goods_list',
          },
        },
      ],
    },
    {
      path: '/supplier_order',
      name: 'supplier_order',
      component: LAYOUT,
      redirect: '/supplier_order/order_lists',
      meta: {
        orderNo: 3,
        icon: 'icondingdanguanli',
        title: t('routes.demo.supplier.supplier_order'),
      },
      children: [
        {
          path: 'order_lists',
          name: 'SupplierOrderOrderLists',
          component: () => import('/@/views/supplier/order/order_lists.vue'),
          meta: {
            title: t('routes.demo.supplier.order_lists'),
          },
        },
        {
          path: 'order_lists_to_detail',
          name: 'OrderSupplierListToDetail',
          component: () => import('/@/views/supplier/order/order_detail.vue'),
          meta: {
            hideMenu: true,
            title: t('routes.demo.supplier.order_detail'),
            showMenu: false,
            currentActiveMenu: '/supplier_order/order_lists',
          },
        },
        {
          path: 'service',
          name: 'supplierService',
          component: () => import('/@/views/supplier/order/service.vue'),
          meta: {
            title: t('routes.demo.supplier.service'),
          },
        },
      ],
    },
    {
      path: '/supplier_bill',
      name: 'supplier_bill',
      component: LAYOUT,
      redirect: '/supplier_bill/lists',
      meta: {
        orderNo: 4,
        icon: 'iconjiesuanguanli',
        title: t('routes.demo.supplier.supplier_bill'),
      },
      children: [
        {
          path: 'lists',
          name: 'supplier_bill_lists',
          component: () => import('/@/views/supplier/bill/bill_lists.vue'),
          meta: {
            title: t('routes.demo.supplier.supplier_bill_lists'),
          },
        },
        {
          path: 'lists_to_detail',
          name: 'supplier_bill_lists_to_detail',
          component: () => import('/@/views/supplier/bill/bill_detail.vue'),
          meta: {
            hideMenu: true,
            title: t('routes.demo.supplier.supplier_bill_detail'),
            showMenu: false,
            currentActiveMenu: '/supplier_bill/lists',
          },
        },
      ],
    },
    {
      path: '/supplier_article',
      name: 'supplier_article',
      component: LAYOUT,
      redirect: '/supplier_article/article_cat_lists',
      meta: {
        orderNo: 5,
        icon: 'iconwenzhangguanli',
        title: t('routes.demo.supplier.supplier_article'),
      },
      children: [
        {
          path: 'article_cat_lists',
          name: 'supplier_article_cat_lists',
          component: () => import('/@/views/supplier/article/article_cat_lists.vue'),
          meta: {
            title: t('routes.demo.supplier.article_cat_lists'),
          },
        },
        {
          path: 'article_lists',
          name: 'supplier_article_lists',
          component: () => import('/@/views/supplier/article/article_lists.vue'),
          meta: {
            title: t('routes.demo.supplier.article_lists'),
          },
        },
        {
          path: 'article_lists_to_add',
          name: 'supplier_article_lists_to_add',
          component: () => import('/@/views/supplier/article/article_lists_to_add.vue'),
          meta: {
            hideMenu: true,
            title: '新增/编辑文章',
            showMenu: false,
            currentActiveMenu: '/supplier_article/article_lists',
          },
        },
      ],
    },
    {
      path: '/supplier_promotion',
      name: 'SupplierPromotion',
      component: LAYOUT,
      redirect: '/supplier_promotion/store_coupon',
      meta: {
        orderNo: 6,
        icon: 'iconyingyongzhongxin',
        title: t('routes.demo.supplier.center'),
      },
      children: [
        {
          path: 'store_coupon',
          name: 'SupplierStoreCoupon',
          component: () => import('/@/views/supplier/promotion/coupon/home.vue'),
          meta: {
            title: t('routes.demo.supplier.storeCoupon'),
          },
        },
        {
          path: 'store_coupon_to_receive_list',
          name: 'SupplierStoreCouponReceiveList',
          component: () =>
            import('/@/views/supplier/promotion/coupon/store_member_receive_lists.vue'),
          meta: {
            hideMenu: true,
            title: t('routes.demo.supplier.receiveCoupon'),
            showMenu: true,
            currentActiveMenu: '/supplier_promotion/store_coupon',
          },
        },
        {
          path: 'store_coupon_to_view',
          name: 'SupplierStoreCouponToView',
          component: () => import('/@/views/supplier/promotion/coupon/store_view_coupon.vue'),
          meta: {
            hideMenu: true,
            title: '优惠券详情',
            showMenu: true,
            currentActiveMenu: '/supplier_promotion/store_coupon',
          },
        },
        {
          path: 'full_discount',
          name: 'SupplierFullDiscount',
          component: () => import('/@/views/supplier/promotion/full/discount.vue'),
          meta: {
            title: t('routes.demo.supplier.full_discount'),
          },
        },
        {
          path: 'seckill',
          name: 'SupplierSeckillIndexList',
          component: () => import('/@/views/supplier/promotion/seckill/lists.vue'),
          meta: {
            title: t('routes.demo.supplier.seckill'),
          },
        },
        {
          path: 'seckill_detail',
          name: 'SupplierSeckillDetail',
          component: () => import('/@/views/supplier/promotion/seckill/detail.vue'),
          meta: {
            title: t('routes.demo.supplier.seckill_detail'),
            hideMenu: true,
            showMenu: true,
            currentActiveMenu: '/supplier_promotion/seckill',
          },
        },
        {
          path: 'seckill_goods_list',
          name: 'SupplierSeckillGoodsList',
          component: () => import('/@/views/supplier/promotion/seckill/seckill_goods_lists.vue'),
          meta: {
            title: t('routes.demo.supplier.seckill_goods_list'),
            hideMenu: true,
            showMenu: true,
            currentActiveMenu: '/supplier_promotion/seckill',
          },
        },
      ],
    },
    {
      path: '/supplier_decorate_pc',
      name: 'supplier_decorate_pc',
      component: LAYOUT,
      redirect: '/supplier_decorate_pc/diy_page_lists',
      meta: {
        orderNo: 7,
        title: 'PC装修',
        icon: 'iconPCzhuangxiu',
      },
      children: [
        {
          path: 'instance_template_lists',
          name: 'supplier_instance_template_lists',
          component: () => import('@/views/supplier/decorate/pc/template_list.vue'),
          meta: {
            title: '实例化模板',
          },
        },
        {
          path: 'instance_template_lists_to_add',
          name: 'supplier_instance_template_lists_to_add',
          component: () => import('@/views/supplier/decorate/pc/diy_template_edit.vue'),
          meta: {
            hideMenu: true,
            title: '模板编辑',
            currentActiveMenu: '/supplier_decorate_pc/instance_template_lists',
          },
        },
        {
          path: 'diy_page_lists',
          name: 'supplier_diy_page_lists',
          component: () => import('@/views/supplier/decorate/pc/diy_page_lists.vue'),
          meta: {
            title: '首页装修',
          },
        },
        {
          path: 'diy_page_lists_to_edit',
          name: 'supplier_diy_page_edit',
          component: () => import('@/views/supplier/decorate/pc/diy_page_edit.vue'),
          meta: {
            hideSideBar: true,
            title: '装修详情',
            currentActiveMenu: 'diy_page_lists',
            hideMenu: true,
            hideHeader: true,
          },
        },
        {
          path: 'topic_diy_page_lists',
          name: 'supplier_pc_topic_page_lists',
          component: () => import('@/views/supplier/decorate/pc/topic_page_lists.vue'),
          meta: {
            title: '专题装修',
          },
        },
        {
          path: 'home_setting',
          name: 'supplier_home_setting',
          component: () => import('@/views/supplier/decorate/pc/home_adv'),
          meta: {
            title: '首页广告',
          },
        },
        {
          path: 'diy_style',
          name: 'supplier_pc_diy_style',
          component: () => import('@/views/supplier/decorate/pc/diy_style.vue'),
          meta: {
            title: '风格配置',
          },
        },
        {
          path: 'nav',
          name: 'supplier_pc_nav_list',
          component: () => import('@/views/supplier/decorate/pc/nav_list.vue'),
          meta: {
            title: '导航设置',
          },
        },
        {
          path: 'footer',
          name: 'supplier_pc_deco_footer',
          component: () => import('@/views/supplier/decorate/pc/footer.vue'),
          meta: {
            title: '页脚设置',
          },
        },
      ],
    },
  ],
};

export default supplier;
