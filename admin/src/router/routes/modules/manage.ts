import type { AppRouteModule } from '/@/router/types';

import { LAYOUT } from '/@/router/constant';
import { t } from '/@/hooks/web/useI18n';

const manage: AppRouteModule = {
  path: '/manage',
  name: 'manage',
  component: LAYOUT,
  redirect: '/manage_product/goods_setting',
  meta: {
    orderNo: 2,
    icon: 'iconv3_shangchengguanli',
    title: t('routes.demo.manage.managenav'),
  },
  children: [
    {
      path: '/manage_product',
      name: 'manage_product',
      component: LAYOUT,
      redirect: '/manage_product/goods_setting',
      meta: {
        orderNo: 21,
        icon: 'iconshangpinguanli-v3',
        title: t('routes.demo.manage.manage_product'),
      },
      children: [
        {
          path: 'goods_setting',
          name: 'goods_setting',
          component: () => import('/@/views/manage/goods/goods_setting.vue'),
          meta: {
            title: t('routes.demo.manage.goods_setting'),
          },
        },
        {
          path: 'goods_list',
          name: 'ManageGoodsList',
          component: () => import('/@/views/manage/goods/goods_list.vue'),
          meta: {
            title: t('routes.demo.manage.goods_list'),
          },
        },
        {
          path: 'goods_list_to_detail',
          name: 'goods_list_to_detail',
          component: () => import('/@/views/manage/goods/goods_detail.vue'),
          meta: {
            hideMenu: true,
            title: t('routes.demo.manage.goods_list_to_detail'),
            showMenu: false,
          },
        },
        {
          path: 'cate_lists',
          name: 'cate_lists',
          component: () => import('/@/views/manage/goods/cate_lists.vue'),
          meta: {
            title: t('routes.demo.manage.cate_lists'),
          },
        },
        {
          path: 'brand',
          name: 'brand',
          component: () => import('/@/views/manage/goods/brand.vue'),
          meta: {
            title: t('routes.demo.manage.brand'),
          },
        },
        {
          path: 'search_attr',
          name: 'search_attr',
          component: () => import('/@/views/manage/goods/search_attr.vue'),
          meta: {
            title: t('routes.demo.manage.search_attr'),
          },
        },
        {
          path: 'goods_label',
          name: 'goods_label',
          component: () => import('/@/views/manage/goods/goods_label.vue'),
          meta: {
            title: t('routes.demo.manage.goods_label'),
          },
        },
      ],
    },
    {
      path: '/manage_goods_platform',
      name: 'manage_goods_platform',
      component: LAYOUT,
      redirect: '/manage_goods_platform/list',
      meta: {
        orderNo: 22,
        icon: 'iconshangpinkuguanli',
        title: t('routes.demo.manage.manage_goods_platform'),
      },
      children: [
        {
          path: 'list',
          name: 'platform_list',
          component: () => import('/@/views/manage/platform/platform_list.vue'),
          meta: {
            title: t('routes.demo.manage.platform_list'),
          },
        },
        {
          path: 'add',
          name: 'platform_list_add',
          component: () => import('/@/views/manage/platform/platform_list_add.vue'),
          meta: {
            title: t('routes.demo.manage.platform_list_add'),
          },
        },
        {
          path: 'list_to_edit',
          name: 'list_to_edit',
          component: () => import('/@/views/manage/platform/platform_edit.vue'),
          meta: {
            hideMenu: true,
            title: t('routes.demo.manage.platform_list_edit'),
            showMenu: false,
          },
        },
        ],
    },
    {
      path: '/manage_store',
      name: 'manage_store',
      component: LAYOUT,
      redirect: '/manage_store/own_list',
      meta: {
        orderNo: 23,
        icon: 'icondianpuguanli',
        title: t('routes.demo.manage.manage_store'),
      },
      children: [
        {
          path: 'own_list',
          name: 'own_list',
          component: () => import('/@/views/manage/settle/own_list.vue'),
          meta: {
            title: t('routes.demo.manage.own_list'),
          },
        },
        {
          path: 'settle_store_list',
          name: 'settle_store_list',
          component: () => import('/@/views/manage/settle/settle_store_list.vue'),
          meta: {
            title: t('routes.demo.manage.settle_store_list'),
          },
        },
        {
          path: 'settle_store_list_view',
          name: 'settle_store_list_view',
          component: () => import('/@/views/manage/settle/settle_store_list_detail.vue'),
          meta: {
            hideMenu: true,
            showMenu: false,
            title: t('routes.demo.manage.settle_store_list_view'),
            currentActiveMenu: '/manage_store/settle_store_list',
          },
        },
        {
          path: 'settle_store_list_apply_detail',
          name: 'settle_store_list_apply_detail',
          component: () => import('/@/views/manage/settle/settle_store_list_apply.vue'),
          meta: {
            hideMenu: true,
            showMenu: false,
            title: t('routes.demo.manage.settle_store_list_apply_detail'),
            currentActiveMenu: '/manage_store/settle_store_list',
          },
        },
        {
          path: 'settle_store_list_to_edit',
          name: 'settle_store_list_to_edit',
          component: () => import('/@/views/manage/settle/settle_store_list_edit.vue'),
          meta: {
            hideMenu: true,
            showMenu: false,
            title: t('routes.demo.manage.settle_store_list_to_edit'),
            currentActiveMenu: '/manage_store/settle_store_list',
          },
        },
        {
          path: 'grade_list',
          name: 'grade_list',
          component: () => import('/@/views/manage/settle/grade_list.vue'),
          meta: {
            title: t('routes.demo.manage.grade_list'),
          },
        },
        ],
    },
    {
      path: '/manage_order',
      name: 'manage_order',
      component: LAYOUT,
      redirect: '/manage_order/order_lists',
      meta: {
        orderNo: 24,
        icon: 'icondingdanguanli',
        title: t('routes.demo.manage.manage_order'),
      },
      children: [
        {
          path: 'order_lists',
          name: 'ManageOrderLists',
          component: () => import('/@/views/manage/order/order_lists.vue'),
          meta: {
            title: t('routes.demo.manage.order_lists'),
          },
        },
        {
          path: 'order_lists_to_detail',
          name: 'order_detail',
          component: () => import('/@/views/manage/order/order_detail.vue'),
          meta: {
            hideMenu: true,
            title: t('routes.demo.manage.order_detail'),
            showMenu: false,
            currentActiveMenu: '/manage_order/order_lists',
          },
        },
        {
          path: 'service',
          name: 'service',
          component: () => import('/@/views/manage/order/service.vue'),
          meta: {
            title: t('routes.demo.manage.service'),
          },
        },
        {
          path: 'evaluation',
          name: 'evaluation',
          component: () => import('/@/views/manage/order/evaluation.vue'),
          meta: {
            title: t('routes.demo.manage.evaluation'),
          },
        },
        {
          path: 'salereson_lists',
          name: 'salereson_lists',
          component: () => import('/@/views/manage/order/salereson_lists.vue'),
          meta: {
            title: t('routes.demo.manage.salereson_lists'),
          },
        },
      ],
    },
    {
      path: '/manage_bill',
      name: 'manage_bill',
      component: LAYOUT,
      redirect: '/manage_bill/lists',
      meta: {
        orderNo: 25,
        icon: 'iconjiesuanguanli',
        title: t('routes.demo.manage.manage_bill'),
      },
      children: [
        {
          path: 'lists',
          name: 'bill_lists',
          component: () => import('/@/views/manage/bill/bill_lists.vue'),
          meta: {
            title: t('routes.demo.manage.bill_lists'),
          },
        },
        {
          path: 'lists_to_detail',
          name: 'lists_to_detail',
          component: () => import('/@/views/manage/bill/bill_detail.vue'),
          meta: {
            hideMenu: true,
            title: '结算详情',
            showMenu: false,
            currentActiveMenu: '/manage_bill/lists',
          },
        },
      ],
    },
    {
      path: '/manage_article',
      name: 'manage_article',
      component: LAYOUT,
      redirect: '/manage_article/article_cat_lists',
      meta: {
        orderNo: 26,
        icon: 'iconwenzhangguanli',
        title: t('routes.demo.manage.manage_article'),
      },
      children: [
        {
          path: 'article_cat_lists',
          name: 'article_cat_lists',
          component: () => import('/@/views/manage/article/article_cat_lists.vue'),
          meta: {
            title: t('routes.demo.manage.article_cat_lists'),
          },
        },
        {
          path: 'article_lists',
          name: 'article_lists',
          component: () => import('/@/views/manage/article/article_lists.vue'),
          meta: {
            title: t('routes.demo.manage.article_lists'),
          },
        },
        {
          path: 'article_lists_to_add',
          name: 'article_lists_to_add',
          component: () => import('/@/views/manage/article/article_lists_to_add.vue'),
          meta: {
            hideMenu: true,
            title: '新增/编辑文章',
            showMenu: false,
          },
        },
      ],
    },
    {
      path: '/manage_material',
      name: 'manage_material',
      component: LAYOUT,
      redirect: '/manage_material/lists',
      meta: {
        orderNo: 27,
        icon: 'iconsucaiguanli',
        title: t('routes.demo.manage.manage_material'),
      },
      children: [
        {
          path: 'material_center',
          name: 'material_center',
          component: () => import('/@/views/manage/material/material_center.vue'),
          meta: {
            title: t('routes.demo.manage.material_center'),
          },
        },
        {
          path: 'material_setting',
          name: 'material_setting',
          component: () => import('/@/views/manage/material/material_setting.vue'),
          meta: {
            title: t('routes.demo.manage.material_setting'),
          },
        },
      ],
    },
  ],
};

export default manage;
