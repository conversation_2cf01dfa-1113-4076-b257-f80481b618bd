import type { AppRouteModule } from '/@/router/types';

import { LAYOUT } from '/@/router/constant';
import { t } from '/@/hooks/web/useI18n';

const member_manage: AppRouteModule = {
  path: '/member',
  name: 'MemberCenter',
  component: LAYOUT,
  redirect: '/member/lists',
  meta: {
    orderNo: 3,
    icon: 'iconv3_huiyuanzhongxin',
    title: t('routes.demo.member.management'),
  },
  children: [
    {
      path: '/member',
      name: 'MemberManage',
      component: LAYOUT,
      redirect: '/member/lists',
      meta: {
        orderNo: 31,
        title: t('routes.demo.member.manage'),
        icon: 'iconhuiyuanguanli',
      },
      children: [
        {
          path: 'lists',
          name: 'MemberLists',
          component: () => import('/@/views/member/management/lists.vue'),
          meta: {
            title: t('routes.demo.member.lists'),
          },
        },
        {
          path: 'lists_to_detail',
          name: 'ListsToDetail',
          component: () => import('/@/views/member/management/lists_to_detail.vue'),
          meta: {
            hideMenu: true,
            title: t('routes.demo.member.list_to_detail'),
            showMenu: true,
            currentActiveMenu: '/member/lists',
          },
        },
        {
          path: 'super',
          name: 'MemberSuper',
          component: () => import('/@/views/member/management/super.vue'),
          meta: {
            title: t('routes.demo.member.super'),
          },
        },
        {
          path: 'super_grade',
          name: 'MemberSuperGrade',
          component: () => import('/@/views/member/management/super_grade_manage.vue'),
          meta: {
            title: t('routes.demo.member.super_grade'),
          },
        },
        {
          path: 'super_grade_to_add',
          name: 'MemberSuperGradeToAdd',
          component: () => import('/@/views/member/management/super_grade_add.vue'),
          meta: {
            title: t('routes.demo.member.super_grade_to_add'),
            hideMenu: true,
            showMenu: true,
            currentActiveMenu: '/member/super_grade',
          },
        },
        {
          path: 'super_progress',
          name: 'MemberSuperProgress',
          component: () => import('/@/views/member/management/super_progress_manage.vue'),
          meta: {
            title: t('routes.demo.member.super_progress'),
          },
        },
        {
          path: 'grade_rules',
          name: 'MemberSuperGradeRules',
          component: () => import('/@/views/member/management/grade_rules.vue'),
          meta: {
            title: t('routes.demo.member.grade_rules'),
          },
        },
        {
          path: 'recharge',
          name: 'MemberRecharges',
          component: () => import('/@/views/member/management/recharge.vue'),
          meta: {
            title: t('routes.demo.member.recharge'),
          },
        },
        {
          path: 'withdraw',
          name: 'MemberWithdraw',
          component: () => import('/@/views/member/management/withdraw.vue'),
          meta: {
            title: t('routes.demo.member.withdraw'),
          },
        },
        {
          path: 'balance_log',
          name: 'MemberBalanceLog',
          component: () => import('/@/views/member/management/balance_log.vue'),
          meta: {
            title: t('routes.demo.member.balance_log'),
          },
        },
        {
          path: 'point_setting',
          name: 'MemberPointSetting',
          component: () => import('/@/views/member/management/point_setting.vue'),
          meta: {
            title: t('routes.demo.member.point_setting'),
          },
        },
      ],
    },
  ],
};

export default member_manage;
