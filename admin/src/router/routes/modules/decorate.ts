import { AppRouteRecordRaw } from '/@/router/types';
import { LAYOUT } from '/@/router/constant';

const decorate: AppRouteRecordRaw = {
  path: '/decorate',
  component: LAYOUT,
  redirect: '/decorate_pc/diy_page_lists',
  name: 'decorate',
  meta: {
    orderNo: 6,
    icon: 'iconv3_zhuangxiu',
    title: '装修',
  },
  children: [
    // dev_pc-start
    {
      path: '/decorate_pc',
      name: 'decorate_pc',
      redirect: '/decorate_pc/diy_page_lists',
      meta: {
        orderNo: 61,
        title: 'PC装修',
        icon: 'iconPCzhuangxiu',
      },
      children: [
        {
          path: '/decorate_pc/instance_template_lists',
          name: 'instance_template_lists',
          component: () => import('@/views/decorate/pc/template_list.vue'),
          meta: {
            title: '实例化模板',
          },
        },
        {
          path: 'instance_template_lists_to_add',
          name: 'instance_template_lists_to_add',
          component: () => import('@/views/decorate/pc/diy_template_edit.vue'),
          meta: {
            hideMenu: true,
            title: '模板编辑',
            currentActiveMenu: '/decorate_pc/instance_template_lists',
          },
        },
        {
          path: '/decorate_pc/diy_page_lists',
          name: 'diy_page_lists',
          component: () => import('@/views/decorate/pc/diy_page_lists.vue'),
          meta: {
            title: '首页装修',
          },
        },
        {
          path: 'diy_page_lists_to_edit',
          name: 'diy_page_edit',
          component: () => import('@/views/decorate/pc/diy_page_edit.vue'),
          meta: {
            hideSideBar: true,
            title: '装修详情',
            currentActiveMenu: 'diy_page_lists',
            hideMenu: true,
            hideHeader: true,
          },
        },
        {
          path: '/decorate_pc/topic_diy_page_lists',
          name: 'pc_topic_page_lists',
          component: () => import('@/views/decorate/pc/topic_page_lists.vue'),
          meta: {
            title: '专题装修',
          },
        },
        {
          path: '/decorate_pc/home_setting',
          name: 'home_setting',
          component: () => import('@/views/decorate/pc/home_adv'),
          meta: {
            title: '首页广告',
          },
        },
        {
          path: '/decorate_pc/diy_style',
          name: 'pc_diy_style',
          component: () => import('@/views/decorate/pc/diy_style.vue'),
          meta: {
            title: '风格配置',
          },
        },
        {
          path: '/decorate_pc/nav',
          name: 'pc_nav_list',
          component: () => import('@/views/decorate/pc/nav_list.vue'),
          meta: {
            title: '导航设置',
          },
        },
        {
          path: '/decorate_pc/footer',
          name: 'pc_deco_footer',
          component: () => import('@/views/decorate/pc/footer.vue'),
          meta: {
            title: '页脚设置',
          },
        },
      ],
    },
    // dev_pc-end
    // dev_mobile-start
    {
      path: '/decorate_m',
      name: 'decorate_m',
      redirect: '/decorate_m/lists',
      meta: {
        orderNo: 62,
        title: '手机装修',
        icon: 'iconshoujizhuangxiu',
      },
      children: [
        {
          path: '/decorate_m/lists',
          name: 'mobile_diy_page_lists',
          component: () => import('@/views/decorate/mobile/diy_page_lists.vue'),
          meta: {
            title: '首页装修',
          },
        },
        {
          path: '/decorate_m/lists_to_diy',
          name: 'mobile_diy_page_lists_add',
          component: () => import('@/views/decorate/mobile/diy_page_lists_add.vue'),
          meta: {
            hideMenu: true,
            title: '装修详情',
            showMenu: true,
            currentActiveMenu: '/decorate_m/lists',
          },
        },
        {
          path: '/decorate_m/topic_lists',
          name: 'mobile_topic_page_lists',
          component: () => import('@/views/decorate/mobile/topic_page_lists.vue'),
          meta: {
            title: '专题装修',
          },
        },
        {
          path: '/decorate_m/topic_lists_to_diy',
          name: 'mobile_topic_page_lists_add',
          component: () => import('@/views/decorate/mobile/diy_page_lists_add.vue'),
          meta: {
            hideMenu: true,
            title: '装修详情',
            showMenu: true,
            currentActiveMenu: '/decorate_m/topic_lists',
          },
        },
        {
          path: '/decorate_m/cat_pic',
          name: 'mobile_category_pic',
          component: () => import('@/views/decorate/mobile/category_pic.vue'),
          meta: {
            title: '分类图片',
          },
        },
        {
          path: '/decorate_m/diy_style',
          name: 'mobile_diy_style',
          component: () => import('@/views/decorate/mobile/diy_style.vue'),
          meta: {
            title: '风格配置',
          },
        },
      ],
    },
    // dev_mobile-end
    
  ],
};

export default decorate;
