.custom_svg_iconfont {
  position: relative;
  top: 3px;
  left: 1px;
}
.stat_part .title_add_goods {
  height: 48px !important;
}
.g2_tooltip_custom {
  box-sizing: border-box;
  width: auto;
  padding: 12px;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.8);
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);
  color: #fff;
  font-size: 14px;
}
.g2_tooltip_custom .g2-tooltip-title {
  margin-bottom: 6px;
}
.g2_tooltip_custom i {
  display: inline-block;
  position: relative;
  top: -3px;
  width: 6px;
  height: 6px;
  margin-right: 8px;
  border-radius: 6px;
}
.map_charts {
  position: relative;
  top: 50%;
  left: 50%;
  transform: translateY(-50%) translateX(-50%);
}
.map_charts canvas {
  margin: 0 auto;
}
.table_td_center_elliplis span {
  overflow: hidden;
  color: #ff701e;
  font-size: 13px;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.update_time_panel {
  margin-left: 10px;
}
.update_time_panel span {
  margin-right: 10px;
  color: #414141;
  font-size: 13px;
}
.update_time_panel .reload_icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-top: 2px;
  background-image: url('../../assets/images/home_basic/reload_icon.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  cursor: pointer;
}
.update_time_panel .reload_icon:hover {
  background-image: url('../../assets/images/home_basic/reload_icon_active.png');
}
.num_stat_item {
  box-sizing: border-box;
  align-items: stretch;
  width: 100%;
  padding: 20px 15px;
  border-top: 1px solid rgba(216, 216, 216, 0.5);
}
.num_stat_item .left_slide {
  flex: 0 0 100px;
  background-color: #fff7f4;
}
.num_stat_item .left_slide .slide_icon {
  display: block;
  width: 40px;
  height: 40px;
  margin-bottom: 5px;
}
.num_stat_item .left_slide .slide_title {
  color: #333;
  font-size: 14px;
}
.num_stat_item .right_main {
  height: 100%;
  overflow: auto;
}
@media (max-width: 1700px) {
  .num_stat_item .right_main ul li {
    padding-left: 15px !important;
  }
}
.num_stat_item .right_main ul li {
  box-sizing: border-box;
  width: 250px;
  height: 90px;
  margin-left: 15px;
  padding-left: 30px;
  border-radius: 2px;
  background-color: rgba(238, 238, 238, 0.2);
}
.num_stat_item .right_main ul li .up_desc span {
  margin-right: 6px;
  color: #333;
  font-size: 14px;
}
.num_stat_item .right_main ul li .up_desc img {
  margin-top: -3px;
  cursor: pointer;
}
.num_stat_item .right_main ul li .down_num {
  margin-top: 5px;
}
.num_stat_item .right_main ul li .down_num span {
  color: #333;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 28px;
  font-weight: 600;
}
.colorful_store_item_5 {
  background-color: #fff;
}
.colorful_store_item_5 ul li {
  width: 19% !important;
}
.saling_stat .colorful_store_item .number::after {
  width: 104% !important;
}
.colorful_store_item {
  box-sizing: border-box;
  width: 100%;
  padding: 25px;
  border-top: 1px solid rgba(216, 216, 216, 0.5);
  background: #fff;
}
.colorful_store_item ul li {
  box-sizing: border-box;
  width: 24%;
  height: 163px;
  padding: 12px 30px;
  padding-left: 2.4%;
  background-repeat: no-repeat;
  background-position: center top;
  background-size: auto 100%;
}
@media (max-width: 1400px) {
  .colorful_store_item ul li .down_desc {
    padding-left: 8px !important;
  }
}
.colorful_store_item ul li .right_operate {
  position: relative;
}
.colorful_store_item ul li .right_operate .select_modal {
  position: absolute;
  top: 24px;
  left: -2px;
  width: 80px;
  height: 92px;
  border-radius: 2px;
  background: #fff;
  box-shadow: 0 1px 8px 0 rgba(203, 136, 0, 0.34);
}
.colorful_store_item ul li .right_operate .select_modal .select_option {
  position: relative;
  width: 100%;
  padding: 4px 0;
  padding-left: 8px;
  color: #414141;
  font-size: 14px;
  cursor: pointer;
}
.colorful_store_item ul li .right_operate .select_modal .select_option:hover,
.colorful_store_item ul li .right_operate .select_modal .select_option.current {
  color: #ff590d;
}
.colorful_store_item ul li .right_operate .select_modal .select_option svg {
  position: absolute;
  top: 0;
  right: 11px;
  bottom: 0;
  margin: auto;
  transform: scale(0.85);
  transform-origin: 100% 50%;
}
.colorful_store_item ul li .right_operate .select_modal .select_option.current {
  color: #ff590d;
}
.colorful_store_item ul li .right_operate .select_modal.hide {
  display: none;
}
.colorful_store_item ul li .right_operate span {
  color: #fff;
  font-size: 14px;
  cursor: pointer;
}
.colorful_store_item ul li .right_operate i {
  display: block;
  position: relative;
  bottom: -4px;
  width: 0;
  height: 0;
  margin-left: 5px;
  transform-origin: 50% 25%;
  transition: all 0.36s;
  border: 5px solid transparent;
  border-top: 6px solid #fff;
  font-style: normal;
  cursor: pointer;
}
.colorful_store_item ul li .right_operate i.open {
  transform: rotate(180deg);
}
.colorful_store_item ul li .up_title {
  color: #fff;
  font-size: 16px;
}
.colorful_store_item ul li .number {
  position: relative;
  width: 66%;
  min-height: 58px;
  margin-top: 24px;
  padding-bottom: 15px;
  color: #fff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 34px;
  font-weight: 700;
}
.colorful_store_item ul li .number::after {
  content: '';
  display: block;
  position: absolute;
  bottom: 0;
  left: -8px;
  width: 78%;
  height: 1px;
  background-color: #fff;
}
.colorful_store_item ul li .down_desc {
  box-sizing: border-box;
  margin-top: 10px;
  padding-left: 15px;
  white-space: nowrap;
}
.colorful_store_item ul li .down_desc i {
  position: relative;
  top: 3px;
  left: 2px;
}
.colorful_store_item ul li .down_desc .intro {
  color: #fff;
  font-size: 14px;
}
.colorful_store_item ul li .down_desc .difference {
  color: #fff;
  font-size: 14px;
}
.preview_stat_panel {
  position: relative;
  box-sizing: border-box;
  padding: 0 20px;
  padding-bottom: 50px;
}
@media (max-width: 1500px) {
  .preview_stat_panel .charts_panel img {
    width: 500px;
    height: 454px;
  }
  .preview_stat_panel .charts_panel .common_position_stat {
    width: 105px !important;
    font-size: 12px !important;
  }
  .preview_stat_panel .charts_panel .first_rate {
    top: 120px;
    right: 46px !important;
  }
  .preview_stat_panel .charts_panel .second_rate {
    top: 206px;
    right: 0 !important;
  }
  .preview_stat_panel .charts_panel .third_rate {
    top: 277px;
    right: 72px !important;
  }
}
.preview_stat_panel .charts_panel {
  position: absolute;
  z-index: 4;
  top: 0;
  right: 52px;
}
.preview_stat_panel .charts_panel .charts_stat_info {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.preview_stat_panel .charts_panel .charts_stat_info .common_position_stat {
  position: absolute;
  width: 120px;
  height: 52px;
  border-radius: 2px;
  background: #98c94a;
  color: #fff;
  font-size: 14px;
}
.preview_stat_panel .charts_panel .charts_stat_info .common_position_stat span {
  margin-top: 5px;
}
.preview_stat_panel .charts_panel .charts_stat_info .first_rate {
  top: 120px;
  right: 113px;
}
.preview_stat_panel .charts_panel .charts_stat_info .second_rate {
  top: 206px;
  right: 1px;
}
.preview_stat_panel .charts_panel .charts_stat_info .third_rate {
  top: 277px;
  right: 114px;
}
.preview_stat_panel .charts_panel .charts_stat_info ul {
  width: 76%;
}
.preview_stat_panel .charts_panel .charts_stat_info ul li {
  height: 158px;
  color: #fff;
  font-size: 18px;
}
.preview_stat_panel .charts_panel .charts_stat_info ul img {
  display: block;
  width: 26px;
  height: 26px;
  margin-right: 5px;
}
.preview_stat_panel .charts_panel > img {
  width: 789px;
  height: 454px;
}
.preview_stat_panel .funnel .part {
  position: relative;
  width: 360px;
  height: 360px;
}
.preview_stat_panel .funnel .part .funnel_center_img {
  width: 20px;
  height: 20px;
}
.preview_stat_panel .funnel .part .funnel_center_img_desc {
  margin-left: 3px;
  color: #fff;
  font-size: 16px;
  font-weight: 600;
}
.preview_stat_panel .funnel .part .center_item_top {
  position: relative;
  z-index: 1;
  width: 240px;
  height: 0;
  border-top: 119px solid #8bd6b1;
  border-right: 33px solid transparent;
  border-left: 33px solid transparent;
  background: #fdfdfd;
}
.preview_stat_panel .funnel .part .center_item_top .top_content {
  position: absolute;
  z-index: 2;
  top: -119px;
  left: -34px;
  width: 240px;
  height: 119px;
}
.preview_stat_panel .funnel .part .center_item_center {
  position: relative;
  z-index: 1;
  width: 174px;
  height: 0;
  border-top: 119px solid #82d3d0;
  border-right: 33px solid transparent;
  border-left: 33px solid transparent;
  background: #f9f9f9;
}
.preview_stat_panel .funnel .part .center_item_center .center_content {
  position: absolute;
  z-index: 2;
  top: -119px;
  left: -33px;
  width: 172px;
  height: 119px;
}
.preview_stat_panel .funnel .part .center_item_bottom {
  position: relative;
  z-index: 1;
  width: 108px;
  height: 0;
  border-top: 119px solid #71c4df;
  border-right: 33px solid transparent;
  border-left: 33px solid transparent;
  background: #f5f5f5;
}
.preview_stat_panel .funnel .part .center_item_bottom .bottom_content {
  position: absolute;
  z-index: 2;
  top: -119px;
  left: -31px;
  width: 100px;
  height: 119px;
}
.preview_stat_panel .funnel .part .left_top_content {
  position: absolute;
  z-index: 1;
  top: 68px;
  left: -45px;
  width: 117px;
  padding: 5px 0;
  background: #8bd6b1;
  text-align: center;
}
.preview_stat_panel .funnel .part .left_bottom_content {
  position: absolute;
  z-index: 1;
  bottom: 100px;
  left: -45px;
  width: 117px;
  padding: 5px 0;
  background: #8bd6b1;
  text-align: center;
}
.preview_stat_panel .funnel .part .right_content {
  position: absolute;
  z-index: 1;
  right: -23px;
  bottom: 180px;
  width: 117px;
  padding: 5px 0;
  background: #8bd6b1;
  text-align: center;
}
.preview_stat_panel .funnel .part .side_content_desc {
  color: #fff;
  font-size: 13px;
  font-weight: 600;
}
.preview_stat_panel .funnel .left_top_line {
  position: absolute;
  top: 30px;
  left: 19px;
  width: 74px;
  height: 118px;
  border: 2px solid #b0e1c9;
  border-right: 0;
}
.preview_stat_panel .funnel .left_bottom_line::after,
.preview_stat_panel .funnel .left_top_line::after {
  content: '';
  position: absolute;
  z-index: 2;
  right: 0;
  bottom: 1px;
  width: 14px;
  height: 2px;
  transform: rotate(30deg);
  background: #b0e1c9;
}
.preview_stat_panel .funnel .left_bottom_line {
  position: absolute;
  bottom: 50px;
  left: 19px;
  width: 109px;
  height: 150px;
  border: 2px solid #b0e1c9;
  border-right: 0;
}
.preview_stat_panel .funnel .right_line {
  position: absolute;
  top: 30px;
  right: 40px;
  width: 88px;
  height: 280px;
  border: 2px solid #b0e1c9;
  border-left: 0;
}
.preview_stat_panel .funnel .right_line::after {
  content: '';
  position: absolute;
  z-index: 2;
  bottom: 1px;
  left: 0;
  width: 14px;
  height: 2px;
  transform: rotate(-30deg);
  background: #b0e1c9;
}
.preview_stat_panel .preview_stat_panel_back {
  overflow: hidden;
  border-radius: 10px;
  box-shadow: 0 1px 12px 0 rgba(0, 0, 0, 0.06);
}
.preview_stat_panel .charts_panel_operate {
  position: absolute;
  z-index: 4;
  top: 0;
  right: 131px;
}
.preview_stat_panel .charts_panel_operate .charts_stat_info {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.preview_stat_panel .charts_panel_operate .charts_stat_info .common_position_stat {
  position: absolute;
  width: 120px;
  height: 52px;
  border-radius: 2px;
  background: #98c94a;
  color: #fff;
  font-size: 14px;
}
.preview_stat_panel .charts_panel_operate .charts_stat_info .common_position_stat span {
  margin-top: 5px;
}
.preview_stat_panel .charts_panel_operate .charts_stat_info .first_rate {
  top: 120px;
  right: 113px;
}
.preview_stat_panel .charts_panel_operate .charts_stat_info .second_rate {
  top: 206px;
  right: 1px;
}
.preview_stat_panel .charts_panel_operate .charts_stat_info .third_rate {
  top: 277px;
  right: 114px;
}
.preview_stat_panel .charts_panel_operate .charts_stat_info ul {
  width: 76%;
}
.preview_stat_panel .charts_panel_operate .charts_stat_info ul li {
  height: 158px;
  color: #fff;
  font-size: 18px;
}
.preview_stat_panel .charts_panel_operate .charts_stat_info ul img {
  display: block;
  width: 26px;
  height: 26px;
  margin-right: 5px;
}
.preview_stat_panel .charts_panel_operate > img {
  width: 789px;
  height: 454px;
}
.preview_stat_panel .funnel_operate .part {
  position: relative;
  width: 520px;
  height: 597px;
}
.preview_stat_panel .funnel_operate .part .funnel_center_img {
  width: 20px;
  height: 20px;
}
.preview_stat_panel .funnel_operate .part .funnel_center_img_desc {
  margin-left: 3px;
  color: #fff;
  font-size: 16px;
  font-weight: 600;
}
.preview_stat_panel .funnel_operate .part .center_item_head {
  position: relative;
  z-index: 1;
  width: 495px;
  height: 0;
  border-top: 119px solid #add9c3;
  border-right: 46px solid transparent;
  border-left: 46px solid transparent;
}
.preview_stat_panel .funnel_operate .part .center_item_head .top_content {
  position: absolute;
  z-index: 2;
  top: -119px;
  left: -47px;
  width: 495px;
  height: 119px;
}
.preview_stat_panel .funnel_operate .part .center_item_top {
  position: relative;
  z-index: 2;
  width: 403px;
  height: 0;
  border-top: 119px solid #8bd6b1;
  border-right: 46px solid transparent;
  border-left: 46px solid transparent;
  background: #f5f5f5;
}
.preview_stat_panel .funnel_operate .part .center_item_top .top_content {
  position: absolute;
  z-index: 2;
  top: -119px;
  left: -47px;
  width: 403px;
  height: 119px;
}
.preview_stat_panel .funnel_operate .part .center_item_center {
  position: relative;
  z-index: 2;
  width: 311px;
  height: 0;
  border-top: 119px solid #82d3d0;
  border-right: 46px solid transparent;
  border-left: 46px solid transparent;
  background: #f5f5f5;
}
.preview_stat_panel .funnel_operate .part .center_item_center .center_content {
  position: absolute;
  z-index: 2;
  top: -119px;
  left: -47px;
  width: 311px;
  height: 119px;
}
.preview_stat_panel .funnel_operate .part .center_item_bottom {
  position: relative;
  z-index: 2;
  width: 219px;
  height: 0;
  border-top: 119px solid #71c4df;
  border-right: 46px solid transparent;
  border-left: 46px solid transparent;
  background: #f5f5f5;
}
.preview_stat_panel .funnel_operate .part .center_item_bottom .bottom_content {
  position: absolute;
  z-index: 2;
  top: -119px;
  left: -47px;
  width: 219px;
  height: 119px;
}
.preview_stat_panel .funnel_operate .part .center_item_footer {
  position: relative;
  z-index: 2;
  width: 127px;
  height: 0;
  border-top: 119px solid #7ab6c9;
  border-right: 46px solid transparent;
  border-left: 46px solid transparent;
  background: #f5f5f5;
}
.preview_stat_panel .funnel_operate .part .center_item_footer .bottom_content {
  position: absolute;
  z-index: 2;
  top: -119px;
  left: -47px;
  width: 127px;
  height: 119px;
}
.preview_stat_panel .funnel_operate .part .left_top_content {
  position: absolute;
  z-index: 1;
  top: 68px;
  left: -104px;
  width: 117px;
  padding: 5px 0;
  background: #8bd6b1;
  text-align: center;
}
.preview_stat_panel .funnel_operate .part .left_top_content_one {
  position: absolute;
  z-index: 1;
  top: 193px;
  left: -104px;
  width: 117px;
  padding: 5px 0;
  background: #8bd6b1;
  text-align: center;
}
.preview_stat_panel .funnel_operate .part .left_top_content_two {
  position: absolute;
  z-index: 1;
  top: 320px;
  left: -104px;
  width: 117px;
  padding: 5px 0;
  background: #8bd6b1;
  text-align: center;
}
.preview_stat_panel .funnel_operate .part .left_bottom_content {
  position: absolute;
  z-index: 1;
  bottom: 111px;
  left: -104px;
  width: 117px;
  padding: 5px 0;
  background: #8bd6b1;
  text-align: center;
}
.preview_stat_panel .funnel_operate .part .right_content {
  position: absolute;
  z-index: 1;
  right: -79px;
  bottom: 281px;
  width: 117px;
  padding: 5px 0;
  background: #8bd6b1;
  text-align: center;
}
.preview_stat_panel .funnel_operate .part .side_content_desc {
  color: #fff;
  font-size: 13px;
  font-weight: 600;
}
.preview_stat_panel .funnel_operate .left_top_line {
  position: absolute;
  top: 30px;
  left: -45px;
  width: 105px;
  height: 114px;
  border: 2px solid #b0e1c9;
  border-right: 0;
}
.preview_stat_panel .funnel_operate .left_top_line_one {
  position: absolute;
  z-index: 1;
  top: 157px;
  left: -45px;
  width: 146px;
  height: 114px;
  border: 2px solid #b0e1c9;
  border-right: 0;
}
.preview_stat_panel .funnel_operate .left_top_line_two {
  position: absolute;
  top: 283px;
  left: -45px;
  width: 198px;
  height: 114px;
  border: 2px solid #b0e1c9;
  border-right: 0;
}
.preview_stat_panel .funnel_operate .left_bottom_line::after,
.preview_stat_panel .funnel_operate .left_top_line::after,
.preview_stat_panel .funnel_operate .left_top_line_one::after,
.preview_stat_panel .funnel_operate .left_top_line_two::after {
  content: '';
  position: absolute;
  z-index: 2;
  right: 0;
  bottom: 1px;
  width: 14px;
  height: 2px;
  transform: rotate(30deg);
  background: #b0e1c9;
}
.preview_stat_panel .funnel_operate .left_bottom_line {
  position: absolute;
  bottom: 75px;
  left: -45px;
  width: 242px;
  height: 114px;
  border: 2px solid #b0e1c9;
  border-right: 0;
}
.preview_stat_panel .funnel_operate .right_line {
  position: absolute;
  top: 35px;
  right: -23px;
  width: 220px;
  height: 503px;
  border: 2px solid #b0e1c9;
  border-left: 0;
}
.preview_stat_panel .funnel_operate .right_line::after {
  content: '';
  position: absolute;
  z-index: 2;
  bottom: 1px;
  left: 0;
  width: 14px;
  height: 2px;
  transform: rotate(-30deg);
  background: #b0e1c9;
}
.preview_stat_panel .stat_item {
  box-sizing: border-box;
  margin-top: -50px;
  padding: 10px 22px;
  padding-bottom: 68px;
}
.preview_stat_panel .stat_item ul li {
  width: 18%;
}
.preview_stat_panel .stat_item ul li .up_label span {
  margin-right: 5px;
  color: #333;
  font-size: 14px;
}
.preview_stat_panel .stat_item ul li .up_label img {
  display: block;
  width: 16px;
  height: 16px;
  cursor: pointer;
}
.preview_stat_panel .stat_item ul li .num {
  min-height: 39px;
  margin: 5px 0;
  color: #333;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 28px;
  font-weight: 500;
}
.preview_stat_panel .stat_item ul li .down_difference {
  color: rgba(51, 51, 51, 0.4);
}
.preview_stat_panel .stat_item ul li .down_difference .label {
  margin-right: 8px;
  color: rgba(51, 51, 51, 0.4);
  font-size: 14px;
}
.preview_stat_panel .stat_item ul li .down_difference .difference_num {
  color: #c41a1a;
  font-size: 14px;
}
.preview_stat_panel .stat_item:nth-child(2) {
  margin-top: 0 !important;
  border-radius: 0 0 20px 20px;
  background: #fdfdfd;
  box-shadow: 0 1px 12px 0 rgba(0, 0, 0, 0.06);
}
.preview_stat_panel .stat_item:nth-child(3) {
  position: relative;
  z-index: 2;
  border-radius: 0 0 20px 20px;
  background: #f9f9f9;
  box-shadow: 0 3px 6px 0 #f5f5f5;
}
.preview_stat_panel .stat_item:nth-child(4) {
  position: relative;
  z-index: 3;
  padding-bottom: 18px;
  border-radius: 20px 20px 0 0;
  background: #f5f5f5;
  box-shadow: 0 2px 4px 0 rgba(187, 187, 187, 0.18);
}
.preview_stat_panel .stat_item_operate {
  display: flex;
  box-sizing: border-box;
  align-items: center;
  height: 119px;
  padding-left: 20px;
  border-bottom: 1px solid #fff;
  background: #f5f5f5;
}
.preview_stat_panel .stat_item_operate ul li {
  width: 18%;
}
.preview_stat_panel .stat_item_operate ul li .up_label span {
  margin-right: 5px;
  color: #333;
  font-size: 14px;
}
.preview_stat_panel .stat_item_operate ul li .up_label img {
  display: block;
  width: 16px;
  height: 16px;
  cursor: pointer;
}
.preview_stat_panel .stat_item_operate ul li .num {
  min-height: 39px;
  margin: 5px 0;
  color: #333;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 28px;
  font-weight: 500;
}
.preview_stat_panel .stat_item_operate ul li .down_difference {
  color: rgba(51, 51, 51, 0.4);
}
.preview_stat_panel .stat_item_operate ul li .down_difference .label {
  margin-right: 8px;
  color: rgba(51, 51, 51, 0.4);
  font-size: 14px;
}
.preview_stat_panel .stat_item_operate ul li .down_difference .difference_num {
  color: #c41a1a;
  font-size: 14px;
}
.preview_stat_panel .stat_item_operate:last-child {
  border-bottom: none;
}
.common_table_item {
  width: calc(50% - 5px);
  background-color: #fff;
}
.common_table_item .store_list_operate_panel {
  position: relative;
  box-sizing: border-box;
  width: calc(100% - 11px);
  padding: 0 11px;
  padding-top: 20px;
}
.common_table_item .store_list_operate_panel > form {
  position: relative;
}
.common_table_item .label_panel {
  position: relative;
  border-bottom: 1px solid #eee;
}
.common_table_item .table_main_overflow_hidden {
  overflow: hidden;
}
.common_table_item .table_main,
.common_table_item .table_main_overflow_hidden {
  box-sizing: border-box;
  height: 498px;
  min-height: 492px;
  padding: 11px;
  padding-bottom: 30px;
}
.common_table_item .table_main div[class='ant-table-bordered'],
.common_table_item .table_main_overflow_hidden div[class='ant-table-bordered'] {
  border-top: none !important;
  border-color: #ffccaf !important;
}
.common_table_item .table_main .operate_panel,
.common_table_item .table_main_overflow_hidden .operate_panel {
  width: 99%;
  height: 72px;
  border-top: 1px solid #f2f2f2;
  border-right: 1px solid #ffccaf;
  background-color: #fff;
}
.common_table_item .table_main .table_main_tab,
.common_table_item .table_main_overflow_hidden .table_main_tab {
  position: relative;
  bottom: -1px;
}
.common_table_item .table_main .table_main_tab ul li,
.common_table_item .table_main_overflow_hidden .table_main_tab ul li {
  width: 96px;
  height: 35px;
  border: 1px solid #f2f2f2;
  border-bottom: 1px solid transparent !important;
  background-color: #f2f2f2;
  color: #5a5a5a;
  font-size: 14px;
  text-align: center;
  cursor: pointer;
}
.common_table_item .table_main :global .ant-table-small,
.common_table_item .table_main_overflow_hidden :global .ant-table-small {
  border: none !important;
  border-bottom: 1px solid #f2f2f2 !important;
}
.common_table_item .table_main :global .ant-table-thead > tr > th .ant-table-column-sorter .ant-table-column-sorter-inner-full,
.common_table_item .table_main_overflow_hidden :global .ant-table-thead > tr > th .ant-table-column-sorter .ant-table-column-sorter-inner-full {
  margin-top: -0.35em !important;
}
.common_table_item .table_main :global .ant-table-thead > tr > th .ant-table-column-sorter .ant-table-column-sorter-inner,
.common_table_item .table_main_overflow_hidden :global .ant-table-thead > tr > th .ant-table-column-sorter .ant-table-column-sorter-inner {
  margin-left: 0.25em !important;
}
.common_table_item .table_main :global svg,
.common_table_item .table_main_overflow_hidden :global svg {
  width: 0.8em !important;
  height: 0.8em !important;
  line-height: 0.8em !important;
}
.common_table_item .tab_current_1 .operate_panel {
  border-left: 1px solid #ffccaf;
}
.common_table_item .tab_current_1 ul li:nth-child(1) {
  border-color: #ffccaf;
  background-color: #fff;
  color: #ff701e;
}
.common_table_item .tab_current_2 .operate_panel {
  border-left: 1px solid #ffccaf;
}
.common_table_item .tab_current_2 ul li:nth-child(1) {
  border-bottom: 1px solid #ffccaf !important;
}
.common_table_item .tab_current_2 ul li:nth-child(2) {
  border-color: #ffccaf;
  background-color: #fff;
  color: #ff701e;
}
.common_table_item .change_trend_radio {
  padding: 13px 0 13px 24px;
}
.common_table_item .change_trend_radio :global .ant-radio-wrapper {
  font-size: 13px !important;
}
.common_table_item .change_trend_radio_wrap {
  padding: 5px 10px;
}
.common_table_item .change_trend_radio_wrap :global .ant-radio-group-outline {
  flex-wrap: wrap;
}
.common_table_item .change_trend_radio_wrap :global .ant-radio-group-outline .ant-radio-wrapper {
  margin-bottom: 5px;
}
.common_table_item:nth-child(2n) {
  margin-left: 5px;
}
.common_table_item:nth-child(2n + 1) {
  margin-right: 5px;
}
.date_part {
  display: inline-block;
  position: relative;
  margin-right: 10px;
}
.date_part .date_select_modal {
  position: absolute;
  z-index: 9;
  top: 32px;
  left: 0;
  box-sizing: border-box;
  height: 308px;
  padding: 10px 10px 10px 0;
  border-radius: 4px;
  background: #fff;
  box-shadow: 0 2px 8px 0 rgba(200, 201, 204, 0.5);
}
.date_part .date_select_modal .left {
  width: 91px;
  height: 288px;
  padding-top: 50px;
  border-right: 1px solid #dcdee0;
}
.date_part .date_select_modal .left a {
  width: 66px;
  height: 24px;
  margin-top: 20px;
  border-radius: 2px;
  background: #f2f2f2;
  color: #323233;
  font-size: 14px;
  line-height: 14px;
  text-align: center;
}
.date_part .date_select_modal .left a:first-child {
  margin-top: -10px;
}
.date_part .date_select_modal .left a.selected {
  background: #ff7324 !important;
  color: #fff !important;
}
.saling_stat {
  width: 100%;
  background-color: #f0f2f5;
}
.module_item {
  border-radius: 4px;
  background-color: #fff;
}
.trade_stat {
  box-sizing: border-box;
}
.stat_common_table :global .ant-table-small {
  border: none !important;
  border-bottom: 1px solid #f2f2f2 !important;
}
.stat_common_table :global .ant-table-thead > tr > th .ant-table-column-sorter .ant-table-column-sorter-inner-full {
  margin-top: -0.35em !important;
}
.stat_common_table :global .ant-table-thead > tr > th .ant-table-column-sorter .ant-table-column-sorter-inner {
  margin-left: 0.25em !important;
}
.stat_common_table :global .ant-table-column-sorter svg {
  width: 0.8em !important;
  height: 0.8em !important;
  line-height: 0.8em !important;
}
.stat_common_table :global .ant-table-small .ant-table-content .ant-table-body table .ant-table-tbody tr td {
  height: 42px !important;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.stat_common_table :global .ant-table-small > .ant-table-content > .ant-table-header > table,
.stat_common_table :global .ant-table-small > .ant-table-content > .ant-table-body > table,
.stat_common_table :global .ant-table-small > .ant-table-content > .ant-table-scroll > .ant-table-header > table,
.stat_common_table :global .ant-table-small > .ant-table-content > .ant-table-scroll > .ant-table-body > table,
.stat_common_table :global .ant-table-small > .ant-table-content > .ant-table-fixed-left > .ant-table-header > table,
.stat_common_table :global .ant-table-small > .ant-table-content > .ant-table-fixed-right > .ant-table-header > table,
.stat_common_table :global .ant-table-small > .ant-table-content > .ant-table-fixed-left > .ant-table-body-outer > .ant-table-body-inner > table,
.stat_common_table :global .ant-table-small > .ant-table-content > .ant-table-fixed-right > .ant-table-body-outer > .ant-table-body-inner > table {
  table-layout: fixed;
}
.stat_common_table .defaultText {
  color: #696969;
  font-size: 12px;
  word-break: break-all;
}
.main_area {
  display: block;
  height: 498px;
}
.visualized_item {
  width: calc(50% - 5px);
  height: 559px;
  margin-top: 10px;
  border-radius: 4px;
  background-color: #fff;
}
.visualized_item .top_info_operate {
  border-bottom: 1px solid #eee;
}
.visualized_item .top_info_operate .time_option_operate {
  margin-right: 11px;
}
.module_item {
  border-radius: 4px;
  background-color: #fff;
}
.today_info_panel {
  margin-top: 10px;
}
.today_info_panel .today_info_item {
  position: relative;
  width: 25%;
  height: 301px;
  margin-right: 10px;
  background-color: #fff;
}
@media (max-width: 1400px) {
  .today_info_panel .today_info_item .position_title {
    width: 132px !important;
    height: 31px !important;
    font-size: 14px !important;
  }
}
@media (max-width: 1400px) {
  .today_info_panel .today_info_item .title {
    margin-top: 20px !important;
    margin-right: 8px !important;
  }
}
.today_info_panel .today_info_item:last-child {
  margin-right: 0;
}
.today_info_panel .today_info_item .position_title {
  position: absolute;
  z-index: 2;
  top: 8px;
  left: -5px;
  width: 163px;
  height: 37px;
  padding-top: 11px;
  padding-left: 16px;
  background-image: url('@/assets/images/home_basic/today_info_bg.png');
  background-size: 100%;
  color: #fff;
  font-size: 15px;
}
.today_info_panel .today_info_item .info_children ul {
  margin-top: 45px;
  margin-left: 13%;
  overflow: hidden;
}
.today_info_panel .today_info_item .info_children ul li {
  width: 50%;
  height: 103px;
  float: left;
}
.today_info_panel .today_info_item .info_children ul li .key {
  color: #333;
  font-size: 14px;
}
.today_info_panel .today_info_item .info_children ul li .key span {
  margin-right: 10px;
}
.today_info_panel .today_info_item .info_children ul li .value {
  margin-top: 7px;
  color: #333;
  font-family: PingFangSC-Medium, 'PingFang SC';
  font-size: 26px;
  letter-spacing: -2px;
  line-height: 26px;
}
.today_info_panel .today_info_item .title {
  margin-top: 26px;
  margin-right: 22px;
}
.today_info_panel .today_info_item .title .update_button {
  cursor: pointer;
}
.today_info_panel .today_info_item .update_time {
  color: #ff701e;
  font-size: 13px;
}
.today_info_panel .today_info_item .update_button {
  position: relative;
  width: 16px;
  height: 16px;
}
.today_info_panel .today_info_item .update_button img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}
.stat_amount_new {
  width: 100%;
}
.stat_amount_new .left_pending_icon {
  width: 71px;
  height: 71px;
  margin-top: 28px;
  margin-left: 11%;
}
.stat_amount_new .item {
  width: 20%;
  height: 130px;
  margin-top: 20px;
  margin-right: 16px;
  margin-bottom: 19px;
  background-color: #fff;
  box-shadow: 0 2px 22px 0 rgba(0, 0, 0, 0.03);
  cursor: pointer;
}
.stat_amount_new .item .item_desc {
  margin-top: 34px;
  margin-left: 21px;
}
.stat_amount_new .item .item_desc .item_title {
  opacity: 0.72;
  color: #333;
  font-size: 15px;
}
.stat_amount_new .item .item_desc .item_num {
  margin-top: 9px;
  color: #333;
  font-size: 26px;
  font-weight: 700;
  line-height: 26px;
}
.stat_amount_new .item:last-child {
  margin-right: 24px;
}
.stat_amount_new .item:first-child {
  margin-left: 24px;
}
.date_select_modal_flag {
  background: #fff;
}
