.custom_svg_iconfont {
  position: relative;
  top: 3px;
  left: 1px;
}

.stat_part {
  .title_add_goods {
    height: 48px !important;
  }
}

.g2_tooltip_custom {
  box-sizing: border-box;
  width: auto;
  padding: 12px;
  border-radius: 4px;
  background: rgb(0 0 0 / 80%);
  box-shadow: 0 2px 8px 0 rgb(0 0 0 / 15%);
  color: #fff;
  font-size: 14px;

  .g2-tooltip-title {
    margin-bottom: 6px;
  }

  i {
    display: inline-block;
    position: relative;
    top: -3px;
    width: 6px;
    height: 6px;
    margin-right: 8px;
    border-radius: 6px;
  }
}

.map_charts {
  position: relative;
  top: 50%;
  left: 50%;
  transform: translateY(-50%) translateX(-50%);

  canvas {
    margin: 0 auto;
  }
}

.table_td_center_elliplis {
  span {
    overflow: hidden;
    color: #ff701e;
    font-size: 13px;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.update_time_panel {
  margin-left: 10px;

  span {
    margin-right: 10px;
    color: #414141;
    font-size: 13px;
  }

  .reload_icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-top: 2px;
    background-image: url('../../assets/images/home_basic/reload_icon.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    cursor: pointer;

    &:hover {
      background-image: url('../../assets/images/home_basic/reload_icon_active.png');
    }
  }
}

.num_stat_item {
  box-sizing: border-box;
  // height: 100%;
  align-items: stretch;
  width: 100%;
  padding: 20px 15px;
  border-top: 1px solid rgb(216 216 216 / 50%);

  .left_slide {
    flex: 0 0 100px;
    background-color: #fff7f4;

    .slide_icon {
      display: block;
      width: 40px;
      height: 40px;
      margin-bottom: 5px;
    }

    .slide_title {
      color: #333;
      font-size: 14px;
    }
  }

  .right_main {
    height: 100%;
    overflow: auto;

    @media (max-width: 1700px) {
      ul {
        li {
          padding-left: 15px !important;
        }
      }
    }

    ul {
      li {
        box-sizing: border-box;
        width: 250px;
        height: 90px;
        margin-left: 15px;
        padding-left: 30px;
        border-radius: 2px;
        background-color: rgb(238 238 238 / 20%);

        .up_desc {
          span {
            margin-right: 6px;
            color: #333;
            font-size: 14px;
          }

          img {
            margin-top: -3px;
            cursor: pointer;
          }
        }

        .down_num {
          margin-top: 5px;

          span {
            color: #333;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'PingFang SC',
              'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            font-size: 28px;
            font-weight: 600;
          }
        }
      }
    }
  }
}

.colorful_store_item_5 {
  background-color: #fff;

  ul {
    li {
      width: 19% !important;
    }
  }
}

.saling_stat {
  .colorful_store_item {
    .number {
      &::after {
        width: 104% !important;
      }
    }
  }
}

.colorful_store_item {
  box-sizing: border-box;
  width: 100%;
  padding: 25px;
  border-top: 1px solid rgb(216 216 216 / 50%);
  background: #fff;

  ul {
    li {
      box-sizing: border-box;
      width: 24%;
      height: 163px;
      padding: 12px 30px;
      padding-left: 2.4%;
      background-repeat: no-repeat;
      background-position: center top;
      background-size: auto 100%;

      @media (max-width: 1400px) {
        .down_desc {
          padding-left: 8px !important;
        }
      }

      .right_operate {
        position: relative;

        .select_modal {
          position: absolute;
          top: 24px;
          left: -2px;
          width: 80px;
          height: 92px;
          border-radius: 2px;
          background: #fff;
          box-shadow: 0 1px 8px 0 rgb(203 136 0 / 34%);

          .select_option {
            position: relative;
            width: 100%;
            padding: 4px 0;
            padding-left: 8px;
            color: #414141;
            font-size: 14px;
            cursor: pointer;

            &:hover,
            &.current {
              color: #ff590d;
            }

            svg {
              position: absolute;
              top: 0;
              right: 11px;
              bottom: 0;
              margin: auto;
              transform: scale(0.85);
              transform-origin: 100% 50%;
            }
          }

          .select_option.current {
            color: #ff590d;
          }
        }

        .select_modal.hide {
          display: none;
        }

        span {
          color: #fff;
          font-size: 14px;
          cursor: pointer;
        }

        i {
          display: block;
          position: relative;
          bottom: -4px;
          width: 0;
          height: 0;
          margin-left: 5px;
          transform-origin: 50% 25%;
          transition: all 0.36s;
          border: 5px solid transparent;
          border-top: 6px solid #fff;
          font-style: normal;
          cursor: pointer;
        }

        i.open {
          transform: rotate(180deg);
        }
      }

      .up_title {
        color: #fff;
        font-size: 16px;
      }

      .number {
        position: relative;
        width: 66%;
        min-height: 58px;
        margin-top: 24px;
        padding-bottom: 15px;
        color: #fff;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'PingFang SC',
          'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        font-size: 34px;
        font-weight: 700;

        &::after {
          content: '';
          display: block;
          position: absolute;
          bottom: 0;
          left: -8px;
          width: 78%;
          height: 1px;
          background-color: #fff;
        }
      }

      .down_desc {
        box-sizing: border-box;
        margin-top: 10px;
        padding-left: 15px;
        white-space: nowrap;

        i {
          position: relative;
          top: 3px;
          left: 2px;
        }

        .intro {
          color: #fff;
          font-size: 14px;
        }

        .difference {
          color: #fff;
          font-size: 14px;
        }
      }
    }
  }
}

.preview_stat_panel {
  position: relative;
  box-sizing: border-box;
  padding: 0 20px;
  padding-bottom: 50px;
  // 运营统计页的倒三角 end
  @media (max-width: 1500px) {
    .charts_panel {
      img {
        width: 500px;
        height: 454px;
      }

      .common_position_stat {
        width: 105px !important;
        font-size: 12px !important;
      }

      .first_rate {
        top: 120px;
        right: 46px !important;
      }

      .second_rate {
        top: 206px;
        right: 0 !important;
      }

      .third_rate {
        top: 277px;
        right: 72px !important;
      }
    }
  }

  .charts_panel {
    position: absolute;
    z-index: 4;
    top: 0;
    right: 52px;

    .charts_stat_info {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;

      .common_position_stat {
        position: absolute;
        width: 120px;
        height: 52px;
        border-radius: 2px;
        background: #98c94a;
        color: #fff;
        font-size: 14px;

        span {
          margin-top: 5px;
        }
      }

      .first_rate {
        top: 120px;
        right: 113px;
      }

      .second_rate {
        top: 206px;
        right: 1px;
      }

      .third_rate {
        top: 277px;
        right: 114px;
      }

      ul {
        width: 76%;

        li {
          height: 158px;
          color: #fff;
          font-size: 18px;
        }

        img {
          display: block;
          width: 26px;
          height: 26px;
          margin-right: 5px;
        }
      }
    }

    > img {
      width: 789px;
      height: 454px;
    }
  }

  .funnel {
    .part {
      position: relative;
      width: 360px;
      height: 360px;

      .funnel_center_img {
        width: 20px;
        height: 20px;
      }

      .funnel_center_img_desc {
        margin-left: 3px;
        color: #fff;
        font-size: 16px;
        font-weight: 600;
      }

      .center_item_top {
        position: relative;
        z-index: 1;
        width: 240px;
        height: 0;
        border-top: 119px solid #8bd6b1;
        border-right: 33px solid transparent;
        border-left: 33px solid transparent;
        background: #fdfdfd;

        .top_content {
          position: absolute;
          z-index: 2;
          top: -119px;
          left: -34px;
          width: 240px;
          height: 119px;
        }
      }

      .center_item_center {
        position: relative;
        z-index: 1;
        width: 174px;
        height: 0;
        border-top: 119px solid #82d3d0;
        border-right: 33px solid transparent;
        border-left: 33px solid transparent;
        background: #f9f9f9;

        .center_content {
          position: absolute;
          z-index: 2;
          top: -119px;
          left: -33px;
          width: 172px;
          height: 119px;
        }
      }

      .center_item_bottom {
        position: relative;
        z-index: 1;
        width: 108px;
        height: 0;
        border-top: 119px solid #71c4df;
        border-right: 33px solid transparent;
        border-left: 33px solid transparent;
        background: #f5f5f5;

        .bottom_content {
          position: absolute;
          z-index: 2;
          top: -119px;
          left: -31px;
          width: 100px;
          height: 119px;
        }
      }

      .left_top_content {
        position: absolute;
        z-index: 1;
        top: 68px;
        left: -45px;
        width: 117px;
        padding: 5px 0;
        background: #8bd6b1;
        text-align: center;
      }

      .left_bottom_content {
        position: absolute;
        z-index: 1;
        bottom: 100px;
        left: -45px;
        width: 117px;
        padding: 5px 0;
        background: #8bd6b1;
        text-align: center;
      }

      .right_content {
        position: absolute;
        z-index: 1;
        right: -23px;
        bottom: 180px;
        width: 117px;
        padding: 5px 0;
        background: #8bd6b1;
        text-align: center;
      }

      .side_content_desc {
        color: #fff;
        font-size: 13px;
        font-weight: 600;
      }
    }

    .left_top_line {
      position: absolute;
      top: 30px;
      left: 19px;
      width: 74px;
      height: 118px;
      border: 2px solid #b0e1c9;
      border-right: 0;
    }

    .left_bottom_line::after,
    .left_top_line::after {
      content: '';
      position: absolute;
      z-index: 2;
      right: 0;
      bottom: 1px;
      width: 14px;
      height: 2px;
      transform: rotate(30deg);
      background: #b0e1c9;
    }

    .left_bottom_line {
      position: absolute;
      bottom: 50px;
      left: 19px;
      width: 109px;
      height: 150px;
      border: 2px solid #b0e1c9;
      border-right: 0;
    }

    .right_line {
      position: absolute;
      top: 30px;
      right: 40px;
      width: 88px;
      height: 280px;
      border: 2px solid #b0e1c9;
      border-left: 0;
    }

    .right_line::after {
      content: '';
      position: absolute;
      z-index: 2;
      bottom: 1px;
      left: 0;
      width: 14px;
      height: 2px;
      transform: rotate(-30deg);
      background: #b0e1c9;
    }
  }

  // 运营统计页的倒三角
  .preview_stat_panel_back {
    overflow: hidden;
    border-radius: 10px;
    box-shadow: 0 1px 12px 0 rgb(0 0 0 / 6%);
  }

  .charts_panel_operate {
    position: absolute;
    z-index: 4;
    top: 0;
    right: 131px;

    .charts_stat_info {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;

      .common_position_stat {
        position: absolute;
        width: 120px;
        height: 52px;
        border-radius: 2px;
        background: #98c94a;
        color: #fff;
        font-size: 14px;

        span {
          margin-top: 5px;
        }
      }

      .first_rate {
        top: 120px;
        right: 113px;
      }

      .second_rate {
        top: 206px;
        right: 1px;
      }

      .third_rate {
        top: 277px;
        right: 114px;
      }

      ul {
        width: 76%;

        li {
          height: 158px;
          color: #fff;
          font-size: 18px;
        }

        img {
          display: block;
          width: 26px;
          height: 26px;
          margin-right: 5px;
        }
      }
    }

    > img {
      width: 789px;
      height: 454px;
    }
  }

  .funnel_operate {
    .part {
      position: relative;
      width: 520px;
      height: 597px;

      .funnel_center_img {
        width: 20px;
        height: 20px;
      }

      .funnel_center_img_desc {
        margin-left: 3px;
        color: #fff;
        font-size: 16px;
        font-weight: 600;
      }

      .center_item_head {
        position: relative;
        z-index: 1;
        width: 495px;
        height: 0;
        border-top: 119px solid #add9c3;
        border-right: 46px solid transparent;
        border-left: 46px solid transparent;

        .top_content {
          position: absolute;
          z-index: 2;
          top: -119px;
          left: -47px;
          width: 495px;
          height: 119px;
        }
      }

      .center_item_top {
        position: relative;
        z-index: 2;
        width: 403px;
        height: 0;
        border-top: 119px solid #8bd6b1;
        border-right: 46px solid transparent;
        border-left: 46px solid transparent;
        background: #f5f5f5;

        .top_content {
          position: absolute;
          z-index: 2;
          top: -119px;
          left: -47px;
          width: 403px;
          height: 119px;
        }
      }

      .center_item_center {
        position: relative;
        z-index: 2;
        width: 311px;
        height: 0;
        border-top: 119px solid #82d3d0;
        border-right: 46px solid transparent;
        border-left: 46px solid transparent;
        background: #f5f5f5;

        .center_content {
          position: absolute;
          z-index: 2;
          top: -119px;
          left: -47px;
          width: 311px;
          height: 119px;
        }
      }

      .center_item_bottom {
        position: relative;
        z-index: 2;
        width: 219px;
        height: 0;
        border-top: 119px solid #71c4df;
        border-right: 46px solid transparent;
        border-left: 46px solid transparent;
        background: #f5f5f5;

        .bottom_content {
          position: absolute;
          z-index: 2;
          top: -119px;
          left: -47px;
          width: 219px;
          height: 119px;
        }
      }

      .center_item_footer {
        position: relative;
        z-index: 2;
        width: 127px;
        height: 0;
        border-top: 119px solid #7ab6c9;
        border-right: 46px solid transparent;
        border-left: 46px solid transparent;
        background: #f5f5f5;

        .bottom_content {
          position: absolute;
          z-index: 2;
          top: -119px;
          left: -47px;
          width: 127px;
          height: 119px;
        }
      }

      .left_top_content {
        position: absolute;
        z-index: 1;
        top: 68px;
        left: -104px;
        width: 117px;
        padding: 5px 0;
        background: #8bd6b1;
        text-align: center;
      }

      .left_top_content_one {
        position: absolute;
        z-index: 1;
        top: 193px;
        left: -104px;
        width: 117px;
        padding: 5px 0;
        background: #8bd6b1;
        text-align: center;
      }

      .left_top_content_two {
        position: absolute;
        z-index: 1;
        top: 320px;
        left: -104px;
        width: 117px;
        padding: 5px 0;
        background: #8bd6b1;
        text-align: center;
      }

      .left_bottom_content {
        position: absolute;
        z-index: 1;
        bottom: 111px;
        left: -104px;
        width: 117px;
        padding: 5px 0;
        background: #8bd6b1;
        text-align: center;
      }

      .right_content {
        position: absolute;
        z-index: 1;
        right: -79px;
        bottom: 281px;
        width: 117px;
        padding: 5px 0;
        background: #8bd6b1;
        text-align: center;
      }

      .side_content_desc {
        color: #fff;
        font-size: 13px;
        font-weight: 600;
      }
    }

    .left_top_line {
      position: absolute;
      top: 30px;
      left: -45px;
      width: 105px;
      height: 114px;
      border: 2px solid #b0e1c9;
      border-right: 0;
    }

    .left_top_line_one {
      position: absolute;
      z-index: 1;
      top: 157px;
      left: -45px;
      width: 146px;
      height: 114px;
      border: 2px solid #b0e1c9;
      border-right: 0;
    }

    .left_top_line_two {
      position: absolute;
      top: 283px;
      left: -45px;
      width: 198px;
      height: 114px;
      border: 2px solid #b0e1c9;
      border-right: 0;
    }

    .left_bottom_line::after,
    .left_top_line::after,
    .left_top_line_one::after,
    .left_top_line_two::after {
      content: '';
      position: absolute;
      z-index: 2;
      right: 0;
      bottom: 1px;
      width: 14px;
      height: 2px;
      transform: rotate(30deg);
      background: #b0e1c9;
    }

    .left_bottom_line {
      position: absolute;
      bottom: 75px;
      left: -45px;
      width: 242px;
      height: 114px;
      border: 2px solid #b0e1c9;
      border-right: 0;
    }

    .right_line {
      position: absolute;
      top: 35px;
      right: -23px;
      width: 220px;
      height: 503px;
      border: 2px solid #b0e1c9;
      border-left: 0;
    }

    .right_line::after {
      content: '';
      position: absolute;
      z-index: 2;
      bottom: 1px;
      left: 0;
      width: 14px;
      height: 2px;
      transform: rotate(-30deg);
      background: #b0e1c9;
    }
  }

  .stat_item {
    box-sizing: border-box;
    margin-top: -50px;
    padding: 10px 22px;
    padding-bottom: 68px;

    ul {
      li {
        width: 18%;

        .up_label {
          span {
            margin-right: 5px;
            color: #333;
            font-size: 14px;
          }

          img {
            display: block;
            width: 16px;
            height: 16px;
            cursor: pointer;
          }
        }

        .num {
          min-height: 39px;
          margin: 5px 0;
          color: #333;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'PingFang SC',
            'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
          font-size: 28px;
          font-weight: 500;
        }

        .down_difference {
          color: rgb(51 51 51 / 40%);

          .label {
            margin-right: 8px;
            color: rgb(51 51 51 / 40%);
            font-size: 14px;
          }

          .difference_num {
            color: #c41a1a;
            font-size: 14px;
          }
        }
      }
    }
  }

  .stat_item:nth-child(2) {
    margin-top: 0 !important;
    border-radius: 0 0 20px 20px;
    background: #fdfdfd;
    box-shadow: 0 1px 12px 0 rgb(0 0 0 / 6%);
  }

  .stat_item:nth-child(3) {
    position: relative;
    z-index: 2;
    border-radius: 0 0 20px 20px;
    background: #f9f9f9;
    box-shadow: 0 3px 6px 0 #f5f5f5;
  }

  .stat_item:nth-child(4) {
    position: relative;
    z-index: 3;
    padding-bottom: 18px;
    border-radius: 20px 20px 0 0;
    background: #f5f5f5;
    box-shadow: 0 2px 4px 0 rgb(187 187 187 / 18%);
  }

  // 运营统计页
  .stat_item_operate {
    display: flex;
    box-sizing: border-box;
    align-items: center;
    height: 119px;
    padding-left: 20px;
    border-bottom: 1px solid #fff;
    background: #f5f5f5;

    ul {
      li {
        width: 18%;

        .up_label {
          span {
            margin-right: 5px;
            color: #333;
            font-size: 14px;
          }

          img {
            display: block;
            width: 16px;
            height: 16px;
            cursor: pointer;
          }
        }

        .num {
          min-height: 39px;
          margin: 5px 0;
          color: #333;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'PingFang SC',
            'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
          font-size: 28px;
          font-weight: 500;
        }

        .down_difference {
          color: rgb(51 51 51 / 40%);

          .label {
            margin-right: 8px;
            color: rgb(51 51 51 / 40%);
            font-size: 14px;
          }

          .difference_num {
            color: #c41a1a;
            font-size: 14px;
          }
        }
      }
    }
  }

  .stat_item_operate:last-child {
    border-bottom: none;
  }
}

.common_table_item {
  width: calc(50% - 5px);
  background-color: #fff;

  .store_list_operate_panel {
    position: relative;
    box-sizing: border-box;
    width: calc(100% - 11px);
    padding: 0 11px;
    padding-top: 20px;

    > form {
      position: relative;
    }
  }

  .label_panel {
    position: relative;
    border-bottom: 1px solid #eee;
  }

  .table_main_overflow_hidden {
    overflow: hidden;
  }

  .table_main,
  .table_main_overflow_hidden {
    box-sizing: border-box;
    height: 498px;
    min-height: 492px;
    padding: 11px;
    padding-bottom: 30px;

    div[class='ant-table-bordered'] {
      border-top: none !important;
      border-color: #ffccaf !important;
    }

    .operate_panel {
      width: 99%;
      height: 72px;
      border-top: 1px solid #f2f2f2;
      border-right: 1px solid #ffccaf;
      background-color: #fff;
    }

    .table_main_tab {
      position: relative;
      bottom: -1px;

      ul {
        li {
          width: 96px;
          height: 35px;
          border: 1px solid #f2f2f2;
          border-bottom: 1px solid transparent !important;
          background-color: #f2f2f2;
          color: #5a5a5a;
          font-size: 14px;
          text-align: center;
          cursor: pointer;
        }
      }
    }

    :global {
      .ant-table-small {
        border: none !important;
        border-bottom: 1px solid #f2f2f2 !important;
      }

      .ant-table-thead > tr > th .ant-table-column-sorter .ant-table-column-sorter-inner-full {
        margin-top: -0.35em !important;
      }

      .ant-table-thead > tr > th .ant-table-column-sorter .ant-table-column-sorter-inner {
        margin-left: 0.25em !important;
      }

      svg {
        width: 0.8em !important;
        height: 0.8em !important;
        line-height: 0.8em !important;
      }
    }
  }

  .tab_current_1 {
    .operate_panel {
      border-left: 1px solid #ffccaf;
    }

    ul {
      li:nth-child(1) {
        border-color: #ffccaf;
        background-color: #fff;
        color: #ff701e;
      }
    }
  }

  .tab_current_2 {
    .operate_panel {
      border-left: 1px solid #ffccaf;
    }

    ul {
      li:nth-child(1) {
        border-bottom: 1px solid #ffccaf !important;
      }

      li:nth-child(2) {
        border-color: #ffccaf;
        background-color: #fff;
        color: #ff701e;
      }
    }
  }

  .change_trend_radio {
    padding: 13px 0 13px 24px;

    :global {
      .ant-radio-wrapper {
        font-size: 13px !important;
      }
    }
  }

  .change_trend_radio_wrap {
    padding: 5px 10px;

    :global {
      .ant-radio-group-outline {
        flex-wrap: wrap;

        .ant-radio-wrapper {
          margin-bottom: 5px;
        }
      }
    }
  }
}

.common_table_item:nth-child(2n) {
  margin-left: 5px;
}

.common_table_item:nth-child(2n + 1) {
  margin-right: 5px;
}

.date_part {
  display: inline-block;
  position: relative;
  margin-right: 10px;

  .date_select_modal {
    position: absolute;
    z-index: 9;
    top: 32px;
    left: 0;
    box-sizing: border-box;
    height: 308px;
    padding: 10px 10px 10px 0;
    border-radius: 4px;
    background: #fff;
    box-shadow: 0 2px 8px 0 rgb(200 201 204 / 50%);

    .left {
      width: 91px;
      height: 288px;
      padding-top: 50px;
      border-right: 1px solid #dcdee0;

      a {
        width: 66px;
        height: 24px;
        margin-top: 20px;
        border-radius: 2px;
        background: #f2f2f2;
        color: #323233;
        font-size: 14px;
        line-height: 14px;
        text-align: center;
      }

      a:first-child {
        margin-top: -10px;
      }

      a.selected {
        background: #ff7324 !important;
        color: #fff !important;
      }
    }
  }
}

.saling_stat {
  width: 100%;
  background-color: #f0f2f5;
}

.module_item {
  border-radius: 4px;
  background-color: #fff;
}

.trade_stat {
  box-sizing: border-box;
}

.stat_common_table {
  :global {
    .ant-table-small {
      border: none !important;
      border-bottom: 1px solid #f2f2f2 !important;
    }

    .ant-table-thead > tr > th .ant-table-column-sorter .ant-table-column-sorter-inner-full {
      margin-top: -0.35em !important;
    }

    .ant-table-thead > tr > th .ant-table-column-sorter .ant-table-column-sorter-inner {
      margin-left: 0.25em !important;
    }

    .ant-table-column-sorter svg {
      width: 0.8em !important;
      height: 0.8em !important;
      line-height: 0.8em !important;
    }

    .ant-table-small .ant-table-content .ant-table-body table .ant-table-tbody tr td {
      height: 42px !important;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .ant-table-small > .ant-table-content > .ant-table-header > table,
    .ant-table-small > .ant-table-content > .ant-table-body > table,
    .ant-table-small > .ant-table-content > .ant-table-scroll > .ant-table-header > table,
    .ant-table-small > .ant-table-content > .ant-table-scroll > .ant-table-body > table,
    .ant-table-small > .ant-table-content > .ant-table-fixed-left > .ant-table-header > table,
    .ant-table-small > .ant-table-content > .ant-table-fixed-right > .ant-table-header > table,
    .ant-table-small
      > .ant-table-content
      > .ant-table-fixed-left
      > .ant-table-body-outer
      > .ant-table-body-inner
      > table,
    .ant-table-small
      > .ant-table-content
      > .ant-table-fixed-right
      > .ant-table-body-outer
      > .ant-table-body-inner
      > table {
      table-layout: fixed;
    }
  }

  .defaultText {
    color: #696969;
    font-size: 12px;
    word-break: break-all;
  }
}

.main_area {
  display: block;
  height: 498px;
}

.visualized_item {
  width: calc(50% - 5px);
  height: 559px;
  margin-top: 10px;
  border-radius: 4px;
  background-color: #fff;

  .top_info_operate {
    border-bottom: 1px solid #eee;

    .time_option_operate {
      margin-right: 11px;
    }
  }
}

.module_item {
  border-radius: 4px;
  background-color: #fff;
}

.today_info_panel {
  margin-top: 10px;

  .today_info_item {
    position: relative;
    width: 25%;
    height: 301px;
    margin-right: 10px;
    background-color: #fff;

    @media (max-width: 1400px) {
      .position_title {
        width: 132px !important;
        height: 31px !important;
        font-size: 14px !important;
      }
    }

    @media (max-width: 1400px) {
      .title {
        margin-top: 20px !important;
        margin-right: 8px !important;
      }
    }

    &:last-child {
      margin-right: 0;
    }

    .position_title {
      position: absolute;
      z-index: 2;
      top: 8px;
      left: -5px;
      width: 163px;
      height: 37px;
      padding-top: 11px;
      padding-left: 16px;
      background-image: url('@/assets/images/home_basic/today_info_bg.png');
      background-size: 100%;
      color: #fff;
      font-size: 15px;
    }

    .info_children {
      ul {
        margin-top: 45px;
        margin-left: 13%;
        overflow: hidden;

        li {
          width: 50%;
          height: 103px;
          float: left;

          .key {
            color: #333;
            font-size: 14px;

            span {
              margin-right: 10px;
            }
          }

          .value {
            margin-top: 7px;
            color: #333;
            font-family: PingFangSC-Medium, 'PingFang SC';
            font-size: 26px;
            letter-spacing: -2px;
            line-height: 26px;
          }
        }
      }
    }

    .title {
      margin-top: 26px;
      margin-right: 22px;

      .update_button {
        cursor: pointer;
      }
    }

    .update_time {
      color: #ff701e;
      font-size: 13px;
    }

    .update_button {
      position: relative;
      width: 16px;
      height: 16px;

      img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
      }
    }
  }
}

.stat_amount_new {
  width: 100%;

  .left_pending_icon {
    width: 71px;
    height: 71px;
    margin-top: 28px;
    margin-left: 11%;
  }

  .item {
    width: 20%;
    height: 130px;
    margin-top: 20px;
    margin-right: 16px;
    margin-bottom: 19px;
    background-color: #fff;
    box-shadow: 0 2px 22px 0 rgb(0 0 0 / 3%);
    cursor: pointer;

    .item_desc {
      margin-top: 34px;
      margin-left: 21px;

      .item_title {
        opacity: 0.72;
        color: #333;
        font-size: 15px;
      }

      .item_num {
        margin-top: 9px;
        color: #333;
        font-size: 26px;
        font-weight: 700;
        line-height: 26px;
      }
    }
  }

  .item:last-child {
    margin-right: 24px;
  }

  .item:first-child {
    margin-left: 24px;
  }
}

.date_select_modal_flag {
  background: #fff;
}
