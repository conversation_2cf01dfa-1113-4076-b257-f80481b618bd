import type { AppRouteRecordRaw, Menu } from '/@/router/types';

import { defineStore } from 'pinia';
import { store } from '/@/store';
import { useI18n } from '/@/hooks/web/useI18n';
import { useUserStore } from './user';
import { useAppStoreWithOut } from './app';
import { toRaw } from 'vue';
import { transformObjToRoute, flatMultiLevelRoutes } from '/@/router/helper/routeHelper';
import { transformRouteToMenu } from '/@/router/helper/menuHelper';

import projectSetting from '/@/settings/projectSetting';

import { PermissionModeEnum } from '/@/enums/appEnum';

import { asyncRoutes } from '/@/router/routes';
import { ERROR_LOG_ROUTE, PAGE_NOT_FOUND_ROUTE } from '/@/router/routes/basic';

import { filter } from '/@/utils/helper/treeHelper';

import { getMenuList } from '/@/api/sys/menu';

import { useMessage } from '/@/hooks/web/useMessage';
import { PageEnum } from '/@/enums/pageEnum';

interface PermissionState {
  // Permission code list
  // 权限代码列表
  permCodeList: string[] | number[];
  // Whether the route has been dynamically added
  // 路由是否动态添加
  isDynamicAddedRoute: boolean;
  // To trigger a menu update
  // 触发菜单更新
  lastBuildMenuTime: number;
  // Backstage menu list
  // 后台菜单列表
  backMenuList: Menu[];
  // 菜单列表
  frontMenuList: Menu[];
}

export const usePermissionStore = defineStore({
  id: 'app-permission',
  state: (): PermissionState => ({
    // 权限代码列表
    permCodeList: [],
    // Whether the route has been dynamically added
    // 路由是否动态添加
    isDynamicAddedRoute: false,
    // To trigger a menu update
    // 触发菜单更新
    lastBuildMenuTime: 0,
    // Backstage menu list
    // 后台菜单列表
    backMenuList: [],
    // menu List
    // 菜单列表
    frontMenuList: [],
  }),
  getters: {
    getPermCodeList(state): string[] | number[] {
      return state.permCodeList;
    },
    getBackMenuList(state): Menu[] {
      return state.backMenuList;
    },
    getFrontMenuList(state): Menu[] {
      return state.frontMenuList;
    },
    getLastBuildMenuTime(state): number {
      return state.lastBuildMenuTime;
    },
    getIsDynamicAddedRoute(state): boolean {
      return state.isDynamicAddedRoute;
    },
  },
  actions: {
    setPermCodeList(codeList: string[]) {
      this.permCodeList = codeList;
    },

    setBackMenuList(list: Menu[]) {
      this.backMenuList = list;
      list?.length > 0 && this.setLastBuildMenuTime();
    },

    setFrontMenuList(list: Menu[]) {
      this.frontMenuList = list;
    },

    setLastBuildMenuTime() {
      this.lastBuildMenuTime = new Date().getTime();
    },

    setDynamicAddedRoute(added: boolean) {
      this.isDynamicAddedRoute = added;
    },
    resetState(): void {
      this.isDynamicAddedRoute = false;
      this.permCodeList = [];
      this.backMenuList = [];
      this.lastBuildMenuTime = 0;
    },

    // 构建路由
    async buildRoutesAction(): Promise<AppRouteRecordRaw[]> {
      const { t } = useI18n();
      const userStore = useUserStore();

      let routes: AppRouteRecordRaw[] = [];
      let routes_info: AppRouteRecordRaw[] = [];
      const roleList = toRaw(userStore.getRoleList) || [];
      const routeRemoveIgnoreFilter = (route: AppRouteRecordRaw) => {
        const { meta } = route;
        // ignoreRoute 为true 则路由仅用于菜单生成，不会在实际的路由表中出现
        const { ignoreRoute } = meta || {};
        // arr.filter 返回 true 表示该元素通过测试
        return !ignoreRoute;
      };

      // 路由过滤器 在 函数filter 作为回调传入遍历使用
      const routeFilter = (route: AppRouteRecordRaw) => {
        const { meta } = route;
        // 抽出角色
        const { roles } = meta || {};
        if (!roles) return true;
        // 进行角色权限判断
        return roleList.some((role) => roles.includes(role));
      };

      const { createMessage } = useMessage();
      if (!localStorage.getItem('menuLoading')) {
        createMessage.loading({
          content: t('sys.app.menuLoading'),
          duration: 1,
        });
        localStorage.setItem('menuLoading', '1');
        setTimeout(() => {
          localStorage.removeItem('menuLoading');
        }, 1000);
      }

      // 对非一级路由进行过滤
      routes_info = filter(asyncRoutes, routeFilter);
      // 对一级路由再次根据角色权限过滤
      routes_info = routes_info.filter(routeFilter);
      // 将路由转换成菜单
      const menuLists = transformRouteToMenu(routes_info, true);
      // 移除掉 ignoreRoute: true 的路由 非一级路由
      routes_info = filter(routes_info, routeRemoveIgnoreFilter);
      // 移除掉 ignoreRoute: true 的路由 一级路由；
      routes_info = routes_info.filter(routeRemoveIgnoreFilter);
      // 对菜单进行排序
      menuLists.sort((a, b) => {
        return (a.meta?.orderNo || 0) - (b.meta?.orderNo || 0);
      });

      // 将多级路由转换为 2 级路由
      routes_info = flatMultiLevelRoutes(routes_info);
      // 空数组
      let routeList: AppRouteRecordRaw[] = [];
      try {
        // 获取接口返回的路由
        const meun_list = localStorage.getItem('sld_menu_data') ? JSON.parse(localStorage.getItem('sld_menu_data')) : [];
        meun_list.map((item) => {
          // 获取frontPath最后的名字
          let path_obj_item = item.frontPath.split('/');
          item.name = path_obj_item[path_obj_item.length - 1];
          // 如果通过/分割出来的获取后半部分赋值  值是下面要匹配的名
          if (path_obj_item.length > 2) {
            item.frontName = path_obj_item[1];
          } else {
            item.frontName = '';
          }
          if (item.children) {
            const children_list = JSON.parse(JSON.stringify(item.children));
            children_list.map((it) => {
              let path_obj = it.frontPath.split('/');
              it.name = path_obj[path_obj.length - 1];
              // 如果通过/分割出来去数组length-1的值 值是下面匹配需要的值
              if (path_obj.length > 2) {
                it.frontName = path_obj[1];
              } else {
                it.frontName = '';
              }
            });
            // 赋值
            item.children = children_list;
          }
        });
        let route_list: any = [];
        route_list = routes_info;
        // 新的进行筛选后的项目路由
        routeList = [];
        // 项目里的路由进行for循环
        for (let i = 0; i < route_list.length; i++) {
          // 如果为path为/:path(.*)*直接赋值
          if (route_list[i].path == '/:path(.*)*') {
            routeList.push(route_list[i]);
          } else {
            // 通过过滤接口返回的路由 如果过滤出来的length大于0说明时有权限的
            let items = meun_list.filter(
              (item) => item.frontPath.indexOf(route_list[i].path) != -1,
            );
            // 通过过滤接口返回的路由 如果过滤出来的length大于0说明时有权限的 反之没有权限不往新路由里赋值
            if (items.length > 0) {
              let child: any = []; //过滤出来的有权限的路由数组
              let child_item: any = []; //将二维数组和一维数组都放到这个数组里 方便匹配
              // 二维数组转为一维  重新赋值
              for (let j = 0; j < items.length; j++) {
                child_item.push(items[j]);
                if (items[j].children.length > 0) {
                  items[j].children.forEach((it) => {
                    child_item.push(it);
                  });
                }
              }
              // 循环数组下的children数组
              route_list[i].children.forEach((it) => {
                // 路由分割 只要后半部分
                it.pathName = it.path.split('/')[it.path.split('/').length - 1];
                // 分割下的数组length大于2时
                if (it.path.split('/').length > 2) {
                  it.frontName = it.path.split('/')[1];
                  // 判断一级路由是否相等和接口里返回的二级路由的后半部分通过indexof匹配路由
                  let child_it = child_item.filter(
                    (ite) =>
                      it.pathName.indexOf(ite.name) != -1 &&
                      it.pathName.indexOf(ite.name) == 0 &&
                      it.frontName == ite.frontName,
                  );
                  if (child_it.length > 0) {
                    child.push(it);
                  }
                } else {
                  // 分割下的数组length小于2时 进行模糊匹配
                  let child_it = child_item.filter(
                    (ite) =>
                      it.pathName.indexOf(ite.name) != -1 && it.pathName.indexOf(ite.name) == 0,
                  );
                  if (child_it.length > 0) {
                    child.push(it);
                  }
                }
              });
              // 组装路由
              route_list[i].children = child;
              route_list[i].redirect = items[0].children[0].frontPath;
              routeList.push(route_list[i]);
            }
          }
        }
      } catch (error) {
        console.error(error);
      }
      routeList.sort((a, b) => a.meta.orderNo - b.meta.orderNo);
      //根据过滤完的页面路由匹配返回数据组装菜单
      let menuList: any = [];
      menuLists.forEach((item) => {
        let item_obj = routeList.filter((ite) => ite.path == item.path);
        let redirectFlag = false;
        if (item_obj.length > 0) {
          if (item_obj[0].path == '/:path(.*)*') {
            menuList.push(item);
          } else {
            let item_arr = [];
            if (item.children && item.children.length > 0) {
              for (let j = 0; j < item.children.length; j++) {
                if (item_obj[0].children) {
                  let child: any = [];
                  if (item.path == '/statistics') {
                    child = item_obj[0].children.filter(
                      (it) =>
                        it.path ==
                        item.children[j].path.split('/')[
                          item.children[j].path.split('/').length - 1
                        ],
                    );
                    if (child.length > 0) {
                      item_arr.push(item.children[j]);
                      if (!redirectFlag) {
                        item_obj.redirect = item.children[j].path;
                        redirectFlag = true;
                      }
                    }
                  } else {
                    child = item_obj[0].children.filter((it) => it.path == item.children[j].path);
                    if (child.length > 0) {
                      let child_child: any = [];
                      if (item.children[j].children) {
                        for (let i = 0; i < item.children[j].children?.length; i++) {
                          if (
                            item_obj[0].children.filter(
                              (it) => it.path == item.children[j].children[i].path,
                            ).length > 0
                          ) {
                            child_child.push(item.children[j].children[i]);
                            if (!redirectFlag) {
                              item_obj.redirect = item.children[j].children[i].path;
                              redirectFlag = true;
                            }
                          }
                        }
                      }
                      item.children[j].children = child_child;
                      item_arr.push(item.children[j]);
                    }
                  }
                }
              }
              item.children = item_arr;
              menuList.push(item);
            } else {
              menuList.push(item);
            }
          }
        }
      });
      this.setFrontMenuList(menuList);
      //  后台路由到菜单结构
      const backMenuList = transformRouteToMenu(routeList);
      //  const sld_all_route = [];
      //  const menuList = JSON.parse(JSON.stringify(backMenuList));
      this.setBackMenuList(backMenuList);

      routeList = filter(routeList, routeRemoveIgnoreFilter);
      routeList = routeList.filter(routeRemoveIgnoreFilter);

      routeList = flatMultiLevelRoutes(routeList);
      routes = [...routeList];
      return routes;
    },
  },
});

// Need to be used outside the setup
// 需要在设置之外使用
export function usePermissionStoreWithOut() {
  return usePermissionStore(store);
}
