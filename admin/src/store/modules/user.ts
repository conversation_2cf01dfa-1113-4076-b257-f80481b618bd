import { defineStore } from 'pinia';
import { store } from '/@/store';
import { PageEnum } from '/@/enums/pageEnum';
import {
  ROLES_KEY,
  USER_INFO_KEY,
  SLD_ACCESS_TOKEN_KEY,
  SLD_REFRESH_TOKEN_KEY,
  SLD_LAST_GET_TOKEN_TIME_KEY,
  SLD_IS_SUPER_ADMIN_KEY,
  SLD_MENU_LIST_KEY,
  SLD_ADMIN_INFO_KEY,
} from '/@/enums/cacheEnum';
import { getAuthCache, setAuthCache } from '/@/utils/auth';
import { loginApi } from '/@/api/sys/user';
import { logoutApi, loginRefreshApi } from '/@/api/common/common';
import { useI18n } from '/@/hooks/web/useI18n';
import { useMessage } from '/@/hooks/web/useMessage';
import { router } from '/@/router';
import { usePermissionStore } from '/@/store/modules/permission';
import { PAGE_NOT_FOUND_ROUTE } from '/@/router/routes/basic';
import { isArray } from '/@/utils/is';
import { h } from 'vue';
import { redirectToService } from '@/utils';

export const useUserStore = defineStore({
  id: 'app-user-copy',
  state: () => ({
    userInfo: null,
    token: '',
    // 获取信息token
    accessToken: '',
    refreshToken: '',
    // 角色列表
    roleList: [],
    // Whether the login expired
    sessionTimeout: false,
    // Last fetch time
    lastUpdateTime: 0,
    // 最新的获取token的时间
    lastGetTokenTime: 0,
    // 是否供应商超级管理员
    isSuperAdmin: '',
    // 要删除的路由缓存
    delKeepAlive: [],
    // 要更新的页面 有tab的页面要用
    updateTab: [],
    // 管理员信息
    adminInfo: {
      adminId: '', // 管理员ID
      adminName: '', // 管理员ID
    },
    }),
  getters: {
    getDelKeepAlive(state) {
      return state.delKeepAlive;
    },
    getUpdateTab(state) {
      return state.updateTab;
    },
    getUserInfo(state) {
      return state.adminInfo || getAuthCache(USER_INFO_KEY) || {};
    },
    getToken(state) {
      return state.token || getAuthCache(SLD_ACCESS_TOKEN_KEY);
    },
    getRoleList(state) {
      return state.roleList.length > 0 ? state.roleList : getAuthCache(ROLES_KEY);
    },
    getSessionTimeout(state) {
      return !!state.sessionTimeout;
    },
    getLastUpdateTime(state) {
      return state.lastUpdateTime;
    },
    getAccessToken(state) {
      return state.accessToken || getAuthCache(SLD_ACCESS_TOKEN_KEY) || '';
    },
    getRefreshToken(state) {
      return state.refreshToken || getAuthCache(SLD_REFRESH_TOKEN_KEY) || '';
    },
    getLastGetTokenTime(state) {
      return state.lastGetTokenTime || getAuthCache(SLD_LAST_GET_TOKEN_TIME_KEY);
    },
    getIsSuperAdmin(state) {
      return state.isSuperAdmin || getAuthCache(SLD_IS_SUPER_ADMIN_KEY);
    },
    getAdminInfo(state) {
      let res = state.adminInfo;
      if (!res.adminId) {
        res = getAuthCache(SLD_ADMIN_INFO_KEY);
      }
      if (!res && JSON.parse(localStorage.getItem('admin_info'))) {
        res = JSON.parse(localStorage.getItem('admin_info'));
      }
      return res;
    },
    },
  actions: {
    setDelKeepAlive(info) {
      this.delKeepAlive = info;
    },
    setUpdateTab(info) {
      if (info) {
        let index = this.updateTab.findIndex((item) => item.name == info.name);
        if (index == -1) {
          this.updateTab.push(info);
        } else {
          this.updateTab.splice(index, 1, info);
        }
      } else {
        this.updateTab = [];
      }
    },
    setDelTab(name) {
      let index = this.updateTab.findIndex((item) => item.name == name);
      if (index > -1) {
        this.updateTab.splice(index, 1);
      }
    },
    setAdminInfo(info) {
      this.adminInfo = info;
      setAuthCache(SLD_ADMIN_INFO_KEY, info);
    },
    setToken(info) {
      this.token = info ? info : '';
      setAuthCache(SLD_ACCESS_TOKEN_KEY, info);
    },
    setRoleList(roleList) {
      this.roleList = roleList;
      setAuthCache(ROLES_KEY, roleList);
    },
    setUserInfo(info) {
      this.userInfo = info;
      this.lastUpdateTime = new Date().getTime();
      setAuthCache(USER_INFO_KEY, info);
    },
    setSessionTimeout(flag) {
      this.sessionTimeout = flag;
    },
    resetState() {
      this.userInfo = null;
      this.token = '';
      this.roleList = [];
      this.sessionTimeout = false;
    },
    setAccessToken(data) {
      this.accessToken = data;
      this.token = data;
      setAuthCache(SLD_ACCESS_TOKEN_KEY, data);
    },
    setRefreshToken(data) {
      this.refreshToken = data;
      setAuthCache(SLD_REFRESH_TOKEN_KEY, data);
    },
    setLastGetTokenTime(data) {
      this.lastGetTokenTime = data;
      setAuthCache(SLD_LAST_GET_TOKEN_TIME_KEY, data);
    },

    /**
     * @description: 登录成功更新信息
     */
    async loginUpdateInfo(data) {
      this.accessToken = data.access_token;
      this.refreshToken = data.refresh_token;
      this.lastGetTokenTime = new Date().getTime();
      this.isSuperAdmin = data.isSuper;
      this.adminInfo = {
        adminId: data.adminId,
        adminName: data.adminName,
      };

      localStorage.setItem(
        'admin_info',
        JSON.stringify({
          adminId: data.adminId,
          adminName: data.adminName,
        }),
      );

      setAuthCache(SLD_ACCESS_TOKEN_KEY, data.access_token);
      setAuthCache(SLD_REFRESH_TOKEN_KEY, data.refresh_token);
      setAuthCache(SLD_LAST_GET_TOKEN_TIME_KEY, this.lastGetTokenTime);
      setAuthCache(SLD_IS_SUPER_ADMIN_KEY, this.isSuperAdmin);
      setAuthCache(SLD_ADMIN_INFO_KEY, this.adminInfo);
      setAuthCache(SLD_MENU_LIST_KEY, data.resourceList);
      const permissionStore = usePermissionStore();
      await permissionStore.buildRoutesAction();
      const menuList = permissionStore.getFrontMenuList.filter(
        (item) => item.path !== '/:path(.*)*',
      );
      let targetPath = '/';
      if (menuList.length > 0) {
        if (menuList[0].path == '/im') {
          //跳转客服页面
          this.adminInfo.isOnlyIm = true;
          setAuthCache(SLD_ADMIN_INFO_KEY, this.adminInfo);
          redirectToService();
        } else {
          targetPath = menuList[0].redirect || menuList[1].redirect;
          //如果有redirect，校验路由有权限后跳转redirect，否则正常跳转
          let browserUrl = decodeURIComponent(window.location.href);
          const positionRe = browserUrl.indexOf('redirect=');
          if (positionRe > -1) {
            browserUrl = browserUrl.substring(positionRe + 9);
            let first_menu = browserUrl.split('/')[3] ? '/' + browserUrl.split('/')[3] : '';
            let second_menu =
              first_menu && browserUrl.split(first_menu + '/')[1]
                ? '/' + browserUrl.split(first_menu + '/')[1]
                : '';
            if (first_menu && second_menu) {
              let first_temp = menuList.filter((menu_item) => menu_item.path == first_menu);
              if (
                first_temp.length > 0 &&
                first_temp[0].children &&
                first_temp[0].children.filter((menu_item) => menu_item.path == second_menu).length >
                  0
              ) {
                targetPath = browserUrl;
              }
            }
          }
        }
      } else {
        //没有菜单的话应该跳转404页面
      }
      window.location.replace(targetPath);
    },
    /**
     * @description: login
     */
    async login(params) {
      try {
        const { goHome = true, mode, ...loginParams } = params;
        const data = await loginApi(loginParams, mode);
        const { token } = data;

        // save token
        this.setToken(token);
        return this.afterLoginAction(goHome);
      } catch (error) {
        return Promise.reject(error);
      }
    },
    async afterLoginAction(goHome) {
      if (!this.getAccessToken) return null;

      const userInfo = await this.getUserInfoAction();

      const sessionTimeout = this.sessionTimeout;
      if (sessionTimeout) {
        this.setSessionTimeout(false);
      } else {
        const permissionStore = usePermissionStore();
        if (!permissionStore.isDynamicAddedRoute) {
          const routes = await permissionStore.buildRoutesAction();
          routes.forEach((route) => {
            router.addRoute(route);
          });
          router.addRoute(PAGE_NOT_FOUND_ROUTE);
          permissionStore.setDynamicAddedRoute(true);
        }
        goHome && (await router.replace(userInfo?.homePath || PageEnum.BASE_HOME));
      }
      return userInfo;
    },
    async getUserInfoAction() {
      if (!this.getToken) return null;
      const userInfo = await this.getUserInfo;
      const { roles = [] } = userInfo;
      if (isArray(roles)) {
        const roleList = roles.map((item) => item.value);
        this.setRoleList(roleList);
      } else {
        userInfo.roles = [];
        this.setRoleList([]);
      }
      this.setUserInfo(userInfo);
      return userInfo;
    },
    async getSldCommonService() {
      if (!this.getToken) return null;
      try {
        let curTime = new Date().getTime();
        if (this.getLastGetTokenTime && curTime - this.getLastGetTokenTime * 1 > 58 * 60 * 1000) {
          let res = await loginRefreshApi({
            grant_type: 'refresh_token',
            refresh_token: this.getRefreshToken,
          });
          if (res.state == 200 || res.state == 267) {
            //更新accessToken、refreshToken、lastGetTokenTime
            this.setAccessToken(res.data.access_token);
            this.setRefreshToken(res.data.refresh_token);
            this.setLastGetTokenTime(new Date().getTime());
            return {
              token: res.data.access_token,
              state: 200,
            };
          }
        } else {
          return {
            state: 255,
          };
        }
      } catch {
        console.log('刷新Token失败');
      }
    },
    /**
     * @description: logout
     */
    async logout(goLogin = false) {
      if (this.getToken) {
        try {
          await logoutApi();
        } catch {
          console.log('注销Token失败');
        }
      }
      this.setToken(undefined);
      this.setAccessToken(undefined);
      this.setRefreshToken(undefined);
      this.setSessionTimeout(false);
      this.setUserInfo(null);
      localStorage.removeItem('admin_info');
      //增加redirect
      let browserUrl = window.location.href;
      if (browserUrl.indexOf('redirect=') != -1) {
        browserUrl = browserUrl.substring(0, browserUrl.indexOf('redirect=') - 1);
      }
      goLogin && router.push(PageEnum.BASE_LOGIN + '?redirect=' + encodeURIComponent(browserUrl));
    },

    /**
     * @description: Confirm before logging out
     */
    confirmLoginOut() {
      const { createConfirm } = useMessage();
      const { t } = useI18n();
      createConfirm({
        iconType: 'warning',
        title: () => h('span', t('sys.app.logoutTip')),
        content: () => h('span', t('sys.app.logoutMessage')),
        onOk: async () => {
          await this.logout(true);
        },
      });
    },
  },
});

// Need to be used outside the setup
export function useUserStoreWithOut() {
  return useUserStore(store);
}
