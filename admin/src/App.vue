<template>
  <ConfigProvider :locale="getAntdLocale">
    <AppProvider>
      <RouterView />
    </AppProvider>
  </ConfigProvider>
</template>

<script lang="ts" setup>
  import { ConfigProvider } from 'ant-design-vue';
  import { AppProvider } from '@/components/Application';
  import { useTitle } from '@/hooks/web/useTitle';
  import { useLocale } from '@/locales/useLocale';
  import { setAuthCache } from '@/utils/auth';
  import { TOKEN_KEY } from '/@/enums/cacheEnum';

  import 'dayjs/locale/zh-cn';
  import { onMounted } from 'vue';

  // support Multi-language
  const { getAntdLocale } = useLocale();

  // Listening to page changes and dynamically changing site titles
  useTitle();

  //调试代码

  onMounted(() => {
    let token =
      'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************.DqdJx3sq6GMBXvsDF9epTjUfiSXBeEW0Dz03mLbHrjsXz8hH8gm5F_8r419EwwRKU-By2wzRZFmeYlhluWGul7IO6FbtvrvTSa8TK5z5tDBAgIlYcuOiOHfI2pQ-8iy0vUANiOo1YQr4lMRyqed1lypOewcKIIBJM2l13vAgmMQ';
    setAuthCache(TOKEN_KEY, token);
  });
</script>

<style lang="less">
  .ant-dropdown {
    &.slodon-header-user-dropdown-dropdown-overlay {
      top: 54px !important;
      right: 2px !important;
      left: unset !important;
    }
  }
</style>
