import type { RouteLocationNormalized, RouteRecordNormalized } from 'vue-router';
import type { App, Component } from 'vue';

import { intersectionWith, isEqual, mergeWith, unionWith } from 'lodash-es';
import { unref } from 'vue';
import { isArray, isObject } from '/@/utils/is';
import { useGlobSetting } from '../hooks/setting';
import { useUserStore } from '@/store/modules/user';

export const noop = () => {};

/**
 * @description:  Set ui mount node
 */
export function getPopupContainer(node?: HTMLElement): HTMLElement {
  return (node?.parentNode as HTMLElement) ?? document.body;
}

/**
 * Add the object as a parameter to the URL
 * @param baseUrl url
 * @param obj
 * @returns {string}
 * eg:
 *  let obj = {a: '3', b: '4'}
 *  setObjToUrlParams('www.baidu.com', obj)
 *  ==>www.baidu.com?a=3&b=4
 */
export function setObjToUrlParams(baseUrl: string, obj: any): string {
  let parameters = '';
  for (const key in obj) {
    parameters += key + '=' + encodeURIComponent(obj[key]) + '&';
  }
  parameters = parameters.replace(/&$/, '');
  return /\?$/.test(baseUrl) ? baseUrl + parameters : baseUrl.replace(/\/?$/, '?') + parameters;
}

/**
 * Recursively merge two objects.
 * 递归合并两个对象。
 *
 * @param source The source object to merge from. 要合并的源对象。
 * @param target The target object to merge into. 目标对象，合并后结果存放于此。
 * @param mergeArrays How to merge arrays. Default is "replace".
 *        如何合并数组。默认为replace。
 *        - "union": Union the arrays. 对数组执行并集操作。
 *        - "intersection": Intersect the arrays. 对数组执行交集操作。
 *        - "concat": Concatenate the arrays. 连接数组。
 *        - "replace": Replace the source array with the target array. 用目标数组替换源数组。
 * @returns The merged object. 合并后的对象。
 */
export function deepMerge<T extends object | null | undefined, U extends object | null | undefined>(
  source: T,
  target: U,
  mergeArrays: 'union' | 'intersection' | 'concat' | 'replace' = 'replace',
): T & U {
  if (!target) {
    return source as T & U;
  }
  if (!source) {
    return target as T & U;
  }
  return mergeWith({}, source, target, (sourceValue, targetValue) => {
    if (isArray(targetValue) && isArray(sourceValue)) {
      switch (mergeArrays) {
        case 'union':
          return unionWith(sourceValue, targetValue, isEqual);
        case 'intersection':
          return intersectionWith(sourceValue, targetValue, isEqual);
        case 'concat':
          return sourceValue.concat(targetValue);
        case 'replace':
          return targetValue;
        default:
          throw new Error(`Unknown merge array strategy: ${mergeArrays as string}`);
      }
    }
    if (isObject(targetValue) && isObject(sourceValue)) {
      return deepMerge(sourceValue, targetValue, mergeArrays);
    }
    return undefined;
  });
}

export function openWindow(
  url: string,
  opt?: { target?: TargetContext | string; noopener?: boolean; noreferrer?: boolean },
) {
  const { target = '__blank', noopener = true, noreferrer = true } = opt || {};
  const feature: string[] = [];

  noopener && feature.push('noopener=yes');
  noreferrer && feature.push('noreferrer=yes');

  window.open(url, target, feature.join(','));
}

// dynamic use hook props
export function getDynamicProps<T extends Record<string, unknown>, U>(props: T): Partial<U> {
  const ret: Recordable = {};

  Object.keys(props).map((key) => {
    ret[key] = unref((props as Recordable)[key]);
  });

  return ret as Partial<U>;
}

export function getRawRoute(route: RouteLocationNormalized): RouteLocationNormalized {
  if (!route) return route;
  const { matched, ...opt } = route;
  return {
    ...opt,
    matched: (matched
      ? matched.map((item) => ({
          meta: item.meta,
          name: item.name,
          path: item.path,
        }))
      : undefined) as RouteRecordNormalized[],
  };
}

// https://github.com/vant-ui/vant/issues/8302
type EventShim = {
  new (...args: any[]): {
    $props: {
      onClick?: (...args: any[]) => void;
    };
  };
};

export type WithInstall<T> = T & {
  install(app: App): void;
} & EventShim;

export type CustomComponent = Component & { displayName?: string };

export const withInstall = <T extends CustomComponent>(component: T, alias?: string) => {
  (component as Record<string, unknown>).install = (app: App) => {
    const compName = component.name || component.displayName;
    if (!compName) return;
    app.component(compName, component);
    if (alias) {
      app.config.globalProperties[alias] = component;
    }
  };
  return component as WithInstall<T>;
};

export const getImagePath = (path: string) => {
  const pathname = new URL(`../assets/${path}`, import.meta.url);
  return pathname as unknown as string;
};

/*
 * 驼峰形式的字符串转为下划线连接
 * @param {int} index 第几列
 * @param {int} size 宽度值
 * @param {array} columns_data table的columns数据
 * */
export function sldConvert(str) {
  let target = '';
  let strArray = str.split('');
  strArray.map((item) => {
    if (item === item.toUpperCase()) {
      target += `_${item.toLowerCase()}`;
    } else {
      target += item;
    }
  });
  return target;
}

/*
 * 数字格式化，增加单位万，减少数据长度，返回数据如：1.5万或者8,999
 * @param {Number} num 要格式化的数据
 * */
export function formatNumPieCircle(num: number, toFixedNum = 0) {
  let target = num.toString();
  let unit = '';
  if (target) {
    if (Number(target) < 10000) {
      target = String(Number(target).toFixed(toFixedNum));
    } else {
      target = (Number(target) / 10000).toFixed(1);
      unit = '万';
    }
    let regExpInfo = /(\d{1,3})(?=(\d{3})+(?:$|\.))/g;
    target = target.toString().replace(regExpInfo, '$1,');
    target = target + unit;
  } else {
    target = num.toString();
  }
  return target;
}

// 16进制颜色转换成rgb,用于 动态颜色转换
export function set16ToRgb(str, op) {
  var reg = /^#([0-9A-Fa-f]{3}|[0-9A-Fa-f]{6})$/;
  if (!reg.test(str)) {
    return;
  }
  let newStr = str.toLowerCase().replace(/\#/g, '');
  let len = newStr.length;
  if (len == 3) {
    let t = '';
    for (var i = 0; i < len; i++) {
      t += newStr.slice(i, i + 1).concat(newStr.slice(i, i + 1));
    }
    newStr = t;
  }
  let arr = []; //将字符串分隔，两个两个的分隔
  for (var i = 0; i < 6; i = i + 2) {
    let s = newStr.slice(i, i + 2);
    arr.push(parseInt('0x' + s));
  }

  if (op !== undefined && op !== '') {
    return `rgba(${arr.join(',')},${op})`;
  } else {
    return 'rgb(' + arr.join(',') + ')';
  }
}

export function extraProperty(target, props) {
  let result = {};
  for (let p of props) {
    result[p] = target[p];
  }
  return result;
}

//获取图片路径的bindId参数
export const resolveImageBindId = (imgPath) => {
  let fakeUrl = new URL(imgPath, 'http://fake.a');
  return fakeUrl.searchParams.get('bindId') || '';
};

export const jumpToService = () => {
  const { getAdminInfo, getAccessToken, getRefreshToken, getIsSuperAdmin } = useUserStore();
  const { imUrl } = useGlobSetting();
  const messageData = {
    tokenInfo: {
      sld_token: getAccessToken,
      sld_refresh_token: getRefreshToken,
    },
    platInfo: {
      adminId: getAdminInfo.adminId,
      adminName: getAdminInfo.adminName,
      isSuper: getIsSuperAdmin,
    },
    identity: 'admin',
  };

  const imWin = window.open(
    `${imUrl}/redirect?source=${encodeURIComponent(window.location.origin)}`,
    '_blank',
  );
  window.onmessage = (e) => {
    if (e.data && e.origin == imUrl) {
      imWin?.postMessage(messageData, imUrl);
    }
  };
};

export const redirectToService = () => {
  const { getAdminInfo, getAccessToken, getRefreshToken, getIsSuperAdmin } = useUserStore();
  const { imUrl } = useGlobSetting();

  let targetInfo = {
    adminId: getAdminInfo.adminId,
    adminName: getAdminInfo.adminName,
    isSuper: getIsSuperAdmin,
  };

  let target =
    imUrl +
    `/redirect?sld_token=${getAccessToken}&sld_refresh_token=${getRefreshToken}&userInfo=${encodeURIComponent(
      JSON.stringify(targetInfo),
    )}&immidiate=true`;

  window.location.replace(target);
};
