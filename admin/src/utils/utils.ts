import { toRaw } from 'vue';
import { message } from 'ant-design-vue';
import { useI18n } from '/@/hooks/web/useI18n';
const { t } = useI18n();

export const nav_default_img = new URL(
  '/@/assets/images/m_diy_img/nav_default.png',
  import.meta.url,
).href;
export const default_goods_img = new URL(
  '/@/assets/images/m_diy_img/default_goods_img.png',
  import.meta.url,
).href;
export const more_tab_empty_goods_img = new URL(
  '/@/assets/images/m_diy_img/more_tab/empty_goods_img.png',
  import.meta.url,
).href;
export const top_nav_cat_swiper1 = new URL(
  '/@/assets/images/m_diy_img/top_nav_cat/swiper1.png',
  import.meta.url,
).href;
export const top_nav_cat_swiper2 = new URL(
  '/@/assets/images/m_diy_img/top_nav_cat/swiper2.png',
  import.meta.url,
).href;
export const top_nav_cat_swiper3 = new URL(
  '/@/assets/images/m_diy_img/top_nav_cat/swiper2.png',
  import.meta.url,
).href;
export const detail_total_icon = new URL(
  '/@/assets/images/bill/detail_total_icon.png',
  import.meta.url,
).href;

export const list_com_page_more = 10000;
export const list_com_page_size_10 = 10;
export const list_com_page_size_5 = 5;
export const list_com_page_size_7 = 7;
export const list_com_page_size_15 = 15;
export const list_com_page_size_16 = 16;
export const list_com_page_size_20 = 20;

/*
 * 空数组
 * */
export const sld_com_empty_arrar_2 = [1, 2];
export const sld_com_empty_arrar_3 = [1, 2, 3];
export const sld_com_empty_arrar_4 = [1, 2, 3, 4];
export const sld_com_empty_arrar_5 = [1, 2, 3, 4, 5];
export const sld_com_empty_arrar_6 = [1, 2, 3, 4, 5, 6];
export const sld_com_empty_arrar_7 = [1, 2, 3, 4, 5, 6, 7];
export const sld_com_empty_arrar_8 = [1, 2, 3, 4, 5, 6, 7, 8];
export const sld_com_empty_arrar_9 = [1, 2, 3, 4, 5, 6, 7, 8, 9];

//通用的年月日
export const dateFormat = 'YYYY-MM-DD';
export const dateTimeFormat = 'YYYY-MM-DD HH:mm:ss';
export const dateTimeFormatOne = 'YYYY-MM-DD HH:mm:00';

/*
 * 将时间戳返回为常规格式时间  返回的结果如：2023-11-17 17:41:00
 * */
export function sldToNormalDate(val) {
  let time = new Date(val);
  let Y = time.getFullYear();
  let M = time.getMonth() + 1 < 10 ? '0' + (time.getMonth() + 1) : time.getMonth() + 1;
  let D = time.getDate() < 10 ? '0' + time.getDate() : time.getDate();
  let h = time.getHours() < 10 ? '0' + time.getHours() : time.getHours();
  let m = time.getMinutes() < 10 ? '0' + time.getMinutes() : time.getMinutes();
  let s = time.getSeconds() < 10 ? '0' + time.getSeconds() : time.getSeconds();
  return Y + '-' + M + '-' + D + ' ' + h + ':' + m + ':' + s;
}

/*
 * 修改配置成功后，需点击右上角更新配置按钮更新配置才可生效
 * */
export function sld_need_update_setting() {
  return [`修改配置成功后，需点击右上角更新配置按钮更新配置才可生效`];
}

/*
 * 成功提示
 * @params con 提示内容
 * @params time 提示时间
 * */
export function sucTip(con, time = 2) {
  let not_show_message = localStorage.getItem('not_show_message') == '1' ? true : false;
  if (!not_show_message) {
    message.success(con, time);
  }
}

/*
 * 失败提示
 * @params con 提示内容
 * @params time 提示时间
 * */
export function failTip(con, time = 3) {
  let not_show_message = localStorage.getItem('not_show_message') == '1' ? true : false;
  if (!not_show_message) {
    message.warn(con, time);
  }
}

/*
 * 多语言翻译函数
 * */
export function sldComLanguage(name) {
  return t('content.' + name) !== '' && t('content.' + name).indexOf('content.') == -1
    ? t('content.' + name)
    : name;
}

export const uploadLimit = 20; //上传限制，单位M

/*
 * 富文本内容反转义（接口返回的富文本内容经过了转义，导致内容无法展示，所以需要反转义）
 * @param {String} str 富文本内容
 * */
export function quillEscapeToHtml(str) {
  if (str != undefined && str) {
    const arrEntities = { lt: '<', gt: '>', nbsp: ' ', amp: '&', quot: '"' };
    return str.replace(/&(lt|gt|nbsp|amp|quot);/gi, function (all, t) {
      return arrEntities[t];
    });
  } else {
    return '';
  }
}

// 数组去重
export function reduction(tempArr, id) {
  for (let i = 0; i < tempArr.length; i++) {
    for (let j = i + 1; j < tempArr.length; j++) {
      if (tempArr[i][id] == tempArr[j][id]) {
        tempArr.splice(j, 1);
        j--;
      }
    }
  }
  return tempArr;
}

// 单选多选事件 selectedRowKeys:表格选中的keys selectedRows:选中的数据 ids：表格key record:点击的数据 selected是否选中
export function selectRadio(selectedRowKeys, selectedRows, ids, record, selected) {
  let rows: any = {
    selectedRowKeys: selectedRowKeys,
    selectedRows: selectedRows,
  };
  if (selected) {
    rows.selectedRowKeys = [...rows.selectedRowKeys, record[ids]];
    rows.selectedRows.push(record);
  } else {
    rows.selectedRowKeys = rows.selectedRowKeys.filter((id) => id !== record[ids]);
    rows.selectedRows = rows.selectedRows.filter((item) => item[ids] !== record[ids]);
  }
  return rows;
}

// 全选按钮事件 selectedRowKeys:表格选中的keys selectedRows:选中的数据 ids：表格key rows:全选的数据 selected是否选中  changeRows访问页的数据
export function SelectAll(selectedRowKeys, selectedRows, ids, selected, rows, changeRows) {
  let rowAll: any = {
    selectedRowKeys: selectedRowKeys,
    selectedRows: selectedRows,
  };
  const changeIds = changeRows.map((item) => item[ids]);
  let row: any = [];
  rows.forEach((item) => {
    if (item) {
      row.push(toRaw(item));
    }
  });
  if (selected) {
    let selectedRowKey: any = [];
    selectedRowKey = [...rowAll.selectedRowKeys, ...changeIds];
    rowAll.selectedRowKeys = selectedRowKey;
    rowAll.selectedRows = [...rowAll.selectedRows, ...row];
    rowAll.selectedRows = reduction(rowAll.selectedRows, ids);
  } else {
    rowAll.selectedRowKeys = rowAll.selectedRowKeys.filter((id) => {
      return !changeIds.includes(id);
    });
    let selectedRow: any = [];
    //去掉的话要删掉行数据
    rowAll.selectedRows.forEach((item) => {
      if (rowAll.selectedRowKeys.indexOf(item[ids]) != -1) {
        selectedRow.push(item);
      }
    });
    rowAll.selectedRows = selectedRow;
  }
  return rowAll;
}

//文件上传前处理数据
export function beforeUpload(file, accept, limit = 20) {
  if (accept != undefined && accept != null && accept) {
    //校验文件格式类型
    let accept_list = accept
      .replaceAll(' ', '')
      .split(',')
      .filter((item) => item && item.trim());
    let file_type = file.name ? '.' + file.name.split('.').at(-1) : '';
    if (accept_list.length > 0 && file_type != '' && accept_list.indexOf(file_type) == -1) {
      failTip('上传文件格式有误');
      return false;
    }
  }
  let uploadLimit = limit ? limit : 20;
  if (file.size > 1024 * 1024 * uploadLimit) {
    failTip('上传文件过大，请上传小于' + uploadLimit + 'M的文件');
    return false;
  }
}

/*
 * 过滤表情
 * @param {String} str 文本内容
 * */
export function replaceEmoji(str) {
  const emoji_reg = /(\ud83c[\udf00-\udfff])|(\ud83d[\udc00-\ude4f\ude80-\udeff])|[\u2600-\u2B55]/g;
  if (str) {
    return str.replace(emoji_reg, '');
  }
}

/*
 * 获取缓存中图片信息
 * @params name  缓存的键
 * */
export function getSldImgSet(name) {
  if (localStorage.getItem(name) != undefined && localStorage.getItem(name)) {
    const item: any = localStorage.getItem(name);
    return JSON.parse(item);
  } else {
    return '';
  }
}

/**
 * base64加密
 * @params data String 要加密的字符串
 */
export function base64Encode(data) {
  const b64 = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
  let o1,
    o2,
    o3,
    h1,
    h2,
    h3,
    h4,
    bits,
    i = 0,
    ac = 0,
    enc = '';
  const tmp_arr: any = [];
  if (!data) {
    return data;
  }
  data = utf8Encode(data);
  do {
    o1 = data.charCodeAt(i++);
    o2 = data.charCodeAt(i++);
    o3 = data.charCodeAt(i++);

    bits = (o1 << 16) | (o2 << 8) | o3;

    h1 = (bits >> 18) & 0x3f;
    h2 = (bits >> 12) & 0x3f;
    h3 = (bits >> 6) & 0x3f;
    h4 = bits & 0x3f;
    tmp_arr[ac++] = b64.charAt(h1) + b64.charAt(h2) + b64.charAt(h3) + b64.charAt(h4);
  } while (i < data.length);

  enc = tmp_arr.join('');

  switch (data.length % 3) {
    case 1:
      enc = enc.slice(0, -2) + '==';
      break;
    case 2:
      enc = enc.slice(0, -1) + '=';
      break;
  }

  return enc;
}

/**
 * base64加密
 * @params data String 要加密的字符串
 * @zoucb-2023-05-25
 */
export function base64Encrypt(data) {
  let b64_obj = {
    a: 'CABDE',
    b: 'FoHIJ',
    c: 'KLpNO',
    d: 'PQRyr',
    e: 'UVWXi',
    f: 'Zght6',
    g: 'efabYzS',
    h: 'jkmu012l',
    m: 'GMqTs345d',
    n: 'cnvwx789()=',
  };
  let position = [
    'a:0',
    'b:1',
    'c:2',
    'd:3',
    'd:4',
    'e:4',
    'f:1',
    'f:2',
    'f:3',
    'n:8',
    'a:1',
    'n:9',
    'g:6',
    'h:7',
    'b:1',
    'm:8',
    'b:1',
    'n:1',
  ];
  for (let i in position) {
    let [k, v] = position[i].split(':');
    data = data + `${b64_obj[k][v]}`;
  }

  let b64 = Object.values(b64_obj).join('');

  let o1,
    o2,
    o3,
    h1,
    h2,
    h3,
    h4,
    bits,
    i = 0,
    ac = 0,
    enc = '',
    tmp_arr = [];
  if (!data) {
    return data;
  }
  data = utf8Encode(data);
  do {
    o1 = data.charCodeAt(i++);
    o2 = data.charCodeAt(i++);
    o3 = data.charCodeAt(i++);

    bits = (o1 << 16) | (o2 << 8) | o3;

    h1 = (bits >> 18) & 0x3f;
    h2 = (bits >> 12) & 0x3f;
    h3 = (bits >> 6) & 0x3f;
    h4 = bits & 0x3f;
    tmp_arr[ac++] = b64.charAt(h1) + b64.charAt(h2) + b64.charAt(h3) + b64.charAt(h4);
  } while (i < data.length);

  enc = tmp_arr.join('');

  switch (data.length % 3) {
    case 1:
      enc = enc.slice(0, -2) + '==';
      break;
    case 2:
      enc = enc.slice(0, -1) + '=';
      break;
  }

  return enc;
}

export function utf8Encode(string) {
  string = (string + '').replace(/\r\n/g, '\n').replace(/\r/g, '\n');

  let utftext = '',
    start,
    end;
  let stringl = 0,
    n;

  start = end = 0;
  stringl = string.length;

  for (n = 0; n < stringl; n++) {
    const c1 = string.charCodeAt(n);
    let enc: any = null;

    if (c1 < 128) {
      end++;
    } else if (c1 > 127 && c1 < 2048) {
      enc = String.fromCharCode((c1 >> 6) | 192, (c1 & 63) | 128);
    } else {
      enc = String.fromCharCode((c1 >> 12) | 224, ((c1 >> 6) & 63) | 128, (c1 & 63) | 128);
    }
    if (enc !== null) {
      if (end > start) {
        utftext += string.substring(start, end);
      }
      utftext += enc;
      start = end = n + 1;
    }
  }

  if (end > start) {
    utftext += string.substring(start, string.length);
  }

  return utftext;
}

//TAB切换购物车展示图标

export function day_hour() {
  return [
    '00:00',
    '01:00',
    '02:00',
    '03:00',
    '04:00',
    '05:00',
    '06:00',
    '07:00',
    '08:00',
    '09:00',
    '10:00',
    '11:00',
    '12:00',
    '13:00',
    '14:00',
    '15:00',
    '16:00',
    '17:00',
    '18:00',
    '19:00',
    '20:00',
    '21:00',
    '22:00',
    '23:00',
  ];
}

//周一～周日对应的数字
export function week_to_num() {
  return [
    {
      label: `周一`,
      value: '1',
    },
    {
      label: `周二`,
      value: '2',
    },
    {
      label: `周三`,
      value: '3',
    },
    {
      label: `周四`,
      value: '4',
    },
    {
      label: `周五`,
      value: '5',
    },
    {
      label: `周六`,
      value: '6',
    },
    {
      label: `周日`,
      value: '7',
    },
  ];
}

//1～31号对应的数字
export function month_to_num() {
  return [
    {
      label: '1',
      value: '1',
    },
    {
      label: '2',
      value: '2',
    },
    {
      label: '3',
      value: '3',
    },
    {
      label: '4',
      value: '4',
    },
    {
      label: '5',
      value: '5',
    },
    {
      label: '6',
      value: '6',
    },
    {
      label: '7',
      value: '7',
    },
    {
      label: '8',
      value: '8',
    },
    {
      label: '9',
      value: '9',
    },
    {
      label: '10',
      value: '10',
    },
    {
      label: '11',
      value: '11',
    },
    {
      label: '12',
      value: '12',
    },
    {
      label: '13',
      value: '13',
    },
    {
      label: '14',
      value: '14',
    },
    {
      label: '15',
      value: '15',
    },
    {
      label: '16',
      value: '16',
    },
    {
      label: '17',
      value: '17',
    },
    {
      label: '18',
      value: '18',
    },
    {
      label: '19',
      value: '19',
    },
    {
      label: '20',
      value: '20',
    },
    {
      label: '21',
      value: '21',
    },
    {
      label: '22',
      value: '22',
    },
    {
      label: '23',
      value: '23',
    },
    {
      label: '24',
      value: '24',
    },
    {
      label: '25',
      value: '25',
    },
    {
      label: '26',
      value: '26',
    },
    {
      label: '27',
      value: '27',
    },
    {
      label: '28',
      value: '28',
    },
    {
      label: '29',
      value: '29',
    },
    {
      label: '30',
      value: '30',
    },
    {
      label: '31',
      value: '31',
    },
  ];
}

//数字转为对应的汉字
export function num_to_num() {
  return {
    1: `一`,
    2: `二`,
    3: `三`,
    4: `四`,
    5: `五`,
  };
}

export const specialFlag =
  window.location.host.indexOf('admin.55sld.com') +
  window.location.host.indexOf('jbbcadmindev.slodon.cn') +
  window.location.host.indexOf('localhost'); //版本号匹配域名显示

/*
 * 装修连接选择器-PC端
 * */

export function diy_link_type() {
  return [
    { key: '', name: `${'无操作'}` },
    { key: 'url', name: `${'链接地址'}` },
    { key: 'keyword', name: `${'关键字'}` },
    { key: 'goods', name: `${'商品'}` },
    { key: 'category', name: `${'商品分类'}` },
    { key: 'topic', name: `${'专题'}` },
    { key: 'brand_home', name: `${'品牌列表'}` },
    { key: 'voucher_center', name: `${'领券中心'}` },
    { key: 'store', name: `${'店铺'}` },
    { key: 'store_list', name: `${'店铺街'}` },
    { key: 'point_center', name: '积分商城' },
  ];
}

/*
 * 装修连接选择器-pc批发市场端
 * */

export function diy_supplier_link_type() {
  return [
    { key: '', name: `${'无操作'}` },
    { key: 'url', name: `${'链接地址'}` },
    { key: 'keyword', name: `${'关键字'}` },
    { key: 'goods', name: `${'商品'}` },
    { key: 'category', name: `${'商品分类'}` },
    { key: 'topic', name: `${'专题'}` },
    { key: 'brand_home', name: `${'品牌列表'}` },
    { key: 'voucher_center', name: `${'领券中心'}` },
    { key: 'store', name: `${'店铺'}` },
    { key: 'store_list', name: `${'店铺街'}` },
  ];
}

/*
 * 格式化数字，超过1万的处理成单位为W的数据，未超过的不处理
 * */
export function formatNumW(num) {
  let result: any = 0;
  num = num * 1;
  if (num < 10000) {
    result = num;
  } else {
    result = (num / 10000).toFixed(2) + 'w';
  }
  return result;
}

/*
 * 装修连接选择器-移动端
 * */
export function m_diy_link_type() {
  return [
    { key: '', name: `${'无操作'}` },
    { key: 'url', name: `${'链接地址'}` },
    { key: 'keyword', name: `${'关键字'}` },
    { key: 'goods', name: `${'商品'}` },
    { key: 'category', name: `${'商品分类'}` },
    { key: 'store', name: `${'店铺'}` },
    { key: 'store_list', name: '店铺街' },
    { key: 'topic', name: `${'专题'}` },
    { key: 'brand_home', name: `${'品牌列表'}` },
    { key: 'seckill', name: `${'秒杀首页'}` },
    { key: 'spell_group', name: `${'拼团首页'}` },
    { key: 'ladder_group', name: `${'阶梯团首页'}` },
    { key: 'presale', name: `${'预售首页'}` },
    { key: 'voucher_center', name: `${'领券中心'}` },
    { key: 'point', name: `${'积分商城'}` },
    { key: 'svideo_center', name: `${'短视频中心'}` },
    
    { key: 'sign_center', name: `${'签到中心'}` },
    // spreader-1-start
    { key: 'spreader_center', name: `${'推手中心'}` },
    // spreader-1-end
    { key: 'rank', name: `${'排行榜中心'}` },
    { key: 'draw', name: `${'抽奖活动'}` },
  ];
}

export function m_diy_o2o_link_type() {
  return [
    { key: '', name: `${'无操作'}` },
    { key: 'url', name: `${'链接地址'}` },
    { key: 'keyword', name: `${'关键字'}` },
    { key: 'goods', name: `${'商品'}` },
    { key: 'category', name: `${'商品分类'}` },
    { key: 'store', name: `${'店铺'}` },
    { key: 'store_list', name: '店铺街' },
    { key: 'o2o_topic', name: `${'专题'}` },
    { key: 'brand_home', name: `${'品牌列表'}` },
    { key: 'seckill', name: `${'秒杀首页'}` },
    { key: 'spell_group', name: `${'拼团首页'}` },
    { key: 'ladder_group', name: `${'阶梯团首页'}` },
    { key: 'presale', name: `${'预售首页'}` },
    { key: 'voucher_center', name: `${'领券中心'}` },
    { key: 'point', name: `${'积分商城'}` },
    { key: 'svideo_center', name: `${'短视频中心'}` },
    
    { key: 'sign_center', name: `${'签到中心'}` },
    // spreader-2-start
    { key: 'spreader_center', name: `${'推手中心'}` },
    // spreader-2-end
    { key: 'rank', name: `${'排行榜中心'}` },
    { key: 'draw', name: `${'抽奖活动'}` },
  ];
}

/*
 * 装修连接选择器-移动端-积分商城
 * */

export function m_diy_point_link_type() {
  return [
    { key: '', name: '无操作' },
    { key: 'url', name: '链接地址' },
    { key: 'keyword', name: '关键字' },
    { key: 'goods', name: '商品' },
    { key: 'category', name: '积分标签' },
  ];
}

/*
 * 装修连接选择器-移动端-推手装修
 * */

export function m_diy_spreader_link_type() {
  return [
    { key: '', name: '无操作' },
    { key: 'url', name: '链接地址' },
    { key: 'keyword', name: '关键字' },
    { key: 'goods', name: '商品' },
    { key: 'category', name: '商品标签' },
  ];
}

/*
 * 新的装修连接选择器-移动端-推手装修
 * */
export const spreader_diy_spreader_link_type = {
  '': {
    name: '无操作',
    need_link_value: false,
  },
  url: {
    name: '链接地址',
    need_link_value: true,
    require_select_modal: false,
  },
  keyword: {
    name: '关键字',
    need_link_value: true,
    require_select_modal: false,
  },
  goods: {
    name: '商品',
    need_link_value: true,
    require_select_modal: true,
  },
  category: {
    name: '商品标签',
    need_link_value: true,
    require_select_modal: true,
  },
  formatOptions() {
    let target = [];
    Object.keys(this).forEach((key) => {
      if (typeof this[key] == 'object') {
        //@ts-ignore
        target.push({
          value: key,
          label: this[key].name,
        });
      }
    });
    return target;
  },
};

//基本类型映射
export const singleDiy_type_name_map = {
  url: '链接地址',
  draw: '抽奖活动名称',
  seckill: '秒杀活动名称',
  store: '店铺名称',
  topic: '专题名称',
  category: '分类名称',
  goods: '商品名称',
  keyword: '关键字',
};

/*
 * 新的装修连接选择器-PC端和移动端公用
 * */
export const new_diy_link_type = {
  '': {
    name: '无操作',
    need_link_value: false,
  },
  url: {
    name: '链接地址',
    need_link_value: true,
    require_select_modal: false,
  },
  keyword: {
    name: '关键字',
    need_link_value: true,
    require_select_modal: false,
  },
  goods: {
    name: '商品',
    need_link_value: true,
    require_select_modal: true,
  },
  category: {
    name: '商品分类',
    need_link_value: true,
    require_select_modal: true,
  },
  topic: {
    name: '专题',
    need_link_value: true,
    require_select_modal: true,
  },
  brand_home: {
    name: '品牌列表',
    need_link_value: false,
    require_select_modal: false,
  },
  voucher_center: {
    name: '领券中心',
    need_link_value: false,
    require_select_modal: false,
  },
  store: {
    name: '店铺',
    need_link_value: true,
    require_select_modal: true,
  },
  store_list: {
    name: '店铺街',
    need_link_value: false,
    require_select_modal: false,
  },
  point_center: {
    name: '积分商城',
    need_link_value: false,
    require_select_modal: false,
  },
  formatOptions() {
    let target = [];
    Object.keys(this).forEach((key) => {
      if (typeof this[key] == 'object') {
        //@ts-ignore
        target.push({
          value: key,
          label: this[key].name,
        });
      }
    });
    return target;
  },
};

/*
 * 新的装修连接选择器-供应链端
 * */
export const new_supplier_diy_link_type = {
  '': {
    name: '无操作',
    need_link_value: false,
  },
  url: {
    name: '链接地址',
    need_link_value: true,
    require_select_modal: false,
  },
  keyword: {
    name: '关键字',
    need_link_value: true,
    require_select_modal: false,
  },
  goods: {
    name: '商品',
    need_link_value: true,
    require_select_modal: true,
  },
  category: {
    name: '商品分类',
    need_link_value: true,
    require_select_modal: true,
  },
  topic: {
    name: '专题',
    need_link_value: true,
    require_select_modal: true,
  },
  brand_home: {
    name: '品牌列表',
    need_link_value: false,
    require_select_modal: false,
  },
  voucher_center: {
    name: '领券中心',
    need_link_value: false,
    require_select_modal: false,
  },
  store: {
    name: '店铺',
    need_link_value: true,
    require_select_modal: true,
  },
  store_list: {
    name: '店铺街',
    need_link_value: false,
    require_select_modal: false,
  },
  formatOptions() {
    let target = [];
    Object.keys(this).forEach((key) => {
      if (typeof this[key] == 'object') {
        //@ts-ignore
        target.push({
          value: key,
          label: this[key].name,
        });
      }
    });
    return target;
  },
};

/**
 * 对象数组实现深拷贝
 * @param {array} origion_data  源对象数组
 */
export function getSldCopyData(origion_data) {
  let new_data: any = [];
  if (origion_data.length > 0) {
    for (let i in origion_data) {
      if (typeof origion_data[i] == 'object') {
        new_data.push({ ...origion_data[i] });
      } else {
        new_data.push(origion_data[i]);
      }
    }
  }
  return new_data;
}

/*
 * 装修连接选择器-移动端独有
 * */

export const new_m_diy_link_type = {
  ...new_diy_link_type,
  seckill: {
    name: '秒杀首页',
    need_link_value: true,
    require_select_modal: true,
  },
  spell_group: {
    name: '拼团首页',
    need_link_value: false,
    require_select_modal: false,
  },
  ladder_group: {
    name: '阶梯团首页',
    need_link_value: false,
    require_select_modal: false,
  },
  presale: {
    name: '预售首页',
    need_link_value: false,
    require_select_modal: false,
  },
  svideo_center: {
    name: '短视频中心',
    need_link_value: false,
    require_select_modal: false,
  },
  
  sign_center: {
    name: '签到中心',
    need_link_value: false,
    require_select_modal: false,
  },
  // spreader-3-start
  spreader_center: {
    name: '推手中心',
    need_link_value: false,
    require_select_modal: false,
  },
  // spreader-3-end
  rank: {
    name: '排行榜中心',
    need_link_value: false,
    require_select_modal: false,
  },
  draw: {
    name: '抽奖活动',
    need_link_value: true,
    require_select_modal: true,
  },
  formatOptions() {
    let target = [];
    Object.keys(this).forEach((key) => {
      if (typeof this[key] == 'object') {
        //@ts-ignore
        target.push({
          value: key,
          label: this[key].name,
        });
      }
    });
    return target;
  },
};

/*
 * 装修连接选择器-积分商城独有
 * */

export const new_m_point_diy_link_type = {
  '': {
    name: '无操作',
    need_link_value: false,
  },
  url: {
    name: '链接地址',
    need_link_value: true,
    require_select_modal: false,
  },
  keyword: {
    name: '关键字',
    need_link_value: true,
    require_select_modal: false,
  },
  goods: {
    name: '商品',
    need_link_value: true,
    require_select_modal: true,
  },
  category: {
    name: '积分标签',
    need_link_value: true,
    require_select_modal: true,
  },
  formatOptions() {
    let target = [];
    Object.keys(this).forEach((key) => {
      if (typeof this[key] == 'object') {
        //@ts-ignore
        target.push({
          value: key,
          label: this[key].name,
        });
      }
    });
    return target;
  },
};

/**
 * 上传图片限制，限制的大小是服务器的配置
 * */
export function sldBeforeUpload(file, fileList, limit = uploadLimit, accept) {
  if (accept != undefined && accept != null && accept) {
    //校验文件格式类型
    let accept_list = accept
      .replaceAll(' ', '')
      .split(',')
      .filter((item) => item && item.trim());
    let file_type = file.name ? '.' + file.name.split('.').at(-1) : '';
    if (accept_list.length > 0 && file_type != '' && accept_list.indexOf(file_type) == -1) {
      failTip('上传文件格式有误');
      return false;
    }
  }
  if (file.size != undefined && file.size > 1024 * 1024 * limit) {
    failTip(`上传文件过大，请上传小于 ${limit} M的图片`);
    return false;
  }
}

/*
 * 笛卡尔积返回商品的SKU
 * 参数的格式：二维数组，eg:[[1, 2, 3], ['a', 'b', 'c']]
 * */
export function calcDescartes(array) {
  if (array.length < 2) return array[0] || [];
  return [].reduce.call(array, function (col, set) {
    var res = [];
    col.forEach(function (c) {
      set.forEach(function (s) {
        var t = [].concat(Array.isArray(c) ? c : [c]);
        t.push(s);
        res.push(t);
      });
    });
    return res;
  });
}

/*
 * 数字格式化，增加单位万，减少数据长度，返回数据如：1.5万或者8,999
 * @param {Number} num 要格式化的数据
 * */
export function formatNum(num, toFixedNum = 0, color = '#333') {
  let target = num * 1;
  let unit = '';
  if (target) {
    if (target < 10000) {
      target = target.toFixed(toFixedNum);
    } else {
      target = (target / 10000).toFixed(toFixedNum) * 1;
      unit = '万';
    }
    let regExpInfo = /(\d{1,3})(?=(\d{3})+(?:$|\.))/g;
    target = target.toString().replace(regExpInfo, '$1,');
  } else {
    target = num;
  }
  return { target, color, unit };
}

/*
 * 关闭当前浏览器窗口
 * */
export function pageClose() {
  if (window && navigator.userAgent.indexOf('MSIE') > 0) {
    if (navigator.userAgent.indexOf('MSIE 6.0') > 0) {
      window.opener = null;
      window.close();
    } else {
      window.open('', '_top');
      window.top.close();
    }
  } else if (window && navigator.userAgent.indexOf('Firefox') > 0) {
    window.location.href = 'about:blank ';
  } else if (window) {
    window.opener = null;
    window.open('', '_self', '');
    window.close();
  }
}

//短视频空图片
export function video_defalut_img() {
  return {
    one: new URL('/@/assets/images/m_diy_img/svideo/center_empty_one.png', import.meta.url).href,
    two: new URL('/@/assets/images/m_diy_img/svideo/center_empty_two.png', import.meta.url).href,
    three: new URL('/@/assets/images/m_diy_img/svideo/center_empty_three.png', import.meta.url)
      .href,
    four: new URL('/@/assets/images/m_diy_img/svideo/center_empty_four.png', import.meta.url).href,
    five: new URL('/@/assets/images/m_diy_img/svideo/center_empty_five.png', import.meta.url).href,
  };
}

//直播空图片
export function live_defalut_img() {
  return {
    one: new URL('/@/assets/images/m_diy_img/live/center_empty_one.png', import.meta.url).href,
    two: new URL('/@/assets/images/m_diy_img/live/center_empty_two.png', import.meta.url).href,
  };
}

//直播展示风格
export const sld_m_diy_live_style = [
  {
    img: new URL('/@/assets/images/m_diy_img/live/show_style1.png', import.meta.url).href,
    sele_style: 'one',
  },
  {
    img: new URL('/@/assets/images/m_diy_img/live/show_style2.png', import.meta.url).href,
    sele_style: 'two',
  },
];

//短视频展示风格
export const sld_m_diy_svideo_style = [
  {
    img: new URL('/@/assets/images/m_diy_img/svideo/show_style1.png', import.meta.url).href,
    sele_style: 'one',
  },
  {
    img: new URL('/@/assets/images/m_diy_img/svideo/show_style2.png', import.meta.url).href,
    sele_style: 'two',
  },
  {
    img: new URL('/@/assets/images/m_diy_img/svideo/show_style3.png', import.meta.url).href,
    sele_style: 'three',
  },
  {
    img: new URL('/@/assets/images/m_diy_img/svideo/show_style4.png', import.meta.url).href,
    sele_style: 'four',
  },
  {
    img: new URL('/@/assets/images/m_diy_img/svideo/show_style5.png', import.meta.url).href,
    sele_style: 'five',
  },
];

//公告风格
export const sld_m_diy_notice_style = [
  {
    img: new URL('/@/assets/images/m_diy_img/notice/show_style1.png', import.meta.url).href,
    left_img: new URL('/@/assets/images/m_diy_img/notice/left_icon_1.png', import.meta.url).href,
    sele_style: 'one',
  },
  {
    img: new URL('/@/assets/images/m_diy_img/notice/show_style2.png', import.meta.url).href,
    left_img: new URL('/@/assets/images/m_diy_img/notice/left_icon_2.png', import.meta.url).href,
    sele_style: 'two',
  },
];

//TAB切换购物车展示图标
export function show_cart_icon_data() {
  return [
    {
      icon: 'gouwuche',
      width: 20,
      type: 1,
    },
    {
      icon: 'shoppingCart',
      width: 18,
      type: 2,
    },
    {
      icon: 'add-sy',
      width: 22,
      type: 3,
    },
    {
      icon: 'ziyuan110',
      width: 22,
      type: 4,
    },
  ];
}

/*
 * 返回一个数字的整数和小数
 * number 需要处理的数据
 * type: 要获取的数据 int 整数  decimal 小数
 */
export function getPartNumber(number, type) {
  let target = '';
  if (number == undefined) {
    return false;
  }
  number = number.toString();
  if (type == 'int') {
    target = number.split('.')[0];
  } else if (type == 'decimal') {
    target = number.split('.')[1] != undefined ? '.' + number.split('.')[1] : '.00';
    if (target.length < 3) {
      target += '0';
    }
  }
  return target;
}

//TAB切换购物车图标
export function cart_icon_data() {
  return [
    {
      icon: 'gouwuche',
      width: 23,
      type: 1,
      padding: 5,
    },
    {
      icon: 'shoppingCart',
      width: 23,
      type: 2,
      padding: 5,
    },
    {
      icon: 'add-sy',
      width: 23,
      type: 3,
      padding: 5,
    },
    {
      icon: 'ziyuan110',
      width: 23,
      type: 4,
      padding: 5,
    },
    {
      icon: 'jinzhi',
      width: 19,
      type: 5,
      padding: 7,
    },
  ];
}

//图片组合
export const sld_m_diy_tpzh_style = [
  {
    img: new URL('/@/assets/images/m_diy_img/tpzh/show_style0.png', import.meta.url).href,
    sele_style: 0,
  },
  {
    img: new URL('/@/assets/images/m_diy_img/tpzh/show_style1.png', import.meta.url).href,
    sele_style: 1,
  },
  {
    img: new URL('/@/assets/images/m_diy_img/tpzh/show_style2.png', import.meta.url).href,
    sele_style: 2,
  },
  {
    img: new URL('/@/assets/images/m_diy_img/tpzh/show_style3.png', import.meta.url).href,
    sele_style: 3,
  },
  {
    img: new URL('/@/assets/images/m_diy_img/tpzh/show_style4.png', import.meta.url).href,
    sele_style: 4,
  },
  {
    img: new URL('/@/assets/images/m_diy_img/tpzh/show_style5.png', import.meta.url).href,
    sele_style: 5,
  },
  {
    img: new URL('/@/assets/images/m_diy_img/tpzh/show_style6.png', import.meta.url).href,
    sele_style: 6,
  },
  {
    img: new URL('/@/assets/images/m_diy_img/tpzh/show_style7.png', import.meta.url).href,
    sele_style: 7,
  },
  {
    img: new URL('/@/assets/images/m_diy_img/tpzh/show_style8.png', import.meta.url).href,
    sele_style: 8,
  },
];

// 生成本地路径
export function newImg(img) {
  return new URL(img, import.meta.url).href;
}

//判断数据是否是空对象
export function isEmptyObject(data) {
  if (Object.getOwnPropertyNames(data).length === 0) {
    return true; //空对象
  } else {
    return false; //非空对象
  }
}

//设置浏览器ico
export function setDocumentIcon(data) {
  if (data) {
    localStorage.setItem('system_ico_image', data);
  } else {
    localStorage.removeItem('system_ico_image');
  }
  var link = document.querySelector("link[rel*='icon']") || document.createElement('link');
  link.type = 'image/x-icon';
  link.rel = 'shortcut icon';
  link.href = data || location.origin + '/favicon.png';
  document.getElementsByTagName('head')[0].appendChild(link);
}
