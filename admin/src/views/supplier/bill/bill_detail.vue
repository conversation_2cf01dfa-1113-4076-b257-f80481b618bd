<template>
  <PageWrapper class="common_description bill_detail">
    <SldComHeader :type="1" :title="'结算详情'" :back="true" />
    <div style="width: 100%; height: 5px"></div>

    <div class="flex_row_center_start progress">
      <template v-for="(item, index) in bill_progress_data" :key="index">
        <div class="flex_column_start_center item">
          <div class="flex_row_center_center top">
            <span class="left_line"></span>
            <img :src="item.img" />
            <span class="right_line"></span>
          </div>
          <span class="state">{{ item.state }}</span>
          <span class="time">{{ item.time }}</span>
        </div>
      </template>
    </div>
    <div v-if="bill_detail.state == 1" class="flex_column_start_center state_part">
      <span class="title">等待供应商确认</span>
    </div>
    <div v-else-if="bill_detail.state == 2" class="flex_column_start_center state_part">
      <span class="title">等待平台审核</span>
      <Popconfirm title="确认审核通过该结算单？" @confirm="handlePass">
        <span class="invoice_btn">审核结算单</span>
      </Popconfirm>
    </div>
    <div v-else-if="bill_detail.state == 3" class="flex_column_start_center state_part">
      <span class="title">等待平台结算</span>
      <div @click="handlePay" class="invoice_btn">确认打款</div>
    </div>
    <div v-else-if="bill_detail.state == 4" class="flex_column_start_center state_part">
      <span class="title">结算完成</span>
    </div>
    <div class="bill_detail_title">结算信息</div>
    <div class="bill_amount">
      <img class="bill_amount_img" :src="detail_total_icon" />
      <span class="bill_amount_total"
        >&nbsp;结算金额￥{{ bill_detail.settleAmount || 0 }} = &nbsp;</span
      >
      <span class="bill_amount_detail">
        订单金额￥{{ bill_detail.orderAmount || 0 }} - 平台佣金￥{{ bill_detail.commission || 0 }} +
        退还佣金￥{{ bill_detail.refundCommission || 0 }} - 退单金额￥{{
          bill_detail.refundAmount || 0
        }}
      </span>
    </div>
    <Description
      v-if="bill_detail.state == 4"
      size="middle"
      title="结算凭证"
      :bordered="true"
      :column="1"
      :data="bill_detail"
      :schema="paymentSchema"
    />
    <Description
      size="middle"
      title="结算信息"
      :bordered="true"
      :column="2"
      :data="bill_detail"
      :schema="infoSchema"
    />
    <Description
      v-if="bill_detail.state != 1"
      size="middle"
      title="结算账号信息"
      :bordered="true"
      :column="2"
      :data="bill_detail"
      :schema="bill_detail.accountType == 1 ? bankSchema : aliSchema"
    />
    <div class="bill_detail_title">结算订单信息</div>
    <BasicTable @register="standardTable" style="padding: 3px">
      <template #headerCell="{ column }">
        <template v-if="column.key == 'compensationAmount'">
          <div class="table_title">
            <span class="table_title_xing">*</span>
            {{ column.customTitle }}
            <span class="table_title_extra">
              <Tooltip placement="bottom">
                <template #title>
                  对于定金预售活动，因商家问题导致无法发货，对会员赔偿的金额
                </template>
                <AliSvgIcon iconName="iconwen" width="14px" height="14px" fillColor="#bfbbba" />
              </Tooltip>
            </span>
          </div>
        </template>
        <template v-else>
          {{ column.customTitle }}
        </template>
      </template>
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.key == 'action'">
          <span class="common_page_edit" @click="checkOrer(record.orderSn)">查看</span>
        </template>
        <template v-else-if="column.key">
          {{ text !== undefined && text !== null ? text : '--' }}
        </template>
      </template>
    </BasicTable>
    <SldModal
      :width="width"
      :title="title"
      :visible="visible"
      :content="content"
      :confirmBtnLoading="confirmBtnLoading"
      :showFoot="true"
      @cancle-event="handleCancle"
      @confirm-event="handleConfirm"
    />
  </PageWrapper>
</template>
<script>
  import { defineComponent, ref, h, onMounted } from 'vue';
  import { Tooltip, Popconfirm } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { Description } from '/@/components/Description/index';
  import { BasicTable, useTable } from '/@/components/Table';
  import { getBillDetail, approvedBill, confirmPaymentBill } from '/@/api/supplier/bill';
  import { useGo } from '/@/hooks/web/usePage';
  import { useRoute } from 'vue-router';
  import SldModal from '@/components/SldModal/index.vue';
  import { sucTip, failTip, detail_total_icon } from '/@/utils/utils';

  export default defineComponent({
    components: {
      SldComHeader,
      Description,
      BasicTable,
      Tooltip,
      Popconfirm,
      SldModal,
    },
    setup() {
      const go = useGo();
      const route = useRoute();
      const bill_detail = ref({});
      const bill_progress_data = ref([
        {
          code: 'createBill',
          state: '生成结算单',
          time: '',
          state_code: 1,
          cur_state: 'cur',
          img:''
        },
        {
          code: 'storeConfirm',
          state: '供应商确认',
          time: '',
          state_code: 2,
          cur_state: 'no',
          img:''
        },
        {
          code: 'systemCheck',
          state: '平台审核',
          time: '',
          state_code: 3,
          img:'',
          cur_state: 'no',
        },
        {
          code: 'finish',
          state: '结算完成',
          time: '',
          state_code: 4,
          img:"",
          cur_state: 'no',
        },
      ]);
      const paymentSchema = [
        {
          field: 'paymentRemark',
          label: '打款备注',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'paymentEvidence',
          label: '结算凭证',
          render: (val) => {
            return h('div', { class: 'form_list_img_item' }, [
              h('img', { src: val, class: 'form_list_img_item_img' }),
              h('div', { class: 'form_list_img_item_right' }, [
                h('div', { class: 'form_list_img_item_pre' }, [
                  h('div', { class: 'form_list_img_item_pre_arrow' }),
                  h('img', { src: val, class: 'form_list_img_item_pre_img' }),
                ]),
              ]),
            ]);
          },
        },
      ];
      const infoSchema = [
        {
          field: 'billSn',
          label: '结算单号',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'startTime',
          label: '结算起止时间',
          render: function (val, data) {
            return (
              (val ? val : data.endTime ? '' : '--') +
              (data.endTime ? (val ? ' ~ ' : '') + data.endTime : '')
            );
          },
        },
        {
          field: 'storeName',
          label: '供应商名称',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'contactName',
          label: '供应商联系人',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'contactPhone',
          label: '供应商联系电话',
          render: function (val) {
            return val ? val : '--';
          },
        },
      ];
      const bankSchema = [
        {
          field: 'bankAccountName',
          label: '银行开户名',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'bankAccountNumber',
          label: '公司银行账号',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'bankBranch',
          label: '开户银行',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'addressAll',
          label: '开户行所在地',
          render: function (val) {
            return val ? val : '--';
          },
        },
      ];
      const aliSchema = [
        {
          field: 'alipayAccount',
          label: '支付宝账号',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'alipayName',
          label: '支付宝姓名',
          render: function (val) {
            return val ? val : '--';
          },
        },
      ];

      const rowKey = ref('bindId');
      const columns = [
        {
          title: '订单号',
          dataIndex: 'orderSn',
          width: 150,
        },
        {
          title: '订单金额(元)',
          dataIndex: 'orderAmount',
          width: 120,
        },
        {
          title: '佣金(元)',
          dataIndex: 'commission',
          width: 120,
        },
        {
          title: '退还佣金(元)',
          dataIndex: 'refundCommission',
          width: 120,
        },
        {
          title: '退单金额(元)',
          dataIndex: 'refundAmount',
          width: 120,
        }, 
        {
          title: '下单日期',
          dataIndex: 'createTime',
          width: 160,
        },
        {
          title: '完成日期',
          dataIndex: 'finishTime',
          width: 160,
        },
      ];
      const dataSource = ref([]);
      const [standardTable] = useTable({
        columns: columns,
        dataSource: dataSource,
        pagination: false,
        actionColumn: {
          title: '操作',
          dataIndex: 'action',
          width: 100,
        },
        bordered: true,
        striped: false,
        showIndexColumn: true,
        canResize: false,
      });
      const click_event = ref(false);

      const width = ref(550);
      const title = ref('确认打款');
      const visible = ref(false);
      const content = ref([]);
      const confirmBtnLoading = ref(false);
      const operate_data = ref([
        {
          type: 'textarea',
          label: `打款备注`,
          name: 'paymentRemark',
          placeholder: `请输入汇款单号、支付方式等付款凭证信息，最多输入200字`,
          extra: '',
          maxLength: 200,
          callback: true,
        },

        {
          type: 'upload_img_upload',
          label: '上传文件',
          name: 'paymentEvidence',
          extra: '',
          initialValue: [],
          upload_name: 'file',
          upload_url: 'v3/oss/admin/upload?source=setting',
          fileList: [],
          limit: 20,
          accept: ' .jpg, .jpeg, .png, .gif',
          rules: [
            {
              required: true,
              message: '请上传打款凭证',
            },
          ],
          callback: true,
        },
      ]);

      onMounted(() => {
        get_bill_detail();
      });

      // 生成本地路径
      const newImg = (img) => {
        return new URL(img, import.meta.url).href;
      };

      //获取结算详情
      const get_bill_detail = async () => {
        const res = await getBillDetail({ billId: route.query.id });
        if (res.state == 200) {
          bill_detail.value = res.data;
          for (let pro in bill_progress_data.value) {
            if (pro < res.data.logList.length) {
              bill_progress_data.value[pro].time = res.data.logList[pro].createTime;
            }
            if (res.data.state < bill_progress_data.value[pro].state_code) {
              bill_progress_data.value[pro].cur_state = 'no';
              if(bill_progress_data.value[pro].state_code==1){
                bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/1_no.png', import.meta.url).href;
              }else if(bill_progress_data.value[pro].state_code==2){
                bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/2_no.png', import.meta.url).href;
              }else if(bill_progress_data.value[pro].state_code==3){
                bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/3_no.png', import.meta.url).href;
              }else if(bill_progress_data.value[pro].state_code==4){
                bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/4_no.png', import.meta.url).href;
              }

            } else if (res.data.state == bill_progress_data.value[pro].state_code) {
              bill_progress_data.value[pro].cur_state = 'cur';
              if(bill_progress_data.value[pro].state_code==1){
                bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/1_cur.png', import.meta.url).href;
              }else if(bill_progress_data.value[pro].state_code==2){
                bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/2_cur.png', import.meta.url).href;
              }else if(bill_progress_data.value[pro].state_code==3){
                bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/3_cur.png', import.meta.url).href;
              }else if(bill_progress_data.value[pro].state_code==4){
                bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/4_cur.png', import.meta.url).href;
              }

            } else {
              bill_progress_data.value[pro].cur_state = 'pass';
              if(bill_progress_data.value[pro].state_code==1){
                bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/1_pass.png', import.meta.url).href;
              }else if(bill_progress_data.value[pro].state_code==2){
                bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/2_pass.png', import.meta.url).href;
              }else if(bill_progress_data.value[pro].state_code==3){
                bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/3_pass.png', import.meta.url).href;
              }else if(bill_progress_data.value[pro].state_code==4){
                bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/4_pass.png', import.meta.url).href;
              }

            }
          }
          dataSource.value = res.data.orderList;
        } else {
          failTip(res.msg);
        }
      };

      function checkOrer(orderSn) {
        go(`/supplier_order/order_lists_to_detail?orderSn=${orderSn}`);
      }

      //审核结算单
      const handlePass = async () => {
        if (click_event.value) return;
        click_event.value = true;
        const res = await approvedBill({ billId: route.query.id });
        click_event.value = false;
        if (res.state == 200) {
          sucTip(res.msg);
          get_bill_detail();
        } else {
          failTip(res.msg);
        }
      };

      //确认打款
      function handlePay() {
        content.value = JSON.parse(JSON.stringify(operate_data.value));
        visible.value = true;
      }

      function handleCancle() {
        visible.value = false;
      }

      const handleConfirm = async (val) => {
        val.billId = route.query.id;
        val.paymentEvidence = val.paymentEvidence[0].response.data.path;
        if (click_event.value) return;
        click_event.value = true;
        const res = await confirmPaymentBill(val);
        click_event.value = false;
        if (res.state == 200) {
          sucTip(res.msg);
          visible.value = false;
          get_bill_detail();
        } else {
          failTip(res.msg);
        }
      };

      return {
        newImg,
        bill_detail,
        bill_progress_data,
        paymentSchema,
        infoSchema,
        bankSchema,
        aliSchema,
        rowKey,
        standardTable,
        click_event,
        checkOrer,
        handlePass,
        handlePay,
        width,
        title,
        visible,
        content,
        confirmBtnLoading,
        operate_data,
        handleCancle,
        handleConfirm,
        detail_total_icon,
      };
    },
  });
</script>
<style lang="less" scoped>
  .bill_detail {
    .slodon-basic-table{
      height: auto !important;
    }

    .bill_detail_title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 32px;
      margin-bottom: 5px;
      padding-left: 14px;
      color: rgb(0 0 0 / 85%);
      font-size: 14px;
      font-weight: 500;
      line-height: 24px;
      cursor: default;
    }

    .bill_amount {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      height: 60px;
      margin: 10px;
      border-radius: 6px;
      background: #fff;
      box-shadow: 0 0 8px 0 rgb(153 153 153 / 5%), 0 0 8px 0 rgb(153 153 153 / 5%);
      font-size: 16px;

      .bill_amount_img {
        width: 36px;
        height: 36px;
        margin-right: 15px;
        margin-left: 20px;
      }

      .bill_amount_total {
        color: #ff2b4e;
        white-space: nowrap;
      }

      .bill_amount_detail {
        color: #333;
      }
    }

    .table_title {
      display: flex;
      align-items: center;
      justify-content: center;

      .table_title_xing {
        margin-right: 4px;
        color: #f00;
      }

      .table_title_extra {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 4px;
        cursor: pointer;
      }
    }

    .progress {
      margin-top: 50px;

      .item {
        .top {
          position: relative;
          width: 215px;

          img {
            width: 75px;
            height: 75px;
          }

          .left_line,
          .right_line {
            content: '';
            position: absolute;
            top: 50%;
            width: 50px;
            height: 0;
            border-top: 1px solid rgb(255 109 31 / 30%);
          }

          .left_line {
            left: 0;
          }

          .right_line {
            right: 0;
          }
        }

        .state {
          margin-top: 10px;
          font-size: 14px;
        }

        .time {
          font-size: 14px;
        }
      }

      .item.cur {
        .state {
          color: #fb6f1e;
        }

        .time {
          color: rgb(251 111 30 / 50%);
        }

        .left_line,
        .right_line {
          border-color: #fb6f1e;
        }
      }

      .item.no {
        .state {
          color: #999;
        }

        .left_line,
        .right_line {
          border-color: #eee;
        }
      }

      .item.pass {
        .state {
          color: rgb(251 111 30 / 50%);
        }

        .time {
          color: rgb(251 111 30 / 30%);
        }

        .left_line,
        .right_line {
          border-color: rgb(251 111 30 / 30%);
        }
      }
    }

    .state_part {
      margin-top: 50px;
      margin-bottom: 40px;

      .title {
        color: #333;
        font-size: 26px;
      }

      .tip {
        color: #999;
        font-size: 14px;
      }
    }

    .agree_btn {
      width: 120px;
      height: 36px;
      margin-top: 15px;
      border: 1px solid #fb6f1e;
      border-radius: 3px;
      color: #fb6f1e;
      font-size: 14px;
      line-height: 36px;
      text-align: center;
    }

    .invoice_btn {
      width: 120px;
      height: 36px;
      margin-top: 15px;
      border-radius: 3px;
      background: #fb6f1e;
      color: rgb(255 255 255 / 100%);
      line-height: 36px;
      text-align: center;
      cursor: pointer;
    }
  }
</style>
