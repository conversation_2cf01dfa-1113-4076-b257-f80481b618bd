<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <SldComHeader
        :type="2"
        :title="'供应商结算'"
        :tipTitle="'温馨提示'"
        :tipData="[
          '计算公式：结算金额 = 订单金额 - 平台佣金 + 退还佣金 - 退单金额',
          '结算流程：生成结算单 > 供应商确认 > 平台审核 > 结算完成',
        ]"
        :clickFlag="true"
        @handle-toggle-tip="handleToggleTip"
      />
      <BasicTable @register="standardTable" style="padding: 0">
        <template #tableTitle>
          <div class="toolbar">
            <div class="toolbar_btn" @click="handleClick(null, 'export')">
              <AliSvgIcon iconName="iconziyuan23" width="15px" height="15px" fillColor="#ff5908" />
              <span>结算单导出</span>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.key == 'action'">
            <span class="common_page_edit" @click="handleClick(record, 'view')">查看</span>
          </template>
          <template v-else-if="column.key == 'startTime'">
            <div style="display: flex; flex-direction: column">
              <span>{{ text }}</span>
              <span>~</span>
              <span>{{ record.endTime }}</span>
            </div>
          </template>
          <template v-else-if="column.key">
            {{ text !== undefined && text !== null ? text : '--' }}
          </template>
        </template>
      </BasicTable>
    </div>
  </div>
</template>
<script>
  import { defineComponent, ref } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import { getBillList, exportBillList } from '/@/api/supplier/bill';
  import { useGo } from '/@/hooks/web/usePage';
  import { sucTip, failTip } from '/@/utils/utils';

  export default defineComponent({
    components: {
      SldComHeader,
      BasicTable,
    },
    setup() {
      const go = useGo();
      const checkedKeys = ref([]); //表格的键值声明，非必填，主要用于勾选功能中的数据处理
      const rowKey = ref('billId'); //表格开启选择功能时，需要声明选中数据变量
      const operate_type = ref('');
      const click_event = ref(false);

      const [standardTable, { reload, redoHeight, getPaginationRef }] = useTable({
        api: (arg) => getBillList({ ...arg }),
        fetchSetting: {
          pageField: 'current',
          sizeField: 'pageSize',
          listField: 'data.list',
          totalField: 'data.pagination.total',
        },
        columns: [
          {
            title: '供应商名称',
            dataIndex: 'storeName',
            width: 150,
          },
          {
            title: '结算单号',
            dataIndex: 'billSn',
            width: 140,
          },
          {
            title: '出账时间',
            dataIndex: 'createTime',
            width: 160,
          },
          {
            title: '结算日',
            dataIndex: 'startTime',
            width: 160,
          },
          {
            title: '订单金额(元)',
            dataIndex: 'orderAmount',
            width: 110,
          },
          {
            title: '佣金(元)',
            dataIndex: 'commission',
            width: 110,
          },
          {
            title: '退单金额(元)',
            dataIndex: 'refundAmount',
            width: 110,
          },
          {
            title: '退还佣金(元)',
            dataIndex: 'refundCommission',
            width: 110,
          },
          {
            title: '应结金额(元)',
            dataIndex: 'settleAmount',
            width: 110,
          },
          {
            title: '结算状态',
            dataIndex: 'stateValue',
            width: 100,
          },
        ],
        actionColumn: {
          width: 100,
          title: '操作',
          dataIndex: 'action',
        },
        useSearchForm: true,
        formConfig: {
          schemas: [
            {
              field: 'storeName',
              component: 'Input',
              colProps: { span: 6, style: 'width:260px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                maxlength: 20,
                placeholder: '请输入供应商名称',
                size: 'default',
              },
              label: '供应商名称',
              labelWidth: 80,
            },
            {
              field: 'state',
              component: 'Select',
              colProps: { span: 6, style: 'width:240px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                placeholder: '请选择结算状态',
                options: [
                  { key: 0, label: '全部', value: '' },
                  { key: 1, label: '待确认', value: '1' },
                  { key: 2, label: '待审核', value: '2' },
                  { key: 3, label: '待结算', value: '3' },
                  { key: 4, label: '结算完成', value: '4' },
                ],
                size: 'default',
              },
              label: '结算状态',
              labelWidth: 80,
            },
            {
              field: '[startTime,endTime]',
              component: 'RangePicker',
              colProps: { span: 6, style: 'width:300px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                size: 'default',
                showTime: true,
              },
              label: '结算时间',
              labelWidth: 80,
            },
          ],
        },
        bordered: true,
        striped: false,
        showIndexColumn: true,
        rowKey: rowKey,
        rowSelection: {
          type: 'checkbox',
          selectedRowKeys: checkedKeys,
          onSelect: onSelect,
          onSelectAll: onSelectAll,
        },
        clickToRowSelect: false,
      });

      //表格的单行选中回调事件
      function onSelect(record, selected) {
        if (selected) {
          checkedKeys.value = [...checkedKeys.value, record[rowKey.value]];
        } else {
          checkedKeys.value = checkedKeys.value.filter((item) => item != record[rowKey.value]);
        }
      }

      //表格的全选回调事件
      function onSelectAll(selected, selectedRows, changeRows) {
        const changeIds = changeRows.map((item) => item[rowKey.value]);
        if (selected) {
          checkedKeys.value = [...checkedKeys.value, ...changeIds];
        } else {
          checkedKeys.value = checkedKeys.value.filter((item) => {
            return !changeIds.includes(item);
          });
        }
      }

      //表格点击回调事件
      function handleClick(item, type) {
        if (click_event.value) return;
        operate_type.value = type;
        if (type == 'view') {
          go(`/supplier_bill/lists_to_detail?id=${item.billId}`);
        } else if (type == 'export') {
          let param = {};
          param.current = getPaginationRef().current;
          param.pageSize = getPaginationRef().pageSize;
          param.fileName = '结算单导出';
          if (checkedKeys.value.length > 0) {
            param.ids = checkedKeys.value.join(',');
          }
          click_event.value = true;
          operate_role(param);
        }
      }

      //操作
      const operate_role = async (params) => {
        let res = {};
        if (operate_type.value == 'export') {
          res = await exportBillList(params);
        }
        click_event.value = false;
        if (res.state == 200) {
          reload();
        }
      };

      const handleToggleTip = ()=> {
        redoHeight()
      }

      return {
        standardTable,
        go,
        operate_type,
        click_event,
        rowKey,
        checkedKeys,
        handleClick,
        handleToggleTip
      };
    },
  });
</script>
<style lang="less" scoped></style>
