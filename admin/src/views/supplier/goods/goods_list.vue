<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader :type="3"
        :clickFlag="true" :title="'商品列表'" @handle-toggle-tip="handleToggleTip" />
      <a-tabs v-model:activeKey="tabIndex" type="card" @change="handleChange">
        <a-tab-pane :key="1" tab="在售列表" />
        <a-tab-pane :key="2" tab="待审核列表" />
        <a-tab-pane :key="3" tab="仓库中列表" />
        <a-tab-pane :key="4" tab="违规下架列表" />
      </a-tabs>
      <div class="section_padding_tab_top"></div>
      <div :style="{ display: tabIndex == 1 ? 'block' : 'none' }">
        <ShowMoreHelpTip style="margin-bottom: 13px;" :marginTop="0" :tipData="tipData[0]" v-if="sld_show_tip"></ShowMoreHelpTip>
        <BasicTable @register="onlineStandardTable" style="padding: 0">
          <template #tableTitle>
            <div class="toolbar">
              <div class="toolbar_btn" @click="handleClick(null, 'lockOff')">
                <AliSvgIcon iconName="iconziyuan31" width="15px" height="15px" fillColor="#f9a006" />
                <span>批量违规下架</span>
              </div>
            </div>
          </template>
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.dataIndex == 'action'">
              <span class="common_page_edit" @click="handleClick(record, 'detail')">查看详情</span>
              <span class="common_page_edit" @click="handleClick(record, 'lockOff')">违规下架</span>
            </template>
            <template v-else-if="column.dataIndex == 'wholesalePrice'">
                <div v-if="record.isRequireInquire == 1 || !record.ladderPrice">
                    {{  text||text==0 ? '￥'+Number(text).toFixed(2) : '--' }}
                </div>
                <div v-else>
                    <template v-for="(item,index) in record.ladderPrice.split(',')" :key="index">
                    <div>¥{{ item.split('-')[1] ? Number(item.split('-')[1]).toFixed(2) : 0 }}(≥{{ item.split('-')[0] }})</div>
                    </template>
                </div>
            </template>
            <template v-else-if="column.dataIndex == 'mainImage'">
              <div class="goods_list_mainIamge">
                <Popover placement="right">
                  <template #content>
                    <div class="goods_list_mainIamge_pop">
                      <img :src="text" />
                    </div>
                  </template>
                  <div
                    class="goods_list_leftImage"
                    :style="{ backgroundImage: `url('${text}')` }"
                  ></div>
                </Popover>
                <div class="goods_list_rightInfo">
                  <div class="goods_list_goodsname">{{ record.goodsName }}</div>
                  <div class="goods_list_extraninfo">
                    <div v-if="record.categoryPath">{{ record.categoryPath }}</div>
                    <div v-if="record.productCode">商品编码：{{ record.productCode }}</div>
                  </div>
                </div>
              </div>
            </template>
            <template v-else-if="column.dataIndex == 'actualSales'">
              {{ text }}/{{ record.virtualSales }}
            </template>
            <template v-else-if="column.dataIndex=='saleModel'">
              {{ text==3?'代发、批发':text==2?'批发':'代发' }}
            </template>
            <template v-else-if="column.dataIndex=='marketPrice' || column.dataIndex=='goodsPrice'||column.dataIndex=='retailPrice'">
              {{  text ? '￥' + text : '--'}}
            </template>
            <template v-else-if="column.dataIndex">
              {{ text !== undefined && text !== null ? text : '--' }}
            </template>
          </template>
        </BasicTable>
      </div>
      <div :style="{ display: tabIndex == 2 ? 'block' : 'none' }">
        <ShowMoreHelpTip :marginTop="0" style="margin-bottom: 13px;" :tipData="tipData[1]" v-if="sld_show_tip"></ShowMoreHelpTip>
        <BasicTable @register="authStandardTable" style="padding: 0">
          <template #tableTitle>
            <div class="toolbar">
              <div v-if="checkedKeys.length == 0" class="toolbar_btn"  @click="handleClick(null, 'pass')">
                <AliSvgIcon
                  iconName="iconshenhetongguo1"
                  width="15px"
                  height="15px"
                  fillColor="#0fb39a"
                />
                <span style="margin-left: 3px;">审核通过</span>
              </div>
              <Popconfirm v-else title="确认审核通过选中的商品吗？" @confirm="handleClick(null, 'pass')">
                <div class="toolbar_btn">
                  <AliSvgIcon
                    iconName="iconshenhetongguo1"
                    width="15px"
                    height="15px"
                    fillColor="#0fb39a"
                  />
                  <span style="margin-left: 3px;">审核通过</span>
                </div>
              </Popconfirm>
              <div class="toolbar_btn" @click="handleClick(null, 'refuse')">
                <AliSvgIcon
                  iconName="iconshenhejujue1"
                  width="15px"
                  height="15px"
                  fillColor="#fa0920"
                />
                <span>审核拒绝</span>
              </div>
            </div>
          </template>
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.dataIndex == 'action'">
              <div style="display: flex; flex-wrap: wrap; justify-content: center">
                <span class="common_page_edit" @click="handleClick(record, 'detail')">查看详情</span>
                <Popconfirm v-if="record.state != 4" title="确认审核通过该商品吗？" @confirm="handleClick(record, 'pass')">
                  <span class="common_page_edit">审核通过</span>
                </Popconfirm>
                <span v-if="record.state != 4" class="common_page_edit" @click="handleClick(record, 'refuse')">审核拒绝</span>
              </div>
            </template>
            <template v-else-if="column.dataIndex == 'wholesalePrice'">
                <div v-if="record.isRequireInquire == 1 || !record.ladderPrice">
                    {{  text||text==0 ? '￥'+Number(text).toFixed(2) : '--' }}
                </div>
                <div v-else>
                    <template v-for="(item,index) in record.ladderPrice.split(',')" :key="index">
                    <div>¥{{ item.split('-')[1] ? Number(item.split('-')[1]).toFixed(2) : 0 }}(≥{{ item.split('-')[0] }})</div>
                    </template>
                </div>
            </template>
            <template v-else-if="column.dataIndex == 'mainImage'">
              <div class="goods_list_mainIamge">
                <Popover placement="right">
                  <template #content>
                    <div class="goods_list_mainIamge_pop">
                      <img :src="text" />
                    </div>
                  </template>
                  <div
                    class="goods_list_leftImage"
                    :style="{ backgroundImage: `url('${text}')` }"
                  ></div>
                </Popover>
                <div class="goods_list_rightInfo">
                  <div class="goods_list_goodsname">{{ record.goodsName }}</div>
                  <div class="goods_list_extraninfo">
                    <div v-if="record.categoryPath">{{ record.categoryPath }}</div>
                    <div v-if="record.productCode">商品编码：{{ record.productCode }}</div>
                  </div>
                </div>
              </div>
            </template>
            <template v-else-if="column.dataIndex=='marketPrice' || column.dataIndex=='goodsPrice'||column.dataIndex=='retailPrice'">
              {{  text ? '￥' + text : '--'}}
            </template>
            <template v-else-if="column.dataIndex == 'actualSales'">
              {{ text }}/{{ record.virtualSales }}
            </template>
            <template v-else-if="column.dataIndex == 'auditReason'">
              {{ text ? text + (record.auditComment ? ','+record.auditComment : '') : '--' }}
            </template>
            <template v-else-if="column.dataIndex=='saleModel'">
              {{ text==3?'代发、批发':text==2?'批发':'代发' }}
            </template>
            <template v-else-if="column.dataIndex">
              {{ text !== undefined && text !== null ? text : '--' }}
            </template>
          </template>
        </BasicTable>
      </div>
      <div :style="{ display: tabIndex == 3 ? 'block' : 'none' }">
        <BasicTable @register="storageStandardTable" style="padding: 0">
          <template #tableTitle>
            <div class="toolbar">
              <div class="toolbar_btn" @click="handleClick(null, 'lockOff')">
                <AliSvgIcon iconName="iconziyuan31" width="15px" height="15px" fillColor="#f9a006" />
                <span>批量违规下架</span>
              </div>
            </div>
          </template>
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.dataIndex == 'action'">
              <span class="common_page_edit" @click="handleClick(record, 'detail')">查看详情</span>
              <span class="common_page_edit" @click="handleClick(record, 'lockOff')">违规下架</span>
            </template>
            <template v-else-if="column.dataIndex == 'mainImage'">
              <div class="goods_list_mainIamge">
                <Popover placement="right">
                  <template #content>
                    <div class="goods_list_mainIamge_pop">
                      <img :src="text" />
                    </div>
                  </template>
                  <div
                    class="goods_list_leftImage"
                    :style="{ backgroundImage: `url('${text}')` }"
                  ></div>
                </Popover>
                <div class="goods_list_rightInfo">
                  <div class="goods_list_goodsname">{{ record.goodsName }}</div>
                  <div class="goods_list_extraninfo">
                    <div v-if="record.categoryPath">{{ record.categoryPath }}</div>
                    <div v-if="record.productCode">商品编码：{{ record.productCode }}</div>
                  </div>
                </div>
              </div>
            </template>
            <template v-else-if="column.dataIndex == 'wholesalePrice'">
                <div v-if="record.isRequireInquire == 1 || !record.ladderPrice">
                    {{  text||text==0 ? '￥'+Number(text).toFixed(2) : '--' }}
                </div>
                <div v-else>
                    <template v-for="(item,index) in record.ladderPrice.split(',')" :key="index">
                    <div>¥{{ item.split('-')[1] ? Number(item.split('-')[1]).toFixed(2) : 0 }}(≥{{ item.split('-')[0] }})</div>
                    </template>
                </div>
            </template>
            <template v-else-if="column.dataIndex == 'actualSales'">
              {{ text }}/{{ record.virtualSales }}
            </template>
            <template v-else-if="column.dataIndex=='marketPrice' || column.dataIndex=='goodsPrice'||column.dataIndex=='retailPrice'">
              {{  text ? '￥' + text : '--'}}
            </template>
            <template v-else-if="column.dataIndex=='saleModel'">
              {{ text==3?'代发、批发':text==2?'批发':'代发' }}
            </template>
            <template v-else-if="column.dataIndex">
              {{ text !== undefined && text !== null ? text : '--' }}
            </template>
          </template>
        </BasicTable>
      </div>
      <div :style="{ display: tabIndex == 4 ? 'block' : 'none' }">
        <BasicTable @register="sheilfStandardTable" style="padding: 0">
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.dataIndex == 'action'">
              <span class="common_page_edit" @click="handleClick(record, 'detail')">查看详情</span>
            </template>
            <template v-else-if="column.dataIndex == 'mainImage'">
              <div class="goods_list_mainIamge">
                <Popover placement="right">
                  <template #content>
                    <div class="goods_list_mainIamge_pop">
                      <img :src="text" />
                    </div>
                  </template>
                  <div
                    class="goods_list_leftImage"
                    :style="{ backgroundImage: `url('${text}')` }"
                  ></div>
                </Popover>
                <div class="goods_list_rightInfo">
                  <div class="goods_list_goodsname">{{ record.goodsName }}</div>
                  <div class="goods_list_extraninfo">
                    <div v-if="record.categoryPath">{{ record.categoryPath }}</div>
                    <div v-if="record.productCode">商品编码：{{ record.productCode }}</div>
                  </div>
                </div>
              </div>
            </template>
            <template v-else-if="column.dataIndex == 'wholesalePrice'">
                <div v-if="record.isRequireInquire == 1 || !record.ladderPrice">
                    {{  text||text==0 ? '￥'+Number(text).toFixed(2) : '--' }}
                </div>
                <div v-else>
                    <template v-for="(item,index) in record.ladderPrice.split(',')" :key="index">
                    <div>¥{{ item.split('-')[1] ? Number(item.split('-')[1]).toFixed(2) : 0 }}(≥{{ item.split('-')[0] }})</div>
                    </template>
                </div>
            </template>
            <template v-else-if="column.dataIndex=='marketPrice' || column.dataIndex=='goodsPrice'||column.dataIndex=='retailPrice'">
              {{  text ? '￥' + text : '--'}}
            </template>
            <template v-else-if="column.dataIndex == 'actualSales'">
              {{ text }}/{{ record.virtualSales }}
            </template>
            <template v-else-if="column.dataIndex=='saleModel'">
              {{ text==3?'代发、批发':text==2?'批发':'代发' }}
            </template>
            <template v-else-if="column.dataIndex == 'offlineReason'">
              {{ text ? text + (record.offlineComment ? ',' + record.offlineComment : '') : '--' }}
            </template>
            <template v-else-if="column.dataIndex">
              {{ text !== undefined && text !== null ? text : '--' }}
            </template>
          </template>
        </BasicTable>
      </div>
      <SldModal
        :width="width"
        :title="title"
        :visible="visible"
        :content="content"
        :confirmBtnLoading="confirmBtnLoading"
        :showFoot="true"
        :parentPage="'good_list'"
        @cancle-event="handleCancle"
        @confirm-event="handleConfirm"
      />

    </div>
  </div>
</template>
<script>
  import { defineComponent, ref, onMounted } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { Tabs, Popconfirm, Popover } from 'ant-design-vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import {
    getGoodsList,
    getReasonList,
    lockOffGoods,
    auditGood,
  } from '/@/api/supplier/goods';
  import SldModal from '@/components/SldModal/index.vue';
  import ShowMoreHelpTip from '/@/components/ShowMoreHelpTip/index.vue';
  import { useGo } from '/@/hooks/web/usePage';
  import { useRoute } from 'vue-router';
  import { failTip, sucTip } from '/@/utils/utils';

  export default defineComponent({
    name:'SupplierProductGoodsList',
    components: {
      SldComHeader,
      [Tabs.name]: Tabs,
      [Tabs.TabPane.name]: Tabs.TabPane,
      BasicTable,
      Popconfirm,
      Popover,
      SldModal,
      ShowMoreHelpTip
    },
    setup() {
      const go = useGo();
      const route = useRoute();
      const sld_show_tip = ref(true)
      const canResize = ref(true);
      const tipData = ref([
        [
          '商品只有在审核通过才能正常出售。商品是否需审核可以在“商品设置”中配置。违规下架或审核失败的商品，供应商只能重新编辑后才能够进行出售。',
        ],
        [
          '在“商品设置”中，开启商品审核后，供应商发布、编辑商品需要管理员审核才能正常销售。',
          '审核状态分为：审核通过、等待审核和审核失败三种状态，审核失败后请详细填写审核失败原因方便供应商修改。',
        ],
        [],
        [],
      ]);
      const tabIndex = ref(1);
      const rowKey = ref('goodsId');
      const fetchSetting = {
        pageField: 'current',
        sizeField: 'pageSize',
        listField: 'data.list',
        totalField: 'data.pagination.total',
      };
      const search_form_schema = [
        {
          field: 'goodsName',
          component: 'Input',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none;' },
          componentProps: {
            minWidth: 300,
            placeholder: '请输入商品名称',
            size: 'default',
          },
          label: '商品名称',
          labelWidth: 80,
        },
        {
          field: 'productCode',
          component: 'Input',
          componentProps: {
            placeholder: '请输入商品编码',
            size: 'default',
          },
          label: '商品编码',
        },
        {
          field: 'goodsCode',
          component: 'Input',
          componentProps: {
            placeholder: '请输入商品货号',
            size: 'default',
          },
          label: '商品货号',
        },
        {
          field: 'barCode',
          component: 'Input',
          componentProps: {
            placeholder: '请输入条形码',
            size: 'default',
          },
          label: '条形码',
        },
        {
          field: 'saleModel',
          component: 'Select',
          componentProps: {
              placeholder: '请选择销售模式',
              size: 'default',
              options: [
                  { label: '全部', value: '' },
                  { label: '代发', value: 1 },
                  { label: '批发', value: 2 },
                  { label: '代发、批发', value: 3 },
              ],
          },
          label: '销售模式',
        },
        {
          field: 'storeName',
          component: 'Input',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            placeholder: '请输入供应商名称',
            size: 'default',
          },
          label: '供应商名称',
          labelWidth: 80,
        },
        {
          component: 'Select',
          label: `商品类型`,
          field: 'isVirtualGoods',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none;' },
          componentProps: {
            placeholder: `请选择商品类型`,
            options: [
              { value: '', label: `全部` },
              { value: '1', label: `实物商品` },
              { value: '2', label: `虚拟商品` },
            ],
          },
        },
        {
          component: 'RangePicker',
          label: `发布时间`,
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none;' },
          field: '[startTime,endTime]',
          componentProps: {
            format: 'YYYY-MM-DD',
            placeholder: ['开始时间', '结束时间'],
          },
        },
      ];
      const search_form_schema_one = [
        {
          field: 'goodsName',
          component: 'Input',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none;' },
          componentProps: {
            minWidth: 300,
            placeholder: '请输入商品名称',
            size: 'default',
          },
          label: '商品名称',
          labelWidth: 80,
        },
        {
          field: 'productCode',
          component: 'Input',
          componentProps: {
            placeholder: '请输入商品编码',
            size: 'default',
          },
          label: '商品编码',
        },
        {
          field: 'goodsCode',
          component: 'Input',
          componentProps: {
            placeholder: '请输入商品货号',
            size: 'default',
          },
          label: '商品货号',
        },
        {
          field: 'barCode',
          component: 'Input',
          componentProps: {
            placeholder: '请输入条形码',
            size: 'default',
          },
          label: '条形码',
        },
        {
          field: 'saleModel',
          component: 'Select',
          componentProps: {
              placeholder: '请选择销售模式',
              size: 'default',
              options: [
                  { label: '全部', value: '' },
                  { label: '代发', value: 1 },
                  { label: '批发', value: 2 },
                  { label: '代发、批发', value: 3 },
              ],
          },
          label: '销售模式',
        },
        {
          field: 'storeName',
          component: 'Input',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            placeholder: '请输入供应商名称',
            size: 'default',
          },
          label: '供应商名称',
          labelWidth: 80,
        },
        {
          component: 'Select',
          label: `商品类型`,
          field: 'isVirtualGoods',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none;' },
          componentProps: {
            placeholder: `请选择商品类型`,
            options: [
              { value: '', label: `全部` },
              { value: '1', label: `实物商品` },
              { value: '2', label: `虚拟商品` },
            ],
          },
        },
        {
          component: 'RangePicker',
          label: `发布时间`,
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none;' },
          field: '[startTime,endTime]',
          componentProps: {
            format: 'YYYY-MM-DD',
            placeholder: ['开始时间', '结束时间'],
          },
        },
        {
          component: 'Select',
          label: `审核状态`,
          field: 'auditState',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none;' },
          componentProps: {
            placeholder: `请选择审核状态`,
            options: [
              { value: '', label: `全部` },
              { value: '2', label: `待审核` },
              { value: '4', label: `审核拒绝` },
            ],
          },
        },
      ];
      const online_storage_columns = [
        {
          title: '商品信息',
          dataIndex: 'mainImage',
          width: 300,
        },
        {
          title: '销售模式',
          dataIndex: 'saleModel',
          width: 100,
        },
        {
          title: '批发价',
          dataIndex: 'wholesalePrice',
          width: 100,
        },
        {
          title: '供货价',
          dataIndex: 'goodsPrice',
          width: 100,
          customRender: ({ text, record }) => {
              return `${record.goodsPrice ? '￥' + record.goodsPrice : '--'}`;
          },
        },
        {
          title: '建议零售价',
          dataIndex: 'retailPrice',
          width: 100,
          customRender: ({ text, record }) => {
              return `${record.retailPrice ? '￥' + record.retailPrice : '--'}`;
          },
        },
        {
          title: '市场价',
          dataIndex: 'marketPrice',
          width: 100,
          customRender: ({ text, record }) => {
              return `${record.marketPrice ? '￥' + record.marketPrice : '--'}`;
          },
        },
        {
          title: '商品库存',
          dataIndex: 'goodsStock',
          width: 100,
        },
        {
          title: '实际/虚拟销量',
          dataIndex: 'actualSales',
          width: 100,
        },
        {
          title: '供应商名称',
          dataIndex: 'storeName',
          width: 100,
        },
        {
          title: '发布时间',
          dataIndex: 'createTime',
          width: 150,
        },
      ];
      const auth_columns = [
        {
          title: '商品信息',
          dataIndex: 'mainImage',
          width: 300,
        },
        {
          title: '销售模式',
          dataIndex: 'saleModel',
          width: 100,
        },
        {
          title: '批发价',
          dataIndex: 'wholesalePrice',
          width: 100,
        },
        {
          title: '供货价',
          dataIndex: 'goodsPrice',
          width: 100,
          customRender: ({ text, record }) => {
              return `${record.goodsPrice ? '￥' + record.goodsPrice : '--'}`;
          },
        },
        {
          title: '建议零售价',
          dataIndex: 'retailPrice',
          width: 100,
          customRender: ({ text, record }) => {
              return `${record.retailPrice ? '￥' + record.retailPrice : '--'}`;
          },
        },
        {
          title: '市场价',
          dataIndex: 'marketPrice',
          width: 100,
          customRender: ({ text, record }) => {
              return `${record.marketPrice ? '￥' + record.marketPrice : '--'}`;
          },
        },
        {
          title: '商品库存',
          dataIndex: 'goodsStock',
          width: 100,
        },
        {
          title: '实际/虚拟销量',
          dataIndex: 'actualSales',
          width: 100,
        },
        {
          title: '供应商名称',
          dataIndex: 'storeName',
          width: 100,
        },
        {
          width: 100,
          title: '状态',
          dataIndex: 'stateValue',
        },
        {
          width: 120,
          title: '拒绝理由',
          dataIndex: 'auditReason',
        },
        {
          title: '发布时间',
          dataIndex: 'createTime',
          width: 120,
        },
      ];
      const sheilf_columns = [
        {
          title: '商品信息',
          dataIndex: 'mainImage',
          width: 300,
        },
        {
          title: '销售模式',
          dataIndex: 'saleModel',
          width: 100,
        },
        {
          title: '批发价',
          dataIndex: 'wholesalePrice',
          width: 100,
        },
        {
          title: '供货价',
          dataIndex: 'goodsPrice',
          width: 100,
          customRender: ({ text, record }) => {
              return `${text ? '￥' + text : '--'}`;
          },
        },
        {
          title: '建议零售价',
          dataIndex: 'retailPrice',
          width: 100,
          customRender: ({ text, record }) => {
              return `${text ? '￥' + text : '--'}`;
          },
        },
        {
          title: '市场价',
          dataIndex: 'marketPrice',
          width: 100,
          customRender: ({ text, record }) => {
              return `${text ? '￥' + text : '--'}`;
          },
        },
        {
          title: '商品库存',
          dataIndex: 'goodsStock',
          width: 100,
        },
        {
          title: '实际/虚拟销量',
          dataIndex: 'actualSales',
          width: 100,
        },
        {
          title: '供应商名称',
          dataIndex: 'storeName',
          width: 100,
        },
        {
          width: 120,
          title: '违规原因',
          dataIndex: 'offlineReason',
        },
        {
          title: '发布时间',
          dataIndex: 'createTime',
          width: 120,
        },
      ];
      const actionColumn = {
        width: 150,
        title: '操作',
        dataIndex: 'action',
      };

      const handleToggleTip = (type) => {
        sld_show_tip.value = type;
        canResize.value = type;
        if(tabIndex.value==1){
          onlineRedoHeight()
        }
        if(tabIndex.value==2){
          authRedoHeight()
        }
        if(tabIndex.value==3){
          storageRedoHeight()
        }
        if(tabIndex.value==4){
          sheilfRedoHeight()
        }
      };
      const checkedKeys = ref([]);
      const [onlineStandardTable, { reload: onlineReload,redoHeight:onlineRedoHeight }] = useTable({
        rowKey: rowKey,
        api: (arg) => getGoodsList({ ...arg, state: 3 }),
        fetchSetting: fetchSetting,
        columns: online_storage_columns,
        ellipsis:false,
        actionColumn: actionColumn,
        useSearchForm: true,
        formConfig: {
          schemas: search_form_schema,
        },
        bordered: true,
        striped: false,
        rowSelection: {
          type: 'checkbox',
          selectedRowKeys: checkedKeys,
          onSelect: onSelect,
          onSelectAll: onSelectAll,
        },
        clickToRowSelect: false,
        beforeFetch(values) {
          values.startTime = values.startTime
            ? values.startTime.split(' ')[0] + ' 00:00:00'
            : undefined;
          values.endTime = values.endTime
            ? values.endTime.split(' ')[0] + ' 23:59:59'
            : undefined;
          return values;
        },
      });
      const [authStandardTable, { reload: authReload ,redoHeight:authRedoHeight}] = useTable({
        rowKey: rowKey,
        api: (arg) => getGoodsList({ ...arg, state: 2 }),
        fetchSetting: fetchSetting,
        columns: auth_columns,
        ellipsis:false,
        actionColumn: actionColumn,
        useSearchForm: true,
        formConfig: {
          schemas: search_form_schema_one,
        },
        bordered: true,
        striped: false,
        rowSelection: {
          type: 'checkbox',
          selectedRowKeys: checkedKeys,
          onSelect: onSelect,
          onSelectAll: onSelectAll,
          getCheckboxProps(record) {
            if (record.state == 4) {
              return { disabled: true };
            } else {
              return { disabled: false };
            }
          },
        },
        clickToRowSelect: false,
        beforeFetch(values) {
          values.startTime = values.startTime
            ? values.startTime.split(' ')[0] + ' 00:00:00'
            : undefined;
          values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
          return values;
        },
      });
      const [storageStandardTable, { reload: storageReload,redoHeight:storageRedoHeight }] = useTable({
        rowKey: rowKey,
        api: (arg) => getGoodsList({ ...arg, state: 4 }),
        fetchSetting: fetchSetting,
        columns: online_storage_columns,
        ellipsis:false,
        actionColumn: actionColumn,
        useSearchForm: true,
        formConfig: {
          schemas: search_form_schema,
        },
        bordered: true,
        striped: false,
        rowSelection: {
          type: 'checkbox',
          selectedRowKeys: checkedKeys,
          onSelect: onSelect,
          onSelectAll: onSelectAll,
        },
        clickToRowSelect: false,
        beforeFetch(values) {
          values.startTime = values.startTime
            ? values.startTime.split(' ')[0] + ' 00:00:00'
            : undefined;
          values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
          return values;
        },
      });
      const [sheilfStandardTable, { reload: sheilfReload,redoHeight:sheilfRedoHeight }] = useTable({
        rowKey: rowKey,
        api: (arg) => getGoodsList({ ...arg, state: 6 }),
        fetchSetting: fetchSetting,
        columns: sheilf_columns,
        actionColumn: actionColumn,
        ellipsis:false,
        useSearchForm: true,
        formConfig: {
          schemas: search_form_schema,
        },
        bordered: true,
        striped: false,
        beforeFetch(values) {
          values.startTime = values.startTime
            ? values.startTime.split(' ')[0] + ' 00:00:00'
            : undefined;
          values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
          return values;
        },
      });
      const click_event = ref(false);
      const width = ref(550);
      const title = ref('');
      const visible = ref(false);
      const content = ref([]);
      const confirmBtnLoading = ref(false);
      const operate_type = ref('');
      const operate_item = ref('');
      const operate_data = ref([]);
      const operate_lockoff_data = ref([
        {
          type: 'select',
          label: `下架理由`,
          name: 'offlineReason',
          placeholder: `请选择下架理由`,
          width: 353,
          selData: [],
          rules: [
            {
              required: true,
              message: `请选择下架理由`,
            },
          ],
        },
        {
          type: 'textarea',
          label: `备注`,
          name: 'offlineComment',
          placeholder: `请输入备注`,
          extra: `最多输入100个字`,
          maxLength: 100,
        },
      ]);
      const operate_refuse_data = ref([
        {
          type: 'select',
          label: `拒绝理由`,
          name: 'auditReason',
          placeholder: `请选择拒绝理由`,
          width: 353,
          selData: [],
          rules: [
            {
              required: true,
              message: `请选择拒绝理由`,
            },
          ],
        },
        {
          type: 'textarea',
          label: `备注`,
          name: 'auditComment',
          placeholder: `请输入请选择拒绝理由`,
          extra: `最多输入100个字`,
          maxLength: 100,
        },
      ]);

      //tab切换
      function handleChange(e) {
        if (e == 1) {
          onlineReload();
        } else if (e == 2) {
          authReload();
        } else if (e == 3) {
          storageReload();
        } else {
          sheilfReload();
        }
        checkedKeys.value = [];
      }

      //表格点击回调事件
      function handleClick(item, type) {
        if (click_event.value) return;
        operate_type.value = type;
        operate_item.value = item ? item : null;
        let params = {};
        let data = JSON.parse(JSON.stringify(operate_data.value));
        if (type == 'detail') {
          go(`/supplier_product/goods_list_to_detail?id=${item.goodsId}`);
          return;
        } else if (type == 'lockOff') {
          if (item == null && checkedKeys.value.length == 0) {
            failTip('请先选中数据');
            return;
          }
          data = JSON.parse(JSON.stringify(operate_lockoff_data.value));
          title.value = '违规下架商品';
        } else if (type == 'pass') {
          if (item == null && checkedKeys.value.length == 0) {
            failTip('请先选中数据');
            return;
          }
          params.goodsIds = item ? item.goodsId : checkedKeys.value.join(',');
          params.state = 1;
          click_event.value = true;
          operate_role(params);
          return;
        } else if (type == 'refuse') {
          if (item == null && checkedKeys.value.length == 0) {
            failTip('请先选中数据');
            return;
          }
          data = JSON.parse(JSON.stringify(operate_refuse_data.value));
          title.value = '审核拒绝理由';
        } else {
          return;
        }
        width.value = 550;
        content.value = data;
        visible.value = true;
      }

      //操作
      const operate_role = async (params) => {
        let res = {};
        if (operate_type.value == 'lockOff') {
          res = await lockOffGoods(params);
        } else if (operate_type.value == 'pass' || operate_type.value == 'refuse') {
          res = await auditGood(params);
        } else {
          click_event.value = false;
          return;
        }
        if (res.state == 200) {
          sucTip(res.msg);
          if (tabIndex.value == 1) {
            onlineReload();
          } else if (tabIndex.value == 2) {
            authReload();
          } else if (tabIndex.value == 3) {
            storageReload();
          } else {
            sheilfReload();
          }
          handleCancle();
          checkedKeys.value = [];
          click_event.value = false;
        } else {
          click_event.value = false;
          failTip(res.msg);
        }
      };

      //弹窗取消事件
      function handleCancle() {
        visible.value = false;
        content.value = [];
        operate_item.value = '';
      }

      //弹窗确认事件
      function handleConfirm(val) {
        if (click_event.value) return;
        click_event.value = true;
        if (operate_type.value == 'lockOff') {
          val.goodsIds = operate_item.value ? operate_item.value.goodsId : checkedKeys.value.join(',');
        } else if (operate_type.value == 'refuse') {
          val.goodsIds = operate_item.value ? operate_item.value.goodsId : checkedKeys.value.join(',');
          val.state = 0;
        }
        operate_role(val);
      }

      //表格的单行选中回调事件
      function onSelect(record, selected) {
        if (selected) {
          checkedKeys.value = [...checkedKeys.value, record[rowKey.value]];
        } else {
          checkedKeys.value = checkedKeys.value.filter((item) => item != record[rowKey.value]);
        }
      }

      //表格的全选回调事件
      function onSelectAll(selected, selectedRows, changeRows) {
        const changeIds = changeRows.map((item) => item[rowKey.value]);
        if (selected) {
          checkedKeys.value = [...checkedKeys.value, ...changeIds];
        } else {
          checkedKeys.value = checkedKeys.value.filter((item) => {
            return !changeIds.includes(item);
          });
        }
      }

      onMounted(() => {
        if(route.query.tab&&route.query.tab=='check'){
          tabIndex.value = 2
        }
        get_reason_list();
      });

      //获取原因列表
      const get_reason_list = async () => {
        const res = await getReasonList({ type: 101, isShow: 1 });
        if (res.state == 200 && res.data && res.data.list) {
          let temp = operate_lockoff_data.value.filter((item) => item.name == 'offlineReason');
          if (temp.length > 0) {
            let selData = [];
            res.data.list.map((item) => {
              selData.push({
                key: item.content,
                name: item.content,
              });
            });
            temp[0].selData = selData;
          }
        }
        const respon = await getReasonList({ type: 102, isShow: 1 });
        if (respon.state == 200 && respon.data && respon.data.list) {
          let temp = operate_refuse_data.value.filter((item) => item.name == 'auditReason');
          if (temp.length > 0) {
            let selData = [];
            respon.data.list.map((item) => {
              selData.push({
                key: item.content,
                name: item.content,
              });
            });
            temp[0].selData = selData;
          }
        }
      };

      return {
        tipData,
        tabIndex,
        handleChange,
        rowKey,
        onlineStandardTable,
        authStandardTable,
        storageStandardTable,
        sheilfStandardTable,
        onSelect,
        onSelectAll,
        handleClick,
        checkedKeys,
        click_event,
        width,
        title,
        visible,
        content,
        confirmBtnLoading,
        operate_item,
        operate_type,
        operate_data,
        operate_lockoff_data,
        operate_refuse_data,
        handleCancle,
        handleToggleTip,
        handleConfirm,
        sld_show_tip,
        canResize
      };
    },
  });
</script>
<style lang="less" scoped></style>
