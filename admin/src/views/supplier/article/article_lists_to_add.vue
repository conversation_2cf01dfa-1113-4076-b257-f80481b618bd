<template>
  <div class="section_padding article_lists_to_add">
    <div class="section_padding_back_add">
      <div class="article_lists_to_add_height">
        <div class="common_page_title">基本信息</div>
        <BasicForm @register="register" style="margin-top: 15px" />
        <div class="common_page_title">内容编辑</div>
        <div style="margin-top: 20px">
          <SldUEditor
            :id="'agreement'"
            :initEditorContent="initEditorContent"
            :getContentFlag="getEditorContentFlag"
            :getEditorContent="getEditorContent"
             v-if="editorFlag"
          />
        </div>
        <div style="width: 100%; height: 50px"></div>
        <div class="m_diy_bottom_wrap"  :style="{ position: 'fixed', left: getRealWidth + 10 + 'px' }">
          <div class="add_goods_bottom_btn" @click="handleBack">返回</div>
          <div class="add_goods_bottom_btn add_goods_bottom_btn_sel" @click="handleConfirm">保存并返回</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  import { defineComponent, ref, onMounted, unref } from 'vue';
  import { Input } from 'ant-design-vue';
  import { useRoute, useRouter } from 'vue-router';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { quillEscapeToHtml, sucTip, failTip } from '/@/utils/utils';
  import {
    getArticleCategoryList,
    getArticleDetail,
    editArticle,
    addArticle,
  } from '/@/api/supplier/article';
  import SldUEditor from '/@/components/SldUEditor/index.vue';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  const { getRealWidth } = useMenuSetting();
  import { pageClose } from '/@/utils/utils';
  import { useMultipleTabStore } from '/@/store/modules/multipleTab';

  export default defineComponent({
    components: {
      BasicForm,
      Input,
      SldUEditor,
    },
    setup() {
      const tabStore = useMultipleTabStore();
      const route = useRoute();
      const editorFlag = ref(false);
      const router = useRouter();
      const [register, { validate, getFieldsValue, setFieldsValue, updateSchema }] = useForm({
        labelWidth: 90,
        baseRowStyle: {
          display: 'block',
        },
        schemas: [
          {
            field: 'categoryId',
            component: 'Select',
            label: '文章分类',
            required: true,
            colProps: {
              span: 8,
            },
            componentProps: {
              options: [],
            },
          },
          {
            field: 'title',
            component: 'Input',
            label: '文章标题',
            required: true,
            componentProps: {
              maxLength: 20,
            },
            colProps: {
              span: 8,
            },
          },
          {
            field: 'outUrl',
            component: 'Input',
            label: '文章外链',
            colProps: {
              span: 8,
            },
          },
          {
            field: 'state',
            component: 'RadioGroup',
            label: '是否显示',
            required: true,
            colProps: {
              span: 8,
            },
            componentProps: {
              options: [
                {
                  label: '是',
                  value: '1',
                },
                {
                  label: '否',
                  value: '0',
                },
              ],
            },
          },
          {
            field: 'sort',
            component: 'InputNumber',
            label: '排序',
            required: true,
            colProps: {
              span: 8,
            },
            componentProps: {
              min: 0,
              max: 255,
            },
          },
        ],
        showActionButtonGroup: false,
      });

      const click_event = ref(false);
      const initEditorContent = ref(''); //百度编辑器内容
      const getEditorContentFlag = ref(false); //获取百度编辑器内容标识

      onMounted(() => {
        get_category_list();
        editorFlag.value = false
        if (route.query && route.query.type == 'edit' && route.query.id) {
          get_article_detail({ articleId: route.query.id });
        } else {
          editorFlag.value = true
          setFieldsValue({
            state: '1',
          });
        }
      });

      //获取文章分类
      const get_category_list = async () => {
        const res = await getArticleCategoryList({ pageSize: 10000 });
        if (res.state == 200) {
          updateSchema({
            field: 'categoryId',
            componentProps: {
              options: unref(res.data.list).map((item) => ({
                key: item.categoryId,
                value: item.categoryId,
                label: item.categoryName,
              })),
            },
          });
        }
      };

      //获取文章详情
      const get_article_detail = async (params) => {
        const res = await getArticleDetail(params);
        if (res.state == 200) {
          initEditorContent.value = quillEscapeToHtml(res.data.content);
          setFieldsValue({
            categoryId: res.data.categoryId || undefined,
            title: res.data.title || undefined,
            outUrl: res.data.outUrl || undefined,
            state: res.data.state ? res.data.state.toString() : '1',
            sort: res.data.sort || undefined,
          });
          editorFlag.value = true
        } else {
          failTip(res.msg);
        }
      };

      //获取编辑器内容
      function getEditorContent(con) {
        if (click_event.value) return;
        click_event.value = true;
        getEditorContentFlag.value = false;
        update_article({
          categoryId: getFieldsValue().categoryId,
          title: getFieldsValue().title,
          outUrl: getFieldsValue().outUrl,
          state: getFieldsValue().state,
          sort: getFieldsValue().sort,
          content: con,
        });
      }

      function handleBack() {
        if (window.history && window.history.length == 1) {
          pageClose();
        } else {
          const { fullPath } = route;
          tabStore.closeTabByKey(fullPath, router);
          router.back();
        }
      }

      function handleConfirm() {
        if (
          !getFieldsValue().categoryId ||
          !getFieldsValue().title ||
          !getFieldsValue().title.trim() ||
          !getFieldsValue().sort
        ) {
          validate();
          return;
        }
        if (click_event.value) return;
        getEditorContentFlag.value = true;
      }

      //更新文章数据
      const update_article = async (params) => {
        let res = '';
        if (route.query && route.query.type == 'edit' && route.query.id) {
          params.articleId = route.query.id;
          res = await editArticle(params);
        } else {
          res = await addArticle(params);
        }
        if (res.state == 200) {
          sucTip(res.msg);
          setTimeout(() => {
            click_event.value = false;
            handleBack();
          }, 1500);
        } else {
          click_event.value = false;
          failTip(res.msg);
        }
      };

      return {
        register,
        getRealWidth,
        get_category_list,
        get_article_detail,
        initEditorContent,
        getEditorContentFlag,
        getEditorContent,
        handleBack,
        handleConfirm,
        update_article,
        click_event,
        editorFlag
      };
    },
  });
</script>
<style lang="less" scoped>
  .article_lists_to_add {
    height: 100%;
    .article_lists_to_add_height{
      max-height: calc(100vh - 46px - 20px - 57px);
      height: calc(100vh - 46px - 20px - 57px);
      overflow: auto;
    }

    .common_page_title {
      display: flex;
      position: relative;
      align-items: center;
      justify-content: flex-start;
      height: 22px;
      padding-left: 10px;
      color: #101010;
      font-size: 14px;
      font-weight: 700;
      line-height: 22px;

      &::before {
        content: '';
        display: inline-block;
        position: absolute;
        top: 50%;
        left: 0;
        width: 4px;
        height: 14px;
        margin-top: -6px;
        background: #fa6f1e;
      }
    }
  }
</style>
