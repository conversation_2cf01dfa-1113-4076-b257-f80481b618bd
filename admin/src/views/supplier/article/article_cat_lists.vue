<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <SldComHeader :type="1" :title="'文章分类管理'" />
      <BasicTable @register="standardTable" style="padding: 0">
        <template #tableTitle>
          <div class="toolbar">
            <div class="toolbar_btn" @click="handleClick(null, 'add')">
              <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#fff" />
              <span>新增文章分类</span>
            </div>
            <Popconfirm
              v-if="checkedKeys.length > 0"
              title="删除后不可恢复，是否确定删除？"
              @confirm="handleClick(null, 'dels')"
            >
              <div class="toolbar_btn">
                <AliSvgIcon iconName="iconshanchu" width="15px" height="15px" fillColor="#ff5908" />
                <span>删除</span>
              </div>
            </Popconfirm>
            <div v-else class="toolbar_btn" @click="handleClick(null, 'dels')">
              <AliSvgIcon iconName="iconshanchu" width="15px" height="15px" fillColor="#ff5908" />
              <span>删除</span>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.key == 'action'">
            <template v-if="record.isSuper == 1"> -- </template>
            <template v-else>
              <span class="common_page_edit" @click="handleClick(record, 'edit')">编辑</span>
              <Popconfirm
                title="删除后不可恢复，是否确定删除？"
                @confirm="handleClick(record, 'del')"
              >
                <span class="common_page_edit">删除</span>
              </Popconfirm>
            </template>
          </template>
          <template v-else-if="column.key">
            {{ text||text==0 ? text : '--' }}
          </template>
        </template>
      </BasicTable>
      <SldModal
        :width="width"
        :title="title"
        :visible="visible"
        :content="content"
        :confirmBtnLoading="confirmBtnLoading"
        :showFoot="true"
        @cancle-event="handleCancle"
        @confirm-event="handleConfirm"
      />
    </div>
  </div>
</template>
<script>
  import { defineComponent, ref } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { Popconfirm } from 'ant-design-vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import {
    getArticleCategoryList,
    addArticleCategory,
    editArticleCategory,
    delArticleCategory,
  } from '/@/api/supplier/article';
  import SldModal from '@/components/SldModal/index.vue';
  import { sucTip, failTip } from '/@/utils/utils';

  export default defineComponent({
    components: {
      SldComHeader,
      BasicTable,
      Popconfirm,
      SldModal,
    },
    setup() {
      const checkedKeys = ref([]); //表格的键值声明，非必填，主要用于勾选功能中的数据处理
      const rowKey = ref('categoryId'); //表格开启选择功能时，需要声明选中数据变量

      const [standardTable, { reload }] = useTable({
        api: (arg) => getArticleCategoryList({ ...arg }),
        fetchSetting: {
          pageField: 'current',
          sizeField: 'pageSize',
          listField: 'data.list',
          totalField: 'data.pagination.total',
        },
        columns: [
          {
            title: '分类名称',
            dataIndex: 'categoryName',
            width: 100,
          },
          {
            title: '创建时间',
            dataIndex: 'createTime',
            width: 100,
          },
          {
            title: '排序',
            dataIndex: 'sort',
            width: 100,
          },
        ],
        actionColumn: {
          width: 100,
          title: '操作',
          dataIndex: 'action',
        },
        useSearchForm: true,
        formConfig: {
          schemas: [
            {
              field: 'categoryName',
              component: 'Input',
              colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                maxlength: 10,
                placeholder: '请输入分类名称',
                size: 'default',
              },
              label: '分类名称',
              labelWidth: 80,
            },
          ],
        },
        bordered: true,
        striped: false,
        showIndexColumn: true,
        rowKey: rowKey,
        rowSelection: {
          type: 'checkbox',
          selectedRowKeys: checkedKeys,
          onSelect: onSelect,
          onSelectAll: onSelectAll,
        },
        clickToRowSelect: false,
      });

      const width = ref(550);
      const title = ref('');
      const visible = ref(false);
      const content = ref([]);
      const confirmBtnLoading = ref(false);
      const operate_id = ref('');
      const operate_type = ref('');

      const operate_data = ref([
        {
          type: 'input',
          label: `分类名称`,
          name: 'categoryName',
          placeholder: `请输入分类名称`,
          maxLength: 10,
          rules: [
            {
              required: true,
              message: `请输入分类名称`,
            },
          ],
        },
        {
          type: 'inputnum',
          label: `排序`,
          name: 'sort',
          placeholder: `请输入排序`,
          extra: `数字越小，显示越靠前，0～255`,
          min: 0,
          max: 255,
          rules: [
            {
              required: true,
              message: `请输入排序`,
            },
          ],
        },
      ]); //新增、编辑弹窗数据

      //表格的单行选中回调事件
      function onSelect(record, selected) {
        if (selected) {
          checkedKeys.value = [...checkedKeys.value, record[rowKey.value]];
        } else {
          checkedKeys.value = checkedKeys.value.filter((item) => item != record[rowKey.value]);
        }
      }

      //表格的全选回调事件
      function onSelectAll(selected, selectedRows, changeRows) {
        const changeIds = changeRows.map((item) => item[rowKey.value]);
        if (selected) {
          checkedKeys.value = [...checkedKeys.value, ...changeIds];
        } else {
          checkedKeys.value = checkedKeys.value.filter((item) => {
            return !changeIds.includes(item);
          });
        }
      }

      //表格点击回调事件
      function handleClick(item, type) {
        operate_type.value = type;
        if (item) {
          operate_id.value = item.categoryId;
        }
        if (type == 'add' || type == 'edit') {
          title.value = type == 'add' ? '添加文章分类' : '编辑文章分类';
          visible.value = true;
          let data = JSON.parse(JSON.stringify(operate_data.value));
          if (type == 'edit') {
            data.map((items) => {
              items.initialValue = item[items.name]||item[items.name]==0 ? item[items.name] : undefined;
            });
          }
          content.value = data;
        } else if (type == 'del') {
          operate_role({ categoryIds: item.categoryId });
        } else if (type == 'dels') {
          if (checkedKeys.value.length == 0) {
            failTip('请先选中数据');
          } else {
            operate_role({ categoryIds: checkedKeys.value.join(',') });
          }
        }
      }

      //弹窗取消事件
      function handleCancle() {
        visible.value = false;
        content.value = [];
        operate_id.value = '';
      }

      //弹窗确认事件
      function handleConfirm(val) {
        if (operate_type.value == 'edit') {
          val.categoryId = operate_id.value;
        }
        operate_role(val);
      }

      //弹窗操作
      const operate_role = async (params) => {
        confirmBtnLoading.value = true;
        let res = {};
        if (operate_type.value == 'add') {
          res = await addArticleCategory(params);
        } else if (operate_type.value == 'edit') {
          res = await editArticleCategory(params);
        } else if (operate_type.value == 'del' || operate_type.value == 'dels') {
          res = await delArticleCategory(params);
        }
        confirmBtnLoading.value = false;
        if (res.state == 200) {
          handleCancle();
          sucTip(res.msg);
          reload();
          checkedKeys.value = [];
        } else {
          failTip(res.msg);
        }
      };

      return {
        standardTable,
        rowKey,
        checkedKeys,
        width,
        title,
        visible,
        content,
        confirmBtnLoading,
        operate_id,
        operate_type,
        operate_data,
        handleClick,
        handleCancle,
        handleConfirm,
      };
    },
  });
</script>
<style lang="less" scoped></style>
