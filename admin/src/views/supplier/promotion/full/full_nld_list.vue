<template>
  <div class="full_ald_list">
    <BasicTable @register="registerTable" rowKey="couponId">
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'startTime'">
          <div class="voucher_time_wrap">
            <p>{{ text }}</p>
            <p>~</p>
            <p>{{ record.endTime }}</p>
          </div>
        </template>
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                label: '查看',
                onClick: view.bind(null, record.fullId),
              },
              {
                label: '失效',
                ifShow: record.state == 2 || record.state == 3,
                popConfirm: {
                  title: '失效后不可恢复，是否确定失效？',
                  placement: 'left',
                  confirm: operate.bind(null, { fullId: record.fullId }, 'invalid'),
                },
              },
              {
                label: '删除',
                ifShow: record.state == 1 || record.state == 4 || record.state == 5,
                popConfirm: {
                  title: '删除后不可恢复，是否确定删除？',
                  placement: 'left',
                  confirm: operate.bind(null, { fullId: record.fullId }, 'del'),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <Modal
      wrapClassName="full_modal"
      :visible="visible"
      title="查看详情"
      :zIndex="999"
      :maskClosable="false"
      :width="1000"
      :footer="null"
      :destroyOnClose="true"
      @cancel="sldHandleCancle"
    >
      <AddFullNld :fullId="fullId" />
    </Modal>
  </div>
</template>
<script>
  export default {
    name: 'FullNldList',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import AddFullNld from './add_full_nld.vue';
  import { Modal } from 'ant-design-vue';
  import { validatorEmoji } from '/@/utils/validate';
  import { getFullNldListApi, getFullNldInvalidApi, getFullNldDelApi } from '/@/api/supplier/full';
  import { failTip, sucTip } from '/@/utils/utils';

  const visible = ref(false);

  const fullId = ref(0);

  const columns = reactive({
    data: [
      {
        title: `店铺名称`,
        dataIndex: 'storeName',
        align: 'center',
        width: 100,
      },
      {
        title: `活动名称`,
        dataIndex: 'fullName',
        align: 'center',
        width: 100,
      },
      {
        title: `活动时间`,
        dataIndex: 'startTime',
        align: 'center',
        width: 100,
      },
      {
        title: `活动状态`,
        dataIndex: 'stateValue',
        align: 'center',
        width: 100,
      },
    ],
  });

  const searchFormSchema = reactive({
    data: [
      {
        component: 'Input',
        label: `店铺名称`,
        field: 'storeName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请输入店铺名称`,
        },
        rules: [
          {
            // @ts-ignore
            validator: async (rule, value) => {
              await validatorEmoji(rule, value);
            },
            trigger: 'change',
          },
        ],
      },
      {
        component: 'Input',
        label: `活动名称`,
        field: 'fullName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请输入活动名称`,
        },
        rules: [
          {
            // @ts-ignore
            validator: async (rule, value) => {
              await validatorEmoji(rule, value);
            },
            trigger: 'change',
          },
        ],
      },
      {
        colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
        component: 'RangePicker',
        label: `活动时间`,
        field: '[startTime,endTime]',
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: ['开始时间', '结束时间'],
        },
      },
      {
        component: 'Select',
        label: `活动状态`,
        field: 'state',
        labelWidth: 90,
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请选择活动状态`,
          options: [
            { value: '', label: `全部` },
            { value: '1', label: `待发布` },
            { value: '2', label: `未开始` },
            { value: '3', label: `进行中` },
            { value: '5', label: `已失效` },
            { value: '4', label: `已结束` },
          ],
        },
      },
    ],
  });

  // 表格数据
  const [registerTable, { reload, getForm }] = useTable({
    // 请求接口
    api: getFullNldListApi,
    // 表头
    columns: columns.data,
    immediate: true,
    // 点击搜索前处理的参数
    beforeFetch(values) {
      values.startTime = values.startTime
        ? values.startTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
      return values;
    },
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: searchFormSchema.data,
    },
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    // 是否显示序号列
    showIndexColumn: true,
    // 表格右侧操作列配置
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
      // slots: { customRender: 'action' },
    },
  });

  const sldHandleCancle = () => {
    visible.value = false;
  };

  const view = (id) => {
    fullId.value = id;
    visible.value = true;
  };

  //操作  type: invalid 失效
  const operate = async (id, type) => {
    let param_data = {};
    let res = null;
    if (type == 'invalid') {
      param_data = id;
      res = await getFullNldInvalidApi(param_data);
    }
    if (type == 'del') {
      param_data = id;
      res = await getFullNldDelApi(param_data);
    }
    if (res.state == 200) {
      sucTip(res.msg);
      reload();
    } else {
      failTip(res.msg);
    }
  };

  onMounted(() => {});
</script>
<style lang="less" scoped>
  p {
    margin-bottom: 0;
  }

  .full_ald_list {
    .voucher_num {
      color: @primary-color;
      cursor: pointer;
    }
  }
</style>
<style lang="less">
  .full_modal {
    .ant-modal-header {
      padding: 12px 24px !important;
      background-color: @primary-color;
    }

    .ant-modal-title {
      color: #fff;
      font-size: 14px !important;
      line-height: 18px !important;
    }

    .ant-modal-close-x .anticon svg {
      fill: #fff;
    }

    .ant-modal .ant-modal-content {
      border-radius: 4px;
    }

    .ant-form-item-with-help {
      margin-bottom: 24px;
    }

    .ant-form-item-has-error .ant-input-number,
    .ant-form-item-has-error .ant-picker {
      border-color: #ed6f6f !important;
    }

    .ant-modal-close {
      top: 12px;
      right: 20px;
      height: auto !important;

      .ant-modal-close-x {
        display: flex !important;
        align-items: center;
        justify-content: center;
        width: auto !important;
        height: auto !important;
        line-height: normal;
      }
    }
  }
</style>
