<template>
  <div class="add_full_ald">
    <Spin :spinning="spinning">
      <Form
        ref="formRef"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
        :model="curFormData"
        :scrollToFirstError="true"
        style="margin-top: 10px"
      >
        <template v-for="(item, index) in formData">
          <template v-if="item.type == 'tipTitle'">
            <div :key="index" class="add_full_ald_title">
              <span class="title">{{ item.title }}</span>
            </div>
          </template>
          <template v-if="item.type == 'input'">
            <Form.Item
              :key="index"
              :name="item.name"
              :label="item.label"
              :extra="item.extra"
              :rules="item.rules"
            >
              <Input
                :maxlength="item.maxLength !== undefined ? item.maxLength : undefined"
                :disabled="item.disable"
                style="width: 400px"
                v-model:value="curFormData[item.name]"
              />
            </Form.Item>
          </template>
          <template v-if="item.type == 'range_picker'">
            <Form.Item
              :key="index"
              :name="item.name"
              :label="item.label"
              :extra="item.extra"
              :rules="item.rules"
            >
              <RangePicker
                style="width: 400px"
                :format="item.format"
                :disabled="item.disabled"
                v-model:value="curFormData[item.name]"
                :disabledDate="item.disabledDate"
                :showTime="true"
                :placeholder="[item.placeholder1, item.placeholder2]"
              />
            </Form.Item>
          </template>
        </template>
        <div v-for="(it, ind) in ladder_promotion" :key="ind">
          <div class="add_full_ald_title">
            <span class="title">{{ num_to_num()[ind + 1] + '级优惠' }}</span>
          </div>
          <Form.Item
            name="fullValue"
            label="优惠门槛"
            extra="以元为单位，设置使用该活动的最低金额"
            required
          >
            <InputNumber
              style="width: 400px !important"
              placeholder="请输入优惠门槛"
              :disabled="true"
              v-model:value="it.fullValue"
              :min="1"
              :max="9999999"
              :precision="2"
            />
          </Form.Item>
          <Form.Item
            name="sendCouponIds"
            label="优惠内容"
            extra="优惠折扣，满足优惠门槛后可以享受该优惠折扣，例如：输入90代表9折"
            required
          >
            <span style="margin-right: 10px">打</span>
            <InputNumber
              style="width: 100px !important"
              :disabled="true"
              v-model:value="it.minusValue"
              :min="0"
              :max="9999999"
              :precision="0"
            />
            <span style="margin-left: 10px">折</span>
          </Form.Item>
          <Form.Item :label="' '" class="couponId_from">
            <Checkbox
              :disabled="viewFlag"
              :checked="
                it.sendCouponIds != undefined && it.sendCouponIds ? true : false
              "
            >
              送优惠券
            </Checkbox>
            <div
              class="sel_goods flex_column_start_start"
              v-if="
                it.sel_voucher && it.sel_voucher != undefined && it.sel_voucher.length > 0
              "
            >
              <BasicTable
                rowKey="couponId"
                :columns="columns_sel_voucher"
                :dataSource="it.sel_voucher"
                :pagination="false"
                :bordered="true"
                :ellipsis="false"
              >
              </BasicTable>
            </div>
          </Form.Item>
          <Form.Item :label="' '" class="couponId_from">
            <Checkbox
              :disabled="viewFlag"
              :checked="it.sendGoodsIds != undefined && it.sendGoodsIds ? true : false"
            >
              送赠品
            </Checkbox>
            <div
              class="sel_goods flex_column_start_start"
              v-if="it.sel_goods && it.sel_goods != undefined && it.sel_goods.length > 0"
            >
              <BasicTable
                rowKey="goodsId"
                :columns="columns_spu"
                :dataSource="it.sel_goods"
                :pagination="false"
                :bordered="true"
                :ellipsis="false"
              >
                <template #bodyCell="{ column, text, record }">
                  <template v-if="column.dataIndex == 'goodsImage'">
                    <Popover placement="rightTop">
                      <template #content>
                        <div style="max-width: 200px; text-align: center">
                          <img :src="text" alt="" style="max-width: 100%; max-height: 100%" />
                        </div>
                      </template>
                      <div class="business_load_img_full">
                        <img :src="text" alt="" />
                      </div>
                    </Popover>
                  </template>
                  <template v-if="column.dataIndex == 'goodsPrice'">
                    {{ text||text==0 ? '￥' + Number(text).toFixed(2) : '--' }}
                  </template>
                  <template v-if="column.dataIndex == 'goodsStock'">
                    {{ text ? text : record.productStock ? record.productStock : '--' }}
                  </template>
                </template>
              </BasicTable>
            </div>
          </Form.Item>
        </div>
      </Form>
    </Spin>
  </div>
</template>
<script>
  export default {
    name: 'AddFullAld',
  };
</script>
<script setup>
  import { ref, onMounted, computed } from 'vue';
  import { BasicTable } from '/@/components/Table';
  import moment from 'moment';
  import dayjs from 'dayjs';
  import {
    Spin,
    Form,
    Input,
    RangePicker,
    InputNumber,
    Checkbox,
    Popover
  } from 'ant-design-vue';
  import { num_to_num } from '/@/utils/utils';
  import { getFullAldDetailApi } from '/@/api/supplier/full';

  const props = defineProps({
    fullId: { type: Number, required: true }, //id
  });

  const viewFlag = ref(true);

  //计算属性转为ref对象
  const fullId = computed(() => {
    return props.fullId;
  });

  const spinning = ref(false);

  const curFormData = ref({});

  const ladder_promotion = ref([
    {
      key: 1,
      fullValue: '', //优惠门槛
      minusValue: '', //优惠金额
      sendCouponIds: false, //是否赠送优惠券
      sel_voucher: [], //选择的优惠券信息
      sendGoodsIds: false, //是否送赠品
      sel_goods: [], //选择的赠品信息
    },
  ]);

  const columns_spu = ref([
    {
      title: '商品图片',
      dataIndex: 'goodsImage',
      align: 'center',
      width: 100,
    },
    {
      title: '商品名称',
      dataIndex: 'goodsName',
      align: 'center',
      width: 150,
    },
    {
      title: '商品价格',
      dataIndex: 'goodsPrice',
      align: 'center',
      width: 100,
    },
    {
      title: '商品库存',
      dataIndex: 'goodsStock',
      align: 'center',
      width: 100,
    },
  ]);

  const columns_sel_voucher = ref([
    {
      title: '优惠券名称',
      dataIndex: 'couponName',
      align: 'center',
      width: 150,
    },
    {
      title: '优惠券类型',
      dataIndex: 'couponTypeValue',
      align: 'center',
      width: 100,
    },
    {
      title: '优惠券内容',
      dataIndex: 'couponContent',
      align: 'center',
      width: 100,
    },
    {
      title: '剩余可用',
      dataIndex: 'couponStock',
      align: 'center',
      width: 100,
    },
  ]);

  const formData = ref([
    {
      type: 'tipTitle',
      title: '活动基本信息',
    },
    {
      type: 'input',
      label: `活动名称`,
      name: 'fullName',
      placeholder: `请输入活动名称`,
      maxLength: 20, //最多字数
      disable: true, //是否禁用
      extra: '最多输入20个字',
      rules: [
        {
          required: true,
          message: `请输入活动名称`, //请输入活动名称
        },
      ],
    },
    {
      type: 'range_picker',
      label: `活动时间`, //开始和结束时间
      name: 'startTime',
      format: 'YYYY-MM-DD HH:mm:ss',
      placeholder: `请输入活动时间`,
      placeholder1: `开始时间`,
      placeholder2: `结束时间`,
      disabled: true,
      disabledDate: (currentDate) => currentDate && currentDate < moment().subtract(1, 'days'),
      rules: [
        {
          required: true,
          message: `请选择活动时间`,
        },
      ],
      extra: '活动时间不可与其他活动重叠',
    },
  ]);

  const get_full_acm_detail = async () => {
    try {
      let res = await getFullAldDetailApi({ fullId: fullId.value });
      if (res.state == 200) {
        formData.value.forEach((item) => {
          if (item.name) {
            curFormData.value[item.name] = res.data[item.name];
            if (item.name == 'startTime') {
              curFormData.value[item.name] = [
                dayjs(res.data.startTime, item.format),
                dayjs(res.data.endTime, item.format),
              ];
            }
          }
        });

        let detail = res.data;
        detail.ruleList.forEach((item, index) => {
          if (index == 0) {
            ladder_promotion.value[0].fullValue = item.fullValue;
            ladder_promotion.value[0].minusValue = item.minusValue;
            //初始化选中的优惠券数据
            if (
              item.couponList != null &&
              item.couponList.length != undefined &&
              item.couponList.length > 0
            ) {
              ladder_promotion.value[0].sel_voucher = item.couponList;
              ladder_promotion.value[0].sendCouponIds = true;
            }
            //初始化选中的商品数据
            if (
              item.giftList != null &&
              item.giftList.length != undefined &&
              item.giftList.length > 0
            ) {
              ladder_promotion.value[0].sel_goods = item.giftList;
              ladder_promotion.value[0].sendGoodsIds = true;
            }
          } else {
            ladder_promotion.value.push({
              fullValue: item.fullValue,
              minusValue: item.minusValue,
              sel_voucher:
                item.couponList != null &&
                item.couponList.length != undefined &&
                item.couponList.length > 0
                  ? item.couponList
                  : [],
              sendCouponIds:
                item.couponList != null &&
                item.couponList.length != undefined &&
                item.couponList.length > 0
                  ? true
                  : false,
              sel_goods:
                item.giftList != null &&
                item.giftList.length != undefined &&
                item.giftList.length > 0
                  ? item.giftList
                  : [],
              sendGoodsIds:
                item.giftList != null &&
                item.giftList.length != undefined &&
                item.giftList.length > 0
                  ? true
                  : false,
            });
          }
        });
      }
    } catch (error) {}
  };

  onMounted(() => {
    get_full_acm_detail();
  });
</script>
<style lang="less" scoped>
  p {
    margin-bottom: 0;
  }

  .add_full_ald {
    max-height: 600px;
    padding: 10px;
    overflow: auto;

    .add_full_ald_title {
      width: 100%;
      height: 36px;
      margin-bottom: 24px;
      border-radius: 2px;
      background: #fffaf7;
      line-height: 36px;

      .title {
        padding-left: 10px;
        color: #333;
        font-size: 13px;
      }
    }
  }

  .sel_goods {
    padding-left: 24px;
  }
</style>
<style lang="less">
  .couponId_from {
    .ant-form-item-label > label {
      &::after {
        content: ' ';
      }
    }
  }

  .add_full_ald {
    .ant-table-body {
      height: auto !important;
      max-height: 400px !important;
    }
  }
</style>
