<template>
  <div class="member_receive_lists section_padding">
    <div class="section_padding_back">
      <SldComHeader title="领取详情" :back="true" />
      <div class="com_line" style="height: 1px"></div>
      <BasicTable @register="registerTable" rowKey="couponId">
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex == 'storeName'">
            <div class="flex_column_center_center">
              <span class="mem_name" v-if="record.storeName">{{ record.storeName }}</span>
              <span class="mem_name" v-if="record.userName">{{ record.userName }}</span>
              <span class="mem_name" v-if="!record.storeName&&!record.userName">--</span>
            </div>
          </template>
        </template>
      </BasicTable>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'StoreCouponReceiveList',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { useRoute } from 'vue-router';
  import { getCouponReceiveListsApi } from '/@/api/supplier/coupon';

  const route = useRoute();

  // 参数
  const id = ref(route.query?.id);
  const searchInfo = ref({
    couponId: id.value,
  });
  const columns = reactive({
    data: [
      {
        title: `用户信息`,
        dataIndex: 'storeName',
        align: 'center',
        width: 100,
      },
      {
        title: `使用状态`,
        dataIndex: 'useStateValue',
        align: 'center',
        width: 100,
      },
      {
        title: `领取时间`,
        dataIndex: 'receiveTime',
        align: 'center',
        width: 100,
      },
      {
        title: `使用时间`,
        dataIndex: 'useTime',
        align: 'center',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
    ],
  });
  const searchFormSchema = reactive({
    data: [
      {
        component: 'Input', 
        label: `用户名称`,
        field: 'storeName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请输入用户名称`,
        },
      },
      
      {
        colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
        component: 'RangePicker',
        label: `领取时间`,
        field: '[receiveStartTime,receiveEndTime]',
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: ['开始时间', '结束时间'],
        },
      },
      {
        colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
        component: 'RangePicker',
        label: `使用时间`,
        field: '[useStartTime,useEndTime]',
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: ['开始时间', '结束时间'],
        },
      },
      {
        component: 'Select',
        label: `使用状态`,
        field: 'useState',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请选择使用状态`,
          options: [
            { value: '', label: `全部` },
            { value: '1', label: `未使用` },
            { value: '2', label: `已使用` },
            { value: '3', label: `已过期` },
          ],
        },
      },
    ],
  });

  // 表格数据
  const [registerTable] = useTable({
    // 请求接口
    api: getCouponReceiveListsApi,
    // 表头
    columns: columns.data,
    immediate: true,
    // 参数
    searchInfo: searchInfo.value,
    // 点击搜索前处理的参数
    beforeFetch(values) {
      values.receiveStartTime = values.receiveStartTime
        ? values.receiveStartTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.receiveEndTime = values.receiveEndTime
        ? values.receiveEndTime.split(' ')[0] + ' 23:59:59'
        : undefined;
      values.useStartTime = values.useStartTime
        ? values.useStartTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.useEndTime = values.useEndTime
        ? values.useEndTime.split(' ')[0] + ' 23:59:59'
        : undefined;
      return values;
    },
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: searchFormSchema.data,
    },
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    // 是否显示序号列
    showIndexColumn: true,
  });

  onMounted(() => {});
</script>
<style lang="less" scoped>
  @import './style/system_lists.less';

  p {
    margin-bottom: 0;
  }

  .member_receive_lists {
    padding: 10px;

    .member_receive_lists_back {
      width: 100%;
      min-height: calc(100vh - @header-height - 20px);
      padding: 10px;
      background-color: #fff;
    }
  }
</style>
