<template>
  <div class="coupon_home_setting section_padding_tab_top">
    <Spin :spinning="loading">
      <StandardTableRow
        width="100%"
        :data="info_data.data"
        @callback-event="callbackEvent"
        @submit-event="submitEvent"
      />
    </Spin>
  </div>
</template>
<script>
  export default {
    name: 'CouponHomeSetting',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { Spin } from 'ant-design-vue';
  import StandardTableRow from '/@/components/StandardTableRow/index.vue';
  import { getSettingListApi, getSettingUpdateApi } from '/@/api/common/common';
  import { sucTip } from '/@/utils/utils';
  import { useUserStore } from '/@/store/modules/user';

  const userStore = useUserStore();

  const coupon_is_enable = ref('')

  const info_data = reactive({
    data: [],
  });

  const loading = ref(true);

  // 获取数据
  const get_setting = async () => {
    try {
      const res = await getSettingListApi({ str: 'supplier_coupon_is_enable,supplier_coupon_expired_reminder' });
      if (res && res.state == 200) {
        loading.value = false;
        info_data.data = [];
        for (let i in res.data) {
          if (res.data[i].type == 1) {
            if (res.data[i].name == 'supplier_coupon_expired_reminder') {
              info_data.data.push({
                type: 'inputnum',
                label: res.data[i].title,
                desc: res.data[i].description,
                key: res.data[i].name,
                placeholder: '',
                min: 0,
                max: 1439, //1440为1天，小于1天
                value: res.data[i].value,
              });
            }
          } else if (res.data[i].type == 4) {
            if(res.data[i].name == 'supplier_coupon_is_enable'){
              coupon_is_enable.value = res.data[i].value
            }
            info_data.data.push({
              type: 'switch',
              label: res.data[i].title,
              desc: res.data[i].description,
              key: res.data[i].name,
              placeholder: '',
              value: res.data[i].value,
              checkedValue: '1',
              unCheckedValue: '0',
            });
          }
        }
        if (info_data.data.length > 0) {
          info_data.data.push({
            type: 'button',
            showSubmit: true,
            submitText: '保存',
            showCancle: false,
          });
        }
      }
    } catch (error) {}
  };

  // 更新数据
  const callbackEvent = (item) => {
    let temp = info_data.data.filter((item) => item.key == item.key);
    if (temp.length > 0) {
      temp[0].value = item.val1;
    }
  };

  // 点击保存
  const submitEvent = async (val) => {
    try {
      loading.value = true;
      let res = await getSettingUpdateApi(val);
      if (res.state == 200) {
        loading.value = false;
        sucTip(res.msg);
        get_setting();
        if(val.coupon_is_enable!=coupon_is_enable){
          userStore.setDelKeepAlive(['AddCoupon','CopyCoupon','EditCoupon'])
        }

      }
    } catch (error) {}
  };

  onMounted(() => {
    get_setting();
  });
</script>
<style lang="less"></style>
