<template>
  <div class="coupon_home section_padding">
    <div class="section_padding_back">
      <SldComHeader title="优惠券管理" />
      <Tabs type="card" v-model:activeKey="activeKey">
        <TabPane key="1" tab="优惠券设置">
          <CouponHomeSetting v-if="activeKey == '1'"/>
        </TabPane>
        <TabPane key="2" tab="店铺优惠券">
          <div class="section_padding_tab_top"></div>
          <StoreCoupon  />
        </TabPane>
        <TabPane key="3" tab="风格配置">
          <div class="section_padding_tab_top"></div>
          <CouponDiyStylePc v-if="activeKey == '3'" />
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'CouponHome',
  };
</script>
<script setup>
  import { ref, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { Tabs, TabPane, message, RadioGroup, RadioButton } from 'ant-design-vue';
  import StoreCoupon from './store_coupon.vue';
  import CouponHomeSetting from './setting.vue';
  import CouponDiyStylePc from './diy_style_pc.vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { useUserStore } from '/@/store/modules/user';

  const router = useRouter();
  const userStore = useUserStore();

  const RadioGroupValue = ref('1');

  const activeKey = ref('1');

  onMounted(() => {
    if(userStore.getUpdateTab.length>0){
      let index = userStore.getUpdateTab.findIndex(item=>item.name == 'CouponHome')
      if(index>-1){
        activeKey.value = userStore.getUpdateTab[index].index
        userStore.setDelTab('CouponHome')
      }
    }
  });
</script>
<style lang="less">
  .coupon_home {
    padding: 10px;

    .coupon_home_back {
      width: 100%;
      min-height: calc(100vh - @header-height - 20px);
      padding: 10px;
      background-color: #fff;
    }
  }

  .RadioGroup_back {
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within {
      box-shadow: none;
    }
  }
</style>
