.coupon_diy_style {
  height: calc(100vh - 48px - 20px - 20px - 32px - 15px - 48px - 32px - 30px);
  padding: 10px 0;
  overflow: auto;

  .diy_style_list {
    .diy_style_item {
      position: relative;
      width: 122px;
      height: 48px;
      margin-right: 10px;
      margin-bottom: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      cursor: pointer;

      &.active {
        border-color: @primary-color;
      }

      .diy_style_color {
        width: 46px;
        height: 20px;
        overflow: hidden;

        .diy_style_color_left {
          flex: 1;
          height: 20px;
        }

        .diy_style_color_middle {
          flex: 1;
          height: 20px;
          margin-right: 2px;
          margin-left: 2px;
        }

        .diy_style_color_right {
          flex: 1;
          height: 20px;
        }
      }

      .diy_style_name {
        margin-left: 8px;
        color: #666;
        font-size: 12px;
        text-align: center;
      }

      .diy_style_auto_name {
        color: @primary-color;
        font-size: 12px;
      }

      .diy_style_auto_arrow {
        flex-shrink: 0;
        width: 10px;
        height: 22px;
        margin-left: 5px;
        color: @primary-color;
        font-family: cursive;
        font-size: 14px;
        font-weight: 600;
      }

      .diy_style_checked {
        position: absolute;
        z-index: 1;
        right: -1px;
        bottom: -1px;
        width: 20px;
        height: 20px;
      }
    }
  }

  .prew_title {
    margin-bottom: 10px;
    color: #222;
    font-family: 'PingFang SC';
    font-size: 15px;
    font-weight: bold;
    line-height: 40px;
  }

  .prew_list {
    padding-left: 15px;

    .prew_item {
      position: relative;
      width: 375px;
      height: 802px;
      margin-right: 50px;
      overflow: hidden;
      border-radius: 10px;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      box-shadow: 0 0 12px 0 rgb(129 129 129 / 30%);
      cursor: default;

      &:last-of-type {
        margin-right: 0;
      }

      &.prew_item_center {
        .top_nav {
          position: absolute;
          z-index: 2;
          top: 182px;
          left: 24px;
          padding-bottom: 4px;
          border-bottom: 4px solid;
          font-family: 'PingFang SC';
          font-size: 14px;
          font-weight: bold;
        }

        .item {
          position: absolute;
          z-index: 2;
          left: 50%;
          width: 354px;
          height: 134px;
          margin-left: -177px;
          border-radius: 10px;

          &:nth-child(2) {
            top: 222px;
          }

          &:nth-child(3) {
            top: 366px;
          }

          &:nth-child(4) {
            top: 510px;
          }

          &:nth-child(5) {
            top: 654px;
          }

          .item_top {
            .item_top_left {
              width: 248px;
              height: 95px;
              border-radius: 10px 0 0 10px;

              .item_top_price {
                width: 80px;
                height: 62px;
                margin-right: 10px;
                text-align: center;

                div {
                  font-family: 'PingFang SC';
                  font-size: 12px;
                  font-weight: 500;

                  &:nth-child(2) {
                    margin-top: 5px;
                    font-size: 18px;

                    span {
                      font-size: 28px;
                      font-weight: 800;
                    }
                  }
                }
              }

              .item_top_desc {
                height: 62px;
                font-family: 'PingFang SC';
                font-weight: 500;

                span {
                  &:nth-child(1) {
                    color: #111;
                    font-family: 'PingFang SC';
                    font-size: 15px;
                    font-weight: bold;
                  }

                  &:nth-child(2) {
                    color: #333;
                    font-size: 12px;
                  }
                }
              }
            }

            .item_top_right {
              width: 106px;
              height: 95px;
              border-radius: 0 10px 10px 0;
              background: #fff;

              .item_top_right_progras {
                div {
                  &:nth-child(1) {
                    position: relative;
                    width: 45px;
                    height: 5px;
                    border: 1px solid;
                    border-radius: 3px;

                    span {
                      display: inline-block;
                      position: absolute;
                      z-index: 3;
                      top: 0;
                      bottom: 0;
                      left: -1px;
                      border-top-right-radius: 3px;
                      border-bottom-right-radius: 3px;
                    }
                  }

                  &:nth-child(2) {
                    margin-left: 6px;
                    color: #333;
                    font-family: 'PingFang SC';
                    font-size: 12px;
                    font-weight: 500;
                  }
                }
              }

              .item_top_right_btn {
                width: 70px;
                height: 26px;
                margin-top: 8px;
                border-radius: 13px;
                background: linear-gradient(-90deg, rgb(221 24 20 / 80%) 100%);
                color: #fff;
                font-family: 'PingFang SC';
                font-size: 12px;
                font-weight: 500;
                line-height: 26px;
                text-align: center;
              }
            }
          }

          .item_bottom {
            position: relative;
            width: 354px;
            height: 39px;
            padding-left: 15px;
            border-radius: 10px;
            background: #fff;
            color: #666;
            font-family: 'PingFang SC';
            font-size: 12px;
            font-weight: 500;
            line-height: 38px;

            &::before {
              content: '';
              position: absolute;
              z-index: 3;
              top: 0;
              left: 50%;
              width: 338px;
              height: 1px;
              margin-left: -169px;
              border: 1px dashed #aaa;
              opacity: 0.3;
            }
          }
        }
      }

      &.prew_item_list {
        .top_nav {
          position: absolute;
          z-index: 2;
          top: 88px;
          left: 20px;
          padding-bottom: 4px;
          border-bottom: 3px solid;
          font-family: 'PingFang SC';
          font-size: 16px;
          font-weight: 800;
        }

        .item {
          position: absolute;
          z-index: 2;
          left: 50%;
          width: 354px;
          height: 96px;
          margin-left: -177px;
          border-radius: 10px;
          background: #fff;

          &:nth-child(2) {
            top: 255px;
          }

          &:nth-child(3) {
            top: 361px;
          }

          &:nth-child(4) {
            top: 467px;
          }

          &:nth-child(5) {
            top: 573px;
          }

          &:nth-child(6) {
            top: 679px;
          }

          .label {
            position: absolute;
            z-index: 3;
            top: -2px;
            left: -2px;
            width: 48px;
            height: 18px;
            transform: scale(0.85);
            border-radius: 8px 0;
            color: #fff;
            font-family: 'PingFang SC';
            font-size: 12px;
            font-weight: 500;
            line-height: 18px;
            text-align: center;
          }

          .price {
            flex-shrink: 0;
            width: 100px;
            margin-right: 16px;
            border-right: 1px solid #ddd;

            div {
              font-family: 'PingFang SC';
              font-weight: 500;

              &:nth-child(1) {
                align-items: baseline;
                font-size: 18px;

                span {
                  font-size: 28px;
                  font-weight: 800;
                }
              }

              &:nth-child(2) {
                font-size: 12px;
              }
            }
          }

          .desc {
            flex-shrink: 0;
            width: 172px;
            height: 50px;

            div {
              &:nth-child(1) {
                color: #111;
                font-family: 'PingFang SC';
                font-size: 16px;
                font-weight: bold;
              }

              &:nth-child(2) {
                color: #333;
                font-family: 'PingFang SC';
                font-size: 12px;
                font-weight: 500;
              }
            }
          }

          .btn {
            flex: 1;
            height: 96px;

            svg {
              position: relative;
              right: 15px;
            }

            span {
              position: absolute;
              top: 50%;
              right: 8px;
              transform: translateY(-50%);
              color: #fff;
              font-family: 'PingFang SC';
              font-size: 12px;
              font-weight: bold;
            }
          }
        }
      }
    }
  }

  .prew_pc_list {
    .prew_item {
      width: 1274px;
      margin-bottom: 55px;

      .prew_item_title {
        flex-shrink: 0;
        width: 60px;
        margin-right: 18px;
        margin-left: 70px;
        color: #222;
        font-family: 'Microsoft YaHei';
        font-size: 14px;
        font-weight: 400;
        line-height: 35px;
        text-align: right;
      }

      .prew_item_center {
        position: relative;
        width: 1125px;
        height: 602px;
        overflow: hidden;
        border-radius: 4px;
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        box-shadow: 0 0 12px 0 rgb(129 129 129 / 30%);
        cursor: default;

        .top_nav {
          width: 1038px;
          margin: 18px auto;
          border-bottom: 1px solid #ddd;

          .top_nav_item {
            width: 75px;
            height: 26px;
            color: #333;
            font-family: 'Microsoft YaHei';
            font-size: 14px;
            font-weight: 400;
            line-height: 26px;
            text-align: center;

            &.active {
              border-radius: 4px 4px 0 0;
              color: #fff;
            }
          }
        }

        .coupon_list {
          flex-wrap: wrap;
          width: 1038px;
          margin: 28px auto 22px;

          .coupon_item {
            width: 343px;
            height: 135px;
            margin-right: 4px;
            margin-bottom: 13px;
            overflow: hidden;
            transform: scale(0.98);
            background-repeat: no-repeat;
            background-position: center;
            background-size: contain;

            &:nth-child(3n + 3) {
              margin-right: 0;
            }

            .coupon_item_left {
              flex-shrink: 0;
              width: 70px;
              padding: 0 20px;
              font-family: 'Microsoft YaHei';
              font-size: 17px;
              font-weight: bold;
              line-height: 1.2;
              text-align: center;
            }

            .coupon_item_middle {
              flex: 1;
              height: 106px;
              font-family: 'Microsoft YaHei';
              font-size: 12px;
              font-weight: 400;

              .coupon_item_price {
                font-size: 28px;
                line-height: 1;

                span {
                  padding-top: 5px;
                  font-size: 14px;
                }
              }

              .coupon_item_desc {
                width: 74px;
                height: 24px;
                border-radius: 2px;
                background: #f1f1f1;
                color: #666;
                line-height: 24px;
                text-align: center;
              }

              .coupon_item_time {
                color: #666;
              }

              .coupon_item_rule {
                color: #666;

                span {
                  color: #999;
                }
              }
            }

            .coupon_item_right {
              flex-shrink: 0;
              width: 90px;

              .coupon_item_percent {
                span {
                  font-family: 'Microsoft YaHei';
                  font-size: 14px;
                  font-weight: 400;

                  &:nth-child(2) {
                    margin-top: 5px;
                  }
                }
              }

              .coupon_item_btn {
                width: 78px;
                height: 22px;
                margin-top: 4px;
                border-radius: 13px;
                color: #fefefe;
                font-family: 'Microsoft YaHei';
                font-size: 13px;
                font-weight: 400;
                line-height: 22px;
                text-align: center;
              }
            }
          }
        }

        .pagenation {
          .pagenation_left {
            div {
              width: 32px;
              height: 28px;
              border-radius: 2px;
              background: #fff;
              line-height: 28px;
              text-align: center;

              &:nth-child(1) {
                border: 1px solid #eee;
                border-right: none;
                color: #ccc;
              }

              &:nth-child(2),
              &:nth-child(3) {
                border: 1px solid #eee;
                font-weight: 700;
              }

              &:nth-child(4) {
                border: 1px solid #eee;
                border-left: none;
              }
            }
          }

          .pagenation_middle {
            margin-right: 10px;
            margin-left: 20px;
          }

          .pagenation_right {
            width: 42px;
            height: 28px;
            border: 1px solid #ddd;
            border-radius: 2px;
            background: #fff;
            font-size: 12px;
            line-height: 28px;
            text-align: center;
          }
        }
      }

      .prew_item_list {
        position: relative;
        width: 1125px;
        height: 639px;
        overflow: hidden;
        border-radius: 4px;
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        box-shadow: 0 0 12px 0 rgb(129 129 129 / 30%);
        cursor: default;

        .top_nav {
          position: absolute;
          top: 48px;
          left: 251px;
          font-family: 'Microsoft YaHei';
          font-size: 14px;
          font-weight: 500;
        }

        .coupon_list {
          position: absolute;
          z-index: 2;
          top: 92px;
          left: 218px;
          flex-wrap: wrap;
          width: 850px;
          height: 486px;
          overflow: hidden;

          .coupon_item {
            position: relative;
            width: 208px;
            height: 236px;
            margin-right: 6px;
            margin-bottom: 6px;
            transform: scale(0.98);
            font-family: 'Microsoft YaHei';
            font-size: 12px;
            font-weight: 400;

            &:nth-child(4n + 4) {
              margin-right: 0;
            }

            .coupon_item_top {
              position: absolute;
              z-index: 2;
              top: 0;
              right: 0;
              left: 0;
              width: 208px;
              height: 110px;
              border-top-left-radius: 5px;
              border-top-right-radius: 5px;
              text-align: center;

              .coupon_item_price {
                margin-top: 14px;
                margin-bottom: 5px;
                color: #fff;
                font-size: 18px;

                span {
                  margin-top: 2px;
                  font-size: 13px;
                }
              }

              .coupon_item_desc {
                margin-bottom: 5px;
                color: #fff;
                font-size: 14px;
              }

              .coupon_item_time {
                color: #fefefe;
              }
            }

            .coupon_item_bottom {
              position: absolute;
              z-index: 3;
              right: 0;
              bottom: 0;
              left: -2px;
              width: 212px;
              height: 135px;
              background-repeat: no-repeat;
              background-position: center;
              background-size: contain;

              .coupon_item_type {
                margin-top: 16px;
                margin-left: 18px;
                font-size: 14px;
              }

              .coupon_item_rule {
                margin-top: 6px;
                margin-left: 18px;
                color: #666;

                span {
                  color: #999;
                }
              }

              .coupon_item_btn {
                position: relative;
                margin-top: 42px;
                text-align: center;

                span {
                  margin-left: 4px;
                }

                &::before {
                  content: '';
                  display: inline-block;
                  position: absolute;
                  z-index: 2;
                  top: -10px;
                  left: 50%;
                  width: 190px;
                  margin-left: -95px;
                  border-top: 1px dashed #ddd;
                }
              }
            }
          }
        }
      }
    }
  }
}

.add_goods_bottom_btn_forbidden {
  margin-right: 0;
  border-color: #ddd;
  background: #ddd;
  color: #fff;
}

.diy_auto_color_modal {
  margin-top: 10px;
  margin-bottom: 10px;

  .color_title {
    width: 115px;
    margin-right: 5px;
    text-align: right;
  }

  .color_show {
    .show_color {
      display: inline-block;
      width: 50px;
      height: 30px;
      margin-right: 10px;
      padding: 5px;
      border: 1px solid #eee;
      line-height: 0;
      cursor: pointer;
    }

    span {
      display: inline-block;
      width: 100%;
      height: 100%;
    }
  }

  .color_picker_wrap {
    position: absolute;
    z-index: 2;

    .color_picker_mask {
      position: fixed;
      inset: 0;
    }
  }
}
