<template>
  <div class="store_coupon_lists">
    <BasicTable @register="registerTable" rowKey="couponId">
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex == 'publishNum'">
          <router-link
            :to="`/supplier_promotion/store_coupon_to_receive_list?id=${record.couponId}`"
          >
            <div class="voucher_num"
              >{{ record.receivedNum }}/{{ record.usedNum }}/{{ record.publishNum }}</div
            >
          </router-link>
        </template>
        <template v-if="column.dataIndex == 'publishStartTime'">
          <div v-if="record.publishType != 3">
            <p>{{ text }}</p>
            <p>~</p>
            <p>{{ record.publishEndTime }}</p>
          </div>
          <div v-else> -- </div>
        </template>
        <template v-if="column.dataIndex == 'effectiveStart'">
          <div v-if="record.cycle">
            {{ `领取后${record.cycle}天内` }}
          </div>
          <div v-else>
            <p>{{ text }}</p>
            <p>~</p>
            <p>{{ record.effectiveEnd }}</p>
          </div>
        </template>
        <template v-if="column.dataIndex == 'action'">
          <TableAction
            :actions="[
              {
                label: '查看',
                onClick: view.bind(null, record.couponId),
              },
              {
                label: '失效',
                ifShow: record.state == 3,
                popConfirm: {
                  title: '失效后不可恢复，是否确定失效？',
                  placement: 'left',
                  confirm: operate.bind(null, { couponId: record.couponId }, 'invalid'),
                },
              },
              {
                label: '删除',
                ifShow: record.state == 1 || record.state == 2 || record.state == 4,
                popConfirm: {
                  title: '删除后不可恢复，是否确定删除？',
                  placement: 'left',
                  confirm: operate.bind(null, { couponId: record.couponId }, 'del'),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
  </div>
</template>
<script>
  export default {
    name: 'StoreCoupon',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import {
    getCouponListApi,
    getCouponInvalidApi,
    getCouponDelApi,
  } from '/@/api/supplier/coupon';
  import { failTip, sucTip } from '/@/utils/utils';

  const router = useRouter();

  const columns = reactive({
    data: [
      {
        title: `店铺名称`,
        dataIndex: 'storeName',
        align: 'center',
        width: 100,
      },
      {
        title: `优惠券名称`,
        dataIndex: 'couponName',
        align: 'center',
        width: 100,
      },
      {
        title: `优惠券类型`,
        dataIndex: 'couponTypeValue',
        align: 'center',
        width: 100,
      },
      {
        title: `优惠内容`,
        dataIndex: 'couponContent',
        align: 'center',
        width: 100,
      },
      {
        title: `已领取/已使用/发布数`,
        dataIndex: 'publishNum',
        align: 'center',
        width: 150,
      },
      {
        title: `活动时间`,
        dataIndex: 'publishStartTime',
        align: 'center',
        width: 150,
      },
      {
        title: `使用时间`,
        dataIndex: 'effectiveStart',
        align: 'center',
        width: 150,
      },
      {
        title: `获取方式`,
        dataIndex: 'publishTypeValue',
        align: 'center',
        width: 100,
      },
      {
        title: `活动状态`,
        dataIndex: 'stateValue',
        align: 'center',
        width: 100,
      },
    ],
  });
  const searchFormSchema = reactive({
    data: [
      {
        component: 'Input',
        label: `店铺名称`,
        field: 'storeName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请输入店铺名称`,
        },
      },
      {
        component: 'Input',
        label: `优惠券名称`,
        labelWidth: 90,
        field: 'couponName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请输入优惠券名称`,
        },
      },
      {
        component: 'Select',
        label: `活动状态`,
        field: 'state',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请选择活动状态`,
          options: [
            { value: '', label: `全部` },
            { value: '1', label: `未开始` },
            { value: '2', label: `进行中` },
            { value: '3', label: `已失效` },
            { value: '4', label: `已结束` },
          ],
        },
      },
      {
        component: 'Select',
        label: `优惠券类型`,
        field: 'couponType',
        labelWidth: 90,
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请选择优惠券类型`,
          options: [
            { value: '', label: `全部` },
            { value: '1', label: `满减券` },
            { value: '2', label: `折扣券` },
            { value: '3', label: `随机金额券` },
          ],
        },
      },
      {
        component: 'Select',
        label: `获取方式`,
        field: 'publishType',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请选择获取方式`,
          options: [
            { value: '', label: `全部` },
            { value: '1', label: `免费领取` },
            { value: '3', label: `活动赠送` },
          ],
        },
      },
      {
        colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
        component: 'RangePicker',
        label: `活动时间`,
        field: '[publishStartTime,publishEndTime]',
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: ['开始时间', '结束时间'],
        },
      },
      {
        colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
        component: 'RangePicker',
        label: `使用时间`,
        field: '[effectiveStart,effectiveEnd]',
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: ['开始时间', '结束时间'],
        },
      },
    ],
  });

  // 表格数据
  const [registerTable, { reload }] = useTable({
    // 请求接口
    api: getCouponListApi,
    // 表头
    columns: columns.data,
    immediate: true,
    // 参数
    // 点击搜索前处理的参数
    beforeFetch(values) {
      values.publishStartTime = values.publishStartTime
        ? values.publishStartTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.publishEndTime = values.publishEndTime
        ? values.publishEndTime.split(' ')[0] + ' 23:59:59'
        : undefined;
      values.effectiveStart = values.effectiveStart
        ? values.effectiveStart.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.effectiveEnd = values.effectiveEnd
        ? values.effectiveEnd.split(' ')[0] + ' 23:59:59'
        : undefined;
      return values;
    },
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: searchFormSchema.data,
    },
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    // 取消...
    ellipsis: false,
    // 是否显示序号列
    showIndexColumn: true,
    // 表格右侧操作列配置
    actionColumn: {
      width: 180,
      title: '操作',
      dataIndex: 'action',
      // slots: { customRender: 'action' },
    },
  });

  //优惠券操作  type: invalid 失效 copy 复制  del 删除
  const operate = async (id, type) => {
    let param_data = {};
    let res = null;
    if (type == 'invalid') {
      param_data = id;
      res = await getCouponInvalidApi(param_data);
    }
    if (type == 'del') {
      param_data = id;
      res = await getCouponDelApi(param_data);
    }
    if (res.state == 200) {
      sucTip(res.msg);
      reload();
    } else {
      failTip(res.msg);
    }
  };

  const view = (id) => {
    router.push({
      path: `/supplier_promotion/store_coupon_to_view`,
      query: { id: id, type: 'system' },
    });
  };

  onMounted(() => {});
</script>
<style lang="less">
  @import './style/store_coupon.less';

  p {
    margin-bottom: 0;
  }

  .store_coupon {
    padding: 10px;

    .store_coupon_back {
      width: 100%;
      min-height: calc(100vh - @header-height - 20px);
      padding: 10px;
      background-color: #fff;
    }
  }

  .RadioGroup_back {
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within {
      box-shadow: none;
    }
  }
</style>
