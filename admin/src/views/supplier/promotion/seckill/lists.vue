<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader title="秒杀活动" />
      <Tabs type="card" v-model:activeKey="activeKey">
        <TabPane key="1" tab="秒杀活动">
          <SeckillLists class="section_padding_tab_top"/>
        </TabPane>
        <TabPane key="2" tab="秒杀标签">
          <LabelLists class="section_padding_tab_top"></LabelLists>
        </TabPane>
        <TabPane key="3" tab="秒杀设置">
          <Setting class="section_padding_tab_top"></Setting>
        </TabPane>
        <TabPane key="4" tab="风格配置">
          <div class="section_padding_tab_top"></div>
          <SeckillDiyStylePc v-if="activeKey == '4'" />
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'SupplierSeckillIndexList',
  };
</script>
<script setup>
  import { ref, onMounted } from 'vue';
  import { Tabs, TabPane } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import SeckillLists from './seckill_lists.vue';
  import LabelLists from './label_lists.vue';
  import SeckillDiyStylePc from './diy_style_pc.vue';
  import Setting from './setting.vue';

  const activeKey = ref('1');

  const RadioGroupValue = ref('1');

  onMounted(() => {});
</script>
<style lang="less">
  .coupon_home {
    padding: 10px;

    .coupon_home_back {
      width: 100%;
      min-height: calc(100vh - @header-height - 20px);
      padding: 10px;
      background-color: #fff;
    }
  }

  .RadioGroup_back {
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within {
      box-shadow: none;
    }
  }
</style>
