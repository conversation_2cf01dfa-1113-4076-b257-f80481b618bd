p {
  margin-bottom: 0;
}

.supplier_order_lists {
  .ant-spin-nested-loading {
    flex: 1;

    .ant-spin-container {
      display: flex;
      flex-direction: column;
      height: 100%;
    }
  }

  .spin_height {
    display: flex;
    flex-direction: column;
    height: calc(100vh - @header-height - 40px - 76px);
  }
  .order_list {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    min-height: 200px;
    overflow-x: auto;
    overflow-y: hidden;
  
    .order_content_height {
      max-height: 100%;
      overflow: auto;
    }
  
    .pagination {
      width: 100%;
      margin-top: 15px;
      text-align: right;
    }
  
    .header {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
      width: 100%;
      height: 40px;
      margin-bottom: 10px;
      border-radius: 3px;
      background: #f5f5f5;
  
      li {
        flex-shrink: 0;
        height: 40px;
        background: #f5f5f5;
        color: #333;
        font-size: 14px;
        line-height: 30px;
      }
    }
  
    .order_content {
      flex: 1;
      width: 100%;
      overflow: auto;
  
      .item:first-child {
        margin-top: 0;
      }
  
      .item {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-start;
        width: 100%;
        margin-top: 10px;
        border-width: 0 1px 1px;
        border-style: solid;
        border-color: #ddd;
  
        .order_info {
          width: 100%;
          height: 35px;
          padding: 0 13px;
          background: #fff1e8;
  
          .left {
            margin-top: 1px;
            line-height: 22px;
  
            .num {
              margin-right: 20px;
              color: #999;
              font-size: 12px;
            }
  
            .order_sn {
              color: #666;
              font-size: 12px;
            }
  
            .order_type {
              position: relative;
              top: 2px;
              height: 20px;
              margin-left: 15px;
              background-image: url('../../../../assets/images/order/virtural_goods_order_icon.png');
              background-repeat: no-repeat;
              background-size: 100% 100%;
  
              .order_type_text {
                position: relative;
                color: #fff;
                font-size: 12px;
                left: 0;
                top: -2px;
                padding: 0 5px;
              }
            }
          }
  
          .create_time {
            color: #999;
            font-size: 12px;
          }
        }
  
        .order_goods_part {
          width: 100%;
  
          .goods_split {
            position: relative;
          }
  
          .goods_split::after {
            content: '';
            position: absolute;
            z-index: 3;
            top: 0;
            right: 0;
            bottom: 0;
            transform: scaleY(0.85);
            border-right: 1px solid rgb(0 0 0 / 5%);
          }
  
          .goods {
            .goods_item {
              .goods_img_wrap {
                flex-shrink: 0;
                width: 82px;
                height: 82px;
                margin: 15px 10px 15px 15px;
                border: 1px solid rgb(0 0 0 / 5%);
                background: #fff;
  
                img {
                  max-width: 100%;
                  max-height: 100%;
                }
              }
  
              .goods_info {
                .goods_name {
                  display: -webkit-box;
                  height: 40px;
                  overflow: hidden;
                  color: #333;
                  font-size: 14px;
                  line-height: 20px;
                  text-overflow: ellipsis;
                  word-break: break-word;
                  -webkit-line-clamp: 2;
  
                  /*! autoprefixer: off */
                  -webkit-box-orient: vertical;
                }
  
                .goods_spec {
                  display: -webkit-box;
                  margin-top: 12px;
                  overflow: hidden;
                  color: #666;
                  font-size: 12px;
                  line-height: 14px;
                  text-overflow: ellipsis;
                  -webkit-line-clamp: 2;
                  word-break: break-word;
  
                  /*! autoprefixer: off */
                  -webkit-box-orient: vertical;
                }
              }
  
              .goods_price {
                color: #333;
                font-size: 12px;
              }
  
              .buy_num {
                color: #333;
                font-size: 12px;
              }
            }
          }
  
          .member_info {
            .mem_name {
              padding: 0 5px;
              color: #333;
              font-size: 12px;
              word-break: break-word;
            }
  
            .mem_tel,
            .mem_email {
              padding: 0 5px;
              color: #666;
              font-size: 12px;
              word-break: break-word;
            }
          }
  
          .order_state {
            color: #666;
            font-size: 12px;
          }
  
          .pay_amount {
            color: #ff1818;
            font-size: 12px;
            font-weight: bold;
          }
        }
      }
    }
  
    .center {
      text-align: center;
    }
  
    .width_8_5 {
      width: 8.2%;
      padding: 5px;
    }
  
    .width_10 {
      width: 10%;
      padding: 5px;
    }
    .width_9 {
      width: 9%;
      padding: 5px;
    }
  
    .width_40 {
      width: 40%;
      padding: 5px;
    }
  
    .width_30 {
      width: 30%;
      padding: 5px;
    }
  
    .width_32 {
      width: 32%;
      padding: 5px;
    }
  
    .width_50 {
      width: 50%;
      padding: 5px;
    }
  
    .width_57_5 {
      width: 57.5%;
      padding: 5px;
    }
  
    .width_60 {
      width: 60%;
      padding: 5px;
    }
  
    .pl_100 {
      padding-left: 100px;
    }
  }
}


/* 退货详情样式-start */
.progress {
  margin-top: 50px;

  .item {
    .top {
      position: relative;
      width: 215px;

      img {
        width: 75px;
        height: 75px;
      }

      .left_line,
      .right_line {
        content: '';
        position: absolute;
        top: 50%;
        width: 50px;
        height: 0;
        border-top: 1px solid #007fff;
      }

      .center_line {
        width: 70px;
        height: 70px;
        border-radius: 35px;
      }

      .left_line {
        left: 0;
      }

      .right_line {
        right: 0;
      }
    }

    .state {
      margin-top: 10px;
      font-size: 14px;
    }

    .time {
      font-size: 14px;
    }
  }

  .item.cur {
    .state {
      color: #007fff;
    }

    .time {
      color: rgb(0 127 255 / 50%);
    }

    .left_line,
    .right_line {
      border-color: #007fff;
    }
  }

  .item.no {
    .state {
      color: #999;
    }

    .left_line,
    .right_line {
      border-color: #eee;
    }
  }

  .item.pass {
    .state {
      color: rgb(0 127 255 / 50%);
    }

    .time {
      color: rgb(0 127 255 / 30%);
    }

    .left_line,
    .right_line {
      border-color: rgb(0 127 255 / 30%);
    }
  }
}

.state_part {
  margin-top: 50px;
  margin-bottom: 40px;

  .title {
    color: #333;
    font-size: 26px;
  }

  .tip {
    color: #999;
    font-size: 14px;
  }
}

.btnsty{
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  .agree_btn{
    height: 36px;
    line-height: 36px;
    text-align: center;
    border-radius: 3px;
    color: #fff;
    font-size: 14px;
    margin-top: 15px;
    padding: 0 10px;
    margin-right: 20px;
    background: @primary-color;
  }
  .refuse_btn{
    height: 36px;
    line-height: 36px;
    text-align: center;
    border: 1px solid  @primary-color;
    border-radius: 3px;
    color:  @primary-color;
    font-size: 14px;
    margin-top: 15px;
    padding: 0 10px;
  }
  .agree_btnnon{
    height: 36px;
    line-height: 36px;
    text-align: center;
    border: 1px solid #DDDDDD;
    border-radius: 3px;
    color: #BBBBBB;
    font-size: 14px;
    margin-top: 15px;
    padding: 0 10px;
    margin-right: 20px;
  }
  .lock_agree_btn{
    width: 120px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    border: 1px solid #DDDDDD;
    border-radius: 3px;
    color: #BBBBBB;
    font-size: 14px;
    margin-top: 15px;
    padding: 0 10px;
    margin-right: 20px;
  }
  .lock_refuse_btn{
    width: 120px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    border-radius: 3px;
    color: #fff;
    font-size: 14px;
    margin-top: 15px;
    padding: 0 10px;
    background: #DDDDDD;
  }
  .cancle_btn{
    width: 120px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    border: 1px solid rgba(255, 113, 30, 1);
    border-radius: 3px;
    color: rgba(255, 113, 30,1);
    font-size: 14px;
    margin-top: 15px;
    margin-right: 20px;
    padding: 0 10px;
    cursor: pointer;
  }
  .deliver_btn{
      width: 120px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    border-radius: 3px;
    color: #fff;
    font-size: 14px;
    margin-top: 15px;
    padding: 0 10px;
    background: rgba(255, 113, 30, 1);
    cursor: pointer;
  }
}
/* 退货详情样式-end */

.goods_info1 {
  .goods_detail {
    white-space: normal;
    flex: 1;
    height: 80px;
    margin-left: 10px;
  }

  .goods_img {
    display: inline-block;
    width: 80px;
    height: 80px;
    overflow: hidden;
    border: 1px solid rgb(226 229 246 / 100%);
    border-radius: 3px;
    background: rgb(248 248 248 / 100%);
  }

  .goods_name {
    display: -webkit-box;
    height: 34px;
    margin-top: 10px;
    overflow: hidden;
    color: #333;
    font-size: 14px;
    line-height: 17px;
    text-align: left;
    text-overflow: ellipsis;
    word-break: break-word;
    -webkit-line-clamp: 2;

    /*! autoprefixer: off */
    -webkit-box-orient: vertical;
  }

  .goods_brief {
    margin-bottom: 11px;
    overflow: hidden;
    color: #666;
    font-size: 12px;
    text-overflow: ellipsis;
    word-break: keep-all;
  }
}

.tableList {
  .tableListOperator {
    position: absolute;
    top: 0;
    right: 0;
    margin-bottom: 16px;
  }
}

.operaBtn {
  width: 110px;
  height: 25px;
  margin-bottom: 5px;
  border: 1px solid #fc701e;
  border-radius: 5px;
  color: #fc701e;
  line-height: 25px;
  text-align: center;
}

.order_goods_part {
  width: 100%;

  .operate {
    .operate_btn {
      width: 85px;
      height: 26px;
      margin-top: 11px;
      border: 1px solid rgb(0 0 0 / 20%);
      border-radius: 3px;
      color: #666;
      font-size: 12px;
      line-height: 26px;
      text-align: center;
      cursor: pointer;
    }

    .operate_btn:first-child {
      margin-top: 0;
    }

    .operate_btn:hover,
    .operate_btn:active {
      border-color: #eb6100;
      color: #eb6100;
    }
  }
}

.goods_info {
  .goods_detail {
    flex: 1;
    height: 80px;
    margin-left: 10px;
  }

  .goods_img {
    display: inline-block;
    width: 80px;
    height: 80px;
    overflow: hidden;
    border: 1px solid rgb(226 229 246 / 100%);
    border-radius: 3px;
    background: rgb(248 248 248 / 100%);
  }

  .goods_name {
    display: -webkit-box;
    flex-shrink: 0;
    height: 34px;
    overflow: hidden;
    color: #333;
    font-size: 14px;
    line-height: 17px;
    text-align: left;
    text-overflow: ellipsis;
    word-break: break-word;
    -webkit-line-clamp: 2;

    /*! autoprefixer: off */
    -webkit-box-orient: vertical;
  }

  .goods_brief {
    display: inline-block;
    width: 100%;
    overflow: hidden;
    color: #666;
    font-size: 12px;
    text-align: left;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.order_detail_total {
  width: 99%;
  height: 45px;
  border-radius: 6px;
  background: #fff;
  box-shadow: 0 0 8px 0 rgb(153 153 153 / 5%), 0 0 8px 0 rgb(153 153 153 / 5%);
  font-size: 14px;
  font-weight: bold;

  .amount_detail {
    color: #333;
  }

  .amount_total {
    color: #ff2b4e;
  }
}

:global {
  .ant-modal-confirm-info {
    .ant-modal-content {
      padding: 20px;
    }
  }

  .ant-modal-confirm-info .ant-modal-confirm-body > .anticon {
    color:  !important;
  }
}

.sld_common_title {
  margin-top: 15px;
  margin-bottom: 15px;
  margin-left: 5px;
  color: rgb(51 51 51);
  font-size: 14px;
  font-weight: bold;
}

.spell_order_detail {
  // .ant-table-body{
  //   overflow: auto !important;
  //   height: auto !important;
  // }
  .ant-descriptions-item-label {
    width: 20%;
  }

  .ant-descriptions-item-content {
    width: 30%;
  }

  .invoice_info_box {
    .ant-descriptions-item-content {
      width: 80%;
    }
  }
  // 详情页高度
  .height_detail {
    max-height: calc(100vh - @header-height - 88px);
    overflow: auto;
  }

  .order_detail_total {
    width: 99%;
    height: 45px;
    border-radius: 6px;
    background: #fff;
    box-shadow: 0 0 8px 0 rgb(153 153 153 / 5%), 0 0 8px 0 rgb(153 153 153 / 5%);
    font-size: 14px;
    font-weight: bold;

    .amount_detail {
      color: #333;
    }

    .amount_total {
      color: #ff2b4e;
    }
  }
}

.star_part {
  .star {
    display: none;
  }

  .add_star {
    display: inline-block;
    height: 18px;
  }

  &:hover {
    .star {
      display: flex;
    }

    .add_star {
      display: none;
    }
  }

  .operate_btn {
    cursor: pointer;
    color: #FF711E;
    font-size: 12px;
    margin-right: 12px;
  }
  .ant-rate{
    height: 18px;
  }
}
