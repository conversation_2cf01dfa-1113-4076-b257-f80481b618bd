<template>
  <div class="section_padding supplier_order_lists">
    <div class="section_padding_back_add section_padding_back_flex">
      <SldComHeader
        title="订单管理"
        tipBtn="订单导出"
        tipBtnIcon="iconziyuan23"
        @tip-btn-click="handleSldExcel"
      />
      <Spin :spinning="loading">
        <div class="spin_height">
          <BasicForm
            :tableFlag="true"
            ref="formRef"
            submitOnReset
            @register="registerForm"
            class="basic-form"
          ></BasicForm>
          <div style="margin-bottom: 10px">
            <RadioGroup size="small" v-model:value="filter_code" @change="clickFilter">
              <RadioButton
                v-for="(item, index) in filter_data"
                :key="index"
                :value="item.filter_code"
                >{{ item.filter_name }}</RadioButton
              >
            </RadioGroup>
          </div>
          <div class="order_list">
            <ul class="header">
              <li class="width_32 pl_100">商品信息</li>
              <li class="width_9 center">单价</li>
              <li class="width_9 center">数量</li>
              <li class="width_10 center">店铺信息</li>
              <li class="width_10 center">实付金额</li>
              <li class="width_10 center">付款方式</li>
              <li class="width_10 center">订单状态</li>
              <li class="width_10 center">操作</li>
            </ul>
            <div class="order_content">
              <Empty
                :image="Empty.PRESENTED_IMAGE_SIMPLE"
                v-if="data.list != undefined && data.list.length == 0"
              />
              <div
                class="order_content_height"
                v-if="data.list != undefined && data.list.length > 0"
              >
                <div class="item" v-for="(item, index) in data.list" :key="index">
                  <div class="order_info flex_row_between_center">
                    <div class="left flex_row_start_start">
                      <span class="order_sn">订单号：{{ item.orderSn }}</span>
                      <span class="order_sn" style="margin-left: 20px"
                        >供应商【{{ item.supplierStoreName }}】</span>
                      <div class="order_type" v-if="item.isVirtualGoods == 2">
                        <span class="order_type_text">虚拟商品订单</span>
                      </div>
                      <div class="order_type" v-if="item.saleModel">
                        <span class="order_type_text">{{item.saleModel==1?'代发订单':'批发订单'}}</span>
                      </div>
                     
                    </div>
                    <div class="flex_row_start_start">
                      <span class="create_time"> 下单时间：{{ item.createTime }}</span>
                    </div>
                  </div>
                  <div class="order_goods_part flex_row_start_center">
                    <div
                      class="goods flex_column_start_start width_50"
                      :class="{
                        goods_split:
                          item.orderProductListVOList != undefined &&
                          item.orderProductListVOList.length > 1,
                      }"
                    >
                      <template
                        v-if="
                          item.orderProductListVOList != undefined &&
                          item.orderProductListVOList.length > 0
                        "
                      >
                        <div
                          class="goods_item flex_row_start_center"
                          style="width: 100%"
                          v-for="(item_goods, index_goods) in item.orderProductListVOList"
                          :key="index_goods"
                        >
                          <div
                            class="flex_row_start_center"
                            :style="{ width: (320 / 500) * 100 + '%' }"
                          >
                            <div class="goods_img_wrap flex_row_center_center">
                              <img :src="item_goods.productImage" alt="" />
                            </div>
                            <div class="goods_info flex_column_start_start">
                              <span class="goods_name">{{ item_goods.goodsName }}</span>
                              <span class="goods_spec" v-if="item_goods.specValues">规格：{{ item_goods.specValues }}</span>
                              <span class="goods_spec" v-else style="width: 10px;height: 1px;"></span>
                            </div>
                          </div>
                          <span
                            class="goods_price center"
                            :style="{ width: (90 / 500) * 100 + '%' }"
                            >￥{{
                              item_goods.productShowPrice
                                ? Number(item_goods.productShowPrice).toFixed(2)
                                : 0
                            }}</span
                          >
                          <span class="buy_num center" :style="{ width: (90 / 500) * 100 + '%' }">{{
                            item_goods.productNum
                          }}</span>
                        </div>
                      </template>
                    </div>
                    <div class="member_info flex_column_center_center width_10">
                      <span class="mem_name" v-if="item.storeName">{{ item.storeName }}</span>
                      <span class="mem_name" v-if="item.vendorName">{{ item.vendorName }}</span>
                      <span class="mem_name" v-if="item.vendorMobile">{{ item.vendorMobile }}</span>
                      <span class="mem_name" v-if="!item.storeName&&!item.vendorMobile&&!item.vendorName">--</span>
                    </div>
                    <div class="pay_amount flex_column_center_center width_10">
                      <span class="mem_name"
                        >￥{{ item.orderAmount ? Number(item.orderAmount).toFixed(2) : 0 }}</span
                      >
                      <span class="mem_name" v-if="item.isFreeShipping==1" style="color:#666;font-weight: normal;">（免运费）</span>
                      <span class="mem_name" v-if="item.isFreeShipping==0" style="color:#666;font-weight: normal;">（含运费<span style="color:#ff1818;font-weight: bold;">￥{{ item.expressFee ? Number(item.expressFee).toFixed(2) : 0 }}</span>）</span
                      >
                    </div>
                    <div class="order_state width_10 center">
                      {{ item.paymentName || '--' }}
                    </div>
                    <div class="order_state width_10 center flex_column_center_center">
                      <span>{{ item.orderStateValue || '--' }}</span>
                    </div>
                    <div class="operate width_10 center flex_row_center_center">
                      <div class="operate_btn" @click="goDetail(item.orderSn)">查看详情</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              v-if="data.list != undefined && data.list.length > 0 && data.pagination != undefined"
              class="pagination"
            >
              <Pagination
                size="small"
                show-quick-jumper
                v-model:current="data.pagination.current"
                :show-total="(total) => `共 ${total} 条数据`"
                :total="data.pagination.total"
                :defaultPageSize="PAGE_SIZE"
                :pageSizeOptions="PAGE_SIZE_OPTIONS"
                @change="onPageChange"
              />
            </div>
          </div>
        </div>
      </Spin>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'SupplierOrderOrderLists',
  };
</script>
<script setup>
  import { ref, onMounted } from 'vue';
  import { RadioGroup, RadioButton, Spin, Empty, Pagination } from 'ant-design-vue';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { failTip, list_com_page_size_10 } from '/@/utils/utils';
  import { PAGE_SIZE, PAGE_SIZE_OPTIONS } from '/@/components/Table/src/const.ts';
  import { getOrderList, exportOrder } from '/@/api/supplier/order';
  import { validatorEmoji } from '/@/utils/validate';
  import { useRouter } from 'vue-router';
  import { useUserStore } from '/@/store/modules/user';

  const router = useRouter();
  const filter_code = ref('');
  const pageSize = ref(list_com_page_size_10);
  const userStore = useUserStore();

  const filter_data = ref([
    { filter_code: '', filter_name: `全部订单` },
    { filter_code: '10', filter_name: `待付款订单` },
    { filter_code: '20', filter_name: `待发货订单` },
    { filter_code: '31', filter_name: `部分发货订单` },
    { filter_code: '30', filter_name: `待收货订单` },
    { filter_code: '40', filter_name: `已完成订单` },
    { filter_code: '0', filter_name: `已取消订单` },
    { filter_code: '50', filter_name: `交易关闭订单` },
  ]);

  const loading = ref(false);
  const data = ref({
    list: [],
  });
  const params = ref({ pageSize: pageSize.value });
  const formValues = ref({}); //搜索条件

  const handleSldExcel = async () => {
    let paramData = {
      ...params.value,
      ...formValues.value,
    };
    if (filter_code.value) {
      paramData.orderState = filter_code.value;
    }
    paramData.fileName = `订单导出`;
    await exportOrder(paramData);
  };

  //表单
  const [registerForm, { validate, getFieldsValue }] = useForm({
    schemas: [
      {
        field: 'orderSn',
        component: 'Input',
        colProps: { span: 6, style: 'width:250px;max-width:100%;flex:none' },
        componentProps: {
          minWidth: 300,
          placeholder: '请输入订单号',
          size: 'default',
        },
        label: '订单号',
        labelWidth: 75,
        rules: [
          {
            // @ts-ignore
            validator: async (rule, value) => {
              await validatorEmoji(rule, value);
            },
            trigger: 'change',
          },
        ],
      },
      {
        field: 'goodsName',
        component: 'Input',
        colProps: { span: 6, style: 'width:250px;max-width:100%;flex:none' },
        componentProps: {
          minWidth: 300,
          placeholder: '请输入商品名称',
          size: 'default',
        },
        label: '商品名称',
        labelWidth: 75,
        rules: [
          {
            // @ts-ignore
            validator: async (rule, value) => {
              await validatorEmoji(rule, value);
            },
            trigger: 'change',
          },
        ],
      },
      {
        field: 'supplierStoreName',
        component: 'Input',
        componentProps: {
          placeholder: '请输入供应商',
        },
        label: '供应商',
        rules: [
          {
            // @ts-ignore
            validator: async (rule, value) => {
              await validatorEmoji(rule, value);
            },
            trigger: 'change',
          },
        ],
      },
      {
        field: 'storeName',
        component: 'Input',
        componentProps: {
          placeholder: '请输入店铺名称',
        },
        label: '店铺名称',
        rules: [
          {
            // @ts-ignore
            validator: async (rule, value) => {
              await validatorEmoji(rule, value);
            },
            trigger: 'change',
          },
        ],
      },
      {
        field: 'userName',
        component: 'Input',
        componentProps: {
          placeholder: '请输入店铺账号',
        },
        label: '店铺账号',
        rules: [
          {
            // @ts-ignore
            validator: async (rule, value) => {
              await validatorEmoji(rule, value);
            },
            trigger: 'change',
          },
        ],
      },
      {
        field: 'userMobile',
        component: 'Input',
        componentProps: {
          placeholder: '请输入店铺手机号',
        },
        label: '店铺手机号',
        rules: [
          {
            // @ts-ignore
            validator: async (rule, value) => {
              await validatorEmoji(rule, value);
            },
            trigger: 'change',
          },
        ],
      },
      {
        field: 'isVirtualGoods',
        component: 'Select',
        colProps: { span: 6, style: 'width:250px;max-width:100%;flex:none' },
        componentProps: {
          placeHolder: '请选择订单类型',
          minWidth: 300,
          options: [
            { value: '', label: '全部' },
            { value: '1', label: '实物商品订单' },
            { value: '2', label: '虚拟商品订单' },
          ],
        },
        label: '订单类型',
        labelWidth: 75,
      },
      {
        field: 'saleModel',
        component: 'Select',
        componentProps: {
          placeHolder: '请选择销售模式',
          minWidth: 300,
          options: [
            { value: '', label: '全部' },
            { value: '1', label: '代发订单' },
            { value: '2', label: '批发订单' },
          ],
        },
        label: '销售模式',
        labelWidth: 75,
      },
      {
        field: 'fieldTime',
        component: 'RangePicker',
        colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: ['开始时间', '结束时间'],
        },
        label: `下单时间`,
        labelWidth: 75,
      },
      {
        field: 'tradeSn',
        component: 'Input',
        colProps: { span: 6, style: 'width:250px;max-width:100%;flex:none' },
        componentProps: {
          minWidth: 300,
          placeholder: '请输入支付流水号',
          size: 'default',
        },
        label: '支付流水号',
        labelWidth: 75,
        rules: [
          {
            // @ts-ignore
            validator: async (rule, value) => {
              await validatorEmoji(rule, value);
            },
            trigger: 'change',
          },
        ],
      },
    ],
    fieldMapToTime: [['fieldTime', ['startTime', 'endTime'], 'YYYY-MM-DD']],
    labelWidth: 80,
    baseColProps: { span: 6 },
    actionColOptions: { span: 24 },
    autoSubmitOnEnter: true,
    showAdvancedButton: true,
    submitFunc: search,
    resetFunc: reset,
  });

  const resetFlag = ref(false)

  // 搜索
  async function search() {
    await validate();
    let values = getFieldsValue();
    values.startTime = values.startTime ? values.startTime.split(' ')[0] + ' 00:00:00' : undefined;
    values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
    for (let i in values) {
      if (values[i] == undefined) {
        delete values[i];
      }
    }
    formValues.value = values;
    let curParams = { ...params.value, ...formValues.value };
    if (filter_code.value&&!resetFlag.value) {
      curParams.orderState = filter_code.value;
    }
    if(resetFlag.value){
      filter_code.value = ''
      resetFlag.value = false
    }
    get_list(curParams);
  }

  async function reset() {
    resetFlag.value = true
    params.value.current = 1;
  }

  //订单条件过滤器
  const clickFilter = (e) => {
    filter_code.value = e.target.value;
    let param = { pageSize: pageSize.value, current: 1, ...formValues.value };
    if (e.target.value) {
      param.orderState = e.target.value;
    }
    get_list(param);
  };

  //获取数据列表
  const get_list = async (param) => {
    loading.value = true;
    let res = await getOrderList({ ...param });
    if (res.state == 200) {
      loading.value = false;
      if (param.current > 1 && res.data.list.length == 0 && params.value.current > 1) {
        param.current = param.current - 1;
        get_list(params);
      } else {
        data.value = res.data;
      }
    } else {
      loading.value = false;
      failTip(res.msg);
    }
  };

  //改变页码
  const onPageChange = (page, pageSize) => {
    let curParams = {...formValues.value };
    if (filter_code.value) {
      curParams.orderState = filter_code.value;
    }
    formValues.value = curParams
    params.value = { pageSize: pageSize, current: page};
    get_list({...params.value,...curParams});
  };

  // 查看详情
  const goDetail = (orderSn) => {
    userStore.setDelKeepAlive(['OrderSupplierListToDetail'])
    setTimeout(()=>{
      router.push(`/supplier_order/order_lists_to_detail?orderSn=${orderSn}`);
    })
  };

  onMounted(() => {
    get_list({ pageSize: pageSize.value });
  });
</script>
<style lang="less">
  @import './style/order.less';
</style>
