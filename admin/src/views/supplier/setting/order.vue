<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <SldComHeader
        :title="'运营配置'"
      />
      <Tabs v-model:activeKey="tabIndex" type="card" @change="handleTab">
        <TabPane key="1" tab="基础配置" />
        <a-tab-pane key="2" tab="订单导出配置" />
      </Tabs>
      <div class="section_padding_tab_top"></div>
      <template v-if="tabIndex == '1'">
        <div class="app_set_scroll">
          <StandardTableRow
            width="100%"
            :data="configuration"
            style="padding: 0"
            @submit-event="submitEvent"
          />
        </div>
      </template>
      <template v-if="tabIndex == '2'">
        <div class="app_set_scroll">
          <StandardTableRow
            width="100%"
            style="padding: 0;"
            :data="exportData"
            @callback-event="callbackEvent"
            @submit-event="submitEvent"
          />
        </div>
      </template>
    </div>
  </div>
</template>
<script setup>
  import {  ref, onMounted } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import StandardTableRow from '/@/components/StandardTableRow/index.vue';
  import { Tabs,TabPane } from 'ant-design-vue';
  import { getSettingList, saveBasicSiteSetting,getOrderOutputCode, } from '/@/api/supplier/setting';
  import { failTip, sucTip } from '/@/utils/utils';

  const tabIndex = ref('1'); //tab下标
  const exportData = ref([]); //订单导出配置列表
  const outputCode = ref([]); //订单导出配置字段
  const configuration = ref([]); //基础设置列表
  const clickEvent = ref(false);

  function handleTab(e) {
    if (e == '1') {
      get_setting_list({
        str: 'supplier_is_enable,supplier_sales_model',
      });
    }else if (e == '2') {
      get_output_code();
    }
  }

  //获取配置信息
  const get_setting_list = async (params) => {
    const res = await getSettingList(params);
    if (res.state == 200 && res.data) {
      let data = [];

      res.data.map((item) => {
        if(tabIndex.value == '2'){
          data.push({
            type: 'checkbox',
            label: item.title,
            key: item.name,
            width: 300,
            right_width: 600,
            checkbox_width: 150,
            initValue: '',
            value: item.value.split(','),
            callback: true,
            data: outputCode.value,
          });
        }else{
          if (item.type == 4) {
            data.push({
              type: 'switch',
              label: item.title,
              desc: item.description,
              key: item.name,
              width: 300,
              desc_width:400,
              placeholder: '',
              value: item.value,
              checkedValue: '1',
              disabled: item.name == 'express_delivery_is_enable' ? true : false,
              unCheckedValue: '0',
            });
          } else if (item.type == 6) {
            data.push({
              type: 'checkbox',
              label: item.title,
              key: item.name,
              width: 300,
              checkbox_margin_right: 20,
              initValue: '',
              value: item.value=='3'?['1','2']:item.value.split(','),
              callback: true,
              data: [
                { useable: item.value.indexOf('1') !== -1 ? true : false, value: '1', title: '代发' },
                { useable: item.value.indexOf('2') !== -1 ? true : false, value: '2', title: '批发' },
              ],
            });
          }
        }
      });
      if (data.length > 0) {
        data.push({
          type: 'button',
          width: 300,
          showSubmit: true,
          submitText: '保存',
          showCancle: false,
        });
      }
      if (tabIndex.value == '1') {
        configuration.value = data;
      }else if (tabIndex.value == '2') {
        exportData.value = data;
      }
    } else {
      failTip(res.msg);
    }
  };

  const callbackEvent = (item) => {
    let temp;
    if (tabIndex.value == '1') {
      temp = exportData.value.filter((items) => items.key == item.contentItem.key);
    }
    if (temp && temp.length > 0) {
      temp[0].value = item.val1;
    }
  };


  const submitEvent = (val) => {
    let params = {};
    params = val;
    if(tabIndex.value == '1'){
      if(params.supplier_sales_model){
        if(params.supplier_sales_model.length==0){
          failTip('销售模式至少要选择一项');
          return
        }
        if(params.supplier_sales_model.indexOf('1')!=-1&&params.supplier_sales_model.indexOf('2')!=-1){
          params.supplier_sales_model = '3'
        }else{
          params.supplier_sales_model = params.supplier_sales_model.join(',')
        }
      }else{
        failTip('销售模式至少要选择一项');
        return
      }

    }else if (tabIndex.value == '2') {
      let array_info = []
      for (let key in val) {
        if(key == 'supplier_order_list_code'){
          if(outputCode.value.length>0){
            outputCode.value.forEach(item=>{
              if(val[key].indexOf(item.key)!=-1){
                array_info.push(item.key)
              }
            })
          }
          params[key] = array_info.join(',');
        }else{
          params[key] = val[key].join(',');
        }
      }
    }
    save_base_site(params);
  };
  

  //获取订单导出字段
  const get_output_code = async () => {
    const res = await getOrderOutputCode();
    if (res.state == 200 && res.data) {
      outputCode.value = [];
      for (let key in res.data) {
        outputCode.value.push({
          key: key,
          value: key,
          title: res.data[key],
        });
      }
      get_setting_list({ str: 'supplier_order_list_code' });
    } else {
      failTip(res.msg);
    }
  };

  //保存站点基本配置
  const save_base_site = async (params) => {
    if (clickEvent.value) return;
    clickEvent.value = true;
    const res = await saveBasicSiteSetting(params);
    if (res.state == 200) {
      sucTip(res.msg);
    } else {
      failTip(res.msg);
    }
    clickEvent.value = false;
  };

  onMounted(() => {
    get_setting_list({ str: 'supplier_is_enable,supplier_sales_model' });
  });
</script>
<style lang="less" scoped></style>
