<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <a-spin :spinning="spinning">
        <SldComHeader
          :title="'站点配置'"
        />
        <div class="site_info_scroll">
          <StandardTableRow
            width="100%"
            :data="rowData"
            @callback-event="callbackEvent"
            @submit-event="submitEvent"
          />
        </div>
      </a-spin>
    </div>
  </div>
</template>
<script>
  import { defineComponent, ref, onMounted } from 'vue';
  import { Spin } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import StandardTableRow from '/@/components/StandardTableRow/index.vue';
  import { getSettingList, saveBasicSiteSetting } from '/@/api/supplier/setting';
  import { failTip, sucTip } from '/@/utils/utils';

  export default defineComponent({
    components: {
      [Spin.name]: Spin,
      SldComHeader,
      StandardTableRow,
    },
    setup() {
      const detail = ref({}); //初始数据
      const spinning = ref(false);
      const rowData = ref([]);
      const clickEvent = ref(false); //事件防抖动

      //获取站点基本配置
      const get_base_site = async () => {
        spinning.value = true;
        const res = await getSettingList({str: 'supplier_hot_search_words'});
        if (res.state == 200 && res.data) {
          detail.value = {};
          let data = [];
          res.data.map((item) => {
            if (item.type == 1) {
              data.push({
                type: 'input',
                label: item.title,
                key: item.name,
                placeholder: '请输入' + item.title,
                width: 300,
                initValue: '',
                value: item.value,
                inputType: 'text',
                callback: true,
                desc: item.description,
              });
              detail.value[item.name] = item.value;
            } else if (item.type == 4) {
              data.push({
                type: 'switch',
                label: item.title,
                key: item.name,
                width: 300,
                desc_width: 300,
                initValue: '',
                value: item.value == 1 ? true : false,
                callback: true,
                desc: item.description,
              });
              detail.value[item.name] = item.value == 1 ? true : false;
            }
          });
          if (data.length > 0) {
            data.push({
              type: 'button',
              width: 300,
              showSubmit: true,
              submitText: '保存',
              showCancle: false,
            });
          }
          rowData.value = data;
        } else {
          failTip(res.msg);
        }
        spinning.value = false;
      };

      const callbackEvent = (item) => {
        let temp = rowData.value.filter((items) => items.key == item.contentItem.key);
        if (temp.length > 0) {
          temp[0].value = item.val1;
        }
      };

      const submitEvent = (val) => {
        save_base_site(val);
      };

      //保存站点基本配置
      const save_base_site = async (params) => {
        if (clickEvent.value) return;
        clickEvent.value = true;
        const res = await saveBasicSiteSetting(params);
        if (res.state == 200) {
          sucTip(res.msg);
        } else {
          failTip(res.msg);
        }
        clickEvent.value = false;
      };


      onMounted(() => {
        get_base_site();
      });

      return {
        spinning,
        rowData,
        clickEvent,
        get_base_site,
        callbackEvent,
        submitEvent,
        save_base_site,
      };
    },
  });
</script>
<style lang="less" scoped></style>
