<template>
  <div class="open_screen p-2 mt--2 pb-11">
    <SldComHeaderTips :tipData="tipData"></SldComHeaderTips>

    <div class="mt-20px openScreen_scr">
      <Form v-bind="formItemLayoutModal" :model="advObject" ref="formRef">
        <Row type="flex" class="more_row">
          <Col flex="1" class="more_cell align_right">
            <div>
              <span class="table_left_con">图片</span>
              <span class="table_left_require">*</span>
            </div>
          </Col>
          <Col flex="8" class="more_cell">
            <div>
              <div class="imgs_wrap">
                <div
                  class="adv_more_img_wrap"
                  :style="{
                    width: layOutSet.img_show_width + 'px',
                    height: layOutSet.img_show_height + 'px' || '100px',
                  }"
                >
                  <img
                    class="adv_01_img"
                    :src="advObject.imgUrl"
                    alt="pic"
                    v-if="advObject.imgUrl"
                  />
                  <span @click="del_img" class="del_img">删除</span>
                </div>
              </div>
              <div class="modal_img">
                <span class="modal_tip_color"
                  >此处对应上传上方选中标签项内容，要求宽度为 {{ layOutSet.img_width }} 像素、高度
                  {{
                    layOutSet.img_height == 0
                      ? '不限制'
                      : layOutSet.img_height + '像素的图片；支持格式gif，jpg，png。'
                  }}</span
                >
                <Button @click="openMaterial">
                  <div class="flex items-center">
                    <UploadOutlined />
                    <span class="ml-2">上传图片</span>
                  </div>
                </Button>
              </div>
            </div>
          </Col>
        </Row>

        <Row type="flex" class="more_row">
          <Col flex="1" class="more_cell align_right">
            <div>
              <span class="table_left_con">操作</span>
            </div>
          </Col>
          <Col flex="8" class="more_cell">
            <div class="modal_img">
              <Select
                :value="advObject.link_type"
                :style="{ width: '110px' }"
                placeholder="请选择链接类型"
                @select="sldHandSeleChange"
                :options="new_supplier_diy_link_type.formatOptions()"
              />
            </div>
          </Col>
        </Row>

        <Row type="flex" class="more_row" v-if="advObject.link_type">
          <Col flex="1" class="more_cell align_right">
            <div>
              <span class="table_left_con">{{ singleDiy_type_name_map[advObject.link_type] }}</span>
              <span class="table_left_require">*</span>
            </div>
          </Col>
          <Col flex="8" class="more_cell">
            <div v-if="advObject.link_type == 'url'">
              <div class="modal_img">
                <FormItem
                  class="!mb-0"
                  name="link_value"
                  :rules="[{ required: true, whitespace: true, message: '请输入链接地址' }]"
                >
                  <Input
                    maxLength="250"
                    v-model:value="advObject.link_value"
                    placeholder="请输入链接地址"
                    class="!w-100"
                  />
                </FormItem>
              </div>
            </div>

            <div v-else-if="advObject.link_type == 'keyword'">
              <div class="modal_img">
                <FormItem
                  class="!mb-0"
                  name="link_value"
                  :rules="[{ required: true, whitespace: true, message: '请输入关键字' }]"
                >
                  <Input
                    maxLength="250"
                    v-model:value="advObject.link_value"
                    placeholder="请输入关键字"
                    class="!w-100"
                  />
                </FormItem>
              </div>
            </div>

            <div v-else>
              <div class="modal_img">
                <span>{{ advObject.link_value }}</span>
              </div>
            </div>
          </Col>
        </Row>

        <Row type="flex" class="more_row">
          <Col flex="1" class="more_cell align_right">
            <div>
              <span class="table_left_con">广告开关</span>
            </div>
          </Col>
          <Col flex="8" class="more_cell">
            <div class="modal_img">
              <Switch v-model:checked="advObject.show_switch"></Switch>
            </div>
          </Col>
        </Row>
      </Form>
    </div>

    <div class="m_diy_bottom_wrap" :style="{ position: 'fixed', left: '160px' }">
      <div @click="handleSaveAllData" class="add_goods_bottom_btn add_goods_bottom_btn_sel">
        保存
      </div>
    </div>

    <SldSelGoodsSingleDiy
      v-bind="modalProps"
      :modalVisible="singleDiy_modalVisible"
      @cancleEvent="singleDiyCancel"
      @confirm-event="singleDiyConfirm"
    />

    <SldMaterialImgs
      ref="sldMaterialImgsRef"
      :maxUploadNum="1"
      @confirm-material="confirmMaterial"
    ></SldMaterialImgs>
  </div>
</template>

<script setup>
  import SldComHeaderTips from '@/components/SldComHeader/tips.vue';
  import {
    SldSelGoodsSingleDiy,
    handleDiyGoodsConfirm,
    setSldSelProps,
  } from '@/components/SldSelGoodsSingleDiy';
  import SldMaterialImgs from '@/components/SldMaterialFiles/sldMaterialImgs.vue';
  import { onMounted, ref, unref } from 'vue';
  import { useCommon } from './common';
  import { resolveImageBindId } from '@/utils';
  import { Form, FormItem, Select, Button, Switch, Row, Col, Input } from 'ant-design-vue';
  import { UploadOutlined } from '@ant-design/icons-vue';
  import { failTip, new_supplier_diy_link_type, sucTip, singleDiy_type_name_map } from '@/utils/utils';
  import { AdvGetApi, saveAdvApi } from '@/api/supplier/deco_pc';

  const tipData = [
    '“显示广告开关” 开启后,会员访问PC端商城首页时，最顶部会显示一个图片广告',
    '“广告图片” 可以设置广告显示的图片',
    '“跳转链接” 可以设置点击图片的跳转地址',
  ];

  const {
    advObject,
    singleDiy_modalVisible,
    confirmMaterial,
    del_img,
    selectImageData,
    formItemLayoutModal,
    singleDiyCancel,
  } = useCommon();

  const formRef = ref();
  const modal_adv_id = ref(0);

  const sldMaterialImgsRef = ref();
  const modalProps = ref({});

  const layOutSet = {
    img_width: 1920,
    img_height: 80,
    img_show_width: 768,
    img_show_height: 75,
  };

  //选择下拉框事件
  const sldHandSeleChange = (val) => {
    advObject.value.link_type = val;
    if (new_supplier_diy_link_type[val].require_select_modal) {
      modalProps.value = setSldSelProps(val,false,'supplier');
      singleDiy_modalVisible.value = true;
    }
  };

  //SldSelGoodsSingleDiy 弹框确认事件
  const singleDiyConfirm = (e) => {
    let item = handleDiyGoodsConfirm(advObject.value.link_type, e, 'supplier');
    advObject.value.link_value = item?.link_value;
    advObject.value.info = item?.info;
    singleDiy_modalVisible.value = false;
  };

  const openMaterial = () => {
    unref(sldMaterialImgsRef).setMaterialModal(true, selectImageData);
  };

  const handleSaveAllData = async () => {
    try {
      await formRef.value.validate();
    } catch (err) {
      return;
    }

    const res = await saveAdvApi({
      data: JSON.stringify(unref(advObject)),
      advId: unref(modal_adv_id),
    });

    if (res?.state == 200) {
      sucTip(res.msg);
    } else {
      failTip(res.msg);
    }
  };

  const getAdvData = async () => {
    const res = await AdvGetApi({ type: 2 });
    if (res?.state == 200) {
      modal_adv_id.value = res.data.advId;
      let advDataObject = JSON.parse(res.data.data.replace(/&quot;/g, '"'));
      selectImageData.ids = [resolveImageBindId(advDataObject.imgPath)];
      selectImageData.data = [
        {
          bindId: resolveImageBindId(advDataObject.imgPath),
          checked: true,
          filePath: advDataObject.imgPath,
          fileUrl: advDataObject.imgUrl,
          fileType: 1,
        },
      ];

      advObject.value = advDataObject;
    }
  };

  onMounted(() => {
    getAdvData();
  });
</script>

<style lang="less">
  .diy_modal_tip_div {
    padding: 10px 15px;

    ul {
      margin-bottom: 0 !important;
      padding-left: 0 !important;

      li {
        padding: 2px 0;
        color: #db5609;
        font-size: 12px;
      }
    }
  }

  .adv_01_img_thumb {
    display: flex;
    position: relative;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    min-height: 50px;
    background: #efefef;
  }

  .imgs_wrap {
    display: flex;
    flex-flow: row wrap;
    align-items: flex-start;
    justify-content: flex-start;
    width: 100%;
    margin-bottom: 10px;
    padding: 10px;
    border-radius: 4px;
  }

  .adv_more_img_wrap {
    display: flex;
    position: relative;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    min-width: 80px;
    height: 44px;
    margin-bottom: 10px;
    margin-left: 10px;
    padding: 10px;
    border: dashed 1px #eee;
    line-height: 15px;
  }

  .adv_more_img_wrap:hover,
  .seleImg {
    border-color: #fbcfac;
    background-color: #fff3da;
  }

  .adv_more_img_wrap .del_img {
    display: inline-block;
    position: absolute;
    top: 0;
    right: 0;
    padding: 2px 5px;
    background: #fe9700;
    color: #fff;
    font-size: 12px;
    cursor: pointer;
  }

  .adv_01_img {
    max-width: 100%;
    max-height: 100%;
    margin: 0 auto;
  }

  .del_img {
    display: inline-block;
    position: absolute;
    top: -1px;
    right: 0;
    padding: 0 4px;
    background: #fe9700;
    color: #fff;
    font-size: 12px;
    cursor: pointer;
  }

  .modal_tip_color {
    display: block;
    padding: 10px 0;
    color: #db5609;
    font-size: 12px;
  }

  .table_left_con {
    color: #333;
    font-size: 13px;
  }

  .table_left_require {
    color: red;
  }

  .modal_img {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    padding-left: 10px;
  }

  .topAdv_scr {
    height: calc(100vh - 337px);
    overflow: auto;
  }
</style>
