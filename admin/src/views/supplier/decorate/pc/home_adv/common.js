import { reactive, ref } from 'vue';
import { resolveImageBindId } from '/@/utils';

export const useCommon = () => {
  const singleDiy_modalVisible = ref(false);
  const advObject = ref({})

  //form布局参数
  const formItemLayoutModal = {
    labelCol: {
      span: 6,
    },
    wrapperCol: {
      span: 14,
    },
  };

  //选中的图片的idx和data
  const selectImageData = reactive({
    ids: [''],
    data: [{}],
    clearAll () {
      this.ids = [];
      this.data = [];
    },
  }); //产品图片数据

  //删除图片
  const del_img = () => {
    selectImageData.clearAll();
    advObject.value.imgUrl = ''
    advObject.value.imgPath = ''
  };

  //确认素材的回调
  const confirmMaterial = (val) => {
    selectImageData.ids = val.ids;
    selectImageData.data = val.data;
    let [{ filePath, fileUrl }] = val.data;
    advObject.value.imgUrl = fileUrl
    advObject.value.imgPath = filePath
  };

  //SldSelGoodsSingleDiy 弹框取消事件
  const singleDiyCancel = () => {
    advObject.value.link_type = '';
    singleDiy_modalVisible.value = false;
  };


  return {
    singleDiyCancel,
    singleDiy_modalVisible,
    advObject,
    confirmMaterial,
    del_img,
    selectImageData,
    resolveImageBindId,
    formItemLayoutModal,
  };
};
