<template>
  <PageWrapper>
    <SldComHeader title="模板编辑" :type="2" :tip-data="tipData" tip-title="操作提示" />

    <div class="operate_bg">
      <SldIconBtn
        wrapML="7"
        wrapMR="7"
        svg="iconfanhui"
        text="返回模板列表"
        svgColor="rgb(250, 111, 30)"
        @click="router.back"
      />
    </div>

    <div class="mt-[10px]" style="overflow-y: auto" :style="{ height: containerHeight + 'px' }">
      <TplComponent
        :tpl_info="template_json"
        client="supplier"
        :image="Empty.PRESENTED_IMAGE_SIMPLE"
        @save_tpl_data="save_tpl_data"
        description="暂无数据"
      />
    </div>
  </PageWrapper>
</template>

<script setup>
  import SldComHeader from '@/components/SldComHeader/index.vue';
  import SldIconBtn from '@/components/SldIconBtn/index.vue';
  import { message, Empty } from 'ant-design-vue';
  import { createAsyncComponent } from '/@/utils/factory/createAsyncComponent';
  import { useRouter } from 'vue-router';
  import { createLocalStorage } from '/@/utils/cache';
  import { save_tpl_request } from '@/views/decorate/pc/template/actions/edit_tpl';
  import { transType2ComponentName } from './template/actions/template_list';

  const router = useRouter();

  const loadError = () => {
    message.error('数据加载失败');
  };

  const storage = createLocalStorage();
  const templateStorage = storage.get('TPL_JSON');

  const containerHeight = window.innerHeight - 320;

  //异步加载组件，由props里的type决定导入的模板组件
  const loadData = () => {
    if (!templateStorage) {
      message.error('无法解析数据');
      return {
        component: Empty,
        tpl_json: '',
      };
    }
    try {
      let template_json = JSON.parse(templateStorage.json.replace(/&quot;/g, '"'));
      let { type } = template_json;
      let componentName = transType2ComponentName(type);
      let asyncComponent = createAsyncComponent(
        () => import(`../../../decorate/pc/template/${componentName}.vue`),
        {},
        loadError,
      );

      return {
        component: asyncComponent,
        tpl_json: template_json,
      };
    } catch (err) {
      console.log(err.message);
      return {
        component: Empty,
        tpl_json: '',
      };
    }
  };

  const { component: TplComponent, tpl_json: template_json } = loadData();

  const tipData = [
    '点击编辑按钮，添加或编辑改模块内容，并根据弹出框体中的提示文字完成内容提交',
    '设置完成后，可点击返回模板列表按钮，回到模板列表页进行其他模板设置操作',
    '小提示：该模版编辑预览页面为实际展示页面效果，所添加编辑的内容即见即所得。',
  ];

  const save_tpl_data = (json,client) => {
    let { dataId, html } = templateStorage;
    let param = { dataId, html };
    param.json = JSON.stringify(json);
    save_tpl_request(param,client).then(() => {
      templateStorage.json = param.json;
      storage.set('TPL_JSON', templateStorage);
    });
  };
</script>

<style lang="less" scoped></style>
