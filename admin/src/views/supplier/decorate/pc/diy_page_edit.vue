<template>
  <div class="diy_page_edit flex flex-col">
    <FloorHeader
      @loadFloor="loadFloor"
      :decoDataFn="getDecoData"
      :bigBackGroundData="bigBackGround"
      @setBackground="handleBackground"
    />
    <FloorMainTop />
    <div
      class="relative content_area"
      :style="{ backgroundImage: `url(${bigBackGround.data.imgUrl})` }"
    >
      <FloorBannerItem
        :tpl_info="main_swiper_data.json"
        :floor-data="main_swiper_data"
        @loadFloor="(e) => loadFloor(e, '')"
        @updateFloor="updateFloor"
        v-if="main_swiper_data.json"
      ></FloorBannerItem>
      <div class="pc_main_diy flex-1 relative min-h-[500px]">
        <FloorNav :nav_info="main_nav" ref="floorNavRef"></FloorNav>
        <VueDraggable
          v-model="diyQueList"
          :animation="150"
          :group="{ name: 'dragGroup' }"
          :sort="true"
          @add="dataAdd"
          class="h-full"
        >
          <div v-for="item in diyQueList" :key="item.id" :id="item.id" class="floor_item">
            <FloorItem
              :tpl_info="item.json"
              :floor-data="item"
              :nav_info="main_nav"
              @handle-move-floor="handleMoveFloor"
              @loadFloor="(e) => loadFloor(e, item.id)"
              @floorNavSet="updateNavList"
            ></FloorItem>
          </div>
        </VueDraggable>
      </div>
      <FloorSelectModal
        :visible="floorSelectVisible"
        :type="floorSelectType"
        :tpl-id="floorSelectTplId"
        @cancel="floorSelectVisible = false"
        @confirm="floorLoadConfirm"
      />
    </div>
  </div>
</template>

<script setup>
  import { onMounted, reactive, ref, unref, watch, getCurrentInstance } from 'vue';
  import FloorHeader from './template/components/FloorHeader.vue';
  import FloorNav from './template/components/FloorNav.vue';
  import { VueDraggable } from 'vue-draggable-plus';
  import FloorItem from './template/components/FloorItem.vue';
  import FloorMainTop from './template/components/FloorMainTop.vue';
  import FloorSelectModal from './template/components/FloorSelectModal.vue';
  import FloorBannerItem from './template/components/FloorBannerItem.vue';
  import { failTip } from '/@/utils/utils';

  import lodash from 'lodash-es';
  import { TplDetailApi } from '/@/api/supplier/deco_pc';
  import { useRoute } from 'vue-router';
  import { buildShortUUID } from '/@/utils/uuid';
  const route = useRoute();
  let decoInfo = {};
  const diyQueList = ref([]);
  const main_swiper_data = ref({});
  const floorSelectVisible = ref(false);
  const floorSelectType = ref('all');
  const floorSelectTplId = ref(0);

  const bigBackGround = ref({
    type: 'main_background',
    data: {
      imgUrl: '',
      imgPath: '',
      width: 0,
      height: 0,
    },
  });

  const main_nav = ref({
    type: 'main_nav',
    show: false,
    adv_img: {
      imgUrl: '',
      imgPath: '',
      link_value: '',
      link_type: '',
      info: {},
    },
    nav_data: [],
  });

  const floorNavRef = ref();

  const vm = getCurrentInstance();
  const { $sldComLanguage } = vm?.appContext.config.globalProperties;

  const getDecoData = () => {
    let floorData = unref(diyQueList);
    let swiperData = unref(main_swiper_data);
    let navData = unref(main_nav);
    return {
      floorData,
      decoInfo,
      swiperData,
      navData,
    };
  };

  //获取装修页面数据
  const get_diy_page_detial = async () => {
    const { id } = route.query;
    const res = await TplDetailApi({ decoId: id });
    if (res.state == 200) {
      decoInfo = res.data;
      let tmp = res.data.data ? JSON.parse(res.data.data.replace(/&quot;/g, '"')) : [];
      for (let i in tmp) {
        if (tmp[i].tplPcId == 28) {
          tmp[i].more_tab_index = 1;
          break;
        }
      }

      main_swiper_data.value = {
        json: {
          data: [],
          type: 'main_banner',
          width: 1920,
          height: 457, //高度为0的话表示不限制
          style: {
            borderRadius: 15,
          },
        },
      };

      if (tmp.length > 0) {
        for (let item of tmp) {
          if (item.json.type == 'main_background') {
            bigBackGround.value = item.json;
          } else if (/main_banner/.test(item.json.type)) {
            main_swiper_data.value = item;
          } else if (item.json.type == 'main_nav') {
            main_nav.value = item.json;
          } else {
            // @ts-ignore
            if (!item.id) {
              item.id = buildShortUUID('diy');
            }

            if (!item.nav) {
              item.nav = {
                imgUrl: '',
                imgPath: '',
                text: '',
              };
            }

            diyQueList.value.push(item);
            updateNavList();
          }
        }
      }
    }
  };

  const updateFloor = (val)=> {
    if(main_swiper_data.value&&main_swiper_data.value.json&&main_swiper_data.value.json.style){
      main_swiper_data.value.json.style.borderRadius = val.borderStyle ? 0 : 15;
    }
  }

  //上移，下移，删除
  const handleMoveFloor = (type, id) => {
    if (type == 'up') {
      for (let i = 0; i < unref(diyQueList).length; i++) {
        if (unref(diyQueList)[i].id == id) {
          let temp = lodash.cloneDeep(unref(diyQueList)[i - 1]);
          unref(diyQueList)[i - 1] = lodash.cloneDeep(unref(diyQueList)[i]);
          unref(diyQueList)[i] = temp;
          break;
        }
      }
    } else if (type == 'down') {
      for (let i = 0; i < unref(diyQueList).length; i++) {
        if (unref(diyQueList)[i].id == id) {
          let temp = lodash.cloneDeep(unref(diyQueList)[i]);
          unref(diyQueList)[i] = lodash.cloneDeep(unref(diyQueList)[i + 1]);
          unref(diyQueList)[i + 1] = temp;
          break;
        }
      }
    } else if (type == 'del') {
      diyQueList.value = diyQueList.value.filter((item) => item.id != id);
    }
    updateNavList();
  };

  // 装载，添加楼层
  let currentPosition;
  let currentId;
  const loadFloor = ({ position, type, tplId }, id) => {
    currentPosition = position;
    currentId = id;
    floorSelectTplId.value = tplId;
    floorSelectType.value = type;
    floorSelectVisible.value = true;
  };

  const dataAdd = () => {
    let more_tab = diyQueList.value.filter((item) => item.tplPcId == 28);
    if (more_tab.length > 1) {
      failTip(`${$sldComLanguage('已添加多TAB切换模块，不可再添加多TAB切换模块')}～`);
      for (let i in diyQueList.value) {
        if (diyQueList.value[i].tplPcId == 28 && !diyQueList.value[i].more_tab_index) {
          diyQueList.value.splice(i, 1);
          break;
        }
      }
      return;
    } else {
      if (more_tab.length == 1) {
        more_tab[0].more_tab_index = 1;
      }
    }
  };

  // 装载，添加楼层---确认事件
  const floorLoadConfirm = (val) => {
    if (val.tplPcId == 28 && currentPosition == 'first') {
      let more_tab = diyQueList.value.filter((item) => item.tplPcId == val.tplPcId);
      if (more_tab.length > 0) {
        failTip(`${$sldComLanguage('已添加多TAB切换模块，不可再添加多TAB切换模块')}～`);
        return;
      }
    }
    floorSelectVisible.value = false;
    let temp_data = JSON.parse(val.json.replace(/&quot;/g, '"'));
    temp_data.insTplId = val.dataId;
    temp_data.insTplName = val.name;
    if (val.tplPcType == 'main_banner') {
      unref(main_swiper_data).json = temp_data;
      unref(main_swiper_data).tplPcId = val.tplPcId;
    } else {
      let target = {
        id: buildShortUUID('diy'),
        html: val.html,
        key: '',
        json: temp_data,
        tplPcId: val.tplPcId,
        nav: {
          imgUrl: '',
          imgPath: '',
          text: '',
        },
      };
      if (!currentPosition) {
        //在最后添加楼层
        diyQueList.value.push(target);
      } else if (currentPosition == 'first') {
        diyQueList.value.unshift(target);
      } else {
        for (let i = 0; i < diyQueList.value.length; i++) {
          if (diyQueList.value[i].id == currentId) {
            if (currentPosition == 'top') {
              diyQueList.value.splice(i, 0, target);
            } else if (currentPosition == 'bottom') {
              diyQueList.value.splice(i + 1, 0, target);
            } else if (currentPosition == 'cur') {
              diyQueList.value[i] = target;
            }
            break;
          }
        }
      }
    }
  };

  const handleBackground = (back) => {
    unref(bigBackGround).data = back;
  };

  const updateNavList = () => {
    let navSet = unref(diyQueList).map((item) => ({ id: item.id, ...item.nav }));
    unref(floorNavRef).getNavList(navSet);
  };

  onMounted(() => {
    get_diy_page_detial();
  });
</script>

<style lang="less" scoped>
  .diy_page_edit {
    height: 100%;
    padding-top: 48px;
    background-color: #fff;
  }

  .content_area {
    width: 100%;
    background-repeat: no-repeat;
    background-position: 50% 0;
  }
</style>

<style lang="less">
  .floor_item {
    &:first-child {
      .sld_web_item_hover .floor_tag .up_do {
        display: none !important;
      }
    }

    &:last-child {
      .sld_web_item_hover .floor_tag .down_do {
        display: none !important;
      }
    }
  }
</style>
