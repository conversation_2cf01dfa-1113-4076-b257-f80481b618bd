<!-- 添加模板弹窗-->
<template>
  <Modal
    title="添加装修模板"
    @ok="modalConfirm"
    @cancel="modalCancel"
    :visible="modalVisible"
    width="1000px"
  >
    <div class="p-[10px]">
      <section>
        <SldComHeader title="基本信息"></SldComHeader>
        <Form
          ref="formRef"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
          :model="formModel"
          :scrollToFirstError="true"
          @finish="onFinish"
          style="margin-top: 20px"
        >
          <FormItem name="name" label="实例化模板名称" :required="true">
            <Input
              v-model:value="formModel.name"
              placeholder="请输入实例化模板名称"
              :maxlength="20"
            ></Input>
          </FormItem>

          <FormItem
            name="sort"
            extra="请输入0~255的数字,数据越小显示越靠前"
            label="排序"
            :required="true"
            :rules="[{ validator: sortValidator, trigger: 'change' }]"
          >
            <InputNumber v-model:value="formModel.sort" placeholder="请输入排序"></InputNumber>
          </FormItem>
        </Form>
      </section>

      <section>
        <SldComHeader title="选择模板" />
        <Tabs
          v-model:active-key="activeKey"
          tabPosition="left"
          style="height: 220px"
          @change="handleTabChange"
          class="!mt-5"
        >
          <TabPane :tab="val.typeName" :key="val.type" v-for="val in templateTypeList">
            <div class="right">
              <div
                class="item"
                :class="{ item_select: selTplPc.tplPcId == items.tplPcId }"
                @click="handle_sele_tpl(items)"
                v-for="(items, indexs) in templateInsList"
                :key="indexs"
              >
                <span class="sele_flag">
                  <AliSvgIcon
                    iconName="iconxuanzhong3"
                    width="21px"
                    height="21px"
                    fillColor="primaryColor"
                  ></AliSvgIcon>
                </span>
                <span class="img_wrap">
                  <img :src="items.image" />
                </span>
                <span class="title">{{ items.name }}</span>
                <span class="desc">{{ items.desc }}</span>
              </div>
            </div>
          </TabPane>
        </Tabs>
      </section>
    </div>
  </Modal>
</template>

<script setup>
  import { Modal, message } from 'ant-design-vue';
  import { reactive, ref, toRef, onMounted } from 'vue';
  import { Form, FormItem, Input, InputNumber, Tabs, TabPane } from 'ant-design-vue';
  import SldComHeader from '@/components/SldComHeader/index.vue';
  import { TplAddListApi, TplInsListApi, TplTypeListApi } from '/@/api/supplier/deco_pc';
  import { AliSvgIcon } from '@/components/SvgIcon';
  const props = defineProps({
    visible: Boolean,
  });

  const emit = defineEmits(['cancel', 'confirm']);

  const formModel = reactive({
    name: '',
    sort: '',
  });

  const formRef = ref();

  const templateTypeList = ref([]);
  const templateInsList = ref([]);
  const selTplPc = reactive({
    tplPcId: -1,
    json: '',
    html: '',
    reset() {
      this.tplPcId = -1;
      this.json = '';
      this.html = '';
    },
  });

  const activeKey = ref('');

  const onFinish = () => {};

  const modalVisible = toRef(props, 'visible');

  const sortValidator = (rules, value) => {
    if (!(value >= 0 && value <= 255)) {
      return Promise.reject('请输入0~255的数字');
    }
    return Promise.resolve();
  };

  const modalConfirm = async () => {
    await formRef.value.validate();
    if (selTplPc.tplPcId < 0) {
      message.warn('请选择实例模板');
      return;
    }
    const res = await TplAddListApi({
      ...formModel,
      ...selTplPc,
    });

    if (res.state == 200) {
      message.success(res.msg);
      formRef.value.resetFields();
      selTplPc.reset();
      emit('confirm');
    } else {
      message.warn(res.msg);
    }
  };

  const handle_sele_tpl = (val) => {
    selTplPc.tplPcId = val.tplPcId;
    selTplPc.json = val.defaultData;
    selTplPc.html = val.data;
  };

  const modalCancel = () => {
    formRef.value.resetFields();
    selTplPc.reset();
    emit('cancel');
  };

  const get_tpl_list = async (type) => {
    const res = await TplInsListApi({ type });
    if (res.state == 200) {
      templateInsList.value = res.data.list;
    }
  };

  //获取所有模板类型列表
  const get_tpl_type_list = async () => {
    const res = await TplTypeListApi();
    if (res.data.length > 0) {
      templateTypeList.value = res.data;
      activeKey.value = res.data[0].type;
      get_tpl_list(activeKey.value);
    }
  };

  const handleTabChange = (key) => {
    get_tpl_list(key);
  };

  onMounted(() => {
    get_tpl_type_list();
  });
</script>

<style lang="less" scoped>
  .right {
    display: flex;
    flex-flow: row wrap;
    align-items: flex-start;
    justify-content: flex-start;
    height: 220px;
    overflow: auto;

    .item {
      display: flex;
      position: relative;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 340px;
      height: 260px;
      border: 1px solid #fff;
      background: #fff;
      cursor: pointer;

      &:hover {
        background-color: rgb(238 238 238);
      }

      &:nth-child(2n) {
        margin-left: 25px;
      }

      &.item_select {
        border-color: #e6e6e6;
        background: #fbfafa;

        .sele_flag {
          display: inline-block;
        }
      }

      .sele_flag {
        display: none;
        position: absolute;
        z-index: 2;
        top: 10px;
        right: 10px;
      }

      .img_wrap {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        width: 288px;
        height: 134px;

        img {
          max-width: 100%;
          max-height: 100%;
        }
      }

      .title {
        color: #333;
        font-size: 14px;
        line-height: 20px;
      }

      .desc {
        height: 28px;
        margin-top: 5px;
        padding: 0 10px;
        overflow: hidden;
        color: #666;
        font-size: 12px;
        line-height: 14px;
      }
    }
  }
</style>
