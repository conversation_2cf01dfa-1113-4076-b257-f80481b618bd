<!-- 装修详情--顶部栏-->
<template>
  <div class="diy_web_fixed_top">
    <div class="diy_web_main h-full flex_row_between_center">
      <div class="diy_page-left flex items-center">
        <div
          class="flex_row_center_center diy_page-item"
          :class="{ selected: currentTopData.top_code == item.top_code }"
          v-for="(item, index) in topData"
          :key="index"
          @click="onChangeTop(item.top_code)"
        >
          <img class="diy_page-icon" :src="item.top_icon" alt="" />
          <span class="diy_page-name">{{ item.top_name }}</span>
        </div>
        <div class="flex_row_center_center show_hide_icon" @click="onChangeTop()">
          <AliSvgIcon
            fillColor="#fff"
            width="13px"
            height="13px"
            :iconName="top_show ? 'iconshangjiantou' : 'iconxiajiantou1'"
          ></AliSvgIcon>
        </div>
      </div>

      <div class="diy_page-right flex items-center">
        <Button class="main_btn" @click="openMaterialImage">设置背景图</Button>
        <Button class="main_btn" @click="emit('loadFloor', { position: 'first', tplId: 0 })"
          >添加楼层</Button
        >
        <Button class="main_btn" @click="save_diy_page_data(false)">保存</Button>
        <Button class="sub_btn" @click="save_diy_page_data(true)">发布</Button>
      </div>
    </div>

    <div class="diy_web_fixed_top_detail flex items-center" v-show="top_show">
      <VueDraggable
        v-model="currentTopData.list"
        :animation="150"
        :group="{ name: 'dragGroup', pull: 'clone', put: false }"
        :sort="false"
        class="flex_row_start_center"
        :clone="diyClone"
      >
        <div
          class="flex_column_center_center mr-[20px] cursor-grab"
          v-for="(item, index) in currentTopData.list"
          :key="index"
        >
          <img
            class="diy_page-tpl_icon"
            :src="
              getImagePath(`images/pc_diy_img/web_diy_top_${item.type}_icon_${item.tplPcId}.png`)
            "
          />
          <span class="diy_page-tpl_name">
            {{ item.name }}
          </span>
        </div>
      </VueDraggable>
    </div>

    <SldMaterialImgs
      ref="sldMaterialImgs"
      @confirmMaterial="selectedMaterialImg"
      :allow-repeat="false"
      :max-upload-num="1"
      title="图片空间（建议上传宽度为1920的图片）"
    />
  </div>
</template>

<script setup>
  import { getCurrentInstance, onMounted, reactive, ref, unref } from 'vue';
  import { TplInsListApi, UpdateDecoApi } from '/@/api/supplier/deco_pc';
  import { getImagePath, resolveImageBindId } from '/@/utils';
  import { AliSvgIcon } from '@/components/SvgIcon';
  import { Button } from 'ant-design-vue';
  import { VueDraggable } from 'vue-draggable-plus';
  import { buildShortUUID } from '/@/utils/uuid';
  import { failTip, quillEscapeToHtml, sucTip } from '/@/utils/utils';
  import SldMaterialImgs from '/@/components/SldMaterialFiles/sldMaterialImgs.vue';

  const props = defineProps({
    decoDataFn: {
      type: Function,
      default: () => {},
    },
    bigBackGroundData: {
      type: Object,
      default: () => {},
    },
  });

  const topData = ref([
    {
      top_code: 'goods_floor',
      top_icon: getImagePath('images/pc_diy_img/web_diy_top_goods_floor_icon.png'),
      top_name: '商品楼层',
      children: [],
    },
    {
      top_code: 'adv_floor',
      top_icon: getImagePath('images/pc_diy_img/web_diy_top_adv_floor_icon.png'),
      top_name: '广告楼层',
      children: [],
    },
  ]); //顶部数据

  const emit = defineEmits(['dragEnd', 'loadFloor', 'setBackground']);

  const sldMaterialImgs = ref();

  const vm = getCurrentInstance();
  const { $sldComLanguage } = vm?.appContext.config.globalProperties;

  const diyClone = (val) => {
    let json = JSON.parse(val.defaultData);
    let id = buildShortUUID('diy');
    return {
      ...val,
      json,
      id,
      nav: {
        imgUrl: '',
        imgPath: '',
        text: '',
      },
    };
  };

  const top_show = ref(false);

  const currentTopData = reactive({
    top_code: '',
    list: [],
  });

  const onChangeTop = (top_code = '') => {
    let local_code;
    if (top_code) {
      local_code = top_code;
    } else {
      local_code = unref(topData)[0].top_code;
    }

    if (currentTopData.top_code == local_code) {
      top_show.value = !top_show.value;
    } else {
      currentTopData.top_code = local_code;
      top_show.value = true;
    }

    let targetList = unref(topData).find((t) => t.top_code == local_code);
    currentTopData.list = targetList?.children;
  };

  //获取模板实例列表
  const get_tpl_list = async (type) => {
    const res = await TplInsListApi();
    if (res.state == 200) {
      topData.value.map((item) => {
        let targetFloor = res.data.list.filter((t) => t.type == item.top_code);
        item.children = targetFloor;
      });
    }
  };

  //保存装修数据  publish: 是否发布 true为发布 false为仅保存，不发布
  const save_diy_page_data = async (isPublish) => {
    const { floorData, swiperData, decoInfo, navData } = props.decoDataFn();
    const { bigBackGroundData } = props;
    //轮播图必须设置
    let main_swiper_data_flag = false; //主轮播图数据标识 false为空
    for (let swiperItem of swiperData.json.data ?? swiperData.json.center.data) {
      if (swiperItem.imgPath) {
        main_swiper_data_flag = true;
        break;
      }
    }
    if (!main_swiper_data_flag) {
      failTip(`${$sldComLanguage('请设置主轮播图')}～`);
      return false;
    }
    let more_tab = floorData.filter((item) => item.tplPcId == 28);
    if (more_tab.length > 0) {
      if (floorData[floorData.length - 1].tplPcId != 28) {
        failTip(`${$sldComLanguage('多TAB切换模块必须放置在最后，请调整顺序后保存')}～`);
        return;
      }
      if (more_tab[0].json.data.length == 0) {
        failTip(`${$sldComLanguage('多TAB切换模块需要选择商品')}～`);
        return;
      } else {
        if (more_tab[0].json.data[0].title == '') {
          failTip(`${$sldComLanguage('多TAB切换模块需要选择商品')}～`);
          return;
        }
      }
    }
    let param = {};
    param.decoId = decoInfo.decoId;
    param.decoType = decoInfo.type;
    param.isEnable = isPublish ? 1 : 0;
    let extraData = [{ json: bigBackGroundData }, { json: navData }];
    let target = [swiperData].concat(extraData).concat(floorData);

    param.data = quillEscapeToHtml(JSON.stringify(target));
    const res = await UpdateDecoApi(param);
    if (res.state == 200) {
      sucTip(res.msg);
    } else {
      failTip(res.msg);
    }
  };

  const openMaterialImage = () => {
    let selectedData = { data: [], ids: [] };
    let { bigBackGroundData } = props;
    if (bigBackGroundData.data.imgPath) {
      let fileData = {
        bindId: Number(resolveImageBindId(bigBackGroundData.data.imgPath)),
        width: bigBackGroundData.data.width,
        height: bigBackGroundData.data.height,
        fileUrl: bigBackGroundData.data.imgUrl,
        filePath: bigBackGroundData.data.imgPath,
      };
      selectedData = {
        data: [fileData],
        ids: [fileData.bindId],
      };
    }
    unref(sldMaterialImgs).setMaterialModal(true, selectedData);
  };

  const selectedMaterialImg = ({ data }) => {
    let [img = {}] = data;

    let imgData = {
      imgUrl: img.fileUrl ?? '',
      imgPath: img.filePath ?? '',
      width: img.width ?? 0,
      height: img.height ?? 0,
    };
    emit('setBackground', imgData);
  };

  onMounted(get_tpl_list);
</script>

<style lang="less" scoped>
  .diy_web_fixed_top {
    position: fixed;
    z-index: 991;
    top: 0;
    right: 0;
    left: 0;
    width: 100vw;
    height: 48px;
    background: linear-gradient(90deg, #ff5216, #ff8a39);

    .diy_web_main {
      padding-right: calc(50vw - 605px);
      padding-left: calc(50vw - 605px);
    }

    .diy_web_fixed_top_detail {
      position: fixed;
      z-index: 999999;
      top: 48px;
      right: 0;
      left: 0;
      width: 100vw;
      height: 120px;
      padding-right: calc(50vw - 605px);
      padding-left: calc(50vw - 605px);
      background: #fff;
      box-shadow: 0 2px 6px 0 rgb(0 0 0 / 20%);
    }

    .diy_page-item {
      height: 48px;
      padding: 0 12px;
      cursor: pointer;

      &.selected {
        background: rgb(255 255 255 / 20%);
      }

      .diy_page-icon {
        width: 20px;
        height: 20px;
      }

      .diy_page-name {
        margin-left: 10px;
        color: #fff;
        font-size: 15px;
        font-weight: 500;
      }
    }

    .diy_page-tpl_icon {
      width: 64px;
      height: 60px;
      margin-bottom: 7px;
    }

    .diy_page-tpl_name {
      color: #666;
      font-size: 13px;
    }

    .show_hide_icon {
      margin-left: 8px;
      padding: 2px;
      border: 1px solid #fff;
      border-radius: 2px;
      cursor: pointer;

      &:hover {
        background: #fff;

        svg {
          fill: @primary-color !important;
        }
      }
    }

    .main_btn {
      margin-left: 20px;
      border-color: #fff;
      background: transparent;
      color: #fff;
    }

    .sub_btn {
      margin-left: 20px;
      border-color: #fff;
      color: @primary-color;
    }
  }
</style>
