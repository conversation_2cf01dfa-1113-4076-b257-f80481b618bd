<!-- 模板编辑---通用的蒙层点击组件 -->
<template>
  <div class="relative mask_container" :class="{ enabled }">
    <slot></slot>
    <div class="mask_layer" v-if="!editBtnType">
      <slot name="button"></slot>
      <div class="edit_tag" v-if="!$slots.button">
        <div class="edit_tag_one" v-if="editBtn == 'text'" @click.stop="openEdit">编辑</div>
        <div
          class="edit_tag_one_del"
          v-if="delBtnType"
          style="margin-top: 3px"
          @click.stop="delEdit"
          >删除</div
        >
        <AliSvgIcon
          class="edit_tag_two"
          iconName="iconbianji2"
          width="18px"
          height="18px"
          fillColor="primaryColor"
          v-if="editBtn == 'icon'"
        ></AliSvgIcon>
      </div>
    </div>

    <div class="mask_layer mask_layer_type" @click="openEdit" v-if="editBtnType == true"> </div>

    <template v-if="enabled">
      <ModalComponent
        :visible="visibleSet"
        :modalVisible="visibleSet"
        v-bind="config"
        @cancleEvent="cancelHandler"
        @confirmEvent="confirmHandler"
        @confirm="confirmHandler"
        @cancel="cancelHandler"
      />
    </template>
  </div>
</template>

<script setup>
  import { AliSvgIcon } from '@/components/SvgIcon';
  import { createAsyncComponent } from '/@/utils/factory/createAsyncComponent';
  import { defineComponent, inject, ref } from 'vue';
  import { useTplContext } from '@/views/decorate/pc/template/actions/edit_tpl';
  const props = defineProps({
    //编辑按钮的模式，当遇到尺寸小的可以用 icon，默认text
    editBtn: {
      type: String,
      default: 'text',
    },

    editBtnType: {
      type: Boolean,
      default: false,
    },
    delBtnType: {
      type: Boolean,
      default: false,
    },

    config: {
      type: Object,
      required: true,
      default: {
        component: '',
        position: '',
        title: '',
      },
    },
  });

  const loadError = () => {};

  const placeHolder = defineComponent({
    template: '<div></div>',
  });

  const ModalComponent = props.config.component
    ? createAsyncComponent(
        () => import(`../../../../../../components/${props.config.component}/index.vue`),
        {},
        loadError,
      )
    : placeHolder;

  //使用 inject 控制 是否enable
  const { isMaskEdit: enabled, editConfirmHandler } = useTplContext();

  const emit = defineEmits(['confirm']);

  const visibleSet = ref(false);

  const openEdit = () => {
    visibleSet.value = true;
  };

  const delEdit = () => {
    let { position, index, formValue } = props.config;
    editConfirmHandler?.([formValue], { position, index, del_type: true });
  };

  const cancelHandler = () => {
    visibleSet.value = false;
  };

  const confirmHandler = (...val) => {
    // index参数 Adv21.vue 需要用
    let { position, index } = props.config;
    visibleSet.value = false;
    editConfirmHandler?.(val, { position, index });
  };
</script>

<style lang="less" scoped>
  .mask_container {
    z-index: 40;

    &.enabled:hover {
      .mask_layer {
        display: block;
      }
    }

    .mask_layer {
      display: none;
      position: absolute;
      z-index: 10;
      inset: 0;
      border: dotted 1px @primary-color;
      background: rgb(78 140 251 / 20%);
      cursor: pointer;

      .edit_tag {
        position: absolute;
        z-index: 1;
        top: 5px;
        right: 5px;

        .edit_tag_one {
          padding: 3px 8px;
          border-radius: 3px;
          background: @primary-color;
          color: #fff;
          font-size: 12px;
          line-height: 16px;
        }

        .edit_tag_one_del {
          padding: 3px 8px;
          border: 1px solid @primary-color;
          border-radius: 3px;
          background: #fff;
          color: @primary-color;
          font-size: 12px;
          line-height: 16px;
        }
      }
    }

    .mask_layer_type {
      background: transparent;
    }
  }
</style>
