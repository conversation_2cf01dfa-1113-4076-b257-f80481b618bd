<template>
  <div>
    <!-- {/* 最顶部 start */} -->
    <div class="header_wrap">
      <div class="header">
        <div class="header_left">
          <span class="hello">您好，欢迎来到</span>
          <span class="register h1">登录</span>
          <span class="register h1">注册</span>
        </div>
        <div class="header_right">
          <ul>
            <li>
              <div class="li_item">商城首页</div>
            </li>
            <li>
              <div class="has_more li_item">
                我的订单
                <div class="li_item_more">
                  <a class="li_item_more_item">待支付订单</a>
                  <a class="li_item_more_item">待收货订单</a>
                  <a class="li_item_more_item">待评价订单</a>
                </div>
              </div>
            </li>
            <li>
              <div class="li_item">个人中心</div>
            </li>
            <li>
              <div class="has_more li_item">
                我的收藏
                <div class="li_item_more">
                  <a class="li_item_more_item">商品收藏</a>
                  <a class="li_item_more_item">店铺收藏</a>
                  <a class="li_item_more_item">我的足迹</a>
                </div>
              </div>
            </li>
            <li>
              <div class="has_more li_item">
                我的账户
                <div class="li_item_more">
                  <a class="li_item_more_item">我的优惠券</a>
                  <a class="li_item_more_item">我的余额</a>
                </div>
              </div>
            </li>
            <li>
              <div class="li_item">服务中心</div>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <!-- {/* 最顶部 end */} -->

    <!-- {/* 搜索部分 start */} -->
    <div class="sld_home_top_search container">
      <div class="ld sld_home_top_search_left">
        <a class="sld_logo_wrap flex_row_start_center">
          <img :src="mall_logo" />
        </a>
      </div>
      <div class="sld_seach_wrap">
        <div class="sld_seach_box ld">
          <div class="form">
            <input
              type="text"
              class="text"
              autocomplete="off"
              style="color: rgb(153 153 153)"
              placeholder="请输入关键词"
            />
            <input type="submit" value="搜索" class="button" />
          </div>
          <div class="hot_search_wrap">
            <div>
              <a :key="index" href="javascript:void(0)" v-for="(item, index) in hot_search_words">{{
                item
              }}</a>
            </div>
          </div>
        </div>
      </div>
      <div class="sld_cart_wrap">
        <dl>
          <dt class="ld cart_icon_text_wrap" style="border-bottom: 1px solid rgb(239 239 239)">
            <AliSvgIcon iconName="icongouwuche1" width="16px" height="16px" />
            <a href="javascript:void(0)">我的购物车</a>
            <div class="cart_goods_num">0</div>
          </dt>
        </dl>
      </div>
    </div>
    <!-- {/* 搜索部分 end */} -->

    <!-- {/* 导航 start */} -->
    <div class="nav_cat">
      <div class="header">
        <div class="product_sort">
          <img src="@/assets/images/pc_diy_top_all_cat_icon.png" />
          <span class="sort">产品分类</span>
        </div>
        <nav>
          <li :key="index" v-for="(item, index) in nav_list"
            ><a>{{ item.navName }}</a></li
          >
        </nav>
      </div>
    </div>
    <!-- {/* 导航 end */} -->
  </div>
</template>

<script setup>
  import { onMounted, ref } from 'vue';
  import { getSettingListApi } from '/@/api/common/common';
  import { PcDecoNavListApi } from '/@/api/supplier/deco_pc';
  import { AliSvgIcon } from '@/components/SvgIcon';
  const hot_search_words = ref([]);
  const nav_list = ref([]);
  const mall_logo = ref('');

  //获取基本配置信息
  const get_base_setting = async () => {
    const res = await getSettingListApi({ str: 'supplier_hot_search_words,supplier_main_site_logo' });
    if (res.state == 200) {
      res.data.map((item) => {
        if (item.name == 'supplier_hot_search_words') {
          let tmp = [];
          if (item.value) {
            tmp = item.value.split(',');
          }
          hot_search_words.value = tmp;
        } else if (item.name == 'main_site_logo') {
          mall_logo.value = item.imageUrl
            ? item.imageUrl
            : require('@/assets/images/pc_diy_top_default_mall_logo.png');
        }
      });
    }
  };

  //获取导航列表
  const get_nav_list = async () => {
    const res = await PcDecoNavListApi({ pageSize: 30 });
    if (res.state == 200) {
      nav_list.value = res.data.list;
    }
  };

  onMounted(() => {
    get_base_setting();
    get_nav_list();
  });
</script>

<style lang="less">
  ul,
  li {
    list-style: none;
  }

  .header_wrap {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 33px;
    background: #f7f7f7;

    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 1210px;
      height: 33px;

      .header_left {
        display: flex;
        width: 360px;
        height: 100%;
        color: #999;
        font-size: 12px;
        line-height: 36px;

        .hello {
          margin-right: 20px;
          color: #999;
        }

        .h1 {
          margin: 0 5px;
          cursor: pointer;

          &:hover {
            color: #e2231a;
          }
        }
      }

      .header_right {
        height: 100%;

        ul {
          display: flex;
          align-items: center;
          width: 100%;
          height: 100%;

          .personal_center {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 121px;
            height: 30px;
          }

          li {
            height: 12px;
            padding: 0 10px;
            float: left;
            border-right: 1px solid #ddd;
            text-align: center;

            .li_item {
              position: relative;
              height: 33px;
              color: #999;
              font-size: 12px;
              line-height: 12px;
              cursor: pointer;

              &:hover {
                color: #e2231a;

                &.has_more {
                  &::before {
                    border-top-color: #e2231a;
                  }
                }

                .li_item_more {
                  display: block;
                }
              }

              &.has_more {
                padding-right: 12px;

                &::before,
                &::after {
                  content: ' ';
                  display: block;
                  position: absolute;
                  right: -2px;
                  width: 0;
                  height: 0;
                  border: 4px solid transparent;
                  border-radius: 2px;
                }

                &::before {
                  top: 3px;
                  border-top: 5px solid #888;
                }

                &::after {
                  top: 1px;
                  border-top: 5px solid #f7f7f7;
                }
              }

              .li_item_more {
                display: none;
                position: absolute;
                z-index: 999;
                top: 21px;
                left: 50%;
                width: 80px;
                padding: 5px 3px;
                transform: translateX(-50%);
                background: #fff;
                box-shadow: 0 0 5px rgb(0 0 0 / 15%);

                &::before,
                &::after {
                  content: ' ';
                  display: block;
                  position: absolute;
                  top: -11px;
                  left: 50%;
                  width: 0;
                  height: 0;
                  transform: translateX(-50%);
                  border: 5px solid transparent;
                  border-bottom: 6px solid #dedede;
                }

                &::after {
                  top: -10px;
                  border-bottom: 6px solid #fff;
                }

                .li_item_more_item {
                  display: block;
                  padding: 8px 0;
                  color: #999;
                  line-height: 12px;
                  text-align: center;

                  &:hover {
                    color: #e2231a;
                  }
                }
              }
            }

            &:last-child {
              padding-right: 0;
              border-right-width: 0;
            }
          }
        }
      }
    }
  }

  .sld_home_top_search {
    position: relative;
    z-index: 12;
    width: 1210px;
    height: 99px;
    margin: 0 auto 15px;
    padding-right: 0;
    padding-left: 0;

    &::before {
      content: ' ';
      display: table;
    }

    .sld_home_top_search_left {
      position: relative;
      width: 331px;
      margin-top: 24px;
      float: left;

      .sld_logo_wrap {
        display: block;
        width: 160px;
        height: 60px;

        img {
          max-width: 100%;
          max-height: 100%;
        }
      }
    }

    .sld_seach_wrap {
      width: 660px;
      margin-top: 30px;
      padding-top: 4px;
      float: left;

      .sld_seach_box {
        z-index: 11;
        width: 580px;
        height: 38px;
        margin-bottom: 3px;
        border: 2px solid #e2231a;

        .form {
          height: 34px;
          overflow: hidden;
          background-color: #e2231a;

          .text {
            width: 471px;
            height: 34px;
            padding: 5px 5px 5px 10px;
            border: none;
            border-radius: 0;
            outline: none;
            background-color: #fff;
            background-repeat: repeat-x;
            background-position: 0 -360px;
            font-size: 12px;
            line-height: 20px;
            appearance: none;
          }

          input {
            height: 34px;
            margin: 0;
            padding: 0;
            border: 0;
          }

          .button {
            width: 103px;
            float: right;
            background: #e2231a;
            color: #fff;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
          }
        }

        .hot_search_wrap {
          height: 30px;
          overflow: hidden;
          color: #999;
          line-height: 30px;

          strong {
            float: left;
            font-weight: 400;
          }

          a {
            color: #666;
            font-size: 12px;

            &:link,
            &:visited {
              margin-right: 10px;
              float: left;
            }

            &:hover {
              color: #e2231a;
            }
          }
        }
      }
    }

    .sld_cart_wrap {
      position: relative;
      z-index: 99;
      width: 165px;
      height: 40px;
      margin-top: 34px;
      margin-right: 13px;
      float: right;

      dl {
        margin-bottom: 0;

        .cart_goods_num {
          display: inline-block;
          min-width: 16px;
          height: 16px;
          margin-left: 10px;
          border: none 0;
          border-radius: 8px;
          background: #e2231a;
          color: #fff;
          font: 11px/16px Verdana;
          text-align: center;
        }

        dt {
          position: absolute;
          z-index: 3;
          width: 165px;
          height: 38px;
          border: 1px solid #e3e3e3;
          background-color: #fff;
          font-weight: 400;
          cursor: pointer;

          .iconfont {
            display: inline-block;
            margin: 0 13px 0 12px;
            color: #e2231a;
            font-size: 17px;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            font-style: normal;
            font-weight: 600;
            line-height: 32px;
            vertical-align: bottom;
          }

          &.cart_icon_text_wrap {
            a {
              color: #666;
              font-size: 14px;
              line-height: 36px;
            }
          }
        }

        dd {
          .cart_goods {
            dl {
              padding-top: 8px;
            }

            dd {
              &.cart_goods_price {
                position: static;

                em {
                  width: auto;
                  margin-right: 6px;
                  color: #666;

                  &:nth-child(1) {
                    display: block;
                    font-weight: 600;
                  }

                  &:nth-child(2) {
                    display: block;
                    margin-top: 6px;
                    text-align: right;
                  }
                }
              }
            }
          }
        }
      }

      dd {
        position: absolute;
        z-index: 1;
        top: 37px;
        right: 0;
        width: 355px;
        border: 1px solid #e3e3e3;
        background: #fff;
      }

      &:hover .cart_more_view {
        display: inline-block;
      }
    }

    .cart_more_view {
      display: none;

      .empty_cart {
        position: relative;
        width: 100%;

        .empty_cart_line {
          position: absolute;
          z-index: 999;
          top: -2px;
          right: 0;
          width: 163px;
          height: 2px;
          background: #fff;
        }

        .empty_cart_txt {
          padding: 10px;
          color: #999;
        }
      }
    }

    .ld {
      position: relative;
      zoom: 1;
    }

    .cart_data {
      display: flex;
      position: relative;
      flex-direction: column;
      height: 300px;

      .cart_data_title {
        height: 32px;
        padding: 7px;
        float: left;
        font-weight: 600;
        line-height: 32px;
      }

      .cart_list {
        padding: 20px;
        overflow-y: scroll;

        .cart_list_pre {
          margin-bottom: 20px;

          .cart_pre_left {
            .cart_pre_img {
              width: 48px;
              height: 48px;
              border: 1px solid #e3e3e3;
              cursor: pointer;

              img {
                width: 100%;
                height: 100%;
              }
            }

            .cart_pre_cen {
              width: 150px;
              margin-left: 20px;
              cursor: pointer;

              .cart_pre_name {
                display: -webkit-box;
                overflow: hidden;
                text-overflow: ellipsis;
                word-break: break-all;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
              }

              .cart_pre_spec {
                margin-top: 5px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }

          .cart_pre_right {
            .cart_pre_price {
              color: #666;
            }

            .cart_pre_del {
              margin-top: 10px;
              color: #666;
              cursor: pointer;

              &:hover {
                color: #e2231a;
              }
            }
          }
        }
      }

      .cart_bottom {
        width: 100%;
        height: 44px;
        border: 1px solid #e3e3e3;

        .cart_bottom_left {
          height: 44px;
          padding-left: 11px;

          span {
            color: #666;

            &:nth-child(2) {
              margin-left: 5px;
            }
          }
        }

        .cart_bottom_right {
          width: 110px;
          height: 44px;
          background-color: #f30213;
          color: #fff;
          line-height: 44px;
          text-align: center;
          cursor: pointer;
        }
      }
    }
  }

  .header {
    display: flex;
    width: 1210px;
    height: 37px;
    margin: 0 auto;
    overflow: visible;

    .product_sort {
      display: flex;
      position: relative;
      box-sizing: border-box;
      flex-wrap: wrap;
      align-items: center;
      justify-content: space-between;
      width: 187px;
      height: 100%;
      padding: 0 50px 0 26px;
      overflow: visible;
      background: #e2231a;
      color: #fff;
      font-size: 15px;
      letter-spacing: 3px;
      line-height: 30px;
      cursor: pointer;

      &:hover {
        #category_sort {
          display: block;
        }
      }

      img {
        width: 12px;
        height: 12px;
      }
    }

    nav {
      display: flex;
      align-items: center;
      width: 1013px;
      height: 30px;
      margin-left: 8px;
      overflow: hidden;
      line-height: 30px;

      a {
        display: inline-block;
        box-sizing: border-box;
        width: max-content;
        height: 37px;
        margin: 0 20px;
        padding: 0 7px;
        color: #333;
        font-size: 15px;
        line-height: 37px;
      }

      a:hover {
        border-bottom: 3px solid #e2231a;
        color: #e2231a;
      }
    }
  }
</style>
