<!-- 模板编辑---通用的商品展示组件 -->
<template>
  <div :class="[layoutClass]" v-show="isShow">
    <div
      class="goods_ad_img relative"
    >
      <img
        :src="goodsInfo.mainImage?goodsInfo.mainImage:goodsInfo.goodsImage"
        class="goods_img"
        v-show="goodsInfo.mainImage?goodsInfo.mainImage:goodsInfo.goodsImage"
        :style="{ objectFit: imgInfo.cover || 'cover' }"
      />
      <div class="placeholder flex_row_center_center">
        【{{ imgInfo.width }}*{{ imgInfo.height }}】
      </div>
    </div>

    <div class="gap" :style="gapStyle"></div>

    <div class="goods_ad_text">
      <div :style="nameStyle" class="name_default">{{ goodsInfo.goodsName }}</div>
      <div class="flex_row_start_center">
        <div class="price_default" :style="priceStyle">
          <span class="price_tag"> ￥</span>
          <span class="int">{{ getPartNumber(goodsInfo.goodsPrice, 'int') }}</span>
          <span class="decimal">{{ getPartNumber(goodsInfo.goodsPrice, 'decimal') }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { computed } from 'vue';
  import lodash from 'lodash-es';
  import { getPartNumber } from '/@/utils/utils';
  import { AliSvgIcon } from '@/components/SvgIcon';

  const props = defineProps({
    //垂直布局还是水平布局
    mode: String,
    //商品信息
    goodsInfo: {
      type: Object,
      default: () => {},
    },
    //图片信息
    imgInfo: {
      type: Object,
      default: () => ({
        width: 0,
        height: 0,
        cover: 'cover',
      }),
    },
    // 图片和商品文本之间的间隔
    gap: Number,
    //商品名样式
    nameStyle: [Object, String],
    //价格样式
    priceStyle: [Object, String],
    //是否显示立即购买按钮
    showPurchase: Boolean,
    // 是否显示销售数
    showSale: Boolean,
    //是否显示占位空标识
    placeholder: Boolean,
    // 是否显示原价
    showMarketPrice: Boolean,
  });
  const layoutClass = computed(() => {
    return props.mode == 'horizonal' ? 'GoodsWrapAdv flex flex-row' : 'GoodsWrapAdv flex flex-col';
  });

  const isShow = props.placeholder ? !lodash.isEmpty(props.goodsInfo) : true;

  const gapStyle = computed(() => {
    return props.mode == 'horizonal'
      ? { marginLeft: props.gap + 'px' }
      : { marginTop: props.gap + 'px' };
  });
</script>

<style lang="less" scoped>
.GoodsWrapAdv{

  .diy_goods_component {
    width: 100%;
    height: 100%;
  }

  .goods_ad_img {
    margin: 0 auto;
    width: 234px;
    height: 234px;

    .goods_img {
      position: absolute;
      z-index: 2;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }

    .placeholder {
      width: 100%;
      height: 100%;
      background-color: #eee;
      font-size: 15px;
    }
  }

  .goods_ad_text {
    flex: 1;

    .name_default {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-height: 21px;
      -webkit-box-orient: vertical;
      word-break: break-word;
      padding-left: 13px;
      padding-right: 20px;
      white-space: normal;
    }

    .price_default {
      color: red;
      padding-left: 13px;
      padding-right: 20px;
      .price_tag{
        font-size: 14px;
        font-weight: 700;
        margin-right: 2px;
      }
      .decimal{
        font-weight: bold;
        font-size: 14px;
      }
      .int{
        font-size: 20px;
        font-weight: bold;
        margin: 0 0 0 -2px;
        font-family: Arial, Helvetica, sans-serif;
      }
    }

    .original_default {
      margin-left: 10px;
      color: #999;
      font-size: 12px;
      text-decoration: line-through;
    }

    .purchase_button {
      display: inline-block;
      padding: 4px 17px;
      border-radius: 11px;
      background: #ff0036;
      color: #fff;
      font-size: 1rem;
      font-weight: bold;
    }

    .sale_default {
      color: #666;
      font-size: 13px;
    }
  }
}
</style>
