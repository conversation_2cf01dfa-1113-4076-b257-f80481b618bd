<template>
  <div class="section_padding">
    <div class="section_padding_back_add flex_com" style="">
      <div ref="topRef">
        <SldComHeader title="实例化装修模板管理" />
        <div class="mt-3 relative">
          <BasicForm @register="tplRegister" @submit="handleSubmit" @reset="handleReset" />
        </div>
        <div class="mt-1 relative">
          <RadioGroup v-model:value="tplType" @change="() => getTemplateList()">
            <RadioButton value="all">全部</RadioButton>
            <RadioButton value="main_banner">主轮播</RadioButton>
            <RadioButton value="adv_floor">广告楼层</RadioButton>
            <RadioButton value="goods_floor">商品楼层</RadioButton>
          </RadioGroup>
        </div>
        <div class="mt-3 operate_bg">
          <SldIconBtn
            wrapML="7"
            wrapMR="7"
            svg="iconxinzeng"
            text="添加装修模板"
            svgColor="rgb(250, 111, 30)"
            @click="addTplVisible = true"
          />
        </div>
      </div>
      <div class="relative flex-1">
        <div class="absolute inset-0 overflow-y-auto overflow-x-hidden">
          <Spin :spinning="getTplLoading">
            <div class="mt-3 flex flex-wrap justify-between" ref="tplOutsideEl">
              <div v-for="(fallItem, fallIndex) in fallTplList" :key="fallIndex" class="fall_item">
                <WebTplItem
                  :tplElWith="tplElWith"
                  :tplItem="item"
                  v-for="(item, index) in fallItem"
                  :key="index"
                  @confirm="getTemplateList"
                />
              </div>
              <div
                class="flex_column_center_center w-full pt-[100px]"
                v-if="!getTplLoading && !tplList.length"
              >
                <Empty :image="Empty.PRESENTED_IMAGE_SIMPLE" description="暂无数据" />
              </div>
            </div>
          </Spin>
        </div>
      </div>

      <AddTemplate
        :visible="addTplVisible"
        @cancel="addTplVisible = false"
        @confirm="
          () => {
            addTplVisible = false;
            getTemplateList();
          }
        "
      />
    </div>
  </div>
</template>

<script setup>
  import SldComHeader from '@/components/SldComHeader/index.vue';
  import AddTemplate from './template/components/AddTemplate.vue';
  import { BasicForm, useForm } from '@/components/Form';
  import { RadioGroup, RadioButton, Spin, Empty } from 'ant-design-vue';
  import SldIconBtn from '@/components/SldIconBtn/index.vue';
  import WebTplItem from './template/components/WebTplItem.vue';
  import { ref, computed, onMounted, unref } from 'vue';
  import { useElementSize } from '@vueuse/core';
  import { tpl_list_handler } from './template/actions/template_list';

  const tplOutsideEl = ref();
  const { width: outsideWidth } = useElementSize(tplOutsideEl);
  const tplElWith = computed(() => unref(outsideWidth) / 2 - 5);

  const tplSchemas = [
    {
      field: 'name',
      component: 'Input',
      componentProps: {
        placeholder: '请输入模板名称',
        size: 'default',
      },
      label: '实例化名称',
      labelWidth: 80,
    },
    {
      field: 'isEnable',
      component: 'Select',
      componentProps: {
        placeholder: '请选择使用状态',
        size: 'default',
        options: [
          {
            label: '全部',
            value: -1,
          },
          {
            label: '启用',
            value: 1,
          },
          {
            label: '禁用',
            value: 0,
          },
        ],
      },
      label: '使用状态',
      labelWidth: 80,
    },
  ];
  const [tplRegister] = useForm({
    labelWidth: 120,
    // @ts-ignore
    schemas: tplSchemas,
    baseColProps: { span: 6 },
    actionColOptions: { span: 24 },
  });

  const { tplList, tplType, getTemplateList, getTplLoading, fallTplList } = tpl_list_handler();

  const addTplVisible = ref(false);

  const handleSubmit = (e) => {
    let values = { ...e };
    if (e.isEnable == -1) delete values.isEnable;
    getTemplateList(values);
  };

  const handleReset = () => {
    getTemplateList();
  };

  onMounted(() => {
    getTemplateList();
  });
</script>

<style lang="scss" scoped>
  .fall_item {
    width: 49.5%;
  }

  .template_list_cont {
    height: calc(100% - 100px);
  }
</style>
