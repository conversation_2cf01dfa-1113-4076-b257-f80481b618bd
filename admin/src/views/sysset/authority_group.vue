<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <SldComHeader :type="1" :title="'权限组管理'" />
      <BasicTable @register="standardTable" style="padding: 0">
        <template #tableTitle>
          <div class="toolbar">
            <div class="toolbar_btn" @click="handleClick(null, 'add')">
              <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#fff" />
              <span>新增权限组</span>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.key == 'action'">
            <template v-if="record.isInner == 1"> -- </template>
            <template v-else>
              <span class="common_page_edit" @click="handleClick(record, 'edit')">编辑</span>
              <span class="common_page_edit" @click="handleClick(record, 'auth')">授权</span>
              <Popconfirm
                title="删除后不可恢复，是否确定删除？"
                @confirm="handleClick(record, 'del')"
              >
                <span class="common_page_edit">删除</span>
              </Popconfirm>
            </template>
          </template>
          <template v-else-if="column.key">
            {{ text ? text : '--' }}
          </template>
        </template>
      </BasicTable>
      <SldModal
        :width="width"
        :title="title"
        :visible="visible"
        :content="content"
        :confirmBtnLoading="confirmBtnLoading"
        :showFoot="true"
        @cancle-event="handleCancle"
        @confirm-event="handleConfirm"
        @tree-select-event="handleTreeSelect"
      />
    </div>
  </div>
</template>
<script>
  import { defineComponent, ref, onMounted } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { Popconfirm } from 'ant-design-vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import {
    getRoleResource,
    getRoleManageList,
    addRole,
    updateRole,
    authRole,
    delRole,
  } from '/@/api/sysset/sysset';
  import SldModal from '@/components/SldModal/index.vue';
  import { failTip, sucTip } from '/@/utils/utils';

  export default defineComponent({
    components: {
      SldComHeader,
      BasicTable,
      Popconfirm,
      SldModal,
    },
    setup() {
      const [standardTable, { reload }] = useTable({
        api: (arg) => getRoleManageList({ ...arg }),
        fetchSetting: {
          pageField: 'current',
          sizeField: 'pageSize',
          listField: 'data.list',
          totalField: 'data.pagination.total',
        },
        columns: [
          {
            title: '权限组名称',
            dataIndex: 'roleName',
            width: 100,
          },
          {
            title: '权限组描述',
            dataIndex: 'description',
            width: 100,
          },
          {
            title: '创建时间',
            dataIndex: 'createTime',
            width: 100,
          },
          {
            title: '更新时间',
            dataIndex: 'updateTime',
            width: 100,
          },
        ],
        actionColumn: {
          width: 100,
          title: '操作',
          dataIndex: 'action',
        },
        useSearchForm: true,
        formConfig: {
          schemas: [
            {
              field: 'roleName',
              component: 'Input',
              colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                maxlength: 10,
                placeholder: '请输入权限组名称',
                size: 'default',
              },
              label: '权限组名称',
              labelWidth: 90,
            },
          ],
        },
        bordered: true,
        striped: false,
        showIndexColumn: true,
      });
      const width = ref(500);
      const title = ref('');
      const visible = ref(false);
      const content = ref([]);
      const confirmBtnLoading = ref(false);
      const operate_id = ref('');
      const operate_type = ref('');

      const operate_add_edit = ref([
        {
          type: 'input',
          label: `权限组名称`,
          name: 'roleName',
          placeholder: `请输入权限组名称`,
          extra: '最多输入10个字',
          maxLength: 10,
          showCount: true,
          rules: [
            {
              required: true,
              whitespace: true,
              message: '请输入权限组名称',
            },
          ],
        },
        {
          type: 'textarea',
          label: `权限组描述`,
          name: 'description',
          placeholder: `请输入权限组描述`,
          extra: '最多输入50个字',
          maxLength: 50,
          showCount: true,
          rules: [
            {
              required: true,
              whitespace: true,
              message: '请输入权限组描述',
            },
          ],
        },
      ]); //添加、编辑权限组弹窗数据

      const operate_auth = ref([
        {
          type: 'tree',
          label: '',
          name: 'resourceIds',
          checkable: true,
          treeData: [],
          fieldNames: {
            key: 'resourceId',
            title: 'content',
            children: 'children',
          },
          initialValue: [],
          maxHeight: 400,
          callback: true,
        },
      ]); //授权弹窗数据

      //表格点击回调事件
      function handleClick(item, type) {
        operate_type.value = type;
        if (item) {
          operate_id.value = item.roleId;
        }
        if (type == 'add') {
          title.value = '添加权限组';
          visible.value = true;
          content.value = JSON.parse(JSON.stringify(operate_add_edit.value));
        } else if (type == 'edit') {
          title.value = '编辑权限组';
          visible.value = true;
          let data = JSON.parse(JSON.stringify(operate_add_edit.value));
          data.map((items) => {
            items.initialValue = item[items.name] ? item[items.name] : undefined;
          });
          content.value = data;
        } else if (type == 'auth') {
          title.value = '授权';
          visible.value = true;
          let data = JSON.parse(JSON.stringify(operate_auth.value));
          data[0].initialValue = item.resourcesList;
          content.value = data;
        } else if (type == 'del') {
          operate_role({ roleId: item.roleId });
        }
      }

      //弹窗取消事件
      function handleCancle() {
        visible.value = false;
        content.value = [];
        operate_id.value = '';
      }

      //弹窗确认事件
      function handleConfirm(val) {
        if (operate_type.value == 'add') {
          operate_role(val);
        } else if (operate_type.value == 'edit') {
          val.roleId = operate_id.value;
          operate_role(val);
        } else if (operate_type.value == 'auth') {
          if (!val.resourceIds || val.resourceIds.length == 0) {
            failTip('请选择数据');
          } else {
            val.roleId = operate_id.value;
            val.resourceIds = val.resourceIds.join(',');
            operate_role(val);
          }
        }
      }

      //权限组操作
      const operate_role = async (params) => {
        confirmBtnLoading.value = true;
        let res = {};
        if (operate_type.value == 'add') {
          res = await addRole(params);
        } else if (operate_type.value == 'edit') {
          res = await updateRole(params);
        } else if (operate_type.value == 'auth') {
          res = await authRole(params);
        } else if (operate_type.value == 'del') {
          res = await delRole(params);
        }
        confirmBtnLoading.value = false;
        if (res.state == 200) {
          sucTip(res.msg);
          handleCancle();
          reload();
        } else {
          failTip(res.msg);
        }
      };

      onMounted(() => {
        get_role_resource();
      });

      //获取资源列表数据
      const get_role_resource = async () => {
        const res = await getRoleResource();
        if (res.state == 200) {
          operate_auth.value[0].treeData = res.data.list;
        }
      };

      function handleTreeSelect(val) {
        content.value[0].initialValue = val;
      }

      return {
        standardTable,
        get_role_resource,
        width,
        title,
        visible,
        content,
        confirmBtnLoading,
        operate_id,
        operate_type,
        operate_add_edit,
        operate_auth,
        handleClick,
        handleCancle,
        handleConfirm,
        handleTreeSelect,
      };
    },
  });
</script>
<style lang="less" scoped></style>
