<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <a-spin :spinning="spinning">
        <SldComHeader
          :title="'物流配置'"
        />
        <a-tabs activeKey="1" type="card">
          <a-tab-pane key="1" tab="快递配置" />
        </a-tabs>
        <div class="section_padding_tab_top"></div>
        <div class="app_set_scroll">
          <StandardTableRow
            width="100%"
            :data="expressData"
            @callback-event="callbackEvent"
            @submit-event="submitEvent"
          />
        </div>
      </a-spin>
    </div>
  </div>
</template>
<script>
  import { defineComponent, ref, onMounted } from 'vue';
  import { Spin, Tabs, Modal } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import StandardTableRow from '/@/components/StandardTableRow/index.vue';
  import { getExpressSetting, saveBasicSiteSetting } from '/@/api/sysset/sysset';
  import { failTip, sucTip } from '/@/utils/utils';
  import { SldForbiddenEditUrl } from '/@/enums/baseEnum';

  export default defineComponent({
    components: {
      [Spin.name]: Spin,
      SldComHeader,
      [Tabs.name]: Tabs,
      [Tabs.TabPane.name]: Tabs.TabPane,
      StandardTableRow,
    },
    setup() {
      const spinning = ref(false);
      const expressData = ref([]);
      const clickEvent = ref(false); //事件防抖动

      //获取短信配置
      const get_sms_setting = async () => {
        spinning.value = true;
        const res = await getExpressSetting();
        if (res.state == 200 && res.data) {
          let data = [];
          res.data.map((item) => {
            if(item.type == 1){
              data.push({
              require: true,
              type: 'input',
              label: item.title,
              key: item.name,
              placeholder: '请输入' + item.title,
              width: 300,
              initValue: '',
              value: item.value,
              inputType: 'text',
              callback: true,
              desc: item.description,
              desc_width: 350,
              rules: [
                {
                  required: true,
                  whitespace: true,
                  message: '请输入' + item.title,
                },
              ],
            });
            }else if(item.type == 5){
              data.push({
              type: 'radio',
              require: true,
              label: item.title,
              key: item.name,
              width: 300,
              initValue: '',
              value: item.value,
              desc: item.description,
              desc_width: 350,
              callback: true,
              data: [
                { key: 0, value: '0', title: '免费' },
                { key: 1, value: '1', title: '付费' },
              ],
            });
            }
            
          });
          if (data.length > 0) {
            data.push({
              type: 'button',
              width: 300,
              showSubmit: true,
              submitText: '保存',
              showCancle: false,
            });
          }
          expressData.value = data;
        } else {
          failTip(res.msg);
        }
        spinning.value = false;
      };

      const callbackEvent = (item) => {
        let temp = expressData.value.filter((items) => items.key == item.contentItem.key);
        if (temp.length > 0) {
          temp[0].value = item.val1;
        }
      };

      const submitEvent = (val) => {
        save_base_site(val);
      };

      //保存站点基本配置
      const save_base_site = async (params) => {
        if (SldForbiddenEditUrl && window.location.href.indexOf(SldForbiddenEditUrl) != -1) {
          Modal.warning({
            title: '提示',
            content:
              '演示站配置数据（物流配置）不支持更改，如需更改，请联系商务',
            okText: '确定',
          });
        } else {
          if (clickEvent.value) return;
          clickEvent.value = true;
          const res = await saveBasicSiteSetting(params);
          if (res.state == 200) {
            sucTip(res.msg);
          } else {
            failTip(res.msg);
          }
          clickEvent.value = false;
        }
      };

      onMounted(() => {
        get_sms_setting();
      });

      return {
        spinning,
        expressData,
        clickEvent,
        get_sms_setting,
        callbackEvent,
        submitEvent,
        save_base_site,
      };
    },
  });
</script>
<style lang="less" scoped></style>
