<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader :type="1" :title="'协议管理'" />
      <BasicTable @register="standardTable" style="padding: 0">
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.key == 'action'">
            <span class="common_page_edit" @click="handleClick(record)">编辑</span>
          </template>
          <template v-else-if="column.key">
            {{ text ? text : '--' }}
          </template>
        </template>
      </BasicTable>
    </div>
  </div>
</template>
<script>
  import { defineComponent } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import { getAgreementList } from '/@/api/sysset/sysset';
  import { useGo } from '/@/hooks/web/usePage';

  export default defineComponent({
    components: {
      SldComHeader,
      BasicTable,
    },
    setup() {
      const go = useGo();
      const [standardTable] = useTable({
        api: (arg) => getAgreementList({ ...arg }),
        fetchSetting: {
          pageField: 'current',
          sizeField: 'pageSize',
          listField: 'data.list',
          totalField: 'data.pagination.total',
        },
        columns: [
          {
            title: '标题',
            dataIndex: 'title',
            width: 100,
          },
          {
            title: '时间',
            dataIndex: 'updateTime',
            width: 100,
          },
        ],
        actionColumn: {
          width: 100,
          title: '操作',
          dataIndex: 'action',
        },
        bordered: true,
        striped: false,
        showIndexColumn: true,
      });

      //表格点击回调事件
      function handleClick(item) {
        go(`/sysset_agreement/lists_to_edit?agreementCode=${item.agreementCode}`);
      }

      return {
        go,
        standardTable,
        handleClick,
      };
    },
  });
</script>
<style lang="less" scoped></style>
