<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <SldComHeader
        :title="'基本设置'"
      />
      <a-tabs activeKey="1" type="card">
        <a-tab-pane key="1" tab="微信配置" />
      </a-tabs>
      <div class="section_padding_tab_top"></div>
      <div class="app_set_scroll">
        <StandardTableRow
          width="100%"
          :data="wxData"
          @callback-event="callbackEvent"
          @submit-event="submitEvent"
        />
      </div>
    </div>
  </div>
</template>
<script>
  import { defineComponent, ref, onMounted } from 'vue';
  import { Tabs, Modal } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import StandardTableRow from '/@/components/StandardTableRow/index.vue';
  import { getWxSetting, saveBasicSiteSetting } from '/@/api/sysset/sysset';
  import { SldForbiddenEditUrl } from '/@/enums/baseEnum';
  import { failTip, sucTip } from '/@/utils/utils';

  export default defineComponent({
    components: {
      SldComHeader,
      [Tabs.name]: Tabs,
      [Tabs.TabPane.name]: Tabs.TabPane,
      StandardTableRow,
    },
    setup() {
      const wxData = ref([]);
      const clickEvent = ref(false); //事件防抖动

      //获取微信配置详情
      const get_wx_setting = async () => {
        const res = await getWxSetting();
        if (res.state == 200 && res.data) {
          let data = [];
          res.data.map((item) => {
            if (item.type == 1) {
              data.push({
                type: 'input',
                label: item.title,
                key: item.name,
                placeholder: '请输入' + item.title,
                width: 300,
                right_width: 380,
                initValue: '',
                value: item.value,
                inputType: 'text',
                callback: true,
                desc: item.description,
                desc_width: 380,
              });
            } else if (item.type == 4) {
              data.push({
                type: 'switch',
                label: item.title,
                key: item.name,
                width: 300,
                desc_width: 300,
                initValue: '',
                value: item.value == 1 ? true : false,
                callback: true,
                desc: item.description,
              });
            }
          });
          if (data.length > 0) {
            data.push({
              type: 'button',
              width: 300,
              showSubmit: true,
              submitText: '保存',
              showCancle: false,
            });
          }
          wxData.value = data;
        } else {
          failTip(res.msg);
        }
      };

      const callbackEvent = (item) => {
        let temp = wxData.value.filter((items) => items.key == item.contentItem.key);
        if (temp.length > 0) {
          temp[0].value = item.val1;
        }
      };

      const submitEvent = (val) => {
        if (SldForbiddenEditUrl && window.location.href.indexOf(SldForbiddenEditUrl) != -1) {
          Modal.warning({
            title: '提示',
            content:
              '演示站配置数据（授权登录）不支持更改，如需更改，请联系商务',
            okText: '确定',
          });
        } else {
          for (let key in val) {
            if (typeof val[key] == 'boolean') {
              val[key] = val[key] ? '1' : '0';
            }
          }
          save_base_site(val);
        }
      };

      //保存站点基本配置
      const save_base_site = async (params) => {
        if (clickEvent.value) return;
        clickEvent.value = true;
        const res = await saveBasicSiteSetting(params);
        if (res.state == 200) {
          sucTip(res.msg);
        } else {
          failTip(res.msg);
        }
        clickEvent.value = false;
      };

      onMounted(() => {
        get_wx_setting();
      });

      return {
        wxData,
        clickEvent,
        get_wx_setting,
        callbackEvent,
        submitEvent,
        save_base_site,
      };
    },
  });
</script>
<style lang="less" scoped></style>
