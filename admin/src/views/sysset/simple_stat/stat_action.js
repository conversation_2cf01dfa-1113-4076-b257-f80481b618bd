import { ref } from 'vue';
import {
  getStat_TradeInfo,
  getStat_UserInfo,
  getStat_ViewInfo,
  getStat_GoodsInfo,
} from '@/api/sys/statAnalysis';
import { sldComLanguage } from '@/utils/utils';

async function handlerApi() {
  const res = await this.apiFunction();
  if (res?.state == 200) {
    this.loading = false;
    this.form.children.map((item) => {
      item.value = res.data[item.mapKey];
    });
    this.form.statsTime = res.data.statsTime;
  }
}

const todayTradeData = {
  loading: true,
  form: {
    update_time: '',
    label: sldComLanguage(`${'今日交易概况'}`),
    key: 'todayTrade',
    statsTime: '',
    children: [
      {
        key: `${'下单数'}`,
        value: '',
        tip: `${'统计时间内，全平台用户成功提交订单的笔数总和'}`,
        mapKey: 'orderSubmitNum',
      },
      {
        key: `${'下单金额(元)'}`,
        value: '',
        tip: `${'统计时间内，全平台用户成功提交订单的金额总和'}`,
        mapKey: 'orderSubmitAmount',
        isMoney: true,
      },
      {
        key: `${'支付订单数'}`,
        value: '',
        tip: `${'统计时间内，全平台用户成功支付的订单数量总和'}`,
        mapKey: 'orderPayNum',
      },
      {
        key: `${'支付金额(元)'}`,
        value: '',
        tip: `${'统计时间内，全平台用户成功支付的金额总和'}`,
        mapKey: 'orderPayAmount',
        isMoney: true,
      },
    ],
  },
  apiFunction: getStat_TradeInfo,
  handlerApi,
}; //今日交易信息
const todayFlowData = {
  loading: true,
  form: {
    update_time: '',
    label: `${'今日流量概况'}`,
    key: 'todayFlow',
    statsTime: '',
    children: [
      {
        key: `${'访客数'}`,
        value: '',
        tip: `${'统计时间内，全平台所有页面的去重人数总和'}`,
        mapKey: 'visitorNum',
      },
      {
        key: `${'浏览量'}`,
        value: '',
        tip: `${'统计时间内，全平台所有页面被访问的次数总和'}`,
        mapKey: 'viewNum',
      },
      {
        key: `${'商品访客数'}`,
        value: '',
        tip: `${'统计时间内，访问商品详情页的去重人数'}`,
        mapKey: 'goodsVisitorNum',
      },
      {
        key: `${'商品浏览量'}`,
        value: '',
        tip: `${'统计时间内，访问商品详情页的人次数'}`,
        mapKey: 'goodsViewNum',
      },
    ],
  },
  apiFunction: getStat_ViewInfo,
  handlerApi,
}; //今日流量信息
const todayGoodsData = {
  loading: true,
  form: {
    update_time: '',
    label: `${'今日商品概况'}`,
    key: 'todayGoods',
    statsTime: '',
    children: [
      {
        key: `${'商品总数'}`,
        value: '',
        tip: `${'截止至当前时间，全平台店铺的商品spu总数'}`,
        mapKey: 'goodsTotalNum',
      },
      {
        key: `${'新增商品数'}`,
        value: '',
        tip: `${'统计时间内，全平台新增商品spu数'}`,
        mapKey: 'newGoodsNum',
      },
      {
        key: `${'在售商品数'}`,
        value: '',
        tip: `${'截止至当前时间，状态为在售的商品数量'}`,
        mapKey: 'saleGoodsNum',
      },
      {
        key: `${'下架商品数'}`,
        value: '',
        tip: `${'截止至当前时间，全平台违规下架的商品总数'}`,
        mapKey: 'shelfGoodsNum',
      },
    ],
  },
  apiFunction: getStat_GoodsInfo,
  handlerApi,
}; //今日商品信息
const todayMemberData = {
  loading: true,
  form: {
    update_time: '',
    label: `${'今日用户概况'}`,
    key: 'todayMember',
    statsTime: '',
    children: [
      {
        key: `${'店铺总数'}`,
        value: '',
        tip: `${'截止至当前时间，全平台商家总数，包括自营商家和入驻商家'}`,
        mapKey: 'storeNum',
      },
      {
        key: `${'新增店铺数'}`,
        value: '',
        tip: `${'统计时间内，全平台新增商家数'}`,
        mapKey: 'newStoreNum',
      },
      {
        key: `${'会员总数'}`,
        value: '',
        tip: `${'截止至当前时间，全平台注册会员数'}`,
        mapKey: 'memberNum',
      },
      {
        key: `${'新增会员数'}`,
        value: '',
        tip: `${'统计时间内，全平台首次注册并发生访问行为的用户。'}`,
        mapKey: 'newMemberNum',
      },
    ],
  },
  apiFunction: getStat_UserInfo,
  handlerApi,
}; //今日会员信息
export const handleTodayData = () => {
  const formList = ref([
    todayTradeData,
    todayFlowData,
    todayGoodsData,
    todayMemberData,
  ]);
  const actions = () => {
    formList.value.forEach((item) => {
      item.handlerApi()
    });
  };
  return { formList, actions };
};

