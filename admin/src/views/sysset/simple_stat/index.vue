<template>
  <div class="section_padding">
    <div class="section_padding_home_scroll simple_stat">
      <!-- 代办事项start -->
      <div class="p-3 pt-4 bg-white rounded">
        <div class="position-title font-bold !text-[14px] mb-2 mr-5"> {{ $sldComLanguage('待办事项') }} </div>
        <div class="flex pl-3">
          <div
            class="flex basic-goods"
            v-for="(item, index) in waitDealData"
            :key="index"
            @click="goPath(item)"
          >
            <div class="w-71px h-71px left_pending_icon">
              <img
                :src="getImageSets(index + 1)"
                alt=""
                style="max-width: 100%; max-height: 100%"
              />
            </div>
            <div class="ml-5 item_desc">
              <div class="basic-item_title">{{ item.name }}</div>
              <div class="basic-item_num">{{ item.value }}</div>
            </div>
          </div>
        </div>
      </div>
      <!-- 代办事项end -->

      <!-- 今日数据start -->
      <div class="flex flex-row w-full">
        <div
          class="relative mt-[10px] today_info_panel flex-1"
          v-for="(item, index) in todayStatData"
          :key="index"
        >
          <div class="flex justify-end items-center mx-16px !ml-0" style="margin-top: 11px">
            <div class="position-title1" :style="{ backgroundImage: `url(${banner})` }">{{
              item.form.label
            }}</div>
            <div class="update_time">{{ item.form.statsTime }}</div>
          </div>
          <Spin :spinning="item.loading">
            <div class="flex flex-wrap mt-45px ml-2/14">
              <div
                class="w-2/4 h-103px"
                v-for="(children, cIndex) in item.form.children"
                :key="cIndex"
              >
                <SldStatCard
                  :label="children.key"
                  :tip="children.tip"
                  :value="children.value"
                  :is-bold="false"
                  :is-money="children.isMoney"
                />
              </div>
            </div>
          </Spin>
        </div>
      </div>
      <!-- 今日数据end -->

      <!-- 支付下单趋势---流量趋势start -->
      <div class="flex flex-row justify-center">
        <SldStatCharts
          :title="$sldComLanguage('支付/下单金额趋势')"
          mode="linear"
          :date-picker="true"
          :api-url="API.GET_PAYTREND"
          :data-handler="payTrendDataHandler"
        />
        <div class="min-w-2 min_width"></div>
        <SldStatCharts
          mode="linear"
          :title="$sldComLanguage('流量趋势')"
          name="flowTrend"
          :date-picker="true"
          :api-url="API.GET_VIEWTREND"
          :data-handler="flowTrendDataHandler"
        />
      </div>
      <!-- 支付下单趋势---流量趋势end -->

      <!-- 会员/店铺新增趋势--地域分布start -->
      <div class="flex flex-row justify-center">
        <SldStatCharts
          mode="linear"
          :title="$sldComLanguage('会员/店铺新增趋势')"
          :date-picker="true"
          :api-url="API.GET_NEWTREND"
          :data-handler="newTrendDataHandler"
        />
        <div class="min-w-2 min_width"></div>
        <SldStatCharts
          mode="geo"
          :title="$sldComLanguage('地域分布')"
          :date-picker="false"
          :api-url="API.GET_MEMBER_REGION"
          :data-handler="regionDataHandler"
          @register="regionDistribution_register"
        >
          <template #extraSlot>
            <RadioGroup
              v-model:value="regionTabValue"
              size="small"
              @change="regionDistributionChange"
            >
              <RadioButton :value="1">{{ $sldComLanguage('会员') }}</RadioButton>
              <RadioButton :value="2">{{ $sldComLanguage('店铺') }}</RadioButton>
            </RadioGroup>
          </template>
        </SldStatCharts>
      </div>
      <!-- 会员/店铺新增趋势--地域分布end -->

      <!-- 店铺销售排行-商品销售排行start -->
      <div class="flex flex-row">
        <SldStatRank
          :options="storeRankOption"
          :title="$sldComLanguage('店铺销售排行') + ' - TOP10'"
          name="storeRank"
          :date-picker="true"
          @register="storeRank_register"
          :is-index="true"
        />
        <div class="min-w-2 min_width"></div>
        <SldStatRank
          :options="goodsRankOption"
          :title="$sldComLanguage('商品销售排行') + ' - TOP10'"
          :date-picker="true"
          :is-index="true"
          @register="goodsRank_register"
        />
      </div>
      <!-- 店铺销售排行-商品销售排行end -->

      <!-- 品类销售排行-品牌销售占比start -->
      <div class="flex flex-row">
        <SldStatRank
          :options="cateRankOption"
          :title="$sldComLanguage('品类销售排行') + ' - TOP10'"
          @register="cateRank_register"
          :date-picker="true"
          :is-index="true"
        />
        <div class="min-w-2 min_width"></div>
        <SldStatCharts
          :title="$sldComLanguage('品牌销售占比')"
          mode="pie"
          @register="brandSale_register"
          :api-url="API.GET_BRAND_PER"
          :param-handler="brandSale_paramHandler"
          :data-handler="brandSaleDataHandler"
          :date-picker="true"
        >
          <template #extraSlot>
            <RadioGroup v-model:value="brandSaleTabValue" size="small" @change="brandSaleTabChange">
              <RadioButton :value="1">{{ $sldComLanguage('销售额') }}</RadioButton>
              <RadioButton :value="2">{{ $sldComLanguage('销量') }}</RadioButton>
            </RadioGroup>
          </template>
        </SldStatCharts>
      </div>
      <!-- 品类销售排行-品牌销售占比end -->
    </div>
  </div>
</template>

<script setup>
  import { getImagePath } from '@/utils';
  import { getWaitDeal, API } from '@/api/sys/statAnalysis';
  import {
    SldStatCharts,
    useStatCharts,
    SldStatRank,
    useStatRank,
    SldStatCard,
  } from '/@/components/SldStat';
  import { RadioGroup, RadioButton } from 'ant-design-vue';
  import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
  import { handleTodayData } from './stat_action';
  import { Spin } from 'ant-design-vue';
  import { useRouter } from 'vue-router';

  const router = useRouter();
  const vm = getCurrentInstance();
  const $sldComLanguage = vm?.appContext.config.globalProperties.$sldComLanguage;
  const banner = getImagePath('images/today_info_bg.png');
  const todayStatData = ref([]);
  const loadState = reactive({
    brandSale: false,
    payTrend: false,
    newTrend: false,
    region: false,
    flowTrend: false,
  });
  const payTrendOption = reactive({});
  const flowTrendOption = reactive({});
  const newTrendOption = reactive({});
  const regionOption = reactive({});
  const brandSaleOption = reactive({
    series: [],
    isRing: false,
    isLegend: true,
  });

  const getImageSets = (index) => {
    return getImagePath(`images/stat_item_icon_${index}.png`);
  };
  const waitDealData = ref([]);
  const waitDealDataMap = {
    auditStoreNum: {
      name: $sldComLanguage('待审核店铺'),
      path: '/manage_store/settle_store_list?tab=check',
    },
    auditGoodsNum: {
      name: $sldComLanguage('待审核商品'),
      path: '/manage_product/goods_list?tab=check',
    },
    auditBrandNum: {
      name: $sldComLanguage('待审核品牌'),
      path: '/manage_product/brand?tab=check',
    },
    confirmReturnNum: {
      name: $sldComLanguage('待确认退款单'),
      path: '/manage_order/service?tab=2',
    },
    dealBillNum: {
      name: $sldComLanguage('待处理结算单'),
      path: '/manage_bill/lists',
    },
  };
  const handleWaitDeal = async () => {
    const res = await getWaitDeal();
    if (res?.state == 200) {
      for (const item in waitDealDataMap) {
        // @ts-ignore
        waitDealData.value.push({
          name: waitDealDataMap[item].name,
          value: res.data[item],
          path: waitDealDataMap[item].path,
        });
      }
    }
  };

  const { formList, actions } = handleTodayData();
  const getTodayStat = async () => {
    todayStatData.value = formList.value;
    actions();
  };

  //支付下单趋势start--------------------------------------------
  const payTrendDataHandler = (return_data) => {
    const { orderAmountList, payAmountList } = return_data;
    const orderSubmitAmountList = orderAmountList.map((or) => or.orderSubmitAmount);
    const orderPayAmountList = payAmountList.map((or) => or.orderPayAmount);
    loadState.payTrend = false;
    payTrendOption.xAxisData = orderAmountList.map((item) => item.statsTime);
    payTrendOption.series = [
      {
        name: $sldComLanguage('下单金额'),
        data: orderSubmitAmountList,
        type: 'line',
      },
      {
        name: $sldComLanguage('支付金额'),
        data: orderPayAmountList,
        type: 'line',
      },
    ];
    payTrendOption.maxMount = Math.max.apply(null, [
      ...orderSubmitAmountList,
      ...orderPayAmountList,
    ]);
    return payTrendOption;
  };
  //支付下单趋势end--------------------------------------------

  //流量趋势start-------------------------------------------
  const flowTrendDataHandler = (return_data) => {
    const { viewNumList, visitorNumList } = return_data;
    const viewNumLists = viewNumList.map((or) => or.viewNum);
    const visitorNumLists = visitorNumList.map((or) => or.visitorNum);
    loadState.flowTrend = false;
    flowTrendOption.xAxisData = viewNumList.map((item) => item.statsTime);
    flowTrendOption.series = [
      {
        name: $sldComLanguage('访问量'),
        data: viewNumLists,
        type: 'line',
      },
      {
        name: $sldComLanguage('访客数'),
        data: visitorNumLists,
        type: 'line',
      },
    ];
    flowTrendOption.maxMount = Math.max.apply(null, [...viewNumLists, ...viewNumLists]);
    return flowTrendOption;
  };
  //流量趋势end ---------------------------------------------

  //会员/店铺新增趋势start-----------------------------------
  const newTrendDataHandler = (return_data) => {
    const { memberList, storeList } = return_data;
    const memberLists = memberList.map((or) => or.newMemberNum);
    const storeLists = storeList.map((or) => or.newStoreNum);
    loadState.newTrend = false;
    newTrendOption.xAxisData = memberList.map((item) => item.statsTime);
    newTrendOption.series = [
      {
        name: $sldComLanguage('新增会员数'),
        data: memberLists,
        type: 'line',
      },
      {
        name: $sldComLanguage('新增店铺数'),
        data: storeLists,
        type: 'line',
      },
    ];
    newTrendOption.maxMount = Math.max.apply(null, [...memberLists, ...storeLists]);
    return newTrendOption;
  };
  //会员/店铺新增趋势end-----------------------------------

  //地域分布start------------------------------
  const {
    register: regionDistribution_register,
    execApi: region_execApi,
    setApiUrl: region_setApiUrl,
  } = useStatCharts();
  const regionTabValue = ref(1);
  const regionDistributionChange = () => {
    region_setApiUrl(regionTabValue.value == 2 ? API.GET_STORE_REGION : API.GET_MEMBER_REGION);
    region_execApi();
  };
  const regionDataHandler = (return_data) => {
    regionOption.series = return_data;
    regionOption.seriesName = regionTabValue.value == 2 ? $sldComLanguage('店铺总数') : $sldComLanguage('会员总数');
    regionOption.dataName = 'provinceName';
    regionOption.dataValue = regionTabValue.value == 2 ? 'storeNum' : 'memberNum';
    return regionOption;
  };
  //地域分布end------------------------------
  // 店铺销售排行start---------------------------------------
  const storeRankOption = ref([
    {
      title: $sldComLanguage('店铺名称'),
      dataIndex: 'storeName',
      customRenderType: 'textRender',
    },
    {
      title: $sldComLanguage('销售额'),
      dataIndex: 'orderPayAmount',
      sorter: true,
      sortDirections: ['descend'],
      customRenderType: 'priceRender',
    },
    {
      title: $sldComLanguage('订单量'),
      dataIndex: 'orderPayNum',
      sorter: true,
      sortDirections: ['descend'],
    },
  ]);

  const storeRank_paramHandler = (dateParam, sortParam) => {
    let target = {
      ...dateParam,
    };
    if (sortParam && sortParam.order) {
      target.sort = sortParam.field == 'orderPayAmount' ? 1 : 2;
    }
    return target;
  };
  const { register: storeRank_register } = useStatRank({
    apiUrl: API.GET_STORE_RANK,
    paramHandler: storeRank_paramHandler,
  });
  // 店铺销售排行end---------------------------------------
  // 商品销售排行start-------------------------------------
  const goodsRankOption = [
    {
      title: $sldComLanguage('商品名称'),
      dataIndex: 'goodsName',
      customRenderType: 'goodsRender',
    },
    {
      title: $sldComLanguage('销售额'),
      dataIndex: 'saleAmount',
      sorter: true,
      sortDirections: ['descend'],
      customRenderType: 'priceRender',
    },
    {
      title: $sldComLanguage('销量'),
      dataIndex: 'saleNum',
      sorter: true,
      sortDirections: ['descend'],
    },
  ];
  const goodsRankParamHandler = (dateParam, sortParam) => {
    let target = {
      ...dateParam,
    };
    if (sortParam && sortParam.order) {
      target.sort = sortParam.field == 'saleAmount' ? 1 : 2;
    }
    return target;
  };
  const { register: goodsRank_register } = useStatRank({
    apiUrl: API.GET_GOODS_RANK,
    paramHandler: goodsRankParamHandler,
  });
  // 商品销售排行end---------------------------------------
  //品类销售排行start-------------------------------------
  const cateRankOption = [
    {
      title: $sldComLanguage('一级品类'),
      dataIndex: 'categoryName',
    },
    {
      title: $sldComLanguage('销售额'),
      dataIndex: 'saleAmount',
      sorter: true,
      sortDirections: ['descend'],
      customRenderType: 'priceRender',
    },
    {
      title: $sldComLanguage('销量'),
      dataIndex: 'saleNum',
      sorter: true,
      sortDirections: ['descend'],
    },
  ];

  const cateRank_paramHandler = (dateParam, sortParam) => {
    let target = {
      ...dateParam,
    };
    if (sortParam && sortParam.order) {
      target.sort = sortParam.field == 'saleAmount' ? 1 : 2;
    }
    return target;
  };
  const { register: cateRank_register } = useStatRank({
    apiUrl: API.GET_CATE_RANK,
    paramHandler: cateRank_paramHandler,
  });
  //品类销售排行end---------------------------------------

  //品牌销售占比start-----------------------------------------
  const { register: brandSale_register, execDataHandler: brandSale_execData } = useStatCharts();
  const brandSaleTabValue = ref(1);
  const brandSale_paramHandler = (dateParam) => ({
    ...dateParam,
    sort: brandSaleTabValue.value,
  });
  const brandSaleDataHandler = (return_data) => {
    brandSaleOption.series = return_data[
      brandSaleTabValue.value == 1 ? 'saleAmountList' : 'saleNumList'
    ].map((item) => ({
      value: brandSaleTabValue.value == 1 ? item.saleAmount : item.saleNum,
      name: item.brandName,
    }));
    return brandSaleOption;
  };
  const brandSaleTabChange = () => {
    brandSale_execData();
  };
  //品牌销售占比end-----------------------------------------

  const goPath = (item) => {
    router.push(item.path);
  };

  onMounted(() => {
    getTodayStat();
    handleWaitDeal();
  });
</script>

<style scoped lang="less">
  .position-title {
    position: relative;
    padding-left: 8px;
    border-left: 4px solid @primary-color;
    color: #3a3e43;
    font-size: 14px;
    font-weight: 700;
    line-height: 18px;
    white-space: nowrap;
  }

  .today_info_panel {
    position: relative;
    height: 301px;
    margin-right: 10px;
    padding: 10px;
    background-color: #fff;

    &:last-child {
      margin-right: 0;
    }

    .update_time {
      color: @primary-color;
      font-size: 13px;
    }

    .position-title1 {
      position: absolute;
      z-index: 2;
      top: 8px;
      left: -5px;
      width: 163px;
      height: 37px;
      padding-top: 11px;
      padding-left: 16px;
      background-size: 100%;
      color: #fff;
      font-size: 15px;
      line-height: 25px;
    }
  }

  .basic-goods {
    width: 20%;
    height: 130px;
    margin-top: 20px;
    margin-right: 16px;
    margin-bottom: 19px;
    background-color: #fff;
    box-shadow: 0 2px 22px 0 rgb(0 0 0 / 3%);
    cursor: pointer;

    .left_pending_icon {
      margin-top: 28px;
      margin-left: 7%;
    }

    .item_desc {
      margin-top: 34px;
      margin-left: 15px;
    }
  }

  .basic-item_num {
    margin-top: 9px;
    color: #333;
    font-size: 26px;
    font-weight: 700;
    line-height: 26px;
  }

  .basic-item_title {
    opacity: 0.72;
    color: #333;
    font-size: 15px;
  }
</style>
