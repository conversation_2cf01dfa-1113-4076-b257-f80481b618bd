<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <a-spin :spinning="spinning">
        <SldComHeader
          :title="'邮件配置'"
        />
        <div class="site_info_scroll">
          <StandardTableRow
            width="100%"
            :data="emailData"
            @callback-event="callbackEvent"
            @submit-event="submitEvent"
            @btn-click-event="btnClickEvent"
          />
        </div>
      </a-spin>
    </div>
  </div>
</template>
<script>
  import { defineComponent, ref, onMounted } from 'vue';
  import { Spin, Modal } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import StandardTableRow from '/@/components/StandardTableRow/index.vue';
  import { getEmailSetting, testEmail, saveBasicSiteSetting } from '/@/api/sysset/sysset';
  import { SldForbiddenEditUrl } from '/@/enums/baseEnum';
  import { sldCheckEmail } from '/@/utils/validate';
  import { failTip, sucTip } from '/@/utils/utils';

  export default defineComponent({
    components: {
      [Spin.name]: Spin,
      SldComHeader,
      StandardTableRow,
    },
    setup() {
      const spinning = ref(false);
      const emailData = ref([]);
      const clickEvent = ref(false); //事件防抖动

      //获取短信配置
      const get_sms_setting = async () => {
        spinning.value = true;
        const res = await getEmailSetting();
        if (res.state == 200 && res.data) {
          let data = [];
          res.data.map((item) => {
            data.push({
              type: 'input',
              label: item.title,
              key: item.name,
              placeholder: '请输入' + item.title,
              width: 300,
              height: item.name == 'notification_email_smtp_host' ? 88 : 72,
              lineHeight: item.name == 'notification_email_smtp_host' ? 88 : 72,
              right_width: 350,
              initValue: '',
              value: item.value,
              inputType: 'text',
              callback: true,
              desc: item.description,
              desc_width: 350,
            });
          });
          let temp = data.filter((item) => item.key == 'notification_email_test_address');
          if (data.length > 0) {
            data.push({
              type: 'button',
              width: 300,
              showSubmit: true,
              submitText: '保存',
              showCancle: false,
              btnList:
                temp.length > 0
                  ? [
                      {
                        btnText: '发送测试邮件',
                        type: 'sendTest',
                        callback: true,
                      },
                    ]
                  : [],
            });
          }
          emailData.value = data;
        } else {
          failTip(res.msg);
        }
        spinning.value = false;
      };

      function callbackEvent(item) {
        let temp = emailData.value.filter((items) => items.key == item.contentItem.key);
        if (temp.length > 0) {
          temp[0].value = item.val1;
        }
      }

      function submitEvent(val) {
        if (SldForbiddenEditUrl && window.location.href.indexOf(SldForbiddenEditUrl) != -1) {
          Modal.warning({
            title: '提示',
            content:
              '演示站配置数据（邮件配置）不支持更改，如需更改，请联系商务',
            okText: '确定',
          });
        } else {
          if (clickEvent.value) return;
          clickEvent.value = true;
          save_base_site(val);
        }
      }

      function btnClickEvent() {
        let temp = emailData.value.filter((item) => item.key == 'notification_email_test_address');
        if (temp.length > 0) {
          if (!temp[0].value) {
            failTip('请输入测试邮件');
          } else {
            //正则验证邮箱
            if (sldCheckEmail(temp[0].value)) {
              if (clickEvent.value) return;
              clickEvent.value = true;
              send_test_email({ toAddress: temp[0].value });
            } else {
              failTip('请输入正确的邮箱');
            }
          }
        }
      }

      const send_test_email = async (params) => {
        const res = await testEmail(params);
        clickEvent.value = false;
        if (res.state == 200) {
          sucTip(res.msg);
        } else {
          failTip(res.msg);
        }
      };

      //保存站点基本配置
      const save_base_site = async (params) => {
        const res = await saveBasicSiteSetting(params);
        clickEvent.value = false;
        if (res.state == 200) {
          sucTip(res.msg);
        } else {
          failTip(res.msg);
        }
      };

      onMounted(() => {
        get_sms_setting();
      });

      return {
        spinning,
        emailData,
        clickEvent,
        get_sms_setting,
        callbackEvent,
        submitEvent,
        btnClickEvent,
        send_test_email,
        save_base_site,
      };
    },
  });
</script>
<style lang="less" scoped></style>
