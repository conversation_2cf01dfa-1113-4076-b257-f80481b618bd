<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <SldComHeader
        :title="'图片配置'"
      />
      <a-tabs v-model:activeKey="tabIndex" type="card" @change="handleChange">
        <a-tab-pane key="1" tab="基本图片" />
        <a-tab-pane key="2" tab="默认图片" />
      </a-tabs>
      <div class="section_padding_tab_top"></div>
      <template v-if="tabIndex == '1'">
        <div class="app_set_scroll">
          <StandardTableRow
            width="100%"
            :data="baseData"
            @callback-event="callbackEvent"
            @submit-event="submitEvent"
          />
        </div>
      </template>
      <template v-else>
        <div class="app_set_scroll">
          <StandardTableRow
            width="100%"
            :data="defaultData"
            @callback-event="callbackEvent"
            @submit-event="submitEvent"
          />
        </div>
      </template>
    </div>
  </div>
</template>
<script>
  import { defineComponent, ref, onMounted } from 'vue';
  import { Tabs } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import StandardTableRow from '/@/components/StandardTableRow/index.vue';
  import { getBaseImageSet, getdefaultImageSet, saveBasicSiteSetting } from '/@/api/sysset/sysset';
  import { useGlobSetting } from '/@/hooks/setting';
  import { failTip, sucTip, setDocumentIcon } from '/@/utils/utils';

  export default defineComponent({
    components: {
      [Tabs.name]: Tabs,
      [Tabs.TabPane.name]: Tabs.TabPane,
      SldComHeader,
      StandardTableRow,
    },
    setup() {
      const { apiUrl } = useGlobSetting();
      const tabIndex = ref('1'); //tab下标
      const baseData = ref([]); //基本图片
      const defaultData = ref([]); //默认图片

      //tab切换
      function handleChange(e) {
        if (e == 1) {
          get_base_img_set();
        } else {
          get_default_img_set();
        }
      }

      //获取基本图片数据
      const get_base_img_set = async () => {
        const res = await getBaseImageSet();
        if (res.state == 200 && res.data) {
          let data = [];
          res.data.map((item, index) => {
            let obj = {
              width: 300,
              type: 'upload_img',
              label: item.title,
              key: item.name,
              accept: '.jpg, .jpeg, .png, .gif',
              action: `${apiUrl}/v3/oss/admin/upload?source=setting`,
              fileList: [],
              desc: item.description,
              desc_width: 350,
              callback: true,
            };
            if (item.name == 'main_site_logo') {
              obj.height = 165;
            }
            if (item.imageUrl) {
              obj.fileList.push({
                uid: 'base_img' + index,
                name: 'base_img' + index,
                status: 'done',
                path: item.value,
                url: item.imageUrl,
              });
            }
            data.push(obj);
          });
          if (data.length > 0) {
            data.push({
              type: 'button',
              width: 300,
              showSubmit: true,
              submitText: '保存',
              showCancle: false,
            });
          }
          baseData.value = data;
        } else {
          failTip(res.msg);
        }
      };

      //获取默认图片数据
      const get_default_img_set = async () => {
        const res = await getdefaultImageSet();
        if (res.state == 200 && res.data) {
          let data = [];
          res.data.map((item, index) => {
            let obj = {
              width: 300,
              type: 'upload_img',
              label: item.title,
              key: item.name,
              accept: '.jpg, .jpeg, .png, .gif',
              action: `${apiUrl}/v3/oss/admin/upload?source=setting`,
              fileList: [],
              desc: item.description,
              desc_width: 350,
              callback: true,
            };
            if (item.imageUrl) {
              obj.fileList.push({
                uid: 'base_img' + index,
                name: 'base_img' + index,
                status: 'done',
                path: item.value,
                url: item.imageUrl,
              });
            }
            data.push(obj);
          });
          if (data.length > 0) {
            data.push({
              type: 'button',
              width: 300,
              showSubmit: true,
              submitText: '保存',
              showCancle: false,
            });
          }
          defaultData.value = data;
        } else {
          failTip(res.msg);
        }
      };

      onMounted(() => {
        get_base_img_set();
      });

      const callbackEvent = (item) => {
        if (item.contentItem.eventType == 'change') {
          let temp = [];
          if (tabIndex.value == '1') {
            temp = baseData.value.filter((items) => items.key == item.contentItem.key);
          } else {
            temp = defaultData.value.filter((items) => items.key == item.contentItem.key);
          }
          if (temp.length > 0) {
            temp[0].fileList = item.val1.fileList;
            setTimeout(() => {
              if (item.val1.fileList.length > 0 && item.val1.fileList[0].response && item.val1.fileList[0].response.state == 255) {
                failTip(item.val1.fileList[0].response.msg);
                temp[0].fileList = [];
              }
            }, 50)
          }
        }
      };

      const submitEvent = (data) => {
        let params = {};
        let background_browser_icon_url;
        for (let key in data) {
          if (key == 'background_browser_icon' && data[key] && data[key].response) {
            background_browser_icon_url = data[key].response.data.url;
          }
          params[key] =
            data[key].response && data[key].response.data && data[key].response.data.path
              ? data[key].response.data.path
              : data[key].path
              ? data[key].path
              : '';
        }
        submitUpdateSetting(params, background_browser_icon_url);
      };

      //保存提交
      const submitUpdateSetting = async (params, background_browser_icon_url) => {
        const res = await saveBasicSiteSetting(params);
        if (res.state == 200) {
          sucTip(res.msg);
          if (tabIndex.value == '1') {
            setDocumentIcon(background_browser_icon_url);
          }
        } else {
          failTip(res.msg);
        }
      };

      return {
        tabIndex,
        baseData,
        defaultData,
        handleChange,
        get_base_img_set,
        get_default_img_set,
        callbackEvent,
        submitEvent,
        submitUpdateSetting,
      };
    },
  });
</script>
<style lang="less" scoped></style>
