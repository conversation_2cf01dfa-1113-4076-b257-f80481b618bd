<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <SldComHeader
        :title="'运营配置'"
      />
      <a-tabs v-model:activeKey="tabIndex" type="card" @change="handleTab">
        <a-tab-pane key="1" tab="订单导出配置" />
        
        
        <a-tab-pane key="2" tab="退款配置" />
      </a-tabs>
      <div class="section_padding_tab_top"></div>
      <template v-if="tabIndex == '1'">
        <div class="app_set_scroll">
          <StandardTableRow
            width="100%"
            style="padding: 0;"
            :data="exportData"
            @callback-event="callbackEvent"
            @submit-event="submitEvent"
          />
        </div>
      </template>
      <template v-else-if="tabIndex == '2'">
        <div class="app_set_scroll">
          <StandardTableRow
            width="100%"
            :data="returnData"
            style="padding: 0;"
            @callback-event="callbackEvent"
            @submit-event="submitEvent"
          />
        </div>
      </template>
      
      
    </div>
  </div>
</template>
<script>
  import { defineComponent, ref, onMounted } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import StandardTableRow from '/@/components/StandardTableRow/index.vue';
  import { Tabs } from 'ant-design-vue';
  import { getOrderOutputCode, getSettingList, saveBasicSiteSetting } from '/@/api/sysset/sysset';
  import { failTip, sucTip } from '/@/utils/utils';

  export default defineComponent({
    components: {
      SldComHeader,
      [Tabs.name]: Tabs,
      [Tabs.TabPane.name]: Tabs.TabPane,
      StandardTableRow,
    },
    setup() {
      const tabIndex = ref('1'); //tab下标
      const exportData = ref([]); //订单导出配置列表
      const outputCode = ref([]); //订单导出配置字段
      const returnData = ref([]); //退款配置列表
      
      
      const clickEvent = ref(false);

      function handleTab(e) {
        if (e == '1') {
          get_output_code();
        }else if (e == '2'){
          get_setting_list({ str: 'refund_setting_switch' });
        }
        
        
      }

      //获取订单导出字段
      const get_output_code = async () => {
        const res = await getOrderOutputCode();
        if (res.state == 200 && res.data) {
          outputCode.value = [];
          for (let key in res.data) {
            outputCode.value.push({
              key: key,
              value: key,
              title: res.data[key],
            });
          }
          get_setting_list({ str: 'order_list_code' });
        } else {
          failTip(res.msg);
        }
      };

      //获取配置信息
      const get_setting_list = async (params) => {
        const res = await getSettingList(params);
        if (res.state == 200 && res.data) {
          let data = [];
          
          res.data.map(item => {
            if (item.type == 1) {
              
                data.push({
                  type: 'checkbox',
                  label: item.title,
                  key: item.name,
                  width: 300,
                  right_width: 600,
                  checkbox_width: 150,
                  initValue: '',
                  value: item.value.split(','),
                  callback: true,
                  data: outputCode.value,
                });
              
            } else if (item.type == 4) {
              
              
              if (tabIndex.value == '1' || tabIndex.value == '2') {
                data.push({
                  type: 'radio',
                  radio_type: 'button',
                  label: item.title,
                  key: item.name,
                  width: 300,
                  initValue: '',
                  value: item.value == '1' ? '1' : '0',
                  callback: true,
                  data: [
                    { key: 0, value: '1', title: '退回余额' },
                    { key: 1, value: '0', title: '原路退回' },
                  ],
                  desc: item.description,
                });
              }
            }
            
          });
          if (data.length > 0) {
            data.push({
              type: 'button',
              width: 300,
              showSubmit: true,
              submitText: '保存',
              showCancle: false,
            });
          }
          if (tabIndex.value == '1') {
            exportData.value = data;
          } else if (tabIndex.value == '2') {
            returnData.value = data;
          }
          
          
        } else {
          failTip(res.msg);
        }
      };

      const callbackEvent = (item) => {
        let temp;
        if (tabIndex.value == '1') {
          temp = exportData.value.filter((items) => items.key == item.contentItem.key);
        }
        
        else {
          temp = returnData.value.filter((items) => items.key == item.contentItem.key);
        }
        if (temp && temp.length > 0) {
          temp[0].value = item.val1;
        }
      };

      const submitEvent = (val) => {
        let params = {};
        if (tabIndex.value == '1') {
          let array_info = []
          for (let key in val) {
            if(key == 'order_list_code'){
              if(outputCode.value.length>0){
                outputCode.value.forEach(item=>{
                  if(val[key].indexOf(item.key)!=-1){
                    array_info.push(item.key)
                  }
                })
              }
              params[key] = array_info.join(',');
            }else{
              params[key] = val[key].join(',');
            }
          }
        }
        
        else {
          params = val;
        }
        save_base_site(params);
      };

      const errEvent = () => {
        document.getElementsByClassName('app_set_scroll')[0].scrollTo({ top: 100, behavior: 'smooth' });
      };

      //保存站点基本配置
      const save_base_site = async (params) => {
        if (clickEvent.value) return;
        clickEvent.value = true;
        const res = await saveBasicSiteSetting(params);
        if (res.state == 200) {
          sucTip(res.msg);
        } else {
          failTip(res.msg);
        }
        clickEvent.value = false;
      };

      onMounted(() => {
        get_output_code();
      });

      return {
        tabIndex,
        exportData,
        outputCode,
        returnData,
        handleTab,
        callbackEvent,
        submitEvent,
        get_output_code,
        get_setting_list,
        save_base_site,
        
        
      };
    },
  });
</script>
<style lang="less" scoped></style>
