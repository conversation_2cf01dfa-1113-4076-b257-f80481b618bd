<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <SldComHeader :type="1" :title="'管理员管理'" />
      <BasicTable @register="standardTable" style="padding: 0">
        <template #tableTitle>
          <div class="toolbar">
            <div class="toolbar_btn" @click="handleClick(null, 'add')">
              <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#fff" />
              <span>新增管理员</span>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.key == 'action'">
            <template v-if="record.isSuper == 1"> -- </template>
            <template v-else>
              <span class="common_page_edit" @click="handleClick(record, 'edit')">编辑</span>
              <span class="common_page_edit" @click="handleClick(record, 'freeze')">{{
                record.state == 1 ? '冻结' : '解冻'
              }}</span>
              <span class="common_page_edit" @click="handleClick(record, 'reset')">重置密码</span>
              <Popconfirm
                title="删除后不可恢复，是否确定删除？"
                @confirm="handleClick(record, 'del')"
              >
                <span class="common_page_edit">删除</span>
              </Popconfirm>
            </template>
          </template>
          <template v-else-if="column.key">
            {{ text ? text : '--' }}
          </template>
        </template>
      </BasicTable>
      <SldModal
        :width="width"
        :title="title"
        :visible="visible"
        :content="content"
        :confirmBtnLoading="confirmBtnLoading"
        :showFoot="true"
        @cancle-event="handleCancle"
        @confirm-event="handleConfirm"
      />
    </div>
  </div>
</template>
<script>
  import { defineComponent, ref, onMounted } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { Popconfirm } from 'ant-design-vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import {
    getAdminUserList,
    getRoleManageList,
    addAdminUser,
    updateAdminUser,
    freezeAdminUser,
    resetAdminUser,
    delAdminUser,
  } from '/@/api/sysset/sysset';
  import SldModal from '@/components/SldModal/index.vue';
  import { validatorVendorEmail, mobile_reg } from '/@/utils/validate';
  import base64Encrypt from '/@/utils/base64Encrypt';
  import { failTip, sucTip } from '/@/utils/utils';

  export default defineComponent({
    components: {
      SldComHeader,
      BasicTable,
      Popconfirm,
      SldModal,
    },
    setup() {
      const [standardTable, { reload }] = useTable({
        api: (arg) => getAdminUserList({ ...arg }),
        fetchSetting: {
          pageField: 'current',
          sizeField: 'pageSize',
          listField: 'data.list',
          totalField: 'data.pagination.total',
        },
        columns: [
          {
            title: '账号',
            dataIndex: 'adminName',
            width: 100,
          },
          {
            title: '所属权限组',
            dataIndex: 'roleName',
            width: 100,
          },
          {
            title: '电话',
            dataIndex: 'phone',
            width: 100,
          },
          {
            title: '创建时间',
            dataIndex: 'createTime',
            width: 100,
          },
          {
            title: '状态',
            dataIndex: 'stateValue',
            width: 100,
          },
        ],
        actionColumn: {
          width: 120,
          title: '操作',
          dataIndex: 'action',
        },
        useSearchForm: true,
        formConfig: {
          schemas: [
            {
              field: 'adminName',
              component: 'Input',
              colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                maxlength: 10,
                placeholder: '请输入账号',
                size: 'default',
              },
              label: '账号',
              labelWidth: 40,
            },
          ],
        },
        bordered: true,
        striped: false,
        showIndexColumn: true,
      });
      const width = ref(550);
      const title = ref('');
      const visible = ref(false);
      const content = ref([]);
      const confirmBtnLoading = ref(false);
      const operate_id = ref('');
      const operate_type = ref('');

      const operate_data = ref([
        {
          type: 'input',
          label: `账号`,
          name: 'adminName',
          placeholder: `请输入账号`,
          extra: '最多输入10个字',
          maxLength: 10,
          rules: [
            {
              required: true,
              whitespace: true,
              message: `请输入账号`,
            },
          ],
        },
        {
          type: 'input',
          input_type: 'password',
          label: `登陆密码`,
          name: 'password',
          placeholder: `请设置6-20位的登陆密码`,
          maxLength: 20,
          rules: [
            {
              required: true,
              whitespace: true,
              message: `请设置登陆密码`,
            },
            {
              min: 6,
              max: 20,
              message: `请输入6-20位的密码`,
            },
          ],
        },
        {
          type: 'input',
          input_type: 'password',
          label: `确认密码`,
          name: 'confirmPwd',
          placeholder: `确认密码需要与登录密码一致`,
          maxLength: 20,
          rules: [
            {
              required: true,
              whitespace: true,
              message: `请输入确认密码`,
            },
            {
              min: 6,
              max: 20,
              message: `请输入6-20位的确认密码`,
            },
          ],
        },
        {
          type: 'input',
          label: `联系人电话`,
          name: 'phone',
          placeholder: `请输入联系人电话`,
          maxLength: 11,
          rules: [
            {
              required: true,
              whitespace: true,
              message: `请输入联系人电话`,
            },
            {
              pattern: mobile_reg,
              message: `请输入正确的手机号`,
            },
          ],
        },
        {
          type: 'input',
          label: `邮箱`,
          name: 'email',
          placeholder: `请输入邮箱`,
          rules: [
            {
              required: true,
              whitespace: true,
              message: `请输入邮箱`,
            },
            {
              validator: (rule, value, callback) => validatorVendorEmail(rule, value, callback),
            },
          ],
        },
        {
          type: 'select',
          label: `权限组`,
          name: 'roleId',
          placeholder: `请选择权限组`,
          selData: [],
          selKey: 'roleId',
          selName: 'roleName',
          diy: true,
          rules: [
            {
              required: true,
              message: `请选择权限组`,
            },
          ],
          width: 366,
          wrapperCol: { span: 16 },
        },
      ]); //新增、编辑、重置密码弹窗数据

      const operate_auth = ref([]); //授权弹窗数据

      //表格点击回调事件
      function handleClick(item, type) {
        operate_type.value = type;
        if (item) {
          operate_id.value = item.adminId;
        }
        if (type == 'add') {
          title.value = '添加管理员';
          visible.value = true;
          content.value = JSON.parse(JSON.stringify(operate_data.value));
          let email_temp = content.value.filter((items) => items.name == 'email');
          if (email_temp.length > 0) {
            email_temp[0].rules = [
              {
                required: true,
                whitespace: true,
                message: `请输入邮箱`,
              },
              {
                validator: (rule, value, callback) => validatorVendorEmail(rule, value, callback),
              },
            ];
            email_temp[3].rules = [
              {
                required: true,
                whitespace: true,
                message: `请输入联系人电话`,
              },
              {
                pattern: mobile_reg,
                message: `请输入正确的手机号`,
              }
            ];
          }
        } else if (type == 'edit') {
          title.value = '编辑管理员';
          visible.value = true;
          let data = JSON.parse(JSON.stringify(operate_data.value));
          data = data.filter((item) => item.name != 'password' && item.name != 'confirmPwd');
          data.map((items) => {
            if (items.name == 'phone') {
              items.rules = [
                {
                  required: true,
                  whitespace: true,
                  message: `请输入联系人电话`,
                },
                {
                  pattern: mobile_reg,
                  message: `请输入正确的手机号`,
                }
              ];
            }
            items.initialValue = item[items.name] ? item[items.name] : undefined;
          });
          content.value = data;
        } else if (type == 'freeze') {
          operate_role({
            adminId: item.adminId,
            isFreeze: item.state == 1 ? true : false,
          });
        } else if (type == 'reset') {
          title.value = '重置密码';
          visible.value = true;
          let data = JSON.parse(JSON.stringify(operate_data.value));
          data = data.filter((item) => item.name == 'password' || item.name == 'confirmPwd');
          content.value = data;
        } else if (type == 'del') {
          operate_role({ adminId: item.adminId });
        }
      }

      //弹窗取消事件
      function handleCancle() {
        visible.value = false;
        content.value = [];
        operate_id.value = '';
      }

      //弹窗确认事件
      function handleConfirm(val) {
        if (val.password && val.confirmPwd && val.password != val.confirmPwd) {
          failTip('两次密码不一致，请重新输入');
          return;
        }
        if (operate_type.value == 'add') {
          val.password = base64Encrypt(val.password);
          val.confirmPwd = base64Encrypt(val.confirmPwd);
        } else if (operate_type.value == 'edit') {
          val.adminId = operate_id.value;
        } else if (operate_type.value == 'reset') {
          val.adminId = operate_id.value;
          val.newPassword = base64Encrypt(val.password);
          val.newPasswordCfm = base64Encrypt(val.confirmPwd);
          delete val.password;
          delete val.confirmPwd;
        }
        operate_role(val);
      }

      //权限组操作
      const operate_role = async (params) => {
        confirmBtnLoading.value = true;
        let res = {};
        if (operate_type.value == 'add') {
          res = await addAdminUser(params);
        } else if (operate_type.value == 'edit') {
          res = await updateAdminUser(params);
        } else if (operate_type.value == 'freeze') {
          res = await freezeAdminUser(params);
        } else if (operate_type.value == 'reset') {
          res = await resetAdminUser(params);
        } else if (operate_type.value == 'del') {
          res = await delAdminUser(params);
        }
        confirmBtnLoading.value = false;
        if (res.state == 200) {
          handleCancle();
          sucTip(res.msg);
          reload();
        } else {
          failTip(res.msg);
        }
      };

      onMounted(() => {
        get_role_list();
      });

      //获取权限组列表数据
      const get_role_list = async () => {
        const res = await getRoleManageList({ pageSize: 10000 });
        if (res.state == 200) {
          let temp = operate_data.value.filter((item) => item.name == 'roleId');
          if (temp.length > 0) {
            temp[0].selData = res.data.list;
          }
        } else {
          failTip(res.msg);
        }
      };

      return {
        standardTable,
        width,
        title,
        visible,
        content,
        confirmBtnLoading,
        operate_id,
        operate_type,
        operate_data,
        operate_auth,
        handleClick,
        handleCancle,
        handleConfirm,
        get_role_list,
      };
    },
  });
</script>
<style lang="less" scoped></style>
