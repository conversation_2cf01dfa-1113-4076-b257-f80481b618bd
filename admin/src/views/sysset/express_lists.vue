<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader :type="1" :title="'物流管理'" />
      <BasicTable @register="standardTable" style="padding: 0;">
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.key == 'action'">
            <template v-if="record.isSuper == 1"> -- </template>
            <template v-else>
              <span class="common_page_edit" @click="handleClick(record, 'edit')">编辑</span>
              <Popconfirm
                title="删除后不可恢复，是否确定删除？"
                @confirm="handleClick(record, 'del')"
              >
                <span class="common_page_edit">删除</span>
              </Popconfirm>
            </template>
          </template>
          <template v-else-if="column.key == 'expressState'">
            <Switch :checked="text == '1' ? true : false" @change="(e) => handleChange(e, record)" />
          </template>
          <template v-else-if="column.key=='sort'">
            {{ text||text==0 ? text : '--' }}
          </template>
          <template v-else-if="column.key">
            {{ text ? text : '--' }}
          </template>
        </template>
      </BasicTable>
      <SldModal
        :width="width"
        :title="title"
        :visible="visible"
        :content="content"
        :confirmBtnLoading="confirmBtnLoading"
        :showFoot="true"
        @cancle-event="handleCancle"
        @confirm-event="handleConfirm"
      />
    </div>
  </div>
</template>
<script>
  import { defineComponent, ref } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { Switch, Popconfirm } from 'ant-design-vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import { getExpressList, updateExpress, delExpress } from '/@/api/sysset/sysset';
  import SldModal from '@/components/SldModal/index.vue';
  import { validatorExpressSort } from '/@/utils/validate';
  import { failTip, sucTip } from '/@/utils/utils';

  export default defineComponent({
    components: {
      SldComHeader,
      BasicTable,
      Switch,
      Popconfirm,
      SldModal,
    },
    setup() {
      const [standardTable, { reload }] = useTable({
        api: (arg) => getExpressList({ ...arg }),
        fetchSetting: {
          pageField: 'current',
          sizeField: 'pageSize',
          listField: 'data.list',
          totalField: 'data.pagination.total',
        },
        columns: [
          {
            title: '物流公司',
            dataIndex: 'expressName',
            width: 100,
          },
          {
            title: '物流代码',
            dataIndex: 'expressCode',
            width: 100,
          },
          {
            title: '网址(仅供参考)',
            dataIndex: 'website',
            width: 100,
          },
          {
            title: '排序',
            dataIndex: 'sort',
            width: 100,
          },
          {
            title: '开启',
            dataIndex: 'expressState',
            width: 100,
          },
        ],
        actionColumn: {
          width: 100,
          title: '操作',
          dataIndex: 'action',
        },
        useSearchForm: true,
        formConfig: {
          schemas: [
            {
              field: 'expressName',
              component: 'Input',
              colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                maxlength: 10,
                placeholder: '请输入物流名称',
                size: 'default',
              },
              label: '物流名称',
              labelWidth: 75,
            },
          ],
        },
        bordered: true,
        striped: false,
        showIndexColumn: true,
      });
      const width = ref(550);
      const title = ref('');
      const visible = ref(false);
      const content = ref([]);
      const confirmBtnLoading = ref(false);
      const operate_id = ref('');
      const operate_type = ref('');

      const operate_data = ref([
        {
          type: 'input',
          label: `物流名称`,
          name: 'expressName',
          placeholder: `请输入物流名称`,
          rules: [
            {
              required: true,
              message: `请输入账号`,
            },
          ],
          disable: true,
        },
        {
          type: 'input',
          label: `物流代码`,
          name: 'expressCode',
          placeholder: `请输入物流代码`,
          disable: true,
        },
        {
          type: 'inputnum',
          label: `排序`,
          name: 'sort',
          placeholder: `请输入排序`,
          extra: '请输入排序，越小越靠前',
          initialValue: '',
          rules: [
            {
              required: true,
              message: `请输入排序`,
            },
            {
              validator: (rule, value) => validatorExpressSort(rule, value),
            },
          ],
        },
        {
          type: 'input',
          label: `物流公司网址`,
          name: 'website',
          placeholder: `请输入物流公司网址`,
          rules: [
            {
              required: true,
              whitespace: true,
              message: `请输入物流公司网址`,
            },
          ],
        },
        {
          type: 'switch',
          label: `启用`,
          name: 'expressState',
          initialValue: '',
        },
      ]); //新增、编辑、重置密码弹窗数据

      //开关事件
      function handleChange(val, item) {
        operate_type.value = 'switch';
        operate_role({
          expressState: val ? 1 : 0,
          expressId: item.expressId,
        });
      }

      //表格点击回调事件
      function handleClick(item, type) {
        operate_type.value = type;
        if (item) {
          operate_id.value = item.expressId;
        }
        if (type == 'edit') {
          title.value = '编辑物流公司';
          visible.value = true;
          let data = JSON.parse(JSON.stringify(operate_data.value));
          let temp = data.filter((items) => items.name == 'sort');
          if (temp.length > 0) {
            temp[0].rules = [
              {
                required: true,
                message: `请输入排序`,
              },
              {
                validator: (rule, value) => validatorExpressSort(rule, value),
              },
            ];
          }
          data.map((items) => {
            if (items.name == 'expressState') {
              items.initialValue = item[items.name] == 1 ? true : false;
            } else {
              items.initialValue = item[items.name]||item[items.name]==0 ? item[items.name] : undefined;
            }
          });
          content.value = data;
        } else if (type == 'del') {
          operate_role({ expressId: item.expressId });
        }
      }

      //弹窗取消事件
      function handleCancle() {
        visible.value = false;
        content.value = [];
        operate_id.value = '';
      }

      //弹窗确认事件
      function handleConfirm(val) {
        if (operate_type.value == 'edit') {
          val.expressId = operate_id.value;
          val.expressState = val.expressState ? 1 : 0;
        }
        operate_role(val);
      }

      //权限组操作
      const operate_role = async (params) => {
        confirmBtnLoading.value = true;
        let res = {};
        if (operate_type.value == 'switch' || operate_type.value == 'edit') {
          res = await updateExpress(params);
        } else if (operate_type.value == 'del') {
          res = await delExpress(params);
        }
        confirmBtnLoading.value = false;
        if (res.state == 200) {
          handleCancle();
          sucTip(res.msg);
          reload();
        } else {
          failTip(res.msg);
        }
      };

      return {
        standardTable,
        width,
        title,
        visible,
        content,
        confirmBtnLoading,
        operate_id,
        operate_type,
        operate_data,
        handleChange,
        handleClick,
        handleCancle,
        handleConfirm,
      };
    },
  });
</script>
<style lang="less" scoped></style>
