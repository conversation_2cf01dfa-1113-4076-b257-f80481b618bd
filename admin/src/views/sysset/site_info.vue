<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <a-spin :spinning="spinning">
        <SldComHeader
          :title="'站点配置'"
          :clickFlag="true"
        />
        <div class="site_info_scroll">
          <StandardTableRow
            width="100%"
            :data="rowData"
            @callback-event="callbackEvent"
            @submit-event="submitEvent"
          />
        </div>
      </a-spin>
    </div>
  </div>
</template>
<script>
  import { defineComponent, ref, onMounted } from 'vue';
  import { Spin, Modal } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import StandardTableRow from '/@/components/StandardTableRow/index.vue';
  import { getBasicSiteSetting, saveBasicSiteSetting } from '/@/api/sysset/sysset';
  import { SldForbiddenEditUrl } from '/@/enums/baseEnum';
  import { failTip, sucTip } from '/@/utils/utils';

  export default defineComponent({
    components: {
      [Spin.name]: Spin,
      SldComHeader,
      StandardTableRow,
    },
    setup() {
      const detail = ref({}); //初始数据
      const spinning = ref(false);
      const rowData = ref([]);
      const clickEvent = ref(false); //事件防抖动

      //获取站点基本配置
      const get_base_site = async () => {
        spinning.value = true;
        const res = await getBasicSiteSetting();
        if (res.state == 200 && res.data) {
          detail.value = {};
          let data = [];
          res.data.map((item) => {
            if (item.type == 1) {
              if (item.name == 'basic_site_tongji') {
                data.push({
                  type: 'textarea',
                  label: item.title,
                  key: item.name,
                  width: 300,
                  placeholder: '请输入' + item.title,
                  initValue: '',
                  value: item.value,
                  maxlength: 100,
                  rows: 2,
                  callback: true,
                  height: 100,
                  desc: item.description,
                });
              } else {
                data.push({
                  type: 'input',
                  label: item.title,
                  key: item.name,
                  placeholder: '请输入' + item.title,
                  width: 300,
                  initValue: '',
                  value: item.value,
                  inputType: 'text',
                  callback: true,
                  desc: item.description,
                  disabled:
                    SldForbiddenEditUrl &&
                    window.location.href.indexOf(SldForbiddenEditUrl) != -1 &&
                    (item.name == 'basic_site_icp' ||
                      item.name == 'basic_site_phone' ||
                      item.name == 'basic_site_email')
                      ? true
                      : false,
                  maxlength: item.name == 'platform_customer_service_name' ? 15 : undefined,
                });
              }
              detail.value[item.name] = item.value;
            } else if (item.type == 4) {
              data.push({
                type: 'switch',
                label: item.title,
                key: item.name,
                width: 300,
                desc_width: 300,
                initValue: '',
                value: item.value == 1 ? true : false,
                callback: true,
                desc: item.description,
              });
              detail.value[item.name] = item.value == 1 ? true : false;
            }
          });
          if (data.length > 0) {
            data.push({
              type: 'button',
              width: 300,
              showSubmit: true,
              submitText: '保存',
              showCancle: false,
            });
          }
          rowData.value = data;
        } else {
          failTip(res.msg);
        }
        spinning.value = false;
      };

      const callbackEvent = (item) => {
        let temp = rowData.value.filter((items) => items.key == item.contentItem.key);
        if (temp.length > 0) {
          temp[0].value = item.val1;
        }
      };

      const submitEvent = (val) => {
        if (
          (SldForbiddenEditUrl && window.location.href.indexOf(SldForbiddenEditUrl) != -1) &&
            ((detail.value.basic_site_icp != val.basic_site_icp) || (detail.value.basic_site_phone != val.basic_site_phone)
              || (detail.value.basic_site_email != val.basic_site_email))
        ) {
          Modal.warning({
            title: '提示',
            content:
              '演示站配置数据（ICP备案号、网站电话、平台邮箱）不支持更改，如需更改，请联系商务',
            okText: '确定',
          });
        } else {
          for (let key in val) {
            if (typeof val[key] == 'boolean') {
              val[key] = val[key] ? '1' : '0';
            }
          }
          save_base_site(val);
        }
      };

      //保存站点基本配置
      const save_base_site = async (params) => {
        if (clickEvent.value) return;
        clickEvent.value = true;
        const res = await saveBasicSiteSetting(params);
        if (res.state == 200) {
          sucTip(res.msg);
        } else {
          failTip(res.msg);
        }
        clickEvent.value = false;
      };

      onMounted(() => {
        get_base_site();
      });

      return {
        spinning,
        rowData,
        clickEvent,
        get_base_site,
        callbackEvent,
        submitEvent,
        save_base_site,
      };
    },
  });
</script>
<style lang="less" scoped></style>
