<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader :title="'平台资源'"/>
      <BasicTable @register="standardTable" style="padding: 0">
        <template #tableTitle>
          <div class="toolbar">
            <div class="toolbar_btn" @click="handleClick(null, 'add')">
               <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="primaryColor" />
              <span>添加一级资源</span>
            </div>
            <div class="toolbar_btn" @click="handleClick(null, 'update_last_sourse')">
              <AliSvgIcon iconName="icongengxin2" width="15px" height="15px" fillColor="#2ec19a" />
              <span>更新四级资源</span>
            </div>
            <div class="toolbar_btn" @click="handleClick(null, 'update_super_admin')">
              <AliSvgIcon iconName="icongengxin2" width="15px" height="15px" fillColor="#2ec19a" />
              <span>更新超级管理员权限</span>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex == 'action'">
            <span
            v-if="record.grade <= 2"
            class="common_page_edit"
            @click="handleClick(record, 'child')"
            >添加下级</span
            >
            <span
            v-if="record.grade == 3"
            class="common_page_edit"
            @click="handleClick(record, 'bind')"
            >绑定接口</span
            >
            <span v-if="record.grade <= 3" class="common_page_edit" @click="handleClick(record, 'edit')">编辑</span>
            <Popconfirm
              v-if="record.grade <= 3"
              title="删除后不可恢复，是否确定删除？"
              @confirm="handleClick(record, 'del')"
            >
              <span class="common_page_edit">删除</span>
            </Popconfirm>

            <Popconfirm
              v-if="record.grade == 4"
              title="解绑后不可恢复，是否确定解绑？"
              @confirm="handleClick(record, 'cancel_bind')"
            >
              <span class="common_page_edit">解绑</span>
            </Popconfirm>
          </template>
          <template v-else-if="column.dataIndex">
            {{ text !== undefined && text !== null ? text : '--' }}
          </template>
        </template>
      </BasicTable>
      <SldModal
        :width="width"
        :title="title"
        :visible="visible"
        :height='500'
        :content="content"
        :confirmBtnLoading="confirmBtnLoading"
        :showFoot="true"
        :parentPage="'cate_lists'"
        @tag-select-event="handleTagSelect"
        @cancle-event="handleCancle"
        @confirm-event="handleConfirm"
      />
    </div>
  </div>
</template>
<script setup>
  import { ref, onMounted } from 'vue';
  import { Popconfirm,} from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import {
    getResourceListApi,
    getResourcePutToDbApi,
    getRoleBindResourceApi,
    getResourceAddApi,
    getResourceDeleteApi,
    getResourceUpdateApi,
    getResourceBindApi,
    getResourceCancelBindApi
  } from '/@/api/sysset/resource';
  import SldModal from '@/components/SldModal/index.vue';
  import { sucTip, failTip } from '/@/utils/utils';

  const dataSource = ref([]);
  const [standardTable] = useTable({
    isTreeTable: true,
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
    },
    ellipsis:false,
    columns: [
      {
        title: '资源名称',
        dataIndex: 'content',
        width: 220,
      },
      {
        title: 'url',
        dataIndex: 'url',
        width: 150,
      },
      {
        title: '前端对应路由',
        dataIndex: 'frontPath',
        width: 150,
      },
      {
        title: '资源等级',
        dataIndex: 'grade',
        width: 80,
      },
      {
        title: '排序',
        dataIndex: 'sort',
        width: 120,
      },
    ],
    actionColumn: {
      width: 170,
      title: '操作',
      dataIndex: 'action',
    },
    dataSource: dataSource,
    bordered: true,
    striped: false,
    rowKey: 'resourceId',
    pagination: false,
  });
  const click_event = ref(false);

  const width = ref(550);
  const title = ref('');
  const visible = ref(false);
  const content = ref([]);
  const confirmBtnLoading = ref(false);
  const operate_type = ref('');
  const operate_item = ref('');
  const operate_data = ref([
    {
      type: 'input',
      label: `资源名称`,
      name: 'content',
      placeholder: `请输入资源名称`,
      extra: `最多输入20个字`,
      maxLength: 20,
      rules: [
        {
          required: true,
          message: `请输入资源名称`,
        },
      ],
    },
    {
      type: 'input',
      label: `前端路由`,
      name: 'frontPath',
      placeholder: `请输入前端路由`,
      extra: `最多输入200个字`,
      maxLength: 200,
      rules: [
        {
          required: true,
          message: `请输入前端路由`,
        },
      ],
    },
    {
      type: 'inputnum',
      label: `排序`,
      name: 'sort',
      placeholder: `请输入排序`,
      min: 0,
    },
  ]);

  //表格点击回调事件 add添加  edit编辑 child添加下级  update_last_sourse更新四级资源  update_super_admin更新超级管理员权限
  function handleClick(item, type) {
    if (click_event.value) return;
    operate_type.value = type;
    operate_item.value = item ? JSON.parse(JSON.stringify(item)) : null;
    let params = {};
    let data = JSON.parse(JSON.stringify(operate_data.value));
    if (type == 'add') {
      title.value = '添加一级资源';
      content.value = data
    } else if (type == 'edit') {
      title.value = '编辑资源';
      data.forEach(it=>{
        it.initialValue = item[it.name]
      })
      content.value = data;
    } else if (type == 'child') {
      operate_item.value.grade = operate_item.value.grade * 1 + 1
      content.value = []
      title.value = '添加下级资源';
      content.value = [
        {
          type: 'input',
          label: `父资源名称`,
          name: 'parent_content',
          disable:true,
          initialValue:item.content,
        },
        ...JSON.parse(JSON.stringify(data))
      ]
      visible.value = true
    } else if (type == 'update_last_sourse') {
      operate_role(null);
      return;
    } else if (type == 'update_super_admin') {
      operate_role(null);
      return;
    } else if (type == 'del') {
      params.resourceId = item.resourceId;
      click_event.value = true;
      operate_role(params);
      return;
    } else if (type == 'cancel_bind') {
      params.resourceId = item.resourceId;
      click_event.value = true;
      operate_role(params);
      return;
    } else if (type == 'bind') {
      content.value = []
      title.value = '绑定四级资源';
      content.value =  [
        {
          type: 'input',
          label: `父资源名称`,
          name: 'parent_content',
          disable:true,
          initialValue:item.content,
        },
        {
          type: 'tag_show_btn_sel',
          label: `四级资源`,
          name: 'resourceIds',
          btnText: `选择资源`,
          extra: ``,
          initialValue: undefined,
          data: [],
          ids: [],
          callback: true,
          diy: true,
          diy_key: 'content',
          tag_type:'sys_resource',
        },
      ]
    } else {
      return;
    }
    width.value = 550;
    visible.value = true;
  }

  //操作
  const operate_role = async (params) => {
    let res = {};
    if (operate_type.value == 'add') {
      params.pid = params.pid !== undefined ? params.pid : 0;
      res = await getResourceAddApi(params);
    } else if (operate_type.value == 'edit') {
      res = await getResourceUpdateApi(params);
    } else if (operate_type.value == 'child') {
      res = await getResourceAddApi(params);
    } else if (operate_type.value == 'del') {
      res = await getResourceDeleteApi(params);
    } else if (operate_type.value == 'update_last_sourse') {
      res = await getResourcePutToDbApi();
    } else if (operate_type.value == 'update_super_admin') {
      res = await getRoleBindResourceApi();
    } else if (operate_type.value == 'bind') {
      res = await getResourceBindApi(params);
    } else if (operate_type.value == 'cancel_bind') {
      res = await getResourceCancelBindApi(params);
    }
    if (res.state == 200) {
      sucTip(res.msg);
      get_list();
      handleCancle();
      click_event.value = false;
    } else {
      click_event.value = false;
      failTip(res.msg);
    }
  };

  //弹窗取消事件
  function handleCancle() {
    visible.value = false;
    content.value = [];
    operate_item.value = '';
  }

  //弹窗确认事件
  function handleConfirm(val) {
    if (click_event.value) return;
    if (operate_type.value == 'add') {
      val.grade = 1;
      val.url = '/';
      val.sort = val.sort||val.sort=='0'?Number(val.sort):'';
    } else if (operate_type.value == 'edit') {
      val.grade = operate_item.value.grade;
      val.pid = operate_item.value.pid;
      val.resourceId = operate_item.value.resourceId;
      val.url = '/';
      val.sort = val.sort||val.sort=='0'?Number(val.sort):'';
    } else if (operate_type.value == 'child') {
      val.grade = operate_item.value.grade;
      val.pid = operate_item.value.resourceId;
      val.url = '/';
      val.sort = val.sort||val.sort=='0'?Number(val.sort):'';
      if(val.parent_content){
        delete val.parent_content
      }
    } else if (operate_type.value == 'bind') {
      if(!val.resourceIds){
        failTip('请选择四级资源～');
        return;
      }
      if(val.parent_content){
        delete val.parent_content
      }
      val.pid = operate_item.value.resourceId;
    }
    click_event.value = true;
    operate_role(val);
  }

  function handleTagSelect(type, item, rows, rowKeys) {
    let temp = content.value.filter((items) => items.name == item.name);
    if (temp.length > 0) {
      if(type == 'add'){
        temp[0].data = rows;
        temp[0].ids = rowKeys;
        temp[0].initialValue = rowKeys.join(',');
      }else{
        temp[0].data = temp[0].data.filter((items) => items.resourceId != rows.resourceId);
        temp[0].ids = temp[0].ids.filter((items) => items != rows.resourceId);
        temp[0].initialValue = temp[0].ids.join(',');
      }
    }
  }


  //获取分类列表数据
  const get_list = async () => {
    const res = await getResourceListApi({});
    if (res.state == 200) {
      dataSource.value = res.data;
    } else {
      failTip(res.msg);
    }
  };


  onMounted(() => {
    get_list();
  });

</script>
<style lang="less" scoped></style>
