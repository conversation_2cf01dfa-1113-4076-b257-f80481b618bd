<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <SldComHeader :type="1" :title="'消息模板管理'" />
      <Tabs v-model:activeKey="tabIndex" type="card">
        <TabPane key="1" tab="会员消息模板" />
        <TabPane key="2" tab="商户消息模板" />
      </Tabs>
      <div class="section_padding_tab_top"></div>
      <template v-if="tabIndex == '1'">
        <BasicTable @register="memberStandardTable">
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.key == 'msgSwitch'">
              <Switch
                :checked="text == '1' ? true : false"
                @change="(e) => handleChange(e, record, 'msgSwitch')"
              />
            </template>
            <template v-else-if="column.key == 'emailSwitch'">
              <Switch
                :checked="text == '1' ? true : false"
                @change="(e) => handleChange(e, record, 'emailSwitch')"
              />
            </template>
            <template v-else-if="column.key == 'smsSwitch'">
              <Switch
                :checked="text == '1' ? true : false"
                @change="(e) => handleChange(e, record, 'smsSwitch')"
              />
            </template>
            <template v-else-if="column.key == 'wxSwitch'">
              <Switch
                :checked="text == '1' ? true : false"
                @change="(e) => handleChange(e, record, 'wxSwitch')"
              />
            </template>
            <template v-else-if="column.key == 'action'">
              <span class="common_page_edit" @click="handleClick(record, 'smg')">站内信</span>
              <span class="common_page_edit" @click="handleClick(record, 'email')">邮件</span>
              <span class="common_page_edit" @click="handleClick(record, 'sms')">短信</span>
              <span class="common_page_edit" @click="handleClick(record, 'wx')">微信</span>
            </template>
          </template>
        </BasicTable>
      </template>
      <template v-else>
        <BasicTable @register="sellerStandardTable">
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.key == 'msgSwitch'">
              <Switch
                :checked="text == '1' ? true : false"
                @change="(e) => handleChange(e, record, 'msgSwitch')"
              />
            </template>
            <template v-else-if="column.key == 'emailSwitch'">
              <Switch
                :checked="text == '1' ? true : false"
                @change="(e) => handleChange(e, record, 'emailSwitch')"
              />
            </template>
            <template v-else-if="column.key == 'smsSwitch'">
              <Switch
                :checked="text == '1' ? true : false"
                @change="(e) => handleChange(e, record, 'smsSwitch')"
              />
            </template>
            <template v-else-if="column.key == 'action'">
              <span class="common_page_edit" @click="handleClick(record, 'smg')">站内信</span>
              <span class="common_page_edit" @click="handleClick(record, 'email')">邮件</span>
              <span class="common_page_edit" @click="handleClick(record, 'sms')">短信</span>
            </template>
          </template>
        </BasicTable>
      </template>
      <SldModal
        :width="width"
        title="编辑消息模板"
        :visible="visible"
        :content="content"
        :confirmBtnLoading="confirmBtnLoading"
        :showFoot="true"
        @cancle-event="handleCancle"
        @confirm-event="handleConfirm"
      />
    </div>
  </div>
</template>
<script>
  export default {
    name: 'msgTpl',
  };
</script>
<script setup>
  import { ref, onMounted } from 'vue';
  import { Tabs, TabPane, Switch } from 'ant-design-vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import SldModal from '@/components/SldModal/index.vue';
  import { getSmsSetting, getMemberTpList, getStoreTpList, updateMemberTpList, updateStoreTpList } from '/@/api/sysset/sysset';
  import { failTip, sucTip } from '/@/utils/utils';

  const sms_type = ref('1'); //短信平台 1:腾讯云短信 2:云片短信
  const tabIndex = ref('1'); //tab下标
  const width = ref(600); //弹窗宽度
  const visible = ref(false); //弹窗是否显示
  const content = ref([]); //弹窗数据
  const confirmBtnLoading = ref(false); //弹窗确认按钮
  const operate_item = ref(''); //当前操作对象
  const operate_type = ref('');

  const [memberStandardTable, { reload: reloadPc }] = useTable({
    api: () => getMemberTpList(),
    fetchSetting: {
      listField: 'data',
    },
    columns: [
      {
        title: '模板描述',
        dataIndex: 'tplName',
        width: 100,
      },
      {
        title: '站内信',
        dataIndex: 'msgSwitch',
        width: 100,
      },
      {
        title: '邮件',
        dataIndex: 'emailSwitch',
        width: 100,
      },
      {
        title: '短信',
        dataIndex: 'smsSwitch',
        width: 100,
      },
      {
        title: '微信',
        dataIndex: 'wxSwitch',
        width: 100,
      },
    ],
    actionColumn: {
      width: 180,
      title: '操作',
      dataIndex: 'action',
    },
    bordered: true,
    striped: false,
    pagination: false,
    showIndexColumn: true,
  });

  const [sellerStandardTable, { reload: reloadMobile }] = useTable({
    api: () => getStoreTpList(),
    fetchSetting: {
      listField: 'data',
    },
    columns: [
      {
        title: '模板描述',
        dataIndex: 'tplName',
        width: 100,
      },
      {
        title: '站内信',
        dataIndex: 'msgSwitch',
        width: 100,
      },
      {
        title: '邮件',
        dataIndex: 'emailSwitch',
        width: 100,
      },
      {
        title: '短信',
        dataIndex: 'smsSwitch',
        width: 100,
      },
    ],
    actionColumn: {
      width: 180,
      title: '操作',
      dataIndex: 'action',
    },
    bordered: true,
    striped: false,
    pagination: false,
    showIndexColumn: true,
  });

  onMounted(() => {
    get_sms_info();
  })

  //获取短信平台类型
  const get_sms_info = async () => {
    const res = await getSmsSetting();
    if (res.state == 200) {
      for (let i in res.data) {
        if (res.data[i].name == 'send_sms_type') {
          sms_type.value = res.data[i].value !== undefined ? res.data[i].value : '2';
          break;
        }
      }
    }
  };

  function handleChange(e, item, key) {
    let params = {};
    params.tplCode = item.tplCode;
    params[key] = e ? 1 : 0;
    submitUpdateSetting(params, true);
  }

  //编辑事件
  function handleClick(item, type) {
    operate_item.value = item;
    operate_type.value = type;
    let data = [
      {
        type: 'switch',
        label: `是否开启`,
        initialValue: '',
      },
    ];
    if (type == 'smg') {
      width.value = 600;
      data[0].name ='msgSwitch';
      data[0].initialValue = item.msgSwitch == 1 ? true : false;
      data.push({
        type: 'textarea',
        label: `模板内容`,
        name: type == 'smg' ? 'msgContent' : 'smsContent',
        placeholder: `请输入模板内容`,
        extra: '最多输入100个字',
        maxLength: 100,
        showCount: true,
        initialValue: item.msgContent ? item.msgContent : '',
        rules: [{ required: true, whitespace: true, message: `请输入模板内容` }],
      });
    } else if (type == 'sms') {
      width.value = 600;
      data[0].name = 'smsSwitch';
      data[0].initialValue = item.smsSwitch == 1 ? true : false;
      if (sms_type.value == '1') {
        data.push({
          type: 'input',
          label: `短信模版ID`,
          name: 'templateId',
          placeholder: `请输入短信模版ID`,
          initialValue: (item.smsContent && JSON.parse(item.smsContent) && JSON.parse(item.smsContent).templateId)
            ? JSON.parse(item.smsContent).templateId : '',
          rules: [{ required: true, whitespace: true, message: `请输入短信模版ID` }],
        });
        data.push({
          disable: true,
          type: 'textarea',
          label: `模板内容`,
          name: 'templateContent',
          placeholder: `请输入模板内容`,
          extra: '最多输入100个字',
          maxLength: 100,
          showCount: true,
          initialValue:
            (item.smsContent && JSON.parse(item.smsContent) && JSON.parse(item.smsContent).templateContent)
              ? JSON.parse(item.smsContent).templateContent : '',
          rules: [{ required: true, whitespace: true, message: `请输入模板内容` }],
        });
      } else {
        data.push({
          type: 'textarea',
          label: `模板内容`,
          name: 'yunPianContent',
          placeholder: `请输入模板内容`,
          extra: '最多输入100个字',
          maxLength: 100,
          showCount: true,
          initialValue:
            (item.smsContent && JSON.parse(item.smsContent) && JSON.parse(item.smsContent).yunPianContent)
              ? JSON.parse(item.smsContent).yunPianContent : '',
          rules: [{ required: true, whitespace: true, message: `请输入模板内容` }],
        });
      }
    } else if (type == 'email') {
      width.value = 800;
      data[0].name = 'emailSwitch';
      data[0].initialValue = item.emailSwitch == 1 ? true : false;
      data.push({
        type: 'input',
        label: `邮件标题`,
        name: 'email_subject',
        placeholder: `请输入邮件标题`,
        initialValue: item.emailContent ? JSON.parse(item.emailContent).email_subject : '',
        rules: [{ required: true, whitespace: true, message: `请输入邮件标题` }],
      });
      data.push({
        type: 'quill',
        label: '邮件内容',
        name: 'email_content',
        placeholder: ``,
        initialValue: item.emailContent && JSON.parse(item.emailContent).email_content ? JSON.parse(item.emailContent).email_content : '',
        handleGetContent: (val) => handleGetContent(val),
      });
    } else if (type == 'wx') {
      width.value = 600;
      data[0].name = 'wxSwitch';
      data[0].initialValue = item.wxSwitch == 1 ? true : false;
      data.push({
        type: 'input',
        label: `微信模板ID`,
        name: 'templateId',
        placeholder: `请输入微信模板ID`,
        initialValue: item.wxContent ? JSON.parse(item.wxContent).templateId : '',
        rules: [{ required: true, whitespace: true, message: `请输入微信模板ID` }],
      });
      data.push({
        type: 'textarea',
        label: `模板内容`,
        name: 'templateContent',
        placeholder: `请输入模板内容`,
        extra: '最多输入100个字',
        maxLength: 100,
        showCount: true,
        initialValue: item.wxContent ? JSON.parse(item.wxContent).templateContent : '',
        rules: [
          {
            required: true,
            whitespace: true,
            message: `请输入模板内容`,
          },
        ],
      });
    }
    content.value = data;
    visible.value = true;
    confirmBtnLoading.value = false;
  }

  //弹窗取消事件
  function handleCancle() {
    operate_item.value = '';
    operate_type.value = '';
    visible.value = false;
  }

  //弹窗确认事件
  function handleConfirm(params) {
    params.tplCode = operate_item.value.tplCode;
    if (operate_type.value == 'smg') {
      params.msgSwitch = params.msgSwitch ? 1 : 0;
    } else if (operate_type.value == 'sms') {
      params.smsSwitch = params.smsSwitch ? 1 : 0;
      let sms_data = {};
      sms_data.templateId = sms_type.value == '1' ? params.templateId
        : (operate_item.value.smsContent && JSON.parse(operate_item.value.smsContent) && JSON.parse(operate_item.value.smsContent).templateId)
            ? JSON.parse(operate_item.value.smsContent).templateId : '',
      sms_data.templateContent = sms_type.value == '1' ? params.templateContent
        : (operate_item.value.smsContent && JSON.parse(operate_item.value.smsContent) && JSON.parse(operate_item.value.smsContent).templateContent)
            ? JSON.parse(operate_item.value.smsContent).templateContent : '',
      sms_data.yunPianContent = sms_type.value == '2' ? params.yunPianContent
        : (operate_item.value.smsContent && JSON.parse(operate_item.value.smsContent) && JSON.parse(operate_item.value.smsContent).yunPianContent)
            ? JSON.parse(operate_item.value.smsContent).yunPianContent : '',
      delete params.templateId;
      delete params.templateContent;
      delete params.yunPianContent;
      params.smsContent = JSON.stringify(sms_data);
    } else if (operate_type.value == 'email') {
      params.emailSwitch = params.emailSwitch ? 1 : 0;
      let emailContent = {};
      emailContent.email_subject = params.email_subject;
      delete params.email_subject;
      emailContent.email_content = params.email_content;
      delete params.email_content;
      params.emailContent = JSON.stringify(emailContent);
    } else if (operate_type.value == 'wx') {
      params.wxSwitch = params.wxSwitch ? 1 : 0;
      let wxContent = {};
      wxContent.templateId = params.templateId;
      delete params.templateId;
      wxContent.templateContent = params.templateContent;
      delete params.templateContent;
      params.wxContent = JSON.stringify(wxContent);
    }
    confirmBtnLoading.value = true;
    submitUpdateSetting(params, true);
  }

  //slodon_获取富文本返回的内容
  function handleGetContent(value) {
    let tmp_data = content.value.filter(item => item.name == 'email_content')[0];
    tmp_data.initialValue = value;
  };

  //保存提交
  const submitUpdateSetting = async (params, reload) => {
    let res = null;
    if (tabIndex.value == '1') {
      res = await updateMemberTpList(params);
    } else {
      res = await updateStoreTpList(params);
    }
    confirmBtnLoading.value = false;
    if (res.state == 200) {
      sucTip(res.msg);
      handleCancle();
      if (reload) {
        if (tabIndex.value == '1') {
          reloadPc();
        } else {
          reloadMobile();
        }
      }
    } else {
      failTip(res.msg);
    }
  };

</script>
<style lang="less" scoped></style>
