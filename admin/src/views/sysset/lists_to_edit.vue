<template>
  <PageWrapper>
    <div>
      <div class="common_page_title">基本信息</div>
      <BasicForm @register="register" style="margin-top: 15px" />
      <div class="common_page_title">内容编辑</div>
      <div style="margin-top: 20px">
        <SldUEditor
          v-if="editorFlag"
          :id="'agreement'"
          :initEditorContent="initEditorContent"
          :getContentFlag="getEditorContentFlag"
          :getEditorContent="getEditorContent"
        />
      </div>
    </div>
    <div style="width: 100%; height: 50px"></div>
    <div class="common_page_btn" :style="{ left: getRealWidth + 'px' }">
      <div class="common_page_btn_back" @click="handleBack">返回</div>
      <div class="common_page_btn_save" @click="handleConfirm">保存并返回</div>
    </div>
  </PageWrapper>
</template>
<script>
  import { defineComponent, ref, onMounted } from 'vue';
  import { Input } from 'ant-design-vue';
  import { useRoute, useRouter } from 'vue-router';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { failTip, quillEscapeToHtml, sucTip } from '/@/utils/utils';
  import { getAgreementDetail, updateAgreement } from '/@/api/sysset/sysset';
  import SldUEditor from '/@/components/SldUEditor/index.vue';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  const { getRealWidth } = useMenuSetting();
  import { pageClose } from '/@/utils/utils';
  import { useMultipleTabStore } from '/@/store/modules/multipleTab';

  export default defineComponent({
    components: {
      BasicForm,
      Input,
      SldUEditor,
    },
    setup() {
      const route = useRoute();
      const tabStore = useMultipleTabStore();
      const router = useRouter();
      const editorFlag = ref(false);
      const [register, { getFieldsValue, setFieldsValue }] = useForm({
        labelWidth: 90,
        baseRowStyle: {
          display: 'block',
        },
        schemas: [
          {
            field: 'title',
            component: 'Input',
            label: '协议标题',
            componentProps: {
              maxLength: 20,
            },
            colProps: {
              span: 8,
            },
            rules: [
              {
                required: true,
                whitespace: true,
                message: '请输入协议标题',
              },
            ],
          },
        ],
        showActionButtonGroup: false,
      });

      const initEditorContent = ref(''); //百度编辑器内容
      const getEditorContentFlag = ref(false); //获取百度编辑器内容标识

      onMounted(() => {
        if (route.query && route.query.agreementCode) {
          editorFlag.value = false;
          get_agreement_detail({ agreementCode: route.query.agreementCode });
        }
      });

      //获取协议详情
      const get_agreement_detail = async (params) => {
        const res = await getAgreementDetail(params);
        if (res.state == 200) {
          initEditorContent.value = quillEscapeToHtml(res.data.content);
          editorFlag.value = true;
          setFieldsValue({
            title: res.data.title,
          });
        } else {
          failTip(res.msg);
        }
      };

      //获取编辑器内容
      function getEditorContent(con) {
        getEditorContentFlag.value = false;
        update_agreement({
          title: getFieldsValue().title,
          content: con,
          agreementCode: route.query.agreementCode,
        });
      }

      function handleBack() {
        if (window.history && window.history.length == 1) {
          pageClose();
        } else {
          const { fullPath } = route;
          tabStore.closeTabByKey(fullPath, router);
          router.back();
        }
      }

      function handleConfirm() {
        if (!getFieldsValue().title || !getFieldsValue().title.trim()) {
          return;
        }
        getEditorContentFlag.value = true;
      }

      //更新协议数据
      const update_agreement = async (params) => {
        const res = await updateAgreement(params);
        if (res.state == 200) {
          sucTip(res.msg);
          setTimeout(() => {
            handleBack();
          }, 1500);
        } else {
          failTip(res.msg);
        }
      };

      return {
        editorFlag,
        register,
        getRealWidth,
        get_agreement_detail,
        initEditorContent,
        getEditorContentFlag,
        getEditorContent,
        handleBack,
        handleConfirm,
        update_agreement,
      };
    },
  });
</script>
<style lang="less" scoped>
  .common_page {
    height: 100%;

    .common_page_title {
      display: flex;
      position: relative;
      align-items: center;
      justify-content: flex-start;
      height: 22px;
      padding-left: 10px;
      color: #101010;
      font-size: 14px;
      font-weight: 700;
      line-height: 22px;

      &::before {
        content: '';
        display: inline-block;
        position: absolute;
        top: 50%;
        left: 0;
        width: 4px;
        height: 14px;
        margin-top: -6px;
        background: #fa6f1e;
      }
    }
  }
</style>
