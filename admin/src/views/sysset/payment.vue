<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader
        :title="'支付配置'"
      />
      <a-tabs v-model:activeKey="tabIndex" type="card">
        <a-tab-pane :key="item.key" :tab="item.value" v-for="(item,index) in tab_info" />
      </a-tabs>
      <div class="section_padding_tab_top"></div>
      <!-- dev_pc-start -->
      <template v-if="tabIndex == '1'">
        <BasicTable @register="pcStandardTable" style="padding: 0;" >
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.key == 'value'">
              <Switch
                :checked="text == '1' ? true : false"
                @change="(e) => handleChange(e, record)"
              />
            </template>
            <template v-else-if="column.key == 'action'">
              <span
                v-if="record.name != 'balance_pay_is_enable_pc'"
                class="common_page_edit"
                @click="handleEdit(record)"
                >编辑</span
              >
              <span v-else class="common_page_edit">--</span>
            </template>
          </template>
        </BasicTable>
      </template>
      <!-- dev_pc-end -->
      <!-- dev_mobile-start -->
      <template v-if="tabIndex == '2'">
        <BasicTable @register="mobileStandardTable"  style="padding: 0;">
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.key == 'value'">
              <Switch
                :checked="text == '1' ? true : false"
                @change="(e) => handleChange(e, record)"
              />
            </template>
            <template v-else-if="column.key == 'action'">
              <span
                v-if="record.name != 'balance_pay_is_enable_h5'"
                class="common_page_edit"
                @click="handleEdit(record)"
                >编辑</span
              >
              <span v-else class="common_page_edit">--</span>
            </template>
          </template>
        </BasicTable>
      </template>
      <!-- dev_mobile-end -->
      <SldModal
        :width="600"
        :title="title"
        :visible="visible"
        :content="content"
        :confirmBtnLoading="confirmBtnLoading"
        :showFoot="true"
        @cancle-event="handleCancle"
        @confirm-event="handleConfirm"
      />
    </div>
  </div>
</template>
<script>
  import { defineComponent, ref,onMounted } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { Tabs, Switch, Modal } from 'ant-design-vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import {
    getPcSetting,
    getMobileSetting,
    saveBasicSiteSetting,
    getPaySetting,
  } from '/@/api/sysset/sysset';
  import { SldForbiddenEditUrl } from '/@/enums/baseEnum';
  import SldModal from '@/components/SldModal/index.vue';
  import { failTip, sucTip } from '/@/utils/utils';

  export default defineComponent({
    components: {
      SldComHeader,
      [Tabs.name]: Tabs,
      [Tabs.TabPane.name]: Tabs.TabPane,
      BasicTable,
      Switch,
      SldModal,
    },
    setup() {
      const tabIndex = ref('-1'); //tab下标
      const title = ref(''); //弹窗标题
      const visible = ref(false); //弹窗是否显示
      const content = ref([]); //弹窗数据
      const confirmBtnLoading = ref(false); //弹窗确认按钮

      const tab_info = ref([
        // dev_pc-start
        {
          key:'1',
          value:'PC支付',
        },
        // dev_pc-end
        // dev_mobile-start
        {
          key:'2',
          value:'移动端支付',
        }
        // dev_mobile-end
      ])
      
      // dev_pc-start
      const [pcStandardTable, { reload: reloadPc,redoHeight:redoHeightPc }] = useTable({
        api: () => getPcSetting(),
        fetchSetting: {
          listField: 'data',
        },
        columns: [
          {
            title: '支付方式',
            dataIndex: 'payment',
            width: 100,
          },
          {
            title: '启用状态',
            dataIndex: 'value',
            width: 100,
          },
        ],
        actionColumn: {
          width: 100,
          title: '操作',
          dataIndex: 'action',
        },
        bordered: true,
        striped: false,
        pagination: false,
        showIndexColumn: true,
      });
      // dev_pc-end

      // dev_mobile-start
      const [mobileStandardTable, { reload: reloadMobile ,redoHeight:redoHeightMobile}] = useTable({
        api: () => getMobileSetting(),
        fetchSetting: {
          listField: 'data',
        },
        columns: [
          {
            title: '支付方式',
            dataIndex: 'payment',
            width: 100,
          },
          {
            title: '启用状态',
            dataIndex: 'value',
            width: 100,
          },
        ],
        actionColumn: {
          width: 100,
          title: '操作',
          dataIndex: 'action',
        },
        bordered: true,
        striped: false,
        pagination: false,
        showIndexColumn: true,
      });
      // dev_mobile-end

      function handleChange(e, item) {
        if (SldForbiddenEditUrl && window.location.href.indexOf(SldForbiddenEditUrl) != -1) {
          Modal.warning({
            title: '提示',
            content:
              '演示站配置数据（支付设置）不支持更改，如需更改，请联系商务',
            okText: '确定',
          });
        } else {
          submitUpdateSetting({ [item.name]: e ? 1 : 0 }, true);
        }
      }

      //编辑事件
      function handleEdit(item) {
        get_pay_setting(item);
      }

      //获取配置信息
      const get_pay_setting = async (item) => {
        const res = await getPaySetting({ name: item.name });
        if (res.state == 200 && res.data) {
          title.value = '编辑' + item.payment + '信息';
          visible.value = true;
          let content_data = [];
          res.data.map((items) => {
            let obj = {
              type: 'input',
              label: items.title,
              name: items.name,
              extra: items.description,
              placeholder: '请输入' + items.title,
              initialValue: items.value,
              rules: [
                {
                  required: true,
                  whitespace: true,
                  message: '请输入' + items.title,
                },
              ],
            };
            content_data.push(obj);
          });
          content.value = content_data;
        } else {
          failTip(res.msg);
        }
      };

      //弹窗取消事件
      function handleCancle() {
        title.value = '';
        visible.value = false;
      }

      //弹窗确认事件
      function handleConfirm(params) {
        if (SldForbiddenEditUrl && window.location.href.indexOf(SldForbiddenEditUrl) != -1) {
          Modal.warning({
            title: '提示',
            content:
              '演示站配置数据（支付设置）不支持更改，如需更改，请联系商务',
            okText: '确定',
          });
        } else {
          confirmBtnLoading.value = true;
          submitUpdateSetting(params, false);
        }
      }

      //保存提交
      const submitUpdateSetting = async (params, reload) => {
        const res = await saveBasicSiteSetting(params);
        confirmBtnLoading.value = false;
        if (res.state == 200) {
          sucTip(res.msg);
          handleCancle();
          if (reload) {
            // dev_pc-start
            if (tabIndex.value == '1') {
              reloadPc();
            } 
            // dev_pc-end
            // dev_mobile-start
            if (tabIndex.value == '2') {
              reloadMobile();
            }
            // dev_mobile-end
          }
        } else {
          failTip(res.msg);
        }
      };

      onMounted(() => {
        if(tab_info.value&&tab_info.value.length>0){
          tabIndex.value = tab_info.value[0].key
        }
      });

      return {
        tabIndex,
        // dev_pc-start
        pcStandardTable,
        // dev_pc-end
        // dev_mobile-start
        mobileStandardTable,
        // dev_mobile-end
        handleChange,
        title,
        visible,
        content,
        confirmBtnLoading,
        handleEdit,
        get_pay_setting,
        handleCancle,
        handleConfirm,
        submitUpdateSetting,
        tab_info
      };
    },
  });
</script>
<style lang="less" scoped>
  .common_page {
    .common_page_edit {
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover {
        color: #ff6a12;
      }
    }
  }
</style>
