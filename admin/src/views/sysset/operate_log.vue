<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <SldComHeader :type="1" :title="'操作行为'" />
      <BasicTable @register="standardTable" style="padding: 0">
        <template #tableTitle>
          <div class="toolbar">
            <template v-if="checkedKeys.length == 0">
              <div class="toolbar_btn" @click="handleClick(checkedKeys, 'dels')">
                <AliSvgIcon iconName="iconxinzeng1" width="15px" height="15px" fillColor="#ff0f3c" />
                <span>批量删除</span>
              </div>
            </template>
            <template v-else>
              <Popconfirm
                title="删除后不可恢复，是否确定删除？"
                @confirm="handleClick(checkedKeys, 'dels')"
              >
                <div class="toolbar_btn">
                  <AliSvgIcon
                    iconName="iconxinzeng1"
                    width="15px"
                    height="15px"
                    fillColor="#ff0f3c"
                  />
                  <span>批量删除</span>
                </div>
              </Popconfirm>
            </template>
            <div class="toolbar_btn" @click="handleClick(null, 'export')">
              <AliSvgIcon iconName="iconziyuan52" width="15px" height="15px" fillColor="#2ea9ff" />
              <span>操作日志导出</span>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.key == 'action'">
            <Popconfirm title="删除后不可恢复，是否确定删除？" @confirm="handleClick(record, 'del')">
              <span class="common_page_edit">删除</span>
            </Popconfirm>
          </template>
          <template v-else-if="column.key">
            {{ text ? text : '--' }}
          </template>
        </template>
      </BasicTable>
    </div>
  </div>
</template>
<script>
  import { defineComponent, ref } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { Popconfirm } from 'ant-design-vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import { getAdminLogList, delAdminLog, exportAdminLog } from '/@/api/sysset/sysset';
  import { failTip, sucTip } from '/@/utils/utils';

  export default defineComponent({
    components: {
      SldComHeader,
      BasicTable,
      Popconfirm,
    },
    setup() {
      const checkedKeys = ref([]);
      const [standardTable, { reload, getForm, getPaginationRef }] = useTable({
        api: (arg) => getAdminLogList({ ...arg }),
        fetchSetting: {
          pageField: 'current',
          sizeField: 'pageSize',
          listField: 'data.list',
          totalField: 'data.pagination.total',
        },
        columns: [
          {
            title: '操作人',
            dataIndex: 'adminName',
            width: 100,
          },
          {
            title: '操作行为',
            dataIndex: 'logContent',
            width: 150,
          },
          {
            title: '操作时间',
            dataIndex: 'logTime',
            width: 100,
          },
          {
            title: 'IP',
            dataIndex: 'logIp',
            width: 100,
          },
        ],
        actionColumn: {
          width: 80,
          title: '操作',
          dataIndex: 'action',
        },
        useSearchForm: true,
        formConfig: {
          schemas: [
            {
              field: 'adminName',
              component: 'Input',
              colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                maxlength: 10,
                placeholder: '请输入操作人',
                size: 'default',
              },
              label: '操作人',
              labelWidth: 50,
            },
            {
              field: 'logContent',
              component: 'Input',
              colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                placeholder: '请输入操作行为',
                size: 'default',
              },
              label: '操作行为',
              labelWidth: 75,
            },
            {
              field: '[startTime, endTime]',
              label: '操作时间',
              component: 'RangePicker',
              colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                format: 'YYYY-MM-DD',
                placeholder: ['开始日期', '结束日期'],
                size: 'default',
              },
              labelWidth: 75,
            },
          ],
        },
        bordered: true,
        striped: false,
        showIndexColumn: true,
        beforeFetch(values) {
          values.startTime = values.startTime
            ? values.startTime.split(' ')[0] + ' 00:00:00'
            : undefined;
          values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
          return values;
        },
        rowKey: 'logId',
        rowSelection: {
          type: 'checkbox',
          selectedRowKeys: checkedKeys,
          onSelect: onSelect,
          onSelectAll: onSelectAll,
        },
        clickToRowSelect: false,
      });
      const operate_type = ref('');

      //操作事件
      function handleClick(item, type) {
        operate_type.value = type;
        if (type == 'del') {
          operateEvent({ logIds: item.logId });
        } else if (type == 'dels') {
          if (checkedKeys.value.length == 0) {
            failTip('请先选中数据');
            return;
          }
          operateEvent({ logIds: checkedKeys.value.join(',') });
        } else if (type == 'export') {
          let params = {};
          if (checkedKeys.value.length > 0) {
            params.logIds = checkedKeys.value.join(',');
          }else{
            params.logIds = ''
          }
          params.fileName = '操作日志导出';
          operateEvent(params);
        }
      }

      function onSelect(record, selected) {
        let data = [];
        if (selected) {
          data = [...checkedKeys.value, record.logId];
        } else {
          data = checkedKeys.value.filter((logId) => logId !== record.logId);
        }
        checkedKeys.value = data;
      }

      function onSelectAll(selected, selectedRows, changeRows) {
        let data = [];
        const changeIds = changeRows.map((item) => item.logId);
        if (selected) {
          data = [...checkedKeys.value, ...changeIds];
        } else {
          data = checkedKeys.value.filter((logId) => {
            return !changeIds.includes(logId);
          });
        }
        checkedKeys.value = data;
      }

      //事件操作
      const operateEvent = async (params) => {
        let res;
        if (operate_type.value == 'del' || operate_type.value == 'dels') {
          res = await delAdminLog(params);
        } else if (operate_type.value == 'export') {
          res = await exportAdminLog(params);
        }
        if (res.state == 200) {
          if (operate_type.value == 'del' || operate_type.value == 'dels') {
            operate_type.value = '';
            checkedKeys.value = [];
            sucTip(res.msg);
            reload();
          }
        } else {
          if (operate_type.value == 'del' || operate_type.value == 'dels') {
            failTip(res.msg);
          }
        }
      };

      return {
        standardTable,
        operate_type,
        handleClick,
        checkedKeys,
        onSelect,
        onSelectAll,
        operateEvent,
      };
    },
  });
</script>
<style lang="less" scoped></style>
