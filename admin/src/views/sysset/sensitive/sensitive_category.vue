<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <SldComHeader 
      title="分类管理"
      />
      <div class="seckill_label_lists">
        <BasicTable @register="standardTable" style="padding: 0">
          <template #tableTitle>
            <div class="toolbar">
              <div class="toolbar_btn" @click="handleClick(null, 'add', null)">
                <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#fff" />
                <span>添加分类</span>
              </div>
            </div>
          </template>
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.dataIndex == 'state'">
              <div>
                <Switch
                checked-children="启用"
                un-checked-children="停用"
                  @change="(checked) => handleClick(record, 'switch',  checked ? 1 : 0)"
                  :checked="text == 1 ? true : false"
                />
              </div>
            </template>
            <template v-if="column.key == 'action'">
              <template v-if="record.isInner == 1"> -- </template>
              <template v-else>
                <TableAction
                  :actions="[
                    {
                      label: '编辑',
                      onClick: handleClick.bind(null, record, 'edit'),
                    },
                    {
                      label: '删除',
                      popConfirm: {
                        title: '是否确认删除该分类。删除后分类下绑定的敏感词数据也将被删除，请谨慎操作',
                        placement: 'left',
                        confirm: handleClick.bind(null, record.categoryId, 'del'),
                      },
                    },
                  ]"
                />
              </template>
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <SldModal
      :width="width"
      :title="title"
      :visible="visible"
      :content="content"
      :confirmBtnLoading="confirmBtnLoading"
      :showFoot="true"
      @cancle-event="handleCancle"
      @confirm-event="handleConfirm"
    />
  </div>
</template>
<script>
  export default {
    name: 'SensitiveCategory',
  };
</script>
<script setup>
  import { ref, onMounted } from 'vue';
  import { Switch} from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import SldModal from '@/components/SldModal/index.vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import {
    getSensitiveWordCategoryListApi,
    getSensitiveWordCategoryEditStateApi,
    getSensitiveWordCategoryDelApi,
    getSensitiveWordCategoryAddApi,
    getSensitiveWordCategoryEditApi,
  } from '/@/api/sysset/sensitive';
  import { failTip, sucTip} from '/@/utils/utils';
  import {  validatorSpecialString } from '/@/utils/validate';

  const [standardTable, { reload}] = useTable({
    api: (arg) => getSensitiveWordCategoryListApi({ ...arg }),
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    columns: [
      {
        title: '分类',
        dataIndex: 'categoryName',
        width: 200,
      },
      {
        title: '添加时间',
        dataIndex: 'createTime',
        width: 200,
      },
      {
        title: '启用状态',
        dataIndex: 'state',
        width: 150,
      },
    ],
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
    useSearchForm: true,
    formConfig: {
      schemas: [
        {
          field: 'categoryName',
          component: 'Input',
          componentProps: {
            placeholder: '请输入分类',
          },
          label: '分类',
        },
      ],
    },
    bordered: true,
    striped: false,
    showIndexColumn: true,
    rowKey:'categoryId',
  });
  const width = ref(500);
  const title = ref('');
  const visible = ref(false);
  const content = ref([]);
  const confirmBtnLoading = ref(false);
  const operate_id = ref('');
  const operate_type = ref('');

  const categoryId = ref('');

  const operate_add_edit = ref([
    {
      type: 'input',
      label: `分类名称`,
      name: 'categoryName',
      placeholder: `请输入分类名称`,
      extra: '最多输入10位',
      maxLength: 10,
      rules: [
        {
          required: true,
          whitespace: true,
          message: '请输入分类名称',
        },
        {
          validator: async (rule, value) => {
            await validatorSpecialString(rule, value);
          },
        },
      ],
    },
    {
      type: 'switch',
      label: `是否启用`, //开关
      name: 'state',
      initialValue: 1, //默认值
      unCheckedValue: 0, //非选中的值 不传返回值为false
      checkedValue: 1, //选中的值 不传返回值为true
      rules: [
        {
          required: true,
        },
      ],
    },
  ]); //添加、编辑商品标签弹窗数据

  //表格点击回调事件
  function handleClick(item, type, check) {
    operate_type.value = type;
    if (item) {
      operate_id.value = item.categoryId;
    }
    if (type == 'add' || type == 'edit') {
      content.value.forEach((it) => {
        it.initialValue = '';
      });
      content.value = [];
      operate_add_edit.value.forEach((it) => {
        if(it.name == 'state'){
          it.initialValue = 1;
        }else{
          it.initialValue = '';

        }
      });
    }
    if (type == 'add') {
      content.value = [];
      title.value = '添加分类';
      visible.value = true;
      content.value = operate_add_edit.value;
    } else if (type == 'edit') {
      title.value = '编辑分类';
      visible.value = true;
      let data = operate_add_edit.value;
      data.map((items) => {
        items.initialValue = item[items.name]!=null ? item[items.name] : undefined;
      });
      content.value = data;
      categoryId.value = item.categoryId;
    } else if (type == 'del') {
      operate_role({ categoryIds: item });
    } else if (type == 'switch') {
      operate_role({ categoryId: item.categoryId, state: check });
    }
  }

  //弹窗取消事件
  function handleCancle() {
    visible.value = false;
    content.value = [];
    categoryId.value = '';
  }

  //弹窗确认事件
  function handleConfirm(val) {
    if (operate_type.value == 'edit') {
      val.categoryId = categoryId.value;
    }
    operate_role(val);
  }

  //商品标签操作
  const operate_role = async (params) => {
    confirmBtnLoading.value = true;
    let res= {};
    if (operate_type.value == 'add') {
      res = await getSensitiveWordCategoryAddApi(params);
    } else if (operate_type.value == 'edit') {
      res = await getSensitiveWordCategoryEditApi(params);
    } else if (operate_type.value == 'del') {
      res = await getSensitiveWordCategoryDelApi(params);
    } else if (operate_type.value == 'switch') {
      res = await getSensitiveWordCategoryEditStateApi(params);
    }
    confirmBtnLoading.value = false;
    if (res.state == 200) {
      sucTip(res.msg);
      handleCancle();
      reload();
    } else {
      failTip(res.msg);
    }
  };


  onMounted(() => {
  });
</script>
<style lang="less">
  
</style>
