<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <SldComHeader 
      title="敏感词词库"
      :clickFlag="true" 
      :tipData="['当敏感词数据有更新时，需点击 `更新缓存` 按钮才能生效',]"
      type="2"
      style="margin-bottom:10px;"
      @handle-toggle-tip="handleToggleTip"
      />
      <div class="seckill_label_lists">
        <BasicTable @register="standardTable" style="padding: 0">
          <template #tableTitle>
            <div class="toolbar">
              <div class="toolbar_btn" @click="handleClick(null, 'add', null)">
                <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#fff" />
                <span>添加敏感词</span>
              </div>
              <div class="toolbar_btn" @click="handleClick(null, 'double', null)">
                <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#fff" />
                <span>批量添加</span>
              </div>
              <Popconfirm title="删除后不可恢复，是否确定删除？" @confirm="handleClick(selectedRowKeys.join(','), 'del',2)"
                :disabled="selectedRowKeys.length == 0">
                <div class="toolbar_btn"
                    @click="() => selectedRowKeys.length == 0 ? handleClick(null, 'del',2) : null">
                    <AliSvgIcon iconName="iconshanchu" width="15px" height="15px" fillColor="#ff0f3c" />
                    <span>批量删除</span>
                </div>
            </Popconfirm>
              <div class="toolbar_btn" @click="operateSen">
                <AliSvgIcon iconName="iconshuaxin1" width="15px" height="15px" fillColor="rgb(255, 89, 8)" />
                <span>更新缓存</span>
              </div>
            </div>
          </template>
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.dataIndex == 'state'">
              <div>
                <Switch
                checked-children="启用"
                un-checked-children="停用"
                  @change="(checked) => handleClick(record, 'switch',  checked ? 1 : 0)"
                  :checked="text == 1 ? true : false"
                />
              </div>
            </template>
            <template v-if="column.key == 'action'">
              <template v-if="record.isInner == 1"> -- </template>
              <template v-else>
                <TableAction
                  :actions="[
                    {
                      label: '编辑',
                      onClick: handleClick.bind(null, record, 'edit'),
                    },
                    {
                      label: '删除',
                      popConfirm: {
                        title: '删除后不可恢复，是否确定删除？',
                        placement: 'left',
                        confirm: handleClick.bind(null, record.wordId, 'del'),
                      },
                    },
                  ]"
                />
              </template>
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <SldModal
      :width="width"
      :title="title"
      :visible="visible"
      :content="content"
      :confirmBtnLoading="confirmBtnLoading"
      :showFoot="true"
      @cancle-event="handleCancle"
      @confirm-event="handleConfirm"
    />
  </div>
</template>
<script>
  export default {
    name: 'SensitiveList',
  };
</script>
<script setup>
  import { ref, onMounted,unref } from 'vue';
  import { useRouter } from 'vue-router';
  import { Switch,Popconfirm} from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import SldModal from '@/components/SldModal/index.vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import {
    getSensitiveWordListApi,
    getSensitiveWordCategoryListApi,
    getSensitiveWordEditStateApi,
    getSensitiveWordDelApi,
    getSensitiveWordAddApi,
    getSensitiveWordEditApi,
    getSensitiveWordInitApi,
    getSensitiveWordBatchAddApi
  } from '/@/api/sysset/sensitive';
  import { selectRadio, SelectAll, failTip, sucTip} from '/@/utils/utils';
  import { validatorEmoji, validatorSpecialString } from '/@/utils/validate';

  const router = useRouter();
  const selectedRowKeys = ref([]);
  const selectedRows = ref([]);

  // 更新表格高度
  const handleToggleTip = (type) => {
    redoHeight();
  };
  const [standardTable, { reload,redoHeight,getForm }] = useTable({
    api: (arg) => getSensitiveWordListApi({ ...arg }),
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    columns: [
      {
        title: '敏感词',
        dataIndex: 'word',
        width: 200,
      },
      {
        title: '分类',
        dataIndex: 'categoryName',
        width: 200,
      },
      {
        title: '添加时间',
        dataIndex: 'createTime',
        width: 200,
      },
      {
        title: '启用状态',
        dataIndex: 'state',
        width: 150,
      },
    ],
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
    useSearchForm: true,
    formConfig: {
      schemas: [
        {
          field: 'word',
          component: 'Input',
          componentProps: {
            placeholder: '请输入敏感词',
          },
          label: '敏感词',
        },
        {
          component: 'Select',
          label: `分类`,
          field: 'categoryId',
          componentProps: {
            placeholder: `请选择分类`,
            options: [],
          },
        },
      ],
    },
    bordered: true,
    striped: false,
    showIndexColumn: true,
    rowKey:'wordId',
    rowSelection: {
      type: 'checkbox',
      selectedRowKeys: selectedRowKeys,
      onSelect: onSelect,
      onSelectAll: onSelectAll,
    },
    clickToRowSelect: false,
  });
  const width = ref(500);
  const title = ref('');
  const visible = ref(false);
  const content = ref([]);
  const confirmBtnLoading = ref(false);
  const operate_id = ref('');
  const operate_type = ref('');

  const wordId = ref('');

  const operate_add_edit = ref([
    {
      type: 'input',
      label: `敏感词`,
      name: 'word',
      placeholder: `请输入敏感词`,
      extra: '最多输入10位',
      maxLength: 10,
      rules: [
        {
          required: true,
          whitespace: true,
          message: '请输入敏感词',
        },
        {
          validator: async (rule, value) => {
            await validatorSpecialString(rule, value);
          },
        },
      ],
    },
    {
      type: 'select',
      label: `分类`,
      name: 'categoryId',
      placeholder: `请选择分类`,
      selData: [],
      rules: [
        {
          required: true,
          message: `请选择分类`,
        },
      ],
    },
    {
      type: 'switch',
      label: `是否启用`, //开关
      name: 'state',
      initialValue: 1, //默认值
      unCheckedValue: 0, //非选中的值 不传返回值为false
      checkedValue: 1, //选中的值 不传返回值为true
    },
  ]); //添加、编辑商品标签弹窗数据

  const operate_add_edit_double = ref([
    {
      type: 'textarea',
      label: `敏感词`,
      name: 'word',
      placeholder: `请输入敏感词`,
      extra: '多个敏感词之间请使用英文逗号”,”进行分隔',
      maxLength: 100,
      rules: [
        {
          required: true,
          whitespace: true,
          message: '请输入敏感词',
        },
      ],
    },
    {
      type: 'select',
      label: `分类`,
      name: 'categoryId',
      placeholder: `请选择分类`,
      selData: [],
      rules: [
        {
          required: true,
          message: `请选择分类`,
        },
      ],
    },
    {
      type: 'switch',
      label: `是否启用`, //开关
      name: 'state',
      initialValue: 1, //默认值
      unCheckedValue: 0, //非选中的值 不传返回值为false
      checkedValue: 1, //选中的值 不传返回值为true
    },
  ]); //添加、编辑商品标签弹窗数据

  //表格点击回调事件
  function handleClick(item, type, check) {
    operate_type.value = type;
    if (item) {
      operate_id.value = item.wordId;
    }
    if (type == 'add' || type == 'edit' || type == 'double') {
      content.value.forEach((it) => {
        it.initialValue = '';
      });
      content.value = [];
      operate_add_edit.value.forEach((it) => {
        if(it.name == 'categoryId'){
          it.initialValue = undefined;
        }else if(it.name == 'state'){
          it.initialValue = 1;
        }else{
          it.initialValue = '';

        }
      });
      operate_add_edit_double.value.forEach((it) => {
        if(it.name == 'categoryId'){
          it.initialValue = undefined;
        }else if(it.name == 'state'){
          it.initialValue = 1;
        }else{
          it.initialValue = '';

        }
      });
    }
    if (type == 'add') {
      content.value = [];
      title.value = '添加敏感词';
      visible.value = true;
      content.value = operate_add_edit.value;
    } else if (type == 'edit') {
      title.value = '编辑敏感词';
      visible.value = true;
      let data = operate_add_edit.value;
      data.map((items) => {
        items.initialValue = item[items.name]!=null ? item[items.name] : undefined;
      });
      content.value = data;
      wordId.value = item.wordId;
    } else if (type == 'del') {
      if(check==2&&selectedRowKeys.value.length==0){
        failTip('请先选中数据')
        return
      }
      operate_role({ wordIds: item });
    } else if (type == 'switch') {
      operate_role({ wordId: item.wordId, state: check });
    }else if(type == 'double'){
      content.value = [];
      title.value = '批量添加敏感词';
      visible.value = true;
      content.value = operate_add_edit_double.value;
    }
  }

  //弹窗取消事件
  function handleCancle() {
    visible.value = false;
    content.value = [];
    wordId.value = '';
  }

  //弹窗确认事件
  function handleConfirm(val) {
    if (operate_type.value == 'edit') {
      val.wordId = wordId.value;
    }
    if (val.word) {
      let keyword = val.word.split(',');
      for (var i = 0; i < keyword.length; i++) {
        if (keyword[i].length == 0) {
          failTip('单个敏感词不能为空');
          return;
        } else if (keyword[i].length > 10) {
          failTip('单个敏感词长度不能超过10位');
          return;
        }
      }
    }
    operate_role(val);
  }

  // 单选多选事件
  function onSelect(record, selected) {
    let rows = selectRadio(
      selectedRowKeys.value,
      selectedRows.value,
      'wordId',
      record,
      selected,
    );
    selectedRowKeys.value = rows.selectedRowKeys;
    selectedRows.value = rows.selectedRows;
  }

  // 全选按钮事件
  function onSelectAll(selected, rows, changeRows) {
    let rowAll = SelectAll(
      selectedRowKeys.value,
      selectedRows.value,
      'wordId',
      selected,
      rows,
      changeRows,
    );
    selectedRowKeys.value = rowAll.selectedRowKeys;
    selectedRows.value = rowAll.selectedRows;
  }

  //商品标签操作
  const operate_role = async (params) => {
    confirmBtnLoading.value = true;
    let res= {};
    if (operate_type.value == 'add') {
      res = await getSensitiveWordAddApi(params);
    } else if (operate_type.value == 'edit') {
      res = await getSensitiveWordEditApi(params);
    } else if (operate_type.value == 'del') {
      res = await getSensitiveWordDelApi(params);
    } else if (operate_type.value == 'switch') {
      res = await getSensitiveWordEditStateApi(params);
    } else if (operate_type.value == 'double') {
      res = await getSensitiveWordBatchAddApi(params);
    }
    confirmBtnLoading.value = false;
    if (res.state == 200) {
      sucTip(res.msg);
      handleCancle();
      reload();
      selectedRowKeys.value = []
      selectedRows.value = []
    } else if (res.state == 267) {
      let data = res.data.join(',')
      failTip('敏感词' + data + '重复，请重新填写');
    } else {
      failTip(res.msg);
    }
  };

  //获取敏感词分类
  const get_category = async () => {
    const { updateSchema } = getForm();
    let res = await getSensitiveWordCategoryListApi({ current: 1,pageSize: 100,state: 1 });
    if (res.state == 200) {
      let selData = [];
      res.data.list.map((item) => {
        selData.push({
          key: item.categoryId,
          name: item.categoryName,
        });
      });
      operate_add_edit.value.forEach((item) => {
        if (item.name == 'categoryId') {
          item.selData = selData;
        }
      });
      operate_add_edit_double.value.forEach((item) => {
        if (item.name == 'categoryId') {
          item.selData = selData;
        }
      });
      await updateSchema({
        field: 'categoryId',
        componentProps: {
          options: [],
        },
      });
      await updateSchema({
        field: 'categoryId',
        componentProps: {
          options: unref(res.data.list).map((item) => ({
            value: item.categoryId,
            label: item.categoryName,
          })),
        },
      });
    }
  };

  // 更新敏感词词库缓存
  const operateSen = async()=>{
    let res = await getSensitiveWordInitApi({})
    if (res.state == 200) {
      sucTip(res.msg);
      handleCancle();
      reload();
    } else {
      failTip(res.msg);
    }
  }

  onMounted(() => {
    get_category()
  });
</script>
<style lang="less">
  
</style>
