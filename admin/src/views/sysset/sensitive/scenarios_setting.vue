<template>
  <div class="sensitive_setting_setting">
    <Spin :spinning="loading">
      <StandardTableRow
        width="100%"
        ref="standardTable"
        :data="info_data.data"
        @callback-event="callbackEvent"
        @submit-event="submitEvent"
      />
      <div style="width: 100%; height: 45px; background: #fff"></div>
      <div
          class="m_diy_bottom_wrap"
          :style="{ position: 'fixed', left: getRealWidth + 10 + 'px' }"
        >
          <div class="add_goods_bottom_btn add_goods_bottom_btn_sel" @click="handleSaveAllData"  v-if="info_data.data.length>0">
           
            保存
          </div>
        </div>
    </Spin>
  </div>
</template>
<script>
  export default {
    name: 'ScenariosSetting',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { Spin } from 'ant-design-vue';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import StandardTableRow from '/@/components/StandardTableRow/index.vue';
  import { getSettingListApi, getSettingUpdateApi } from '/@/api/common/common';
  import { sucTip } from '/@/utils/utils';

  const info_data = reactive({
    data: [],
  });

  const standardTable = ref()
  const { getRealWidth } = useMenuSetting();
  const loading = ref(true);

  // 获取数据
  const get_setting = async () => {
    try {
      loading.value = true
      let description = { //描述信息
        sensitive_word_is_enable: '',
        sensitive_word_type: '',
        sensitive_word_member_is_enable: `涉及：昵称、真实姓名、收货人、详细地址等信息`,
        sensitive_word_comment_is_enable: `涉及：商品评价图文信息`,
        sensitive_word_video_is_enable: `涉及：视频标题、视频介绍、标题、图文介绍、正文内容、评论内容、标签名称、标签主题等`,
        sensitive_word_store_is_enable: `涉及：主营商品信息、店铺名称等`,
        sensitive_word_goods_is_enable: `涉及：商品分类、商品名称、广告语、商品详情、商品评价回复、品牌名称、品牌描述、属性名称、属性值、商品标签、标签描述等`,
        sensitive_word_promotion_is_enable: `涉及：活动名称、规则/活动说明、优惠券名称、排行榜名称、排行榜分类名称、上榜理由、签到活动`,
        sensitive_word_decoration_is_enable: `涉及：导航名称、页脚标题、装修楼层中的标题和子标题`,
        sensitive_word_reason_is_enable: `涉及：拒绝原因`,
        sensitive_word_article_is_enable: `涉及：分类名称、文章标题、文章详情`,
      }
      let str = 'sensitive_word_member_is_enable,' + //会员信息
				'sensitive_word_comment_is_enable,' + //商品评价
				'sensitive_word_video_is_enable,' + //图文模块
				'sensitive_word_store_is_enable,' + //店铺信息
				'sensitive_word_goods_is_enable,' + //商品信息
				'sensitive_word_promotion_is_enable,' + //促销活动
				'sensitive_word_decoration_is_enable,' + //装修模块
				'sensitive_word_reason_is_enable,' + //原因管理
				'sensitive_word_article_is_enable,' //文章管理
      const res = await getSettingListApi({ str: str });
      if (res && res.state == 200) {
        loading.value = false;
        info_data.data = [];
        for (let i in res.data) {
          if (res.data[i].type == 1) {
            
            if (res.data[i].name == 'sensitive_word_type') {
              info_data.data.push({
                type: 'radio',
                label: res.data[i].title,
                desc: description[res.data[i].name] !== undefined ? description[res.data[i].name] : res.data[i].description,
                key: res.data[i].name,
                placeholder: '',
                value: res.data[i].value,
                data: [
                { key: 0, value: '1', title: '平台服务' },
                { key: 1, value: '2', title: '第三方服务' },
              ],
              });
            }else {
              info_data.data.push({
                type: 'input',
                label:  res.data[i].title,
                key:  res.data[i].name,
                placeholder: '请输入' +  res.data[i].title,
                initValue: '',
                value:  res.data[i].value,
                inputType: 'text',
                desc:  description[res.data[i].name] !== undefined ? description[res.data[i].name] : res.data[i].description,
              })
            }
          } else if (res.data[i].type == 4) {
            info_data.data.push({
              type: 'switch',
              label: res.data[i].title,
              desc: description[res.data[i].name] !== undefined ? description[res.data[i].name] : res.data[i].description,
              key: res.data[i].name,
              placeholder: '',
              value: res.data[i].value,
              checkedValue: '1',
              unCheckedValue: '0',
            });
          }
        }
      }
    } catch (error) {
      console.log(error,'error')
    }
  };

  // 更新数据
  const callbackEvent = (item) => {
    let temp = info_data.data.filter((item) => item.key == item.key);
    if (temp.length > 0) {
      temp[0].value = item.val1;
    }
  };

  // 调用子组件里暴露的方法从而调用保存事件
  const handleSaveAllData = ()=> {
    standardTable.value.standardSubmit()
  }

  // 点击保存
  const submitEvent = async (val) => {
    try {
      loading.value = true;
      let res = await getSettingUpdateApi(val);
      if (res.state == 200) {
        loading.value = false;
        sucTip(res.msg);
        get_setting();
      }
    } catch (error) {}
  };

  onMounted(() => {
    get_setting();
  });
</script>
<style lang="less">
.sensitive_setting_setting{
  flex: 1;
  overflow: auto;
}
</style>
