<template>
  <div class="section_padding sensitive_setting_tabs">
    <div class="section_padding_back_add">
      <SldComHeader title="敏感词配置" :clickFlag="true" type="3"  @handle-toggle-tip="handleToggleTip"/>
      <Tabs type="card" v-model:activeKey="activeKey" class="sensitive_setting_tab">
        <TabPane key="1" tab="功能开关">
          <ShowMoreHelpTip :tipData="goodsTip" v-if="sld_show_tip"></ShowMoreHelpTip>
          <SenSetting v-if="activeKey == '1'" style="margin-top: 10px;"/>
        </TabPane>
        <TabPane key="2" tab="应用场景配置">
          <ShowMoreHelpTip :tipData="goodsTip" v-if="sld_show_tip"></ShowMoreHelpTip>
          <ScenariosSetting v-if="activeKey == '2'" style="margin-top: 10px;"/>
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'SensitiveSetting',
  };
</script>
<script setup>
  import { ref, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { Tabs, TabPane, message, RadioGroup, RadioButton } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import ShowMoreHelpTip from '/@/components/ShowMoreHelpTip/index.vue';
  import {sld_need_update_setting} from '/@/utils/utils';
  import SenSetting from './setting.vue';
  import ScenariosSetting from './scenarios_setting.vue';

  const router = useRouter();
  const sld_show_tip = ref(true);
  const goodsTip = ref(sld_need_update_setting());

  const activeKey = ref('1');

  const handleToggleTip = ()=> {
    sld_show_tip.value = !sld_show_tip.value
  }

  onMounted(() => {});
</script>
<style lang="less">
  
</style>
