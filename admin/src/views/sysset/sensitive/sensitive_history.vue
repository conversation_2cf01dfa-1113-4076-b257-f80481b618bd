<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <SldComHeader 
      title="敏感词命中记录"
      tipBtn="导出"
      tipBtnIcon="iconziyuan23"
      @tip-btn-click="handleSldExcel"
      />
      <div class="seckill_label_lists">
        <BasicTable @register="standardTable" style="padding: 0">
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.dataIndex == 'word'">
              <template v-if="record.details && record.details.length > 0">
                <template v-for="(item,index) in record.details">
                  <span :key="index" v-if="item.word">{{ item.word }}{{ (index < record.details.length - 1) ? ',' :'' }}</span>
                </template>
              </template>
              <span v-else>--</span>
            </template>
            <template v-if="column.dataIndex == 'categoryNames'">
              <template v-if="record.details && record.details.length > 0">
                <template v-for="(item,index) in record.details">
                  <span :key="index" v-if="item.categoryName">{{ item.categoryName }}{{ (index < record.details.length - 1) ? ',' :'' }}</span>
                </template>
              </template>
              <span v-else>--</span>
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'SensitiveHistory',
  };
</script>
<script setup>
  import { ref, onMounted,unref } from 'vue';
  import { Switch} from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import {
    getSensitiveWordLogListApi,
    getSensitiveWordCategoryListApi,
    getSensitiveWordLogExportApi
  } from '/@/api/sysset/sensitive';
  import { failTip, sucTip} from '/@/utils/utils';

  const params = ref({})

  const [standardTable, { reload,getForm,getPaginationRef}] = useTable({
    api: (arg) => {
    params.value = arg
     return getSensitiveWordLogListApi({ ...arg })
    },
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    columns: [
      {
        title: '文本信息',
        dataIndex: 'content',
        width: 150,
      },
      {
        title: '敏感词',
        dataIndex: 'word',
        width: 150,
      },
      {
        title: '敏感词类别',
        dataIndex: 'categoryNames',
        width: 150,
      },
      {
        title: '命中时间',
        dataIndex: 'createTime',
        width: 150,
      },
      {
        title: '用户信息',
        dataIndex: 'createName',
        width: 150,
      },
      {
        title: '用户类别',
        dataIndex: 'createUserTypeValue',
        width: 150,
      },
    ],
    useSearchForm: true,
    formConfig: {
      schemas: [
        {
          field: 'word',
          component: 'Input',
          componentProps: {
            placeholder: '请输入敏感词',
          },
          label: '敏感词',
        },
        {
          component: 'Select',
          label: `敏感词类别`,
          field: 'categoryId',
          componentProps: {
            placeholder: `请选择敏感词类别`,
            options: [],
          },
        },
        {
          component: 'RangePicker',
          field: '[startTime,endTime]',
          label: '命中时间',
          componentProps: {
            placeHolder: ['开始时间', '结束时间'],
          },
        },
        {
          field: 'createName',
          component: 'Input',
          componentProps: {
            placeholder: '请输入用户名',
          },
          label: '用户名',
        },
        {
          field: 'createUserType',
          component: 'Select',
          componentProps: {
            placeHolder: '请选择用户类别',
            options: [
              { value: '0', label: `平台运营` },
              { value: '1', label: `自营商家` },
              { value: '2', label: `入驻商家` },
              { value: '3', label: `商城会员` },
            ],
          },
          label: '用户类别',
        },
      ],
    },
     // 点击搜索前处理的参数
    beforeFetch(values) {
      values.startTime = values.startTime
        ? values.startTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
      return values;
    },
    bordered: true,
    striped: false,
    showIndexColumn: true,
    rowKey:'categoryId',
    ellipsis:false,
  });

   //获取敏感词分类
   const get_category = async () => {
    const { updateSchema } = getForm();
    let res = await getSensitiveWordCategoryListApi({ current: 1,pageSize: 100,state: 1 });
    if (res.state == 200) {
      await updateSchema({
        field: 'categoryId',
        componentProps: {
          options: [],
        },
      });
      await updateSchema({
        field: 'categoryId',
        componentProps: {
          options: unref(res.data.list).map((item) => ({
            value: item.categoryId,
            label: item.categoryName,
          })),
        },
      });
    }
  };

  // 导出
  const handleSldExcel = async () => {
    let paramData = JSON.parse(JSON.stringify(params.value))
    for (let i in paramData) {
      if (paramData[i] == undefined) {
        delete paramData[i];
      }
    }
    paramData.fileName = `敏感词命中记录导出`;
    let res = await getSensitiveWordLogExportApi(paramData);
  };



  onMounted(() => {
    get_category()
  });
</script>
<style lang="less">
  
</style>
