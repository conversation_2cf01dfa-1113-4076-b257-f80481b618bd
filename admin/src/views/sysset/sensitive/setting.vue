<template>
  <div class="sensitive_setting_setting">
    <Spin :spinning="loading">
      <StandardTableRow
        width="100%"
        ref="standardTable"
        :data="option_data.data"
        @callback-event="callbackEvent"
        @submit-event="submitEvent"
      />
      <div style="width: 100%; height: 45px; background: #fff"></div>
      <div
          class="m_diy_bottom_wrap"
          :style="{ position: 'fixed', left: getRealWidth + 10 + 'px' }"
        >
          <div class="add_goods_bottom_btn add_goods_bottom_btn_sel" 
          v-if="option_data.data.length>0"
          @click="handleSaveAllData">
            保存
          </div>
        </div>
    </Spin>
  </div>
</template>
<script>
  export default {
    name: 'SenSetting',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { Spin } from 'ant-design-vue';
  import StandardTableRow from '/@/components/StandardTableRow/index.vue';
  import { getSettingListApi, getSettingUpdateApi } from '/@/api/common/common';
  import { sucTip } from '/@/utils/utils';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';

  const detail = ref([]);
  const info_data = reactive({
    data: [],
  });
  const option_data = reactive({
    data: [],
  })
  const { getRealWidth } = useMenuSetting();
  const standardTable = ref()
  const loading = ref(true);

  // 获取数据
  const get_setting = async () => {
    try {
      loading.value = true
      let str = 'sensitive_word_is_enable,' + //功能开关
				'sensitive_word_type,' + //启用服务
				'sensitive_word_aliyun_access_key_id,' + //阿里云AccessKey ID
				'sensitive_word_aliyun_access_key_secret,' + //阿里云AccessKey Secret
				'sensitive_word_aliyun_region_id,' //阿里云服务地域ID
      const res = await getSettingListApi({ str: str });
      if (res && res.state == 200) {
        loading.value = false;
        detail.value = res.data;
        setFormData(res.data);
      }
    } catch (error) {
      console.log(error, 'error')
    }
  };

  // 更新数据
  const callbackEvent = (item) => {
    let temp = detail.value.filter((items) => items.name == item.contentItem.key)[0];
    temp.value = item.val1;
    setFormData(detail.value);
  };

  const setFormData = (data) => {
    let description = { //描述信息
      sensitive_word_is_enable: '',
      sensitive_word_type: '',
        sensitive_word_member_is_enable: '涉及：昵称、真实姓名、收货人、详细地址等信息',
        sensitive_word_comment_is_enable: '涉及：商品评价图文信息',
        sensitive_word_goods_is_enable: '涉及：商品分类、商品名称、广告语、商品详情、商品评价回复、属性名称、属性值等',
        sensitive_word_promotion_is_enable: '涉及：活动名称、规则/活动说明、优惠券名称、排行榜名称、排行榜分类名称、上榜理由、签到活动',
        sensitive_word_decoration_is_enable: '涉及：装修页标题、装修页内容',
        sensitive_word_reason_is_enable: '涉及：拒绝原因',
    }
    info_data.data = [];
    for (let i in data) {
      if (data[i].type == 1) {
        if (data[i].name == 'sensitive_word_type') {
          info_data.data.push({
            type: 'radio',
            label: data[i].title,
            desc: description[data[i].name] !== undefined ? description[data[i].name] : data[i].description,
            key: data[i].name,
            placeholder: '',
            value: data[i].value,
            data: [
              { key: 0, value: '1', title: '平台服务' },
              { key: 1, value: '2', title: '第三方服务' },
            ],
            callback: true,
            width: 300,
          });
        }else {
          info_data.data.push({
            type: 'input',
            label:  data[i].title,
            key:  data[i].name,
            placeholder: '请输入' +  data[i].title,
            initValue: '',
            value:  data[i].value,
            inputType: 'text',
            desc:  description[data[i].name] !== undefined ? description[data[i].name] : data[i].description,
            width: 300,
          })
        }
      } else if (data[i].type == 4) {
        info_data.data.push({
          type: 'switch',
          label: data[i].title,
          desc: description[data[i].name] !== undefined ? description[data[i].name] : data[i].description,
          key: data[i].name,
          placeholder: '',
          value: data[i].value,
          checkedValue: '1',
          unCheckedValue: '0',
          callback: true,
          width: 300,
        });
      }
    }
    if ( info_data.data.length) {
      option_data.switch = info_data.data[0].value == '1' ? true : false;
      option_data.service = info_data.data[1].value;
      if (!option_data.switch) {
        //功能开关关闭
        option_data.data = [info_data.data[0]];
      } else if (option_data.service == '1') {
        //开关开启，启用平台服务
        option_data.data = [info_data.data[0], info_data.data[1]];
      } else {
        //开关开启，启用第三方服务
        option_data.data = info_data.data;
      }
    }
  }

  // 调用子组件里暴露的方法从而调用保存事件
  const handleSaveAllData = ()=> {
    standardTable.value.standardSubmit()
  }

  // 点击保存
  const submitEvent = async (val) => {
    try {
      loading.value = true;
      let res = await getSettingUpdateApi(val);
      if (res.state == 200) {
        loading.value = false;
        sucTip(res.msg);
        get_setting();
      }
    } catch (error) {}
  };

  onMounted(() => {
    get_setting();
  });
</script>
<style lang="less">
.sensitive_setting_setting{
  flex: 1;
  overflow: auto;
}
</style>
