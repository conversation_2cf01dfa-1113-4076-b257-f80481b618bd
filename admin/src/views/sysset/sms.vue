<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <a-spin :spinning="spinning">
        <SldComHeader
          :title="'短信配置'"
        />
        <div class="site_info_scroll">
          <StandardTableRow
            width="100%"
            :data="smsData"
            @callback-event="callbackEvent"
            @submit-event="submitEvent"
          />
        </div>
      </a-spin>
    </div>
  </div>
</template>
<script>
  import { defineComponent, ref, onMounted } from 'vue';
  import { Spin, Modal } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import StandardTableRow from '/@/components/StandardTableRow/index.vue';
  import { getSmsSetting, saveBasicSiteSetting } from '/@/api/sysset/sysset';
  import { SldForbiddenEditUrl } from '/@/enums/baseEnum';
  import { useUserStore } from '/@/store/modules/user';
  import { failTip, sucTip } from '/@/utils/utils';

  export default defineComponent({
    components: {
      [Spin.name]: Spin,
      SldComHeader,
      StandardTableRow,
    },
    setup() {
      const spinning = ref(false);
      const smsAllData = ref([]);
      const smsData = ref([]);
      const sms_type = ref(''); //短信配置类型 1-腾讯云短信 2-云片短信 3-阿里云短信
      const clickEvent = ref(false); //事件防抖动
      const userStore = useUserStore();

      //获取短信配置
      const get_sms_setting = async () => {
        spinning.value = true;
        const res = await getSmsSetting();
        if (res.state == 200 && res.data) {
          smsAllData.value = res.data;
          let temp = res.data.filter((item) => item.name == 'send_sms_type');
          if (temp.length > 0) {
            sms_type.value = temp[0].value;
          }
          initData(res.data);
        } else {
          failTip(res.msg);
        }
        spinning.value = false;
      };

      //数据显示处理
      function initData(res_data) {
        let data = [];
        res_data.map((item) => {
          if (item.type == 1) {
            if (
              (sms_type.value == '1' &&
                (item.name == 'notification_sms_secret_key' ||
                  item.name == 'notification_sms_secret' ||
                  item.name == 'notification_sms_app_id' ||
                  item.name == 'notification_sms_content_signature' ||
                  item.name == 'notification_sms_tplid')) ||
              (sms_type.value == '2' &&
                (item.name == 'notification_sms_key' ||
                  item.name == 'notification_sms_tpl_content_vc' ||
                  item.name == 'notification_sms_signature')) ||
              (sms_type.value == '3' &&
                item.name == 'notification_sms_signature')
            ) {
              data.push({
                type: 'input',
                label: item.title,
                key: item.name,
                placeholder: '请输入' + item.title,
                width: 300,
                right_width: 400,
                height: 60,
                lineHeight: 60,
                initValue: '',
                value: item.value,
                inputType: 'text',
                callback: true,
              });
            }
          } else if (item.type == 4) {
            data.push({
              type: 'radio',
              label: item.title,
              key: item.name,
              width: 300,
              height: 60,
              lineHeight: 60,
              initValue: '',
              value: item.value,
              callback: true,
              data: [
                { key: 0, value: '1', title: '腾讯云短信' },
                { key: 1, value: '2', title: '云片短信' },
                { key: 2, value: '3', title: '阿里云短信' },
              ],
            });
          }
        });
        if (data.length > 0) {
          data.push({
            type: 'button',
            width: 300,
            showSubmit: true,
            submitText: '保存',
            showCancle: false,
          });
        }
        smsData.value = data;
      }

      const callbackEvent = (item) => {
        let temp = smsData.value.filter((items) => items.key == item.contentItem.key);
        if (temp.length > 0) {
          temp[0].value = item.val1;
        }
        if (item.contentItem.key == 'send_sms_type') {
          sms_type.value = item.val1;
          smsAllData.value[0]['value'] = item.val1;
          initData(smsAllData.value);
        }
      };

      const submitEvent = (val) => {
        if (SldForbiddenEditUrl && window.location.href.indexOf(SldForbiddenEditUrl) != -1) {
          Modal.warning({
            title: '提示',
            content:
              '演示站配置数据（短信配置）不支持更改，如需更改，请联系商务',
            okText: '确定',
          });
        } else {
          // 构造提交数据
          const submitData = {};
          smsData.value.forEach(item => {
            if (item.type !== 'button') {
              submitData[item.key] = item.value;
            }
          });
          save_base_site(submitData);
        }
      };

      //保存站点基本配置
      const save_base_site = async (params) => {
        if (clickEvent.value) return;
        clickEvent.value = true;
        
        // 当选择阿里云短信时，只提交短信类型和签名
        let submitParams = params;
        if (sms_type.value === '3') {
          submitParams = {
            send_sms_type: '3',
            notification_sms_signature: params.notification_sms_signature || ''
          };
        }
        
        try {
          const res = await saveBasicSiteSetting(submitParams);
          if (res.state == 200) {
            sucTip(res.msg);
            userStore.setDelKeepAlive(['msgTpl']);
          } else {
            failTip(res.msg);
          }
        } catch (error) {
          failTip('保存失败');
        } finally {
          clickEvent.value = false;
        }
      };

      onMounted(() => {
        get_sms_setting();
      });

      return {
        spinning,
        smsAllData,
        smsData,
        sms_type,
        clickEvent,
        get_sms_setting,
        initData,
        callbackEvent,
        submitEvent,
        save_base_site,
      };
    },
  });
</script>
<style lang="less" scoped></style>
