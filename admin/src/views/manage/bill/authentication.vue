<template>
  <div class="section_padding authentication_box">
    <div class="section_padding_back">
      <Spin :spinning="initLoading">
        <div class="flex_column_center_center authentication_info" v-if="loading && state == 0">
          <img src="@/assets/images/authen/un_authen.png" alt="" />
          <div>尚未实名认证</div>
          <Button type="primary" @click="operate('apply')">立即认证</Button>
        </div>
        <SldComHeader :title="$sldComLanguage('实名认证')" v-else />
        <template v-if="loading && state==131 && isAuthed">
          <div class="authentication_box_height">
            <Descriptions :bordered="true" :column="2" size="middle">
              <DescriptionsItem :label='item.label' v-for="(item,index) in authen_company_data" :key="index">
                <div v-if="item.type=='show_text'">
                  <div v-if="item.text!=null&&item.text.length>62">
                    <Tooltip placement="bottom">
                      <template #title>{{ item.text }}</template>
                      <div style="margin-left: 5px;margin-top: 3px;">
                       <span>{{ item.text.substring(0, 61) }}...</span> 
                      </div>
                    </Tooltip>
                  </div>
                  <div v-else>
                    {{ item.text!=null ? item.text : '--' }}
                  </div>
                </div>
                <div v-if="item.type == 'show_goods_img_more'">
                  <div class="flex_row_start_center">
                    <template v-if="item.data.length>0">
                      <ImagePreviewGroup>
                        <div class="flex_row_start_center" style="flex-wrap: wrap;" v-for="(it,ind) in item.data" :key="ind">
                        <div class="flex_row_center_center invoice_info_box_img">
                            <Image :src="it" style="max-width: 100%;max-height: 100%;"></Image>
                        </div>
                      </div>
                      </ImagePreviewGroup>
                    </template>
                    <div v-else>--</div>
                  </div>
                </div>
              </DescriptionsItem>
            </Descriptions>
          </div>
        </template>
        <template v-if="loading && state != 0 && (state != 131 || (state == 131 && !isAuthed))">
          <div class="flex_row_center_center prograss" v-if="state != 112 && state != 212">
            <div class="flex_row_center_center prograss_item active">
              <div class="prograss_left">
                <img src="@/assets/images/authen/prograss_on.png" alt="" />
                <span>1</span>
              </div>
              <div class="prograss_right">{{ '企业认证' }}</div>
            </div>
            <div class="flex_row_center_center prograss_item" :class="{ active: step > 0 }">
              <div class="prograss_left">
                <img src="@/assets/images/authen/prograss_on.png" alt="" v-if="step > 0" />
                <img src="@/assets/images/authen/prograss_off.png" alt="" v-else />
                <span>2</span>
              </div>
              <div class="prograss_right">绑定手机号</div>
            </div>
            <div class="flex_row_center_center prograss_item" :class="{ active: step > 1 }">
              <div class="prograss_left">
                <img src="@/assets/images/authen/prograss_on.png" alt="" v-if="step > 1" />
                <img src="@/assets/images/authen/prograss_off.png" alt="" v-else />
                <span>3</span>
              </div>
              <div class="prograss_right">签约成功</div>
            </div>
          </div>
          <div class="authentication_info_height full_activity flex_column_start_center">
            <Form layout="inline" ref="formRef" :model="detail">
              <div class="flex_column_center_center" v-if="state == 2">
                <div class="full_acm_activity flex_column_start_start">
                  <template v-if="step == 0">
                    <!-- 企业名称-start -->
                    <div class="item flex_row_start_start">
                      <div class="left" style="width: 140px">
                        <span style="color: red">*</span>企业名称
                      </div>
                      <div class="right flex_column_start_start">
                        <FormItem
                          name="companyName"
                          :rules="[
                            {
                              required: true,
                              whitespace: true,
                              message: $sldComLanguage('请输入企业名称'),
                            },
                          ]"
                        >
                          <Input
                            :maxLength="20"
                            :disabled="disEditType == 1 || disEditType == 2"
                            :placeholder="$sldComLanguage('请输入企业名称')"
                            v-model:value="detail.companyName"
                          />
                        </FormItem>
                      </div>
                    </div>
                    <!-- 企业名称-end -->
                    <!-- 法人姓名-start -->
                    <div class="item flex_row_start_start">
                      <div class="left" style="width: 140px">
                        <span style="color: red">*</span>法人姓名
                      </div>
                      <div class="right flex_column_start_start">
                        <FormItem
                          name="legalPerson"
                          :rules="[
                            {
                              required: true,
                              whitespace: true,
                              message: $sldComLanguage('请输入法人姓名'),
                            },
                          ]"
                        >
                          <Input
                            :maxLength="20"
                            :disabled="disEditType == 2"
                            :placeholder="$sldComLanguage('请输入法人姓名')"
                            v-model:value="detail.legalPerson"
                          />
                        </FormItem>
                      </div>
                    </div>
                    <!-- 法人姓名-end -->
                    <!-- 法人手机号-start -->
                    <div class="item flex_row_start_start" v-if="detail.acctType != '0'">
                      <div class="left" style="width: 140px">
                        <span style="color: red">*</span>法人手机号
                      </div>
                      <div class="right flex_column_start_start">
                        <FormItem
                          name="legalPhone"
                          :validateFirst="true"
                          :rules="[
                            {
                              required: true,
                              whitespace: true,
                              message: $sldComLanguage('请输入法人手机号'),
                            },
                            {
                              validator: async (rule, value) => {
                                await validatorVendorPhone(rule, value);
                              },
                            },
                          ]"
                        >
                          <Input
                            :maxLength="11"
                            :disabled="disEditType == 1 || disEditType == 2"
                            :placeholder="$sldComLanguage('请输入法人手机号')"
                            v-model:value="detail.legalPhone"
                          />
                        </FormItem>
                      </div>
                    </div>
                    <!-- 法人手机号-end -->
                    <!-- 法人证件类型-start -->
                    <div class="item flex_row_start_start">
                      <div class="left" style="width: 140px">
                        <span style="color: red">*</span>法人证件类型
                      </div>
                      <div class="right flex_column_start_start">
                        <FormItem name="identityType"
                          ><span>{{ detail.identityType ? detail.identityType : '' }}</span></FormItem
                        >
                      </div>
                    </div>
                    <!-- 法人证件类型-end -->
                    <!-- 法人证件号码-start -->
                    <div class="item flex_row_start_start">
                      <div class="left" style="width: 140px">
                        <span style="color: red">*</span>法人证件号码
                      </div>
                      <div class="right flex_column_start_start">
                        <FormItem
                          name="legalIds"
                          :validateFirst="true"
                          :rules="[
                            {
                              required: true,
                              whitespace: true,
                              message: $sldComLanguage('请输入法人证件号码'),
                            },
                            {
                              validator: async (rule, value) => {
                                await validatorIdCard(rule, value, '请输入正确的法人证件号码');
                              },
                            },
                          ]"
                        >
                          <Input
                            :maxLength="20"
                            :disabled="disEditType == 2"
                            :placeholder="$sldComLanguage('请输入法人证件号码')"
                            v-model:value="detail.legalIds"
                          />
                        </FormItem>
                      </div>
                    </div>
                    <!-- 法人证件号码-end -->
                    <!-- 对公账户-start -->
                    <div class="item flex_row_start_start">
                      <div class="left" style="width: 140px">
                        <span style="color: red">*</span>对公账户
                      </div>
                      <div class="right flex_column_start_start">
                        <FormItem
                          name="corporateAccount"
                          :validateFirst="true"
                          :rules="[
                            {
                              required: true,
                              whitespace: true,
                              message: $sldComLanguage('请输入对公账户'),
                            },
                          ]"
                        >
                          <Input
                            :maxLength="30"
                            :disabled="
                              disEditType == 1 ||
                              disEditType == 2 ||
                              disEdit_id_bank ||
                              disEdit_enter_type
                            "
                            :placeholder="$sldComLanguage('请输入对公账户')"
                            v-model:value="detail.corporateAccount"
                          />
                        </FormItem>
                      </div>
                    </div>
                    <!-- 对公账户-end -->
                    <!-- 开户银行名称-start -->
                    <div class="item flex_row_start_start">
                      <div class="left" style="width: 140px">
                        <span style="color: red">*</span>开户银行名称
                      </div>
                      <div class="right flex_column_start_start">
                        <FormItem
                          name="depositBank"
                          :validateFirst="true"
                          :rules="[
                            {
                              required: true,
                              whitespace: true,
                              message: $sldComLanguage('请输入开户银行名称'),
                            },
                          ]"
                        >
                          <Input
                            :maxLength="30"
                            :disabled="disEditType == 1 || disEditType == 2 || disEdit_enter_type"
                            :placeholder="$sldComLanguage('请输入开户银行名称')"
                            v-model:value="detail.depositBank"
                          />
                        </FormItem>
                      </div>
                    </div>
                    <!-- 开户银行名称-end -->
                    <!-- 开户行支行名称-start -->
                    <div class="item flex_row_start_start">
                      <div class="left" style="width: 140px">
                        <span style="color: red">*</span>开户行支行名称
                      </div>
                      <div class="right flex_column_start_start">
                        <FormItem
                          name="depositSubBank"
                          :validateFirst="true"
                          :rules="[
                            {
                              required: true,
                              whitespace: true,
                              message: $sldComLanguage('请输入开户行支行名称'),
                            },
                          ]"
                        >
                          <Input
                            :maxLength="30"
                            :disabled="disEditType == 1 || disEditType == 2 || disEdit_enter_type"
                            :placeholder="$sldComLanguage('请输入开户行支行名称')"
                            v-model:value="detail.depositSubBank"
                          />
                        </FormItem>
                      </div>
                    </div>
                    <!-- 开户行支行名称-end -->
                    <!-- 支行行号-start -->
                    <div class="item flex_row_start_start">
                      <div class="left" style="width: 140px">
                        <span style="color: red">*</span>支行行号
                      </div>
                      <div class="right flex_column_start_start">
                        <FormItem
                          name="subBankNo"
                          :validateFirst="true"
                          :rules="[
                            {
                              required: true,
                              whitespace: true,
                              message: $sldComLanguage('请输入支行行号'),
                            },
                          ]"
                        >
                          <Input
                            :maxLength="12"
                            :disabled="disEditType == 1 || disEditType == 2 || disEdit_enter_type"
                            :placeholder="$sldComLanguage('请输入支行行号')"
                            v-model:value="detail.subBankNo"
                          />
                        </FormItem>
                      </div>
                    </div>
                    <!-- 支行行号-end -->
                    <!-- 统一社会信用代码-start -->
                    <div class="item flex_row_start_start">
                      <div class="left" style="width: 140px">
                        <span style="color: red">*</span>统一社会信用代码
                      </div>
                      <div class="right flex_column_start_start">
                        <FormItem
                          name="code"
                          :validateFirst="true"
                          :rules="[
                            {
                              required: true,
                              whitespace: true,
                              message: $sldComLanguage('请输入统一社会信用代码'),
                            },
                          ]"
                        >
                          <Input
                            :maxLength="30"
                            :disabled="(disEditType==1 || disEditType==2 || disEdit_enter_type)"
                            :placeholder="$sldComLanguage('请输入统一社会信用代码')"
                            v-model:value="detail.code"
                          />
                        </FormItem>
                      </div>
                    </div>
                    <!-- 统一社会信用代码-end -->
                    <!-- 法人身份证人像页-start -->
                    <div class="item flex_row_start_start">
                      <div class="left" style="width: 140px">
                        <span style="color: red">*</span>法人身份证人像页
                      </div>
                      <div class="right flex_column_start_start">
                        <FormItem
                        >
                        <Upload
                            :maxCount="1"
                            accept=" .gif, .jpeg, .png, .jpg,"
                            name="file"
                            @click="beforeUploadClick"
                            :disabled="disEditType==2"
                            :action="`${apiUrl}/v3/oss/admin/upload?source=sellerApply`"
                            listType="picture-card"
                            :file-list="fileList_data.personCardUp"
                            :beforeUpload="
                              (file) => beforeUpload(file, ' .gif, .jpeg, .png, .jpg,', 20)
                            "
                            @change="(e) => handleFileChange(e,'personCardUp')"
                            :headers="{
                              Authorization: imgToken,
                            }"
                          >
                            <div v-if="fileList_data.personCardUp.length < 1">
                              <PlusOutlined />
                              <div className="ant-upload-text">上传图片</div>
                            </div>
                          </Upload>
                        </FormItem>
                      </div>
                    </div>
                    <!-- 法人身份证人像页-end -->
                    <!-- 法人身份证国徽页-start -->
                    <div class="item flex_row_start_start">
                      <div class="left" style="width: 140px">
                        <span style="color: red">*</span>法人身份证国徽页
                      </div>
                      <div class="right flex_column_start_start">
                        <FormItem
                        >
                        <Upload
                            :maxCount="1"
                            accept=" .gif, .jpeg, .png, .jpg,"
                            name="file"
                            @click="beforeUploadClick"
                            :disabled="disEditType==2"
                            :action="`${apiUrl}/v3/oss/admin/upload?source=sellerApply`"
                            listType="picture-card"
                            :file-list="fileList_data.personCardDown"
                            :beforeUpload="
                              (file) => beforeUpload(file, ' .gif, .jpeg, .png, .jpg,', 20)
                            "
                            @change="(e) => handleFileChange(e,'personCardDown')"
                            :headers="{
                              Authorization: imgToken,
                            }"
                          >
                            <div v-if="fileList_data.personCardDown.length < 1">
                              <PlusOutlined />
                              <div className="ant-upload-text">上传图片</div>
                            </div>
                          </Upload>
                        </FormItem>
                      </div>
                    </div>
                    <!-- 法人身份证国徽页-end -->
                    <!-- 营业执照-start -->
                    <div class="item flex_row_start_start">
                      <div class="left" style="width: 140px">
                        <span style="color: red">*</span>营业执照
                      </div>
                      <div class="right flex_column_start_start">
                        <FormItem
                        >
                        <Upload
                            :maxCount="1"
                            accept=" .gif, .jpeg, .png, .jpg,"
                            name="file"
                            @click="beforeUploadClick"
                            :disabled="disEditType==2"
                            :action="`${apiUrl}/v3/oss/admin/upload?source=sellerApply`"
                            listType="picture-card"
                            :file-list="fileList_data.businessLicenseImage"
                            :beforeUpload="
                              (file) => beforeUpload(file, ' .gif, .jpeg, .png, .jpg,', 20)
                            "
                            @change="(e) => handleFileChange(e,'businessLicenseImage')"
                            :headers="{
                              Authorization: imgToken,
                            }"
                          >
                            <div v-if="fileList_data.businessLicenseImage.length < 1">
                              <PlusOutlined />
                              <div className="ant-upload-text">上传图片</div>
                            </div>
                          </Upload>
                        </FormItem>
                      </div>
                    </div>
                    <!-- 营业执照-end -->
                  </template>
                  <template v-if="step == 1">
                    <!-- 手机号-start -->
                    <div class="item flex_row_start_start">
                      <div class="left" style="width: 140px">
                        <span style="color: red">*</span>手机号
                      </div>
                      <div class="right flex_column_start_start">
                        <FormItem
                          name="phone"
                          :validateFirst="true"
                          :rules="[
                            {
                              required: true,
                              whitespace: true,
                              message: $sldComLanguage('请输入手机号'),
                            },
                            {
                              validator: async (rule, value) => {
                                await validatorVendorPhone(rule, value);
                              },
                            },
                          ]"
                        >
                          <Input
                            class="auth_input"
                            :maxLength="11"
                            :placeholder="$sldComLanguage('请输入手机号')"
                            v-model:value="detail.phone"
                          />
                        </FormItem>
                      </div>
                    </div>
                    <!-- 手机号-end -->
                    <!-- 验证码-start -->
                    <div class="item flex_row_start_start">
                      <div class="left" style="width: 140px">
                        <span style="color: red">*</span>验证码
                      </div>
                      <div class="right flex_column_start_start">
                        <FormItem
                          name="verificationCode"
                          :validateFirst="true"
                          :rules="[
                            {
                              required: true,
                              whitespace: true,
                              message: $sldComLanguage('请输入验证码'),
                            },
                          ]"
                        >
                          <div class="flex_row_start_center">
                            <Input
                              :maxLength="10"
                              class="auth_input"
                              :placeholder="$sldComLanguage('请输入验证码')"
                              v-model:value="detail.verificationCode"
                            />
                            <div style="text-align: center;cursor: pointer;font-size: 13px;color: #ff7e28;margin-left: 10px;" @click="sendSmsCode">{{ sendTime>0 ? sendTime+' s' : '获取验证码' }}</div>
                          </div>
                        </FormItem>
                      </div>
                    </div>
                    <!-- 验证码-end -->
                  </template>
                </div>
                <Button type="primary" style="margin-top: 35px;" @click="nextStep">下一步</Button>
              </div>
            </Form>
            <template v-if="(state==111 || state==114 || state==130)">
              <div class="flex_column_center_center prograss_authentication_info">
                <img src="@/assets/images/authen/on_authen.png" alt="">
                <div>{{state==111 ? '企业信息审核通过，待绑定手机号' : (state==114 ? '企业信息审核通过，影印件审核中，预计需要5分钟左右' : '签约中')}}</div>
              </div>
            </template>
            <template v-if="(state==112 || state==113 || state==212)">
              <div class="flex_column_center_center prograss_authentication_info">
                <img src="@/assets/images/authen/fail_authen.png" alt="" class="fail_img" />
                <div class="flex_column_center_center">
                  <span>{{ '企业认证审核失败'}}</span>
                  <span>{{'失败原因：' + (refuseReason.desc ? refuseReason.desc : '--')}}</span>
                </div>
                <Button type="primary" @click="operate('reApply')">重新认证</Button>
              </div>
            </template>
            <template v-if="(state==121 || state==231)">
              <div class="flex_column_center_center prograss_authentication_info">
                <img src="@/assets/images/authen/pass_authen.png" alt="" />
                <div>绑定成功，请进行签约</div>
                <Button type="primary" @click="operate('sign')">去签约</Button>
              </div>
            </template>
            <template v-if="state==131">
              <div class="flex_column_center_center prograss_authentication_info">
                <img src="@/assets/images/authen/pass_authen.png" alt="" />
                <div>签约成功</div>
                <Button type="primary" @click="operate('finish')">确定</Button>
              </div>
            </template>
          </div>
        </template>
      </Spin>
    </div>
  </div>
</template>

<script setup>
import { getCurrentInstance, reactive, ref, onMounted } from 'vue';
import SldComHeader from '@/components/SldComHeader/index.vue';
import {
  Spin,
  Button,
  Form,
  FormItem,
  Input,
  RadioGroup,
  Radio,
  Upload,
  Descriptions,
  DescriptionsItem,
  Tooltip,
  ImagePreviewGroup,
  Image
} from 'ant-design-vue';
import { sucTip, failTip } from '@/utils/utils';
import { getAccountTlDetailApi, getAccountTlSignApi,getAccountTlBindPhoneApi,getAccountTlSendCodeApi,getAccountTlCompanyStepApi } from '@/api/manage/authentication';
import { validatorVendorPhone, validatorIdCard } from '/@/utils/validate';
import { getToken } from '/@/utils/auth';
import { useUserStore } from '/@/store/modules/user';
import { useGlobSetting } from '/@/hooks/setting';
import { PlusOutlined } from '@ant-design/icons-vue';

const vm = getCurrentInstance();
const $sldComLanguage = vm?.appContext.config.globalProperties.$sldComLanguage;

const loading = ref(false);
const userStore = useUserStore();
const formRef = ref();
const { apiUrl } = useGlobSetting();

const imgToken = ref('Bearer ' + getToken() || ''); //上传文件token参数

const initLoading = ref(false);
const roleType = ref(0); //角色类型 0 个人 1 企业
const state = ref(0); //认证状态
const isAuthed = ref(false); //是否已认证

/**
 *  0-待开户，111企业信息审核通过待绑定手机号，112-企业信息审核失败，121-企业绑定手机号成功待签约，122-企业绑定手机号失败， 130-签约中，131-签约完成开户成功，132-签约失败；
 *  211-个人实名认证成功待绑定手机号，212-个人实名认证失败，221-个人绑定手机号成功待绑定银行卡，222-个人绑定手机号失败，231个人绑定银行卡成功待签约，232-个人绑定银行卡失败
 */

//企业认证信息
const authen_company_data = ref([
  {
    type: 'show_text',
    name: 'acctTypeValue',
    label: '认证类型',
    labelMinWidth: 10,
    contentMinWidth: 100,
    text: ``,
  },
  {
    type: 'show_text',
    name: 'bindPhone',
    label: '手机号',
    labelMinWidth: 10,
    contentMinWidth: 100,
    text: ``,
  },
  {
    type: 'show_text',
    name: 'companyName',
    label: '企业名称',
    labelMinWidth: 10,
    contentMinWidth: 100,
    text: ``,
  },
  {
    type: 'show_text',
    name: 'legalPerson',
    label: '法人姓名',
    labelMinWidth: 10,
    contentMinWidth: 100,
    text: ``,
  },
  {
    type: 'show_text',
    name: 'legalPhone',
    label: '法人手机号',
    labelMinWidth: 10,
    contentMinWidth: 100,
    text: ``,
  },
  {
    type: 'show_text',
    name: 'legalIdType',
    label: '法人证件类型',
    labelMinWidth: 10,
    contentMinWidth: 100,
    text: ``,
  },
  {
    type: 'show_text',
    name: 'legalIds',
    label: '法人证件号',
    labelMinWidth: 10,
    contentMinWidth: 100,
    text: ``,
  },
  {
    type: 'show_text',
    name: 'code',
    label: '社会统一信用代码',
    labelMinWidth: 10,
    contentMinWidth: 100,
    text: ``,
  },
  {
    type: 'show_goods_img_more',
    name: 'personCardUpPath',
    label: '法人身份证人像页',
    labelMinWidth: 10,
    contentMinWidth: 100,
    data: [],
    extra: ``,
    item_height: 140,
  },
  {
    type: 'show_goods_img_more',
    name: 'personCardDownPath',
    label: '法人身份证国徽页',
    labelMinWidth: 10,
    contentMinWidth: 100,
    data: [],
    extra: ``,
    item_height: 140,
  },
  {
    type: 'show_goods_img_more',
    name: 'businessLicenseImagePath',
    label: '营业执照',
    labelMinWidth: 10,
    contentMinWidth: 100,
    data: [],
    extra: ``,
    item_height: 140,
  },
]);

//认证进度
const step = ref(0);

//信息
const detail = ref({
  name: '', //真实姓名
  legalIds: '', //	身份证号
  phone: '', //手机号
  verificationCode: '', //验证码
  acctType: '1',
  companyName: '', //企业名称
  legalPerson: '', //法人姓名
  legalPhone: '', //法人手机号
  identityType: '', //法人证件类型,1身份证
  corporateAccount: '', //企业对公账户
  depositBank: '', //开户银行名称，测试环境建议使用工农中建交
  depositSubBank: '', //开户支行名称
  subBankNo: '', //支行行号,12位
  code: '', //统一社会信用代码,一证
  personCardUp: '', //身份证正面扫描件
  personCardUpPath: '', //身份证正面扫描件
  personCardDown: '', //身份证背面扫描件
  personCardDownPath: '', //身份证背面扫描件
  businessLicenseImage: '', //营业执照扫描件
  businessLicenseImagePath: '', //营业执照扫描件
});

const sendEvent = ref(false);
const sendTime = ref(0);

// 失败信息
const refuseReason = ref({
  state: '',
  desc: '',
});

const disEditType = ref(0); //禁止编辑类型   0 不禁止  1 禁止编辑信息  2 都禁止
const disEdit_id_bank = ref(false); //禁止编辑身份证号及银行卡号
const disEdit_enter_type = ref(false); //企业认证-状态为113时，需要禁止编辑的数据
const fileList_data = ref({
  personCardUp: [],
  personCardDown: [],
  businessLicenseImage: [],
});
const sendTimeEvent = ref(''); //倒计时定时任务
const tranceNum = ref(''); //绑卡--返回流水号

// 获取认证信息
const getAuthenState = async () => {
  let res = await getAccountTlDetailApi({});
  if (res.state == 200 && res.data) {
    disEditType.value = 0;
    disEdit_id_bank.value = false;
    disEdit_enter_type.value = false;
    fileList_data.value.personCardUp = [];
    fileList_data.value.personCardDown = [];
    fileList_data.value.businessLicenseImage = [];
    refuseReason.value.state = '';
    refuseReason.value.desc = '';
    detail.value.acctType = res.data.acctType ? res.data.acctType.toString() : '0';
    authen_company_data.value.forEach((item) => {
      if (item.name == 'enterType') {
        item.text = item.accept && item.accept == 1 ? '企业' : '个体工商户';
      } else if (item.name == 'legalIdType') {
        item.text = '身份证';
      } else if (
        item.name == 'personCardUpPath' ||
        item.name == 'personCardDownPath' ||
        item.name == 'businessLicenseImagePath'
      ) {
        item.data = res.data[item.name] != undefined ? [res.data[item.name]] : [];
      } else {
        item.text =
          res.data[item.name] != undefined &&
          res.data[item.name] != null &&
          res.data[item.name] != ''
            ? res.data[item.name]
            : '--';
      }
    });
    detail.value.companyName = res.data.companyName != undefined ? res.data.companyName : '';
    detail.value.phone = res.data.contactPhone != undefined ? res.data.contactPhone : '';
    detail.value.identityType = '身份证';
    detail.value.personCardUp = res.data.personCardUp != undefined ? res.data.personCardUp : '';
    detail.value.personCardUpPath =
      res.data.personCardUpPath != undefined ? res.data.personCardUpPath : '';
    detail.value.personCardDown =
      res.data.personCardDown != undefined ? res.data.personCardDown : '';
    detail.value.personCardDownPath =
      res.data.personCardDownPath != undefined ? res.data.personCardDownPath : '';
    detail.value.businessLicenseImage =
      res.data.businessLicenseImage != undefined ? res.data.businessLicenseImage : '';
    detail.value.businessLicenseImagePath =
      res.data.businessLicenseImagePath != undefined ? res.data.businessLicenseImagePath : '';
    // 图片数据
    if (res.data.personCardUp && res.data.personCardUpPath) {
      fileList_data.value.personCardUp = [
        {
          uid: res.data.personCardUp,
          thumbUrl: res.data.personCardUpPath,
          name: res.data.personCardUpPath,
          status: 'done',
          response: {
            state: 200,
            data: {
              path: res.data.personCardUp,
              url: res.data.personCardUpPath,
            },
          },
        },
      ];
    }
    if (res.data.personCardDown && res.data.personCardDownPath) {
      fileList_data.value.personCardDown = [
        {
          uid: res.data.personCardDown,
          thumbUrl: res.data.personCardDownPath,
          status: 'done',
          name: res.data.personCardDownPath,
          response: {
            state: 200,
            data: {
              path: res.data.personCardDown,
              url: res.data.personCardDownPath,
            },
          },
        },
      ];
    }
    if (res.data.businessLicenseImage && res.data.businessLicenseImagePath) {
      fileList_data.value.businessLicenseImage = [
        {
          uid: res.data.businessLicenseImage,
          thumbUrl: res.data.businessLicenseImagePath,
          status: 'done',
          name: res.data.businessLicenseImagePath,
          response: {
            state: 200,
            data: {
              path: res.data.businessLicenseImage,
              url: res.data.businessLicenseImagePath,
            },
          },
        },
      ];
    }
    if (res.data.state == 211) {
      state.value = 2;
      step.value = 1;
      detail.value.legalIds = res.data.legalIds;
    } else if (res.data.state == 221) {
      state.value = 2;
      step.value = 2;
      detail.value.legalIds = res.data.legalIds;
    } else if (res.data.state == 231) {
      state.value = 231;
      step.value = 2;
    } else if (res.data.state == 111) {
      state.value = 2;
      step.value = 1;
      disEditType.value = 2;
    } else if (res.data.state == 112 || res.data.state == 113) {
      detail.value.acctProtocolNo = res.data.acctProtocolNo;
      detail.value.bindPhone = res.data.bindPhone;
      detail.value.code = res.data.code;
      detail.value.contactName = res.data.contactName;
      detail.value.corporateAccount = res.data.state == 112 ? '' : res.data.corporateAccount;
      detail.value.depositBank = res.data.depositBank;
      detail.value.depositSubBank = res.data.depositSubBank;
      detail.value.legalIds = '';
      detail.value.legalPerson = res.data.legalPerson;
      detail.value.legalPhone = res.data.legalPhone;
      detail.value.subBankNo = res.data.subBankNo;
      refuseReason.value.state = res.data.state;
      refuseReason.value.desc = res.data.refuseReason;
      state.value = res.data.state;
      step.value = 0;
      disEditType.value = res.data.enterType == 1 && state == 113 ? 0 : state == 112 ? 0 : 1;
      disEdit_id_bank.value =
        res.data.enterType == 1 && state == 113 ? false : state == 112 ? false : true;
      disEdit_enter_type.value = res.data.enterType == 1 && state == 113 ? true : false;
    } else if (res.data.state == 114) {
      state.value = 114;
      step.value = 1;
    } else if (res.data.state == 121 || res.data.state == 130) {
      state.value = 121;
      step.value = res.data.enterType == 1 ? 1 : 2;
    } else {
      state.value = res.data.state != undefined ? res.data.state : 0;
      step.value = 0;
    }
    if (
      location.search &&
      location.search.indexOf('result=') != -1 &&
      location.search.split('result=')[1].split('&')[0] == 'ok'
    ) {
      res.data.state = 131;
      state.value = 131;
      step.value = 0;
    }
    loading.value = true;
    roleType.value = res.data.enterType != undefined ? res.data.enterType : 0;
    isAuthed.value = res.data.state != undefined && res.data.state == 131 ? true : false;
    initLoading.value = false;
  }else{
    failTip(res.msg)
  }
};

//操作
const operate = async (type) => {
  if (type == 'apply') {
    state.value = 2;
  } else if (type == 'reApply') {
    step.value = 0;
    state.value = 2;
  } else if (type == 'finish') {
    isAuthed.value = true;
  } else if (type == 'sign') {
    initLoading.value = true;
    let res = await getAccountTlSignApi();
    initLoading.value = false;
    if (res.state == 200 && res.data) {
      window.location.href = res.data;
    } else {
      failTip(res.msg);
    }
  }
};

//获取验证码
const sendSmsCode = async()=> {
  if(sendTimeEvent.value || sendEvent.value){
    return
  }
  sendEvent.value = true
  let params = {}
  if(detail.value.phone.indexOf('*') != -1){
    failTip('请输入正确的手机号');
    sendEvent.value = false
    return;
  }
  params.phone = detail.value.phone;
  let res;
  res = await getAccountTlSendCodeApi(params)
  if(res.state == 200){
    if (step.value == 2) {
      tranceNum.value = res.data;
    }
    sendTimeEvent.value = setInterval(() => {
      if (sendTime.value == 0) {
        sendTime.value = 60;
      } else if (sendTime.value > 1 && sendTime.value <= 60) {
        sendTime.value -= 1;
      } else if (sendTime.value == 1) {
        sendTime.value = 0;
        sendEvent.value = false;
        clearInterval(sendTimeEvent.value);
        sendTimeEvent.value = '';
      }
    }, 1000)
  }else{
    failTip(res.msg);
    sendEvent.value = false
  }
}

// 下一步
const nextStep = async()=> {
  formRef.value.validate().then(async (values) => {
    if(initLoading.value){
      return
    }else if(sendTimeEvent.value){
      clearInterval(sendTimeEvent.value)
      sendTimeEvent.value = ''
      sendEvent.value = false
      sendTime.value = 0
    }
    if(step.value == 0 ||  step.value == 1){
      if(step.value == 0){
        let obj = ['personCardUp','personCardDown','businessLicenseImage']
        let obj_title = ['法人身份证人像页','法人身份证国徽页','营业执照']
        for(let i in obj){
          let item = obj[i]
          if(fileList_data.value[item]&&fileList_data.value[item].length>0){
            if(fileList_data.value[item][0].response&&fileList_data.value[item][0].response.data&&fileList_data.value[item][0].response.data.path){
              values[item] = fileList_data.value[item][0].response.data.path
            }else{
              failTip('请上传'+obj_title[i])
              return
            }
          }else{
            failTip('请上传'+obj_title[i])
            return
          }
        }
        values.identityType = 1;
      }
      initLoading.value = true
      let res;
      if(step.value == 0){
        res = await getAccountTlCompanyStepApi(values)
      }else{
        res = await getAccountTlBindPhoneApi(values)
      }
      if(res.state == 200){
        if(step.value == 0){
          getAuthenState()
        }else{
          step.value++
          state.value = 121
        }
      }else{
        failTip(res.msg);
      }
      initLoading.value = false
    }
  })
}


const beforeUploadClick = async()=> {
    imgToken.value = 'Bearer ' + userStore.getToken
    if(userStore.getToken){
      let res = await userStore.getSldCommonService()
      if(res.state == 200){
        imgToken.value = 'Bearer ' + res.token
      }
    }
  }

  //文件上传前处理数据
  function beforeUpload(file, accept, limit) {
    if (accept != undefined && accept != null && accept) {
      //校验文件格式类型
      let accept_list = accept
        .replaceAll(' ', '')
        .split(',')
        .filter((item) => item && item.trim());
      let file_type = file.name ? '.' + file.name.split('.').at(-1) : '';
      if (accept_list.length > 0 && file_type != '' && accept_list.indexOf(file_type) == -1) {
        failTip('上传文件格式有误');
        return false;
      }
    }
    let uploadLimit = limit ? limit : 20;
    if (file.size > 1024 * 1024 * uploadLimit) {
      failTip('上传文件过大，请上传小于' + uploadLimit + 'M的文件');
      return false;
    }
  }

  //数据变化事件
  function handleFileChange(e,key) {
    if (e.file.status != undefined && e.file.status != 'error') {
      e.fileList.forEach((item) => {
        if (item.response && item.response.data) {
          item.url = item.response.data.url;
        }
      });
      fileList_data.value[key] = e.fileList;
    }
  }

onMounted(() => {
  loading.value = false;
  getAuthenState();
});
</script>

<style lang="scss">
@import url('./style/authentication.less');
@import url('/@/assets/css/promotion.less');
</style>
