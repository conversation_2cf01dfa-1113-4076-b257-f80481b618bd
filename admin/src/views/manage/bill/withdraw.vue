<template>
  <div class="section_padding point_goods_list">
    <div class="section_padding_back">
      <SldComHeader
        :title="$sldComLanguage('提现')"
      />
      <div v-if="loading">
        <template v-if="!authenState">
          <div class="flex_column_center_center" style="width:100%;height:55vh;">
            <img src="@/assets/images/authen/fail_authen.png" alt="">
            <span style="margin-top: 10px;margin-bottom: 20px;">尚未实名认证，请优先完成实名认证。</span>
            <Button type="primary" @click="gotoAuth">立即认证</Button>
          </div>
        </template>
        <Tabs v-else type="card" v-model:activeKey="activeKey" class="tabs_nav">
          <TabPane key="1" :tab="$sldComLanguage('账户提现')">
            <WithdrawList />
          </TabPane>
          <TabPane key="2" :tab="$sldComLanguage('银行卡')">
            <BankList :detail="detail"/>
          </TabPane>
        </Tabs>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    name: '',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, onMounted } from 'vue';
  import { Tabs, TabPane ,Button} from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { getAccountTlDetailApi } from '@/api/manage/authentication';
  import { useRouter } from 'vue-router';
  import WithdrawList from './withdraw_list.vue';
  import BankList from './bank_list.vue';

  const vm = getCurrentInstance();
  const $sldComLanguage = vm?.appContext.config.globalProperties.$sldComLanguage;

  const activeKey = ref('1');
  const router = useRouter();

  const loading = ref(true)
  const authenState = ref(false)//用户是否签约
  const detail = ref({})


  const getAuthenState = async()=> {
    let res = await getAccountTlDetailApi({})
    if(res.state == 200 && res.data && res.data.state && res.data.state == 131){
      authenState.value = true
      detail.value = res.data
      loading.value = true
    }else{
      loading.value = true
    }
  }

  const gotoAuth = ()=> {
    router.push('/manage_bill/authen');
  }


  onMounted(() => {
    getAuthenState()
  });
  
</script>
<style lang="less">
  .point_goods_list {
    .tabs_nav {
      .ant-tabs-nav {
        margin-bottom: 0;
      }
    }
  }
</style>
