<template>
  <div>
    <div class="flex_row_start_center" style="margin-top: 10px;margin-bottom: 10px;margin: 10px;">
      <div class="account_info">
        <span>账户余额:</span>
        <span>￥{{accountInfo.balance>0 ? accountInfo.balance.toFixed(2) : 0}}</span>
        <span @click="refresh">刷新</span>
      </div>
    </div>
    <div>
      <BasicTable @register="standardTable" style="padding: 0">
        <template #tableTitle>
          <div class="toolbar">
            <div class="toolbar_btn" @click="addTack">
              <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#ff5908" />
              <span>{{ $sldComLanguage('提现') }}</span>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex == 'bankAccount'">
             <div class="flex_column_center_start">
              <span>开户行：{{record.depositBank}}</span>
              <span v-if="record.bankType==1">收款账户：{{text?text:'--'}}</span>
              <span v-else>收款账户：{{record.corporateAccount?record.corporateAccount:'--'}}</span>
             </div>
          </template>
        </template>
      </BasicTable>
    </div>

      <Modal
        destroyOnClose
        :maskClosable="false"
        :title="title"
        :visible="modalVisible"
        :width="600"
        :okText="okText"
        @ok="sldConfirm()"
        :confirmLoading="confirmLoading"
        @cancel="sldCancle()"
      >
        <div class="flex_row_center_center" style="height:150px" v-if="type == 'authen'">
          <div style="width: 200px;text-align: center;">尚未绑定银行卡，请绑定银行卡后再进行提现操作。</div>
        </div>
        <div class="flex_row_center_center" style="height:150px" v-if="type == 'tips'">
          <div style="width: 200px;text-align: center;">每日20时至次日7时不可提现。</div>
        </div>
        <div class="flex_column_center_center modal_bank modal_bank_form" v-if="type == 'add'">
          <div class="modal_tips">
            <span>温馨提示：提现手续费为{{ feeWithdraw }}%，最低提现金额为￥{{ minWithdraw>0?Number(minWithdraw).toFixed(2):0 }}。</span>
          </div>
          <div class="flex_column_center_center">

            <Form layout="inline" ref="formRef">
              <FormItem name="amount" label="提现金额" style="width:600px;">
                <div class="flex_row_start_center modal_bank_money">
                  <InputNumber
                      :max="balanceMax"
                      :min="0"
                      :placeholder="'可提现金额￥'+accountInfo.balance"
                      style="width: 300px !important"
                      :precision="2"
                      v-model:value="amount"
                    />
                    <span @click="setAll()">全部</span>
                </div>
              </FormItem>
              <FormItem name="amount" label="收款账户"  style="width:600px;">
                <RadioGroup style="width: 425px;" v-model:value="bankId">
                  <div class="flex_row_start_center modal_bank_list">
                    <template v-if="bankList.length>0">
                      <div class="modal_bank_item" v-for="(item,index) in bankList" :key="index">
                        <Radio :value="item.accountId">
                          <div class="modal_bank_info">
                            <div class="modal_bank_title">
                              {{ item.depositBank }}
                              <span v-if="item.isDefault == 1">默认</span>
                            </div>
                            <div class="modal_bank_account">
                              收款账户：{{item.bankAccount ? item.bankAccount : item.corporateAccount}}
                            </div>
                          </div>
                        </Radio>
                      </div>
                    </template>
                  </div>
                </RadioGroup>
              </FormItem>
            </Form>
          </div>
        </div>
      </Modal>
  </div>
</template>
<script>
  export default {
    name: 'RefundLists',
  };
</script>
<script setup>
  import { getCurrentInstance, ref, reactive, onMounted } from 'vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import {Modal,Form,InputNumber,FormItem,RadioGroup,Radio } from 'ant-design-vue';
  import { sucTip, failTip, } from '@/utils/utils';
  import { getSettingListApi } from '/@/api/common/common';
  import {
    getWithdrawalTlListApi,
    getBankAccountListApi,
    getWithdrawalTlAccountInfoApi,
    getWithdrawalTlApplyApi
  } from '/@/api/manage/withdraw';

  const vm = getCurrentInstance();
  const $sldComLanguage = vm?.appContext.config.globalProperties.$sldComLanguage;

  const title = ref('')
  const modalVisible = ref(false)
  const okText = ref('确定')
  const type = ref('add')
  const formRef = ref(null)
  const confirmLoading = ref(false)

  const balanceMax = ref(999999)
  //账户信息
  const accountInfo = ref({
    autoWithdrawal: 0,
    balance: 0,
    remainWithdrawTime: null,
  })
  const bankList = ref([])
  const amount = ref(0)
  const bankId = ref('')
  const minWithdraw = ref(0)//最低提现金额
  const feeWithdraw = ref(0)//提现手续费

  const default_bank_id = ref('')

  const [standardTable, { reload }] = useTable({
    api: (arg) => getWithdrawalTlListApi({ ...arg,}),
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    columns: [
      {
        title: $sldComLanguage('提现单号'),
        dataIndex: 'withdrawalSn',
        width: 150,
      },
      {
        title: $sldComLanguage('提现金额'),
        dataIndex: 'amount',
        width: 100,
        customRender: ({ text, record }) => {
            return text!=undefined ? '¥'+Number(text).toFixed(2) : '--';
        },
      },
      {
        title: $sldComLanguage('手续费'),
        dataIndex: 'fee',
        width: 150,
        customRender: ({ text }) => {
          return text!=undefined ? '¥'+Number(text).toFixed(2) : '--';
        },
      },
      {
        title: $sldComLanguage('提现时间'),
        dataIndex: 'createTime',
        width: 160,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: $sldComLanguage('收款账户'),
        dataIndex: 'bankAccount',
        width: 150,
      },
      {
        title: $sldComLanguage('退款状态'),
        dataIndex: 'stateValue',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
    ],
    useSearchForm: true,
    formConfig: {
      schemas: [
        {
          field: 'withdrawalSn',
          component: 'Input',
          componentProps: {
            placeholder: $sldComLanguage('请输入提现单号'),
            size: 'default',
          },
          label: $sldComLanguage('提现单号'),
        },
        {
          field: '[createTimeStart,createTimeEnd]',
          component: 'RangePicker',
          componentProps: {
            format: 'YYYY-MM-DD',
            placeholder: [$sldComLanguage('开始时间'), $sldComLanguage('结束时间')],
          },
          label: $sldComLanguage('提现时间'),
        },
        {
        component: 'Select',
        label: $sldComLanguage('提现状态'),
        field: 'state',
        componentProps: {
          placeholder: $sldComLanguage('请选择提现状态'),
          options: [
            { value: '', label: $sldComLanguage('全部') },
            { value: '10', label: $sldComLanguage('提现中') },
            { value: '20', label: $sldComLanguage('提现成功') },
            { value: '30', label: $sldComLanguage('提现失败') },
          ],
        },
      },
      ],
    },
    beforeFetch(values) {
      values.createTimeStart = values.createTimeStart
        ? values.createTimeStart.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.createTimeEnd = values.createTimeEnd ? values.createTimeEnd.split(' ')[0] + ' 23:59:59' : undefined;
      return values;
    },
    ellipsis:false,
    bordered: true,
    striped: false,
    showIndexColumn: true,
    rowKey:'withdrawalSn',
  });
  
  // 提现
  const addTack = async () => {
    let time = new Date();
    let hour = time.getHours();
    confirmLoading.value = false
    if (hour >= 20 || hour <= 7) {
      type.value = 'tips'
      okText.value = '我已知晓'
      title.value = '确定'
      modalVisible.value = true
    }else if(bankList.value.length > 0){
      type.value = 'add'
      okText.value = '确定'
      title.value = '发起提现'
      amount.value = 0
      bankId.value = default_bank_id.value
      modalVisible.value = true
    }else{
      type.value = 'authen'
      okText.value = '去绑定'
      title.value = '提示'
      modalVisible.value = true
    }
  }

  //获取账户列表
  const get_bank_list = async()=> {
    let res = await getBankAccountListApi({pageSize: 10000})
    if(res.state == 200 && res.data.list){
      for (let i in res.data.list) {
        if (res.data.list[i].isDefault == 1) {
          default_bank_id.value = res.data.list[i].accountId;
        }
      }
      bankList.value = res.data.list
    }
  }

  //刷新
  const refresh = ()=> {
    get_account(true);
  }

  //获取账户信息
  const get_account = async(showTip)=> {
    let res = await getWithdrawalTlAccountInfoApi({})
    if(res.state == 200){
      accountInfo.value = res.data
      balanceMax.value = accountInfo.value.balance>999999?999999:Number(accountInfo.value.balance)
      if (showTip) {
        sucTip('刷新成功');
      }
    }else{
      if(showTip){
        failTip(res.msg)
      }
    }
  }

  //获取提现数据
  const getSetting = async()=> {
    let res = await getSettingListApi({str:'tl_withdraw_store_min,tl_withdraw_store_fee'})
    if(res.state == 200){
      res.data.forEach(item => {
        if (item.name == "tl_withdraw_store_min") {
          minWithdraw.value = item.value
        } else if (item.name == "tl_withdraw_store_fee") {
          feeWithdraw.value = item.value
        }
      })
    }
  }

  //设置全部金额提现
  const setAll = ()=> {
    amount.value = JSON.parse(JSON.stringify(accountInfo.value.balance))
  }

  const sldConfirm = async()=> {
    if(type.value == 'authen'){
      sldCancle()
    }else if(type.value == 'tips'){
      sldCancle()
    }else{
      if(!amount.value){
        failTip('请输入提现金额')
      } else if (amount.value < minWithdraw.value) {
        failTip('请输入提最低提现金额为￥' + (minWithdraw.value>0?Number(minWithdraw.value).toFixed(2):0))
      } else if (!bankId.value){
        failTip('请选择收款账户')
      } else {
        confirmLoading.value = true
        let res = await getWithdrawalTlApplyApi({amount:amount.value,accountId:bankId.value})
        if(res.state == 200){
          sucTip(res.msg);
          sldCancle()
          reload()
          setTimeout(()=>{
            get_bank_list()
            get_account()
          },500)
        }else if(res.state == 267){
          window.location.href = res.data;
          confirmLoading.value = false
        }else {
          confirmLoading.value = false
          failTip(res.msg);
        }
      }
    }
  }

  const sldCancle = ()=> {
    modalVisible.value = false
    confirmLoading.value = false
  }


  onMounted(() => {
    get_bank_list()
    get_account()
    getSetting()
  });

</script>
<style lang="less">
@import './style/withdraw.less';
.refund_lists{
  .goods_info {
    .business_load_img {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      margin: auto;
  
      img {
        max-width: 100%;
        max-height: 100%;
      }
    }
  }
}
</style>
