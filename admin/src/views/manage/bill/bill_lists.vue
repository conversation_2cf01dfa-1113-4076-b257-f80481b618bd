<template>
  <div class="section_padding">
    <!-- 添加店铺快速结算模块 -->
    <div class="quick_bill_box">
      <div class="quick_bill_title">
        <div class="title_line"></div>
        <span>店铺快速结算</span>
      </div>
      <div class="quick_bill_content">
        <div class="store_search">
          <div class="input_label">店铺名称：</div>
          <div class="search_wrapper">
            <a-input
              v-model:value="storeKeyword"
              placeholder="请输入店铺名称"
              class="store_input"
              @focus="showDropdown = true"
              @input="handleStoreInput"
            />
            <div class="search_dropdown" v-if="showDropdown && storeOptions.length > 0">
              <div 
                v-for="option in storeOptions" 
                :key="option.value" 
                class="dropdown_item"
                @click.stop="selectStore(option)"
              >
                {{ option.label }}
              </div>
            </div>
          </div>
        </div>
        <a-button type="primary" class="quick_bill_btn" :loading="quickBillLoading" @click="handleQuickBill">快速结算</a-button>
      </div>
    </div>

    <div class="section_padding_back_add">
      <SldComHeader
        :type="2"
        :title="'结算账单管理'"
        :tipTitle="'温馨提示'"
        :tipData="[
          '计算公式：结算金额 = 订单金额 - 支付平台手续费  - 平台佣金 + 退还佣金 - 退单金额 + 平台优惠券 + 积分抵现金额'
          +' - 定金赔偿金额'
          //spreader-1-start
          +' - 推手佣金'
          //spreader-1-end
          +' - 供应商结算金额'
          
          ,
          '结算流程：生成结算单 > 店铺确认 > 平台审核 > 结算完成',
        ]"
        :clickFlag="true"
        @handle-toggle-tip="handleToggleTip"
      />
      <BasicTable @register="standardTable" style="padding: 0">
        <template #tableTitle>
          <div class="toolbar">
            <div class="toolbar_btn" @click="handleClick(null, 'export')">
              <AliSvgIcon iconName="iconziyuan23" width="15px" height="15px" fillColor="#ff5908" />
              <span>结算单导出</span>
            </div>
            <div class="toolbar_btn" @click="handleClick(null, 'trigger_bill')">
              <AliSvgIcon iconName="iconziyuan23" width="15px" height="15px" fillColor="#ff5908" />
              <span>更新结算单</span>
            </div>
            <div class="toolbar_btn" @click="handleClick(null, 'batch_pay')">
              <AliSvgIcon iconName="iconziyuan23" width="15px" height="15px" fillColor="#ff5908" />
              <span>批量打款</span>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.key == 'action'">
            <span class="common_page_view" @click="handleClick(record, 'view')">查看</span>
            <span 
              class="common_page_view" 
              style="margin-left: 8px;"
              @click="handleExport(record)"
            >导出结算单</span>
            <span 
              v-if="record.state == 3"
              class="common_page_edit" 
              style="margin-left: 8px;"
              @click="handlePay(record)"
            >一键打款</span>
          </template>
          <template v-else-if="column.key == 'startTime'">
            <div style="display: flex; flex-direction: column">
              <span>{{ text }}</span>
              <span>~</span>
              <span>{{ record.endTime }}</span>
            </div>
          </template>
          <template v-else-if="column.key">
            {{ text !== undefined && text !== null && text != '' ? text : '--' }}
          </template>
        </template>
      </BasicTable>
    </div>
  </div>
  <SldModal
    :width="width"
    :title="title"
    :visible="visible"
    :content="content"
    :confirmBtnLoading="confirmBtnLoading"
    :showFoot="true"
    @cancle-event="handleCancle"
    @confirm-event="handleConfirm"
    @callback-event="handleCallback"
  />
</template>
<script>
import SldModal from '@/components/SldModal/index.vue';
import { defineComponent, onMounted, ref, computed, watch, onUnmounted } from 'vue';
import { confirmPaymentBill, exportBillList, getBillList, triggerBill, exportBillDetail, batchConfirmPayment, getStoreList } from '/@/api/manage/manage';
import { quickBill } from '/@/api/manage/bill';
import SldComHeader from '/@/components/SldComHeader/index.vue';
import { BasicTable, useTable } from '/@/components/Table';
import { useGo } from '/@/hooks/web/usePage';
import { failTip, sucTip } from '/@/utils/utils';
    export default defineComponent({
    components: {
      SldComHeader,
      BasicTable,
      SldModal,
    },
    setup() {
      const go = useGo();
      const checkedKeys = ref([]); //表格的键值声明，非必填，主要用于勾选能中的数据处理
      const checkedKeysForTable = computed(() => checkedKeys.value);
      const rowKey = ref('billId'); //表格开启选择功能时，需要声明选中数据变量
      const operate_type = ref('');
      const click_event = ref(false);
      const width = ref(550);
      const title = ref('确认打款');
      const visible = ref(false);
      const content = ref([]);
      const confirmBtnLoading = ref(false);
      const currentBill = ref(null); // 当前操作的账单
      const loading = ref(false);

      // 快速结算相关变量
      const storeKeyword = ref('');
      const storeOptions = ref([]);
      const quickBillLoading = ref(false);
      const showDropdown = ref(false);
      const selectedStoreId = ref(null);
      const allStores = ref([]); // 存储所有店铺列表
      
      // 获取所有店铺列表
      const fetchAllStores = async () => {
        try {
          const res = await getStoreList({ pageSize: 999 }); // 获取大量店铺
          if (res.state === 200 && res.data && res.data.list) {
            allStores.value = res.data.list.map(store => ({
              value: store.storeId,
              label: store.storeName
            }));
          }
        } catch (error) {
          console.error('获取店铺列表失败:', error);
        }
      };
      
      // 监听输入，在前端过滤店铺
      watch(storeKeyword, (newVal) => {
        if (newVal) {
          const keyword = newVal.toLowerCase();
          storeOptions.value = allStores.value.filter(store => 
            store.label.toLowerCase().includes(keyword)
          ).slice(0, 10); // 最多显示10个结果
          
          if (storeOptions.value.length > 0) {
            showDropdown.value = true;
          }
        } else {
          storeOptions.value = [];
          showDropdown.value = false;
        }
      });

      const columns = ref([
        {
          title: '店铺名称',
          dataIndex: 'storeName',
          width: 150,
        },
        {
          title: '结算单号',
          dataIndex: 'billSn',
          width: 140,
        },
        {
          title: '出账时间',
          dataIndex: 'createTime',
          width: 160,
        },
        {
          title: '结算日',
          dataIndex: 'startTime',
          width: 160,
        },
        {
          title: '订单金额(元)',
          dataIndex: 'orderAmount',
          width: 110,
        },
        {
          title: '佣金(元)',
          dataIndex: 'commission',
          width: 110,
        },
        {
          title: '退单金额(元)',
          dataIndex: 'refundAmount',
          width: 110,
        },
        {
          title: '退还佣金(元)',
          dataIndex: 'refundCommission',
          width: 110,
        },
        {
          title: '平台优惠券',
          dataIndex: 'platformVoucherAmount',
          width: 110,
        },
        {
          title: '积分抵扣金额(元)',
          dataIndex: 'integralCashAmount',
          width: 140,
        },
        {
          title: '定金赔偿金额(元)',
          dataIndex: 'compensationAmount',
          width: 140,
        },
        //spreader-2-start
        {
          title: '推手佣金(元)',
          dataIndex: 'attachCommission',
          width: 110,
        },
        //spreader-2-end
        
        {
          title: '应结金额(元)',
          dataIndex: 'settleAmount',
          width: 110,
        },
        {
          title: '结算状态',
          dataIndex: 'stateValue',
          width: 100,
        },
      ])

      const operate_data = ref([
        {
          type: 'inputnum',
          label: '结算金额',
          name: 'settleAmount',
          initialValue: '',
          precision: 2,
          min: 0,
          placeholder: '请输入结算金额',
          rules: [{ required: true, message: '请输入结算金额' }],
          callback: true,
          originAmount: 0,
          style: {},
          className: 'amount-input',
          addonAfter: '元',
          stringMode: true,
          maxLength: 16
        },
        {
          type: 'show_content',
          label: '结算单号',
          name: 'billSn',
          content: '',
        },
        {
          type: 'textarea',
          label: `打款备注`,
          name: 'paymentRemark',
          placeholder: `请输入汇款单号、支付方式等付款凭证信息，最多输入200字`,
          extra: '',
          maxLength: 200,
          callback: true,
        },
        {
          type: 'upload_img_upload',
          label: '上传文件',
          name: 'paymentEvidence',
          extra: '',
          initialValue: [],
          upload_name: 'file',
          upload_url: 'v3/oss/admin/upload?source=setting',
          fileList: [],
          limit: 20,
          accept: ' .jpg, .jpeg, .png, .gif',
          callback: true,
          rules: [],
        },
      ]);

      const [standardTable, { reload, redoHeight, getPaginationRef }] = useTable({
        api: (arg) => getBillList({ ...arg }),  
        fetchSetting: {
          pageField: 'current',
          sizeField: 'pageSize',
          listField: 'data.list',
          totalField: 'data.pagination.total',
        },
        columns:columns.value ,
        actionColumn: {
          width: 220,
          title: '操作',
          dataIndex: 'action',
        },
        useSearchForm: true,
        formConfig: {
          schemas: [
            {
              field: 'storeName',
              component: 'Input',
              colProps: { span: 6, style: 'width:260px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                maxlength: 20,
                placeholder: '请输入店铺名称',
                size: 'default',
              },
              label: '店铺名称',
              labelWidth: 80,
            },
            {
              field: 'billSn',
              component: 'Input',
              colProps: { span: 6, style: 'width:260px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                placeholder: '请输入结算单号',
                size: 'default',
              },
              label: '结算单号',
              labelWidth: 80,
            },
            {
              field: 'state',
              component: 'Select',
              colProps: { span: 6, style: 'width:240px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                placeholder: '请选择结算状态',
                options: [
                  { key: 0, label: '全部', value: '' },
                  { key: 1, label: '待确认', value: '1' },
                  { key: 2, label: '待审核', value: '2' },
                  { key: 3, label: '待结算', value: '3' },
                  { key: 4, label: '结算完成', value: '4' },
                ],
                size: 'default',
              },
              defaultValue: '3',
              label: '结算状态',
              labelWidth: 80,
            },
            {
              field: '[startTime,endTime]',
              component: 'RangePicker',
              colProps: { span: 6, style: 'width:300px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                size: 'default',
                showTime: true,
              },
              label: '结算日',
              labelWidth: 80,
            },
          ],
        },
        bordered: true,
        striped: false,
        showIndexColumn: true,
        rowKey: rowKey,
        rowSelection: {
          type: 'checkbox',
          selectedRowKeys: checkedKeysForTable,
          onSelect: onSelect,
          onSelectAll: onSelectAll,
        },
        clickToRowSelect: false,
      });

      //表格的单行选中回调事件
      function onSelect(record, selected) {
        // 只允许选择待结算状态的账单
        if (selected && record.state !== 3) {
          failTip('只能选择待结算状态的账单');
          return;
        }
        
        if (selected) {
          checkedKeys.value = [...checkedKeys.value, record[rowKey.value]];
        } else {
          checkedKeys.value = checkedKeys.value.filter((item) => item != record[rowKey.value]);
        }
      }

      //表格的全选回调事件
      function onSelectAll(selected, selectedRows, changeRows) {
        // 过滤出待结算状态的账单
        const validChangeRows = changeRows.filter(row => row.state === 3);
        const changeIds = validChangeRows.map((item) => item[rowKey.value]);
        
        if (selected) {
          checkedKeys.value = [...checkedKeys.value, ...changeIds];
        } else {
          checkedKeys.value = checkedKeys.value.filter((item) => {
            return !changeIds.includes(item);
          });
        }
      }

      //表格点击回调事
      async function handleClick(item, type) {
        if (click_event.value) return;
        operate_type.value = type;
        
        if (type == 'view') {
          go(`/manage_bill/lists_to_detail?id=${item.billId}`);
        } else if (type == 'export') {
          let param = {};
          param.current = getPaginationRef().current;
          param.pageSize = getPaginationRef().pageSize;
          param.fileName = '结算单导出';
          if (checkedKeys.value.length > 0) {
            param.ids = checkedKeys.value.join(',');
          }
          click_event.value = true;
          operate_role(param);
        } else if (type=='trigger_bill'){
          const res = await triggerBill({});
          if (res.state == 200) {
            sucTip(res.msg)
            reload();
          }else{
            failTip(res.msg)
          }
        } else if (type == 'batch_pay') {
          // 获取选中的待结算账单
          if (checkedKeys.value.length === 0) {
            failTip('请选择待结算的账单进行批量打款');
            return;
          }

          try {
            // 直接传入数组，使用JSON格式
            const res = await batchConfirmPayment({
              billIds: Array.from(checkedKeys.value)
            });
            
            if (res.state === 200) {
              sucTip('批量打款成功');
              reload();
              // 清空选中状态
              checkedKeys.value = [];
            } else {
              failTip(res.msg || '批量打款失败');
            }
          } catch (error) {
            console.error('批量打款失败:', error);
            failTip(error.message || '批量打款失败，请稍后重试');
          }
        }
      }

      //操作
      const operate_role = async (params) => {
        let res = {};
        if (operate_type.value == 'export') {
          res = await exportBillList(params);
        }
        click_event.value = false;
        if (res.state == 200) {
          reload();
        }
      };

      const handleToggleTip = ()=> {
        redoHeight()
      }

      function handlePay(record) {
        currentBill.value = record;
        const tempData = JSON.parse(JSON.stringify(operate_data.value));
        tempData[0].initialValue = record.settleAmount || 0;
        tempData[0].originAmount = record.settleAmount || 0;
        tempData[1].content = record.billSn || '';
        content.value = tempData;
        visible.value = true;
      }

      function handleCancle() {
        visible.value = false;
        currentBill.value = null;
      }

      const handleConfirm = async (val) => {
        if (click_event.value) return;
        click_event.value = true;
        
        try {
          if (title.value === '批量打款确认') {
            // 获取选中的待结算账单
            // 通过checkedKeys获取选中的账单ID
            const billIds = checkedKeys.value;
            
            // 批量处理每个账单
            for (const billId of billIds) {
              const bill = standardTable.getDataSource().find(item => item.billId === billId);
              if (bill && bill.state === 3) {
                const params = {
                  billId: bill.billId,
                  payAmount: bill.settleAmount,
                  paymentRemark: val.paymentRemark,
                };
                
                if (val.paymentEvidence && val.paymentEvidence.length > 0 && val.paymentEvidence[0].response) {
                  params.paymentEvidence = val.paymentEvidence[0].response.data.path;
                }
                
                await confirmPaymentBill(params);
              }
            }
            
            sucTip('批量打款成功');
          } else {
            // 单个账单打款逻辑
            if (!currentBill.value) {
              failTip('账单信息不存在');
              return;
            }
            
            const params = {
              billId: currentBill.value.billId,
              payAmount: val.settleAmount,
              paymentRemark: val.paymentRemark,
            };

            if (val.paymentEvidence && val.paymentEvidence.length > 0 && val.paymentEvidence[0].response) {
              params.paymentEvidence = val.paymentEvidence[0].response.data.path;
            }

            const res = await confirmPaymentBill(params);
            if (res.state == 200) {
              sucTip(res.msg);
            } else {
              failTip(res.msg);
            }
          }
          
          visible.value = false;
          currentBill.value = null;
          reload();
        } catch (error) {
          failTip('操作失败，请重试');
          console.error('确认打款失败:', error);
        } finally {
          click_event.value = false;
        }
      };

      const handleAmountChange = (val) => {
        if (content.value && content.value.length > 0) {
          const contentItem = content.value[0];
          const originAmount = contentItem.originAmount || 0;
          contentItem.initialValue = val;
          contentItem.style = Number(val) > Number(originAmount) 
            ? { color: '#ff4d4f' } 
            : {};
        }
      };

      const handleCallback = (data) => {
        if (data.contentItem && data.contentItem.name === 'settleAmount') {
          handleAmountChange(data.val1);
        }
      };

      const handleExport = async (record) => {
        try {
          loading.value = true;
          const res = await exportBillDetail({ 
            billSns: record.billSn,
            fileName: `结算账单${record.billSn}`
          });
          if(res.state != undefined && res.state == 255){
            failTip(res.msg || '导出失败');
          }
        } catch (error) {
          failTip('导出失败');
        } finally {
          loading.value = false;
        }
      };

      // 处理店铺搜索
      const handleStoreInput = () => {
        showDropdown.value = true;
      };

      // 点击文档其他区域关闭下拉列表
      const handleDocumentClick = (e) => {
        const dropdown = document.querySelector('.search_wrapper');
        if (dropdown && !dropdown.contains(e.target)) {
          showDropdown.value = false;
        }
      };

      const selectStore = (option) => {
        storeKeyword.value = option.label;
        selectedStoreId.value = option.value;
        showDropdown.value = false;
      };

      // 处理快速结算
      const handleQuickBill = async () => {
        if (!selectedStoreId.value) {
          failTip('请先选择店铺');
          return;
        }

        try {
          quickBillLoading.value = true;
          const res = await quickBill({ storeId: selectedStoreId.value });
          if (res.state === 200) {
            sucTip(res.msg || '快速结算成功');
            selectedStoreId.value = null;
            reload();
          } else {
            failTip(res.msg || '快速结算失败');
          }
        } catch (error) {
          console.error('快速结算失败:', error);
          failTip(error.message || '快速结算失败，请稍后重试');
        } finally {
          quickBillLoading.value = false;
        }
      };

      onMounted(() => {
        fetchAllStores();
        // 添加全局点击事件
        document.addEventListener('click', handleDocumentClick);
      });

      // 组件卸载时移除事件监听
      onUnmounted(() => {
        document.removeEventListener('click', handleDocumentClick);
      });

      return {
        standardTable,
        go,
        operate_type,
        click_event,
        rowKey,
        checkedKeys,
        checkedKeysForTable,
        handleClick,
        handleToggleTip,
        columns,
        width,
        title,
        visible,
        content,
        confirmBtnLoading,
        operate_data,
        handlePay,
        handleCancle,
        handleConfirm,
        handleAmountChange,
        handleCallback,
        handleExport,
        selectedStoreId,
        storeOptions,
        quickBillLoading,
        handleStoreInput,
        handleQuickBill,
        storeKeyword,
        showDropdown,
        selectStore,
        allStores,
        fetchAllStores,
        handleDocumentClick,
      };
    },
  });
</script>
<style lang="less" scoped>
.common_page_view {
  display: inline-block;
  cursor: pointer;
  &:hover {
    color: #ff6a12;
  }
}

.common_page_edit {
  display: inline-block;
  min-width: 50px;
  cursor: pointer;
  color: #ff6a12;
  
  &:hover {
    color: #ff8534;
  }
}

// 店铺快速结算样式
.quick_bill_box {
  background: #fff;
  margin-bottom: 10px;
  padding: 15px 10px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.quick_bill_title {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  
  .title_line {
    width: 4px;
    height: 16px;
    background-color: #ff5908;
    margin-right: 8px;
  }
  
  span {
    font-weight: 700;
    color: #101010;
  }
}

.quick_bill_content {
  display: flex;
  align-items: center;
  
  .store_search {
    flex: 1;
    margin-right: 15px;
    display: flex;
    align-items: center;
  }
  
  .input_label {
    white-space: nowrap;
    font-size: 14px;
    color: #333;
    margin-right: 8px;
  }
  
  .search_wrapper {
    flex: 1;
    position: relative;
  }
  
  .store_input {
    width: 100%;
  }
  
  .search_dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    max-height: 200px;
    overflow-y: auto;
    background-color: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 0 0 4px 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 10;
  }
  
  .dropdown_item {
    padding: 8px 12px;
    cursor: pointer;
    
    &:hover {
      background-color: #f5f5f5;
    }
  }
  
  .quick_bill_btn {
    width: 120px;
    height: 32px;
    background-color: #ff7e28;
    border-color: #ff7e28;
    
    &:hover, &:focus {
      background-color: #ff7b38;
      border-color: #ff7b38;
    }
  }
}

:deep(.amount-input) {
  .ant-input-number-input {
    color: inherit;
  }
  
  .ant-input-number-group-addon {
    color: rgba(0, 0, 0, 0.65);
    background-color: #fafafa;
    border: 1px solid #d9d9d9;
    border-left: none;
  }
}
</style>
