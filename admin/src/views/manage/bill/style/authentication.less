.authentication_box{
  .authentication_info {
    height: 60vh;
  
    img {
      width: 120px;
      height: 120px;
    }
    div {
      color: #333333;
      font-size: 15px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      margin-top: 15px;
      margin-bottom: 30px;
    }
  }
  .auth_input{
    width: 219px;
  }

  .ant-form-item-explain{
    position: absolute;
    z-index: 2;
    top: -10px;
    right: 5%;
    min-height: 20px;
    background: #fff;
    font-size: 13px;
    text-align: left;
  }

  .full_acm_activity{
    width: 500px;
  }

  .authentication_info_height{
    max-height: calc(100vh - @header-height - 54px - 96px);
    overflow: auto;
  }
  .authentication_box_height{
    max-height: calc(100vh - @header-height - 54px - 52px);
    overflow: auto;
  }
  .ant-descriptions-item-label {
    width: 20%;
  }

  .ant-descriptions-item-content {
    width: 30%;
  }

  .invoice_info_box_img{
    overflow: hidden;
    width: 100px;
    height: 100px;
    background: #f8f8f8;
    margin-right: 10px;
    margin-bottom: 5px;
  }
  .prograss {
    .prograss_item {
      width: 24%;
      flex-shrink: 0;
      opacity: 0.9;
  
      &.active {
        opacity: 1;
      }
  
      .prograss_left {
        position: relative;
        img {
          width: 45px;
          height: 45px;
        }
        span {
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          color: #ffffff;
        }
      }
  
      .prograss_right {
        font-size: 15px;
        margin-left: 8px;
      }
    }
  }
  
  .prograss_authentication_info {
    height: 60vh;
  
    img {
      width: 90px;
      
      &.fail_img {
        width: 200px;
      }
    }
    div {
      color: #333333;
      font-size: 15px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      margin-top: 20px;
      margin-bottom: 30px;
  
      span {
        &:nth-child(2) {
          margin-top: 6px;
        }
      }
    }
  }
}