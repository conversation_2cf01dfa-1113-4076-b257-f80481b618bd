<template>
  <div class="bill_bank_list">
    <div class="operate_bg">
      <div class="toolbar" style="margin-bottom: 0">
        <div class="toolbar_btn" @click="addBank">
          <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#ff5908" />
          <span>{{ $sldComLanguage('添加银行卡') }}</span>
        </div>
      </div>
    </div>
    <Spin :spinning="initLoading">
      <div class="bank_list_height">
        <template v-if="bankList.length > 0">
          <div class="bank_list">
            <div
              v-for="(item, index) in bankList"
              :key="index"
              class="flex_column_between_start bank_item"
            >
              <div class="flex_row_between_start bank_top">
                <div class="bank_title">{{ item.depositBank }}</div>
                <div class="flex_row_end_center">
                  <span v-if="item.isDefault == 1">默认</span>
                  <Switch
                    :checked="item.isDefault == 1 ? true : false"
                    @change="(e) => changeCard(e, item.accountId)"
                  />
                </div>
              </div>
              <div class="flex_row_between_center bank_bottom">
                <span>{{ item.bankAccount ? item.bankAccount : item.corporateAccount }}</span>
                <span @click="unBind(item.accountId)">解绑</span>
              </div>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="flex_column_center_center" style="width: 100%; height: 180px">
            <Empty :image="Empty.PRESENTED_IMAGE_SIMPLE" />
          </div>
        </template>
      </div>
    </Spin>
    <SldModal
      :width="width"
      :title="title"
      :visible="modalVisible"
      :content="content"
      :confirmBtnLoading="confirmBtnLoading"
      :showFoot="true"
      @callback-event="handleCallback"
      @cancle-event="handleCancle"
      @confirm-event="handleConfirm"
    />
  </div>
</template>
<script setup>
import { getCurrentInstance, ref, onMounted } from 'vue';
import { Spin, Switch, Empty } from 'ant-design-vue';
import {
  getBankAccountListApi,
  getBankAccountIsDefaultApi,
  getBankAccountCompanyBindCardApi,
  getBankAccountUnBindApi,
} from '@/api/manage/withdraw';
import SldModal from '@/components/SldModal/index.vue';
import { sucTip, failTip } from '@/utils/utils';

const vm = getCurrentInstance();
const $sldComLanguage = vm?.appContext.config.globalProperties.$sldComLanguage;

const props = defineProps({
  detail: { type: Object, default: {} },
});

const width = ref(500);
const content = ref([]);
const confirmBtnLoading = ref(false);

const initLoading = ref(false);
const bankList = ref([]); //银行卡列表数据
const title = ref('');
const type = ref('add');
const modalVisible = ref(false);

const operate_id = ref('');

//获取数据列表
const get_list = async () => {
  initLoading.value = true;
  let res = await getBankAccountListApi({});
  initLoading.value = false;
  if (res.state == 200 && res.data.list) {
    bankList.value = res.data.list;
  }
};

const changeCard = async (e, accountId) => {
  initLoading.value = true;
  let res = await getBankAccountIsDefaultApi({ accountId: accountId, isDefault: e ? 1 : 0 });
  initLoading.value = false;
  if (res.state == 200) {
    bankList.value.forEach((item) => {
      if (item.accountId == accountId) {
        item.isDefault = e ? 1 : 0;
      } else {
        item.isDefault = 0;
      }
    });
  } else {
    failTip(res.msg);
  }
};

//弹窗取消事件
function handleCancle() {
  modalVisible.value = false;
  content.value = [];
  confirmBtnLoading.value = false;
  operate_id.value = '';
}

const addBank = () => {
  type.value = 'add';
  title.value = '添加银行卡';
  let obj = [];
  obj = [
    {
      type: 'input',
      label: `对公账户`,
      name: 'corporateAccount',
      placeholder: `请输入对公账户`,
      maxLength: 30,
      rules: [
        {
          required: true,
          whitespace: true,
          message: '请输入对公账户',
        },
      ],
    },
    {
      type: 'input',
      label: `开户银行`,
      name: 'depositBank',
      placeholder: `请输入开户银行`,
      maxLength: 30,
      rules: [
        {
          required: true,
          whitespace: true,
          message: '请输入开户银行',
        },
      ],
    },
    {
      type: 'input',
      label: `开户支行`,
      name: 'depositSubBank',
      placeholder: `请输入开户支行`,
      maxLength: 30,
      rules: [
        {
          required: true,
          whitespace: true,
          message: '请输入开户支行',
        },
      ],
    },
    {
      type: 'input',
      label: `支行行号`,
      name: 'subBankNo',
      placeholder: `请输入支行行号`,
      maxLength: 12,
      rules: [
        {
          required: true,
          whitespace: true,
          message: '请输入支行行号',
        },
      ],
    },
    {
      type: 'switch',
      label: '设为默认', //开关
      name: 'isDefault',
      initialValue: 0, //默认值
      unCheckedValue: 0, //非选中的值 不传返回值为false
      checkedValue: 1, //选中的值 不传返回值为true
    },
  ];
  content.value = obj;
  modalVisible.value = true;
};



// 弹窗点击确定
const handleConfirm = async (val) => {
  let params = {};
  confirmBtnLoading.value = false;
  if (type.value == 'unbind') {
    params.accountId = operate_id.value;
    confirmBtnLoading.value = true;
    let res = await getBankAccountUnBindApi(params);
    if (res.state == 200) {
      sucTip(res.msg);
      get_list();
      handleCancle();
    } else {
      failTip(res.msg);
      confirmBtnLoading.value = false;
    }
  } else {
    params.corporateAccount = val.corporateAccount;
    params.depositBank = val.depositBank;
    params.depositSubBank = val.depositSubBank;
    params.subBankNo = val.subBankNo;
    params.isDefault = val.isDefault != undefined ? val.isDefault : 0;
    confirmBtnLoading.value = true;
    let res = await getBankAccountCompanyBindCardApi(params);
    if (res.state == 200) {
      sucTip(res.msg);
      get_list();
      handleCancle();
    } else {
      failTip(res.msg);
      confirmBtnLoading.value = false;
    }
  }
};

// 解绑
const unBind = (id) => {
  operate_id.value = id;
  type.value = 'unbind';
  let obj = [
    {
      type: 'show_content',
      name: 'legalPerson',
      style: 'justify-content: center;margin: 40px 0 !important;',
      content: '是否确定解绑该银行卡？',
    },
  ];
  title.value = '提示';
  content.value = obj;
  modalVisible.value = true;
};

onMounted(() => {
  get_list();
});
</script>
<style lang="less">
@import './style/withdraw.less';
.bank_list_height {
  height: calc(100vh - @header-height - 54px - 52px - 92px);
  overflow: auto;
}
</style>
