<template>
  <div class="section_padding bill_detail">
    <div class="section_padding_back">
      <SldComHeader :type="1" :title="'结算详情'" :back="true" />
      <div style="width: 100%; height: 5px"></div>
      <div class="height_detail">
        <div class="flex_row_center_start progress">
          <template v-for="(item, index) in bill_progress_data" :key="index">
            <div class="flex_column_start_center item">
              <div class="flex_row_center_center top">
                <span class="left_line"></span>
                <img :src="item.img" />
                <span class="right_line"></span>
              </div>
              <span class="state">{{ item.state }}</span>
              <span class="time">{{ item.time }}</span>
            </div>
          </template>
        </div>
      <div v-if="bill_detail.state == 1" class="flex_column_start_center state_part">
        <span class="title">等待店铺确认</span>
      </div>
      <div v-else-if="bill_detail.state == 2" class="flex_column_start_center state_part">
        <span class="title">等待平台审核</span>
        <Popconfirm title="确认审核通过该结算单？" @confirm="handlePass">
          <span class="invoice_btn">审核结算单</span>
        </Popconfirm>
      </div>
      <div v-else-if="bill_detail.state == 3" class="flex_column_start_center state_part">
        <span class="title">等待平台结算</span>
        <div @click="handlePay" class="invoice_btn">确认打款</div>
      </div>
      <div v-else-if="bill_detail.state == 4" class="flex_column_start_center state_part">
        <span class="title">结算完成</span>
      </div>
      <div class="bill_detail_title">结算信息</div>
      <div class="bill_amount">
        <img class="bill_amount_img" :src="detail_total_icon" />
        <span class="bill_amount_total"
          >&nbsp;结算金额￥{{ Number(bill_detail.settleAmount || 0).toFixed(2) }} = &nbsp;</span
        >
        <span class="bill_amount_detail">
          订单金额￥{{ Number(bill_detail.orderAmount || 0).toFixed(2) }}  - 支付平台手续费￥{{ Number(bill_detail.payFeeAmount || 0).toFixed(2) }} - 平台佣金￥{{ Number(bill_detail.commission || 0).toFixed(2) }} +
          退还佣金￥{{ Number(bill_detail.refundCommission || 0).toFixed(2) }} - 退单金额￥{{
            Number(bill_detail.refundAmount || 0).toFixed(2)
          }}
          + 平台优惠券￥{{ Number(bill_detail.platformVoucherAmount || 0).toFixed(2) }} + 积分抵扣金额￥{{
            Number(bill_detail.integralCashAmount || 0).toFixed(2)
          }}
          - 定金赔偿金额￥{{ Number(bill_detail.compensationAmount || 0).toFixed(2) }} - 供应商结算金额￥{{
            Number(bill_detail.thirdOrderAmount || 0).toFixed(2)
          }}
          <!-- spreader-1-start -->
          <template v-if="spreaderFlag">
            - 推手佣金￥{{ Number(bill_detail.attachCommission || 0).toFixed(2) }}</template
          >
          <!-- spreader-1-end -->
          
        </span>
      </div>
      <Description
        v-if="bill_detail.state == 4"
        size="middle"
        title="结算凭证"
        :bordered="true"
        :column="1"
        :data="bill_detail"
        :schema="paymentSchema"
      />
      <Description
        size="middle"
        title="结算信息"
        :bordered="true"
        :column="2"
        :data="bill_detail"
        :schema="infoSchema"
      />
      <Description
        v-if="bill_detail.state != 1"
        size="middle"
        title="结算账号信息"
        :bordered="true"
        :column="2"
        :data="bill_detail"
        :schema="bill_detail.accountType == 1 ? bankSchema : aliSchema"
      />
      <SldComHeader
        :type="1"
        :title="'结算订单信息'"
        :tipBtn="'导出结算单'"
        :tipBtnIcon="'iconziyuan23'"
        @tip-btn-click="handleExport"
      />
      <BasicTable @register="standardTable" style="padding: 3px">
        <template #headerCell="{ column }">
          <template v-if="column.key == 'compensationAmount'">
            <div class="table_title">
              <span class="table_title_xing">*</span>
              {{ column.customTitle }}
              <span class="table_title_extra">
                <Tooltip placement="bottom">
                  <template #title>
                    对于定金预售活动，因商家问题导致无法发货，对会员赔偿的金额
                  </template>
                  <AliSvgIcon iconName="iconwen" width="14px" height="14px" fillColor="#bfbbba" />
                </Tooltip>
              </span>
            </div>
          </template>
          <template v-else>
            {{ column.customTitle }}
          </template>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.key == 'action'">
            <span class="common_page_edit" @click="checkOrer(record.orderSn)">查看</span>
          </template>
          <template v-else-if="column.key">
            {{ text !== undefined && text !== null && text != '' ? text : '--' }}
          </template>
        </template>
      </BasicTable>
      </div>
      <SldModal
        :width="width"
        :title="title"
        :visible="visible"
        :content="content"
        :confirmBtnLoading="confirmBtnLoading"
        :showFoot="true"
        @cancle-event="handleCancle"
        @confirm-event="handleConfirm"
        @callback-event="handleCallback"
      />
    </div>
  </div>
</template>
<script>
  import { defineComponent, ref, h, onMounted } from 'vue';
  import { Tooltip, Popconfirm } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { Description } from '/@/components/Description/index';
  import { BasicTable, useTable } from '/@/components/Table';
  import { getBillDetail, approvedBill, confirmPaymentBill, exportBillDetail } from '/@/api/manage/manage';
  import { useGo } from '/@/hooks/web/usePage';
  import { useRoute } from 'vue-router';
  import SldModal from '@/components/SldModal/index.vue';
  import { sucTip, failTip, detail_total_icon } from '/@/utils/utils';
  export default defineComponent({
    components: {
      SldComHeader,
      Description,
      BasicTable,
      Tooltip,
      Popconfirm,
      SldModal,
    },
    setup() {
      const go = useGo();
      const route = useRoute();
      const bill_detail = ref({
        state: 0,
        settleAmount: 0,
        billSn: '',
        orderAmount: 0,
        commission: 0,
        refundCommission: 0,
        refundAmount: 0,
        platformVoucherAmount: 0,
        integralCashAmount: 0,
        compensationAmount: 0,
        thirdOrderAmount: 0,
        attachCommission: 0,
        accountType: 1,
      });
      const bill_progress_data = ref([
        {
          code: 'createBill',
          state: '生成结算单',
          time: '',
          state_code: 1,
          cur_state: 'cur',
          img:''
        },
        {
          code: 'storeConfirm',
          state: '店铺确认',
          time: '',
          state_code: 2,
          cur_state: 'no',
          img:''
        },
        {
          code: 'systemCheck',
          state: '平台审核',
          time: '',
          state_code: 3,
          img:'',
          cur_state: 'no',
        },
        {
          code: 'finish',
          state: '结算完成',
          time: '',
          state_code: 4,
          img:"",
          cur_state: 'no',
        },
      ]);
      //spreader-2-start
      const spreaderFlag = ref(false);
      //spreader-2-end
      const paymentSchema = [
        {
          field: 'paymentRemark',
          label: '打款备注',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'paymentEvidence',
          label: '结算凭证',
          render: (val) => {
            return h('div', { class: 'form_list_img_item' }, [
              h('img', { src: val, class: 'form_list_img_item_img' }),
              h('div', { class: 'form_list_img_item_right' }, [
                h('div', { class: 'form_list_img_item_pre' }, [
                  h('div', { class: 'form_list_img_item_pre_arrow' }),
                  h('img', { src: val, class: 'form_list_img_item_pre_img' }),
                ]),
              ]),
            ]);
          },
        },
      ];
      const infoSchema = [
        {
          field: 'billSn',
          label: '结算单号',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'startTime',
          label: '结算起止时间',
          render: function (val, data) {
            return (
              (val ? val : data.endTime ? '' : '--') +
              (data.endTime ? (val ? ' ~ ' : '') + data.endTime : '')
            );
          },
        },
        {
          field: 'storeName',
          label: '店铺名称',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'contactName',
          label: '店铺联系人',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'contactPhone',
          label: '店铺联系电话',
          render: function (val) {
            return val ? val : '--';
          },
        },
      ];
      const bankSchema = [
        {
          field: 'bankAccountName',
          label: '银行开户名',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'bankAccountNumber',
          label: '公司银行账号',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'bankBranch',
          label: '开户银行',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'addressAll',
          label: '开户行所在地',
          render: function (val) {
            return val ? val : '--';
          },
        },
      ];
      const aliSchema = [
        {
          field: 'alipayAccount',
          label: '支付宝账号',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'alipayName',
          label: '支付宝姓名',
          render: function (val) {
            return val ? val : '--';
          },
        },
      ];

      const rowKey = ref('bindId');
      const columns = ref([
        {
          title: '订单号',
          dataIndex: 'orderSn',
          width: 150,
        },
        {
          title: '商品名称',
          dataIndex: 'goodsNames',
          width: 150,
        },
        {
          title: '商品ID',
          dataIndex: 'goodsIds',
          width: 150,
        },
        {
          title: '商品数',
          dataIndex: 'productNum',
          width: 100,
        },
        {
          title: '订单金额(元)',
          dataIndex: 'orderAmount',
          width: 120,
          customRender: ({ text }) => Number(text || 0).toFixed(2)
        },
        {
          title: '支付平台手续费(元)',
          dataIndex: 'payFeeAmount',
          width: 150,
          helpMessage: '微信/支付宝等支付平台手续费',
          customRender: ({ text }) => Number(text || 0).toFixed(2)
        },
        {
          title: '佣金(元)',
          dataIndex: 'commission',
          width: 120,
          customRender: ({ text }) => Number(text || 0).toFixed(2)
        },
        {
          title: '退还佣金(元)',
          dataIndex: 'refundCommission',
          width: 120,
          customRender: ({ text }) => Number(text || 0).toFixed(2)
        },
        {
          title: '退单金额(元)',
          dataIndex: 'refundAmount',
          width: 120,
          customRender: ({ text }) => Number(text || 0).toFixed(2)
        },
        {
          title: '平台优惠券',
          dataIndex: 'platformVoucherAmount',
          width: 120,
          customRender: ({ text }) => Number(text || 0).toFixed(2)
        },
        {
          title: '积分抵扣金额',
          dataIndex: 'integralCashAmount',
          width: 120,
          customRender: ({ text }) => Number(text || 0).toFixed(2)
        },
        {
          title: '定金赔偿金额(元)',
          dataIndex: 'compensationAmount',
          width: 150,
          customRender: ({ text }) => Number(text || 0).toFixed(2)
        },
        //spreader-3-start
        {
          title: '推手佣金(元)',
          dataIndex: 'attachCommission',
          width: 120,
          customRender: ({ text }) => Number(text || 0).toFixed(2)
        },
        //spreader-3-end
        {
          title: '供应商结算金额(元)',
          dataIndex: 'thirdOrderAmount',
          width: 140,
          customRender: ({ text }) => Number(text || 0).toFixed(2)
        },
         
        {
          title: '下单日期',
          dataIndex: 'createTime',
          width: 160,
        },
        {
          title: '完成日期',
          dataIndex: 'finishTime',
          width: 160,
        },
        {
          title: '操作',
          dataIndex: 'action',
          width: 100,
          fixed: 'right'
        },
      ]);
      const dataSource = ref([]);
      const [standardTable] = useTable({
        columns: columns.value,
        dataSource: dataSource,
        pagination: false,
        scroll: { x: 2300 },
        bordered: true,
        striped: false,
        showIndexColumn: true,
        canResize: false,
      });
      const click_event = ref(false);

      const width = ref(550);
      const title = ref('确认打款');
      const visible = ref(false);
      const content = ref([]);
      const confirmBtnLoading = ref(false);
      const operate_data = ref([
        {
          type: 'inputnum',
          label: '结算金额',
          name: 'settleAmount',
          initialValue: '',
          precision: 2,
          min: 0,
          placeholder: '请输入结算金额',
          rules: [{ required: true, message: '请输入结算金额' }],
          callback: true,
          originAmount: 0,
          style: {},
          className: 'amount-input',
          addonAfter: '元',
          stringMode: true,
          maxLength: 16
        },
        {
          type: 'show_content',
          label: '结算单号',
          name: 'billSn',
          content: '',
        },
        {
          type: 'textarea',
          label: `打款备注`,
          name: 'paymentRemark',
          placeholder: `请输入汇款单号、支付方式等付款凭证信息，最多输入200字`,
          extra: '',
          maxLength: 200,
          callback: true,
        },
        {
          type: 'upload_img_upload',
          label: '上传文件',
          name: 'paymentEvidence',
          extra: '',
          initialValue: [],
          upload_name: 'file',
          upload_url: 'v3/oss/admin/upload?source=setting',
          fileList: [],
          limit: 20,
          accept: ' .jpg, .jpeg, .png, .gif',
          callback: true,
          rules: [],
        },
      ]);

      const loading = ref(false);

      onMounted(() => {
        get_bill_detail();
      });

      // 生成本地路径
      const newImg = (img) => {
        return new URL(img, import.meta.url).href;
      };

      //获取结算详情
      const get_bill_detail = async () => {
        const res = await getBillDetail({ billId: route.query.id });
        if (res.state == 200) {
          bill_detail.value = res.data;
          for (let pro in bill_progress_data.value) {
            if (pro < res.data.logList.length) {
              bill_progress_data.value[pro].time = res.data.logList[pro].createTime;
            }
            if (res.data.state < bill_progress_data.value[pro].state_code) {
              bill_progress_data.value[pro].cur_state = 'no';
              if(bill_progress_data.value[pro].state_code==1){
                bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/1_no.png', import.meta.url).href;
              }else if(bill_progress_data.value[pro].state_code==2){
                bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/2_no.png', import.meta.url).href;
              }else if(bill_progress_data.value[pro].state_code==3){
                bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/3_no.png', import.meta.url).href;
              }else if(bill_progress_data.value[pro].state_code==4){
                bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/4_no.png', import.meta.url).href;
              }

            } else if (res.data.state == bill_progress_data.value[pro].state_code) {
              bill_progress_data.value[pro].cur_state = 'cur';
              if(bill_progress_data.value[pro].state_code==1){
                bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/1_cur.png', import.meta.url).href;
              }else if(bill_progress_data.value[pro].state_code==2){
                bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/2_cur.png', import.meta.url).href;
              }else if(bill_progress_data.value[pro].state_code==3){
                bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/3_cur.png', import.meta.url).href;
              }else if(bill_progress_data.value[pro].state_code==4){
                bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/4_cur.png', import.meta.url).href;
              }

            } else {
              bill_progress_data.value[pro].cur_state = 'pass';
              if(bill_progress_data.value[pro].state_code==1){
                bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/1_pass.png', import.meta.url).href;
              }else if(bill_progress_data.value[pro].state_code==2){
                bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/2_pass.png', import.meta.url).href;
              }else if(bill_progress_data.value[pro].state_code==3){
                bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/3_pass.png', import.meta.url).href;
              }else if(bill_progress_data.value[pro].state_code==4){
                bill_progress_data.value[pro].img = new URL('/@/assets/images/bill/4_pass.png', import.meta.url).href;
              }

            }
          }
          dataSource.value = res.data.orderList;
        } else {
          failTip(res.msg);
        }
      };

      function checkOrer(orderSn) {
        go(`/manage_order/order_lists_to_detail?orderSn=${orderSn}`);
      }

      //审核结算单
      const handlePass = async () => {
        if (click_event.value) return;
        click_event.value = true;
        const res = await approvedBill({ billId: route.query.id });
        click_event.value = false;
        if (res.state == 200) {
          sucTip(res.msg);
          get_bill_detail();
        } else {
          failTip(res.msg);
        }
      };

      //确认打款
      function handlePay() {
        const tempData = JSON.parse(JSON.stringify(operate_data.value));
        tempData[0].initialValue = bill_detail.value.settleAmount || 0;
        tempData[0].originAmount = bill_detail.value.settleAmount || 0;
        tempData[1].content = bill_detail.value.billSn || '';
        content.value = tempData;
        visible.value = true;
      }

      function handleCancle() {
        visible.value = false;
      }

      const handleConfirm = async (val) => {
        if (click_event.value) return;
        click_event.value = true;
        
        try {
          // 修改参数名称
          const params = {
            billId: route.query.id,
            payAmount: val.settleAmount, // 将 settleAmount 改为 payAmount
            paymentRemark: val.paymentRemark,
          };

          // 如果上传了文件，则添加文件路径
          if (val.paymentEvidence && val.paymentEvidence.length > 0 && val.paymentEvidence[0].response) {
            params.paymentEvidence = val.paymentEvidence[0].response.data.path;
          }

          const res = await confirmPaymentBill(params);
          click_event.value = false;
          
          if (res.state == 200) {
            sucTip(res.msg);
            visible.value = false;
            get_bill_detail();
          } else {
            failTip(res.msg);
          }
        } catch (error) {
          click_event.value = false;
          failTip('操作失败，请重试');
          console.error('确认打款失败:', error);
        }
      };

      const handleAmountChange = (val) => {
        const originAmount = content.value[0].originAmount;
        content.value[0].initialValue = val;
        content.value[0].style = Number(val) > Number(originAmount) 
          ? { color: '#ff4d4f' } 
          : {};
      };

      const handleCallback = (data) => {
        if (data.contentItem.name === 'settleAmount') {
          handleAmountChange(data.val1);
        }
      };

      // 导出结算单
      const handleExport = async () => {
        try {
          loading.value = true;
          const res = await exportBillDetail({ 
            billSns: bill_detail.value.billSn,
            fileName: `结算账单${bill_detail.value.billSn}`
          });
          if(res.state != undefined && res.state == 255){
            failTip(res.msg || '导出失败');
          }
        } catch (error) {
          failTip('导出失败');
        } finally {
          loading.value = false;
        }
      };

      return {
        newImg,
        bill_detail,
        bill_progress_data,
        //spreader-4-start
        spreaderFlag,
        //spreader-4-end
        paymentSchema,
        infoSchema,
        bankSchema,
        aliSchema,
        rowKey,
        standardTable,
        click_event,
        checkOrer,
        handlePass,
        handlePay,
        width,
        title,
        visible,
        content,
        confirmBtnLoading,
        operate_data,
        handleCancle,
        handleConfirm,
        detail_total_icon,
        handleAmountChange,
        handleCallback,
        handleExport,
        loading,
      };
    },
  });
</script>
<style lang="less" scoped>
  .bill_detail {
    .slodon-basic-table{
      height: auto !important;
    }

    .bill_detail_title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 32px;
      margin-bottom: 5px;
      padding-left: 14px;
      color: rgb(0 0 0 / 85%);
      font-size: 14px;
      font-weight: 500;
      line-height: 24px;
      cursor: default;
    }

    .flex_row_center_between {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .export_btn {
      cursor: pointer;
      color: #ff6a12;
      &:hover {
        opacity: 0.8;
      }
    }

    .bill_amount {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      height: 60px;
      margin: 10px;
      border-radius: 6px;
      background: #fff;
      box-shadow: 0 0 8px 0 rgb(153 153 153 / 5%), 0 0 8px 0 rgb(153 153 153 / 5%);
      font-size: 16px;

      .bill_amount_img {
        width: 36px;
        height: 36px;
        margin-right: 15px;
        margin-left: 20px;
      }

      .bill_amount_total {
        color: #ff2b4e;
        white-space: nowrap;
      }

      .bill_amount_detail {
        color: #333;
      }
    }

    .table_title {
      display: flex;
      align-items: center;
      justify-content: center;

      .table_title_xing {
        margin-right: 4px;
        color: #f00;
      }

      .table_title_extra {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 4px;
        cursor: pointer;
      }
    }

    .progress {
      margin-top: 50px;

      .item {
        .top {
          position: relative;
          width: 215px;

          img {
            width: 75px;
            height: 75px;
          }

          .left_line,
          .right_line {
            content: '';
            position: absolute;
            top: 50%;
            width: 50px;
            height: 0;
            border-top: 1px solid rgb(255 109 31 / 30%);
          }

          .left_line {
            left: 0;
          }

          .right_line {
            right: 0;
          }
        }

        .state {
          margin-top: 10px;
          font-size: 14px;
        }

        .time {
          font-size: 14px;
        }
      }

      .item.cur {
        .state {
          color: #fb6f1e;
        }

        .time {
          color: rgb(251 111 30 / 50%);
        }

        .left_line,
        .right_line {
          border-color: #fb6f1e;
        }
      }

      .item.no {
        .state {
          color: #999;
        }

        .left_line,
        .right_line {
          border-color: #eee;
        }
      }

      .item.pass {
        .state {
          color: rgb(251 111 30 / 50%);
        }

        .time {
          color: rgb(251 111 30 / 30%);
        }

        .left_line,
        .right_line {
          border-color: rgb(251 111 30 / 30%);
        }
      }
    }

    .state_part {
      margin-top: 50px;
      margin-bottom: 40px;

      .title {
        color: #333;
        font-size: 26px;
      }

      .tip {
        color: #999;
        font-size: 14px;
      }
    }

    .agree_btn {
      width: 120px;
      height: 36px;
      margin-top: 15px;
      border: 1px solid #fb6f1e;
      border-radius: 3px;
      color: #fb6f1e;
      font-size: 14px;
      line-height: 36px;
      text-align: center;
    }

    .invoice_btn {
      width: 120px;
      height: 36px;
      margin-top: 15px;
      border-radius: 3px;
      background: #fb6f1e;
      color: rgb(255 255 255 / 100%);
      line-height: 36px;
      text-align: center;
      cursor: pointer;
    }
  }

  .bill-info {
    margin-bottom: 16px;
    padding: 12px 16px;
    background: #f5f5f5;
    border-radius: 4px;

    .bill-info-item {
      display: flex;
      align-items: center;
      line-height: 32px;

      .label {
        color: #666;
        min-width: 80px;
      }

      .value {
        color: #333;
        font-weight: 500;
      }
    }
  }

  :deep(.amount-input) {
    .ant-input-number {
      width: 100%;
    }

    .ant-input-number-input {
      text-align: left;
      padding-right: 24px;
      color: inherit;
    }

    .ant-input-number-group-addon {
      color: rgba(0, 0, 0, 0.65);
      background-color: #fafafa;
      border: 1px solid #d9d9d9;
      border-left: none;
    }
  }
</style>
