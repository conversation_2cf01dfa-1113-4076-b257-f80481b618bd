<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader
        :type="2"
        :title="'入驻店铺管理'"
        :showTipBtn="false"
        :tipData="tabIndex == 4 ? tipData : []"
      />
      <a-tabs v-model:activeKey="tabIndex" type="card" @change="handleChange">
        <a-tab-pane :key="1" tab="入驻店铺列表" />
        <a-tab-pane :key="2" tab="临效期店铺" />
        <a-tab-pane :key="3" tab="入驻审核" />
        <a-tab-pane :key="4" tab="续签管理" />
        <a-tab-pane :key="5" tab="经营类目审核" />
      </a-tabs>
      <div class="section_padding_tab_top"></div>
      <div :style="{ display: tabIndex == 1 ? 'block' : 'none' }">
        <BasicTable @register="standardTable" style="padding: 0">
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.key == 'action'">
              <div style="display: flex; flex-wrap: wrap; justify-content: center">
                <span class="common_page_edit" @click="handleClick(record, 'view')">查看</span>
                <span class="common_page_edit" @click="handleClick(record, 'edit')">编辑</span>
                <span class="common_page_edit" @click="handleClick(record, 'setPwd')">重置密码</span>
                <span class="common_page_edit" @click="handleClick(record, 'setPay')"
                  >设置结算周期</span
                >
                <Popconfirm
                  v-if="record.state != 1"
                  title="删除后不可恢复，是否确定删除？"
                  @confirm="handleClick(record, 'del')"
                >
                  <span class="common_page_edit">删除</span>
                </Popconfirm>
              </div>
            </template>
            <template v-else-if="column.key == 'state'">
              <Switch
                :checked="text == '1' ? true : false"
                checked-children="开启"
                un-checked-children="关闭"
                @change="(e) => handleClick({ state: e ? 1 : 2, storeId: record.storeId }, 'switch')"
              />
            </template>
            <template v-else-if="column.key">
              {{ text !== undefined && text !== null ? text : '--' }}
            </template>
          </template>
        </BasicTable>
      </div>
      <div :style="{ display: tabIndex == 2 ? 'block' : 'none' }">
        <BasicTable @register="nearStandardTable" style="padding: 0">
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.key == 'action'">
              <div style="display: flex; flex-wrap: wrap; justify-content: center">
                <span class="common_page_edit" @click="handleClick(record, 'view')">查看</span>
                <span class="common_page_edit" @click="handleClick(record, 'edit')">编辑</span>
                <span class="common_page_edit" @click="handleClick(record, 'setPay')"
                  >设置结算周期</span
                >
                <Popconfirm
                  v-if="record.state != 1"
                  title="删除后不可恢复，是否确定删除？"
                  @confirm="handleClick(record, 'del')"
                >
                  <span class="common_page_edit">删除</span>
                </Popconfirm>
              </div>
            </template>
            <template v-else-if="column.key == 'state'">
              <Switch
                :checked="text == '1' ? true : false"
                checked-children="开启"
                un-checked-children="关闭"
                @change="(e) => handleClick({ state: e ? 1 : 2, storeId: record.storeId }, 'switch')"
              />
            </template>
            <template v-else-if="column.key">
              {{ text !== undefined && text !== null ? text : '--' }}
            </template>
          </template>
        </BasicTable>
      </div>
      <div :style="{ display: tabIndex == 3 ? 'block' : 'none' }">
        <BasicTable @register="auditStandardTable" style="padding: 0">
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.key == 'action'">
              <span
                v-if="record.state != 1"
                class="common_page_edit"
                @click="handleClick(record, 'view')"
                >查看</span
              >
              <span v-else class="common_page_edit" @click="handleClick(record, 'audit')">审核</span>
            </template>
            <template v-else-if="column.key">
              {{ text ? text : '--' }}
            </template>
          </template>
        </BasicTable>
      </div>
      <div :style="{ display: tabIndex == 4 ? 'block' : 'none' }">
        <BasicTable @register="renewStandardTable" style="padding: 0">
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.key == 'action'">
              <Popconfirm
                title="删除后不可恢复，是否确定删除？"
                @confirm="handleClick(record, 'del')"
              >
                <span class="common_page_edit" style="margin-right: 0; padding-right: 0">删除</span>
              </Popconfirm>
            </template>
            <template v-else-if="column.key == 'startTime'">
              <div class="flex_column_center_center">
                <span>{{ text }}</span>
                <span>~</span>
                <span>{{ record.endTime }}</span>
              </div>
            </template>
            <template v-else-if="column.key">
              {{ (text !== undefined && text !== null && text !== '') ? text : '--' }}
            </template>
          </template>
        </BasicTable>
      </div>
      <div :style="{ display: tabIndex == 5 ? 'block' : 'none' }">
        <BasicTable @register="categoryStandardTable" style="padding: 0">
          <template #tableTitle>
            <div class="toolbar">
              <Popconfirm
                v-if="checkedKeys.length > 0"
                title="确认审核通过选中的类目吗？"
                @confirm="handleClick(null, 'pass')"
              >
                <div class="toolbar_btn">
                  <AliSvgIcon
                    iconName="iconshenhetongguo1"
                    width="15px"
                    height="15px"
                    fillColor="#0fb39a"
                  />
                  <span>审核通过</span>
                </div>
              </Popconfirm>
              <div v-else class="toolbar_btn" @click="handleClick(null, 'pass')">
                <AliSvgIcon
                  iconName="iconshenhetongguo1"
                  width="15px"
                  height="15px"
                  fillColor="#0fb39a"
                />
                <span>审核通过</span>
              </div>
              <div class="toolbar_btn" @click="handleClick(null, 'refuse')">
                <AliSvgIcon
                  iconName="iconshenhejujue1"
                  width="15px"
                  height="15px"
                  fillColor="#ff5908"
                />
                <span>审核拒绝</span>
              </div>
            </div>
          </template>
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.key == 'action'">
              <template v-if="record.state == 1">
                <Popconfirm title="确认审核通过该条申请吗？" @confirm="handleClick(record, 'pass')">
                  <span class="common_page_edit">审核通过</span>
                </Popconfirm>
                <span class="common_page_edit" @click="handleClick(record, 'refuse')">审核拒绝</span>
              </template>
              <template v-else>--</template>
            </template>
            <template v-else-if="column.key == 'scaling'">
              {{ text ? text : 0 }}
            </template>
            <template v-else-if="column.key == 'imageUrl'">
              <img :src="text" style="max-height: 30px" />
            </template>
            <template v-else-if="column.key">
              {{ text ? text : '--' }}
            </template>
          </template>
        </BasicTable>
      </div>
      <SldModal
        :width="width"
        :title="title"
        :visible="visible"
        :content="content"
        :confirmBtnLoading="confirmBtnLoading"
        :showFoot="true"
        @callback-event="handelSelect"
        @radio-change-event="handleRadio"
        @cancle-event="handleCancle"
        @confirm-event="handleConfirm"
      />
    </div>
  </div>
</template>
<script>
  import { defineComponent, ref, unref, onMounted } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { Tabs, Popconfirm, Switch } from 'ant-design-vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import {
    getStoreGrade,
    getStoreList,
    getExpiryStoreList,
    getAuditStoreList,
    getRenewStoreList,
    getAuditCateList,
    resetStorePwd,
    setStorePayday,
    lockOwn,
    delOwn,
    delStoreRenew,
    auditStoreCate,
  } from '/@/api/manage/manage';
  // dev_supplier_o2o-start
  import {
    getDictDataApi
  } from '/@/api/common/common';
  // dev_supplier_o2o-end
  import SldModal from '@/components/SldModal/index.vue';
  import { useGo } from '/@/hooks/web/usePage';
  import { useRoute } from 'vue-router';
  import { base64Encrypt, failTip, month_to_num, sucTip, week_to_num } from '/@/utils/utils';
  import { validatorMemPwd } from '/@/utils/validate';

  export default defineComponent({
    components: {
      SldComHeader,
      [Tabs.name]: Tabs,
      [Tabs.TabPane.name]: Tabs.TabPane,
      BasicTable,
      Popconfirm,
      Switch,
      SldModal,
    },
    setup() {
      const go = useGo();
      const tabIndex = ref(1); //tab下标
      const tipData = ref(['店铺等级发生变化时，续签的店铺等级会在新的签约有效期内生效']);
      const route = useRoute();
      const checkedKeys = ref([]); //表格的键值声明，非必填，主要用于勾选功能中的数据处理
      const rowKey = ref('brandId'); //表格开启选择功能时，需要声明选中数据变量

      const shop_type_list = ref([])

      const columns = [
        {
          title: '店铺名称',
          dataIndex: 'storeName',
          width: 100,
        },
        {
          title: '店主账号',
          dataIndex: 'vendorName',
          width: 100,
        },
        {
          title: '联系电话',
          dataIndex: 'contactPhone',
          width: 90,
        },
        // dev_supplier_o2o-start
        {
          title: '店铺类型',
          dataIndex: 'shopType', 
          width: 80,
          customRender: ({ text }) => {
            let val =  
            // dev_supplier-start
            text == 3 ? '供应商店铺' : 
            // dev_supplier-end
            
            '普通店铺';
            return val
          },
        },
        // dev_supplier_o2o-end
        {
          title: '店铺等级',
          dataIndex: 'storeGradeName',
          width: 80,
        },
        {
          title: '开店时间',
          dataIndex: 'createTime',
          width: 150,
        },
        {
          title: '有效期',
          dataIndex: 'storeExpireTime',
          width: 150,
        },
        {
          title: '店铺状态',
          dataIndex: 'state',
          width: 80,
        },
      ];
      const schemas = [
        {
          field: 'storeName',
          component: 'Input',
          colProps: { span: 6, style: 'width:260px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            maxlength: 10,
            placeholder: '请输入店铺名称',
            size: 'default',
          },
          label: '店铺名称',
          labelWidth: 80,
        },
        {
          field: 'vendorName',
          component: 'Input',
          colProps: { span: 6, style: 'width:260px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            maxlength: 20,
            placeholder: '请输入店主账号',
            size: 'default',
          },
          label: '店主账号',
          labelWidth: 80,
        },
        // dev_supplier_o2o-start
        {
          field: 'shopType',
          component: 'Select',
          colProps: { span: 6, style: 'width:240px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            placeholder: '请选择店铺类型',
            options: [
              { key: 0, label: '全部', value: '' },
              { key: 1, label: '普通店铺', value: '1' },
              // dev_supplier-start
              { key: 3, label: '供应商店铺', value: '3' },
              // dev_supplier-end
              
            ],
            size: 'default',
          },
          label: '店铺类型',
          labelWidth: 80,
        },
        // dev_supplier_o2o-end
        {
          field: 'storeGradeId',
          component: 'Select',
          colProps: { span: 6, style: 'width:240px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            placeholder: '请选择店铺等级',
            options: [],
            size: 'default',
          },
          label: '店铺等级',
          labelWidth: 80,
        },
      ];

      const [standardTable, { reload: reload }] = useTable({
        api: (arg) => getStoreList({ ...arg, isOwnStore: 2 }),
        fetchSetting: {
          pageField: 'current',
          sizeField: 'pageSize',
          listField: 'data.list',
          totalField: 'data.pagination.total',
        },
        columns: columns,
        actionColumn: {
          width: 120,
          title: '操作',
          dataIndex: 'action',
        },
        useSearchForm: true,
        formConfig: {
          schemas: [
            ...schemas,
            {
              field: 'state',
              component: 'Select',
              colProps: { span: 6, style: 'width:240px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                placeholder: '请选择店铺状态',
                options: [
                  { key: 0, label: '全部', value: '' },
                  { key: 1, label: '开启', value: '1' },
                  { key: 2, label: '关闭', value: '2' },
                ],
                size: 'default',
              },
              label: '店铺状态',
              labelWidth: 80,
            },
          ],
        },
        bordered: true,
        striped: false,
        showIndexColumn: true,
      });
      const [nearStandardTable, { reload: reloadNear }] = useTable({
        api: (arg) => getExpiryStoreList({ ...arg }),
        fetchSetting: {
          pageField: 'current',
          sizeField: 'pageSize',
          listField: 'data.list',
          totalField: 'data.pagination.total',
        },
        columns: columns,
        actionColumn: {
          width: 120,
          title: '操作',
          dataIndex: 'action',
        },
        useSearchForm: true,
        formConfig: {
          schemas: [
            ...schemas,
            {
              field: 'state',
              component: 'Select',
              colProps: { span: 6, style: 'width:240px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                placeholder: '请选择店铺状态',
                options: [
                  { key: 0, label: '全部', value: '' },
                  { key: 1, label: '开启', value: '1' },
                  { key: 2, label: '关闭', value: '2' },
                ],
                size: 'default',
              },
              label: '店铺状态',
              labelWidth: 80,
            },
          ],
        },
        bordered: true,
        striped: false,
        showIndexColumn: true,
      });
      const [auditStandardTable, { reload: reloadAudit }] = useTable({
        api: (arg) => getAuditStoreList({ ...arg }),
        fetchSetting: {
          pageField: 'current',
          sizeField: 'pageSize',
          listField: 'data.list',
          totalField: 'data.pagination.total',
        },
        columns: [
          {
            title: '店铺名称',
            dataIndex: 'storeName',
            width: 100,
          },
          {
            title: '店主账号',
            dataIndex: 'vendorName',
            width: 100,
          },
          {
            title: '联系人',
            dataIndex: 'contactName',
            width: 80,
          },
          {
            title: '联系电话',
            dataIndex: 'contactPhone',
            width: 80,
          },
          // dev_supplier_o2o-start
          {
            title: '店铺类型',
            dataIndex: 'shopType', 
            width: 80,
            customRender: ({ text }) => {
              let val =  
              // dev_supplier-start
              text == 3 ? '供应商店铺' : 
              // dev_supplier-end
              
              '普通店铺';
              return val
            },
          },
          // dev_supplier_o2o-end
          {
            title: '店铺等级',
            dataIndex: 'storeGradeName',
            width: 80,
          },
          {
            title: '审核状态',
            dataIndex: 'stateValue',
            width: 80,
          },
          {
            title: '拒绝理由',
            dataIndex: 'refuseReason',
            width: 130,
          },
        ],
        actionColumn: {
          width: 100,
          title: '操作',
          dataIndex: 'action',
        },
        useSearchForm: true,
        formConfig: {
          schemas: [
            ...schemas,
            {
              field: 'state',
              component: 'Select',
              colProps: { span: 6, style: 'width:240px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                placeholder: '请选择审核状态',
                options: [
                  { key: 0, label: '全部', value: '' },
                  { key: 1, label: '待审核', value: '1' },
                  { key: 2, label: '待付款', value: '2' },
                  { key: 3, label: '已拒绝', value: '3' },
                ],
                size: 'default',
              },
              label: '审核状态',
              labelWidth: 80,
            },
          ],
        },
        bordered: true,
        striped: false,
        showIndexColumn: true,
      });
      const [renewStandardTable, { reload: reloadRenew }] = useTable({
        api: (arg) => getRenewStoreList({ ...arg }),
        fetchSetting: {
          pageField: 'current',
          sizeField: 'pageSize',
          listField: 'data.list',
          totalField: 'data.pagination.total',
        },
        columns: [
          {
            title: '店铺名称',
            dataIndex: 'storeName',
            width: 120,
          },
          {
            title: '店主账号',
            dataIndex: 'vendorName',
            width: 120,
          },
          {
            title: '联系人',
            dataIndex: 'contactName',
            width: 120,
          },
          {
            title: '联系电话',
            dataIndex: 'contactPhone',
            width: 120,
          },
          // dev_supplier_o2o-start
          {
            title: '店铺类型',
            dataIndex: 'shopType', 
            width: 80,
            customRender: ({ text }) => {
              let val =  
              // dev_supplier-start
              text == 3 ? '供应商店铺' : 
              // dev_supplier-end
              
              '普通店铺';
              return val
            },
          },
          // dev_supplier_o2o-end
          {
            title: '续签等级',
            dataIndex: 'gradeName',
            width: 120,
          },
          {
            title: '收费标准(元/年)',
            dataIndex: 'price',
            width: 120,
          },
          {
            title: '续签时长(年)',
            dataIndex: 'duration',
            width: 120,
          },
          {
            title: '应付金额(元)',
            dataIndex: 'payAmount',
            width: 120,
          },
          {
            title: '续签起止有效期',
            dataIndex: 'startTime',
            width: 140,
          },
          {
            title: '付款状态',
            dataIndex: 'stateValue',
            width: 120,
          },
          {
            title: '付款方式',
            dataIndex: 'paymentName',
            width: 120,
          },
        ],
        actionColumn: {
          width: 100,
          title: '操作',
          dataIndex: 'action',
        },
        useSearchForm: true,
        formConfig: {
          schemas: [
            {
              field: 'storeName',
              component: 'Input',
              colProps: { span: 6, style: 'width:260px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                maxlength: 10,
                placeholder: '请输入店铺名称',
                size: 'default',
              },
              label: '店铺名称',
              labelWidth: 80,
            },
            {
              field: 'vendorName',
              component: 'Input',
              colProps: { span: 6, style: 'width:260px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                maxlength: 20,
                placeholder: '请输入店主账号',
                size: 'default',
              },
              label: '店主账号',
              labelWidth: 80,
            },
            {
              field: 'contactName',
              component: 'Input',
              colProps: { span: 6, style: 'width:260px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                maxlength: 6,
                placeholder: '请输入联系人',
                size: 'default',
              },
              label: '联系人',
              labelWidth: 80,
            },
            {
              field: 'contactPhone',
              component: 'Input',
              colProps: { span: 6, style: 'width:260px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                maxlength: 11,
                placeholder: '请输入联系电话',
                size: 'default',
              },
              label: '联系电话',
              labelWidth: 80,
            },
            // dev_supplier_o2o-start
            {
              field: 'shopType',
              component: 'Select',
              colProps: { span: 6, style: 'width:240px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                placeholder: '请选择店铺类型',
                options: [
                  { key: 0, label: '全部', value: '' },
                  { key: 1, label: '普通店铺', value: '1' },
                  // dev_supplier-start
                  { key: 3, label: '供应商店铺', value: '3' },
                  // dev_supplier-end
                  
                ],
                size: 'default',
              },
              label: '店铺类型',
              labelWidth: 80,
            },
            // dev_supplier_o2o-end
            {
              field: 'state',
              component: 'Select',
              colProps: { span: 6, style: 'width:240px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                placeholder: '请选择付款状态',
                options: [
                  { key: 0, label: '全部', value: '' },
                  { key: 1, label: '待付款', value: '1' },
                  { key: 2, label: '已付款', value: '2' },
                ],
                size: 'default',
              },
              label: '付款状态',
              labelWidth: 80,
            },
          ],
        },
        bordered: true,
        striped: false,
        showIndexColumn: true,
      });
      const [categoryStandardTable, { reload: reloadCate }] = useTable({
        rowKey: rowKey,
        api: (arg) => getAuditCateList({ ...arg }),
        fetchSetting: {
          pageField: 'current',
          sizeField: 'pageSize',
          listField: 'data.list',
          totalField: 'data.pagination.total',
        },
        columns: [
          {
            title: '店铺名称',
            dataIndex: 'storeName',
            width: 100,
          },
          {
            title: '店主账号',
            dataIndex: 'vendorName',
            width: 100,
          },
          {
            title: '申请类目',
            dataIndex: 'goodsCateName',
            width: 150,
          },
          {
            title: '分佣比例',
            dataIndex: 'scaling',
            width: 100,
          },
          {
            title: '审核状态',
            dataIndex: 'stateValue',
            width: 100,
          },
          {
            title: '拒绝理由',
            dataIndex: 'refuseReason',
            width: 120,
          },
        ],
        actionColumn: {
          width: 100,
          title: '操作',
          dataIndex: 'action',
        },
        useSearchForm: true,
        formConfig: {
          schemas: [
            {
              field: 'storeName',
              component: 'Input',
              colProps: { span: 6, style: 'width:260px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                maxlength: 10,
                placeholder: '请输入店铺名称',
                size: 'default',
              },
              label: '店铺名称',
              labelWidth: 80,
            },
            {
              field: 'state',
              component: 'Select',
              colProps: { span: 6, style: 'width:240px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                placeholder: '请选择审核状态',
                options: [
                  { key: 0, label: '全部', value: '' },
                  { key: 1, label: '待审核', value: '1' },
                  { key: 2, label: '审核通过', value: '2' },
                  { key: 3, label: '审核拒绝', value: '3' },
                ],
                size: 'default',
              },
              label: '审核状态',
              labelWidth: 80,
            },
          ],
        },
        bordered: true,
        striped: false,
        showIndexColumn: true,
        rowSelection: {
          type: 'checkbox',
          selectedRowKeys: checkedKeys,
          onSelect: onSelect,
          onSelectAll: onSelectAll,
          getCheckboxProps(record) {
            return { disabled: record.state == 1 ? false : true };
          },
        },
        clickToRowSelect: false,
      });

      const width = ref(550);
      const title = ref('');
      const visible = ref(false);
      const content = ref([]);
      const confirmBtnLoading = ref(false);
      const operate_item = ref('');
      const operate_type = ref('');
      const operate_add_edit_data = ref([
        {
          type: 'input',
          label: `品牌名称`,
          name: 'brandName',
          placeholder: `请输入品牌名称`,
          extra: `最多输入20个字`,
          maxLength: 20,
          rules: [
            {
              required: true,
              message: `请输入品牌名称`,
            },
          ],
        },
        {
          type: 'upload_img_upload',
          label: '品牌LOGO',
          name: 'imageUrl',
          extra: '建议上传宽153*高54的图片',
          initialValue: [],
          upload_name: 'file',
          upload_url: 'v3/oss/admin/upload?source=setting',
          limit: 10,
          accept: ' .jpg, .jpeg, .png',
          rules: [
            {
              required: true,
              message: `请上传品牌LOGO`,
            },
          ],
        },
        {
          type: 'textarea',
          label: `品牌描述`,
          name: 'brandDesc',
          placeholder: `请输入品牌描述`,
          extra: `最多输入200个字`,
          maxLength: 200,
        },
      ]);
      const operate_reset_data = ref([
        {
          type: 'input',
          input_type: 'password',
          label: `新密码`,
          name: 'newPassword',
          placeholder: `请输入新密码`,
          maxLength: 20,
          rules: [
            {
              required: true,
              whitespace: true,
              message: `请输入新密码`,
            },
            {
              validator: (rule, value) => validatorMemPwd(rule, value),
            },
          ],
        },
        {
          type: 'input',
          input_type: 'password',
          label: `确认新密码`,
          name: 'newPasswordCfm',
          placeholder: `请输入确认新密码`,
          maxLength: 20,
          rules: [
            {
              required: true,
              whitespace: true,
              message: `请输入确认新密码`,
            },
            {
              validator: (rule, value) => validatorMemPwd(rule, value),
            },
          ],
        },
      ]);
      const operate_payday_data = ref([
        {
          type: 'radio_select',
          label: '结算方式',
          name: 'billType',
          data: [
            { key: 1, value: '按月结算' },
            { key: 2, value: '按周结算' },
          ],
          initialValue: 1,
          callback: true,
        },
        {
          type: 'checkboxgroup',
          label: '结算日',
          extra: '设置该商家每月几号进行结算，可多选，若当月没有设置的日期则该日不进行结算。',
          name: 'billDays',
          initialValue: [],
          sldOptions: month_to_num(),
          rules: [
            {
              required: true,
              message: `请选择结算日`,
            },
          ],
          callback: true,
        },
      ]);
      const operate_audit_data = ref([
        {
          type: 'radio_select',
          label: `审核结果`,
          name: 'state',
          extra: `请选择审核结果`,
          data: [
            { key: 1, value: `通过` },
            { key: 0, value: `拒绝` },
          ],
          initialValue: 1,
          callback: true,
        },
      ]);
      const operate_refuse_data = ref([
        {
          type: 'textarea',
          label: `备注`,
          name: 'refuseReason',
          placeholder: `请输入备注`,
          extra: `最多输入100字`,
          maxLength: 100,
          rules: [
            {
              required: true,
              whitespace: true,
              message: `请输入备注`,
            },
          ],
        },
      ]);

      const click_event = ref(false);
      const indexFlag = ref([1, 0, 0, 0, 0]); //各列表初次切换时不执行reload事件

      //tab切换
      function handleChange(e) {
        if (!indexFlag.value[e - 1]) {
          indexFlag.value[e - 1] = 1;
          if (e == 1) {
            rowKey.value = 'storeId';
          } else if (e == 2) {
            rowKey.value = 'storeId';
          } else if (e == 3) {
            rowKey.value = 'applyId';
          } else if (e == 4) {
            rowKey.value = 'storeId';
          } else {
            rowKey.value = 'bindId';
          }
        } else {
          if (e == 1) {
            rowKey.value = 'storeId';
            reload();
          } else if (e == 2) {
            rowKey.value = 'storeId';
            reloadNear();
          } else if (e == 3) {
            rowKey.value = 'applyId';
            reloadAudit();
          } else if (e == 4) {
            rowKey.value = 'storeId';
            reloadRenew();
          } else {
            rowKey.value = 'bindId';
            reloadCate();
          }
        }
      }

      //表格的单行选中回调事件
      function onSelect(record, selected) {
        if (selected) {
          checkedKeys.value = [...checkedKeys.value, record[rowKey.value]];
        } else {
          checkedKeys.value = checkedKeys.value.filter((item) => item != record[rowKey.value]);
        }
      }

      //表格的全选回调事件
      function onSelectAll(selected, selectedRows, changeRows) {
        const changeIds = changeRows.map((item) => item[rowKey.value]);
        if (selected) {
          checkedKeys.value = [...checkedKeys.value, ...changeIds];
        } else {
          checkedKeys.value = checkedKeys.value.filter((item) => {
            return !changeIds.includes(item);
          });
        }
      }

      //表格点击回调事件
      const handleClick = async(item, type)=> {
        if (click_event.value) return;
        operate_type.value = type;
        if (item != undefined && item != null) {
          operate_item.value = item;
        }
        let params = {};
        if (type == 'edit') {
          // dev_supplier_o2o-start
          await getDictDataList();
          if(item.shopType!=1&&shop_type_list.value.indexOf(String(item.shopType))==-1){
            
            // dev_supplier-start
            if(item.shopType==3){
              failTip('供应商模块已关闭，所以该店铺不支持编辑')
            }
            // dev_supplier-end
            return
          }
          // dev_supplier_o2o-end
          go(`/manage_store/settle_store_list_to_edit?id=${item.storeId}`);
        } else if (type == 'del') {
          if (tabIndex.value == 1 || tabIndex.value == 2) {
            params.storeId = item.storeId;
          } else if (tabIndex.value == 4) {
            params.renewId = item.renewId;
          }
          click_event.value = true;
          operate_role(params);
        } else if (type == 'switch') {
          operate_role(item);
        } else if (type == 'view') {
          if (tabIndex.value == 1 || tabIndex.value == 2) {
            go(`/manage_store/settle_store_list_view?id=${item.storeId}`);
          } else {
            go(`/manage_store/settle_store_list_apply_detail?state=1&id=${item.applyId}`);
          }
        } else if (type == 'setPwd') {
          title.value = '重置密码';
          visible.value = true;
          content.value = JSON.parse(JSON.stringify(operate_reset_data.value));
          content.value[0].rules = [
            {
              required: true,
              whitespace: true,
              message: `请输入新密码`,
            },
            {
              validator: (rule, value) => validatorMemPwd(rule, value),
            },
          ];
          content.value[1].rules = [
            {
              required: true,
              whitespace: true,
              message: `请输入确认新密码`,
            },
            {
              validator: (rule, value) => validatorMemPwd(rule, value),
            },
          ];
        } else if (type == 'setPay') {
          title.value = '设置结算周期';
          visible.value = true;
          content.value = JSON.parse(JSON.stringify(operate_payday_data.value));
          content.value[0].initialValue = item.billType;
          content.value[1].initialValue = item.billDay.split(',');
          content.value[1].sldOptions = item.billType == 1
            ? month_to_num() : week_to_num();
          content.value[1].extra = item.billType == 1
            ? '设置该商家每月几号进行结算，可多选，若当月没有设置的日期则该日不进行结算。'
            : '设置该商家每周几进行结算，可多选，全部选中则为按天结算。';
        } else if (type == 'audit') {
          go(`/manage_store/settle_store_list_apply_detail?state=2&id=${item.applyId}`);
        } else if (type == 'pass') {
          params.isPass = type == 'pass' ? true : false;
          if (item == null) {
            if (checkedKeys.value.length == 0) {
              failTip('请先选中数据');
              return;
            } else {
              params.bindIds = checkedKeys.value.join(',');
            }
          } else {
            params.bindIds = item.bindId;
          }
          click_event.value = true;
          operate_role(params);
        } else if (type == 'refuse') {
          if (item == null && checkedKeys.value.length == 0) {
            failTip('请先选中数据');
            return;
          }
          content.value = JSON.parse(JSON.stringify(operate_refuse_data.value));
          title.value = '审核拒绝理由';
          visible.value = true;
        }
      }

      //操作
      const operate_role = async (params) => {
        let res = {};
        if (operate_type.value == 'del') {
          if (tabIndex.value == 4) {
            res = await delStoreRenew(params);
          } else {
            res = await delOwn(params);
          }
        } else if (operate_type.value == 'switch') {
          res = await lockOwn(params);
        } else if (operate_type.value == 'setPwd') {
          res = await resetStorePwd(params);
        } else if (operate_type.value == 'setPay') {
          res = await setStorePayday(params);
        } else if (operate_type.value == 'pass' || operate_type.value == 'refuse') {
          res = await auditStoreCate(params);
        }
        click_event.value = false;
        if (res.state == 200) {
          sucTip(res.msg);
          checkedKeys.value = [];
          if (
            operate_type.value == 'setPwd' ||
            operate_type.value == 'setPay' ||
            operate_type.value == 'refuse'
          ) {
            handleCancle();
          }
          if (tabIndex.value == 1) {
            reload();
          } else if (tabIndex.value == 2) {
            reloadNear();
          } else if (tabIndex.value == 3) {
            reloadAudit();
          } else if (tabIndex.value == 4) {
            reloadRenew();
          } else {
            reloadCate();
          }
        } else {
          failTip(res.msg);
        }
      };

      function handelSelect(val) {
        let temp = content.value.filter((item) => item.name == val.contentItem.name);
        if (temp.length > 0) {
          temp[0].initialValue = val.val1;
        }
      }

      //弹窗回调事件
      function handleRadio(val) {
        if (operate_type.value == 'setPay') {
          content.value[0].initialValue = val;
          content.value[1].initialValue =
            operate_item.value.billType == val ? operate_item.value.billDay.split(',') : [];
          content.value[1].sldOptions = val == 1 ? month_to_num() : week_to_num();
          content.value[1].extra =
            val == 1
              ? '设置该商家每月几号进行结算，可多选，若当月没有设置的日期则该日不进行结算。'
              : '设置该商家每周几进行结算，可多选，全部选中则为按天结算。';
        } else {
          let temp = content.value.filter((item) => item.name == 'state');
          temp[0].initialValue = val;
          content.value = content.value.filter((item) => item.name != 'auditReason');
          if (val == 0) {
            content.value.push({
              type: 'textarea',
              label: `审核意见`,
              name: 'auditReason',
              placeholder: `请输入审核意见`,
              extra: `最多100字`,
              maxLength: 100,
              rules: [
                {
                  required: true,
                  whitespace: true,
                  message: `请输入审核意见`,
                },
              ],
            });
          }
        }
      }

      //弹窗取消事件
      function handleCancle() {
        visible.value = false;
        content.value = [];
        operate_item.value = '';
        checkedKeys.value = [];
      }

      //弹窗确认事件
      function handleConfirm(val) {
        if (click_event.value) return;
        if (operate_type.value == 'setPwd') {
          val.newPassword = base64Encrypt(val.newPassword);
          val.newPasswordCfm = base64Encrypt(val.newPasswordCfm);
          if (val.newPassword !== val.newPasswordCfm) {
            failTip('新密码与确认新密码输入不一致');
            return;
          }
          val.vendorId = operate_item.value.vendorId;
        } else if (operate_type.value == 'setPay') {
          val.billDays = val.billDays.join(',');
          val.storeId = operate_item.value.storeId;
        } else if (operate_type.value == 'refuse') {
          val.isPass = false;
          val.bindIds = operate_item.value.bindId || checkedKeys.value.join(',');
        }
        click_event.value = true;
        operate_role(val);
      }


       // dev_supplier_o2o-start
      // 获取店铺类型
      const getDictDataList = async()=> {
        shop_type_list.value = []
        let res = await getDictDataApi({dictCode:'shop_type'})
        if(res.state == 200){
          if(res.data.length>0){
            let obj = []
            res.data.forEach(item=>{
              obj.push({ value: item.dictKey, label: item.dictValue })
              shop_type_list.value.push(String(item.dictKey))
            })
          }
        }
      }
      // dev_supplier_o2o-end


      onMounted(() => {
        if(route.query.tab&&route.query.tab=='check'){
          tabIndex.value = 3
        }
        get_store_grade();
      });

      //获取店铺等级
      const get_store_grade = async () => {
        const res = await getStoreGrade({ pageSize: 10000 });
        if (res.state == 200 && res.data && res.data.list) {
          let temp = schemas.filter((item) => item.field == 'storeGradeId');
          if (temp.length > 0) {
            temp[0].componentProps.options = unref(res.data.list).map((item) => ({
              key: item.gradeId,
              value: item.gradeId,
              label: item.gradeName,
            }));
          }
        }
      };

      return {
        tabIndex,
        tipData,
        checkedKeys,
        rowKey,
        standardTable,
        nearStandardTable,
        auditStandardTable,
        renewStandardTable,
        categoryStandardTable,
        handleChange,
        handleClick,
        width,
        title,
        visible,
        content,
        confirmBtnLoading,
        operate_item,
        operate_type,
        operate_add_edit_data,
        operate_reset_data,
        operate_payday_data,
        operate_audit_data,
        operate_refuse_data,
        handelSelect,
        handleRadio,
        handleCancle,
        handleConfirm,
        click_event,
        get_store_grade,
        // dev_supplier_o2o-start
        getDictDataList,
        shop_type_list
        // dev_supplier_o2o-end
      };
    },
  });
</script>
<style lang="less" scoped></style>
