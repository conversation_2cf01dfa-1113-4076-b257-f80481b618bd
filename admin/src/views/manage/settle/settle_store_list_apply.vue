<template>
  <div class="section_padding common_description settle_store_apply">
    <div class="section_padding_back">
      <SldComHeader :type="1" :title="'入驻店铺管理'" :back="true" />
      <div class="list_apply_height_detail">
        <template v-if="detail.state !== undefined">
          <Description
            v-if="detail.enterType == 1"
            size="middle"
            title="公司联系人信息"
            :bordered="true"
            :column="2"
            :data="detail"
            :schema="componaySchema"
          />
          <Description
            v-else
            size="middle"
            title="店铺信息"
            :bordered="true"
            :column="2"
            :data="detail"
            :schema="storeSchema"
          />
          <Description
            v-if="detail.enterType == 1"
            size="middle"
            title="营业执照信息"
            :bordered="true"
            :column="2"
            :data="detail"
            :schema="businessSchema"
          />
          <Description
            size="middle"
            :title="detail.enterType == 1 ? '法人身份信息' : '身份证信息'"
            :bordered="true"
            :column="2"
            :data="detail"
            :schema="cardSchema"
          />
          <Description
            v-if="
              detail.enterType == 1 &&
              (detail.moreQualification1Path ||
                detail.moreQualification2Path ||
                detail.moreQualification3Path)
            "
            size="middle"
            title="补充认证信息"
            :bordered="true"
            :column="2"
            :data="detail"
            :schema="extraSchema"
          />
          <Description
            size="middle"
            title="店铺经营信息"
            :bordered="true"
            :column="2"
            :data="detail"
            :schema="infoSchema"
          />
          <div class="settle_store_detail_title flex_row_between_center">
            <div>经营类目</div>
            <Popconfirm v-if="detail.state && detail.state == 1" @confirm="handleSetScaling">
              <template #title>
                <InputNumber
                  :min="0"
                  :max="1"
                  :precision="3"
                  placeholder="佣金比例在0～1之间"
                  :value="scaling"
                  @change="(e) => handleScaling(e, null)"
                />
              </template>
              <div class="settle_store_detail_title_btn">批量设置</div>
            </Popconfirm>
          </div>
          <BasicTable @register="standardTable" style="padding: 0 3px">
            <template #headerCell="{ column }">
              <template v-if="column.key == 'scaling'">
                <div class="table_title">
                  <span class="table_title_xing">*</span>
                  {{ column.customTitle }}
                  <span class="table_title_extra">
                    <Tooltip placement="bottom">
                      <template #title> 佣金比例在0～1之间 </template>
                      <AliSvgIcon iconName="iconwen" width="14px" height="14px" fillColor="#bfbbba" />
                    </Tooltip>
                  </span>
                </div>
              </template>
              <template v-else>
                {{ column.customTitle }}
              </template>
            </template>
            <template #bodyCell="{ column, text, index }">
              <template v-if="column.key == 'scaling'">
                <InputNumber
                  v-if="detail.state && detail.state == 1"
                  :min="0"
                  :max="1"
                  :precision="3"
                  placeholder="佣金比例在0～1之间"
                  :value="Number(text)"
                  @change="(e) => handleScaling(e, index)"
                />
                <span v-else>{{ text }}</span>
              </template>
              <template v-else-if="column.key">
                {{ text !== undefined && text !== null ? text : '--' }}
              </template>
            </template>
          </BasicTable>
          <template v-if="detail.state == 1">
            <div style="width: 100%; height: 50px"></div>
            <div
              class="m_diy_bottom_wrap"
              :style="{ position: 'fixed', left: getRealWidth + 10 + 'px' }"
            >
              <div class="add_goods_bottom_btn" @click="handleClick('refuse')"> 拒绝 </div>
              <Popconfirm title="确定审核通过该店铺？" @confirm="handleClick('pass')">
                <div class="add_goods_bottom_btn add_goods_bottom_btn_sel">通过</div>
              </Popconfirm>
            </div>
          </template>
        </template>
      </div>
      <SldModal
        :width="width"
        :title="title"
        :visible="visible"
        :content="content"
        :confirmBtnLoading="confirmBtnLoading"
        :showFoot="true"
        @cancle-event="handleCancle"
        @confirm-event="handleConfirm"
      />
    </div>
  </div>
</template>
<script lang="ts">
  import { defineComponent, ref, onMounted, h } from 'vue';
  import { useRoute } from 'vue-router';
  import { Tabs, Tooltip, InputNumber, Popconfirm } from 'ant-design-vue';
  import { Description, DescItem } from '/@/components/Description/index';
  import { BasicTable, useTable } from '/@/components/Table';
  import { getApplyStoreDetail, setAuditStore, getReasonList } from '/@/api/manage/manage';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import SldModal from '@/components/SldModal/index.vue';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import { sucTip, failTip } from '/@/utils/utils';
  const { getRealWidth } = useMenuSetting();

  export default defineComponent({
    components: {
      [Tabs.name]: Tabs,
      [Tabs.TabPane.name]: Tabs.TabPane,
      Tooltip,
      InputNumber,
      Popconfirm,
      Description,
      BasicTable,
      SldComHeader,
      SldModal,
    },
    setup() {
      const route = useRoute();
      const detail: any = ref({});
      const storeSchema: DescItem[] = [
        {
          field: 'enterTypeValue',
          label: '入驻类型',
          render: function (val) {
            return val ? val : '--';
          },
        },
        // dev_supplier_o2o-start
        {
          field: 'shopType',
          label: '店铺类型',
          render: function (text) {
            let val =  
            // dev_supplier-start
            text == 3 ? '供应商店铺' : 
            // dev_supplier-end
            
            '普通店铺';
            return val
          },
        },
        // dev_supplier_o2o-end
        {
          field: 'areaInfo',
          label: '所在地',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'companyAddress',
          label: '详细地址',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'contactName',
          label: '联系人',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'contactPhone',
          label: '联系人手机号',
          render: function (val) {
            return val ? val : '--';
          },
        },
      ];
      const componaySchema: DescItem[] = [
        {
          field: 'enterTypeValue',
          label: '入驻类型',
          render: function (val) {
            return val ? val : '--';
          },
        },
        // dev_supplier_o2o-start
        {
          field: 'shopType',
          label: '店铺类型',
          render: function (text) {
            let val =  
            // dev_supplier-start
            text == 3 ? '供应商店铺' : 
            // dev_supplier-end
            
            '普通店铺';
            return val
          },
        },
        // dev_supplier_o2o-end
        {
          field: 'companyName',
          label: '公司名称',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'areaInfo',
          label: '所在地',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'companyAddress',
          label: '详细地址',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'contactName',
          label: '联系人',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'contactPhone',
          label: '联系人手机号',
          render: function (val) {
            return val ? val : '--';
          },
        },
      ];
      const businessSchema: DescItem[] = [
        {
          field: 'businessLicenseImagePath',
          label: '营业执照',
          render: (val) => {
            return h('div', { class: 'form_list_img_item' }, [
              h('img', { src: val, class: 'form_list_img_item_img' }),
              h('div', { class: 'form_list_img_item_right' }, [
                h('div', { class: 'form_list_img_item_pre' }, [
                  h('div', { class: 'form_list_img_item_pre_arrow' }),
                  h('img', { src: val, class: 'form_list_img_item_pre_img' }),
                ]),
              ]),
            ]);
          },
        },
      ];
      const cardSchema: DescItem[] = [
        {
          field: 'personCardUpPath',
          label: '身份证正面',
          render: (val) => {
            return h('div', { class: 'form_list_img_item' }, [
              h('img', { src: val, class: 'form_list_img_item_img' }),
              h('div', { class: 'form_list_img_item_right' }, [
                h('div', { class: 'form_list_img_item_pre' }, [
                  h('div', { class: 'form_list_img_item_pre_arrow' }),
                  h('img', { src: val, class: 'form_list_img_item_pre_img' }),
                ]),
              ]),
            ]);
          },
        },
        {
          field: 'personCardDownPath',
          label: '身份证反面',
          render: (val) => {
            return h('div', { class: 'form_list_img_item' }, [
              h('img', { src: val, class: 'form_list_img_item_img' }),
              h('div', { class: 'form_list_img_item_right' }, [
                h('div', { class: 'form_list_img_item_pre' }, [
                  h('div', { class: 'form_list_img_item_pre_arrow' }),
                  h('img', { src: val, class: 'form_list_img_item_pre_img' }),
                ]),
              ]),
            ]);
          },
        },
      ];
      const extraSchema: DescItem[] = [
        {
          field: 'moreQualification1Path',
          label: '补充认证资质',
          render: (val) => {
            return h('div', { class: 'form_list_img_item' }, [
              h('img', { src: val, class: 'form_list_img_item_img' }),
              h('div', { class: 'form_list_img_item_right' }, [
                h('div', { class: 'form_list_img_item_pre' }, [
                  h('div', { class: 'form_list_img_item_pre_arrow' }),
                  h('img', { src: val, class: 'form_list_img_item_pre_img' }),
                ]),
              ]),
            ]);
          },
          show: (item) => {
            return item.moreQualification1Path ? true : false;
          },
        },
        {
          field: 'moreQualification2Path',
          label: '补充认证资质',
          render: (val) => {
            return h('div', { class: 'form_list_img_item' }, [
              h('img', { src: val, class: 'form_list_img_item_img' }),
              h('div', { class: 'form_list_img_item_right' }, [
                h('div', { class: 'form_list_img_item_pre' }, [
                  h('div', { class: 'form_list_img_item_pre_arrow' }),
                  h('img', { src: val, class: 'form_list_img_item_pre_img' }),
                ]),
              ]),
            ]);
          },
          show: (item) => {
            return item.moreQualification2Path ? true : false;
          },
        },
        {
          field: 'moreQualification3Path',
          label: '补充认证资质',
          render: (val) => {
            return h('div', { class: 'form_list_img_item' }, [
              h('img', { src: val, class: 'form_list_img_item_img' }),
              h('div', { class: 'form_list_img_item_right' }, [
                h('div', { class: 'form_list_img_item_pre' }, [
                  h('div', { class: 'form_list_img_item_pre_arrow' }),
                  h('img', { src: val, class: 'form_list_img_item_pre_img' }),
                ]),
              ]),
            ]);
          },
          show: (item) => {
            return item.moreQualification3Path ? true : false;
          },
        },
      ];
      const infoSchema: DescItem[] = [
        {
          field: 'storeName',
          label: '店铺名称',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'storeGradeName',
          label: '店铺等级',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'applyYear',
          label: '开店时长',
          render: function (val) {
            return val ? val + '年' : '--';
          },
        },
      ];

      const rowKey: any = ref('bindId');
      const columns = [
        {
          title: '一级类目',
          dataIndex: 'goodsCateName1',
          width: 100,
        },
        {
          title: '二级类目',
          dataIndex: 'goodsCateName2',
          width: 100,
        },
        {
          title: '三级类目',
          dataIndex: 'goodsCateName3',
          width: 100,
        },
        {
          title: '佣金比例',
          dataIndex: 'scaling',
          width: 100,
        },
      ];
      const dataSource: any = ref([]);
      const [standardTable, { setProps }] = useTable({
        columns: columns,
        dataSource: dataSource,
        pagination: false,
        bordered: true,
        striped: false,
        showIndexColumn: true,
        canResize: false,
      });
      const scaling = ref(0);

      const width = ref(550);
      const title = ref('审核拒绝理由');
      const visible = ref(false);
      const content: any = ref([]);
      const confirmBtnLoading = ref(false);
      const operate_data: any = ref([
        {
          type: 'select',
          label: `拒绝理由`,
          name: 'refuseReason',
          placeholder: `请选择拒绝理由`,
          selData: [],
          rules: [
            {
              required: true,
              message: `请选择跟进方式`,
            },
          ],
          wrapperCol: { span: 15 },
          width: 366,
          diy: true,
          selKey: 'content',
          selName: 'content',
          callback: true,
        },
        {
          type: 'textarea',
          label: `备注`,
          name: 'remark',
          placeholder: `请输入审核拒绝理由，最多100字`,
          extra: `最多输入100字`,
          maxLength: 100,
        },
      ]);

      const click_event = ref(false);

      function handleClick(type) {
        if (type == 'refuse') {
          visible.value = true;
          content.value = JSON.parse(JSON.stringify(operate_data.value));
        } else if (type == 'pass') {
          if (click_event.value) return;
          click_event.value = true;
          let params: any = {};
          params.isPass = true;
          params.applyId = route.query.id;
          let scalingBindIds: any = [];
          dataSource.value.map((items) => {
            scalingBindIds.push(items.bindId + '-' + items.scaling);
          });
          params.scalingBindIds = scalingBindIds.join(',');
          operate_role(params);
        }
      }

      //弹窗取消事件
      function handleCancle() {
        visible.value = false;
        content.value = [];
      }

      //弹窗确认事件
      function handleConfirm(val) {
        if (click_event.value) return;
        click_event.value = true;
        val.isPass = false;
        val.applyId = route.query.id;
        let scalingBindIds: any = [];
        dataSource.value.map((items) => {
          scalingBindIds.push(items.bindId + '-' + items.scaling);
        });
        val.scalingBindIds = scalingBindIds.join(',');
        operate_role(val);
      }

      function handleScaling(e, index) {
        if (index === null) {
          scaling.value = e;
        } else {
          let data = JSON.parse(JSON.stringify(dataSource.value));
          data[index].scaling = e;
          dataSource.value = data;
          setProps({
            dataSource: data,
          });
        }
      }

      function handleSetScaling() {
        let data = JSON.parse(JSON.stringify(dataSource.value));
        data.map((items) => {
          items.scaling = scaling.value;
        });
        scaling.value = 0;
        dataSource.value = data;
        setProps({
          dataSource: data,
        });
      }

      //操作
      const operate_role = async (params) => {
        let res: any = await setAuditStore(params);
        click_event.value = false;
        if (res.state == 200) {
          sucTip(res.msg);
          get_apply_store_detail();
          handleCancle();
        } else {
          failTip(res.msg);
        }
      };

      onMounted(() => {
        get_reason_list();
        get_apply_store_detail();
      });

      //获取原因数据
      const get_reason_list = async () => {
        const res: any = await getReasonList({ type: 103, isShow: 1 });
        if (res.state == 200 && res.data) {
          let temp = operate_data.value.filter((item) => item.name == 'refuseReason');
          if (temp.length > 0) {
            temp[0].selData = res.data.list;
          }
        }
      };

      //获取店铺申请详情
      const get_apply_store_detail = async () => {
        const res: any = await getApplyStoreDetail({ applyId: route.query.id });
        if (res.state == 200 && res.data) {
          detail.value = res.data;
          dataSource.value = res.data.storeGoodsCateVOList ? res.data.storeGoodsCateVOList : [];
        } else {
          failTip(res.msg);
        }
      };

      return {
        detail,
        storeSchema,
        componaySchema,
        businessSchema,
        cardSchema,
        extraSchema,
        infoSchema,

        rowKey,
        dataSource,
        standardTable,
        width,
        title,
        visible,
        content,
        confirmBtnLoading,
        operate_data,
        handleCancle,
        handleConfirm,
        click_event,

        getRealWidth,
        handleClick,
        scaling,
        handleScaling,
        handleSetScaling,
      };
    },
  });
</script>
<style lang="less">
  .settle_store_apply {
    .slodon-basic-table{
      height: auto !important;
    }
    
    .ant-spin-container {
      .ant-table-container {
        .ant-table-body {
          height: auto !important;
        }
      }
    }

    .settle_store_detail_title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 25px;
      margin-bottom: 10px;
      padding-left: 0.5rem;
      color: #111;
      font-size: 14px;
      font-weight: bold;
      line-height: 24px;
      cursor: default;

      .settle_store_detail_title_btn {
        height: 28px;
        padding: 0 15px;
        border-radius: 3px;
        background: @primary-color;
        color: #fff;
        font-size: 13px;
        line-height: 27px;
        cursor: pointer;
      }
    }

    .table_title {
      display: flex;
      align-items: center;
      justify-content: center;

      .table_title_xing {
        margin-right: 4px;
        color: #f00;
      }

      .table_title_extra {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 4px;
        cursor: pointer;
      }
    }

    .slodon-collapse-container__header {
      height: 25px;
      padding: 0;
      border-bottom: none;

      .slodon-basic-title {
        color: #111;
        font-size: 14px;
        font-weight: bold;
      }
    }
    .list_apply_height_detail{
      max-height: calc(100vh - 46px - 127px);
      overflow: auto;
    }
  }
</style>
