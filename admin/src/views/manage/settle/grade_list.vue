<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader :type="1" :title="'店铺等级'" />
      <BasicTable @register="standardTable" style="padding: 0">
        <template #tableTitle>
          <div class="toolbar">
            <div class="toolbar_btn" @click="handleClick(null, 'add')">
              <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#fff" />
              <span>新增等级</span>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.key == 'action'">
            <template v-if="record.isSuper == 1"> -- </template>
            <template v-else>
              <span class="common_page_edit" @click="handleClick(record, 'edit')">编辑</span>
              <Popconfirm
                title="删除后不可恢复，是否确定删除？"
                @confirm="handleClick(record, 'del')"
              >
                <span class="common_page_edit">删除</span>
              </Popconfirm>
            </template>
          </template>
          <template v-else-if="column.key == 'recommendLimit' || column.key == 'goodsLimit' || column.key == 'sort'">
            {{ text>0 ? text : 0 }}
          </template>
          <template v-else-if="column.key == 'price'">
            ￥{{ text>0 ? Number(text).toFixed(2) : 0 }}
          </template>
          <template v-else-if="column.key">
            {{ text ? text : '--' }}
          </template>
        </template>
      </BasicTable>
      <SldModal
        :width="width"
        :title="title"
        :visible="visible"
        :content="content"
        :confirmBtnLoading="confirmBtnLoading"
        :showFoot="true"
        @cancle-event="handleCancle"
        @confirm-event="handleConfirm"
      />
    </div>
  </div>
</template>
<script>
  import { defineComponent, ref } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { Popconfirm } from 'ant-design-vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import {
    getStoreGrade,
    addStoreGrade,
    editStoreGrade,
    delStoreGrade,
  } from '/@/api/manage/manage';
  import SldModal from '@/components/SldModal/index.vue';
  import { failTip, sucTip } from '/@/utils/utils';

  export default defineComponent({
    components: {
      SldComHeader,
      BasicTable,
      Popconfirm,
      SldModal,
    },
    setup() {
      const [standardTable, { reload }] = useTable({
        api: (arg) => getStoreGrade({ ...arg }),
        fetchSetting: {
          pageField: 'current',
          sizeField: 'pageSize',
          listField: 'data.list',
          totalField: 'data.pagination.total',
        },
        columns: [
          {
            title: '等级名称',
            dataIndex: 'gradeName',
            width: 100,
          },
          {
            title: '可推荐商品数',
            dataIndex: 'recommendLimit',
            width: 100,
          },
          {
            title: '可发布商品数',
            dataIndex: 'goodsLimit',
            width: 100,
          },
          {
            title: '收费标准（每年）',
            dataIndex: 'price',
            width: 100,
          },
          {
            title: '申请说明',
            dataIndex: 'description',
            width: 100,
          },
          {
            title: '排序',
            dataIndex: 'sort',
            width: 100,
          },
        ],
        actionColumn: {
          width: 100,
          title: '操作',
          dataIndex: 'action',
        },
        useSearchForm: true,
        formConfig: {
          schemas: [
            {
              field: 'gradeName',
              component: 'Input',
              colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                maxlength: 6,
                placeholder: '请输入等级名称',
                size: 'default',
              },
              label: '等级名称',
              labelWidth: 80,
            },
          ],
        },
        bordered: true,
        striped: false,
        showIndexColumn: true,
      });
      const width = ref(550);
      const title = ref('');
      const visible = ref(false);
      const content = ref([]);
      const confirmBtnLoading = ref(false);
      const operate_id = ref('');
      const operate_type = ref('');

      const operate_data = ref([
        {
          type: 'input',
          label: `等级名称`,
          name: 'gradeName',
          placeholder: `请输入等级名称`,
          maxLength: 6,
          rules: [
            {
              required: true,
              message: `请输入等级名称`,
            },
          ],
        },
        {
          type: 'inputnum',
          label: `可推荐商品数`,
          name: 'recommendLimit',
          placeholder: `请输入可推荐商品数`,
          min: 0,
          max: 9999,
          rules: [
            {
              required: true,
              message: `请输入可推荐商品数`,
            },
          ],
        },
        {
          type: 'inputnum',
          label: `可发布商品数`,
          name: 'goodsLimit',
          placeholder: `请输入可发布商品数`,
          min: 0,
          max: 9999,
          rules: [
            {
              required: true,
              message: `请输入可发布商品数`,
            },
          ],
        },
        {
          type: 'inputnum',
          label: `收费标准`,
          name: 'price',
          placeholder: `请输入收费标准`,
          extra: `请输入0～9999999之间的整数，在店铺开通或升级店铺时将显示在前台`,
          min: 0,
          max: 9999999,
          rules: [
            {
              required: true,
              message: `请输入收费标准`,
            },
          ],
        },
        {
          type: 'textarea',
          label: `申请说明`,
          name: 'description',
          placeholder: `请输入申请说明`,
          extra: `申请说明，在店铺开通或升级店铺时将显示在前台，最多100字`,
          maxLength: 100,
        },
        {
          type: 'inputnum',
          label: `排序`,
          name: 'sort',
          placeholder: `请输入排序`,
          extra: `请输入0~255的数字,数值越大表明级别越高`,
          min: 0,
          max: 255,
          rules: [
            {
              required: true,
              message: `请输入排序`,
            },
          ],
        },
      ]); //新增、编辑弹窗数据

      //表格点击回调事件
      function handleClick(item, type) {
        operate_type.value = type;
        if (item) {
          operate_id.value = item.gradeId;
        }
        if (type == 'add' || type == 'edit') {
          title.value = type == 'add' ? '添加店铺等级' : '编辑店铺等级';
          visible.value = true;
          let data = JSON.parse(JSON.stringify(operate_data.value));
          if (type == 'edit') {
            data.map((items) => {
              items.initialValue = item[items.name]||item[items.name]==0 ? item[items.name] : undefined;
            });
          }
          content.value = data;
        } else if (type == 'del') {
          operate_role({ gradeId: item.gradeId });
        }
      }

      //弹窗取消事件
      function handleCancle() {
        visible.value = false;
        content.value = [];
        operate_id.value = '';
      }

      //弹窗确认事件
      function handleConfirm(val) {
        if (operate_type.value == 'edit') {
          val.gradeId = operate_id.value;
        }
        operate_role(val);
      }

      //弹窗操作
      const operate_role = async (params) => {
        confirmBtnLoading.value = true;
        let res = {};
        if (operate_type.value == 'add') {
          res = await addStoreGrade(params);
        } else if (operate_type.value == 'edit') {
          res = await editStoreGrade(params);
        } else if (operate_type.value == 'del') {
          res = await delStoreGrade(params);
        }
        confirmBtnLoading.value = false;
        if (res.state == 200) {
          handleCancle();
          sucTip(res.msg);
          reload();
        } else {
          failTip(res.msg);
        }
      };

      return {
        standardTable,
        width,
        title,
        visible,
        content,
        confirmBtnLoading,
        operate_id,
        operate_type,
        operate_data,
        handleClick,
        handleCancle,
        handleConfirm,
      };
    },
  });
</script>
<style lang="less" scoped></style>
