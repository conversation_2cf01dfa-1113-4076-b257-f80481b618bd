<template>
  <PageWrapper class="common_page_toolbar common_description settle_store_edit">
    <SldComHeader :type="1" :title="'编辑店铺信息'" :back="true" />
    <template v-if="detail.storeId !== undefined">
      <template v-if="detail.enterType == 1">
        <StandardFormRow
          :width="'50%'"
          :left_width="160"
          :data="companyData"
          :valida="valida"
          @valida-event="handleValida"
          @callback-event="handleChange"
        />
        <StandardFormRow
          :width="'100%'"
          :left_width="160"
          :data="businessData"
          :valida="valida"
          @valida-event="handleValida"
          @callback-event="handleChange"
        />
      </template>
      <template v-else>
        <StandardFormRow
          :width="'50%'"
          :left_width="160"
          :data="storeData"
          :valida="valida"
          @valida-event="handleValida"
          @callback-event="handleChange"
        />
      </template>
      <StandardFormRow
        :width="'100%'"
        :left_width="160"
        :data="cardData"
        :valida="valida"
        @valida-event="handleValida"
        @callback-event="handleChange"
      />
      <StandardFormRow
        v-if="detail.enterType == 1"
        :width="'100%'"
        :left_width="160"
        :data="extraData"
        :valida="valida"
        @valida-event="handleValida"
        @callback-event="handleChange"
      />
      <StandardFormRow
        :width="'50%'"
        :left_width="160"
        :data="infoData"
        :valida="valida"
        @valida-event="handleValida"
        @callback-event="handleChange"
      />
      <StandardFormRow
        :width="'100%'"
        :left_width="160"
        :data="sellteData"
        :valida="valida"
        @valida-event="handleValida"
        @callback-event="handleChange"
      />
      <div class="settle_store_detail_title flex_row_between_center">
        <div>经营类目</div>
        <Popconfirm @confirm="handleSetScaling">
          <template #title>
            <InputNumber
              :min="0"
              :max="1"
              :precision="3"
              placeholder="佣金比例在0～1之间"
              :value="scaling"
              @change="(e) => handleScaling(e, null)"
            />
          </template>
          <div class="settle_store_detail_title_btn">批量设置</div>
        </Popconfirm>
      </div>
      <BasicTable @register="standardTable" style="padding: 0 3px">
        <template #headerCell="{ column }">
          <template v-if="column.key == 'scaling'">
            <div class="table_title">
              <span class="table_title_xing">*</span>
              {{ column.customTitle }}
              <span class="table_title_extra">
                <Tooltip placement="bottom">
                  <template #title> 佣金比例在0～1之间 </template>
                  <AliSvgIcon iconName="iconwen" width="14px" height="14px" fillColor="#bfbbba" />
                </Tooltip>
              </span>
            </div>
          </template>
          <template v-else>
            {{ column.customTitle }}
          </template>
        </template>
        <template #bodyCell="{ column, text ,index,record}">
          <template v-if="column.key == 'scaling'">
            <InputNumber
              :min="0"
              :max="1"
              :precision="3"
              placeholder="佣金比例在0～1之间"
              v-model:value="record.scaling"
              @blur="(e) => handleScalingBlur(e, index)"
              @change="(e) => handleScaling(e, index)"
            />
          </template>
          <template v-else-if="column.key">
            {{ text !== undefined && text !== null ? text : '--' }}
          </template>
        </template>
      </BasicTable>
      <div style="width: 100%; height: 50px"></div>
      <div class="common_page_btn" :style="{ left: getRealWidth + 'px' }">
        <div class="common_page_btn_back" @click="handleClick('back')">返回</div>
        <div class="common_page_btn_save" @click="handleClick('save')">保存</div>
      </div>
    </template>
  </PageWrapper>
</template>
<script lang="ts">
  import { defineComponent, ref, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { Tabs, Tooltip, InputNumber, Popconfirm } from 'ant-design-vue';
  import { Description } from '/@/components/Description/index';
  import { BasicTable, useTable } from '/@/components/Table';
  import { getStoreGrade, getOpenTime, getStoreDetail, editStore } from '/@/api/manage/manage';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import StandardFormRow from '/@/components/StandardFormRow/index.vue';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import areaData from '@/assets/json/area.json';
  import { failTip, month_to_num, sucTip, week_to_num } from '/@/utils/utils';
  import { mobile_reg } from '/@/utils/validate';
  import { useGlobSetting } from '/@/hooks/setting';
  const { getRealWidth } = useMenuSetting();
  import { pageClose } from '/@/utils/utils';
  import { useMultipleTabStore } from '/@/store/modules/multipleTab';
  // dev_supplier_o2o-start
  import {
    getDictDataApi
  } from '/@/api/common/common';
  // dev_supplier_o2o-end

  export default defineComponent({
    components: {
      [Tabs.name]: Tabs,
      [Tabs.TabPane.name]: Tabs.TabPane,
      Tooltip,
      InputNumber,
      Popconfirm,
      Description,
      BasicTable,
      SldComHeader,
      StandardFormRow,
    },
    setup() {
      const scaling = ref(0);
      const route = useRoute();
      const tabStore = useMultipleTabStore();
      const router = useRouter();
      const { apiUrl } = useGlobSetting();
      const detail: any = ref({});
      const billType = ref('');
      const billDay = ref('');
      // dev_supplier_o2o-start
      const shop_type_list = ref([])
      // dev_supplier_o2o-end
      const companyData = ref([
        {
          type: 'title',
          label: '公司联系人信息',
        },
        {
          type: 'show_text',
          label: '入驻类型',
          key: 'enterTypeValue',
          value: '',
          item_width: '100%',
        },
        // dev_supplier_o2o-start
        {
          disabled: true,
          type: 'radio',
          require: true,
          label: '店铺类型',
          key: 'shopType',
          options: [],
          value: 1,
          item_width: '100%',
          callback: true,
        },
        // dev_supplier_o2o-end
        {
          type: 'input',
          require: true,
          label: '公司名称',
          key: 'companyName',
          placeholder: '请输入公司名称',
          value: '',
          right_width: '80%',
          rules: [
            {
              required: true,
              whitespace: true,
              message: '请输入公司名称',
            },
          ],
          item_width: '100%',
          callback: true,
        },
        {
          type: 'cascader',
          require: true,
          label: '所在地',
          key: 'areaInfo',
          placeholder: '请选择所在地',
          data: areaData,
          expandTrigger: 'click',
          fieldNames: {
            label: 'regionName',
            value: 'regionCode',
            children: 'children',
          },
          value: [],
          right_width: '80%',
          rules: [
            {
              required: true,
              message: '请选择所在地',
            },
          ],
          item_width: '100%',
          callback: true,
          
        },
        {
          type: 'textarea',
          require: true,
          label: '详细地址',
          key: 'companyAddress',
          placeholder: '请输入详细地址',
          maxlength: 40,
          rows: 1,
          value: '',
          right_width: '80%',
          rules: [
            {
              required: true,
              whitespace: true,
              message: '请输入详细地址',
            },
          ],
          item_width: '100%',
          
          callback: true,
        },
        {
          type: 'input',
          require: true,
          label: '联系人',
          key: 'contactName',
          placeholder: '请输入联系人',
          value: '',
          right_width: '80%',
          rules: [
            {
              required: true,
              whitespace: true,
              message: '请输入联系人',
            },
          ],
          item_width: '100%',
          callback: true,
        },
        {
          type: 'input',
          require: true,
          label: '联系人手机号',
          key: 'contactPhone',
          placeholder: '请输入联系人手机号',
          value: '',
          right_width: '80%',
          rules: [
            {
              required: true,
              whitespace: true,
              message: '请输入联系人手机号',
            },
            {
              pattern: mobile_reg,
              message: '请输入正确的手机号',
            },
          ],
          item_width: '100%',
          callback: true,
        },
      ]);
      const storeData = ref([
        {
          type: 'title',
          label: '店铺信息',
        },
        {
          type: 'show_text',
          label: '入驻类型',
          key: 'enterTypeValue',
          value: '',
          item_width: '100%',
        },
        // dev_supplier_o2o-start
        {
          disabled: true,
          type: 'radio',
          require: true,
          label: '店铺类型',
          key: 'shopType',
          options: [],
          value: 1,
          item_width: '100%',
          callback: true,
        },
        // dev_supplier_o2o-end
        {
          type: 'cascader',
          require: true,
          label: '所在地',
          key: 'areaInfo',
          placeholder: '请选择所在地',
          data: areaData,
          expandTrigger: 'click',
          fieldNames: {
            label: 'regionName',
            value: 'regionCode',
            children: 'children',
          },
          value: [],
          right_width: '80%',
          rules: [
            {
              required: true,
              message: '请选择所在地',
            },
          ],
          item_width: '100%',
          callback: true,
          
        },
        {
          type: 'textarea',
          require: true,
          label: '详细地址',
          key: 'companyAddress',
          placeholder: '请输入详细地址',
          maxlength: 40,
          rows: 1,
          value: '',
          right_width: '80%',
          rules: [
            {
              required: true,
              whitespace: true,
              message: '请输入详细地址',
            },
          ],
          item_width: '100%',
          
          callback: true,
        },
        {
          type: 'input',
          require: true,
          label: '联系人',
          key: 'contactName',
          placeholder: '请输入联系人',
          value: '',
          right_width: '80%',
          rules: [
            {
              required: true,
              whitespace: true,
              message: '请输入联系人',
            },
          ],
          item_width: '100%',
          callback: true,
        },
        {
          type: 'input',
          require: true,
          label: '联系人手机号',
          key: 'contactPhone',
          placeholder: '请输入联系人手机号',
          value: '',
          right_width: '80%',
          rules: [
            {
              required: true,
              whitespace: true,
              message: '请输入联系人手机号',
            },
            {
              pattern: mobile_reg,
              message: '请输入正确的手机号',
            },
          ],
          item_width: '100%',
          callback: true,
        },
      ]);
      
      const businessData = ref([
        {
          type: 'title',
          label: '营业执照信息',
        },
        {
          type: 'upload_img',
          require: true,
          label: '营业执照',
          key: 'businessLicenseImage',
          accept: '.jpg, .jpeg, .png',
          action: `${apiUrl}/v3/oss/admin/upload?source=sellerApply`,
          method: 'post',
          data: {},
          disabled: false,
          fileList: [],
          multiple: false,
          desc: '支持JPG/PNG,大小不超过20M',
          item_width: '100%',
          height: 150,
          callback: true,
        },
      ]);
      const cardData = ref([
        {
          type: 'title',
          label: '法人身份信息',
        },
        {
          type: 'upload_img',
          require: true,
          label: '身份证正面',
          key: 'personCardUp',
          accept: '.jpg, .jpeg, .png',
          action: `${apiUrl}/v3/oss/admin/upload?source=sellerApply`,
          method: 'post',
          data: {},
          disabled: false,
          fileList: [],
          multiple: false,
          desc: '支持JPG/PNG,大小不超过20M',
          item_width: '100%',
          height: 150,
          callback: true,
          rules: [
            {
              required: true,
              message: '请上传身份证正面',
            },
          ],
        },
        {
          type: 'upload_img',
          require: true,
          label: '身份证反面',
          key: 'personCardDown',
          accept: '.jpg, .jpeg, .png',
          action: `${apiUrl}/v3/oss/admin/upload?source=sellerApply`,
          method: 'post',
          data: {},
          disabled: false,
          fileList: [],
          multiple: false,
          desc: '支持JPG/PNG,大小不超过20M',
          item_width: '100%',
          height: 150,
          callback: true,
          rules: [
            {
              required: true,
              message: '请上传身份证正面',
            },
          ],
        },
      ]);
      const extraData = ref([
        {
          type: 'title',
          label: '补充认证信息',
        },
        {
          type: 'upload_img',
          label: '补充认证一',
          key: 'moreQualification1',
          accept: '.jpg, .jpeg, .png',
          action: `${apiUrl}/v3/oss/admin/upload?source=sellerApply`,
          method: 'post',
          data: {},
          disabled: false,
          fileList: [],
          multiple: false,
          desc: '支持JPG/PNG,大小不超过20M',
          item_width: '100%',
          height: 150,
          callback: true,
        },
        {
          type: 'upload_img',
          label: '补充认证二',
          key: 'moreQualification2',
          accept: '.jpg, .jpeg, .png',
          action: `${apiUrl}/v3/oss/admin/upload?source=sellerApply`,
          method: 'post',
          data: {},
          disabled: false,
          fileList: [],
          multiple: false,
          desc: '支持JPG/PNG,大小不超过20M',
          item_width: '100%',
          height: 150,
          callback: true,
        },
        {
          type: 'upload_img',
          label: '补充认证三',
          key: 'moreQualification3',
          accept: '.jpg, .jpeg, .png',
          action: `${apiUrl}/v3/oss/admin/upload?source=sellerApply`,
          method: 'post',
          data: {},
          disabled: false,
          fileList: [],
          multiple: false,
          desc: '支持JPG/PNG,大小不超过20M',
          item_width: '100%',
          height: 150,
          callback: true,
        },
      ]);
      const infoData = ref([
        {
          type: 'title',
          label: '店铺经营信息',
        },
        {
          type: 'show_text',
          label: '店铺名称',
          key: 'storeName',
          value: '',
          item_width: '100%',
        },
        {
          type: 'select',
          require: true,
          label: '店铺等级',
          placeholder: '请选择店铺等级',
          key: 'storeGradeId',
          value: '',
          data: [],
          right_width: '80%',
          diy: true,
          diy_key: 'gradeName',
          diy_value: 'gradeId',
          item_width: '100%',
          callback: true,
          rules: [
            {
              required: true,
              message: '请选择店铺等级',
            },
          ],
        },
        {
          type: 'select',
          require: true,
          label: '开店时长',
          placeholder: '请选择开店时长',
          key: 'openTime',
          value: '',
          data: [],
          right_width: '80%',
          item_width: '100%',
          callback: true,
          rules: [
            {
              required: true,
              message: '请选择开店时长',
            },
          ],
        },
      ]);
      const sellteData = ref([
        {
          type: 'title',
          label: '店铺结算信息',
        },
        {
          type: 'radio',
          require: true,
          label: '结算方式',
          key: 'billType',
          options: [
            { label: '按月结算', value: 1 },
            { label: '按周结算', value: 2 },
          ],
          value: '1',
          item_width: '100%',
          callback: true,
          rules: [
            {
              required: true,
              message: '请选择结算方式',
            },
          ],
        },
        {
          type: 'checkbox',
          require: true,
          label: '结算日',
          key: 'billDay',
          value: [],
          data: month_to_num(),
          desc: '设置该商家每月几号进行结算，可多选，若当月没有设置的日期则该日不进行结算。',
          diy: true,
          diy_key: 'label',
          diy_value: 'value',
          height: 110,
          right_width: '80%',
          item_width: '100%',
          desc_width: '90%',
          callback: true,
          rules: [
            {
              required: true,
              message: '请选择结算日',
            },
          ],
        },
      ]);
      const valida = ref(false);
      const validaFlag = ref(true);
      const validaLength = ref(0);

      const rowKey: any = ref('bindId');
      const columns = [
        {
          title: '一级类目',
          dataIndex: 'goodsCateName1',
          width: 100,
        },
        {
          title: '二级类目',
          dataIndex: 'goodsCateName2',
          width: 100,
        },
        {
          title: '三级类目',
          dataIndex: 'goodsCateName3',
          width: 100,
        },
        {
          title: '佣金比例',
          dataIndex: 'scaling',
          width: 100,
        },
      ];
      const dataSource: any = ref([]);
      const [standardTable,{setProps}] = useTable({
        columns: columns,
        dataSource: dataSource,
        pagination: false,
        bordered: true,
        striped: false,
        showIndexColumn: true,
        canResize: false,
      });

      const click_event = ref(false);
      function handleClick(type) {
        if (type == 'back') {
          if (window.history && window.history.length == 1) {
            pageClose();
          } else {
            const { fullPath } = route;
            tabStore.closeTabByKey(fullPath, router);
            router.back();
          }
        } else if (type == 'save') {
          valida.value = true;
          validaFlag.value = true;
          validaLength.value = detail.value.enterType == 1 ? 6 : 4;
        }
      }

      function handleValida(res, msg) {
        if (!validaFlag.value) {
          return;
        } else if (!res) {
          if (msg) {
            failTip(msg);
          }
          validaFlag.value = false;
          validaLength.value = 0;
          valida.value = false;
          return;
        }
        validaLength.value -= 1;
        if (validaLength.value == 0) {
          valida.value = false;
          if (click_event.value) return;
          click_event.value = true;
          let params: any = {};
          if(detail.value.enterType == 1&&!detail.value.businessLicenseImage){
            failTip('请上传营业执照')
            validaFlag.value = false;
            validaLength.value = 0;
            valida.value = false;
            click_event.value = false;
            return
          }
          if(!detail.value.personCardUp){
            failTip('请上传身份证正面')
            validaFlag.value = false;
            validaLength.value = 0;
            valida.value = false;
            click_event.value = false;
            return
          }
          if(!detail.value.personCardDown){
            failTip('请上传身份证反面')
            validaFlag.value = false;
            validaLength.value = 0;
            valida.value = false;
            click_event.value = false;
            return
          }
          params.storeId = detail.value.storeId;
          params.areaInfo = detail.value.areaInfo;
          params.companyProvinceCode = detail.value.companyProvinceCode;
          params.companyCityCode = detail.value.companyCityCode;
          params.companyAreaCode = detail.value.companyAreaCode;
          params.companyAddress = detail.value.companyAddress;
          params.contactName = detail.value.contactName;
          params.contactPhone = detail.value.contactPhone;
          params.personCardUp = detail.value.personCardUp;
          params.personCardDown = detail.value.personCardDown;
          if (detail.value.enterType == 1) {
            params.companyName = detail.value.companyName;
            params.businessLicenseImage = detail.value.businessLicenseImage;
            if (detail.value.moreQualification1) {
              params.moreQualification1 = detail.value.moreQualification1;
            }
            if (detail.value.moreQualification2) {
              params.moreQualification2 = detail.value.moreQualification2;
            }
            if (detail.value.moreQualification3) {
              params.moreQualification3 = detail.value.moreQualification3;
            }
          }
          params.storeGradeId = detail.value.storeGradeId;
          params.openTime = detail.value.openTime;
          params.billType = detail.value.billType;
          params.billDays =
            typeof detail.value.billDay == 'string'
              ? detail.value.billDay
              : detail.value.billDay.join(',');
              params.categoryList = []
          dataSource.value.forEach(item=>{
            params.categoryList.push({
              bindId:item.bindId,
              scaling:item.scaling?Number(item.scaling):0
            })
          })
          operate_role(params);
        }
      }

      //操作
      const operate_role = async (params) => {
        let res: any = await editStore(params);
        if (res.state == 200) {
          sucTip(res.msg);
          setTimeout(() => {
            handleClick('back');
          }, 1500);
        } else {
          click_event.value = false;
          failTip(res.msg);
        }
      };

      function handleChange(item) {
        let temp: any = [];
        let key = item.contentItem.key;
        if (key == 'businessLicenseImage') {
          temp = businessData.value.filter((items) => items.key == 'businessLicenseImage');
        } else if (key == 'personCardUp') {
          temp = cardData.value.filter((items) => items.key == 'personCardUp');
        } else if (key == 'personCardDown') {
          temp = cardData.value.filter((items) => items.key == 'personCardDown');
        } else if (key == 'moreQualification1') {
          temp = extraData.value.filter((items) => items.key == 'moreQualification1');
        } else if (key == 'moreQualification2') {
          temp = extraData.value.filter((items) => items.key == 'moreQualification2');
        } else if (key == 'moreQualification3') {
          temp = extraData.value.filter((items) => items.key == 'moreQualification3');
        } 
        // dev_supplier_o2o-start
        else if (key == 'shopType') {
          if (detail.value.enterType == 1) {
            if (item.val != 2) {
              companyData.value = companyData.value.filter((items) => items.type !== 'show_map');
            } 
            
            temp = companyData.value.filter((items) => items.key == 'shopType');
          } else {
            if (item.val != 2) {
              storeData.value = storeData.value.filter((items) => items.type !== 'show_map');
            }
            
            temp = storeData.value.filter((items) => items.key == 'shopType');
          }
          if (temp.length > 0) {
            temp[0].value = item.val;
          }
        }
        // dev_supplier_o2o-end
        if (
          key == 'businessLicenseImage' ||
          key == 'personCardUp' ||
          key == 'personCardDown' ||
          key == 'moreQualification1' ||
          key == 'moreQualification2' ||
          key == 'moreQualification3'
        ) {
          if (
            temp.length > 0 &&
            item.val.file.status != undefined &&
            item.val.file.status != 'error'
          ) {
            if (item.val.file.response && item.val.file.response.state != 200) {
              if(item.val.file.status!="removed"){
                failTip(item.val.file.response.msg || '上传失败');
              }
              item.val.fileList = item.val.fileList.filter((items) => items.response.state == 200);
            }
            temp[0].fileList = item.val.fileList;
            detail.value[key] =
              item.val.fileList.length > 0 &&
              item.val.file.response &&
              item.val.file.response.data &&
              item.val.file.response.data.path
                ? item.val.file.response.data.path
                : '';
            detail.value[key + 'Path'] =
              item.val.fileList.length > 0 &&
              item.val.file.response &&
              item.val.file.response.data &&
              item.val.file.response.data.url
                ? item.val.file.response.data.url
                : '';
          }
        } else if (key == 'areaInfo') {
          detail.value[key] =
            item.extra[0].regionName + item.extra[1].regionName + item.extra[2].regionName;
          detail.value['companyProvinceCode'] = item.val[0];
          detail.value['companyCityCode'] = item.val[1];
          detail.value['companyAreaCode'] = item.val[2];
        } else if (key == 'billType') {
          detail.value[key] = item.val;
          let type_temp = sellteData.value.filter((items) => items.key == 'billType');
          if (type_temp.length > 0) {
            type_temp[0].value = item.val;
          }
          let day_temp = sellteData.value.filter((items) => items.key == 'billDay');
          if (day_temp.length > 0) {
            day_temp[0].data = item.val == 1 ? month_to_num() : week_to_num();
            day_temp[0].value = item.val == billType.value ? billDay.value : [];
            day_temp[0].desc =
              item.val == 1
                ? '设置该商家每月几号进行结算，可多选，若当月没有设置的日期则该日不进行结算。'
                : '设置该商家每周几进行结算，可多选，全部选中则为按天结算。';
          }
        } else if (key) {
          detail.value[key] = item.val;
        }
      }

      onMounted(() => {
        // dev_supplier_o2o-start
        getDictDataList();
        // dev_supplier_o2o-end
        get_store_grade();
        get_open_time();
        get_apply_store_detail();
      });

      // dev_supplier_o2o-start
      // 获取店铺类型
      const getDictDataList = async()=> {
        shop_type_list.value = []
        let res = await getDictDataApi({dictCode:'shop_type'})
        if(res.state == 200){
          if(res.data.length>0){
            let obj = []
            res.data.forEach(item=>{
              obj.push({ value: Number(item.dictKey), label: item.dictValue })
              shop_type_list.value.push(String(item.dictKey))
            })
            companyData.value.forEach(item=>{
              if(item.key == 'shopType'){
                item.options = obj
              }
            })
            storeData.value.forEach(item=>{
              if(item.key == 'shopType'){
                item.options = obj
              }
            })
          }
        }
      }
      // dev_supplier_o2o-end

      //获取店铺等级
      const get_store_grade = async () => {
        const res: any = await getStoreGrade({ pageSize: 10000, isSort: true });
        if (res.state == 200 && res.data) {
          let temp = infoData.value.filter((item) => item.key == 'storeGradeId');
          if (temp.length > 0) {
            temp[0].data = res.data.list;
          }
        }
      };

      //获取店铺等级
      const get_open_time = async () => {
        const res: any = await getOpenTime();
        if (res.state == 200) {
          let temp = infoData.value.filter((item) => item.key == 'openTime');
          if (temp.length > 0) {
            let data: any = [];
            res.data.map((item) => {
              data.push({
                value: item,
                title: item + '年',
              });
            });
            temp[0].data = data;
          }
        }
      };

      //获取店铺申请详情
      const get_apply_store_detail = async () => {
        const res: any = await getStoreDetail({ storeId: route.query.id });
        if (res.state == 200 && res.data) {
          detail.value = res.data;
          billType.value = res.data.billType;
          billDay.value = res.data.billDay;
          dataSource.value = res.data.storeGoodsCateVOList ? res.data.storeGoodsCateVOList : [];
          if (res.data.enterType == 1) {
            companyData.value.map((item) => {
              if (item.key == 'areaInfo') {
                item.value = [
                  res.data.companyProvinceCode,
                  res.data.companyCityCode,
                  res.data.companyAreaCode,
                ];
              } else if (item.key) {
                item.value = res.data[item.key];
              }
              
            });
            
            businessData.value.map((item) => {
              if (item.key == 'businessLicenseImage') {
                item.fileList = [];
                let tmp_data: any = {};
                tmp_data.uid = res.data[item.key];
                tmp_data.name = res.data[item.key];
                tmp_data.status = 'done';
                tmp_data.url = res.data['businessLicenseImagePath'];
                tmp_data.response = {
                  data: {
                    url: res.data['businessLicenseImagePath'],
                    path: res.data[item.key],
                  },
                };
                item.fileList.push(tmp_data);
              } else if (item.key) {
                item.value = res.data[item.key];
              }
            });
          } else {
            storeData.value.map((item) => {
              if (item.key) {
                item.value = res.data[item.key];
              }
              
            });
            
          }
          cardData.value.map((item) => {
            if (item.type == 'title') {
              item.label = res.data.enterType == 1 ? '法人身份信息' : '身份证信息';
            } else if (item.key == 'personCardUp') {
              item.fileList = [];
              let tmp_data: any = {};
              tmp_data.uid = res.data[item.key];
              tmp_data.name = res.data[item.key];
              tmp_data.status = 'done';
              tmp_data.url = res.data['personCardUpPath'];
              tmp_data.response = {
                data: {
                  url: res.data['personCardUpPath'],
                  path: res.data[item.key],
                },
              };
              item.fileList.push(tmp_data);
            } else if (item.key == 'personCardDown') {
              item.fileList = [];
              let tmp_data: any = {};
              tmp_data.uid = res.data[item.key];
              tmp_data.name = res.data[item.key];
              tmp_data.status = 'done';
              tmp_data.url = res.data['personCardDownPath'];
              tmp_data.response = {
                data: {
                  url: res.data['personCardDownPath'],
                  path: res.data[item.key],
                },
              };
              item.fileList.push(tmp_data);
            } else if (item.key) {
              item.value = res.data[item.key];
            }
          });
          extraData.value.map((item) => {
            if (item.key == 'moreQualification1') {
              item.fileList = [];
              if (res.data[item.key]) {
                let tmp_data: any = {};
                tmp_data.uid = res.data[item.key];
                tmp_data.name = res.data[item.key];
                tmp_data.status = 'done';
                tmp_data.url = res.data['moreQualification1Path'];
                tmp_data.response = {
                  data: {
                    url: res.data['moreQualification1Path'],
                    path: res.data[item.key],
                  },
                };
                item.fileList.push(tmp_data);
              }
            } else if (item.key == 'moreQualification2') {
              item.fileList = [];
              if (res.data[item.key]) {
                let tmp_data: any = {};
                tmp_data.uid = res.data[item.key];
                tmp_data.name = res.data[item.key];
                tmp_data.status = 'done';
                tmp_data.url = res.data['moreQualification2Path'];
                tmp_data.response = {
                  data: {
                    url: res.data['moreQualification2Path'],
                    path: res.data[item.key],
                  },
                };
                item.fileList.push(tmp_data);
              }
            } else if (item.key == 'moreQualification3') {
              item.fileList = [];
              if (res.data[item.key]) {
                let tmp_data: any = {};
                tmp_data.uid = res.data[item.key];
                tmp_data.name = res.data[item.key];
                tmp_data.status = 'done';
                tmp_data.url = res.data['moreQualification3Path'];
                tmp_data.response = {
                  data: {
                    url: res.data['moreQualification3Path'],
                    path: res.data[item.key],
                  },
                };
                item.fileList.push(tmp_data);
              }
            } else if (item.key) {
              item.value = res.data[item.key];
            }
          });
          infoData.value.map((item) => {
            if (item.key) {
              item.value = res.data[item.key];
            }
          });
          sellteData.value.map((item) => {
            if (item.key == 'billDay') {
              item.value =
                typeof res.data[item.key] == 'string'
                  ? res.data[item.key].split(',')
                  : res.data[item.key];
              item.data = res.data.billType == 1 ? month_to_num() : week_to_num();
              item.desc =
                item.val == 1
                  ? '设置该商家每月几号进行结算，可多选，若当月没有设置的日期则该日不进行结算。'
                  : '设置该商家每周几进行结算，可多选，全部选中则为按天结算。';
            } else if (item.key) {
              item.value = res.data[item.key];
            }
          });
        } else {
          failTip(res.msg);
        }
      };

      function handleScaling(e, index) {
        if (index === null) {
          scaling.value = e;
        } else {
          dataSource.value[index].scaling = e;
          setProps({
            dataSource: dataSource.value,
          });
        }
      }

      function handleScalingBlur(e, index) {
        if (index === null) {
          scaling.value = e.target.value&&isNaN(Number(e.target.value))==false?(e.target.value>=1?1:e.target.value):0;
        } else {
          let data = JSON.parse(JSON.stringify(dataSource.value));
          data[index].scaling = e.target.value&&isNaN(Number(e.target.value))==false?(e.target.value>=1?1:e.target.value):0;
          dataSource.value = data;
          setProps({
            dataSource: data,
          });
        }
      }

      function handleSetScaling() {
        let data = JSON.parse(JSON.stringify(dataSource.value));
        data.map((items) => {
          items.scaling = scaling.value;
        });
        scaling.value = 0;
        dataSource.value = data;
        setProps({
          dataSource: data,
        });
      }

      return {
        scaling,
        handleScaling,
        handleScalingBlur,
        handleSetScaling,
        detail,
        billType,
        billDay,
        areaData,
        companyData,
        storeData,
        businessData,
        cardData,
        extraData,
        infoData,
        sellteData,
        rowKey,
        dataSource,
        standardTable,
        click_event,
        handleClick,
        getRealWidth,
        handleChange,
        valida,
        validaFlag,
        handleValida,
        tabStore,
        // dev_supplier_o2o-start
        getDictDataList,
        shop_type_list,
        // dev_supplier_o2o-end
      };
    },
  });
</script>
<style lang="less">
  .settle_store_edit {
    .ant-spin-container {
      .ant-table-container {
        .ant-table-body {
          height: auto !important;
        }
      }
    }

    .settle_store_detail_title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 25px;
      margin-bottom: 10px;
      padding-left: 0.5rem;
      color: #111;
      font-size: 14px;
      font-weight: bold;
      line-height: 24px;
      cursor: default;

      .settle_store_detail_title_btn {
        height: 28px;
        padding: 0 15px;
        border-radius: 3px;
        background: @primary-color;
        color: #fff;
        font-size: 13px;
        line-height: 27px;
        cursor: pointer;
      }
    }

    .table_title {
      display: flex;
      align-items: center;
      justify-content: center;

      .table_title_xing {
        margin-right: 4px;
        color: #f00;
      }

      .table_title_extra {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 4px;
        cursor: pointer;
      }
    }

    .slodon-basic-table {
      height: auto;
    }

    .slodon-collapse-container__header {
      height: 25px;
      padding: 0;
      border-bottom: none;

      .slodon-basic-title {
        color: #111;
        font-size: 14px;
        font-weight: bold;
      }
    }
  }
</style>
