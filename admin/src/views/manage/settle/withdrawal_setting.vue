<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <Spin :spinning="loading">
        <SldComHeader
          :title="'提现配置'"
        />
        <div class="site_info_scroll">
          <StandardTableRow
            width="100%"
            :data="info_data.data"
            @callback-event="callbackEvent"
            @submit-event="submitEvent"
          />
        </div>
      </Spin>
    </div>
  </div>
</template>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { Spin } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import StandardTableRow from '/@/components/StandardTableRow/index.vue';
  import { getSettingListApi, getSettingUpdateApi } from '/@/api/common/common';
  import { sucTip } from '/@/utils/utils';

  const info_data = reactive({
    data: [],
  });

  const loading = ref(true);

  // 获取数据
  const get_setting = async () => {
    try {
      const res = await getSettingListApi({ str: 'tl_withdraw_store_min,tl_withdraw_store_fee,tl_withdraw_store_limit' });
      if (res && res.state == 200) {
        info_data.data = []
        loading.value = false;
        for (let i in res.data) {
          let input_after_text = ''
          if (res.data[i].type == 1) {
            let numberMax = 0
            let numberMin = 0
            let precision = 0
            if(res.data[i].name=='tl_withdraw_store_fee'){
              input_after_text = '%'
              numberMax = 100
            }else{
              numberMax = 999999
            }
            if(res.data[i].name=='tl_withdraw_store_min'){
              numberMin = 0.01
              input_after_text = '元'
            }else{
              numberMin = 0
            }
            if(res.data[i].name=='tl_withdraw_store_limit'){
              input_after_text = '次/月'
              precision = 0
            }else{
              precision = 2
            }
            info_data.data.push({
              type: 'inputnum',
              label: res.data[i].title,
              desc: res.data[i].description,
              key: res.data[i].name,
              placeholder: '',
              min: numberMin,
              precision:precision,
              max: numberMax, 
              value: res.data[i].value,
              input_after_text:input_after_text,
            });
          }
        }
        if (info_data.data.length > 0) {
          info_data.data.push({
            type: 'button',
            showSubmit: true,
            submitText: '保存',
            showCancle: false,
          });
        }
      }
    } catch (error) {}
  };

  // 更新数据
  const callbackEvent = (item) => {
    let temp = info_data.data.filter((item) => item.key == item.key);
    if (temp.length > 0) {
      temp[0].value = item.val1;
    }
  };

  // 点击保存
  const submitEvent = async (val) => {
    try {
      loading.value = true;
      let res = await getSettingUpdateApi(val);
      if (res.state == 200) {
        loading.value = false;
        sucTip(res.msg);
        get_setting();

      }
    } catch (error) {}
  };

  onMounted(() => {
    get_setting();
  });
</script>
<style lang="less"></style>