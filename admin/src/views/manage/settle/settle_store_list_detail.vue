<template>
  <PageWrapper class="common_page_toolbar common_description settle_store_detail">
    <SldComHeader :type="1" :title="'店铺信息'" :back="true" />
    <template v-if="detail.storeId !== undefined">
      <Description
        v-if="detail.enterType == 1"
        size="middle"
        title="公司联系人信息"
        :bordered="true"
        :column="2"
        :data="detail"
        :schema="componaySchema"
      />
      <Description
        v-else
        size="middle"
        title="店铺信息"
        :bordered="true"
        :column="2"
        :data="detail"
        :schema="storeSchema"
      />
      <Description
        v-if="detail.enterType == 1"
        size="middle"
        title="营业执照信息"
        :bordered="true"
        :column="2"
        :data="detail"
        :schema="businessSchema"
      />
      <Description
        size="middle"
        :title="detail.enterType == 1 ? '法人身份信息' : '身份证信息'"
        :bordered="true"
        :column="2"
        :data="detail"
        :schema="cardSchema"
      />
      <Description
        v-if="
          detail.enterType == 1 &&
          (detail.moreQualification1Path ||
            detail.moreQualification2Path ||
            detail.moreQualification3Path)
        "
        size="middle"
        title="补充认证信息"
        :bordered="true"
        :column="2"
        :data="detail"
        :schema="extraSchema"
      />
      <Description
        size="middle"
        title="店铺经营信息"
        :bordered="true"
        :column="2"
        :data="detail"
        :schema="infoSchema"
      />
      <div class="settle_store_detail_title">经营类目</div>
      <BasicTable @register="standardTable" style="padding: 0 3px">
        <template #headerCell="{ column }">
          <template v-if="column.key == 'scaling'">
            <div class="table_title">
              <span class="table_title_xing">*</span>
              {{ column.customTitle }}
              <span class="table_title_extra">
                <Tooltip placement="bottom">
                  <template #title> 佣金比例在0～1之间 </template>
                  <AliSvgIcon iconName="iconwen" width="14px" height="14px" fillColor="#bfbbba" />
                </Tooltip>
              </span>
            </div>
          </template>
          <template v-else>
            {{ column.customTitle }}
          </template>
        </template>
        <template #bodyCell="{ column, text }">
          <template v-if="column.key">
            {{ text !== undefined && text !== null ? text : '--' }}
          </template>
        </template>
      </BasicTable>
    </template>
  </PageWrapper>
</template>
<script lang="ts">
  import { defineComponent, ref, onMounted, h } from 'vue';
  import { useRoute } from 'vue-router';
  import { Tabs, Tooltip, InputNumber, Popconfirm } from 'ant-design-vue';
  import { Description, DescItem } from '/@/components/Description/index';
  import { BasicTable, useTable } from '/@/components/Table';
  import { getStoreDetail } from '/@/api/manage/manage';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { failTip } from '/@/utils/utils';

  export default defineComponent({
    components: {
      [Tabs.name]: Tabs,
      [Tabs.TabPane.name]: Tabs.TabPane,
      Tooltip,
      InputNumber,
      Popconfirm,
      Description,
      BasicTable,
      SldComHeader,
    },
    setup() {
      const route = useRoute();
      const detail: any = ref({});
      const storeSchema: DescItem[] = [
        {
          field: 'enterTypeValue',
          label: '入驻类型',
          render: function (val) {
            return val ? val : '--';
          },
        },
        // dev_supplier_o2o-start
        {
          field: 'shopType',
          label: '店铺类型',
          render: function (text) {
            let val =  
            // dev_supplier-start
            text == 3 ? '供应商店铺' : 
            // dev_supplier-end
            
            '普通店铺';
            return val
          },
        },
         // dev_supplier_o2o-end
        {
          field: 'areaInfo',
          label: '所在地',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'companyAddress',
          label: '详细地址',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'contactName',
          label: '联系人',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'contactPhone',
          label: '联系人手机号',
          render: function (val) {
            return val ? val : '--';
          },
        },
      ];
      const componaySchema: DescItem[] = [
        {
          field: 'enterTypeValue',
          label: '入驻类型',
          render: function (val) {
            return val ? val : '--';
          },
        },
        // dev_supplier_o2o-start
        {
          field: 'shopType',
          label: '店铺类型',
          render: function (text) {
            let val =  
            // dev_supplier-start
            text == 3 ? '供应商店铺' : 
            // dev_supplier-end
            
            '普通店铺';
            return val
          },
        },
         // dev_supplier_o2o-end
        {
          field: 'companyName',
          label: '公司名称',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'areaInfo',
          label: '所在地',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'companyAddress',
          label: '详细地址',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'contactName',
          label: '联系人',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'contactPhone',
          label: '联系人手机号',
          render: function (val) {
            return val ? val : '--';
          },
        },
      ];
      const businessSchema: DescItem[] = [
        {
          field: 'businessLicenseImagePath',
          label: '营业执照',
          render: (val) => {
            return h('div', { class: 'form_list_img_item' }, [
              h('img', { src: val, class: 'form_list_img_item_img' }),
              h('div', { class: 'form_list_img_item_right' }, [
                h('div', { class: 'form_list_img_item_pre' }, [
                  h('div', { class: 'form_list_img_item_pre_arrow' }),
                  h('img', { src: val, class: 'form_list_img_item_pre_img' }),
                ]),
              ]),
            ]);
          },
        },
      ];
      const cardSchema: DescItem[] = [
        {
          field: 'personCardUpPath',
          label: '身份证正面',
          render: (val) => {
            return h('div', { class: 'form_list_img_item' }, [
              h('img', { src: val, class: 'form_list_img_item_img' }),
              h('div', { class: 'form_list_img_item_right' }, [
                h('div', { class: 'form_list_img_item_pre' }, [
                  h('div', { class: 'form_list_img_item_pre_arrow' }),
                  h('img', { src: val, class: 'form_list_img_item_pre_img' }),
                ]),
              ]),
            ]);
          },
        },
        {
          field: 'personCardDownPath',
          label: '身份证反面',
          render: (val) => {
            return h('div', { class: 'form_list_img_item' }, [
              h('img', { src: val, class: 'form_list_img_item_img' }),
              h('div', { class: 'form_list_img_item_right' }, [
                h('div', { class: 'form_list_img_item_pre' }, [
                  h('div', { class: 'form_list_img_item_pre_arrow' }),
                  h('img', { src: val, class: 'form_list_img_item_pre_img' }),
                ]),
              ]),
            ]);
          },
        },
      ];
      const extraSchema: DescItem[] = [
        {
          field: 'moreQualification1Path',
          label: '补充认证资质',
          render: (val) => {
            return h('div', { class: 'form_list_img_item' }, [
              h('img', { src: val, class: 'form_list_img_item_img' }),
              h('div', { class: 'form_list_img_item_right' }, [
                h('div', { class: 'form_list_img_item_pre' }, [
                  h('div', { class: 'form_list_img_item_pre_arrow' }),
                  h('img', { src: val, class: 'form_list_img_item_pre_img' }),
                ]),
              ]),
            ]);
          },
          show: (item) => {
            return item.moreQualification1Path ? true : false;
          },
        },
        {
          field: 'moreQualification2Path',
          label: '补充认证资质',
          render: (val) => {
            return h('div', { class: 'form_list_img_item' }, [
              h('img', { src: val, class: 'form_list_img_item_img' }),
              h('div', { class: 'form_list_img_item_right' }, [
                h('div', { class: 'form_list_img_item_pre' }, [
                  h('div', { class: 'form_list_img_item_pre_arrow' }),
                  h('img', { src: val, class: 'form_list_img_item_pre_img' }),
                ]),
              ]),
            ]);
          },
          show: (item) => {
            return item.moreQualification2Path ? true : false;
          },
        },
        {
          field: 'moreQualification3Path',
          label: '补充认证资质',
          render: (val) => {
            return h('div', { class: 'form_list_img_item' }, [
              h('img', { src: val, class: 'form_list_img_item_img' }),
              h('div', { class: 'form_list_img_item_right' }, [
                h('div', { class: 'form_list_img_item_pre' }, [
                  h('div', { class: 'form_list_img_item_pre_arrow' }),
                  h('img', { src: val, class: 'form_list_img_item_pre_img' }),
                ]),
              ]),
            ]);
          },
          show: (item) => {
            return item.moreQualification3Path ? true : false;
          },
        },
      ];
      const infoSchema: DescItem[] = [
        {
          field: 'storeName',
          label: '店铺名称',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'storeGradeName',
          label: '店铺等级',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'openTime',
          label: '开店时长',
          render: function (val) {
            return val ? val + '年' : '--';
          },
        },
        {
          field: 'payAmount',
          label: '已付年费',
          render: function (val) {
            return '¥' + (val ? val : 0);
          },
        },
        {
          field: 'paymentName',
          label: '付款方式',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'billCycle',
          label: '结算周期',
          render: function (val) {
            return val ? val : '--';
          },
        },
      ];

      const rowKey: any = ref('bindId');
      const columns = [
        {
          title: '一级类目',
          dataIndex: 'goodsCateName1',
          width: 100,
        },
        {
          title: '二级类目',
          dataIndex: 'goodsCateName2',
          width: 100,
        },
        {
          title: '三级类目',
          dataIndex: 'goodsCateName3',
          width: 100,
        },
        {
          title: '佣金比例',
          dataIndex: 'scaling',
          width: 100,
        },
      ];
      const dataSource: any = ref([]);
      const [standardTable] = useTable({
        columns: columns,
        dataSource: dataSource,
        pagination: false,
        bordered: true,
        striped: false,
        showIndexColumn: true,
        canResize: false,
      });

      onMounted(() => {
        get_store_detail();
      });

      //获取店铺详情
      const get_store_detail = async () => {
        const res: any = await getStoreDetail({ storeId: route.query.id });
        if (res.state == 200 && res.data) {
          detail.value = res.data;
          dataSource.value = res.data.storeGoodsCateVOList ? res.data.storeGoodsCateVOList : [];
        } else {
          failTip(res.msg);
        }
      };

      return {
        detail,
        storeSchema,
        componaySchema,
        businessSchema,
        cardSchema,
        extraSchema,
        infoSchema,
        rowKey,
        dataSource,
        standardTable,
      };
    },
  });
</script>
<style lang="less">
  .settle_store_detail {
    .slodon-basic-table{
      height: auto !important;
    }

    .ant-spin-container {
      .ant-table-container {
        .ant-table-body {
          height: auto !important;
        }
      }
    }

    .settle_store_detail_title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 25px;
      margin-bottom: 10px;
      padding-left: 0.5rem;
      color: #111;
      font-size: 14px;
      font-weight: bold;
      line-height: 24px;
      cursor: default;
    }

    .table_title {
      display: flex;
      align-items: center;
      justify-content: center;

      .table_title_xing {
        margin-right: 4px;
        color: #f00;
      }

      .table_title_extra {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 4px;
        cursor: pointer;
      }
    }

    .slodon-collapse-container__header {
      height: 25px;
      padding: 0;
      border-bottom: none;

      .slodon-basic-title {
        color: #111;
        font-size: 14px;
        font-weight: bold;
      }
    }
  }
</style>
