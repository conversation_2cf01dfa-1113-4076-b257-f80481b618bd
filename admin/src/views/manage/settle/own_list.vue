<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader :type="1" :title="'自营店铺管理'" />
      <BasicTable @register="standardTable" style="padding: 0">
        <template #tableTitle>
          <div class="toolbar">
            <div class="toolbar_btn" @click="handleClick(null, 'add')">
              <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#fff" />
              <span>新增自营店铺</span>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record, text, index }">
          <template v-if="column.key == 'action'">
            <template v-if="record.isInner == 1"> -- </template>
            <template v-else>
              <span class="common_page_edit" @click="handleClick(record, 'edit')">编辑</span>
              <span class="common_page_edit" @click="handleClick(record, 'reset')">重置密码</span>
              <Popconfirm
                v-if="record.state != 1"
                title="删除后不可恢复，是否确定删除？"
                @confirm="handleClick(record, 'del')"
              >
                <span class="common_page_edit">删除</span>
              </Popconfirm>
            </template>
          </template>
          <template v-else-if="column.key == 'state'">
            <Switch
              :checked="text == '1' ? true : false"
              checked-children="开启"
              un-checked-children="关闭"
              @change="(e) => handleChange(e, record, index)"
            />
          </template>
          <template v-else-if="column.key == 'capacitySize'">
            <div style="display: flex; align-items: center; justify-content: center">
              <template v-if="!record.editFlag">
                {{ text ? text + 'G' : '默认' }}
                <span
                  style="
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-left: 10px;
                    cursor: pointer;
                  "
                  @click="handleOperateSize('edit', record, index)"
                >
                  <AliSvgIcon iconName="iconedit" width="14px" height="14px" fillColor="#fa6f1e" />
                </span>
              </template>
              <template v-else>
                <InputNumber
                  :min="1"
                  :max="99999999"
                  :precision="0"
                  :value="record.showCapacitySize < 1 ? record.showCapacitySize : undefined"
                  @change="(e) => handleOperateSize(e, record, index)"
                />
                <span
                  style="
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-left: 10px;
                    cursor: pointer;
                  "
                  @click="handleOperateSize('save', record, index)"
                >
                  <AliSvgIcon
                    iconName="iconxuanzhong"
                    width="14px"
                    height="14px"
                    fillColor="#fa6f1e"
                  />
                </span>
                <span
                  style="
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-left: 10px;
                    cursor: pointer;
                  "
                  @click="handleOperateSize('close', record, index)"
                >
                  <AliSvgIcon iconName="iconguanbi" width="14px" height="14px" fillColor="#fa6f1e" />
                </span>
              </template>
            </div>
          </template>
          <template v-else-if="column.key">
            {{ text ? text : '--' }}
          </template>
        </template>
      </BasicTable>
      <SldModal
        :width="width"
        :title="title"
        :visible="visible"
        :content="content"
        :confirmBtnLoading="confirmBtnLoading"
        :showFoot="true"
        @callback-event="handleCallback"
        @radio-change-event="handleCallback"
        @cancle-event="handleCancle"
        @click-sms-event="handleSmsCancle"
        @confirm-event="handleConfirm"
      />
    </div>
  </div>
</template>
<script setup>
  import { ref,onMounted,unref } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import SldModal from '@/components/SldModal/index.vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import { Popconfirm, Switch, InputNumber } from 'ant-design-vue';
  import {
    getOwnList,
    getOwnDetail,
    addOwn,
    editOwn,
    delOwn,
    lockOwn,
    resetOwn,
    updateCapacitySize,
  } from '/@/api/manage/manage';
  // dev_supplier_o2o-start
  import {
    getDictDataApi
  } from '/@/api/common/common';
  // dev_supplier_o2o-end
  import AreaJson from '/@/assets/json/area.json';
  import { week_to_num, month_to_num, base64Encrypt, failTip, sucTip } from '/@/utils/utils';
  import { validatorMemPwd, mobile_reg, validatorVendorEmail,checkSmsCode } from '/@/utils/validate';
  import { useUserStore } from '/@/store/modules/user';

  const userStore = useUserStore();

  const [standardTable, { reload, getDataSource, setTableData }] = useTable({
    api: (arg) => getOwnList({ ...arg }),
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    columns: [
      {
        title: '店铺名称',
        dataIndex: 'storeName',
        width: 100,
      },
      {
        title: '店铺账号',
        dataIndex: 'vendorName',
        width: 100,
      },
      {
        title: '电话',
        dataIndex: 'contactPhone',
        width: 100,
      },
      // dev_supplier_o2o-start
      {
        title: '店铺类型',
        dataIndex: 'shopType',
        width: 100,
        customRender: ({ text }) => {
          let val =  
          // dev_supplier-start
          text == 3 ? '供应商店铺' : 
          // dev_supplier-end
          
          '普通店铺';
          return val
         
        },
      },
      // dev_supplier_o2o-end
      {
        title: '店铺状态',
        dataIndex: 'state',
        width: 100,
      },
      {
        title: '素材中心容量',
        dataIndex: 'capacitySize',
        width: 100,
      },
    ],
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
    useSearchForm: true,
    formConfig: {
      schemas: [
        {
          field: 'storeName',
          component: 'Input',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            maxlength: 10,
            placeholder: '请输入店铺名称',
            size: 'default',
          },
          label: '店铺名称',
          labelWidth: 70,
        },
        // dev_supplier_o2o-start
        {
          field: 'shopType',
          component: 'Select',
          colProps: { span: 6, style: 'width:240px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            placeholder: '请选择店铺类型',
            options: [
              { key: 0, label: '全部', value: '' },
              { key: 1, label: '普通店铺', value: '1' },
              // dev_supplier-start
              { key: 3, label: '供应商店铺', value: '3' },
              // dev_supplier-end
              
            ],
            size: 'default',
          },
          label: '店铺类型',
          labelWidth: 80,
        },
        // dev_supplier_o2o-end
        {
          field: 'state',
          component: 'Select',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            placeholder: '请选中店铺状态',
            size: 'default',
            options: [
              { key: 0, label: '全部', value: '' },
              { key: 1, label: '开启', value: '1' },
              { key: 2, label: '关闭', value: '2' },
            ],
          },
          label: '店铺状态',
          labelWidth: 80,
        },
      ],
    },
    bordered: true,
    striped: false,
    showIndexColumn: true,
  });

  const width = ref(600);
  const title = ref('');
  const visible = ref(false);
  const content = ref([]);
  const confirmBtnLoading = ref(false);
  const operate_item = ref({});
  const operate_type = ref('');
  const click_event = ref(false);

  const shop_type_list = ref([])

  const operate_add_edit = ref([
    {
      type: 'input',
      label: `店铺名称`,
      name: 'storeName',
      placeholder: `请输入店铺名称`,
      extra: '最多输入10个字',
      maxLength: 10,
      showCount: true,
      rules: [
        {
          required: true,
          whitespace: true,
          message: '请输入店铺名称',
        },
      ],
      callback: true,
    },
    {
      type: 'input',
      label: `店铺联系人`,
      name: 'contactName',
      placeholder: `请输入店铺联系人`,
      extra: '最多输入5个字',
      maxLength: 5,
      showCount: true,
      rules: [
        {
          required: true,
          whitespace: true,
          message: '请输入店铺联系人',
        },
      ],
      callback: true,
    },
    {
      type: 'input',
      label: `手机号`,
      name: 'contactPhone',
      placeholder: `请输入手机号`,
      maxLength: 11,
      rules: [
        {
          required: true,
          whitespace: true,
          message: '请输入手机号',
        },
        {
          pattern: mobile_reg,
          message: '请输入正确的手机号',
        },
      ],
      callback: true,
    },
    // dev_supplier_o2o-start
    {
      type: 'radio_single',
      label: `店铺类型`,
      name: 'shopType',
      data: [
        { value: '1', label: `普通店铺` },
      ],
      initialValue: '1',
      rules: [
        {
          required: true,
          message: '请选择店铺类型',
        },
      ],
      callback: true,
    },
    // dev_supplier_o2o-end
    {
      type: 'cascader_common',
      label: `店铺地址`,
      name: 'area',
      data: AreaJson,
      fieldNames: { label: 'regionName', value: 'regionCode', children: 'children' },
      placeholder: `请选择店铺地址`,
      initialValue: [],
      wrapperCol: true,
      rules: [
        {
          required: true,
          message: '请选择店铺地址',
        },
      ],
      callback: true,
      
    },
    {
      type: 'textarea',
      label: `店铺详细地址`,
      name: 'address',
      placeholder: `请输入店铺详细地址`,
      extra: '最多输入50字',
      maxLength: 50,
      minRows: 1,
      maxRows: 1,
      showCount: true,
      rules: [
        {
          required: true,
          whitespace: true,
          message: '请输入店铺详细地址',
        },
      ],
      item_width: '',
      
      callback: true,
    },
    {
      type: 'input',
      label: `店铺账号`,
      name: 'vendorName',
      placeholder: `请输入店铺账号`,
      extra: '6-20位数字与字母组合',
      maxLength: 20,
      rules: [
        {
          required: true,
          whitespace: true,
          message: '请输入店铺账号',
        },
      ],
      callback: true,
    },
    {
      type: 'input',
      input_type: 'password',
      label: `登陆密码`,
      name: 'vendorPassword',
      placeholder: `请设置6~20位字母、数字或符号组成的密码`,
      rules: [
        {
          required: true,
          whitespace: true,
          message: '请输入登陆密码',
        },
      ],
      callback: true,
    },
    {
      type: 'input',
      label: `邮箱`,
      name: 'vendorEmail',
      extra:'主要用于商家接收消息',
      placeholder: `请输入正确的邮箱`,
      rules: [
        {
          validator: (rule, value) => validatorVendorEmail(rule, value),
        },
      ],
      callback: true,
    },
    {
      type: 'radio_select',
      label: `结算方式`,
      name: 'billType',
      data: [
        { key: '1', value: `按月结算` },
        { key: '2', value: `按周结算` },
      ],
      initialValue: '1',
      callback: true,
    },
    {
      type: 'checkboxgroup',
      label: `结算日`,
      extra: `设置该商家每月几号进行结算，可多选，若当月没有设置的日期则该日不进行结算。`,
      name: 'billDays',
      initialValue: [],
      sldOptions: month_to_num(),
      callback: true,
    },
  ]); //添加、编辑商品标签弹窗数据
  

  const operate_reset = ref([
    {
      type: 'input',
      input_type: 'password',
      label: `新密码`,
      name: 'newPassword',
      placeholder: `请输入新密码`,
      rules: [
        {
          required: true,
          whitespace: true,
          message: '请输入新密码',
        },
        {
          validator: (rule, value) => validatorMemPwd(rule, value),
        },
      ],
      callback: true,
    },
    {
      type: 'input',
      input_type: 'password',
      label: `确认新密码`,
      name: 'newPasswordCfm',
      placeholder: `请输入确认新密码`,
      rules: [
        {
          required: true,
          whitespace: true,
          message: '请输入确认新密码',
        },
        {
          validator: (rule, value) => validatorMemPwd(rule, value),
        },
      ],
      callback: true,
    },
  ]); //重置密码弹窗数据

  //弹窗回调事件
  function handleCallback(item, index) {
    if (index != undefined) {
      content.value[index].initialValue = item.toString();
      
        let temp = content.value.filter((items) => items.name == 'billDays');
        if (temp.length > 0) {
          temp[0].sldOptions = item == '1' ? month_to_num() : week_to_num();
          temp[0].initialValue =
            item == '1'
              ? temp[0].month_initialValue
                ? temp[0].month_initialValue
                : []
              : temp[0].week_initialValue
              ? temp[0].week_initialValue
              : [];
          temp[0].extra = item == '1'
            ? '设置该商家每月几号进行结算，可多选，若当月没有设置的日期则该日不进行结算。'
            : '设置该商家每周几进行结算，可多选，全部选中则为按天结算。'
        }
      
    } else if (item.contentItem) {
      let days_type;
      let type_temp = content.value.filter((items) => items.name == 'billType');
      if (type_temp.length > 0) {
        days_type = type_temp[0].initialValue;
      }
      let temp = content.value.filter((items) => items.name == item.contentItem.name);
      if (temp.length > 0) {
        if (item.contentItem.name == 'billDays') {
          if (days_type == '1') {
            temp[0].month_initialValue = item.val1;
            temp[0].extra = '设置该商家每月几号进行结算，可多选，若当月没有设置的日期则该日不进行结算。';
          } else {
            temp[0].week_initialValue = item.val1;
            temp[0].extra = '设置该商家每周几进行结算，可多选，全部选中则为按天结算。';
          }
        }
        temp[0].initialValue = item.val1;
      }
    }
  }

  const handleSmsCancle = (info)=> {
    }

  //表格点击回调事件
  const handleClick= async(item, type)=> {
    if (click_event.value) return;
    operate_type.value = type;
    operate_item.value = item ? item : {};
    if (type == 'add') {
      // dev_supplier_o2o-start
      await getDictDataList();
      // dev_supplier_o2o-end
      title.value = '新增自营店铺';
      visible.value = true;
      let obj = {
        type: 'verify_code', //验证码
        label: '短信验证码',
        name: 'code',
        verification:true, //为true的话调的是另一个验证码接口
        mobile: 'contactPhone', //关联的手机号字段
        placeholder: '请输入短信验证码',
        extra: ``,
        initialValue: '',
        maxLength: 6, //最多字数
        rules: [
          {
            required: true,
            whitespace: true,
            message: '请输入短信验证码',
          },
          {
            // @ts-ignore
            validator: async (rule, value) => {
              await checkSmsCode(rule, value);
            },
            trigger: 'change',
          },
        ],
        callback: true,
      }
      content.value = JSON.parse(JSON.stringify(operate_add_edit.value));
      for(let i in content.value){
        if(content.value[i].name == 'contactPhone'){
          content.value.splice(Number(i)+1,0,obj)
          break
        }
      }
      content.value.forEach(item=>{
        if(item.name == 'vendorEmail'){
          item.rules = [
            { validator: (rule, value) => validatorVendorEmail(rule, value) },
          ];
        }
      })
    } else if (type == 'edit') {
      // dev_supplier_o2o-start
      await getDictDataList();
      if(item.shopType!=1&&shop_type_list.value.indexOf(String(item.shopType))==-1){
        
        // dev_supplier-start
        if(item.shopType==3){
          failTip('供应商模块已关闭，所以该店铺不支持编辑')
        }
        // dev_supplier-end
        return
      }
      // dev_supplier_o2o-end
      click_event.value = true;
      get_store_detail({ storeId: item.storeId });
    } else if (type == 'reset') {
      title.value = '重置密码';
      visible.value = true;
      content.value = JSON.parse(JSON.stringify(operate_reset.value));
      content.value[0].rules = [
        { required: true, whitespace: true, message: '请输入新密码' },
        { validator: (rule, value) => validatorMemPwd(rule, value) },
      ];
      content.value[1].rules = [
        { required: true, whitespace: true, message: '请输入确认新密码' },
        { validator: (rule, value) => validatorMemPwd(rule, value) },
      ];
    } else if (type == 'del') {
      operate_role({ storeId: item.storeId }, null);
    }
  }

  const get_store_detail = async (params) => {
    const res = await getOwnDetail(params);
    click_event.value = false;
    if (res.state == 200) {
      title.value = '编辑自营店铺';
      visible.value = true;
      let data = JSON.parse(JSON.stringify(operate_add_edit.value));
      let show_map = false;
      data = data.filter(
        (items) => items.name != 'vendorName' && items.name != 'vendorPassword',
      );
      data.map((items) => {
        if (items.name == 'storeName') {
          items.disable = true;
          items.initialValue = res.data[items.name] ? res.data[items.name] : undefined;
        } 
        
        else if (items.name == 'address') {
          if (res.data.shopType == 2) {
            items.item_width = '400px';
            
          }
          items.initialValue = res.data[items.name] ? res.data[items.name] : undefined;
        } else if (items.name == 'area') {
          items.initialValue = [
            res.data['provinceCode'],
            res.data['cityCode'],
            res.data['areaCode'],
          ];
        } else if (items.name == 'radio_select') {
          items.initialValue =
            res.data[items.name] != undefined ? res.data[items.name].toString() : '1';
        } else if (items.name == 'vendorEmail') {
          items.initialValue = res.data[items.name] ? res.data[items.name] : undefined;
          items.rules = [
            { validator: (rule, value) => validatorVendorEmail(rule, value) },
          ];
        } else if (items.name == 'billType') {
          items.initialValue = res.data[items.name] ? res.data[items.name].toString() : '1';
          let day_temp = data.filter((items) => items.name == 'billDays');
          if (day_temp.length > 0) {
            day_temp[0].sldOptions = items.initialValue == '1' ? month_to_num() : week_to_num();
          }
        } else if (items.name == 'billDays') {
          items.initialValue = res.data['billDay'] ? res.data['billDay'].split(',') : [];
          if (res.data['billType'] == '1') {
            items.month_initialValue = items.initialValue;
            items.extra = '设置该商家每月几号进行结算，可多选，若当月没有设置的日期则该日不进行结算。';
          } else if (res.data['billType'] == '2') {
            items.week_initialValue = items.initialValue;
            items.extra = '设置该商家每周几进行结算，可多选，全部选中则为按天结算。';
          }
        } else {
          items.initialValue = res.data[items.name] ? res.data[items.name] : undefined;
        }
      });
      
      content.value = data;
    } else {
      failTip(res.msg);
    }
  };

  //弹窗取消事件
  function handleCancle() {
    visible.value = false;
    content.value = [];
    operate_item.value = '';
    }

  //弹窗确认事件
  function handleConfirm(val) {
    if (operate_type.value == 'add' || operate_type.value == 'edit') {
      if (operate_type.value == 'edit') {
        val.storeId = operate_item.value.storeId;
      }
      val.provinceCode = val.area[0];
      val.cityCode = val.area[1];
      val.areaCode = val.area[2];

      let areaInfo = '';
      let first_temp = AreaJson.filter(first_item => first_item.regionCode == val.area[0])[0];
      areaInfo += first_temp.regionName;
      let second_temp = first_temp.children.filter(second_item => second_item.regionCode == val.area[1])[0];
      areaInfo += second_temp.regionName;
      let third_temp = second_temp.children.filter(third_item => third_item.regionCode == val.area[2])[0];
      areaInfo += third_temp.regionName;
      val.areaInfo = areaInfo; //省市区地址

      val.vendorPassword = base64Encrypt(val.vendorPassword);
      val.billDays = val.billDays.join(',');
      delete val.area;
    } else if (operate_type.value == 'reset') {
      val.vendorId = operate_item.value.vendorId;
      val.newPassword = base64Encrypt(val.newPassword);
      val.newPasswordCfm = base64Encrypt(val.newPasswordCfm);
    }
    operate_role(val, null);
  }

  //商品标签操作
  const operate_role = async (params, index) => {
    
    confirmBtnLoading.value = true;
    let res = {};
    if (operate_type.value == 'add') {
      res = await addOwn(params);
    } else if (operate_type.value == 'edit') {
      res = await editOwn(params);
    } else if (operate_type.value == 'switch') {
      res = await lockOwn(params);
    } else if (operate_type.value == 'reset') {
      res = await resetOwn(params);
    } else if (operate_type.value == 'del') {
      res = await delOwn(params);
    }
    confirmBtnLoading.value = false;
    if (res.state == 200) {
      sucTip(res.msg);
      handleCancle();
      if (index !== null) {
        let dataSource = getDataSource();
        dataSource[index].state = params.state;
        setTableData(dataSource);
      } else {
        reload();
      }
    } else {
      failTip(res.msg);
    }
  };

  //开关事件
  function handleChange(val, item, index) {
    operate_type.value = 'switch';
    operate_role(
      {
        storeId: item.storeId,
        state: val ? 1 : 2,
      },
      index,
    );
  }

  //设置素材中心容量
  function handleOperateSize(type, item, index) {
    const dataSource = getDataSource();
    let items = dataSource[index];
    if (type == 'edit') {
      items.editFlag = true;
      items.showCapacitySize = items.capacitySize;
    } else if (type == 'save') {
      update_capacity_size(
        {
          storeId: item.storeId,
          capacitySize: items.showCapacitySize ? items.showCapacitySize : 1,
        },
        index,
      );
      return;
    } else if (type == 'close') {
      items.editFlag = false;
      items.showCapacitySize = '';
    } else {
      items.showCapacitySize = type;
    }
    setTableData(dataSource);
  }

  //修改素材中心容量
  const update_capacity_size = async (params, index) => {
    const res = await updateCapacitySize(params);
    if (res.state == 200) {
      const dataSource = getDataSource();
      let items = dataSource[index];
      items.editFlag = false;
      items.capacitySize = params.capacitySize ? params.capacitySize : 1;
      setTableData(dataSource);
      sucTip(res.msg);
    } else {
      failTip(res.msg);
    }
  };

  // dev_supplier_o2o-start
  // 获取店铺类型
  const getDictDataList = async()=> {
    shop_type_list.value = []
    let res = await getDictDataApi({dictCode:'shop_type'})
    if(res.state == 200){
      if(res.data.length>0){
        let obj = []
        res.data.forEach(item=>{
          obj.push({ value: item.dictKey, label: item.dictValue })
          shop_type_list.value.push(String(item.dictKey))
        })
        operate_add_edit.value.forEach(item=>{
          if(item.name == 'shopType'){
            item.data = obj
            item.initialValue = res.data[0].dictKey
          }
        })
      }
    }
  }
  // dev_supplier_o2o-end

  onMounted(() => {
    // dev_supplier_o2o-start
    getDictDataList();
    // dev_supplier_o2o-end
  });
</script>
<style lang="less" scoped></style>
