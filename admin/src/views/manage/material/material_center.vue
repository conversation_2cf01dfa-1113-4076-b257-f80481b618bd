<template>
  <div class="section_padding">
    <div class="section_padding_back_add material_center">
      <SldComHeader :type="1" :title="'素材中心'" />
      <div class="top_clean_btn" @click="handleOperate('clean', null, null, null)">清理素材</div>
      <div style="width: 100%; height: 5px"></div>
      <a-tabs v-model:activeKey="tabIndex" type="card" @change="handleChange">
        <a-tab-pane :key="1" tab="图片" />
        <a-tab-pane :key="2" tab="视频" />
      </a-tabs>
      <div class="section_padding_tab_top"></div>
      <template v-if="tabIndex == 1">
        <div class="flex_row_start_start material_center_main">
          <div
            class="flex_column_between_center material_center_left"
            :style="{ height: category_height }"
          >
            <div style="width: 100%">
              <div class="material_center_left_title">素材类目</div>
              <div class="material_center_left_list">
                <div class="flex_row_center_center material_center_left_list_search">
                  <Input
                    placeholder="搜索类目名称"
                    :maxlength="20"
                    size="small"
                    @change="(e) => handleOperate('category', e.target.value, e.type, null)"
                    allowClear
                  />
                  <span @click="handleOperate('searchCategory', null, null, null)">搜索</span>
                </div>
                <div class="material_center_left_list_category">
                  <template v-for="(item, index) in category_list" :key="index">
                    <div
                      class="material_center_left_list_category_item"
                      :class="category_selected == item.categoryId ? 'active' : ''"
                      @click="handleClickCategory('select', item.categoryId, null)"
                    >
                      <div class="flex_row_start_center" style="width: 100%">
                        <div
                          v-if="item.subCategoryList.length > 0"
                          class="material_center_left_list_category_item_arrow"
                          @click="(e) => handleClickCategory('show', index, e)"
                        >
                          <span style="position: relative; bottom: 2px">
                            <Icon
                              v-if="!item.showSubCategoryList"
                              icon="ant-design:caret-right-outlined"
                              :style="{ fontSize: 13 }"
                            />
                            <Icon
                              v-else
                              icon="ant-design:caret-down-outlined"
                              :style="{ fontSize: 13 }"
                            />
                          </span>
                        </div>
                        <div v-else class="material_center_left_list_category_item_arrow"></div>
                        <div
                          class="material_center_left_list_category_item_name"
                          :title="item.categoryName"
                          >{{ item.categoryName }}</div
                        >
                        <div
                          class="material_center_left_list_category_item_num"
                          :class="item.isInner == 1 ? 'no_hover' : ''"
                          >{{ item.fileCount }}</div
                        >
                        <div
                          class="flex_row_end_center material_center_left_list_category_item_btns"
                          :class="item.isInner == 1 ? 'no_hover' : ''"
                        >
                          <Icon
                            icon="ant-design:edit-outlined"
                            @click="(e) => handleOperate('editCategory', index, null, e)"
                          />
                          <Popconfirm
                            title="确认删除该素材分类？删除后该分类下的素材将移动至默认分类下。"
                            @confirm="(e) => handleOperate('delCategory', index, null, e)"
                          >
                            <Icon icon="ant-design:delete-outlined" @click="(e) => clickStop(e)" />
                          </Popconfirm>
                        </div>
                      </div>
                    </div>
                    <template v-if="item.showSubCategoryList">
                      <div
                        v-for="(items, indexs) in item.subCategoryList"
                        :key="indexs"
                        class="material_center_left_list_category_item material_center_left_list_category_child"
                        :class="category_selected == items.categoryId ? 'active' : ''"
                        @click="handleClickCategory('select', items.categoryId, null)"
                      >
                        <div class="flex_row_start_center" style="width: 100%">
                          <div
                            class="material_center_left_list_category_item_name"
                            :title="items.categoryName"
                            >{{ items.categoryName }}</div
                          >
                          <div
                            class="material_center_left_list_category_item_num"
                            :class="items.isInner == 1 ? 'no_hover' : ''"
                            >{{ items.fileCount }}</div
                          >
                          <div
                            class="flex_row_end_center material_center_left_list_category_item_btns"
                            :class="items.isInner == 1 ? 'no_hover' : ''"
                          >
                            <Icon
                              icon="ant-design:edit-outlined"
                              @click="(e) => handleOperate('editCategory', index, indexs, e)"
                            />
                            <Popconfirm
                              title="确认删除该素材分类？删除后该分类下的素材将移动至默认分类下。"
                              @confirm="(e) => handleOperate('delCategory', index, indexs, e)"
                            >
                              <Icon icon="ant-design:delete-outlined" @click="(e) => clickStop(e)" />
                            </Popconfirm>
                          </div>
                        </div>
                      </div>
                    </template>
                  </template>
                </div>
              </div>
            </div>
            <div class="flex_row_center_center material_center_left_add">
              <div
                class="material_center_left_add_btn"
                @click="handleOperate('addCategory', null, null, null)"
                >添加分类</div
              >
            </div>
          </div>
          <div class="material_center_right">
            <div class="flex_row_start_center material_center_right_search">
              <div
                class="flex_row_center_center material_center_right_search_upload"
                @click="handleOperate('upload', null, null, null)"
              >
                <AliSvgIcon iconName="iconxinzeng" width="14px" height="14px" fillColor="#fff" />
                <span>上传图片</span>
              </div>
              <div class="material_center_right_search_form">
                <BasicForm @register="imgsRegister" @submit="handleSubmit" @reset="handleReset" />
              </div>
            </div>
            <div
              v-if="data.list && data.list.length > 0"
              class="flex_row_start_center material_center_right_operate"
            >
              <Checkbox
                :checked="checked_all"
                @change="(e) => handleOperate('all', e.target.checked, null, null)"
                >全选</Checkbox
              >
              <div
                class="flex_row_center_center material_center_right_operate_move"
                @click="(e) => handleOperate('moveto', e, null, null)"
              >
                <AliSvgIcon iconName="icondaoru" width="14px" height="14px" fillColor="#fff" />
                <span>批量移动至</span>
              </div>
            </div>
            <div class="material_center_right_list" :style="{ height: data_height }">
              <div
                v-if="data.list && data.list.length > 0"
                class="flex_row_start_start right_goods_item"
                :style="{ width: goods_total_width + 'px', minWidth: '720px' }"
              >
                <div
                  v-for="(item, index) in data.list"
                  :key="index"
                  class="flex_column_start_start item"
                  :class="item.checked ? 'active' : ''"
                  :style="{ minWidth: '134px', flex: 'unset', width: file_item_width + 'px' }"
                >
                  <div
                    class="flex_row_center_center img_wrap"
                    @click="handleOperate('view', item.fileUrl, null, null)"
                    :style="{
                      width: file_item_width - 2 + 'px',
                      height: file_item_width - 2 + 'px',
                      backgroundImage: 'url(' + item.fileUrl + ')',
                    }"
                  >
                    <img
                      v-if="item.checked"
                      class="checked"
                      @click="(e) => handleOperate('checked', e, index, null)"
                      src="@/assets/images/files_checked.png"
                    />
                    <img
                      v-else
                      class="checked"
                      @click="(e) => handleOperate('checked', e, index, null)"
                      src="@/assets/images/files_check.png"
                    />
                    <div class="file_state">
                      <text :class="item.useNum > 0 ? 'file_state_on' : 'file_state_off'">{{
                        item.useNum > 0 ? '已引用' : '未引用'
                      }}</text>
                      <img v-if="item.useNum > 0" src="@/assets/images/files_on.png" />
                      <img v-else src="@/assets/images/files_off.png" />
                    </div>
                    <div class="file_size">{{ item.width }}*{{ item.height }}</div>
                  </div>
                  <p
                    :title="item.fileName"
                    class="file_name"
                    :style="{ width: '100%' }"
                    @click="(e) => clickStop(e)"
                    >{{ item.fileName }}</p
                  >
                  <div class="flex_row_start_start item_btns" @click="(e) => clickStop(e)">
                    <span @click="(e) => handleOperate('rename', e, item, index)">重命名</span>
                    <span @click="(e) => handleOperate('moveto', e, item, null)">移动至</span>
                    <span @click="(e) => handleOperate('copy', e, item, null)">链接</span>
                    <Popconfirm
                      v-if="item.useNum == 0"
                      title="确认删除该图片？删除后不可恢复。"
                      @confirm="(e) => handleOperate('del', index, item, e)"
                    >
                      <span @click="(e) => clickStop(e)">删除</span>
                    </Popconfirm>
                  </div>
                </div>
              </div>
              <div v-else class="flex_column_center_center empty">
                <img src="@/assets/images/moudle_disable.png" />
                <span>暂无数据~</span>
              </div>
              <div
                v-if="data.pagination && data.pagination.total > 10"
                class="flex_row_end_center"
                :style="{ padding: '0 20px' }"
              >
                <Pagination
                  size="small"
                  showSizeChanger
                  :pageSizeOptions="['10', '20', '30', '40']"
                  show-quick-jumper
                  :current="data.pagination.current"
                  :total="data.pagination.total"
                  :pageSize="data.pagination.pageSize"
                  @change="(page, pageSize) => changePage(page, pageSize)"
                  @showSizeChange="(current, size) => changePage(current, size)"
                />
              </div>
            </div>
          </div>
        </div>
      </template>
      <template v-else-if="tabIndex == 2">
        <div class="flex_row_start_start material_center_main">
          <div
            class="flex_column_between_center material_center_left"
            :style="{ height: category_height }"
          >
            <div style="width: 100%">
              <div class="material_center_left_title">素材类目</div>
              <div class="material_center_left_list">
                <div class="flex_row_center_center material_center_left_list_search">
                  <Input
                    placeholder="搜索类目名称"
                    :maxlength="20"
                    size="small"
                    @change="(e) => handleOperate('category', e.target.value, e.type, null)"
                    allowClear
                  />
                  <span @click="handleOperate('searchCategory', null, null, null)">搜索</span>
                </div>
                <div class="material_center_left_list_category">
                  <template v-for="(item, index) in category_list" :key="index">
                    <div
                      class="material_center_left_list_category_item"
                      :class="category_selected == item.categoryId ? 'active' : ''"
                      @click="handleClickCategory('select', item.categoryId, null)"
                    >
                      <div class="flex_row_start_center" style="width: 100%">
                        <div
                          v-if="item.subCategoryList.length > 0"
                          class="material_center_left_list_category_item_arrow"
                          @click="(e) => handleClickCategory('show', index, e)"
                        >
                          <span style="position: relative; bottom: 2px">
                            <Icon
                              v-if="!item.showSubCategoryList"
                              icon="ant-design:caret-right-outlined"
                              :style="{ fontSize: 13 }"
                            />
                            <Icon
                              v-else
                              icon="ant-design:caret-down-outlined"
                              :style="{ fontSize: 13 }"
                            />
                          </span>
                        </div>
                        <div v-else class="material_center_left_list_category_item_arrow"></div>
                        <div
                          class="material_center_left_list_category_item_name"
                          :title="item.categoryName"
                          >{{ item.categoryName }}</div
                        >
                        <div
                          class="material_center_left_list_category_item_num"
                          :class="item.isInner == 1 ? 'no_hover' : ''"
                          >{{ item.fileCount }}</div
                        >
                        <div
                          class="flex_row_end_center material_center_left_list_category_item_btns"
                          :class="item.isInner == 1 ? 'no_hover' : ''"
                        >
                          <Icon
                            icon="ant-design:edit-outlined"
                            @click="(e) => handleOperate('editCategory', index, null, e)"
                          />
                          <Popconfirm
                            title="确认删除该素材分类？删除后该分类下的素材将移动至默认分类下。"
                            @confirm="(e) => handleOperate('delCategory', index, null, e)"
                          >
                            <Icon icon="ant-design:delete-outlined" @click="(e) => clickStop(e)" />
                          </Popconfirm>
                        </div>
                      </div>
                    </div>
                    <template v-if="item.showSubCategoryList">
                      <div
                        v-for="(items, indexs) in item.subCategoryList"
                        :key="indexs"
                        class="material_center_left_list_category_item material_center_left_list_category_child"
                        :class="category_selected == items.categoryId ? 'active' : ''"
                        @click="handleClickCategory('select', items.categoryId, null)"
                      >
                        <div class="flex_row_start_center" style="width: 100%">
                          <div
                            class="material_center_left_list_category_item_name"
                            :title="items.categoryName"
                            >{{ items.categoryName }}</div
                          >
                          <div
                            class="material_center_left_list_category_item_num"
                            :class="items.isInner == 1 ? 'no_hover' : ''"
                            >{{ items.fileCount }}</div
                          >
                          <div
                            class="flex_row_end_center material_center_left_list_category_item_btns"
                            :class="items.isInner == 1 ? 'no_hover' : ''"
                          >
                            <Icon
                              icon="ant-design:edit-outlined"
                              @click="(e) => handleOperate('editCategory', index, indexs, e)"
                            />
                            <Popconfirm
                              title="确认删除该素材分类？删除后该分类下的素材将移动至默认分类下。"
                              @confirm="(e) => handleOperate('delCategory', index, indexs, e)"
                            >
                              <Icon icon="ant-design:delete-outlined" @click="(e) => clickStop(e)" />
                            </Popconfirm>
                          </div>
                        </div>
                      </div>
                    </template>
                  </template>
                </div>
              </div>
            </div>
            <div class="flex_row_center_center material_center_left_add">
              <div
                class="material_center_left_add_btn"
                @click="handleOperate('addCategory', null, null, null)"
                >添加分类</div
              >
            </div>
          </div>
          <div class="material_center_right">
            <div class="flex_row_start_center material_center_right_search">
              <div
                class="flex_row_center_center material_center_right_search_upload"
                @click="handleOperate('upload', null, null, null)"
              >
                <AliSvgIcon iconName="iconxinzeng" width="14px" height="14px" fillColor="#fff" />
                <span>上传视频</span>
              </div>
              <div class="material_center_right_search_form">
                <BasicForm @register="videosRegister" @submit="handleSubmit" @reset="handleReset" />
              </div>
            </div>
            <div
              v-if="data.list && data.list.length > 0"
              class="flex_row_start_center material_center_right_operate"
            >
              <Checkbox
                :checked="checked_all"
                @change="(e) => handleOperate('all', e.target.checked, null, null)"
                >全选</Checkbox
              >
              <div
                class="flex_row_center_center material_center_right_operate_move"
                @click="(e) => handleOperate('moveto', e, null, null)"
              >
                <AliSvgIcon iconName="icondaoru" width="14px" height="14px" fillColor="#fff" />
                <span>批量移动至</span>
              </div>
            </div>
            <div class="material_center_right_list" :style="{ height: data_height }">
              <div
                v-if="data.list && data.list.length > 0"
                class="flex_row_start_start right_goods_item"
                :style="{ width: goods_total_width + 'px', minWidth: '720px' }"
              >
                <div
                  v-for="(item, index) in data.list"
                  :key="index"
                  class="flex_column_start_start item"
                  :class="item.checked ? 'active' : ''"
                  :style="{ minWidth: '134px', flex: 'unset', width: file_item_width + 'px' }"
                >
                  <div
                    class="flex_row_center_center img_wrap"
                    @click="handleOperate('view', item.fileUrl, null, null)"
                    :style="{
                      width: file_item_width - 2 + 'px',
                      height: file_item_width - 2 + 'px',
                      backgroundImage: `url('${item.fileBase64 ? item.fileBase64 : defaultVideoImg}')`,
                    }"
                  >
                    <img
                      v-if="item.checked"
                      class="checked"
                      @click="(e) => handleOperate('checked', e, index, null)"
                      src="@/assets/images/files_checked.png"
                    />
                    <img
                      v-else
                      class="checked"
                      @click="(e) => handleOperate('checked', e, index, null)"
                      src="@/assets/images/files_check.png"
                    />
                    <div class="file_state">
                      <text :class="item.useNum > 0 ? 'file_state_on' : 'file_state_off'">{{
                        item.useNum > 0 ? '已引用' : '未引用'
                      }}</text>
                      <img v-if="item.useNum > 0" src="@/assets/images/files_on.png" />
                      <img v-else src="@/assets/images/files_off.png" />
                    </div>
                  </div>
                  <p
                    :title="item.fileName"
                    class="file_name"
                    :style="{ width: '100%' }"
                    @click="(e) => clickStop(e)"
                    >{{ item.fileName }}</p
                  >
                  <div class="flex_row_start_start item_btns" @click="(e) => clickStop(e)">
                    <span @click="(e) => handleOperate('rename', e, item, index)">重命名</span>
                    <span @click="(e) => handleOperate('moveto', e, item, null)">移动至</span>
                    <span @click="(e) => handleOperate('copy', e, item, null)">链接</span>
                    <Popconfirm
                      v-if="item.useNum == 0"
                      title="确认删除该视频？删除后不可恢复。"
                      @confirm="(e) => handleOperate('del', index, item, e)"
                    >
                      <span @click="(e) => clickStop(e)">删除</span>
                    </Popconfirm>
                  </div>
                </div>
              </div>
              <div v-else class="flex_column_center_center empty">
                <img src="@/assets/images/moudle_disable.png" />
                <span>暂无数据~</span>
              </div>
              <div
                v-if="data.pagination && data.pagination.total > 10"
                class="flex_row_end_center"
                :style="{ padding: '0 20px' }"
              >
                <Pagination
                  size="small"
                  showSizeChanger
                  :pageSizeOptions="['10', '20', '30', '40']"
                  show-quick-jumper
                  :current="data.pagination.current"
                  :total="data.pagination.total"
                  :pageSize="data.pagination.pageSize"
                  @change="(page, pageSize) => changePage(page, pageSize)"
                  @showSizeChange="(current, size) => changePage(current, size)"
                />
              </div>
            </div>
          </div>
        </div>
      </template>
      <SldModal
        :width="width"
        :title="title"
        :visible="visible"
        :content="content"
        :confirmBtnLoading="confirmBtnLoading"
        @cancle-event="handleCancle"
        @confirm-event="handleConfirm"
      />
      <Modal
        :destroyOnClose="true"
        :maskClosable="false"
        title="清理素材"
        :width="795"
        :zIndex="999"
        :visible="modalVisible"
        ok-text="确认删除"
        @ok="sldConfirm"
        @cancel="sldCancle"
      >
        <div class="material_center material_center_clean">
          <div class="material_center_right">
            <a-tabs v-model:activeKey="modalTabIndex" type="card" @change="handleModalChange">
              <a-tab-pane :key="1" tab="图片" />
              <a-tab-pane :key="2" tab="视频" />
            </a-tabs>
            <div v-if="modalTabIndex == 1" class="material_center_right_list">
              <BasicForm
                @register="imgCategoryRegister"
                @submit="handleSubmit"
                @reset="handleReset"
              />
              <div class="flex_row_start_center material_center_right_operate">
                <Checkbox
                  :checked="clean_checked_all"
                  @change="(e) => handleOperate('all', e.target.checked, null, null)"
                  >全选</Checkbox
                >
              </div>
              <div
                class="flex_row_start_start right_goods_item clean_right_goods_item"
                style="flex-wrap: wrap"
              >
                <div
                  v-for="(item, index) in clean_data.list"
                  :key="index"
                  class="flex_column_start_start item"
                  :class="item.checked ? 'active' : ''"
                  style="flex: unset; width: 144px"
                >
                  <div
                    class="flex_row_center_center img_wrap"
                    @click="handleOperate('view', item.fileUrl, null, null)"
                    :style="{
                      width: 144 + 'px',
                      height: 144 + 'px',
                      backgroundImage: 'url(' + item.fileUrl + ')',
                    }"
                  >
                    <img
                      v-if="item.checked"
                      class="checked"
                      @click="(e) => handleOperate('checked', e, index, null)"
                      src="@/assets/images/files_checked.png"
                    />
                    <img
                      v-else
                      class="checked"
                      @click="(e) => handleOperate('checked', e, index, null)"
                      src="@/assets/images/files_check.png"
                    />
                    <div class="file_state">
                      <text :class="item.useNum > 0 ? 'file_state_on' : 'file_state_off'">{{
                        item.useNum > 0 ? '已引用' : '未引用'
                      }}</text>
                      <img v-if="item.useNum > 0" src="@/assets/images/files_on.png" />
                      <img v-else src="@/assets/images/files_off.png" />
                    </div>
                    <div class="file_size">{{ item.width }}*{{ item.height }}</div>
                  </div>
                  <p
                    :title="item.fileName"
                    class="file_name"
                    :style="{ width: '100%' }"
                    @click="(e) => clickStop(e)"
                    >{{ item.fileName }}</p
                  >
                </div>
              </div>
              <div
                v-if="clean_data.pagination && clean_data.pagination.total > 10"
                class="flex_row_end_center"
                :style="{ padding: '0 20px' }"
              >
                <Pagination
                  size="small"
                  showSizeChanger
                  :pageSizeOptions="['10', '20', '30', '40']"
                  show-quick-jumper
                  :current="clean_data.pagination.current"
                  :total="clean_data.pagination.total"
                  :pageSize="clean_data.pagination.pageSize"
                  @change="(page, pageSize) => changePage(page, pageSize)"
                  @showSizeChange="(current, size) => changePage(current, size)"
                />
              </div>
            </div>
            <div v-else class="material_center_right_list">
              <BasicForm
                @register="videoCategoryRegister"
                @submit="handleSubmit"
                @reset="handleReset"
              />
              <div class="flex_row_start_center material_center_right_operate">
                <Checkbox
                  :checked="clean_checked_all"
                  @change="(e) => handleOperate('all', e.target.checked, null, null)"
                  >全选</Checkbox
                >
              </div>
              <div
                class="flex_row_start_start right_goods_item clean_right_goods_item"
                style="flex-wrap: wrap"
              >
                <div
                  v-for="(item, index) in clean_data.list"
                  :key="index"
                  class="flex_column_start_start item"
                  :class="item.checked ? 'active' : ''"
                  style="flex: unset; width: 144px"
                >
                  <div
                    class="flex_row_center_center img_wrap"
                    @click="handleOperate('view', item.fileUrl, null, null)"
                    :style="{
                      width: 144 + 'px',
                      height: 144 + 'px',
                      backgroundImage: `url('${item.fileBase64 ? item.fileBase64 : defaultVideoImg}')`,
                    }"
                  >
                    <img
                      v-if="item.checked"
                      class="checked"
                      @click="(e) => handleOperate('checked', e, index, null)"
                      src="@/assets/images/files_checked.png"
                    />
                    <img
                      v-else
                      class="checked"
                      @click="(e) => handleOperate('checked', e, index, null)"
                      src="@/assets/images/files_check.png"
                    />
                    <div class="file_state">
                      <text :class="item.useNum > 0 ? 'file_state_on' : 'file_state_off'">{{
                        item.useNum > 0 ? '已引用' : '未引用'
                      }}</text>
                      <img v-if="item.useNum > 0" src="@/assets/images/files_on.png" />
                      <img v-else src="@/assets/images/files_off.png" />
                    </div>
                    <div class="file_size">{{ item.width }}*{{ item.height }}</div>
                  </div>
                  <p
                    :title="item.fileName"
                    class="file_name"
                    :style="{ width: '100%' }"
                    @click="(e) => clickStop(e)"
                    >{{ item.fileName }}</p
                  >
                </div>
              </div>
              <div
                v-if="clean_data.pagination && clean_data.pagination.total > 10"
                class="flex_row_end_center"
                :style="{ padding: '0 20px' }"
              >
                <Pagination
                  size="small"
                  showSizeChanger
                  :pageSizeOptions="['10', '20', '30', '40']"
                  show-quick-jumper
                  :current="clean_data.pagination.current"
                  :total="clean_data.pagination.total"
                  :pageSize="clean_data.pagination.pageSize"
                  @change="(page, pageSize) => changePage(page, pageSize)"
                  @showSizeChange="(current, size) => changePage(current, size)"
                />
              </div>
            </div>
          </div>
        </div>
      </Modal>
      <SldPreviewImg
        :modal_width="500"
        :show_preview_modal="show_img_preview_modal"
        :img="preview_file"
        @close-preview-modal="closeViewModal"
      />
      <SldPreviewVideo
        :modal_width="500"
        :show_preview_modal="show_video_preview_modal"
        :video="preview_file"
        @close-preview-modal="closeViewModal"
      />
    </div>
  </div>
</template>
<script lang="ts">
  import { defineComponent, reactive, ref, onMounted, onUnmounted, unref } from 'vue';
  import { Tabs, Input, Checkbox, Popconfirm, Pagination, Modal } from 'ant-design-vue';
  import {
    getMaterialCategoryList,
    getMaterialFileList,
    addMaterialCategory,
    updateMaterialCategory,
    delMaterialCategory,
    renameMaterial,
    moveToMaterial,
    cleanMaterial,
    fileBindCategory,
  } from '/@/api/manage/manage';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import Icon from '@/components/Icon/Icon.vue';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import SldModal from '@/components/SldModal/index.vue';
  import SldPreviewImg from '@/components/SldPreviewImg/SldPreviewImg.vue';
  import SldPreviewVideo from '@/components/SldPreviewVideo/SldPreviewVideo.vue';
  import copy from 'copy-to-clipboard';
  import { sucTip, failTip } from '/@/utils/utils';

  export default defineComponent({
    components: {
      SldComHeader,
      [Tabs.name]: Tabs,
      [Tabs.TabPane.name]: Tabs.TabPane,
      Input,
      Icon,
      BasicForm,
      Checkbox,
      Popconfirm,
      Pagination,
      SldModal,
      Modal,
      SldPreviewImg,
      SldPreviewVideo,
    },
    setup() {
      const defaultVideoImg = ref(new URL('/@/assets/images/center/svideo.png', import.meta.url).href);
      const { getRealWidth } = useMenuSetting();
      const clickEvent = ref(false);
      const tabIndex = ref(1);
      const category_search = ref(''); //类目搜索
      const category_list: any = ref([]);
      const category_selected = ref('1'); //已选类目
      const model_category_selected = ref(''); //已选类目
      const category_height: any = ref(0); //左侧类目模块高度
      const screenW: any = ref(document.body.clientWidth); //屏幕宽度
      const common_page_width: any = ref(0);
      const file_item_width: any = ref(0);
      const goods_total_width: any = ref(0);
      const data: any = ref({});
      const data_height: any = ref(0); //右侧数据模块高度
      const checked_all = ref(false); //全选
      const checked_val: any = ref({ data: [], ids: [] }); //已选数据
      let pageValue = reactive({ current: 1, pageSize: 10 }); //分页数据
      let searchValue = reactive({}); //筛选数据
      const imgsSchemas: FormSchema[] = [
        {
          field: 'state',
          component: 'Select',
          colProps: { span: 6, style: 'width:240px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            placeholder: '请选择状态',
            options: [
              { key: 0, label: '全部', value: '' },
              { key: 1, label: '已引用', value: '1' },
              { key: 2, label: '未引用', value: '2' },
            ],
            size: 'default',
          },
          label: '状态',
          labelWidth: 50,
        },
        {
          field: 'fileName',
          component: 'Input',
          colProps: { span: 6, style: 'width:260px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            maxlength: 6,
            placeholder: '请输入图片名称',
            size: 'default',
          },
          label: '图片名称',
          labelWidth: 80,
        },
      ];
      const videosSchemas: FormSchema[] = [
        {
          field: 'state',
          component: 'Select',
          colProps: { span: 6, style: 'width:240px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            placeholder: '请选择状态',
            options: [
              { key: 0, label: '全部', value: '' },
              { key: 1, label: '已引用', value: '1' },
              { key: 2, label: '未引用', value: '2' },
            ],
            size: 'default',
          },
          label: '状态',
          labelWidth: 50,
        },
        {
          field: 'fileName',
          component: 'Input',
          colProps: { span: 6, style: 'width:260px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            maxlength: 6,
            placeholder: '请输入视频名称',
            size: 'default',
          },
          label: '视频名称',
          labelWidth: 80,
        },
      ];

      // 图片素材类目
      const imgCategorySchemas: FormSchema[] = [
        {
          field: 'categoryId',
          component: 'TreeSelect',
          colProps: { span: 6, style: 'width:240px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            placeholder: '请选择分类',
            treeData: [],
            size: 'default',
          },
          label: '素材类目',
          labelWidth: 80,
        },
        {
          field: 'fileName',
          component: 'Input',
          colProps: { span: 6, style: 'width:260px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            maxlength: 6,
            placeholder: '请输入图片名称',
            size: 'default',
          },
          label: '图片名称',
          labelWidth: 80,
        },
      ];
      // 视频素材类目
      const videosCategorySchemas: FormSchema[] = [
        {
          field: 'categoryId',
          component: 'TreeSelect',
          colProps: { span: 6, style: 'width:240px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            placeholder: '请选择分类',
            treeData: [],
            size: 'default',
          },
          label: '素材类目',
          labelWidth: 80,
        },
        {
          field: 'fileName',
          component: 'Input',
          colProps: { span: 6, style: 'width:260px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            maxlength: 6,
            placeholder: '请输入视频名称',
            size: 'default',
          },
          label: '视频名称',
          labelWidth: 80,
        },
      ];
      const [imgsRegister] = useForm({
        labelWidth: 120,
        schemas: imgsSchemas,
        actionColOptions: { span: 24 },
      });
      const [videosRegister] = useForm({
        labelWidth: 120,
        schemas: videosSchemas,
        actionColOptions: { span: 24 },
      });

      const [imgCategoryRegister] = useForm({
        labelWidth: 120,
        schemas: imgCategorySchemas,
        actionColOptions: { span: 24 },
      });
      const [videoCategoryRegister] = useForm({
        labelWidth: 120,
        schemas: videosCategorySchemas,
        actionColOptions: { span: 24 },
      });
      const width = ref(600);
      const title = ref('');
      const visible = ref(false);
      const content = ref([]);
      const confirmBtnLoading = ref(false);
      const operate_item: any = ref('');
      const operate_type = ref('');
      const operate_add_edit_data = ref([
        {
          type: 'input',
          label: `分类名称`,
          name: 'name',
          placeholder: `请输入分类名称`,
          extra: '最多20个字',
          maxLength: 20,
          showCount: true,
          rules: [
            {
              required: true,
              whitespace: true,
              message: '请输入分类名称',
            },
          ],
        },
        {
          type: 'select',
          label: `上级分类`,
          name: 'pid',
          placeholder: `请选择上级分类`,
          extra: '未选则上级分类默认创建一级分类',
          selData: [],
          diy: true,
          selKey: 'categoryId',
          selName: 'categoryName',
          wrapperCol: { span: 16 },
        },
      ]);
      const operate_rename_data = ref([
        {
          type: 'input',
          label: `图片名称`,
          name: 'fileName',
          placeholder: `请输入图片名称`,
          extra: '最多50个字',
          maxLength: 50,
          showCount: true,
          rules: [
            {
              required: true,
              whitespace: true,
              message: '请输入图片名称',
            },
          ],
        },
      ]);
      const operate_moveto_data = ref([
        {
          type: 'TreeSelect',
          label: `移动至分类`,
          name: 'categoryId',
          placeholder: `请选择分类`,
          initialValue: undefined,
          data: [],
          fieldNames: { children: 'subCategoryList', label: 'categoryName', value: 'categoryId' },
          treeNodeFilterProp: 'title',
          colSpan: 16,
          rules: [
            {
              required: true,
              message: '请选择分类',
            },
          ],
        },
      ]);
      const operate_upload_img_data = ref([
        {
          type: 'TreeSelect',
          label: `上传分类`,
          name: 'categoryId',
          placeholder: `请选择分类`,
          initialValue: undefined,
          data: [],
          fieldNames: { children: 'subCategoryList', label: 'categoryName', value: 'categoryId' },
          treeNodeFilterProp: 'title',
          colSpan: 16,
          rules: [
            {
              required: true,
              message: '请选择分类',
            },
          ],
        },
        {
          type: 'upload_img_upload',
          label: '本地上传',
          multiple:true,
          name: 'fileIds',
          extra: '支持.gif, .jpeg, .png, .jpg, .bmp, .tif格式图片。',
          initialValue: [],
          upload_name: 'file',
          upload_url: `v3/oss/admin/upload?source=goods`,
          fileList: [],
          img_succ_info: {},
          maxCount: 100,
          limit: 20,
          accept: '.gif, .jpeg, .png, .jpg, .bmp, .tif',
        },
      ]);
      const operate_upload_video_data = ref([
        {
          type: 'TreeSelect',
          label: `上传分类`,
          name: 'categoryId',
          placeholder: `请选择分类`,
          initialValue: undefined,
          data: [],
          fieldNames: { children: 'subCategoryList', label: 'categoryName', value: 'categoryId' },
          treeNodeFilterProp: 'title',
          colSpan: 16,
          rules: [
            {
              required: true,
              message: '请选择分类',
            },
          ],
        },
        {
          type: 'upload_video_upload',
          label: '本地上传',
          name: 'fileIds',
          extra: '支持mp4格式,最大限制20M。推荐时长不低于6s,不超过90s',
          initialValue: [],
          multiple:true,
          upload_name: 'file',
          upload_url: `v3/oss/admin/upload?source=video`,
          fileList: [],
          img_succ_info: {},
          maxCount: 100,
          limit: 20,
          accept: '.mp4',
        },
      ]);
      const modalVisible = ref(false); //清理素材弹窗是否显示
      const modalTabIndex = ref(1);
      const clean_data: any = ref({});
      const clean_checked_all = ref(false); //全选
      const clean_checked_val: any = ref({ data: [], ids: [] }); //已选数据
      let clean_pageValue = reactive({ current: 1, pageSize: 10 }); //分页数据
      let clean_searchValue = reactive({}); //筛选数据
      const show_img_preview_modal = ref(false);
      const show_video_preview_modal = ref(false);
      const preview_file = ref('');

      function handleChange(e) {
        tabIndex.value = e;
        data.value = {};
        get_category_list({}, null);
        handleReset();
      }

      onMounted(() => {
        pageValue.current = 1;
        pageValue.pageSize = 10;
        get_category_list({}, null);
        get_model_category_list(1);
        get_model_category_list(2);
        resizePage();
        window.addEventListener('resize', resizePage, true);
      });

      onUnmounted(() => {
        window.removeEventListener('resize', resizePage, true);
      });

      function resizePage() {
        category_height.value = document.body.clientHeight - 202 + 'px';
        data_height.value = document.body.clientHeight - 290 + 'px';
        let goods_width = common_page_width.value
          ? common_page_width.value - 255
          : screenW.value - unref(getRealWidth) - 295;
        let normal_num = 5;
        if (goods_width <= 1440) {
          normal_num = 6;
        } else if (goods_width <= 1680) {
          normal_num = 7;
        } else if (goods_width <= 1920) {
          normal_num = 8;
        } else if (goods_width <= 2500) {
          normal_num = 10;
        } else if (goods_width <= 3000) {
          normal_num = 13;
        } else if (goods_width <= 3500) {
          normal_num = 15;
        } else {
          normal_num = 18;
        }
        goods_total_width.value = goods_width;
        file_item_width.value = goods_width / normal_num - 10;
      }

      //获取素材列表
      const get_file_list = async (params) => {
        const res: any = await getMaterialFileList(params);
        if (res.state == 200 && res.data) {
          if (res.data.list && res.data.list.length > 0) {
            res.data.list.map((item) => {
              if (modalVisible.value) {
                item.checked = clean_checked_all.value;
                if (
                  clean_checked_all.value &&
                  clean_checked_val.value.ids.indexOf(item.bindId) == -1
                ) {
                  clean_checked_val.value.data.push(item);
                  clean_checked_val.value.ids.push(item.bindId);
                }
              } else {
                item.checked = checked_all.value;
                if (checked_all.value && checked_val.value.ids.indexOf(item.bindId) == -1) {
                  checked_val.value.data.push(item);
                  checked_val.value.ids.push(item.bindId);
                }
              }
            });
          }
          if (modalVisible.value) {
            clean_data.value = res.data;
          } else {
            data.value = res.data;
          }
        } else {
          failTip(res.msg);
        }
      };

      //获取素材类目
      const get_category_list = async (params, categoryId) => {
        const res: any = await getMaterialCategoryList({
          type: tabIndex.value,
          showFileCount: 1,
          ...params,
        });
        if (res.state == 200 && res.data) {
          if (res.data.length > 0) {
            category_selected.value = categoryId === null ? res.data[0].categoryId : categoryId;
            pageValue.current = 1;
            get_file_list({
              categoryId: category_selected.value,
              fileType: tabIndex.value,
              ...pageValue,
              ...searchValue,
            });
          }
          category_list.value = res.data;
          let temp = operate_add_edit_data.value.filter((item) => item.name == 'pid');
          if (temp.length > 0) {
            temp[0].selData = res.data;
          }
          let move_temp = operate_moveto_data.value.filter((item) => item.name == 'categoryId');
          if (move_temp.length > 0) {
            move_temp[0].data = res.data;
          }
          let upload_temp =
            tabIndex.value == 1
              ? operate_upload_img_data.value.filter((item) => item.name == 'categoryId')
              : operate_upload_video_data.value.filter((item) => item.name == 'categoryId');
          if (upload_temp.length > 0) {
            upload_temp[0].data = res.data;
          }
        } else {
          failTip(res.msg);
        }
      };

      //类目点击事件
      function handleClickCategory(type, index, indexs) {
        if (type == 'show') {
          indexs.stopPropagation();
          category_list.value[index].showSubCategoryList =
            !category_list.value[index].showSubCategoryList;
        } else if (type == 'select') {
          category_selected.value = index;
          get_file_list({
            categoryId: category_selected.value,
            fileType: tabIndex.value,
            ...pageValue,
            ...searchValue,
          });
        }
      }

      //筛选搜索
      function handleSubmit(val) {
        for (let key in val) {
          if (val[key] === undefined || val[key] === null) {
            delete val[key];
          }
        }
        if (modalVisible.value) {
          clean_searchValue = val;
          clean_pageValue.current = 1;
          let params: any = {
            fileType: modalTabIndex.value,
            state: 2,
            ...clean_pageValue,
            ...val,
          };
          if (model_category_selected.value) {
            params.categoryId = model_category_selected.value;
          }
          get_file_list(params);
        } else {
          searchValue = val;
          pageValue.current = 1;
          get_file_list({
            categoryId: category_selected.value,
            fileType: tabIndex.value,
            ...pageValue,
            ...val,
          });
        }
      }

      //筛选重置
      function handleReset() {
        if (modalVisible.value) {
          clean_searchValue = {};
          clean_checked_all.value = false;
          clean_checked_val.value.data = [];
          clean_checked_val.value.ids = [];
          model_category_selected.value = '';
          let params: any = {
            fileType: modalTabIndex.value,
            state: 2,
            ...clean_pageValue,
            ...clean_searchValue,
          };
          if (model_category_selected.value) {
            params.categoryId = model_category_selected.value;
          }
          get_file_list(params);
        } else {
          searchValue = {};
          category_selected.value = '1';
          checked_all.value = false;
          checked_val.value.data = [];
          checked_val.value.ids = [];
          get_file_list({ categoryId: 1, fileType: tabIndex.value, ...pageValue, ...searchValue });
        }
      }

      //分页切换事件
      function changePage(page, pageSize) {
        if (modalVisible.value) {
          clean_pageValue.current = page;
          clean_pageValue.pageSize = pageSize;
          let params: any = {
            fileType: modalTabIndex.value,
            state: 2,
            ...clean_pageValue,
            ...clean_searchValue,
          };
          if (model_category_selected.value) {
            params.categoryId = model_category_selected.value;
          }
          get_file_list(params);
        } else {
          pageValue.current = page;
          pageValue.pageSize = pageSize;
          get_file_list({
            categoryId: category_selected.value,
            fileType: tabIndex.value,
            ...pageValue,
            ...searchValue,
          });
        }
      }

      function clickStop(e) {
        e.stopPropagation();
      }

      //操作事件
      function handleOperate(type, val1, val2, val3) {
        if (clickEvent.value) return;
        if (type == 'all') {
          //全选按钮
          if (modalVisible.value) {
            clean_checked_all.value = val1;
            let newData = JSON.parse(JSON.stringify(clean_data.value.list));
            newData.map((item) => {
              item.checked = val1;
              if (val1 && clean_checked_val.value.ids.indexOf(item.bindId) == -1) {
                clean_checked_val.value.data.push(item);
                clean_checked_val.value.ids.push(item.bindId);
              } else if (!val1 && clean_checked_val.value.ids.indexOf(item.bindId) != -1) {
                clean_checked_val.value.data.splice(
                  clean_checked_val.value.ids.indexOf(item.bindId),
                  1,
                );
                clean_checked_val.value.ids.splice(
                  clean_checked_val.value.ids.indexOf(item.bindId),
                  1,
                );
              }
            });
            clean_data.value.list = newData;
          } else {
            checked_all.value = val1;
            let newData = JSON.parse(JSON.stringify(data.value.list));
            newData.map((item) => {
              item.checked = val1;
              if (val1 && checked_val.value.ids.indexOf(item.bindId) == -1) {
                checked_val.value.data.push(item);
                checked_val.value.ids.push(item.bindId);
              } else if (!val1 && checked_val.value.ids.indexOf(item.bindId) != -1) {
                checked_val.value.data.splice(checked_val.value.ids.indexOf(item.bindId), 1);
                checked_val.value.ids.splice(checked_val.value.ids.indexOf(item.bindId), 1);
              }
            });
            data.value.list = newData;
          }
        } else if (type == 'checked') {
          //单选按钮
          val1.stopPropagation();
          if (modalVisible.value) {
            let newData: any = JSON.parse(JSON.stringify(clean_data.value.list));
            newData[val2].checked = !newData[val2].checked;
            let checked_length = newData.filter((item) => item.checked).length;
            checked_all.value = checked_length == newData.length ? true : false;
            clean_data.value.list = newData;
            if (
              newData[val2].checked &&
              clean_checked_val.value.ids.indexOf(newData[val2].bindId) == -1
            ) {
              clean_checked_val.value.data.push(newData[val2]);
              clean_checked_val.value.ids.push(newData[val2].bindId);
            } else if (
              !newData[val2].checked &&
              clean_checked_val.value.ids.indexOf(newData[val2].bindId) != -1
            ) {
              clean_checked_val.value.data.splice(
                clean_checked_val.value.ids.indexOf(newData[val2].bindId),
                1,
              );
              clean_checked_val.value.ids.splice(
                clean_checked_val.value.ids.indexOf(newData[val2].bindId),
                1,
              );
            }
          } else {
            let newData: any = JSON.parse(JSON.stringify(data.value.list));
            newData[val2].checked = !newData[val2].checked;
            let checked_length = newData.filter((item) => item.checked).length;
            checked_all.value = checked_length == newData.length ? true : false;
            data.value.list = newData;
            if (
              newData[val2].checked &&
              checked_val.value.ids.indexOf(newData[val2].bindId) == -1
            ) {
              checked_val.value.data.push(newData[val2]);
              checked_val.value.ids.push(newData[val2].bindId);
            } else if (
              !newData[val2].checked &&
              checked_val.value.ids.indexOf(newData[val2].bindId) != -1
            ) {
              checked_val.value.data.splice(checked_val.value.ids.indexOf(newData[val2].bindId), 1);
              checked_val.value.ids.splice(checked_val.value.ids.indexOf(newData[val2].bindId), 1);
            }
          }
        } else if (type == 'upload') {
          //上传
          visible.value = true;
          title.value = tabIndex.value == 1 ? '上传图片' : '上传视频';
          operate_type.value = type;
          content.value =
            tabIndex.value == 1
              ? JSON.parse(JSON.stringify(operate_upload_img_data.value))
              : JSON.parse(JSON.stringify(operate_upload_video_data.value));
        } else if (type == 'clean') {
          //清理素材
          modalVisible.value = true;
          modalTabIndex.value = 1;
          clean_pageValue.current = 1;
          clean_searchValue = {};
          get_model_category_list(1);
          get_file_list({
            fileType: 1,
            state: 2,
            ...clean_pageValue,
            ...clean_searchValue,
          });
        } else if (type == 'view') {
          //预览
          if (tabIndex.value == 1 || (modalVisible.value && modalTabIndex.value == 1)) {
            show_img_preview_modal.value = true;
          } else {
            show_video_preview_modal.value = true;
          }
          preview_file.value = val1;
        } else if (type == 'rename') {
          //重命名
          val1.stopPropagation();
          visible.value = true;
          title.value = '重命名';
          operate_type.value = type;
          operate_item.value = val2;
          let newContent = JSON.parse(JSON.stringify(operate_rename_data.value));
          newContent[0].initialValue = data.value.list[val3].fileName;
          content.value = newContent;
        } else if (type == 'moveto') {
          //移动至
          val1.stopPropagation();
          if (val2 === null && checked_val.value.ids.length == 0) {
            failTip('请先选中数据');
            return;
          }
          visible.value = true;
          title.value = '重命名';
          operate_type.value = type;
          let data = JSON.parse(JSON.stringify(operate_moveto_data.value));
          if (data[0].data.length > 0) {
            data[0].initialValue = data[0].data[0].categoryId;
          }
          operate_item.value = val2;
          content.value = data;
        } else if (type == 'copy') {
          //链接
          copy(val2.fileUrl);
          sucTip('已复制到剪切板');
        } else if (type == 'del') {
          //删除
          clickEvent.value = true;
          operateEvent(type, {
            bindIds: val2 === null ? checked_val.value.ids.join(',') : val2.bindId,
          });
        } else if (type == 'category') {
          //输入分类名称
          category_search.value = val1;
          if (val2 == 'click' && !val1) {
            handleOperate('searchCategory', null, null, null);
          }
        } else if (type == 'searchCategory') {
          //搜索分类
          get_category_list({ name: category_search.value }, category_selected.value);
        } else if (type == 'addCategory') {
          //添加分类
          visible.value = true;
          title.value = '新增分类';
          content.value = JSON.parse(JSON.stringify(operate_add_edit_data.value));
          operate_type.value = type;
        } else if (type == 'editCategory') {
          //编辑分类
          val3.stopPropagation();
          visible.value = true;
          title.value = '编辑分类';
          operate_type.value = type;
          let newContent = JSON.parse(JSON.stringify(operate_add_edit_data.value)).filter(
            (item) => item.name != 'pid',
          );
          if (val2 === null) {
            operate_item.value = category_list.value[val1];
            newContent[0].initialValue = category_list.value[val1].categoryName;
          } else {
            operate_item.value = category_list.value[val1].subCategoryList[val2];
            newContent[0].initialValue =
              category_list.value[val1].subCategoryList[val2].categoryName;
          }
          content.value = newContent;
        } else if (type == 'delCategory') {
          //删除分类
          val3.stopPropagation();
          clickEvent.value = true;
          let cid = '';
          if (val2 === null) {
            cid = category_list.value[val1].categoryId;
          } else {
            cid = category_list.value[val1].subCategoryList[val2].categoryId;
          }
          operateEvent(type, { type: tabIndex.value, cid });
        }
      }

      const operateEvent = async (type, params) => {
        let res: any = '';
        if (type == 'addCategory') {
          res = await addMaterialCategory(params);
        } else if (type == 'editCategory') {
          res = await updateMaterialCategory(params);
        } else if (type == 'delCategory') {
          res = await delMaterialCategory(params);
        } else if (type == 'rename') {
          res = await renameMaterial(params);
        } else if (type == 'moveto') {
          res = await moveToMaterial(params);
        } else if (type == 'del' || type == 'clean') {
          res = await cleanMaterial(params);
        } else if (type == 'upload') {
          res = await fileBindCategory(params);
        } else {
          clickEvent.value = false;
          return;
        }
        clickEvent.value = false;
        if (res.state == 200) {
          sucTip(res.msg);
          if (type != 'clean') {
            handleCancle();
          }
          if (
            type == 'addCategory' ||
            type == 'delCategory' ||
            type == 'moveto' ||
            type == 'del' ||
            type == 'upload'
          ) {
            if (type == 'delCategory' && params.cid == category_selected.value) {
              category_selected.value = '1';
            }
            get_category_list({}, category_selected.value);
          } else if (type == 'editCategory') {
            let list = JSON.parse(JSON.stringify(category_list.value));
            if (operate_item.value.pid) {
              let temp = list.filter((item) => item.categoryId == operate_item.value.pid);
              if (temp.length > 0) {
                let temps = temp[0].subCategoryList.filter(
                  (item) => item.categoryId == operate_item.value.categoryId,
                );
                if (temps.length > 0) {
                  temps[0].categoryName = params.categoryName;
                }
              }
            } else {
              let temp = list.filter((item) => item.categoryId == operate_item.value.categoryId);
              if (temp.length > 0) {
                temp[0].categoryName = params.categoryName;
              }
            }
            category_list.value = list;
          } else if (type == 'rename') {
            let list = JSON.parse(JSON.stringify(data.value.list));
            let temp = list.filter((item) => item.bindId == operate_item.value.bindId);
            if (temp.length > 0) {
              temp[0].fileName = params.fileName;
            }
            data.value.list = list;
          } else if (type == 'clean') {
            clean_pageValue.current = 1;
            get_file_list({
              fileType: modalTabIndex.value,
              state: 2,
              ...clean_pageValue,
              ...clean_searchValue,
            });
          }
        } else {
          failTip(res.msg);
        }
      };

      //弹窗取消事件
      function handleCancle() {
        visible.value = false;
        content.value = [];
      }

      //弹窗确认事件
      function handleConfirm(val) {
        if (clickEvent.value) return;
        clickEvent.value = true;
        if (operate_type.value == 'addCategory') {
          val.type = tabIndex.value;
        } else if (operate_type.value == 'editCategory') {
          val.categoryName = val.name;
          delete val.name;
          val.categoryId = operate_item.value.categoryId;
        } else if (operate_type.value == 'rename') {
          val.bindId = operate_item.value.bindId;
        } else if (operate_type.value == 'moveto') {
          val.bindIds =
            operate_item.value === null
              ? checked_val.value.ids.join(',')
              : operate_item.value.bindId;
        } else if (operate_type.value == 'upload') {
          if (!val.fileIds || val.fileIds.length == 0) {
            failTip(tabIndex.value == 1 ? '请上传图片' : '请上传视频');
            clickEvent.value = false;
            return;
          } else {
            let fileIds: any = [];
            val.fileIds.map((item) => {
              if (item.response && item.response.state == 200 && item.response.data) {
                fileIds.push(item.response.data.fileId);
              }
            });
            val.fileIds = fileIds.join(',');
          }
        } else {
          return;
        }
        operateEvent(operate_type.value, val);
      }

      function handleModalChange(e) {
        modalTabIndex.value = e;
        clean_data.value = {};
        clean_checked_all.value = false;
        clean_checked_val.value = { data: [], ids: [] };
        clean_pageValue.current = 1;
        clean_searchValue = {};
        model_category_selected.value = '';
        get_model_category_list(modalTabIndex.value);
        get_file_list({
          fileType: e,
          state: 2,
          ...clean_pageValue,
          ...clean_searchValue,
        });
      }

      //清理素材弹窗取消
      function sldCancle() {
        modalVisible.value = false;
        clean_data.value = {};
        clean_checked_all.value = false;
        clean_checked_val.value = { data: [], ids: [] };
        clean_pageValue = { current: 1, pageSize: 10 };
        model_category_selected.value = '';
        clean_searchValue = {};
      }

      //清理素材弹窗确认
      function sldConfirm() {
        if (!clean_checked_val.value.ids || clean_checked_val.value.ids.length == 0) {
          failTip('请选择要清理的素材');
        } else {
          operateEvent('clean', { bindIds: clean_checked_val.value.ids.join(',') });
        }
      }

      //关闭预览图片
      function closeViewModal() {
        show_img_preview_modal.value = false;
        show_video_preview_modal.value = false;
        preview_file.value = '';
      }

      const get_model_category_list = async (index) => {
        const res: any = await getMaterialCategoryList({
          type: index,
        });
        if (res.state == 200 && res.data) {
          let TreeSelectData: any = [];
          res.data.map((v) => {
            let itemData = {
              label: v.categoryName,
              value: v.categoryId,
              children: [],
            };
            if (v.subCategoryList && v.subCategoryList.length > 0) {
              itemData.children = v.subCategoryList.map((x) => {
                return {
                  label: x.categoryName,
                  value: x.categoryId,
                };
              });
            }
            TreeSelectData.push(itemData);
          });
          if (index == 1) {
            imgCategorySchemas.map((v, i) => {
              if (v.field == 'categoryId') {
                imgCategorySchemas[i] = {
                  field: 'categoryId',
                  component: 'TreeSelect',
                  colProps: { span: 6, style: 'width:240px;max-width:100%;flex:none' },
                  componentProps: {
                    minWidth: 300,
                    placeholder: '请选择分类',
                    treeData: JSON.parse(JSON.stringify(TreeSelectData)),
                    size: 'default',
                  },
                  label: '素材类目',
                  labelWidth: 80,
                };
              }
            });
          } else {
            videosCategorySchemas.map((v, i) => {
              if (v.field == 'categoryId') {
                videosCategorySchemas[i] = {
                  field: 'categoryId',
                  component: 'TreeSelect',
                  colProps: { span: 6, style: 'width:240px;max-width:100%;flex:none' },
                  componentProps: {
                    minWidth: 300,
                    placeholder: '请选择分类',
                    treeData: JSON.parse(JSON.stringify(TreeSelectData)),
                    size: 'default',
                  },
                  label: '素材类目',
                  labelWidth: 80,
                };
              }
            });
          }
        } else {
          failTip(res.msg);
        }
      };

      return {
        defaultVideoImg,
        clickEvent,
        tabIndex,
        category_search,
        category_list,
        category_selected,
        category_height,
        screenW,
        common_page_width,
        file_item_width,
        goods_total_width,
        data,
        data_height,
        checked_all,
        checked_val,
        pageValue,
        searchValue,
        imgsRegister,
        videosRegister,
        imgCategoryRegister,
        videoCategoryRegister,
        width,
        title,
        visible,
        content,
        confirmBtnLoading,
        operate_item,
        operate_type,
        operate_add_edit_data,
        operate_rename_data,
        operate_moveto_data,
        operate_upload_img_data,
        handleChange,
        get_category_list,
        handleClickCategory,
        handleSubmit,
        handleReset,
        changePage,
        clickStop,
        handleOperate,
        operateEvent,
        handleCancle,
        handleConfirm,
        modalVisible,
        modalTabIndex,
        handleModalChange,
        sldCancle,
        sldConfirm,
        clean_data,
        clean_checked_all,
        clean_checked_val,
        clean_pageValue,
        clean_searchValue,
        show_img_preview_modal,
        show_video_preview_modal,
        preview_file,
        closeViewModal,
      };
    },
  });
</script>
<style lang="less">
  .material_center {
    position: relative;

    &.material_center_clean {
      min-height: 385px;
      padding: 10px;

      .material_center_right {
        .material_center_right_list {
          margin-top: 10px;
          overflow: hidden;
        }
      }
    }

    .top_clean_btn {
      position: absolute;
      z-index: 9;
      top: 10px;
      left: 105px;
      color: @primary-color;
      cursor: pointer;
    }

    .material_center_left {
      position: relative;
      flex-shrink: 0;
      width: 220px;
      margin-right: 10px;
      border: 1px solid #ebedf0;
      border-radius: 2px;
      background: #fff;

      .material_center_left_title {
        width: 100%;
        height: 49px;
        padding-left: 20px;
        border-bottom: 1px solid #d8d8d8;
        color: #323233;
        font-family: PingFangSC-Medium, 'PingFang SC';
        font-size: 14px;
        font-weight: 500;
        line-height: 49px;
      }

      .material_center_left_list {
        width: 100%;

        .material_center_left_list_search {
          width: 100%;
          padding: 10px;

          .ant-input-affix-wrapper {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            width: 150px;
            height: 32px;
            font-size: 13px;

            input {
              width: 115px; 
            }

            .ant-input-suffix {
              display: flex;
              align-items: center;
              justify-content: center;
              margin-left: 5px;

              .ant-input-clear-icon {
                margin: 0;
              }
            }
          }

          span {
            display: inline-block;
            margin-left: 10px;
            color: #323233;
            font-family: PingFangSC-Regular, 'PingFang SC';
            font-size: 14px;
            font-weight: 400;
            white-space: nowrap;
            cursor: pointer;
          }
        }

        .material_center_left_list_category {
          width: 100%;

          .material_center_left_list_category_item {
            height: unset;
            padding-top: 10px;
            padding-bottom: 10px;
            padding-left: 10px;
            cursor: pointer;

            .material_center_left_list_category_item_arrow {
              width: 16px;
              height: 16px;
            }

            .material_center_left_list_category_item_name {
              overflow: hidden;
              text-align: left;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .material_center_left_list_category_item_num {
              flex: 1;
              padding-right: 10px;
              text-align: right;
            }

            .material_center_left_list_category_item_btns {
              display: none;
              flex: 1;
              padding-right: 10px;

              span {
                margin-left: 6px;

                &:hover {
                  color: @primary-color;
                }
              }
            }

            &:hover {
              .material_center_left_list_category_item_name {
                color: @primary-color;
              }

              .material_center_left_list_category_item_num {
                display: none;

                &.no_hover {
                  display: block;
                }
              }

              .material_center_left_list_category_item_btns {
                display: flex;

                &.no_hover {
                  display: none;
                }
              }
            }

            &.material_center_left_list_category_child {
              .material_center_left_list_category_item_name {
                margin-left: 27px;
              }
            }

            &.active {
              background: #f9f9f9;

              .material_center_left_list_category_item_name {
                color: @primary-color;
              }
            }
          }
        }
      }

      .material_center_left_add {
        width: 100%;
        margin-top: 10px;
        margin-bottom: 10px;

        .material_center_left_add_btn {
          height: 28px;
          padding-right: 7px;
          padding-left: 7px;
          transition: all 0.3s ease;
          border: 1px solid #c9c9c9;
          border-radius: 3px;
          background: #fff;
          color: #333;
          line-height: 26px;
          cursor: pointer;

          &:hover {
            border-color: @primary-color;
            color: @primary-color;
          }
        }
      }
    }

    .material_center_right {
      flex: 1;

      .material_center_right_search {
        position: relative;

        .material_center_right_search_upload {
          width: 90px;
          height: 28px;
          margin-bottom: 8px;
          margin-left: 15px;
          padding-right: 7px;
          padding-left: 7px;
          border-radius: 3px;
          background-color: #ff6a12;
          color: #fff;
          line-height: 27px;
          cursor: pointer;

          svg {
            filter: grayscale(100%) brightness(200%);
          }

          span {
            margin-left: 5px;
          }
        }
      }

      .material_center_right_operate {
        width: 100%;
        height: 40px;
        margin-bottom: 10px;
        padding-left: 15px;
        background: #f2f2f2;

        .material_center_right_operate_move {
          height: 28px;
          margin-left: 10px;
          padding-right: 7px;
          padding-left: 7px;
          border-radius: 3px;
          background-color: #ff6a12;
          cursor: pointer;

          span {
            margin-left: 3px;
            color: #fff;
            font-size: 13px;
          }
        }
      }

      .material_center_right_list {
        overflow-x: hidden;
        overflow-y: auto;

        .right_goods_item {
          flex-wrap: wrap;

          .item {
            position: relative;
            flex-shrink: 0;
            margin-right: 10px;
            margin-bottom: 15px;
            overflow: hidden;

            &.active {
              .img_wrap {
                border-color: @primary-color;
              }
            }

            .img_wrap {
              position: relative;
              overflow: hidden;
              border: 1px solid #e0e0e0;
              border-radius: 4px;
              background-repeat: no-repeat;
              background-position: center;
              background-size: contain;

              .checked {
                position: absolute;
                z-index: 9;
                top: 0;
                right: 0;
                width: 22px;
                height: 22px;
                cursor: pointer;
              }

              .file_state {
                position: absolute;
                z-index: 9;
                top: 0;
                left: 0;
                height: 22px;
                line-height: 22px;
                cursor: default;

                text {
                  padding-left: 4px;

                  &.file_state_on {
                    color: #fff;
                  }

                  &.file_state_off {
                    color: #000000a6;
                  }
                }

                img {
                  position: absolute;
                  z-index: -1;
                  top: 0;
                  left: 0;
                  width: 58px;
                  height: 22px;
                }
              }

              .file_size {
                position: absolute;
                bottom: 0;
                width: 100%;
                height: 26px;
                background: #00000085;
                color: #fff;
                line-height: 26px;
                text-align: center;
                cursor: default;
              }
            }

            .file_name {
              display: -webkit-box;
              height: 34px;
              margin-top: 5px;
              margin-bottom: 5px;
              padding-right: 6px;
              padding-left: 6px;
              overflow: hidden;
              font-family: PingFangSC-Regular, 'PingFang SC';
              font-size: 13px;
              font-weight: 400;
              line-height: 18px;
              text-overflow: ellipsis;
              word-break: break-all;
              -webkit-line-clamp: 2;

              /* autoprefixer: off */
              -webkit-box-orient: vertical;
            }

            .item_btns {
              flex-wrap: wrap;
              padding-right: 6px;
              padding-left: 6px;
              color: #ff701e;
              font-family: PingFangSC-Regular, 'PingFang SC';
              font-size: 13px;
              font-weight: 400;
              white-space: nowrap;

              span {
                margin-right: 6px;
                cursor: pointer;
              }
            }
          }

          &.clean_right_goods_item {
            max-height: 406px;
            overflow-y: auto;

            .item {
              &:nth-child(5n + 5) {
                margin-right: 0;
              }
            }
          }
        }

        .empty {
          height: 100%;

          img {
            width: 80px;
            margin-bottom: 10px;
          }
        }
      }
    }
  }
</style>
