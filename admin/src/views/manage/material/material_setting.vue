<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <a-spin :spinning="spinning">
        <SldComHeader :type="1" :title="'素材中心设置'" />
        <div style="width: 100%; height: 5px"></div>
        <StandardTableRow
          width="100%"
          :data="rowData"
          @callback-event="callbackEvent"
          @submit-event="submitEvent"
        />
      </a-spin>
    </div>
  </div>
</template>
<script lang="ts">
  import { defineComponent, ref, onMounted } from 'vue';
  import { Spin } from 'ant-design-vue';
  import { getSettingList, saveBasicSiteSetting } from '/@/api/sysset/sysset';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import StandardTableRow from '/@/components/StandardTableRow/index.vue';
  import { failTip, sucTip } from '/@/utils/utils';

  export default defineComponent({
    components: {
      [Spin.name]: Spin,
      SldComHeader,
      StandardTableRow,
    },
    setup() {
      const clickEvent = ref(false);
      const spinning = ref(false);
      const rowData = ref([]);

      onMounted(() => {
        spinning.value = true;
        getSetting();
      });

      const getSetting = async () => {
        const res: any = await getSettingList({ str: 'default_store_material_storage_size' });
        spinning.value = false;
        if (res.state == 200) {
          let data: any = [];
          res.data.map((item) => {
            data.push({
              type: 'inputnum',
              width: 300,
              label: item.title,
              desc: item.description,
              key: item.name,
              placeholder: '请输入' + item.title,
              input_after_text: 'G',
              min: 1,
              max: 99999999,
              precision: 2,
              value: item.value,
              callback: true,
            });
          });
          if (data.length > 0) {
            data.push({
              type: 'button',
              width: 300,
              showSubmit: true,
              submitText: '保存',
              showCancle: false,
            });
          }
          rowData.value = data;
        } else {
          failTip(res.msg);
        }
      };

      const callbackEvent = (item) => {
        let temp: any = rowData.value.filter((items) => items.key == item.contentItem.key);
        if (temp.length > 0) {
          temp[0].value = item.val1;
        }
      };

      const submitEvent = async (val) => {
        if (clickEvent.value) return;
        clickEvent.value = true;
        const res = await saveBasicSiteSetting(val);
        if (res.state == 200) {
          sucTip(res.msg);
        } else {
          failTip(res.msg);
        }
        clickEvent.value = false;
      };

      return {
        clickEvent,
        spinning,
        rowData,
        getSetting,
        callbackEvent,
        submitEvent,
      };
    },
  });
</script>
<style lang="less" scoped></style>
