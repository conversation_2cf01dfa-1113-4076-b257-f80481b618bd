<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader :type="1" :title="'属性管理'" />
      <BasicTable @register="standardTable" style="padding: 0">
        <template #tableTitle>
          <div class="toolbar">
            <div class="toolbar_btn" @click="handleClick(null, 'add')">
              <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#fff" />
              <span>新增管理</span>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.key == 'action'">
            <template v-if="record.isInner == 1"> -- </template>
            <template v-else>
              <span class="common_page_edit" @click="handleClick(record, 'edit')">编辑</span>
              <Popconfirm
                title="删除后不可恢复，是否确定删除？"
                @confirm="handleClick(record, 'del')"
              >
                <span class="common_page_edit">删除</span>
              </Popconfirm>
            </template>
          </template>
          <template v-else-if="column.key == 'attributeValues'">
            {{ text ? text.join(',') : '--' }}
          </template>
          <template v-else-if="column.key == 'isShow'">
            <Switch
              :checked="text == '1' ? true : false"
              checked-children="启用"
              un-checked-children="停用"
              @change="(e) => handleChange(e, record)"
            />
          </template>
          <template v-else-if="column.key=='sort'">
            {{ text||text==0 ? text : '--' }}
          </template>
          <template v-else-if="column.key">
            {{ text ? text : '--' }}
          </template>
        </template>
      </BasicTable>
      <SldModal
        :width="width"
        :title="title"
        :visible="visible"
        :content="content"
        :confirmBtnLoading="confirmBtnLoading"
        :showFoot="true"
        @cancle-event="handleCancle"
        @confirm-event="handleConfirm"
      />
    </div>
  </div>
</template>
<script>
  import { defineComponent, ref } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import SldModal from '@/components/SldModal/index.vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import { Switch, Popconfirm } from 'ant-design-vue';
  import { getAttrList, addAttr, editAttr, delAttr } from '/@/api/manage/manage';
  import { sucTip, failTip } from '/@/utils/utils';

  export default defineComponent({
    components: {
      SldComHeader,
      SldModal,
      BasicTable,
      Switch,
      Popconfirm,
    },
    setup() {
      const [standardTable, { reload }] = useTable({
        api: (arg) => getAttrList({ ...arg }),
        fetchSetting: {
          pageField: 'current',
          sizeField: 'pageSize',
          listField: 'data.list',
          totalField: 'data.pagination.total',
        },
        columns: [
          {
            title: '属性名称',
            dataIndex: 'attributeName',
            width: 100,
          },
          {
            title: '属性值',
            dataIndex: 'attributeValues',
            width: 200,
          },
          {
            title: '排序',
            dataIndex: 'sort',
            width: 100,
          },
          {
            title: '添加时间',
            dataIndex: 'createTime',
            width: 100,
          },
          {
            title: '启用状态',
            dataIndex: 'isShow',
            width: 100,
          },
        ],
        actionColumn: {
          width: 100,
          title: '操作',
          dataIndex: 'action',
        },
        useSearchForm: true,
        formConfig: {
          schemas: [
            {
              field: 'attributeName',
              component: 'Input',
              colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                maxlength: 10,
                placeholder: '请输入属性名称',
                size: 'default',
              },
              label: '属性名称',
              labelWidth: 70,
            },
          ],
        },
        bordered: true,
        striped: false,
        showIndexColumn: true,
      });
      const width = ref(500);
      const title = ref('');
      const visible = ref(false);
      const content = ref([]);
      const confirmBtnLoading = ref(false);
      const operate_id = ref('');
      const operate_type = ref('');

      const operate_add_edit = ref([
        {
          type: 'input',
          label: `属性名称`,
          name: 'attributeName',
          placeholder: `请输入属性名称`,
          extra: '最多6个字',
          maxLength: 6,
          showCount: true,
          rules: [
            {
              required: true,
              whitespace: true,
              message: '请输入属性名称',
            },
          ],
        },
        {
          type: 'attr_tags',
          label: `属性值`,
          name: 'attributeValues',
          extra: '最多可添加20个，每个属性值最多可以输入10个字，且不可重复',
          placeholder: `请设置属性值`,
          initialValue: [],
          inputVisible: false,
          tip_con: `添加属性值`,
          maxNum: 20,
          rules: [
            {
              required: true,
              trigger: 'blur',
              message: '请设置属性值',
            },
          ],
        },
        {
          type: 'inputnum',
          label: `排序`,
          name: 'sort',
          placeholder: `请输入排序`,
          extra: '请输入0~255的数字，值越小显示越靠前',
          min: 0,
          max: 255,
          rules: [
            {
              required: true,
              message: `请输入排序`,
            },
          ],
        },
        {
          type: 'switch',
          label: `启用`,
          name: 'isShow',
          initialValue: '',
        },
      ]); //添加、编辑商品管理弹窗数据

      //表格点击回调事件
      function handleClick(item, type) {
        operate_type.value = type;
        if (item) {
          operate_id.value = item.attributeId;
        }
        if (type == 'add') {
          title.value = '添加属性';
          visible.value = true;
          content.value = JSON.parse(JSON.stringify(operate_add_edit.value));
        } else if (type == 'edit') {
          title.value = '编辑属性';
          visible.value = true;
          let data = JSON.parse(JSON.stringify(operate_add_edit.value));
          data.map((items) => {
            if (items.name == 'isShow') {
              items.initialValue = item[items.name] ? true : false;
            } else {
              items.initialValue = item[items.name]||item[items.name]==0 ? item[items.name] : undefined;
            }
          });
          content.value = data;
        } else if (type == 'del') {
          operate_role({ attributeId: item.attributeId });
        }
      }

      //弹窗取消事件
      function handleCancle() {
        visible.value = false;
        content.value = [];
        operate_id.value = '';
      }

      //弹窗确认事件
      function handleConfirm(val) {
        if (operate_type.value == 'add') {
          val.attributeValues = val.attributeValues.join(',');
          val.isShow = val.isShow ? 1 : 0;
        } else if (operate_type.value == 'edit') {
          val.attributeId = operate_id.value;
          val.attributeValues = val.attributeValues.join(',');
          val.isShow = val.isShow ? 1 : 0;
        }
        operate_role(val);
      }

      //开关事件
      function handleChange(val, item) {
        operate_type.value = 'switch';
        operate_role({
          attributeId: item.attributeId,
          isShow: val ? 1 : 0,
        });
      }

      //商品管理操作
      const operate_role = async (params) => {
        confirmBtnLoading.value = true;
        let res = {};
        if (operate_type.value == 'add') {
          res = await addAttr(params);
        } else if (operate_type.value == 'edit' || operate_type.value == 'switch') {
          res = await editAttr(params);
        } else if (operate_type.value == 'del') {
          res = await delAttr(params);
        }
        confirmBtnLoading.value = false;
        if (res.state == 200) {
          sucTip(res.msg);
          handleCancle();
          reload();
        } else {
          failTip(res.msg);
        }
      };

      return {
        standardTable,
        width,
        title,
        visible,
        content,
        confirmBtnLoading,
        operate_id,
        operate_type,
        operate_add_edit,
        handleClick,
        handleCancle,
        handleConfirm,
        handleChange,
      };
    },
  });
</script>
<style lang="less" scoped></style>
