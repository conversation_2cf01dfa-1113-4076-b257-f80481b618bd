<template>
  <PageWrapper class="common_page_toolbar common_description goods_detail">
    <SldComHeader :type="1" :title="'商品详情'" :back="true" />
    <Description
      size="middle"
      title="基本信息"
      :bordered="true"
      :column="2"
      :data="detail"
      :schema="baseSchema"
    />
    <div class="goods_detail_title">商品详情</div>
    <BasicTable @register="standardTable" style="padding: 0">
      <template #bodyCell="{ column, text }">
        <template v-if="column.key == 'specValues'">
          {{ text ? text : detail.productList.length == 1 ? '默认' : '--' }}
        </template>
        <template v-else-if="column.key == 'productCode' || column.key == 'barCode'">
          {{ text ? text : '--' }}
        </template>
        <template v-else-if="column.key == 'isDefault'">
          {{ text == 1 ? '是' : '否' }}
        </template>
        <template v-else-if="column.key == 'productStockWarning'">
          {{ text > 0 ? text : 0 }}
        </template>
        <template v-else-if="column.key">
          {{ text !== undefined && text !== null ? text : '--' }}
        </template>
      </template>
    </BasicTable>
    <div style="width: 100%; height: 5px"></div>
    <Description
      v-if="detail.imageList && detail.imageList.length > 0"
      size="middle"
      title="商品图片"
      :bordered="true"
      :column="2"
      :data="detail.imageList"
      :schema="imgSchema"
    />
    <Description
      v-if="detail.goodsVideoUrl"
      size="middle"
      title="商品视频"
      :bordered="true"
      :column="2"
      :data="detail.goodsVideoUrl"
      :schema="videoSchema"
    />
    <Description
      size="middle"
      title="发票信息"
      :bordered="true"
      :column="2"
      :data="detail"
      :schema="invoiceSchema"
    />
    <Description
      size="middle"
      title="其他信息"
      :bordered="true"
      :column="2"
      :data="detail"
      :schema="extraSchema"
    />
    <div
      v-if="detail.topTemplateContent || detail.goodsDetails || detail.bottomTemplateContent"
      class="goods_html"
    >
      <div class="goods_html_title">商品详情</div>
      <div
        v-if="detail.topTemplateContent"
        class="goods_htmls"
        v-html="quillEscapeToHtml(detail.topTemplateContent)"
      ></div>
      <div
        v-if="detail.goodsDetails"
        class="goods_htmls"
        v-html="quillEscapeToHtml(detail.goodsDetails)"
      ></div>
      <div
        v-if="detail.bottomTemplateContent"
        class="goods_htmls"
        v-html="quillEscapeToHtml(detail.bottomTemplateContent)"
      ></div>
    </div>
  </PageWrapper>
</template>
<script lang="ts">
  import { defineComponent, ref, onMounted, h } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { Description, DescItem } from '/@/components/Description/index';
  import { BasicTable, useTable } from '/@/components/Table';
  import { getGoodsDetail } from '/@/api/manage/manage';
  import { useRoute } from 'vue-router';
  import { failTip, quillEscapeToHtml } from '/@/utils/utils';

  export default defineComponent({
    components: {
      SldComHeader,
      Description,
      BasicTable,
    },
    setup() {
      const route: any = useRoute();
      const detail: any = ref({});
      const baseSchema: DescItem[] = [
        {
          field: 'isVirtualGoods',
          label: '商品类型：',
          render: function (val) {
            return val == 1 ? '实物商品' : '虚拟商品';
          },
        },
        {
          field: 'categoryPath',
          label: '商品分类：',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'goodsName',
          label: '商品名称：',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'goodsBrief',
          label: '商品广告语：',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'brandName',
          label: '品牌：',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'storeName',
          label: '店铺名称：',
          render: function (val) {
            return val ? val : '--';
          },
        },
      ];
      const imgSchema: DescItem[] = [
        {
          field: 'imageList',
          label: '商品图片：',
          render: (val, data) => {
            return data.map((items) => {
              return h('div', { style: { display: 'inline-block', marginRight: '10px' } }, [
                h('img', { src: items.imageUrl, style: { maxWidth: '100px', maxHeight: '100px' } }),
              ]);
            });
          },
        },
      ];
      const videoSchema: DescItem[] = [
        {
          field: 'goodsVideoUrl',
          label: '商品视频：',
          render: (val, data) => {
            return h('div', { style: { display: 'inline-block', marginRight: '10px' } }, [
              h('video', {
                src: data,
                style: { width: '102px', height: '102px' },
                controls: true,
                autoplay: true,
              }),
            ]);
          },
        },
      ];
      const invoiceSchema: DescItem[] = [
        {
          field: 'isVatInvoice',
          label: '是否开专票：',
          render: function (val) {
            return val == 1 ? '是' : '否';
          },
        },
      ];
      const extraSchema: DescItem[] = [
        {
          field: 'storeInnerLabelList',
          label: '店铺分类：',
          render: function (val) {
            return val && val.length > 0
              ? val.map((items, index) => {
                  return (index > 0 ? ',' : '') + items.innerLabelPath;
                })
              : '--';
          },
        },
        {
          field: 'goodsLabelList',
          label: '商品标签：',
          render: function (val) {
            return val && val.length > 0
              ? val.map((items, index) => {
                  return (index > 0 ? ',' : '') + items.labelName;
                })
              : '--';
          },
        },
        {
          field: 'virtualSales',
          label: '虚拟销量：',
          render: function (val) {
            return val !== undefined && val !== null ? val : '--';
          },
        },
        {
          field: 'sellNow',
          label: '发布状态：',
          render: function (val) {
            return val ? '立即售卖' : '放入仓库';
          },
        },
        {
          field: 'stateValue',
          label: '商品状态：',
          render: function (val) {
            return val ? val : '--';
          },
        },
        {
          field: 'storeIsRecommend',
          label: '商品推荐：',
          render: function (val) {
            return val == 1 ? '是' : '否';
          },
        },
      ];
      const columns = [
        {
          title: '规格名称',
          dataIndex: 'specValues',
          width: 100,
        },
        {
          title: '市场价(¥)',
          dataIndex: 'marketPrice',
          width: 100,
        },
        {
          title: '价格(¥)',
          dataIndex: 'productPrice',
          width: 100,
        },
        {
          title: '库存',
          dataIndex: 'productStock',
          width: 100,
        },
        {
          title: '重量(KG)',
          dataIndex: 'weight',
          width: 100,
        },
        {
          title: '长(CM)',
          dataIndex: 'length',
          width: 100,
        },
        {
          title: '宽(CM)',
          dataIndex: 'width',
          width: 100,
        },
        {
          title: '高(CM)',
          dataIndex: 'height',
          width: 100,
        },
        {
          title: '预警值',
          dataIndex: 'productStockWarning',
          width: 100,
        },
        {
          title: '货号',
          dataIndex: 'productCode',
          width: 100,
        },
        {
          title: '条形码',
          dataIndex: 'barCode',
          width: 100,
        },
        {
          title: '默认选中',
          dataIndex: 'isDefault',
          width: 100,
        },
      ];
      const [standardTable, { setProps }] = useTable({
        columns: columns,
        dataSource: [],
        showIndexColumn: true,
        pagination: false,
        bordered: true,
        striped: false,
      });

      onMounted(() => {
        get_detail();
      });

      //获取商品详情
      const get_detail = async () => {
        const res: any = await getGoodsDetail({ goodsId: route.query.id });
        if (res.state == 200 && res.data) {
          detail.value = res.data;
          setProps({
            dataSource: res.data.productList ? res.data.productList : [],
          });
        } else {
          failTip(res.msg);
        }
      };

      return {
        quillEscapeToHtml,
        detail,
        baseSchema,
        imgSchema,
        videoSchema,
        invoiceSchema,
        extraSchema,
        standardTable,
      };
    },
  });
</script>
<style lang="less">
  .goods_detail {
    .slodon-basic-table{
      height: auto !important;
    }

    .ant-spin-container {
      .ant-table-container {
        .ant-table-body {
          height: auto !important;
        }
      }
    }

    .goods_detail_title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 32px;
      margin-bottom: 10px;
      padding-left: 14px;
      color: rgb(0 0 0 / 85%);
      font-size: 14px;
      font-weight: 500;
      line-height: 24px;
      cursor: default;
    }

    .goods_html {
      .goods_html_title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 32px;
        padding-left: 14px;
        color: rgb(0 0 0 / 85%);
        font-size: 14px;
        font-weight: 500;
        line-height: 24px;
        cursor: default;
      }

      .goods_htmls {
        padding: 0.5rem;

        .ql-video {
          width: 525px;
          height: 315px;
        }

        p {
          line-height: 20px;
        }

        a {
          display: inline-block;
          margin: 5px auto;
          color: #00f;
          text-decoration: underline;
        }

        table {
          padding: 0;
          border-collapse: collapse;
        }

        td,
        th {
          padding: 5px 10px;
          border: 1px solid #ddd;
        }

        ol,
        ul {
          padding-left: 10px;
        }

        ol li,
        ul li {
          list-style: unset;
        }
      }
    }
  }
</style>
