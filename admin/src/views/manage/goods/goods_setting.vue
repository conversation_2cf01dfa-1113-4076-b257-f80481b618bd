<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <a-spin :spinning="spinning">
        <SldComHeader
          :title="'商品设置'"
        />
        <div class="site_info_scroll">
          <StandardTableRow
            width="100%"
            :data="rowData"
            @callback-event="callbackEvent"
            @submit-event="submitEvent"
            @btn-click-event="btnClickEvent"
          />
        </div>
      </a-spin>
    </div>
  </div>
</template>
<script>
  import { defineComponent, ref, onMounted } from 'vue';
  import { Spin } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import StandardTableRow from '/@/components/StandardTableRow/index.vue';
  import { getProductSetting, updateProduct, saveBasicSiteSetting } from '/@/api/manage/manage';
  import { failTip, sucTip } from '/@/utils/utils';

  export default defineComponent({
    components: {
      [Spin.name]: Spin,
      SldComHeader,
      StandardTableRow,
    },
    setup() {
      const spinning = ref(false);
      const rowData = ref([]);
      const clickEvent = ref(false); //事件防抖动

      //获取站点基本配置
      const get_base_site = async () => {
        spinning.value = true;
        const res = await getProductSetting({
          str: 'goods_publish_need_audit,goods_sort_weight_sale,goods_sort_weight_view',
        });
        if (res.state == 200 && res.data) {
          let data = [];
          res.data.map((item) => {
            if (item.type == 1) {
              data.push({
                type: 'inputnum',
                width: 300,
                label: item.title,
                desc: item.description,
                key: item.name,
                placeholder: '请输入' + item.title,
                min: 0,
                max: 100,
                precision: 0,
                value: item.value,
                callback: true,
              });
            } else if (item.type == 4) {
              data.push({
                type: 'switch',
                label: item.title,
                key: item.name,
                width: 300,
                desc_width: 300,
                initValue: '',
                value: item.value == 1 ? true : false,
                callback: true,
                desc: item.description,
              });
            }
          });
          if (data.length > 0) {
            data.push({
              type: 'button',
              width: 300,
              showSubmit: true,
              submitText: '保存',
              showCancle: false,
              btnList: [
                {
                  btnText: '立即更新商品数据',
                  type: 'updateGoods',
                  callback: true,
                },
              ],
            });
          }
          rowData.value = data;
        } else {
          failTip(res.msg);
        }
        spinning.value = false;
      };

      const callbackEvent = (item) => {
        let temp = rowData.value.filter((items) => items.key == item.contentItem.key);
        if (temp.length > 0) {
          temp[0].value = item.val1;
        }
      };

      //保存
      function submitEvent(val) {
        for (let key in val) {
          if (typeof val[key] == 'boolean') {
            val[key] = val[key] ? '1' : '0';
          }
        }
        if (clickEvent.value) return;
        clickEvent.value = true;
        save_base_site(val);
      }

      function btnClickEvent() {
        if (clickEvent.value) return;
        clickEvent.value = true;
        updateEvent();
      }

      //立即更新商品数据
      const updateEvent = async () => {
        const res = await updateProduct();
        clickEvent.value = false;
        if (res.state == 200) {
          sucTip(res.msg);
        } else {
          failTip(res.msg);
        }
      };

      //保存站点基本配置
      const save_base_site = async (params) => {
        const res = await saveBasicSiteSetting(params);
        clickEvent.value = false;
        if (res.state == 200) {
          sucTip(res.msg);
        } else {
          failTip(res.msg);
        }
      };

      onMounted(() => {
        get_base_site();
      });

      return {
        spinning,
        rowData,
        clickEvent,
        get_base_site,
        callbackEvent,
        submitEvent,
        btnClickEvent,
        updateEvent,
        save_base_site,
      };
    },
  });
</script>
<style lang="less" scoped></style>
