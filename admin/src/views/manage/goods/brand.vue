<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader :type="3" :title="'品牌管理'" :clickFlag="true" @handle-toggle-tip="handleToggleTip"/>
      <a-tabs v-model:activeKey="tabIndex" type="card" @change="handleChange">
        <a-tab-pane :key="1" tab="品牌列表" />
        <a-tab-pane :key="2" tab="待审核品牌" />
      </a-tabs>
      <div class="section_padding_tab_top"></div>
      <ShowMoreHelpTip :marginTop="0" style="margin-bottom: 10px;" :tipData="tipData[tabIndex - 1]" v-if="sld_show_tip"></ShowMoreHelpTip>
      <div :style="{ display: tabIndex == 1 ? 'block' : 'none' }">
        <BasicTable @register="standardTable" style="padding: 0">
          <template #tableTitle>
            <div class="toolbar">
              <div class="toolbar_btn" @click="handleClick(null, 'add')">
                <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#ff5908" />
                <span>新增品牌</span>
              </div>
            </div>
          </template>
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.key == 'action'">
              <span class="common_page_edit" @click="handleClick(record, 'edit')">编辑</span>
              <Popconfirm
                title="删除后不可恢复，是否确定删除？"
                @confirm="handleClick(record, 'del')"
              >
                <span class="common_page_edit">删除</span>
              </Popconfirm>
            </template>
            <template v-else-if="column.key == 'imageUrl'">
              <Popover placement="right">
                <template #content>
                  <div class="goods_list_mainIamge_pop">
                    <img :src="text" />
                  </div>
                </template>
                <img :src="text" style="max-height: 30px" />
              </Popover>
            </template>
            <template v-else-if="column.key == 'onSaleGoodsNum'">
              {{ text || 0 }}/{{ record.totalGoodsNum || 0 }}
            </template>
            <template v-else-if="column.key == 'brandDesc'">
              {{ text ? text : '--' }}
            </template>
            <template v-else-if="column.key">
              {{ text !== undefined && text !== null ? text : '--' }}
            </template>
          </template>
        </BasicTable>
      </div>
      <div :style="{ display: tabIndex == 2 ? 'block' : 'none' }">
        <BasicTable @register="auditStandardTable" style="padding: 0">
          <template #tableTitle>
            <div class="toolbar">
              <div class="toolbar_btn" @click="handleClick(null, 'audits')">
                <AliSvgIcon
                  iconName="iconshenhetongguo1"
                  width="15px"
                  height="15px"
                  fillColor="#0fb39a"
                />
                <span style="margin-left: 3px;">批量审核</span>
              </div>
              <Popconfirm
                v-if="checkedKeys.length > 0"
                title="删除后不可恢复，是否确定删除？"
                @confirm="handleClick(record, 'dels')"
              >
                <div class="toolbar_btn">
                  <AliSvgIcon
                    iconName="iconshenhejujue1"
                    width="15px"
                    height="15px"
                    fillColor="#fa0920"
                  />
                  <span>批量删除</span>
                </div>
              </Popconfirm>
              <div v-else class="toolbar_btn" @click="handleClick(null, 'dels')">
                <AliSvgIcon
                  iconName="iconshenhejujue1"
                  width="15px"
                  height="15px"
                  fillColor="#fa0920"
                />
                <span>批量删除</span>
              </div>
            </div>
          </template>
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.key == 'action'">
              <span v-if="record.state == 2" class="common_page_edit" @click="handleClick(record, 'audit')">审核</span>
              <Popconfirm
                title="删除后不可恢复，是否确定删除？"
                @confirm="handleClick(record, 'del')"
              >
                <span class="common_page_edit">删除</span>
              </Popconfirm>
            </template>
            <template v-else-if="column.key == 'imageUrl'">
              <Popover placement="right">
                <template #content>
                  <div class="goods_list_mainIamge_pop">
                    <img :src="text" />
                  </div>
                </template>
                <img :src="text" style="max-height: 30px" />
              </Popover>
            </template>
            <template v-else-if="column.key">
              {{ text ? text : '--' }}
            </template>
          </template>
        </BasicTable>
      </div>
      <SldModal
        :width="width"
        :title="title"
        :visible="visible"
        :content="content"
        :confirmBtnLoading="confirmBtnLoading"
        :showFoot="true"
        @radio-change-event="handleRadio"
        @cancle-event="handleCancle"
        @confirm-event="handleConfirm"
      />
    </div>
  </div>
</template>
<script>
  import { defineComponent, ref,onMounted } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { Tabs, Popconfirm, Popover } from 'ant-design-vue';
  import { useRoute } from 'vue-router';
  import { BasicTable, useTable } from '/@/components/Table';
  import {
    getBrandList,
    getApplyBrandList,
    addBrand,
    editBrand,
    delBrand,
    auditBrand,
  } from '/@/api/manage/manage';
  import SldModal from '@/components/SldModal/index.vue';
  import { sucTip, failTip } from '/@/utils/utils';
  import ShowMoreHelpTip from '/@/components/ShowMoreHelpTip/index.vue';

  export default defineComponent({
    components: {
      SldComHeader,
      [Tabs.name]: Tabs,
      [Tabs.TabPane.name]: Tabs.TabPane,
      BasicTable,
      Popconfirm,
      Popover,
      SldModal,
      ShowMoreHelpTip
    },
    setup() {
      const sld_show_tip = ref(true)
      const route = useRoute();
      const tabIndex = ref(1); //tab下标
      const tipData = ref([
        [
          '商品品牌建立后可与商品分类进行绑定，新增一个品牌则需要重新与商品分类建立所属关系。',
          '品牌绑定商品分类后，商家发布商品时，可根据发布的商品所在分类找到对应的所属品牌并选择。',
        ],
        [
          '审核通过后，申请品牌和分类的绑定关系生效，发布商品的时候选择该分类即可选择该品牌。',
          '分类需绑定到第三级。',
        ],
      ]);

      const checkedKeys = ref([]); //表格的键值声明，非必填，主要用于勾选功能中的数据处理
      const rowKey = ref('brandId'); //表格开启选择功能时，需要声明选中数据变量

      const [standardTable, { reload: reload, redoHeight: redoHeight }] = useTable({
        api: (arg) => getBrandList({ ...arg }),
        fetchSetting: {
          pageField: 'current',
          sizeField: 'pageSize',
          listField: 'data.list',
          totalField: 'data.pagination.total',
        },
        columns: [
          {
            title: '品牌名称',
            dataIndex: 'brandName',
            width: 100,
          },
          {
            title: '品牌LOGO',
            dataIndex: 'imageUrl',
            width: 100,
          },
          {
            title: '品牌描述',
            dataIndex: 'brandDesc',
            width: 100,
          },
          {
            title: '在售/全部商品',
            dataIndex: 'onSaleGoodsNum',
            width: 100,
          },
          {
            title: '创建时间',
            dataIndex: 'createTime',
            width: 100,
          },
        ],
        actionColumn: {
          width: 100,
          title: '操作',
          dataIndex: 'action',
        },
        useSearchForm: true,
        formConfig: {
          schemas: [
            {
              field: 'brandName',
              component: 'Input',
              colProps: { span: 6, style: 'width:260px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                maxlength: 6,
                placeholder: '请输入品牌名称',
                size: 'default',
              },
              label: '品牌名称',
              labelWidth: 80,
            },
          ],
        },
        bordered: true,
        striped: false,
        showIndexColumn: true,
      });

      const [auditStandardTable, { reload: auditReload , redoHeight: auditRedoHeight }] = useTable({
        api: (arg) => getApplyBrandList({ ...arg }),
        fetchSetting: {
          pageField: 'current',
          sizeField: 'pageSize',
          listField: 'data.list',
          totalField: 'data.pagination.total',
        },
        columns: [
          {
            title: '店铺名称',
            dataIndex: 'storeName',
            width: 100,
          },
          {
            title: '品牌名称',
            dataIndex: 'brandName',
            width: 100,
          },
          {
            title: '品牌LOGO',
            dataIndex: 'imageUrl',
            width: 100,
          },
          {
            title: '品牌分类',
            dataIndex: 'goodsCategoryPath',
            width: 150,
          },
          {
            title: '品牌描述',
            dataIndex: 'brandDesc',
            width: 100,
          },
          {
            title: '审核状态',
            dataIndex: 'stateValue',
            width: 100,
          },
          {
            title: '审核理由',
            dataIndex: 'failReason',
            width: 120,
          },
        ],
        actionColumn: {
          width: 100,
          title: '操作',
          dataIndex: 'action',
        },
        useSearchForm: true,
        formConfig: {
          schemas: [
            {
              field: 'storeName',
              component: 'Input',
              colProps: { span: 6, style: 'width:260px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                placeholder: '请输入店铺名称',
                size: 'default',
              },
              label: '店铺名称',
              labelWidth: 80,
            },
            {
              field: 'brandName',
              component: 'Input',
              colProps: { span: 6, style: 'width:260px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                maxlength: 6,
                placeholder: '请输入品牌名称',
                size: 'default',
              },
              label: '品牌名称',
              labelWidth: 80,
            },
            {
              field: 'state',
              component: 'Select',
              colProps: { span: 6, style: 'width:240px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                placeholder: '请选择审核状态',
                options: [
                  { key: 0, label: '全部', value: '' },
                  { key: 2, label: '待审核', value: '2' },
                  { key: 3, label: '审核拒绝', value: '3' },
                ],
                size: 'default',
              },
              label: '审核状态',
              labelWidth: 80,
            },
          ],
        },
        bordered: true,
        striped: false,
        showIndexColumn: true,
        rowKey: rowKey,
        rowSelection: {
          type: 'checkbox',
          selectedRowKeys: checkedKeys,
          onSelect: onSelect,
          onSelectAll: onSelectAll,
          getCheckboxProps(record) {
            if (record.state != 2) {
              return { disabled: true };
            } else {
              return { disabled: false };
            }
          },
        },
        clickToRowSelect: false,
      });

      const width = ref(550);
      const title = ref('');
      const visible = ref(false);
      const content = ref([]);
      const confirmBtnLoading = ref(false);
      const operate_id = ref('');
      const operate_type = ref('');
      const operate_add_edit_data = ref([
        {
          type: 'input',
          label: `品牌名称`,
          name: 'brandName',
          placeholder: `请输入品牌名称`,
          extra: `最多输入20个字`,
          maxLength: 20,
          rules: [
            {
              required: true,
              message: `请输入品牌名称`,
            },
          ],
        },
        {
          type: 'upload_img_upload',
          label: '品牌LOGO',
          name: 'imageUrl',
          extra: '建议上传宽153*高54的图片',
          initialValue: [],
          upload_name: 'file',
          upload_url: 'v3/oss/admin/upload?source=setting',
          limit: 10,
          accept: '.jpg, .jpeg, .png',
          rules: [
            {
              required: true,
              message: `请上传品牌LOGO`,
            },
          ],
        },
        {
          type: 'textarea',
          label: `品牌描述`,
          name: 'brandDesc',
          placeholder: `请输入品牌描述`,
          extra: `最多输入200个字`,
          maxLength: 200,
        },
      ]);
      const operate_audit_data = ref([
        {
          type: 'radio_select',
          label: `审核结果`,
          name: 'state',
          extra: `请选择审核结果`,
          data: [
            { key: 1, value: `通过` },
            { key: 0, value: `拒绝` },
          ],
          initialValue: 1,
          callback: true,
        },
      ]);

      const click_event = ref(false);

      //tab切换
      function handleChange(e) {
        if (e == 1) {
          reload();
        } else {
          auditReload();
        }
      }

      //表格的单行选中回调事件
      function onSelect(record, selected) {
        if (selected) {
          checkedKeys.value = [...checkedKeys.value, record[rowKey.value]];
        } else {
          checkedKeys.value = checkedKeys.value.filter((item) => item != record[rowKey.value]);
        }
      }

      //表格的全选回调事件
      function onSelectAll(selected, selectedRows, changeRows) {
        const changeIds = changeRows.map((item) => item[rowKey.value]);
        if (selected) {
          checkedKeys.value = [...checkedKeys.value, ...changeIds];
        } else {
          checkedKeys.value = checkedKeys.value.filter((item) => {
            return !changeIds.includes(item);
          });
        }
      }

      //表格点击回调事件
      function handleClick(item, type) {
        if (click_event.value) return;
        operate_type.value = type;
        if (item && item.brandId) {
          operate_id.value = item.brandId;
        }
        let params = {};
        if (type == 'add') {
          content.value = JSON.parse(JSON.stringify(operate_add_edit_data.value));
          width.value = 550;
          title.value = '新增品牌';
          visible.value = true;
        } else if (type == 'edit') {
          let data = JSON.parse(JSON.stringify(operate_add_edit_data.value));
          data.map((items) => {
            if (items.name == 'imageUrl') {
              items.initialValue = item[items.name]
                ? [
                    {
                      uid: 'image_1',
                      name: 'image.png',
                      status: 'done',
                      url: item[items.name],
                    },
                  ]
                : [];
            } else {
              items.initialValue = item[items.name] ? item[items.name] : undefined;
            }
          });
          content.value = data;
          width.value = 550;
          title.value = '编辑品牌';
          visible.value = true;
        } else if (type == 'del') {
          params.brandIds = item.brandId;
          click_event.value = true;
          operate_role(params);
        } else if (type == 'dels') {
          if (checkedKeys.value.length == 0) {
            failTip('请先选中数据');
            return;
          }
          params.brandIds = checkedKeys.value.join(',');
          click_event.value = true;
          operate_role(params);
        } else if (type == 'audit' || type == 'audits') {
          if (type == 'audits' && checkedKeys.value.length == 0) {
            failTip('请先选中数据');
            return;
          }
          content.value = JSON.parse(JSON.stringify(operate_audit_data.value));
          width.value = 550;
          title.value = '审核品牌';
          visible.value = true;
        }
      }

      //操作
      const operate_role = async (params) => {
        let res = {};
        if (operate_type.value == 'add') {
          res = await addBrand(params);
        } else if (operate_type.value == 'edit') {
          res = await editBrand(params);
        } else if (operate_type.value == 'del' || operate_type.value == 'dels') {
          res = await delBrand(params);
        } else if (operate_type.value == 'audit' || operate_type.value == 'audits') {
          res = await auditBrand(params);
        }
        click_event.value = false;
        if (res.state == 200) {
          sucTip(res.msg);
          if (
            operate_type.value == 'add' ||
            operate_type.value == 'edit' ||
            operate_type.value == 'audit' ||
            operate_type.value == 'audits'
          ) {
            handleCancle();
          }
          reload();
          auditReload();
        } else {
          failTip(res.msg);
        }
      };

      //弹窗回调事件
      function handleRadio(val) {
        let temp = content.value.filter((item) => item.name == 'state');
        temp[0].initialValue = val;
        content.value = content.value.filter((item) => item.name != 'auditReason');
        if (val == 0) {
          content.value.push({
            type: 'textarea',
            label: `审核意见`,
            name: 'auditReason',
            placeholder: `请输入审核意见`,
            extra: `最多100字`,
            maxLength: 100,
            rules: [
              {
                required: true,
                whitespace: true,
                message: `请输入审核意见`,
              },
            ],
          });
        }
      }

      const handleToggleTip = (type) => {
        sld_show_tip.value = type;
        if(tabIndex.value==1){
          redoHeight()
        }
        if(tabIndex.value==2){
          auditRedoHeight()
        }
      };

      //弹窗取消事件
      function handleCancle() {
        visible.value = false;
        content.value = [];
        operate_id.value = '';
        checkedKeys.value = [];
      }

      //弹窗确认事件
      function handleConfirm(val) {
        if (click_event.value) return;
        if (operate_type.value == 'add') {
          val.image = val.imageUrl && val.imageUrl.length > 0 ? val.imageUrl[0].url : '';
          delete val.imageUrl;
        } else if (operate_type.value == 'edit') {
          val.brandId = operate_id.value;
          val.isRecommend = 0;
          val.image = val.imageUrl && val.imageUrl.length > 0 ? val.imageUrl[0].url : '';
          delete val.imageUrl;
        } else if (operate_type.value == 'audit') {
          val.brandIds = operate_id.value;
        } else if (operate_type.value == 'audits') {
          val.brandIds = checkedKeys.value.join(',');
        }
        click_event.value = true;
        operate_role(val);
      }

      onMounted(() => {
        if(route.query.tab&&route.query.tab=='check'){
          tabIndex.value = 2
          handleChange(2)
        }
      });

      return {
        tabIndex,
        tipData,
        checkedKeys,
        rowKey,
        standardTable,
        auditStandardTable,
        handleChange,
        handleClick,
        width,
        title,
        visible,
        content,
        confirmBtnLoading,
        operate_id,
        operate_type,
        operate_add_edit_data,
        operate_audit_data,
        handleRadio,
        handleCancle,
        handleConfirm,
        click_event,
        sld_show_tip,
        handleToggleTip
      };
    },
  });
</script>
<style lang="less" scoped></style>
