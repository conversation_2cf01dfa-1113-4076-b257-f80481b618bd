<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader :type="1" :title="'商品标签管理'" />
      <BasicTable @register="standardTable" style="padding: 0">
        <template #tableTitle>
          <div class="toolbar">
            <div class="toolbar_btn" @click="handleClick(null, 'add')">
              <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#fff" />
              <span>新增标签</span>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.key == 'action'">
            <template v-if="record.isInner == 1"> -- </template>
            <template v-else>
              <span class="common_page_edit" @click="handleClick(record, 'edit')">编辑</span>
              <Popconfirm
                title="删除后不可恢复，是否确定删除？"
                @confirm="handleClick(record, 'del')"
              >
                <span class="common_page_edit">删除</span>
              </Popconfirm>
            </template>
          </template>
          <template v-else-if="column.key=='sort'">
            {{ text||text==0 ? text : '--' }}
          </template>
          <template v-else-if="column.key">
            {{ text ? text : '--' }}
          </template>
        </template>
      </BasicTable>
      <SldModal
        :width="width"
        :title="title"
        :visible="visible"
        :content="content"
        :confirmBtnLoading="confirmBtnLoading"
        :showFoot="true"
        @cancle-event="handleCancle"
        @confirm-event="handleConfirm"
      />
    </div>
  </div>
</template>
<script>
  import { defineComponent, ref } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import SldModal from '@/components/SldModal/index.vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import { Popconfirm } from 'ant-design-vue';
  import { getLabelList, addLabel, editLabel, delLabel } from '/@/api/manage/manage';
  import { sucTip, failTip } from '/@/utils/utils';

  export default defineComponent({
    components: {
      SldComHeader,
      SldModal,
      BasicTable,
      Popconfirm,
    },
    setup() {
      const [standardTable, { reload }] = useTable({
        api: (arg) => getLabelList({ ...arg }),
        fetchSetting: {
          pageField: 'current',
          sizeField: 'pageSize',
          listField: 'data.list',
          totalField: 'data.pagination.total',
        },
        columns: [
          {
            title: '标签名称',
            dataIndex: 'labelName',
            width: 120,
          },
          {
            title: '标签描述',
            dataIndex: 'description',
            width: 200,
          },
          {
            title: '排序',
            dataIndex: 'sort',
            width: 80,
          },
          {
            title: '添加时间',
            dataIndex: 'createTime',
            width: 100,
          },
          {
            title: '更新时间',
            dataIndex: 'updateTime',
            width: 100,
          },
        ],
        actionColumn: {
          width: 100,
          title: '操作',
          dataIndex: 'action',
        },
        useSearchForm: true,
        formConfig: {
          schemas: [
            {
              field: 'labelName',
              component: 'Input',
              colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                maxlength: 10,
                placeholder: '请输入商品标签',
                size: 'default',
              },
              label: '商品标签',
              labelWidth: 70,
            },
          ],
        },
        bordered: true,
        striped: false,
        showIndexColumn: true,
      });
      const width = ref(500);
      const title = ref('');
      const visible = ref(false);
      const content = ref([]);
      const confirmBtnLoading = ref(false);
      const operate_id = ref('');
      const operate_type = ref('');

      const operate_add_edit = ref([
        {
          type: 'input',
          label: `商品标签`,
          name: 'labelName',
          placeholder: `请输入商品标签`,
          extra: '最多输入20个字',
          maxLength: 20,
          showCount: true,
          rules: [
            {
              required: true,
              whitespace: true,
              message: '请输入商品标签',
            },
          ],
        },
        {
          type: 'textarea',
          label: `标签描述`,
          name: 'description',
          placeholder: `请输入标签描述`,
          extra: '请输入100字以内的描述',
          maxLength: 100,
          showCount: true,
          rules: [
            {
              required: true,
              whitespace: true,
              message: '请输入标签描述',
            },
          ],
        },
        {
          type: 'inputnum',
          label: `排序`,
          name: 'sort',
          placeholder: `请输入排序`,
          extra: '请输入0~255的数字，值越小显示越靠前',
          min: 0,
          max: 255,
          rules: [
            {
              required: true,
              message: `请输入排序`,
            },
          ],
        },
      ]); //添加、编辑商品标签弹窗数据

      //表格点击回调事件
      function handleClick(item, type) {
        operate_type.value = type;
        if (item) {
          operate_id.value = item.labelId;
        }
        if (type == 'add') {
          title.value = '添加商品标签';
          visible.value = true;
          content.value = JSON.parse(JSON.stringify(operate_add_edit.value));
        } else if (type == 'edit') {
          title.value = '编辑商品标签';
          visible.value = true;
          let data = JSON.parse(JSON.stringify(operate_add_edit.value));
          data.map((items) => {
            items.initialValue = item[items.name]||item[items.name]==0 ? item[items.name] : undefined;
          });
          content.value = data;
        } else if (type == 'del') {
          operate_role({ labelIds: item.labelId });
        }
      }

      //弹窗取消事件
      function handleCancle() {
        visible.value = false;
        content.value = [];
        operate_id.value = '';
      }

      //弹窗确认事件
      function handleConfirm(val) {
        if (operate_type.value == 'edit') {
          val.labelId = operate_id.value;
        }
        operate_role(val);
      }

      //商品标签操作
      const operate_role = async (params) => {
        confirmBtnLoading.value = true;
        let res = {};
        if (operate_type.value == 'add') {
          res = await addLabel(params);
        } else if (operate_type.value == 'edit') {
          res = await editLabel(params);
        } else if (operate_type.value == 'del') {
          res = await delLabel(params);
        }
        confirmBtnLoading.value = false;
        if (res.state == 200) {
          sucTip(res.msg);
          handleCancle();
          reload();
        } else {
          failTip(res.msg);
        }
      };

      return {
        standardTable,
        width,
        title,
        visible,
        content,
        confirmBtnLoading,
        operate_id,
        operate_type,
        operate_add_edit,
        handleClick,
        handleCancle,
        handleConfirm,
      };
    },
  });
</script>
<style lang="less" scoped></style>
