<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader
        :type="2"
        :title="'分类管理'"
        :showTipBtn="false"
        :tipData="['当分类数据有更新的时候，需点击`更新分类缓存`按钮才能生效']"
      />
      <BasicTable @register="standardTable" style="padding: 0">
        <template #tableTitle>
          <div class="toolbar">
            <div class="toolbar_btn" @click="handleClick(null, 'add')">
              <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#ff5908" />
              <span>新增</span>
            </div>
            <div class="toolbar_btn" @click="handleClick(null, 'cache')">
              <AliSvgIcon iconName="iconshuaxin1" width="15px" height="15px" fillColor="#ff5908" />
              <span>更新分类缓存</span>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.key == 'action'">
            <span class="common_page_edit" @click="handleClick(record, 'edit')">编辑</span>
            <span
              v-if="record.grade < 3"
              class="common_page_edit"
              @click="handleClick(record, 'child')"
              >添加下级分类</span
            >
            <Popconfirm
              v-if="record.children === null"
              title="删除后不可恢复，是否确定删除？"
              @confirm="handleClick(record, 'del')"
            >
              <span class="common_page_edit">删除</span>
            </Popconfirm>
          </template>
          <template v-else-if="column.key == 'onSaleGoodsNum'">
            {{ text || 0 }}/{{ record.totalGoodsNum || 0 }}
          </template>
          <template v-else-if="column.key == 'scaling'">
            {{ text ? Number(text * 100).toFixed(1) + '%' : '--' }}
          </template>
          <template v-else-if="column.key">
            {{ text !== undefined && text !== null ? text : '--' }}
          </template>
        </template>
      </BasicTable>
      <SldModal
        :width="width"
        :title="title"
        :visible="visible"
        :content="content"
        :confirmBtnLoading="confirmBtnLoading"
        :showFoot="true"
        :parentPage="'cate_lists'"
        @tag-select-event="handleTagSelect"
        @cancle-event="handleCancle"
        @confirm-event="handleConfirm"
        @callback-event="handleChange"
        @tree-select-change-event="handleSelectCate"
      />
    </div>
  </div>
</template>
<script>
  import SldModal from '@/components/SldModal/index.vue';
import { Popconfirm, Tabs } from 'ant-design-vue';
import { defineComponent, onMounted, ref } from 'vue';
import {
addCategory,
delCategory,
editCategory,
getCategoryList,
getCategoryTree,
updateCategory,
} from '/@/api/manage/manage';
import SldComHeader from '/@/components/SldComHeader/index.vue';
import { BasicTable, useTable } from '/@/components/Table';
import { failTip, sucTip } from '/@/utils/utils';

  export default defineComponent({
    components: {
      SldComHeader,
      [Tabs.name]: Tabs,
      [Tabs.TabPane.name]: Tabs.TabPane,
      BasicTable,
      Popconfirm,
      SldModal,
    },
    setup() {
      const dataSource = ref([]);
      const [standardTable, { collapseAll, setTableData }] = useTable({
        isTreeTable: true,
        fetchSetting: {
          pageField: 'current',
          sizeField: 'pageSize',
          listField: 'data.list',
        },
        columns: [
          {
            title: '分类名称',
            dataIndex: 'categoryName',
            width: 150,
          },
          {
            title: '排序',
            dataIndex: 'sort',
            width: 100,
          },
          {
            title: '在售/全部商品',
            dataIndex: 'onSaleGoodsNum',
            width: 100,
          },
          {
            title: '分佣比例',
            dataIndex: 'scaling',
            width: 100,
          },
        ],
        actionColumn: {
          width: 120,
          title: '操作',
          dataIndex: 'action',
        },
        dataSource: dataSource,
        bordered: true,
        striped: false,
        rowKey: 'categoryId',
        onExpand: (expande, record) => onExpandTable(expande, record),
        pagination: false,
      });
      const click_event = ref(false);

      const width = ref(550);
      const title = ref('');
      const visible = ref(false);
      const content = ref([]);
      const confirmBtnLoading = ref(false);
      const operate_type = ref('');
      const operate_item = ref('');
      const operate_data = ref([
        {
          type: 'input',
          label: `分类名称`,
          name: 'categoryName',
          placeholder: `请输入分类名称`,
          extra: `最多输入6个字`,
          maxLength: 6,
          rules: [
            {
              required: true,
              message: `请输入分类名称`,
            },
          ],
          callback: true,
        },
        {
          type: 'TreeSelect',
          label: `上级分类`,
          name: 'pid',
          placeholder: `请选择上级分类`,
          allowClear: true,
          initialValue: undefined,
          data: [],
          treeNodeFilterProp: 'title',
          colSpan: 16,
          callback: true,
        },
        {
          type: 'inputnum',
          label: `排序`,
          name: 'sort',
          placeholder: `请输入排序`,
          extra: '请输入0~255的数字,值越小,显示越靠前',
          min: 0,
          max: 255,
          rules: [
            {
              required: true,
              message: `请输入排序`,
            },
          ],
          callback: true,
        },
      ]);
      const operate_third_data = ref([
        {
          type: 'input',
          label: `分类名称`,
          name: 'categoryName',
          placeholder: `请输入分类名称`,
          extra: `最多输入6个字`,
          maxLength: 6,
          rules: [
            {
              required: true,
              message: `请输入分类名称`,
            },
          ],
          callback: true,
        },
        {
          type: 'tag_show_btn_sel',
          label: `绑定品牌`,
          name: 'bindBrands',
          btnText: `选择品牌`,
          extra: ``,
          initialValue: undefined,
          data: [],
          ids: [],
          callback: true,
          diy: true,
          diy_key: 'brandName',
        },
        {
          type: 'tag_show_btn_sel',
          label: `绑定属性`,
          name: 'bindAttributes',
          btnText: `选择属性`,
          extra: ``,
          initialValue: undefined,
          data: [],
          ids: [],
          callback: true,
          diy: true,
          diy_key: 'attributeName',
        },
        {
          type: 'TreeSelect',
          label: `上级分类`,
          name: 'pid',
          placeholder: `请选择上级分类`,
          extra: `默认为最顶级`,
          allowClear: true,
          initialValue: undefined,
          data: [],
          treeNodeFilterProp: 'title',
          colSpan: 16,
        },
        {
          type: 'inputnum',
          label: `分佣比例`,
          name: 'scaling',
          placeholder: `请输入分佣比例`,
          extra: '请输入0~100的数字,最多1位小数',
          min: 0,
          max: 100,
          precision: 1,
          formatter: '%',
          rules: [
            {
              required: true,
              message: `请输入分佣比例`,
            },
          ],
          callback: true,
        },
        {
          type: 'inputnum',
          label: `排序`,
          name: 'sort',
          placeholder: `请输入排序`,
          extra: '请输入0~255的数字,值越小,显示越靠前',
          min: 0,
          max: 255,
          rules: [
            {
              required: true,
              message: `请输入排序`,
            },
          ],
          callback: true,
        },
      ]);

      //表格点击回调事件
      function handleClick(item, type) {
        if (click_event.value) return;
        operate_type.value = type;
        operate_item.value = item ? item : null;
        let params = {};
        let data = JSON.parse(JSON.stringify(operate_data.value));
        if (type == 'add') {
          title.value = '新增商品分类';
        } else if (type == 'edit') {
          if (item.grade > 2) {
            data = JSON.parse(JSON.stringify(operate_third_data.value));
          }
          title.value = '编辑商品分类';
          data = data.filter((items) => items.name != 'pid');
          data.map((items) => {
            if (items.name == 'scaling') {
              items.initialValue = item[items.name] ? Number(item[items.name] * 100).toFixed(1) : 0;
            } else if (items.name == 'bindBrands') {
              items.data = item.goodsBrandList || [];
              let ids = [];
              items.data.map((brand_items) => {
                ids.push(brand_items.brandId);
              });
              items.ids = ids;
              items.initialValue = ids.join(',');
            } else if (items.name == 'bindAttributes') {
              items.data = item.goodsAttributeList || [];
              let ids = [];
              items.data.map((attr_items) => {
                ids.push(attr_items.attributeId);
              });
              items.ids = ids;
              items.initialValue = ids.join(',');
            } else {
              items.initialValue = item[items.name]||item[items.name]==0 ? item[items.name] : undefined;
            }
          });
        } else if (type == 'child') {
          if (item.grade > 1) {
            data = JSON.parse(JSON.stringify(operate_third_data.value));
          }
          let temp = data.filter((items) => items.name == 'pid');
          if (temp.length > 0) {
            temp[0].initialValue = item.categoryId;
            temp[0].disabled = true;
          }
          title.value = '添加下级分类';
        } else if (type == 'del') {
          params.categoryId = item.categoryId;
          click_event.value = true;
          operate_role(params);
          return;
        } else if (type == 'cache') {
          operate_role(null);
          return;
        } else {
          return;
        }
        width.value = 550;
        content.value = data;
        visible.value = true;
      }

      //操作
      const operate_role = async (params) => {
        let res = {};
        if (operate_type.value == 'add') {
          params.pid = params.pid !== undefined ? params.pid : 0;
          res = await addCategory(params);
        } else if (operate_type.value == 'edit') {
          res = await editCategory(params);
        } else if (operate_type.value == 'child') {
          res = await addCategory(params);
        } else if (operate_type.value == 'del') {
          res = await delCategory(params);
        } else if (operate_type.value == 'cache') {
          res = await updateCategory();
        }
        if (res.state == 200) {
          sucTip(res.msg);
          if (operate_type.value == 'add') {
            get_category_list({ categoryId: 0, pageSize: 10000,isSort:true }, { grade: 0, pid: 0 });
            collapseAll();
          } else if (operate_type.value == 'edit') {
            if (operate_item.value.grade == 1) {
              let temp = dataSource.value.filter(
                (items) => items.categoryId == operate_item.value.categoryId,
              );
              if (temp.length > 0) {
                temp[0].categoryName = params.categoryName;
                temp[0].sort = params.sort;
              }
            } else if (operate_item.value.grade == 2) {
              let temp = dataSource.value.filter(
                (items) => items.categoryId == operate_item.value.pid,
              );
              if (temp.length > 0) {
                let temps = temp[0].children.filter(
                  (items) => items.categoryId == operate_item.value.categoryId,
                );
                temps[0].categoryName = params.categoryName;
                temps[0].sort = params.sort;
              }
            } else if (operate_item.value.grade == 3) {
              let temp = dataSource.value.filter(
                (items) => items.categoryId == operate_item.value.path.split('/')[1],
              );
              if (temp.length > 0) {
                let temps = temp[0].children.filter(
                  (items) => items.categoryId == operate_item.value.pid,
                );
                if (temps.length > 0) {
                  let tempss = temps[0].children.filter(
                    (items) => items.categoryId == operate_item.value.categoryId,
                  );
                  if (tempss.length > 0) {
                    for (let key in params) {
                      if (key == 'bindBrands') {
                        let content_temp = content.value.filter(content_item => content_item.name == 'bindBrands')[0];
                        tempss[0]['goodsBrandList'] = JSON.parse(JSON.stringify(content_temp.data));
                      } else if (key == 'bindAttributes') {
                        let content_temp = content.value.filter(content_item => content_item.name == 'bindAttributes')[0];
                        tempss[0]['goodsAttributeList'] = JSON.parse(JSON.stringify(content_temp.data));;
                      }
                      tempss[0][key] = params[key];
                    }
                  }
                }
              }
            }
            setTableData(dataSource.value);
          } else if (operate_type.value == 'child') {
            get_category_list(
              { categoryId: operate_item.value.categoryId, pageSize: 10000 },
              operate_item.value,
            );
          } else if (operate_type.value == 'del') {
            if (operate_item.value.grade == 1) {
              dataSource.value = dataSource.value.filter(
                (items) => items.categoryId != operate_item.value.categoryId,
              );
            } else if (operate_item.value.grade == 2) {
              let temp = dataSource.value.filter(
                (items) => items.categoryId == operate_item.value.pid,
              );
              if (temp.length > 0) {
                temp[0].children = temp[0].children.filter(
                  (items) => items.categoryId != operate_item.value.categoryId,
                );
                if (temp[0].children.length == 0) {
                  temp[0].children = null;
                }
              }
            } else if (operate_item.value.grade == 3) {
              let temp = dataSource.value.filter(
                (items) => items.categoryId == operate_item.value.path.split('/')[1],
              );
              if (temp.length > 0) {
                let temps = temp[0].children.filter(
                  (items) => items.categoryId == operate_item.value.pid,
                );
                if (temps.length > 0) {
                  temps[0].children = temps[0].children.filter(
                    (items) => items.categoryId != operate_item.value.categoryId,
                  );
                  if (temps[0].children.length == 0) {
                    temps[0].children = null;
                  }
                }
              }
            }
          }
          handleCancle();
          get_category_tree();
          click_event.value = false;
        } else {
          click_event.value = false;
          failTip(res.msg);
        }
      };

      //弹窗取消事件
      function handleCancle() {
        visible.value = false;
        content.value = [];
        operate_item.value = '';
      }

      //弹窗确认事件
      function handleConfirm(val) {
        if (click_event.value) return;
        if (operate_type.value == 'edit' || operate_type.value == 'child') {
          val.categoryId = operate_item.value.categoryId;
        }
        if (val.scaling) {
          val.scaling = (val.scaling / 100).toFixed(3);
        }
        click_event.value = true;
        operate_role(val);
      }

      //弹窗编辑事件
      function handleChange(item) {
        let temp = content.value.filter((items) => items.name == item.contentItem.name);
        if (temp.length > 0) {
          temp[0].initialValue = item.val1;
        }
      }

      //选择上级分类事件
      function handleSelectCate(id) {
        let cate_temp = operate_data.value.filter((item) => item.name == 'pid')[0].data;
        if (cate_temp?.filter(cate_item => cate_item.key == id).length == 0 && content.value.filter(content_temp => content_temp.name == 'bindBrands').length == 0) {
          let brand_temp = JSON.parse(JSON.stringify(operate_third_data.value.filter(third_item => third_item.name == 'bindBrands')[0]));
          let attribute_temp = JSON.parse(JSON.stringify(operate_third_data.value.filter(third_item => third_item.name == 'bindAttributes')[0]));
          let scaling_temp = JSON.parse(JSON.stringify(operate_third_data.value.filter(third_item => third_item.name == 'scaling')[0]));
          content.value.splice(2, 0, brand_temp, attribute_temp);
          content.value.push(scaling_temp);
        } else if (cate_temp?.filter(cate_item => cate_item.key == id).length > 0 && content.value.filter(content_temp => content_temp.name == 'bindBrands').length > 0) {
          content.value = content.value.filter(content_temp =>
            content_temp.name != 'bindBrands' && content_temp.name != 'bindAttributes' && content_temp.name != 'scaling');
        }
        let content_temp = content.value.filter((item) => item.name == 'pid');
        if (content_temp?.length > 0) {
          content_temp[0].initialValue = id;
        }
      }

      //获取分类树数据
      const get_category_tree = async () => {
        const res = await getCategoryTree({ pId: 0, grade: 2 });
        if (res.state == 200 && res.data) {
          let data = [];
          for (let i = 0; i < res.data.length; i++) {
            let obj = {};
            obj.key = res.data[i].categoryId;
            obj.value = res.data[i].categoryId;
            obj.title = res.data[i].categoryName;
            obj.children = [];
            if (res.data[i].children) {
              for (let j = 0; j < res.data[i].children.length; j++) {
                let child_obj = {};
                child_obj.key = res.data[i].children[j].categoryId;
                child_obj.value = res.data[i].children[j].categoryId;
                child_obj.title = res.data[i].children[j].categoryName;
                obj.children.push(child_obj);
              }
            }
            data.push(obj);
          }
          let temp = operate_data.value.filter((item) => item.name == 'pid');
          if (temp.length > 0) {
            temp[0].data = data;
          }
          let temps = operate_third_data.value.filter((item) => item.name == 'pid');
          if (temps.length > 0) {
            temps[0].data = data;
          }
        }
      };

      //表格展开关闭事件
      function onExpandTable(expande, record) {
        if (expande && (!record.children || record.children.length == 0)) {
          get_category_list({ categoryId: record.categoryId, pageSize: 10000,isSort:true}, record);
        }
      }

      //获取分类列表数据
      const get_category_list = async (params, extra) => {
        const res = await getCategoryList(params);
        if (res.state == 200 && res.data && res.data.list) {
          if (extra.grade == 0) {
            dataSource.value = res.data.list;
          } else if (extra.grade == 1) {
            let temp = dataSource.value.filter((item) => item.categoryId == extra.categoryId);
            if (temp.length > 0) {
              temp[0].children = res.data.list;
            }
          } else if (extra.grade == 2) {
            let temp = dataSource.value.filter((item) => item.categoryId == extra.pid);
            if (temp.length > 0) {
              let temps = temp[0].children.filter((item) => item.categoryId == extra.categoryId);
              if (temps.length > 0) {
                temps[0].children = res.data.list;
              }
            }
          } else if (extra.grade == 3) {
            let temp = dataSource.value.filter(
              (item) => item.categoryId == extra.path.split('/')[1],
            );
            if (temp.length > 0) {
              let temps = temp[0].children.filter((item) => item.categoryId == extra.pid);
              if (temps.length > 0) {
                let tempss = temps[0].children.filter(
                  (item) => item.categoryId == extra.categoryId,
                );
                if (tempss.length > 0) {
                  tempss[0].children = res.data.list;
                }
              }
            }
          }
        } else {
          failTip(res.msg);
        }
      };

      function handleTagSelect(type, item, rows, rowKeys) {
        let temp = content.value.filter((items) => items.name == item.name);
        if (temp.length > 0) {
          if (type == 'add') {
            temp[0].data = rows;
            temp[0].ids = rowKeys;
            temp[0].initialValue = rowKeys.join(',');
          } else if (type == 'del') {
            if (temp[0].name == 'bindBrands') {
              temp[0].data = temp[0].data.filter((items) => items.brandId != rows.brandId);
              temp[0].ids = temp[0].ids.filter((items) => items != rows.brandId);
              temp[0].initialValue = temp[0].ids.join(',');
            } else if (temp[0].name == 'bindAttributes') {
              temp[0].data = temp[0].data.filter((items) => items.attributeId != rows.attributeId);
              temp[0].ids = temp[0].ids.filter((items) => items != rows.attributeId);
              temp[0].initialValue = temp[0].ids.join(',');
            }
          }
        }
      }

      onMounted(() => {
        get_category_list({ categoryId: 0, pageSize: 10000,isSort:true }, { grade: 0, pid: 0 });
        get_category_tree();
      });

      return {
        dataSource,
        standardTable,
        collapseAll,
        handleClick,
        width,
        title,
        visible,
        content,
        confirmBtnLoading,
        operate_item,
        operate_type,
        operate_data,
        operate_third_data,
        handleCancle,
        handleConfirm,
        handleChange,
        handleSelectCate,
        click_event,
        get_category_tree,
        onExpandTable,
        get_category_list,
        handleTagSelect,
      };
    },
  });
</script>
<style lang="less" scoped></style>
