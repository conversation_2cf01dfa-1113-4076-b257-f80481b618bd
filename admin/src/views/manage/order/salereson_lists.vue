<template>
  <div class="section_padding">
    <div class="section_padding_back_add salereson_lists">
      <SldComHeader
        :type="2"
        :title="'订单原因管理'"
        :tipData="tipData[tabIndex - 1]"
        :showTipBtn="false"
      />
      <a-tabs v-model:activeKey="tabIndex" type="card" @change="handleChange">
        <a-tab-pane :key="1" tab="违规下架" />
        <a-tab-pane :key="2" tab="商品审核拒绝" />
        <a-tab-pane :key="3" tab="入驻审核拒绝" />
        <a-tab-pane :key="4" tab="会员取消原因" />
        <a-tab-pane :key="5" tab="仅退款-未收货" />
        <a-tab-pane :key="6" tab="仅退款-已收货" />
        <a-tab-pane :key="7" tab="退款退货原因" />
        <a-tab-pane :key="8" tab="商户取消订单" />
      </a-tabs>
      <div class="section_padding_tab_top"></div>
      <div class="flex_row_start_center table_search">
        <div class="flex_row_center_center table_search_btn" @click="handleClick(null, 'add')">
          <AliSvgIcon iconName="iconxinzeng" width="14px" height="14px" />
          <span>新增原因</span>
        </div>
        <div class="table_search_form">
          <BasicForm @register="register" @submit="handleSubmit" @reset="handleReset" />
        </div>
      </div>
      <template v-if="tabIndex == 1">
        <BasicTable @register="standardTableLockoff" style="padding: 0">
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.key == 'action'">
              <template v-if="record.isInner == 1">--</template>
              <template v-else>
                <span class="common_page_edit" @click="handleClick(record, 'edit')">编辑</span>
                <Popconfirm
                  title="删除后不可恢复，是否确定删除？"
                  @confirm="handleClick({ reasonId: record.reasonId }, 'del')"
                >
                  <span class="common_page_edit">删除</span>
                </Popconfirm>
              </template>
            </template>
            <template v-else-if="column.key == 'isShow'">
              <template v-if="record.isInner == 1">--</template>
              <template v-else>
                <Switch
                  :checked="text == 1 ? true : false"
                  @change="
                    (checked) =>
                      handleClick({ reasonId: record.reasonId, isShow: checked ? 1 : 0 }, 'switch')
                  "
                />
              </template>
            </template>
            <template v-else-if="column.key">
              {{ text || text === 0 ? text : '--' }}
            </template>
          </template>
        </BasicTable>
      </template>
      <template v-else-if="tabIndex == 2">
        <BasicTable @register="standardTableGoods" style="padding: 0">
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.key == 'action'">
              <template v-if="record.isInner == 1">--</template>
              <template v-else>
                <span class="common_page_edit" @click="handleClick(record, 'edit')">编辑</span>
                <Popconfirm
                  title="删除后不可恢复，是否确定删除？"
                  @confirm="handleClick({ reasonId: record.reasonId }, 'del')"
                >
                  <span class="common_page_edit">删除</span>
                </Popconfirm>
              </template>
            </template>
            <template v-else-if="column.key == 'isShow'">
              <template v-if="record.isInner == 1">--</template>
              <template v-else>
                <Switch
                  :checked="text == 1 ? true : false"
                  @change="
                    (checked) =>
                      handleClick({ reasonId: record.reasonId, isShow: checked ? 1 : 0 }, 'switch')
                  "
                />
              </template>
            </template>
            <template v-else-if="column.key">
              {{ text || text === 0 ? text : '--' }}
            </template>
          </template>
        </BasicTable>
      </template>
      <template v-else-if="tabIndex == 3">
        <BasicTable @register="standardTableStore" style="padding: 0">
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.key == 'action'">
              <template v-if="record.isInner == 1">--</template>
              <template v-else>
                <span class="common_page_edit" @click="handleClick(record, 'edit')">编辑</span>
                <Popconfirm
                  title="删除后不可恢复，是否确定删除？"
                  @confirm="handleClick({ reasonId: record.reasonId }, 'del')"
                >
                  <span class="common_page_edit">删除</span>
                </Popconfirm>
              </template>
            </template>
            <template v-else-if="column.key == 'isShow'">
              <template v-if="record.isInner == 1">--</template>
              <template v-else>
                <Switch
                  :checked="text == 1 ? true : false"
                  @change="
                    (checked) =>
                      handleClick({ reasonId: record.reasonId, isShow: checked ? 1 : 0 }, 'switch')
                  "
                />
              </template>
            </template>
            <template v-else-if="column.key">
              {{ text || text === 0 ? text : '--' }}
            </template>
          </template>
        </BasicTable>
      </template>
      <template v-else-if="tabIndex == 4">
        <BasicTable @register="standardTableUcancle" style="padding: 0">
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.key == 'action'">
              <template v-if="record.isInner == 1">--</template>
              <template v-else>
                <span class="common_page_edit" @click="handleClick(record, 'edit')">编辑</span>
                <Popconfirm
                  title="删除后不可恢复，是否确定删除？"
                  @confirm="handleClick({ reasonId: record.reasonId }, 'del')"
                >
                  <span class="common_page_edit">删除</span>
                </Popconfirm>
              </template>
            </template>
            <template v-else-if="column.key == 'isShow'">
              <template v-if="record.isInner == 1">--</template>
              <template v-else>
                <Switch
                  :checked="text == 1 ? true : false"
                  @change="
                    (checked) =>
                      handleClick({ reasonId: record.reasonId, isShow: checked ? 1 : 0 }, 'switch')
                  "
                />
              </template>
            </template>
            <template v-else-if="column.key">
              {{ text || text === 0 ? text : '--' }}
            </template>
          </template>
        </BasicTable>
      </template>
      <template v-else-if="tabIndex == 5">
        <BasicTable @register="standardTableUpay" style="padding: 0">
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.key == 'action'">
              <template v-if="record.isInner == 1">--</template>
              <template v-else>
                <span class="common_page_edit" @click="handleClick(record, 'edit')">编辑</span>
                <Popconfirm
                  title="删除后不可恢复，是否确定删除？"
                  @confirm="handleClick({ reasonId: record.reasonId }, 'del')"
                >
                  <span class="common_page_edit">删除</span>
                </Popconfirm>
              </template>
            </template>
            <template v-else-if="column.key == 'isShow'">
              <template v-if="record.isInner == 1">--</template>
              <template v-else>
                <Switch
                  :checked="text == 1 ? true : false"
                  @change="
                    (checked) =>
                      handleClick({ reasonId: record.reasonId, isShow: checked ? 1 : 0 }, 'switch')
                  "
                />
              </template>
            </template>
            <template v-else-if="column.key">
              {{ text || text === 0 ? text : '--' }}
            </template>
          </template>
        </BasicTable>
      </template>
      <template v-else-if="tabIndex == 6">
        <BasicTable @register="standardTableUgoods" style="padding: 0">
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.key == 'action'">
              <template v-if="record.isInner == 1">--</template>
              <template v-else>
                <span class="common_page_edit" @click="handleClick(record, 'edit')">编辑</span>
                <Popconfirm
                  title="删除后不可恢复，是否确定删除？"
                  @confirm="handleClick({ reasonId: record.reasonId }, 'del')"
                >
                  <span class="common_page_edit">删除</span>
                </Popconfirm>
              </template>
            </template>
            <template v-else-if="column.key == 'isShow'">
              <template v-if="record.isInner == 1">--</template>
              <template v-else>
                <Switch
                  :checked="text == 1 ? true : false"
                  @change="
                    (checked) =>
                      handleClick({ reasonId: record.reasonId, isShow: checked ? 1 : 0 }, 'switch')
                  "
                />
              </template>
            </template>
            <template v-else-if="column.key">
              {{ text || text === 0 ? text : '--' }}
            </template>
          </template>
        </BasicTable>
      </template>
      <template v-else-if="tabIndex == 7">
        <BasicTable @register="standardTablePayGoods" style="padding: 0">
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.key == 'action'">
              <template v-if="record.isInner == 1">--</template>
              <template v-else>
                <span class="common_page_edit" @click="handleClick(record, 'edit')">编辑</span>
                <Popconfirm
                  title="删除后不可恢复，是否确定删除？"
                  @confirm="handleClick({ reasonId: record.reasonId }, 'del')"
                >
                  <span class="common_page_edit">删除</span>
                </Popconfirm>
              </template>
            </template>
            <template v-else-if="column.key == 'isShow'">
              <template v-if="record.isInner == 1">--</template>
              <template v-else>
                <Switch
                  :checked="text == 1 ? true : false"
                  @change="
                    (checked) =>
                      handleClick({ reasonId: record.reasonId, isShow: checked ? 1 : 0 }, 'switch')
                  "
                />
              </template>
            </template>
            <template v-else-if="column.key">
              {{ text || text === 0 ? text : '--' }}
            </template>
          </template>
        </BasicTable>
      </template>
      <template v-else>
        <BasicTable @register="standardTableScancle" style="padding: 0">
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.key == 'action'">
              <template v-if="record.isInner == 1">--</template>
              <template v-else>
                <span class="common_page_edit" @click="handleClick(record, 'edit')">编辑</span>
                <Popconfirm
                  title="删除后不可恢复，是否确定删除？"
                  @confirm="handleClick({ reasonId: record.reasonId }, 'del')"
                >
                  <span class="common_page_edit">删除</span>
                </Popconfirm>
              </template>
            </template>
            <template v-else-if="column.key == 'isShow'">
              <template v-if="record.isInner == 1">--</template>
              <template v-else>
                <Switch
                  :checked="text == 1 ? true : false"
                  @change="
                    (checked) =>
                      handleClick({ reasonId: record.reasonId, isShow: checked ? 1 : 0 }, 'switch')
                  "
                />
              </template>
            </template>
            <template v-else-if="column.key">
              {{ text || text === 0 ? text : '--' }}
            </template>
          </template>
        </BasicTable>
      </template>
      <SldModal
        :width="width"
        :title="title"
        :visible="visible"
        :content="content"
        :confirmBtnLoading="confirmBtnLoading"
        :showFoot="true"
        @cancle-event="handleCancle"
        @confirm-event="handleConfirm"
      />
    </div>
  </div>
</template>
<script lang="ts">
  import { defineComponent, ref, reactive } from 'vue';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { Tabs, Popconfirm, Switch } from 'ant-design-vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import {
    getReasonList,
    addReason,
    switchReason,
    delReason,
    updateReason,
  } from '/@/api/manage/manage';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import SldModal from '@/components/SldModal/index.vue';
  import { failTip, sucTip } from '/@/utils/utils';

  export default defineComponent({
    components: {
      SldComHeader,
      [Tabs.name]: Tabs,
      [Tabs.TabPane.name]: Tabs.TabPane,
      BasicForm,
      BasicTable,
      Popconfirm,
      Switch,
      SldModal,
    },
    setup() {
      const clickEvent = ref(false);
      const tabIndex = ref(1);
      const tipData = ref([
        ['用于平台处理商品违规下架时选择下架理由，最多20条。'],
        ['用于平台审核商品时选择拒绝理由，最多20条。'],
        ['用于平台审核入驻店铺时选择拒绝理由，最多20条。'],
        ['会员待付款状态下取消订单可选择的原因，最多20条。'],
        ['会员在未收货时申请仅退款可选择的原因，最多20条。'],
        ['会员在已收货时申请仅退款可选择的原因，最多20条。'],
        ['会员在申请退货退款时可选择的原因，最多20条。'],
        ['商户取消会员订单时可选择的原因，最多20条。'],
      ]);

      function handleChange(e) {
        searchValue = {};
        resetFields();
        tabIndex.value = e;
        updateSchema({
          field: 'content',
          component: 'Input',
          colProps: { span: 6, style: 'width:240px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 240,
            maxlength: 6,
            placeholder:
              e == 1
                ? '请输入违规下架原因'
                : e == 2 || e == 3
                ? '请输入拒绝原因'
                : e == 4 || e == 8
                ? '请输入取消原因'
                : e == 5 || e == 6
                ? '请输入退款原因'
                : e == 7
                ? '请输入退货原因'
                : '请输入原因',
            size: 'default',
          },
          label: '',
          labelWidth: 0,
        });
      }

      let searchValue = reactive({});
      const schemas: FormSchema[] = [
        {
          field: 'content',
          component: 'Input',
          colProps: { span: 6, style: 'width:240px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 240,
            maxlength: 6,
            placeholder: '请输入违规下架原因',
            size: 'default',
          },
          label: '',
          labelWidth: 0,
        },
      ];
      const [register, { resetFields, updateSchema }] = useForm({
        labelWidth: 120,
        schemas: schemas,
        actionColOptions: { span: 24 },
      });

      const fetchSetting = {
        pageField: 'current',
        sizeField: 'pageSize',
        listField: 'data.list',
        totalField: 'data.pagination.total',
      };
      const columns = [
        {
          title: '申请原因',
          dataIndex: 'content',
          width: 150,
        },
        {
          title: '排序',
          dataIndex: 'sort',
          width: 100,
        },
        {
          title: '是否显示',
          dataIndex: 'isShow',
          width: 100,
        },
      ];
      const actionColumn = {
        width: 100,
        title: '操作',
        dataIndex: 'action',
      };
      //违规下架
      const [standardTableLockoff, { reload: reloadLockoff }] = useTable({
        api: (rag) => getReasonList({ ...rag, type: 101, pageSize: 10000, ...searchValue }),
        fetchSetting: fetchSetting,
        columns: columns,
        actionColumn: actionColumn,
        bordered: true,
        striped: false,
        pagination: false,
      });
      //商品审核拒绝
      const [standardTableGoods, { reload: reloadGoods }] = useTable({
        api: (rag) => getReasonList({ ...rag, type: 102, pageSize: 10000, ...searchValue }),
        fetchSetting: fetchSetting,
        columns: columns,
        actionColumn: actionColumn,
        bordered: true,
        striped: false,
        pagination: false,
      });
      //入驻审核拒绝
      const [standardTableStore, { reload: reloadStore }] = useTable({
        api: (rag) => getReasonList({ ...rag, type: 103, pageSize: 10000, ...searchValue }),
        fetchSetting: fetchSetting,
        columns: columns,
        actionColumn: actionColumn,
        bordered: true,
        striped: false,
        pagination: false,
      });
      //会员取消原因
      const [standardTableUcancle, { reload: reloadUcancle }] = useTable({
        api: (rag) => getReasonList({ ...rag, type: 104, pageSize: 10000, ...searchValue }),
        fetchSetting: fetchSetting,
        columns: columns,
        actionColumn: actionColumn,
        bordered: true,
        striped: false,
        pagination: false,
      });
      //仅退款-未收货
      const [standardTableUpay, { reload: reloadUpay }] = useTable({
        api: (rag) => getReasonList({ ...rag, type: 105, pageSize: 10000, ...searchValue }),
        fetchSetting: fetchSetting,
        columns: columns,
        actionColumn: actionColumn,
        bordered: true,
        striped: false,
        pagination: false,
      });
      //仅退款-已收货
      const [standardTableUgoods, { reload: reloadUgoods }] = useTable({
        api: (rag) => getReasonList({ ...rag, type: 106, pageSize: 10000, ...searchValue }),
        fetchSetting: fetchSetting,
        columns: columns,
        actionColumn: actionColumn,
        bordered: true,
        striped: false,
        pagination: false,
      });
      //退款退货原因
      const [standardTablePayGoods, { reload: reloadPayGoods }] = useTable({
        api: (rag) => getReasonList({ ...rag, type: 107, pageSize: 10000, ...searchValue }),
        fetchSetting: fetchSetting,
        columns: columns,
        actionColumn: actionColumn,
        bordered: true,
        striped: false,
        pagination: false,
      });
      //商户取消订单
      const [standardTableScancle, { reload: reloadScancle }] = useTable({
        api: (rag) => getReasonList({ ...rag, type: 108, pageSize: 10000, ...searchValue }),
        fetchSetting: fetchSetting,
        columns: columns,
        actionColumn: actionColumn,
        bordered: true,
        striped: false,
        pagination: false,
      });

      //筛选搜索
      function handleSubmit(val) {
        for (let key in val) {
          if (val[key] === undefined || val[key] === null) {
            delete val[key];
          }
        }
        searchValue = val;
        if (tabIndex.value == 1) {
          reloadLockoff();
        } else if (tabIndex.value == 2) {
          reloadGoods();
        } else if (tabIndex.value == 3) {
          reloadStore();
        } else if (tabIndex.value == 4) {
          reloadUcancle();
        } else if (tabIndex.value == 5) {
          reloadUpay();
        } else if (tabIndex.value == 6) {
          reloadUgoods();
        } else if (tabIndex.value == 7) {
          reloadPayGoods();
        } else if (tabIndex.value == 8) {
          reloadScancle();
        }
      }

      //筛选重置
      function handleReset() {
        searchValue = {};
        if (tabIndex.value == 1) {
          reloadLockoff();
        } else if (tabIndex.value == 2) {
          reloadGoods();
        } else if (tabIndex.value == 3) {
          reloadStore();
        } else if (tabIndex.value == 4) {
          reloadUcancle();
        } else if (tabIndex.value == 5) {
          reloadUpay();
        } else if (tabIndex.value == 6) {
          reloadUgoods();
        } else if (tabIndex.value == 7) {
          reloadPayGoods();
        } else if (tabIndex.value == 8) {
          reloadScancle();
        }
      }

      const handleClick = async (item, type) => {
        if (clickEvent.value) return;
        let res: any = '';
        let params = {};
        operate_type.value = type;
        if (type == 'add') {
          title.value = '新增原因';
          visible.value = true;
          let data = JSON.parse(JSON.stringify(operate_data.value));
          let resaon_label = '';
          switch (tabIndex.value) {
            case 1:
                resaon_label = '违规下架原因';
                break;
            case 2:
                resaon_label = '商品审核拒绝原因';
                break;
            case 3:
                resaon_label = '入驻审核拒绝原因';
                break;
            case 4:
                resaon_label = '会员取消原因';
                break;
            case 5:
                resaon_label = '仅退款-未收货原因';
                break;
            case 6:
                resaon_label = '仅退款-已收货原因';
                break;
            case 7:
                resaon_label = '退款退货原因';
                break;
            case 8:
                resaon_label = '商户取消订单';
                break;
          };
          data[0].label = resaon_label;
          data[0].placeholder = `请输入` + resaon_label;
          data[0].rules = [{ required: true, message: `请输入` + resaon_label }];
          content.value = data;
          return;
        } else if (type == 'edit') {
          title.value = '编辑原因';
          visible.value = true;
          operate_item.value = item;
          let data = JSON.parse(JSON.stringify(operate_data.value));
          data.map((items) => {
            if (items.name == 'content') {
              items.initialValue = item.content;
            } else if (items.name == 'sort') {
              items.initialValue = Number(item.sort);
            } else if (items.name == 'isShow') {
              items.initialValue = item.isShow;
            }
          });
          let resaon_label = '';
          switch (tabIndex.value) {
            case 1:
                resaon_label = '违规下架原因';
                break;
            case 2:
                resaon_label = '商品审核拒绝原因';
                break;
            case 3:
                resaon_label = '入驻审核拒绝原因';
                break;
            case 4:
                resaon_label = '会员取消原因原因';
                break;
            case 5:
                resaon_label = '仅退款-未收货原因';
                break;
            case 6:
                resaon_label = '仅退款-已收货原因';
                break;
            case 7:
                resaon_label = '退款退货原因';
                break;
            case 8:
                resaon_label = '商户取消订单';
                break;
          };
          data[0].label = resaon_label;
          data[0].placeholder = `请输入` + resaon_label;
          data[0].rules = [{ required: true, message: `请输入` + resaon_label }];
          content.value = data;
          return;
        } else if (type == 'switch') {
          clickEvent.value = true;
          params = item;
          res = await switchReason(params);
        } else if (type == 'del') {
          clickEvent.value = true;
          params = item;
          res = await delReason(params);
        } else {
          return;
        }
        clickEvent.value = false;
        if (res.state == 200) {
          sucTip(res.msg);
          if (tabIndex.value == 1) {
            reloadLockoff();
          } else if (tabIndex.value == 2) {
            reloadGoods();
          } else if (tabIndex.value == 3) {
            reloadStore();
          } else if (tabIndex.value == 4) {
            reloadUcancle();
          } else if (tabIndex.value == 5) {
            reloadUpay();
          } else if (tabIndex.value == 6) {
            reloadUgoods();
          } else if (tabIndex.value == 7) {
            reloadPayGoods();
          } else if (tabIndex.value == 8) {
            reloadScancle();
          }
        } else {
          failTip(res.msg);
        }
      };

      const width = ref(550);
      const title = ref('');
      const visible = ref(false);
      const content: any = ref([]);
      const confirmBtnLoading = ref(false);
      const operate_item: any = ref('');
      const operate_type = ref('');
      const operate_data = ref([
        {
          type: 'input',
          label: `违规下架原因`,
          name: 'content',
          placeholder: `请输入违规下架原因`,
          extra: `最多输入10个字`,
          maxLength: 10,
          rules: [
            {
              required: true,
              message: `请输入违规下架原因`,
            },
          ],
        },
        {
          type: 'inputnum',
          label: `排序`,
          name: 'sort',
          placeholder: `请输入排序`,
          extra: '请输入0~255的数字，值越小显示越靠前',
          min: 0,
          max: 255,
          rules: [
            {
              required: true,
              message: `请输入排序`,
            },
          ],
        },
        {
          type: 'switch',
          label: `是否显示`,
          name: 'isShow',
          initialValue: 0,
          unCheckedValue: 0,
          checkedValue: 1,
        },
      ]);

      function handleCancle() {
        visible.value = false;
        confirmBtnLoading.value = false;
      }

      const handleConfirm = async (val) => {
        if (clickEvent.value) return;
        confirmBtnLoading.value = true;
        let res: any = '';
        if (operate_type.value == 'add') {
          if (tabIndex.value == 1) {
            val.type = 101;
          } else if (tabIndex.value == 2) {
            val.type = 102;
          } else if (tabIndex.value == 3) {
            val.type = 103;
          } else if (tabIndex.value == 4) {
            val.type = 104;
          } else if (tabIndex.value == 5) {
            val.type = 105;
          } else if (tabIndex.value == 6) {
            val.type = 106;
          } else if (tabIndex.value == 7) {
            val.type = 107;
          } else if (tabIndex.value == 8) {
            val.type = 108;
          }
          res = await addReason(val);
        } else if (operate_type.value == 'edit') {
          val.reasonId = operate_item.value.reasonId;
          res = await updateReason(val);
        } else {
          return;
        }
        confirmBtnLoading.value = false;
        if (res.state == 200) {
          visible.value = false;
          sucTip(res.msg);
          if (tabIndex.value == 1) {
            reloadLockoff();
          } else if (tabIndex.value == 2) {
            reloadGoods();
          } else if (tabIndex.value == 3) {
            reloadStore();
          } else if (tabIndex.value == 4) {
            reloadUcancle();
          } else if (tabIndex.value == 5) {
            reloadUpay();
          } else if (tabIndex.value == 6) {
            reloadUgoods();
          } else if (tabIndex.value == 7) {
            reloadPayGoods();
          } else if (tabIndex.value == 8) {
            reloadScancle();
          }
        } else {
          failTip(res.msg);
        }
      };

      return {
        clickEvent,
        tabIndex,
        tipData,
        handleChange,

        searchValue,
        schemas,
        register,
        handleSubmit,
        handleReset,

        standardTableLockoff,
        standardTableGoods,
        standardTableStore,
        standardTableUcancle,
        standardTableUpay,
        standardTableUgoods,
        standardTablePayGoods,
        standardTableScancle,
        handleClick,

        width,
        title,
        visible,
        content,
        confirmBtnLoading,
        operate_item,
        operate_type,
        operate_data,
        handleCancle,
        handleConfirm,
      };
    },
  });
</script>
<style lang="less" scoped>
  .salereson_lists {
    .table_search {
      width: 100%;
      height: 40px;
      margin-bottom: 10px;
      padding: 0 10px;
      background: #ffe3d5;

      .table_search_btn {
        height: 28px;
        padding-right: 7px;
        padding-left: 7px;
        border-radius: 3px;
        background: #fff;
        cursor: pointer;

        span {
          margin-left: 4px;
        }
      }

      .table_search_form {
        position: relative;
        top: 4px;
        width: 390px;
        margin-left: 10px;
      }
    }
  }
</style>
