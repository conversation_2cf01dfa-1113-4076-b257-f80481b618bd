<template>
  <div class="section_padding">
    <div class="section_padding_back_add evaluation_lists">
      <SldComHeader :type="1" :title="'评价管理'" />
      <a-tabs v-model:activeKey="tabIndex" type="card" @change="handleChange">
        <a-tab-pane :key="1" tab="商品评价" />
        <a-tab-pane :key="2" tab="店铺评价" />
      </a-tabs>
      <div class="section_padding_tab_top"></div>
      <template v-if="tabIndex == 1">
        <BasicTable @register="goodStandardTable" style="padding: 0">
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.key == 'action'">
              <Popconfirm
                title="删除后不可恢复，是否确定删除？"
                @confirm="handleClick({ commentIds: record.commentId }, 'del')"
              >
                <span class="common_page_edit">删除</span>
              </Popconfirm>
            </template>
            <template v-else-if="column.key == 'score'">
              <div class="flex_row_start_center score_item">
                <span>商品评分：</span>
                <Rate :value="text" disabled />
                <span v-if="record.createTime">{{ record.createTime }}</span>
              </div>
              <div class="flex_row_start_center score_item" v-if="record.content">
                <span>评价内容：</span>
                <span class="text_overflow_hidden" :title="record.content">{{ record.content }}</span>
              </div>
              <div
                class="flex_row_start_center score_item"
                v-if="record.imageValue && record.imageValue.length > 0"
              >
                <span>晒单图片：</span>
                <a-image
                  v-for="(items, indexs) in record.imageValue"
                  :key="indexs"
                  :src="items"
                  :previewMask="false"
                />
              </div>
              <div class="flex_row_start_center score_item" v-if="record.replyContent">
                <span>商家回复：</span>
                <span class="text_overflow_hidden" :title="record.replyContent">{{ record.replyContent }}</span>
              </div>
            </template>
            <template v-else-if="column.key">
              {{ text || text === 0 ? text : '--' }}
            </template>
          </template>
        </BasicTable>
      </template>
      <template v-else-if="tabIndex == 2">
        <BasicTable @register="storeStandardTable" style="padding: 0">
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.key == 'action'">
              <Popconfirm
                title="删除后不可恢复，是否确定删除？"
                @confirm="handleClick({ commentIds: record.commentId }, 'del')"
              >
                <span class="common_page_edit">删除</span>
              </Popconfirm>
            </template>
            <template v-else-if="column.key == 'description'">
              <div class="flex_column_center_center">
                <div class="flex_row_start_center score_item">
                  <span>描述相符：</span>
                  <Rate :value="text" disabled />
                </div>
                <div class="flex_row_start_center score_item">
                  <span>服务态度：</span>
                  <Rate :value="record.serviceAttitude" disabled />
                </div>
                <div class="flex_row_start_center score_item">
                  <span>发货速度：</span>
                  <Rate :value="record.deliverSpeed" disabled />
                </div>
              </div>
            </template>
            <template v-else-if="column.key">
              {{ text || text === 0 ? text : '--' }}
            </template>
          </template>
        </BasicTable>
      </template>
    </div>
  </div>
</template>
<script lang="ts">
  import { defineComponent, ref } from 'vue';
  import { Tabs, Popconfirm, Rate, Image } from 'ant-design-vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import {
    getGoodEvaluation,
    getStoreEvaluation,
    delGoodEvaluation,
    delStoreEvaluation,
  } from '/@/api/manage/manage';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { sucTip, failTip } from '/@/utils/utils';

  export default defineComponent({
    components: {
      SldComHeader,
      [Tabs.name]: Tabs,
      [Tabs.TabPane.name]: Tabs.TabPane,
      BasicTable,
      Popconfirm,
      Rate,
      [Image.name]: Image,
    },
    setup() {
      const clickEvent = ref(false);
      const tabIndex = ref(1);

      function handleChange(e) {
        tabIndex.value = e;
        if (e == 1) {
          goodReload();
        } else if (e == 2) {
          storeReload();
        }
      }

      const fetchSetting = {
        pageField: 'current',
        sizeField: 'pageSize',
        listField: 'data.list',
        totalField: 'data.pagination.total',
      };
      const actionColumn = {
        width: 60,
        title: '操作',
        dataIndex: 'action',
      };
      //商品评价
      const [goodStandardTable, { reload: goodReload }] = useTable({
        api: (rag) => getGoodEvaluation({ ...rag }),
        fetchSetting: fetchSetting,
        columns: [
          {
            title: '商品名称',
            dataIndex: 'goodsName',
            width: 100,
          },
          {
            title: '评价描述',
            dataIndex: 'score',
            width: 180,
          },
          {
            title: '评价人',
            dataIndex: 'memberName',
            width: 80,
          },
          {
            title: '店铺名称',
            dataIndex: 'storeName',
            width: 80,
          },
          {
            title: '评价时间',
            dataIndex: 'createTime',
            width: 90,
          },
        ],
        actionColumn: actionColumn,
        useSearchForm: true,
        formConfig: {
          schemas: [
            {
              field: 'goodsName',
              component: 'Input',
              colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                placeholder: '请输入商品名称',
                size: 'default',
              },
              label: '商品名称',
              labelWidth: 80,
            },
            {
              field: 'memberName',
              component: 'Input',
              colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                placeholder: '请输入评价人',
                size: 'default',
              },
              label: '评价人',
              labelWidth: 80,
            },
            {
              field: '[startTime,endTime]',
              component: 'RangePicker',
              colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
              componentProps: {
                format: 'YYYY-MM-DD',
                placeholder: ['开始时间', '结束时间'],
              },
              label: `评价时间`,
              labelWidth: 80,
            },
          ],
        },
        beforeFetch(values) {
          values.startTime = values.startTime
            ? values.startTime.split(' ')[0] + ' 00:00:00'
            : undefined;
          values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
          return values;
        },
        bordered: true,
        striped: false,
      });
      //店铺评价
      const [storeStandardTable, { reload: storeReload }] = useTable({
        api: (rag) => getStoreEvaluation({ ...rag }),
        fetchSetting: fetchSetting,
        columns: [
          {
            title: '店铺名称',
            dataIndex: 'storeName',
            width: 90,
          },
          {
            title: '订单号',
            dataIndex: 'orderSn',
            width: 90,
          },
          {
            title: '评价人',
            dataIndex: 'memberName',
            width: 90,
          },
          {
            title: '评分',
            dataIndex: 'description',
            width: 110,
          },
          {
            title: '评价时间',
            dataIndex: 'createTime',
            width: 90,
          },
        ],
        actionColumn: actionColumn,
        useSearchForm: true,
        formConfig: {
          schemas: [
            {
              field: 'storeName',
              component: 'Input',
              colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                placeholder: '请输入店铺名称',
                size: 'default',
              },
              label: '店铺名称',
              labelWidth: 80,
            },
            {
              field: 'memberName',
              component: 'Input',
              colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                placeholder: '请输入评价人',
                size: 'default',
              },
              label: '评价人',
              labelWidth: 80,
            },
            {
              field: '[startTime,endTime]',
              component: 'RangePicker',
              colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
              componentProps: {
                format: 'YYYY-MM-DD',
                placeholder: ['开始时间', '结束时间'],
              },
              label: `评价时间`,
              labelWidth: 80,
            },
          ],
        },
        beforeFetch(values) {
          values.startTime = values.startTime
            ? values.startTime.split(' ')[0] + ' 00:00:00'
            : undefined;
          values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
          return values;
        },
        bordered: true,
        striped: false,
      });

      const handleClick = async (item, type) => {
        if (clickEvent.value) return;
        clickEvent.value = true;
        let res: any = '';
        if (type == 'del') {
          if (tabIndex.value == 1) {
            res = await delGoodEvaluation(item);
          } else if (tabIndex.value == 2) {
            res = await delStoreEvaluation(item);
          }
        } else {
          return;
        }
        clickEvent.value = false;
        if (res.state == 200) {
          sucTip(res.msg);
          if (tabIndex.value == 1) {
            goodReload();
          } else if (tabIndex.value == 2) {
            storeReload();
          }
        } else {
          failTip(res.msg);
        }
      };

      return {
        clickEvent,
        tabIndex,
        handleChange,
        goodStandardTable,
        storeStandardTable,
        handleClick,
      };
    },
  });
</script>
<style lang="less">
  .evaluation_lists {
    .common_page_edit {
      margin: 0;
      padding: 0;
    }

    .score_item {
      min-height: 26px;

      img {
        width: 35px;
        height: 35px;
        margin: 3px 6px 3px 0;
        cursor: pointer;
      }

      .ant-image {
        &:last-of-type {
          img {
            margin-right: 0;
          }
        }
      }
    }

    .ant-rate {
      position: relative;
      top: 5px;
      margin-right: 8px;
      font-size: 18px;

      .ant-rate-star:not(:last-child) {
        margin-right: 4px;
      }
    }
  }
</style>
