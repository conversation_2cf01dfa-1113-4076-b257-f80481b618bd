<template>
  <div class="section_padding">
    <div class="section_padding_back_add service_lists">
      <SldComHeader :type="1" :title="'售后管理'" />
      <a-tabs v-model:activeKey="tabIndex" type="card" @change="handleChange">
        <a-tab-pane :key="1" tab="售后列表" />
        <a-tab-pane :key="2" tab="退款审核" />
      </a-tabs>
      <div class="section_padding_tab_top"></div>
      <template v-if="tabIndex == 1">
        <BasicTable @register="serviceStandardTable" style="padding: 0">
          <template #tableTitle>
            <div class="toolbar">
              <div class="toolbar_btn" @click="handleClick(null, 'export')">
                <AliSvgIcon iconName="iconziyuan23" width="15px" height="15px" fillColor="#ff5908" />
                <span>导出</span>
              </div>
            </div>
          </template>
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.key == 'action'">
              <span class="common_page_edit" @click="handleClick(record, 'view')">查看</span>
            </template>
            <template v-else-if="column.key == 'productImage'">
              <div class="service_list_mainIamge">
                <Popover placement="right">
                  <template #content>
                    <div class="service_list_mainIamge_pop">
                      <img :src="text" />
                    </div>
                  </template>
                  <div
                    class="service_list_leftImage"
                    :style="{ backgroundImage: `url('${text}')` }"
                  ></div>
                </Popover>
                <div class="flex_column_between_start service_list_rightInfo">
                  <div class="service_list_rightInfo_name" :title="record.goodsName">{{
                    record.goodsName
                  }}</div>
                  <div class="service_list_rightInfo_sn">订单编号：{{ record.orderSn }}</div>
                  <div class="service_list_rightInfo_sn">退款编号：{{ record.afsSn }}</div>
                </div>
              </div>
            </template>
            <template v-else-if="column.key">
              {{ text || text === 0 ? text : '--' }}
            </template>
          </template>
        </BasicTable>
      </template>
      <template v-else-if="tabIndex == 2">
        <BasicTable @register="auditStandardTable" style="padding: 0">
          <template #tableTitle>
            <div class="toolbar">
              <div class="toolbar_btn" @click="handleClick(null, 'export')">
                <AliSvgIcon iconName="iconziyuan23" width="15px" height="15px" fillColor="#ff5908" />
                <span>导出</span>
              </div>
              <div class="toolbar_btn" @click="handleClick(null, 'submit')">
                <AliSvgIcon
                  iconName="iconpiliangxiajia"
                  width="15px"
                  height="15px"
                  fillColor="#fa6f1e"
                />
                <span>批量确认</span>
              </div>
            </div>
          </template>
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.key == 'action'">
              <span class="common_page_edit" @click="handleClick(record, 'view')">查看</span>
              <span class="common_page_edit" @click="handleClick(record, 'submit')">确认</span>
            </template>
            <template v-else-if="column.key == 'productImage'">
              <div class="service_list_mainIamge">
                <Popover placement="right">
                  <template #content>
                    <div class="service_list_mainIamge_pop">
                      <img :src="text" />
                    </div>
                  </template>
                  <div
                    class="service_list_leftImage"
                    :style="{ backgroundImage: `url('${text}')` }"
                  ></div>
                </Popover>
                <div class="flex_column_between_start service_list_rightInfo">
                  <div class="service_list_rightInfo_name" :title="record.goodsName">{{
                    record.goodsName
                  }}</div>
                  <div class="service_list_rightInfo_sn">订单编号：{{ record.orderSn }}</div>
                  <div class="service_list_rightInfo_sn">退款编号：{{ record.afsSn }}</div>
                </div>
              </div>
            </template>
            <template v-else-if="column.key">
              {{ text || text === 0 ? text : '--' }}
            </template>
          </template>
        </BasicTable>
      </template>
      <SldModal
        :width="width"
        :title="title"
        :visible="visible"
        :content="content"
        :confirmBtnLoading="confirmBtnLoading"
        :showFoot="showFootg"
        @cancle-event="handleCancle"
        @confirm-event="handleConfirm"
      />
    </div>
  </div>
</template>
<script lang="ts">
  import { defineComponent, ref, onMounted } from 'vue';
  import { Tabs, Popover, Image } from 'ant-design-vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import { FormSchema } from '/@/components/Form/index';
  import {
    getServiceList,
    getServiceDetail,
    exportService,
    confirmService,
  } from '/@/api/manage/manage';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import SldModal from '@/components/SldModal/index.vue';
  import { useRoute } from 'vue-router';
  import { failTip, sucTip } from '/@/utils/utils';

  export default defineComponent({
    components: {
      SldComHeader,
      [Tabs.name]: Tabs,
      [Tabs.TabPane.name]: Tabs.TabPane,
      BasicTable,
      Popover,
      [Image.name]: Image,
      SldModal,
    },
    setup() {
      const route = useRoute();
      const clickEvent = ref(false);
      const tabIndex = ref(1);
      const tabFlag = ref([1, 0]);

      function handleChange(e) {
        if (!tabFlag.value[e - 1]) {
          tabFlag.value[e - 1] = 1;
        } else {
          if (e == 1) {
            serviceReload();
          } else if (e == 2) {
            auditReload();
          }
        }
        tabIndex.value = e;
      }

      const rowKey = ref('afsSn');
      const checkedKeys: any = ref([]);
      const fetchSetting = {
        pageField: 'current',
        sizeField: 'pageSize',
        listField: 'data.list',
        totalField: 'data.pagination.total',
      };
      const actionColumn = {
        width: 60,
        title: '操作',
        dataIndex: 'action',
      };
      const tableSchemas: FormSchema[] = [
        {
          field: 'orderSn',
          component: 'Input',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            placeholder: '请输入订单号',
            size: 'default',
          },
          label: '订单号',
          labelWidth: 80,
        },
        {
          field: 'afsSn',
          component: 'Input',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            placeholder: '请输入退款编号',
            size: 'default',
          },
          label: '退款编号',
          labelWidth: 80,
        },
        {
          field: 'memberName',
          component: 'Input',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            placeholder: '请输入会员名',
            size: 'default',
          },
          label: '会员名',
          labelWidth: 80,
        },
        {
          field: 'storeName',
          component: 'Input',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            placeholder: '请输入店铺名称',
            size: 'default',
          },
          label: '店铺名称',
          labelWidth: 80,
        },
        {
          component: 'Select',
          field: 'returnType',
          colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
          componentProps: {
            placeholder: `请选择退款方式`,
            options: [
              { key: '', label:  '全部' },
              { value: '1', label:  '仅退款' },
              { value: '2', label: '退货退款' },
            ],
          },
          label: `退款方式`,
          labelWidth: 80,
        },
        {
          field: '[startTime, endTime]',
          component: 'RangePicker',
          colProps: { span: 6, style: 'width:290px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 220,
            format: 'YYYY-MM-DD',
            placeholder: ['开始时间', '结束时间'],
            size: 'default',
          },
          label: '申请时间',
          labelWidth: 70,
        },
        {
          component: 'Select',
          field: 'state',
          colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
          componentProps: {
            placeholder: `请选择退款状态`,
            options: [
              { key: '', label:  '全部' },
              { value: '100', label:  '待商家审核' },
              { value: '201', label: '待买家发货' },
              { value: '102', label:  '待商家收货' },
              { value: '202', label: '售后关闭' },
              { value: '203', label:  '待平台处理' },
              { value: '300', label: '退款成功' },
            ],
          },
          label: `退款状态`,
          labelWidth: 80,
        },
      ];
      //售后列表
      const [serviceStandardTable, { reload: serviceReload, getForm: getServiceForm, getDataSource ,getPaginationRef:getServicePaginationRef}] = useTable({
        rowKey: rowKey,
        api: (rag) => getServiceList({ ...rag }),
        fetchSetting: fetchSetting,
        columns: [
          {
            title: '商品信息',
            dataIndex: 'productImage',
            width: 180,
          },
          {
            title: '退款金额(元)',
            dataIndex: 'returnMoneyAmount',
            width: 80,
          },
          {
            title: '店铺名称',
            dataIndex: 'storeName',
            width: 80,
          },
          {
            title: '会员名',
            dataIndex: 'memberName',
            width: 80,
          },
          {
            title: '退款方式',
            dataIndex: 'returnTypeValue',
            width: 80,
          },
          {
            title: '售后状态',
            dataIndex: 'stateValue',
            width: 80,
          },
          {
            title: '申请时间',
            dataIndex: 'applyTime',
            width: 150,
          },
        ],
        actionColumn: actionColumn,
        useSearchForm: true,
        formConfig: {
          schemas: tableSchemas,
        },
        beforeFetch(values) {
          values.startTime = values.startTime
            ? values.startTime.split(' ')[0] + ' 00:00:00'
            : undefined;
          values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
          return values;
        },
        bordered: true,
        striped: false,
        showIndexColumn: true,
      });
      //退款审核
      const [auditStandardTable, { reload: auditReload, getForm: getAuditForm, getDataSource: getAuditDataSource,getPaginationRef:getAuditPaginationRef }] = useTable({
        rowKey: rowKey,
        api: (rag) => getServiceList({ ...rag, state: 203, type: 'audit' }),
        fetchSetting: fetchSetting,
        columns: [
          {
            title: '商品信息',
            dataIndex: 'productImage',
            width: 180,
          },
          {
            title: '退款金额(元)',
            dataIndex: 'returnMoneyAmount',
            width: 80,
          },
          {
            title: '店铺名称',
            dataIndex: 'storeName',
            width: 80,
          },
          {
            title: '会员名',
            dataIndex: 'memberName',
            width: 80,
          },
          {
            title: '审核状态',
            dataIndex: 'stateValue',
            width: 80,
          },
          {
            title: '申请时间',
            dataIndex: 'applyTime',
            width: 80,
          },
        ],
        actionColumn: actionColumn,
        useSearchForm: true,
        formConfig: {
          schemas: tableSchemas.filter(item => item.field != 'returnType' && item.field != 'state'),
        },
        beforeFetch(values) {
          values.startTime = values.startTime
            ? values.startTime.split(' ')[0] + ' 00:00:00'
            : undefined;
          values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
          return values;
        },
        bordered: true,
        striped: false,
        showIndexColumn: true,
        rowSelection: {
          type: 'checkbox',
          selectedRowKeys: checkedKeys,
          onSelect: onSelect,
          onSelectAll: onSelectAll,
        },
        clickToRowSelect: false,
      });

      onMounted(() => {
        if (route.query.tab != undefined && route.query.tab != null) {
          tabIndex.value = Number(route.query.tab);
          if (tabIndex.value == 1) {
            if (route.query.orderSn != undefined && route.query.orderSn != null) {
              getServiceForm().setFieldsValue({
                orderSn: route.query.orderSn,
              });
            } else {
              serviceReload();
            }
          } else {
            if (route.query.orderSn != undefined && route.query.orderSn != null) {
              getAuditForm().setFieldsValue({
                orderSn: route.query.orderSn,
              });
            } else {
              auditReload();
            }
          }
        }
      });

      const handleClick = async (item, type) => {
        if (clickEvent.value) return;
        if (type == 'export') {
          if (tabIndex.value == 1) {
            if (getDataSource().length == 0) {
              failTip('暂无数据可导出');
              return;
            }
            let paramData = getServiceForm().getFieldsValue()
            paramData.pageSize = getServicePaginationRef().pageSize
            if(Number(getServicePaginationRef().current)>1){
              paramData.current = getServicePaginationRef().current
            }
            for(let i in paramData){
              if(paramData[i]==undefined){
                delete paramData[i]
              }
            }
            paramData.fileName = '售后数据导出'
            await exportService(paramData);
          } else if (tabIndex.value == 2) {
            if (getAuditDataSource().length == 0) {
              failTip('暂无数据可导出');
              return;
            }
            let paramData = getAuditForm().getFieldsValue()
            paramData.pageSize = getAuditPaginationRef().pageSize
            if(Number(getAuditPaginationRef().current)>1){
              paramData.current = getAuditPaginationRef().current
            }
            for(let i in paramData){
              if(paramData[i]==undefined){
                delete paramData[i]
              }
            }
            paramData.fileName = '售后数据导出'
            paramData.state = 203
            paramData.type = 'audit'
            await exportService(paramData);
          }
        } else if (type == 'view') {
          clickEvent.value = true;
          showFootg.value = false;
          const res: any = await getServiceDetail({ afsSn: item.afsSn });
          clickEvent.value = false;
          if (res.state == 200) {
            title.value = item.returnType == 1 ? '退款详情' : '退款退货详情';
            visible.value = true;
            let data = JSON.parse(JSON.stringify(operate_detail_data.value));
            data.map((items) => {
              if (items.name == 'applyImageList' && res.data[items.name].length == 0) {
                items.type = 'show_test';
                res.data[items.name] = '';
              } else if (items.name == 'returnLogList') {
                res.data[items.name].map((itemss) => {
                  itemss.content = itemss.createTime + ' ' + itemss.content;
                });
              }
              items.initialValue = res.data[items.name];
            });
            content.value = data;
          } else {
            failTip(res.msg);
          }
        } else if (type == 'submit') {
          if (item === null && checkedKeys.value.length == 0) {
            failTip('请先选中数据');
            return;
          }
          operate_item.value = item;
          showFootg.value = true;
          title.value = '确认退款';
          content.value = JSON.parse(JSON.stringify(operate_submit_data.value));
          visible.value = true;
        }
      };

      //表格的单行选中回调事件
      function onSelect(record, selected) {
        if (selected) {
          checkedKeys.value = [...checkedKeys.value, record[rowKey.value]];
        } else {
          checkedKeys.value = checkedKeys.value.filter((item) => item != record[rowKey.value]);
        }
      }

      //表格的全选回调事件
      function onSelectAll(selected, selectedRows, changeRows) {
        const changeIds = changeRows.map((item) => item[rowKey.value]);
        if (selected) {
          checkedKeys.value = [...checkedKeys.value, ...changeIds];
        } else {
          checkedKeys.value = checkedKeys.value.filter((item) => {
            return !changeIds.includes(item);
          });
        }
      }

      const width = ref(550);
      const title = ref('');
      const visible = ref(false);
      const content = ref([]);
      const confirmBtnLoading = ref(false);
      const operate_item: any = ref('');
      const showFootg = ref(true);
      const operate_data: any = ref([]);
      const operate_detail_data = ref([
        {
          type: 'show_test',
          label: '退款编号',
          name: 'afsSn',
          initialValue: '',
        },
        {
          type: 'show_test',
          label: '订单号',
          name: 'orderSn',
          initialValue: '',
        },
        {
          type: 'show_test',
          label: '退款状态',
          name: 'stateValue',
          initialValue: '',
        },
        {
          type: 'show_test',
          label: '申请商品',
          name: 'goodsName',
          initialValue: '',
        },
        {
          type: 'show_test',
          label: '退款方式',
          name: 'returnTypeValue',
          initialValue: '',
        },
        {
          type: 'show_test',
          label: '退款金额(元)',
          name: 'returnMoneyAmount',
          initialValue: '',
        },
        {
          type: 'show_test',
          label: '退款数量',
          name: 'returnNum',
          initialValue: '',
        },
        {
          type: 'show_test',
          label: '会员名',
          name: 'memberName',
          initialValue: '',
        },
        {
          type: 'show_test',
          label: '退款原因',
          name: 'applyReasonContent',
          initialValue: '',
        },
        {
          type: 'show_imgs',
          label: '退款凭证',
          name: 'applyImageList',
          initialValue: [],
        },
        {
          type: 'show_test',
          label: '退款明细',
          name: 'returnLogList',
          diy_key: 'content',
          initialValue: [],
        },
      ]);
      const operate_submit_data = ref([
        {
          type: 'textarea',
          label: `备注信息`,
          name: 'remark',
          placeholder: `请输入备注信息,最多100个字`,
          maxLength: 100,
        },
      ]);

      function handleCancle() {
        visible.value = false;
      }

      const handleConfirm = async (val) => {
        if (clickEvent.value) return;
        clickEvent.value = true;
        val.afsSns =
          operate_item.value === null ? checkedKeys.value.join(',') : operate_item.value.afsSn;
        const res: any = await confirmService(val);
        clickEvent.value = false;
        if (res.state == 200) {
          sucTip(res.msg);
          visible.value = false;
          checkedKeys.value = []
          serviceReload();
          auditReload();
        } else {
          failTip(res.msg);
        }
      };

      return {
        clickEvent,
        tabIndex,
        tabFlag,
        handleChange,
        rowKey,
        checkedKeys,
        serviceStandardTable,
        auditStandardTable,
        handleClick,
        onSelect,
        onSelectAll,
        width,
        title,
        visible,
        content,
        confirmBtnLoading,
        operate_item,
        showFootg,
        operate_data,
        operate_detail_data,
        operate_submit_data,
        handleCancle,
        handleConfirm,
      };
    },
  });
</script>
<style lang="less" scoped></style>
