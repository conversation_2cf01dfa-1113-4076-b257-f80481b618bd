<template>
  <div class="section_padding order_detail">
    <div class="section_padding_back">
      <SldComHeader title="订单详情" back />
      <div style="width: 100%; height: 10px"></div>
      <Spin :spinning="loading">
        <div class="height_detail">
          <div class="flex_row_center_start progress">
            <div
              class="flex_column_start_center item"
              v-for="(item, index) in return_progress_data"
              :key="index"
            >
              <div class="top flex_row_center_center">
                <span class="left_line" :style="{ borderColor: item.line_color }"></span>
                <img :src="item.icon" alt="" />
                <span class="right_line" :style="{ borderColor: item.line_color }"></span>
              </div>
              <span class="state" :style="{ color: item.state_color }">{{ item.state }}</span>
              <span class="time" :style="{ color: item.time_color }">{{ item.time }}</span>
            </div>
          </div>
          <div v-if="order_detail.orderState == 0" class="state_part flex_column_start_center">
            <span class="title">{{ order_detail.orderStateValue }}</span>
            <span class="tip">取消原因:{{order_detail.refuseReason +(order_detail.refuseRemark ? ',' + order_detail.refuseRemark : '')}}</span>
          </div>
          <div
            v-else-if="order_detail.orderState == 10"
            class="state_part flex_column_start_center"
          >
            <span class="title">{{ order_detail.orderStateValue }}</span>
            <span v-if="order_detail.pickupTime" class="time">送达时间：{{ order_detail.pickupTime }}</span>
          </div>
          <div
            v-else-if="
              (order_detail.orderState == 20 || order_detail.orderState == 31) &&
              order_detail.lockState == 0
            "
            class="state_part flex_column_start_center"
          >
            <span class="title">{{ order_detail.orderStateValue }}</span>
            
            <span v-if="order_detail.pickupTime" class="time">送达时间：{{ order_detail.pickupTime }}</span>
            
          </div>
          <div
            v-else-if="order_detail.orderState == 20"
            class="state_part flex_column_start_center"
          >
            <span class="title">{{ order_detail.orderStateValue }}</span>
            
            <span v-if="order_detail.pickupTime" class="time">送达时间：{{ order_detail.pickupTime }}</span>
            
          </div>
          <div
            v-else-if="order_detail.orderState == 30"
            class="state_part flex_column_start_center"
          >
            
            
            <span class="title"  v-if="order_detail.deliverMethod!=2&&order_detail.deliverMethod!=3">商品已发出,等待买家收货</span>
            
            <span v-if="order_detail.pickupTime" class="time">送达时间：{{ order_detail.pickupTime }}</span>
            
          </div>
          <div
            v-else-if="order_detail.orderState == 40"
            class="state_part flex_column_start_center"
          >
            <span class="title" v-if="order_detail.deliverMethod!=3">买家已确认收货,订单完成</span>
            
            <span v-if="order_detail.pickupTime" class="time">送达时间：{{ order_detail.pickupTime }}</span>
          </div>
          <div
            v-else-if="order_detail.orderState == 50"
            class="state_part flex_column_start_center"
          >
            <span class="title">{{ order_detail.orderStateValue }}</span>
            <span v-if="order_detail.pickupTime" class="time">送达时间：{{ order_detail.pickupTime }}</span>
          </div>

          <!-- 订单信息 start -->
          <div class="sld_common_title">订单信息:</div>
          <Description
            size="middle"
            :useCollapse="true"
            :bordered="true"
            :column="2"
            :data="order_detail"
            :schema="order_info"
          />
          <!-- 订单信息 end -->
          <!-- 收货人信息 start -->
          <template v-if="order_detail.isVirtualGoods == 1&&order_detail.deliverMethod!=2">
            <div class="sld_common_title">收货人信息:</div>
            <Description
              size="middle"
              :useCollapse="true"
              :bordered="true"
              :column="2"
              :data="order_detail"
              :schema="receiver_info"
            />
          </template>
          <!-- 收货人信息 end -->
          
          <!-- 用户预留信息 start -->
          <template
            v-if="order_detail.isVirtualGoods == 2 && order_detail.orderReserveList.length > 0"
          >
            <div class="sld_common_title">用户预留信息</div>
            <Description
              :useCollapse="true"
              :bordered="true"
              :column="2"
              :data="reserve_detail"
              :schema="reserve_info"
            />
          </template>
          <!-- 用户预留信息 end -->
          <!-- 发票信息 start -->
          <div class="sld_common_title">发票信息:</div>
          <div :style="{ width: invoice_info.length == 1 ? '50%' : '100%' }">
            <Description
              :useCollapse="true"
              :bordered="true"
              :column="2"
              :data="order_detail"
              :schema="invoice_info"
            />
          </div>
          <!-- 发票信息 end -->
          <!-- 更多操作日志 start -->
          <template
            v-if="
             order_detail.orderOperateList != undefined && order_detail.orderOperateList.length != undefined && order_detail.orderOperateList.length > 0 
            "
          >
          <div class="sld_common_title">更多操作日志:</div>
            <BasicTable
              :maxHeight="400"
              :columns="columns_order_log"
              :bordered="true"
              :ellipsis="false"
              :pagination="false"
              :dataSource="order_detail.orderOperateList"
            >
          </BasicTable>
          </template>
          <!-- 更多操作日志 end -->
          <!-- 商品信息 start -->
          <div class="sld_common_title">商品信息</div>
          <BasicTable
            :maxHeight="400"
            :bordered="true"
            :canResize="false"
            :dataSource="order_detail.orderProductList"
            :columns="columns_order_goods"
            :pagination="false"
          >
            <template #bodyCell="{ column, record, text }">
              <template v-if="column.dataIndex == 'productImage'">
                <div class="goods_info com_flex_row_flex_start">
                  <div class="goods_img" style="border: none">
                    <Popover placement="rightTop">
                      <template #content>
                        <div style="width: 200px">
                          <img :src="text" alt="" style="max-width: 100%; max-height: 100%" />
                        </div>
                      </template>
                      <div class="business_load_img">
                        <img :src="text" alt="" />
                      </div>
                    </Popover>
                  </div>
                  <div class="com_flex_column_space_between goods_detail">
                    <div
                      class="goods_name"
                      style="width: 380px; margin-top: 6px; white-space: initial"
                      :title="record.goodsName"
                    >
                      {{ record.goodsName }}
                    </div>
                    <span class="goods_brief" :title="record.specValues">
                      {{ record.specValues }}
                    </span>
                  </div>
                </div>
              </template>
              <template v-else-if="column.dataIndex == 'afsSn'">
                <TableAction
                  v-if="text"
                  style="justify-content: center;"
                  :actions="[
                    { label: '查看售后详情', onClick: goServiceDetail.bind(null, record.orderSn) },
                  ]"
                />
                <span v-else>--</span>
              </template>
            </template>
          </BasicTable>
          <!-- 商品信息 end -->

          <div class="order_detail_total_num">
            <div class="amount_num_detail">
              <div class="amount_num_detail_item">
                <p>商品金额：</p>
                <span>{{`¥${order_detail.goodsAmount?Number(order_detail.goodsAmount).toFixed(2):'0.00'}`}}</span>
              </div>
              <template v-if="order_detail.promotionInfo != undefined && order_detail.promotionInfo.length > 0">
                <template v-for="(item, index) in order_detail.promotionInfo" :key="index">
                  <div class="amount_num_detail_item">
                    <p>
                      <span style="color: #000;">{{item.promotionName}}</span>
                      <template v-if="item.remark">
                        <Tooltip>
                          <template #title>
                            <div class="rule_list">
                              <div>该笔订单参与了：</div>
                              <div>{{item.remark}}</div>
                            </div>
                          </template>
                          <img src="/src/assets/images/pv_icon.png" alt="" style="padding: 0 3px;vertical-align: -4px;">
                        </Tooltip>
                      </template>
                      <span style="color: #000;">：</span>
                    </p>
                    <span>-{{`¥${item.discount?Number(item.discount).toFixed(2):'0.00'}`}}</span>
                  </div>
                </template>
              </template>
              <div class="amount_num_detail_item">
                <p>运费：</p>
                <span>{{`¥${order_detail.expressFee?Number(order_detail.expressFee).toFixed(2):'0.00'}`}}</span>
              </div>
            </div>
            <div class="amount_num_detail_item" style="margin-bottom: 0">
              <p class="amount_num_title">{{order_detail.orderState!=0&&order_detail.orderState!=20?'实付金额':'应付金额'}}：</p>
              <span class="amount_num_price">¥<span>{{`${order_detail.orderAmount?Number(order_detail.orderAmount).toFixed(2):'0.00'}`}}</span></span>
            </div>
          </div>
          <template
            v-if="
              (order_detail.orderState == 31 ||
                order_detail.orderState == 30 ||
                order_detail.orderState == 40) &&
              order_detail.orderDeliverList != undefined &&
              order_detail.orderDeliverList.length > 0
            "
          >
            <div class="sld_common_title">发货信息</div>
            <OrderDeliverInfo :data="order_detail"></OrderDeliverInfo>
          </template>
        </div>
      </Spin>
    
    </div>
  </div>
</template>
<script>
  export default {
    name: 'OrderListToDetail',
  };
</script>
<script setup>
  import { ref, onMounted,nextTick,unref } from 'vue';
  import { Spin, Popover, Modal,Tooltip } from 'ant-design-vue';
  import { useRoute, useRouter } from 'vue-router';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { Description } from '/@/components/Description/index';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { getOrderDetail,
    
   } from '/@/api/manage/manage';
  import { failTip } from '/@/utils/utils';
  import { useUserStore } from '/@/store/modules/user';
  import OrderDeliverInfo from './order_deliver_info.vue';
  import { h } from 'vue';

  const userStore = useUserStore();

  




  const router = useRouter();
  const route = useRoute();
  const loading = ref(true);
  const order_detail = ref({}); //订单详情
  const return_progress_data = ref([]); //退货进度条
  const order_info = ref([
    {
      field: 'orderTypeValue',
      label: '订单类型',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'orderSn',
      label: '订单号',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    
    {
      field: 'storeName',
      label: '店铺名称',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'paymentName',
      label: '支付方式',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'tradeSn',
      label: '支付流水号',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'orderRemark',
      label: '订单备注',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
  ]); //订单信息
  const receiver_info = ref([
    {
      field: 'memberName',
      label: '会员名称',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'receiverName',
      label: '收货人',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'receiverMobile',
      label: '收货人手机号',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'receiverAddress',
      label: '收货地址',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
  ]); //收货人信息
  
  const reserve_info = ref([]); //用户预留信息
  const reserve_detail = ref({});
  const invoice_info = ref([
    {
      field: 'invoiceTitle',
      label: '单位名称',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'taxCode',
      label: '税号',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'receiverEmail',
      label: '收票邮箱',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
  ]); //发票信息
  const invoice_info_other = ref([
    {
      field: 'invoiceStatusValue',
      label: '是否需要开票',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
  ]); //不需要发票的情况
  const invoice_info_personal = ref([
    {
      field: 'invoiceTitle',
      label: '发票抬头',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'receiverEmail',
      label: '收票邮箱',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
  ]); //个人发票
  const invoice_info_VAT = ref([
    {
      field: 'invoiceTitle',
      label: '单位名称',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'taxCode',
      label: '税号',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'registerAddr',
      label: '注册地址',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'registerPhone',
      label: '注册电话',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'bankName',
      label: '开户银行',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'bankAccount',
      label: '银行账户',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'receiverName',
      label: '收票人',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'receiverMobile',
      label: '收票电话',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'receiverAddress',
      label: '收票地址',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
  ]); //公司发票——增值税发票
  const columns_order_log = ref([
  {
      title: '操作方',
      dataIndex: 'logRole',
      align: 'center',
      width: 100,
      customRender: ({ text }) => {
        let con = '';
        if (text == 1) {
          con = '系统管理员';
        } else if (text == 2) {
          con = '商家';
        } else if (text == 2) {
          con = '会员';
        }
        return con;
      },
    },
    {
      title: '操作人',
      dataIndex: 'logUserName',
      align: 'center',
      width: 100,
    },
    {
      title: '操作时间',
      dataIndex: 'logTime',
      align: 'center',
      width: 100,
    },
    {
      title: '操作内容',
      dataIndex: 'logContent',
      align: 'center',
      width: 100,
    },
  ]); //更多操作日志信息
  const columns_order_goods = ref([
    {
      title: `商品信息`,
      dataIndex: 'productImage',
      align: 'center',
      width: 500,
    },
    {
      title: `商品来源`,
      dataIndex: 'supplierName',
      align: 'center',
      width: 100,
    },
    {
      title: `单价(元)`,
      dataIndex: 'productShowPrice',
      align: 'center',
      width: 100,
    },
    {
      title: `数量`,
      dataIndex: 'productNum',
      align: 'center',
      width: 100,
    },
  ]); //商品信息
 

  onMounted(() => {
    get_order_detail({ orderSn: route.query.orderSn });
  });

  //获取订单详情
  const get_order_detail = async (params) => {
    loading.value = true;
    let res = await getOrderDetail(params);
    if (res.state == 200) {
      loading.value = false;

      let data = res.data;
      let orderLogList = res.data.orderLogs;
      let return_data = [];

      //订单信息
      order_info.value.map((item) => {
        if (item.field == 'orderTypeValue') {
          data[item.field] = data[item.field]
            ? `${data[item.field]}${'订单'}${data.isVirtualGoods == 2 ? '、虚拟订单' : ''}`
            : data.isVirtualGoods == 2
            ? '虚拟订单'
            : '普通订单';
        
        }else {
          data[item.field] =
            data[item.field] != undefined && data[item.field] != '' ? data[item.field] : '--';
        }
      });

      //收货人信息
      receiver_info.value.map((item) => {
        if (item.field == 'receiverAddress') {
          data[item.field] = data.receiverAreaInfo + ' ' + data.receiverAddress;
        } else {
          data[item.field] =
            data[item.field] != undefined && data[item.field] != '' ? data[item.field] : '--';
        }
      });
      

      //收票信息
      if (data.invoiceStatus == 1) {
        data.invoiceStatusValue = '是';
        if (data.invoiceInfo.titleType == 1) {
          //个人发票
          invoice_info.value = JSON.parse(JSON.stringify(invoice_info_personal.value));
          data.invoiceTypeCombine = '个人发票';
        } else {
          //公司发票
          if (data.invoiceInfo.invoiceType != 1) {
            //增值税发票
            invoice_info.value = JSON.parse(JSON.stringify(invoice_info_VAT.value));
            data.invoiceTypeCombine = '增值税专用发票';
          } else {
            data.invoiceTypeCombine = '普通发票';
          }
        }
        //需要发票
        invoice_info.value.map((item) => {
          data[item.field] =
            data.invoiceInfo[item.field] != undefined && data.invoiceInfo[item.field] != ''
              ? data.invoiceInfo[item.field]
              : '--';
        });
        data.invoiceContent = data.invoiceInfo.invoiceContent == 1 ? '商品明细' : '商品类别';
        //需要添加发票类型和发票内容
        invoice_info.value = [
          {
            field: 'invoiceTypeCombine',
            label: '发票类型',
            labelMinWidth: 10,
            contentMinWidth: 100,
          },
          {
            field: 'invoiceContent',
            label: '发票内容',
            labelMinWidth: 10,
            contentMinWidth: 100,
          },
          ...invoice_info.value,
        ];
      } else {
        //不需要发票
        data.invoiceStatusValue = '否';
        invoice_info.value = JSON.parse(JSON.stringify(invoice_info_other.value));
      }

      //用户预留信息
      reserve_info.value = [];
      reserve_detail.value = {};
      if (
        data.isVirtualGoods == 2 &&
        data.orderReserveList.length != undefined &&
        data.orderReserveList.length
      ) {
        data.orderReserveList.map((item) => {
          reserve_info.value.push({
            field: item.reserveId,
            label: item.reserveName,
            labelMinWidth: 10,
            contentMinWidth: 100,
          });
          reserve_detail.value[item.reserveName] = item.reserveValue;
        });
      }

      if (data.orderState == 0) {
        // 订单取消
        return_data.push({
          icon: new URL('/@/assets/images/order/submit_pass.png', import.meta.url).href,
          state: '提交订单',
          time:
            orderLogList.length > 0 && orderLogList[0].logTime != undefined
              ? orderLogList[0].logTime
              : '',
          state_color: 'rgba(255, 113, 30, .6)',
          time_color: 'rgba(255, 113, 30, .3)',
          line_color: 'rgba(255, 113, 30, .3)',
        });
        return_data.push({
          icon: new URL('/@/assets/images/order/fail_current.png', import.meta.url).href,
          state: '订单取消',
          time:
            orderLogList.length > 1 && orderLogList[1].logTime != undefined
              ? orderLogList[1].logTime
              : '',
          state_color: 'rgba(255, 113, 30,1)',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: 'rgba(255, 113, 30,1)',
        });
      } else if (data.orderState == 10) {
        //未付款订单
        return_data.push({
          icon: new URL('/@/assets/images/order/submit_current.png', import.meta.url).href,
          state: '提交订单',
          time:
            orderLogList.length > 0 && orderLogList[0].logTime != undefined
              ? orderLogList[0].logTime
              : '',
          state_color: 'rgba(255, 113, 30,1)',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: 'rgba(255, 113, 30,1)',
        });
        return_data.push({
          icon: new URL('/@/assets/images/order/pay_future.png', import.meta.url).href,
          state: '付款成功',
          time: '',
          state_color: '#999999',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: '#eee',
        });
        return_data.push({
          icon: new URL('/@/assets/images/order/deliver_future.png', import.meta.url).href,
          state: data.deliverMethod == 2 ? '等待提货' : '商品发货',
          time: '',
          state_color: '#999999',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: '#eee',
        });
        return_data.push({
          icon: new URL('/@/assets/images/order/suc_future.png', import.meta.url).href,
          state: '订单完成',
          time: '',
          state_color: '#999999',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: '#eee',
        });
      } else if (data.orderState == 20) {
        return_data.push({
          icon: new URL('/@/assets/images/order/submit_pass.png', import.meta.url).href,
          state: '提交订单',
          time:
            orderLogList.length > 0 && orderLogList[0].logTime != undefined
              ? orderLogList[0].logTime
              : '',
          state_color: 'rgba(255, 113, 30, .6)',
          time_color: 'rgba(255, 113, 30, .3)',
          line_color: 'rgba(255, 113, 30, .3)',
        });
        return_data.push({
          icon: new URL('/@/assets/images/order/pay_current.png', import.meta.url).href,
          state: '付款成功',
          time:
            orderLogList.length > 1 && orderLogList[1].logTime != undefined
              ? orderLogList[1].logTime
              : '',
          state_color: 'rgba(255, 113, 30,1)',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: 'rgba(255, 113, 30,1)',
        });
        return_data.push({
          icon: new URL('/@/assets/images/order/deliver_future.png', import.meta.url).href,
          state: data.deliverMethod == 2 ? '等待提货' : '商品发货',
          time: '',
          state_color: '#999999',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: '#eee',
        });
        return_data.push({
          icon: new URL('/@/assets/images/order/suc_future.png', import.meta.url).href,
          state: '订单完成',
          time: '',
          state_color: '#999999',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: '#eee',
        });
      } else if (data.orderState == 30 || data.orderState == 31) {
        return_data.push({
          icon: new URL('/@/assets/images/order/submit_pass.png', import.meta.url).href,
          state: '提交订单',
          time:
            orderLogList.length > 0 && orderLogList[0].logTime != undefined
              ? orderLogList[0].logTime
              : '',
          state_color: 'rgba(255, 113, 30, .6)',
          time_color: 'rgba(255, 113, 30, .3)',
          line_color: 'rgba(255, 113, 30, .3)',
        });
        return_data.push({
          icon: new URL('/@/assets/images/order/pay_pass.png', import.meta.url).href,
          state: '付款成功',
          time:
            orderLogList.length > 1 && orderLogList[1].logTime != undefined
              ? orderLogList[1].logTime
              : '',
          state_color: 'rgba(255, 113, 30, .6)',
          time_color: 'rgba(255, 113, 30, .3)',
          line_color: 'rgba(255, 113, 30, .3)',
        });
        return_data.push({
          icon: new URL('/@/assets/images/order/deliver_current.png', import.meta.url).href,
          state: data.deliverMethod == 2 ? '等待提货' : '商品发货',
          time:
            orderLogList.length > 2 && orderLogList[2].logTime != undefined
              ? orderLogList[2].logTime
              : '',
          state_color: 'rgba(255, 113, 30,1)',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: 'rgba(255, 113, 30,1)',
        });
        return_data.push({
          icon: new URL('/@/assets/images/order/suc_future.png', import.meta.url).href,
          state: '订单完成',
          time: '',
          state_color: 'rgba(51, 51, 51, .5)',
          time_color: 'rgba(51, 51, 51, .5)',
          line_color: '#eee',
        });
      } else if (data.orderState == 40) {
        return_data.push({
          icon: new URL('/@/assets/images/order/submit_pass.png', import.meta.url).href,
          state: '提交订单',
          time:
            orderLogList.length > 0 && orderLogList[0].logTime != undefined
              ? orderLogList[0].logTime
              : '',
          state_color: 'rgba(255, 113, 30, .6)',
          time_color: 'rgba(255, 113, 30, .3)',
          line_color: 'rgba(255, 113, 30, .3)',
        });
        return_data.push({
          icon: new URL('/@/assets/images/order/pay_pass.png', import.meta.url).href,
          state: '付款成功',
          time:
            orderLogList.length > 1 && orderLogList[1].logTime != undefined
              ? orderLogList[1].logTime
              : '',
          state_color: 'rgba(255, 113, 30, .6)',
          time_color: 'rgba(255, 113, 30, .3)',
          line_color: 'rgba(255, 113, 30, .3)',
        });
        return_data.push({
          icon: new URL('/@/assets/images/order/deliver_pass.png', import.meta.url).href,
          state: data.deliverMethod == 2 ? '等待提货' : '商品发货',
          time:
            orderLogList.length > 2 && orderLogList[2].logTime != undefined
              ? orderLogList[2].logTime
              : '',
          state_color: 'rgba(255, 113, 30, .6)',
          time_color: 'rgba(255, 113, 30, .3)',
          line_color: 'rgba(255, 113, 30, .3)',
        });
        return_data.push({
          icon: new URL('/@/assets/images/order/suc_current.png', import.meta.url).href,
          state: '订单完成',
          time:
            orderLogList.length > 3 && orderLogList[3].logTime != undefined
              ? orderLogList[3].logTime
              : '',
          state_color: 'rgba(255, 113, 30,1)',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: 'rgba(255, 113, 30,1)',
        });
      }else if (data.orderState == 50) {
        // 订单取消
        return_data.push({
          icon: new URL('/@/assets/images/order/submit_pass.png', import.meta.url).href,
          state: '提交订单',
          time:
            orderLogList.length > 0 && orderLogList[0].logTime != undefined
              ? orderLogList[0].logTime
              : '',
          state_color: 'rgba(255, 113, 30, .6)',
          time_color: 'rgba(255, 113, 30, .3)',
          line_color: 'rgba(255, 113, 30, .3)',
        });
        return_data.push({
          icon: new URL('/@/assets/images/order/fail_current.png', import.meta.url).href,
          state: '交易关闭',
          time:
            orderLogList.length > 1 && orderLogList[1].logTime != undefined
              ? orderLogList[1].logTime
              : '',
          state_color: 'rgba(255, 113, 30,1)',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: 'rgba(255, 113, 30,1)',
        });
      }

      if (data.orderProductList && data.orderProductList.length > 0) {
        for (var order_index = 0; order_index < data.orderProductList.length; order_index++) {
          if (data.orderProductList[order_index].afsSn) {
            columns_order_goods.value.push({
              title: `操作`,
              dataIndex: 'afsSn',
              align: 'center',
              width: 100,
            });
          }
        }
      }

      order_detail.value = data;
      return_progress_data.value = return_data;
    } else {
      failTip(res.msg);
    }
  };

  //前往售后列表页
  const goServiceDetail = (orderSn) => {
    router.push(`/manage_order/service?tab=1&orderSn=${orderSn}`);
  };

  
  
</script>
<style lang="less">
  @import '@/assets/css/order.less';

  .business_load_img {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    margin: auto;

    img {
      max-width: 100%;
      max-height: 100%;
    }
  }

  .rider_modal {
    padding: 18px 22px;

    .rider_state {
      margin-bottom: 18px;
      color: #333;
      font-size: 14px;
      line-height: 26px;
    }

    .rider_map {
      margin-bottom: 18px;

      .amap-marker-label{
        border: none;
        background-color: transparent;
      }

      .map_content{
        padding: 9px 15px;
        border: none;
        border-radius: 16px;
        background: #fff;
        box-shadow: 0 0 10px 0 rgb(0 0 0 / 25%);
        font-size: 14px;

        .map_content_color{
          color: @primary-color;
        }

        .map_content_xian{
          display: inline-block;
          width: 1px;
          height: 10px;
          margin: 0 6px;
          background: #666;
        }
      }

      .map_content_shop{
        padding: 9px 15px;
        border: none;
        border-radius: 16px;
        background: #fff;
        box-shadow: 0 0 10px 0 rgb(0 0 0 / 25%);

        .map_content_dao{
          color: #333;
          font-size: 30rpx;
          font-weight: bold;
          text-align: center;
        }

        .map_content_song{
          color: #666;
          font-size: 24rpx;
          text-align: center;
        }
        
      }

      .custom-content-marker{
        position: relative;
        width: 40px;
        height: 49px;

        .custom-content-marker_back{
          width: 40px;
          height: 49px;
        }

        .custom-content-marker_img{
          display: flex;
          position: absolute;
          top:2px;
          left: 50%;
          align-items: center;
          justify-content: center;
          width: 36px;
          height: 36px;
          overflow: hidden;
          transform: translateX(-49.5%);
          border-radius: 50%;

          img{
            max-width: 100% !important;
            max-height: 100% !important;
            border-radius: 50%;
          }
        }
      }

      .map_content_dian{
        display: flex;
        position: absolute;
        bottom:-16px;
        left:50%;
        align-items: center;
        justify-content: center;
        width: 16px;
        height: 16px;
        transform: translateX(-50%);
        border: 5px solid #FFF;
        border-radius: 50%;
        background: #04B422;
        box-shadow: 0 0 7px 0 rgb(0 0 0 / 20%);
      }
    }

    .rider_list {
      flex-wrap: wrap;

      .rider_item {
        width: 50%;
        margin-top: 5px;
        margin-bottom: 5px;

        .rider_label {
          width: 30%;
          color: #888;
          font-size: 15px;
          text-align: right;
        }

        .rider_content {
          width: 70%;
          color: #333;
          font-size: 15px;
        }

        .rider_content_time{
          width: 50%;
        }

        .rider_btn {
          margin-left: 15px;
          color: #0E85FF;
          font-size: 15px;
          font-weight: 400;
          cursor: pointer; 
        }
      }
    }
  }
</style>
