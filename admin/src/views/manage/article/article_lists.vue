<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <SldComHeader :type="1" :title="'文章管理'" />
      <BasicTable @register="standardTable" style="padding: 0">
        <template #tableTitle>
          <div class="toolbar">
            <div class="toolbar_btn" @click="handleClick(null, 'add')">
              <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#fff" />
              <span>新增文章</span>
            </div>
            <Popconfirm
              v-if="checkedKeys.length > 0"
              title="删除后不可恢复，是否确定删除？"
              @confirm="handleClick(null, 'dels')"
            >
              <div class="toolbar_btn">
                <AliSvgIcon iconName="iconshanchu" width="15px" height="15px" fillColor="#ff5908" />
                <span>批量删除</span>
              </div>
            </Popconfirm>
            <div v-else class="toolbar_btn" @click="handleClick(null, 'dels')">
              <AliSvgIcon iconName="iconshanchu" width="15px" height="15px" fillColor="#ff5908" />
              <span>批量删除</span>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.key == 'action'">
            <template v-if="record.isSuper == 1"> -- </template>
            <template v-else>
              <span class="common_page_edit" @click="handleClick(record, 'edit')">编辑</span>
              <Popconfirm
                title="删除后不可恢复，是否确定删除？"
                @confirm="handleClick(record, 'del')"
              >
                <span class="common_page_edit">删除</span>
              </Popconfirm>
            </template>
          </template>
          <template v-else-if="column.key">
            {{ text ? text : '--' }}
          </template>
        </template>
      </BasicTable>
    </div>
  </div>
</template>
<script>
  import { defineComponent, ref } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { Popconfirm } from 'ant-design-vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import { getArticleList, delArticle } from '/@/api/manage/manage';
  import { useGo } from '/@/hooks/web/usePage';
  import { sucTip, failTip } from '/@/utils/utils';

  export default defineComponent({
    components: {
      SldComHeader,
      BasicTable,
      Popconfirm,
    },
    setup() {
      const go = useGo();
      const checkedKeys = ref([]); //表格的键值声明，非必填，主要用于勾选功能中的数据处理
      const rowKey = ref('articleId'); //表格开启选择功能时，需要声明选中数据变量
      const operate_type = ref('');
      const click_event = ref(false);

      const [standardTable, { reload }] = useTable({
        api: (arg) => getArticleList({ ...arg }),
        fetchSetting: {
          pageField: 'current',
          sizeField: 'pageSize',
          listField: 'data.list',
          totalField: 'data.pagination.total',
        },
        columns: [
          {
            title: '文章标题',
            dataIndex: 'title',
            width: 100,
          },
          {
            title: '所属分类',
            dataIndex: 'categoryName',
            width: 100,
          },
          {
            title: '是否显示',
            dataIndex: 'stateValue',
            width: 100,
          },
          {
            title: '排序',
            dataIndex: 'sort',
            width: 100,
          },
          {
            title: '创建时间',
            dataIndex: 'createTime',
            width: 100,
          },
        ],
        actionColumn: {
          width: 100,
          title: '操作',
          dataIndex: 'action',
        },
        useSearchForm: true,
        formConfig: {
          schemas: [
            {
              field: 'title',
              component: 'Input',
              colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                maxlength: 6,
                placeholder: '请输入文章标题',
                size: 'default',
              },
              label: '文章标题',
              labelWidth: 80,
            },
            {
              field: 'content',
              component: 'Input',
              colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 300,
                maxlength: 6,
                placeholder: '请输入文章内容',
                size: 'default',
              },
              label: '文章内容',
              labelWidth: 80,
            },
          ],
        },
        bordered: true,
        striped: false,
        showIndexColumn: true,
        rowKey: rowKey,
        rowSelection: {
          type: 'checkbox',
          selectedRowKeys: checkedKeys,
          onSelect: onSelect,
          onSelectAll: onSelectAll,
        },
        clickToRowSelect: false,
      });

      //表格的单行选中回调事件
      function onSelect(record, selected) {
        if (selected) {
          checkedKeys.value = [...checkedKeys.value, record[rowKey.value]];
        } else {
          checkedKeys.value = checkedKeys.value.filter((item) => item != record[rowKey.value]);
        }
      }

      //表格的全选回调事件
      function onSelectAll(selected, selectedRows, changeRows) {
        const changeIds = changeRows.map((item) => item[rowKey.value]);
        if (selected) {
          checkedKeys.value = [...checkedKeys.value, ...changeIds];
        } else {
          checkedKeys.value = checkedKeys.value.filter((item) => {
            return !changeIds.includes(item);
          });
        }
      }

      //表格点击回调事件
      function handleClick(item, type) {
        if (click_event.value) return;
        operate_type.value = type;
        if (type == 'add') {
          go(`/manage_article/article_lists_to_add?type=add`);
        } else if (type == 'edit') {
          go(`/manage_article/article_lists_to_add?type=edit&id=${item.articleId}`);
        } else if (type == 'del') {
          click_event.value = true;
          operate_role({ ids: item.articleId });
        } else if (type == 'dels') {
          if (checkedKeys.value.length == 0) {
            failTip('请先选中数据');
          } else {
            click_event.value = true;
            operate_role({ ids: checkedKeys.value.join(',') });
          }
        }
      }

      //弹窗操作
      const operate_role = async (params) => {
        let res = {};
        if (operate_type.value == 'del' || operate_type.value == 'dels') {
          res = await delArticle(params);
        }
        click_event.value = false;
        if (res.state == 200) {
          sucTip(res.msg);
          reload();
        } else {
          failTip(res.msg);
        }
      };

      return {
        standardTable,
        go,
        operate_type,
        click_event,
        rowKey,
        checkedKeys,
        handleClick,
      };
    },
  });
</script>
<style lang="less" scoped></style>
