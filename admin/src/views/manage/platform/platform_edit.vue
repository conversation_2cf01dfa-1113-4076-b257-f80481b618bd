<template>
  <div class="section_padding">
    <div class="section_padding_back platform_list">
      <SldComHeader :type="1" :title="'编辑商品资料'" />
      <PlatformAdd :id="id" />

    </div>
  </div>
</template>
<script setup>
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import PlatformAdd from './platform_add.vue';
  import { useRoute } from 'vue-router';
  const route = useRoute();
  const id = route.query.id;
</script>
<style lang="less" scoped></style>
