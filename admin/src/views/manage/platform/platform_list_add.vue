<template>
  <div class="section_padding ">
    <div class="section_padding_back platform_list">
      <SldComHeader
        :type="2"
        :title="'商品资料库'"
        :tipTitle="tipData[tabIndex - 1].title"
        :tipData="tipData[tabIndex - 1].data"
        :clickFlag="true"
        @handle-toggle-tip="handleTip"
      />
      <a-tabs v-model:activeKey="tabIndex" type="card" @change="handleChange">
        <a-tab-pane :key="1" tab="店铺商品导入" />
        <a-tab-pane :key="2" tab="Excel导入" />
        <a-tab-pane :key="3" tab="直接发布" />
      </a-tabs>
      <div class="section_padding_tab_top"></div>
      <template v-if="tabIndex == 1">
        <div class="goods">
          <div class="goods_search">
            <BasicForm
              :tableFlag="true"
              @register="register"
              @submit="handleSubmit"
              @reset="handleReset"
            />
          </div>
          <div class="goods_main">
            <div class="goods_category">
              <div class="goods_category_title">商品分类</div>
              <div class="goods_category_list" :style="{ height: category_height }">
                <div
                  v-for="(item, index) in categoryList"
                  :key="index"
                  class="goods_category_item"
                  :class="selectedCategoryId == item.categoryId ? 'active' : ''"
                  @click="selectCategory(item.categoryId, 0)"
                  @mouseenter="() => get_category_list(item, 1)"
                >
                  <span>{{ item.categoryName }}</span>
                  <AliSvgIcon
                    iconName="icongengduo2"
                    width="14px"
                    height="14px"
                    :fillColor="'#434343'"
                  />
                </div>
                <div
                  v-if="showMoreCat"
                  class="goods_category_children"
                  :style="{ height: category_height }"
                >
                  <div
                    v-for="(item, index) in categoryChildrenList"
                    :key="index"
                    class="flex_row_start_start goods_category_children_list"
                  >
                    <div
                      class="goods_category_children_list_title"
                      :class="
                        selectedCategoryPid == item.categoryId ||
                        selectedCategoryId == item.categoryId
                          ? 'active'
                          : ''
                      "
                      @click="selectCategory(item.categoryId, 0)"
                      >{{ item.categoryName
                      }}<span
                        ><AliSvgIcon
                          iconName="icongengduo2"
                          width="14px"
                          height="14px"
                          fillColor="#101010" /></span
                    ></div>
                    <div
                      v-if="item.children"
                      class="flex_row_start_center goods_category_children_list_main"
                    >
                      <span
                        v-for="(items, indexs) in item.children"
                        :key="indexs"
                        class="goods_category_children_item"
                        :class="selectedCategoryId == items.categoryId ? 'active' : ''"
                        @click="selectCategory(items.categoryId, item.categoryId)"
                        >{{ items.categoryName }}</span
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="goods_list">
              <BasicTable @register="standardTable" style="padding: 0">
                <template #tableTitle>
                  <div class="goods_list_operate">
                    <Popconfirm
                      v-if="checkedKeys.length > 0"
                      title="确定批量导入选中的商品吗？"
                      @confirm="handleClick(null, 'export')"
                    >
                      <div class="goods_list_operate_item">批量导入</div>
                    </Popconfirm>
                    <div v-else class="goods_list_operate_item" @click="handleClick(null, 'export')"
                      >批量导入</div
                    >
                    <div v-if="checkedKeys.length" class="goods_list_operate_num"
                      >已选<span>{{ checkedKeys.length }}</span
                      >款商品</div
                    >
                  </div>
                </template>
                <template #bodyCell="{ column, record, text }">
                  <template v-if="column.key == 'mainImage'">
                    <div class="goods_list_mainIamge">
                      <Popover placement="right">
                        <template #content>
                          <div class="goods_list_mainIamge_pop">
                            <img :src="text" />
                          </div>
                        </template>
                        <div
                          class="goods_list_leftImage"
                          :style="{ backgroundImage: `url('${text}')` }"
                        ></div>
                      </Popover>
                      <div class="goods_list_rightInfo">
                        <div class="goods_list_goodsname">{{ record.goodsName }}</div>
                        <div class="goods_list_extraninfo">
                          <div v-if="record.categoryPath">{{ record.categoryPath }}</div>
                          <div v-if="record.storeName">所属店铺：{{ record.storeName }}</div>
                        </div>
                      </div>
                    </div>
                  </template>
                  <template v-else-if="column.key == 'action'">
                    <Popconfirm
                      title="确定批量导入选中的商品吗？"
                      @confirm="handleClick(record, 'export')"
                    >
                      <span class="common_page_edit">导入</span>
                    </Popconfirm>
                  </template>
                  <template v-else-if="column.key">
                    {{ text ? text : '--' }}
                  </template>
                </template>
              </BasicTable>
            </div>
          </div>
        </div>
      </template>
      <template v-else-if="tabIndex == 2">
        <ExcelPage />
      </template>
      <template v-else>
        <PlatformAdd @submit-event="handleAddGood" />
      </template>
      <SldSelGoodsSingleDiy
        :modalWidth="750"
        :rowId="'platformGoodsId'"
        :modalTitle="'查看规格'"
        :modalVisible="modalVisible"
        :dataSource="dataSource"
        :column="[
          {
            title: '商品规格',
            dataIndex: 'specValues',
            width: 100,
          },
          {
            title: '价格(元)',
            dataIndex: 'productPrice',
            width: 100,
          },
          {
            title: '条形码',
            dataIndex: 'barCode',
            width: 100,
          },
        ]"
        :pagination="false"
        :look="true"
        @cancle-event="
          () => {
            modalVisible = false;
          }
        "
      >
        <template #bodyCell="{ column, text }">
          <template v-if="column.dataIndex">
            {{ text ? text : '--' }}
          </template>
        </template>
      </SldSelGoodsSingleDiy>
    </div>
  </div>
</template>
<script lang="ts">
  import { defineComponent, ref, reactive, onMounted, onUnmounted } from 'vue';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { Tabs, Popconfirm, Popover } from 'ant-design-vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import {
    getCategoryList,
    getGoodsPlatformCategory,
    getStorePlatformList,
    importPlatform,
  } from '/@/api/manage/manage';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import SldSelGoodsSingleDiy from '/@/components/SldSelGoodsSingleDiy/index.vue';
  import ExcelPage from './platform_excel.vue';
  import PlatformAdd from './platform_add.vue';
  import { failTip, sucTip } from '/@/utils/utils';

  export default defineComponent({
    components: {
      SldComHeader,
      [Tabs.name]: Tabs,
      [Tabs.TabPane.name]: Tabs.TabPane,
      BasicForm,
      BasicTable,
      Popconfirm,
      Popover,
      SldSelGoodsSingleDiy,
      ExcelPage,
      PlatformAdd,
    },
    setup() {
      const tipData = ref([
        {
          title: '',
          data: [
            '导入店铺商品数据：将店铺商品导入到商品资料库，店铺从商品资料库导入的商品除外；',
            '如果导入的店铺商品的条形码与商品资料库商品条形码重复，则需要判断商品资料库商品是否有图片，没有图片，则将店铺商品覆盖商品资料库商品，有图片，则不导入；',
            '如果导入的店铺商品的条形码未与商品资料库商品条形码重复，则允许导入。',
          ],
        },
        {
          title: '操作提示',
          data: [
            '请先下载商品导入模板，并按照批注中的要求填写商品数据，未按要求填写将会导致商品导入失败',
            '选择.xls文件，每次只能导入一个文件，建议每次导入不超过6000条商品数据',
          ],
        },
        {
          title: '',
          data: [],
        },
      ]);
      const tabIndex = ref(1);
      const headerTipFlag = ref(true); //顶部标题组件是否展示提示信息
      const category_height = ref(
        document.body.clientHeight -
          40 -
          (headerTipFlag.value ? 120 + 15 : 35) -
          48 -
          52 -
          49 -
          52  - 20 +
          'px',
      ); //商品分类模块高度

      function handleTip(val) {
        headerTipFlag.value = val;
        category_height.value =
          document.body.clientHeight - 40 - (val ? 120 + 15 : 35) - 48 - 52 - 49 - 52 - 20 + 'px';
        if(tabIndex.value == 1){
          redoHeight()
        }
      }

      const showMoreCat = ref(false);
      const selectedCategoryId = ref('');
      const selectedCategoryPid = ref('');
      const categoryList: any = ref([]);
      const categoryChildrenList: any = ref([]);
      let searchValue = reactive({});
      const schemas: FormSchema[] = [
        {
          field: 'goodsName',
          component: 'Input',
          colProps: { span: 6, style: 'width:260px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            maxlength: 6,
            placeholder: '请输入商品名称',
            size: 'default',
          },
          label: '商品名称',
          labelWidth: 80,
        },
        {
          field: 'brandName',
          component: 'Input',
          colProps: { span: 6, style: 'width:260px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            maxlength: 6,
            placeholder: '请输入商品品牌',
            size: 'default',
          },
          label: '商品品牌',
          labelWidth: 80,
        },
        {
          field: 'storeName',
          component: 'Input',
          colProps: { span: 6, style: 'width:260px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            maxlength: 6,
            placeholder: '请输入店铺名称',
            size: 'default',
          },
          label: '店铺名称',
          labelWidth: 80,
        },
      ];
      const [register] = useForm({
        labelWidth: 120,
        schemas: schemas,
        actionColOptions: { span: 24 },
      });
      const rowKey = ref('goodsId');
      const checkedKeys: any = ref([]);
      const [standardTable, { reload: reload,redoHeight }] = useTable({
        rowKey: rowKey,
        api: (rag) =>
          getStorePlatformList({ ...rag, ...searchValue, categoryId: selectedCategoryId.value }),
        fetchSetting: {
          pageField: 'current',
          sizeField: 'pageSize',
          listField: 'data.list',
          totalField: 'data.pagination.total',
        },
        columns: [
          {
            title: '商品信息',
            dataIndex: 'mainImage',
            width: 220,
          },
          {
            title: '商品品牌',
            dataIndex: 'brandName',
            width: 60,
          },
          {
            title: '所属店铺',
            dataIndex: 'storeName',
            width: 70,
          },
          {
            title: '售价(元)',
            dataIndex: 'goodsPrice',
            width: 60,
          },
        ],
        actionColumn: {
          width: 50,
          title: '操作',
          dataIndex: 'action',
        },
        bordered: true,
        striped: false,
        rowSelection: {
          type: 'checkbox',
          selectedRowKeys: checkedKeys,
          onSelect: onSelect,
          onSelectAll: onSelectAll,
        },
        clickToRowSelect: false,
      });

      const click_event = ref(false);
      const operate_type = ref('');

      //tab切换
      function handleChange(e) {
        if (e == 1) {
          showMoreCat.value = false;
          selectedCategoryId.value = '';
          selectedCategoryPid.value = '';
          categoryChildrenList.value = [];
          checkedKeys.value = [];
          reload();
        }
      }

      //表格点击回调事件
      function handleClick(item, type) {
        if (click_event.value) return;
        operate_type.value = type;
        let params: any = {};
        if (type == 'export') {
          if (!item && checkedKeys.value.length == 0) {
            failTip('请先选中数据');
            return;
          }
          click_event.value = true;
          params.goodsIds = item === null ? checkedKeys.value.join(',') : item.goodsId;
        }
        handleOperate(params);
      }

      const handleOperate = async (params) => {
        let res: any = {};
        if (operate_type.value == 'export') {
          res = await importPlatform(params);
        }
        click_event.value = false;
        if (res.state == 200) {
          sucTip(res.msg);
          if (tabIndex.value == 1) {
            checkedKeys.value = [];
            reload();
          }
        } else {
          failTip(res.msg);
        }
      };

      //表格的单行选中回调事件
      function onSelect(record, selected) {
        if (selected) {
          checkedKeys.value = [...checkedKeys.value, record[rowKey.value]];
        } else {
          checkedKeys.value = checkedKeys.value.filter((item) => item != record[rowKey.value]);
        }
      }

      //表格的全选回调事件
      function onSelectAll(selected, selectedRows, changeRows) {
        const changeIds = changeRows.map((item) => item[rowKey.value]);
        if (selected) {
          checkedKeys.value = [...checkedKeys.value, ...changeIds];
        } else {
          checkedKeys.value = checkedKeys.value.filter((item) => {
            return !changeIds.includes(item);
          });
        }
      }

      function selectCategory(categoryId, categoryPid) {
        selectedCategoryId.value = categoryId;
        selectedCategoryPid.value = categoryPid;
        showMoreCat.value = false;
        if (tabIndex.value == 1) {
          reload();
        }
      }

      onMounted(() => {
        get_category_list({ categoryId: 0, isSort: true, pageSize: 10000 }, 0);
        window.addEventListener('resize', resizeCategoryHeight, true);
      });

      onUnmounted(() => {
        window.removeEventListener('resize', resizeCategoryHeight, true);
      });

      function resizeCategoryHeight() {
        category_height.value =
          document.body.clientHeight -
          40 -
          (headerTipFlag.value ? 120 + 15 : 32) -
          48 -
          52 -
          49 -
          52 +
          'px';
      }

      //获取商品分类
      const get_category_list = async (params, grade) => {
        let res: any;
        if (grade === 0) {
          res = await getCategoryList(params);
        } else if (grade === 1) {
          res = await getGoodsPlatformCategory({ categoryId1: params.categoryId });
        } else {
          return;
        }
        if (res.state == 200) {
          if (grade === 0) {
            categoryList.value = res.data.list;
          } else {
            categoryChildrenList.value = res.data;
            showMoreCat.value = true;
          }
        } else {
          failTip(res.msg);
        }
      };

      //筛选搜索
      function handleSubmit(val) {
        for (let key in val) {
          if (val[key] === undefined || val[key] === null) {
            delete val[key];
          }
        }
        searchValue = val;
        if (tabIndex.value == 1) {
          reload();
        }
      }

      //筛选重置
      function handleReset() {
        searchValue = {};
        selectedCategoryId.value = '';
        if (tabIndex.value == 1) {
          checkedKeys.value = [];
          reload();
        }
      }

      function handleAddGood() {
        tabIndex.value = 1;
        showMoreCat.value = false;
        selectedCategoryId.value = '';
        selectedCategoryPid.value = '';
        categoryChildrenList.value = [];
        checkedKeys.value = [];
        reload();
      }

      return {
        tipData,
        handleTip,
        headerTipFlag,
        tabIndex,
        handleChange,
        category_height,
        handleOperate,

        register,
        schemas,
        handleSubmit,
        handleReset,

        selectedCategoryId,
        selectedCategoryPid,
        categoryList,
        categoryChildrenList,
        showMoreCat,
        get_category_list,
        rowKey,
        searchValue,
        checkedKeys,
        standardTable,
        onSelect,
        onSelectAll,
        handleClick,
        selectCategory,
        click_event,
        resizeCategoryHeight,
        handleAddGood,
      };
    },
  });
</script>
<style lang="less" scoped>
  .platform_list {
    .goods {
      // .goods_search {}
      .goods_main {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;

        .goods_category {
          position: relative;
          flex-shrink: 0;
          width: 220px;
          margin-right: 10px;
          border: 1px solid #ebedf0;
          border-radius: 2px;
          background: #fff;

          .goods_category_title {
            width: 100%;
            height: 49px;
            padding-left: 20px;
            border-bottom: 1px solid #d8d8d8;
            color: #323233;
            font-family: PingFangSC-Medium, 'PingFang SC';
            font-size: 14px;
            font-weight: 500;
            line-height: 49px;
          }

          .goods_category_list {
            width: 100%;
            overflow-x: hidden;
            overflow-y: auto;

            .goods_category_item {
              display: flex;
              position: relative;
              align-items: center;
              justify-content: space-between;
              height: 44px;
              padding-left: 20px;
              cursor: pointer;

              span {
                color: #323233;
                font-family: PingFangSC-Regular, 'PingFang SC';
                font-size: 14px;
                font-weight: 400;
                line-height: 20px;
              }

              svg {
                margin-right: 76px;
              }

              &:hover,
              &.active {
                span {
                  color: @primary-color;
                }

                svg {
                  fill: #ff701e !important;
                }
              }
            }

            &:hover {
              .goods_category_children {
                display: block;
              }
            }

            .goods_category_children {
              display: none;
              position: absolute;
              z-index: 999;
              top: 49px;
              bottom: 0;
              left: 205px;
              width: 800px;
              padding: 25px;
              border: 1px solid #dfdfdf;
              background: #f9f9f9;

              .goods_category_children_list {
                .goods_category_children_list_title {
                  flex-shrink: 0;
                  width: 108px;
                  height: 20px;
                  color: #4c4c4c;
                  font-family: PingFangSC-Semibold, 'PingFang SC';
                  font-size: 14px;
                  font-weight: 600;
                  line-height: 20px;
                  text-align: right;
                  white-space: nowrap;
                  cursor: pointer;

                  span {
                    position: relative;
                    top: 2px;
                    margin-left: 6px;
                  }

                  &:hover,
                  &.active {
                    color: @primary-color;

                    span {
                      svg {
                        fill: @primary-color !important;
                      }
                    }
                  }
                }

                .goods_category_children_list_main {
                  flex-wrap: wrap;
                  margin-left: 20px;

                  .goods_category_children_item {
                    margin-right: 20px;
                    margin-bottom: 15px;
                    font-family: PingFangSC-Regular, 'PingFang SC';
                    font-size: 14px;
                    font-weight: 400;
                    line-height: 20px;
                    cursor: pointer;

                    &:hover,
                    &.active {
                      color: @primary-color;
                    }
                  }
                }
              }
            }
          }
        }

        .goods_list {
          flex: 1;

          .goods_list_operate {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            width: 100%;

            .goods_list_operate_item {
              height: 28px;
              margin-left: 20px;
              padding: 0 16px;
              border: 1px solid #d9d9d9;
              border-radius: 4px;
              background: #fff;
              color: #595959;
              font-family: PingFangSC-Regular, 'PingFang SC';
              font-size: 13px;
              font-weight: 400;
              line-height: 26px;
              cursor: pointer;
            }

            .goods_list_operate_num {
              margin-left: 20px;
              color: #999;
              font-family: PingFangSC-Regular, 'PingFang SC';
              font-size: 13px;
              font-weight: 400;

              span {
                margin-right: 2px;
                margin-left: 2px;
                color: #ff6a12;
                font-weight: bold;
              }
            }
          }
        }
      }
    }
  }
</style>
