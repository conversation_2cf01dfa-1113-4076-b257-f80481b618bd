<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <div class="platform_list">
        <SldComHeader
          :type="2"
          :title="'商品资料库'"
          :tipData="['商品在刊登后才能被商家导入。对于待刊登和已下架状态的商品，则不能导入。']"
          :clickFlag="true"
          @handle-toggle-tip="handleTip"
        />
        <a-tabs v-model:activeKey="tabIndex" type="card" @change="handleChange">
          <a-tab-pane :key="1" tab="已刊登" />
          <a-tab-pane :key="2" tab="待刊登" />
          <a-tab-pane :key="3" tab="已下架" />
        </a-tabs>
        <div class="section_padding_tab_top"></div>
        <div class="goods">
          <div class="goods_search">
            <BasicForm
              :tableFlag="true"
              @register="register"
              @submit="handleSubmit"
              @reset="handleReset"
            />
          </div>
          <div class="goods_main">
            <div class="goods_category">
              <div class="goods_category_title">商品分类</div>
              <div class="goods_category_list" :style="{ height: category_height }">
                <div
                  v-for="(item, index) in categoryList"
                  :key="index"
                  class="goods_category_item"
                  :class="selectedCategoryId == item.categoryId ? 'active' : ''"
                  @click="selectCategory(item.categoryId, 0)"
                  @mouseenter="() => get_category_list(item, 1)"
                >
                  <span>{{ item.categoryName }}</span>
                  <AliSvgIcon
                    iconName="icongengduo2"
                    width="14px"
                    height="14px"
                    :fillColor="'#434343'"
                  />
                </div>
                <div
                  v-if="showMoreCat"
                  class="goods_category_children"
                  :style="{ height: category_height }"
                >
                  <div
                    v-for="(item, index) in categoryChildrenList"
                    :key="index"
                    class="flex_row_start_start goods_category_children_list"
                  >
                    <div
                      class="goods_category_children_list_title"
                      :class="
                        selectedCategoryPid == item.categoryId || selectedCategoryId == item.categoryId
                          ? 'active'
                          : ''
                      "
                      @click="selectCategory(item.categoryId, 0)"
                      >{{ item.categoryName
                      }}<span
                        ><AliSvgIcon
                          iconName="icongengduo2"
                          width="14px"
                          height="14px"
                          fillColor="#101010" /></span
                    ></div>
                    <div
                      v-if="item.children"
                      class="flex_row_start_center goods_category_children_list_main"
                    >
                      <span
                        v-for="(items, indexs) in item.children"
                        :key="indexs"
                        class="goods_category_children_item"
                        :class="selectedCategoryId == items.categoryId ? 'active' : ''"
                        @click="selectCategory(items.categoryId, item.categoryId)"
                        >{{ items.categoryName }}</span
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="goods_list">
              <template v-if="tabIndex == 1">
                <BasicTable @register="onlineStandardTable" style="padding: 0">
                  <template #tableTitle>
                    <div class="goods_list_operate">
                      <div class="goods_list_operate_item" @click="handleClick(null, 'lockup')"
                        >批量下架</div
                      >
                      <Popconfirm
                        v-if="checkedKeys.length > 0"
                        title="确认批量删除选中的商品吗？"
                        @confirm="handleClick(null, 'del')"
                      >
                        <div class="goods_list_operate_item">批量删除</div>
                      </Popconfirm>
                      <div v-else class="goods_list_operate_item" @click="handleClick(null, 'del')"
                        >批量删除</div
                      >
                      <div v-if="checkedKeys.length" class="goods_list_operate_num"
                        >已选<span>{{ checkedKeys.length }}</span
                        >款商品</div
                      >
                    </div>
                  </template>
                  <template #bodyCell="{ column, record, text }">
                    <template v-if="column.key == 'mainImage'">
                      <div class="goods_list_mainIamge">
                        <Popover placement="right">
                          <template #content>
                            <div class="goods_list_mainIamge_pop">
                              <img :src="text" />
                            </div>
                          </template>
                          <div
                            class="goods_list_leftImage"
                            :style="{ backgroundImage: `url('${text}')` }"
                          ></div>
                        </Popover>
                        <div class="goods_list_rightInfo">
                          <div class="goods_list_goodsname">{{ record.goodsName }}</div>
                          <div class="goods_list_extraninfo">
                            <div v-if="record.categoryPath">{{ record.categoryPath }}</div>
                            <div v-if="record.storeName">所属店铺：{{ record.storeName }}</div>
                          </div>
                        </div>
                      </div>
                    </template>
                    <template v-else-if="column.key == 'action'">
                      <TableAction
                        :actions="[
                          {
                            label: '编辑',
                            onClick: handleClick.bind(null, record, 'edit'),
                          },
                          {
                            label: 'SKU',
                            onClick: handleClick.bind(null, record, 'sku'),
                          },
                          {
                            label: '下架',
                            onClick: handleClick.bind(null, record, 'lockup'),
                          },
                          {
                            label: '删除',
                            popConfirm: {
                              title: '删除后不可恢复，是否确定删除？',
                              placement: 'left',
                              confirm: handleClick.bind(null, record, 'del'),
                            },
                          },
                        ]"
                      />
                    </template>
                    <template v-else-if="column.key">
                      {{ text ? text : '--' }}
                    </template>
                  </template>
                </BasicTable>
              </template>
              <template v-else-if="tabIndex == 2">
                <BasicTable @register="storageStandardTable" style="padding: 0">
                  <template #tableTitle>
                    <div class="goods_list_operate">
                      <Popconfirm
                        v-if="checkedKeys.length > 0"
                        title="确定批量刊登选中的商品吗？"
                        @confirm="handleClick(null, 'publish')"
                      >
                        <div class="goods_list_operate_item">批量刊登</div>
                      </Popconfirm>
                      <div v-else class="goods_list_operate_item" @click="handleClick(null, 'publish')"
                        >批量刊登</div
                      >
                      <Popconfirm
                        v-if="checkedKeys.length > 0"
                        title="确认批量删除选中的商品吗？"
                        @confirm="handleClick(null, 'del')"
                      >
                        <div class="goods_list_operate_item">批量删除</div>
                      </Popconfirm>
                      <div v-else class="goods_list_operate_item" @click="handleClick(null, 'del')"
                        >批量删除</div
                      >
                      <div v-if="checkedKeys.length" class="goods_list_operate_num"
                        >已选<span>{{ checkedKeys.length }}</span
                        >款商品</div
                      >
                    </div>
                  </template>
                  <template #bodyCell="{ column, record, text }">
                    <template v-if="column.key == 'mainImage'">
                      <div class="goods_list_mainIamge">
                        <Popover placement="right">
                          <template #content>
                            <div class="goods_list_mainIamge_pop">
                              <img :src="text" />
                            </div>
                          </template>
                          <div
                            class="goods_list_leftImage"
                            :style="{ backgroundImage: `url('${text}')` }"
                          ></div>
                        </Popover>
                        <div class="goods_list_rightInfo">
                          <div class="goods_list_goodsname">{{ record.goodsName }}</div>
                          <div class="goods_list_extraninfo">
                            <div v-if="record.categoryPath">{{ record.categoryPath }}</div>
                            <div v-if="record.storeName">所属店铺：{{ record.storeName }}</div>
                          </div>
                        </div>
                      </div>
                    </template>
                    <template v-else-if="column.key == 'action'">
                      <TableAction
                        :actions="[
                          {
                            label: '编辑',
                            onClick: handleClick.bind(null, record, 'edit'),
                          },
                          {
                            label: 'SKU',
                            onClick: handleClick.bind(null, record, 'sku'),
                          },
                          {
                            label: '刊登',
                            popConfirm: {
                              title: '确定批量刊登选中的商品吗？',
                              placement: 'left',
                              confirm: handleClick.bind(null, record, 'publish'),
                            },
                          },
                          {
                            label: '删除',
                            popConfirm: {
                              title: '删除后不可恢复，是否确定删除？',
                              placement: 'left',
                              confirm: handleClick.bind(null, record, 'del'),
                            },
                          },
                        ]"
                      />
                    </template>
                    <template v-else-if="column.key">
                      {{ text ? text : '--' }}
                    </template>
                  </template>
                </BasicTable>
              </template>
              <template v-else>
                <BasicTable @register="lockoffStandardTable" style="padding: 0">
                  <template #tableTitle>
                    <div class="goods_list_operate">
                      <Popconfirm
                        v-if="checkedKeys.length > 0"
                        title="确定批量刊登选中的商品吗？"
                        @confirm="handleClick(null, 'publish')"
                      >
                        <div class="goods_list_operate_item">批量刊登</div>
                      </Popconfirm>
                      <div v-else class="goods_list_operate_item" @click="handleClick(null, 'publish')"
                        >批量刊登</div
                      >
                      <Popconfirm
                        v-if="checkedKeys.length > 0"
                        title="确认批量删除选中的商品吗？"
                        @confirm="handleClick(null, 'del')"
                      >
                        <div class="goods_list_operate_item">批量删除</div>
                      </Popconfirm>
                      <div v-else class="goods_list_operate_item" @click="handleClick(null, 'del')"
                        >批量删除</div
                      >
                      <div v-if="checkedKeys.length" class="goods_list_operate_num"
                        >已选<span>{{ checkedKeys.length }}</span
                        >款商品</div
                      >
                    </div>
                  </template>
                  <template #bodyCell="{ column, record, text }">
                    <template v-if="column.key == 'mainImage'">
                      <div class="goods_list_mainIamge">
                        <Popover placement="right">
                          <template #content>
                            <div class="goods_list_mainIamge_pop">
                              <img :src="text" />
                            </div>
                          </template>
                          <div
                            class="goods_list_leftImage"
                            :style="{ backgroundImage: `url('${text}')` }"
                          ></div>
                        </Popover>
                        <div class="goods_list_rightInfo">
                          <div class="goods_list_goodsname">{{ record.goodsName }}</div>
                          <div class="goods_list_extraninfo">
                            <div v-if="record.categoryPath">{{ record.categoryPath }}</div>
                            <div v-if="record.storeName">所属店铺：{{ record.storeName }}</div>
                          </div>
                        </div>
                      </div>
                    </template>
                    <template v-else-if="column.key == 'action'">
                      <TableAction
                        :actions="[
                          {
                            label: '编辑',
                            onClick: handleClick.bind(null, record, 'edit'),
                          },
                          {
                            label: 'SKU',
                            onClick: handleClick.bind(null, record, 'sku'),
                          },
                          {
                            label: '刊登',
                            popConfirm: {
                              title: '确定批量刊登选中的商品吗？',
                              placement: 'left',
                              confirm: handleClick.bind(null, record, 'publish'),
                            },
                          },
                          {
                            label: '删除',
                            popConfirm: {
                              title: '删除后不可恢复，是否确定删除？',
                              placement: 'left',
                              confirm: handleClick.bind(null, record, 'del'),
                            },
                          },
                        ]"
                      />
                    </template>
                    <template v-else-if="column.key">
                      {{ text ? text : '--' }}
                    </template>
                  </template>
                </BasicTable>
              </template>
            </div>
      </div>
        </div>
      </div>
      <SldModal
        :width="width"
        :title="title"
        :visible="visible"
        :content="content"
        :confirmBtnLoading="confirmBtnLoading"
        :showFoot="true"
        :parentPage="'good_list'"
        @cancle-event="handleCancle"
        @confirm-event="handleConfirm"
      />
      <SldSelGoodsSingleDiy
        :modalWidth="750"
        :rowId="'platformGoodsId'"
        :modalTitle="'查看规格'"
        :modalVisible="modalVisible"
        :dataSource="dataSource"
        :column="[
          {
            title: '商品规格',
            dataIndex: 'specValues',
            width: 100,
          },
          {
            title: '价格(元)',
            dataIndex: 'productPrice',
            width: 100,
          },
          {
            title: '条形码',
            dataIndex: 'barCode',
            width: 100,
          },
        ]"
        :pagination="false"
        :look="true"
        @cancle-event="
          () => {
            modalVisible = false;
          }
        "
      >
        <template #bodyCell="{ column, text }">
          <template v-if="column.dataIndex">
            {{ text ? text : '--' }}
          </template>
        </template>
      </SldSelGoodsSingleDiy>
    </div>
  </div>
</template>
<script lang="ts">
  import { defineComponent, ref, reactive, onMounted, onUnmounted } from 'vue';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { Tabs, Popconfirm, Popover } from 'ant-design-vue';
  import { BasicTable, useTable ,TableAction} from '/@/components/Table';
  import {
    getCategoryList,
    getGoodsPlatformCategory,
    getGoodsPlatformList,
    lockupGoodsPlatform,
    publishGoodsPlatform,
    delGoodsPlatform,
  } from '/@/api/manage/manage';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import SldModal from '@/components/SldModal/index.vue';
  import SldSelGoodsSingleDiy from '/@/components/SldSelGoodsSingleDiy/index.vue';
  import { useGo } from '/@/hooks/web/usePage';
  import { failTip, sucTip } from '/@/utils/utils';

  export default defineComponent({
    components: {
      SldComHeader,
      [Tabs.name]: Tabs,
      [Tabs.TabPane.name]: Tabs.TabPane,
      BasicForm,
      BasicTable,
      Popconfirm,
      Popover,
      SldModal,
      SldSelGoodsSingleDiy,
      TableAction
    },
    setup() {
      const go = useGo();
      const tabIndex = ref(1);
      const indexFlag = ref([1, 0, 0]); //各列表初次切换时不执行reload事件
      const headerTipFlag = ref(true); //顶部标题组件是否展示提示信息
      const category_height = ref(
        document.body.clientHeight - 40 - (headerTipFlag.value ? 75 + 10 : 32) - 48 - 52 - 49 - 52 - 22 + 'px',
      ); //商品分类模块高度

      function handleTip(val) {
        headerTipFlag.value = val;
        category_height.value =
          document.body.clientHeight - 40 - (val ? 75 + 10 : 32) - 48 - 52 - 49 - 52  - 22  + 'px';
        if(tabIndex.value == 1){
          onlineRedoHeight()
        }else if(tabIndex.value == 2){
          storageRedoHeight()
        }else{
          lockoffRedoHeight()
        }
      }

      const showMoreCat = ref(false);
      const selectedCategoryId = ref('');
      const selectedCategoryPid = ref('');
      const categoryList: any = ref([]);
      const categoryChildrenList: any = ref([]);
      let searchValue = reactive({});
      const onlineSchemas: FormSchema[] = [
        {
          field: 'goodsName',
          component: 'Input',
          colProps: { span: 6, style: 'width:260px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            maxlength: 6,
            placeholder: '请输入商品名称',
            size: 'default',
          },
          label: '商品名称',
          labelWidth: 80,
        },
        {
          field: 'brandName',
          component: 'Input',
          colProps: { span: 6, style: 'width:260px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            maxlength: 6,
            placeholder: '请输入商品品牌',
            size: 'default',
          },
          label: '商品品牌',
          labelWidth: 80,
        },
        {
          field: 'isVirtualGoods',
          component: 'Select',
          colProps: { span: 6, style: 'width:240px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            placeholder: '请选择商品类型',
            options: [
              { key: 0, label: '全部', value: '' },
              { key: 1, label: '实物商品', value: '1' },
              { key: 2, label: '虚拟商品', value: '2' },
            ],
            size: 'default',
          },
          label: '商品类型',
          labelWidth: 80,
        },
      ];
      const [register] = useForm({
        labelWidth: 120,
        schemas: onlineSchemas,
        actionColOptions: { span: 24 },
      });
      const rowKey = ref('platformGoodsId');
      const checkedKeys: any = ref([]);
      const fetchSetting = {
        pageField: 'current',
        sizeField: 'pageSize',
        listField: 'data.list',
        totalField: 'data.pagination.total',
      };
      const online_storage_columns = [
        {
          title: '商品信息',
          dataIndex: 'mainImage',
          width: 250,
        },
        {
          title: '商品品牌',
          dataIndex: 'brandName',
          width: 100,
        },
        {
          title: '售价(元)',
          dataIndex: 'goodsPrice',
          width: 100,
        },
      ];
      const lockoff_columns = [
        {
          title: '商品信息',
          dataIndex: 'mainImage',
          width: 220,
        },
        {
          title: '商品品牌',
          dataIndex: 'brandName',
          width: 70,
        },
        {
          title: '售价(元)',
          dataIndex: 'goodsPrice',
          width: 70,
        },
        {
          title: '下架理由',
          dataIndex: 'offlineReason',
          width: 70,
        },
      ];
      const actionColumn = {
        width: 200,
        title: '操作',
        dataIndex: 'action',
      };
      const [onlineStandardTable, { reload: onlineReload,redoHeight:onlineRedoHeight }] = useTable({
        rowKey: rowKey,
        api: (rag) =>
          getGoodsPlatformList({
            ...rag,
            state: 2,
            ...searchValue,
            categoryId: selectedCategoryId.value,
          }),
        fetchSetting: fetchSetting,
        columns: online_storage_columns,
        actionColumn: actionColumn,
        bordered: true,
        striped: false,
        rowSelection: {
          type: 'checkbox',
          selectedRowKeys: checkedKeys,
          onSelect: onSelect,
          onSelectAll: onSelectAll,
        },
        clickToRowSelect: false,
      });
      const [storageStandardTable, { reload: storageReload,redoHeight:storageRedoHeight }] = useTable({
        rowKey: rowKey,
        api: (rag) =>
          getGoodsPlatformList({
            ...rag,
            state: 1,
            ...searchValue,
            categoryId: selectedCategoryId.value,
          }),
        fetchSetting: fetchSetting,
        columns: online_storage_columns,
        actionColumn: actionColumn,
        bordered: true,
        striped: false,
        rowSelection: {
          type: 'checkbox',
          selectedRowKeys: checkedKeys,
          onSelect: onSelect,
          onSelectAll: onSelectAll,
        },
        clickToRowSelect: false,
      });
      const [lockoffStandardTable, { reload: lockoffReload,redoHeight:lockoffRedoHeight }] = useTable({
        rowKey: rowKey,
        api: (rag) =>
          getGoodsPlatformList({
            ...rag,
            state: 3,
            ...searchValue,
            categoryId: selectedCategoryId.value,
          }),
        fetchSetting: fetchSetting,
        columns: lockoff_columns,
        actionColumn: actionColumn,
        bordered: true,
        striped: false,
        rowSelection: {
          type: 'checkbox',
          selectedRowKeys: checkedKeys,
          onSelect: onSelect,
          onSelectAll: onSelectAll,
        },
        clickToRowSelect: false,
      });

      const click_event = ref(false);
      const width = ref(550);
      const title = ref('');
      const visible = ref(false);
      const content: any = ref([]);
      const confirmBtnLoading = ref(false);
      const operate_item: any = ref({});
      const operate_type = ref('');
      const operate_data = ref([]);
      const operate_lockup_data = ref([
        {
          type: 'textarea',
          label: `下架理由`,
          name: 'offlineReason',
          placeholder: `请输入下架理由`,
          extra: `最多输入100个字`,
          maxLength: 100,
          rules: [
            {
              required: true,
              whitespace: true,
              message: '请输入下架理由',
            },
          ],
        },
      ]);

      const modalVisible = ref(false);
      const dataSource = ref([]);

      //tab切换
      function handleChange(e) {
        if (!indexFlag.value[e - 1]) {
          indexFlag.value[e - 1] = 1;
        } else {
          if (e == 1) {
            onlineReload();
          } else if (e == 2) {
            storageReload();
          } else {
            lockoffReload();
          }
        }
        showMoreCat.value = false;
        selectedCategoryId.value = '';
        selectedCategoryPid.value = '';
        categoryChildrenList.value = [];
        checkedKeys.value = [];
      }

      //表格点击回调事件
      function handleClick(item, type) {
        if (click_event.value) return;
        operate_item.value = item ? item : null;
        operate_type.value = type;
        let params: any = {};
        let data = JSON.parse(JSON.stringify(operate_data.value));
        if (type == 'lockup') {
          if (!item && checkedKeys.value.length == 0) {
            failTip('请先选中数据');
            return;
          }
          data = JSON.parse(JSON.stringify(operate_lockup_data.value));
          title.value = '商品下架';
        } else if (type == 'publish' || type == 'del') {
          if (!item && checkedKeys.value.length == 0) {
            failTip('请先选中数据');
            return;
          }
          click_event.value = true;
          params.platformGoodsIds =
            operate_item.value === null ? checkedKeys.value.join(',') : item.platformGoodsId;
          operate_role(params);
          return;
        } else if (type == 'sku') {
          modalVisible.value = true;
          dataSource.value = item.productList;
          return;
        } else if (type == 'edit') {
          go(`/manage_goods_platform/list_to_edit?id=${item.platformGoodsId}`);
          return;
        } else {
          return;
        }
        width.value = 550;
        content.value = data;
        visible.value = true;
      }

      //表格的单行选中回调事件
      function onSelect(record, selected) {
        if (selected) {
          checkedKeys.value = [...checkedKeys.value, record[rowKey.value]];
        } else {
          checkedKeys.value = checkedKeys.value.filter((item) => item != record[rowKey.value]);
        }
      }

      //表格的全选回调事件
      function onSelectAll(selected, selectedRows, changeRows) {
        const changeIds = changeRows.map((item) => item[rowKey.value]);
        if (selected) {
          checkedKeys.value = [...checkedKeys.value, ...changeIds];
        } else {
          checkedKeys.value = checkedKeys.value.filter((item) => {
            return !changeIds.includes(item);
          });
        }
      }

      //弹窗确认事件
      function handleConfirm(val) {
        if (click_event.value) return;
        click_event.value = true;
        if (operate_type.value == 'lockup') {
          val.platformGoodsIds =
            operate_item.value !== null
              ? operate_item.value.platformGoodsId
              : checkedKeys.value.join(',');
        }
        operate_role(val);
      }

      //弹窗取消事件
      function handleCancle() {
        visible.value = false;
        content.value = [];
        operate_item.value = {};
      }

      function selectCategory(categoryId, categoryPid) {
        selectedCategoryId.value = categoryId;
        selectedCategoryPid.value = categoryPid;
        showMoreCat.value = false;
        if (tabIndex.value == 1) {
          onlineReload();
        } else if (tabIndex.value == 2) {
          storageReload();
        } else {
          lockoffReload();
        }
      }

      //操作
      const operate_role = async (params) => {
        let res: any = {};
        if (operate_type.value == 'lockup') {
          res = await lockupGoodsPlatform(params);
        } else if (operate_type.value == 'publish') {
          res = await publishGoodsPlatform(params);
        } else if (operate_type.value == 'del') {
          res = await delGoodsPlatform(params);
        }
        click_event.value = false;
        if (res.state == 200) {
          sucTip(res.msg);
          handleCancle();
          if (tabIndex.value == 1) {
            onlineReload();
          } else if (tabIndex.value == 2) {
            storageReload();
          } else {
            lockoffReload();
          }
          checkedKeys.value = [];
        } else {
          failTip(res.msg);
        }
      };

      onMounted(() => {
        get_category_list({ categoryId: 0, isSort: true, pageSize: 10000 }, 0);
        window.addEventListener('resize', resizeCategoryHeight, true);
      });

      onUnmounted(() => {
        window.removeEventListener('resize', resizeCategoryHeight, true);
      });

      function resizeCategoryHeight() {
        category_height.value =
          document.body.clientHeight -
          40 -
          (headerTipFlag.value ? 75 + 15 : 32) -
          48 -
          52 -
          49 -
          52 +
          'px';
      }

      //获取商品分类
      const get_category_list = async (params, grade) => {
        let res: any;
        if (grade === 0) {
          res = await getCategoryList(params);
        } else if (grade === 1) {
          res = await getGoodsPlatformCategory({ categoryId1: params.categoryId });
        } else {
          return;
        }
        if (res.state == 200) {
          if (grade === 0) {
            categoryList.value = res.data.list;
          } else {
            categoryChildrenList.value = res.data;
            showMoreCat.value = true;
          }
        } else {
          failTip(res.msg);
        }
      };

      //筛选搜索
      function handleSubmit(val) {
        for (let key in val) {
          if (val[key] === undefined || val[key] === null) {
            delete val[key];
          }
        }
        searchValue = val;
        if (tabIndex.value == 1) {
          onlineReload();
        } else if (tabIndex.value == 2) {
          storageReload();
        } else {
          lockoffReload();
        }
      }

      //筛选重置
      function handleReset() {
        searchValue = {};
        selectedCategoryId.value = '';
        checkedKeys.value = [];
        if (tabIndex.value == 1) {
          onlineReload();
        } else if (tabIndex.value == 2) {
          storageReload();
        } else {
          lockoffReload();
        }
      }

      return {
        tabIndex,
        handleTip,
        indexFlag,
        handleChange,
        headerTipFlag,
        category_height,

        register,
        onlineSchemas,
        handleSubmit,
        handleReset,

        selectedCategoryId,
        selectedCategoryPid,
        categoryList,
        categoryChildrenList,
        showMoreCat,
        get_category_list,
        rowKey,
        searchValue,
        checkedKeys,
        onlineStandardTable,
        storageStandardTable,
        lockoffStandardTable,
        onSelect,
        onSelectAll,
        handleClick,
        selectCategory,

        click_event,
        width,
        title,
        visible,
        content,
        confirmBtnLoading,
        operate_item,
        operate_type,
        operate_data,
        operate_lockup_data,
        handleCancle,
        handleConfirm,
        modalVisible,
        dataSource,
      };
    },
  });
</script>
<style lang="less" scoped>
  .platform_list {
    .goods {
      // .goods_search {}
      .goods_main {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;

        .goods_category {
          position: relative;
          flex-shrink: 0;
          width: 220px;
          margin-right: 10px;
          border: 1px solid #ebedf0;
          border-radius: 2px;
          background: #fff;

          .goods_category_title {
            width: 100%;
            height: 49px;
            padding-left: 20px;
            border-bottom: 1px solid #d8d8d8;
            color: #323233;
            font-family: PingFangSC-Medium, 'PingFang SC';
            font-size: 14px;
            font-weight: 500;
            line-height: 49px;
          }

          .goods_category_list {
            width: 100%;
            overflow-x: hidden;
            overflow-y: auto;

            .goods_category_item {
              display: flex;
              position: relative;
              align-items: center;
              justify-content: space-between;
              height: 44px;
              padding-left: 20px;
              cursor: pointer;

              span {
                color: #323233;
                font-family: PingFangSC-Regular, 'PingFang SC';
                font-size: 14px;
                font-weight: 400;
                line-height: 20px;
              }

              svg {
                margin-right: 76px;
              }

              &:hover,
              &.active {
                span {
                  color: @primary-color;
                }

                svg {
                  fill: @primary-color !important;
                }
              }
            }

            &:hover {
              .goods_category_children {
                display: block;
              }
            }

            .goods_category_children {
              display: none;
              position: absolute;
              z-index: 999;
              top: 49px;
              bottom: 0;
              left: 205px;
              width: 800px;
              padding: 25px;
              border: 1px solid #dfdfdf;
              background: #f9f9f9;

              .goods_category_children_list {
                .goods_category_children_list_title {
                  flex-shrink: 0;
                  width: 108px;
                  height: 20px;
                  color: #4c4c4c;
                  font-family: PingFangSC-Semibold, 'PingFang SC';
                  font-size: 14px;
                  font-weight: 600;
                  line-height: 20px;
                  text-align: right;
                  white-space: nowrap;
                  cursor: pointer;

                  span {
                    position: relative;
                    top: 2px;
                    margin-left: 6px;
                  }

                  &:hover,
                  &.active {
                    color: @primary-color;

                    span {
                      svg {
                        fill: @primary-color !important;
                      }
                    }
                  }
                }

                .goods_category_children_list_main {
                  flex-wrap: wrap;
                  margin-left: 20px;

                  .goods_category_children_item {
                    margin-right: 20px;
                    margin-bottom: 15px;
                    font-family: PingFangSC-Regular, 'PingFang SC';
                    font-size: 14px;
                    font-weight: 400;
                    line-height: 20px;
                    cursor: pointer;

                    &:hover,
                    &.active {
                      color: @primary-color;
                    }
                  }
                }
              }
            }
          }
        }

        .goods_list {
          width: calc(100% - 240px);
          .goods_list_operate {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            width: 100%;

            .goods_list_operate_item {
              height: 28px;
              margin-left: 20px;
              padding: 0 16px;
              border: 1px solid #d9d9d9;
              border-radius: 4px;
              background: #fff;
              color: #595959;
              font-family: PingFangSC-Regular, 'PingFang SC';
              font-size: 13px;
              font-weight: 400;
              line-height: 26px;
              cursor: pointer;
            }

            .goods_list_operate_num {
              margin-left: 20px;
              color: #999;
              font-family: PingFangSC-Regular, 'PingFang SC';
              font-size: 13px;
              font-weight: 400;

              span {
                margin-right: 2px;
                margin-left: 2px;
                color: #ff6a12;
                font-weight: bold;
              }
            }
          }
        }
      }
    }
  }
</style>
