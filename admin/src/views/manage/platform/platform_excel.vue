<template>
  <div class="plateform_excel">
    <div class="step_list">
      <a-steps :current="step">
        <a-step title="下载商品导入模板" />
        <a-step title="上传文件" />
        <a-step title="完成" />
      </a-steps>
    </div>
    <template v-if="step === 0">
      <div class="flex_column_center_center btn_list">
        <div class="flex_row_center_center download_modal" @click="downloadModal">
          <download-outlined :style="{ fontSize: '15px' }" />
          <span>下载商品导入模板</span>
        </div>
        <div class="flex_row_center_center next_btn" @click="nextStep(1)">下一步</div>
      </div>
    </template>
    <template v-else-if="step === 1">
      <UploadDragger
        name="file"
        v-model:fileList="fileList"
        :action="`${apiUrl}` + '/v3/goods/admin/goods/platform/import'"
        :headers="{
          Authorization: imgToken,
        }"
        :maxCount="1"
        @change="handleChange"
      >
        <div class="flex_column_center_center" style="height: 120px">
          <PlusOutlined :style="{ fontSize: '32px', color: '#999999' }" />
          <p style="margin-top: 4px">点击上传或者拖拽文件到该区域即可</p>
        </div>
      </UploadDragger>
      <template v-if="errorFileUrl">
        <div style="text-align: center;margin-top: 10px;">
          <span style="color: rgb(225, 0, 0);">上传失败！</span>
          <span>您可以</span>
          <a download="错误表格.xls" :href="errorFileUrl" class="global_color">下载错误表格</a>
          <span>，查看错误原因，修改后重新上传。</span>
        </div>
      </template>
    </template>
    <template v-else>
      <div class="finish_title">导入成功！</div>
      <div class="finish_content">您可以前往商品列表查看已导入的商品，或是继续导入。</div>
      <div class="flex_row_center_center btn_list">
        <div class="flex_row_center_center next_btn" @click="nextStep(1)">继续导入</div>
      </div>
    </template>
  </div>
</template>
<script lang="ts">
  import { defineComponent, ref } from 'vue';
  import { Steps, UploadDragger } from 'ant-design-vue';
  import { DownloadOutlined, PlusOutlined } from '@ant-design/icons-vue';
  import { downloadPlatform } from '/@/api/manage/manage';
  import { useGlobSetting } from '/@/hooks/setting';
  const { apiUrl } = useGlobSetting();
  import { getToken } from '/@/utils/auth';
  import { failTip } from '/@/utils/utils';

  export default defineComponent({
    components: {
      [Steps.name]: Steps,
      [Steps.Step.name]: Steps.Step,
      DownloadOutlined,
      PlusOutlined,
      UploadDragger,
    },
    setup() {
      const imgToken = 'Bearer ' + getToken() || ''; //上传文件token参数
      const step = ref(0); //excel导入步骤
      const click_event = ref(false); //事件防抖动
      const fileList = ref([]);
      const errorFileUrl = ref('')

      //下载商品导入模板
      const downloadModal = async () => {
        if (click_event.value) return;
        click_event.value = true;
        await downloadPlatform({ fileName: '商品导入模板' });
        step.value = 1;
        click_event.value = false;
      };

      //上传文件
      function handleChange(res) {
        errorFileUrl.value = ''
        if (res.file && res.file.response) {
          if (res.file.response.state == 200) {
            step.value = 2;
            fileList.value = [];
          } else if (res.file.response.state == 267) {
            errorFileUrl.value = res.file.response.data
            fileList.value = [];
          }else {
            failTip('上传文件失败');
          }
        }
      }

      //下一步
      function nextStep(setp) {
        errorFileUrl.value = ''
        step.value = setp;
      }

      return {
        apiUrl,
        imgToken,
        step,
        click_event,
        downloadModal,
        nextStep,
        fileList,
        handleChange,
        errorFileUrl
      };
    },
  });
</script>
<style lang="less" scoped>
  .plateform_excel {
    width: 100%;
    padding: 0 20px;

    .step_list {
      padding: 40px 200px;
    }

    .btn_list {
      .download_modal {
        span {
          &:nth-child(1) {
            position: relative;
            top: 3px;
            margin-right: 5px;
          }
        }
      }

      .download_modal,
      .next_btn {
        display: inline-block;
        height: 32px;
        margin-top: 20px;
        margin-bottom: 20px;
        padding: 4px 15px;
        transition: all 0.3s ease;
        border-radius: 4px;
        background-color: @primary-color;
        color: #fff;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;

        &:hover {
          background-color: @primary-color;
        }
      }
    }

    .finish_title {
      margin-top: 20px;
      font-size: 16px;
      font-weight: 700;
      text-align: center;
    }

    .finish_content {
      margin-top: 30px;
      color: #666;
      text-align: center;
    }
  }
</style>
