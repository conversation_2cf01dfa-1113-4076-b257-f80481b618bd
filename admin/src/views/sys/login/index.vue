<template>
  <div
    class="full_screen"
    :style="{
      backgroundImage: 'url(' + default_login_img.admin_login_bg + ')',
      backgroundSize: 'cover',
    }"
  >
    <div class="left_bg">
      <img class="left_bg_img" :src="default_login_img.admin_login_left_bg" alt="" />
      <img class="login_logo" :src="default_login_img.main_admin_top_logo" />
      <div class="right_bg">
        <span class="login_title">平台管理中心</span>
        <div class="formlogin">
          <Form
            layout="horizontal"
            ref="formRef"
            :model="formState"
            :rules="loginRules"
            @change="clearValidate('username')"
          >
            <FormItem class="!mt-8" name="username">
              <Input
                v-model:value="formState.username"
                placeholder="请输入用户名"
                :maxlength="20"
                class="!w-300px !h-40px border"
              >
                <template #prefix>
                  <img :src="inputIcons.username" alt="1" />
                </template>
              </Input>
            </FormItem>
            <FormItem class="mt-10" name="password">
              <Input
                v-model:value="formState.password"
                :maxlength="20"
                type="password"
                placeholder="请输入密码"
                @change="clearValidate('password')"
                class="!w-300px !h-40px border"
              >
                <template #prefix>
                  <img :src="inputIcons.loginPwd" alt="1" />
                </template>
              </Input>
            </FormItem>
            <FormItem ref="verifyCode" name="verifyCode" class="block mt-5">
              <Input
                placeholder="请输入验证码"
                v-model:value="formState.verifyCode"
                type="text"
                :maxlength="4"
                @change="clearValidate('verifyCode')"
                class="!w-300px !h-40px border !pr-0"
              >
                <template #prefix>
                  <img :src="inputIcons.imageCode" alt="1" />
                </template>
                <template #suffix>
                  <img
                    :src="capchaImg"
                    alt="1"
                    class="h-full verification_code"
                    @click="getCapImage"
                  />
                </template>
              </Input>
            </FormItem>
          </Form>

          <div class="sld_login_btn_wrap">
            <Button
              class="sld_login_btn flex_row_center_center"
              @click="loginNow"
              :loading="loginLoading"
            >
              立即登录
            </Button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { reactive, onMounted, ref, onUnmounted } from 'vue';
  import { getImagePath } from '/@/utils';
  import { getCaptchaApi } from '@/api/sys/login';
  import { FormItem, Form, Input, Button } from 'ant-design-vue';
  import { UserOutlined, UnlockOutlined, SafetyOutlined } from '@ant-design/icons-vue';
  import { accountLogin } from '/@/api/sys/login';
  import { useEventListener } from '/@/hooks/event/useEventListener';
  import { useUserStore } from '/@/store/modules/user';
  import { useImageStore } from '@/store/modules/images';
  import { failTip, setDocumentIcon } from '/@/utils/utils';

  const formRef = ref();
  const loginLoading = ref(false);
  const userStore = useUserStore();
  const imageStore = useImageStore();

  //校验图形验证码
  function checkImgCode(value) {
    const errState = { checkState: 0, stateValue: '' };
    if (!value.trim()) {
      errState.checkState = 1;
      errState.stateValue = '请输入图形验证码';
    } else if (value.length < 4 || !/^[0-9a-zA-Z]+$/.test(value)) {
      errState.checkState = 2;
      errState.stateValue = '请输入正确的图形验证码';
    }
    if (errState.checkState > 0) {
      return Promise.reject(errState.stateValue);
    } else {
      return Promise.resolve();
    }
  }

  const default_login_img = reactive({
    admin_login_bg: getImagePath('images/sld_login_bg.png'), //登录背景
    admin_login_left_bg: getImagePath('images/sld_login_left.png'), //登录左侧图片
    main_admin_top_logo: getImagePath('images/sld_java_logo.png'), //登录页logo
  });

  const inputIcons = {
    username: getImagePath('images/sld_login_username.png'),
    loginPwd: getImagePath('images/sld_login_pwd.png'),
    imageCode: getImagePath('images/sld_login_code.png'),
    phone: getImagePath('images/sld_login_phone.png'),
  };

  const formState = reactive({
    username: '',
    password: '',
    verifyCode: '',
  });

  const loginRules = {
    username: [{ required: true, trigger: 'blur', message: '用户名不能为空' }],
    password: [{ required: true, message: '密码不能为空', trigger: 'blur' }],
    verifyCode: [{ required: true, trigger: 'blur', validator: (r, v) => checkImgCode(v) }],
  };

  const capchaImg = ref('');
  const capchaKey = ref('');

  const getLogo = async () => {
    await imageStore.fetchSetting();
    const res = imageStore.getImagesAll;
    res.map((item) => {
      if (item.name == 'background_browser_icon') {
        setDocumentIcon(item.imageUrl ? item.imageUrl : null);
      }
      if (item.imageUrl) {
        default_login_img[item.name] = item.imageUrl;
      }
    });
  };

  const getCapImage = async () => {
    const cap = await getCaptchaApi();
    if (cap?.data) {
      capchaImg.value = 'data:image/png;base64,' + cap?.['data']['captcha'];
      capchaKey.value = cap?.['data']['key'];
    }
  };

  const clearValidate = (name) => {
    formRef.value.clearValidate(name);
  };

  const handleLoginData = (res) => {
    let cur_top_nav = []; //顶部菜单
    let cur_top_nav_info = []; //顶部菜单详细信息
    if (res.data.resourceList.length > 0) {
      localStorage.setItem('sld_menu_data', JSON.stringify(res.data.resourceList));
      let sld_all_routes = []; //所有页面的路由
      res.data.resourceList.map((item) => {
        item.children.map((child) => {
          sld_all_routes.push(child.frontPath);
        });
      });
      localStorage.setItem('sld_all_routes', JSON.stringify(sld_all_routes));
      let tmp_data = res.data.resourceList;
      for (let i in tmp_data) {
        let split_first = tmp_data[i].frontPath.split('/');
        let target = split_first[1].split('_')[0];
        if (cur_top_nav.indexOf(target) == -1) {
          let target_data = {};
          target_data.top_nav = target;
          target_data.path = tmp_data[i].children[0].frontPath;
          if (target == 'sysset') {
            target_data.name = '系统配置';
            target_data.icon = 'xitong1';
          } else if (target == 'manage') {
            target_data.name = '商城管理';
            target_data.icon = 'shangchengguanli2';
          } else if (target == 'decorate') {
            target_data.name = '装修';
            target_data.icon = 'ziyuan114';
          } else if (target == 'marketing') {
            target_data.name = '应用中心';
            target_data.icon = 'yunying';
          } else if (target == 'member') {
            target_data.name = '会员中心';
            target_data.icon = 'huiyuanzhongxin';
          } else if (target == 'statistics') {
            target_data.name = '统计中心';
            target_data.icon = 'tongjizhongxin';
          } else if (target == 'im') {
            target_data.name = '客服';
            target_data.icon = 'kefu1';
          } else if (target == 'operation') {
            target_data.name = '运营';
            target_data.icon = 'yunying1';
          }
          cur_top_nav.push(target);
          cur_top_nav_info.push(target_data);
        }
      }

      localStorage.setItem('cur_top_nav', JSON.stringify(cur_top_nav));
      localStorage.setItem('cur_top_nav_info', JSON.stringify(cur_top_nav_info));
    }
    setTimeout(() => {
      loginUpdateInfo(res.data);
    });
  };

  const loginUpdateInfo = (data) => {
    userStore.loginUpdateInfo(data);
  };

  const loginNow = async () => {
    loginLoading.value = true;
    try {
      await formRef.value.validate();
      const res = await accountLogin({
        ...formState,
        verifyKey: capchaKey.value,
      });
      if (res.state == 200 || res.state == 267) {
        handleLoginData(res);
      } else {
        loginLoading.value = false;
        getCapImage();
        failTip(res.msg);
      }
    } catch (error) {
      loginLoading.value = false;
    }
  };

  const keyEvent = (e) => {
    if (e.keyCode == 13) {
      loginNow();
    }
  };

  const { removeEvent } = useEventListener({
    name: 'keydown',
    listener: keyEvent,
  });

  onMounted(() => {
    if (localStorage.getItem('not_show_message') == '1') {
      localStorage.setItem('not_show_message', '2');
    }
    localStorage.removeItem('admin_info')
    getLogo();
    getCapImage();
  });

  onUnmounted(removeEvent);
</script>
<style lang="less">
.sld_login_btn {
  span.anticon:not( .app-iconify, .anticon-vertical-align-top, .anticon-bell, .anticon-left, .anticon-right, .anticon-close ){
           vertical-align: -3px !important;
         }

}
</style>
<style lang="less" scoped>
  .main {
    width: 368px;
    margin: 0 auto;

    @media screen and (max-width: 767px) {
      width: 95%;
    }

    .icon {
      margin-left: 16px;
      transition: color 0.3s;
      color: rgb(0 0 0 / 20%);
      font-size: 24px;
      vertical-align: middle;
      cursor: pointer;

      &:hover {
        color: @primary-color;
      }
    }

    .other {
      margin-top: 24px;
      line-height: 22px;
      text-align: left;

      .register {
        float: right;
      }
    }
  }

  .full_screen {
    display: flex;
    flex: 1;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 100vw;
    height: 100vh;

    .left_bg {
      position: relative;
      width: 1000px;
      min-width: 1000px;
      height: 672px;

      .left_bg_img {
        width: 100%;
        height: 100%;
      }

      .login_logo {
        position: absolute;
        z-index: 2;
        top: 87px;
        left: 90px;
        max-width: 200px;
        max-height: 50px;
      }
    }

    .right_bg {
      display: flex;
      position: absolute;
      z-index: 2;
      right: 80px;
      bottom: 130px;
      flex-direction: column;
      align-items: center;
      justify-content: start;
      width: 410px;
      height: 420px;
      margin-left: -10px;
      padding: 55px 45px 22px;
      border-radius: 0 6px 6px 0;
      background: #fff;

      .login_title {
        display: flex;
        flex-direction: row;
        align-items: center;
        color: #fc7b0c;
        font-family: MicrosoftYaHei-Bold;
        font-size: 30px;
        font-weight: bold;
        letter-spacing: 2px;
      }

      .sld_login_btn_wrap {
        display: flex;
        position: absolute;
        flex-direction: row;
        justify-content: center;
        margin-top: 45px;

        .sld_login_btn {
          width: 300px;
          height: 40px;
          border-radius: 5px;
          border-color: transparent !important;
          background: #ff7505;
          color: #fff;
          font-size: 19px;
          font-weight: bold;
          letter-spacing: 1px;
          cursor: pointer;
         
        }
      }

      .info_wrap {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        width: 340px;
      }

      .autoPwd {
        color: #999;
        font-size: 12px;
      }
    }
  }

  .formlogin {
    position: relative;

    .ant-form-item-with-help{
      margin-bottom: 0 !important;
    }

    .ant-input {
      height: 40px !important;
      border: 1px solid #e5e5e5 !important;
      outline: none;
      background-color: transparent !important;
      line-height: 40px !important;
      -webkit-tap-highlight-color: rgb(255 0 0 / 0%);
    }

    .ant-input-affix-wrapper .ant-input:not(:first-child) {
      padding-left: 40px !important;
      color: #333 !important;
      font-size: 14px !important;
    }

    .ant-input:focus {
      border-color: transparent;
      outline: 0;
      box-shadow: 0 0 0 0 !important;
    }

    .ant-input-affix-wrapper .ant-input-suffix {
      right: 1px !important;
    }

    .verification_code {
      width: 96px;
      height: 40px;
      margin-right: 0;
      border-radius: 0 3px 3px 0;
    }
  }
</style>
