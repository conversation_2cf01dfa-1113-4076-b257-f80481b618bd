<template>
  <div class="adv_26_wrap flex_row_start_center">
    <MaskEdit :config="adv26ImgConfig">
      <ImgWrap
        :width="190"
        :height="260"
        placeholder="icon"
        mode="contain"
        :src="img_data.imgUrl"
      ></ImgWrap>
    </MaskEdit>
    <MaskEdit
      :config="
        formGoodsConfig(
          $sldComLanguage('商品选择(至少选择6个商品)'),
          'right_goods',
          adv26GoodsContent,
        )
      "
    >
      <div class="adv_26_wrap_right" v-if="loading">
        <Carousel :dots="false" arrows>
          <template #prevArrow>
            <div class="custom-slick-arrow" style="left: 0">
              <div class="carousel__arrow carousel__arrow_left">
                <LeftOutlined style="color: #fff; font-size: 13px" />
              </div>
            </div>
          </template>
          <template #nextArrow>
            <div class="custom-slick-arrow" style="right: 0">
              <div class="carousel__arrow carousel__arrow_right">
                <RightOutlined style="color: #fff; font-size: 13px" />
              </div>
            </div>
          </template>
          <div
            class="adv_26_wrap_right_goods"
            v-for="(item, index) in type ? data.goods_data : 1"
            :key="index"
          >
            <div class="adv_26_wrap_right_goods_box">
              <div
                class="adv_26_wrap_right_goods_item"
                :class="{ adv_26_wrap_right_item: !type }"
                v-for="(it, ind) in type ? item : 5"
                :key="'ind_' + ind"
              >
                <div class="flex_column_center_center" v-if="type">
                  <img :src="it.mainImage" alt="" class="adv_26_wrap_img" />
                </div>
                <div style="margin-top: 18px" v-if="type">
                  <div class="goods_n normal">{{ it.goodsName }}</div>
                  <div class="flex_row_center_center" style="margin-top: 15px">
                    <span class="price_n"
                      ><span>￥</span>{{ getPartNumber(it.goodsPrice, 'int')
                      }}{{ getPartNumber(it.goodsPrice, 'decimal') }}</span
                    >
                  </div>
                </div>
                <div class="h-full w-full flex_row_center_center" v-if="!type">
                  <img src="@/assets/images/pc_diy_img/web_goods.png" />
                </div>
              </div>
            </div>
          </div>
        </Carousel>
      </div>
    </MaskEdit>
  </div>
</template>

<script setup>
  import { provide, reactive, ref, toRefs, unref, getCurrentInstance, onMounted } from 'vue';
  import {
    createTplContext,
    dfEmit,
    dfProps,
    formSingleImgConfig,
    formGoodsConfig,
  } from './actions/edit_tpl';
  import { RightOutlined, LeftOutlined } from '@ant-design/icons-vue';
  import ImgWrap from './components/ImgWrap.vue';
  import { getPartNumber } from '@/utils/utils';
  import MaskEdit from './components/MaskEdit.vue';
  import { Carousel } from 'ant-design-vue';

  const vm = getCurrentInstance();
  const { $sldComLanguage } = vm?.appContext.config.globalProperties;
  const props = defineProps(dfProps);
  const emit = defineEmits(dfEmit);
  let { img_data, data } = toRefs(props.tpl_info);
  const loading = ref(true);
  const type = ref(false);
  //广告图设置
  const adv26ImgConfig = formSingleImgConfig('左侧图片设置', 'left_img', {
    content:{
      type: 'adv_26',
      width: '190',
      height: '260',
      data: { ...unref(img_data) },
    },
    client:props.client,
  });

  const adv26GoodsContent = ref({
    selectedRow: [],
    selectedRowKey: unref(data).goods_ids,
    extra: {
      min_num: 6,
      max_num: 50,
    },
    client:props.client,
  });

  //
  const getList = () => {
    let goods_arr = [];
    if (data.value.goods_ids.length > 0) {
      type.value = true;
      data.value.goods_data.forEach((item) => {
        goods_arr.push(...item);
      });
      adv26GoodsContent.value.selectedRow = goods_arr;
    }
  };

  onMounted(() => {
    getList();
  });

  const editConfirmHandler = (values, { modal, position }) => {
    switch (position) {
      case 'left_img': {
        if (values[0].width) {
          delete values[0].width;
          delete values[0].height;
        }
        img_data.value = values[0];
        adv26ImgConfig.content.data = values[0];
        break;
      }
      case 'right_goods': {
        loading.value = false;
        adv26GoodsContent.value.selectedRow = values[0];
        adv26GoodsContent.value.selectedRowKey = values[1];
        let obj = [];
        let startX = 0;
        let dataList = values[0];
        while (startX <= dataList?.length) {
          let tmpList = dataList.slice(startX, (startX += 5));
          if (tmpList.length) {
            obj.push(tmpList);
          }
        }
        data.value.goods_ids = values[1];
        data.value.goods_data = obj;
        type.value = true;
        setTimeout(() => {
          loading.value = true;
        });
        break;
      }
    }
    emit('save_tpl_data', props.tpl_info,props.client);
  };

  createTplContext(props.mode, editConfirmHandler);
</script>

<style lang="scss">
  .adv_26_wrap {
    width: 1210px;
    height: 260px;
    margin: 10px auto;

    .adv_26_wrap_right {
      width: 1020px;
      height: 260px;
      overflow: hidden;

      .slick-next,
      .slick-arrow {
        z-index: 99;
        width: 26px;
        height: 35px;
      }

      .carousel__arrow {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 26px;
        height: 35px;
        border-radius: 0;
        background-color: rgb(0 0 0 / 15%);
        font-size: 13px !important;
        line-height: 35px;
      }

      .carousel__arrow_left {
        padding-right: 5px;
        border-top-right-radius: 18px;
        border-bottom-right-radius: 18px;
      }

      .carousel__arrow_right {
        padding-left: 5px;
        border-top-left-radius: 18px;
        border-bottom-left-radius: 18px;
      }

      .adv_26_wrap_right_goods {
        .adv_26_wrap_right_goods_box {
          display: flex;
          align-items: center;
        }

        .adv_26_wrap_right_goods_item {
          display: flex;
          position: relative;
          flex-direction: column;
          align-items: center;
          width: 204px;
          height: 260px;
          padding-top: 30px;
          background: #fff;

          &::after {
            content: '';
            display: block;
            position: absolute;
            top: 50%;
            right: 0;
            width: 1px;
            height: 200px;
            transform: translateY(-50%);
            background: -webkit-gradient(
              linear,
              left top,
              left bottom,
              from(#fff0),
              color-stop(#e3e3e3),
              to(#fff0)
            );
            background: linear-gradient(180deg, #fff0, #e3e3e3, #fff0);
          }

          &:nth-child(5n) {
            &::after {
              display: none;
            }
          }

          .adv_26_wrap_img {
            width: 140px;
            height: 140px;
            transition: opacity ease 0.5s;
            background: #eee !important;
            font-weight: 300;
            object-fit: cover;
          }

          .goods_n {
            overflow: hidden;
            transition: color ease 0.5s;
            color: #2d2d2d;
            font-weight: 400;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .goods_n.normal {
            width: 160px;
            font-family: 'Microsoft YaHei';
            font-size: 12px;
            font-weight: 400;
            line-height: 15px;
            text-align: center;
          }

          .price_n {
            color: #e2231a;
            font-family: 'Microsoft YaHei';
            font-size: 16px;
            font-weight: 400;
            font-weight: bold;
            line-height: 15px;

            span {
              &:first-child {
                font-size: 12px;
              }
            }
          }
        }

        .adv_26_wrap_right_item {
          padding-top: 0 !important;
          background: #f6f6f6;
        }
      }
    }
  }
</style>
