<template>
  <div class="adv_05_wrap">
    <MaskEdit :config="adv05TopTitleConfig">
      <section class="flex_row_between_center floor_title">
        <div class="flex_row_start_center">
          <div class="main_title">{{ title_info.title.initialValue }}</div>
          <div class="sub_title">{{ title_info.sub_title.initialValue }}</div>
        </div>

        <div class="right_action"> 查看更多>> </div>
      </section>
    </MaskEdit>

    <section class="floor_content flex_row_center_start">
      <div class="floor_left relative">
        <MaskEdit :config="adv05LeftImgConfig">
          <div class="floor_bg_img">
            <ImgWrap :width="298" :height="482" :src="left.data.imgUrl" mode="cover"></ImgWrap>
          </div>
        </MaskEdit>
        <div class="floor_words flex_column_start_center">
          <MaskEdit
            edit-btn="icon"
            :config="adv05catTitleContent()"
            >
            <div class="floor_words_top_title flex_row_center_center">
              <em></em>
              <span>{{ left.cat_data.title_info.title_name || flr_default.words_top_title }}</span>
              <em></em>
            </div>
          </MaskEdit>
          <div class="mt-[10px]"></div>
          <MaskEdit :config="adv05LeftBottomConfig()">
            <ul class="cat_data_wrap">
              <li v-for="(item, index) in left.cat_data.cat_datas" :key="index">{{
                item.categoryName
              }}</li>
            </ul>
          </MaskEdit>
        </div>
      </div>
      <div class="floor_border_top flex_row_end_start">
        <div class="floor_middle flex_row_center_center">
          <MaskEdit :config="formGoodsConfig('商品选择5个', 'center', adv05CenterContent)">
            <div class="flex flex-row flex-wrap" style="width: 662px">
              <div
                class="floor_goods_item"
                v-for="(item, index) in center.data.goods_data"
                :key="index"
                :class="{ big_item: index == 0, small_item: index > 0 }"
              >
                <Goods
                  :goods-info="item"
                  :img-info="{ width: 162, height: 162, cover: 'cover' }"
                  :mode="index > 0 ? 'vertical' : 'horizonal'"
                  :gap="index > 0 ? 10 : 34"
                  :name-style="index > 0 ? smallGoodsItemStyle.name : bigGoodsItemStyle.name"
                ></Goods>
              </div>
            </div>
          </MaskEdit>
        </div>

        <div class="floor_right">
          <MaskEdit
            edit-btn="icon"
            :config="adv05RightTitleContent()"
          >
            <div class="floor_right_new_top_title flex_row_center_center">
              <em></em>
              <span style="color: rgb(252 88 99)">{{
                right.title_info.title_name || flr_default.words_top_title
              }}</span>
              <em></em>
            </div>
          </MaskEdit>

          <MaskEdit :config="formGoodsConfig('商品选择4个', 'right_goods', adv05RightGoodsContent)">
            <div
              class="floor_goods_right_item"
              v-for="(item, index) in right.data.goods_data"
              :key="index"
            >
              <Goods
                :goods-info="item"
                :img-info="{ width: 90, height: 90 }"
                mode="horizonal"
                :gap="10"
                :name-style="rightGoodsItemStyle.name"
                price-style="font-size:17px;margin-left:3px;"
              ></Goods>
            </div>
          </MaskEdit>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
  import { provide, reactive, toRefs, unref ,getCurrentInstance} from 'vue';
  import {
    dfProps,
    dfEmit,
    formTitleConfig,
    formGoodsConfig,
    formSingleImgConfig,
    formTitleLinkConfig,
    createTplContext,
  } from './actions/edit_tpl';
  import Goods from './components/GoodsWrap.vue';
  import ImgWrap from './components/ImgWrap.vue';
  import MaskEdit from './components/MaskEdit.vue';
  import lodash from 'lodash-es';
  const flr_default = {
    words_top_title: '添加标题',
    cat_data: Array(9).fill({ categoryName: '添加分类' }),
    middle_goods_list: Array(5).fill({ goodsName: '商品名称', goodsPrice: 0, goodsImage: '' }),
    right_goods_list: Array(4).fill({ goodsName: '商品名称', goodsPrice: 0, goodsImage: '' }),
  };

  const props = defineProps(dfProps);
  const emit = defineEmits(dfEmit);
  const vm = getCurrentInstance();

  const { left, right, center, title_info } = toRefs(props.tpl_info);
  //补充栏位start
  if (!unref(left).cat_data.cat_datas.length) {
    unref(left).cat_data.cat_datas = lodash.cloneDeep(flr_default.cat_data);
  }
  if (!unref(center).data.goods_data.length) {
    unref(center).data.goods_data = lodash.cloneDeep(flr_default.middle_goods_list);
  }
  if (!unref(right).data.goods_data.length) {
    unref(right).data.goods_data = lodash.cloneDeep(flr_default.right_goods_list);
  }

  //补充栏位end

  //样式start
  const bigGoodsItemStyle = {
    name: {
      height: '56px',
      marginTop: '10px',
      marginBottom: '5px',
    },
    price: '',
  };
  const smallGoodsItemStyle = {
    name: {
      marginBottom: '5px',
      whiteSpace: 'nowrap',
    },
  };
  const rightGoodsItemStyle = {
    name: {
      marginBottom: '5px',
      height: ' 40px',
      marginTop: '10px',
      color: '#000',
      fontSize: '12px',
    },
    price: {},
  };
  //样式end

  //中间商品编辑配置
  const adv05CenterContent = {
    selectedRow: unref(center).data.goods_ids.length ? unref(center).data.goods_data : [],
    selectedRowKey: unref(center).data.goods_ids,
    extra: {
      total_num: 5,
    },
    client:props.client
  };

  // 右边的商品编辑配置
  const adv05RightGoodsContent = {
    selectedRow: unref(right).data.goods_ids.length ? unref(right).data.goods_data : [],
    selectedRowKey: unref(right).data.goods_ids,
    extra: {
      total_num: 4,
    },
    client:props.client
  };
  // // 顶部的title配置
  const adv05TopTitleConfig = formTitleLinkConfig('标题设置', 'top_title', {
    content: {
      ...unref(title_info),
    },
    client:props.client,
  });

   //分类标题栏title配置
   const adv05catTitleContent = ()=> {
    let obj = [
      {
        type: 'input',
        label: '标题名称',
        name: 'title',
        extra: '最多8个字',
        maxLength: 8,
        rules: [{ required: true }],
        placeholder: '请输入标题名称',
        initialValue: unref(left).cat_data.title_info.title_name,
      },
    ]
    return formTitleConfig('左侧分类信息标题', 'cat_title', {content:obj,client:props.client})
  }

  //右侧题栏title配置
  const adv05RightTitleContent = ()=> {
    let obj = [
      {
        type: 'input',
        label: '标题名称',
        name: 'title',
        extra: '最多8个字',
        maxLength: 8,
        placeholder: '请输入标题名称',
        rules: [{ required: true }],
        initialValue: unref(right).title_info.title_name,
      },
    ]
    return formTitleConfig('右侧标题设置', 'right_title', {content:obj,client:props.client})
  }


  //左侧图片设置
  const adv05LeftImgConfig = formSingleImgConfig('左侧图片设置', 'left_img', {
    content:{
      ...unref(left),
      data: unref(left).data,
    },
    client:props.client,
  });

  //左侧分类选择
  const adv05LeftBottomConfig = ()=> {
    let obj = {
      modalTitle: '左侧分类选择9个',
      position: 'left_bottom_title',
      component: 'SldSelCatMore',
      modalWidth: 1000,
      extra: { max_num: 9 },
      selectedRows: unref(left).cat_data.cat_datas,
      selectedRowKeys: unref(left).cat_data.cat_ids,
      client:props.client,
    }
    return obj
  }

  const editConfirmHandler = (values, { modal, position }) => {
    switch (position) {
      case 'center': {
        unref(center).data.goods_data = values[0];
        unref(center).data.goods_ids = values[1];
        vm?.proxy?.$forceUpdate();
        break;
      }

      case 'right_goods': {
        unref(right).data.goods_data = values[0];
        unref(right).data.goods_ids = values[1];
        vm?.proxy?.$forceUpdate();
        break;
      }

      case 'right_title': {
        unref(right).title_info.title_name = values[0].title;
        vm?.proxy?.$forceUpdate();
        break;
      }

      case 'cat_title': {
        unref(left).cat_data.title_info.title_name = values[0].title;
        vm?.proxy?.$forceUpdate();
        break;
      }

      case 'left_img': {
        unref(left).data = values[0];
        vm?.proxy?.$forceUpdate();
        break;
      }

      case 'top_title': {
        unref(title_info).title.initialValue = values[0].title;
        unref(title_info).sub_title.initialValue = values[0].sub_title;
        unref(title_info).link_type = values[0].link_type;
        unref(title_info).link_value = values[0].link_value;
        unref(title_info).info = values[0].info;
        vm?.proxy?.$forceUpdate();
        break;
      }

      case 'left_bottom_title': {
        left.value.cat_data.cat_datas = values[0].data;
        left.value.cat_data.cat_ids = values[1];
        vm?.proxy?.$forceUpdate();
        break;
      }
    }
    emit('save_tpl_data', props.tpl_info,props.client);
  };

  createTplContext(props.mode, editConfirmHandler);
</script>

<style lang="scss" scoped>
  .adv_05_wrap {
    width: 1210px;
    margin: 0 auto;

    .floor_title {
      height: 45px;

      .main_title {
        color: #3d3d3d;
        font-size: 20px;
        font-weight: bold;
      }

      .sub_title {
        margin: 0 0 0 10px;
        color: #a4a4a4;
        font-size: 16px;
        font-style: italic;
      }

      .right_action {
        margin: 0 12px 0 0;
        color: #333;
        font-size: 14px;
      }
    }

    .floor_content {
      background-color: #fff;

      .floor_left {
        .floor_img {
          //
        }

        .floor_words {
          position: absolute;
          z-index: 41;
          bottom: 20px;
          left: 20px;
          width: 260px;
          height: 160px;
          padding: 10px 30px;
          padding-bottom: 0;
          background: #fff;

          .floor_words_top_title {
            height: 30px;

            span {
              margin: 0 9px;
              color: #101010;
              font-size: 14px;
              font-weight: bold;
            }

            em {
              display: inline-block;
              width: 39px;
              height: 1px;
              background: #d4d4d4;
            }
          }

          .cat_data_wrap {
            width: 200px;
            height: 86px;
            margin-bottom: 0;

            li {
              width: 60px;
              margin: 0 3px;
              margin-bottom: 12px;
              float: left;
              color: #6d6d6d;
              font-size: 12px;
              font-weight: 400;
            }
          }
        }
      }

      .floor_border_top {
        border-top: 2px solid #fc5863;
      }

      .floor_middle {
        background-color: #fff;

        .floor_goods_item {
          border-width: 0 1px 1px 0;
          border-style: solid;
          border-color: #e7e7e7;

          &.big_item {
            display: flex;
            align-items: center;
            width: 440px;
            height: 240px;
            padding: 0 30px;
          }

          &.small_item {
            width: 220px;
            padding: 10px;
          }
        }
      }

      .floor_right {
        position: relative;
        width: 250px;
        height: 480px;
        padding: 5px 10px 0;

        .floor_right_new_top_title {
          position: relative;
          height: 30px;
          margin: 0 0 6px;
          overflow: hidden;
          line-height: 30px;
          text-align: center;

          em {
            display: inline-block;
            width: 39px;
            height: 1.5px;
            background: #fc585a;
          }

          span {
            margin: 0 9px;
            color: #fc585a;
            font-size: 14px;
            font-weight: bold;
          }
        }

        .floor_goods_right_item {
          padding: 9px 0;
          border-width: 0 0 1px;
          border-style: solid;
          border-color: #e7e7e7;
        }
      }
    }
  }
</style>
./actions/edit_tpl
