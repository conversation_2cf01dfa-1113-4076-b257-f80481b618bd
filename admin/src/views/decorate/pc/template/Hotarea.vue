<template>
  <div class="hot_are_wrap mt-5 h-full">
    <div class="relative h-full" :class="{ display_mode: mode == 'display' }">
      <MaskEdit :style="{ height: img_info.imgUrl ? 'auto' : '100%' }">
        <template #button>
          <div class="edit_tag_diy">
            <template v-if="img_info.imgUrl">
              <Popconfirm
                @confirm="handleImg('delete')"
                title="确认删除图片？"
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                placement="bottom"
              >
                <Button type="primary" class="ml-3">删除图片</Button>
              </Popconfirm>
            </template>
            <Button type="primary" @click="handleImg('add')" v-else>添加图片</Button>
            <Button type="primary" class="ml-3" @click="handleImg('style')">设置样式</Button>
            <Button type="primary" class="ml-3" @click="openModal">设置热区</Button>
          </div>
        </template>
        <div class="img_placeholder flex_row_center_center" :style="dynaStyle('outer')">
          <div :style="dynaStyle('inner')" class="relative overflow-hidden">
            <img
              :src="img_info.imgUrl"
              class="display_img"
              v-show="img_info.imgUrl"
              :style="getImgStyle.domStyle"
            />
            <div
              class="area_item"
              v-for="(item, index) in area_list"
              :key="index"
              :style="getAreaStyle(item)"
              ><span>热区{{ index + 1 }}</span></div
            >
          </div>
        </div>
        <div v-show="!img_info.imgUrl" class="w-full display_height flex_row_center_center">
          <div class="diy_img_placeholder flex_row_center_center">
            <span> 此处添加图片【宽高不限】 </span>
          </div>
        </div>
      </MaskEdit>
    </div>
    <HotAreaModal
      ref="HotAreaModalRef"
      :client="props.client?props.client:'pc'"
      :area-data-list="area_list"
      :img-info="img_info"
      @confirm="modalConfirm"
    />
    <SldMaterialImgs ref="sldMaterialImgs" @confirmMaterial="selectedMaterialImg" />
    <StyleSetModal
      :visible="modalVisible"
      @cancel="modalVisible = false"
      @confirm="styleSetConfirm"
      :style="style"
    ></StyleSetModal>
  </div>
</template>

<script setup>
  import { computed, ref, toRef, unref } from 'vue';
  import { createTplContext, dfEmit, dfProps } from './actions/edit_tpl';
  import MaskEdit from './components/MaskEdit.vue';
  import { HotAreaModal } from '@/components/HotArea';
  import { Button, message, Popconfirm } from 'ant-design-vue';
  import SldMaterialImgs from '/@/components/SldMaterialFiles/sldMaterialImgs.vue';
  import { resolveImageBindId } from '/@/utils';
  import { StyleSetModal } from '@/components/HotArea';
  import { getCurrentInstance } from 'vue';

  const props = defineProps(dfProps);
  const emit = defineEmits(dfEmit);

  const img_info = toRef(props.tpl_info, 'img_info');
  const area_list = toRef(props.tpl_info, 'area_list');
  const style = toRef(props.tpl_info, 'style');

  const modalVisible = ref(false);

  const getImgStyle = computed(() => {
    let { width, height } = unref(img_info);
    let { padding } = unref(style);

    let calcWidth = (width > 1210 ? 1210 : width) - (padding.left + padding.right);
    let calcHeight = calcWidth * (height / width);

    let domStyle = { width: calcWidth + 'px', height: calcHeight + 'px' };

    return {
      domStyle,
      calcWidth,
      calcHeight,
    };
  });

  const vm = getCurrentInstance();

  const sldMaterialImgs = ref();

  createTplContext(props.mode, () => {});

  const HotAreaModalRef = ref();

  const openModal = () => {
    if (!unref(img_info).imgUrl) {
      message.warn('请先设置图片');
      return;
    }

    unref(HotAreaModalRef).open(true);
  };

  const getAreaStyle = (item) => {
    let { areaWidth_ratio, areaHeight_ratio, startX_ratio, startY_ratio } = item;
    let { calcWidth, calcHeight } = unref(getImgStyle);

    let width = calcWidth * areaWidth_ratio + 'px';
    let height = calcHeight * areaHeight_ratio + 'px';
    let left = calcWidth * startX_ratio + 'px';
    let top = calcHeight * startY_ratio + 'px';

    return {
      width,
      height,
      top,
      left,
    };
  };

  const handleImg = (type) => {
    switch (type) {
      case 'add': {
        let selectedData = { data: [], ids: [] };
        if (unref(img_info).imgPath) {
          let fileData = {
            bindId: resolveImageBindId(unref(img_info).imgPath),
            width: unref(img_info).width,
            height: unref(img_info).height,
            fileUrl: unref(img_info).imgUrl,
            filePath: unref(img_info).imgPath,
          };
          selectedData = {
            data: [fileData],
            ids: [fileData.bindId],
          };
          unref(sldMaterialImgs).setMaterialModal(true, selectedData);
        } else {
          unref(sldMaterialImgs).setMaterialModal(true, selectedData);
        }
        break;
      }

      case 'delete': {
        img_info.value = {
          width: 0,
          height: 0,
          imgUrl: '',
          imgPath: '',
        };
        area_list.value.splice(0, area_list.value.length);
        vm?.proxy?.$forceUpdate();
        break;
      }

      case 'style': {
        if (!unref(img_info).imgUrl) {
          message.warn('请先设置图片');
          return;
        }
        modalVisible.value = true;
      }
    }
  };

  const modalConfirm = () => {
    emitData();
    vm?.proxy?.$forceUpdate();
  };

  const selectedMaterialImg = ({ data }) => {
    let [img] = data;
    img_info.value = {
      imgUrl: img.fileUrl,
      imgPath: img.filePath,
      width: img.width,
      height: img.height,
    };
    vm?.proxy?.$forceUpdate();
    emitData();
  };

  const dynaStyle = (pos) => {
    switch (pos) {
      case 'outer': {
        let { padding, bgColor } = unref(style);
        return {
          padding: `${padding.top}px ${padding.right}px ${padding.bottom}px ${padding.left}px`,
          backgroundColor: bgColor,
        };
      }
      case 'inner': {
        let { borderRadius } = unref(style);
        return {
          borderRadius: `${borderRadius.top}px ${borderRadius.top}px ${borderRadius.bottom}px  ${borderRadius.bottom}px`,
        };
      }
    }
  };

  const styleSetConfirm = (e) => {
    style.value = e;
    modalVisible.value = false;
    emitData();
  };

  const emitData = () => {
    emit('save_tpl_data', props.tpl_info,props.client);
  };
</script>

<style lang="scss" scoped>
  .hot_are_wrap {
    width: 1210px;
    margin: auto;
  }

  .img_placeholder {
    width: 100%;
    background-color: #eee;
  }

  .display_height {
    height: 417px;
    overflow-y: hidden;
    background-color: #eee;
  }

  .area_item {
    display: flex;
    position: absolute;
    align-items: center;
    justify-content: center;
    max-width: 100%;
    max-height: 100%;
    overflow: hidden;
    background: rgb(41 128 185 / 70%);
    color: #fff;
  }

  .edit_tag_diy {
    position: absolute;
    top: 10px;
    left: 10px;
  }

  .display_mode {
    height: 400px;
    overflow-y: hidden;
  }

  .diy_img_placeholder {
    color: #777;
    font-size: 15px;
  }
</style>
