<template>
  <div class="adv_12_wrap flex_row_between_center">
    <div class="item left clear_padding">
      <MaskEdit :config="adv12LeftImgConfig">
        <div class="l_img">
          <ImgWrap
            v-for="(item, index) in left.data"
            :key="index"
            :src="item.imgUrl"
            :width="left.width"
            :height="left.height"
          ></ImgWrap>
        </div>
      </MaskEdit>
    </div>

    <MaskEdit :config="adv12CenterConfig">
      <div class="item center">
        <div v-for="(item, index) in center.data" :key="index" class="center_img">
          <ImgWrap
            :src="item.imgUrl"
            :width="center.width"
            :height="center.height"
            mode="contain"
          ></ImgWrap>
        </div>
      </div>
    </MaskEdit>

    <div class="item right">
      <MaskEdit :config="adv12RightTitleConfig">
        <div class="title_wrap">
          <a class="title">{{
            right.title_info.title.initialValue
              ? right.title_info.title.initialValue
              : $sldComLanguage('添加标题')
          }}</a>
          <span>》</span>
          <a class="subtitle">{{
            right.title_info.title.initialValue
              ? right.title_info.sub_title.initialValue
              : $sldComLanguage('添加子标题')
          }}</a>
        </div>
      </MaskEdit>

      <MaskEdit :config="adv12RightTopConfig">
        <div class="img_top right_img">
          <ImgWrap
            v-for="(item, index) in right.top.data"
            :key="index"
            :src="item.imgUrl"
            :width="right.top.width"
            :height="right.top.height"
          ></ImgWrap>
        </div>
      </MaskEdit>
      <MaskEdit :config="adv12RightBottomConfig">
        <div class="img_bottom flex_row_between_center">
          <ImgWrap
            v-for="(item, index) in right.bottom.data"
            :key="index"
            :src="item.imgUrl"
            :width="right.bottom.width"
            :height="right.bottom.height"
            class="right_img"
          ></ImgWrap>
        </div>
      </MaskEdit>
    </div>
  </div>
</template>

<script setup>
  import { getCurrentInstance, provide, toRefs, unref, reactive } from 'vue';
  import {
    createTplContext,
    dfEmit,
    dfProps,
    formMoreImgConfig,
    formSingleImgConfig,
    formTitleLinkConfig,
  } from './actions/edit_tpl';
  import ImgWrap from './components/ImgWrap.vue';
  import MaskEdit from './components/MaskEdit.vue';
  const props = defineProps(dfProps);
  const emit = defineEmits(dfEmit);

  const { right, left, center } = toRefs(props.tpl_info);
  const vm = getCurrentInstance();
  const { $sldComLanguage } = vm?.appContext.config.globalProperties;

  const formMultiImgConfig = (title, position, variant,info={}) => {
    let content = {
      content:{
        show_height: variant.height,
        show_width: variant.width,
        height: variant.height,
        width: variant.width,
        data: variant.data,
      },
      client:props.client,
      ...info
    };
    return formMoreImgConfig(title, position, content, [], 800);
  };

  const adv12LeftImgConfig = formSingleImgConfig('左侧图片设置', 'left_img', {
    content:{
      width: 396,
      height: 450,
      show_width: 396,
      show_height: 450,
      data: unref(left).data[0],
    },
    client:props.client,
  });

  const adv12CenterConfig = formMultiImgConfig('中间图片设置', 'center_img', { ...unref(center) });

  const adv12RightTitleConfig = formTitleLinkConfig('右侧标题设置', 'right_title', {
    modal_tip: ['标题不能为空，最多输入5个字', '子标题不能为空，最多输入10个字'],
    extra: { title_limit: 5, sub_title_limit: 10 },
    content: {
      ...unref(right).title_info,
    },
    client:props.client,
  });

  const adv12RightTopConfig = formSingleImgConfig('右侧上部图片设置', 'right_top', {
    content:{
      width: 376,
      height: 180,
      show_width: 376,
      show_height: 180,
      data: unref(right).top.data[0],
    },
    client:props.client,
  });

  const adv12RightBottomConfig = formMultiImgConfig('右侧下部图片设置', 'right_bottom_img', {
    width: 183,
    height: 180,
    ...unref(right),
    data: unref(right).bottom.data,
  },{totalNum:2});

  const editConfirmHandler = (values, { position }) => {
    let [value] = values;
    switch (position) {
      case 'left_img': {
        unref(left).data = values;
        adv12LeftImgConfig.content.data = values[0];
        break;
      }

      case 'center_img': {
        unref(center).data = value.parent_data;
        break;
      }

      case 'right_title': {
        unref(right).title_info.title.initialValue = values[0].title;
        unref(right).title_info.sub_title.initialValue = values[0].sub_title;
        unref(right).title_info.link_type = values[0].link_type;
        unref(right).title_info.link_value = values[0].link_value;
        unref(right).title_info.info = values[0].info;
        adv12RightTitleConfig.content = { ...unref(right).title_info };
        break;
      }

      case 'right_top': {
        unref(right).top.data = values;
        adv12RightTopConfig.content.data = values[0];
        break;
      }

      case 'right_bottom_img': {
        unref(right).bottom.data = value.parent_data;
        break;
      }
    }
    emit('save_tpl_data', props.tpl_info,props.client);
  };
  createTplContext(props.mode, editConfirmHandler);
</script>

<style lang="less" scoped>
  /* adv_12-start */
  .adv_12_wrap {
    position: relative;
    width: 1210px;
    margin: 0 auto;
    clear: both;
    overflow: hidden;
  }

  .clear_padding {
    padding: 0 !important;
  }

  .l_img {
    display: block;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      z-index: 50;
      top: 0;
      left: -150px;
      width: 80px;
      height: 450px;
      overflow: hidden;
      transform: skewX(-25deg);
      background: -webkit-gradient(
        linear,
        left top,
        right top,
        color-stop(0%, rgb(255 255 255 / 0%)),
        color-stop(50%, rgb(255 255 255 / 20%)),
        color-stop(100%, rgb(255 255 255 / 0%))
      );
      background: linear-gradient(
        left,
        rgb(255 255 255 / 0%) 0,
        rgb(255 255 255 / 20%) 50%,
        rgb(255 255 255 / 0%) 100%
      );
    }

    &:hover {
      &::before {
        left: 450px;
        transition: left 0.5s;
      }
    }
  }

  .item {
    padding: 5px;
  }

  .adv_12_wrap .item a {
    display: inline-block;
    position: relative;
    width: 100%;
    height: 100%;
    background: #eee !important;
    text-align: center;
  }

  .adv_12_wrap .item a img {
    max-width: 100%;
    max-height: 100%;
  }

  .adv_12_wrap .item a span {
    display: block;
    position: absolute;
    top: 50%;
    width: 100%;
    margin-top: -12px;
    color: #777;
    font-size: 13px !important;
    line-height: 24px;
  }

  .left {
    margin-right: 10px;
  }

  .center {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;

    .center_img {
      margin: 5px;
      transition: transform 0.5s;

      &:hover {
        transform: scale(1.05);
      }
    }
  }

  .right {
    margin-left: 10px;

    .right_img {
      transition: transform 0.5s;

      &:hover {
        transform: scale(1.05);
      }
    }
  }

  .adv_12_wrap .item.right .title_wrap {
    position: relative;
    height: 50px;
    line-height: 50px;
  }

  .adv_12_wrap .item.right .title_wrap .title {
    display: inline;
    color: #666;
    font-size: 19px;
  }

  .adv_12_wrap .item.right .title_wrap .title span {
    margin: 0 18px 0 5px;
  }

  .adv_12_wrap .item.right .title_wrap .subtitle {
    display: inline;
    color: #666;
    font-size: 12px;
  }

  .adv_12_wrap .item.right .img_top {
    position: relative;
    width: 376px;
    height: 180px;
    margin: 10px 0;
  }

  .adv_12_wrap .item.right .img_bottom {
    position: relative;
    width: 376px;
    height: 180px;
  }

  .adv_12_wrap .item.right .img_bottom a {
    position: relative;
    width: 183px;
    height: 180px;
    float: left;
    text-align: center;
  }

  .adv_12_wrap .item.right .img_bottom a:last-child {
    margin-left: 10px;
  }

  /* adv_12-end */
</style>
