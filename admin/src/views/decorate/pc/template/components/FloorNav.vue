<template>
  <div class="floor_nav" ref="containerRef" :class="{ fixedTop: isTop }">
    <template v-if="nav_info.show">
      <MaskEdit :config="adv_config()">
        <div class="adv_image flex_row_center_center">
          <img
            :src="nav_info.adv_img.imgUrl"
            class="adv_image_img"
            v-if="nav_info.adv_img.imgUrl"
          />
          <div class="place_holder_image flex_row_center_center" v-else>
            <div>
              <p>设置宽</p>
              <p>80像素</p>
              <p>高不限</p>
              <p>广告图</p>
            </div>
          </div>
        </div>
      </MaskEdit>

      <div class="nav_area">
        <div
          class="flex_column_center_center nav_item"
          v-for="(item, index) in nav_list"
          :index="index"
          @click="scrollIntoView(item.id)"
        >
          <img :src="item.imgUrl" class="nav_item_img" v-if="item.imgUrl" />
          <span class="nav_item_text" v-if="item.text">{{ item.text }}</span>
        </div>
      </div>
    </template>
    <div class="back_top flex_column_center_center" @click="backTop" v-show="isTop">
      <AliSvgIcon
        iconName="icongengduo2"
        width="24px"
        height="24px"
        fillColor="primaryColor"
        class="-rotate-90"
      ></AliSvgIcon>
      <span class="mt-[8px] back_top_text">顶部</span>
    </div>

    <div class="hide_show" @click="nav_info.show = !nav_info.show">
      {{ nav_info.show ? '隐藏' : '显示' }}
    </div>
  </div>
</template>

<script setup>
  import MaskEdit from './MaskEdit.vue';
  import { createTplContext, formSingleImgConfig } from '../actions/edit_tpl';
  import { ref, toRef, unref } from 'vue';
  const isTop = ref(false);
  const containerRef = ref();
  const adv_config_tips = [
    '请严格根据提示要求上传规定尺寸的广告图片',
    '编辑项中的“操作”指点击该内容所产生的链接地址，可通过下拉选项选择不同的方式',
  ];

  const nav_list = ref([]);

  const props = defineProps({
    nav_info: {
      type: Object,
      default: () => {},
    },
  });

  const nav_info = toRef(props, 'nav_info');

  const adv_config = () =>
    formSingleImgConfig(
      '广告图设置',
      '',
      { 
        content:{
          show_width: 150, show_height: 230, width: 80, height: 0, data: unref(nav_info).adv_img 
        },
        client:props.client,
      },
      adv_config_tips,
    );

  document.body.addEventListener('scroll', (e) => {
    let pc_main_diy = document.querySelector('.pc_main_diy');
    let top = pc_main_diy?.getBoundingClientRect()?.top;
    if (top <= 68) {
      isTop.value = true;
    } else {
      isTop.value = false;
    }
  });

  const backTop = () => {
    document.body.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  };

  const editConfirmHandler = (values) => {
    let [data] = values;
    unref(nav_info).adv_img = data;
  };

  const getNavList = (list) => {
    nav_list.value = list.filter((i) => i.imgUrl || i.text);
  };

  const scrollIntoView = (id) => {
    document.getElementById(id)?.scrollIntoView({
      block: 'center',
      inline: 'nearest',
      behavior: 'smooth',
    });
  };

  createTplContext('edit', editConfirmHandler);

  defineExpose({
    getNavList,
  });
</script>

<style lang="less" scoped>
  .floor_nav {
    position: absolute;
    z-index: 50;
    top: 0;
    right: 10px;
    width: 80px;

    &:hover {
      .hide_show {
        display: block;
      }
    }

    &.fixedTop {
      position: fixed;
      top: 100px;
      right: 16px;
    }

    .adv_image {
      .adv_image_img {
        width: 80px;
      }

      .place_holder_image {
        width: 80px;
        min-height: 150px;
        background: #eee;
        color: #999;
        font-size: 12px;
      }
    }

    .nav_area {
      margin-top: 4px;
      border-radius: 4px;
      background-color: #fff;
      box-shadow: 0 0 6px 0 rgb(86 86 86 / 10%);

      .nav_item {
        height: 67px;
        transition: all 0.2s;
        border-bottom: 1px solid #ebeced;
        cursor: pointer;

        &:hover {
          background: @primary-color;

          .nav_item_text {
            color: #fff;
          }
        }

        .nav_item_img {
          width: 24px;
          height: 24px;
        }

        .nav_item_text {
          margin-top: 6px;
          color: #333;
          font-family: 'Microsoft YaHei';
          font-size: 13px;
          font-weight: 400;
        }
      }
    }

    .back_top {
      height: 68px;
      margin-top: 9px;
      border-radius: 4px;
      background: #fff;
      background-color: #fff;
      box-shadow: 0 0 6px 0 rgb(86 86 86 / 10%);
      cursor: pointer;

      &:hover {
        background: @primary-color;

        .back_top_text {
          color: #fff;
        }

        svg {
          fill: #fff !important;
        }
      }

      .back_top_text {
        color: @primary-color;
        font-size: 13px;
        font-weight: 400;
      }
    }
  }

  .hide_show {
    display: none;
    position: absolute;
    top: -25px;
    width: 80px;
    height: 26px;
    border-radius: 10px 10px 0 0;
    background-color: @primary-color;
    color: #fff;
    font-size: 13px;
    line-height: 26px;
    text-align: center;
    cursor: pointer;
  }
</style>
