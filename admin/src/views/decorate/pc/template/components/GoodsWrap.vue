<!-- 模板编辑---通用的商品展示组件 -->
<template>
  <div :class="[layoutClass]" v-show="isShow">
    <div
      class="goods_ad_img relative"
      :style="{
        width: imgInfo.width + 'px',
        height: imgInfo.height + 'px',
      }"
    >
      <img
        :src="goodsInfo.mainImage ? goodsInfo.mainImage : goodsInfo.goodsImage"
        class="goods_img"
        v-show="goodsInfo.mainImage ? goodsInfo.mainImage : goodsInfo.goodsImage"
        :style="{ objectFit: imgInfo.cover || 'cover' }"
      />
      <div class="placeholder flex_row_center_center">
        【{{ imgInfo.width }}*{{ imgInfo.height }}】
      </div>
    </div>

    <div class="gap" :style="gapStyle"></div>

    <div class="goods_ad_text">
      <div :style="nameStyle" class="name_default">{{ goodsInfo.goodsName }}</div>
      <div class="sale_default" v-if="showSale">{{ goodsInfo.actualSales*1 + goodsInfo.virtualSales*1}}人已买</div>
      <div class="flex_row_start_center">
        <div class="price_default" :style="priceStyle">
          <span class="price_tag"> ￥</span>
          <span>{{ goodsInfo.goodsPrice }}</span>
        </div>
        <div class="original_default" v-if="showMarketPrice && goodsInfo.marketPrice">
          ￥{{ goodsInfo.marketPrice }}
        </div>
      </div>
      <div class="purchase_button" v-if="showPurchase"> 立即抢购 </div>
    </div>
  </div>

  <div class="goods_placeholder h-full flex_row_center_center" v-show="!isShow">
    <AliSvgIcon
      iconName="iconshangpinguanli1"
      width="50px"
      height="50px"
      fillColor="rgb(229 230 222)"
    />
  </div>
</template>

<script setup>
  import { computed } from 'vue';
  import lodash from 'lodash-es';
  import { AliSvgIcon } from '@/components/SvgIcon';

  const props = defineProps({
    //垂直布局还是水平布局
    mode: String,
    //商品信息
    goodsInfo: {
      type: Object,
      default: () => {},
    },
    //图片信息
    imgInfo: {
      type: Object,
      default: () => ({
        width: 0,
        height: 0,
        cover: 'cover',
      }),
    },
    // 图片和商品文本之间的间隔
    gap: Number,
    //商品名样式
    nameStyle: [Object, String],
    //价格样式
    priceStyle: [Object, String],
    //是否显示立即购买按钮
    showPurchase: Boolean,
    // 是否显示销售数
    showSale: Boolean,
    //是否显示占位空标识
    placeholder: Boolean,
    // 是否显示原价
    showMarketPrice: Boolean,
  });
  const layoutClass = computed(() => {
    return props.mode == 'horizonal' ? 'flex flex-row' : 'flex flex-col';
  });

  const isShow = computed(() => (props.placeholder ? !lodash.isEmpty(props.goodsInfo) : true));

  const gapStyle = computed(() => {
    return props.mode == 'horizonal'
      ? { marginLeft: props.gap + 'px' }
      : { marginTop: props.gap + 'px' };
  });
</script>

<style lang="less" scoped>
  .diy_goods_component {
    width: 100%;
    height: 100%;
  }

  .goods_ad_img {
    margin: 0 auto;

    .goods_img {
      position: absolute;
      z-index: 2;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }

    .placeholder {
      width: 100%;
      height: 100%;
      background-color: #eee;
      font-size: 15px;
    }
  }

  .goods_ad_text {
    flex: 1;

    .name_default {
      overflow: hidden;
      text-overflow: ellipsis;
      word-wrap: break-word;
      word-break: normal;
    }

    .price_default {
      color: red;
      font-size: 12px;
      font-weight: bold;
    }

    .original_default {
      margin-left: 10px;
      color: #999;
      font-size: 12px;
      text-decoration: line-through;
    }

    .purchase_button {
      display: inline-block;
      padding: 4px 17px;
      border-radius: 11px;
      background: #ff0036;
      color: #fff;
      font-size: 1rem;
      font-weight: bold;
    }

    .sale_default {
      color: #666;
      font-size: 13px;
    }
  }
</style>
