<!-- 模板列表 包裹模板组件的部分 -->
<template>
  <div>
    <div class="web_tpl_list_item" :style="{ width: tplElWith + 'px' }">
      <div class="operation flex_row_between_center">
        <span class="tpl_name">{{ tplItem.name }}</span>
        <div class="flex_row_end_center" v-if="mode == 'edit'">
          <Switch
            size="small"
            @change="handleSetEnable"
            :checked="tplItem.isEnable"
            :checkedValue="1"
            :unCheckedValue="0"
          />
          <img
            class="opt_icon"
            @click="editData"
            src="@/assets/images/pc_diy_img/web_tpl_edit_icon.png"
          />
          <img
            class="opt_icon"
            @click="diyTpl(tplItem)"
            src="@/assets/images/pc_diy_img/web_tpl_diy_icon.png"
          />
          <Popconfirm
            placement="leftBottom"
            title="删除后不可恢复，是否确定删除"
            @confirm="delData(tplItem.dataId)"
            okText="确定"
            cancelText="取消"
          >
            <img class="opt_icon" src="@/assets/images/pc_diy_img/web_ins_tpl_del_icon.png" />
          </Popconfirm>
        </div>
        <div v-if="mode == 'select'" class="cursor-pointer" @click="emit('check')">
          <AliSvgIcon
            :iconName="checked ? 'iconxuanzhong3' : 'iconziyuan43'"
            width="16px"
            height="16px"
            fillColor="rgb(255, 126, 40)"
          />
        </div>
      </div>
      <div :style="{ zoom: tplElWith / 1210 }">
        <TplComponent mode="display" :tpl_info="tplItem.data_json" />
      </div>
    </div>
    <SldModal
      :content="editModalContent"
      :visible="sldModalVisible"
      title="编辑装修模板基本信息"
      :width="600"
      @cancle-event="sldModalVisible = false"
      @confirmEvent="editConfirm"
    ></SldModal>
  </div>
</template>

<script setup>
  import { Popconfirm, Switch, message } from 'ant-design-vue';
  import SldModal from '@/components/SldModal/index.vue';
  import { TplDeleteApi, TplEnableApi, TplUpdateApi } from '/@/api/decorate/deco_pc';
  import { ref, unref, watch } from 'vue';
  import { createLocalStorage } from '/@/utils/cache';
  import { useGo } from '/@/hooks/web/usePage';
  import { AliSvgIcon } from '@/components/SvgIcon';
  import { transType2ComponentName } from '../actions/template_list';
  import { createAsyncComponent } from '/@/utils/factory/createAsyncComponent';

  const props = defineProps({
    tplElWith: {
      type: Number,
      default: 1210,
    },

    tplItem: {
      type: Object,
      default: () => {},
    },

    mode: {
      type: String,
      default: 'edit',
    },

    checked: {
      type: Boolean,
      default: false,
    },
  });
  const loadError = () => {};

  //通过传过来的type 异步导入对应的模板组件
  let componentName = transType2ComponentName(props.tplItem.data_json.type);
  let TplComponent = createAsyncComponent(() => import(`../${componentName}.vue`), {}, loadError);

  //通过watch再次动态异步导入
  watch(
    () => props.tplItem,
    () => {
      componentName = transType2ComponentName(props.tplItem.data_json.type);
      TplComponent = createAsyncComponent(() => import(`../${componentName}.vue`), {}, loadError);
    },
  );

  const storage = createLocalStorage();

  const go = useGo();

  const emit = defineEmits(['confirm', 'check']);

  const sldModalVisible = ref(false);

  const handleSetEnable = async () => {
    let { tplItem } = props;
    const res = await TplEnableApi({
      dataId: tplItem.dataId,
      isEnable: tplItem.isEnable == 1 ? false : true,
    });
    if (res.state == 200) {
      tplItem.isEnable = tplItem.isEnable == 1 ? 0 : 1;
    }
  };

  const diyTpl = (item) => {
    storage.set('TPL_JSON', item);
    go('instance_template_lists_to_add');
  };

  const editModalContent = ref([
    {
      name: 'name',
      type: 'input',
      placeholder: '请输入实例化模板名称',
      label: '实例化模板名称',
      labelWidth: 70,
      maxLength: 20,
      rules: [
        {
          whiteSpace: true,
          trigger: 'blur',
          required: true,
          message: '请输入实例化模板名称',
        },
      ],
    },
    {
      name: 'sort',
      type: 'inputnum',
      placeholder: '请输入排序',
      label: '排序',
      labelWidth: 70,
      extra: '请输入0~255的数字,数据越小显示越靠前',
      max: 255,
      rules: [
        {
          trigger: 'blur',
          required: true,
          message: '请输入排序',
        },
        {
          validator: (rules, value) => {
            if (!(value >= 0 && value <= 255)) {
              return Promise.reject('请输入0~255的数字');
            }
            return Promise.resolve();
          },
        },
      ],
    },
  ]);

  const editData = () => {
    unref(editModalContent).map((com) => (com.initialValue = props.tplItem[com.name]));
    sldModalVisible.value = true;
  };

  const editConfirm = async (values, resetFields) => {
    const res = await TplUpdateApi({ ...values, dataId: props.tplItem.dataId });
    if (res.state == 200) {
      resetFields();
      message.success(res.msg);
      sldModalVisible.value = false;
      emit('confirm');
    } else {
      message.error(res.msg);
    }
  };

  const delData = async (dataId) => {
    const res = await TplDeleteApi({ dataId });
    if (res.state == 200) {
      message.success(res.msg);
      emit('confirm');
    } else {
      message.error(res.msg);
    }
  };
</script>

<style lang="less">
  .web_tpl_list_item {
    position: relative;
    box-sizing: border-box;
    flex-wrap: wrap;
    margin-bottom: 10px;
    overflow: hidden;
    border: 1px solid #fadfd2;
    border-radius: 13px;
    box-shadow: 2px 3px 9px 0 rgb(33 17 14 / 26%);

    .operation {
      height: 38px;
      padding: 0 20px;
      border-radius: 13px 13px 0 0;
      background-color: rgb(255 113 30 / 5%);

      .tpl_name {
        color: #252525;
        font-size: 14px;
      }

      .opt_icon {
        width: 16px;
        height: 16px;
        margin-left: 15px;
        cursor: pointer;
      }
    }
  }
</style>
