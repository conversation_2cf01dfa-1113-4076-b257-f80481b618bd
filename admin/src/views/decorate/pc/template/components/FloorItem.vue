<!-- 装修详情---楼层组件 -->
<template>
  <div style="position: relative; width: 100%" class="sld_web_item_hover">
    <div class="flex_row_between_start">
      <div class="instance_tpl_name">{{ floorData.name }}</div>
      <a class="top_add" @click="addTplData('top')">
        <AliSvgIcon iconName="iconjia" fillColor="primaryColor" width="19px" height="19px" />
      </a>
      <a class="bottom_add" @click="addTplData('bottom')">
        <AliSvgIcon iconName="iconjia" fillColor="primaryColor" width="19px" height="19px" />
      </a>
      <div class="flex_row_start_start floor_tag">
        <div class="flex_row_center_center opt up" @click="setFloorNav">
          <span>设置楼层导航</span></div
        >
        <div class="flex_row_center_center opt up" @click="handleFloorStyle">
          <span>楼层样式</span></div
        >
        <div
          class="flex_row_center_center opt up_do"
          @click="emit('handleMoveFloor', 'up', floorData.id)"
        >
          <span>上移</span></div
        >
        <div
          class="flex_row_center_center opt down_do"
          @click="emit('handleMoveFloor', 'down', floorData.id)"
        >
          <span>下移</span></div
        >

        <div class="flex_row_center_center opt up" @click="addTplData('cur', 'floor')">
          <span>装载</span>
        </div>
        <div class="flex_row_center_center opt up" @click="saveAsInstanceTpl">
          <span>{{
            floorData.json.insTplId != undefined && floorData.json.insTplId ? '另存为' : '保存'
          }}</span>
        </div>
        <div
          @click="emit('handleMoveFloor', 'del', floorData.id)"
          class="flex_row_center_center opt up"
        >
          <span>删除</span>
        </div>
      </div>
    </div>
    <div class="diy_part_wrap allow_show_edit" :style="outSideStyle">
      <TplComponent :tpl_info="tpl_info"></TplComponent>
    </div>
  </div>
  <SldModal
    v-bind="modalProps"
    @cancle-event="modalProps.visible = false"
    @confirm-event="modalConfirm"
  >
  </SldModal>

  <SldNavModal
    title="设置楼层导航"
    v-model:modalVisible="navModalVisible"
    :content="navModalContent()"
    :width="800"
    :modal-tip="['楼层导航可设置纯文字内容或纯图片，也可以设置图片+文字形式']"
    @confirmEvent="navModalConfirm"
  >
  </SldNavModal>
</template>

<script setup>
  import { message } from 'ant-design-vue';
  import { reactive, ref, toRefs, unref, watch } from 'vue';
  import { AliSvgIcon } from '@/components/SvgIcon';
  import SldNavModal from '@/components/SldNavModal/index.vue';
  import SldModal from '@/components/SldModal/index.vue';
  import { getCurrentInstance } from 'vue';
  import lodash from 'lodash-es';
  import { TplAddListApi } from '/@/api/decorate/deco_pc';
  import { transType2ComponentName } from '../actions/template_list';
  import { createAsyncComponent } from '/@/utils/factory/createAsyncComponent';
  const props = defineProps({
    floorData: {
      type: Object,
      default: () => {},
    },
    tpl_info: {
      type: Object,
      default: () => {},
    },
    nav_info: {
      type: Object,
      default: () => {},
    },
  });
  const emit = defineEmits(['handleMoveFloor', 'loadFloor', 'floorNavSet']);
  let currentAction = '';
  const outSideStyle = reactive({
    backgroundColor: props.floorData.json?.bg_color,
    paddingTop: props.floorData.json?.padding_top + 'px',
    paddingBottom: props.floorData.json?.padding_bottom + 'px',
  });

  const { nav: nav_info } = toRefs(props.floorData);

  const navModalVisible = ref(false);

  const navModalContent = () => ({
    layout: {
      img_show_width: 100,
      img_show_height: 100,
      width: 24,
      height: 24,
    },
    img: {
      label: '导航图片',
      imgUrl: unref(nav_info).imgUrl,
      imgPath: unref(nav_info).imgPath,
    },

    text: {
      label: '导航文字',
      value: unref(nav_info).text,
    },
  });

  const vm = getCurrentInstance();
  // @ts-ignore
  const { $sldComLanguage } = vm?.appContext.config.globalProperties;
  const loadError = () => {};

  //根据楼层的模板类型,异步导入对应的模板组件
  let componentName = transType2ComponentName(props.tpl_info.type);
  let TplComponent = createAsyncComponent(() => import(`../${componentName}.vue`), {}, loadError);

  //监听tpl_info
  watch(
    () => props.tpl_info,
    () => {
      componentName = transType2ComponentName(props.tpl_info.type);
      TplComponent = createAsyncComponent(() => import(`../${componentName}.vue`), {}, loadError);
    },
  );

  const modalContent = [
    {
      type: 'inputnum',
      label: $sldComLanguage('上边距'),
      name: 'padding_top',
      placeholder: $sldComLanguage('请输入上边距'),
      extra: $sldComLanguage('单位：px，设置楼层顶部内边距值，范围为0～200'),
      initialValue: '',
      min: 0,
      max: 200,
      rules: [
        {
          required: true,
          message: $sldComLanguage('请输入上边距'),
        },
      ],
    },
    {
      type: 'inputnum',
      label: $sldComLanguage('下边距'),
      name: 'padding_bottom',
      placeholder: $sldComLanguage('请输入下边距'),
      extra: $sldComLanguage('单位：px，设置楼层底部内边距值，范围为0～200'),
      initialValue: '',
      min: 0,
      max: 200,
      rules: [
        {
          required: true,
          message: $sldComLanguage('请输入下边距'),
        },
      ],
    },
    {
      type: 'more_color_picker',
      label: $sldComLanguage('背景色'),
      name: 'bg_color',
      placeholder: $sldComLanguage('请点击选择颜色'),
      initialValue: 'rgba(255,255,255,1)',
      is_show: false,
    },
  ]; //modal框的数据

  const modalProps = reactive({
    visible: false,
    content: [],
    title: '',
    width: '560px',
  });

  const addTplData = (position) => {
    let { floorData } = props;
    emit('loadFloor', {
      position,
      type: 'banner_except',
      tplId: position == 'cur' ? floorData.tplPcId : 0,
    });
  };

  //设置样式
  const handleFloorStyle = () => {
    let cloneContent = lodash.cloneDeep(modalContent);
    modalProps.visible = true;
    modalProps.title = $sldComLanguage('设置楼层样式');
    currentAction = 'set_style';
    // @ts-ignore
    if (!modalProps.content.length) {
      modalProps.content = cloneContent.map((item) => ({
        ...item,
        initialValue: props.floorData.json[item.name],
      }));
    }

    if (props.tpl_info.type == 'adv_01') {
      // @ts-ignore
      modalProps.content.push({
        type: 'switch',
        label: $sldComLanguage('宽度占满屏幕'),
        name: 'full_screen',
        placeholder: ``,
        initialValue: props.floorData.full_screen ?? 0,
      });
    }
  };

  //模板另存为
  // @ts-ignore
  const saveAsInstanceTpl = () => {
    let { floorData } = props;
    modalProps.visible = true;
    currentAction = 'save';
    modalProps.title =
      floorData.insTplId != undefined && floorData.insTplId
        ? $sldComLanguage('模板另存为')
        : $sldComLanguage('保存模板');
    modalProps.content = [
      // @ts-ignore
      {
        type: 'input',
        label: $sldComLanguage('实例化模板名称'),
        name: 'name',
        placeholder: $sldComLanguage('请输入实例化模板名称'),
        initialValue: '',
        maxLength: 20,
        rules: [
          {
            required: true,
            whitespace: true,
            message: $sldComLanguage('请输入实例化模板名称'),
          },
        ],
      },
    ];
  };

  //保存
  const addInstanceTpl = async (param) => {
    let { floorData, tpl_info } = props;
    let params = {
      tplPcId: floorData.tplPcId,
      html: '',
      json: JSON.stringify(tpl_info),
      sort: 0,
      isEnable: 1,
      name: param.name,
    };

    const res = await TplAddListApi(params);

    if (res.state == 200) {
      message.success(res.msg);
    } else {
      message.warn(res.msg);
    }
  };

  //modal确认
  const modalConfirm = (val) => {
    switch (currentAction) {
      case 'set_style': {
        outSideStyle.backgroundColor = val.bg_color;
        outSideStyle.paddingTop = val.padding_top + 'px';
        outSideStyle.paddingBottom = val.padding_bottom + 'px';

        Object.keys(val).forEach((key) => {
          props.floorData.json[key] = val[key];
        });

        break;
      }
      case 'save': {
        addInstanceTpl(val);
        break;
      }
    }

    modalProps.visible = false;
  };

  const setFloorNav = () => {
    navModalVisible.value = true;
  };

  const navModalConfirm = (info) => {
    navModalVisible.value = false;
    nav_info.value = info;
    emit('floorNavSet');
  };
</script>

<style lang="less">
  .floor_tag {
    position: absolute;
    z-index: 41;
    top: -30px;
    left: 0;
    overflow: hidden;
    border-radius: 3px 3px 0 0;
    cursor: pointer;
  }

  .sld_web_item {
    &:first-child {
      .up {
        display: none;
      }
    }

    &:last-child {
      .down {
        display: none;
      }
    }
  }

  .sld_web_item_hover:hover {
    padding: 1px;

    &::before {
      display: block;
    }

    .opt,
    .instance_tpl_name {
      display: flex;
    }

    a.top_add {
      display: flex;
      z-index: 42;
    }

    a.bottom_add {
      display: flex;
      z-index: 42;
    }
  }

  .sld_web_item_hover {
    cursor: grab;

    &::before {
      content: '';
      display: none;
      position: absolute;
      z-index: 14;
      inset: -1px;
      border-width: 1px;
      border-style: solid;
      border-color: @primary-color;
    }

    a.top_add {
      display: none;
      position: absolute;
      z-index: 4;
      top: -10px;
      left: 50vw;
      width: 19px;
      height: 19px;
      margin-left: 10px;
      border-radius: 50%;
      background: #fff;
    }

    a.bottom_add {
      display: none;
      position: absolute;
      z-index: 4;
      bottom: -10px;
      left: 50vw;
      width: 19px;
      height: 19px;
      margin-left: 10px;
      border-radius: 50%;
      background: #fff;
    }

    .instance_tpl_name {
      display: none;
      position: absolute;
      z-index: 4;
      top: -25px;
      left: 0;
      padding: 7px 0 0 10px;
      color: #000;
      font-size: 14px;
    }

    .opt {
      display: none;
      height: 29px;
      padding-right: 12px;
      padding-left: 12px;
      transition: background-color 500ms ease-out;
      border-left: 1px solid rgb(255 255 255 / 20%);
      background-color: @primary-color;

      &:first-child {
        border-left: none;
      }

      img {
        width: 15px;
        height: 15px;
      }

      span {
        margin-left: 4px;
        color: #fff;
        font-size: 13px;
      }
    }
  }
</style>
