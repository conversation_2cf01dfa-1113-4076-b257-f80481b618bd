<!-- 装修详情---选择装载的楼层 -->
<template>
  <Modal
    title="添加楼层"
    :visible="visible"
    :width="1230"
    @cancel="selectCancel"
    @ok="selectConfirm"
  >
    <div style="padding: 20px 10px">
      <BasicForm @register="faRegister"></BasicForm>
      <div class="mt-1 relative" v-if="tplId == 0">
        <RadioGroup
          v-model:value="tplType"
          @change="() => getTemplateList()"
          v-if="type == 'banner_except'"
        >
          <RadioButton value="banner_except">全部</RadioButton>
          <RadioButton value="adv_floor">广告楼层</RadioButton>
          <RadioButton value="goods_floor">商品楼层</RadioButton>
        </RadioGroup>
      </div>
      <div class="web_tpl_item_list" :style="{ height: contentHeight + 'px' }">
        <Spin :spinning="getTplLoading">
          <div class="mt-3 flex flex-wrap justify-between" ref="tplOutsideEl">
            <div v-for="(fallItem, fallIndex) in fallTplList" :key="fallIndex" class="fall_item">
              <WebTplItem
                :tplElWith="tplElWith"
                :tplItem="item"
                v-for="(item, index) in fallItem"
                :key="index"
                @confirm="getTemplateList"
                mode="select"
                :checked="curSelectInstanceTpl.dataId == item.dataId"
                @check="tplCheck(item)"
              />
            </div>
            <div
              class="flex_column_center_center w-full pt-[100px]"
              v-if="!getTplLoading && !tplList.length"
            >
              <Empty :image="Empty.PRESENTED_IMAGE_SIMPLE" description="暂无数据" />
            </div>
          </div>
        </Spin>
      </div>
    </div>
  </Modal>
</template>

<script setup>
  import { getCurrentInstance, onMounted, reactive, ref, unref, watch } from 'vue';
  import { RadioGroup, RadioButton, Spin, Empty } from 'ant-design-vue';

  import { Modal } from 'ant-design-vue';
  import { BasicForm, useForm } from '@/components/Form';
  import WebTplItem from './WebTplItem.vue';
  import { tpl_list_handler } from '../actions/template_list';
  import { isEmpty } from '/@/utils/is';
  import { failTip } from '/@/utils/utils';

  const props = defineProps({
    visible: Boolean,
    type: {
      type: String,
      default: 'banner_except',
    },
    tplId: {
      type: Number,
      default: -1,
    },
  });

  const vm = getCurrentInstance();
  const { $sldComLanguage } = vm?.appContext.config.globalProperties;

  const emit = defineEmits(['cancel', 'confirm']);
  const curSelectInstanceTpl = ref({});
  const { tplList, tplType, getTemplateList, fallTplList, getTplLoading, tplId } = tpl_list_handler(
    {
      isEnable: 1,
    },
  );

  watch(props, () => {
    if (props.visible) {
      tplId.value = props.tplId;
      tplType.value = props.type;
      getTemplateList();
    }
  });

  const contentHeight = ref(window.innerHeight - 350);

  const tplElWith = (1230 - 40) / 2;

  const handleAddFloor = async () => {
    let values = getFieldsValue();
    getTemplateList(values);
  };
  const [faRegister, { getFieldsValue }] = useForm({
    layout: 'inline',
    schemas: [
      {
        colProps: { span: 8, style: 'width:280px;max-width:100%;flex:none' },
        labelWidth: 100,
        field: 'name',
        component: 'Input',
        label: '实例化名称',
        componentProps: {
          placeholder: '请输入实例化名称',
        },
      },
    ],
    submitFunc: handleAddFloor,
    resetFunc: getTemplateList,
  });

  const tplCheck = (item) => {
    curSelectInstanceTpl.value = item;
  };

  const selectConfirm = () => {
    if (isEmpty(unref(curSelectInstanceTpl))) {
      failTip(`${$sldComLanguage('请先选择数据')}～`);
      return;
    }
    emit('confirm', unref(curSelectInstanceTpl));
    curSelectInstanceTpl.value = {};
  };

  const selectCancel = () => {
    curSelectInstanceTpl.value = {};
    emit('cancel');
  };
</script>

<style lang="scss" scoped>
  .web_tpl_item_list {
    overflow: auto;
  }
</style>
