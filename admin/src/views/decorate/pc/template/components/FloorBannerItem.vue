<!-- 装修详情---楼层组件 -->
<template>
  <div class="sld_web_item">
    <div style="position: relative; width: 100%" class="sld_web_item_hover">
      <div class="flex_row_between_start">
        <div class="instance_tpl_name">{{ floorData.name }}</div>
        <div class="flex_row_end_start floor_tag">
          <div
            class="flex_row_center_center opt up"
            @click="handleFloorStyle"
            v-if="tpl_info.type !== 'main_banner'"
          >
            <span>楼层样式</span></div
          >
          <div class="flex_row_center_center opt up" @click="addTplData('cur', 'floor')">
            <span>装载</span>
          </div>
        </div>
      </div>
      <div class="diy_part_wrap allow_show_edit">
        <TplComponent :tpl_info="tpl_info"></TplComponent>
      </div>
    </div>
    <SldModal
      v-bind="modalProps"
      @cancle-event="modalProps.visible = false"
      @confirm-event="modalConfirm"
    >
    </SldModal>
  </div>
</template>

<script setup>
  import { message } from 'ant-design-vue';
  import { reactive, watch, toRefs, unref, nextTick, onMounted } from 'vue';

  import SldModal from '@/components/SldModal/index.vue';
  import { getCurrentInstance } from 'vue';
  import { TplAddListApi } from '/@/api/decorate/deco_pc';
  import { transType2ComponentName } from '../actions/template_list';
  import { createAsyncComponent } from '/@/utils/factory/createAsyncComponent';
  const props = defineProps({
    floorData: {
      type: Object,
      default: () => {},
    },
    tpl_info: {
      type: Object,
      default: () => {},
    },
  });
  const emit = defineEmits(['handleMoveFloor', 'loadFloor', 'updateFloor']);
  let currentAction = '';

  const { style: bannerStyle } = toRefs(props.tpl_info);

  const vm = getCurrentInstance();
  // @ts-ignore
  const { $sldComLanguage } = vm?.appContext.config.globalProperties;
  const loadError = () => {};

  //根据楼层的模板类型,异步导入对应的模板组件
  let componentName = transType2ComponentName(props.tpl_info.type);
  let TplComponent = createAsyncComponent(() => import(`../${componentName}.vue`), {}, loadError);
  //监听tpl_info
  watch(
    () => props.tpl_info,
    () => {
      componentName = transType2ComponentName(props.tpl_info.type);
      TplComponent = createAsyncComponent(() => import(`../${componentName}.vue`), {}, loadError);
    },
  );

  const modalContent = [
    {
      type: 'radio_select',
      label: '楼层风格',
      name: 'borderStyle',
      data: [
        { key: 1, value: '直角风格' },
        { key: 0, value: '圆弧风格' },
      ],
      initialValue: unref(bannerStyle)?.borderRadius ? 0 : 1,
    },
  ]; //modal框的数据

  const modalProps = reactive({
    visible: false,
    content: [],
    title: '',
    width: '560px',
  });

  const addTplData = (position) => {
    let { floorData } = props;
    emit('loadFloor', {
      position,
      type: 'main_banner',
      tplId: position == 'cur' ? floorData.tplPcId : 0,
    });
  };

  //设置样式
  const handleFloorStyle = () => {
    modalProps.visible = true;
    modalProps.title = $sldComLanguage('设置楼层样式');
    currentAction = 'set_style';
    // @ts-ignore
    let { style } = props.tpl_info;

    modalProps.content = modalContent.map((item) => {
      item.initialValue = style.borderRadius ? 0 : 1;
      return item;
    });
  };

  //保存
  const addInstanceTpl = async (param) => {
    let { floorData, tpl_info } = props;
    let params = {
      tplPcId: floorData.tplPcId,
      html: '',
      json: JSON.stringify(tpl_info),
      sort: 0,
      isEnable: 1,
      name: param.name,
    };

    const res = await TplAddListApi(params);

    if (res.state == 200) {
      message.success(res.msg);
    } else {
      message.warn(res.msg);
    }
  };

  //modal确认
  const modalConfirm = (val) => {
    switch (currentAction) {
      case 'set_style': {
        unref(bannerStyle).borderRadius = val.borderRadius ? 0 : 15;
        emit('updateFloor', val);
        break;
      }
      case 'save': {
        addInstanceTpl(val);
        break;
      }
    }

    modalProps.visible = false;
  };

  onMounted(() => {});
</script>

<style lang="less">
  .floor_tag {
    position: absolute;
    z-index: 41;
    top: -30px;
    right: 0;
    overflow: hidden;
    border-radius: 3px 3px 0 0;
  }

  .sld_web_item {
    &:first-child {
      .up {
        display: none;
      }
    }

    &:last-child {
      .down {
        display: none;
      }
    }
  }

  .sld_web_item_hover:hover {
    padding: 1px;

    &::before {
      display: block;
    }

    .opt,
    .instance_tpl_name {
      display: flex;
    }

    a.top_add {
      display: flex;
      z-index: 42;
    }

    a.bottom_add {
      display: flex;
      z-index: 42;
    }
  }

  .sld_web_item_hover {
    cursor: grab;

    &::before {
      content: '';
      display: none;
      position: absolute;
      z-index: 14;
      inset: -1px;
      border-width: 1px;
      border-style: solid;
      border-color: @primary-color;
    }

    a.top_add {
      display: none;
      position: absolute;
      z-index: 4;
      top: -10px;
      left: 50vw;
      width: 19px;
      height: 19px;
      margin-left: 10px;
      border-radius: 50%;
      background: #fff;
    }

    a.bottom_add {
      display: none;
      position: absolute;
      z-index: 4;
      bottom: -10px;
      left: 50vw;
      width: 19px;
      height: 19px;
      margin-left: 10px;
      border-radius: 50%;
      background: #fff;
    }

    .instance_tpl_name {
      display: none;
      position: absolute;
      z-index: 4;
      top: -25px;
      left: 0;
      padding: 7px 0 0 10px;
      color: #000;
      font-size: 14px;
    }

    .opt {
      display: none;
      height: 29px;
      padding-right: 12px;
      padding-left: 12px;
      transition: background-color 500ms ease-out;
      border-left: 1px solid rgb(255 255 255 / 20%);
      background-color: @primary-color;

      &:first-child {
        border-left: none;
      }

      img {
        width: 15px;
        height: 15px;
      }

      span {
        margin-left: 4px;
        color: #fff;
        font-size: 13px;
      }
    }
  }
</style>
