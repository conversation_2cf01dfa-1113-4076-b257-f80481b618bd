<template>
  <div class="adv_21">
    <div class="adv_21_wrap flex_row_start_start">
      <!-- <MaskEdit>
      </MaskEdit> -->
      <template v-if="loading">
        <div
          class="flex_column_start_start adv_21_wrap_item"
          v-for="(item1, index1) in imageData"
          :key="'item1' + index1"
        >
          <!-- 总标题部分 start -->
          <MaskEdit style="width: 100%" :config="adv21TopTitleConfig.data[index1]">
            <section class="flex_row_between_center title_part">
              <span class="title">{{ item1.top_title.title.initialValue }}</span>
              <a class="view_more">{{ item1.top_title.sub_title.initialValue }}></a>
            </section>
          </MaskEdit>
          <!-- 总标题部分 end -->
          <div class="flex_column_start_start detail">
            <div
              class="flex_row_start_start item"
              v-for="(item2, index2) in item1.detail"
              :key="'item2' + index2"
            >
              <!-- 左侧子标题部分 start -->
              <div class="item_left">
                <MaskEdit
                  style="width: 100%"
                  :config="adv21LeftTopTitleConfig.data[index1].data[index2]"
                >
                  {{ item2.left.title.initialValue }}
                </MaskEdit>
              </div>
              <!-- 左侧子标题部分 end -->
              <!-- 右侧子标题部分 start -->
              <div class="flex_row_start_center item_right">
                <MaskEdit
                  style="width: 100%"
                  :config="adv21rightTopTitleConfig.data[index1].data[index2]"
                >
                  <section class="flex_row_start_center item_right">
                    <span
                      v-for="(item3, index3) in item2.right"
                      :key="'item3' + index3"
                      class="item_right_con"
                    >
                      {{ item3.title.initialValue }}
                    </span>
                  </section>
                </MaskEdit>
              </div>
              <!-- 右侧子标题部分 end -->
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
  import { provide, reactive, ref, toRef, unref, onMounted } from 'vue';
  import { createTplContext, dfEmit, dfProps } from './actions/edit_tpl';
  import MaskEdit from './components/MaskEdit.vue';
  const props = defineProps(dfProps);
  const emit = defineEmits(dfEmit);
  const imageData = toRef(props.tpl_info, 'data');
  const loading = ref(false);

  const getList = () => {
    let TopTitle = [];
    let leftTitle = [];
    let rightTitle = [];
    imageData.value.forEach((item, index) => {
      TopTitle.push({
        title: '顶部标题设置',
        position: 'top_title',
        index: index,
        extra: { title_limit: 6, sub_title_limit: 10 },
        modal_tip: ['左侧标题不能为空，最多输入6个字', '子标题不能为空，最多输入10个字'],
        component: 'SldDiyTitleLinkModal',
        width: 1000,
        content: {
          ...unref(imageData)[index].top_title,
        },
        client:props.client,
      });
      let leftTitleObj = [];
      item.detail.forEach((it, ind) => {
        leftTitleObj.push({
          title: `左侧标题${ind + 1}设置`,
          position: 'left_title',
          extra: { title_limit: 5 },
          modal_tip: ['标题不能为空，最多输入5个字'],
          index: [index, ind],
          component: 'SldDiyTitleLinkModal',
          width: 1000,
          content: {
            ...unref(imageData)[index].detail[ind].left,
          },
          client:props.client,
        });
      });
      leftTitle.push({
        data: leftTitleObj,
      });
      let rightTitleObj = [];
      item.detail.forEach((it, ind) => {
        rightTitleObj.push({
          title: `右侧子标题${ind + 1}设置`,
          position: 'right_title',
          extra: { input_limit: 4 },
          modal_tip: ['子标题不能为空，最多输入4个字'],
          index: [index, ind],
          component: 'SldDiyMoreTitleLinkModal',
          width: 1000,
          content: imageData.value[index].detail[ind].right,
          client:props.client,
        });
      });
      rightTitle.push({
        data: rightTitleObj,
      });
    });
    adv21TopTitleConfig.data = TopTitle;
    adv21LeftTopTitleConfig.data = leftTitle;
    adv21rightTopTitleConfig.data = rightTitle;
    loading.value = true;
  };

  // 顶部的title配置
  const adv21TopTitleConfig = reactive({
    data: [],
  });

  // 左侧的子title配置
  const adv21LeftTopTitleConfig = reactive({
    data: [],
  });

  // 右侧的子title配置
  const adv21rightTopTitleConfig = reactive({
    data: [],
  });

  const editConfirmHandler = (values, { modal, position, index }) => {
    switch (position) {
      case 'top_title': {
        imageData.value[index].top_title.title.initialValue = values[0].title;
        imageData.value[index].top_title.sub_title.initialValue = values[0].sub_title;
        imageData.value[index].top_title.link_type = values[0].link_type;
        imageData.value[index].top_title.link_value = values[0].link_value;
        imageData.value[index].top_title.info = values[0].info;
        adv21TopTitleConfig.data[index].content = { ...imageData.value[index].top_title };
        break;
      }

      case 'left_title': {
        let index1 = index[0];
        let index2 = index[1];
        imageData.value[index1].detail[index2].left.title.initialValue = values[0].title;
        imageData.value[index1].detail[index2].left.link_type = values[0].link_type;
        imageData.value[index1].detail[index2].left.link_value = values[0].link_value;
        imageData.value[index1].detail[index2].left.info = values[0].info;
        adv21LeftTopTitleConfig.data[index1].data[index2].content = {
          ...imageData.value[index1].detail[index2].left,
        };
        break;
      }

      case 'right_title': {
        let index1 = index[0];
        let index2 = index[1];
        imageData.value[index1].detail[index2].right = values[0];
        adv21rightTopTitleConfig.data[index1].data[index2].content = values[0];
        break;
      }
    }
    emit('save_tpl_data', props.tpl_info,props.client);
  };

  createTplContext(props.mode, editConfirmHandler);

  onMounted(() => {
    getList();
  });
</script>

<style lang="scss" scoped>
  .adv_21 {
    width: 100%;
    overflow: auto;

    .adv_21_wrap {
      position: relative;
      flex-wrap: wrap;
      width: 1210px;
      margin: auto;
      padding: 18px 0;
      clear: both;
      overflow: hidden;
    }
  }

  .adv_21 .adv_21_wrap .adv_21_wrap_item {
    width: 380px;
    height: 388px;
    margin-right: 35px;
    border-radius: 2px;
    background: #fff;
    box-shadow: 0 0 6px 0 rgb(0 0 0 / 9%);
  }

  .adv_21 .adv_21_wrap .adv_21_wrap_item:last-child {
    width: 380px;
    height: 388px;
    margin-right: 0;
    border-radius: 2px;
    background: #fff;
    box-shadow: 0 0 6px 0 rgb(0 0 0 / 9%);
  }

  .adv_21 .adv_21_wrap .adv_21_wrap_item .title_part {
    position: relative;
    flex-shrink: 0;
    width: 100%;
    height: 54px;
    border-bottom: 1px solid #f0f0f0;
  }

  .adv_21 .adv_21_wrap .adv_21_wrap_item .title_part .title {
    padding-left: 15px;
    border-left: 4px solid #ee712e;
    color: #333;
    font-family: PingFangSC-Semibold, 'PingFang SC';
    font-size: 20px;
    font-weight: 600;
    line-height: 28px;
  }

  .adv_21 .adv_21_wrap .adv_21_wrap_item .title_part .view_more {
    height: 20px;
    margin-right: 18px;
    color: #ee712e;
    font-family: PingFangSC-Regular, 'PingFang SC';
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
  }

  .adv_21 .adv_21_wrap .adv_21_wrap_item .detail {
    flex: 1;
    width: 100%;
  }

  .adv_21 .adv_21_wrap .adv_21_wrap_item .detail .item {
    margin-top: 19px;
  }

  .adv_21 .adv_21_wrap .adv_21_wrap_item .detail .item .item_left {
    position: relative;
    flex-shrink: 0;
    width: 110px;
    margin-left: 20px;
    color: #a2a2a2;
    font-family: PingFangSC-Regular, 'PingFang SC';
    font-size: 16px;
    font-weight: 400;
    line-height: 30px;
  }

  .adv_21 .adv_21_wrap .adv_21_wrap_item .detail .item .item_right {
    position: relative;
    flex-wrap: wrap;
    margin-right: 15px;
  }

  .adv_21 .adv_21_wrap .adv_21_wrap_item .detail .item .item_right .item_right_con {
    margin-right: 20px;
    color: #666;
    font-family: PingFangSC-Regular, 'PingFang SC';
    font-size: 14px;
    font-weight: 400;
    line-height: 28px;
  }

  .adv_21 .adv_21_wrap .adv_21_wrap_item .detail .item .item_right .item_right_con:hover {
    color: #ee712e;
  }
</style>
