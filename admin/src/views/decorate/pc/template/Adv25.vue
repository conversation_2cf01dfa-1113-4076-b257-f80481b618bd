<template>
  <div class="adv_25_wrap" style="padding-top: 10px;">
    <template v-if="loading">
      <MaskEdit :config="formTitleConfig('顶部标题设置', 'top_title', {content:adv04TitleContent,client:props.client})">
        <div class="floor_title">
          <div class="floor_title_box" v-if="!title_info.title_img">
            <img src="@/assets/images/pc_diy_img/temp_title_left.png" />
            <span class="floor_title_box_span">{{$sldComLanguage('请上传标题')}}</span>
            <img src="@/assets/images/pc_diy_img/temp_title_right.png" />
          </div>
          <img :src="title_info.title_img" alt="" v-else>
        </div>
      </MaskEdit>
      <div class="tab_back">
        <div class="floor_tab_wrapper_scroll" v-if="data.length>0&&data[0].title!=''">
          <div class="floor_tab_wrapper" >
            <template v-for="(item,index) in data" :key="index">
              <div style="margin-right: 10px;height: 58px;" @click="click_tab(index)">
                <MaskEdit
              :config="adv25TitleConfig.data[index]" delBtnType>
                    <div class="floor_tab_li" :class="{'floor_tab_li_active':index==ind}">
                      <div :style="{marginTop:item.subtitle?'8px':'18px'}">{{ item.title }}</div>
                      <p>{{ item.subtitle }}</p>
                    </div>
              </MaskEdit>
              </div>
            </template>
          </div>
        </div>
        <div class="floor_tab_wrapper" v-else>
          <div class="floor_tab_li">
            <div style="margin-top: 8px;">TAB栏标题</div>
            <p>TAB说明栏</p>
          </div>
        </div>
        <div class="tab_back_right" v-if="data.length<30">
          <MaskEdit
          :editBtnType="true"
          :config="formMultipleGoodsConfig(
                $sldComLanguage('添加TAB栏'),
                'tab',{client:props.client})">
          <Button
              key="submit"
              type="primary"
              >
              添加TAB栏
            </Button>
          </MaskEdit>
          
        </div>
        <div v-else>
          <Button
              key="submit"
              type="primary"
              @click="formMultipleTab"
              >
              添加TAB栏
            </Button>
        </div>
      </div>
    </template>
    <div class="floor_goods flex_row_start_center">
      <div v-for="(item, index) in info.length>0?info:flr_default.goods_list" :key="index" class="adv_25_goods">
        <GoodsWrap
          :goods-info="item"
          mode="vertical"
          :imgInfo="imgInfo"
          :gap="12"
          :name-style="goodsNameStyle"
          :price-style="goodsPriceStyle"
        ></GoodsWrap>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { getCurrentInstance, provide, reactive, toRefs, unref,ref,onMounted } from 'vue';
  import GoodsWrap from './components/GoodsWrapAdv.vue';
  import MaskEdit from './components/MaskEdit.vue';
  import { failTip, sucTip } from '/@/utils/utils';
  import { Button } from 'ant-design-vue';
  import { 
    getGoodsGoodsListApi,
    // dev_supplier-start
    getGoodsGoodsSupplierListApi
    // dev_supplier-end
   } from '/@/api/common/common';
  import {
    dfProps,
    dfEmit,
    formTitleConfig,
    createTplContext,
    formMultipleGoodsConfig
  } from './actions/edit_tpl';

  const vm = getCurrentInstance();
  const { $sldComLanguage } = vm?.appContext.config.globalProperties;

  const props = defineProps(dfProps);
  const emit = defineEmits(dfEmit);
  const loading = ref(false);
  const params = ref({pageSize: 5})

  const { title_info, data } = toRefs(props.tpl_info);
  const flr_default = reactive({
    goods_list: Array(5).fill({
      goodsName: $sldComLanguage('商品名称'),
      goodsPrice: 0,
      goodsImage: '',
    }),
  });

  const adv25TitleConfig = reactive({
    data:[]
  })

  const info = ref([])

  const ind = ref(0)

  onMounted(() => {
    getList();
  });

  // 
  const formMultipleTab = ()=> {
    failTip('tab最多添加30个')
  }

  const getList = ()=> {
    if(data.value.length>0){
      if(data.value[0].title!=''){
        let TopTitle = [];
        data.value.forEach((item,index)=>{
          let obj_info = {
            title:'编辑tab栏',
            position: 'tab_index',
            component: 'SldSelMoreMultipleGoods',
            index: index,
            formValue:item
          }
          if(props.client){
            obj_info.client = props.client
          }
          TopTitle.push(obj_info)
        })
        if(data.value[ind.value].qc.showGoods){
          get_goods_list(ind.value)
        }else{
          data.value[ind.value].info = data.value[ind.value].info.filter(item=>!item.state||item.state==3)
          info.value = data.value[ind.value].info
        }
        adv25TitleConfig.data = TopTitle
      }
    }
    loading.value = true
  }

  const imgInfo = {
    width: 172,
    height: 170,
    cover: 'cover',
  };


  const goodsNameStyle = {
    height: '38px',
    maxHeight: '38px',
    marginBottom: '8px',
    color: '#333',
    fontSize: '14px',
  };

  const goodsPriceStyle = {
    width: '100%',
    fontSize: '17px',
  };

  const adv04TitleContent = [
    {
      type: 'upload_img_upload',
      label: '标题图片',
      name: 'title_img',
      initialValue:title_info.value.title_img ? [{
          uid: 'image_1',
          name: 'image.png',
          status: 'done',
          url: title_info.value.title_img,
      }] : [],
      upload_name: 'file',
      upload_url: 'v3/oss/admin/upload?source=setting',
      limit: 10,
      accept: '.jpg, .jpeg, .png',
      extra:'此处建议上传宽260*高30的图片；支持格式gif，jpg，png。',
      rules: [
        {
          required: true,
          message: `请上传标题图片`,
        },
      ],
    },
  ];

  const editConfirmHandler = (values, { modal, position,index,del_type }) => {
    switch (position) {
      case 'top_title': {
        let value = {}
        value.title_img = values[0].title_img[0].url;
        title_info.value = value;
        break;
      }
      
      case 'tab': {
        loading.value = false
        let obj = JSON.parse(JSON.stringify(data.value))
        if(obj.length>0){
          if(obj[0].title==''){
            obj.splice(0,1)
          }
        }
        obj.push(values[0])
        data.value = obj
        let obj_info = {
          title:'编辑tab栏',
          position: 'tab_index',
          component: 'SldSelMoreMultipleGoods',
          index: adv25TitleConfig.data.length,
          formValue:values[0],
        }
        if(props.client){
          obj_info.client = props.client
        }
        adv25TitleConfig.data.push(obj_info)
        if(data.value.length==1){
          if(data.value[0].qc.showGoods){
            get_goods_list(0)
          }
          info.value = data.value[0].info
        }
        loading.value = true
        break;
      }
      case 'tab_index': {
        loading.value = false
        if(del_type){
          data.value.splice(index,1)
          adv25TitleConfig.data.splice(index,1)
          adv25TitleConfig.data.forEach((item,index)=>{
            item.index = index
          })
          if(ind.value==0){
            info.value = []
          }else{
            if(ind.value==index){
              ind.value = ind.value-1
              info.value = data.value[ind.value].info
            }
          }
        }else{
          data.value[index] = values[0]
          adv25TitleConfig.data[index].formValue = values[0]
          if(data.value[ind.value].qc.showGoods){
            get_goods_list(ind.value)
          }else{
            info.value = data.value[ind.value].info
          }
        }
        loading.value = true
        break;
      }
    }
    emit('save_tpl_data', props.tpl_info,props.client);
  };

  
  const get_goods_list = async(indexs)=> {
    let res = null;
    let new_params = data.value[indexs].qc
    // dev_supplier-start
    if(props.client&&props.client=='supplier'){
      res = await getGoodsGoodsSupplierListApi({...new_params,...params.value});
    }else{
    // dev_supplier-end
      res = await getGoodsGoodsListApi({...new_params,...params.value});
    // dev_supplier-start
    }
    // dev_supplier-end
    if (res.state == 200) {
      info.value = res.data.list
    }
  }

  const click_tab = (i)=> {
    if(i!=ind.value){
      ind.value = i
      if(data.value[ind.value].qc.showGoods){
        get_goods_list(ind.value)
      }else{
        info.value = data.value[ind.value].info
      }
    }
  }

  createTplContext(props.mode, editConfirmHandler);
</script>

<style lang="less">
  .adv_25_wrap {
    width: 1210px;
    margin: 0 auto;
  }

  .adv_25_wrap .floor_title {
    position: relative;
    min-height: 36px;
    margin-bottom: 20px;
    overflow: hidden;
    line-height: 35px;
    text-align: center;
    .floor_title_box_span{
      font-size: 24px;
      font-weight: bold;
      color: #333333;
    }
    .floor_title_box{
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 5px 0;
      img{
        width: 38px;
        height: 29px;
        &:first-child{
          margin-right: 23px;
        }
        &:last-child{
          margin-left: 23px;
        }
      }
    }
  }

  .adv_25_wrap .tab_back{
    width: 1210px;
    height: 74px;
    background: #EEEEEE;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    .floor_tab_wrapper_scroll{
      overflow-x: auto;
      overflow-y: hidden;
      height: 74px;
      display: flex;
      z-index: 990;
      align-items: center;
    }
    .floor_tab_wrapper{
      width: 1090px;
      height: 58px;
      display: flex;
      align-items: center;
      padding-left: 10px;
      .floor_tab_li{
        width: 190px;
        height: 58px;
        background: #FFFFFF;
        text-align: center;
        div{
          font-size: 16px;
          font-weight: 700;
          display: inline-block;
          height: 22px;
          line-height: 22px;
          color: #111111;
        }
        p{
          font-size: 14px;
          line-height: 14px;
          margin-top: 5px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #999999;
        }
      }
      .floor_tab_li_active{
        div{
          min-width: 81px;
          background: #f41410;
          color: #fff;
          border-radius: 50px;
          padding: 0 6px;
        }
        p{
         color: #f41410; 
        }
      }
    }
    .tab_back_right{
      padding-right: 10px;
    }
  }

  .adv_25_wrap .floor_title h2 {
    width: 100%;
    margin: 0 auto;
    padding-left: 10px;
    color: #333;
    font-size: 28px;
    line-height: 35px;
  }

  .adv_25_wrap .floor_title h2 font {
    display: inline-block;
    position: relative;
    top: 20px;
    width: 80px;
    height: 1px;
    background: red;
  }

  .adv_25_wrap .floor_title h2 span {
    display: inline-block;
    width: auto;
    min-width: 30px;
    height: 35px;
    margin: 0 20px;
    color: red;
    font-size: 24px;
    font-weight: normal;
    line-height: 35px;
    text-indent: 3px;
    vertical-align: middle;
  }

  .adv_25_wrap .floor_goods {
    position: relative;
    flex-wrap: wrap;
    width: 100%;
  }

  .adv_25_goods {
    width: 234px;
    height: 335px;
    margin: 0 10px 10px 0;
    background-color: #fff;
    box-sizing: border-box;

    &:nth-child(5n + 5) {
      margin-right: 0 !important;
    }
  }

  .adv_25_wrap .floor_goods .item {
    width: 234px;
    height: 310px;
    padding: 24px 31px 26px;
    float: left;
    background-color: #fff;
  }

  .adv_25_wrap .floor_goods .item .adv_25_wrap .floor_goods .item .wrap {
    position: relative;
    width: 172px;
    margin: 0 auto;
    font-size: 14px;
    text-align: center;
  }

  .adv_25_wrap .floor_goods .item .wrap img {
    display: block;
    width: 170px;
    height: 170px;
  }

  .adv_25_wrap .floor_goods .item .wrap .example_text {
    display: block;
    position: relative;
    width: 170px;
    height: 170px;
    background: #eee !important;
    color: #777;
    font-weight: 300;
    text-align: center;
  }

  .adv_25_wrap .floor_goods .item .wrap .example_text span {
    display: block;
    position: absolute;
    top: 50%;
    width: 100%;
    margin-top: -24px;
    font-size: 15px !important;
    line-height: 24px;
  }

  .adv_25_wrap .floor_goods .item .wrap .title {
    display: -webkit-box;
    display: inline-block;
    height: 38px;
    max-height: 38px;
    margin: 26px 0 18px;
    overflow: hidden;
    text-overflow: ellipsis;
    word-wrap: break-word;
    word-break: normal;
    -webkit-line-clamp: 2;

    /*! autoprefixer: off */
    -webkit-box-orient: vertical;
  }

  .adv_25_wrap .floor_goods .item .wrap .title a {
    color: #000;
    font-size: 12px;
    line-height: 19px !important;
  }

  .adv_25_wrap .floor_goods .item .wrap .price {
    color: red;
    font-size: 12px;
    font-weight: bold;
  }

  .adv_25_wrap .floor_goods .item .wrap .price .money_number {
    margin: 0 0 0 3px;
    font-family: Arial, Helvetica, sans-serif;
    font-size: 17px;
    font-weight: bold;
  }
</style>
