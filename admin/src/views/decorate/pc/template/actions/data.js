import { cloneDeep } from "lodash-es";

const basic_data = {
  link_type: '',
  link_value: '',
  info: {},
}




export const left_category_default_data = (rows = 7, sub_category_count = 4) => {
  let category_unit = {
    title: '',
    ...basic_data
  };

  const main_data = Array(rows).fill({}).map(() => ({
    img: {
      imgUrl: '',
      imgPath: '',
    },
    main_category: cloneDeep(category_unit),
    sub_category: new Array(sub_category_count).fill(void 0).map(() => cloneDeep(category_unit))
  }))
  return main_data
}

export const carousel_default_data = (count = 6) => {
  return Array(count).fill({
    imgUrl: '',
    imgPath: '',
    ...basic_data
  })
}

export const pic_title_default_data = (count = 6) => {
  return Array(count).fill({
    imgUrl: '',
    imgPath: '',
    main_title: '',
    ...basic_data
  })
}


