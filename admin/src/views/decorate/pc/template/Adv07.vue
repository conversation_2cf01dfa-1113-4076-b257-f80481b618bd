<template>
  <MaskEdit :config="adv07Config">
    <div class="adv_07_wrap flex_row_center_start">
      <div v-for="(item, index) in imageData" :key="index">
        <ImgWrap :width="403" height="auto" :src="item.imgUrl" mode="cover" />
      </div>
    </div>
  </MaskEdit>
</template>

<script setup>
  import { provide, reactive, ref, toRef, unref } from 'vue';
  import { createTplContext, dfEmit, dfProps, formMoreImgConfig } from './actions/edit_tpl';
  import ImgWrap from './components/ImgWrap.vue';
  import MaskEdit from './components/MaskEdit.vue';
  const props = defineProps(dfProps);
  const emit = defineEmits(dfEmit);
  const imageData = toRef(props.tpl_info, 'data');

  const adv07Config = formMoreImgConfig(
    '编辑三栏广告',
    '',
    {
      content:{
        width: 403,
        height: 0,
        show_width: 403,
        data: unref(imageData),
      },
      client:props.client,
    },
    [
      '一行3张图片，宽度按照指定要求传，高度不限，建议3张图的高度一致',
      '请严格根据提示要求上传规定尺寸的广告图片',
      '编辑项中的“操作”指点击该内容所产生的链接地址，可通过下拉选项选择不同的方式',
    ],
  );

  const editConfirmHandler = (values) => {
    let [value] = values;
    imageData.value = value.parent_data;
    emit('save_tpl_data', props.tpl_info,props.client);
  };
  createTplContext(props.mode, editConfirmHandler);
</script>

<style lang="less" scoped>
  .adv_07_wrap {
    .diy_img_wrap {
      height: 350px;
    }
  }
</style>
