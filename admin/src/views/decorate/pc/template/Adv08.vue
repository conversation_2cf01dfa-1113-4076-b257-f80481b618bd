<template>
  <div class="adv_08_wrap">
    <MaskEdit :config="adv08Config">
      <div class="flex_row_center_start">
        <div v-for="(item, index) in imageData" :key="index" class="fixed_height">
          <ImgWrap :width="242" height="auto" :src="item.imgUrl" mode="cover" />
        </div>
      </div>
    </MaskEdit>
  </div>
</template>

<script setup>
  import { provide, toRef, unref } from 'vue';
  import { createTplContext, dfEmit, dfProps, formMoreImgConfig } from './actions/edit_tpl';
  import ImgWrap from './components/ImgWrap.vue';
  import MaskEdit from './components/MaskEdit.vue';
  const props = defineProps(dfProps);
  const emit = defineEmits(dfEmit);
  const imageData = toRef(props.tpl_info, 'data');

  const adv08Config = formMoreImgConfig(
    '编辑五栏广告',
    '',
    {
      content:{
        width: 242,
        height: 0,
        show_width: 242,
        show_height: 100,
        data: unref(imageData),
      },
      client:props.client,
    },
    [
      '一行5张图片，宽度按照指定要求传，高度不限，建议5张图的高度一致',
      '请严格根据提示要求上传规定尺寸的广告图片',
      '编辑项中的“操作”指点击该内容所产生的链接地址，可通过下拉选项选择不同的方式',
    ],
  );

  const editConfirmHandler = (values) => {
    let [value] = values;
    imageData.value = value.parent_data;
    emit('save_tpl_data', props.tpl_info,props.client);
  };
  createTplContext(props.mode, editConfirmHandler);
</script>

<style lang="less" scoped>
  .fixed_height {
    height: 350px;

    .diy_img_wrap {
      height: 100%;
    }
  }
</style>
