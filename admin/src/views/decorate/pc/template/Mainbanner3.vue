<template>
  <div class="main_banner_3">
    <div>
      <MaskEdit :config="categoryConfig">
        <div class="side_wrapper">
          <div
            class="category_unit flex_row_start_start"
            v-for="(leftItem, leftIdx) in left"
            :key="leftIdx"
          >
            <div class="unit_image">
              <img :src="leftItem.img.imgUrl" alt="" v-show="leftItem.img.imgUrl" />
            </div>
            <div class="unit_text">
              <p>{{ leftItem.main_category.title || '一级分类名称' }}</p>
              <div class="truncate w-[145px]">
                <span
                  v-for="(sub, subIdx) in leftItem.sub_category"
                  :key="subIdx"
                  class="sub_category"
                >
                  <a :class="{ text_active: sub.title }">{{ sub.title || '二级分类名称' }}</a>
                  <span class="mx-[1px] text_seperator">/</span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </MaskEdit>
    </div>
    <div class="carousel_area" :style="computedStyle">
      <MaskEdit :config="bannerConfig">
        <Carousel arrows>
          <div
            class="relative main_banner_item_wrap"
            v-for="(citem, cindex) in center.data"
            :key="cindex"
          >
            <div
              class="banner_img_show"
              :style="{ backgroundImage: `url('${citem.imgUrl}')` }"
            ></div>
          </div>
        </Carousel>
      </MaskEdit>
    </div>
    <div class="img_area">
      <MaskEdit :config="centerRightConfig" class="h-full">
        <div class="flex_column_between_center img_adv_wrapper">
          <div
            v-for="(critem, crIdx) in center_right"
            :key="crIdx"
            :style="computedStyle"
            class="img_sub_wrap"
          >
            <ImgWrap :width="180" :height="146" mode="contain" :src="critem.imgUrl"></ImgWrap>
          </div>
        </div>
      </MaskEdit>
    </div>
    <div class="side_wrapper p-[10px]">
      <div class="login_area flex_column_center_center">
        <div class="avatar"></div>
        <div class="welcome_intro"> Hi~ 欢迎来到{{ basic_site_name }}！</div>
        <div class="flex mt-1">
          <div class="btn_lgn but cursor-pointer">登录</div>
          <span>&nbsp;&nbsp;&nbsp;&nbsp;</span>
          <div class="btn_reg but cursor-pointer">注册</div>
        </div>
      </div>
      <div class="h-px bg-[#EBECED] w-full"></div>
      <div class="my-[10px] link_article">
        <div class="flex justify-between items-center">
          <p class="title">资讯快报</p>
          <div class="more">
            <span>更多</span>
            <span>></span>
          </div>
        </div>
        <div class="mt-[10px]">
          <p v-for="(art, artIdx) in article_list" class="article_item" :key="artIdx">
            <span class="hot_tag">Hot</span>
            <span class="article_title">{{ art.title }}</span>
          </p>
        </div>
      </div>
      <div class="h-px bg-[#EBECED] w-full"></div>
      <div class="mt-[10px]">
        <MaskEdit :config="rightInfoConfig()">
          <div class="flex flex-wrap justify-between">
            <div
              v-for="(item, index) in right"
              class="link_item flex_column_center_center"
              :key="index"
            >
              <div class="info_image">
                <img :src="item.imgUrl" alt="" v-show="item.imgUrl" />
              </div>
              <p :class="{ small: item.main_title.length > 4 }">{{
                item.main_title || $sldComLanguage('标题名称')
              }}</p>
            </div>
          </div>
        </MaskEdit>
      </div>
    </div>
  </div>
</template>

<script setup>
  import MaskEdit from './components/MaskEdit.vue';
  import { Carousel } from 'ant-design-vue';
  import { cloneDeep } from 'lodash-es';
  import { computed, getCurrentInstance, onMounted, reactive, ref, toRefs, unref } from 'vue';
  import { createTplContext, dfEmit, dfProps, formMoreImgConfig } from './actions/edit_tpl';
  import { getBasicSiteSetting } from '/@/api/sysset/sysset';
  import { getArticleList } from '/@/api/manage/manage';
  import ImgWrap from './components/ImgWrap.vue';
  import {
    carousel_default_data,
    left_category_default_data,
    pic_title_default_data,
  } from './actions/data';

  const props = defineProps(dfProps);
  const emit = defineEmits(dfEmit);

  const vm = getCurrentInstance();
  const { $sldComLanguage: $sldComLanguage } = vm?.appContext.config.globalProperties;
  const { left, right, style, center_right } = toRefs(props.tpl_info);
  const center = ref(props.tpl_info.center);

  const bannerSize = {
    type: 'main_banner',
    width: 585,
    height: 457,
    show_width: 195,
    show_height: 152,
  };

  unref(center).data = unref(center).data.concat(
    carousel_default_data(6 - unref(center).data.length),
  );

  left.value = left.value.concat(left_category_default_data(7 - unref(left).length, 4));

  right.value = right.value.concat(pic_title_default_data(6 - unref(right).length));

  center_right.value = center_right.value.concat(
    carousel_default_data(3 - unref(center_right).length),
  );

  const computedStyle = computed(() => {
    let style_s = {};
    Object.keys(unref(style)).forEach((key) => {
      style_s[key] = unref(style)[key] + 'px';
    });
    return style_s;
  });

  const bannerConfig = formMoreImgConfig($sldComLanguage('中间轮播图设置'), 'center', {
    content: {
      show_width: bannerSize.show_width,
      show_height: bannerSize.show_height,
      width: props.tpl_info.center.width,
      height: props.tpl_info.center.height,
      data: unref(center).data,
    },
    client: props.client,
  });

  const categoryConfig = {
    component: 'SldDiyCategory',
    title: '左侧分类设置',
    position: 'left',
    width: 1000,
    client: 'pc',
    imgLayout: {
      width: 19,
      height: 19,
      show_width: 120,
      show_height: 120,
    },
    content: cloneDeep(unref(left)),
  };

  const rightInfoConfig = () => {
    let content = {
      content: {
        width: 28,
        height: 28,
        show_width: 120,
        show_height: 120,
        data: cloneDeep(unref(right)),
      },
      client: props.client,
      imgTitleMaxLength: 5,
    };
    return formMoreImgConfig($sldComLanguage('快捷入口设置'), 'right', content);
  };

  const centerRightConfig = formMoreImgConfig(
    '三图广告设置',
    'center_right',
    {
      content: {
        width: 180,
        height: 146,
        show_width: 200,
        show_height: 166,
        data: cloneDeep(unref(center_right)),
      },
      client: props.client,
    },
    [
      '一行3张图片，宽度按照指定要求传，高度不限，建议3张图的高度一致',
      '请严格根据提示要求上传规定尺寸的广告图片',
      '编辑项中的“操作”指点击该内容所产生的链接地址，可通过下拉选项选择不同的方式',
    ],
  );

  const editConfirmHandler = (values, { position }) => {
    let [value] = values;
    switch (position) {
      case 'left': {
        left.value = value;
        break;
      }
      case 'right': {
        right.value = value.parent_data;
        break;
      }

      case 'center': {
        unref(center).data = value.parent_data;
        vm?.proxy?.$forceUpdate();
        break;
      }

      case 'center_right': {
        center_right.value = value.parent_data;
        break;
      }
    }
    emit('save_tpl_data', props.tpl_info, props.client);
  };

  const basic_site_name = ref('');

  const article_list = ref([]);

  //获取站点基本配置
  const get_base_site = async () => {
    const res = await getBasicSiteSetting();
    if (res.state == 200 && res.data) {
      let basicSiteName = res.data.find((item) => item.name == 'basic_site_name');
      basic_site_name.value = basicSiteName.value;
    }
  };

  const get_article_list = async () => {
    const res = await getArticleList({ pageSize: 20 });
    if (res.state == 200) {
      let list = res.data.list.filter((li) => li.state == 1);
      article_list.value = list.toSorted((a, b) => a.sort - b.sort).slice(0, 4);
    }
  };

  createTplContext(props.mode, editConfirmHandler);
  onMounted(() => {
    get_article_list();
    get_base_site();
  });
</script>

<style lang="less" scoped>
  .main_banner_3 {
    display: flex;
    position: relative;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 1210px;
    margin: 0 auto;
    padding: 10px 0;
  }

  .carousel_area {
    width: 585px;
    margin: 0 10px;
    background: #eee;
  }

  .side_wrapper {
    width: 200px;
    height: 457px;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0 0 10px 0 rgb(120 118 118 / 20%);
  }

  .category_unit {
    padding: 10px;

    .unit_image {
      width: 20px;
      min-width: 20px;
      height: 20px;
      border-radius: 50%;
      background: #eee;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .unit_text {
      flex: 1;
      margin-left: 6px;
      padding-top: 2px;

      p {
        color: #333;
        font-family: 'PingFang SC';
        font-size: 15px;
        font-weight: bold;
        line-height: 21px;
      }

      span {
        height: 13px;
        overflow: hidden;
        color: #888;
        font-family: 'PingFang SC';
        font-size: 13px;
        font-weight: 500;
        text-overflow: ellipsis;
        white-space: nowrap;

        &:last-child {
          .text_seperator {
            display: none;
          }
        }

        a {
          color: #999;
          font-style: normal;

          &.text_active {
            color: #777;
          }
        }
      }
    }
  }

  .img_area {
    height: 457px;
    margin-right: 10px;

    .img_adv_wrapper {
      height: 100%;
    }

    .img_sub_wrap {
      overflow: hidden;
    }
  }

  .login_area {
    margin-bottom: 20px;

    .avatar {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background-color: #efefef;
    }

    .welcome_intro {
      height: 36px;
      margin-top: 11px;
      padding: 0 10px;
      color: #666;
      font-family: 'Microsoft YaHei';
      font-size: 12px;
      font-weight: 400;
      line-height: 15px;
      text-align: center;
      word-break: break-all;
    }

    .but {
      width: 67px;
      height: 24px;
      border-radius: 12px;
      color: #fff;
      font-family: 'Microsoft YaHei';
      font-size: 12px;
      font-weight: 400;
      line-height: 24px;
      text-align: center;

      &.btn_lgn {
        background: #e2231a;
      }

      &.btn_reg {
        background: #fe8c1d;
      }
    }
  }

  .link_article {
    .title {
      color: #333;
      font-family: 'Microsoft YaHei';
      font-size: 14px;
      font-weight: bold;
    }

    .more {
      color: #999;
      font-family: 'Microsoft YaHei';
      font-size: 12px;
      font-weight: 400;
      cursor: pointer;
    }

    .article_item {
      margin: 5px 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      .hot_tag {
        display: inline-block;
        width: 30px;
        min-width: 30px;
        height: 16px;
        border-radius: 3px;
        background: #feebed;
        color: #ed544d;
        font-family: 'Microsoft YaHei';
        font-size: 11px;
        font-weight: 400;
        line-height: 16px;
        text-align: center;
      }

      .article_title {
        color: #666;
        font-family: 'Microsoft YaHei';
        font-size: 12px;
        font-weight: 400;
      }
    }
  }

  .link_item {
    width: 48px;
    margin-bottom: 10px;

    .info_image {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      background: #eee;

      img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }
    }

    p {
      margin-top: 10px;
      color: #333;
      font-family: 'Microsoft YaHei';
      font-size: 12px;
      font-weight: 400;

      &.small {
        font-size: 11px;
        white-space: nowrap;
      }
    }
  }

  .main_banner_item_wrap {
    width: 585px;
    height: 457px;
    overflow: hidden;
  }

  .banner_img_show {
    position: absolute;
    top: 0;
    left: 50%;
    width: 585px;
    height: 457px;
    transform: translateX(-50%);
    background-repeat: no-repeat;
    background-position: 50%;
    background-size: contain;
  }
</style>
