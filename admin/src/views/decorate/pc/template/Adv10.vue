<template>
  <div class="adv_10_wrap">
    <MaskEdit :config="adv10TopImgConfig">
      <div class="row_one flex">
        <ImgWrap
          v-for="(item, index) in row_one.data"
          :key="index"
          :src="item.imgUrl"
          :width="row_one.width"
          :height="row_one.height"
          background="#fff"
        ></ImgWrap>
      </div>
    </MaskEdit>

    <MaskEdit :config="adv10MiddleConfig" class="my-[10px]">
      <div class="row_four flex justify-between">
        <ImgWrap
          v-for="(item, index) in row_four.data"
          :key="index"
          :src="item.imgUrl"
          :width="row_four.width"
          :height="row_four.height"
          mode="contain"
          background="#fff"
        ></ImgWrap>
      </div>
    </MaskEdit>

    <MaskEdit :config="adv10BottomConfig">
      <div class="row_five flex justify-between">
        <ImgWrap
          v-for="(item, index) in row_five.data"
          :key="index"
          :src="item.imgUrl"
          :width="row_five.width"
          :height="row_five.height"
          mode="contain"
          background="#fff"
        ></ImgWrap>
      </div>
    </MaskEdit>
  </div>
</template>

<script setup>
  import { provide, toRefs, unref, reactive } from 'vue';
  import {
    createTplContext,
    dfEmit,
    dfProps,
    formMoreImgConfig,
    formSingleImgConfig,
  } from './actions/edit_tpl';
  import ImgWrap from './components/ImgWrap.vue';
  import MaskEdit from './components/MaskEdit.vue';
  const props = defineProps(dfProps);
  const emit = defineEmits(dfEmit);

  const { row_five, row_four, row_one } = toRefs(props.tpl_info);

  const formMultiImgConfig = (title, position, variant) => {
    let content = {
      content:{
        show_height: variant.height,
        show_width: variant.width,
        height: variant.height,
        width: variant.width,
        data: variant.data,
      },
      client:props.client,
    };
    return formMoreImgConfig(title, position, content);
  };

  const adv10TopImgConfig = formSingleImgConfig('顶部单图设置', 'top_img', {
    content:{
      width: 1210,
      height: 30,
      data: unref(row_one).data[0],
    },
    client:props.client,
  });

  const adv10MiddleConfig = formMultiImgConfig('中间4图设置', 'middle', unref(row_four));

  const adv10BottomConfig = formMultiImgConfig('底部5图设置', 'bottom', unref(row_five));

  const editConfirmHandler = (values, { position }) => {
    let [value] = values;
    switch (position) {
      case 'top_img': {
        unref(row_one).data = values;
        adv10TopImgConfig.content.data = values[0];
        break;
      }
      case 'middle': {
        unref(row_four).data = value.parent_data;
        break;
      }
      case 'bottom': {
        unref(row_five).data = value.parent_data;
        break;
      }
    }
    emit('save_tpl_data', props.tpl_info,props.client);
  };
  createTplContext(props.mode, editConfirmHandler);
</script>

<style lang="less" scoped>
  .adv_10_wrap {
    position: relative;
    width: 100%;
    width: 1210px;
    margin: 0 auto;
    clear: both;
    overflow: hidden;
  }
</style>
