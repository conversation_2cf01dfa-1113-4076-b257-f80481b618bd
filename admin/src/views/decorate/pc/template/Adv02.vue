<template>
  <div class="adv_02_wrap flex_row_center_start">
    <MaskEdit :config="adv02LeftConfig()">
      <ImgWrap
        :width="210"
        :height="344"
        placeholder="icon"
        mode="contain"
        :src="left.data.imgUrl"
      ></ImgWrap>
    </MaskEdit>

    <MaskEdit :config="adv02CenterConfig()">
      <div class="adv_02_center flex flex-wrap">
        <div v-for="(item, index) in center.data.goods_data" :key="index" class="adv_02_goods">
          <Goods
            :goods-info="item"
            placeholder
            mode="horizonal"
            :imgInfo="imgInfo"
            :show-purchase="true"
            :gap="20"
            :name-style="goodsNameStyle"
            :price-style="goodsPriceStyle"
            :show-sale="true"
          ></Goods>
        </div>
      </div>
    </MaskEdit>

    <MaskEdit :config="adv02RightConfig()">
      <div class="adv_02_right">
        <ImgWrap
          :width="253"
          :height="114"
          placeholder="icon"
          :src="item.imgUrl"
          :key="index"
          mode="contain"
          v-for="(item, index) in right.data"
        ></ImgWrap>
      </div>
    </MaskEdit>
  </div>
</template>

<script setup>
  import { provide, reactive, ref, toRefs, unref,getCurrentInstance } from 'vue';
  import Goods from './components/GoodsWrap.vue';
  import ImgWrap from './components/ImgWrap.vue';
  import MaskEdit from './components/MaskEdit.vue';
  import {
    createTplContext,
    dfEmit,
    dfProps,
    formGoodsConfig,
    formMoreImgConfig,
    formSingleImgConfig,
  } from './actions/edit_tpl';
  const vm = getCurrentInstance();

  const props = defineProps(dfProps);
  const emit = defineEmits(dfEmit);

  const flr_default = reactive({
    center_goods_list: Array(4).fill({}),
  });

  const imgInfo = {
    width: 140,
    height: 140,
    cover: 'contain',
  };

  const goodsNameStyle = {
    marginTop: '10px',
    height: '40px',
    fontSize: '13px',
    '-webkit-line-clamp': 2,
  };

  const goodsPriceStyle = {
    margin: '10px 0',
  };

  const { left, right, center } = toRefs(props.tpl_info);

  if (!unref(center).data.goods_data.length) {
    unref(center).data.goods_data = flr_default.center_goods_list;
  }

  //左侧图片设置
  const adv02LeftConfig =()=> formSingleImgConfig('左侧图片设置', 'left', {
    content: {
      type: 'adv_02',
      ...unref(left),
    },
    client: props.client,
  });

  //中间商品编辑配置
  const adv02CenterConfig =()=> formGoodsConfig('商品选择4个', 'center', {
    selectedRow: unref(center).data.goods_data.filter((i) => i.goodsId),
    selectedRowKey: unref(center).data.goods_ids,
    extra: {
      total_num: 4,
    },
    client: props.client,
  });

  //右侧图片编辑配置
  const adv02RightConfig =()=> formMoreImgConfig(
    '设置导航',
    'right',
    {
      content: {
        show_width: 242,
        show_height: 108,
        width: 242,
        height: 108,
        data: unref(right).data,
      },
      client: props.client,
    },
    [
      '请严格根据提示要求上传规定尺寸的广告图片',
      '编辑项中的“操作”指点击该内容所产生的链接地址，可通过下拉选项选择不同的方式',
    ],
  );

  const editConfirmHandler = (values, { modal, position }) => {
    switch (position) {
      case 'center': {
        unref(center).data.goods_data = values[0];
        unref(center).data.goods_ids = values[1];
        vm?.proxy?.$forceUpdate();
        break;
      }

      case 'right': {
        unref(right).data = values[0].parent_data;
        vm?.proxy?.$forceUpdate();
        break;
      }

      case 'left': {
        unref(left).data = values[0];
        vm?.proxy?.$forceUpdate();
        break;
      }
    }
    emit('save_tpl_data', props.tpl_info, props.client);
  };

  createTplContext(props.mode, editConfirmHandler);
</script>

<style lang="scss" scoped>
  .adv_02_wrap {
    width: 1210px;
    margin: 0 auto;

    .adv_02_center {
      width: 736px;
      background: #fff;
    }

    .adv_02_goods {
      width: 368px;
      height: 172px;
      padding: 10px;
      border-color: #f0f2f3;

      &:nth-child(4) {
        border-width: 1px 0 0 1px;
      }

      &:nth-child(1) {
        border-width: 0 1px 1px 0;
      }
    }

    .adv_02_right {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: center;
      width: 253px;
      height: 344px;
    }

    .floor_title {
      height: 45px;

      .main_title {
        color: #3d3d3d;
        font-size: 20px;
        font-weight: bold;
      }

      .sub_title {
        margin: 0 0 0 10px;
        color: #a4a4a4;
        font-size: 16px;
        font-style: italic;
      }

      .right_action {
        margin: 0 12px 0 0;
        color: #333;
        font-size: 14px;
      }
    }

    .floor_content {
      .floor_left {
        .floor_img {
          //
        }

        .floor_words {
          position: absolute;
          z-index: 11;
          bottom: 20px;
          left: 20px;
          width: 260px;
          height: 160px;
          padding: 10px 30px;
          padding-bottom: 0;
          background: #fff;

          .floor_words_top_title {
            height: 30px;

            span {
              margin: 0 9px;
              color: #101010;
              font-size: 14px;
              font-weight: bold;
            }

            em {
              display: inline-block;
              width: 39px;
              height: 1px;
              background: #d4d4d4;
            }
          }

          .cat_data_wrap {
            width: 200px;
            height: 86px;
            margin-bottom: 0;

            li {
              width: 60px;
              margin: 0 3px;
              margin-bottom: 12px;
              float: left;
              color: #6d6d6d;
              font-size: 12px;
              font-weight: 400;
            }
          }
        }
      }

      .floor_border_top {
        border-top: 2px solid #fc5863;
      }

      .floor_middle {
        background-color: #fff;

        .floor_goods_item {
          border-width: 0 1px 1px 0;
          border-style: solid;
          border-color: #e7e7e7;

          &.big_item {
            display: flex;
            align-items: center;
            width: 440px;
            height: 240px;
            padding: 0 30px;
          }

          &.small_item {
            width: 220px;
            padding: 10px;
          }
        }
      }

      .floor_right {
        position: relative;
        width: 250px;
        height: 480px;
        padding: 5px 10px 0;

        .floor_right_new_top_title {
          position: relative;
          height: 30px;
          margin: 0 0 6px;
          overflow: hidden;
          line-height: 30px;
          text-align: center;

          em {
            display: inline-block;
            width: 39px;
            height: 1.5px;
            background: #fc585a;
          }

          span {
            margin: 0 9px;
            color: #fc585a;
            font-size: 14px;
            font-weight: bold;
          }
        }

        .floor_goods_right_item {
          padding: 10px 0;
          border-width: 0 0 1px;
          border-style: solid;
          border-color: #e7e7e7;
        }
      }
    }
  }
</style>
