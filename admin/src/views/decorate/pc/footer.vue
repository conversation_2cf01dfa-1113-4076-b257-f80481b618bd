<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <SldComHeader :type="1" title="页脚管理" />
      <div style="width: 100%; height: 5px"></div>
      <BasicTable @register="standardTable">
        <template #tableTitle>
          <div class="toolbar flex flex-row">
            <div class="toolbar_btn" @click="handleClick(null, 'add')">
              <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#fff" />
              <span>新增页脚</span>
            </div>

            <div class="ml-2">
              <InputSearch
                v-model:value="linkName"
                placeholder="请输入名称"
                enter-button="搜索"
                class="text-xs"
                @search="onSearch"
                :allow-clear="true"
              ></InputSearch>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key == 'action'">
            <div class="flex flex-row items-center justify-center">
              <span
                class="common_page_edit cursor-pointer hover:text-[#FF6A12]"
                @click="handleClick(record, 'edit')"
                >编辑</span
              >
              <Popconfirm
                title="删除后不可恢复，是否确定删除？"
                @confirm="handleClick(record, 'del')"
              >
                <span class="common_page_edit cursor-pointer hover:text-[#FF6A12]">删除</span>
              </Popconfirm>
            </div>
          </template>
        </template>
      </BasicTable>

      <SldModal
        v-bind="modalState"
        @confirm-event="handleConfirm"
        @cancle-event="modalState.visible = false"
      />
    </div>
  </div>
</template>

<script setup>
  import { BasicTable, useTable } from '/@/components/Table';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { getLinkList, editLink, addLink, delLink } from '@/api/decorate/deco_pc';
  import SldModal from '/@/components/SldModal/index.vue';
  import { Popconfirm, InputSearch } from 'ant-design-vue';
  import { reactive, ref } from 'vue';
  import { sucTip, failTip } from '/@/utils/utils';

  const footer_columns = [
    { title: '名称', dataIndex: 'linkName', width: 300 },
    { title: '链接', dataIndex: 'linkUrl' },
    { title: '排序', dataIndex: 'sort' },
    { title: '创建时间', dataIndex: 'createTime' },
  ];

  const linkName = ref('');

  const content = ref([
    {
      type: 'input',
      label: '标题',
      name: 'linkName',
      placeholder: '请输入标题',
      extra: '最多输入20个字',
      maxLength: 20,
      showCount: true,
      rules: [
        {
          required: true,
          whitespace: true,
          message: '请输入标题',
        },
      ],
      initialValue: '',
    },
    {
      type: 'input',
      label: '链接',
      name: 'linkUrl',
      placeholder: '请输入链接',
      extra: '链接应已http://或者https://开头',
      rules: [
        {
          required: true,
          whitespace: true,
          message: '请输入链接',
        },
      ],
      initialValue: '',
    },
    {
      type: 'inputnum',
      label: '排序',
      name: 'sort',
      placeholder: '请输入排序',
      extra: '请输入0~255的数字，数字越小，该项显示将越靠前',
      rules: [
        {
          required: true,
          message: '请输入排序',
        },
        {
          validator: (rules, value) => {
            if (!(value >= 0 && value <= 255)) {
              return Promise.reject('请输入0~255的数字');
            }
            return Promise.resolve();
          },
        },
      ],
      initialValue: '',
    },
  ]);

  const modalState = reactive({
    width: 600,
    title: '新增装修页面',
    visible: false,
    content,
    confirmBtnLoading: false,
    showFoot: true,
    type: 'add',
    linkId: 0,
  });

  const handleConfirm = async (params, resetFields) => {
    modalState.confirmBtnLoading = true;
    const res = await (modalState.type == 'add'
      ? addLink({ ...params, showType: 1 })
      : editLink({ ...params, showType: 1, linkId: modalState.linkId }));
    modalState.confirmBtnLoading = false;
    if (res?.state == 200) {
      resetFields();
      sucTip(res.msg);
      modalState.visible = false;
      reload();
    } else {
      failTip(res.msg);
    }
  };

  const [standardTable, { reload, setLoading }] = useTable({
    api: getLinkList,
    columns: footer_columns,
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    actionColumn: {
      width: 200,
      title: '操作',
      dataIndex: 'action',
      align: 'center',
    },
    pagination: {
      pageSize: 10,
    },
    useSearchForm: false,
    bordered: true,
    striped: false,
    beforeFetch(params) {
      let targetParams = params;
      if (linkName.value) {
        targetParams.linkName = linkName.value;
      }
      return targetParams;
    },
  });

  const handleClick = (record, type) => {
    if (type == 'del') {
      setLoading(true);
      delLink({ linkIds: record.linkId }).then((res) => {
        setLoading(false);
        if (res.state == 200) {
          reload();
          sucTip(res.msg);
        } else {
          failTip(res.msg);
        }
      });
    } else if (type == 'add') {
      clearContent();
      modalState.title = '新增页脚';
      modalState.visible = true;
      modalState.type = 'add';
    } else if (type == 'edit') {
      modalState.title = '编辑页脚';
      let { sort, linkName, linkUrl } = record;
      let arr = [linkName, linkUrl, sort];
      content.value.map((item, index) => (item.initialValue = arr[index]));
      modalState.type = 'edit';
      modalState.visible = true;
      modalState.linkId = record.linkId;
    }
  };

  const clearContent = () => {
    content.value.map((item) => (item.initialValue = ''));
  };

  const onSearch = () => {
    reload();
  };
</script>

<style scoped>
  .diy_input {
    font-size: 13px;
  }
</style>
