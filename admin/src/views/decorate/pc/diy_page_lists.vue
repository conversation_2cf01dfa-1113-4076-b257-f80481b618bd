<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <SldComHeader :type="1" title="首页装修列表管理" />
      <div style="width: 100%; height: 5px"></div>
      <BasicTable @register="standardTable">
        <template #tableTitle>
          <div class="toolbar">
            <div class="toolbar_btn" @click="handleClick(null, 'add')">
              <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#fff" />
              <span>新增页面</span>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.key == 'action'">
            <div class="flex flex-row items-center justify-center">
              <span
                class="common_page_edit cursor-pointer hover:text-[#FF6A12]"
                @click="handleClick(record, 'diy')"
                >装修</span
              >
              <Popconfirm
                title="删除后不可恢复，是否确定删除？"
                @confirm="handleClick(record, 'del')"
              >
                <span class="common_page_edit cursor-pointer hover:text-[#FF6A12]">删除</span>
              </Popconfirm>

              <span
                class="common_page_edit cursor-pointer hover:text-[#FF6A12]"
                @click="handleClick(record, 'copy')"
                >复制</span
              >

              <span
                class="common_page_edit cursor-pointer hover:text-[#FF6A12]"
                @click="handleClick(record, 'preview')"
                >预览</span
              >
            </div>
          </template>
          <template v-if="column.key == 'isEnable'">
            <Switch :checked="record.isEnable == 1" @change="switchChange(record)"></Switch>
          </template>
          <template v-if="column.key == 'decoName'">
            <div class="flex flex-row justify-between items-center" v-if="record.is_edit">
              <Input v-model:value="record.edit_name" :maxLength="8" class="diy_input"></Input>
              <div class="w-10px"></div>
              <div class="flex flex-row items-center">
                <AliSvgIcon
                  iconName="iconxuanzhong"
                  width="16px"
                  height="16px"
                  fillColor="#FA6F1E"
                  class="cursor-pointer"
                  @click="setEditDecoName(record, 'confirm')"
                ></AliSvgIcon>
                <AliSvgIcon
                  iconName="iconqingchu"
                  width="16px"
                  height="16px"
                  fillColor="#FA6F1E"
                  class="cursor-pointer ml-2"
                  @click="setEditDecoName(record, 'close')"
                ></AliSvgIcon>
              </div>
            </div>
            <div class="flex flex-row justify-between" v-else>
              <div>{{ text }}</div>
              <AliSvgIcon
                iconName="iconedit"
                width="16px"
                height="16px"
                fillColor="#FA6F1E"
                class="cursor-pointer"
                @click="openEditDecoName(record)"
              ></AliSvgIcon>
            </div>
          </template>
        </template>
      </BasicTable>

      <SldModal
        v-bind="modalState"
        @confirm-event="(e) => handleModalEvent(e, 'confirm')"
        @cancle-event="handleModalEvent({}, 'cancel')"
      />
    </div>
  </div>
</template>

<script setup>
  import { BasicTable, useTable } from '/@/components/Table';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { useRouter } from 'vue-router';
  import { AliSvgIcon } from '/@/components/SvgIcon';
  import {
    getDecoList,
    UpdateDecoApi,
    updateIsEnabled,
    decoDelete,
    decoCopy,
    decoAdd,
  } from '@/api/decorate/deco_pc';
  import SldModal from '/@/components/SldModal/index.vue';
  import { Popconfirm, Input, Switch } from 'ant-design-vue';
  import { reactive } from 'vue';
  import { sucTip, failTip } from '/@/utils/utils';
  const router = useRouter();
  const deco_columns = [
    { title: '名称', dataIndex: 'decoName', width: 300 },
    { title: '创建时间', dataIndex: 'createTime' },
    { title: '修改时间', dataIndex: 'updateTime' },
    { title: '启用状态', dataIndex: 'isEnable' },
  ];

  const content = [
    {
      type: 'input',
      label: '装修页面名称',
      name: 'decoName',
      placeholder: '请输入装修页面名称',
      extra: '最多输入8个字',
      maxLength: 8,
      showCount: true,
      rules: [
        {
          required: true,
          whitespace: true,
          message: '请输入装修页面名称',
        },
      ],
      initialValue: '',
    },
  ];

  const modalState = reactive({
    width: 600,
    title: '新增装修页面',
    visible: false,
    content,
    confirmBtnLoading: false,
    showFoot: true,
  });

  const handleModalEvent = async (params, type) => {
    if (type == 'confirm') {
      modalState.confirmBtnLoading = true;
      const res = await decoAdd({ ...params, decoType: 'index' });
      modalState.confirmBtnLoading = false;
      if (res?.state == 200) {
        modalState.visible = false;
        modalState.content[0].initialValue = void 0;

        reload();
      } else {
        failTip(res.msg);
      }
    } else {
      modalState.visible = false;
      modalState.content[0].initialValue = void 0;
    }
  };

  const [standardTable, { reload, setLoading }] = useTable({
    api: getDecoList,
    columns: deco_columns,
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    actionColumn: {
      width: 200,
      title: '操作',
      dataIndex: 'action',
      align: 'center',
    },
    pagination: {
      pageSize: 10,
    },
    useSearchForm: true,
    formConfig: {
      schemas: [
        {
          field: 'decoName',
          component: 'Input',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
          componentProps: {
            placeHolder: '请输入页面名称',
            minWidth: 300,
            maxlength: 6,
            size: 'default',
          },
          label: '页面名称',
          labelWidth: 80,
        },

        {
          field: 'isEnable',
          component: 'Select',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
          componentProps: {
            placeHolder: '请选择使用状态',
            minWidth: 300,
            maxlength: 6,
            size: 'default',
            options: [
              { label: '全部', value: -1 },
              { label: '启用', value: 1 },
              { label: '禁用', value: 0 },
            ],
          },
          label: '使用状态',
          labelWidth: 80,
        },
      ],
    },
    searchInfo: {
      decoType: 'index',
    },
    bordered: true,
    striped: false,
    beforeFetch({ isEnable, ...params }) {
      let targetParams = params;
      if (isEnable >= 0) {
        targetParams.isEnable = isEnable;
      }
      return targetParams;
    },
  });

  const handleClick = (record, type) => {
    if (type == 'del') {
      setLoading(true);
      decoDelete({ decoId: record.decoId }).then((res) => {
        setLoading(false);
        if (res.state == 200) {
          reload();
          sucTip(res.msg);
        } else {
          failTip(res.msg);
        }
      });
    } else if (type == 'copy') {
      setLoading(true);
      decoCopy({ decoId: record.decoId }).then((res) => {
        setLoading(false);
        if (res.state == 200) {
          reload();
          sucTip(res.msg);
        } else {
          failTip(res.msg);
        }
      });
    } else if (type == 'preview') {
      window.open(record.pcUrl);
    } else if (type == 'add') {
      modalState.visible = true;
    } else if (type == 'diy') {
      let newWin = router.resolve({
        path: 'diy_page_lists_to_edit',
        query: {
          id: record.decoId,
          type: 'index',
        },
      });
      window.open(newWin.href);
    }
  };

  const switchChange = async (record) => {
    const res = await updateIsEnabled({
      decoId: record.decoId,
      isEnable: record.isEnable != 1,
    });
    if (res?.state == 200) {
      reload();
      sucTip(res.msg);
    } else {
      failTip(res.msg);
    }
  };

  const openEditDecoName = (record) => {
    record.is_edit = true;
    record.edit_name = record.decoName;
  };

  const setEditDecoName = (record, type) => {
    if (type == 'close') record.is_edit = false;
    if (type == 'confirm') {
      setLoading(true);
      UpdateDecoApi({ decoId: record.decoId, decoName: record.edit_name }).then((res) => {
        setLoading(false);
        if (res.state == 200) {
          record.is_edit = false;
          record.decoName = record.edit_name;
          sucTip(res.msg);
        } else {
          failTip(res.msg);
        }
      });
    }
  };
</script>

<style scoped>
  .diy_input {
    font-size: 13px;
  }
</style>
