<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <SldComHeader
        clickFlag
        :type="2"
        @handleToggleTip="handleToggleTip"
        title="首页导航设置"
        tip-title="操作提示"
        :tip-data="tipData"
      />
      <div style="width: 100%; height: 5px"></div>
      <BasicTable @register="standardTable">
        <template #tableTitle>
          <div class="toolbar flex flex-row">
            <div class="toolbar_btn" @click="handleClick(null, 'add')">
              <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#fff" />
              <span>新增导航</span>
            </div>

            <div class="ml-2">
              <InputSearch
                v-model:value="navName"
                placeholder="请输入导航名称"
                enter-button="搜索"
                class="text-xs"
                @search="onSearch"
                :allow-clear="true"
              />
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key == 'action'">
            <div class="flex flex-row items-center justify-center">
              <span
                class="common_page_edit cursor-pointer hover:text-[#FF6A12]"
                @click="handleClick(record, 'edit')"
                >编辑</span
              >
              <Popconfirm
                title="删除后不可恢复，是否确定删除？"
                @confirm="handleClick(record, 'del')"
              >
                <span class="common_page_edit cursor-pointer hover:text-[#FF6A12]">删除</span>
              </Popconfirm>
            </div>
          </template>
          <template v-else-if="column.key == 'isShow'">
            <Switch :checked="record.isShow == 1" @change="switchChange(record)" />
          </template>
          <template v-else-if="column.key == 'link_type' || column.key == 'link_value'">
            <span>{{ handleParse(record.data, column.key) }}</span>
          </template>
        </template>
      </BasicTable>

      <SldModal
        v-bind="modalState"
        :content="operateContent"
        @confirm-event="handleConfirm"
        @cancle-event="handleCancle"
        @callbackEvent="callbackEvent"
      />

      <SldSelGoodsSingleDiy
        v-bind="modalProps"
        @confirm-event="tableConfirm"
        @cancle-event="tableCancle"
      />
    </div>
  </div>
</template>

<script setup>
  import { diy_link_type, sucTip, failTip } from '@/utils/utils';
  import { navDel, getNavList, navIsShow, navAdd, navEdit } from '@/api/decorate/deco_pc';
  import { Popconfirm, Switch, InputSearch } from 'ant-design-vue';
  import { reactive, unref, ref } from 'vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { SldSelGoodsSingleDiy, setSldSelProps } from '/@/components/SldSelGoodsSingleDiy';
  import SldModal from '/@/components/SldModal/index.vue';
  /// @ts-ignore
  const navName = ref('');

  const tipData = [
    '序号越小，显示越靠前',
    'PC商城首页展示一行，超出部分不展示，需要根据页面效果调整展示的数据',
  ];

  const deco_columns = [
    { title: '导航名称', dataIndex: 'navName', width: 300 },
    { title: '排序', dataIndex: 'sort' },
    { title: '链接类型', dataIndex: 'link_type' },
    { title: '跳转目标', dataIndex: 'link_value' },
    { title: '启用状态', dataIndex: 'isShow' },
  ];

  //SldSelGoodsSingleDiy组件传的Props
  const modalProps = reactive({
    // @ts-ignore
    api: (...arg) => {},
    modalTitle: '',
    modalVisible: false,
    column: [],
    formConfig: [],
    selectedRows: [],
    selectedRowKeys: [],
    searchInfo: {},
    showHeader: true,
    showIndex: true,
    look: false,
    link_type: '',
    rowKey: '',
  });

  let current_link_type = '';

  // 新增导航的content
  const content = ref([
    {
      type: 'input',
      label: '导航名称',
      name: 'navName',
      placeholder: '请输入导航名称',
      extra: '最多输入6个字',
      maxLength: 6,
      showCount: true,
      rules: [
        {
          required: true,
          whitespace: true,
          message: '请输入导航名称',
        },
      ],
      initialValue: '',
    },
    {
      type: 'inputnum',
      label: '排序',
      name: 'sort',
      placeholder: '请输入排序',
      extra: '请输入0~255的数字，值越小，显示越靠前',
      rules: [
        {
          required: true,
          message: '请输入0~255的数字',
          validator: (rules, value) => {
            if (!(value >= 0 && value <= 255)) {
              return Promise.reject('请输入0~255的数字');
            }
            return Promise.resolve();
          },
        },
      ],
      initialValue: '',
    },
    {
      type: 'radio_select',
      label: '是否显示',
      name: 'isShow',
      data: [
        {
          key: 1,
          value: '是',
        },
        {
          key: 0,
          value: '否',
        },
      ],
      initialValue: 1,
    },
    {
      type: 'select',
      label: '链接类型',
      name: 'link_type',
      selData: diy_link_type(),
      initialValue: '',
      selectCallback: true,
    },
  ]);
  const operateContent = ref([]);

  const modalState = reactive({
    width: 600,
    title: '新增导航',
    visible: false,
    confirmBtnLoading: false,
    showFoot: true,
    modalType: 'add',
    navId: 0,
  });

  //往接口传参的Props
  const addNavProps = {
    navName: '',
    sort: '',
    isShow: 1,
    data: '',
  };

  //清除内容
  const clearContent = () => {
    operateContent.value.splice(4, 1);
    operateContent.value.map((item) =>
      item.name == 'isShow' ? (item.initialValue = 1) : (item.initialValue = ''),
    );
  };

  //添加新导航确认事件
  const handleConfirm = async (param, resetFields) => {
    modalProps.modalVisible = false;
    let params = {};
    params.navName = param.navName;
    delete param.navName;
    params.sort = param.sort;
    delete param.sort;
    params.isShow = param.isShow;
    delete param.isShow;
    params.data = addNavProps.data
      ? JSON.stringify(addNavProps.data)
      : JSON.stringify({ ...param });
    modalState.confirmBtnLoading = true;
    const res = await (modalState.modalType == 'add'
      ? navAdd(params)
      : navEdit({ ...params, navId: modalState.navId }));
    modalState.confirmBtnLoading = false;
    if (res?.state == 200) {
      sucTip(res.msg);
      modalState.visible = false;
      operateContent.value = [];
      modalState.navId = 0;
      resetFields();
      reload();
    } else {
      failTip(res.msg);
    }
  };

  const handleCancle = () => {
    modalState.visible = false;
    operateContent.value = [];
  };

  const [standardTable, { reload, setLoading, redoHeight }] = useTable({
    api: getNavList,
    columns: deco_columns,
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    actionColumn: {
      width: 200,
      title: '操作',
      dataIndex: 'action',
      align: 'center',
    },
    pagination: {
      pageSize: 10,
    },
    bordered: true,
    striped: false,
    canResize: true,
    beforeFetch(params) {
      let targetParams = params;
      if (unref(navName)) {
        targetParams.navName = unref(navName);
      }
      return targetParams;
    },
  });

  //新增导航，编辑导航，删除导航事件
  const handleClick = (record, type) => {
    if (type == 'del') {
      setLoading(true);
      navDel({ navId: record.navId }).then((res) => {
        setLoading(false);
        if (res.state == 200) {
          reload();
          sucTip(res.msg);
        } else {
          failTip(res.msg);
        }
      });
    } else if (type == 'add') {
      operateContent.value = JSON.parse(JSON.stringify(content.value));
      clearContent();
      modalState.visible = true;
      modalState.modalType = 'add';
    } else if (type == 'edit') {
      operateContent.value = JSON.parse(JSON.stringify(content.value));
      clearContent();
      let { navName, isShow, data, sort, navId } = record;
      let { link_type, link_value } = JSON.parse(data);
      modalState.navId = navId;
      operateContent.value.map((item) => {
        if (item.name == 'navName') item.initialValue = navName;
        if (item.name == 'isShow') item.initialValue = isShow;
        if (item.name == 'sort') item.initialValue = sort;
        if (item.name == 'link_type') item.initialValue = link_type;
      });
      if (link_value) {
        addContent(link_type, false, link_value);
      }
      modalState.modalType = 'edit';
      modalState.visible = true;
    }
  };

  const handleToggleTip = () => {
    redoHeight();
  };

  const switchChange = async (record) => {
    const res = await navIsShow({
      navId: record.navId,
      isShow: record.isShow == 1 ? 0 : 1,
    });
    if (res?.state == 200) {
      reload();
      sucTip(res.msg);
    } else {
      failTip(res.msg);
    }
  };

  const handleParse = (recordData, prop) => {
    let tmp_data = recordData ? JSON.parse(recordData.replace(/&quot;/g, '"')) : '';
    if (prop == 'link_type') {
      let data = diy_link_type().filter((item) => item.key == tmp_data.link_type)[0];
      return data != undefined ? data.name : '--';
    } else {
      return tmp_data.link_value ? tmp_data.link_value : '--';
    }
  };

  //选择器选择到需要弹窗的选项时的设置属性
  const setModalProps = (type) => {
    modalProps.searchInfo = {};
    modalProps.link_type = type;
    let props = setSldSelProps(type, 'default', 'pc');
    Object.keys(props).forEach((key) => {
      modalProps[key] = props[key];
    });
    modalProps.modalVisible = true;
  };

  //追加内容选择器选择的回调
  const callbackEvent = ({ val1, from_info }) => {
    current_link_type = val1;
    operateContent.value = from_info;
    addContent(val1, true);
  };

  //新增页面content追加内容
  const addContent = (type, modalFlag, value = '') => {
    let extraContent = {};
    if (type == 'url') {
      extraContent = {
        type: 'input',
        label: `${'链接地址'}`,
        name: 'link_value',
        placeholder: `${'请输入'}${'链接地址'}`,
        initialValue: value,
        rules: [
          {
            required: true,
            whitespace: true,
            message: `${'请输入'}${'链接地址'}`,
          },
        ],
      };
    } else if (type == 'keyword') {
      extraContent = {
        type: 'input',
        label: `${'关键字'}`, //关键字
        name: 'link_value',
        placeholder: `${'请输入'}${'关键字'}`,
        initialValue: value,
        rules: [
          {
            required: true,
            whitespace: true,
            message: `${'请输入'}${'关键字'}`,
          },
        ],
      };
    } else if (type == 'goods') {
      extraContent = {
        type: 'input',
        label: `${'商品名称'}`,
        name: 'link_value',
        placeholder: `${'请输入'}${'商品名称'}`,
        initialValue: value,
        disable: true,
      };
      setModalProps('goods');
      modalProps.modalVisible = modalFlag;
    } else if (type == 'topic') {
      extraContent = {
        type: 'input',
        label: `${'专题名称'}`,
        name: 'link_value',
        placeholder: `${'请输入'}${'专题名称'}`,
        initialValue: value,
        disable: true,
      };
      setModalProps('topic');
      modalProps.modalVisible = modalFlag;
    } else if (type == 'store') {
      extraContent = {
        type: 'input',
        label: `店铺名称`,
        name: 'link_value',
        placeholder: `请输入店铺名称`,
        initialValue: value,
        disable: true,
      };
      setModalProps('store');
      modalProps.modalVisible = modalFlag;
    } else if (type == 'category') {
      extraContent = {
        type: 'input',
        label: `分类名称`,
        name: 'link_value',
        placeholder: `请输入分类名称`,
        initialValue: value,
        disable: true,
      };
      setModalProps('category');
      modalProps.modalVisible = modalFlag;
    }
    addNavProps.data = '';
    // @ts-ignore
    operateContent.value.splice(4, 1, extraContent);
  };

  //SldSelGoodsSingleDiy 取消事件
  const tableCancle = () => {
    operateContent.value[3].initialValue = '';
    if (operateContent.value.length > 4) {
      operateContent.value.splice(4, 1);
    }
    modalProps.modalVisible = false;
  };

  //SldSelGoodsSingleDiy 确认事件
  const tableConfirm = (e) => {
    if (current_link_type == 'goods') {
      operateContent.value[4].initialValue = e[0]?.goodsName;
      addNavProps.data = {
        link_type: 'goods',
        link_value: e[0]?.goodsName,
        info: {
          id: e[0]?.goodsId,
          gid: e[0]?.productId,
          defaultProductId: e[0]?.productId,
        },
      };
    } else if (current_link_type == 'topic') {
      operateContent.value[4].initialValue = e[0]?.decoName;
      addNavProps.data = {
        link_type: 'topic',
        link_value: e[0]?.decoName,
        info: {
          id: e[0]?.decoId,
          decoId: e[0]?.decoId,
        },
      };
    } else if (current_link_type == 'store') {
      operateContent.value[4].initialValue = e[0]?.storeName;
      addNavProps.data = {
        link_type: 'store',
        link_value: e[0]?.storeName,
        info: {
          storeId: e[0]?.storeId,
          isOwnStore: e[0]?.isOwnStore,
        },
      };
    } else if (current_link_type == 'category') {
      operateContent.value[4].initialValue = e?.categoryName;
      addNavProps.data = {
        link_type: 'category',
        link_value: e?.categoryName,
        info: {
          id: e?.categoryId,
          categoryId: e?.categoryId,
          grade: e?.grade,
        },
      };
    }
    modalProps.modalVisible = false;
  };

  const onSearch = () => {
    reload();
  };
</script>

<style lang="less">
  .toolbar {
    .ant-input-wrapper {
      height: 28px !important;
    }

    .ant-input-affix-wrapper {
      border: none;
    }

    .ant-input {
      font-size: 13px;
    }

    .ant-input-search-button,
    .ant-input-group-addon {
      height: 31px !important;
    }
  }

  .diy_input {
    font-size: 13px;
  }
</style>
