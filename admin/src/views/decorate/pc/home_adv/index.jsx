import { TabPane, Tabs } from 'ant-design-vue';
import { defineComponent, ref, unref } from 'vue';
import SldComHeader from '@/components/SldComHeader/index.vue';
import OpenScreen from './OpenScreen.vue';
import TopAdv from './TopAdv.vue';
import PageWrapper from '/@/components/PageWrapper/index.vue';

export default defineComponent({
  setup() {
    const activeKey = ref(1);
    return () => (
      <div class="section_padding">
        <div class='section_padding_back_add'>

          <SldComHeader title="首页广告设置"></SldComHeader>
          <Tabs type="card" activeKey={unref(activeKey)} onChange={(key) => (activeKey.value = key)}>
            <TabPane tab="开屏图设置" key={1}>
              <OpenScreen></OpenScreen>
            </TabPane>
            <TabPane tab="顶部广告设置" key={2}>
              <TopAdv></TopAdv>
            </TabPane>
          </Tabs>
        </div>
      </div>
    );
  },
});
