<template>
  <div class="prew_item_o2o prew_item_goods_o2o" :style="{ backgroundImage: `url(${backgroundImage})` }">
    <div class="flex_row_start_start diy_style_price" :style="{ color: currentColor.priceColor }">
      ￥<span>19</span>.00
    </div>
    <div class="diy_style_origin_price">￥399.00</div>
    <div class="diy_style_location" :style="{ background: currentColor.mainColor }">
      <img src="@/assets/images/o2o_diy_style/lightning.png" alt="" />
      <span>小时达</span>
    </div>
    <div class="diy_style_cart_num" :style="{ background: currentColor.mainColor }">6</div>
    <div class="diy_style_add" :style="{ background: currentColor.subColor }">加入购物车</div>
    <div class="diy_style_buy" :style="{ background: currentColor.mainLinearColor }">立即购买</div>
  </div>
</template>

<script>
  import { defineComponent } from 'vue';
  import { getImagePath } from '/@/utils';
  export default defineComponent({
    props: {
      currentColor: Object,
    },
    setup(props) {
      const backgroundImage = getImagePath('images/o2o_diy_style/diy_style_goods.png');
      return { backgroundImage };
    },
  });
</script>

<style lang="less" scoped>
  .prew_item_o2o {
    position: relative;
    width: 375px;
    height: 667px;
    margin-right: 50px;
    overflow: hidden;
    border-radius: 10px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    box-shadow: 0 0 12px 0 rgb(129 129 129 / 30%);
    cursor: default;

    &:last-of-type {
      margin-right: 0;
    }

    &.prew_item_goods_o2o {
      .diy_style_price {
        position: absolute;
        z-index: 2;
        top: 442px;
        left: 6px;
        align-items: baseline;
        font-family: 'PingFang SC';
        font-size: 14px;
        font-weight: 700;

        span {
          font-size: 24px;
        }
      }

      .diy_style_origin_price {
        position: absolute;
        z-index: 2;
        top: 408px;
        left: 8px;
        color: #999;
        font-size: 12px;
        text-decoration: line-through;
      }

      .diy_style_location {
        position: absolute;
        z-index: 2;
        top: 499px;
        left: 13px;
        width: 53px;
        height: 16px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        padding-left: 2px;
        img{
          width: 10px;
          height: 13px;
        }
        span{
          font-weight: bold;
          font-size: 12px;
          color: #FFFFFF;
        }
      }

      .diy_style_cart_num {
        position: absolute;
        z-index: 2;
        bottom: 28px;
        left: 122px;
        width: 15px;
        height: 15px;
        transform: scale(0.9);
        border-radius: 50%;
        color: #fff;
        font-size: 13px;
        line-height: 15px;
        text-align: center;
      }

      .diy_style_add,
      .diy_style_buy {
        position: absolute;
        z-index: 2;
        bottom: 6px;
        width: 111px;
        height: 35px;
        color: #fff;
        font-size: 15px;
        display: flex;
        align-items: center;
        justify-content: center
      }

      .diy_style_add {
        right: 119px;
        border-top-left-radius: 18px;
        border-bottom-left-radius: 18px;
      }

      .diy_style_buy {
        right: 8px;
        border-top-right-radius: 18px;
        border-bottom-right-radius: 18px;
      }
    }
  }
</style>
