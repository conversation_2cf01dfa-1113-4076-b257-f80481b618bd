<template>
  <div class="prew_item_o2o prew_item_confirm_o2o" :style="{ backgroundImage: `url(${backgroundImage})` }">
    <div class="flex_row_start_start diy_style_zonghe" :style="{ color: currentColor.mainColor }">
      <span>综合</span>
      <div class="diy_style_san" :style="{ borderTopColor: currentColor.mainColor }"></div>
    </div>
    <div class="diy_style_branch" :style="{ color: currentColor.subColor }">
      <div class="branch_box branch_one"><span>4.9</span>分</div>
      <div class="branch_box branch_two"><span>5.0</span>分</div>
      <div class="branch_box branch_three"><span>5.0</span>分</div>
    </div>
    <div class="flex_row_start_start diy_style_price" :style="{ color: currentColor.priceColor }">
      <div class="style_price_box_one style_price_one_one">￥<span>99</span>.50</div>
      <div class="style_price_box_one style_price_one_two">￥<span>180</span>.50</div>
      <div class="style_price_box_one style_price_one_three">￥<span>9</span>.90</div>
    </div>
    <div class="flex_row_start_start diy_style_price" :style="{ color: currentColor.priceColor }">
      <div class="style_price_box_two style_price_two_one">￥<span>157</span>.50</div>
      <div class="style_price_box_two style_price_two_two">￥<span>3</span>.60</div>
      <div class="style_price_box_two style_price_two_three">￥<span>199</span>.90</div>
    </div>
    <div class="diy_style_label" :style="{ color: currentColor.subColor }">
      <div class="style_label_box style_label_one" :style="{ borderColor: currentColor.subColor }">同城配送</div>
      <div class="style_label_box style_label_two" :style="{ borderColor: currentColor.subColor }">上门自提</div>
    </div>
    <div class="flex_row_start_center diy_style_type_list">
      <div
        class="diy_style_type_item"
        :style="{
          color: currentColor.priceColor,
          
          borderColor: currentColor.priceColor,
        }"
        >
        <div class="diy_style_type_item_one">
          30减8
        </div>
        <div class="diy_style_type_item_two" :style="{background: currentColor.priceOpacityColor}">领券</div>
        </div
      >
      <div
        class="diy_style_type_item"
        :style="{
          width:'81px',
          color: currentColor.priceColor,
          borderColor: currentColor.priceColor,
          marginLeft:'5px'
        }"
        >
        <div class="diy_style_type_item_one">
          60减18
        </div>
        <div class="diy_style_type_item_two" :style="{background: currentColor.priceOpacityColor}">领券</div>
        </div
      >
    </div>
  </div>
</template>

<script>
  import { defineComponent } from 'vue';
  import { getImagePath } from '/@/utils';
  export default defineComponent({
    props: {
      currentColor: Object,
    },
    setup(props) {
      const backgroundImage = getImagePath('images/o2o_diy_style/diy_style_shop.png');
      return { backgroundImage };
    },
  });
</script>

<style lang="less" scoped>
  .prew_item_o2o {
    position: relative;
    width: 375px;
    height: 666px;
    margin-right: 50px;
    overflow: hidden;
    border-radius: 10px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    box-shadow: 0 0 12px 0 rgb(129 129 129 / 30%);
    cursor: default;

    &:last-of-type {
      margin-right: 0;
    }

    &.prew_item_confirm_o2o {
      .diy_style_zonghe{
        position: absolute;
        z-index: 2;
        top: 85px;
        left: 39px;
        font-weight: bold;
        font-size: 15px;
        line-height: 15px;
        .diy_style_san{
          width: 0;
          height: 0;
          border: 5px solid transparent;
          margin-top: 6px;
          margin-left: 6px;
        }
      }
      .diy_style_branch{
        font-weight: bold;
        font-size: 12px;
        span{
          font-size: 14px;
        }
        .branch_box{
          left: 87px;
          line-height: 14px;
          display: flex;
          align-items: center;
          position: absolute;

        }
        .branch_one{
          top: 149px;
        }
        .branch_two{
          top: 375px;
        }
        .branch_three{
          top: 623px;
        }
      }
      .diy_style_price {
        z-index: 2;
        font-size: 12px;
        font-weight: bold;
        .style_price_box_one{
          position: absolute;
          top: 297px;
        }
        .style_price_one_one{
            left: 99px;
        }
        .style_price_one_two{
            left: 186px;
        }
        .style_price_one_three{
            left: 282px;
        }
        .style_price_box_two{
          position: absolute;
          top: 544px;
        }
        .style_price_two_one{
            left: 96px;
        }
        .style_price_two_two{
            left: 193px;
        }
        .style_price_two_three{
            left: 274px;
        }

        span {
          font-size: 15px;
        }
      }
      .diy_style_label{
        font-size: 11px;
        .style_label_box{
          width: 56px;
          height: 18px;
          background: #FFFFFF;
          border-radius: 4px;
          border: 1px solid;
          display: flex;
          align-items: center;
          justify-content: center;
          position: absolute;
          right: 23px;
        }
        .style_label_one{
          top: 351px;
        }
        .style_label_two{
          bottom: 49px;
        }
      }

      .diy_style_type_list {
        position: absolute;
        z-index: 2;
        top: 416px;
        left: 87px;

        .diy_style_type_item {
          width: 73px;
          height: 15px;
          border: 1px solid;
          border-radius: 4px;
          font-size: 14px;
          font-size: 11px;
          display: flex;
          align-items: center;
          .diy_style_type_item_one{
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .diy_style_type_item_two{
            width: 32px;
            height: 15px;
            line-height: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }

    }
  }
</style>
