<template>
  <div class="prew_item_o2o prew_item_o2o_order" :style="{ backgroundImage: `url(${backgroundImage})` }">
    <div class="flex_row_start_center diy_style_type_service" :style="{
        color: currentColor.mainColor,
        borderColor: currentColor.mainColor, 
        }">
      <div class="diy_style_service_one" :style="{ background: currentColor.mainColor }">预计送达</div>
      <div class="diy_style_service_two" :style="{color: currentColor.mainColor,}">今天08:45后配送</div>
    </div>
    <div class="diy_style_branch" :style="{ color: currentColor.subColor }">
      <div class="branch_box branch_one"><span>4.9</span>分</div>
    </div>
    <div class="flex_row_start_center diy_style_label" :style="{ color: currentColor.subColor }">
      <div class="style_label_box style_label_one" :style="{ borderColor: currentColor.subColor }">同城配送</div>
      <div class="style_label_box style_label_two" :style="{ marginLeft:'4px',borderColor: currentColor.subColor }">上门自提</div>
    </div>
    <div class="flex_row_center_center diy_style_all" :style="{ color: currentColor.mainColor,background:currentColor.mainOpacityColor }">
      全部
    </div>
    <div class="flex_row_start_center diy_style_type_list">
      <div class="diy_style_type_item" :style="{
        color: currentColor.priceColor,

        borderColor: currentColor.priceColor,
      }">
        <div class="diy_style_type_item_one">
          30减8
        </div>
        <div class="diy_style_type_item_two" :style="{ background: currentColor.priceOpacityColor }">领券</div>
      </div>
      <div class="diy_style_type_item" :style="{
        width: '81px',
        color: currentColor.priceColor,
        borderColor: currentColor.priceColor,
        marginLeft: '10px'
        }">
        <div class="diy_style_type_item_one">
          60减18
        </div>
        <div class="diy_style_type_item_two" :style="{ background: currentColor.priceOpacityColor }">领券</div>
      </div>
      <div class="diy_style_type_item" :style="{
        width: '81px',
        color: currentColor.priceColor,
        borderColor: currentColor.priceColor,
        marginLeft: '10px'
        }">
        <div class="diy_style_type_item_one">
          99减29
        </div>
        <div class="diy_style_type_item_two" :style="{ background: currentColor.priceOpacityColor }">领券</div>
      </div>
    </div>
    <div class="flex_row_start_start diy_style_price" :style="{ color: currentColor.priceColor }">
      <div class="style_price_box_one style_price_one_one"><p>￥</p><span>19</span>.90</div>
      <div class="style_price_box_one style_price_one_two"><p>￥</p><span>18</span>.90</div>
    </div>
    <div class="diy_style_shu" :style="{ background: currentColor.mainColor }"></div>
    <div class="diy_style_milk" :style="{ color: currentColor.mainColor }">牛奶酸奶</div>
    <div class="flex_row_start_center diy_style_type_sale" :style="{
        color: currentColor.priceColor,
        }">
      <div class="diy_style_service_one" :style="{ background: currentColor.priceColor }">秒杀中</div>
      <div class="diy_style_service_two" :style="{color: currentColor.priceColor,background: currentColor.priceOpacityColor}">10:20:38</div>
    </div>
    <div class="diy_style_type_jian">
      <AliSvgIcon
        :fillColor="currentColor.mainColor"
        width="22px"
        height="22px"
        iconName="iconshanchu7"
      />
    </div>
    <div class="diy_style_type_jia diy_style_type_jia_one">
      <AliSvgIcon
        :fillColor="currentColor.mainColor"
        width="22px"
        height="22px"
        iconName="iconziyuan110"
      />
    </div>
    <div class="diy_style_type_jia diy_style_type_jia_two">
      <AliSvgIcon
        :fillColor="currentColor.mainColor"
        width="22px"
        height="22px"
        iconName="iconziyuan110"
      />
    </div>
    <div class="diy_style_type_cart">
      <AliSvgIcon
        :fillColor="currentColor.mainColor"
        width="36px"
        height="36px"
        iconName="icongouwujiesuan"
      />
    </div>
    <div class="diy_style_type_cart_num" :style="{ background: currentColor.priceColor }">
      6
    </div>
    <div class="flex_row_end_center diy_style_order_bottom">
      <div class="flex_row_start_start diy_style_order_bottom_price" :style="{ color: currentColor.priceColor }">
        ￥<span>19</span>.90
      </div>
    </div>
    <div class="diy_style_order_bottom_btn" :style="{ background: currentColor.mainColor }">去结算</div>
  </div>
</template>

<script>
import { defineComponent } from 'vue';
import { getImagePath } from '/@/utils';
export default defineComponent({
  props: {
    currentColor: Object,
  },
  setup(props) {
    const backgroundImage = getImagePath('images/o2o_diy_style/diy_style_index.png');
    return { backgroundImage };
  },
});
</script>

<style lang="less" scoped>
.prew_item_o2o {
  position: relative;
  width: 375px;
  height: 666px;
  margin-right: 50px;
  overflow: hidden;
  border-radius: 10px;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  box-shadow: 0 0 12px 0 rgb(129 129 129 / 30%);
  cursor: default;

  &:last-of-type {
    margin-right: 0;
  }

  &.prew_item_o2o_order {
    .diy_style_all{
      width: 51px;
      height: 31px;
      border-radius: 16px;
      position: absolute;
      top: 256px;
      left: 95px;
    }
    .diy_style_label{
      font-size: 11px;
      position: absolute;
      top: 140px;
      left: 186px;
        .style_label_box{
          width: 56px;
          height: 18px;
          background: #FFFFFF;
          border-radius: 4px;
          border: 1px solid;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    .diy_style_branch{
        font-weight: bold;
        font-size: 12px;
        span{
          font-size: 14px;
        }
        .branch_box{
          left: 89px;
          line-height: 14px;
          display: flex;
          align-items: center;
          position: absolute;

        }
        .branch_one{
          top: 142px;
        }
      }
    .diy_style_type_service{
      position: absolute;
      z-index: 2;
      font-size: 11px;
      top: 119px;
      left: 87px;
      width: 156px;
      height: 17px;
      border-radius: 4px;
      border: 1px solid;
      .diy_style_service_one{
        width: 52px;
        height: 15px;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .diy_style_service_two{
        flex: 1;
        height: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    .diy_style_type_jian{
      position: absolute;
      z-index: 2;
      top: 447px;
      left: 289px;
    }
    .diy_style_type_jia{
      position: absolute;
      z-index: 2;
      left: 343px;
    }
    .diy_style_type_jia_one{
      top: 447px;
    }
    .diy_style_type_jia_two{
      bottom: 50px;
    }
    .diy_style_type_sale{
      position: absolute;
      z-index: 2;
      font-size: 11px;
      top: 420px;
      left: 197px;
      width: 98px;
      height: 17px;
      border-radius: 4px;
      overflow: hidden;
      .diy_style_service_one{
        width: 41px;
        height: 15px;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .diy_style_service_two{
        flex: 1;
        height: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    .diy_style_type_list {
      position: absolute;
      z-index: 2;
      top: 222px;
      left: 10px;

      .diy_style_type_item {
        width: 73px;
        height: 15px;
        border: 1px solid;
        border-radius: 4px;
        font-size: 14px;
        font-size: 11px;
        display: flex;
        align-items: center;

        .diy_style_type_item_one {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .diy_style_type_item_two {
          width: 32px;
          height: 15px;
          line-height: 15px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
    .diy_style_shu{
      width: 3px;
      height: 50px;
      position: absolute;
      left: 0;
      top: 306px;
    }
    .diy_style_milk{
      font-weight: bold;
      font-size: 15px;
      position: absolute;
      left: 13px;
      top: 320px;
    }
    .diy_style_price {
        z-index: 2;
        font-size: 15px;
        font-weight: bold;
        line-height: 15px;
        .style_price_box_one{
          position: absolute;
          display: flex;
          align-items: flex-end;
          left: 199px;
          p{
            font-size: 12px;
            line-height: 14px;
          }
        }
        .style_price_one_one{
            top: 448px;
        }
        .style_price_one_two{
            bottom: 59px;
        }

        span {
          font-size: 17px;
          line-height: 17px;
        }
      }

    .diy_style_type_cart{
      position: absolute;
      z-index: 2;
      left: 13px;
      bottom: 0px;
    }
    .diy_style_type_cart_num{
      position: absolute;
      z-index: 2;
      left: 37px;
      bottom: 30px;
      width: 15px;
      height: 15px;
      font-size: 13px;
      color: #fff;
      display: flex;
      align-items: center;
      border-radius: 50%;
      justify-content: center;
      padding-bottom: 2px;
    }
    .diy_style_order_bottom {
      position: absolute;
      z-index: 2;
      left: 62px;
      bottom: 24px;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-weight: 500;

      .diy_style_order_bottom_price {
        align-items: baseline;
        font-size: 14px;
        font-weight: 700;

        span {
          font-size: 19px;
        }
      }

    }
    .diy_style_order_bottom_btn {
      width: 99px;
      height: 35px;
      position: absolute;
      z-index: 2;
      right: 9px;
      bottom: 9px;
      margin-left: 6px;
      border-radius: 17px;
      color: #fff;
      display: flex;
      font-size: 15px;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
