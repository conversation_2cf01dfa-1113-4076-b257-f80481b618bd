import { Upload, Modal } from 'ant-design-vue';
import { useGlobSetting } from '/@/hooks/setting';
import { getToken } from '@/utils/auth';
import { sldBeforeUpload } from '@/utils/utils';
import { useUserStore } from '@/store/modules/user';
import { PlusOutlined } from '@ant-design/icons-vue';
const { apiUrl } = useGlobSetting();


const userStore = useUserStore();



const uploadButton = (clickEvent) => (
  <div className="w-full h-full flex_column_center_center" onClick={clickEvent}>
    <PlusOutlined />
    <div className="ant-upload-text mt-2">上传图片</div>
  </div>
);

export function CategoryImageRender(record, targetGrage, { delImage, uploadImgPre, openMaterial }) {
  let tmp_file_list = [];
  if (record.categoryImageUrl) {
    let tmp_data = {};
    tmp_data.uid = record.categoryId;
    tmp_data.name = record.categoryImageUrl;
    tmp_data.status = 'done';
    tmp_data.url = record.categoryImageUrl;
    tmp_file_list.push(tmp_data);
  }

  let imgToken = 'Bearer ' + userStore.getToken || ''; //上传文件token参数

  const handClick = () => {
    openMaterial(record, targetGrage);
  };

  const beforeUploadClick = async()=> {
    if(userStore.getToken){
      let res = await userStore.getSldCommonService()
      if(res.state == 200){
        imgToken = 'Bearer ' + res.token
      }
    }
  }

  const uploadRender = (
    <div
      // @ts-ignore
      className="flex_column_center_center mcat_upload_wrap"
    >
      <Upload
        withCredentials={true}
        beforeUpload={(file, fileList) =>
          sldBeforeUpload(file, fileList, undefined, '.gif, .jpeg, .png, .jpg,')
        }
        accept={'.gif, .jpeg, .png, .jpg,'}
        name={'file'}
        action={`${apiUrl}/v3/oss/admin/upload?source=adminDeco`}
        listType="picture-card"
        fileList={tmp_file_list}
        onPreview={(info) => uploadImgPre(info)}
        onChange={(info) => delImage(info, record)}
        onClick={()=>beforeUploadClick()}
        openFileDialogOnClick={false}
        headers={{
          Authorization: imgToken,
        }}
      >
        {tmp_file_list.length >= 1 ? null : uploadButton(handClick)}
      </Upload>
      <span>建议上传160*160的图片</span>
    </div>
  );

  return record.grade == targetGrage ? uploadRender : '--';
}
