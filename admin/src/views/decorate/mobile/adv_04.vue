<!-- 辅助线组件 -->
<template>
  <div
    class="fzx"
    :style="{
      background: data.bg_color ? data.bg_color : '#fff',
      paddingTop: data.top_margin + 'px',
      paddingBottom: data.bottom_margin + 'px',
    }"
  >
    <div
      :style="{
        color: data.color,
        borderTopColor: data.color,
        borderTopStyle: data.val,
        marginLeft: data.lrmargin + 'px',
        marginRight: data.lrmargin + 'px',
        marginTop: data.tbmargin + 'px',
        marginBottom: data.tbmargin + 'px',
      }"
    ></div>
  </div>
</template>
<script setup>
  const props = defineProps({
    data: {
      type: Object,
      default: {
        top_margin: 10,
        bottom_margin: 10,
        bg_color: '#fff',
        val: 'solid', //辅助线类型
        color: '#e3e5e9', //颜色
        lrmargin: 10,  //左右边距
        tbmargin: 10,  //上下边距
      }
    },
  });
</script>
<style lang="less" scoped>
.fzx {
  padding: 20px 0;

  div {
    border: none;
    border-top: 1px solid #bbb;
    color: #fff;
  }
}
</style>