<!-- 图片组合-8张图片组合- 组件 -->
<template>
  <div class="flex_com_row_start_center sld_com_img">
    <div class="common_img_part flex_com_column_center_center">
      <div class="upload_img flex_column_center_center">
        <Upload
          name="file"
          :withCredentials="true"
          accept=".gif, .jpeg, .png, .jpg"
          :showUploadList="false"
          :openFileDialogOnClick="false"
        >
          <div
            class="flex_column_center_center"
            @click="openMaterial(data, index, parent_index)"
          >
            <img v-if="data.img" :src="data.img" />
            <AliSvgIcon
              v-else
              width="40px"
              height="40px"
              fillColor="#FC701E"
              iconName="iconziyuan110"
            />
            <span class="upload_btn">选择图片</span>
          </div>
        </Upload>
      </div>
      <span class="upload_img_tip">{{ upload_img_tip }}</span>
    </div>
    <div class="img_con flex_com_column_center_flex_start">
      <Input
        class="title"
        :maxLength="limintLength"
        :placeholder="title_placeholder"
        :value="data[title_key]"
        @change="(e) => onChange(e.target.value, title_key, index,parent_index)"
      />
      <Select
        :value="data.url_type"
        style="width: 232px"
        placeholder="请选择链接类型"
        @select="(e) => sldHandSeleLink(e, index, parent_index)"
      >
        <template v-if="props.diy_type == 'o2o'">
          <Select.Option
            v-for="(items, index) in m_diy_o2o_link_type()"
            :key="index"
            :value="items.key"
          >{{ items.name }}</Select.Option>
        </template>
        <template v-else-if="props.diy_type == 'integral'">
          <Select.Option
            v-for="(items, index) in m_diy_point_link_type()"
            :key="index"
            :value="items.key"
          >{{ items.name }}</Select.Option>
        </template>
        <template v-else-if="props.diy_type == 'spreader'">
          <Select.Option
            v-for="(items, index) in m_diy_spreader_link_type()"
            :key="index"
            :value="items.key"
          >{{ items.name }}</Select.Option>
        </template>
        <template v-else>
          <Select.Option
            v-for="(items, index) in m_diy_link_type()"
            :key="index"
            :value="items.key"
          >{{ items.name }}</Select.Option>
        </template>
      </Select>
      <SldDiyItemSelectLinkHideLabel
        :data="data"
        :index="index"
        :parent_index="parent_index"
        @handleChange="onChange"
        :label_width="label_width"
        :label_top="label_top"
        :label_bottom="label_bottom"
      />
    </div>
  </div>

  <!-- 图片素材选择 start -->
  <SldMaterialImgs
    ref="sldMaterialImgsRef"
    :visibleModal="chooseFile == 1 ? true : false"
    :maxUploadNum="1"
    :allowRepeat="false"
    :selectedData="selectImageData"
    @closeMaterial="() => closeMaterial()"
    @confirmMaterial="(val) => confirmMaterial(val)"
  ></SldMaterialImgs>
  <!-- 图片素材选择 end -->
</template>
<script setup>
  import { reactive, ref } from 'vue';
  import { Upload, Input, Select } from 'ant-design-vue';
  import { m_diy_link_type, m_diy_o2o_link_type,m_diy_point_link_type,m_diy_spreader_link_type, } from '@/utils/utils';
  import SldDiyItemSelectLinkHideLabel from '@/components/SldDiyItemEdit/indexHideLabel.vue';
  import SldMaterialImgs from '@/components/SldMaterialFiles/sldMaterialImgs.vue';

  const props = defineProps({
    data: {
      type: Object,
      default: {}
    },
    index: {
      type: Number,
      default: 0
    },
    parent_index: {
      type: Number,
      default: 0
    },
    upload_img_tip: {
      type: String,
      default: ''
    },
    list: {
      type: Array,
      default: []
    },
    label_width: {
      type: Number,
      default: 300
    },
    label_top: {
      type: Number,
      default: 0
    },
    label_bottom: {
      type: Number,
      default: 16
    },
    diy_type: { //装修来源类型: home-首页手机装修 topic-专题装修 integral-积分商城装修 spreader-推手装修
      type: String,
      default: '',
    },
  });

  const limintLength = 15; //限制内容长度
  const title_key = 'title';
  const title_placeholder = '请输入图片标题';

  const emit = defineEmits(['handSeleLink', 'handleChange', 'handleCurSelData']);

  function onChange(val, type, tar_index = 0, parent_index = -1) {
    emit('handleChange', val, type, tar_index, parent_index);
  };

  const chooseFile = ref(0);
  const operate_files_index = ref('');
  const operate_files_index_parent = ref('');
  let selectImageData = reactive({ ids: [], data: [] });

  function sldHandSeleLink(e, index, parent_index) {
    emit('handSeleLink', e, index, parent_index);
  };

  //打开上传素材文件
  function openMaterial(item, index, indexs = -1) {
    if (indexs !== -1) {
      operate_files_index_parent.value = indexs;
    } else {
      operate_files_index_parent.value = '';
    }
    operate_files_index.value = index;
    let selectImageDataVal = { ids: [], data: [] };
    if (item.img_path) {
      selectImageDataVal = {
        ids:[Number(item.img_path.split('?bindId=')[1])],
        data:[{
          bindId: item.img_path.split('?bindId=')[1],
          checked: true,
          filePath: item.img_path,
          fileUrl: item.img,
          fileType: 1,
        }]
      };
    }
    selectImageData = selectImageDataVal;
    chooseFile.value = 1;
  };
    
  //关闭上传素材文件
  function closeMaterial() {
    operate_files_index.value = '';
    chooseFile.value = 0;
  };
  
  //确认上传素材文件
  function confirmMaterial(val) {
    let data = { ...props.list };
    if (operate_files_index_parent.value !== '') {
      data.data[operate_files_index_parent.value].img[operate_files_index.value].img = val.ids.length ? val.data[0].fileUrl : '';
      data.data[operate_files_index_parent.value].img[operate_files_index.value].img_path = val.ids.length ? val.data[0].filePath : '';
      data.data[operate_files_index_parent.value].img[operate_files_index.value].imgPath = val.ids.length ? val.data[0].filePath : '';
      data.data[operate_files_index_parent.value].img[operate_files_index.value].width = val.ids.length ? val.data[0].width : '';
      data.data[operate_files_index_parent.value].img[operate_files_index.value].height = val.ids.length ? val.data[0].height : '';
      data.width = val.ids.length ? val.data[0].width : '';
      data.height = val.ids.length ? val.data[0].height : '';
    } else {
      data.data[operate_files_index.value].img = val.ids.length ? val.data[0].fileUrl : '';
      data.data[operate_files_index.value].img_path = val.ids.length ? val.data[0].filePath : '';
      data.data[operate_files_index.value].imgPath = val.ids.length ? val.data[0].filePath : '';
      data.data[operate_files_index.value].width = val.ids.length ? val.data[0].width : '';
      data.data[operate_files_index.value].height = val.ids.length ? val.data[0].height : '';
      data.width = val.ids.length ? val.data[0].width : '';
      data.height = val.ids.length ? val.data[0].height : '';
    }
    chooseFile.value = 0;
    selectImageData = val;
    emit('handleCurSelData', data);
    operate_files_index_parent.value = '';
    operate_files_index.value = '';
  };
</script>
<style lang="less" scoped>
.sld_com_img {
  position: relative;
  margin-top: 10px;
  padding: 10px 0;
  border: 1px solid #eee;
  background: #f8f8f8;

  .common_img_part {
    .upload_img_tip {
      margin-top: -20px;
      margin-bottom: 21px;
      transform: scale(0.9);
      color: #bbb;
      font-size: 12px;
      line-height: 12px;
    }

    .upload_img {
      position: relative;
      margin: 22px 0 22px 22px;
      padding-right: 22px;
      border-right: 1px solid #eeee;

      img {
        max-width: 48px;
        max-height: 48px;
        cursor: pointer;
      }

      .upload_btn {
        margin-top: 5px;
        color: #666;
        font-size: 12px;
        line-height: 12px;
        text-align: center;
        white-space: nowrap;
        cursor: pointer;
      }
    }

    .upload_img:hover {
      .img_mask {
        display: inline-block !important;
      }
    }

    &.nav_bottom_img_part {
      &:nth-child(2) {
        .upload_img {
          border-right: none;
        }
      }

      .upload_img  {
        margin: 10px 0 10px 17px;
        padding-right: 17px;

        img {
          max-width: 38px;
          max-height: 38px;
        }

        .upload_btn {
          margin-top: 10px;
        }

        .upload_btn_select {
          margin-top: 2px;
          color: #ABAAAA;
          font-family: "Microsoft YaHei";
          font-size: 12px;
          font-weight: 400;
        }
      }
    }
  }

  .img_con {
    margin-right: 15px;
    margin-left: 15px;

    .title {
      width: 232px;
      margin-bottom: 8px;
    }

    .bg_color_current {
      border-color: #429DFD !important;
      cursor: pointer;
    }

    &.nav_bottom_img_con {
      height: 74px;
      margin-right: 16px;
      margin-left: 0;

      .title {
        width: 180px;
      }
    }
  }

  .del_sld_com_img {
    position: absolute;
    top: -8px;
    right: -8px;
    margin-left: 20px;
    cursor: pointer;
  }
}
</style>