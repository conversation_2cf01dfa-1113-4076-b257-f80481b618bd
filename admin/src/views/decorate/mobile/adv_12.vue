<!-- 导航组件 -->
<template>
  <div
    class="adv_12"
    :style="{
      background: data.color ? data.color : '#fff',
      paddingTop: data.top_margin + 'px',
      paddingBottom: data.bottom_margin + 'px',
    }"
  >
    <template v-if="data.page_set == 'scroll'">
      <Carousel dotPosition="bottom">
        <template v-for="(item, index) in splitArray(data.data, data.page_num)" :key="index">
          <div>
            <template v-if="data.style_set == 'nav'">
              <div class="nav flex_row_start_start" style="flex-wrap: wrap; padding-bottom: 20px;">
                <div
                  v-for="(items, indexs) in item"
                  :key={indexs}
                  class="item flex_com_row_center"
                  :style="{
                    flexDirection: data.icon_set == 'up' ? 'column' : 'row',
                    width: data.data.length > 4 ? '20%' : 'unset',
                    flex: data.data.length > 4 ? 'unset' : 1,
                    marginBottom: 8 + 'px',
                  }"
                >
                  <img
                    v-if="data.icon_set != 'no-icon'"
                    :style="{
                      width: data.slide + 'px',
                      height: data.slide + 'px',
                      marginBottom: data.icon_set == 'up' ? 5 : data.icon_set == 'left' ? 0 : 'unset',
                      marginRight: data.icon_set == 'up' ? 0 : data.icon_set == 'left' ? 5 : 'unset'
                    }"
                    :src="items.img ? items.img : nav_default_img"
                  />
                  <span class="nav_text">{{ items.name ? items.name : '导航' }}</span>
                </div>
              </div>
            </template>
            <template v-else-if="data.style_set == 'tag-nav'">
              <div class="nav flex_com_row tag_nav" style="padding-bottom: 20px;">
                <div
                  v-for="(items, index) in item"
                  :key="index"
                  class="item flex_com_row_center"
                >
                  <img :style="{
                        width: data.slide + 'px',
                        height: data.slide + 'px'
                      }"
                      :src="items.img ? items.img : nav_default_img"/>
                  <span class="nav_text">{{ items.name ? items.name : '导航' }}</span>
                </div>
              </div>
            </template>
          </div>
        </template>
      </Carousel>
    </template>
    <template v-else>
      <!-- 导航样式 -->
      <div v-if="data.style_set == 'nav'"
          class="nav flex_com_row_space_around_center">
        <template v-for="(item, index) in data.data" :key={index}>
          <div
            v-if="index < 5"
            class="item flex_com_row_center"
            :style="{ flexDirection: data.icon_set == 'up' ? 'column' : 'row' }"
          >
              <img
                v-if="data.icon_set != 'no-icon'"
                :style="{
                  width: data.slide + 'px',
                  height: data.slide + 'px',
                  marginBottom: data.icon_set == 'up' ? 5 : data.icon_set == 'left' ? 0 : 'unset',
                  marginRight: data.icon_set == 'up' ? 0 : data.icon_set == 'left' ? 5 : 'unset'
                }"
                :src="item.img ? item.img : nav_default_img"
              />
              <span class="nav_text">{{ item.name ? item.name : '导航' }}</span>
          </div>
        </template>
      </div>
      <!-- 分组样式 -->
      <div v-else-if="data.style_set == 'tag-nav'"
          class="nav flex_com_row tag_nav">
        <div
          v-for="(item, index) in data.data"
          :key="index"
          class="item flex_com_row_center"
        >
          <img :style="{
                width: data.slide + 'px',
                height: data.slide + 'px'
              }"
              :src="item.img ? item.img : nav_default_img"/>
          <span class="nav_text">{{ item.name ? item.name : '导航' }}</span>
        </div>
      </div>
    </template>
  </div>
</template>
<script setup>
  import { Carousel } from 'ant-design-vue';
  import { nav_default_img } from '/@/utils/utils';

  const props = defineProps({
    data: {
      type: Object,
      default: {
        top_margin: 10,
        bottom_margin: 0,
        color: '#fff',
        style_set: 'nav', //样式设置 nav 导航  tag-nav分组
        icon_set: 'up', //图标方向 up 图标向上 left 图标向左  no-icon 不显示图标
        slide: 35, //图标的大小
        data: [], //导航数据
      }
    },
  });


  function splitArray(arr, groupSize) {
    var result = [];
    for (var i = 0; i < arr.length; i += groupSize) {
        result.push(arr.slice(i, i + groupSize));
    }
    return result;
  }
</script>
<style lang="less">
.adv_12 {
  .nav {
    padding: 5px;

    .nav_text {
      color: #555;
      font-size: 12px;
    }
  }

  .tag_nav {
    flex-wrap: wrap;

    .item {
      width: 50%;
      padding: 8px;

      img {
        margin-right: 10px;
      }
    }
  }

  .ant-carousel .slick-dots-bottom {
    bottom: 0;
    margin-bottom: 10px;
  }

  .ant-carousel .slick-dots li {
    margin: 0;
  }

  .ant-carousel .slick-dots li button {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: rgb(255 0 0 / 50%);
  }

  .ant-carousel .slick-dots li.slick-active button {
    width: 20px;
    height: 6px;
    border-radius: 4px;
    background: #F00;
  }
}
</style>