<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <SldComHeader :type="1" title="首页装修" />
      <BasicTable @register="standardTable">
        <template #tableTitle>
          <div class="toolbar flex flex-row">
            <div class="toolbar_btn" @click="handleClick(null, 'add')">
              <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#fff" />
              <span>新增页面</span>
            </div>

            <div class="ml-2">
              <InputSearch
                v-model:value="searchName"
                placeholder="请输入页面名称"
                enter-button="搜索"
                class="text-xs"
                @search="onSearch"
                :allow-clear="true"
              ></InputSearch>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.key == 'action'">
            <div class="flex flex-row items-center justify-center">
              <span
                class="common_page_edit cursor-pointer hover:text-[#FF6A12]"
                @click="handleClick(record, 'diy')"
                >装修</span
              >
              <span
                class="common_page_edit cursor-pointer hover:text-[#FF6A12]"
                @click="handleClick(record, 'edit')"
                >编辑</span
              >

              <span
                class="common_page_edit cursor-pointer hover:text-[#FF6A12]"
                @click="handleClick(record, 'copy')"
                >复制</span
              >

              <span
                class="common_page_edit cursor-pointer hover:text-[#FF6A12]"
                @click="handleClick(record, 'open_screen')"
                >开屏图</span
              >

              <Popconfirm
                title="删除后不可恢复，是否确定删除？"
                @confirm="handleClick(record, 'del')"
              >
                <span class="common_page_edit cursor-pointer hover:text-[#FF6A12]">删除</span>
              </Popconfirm>
            </div>
          </template>
          <template v-if="['h5', 'ios', 'weixinXcx', 'android'].includes(column.key)">
            <Switch
              :checked="record[column.key] == 1"
              @change="switchChange(record, column.key)"
            ></Switch>
          </template>
        </template>
      </BasicTable>
    </div>

    <SldModal
      v-bind="modalState"
      @confirm-event="(e, d) => handleModalEvent(e, 'confirm', d)"
      @cancle-event="handleModalEvent({}, 'cancel')"
    />

    <SldMoreImgModal
      :width="1000"
      :title="modalTitle"
      :modalTip="modal_tip"
      :content="modalContent"
      ref="sldMoreImgModalRef"
      @confirm="moreImgConfirm"
      client="mobile"
      :totalNum="1"
    ></SldMoreImgModal>
  </div>
</template>
<script>
  export default {
    name: 'mobile_diy_page_lists',
  };
</script>
<script setup>
  import { BasicTable, useTable } from '/@/components/Table';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import {
    getMobleDecoListApi,
    updateIsUse,
    decoMobileAddApi,
    decoMobileDelete,
    decoMobileCopy,
    updateMobileDeco,
  } from '@/api/decorate/deco_m';
  import SldModal from '/@/components/SldModal/index.vue';
  import SldMoreImgModal from '@/components/SldMoreImgModal/index.vue';
  import { Popconfirm, Switch, InputSearch } from 'ant-design-vue';
  import { reactive, ref } from 'vue';
  import { sucTip, failTip } from '@/utils/utils';
  import { useRouter } from 'vue-router';

  const router = useRouter();
  const sldMoreImgModalRef = ref();
  const searchName = ref('');
  const modalTitle = ref('设置开屏图');
  const modal_tip = [
    '请严格根据提示要求上传规定尺寸的图片,图片不可以超过1M,否则影响页面加载效果',
    '编辑项中的“操作”指点击该内容所产生的链接地址，可通过下拉选项选择不同的方式',
  ];
  const modalContent = reactive({
    width: 580,
    height: 776,
    admin_show_width: 290,
    admin_show_height: 388,
    data: [],
  });

  const deco_columns = [
    { title: '页面名称', dataIndex: 'name', width: 150 },
    { title: '创建时间', dataIndex: 'createTime', width: 150 },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      width: 150,
      customRender: ({ record }) => record.updateTime || '--',
    },
    { title: 'Android', dataIndex: 'android', width: 100 },
    { title: 'IOS', dataIndex: 'ios', width: 100 },
    { title: '微商城', dataIndex: 'h5', width: 100 },
    { title: '微信小程序', dataIndex: 'weixinXcx', width: 100 },
  ];

  const content = ref([
    {
      type: 'input',
      label: '页面名称',
      name: 'name',
      placeholder: '请输入装修页面名称',
      extra: '最多输入8个字',
      maxLength: 8,
      showCount: true,
      rules: [
        {
          required: true,
          whitespace: true,
          message: '请输入页面名称',
        },
      ],
      initialValue: '',
    },
  ]);

  const modalState = reactive({
    width: 600,
    title: '新增装修页面',
    visible: false,
    content: content.value,
    confirmBtnLoading: false,
    showFoot: true,
    decoId: 0,
    type: 'add',
  });

  const handleModalEvent = async (params, type, resetFields) => {
    if (type == 'confirm') {
      modalState.confirmBtnLoading = true;
      const res = await (modalState.type == 'add'
        ? decoMobileAddApi({ ...params, type: 'home' })
        : updateMobileDeco({ ...params, type: 'home', decoId: modalState.decoId }));
      modalState.confirmBtnLoading = false;
      if (res?.state == 200) {
        resetFields();
        modalState.visible = false;
        reload();
      } else {
        failTip(res.msg);
      }
    } else {
      modalState.visible = false;
    }
  };

  const [standardTable, { reload, setLoading }] = useTable({
    api: getMobleDecoListApi,
    columns: deco_columns,
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    actionColumn: {
      width: 250,
      title: '操作',
      dataIndex: 'action',
      align: 'center',
    },
    pagination: {
      pageSize: 10,
    },
    searchInfo: {
      type: 'home',
    },
    bordered: true,
    striped: false,
    beforeFetch({ ...params }) {
      let targetParams = params;
      if (searchName.value) {
        targetParams.name = searchName.value;
      }
      return targetParams;
    },
  });

  const handleClick = (record, type) => {
    if (type == 'diy') {
      router.push(`/decorate_m/lists_to_diy?id=${record.decoId}&source=decorate_m/lists&type=home`);
    } else if (type == 'del') {
      setLoading(true);
      decoMobileDelete({ decoId: record.decoId }).then((res) => {
        setLoading(false);
        if (res.state == 200) {
          reload();
          sucTip(res.msg);
        } else {
          failTip(res.msg);
        }
      });
    } else if (type == 'copy') {
      setLoading(true);
      decoMobileCopy({ decoId: record.decoId }).then((res) => {
        setLoading(false);
        if (res.state == 200) {
          reload();
          sucTip(res.msg);
        } else {
          failTip(res.msg);
        }
      });
    } else if (type == 'preview') {
      window.open(record.pcUrl);
    } else if (type == 'add') {
      modalState.type = type;
      modalState.visible = true;
    } else if (type == 'edit') {
      modalState.type = type;
      modalState.decoId = record.decoId;
      content.value[0].initialValue = record.name;
      modalState.visible = true;
    } else if (type == 'open_screen') {
      modalState.decoId = record.decoId;
      if (record.showTip) {
        modalTitle.value = '编辑开屏图';
        let adv_data = JSON.parse(record.showTip.replace(/&quot;/g, '"'));
        modalContent.data = adv_data;
      } else {
        modalContent.data[0] = {
          imgPath: '',
          imgUrl: '',
          info: {},
          link_type: '',
          link_value: 'none',
          title: '',
          width: 0,
          height: 0,
        };
        modalTitle.value = '设置开屏图';
      }
      sldMoreImgModalRef.value.setModal(true);
    }
  };

  const switchChange = async (record, key) => {
    const res = await updateIsUse({
      decoId: record.decoId,
      isUse: record[key] == 1 ? 0 : 1,
      os: key,
    });
    if (res?.state == 200) {
      reload();
      sucTip(res.msg);
    } else {
      failTip(res.msg);
    }
  };

  const moreImgConfirm = async (dataSource) => {
    let {
      parent_data: [targetData],
    } = dataSource;
    const res = await updateMobileDeco({
      decoId: modalState.decoId,
      type: 'home',
      showTip: JSON.stringify([targetData]),
    });

    if (res?.state == 200) {
      sucTip(res.msg);
      modalState.decoId = 0;
      sldMoreImgModalRef.value.setModal(false);
      reload();
    } else {
      failTip(res.msg);
    }
  };

  const onSearch = () => {
    reload();
  };
</script>

<style scoped>
  .diy_input {
    font-size: 13px;
  }
</style>
