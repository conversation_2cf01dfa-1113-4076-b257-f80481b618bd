<!-- 图片组合组件 -->
<template>
  <div
    class="tupianzuhe"
    :style="{
      ...wrap_style,
      background: data.color ? data.color : '#fff',
      paddingTop: data.top_margin + 'px',
      paddingBottom: data.bottom_margin + 'px',
    }"
  >
    <template v-if="curFormData.sele_style == 0 || curFormData.sele_style == 1 || curFormData.sele_style == 2 || curFormData.sele_style == 3">
      <template v-for="(item, index) in curFormData.data" :key="index">
        <div
          class="item item_0"
          :style="{
            height: item.img ? 'auto' : item.height + 'px',
            backgroundColor: item.img ? '#fff' : empty_bg_color,
            ...style,
          }"
        >
          <img v-if="item.img" :src="item.img" />
          <div v-else class="flex_column_center_center">
            <AliSvgIcon
              width="40.8px"
              height="33.6px"
              fillColor="#fff"
              iconName="icontupian1"
            />
            <span class="center_tip flex_row_common">宽{{ tip_width }}*高{{ tip_height }}</span>
          </div>
        </div>
      </template>
    </template>
    <template v-else-if="curFormData.sele_style == 4">
      <div
        class="flex_com_row_center"
        :style="{
          width: tmp_data.one_width + 'px',
          height: tmp_data.one_height + 'px',
          backgroundColor: curFormData.data[0].img ? '#fff' : empty_bg_color,
        }"
      >
        <img v-if="curFormData.data[0].img" :src="curFormData.data[0].img" />
        <div v-else class="flex_column_center_center">
          <AliSvgIcon
            width="40.8px"
            height="33.6px"
            fillColor="#fff"
            iconName="icontupian1"
          />
          <span class="center_tip flex_row_common">宽300*高320</span>
        </div>
      </div>
      <div class="flex_com_column_space_betweent_center" :style="{ height: tmp_data.one_height + 'px' }">
        <div
          class="flex_com_row_center"
          :style="{
            width: tmp_data.one_width + 'px',
            height: tmp_data.second_height + 'px',
            backgroundColor: curFormData.data[1].img ? '#fff' : empty_bg_color,
          }"
        >
          <img v-if="curFormData.data[1].img" :src="curFormData.data[1].img" />
          <div v-else class="flex_column_center_center">
            <AliSvgIcon
              width="40.8px"
              height="33.6px"
              fillColor="#fff"
              iconName="icontupian1"
            />
            <span class="center_tip flex_row_common">宽300*高150</span>
          </div>
        </div>
        <div
          class="flex_com_row_center"
          :style="{
            width: tmp_data.one_width + 'px',
            height: tmp_data.second_height + 'px',
            backgroundColor: curFormData.data[2].img ? '#fff' : empty_bg_color,
          }"
        >
          <img v-if="curFormData.data[2].img" :src="curFormData.data[2].img" />
          <div v-else class="flex_column_center_center">
            <AliSvgIcon
              width="40.8px"
              height="33.6px"
              fillColor="#fff"
              iconName="icontupian1"
            />
            <span class="center_tip flex_row_common">宽300*高150</span>
          </div>
        </div>
      </div>
    </template>
    <template v-else-if="curFormData.sele_style == 5">
      <div class="flex_com_space_between" :style="{ width: screenWidth - 20 + 'px' }">
        <div
          class="flex_com_row_center"
          :style="{
            width: tmp_data.left_width + 'px',
            height: tmp_data.left_width + 'px',
            backgroundColor: curFormData.data[0].img ? '#fff' : empty_bg_color,
          }"
        >
          <img v-if="curFormData.data[0].img" :src="curFormData.data[0].img" />
          <div v-else class="flex_column_center_center">
            <AliSvgIcon
              width="40.8px"
              height="33.6px"
              fillColor="#fff"
              iconName="icontupian1"
            />
            <span class="center_tip flex_row_common">宽200*高200</span>
          </div>
        </div>
        <div
          class="flex_com_row_center"
          :style="{
            width: tmp_data.right_width + 'px',
            height: tmp_data.left_width + 'px',
            backgroundColor: curFormData.data[1].img ? '#fff' : empty_bg_color,
          }"
        >
          <img v-if="curFormData.data[1].img" :src="curFormData.data[1].img" />
          <div v-else class="flex_column_center_center">
            <AliSvgIcon
              width="40.8px"
              height="33.6px"
              fillColor="#fff"
              iconName="icontupian1"
            />
            <span class="center_tip flex_row_common">宽400*高200</span>
          </div>
        </div>
      </div>
      <div class="flex_com_space_between" :style="{ width: screenWidth - 20 + 'px' }">
        <div
          class="flex_com_row_center"
          :style="{
            width: tmp_data.right_width + 'px',
            height: tmp_data.left_width + 'px',
            backgroundColor: curFormData.data[2].img ? '#fff' : empty_bg_color,
          }"
        >
          <img v-if="curFormData.data[2].img" :src="curFormData.data[2].img" />
          <div v-else class="flex_column_center_center">
            <AliSvgIcon
              width="40.8px"
              height="33.6px"
              fillColor="#fff"
              iconName="icontupian1"
            />
            <span class="center_tip flex_row_common">宽400*高200</span>
          </div>
        </div>
        <div
          class="flex_com_row_center"
          :style="{
            width: tmp_data.left_width + 'px',
            height: tmp_data.left_width + 'px',
            backgroundColor: curFormData.data[3].img ? '#fff' : empty_bg_color,
          }"
        >
          <img v-if="curFormData.data[3].img" :src="curFormData.data[3].img" />
          <div v-else class="flex_column_center_center">
            <AliSvgIcon
              width="40.8px"
              height="33.6px"
              fillColor="#fff"
              iconName="icontupian1"
            />
            <span class="center_tip flex_row_common">宽200*高200</span>
          </div>
        </div>
      </div>
    </template>
    <template v-else-if="curFormData.sele_style == 6">
      <div class="flex_com_column_space_betweent_center" :style="{ height: tmp_data.total_height + 'px' }">
        <div
          class="flex_com_row_center"
          :style="{
            width: tmp_data.left_width + 'px',
            height: tmp_data.left_height + 'px',
            backgroundColor: curFormData.data[0].img ? '#fff' : empty_bg_color,
          }"
        >
          <img v-if="curFormData.data[0].img" :src="curFormData.data[0].img" />
          <div v-else class="flex_column_center_center">
            <AliSvgIcon
              width="40.8px"
              height="33.6px"
              fillColor="#fff"
              iconName="icontupian1"
            />
            <span class="center_tip flex_row_common">宽300*高150</span>
          </div>
        </div>
        <div
          class="flex_com_row_center"
          :style="{
            width: tmp_data.left_width + 'px',
            height: tmp_data.left_width + 'px',
            backgroundColor: curFormData.data[1].img ? '#fff' : empty_bg_color,
          }"
        >
          <img v-if="curFormData.data[1].img" :src="curFormData.data[1].img" />
          <div v-else class="flex_column_center_center">
            <AliSvgIcon
              width="40.8px"
              height="33.6px"
              fillColor="#fff"
              iconName="icontupian1"
            />
            <span class="center_tip flex_row_common">宽300*高300</span>
          </div>
        </div>
      </div>
      <div class="flex_com_column_space_betweent_center" :style="{ height: tmp_data.total_height + 'px' }">
        <div
          class="flex_com_row_center"
          :style="{
            width: tmp_data.left_width + 'px',
            height: tmp_data.left_width + 'px',
            backgroundColor: curFormData.data[2].img ? '#fff' : empty_bg_color,
          }"
        >
          <img v-if="curFormData.data[2].img" :src="curFormData.data[2].img" />
          <div v-else class="flex_column_center_center">
            <AliSvgIcon
              width="40.8px"
              height="33.6px"
              fillColor="#fff"
              iconName="icontupian1"
            />
            <span class="center_tip flex_row_common">宽300*高300</span>
          </div>
        </div>
        <div
          class="flex_com_row_center"
          :style="{
            width: tmp_data.left_width + 'px',
            height: tmp_data.left_height + 'px',
            backgroundColor: curFormData.data[3].img ? '#fff' : empty_bg_color,
          }"
        >
          <img v-if="curFormData.data[3].img" :src="curFormData.data[3].img" />
          <div v-else class="flex_column_center_center">
            <AliSvgIcon
              width="40.8px"
              height="33.6px"
              fillColor="#fff"
              iconName="icontupian1"
            />
            <span class="center_tip flex_row_common">宽300*高150</span>
          </div>
        </div>
      </div>
    </template>
    <template v-else-if="curFormData.sele_style == 7">
      <div class="flex_com_column_space_betweent_center" :style="{ height: tmp_data.total_height + 'px' }">
        <div
          class="flex_com_row_center"
          :style="{
            width: tmp_data.left_width + 'px',
            height: tmp_data.left_width + 'px',
            backgroundColor: curFormData.data[0].img ? '#fff' : empty_bg_color,
          }"
        >
          <img v-if="curFormData.data[0].img" :src="curFormData.data[0].img" />
          <div v-else class="flex_column_center_center">
            <AliSvgIcon
              width="40.8px"
              height="33.6px"
              fillColor="#fff"
              iconName="icontupian1"
            />
            <span class="center_tip flex_row_common">宽200*高200</span>
          </div>
        </div>
        <div
          class="flex_com_row_center"
          :style="{
            width: tmp_data.left_width + 'px',
            height: tmp_data.left_width + 'px',
            backgroundColor: curFormData.data[1].img ? '#fff' : empty_bg_color,
          }"
        >
          <img v-if="curFormData.data[1].img" :src="curFormData.data[1].img" />
          <div v-else class="flex_column_center_center">
            <AliSvgIcon
              width="40.8px"
              height="33.6px"
              fillColor="#fff"
              iconName="icontupian1"
            />
            <span class="center_tip flex_row_common">宽200*高200</span>
          </div>
        </div>
      </div>
      <div class="flex_com_column_space_betweent_center" :style="{ height: tmp_data.total_height + 'px' }">
        <div
          class="flex_com_row_center"
          :style="{
            width: tmp_data.left_width + 'px',
            height: tmp_data.left_width + 'px',
            backgroundColor: curFormData.data[2].img ? '#fff' : empty_bg_color,
          }"
        >
          <img v-if="curFormData.data[2].img" :src="curFormData.data[2].img" />
          <div v-else class="flex_column_center_center">
            <AliSvgIcon
              width="40.8px"
              height="33.6px"
              fillColor="#fff"
              iconName="icontupian1"
            />
            <span class="center_tip flex_row_common">宽200*高200</span>
          </div>
        </div>
        <div
          class="flex_com_row_center"
          :style="{
            width: tmp_data.left_width + 'px',
            height: tmp_data.left_width + 'px',
            backgroundColor: curFormData.data[3].img ? '#fff' : empty_bg_color,
          }"
        >
          <img v-if="curFormData.data[3].img" :src="curFormData.data[3].img" />
          <div v-else class="flex_column_center_center">
            <AliSvgIcon
              width="40.8px"
              height="33.6px"
              fillColor="#fff"
              iconName="icontupian1"
            />
            <span class="center_tip flex_row_common">宽200高200</span>
          </div>
        </div>
      </div>
      <div
        class="flex_com_row_center"
        :style="{
          width: tmp_data.left_width + 'px',
          height: tmp_data.total_height + 'px',
          backgroundColor: curFormData.data[4].img ? '#fff' : empty_bg_color,
        }"
      >
        <img v-if="curFormData.data[4].img" :src="curFormData.data[4].img" />
        <div v-else class="flex_column_center_center">
          <AliSvgIcon
            width="40.8px"
            height="33.6px"
            fillColor="#fff"
            iconName="icontupian1"
          />
          <span class="center_tip flex_row_common">宽200高420</span>
        </div>
      </div>
    </template>
    <template v-else-if="curFormData.sele_style == 8">
      <div class="tpzh_moudle_8 flex_row_start_start" style="flex-wrap: wrap">
        <span class="tpzh_moudle_8_h_line"></span>
        <span class="tpzh_moudle_8_v_line"></span>
        <template v-for="(item,index) in curFormData.data" :key="'tupianzuhe8_' + index">
          <div class="tpzh_moudle_8_item flex_column_start_start">
            <div class="flex_column_start_start title_part">
              <span class="main">{{ item.main_title ? item.main_title : '请输入主标题' }}</span>
              <span class="sub">{{ item.sub_title ? item.sub_title : '请输入子标题' }}</span>
            </div>
            <div class="flex_row_start_start img_part">
              <template v-for="(child,child_index) in item.img" :key="'tupianzuhe8_' + index + '_' + child_index">
                <div class="flex_row_center_center img">
                  <img v-if="child.img" :src="child.img" />
                  <div v-else class="empty_swiper_img flex_column_center_center" style="height: 100%">
                    <AliSvgIcon
                      width="27.8px"
                      height="24.6px"
                      fillColor="#fff"
                      iconName="icontupian1"
                    />
                    <span class="center_tip flex_row_common">154*188</span>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </template>
      </div>
    </template>
  </div>
</template>
<script setup>
  import { ref, computed, watch, reactive } from "vue";

  const props = defineProps({
    data: { //数据
      type: Object,
      default: []
    },
    sele_style: { //选择类型：0为第一种图片展示方式，1为第二种，2为第三种，3为第四种，4为第五种，5为第六种，6为第七种，7为第八种，8为第九种
      type: Number,
      default: 8
    },
  });

  const curFormData = ref({});
  const formData = computed(() => {
    return props.data;
  });
  const screenWidth = 371;
  let wrap_style = reactive({});
  let tmp_data = reactive({
    one_width: 0,
    one_height: 0,
    second_height: 0,
    left_width: 0,
    left_height: 0,
    right_width: 0,
    total_height: 0,
  });
  let style = ref({});
  const tip_width = ref(750); //提示图片宽度
  const tip_height = ref('不限'); //提示图片高度
  const empty_bg_color = ref('#DFF2FD');


  const emit = defineEmits(['handleCurSelData','handleChange']);

  watch(
    formData,
    () => {
      if (props.data) {
        curFormData.value = props.data;
      }
      if (curFormData.value['sele_style'] == 0) {
        style.value = {};
        wrap_style = {}
      } else if (curFormData.value['sele_style'] == 1) {
        wrap_style = {}
        style.value = { margin: '8px', marginTop: '0px' };
      } else if (curFormData.value['sele_style'] == 2) {
        wrap_style = { display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', flexWrap: 'wrap' };
        tip_width.value = 350;
        tip_height.value = '不限';
        style.value = { marginTop: '0px', marginRight: '0px', marginBottom: '0px', marginLeft: '8px', width: '173px' };
      } else if (curFormData.value['sele_style'] == 3) {
        wrap_style = { display: 'flex', flexDirection: 'row', justifyContent: 'flex-start', flexWrap: 'wrap' };
        tip_width.value = 240;
        tip_height.value = '不限';
        style.value = { marginTop: '0px', marginRight: '0px', marginBottom: '0px', marginLeft: '8px', width: '113px' };
      } else if (curFormData.value['sele_style'] == 4) {
        wrap_style = {
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          margin: 0,
          paddingLeft: '10px',
          paddingRight: '10px',
          width: screenWidth + 'px',
        };
        tmp_data.one_width = Math.trunc((screenWidth - 30) / 2);
        tmp_data.one_height = tmp_data.one_width + 10;
        tmp_data.second_height = Math.trunc(tmp_data.one_width / 2);
      } else if (curFormData.value['sele_style'] == 5) {
        wrap_style = {
          display: 'flex',
          flexDirection: 'cloumn',
          justifyContent: 'space-between',
          alignItems: 'center',
          margin: 0,
          paddingLeft: '10px',
          paddingRight: '10px',
          height: tmp_data.right_width + 15 + 'px',
          flexWrap: 'wrap',
        };
        tmp_data.left_width = Math.trunc((screenWidth - 30) / 3);
        tmp_data.right_width = tmp_data.left_width * 2;
      } else if (curFormData.value['sele_style'] == 6) {
        wrap_style = {
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          margin: 0,
          paddingLeft: '10px',
          paddingRight: '10px',
          width: screenWidth + 'px',
        };
        tmp_data.left_width = Math.trunc((screenWidth - 30) / 2);//第一个图片的宽
        tmp_data.left_height = Math.trunc(tmp_data.left_width / 2);//第一个图片的高
        tmp_data.total_height = tmp_data.left_width + tmp_data.left_height + 10;//该模块总高度
      } else if (curFormData.value['sele_style'] == 7) {
        wrap_style = {
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          margin: 0,
          paddingLeft: '10px',
          paddingRight: '10px',
          width: screenWidth + 'px',
        };
        tmp_data.left_width = Math.trunc((screenWidth - 40) / 3);//第一个图片的宽
        tmp_data.total_height = tmp_data.left_width * 2 + 10;//该模块总高度
      } else {
        wrap_style = {};
        tmp_data = {
          one_width: 0,
          one_height: 0,
          second_height: 0,
          left_width: 0,
          left_height: 0,
          right_width: 0,
          total_height: 0,
        }
      }
    },
    { deep: true, immediate: true },
  );

</script>
<style lang="less" scoped>
.tupianzuhe {
  .item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .item_0 {
    img {
      width: 100%;
    }
  }

  img {
    max-width: 100%;
    max-height: 100%;
  }
}

.tpzh_moudle_8 {
  position: relative;
  width: 371px;
  height: 364px;
  padding: 0 10px;
  // background: #fff;

  .tpzh_moudle_8_h_line{
    position: absolute;
    top:182px;
    left:0;
    width: 371px;
    height: 1px;
    background: #DDD;
  }

  .tpzh_moudle_8_v_line{
    position: absolute;
    top: 0;
    left: 185px;
    width: 1px;
    height: 364px;
    background: #DDD;
  }

  .tpzh_moudle_8_item{
    .title_part{
      .main{
        margin-top: 16px;
        color: #333;
        font-size: 18px;
        font-weight: 600;
        line-height: 18px;
      }

      .sub{
        margin-top: 11px;
        margin-bottom: 12px;
        color: #333;
        font-size: 13px;
        font-weight: 400;
        line-height: 18px;
      }
    }

    &:nth-child(2n+1){
      margin-right: 10px;
    }

    &:nth-child(2n){
      margin-left: 9px;
    }

    .img_part{
      .img{
        width: 78px;
        height: 93px;
        overflow: hidden;
        background: #ecf5ff;
      }

      .img:first-child{
        margin-right: 9px;
      }
    }
  }
}
</style>