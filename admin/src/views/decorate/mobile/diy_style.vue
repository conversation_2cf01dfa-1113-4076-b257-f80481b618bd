<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <SldComHeader title="风格配置"></SldComHeader>
      <div class="diy_style_mobile">
        <section>
          <div class="flex flex-row justify-start items-center">
            <div
              @click="selectColor(index)"
              class="flex_row_center_center diy_style_item"
              :class="{ active: color_index == index }"
              v-for="(item, index) in color_list"
              :key="index"
            >
              <div class="diy_style_color flex_row_center_center">
                <div class="diy_style_color_left" :style="{ background: item.mainColor }"></div>
                <div class="diy_style_color_middle" :style="{ background: item.subColor }"></div>
                <div class="diy_style_color_right" :style="{ background: item.priceColor }"></div>
              </div>
              <div class="diy_style_name">{{ item.name }}</div>
              <img class="diy_style_checked" :src="check_img" v-if="color_index == index" />
            </div>
  
            <div
              @click="selectAutoColor"
              class="flex_row_center_center diy_style_item"
              :class="{ active: color_index == color_list.length }"
            >
              <div class="diy_style_color flex_row_center_center" v-if="isAutoSelect">
                <div class="diy_style_color_left" :style="{ background: color_auto.mainColor }"></div>
                <div
                  class="diy_style_color_middle"
                  :style="{ background: color_auto.subColor }"
                ></div>
                <div
                  class="diy_style_color_right"
                  :style="{ background: color_auto.priceColor }"
                ></div>
              </div>
              <div class="diy_style_auto_name" v-else>自定义颜色选择</div>
              <div class="diy_style_name" v-if="isAutoSelect">重选</div>
              <div class="diy_style_auto_arrow" v-else>&gt;</div>
              <img
                class="diy_style_checked"
                :src="check_img"
                v-if="color_index == color_list.length"
              />
            </div>
          </div>
          <div class="prew_tips">颜色说明： 从左至右三个颜色依次为·主色、辅助色、价格色</div>
        </section>
        <section style="padding-bottom: 20px">
          <div class="prew_title">图片预览</div>
          <div class="prew_list flex_row_start_center">
            <MobilePanel1 :currentColor="currentColor"></MobilePanel1>
            <MobilePanel2 :currentColor="currentColor"></MobilePanel2>
            <MobilePanel3 :currentColor="currentColor"></MobilePanel3>
          </div>
        </section>
      </div>

      <div class="m_diy_bottom_wrap" :style="{ position: 'fixed', left: getRealWidth + 10 + 'px' }">
        <div @click="handleSaveAllData" class="add_goods_bottom_btn add_goods_bottom_btn_sel">
          保存
        </div>
      </div>

      <Modal
        title="自定义颜色选择"
        :width="500"
        :visible="modalVisible"
        @cancel="modalVisible = false"
        @ok="modalColorConfirm"
      >
        <div class="flex_row_start_center diy_auto_color_modal">
          <div class="color_title">主色选择：</div>
          <div class="color_show flex items-center">
            <div class="show_color">
              <label for="color_main">
                <span :style="{ backgroundColor: color_auto_modal.mainColor }"></span>
              </label>
              <Input
                type="color"
                id="color_main"
                style="visibility: hidden"
                @change="(e) => changeColor(e, 'mainColor')"
              ></Input>
            </div>
            <a @click="resetColor('mainColor')">重置</a>
          </div>
        </div>
        <div class="flex_row_start_center diy_auto_color_modal">
          <div class="color_title">辅助色选择：</div>
          <div class="color_show flex items-center">
            <div class="show_color">
              <label for="color_other">
                <span :style="{ backgroundColor: color_auto_modal.subColor }"></span>
              </label>
              <Input
                type="color"
                id="color_other"
                style="visibility: hidden"
                @change="(e) => changeColor(e, 'subColor')"
              ></Input>
            </div>
            <a @click="resetColor('subColor')">重置</a>
          </div>
        </div>
        <div class="flex_row_start_center diy_auto_color_modal">
          <div class="color_title">价格颜色选择：</div>
          <div class="color_show flex items-center">
            <div class="show_color">
              <label for="color_price">
                <span :style="{ backgroundColor: color_auto_modal.priceColor }"></span>
              </label>
              <Input
                type="color"
                id="color_price"
                style="visibility: hidden"
                @change="(e) => changeColor(e, 'priceColor')"
              ></Input>
            </div>
            <a @click="resetColor('priceColor')">重置</a>
          </div>
        </div>
      </Modal>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'mobile_diy_page_lists',
  };
</script>
<script setup>
  import { computed, reactive, ref, unref } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import {
    MobilePanel1,
    MobilePanel2,
    MobilePanel3,
  } from '/@/components/SldDiyStyle';
  import { Modal, Input } from 'ant-design-vue';
  import { getImagePath, set16ToRgb } from '/@/utils';
  import { useDebounceFn } from '@vueuse/core';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import { getSettingUpdateApi, getSettingListApi } from '@/api/common/common';
  import { onMounted } from 'vue';
  import { isDef, isNull } from '/@/utils/is';
  import { failTip, sucTip } from '/@/utils/utils';
  
  const { getRealWidth } = useMenuSetting();
  const color_index = ref(0);
  const check_img = getImagePath('images/diy_style_checked.png');
  const color_list = [
    {
      mainColor: '#F41410',
      mainOpacityColor: '#F414101A',
      mainLinearColor: 'linear-gradient(90deg, #F41410BF, #F41410)',
      mainLinearColorZero: 'linear-gradient(0deg, #F41410B3, #F41410)', //主色纵向渐变
      subColor: '#FF8828',
      subLinearColor: 'linear-gradient(90deg, #FF8828BF, #FF8828)',
      priceColor: '#F30300',
      name: '默认色',
    },
    {
      mainColor: '#07E188',
      mainOpacityColor: '#07E1881A',
      mainLinearColor: 'linear-gradient(90deg, #07E188BF, #07E188)',
      mainLinearColorZero: 'linear-gradient(0deg, #07E188B3, #07E188)',
      subColor: '#4E4E61',
      subLinearColor: 'linear-gradient(90deg, #4E4E61BF, #4E4E61)',
      priceColor: '#13D5AE',
      name: '翡翠绿',
    },
    {
      mainColor: '#E73C60',
      mainOpacityColor: '#E73C601A',
      mainLinearColor: 'linear-gradient(90deg, #E73C60BF, #E73C60)',
      mainLinearColorZero: 'linear-gradient(0deg, #E73C60B3, #E73C60)',
      subColor: '#F09B38',
      subLinearColor: 'linear-gradient(90deg, #F09B38BF, #F09B38)',
      priceColor: '#E84165',
      name: '雅致粉',
    },
    {
      mainColor: '#C58941',
      mainOpacityColor: '#C589411A',
      mainLinearColor: 'linear-gradient(90deg, #C58941BF, #C58941)',
      mainLinearColorZero: 'linear-gradient(0deg, #C58941B3, #C58941)',
      subColor: '#454444',
      subLinearColor: 'linear-gradient(90deg, #454444BF, #454444)',
      priceColor: '#DD1814',
      name: '奢华金',
    },
    {
      mainColor: '#1C1A19',
      mainOpacityColor: '#1C1A191A',
      mainLinearColor: 'linear-gradient(90deg, #1C1A19BF, #1C1A19)',
      mainLinearColorZero: 'linear-gradient(0deg, #1C1A19B3, #1C1A19)',
      subColor: '#F09B38',
      subLinearColor: 'linear-gradient(90deg, #F09B38BF, #F09B38)',
      priceColor: '#F09B38',
      name: '雅酷黑',
    },
    {
      mainColor: '#ED662E',
      mainOpacityColor: '#ED662E1A',
      mainLinearColor: 'linear-gradient(90deg, #ED662EBF, #ED662E)',
      mainLinearColorZero: 'linear-gradient(0deg, #ED662EB3, #ED662E)',
      subColor: '#F5BF41',
      subLinearColor: 'linear-gradient(90deg, #F5BF41BF, #F5BF41)',
      priceColor: '#ED6A32',
      name: '活力橙',
    },
    {
      mainColor: '#1F76F5',
      mainOpacityColor: '#1F76F51A',
      mainLinearColor: 'linear-gradient(90deg, #1F76F5BF, #1F76F5)',
      mainLinearColorZero: 'linear-gradient(0deg, #1F76F5B3, #1F76F5)',
      subColor: '#534E61',
      subLinearColor: 'linear-gradient(90deg, #534E61BF, #534E61)',
      priceColor: '#237AF5',
      name: '天空蓝',
    },
    {
      mainColor: '#AB09CB',
      mainOpacityColor: '#AB09CB1A',
      mainLinearColor: 'linear-gradient(90deg, #AB09CBBF, #AB09CB)',
      mainLinearColorZero: 'linear-gradient(0deg, #AB09CBB3, #AB09CB)',
      subColor: '#F09B38',
      subLinearColor: 'linear-gradient(90deg, #F09B38BF, #F09B38)',
      priceColor: '#AF10CD',
      name: '紫幽兰',
    },
  ]; //颜色列表

  const color_auto = reactive({
    //自定义颜色
    mainColor: '', //主色
    mainOpacityColor: '', //主色透明色
    mainLinearColor: '', //主色渐变色
    mainLinearColorZero: '',
    subColor: '', //辅助色
    subLinearColor: '', //辅助渐变色
    priceColor: '', //价格颜色
  });
  const color_auto_modal = reactive({
    //自定义颜色弹窗选择
    mainColor: '', //主色
    mainOpacityColor: '', //主色透明色
    mainLinearColor: '', //主色渐变色
    mainLinearColorZero: '',
    subColor: '', //辅助色
    subLinearColor: '', //辅助渐变色
    priceColor: '', //价格颜色
  });

  const isAutoSelect = computed(
    () =>
      (color_auto.mainColor && color_auto.mainColor != '#fff') ||
      (color_auto.subColor && color_auto.subColor != '#fff') ||
      (color_auto.priceColor && color_auto.priceColor != '#fff'),
  );

  const modalVisible = ref(false);

  const selectColor = (index) => {
    color_index.value = index;
  };

  const currentColor = computed(() =>
    color_index.value < color_list.length ? color_list[color_index.value] : color_auto,
  );

  //选择自定义颜色
  const selectAutoColor = () => {
    for (var key in color_auto) {
      color_auto_modal[key] = color_auto[key];
    }
    modalVisible.value = true;
  };

  const changeColor = useDebounceFn((e, key) => {
    color_auto_modal[key] = e.target.value;
    let rgbColor = set16ToRgb(color_auto_modal[key], 1);
    if (key == 'mainColor') {
      color_auto_modal.mainOpacityColor = color_auto_modal[key] + '1A';
      color_auto_modal.mainLinearColor = `linear-gradient(90deg, ${rgbColor.replace(
        ',1)',
        ',.75)',
      )}, ${color_auto_modal[key]})`;
      color_auto_modal.mainLinearColorZero = `linear-gradient(0deg, ${rgbColor.replace(
        ',1)',
        ',.7)',
      )}, ${color_auto_modal[key]})`;
    } else if (key == 'subColor') {
      color_auto_modal.subLinearColor = `linear-gradient(90deg, ${rgbColor.replace(
        ',1)',
        ',.75)',
      )}, ${color_auto_modal[key]})`;
    }
  }, 400);

  const resetColor = (key) => {
    color_auto_modal[key] = '#fff';
  };

  const modalColorConfirm = () => {
    let resetFlag = 0;
    for (var key in color_auto_modal) {
      if (color_auto_modal[key] == '#fff' || !color_auto_modal[key]) {
        resetFlag += 1;
      }
      color_auto[key] = color_auto_modal[key];
    }
    color_index.value = resetFlag == 3 ? 0 : color_list.length;
    modalVisible.value = false;
  };

  const handleSaveAllData = async () => {
    let params = JSON.stringify(unref(currentColor));
    const res = await getSettingUpdateApi({ mobile_mall_style: params });
    if (res?.state == 200) {
      sucTip(res.msg);
    } else {
      failTip(res.msg);
    }
  };

  const judgeColor = (useColor, targetColor) => {
    return useColor == '#fff' ? '#FFFFFF' : targetColor ?? '';
  };

  const getSetting = async () => {
    const res = await getSettingListApi({ str: 'mobile_mall_style' });
    if (res?.state == 200) {
      let [mobile_mall_style] = res.data;
      let colorSet = {};
      let data =
        mobile_mall_style && mobile_mall_style.value ? JSON.parse(mobile_mall_style.value) : {};
      if (Object.keys(data).length > 0) {
        color_index.value = color_list.length;
        ['mainColor', 'mainOpacityColor', 'mainLinearColor','mainLinearColorZero'].forEach((key) => {
          colorSet[key] = judgeColor(data.mainColor, data[key]);
        });

        ['subColor', 'subLinearColor'].forEach((key) => {
          colorSet[key] = judgeColor(data.subColor, data[key]);
        });

        colorSet.priceColor = judgeColor(data.priceColor, data.priceColor);

        let isAllDef = Object.keys(colorSet).every(
          (item) => isDef(colorSet[item]) && !isNull(colorSet[item]),
        );
        if (isAllDef) {
          for (var index = 0; index < color_list.length; index++) {
            let isAllEqual = Object.keys(colorSet).every((item) =>
              Object.is(colorSet[item], color_list[index][item]),
            );
            if (isAllEqual) {
              color_index.value = index;
              break;
            }
          }
          if (color_index.value == color_list.length) {
            color_index.value = color_list.length;
            Object.keys(colorSet).forEach((item, index) => {
              color_auto[item] = colorSet[item];
            });
          }
        }
      }
    }
  };

  onMounted(getSetting);
</script>

<style lang="less">
  .prew_tips {
    margin-bottom: 10px;
    color: #999;
    font-family: 'PingFang SC';
    font-size: 12px;
    font-weight: 500;
  }

  .prew_item_title {
    flex-shrink: 0;
    width: 60px;
    margin-right: 18px;
    margin-left: 70px;
    color: #222;
    font-family: 'Microsoft YaHei';
    font-size: 14px;
    font-weight: 400;
    line-height: 35px;
    text-align: right;
  }

  .prew_title {
    margin-top: 10px;
    margin-bottom: 10px;
    color: #222;
    font-family: 'PingFang SC';
    font-size: 15px;
    font-weight: bold;
    line-height: 40px;
  }

  .prew_pc_list {
    .prew_item {
      width: 1274px;
      height: 696px;
      margin-bottom: 35px;
    }
  }

  .diy_style_item {
    position: relative;
    width: 122px;
    height: 48px;
    margin-right: 10px;
    margin-bottom: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;

    &.active {
      border-color: #fc701e;
    }

    .diy_style_color {
      width: 46px;
      height: 20px;
      overflow: hidden;

      .diy_style_color_left {
        flex: 1;
        height: 20px;
      }

      .diy_style_color_middle {
        flex: 1;
        height: 20px;
        margin-right: 2px;
        margin-left: 2px;
      }

      .diy_style_color_right {
        flex: 1;
        height: 20px;
      }
    }

    .diy_style_name {
      margin-left: 8px;
      color: #666;
      font-size: 12px;
      text-align: center;
    }

    .diy_style_auto_name {
      color: #fc701e;
      font-size: 12px;
    }

    .diy_style_auto_arrow {
      flex-shrink: 0;
      width: 10px;
      height: 22px;
      margin-left: 5px;
      color: #fc701e;
      font-family: cursive;
      font-size: 14px;
      font-weight: 600;
    }

    .diy_style_checked {
      position: absolute;
      z-index: 1;
      right: -1px;
      bottom: -1px;
      width: 20px;
      height: 20px;
    }
  }

  .diy_auto_color_modal {
    margin-top: 10px;
    margin-bottom: 10px;

    .color_title {
      width: 115px;
      margin-right: 5px;
      text-align: right;
    }

    .color_show {
      .show_color {
        display: inline-block;
        width: 50px;
        height: 30px;
        margin-right: 10px;
        padding: 5px;
        overflow-y: hidden;
        border: 1px solid #eee;
        line-height: 0;
        cursor: pointer;
      }

      span {
        display: inline-block;
        width: 100%;
        height: 100%;
      }
    }

    .color_picker_wrap {
      position: absolute;
      z-index: 2;

      .color_picker_mask {
        position: fixed;
        inset: 0;
      }
    }
  }

  .prew_list {
    width: 1240px;
    padding-left: 15px;
  }

  .diy_style_mobile{
    height: calc(100vh - 200px);
    overflow: auto;
  }
</style>
