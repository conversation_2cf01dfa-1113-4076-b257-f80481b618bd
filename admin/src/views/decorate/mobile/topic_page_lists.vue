<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <SldComHeader :type="1" title="首页装修" />
      <BasicTable @register="standardTable" style="padding: 0">
        <template #tableTitle>
          <div class="toolbar flex flex-row">
            <div class="toolbar_btn" @click="handleClick(null, 'add')">
              <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#fff" />
              <span>新增页面</span>
            </div>

            <div class="ml-2">
              <InputSearch
                v-model:value="searchName"
                placeholder="请输入页面名称"
                enter-button="搜索"
                class="text-xs"
                @search="onSearch"
                :allow-clear="true"
              ></InputSearch>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.key == 'action'">
            <div class="flex flex-row items-center justify-center">
              <span
                class="common_page_edit cursor-pointer hover:text-[#FF6A12]"
                @click="handleClick(record, 'diy')"
                >装修</span
              >
              <span
                class="common_page_edit cursor-pointer hover:text-[#FF6A12]"
                @click="handleClick(record, 'edit')"
                >编辑</span
              >

              <span
                class="common_page_edit cursor-pointer hover:text-[#FF6A12]"
                @click="handleClick(record, 'copy')"
                >复制</span
              >

              <Popconfirm
                title="删除后不可恢复，是否确定删除？"
                @confirm="handleClick(record, 'del')"
              >
                <span class="common_page_edit cursor-pointer hover:text-[#FF6A12]">删除</span>
              </Popconfirm>
            </div>
          </template>
        </template>
      </BasicTable>
    </div>

    <SldModal
      v-bind="modalState"
      @confirm-event="handleConfirm"
      @cancle-event="modalState.visible = false"
    />
  </div>
</template>
<script>
  export default {
    name: 'mobile_diy_page_lists',
  };
</script>
<script setup>
  import { BasicTable, useTable } from '/@/components/Table';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import {
    getMobleDecoListApi,
    updateIsUse,
    decoMobileAddApi,
    decoMobileDelete,
    decoMobileCopy,
    updateMobileDeco,
  } from '@/api/decorate/deco_m';
  import SldModal from '/@/components/SldModal/index.vue';
  import { Popconfirm, InputSearch } from 'ant-design-vue';
  import { reactive, ref } from 'vue';
  import { sucTip, failTip } from '/@/utils/utils';
  import { useRouter } from 'vue-router';

  const router = useRouter();

  const deco_columns = [
    { title: '页面名称', dataIndex: 'name' },
    { title: '创建时间', dataIndex: 'createTime' },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      customRender: ({ record }) => record.updateTime || '--',
    },
  ];

  const searchName = ref('');

  const content = ref([
    {
      type: 'input',
      label: '页面名称',
      name: 'name',
      placeholder: '请输入装修页面名称',
      extra: '最多输入8个字',
      maxLength: 8,
      showCount: true,
      rules: [
        {
          required: true,
          whitespace: true,
          message: '请输入页面名称',
        },
      ],
      initialValue: '',
    },
  ]);

  const modalState = reactive({
    width: 600,
    title: '新增装修页面',
    visible: false,
    content: content.value,
    confirmBtnLoading: false,
    showFoot: true,
    decoId: 0,
    type: 'add',
  });

  const handleConfirm = async (params, resetFields) => {
    modalState.confirmBtnLoading = true;
    const res = await (modalState.type == 'add'
      ? decoMobileAddApi({ ...params, type: 'topic' })
      : updateMobileDeco({ ...params, type: 'topic', decoId: modalState.decoId }));
    modalState.confirmBtnLoading = false;
    if (res?.state == 200) {
      modalState.visible = false;
      resetFields();
      reload();
    } else {
      failTip(res.msg);
    }
  };

  const [standardTable, { reload, setLoading }] = useTable({
    api: getMobleDecoListApi,
    columns: deco_columns,
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    actionColumn: {
      width: 200,
      title: '操作',
      dataIndex: 'action',
      align: 'center',
    },
    pagination: {
      pageSize: 10,
    },
    searchInfo: {
      type: 'topic',
    },
    bordered: true,
    striped: false,
    beforeFetch({ ...params }) {
      let targetParams = params;
      if (searchName.value) {
        targetParams.name = searchName.value;
      }
      return targetParams;
    },
  });

  const handleClick = (record, type) => {
    if (type == 'diy') {
      router.push(
        `/decorate_m/topic_lists_to_diy?id=${record.decoId}&source=decorate_m/topic_lists&type=topic`,
      );
    } else if (type == 'del') {
      setLoading(true);
      decoMobileDelete({ decoId: record.decoId }).then((res) => {
        setLoading(false);
        if (res.state == 200) {
          reload();
          sucTip(res.msg);
        } else {
          failTip(res.msg);
        }
      });
    } else if (type == 'copy') {
      setLoading(true);
      decoMobileCopy({ decoId: record.decoId }).then((res) => {
        setLoading(false);
        if (res.state == 200) {
          reload();
          sucTip(res.msg);
        } else {
          failTip(res.msg);
        }
      });
    } else if (type == 'preview') {
      window.open(record.pcUrl);
    } else if (type == 'add') {
      modalState.type = type;
      modalState.visible = true;
    } else if (type == 'edit') {
      modalState.type = type;
      modalState.decoId = record.decoId;
      content.value[0].initialValue = record.name;
      modalState.visible = true;
    }
  };

  const switchChange = async (record, key) => {
    const res = await updateIsUse({
      decoId: record.decoId,
      isUse: record.isUse == 1 ? 0 : 1,
      os: key,
    });
    if (res?.state == 200) {
      reload();
      sucTip(res.msg);
    } else {
      failTip(res.msg);
    }
  };

  const openEditDecoName = (record) => {
    record.is_edit = true;
    record.edit_name = record.decoName;
  };

  const setEditDecoName = (record, type) => {
    if (type == 'close') record.is_edit = false;
    if (type == 'confirm') {
      setLoading(true);
      UpdateDecoApi({ decoId: record.decoId, decoName: record.edit_name }).then((res) => {
        setLoading(false);
        if (res.state == 200) {
          record.is_edit = false;
          record.decoName = record.edit_name;
          sucTip(res.msg);
        } else {
          failTip(res.msg);
        }
      });
    }
  };

  const onSearch = () => {
    reload();
  };
</script>

<style scoped>
  .diy_input {
    font-size: 13px;
  }
</style>
