<!-- TAB切换组件 -->
<template>
  <div
    class="tab_wrap flex_column_start_start"
    :style="{
      background: data.color ? data.color : '#fff',
      paddingTop: data.top_margin + 'px',
      paddingBottom: data.bottom_margin + 'px',
    }"
  >
    <div class="tab_top flex_row_start_center">
      <template v-if="(curFormData.data.length == 0 || (curFormData.data.length == 1 && curFormData.data[0].title == '' && curFormData.data[0].sub_title == ''))">
        <template v-for="(item, index) in sld_com_empty_arrar_9" :key="index">
          <div class="item flex_column_between_center" @click="onChange(index)">
            <div class="tab_name_part flex_column_between_center">
              <span class="tab_name">全部</span>
              <span class="line" :class="curFormData.nav_current == index ? 'current_nav' : null"></span>
            </div>
            <span class="tab_sub_name">全部分类</span>
          </div>
        </template>
      </template>
      <template v-else>
        <template v-for="(item, index) in curFormData.data" :key="index">
          <div class="item flex_column_between_center" @click="onChange(index)">
            <div class="tab_name_part flex_column_between_center">
              <span class="tab_name">{{ item.title }}</span>
              <span class="line" :class="curFormData.nav_current == index ? 'current_nav' : null"></span>
            </div>
            <span class="tab_sub_name">{{ item.sub_title }}</span>
          </div>
        </template>
      </template>
    </div>
    <div class="tab_content flex_row_start_start">
      <!-- 空数据默认展示空商品 -->
      <template v-if="curFormData.data.length == 0">
        <template v-for="(item) in sld_com_empty_arrar_4" :key="item">
          <MoreTabContent
            type="goods"
            :empty="true"
            :border_radius="curFormData.border_radius"
            :diy_type="diy_type"
          ></MoreTabContent>
        </template>
      </template>

      <template v-if="curFormData.data.length > 0 && curFormData.data[curFormData.nav_current] == undefined">
        <template v-for="(item) in sld_com_empty_arrar_4" :key="item">
          <MoreTabContent
            type="goods"
            :empty="true"
            :border_radius="curFormData.border_radius"
            :diy_type="diy_type"
          ></MoreTabContent>
        </template>
      </template>

      <template v-if="curFormData.data.length > 0 && curFormData.data[curFormData.nav_current] != undefined && curFormData.data[curFormData.nav_current].info.length == 0">
        <template v-for="(item) in sld_com_empty_arrar_4" :key="item">
          <MoreTabContent
            :type="curFormData.data[data.nav_current].data_type"
            :empty="true"
            :data="curFormData.data[data.nav_current]"
            :border_radius="curFormData.border_radius"
            :diy_type="diy_type"
          ></MoreTabContent>
        </template>
      </template>

      <template v-if="curFormData.data.length > 0 && curFormData.data[curFormData.nav_current] != undefined && curFormData.data[curFormData.nav_current].info.length > 0">
        <template v-for="(info_item, info_index) in curFormData.data[curFormData.nav_current].info" :key="info_index">
          <MoreTabContent
            :info="info_item"
             v-if="curFormData.data[curFormData.nav_current].data_type!='goods'||(curFormData.data[curFormData.nav_current].data_type=='goods'&&(!info_item.state||(info_item.state&&info_item.state==3)))"
            :index="info_index"
            :data="curFormData.data[data.nav_current]"
            :border_radius="curFormData.border_radius"
            :diy_type="diy_type"
          ></MoreTabContent>
        </template>
      </template>
    </div>
  </div>
</template>
<script setup>
  import { ref, computed, watch } from 'vue';
  import { sld_com_empty_arrar_9, sld_com_empty_arrar_4 } from '@/utils/utils';
  import MoreTabContent from './more_tab_content.vue';

  const props = defineProps({
    data: {
      type: Object,
      default: {
        top_margin: 10,
        bottom_margin: 0,
        color: '#fff',
        border_radius: 8, //数据是否圆角，默认圆角
        nav_current: 0, //当前选中的导航
        data: [], //TAB切换数据
      }
    },
    diy_type: { type: String, default: 'home' }, //装修来源类型: home-首页手机装修 topic-专题装修 integral-积分商城装修 spreader-推手装修
  });
  const curFormData = ref({
    border_radius: 8,
    nav_current: 0,
    data: [],
  });

  const formData = computed(() => {
    return props.data;
  })

  watch(
    formData,
    () => {
      if (props.data.data != undefined && props.data.data.length > 0
       && props.data.nav_current != undefined
       && (props.data.nav_current+1 > props.data.data.length)) {
        props.data.nav_current = 0;
      }
      curFormData.value = props.data;
    },
    { deep: true, immediate: true },
  );

  //切换菜单
  function onChange(val) {
    curFormData.value['nav_current'] = val;
  };
</script>
<style lang="less" scoped>
  .tab_wrap {
    width: 100%;

    .tab_top {
      width: 100%;
      height: 65px;
      padding: 0 10px;
      overflow-x: auto;
      overflow-y: hidden;
      background: #fff;

      .item {
        flex-shrink: 0;
        height: 100%;
        margin-right: 20px;
        padding: 7px 0;
        cursor: pointer;

        .tab_name_part {
          .tab_name {
            color: #2E2E2E;
            font-size: 14px;
            line-height: 14px;
          }

          .line {
            width: 100%;
            height: 4px;
            margin-top: 7px;
            border-radius: 2px;
            background: #fff;
          }

          .line.current_nav {
            background: #FC1C1C !important;
          }
        }

        .tab_sub_name {
          color: #666;
          font-size: 12px;
          line-height: 12px;
        }
      }
    }

    .tab_content {
      flex-wrap: wrap;
      align-items: start;
      width: 100%;
      padding-bottom: 10px;
      background: #f5f5f5;

      .goods_item {
        width: 171px;
        height: 252px;
        margin-top: 10px;
        margin-left: 10px;
        background: #fff;

        .goods_img {
          width: 171px;
          height: 171px;
          background-repeat: no-repeat;
          background-position: center center;
          background-size: cover;
        }

        .goods_name {
          display: -webkit-box;
          width: 100%;
          height: 36px;
          margin-top: 10px;
          padding: 0 10px;
          overflow: hidden;
          color: #2E2E2E;
          font-size: 14px;
          line-height: 18px;
          text-overflow: ellipsis;
          word-break: break-word;
          -webkit-line-clamp: 2;

          /*! autoprefixer: off */
          -webkit-box-orient: vertical;

        }

        .bottom_part {
          width: 100%;
          padding: 0 10px;

          .price {
            align-items: baseline;
            margin-top: 9px;
            color: #FF2C20;

            .unit {
              font-size: 12px;
            }

            .price_int {
              margin-left: 1px;
              font-size: 16px;
              line-height: 16px;
            }

            .price_decimal {
              font-size: 12px;
            }
          }
        }
      }

      .live_item {
        width: 171px;
        height: 252px;
        margin-top: 10px;
        margin-left: 10px;
        background: #fff;

        .empty_live_img {
          width: 100%;
          height: 100%;
        }

        .live_img {
          position: relative;
          width: 171px;
          height: 171px;
          background-repeat: no-repeat;
          background-position: center center;
          background-size: cover;

          .live_click {
            position: absolute;
            z-index: 2;
            top: 0;
            left: 0;
            height: 18px;
            padding: 0 5px 0 7px;
            background: rgb(0 0 0 / 20%);
            font-size: 12px;

            .play_icon {
              width: 14px;
              height: 11px;
            }

            .back_icon {
              width: 12px;
              height: 12px;
            }

            .live_click_num {
              margin-left: -1px;
              transform: scale(0.9);
              color: #fff;
              font-size: 12px;
            }
          }

          .right_bottom_icon {
            position: absolute;
            z-index: 2;
            right: 2px;
            bottom: 4px;
            width: 50px;
          }
        }

        .live_name {
          display: -webkit-box;
          height: 36px;
          margin-top: 10px;
          padding: 0 10px;
          overflow: hidden;
          color: #2E2E2E;
          font-size: 14px;
          line-height: 18px;
          text-overflow: ellipsis;
          word-break: break-word;
          -webkit-line-clamp: 2;

          /*! autoprefixer: off */
          -webkit-box-orient: vertical;

        }

        .bottom_part {
          position: relative;
          width: 100%;
          margin-top: 7px;
          padding: 0 10px;

          .left {
            .author_avator {
              width: 20px;
              height: 20px;
              margin-right: 6px;
              border-radius: 50%;
            }

            .author_nick_name {
              width: 80px;
              overflow: hidden;
              color: #999;
              font-size: 12px;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }

          .live_status {
            position: absolute;
            right: -12px;
            bottom: -6px;
            flex-shrink: 0;
            padding: 0 9px;
            transform: scale(0.5);
            border-radius: 16px;
            color: #fff;
            font-size: 22px;
          }
        }
      }

      .svideo_item {
        width: 171px;
        height: 253px;
        margin-top: 10px;
        margin-left: 10px;
        background: #fff;

        .empty_svideo_img {
          width: 100%;
          height: 100%;
        }

        .svideo_img {
          position: relative;
          width: 171px;
          height: 171px;
          background-repeat: no-repeat;
          background-position: center center;
          background-size: cover;

          .video_click {
            display: inline-block;
            height: 17px;
            margin: 5px;
            border-radius: 8px;
            background: linear-gradient(90deg, #666 0%, rgb(0 0 0 / 0%) 100%);
            font-size: 12px;

            .play_icon {
              width: 11px;
              height: 11px;
              margin-top: -2px;
              margin-left: 3px;
            }

            .video_click_num {
              display: inline-block;
              margin-left: -2px;
              transform: scale(0.85);
              color: #fff;
              font-size: 12px;
            }
          }
        }

        .svideo_name {
          display: -webkit-box;
          height: 36px;
          margin-top: 10px;
          padding: 0 10px;
          overflow: hidden;
          color: #2E2E2E;
          font-size: 14px;
          line-height: 18px;
          text-overflow: ellipsis;
          word-break: break-word;
          -webkit-line-clamp: 2;

          /*! autoprefixer: off */
          -webkit-box-orient: vertical;

        }

        .bottom_part {
          position: relative;
          width: 100%;
          margin-top: 7px;
          padding: 0 10px;

          .left {
            .author_avator {
              width: 20px;
              height: 20px;
              margin-right: 6px;
              border-radius: 50%;
            }

            .author_nick_name {
              width: 80px;
              overflow: hidden;
              color: #999;
              font-size: 12px;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
      }
    }
  }

  .tab_content {
    flex-wrap: wrap;
    align-items: start;
    width: 100%;
    padding-bottom: 10px;
    background: #f5f5f5;
  }
</style>