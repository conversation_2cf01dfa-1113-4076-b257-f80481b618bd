<template>
  <PageWrapper>
    <div class="mobile_diy_page">
      <SldComHeader :type="1" :title="title" :back="true" />
      <div class="flex_row_between_start mobile_diy_page_main">
        <div class="left_menu">
          <VueDraggable
            v-model="detail.menu"
            animation="150"
            :group="{ name: 'people', pull: 'clone', put: false }"
            :removeCloneOnHide="true"
            :sort="false"
            class="flex_row_start_start left_menu_body"
            filter=".unDrag"
          >
            <div
              v-for="item in detail.menu"
              :key="item.tplId"
              class="flex_column_center_center left_menu_item"
              :class="item.type == 'top_cat' ? 'unDrag' : ''"
            >
              <!-- 顶部分类导航-s -->
              <template v-if="item.type == 'top_cat'">
                <div class="left_menu_item_icon">
                  <AliSvgIcon
                    width="35px"
                    height="35px"
                    fillColor="#FC701E"
                    iconName="iconziyuan16"
                  />
                </div>
                <span style="color: #333">顶部分类</span>
                <div class="left_menu_item_active flex_row_center_center" @click="setTopCatNav">{{
                  show_top_cat_nav ? '停用' : '启用'
                }}</div>
              </template>
              <!-- 顶部分类导航-e -->
              <template v-else>
                <div class="left_menu_item_icon">
                  <AliSvgIcon
                    width="35px"
                    height="35px"
                    fillColor="#fc701e"
                    :iconName="'icon' + item.icon"
                  />
                </div>
                <span>{{ item.name }}</span>
              </template>
            </div>
          </VueDraggable>
        </div>
        <div class="main_data">
          <div class="flex_row_center_start main_data_body">
            <div class="main_data_body_main">
              <img class="fixed_top_img" src="@/assets/images/m_diy_img/m_top_status_bar.png" />
              <!-- 固定的顶部组件 - s -->
              <div
                v-if="show_top_cat_nav"
                class="flex_column_start_start fixed_top"
                :class="is_selected_top_cat_nav ? 'active' : ''"
                @click="selectTopCatNav"
                :style="{
                  borderColor: is_selected_top_cat_nav ? '#FC701E' : '#fff',
                  paddingTop: 48 + 'px',
                  paddingBottom: center_fixed_data.bottom_margin + 'px',
                  background: center_fixed_data.color,
                }"
              >
                <div
                  class="flex_row_start_center cat_nav"
                  :style="{ backgroundColor: cur_top_swiper_bg_color }"
                >
                  <div class="flex_row_start_center cat_nav_left">
                    <span
                      v-for="(item, index) in detail.category"
                      :key="index"
                      class="cat_nam"
                      :class="sele_cat_nav_index == index ? 'current_cat_nav' : null"
                      @click="onChangeCatNav(index)"
                    >
                      {{ item.categoryName }}
                    </span>
                    <span class="right_empty"></span>
                  </div>
                  <div
                    class="flex_row_center_center right_fixed"
                    :style="{ backgroundColor: cur_top_swiper_bg_color }"
                  >
                    <img
                      class="fixed_icon"
                      src="@/assets/images/m_diy_img/cat_nav_right_fixed.png"
                    />
                    <span class="fixed_con">分类</span>
                  </div>
                </div>
                <!-- 轮播图 - s -->
                <div class="lunbo_box">
                  <Carousel autoplay :dots="false" :afterChange="updateCurTopSwiperBgColor">
                    <template v-if="center_fixed_data.data.length > 0">
                      <template v-for="(item, index) in center_fixed_data.data" :key="index">
                        <div class="lunbo_wrap">
                          <div
                            class="swiper_img_wrap flex_row_center_center"
                            style="width: 373px"
                            :style="{
                              paddingTop: (center_fixed_data.top_margin || 0) + 'px',
                            }"
                          >
                            <img v-if="item.img" style="height: 140px" :src="item.img" />
                            <div
                              v-else
                              class="empty_swiper_img flex_column_center_center"
                              style="width: 355"
                            >
                              <AliSvgIcon
                                width="40.8px"
                                height="33.6px"
                                fillColor="#fff"
                                iconName="icontupian1"
                              />
                              <span class="center_tip flex_row_common">宽710*高280</span>
                            </div>
                            <span
                              class="swiper_bg"
                              :style="{
                                background:
                                  center_fixed_data.swiper_bg_style == 1
                                    ? cur_top_swiper_bg_color
                                    : 'linear-gradient(180deg, ' +
                                      cur_top_swiper_bg_color +
                                      ' 0%, ' +
                                      cur_top_swiper_bg_color +
                                      ' 42%, #FFFFFF 100%)',
                                borderTopLeftRadius: 0,
                                borderTopRightRadius: 0,
                                borderBottomLeftRadius:
                                  center_fixed_data.swiper_bg_style == 1 ? '16px' : 0,
                                borderBottomRightRadius:
                                  center_fixed_data.swiper_bg_style == 1 ? '16px' : 0,
                                height: center_fixed_data.top_margin + 140 + 'px',
                              }"
                            ></span>
                          </div>
                        </div>
                      </template>
                    </template>
                    <template v-else>
                      <div
                        v-for="(item, index) in m_diy_swiper_data"
                        :key="index"
                        class="lunbo_wrap"
                      >
                        <div
                          class="swiper_img_wrap flex_row_center_center"
                          style="width: 373px"
                          :style="{
                            paddingTop: (center_fixed_data.top_margin || 0) + 'px',
                          }"
                        >
                          <img style="height: 140px" :src="item.img" />
                          <span
                            class="swiper_bg"
                            :style="{
                              background:
                                center_fixed_data.swiper_bg_style == 1
                                  ? cur_top_swiper_bg_color
                                  : 'linear-gradient(180deg, ' +
                                    cur_top_swiper_bg_color +
                                    ' 0%, ' +
                                    cur_top_swiper_bg_color +
                                    ' 42%, #FFFFFF 100%)',
                              borderTopLeftRadius: 0,
                              borderTopRightRadius: 0,
                              borderBottomLeftRadius:
                                center_fixed_data.swiper_bg_style == 1 ? '16px' : 0,
                              borderBottomRightRadius:
                                center_fixed_data.swiper_bg_style == 1 ? '16px' : 0,
                              height: center_fixed_data.top_margin + 140 + 'px',
                            }"
                          ></span>
                        </div>
                      </div>
                    </template>
                  </Carousel>
                </div>
                <!-- 轮播图 - e -->
              </div>
              <!-- 固定的顶部组件 - e -->
              <VueDraggable
                v-model="diy_data"
                animation="150"
                group="people"
                @add="dataAdd"
                class="main_data_body_list"
              >
                <div
                  v-for="(item, index) in diy_data"
                  :key="index"
                  class="main_data_body_item"
                  :class="select_data.id !== undefined && select_data.id == item.id ? 'active' : ''"
                  @click="handleClick(item)"
                >
                  <adv_01 v-if="item.type == 'live'" :data="item"></adv_01>
                  <adv_02 v-else-if="item.type == 'svideo'" :data="item"></adv_02>
                  <adv_03 v-else-if="item.type == 'more_tab'" :data="item"></adv_03>
                  <adv_04 v-else-if="item.type == 'fzx'" :data="item"></adv_04>
                  <adv_05 v-else-if="item.type == 'fzkb'" :data="item"></adv_05>
                  <adv_06 v-else-if="item.type == 'gonggao'" :data="item"></adv_06>
                  <adv_07 v-else-if="item.type == 'kefu'" :data="item"></adv_07>
                  <adv_08 v-else-if="item.type == 'fuwenben'" :data="item"></adv_08>
                  <adv_09 v-else-if="item.type == 'dapei'" :data="item"></adv_09>
                  <adv_10 v-else-if="item.type == 'tuijianshangpin'" :data="item"></adv_10>
                  <adv_11 v-else-if="item.type == 'tupianzuhe'" :data="item"></adv_11>
                  <adv_12 v-else-if="item.type == 'nav'" :data="item"></adv_12>
                  <adv_13 v-else-if="item.type == 'lunbo'" :data="item"></adv_13>
                  
                  <CubeDisplay v-else-if="item.type == 'cube'" :data="item"></CubeDisplay>
                  <HotAreaDisplay v-else-if="item.type == 'hot_area'" :data="item"></HotAreaDisplay>
                  <div v-else>{{ item }}</div>
                  <div class="operate_wrap">
                    <div
                      class="mdiy_operate_a flex_row_center_center"
                      style="height: 30px; cursor: pointer"
                      @click="(e) => handleItem(e, item.id, 'up')"
                    >
                      <AliSvgIcon
                        iconName="iconmove-up"
                        width="15px"
                        height="15px"
                        fillColor="#fff"
                      />
                    </div>
                    <div
                      class="mdiy_operate_a flex_row_center_center"
                      style="height: 30px; cursor: pointer"
                      @click="(e) => handleItem(e, item.id, 'down')"
                    >
                      <AliSvgIcon iconName="iconxia1" width="15px" height="15px" fillColor="#fff" />
                    </div>
                    <div
                      v-if="item.is_show"
                      class="mdiy_operate_a flex_row_center_center"
                      style="height: 30px; cursor: pointer"
                      @click="(e) => handleItem(e, item.id, 'is_show')"
                    >
                      <AliSvgIcon
                        iconName="iconkejian"
                        width="16px"
                        height="16px"
                        fillColor="#fff"
                      />
                    </div>
                    <div
                      v-else
                      class="mdiy_operate_a flex_row_center_center"
                      style="height: 30px; cursor: pointer"
                      @click="(e) => handleItem(e, item.id, 'is_show')"
                    >
                      <AliSvgIcon
                        iconName="iconbukejian11"
                        width="16px"
                        height="16px"
                        fillColor="#fff"
                      />
                    </div>
                    <div
                      class="mdiy_operate_a flex_row_center_center"
                      style="height: 30px; cursor: pointer"
                      @click="(e) => handleItem(e, item.id, 'del')"
                    >
                      <AliSvgIcon
                        iconName="iconguanbi3"
                        width="16px"
                        height="16px"
                        fillColor="#fff"
                      />
                    </div>
                  </div>
                </div>
              </VueDraggable>
              <!-- 固定的底部组件 - s -->
              <template v-if="route.query.type == 'home' || route.query.type == 'o2o'">
                <div style="width: 100%; height: 50px"></div>
                <div
                  class="fixed_bottom flex_row_center_center"
                  :class="is_selected_bottom_nav ? 'active' : ''"
                  @click="selectBottomNav"
                  :style="{
                    borderColor: is_selected_bottom_nav ? '#FC701E' : '#fff',
                    borderTopColor: is_selected_bottom_nav ? '#FC701E' : '#EEEEEE',
                  }"
                >
                  <template v-for="(item, index) in bottom_fixed_data.data" :key="index">
                    <div class="flex_column_center_center fixed_bottom_item">
                      <template v-if="index == 0 && item.selectImg.img">
                        <img
                          :class="item.title ? 'fixed_bottom_img' : 'fixed_bottom_img_large'"
                          :src="item.selectImg.img ? item.selectImg.img : nav_default_img"
                        />
                        <span
                          v-if="item.title"
                          class="fixed_bottom_name"
                          :style="{ color: main_style_color }"
                          >{{ item.title }}</span
                        >
                      </template>
                      <template v-else>
                        <img
                          :class="item.title ? 'fixed_bottom_img' : 'fixed_bottom_img_large'"
                          :src="item.autoImg.img ? item.autoImg.img : nav_default_img"
                        />
                        <span v-if="item.title" class="fixed_bottom_name">{{ item.title }}</span>
                      </template>
                    </div>
                  </template>
                </div>
              </template>
              <!-- 固定的底部组件 - e -->
            </div>
          </div>
        </div>
        <div
          class="right_wrap"
          :style="{
            background:
              select_data.type !== undefined || special_select_data.type !== undefined
                ? '#fff'
                : '#f8f8f8',
          }"
        >
          <div class="right_wrap_body">
            <template v-if="select_data.type != undefined">
              <div class="flex_row_start_center r_title">
                <AliSvgIcon
                  width="22px"
                  height="22px"
                  fillColor="#FC701E"
                  :iconName="
                    'icon' + (select_data.icon ? select_data.icon : select_data.admin_icon)
                  "
                />
                <span class="r_title_text"
                  >{{ select_data.name ? select_data.name : select_data.admin_text }}设置</span
                >
              </div>
              <div class="r_item">
                <CubeEdit
                  v-if="select_data.type == 'cube'"
                  :data="select_data"
                  @handleCurSelData="handleCurSelData"
                />
                <HotAreaEditor
                  v-if="select_data.type == 'hot_area'"
                  :data="select_data"
                  :mode="route.query.type"
                  client="mobile"
                  @handleCurSelData="handleCurSelData"
                />
                <diyItemEdit
                  :data="select_data"
                  :diy_type="route.query.type"
                  @handleCurSelData="handleCurSelData"
                  v-else
                />
              </div>
            </template>
            <template v-if="special_select_data.type != undefined">
              <div class="flex_row_start_center r_title">
                <AliSvgIcon
                  width="22px"
                  height="22px"
                  fillColor="#FC701E"
                  iconName="iconziyuan16"
                />
                <span class="r_title_text">{{
                  special_select_data.type == 'top_cat_nav'
                    ? '顶部分类设置'
                    : special_select_data.type == 'bottom_nav'
                    ? '底部导航设置'
                    : '设置'
                }}</span>
              </div>
              <div class="r_item">
                <diyItemEdit
                  :data="special_select_data"
                  :diy_type="route.query.type"
                  @handleCurSelData="handleCurFixedData"
                />
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
    <div class="m_diy_bottom_wrap" :style="{ position: 'fixed', left: getRealWidth + 10 + 'px' }">
      <div class="add_goods_bottom_btn add_goods_bottom_btn_sel" @click="handleSave">保存装修</div>
    </div>
  </PageWrapper>
</template>
<script setup>
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { VueDraggable } from 'vue-draggable-plus';
  import { ref, reactive, onMounted } from 'vue';
  import { Carousel } from 'ant-design-vue';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import { useRoute, useRouter } from 'vue-router';
  import {
    failTip,
    nav_default_img,
    sld_com_empty_arrar_2,
    sld_com_empty_arrar_4,
    sucTip,
    top_nav_cat_swiper1,
    top_nav_cat_swiper2,
    top_nav_cat_swiper3,
  } from '/@/utils/utils';
  import {
    getMobleDecoDetailApi,
    getMobleDecoMenuApi,
    getCategoryList,
    updateMobileDeco,
  } from '@/api/decorate/deco_m';
  import { getSettingListApi, getSettingUpdateApi } from '@/api/common/common';
  import adv_01 from './adv_01.vue';
  import adv_02 from './adv_02.vue';
  import adv_03 from './adv_03.vue';
  import adv_04 from './adv_04.vue';
  import adv_05 from './adv_05.vue';
  import adv_06 from './adv_06.vue';
  import adv_07 from './adv_07.vue';
  import adv_08 from './adv_08.vue';
  import adv_09 from './adv_09.vue';
  import adv_10 from './adv_10.vue';
  import adv_11 from './adv_11.vue';
  import adv_12 from './adv_12.vue';
  import adv_13 from './adv_13.vue';
  
  import diyItemEdit from './diy_item_edit.vue';
  import { CubeDisplay, CubeEdit } from '@/components/Cube';
  import { HotAreaEditor, HotAreaDisplay } from '@/components/HotArea';
  import { pageClose } from '/@/utils/utils';
  import { useMultipleTabStore } from '/@/store/modules/multipleTab';

  const { getRealWidth } = useMenuSetting();
  const tabStore = useMultipleTabStore();
  const router = useRouter();
  const route = useRoute();
  const title = ref('');
  const index = ref(0); //添加的模块数量
  const click_type = ref(false);
  const detail = reactive({
    data: [], //数据
    menu: [], //菜单
    category: [], //顶部分类
  }); //装修详情
  const diy_data = ref([]); //中间装修数据
  const items = getItems(10);
  const sele_cat_nav_index = ref(0); //选中的分类导航index，默认第一个
  const select_data = ref({}); //选中的当前数据
  const m_diy_swiper_data = [
    {
      img: top_nav_cat_swiper1,
      bg_color: '#E81832',
    },
    {
      img: top_nav_cat_swiper2,
      bg_color: '#1C6BFC',
    },
    {
      img: top_nav_cat_swiper3,
      bg_color: '#11986B',
    },
  ]; //手机装修顶部分类导航数据
  const cur_top_swiper_bg_color = ref('#FFFFFF'); //顶部分类主要颜色
  const show_top_cat_nav = ref(
    route.query.type == 'home' || route.query.type == 'o2o' ? true : false,
  ); //是否显示顶部分类导航，默认显示
  const is_selected_top_cat_nav = ref(false); //是否选中顶部分类导航，默认不选中
  let center_fixed_data = ref({
    type: 'top_cat_nav',
    swiper_bg_style: 1, //轮播背景显示风格，1为纯色弧度 2为渐变
    data: [], //轮播图
    color: '#fff',
    top_margin: 0,
    bottom_margin: 0,
    width: undefined,
    height: undefined,
  }); //中间部分需要显示的不可拖动的数据
  const special_select_data = ref({}); //选中的不可拖动的数据
  const is_selected_bottom_nav = ref(false); //是否选中顶部tabbar，默认不选中
  let bottom_fixed_data = ref({
    type: 'bottom_nav',
    data: [
      //编辑状态 editState 0:不可编辑 1:可编辑 2:仅可编辑图片
      {
        title: '首页',
        autoImg: {},
        selectImg: {},
        info: '',
        url: '',
        url_type: 'home',
        editState: 1,
      },
      {
        title: '分类',
        autoImg: {},
        selectImg: {},
        info: '',
        url: '',
        url_type: 'all_category',
        editState: 1,
      },
      {
        title: '',
        autoImg: {},
        selectImg: {},
        info: '',
        url: '',
        url_type: 'discover',
        editState: 1,
      },
      {
        title: '购物车',
        autoImg: {},
        selectImg: {},
        info: '',
        url: '',
        url_type: 'cart',
        editState: 1,
      },
      {
        title: '我的',
        autoImg: {},
        selectImg: {},
        info: '',
        url: '',
        url_type: 'user',
        editState: 1,
      },
    ],
  });
  const main_style_color = ref('#F41410'); //商城主色
  const slidIndex = ref(0); //当前轮播图下标

  onMounted(() => {
    if (route.query.id) {
      get_detail();
    }
    get_menu();
    get_category();
    get_m_style();
  });

  // fake data generator
  function getItems(count, offset = 0) {
    Array.from({ length: count }, (v, k) => k).map((k) => ({
      id: `item-${k + offset}`,
      content: `item ${k + offset}`,
    }));
  }

  //获取装修详情
  const get_detail = async () => {
    cur_top_swiper_bg_color.value = m_diy_swiper_data[0].bg_color;
    const res = await getMobleDecoDetailApi({ decoId: route.query.id });
    if (res.state == 200) {
      //空处理
      if (res.data.data) {
        let tmp_data = JSON.parse(res.data.data.replace(/&quot;/g, '"'));
        let top_cat_nav_data = tmp_data.filter((item) => item.type == 'top_cat_nav');
        if (top_cat_nav_data != undefined && top_cat_nav_data.length > 0) {
          //初始化顶部分类导航数据
          center_fixed_data.value = top_cat_nav_data[0];
          if (
            top_cat_nav_data[0].data &&
            top_cat_nav_data[0].data.length > 0 &&
            top_cat_nav_data[0].data[0].bg_color
          ) {
            cur_top_swiper_bg_color.value = top_cat_nav_data[0].data[0].bg_color;
          }
          //过滤掉顶部分类导航
          tmp_data = tmp_data.filter((item) => item.type != 'top_cat_nav');
          show_top_cat_nav.value = true;
        } else {
          show_top_cat_nav.value = false; //不显示顶部分类
        }
        for (let i in tmp_data) {
          if(tmp_data[i].type == 'tuijianshangpin'){
            if(tmp_data[i].data.info&&Array.isArray(tmp_data[i].data.info)){
              let key = []
              let keyRows = []
              tmp_data[i].data.info.forEach(item=>{
                if(!item.state||(item.state&&item.state==3)){
                  keyRows.push(item)
                  key.push(item.goodsId)
                }
              })
              tmp_data[i].data.info = keyRows
              tmp_data[i].data.ids = key
            }
          }else if(tmp_data[i].type == 'more_tab'){
            tmp_data[i].data.forEach(item=>{
              if(item.info&&item.info.length>0&&item.data_type=='goods'){
                let key = []
                let keyRows = []
                item.info.forEach(it=>{
                  if(!it.state||(it.state&&it.state==3)){
                    keyRows.push(it)
                    key.push(it.goodsId)
                  }
                })
                item.info = keyRows
                item.ids = key
              }
            })
          }
          index.value++;
          tmp_data[i].id = index.value;
        }
        diy_data.value = tmp_data;
      } else {
        diy_data.value = [];
      }
      detail.data = res.data.data ? JSON.parse(res.data.data) : [];
      title.value = res.data.name;
    } else {
      failTip(res.msg);
    }
  };

  //获取开启的装修菜单
  const get_menu = async () => {
    let type = route.query.type
    if(route.query.type&&route.query.type=='o2oTopic'){
      type = 'o2o'
    }
    const res = await getMobleDecoMenuApi({ isEnable: 1, apply: type });
    if (res.state == 200 && res.data) {
      let top_cat = res.data.filter((item) => item.type == 'top_cat');
      if (top_cat.length > 0) {
        res.data = res.data.filter((item) => item.type != 'top_cat');
        res.data.unshift(top_cat[0]);
      } else if (route.query.type == 'home' || route.query.type == 'o2o') {
        res.data.unshift({
          isUse: 1,
          icon: 'ziyuan16',
          name: '顶部分类',
          type: 'top_cat',
        });
      }
      detail.menu = res.data;
    } else {
      failTip(res.msg);
    }
  };

  //获取顶部分类数据
  const get_category = async () => {
    const res = await getCategoryList({ categoryId: 0, pageSize: 10000, isSort: true });
    if (res.state == 200) {
      detail.category = res.data.list ? res.data.list : [];
    } else {
      failTip(res.msg);
    }
  };

  //获取风格配置
  const get_m_style = async () => {
    const res = await getSettingListApi({ str: 'mobile_mall_style,bottom_bar_change_color' });
    if (res.state == 200) {
      main_style_color.value = !res.data[0].value
        ? '#C58941'
        : JSON.parse(res.data[0].value).mainColor;
      bottom_fixed_data.value = !res.data[1].value
        ? bottom_fixed_data.value
        : JSON.parse(res.data[1].value);
    } else {
      failTip(res.msg);
    }
  };

  //顶部分类导航切换事件
  function onChangeCatNav(index) {
    sele_cat_nav_index.value = index;
  }

  //是否显示顶部分类导航
  function setTopCatNav() {
    show_top_cat_nav.value = !show_top_cat_nav.value;
  }

  //选中顶部分类导航
  function selectTopCatNav() {
    special_select_data.value = JSON.parse(JSON.stringify(center_fixed_data.value));
    is_selected_top_cat_nav.value = true;
    select_data.value = {};
  }

  //选中底部tabbar
  function selectBottomNav() {
    special_select_data.value = JSON.parse(JSON.stringify(bottom_fixed_data.value));
    is_selected_bottom_nav.value = true;
    select_data.value = {};
  }

  //更新顶部分类导航背景色
  function updateCurTopSwiperBgColor(index) {
    if (center_fixed_data.value.data.length > 0 && index < center_fixed_data.value.data.length) {
      cur_top_swiper_bg_color.value = center_fixed_data.value.data[index].bg_color;
    } else {
      cur_top_swiper_bg_color.value = m_diy_swiper_data[index].bg_color;
    }
    slidIndex.value = index;
  }

  //选择组件
  function handleClick(item) {
    special_select_data.value = {};
    select_data.value = {};
    is_selected_top_cat_nav.value = false;
    is_selected_bottom_nav.value = false;
    setTimeout(() => {
      select_data.value = item;
    });
  }

  //增加组件
  function dataAdd(e) {
    let menu_item = detail.menu[e.oldDraggableIndex];
    let diy_item = diy_data.value[e.newDraggableIndex];
    diy_item.is_show = true;
    if (menu_item.type == 'live') {
      //直播组件
      diy_item.title = '商联达直播';
      diy_item.top_margin = 10;
      diy_item.bottom_margin = 0;
      diy_item.color = '#fff';
      diy_item.view_more_url = '';
      diy_item.show_style = 'one';
      diy_item.border_radius = 8;
      diy_item.data = {
        ids: [],
        info: [],
      };
    } else if (menu_item.type == 'svideo') {
      //短视频组件
      diy_item.title = '商联达短视频';
      diy_item.top_margin = 10;
      diy_item.bottom_margin = 0;
      diy_item.color = '#fff';
      diy_item.view_more_url = '';
      diy_item.show_style = 'one';
      diy_item.border_radius = 8;
      diy_item.data = {
        ids: [],
        info: [],
      };
    } else if (menu_item.type == 'more_tab') {
      //TAB切换组件
      diy_item.top_margin = 10;
      diy_item.bottom_margin = 0;
      diy_item.color = '#fff';
      diy_item.border_radius = 8;
      diy_item.nav_current = 0;
      diy_item.data = [];
    } else if (menu_item.type == 'fzx') {
      //辅助线组件
      diy_item.val = 'solid';
      diy_item.top_margin = 10;
      diy_item.bottom_margin = 10;
      diy_item.bg_color = '#fff';
      diy_item.color = '#e3e5e9';
    } else if (menu_item.type == 'fzkb') {
      //辅助空白组件
      diy_item.top_margin = 0;
      diy_item.bottom_margin = 0;
      diy_item.color = '#fff';
      diy_item.text = 30;
    } else if (menu_item.type == 'gonggao') {
      //公告组件
      diy_item.top_margin = 10;
      diy_item.bottom_margin = 0;
      diy_item.color = '#fff';
      diy_item.show_style = 'one'; //公告展示风格
      diy_item.text = '';
      diy_item.url = '';
      diy_item.url_type = '';
      diy_item.info = '';
    } else if (menu_item.type == 'kefu') {
      //客服组件
      diy_item.top_margin = 10;
      diy_item.bottom_margin = 0;
      diy_item.color = '#fff';
      diy_item.tel = 15288888888;
      diy_item.text = '客服电话：';
    } else if (menu_item.type == 'fuwenben') {
      //富文本组件
      diy_item.top_margin = 10;
      diy_item.bottom_margin = 0;
      diy_item.color = '#fff';
      diy_item.text = '';
    } else if (menu_item.type == 'nav') {
      //导航组件
      diy_item.page_set = 'single';
      diy_item.page_num = 5;
      diy_item.top_margin = 10;
      diy_item.bottom_margin = 0;
      diy_item.color = '#fff';
      diy_item.data = [];
      diy_item.icon_set = 'up';
      diy_item.slide = 35;
      diy_item.style_set = 'nav';
      sld_com_empty_arrar_4.map((item) => {
        diy_item.data.push({
          img: '',
          img_path: '',
          name: '',
          url: '',
          url_type: '',
          info: '',
        });
      });
    } else if (menu_item.type == 'tupianzuhe') {
      //图片组件
      diy_item.top_margin = 10;
      diy_item.bottom_margin = 0;
      diy_item.color = '#fff';
      diy_item.data = [];
      for (let i in sld_com_empty_arrar_4) {
        let temp = [];
        for (let j in sld_com_empty_arrar_2) {
          temp.push({
            img: '',
            img_path: '',
            imgPath: '',
            name: '',
            url: '',
            url_type: '',
            info: '',
          });
        }
        diy_item.data.push({
          main_title: '',
          sub_title: '',
          url: '',
          url_type: '',
          info: '',
          img: temp,
        });
      } //数据
      diy_item.sele_style = 8;
    } else if (menu_item.type == 'dapei') {
      //搭配组件
      diy_item.top_margin = 10;
      diy_item.bottom_margin = 0;
      diy_item.color = '#fff';
      diy_item.dapei_desc = ''; //搭配图片描述
      diy_item.dapei_img = ''; //搭配图片
      diy_item.width = ''; //搭配图片宽度
      diy_item.height = ''; //搭配图片高度
      diy_item.dapei_title = ''; //搭配标题
      diy_item.img_path = ''; //图片相对路径
      diy_item.imgPath = ''; //图片相对路径
      diy_item.data = {
        ids: [],
        info: [],
      }; //商品数据
    } else if (menu_item.type == 'lunbo') {
      //轮播图组件
      diy_item.top_margin = 10;
      diy_item.bottom_margin = 0;
      diy_item.color = '#fff';
      diy_item.width = 750;
      diy_item.height = 285.5;
      diy_item.data = [];
    } else if (menu_item.type == 'tuijianshangpin') {
      //拖动的类型为推荐商品
      diy_item.top_margin = 10;
      diy_item.bottom_margin = 0;
      diy_item.color = '#fff';
      diy_item.isshow_sales = 0; //是否展示销量
      diy_item.cart_icon_type = 1; //购物车图标样式
      diy_item.show_style = 'small'; //展示类型：big 大图 small 一行两个 list 列表 一行一个
      diy_item.border_radius = 10; //商品圆角 0表示直角
      diy_item.border_style = 'card-shadow'; //商品样式  border_none无边白底  card-shadow卡片投影  border_eee描边白底 border_none_grey_bg 无边灰底
      diy_item.page_margin = 10; //距离页面两边的距离
      diy_item.goods_margin = 10; //商品之间的距离
      diy_item.text_align = 'flex-start'; //文本对齐方式，flex-start 左对齐 center 居中对齐
      diy_item.text_style = 'normal'; //文本样式，normal常规体 bold 加粗体
      diy_item.data = {
        ids: [],
        info: [],
      }; //商品数据
    } else if (menu_item.type == 'cube') {
      diy_item.top_margin = 10;
      diy_item.bottom_margin = 0;
      diy_item.color = '#fff';
      diy_item.data = [];
      diy_item.size = 4;
    } else if (menu_item.type == 'hot_area') {
      diy_item.area_list = [];
      diy_item.style = {
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
        borderRadius: { top: 0, bottom: 0 },
        bgColor: '',
      };
      diy_item.img_info = {};
     
    }
    index.value++;
    diy_item.id = index.value;
  }

  //保存装修
  const handleSave = async () => {
    if (click_type.value) return;
    let params = {};
    params.decoId = route.query.id;
    let tar_data = [...diy_data.value];
    //顶部分类导航启用的话需要合并一下数据
    if (show_top_cat_nav.value) {
      tar_data.unshift({ ...center_fixed_data.value });
    }
    params.data = JSON.stringify(tar_data);
    click_type.value = true;
    let res = await updateMobileDeco(params);
    if (res.state == 200) {
      if (route.query.type == 'home' || route.query.type == 'o2o') {
        //首页装修-底部tabbar需要合并数据
        let respon = await getSettingUpdateApi({
          bottom_bar_change_color: JSON.stringify(bottom_fixed_data.value),
        });
        if (respon.state == 200) {
          sucTip(respon.msg);
          //2s之后返回上一页
          setTimeout(() => {
            click_type.value = false;
            goBack();
          }, 2000);
        } else {
          click_type.value = false;
          failTip(respon.msg);
        }
      } else {
        sucTip(res.msg);
        // 2s之后返回上一页
        setTimeout(() => {
          click_type.value = false;
          goBack();
        }, 2000);
      }
    } else {
      click_type.value = false;
      failTip(res.msg);
    }
  };

  const goBack = () => {
    if (window.history && window.history.length == 1) {
      pageClose();
    } else {
      const { fullPath } = route;
      tabStore.closeTabByKey(fullPath, router);
      router.back();
    }
  };

  //编辑组件更新数据
  function handleCurSelData(data) {
    select_data.value = { ...select_data.value, ...data };
    for (let i in diy_data.value) {
      if (diy_data.value[i].id == select_data.value.id) {
        diy_data.value[i] = select_data.value;
        break;
      }
    }
  }

  //编辑组件更新不可拖动的数据
  function handleCurFixedData(data) {
    if (data.type == 'top_cat_nav') {
      if (
        data.data &&
        data.data.length > 0 &&
        data.data.length > slidIndex.value &&
        data.data[slidIndex.value].bg_color &&
        data.data[slidIndex.value].bg_color != cur_top_swiper_bg_color.value
      ) {
        cur_top_swiper_bg_color.value = data.data[slidIndex.value].bg_color;
      } else if (
        data.data &&
        data.data.length == 0 &&
        m_diy_swiper_data.length > slidIndex.value &&
        m_diy_swiper_data[slidIndex.value].bg_color &&
        m_diy_swiper_data[slidIndex.value].bg_color != cur_top_swiper_bg_color.value
      ) {
        cur_top_swiper_bg_color.value = m_diy_swiper_data[slidIndex.value].bg_color;
      }
      let center_fixed_data_val = { ...special_select_data.value, ...data };
      center_fixed_data.value.color = center_fixed_data_val.color;
      center_fixed_data.value.top_margin = center_fixed_data_val.top_margin;
      center_fixed_data.value.bottom_margin = center_fixed_data_val.bottom_margin;
      center_fixed_data.value.data = center_fixed_data_val.data;
      center_fixed_data.value.swiper_bg_style = center_fixed_data_val.swiper_bg_style;
      center_fixed_data.value.width = center_fixed_data_val.width
        ? center_fixed_data_val.width
        : undefined;
      center_fixed_data.value.height = center_fixed_data_val.height
        ? center_fixed_data_val.height
        : undefined;
      special_select_data.value = JSON.parse(JSON.stringify(center_fixed_data_val));
    } else if (data.type == 'bottom_nav') {
      let center_fixed_data_val = { ...special_select_data.value, ...data };
      bottom_fixed_data.value = center_fixed_data_val;
      special_select_data.value = JSON.parse(JSON.stringify(center_fixed_data_val));
    }
  }

  const handleItem = (e, index, type) => {
    //装修模块的操作
    e.stopPropagation();
    e.preventDefault();
    if (type == 'del') {
      diy_data.value = diy_data.value.filter((item) => item.id != index); //删除操作
      select_data.value = {};
    } else {
      for (let i = 0; i < diy_data.value.length; i++) {
        if (diy_data.value[i].id == index) {
          let tmp = {};
          if (type == 'down') {
            //向下移动,最后一个不处理
            if (i < diy_data.value.length - 1) {
              tmp = { ...diy_data.value[i] };
              diy_data.value[i] = { ...diy_data.value[i + 1] };
              diy_data.value[i + 1] = { ...tmp };
            }
          } else if (type == 'up') {
            //向上移动，第一个不处理
            if (i != 0) {
              tmp = { ...diy_data.value[i] };
              diy_data.value[i] = { ...diy_data.value[i - 1] };
              diy_data.value[i - 1] = { ...tmp };
            }
          } else if (type == 'is_show') {
            diy_data.value[i].is_show = !diy_data.value[i].is_show; //是否显示
          } else if (type == 'edit') {
            select_data.value = diy_data.value[i]; //编辑
          }
          break;
        }
      }
    }
  };
</script>
<style lang="less" scoped>
  .mobile_diy_page {
    .mobile_diy_page_main {
      .left_menu {
        position: relative;
        flex-shrink: 0;
        width: 220px;
        height: 100%;
        padding: 15px;

        .left_menu_body {
          position: absolute;
          flex-wrap: wrap;
          width: 100%;
          height: 100%;
          padding-bottom: 65px;
          overflow: hidden auto;

          .left_menu_item {
            position: relative;
            width: 90px;
            height: 90px;
            margin-right: 10px;
            margin-bottom: 10px;
            border-radius: 3px;
            background: #fff5f0;
            cursor: pointer;

            .left_menu_item_icon {
              width: 35px;
              height: 35px;
              margin-top: 7px;
              margin-bottom: 3px;
            }

            .left_menu_item_active {
              position: absolute;
              inset: 0;
              background: rgb(0 0 0 / 70%);
              color: #fff;
              font-size: 14px;
            }

            &:nth-child(2n) {
              margin-right: 0;
            }
          }
        }
      }

      .main_data {
        position: relative;
        flex: 1;
        height: 100%;

        .main_data_body {
          position: absolute;
          width: 100%;
          height: 100%;
          padding-top: 20px;
          padding-bottom: 65px;
          overflow: hidden auto;

          .main_data_body_main {
            position: relative;
            width: 401px;
            padding: 13px;
            background: #f8f8f8;

            .fixed_top_img {
              margin-bottom: -1px;
            }

            .main_data_body_list {
              width: 375px;
              height: 100%;
              min-height: 600px;
              border: 1px solid #eee;
              background: #fff;

              .main_data_body_item {
                position: relative;

                &:hover {
                  border-color: @primary-color;

                  .operate_wrap {
                    display: flex !important;
                  }
                }

                &.active {
                  border: 1px solid @primary-color;
                }

                .operate_wrap {
                  display: none;
                  position: absolute;
                  z-index: 99;
                  right: 0;
                  bottom: 0;
                  width: 135px;
                  height: 30px;
                  border-radius: 12px 0 0;
                  background: #fc701e;
                }

                .mdiy_operate_a {
                  margin-left: 15px;
                }
              }
            }

            .fixed_top {
              width: 100%;
              overflow: hidden;
              border-width: 1px;
              border-style: solid;

              .cat_nav {
                position: absolute;
                top: 64px;
                width: 374px;
                height: 48px;
                color: #fff;
                font-size: 14px;

                .cat_nav_left {
                  width: 372px;
                  height: 48px;
                  overflow-x: auto;
                  white-space: nowrap;

                  .right_empty {
                    display: inline-block;
                    flex-shrink: 0;
                    width: 58px;
                    height: 1px;
                  }
                }

                .cat_nam {
                  display: inline-block;
                  position: relative;
                  flex-shrink: 0;
                  margin: -6px 13px 0;
                }

                .current_cat_nav::after {
                  content: ' ';
                  position: absolute;
                  z-index: 2;
                  bottom: -7px;
                  left: 50%;
                  width: 16px;
                  height: 10px;
                  margin-left: -8px;
                  border: 2px solid #fff;
                  border-top: none;
                  border-right: none;
                  border-left: none;
                  border-radius: 100%;
                }
              }

              .right_fixed {
                position: absolute;
                z-index: 3;
                top: 0;
                right: 0;
                width: 58px;
                height: 36px;
                margin: 3px 0;
                box-shadow: -6px 0 4px -4px rgb(0 0 0 / 40%);

                .fixed_icon {
                  width: 11px;
                  height: 9px;
                  margin-right: 4px;
                }

                .fixed_con {
                  color: #fff;
                  font-size: 14px;
                }
              }

              .lunbo_box {
                width: 375px;

                .lunbo_wrap {
                  display: flex !important;
                  position: relative;
                  width: 100%;
                  margin: 0 auto;

                  img {
                    width: 355px;
                    height: 140px;
                    border-radius: 8px;
                  }

                  .swiper_img_wrap {
                    position: relative;
                    width: 373px;

                    .swiper_bg {
                      position: absolute;
                      z-index: -1;
                      top: -4rem;
                      right: 0;
                      left: 0;
                      height: 10rem;
                      border-radius: 30%;
                    }

                    .empty_swiper_img {
                      width: 355px;
                      height: 140px;
                      border-radius: 8px;
                      background: #ecf5ff;
                    }
                  }
                }
              }

              &:hover {
                border: 1px dashed @primary-color !important;
              }

              &.active {
                border: 1px solid @primary-color !important;
              }
            }

            .fixed_bottom {
              position: absolute;
              z-index: 2;
              bottom: 13px;
              left: 50%;
              width: 374px;
              height: 50px;
              margin-left: -187px;
              border: 1px solid;
              background: #fff;

              .fixed_bottom_item {
                flex: 1;
                margin-top: 2px;
                transform: scale(0.9);

                .fixed_bottom_img {
                  width: 25px;
                  height: 25px;
                }

                .fixed_bottom_img_large {
                  width: 34px;
                  height: 34px;
                }

                .fixed_bottom_name {
                  margin-top: 2px;
                  color: #333;
                  font-family: 'Microsoft YaHei';
                  font-size: 12px;
                  font-weight: 400;
                }
              }

              &:hover {
                border: 1px dashed @primary-color !important;
              }

              &.active {
                border: 1px solid @primary-color !important;
              }
            }
          }
        }
      }

      .right_wrap {
        position: relative;
        flex-shrink: 0;
        width: 400px;
        height: 100%;
        background: #f8f8f8;

        .right_wrap_body {
          position: absolute;
          width: 100%;
          height: 100%;

          .r_title {
            height: 45px;
            padding: 10px 20px;
            background: #f8f8f8;

            .r_title_left_border {
              display: inline-block;
              width: 4px;
              height: 14px;
              background: #397ed3;
            }

            .r_title_text {
              display: inline-block;
              margin-left: 10px;
              color: #fc701e;
              font-size: 16px;
              font-weight: bold;
            }
          }

          .r_item {
            position: relative;
            width: 400px;
            height: calc(100% - 66px);
            overflow: hidden auto;
          }
        }
      }
    }
  }
</style>
<style>
  .slodon-layout-content > div,
  .slodon-layout-content > div > .bg-white,
  .mobile_diy_page,
  .mobile_diy_page > .mobile_diy_page_main {
    height: 98.5%;
  }

  /* 滚动条样式 */
  .fixed_top *::-webkit-scrollbar {
    width: 5px;
    height: 7px;
  }

  .fixed_top *::-webkit-scrollbar-track {
    border-radius: 2px;
    background: transparent;
  }

  .fixed_top *::-webkit-scrollbar-thumb {
    border-radius: 5px;
    background: transparent;
  }

  .fixed_top *::-webkit-scrollbar-corner {
    background: transparent;
  }

  .fixed_top *::-webkit-scrollbar-thumb:hover {
    background: rgb(0 0 0 / 10%);
  }
</style>
