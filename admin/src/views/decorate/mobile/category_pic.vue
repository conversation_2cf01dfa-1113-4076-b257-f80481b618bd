<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <SldComHeader :type="1" title="分类图片管理" />
      <SldComTips :tipData="['当分类图片有更新的时候，需点击`更新分类缓存`按钮才能生效']" />
      <div style="width: 100%; height: 13px"></div>
      <BasicTable @register="standardTable" @expand="expandedRowsChange" style="padding: 0">
        <template #tableTitle>
          <div class="toolbar flex flex-row">
            <div class="toolbar_btn" @click="operateCat('', 'cache')">
              <ReloadOutlined style="color: rgb(255 89 8)" />
              <span>更新分类缓存</span>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key == 'action'">
            <div v-if="record.grade == 1" class="flex flex-row items-center justify-center">
              <span
                class="common_page_edit cursor-pointer hover:text-[#FF6A12]"
                @click="handleClickAdv(record)"
                >设置分类广告</span
              >
            </div>
            <span v-else>--</span>
          </template>
        </template>
      </BasicTable>
    </div>

    <Modal
      :style="{ textAlign: 'center', width: 300 }"
      :visible="show_preview_modal"
      :footer="null"
      @cancel="show_preview_modal = false"
    >
      <img alt="pic" :style="{ maxWidth: '100%', maxHeight: '100%' }" :src="currentImageUrl" />
    </Modal>

    <SldMoreImgModal
      :width="1000"
      :title="modalTitle"
      :modalTip="modal_tip"
      :content="modalContent"
      client="mobile"
      ref="sldMoreImgModalRef"
      @confirm="moreImgConfirm"
    ></SldMoreImgModal>

    <SldMaterialImgs
      ref="sldMaterialImgsRef"
      :maxUploadNum="1"
      @confirm-material="confirmMaterial"
    ></SldMaterialImgs>
  </div>
</template>

<script setup>
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import SldComTips from '/@/components/SldComHeader/tips.vue';
  import { BasicTable, useTable } from '@/components/Table';
  import { getCategoryList, cateEdit, cateInit } from '@/api/decorate/deco_m';
  import { reactive, ref, unref } from 'vue';
  import { Modal } from 'ant-design-vue';
  import { CategoryImageRender } from './category_pic';
  import SldMoreImgModal from '@/components/SldMoreImgModal/index.vue';
  import SldMaterialImgs from '@/components/SldMaterialFiles/sldMaterialImgs.vue';
  import { ReloadOutlined } from '@ant-design/icons-vue';
  import { sucTip, failTip } from '@/utils/utils';

  const modalVisibleAdv = ref(false);
  const show_preview_modal = ref(false);
  const currentImageUrl = ref('');
  const currentCategory = ref({});
  const sldMoreImgModalRef = ref();
  const sldMaterialImgsRef = ref();

  //操作上传素材的分类存储变量
  const memoryCategory = {
    grade: 1, //当前为几级
    caId_1st: '', //一级分类
    caId_2nd: '', //二级分类
    caId_3rd: '', //三级分类
    clearAll() {
      this.caId_1st = '';
      this.caId_2nd = '';
      this.caId_3rd = '';
    },
  };

  const modal_tip = [
    '最多上传3张图片,每张图片不可以超过1M',
    '请严格根据提示要求上传规定尺寸的广告图片',
    '编辑项中的“操作”指点击该内容所产生的链接地址，可通过下拉选项选择不同的方式',
  ];

  const modalTitle = ref('编辑分类广告');

  const modalContent = reactive({
    width: 520,
    height: 210,
    admin_show_width: 260,
    admin_show_height: 105,
    data: [],
  });

  const [standardTable, { reload, setLoading, getDataSource, setTableData }] = useTable({
    api: getCategoryList,
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    rowKey: 'categoryId',
    columns: [
      {
        title: '分类名称', //分类名称
        align: 'left',
        dataIndex: 'categoryName',
        width: 200,
      },
      {
        title: '排序', //排序
        dataIndex: 'sort',
        align: 'center',
        width: 100,
      },
      {
        title: '二级分类图片', //二级分类图片
        dataIndex: 'categoryImageUrl',
        align: 'center',
        width: 100,
        customRender: ({ record, text }) =>
          CategoryImageRender(record, 2, { delImage, uploadImgPre, openMaterial }),
      },
      {
        title: '三级分类图片', //二级分类图片
        dataIndex: 'categoryImageUrl',
        align: 'center',
        width: 100,
        customRender: ({ record, text }) =>
          CategoryImageRender(record, 3, { delImage, uploadImgPre, openMaterial }),
      },
      {
        title: '创建时间', //创建时间
        dataIndex: 'createTime',
        align: 'center',
        width: 150,
      },
      {
        title: '更新时间', //更新时间
        dataIndex: 'updateTime',
        align: 'center',
        width: 150,
      },
    ],
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
      align: 'center',
    },
    indexColumnProps: {
      width: 80,
    },
    pagination: {
      pageSize: 1000,
    },
    searchInfo: {
      categoryId: 0,
    },
  });

  const uploadImgPre = (info) => {
    currentImageUrl.value = info.url || info.thumbUrl;
    show_preview_modal.value = true;
  };

  //删除图片
  const delImage = (info, record) => {
    let edit_data = {},
      target = {};
    const dataSource = getDataSource();

    if (record.grade == 2) {
      dataSource.forEach((cate1) => {
        let tmp = cate1.children?.find((cate2) => cate2.categoryId == record.categoryId);
        if (tmp) target = tmp;
      });
    } else {
      dataSource.forEach((cate1) => {
        cate1.children?.forEach((cate2) => {
          let tmp = cate2.children?.find((cate3) => cate3.categoryId == record.categoryId);
          if (tmp) target = tmp;
        });
      });
    }

    if (info.file.status == 'removed') {
      edit_data.categoryId = record.categoryId;
      edit_data.categoryImage = '';
      target.categoryImage = '';
      target.categoryImageUrl = '';
      operateCat(edit_data, 'edit');
      setTableData(dataSource);
    }
  };

  //分类操作事件 type edit:编辑 cache:更新分类缓存 source:来源，默认为空，如果是设置分类广告，则为adv，需要更新数据
  const operateCat = async (id, type, source = '') => {
    let params = {};
    let handleApi = (arg) => ({});
    if (type == 'edit') {
      handleApi = cateEdit;
      params = id;
    } else if (type == 'cache') {
      handleApi = cateInit;
    }
    setLoading(true);
    const res = await handleApi(params);
    if (res?.state == 200) {
      sucTip(res.msg);
      if (type == 'cache') {
        //更新缓存需要重新获取数据
        reload();
      } else {
        if (source == 'adv') {
          const dataSource = getDataSource();
          let temp = dataSource.findIndex((item) => item.categoryId == id.categoryId);
          dataSource[temp].mobileImage = id.mobileImage;
          setTableData(dataSource);
          sldMoreImgModalRef.value.setModal(false);
        }
      }
      //更新数据
      modalVisibleAdv.value = false;
    } else {
      failTip(res.msg);
    }
    setLoading(false);
  };

  const expandedRowsChange = async (expanded, record) => {
    if (expanded) {
      const res = await getCategoryList({ categoryId: record.categoryId, pageSize: 1000 });
      if (res?.state == 200) {
        const data = getDataSource();
        const result = deepSearch(data, record.categoryId, res.data.list);
        setTableData(result);
      }
    }
  };

  const deepSearch = (data, categoryId, targetChildren) => {
    for (let ins in data) {
      if (data[ins].categoryId == categoryId) {
        data[ins].children = targetChildren;
        data[ins].dataRequested = true;
      } else if (data[ins].children && data[ins].children.length) {
        deepSearch(data[ins].children, categoryId, targetChildren);
      }
    }
    return data;
  };

  const handleClickAdv = (val) => {
    if (val.mobileImage) {
      modalTitle.value = '编辑分类广告';
      let adv_data = JSON.parse(val.mobileImage.replace(/&quot;/g, '"'));
      modalContent.data = adv_data;
    } else {
      modalTitle.value = '设置分类广告';
      modalContent.data = [];
    }
    currentCategory.value = val;
    sldMoreImgModalRef.value.setModal(true);
  };

  const moreImgConfirm = (dataSource) => {
    operateCat(
      {
        categoryId: unref(currentCategory).categoryId,
        mobileImage: JSON.stringify(dataSource.parent_data),
      },
      'edit',
      'adv',
    );
  };

  const openMaterial = (item, grade) => {
    memoryCategory.clearAll();
    memoryCategory.grade = grade;
    if (grade == 2) {
      memoryCategory.caId_1st = item.pid;
      memoryCategory.caId_2nd = item.categoryId;
    } else if (grade == 3 && item.path) {
      memoryCategory.caId_1st = item.path.split('/')[1];
      memoryCategory.caId_2nd = item.pid;
      memoryCategory.caId_3rd = item.categoryId;
    } else {
      return;
    }
    unref(sldMaterialImgsRef).setMaterialModal(true);
  };

  const confirmMaterial = (val) => {
    const dataSource = getDataSource();
    let edit_data = {};
    let fileList = [];
    if (val.data.length) {
      fileList = val.data.map((v) => ({
        uid: v.bindId,
        name: v.fileUrl,
        status: 'done',
        url: v.fileUrl,
        path: v.filePath,
      }));
    }
    let { caId_2nd, caId_3rd, grade } = memoryCategory;
    if (grade == 2) {
      dataSource.map((cate1) => {
        let target = cate1.children?.find((cate2) => cate2.categoryId == caId_2nd);
        if (target && fileList.length) {
          let [{ url, path }] = fileList;
          target.categoryImageUrl = url;
          target.categoryImage = path;
          edit_data.categoryImage = path;
          edit_data.categoryId = caId_2nd;
        }
      });
    } else {
      dataSource.map((cate1) => {
        cate1.children &&
          cate1.children.map((cate2) => {
            let target = cate2.children?.find((cate3) => cate3.categoryId == caId_3rd);
            if (target && fileList.length) {
              let [{ url, path }] = fileList;
              target.categoryImageUrl = url;
              target.categoryImage = path;
              edit_data.categoryImage = path;
              edit_data.categoryId = caId_3rd;
            }
          });
      });
    }
    operateCat(edit_data, 'edit');
    unref(sldMaterialImgsRef).setMaterialModal(false);
  };
</script>

<style lang="less">
  .mcat_upload_wrap {
    .ant-upload-list-picture-card .ant-upload-list-item-info::before {
      left: 0;
    }

    .ant-upload-list-picture-card-container {
      width: 75px !important;
      height: 75px !important;
    }

    .ant-upload.ant-upload-select-picture-card {
      width: 75px !important;
      height: 75px !important;
      margin-right: 0 !important;
      margin-bottom: 2px !important;
    }

    .ant-upload {
      font-size: 12px !important;
    }

    .ant-upload-list-picture-card .ant-upload-list-item {
      width: 75px !important;
      height: 75px !important;
      margin-right: 0 !important;
      margin-bottom: 2px !important;
    }
  }

  .mcat_upload_wrap > span:nth-child(1) {
    width: 75px;
    height: 77px;
    margin-bottom: 2px;
    overflow: hidden;
  }
</style>

<style lang="less">
  .left_wrap {
    width: 220px;
    margin-bottom: 95px;
    padding: 15px;

    .item:nth-child(2n) {
      margin-right: 0;
    }

    .item {
      position: relative;
      width: 90px;
      height: 90px;
      margin-right: 10px;
      margin-bottom: 10px;
      border-radius: 3px;
      background: #fff5f0;

      .mask {
        display: flex;
        position: absolute;
        inset: 0;
        background: rgb(0 0 0 / 70%);
        color: #fff;
        font-size: 14px;
      }

      .image_wrap {
        width: 35px;
        height: 35px;
        margin-top: 7px;
        margin-bottom: 3px;

        img {
          max-width: 100%;
          max-height: 100%;
        }
      }

      .name {
        color: #333;
        font-size: 14px;
      }
    }
  }

  .center_wrap {
    position: relative;
    flex: 1;
    overflow: hidden;

    .center_scroller {
      flex: 1;
      min-height: 600px;
      background: #fff;

      .center_con_border {
        position: relative;
        width: 401px;
        margin-top: 30px;
        margin-bottom: 130px;
        padding: 13px;
        background: #f8f8f8;
      }

      .center_con {
        width: 375px;
        height: 100%;
        min-height: 600px;
        border: 1px solid #eee;
        background: #fff;

        .fixed_top {
          width: 100%;
          border-width: 1px;
          border-style: solid;

          .cat_nav {
            position: relative;
            width: 100%;
            height: 48px;
            color: #fff;
            font-size: 14px;

            .cat_nav_left {
              width: 100%;
              height: 48px;
              white-space: nowrap;

              .right_empty {
                display: inline-block;
                flex-shrink: 0;
                width: 58px;
                height: 1px;
              }
            }

            .cat_nam {
              display: inline-block;
              position: relative;
              flex-shrink: 0;
              margin: -8px 13px 0;
            }

            .current_cat_nav::after {
              content: ' ';
              position: absolute;
              z-index: 2;
              bottom: -7px;
              left: 50%;
              width: 16px;
              height: 10px;
              margin-left: -8px;
              border: 2px solid #fff;
              border-top: none;
              border-right: none;
              border-left: none;
              border-radius: 100%;
            }
          }

          .right_fixed {
            position: absolute;
            z-index: 3;
            top: 0;
            right: 0;
            width: 58px;
            height: 36px;
            margin: 3px 0;
            box-shadow: -6px 0 4px -4px rgb(0 0 0 / 40%);

            .fixed_icon {
              width: 11px;
              height: 9px;
              margin-right: 4px;
            }

            .fixed_con {
              color: #fff;
              font-size: 14px;
            }
          }
        }

        .lunbo_wrap {
          display: flex !important;
          position: relative;
          width: 100%;
          margin: 0 auto;

          img {
            width: 355px;
            height: 140px;
            border-radius: 8px;
          }

          .swiper_img_wrap {
            position: relative;
            width: 373px;

            .swiper_bg {
              position: absolute;
              z-index: -1;
              top: -4rem;
              right: 0;
              left: 0;
              height: 10rem;
              border-radius: 30%;
            }
          }
        }

        .fixed_bottom {
          position: absolute;
          z-index: 2;
          bottom: 13px;
          left: 50%;
          width: 374px;
          height: 50px;
          margin-left: -187px;
          border: 1px solid;
          background: #fff;

          .fixed_bottom_item {
            flex: 1;
            margin-top: 2px;
            transform: scale(0.9);

            .fixed_bottom_img {
              width: 25px;
              height: 25px;
            }

            .fixed_bottom_img_large {
              width: 34px;
              height: 34px;
            }

            .fixed_bottom_name {
              margin-top: 2px;
              color: #333;
              font-family: 'Microsoft YaHei';
              font-size: 12px;
              font-weight: 400;
            }
          }
        }
      }
    }

    .status_bar {
      width: 375px;
      height: 50px;
    }

    .center_item:hover {
      border: dashed 1px #458feb;

      a {
        display: inline-block;
      }
    }

    .empty_swiper_img {
      width: 355px;
      height: 140px;
      border-radius: 8px;
      background: #ecf5ff;
    }
  }

  .right_wrap {
    width: 400px;
    height: 100%;

    .r_title {
      padding: 10px 20px;
      background: #f8f8f8;

      .r_title_left_border {
        display: inline-block;
        width: 4px;
        height: 14px;
        background: #397ed3;
      }

      .r_title_text {
        display: inline-block;
        margin-left: 10px;
        color: #fc701e;
        font-size: 16px;
        font-weight: bold;
      }
    }
  }

  .m_diy_bottom_wrap {
    display: flex;
    position: absolute;
    z-index: 999;
    right: 10px;
    bottom: 0;
    left: 0;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    height: 60px;
    background: rgb(255 255 255 / 100%);
    box-shadow: 0 0 20px 0 rgb(187 187 187 / 15%);
  }

  .btn_fixed_bottom {
    width: 116px;
    height: 36px;
    border-radius: 3px;
    background: rgb(65 157 253 / 100%);
    font-size: 14px !important;
  }
</style>
