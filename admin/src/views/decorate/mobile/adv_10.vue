<!-- 推荐商品组件 -->
<template>
  <div
    class="tuijianshangpin"
    :style="{
      padding: curFormData.page_margin + 'px',
      paddingTop: data.top_margin + 'px',
      paddingBottom: data.bottom_margin + 'px',
      width: `calc(100%-${curFormData.page_margin}px*2)`,
      backgroundColor: data.color ? data.color : curFormData.border_style == 'border_none_grey_bg' ? '#f5f5f5' : '#fff',
    }"
  >
    <div
      v-for="(item,index) in curFormData.data.info.length == 0 ? sld_com_empty_arrar_4 : curFormData.data.info"
      :key="index"
      class="goods_info"
      :style="{
        width: curFormData.show_style == 'list' ? (show_style * 2 )+'px': show_style+'px',
        borderRadius: curFormData.border_radius+'px', ...border_style,
        marginRight: getMarginRB(curFormData.data.info.length == 0?sld_com_empty_arrar_4:curFormData.data.info, index).marginR,
        marginBottom: getMarginRB(curFormData.data.info.length == 0?sld_com_empty_arrar_4:curFormData.data.info, index).marginB, ...list_layout,
        backgroundColor: '#fff',
      }"
    >
      <div
        class="img_wrap flex_com_row_center"
        :style="{
          width: show_style+'px',
          height: show_style+'px',
          borderTopLeftRadius: curFormData.border_radius+'px',
          borderTopRightRadius: curFormData.show_style == 'list' ? '0px' : curFormData.border_radius+'px',
          borderBottomLeftRadius: curFormData.show_style == 'list' ? curFormData.border_radius+'px' : '0px',
        }"
      >
        <img
          v-if="(diy_type == 'integral' && item.integralGoodsId != undefined) || (diy_type != 'integral' && item.goodsId != undefined)"
          :src="diy_type == 'spreader' ? item.goodsImage : item.mainImage"
          alt=""
        />
        <AliSvgIcon v-else iconName="icontupian1" width="40.8px" height="33.6px" fillColor="#fff"/>
      </div>
      <div
        class="flex_com_column_center_center detail" 
        :style="{
          alignItems: curFormData.text_align,
          padding: curFormData.show_style == 'list' ? '10px' : '8px 10px 8px 10px',
          justifyContent: curFormData.show_style == 'list' ? 'space-between' : 'center',
          height: curFormData.show_style == 'list' ? '100%' : 'auto',
        }"
      >
        <span :style="{fontWeight: curFormData.text_style}" class="name">{{ (item.integralGoodsId != undefined || item.goodsId != undefined) ? item.goodsName : '商品名称商品名称商品名称商品名称商品名称商品名称商品名称' }}</span>
        <div class="flex_column_start_start" style="width: 100%;">
          <div class="bottom_part flex_row_between_center">
            <div class="price flex_row_start_end">
              <template v-if="diy_type == 'integral'">
                <span class="unit">{{ type == 'empty' ? 0 : item.integralPrice }}积分{{ type == 'empty' ? '+¥0.00' : item.cashPrice !== undefined ? '+¥' + item.cashPrice : '' }}</span>
              </template>
              <template v-else-if="diy_type == 'spreader'">
                <span class="unit">¥</span>
                <span class="price_int">{{ type == 'empty' ? 0 : getPartNumber(item.productPrice, 'int') }}</span>
                <span class="price_decimal">{{ type == 'empty' ? '.00' : getPartNumber(item.productPrice, 'decimal') }}</span>
              </template>
              <template v-else>
                <span class="unit">¥</span>
                <span class="price_int">{{ type == 'empty' ? 0 : getPartNumber(item.goodsPrice, 'int') }}</span>
                <span class="price_decimal">{{ type == 'empty' ? '.00' : getPartNumber(item.goodsPrice, 'decimal') }}</span>
              </template>
            </div>
            <AliSvgIcon
              v-if="diy_type != 'integral' && diy_type != 'spreader' && curFormData.isshow_sales == 0 && curFormData.cart_icon_type && curFormData.cart_icon_type != 5"
              :iconName="'icon' + show_cart_icon_data()[curFormData.cart_icon_type - 1]['icon']"
              :width="show_cart_icon_data()[curFormData.cart_icon_type - 1]['width'] + 'px'"
              :height="show_cart_icon_data()[curFormData.cart_icon_type - 1]['width'] + 'px'"
              fillColor="#F10D3B"
            />
          </div>
          <div class="bottom_part flex_row_between_center" v-if="diy_type != 'integral' && curFormData.isshow_sales == 1" style="margin-top: 3px;">
            <span class="sales_num">已售{{ type == 'empty' ? 0 : item.goods_salenum }}件</span>
            <AliSvgIcon :iconName="'icon' + show_cart_icon_data()[curFormData.cart_icon_type - 1]['icon']" :width="show_cart_icon_data()[curFormData.cart_icon_type - 1]['width'] + 'px'"
            :height="show_cart_icon_data()[curFormData.cart_icon_type - 1]['width'] + 'px'" fillColor="#F10D3B" v-if="curFormData.cart_icon_type && curFormData.cart_icon_type != 5"/>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, computed, watch } from 'vue';
  import { sld_com_empty_arrar_4,getPartNumber,show_cart_icon_data } from '@/utils/utils';
  const props = defineProps({
    data: {
      type: Object,
      default: {
       top_margin: 10,
       bottom_margin: 0,
       color: '#fff',
       isshow_sales:0,//是否展示销量
       cart_icon_type:1,//购物车图标样式
       show_style:'small',//展示类型：big 大图 small 一行两个 list 列表 一行一个
       border_radius:10,//商品圆角 0表示直角
       border_style:'card-shadow',//商品样式  border_none无边白底  card-shadow卡片投影  border_eee描边白底 border_none_grey_bg 无边灰底
       page_margin:10,//距离页面两边的距离
       goods_margin:10,//商品之间的距离
       text_align:'flex-start',//文本对齐方式，flex-start 左对齐 center 居中对齐
       text_style:'normal',//文本样式，normal常规体 bold 加粗体
       data:{
        ids: [],
        info: [],
       }//商品数据
      }
    },
    diy_type: { type: String, default: 'home' }, //装修来源类型: home-首页手机装修 topic-专题装修 integral-积分商城装修 spreader-推手装修
  });

  const formData = computed(() => {
    return props.data;
  })

  const curFormData = ref({
    isshow_sales:0,//是否展示销量
    cart_icon_type:1,//购物车图标样式
    show_style:'small',//展示类型：big 大图 small 一行两个 list 列表 一行一个
    border_radius:10,//商品圆角 0表示直角
    border_style:'card-shadow',//商品样式  border_none无边白底  card-shadow卡片投影  border_eee描边白底 border_none_grey_bg 无边灰底
    page_margin:10,//距离页面两边的距离
    goods_margin:10,//商品之间的距离
    text_align:'flex-start',//文本对齐方式，flex-start 左对齐 center 居中对齐
    text_style:'normal',//文本样式，normal常规体 bold 加粗体
    data: {
      ids: [],
      info: [],
    },
  });

  const border_style = ref({}) //商品样式  border_none无边白底  card-shadow卡片投影  border_eee描边白底
  const show_style = ref() //展示类型：big 大图 small 一行两个 list 列表 一行一个
  const list_layout = ref({}) //list 展示的话商品图片和商品信息变为横向布局
  const type = ref('')

  watch(
    formData,
    () => {
      list_layout.value = {}
      show_style.value  = 0
      border_style.value = {}
      if (props.data.border_style == 'border_none') {
        border_style.value = { border: 0 };
      } else if (props.data.border_style == 'card-shadow') {
        border_style.value = { border: 0, boxShadow: '0 2px 8px rgba(93,113,127,.08)' };
      } else if (props.data.border_style == 'border_eee') {
        border_style.value = { border: '1px solid #e6e6e6' };
      }
      let screenWidth = 371; // body当前宽度
      if (props.data.show_style == 'small') {
        show_style.value = Math.floor((screenWidth - props.data.page_margin * 2 - props.data.goods_margin) / 2);
      } else if (props.data.show_style == 'list') {
        show_style.value = Math.floor((screenWidth - props.data.page_margin * 2) / 2);
        list_layout.value = {
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'flex-start',
          alignItems: 'flex-start',
        };
      } else {
        show_style.value = screenWidth - props.data.page_margin * 2;
      }
      if(props.data.data.info.length==0){
        type.value = 'empty'
      }else{
        type.value = 'goods'
      }
      curFormData.value = props.data
    },
    { deep: true, immediate: true },
  );

 
  

  const getMarginRB = (id_array, index) => { //推荐商品获取商品样式 data:id 数组 index 循环的序号

    let marginR = 0,
      marginB = 0;
    if (props.data.show_style == 'small') {
      marginB = marginR = props.data.goods_margin;
      if (index % 2 == 1) {
        marginR = 0;//每行的最后一个元素右边距为0
      }
      if (props.data.data.info.length % 2 == 0) {
        //最后一行是偶数
        if (index >= id_array.length - 2) {
          marginB = 0;
        }
      } else {
        //最后一行是奇数
        if (index == id_array.length - 1) {
          marginB = 0;
        }
      }
    } else {
      marginR = 0;
      marginB = props.data.goods_margin;
      if (index == id_array.length - 1) {
        //最后一个距离底部为0
        marginB = 0;
      }
    }
    return { marginB: marginB+'px', marginR: marginR+'px'};
  };


</script>
<style lang="less" scoped>
.tuijianshangpin {
  display: flex;
  flex-flow: row wrap;
  justify-content: flex-start;
  background: #fff;

  .goods_info {

    .img_wrap {
      overflow: hidden;
      background: #ECF5FF;

      img {
        max-width: 100%;
        max-height: 100%;
      }
    }

    .detail {
      flex: 1;

      .name {
        display: -webkit-box;
        height: 34px;
        overflow: hidden;
        color: #232326;
        font-size: 13px;
        line-height: 17px;
        text-overflow: ellipsis;
        word-break: break-all;
        -webkit-line-clamp: 2;

        /*! autoprefixer: off */
        -webkit-box-orient: vertical;
      }

      .bottom_part {
        width: 100%;
        margin-top: 0;

        .price {
          align-items: baseline;
          color: #FF2C20;

          .unit {
            font-size: 12px;
          }

          .price_int {
            margin-left: 1px;
            font-size: 16px;
            line-height: 16px;
          }

          .price_decimal {
            font-size: 12px;
          }
        }

        .sales_num {
          color: #9A9A9A;
          font-size: 12px;
        }
      }

      .price {
        display: inline-block;
        position: relative;
        top: 1px;
        height: 25px;
        padding: 0 5px 0 4px;
        color: #f23030;
        line-height: 25px;
      }
    }

  }
}
</style>