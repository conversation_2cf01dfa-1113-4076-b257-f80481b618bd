<template>
  <div class="section_padding order_lists_page">
    <div class="section_padding_back_flex">
      <SldComHeader
        title="历史订单"
        tipBtn="订单导出"
        tipBtnIcon="iconziyuan23"
        @tip-btn-click="handleSldExcel"
      />
      <Spin :spinning="loading">
        <div class="spin_height">
          <BasicForm
            :tableFlag="true"
            ref="formRef"
            submitOnReset
            @register="registerForm"
            class="basic-form"
          ></BasicForm>
          <div style="margin-bottom: 10px">
            <RadioGroup size="small" v-model:value="filter_code" @change="clickFilter">
              <RadioButton
                v-for="(item, index) in filter_data"
                :key="index"
                :value="item.filter_code"
                >{{ item.filter_name }}</RadioButton
              >
            </RadioGroup>
          </div>
          <div class="order_list">
            <ul class="header">
              <li class="width_30 pl_100">商品信息</li>
              <li class="width_10 center">单价</li>
              <li class="width_10 center">数量</li>
              <li class="width_10 center"></li>
              <li class="width_10 center">实付金额</li>
              <li class="width_10 center">付款方式</li>
              <li class="width_10 center">订单状态</li>
              <li class="width_10 center">操作</li>
            </ul>
            <div class="order_content">
              <Empty
                :image="Empty.PRESENTED_IMAGE_SIMPLE"
                v-if="data.list != undefined && data.list.length == 0"
              />
              <div
                class="order_content_height"
                v-if="data.list != undefined && data.list.length > 0"
              >
                <div class="item" v-for="(item, index) in data.list" :key="index">
                  <div class="order_info flex_row_between_center">
                    <div class="left flex_row_start_start">
                      <span class="order_sn">订单号：{{ item.orderSn }}</span>
                      <div class="order_type" v-if="item.isVirtualGoods == 2">
                        <img
                          src="@/assets/images/order/virtural_goods_order_icon.png"
                          alt=""
                          class="order_type_icon"
                        />
                        <span class="order_type_text">虚拟商品订单</span>
                      </div>
                      <span class="order_sn" style="margin-left: 20px"
                        >店铺【{{ item.storeName }}】</span
                      >
                    </div>
                    <span class="create_time">下单时间：{{ item.createTime }}</span>
                  </div>
                  <div class="order_goods_part flex_row_start_center">
                    <div
                      class="goods flex_column_start_start width_50"
                      :class="{
                        goods_split:
                          item.orderProductListVOList != undefined &&
                          item.orderProductListVOList.length > 1,
                      }"
                    >
                      <template
                        v-if="
                          item.orderProductListVOList != undefined &&
                          item.orderProductListVOList.length > 0
                        "
                      >
                        <div
                          class="goods_item flex_row_start_center"
                          style="width: 100%"
                          v-for="(item_goods, index_goods) in item.orderProductListVOList"
                          :key="index_goods"
                        >
                          <div
                            class="flex_row_start_center"
                            :style="{ width: '60%' }"
                          >
                            <div class="goods_img_wrap flex_row_center_center">
                              <img :src="item_goods.productImage" alt="" />
                            </div>
                            <div class="goods_info flex_column_start_start">
                              <span class="goods_name">{{ item_goods.goodsName }}</span>
                              <span class="goods_spec">{{ item_goods.specValues }}</span>
                            </div>
                          </div>
                          <span
                            class="goods_price center"
                            :style="{ width: '20%' }"
                            >￥{{
                              item_goods.productShowPrice
                                ? Number(item_goods.productShowPrice).toFixed(2)
                                : 0
                            }}</span
                          >
                          <span class="buy_num center" :style="{ width:'20%' }">{{
                            item_goods.productNum
                          }}</span>
                        </div>
                      </template>
                    </div>
                    <div class="pay_amount center width_10">
                      <span class="mem_name"
                        ></span
                      >
                    </div>
                    <div class="pay_amount center width_10">
                      <span class="mem_name"
                        >￥{{ item.orderAmount ? Number(item.orderAmount).toFixed(2) : 0 }}</span
                      >
                    </div>
                    <div class="order_state width_10 center">
                      {{ item.paymentName || '--' }}
                    </div>
                    <div class="order_state width_10 center">{{
                      item.orderStateValue || '--'
                    }}</div>
                    <div class="operate width_10 center flex_row_center_center">
                      <div class="operate_btn" @click="goDetail(item.orderSn)">查看详情</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              v-if="data.list != undefined && data.list.length > 0 && data.pagination != undefined"
              class="pagination"
            >
              <Pagination
                size="small"
                show-quick-jumper
                v-model:current="data.pagination.current"
                :show-total="(total) => `共 ${total} 条数据`"
                :total="data.pagination.total"
                :defaultPageSize="PAGE_SIZE"
                :pageSizeOptions="PAGE_SIZE_OPTIONS"
                @change="onPageChange"
              />
            </div>
          </div>
        </div>
      </Spin>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'OrderListsPage',
  };
</script>
<script setup>
  import { ref, onMounted,computed } from 'vue';
  import { RadioGroup, RadioButton, Spin, Empty, Pagination } from 'ant-design-vue';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { failTip, list_com_page_size_10 } from '/@/utils/utils';
  import { PAGE_SIZE, PAGE_SIZE_OPTIONS } from '/@/components/Table/src/const.ts';
  import { getOrderList, exportOrder } from '/@/api/manage/manage';
  import { validatorEmoji } from '/@/utils/validate';
  import { useRouter } from 'vue-router';

  const props = defineProps({
    id: { type: String || Number, required: true }, //id
  });

  const id = computed(() => {
    return props.id;
  });

  const router = useRouter();
  const filter_code = ref('');
  const pageSize = ref(list_com_page_size_10);

  const filter_data = ref([
    { filter_code: '', filter_name: `全部订单` },
    { filter_code: '10', filter_name: `待付款订单` },
    { filter_code: '20', filter_name: `待发货订单` },
    // { filter_code: '31', filter_name: `部分发货订单` },
    { filter_code: '30', filter_name: `待收货订单` },
    { filter_code: '40', filter_name: `已完成订单` },
    { filter_code: '0', filter_name: `已取消订单` },
  ]);

  const loading = ref(false);
  const data = ref({
    list: [],
  });
  const params = ref({ pageSize: pageSize.value });
  const formValues = ref({}); //搜索条件

  const handleSldExcel = async () => {
    if (data.value.list.length == 0) {
      failTip('暂无数据可导出');
      return;
    }
    let paramData = {
      ...params.value,
      ...formValues.value,
      memberId:id.value,
    };
    if (filter_code.value) {
      paramData.orderState = filter_code.value;
    }
    paramData.fileName = `订单导出`;
    let res = await exportOrder(paramData);
  };

  //表单
  const [registerForm, { validate, getFieldsValue }] = useForm({
    schemas: [
      {
        field: 'orderSn',
        component: 'Input',
        componentProps: {
          placeholder: '请输入订单号',
          size: 'default',
        },
        label: '订单号',
        rules: [
          {
            // @ts-ignore
            validator: async (rule, value) => {
              await validatorEmoji(rule, value);
            },
            trigger: 'change',
          },
        ],
      },
      {
        field: 'goodsName',
        component: 'Input',
        componentProps: {
          placeholder: '请输入商品名称',
          size: 'default',
        },
        label: '商品名称',
        rules: [
          {
            // @ts-ignore
            validator: async (rule, value) => {
              await validatorEmoji(rule, value);
            },
            trigger: 'change',
          },
        ],
      },
      {
        field: 'fieldTime',
        component: 'RangePicker',
        colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: ['开始时间', '结束时间'],
        },
        label: `下单时间`,
        labelWidth: 75,
      }
    ],
    fieldMapToTime: [['fieldTime', ['startTime', 'endTime'], 'YYYY-MM-DD']],
    labelWidth: 80,
    baseColProps: { span: 6 },
    actionColOptions: { span: 24 },
    autoSubmitOnEnter: true,
    showAdvancedButton: true,
    submitFunc: search,
    resetFunc: reset,
  });

  // 搜索
  async function search() {
    await validate();
    let values = getFieldsValue();
    values.startTime = values.startTime ? values.startTime.split(' ')[0] + ' 00:00:00' : undefined;
    values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
    for (let i in values) {
      if (values[i] == undefined) {
        delete values[i];
      }
    }
    formValues.value = values;
    get_list({ ...params.value, ...formValues.value });
  }

  async function reset() {
    params.value.current = 1;
  }

  //订单条件过滤器
  const clickFilter = (e) => {
    filter_code.value = e.target.value;
    let param = { pageSize: pageSize.value, current: 1, ...formValues.value };
    if (e.target.value) {
      param.orderState = e.target.value;
    }
    get_list(param);
  };

  //获取数据列表
  const get_list = async (param) => {
    loading.value = true;
    let res = await getOrderList({ ...param,memberId:id.value, });
    if (res.state == 200) {
      loading.value = false;
      if (param.current > 1 && res.data.list.length == 0 && params.value.current > 1) {
        param.current = param.current - 1;
        get_list(params);
      } else {
        data.value = res.data;
      }
    } else {
      loading.value = false;
      failTip(res.msg);
    }
  };

  //改变页码
  const onPageChange = (page, pageSize) => {
    let curParams = { pageSize: pageSize, current: page, ...formValues.value };
    if (filter_code.value) {
      curParams.orderState = filter_code.value;
    }
    params.value = curParams;
    get_list(curParams);
  };

  // 查看详情
  const goDetail = (orderSn) => {
    router.push(`/manage_order/order_lists_to_detail?orderSn=${orderSn}`);
  };

  onMounted(() => {
    get_list({ pageSize: pageSize.value });
  });
</script>
<style lang="less">
  @import './style/order.less';
</style>
