<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <a-spin :spinning="spinning">
        <SldComHeader
          :title="'积分设置'"
        />
        <div class="site_info_scroll">
          <StandardTableRow
            width="100%"
            :data="rowData"
            @callback-event="callbackEvent"
            @submit-event="submitEvent"
          />
        </div>
      </a-spin>
    </div>
  </div>
</template>
<script>
  import { defineComponent, ref, onMounted } from 'vue';
  import { Spin } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import StandardTableRow from '/@/components/StandardTableRow/index.vue';
  import { getSettingList, updateSettingList } from '/@/api/member/member';
  import { sucTip, failTip } from '/@/utils/utils';

  export default defineComponent({
    name: 'ponitSetting',
    components: {
      [Spin.name]: Spin,
      SldComHeader,
      StandardTableRow,
    },
    setup() {
      const spinning = ref(false);
      const rowData = ref([]);
      const clickEvent = ref(false); //事件防抖动

      //获取站点基本配置
      const get_base_site = async () => {
        spinning.value = true;
        let params = {
          str: 'integral_present_register,integral_present_login,integral_present_order_evaluate,integral_present_goods_buy,integral_present_order_max',
        };
        const res = await getSettingList(params);
        if (res.state == 200 && res.data) {
          let data = [];
          res.data.map((item) => {
            data.push({
              type: 'inputnum',
              label: item.title,
              key: item.name,
              placeholder: '请输入' + item.title,
              width: 300,
              height: 85,
              min: 0,
              max: 9999999,
              initValue: '',
              value: item.value,
              callback: true,
              desc: item.description,
              maxlength: 8,
            });
          });
          if (data.length > 0) {
            data.push({
              type: 'button',
              width: 300,
              showSubmit: true,
              submitText: '保存',
              showCancle: false,
            });
          }
          rowData.value = data;
        } else {
          failTip(res.msg);
        }
        spinning.value = false;
      };

      // 输入回调
      const callbackEvent = (item) => {};

      //保存站点基本配置
      const submitEvent = async (params) => {
        if (clickEvent.value) return;
        clickEvent.value = true;
        const res = await updateSettingList(params);
        if (res.state == 200) {
          sucTip(res.msg);
        } else {
          failTip(res.msg);
        }
        clickEvent.value = false;
      };

      onMounted(() => {
        get_base_site();
      });

      return {
        spinning,
        rowData,
        clickEvent,
        get_base_site,
        callbackEvent,
        submitEvent,
      };
    },
  });
</script>
<style lang="less" scoped>
  .common_page {
    padding: 20px;
    background: #fff;
  }
</style>
