<template>
  <div class="sensitive_setting_setting super_member_set">
    <Spin :spinning="initLoading">
      <div class="full_activity">
        <Form layout="inline" ref="formRef" :model="setting">
          <div class="full_acm_activity flex_column_start_start">
            <!-- 功能开关-start -->
            <div class="item flex_row_start_start" style="padding-top: 5px;">
              <div class="left">功能开关：</div>
              <div class="right">
                <Form.Item name="super_is_enable" extra="" style="width: 300px;">
                  <Switch
                    :checkedValue="1"
                    :unCheckedValue="0"
                    v-model:checked="setting.super_is_enable"

                  />
                </Form.Item>
              </div>
            </div>
            <!-- 功能开关-end -->
            <!-- 会员权益是否适用入驻商家-start -->
            <div class="item flex_row_start_start">
              <div class="left">会员权益是否适用入驻商家：</div>
              <div class="right">
                <Form.Item name="super_enter_store_is_applicable" extra="" style="width: 300px;">
                  <Switch
                    :checkedValue="1"
                    :unCheckedValue="0"
                    v-model:checked="setting.super_enter_store_is_applicable"

                  />
                </Form.Item>
              </div>
            </div>
            <!-- 会员权益是否适用入驻商家-end -->
            <!-- 自定义名称-start -->
            <div class="item flex_row_start_start">
              <div class="left">自定义名称：</div>
              <div class="right">
                <Form.Item
                    extra="最多6个字，默认为超级会员"
                    name="super_custom_name"
                    style="width: 300px"
                  >
                    <Input
                      :maxLength="6"
                      placeholder="请输入自定义名称"
                      v-model:value="setting.super_custom_name"
                    />
                  </Form.Item>
              </div>
            </div>
            <!-- 自定义名称-end -->
            <!-- 个人中心超级会员图标-start -->
            <div class="item flex_row_start_start">
              <div class="left">个人中心超级会员图标：</div>
              <div class="right">
                <Form.Item
                  name="super_personal_center_icon"
                  style="width: 300px"
                >
                  <div class="upload_file">
                    <Upload
                      :maxCount="1"
                      accept=" .gif, .jpeg, .png, .jpg, .bmp, .tif, .svg"
                      name="file"
                      :action="`${apiUrl}/v3/oss/admin/upload?source=setting`"
                      listType="picture-card"
                      @click="beforeUploadClick"
                      :beforeUpload="(file) => beforeUpload(file, ' .gif, .jpeg, .png, .jpg, .bmp, .tif, .svg')"
                      :file-list="setting.super_personal_center_icon_url"
                      @change="(e) => handleFileChange(e,'super_personal_center_icon')"
                      :headers="{
                        Authorization: imgToken,
                      }"
                    >
                      <div v-if="setting.super_personal_center_icon_url.length < 1">
                        <PlusOutlined />
                        <div className="ant-upload-text">上传图片</div>
                      </div>
                    </Upload>
                  </div>
                </Form.Item>
              </div>
            </div>
            <!-- 个人中心超级会员图标-end -->
            <!-- dev_mobile-start -->
            <!-- 移动端宣传页-start -->
            <div class="item flex_row_start_start">
              <div class="left">移动端宣传页：</div>
              <div class="right">
                <Form.Item
                  name="super_mobile_page"
                  style="width: 300px"
                  extra="建议尺寸1125*1350px，最大不过5M"
                >
                  <div class="upload_file">
                    <Upload
                      :maxCount="1"
                      accept=" .gif, .jpeg, .png, .jpg, .bmp, .tif, .svg"
                      name="file"
                      @click="beforeUploadClick"
                      :action="`${apiUrl}/v3/oss/admin/upload?source=setting`"
                      listType="picture-card"
                      :beforeUpload="(file) => beforeUpload(file, ' .gif, .jpeg, .png, .jpg, .bmp, .tif, .svg')"
                      :file-list="setting.super_mobile_page_url"
                      @change="(e) => handleFileChange(e,'super_mobile_page')"
                      :headers="{
                        Authorization: imgToken,
                      }"
                    >
                      <div v-if="setting.super_mobile_page_url.length < 1">
                        <PlusOutlined />
                        <div className="ant-upload-text">上传图片</div>
                      </div>
                    </Upload>
                  </div>
                </Form.Item>
              </div>
            </div>
            <!-- 移动端宣传页-end -->
            <!-- dev_mobile-end -->
            <!-- dev_pc-start -->
            <!-- PC端宣传页-start -->
            <div class="item flex_row_start_start">
              <div class="left">PC端宣传页：</div>
              <div class="right">
                <Form.Item
                  name="super_pc_page"
                  style="width: 300px"
                  extra="建议尺寸1920*497px，最大不过10M"
                >
                  <div class="upload_file">
                    <Upload
                      :maxCount="1"
                      accept=" .gif, .jpeg, .png, .jpg, .bmp, .tif, .svg"
                      name="file"
                      @click="beforeUploadClick"
                      :action="`${apiUrl}/v3/oss/admin/upload?source=setting`"
                      listType="picture-card"
                      :beforeUpload="(file) => beforeUpload(file, ' .gif, .jpeg, .png, .jpg, .bmp, .tif, .svg')"
                      :file-list="setting.super_pc_page_url"
                      @change="(e) => handleFileChange(e,'super_pc_page')"
                      :headers="{
                        Authorization: imgToken,
                      }"
                    >
                      <div v-if="setting.super_pc_page_url.length < 1">
                        <PlusOutlined />
                        <div className="ant-upload-text">上传图片</div>
                      </div>
                    </Upload>
                  </div>
                </Form.Item>
              </div>
            </div>
            <!-- PC端宣传页-end -->
            <!-- dev_pc-end -->
            <!-- 会员价格-start -->
            <div class="item flex_row_start_start">
              <div class="left">会员价格：</div>
              <div class="right">
                <div class="flex_row_start_center" style="margin-bottom: 10px;">
                  <Form.Item
                      name="super_price_one_month_enabled"
                      style="width: 60px"
                    >
                    <Checkbox :checked="setting.super_price_one_month_enabled== 1 ? true : false" @change="(e) => handleFieldChange(e.target.checked ? 1 : 0, 'super_price_one_month_enabled')"> 
                      <span style="display: inline-block;width: 50px;">一个月</span>
                    </Checkbox>
                  </Form.Item>
                  <template v-if="setting.super_price_one_month_enabled == 1">
                    <InputNumber
                        :max="9999999"
                        :min="0.01"
                        :precision="2"
                        style="width: 120px !important"
                        v-model:value="setting.super_price_one_month"
                      />
                    <span style="margin-left: 8px;color: #000000a6;font-size: 14px;">元</span>
                  </template>
                </div>
                <div class="flex_row_start_center" style="margin-bottom: 10px;">
                  <Form.Item
                      name="super_price_three_month_enabled"
                      style="width: 60px"
                    >
                    <Checkbox :checked="setting.super_price_three_month_enabled== 1 ? true : false" @change="(e) => handleFieldChange(e.target.checked ? 1 : 0, 'super_price_three_month_enabled')"> 
                      <span style="display: inline-block;width: 50px;">三个月</span>
                    </Checkbox>
                  </Form.Item>
                  <template v-if="setting.super_price_three_month_enabled == 1">
                    <InputNumber
                        :max="9999999"
                        :min="0.01"
                        :precision="2"
                        style="width: 120px !important"
                        v-model:value="setting.super_price_three_month"
                      />
                    <span style="margin-left: 8px;color: #000000a6;font-size: 14px;">元</span>
                  </template>
                </div>
                <div class="flex_row_start_center" style="margin-bottom: 10px;">
                  <Form.Item
                      name="super_price_one_year_enabled"
                      style="width: 60px"
                    >
                    <Checkbox :checked="setting.super_price_one_year_enabled== 1 ? true : false" @change="(e) => handleFieldChange(e.target.checked ? 1 : 0, 'super_price_one_year_enabled')"> 
                      <span style="display: inline-block;width: 50px;">一年</span>
                    </Checkbox>
                  </Form.Item>
                  <template v-if="setting.super_price_one_year_enabled == 1">
                    <InputNumber
                        :max="9999999"
                        :min="0.01"
                        :precision="2"
                        style="width: 120px !important"
                        v-model:value="setting.super_price_one_year"
                      />
                    <span style="margin-left: 8px;color: #000000a6;font-size: 14px;">元</span>
                  </template>
                </div>
              </div>
            </div>
             <!-- 会员价格-end -->
            <!-- 权益设置-start -->
            <div class="item flex_row_start_start">
              <div class="left">权益设置：</div>
              <div class="right">
                <div class="flex_row_start_center" style="margin-bottom: 10px;">
                  <Form.Item
                      name="super_discount_enabled"
                      style="width: 300px"
                      extra="超级会员购买自营商品折扣"
                    >
                    <Checkbox :checked="setting.super_discount_enabled== 1 ? true : false" @change="(e) => handleFieldChange(e.target.checked ? 1 : 0, 'super_discount_enabled')"> 
                      <span style="display: inline-block;width: 50px;">购物打</span>
                    </Checkbox>
                    <FormItemRest>
                      <template v-if="setting.super_discount_enabled == 1">
                        <InputNumber
                            :max="9.9"
                            :min="0.1"
                            :precision="1"
                            style="width: 120px !important"
                            v-model:value="setting.super_discount"
                          />
                        <span style="margin-left: 8px;color: #000000a6;font-size: 14px;">折</span>
                      </template>
                    </FormItemRest>
                  </Form.Item>
                </div>
                <div class="flex_row_start_center" style="margin-bottom: 10px;">
                  <Form.Item
                      name="super_integral_multiple_enabled"
                      style="width: 300px"
                      extra="超级会员购买或评价赠送积分翻倍"
                    >
                    <Checkbox :checked="setting.super_integral_multiple_enabled== 1 ? true : false" @change="(e) => handleFieldChange(e.target.checked ? 1 : 0, 'super_integral_multiple_enabled')"> 
                      <span style="display: inline-block;">赠送积分翻<span  v-if="setting.super_integral_multiple_enabled != 1">倍</span></span>
                    </Checkbox>
                    <FormItemRest>
                      <template v-if="setting.super_integral_multiple_enabled == 1">
                        <InputNumber
                            :max="10"
                            :min="1.1"
                            :precision="1"
                            style="width: 120px !important"
                            v-model:value="setting.super_integral_multiple"
                          />
                        <span style="margin-left: 8px;color: #000000a6;font-size: 14px;">倍</span>
                      </template>
                    </FormItemRest>
                  </Form.Item>
                </div>
                <div class="" style="margin-bottom: 10px;">
                  <Form.Item
                      name="super_coupon_enabled"
                      style="width: 300px"
                    >
                    <Checkbox :checked="setting.super_coupon_enabled== 1 ? true : false" @change="(e) => handleFieldChange(e.target.checked ? 1 : 0, 'super_coupon_enabled')"> 
                      赠送专属优惠券
                    </Checkbox>
                  </Form.Item>
                  <template v-if="setting.super_coupon_enabled == 1">
                    <Button type="primary" style="margin-left: 24px;margin-bottom: 10px;" @click="setAccount('exclusive')">设置优惠券</Button>
                  </template>
                  <!-- 标准表格-start -->
                  <template v-if="exclusiveCouponListRow.length > 0">
                      <BasicTable
                        rowKey="couponId"
                        :columns="view_columns"
                        style="width: 600px;padding-left: 0;"
                        :dataSource="exclusiveCouponListRow"
                        :pagination="false"
                        :bordered="true"
                        :maxHeight="200"
                        :ellipsis="false"
                        :actionColumn="{
                          title: '操作',
                          width: 80,
                          dataIndex: 'action',
                        }"
                      >
                        <template #bodyCell="{ column, record, text,index }">
                          <template v-if="column.dataIndex == 'sendNum'">
                            <InputNumber :min="1" :max="record.availableNum < 10 ? record.availableNum : 10" :precision="0" v-model:value="record.sendNum"/>
                          </template>
                          <template v-if="column.dataIndex === 'action'">
                            <span
                              @click="delCoupon(record.couponId, record.coupon_type)"
                              class="tableOperateText"
                            >
                            <AliSvgIcon
                              iconName="iconshanchu5"
                              width="18px"
                              height="18px"
                              fillColor="#2d2d2d"
                            />  
                            </span>
                          </template>
                        </template>
                      </BasicTable>
                    </template>
                    <!-- 标准表格-end -->
                </div>
                <div class="" style="margin-bottom: 10px;">
                  <Form.Item
                      name="super_birthday_coupon_enabled"
                      style="width: 300px"
                    >
                    <Checkbox :checked="setting.super_birthday_coupon_enabled== 1 ? true : false" @change="(e) => handleFieldChange(e.target.checked ? 1 : 0, 'super_birthday_coupon_enabled')"> 
                      赠送生日礼包
                    </Checkbox>
                  </Form.Item>
                  <template v-if="setting.super_birthday_coupon_enabled == 1">
                    <Button type="primary" style="margin-left: 24px;margin-bottom: 10px;" @click="setAccount('birthday')">设置优惠券</Button>
                  </template>
                  <!-- 标准表格-start -->
                  <template v-if="birthdayCouponListRow.length > 0">
                      <BasicTable
                        rowKey="couponId"
                        :columns="view_columns"
                        style="width: 600px;padding-left: 0;"
                        :dataSource="birthdayCouponListRow"
                        :pagination="false"
                        :bordered="true"
                        :maxHeight="200"
                        :ellipsis="false"
                        :actionColumn="{
                          title: '操作',
                          width: 80,
                          dataIndex: 'action',
                        }"
                      >
                        <template #bodyCell="{ column, record, text,index }">
                          <template v-if="column.dataIndex == 'sendNum'">
                            <InputNumber :min="1" :max="record.availableNum < 10 ? record.availableNum : 10" :precision="0" v-model:value="record.sendNum"/>
                          </template>
                          <template v-if="column.dataIndex === 'action'">
                            <span
                              @click="delCoupon(record.couponId, record.coupon_type)"
                              class="tableOperateText"
                            >
                            <AliSvgIcon
                              iconName="iconshanchu5"
                              width="18px"
                              height="18px"
                              fillColor="#2d2d2d"
                            />  
                            </span>
                          </template>
                        </template>
                      </BasicTable>
                    </template>
                    <!-- 标准表格-end -->
                </div>
                <div class="" style="margin-bottom: 10px;">
                  <Form.Item
                      name="super_freight_coupon_enabled"
                      style="width: 300px"
                    >
                    <Checkbox :checked="setting.super_freight_coupon_enabled== 1 ? true : false" @change="(e) => handleFieldChange(e.target.checked ? 1 : 0, 'super_freight_coupon_enabled')"> 
                      运费优惠
                    </Checkbox>
                  </Form.Item>
                  <template v-if="setting.super_freight_coupon_enabled == 1">
                    <Button type="primary" style="margin-left: 24px;margin-bottom: 10px;" @click="setAccount('freight')">设置运费券</Button>
                  </template>
                  <!-- 标准表格-start -->
                  <template v-if="freightCouponListRow.length > 0">
                      <BasicTable
                        rowKey="couponId"
                        :columns="view_columns"
                        style="width: 600px;padding-left: 0;"
                        :dataSource="freightCouponListRow"
                        :pagination="false"
                        :bordered="true"
                        :maxHeight="200"
                        :ellipsis="false"
                        :actionColumn="{
                          title: '操作',
                          width: 80,
                          dataIndex: 'action',
                        }"
                      >
                        <template #bodyCell="{ column, record, text,index }">
                          <template v-if="column.dataIndex == 'sendNum'">
                            <InputNumber :min="1" :max="record.availableNum < 10 ? record.availableNum : 10" :precision="0" v-model:value="record.sendNum"/>
                          </template>
                          <template v-if="column.dataIndex === 'action'">
                            <span
                              @click="delCoupon(record.couponId, record.coupon_type)"
                              class="tableOperateText"
                            >
                            <AliSvgIcon
                              iconName="iconshanchu5"
                              width="18px"
                              height="18px"
                              fillColor="#2d2d2d"
                            />  
                            </span>
                          </template>
                        </template>
                      </BasicTable>
                    </template>
                    <!-- 标准表格-end -->
                </div>
              </div>
            </div>
             <!-- 权益设置-end -->

          </div>
        </Form>
      </div>
      <div style="width: 100%; height: 60px;"></div>
      <div
        class="m_diy_bottom_wrap"
        :style="{ position: 'fixed', left: getRealWidth + 10 + 'px' }"
      >
        <div class="add_goods_bottom_btn add_goods_bottom_btn_sel" @click="handleSaveAllData">
          保存并应用
        </div>
      </div>
    </Spin>
    <SldSelGoodsSingleDiy link_type="crowed_coupon" coupon_type="super" :selectedRows="selectedRows" :selectedRowKeys="selectedRowKeys" :searchInfo="searchInfo" :modalVisible="visibleModal" @cancleEvent="sldHandleLinkCancle"
      @confirm-event="sldHandleLinkConfirm" >
      </SldSelGoodsSingleDiy>
  </div>
</template>
<script>
  export default {
    name: 'SuperMemberSet',
  };
</script>
<script setup>
  import { ref, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import { Spin,Form,Switch,Input,Upload,Checkbox,InputNumber,FormItemRest,Button,Modal  } from 'ant-design-vue';
  import { useGlobSetting } from '/@/hooks/setting';
  import SldSelGoodsSingleDiy from '/@/components/SldSelGoodsSingleDiy/index.vue';
  import { getToken } from '/@/utils/auth';
  import { BasicTable, useTable,TableAction } from '/@/components/Table';
  import { failTip, list_com_page_size_10, sucTip,beforeUpload } from '/@/utils/utils';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import {getSuperMemberSettingList,getSuperMemberUpdateSettingApi } from '/@/api/member/super';

  const router = useRouter();
  const { apiUrl } = useGlobSetting();
  const { getRealWidth } = useMenuSetting();
  const formRef = ref()

  const initLoading = ref(false)//页面初始化加载状态
  const imgToken = ref('Bearer ' + getToken() || ''); //上传文件token参数
  const modal_width = ref(300)//图片预览宽度
  const preview_img = ref('')//预览图片
  const preview_alt_con = ref('')//预览图片内容
  const show_preview_modal = ref(false)//预览图片modal框是否展示
  const searchInfo = ref({})
  const setting = ref({
    super_personal_center_icon_url:[],
    // dev_mobile-start
    super_mobile_page_url:[],
    // dev_mobile-end
    // dev_pc-start
    super_pc_page_url:[]
    // dev_pc-end
  }) //设置信息
  const params = ref({pageSize:list_com_page_size_10})
  const selectedRows = ref([])
  const selectedRowKeys = ref([])
  const visibleModal = ref(false)
  const data = ref({})
  const exclusiveCouponListRow = ref([]) //赠送专属优惠券
  const birthdayCouponListRowKeys = ref([]) 
  const freightCouponListRow = ref([]) //运费优惠
  const exclusiveCouponListRowKeys = ref([]) 
  const birthdayCouponListRow = ref([])//赠送生日礼包
  const freightCouponListRowKeys = ref([])

  const view_columns = ref([
    {
      title: '优惠券名称',
      dataIndex: 'couponName',
      align: 'center',
      width: 100,
    },
    {
      title: '优惠券类型',
      dataIndex: 'couponTypeValue',
      align: 'center',
      width: 100,
    },
    {
      title: '优惠券内容',
      dataIndex: 'couponContent',
      align: 'center',
      width: 100,
    },
    {
      title: '赠送数量',
      dataIndex: 'sendNum',
      align: 'center',
      width: 100,
    },
  ])
 
  const coupon_list_type = ref('') //优惠券类型，exclusive-专属优惠券；birthday-生日礼包；freight-运费券
  const super_is_enable = ref('')  //超级会员功能开关状态

  //数据变化事件
  function handleFileChange(e,key) {
    if (e.file.status != undefined && e.file.status != 'error') {
      if (e.file.response && e.file.response.state != 200 && e.fileList.length > 0) {
        failTip(e.file.response.msg || '上传失败');
        e.fileList = e.fileList.filter(item => item.response.state == 200);
      }
      e.fileList.forEach((item) => {
        if (item.response && item.response.data) {
          item.url = item.response.data.url;
        }
      });
      let img_succ_info = (e.file.response != undefined && e.fileList.length > 0 && e.file.response.data != undefined) ? e.file.response.data : [];
      setting.value[key] = img_succ_info.path;
      setting.value[key+'_url'] = e.fileList;
    }
  }

  const get_super_member_setting = async()=> {
    let res = await getSuperMemberSettingList({})
    if (res.state == 200) {
      if (res.data.exclusiveCouponList.length > 0) {
        res.data.exclusiveCouponList.map(item => {
          item.coupon_type = 'exclusive';
          exclusiveCouponListRow.value.push(item);
          exclusiveCouponListRowKeys.value.push(item.couponId);
        })
      }
      if (res.data.birthdayCouponList.length > 0) {
        res.data.birthdayCouponList.map(item => {
          item.coupon_type = 'birthday';
          birthdayCouponListRow.value.push(item);
          birthdayCouponListRowKeys.value.push(item.couponId);
        })
      }
      if (res.data.freightCouponList.length > 0) {
        res.data.freightCouponList.map(item => {
          item.coupon_type = 'freight';
          freightCouponListRow.value.push(item);
          freightCouponListRowKeys.value.push(item.couponId);
        })
      }
      res.data.settingList.map(item => {
        if (item.name == 'super_is_enable') {
          super_is_enable.value = item.value ? true : false;
        }
        if (item.name.indexOf('_enable') != -1 || item.name == 'super_enter_store_is_applicable') {
          setting.value[item.name] = (item.value == 1 || item.value == 'true') ? 1 : 0;
        } else if (
          item.name == 'super_personal_center_icon'
          // dev_mobile-start
          || item.name == 'super_mobile_page'
          // dev_mobile-end
          // dev_pc-start
          || item.name == 'super_pc_page'
          // dev_pc-end
          ) {
          setting.value[item.name] = item.value;
          if (item.value) {
            let image_file = {};
            image_file.uid = item.value;
            image_file.thumbUrl = item.imageUrl;
            image_file.status = 'done';
            image_file.response = {};
            image_file.response.state = 200;
            image_file.response.data = {
              path: item.value,
              url: item.imageUrl,
            };
            setting.value[item.name+'_url'] = [image_file];
          }
        } else {
          setting.value[item.name] = item.value;
        }
      })
    }else{
      failTip(res.msg);
    }
  }

  const handleFieldChange = (val, key)=> {
    setting.value[key] = val;
  }

  const beforeUploadClick = async()=> {
    imgToken.value = 'Bearer ' + userStore.getToken
    if(userStore.getToken){
      let res = await userStore.getSldCommonService()
      if(res.state == 200){
        imgToken.value = 'Bearer ' + res.token
      }
    }
  }


  // 打开优惠券列表弹窗
  const setAccount = (type)=> {
    coupon_list_type.value = type
    selectedRows.value = []
    selectedRowKeys.value = []
    if(type == 'exclusive'){
      selectedRows.value = JSON.parse(JSON.stringify(exclusiveCouponListRow.value))
      selectedRowKeys.value = JSON.parse(JSON.stringify(exclusiveCouponListRowKeys.value))
      searchInfo.value = {type, systemType: 'admin', state: 2, couponTypeNotEquals: 4}
      visibleModal.value = true
    }else if(type == 'birthday'){
      selectedRows.value = JSON.parse(JSON.stringify(birthdayCouponListRow.value))
      selectedRowKeys.value = JSON.parse(JSON.stringify(birthdayCouponListRowKeys.value))
      searchInfo.value = {type, systemType: 'admin', state: 2, couponTypeNotEquals: 4}
      visibleModal.value = true
    }else{
      selectedRows.value = JSON.parse(JSON.stringify(freightCouponListRow.value))
      selectedRowKeys.value = JSON.parse(JSON.stringify(freightCouponListRowKeys.value))
      searchInfo.value = {type, systemType: 'admin', state: 2, couponType:4}
      visibleModal.value = true
    }
  }

  // 弹窗点击确定
  const sldHandleLinkConfirm = (record, ids)=> {
    if(ids.length==0){
      failTip('请选择数据');
      return;
    } else {
      for (var i in record) {
        if (!record[i].sendNum) {
          failTip('请输入' + record[i].couponName + '的赠送数量');
          return;
        }
      }
    }
    record.forEach(item=>item.coupon_type = coupon_list_type.value)
    if(coupon_list_type.value == 'exclusive'){
      exclusiveCouponListRow.value = JSON.parse(JSON.stringify(record))
      exclusiveCouponListRowKeys.value = JSON.parse(JSON.stringify(ids))
    }else if(coupon_list_type.value == 'birthday'){
      birthdayCouponListRow.value = JSON.parse(JSON.stringify(record))
      birthdayCouponListRowKeys.value = JSON.parse(JSON.stringify(ids))
    }else{
      freightCouponListRow.value = JSON.parse(JSON.stringify(record))
      freightCouponListRowKeys.value = JSON.parse(JSON.stringify(ids))
    }
    selectedRows.value = []
    selectedRowKeys.value = []
    visibleModal.value = false
  }

  // 弹窗关闭
  const sldHandleLinkCancle = () => {
    visibleModal.value = false;
    searchInfo.value = {}
  };

  // 删除
  const delCoupon = (coupon_id, coupon_type)=> {
    if (coupon_type == 'exclusive') {
      exclusiveCouponListRow.value = exclusiveCouponListRow.value.filter(item => item.couponId != coupon_id);
      exclusiveCouponListRowKeys.value = exclusiveCouponListRowKeys.value.filter(item => item != coupon_id);
    } else if (coupon_type == 'birthday') {
      birthdayCouponListRow.value = birthdayCouponListRow.value.filter(item => item.couponId != coupon_id);
      birthdayCouponListRowKeys.value = birthdayCouponListRowKeys.value.filter(item => item != coupon_id);
    } else {
      freightCouponListRow.value = freightCouponListRow.value.filter(item => item.couponId != coupon_id);
      freightCouponListRowKeys.value = freightCouponListRowKeys.value.filter(item => item != coupon_id);
    }
  }

  const handleSaveAllData = ()=> {
    formRef.value
      .validate()
      .then(async (value) => {
        if(super_is_enable.value && !value.super_is_enable){
          Modal.confirm({
            title: '是否确认将付费会员功能关闭？',
            content: '关闭后现有付费会员的权益将受影响而无法使用，请谨慎操作。',
            okText: '继续关闭',
            cancelText: '取消',
            okType: 'default',
            onOk() {
              handleSubmit()
            },
          });
        }else{
          handleSubmit()
        }
      })
  }

  const handleSubmit = async()=> {
    let params = { ...setting.value };
    if (!params.super_personal_center_icon) {
      params.super_personal_center_icon = '';
    }
    // dev_mobile-start
    if (!params.super_mobile_page) {
      params.super_mobile_page = '';
    }
    // dev_mobile-end
    // dev_pc-start
    if (!params.super_pc_page) {
      params.super_pc_page = '';
    }
    // dev_pc-end
    delete params.super_personal_center_icon_url;
    // dev_mobile-start
    delete params.super_mobile_page_url;
    // dev_mobile-end
    // dev_pc-start
    delete params.super_pc_page_url;
    // dev_pc-end
    // 处理优惠券 start
    let exlArr = [];
    if (params.super_coupon_enabled && exclusiveCouponListRow.value.length==0) {
      failTip('请设置赠送专属优惠券');
      return;
    } else if (params.super_birthday_coupon_enabled && birthdayCouponListRow.value.length==0) {
      failTip('请设置赠送生日礼包优惠券');
      return;
    } else if (params.super_freight_coupon_enabled && freightCouponListRow.value.length==0) {
      failTip('请设置运费优惠券');
      return;
    }
    for (var exl=0; exl < exclusiveCouponListRow.value.length; exl++) {
      if (!exclusiveCouponListRow.value[exl].sendNum) {
        failTip('请输入赠送专属优惠券['+exclusiveCouponListRow.value[exl].couponName+']的赠送数量')
        return;
      } else {
        exlArr.push(exclusiveCouponListRow.value[exl].couponId+'-'+exclusiveCouponListRow.value[exl].sendNum);
      }
    }
    let birArr = [];
    for (var bir=0; bir < birthdayCouponListRow.value.length; bir++) {
      if (!birthdayCouponListRow.value[bir].sendNum) {
        failTip('请输入赠送生日礼包['+birthdayCouponListRow.value[bir].couponName+']的赠送数量')
        return;
      } else {
        birArr.push(birthdayCouponListRow.value[bir].couponId+'-'+birthdayCouponListRow.value[bir].sendNum);
      }
    }
    let freArr = [];
    for (var fre=0; fre < freightCouponListRow.value.length; fre++) {
      if (!freightCouponListRow.value[fre].sendNum) {
        failTip('请输入运费优惠['+freightCouponListRow.value[fre].couponName+']的赠送数量')
        return;
      } else {
        freArr.push(freightCouponListRow.value[fre].couponId+'-'+freightCouponListRow.value[fre].sendNum);
      }
    }
    params.super_coupon = exlArr.join();
    params.super_birthday_coupon = birArr.join();
    params.super_freight_coupon = freArr.join();
     // 处理优惠券 end

    for (var key in params) {
      if (key.indexOf('_enable') != -1 || key == 'super_enter_store_is_applicable') {
        params[key] = params[key] ? 1 : 0;
      }
    }

    if (!params.super_custom_name) {
      params.super_custom_name = '超级会员';
    }

    // 处理会员价格 start
    if (!params.super_price_one_month_enabled && !params.super_price_three_month_enabled && !params.super_price_one_year_enabled) {
      failTip('请至少选择一项会员价格');
      return;
    } else if (params.super_price_one_month_enabled && !params.super_price_one_month) {
      failTip('请输入会员价格一个月的金额');
      return;
    } else if (params.super_price_three_month_enabled && !params.super_price_three_month) {
      failTip('请输入会员价格三个月的金额');
      return;
    } else if (params.super_price_one_year_enabled && !params.super_price_one_year) {
      failTip('请输入会员价格一年的金额');
      return;
    }
    if (!params.super_price_one_month_enabled && params.super_price_one_month) {
      delete params.super_price_one_month;
    }
    if (!params.super_price_three_month_enabled && params.super_price_three_month) {
      delete params.super_price_three_month;
    }
    if (!params.super_price_one_year_enabled && params.super_price_one_year) {
      delete params.super_price_one_year;
    }
    // 处理会员价格 end
    if (!params.super_discount_enabled) {
      delete params.super_discount;
    }
    if (!params.super_integral_multiple_enabled) {
      delete params.super_integral_multiple;
    }
    let res = await getSuperMemberUpdateSettingApi(params)
    if (res.state == 200) {
      sucTip(res.msg);
    } else {
      failTip(res.msg);
    }
  }

  onMounted(() => {
    get_super_member_setting()
  });
</script>
<style lang="less">
 @import '/@/assets/css/promotion.less';

  .sensitive_setting_setting{
    flex: 1;
    overflow: auto;
  }

  .super_member_set{
    .ant-table-body{
      height: auto !important;
      max-height: 200px !important;
    }
  }
</style>
