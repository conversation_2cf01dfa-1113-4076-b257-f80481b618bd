<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <SldComHeader
        :title="'充值管理'"
      />
      <a-tabs v-model:activeKey="tabIndex" type="card" @change="handleTab">
        <a-tab-pane key="1" tab="基本设置" />
        <a-tab-pane key="2" tab="充值明细" />
      </a-tabs>
      <div class="section_padding_tab_top"></div>
      <template v-if="tabIndex == '1'">
        <StandardTableRow
          width="100%"
          :data="basicSettings"
          @callback-event="callbackEvent"
          @submit-event="submitEvent"
        />
      </template>
      <template v-else>
        <div class="recharge_detail_top">
          <div class="item">
            <AliSvgIcon iconName="iconziyuan57" width="95px" height="95px" fillColor="#fa6f1e" />
            <div class="right">
              <span class="num">{{
                rechargeNumAndAmount.rechargeNum ? rechargeNumAndAmount.rechargeNum : 0
              }}</span>
              <span class="text">累计充值人数</span>
            </div>
          </div>
          <div class="item">
            <AliSvgIcon iconName="iconziyuan58" width="95px" height="95px" fillColor="#fa6f1e" />
            <div class="right">
              <span class="num">{{
                rechargeNumAndAmount.rechargeSum ? rechargeNumAndAmount.rechargeSum : 0
              }}</span>
              <span class="text">累计充值金额(元)</span>
            </div>
          </div>
        </div>
        <BasicTable @register="standardTable" style="padding: 0">
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.key == 'action'">
              <template v-if="record.payState == 1">
                <span class="common_page_edit" @click="handleClick(record, 'pay')">付款</span>
                <Popconfirm
                  title="删除后不可恢复，是否确定删除？"
                  @confirm="handleClick(record, 'del')"
                >
                  <span class="common_page_edit">删除</span>
                </Popconfirm>
              </template>
              <template v-else>--</template>
            </template>
            <template v-else-if="column.key">
              {{ text ? text : '--' }}
            </template>
          </template>
        </BasicTable>
        <SldModal
          :width="width"
          :title="title"
          :visible="visible"
          :content="content"
          :confirmBtnLoading="confirmBtnLoading"
          :showFoot="true"
          @cancle-event="handleCancle"
          @confirm-event="handleConfirm"
        />
      </template>
    </div>
  </div>
</template>
<script>
  import { defineComponent, ref, onMounted } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import SldModal from '@/components/SldModal/index.vue';
  import StandardTableRow from '/@/components/StandardTableRow/index.vue';
  import { Tabs, Popconfirm, InputNumber } from 'ant-design-vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import {
    getSettingList,
    updateSettingList,
    getRechargeNum,
    getRechargeDetailList,
    toPay,
    delRecharge,
  } from '/@/api/member/member';
  import { failTip, sucTip } from '/@/utils/utils';

  export default defineComponent({
    name: 'MemberRecharges',
    components: {
      SldComHeader,
      [Tabs.name]: Tabs,
      [Tabs.TabPane.name]: Tabs.TabPane,
      StandardTableRow,
      BasicTable,
      SldModal,
      Popconfirm,
      InputNumber,
    },
    setup() {
      const [standardTable, { reload }] = useTable({
        api: (arg) => getRechargeDetailList({ ...arg }),
        // 点击搜索前处理的参数
        handleSearchInfoFn: (values) => {
          values.startTime = values.startTime
            ? values.startTime.split(' ')[0] + ' 00:00:00'
            : undefined;
          values.endTime = values.endTime
            ? values.endTime.split(' ')[0] + ' 23:59:59'
            : undefined;
          values.payStartTime = values.payStartTime
            ? values.payStartTime.split(' ')[0] + ' 00:00:00'
            : undefined;
          values.payEndTime = values.payEndTime
            ? values.payEndTime.split(' ')[0] + ' 23:59:59'
            : undefined;
          return values;
        },
        beforeFetch(values) {
          values.startTime = values.startTime
            ? values.startTime.split(' ')[0] + ' 00:00:00'
            : undefined;
          values.endTime = values.endTime
            ? values.endTime.split(' ')[0] + ' 23:59:59'
            : undefined;
          values.payStartTime = values.payStartTime
            ? values.payStartTime.split(' ')[0] + ' 00:00:00'
            : undefined;
          values.payEndTime = values.payEndTime
            ? values.payEndTime.split(' ')[0] + ' 23:59:59'
            : undefined;
          return values;
        },
        fetchSetting: {
          pageField: 'current',
          sizeField: 'pageSize',
          listField: 'data.list',
          totalField: 'data.pagination.total',
        },
        columns: [
          {
            title: '会员名',
            dataIndex: 'memberName',
            width: 100,
          },
          {
            title: '手机号',
            dataIndex: 'memberMobile',
            width: 100,
          },
          {
            title: '充值金额',
            dataIndex: 'payAmount',
            width: 100,
          },
          {
            title: '充值方式',
            dataIndex: 'paymentName',
            width: 100,
          },
          {
            title: '充值单号',
            dataIndex: 'rechargeSn',
            width: 100,
          },
          {
            title: '流水号',
            dataIndex: 'tradeSn',
            width: 100,
          },
          {
            title: '充值状态',
            dataIndex: 'payStateValue',
            width: 100,
          },
          {
            title: '创建时间',
            dataIndex: 'createTime',
            width: 100,
          },
          {
            title: '支付完成时间',
            dataIndex: 'payTime',
            width: 100,
          },
        ],
        actionColumn: {
          width: 150,
          title: '操作',
          dataIndex: 'action',
        },
        useSearchForm: true,
        formConfig: {
          schemas: [
            {
              field: 'memberName',
              component: 'Input',
              colProps: { span: 6, style: 'width:210px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 150,
                maxlength: 10,
                placeholder: '请输入会员名',
                size: 'default',
              },
              label: '会员名',
              labelWidth: 60,
            },
            {
              field: 'memberMobile',
              component: 'Input',
              colProps: { span: 6, style: 'width:210px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 150,
                maxLength: 11,
                placeholder: '请输入手机号',
                size: 'default',
              },
              label: '手机号',
              labelWidth: 60,
            },
            {
              field: 'payState',
              component: 'Select',
              colProps: { span: 6, style: 'width:220px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 150,
                placeholder: '请选择充值状态',
                size: 'default',
                options: [
                  { key: 0, label: '全部', value: '' },
                  { key: 1, label: '已支付', value: '2' },
                  { key: 2, label: '未支付', value: '1' },
                ],
              },
              label: '充值状态',
              labelWidth: 70,
            },
            {
              field: 'paymentCode',
              component: 'Select',
              colProps: { span: 6, style: 'width:220px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 150,
                placeholder: '请选择充值方式',
                size: 'default',
                options: [
                  { key: 0, label: '全部', value: '' },
                  { key: 1, label: '微信支付', value: 'WXPAY' },
                  { key: 2, label: '支付宝支付', value: 'ALIPAY' },
                ],
              },
              label: '充值方式',
              labelWidth: 70,
            },
            {
              field: '[startTime, endTime]',
              component: 'RangePicker',
              colProps: { span: 6, style: 'width:290px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 220,
                format: 'YYYY-MM-DD',
                placeholder: ['开始时间', '结束时间'],
                size: 'default',
              },
              label: '创建时间',
              labelWidth: 70,
            },
            {
              field: '[payStartTime, payEndTime]',
              component: 'RangePicker',
              colProps: { span: 6, style: 'width:290px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 220,
                format: 'YYYY-MM-DD',
                placeholder: ['开始时间', '结束时间'],
                size: 'default',
              },
              label: '支付时间',
              labelWidth: 70,
            },
          ],
        },
        bordered: true,
        striped: false,
        showIndexColumn: true,
      });
      const tabIndex = ref('1'); //tab下标
      const rechargeState = ref(true); //充值开关的状态 发现tab切换的时候开关的状态值会丢失 用这个保存状态值
      const basicSettings = ref([]); //基本设置配置
      const rechargeNumAndAmount = ref({}); //充值数量
      const choose_row = ref({}); //选择的项
      const choose_type = ref(''); //选择的类型
      const width = ref(500);
      const title = ref('');
      const visible = ref(false);
      const content = ref([]);
      const confirmBtnLoading = ref(false);

      // 付款弹出的内容
      const pay_content = ref([
        {
          type: 'date_picker',
          width: '333px',
          label: `付款时间`,
          name: 'payTime',
          placeholder: `请选择付款时间`,
          format: 'YYYY-MM-DD HH:mm:ss',
          show_time: true,
          rules: [
            {
              required: true,
              message: '请选择付款时间',
            },
          ],
          callback: true,
        },
        {
          type: 'select',
          label: `付款方式`,
          name: 'paymentCode',
          placeholder: `请选择付款方式`,
          width: '333px',
          initialValue: 'PCALIPAY',
          selData: [
            {
              key: 'PCALIPAY',
              name: 'PC支付宝',
            },
            {
              key: 'H5ALIPAY',
              name: 'H5支付宝',
            },
            {
              key: 'PCWXPAY',
              name: 'PC微信',
            },
            {
              key: 'H5WXPAY',
              name: 'H5微信',
            },
          ],
          callback: true,
        },
        {
          type: 'input',
          label: `平台交易号`,
          name: 'tradeSn',
          placeholder: `请输入平台交易号`,
          rules: [
            {
              required: true,
              whitespace: true,
              message: '请输入平台交易号',
            },
          ],
          callback: true,
        },
      ]);

      const handleTab = (e) => {
        if (e == 2) {
          getNum();
        }
      };

      const callbackEvent = (e) => {
        rechargeState.value = e.val1;
      };

      const submitEvent = async (val) => {
        let params = { recharge_is_enable: rechargeState.value ? 1 : 0 };
        let res = await updateSettingList(params);

        if (res.state == 200) {
          sucTip(res.msg);
        } else {
          getRechargeInfo();
          failTip(res.msg);
        }
      };

      // 获取充值配置信息
      const getRechargeInfo = async () => {
        let params = { str: 'recharge_is_enable' };
        const res = await getSettingList(params);
        const resData = res.data[0];
        rechargeState.value = resData.value == 1 ? true : false;
        basicSettings.value = [
          {
            type: 'switch',
            label: resData.title,
            key: resData.name,
            width: 300,
            desc_width: 300,
            initValue: '',
            value: resData.value == 1 ? true : false,
            callback: true,
            desc: resData.description,
          },
          {
            type: 'button',
            width: 300,
            showSubmit: true,
            submitText: '保存',
            showCancle: false,
          },
        ];
      };

      //点击回调事件
      const handleClick = (val, type) => {
        choose_row.value = val ? val : {};
        choose_type.value = type;
        if (type == 'pay') {
          title.value = '付款';
          visible.value = true;
          content.value = JSON.parse(JSON.stringify(pay_content.value));
        } else if (type == 'del') {
          const rechargeIds = { rechargeIds: val.rechargeId };
          OperateType(rechargeIds);
        }
      };

      //弹窗确认事件
      const handleConfirm = (val) => {
        val.rechargeId = choose_row.value.rechargeId;
        OperateType(val);
      };

      const OperateType = async (params) => {
        let res = {};
        if (choose_type.value == 'pay') {
          res = await toPay(params);
        } else if (choose_type.value == 'del') {
          res = await delRecharge(params);
        }
        if (res.state == 200) {
          sucTip(res.msg);
          if (choose_type.value == 'pay') getNum();
          handleCancle();
          reload();
        } else {
          failTip(res.msg);
        }
      };

      //弹窗取消事件
      const handleCancle = () => {
        visible.value = false;
        content.value = [];
        choose_row.value = {};
        choose_type.value = '';
      };

      // 获取充值人数和充值金额
      const getNum = async () => {
        let res = await getRechargeNum({});
        if (res.state == 200) {
          rechargeNumAndAmount.value = res.data;
        } else {
          failTip(res.mag);
        }
      };
      onMounted(() => {
        getRechargeInfo();
      });

      return {
        tabIndex,
        basicSettings,
        rechargeNumAndAmount,
        width,
        title,
        visible,
        content,
        confirmBtnLoading,
        handleTab,
        callbackEvent,
        submitEvent,
        standardTable,
        handleClick,
        handleCancle,
        handleConfirm,
      };
    },
  });
</script>
<style lang="less" scoped>
  .common_page {
    padding: 20px;
    background: #fff;

    .common_page_edit {
      position: relative;
      margin-right: 5px;
      padding-right: 5px;
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover {
        color: #ff6a12;
      }

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        right: 0;
        width: 1px;
        height: 12px;
        margin-top: -5px;
        background: #ddd;
      }

      &:last-of-type {
        &::after {
          display: none;
        }
      }
    }

    &.common_page_toolbar {
      .vben-basic-table-form-container .ant-form {
        margin-bottom: 0;
      }

      .toolbar {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: 100%;
        height: 42px;
        margin-bottom: 5px;
        background: #ffe3d5;

        .toolbar_btn {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 28px;
          margin-left: 10px;
          padding: 0 7px;
          border-radius: 3px;
          background: #fff;
          cursor: pointer;

          span {
            margin-left: 5px;
          }
        }
      }
    }
  }

  .recharge_detail_top {
    display: flex;
    position: relative;
    align-items: center;
    justify-content: center;
    height: 150px;
    margin-bottom: 20px;
    border-bottom: 1px solid #e2e5f6;

    &::after {
      content: ' ';
      position: absolute;
      width: 1px;
      height: 50%;
      background: rgb(70 92 235 / 20%);
    }

    .item {
      display: flex;
      flex: 1 1;
      align-items: center;
      justify-content: center;

      .right {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-left: 65px;

        .num {
          color: #fa6f1e;
          font-size: 42px;
          font-weight: 700;
          line-height: 65px;
        }

        .text {
          color: #333;
          font-size: 18px;
        }
      }
    }
  }
</style>
