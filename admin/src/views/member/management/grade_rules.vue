<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <SldComHeader title="会员等级规则" />
      <Spin :spinning="showLoading">
        <div class="add_crowd_height full_activity">
          <SldUEditor
            :id="'agreement'"
            v-if="showEditoring"
            :initEditorContent="initEditorContent"
            :getContentFlag="getEditorContentFlag"
            :getEditorContent="getEditorContent"
          />
        </div>
        <div
            class="m_diy_bottom_wrap"
            :style="{ position: 'fixed', left: getRealWidth + 10 + 'px' }"
          >
            <div class="add_goods_bottom_btn add_goods_bottom_btn_sel" @click="handleSaveAllData">
              保存
            </div>
          </div>
      </Spin>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'MemberSuperGradeRules',
  };
</script>
<script setup>
  import { ref, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { Spin } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import {getMemberSettingGetListApi,getMemberSettingUpdateListApi} from '/@/api/member/super';
  import { sucTip, failTip,quillEscapeToHtml } from '@/utils/utils';
  import SldUEditor from '/@/components/SldUEditor/index.vue';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';

  const { getRealWidth } = useMenuSetting();
  const router = useRouter();

  const initEditorContent = ref(''); //百度编辑器内容
  const getEditorContentFlag = ref(false); //获取百度编辑器内容标识
  const detail = ref({})//文章详情
  const showLoading = ref(true)
  const showEditoring = ref(false)
  const click_event = ref(false)

  // 获取详情数据
  const getDetail = async()=> {
    let res = await getMemberSettingGetListApi({ str: 'level_rule' })
    if (res.state == 200) {
      //初始化数据
      detail.value = res.data[0].value;
      initEditorContent.value = quillEscapeToHtml(detail.value);
      showLoading.value = false
      showEditoring.value = true
    } else {
      failTip(res.msg);
      showEditoring.value = true
    }
  }

   //获取编辑器内容
   function getEditorContent(con) {
    if (click_event.value) return;
    click_event.value = true;
    showLoading.value = true
    getEditorContentFlag.value = false;
    saveData(con)
  }

  const saveData = async(con)=> {
    let res = await getMemberSettingUpdateListApi({ level_rule: con })
    click_event.value = false
    showLoading.value = false
    if (res.state == 200) {
      sucTip(res.msg);
      getDetail()
    } else {
      failTip(res.msg);
    }
  }

  const handleSaveAllData = ()=> {
    getEditorContentFlag.value = true;
  }

  onMounted(() => {
    getDetail()
  });
</script>
<style lang="less">
  @import '/@/assets/css/promotion.less';
</style>
