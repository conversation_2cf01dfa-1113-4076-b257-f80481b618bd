<template>
  <div class="section_padding super_grade_add">
    <div class="section_padding_back_add">
      <SldComHeader title="编辑等级" />
      <Spin :spinning="initLoading">
        <div class="add_crowd_height full_activity">
          <Form layout="inline" ref="formRef" :model="setting">
            <!-- 基本信息-start -->
            <CommonTitle text="基本信息" style="margin-top: 10px" />
            <div style="width: 100%;height: 10px;"></div>
            <div class="full_acm_activity flex_column_start_start">
              <!-- 等级名称-start -->
              <div class="item flex_row_start_start">
                <div class="left">
                  <span style="color: red;">*</span>等级名称
                </div>
                <div class="right">
                  <Form.Item
                    name="levelName"
                    style="width: 200px"
                    :rules="[
                      {
                        required: true,
                        whitespace: true,
                        message: `请输入等级名称`,
                      },
                    ]"
                  >
                    <Input
                      :maxLength="6"
                      style="width: 200px !important"
                      placeholder="请输入等级名称"
                      v-model:value="setting.levelName"
                    />
                  </Form.Item>
                </div>
              </div>
              <!-- 等级名称-end -->
              <!-- 获得条件 start -->
              <div class="item flex_row_start_start">
                <div class="left"> <span style="color: red">*</span>获得条件</div>
                <div class="right">
                  <Form.Item
                    extra="修改所需成长值后可能导致部分会员未达到成长值而发生等级变化。"
                    name="growthValue"
                    class="growthValue_box"
                    style="width: 500px"
                    :rules="[
                      {
                        required: true,
                        message: `请输入成长值`,
                      },
                    ]"
                  >
                    <div class="flex_row_start_center">
                      <span
                        style="margin-right: 8px;color: #000000A6;font-size: 14px;"
                        >成长值</span
                      >
                      <InputNumber
                        :min="query.min !== undefined ? query.min : 0"
                        :max="query.max !== undefined ? query.max : 9999999"
                        style="width: 150px !important"
                        :precision="0"
                        placeholder="请输入成长值"
                        v-model:value="setting.growthValue"
                      />
                    </div>
                  </Form.Item>
                </div>
              </div>
              <!-- 获得条件 end -->
            </div>
            <!-- 基本信息-end -->
            <!-- 权益信息-start -->
            <CommonTitle text="权益信息" style="margin-top: 10px" />
            <div style="width: 100%;height: 10px;"></div>
            <div class="full_acm_activity flex_column_start_start">
              <div class="item flex_row_start_start">
                <div class="left" style="width: 100px;"></div>
                <div class="right">
                  <div class="flex_column_start_start">
                    <Form.Item
                      name="freightCouponEnable"
                      style="width: 500px"
                    >
                      <Checkbox v-model:checked="setting.freightCouponEnable"
                      >
                        运费券
                      </Checkbox>
                    </Form.Item>
                    <div class="flex_column_start_start">
                      <div class="flex_row_start_center" v-if="setting.freightCouponEnable == 1">
                        <Button type="primary" style="margin-top: 5px;margin-left: 24px;" @click="setAccount('freight')">选择运费券</Button>
                        <div style="margin-top: 5px;margin-left: 5px;color: #000000A6;font-size: 14px;">已选<span style="margin: 0 2px;" class="primary_color">{{ freightCouponListRow.length }}</span>张</div>
                      </div>
                      <div style="width: 100%;height: 10px;"></div>
                       <!-- 标准表格-start -->
                       <template v-if="freightCouponListRow.length > 0">
                        <BasicTable
                          rowKey="couponId"
                          :columns="[...view_freight_columns, ...view_columns]"
                          style="width: 900px;padding-left: 0;"
                          :dataSource="freightCouponListRow"
                          :pagination="false"
                          :bordered="true"
                          :maxHeight="200"
                          :ellipsis="false"
                          :actionColumn="{
                            title: '操作',
                            width: 80,
                            dataIndex: 'action',
                          }"
                        >
                          <template #bodyCell="{ column, record, text,index }">
                            <template v-if="column.dataIndex == 'effectiveStart'">
                              <span v-if="record.cycle">领取后{{ record.cycle }}天内</span>
                              <div v-else-if="text && record.effectiveEnd" class="voucher_time_wrap">
                                <p>{{text}}</p>
                                <p>~</p>
                                <p>{{record.effectiveEnd}}</p>
                              </div>
                              <div v-else-if="record.useTime&&record.useTimeType!=2">
                                <p>{{record.useTime.split('~')[0]}}</p>
                                <p>~</p>
                                <p>{{record.useTime.split('~')[1]}}</p>
                              </div>
                              <span v-else-if="record.useTime&&record.useTimeType==2">
                              {{ record.useTime}}
                              </span>
                              <span v-else>
                                --
                              </span>
                            </template>
                            <template v-if="column.dataIndex == 'sendNum'">
                              <InputNumber :min="1" :max="record.totalNum != undefined ? record.totalNum : 9999999" v-model:value="record.sendNum"/>
                            </template>
                            <template v-if="column.dataIndex === 'action'">
                              <span
                                @click="delCoupon(record.couponId, record.coupon_type)"
                                class="tableOperateText"
                              >
                              <AliSvgIcon
                                iconName="iconshanchu5"
                                width="18px"
                                height="18px"
                                fillColor="#2d2d2d"
                              />  
                              </span>
                            </template>
                          </template>
                        </BasicTable>
                      </template>
                    <!-- 标准表格-end -->
                    </div>
                  </div>
                </div>
              </div>
              <div class="item flex_row_start_start">
                <div class="left" style="width: 100px;"></div>
                <div class="right">
                  <div class="flex_column_start_start">
                    <Form.Item
                      name="couponEnable"
                      style="width: 500px"
                    >
                      <Checkbox v-model:checked="setting.couponEnable"
                      >
                        优惠券
                      </Checkbox>
                    </Form.Item>
                    <div class="flex_column_start_start">
                      <div class="flex_row_start_center" v-if="setting.couponEnable == 1">
                        <Button type="primary" style="margin-top: 5px;margin-left: 24px;" @click="setAccount('coupon')">选择优惠券</Button>
                        <div style="margin-top: 5px;margin-left: 5px;color: #000000A6;font-size: 14px;">已选<span style="margin: 0 2px;" class="primary_color">{{ couponListRow.length }}</span>张</div>
                      </div>
                      <div style="width: 100%;height: 10px;"></div>
                       <!-- 标准表格-start -->
                       <template v-if="couponListRow.length > 0">
                        <BasicTable
                          rowKey="couponId"
                          :columns="[...view_coupon_columns,...view_columns]"
                          style="width: 900px;padding-left: 0;"
                          :dataSource="couponListRow"
                          :pagination="false"
                          :bordered="true"
                          :maxHeight="200"
                          :ellipsis="false"
                          :actionColumn="{
                            title: '操作',
                            width: 80,
                            dataIndex: 'action',
                          }"
                        >
                          <template #bodyCell="{ column, record, text,index }">
                            <template v-if="column.dataIndex == 'effectiveStart'">
                              <span v-if="record.cycle">领取后{{ record.cycle }}天内</span>
                              <div v-else-if="text && record.effectiveEnd" class="voucher_time_wrap">
                                <p>{{text}}</p>
                                <p>~</p>
                                <p>{{record.effectiveEnd}}</p>
                              </div>
                              <div v-else-if="record.useTime&&record.useTimeType!=2">
                                <p>{{record.useTime.split('~')[0]}}</p>
                                <p>~</p>
                                <p>{{record.useTime.split('~')[1]}}</p>
                              </div>
                              <span v-else-if="record.useTime&&record.useTimeType==2">
                              {{ record.useTime}}
                              </span>
                              <span v-else>
                                --
                              </span>
                            </template>
                            <template v-if="column.dataIndex == 'sendNum'">
                              <InputNumber :min="1" :max="record.totalNum != undefined ? record.totalNum : 9999999" v-model:value="record.sendNum"/>
                            </template>
                            <template v-if="column.dataIndex === 'action'">
                              <span
                                @click="delCoupon(record.couponId, record.coupon_type)"
                                class="tableOperateText"
                              >
                              <AliSvgIcon
                                iconName="iconshanchu5"
                                width="18px"
                                height="18px"
                                fillColor="#2d2d2d"
                              />  
                              </span>
                            </template>
                          </template>
                        </BasicTable>
                      </template>
                    <!-- 标准表格-end -->
                    </div>
                  </div>
                </div>
              </div>
              <div class="item flex_row_start_start">
                <div class="left" style="width: 100px;"></div>
                <div class="right">
                  
                  <div class="flex_row_start_center">
                    <Form.Item
                      name="integralMultipleEnable"
                      style="margin-right:0;"
                    >
                      <Checkbox v-model:checked="setting.integralMultipleEnable">积分回馈倍数</Checkbox>
                    </Form.Item>
                    <InputNumber
                      :min="1"
                      :max="100"
                      style="width: 120px !important"
                      :precision="0"
                      v-model:value="setting.integralMultipleValue"
                    />
                    <span style="margin-left:8px;color: #000000a6;font-size: 14px;">倍</span>
                  </div>
                </div>
              </div>
            </div>
            <!-- 权益信息-end -->
          </Form>
        </div>
        <div
            class="m_diy_bottom_wrap"
            :style="{ position: 'fixed', left: getRealWidth + 10 + 'px' }"
          >
            <div class="add_goods_bottom_btn" @click="goBack"> 返回 </div>
            <div v-if="query.type != 'view'" class="add_goods_bottom_btn add_goods_bottom_btn_sel" @click="handleSaveAllData">
              保存
            </div>
          </div>
      </Spin>
    </div>
    <SldSelGoodsSingleDiy link_type="level_coupon" :coupon_type="coupon_list_type" :selectedRows="selectedRows" :selectedRowKeys="selectedRowKeys" :searchInfo="searchInfo" :modalVisible="visibleModal" @cancleEvent="sldHandleLinkCancle"
      @confirm-event="sldHandleLinkConfirm" >
      </SldSelGoodsSingleDiy>
  </div>
</template>
<script>
  export default {
    name: 'MemberSuperGradeToAdd',
  };
</script>
<script setup>
  import { ref, onMounted } from 'vue';
  import { useRouter,useRoute } from 'vue-router';
  import { Spin,Form,Input,InputNumber,Checkbox,Button } from 'ant-design-vue';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import CommonTitle from '/@/components/CommonTitle/index.vue';
  import SldSelGoodsSingleDiy from '/@/components/SldSelGoodsSingleDiy/index.vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import {getMemberLevelUpdateApi,getAdminMemberLevelDetailApi } from '/@/api/member/super';
  import { BasicTable, useTable,TableAction } from '/@/components/Table';
  import { sucTip, failTip } from '@/utils/utils';
  import { pageClose } from '/@/utils/utils';
  import { useMultipleTabStore } from '/@/store/modules/multipleTab';
  
  const router = useRouter();
  const { getRealWidth } = useMenuSetting();
  const route = useRoute();
  const initLoading = ref(false)

  const formRef = ref()
  const tabStore = useMultipleTabStore();
  const query = ref(route.query)
  const setting = ref({}) //设置信息
  const data = ref({})
  const searchInfo = ref({});
  const formValues = ref({})
  const visibleModal = ref(false)
  const selectedRows = ref([])
  const selectedRowKeys = ref([])
  const couponListRow = ref([])
  const couponListRowKeys = ref([])
  const freightCouponListRow = ref([])
  const freightCouponListRowKeys = ref([])
  const view_freight_columns = ref([
    {
      title: '运费券名称',
      dataIndex: 'couponName',
      align: 'center',
      width: 120,
    },
    {
      title: '类型',
      dataIndex: 'couponTypeValue',
      align: 'center',
      width: 100,
    },
    {
      title: '运费券内容',
      dataIndex: 'couponContent',
      align: 'center',
      width: 100,
      customRender: ({ text }) => {
        return text ? text : '--';
      },
    },
  ])

  const view_coupon_columns = ref([
    {
      title: '优惠券名称',
      dataIndex: 'couponName',
      align: 'center',
      width: 120,
    },
    {
      title: '优惠券类型',
      dataIndex: 'couponTypeValue',
      align: 'center',
      width: 100,
    },
    {
      title: '优惠券内容',
      dataIndex: 'couponContent',
      align: 'center',
      width: 100,
    },
  ])

  const view_columns = ref([
    {
      title: '使用范围',
      dataIndex: 'description',
      align: 'center',
      width: 120,
    },
    {
      title: '使用时间',
      dataIndex: 'effectiveStart',
      align: 'center',
      width: 150,
    },
    {
      title: '赠券数',
      dataIndex: 'sendNum',
      align: 'center',
      width: 100,
    }
  ])

  const coupon_list_type = ref('') //优惠券类型 coupon-优惠券；freight-运费券
  
 //设置优惠券
  const setAccount = (type)=> {
    coupon_list_type.value = type
    searchInfo.value = {type:type,levelId:route.query.id}
    selectedRows.value = []
    selectedRowKeys.value = []
    if(type=='coupon'){
      selectedRows.value = JSON.parse(JSON.stringify(couponListRow.value))
      selectedRowKeys.value = JSON.parse(JSON.stringify(couponListRowKeys.value))
    }else{
      selectedRows.value = JSON.parse(JSON.stringify(freightCouponListRow.value))
      selectedRowKeys.value = JSON.parse(JSON.stringify(freightCouponListRowKeys.value))
    }
    visibleModal.value = true
  }

  // 弹窗关闭
  const sldHandleLinkCancle = () => {
    visibleModal.value = false;
  };

  const sldHandleLinkConfirm = (record, ids)=> {
    if (record.length == 0) {
      failTip('请选择数据');
      return;
    }else{
      for (var i in record) {
        if (!record[i].sendNum) {
          failTip('请输入' + record[i].couponName + '的赠券数');
          return;
        }
      }
    }
    record.forEach(item => item.coupon_type = coupon_list_type.value);
    if(coupon_list_type.value == 'coupon'){
      couponListRow.value = JSON.parse(JSON.stringify(record))
      couponListRowKeys.value = JSON.parse(JSON.stringify(ids))
      selectedRows.value = []
      selectedRowKeys.value = []
      visibleModal.value = false
    }else{
      freightCouponListRow.value = JSON.parse(JSON.stringify(record))
      freightCouponListRowKeys.value = JSON.parse(JSON.stringify(ids))
      selectedRows.value = []
      selectedRowKeys.value = []
      visibleModal.value = false
    }
  }

  // 删除运费券或优惠券
  const delCoupon = (coupon_id,coupon_type)=> {
    if(coupon_type == 'coupon'){
      couponListRow.value = couponListRow.value.filter(item => item.couponId != coupon_id);
      couponListRowKeys.value = couponListRowKeys.value.filter(item => item != coupon_id);

    }else{
      freightCouponListRow.value = freightCouponListRow.value.filter(item => item.couponId != coupon_id);
      freightCouponListRowKeys.value = freightCouponListRowKeys.value.filter(item => item != coupon_id);
    }
  }

  const goBack = ()=> {
    if (window.history && window.history.length == 1) {
      pageClose();
    } else {
      const { fullPath } = route;
      tabStore.closeTabByKey(fullPath, router);
      router.back();
    }
  }

  // 保存
  const handleSaveAllData = ()=> {
    formRef.value.validate().then(async (values) => {
      if(initLoading.value){
        return
      }
      let params = { ...setting.value };
      let couArr = [];
      for (var cou=0; cou < couponListRow.value.length; cou++) {
        if (!couponListRow.value[cou].sendNum) {
          failTip('请输入运费券['+couponListRow.value[cou].couponName+']的赠券数')
          return;
        } else {
          couArr.push(couponListRow.value[cou].couponId+'-'+couponListRow.value[cou].sendNum);
        }
      }
      let freArr = [];
      for (var fre=0; fre < freightCouponListRow.value.length; fre++) {
        if (!freightCouponListRow.value[fre].sendNum) {
          failTip('请输入优惠券['+freightCouponListRow.value[fre].couponName+']的赠券数')
          return;
        } else {
          freArr.push(freightCouponListRow.value[fre].couponId+'-'+freightCouponListRow.value[fre].sendNum);
        }
      }
      params.coupon = couArr.join();
      params.freightCoupon = freArr.join();
      for (var key in params) {
      if (key == 'freightCouponEnable' || key == 'couponEnable' || key == 'integralMultipleEnable') {
          params[key] = params[key] ? 1 : 0;
        } else if (!params[key] && params[key] !== 0) {
          delete params[key];
        }
      }
      if (!params.freightCouponEnable) {
        params.freightCoupon = '';
      } else if (freightCouponListRow.value.length == 0) {
        failTip('请选择运费券');
        return;
      }
      if (!params.couponEnable) {
        params.coupon = '';
      } else if (couponListRow.value.length == 0) {
        failTip('请选择优惠券');
        return;
      }
      if (params.integralMultipleEnable && !params.integralMultipleValue) {
        failTip('请输入积分回馈倍数');
        return;
      }
      initLoading.value = true
      let res = await getMemberLevelUpdateApi(params)
      if (res.state == 200) {
        sucTip(res.msg);
        localStorage.setItem('update_super_grade', 'true');
        setTimeout(()=>{
          goBack()
          initLoading.value = false
        },1000)
      } else {
        failTip(res.msg);
        initLoading.value = false
      }
    })
  }

  //获取成长值信息
  const get_super_grade_detail = async(levelId)=> {
    let res = await getAdminMemberLevelDetailApi({levelId:levelId})
    initLoading.value = false
    if (res.state == 200 && res.data) {
      for (var key in res.data) {
        if (key == 'freightCouponEnable' || key == 'couponEnable' || key == 'integralMultipleEnable') {
          setting.value[key] = res.data[key] == 1 ? true : false;
        } else if (key == 'couponList') {
          if (!res.data.couponList || res.data.couponList.length == 0) continue;
          res.data.couponList.map(item => {
            item.coupon_type = 'coupon';
            couponListRow.value.push(item);
            couponListRowKeys.value.push(item.couponId);
          })
        } else if (key == 'freightCouponList') {
          if (!res.data.freightCouponList || res.data.freightCouponList.length == 0) continue;
          res.data.freightCouponList.map(item => {
            item.coupon_type = 'freight';
            freightCouponListRow.value.push(item);
            freightCouponListRowKeys.value.push(item.couponId);
          })
        } else if (key == 'growthValue') {
          setting.value[key] = (query.max && res.data[key] > query.max) ? query.max : res.data[key];
        } else {
          setting.value[key] = res.data[key];
        }
      }
    } else {
      failTip(res.msg);
    }
  }

  onMounted(() => {
    if (route.query.id) {
      initLoading.value = true
      get_super_grade_detail(route.query.id);
    }
  });
</script>
<style lang="less">
  @import '/@/assets/css/promotion.less';

  .super_grade_add{
    .growthValue_box{
      .ant-form-item-explain{
        right: 61%;
        min-height: 20px;
      }
    }

    .ant-table-body{
      height: auto !important;
      max-height: 300px !important;
    }
  }
</style>
