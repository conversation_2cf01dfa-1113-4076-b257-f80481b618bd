<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <SldComHeader :type="1" :title="'会员管理'" />
      <BasicTable @register="standardTable" style="padding: 0">
        <template #tableTitle>
          <div class="toolbar">
            <div class="toolbar_btn" @click="handleClick(null, 'add')">
              <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#fff" />
              <span>新增会员</span>
            </div>
            
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.key == 'state'">
            <Switch
              :checked="text == '1' ? true : false"
              checked-children="可用"
              un-checked-children="禁用"
              @change="(e) => handleChange(e, record)"
            />
          </template>
          <template v-else-if="column.key == 'memberAvatar'">
            <Image :src="text" :width="50" :height="50" />
          </template>
          <template v-else-if="column.key == 'action'">
            <TableAction
              :actions="[
                {
                  label: '编辑',
                  onClick: handleClick.bind(null, record, 'edit'),
                },
                {
                  label: '重置密码',
                  onClick: handleClick.bind(null, record, 'reset'),
                },
                {
                  label: '设置积分',
                  onClick: handleClick.bind(null, record, 'setPoint'),
                },
                {
                  label: '余额变更',
                  onClick: handleClick.bind(null, record, 'changeBalance'),
                },
                {
                  label: '查看',
                  onClick: handleClick.bind(null, record, 'check'),
                },
              ]"
            />
          </template>
          <template v-else-if="column.key">
            {{ text!=null ? text : '--' }}
          </template>
        </template>
      </BasicTable>
      <SldModal
        :width="width"
        :title="title"
        :visible="visible"
        :content="content"
        :confirmBtnLoading="confirmBtnLoading"
        :showFoot="true"
        @radio-change-event="handleCallback"
        @callback-event="handleCallback"
        @cancle-event="handleCancle"
        @click-sms-event="handleSmsCancle"
        @confirm-event="handleConfirm"
      />
    </div>
  </div>
</template>
<script>
export default {
  name: 'MemberLists',
}
</script>
<script setup>
  import { ref, onMounted, reactive } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import SldModal from '@/components/SldModal/index.vue';
  import { TableAction,BasicTable, useTable } from '/@/components/Table';
  import { Switch, Image } from 'ant-design-vue';
  import {
    getMemberList,
    editUserState,
    addUser,
    editUser,
    editPwd,
    editMemberIntegral,
    editMemberBalance,
  } from '/@/api/member/member';
  import { getAdminMemberLevelListApi } from '/@/api/member/super';
  import { base64Encrypt, sucTip, failTip,selectRadio, SelectAll,  } from '/@/utils/utils';
  import { validatorMem, validatorMemPwd, mobile_reg, validatorVendorEmail,checkSmsCode } from '/@/utils/validate';
  import { useUserStore } from '/@/store/modules/user';
  import { useRouter } from 'vue-router';

  const userStore = useUserStore();
  const selectedRowKeys = ref([]);
  const selectedRows = ref([]);
  const searchInfo = ref({})
  const columns = reactive({
    data:[
    {
      title: '会员名',
      dataIndex: 'memberName',
      width: 100,
    },
    {
      title: '手机号',
      dataIndex: 'memberMobile',
      width: 100,
    },
    {
      title: '会员头像',
      dataIndex: 'memberAvatar',
      width: 100,
    },
    {
      title: '会员昵称',
      dataIndex: 'memberNickName',
      width: 100,
    },
    {
      title: '账户余额',
      dataIndex: 'balance',
      sorter: true,
      showSorterTooltip:false,
      sorterName:'balance_available',
      width: 100,
    },
    {
      title: '积分',
      sorter: true,
      showSorterTooltip:false,
      sorterName:'member_integral',
      dataIndex: 'memberIntegral',
      width: 100,
    },
    {
      title: '会员成长值',
      dataIndex: 'memberGrowthValue',
      width: 100,
    },
    {
      title: '等级',
      dataIndex: 'levelName',
      width: 100,
    },
    {
      title: '注册时间',
      dataIndex: 'registerTime',
      sorter: true,
      showSorterTooltip:false,
      sorterName:'register_time',
      width: 150,
    },
    {
      title: '最近一次支付时间',
      dataIndex: 'lastPayTime',
      sorter: true,
      showSorterTooltip:false,
      sorterName:'last_pay_time',
      width: 150,
    },
    {
      title: '累计支付订单数',
      dataIndex: 'totalPayNum',
      sorter: true,
      showSorterTooltip:false,
      sorterName:'total_pay_num',
      width: 150,
    },
    {
      title: '累计支付金额',
      dataIndex: 'totalPayAmount',
      sorter: true,
      showSorterTooltip:false,
      sorterName:'total_pay_amount',
      width: 150,
    },
    {
      title: '是否可用',
      dataIndex: 'state',
      width: 100,
    },
  ]})
  const schemas = [
    {
      field: 'memberName',
      component: 'Input',
      colProps: { span: 6, style: 'width:230px;max-width:100%;flex:none' },
      componentProps: {
        minWidth: 150,
        placeholder: '请输入会员名',
        size: 'default',
      },
      label: '会员名',
      labelWidth: 80,
    },
    {
      field: 'memberMobile',
      component: 'Input',
      colProps: { span: 6, style: 'width:230px;max-width:100%;flex:none' },
      componentProps: {
        minWidth: 150,
        maxLength: 11,
        placeholder: '请输入手机号',
        size: 'default',
      },
      label: '手机号',
      labelWidth: 80,
    },
    {
      field: 'state',
      component: 'Select',
      colProps: { span: 6, style: 'width:230px;max-width:100%;flex:none' },
      componentProps: {
        minWidth: 150,
        placeholder: '请选择会员状态',
        size: 'default',
        options: [
          { key: 0, label: '全部', value: '' },
          { key: 1, label: '正常', value: '1' },
          { key: 2, label: '禁用', value: '0' },
        ],
      },
      label: '会员状态',
      labelWidth: 80,
    },
    {
      field: '[startTime, endTime]',
      component: 'RangePicker',
      colProps: { span: 6, style: 'width:300px;max-width:100%;flex:none' },
      componentProps: {
        minWidth: 220,
        format: 'YYYY-MM-DD',
        placeholder: ['开始时间', '结束时间'],
        size: 'default',
      },
      label: '注册时间',
      labelWidth: 80,
    },
    {
      field: 'memberLevelId',
      component: 'Select',
      componentProps: {
        placeholder: '请选择会员等级',
        size: 'default',
        options: [],
      },
      label: '会员等级',
    },
    {
      field: 'payNumMin',
      component: 'DoubleInputNumber',
      componentProps: {
        fields: ['payNumMin','payNumMax'],
        precision: 0,
      },
      label: '累计支付次数',
    },
    {
      field: 'balanceMin',
      component: 'DoubleInputNumber',
      componentProps: {
        fields: ['balanceMin','balanceMax'],
        precision: 0,
      },
      label: '余额',
    },
    {
      field: 'gender',
      component: 'Select',
      componentProps: {
        placeholder: '请选择性别',
        size: 'default',
        options: [
          { key: 0, label: '全部', value: '' },
          { key: 1, label: '保密', value: '0' },
          { key: 2, label: '男', value: '1' },
          { key: 3, label: '女', value: '2' },
        ],
      },
      label: '性别',
    },
    {
      field: 'integralMin',
      component: 'DoubleInputNumber',
      componentProps: {
        fields: ['integralMin','integralMax'],
        precision: 0,
      },
      label: '积分',
    },
    {
      field: 'memberGrowthValueMin',
      component: 'DoubleInputNumber',
      componentProps: {
        fields: ['memberGrowthValueMin','memberGrowthValueMax'],
        precision: 0,
      },
      label: '会员成长值',
    },
    {
      field: 'registerChannel',
      component: 'Select',
      componentProps: {
        placeholder: '请选择来源渠道',
        size: 'default',
        options: [
          { key: 0, label: '全部', value: '' },
          { key: 1, label: 'PC', value: '1' },
          { key: 2, label: 'H5', value: '2' },
          { key: 3, label: '商城管理平台', value: '5' },
          { key: 4, label: '小程序', value: '6' },
        ],
      },
      label: '来源渠道',
    },
    {
      field: '[lastPayTimeStart, lastPayTimeEnd]',
      component: 'RangePicker',
      componentProps: {
        minWidth: 220,
        format: 'YYYY-MM-DD',
        placeholder: ['开始时间', '结束时间'],
        size: 'default',
      },
      label: '最近一次支付时间',
    },
    {
      field: '[birthdayStart, birthdayEnd]',
      component: 'RangePicker',
      componentProps: {
        format: 'YYYY-MM-DD',
        placeholder: ['开始时间', '结束时间'],
      },
      label: '生日',
    },
  ];
  const [standardTable, { reload,setProps }] = useTable({
    api: (arg) => getMemberList({ ...arg }),
    beforeFetch(values) {
      if (values.payNumMin) {
        if (values.payNumMin.split(',')[1]) {
          values.payNumMax = values.payNumMin.split(',')[1]
        }
        if (values.payNumMin.split(',')[0]) {
          values.payNumMin = values.payNumMin.split(',')[0]
        }
      }
      if (values.balanceMin) {
        if (values.balanceMin.split(',')[1]) {
          values.balanceMax = values.balanceMin.split(',')[1]
        }
        if (values.balanceMin.split(',')[0]) {
          values.balanceMin = values.balanceMin.split(',')[0]
        }
      }
      if (values.integralMin) {
        if (values.integralMin.split(',')[1]) {
          values.integralMax = values.integralMin.split(',')[1]
        }
        if (values.integralMin.split(',')[0]) {
          values.integralMin = values.integralMin.split(',')[0]
        }
      }
      if (values.memberGrowthValueMin) {
        if (values.memberGrowthValueMin.split(',')[1]) {
          values.memberGrowthValueMax = values.memberGrowthValueMin.split(',')[1]
        }
        if (values.memberGrowthValueMin.split(',')[0]) {
          values.memberGrowthValueMin = values.memberGrowthValueMin.split(',')[0]
        }
      }

      values.startTime = values.startTime
        ? values.startTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;

      values.lastPayTimeStart = values.lastPayTimeStart
        ? values.lastPayTimeStart.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.lastPayTimeEnd = values.lastPayTimeEnd ? values.lastPayTimeEnd.split(' ')[0] + ' 23:59:59' : undefined;

      values.birthdayStart = values.birthdayStart
        ? values.birthdayStart.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.birthdayEnd = values.birthdayEnd ? values.birthdayEnd.split(' ')[0] + ' 23:59:59' : undefined;
      return values;
    },
    // 点击搜索前处理的参数
    handleSearchInfoFn: (values, type) => {
      if (type == 2) {
        columns.data.forEach(item => {
          if (item.sorter) {
            item.sortOrder = null
          }
        })
        searchInfo.value = {}
        setProps({
          searchInfo:{}
        })
        selectedRowKeys.value = []
        selectedRows.value = []
      } else {
        setProps({
          searchInfo: searchInfo.value
        })
      }
      return values;
    },
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    columns:columns.data ,
    actionColumn: {
      width: 320,
      title: '操作',
      dataIndex: 'action',
    },
    useSearchForm: true,
    formConfig: {
      schemas: schemas,
    },
    bordered: true,
    striped: false,
    showIndexColumn: true,
    clickToRowSelect:false,
    rowKey: 'memberId',
    
    // 排序函数
    sortFn: (sortInfo) => {
      columns.data.forEach(item=>{
        if(item.sorter){
          item.sortOrder = null
        }
        if(item.dataIndex==sortInfo.field&&item.sorter){
          if(sortInfo.order){
            item.sortOrder = sortInfo.order
          }else{
            item.sortOrder = null
          }
        }
      })
      if(sortInfo.order){
        searchInfo.value = {sort:sortInfo.column.sorterName,sortType:sortInfo.order.split('end')[0]}
      }else{
        searchInfo.value = {}
      }
      setProps({
        searchInfo:searchInfo.value
      })
    },
  });

  const router = useRouter();
  const width = ref(500);
  const title = ref('');
  const visible = ref(false);
  const content = ref([]);
  const confirmBtnLoading = ref(false);
  const operate_item = ref({});
  const operate_type = ref('');
  const labelList = ref([])

  const content_data = ref([
    {
      type: 'select',
      label: `选择标签`,
      name: 'labelIds',
      placeholder: `请选择标签`,
      mode:'multiple',
      initialValue:undefined,
      selData: [],
      rules: [
        {
          required: true,
          message: `请选择标签`,
        },
      ],
    },
  ]);

  const member_add_edit = ref([
    {
      type: 'input',
      label: `会员名`,
      name: 'memberName',
      placeholder: `请输入会员名`,
      extra: '请输入6-20位中、英文、数字、"-"及"_"，且不能全为数字',
      maxLength: 20,
      showCount: true,
      rules: [
        {
          required: true,
          whitespace: true,
          message: '会员名必填',
        },
        {
          validator: (rule, value) => validatorMem(rule, value),
        },
      ],
      callback: true,
    },
    {
      type: 'input',
      input_type: 'password',
      label: `密码`,
      name: 'loginPwd',
      placeholder: `请设置6～20位字母、数字或符号组成的密码`,
      maxLength: 20,
      showCount: true,
      rules: [
        {
          required: true,
          whitespace: true,
          message: '密码必填 请输入6～20位的密码',
        },
        {
          validator: (rule, value) => validatorMemPwd(rule, value),
        },
      ],
      callback: true,
    },
    {
      type: 'input',
      input_type: 'password',
      label: `确认密码`,
      name: 'confirmPwd',
      placeholder: `请再次输入密码`,
      maxLength: 20,
      showCount: true,
      rules: [
        {
          required: true,
          whitespace: true,
          message: '确认密码必填 请输入6～20位的密码',
        },
        {
          validator: (rule, value) => validatorMemPwd(rule, value),
        },
      ],
      callback: true,
    },
    {
      type: 'input',
      label: `手机号`,
      name: 'memberMobile',
      placeholder: `请输入手机号`,
      maxLength: 11,
      rules: [
        {
          required: true,
          whitespace: true,
          message: '请输入手机号',
        },
        {
          pattern: mobile_reg,
          message: '请输入正确的手机号',
        },
      ],
      callback: true,
    },
    {
      type: 'input',
      label: `邮箱`,
      name: 'memberEmail',
      placeholder: `请输入邮箱`,
      rules: [
        {
          validator: (rule, value) => validatorVendorEmail(rule, value),
        },
      ],
      callback: true,
    },
    {
      type: 'input',
      label: `真实姓名`,
      extra: '最多输入10位',
      name: 'memberTrueName',
      placeholder: `请输入真实姓名`,
      maxLength: 10,
      showCount: true,
      callback: true,
    },
  ]); //添加、编辑会员弹窗数据

  const operate_reset = ref([
    {
      type: 'input',
      input_type: 'password',
      label: `密码`,
      name: 'loginPwd',
      placeholder: `请输入新密码`,
      maxLength: 20,
      showCount: true,
      rules: [
        {
          required: true,
          whitespace: true,
          message: '密码必填 请输入6～20位的密码',
        },
        {
          validator: (rule, value) => validatorMemPwd(rule, value),
        },
      ],
      callback: true,
    },
    {
      type: 'input',
      input_type: 'password',
      label: `确认新密码`,
      name: 'confirmPwd',
      placeholder: `请再次输入新密码`,
      maxLength: 20,
      showCount: true,
      rules: [
        {
          required: true,
          whitespace: true,
          message: '确认新密码必填 请输入6～20位的密码',
        },
        {
          validator: (rule, value) => validatorMemPwd(rule, value),
        },
      ],
      callback: true,
    },
  ]); //重置密码弹窗数据

  // 积分设置数据
  const set_point = ref([
    {
      type: 'show_content',
      label: `会员名`,
      name: 'memberName',
      showCount: true,
      rules: [],
      callback: true,
    },
    {
      type: 'show_content',
      label: `当前积分`,
      name: 'memberIntegral',
      showCount: true,
      rules: [],
      callback: true,
    },
    {
      type: 'radio_select',
      label: `操作类型`,
      name: 'type',
      data: [
        { key: '1', value: `增加` },
        { key: '2', value: `减少` },
      ],
      initialValue: '1',
      callback: true,
    },
    {
      type: 'inputnum',
      label: `积分`,
      name: 'value',
      placeholder: `请输入积分数值`,
      min: 0,
      max: 99999999,
      rules: [
        {
          required: true,
          message: '请输入积分数值',
        },
      ],
      callback: true,
    },
    {
      type: 'textarea',
      label: `备注`,
      name: 'description',
      placeholder: `请输入操作备注，最多15字`,
      maxLength: 15,
      item_height: 106,
      showCount: true,
      rules: [
        {
          required: true,
          whitespace: true,
          message: '请输入操作���注',
        },
      ],
      callback: true,
    },
  ]);

  // 修改余额变更的表单配置
  const balance_change = ref([
    {
      type: 'show_content',
      label: '会员名',
      name: 'memberName',
      content: '',
    },
    {
      type: 'show_content',
      label: '当前余额',
      name: 'balance',
      content: '',
    },
    {
      type: 'radio_select',
      label: '操作类型',
      name: 'type',
      data: [
        { key: '5', value: '打款' },
        { key: '6', value: '扣款' },
      ],
      initialValue: '5',
      callback: true,
    },
    {
      type: 'inputnum',
      label: '打款金额',
      name: 'amount',
      placeholder: '请输入变更金额',
      precision: 2,
      min: 0,
      addonAfter: '元',
      stringMode: true,
      maxLength: 16, // 增加最大长度限制
      rules: [
        {
          required: true,
          message: '请输入变更金额',
        },
      ],
      callback: true,
      style: {},
    },
    {
      type: 'textarea',
      label: '备注',
      name: 'description',
      placeholder: '请输入操作备注，最多200字',
      maxLength: 200,
      item_height: 106,
      showCount: true,
      callback: true,
    },
  ]);

  //表格点击回调事件
  function handleClick(item, type) {
    operate_type.value = type;
    operate_item.value = item ? item : {};
    if (type == 'add') {
      title.value = '新增会员';
      visible.value = true;
      content.value = JSON.parse(JSON.stringify(member_add_edit.value));
      content.value[0].disable = false;
      content.value[0].rules = [
        {
          required: true,
          whitespace: true,
          message: '会员名必填',
        },
        {
          validator: (rule, value) => validatorMem(rule, value),
        },
      ];
      content.value[1].rules = content.value[2].rules = [
        { required: true, whitespace: true, message: '密码必填 请输入6～20位的密码' },
        { validator: (rule, value) => validatorMemPwd(rule, value) },
      ];
      content.value[3].rules = [
        { required: true, whitespace: true, message: '请输入手机号' },
        { pattern: mobile_reg, message: '请输入正确的手机号' },
      ];
      content.value[4].rules = [
        { validator: (rule, value) => validatorVendorEmail(rule, value) },
      ];
      let obj = {
        type: 'verify_code', //验证码
        label: '短信验证码',
        name: 'code',
        verification:true, //为true的话调的是另一个验证码接口
        mobile: 'memberMobile', //关联的手机号字段
        placeholder: '请输入短信验码',
        extra: ``,
        initialValue: '',
        maxLength: 6, //最多字数
        rules: [
          {
            required: true,
            whitespace: true,
            message: '请输入短信验证码',
          },
          {
            // @ts-ignore
            validator: async (rule, value) => {
              await checkSmsCode(rule, value);
            },
            trigger: 'change',
          },
        ],
        callback: true,
      }
      for(let i in content.value){
        if(content.value[i].name == 'memberMobile'){
          content.value.splice(Number(i)+1,0,obj)
          break
        }
      }
    } else if (type == 'edit') {
      title.value = '编辑会员';
      getMemberDetail(item, 'edit');
      content.value[4].rules = [
        { validator: (rule, value) => validatorVendorEmail(rule, value) },
      ];
    } else if (type == 'reset') {
      title.value = '修改会员密码';
      visible.value = true;
      content.value = JSON.parse(JSON.stringify(operate_reset.value));
      content.value[0].rules = [
        { required: true, whitespace: true, message: '密码必填 请输入6～20位的密码' },
        { validator: (rule, value) => validatorMemPwd(rule, value) },
      ];
      content.value[1].rules = [
        { required: true, whitespace: true, message: '确认新密码必填 请输入6～20位的密码' },
        { validator: (rule, value) => validatorMemPwd(rule, value) },
      ];
    } else if (type == 'setPoint') {
      title.value = '积分设置';
      getMemberDetail(item, 'setPoint');
    
    } else if (type == 'check') {
      userStore.setDelKeepAlive(['ListsToDetail']);
      router.push({
        path: '/member/lists_to_detail',
        query: {
          id: item.memberId,
        },
      });
    } else if (type == 'changeBalance') {
      title.value = '余额变更';
      getMemberDetail(item, 'changeBalance');
    }
  }

  const getMemberDetail = (item, type) => {
    visible.value = true;
    let data = [];
    if (type == 'edit') {
      data = JSON.parse(JSON.stringify(member_add_edit.value));
      data[0].disable = true;
      data.map((v, index) => {
        if (index == 1 || index == 2) v.type = 'empty';
        v.initialValue = item[v.name];
        return v;
      });
      data[3].rules = [
        { required: true, whitespace: true, message: '请输入手机号' },
        { pattern: mobile_reg, message: '请输入正确的手机号' },
      ];
      data[4].rules = [{ validator: (rule, value) => validatorVendorEmail(rule, value) }];
    } else if (type == 'setPoint') {
      data = JSON.parse(JSON.stringify(set_point.value));
      data.map((v, index) => {
        v.content = item[v.name];
        return v;
      });
    } else if (type == 'changeBalance') {
      data = JSON.parse(JSON.stringify(balance_change.value));
      data.map((v) => {
        if (v.name === 'memberName') {
          v.content = item.memberName;
        } else if (v.name === 'balance') {
          v.content = `￥${item.balance || 0}`;
        }
        return v;
      });
    }
    content.value = data;
  };

  // 弹窗回调事件
  const handleCallback = (data) => {
    // 处理 radio-change-event
    if (typeof data === 'string') {
      const index = content.value.findIndex(item => item.name === 'type');
      if (index > -1) {
        content.value[index].initialValue = data;
        // 当选择类型变化时，只修改金额字段的标题
        const amountItem = content.value.find(item => item.name === 'amount');
        if (amountItem) {
          // 根据类型设置标题
          amountItem.label = data === '5' ? '打款金额' : '扣款金额';
          // 清空输入值
          amountItem.initialValue = '';
        }
      }
      return;
    }

    // 处理其他回调
    if (data.contentItem && data.contentItem.name === 'amount') {
      // 移除金额限制相关的逻辑
      return;
    }
  };

  //弹窗取消事件
  function handleCancle() {
    visible.value = false;
    content.value = [];
    operate_item.value = '';
    }

  //弹窗确认事件
  function handleConfirm(val) {
    if (operate_type.value == 'add' || operate_type.value == 'edit') {
      if (operate_type.value == 'edit') {
        val.memberId = operate_item.value.memberId;
      } else if (operate_type.value == 'add') {
        val.registerChannel = 5;
      }
      val.loginPwd = base64Encrypt(val.loginPwd);
      val.confirmPwd = base64Encrypt(val.confirmPwd);
      userOperateType(val);
    } else if (operate_type.value == 'reset') {
      val.memberId = operate_item.value.memberId;
      val.loginPwd = base64Encrypt(val.loginPwd);
      val.confirmPwd = base64Encrypt(val.confirmPwd);
      userOperateType(val);
    } else if (operate_type.value == 'setPoint') {
      val.memberId = operate_item.value.memberId;
      delete val.memberIntegral;
      delete val.memberName;
      userOperateType(val);
    } else if (operate_type.value == 'changeBalance') {
      const params = {
        memberId: parseInt(operate_item.value.memberId),
        type: parseInt(val.type),
        amount: val.amount.toString(),
        rechargeDescription: val.description || ''
      };
      userOperateType(params);
    }
  }

  //会员列表操作
  const userOperateType = async (params) => {
    confirmBtnLoading.value = true;
    let res = {};
    if (operate_type.value == 'add') {
      res = await addUser(params);
    } else if (operate_type.value == 'edit') {
      res = await editUser(params);
    } else if (operate_type.value == 'switch') {
      res = await editUserState(params);
    } else if (operate_type.value == 'reset') {
      res = await editPwd(params);
    } else if (operate_type.value == 'setPoint') {
      res = await editMemberIntegral(params);
    } else if (operate_type.value == 'changeBalance') {
      res = await editMemberBalance(params);
    }
    confirmBtnLoading.value = false;
    if (res.state == 200) {
      sucTip(res.msg);
      handleCancle();
      reload();
    } else {
      failTip(res.msg);
    }
  };

  //开关事件
  const handleChange = (val, item) => {
    operate_type.value = 'switch';
    userOperateType({
      memberId: item.memberId,
      state: val ? 1 : 0,
    });
  };

  

  //获取会员等级
  const get_grade_list = async () => {
    let res = await getAdminMemberLevelListApi()
    let temp = schemas.filter((item) => item.field == 'memberLevelId');
    if (temp.length > 0) {
      let selData = [];
      res.data.forEach((item) => {
        if (item.levelId && item.levelName) {
          selData.push({
            key: item.levelId,
            value: item.levelId,
            label: item.levelName
          });
        }
      });
      temp[0].componentProps.options = selData;
    }
  };

  const handleSmsCancle = (info) => {
    }

  onMounted(() => {
    
    get_grade_list();
  });

</script>
<style lang="less" scoped>
  .common_page {
    padding: 20px;
    background: #fff;

    .common_page_edit {
      position: relative;
      margin-right: 5px;
      padding-right: 5px;
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover {
        color: #ff6a12;
      }

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        right: 0;
        width: 1px;
        height: 12px;
        margin-top: -5px;
        background: #ddd;
      }

      &:last-of-type {
        &::after {
          display: none;
        }
      }
    }

    &.common_page_toolbar {
      .vben-basic-table-form-container .ant-form {
        margin-bottom: 0;
      }

      .toolbar {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: 100%;
        height: 42px;
        margin-bottom: 5px;
        background: #ffe3d5;

        .toolbar_btn {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 28px;
          margin-left: 10px;
          padding: 0 7px;
          border-radius: 3px;
          background: #fff;
          cursor: pointer;

          span {
            margin-left: 5px;
          }
        }
      }
    }
  }

  :deep(.amount-input) {
    .ant-input-number {
      width: 100%;
    }

    .ant-input-number-input {
      text-align: left;
      padding-right: 24px;
      color: inherit;
    }

    .ant-input-number-group-addon {
      color: rgba(0, 0, 0, 0.65);
      background-color: #fafafa;
      border: 1px solid #d9d9d9;
      border-left: none;
    }
  }
</style>
