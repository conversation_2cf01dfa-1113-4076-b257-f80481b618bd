<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <SldComHeader title="会员等级管理" />
      <BasicTable
        :bordered="true"
        :showIndexColumn="false"
        :dataSource="data.list"
        :columns="columns"
        :pagination="false"
        :ellipsis="false"
        :actionColumn="{
          title: '操作',
          width: 120,
          dataIndex: 'action',
        }"
      >
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'couponNameList'">
            <div class="flex_column_start_start" v-if="((record.freightCouponNameList && record.freightCouponNameList.length>0) || (text && text.length>0) || (record.integralMultipleValue!=undefined&&record.integralMultipleValue>=0))">
              <div v-if="record.freightCouponNameList && record.freightCouponNameList.length>0" style="position: relative;padding-right: 19px;">运费券：{{ record.freightCouponNameList[0] }}
                <template v-if="(!text||text.length==0) && (record.integralMultipleValue===undefined||record.integralMultipleValue===null)">
                  <Tooltip>
                    <template #title>
                      <div style="margin-bottom: 6px;" v-if="record.freightCouponNameList && record.freightCouponNameList.length>0">
                        <div>运费券：{{ record.freightCouponNameList.join('，') }}</div>
                      </div>
                      <div style="margin-bottom: 6px;" v-if="text && text.length>0">
                        <div>优惠券：{{ text.join('，') }}</div>
                      </div>
                      <div v-if="record.integralMultipleValue!=undefined && record.integralMultipleValue>=0">积分回馈倍数：{{ record.integralMultipleValue }}倍</div>
                    </template>
                    <span style="position: absolute;right: -3px;bottom: -1px;width: 18px;cursor: pointer;">&gt;&gt;</span>
                  </Tooltip>
                </template>
              </div>
              <div style="position: relative;padding-right: 19px;" v-if="text && text.length>0">
                优惠券：{{text[0]}}
                <template v-if="(record.integralMultipleValue==undefined||record.integralMultipleValue==null)">
                  <Tooltip>
                    <template #title>
                      <div style="margin-bottom: 6px;" v-if="record.freightCouponNameList && record.freightCouponNameList.length>0">
                        <div>运费券：{{ record.freightCouponNameList.join('，') }}</div>
                      </div>
                      <div style="margin-bottom: 6px;" v-if="text && text.length>0">
                        <div>优惠券：{{ text.join('，') }}</div>
                      </div>
                      <div v-if="record.integralMultipleValue!=undefined && record.integralMultipleValue>=0">积分回馈倍数：{{ record.integralMultipleValue }}倍</div>
                    </template>
                    <span style="position: absolute;right: -3px;bottom: -1px;width: 18px;cursor: pointer;">&gt;&gt;</span>
                  </Tooltip>
                </template>
              </div>
              <div style="position: relative;padding-right: 19px;" v-if="record.integralMultipleValue!=undefined&&record.integralMultipleValue>=0">
                积分回馈倍数：{{ record.integralMultipleValue }}倍
                <template v-if="((record.freightCouponNameList && record.freightCouponNameList.length>0) || (text && text.length>0))">
                  <Tooltip >
                    <template #title>
                      <div style="margin-bottom: 6px;" v-if="record.freightCouponNameList && record.freightCouponNameList.length>0">
                        <div>运费券：{{ record.freightCouponNameList.join('，') }}</div>
                      </div>
                      <div style="margin-bottom: 6px;" v-if="text && text.length>0">
                        <div>优惠券：{{ text.join('，') }}</div>
                      </div>
                      <div v-if="record.integralMultipleValue!=undefined && record.integralMultipleValue>=0">积分回馈倍数：{{ record.integralMultipleValue }}倍</div>
                    </template>
                    <span style="position: absolute;right: -3px;bottom: -1px;width: 18px;cursor: pointer;">&gt;&gt;</span>
                  </Tooltip>
                </template>
              </div>
            </div>
            <div v-else class="flex_column_start_center">
              --
            </div>
          </template>
          <template v-if="column.dataIndex === 'action'">
            <TableAction
              :actions="[
                {
                  ifShow:record.canEdit==true,
                  label: '编辑',
                  onClick: operate.bind(null, 'edit', record),
                },
                {
                  ifShow:record.canStop==true,
                  label: '停用',
                  onClick: operate.bind(null, 'reset', record),
                },
                {
                  ifShow:record.canSet==true,
                  label: '待配置',
                  onClick: operate.bind(null, 'set', record),
                },
                {
                  ifShow:!(record.canEdit || record.canStop || record.canSet),
                  label: '--',
                },
              ]"
            />
          </template>
        </template>
      </BasicTable>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'MemberSuperGrade',
  };
</script>
<script setup>
  import { ref, onMounted, onUpdated } from 'vue';
  import { useRouter } from 'vue-router';
  import { Tooltip,Modal } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import {getAdminMemberLevelListApi,getAdminMemberLevelDisableApi } from '/@/api/member/super';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { sucTip, failTip } from '@/utils/utils';

  const router = useRouter();
  const data = ref({
    list:[]
  })
  const columns = ref([
    {
      title: `会员等级`,//会员等级
      dataIndex: 'level',
      align: 'center',
      width: 100,
      customRender: ({ text }) => {
        return text ? text : '--';
      },
    },
    {
      title: `等级名称`,//会员等级
      dataIndex: 'levelName',
      align: 'center',
      width: 120,
      customRender: ({ text }) => {
        return text ? text : '--';
      },
    },
    {
      title: `成长值`,//成长值
      dataIndex: 'growthValue',
      align: 'center',
      width: 100,
      customRender: ({ text }) => {
        return text ? text : '--';
      },
    },
    {
      title: `会员权益`,//会员权益
      dataIndex: 'couponNameList',
      align: 'center',
      width: 100,
      customRender: ({ text }) => {
        return text ? text : '--';
      },
    },
    {
      title: `会员数`,//会员数
      dataIndex: 'memberNum',
      align: 'center',
      width: 100,
      customRender: ({ text }) => {
        return text ? text : '--';
      },
    },
  ])
  
   //获取数据列表
  const get_list = async()=> {
    let res = await getAdminMemberLevelListApi({})
    if (res.state == 200) {
      if (res.data.length > 0) {
        for (var i=0; i<res.data.length; i++) {
          res.data[i].canEdit = (res.data[i].state ==2) ? true : false; //编辑按钮
          if (i == 0 && res.data.length>1) {
            res.data[i].preNum = 0;
            res.data[i].nextNum = res.data[i+1].growthValue-1;
          } else if (i == res.data.length-1) {
            res.data[i].preNum = res.data[i-1].growthValue+1;
            res.data[i].nextNum = 9999999;
          } else {
            res.data[i].preNum = res.data[i-1].growthValue+1;
            res.data[i].nextNum = res.data[i+1].growthValue ? res.data[i+1].growthValue-1 : 9999999;
          }
        }
        let stopItem = res.data.filter(item => item.state == 2); //停用按钮
        if (stopItem.length > 0) {
          stopItem[stopItem.length-1].canStop = true;
        } 
        let setItem = res.data.filter(item => item.state != 2); //待配置按钮
        if (setItem.length > 0) {
          setItem[0].canSet = true;
        }
      }
      data.value.list = res.data;
      data.value.pagination = {};
    }
  }

  //操作事件 edit 编辑 reset 停用校验 reset_submit 停用提交 set 配置
  const operate = async(type, item)=> {
    if (type == 'edit') {
      router.push(`/member/super_grade_to_add?id=${item.levelId}&min=${item.preNum}&max=${item.nextNum<0 ? 9999999 : item.nextNum}`);
    } else if (type == 'set') {
      router.push(`/member/super_grade_to_add?id=${item.levelId}&min=${item.preNum}&max=${item.nextNum<0 ? 9999999 : item.nextNum}`);
    } else if (type == 'reset') {
      Modal.confirm({
        title: '提示？',
        content: '是否确认停用该等级？停用后会员等级将被删除，该等级下的会员自动降级至相邻的低等级！请谨慎操作。',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {operate('reset_submit', item) },
      })
    } else if (type == 'reset_submit') {
      let res = await getAdminMemberLevelDisableApi({ levelId: item.levelId })
      if (res.state == 200) {
        sucTip(res.msg);
        get_list();
      } else {
        failTip(res.msg);
      }
    }
  }

  onMounted(() => {
    get_list()
  });

  onUpdated(() => {
    let flag = localStorage.getItem('update_super_grade');
    if (flag) {
      localStorage.removeItem('update_super_grade');
      get_list();
    }
  })
</script>
<style lang="less">
  
</style>
