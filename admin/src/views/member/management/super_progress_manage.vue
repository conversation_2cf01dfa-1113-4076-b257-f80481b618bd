<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <SldComHeader title="成长值管理" />
      <Spin :spinning="initLoading">
        <div class="add_crowd_height full_activity" style="margin-bottom:5px;">
          <Form layout="inline" ref="formRef" :model="setting">
            <!-- 基础任务-start -->
            <CommonTitle text="基础任务" />
            <div style="width: 100%;height: 10px;"></div>
            <div class="full_acm_activity flex_column_start_start">
              <!-- 获得条件 start -->
              <div class="item flex_row_start_start">
                <div class="left"></div>
                <div class="right">
                  <Form.Item
                    name="task_complete_info_enabled"
                    style="width: 500px;"
                  >
                    <div class="flex_row_start_center">
                      <Checkbox v-model:checked="setting.task_complete_info_enabled"
                      >完善信息</Checkbox>
                    </div>
                  </Form.Item>
                  <div class="flex_row_start_center" style="color: #000000A6;font-size: 14px;">
                    <span style="margin-left: 24px;margin-right: 8px;">完成后赠送</span>
                    <InputNumber
                      :min="1"
                      :max="9999999"
                      style="width: 120px !important"
                      :precision="0"
                      v-model:value="setting.task_complete_info_growth_value"
                    />
                    <span style="margin-left:8px;">成长值</span>
                  </div>
                </div>
              </div>
              <!-- 获得条件 end -->
            </div>
            <!-- 基础任务-end -->
            <!-- 消费任务-start -->
            <CommonTitle text="消费任务" style="margin-top: 10px" />
            <div style="width: 100%;height: 10px;"></div>
            <div class="full_acm_activity flex_column_start_start">
              <!-- 购买商品 start -->
              <div class="item flex_row_start_start">
                <div class="left"></div>
                <div class="right">
                  <Form.Item
                    name="task_purchase_enabled"
                    style="width: 500px;"
                  >
                    <div class="flex_row_start_center">
                      <Checkbox v-model:checked="setting.task_purchase_enabled"
                      >购买商品</Checkbox>
                    </div>
                  </Form.Item>
                  <div class="flex_row_start_center" style="color: #000000A6;font-size: 14px;">
                    <span style="margin-left: 24px;margin-right: 8px;">每消费</span>
                    <InputNumber
                      :min="1"
                      :max="9999999"
                      style="width: 120px !important"
                      :precision="0"
                      v-model:value="setting.task_purchase_every"
                    />
                    <span style="margin-left:8px;margin-right: 8px;">元，获得</span>
                    <InputNumber
                      :min="1"
                      :max="9999999"
                      style="width: 120px !important"
                      :precision="0"
                      v-model:value="setting.task_purchase_growth_value"
                    />
                    <span style="margin-left:8px;">成长值</span>
                  </div>
                </div>
              </div>
              <!-- 购买商品 end -->
              <!-- 发布评论 start -->
              <div class="item flex_row_start_start">
                <div class="left"></div>
                <div class="right">
                  <Form.Item
                    name="task_comment_enabled"
                    style="width: 500px;"
                  >
                    <div class="flex_row_start_center">
                      <Checkbox v-model:checked="setting.task_comment_enabled"
                      >发布评论</Checkbox>
                    </div>
                  </Form.Item>
                  <div class="flex_row_start_center" style="color: #000000A6;font-size: 14px;">
                    <span style="margin-left: 24px;margin-right: 8px;">每发布</span>
                    <InputNumber
                      :min="1"
                      :max="9999999"
                      style="width: 120px !important"
                      :precision="0"
                      v-model:value="setting.task_comment_every"
                    />
                    <span style="margin-left:8px;margin-right: 8px;">条评论，获得</span>
                    <InputNumber
                      :min="1"
                      :max="9999999"
                      style="width: 120px !important"
                      :precision="0"
                      v-model:value="setting.task_comment_growth_value"
                    />
                    <span style="margin-left:8px;">成长值</span>
                  </div>
                </div>
              </div>
              <!-- 发布评论 end -->
            </div>
            <!-- 消费任务-end -->
             <!-- 日常任务-start -->
             <CommonTitle text="日常任务" style="margin-top: 10px" />
            <div style="width: 100%;height: 10px;"></div>
            <div class="full_acm_activity flex_column_start_start">
              <!-- 登录 start -->
              <div class="item flex_row_start_start" v-for="(item,index) in task_sign" :key="index">
                <div class="left"></div>
                <div class="right">
                  <Form.Item
                    name="task_login_enabled"
                    style="width: 500px;"
                  >
                    <div class="flex_row_start_center">
                      <Checkbox v-model:checked="setting[item.check_value]"
                      >{{ item.title }}</Checkbox>
                    </div>
                  </Form.Item>
                  <div class="flex_row_start_center" style="color: #000000A6;font-size: 14px;">
                    <span style="margin-left: 24px;margin-right: 8px;">{{ item.content }}</span>
                    <InputNumber
                      :min="item.min"
                      :max="item.max"
                      style="width: 120px !important"
                      :precision="0"
                      v-model:value="setting[item.number_value]"
                    />
                    <span style="margin-left:8px;">{{ item.content_one }}</span>
                  </div>
                </div>
              </div>
              <!-- 登录 end -->
            </div>
            <!-- 日常任务-end -->
            <!-- 风险管理-start -->
            <CommonTitle text="风险管理" style="margin-top: 10px" />
            <div style="width: 100%;height: 10px;"></div>
            <div class="full_acm_activity flex_column_start_start">
              <!-- 获得条件 start -->
              <div class="item flex_row_start_start">
                <div class="left"></div>
                <div class="right">
                  <Form.Item
                    name="growth_value_max"
                    style="width: 500px;"
                  >
                    <div class="flex_row_start_center" style="color: #000000A6;font-size: 14px;">
                      <span style="margin-left: 24px;margin-right: 8px;">每日最多可获得</span>
                      <InputNumber
                        :min="1"
                        :max="9999999"
                        style="width: 120px !important"
                        :precision="0"
                        v-model:value="setting.growth_value_max"
                      />
                      <span style="margin-left:8px;">成长值</span>
                    </div>
                  </Form.Item>
                </div>
              </div>
              <!-- 获得条件 end -->
            </div>
            <!-- 风险管理-end -->
          </Form>
        </div>
        <div
            class="m_diy_bottom_wrap"
            :style="{ position: 'fixed', left: getRealWidth + 10 + 'px' }"
          >
            <div v-if="query.type != 'view'" class="add_goods_bottom_btn add_goods_bottom_btn_sel" @click="handleSaveAllData">
              保存并生效
            </div>
          </div>
      </Spin>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'MemberSuperProgress',
  };
</script>
<script setup>
  import { ref, onMounted } from 'vue';
  import { useRouter,useRoute } from 'vue-router';
  import CommonTitle from '/@/components/CommonTitle/index.vue';
  import { Spin,InputNumber,Checkbox ,Form} from 'ant-design-vue';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import {getMemberSettingGetListApi,getMemberSettingUpdateListApi} from '/@/api/member/super';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { sucTip, failTip } from '@/utils/utils';

  const { getRealWidth } = useMenuSetting();
  const route = useRoute();
  const initLoading = ref(false)

  const formRef = ref()
  const query = ref(route.query)
  const setting = ref({}) //设置信息

  const task_sign = ref([
    {
      title:'登录',
      check_value:'task_login_enabled',
      content:'每日登录可获得',
      content_one:'成长值',
      number_value:'task_login_growth_value',
      min:1,
      max:9999999
    },
    {
      title:'签到',
      check_value:'task_sign_enabled',
      content:'每日签到可获得',
      content_one:'成长值',
      number_value:'task_sign_growth_value',
      min:1,
      max:9999999
    },
  ])

  const router = useRouter();

  //获取成长值信息
  const get_super_progress_setting  = async()=> {
    let res = await getMemberSettingGetListApi({ str: 'growth_value_max,task_complete_info_enabled,task_complete_info_growth_value,task_purchase_enabled,task_purchase_every,task_purchase_growth_value,'+'task_comment_enabled,task_comment_every,task_comment_growth_value,task_login_enabled,task_login_growth_value,task_sign_enabled,task_sign_growth_value,' })
    if (res.state == 200 && res.data && res.data.length) {
      res.data.map(item => {
        if (item.name.indexOf('_enable') != -1) {
          setting.value[item.name] = (item.value == 1 || item.value == 'true') ? 1 : 0;
        } else {
          setting.value[item.name] = item.value;
        }
      })
    } else {
      failTip(res.msg);
    }
  }

  //保存
  const handleSaveAllData = ()=> {
    formRef.value.validate().then(async (values) => {
      if(initLoading.value){
        return
      }
      let params = { ...setting.value };
      for (var key in params) {
        if (key.indexOf('_enable') != -1) {
          params[key] = params[key] ? 1 : 0;
        }
      }
      if (params.task_complete_info_enabled == 1 && !params.task_complete_info_growth_value) {
        failTip('请输入完善信息赠送的成长值');
        return;
      } else if (params.task_purchase_enabled == 1 && (!params.task_purchase_every || !params.task_purchase_growth_value)) {
        if (!params.task_purchase_every) {
          failTip('请输入购买商品的消费金额');
          return;
        } else {
          failTip('请输入购买商品的成长值');
          return;
        }
      } else if (params.task_comment_enabled == 1 && (!params.task_comment_every || !params.task_comment_growth_value)) {
        if (!params.task_comment_every) {
          failTip('请输入发布评论的发布条数');
          return;
        } else {
          failTip('请输入发布评论的成长值');
          return;
        }
      } else if (params.task_login_enabled == 1 && !params.task_login_growth_value) {
        failTip('请输入登录的成长值');
        return;
      } else if (params.task_sign_enabled == 1 && !params.task_sign_growth_value) {
        failTip('请输入签到的成长值');
        return;
      } else if (!params.growth_value_max) {
        failTip('请输入最多可获得的成长值');
        return;
      }
      initLoading.value = true
      let res = await getMemberSettingUpdateListApi(params)
      initLoading.value = false
      if (res.state == 200) {
          sucTip(res.msg);
        } else {
          failTip(res.msg);
        }
    })
  }
  onMounted(() => {
    get_super_progress_setting()
  });
</script>
<style lang="less">
  @import '/@/assets/css/promotion.less';
</style>
