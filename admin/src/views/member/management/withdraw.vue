<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <SldComHeader :type="2" :title="'提现管理'" />
      <a-tabs v-model:activeKey="tabIndex" type="card" @change="handleTab">
        <a-tab-pane key="1" tab="基本设置" />
        <a-tab-pane key="2" tab="提现明细" />
      </a-tabs>
      <div class="section_padding_tab_top"></div>
      <template v-if="tabIndex == '1'">
        <div class="section_padding_set_scroll">
          <StandardTableRow
            width="100%"
            :data="basicSettings"
            @callback-event="callbackEvent"
            @submit-event="submitEvent"
          />
        </div>
      </template>
      <template v-else>
        <BasicTable @register="standardTable" style="padding: 0">
          <template #tableTitle>
            <div class="toolbar">
              <template v-if="checkedKeys.length == 0">
                <div class="toolbar_btn" @click="handleClick(null, 'pass')">
                  <AliSvgIcon
                    iconName="iconshenhetongguo1"
                    width="15px"
                    height="15px"
                    fillColor="#0fb39a"
                  />
                  <span>批量通过</span>
                </div>
              </template>
              <template v-else>
                <Popconfirm title="确认通过该提现申请吗？" @confirm="handleClick(null, 'pass')">
                  <div class="toolbar_btn">
                    <AliSvgIcon
                      iconName="iconshenhetongguo1"
                      width="15px"
                      height="15px"
                      fillColor="#0fb39a"
                    />
                    <span>批量通过</span>
                  </div>
                </Popconfirm>
              </template>
              <div class="toolbar_btn" @click="handleClick(null, 'refuse')">
                <AliSvgIcon
                  iconName="iconshenhejujue1"
                  width="15px"
                  height="15px"
                  fillColor="#ff0f3c"
                />
                <span>批量拒绝</span>
              </div>
            </div>
          </template>
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.key == 'action'">
              <template v-if="record.state == 1">
                <Popconfirm title="确认通过该提现申请吗？" @confirm="handleClick(record, 'pass')">
                  <span class="common_page_edit">通过</span>
                </Popconfirm>
                <span class="common_page_edit" @click="handleClick(record, 'refuse')">拒绝</span>
              </template>
              <template v-else>--</template>
            </template>
            <template v-else-if="column.key == 'cashId'"> 11 </template>
            <template v-else-if="column.key">
              {{ text ? text : '--' }}
            </template>
          </template>
        </BasicTable>
        <SldModal
          :width="width"
          :title="title"
          :visible="visible"
          :content="content"
          :confirmBtnLoading="confirmBtnLoading"
          :showFoot="true"
          @cancle-event="handleCancle"
          @confirm-event="handleConfirm"
        />
      </template>
    </div>
  </div>
</template>
<script>
  import { defineComponent, ref, onMounted } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import SldModal from '@/components/SldModal/index.vue';
  import StandardTableRow from '/@/components/StandardTableRow/index.vue';
  import { Tabs, Popconfirm } from 'ant-design-vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import {
    getSettingList,
    updateSettingList,
    withdrawList,
    withdrawAudit,
  } from '/@/api/member/member';
  import { sucTip, failTip } from '/@/utils/utils';

  export default defineComponent({
    name: 'MemberWithraw',
    components: {
      SldComHeader,
      [Tabs.name]: Tabs,
      [Tabs.TabPane.name]: Tabs.TabPane,
      StandardTableRow,
      BasicTable,
      SldModal,
      Popconfirm,
    },
    setup() {
      const checkedKeys = ref([]);
      const [standardTable, { reload }] = useTable({
        api: (arg) => withdrawList({ ...arg }),
        // 点击搜索前处理的参数
        beforeFetch(values) {
          values.startTime = values.startTime
            ? values.startTime.split(' ')[0] + ' 00:00:00'
            : undefined;
          values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
          return values;
        },
        fetchSetting: {
          pageField: 'current',
          sizeField: 'pageSize',
          listField: 'data.list',
          totalField: 'data.pagination.total',
        },
        columns: [
          {
            title: '提现单号',
            dataIndex: 'cashSn',
            width: 130,
          },
          {
            title: '交易流水号',
            dataIndex: 'transactionSn',
            width: 250,
          },
          {
            title: '提现方式',
            dataIndex: 'receiveBank',
            width: 100,
          },
          {
            title: '申请时间',
            dataIndex: 'applyTime',
            width: 150,
          },
          {
            title: '会员名',
            dataIndex: 'memberName',
            width: 100,
          },
          {
            title: '收款人',
            dataIndex: 'receiveName',
            width: 100,
          },
          {
            title: '手机号',
            dataIndex: 'memberMobile',
            width: 100,
          },
          {
            title: '提现金额(¥)',
            dataIndex: 'cashAmount',
            width: 100,
          },
          {
            title: '手续费(¥)',
            dataIndex: 'serviceFee',
            width: 100,
          },
          {
            title: '状态',
            dataIndex: 'stateValue',
            width: 100,
          },
          {
            title: '拒绝理由',
            dataIndex: 'refuseReason',
            width: 100,
          },
          {
            title: '提现失败理由',
            dataIndex: 'failReason',
            width: 100,
          },
          {
            title: '操作人',
            dataIndex: 'adminName',
            width: 100,
          },
        ],
        actionColumn: {
          width: 150,
          title: '操作',
          dataIndex: 'action',
        },
        useSearchForm: true,
        formConfig: {
          schemas: [
            {
              field: 'cashSn',
              component: 'Input',
              colProps: { span: 6, style: 'width:220px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 150,
                placeholder: '请输入提现单号',
                size: 'default',
              },
              label: '提现单号',
              labelWidth: 70,
            },
            {
              field: '[startTime, endTime]',
              component: 'RangePicker',
              colProps: { span: 6, style: 'width:290px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 220,
                format: 'YYYY-MM-DD',
                placeholder: ['开始时间', '结束时间'],
                size: 'default',
              },
              label: '申请时间',
              labelWidth: 70,
            },
            {
              field: 'memberName',
              component: 'Input',
              colProps: { span: 6, style: 'width:220px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 150,
                maxlength: 10,
                placeholder: '请输入会员名',
                size: 'default',
              },
              label: '会员名',
              labelWidth: 70,
            },
            {
              field: 'receiveName',
              component: 'Input',
              colProps: { span: 6, style: 'width:220px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 150,
                maxlength: 10,
                placeholder: '请输入收款人',
                size: 'default',
              },
              label: '收款人',
              labelWidth: 70,
            },
            {
              field: 'state',
              component: 'Select',
              colProps: { span: 6, style: 'width:220px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 150,
                placeholder: '请选择审核状态',
                size: 'default',
                defaultValue: '1',
                options: [
                  { key: 0, label: '全部', value: '' },
                  { key: 1, label: '待处理', value: '1' },
                  { key: 2, label: '已完成', value: '2' },
                  { key: 2, label: '已拒绝', value: '3' },
                  { key: 2, label: '提现失败', value: '4' },
                ],
              },
              label: '状态',
              labelWidth: 70,
            },
            {
              field: 'receiveType',
              component: 'Select',
              colProps: { span: 6, style: 'width:220px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 150,
                placeholder: '请选择提现方式',
                size: 'default',
                defaultValue: 'ALIPAY',
                options: [
                  { key: 0, label: '全部', value: '' },
                  { key: 1, label: '微信', value: 'WXPAY' },
                  { key: 2, label: '支付宝', value: 'ALIPAY' },
                ],
              },
              label: '提现方式',
              labelWidth: 70,
            },
          ],
        },
        bordered: true,
        striped: false,
        showIndexColumn: true,
        rowKey: 'cashId',
        rowSelection: {
          type: 'checkbox',
          selectedRowKeys: checkedKeys,
          getCheckboxProps(record) {
            // 审核状态不等于1的时候禁止选中
            if (record.state !== 1) {
              return { disabled: true };
            } else {
              return { disabled: false };
            }
          },
          onSelect: onSelect,
          onSelectAll: onSelectAll,
        },
        clickToRowSelect: false,
      });

      const tabIndex = ref('2'); //tab下标
      const choose_row = ref({}); //选择的项
      const choose_type = ref(''); //选择的类型
      const width = ref(500);
      const title = ref('');
      const visible = ref(false);
      const content = ref([]);
      const confirmBtnLoading = ref(false);
      const basicSettings = ref([]); //基本设置配置

      // 拒绝提现弹窗内容
      const refuse_content = ref([
        {
          type: 'textarea',
          label: `拒绝理由`,
          name: 'refuseReason',
          placeholder: `请输入拒绝理由`,
          extra: '最多输入100字',
          maxLength: 100,
          showCount: true,
          rules: [
            {
              required: true,
              whitespace: true,
              message: '请输入拒绝理由',
            },
          ],
          callback: true,
        },
      ]);

      const handleTab = (e) => {
        if (e == 1) {
          getSettingInfo();
        } else {
        }
      };

      function onSelect(record, selected) {
        let data = [];
        if (selected) {
          data = [...checkedKeys.value, record.cashId];
        } else {
          data = checkedKeys.value.filter((cashId) => cashId !== record.cashId);
        }
        checkedKeys.value = data;
      }

      function onSelectAll(selected, selectedRows, changeRows) {
        let data = [];
        const changeIds = changeRows.map((item) => item.cashId);
        if (selected) {
          data = [...checkedKeys.value, ...changeIds];
        } else {
          data = checkedKeys.value.filter((cashId) => {
            return !changeIds.includes(cashId);
          });
        }
        checkedKeys.value = data;
      }

      const callbackEvent = (e) => {
        if (e.contentItem && e.contentItem.key == 'withdraw_wxpay_is_enable') {
          let v3_temp = basicSettings.value.filter((item) => item.key == 'wx_transfer_api_v3_key');
          if (v3_temp.length > 0) {
            v3_temp[0].hidden = e.val1 ? false : true;
          }
          let temp = basicSettings.value.filter((item) => item.key == 'wx_max_withdraw_amount');
          if (temp.length > 0) {
            temp[0].hidden = e.val1 ? false : true;
          }
        }

        if(e.contentItem && e.contentItem.key == 'withdraw_is_enable'){
          basicSettings.value.forEach(item=>{
            if(item.key !='withdraw_is_enable' && item.label != '功能设置'&&item.type != 'button'){
              item.hidden =  e.val1 ? false : true;
            }
          })
        }
      };

      const submitEvent = async (params) => {
        params.withdraw_alipay_is_enable = params.withdraw_alipay_is_enable ? '1' : '0';
        params.withdraw_is_enable = params.withdraw_is_enable ? '1' : '0';
        params.withdraw_wxpay_is_enable = params.withdraw_wxpay_is_enable ? '1' : '0';
        if (
          params.withdraw_is_enable == 1 &&
          params.withdraw_alipay_is_enable == 0 &&
          params.withdraw_wxpay_is_enable == 0
        ) {
          failTip('需开启至少一种提现方式～');
          return false;
        } else if (
          params.withdraw_wxpay_is_enable == 1 &&
          params.wx_max_withdraw_amount <= (params.min_withdraw_amount || 0)
        ) {
          failTip('最高提现金额必须大于最低提现金额');
          return false;
        }
        let res = await updateSettingList(params);
        if (res.state == 200) {
          sucTip(res.msg);
        } else {
          getSettingInfo();
          failTip(res.msg);
        }
      };

      // 获取配置信息
      const getSettingInfo = async () => {
        let params = {
          str: 'withdraw_is_enable,withdraw_alipay_is_enable,withdraw_wxpay_is_enable,withdraw_fee,min_withdraw_amount,wx_transfer_api_v3_key,wx_max_withdraw_amount',
        };
        const res = await getSettingList(params);
        const resData = res.data;
        basicSettings.value = [
          {
            type: 'title',
            label: '功能设置',
          },
          {
            require: true,
            type: 'switch',
            label: '提现开关',
            key: resData[0].name,
            width: 300,
            desc_width: 300,
            initValue: '',
            value: resData[0].value == '1' ? true : false,
            callback: true,
            desc: resData[0].desc ? resData[0].desc : '开关开启后，用户可以申请余额提现。',
          },
          {
            type: 'line',
            height: 10,
            hidden:resData[0].value == '1' ? false : true,
          },
          {
            type: 'title',
            label: '提现方式设置',
            hidden:resData[0].value == '1' ? false : true,
          },
          {
            type: 'switch',
            require: true,
            label: '支付宝提现',
            key: resData[1].name,
            width: 300,
            desc_width: 300,
            initValue: '',
            value: resData[1].value == '1' ? true : false,
            callback: true,
            hidden:resData[0].value == '1' ? false : true,
            desc: resData[1].desc
              ? resData[1].desc
              : '开关开启后，用户可以申请余额提现至支付宝账户。',
          },
          {
            require: true,
            type: 'switch',
            label: '微信提现',
            key: resData[2].name,
            width: 300,
            desc_width: 300,
            initValue: '',
            value: resData[2].value == '1' ? true : false,
            callback: true,
            hidden:resData[0].value == '1' ? false : true,hidden:resData[0].value == '1' ? false : true,
            desc: resData[2].desc
              ? resData[2].desc
              : '开关开启后，用户可以申请余额提现至微信零钱。',
          },
          {
            type: 'line',
            height: 10,
            hidden:resData[0].value == '1' ? false : true,
          },
          {
            type: 'title',
            label: '详细设置',
            hidden:resData[0].value == '1' ? false : true,
          },
          {
            type: 'inputnum',
            label: '提现手续费',
            key: resData[3].name,
            width: 300,
            desc_width: 300,
            min: 0,
            max: 50,
            precision: 2,
            placeholder: '请输入' + resData[3].title,
            value: resData[3].value,
            callback: true,
            hidden:resData[0].value == '1' ? false : true,
            desc: resData[3].desc ? resData[3].desc : '提现手续费比例，0或不填代表不收取手续费。',
            input_after_text: '%',
          },
          {
            type: 'inputnum',
            label: '最低提现金额',
            key: resData[4].name,
            width: 300,
            desc_width: 300,
            min: 0,
            max: 100000,
            precision: 2,
            placeholder: '请输入' + resData[4].title,
            value: resData[4].value,
            callback: true,
            hidden:resData[0].value == '1' ? false : true,
            desc: resData[4].desc ? resData[4].desc : '0或不填代表不设置最低提现金额。',
            input_after_text: '元',
          },
          {
            hidden: resData[2].value == '1' ? false : true,
            type: 'input',
            label: '微信商户APIV3密钥',
            key: resData[5].name,
            width: 300,
            desc_width: 300,
            maxlength: 100,
            placeholder: '请输入' + resData[5].title,
            value: resData[5].value,
            callback: true,
            hidden:resData[0].value == '1' ? false : true,
            desc: resData[5].desc ? resData[5].desc : '微信商户APIV3密钥，必填。',
            input_after_text: '元',
            require: true,
            rules: [
              {
                required: true,
                whitespace: true,
                message: '请输入' + resData[5].title,
              },
            ],
          },
          {
            hidden: resData[2].value == '1' ? false : true,
            type: 'inputnum',
            label: '微信单笔最高提现金额',
            key: resData[6].name,
            width: 300,
            desc_width: 300,
            min: 0,
            max: 200,
            precision: 2,
            placeholder: '请输入' + resData[6].title,
            value: resData[6].value,
            callback: true,
            hidden:resData[0].value == '1' ? false : true,
            desc: resData[6].desc ? resData[6].desc : '单笔提现金额最高不超过200元。',
            input_after_text: '元',
            require: true,
            rules: [
              {
                required: true,
                message: '请输入' + resData[6].title,
              },
            ],
          },
          {
            type: 'button',
            width: 300,
            showSubmit: true,
            submitText: '保存',
            showCancle: false,
          },
        ];
      };

      //点击回调事件
      const handleClick = (val, type) => {
        choose_row.value = val ? val : {};
        choose_type.value = type;
        if (val == null && checkedKeys.value.length == 0) return failTip('请先选中数据');
        if (type == 'refuse') {
          title.value = '拒绝理由';
          visible.value = true;
          content.value = JSON.parse(JSON.stringify(refuse_content.value));
        } else if (type == 'pass') {
          const params = {
            cashIds: val == null ? checkedKeys.value.join(',') : val.cashId,
            isPass: true,
          };
          OperateType(params);
        }
      };

      //弹窗确认事件
      const handleConfirm = (val) => {
        if (choose_type.value == 'refuse') {
          val.cashIds = choose_row.value.cashId
            ? choose_row.value.cashId
            : checkedKeys.value.join(',');
          val.isPass = false;
        }
        OperateType(val);
      };

      const OperateType = async (params) => {
        let res = await withdrawAudit(params);
        if (res.state == 200) {
          sucTip(res.msg);
          checkedKeys.value = [];
          handleCancle();
          reload();
        } else {
          failTip(res.msg);
        }
      };

      //弹窗取消事件
      const handleCancle = () => {
        visible.value = false;
        content.value = [];
        choose_row.value = {};
        choose_type.value = '';
      };

      onMounted(() => {
        getSettingInfo();
      });

      return {
        tabIndex,
        basicSettings,
        width,
        title,
        visible,
        content,
        confirmBtnLoading,
        checkedKeys,
        handleTab,
        callbackEvent,
        submitEvent,
        standardTable,
        handleClick,
        handleCancle,
        handleConfirm,
        onSelect,
        onSelectAll,
      };
    },
  });
</script>
<style lang="less" scoped>
  .common_page {
    padding: 20px;
    background: #fff;

    .common_page_edit {
      position: relative;
      margin-right: 5px;
      padding-right: 5px;
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover {
        color: #ff6a12;
      }

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        right: 0;
        width: 1px;
        height: 12px;
        margin-top: -5px;
        background: #ddd;
      }

      &:last-of-type {
        &::after {
          display: none;
        }
      }
    }

    &.authority_group {
      .vben-basic-table-form-container .ant-form {
        margin-bottom: 0;
      }
    }

    .toolbar {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      width: 100%;
      height: 42px;
      margin-bottom: 5px;
      background: #ffe3d5;

      .toolbar_btn {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 28px;
        margin-left: 10px;
        padding: 0 7px;
        border-radius: 3px;
        background: #fff;
        cursor: pointer;

        span {
          margin-left: 5px;
        }
      }
    }
  }
</style>
