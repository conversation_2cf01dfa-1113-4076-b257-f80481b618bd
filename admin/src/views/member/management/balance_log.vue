<template>
  <div class="section_padding">
    <div class="section_padding_back_add">
      <SldComHeader :type="1" :title="'资金明细'" />
      <BasicTable @register="standardTable" style="padding: 0">
        <template #tableTitle>
          <div class="toolbar">
            <div class="toolbar_btn" @click="handleClick('export')">
              <AliSvgIcon iconName="iconziyuan52" width="15px" height="15px" fillColor="#2ea9ff" />
              <span>导出Excel</span>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.key == 'changeValue'">
            <span :style="{ color: record.state == 1 ? '#10D154' : '#F30606' }"
              >{{ record.state == 1 ? '+' : '-' }}{{ record.changeValue }}</span
            >
          </template>
          <template v-else-if="column.key">
            {{ text ? text : text == 0 ? text : '--' }}
          </template>
        </template>
      </BasicTable>
    </div>
  </div>
</template>
<script>
  import { defineComponent } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import { Popconfirm } from 'ant-design-vue';
  import { getBalanceLog, balanceLogExport } from '/@/api/member/member';

  export default defineComponent({
    name: 'MemberBalanceLog',
    components: {
      SldComHeader,
      BasicTable,
      Popconfirm,
    },
    setup() {
      const [standardTable] = useTable({
        api: (arg) => getBalanceLog({ ...arg }),
        // 点击搜索前处理的参数
        beforeFetch(values) {
          values.startTime = values.startTime
            ? values.startTime.split(' ')[0] + ' 00:00:00'
            : undefined;
          values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
          return values;
        },
        fetchSetting: {
          pageField: 'current',
          sizeField: 'pageSize',
          listField: 'data.list',
          totalField: 'data.pagination.total',
        },
        columns: [
          {
            title: '会员名',
            dataIndex: 'memberName',
            width: 100,
          },
          {
            title: '变动金额(元)',
            dataIndex: 'changeValue',
            width: 100,
          },
          {
            title: '冻结金额(元)',
            dataIndex: 'freezeValue',
            width: 100,
          },
          {
            title: '当前总金额(元)',
            dataIndex: 'afterChangeAmount',
            width: 100,
          },
          {
            title: '当前冻结金额(元)',
            dataIndex: 'freezeAmount',
            width: 100,
          },
          {
            title: '变更时间',
            dataIndex: 'createTime',
            width: 100,
          },
          {
            title: '操作管理员',
            dataIndex: 'adminName',
            width: 100,
          },
          {
            title: '操作描述',
            dataIndex: 'description',
            width: 100,
          },
        ],
        useSearchForm: true,
        formConfig: {
          schemas: [
            {
              field: 'memberName',
              component: 'Input',
              colProps: { span: 6, style: 'width:250px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 150,
                maxlength: 10,
                placeholder: '请输入会员名',
                size: 'default',
              },
              label: '会员名',
              labelWidth: 80,
            },
            {
              field: 'adminName',
              component: 'Input',
              colProps: { span: 6, style: 'width:250px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 150,
                maxLength: 11,
                placeholder: '请输入管理员名字',
                size: 'default',
              },
              label: '操作人',
              labelWidth: 80,
            },
            {
              field: '[startTime, endTime]',
              component: 'RangePicker',
              colProps: { span: 6, style: 'width:300px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 220,
                format: 'YYYY-MM-DD',
                placeholder: ['开始时间', '结束时间'],
                size: 'default',
              },
              label: '变更时间',
              labelWidth: 80,
            },
          ],
        },
        bordered: true,
        striped: false,
        showIndexColumn: true,
      });

      const handleClick = async (type) => {
        let params = { fileName: '资金明细导出', type };
        let res = await balanceLogExport(params);
      };

      return {
        standardTable,
        handleClick,
      };
    },
  });
</script>
<style lang="less">
  .common_page {
    padding: 20px;
    background: #fff;

    .common_page_edit {
      position: relative;
      margin-right: 5px;
      padding-right: 5px;
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover {
        color: #ff6a12;
      }

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        right: 0;
        width: 1px;
        height: 12px;
        margin-top: -5px;
        background: #ddd;
      }

      &:last-of-type {
        &::after {
          display: none;
        }
      }
    }

    &.common_page_toolbar {
      .vben-basic-table-form-container .ant-form {
        margin-bottom: 0;
      }

      .toolbar {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: 100%;
        height: 42px;
        margin-bottom: 5px;
        background: #ffe3d5;

        .toolbar_btn {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 28px;
          margin-left: 10px;
          padding: 0 7px;
          border-radius: 3px;
          background: #fff;
          cursor: pointer;

          span {
            margin-left: 5px;
          }
        }
      }
    }
  }
</style>
