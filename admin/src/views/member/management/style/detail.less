p{
  margin-bottom: 0;
}
.lists_to_detail_member{
  padding:10px 0;
  .lists_to_detail_member_padd{
    padding:0 10px;
  }
  .common_title {
    font-size: 14px;
    color: #333;
    margin: 20px 0 10px 0;
  }
  
  .basic_info {
    border: 1px solid #f2f2f2;
    border-radius: 6px;
    display: flex;
    align-items: center;
    padding: 20px;
    padding-left: 0;
    box-sizing: border-box;
  
    .item_text {
      color: #333;
    }
  
    .basic_portrait {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 190px;
  
      img {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        display: block;
        margin-bottom: 17px;
      }
    }
  
    .basic_introduce_wrap {
      flex: 1;
      padding-left: 20px;
      border-left: 1px solid #f2f2f2;
      box-sizing: border-box;
  
      .basic_introduce {
        display: flex;
        background: #FCF9F7;
        padding: 10px 0;
        flex: 1;
  
        .introduce_item {
          flex: 1;
          border-right: 1px solid rgba(0, 0, 0, 0.05);
          padding: 0 20px;
          color: #666;
  
          &:last-of-type {
            border: none;
          }
  
          p {
            height: 28px;
            line-height: 28px;
          }
  
        }
      }
  
      .basic_row {
        margin-top: 10px;
        border-top: 1px solid rgba(248, 248, 248, 1);
        display: flex;
        justify-content: space-between;
  
        p {
          color: #666;
          margin-top: 15px;
          flex: 1;
        }
      }
    }
  
  }
  
  .trading_info {
    border: 1px solid #f2f2f2;
    border-radius: 3px;
  
    .trading_row {
      display: flex;
      justify-content: space-between;
      padding: 5px 0;
  
      &:first-of-type {
        background: #FFFAF7;
        color: #333;
  
      }
  
      &:last-of-type {
        height: 52px;
        align-items: center;
        color: #666;
  
      }
  
      .trading_item {
        height: 30px;
        line-height: 30px;
        box-sizing: border-box;
        flex: 1;
        text-align: center;
        font-weight: bold;
        font-size: 14px;
      }
    }
  }
  
  @baseWidth: 1920px;
  
  //自适应高度
  .calc_height(@w,@h) {
    @scale: @w / @h;
    height: (@w / @baseWidth) * 100vw / @scale;
  }
  
  .label_assets_info {
    background-color: #F5F5F5;
    padding-top: 10px;
    .label_assets_info_left,
    .label_assets_info_right {
      flex: 1;
      min-height: 530px;
      background-color: #FFFFFF;
      padding: 0 10px;
      .common_title {
        margin-top: 10px;
      }
    }
    .label_assets_info_left {
      margin-right: 5px;
      .label_info {
        .label_left,
        .label_right {
          flex: 1;
          .label_info_scroll{
            overflow: auto;
            height: 456px;
          }
          .label_title {
            padding-left: 2rem;
            line-height: 34px;
            span {
              font-size: 12px;
              margin-left: 10px;
              padding-top: 2px;
              color: #fa6f1e;
              cursor: pointer;
            }
          }
          .label_item {
            padding-left: 4rem;
            line-height: 34px;
            &:hover {
              .svg {
                display: block;
              }
              .svg_block{
                display: none;
              }
            }
            .svg {
              display: none;
              margin-right: 20px;
            }
            .svg_block{
              width: 14px;
              height: 14px;
            }
          }
        }
      }
    }
    .label_assets_info_right {
      margin-left: 5px;
      .assets_info {
        .assets_item {
          // width: 63%;
          // .calc_height(555, 190);
          width: 414px;
          height: 152px;
          position: relative;
          background-repeat: no-repeat;
          &:nth-of-type(1) {
            background-size: contain;
          }
  
          &:nth-of-type(2) {
            background-size: contain;
          }
  
          &:nth-of-type(3) {
            background-size: contain;
          }
  
          p {
            position: absolute;
            color: #fff;
            font-size: 40px;
            font-weight: bold;
            font-family: DIN Black;
            top: 49%;
            left: 7%;
            width: 66%;
            overflow: hidden;
            text-overflow: ellipsis;
            transform: translateY(-30%);
          }
        }
      }
    }
  }
}
