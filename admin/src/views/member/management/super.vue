<template>
  <div class="section_padding sensitive_setting_tabs">
    <div class="section_padding_back_add">
      <SldComHeader title="超级会员" />
      <Tabs type="card" v-model:activeKey="activeKey">
        <TabPane key="1" tab="超级会员列表">
          <SuperMemberList  class="section_padding_tab_top"/>
        </TabPane>
        <!-- <TabPane key="2" tab="会员价商品设置">
          <SuperGoodsSet  class="section_padding_tab_top"/>
        </TabPane> -->
        <TabPane key="3" tab="超级会员设置">
          <SuperMemberSet  class="section_padding_tab_top"/>
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'MemberSuper',
  };
</script>
<script setup>
  import { ref, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { Tabs, TabPane, message, RadioGroup, RadioButton } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import SuperMemberList from './super_lists.vue';
  import SuperGoodsSet from './super_goods_set.vue';
  import SuperMemberSet from './super_member_set.vue';

  const router = useRouter();

  const activeKey = ref('1');

  onMounted(() => {});
</script>
<style lang="less">
  
</style>
