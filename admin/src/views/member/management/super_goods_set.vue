<template>
  <div class="sensitive_setting_setting super_goods">
    <div class="full_activity">
      <div class="flex_row_start_center item" style="margin-bottom: 10px;">
        <div class="left" style="width: 90px;font-size: 13px;color: #555;margin-left: 15px;margin-right: 10px;">会员价商品:</div>
        <div class="right">
          <Switch
            @change="(val) => handleChange(val, 'super_goods_is_enable')"
            :checked="setting.super_goods_is_enable"
            :un-checked-value="0"
            :checked-value="1"
          />
        </div>
      </div>
      <div class="flex_row_start_center item" style="margin-bottom: 10px;">
        <div class="left" style="width: 90px;font-size: 13px;color: #555;margin-left: 15px;margin-right: 10px;">是否包含入驻商家商品:</div>
        <div class="right">
          <Switch
            @change="(val) => handleChange(val, 'super_goods_include_enter_store')"
            :checked="setting.super_goods_include_enter_store"
            :un-checked-value="0"
            :checked-value="1"
          />
        </div>
      </div>
      <div class="flex_row_start_center item" style="margin-bottom: 10px;">
        <div class="left" style="width: 90px;font-size: 13px;color: #555;margin-left: 15px;margin-right: 10px;">折扣系数:</div>
        <div class="right">
          <div class="flex_row_start_center" v-if="edit_doscount">
            <InputNumber
              :max="1"
              :min="0.01"
              :precision="2"
              placeholder="请输入发放总量"
              @change="e => handleChange(e, 'super_goods_discount_factor')"
              v-model:value="setting.super_goods_discount_factor"
            />
            <div class="flex_row_center_center" @click="changeEditDiscount(false)" style="margin-left: 15px;">
              <AliSvgIcon iconName="iconxuanzhong" width="16px" height="16px" fillColor="#FA6F1E" />
            </div>
          </div>
          <div class="flex_row_start_center" v-else>
            
            <span style="color: #333;font-size: 13px;margin-right: 15px;">{{ setting.super_goods_discount_factor }}</span>
            <div class="flex_row_center_center" @click="changeEditDiscount(true)" style="margin-left: 15px;">
              <AliSvgIcon iconName="iconedit" width="16px" height="16px" fillColor="#FA6F1E" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <BasicTable @register="standardTable" style="padding: 0">
      <template #tableTitle>
        <div class="toolbar">
          <div class="toolbar_btn" @click="operate('add', null)">
            <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#fff" />
            <span>添加会员价商品</span>
          </div>
          <Popconfirm title="删除后不可恢复，是否确定删除？" @confirm="operate('del', 2)"
              :disabled="selectedRowKeys.length == 0">
              <div class="toolbar_btn"
                  @click="() => selectedRowKeys.length == 0 ? operate('del', 2) : null">
                  <AliSvgIcon iconName="iconshanchu" width="15px" height="15px" fillColor="#ff5908" />
                  <span>批量删除</span>
              </div>
          </Popconfirm>
          <div style="font-size: 13px;color: #555;margin-left: 10px;">共计<span style="color:#FF711E;margin: 0 3px;">{{ getPaginationRef()?getPaginationRef().total : 0}}</span>件商品</div>
        </div>
      </template>
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'mainImage'">
          <div class="goods_list_mainIamge">
            <Popover placement="right">
              <template #content>
                <div class="goods_list_mainIamge_pop">
                  <img :src="text" />
                </div>
              </template>
              <div
                class="goods_list_leftImage"
                :style="{ backgroundImage: `url('${text}')` }"
              ></div>
            </Popover>
            <div class="goods_list_online_rightInfo">
              <div class="goods_list_goodsname">{{ record.goodsName }}</div>
              <div class="goods_list_extraninfo">
                <div v-if="record.categoryPath" :title="record.categoryPath">{{ record.categoryPath }}</div>
                <div v-if="record.storeName" :title=" record.storeName">所属店铺：{{ record.storeName }}</div>
              </div>
            </div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            stopButtonPropagation
            :actions="[
              {
                label: '删除',
                popConfirm: {
                  title: '删除后不可恢复，是否确定删除？',
                  placement: 'left',
                  confirm: operate.bind(null, 'del',record),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <SldSelGoodsSingleDiy
      @cancle-event="handleCancle"
      @select-event="selectCancle"
      @confirm-event="confirmCancle"
      :api="getSuperMemberGoodsListApi"
      rowId="goodsId"
      modalTitle="查看下级"
      :checkedType="true"
      :column="modal_columns"
      :formConfig="modal_search"
      :modalVisible="modalVisible"
      :tableTitle="true"
    >
    <template #tableTitle>
      <div style="font-size: 13px;color: #555;margin-left: 10px;">已选<span style="color:#FF711E;margin: 0 3px;">{{selectedNum}}</span>件商品</div>
    </template>
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'mainImage'">
          <div class="super_goods">

            <div class="goods_list_mainIamge">
              <Popover placement="right">
                <template #content>
                  <div class="goods_list_mainIamge_pop">
                    <img :src="text" />
                  </div>
                </template>
                <div
                  class="goods_list_leftImage"
                  :style="{ backgroundImage: `url('${text}')` }"
                ></div>
              </Popover>
              <div class="goods_list_online_rightInfo">
                <div class="goods_list_goodsname">{{ record.goodsName }}</div>
                <div class="goods_list_extraninfo">
                  <div v-if="record.categoryPath" :title="record.categoryPath">{{ record.categoryPath }}</div>
                  <div v-if="record.storeName" :title=" record.storeName">所属店铺：{{ record.storeName }}</div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </template>
    </SldSelGoodsSingleDiy>
  </div>
</template>
<script>
  export default {
    name: 'SuperMemberList',
  };
</script>
<script setup>
  import { ref, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { Popover,Popconfirm,Switch,InputNumber } from 'ant-design-vue';
  import {getSuperMemberSuperListApi,getSuperMemberSuperDelApi,getSuperMemberGoodsListApi,getSuperMemberGoodsAddApi,getMemberSettingGetListApi,getMemberSettingUpdateListApi } from '/@/api/member/super';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import SldSelGoodsSingleDiy from '/@/components/SldSelGoodsSingleDiy/index.vue';
  import { sucTip, failTip,selectRadio, SelectAll, } from '/@/utils/utils';
  import { 
    getCategoryTreeListApi,
   } from '/@/api/operation/member';

  const selectedRowKeys = ref([])
  const selectedRows = ref([])
  const selectedNum = ref(0)

  const [standardTable, { reload,getForm,getPaginationRef}] = useTable({
    api: (arg) => {
     return getSuperMemberSuperListApi({ ...arg })
    },
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    columns: [
      {
        title: '商品信息',
        dataIndex: 'mainImage',
        width: 250,
      },
      {
        title: '价格',
        dataIndex: 'goodsPrice',
        width: 150,
        customRender: ({ text }) => {
          return text != undefined ? '￥'+(text?Number(text).toFixed(2):0) : '--';
        },
      },
      {
        title: '会员价',
        dataIndex: 'superMemberPrice',
        width: 150,
        customRender: ({ text }) => {
          return text != undefined ? '￥'+(text?Number(text).toFixed(2):0) : '--';
        },
      },
      {
        title: '库存',
        dataIndex: 'goodsStock',
        width: 100,
        customRender: ({ text }) => {
          return  text ? text : '--';
        },
      },
      {
        title: '销量',
        dataIndex: 'actualSales',
        width: 100,
        customRender: ({ text }) => {
          return  (text>=0 && text != undefined) ? text : '--';
        },
      },
    ],
    useSearchForm: true,
    formConfig: {
      schemas: [
        {
          field: 'goodsName',
          component: 'Input',
          componentProps: {
            placeholder: '请输入商品名称',
          },
          label: '商品名称',
        },
        {
          component: 'TreeSelect',
          field: 'categoryId',
          label: '商品分类',
          componentProps:{
            treeData:[]
          }
        },
      ],
    },
     // 点击搜索前处理的参数
    beforeFetch(values) {
      values.purchaseStartTime = values.purchaseStartTime
        ? values.purchaseStartTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.purchaseEndTime = values.purchaseEndTime ? values.purchaseEndTime.split(' ')[0] + ' 23:59:59' : undefined;
      values.expirationStartTime = values.expirationStartTime
        ? values.expirationStartTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.expirationEndTime = values.expirationEndTime ? values.expirationEndTime.split(' ')[0] + ' 23:59:59' : undefined;
      return values;
    },
    bordered: true,
    striped: false,
    showIndexColumn: true,
    rowKey:'goodsId',
    // 表格右侧操作列配置
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
    ellipsis:false,
    rowSelection: {
      type: 'checkbox',
      selectedRowKeys: selectedRowKeys,
      onSelect: onSelect,
      onSelectAll: onSelectAll,
    },
    clickToRowSelect: false,
  });
  
  const modal_columns = ref([
    {
      title: '商品信息',
      dataIndex: 'mainImage',
      width: 250,
    },
    {
      title: '价格',
      dataIndex: 'goodsPrice',
      width: 100,
    },
    {
      title: '库存',
      dataIndex: 'goodsStock',
      width: 80,
    },
    {
      title: '销量',
      dataIndex: 'actualSales',
      width: 80,
    },
  ])
  
  const modal_search = ref([
    {
      field: 'goodsName',
      component: 'Input',
      colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
      componentProps: {
        placeholder: '请输入商品名称',
      },
      label: '商品名称',
    },
    {
      component: 'TreeSelect',
      field: 'categoryId',
      colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
      label: '商品分类',
      componentProps:{
        treeData:[]
      }
    },
  ])

  const modalVisible = ref(false)
  const router = useRouter();
  const setting = ref({ //设置信息
    super_goods_is_enable: false,
    super_goods_include_enter_store: false,
    super_goods_discount_factor: 1,
  })
  const edit_doscount = ref(false)

 //操作按钮  add 添加会员价商品  del 删除
  const operate = async(type, val)=> {
    let res;
    if(type == 'del'){
      if(val==2&&selectedRowKeys.value.length==0){
       failTip('请先选中数据')
       return 
      }
      res = await getSuperMemberSuperDelApi(val==2?{goodsIds:selectedRowKeys.value.join(',')}:{goodsIds:val.goodsId})
    }else if(type=='add'){
      selectedNum.value = 0
      modalVisible.value = true
      return
    }
    if (res.state == 200) {
      sucTip(res.msg);
      reload()
    } else {
      failTip(res.msg);
    }
  }

  // 单选多选事件
  function onSelect(record, selected) {
    let rows = selectRadio(
      selectedRowKeys.value,
      selectedRows.value,
      'goodsId',
      record,
      selected,
    );
    selectedRowKeys.value = rows.selectedRowKeys;
    selectedRows.value = rows.selectedRows;
  }

  // 全选按钮事件
  function onSelectAll(selected, rows, changeRows) {
    let rowAll = SelectAll(
      selectedRowKeys.value,
      selectedRows.value,
      'goodsId',
      selected,
      rows,
      changeRows,
    );
    selectedRowKeys.value = rowAll.selectedRowKeys;
    selectedRows.value = rowAll.selectedRows;
  }


  //获取商品分类数据
  const get_category_list = async()=> {
    const { updateSchema } = getForm();
    let res = await getCategoryTreeListApi({ pId: 0, grade: 3 })
    if(res.state == 200){
      if (res.data.length > 0) {
        res.data.map(item => {
          item.value = item.categoryId;
          item.label = item.categoryName;
          if (item.children != null) {
            item.children.map(second => {
              second.value = second.categoryId;
              second.label = second.categoryName;
              if (second.children != null) {
                second.children.map(third => {
                  third.value = third.categoryId;
                  third.label = third.categoryName;
                });
              }
            });
          }
        });
      }
      modal_search.value.forEach(item=>{
        if(item.field=='categoryId'){
          item.componentProps.treeData = res.data
        }
      })
      await updateSchema({
        field: 'categoryId',
        componentProps: {
          treeData: [],
        },
      });
      await updateSchema({
        field: 'categoryId',
        componentProps: {
          treeData: res.data,
        },
      });
    }
  }


  const handleCancle = ()=> {
    modalVisible.value = false
  }

  // 选中的数量
  const selectCancle = (num)=> {
    selectedNum.value = num
  }

  // 弹窗点击确定
  const confirmCancle = async(record, ids)=> {
    if(ids.length==0){
      failTip('请选择商品');
      return;
    }
    let res = await getSuperMemberGoodsAddApi({ goodsIds: ids.join() })
    if (res.state == 200) {
      sucTip(res.msg);
      handleCancle();
      reload()
    } else {
      failTip(res.msg);
    }
  }

  //获取会员设置
  const get_member_set = async()=> {
    let res = await getMemberSettingGetListApi({ str: 'super_goods_is_enable,super_goods_include_enter_store,super_goods_discount_factor' })
    if (res.state == 200 && res.data) {
      res.data.map(item => {
        if (item.name == 'super_goods_is_enable' || item.name == 'super_goods_include_enter_store') {
          setting.value[item.name] = Number(item.value);
        } else {
          setting.value[item.name] = item.value ? item.value : 1;
        }
      })
    } else {
      failTip(res.msg);
    }
  }

  const handleChange = (val, key)=> {
    if (key == 'super_goods_discount_factor') {
      setting[key] = val;
    } else if (key == 'super_goods_is_enable' || key == 'super_goods_include_enter_store') {
      update_member_set(key, val);
      return;
    } else {
      setting[key] = val;
    }
  }

  //切换折扣系数编辑状态
  const changeEditDiscount = (e)=> {
    if(e){
      edit_doscount.value = e
    }else{
      update_member_set('super_goods_discount_factor', setting.value.super_goods_discount_factor);
    }
  }

  //保存会员设置
  const update_member_set = async(key, value)=> {
    let param = {};
    param[key] = value;
    let res = await getMemberSettingUpdateListApi(param)
    if (res.state == 200) {
      sucTip(res.msg);
      if (key == 'super_goods_is_enable' || key == 'super_goods_include_enter_store') {
        setting.value[key] = value;
      } else if (key == 'super_goods_discount_factor') {
        edit_doscount.value = false
      }
    } else {
      failTip(res.msg);
    }
  }

  onMounted(() => {
    get_member_set()
    get_category_list()
  });
</script>
<style lang="less">
 @import '/@/assets/css/promotion.less';
.sensitive_setting_setting{
    flex: 1;
    overflow: auto;
  }
  .business_load_img {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      margin: auto;

      img {
        max-width: 100%;
        max-height: 100%;
      }
    }
    .super_goods{
      .slodon-basic-table{
        height: auto !important;
      }
      .goods_list_online_rightInfo {
        display: flex;
        flex: 1;
        flex-direction: column;
        align-items: flex-start;
        justify-content: space-between;
        margin-left: 10px;
        padding: 1px 0;
  
        .goods_list_goodsname {
          display: -webkit-box;
          flex-shrink: 0;
          height: 34px;
          overflow: hidden;
          color: #333;
          font-size: 14px;
          line-height: 17px;
          text-align: left;
          text-overflow: ellipsis;
          word-break: break-word;
          white-space: normal;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
  
        .goods_list_extraninfo {
          color: #666;
          font-size: 12px;
          text-align: left;
  
          div {
            display: -webkit-box;
            flex-shrink: 0;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-word;
            white-space: normal;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
          }
        }
      }
    }
</style>
