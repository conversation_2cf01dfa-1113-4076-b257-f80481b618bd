<template>
  <div class="section_padding">
    <div class="section_padding_back lists_to_detail_member">
      <SldComHeader :type="1" :title="'会员详情'" :back="true" style="padding: 0 10px;"/>
      <div class="section_detail_scroll">
        <div class="lists_to_detail_member_padd" style="padding-bottom: 10px;">
         <div class="common_title">基本信息</div>
         <div class="basic_info">
           <div class="basic_portrait">
             <img :src="memberDetail.memberAvatar" alt="" />
             <p>会员头像</p>
           </div>
           <div class="basic_introduce_wrap">
             <div class="basic_introduce">
               <div class="introduce_item">
                 <p
                   >会员名称：<span class="item_text">{{
                     memberDetail.memberName ? memberDetail.memberName : '--'
                   }}</span></p
                 >
                 <p
                   >真实姓名：<span class="item_text">{{
                     memberDetail.memberTrueName ? memberDetail.memberTrueName : '--'
                   }}</span></p
                 >
                 <p
                   >昵称：<span class="item_text">{{
                     memberDetail.memberNickName ? memberDetail.memberNickName : '--'
                   }}</span></p
                 >
               </div>
               <div class="introduce_item">
                 <p
                   >性别：<span class="item_text">{{
                     memberDetail.genderValue ? memberDetail.genderValue : '--'
                   }}</span></p
                 >
                 <p
                   >生日：<span class="item_text">{{
                     memberDetail.memberBirthday ? memberDetail.memberBirthday : '--'
                   }}</span></p
                 >
                 <p
                   >手机号：<span class="item_text">{{
                     memberDetail.memberMobile ? memberDetail.memberMobile : '--'
                   }}</span></p
                 >
               </div>
               <div class="introduce_item">
                 <p
                   >邮箱：<span>{{
                     memberDetail.memberEmail ? memberDetail.memberEmail : '--'
                   }}</span></p
                 >
               </div>
             </div>
             <div class="basic_row">
               <p
                 ><span class="item_text">来源：</span
                 >{{ memberDetail.registerChannelValue ? memberDetail.registerChannelValue : '--' }}</p
               >
               <p
                 ><span class="item_text">注册时间：</span
                 >{{ memberDetail.registerTime ? memberDetail.registerTime : '--' }}</p
               >
               <p
                 ><span class="item_text">最近登陆IP：</span
                 >{{ memberDetail.lastLoginIp ? memberDetail.lastLoginIp : '--' }}</p
               >
               <p
                 ><span class="item_text">最近下单时间：</span
                 >{{ memberDetail.createTime ? memberDetail.createTime : '--' }}</p
               >
             </div>
           </div>
         </div>
         <div class="common_title">交易信息</div>
         <div class="trading_info">
           <div class="trading_row">
             <p class="trading_item" style="border-right: 1px solid rgb(214 218 242 / 20%);">客单价（元）</p>
             <p class="trading_item" style="border-right: 1px solid rgb(214 218 242 / 20%);">累计消费金额（元）</p>
             <p class="trading_item" style="border-right: 1px solid rgb(214 218 242 / 20%);">累计消费订单（单）</p>
             <p class="trading_item" style="border-right: 1px solid rgb(214 218 242 / 20%);">累计退款金额（元）</p>
             <p class="trading_item" style="border-right: 1px solid rgb(214 218 242 / 20%);">累计退款订单（单）</p>
           </div>
           <div class="trading_row">
             <p class="trading_item">{{ memberDetail.pstPrice ? memberDetail.pstPrice : 0 }}</p>
             <p class="trading_item">{{ memberDetail.orderAmount ? memberDetail.orderAmount : 0 }}</p>
             <p class="trading_item">{{ memberDetail.orderNumber ? memberDetail.orderNumber : 0 }}</p>
             <p class="trading_item">{{ memberDetail.refundAmount ? memberDetail.refundAmount : 0 }}</p>
             <p class="trading_item">{{ memberDetail.refundOrderNumber ? memberDetail.refundOrderNumber : 0 }}</p>
           </div>
         </div>
        </div>
         
         <!-- 历史订单 -->
         <div style="padding-top: 20px;background: #f5f5f5;">
           <div style="background: #fff;">
             <OrderListsPage :id="route.query.id"/>
           </div>
         </div>
      </div>
    </div>
    
  </div>
</template>
<script>
  export default {
    name: 'ListsToDetail',
  };
</script>
<script setup>
  import { ref, onMounted } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { 
    getMemberDetail,
    
   } from '/@/api/member/member';
  import { getImagePath } from '@/utils';
  import SldModal from '@/components/SldModal/index.vue';
  import { useRoute } from 'vue-router';
  import { Empty, Popconfirm } from 'ant-design-vue';
  import { failTip,sucTip } from '/@/utils/utils';
  import OrderListsPage from './order_lists_page.vue';


  const route = useRoute();
  const memberDetail = ref({});
  const balance_bj = getImagePath('images/balance_bj.png');
  const coupons_bj = getImagePath('images/coupons_bj.png');
  const integral_bj = getImagePath('images/integral_bj.png');
  const content = ref([])

  const content_data = ref([
      {
        type: 'select',
        label: `选择标签`,
        name: 'labelIds',
        placeholder: `请选择标签`,
        mode:'multiple',
        initialValue:undefined,
        selData: [],
        rules: [
          {
            required: true,
            message: `请选择标签`,
          },
        ],
      },
    ]);

  

  const getDetail = async () => {
    let res = await getMemberDetail({ memberId: route.query.id });
    if (res.state == 200) {
      memberDetail.value = res.data;
    } else {
      failTip(res.msg);
    }
  };

  

  onMounted(() => {
    getDetail();
    
  });

</script>
<style lang="less">
  @import './style/detail.less';
 
</style>
