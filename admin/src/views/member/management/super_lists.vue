<template>
  <div class="">
    <BasicTable @register="standardTable" style="padding: 0">
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'memberAvatar'">
          <Popover placement="rightTop">
            <template #content>
              <div style="width: 100px">
                <img :src="text" alt="" style="max-width: 100%; max-height: 100%" />
              </div>
            </template>
            <div class="business_load_img">
              <img :src="text" alt="" />
            </div>
          </Popover>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            stopButtonPropagation
            :actions="[
              {
                label: '查看',
                onclick: go_detail.bind(null, record),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
  </div>
</template>
<script>
  export default {
    name: 'SuperMemberList',
  };
</script>
<script setup>
  import { ref, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { Popover } from 'ant-design-vue';
  import {getSuperMemberList } from '/@/api/member/super';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';

  const [standardTable, { reload}] = useTable({
    api: (arg) => {
     return getSuperMemberList({ ...arg })
    },
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    columns: [
      {
        title: '会员头像',
        dataIndex: 'memberAvatar',
        width: 150,
      },
      {
        title: '会员名',
        dataIndex: 'memberName',
        width: 150,
      },
      {
        title: '会员昵称',
        dataIndex: 'memberNickName',
        width: 150,
      },
      {
        title: '手机号',
        dataIndex: 'memberMobile',
        width: 100,
      },
      {
        title: '购买时间',
        dataIndex: 'superPurchaseTime',
        width: 100,
      },
      {
        title: '到期时间',
        dataIndex: 'superExpirationTime',
        width: 100,
      },
    ],
    useSearchForm: true,
    formConfig: {
      schemas: [
        {
          field: 'memberName',
          component: 'Input',
          componentProps: {
            placeholder: '请输入会员名',
          },
          label: '会员名',
        },
        {
          component: 'RangePicker',
          field: '[purchaseStartTime,purchaseEndTime]',
          label: '购买时间',
          componentProps: {
            placeHolder: ['开始时间', '结束时间'],
          },
        },
        {
          component: 'RangePicker',
          field: '[expirationStartTime,expirationEndTime]',
          label: '到期时间',
          componentProps: {
            placeHolder: ['开始时间', '结束时间'],
          },
        },
        {
          field: 'memberMobile',
          component: 'Input',
          componentProps: {
            placeholder: '请输入手机号',
          },
          label: '手机号',
        },
      ],
    },
     // 点击搜索前处理的参数
    beforeFetch(values) {
      values.purchaseStartTime = values.purchaseStartTime
        ? values.purchaseStartTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.purchaseEndTime = values.purchaseEndTime ? values.purchaseEndTime.split(' ')[0] + ' 23:59:59' : undefined;
      values.expirationStartTime = values.expirationStartTime
        ? values.expirationStartTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.expirationEndTime = values.expirationEndTime ? values.expirationEndTime.split(' ')[0] + ' 23:59:59' : undefined;
      return values;
    },
    bordered: true,
    striped: false,
    showIndexColumn: true,
    rowKey:'memberId',
    // 表格右侧操作列配置
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
    ellipsis:false,
  });

  const router = useRouter();

  const go_detail = (item)=> {
    router.push({
      path: '/member/lists_to_detail',
      query: {
        id: item.memberId,
      },
    });
  }

  onMounted(() => {});
</script>
<style lang="less">
  .business_load_img {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      margin: auto;

      img {
        max-width: 100%;
        max-height: 100%;
      }
    }
</style>
