<template>
  <div class="section_padding">
    <div class="section_padding_home_scroll">
      <!-- 各省份支付金额占比/地域分布start -->
      <div class="w-full flex flex-row justify-between">
        <SldStatCharts
          title="各省份支付金额占比"
          :date-picker="true"
          :api-url="API.GET_PROVINCE_PAYAMOUNT_PERCENT"
          :data-handler="payAmountPercent_DataHandler"
          @register="payAmountPercent_register"
          mode="pie"
          style="margin-top: 0"
        >
        </SldStatCharts>
        <div class="padding_width"></div>
        <SldStatCharts
          title="地域分布"
          style="margin-top: 0"
          :api-url="API.GET_MEMBER_REGION"
          :data-handler="regionDataHandler"
          @register="region_register"
          :mode="regionRadioValue"
        >
          <template #extraSlot>
            <RadioGroup size="small" @change="radioTypeChange" v-model:value="regionTypeValue">
              <RadioButton value="member"> 会员 </RadioButton>
              <RadioButton value="store"> 店铺 </RadioButton>
            </RadioGroup>
          </template>
          <template #rightSlot>
            <RadioGroup size="small" @change="radioRegionChange" v-model:value="regionRadioValue">
              <RadioButton value="geo"> 地图 </RadioButton>
              <RadioButton value="pie"> 饼状图 </RadioButton>
            </RadioGroup>
          </template>
        </SldStatCharts>
      </div>
      <!-- 各省份支付金额占比/地域分布end -->

      <!-- 各省份增长趋势start -->
      <div class="w-full">
        <SldStatCharts
          title="各省份增长趋势"
          :date-picker="true"
          :api-url="API.GET_PROVINCE_RISE_TREND"
          :data-handler="proviceRiseTrendDataHandler"
          @register="proviceRiseTrend_register"
          mode="linear"
        >
          <template #extraSlot>
            <div class="flex flex-row items-center">
              <RadioGroup
                size="small"
                @change="provinceRiseRadioChange"
                v-model:value="provinceRiseRadio"
              >
                <RadioButton value="member"> 会员 </RadioButton>
                <RadioButton value="store"> 店铺 </RadioButton>
              </RadioGroup>
            </div>
          </template>
        </SldStatCharts>
      </div>
      <!-- 各省份增长趋势end -->

      <!-- 地域概况报表start--------------------- -->
      <div class="w-full flex flex-row" style="margin-top: 10px">
        <ReportWrapper title="地域概况报表" :export-option="currentExportOption">
          <ReportForm
            :columns="reportOptionsSet.columns"
            :searchData="reportOptionsSet.searchData"
            :beforeFetch="reportOptionsSet.beforeFetch"
            :api="reportOptionsSet.apiFunc"
            :isColumnIndex="true"
            :hidePagination="true"
            list-field="data"
          >
          </ReportForm>
        </ReportWrapper>
      </div>
      <!-- 地域概况报表end--------------------- -->
    </div>
  </div>
</template>

<script setup>
  import {
    SldStatCharts,
    useStatCharts,
    PriceCustomRender,
    ReportWrapper,
    ReportForm,
  } from '@/components/SldStat';
  import { API, getRegionReport } from '@/api/sys/statAnalysis';
  import { RadioGroup, RadioButton } from 'ant-design-vue';
  import { ref } from 'vue';

  //各省份支付金额占比start------------------
  const { register: payAmountPercent_register } = useStatCharts();
  const payAmountPercent_DataHandler = (return_data) => {
    let payAmountPercentOptions = {
      series: return_data.orderAmountList.map((item) => ({
        name: item.provinceName,
        value: item.orderPayAmount,
      })),
      isLegend: true,
      isRing: false,
    };

    return payAmountPercentOptions;
  };
  //各省份支付金额占比end------------------

  //地域分布start------------------------------
  const regionRadioValue = ref('geo');
  const regionTypeValue = ref('member');

  const {
    register: region_register,
    setCharts: region_setCharts,
    setApiUrl: region_setApiUrl,
    execApi: region_execApi,
  } = useStatCharts();
  const regionRawData = ref([]);
  const region_pieOrGeo = (data, bool) => {
    let piePart = {
      series: data.map((item) => ({
        name: item.provinceName,
        value: regionTypeValue.value == 'member' ? item.memberNum : item.storeNum,
      })),
      isLegend: true,
      isRing: false,
    };
    let geoPart = {
      series: data,
      seriesName: regionTypeValue.value == 'member' ? '会员总数' : '店铺总数',
      dataName: 'provinceName',
      dataValue: regionTypeValue.value == 'member' ? 'memberNum' : 'storeNum',
    };
    return bool ? geoPart : piePart;
  };

  const regionDataHandler = (return_data) => {
    regionRawData.value = return_data;
    return region_pieOrGeo(regionRawData.value, regionRadioValue.value == 'geo');
  };
  const radioRegionChange = () => {
    let piePart = {
      series: regionRawData.value.map((item) => ({
        name: item.provinceName,
        value: regionTypeValue.value == 'member' ? item.memberNum : item.storeNum,
      })),
      isLegend: true,
      isRing: false,
    };
    let geoPart = {
      series: regionRawData.value,
      seriesName: regionTypeValue.value == 'member' ? '会员总数' : '店铺总数',
      dataName: 'provinceName',
      dataValue: regionTypeValue.value == 'member' ? 'memberNum' : 'storeNum',
    };
    region_setCharts(regionRadioValue.value == 'geo' ? geoPart : piePart, regionRadioValue.value);
  };

  const radioTypeChange = () => {
    region_setApiUrl(
      regionTypeValue.value == 'member' ? API.GET_MEMBER_REGION : API.GET_STORE_REGION,
    );
    region_execApi();
  };

  //地域分布end------------------------------

  //各省份增长趋势start------------------------------
  const provinceRiseRadio = ref('member');
  const { register: proviceRiseTrend_register, execDataHandler: proviceRiseTrend_execDataHandler } =
    useStatCharts();
  const proviceRiseTrendDataHandler = (return_data) => {
    const xAxisData = return_data.map((item) => item.provinceName);
    const numList = return_data.map((item) =>
      provinceRiseRadio.value == 'member' ? item.memberNum : item.storeNum,
    );
    const series = [
      {
        data: numList,
        type: 'bar',
        barWidth: '20%',
      },
    ];
    const maxMount = Math.max.apply(null, numList);
    return { xAxisData, series, maxMount };
  };
  const provinceRiseRadioChange = () => {
    proviceRiseTrend_execDataHandler();
  };
  //各省份增长趋势end------------------------------

  //地域概况报表start-------------------------------
  const regionReportOption = [
    {
      title: `省份`,
      dataIndex: 'provinceName',
      sorter: true,
      width: 100,
    },
    {
      title: `支付金额`,
      dataIndex: 'orderPayAmount',
      sorter: true,
      width: 80,
      customRender: PriceCustomRender,
    },
    {
      title: `下单金额`,
      dataIndex: 'orderSubmitAmount',
      sorter: true,
      width: 80,
      customRender: PriceCustomRender,
    },
    {
      title: `支付订单数`,
      dataIndex: 'orderPayNum',
      sorter: true,
      width: 80,
    },
    {
      title: `下单数`,
      dataIndex: 'orderSubmitNum',
      sorter: true,
      width: 80,
    },
    {
      title: `新增会员数`,
      dataIndex: 'newMemberNum',
      sorter: true,
      width: 80,
    },
    {
      title: `新增店铺数`,
      dataIndex: 'newStoreNum',
      sorter: true,
      width: 80,
    },
    {
      title: `退单数`,
      dataIndex: 'returnNum',
      sorter: true,
      width: 80,
    },
    {
      title: `退单金额`,
      dataIndex: 'returnAmount',
      sorter: true,
      width: 80,
      customRender: PriceCustomRender,
    },
  ];

  const reportOptionsSet = ref({
    apiFunc: getRegionReport,
    columns: regionReportOption,
    searchData: [
      {
        field: 'provinceName',
        component: 'Input',
        colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
        componentProps: {
          minWidth: 300,
          maxlength: 6,
          placeholder: '请输入省份名称',
          size: 'default',
        },
        label: '省份名称',
        labelWidth: 80,
      },
    ],
    isColumnIndex: true,
    beforeFetch({ startTime, endTime, field, order, current, provinceName, pageSize }) {
      let paramLocal = { startTime, endTime, current, pageSize };
      if (provinceName) paramLocal.provinceName = provinceName;
      if (order) {
        paramLocal.sort = field;
        paramLocal.type = order == 'descend' ? 'desc' : 'asc';
      }
      return paramLocal;
    },
  });

  const currentExportOption = {
    api: '/statistics/admin/region/analysis/export',
    fileName: '地域概况报表',
  };
  //地域概况报表end---------------------------------
</script>

<style lang="less"></style>
