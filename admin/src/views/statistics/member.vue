<template>
  <div class="section_padding">
    <div class="section_padding_home_scroll">
      <!-- 会员总览start -->
      <div class="w-full bg-white">
        <div class="flex flex-row h-12 items-center pl-3 border-b-1 border-zinc-100">
          <div class="position-title font-bold mr-2"> 会员总览 </div>
        </div>

        <div class="p-5 flex flex-row justify-between">
          <CardLayout v-bind="item.bg" v-for="(item, index) in conbineData" :key="index">
            <div class="flex flex-row justify-between items-center">
              <div class="text-sm text-white">{{ item.name }}</div>

              <div v-if="item.isShowOperate">
                <Popover placement="bottom" trigger="click">
                  <template #content>
                    <div class="bg-white w-17">
                      <div
                        class="flex flex-row items-center py-1 justify-between hover:text-[#ff590d] cursor-pointer"
                        v-for="(op, opdx) in selectData"
                        :key="opdx"
                        @click="changeSelData(item, opdx, index)"
                      >
                        <span class="">{{ op.label }}</span>
                        <CheckOutlined style="color: #ff590d" v-if="item.opValue == opdx" />
                      </div>
                    </div>
                  </template>
                  <div class="flex flex-row items-center cursor-pointer">
                    <span class="text-sm text-white">{{ selectData[item.opValue].label }}</span>
                    <CaretDownOutlined style="color: white; font-size: 15px" class="ml-1" />
                  </div>
                </Popover>
              </div>
            </div>

            <div>
              <div class="mt-3 text-3xl font-bold">
                <CountTo
                  :color="'#fff'"
                  :startVal="0"
                  :prefixStyle="{ fontSize: '17px' }"
                  :endVal="Number(item.num) || 0"
                  :duration="1000"
                />
              </div>
              <div class="w-1/2 mt-3 h-px bg-white"></div>
              <div class="flex flex-row items-center text-white mt-3">
                <span>较上期</span>
                <div class="ml-2 font-bold">{{ item.differenceNum }}</div>
                <ArrowUpOutlined class="ml-1" v-if="item.isUp" style="font-weight: bold" />
                <ArrowDownOutlined class="ml-1" v-else style="font-weight: bold" />
              </div>
            </div>
          </CardLayout>
        </div>
      </div>
      <!-- 会员总览end -->

      <!-- 变化趋势start -->
      <div class="w-full mt-1">
        <SldStatCharts
          title="变化趋势"
          :date-picker="true"
          :api-url="API.GET_MEMBER_30_TREND"
          :data-handler="trendDataHandler"
          @register="trend_register"
          mode="linear"
        >
          <template #extraMiddleSlot>
            <div class="flex flex-row items-center px-5 py-4">
              <div class="mr-3">筛选项</div>
              <RadioGroup size="small" @change="radioTrendChange" v-model:value="trendRadioValue">
                <Radio value="newMember"> 新增会员数 </Radio>
                <Radio value="rechargeMember"> 储值会员数 </Radio>
                <Radio value="submitMember"> 下单人数 </Radio>
                <Radio value="payMember">支付人数</Radio>
              </RadioGroup>
            </div>
          </template>
        </SldStatCharts>
      </div>
      <!-- 变化趋势end -->

      <!-- 会员偏好商品排行/各终端会员占比start -->
      <div class="w-full flex flex-row justify-between">
        <SldStatRank
          title="会员偏好商品排行 - TOP10"
          :date-picker="true"
          @register="favRegister"
          :options="favOption"
          :isIndex="true"
        >
        </SldStatRank>
        <div class="padding_width"></div>
        <SldStatCharts
          title="各终端会员占比"
          :api-url="API.GET_MEMBER_PERCENT"
          :data-handler="memberPercentDataHandler"
          @register="memberPercent_register"
          mode="pie"
        >
        </SldStatCharts>
      </div>
      <!-- 会员偏好商品排行/各终端会员占比end -->

      <!-- 下单会员地域分布占比/地域分布start -->
      <div class="w-full flex flex-row justify-between">
        <SldStatCharts
          title="下单会员地域分布占比"
          :api-url="API.GET_MEMBER_REGION_PERCENT"
          :data-handler="memberRegionPercentDataHandler"
          @register="memberRegionPercent_register"
          :date-picker="true"
          mode="pie"
        >
        </SldStatCharts>
        <div class="padding_width"></div>
        <SldStatCharts
          title="地域分布"
          :api-url="API.GET_MEMBER_REGION"
          :data-handler="memberRegionDataHandler"
          @register="memberRegion_register"
          :mode="regionRadioValue"
        >
          <template #rightSlot>
            <RadioGroup size="small" @change="radioRegionChange" v-model:value="regionRadioValue">
              <RadioButton value="geo"> 地图 </RadioButton>
              <RadioButton value="pie"> 饼状图 </RadioButton>
            </RadioGroup>
          </template>
        </SldStatCharts>
      </div>
      <!-- 下单会员地域分布占比/地域分布end -->

      <!-- 会员报表start--------------------- -->
      <div class="w-full flex flex-row" style="margin-top: 10px">
        <ReportWrapper title="会员报表" :export-option="currentExportOption">
          <Tabs type="card" v-model:active-key="reportTabValue">
            <TabPane :key="tab.key" :tab="tab.name" v-for="tab in reportOptionsSet">
              <ReportForm
                :columns="tab.columns"
                :searchData="tab.searchData"
                :beforeFetch="tab.beforeFetch"
                :api="tab.apiFunc"
                :isColumnIndex="tab.isColumnIndex"
              >
              </ReportForm>
            </TabPane>
          </Tabs>
        </ReportWrapper>
      </div>
      <!-- 会员报表end--------------------- -->
    </div>
  </div>
</template>

<script setup>
  import {
    SldStatCharts,
    useStatCharts,
    SldStatRank,
    useStatRank,
    ReportForm,
    ReportWrapper,
    CardLayout,
  } from '@/components/SldStat';
  import { API } from '@/api/sys/statAnalysis';
  import { RadioGroup, Radio, RadioButton, Popover, TabPane, Tabs } from 'ant-design-vue';
  import { ref } from 'vue';
  import { formatNumPieCircle } from '/@/utils';
  import { CountTo } from '@/components/CountTo';
  import { handleMemberOverView, handleReport } from './actions/member';
  import {
    ArrowUpOutlined,
    ArrowDownOutlined,
    CaretDownOutlined,
    CheckOutlined,
  } from '@ant-design/icons-vue';

  // 会员总览start---------------------------
  const { memberPreviewData, selectData, changeSelData, memeberNumData } = handleMemberOverView();
  const conbineData = ref([memeberNumData, ...memberPreviewData.value]);
  // 会员总览end-----------------------------

  //会员30天趋势start------------------
  const { register: trend_register, execDataHandler: trendExecDataHandler } = useStatCharts();
  const trendRadioValue = ref('newMember');
  const trendRadioMap = {
    newMember: 'newMemberNum',
    rechargeMember: 'rechargeMemberNum',
    submitMember: 'orderSubmitMemberNum',
    payMember: 'orderPayMemberNum',
  };
  const trendDataHandler = (return_data) => {
    let memberTrendOptions = {};
    const sortCate = {};
    const xAxisData = return_data.map((item) => item.statsTime);
    let maxMount = 0;
    for (const tar in xAxisData) {
      const { terminalList } = return_data[tar];
      for (const ter in terminalList) {
        const singleItem = terminalList[ter];
        const { terminalName } = singleItem;
        let fimount = singleItem[trendRadioMap[trendRadioValue.value]];
        sortCate[terminalName] = sortCate[terminalName] || [];
        sortCate[terminalName].push(fimount);
        maxMount = fimount > maxMount ? fimount : maxMount;
      }
    }

    const series = Object.keys(sortCate).map((key) => ({
      name: key,
      data: sortCate[key],
      type: 'line',
    }));
    memberTrendOptions.maxMount = maxMount;
    memberTrendOptions.series = series;
    memberTrendOptions.xAxisData = xAxisData;
    return memberTrendOptions;
  };
  const radioTrendChange = () => {
    trendExecDataHandler();
  };
  //会员30天趋势end------------------

  //会员终端占比start------------------------------
  const { register: memberPercent_register } = useStatCharts();
  const memberPercentDataHandler = (return_data) => {
    const totalNum = return_data.reduce((total, prev) => total + prev.memberNum, 0);
    let memberPercentOption = {
      series: return_data.map((item) => ({
        name: item.terminalName,
        value: item.memberNum,
      })),
      isLegend: false,
      isRing: true,
      title: {
        text: '各终端会员总数',
        subtext: formatNumPieCircle(totalNum, totalNum > 9999 ? 1 : 0),
        textStyle: {
          fontSize: 14,
        },
        subtextStyle: {
          color: '#333',
          fontSize: 26,
        },
      },
    };
    return memberPercentOption;
  };
  //会员终端占比end------------------------------

  //会员下单地域占比start------------------------------
  const { register: memberRegionPercent_register } = useStatCharts();
  const memberRegionPercentDataHandler = (return_data) => {
    let memberPercentOption = {
      series: return_data.map((item) => ({
        name: item.provinceName,
        value: item.memberNum,
      })),
      isLegend: true,
      isRing: false,
    };
    return memberPercentOption;
  };
  //会员下单地域占比end------------------------------

  //会员下单地域占比start------------------------------
  const regionRadioValue = ref('geo');
  const { register: memberRegion_register, setCharts: memberRegion_setCharts } = useStatCharts();
  const regionRawData = ref([]);
  const memberRegionDataHandler = (return_data) => {
    regionRawData.value = return_data;
    let memberRegionOption = {
      series: return_data,
      seriesName: '会员总数',
      dataName: 'provinceName',
      dataValue: 'memberNum',
    };
    return memberRegionOption;
  };
  const radioRegionChange = () => {
    let piePart = {
      series: regionRawData.value.map((item) => ({
        name: item.provinceName,
        value: item.memberNum,
      })),
      isLegend: true,
      isRing: false,
    };
    let geoPart = {
      series: regionRawData.value,
      seriesName: '会员总数',
      dataName: 'provinceName',
      dataValue: 'memberNum',
    };
    memberRegion_setCharts(
      regionRadioValue.value == 'geo' ? geoPart : piePart,
      regionRadioValue.value,
    );
  };
  //会员下单地域占比end------------------------------

  //会员偏好商品排行start-------------------
  const favOption = [
    { title: '商品名称', dataIndex: 'goodsName', customRenderType: 'goodsRender' },
    {
      title: '商品浏览量',
      dataIndex: 'viewNum',
      sorter: true,
      sortDirections: ['descend'],
      width: 120,
    },
    { title: '商品收藏数', dataIndex: 'collectionNum', sorter: true, sortDirections: ['descend'] },
    { title: '支付订单数', dataIndex: 'orderPayNum', sorter: true, sortDirections: ['descend'] },
  ];
  const favParamHandler = (date, sortP) => {
    let param = date;
    if (sortP.field) {
      param.sort = sortP.field == 'viewNum' ? 1 : sortP.field == 'collectionNum' ? 2 : 3;
    }
    return param;
  };
  const { register: favRegister } = useStatRank({
    apiUrl: API.GET_PREFER_GOODS_RANK,
    paramHandler: favParamHandler,
  });
  //会员偏好商品排行end-------------------

  //会员报表start-------------------------
  const { currentExportOption, reportOptionsSet, reportTabValue } = handleReport();
  //会员报表end-------------------------
</script>

<style lang="less">
  .colorful_item {
    box-sizing: border-box;
    flex: 1;
    height: 163px;
    margin-right: 20px;
    padding: 12px 30px 12px 2.4%;
    background-repeat: no-repeat;
    background-position: top;
    background-size: auto 100%;

    &:last-child {
      margin-right: 0;
    }
  }
</style>
