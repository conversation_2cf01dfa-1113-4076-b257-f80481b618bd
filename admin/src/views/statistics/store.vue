<template>
  <div class="section_padding">
    <div class="section_padding_home_scroll">
      <!-- 会员总览start -->
      <div class="w-full bg-white">
        <div class="flex flex-row h-12 items-center pl-3 border-b-1 border-zinc-100">
          <div class="position-title font-bold mr-2"> 店铺总览 </div>
        </div>

        <div class="p-5 flex flex-row justify-between">
          <CardLayout v-for="(item, index) in conbineData" :key="index" v-bind="item.bg">
            <div class="flex flex-row justify-between items-center">
              <div class="text-sm text-white">{{ item.name }}</div>

              <div v-if="item.isShowOperate">
                <Popover placement="bottom" trigger="click">
                  <template #content>
                    <div class="bg-white w-17">
                      <div
                        class="flex flex-row items-center py-1 justify-between hover:text-[#ff590d] cursor-pointer"
                        v-for="(op, opdx) in selectData"
                        :key="opdx"
                        @click="changeSelData(item, opdx, index)"
                      >
                        <span class="">{{ op.label }}</span>
                        <CheckOutlined style="color: #ff590d" v-if="item.opValue == opdx" />
                      </div>
                    </div>
                  </template>
                  <div class="flex flex-row items-center cursor-pointer">
                    <span class="text-sm text-white">{{ selectData[item.opValue].label }}</span>
                    <CaretDownOutlined style="color: white; font-size: 15px" class="ml-1" />
                  </div>
                </Popover>
              </div>
            </div>

            <div>
              <div class="mt-3 text-3xl font-bold">
                <CountTo
                  :color="'#fff'"
                  :startVal="0"
                  :prefixStyle="{ fontSize: '17px' }"
                  :endVal="Number(item.num) || 0"
                  :duration="1000"
                />
              </div>
              <div class="w-1/2 mt-3 h-px bg-white"></div>
              <div
                class="flex flex-row items-center text-white mt-3"
                :class="{ invisible: !item.isShowOperate }"
              >
                <span>较上期</span>
                <div class="ml-2 font-bold">{{ item.differenceNum }}</div>
                <ArrowUpOutlined class="ml-1" v-if="item.isUp" style="font-weight: bold" />
                <ArrowDownOutlined class="ml-1" v-else style="font-weight: bold" />
              </div>
            </div>
          </CardLayout>

          <!-- <CardLayout icon="goods_icon1" color="blue"> </CardLayout> -->
        </div>
      </div>
      <!-- 会员总览end -->

      <!-- 新增店铺趋势/地域分布start -->
      <div class="w-full flex flex-row justify-between">
        <SldStatCharts
          title="新增店铺趋势"
          :date-picker="true"
          :api-url="API.GET_NEW_STORE_TREND"
          :data-handler="trendDataHandler"
          @register="trend_register"
          mode="linear"
        >
        </SldStatCharts>

        <div class="padding_width"></div>
        <SldStatCharts
          title="地域分布"
          :api-url="API.GET_STORE_REGION"
          :data-handler="storeRegionDataHandler"
          @register="storeRegion_register"
          :mode="regionRadioValue"
        >
          <template #rightSlot>
            <RadioGroup size="small" @change="radioRegionChange" v-model:value="regionRadioValue">
              <RadioButton value="geo"> 地图 </RadioButton>
              <RadioButton value="pie"> 饼状图 </RadioButton>
            </RadioGroup>
          </template>
        </SldStatCharts>
      </div>
      <!-- 新增店铺趋势/地域分布end -->

      <!-- 店铺等级分布占比start -->
      <div class="w-full flex flex-row justify-between">
        <SldStatCharts
          title="店铺等级分布占比"
          :api-url="API.GET_STORE_GRADE_PERCENT"
          :data-handler="storeGradePercentDataHandler"
          @register="storeGradePercent_register"
          mode="pie"
        >
        </SldStatCharts>
        <div class="padding_width"></div>
        <SldStatCharts
          title="店铺类型占比"
          :api-url="API.GET_STORE_TYPE_PERCENT"
          :data-handler="storeTypePercentDataHandler"
          @register="storeTypePercent_register"
          mode="pie"
        >
        </SldStatCharts>
      </div>
      <!-- 店铺等级分布占比end -->

      <!-- 店铺报表start--------------------- -->
      <div class="w-full flex flex-row" style="margin-top: 10px;">
        <ReportWrapper title="店铺报表" :export-option="currentExportOption">
          <ReportForm
            :columns="reportOptionsSet.columns"
            :searchData="reportOptionsSet.searchData"
            :beforeFetch="reportOptionsSet.beforeFetch"
            :api="reportOptionsSet.apiFunc"
            :isColumnIndex="true"
          >
          </ReportForm>
        </ReportWrapper>
      </div>
      <!-- 店铺报表end--------------------- -->
    </div>
  </div>
</template>

<script setup>
  import {
    SldStatCharts,
    useStatCharts,
    ReportWrapper,
    ReportForm,
    CardLayout,
  } from '@/components/SldStat';
  import { API } from '@/api/sys/statAnalysis';
  import { RadioGroup, RadioButton, Popover } from 'ant-design-vue';
  import { ref } from 'vue';
  import { handleStoreOverView, handleReport } from './actions/store';
  import { CountTo } from '@/components/CountTo';

  import {
    ArrowUpOutlined,
    ArrowDownOutlined,
    CaretDownOutlined,
    CheckOutlined,
  } from '@ant-design/icons-vue';

  // 店铺总览start---------------------------
  const { storePreviewData, selectData, changeSelData, storeNumData } = handleStoreOverView();
  const conbineData = ref([storeNumData, ...storePreviewData.value]);
  // 店铺总览end-----------------------------

  //新增店铺趋势start------------------
  const { register: trend_register } = useStatCharts();
  const trendDataHandler = (return_data) => {
    let storeTrendOptions = {};
    const xAxisData = return_data.map((item) => item.statsTime);
    const storeNumList = return_data.map((item) => item.newStoreNum);
    let maxMount = storeNumList.reduce((total, prev) => total + prev, 0);
    const series = [
      {
        name: '新增店铺数',
        data: return_data.map((item) => item.newStoreNum),
        type: 'line',
      },
    ];
    storeTrendOptions.maxMount = maxMount;
    storeTrendOptions.series = series;
    storeTrendOptions.xAxisData = xAxisData;
    return storeTrendOptions;
  };
  //新增店铺趋势end------------------

  //地域分布start------------------------------
  const regionRadioValue = ref('geo');
  const { register: storeRegion_register, setCharts: storeRegion_setCharts } = useStatCharts();
  const regionRawData = ref([]);
  const storeRegionDataHandler = (return_data) => {
    regionRawData.value = return_data;
    let storeRegionOption = {
      series: return_data,
      seriesName: '店铺总数',
      dataName: 'provinceName',
      dataValue: 'storeNum',
    };
    return storeRegionOption;
  };
  const radioRegionChange = () => {
    let piePart = {
      series: regionRawData.value.map((item) => ({
        name: item.provinceName,
        value: item.storeNum,
      })),
      isLegend: true,
      isRing: false,
    };
    let geoPart = {
      series: regionRawData.value,
      seriesName: '店铺总数',
      dataName: 'provinceName',
      dataValue: 'storeNum',
    };
    storeRegion_setCharts(
      regionRadioValue.value == 'geo' ? geoPart : piePart,
      regionRadioValue.value,
    );
  };
  //地域分布end------------------------------

  //会员终端占比start------------------------------
  const { register: storeTypePercent_register } = useStatCharts();
  const storeTypePercentDataHandler = (return_data) => {
    let storeTypePercentOption = {
      series: return_data.map((item) => ({
        name: item.typeName,
        value: item.storeNum,
      })),
      isLegend: true,
      isRing: false,
    };
    return storeTypePercentOption;
  };
  //会员终端占比end------------------------------

  //店铺等级分布占比start------------------------------
  const { register: storeGradePercent_register } = useStatCharts();
  const storeGradePercentDataHandler = (return_data) => {
    let storeGradePercentOption = {
      series: return_data.map((item) => ({
        name: item.gradeName,
        value: item.storeNum,
      })),
      isLegend: true,
      isRing: false,
    };
    return storeGradePercentOption;
  };
  //店铺等级分布占比end------------------------------

  //店铺报表start-----------------------------------
  const { currentExportOption, reportOptionsSet } = handleReport();
  //店铺报表end-----------------------------------
</script>

<style lang="less">
  .colorful_item {
    box-sizing: border-box;
    flex: 1;
    height: 163px;
    margin-right: 20px;
    padding: 12px 30px 12px 2.4%;
    background-repeat: no-repeat;
    background-position: top;
    background-size: auto 100%;

    &:last-child {
      margin-right: 0;
    }
  }
</style>
