<template>
  <div class="section_padding">
    <div class="section_padding_home_scroll">
      <!-- 品类销售趋势start -->
      <div class="w-full bg-white">
        <SldStatCharts
          title="TOP10品类销售趋势"
          name="top10Cate"
          @register="cateRegister"
          :datePicker="true"
          :api-url="API.GET_CATE_SALE_RANK"
          style="margin-top: 0;"
          :data-handler="handleCateSaleTrendData"
        >
          <template #extraSlot>
            <RadioGroup
              v-model:value="cateLinearType"
              @change="cateLinearType_radio_change"
              size="small"
            >
              <RadioButton :value="1">面积图</RadioButton>
              <RadioButton :value="2">柱状图</RadioButton>
            </RadioGroup>
          </template>
          <template #extraMiddleSlot>
            <div class="flex flex-row items-center px-5 py-4">
              <RadioGroup v-model:value="cateSale_radio" @change="cateSale_radio_change" size="small">
                <RadioButton :value="1">销售额</RadioButton>
                <RadioButton :value="2">销量</RadioButton>
              </RadioGroup>
            </div>
          </template>
        </SldStatCharts>
      </div>
      <!-- 品类销售趋势end -->
  
      <!-- 品类销售额占比/品类退货占比start -->
      <div class="w-full flex flex-row justify-center">
        <SldStatCharts
          name="cateSalePercent"
          title="品类销售占比"
          :datePicker="true"
          @register="cateSalePercent_register"
          :api-url="API.GET_CATE_SALE_PERCENT"
          :data-handler="cateSalePercentDataHandler"
          mode="pie"
        />
        <div class="padding_width"></div>
        <SldStatCharts
          name="cateReturnPercent"
          title="品类退货占比"
          :datePicker="true"
          @register="cateReturnPercent_register"
          :data-handler="cateReturnPercentDataHandler"
          :api-url="API.GET_CATE_RETURN_PERCENT"
          mode="pie"
        />
      </div>
      <!-- 品类退货占比/品类销售额占比end -->
  
      <!-- 品类销售报表start -->
      <div class="w-full flex flex-row justify-center" style="margin-top: 10px;">
        <div class="w-full flex flex-row justify-center bg-white">
          <ReportWrapper title="品类销售报表" :export-option="currentExportOption">
            <Tabs type="card" v-model:active-key="reportTabValue">
              <TabPane :key="tab.key" :tab="tab.name" v-for="tab in reportOptionsSet">
                <ReportForm
                  :columns="tab.columns"
                  :searchData="tab.searchData"
                  :beforeFetch="tab.beforeFetch"
                  :api="tab.apiFunc"
                  :isColumnIndex="tab.isColumnIndex"
                >
                </ReportForm>
              </TabPane>
            </Tabs>
          </ReportWrapper>
        </div>
      </div>
      <!-- 品类销售报表end -->
    </div>
  </div>
</template>

<script setup>
  import { SldStatCharts, useStatCharts, ReportForm, ReportWrapper } from '/@/components/SldStat';
  import { RadioGroup, RadioButton, TabPane, Tabs } from 'ant-design-vue';
  import { onMounted, ref } from 'vue';
  import { handleReport } from './actions/goods_category';
  import { API } from '/@/api/sys/statAnalysis';

  //品类销售趋势start-----------------------------------------
  const cateLinearType = ref(1);
  const cateSale_radio = ref(1);
  const { register: cateRegister, execDataHandler: cate_execDataHandler } = useStatCharts();
  const cateLinearType_radio_change = () => {
    cate_execDataHandler();
  };
  const cateSale_radio_change = () => {
    cate_execDataHandler();
  };

  const handleCateSaleTrendData = (return_data) => {
    let maxMount = 0;
    const sortObject = {};
    let cateOpition = {};
    cateOpition.xAxisData = return_data.map((i) => i.statsTime);
    const cateListMap = return_data.map((i) => i.categoryList).flat(1);
    cateListMap.forEach((item) => {
      let targetMount = cateSale_radio.value == 1 ? item.saleAmount : item.saleNum;
      if (targetMount > maxMount) maxMount = targetMount;
      sortObject[item.categoryId] = sortObject[item.categoryId] || {
        name: item.categoryName,
        data: [],
      };
      sortObject[item.categoryId].data.push(targetMount);
    });

    cateOpition.series = Object.keys(sortObject).map((sort) => ({
      name: sortObject[sort].name,
      data: sortObject[sort].data,
      type: cateLinearType.value == 1 ? 'line' : 'bar',
      [cateLinearType.value == 2 ? 'stack' : '']: 'x',
      barWidth: '20%',
    }));
    cateOpition.maxMount = maxMount;
    return cateOpition;
  };
  //品类销售趋势end--------------------------

  //品类销售占比start--------------------------
  const { register: cateSalePercent_register } = useStatCharts();
  const cateSalePercentDataHandler = (return_data) => {
    let options = {
      series: return_data.map((itm) => ({ name: itm.categoryName, value: itm.saleAmount })),
      isRing: false,
      isLegend: true,
    };
    return options;
  };
  //品类销售占比end--------------------------

  //品类退货占比start--------------------------
  const { register: cateReturnPercent_register } = useStatCharts();
  const cateReturnPercentDataHandler = (return_data) => {
    let options = {
      series: return_data.map((itm) => ({ name: itm.categoryName, value: itm.returnNum })),
      isRing: false,
      isLegend: true,
    };
    return options;
  };
  //品类退货占比end--------------------------

  //品类销售报表start---------------------
  const { reportOptionsSet, currentExportOption, reportTabValue } = handleReport();
  //品类销售报表end---------------------

  onMounted(() => {});
</script>

<style scoped></style>
