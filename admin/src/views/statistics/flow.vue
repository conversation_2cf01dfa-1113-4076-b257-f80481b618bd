<template>
  <div class="section_padding">
    <div class="section_padding_home_scroll">
      <!-- 流量总览start -->
      <div class="w-full bg-white">
        <div class="flex flex-row h-12 items-center pl-3 border-b-1 border-zinc-100">
          <div class="position-title font-bold mr-2"> 流量总览 </div>
          <DateMultiPicker name="over_view" @change="dateOverChange"></DateMultiPicker>
        </div>
        <div class="px-7 py-5">
          <div class="flex flex-row items-center">
            <div class="mr-3">筛选项</div>
            <RadioGroup
              size="small"
              @change="radioTerminalChange"
              v-model:value="radioTerminalValueC"
            >
              <Radio value="all" class="day_size"> 全部 </Radio>
              <!-- dev_mobile-start -->
              
              <!-- dev_mobile-end -->
              <!-- dev_pc-start -->
              <Radio value="pc">PC</Radio>
              <!-- dev_pc-end -->
              <!-- dev_mobile-start -->
              <!-- dev_wx-start -->
              <Radio value="xcx">微信小程序</Radio>
              <!-- dev_wx-end -->
              <!-- dev_h5-start -->
              <Radio value="h5">H5</Radio>
              <!-- dev_h5-end -->
              <!-- dev_mobile-end -->
            </RadioGroup>
          </div>

          <div class="flex flex-row overViewBackground mt-4 p-4 justify-between">
            <div>
              <div v-for="({ children }, index) in fieldMap" :key="index" class="flex flex-row">
                <div
                  v-for="(child, childx) in children"
                  :key="childx"
                  style="width: 210.5px; margin-bottom: 22px"
                >
                  <SldStatCard
                    :label="child.label"
                    :value="child.num"
                    :tip="child.tip"
                    :is-growth="true"
                    growth-text="较上期"
                    :is-growth-up="child.isUp"
                    :is-money="false"
                    :growth-value="child.differenceNum"
                  ></SldStatCard>
                </div>
              </div>
            </div>
            <ConverImage
              :view-pay="chartsInfoData.pvPayRate"
              :view-submit="chartsInfoData.pvSubmitRate"
              :submit-pay="chartsInfoData.submitPayRate"
            ></ConverImage>
          </div>
        </div>
      </div>
      <!-- 流量总览end -->

      <!-- 30天变化趋势start -->
      <div class="w-full bg-white mt-3">
        <SldStatCharts
          title="近30天变化趋势"
          :api-url="API.GET_30_TREND"
          :param-handler="trend30Flow_paramHandler"
          :data-handler="trendDataHandler"
          @register="trend30flow_Register"
        >
          <template #extraSlot>
            <RadioGroup
              size="small"
              @change="trend30TerminalChange"
              v-model:value="radioTerminalValue"
            >
              <RadioButton value="all"> 全部 </RadioButton>
             <!-- dev_mobile-start -->
              
              <!-- dev_mobile-end -->
              <!-- dev_pc-start -->
              <RadioButton value="pc">PC</RadioButton>
              <!-- dev_pc-end -->
              <!-- dev_mobile-start -->
              <!-- dev_wx-start -->
              <RadioButton value="xcx">微信小程序</RadioButton>
              <!-- dev_wx-end -->
              <!-- dev_h5-start -->
              <RadioButton value="h5">H5</RadioButton>
              <!-- dev_h5-end -->
              <!-- dev_mobile-end -->
            </RadioGroup>
          </template>
          <template #extraMiddleSlot>
            <div class="px-7 py-5">
              <div class="flex flex-row items-center">
                <div class="mr-3">筛选项</div>
                <RadioGroup size="small" v-model:value="current30Radio" @change="trend30RadioChange">
                  <Radio
                    :value="item.value"
                    class="day_size"
                    v-for="(item, index) in radio30Trend"
                    :key="index"
                    >{{ item.title }}</Radio
                  >
                </RadioGroup>
              </div>
            </div>
          </template>
        </SldStatCharts>
      </div>
      <!-- 30天变化趋势end -->

      <!-- 终端浏览量占比/终端访客数占比start -->
      <div class="w-full flex flex-row">
        <SldStatCharts
          mode="pie"
          title="各终端浏览量占比"
          name="terminalViewNumPercent"
          :date-picker="true"
          @register="terminal_ViewNP_Register"
          :api-url="API.GET_TERNINAL_VIEWNP"
          :data-handler="terminal_ViewNP_handler"
        >
        </SldStatCharts>

        <div class="padding_width"></div>

        <SldStatCharts
          mode="pie"
          title="各终端访客数占比"
          name="terminalVisitorNumPercent"
          :date-picker="true"
          @register="terminal_VisitNP_Register"
          :api-url="API.GET_TERNINAL_VISITNP"
          :data-handler="terminal_VisitNP_handler"
        >
        </SldStatCharts>
      </div>
      <!-- 终端浏览量占比/终端访客数占比end -->

      <!-- 店铺流量排行/商品流量排行start -->
      <div class="w-full flex flex-row">
        <SldStatRank
          name="storeRank"
          title="店铺流量排行 - TOP10"
          :options="[
            { title: '店铺名称', dataIndex: 'storeName', customRenderType: 'textRender' },
            {
              title: '浏览量',
              dataIndex: 'viewNum',
              sortDirections: ['descend'],
              sorter: true,
            },
            { title: '访客数', dataIndex: 'visitorNum', sortDirections: ['descend'], sorter: true },
          ]"
          @register="storeFlow_register"
          :date-picker="true"
          :is-index="true"
        ></SldStatRank>
        <div class="padding_width"></div>
        <SldStatRank
          title="商品流量排行 - TOP10"
          :options="[
            { title: '商品名称', dataIndex: 'goodsName', customRenderType: 'goodsRender' },
            {
              title: '浏览量',
              dataIndex: 'viewNum',
              sortDirections: ['descend'],
              sorter: true,
            },
            { title: '访客数', dataIndex: 'visitorNum', sortDirections: ['descend'], sorter: true },
          ]"
          :date-picker="true"
          :is-index="true"
          @register="goodsFlow_register"
        ></SldStatRank>
      </div>
      <!-- 店铺流量排行/商品流量排行end -->

      <!-- 流量报表start--------------------- -->
      <div class="w-full flex flex-row" style="margin-top: 10px;">
        <ReportWrapper title="流量报表" :export-option="currentExportOption">
          <Tabs type="card" v-model:active-key="reportTabValue">
            <TabPane :key="tab.key" :tab="tab.name" v-for="tab in reportOptionsSet">
              <ReportForm
                :columns="tab.columns"
                :searchData="tab.searchData"
                :beforeFetch="tab.beforeFetch"
                :api="tab.apiFunc"
                :isColumnIndex="tab.isColumnIndex"
              >
              </ReportForm>
            </TabPane>
          </Tabs>
        </ReportWrapper>
      </div>
      <!-- 流量报表end--------------------- -->
    </div>
  </div>
</template>

<script setup>
  import { ref } from 'vue';
  import { API } from '@/api/sys/statAnalysis';
  import { RadioGroup, Radio, RadioButton, Tabs, TabPane } from 'ant-design-vue';
  import {
    SldStatCharts,
    useStatCharts,
    SldStatRank,
    useStatRank,
    SldStatCard,
    ConverImage,
    DateMultiPicker,
    ReportWrapper,
    ReportForm,
  } from '@/components/SldStat';

  import { trade30FlowTrend, handleFlowReport, flowOverView } from './actions/flow';

  //各终端访客数占比start-------------------------------
  const { register: terminal_VisitNP_Register } = useStatCharts();
  const terminal_VisitNP_handler = (return_data) => {
    const totalNum = return_data.reduce((total, prev) => total + prev.visitorNum, 0);
    const options = {
      series: return_data.map((item) => ({ name: item.terminalName, value: item.visitorNum })),
      isRing: true,
      isLegend: false,
      title: {
        text: '各终端总访客数',
        subtext: totalNum,
        textStyle: {
          fontSize: 14,
        },
        subtextStyle: {
          color: '#333',
          fontSize: 26,
        },
      },
    };
    return options;
  };
  //各终端访客数占比end-------------------------------

  //各终端浏览量占比start-------------------------------
  const { register: terminal_ViewNP_Register } = useStatCharts();
  const terminal_ViewNP_handler = (return_data) => {
    const totalNum = return_data.reduce((total, prev) => total + prev.viewNum, 0);
    let options = {
      series: return_data.map((item) => ({ name: item.terminalName, value: item.viewNum })),
      isRing: true,
      isLegend: false,
      title: {
        text: '各终端浏览量总数',
        subtext: totalNum,
        textStyle: {
          fontSize: 14,
        },
        subtextStyle: {
          color: '#333',
          fontSize: 26,
        },
      },
    };
    return options;
  };
  //各终端浏览量占比end-------------------------------

  //近30天变化趋势start-------------------------------
  const radioTerminalValue = ref('all');
  const {
    register: trend30flow_Register,
    execApi: trend30Flow_execApi,
    execDataHandler: trend30Flow_execDataHandler,
  } = useStatCharts();
  const trend30Flow_paramHandler = () => {
    let param = {};
    if (radioTerminalValue.value != 'all') {
      param.terminalTypes = radioTerminalValue.value;
    }
    return param;
  };
  const {
    current30Radio,
    radio30Trend,
    trend30RadioChange,
    trend30TerminalChange,
    trendDataHandler,
  } = trade30FlowTrend(trend30Flow_execApi, trend30Flow_execDataHandler);
  //近30天变化趋势end-------------------------------

  //店铺流量排行start-------------------------------
  const storeFlow_paramHandler = (date, sortParam) => {
    let param = { ...date };
    if (sortParam.order) {
      param.sort = sortParam.field == 'viewNum' ? 1 : 2;
    }
    return param;
  };
  const { register: storeFlow_register } = useStatRank({
    apiUrl: API.GET_STORE_FLOW_RANK,
    paramHandler: storeFlow_paramHandler,
  });
  //店铺流量排行end-------------------------------

  //商品流量排行start-------------------------------
  const goodsFlow_paramHandler = (dateParam, sortParam) => {
    let param = { ...dateParam };
    if (sortParam.order) {
      param.sort = sortParam.field == 'viewNum' ? 1 : 2;
    }
    return param;
  };
  const { register: goodsFlow_register } = useStatRank({
    apiUrl: API.GET_GOODS_FLOW_RANK,
    paramHandler: goodsFlow_paramHandler,
  });
  //商品流量排行end-------------------------------

  //流量报表start-------------------------------
  const { currentExportOption, reportOptionsSet, reportTabValue } = handleFlowReport();
  //流量报表end-------------------------------

  //流量总览start----------------------------
  const radioTerminalValueC = ref('all');
  const { fieldMap, chartsInfoData, dateOverChange, radioTerminalChange } = flowOverView();
  //流量总览end-----------------------------
</script>

<style scoped lang="less">
  .position-title {
    position: relative;
    height: 15px;
    padding-left: 8px;
    border-left: 4px solid @primary-color;
    color: #3a3e43;
    font-size: 14px;
    font-weight: 700;
    line-height: 15px;
    white-space: nowrap;
  }

  .overViewBackground {
    background-color: #f5f5f5;
  }
</style>
