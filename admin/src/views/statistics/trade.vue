<template>
  <div class="section_padding">
    <div class="section_padding_home_scroll">
      <!-- 交易总览start -->
      <div class="w-full bg-white">
        <div class="flex flex-row h-12 items-center pl-3 border-b-1 border-zinc-100">
          <div class="position-title font-bold mr-2"> 交易总览 </div>
          <DateMultiPicker name="over_view" @change="dateOverChange"></DateMultiPicker>
        </div>
        <div class="px-7 py-5">
          <div class="flex flex-row items-center">
            <div class="mr-3">筛选项</div>
            <RadioGroup size="small" @change="radioTerminalChange" v-model:value="radioValue">
              <Radio value="all" class="day_size"> 全部 </Radio>
              <!-- dev_mobile-start -->
              
              <!-- dev_mobile-end -->
              <!-- dev_pc-start -->
              <Radio value="pc">PC</Radio>
              <!-- dev_pc-end -->
              <!-- dev_mobile-start -->
              <!-- dev_wx-start -->
              <Radio value="xcx">微信小程序</Radio>
              <!-- dev_wx-end -->
              <!-- dev_h5-start -->
              <Radio value="h5">H5</Radio>
              <!-- dev_h5-end -->
              <!-- dev_mobile-end -->
            </RadioGroup>
          </div>

          <div class="flex flex-row overViewBackground mt-4 p-4 justify-between pr-10">
            <div>
              <div v-for="({ children }, index) in fieldMap" :key="index" class="flex flex-row">
                <div
                  v-for="(child, childx) in children"
                  :key="childx"
                  style="width: 210.5px; margin-bottom: 22px"
                >
                  <SldStatCard
                    :label="child.label"
                    :value="child.num"
                    :tip="child.tip"
                    :is-growth="true"
                    growth-text="较上期"
                    :is-growth-up="child.isUp"
                    :is-money="child.isMoney"
                    :growth-value="child.differenceNum"
                  ></SldStatCard>
                </div>
              </div>
            </div>
            <ConverImage
              :view-pay="chartsInfoData.pvPayRate"
              :view-submit="chartsInfoData.pvSubmitRate"
              :submit-pay="chartsInfoData.submitPayRate"
            ></ConverImage>
          </div>
        </div>
      </div>
      <!-- 交易总览end -->

      <!-- 近30天变化趋势start -->
      <div class="w-full bg-white mt-3">
        <SldStatCharts
          mode="linear"
          title="近30天变化趋势"
          @register="trade30Trend_register"
          :api-url="API.GET_30_TREND"
          :data-handler="trendDataHandler"
          :param-handler="trend30_paramHandler"
        >
          <template #extraSlot>
            <RadioGroup
              size="small"
              @change="trend30TerminalChange"
              v-model:value="current30Terminal"
            >
              <RadioButton value="all" class="day_size"> 全部 </RadioButton>
              <!-- dev_mobile-start -->
              
              <!-- dev_mobile-end -->
              <!-- dev_pc-start -->
              <RadioButton value="pc">PC</RadioButton>
              <!-- dev_pc-end -->
              <!-- dev_mobile-start -->
              <!-- dev_wx-start -->
              <RadioButton value="xcx">微信小程序</RadioButton>
              <!-- dev_wx-end -->
              <!-- dev_h5-start -->
              <RadioButton value="h5">H5</RadioButton>
              <!-- dev_h5-end -->
              <!-- dev_mobile-end -->
            </RadioGroup>
          </template>
          <template #extraMiddleSlot>
            <div class="px-7 py-5">
              <div class="flex flex-row items-center">
                <div class="mr-3">筛选项</div>
                <RadioGroup
                  size="small"
                  v-model:value="current30Radio"
                  @change="trend30RadioChange"
                >
                  <Radio
                    :value="item.value"
                    class="day_size"
                    v-for="(item, index) in radio30Trend"
                    :key="index"
                    >{{ item.title }}</Radio
                  >
                </RadioGroup>
              </div>
            </div>
          </template>
        </SldStatCharts>
      </div>
      <!-- 近30天变化趋势end -->

      <div class="w-full flex flex-row">
        <SldStatRank
          name="storeRank"
          title="店铺销售排行 - TOP10"
          :options="[
            { title: '店铺名称', dataIndex: 'storeName', customRenderType: 'textRender' },
            {
              title: '销售额',
              dataIndex: 'orderPayAmount',
              sortDirections: ['descend'],
              sorter: true,
            },
            {
              title: '订单量',
              dataIndex: 'orderPayNum',
              sortDirections: ['descend'],
              sorter: true,
            },
          ]"
          @register="storeRank_register"
          :is-index="true"
          :date-picker="true"
        ></SldStatRank>
        <div class="padding_width"></div>
        <SldStatRank
          title="商品销售排行 - TOP10"
          @register="goodsRank_register"
          :options="[
            { title: '商品名称', dataIndex: 'goodsName', customRenderType: 'goodsRender' },
            {
              title: '销售额',
              dataIndex: 'saleAmount',
              sortDirections: ['descend'],
              sorter: true,
              customRenderType: 'priceRender',
            },
            { title: '销量', dataIndex: 'saleNum', sortDirections: ['descend'], sorter: true },
          ]"
          :date-picker="true"
          :is-index="true"
        ></SldStatRank>
      </div>

      <!-- 各省份销售占比/各省份销售趋势start -->
      <div class="w-full flex flex-row">
        <SldStatCharts
          title="各省份销售趋势"
          @register="provinceTrend_register"
          :date-picker="true"
          :api-url="API.GET_PROVINCE_TREND"
          :param-handler="provinceTrend_paramHandler"
          :data-handler="proviceTrendDataHandler"
        >
          <template #extraSlot>
            <Select
              style="width: 120px"
              @change="proviceSelectChange"
              :options="provinceOption"
              v-model:value="currentProvince.regionCode"
            >
            </Select>
          </template>
        </SldStatCharts>

        <div class="padding_width"></div>

        <SldStatCharts
          title="各省份销售占比"
          @register="provicePercent_register"
          :date-picker="true"
          :api-url="API.GET_PROVINCE_PERCENT"
          :data-handler="provincePercent_dataHandler"
          mode="pie"
        >
          <template #extraSlot>
            <RadioGroup v-model:value="isAmountNum" @change="provincePercentChange" size="small">
              <RadioButton :value="1">销售额</RadioButton>
              <RadioButton :value="2">订单量</RadioButton>
            </RadioGroup>
          </template>
        </SldStatCharts>
      </div>
      <!-- 各省份销售占比/各省份销售趋势end -->

      <!-- 各终端销售趋势/各终端销售占比start -->
      <div class="w-full flex flex-row">
        <SldStatCharts
          title="各终端销售趋势"
          @register="terminalTrend_register"
          :date-picker="true"
          :api-url="API.GET_TERMINAL_TREND"
          :data-handler="terminalTrendDataHandler"
        >
          <template #extraSlot>
            <RadioGroup
              v-model:value="isAmountNumTerminalTrend"
              size="small"
              @change="terminalTrendChange"
            >
              <RadioButton :value="1">销售额</RadioButton>
              <RadioButton :value="2">订单量</RadioButton>
            </RadioGroup>
          </template>
        </SldStatCharts>
        <div class="padding_width"></div>
        <SldStatCharts
          mode="pie"
          title="各终端销售占比"
          @register="terminalPercent_register"
          :date-picker="true"
          :api-url="API.GET_TERMINAL_PERCENT"
          :data-handler="terminalPercentDataHandler"
        >
          <template #extraSlot>
            <RadioGroup
              v-model:value="isAmountNumTerminalPercent"
              size="small"
              @change="terminalPercentChange"
            >
              <RadioButton :value="1">销售额</RadioButton>
              <RadioButton :value="2">订单量</RadioButton>
            </RadioGroup>
          </template>
        </SldStatCharts>
      </div>
      <!-- 各终端销售趋势/各终端销售占比end -->

      <div class="w-full flex flex-row" style="margin-top: 10px">
        <ReportWrapper
          title="交易报表"
          :export-option="{
            api: '/statistics/admin/trade/analysis/export',
            fileName: '交易报表导出',
          }"
        >
          <ReportForm
            :columns="[
              {
                title: `${'时间'}`,
                dataIndex: 'statsTime',
                sorter: true,
                width: 60,
              },
              {
                title: `${'下单数'}`,
                dataIndex: 'orderSubmitNum',
                sorter: true,
                width: 80,
              },
              {
                title: `${'下单人数'}`,
                dataIndex: 'orderSubmitMemberNum',
                sorter: true,
                width: 80,
              },
              {
                title: `${'下单金额'}`,
                dataIndex: 'orderSubmitAmount',
                sorter: true,
                width: 80,
              },
              {
                title: `${'支付订单数'}`,
                dataIndex: 'orderPayNum',
                sorter: true,
                width: 80,
              },
              {
                title: `${'支付人数'}`,
                dataIndex: 'orderPayMemberNum',
                sorter: true,
                width: 80,
              },
              {
                title: `${'支付金额'}`,
                dataIndex: 'orderPayAmount',
                sorter: true,
                width: 80,
              },
              {
                title: '支付转化率',
                dataIndex: 'submitPayRate',
                sorter: true,
                width: 80,
                helpMessage: '下单-支付转化率：统计时间内，支付人数/下单人数',
              },
            ]"
            :api="getTradeReport"
            :before-fetch="tradeReportFetch"
          >
          </ReportForm>
        </ReportWrapper>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { sldConvert } from '@/utils';
  import { onMounted, ref } from 'vue';
  import { Select } from 'ant-design-vue';
  import { getTradeReport, API } from '@/api/sys/statAnalysis';
  import { RadioGroup, Radio, RadioButton } from 'ant-design-vue';
  import {
    tradeOverView,
    trade30Trend,
    handleProvinceTrend,
    handleProvicePercent,
    handleTerminalTrend,
    handleTerminalPercent,
  } from './actions/trade';
  import {
    SldStatCharts,
    useStatCharts,
    SldStatRank,
    useStatRank,
    SldStatCard,
    ConverImage,
    DateMultiPicker,
    ReportWrapper,
    ReportForm,
  } from '@/components/SldStat';
  const radioValue = ref('all');
  const { fieldMap, chartsInfoData, dateOverChange, radioTerminalChange } = tradeOverView();

  //近30天变化趋势start--------------------------
  const current30Terminal = ref('all');
  const {
    register: trade30Trend_register,
    execApi: trade30Trend_execApi,
    execDataHandler: trade30Trend_execDataHandler,
  } = useStatCharts();
  const {
    current30Radio,
    radio30Trend,
    trend30RadioChange,
    trend30TerminalChange,
    trendDataHandler,
  } = trade30Trend(trade30Trend_execApi, trade30Trend_execDataHandler);
  const trend30_paramHandler = () => {
    let param = {};
    if (current30Terminal.value != 'all') {
      param.terminalTypes = current30Terminal.value;
    }
    return param;
  };
  //近30天变化趋势end--------------------------

  //各终端销售趋势start-----------------
  const { register: provinceTrend_register, execApi: provinceTrend_execApi } = useStatCharts();
  const { currentProvince, proviceSelectChange, proviceTrendDataHandler, areaList } =
    handleProvinceTrend(provinceTrend_execApi);
  const list = areaList.map((item) => ({ label: item.regionName, value: item.regionCode }));
  const provinceOption = ref(list);
  const provinceTrend_paramHandler = (dateParam) => {
    return {
      ...dateParam,
      provinceCode: currentProvince.value.regionCode,
    };
  };
  //各终端销售趋势end-------------------

  //各省份销售占比start----------------------------
  const { register: provicePercent_register, execDataHandler: provicePercent_execDataHandler } =
    useStatCharts();
  const { isAmountNum, provincePercent_dataHandler, provincePercentChange } = handleProvicePercent(
    provicePercent_execDataHandler,
  );
  //各省份销售占比end------------------------------

  //各终端销售趋势start----------------------------
  const { register: terminalTrend_register, execDataHandler: terminalTrend_execDataHandler } =
    useStatCharts();
  const { terminalTrendChange, terminalTrendDataHandler, isAmountNumTerminalTrend } =
    handleTerminalTrend(terminalTrend_execDataHandler);
  //各终端销售趋势end------------------------------

  // 各终端销售占比start--------------------------
  const { register: terminalPercent_register, execDataHandler: terminalPercent_execDataHandler } =
    useStatCharts();
  const { terminalPercentDataHandler, isAmountNumTerminalPercent, terminalPercentChange } =
    handleTerminalPercent(terminalPercent_execDataHandler);
  // 各终端销售占比end--------------------------

  //店铺销售排行start--------------------
  const storeRank_paramHandler = (date, sortParam) => {
    let param = { ...date };
    if (sortParam.order) {
      param.sort = sortParam.field == 'orderPayAmount' ? 1 : 2;
    }
    return param;
  };
  const { register: storeRank_register } = useStatRank({
    apiUrl: API.GET_STORE_RANK,
    paramHandler: storeRank_paramHandler,
  });
  //店铺销售排行end--------------------

  //商品销售排行start------------------
  const goodsRank_paramHandler = (date, sortParam) => {
    let param = { ...date };
    if (sortParam.order) {
      param.sort = sortParam.field == 'saleAmount' ? 1 : 2;
    }
    return param;
  };
  const { register: goodsRank_register } = useStatRank({
    apiUrl: API.GET_GOODS_RANK,
    paramHandler: goodsRank_paramHandler,
  });
  //商品销售排行end-----------------------

  //交易报表start----------------------------
  const tradeReportFetch = ({ startTime, endTime, field, order, current }) => {
    let paramLocal = { startTime, endTime, current };
    if (order) {
      paramLocal.sort = sldConvert(field);
      paramLocal.type = order == 'descend' ? 'desc' : 'asc';
    }
    return paramLocal;
  };
  //交易报表end----------------------------

  onMounted(() => {});
</script>

<style scoped lang="less">
  .position-title {
    position: relative;
    height: 15px;
    padding-left: 8px;
    border-left: 4px solid @primary-color;
    color: #3a3e43;
    font-size: 14px;
    font-weight: 700;
    line-height: 15px;
    white-space: nowrap;
  }

  .overViewBackground {
    background-color: #f5f5f5;
  }
</style>
