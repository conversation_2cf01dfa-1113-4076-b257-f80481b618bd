<template>
  <div class="section_padding">
    <div class="section_padding_home_scroll">
      <!-- 实时分析start -->
      <Spin :spinning="spin">
        <div class="w-full bg-white">
          <div class="flex flex-row h-12 items-center pl-3 border-b-1 border-zinc-100">
            <div class="position-title font-bold mr-2"> 实时分析 </div>
            <Tooltip
              placement="right"
              title="今日实时数据的统计时间均为今日零时至当前更新时间。点击刷新按钮可强制更新。"
            >
              <QuestionCircleFilled style="color: #dadada" />
            </Tooltip>
            <div class="ml-2">更新时间：{{ statsTime }} </div>
            <SyncOutlined
              class="ml-3 cursor-pointer"
              style="color: #999"
              @click="getPresentData(true)"
              :spin="spin"
            ></SyncOutlined>
          </div>

          <div class="stat-num_stat_item flex flex-row">
            <div class="stat-left_side flex flex-col items-center justify-center">
              <img :src="realTime_data1.icon" alt="" width="40" height="40" />
              <div class="text-[14px] text-[#333] mt-2">{{ realTime_data1.title }}</div>
            </div>

            <div class="flex flex-row items-center">
              <div
                v-for="(item, index2) in realTime_data1.list"
                :key="index2"
                class="stat-right_main_item flex flex-row items-center"
              >
                <SldStatCard
                  :label="item.name"
                  :value="item.value"
                  :is-money="item.isMoney"
                  :tip="item.tip"
                ></SldStatCard>
              </div>
            </div>
          </div>

          <div class="stat-num_stat_item flex flex-row">
            <div class="stat-left_side flex flex-col items-center justify-center">
              <img :src="realTime_data2.icon" alt="" width="40" height="40" />
              <div class="text-[14px] text-[#333] mt-2">{{ realTime_data2.title }}</div>
            </div>

            <div class="flex flex-row items-center flex-wrap">
              <template v-for="item in realTime_data2.list">
                <div
                  v-if="item.isBr"
                  style="width: 100%; height: 15px; background-color: rgb(255 255 255)"
                >
                </div>
                <div class="stat-right_main_item flex flex-row items-center" v-else>
                  <SldStatCard
                    :label="item.name"
                    :value="Number(item.value)"
                    :is-money="item.isMoney"
                    :tip="item.tip"
                    :isPercent="item.isPercent"
                  ></SldStatCard>
                </div>
              </template>
            </div>
          </div>
        </div>
      </Spin>
      <!-- 实时分析end -->

      <div class="w-full">
        <!-- 实时商品销售排行/实时店铺销售排行start -->
        <div class="flex flex-row">
          <SldStatRank
            title="实时商品销售排行 - TOP10"
            :options="[
              { title: '商品名称', dataIndex: 'goodsName', customRenderType: 'goodsRender' },
              {
                title: '销售额',
                dataIndex: 'saleAmount',
                sorter: (a, b) => a.saleAmount - b.saleAmount,
                sortDirections: ['descend'],
                customRenderType: 'priceRender',
              },
              {
                title: '销量',
                dataIndex: 'saleNum',
                sorter: (a, b) => a.saleNum - b.saleNum,
                sortDirections: ['descend'],
              },
            ]"
            :data-source="goodsSaleAmountList"
            :date-picker="false"
            :is-index="true"
            :raw-sort="true"
          ></SldStatRank>
          <div class="padding_width"></div>
          <SldStatRank
            title="实时店铺销售排行 - TOP10"
            :options="[
              { title: '店铺名称', dataIndex: 'storeName', customRenderType: 'textRender' },
              {
                title: '销售额',
                dataIndex: 'orderPayAmount',
                sorter: (a, b) => a.orderPayAmount - b.orderPayAmount,
                sortDirections: ['descend'],
              },
              {
                title: '订单量',
                dataIndex: 'orderSubmitNum',
                sorter: (a, b) => a.orderSubmitNum - b.orderSubmitNum,
                sortDirections: ['descend'],
              },
            ]"
            name="storeSale"
            :data-source="storeSaleAmountList"
            :date-picker="false"
            :is-index="true"
            :raw-sort="true"
          ></SldStatRank>
        </div>
        <!-- 实时商品销售排行/实时店铺销售排行end -->

        <!-- 实时品类销售排行/分时支付/下单金额趋势start -->
        <div class="flex flex-row">
          <SldStatRank
            title="实时品类销售排行 - TOP10"
            :options="[
              { title: '一级品类', dataIndex: 'categoryName' },
              {
                title: '销售额',
                dataIndex: 'saleAmount',
                sorter: (a, b) => a.saleAmount - b.saleAmount,
                sortDirections: ['descend'],
              },
              {
                title: '销量',
                dataIndex: 'saleNum',
                sorter: (a, b) => a.saleNum - b.saleNum,
                sortDirections: ['descend'],
              },
            ]"
            :data-source="categorySaleAmountList"
            :date-picker="false"
            :raw-sort="true"
            :is-index="true"
          >
          </SldStatRank>
          <div class="padding_width"></div>
          <SldStatCharts
            title="分时支付/下单金额趋势"
            mode="linear"
            @register="orderTrend_register"
          ></SldStatCharts>
        </div>
        <!-- 实时品类销售排行/分时支付/下单金额趋势end -->

        <!-- 分时会员/店铺新增趋势 / 分时流量趋势start -->
        <div class="flex flex-row">
          <SldStatCharts
            title="分时会员/店铺新增趋势"
            mode="linear"
            @register="newMemberStore_register"
          ></SldStatCharts>
          <div class="padding_width"></div>
          <SldStatCharts
            title="分时流量趋势"
            mode="linear"
            @register="visit_view_register"
          ></SldStatCharts>
        </div>
        <!-- 分时会员/店铺新增趋势 / 分时流量趋势end -->
      </div>
    </div>
  </div>
</template>

<script setup>
  import { onMounted, reactive, ref } from 'vue';
  import { Tooltip, Spin } from 'ant-design-vue';
  import { QuestionCircleFilled } from '@ant-design/icons-vue';
  import { getPresentStat } from '@/api/sys/statAnalysis';
  import { realTimeData1, realTimeData2 } from './actions/realTime';
  import { SyncOutlined } from '@ant-design/icons-vue';
  import { useStatCharts, SldStatCharts, SldStatRank, SldStatCard } from '/@/components/SldStat';
  import { isString } from 'lodash-es';
  const realTime_data1 = reactive(realTimeData1);
  const realTime_data2 = reactive(realTimeData2);
  const statsTime = ref('');
  const spin = ref(false);
  const goodsSaleAmountList = ref([]);
  const storeSaleAmountList = ref([]);
  const categorySaleAmountList = ref([]);
  const orderTrendOptions = reactive({});
  const member_store_options = reactive({});
  const visit_view_options = reactive({});

  const getPresentData = async (refresh) => {
    spin.value = true;
    let params = {};
    if (refresh) params.refresh = true;
    const res = await getPresentStat(params);
    if (res?.state == 200) {
      statsTime.value = res.data.statsTime;
      goodsSaleAmountList.value = res.data.goodsSaleAmountList;
      storeSaleAmountList.value = res.data.storeSaleAmountList;
      categorySaleAmountList.value = res.data.categorySaleAmountList;
      handleChartsOption(res.data);
      handleMemberStoreOption(res.data);
      handleVisitViewOption(res.data);
      const tempArray = [...realTime_data1.list, ...realTime_data2.list];
      const tempActionData = {
        ...res.data.platformSummary,
        ...res.data.platformTodaySummary,
        orderPayAmountTotal: res.data.platformSummary.orderPayAmount,
      };
      tempArray.forEach((item, index) => {
        tempArray[index]['value'] = isString(tempActionData[item.mapKey])
          ? tempActionData[item.mapKey].split('%')[0]
          : tempActionData[item.mapKey];
      });
      setTimeout(() => {
        spin.value = false;
      }, 200);
    }
  };

  //分时支付/下单金额趋势start-----------------------------------------------------------
  const { register: orderTrend_register, setCharts: orderTrend_setCharts } = useStatCharts();
  const handleChartsOption = (data) => {
    orderTrendOptions.xAxisData = data.orderSubmitAmountList.map((or) => or.hour);
    const data1 = data.orderSubmitAmountList.map((or) => or.amount);
    const data2 = data.orderPayAmountList.map((or) => or.amount);

    orderTrendOptions.series = [
      { name: '支付金额', data: data2, type: 'line' },
      { name: '下单金额', data: data1, type: 'line' },
    ];
    orderTrendOptions.maxMount = Math.max.apply(null, [...data1, ...data2]);
    orderTrend_setCharts(orderTrendOptions);
  };
  //分时支付/下单金额趋势end-----------------------------------------------------------

  //分时会员/店铺新增趋势start----------------------------------
  const { register: newMemberStore_register, setCharts: newMemberStore_setCharts } =
    useStatCharts();
  const handleMemberStoreOption = (data) => {
    member_store_options.xAxisData = data.newMemberNumList.map((or) => or.hour);
    const data1 = data.newMemberNumList.map((or) => or.num);
    const data2 = data.newStoreNumList.map((or) => or.num);

    member_store_options.series = [
      { name: '新增会员数', data: data1, type: 'line' },
      { name: '新增店铺数', data: data2, type: 'line' },
    ];
    member_store_options.maxMount = Math.max.apply(null, [...data1, ...data2]);
    newMemberStore_setCharts(member_store_options);
  };
  //分时会员/店铺新增趋势end----------------------------------

  //分时流量趋势start--------------------------------
  const { register: visit_view_register, setCharts: visit_view_setCharts } = useStatCharts();
  const handleVisitViewOption = (data) => {
    visit_view_options.xAxisData = data.visitorNumList.map((or) => or.hour);
    const data1 = data.visitorNumList.map((or) => or.num);
    const data2 = data.viewNumList.map((or) => or.num);

    visit_view_options.series = [
      { name: '访客', data: data1, type: 'line' },
      { name: '浏览量', data: data2, type: 'line' },
    ];
    visit_view_options.maxMount = Math.max.apply(null, [...data1, ...data2]);
    visit_view_setCharts(visit_view_options);
  };
  //分时流量趋势end--------------------------------

  onMounted(() => {
    getPresentData();
  });
</script>

<style scoped lang="less">
  .position-title {
    position: relative;
    height: 15px;
    padding-left: 8px;
    border-left: 4px solid @primary-color;
    color: #3a3e43;
    font-size: 14px;
    font-weight: 700;
    line-height: 15px;
    white-space: nowrap;
  }

  .stat-num_stat_item {
    box-sizing: border-box;
    align-items: stretch;
    width: 100%;
    height: 100%;
    padding: 20px 15px;
    border-top: 1px solid hsl(0deg 0% 84.7% / 50%);
  }

  .stat-left_side {
    flex: 0 0 100px;
    background-color: #fff7f4;
  }

  .stat-right_main_item {
    box-sizing: border-box;
    width: 216px;
    height: 90px;
    margin-left: 15px;
    padding-left: 30px;
    border-radius: 2px;
    background-color: hsl(0deg 0% 93.3% / 20%);
  }
</style>
