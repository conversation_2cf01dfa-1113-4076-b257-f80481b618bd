import { PriceCustomRender, TextHiddenCustomRender } from '/@/components/SldStat';
import { computed, ref } from 'vue';
import { getCateDayReport, getCateCateReport } from '@/api/sys/statAnalysis';
import { sldConvert } from '/@/utils';
export const handleReport = () => {
  const reportOptionsSet = ref([
    {
      name: '按天',
      key: 'byDay',
      apiFunc: getCateDayReport,
      columns: [
        { title: '时间', dataIndex: 'statsTime', sorter: true },
        { title: '一级分类名称', dataIndex: 'categoryName' },
        { title: '下单数', dataIndex: 'orderSubmitNum', sorter: true },
        {
          title: '下单金额',
          dataIndex: 'orderSubmitAmount',
          sorter: true,
          customRender: PriceCustomRender,
        },
        { title: '支付订单数', dataIndex: 'orderPayNum', sorter: true, width: 100 },
        { title: '销售额', dataIndex: 'saleAmount', sorter: true, width: 100 },
        { title: '销售额占比', dataIndex: 'saleAmountPercent', sorter: true, width: 100 },
        {
          title: '销冠商品',
          dataIndex: 'goodsName',
          width: 100,
          helpMessage: '所选时间段内销售额最高的商品',
          customRender: TextHiddenCustomRender,
        },
        { title: '退单数', dataIndex: 'returnNum', sorter: true, width: 100 },
        {
          title: '退单金额',
          dataIndex: 'returnAmount',
          sorter: true,
          width: 100,
          customRender: PriceCustomRender,
        },
      ],
      searchData: [
        {
          field: 'categoryName',
          component: 'Input',
          colProps: { span: 6, style: 'width:280px;max-width:100%' },
          componentProps: {
            maxlength: 6,
            placeholder: '请输入一级分类名称',
            size: 'default',
          },
          label: '一级分类名称',
          labelWidth: 140,
        },
      ],
      beforeFetch ({ startTime, endTime, field, order, current, categoryName, pageSize }) {
        let paramLocal = { startTime, endTime, current, categoryName, pageSize };
        if (order) {
          paramLocal.sort = sldConvert(field);
          paramLocal.type = order == 'descend' ? 'desc' : 'asc';
        }
        return paramLocal;
      },
      isColumnIndex: false,
    },
    {
      name: '按品类',
      key: 'byCate',
      apiFunc: getCateCateReport,
      columns: [
        { title: '一级分类名称', dataIndex: 'categoryName', width: 100 },
        { title: '下单数', dataIndex: 'orderSubmitNum', sorter: true, width: 100 },
        {
          title: '下单金额',
          dataIndex: 'orderSubmitAmount',
          sorter: true,
          width: 100,
          customRender: PriceCustomRender,
        },
        { title: '支付订单数', dataIndex: 'orderPayNum', sorter: true, width: 100 },
        {
          title: '销售额',
          dataIndex: 'saleAmount',
          sorter: true,
          width: 100,
          customRender: PriceCustomRender,
        },
        { title: '销售额占比', dataIndex: 'saleAmountPercent', sorter: true, width: 100 },
        {
          title: '销冠商品',
          dataIndex: 'goodsName',
          width: 100,
          helpMessage: '所选时间段内销售额最高的商品',
          customRender: TextHiddenCustomRender,
        },
        { title: '退单数', dataIndex: 'returnNum', sorter: true, width: 100 },
        {
          title: '退单金额',
          dataIndex: 'returnAmount',
          sorter: true,
          width: 100,
          customRender: PriceCustomRender,
        },
      ],
      searchData: [
        {
          field: 'categoryName',
          component: 'Input',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            maxlength: 6,
            placeholder: '请输入一级分类名称',
            size: 'default',
          },
          label: '一级分类名称',
          labelWidth: 100,
        },
      ],
      beforeFetch ({ startTime, endTime, field, order, current, categoryName, pageSize }) {
        let paramLocal = { startTime, endTime, current, categoryName, pageSize };
        if (order) {
          paramLocal.sort = field;
          paramLocal.type = order == 'descend' ? 'desc' : 'asc';
        }
        return paramLocal;
      },
      isColumnIndex: true,
    },
  ]);
  const currentExportOption = computed(() => ({
    api:
      reportTabValue.value == 'byDay'
        ? '/statistics/admin/category/analysis/dayExport'
        : '/statistics/admin/category/analysis/categoryExport',
    fileName: '品类销售报表',
  }));
  const reportTabValue = ref('byDay');
  return {
    reportOptionsSet,
    currentExportOption,
    reportTabValue,
  };
};
