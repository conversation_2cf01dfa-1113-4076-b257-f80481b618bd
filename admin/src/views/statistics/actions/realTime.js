import { getImagePath } from '/@/utils';
export const realTimeData1 = {
  icon: getImagePath('images/real_icon_1.png'),
  title: `${'平台汇总'}`,
  list: [
    {
      name: `${'销售总额'}`,
      value: 0,
      isHelpIcon: false,
      tip: ``,
      mapKey: 'orderPayAmountTotal',
      isMoney: true,
    },
    {
      name: `${'会员总数'}`,
      value: 0,
      isHelpIcon: true,
      tip: `${'截止至当前时间，全平台注册会员数'}`,
      mapKey: 'memberNum',
    },
    {
      name: `${'店铺总数'}`,
      value: 0,
      isHelpIcon: true,
      tip: `${'截止至当前时间，全平台商家总数，包括自营商家和入驻商家'}`,
      mapKey: 'storeNum',
    },
    {
      name: `${'在售商品数'}`,
      value: 0,
      isHelpIcon: true,
      tip: `${'截止至当前时间，状态为在售的商品数量'}`,
      mapKey: 'saleGoodsNum',
    },
  ],
};
export const realTimeData2 = {
  icon: getImagePath('images/real_icon_2.png'),
  title: `${'今日实时'}`,
  list: [
    {
      name: `${'今日销售额'}`,
      value: 0,
      isHelpIcon: false,
      tip: `${'今日0时至当前时间的销售额'}`,
      mapKey: 'orderPayAmount',
      isMoney: true,
    },
    {
      name: `${'新增会员数'}`,
      value: 0,
      isHelpIcon: true,
      tip: `${'统计时间内，全平台注册的人数总和'}`,
      mapKey: 'newMemberNum',
    },
    {
      name: `${'新增店铺数'}`,
      value: 0,
      isHelpIcon: true,
      tip: `${'统计时间内，全平台新增商家数'}`,
      mapKey: 'newStoreNum',
    },
    {
      name: `${'新增商品'}`,
      value: 0,
      isHelpIcon: true,
      tip: `${'统计时间内，全平台新增商品spu数'}`,
      mapKey: 'newGoodsNum',
    },
    {
      isBr: true,
    },
    {
      name: `${'访客数'}`,
      value: 0,
      isHelpIcon: true,
      tip: `${'统计时间内，全平台所有页面的去重人数总和'}`,
      mapKey: 'visitorNum',
    },
    {
      name: `${'浏览量'}`,
      value: 0,
      isHelpIcon: true,
      tip: `${'统计时间内，全平台所有页面被访问的次数总和'}`,
      mapKey: 'viewNum',
    },
    {
      name: `${'商品访客数'}`,
      value: 0,
      isHelpIcon: true,
      tip: `${'统计时间内，访问商品详情页的去重人数'}`,
      mapKey: 'goodsVisitorNum',
    },
    {
      name: `${'商品浏览量'}`,
      value: 0,
      isHelpIcon: true,
      tip: `${'统计时间内，访问商品详情页的人次数'}`,
      mapKey: 'goodsViewNum',
    },
    {
      isBr: true,
    },
    {
      name: `${'下单数'}`,
      value: 0,
      isHelpIcon: true,
      tip: `${'统计时间内，全平台用户成功提交订单的笔数总和'}`,
      mapKey: 'orderSubmitNum',
    },
    {
      name: `${'下单人数'}`,
      value: 0,
      isHelpIcon: true,
      tip: `${'统计时间内，全平台成功提交订单的去重人数总和'}`,
      mapKey: 'orderSubmitMemberNum',
    },
    {
      name: `${'下单金额'}`,
      value: 0,
      isHelpIcon: true,
      tip: `${'统计时间内，全平台用户成功提交订单的金额总和'}`,
      mapKey: 'orderSubmitAmount',
      isMoney: true,
    },
    {
      name: `${'下单客单价'}`,
      value: 0,
      isHelpIcon: true,
      tip: `${'统计时间内，全平台下单金额/下单人数'}`,
      mapKey: 'orderSubmitAtv',
      isMoney: true,
    },
    {
      name: `${'访问-下单转化率'}`,
      value: 0,
      isHelpIcon: true,
      tip: `${'统计时间内，全平台下单人数/平台访客数'}`,
      mapKey: 'pvSubmitRate',
      isPercent: true
    },
    {
      isBr: true,
    },
    {
      name: `${'支付订单数'}`,
      value: 0,
      isHelpIcon: true,
      tip: `${'统计时间内，全平台用户成功支付的订单数量总和'}`,
      mapKey: 'orderPayNum',
    },
    {
      name: `${'支付人数'}`,
      value: 0,
      isHelpIcon: true,
      tip: `${'统计时间内，全平台成功付款的去重人数总和'}`,
      mapKey: 'orderPayMemberNum',
    },
    {
      name: `${'支付金额'}`,
      value: 0,
      isHelpIcon: true,
      tip: `${'统计时间内，全平台用户成功支付的金额总和'}`,
      mapKey: 'orderPayAmount',
      isMoney: true,
    },
    {
      name: `${'支付客单价'}`,
      value: 0,
      isHelpIcon: true,
      tip: `${'统计时间内，全平台下单金额/下单人数'}`,
      mapKey: 'orderPayAtv',
      isMoney: true,
    },
    {
      name: `${'访问-支付转化率'}`,
      value: 0,
      isHelpIcon: true,
      tip: `${'统计时间内，全平台支付人数/平台访客数'}`,
      mapKey: 'pvPayRate',
      isPercent: true

    },
  ],
};
