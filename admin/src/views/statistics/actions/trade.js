import { ref, reactive, onMounted } from 'vue';
import { getTradeViewStat } from '@/api/sys/statAnalysis';
import areaJSON from '@/assets/json/area.json';
import { initialDateVar } from '/@/components/SldStat';
import { formatNumPieCircle } from '/@/utils';
export const tradeOverView = () => {
  const fieldMap = reactive([
    {
      children: [
        {
          label: `${'浏览量'}`,
          num: 0,
          differenceNum: '',
          isUp: false,
          tip: `${'统计时间内，全平台所有页面被访问的次数总和'}`,
          mapValue: 'viewNum',
          mapDifferentValue: 'previousViewNum',
        },
        {
          label: `${'访客数'}`,
          num: 0,
          differenceNum: '',
          isUp: false,
          tip: `${'统计时间内，全平台所有页面的去重人数总和'}`,
          mapValue: 'visitorNum',
          mapDifferentValue: 'previousVisitorNum',
        },
      ],
    },
    {
      children: [
        {
          label: `${'下单金额'}`,
          num: 0,
          differenceNum: '',
          isUp: false,
          tip: `${'统计时间内，全平台用户成功提交订单的金额总和'}`,
          mapValue: 'orderSubmitAmount',
          mapDifferentValue: 'previousOrderSubmitAmount',
          isMoney: true,
        },
        {
          label: `${'下单数'}`,
          num: 0,
          differenceNum: '',
          isUp: false,
          tip: `${'统计时间内，全平台用户成功提交订单的笔数总和'}`,
          mapValue: 'orderSubmitNum',
          mapDifferentValue: 'previousOrderSubmitNum',
        },
        {
          label: `${'下单人数'}`,
          num: 0,
          differenceNum: '',
          isUp: false,
          tip: `${'统计时间内，全平台成功提交订单的去重人数总和'}`,
          mapValue: 'orderSubmitMemberNum',
          mapDifferentValue: 'previousOrderSubmitMemberNum',
        },
        {
          label: `${'下单客单价'}`,
          num: 0,
          differenceNum: '',
          isUp: false,
          tip: `${'统计时间内，全平台下单金额/下单人数'}`,
          mapValue: 'orderSubmitAtv',
          mapDifferentValue: 'previousOrderSubmitAtv',
          isMoney: true,
        },
      ],
    },
    {
      children: [
        {
          label: `${'支付金额'}`,
          num: 0,
          differenceNum: '',
          isUp: false,
          tip: `${'统计时间内，全平台用户成功支付的金额总和'}`,
          mapValue: 'orderPayAmount',
          mapDifferentValue: 'previousOrderPayAmount',
          isMoney: true,
        },
        {
          label: `${'支付订单数'}`,
          num: 0,
          differenceNum: '',
          isUp: false,
          tip: `${'统计时间内，全平台用户成功支付的订单数量总和'}`,
          mapValue: 'orderPayNum',
          mapDifferentValue: 'previousOrderPayNum',
        },
        {
          label: `${'支付人数'}`,
          num: 0,
          differenceNum: '',
          isUp: false,
          tip: `${'统计时间内，全平台成功付款的去重人数总和'}`,
          mapValue: 'orderPayMemberNum',
          mapDifferentValue: 'previousOrderPayMemberNum',
        },
        {
          label: `${'支付客单价'}`,
          num: 0,
          differenceNum: '',
          isUp: false,
          tip: `${'统计时间内，全平台支付金额/下单人数'}`,
          mapValue: 'orderPayAtv',
          mapDifferentValue: 'previousOrderPayAtv',
          isMoney: true,
        },
      ],
    },
  ]);
  let paramView = {};
  const chartsInfoData = reactive({});
  const getTradeOverView = async (params) => {
    const res = await getTradeViewStat(params);
    if (res?.state == 200) {
      chartsInfoData.pvPayRate = res.data.pvPayRate || '--';
      chartsInfoData.pvSubmitRate = res.data.pvSubmitRate || '--';
      chartsInfoData.submitPayRate = res.data.submitPayRate || '--';
      fieldMap.forEach((item, index) => {
        item.children.forEach((item2, index2) => {
          if (
            res.data[item2.mapDifferentValue] &&
            res.data[item2.mapDifferentValue].indexOf('-') != 0
          ) {
            //上涨
            item2['differenceNum'] = '+' + res.data[item2.mapDifferentValue];
            item2.isUp = true;
          } else {
            //下降
            item2['differenceNum'] = res.data[item2.mapDifferentValue];
            item2.isUp = false;
          }
          if (!res.data[item2.mapValue]) {
            item2['num'] = 0;
            return;
          }
          item2['num'] = item2.isMoney
            ? parseFloat(res.data[item2.mapValue]).toFixed(2)
            : res.data[item2.mapValue];
        });
      });
    }
  };
  const dateOverChange = (param) => {
    paramView = param;
    getTradeOverView(paramView);
  };
  const radioTerminalChange = ({ target }) => {
    const { value } = target;
    let radioParam = {};
    if (value != 'all') {
      radioParam.terminalType = value;
    }
    getTradeOverView({ ...paramView, ...radioParam });
  };
  onMounted(() => {
    paramView = initialDateVar;
    getTradeOverView(paramView);
  });
  return { fieldMap, getTradeOverView, chartsInfoData, dateOverChange, radioTerminalChange };
};
export const trade30Trend = (execApi, execDataHandler) => {
  const trade30Option = reactive({
    series: [],
    xAxisData: [],
    maxMount: 0,
  });
  const current30Radio = ref(0);
  const radio30Trend = [
    {
      title: '下单金额/支付金额',
      value: 0,
      list: [
        { name: '下单金额', key: 'orderSubmitAmount' },
        { name: '支付金额', key: 'orderPayAmount' },
      ],
    },
    {
      title: '下单数/支付订单数',
      value: 1,
      list: [
        { name: '下单数', key: 'orderSubmitNum' },
        { name: '支付订单数', key: 'orderPayNum' },
      ],
    },
    {
      title: '下单客单价/支付客单价',
      value: 2,
      list: [
        { name: '下单客单价', key: 'orderSubmitAtv' },
        { name: '支付客单价', key: 'orderPayAtv' },
      ],
    },
    {
      title: '浏览量',
      value: 3,
      list: [{ name: '浏览量', key: 'viewNum' }],
    },
    {
      title: '浏览-支付转化率',
      value: 4,
      list: [{ name: '浏览-支付转化率(%)', key: 'pvPayRate' }],
    },
  ];
  const trendDataHandler = (return_data) => {
    const target = radio30Trend[current30Radio.value];
    trade30Option.xAxisData = return_data.map((item) => item.statsTime);
    const maxList = [];
    trade30Option.series = [];
    target.list.forEach((tar) => {
      let dataList = return_data.map((item) => parseFloat(item[tar.key]));
      trade30Option.series.push({
        name: tar.name,
        data: dataList,
        type: 'bar',
        barGap: '0',
        barWidth: '22%',
      });
      maxList.push(Math.max.apply(null, dataList));
    });
    trade30Option.maxMount = Math.max.apply(null, maxList);
    return trade30Option;
  };
  const trend30RadioChange = () => {
    execDataHandler();
  };
  const trend30TerminalChange = ({ target }) => {
    execApi();
  };
  return {
    trendDataHandler,
    trade30Option,
    current30Radio,
    radio30Trend,
    trend30RadioChange,
    trend30TerminalChange,
  };
};
export const handleProvinceTrend = (execApi) => {
  const areaList = areaJSON;
  const currentProvince = ref(areaList[0]);
  const provinceSalesOptions = reactive({
    xAxisData: [],
    series: [],
    maxMount: 0,
  });
  const proviceTrendDataHandler = (return_data) => {
    provinceSalesOptions.xAxisData = return_data.orderAmountList.map((item) => item.statsTime);
    const orderAmountLists = return_data.orderAmountList.map((i) => i.orderPayAmount);
    const orderNumLists = return_data.orderNumList.map((i) => i.orderPayNum);
    provinceSalesOptions.maxMount = Math.max.apply(null, [...orderAmountLists, ...orderNumLists]);
    provinceSalesOptions.series = [
      {
        name: '销售额',
        data: orderAmountLists,
        type: 'line',
      },
      {
        name: '订单量',
        data: orderNumLists,
        type: 'line',
      },
    ];
    return provinceSalesOptions;
  };
  const proviceSelectChange = (e) => {
    currentProvince.value.regionCode = e;
    execApi();
  };
  return {
    proviceSelectChange,
    provinceSalesOptions,
    proviceTrendDataHandler,
    currentProvince,
    areaList,
  };
};
export const handleProvicePercent = (execDataHandler) => {
  const isAmountNum = ref(1);
  const provicePercentOption = reactive({
    series: [],
    isRing: false,
    isLegend: true,
  });
  const provincePercent_dataHandler = (return_data) => {
    const list = isAmountNum.value == 1 ? return_data.orderAmountList : return_data.orderNumList;
    provicePercentOption.series = list.map((i) => ({
      name: i.provinceName,
      value: isAmountNum.value == 1 ? i.orderPayAmount : i.orderPayNum,
    }));
    return provicePercentOption;
  };
  const provincePercentChange = () => {
    execDataHandler();
  };
  return {
    provicePercentOption,
    isAmountNum,
    provincePercent_dataHandler,
    provincePercentChange,
  };
};
export const handleTerminalTrend = (execDataHandler) => {
  const terminalTrendOptions = reactive({
    xAxisData: [],
    series: [],
    maxMount: 0,
  });
  const isAmountNumTerminalTrend = ref(1);
  const terminalTrendDataHandler = (return_data) => {
    const sortCate = {};
    const xAxisData = return_data.map((item) => item.statsTime);
    let maxMount = 0;
    for (const tar in xAxisData) {
      const { terminalList } = return_data[tar];
      for (const ter in terminalList) {
        const { terminalName, orderPayAmount, orderPayNum } = terminalList[ter];
        let fimount = isAmountNumTerminalTrend.value == 1 ? orderPayAmount : orderPayNum;
        sortCate[terminalName] = sortCate[terminalName] || [];
        sortCate[terminalName].push(fimount);
        maxMount = fimount > maxMount ? fimount : maxMount;
      }
    }
    const series = Object.keys(sortCate).map((key) => ({
      name: key,
      data: sortCate[key],
      type: 'line',
    }));
    terminalTrendOptions.maxMount = maxMount;
    terminalTrendOptions.series = series;
    terminalTrendOptions.xAxisData = xAxisData;
    return terminalTrendOptions;
  };
  const terminalTrendChange = () => {
    execDataHandler();
  };
  return {
    terminalTrendOptions,
    isAmountNumTerminalTrend,
    terminalTrendDataHandler,
    terminalTrendChange,
  };
};
export const handleTerminalPercent = (execDataHandler) => {
  const terminalPercentOption = reactive({
    series: [],
    isRing: true,
    isLegend: false,
    title: {
      text: '',
      subtext: 0,
      textStyle: {
        fontSize: 14,
      },
      subtextStyle: {
        color: '#333',
        fontSize: 26,
      },
    },
  });
  const isAmountNumTerminalPercent = ref(1);
  const terminalPercentDataHandler = (return_data) => {
    terminalPercentOption.series = return_data[
      isAmountNumTerminalPercent.value == 1 ? 'orderAmountList' : 'orderNumList'
    ].map((item) => ({
      name: item.terminalName,
      value: isAmountNumTerminalPercent.value == 1 ? item.orderPayAmount : item.orderPayNum,
    }));
    const totalNum = terminalPercentOption.series.reduce((total, prev) => total + prev.value, 0);
    terminalPercentOption.title.text =
      isAmountNumTerminalPercent.value == 1 ? '总销售额(元)' : '总订单量';
    terminalPercentOption.title.subtext =
      isAmountNumTerminalPercent.value == 1 ? formatNumPieCircle(totalNum) : totalNum;
    return terminalPercentOption;
  };
  const terminalPercentChange = () => {
    execDataHandler();
  };
  return {
    isAmountNumTerminalPercent,
    terminalPercentDataHandler,
    terminalPercentChange,
  };
};
