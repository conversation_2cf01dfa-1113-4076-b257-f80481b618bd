import moment from 'moment';
import { getImagePath } from '/@/utils';
import { onMounted, reactive, ref } from 'vue';
import { getStoreOverView, getStoreNum, getStoreReport } from '@/api/sys/statAnalysis';
import { PriceCustomRender } from '@/components/SldStat';
import { failTip } from '/@/utils/utils';

export const handleStoreOverView = () => {
  const storeNumData = reactive({
    name: `店铺总数`,
    isShowOperate: false,
    opValue: 0,
    num: 0,
    differenceNum: '',
    bg: { icon: 'store_icon2', color: 'green' },
    mapValue: 'storeNum',
    mapDifferentValue: '',
    isDifferenceShow: false,
    isUp: false,
  });
  const storePreviewData = ref([
    {
      name: `新增店铺数`,
      isShowOperate: true,
      opValue: 0,
      num: 0,
      differenceNum: '',
      bg: { icon: 'store_icon1', color: 'yellow' },
      mapValue: 'newStoreNum',
      mapDifferentValue: 'preNewStore',
      isDifferenceShow: true,
      isUp: false,
    },
    {
      name: `访客数`,
      isShowOperate: true,
      opValue: 0,
      num: 0,
      differenceNum: '',
      bg: { icon: 'user_icon1', color: 'blue' },
      mapValue: 'visitorNum',
      mapDifferentValue: 'preVisitor',
      isDifferenceShow: true,
      isUp: false,
    },
    {
      name: `浏览量`,
      isShowOperate: true,
      opValue: 0,
      num: 0,
      differenceNum: '',
      bg: { icon: 'user_icon2', color: 'purple' },
      mapValue: 'viewNum',
      mapDifferentValue: 'preView',
      isDifferenceShow: true,
      isUp: false,
    },
  ]);
  const getStoreNumData = async () => {
    const res = await getStoreNum();
    storeNumData.num = res.data.storeNum;
  };
  const getStoreOverViewData = async (params, itemIndex) => {
    const res = await getStoreOverView(params);
    if (res?.state == 200) {
      let item;
      for (let i = 0, len = storePreviewData.value.length; i < len; i++) {
        if (itemIndex != undefined) {
          item = storePreviewData.value[itemIndex - 1];
        } else {
          item = storePreviewData.value[i];
        }
        if (
          res.data[item.mapDifferentValue] &&
          res.data[item.mapDifferentValue].indexOf('-') === 0
        ) {
          //较上期下降
          item.differenceNum = res.data[item.mapDifferentValue];
          item.isUp = false;
        } else {
          //较上期上涨
          item.differenceNum = '+' + (res.data[item.mapDifferentValue] || '0');
          item.isUp = true;
        }
        item.num = res.data[item.mapValue];
        if (itemIndex != undefined) {
          break;
        }
      }
    } else {
      failTip(res.msg);
    }
  };
  const selectData = [
    {
      label: '昨天',
      param: {
        startTime: moment().subtract(1, 'days').format('YYYY-MM-DD') + ' 00:00:00',
        endTime: moment().subtract(1, 'days').format('YYYY-MM-DD') + ' 23:59:59:999',
      },
    },
    {
      label: '近7天',
      param: {
        startTime: moment().subtract(7, 'days').format('YYYY-MM-DD') + ' 00:00:00',
        endTime: moment().subtract(1, 'days').format('YYYY-MM-DD') + ' 23:59:59:999',
      },
    },
    {
      label: '近30天',
      param: {
        startTime: moment().subtract(30, 'days').format('YYYY-MM-DD') + ' 00:00:00',
        endTime: moment().subtract(1, 'days').format('YYYY-MM-DD') + ' 23:59:59:999',
      },
    },
  ];
  const changeSelData = (op, opdx, itemIndex) => {
    op.opValue = opdx;
    getStoreOverViewData(selectData[opdx].param, itemIndex);
  };
  onMounted(() => {
    getStoreOverViewData(selectData[0].param);
    getStoreNumData();
  });
  return { storePreviewData, getStoreOverViewData, selectData, changeSelData, storeNumData };
};
export const handleReport = () => {
  const storeReportOption = [
    {
      title: `店铺名称`,
      dataIndex: 'storeName',
      sorter: true,
      width: 100,
    },
    {
      title: `支付金额`,
      dataIndex: 'orderPayAmount',
      sorter: true,
      width: 100,
      customRender: PriceCustomRender,
    },
    {
      title: `下单金额`,
      dataIndex: 'orderSubmitNum',
      sorter: true,
      width: 100,
      customRender: PriceCustomRender,
    },
    {
      title: `支付订单数`,
      dataIndex: 'orderPayNum',
      sorter: true,
      width: 100,
    },
    {
      title: `下单数`,
      dataIndex: 'orderSubmitNum',
      sorter: true,
      width: 100,
    },
    {
      title: `店铺访客数`,
      dataIndex: 'visitorNum',
      sorter: true,
      width: 100,
    },
    {
      title: `店铺浏览量`,
      dataIndex: 'viewNum',
      sorter: true,
      width: 100,
    },
    {
      title: `商品访客数`,
      dataIndex: 'goodsVisitorNum',
      sorter: true,
      width: 100,
    },
    {
      title: `商品浏览量`,
      dataIndex: 'goodsViewNum',
      sorter: true,
      width: 100,
    },
    {
      title: `被访问商品数`,
      dataIndex: 'viewGoodsNum',
      sorter: true,
      width: 100,
    },
    {
      title: `动销商品数`,
      dataIndex: 'salingGoodsNum',
      sorter: true,
      width: 100,
    },
    {
      title: `退单数`,
      dataIndex: 'returnNum',
      sorter: true,
      width: 100,
    },
    {
      title: `退单金额`,
      dataIndex: 'returnAmount',
      sorter: true,
      width: 100,
      customRender: PriceCustomRender,
    },
  ];
  const reportOptionsSet = ref({
    apiFunc: getStoreReport,
    columns: storeReportOption,
    searchData: [
      {
        field: 'storeName',
        component: 'Input',
        colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
        componentProps: {
          minWidth: 300,
          maxlength: 6,
          placeholder: '请输入店铺名称',
          size: 'default',
        },
        label: '店铺名称',
        labelWidth: 80,
      },
    ],
    isColumnIndex: true,
    beforeFetch ({ startTime, endTime, field, order, current, storeName, pageSize }) {
      let paramLocal = { startTime, endTime, current, pageSize };
      if (storeName) paramLocal.storeName = storeName;
      if (order) {
        paramLocal.sort = field;
        paramLocal.type = order == 'descend' ? 'desc' : 'asc';
      }
      return paramLocal;
    },
  });
  const currentExportOption = {
    api: '/statistics/admin/store/analysis/export',
    fileName: '店铺报表',
  };
  return {
    currentExportOption,
    reportOptionsSet,
  };
};
