import { computed, onMounted, reactive, ref } from 'vue';
import { getFlowReport, getStoreFlowReport, getFlowOverView } from '/@/api/sys/statAnalysis';
import { initialDateVar } from '/@/components/SldStat';
import { failTip } from '/@/utils/utils';

export const trade30FlowTrend = (execApi, execDataHandler) => {
  const trade30Option = reactive({
    series: [],
    xAxisData: [],
    maxMount: 0,
  });
  const current30Radio = ref(0);
  const radio30Trend = [
    {
      title: '访客数/新访客数',
      value: 0,
      list: [
        { name: '访客数', key: 'visitorNum' },
        { name: '新访客数', key: 'newVisitorNum' },
      ],
    },
    {
      title: '下单人数/支付人数',
      value: 1,
      list: [
        { name: '下单人数', key: 'orderSubmitMemberNum' },
        { name: '支付人数', key: 'orderPayMemberNum' },
      ],
    },
    {
      title: '下单数/支付订单数',
      value: 2,
      list: [
        { name: '下单数', key: 'orderSubmitNum' },
        { name: '支付订单数', key: 'orderPayNum' },
      ],
    },
    {
      title: '浏览量',
      value: 3,
      list: [{ name: '浏览量', key: 'viewNum' }],
    },
    {
      title: '浏览-付款转化率',
      value: 4,
      list: [{ name: '浏览-付款转化率(%)', key: 'pvPayRate' }],
    },
  ];
  const trendDataHandler = (return_data) => {
    const target = radio30Trend[current30Radio.value];
    trade30Option.xAxisData = return_data.map((item) => item.statsTime);
    const maxList = [];
    trade30Option.series = [];
    target.list.forEach((tar) => {
      let dataList = return_data.map((item) => parseFloat(item[tar.key]));
      trade30Option.series.push({
        name: tar.name,
        data: dataList,
        type: 'bar',
        barGap: '0',
        barWidth: '22%',
      });
      maxList.push(Math.max.apply(null, dataList));
    });
    trade30Option.maxMount = Math.max.apply(null, maxList);
    return trade30Option;
  };
  const trend30RadioChange = () => {
    execDataHandler();
  };
  const trend30TerminalChange = () => {
    execApi();
  };
  return {
    trade30Option,
    current30Radio,
    radio30Trend,
    trend30RadioChange,
    trend30TerminalChange,
    trendDataHandler,
  };
};
export const handleFlowReport = () => {
  const dayReportOption = [
    {
      title: `${'时间'}`,
      dataIndex: 'statsTime',
      sorter: true,
      width: 60,
    },
    {
      title: `${'平台访客数'}`,
      dataIndex: 'visitorNum',
      sorter: true,
      width: 80,
    },
    {
      title: `${'平台浏览量'}`,
      dataIndex: 'viewNum',
      sorter: true,
      width: 80,
    },
    {
      title: `${'店铺访客数'}`,
      dataIndex: 'storeVisitorNum',
      sorter: true,
      width: 80,
    },
    {
      title: `${'店铺浏览量'}`,
      dataIndex: 'storeViewNum',
      sorter: true,
      width: 80,
    },
    {
      title: `${'商品访客数'}`,
      dataIndex: 'goodsVisitorNum',
      sorter: true,
      width: 80,
    },
    {
      title: `${'商品浏览量'}`,
      dataIndex: 'goodsViewNum',
      sorter: true,
      width: 80,
    },
  ];
  const storeReportOption = [
    {
      title: `${'店铺名称'}`,
      dataIndex: 'storeName',
      sorter: true,
      width: 60,
    },
    {
      title: `${'店铺访客数'}`,
      dataIndex: 'storeVisitorNum',
      sorter: true,
      width: 80,
    },
    {
      title: `${'店铺浏览量'}`,
      dataIndex: 'storeViewNum',
      sorter: true,
      width: 80,
    },
    {
      title: `${'商品访客数'}`,
      dataIndex: 'goodsVisitorNum',
      sorter: true,
      width: 80,
    },
    {
      title: `${'商品浏览量'}`,
      dataIndex: 'goodsViewNum',
      sorter: true,
      width: 80,
    },
  ];
  const reportOptionsSet = ref([
    {
      name: '按天',
      key: 'byDay',
      apiFunc: getFlowReport,
      columns: dayReportOption,
      isColumnIndex: false,
      beforeFetch({ startTime, endTime, field, order, current, pageSize }) {
        let paramLocal = { startTime, endTime, current, pageSize };
        if (order) {
          paramLocal.sort = field;
          paramLocal.type = order == 'descend' ? 'desc' : 'asc';
        }
        return paramLocal;
      },
    },
    {
      name: '按店铺',
      key: 'byStore',
      apiFunc: getStoreFlowReport,
      columns: storeReportOption,
      searchData: [
        {
          field: 'storeName',
          component: 'Input',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            maxlength: 6,
            placeholder: '请输入店铺名称',
            size: 'default',
          },
          label: '店铺名称',
          labelWidth: 80,
        },
      ],
      isColumnIndex: true,
      beforeFetch({ startTime, endTime, field, order, current, storeName, pageSize }) {
        let paramLocal = { startTime, endTime, current, storeName, pageSize };
        if (order) {
          paramLocal.sort = field;
          paramLocal.type = order == 'descend' ? 'desc' : 'asc';
        }
        return paramLocal;
      },
    },
  ]);
  const reportTabValue = ref('byDay');
  const currentExportOption = computed(() => ({
    api:
      reportTabValue.value == 'byDay'
        ? '/statistics/admin/flow/analysis/dayExport'
        : '/statistics/admin/flow/analysis/storeExport',
    fileName: '流量报表',
  }));
  return {
    currentExportOption,
    reportOptionsSet,
    reportTabValue,
  };
};
export const flowOverView = () => {
  const fieldMap = reactive([
    {
      children: [
        {
          label: `${'访客数'}`,
          num: 0,
          differenceNum: '',
          isUp: false,
          tip: `${'统计时间内，全平台所有页面的去重人数总和'}`,
          mapValue: 'visitorNum',
          mapDifferentValue: 'previousVisitorNum',
        },
        {
          label: `${'浏览量'}`,
          num: 0,
          differenceNum: '',
          isUp: false,
          tip: `${'统计时间内，全平台所有页面被访问的次数总和'}`,
          mapValue: 'viewNum',
          mapDifferentValue: 'previousViewNum',
        },
        {
          label: `${'新访客数'}`,
          num: 0,
          differenceNum: '',
          isUp: false,
          tip: `${'统计时间内，首次在平台发生浏览行为的人数'}`,
          mapValue: 'newVisitorNum',
          mapDifferentValue: 'previousNewVisitorNum',
        },
      ],
    },
    {
      children: [
        {
          label: `${'下单人数'}`,
          num: 0,
          differenceNum: '',
          isUp: false,
          tip: `${'统计时间内，全平台成功提交订单的去重人数总和'}`,
          mapValue: 'orderSubmitMemberNum',
          mapDifferentValue: 'previousOrderSubmitMemberNum',
        },
        {
          label: `${'下单数'}`,
          num: 0,
          differenceNum: '',
          isUp: false,
          tip: `${'统计时间内，全平台用户成功提交订单的笔数总和'}`,
          mapValue: 'orderSubmitNum',
          mapDifferentValue: 'previousOrderSubmitNum',
        },
      ],
    },
    {
      children: [
        {
          label: `${'支付人数'}`,
          num: 0,
          differenceNum: '',
          isUp: false,
          tip: `${'统计时间内，全平台成功付款的去重人数总和'}`,
          mapValue: 'orderPayMemberNum',
          mapDifferentValue: 'previousOrderPayMemberNum',
        },
        {
          label: `${'支付订单数'}`,
          num: 0,
          differenceNum: '',
          isUp: false,
          tip: `${'统计时间内，全平台用户成功支付的订单数量总和'}`,
          mapValue: 'orderPayNum',
          mapDifferentValue: 'previousOrderPayNum',
        },
      ],
    },
  ]);
  let paramView = {};
  const chartsInfoData = reactive({});
  const getTradeOverView = async (params) => {
    const res = await getFlowOverView(params);
    if (res?.state == 200) {
      chartsInfoData.pvPayRate = res.data.pvPayRate || '--';
      chartsInfoData.pvSubmitRate = res.data.pvSubmitRate || '--';
      chartsInfoData.submitPayRate = res.data.submitPayRate || '--';
      fieldMap.forEach((item, index) => {
        item.children.forEach((item2, index2) => {
          if (
            res.data[item2.mapDifferentValue] &&
            res.data[item2.mapDifferentValue].indexOf('-') != 0
          ) {
            //上涨
            item2['differenceNum'] = '+' + res.data[item2.mapDifferentValue];
            item2.isUp = true;
          } else {
            //下降
            item2['differenceNum'] = res.data[item2.mapDifferentValue];
            item2.isUp = false;
          }
          if (!res.data[item2.mapValue]) {
            item2['num'] = 0;
            return;
          }
          item2['num'] = res.data[item2.mapValue];
        });
      });
    } else {
      failTip(res.msg);
    }
  };
  const dateOverChange = (param) => {
    paramView = param;
    getTradeOverView(paramView);
  };
  const radioTerminalChange = ({ target }) => {
    const { value } = target;
    let radioParam = {};
    if (value != 'all') {
      radioParam.terminalType = value;
    }
    getTradeOverView({ ...paramView, ...radioParam });
  };
  onMounted(() => {
    paramView = initialDateVar;
    getTradeOverView(paramView);
  });
  return { fieldMap, getTradeOverView, chartsInfoData, dateOverChange, radioTerminalChange };
};
