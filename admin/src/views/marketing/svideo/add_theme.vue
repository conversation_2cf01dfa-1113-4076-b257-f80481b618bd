<template>
  <div class="section_padding add_theme">
    <div class="section_padding_back full_activity">
      <SldComHeader :title="pageTitle" />
      <div class="com_line" style="height: 1px"></div>
      <Spin :spinning="loading">
        <div class="add_theme_height">
          <Form layout="inline" ref="formRef" :model="theme_detail">
            <div class="full_acm_activity flex_column_start_start">
              <!-- 封面图片 start -->
              <div class="item flex_row_start_start">
                <div class="left"> <span style="color: red">*</span>封面图片 </div>
                <div class="right">
                  <Form.Item extra="建议上传宽710*高345的图片" style="width: 400px">
                    <Upload
                      :maxCount="1"
                      accept=" .gif, .jpeg, .png, .jpg,"
                      name="file"
                      @click="beforeUploadClick"
                      :action="`${apiUrl}/v3/oss/admin/upload?source=setting`"
                      listType="picture-card"
                      :file-list="img_data[0].fileList"
                      :beforeUpload="(file) => beforeUpload(file, ' .gif, .jpeg, .png, .jpg,', 20)"
                      @change="(e) => handleFileChange(e, 'carouselImage')"
                      :headers="{
                        Authorization: imgToken,
                      }"
                    >
                      <div v-if="img_data[0].fileList.length < 1">
                        <PlusOutlined />
                        <div className="ant-upload-text">上传图片</div>
                      </div>
                    </Upload>
                  </Form.Item>
                </div>
              </div>
              <!-- 封面图片 end -->
              <!-- 主题图片 start -->
              <div class="item flex_row_start_start">
                <div class="left"> <span style="color: red">*</span>主题图片 </div>
                <div class="right">
                  <Form.Item extra="建议上传宽710*高345的图片" style="width: 400px">
                    <Upload
                      :maxCount="1"
                      accept=" .gif, .jpeg, .png, .jpg,"
                      name="file"
                      @click="beforeUploadClick"
                      :action="`${apiUrl}/v3/oss/admin/upload?source=setting`"
                      listType="picture-card"
                      :file-list="img_data[1].fileList"
                      :beforeUpload="(file) => beforeUpload(file, ' .gif, .jpeg, .png, .jpg,', 20)"
                      @change="(e) => handleFileChange(e, 'image')"
                      :headers="{
                        Authorization: imgToken,
                      }"
                    >
                      <div v-if="img_data[1].fileList.length < 1">
                        <PlusOutlined />
                        <div className="ant-upload-text">上传图片</div>
                      </div>
                    </Upload>
                  </Form.Item>
                </div>
              </div>
              <!-- 主题图片 end -->
              <!-- 主题名称 start -->
              <div class="item flex_row_start_start">
                <div class="left"> <span style="color: red">*</span>主题名称 </div>
                <div class="right">
                  <Form.Item
                    extra="最多可输入5个字"
                    name="themeName"
                    style="width: 400px"
                    :rules="[
                      {
                        required: true,
                        whitespace: true,
                        message: `请输入主题名称`,
                      },
                    ]"
                  >
                    <Input
                      :maxLength="5"
                      style="width: 400px !important"
                      placeholder="请输入主题名称"
                      v-model:value="theme_detail.themeName"
                    />
                  </Form.Item>
                </div>
              </div>
              <!-- 主题名称 end -->
              <!-- 排序 start -->
              <div class="item flex_row_start_start">
                <div class="left"> <span style="color: red">*</span>排序 </div>
                <div class="right">
                  <Form.Item
                    extra="请输入0~255之间的数字，值越小，显示越靠前"
                    name="sort"
                    style="width: 400px"
                    :rules="[
                      {
                        required: true,
                        message: `请输入排序`,
                      },
                    ]"
                  >
                    <InputNumber
                      :max="255"
                      :min="0"
                      style="width: 400px !important"
                      :precision="0"
                      placeholder="请输入排序"
                      v-model:value="theme_detail.sort"
                    />
                  </Form.Item>
                </div>
              </div>
              <!-- 排序 end -->
              <!-- 是否显示 start -->
              <div class="item flex_row_start_start">
                <div class="left"> <span style="color: red">*</span>是否显示 </div>
                <div class="right">
                  <Form.Item name="isShow" style="width: 400px">
                    <RadioGroup size="small" v-model:value="theme_detail.isShow">
                      <Radio :value="1">显示</Radio>
                      <Radio :value="0">不显示</Radio>
                    </RadioGroup>
                  </Form.Item>
                </div>
              </div>
              <!-- 是否显示 end -->
              <!-- 选择所属标签 start -->
              <div class="item flex_row_start_start">
                <div class="left"> <span style="color: red">*</span>选择所属标签 </div>
                <div class="right">
                  <Form.Item
                    name="labelId"
                    style="width: 400px"
                    :rules="[
                      {
                        required: true,
                        message: `请选择所属标签`,
                      },
                    ]"
                  >
                    <Select
                      placeholder="请选择所属标签"
                      style="width: 400px"
                      :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                      v-model:value="theme_detail.labelId"
                    >
                      <Select.Option
                        v-for="(item, index) in label_data"
                        :key="index"
                        :value="item.labelId"
                        >{{ item.labelName }}</Select.Option
                      >
                    </Select>
                  </Form.Item>
                </div>
              </div>
              <!-- 选择所属标签 end -->
              <!-- 选择作品 start -->
              <div class="item flex_row_start_start">
                <div class="left"> <span style="color: red">*</span>选择作品 </div>
                <div class="right">
                  <Form.Item style="width: 400px">
                    <div class="reset_sel" @click="resetSelVideo">
                      {{ selectedRows.length > 0 ? '重新选择' : '选择作品' }}
                    </div>
                    <div>
                      <BasicTable
                        v-if="selectedRows.length > 0"
                        style="width: 800px"
                        rowKey="goodsId"
                        :columns="columns_spu"
                        :dataSource="selectedRows"
                        :pagination="false"
                        :bordered="true"
                        :canResize="false"
                        :maxHeight="200"
                        :ellipsis="false"
                        :actionColumn="{
                          title: '操作',
                          width: 80,
                          dataIndex: 'action',
                        }"
                      >
                        <template #bodyCell="{ column, text, record }">
                          <template v-if="column.dataIndex == 'videoImage'">
                            <Popover placement="rightTop">
                              <template #content>
                                <div style="width: 200px">
                                  <img
                                    :src="text"
                                    alt=""
                                    style="max-width: 100%; max-height: 100%"
                                  />
                                </div>
                              </template>
                              <div class="business_load_img">
                                <img :src="text" alt="" />
                              </div>
                            </Popover>
                          </template>
                          <template v-if="column.dataIndex === 'action'">
                            <TableAction
                              :actions="[
                                {
                                  label: '预览',
                                  onClick: prevVideo.bind(null, record),
                                },
                                {
                                  label: '删除',
                                  onClick: delVideo.bind(null, record.videoId),
                                },
                              ]"
                            />
                          </template>
                        </template>
                      </BasicTable>
                    </div>
                  </Form.Item>
                </div>
              </div>
              <!-- 选择作品 end -->
            </div>
            <div style="width: 100%; height: 20px; background: #fff"></div>
          </Form>
        </div>
        <div
          class="m_diy_bottom_wrap"
          :style="{ position: 'fixed', left: getRealWidth + 10 + 'px' }"
        >
          <div class="add_goods_bottom_btn" @click="goBack"> 返回 </div>
          <div class="add_goods_bottom_btn add_goods_bottom_btn_sel" @click="handleSaveAllData">
            保存
          </div>
        </div>
      </Spin>
    </div>
    <SldModal
      :width="600"
      title="预览"
      :visible="modalVisible"
      :height="600"
      :content="operateData"
      :showFoot="false"
      @cancle-event="handleModalCancle"
    />
    <SldSelMoreLeftRightSvideo
      @confirm-event="seleSvideo"
      @cancle-event="handleModalCancle"
      :width="1000"
      :selectedRow="sele_more_svideo.info"
      :selectedRowKey="sele_more_svideo.ids"
      :title="sle_more_title"
      :height="height - 400"
      :modalVisible="modalVisibleGoods"
      :extra="sele_more_svideo"
    >
    </SldSelMoreLeftRightSvideo>
  </div>
</template>
<script setup>
  import { ref, onMounted } from 'vue';
  import {
    Spin,
    Form,
    Upload,
    Input,
    InputNumber,
    RadioGroup,
    Radio,
    Select,
  } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import SldSelMoreLeftRightSvideo from '/@/components/SldSelMoreLeftRightSvideo/index.vue';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import SldModal from '/@/components/SldModal/index.vue';
  import { useGlobSetting } from '/@/hooks/setting';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import { useRoute, useRouter } from 'vue-router';
  import { getVideoLabelListApi } from '/@/api/svideo/label';
  import { useUserStore } from '/@/store/modules/user';
  import {
    getVideoThemeDetailApi,
    getVideoThemeUpdateApi,
    getVideoThemeAddApi,
  } from '/@/api/svideo/theme';
  import { list_com_page_more, beforeUpload, failTip, sucTip } from '/@/utils/utils';
  import { getToken } from '/@/utils/auth';
  import { pageClose } from '/@/utils/utils';
  import { useMultipleTabStore } from '/@/store/modules/multipleTab';

  const { apiUrl } = useGlobSetting();
  const imgToken = ref('Bearer ' + getToken() || ''); //上传文件token参数
  const { getRealWidth } = useMenuSetting();

  const route = useRoute();
  const tabStore = useMultipleTabStore();
  const router = useRouter();
  const userStore = useUserStore();

  const loading = ref(false);
  const formRef = ref();
  const operateData = ref([]);

  const pageTitle = ref(
    route.query != undefined && route.query.id != undefined && route.query.id
      ? `编辑推荐主题`
      : `新增推荐主题`,
  );
  const showItem = ref({});
  const isFirstLoading = ref(true); //是否第一次加载
  const theme_detail = ref({
    isShow: 1,
  }); //推荐主题详情
  const label_data = ref([]); //标签列表
  const height = ref(document.body.clientHeight);
  const sle_more_title = ref(''); //选择商品的标题
  const modalVisible = ref(false);
  const modalVisibleGoods = ref(false);
  const selectedRows = ref([]);
  const selectedRowKeys = ref([]); //selectedRows的key
  const img_data = ref([
    {
      name: 'carouselImage',
      fileList: [],
      img_succ_info: {},
    },
    {
      name: 'image',
      fileList: [],
      img_succ_info: {},
    },
  ]);
  const columns_spu = ref([
    {
      title: `封面图`,
      dataIndex: 'videoImage',
      align: 'center',
      width: 100,
    },
    {
      title: `作品名称`,
      dataIndex: 'videoName',
      align: 'center',
      width: 100,
      customRender: ({ text }) => {
        return text ? text : '--';
      },
    },
    {
      title: `作品简介`,
      dataIndex: 'introduction',
      align: 'center',
      width: 100,
      customRender: ({ text }) => {
        return text ? text : '--';
      },
    },
    {
      title: `作品类型`,
      dataIndex: 'videoTypeValue',
      align: 'center',
      width: 100,
      customRender: ({ text }) => {
        return text ? text : '--';
      },
    },
  ]);

  const sele_more_svideo = ref({
    info: [], //选择的短视频数组
    ids: [], //选择的短视频id数组
    min_num: 1, //最小数量，0为不限制
    max_num: 30, //最多选择30个
  });

  //获取所有标签
  const get_all_lable = async () => {
    try {
      let res = await getVideoLabelListApi({ pageSize: list_com_page_more });
      if (res.state == 200) {
        label_data.value = res.data.list;
      }
    } catch (error) {
      console.info(error, 'error');
    }
  };

  //短视频多选-回调事件
  const seleSvideo = (selectedRowsP, selectedRowKeysP) => {
    sele_more_svideo.value.ids = [...selectedRowKeysP];
    sele_more_svideo.value.info = JSON.parse(JSON.stringify(selectedRowsP));
    selectedRowKeys.value = selectedRowKeysP;
    selectedRows.value = selectedRowsP;
    handleModalCancle();
  };

  const resetSelVideo = () => {
    modalVisibleGoods.value = true;
    sle_more_title.value = '选择作品(最少选择1个)';
  };

  //获取推荐主题详情
  const get_detail = async (id) => {
    loading.value = true;
    let res = await getVideoThemeDetailApi({ themeId: id });
    if (res.state == 200) {
      img_data.value.map((item) => {
        let fileList = [];
        let tmp_data = {};
        if (item.name == 'carouselImage') {
          tmp_data.uid = res.data.carouselImage;
          tmp_data.name = res.data.carouselImageUrl;
          tmp_data.status = 'done';
          tmp_data.url = res.data.carouselImageUrl;
          fileList.push(tmp_data);
          item.fileList = fileList;
          item.img_succ_info.path = res.data.carouselImage;
        } else if (item.name == 'image') {
          tmp_data.uid = res.data.image;
          tmp_data.name = res.data.imageUrl;
          tmp_data.status = 'done';
          tmp_data.url = res.data.imageUrl;
          fileList.push(tmp_data);
          item.fileList = fileList;
          item.img_succ_info.path = res.data.image;
        }
      });
      res.data.videoList.map((item) => {
        selectedRows.value.push(item);
        selectedRowKeys.value.push(item.videoId);
        sele_more_svideo.value.info.push(item);
        sele_more_svideo.value.ids.push(item.videoId);
      });
      theme_detail.value = res.data;
      loading.value = false;
    }
  };

  //数据变化事件
  function handleFileChange(e, name) {
    if (e.file.status != undefined && e.file.status != 'error') {
      e.fileList.forEach((item) => {
        if (item.response && item.response.data) {
          item.url = item.response.data.url;
        }
      });
      for (let i in img_data.value) {
        if (img_data.value[i].name == name) {
          img_data.value[i].fileList = e.fileList;
          img_data.value[i].img_succ_info =
            e.file.response != undefined &&
            e.fileList.length > 0 &&
            e.file.response.data != undefined
              ? e.file.response.data
              : [];
        }
      }
    }
  }

  const beforeUploadClick = async()=> {
    imgToken.value = 'Bearer ' + userStore.getToken
    if(userStore.getToken){
      let res = await userStore.getSldCommonService()
      if(res.state == 200){
        imgToken.value = 'Bearer ' + res.token
      }
    }
  }

  // 返回
  const goBack = () => {
    if (window.history && window.history.length == 1) {
      pageClose();
    } else {
      const { fullPath } = route;
      tabStore.closeTabByKey(fullPath, router);
      router.back();
    }
  };

  // 保存
  const handleSaveAllData = async () => {
    formRef.value
      .validate()
      .then(async (values) => {
        let param = {};
        if (img_data.value[0].img_succ_info.path == undefined) {
          failTip(`请上传封面图片～`);
          return;
        } else {
          param.carouselImage = img_data.value[0].img_succ_info.path;
        }
        if (img_data.value[1].img_succ_info.path == undefined) {
          failTip(`请上传主题图片～`);
          return;
        } else {
          param.image = img_data.value[1].img_succ_info.path;
        }
        if (selectedRowKeys.value.length == 0) {
          failTip(`请选择作品～`);
          return;
        } else {
          param.videoIds = selectedRowKeys.value.join(',');
        }
        param.sort = values.sort;
        param.labelId = values.labelId;
        param.isShow = values.isShow;
        param.themeName = values.themeName;

        let res = '';
        if (route.query.id != undefined && route.query.id > 0) {
          //编辑推荐主题
          param.themeId = route.query.id;
          res = await getVideoThemeUpdateApi(param);
        } else {
          //新增推荐主题
          res = await getVideoThemeAddApi(param);
        }
        loading.value = true;
        if (res.state == 200) {
          sucTip(res.msg);
          userStore.setDelKeepAlive([route.name,'SvideoTheme'])
          setTimeout(() => {
            goBack();
          }, 500);
        } else {
          failTip(res.msg);
        }
      })
      .catch((err) => {
        console.info(err, '验证失败');
      });
  };

  // 预览
  const prevVideo = (record) => {
    operateData.value = [];
    if (record.videoType == 2) {
      //图文
      operateData.value.push({
        type: 'view_img_text',
        label: ``,
        name: 'videoContent',
        width: 300,
        height: 200,
        content: record.imageList, //图片
        text: record.videoContent, //文章
      });
    } else {
      //视频
      operateData.value.push({
        type: 'view_video',
        label: ``,
        name: 'videoPath',
        width: 600,
        height: 400,
        content: record.videoPath,
      });
    }
    modalVisible.value = true;
  };

  //关闭弹框
  const handleModalCancle = () => {
    modalVisible.value = false;
    operateData.value = [];
    modalVisibleGoods.value = false;
  };

  //短视频删除事件
  const delVideo = (videoId) => {
    selectedRows.value = selectedRows.value.filter((item) => item.videoId != videoId);
    selectedRowKeys.value = selectedRowKeys.value.filter((item) => item != videoId);
    sele_more_svideo.value.ids = [...selectedRowKeys.value];
    sele_more_svideo.value.info = JSON.parse(JSON.stringify(selectedRows.value));
  };

  onMounted(() => {
    if (route.query.id != undefined && route.query.id > 0) {
      get_detail(route.query.id);
    } else {
      isFirstLoading.value = false;
    }
    get_all_lable();
  });
</script>
<style lang="less">
  @import './style/add_theme.less';
</style>
