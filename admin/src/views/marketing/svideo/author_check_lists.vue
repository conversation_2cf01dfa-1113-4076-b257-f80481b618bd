<template>
  <div class="svideo_author_lists">
    <BasicTable @register="registerTable" rowKey="authorId">
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'memberAvatar'">
          <Popover placement="rightTop">
            <template #content>
              <div style="width: 100px">
                <img :src="text" alt="" style="max-width: 100%; max-height: 100%" />
              </div>
            </template>
            <div class="business_load_img">
              <img :src="text" alt="" />
            </div>
          </Popover>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            stopButtonPropagation
            v-if="record.permissionState == 1"
            :actions="[
              {
                label: '审核',
                ifShow: record.permissionState == 1,
                onclick: operateVideo.bind(null, record, 1),
              },
            ]"
          />
          <span v-else>--</span>
        </template>
      </template>
    </BasicTable>
    <SldModal
      :width="600"
      title="作者审核"
      :visible="modalVisible"
      :content="operateData"
      :showFoot="true"
      :height="600"
      :confirmBtnLoading="btnLoading"
      @cancle-event="handleModalCancle"
      @radio-change-event="radioChangeEvent"
      @confirm-event="handleModalConfirm"
      :showTopTip="showTopTip"
    />
  </div>
</template>
<script>
  export default {
    name: 'ViewThemeVideo',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import SldModal from '/@/components/SldModal/index.vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useRoute } from 'vue-router';
  import { getVideoAuthorListApi, getVideoAuthorAuditApi } from '/@/api/svideo/author_manage';
  import { Popover } from 'ant-design-vue';
  import { failTip, sucTip } from '/@/utils/utils';

  const route = useRoute();

  // 参数
  const id = ref(route.query?.id);

  const modalVisible = ref(false);
  const btnLoading = ref(false);

  const operateData = ref([]);

  const authorId = ref('');

  const expandData = ref([
    {
      key: `作品数：`,
      val: 'videoNum',
    },
    {
      key: `关注数：`,
      val: 'followNum',
    },
    {
      key: `粉丝数：`,
      val: 'fansNum',
    },
    {
      key: `获赞数：`,
      val: 'likeNum',
    },
  ]); //额外展开的信息

  const columns = reactive({
    data: [
      {
        title: `会员名称`,
        dataIndex: 'memberName',
        align: 'center',
        width: 180,
      },
      {
        title: `头像`,
        dataIndex: 'memberAvatar',
        align: 'center',
        width: 100,
      },
      {
        title: `简介`,
        dataIndex: 'introduction',
        align: 'center',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: `作者昵称`,
        dataIndex: 'memberNickname',
        align: 'center',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: `审核状态`,
        dataIndex: 'permissionStateValue',
        align: 'center',
        width: 100,
      },
      {
        title: `审核意见`,
        dataIndex: 'remark',
        align: 'center',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
    ],
  });

  const searchFormSchema = reactive({
    data: [
      {
        component: 'Input',
        label: `会员名称`,
        field: 'memberName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请输入会员名称`,
        },
      },
      {
        component: 'Input',
        label: `作者昵称`,
        field: 'memberNickname',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请输入作者昵称`,
        },
      },
      {
        component: 'Select',
        label: `审核状态`,
        field: 'permissionState',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请选择审核状态`,
          options: [
            { value: '', label: `全部` },
            { value: '1', label: `待审核` },
            { value: '3', label: `审核拒绝` },
          ],
        },
      },
    ],
  });

  // 表格数据
  const [registerTable, { reload }] = useTable({
    // 请求接口
    api: getVideoAuthorListApi,
    // 表头
    columns: columns.data,
    immediate: true,
    searchInfo: {
      type: 'audit',
    },
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: searchFormSchema.data,
    },
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    ellipsis: false,
    // 是否显示序号列
    showIndexColumn: true,
    // 表格右侧操作列配置
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
      // slots: { customRender: 'action' },
    },
  });

  // 操作  type==1 打开静止发布弹窗 2解除禁止 3禁止
  const operateVideo = async (record, type) => {
    try {
      let res;
      let param_data = {};
      if (type == 1) {
        operateData.value = [];
        let obj = [
          {
            type: 'radio_select',
            label: `审核`, //单选
            name: 'isPass',
            data: [
              //字段为key：选中的值  value：页面显示内容
              {
                key: true,
                value: `通过`,
              },
              {
                key: false,
                value: `拒绝`,
              },
            ],
            initialValue: true, //默认选中
            callback: true, //开启change事件:@radio-change-event   仅改变值的话不需要开启事件
          },
        ];
        operateData.value = obj;
        authorId.value = record.authorId;
        modalVisible.value = true;
        return;
      } else if (type == 3) {
        param_data = { ...record };
        param_data.authorId = authorId.value;
        res = await getVideoAuthorAuditApi(param_data);
      }
      btnLoading.value = true;
      if (res.state == 200) {
        modalVisible.value = false;
        btnLoading.value = false;
        sucTip(res.msg);
        operateData.value = [];
        reload();
      } else {
        btnLoading.value = false;
        failTip(res.msg);
      }
    } catch (error) {}
  };

  // radio_select状态的切换事件
  const radioChangeEvent = (value, index, from_info) => {
    operateData.value = [];
    let info = JSON.parse(JSON.stringify(from_info));
    if (!value) {
      let remark = {
        type: 'textarea',
        label: `审核意见`, //textarea输入框
        name: 'remark',
        extra: '最多100字',
        placeholder: `请输入审核意见`,
        maxLength: 100, //最多字数
        disable: false, //是否禁用
        rules: [
          {
            required: true,
            message: `请输入审核意见`,
          },
        ],
      };
      info.splice(index + 1, 0, remark);
    } else {
      info[index].initialValue = value;
      info.splice(index + 1, 1);
    }
    operateData.value = info;
  };

  //关闭弹框
  const handleModalCancle = () => {
    modalVisible.value = false;
  };

  const handleModalConfirm = (val) => {
    operateVideo(val, 3);
  };

  onMounted(() => {});
</script>
<style lang="less">
  .svideo_author_lists {
    .ant-table-row-expand-icon {
      float: initial;
    }

    .ant-table-cell {
      &:first-child {
        text-align: center;
      }
    }

    .business_load_img {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      margin: auto;

      img {
        max-width: 100%;
        max-height: 100%;
      }
    }

    .goods_info {
      position: relative;

      .virtual_goods_flag {
        position: absolute;
        top: 1px;
        left: 1px;
        padding: 0 3px;
        border-radius: 3px 0;
        background: linear-gradient(90deg, #f7d47e 0%, #e6b845 100%);
        color: #fff;
        font-size: 12px;
      }

      .goods_detail {
        flex: 1;
        width: 169px;
        height: 80px;
        margin-left: 10px;
      }

      .goods_img {
        display: inline-block;
        width: 80px;
        height: 80px;
        overflow: hidden;
        border: 1px solid rgb(226 229 246 / 100%);
        border-radius: 3px;
        background: rgb(248 248 248 / 100%);
      }

      .goods_name {
        display: -webkit-box;
        flex-shrink: 0;
        height: 34px;
        overflow: hidden;
        color: #333;
        font-size: 13px;
        line-height: 17px;
        text-align: left;
        text-overflow: ellipsis;
        word-break: break-word;
        -webkit-line-clamp: 2;

        /*! autoprefixer: off */
        -webkit-box-orient: vertical;
      }

      .goods_brief {
        display: inline-block;
        width: 100%;
        overflow: hidden;
        color: #666;
        font-size: 12px;
        text-align: left;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  p {
    margin-bottom: 0;
  }
</style>
