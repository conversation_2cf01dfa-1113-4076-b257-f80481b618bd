<template>
  <div class="section_padding view_theme_video">
    <div class="section_padding_back">
      <SldComHeader title="推荐主题作品列表" :back="true" />
      <div class="com_line" style="height: 1px"></div>
      <BasicTable @register="registerTable" rowKey="themeVideoId">
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'videoImage'">
            <Popover placement="rightTop">
              <template #content>
                <div style="width: 200px">
                  <img :src="text" alt="" style="max-width: 100%; max-height: 100%" />
                </div>
              </template>
              <div class="business_load_img">
                <img :src="text" alt="" />
              </div>
            </Popover>
          </template>
          <template v-if="column.dataIndex === 'action'">
            <TableAction
              :actions="[
                {
                  onClick: viewSpec.bind(null, record),
                  label: '预览',
                },
                {
                  label: '删除',
                  popConfirm: {
                    title: '删除后不可恢复，是否确定删除？',
                    placement: 'left',
                    confirm: operateVideo.bind(null, { themeVideoId: record.themeVideoId }),
                  },
                },
              ]"
            />
          </template>
        </template>
      </BasicTable>
    </div>
    <SldModal
      :width="600"
      title="预览"
      :visible="modalVisible"
      :height="600"
      :content="operateData"
      :showFoot="false"
      @cancle-event="handleModalCancle"
    />
  </div>
</template>
<script>
  export default {
    name: 'ViewThemeVideo',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { useRoute, useRouter } from 'vue-router';
  import { getVideoThemeVideoListApi, getVideoThemeDelVideoApi } from '/@/api/svideo/theme';
  import SldModal from '/@/components/SldModal/index.vue';
  import { Popover } from 'ant-design-vue';
  import { failTip, sucTip } from '/@/utils/utils';

  const route = useRoute();

  const router = useRouter();

  // 参数
  const id = ref(route.query?.id);

  const modalVisible = ref(false);

  const operateData = ref([]);

  const searchInfo = ref({
    themeId: id.value,
  });
  const columns = reactive({
    data: [
      {
        title: `封面`,
        dataIndex: 'videoImage',
        align: 'center',
        width: 180,
      },
      {
        title: `作品名称`,
        dataIndex: 'videoName',
        align: 'center',
        width: 100,
      },
      {
        title: `作品简介`,
        dataIndex: 'introduction',
        align: 'center',
        width: 100,
      },
      {
        title: `作品类型`,
        dataIndex: 'videoTypeValue',
        align: 'center',
        width: 100,
      },
    ],
  });

  const searchFormSchema = reactive({
    data: [
      {
        component: 'Input',
        label: `作品名称`,
        field: 'videoName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请输入作品名称`,
        },
      },
    ],
  });

  // 表格数据
  const [registerTable, { reload }] = useTable({
    // 请求接口
    api: getVideoThemeVideoListApi,
    // 表头
    columns: columns.data,
    immediate: true,
    // 参数
    searchInfo: searchInfo.value,
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: searchFormSchema.data,
    },
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    ellipsis: false,
    // 是否显示序号列
    showIndexColumn: true,
    // 表格右侧操作列配置
    actionColumn: {
      width: 160,
      title: '操作',
      dataIndex: 'action',
      // slots: { customRender: 'action' },
    },
  });

  const viewSpec = (record) => {
    operateData.value = [];
    if (record.videoType == 2) {
      //图文
      operateData.value.push({
        type: 'view_img_text',
        label: ``,
        name: 'videoContent',
        width: 300,
        height: 200,
        content: record.imageList, //图片
        text: record.videoContent, //文章
      });
    } else {
      //视频
      operateData.value.push({
        type: 'view_video',
        label: ``,
        name: 'videoPath',
        width: 600,
        height: 400,
        content: record.videoPath,
      });
    }
    modalVisible.value = true;
  };

  //关闭弹框
  const handleModalCancle = () => {
    modalVisible.value = false;
    operateData.value = [];
  };

  // 删除
  const operateVideo = async (id) => {
    try {
      let res = await getVideoThemeDelVideoApi(id);
      if (res.state == 200) {
        sucTip(res.msg);
        reload();
      } else {
        failTip(res.msg);
      }
    } catch (error) {}
  };

  onMounted(() => {});
</script>
<style lang="less" scoped>
  .view_theme_video {
    .business_load_img {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60px;
      height: 60px;
      margin: auto;

      img {
        max-width: 100%;
        max-height: 100%;
      }
    }

    .goods_info {
      position: relative;

      .virtual_goods_flag {
        position: absolute;
        top: 1px;
        left: 1px;
        padding: 0 3px;
        border-radius: 3px 0;
        background: linear-gradient(90deg, #f7d47e 0%, #e6b845 100%);
        color: #fff;
        font-size: 12px;
      }

      .goods_detail {
        flex: 1;
        width: 169px;
        height: 80px;
        margin-left: 10px;
      }

      .goods_img {
        display: inline-block;
        width: 80px;
        height: 80px;
        overflow: hidden;
        border: 1px solid rgb(226 229 246 / 100%);
        border-radius: 3px;
        background: rgb(248 248 248 / 100%);
      }

      .goods_name {
        display: -webkit-box;
        flex-shrink: 0;
        height: 34px;
        overflow: hidden;
        color: #333;
        font-size: 13px;
        line-height: 17px;
        text-align: left;
        text-overflow: ellipsis;
        word-break: break-word;
        -webkit-line-clamp: 2;

        /*! autoprefixer: off */
        -webkit-box-orient: vertical;
      }

      .goods_brief {
        display: inline-block;
        width: 100%;
        overflow: hidden;
        color: #666;
        font-size: 12px;
        text-align: left;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  p {
    margin-bottom: 0;
  }
</style>
