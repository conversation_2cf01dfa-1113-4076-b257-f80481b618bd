<template>
  <div style="padding: 20px">
    <BasicTable @register="registerTable" rowKey="replyId">
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                label: '删除',
                popConfirm: {
                  title: '删除后不可恢复，是否确定删除？',
                  placement: 'left',
                  confirm: operateComment.bind(null, record.replyId),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
  </div>
</template>
<script>
  export default {
    name: 'DrawRecordList',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { validatorEmoji } from '/@/utils/validate';
  import { getVideoCommentReplyApi, getVideoCommentDelReplyApi } from '/@/api/svideo/comment_lists';
  import { failTip, sucTip } from '/@/utils/utils';

  const props = defineProps({
    commentId: {
      type: Number,
    },
  });

  const columns = reactive({
    data: [
      {
        title: `回复内容`,
        dataIndex: 'content',
        align: 'center',
        width: 100,
      },
      {
        title: `回复时间`,
        dataIndex: 'createTime',
        align: 'center',
        width: 100,
      },
      {
        title: `回复人名称`,
        dataIndex: 'fromAuthorName',
        align: 'center',
        width: 100,
      },
      {
        title: `被回复人名称`,
        dataIndex: 'toAuthorName',
        align: 'center',
        width: 100,
      },
      {
        title: `获赞数`,
        dataIndex: 'likeNum',
        align: 'center',
        width: 100,
      },
    ],
  });

  const rules = ref([
    {
      // @ts-ignore
      validator: async (rule, value) => {
        await validatorEmoji(rule, value);
      },
      trigger: 'change',
    },
  ]);

  const searchFormSchema = reactive({
    data: [
      {
        component: 'Input',
        label: `回复人`,
        field: 'fromAuthorName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请输入回复人名称`,
        },
        rules: rules.value,
      },
      {
        component: 'Input',
        label: `被回复人`,
        field: 'toAuthorName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请输入被回复人名称`,
        },
        rules: rules.value,
      },
      {
        component: 'Input',
        label: `回复内容`,
        field: 'content',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请输入回复内容`,
        },
        rules: rules.value,
      },
      {
        colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
        component: 'RangePicker',
        label: `回复时间`,
        field: '[startTime,endTime]',
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: ['开始时间', '结束时间'],
        },
      },
    ],
  });

  // 表格数据
  const [registerTable, { reload }] = useTable({
    // 请求接口
    api: getVideoCommentReplyApi,
    // 表头
    columns: columns.data,
    immediate: true,
    // 参数
    searchInfo: {
      commentId: props.commentId,
    },
    // 点击搜索前处理的参数
    beforeFetch(values) {
      values.startTime = values.startTime
        ? values.startTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
      return values;
    },
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: searchFormSchema.data,
    },
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    maxHeight: 300,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    ellipsis: false,
    // 是否显示序号列
    showIndexColumn: true,
    // 表格右侧操作列配置
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
      // slots: { customRender: 'action' },
    },
  });

  const operateComment = async (id) => {
    try {
      let res = await getVideoCommentDelReplyApi({ replyId: id });
      if (res.state == 200) {
        sucTip(res.msg);
        reload();
      } else {
        failTip(res.msg);
      }
    } catch (error) {
      console.info(error, 'error');
    }
  };
  onMounted(() => {});
</script>
<style lang="less" scoped>
  p {
    margin-bottom: 0;
  }
</style>
<style lang="less"></style>
