<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader title="推荐主题" />
      <div class="seckill_label_lists">
        <BasicTable @register="standardTable" style="padding: 0">
          <template #tableTitle>
            <div class="toolbar">
              <div class="toolbar_btn" @click="add_theme(null, 'add','AddTheme')">
                <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#fff" />
                <span>新增推荐主题</span>
              </div>
            </div>
          </template>
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.dataIndex == 'isShow'">
              <div>
                <Switch
                  @change="(checked) => handleClick(record, 'switch', checked ? 1 : 0)"
                  :checked="text == 1 ? true : false"
                />
              </div>
            </template>
            <template v-if="column.key == 'action'">
              <template v-if="record.isInner == 1"> -- </template>
              <template v-else>
                <TableAction
                  :actions="[
                    {
                      label: '查看作品',
                      onClick: handleClick.bind(null, record, 'view'),
                    },
                    {
                      label: '编辑',
                      onClick: add_theme.bind(null, record, 'edit','EditTheme'),
                    },
                    {
                      label: '删除',
                      popConfirm: {
                        title: '删除后不可恢复，是否确定删除？',
                        placement: 'left',
                        confirm: handleClick.bind(null, record, 'del'),
                      },
                    },
                  ]"
                />
              </template>
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'SvideoTheme',
  };
</script>
<script setup>
  import { ref, onMounted, unref } from 'vue';
  import { useRouter } from 'vue-router';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { Switch } from 'ant-design-vue';
  import { getVideoLabelListApi } from '/@/api/svideo/label';
  import { useUserStore } from '/@/store/modules/user';
  import {
    getVideoThemeListApi,
    getVideoThemeAddApi,
    getVideoThemeUpdateApi,
    getVideoThemeDetailApi,
    getVideoThemeDelApi,
    getVideoThemeIsShowApi,
  } from '/@/api/svideo/theme';
  import { validatorEmoji } from '/@/utils/validate';
  import { failTip, list_com_page_more, sucTip } from '/@/utils/utils';
  const userStore = useUserStore();

  const router = useRouter();

  const [standardTable, { reload, getForm }] = useTable({
    api: (arg) => getVideoThemeListApi({ ...arg }),
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    columns: [
      {
        title: '主题名称',
        dataIndex: 'themeName',
        width: 120,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: '排序',
        dataIndex: 'sort',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: '所属标签',
        dataIndex: 'labelName',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: '绑定作品数',
        dataIndex: 'videoNum',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: '是否显示',
        dataIndex: 'isShow',
        width: 60,
      },
    ],
    actionColumn: {
      width: 200,
      title: '操作',
      dataIndex: 'action',
    },
    useSearchForm: true,
    formConfig: {
      schemas: [
        {
          field: 'themeName',
          component: 'Input',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            placeholder: '请输入主题名称',
            size: 'default',
          },
          label: '主题名称',
          labelWidth: 70,
          rules: [
            {
              // @ts-ignore
              validator: async (rule, value) => {
                await validatorEmoji(rule, value);
              },
              trigger: 'change',
            },
          ],
        },
        {
          component: 'Select',
          label: `所属标签`,
          field: 'labelId',
          labelWidth: 90,
          colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
          componentProps: {
            placeholder: `请选择所属标签`,
            options: [],
          },
        },
      ],
    },
    bordered: true,
    striped: false,
    showIndexColumn: true,
  });
  const operate_id = ref('');
  const operate_type = ref('');

  //表格点击回调事件
  function handleClick(item, type, check) {
    operate_type.value = type;
    if (item) {
      operate_id.value = item.themeId;
    }
    if (type == 'add' || type == 'edit') {
    }
    if (type == 'add') {
    } else if (type == 'edit') {
    } else if (type == 'del') {
      operate_role({ themeId: item.themeId });
    } else if (type == 'switch') {
      operate_role({ themeId: item.themeId, isShow: check });
    } else if (type == 'view') {
      router.push({
        path: `/marketing_svideo/video_theme_bind_video`,
        query: { id: item.themeId },
      });
    }
  }

  //商品标签操作
  const operate_role = async (params) => {
    let res = {};
    if (operate_type.value == 'del') {
      res = await getVideoThemeDelApi(params);
    } else if (operate_type.value == 'switch') {
      res = await getVideoThemeIsShowApi(params);
    }
    if (res.state == 200) {
      operate_type.value = '';
      sucTip(res.msg);
      reload();
    } else {
      failTip(res.msg);
    }
  };

  //获取所有标签
  const get_all_lable = async () => {
    try {
      const { updateSchema } = getForm();
      let res = await getVideoLabelListApi({ pageSize: list_com_page_more });
      if (res.state == 200) {
        await updateSchema({
          field: 'labelId',
          componentProps: {
            options: unref(res.data.list).map((item) => ({
              value: item.labelId,
              label: item.labelName,
            })),
          },
        });
      }
    } catch (error) {}
  };

  // 新增推荐主题
  const add_theme = (record, type,pathName) => {
    userStore.setDelKeepAlive([pathName])
    if (type == 'add') {
      router.push({
        path: `/marketing_svideo/video_theme_to_add`,
      });
    } else {
      router.push({
        path: `/marketing_svideo/video_theme_to_${type}`,
        query: {
          id: record.themeId,
        },
      });
    }
  };

  onMounted(() => {
    get_all_lable();
  });
</script>
<style lang="less" scoped></style>
