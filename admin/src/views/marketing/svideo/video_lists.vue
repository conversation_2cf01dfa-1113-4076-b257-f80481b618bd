<template>
  <div class="svideo_author_lists">
    <BasicTable @register="registerTable" rowKey="videoId">
      <template #expandedRowRender="{ record }">
        <div style="text-align: left">
          <span
            style="display: inline-block; width: 400px"
            v-for="(item, index) in expandData"
            :key="index"
            >{{ item.key }}{{ record[item.val] }}
          </span>
        </div>
      </template>
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'videoImage'">
          <Popover placement="rightTop">
            <template #content>
              <div style="width: 200px">
                <img :src="text" alt="" style="max-width: 100%; max-height: 100%" />
              </div>
            </template>
            <div class="business_load_img">
              <img :src="text" alt="" />
            </div>
          </Popover>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            stopButtonPropagation
            :actions="[
              {
                label: '预览',
                onClick: viewSpec.bind(null, record),
              },
              {
                label: '查看商品',
                onClick: operateVideo.bind(null, record, 'look'),
              },
              {
                label: '禁止显示',
                ifShow: record.state == 2,
                onClick: operateVideo.bind(null, record, 'hide'),
              },
              {
                label: '解除禁止',
                ifShow: record.state == 4,
                popConfirm: {
                  title: '确定解除禁止？',
                  placement: 'left',
                  confirm: operateVideo.bind(null, record, 'show'),
                },
              },
              {
                label: '删除',
                ifShow: record.state == 4,
                popConfirm: {
                  title: '删除后不可恢复，是否确定删除？',
                  placement: 'left',
                  confirm: operateVideo.bind(null, record, 'del'),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <SldModal
      :width="600"
      :title="title"
      :visible="modalVisible"
      :height="600"
      :content="operateData"
      :showFoot="showFoot"
      @cancle-event="handleModalCancle"
      @confirm-event="handleModalConfirm"
    />
  </div>
</template>
<script>
  export default {
    name: 'VideoList',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import SldModal from '/@/components/SldModal/index.vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useRoute, useRouter } from 'vue-router';
  import { getVideoListApi, getVideoApi, getVideoIsShowApi } from '/@/api/svideo/video_manage';
  import { Popover } from 'ant-design-vue';
  import { sucTip, failTip } from '/@/utils/utils';

  const route = useRoute();

  const router = useRouter();

  // 参数
  const id = ref(route.query?.id);

  const modalVisible = ref(false);

  const btnLoading = ref(false);

  const showFoot = ref(false);

  const operateData = ref([
    {
      type: 'textarea',
      label: `禁止理由`, //textarea输入框
      name: 'remark',
      placeholder: `请输入禁止理由，最多输入150字`,
      maxLength: 150, //最多字数
      disable: false, //是否禁用
      rules: [
        {
          required: true,
          message: `请输入禁止理由`,
        },
      ],
    },
  ]);

  const videoId = ref('');

  const title = ref('');

  const expandData = ref([
    {
      key: `评论数：`,
      val: 'commentNum',
    },
    {
      key: `商品数：`,
      val: 'goodsNum',
    },
    {
      key: `播放数：`,
      val: 'clickNum',
    },
    {
      key: `点赞数：`,
      val: 'likeNum',
    },
    {
      key: `作品简介：`,
      val: 'introduction',
    },
  ]); //额外展开的信息

  const columns = reactive({
    data: [
      {
        title: `会员名称`,
        dataIndex: 'memberName',
        align: 'center',
        width: 150,
      },
      {
        title: `作者昵称`,
        dataIndex: 'memberNickname',
        align: 'center',
        width: 150,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: `封面`,
        dataIndex: 'videoImage',
        align: 'center',
        width: 100,
      },
      {
        title: `作品名称`,
        dataIndex: 'videoName',
        align: 'center',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: `发布时间`,
        dataIndex: 'createTime',
        align: 'center',
        width: 150,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: `作品类型`,
        dataIndex: 'videoTypeValue',
        align: 'center',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: `作品状态`,
        dataIndex: 'stateValue',
        align: 'center',
        width: 100,
      },
      {
        title: `禁止理由`,
        dataIndex: 'remark',
        align: 'center',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
    ],
  });

  const searchFormSchema = reactive({
    data: [
      {
        component: 'Input',
        label: `作品名称`,
        field: 'videoName',
        componentProps: {
          placeholder: `请输入作品名称`,
        },
      },
      {
        component: 'Input',
        label: `会员名称`,
        field: 'memberName',
        componentProps: {
          placeholder: `请输入会员名称`,
        },
      },
      {
        component: 'Input',
        label: `作者昵称`,
        field: 'memberNickname',
        componentProps: {
          placeholder: `请输入作者昵称`,
        },
      },
      {
        colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
        component: 'RangePicker',
        label: `发布时间`,
        field: '[startTime,endTime]',
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: ['开始时间', '结束时间'],
        },
      },
      {
        component: 'Select',
        label: `作品状态`,
        field: 'state',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请选择作品状态`,
          options: [
            { value: '', label: `全部` },
            { value: '2', label: `正常` },
            { value: '4', label: `禁止` },
          ],
        },
      },
      {
        component: 'Select',
        label: `作品类型`,
        field: 'videoType',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请选择作品类型`,
          options: [
            { value: '', label: `全部` },
            { value: '1', label: `视频` },
            { value: '2', label: `图文` },
          ],
        },
      },
    ],
  });

  // 表格数据
  const [registerTable, { reload }] = useTable({
    // 请求接口
    api: getVideoListApi,
    // 表头
    columns: columns.data,
    immediate: true,
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: searchFormSchema.data,
    },
    inset: false,
    beforeFetch(values) {
      values.startTime = values.startTime
        ? values.startTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
      return values;
    },
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    ellipsis: false,
    // 是否显示序号列
    showIndexColumn: true,
    // 表格右侧操作列配置
    actionColumn: {
      width: 240,
      title: '操作',
      dataIndex: 'action',
      // slots: { customRender: 'action' },
    },
  });

  // 操作  type==show 解除禁止 del 删除  ''禁止
  const operateVideo = async (record, type) => {
    try {
      let res;
      let param_data = {};
      if (type == 'show') {
        param_data.isShow = true;
        param_data.videoId = record.videoId;
        res = await getVideoIsShowApi(param_data);
      } else if (type == 'hide') {
        operateData.value = [];
        let obj = [
          {
            type: 'textarea',
            label: `禁止理由`, //textarea输入框
            name: 'remark',
            placeholder: `请输入禁止理由，最多输入50字`,
            maxLength: 50, //最多字数
            disable: false, //是否禁用
            rules: [
              {
                required: true,
                message: `请输入禁止理由`,
              },
            ],
          },
        ];
        videoId.value = record.videoId;
        operateData.value = obj;
        showFoot.value = true;
        title.value = '禁止显示';
        modalVisible.value = true;
        return;
      } else if (type == 'del') {
        param_data.videoId = record.videoId;
        res = await getVideoApi(param_data);
      } else if (type == 'look') {
        router.push({
          path: `/marketing_svideo/video_manage_bind_goods`,
          query: { id: record.videoId, videoName: record.videoName },
        });
        return;
      } else {
        param_data = { ...record };
        param_data.isShow = false;
        param_data.videoId = videoId.value;
        res = await getVideoIsShowApi(param_data);
      }
      if (res.state == 200) {
        operateData.value = [];
        modalVisible.value = false;
        sucTip(res.msg);
        reload();
      } else {
        failTip(res.msg);
      }
    } catch (error) {}
  };

  // 预览
  const viewSpec = (record) => {
    operateData.value = [];
    if (record.videoType == 2) {
      //图文
      operateData.value.push({
        type: 'view_img_text',
        label: ``,
        name: 'videoContent',
        width: 300,
        height: 200,
        content: record.imageList, //图片
        text: record.videoContent, //文章
      });
    } else {
      //视频
      operateData.value.push({
        type: 'view_video',
        label: ``,
        name: 'videoPath',
        width: 600,
        height: 400,
        content: record.videoPath,
      });
    }
    showFoot.value = false;
    title.value = '预览';
    modalVisible.value = true;
  };

  //关闭弹框
  const handleModalCancle = () => {
    operateData.value = [];
    modalVisible.value = false;
  };

  const handleModalConfirm = (val) => {
    operateVideo(val, '');
  };

  onMounted(() => {});
</script>
<style lang="less">
  .svideo_author_lists {
    .ant-table-row-expand-icon {
      float: initial;
    }

    .ant-table-cell {
      &:first-child {
        text-align: center;
      }
    }

    .business_load_img {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60px;
      height: 60px;
      margin: auto;

      img {
        max-width: 100%;
        max-height: 100%;
      }
    }

    .goods_info {
      position: relative;

      .virtual_goods_flag {
        position: absolute;
        top: 1px;
        left: 1px;
        padding: 0 3px;
        border-radius: 3px 0;
        background: linear-gradient(90deg, #f7d47e 0%, #e6b845 100%);
        color: #fff;
        font-size: 12px;
      }

      .goods_detail {
        flex: 1;
        width: 169px;
        height: 80px;
        margin-left: 10px;
      }

      .goods_img {
        display: inline-block;
        width: 80px;
        height: 80px;
        overflow: hidden;
        border: 1px solid rgb(226 229 246 / 100%);
        border-radius: 3px;
        background: rgb(248 248 248 / 100%);
      }

      .goods_name {
        display: -webkit-box;
        flex-shrink: 0;
        height: 34px;
        overflow: hidden;
        color: #333;
        font-size: 13px;
        line-height: 17px;
        text-align: left;
        text-overflow: ellipsis;
        word-break: break-word;
        -webkit-line-clamp: 2;

        /*! autoprefixer: off */
        -webkit-box-orient: vertical;
      }

      .goods_brief {
        display: inline-block;
        width: 100%;
        overflow: hidden;
        color: #666;
        font-size: 12px;
        text-align: left;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  p {
    margin-bottom: 0;
  }
</style>
