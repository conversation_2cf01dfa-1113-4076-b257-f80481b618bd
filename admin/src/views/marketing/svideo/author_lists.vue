<template>
  <div class="svideo_author_lists">
    <BasicTable @register="registerTable" rowKey="authorId">
      <template #expandedRowRender="{ record }">
        <div style="text-align: left">
          <span
            style="display: inline-block; width: 400px"
            v-for="(item, index) in expandData"
            :key="index"
            >{{ item.key }}{{ record[item.val] }}
          </span>
        </div>
      </template>
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'memberAvatar'">
          <Popover placement="rightTop">
            <template #content>
              <div style="width: 100px">
                <img :src="text" alt="" style="max-width: 100%; max-height: 100%" />
              </div>
            </template>
            <div class="business_load_img">
              <img :src="text" alt="" />
            </div>
          </Popover>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            stopButtonPropagation
            :actions="[
              {
                label: '禁止发布',
                ifShow: record.permissionState == 2,
                onclick: operateVideo.bind(null, record, 1),
              },
              {
                label: '解除禁止',
                ifShow: record.permissionState == 4,
                popConfirm: {
                  title: '确定解除禁止？',
                  placement: 'left',
                  confirm: operateVideo.bind(null, record, 2),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <SldModal
      :width="600"
      title="禁止发布"
      :visible="modalVisible"
      :content="operateData"
      :showFoot="true"
      :height="600"
      :confirmBtnLoading="btnLoading"
      @cancle-event="handleModalCancle"
      @confirm-event="handleModalConfirm"
    />
  </div>
</template>
<script>
  export default {
    name: 'ViewThemeVideo',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import SldModal from '/@/components/SldModal/index.vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useRoute } from 'vue-router';
  import { getVideoAuthorListApi, getVideoAuthorIsPubLishApi } from '/@/api/svideo/author_manage';
  import { Popover } from 'ant-design-vue';
  import { failTip, sucTip } from '/@/utils/utils';

  const route = useRoute();

  // 参数
  const id = ref(route.query?.id);

  const modalVisible = ref(false);
  const btnLoading = ref(false);

  const operateData = ref([
    {
      type: 'textarea',
      label: `禁止理由`, //textarea输入框
      name: 'remark',
      placeholder: `请输入禁止理由，最多输入150字`,
      maxLength: 150, //最多字数
      disable: false, //是否禁用
      rules: [
        {
          required: true,
          message: `请输入禁止理由`,
        },
      ],
    },
  ]);

  const authorId = ref('');

  const expandData = ref([
    {
      key: `作品数：`,
      val: 'videoNum',
    },
    {
      key: `关注数：`,
      val: 'followNum',
    },
    {
      key: `粉丝数：`,
      val: 'fansNum',
    },
    {
      key: `获赞数：`,
      val: 'likeNum',
    },
  ]); //额外展开的信息

  const columns = reactive({
    data: [
      {
        title: `会员名称`,
        dataIndex: 'memberName',
        align: 'center',
        width: 180,
      },
      {
        title: `头像`,
        dataIndex: 'memberAvatar',
        align: 'center',
        width: 100,
      },
      {
        title: `简介`,
        dataIndex: 'introduction',
        align: 'center',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: `作者昵称`,
        dataIndex: 'memberNickname',
        align: 'center',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: `发布状态`,
        dataIndex: 'permissionStateValue',
        align: 'center',
        width: 100,
      },
      {
        title: `禁止理由`,
        dataIndex: 'remark',
        align: 'center',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
    ],
  });

  const searchFormSchema = reactive({
    data: [
      {
        component: 'Input',
        label: `会员名称`,
        field: 'memberName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请输入会员名称`,
        },
      },
      {
        component: 'Input',
        label: `作者昵称`,
        field: 'memberNickname',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请输入作者昵称`,
        },
      },
      {
        component: 'Select',
        label: `发布状态`,
        field: 'permissionState',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请选择发布状态`,
          options: [
            { value: '', label: `全部` },
            { value: '2', label: `正常` },
            { value: '4', label: `禁止` },
          ],
        },
      },
    ],
  });

  // 表格数据
  const [registerTable, { reload }] = useTable({
    // 请求接口
    api: getVideoAuthorListApi,
    // 表头
    columns: columns.data,
    immediate: true,
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: searchFormSchema.data,
    },
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    ellipsis: false,
    // 是否显示序号列
    showIndexColumn: true,
    // 表格右侧操作列配置
    actionColumn: {
      width: 160,
      title: '操作',
      dataIndex: 'action',
      // slots: { customRender: 'action' },
    },
  });

  // 操作  type==1 打开静止发布弹窗 2解除禁止 3禁止
  const operateVideo = async (record, type) => {
    try {
      let res;
      let param_data = {};
      if (type == 1) {
        authorId.value = record.authorId;
        modalVisible.value = true;
        return;
      } else if (type == 2) {
        param_data.isPass = true;
        param_data.authorId = record.authorId;
        res = await getVideoAuthorIsPubLishApi(param_data);
      } else if (type == 3) {
        param_data = { ...record };
        param_data.isPass = false;
        param_data.authorId = authorId.value;
        res = await getVideoAuthorIsPubLishApi(param_data);
      }
      if (res.state == 200) {
        operateData.value[0].initialValue = '';
        modalVisible.value = false;
        sucTip(res.msg);
        reload();
      } else {
        failTip(res.msg);
      }
    } catch (error) {}
  };

  //关闭弹框
  const handleModalCancle = () => {
    operateData.value[0].initialValue = '';
    modalVisible.value = false;
  };

  const handleModalConfirm = (val) => {
    operateVideo(val, 3);
  };

  onMounted(() => {});
</script>
<style lang="less">
  .svideo_author_lists {
    .ant-table-row-expand-icon {
      float: initial;
    }

    .ant-table-cell {
      &:first-child {
        text-align: center;
      }
    }

    .business_load_img {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      margin: auto;

      img {
        max-width: 100%;
        max-height: 100%;
      }
    }
  }

  p {
    margin-bottom: 0;
  }
</style>
