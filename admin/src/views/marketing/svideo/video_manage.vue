<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader title="作品管理" />
      <Tabs type="card" v-model:activeKey="activeKey">
        <TabPane key="1" tab="作品列表">
          <VideoLists class="section_padding_tab_top"></VideoLists>
        </TabPane>
        <TabPane key="2" tab="作品审核">
          <VideoCheckLists class="section_padding_tab_top"></VideoCheckLists>
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'AuthorManage',
  };
</script>
<script setup>
  import { ref, onMounted } from 'vue';
  import { Tabs, TabPane } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import VideoLists from './video_lists.vue';
  import VideoCheckLists from './video_check_lists.vue';

  const activeKey = ref('1');

  onMounted(() => {});
</script>
<style lang="less">
  .RadioGroup_back {
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within {
      box-shadow: none;
    }
  }
</style>
