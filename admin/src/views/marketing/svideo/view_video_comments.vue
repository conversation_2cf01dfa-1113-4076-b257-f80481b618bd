<template>
  <div class="section_padding view_video_comments">
    <div class="section_padding_back">
      <SldComHeader :title="title" :back="true" />
      <div class="com_line" style="height: 1px"></div>
      <BasicTable @register="registerTable" rowKey="commentId">
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction
              :actions="[
                {
                  label: '查看回复',
                  onClick: view.bind(null, record.commentId),
                },
                {
                  label: '删除评论',
                  popConfirm: {
                    title: '删除后不可恢复，是否确定删除？',
                    placement: 'left',
                    confirm: operateComment.bind(null, record.commentId),
                  },
                },
              ]"
            />
          </template>
        </template>
      </BasicTable>
    </div>
    <Modal
      destroyOnClose
      :maskClosable="false"
      title="查看回复"
      :visible="modalVisible"
      :width="1000"
      :footer="null"
      @cancel="sldCancle"
    >
      <CommentReplyLists :commentId="commentId"></CommentReplyLists>
    </Modal>
  </div>
</template>
<script>
  export default {
    name: 'CommentListsToView',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { useRoute } from 'vue-router';
  import {
    getVideoCommentCommentListApi,
    getVideoCommentDelCommentApi,
  } from '/@/api/svideo/comment_lists';
  import { Modal } from 'ant-design-vue';
  import CommentReplyLists from './comment_reply_lists.vue';
  import { sucTip, failTip } from '/@/utils/utils';

  const route = useRoute();

  // 参数
  const id = ref(route.query?.id);

  const modalVisible = ref(false);

  const title = ref('查看' + route.query?.videoName + '的评论');

  const searchInfo = ref({
    videoId: id.value,
  });

  const commentId = ref('');

  const columns = reactive({
    data: [
      {
        title: `评论内容`,
        dataIndex: 'content',
        align: 'center',
        width: 180,
      },
      {
        title: `评论人名称`,
        dataIndex: 'authorName',
        align: 'center',
        width: 100,
      },
      {
        title: `获赞数`,
        dataIndex: 'likeNum',
        align: 'center',
        width: 100,
      },
      {
        title: `评论时间`,
        dataIndex: 'createTime',
        align: 'center',
        width: 100,
      },
    ],
  });

  const searchFormSchema = reactive({
    data: [
      {
        component: 'Input',
        label: `评论人`,
        field: 'authorName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `评论人名称`,
        },
      },
      {
        component: 'Input',
        label: `评论内容`,
        field: 'content',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `评论内容`,
        },
      },
      {
        colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
        component: 'RangePicker',
        label: `评论时间`,
        field: '[startTime,endTime]',
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: ['开始时间', '结束时间'],
        },
      },
    ],
  });

  // 表格数据
  const [registerTable, { reload }] = useTable({
    // 请求接口
    api: getVideoCommentCommentListApi,
    // 表头
    columns: columns.data,
    immediate: true,
    // 参数
    searchInfo: searchInfo.value,
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: searchFormSchema.data,
    },
    inset: false,
    // 点击搜索前处理的参数
    beforeFetch(values) {
      values.startTime = values.startTime
        ? values.startTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
      return values;
    },
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    ellipsis: false,
    // 是否显示序号列
    showIndexColumn: true,
    // 表格右侧操作列配置
    actionColumn: {
      width: 150,
      title: '操作',
      dataIndex: 'action',
      // slots: { customRender: 'action' },
    },
  });

  const operateComment = async (id) => {
    try {
      let res = await getVideoCommentDelCommentApi({ commentId: id });
      if (res.state == 200) {
        sucTip(res.msg);
        reload();
      } else {
        failTip(res.msg);
      }
    } catch (error) {}
  };

  const view = (id) => {
    commentId.value = id;
    modalVisible.value = true;
  };

  const sldCancle = () => {
    modalVisible.value = false;
    commentId.value = 0;
  };

  onMounted(() => {});
</script>
<style lang="less" scoped>
  .view_video_comments {
    .business_load_img {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60px;
      height: 60px;
      margin: auto;

      img {
        max-width: 100%;
        max-height: 100%;
      }
    }

    .goods_info {
      position: relative;

      .virtual_goods_flag {
        position: absolute;
        top: 1px;
        left: 1px;
        padding: 0 3px;
        border-radius: 3px 0;
        background: linear-gradient(90deg, #f7d47e 0%, #e6b845 100%);
        color: #fff;
        font-size: 12px;
      }

      .goods_detail {
        flex: 1;
        width: 169px;
        height: 80px;
        margin-left: 10px;
      }

      .goods_img {
        display: inline-block;
        width: 80px;
        height: 80px;
        overflow: hidden;
        border: 1px solid rgb(226 229 246 / 100%);
        border-radius: 3px;
        background: rgb(248 248 248 / 100%);
      }

      .goods_name {
        display: -webkit-box;
        flex-shrink: 0;
        height: 34px;
        overflow: hidden;
        color: #333;
        font-size: 13px;
        line-height: 17px;
        text-align: left;
        text-overflow: ellipsis;
        word-break: break-word;
        -webkit-line-clamp: 2;

        /*! autoprefixer: off */
        -webkit-box-orient: vertical;
      }

      .goods_brief {
        display: inline-block;
        width: 100%;
        overflow: hidden;
        color: #666;
        font-size: 12px;
        text-align: left;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  p {
    margin-bottom: 0;
  }
</style>
