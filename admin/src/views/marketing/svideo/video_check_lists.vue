<template>
  <div class="svideo_author_lists">
    <BasicTable @register="registerTable" rowKey="videoId">
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'videoImage'">
          <Popover placement="rightTop">
            <template #content>
              <div style="width: 200px">
                <img :src="text" alt="" style="max-width: 100%; max-height: 100%" />
              </div>
            </template>
            <div class="business_load_img">
              <img :src="text" alt="" />
            </div>
          </Popover>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            stopButtonPropagation
            :actions="[
              {
                label: '审核',
                ifShow: record.state == 1,
                onclick: operateVideo.bind(null, record, 1),
              },
              {
                label: '查看',
                ifShow: record.state != 1,
                onclick: operateVideo.bind(null, record, 1),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <SldModal
      :width="1000"
      title="作品审核"
      :visible="modalVisible"
      :content="operateData"
      :showFoot="showFoot"
      :height="600"
      :confirmBtnLoading="btnLoading"
      @cancle-event="handleModalCancle"
      @radio-change-event="radioChangeEvent"
      @confirm-event="handleModalConfirm"
      :showTopTip="showTopTip"
    />
  </div>
</template>
<script>
  export default {
    name: 'VideoCheckLists',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import SldModal from '/@/components/SldModal/index.vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useRoute, useRouter } from 'vue-router';
  import { getVideoListApi, getVideoAuditApi, getVideoGoodsApi } from '/@/api/svideo/video_manage';
  import { Popover } from 'ant-design-vue';
  import { sucTip, failTip } from '/@/utils/utils';

  const route = useRoute();

  const router = useRouter();

  // 参数
  const id = ref(route.query?.id);

  const modalVisible = ref(false);
  const btnLoading = ref(false);

  const operateData = ref([]);

  const showFoot = ref(true);

  const videoId = ref('');

  const columns = reactive({
    data: [
      {
        title: `会员名称`,
        dataIndex: 'memberName',
        align: 'center',
        width: 180,
      },
      {
        title: `作者昵称`,
        dataIndex: 'memberNickname',
        align: 'center',
        width: 180,
      },
      {
        title: `封面`,
        dataIndex: 'videoImage',
        align: 'center',
        width: 100,
      },
      {
        title: `作品名称`,
        dataIndex: 'videoName',
        align: 'center',
        width: 100,
      },
      {
        title: `作品简介`,
        dataIndex: 'introduction',
        align: 'center',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: `商品数`,
        dataIndex: 'goodsNum',
        align: 'center',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: `发布时间`,
        dataIndex: 'createTime',
        align: 'center',
        width: 100,
      },
      {
        title: `作品类型`,
        dataIndex: 'videoTypeValue',
        align: 'center',
        width: 100,
      },
      {
        title: `审核状态`,
        dataIndex: 'stateValue',
        align: 'center',
        width: 100,
      },
      {
        title: `审核意见`,
        dataIndex: 'remark',
        align: 'center',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
    ],
  });

  const searchFormSchema = reactive({
    data: [
      {
        component: 'Input',
        label: `作品名称`,
        field: 'videoName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请输入作品名称`,
        },
      },
      {
        component: 'Input',
        label: `会员名称`,
        field: 'memberName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请输入会员名称`,
        },
      },
      {
        component: 'Input',
        label: `作者昵称`,
        field: 'memberNickname',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请输入作者昵称`,
        },
      },
      {
        colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
        component: 'RangePicker',
        label: `发布时间`,
        field: '[startTime,endTime]',
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: ['开始时间', '结束时间'],
        },
      },
      {
        component: 'Select',
        label: `审核状态`,
        field: 'state',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请选择审核状态`,
          options: [
            { value: '', label: `全部` },
            { value: '1', label: `待审核` },
            { value: '3', label: `审核失败` },
          ],
        },
      },
      {
        component: 'Select',
        label: `作品类型`,
        field: 'videoType',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请选择作品类型`,
          options: [
            { value: '', label: `全部` },
            { value: '1', label: `视频` },
            { value: '2', label: `图文` },
          ],
        },
      },
    ],
  });

  const checkData = ref([
    {
      type: 'show_content', //展示文本内容
      name: 'memberName',
      label: `会员名称`, //文本内容
      content: '',
    },
    {
      type: 'show_content', //展示文本内容
      name: 'memberNickname',
      label: `作者昵称`, //文本内容
      content: '',
    },
    {
      type: 'show_content', //展示文本内容
      name: 'videoName',
      label: `作品名称`, //文本内容
      content: '',
    },
    {
      type: 'show_content', //展示文本内容
      name: 'introduction',
      label: `作品简介`, //文本内容
      content: '',
    },
    {
      type: 'show_image',
      name: 'videoImage',
      label: `作品封面`, //展示图片，并可以预览
      width: '100px',
      height: '100px',
      content: '', //单张
    },
    {
      type: 'view_video',
      label: `作品内容`,
      name: 'videoPath',
      width: 300,
      height: 200,
      content: '',
    },
  ]);

  const goods_table_columns = ref([
    {
      title: ' ',
      align: 'center',
      width: 50,
      customRender: ({ text, record, index }) => {
        return `${index + 1}`;
      },
    },
    {
      title: `商品名称`,
      dataIndex: 'goodsName',
      align: 'center',
      width: 150,
    },
    {
      title: `商品图片`,
      dataIndex: 'goodsImage',
      align: 'center',
      width: 100,
      dataType: 'img',
      imgWidth: '60px',
    },
    {
      title: `店铺名称`,
      dataIndex: 'storeName',
      align: 'center',
      width: 150,
    },
    {
      title: `商品价格(¥)`,
      dataIndex: 'goodsPrice',
      align: 'center',
      width: 150,
    },
    {
      title: `商品库存`,
      align: 'center',
      dataIndex: 'goodsStock',
      width: 100,
    },
  ]);

  // 表格数据
  const [registerTable, { reload }] = useTable({
    // 请求接口
    api: getVideoListApi,
    // 表头
    columns: columns.data,
    immediate: true,
    searchInfo: {
      type: 'audit',
    },
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: searchFormSchema.data,
    },
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    // 不显示表格设置工具
    showTableSetting: false,
    // 点击搜索前处理的参数
    beforeFetch(values) {
      values.startTime = values.startTime
        ? values.startTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
      return values;
    },
    // 是否显示表格边框
    bordered: true,
    ellipsis: false,
    // 是否显示序号列
    showIndexColumn: true,
    // 表格右侧操作列配置
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
      // slots: { customRender: 'action' },
    },
  });

  // 操作  type==1 打开静止发布弹窗 2解除禁止 3禁止
  const operateVideo = async (record, type) => {
    try {
      let res;
      let param_data = {};
      if (type == 1) {
        let obj = [];
        let data = [];
        obj = JSON.parse(JSON.stringify(checkData.value));
        let res_one = await getVideoGoodsApi({ videoId: record.videoId, videoType: 1 });
        if (res_one.state == 200) {
          data = res_one.data.list;
        }
        for (let i in obj) {
          if (obj[i].name == 'videoPath') {
            if (record.videoType == 1) {
              //视频
              obj[i].type = 'view_video';
              obj[i].content = record.videoPath;
            } else {
              //图文
              obj[i].type = 'view_img_text';
              obj[i].content = record.imageList;
              obj[i].text = record.videoContent;
            }
          } else {
            obj[i].content = record[obj[i].name];
          }
        }
        if (data.length > 0) {
          obj.push({
            type: 'show_content_table', //展示表格，没有分页，不能勾选
            name: 'bind_goods',
            label: `绑定的商品`,
            content: '',
            data: data,
            scrollHeight: 300, // 设置纵向滚动，也可用于指定滚动区域的高，可以设置为像素值
            columns: goods_table_columns.value,
            rowKey: 'goodsId',
            scroll: false,
          });
        }
        operateData.value = [];
        if (record.state == 1) {
          obj.push({
            type: 'radio_select',
            label: `审核`, //单选
            name: 'isPass',
            data: [
              //字段为key：选中的值  value：页面显示内容
              {
                key: true,
                value: `通过`,
              },
              {
                key: false,
                value: `拒绝`,
              },
            ],
            initialValue: true, //默认选中
            callback: true, //开启change事件:@radio-change-event   仅改变值的话不需要开启事件
          });
          showFoot.value = true;
        } else {
          obj.push(
            {
              type: 'show_content', //展示文本内容
              name: 'stateValue',
              label: `审核结果`, //文本内容
              content: record.stateValue,
            },
            {
              type: 'show_content', //展示文本内容
              name: 'remark',
              label: `审核意见`, //文本内容
              content: record.remark,
            },
          );
          showFoot.value = false;
        }
        operateData.value = obj;
        videoId.value = record.videoId;
        modalVisible.value = true;
        return;
      } else if (type == 3) {
        param_data = { ...record };
        param_data.videoId = videoId.value;
        res = await getVideoAuditApi(param_data);
      }
      btnLoading.value = true;
      if (res.state == 200) {
        modalVisible.value = false;
        btnLoading.value = false;
        sucTip(res.msg);
        operateData.value = [];
        reload();
      } else {
        btnLoading.value = false;
        failTip(res.msg);
      }
    } catch (error) {
      console.info(error, 'error');
    }
  };

  // radio_select状态的切换事件
  const radioChangeEvent = (value, index, from_info) => {
    operateData.value = [];
    let info = JSON.parse(JSON.stringify(from_info));
    if (!value) {
      let remark = {
        type: 'textarea',
        label: `审核意见`, //textarea输入框
        name: 'remark',
        extra: '最多100字',
        placeholder: `请输入审核意见`,
        maxLength: 100, //最多字数
        disable: false, //是否禁用
        rules: [
          {
            required: true,
            message: `请输入审核意见`,
          },
        ],
      };
      info.splice(index + 1, 0, remark);
    } else {
      info[index].initialValue = value;
      info.splice(index + 1, 1);
    }
    operateData.value = info;
  };

  //关闭弹框
  const handleModalCancle = () => {
    modalVisible.value = false;
  };

  const handleModalConfirm = (val) => {
    for (var i in val) {
      if (val[i] == undefined) {
        delete val[i];
      }
    }
    operateVideo(val, 3);
  };

  onMounted(() => {});
</script>
<style lang="less">
  .svideo_author_lists {
    .ant-table-row-expand-icon {
      float: initial;
    }

    .ant-table-cell {
      &:first-child {
        text-align: center;
      }
    }

    .business_load_img {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60px;
      height: 60px;
      margin: auto;

      img {
        max-width: 100%;
        max-height: 100%;
      }
    }

    .goods_info {
      position: relative;

      .virtual_goods_flag {
        position: absolute;
        top: 1px;
        left: 1px;
        padding: 0 3px;
        border-radius: 3px 0;
        background: linear-gradient(90deg, #f7d47e 0%, #e6b845 100%);
        color: #fff;
        font-size: 12px;
      }

      .goods_detail {
        flex: 1;
        width: 169px;
        height: 80px;
        margin-left: 10px;
      }

      .goods_img {
        display: inline-block;
        width: 80px;
        height: 80px;
        overflow: hidden;
        border: 1px solid rgb(226 229 246 / 100%);
        border-radius: 3px;
        background: rgb(248 248 248 / 100%);
      }

      .goods_name {
        display: -webkit-box;
        flex-shrink: 0;
        height: 34px;
        overflow: hidden;
        color: #333;
        font-size: 13px;
        line-height: 17px;
        text-align: left;
        text-overflow: ellipsis;
        word-break: break-word;
        -webkit-line-clamp: 2;

        /*! autoprefixer: off */
        -webkit-box-orient: vertical;
      }

      .goods_brief {
        display: inline-block;
        width: 100%;
        overflow: hidden;
        color: #666;
        font-size: 12px;
        text-align: left;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  p {
    margin-bottom: 0;
  }
</style>
