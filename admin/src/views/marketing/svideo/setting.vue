<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader title="短视频设置" />
      <Tabs type="card" v-model:activeKey="activeKey">
        <TabPane key="1" tab="基本设置">
          <div class="section_padding_set_scroll">
            <SettingPage class="section_padding_tab_top" v-if="activeKey == 1" />

          </div>
        </TabPane>
        <TabPane key="2" tab="风格配置">
          <SvideoDiyStyleM class="section_padding_tab_top" v-if="activeKey == 2"></SvideoDiyStyleM>
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'SvideoSetting',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { Tabs, TabPane, message, RadioGroup, RadioButton } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import SettingPage from './setting_page.vue';
  import SvideoDiyStyleM from './diy_style_m.vue';

  const activeKey = ref('1');

  onMounted(() => {});
</script>
<style lang="less">
  .RadioGroup_back {
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within {
      box-shadow: none;
    }
  }
</style>
