<template>
  <div class="section_padding video_goods">
    <div class="section_padding_back">
      <SldComHeader :title="title" :back="true" />
      <div class="com_line" style="height: 1px"></div>
      <BasicTable @register="registerTable" rowKey="themeVideoId">
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'goodsImage'">
            <Popover placement="rightTop">
              <template #content>
                <div style="width: 200px">
                  <img :src="text" alt="" style="max-width: 100%; max-height: 100%" />
                </div>
              </template>
              <div class="business_load_img">
                <img :src="text" alt="" />
              </div>
            </Popover>
          </template>
        </template>
      </BasicTable>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'VideoGoods',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { useRoute } from 'vue-router';
  import { getVideoGoodsApi } from '/@/api/svideo/video_manage';
  import { Popover } from 'ant-design-vue';

  const route = useRoute();

  // 参数
  const id = ref(route.query?.id);

  const title = ref(route.query?.videoName + '绑定的商品');

  const searchInfo = ref({
    videoId: id.value,
    videoType: 1,
  });
  const columns = reactive({
    data: [
      {
        title: `商品名称`,
        dataIndex: 'goodsName',
        align: 'center',
        width: 180,
      },
      {
        title: `商品图片`,
        dataIndex: 'goodsImage',
        align: 'center',
        width: 100,
      },
      {
        title: `店铺名称`,
        dataIndex: 'storeName',
        align: 'center',
        width: 100,
      },
      {
        title: `商品价格`,
        dataIndex: 'goodsPrice',
        align: 'center',
        width: 100,
      },
      {
        title: `商品库存`,
        dataIndex: 'goodsStock',
        align: 'center',
        width: 100,
      },
    ],
  });

  const searchFormSchema = reactive({
    data: [
      {
        component: 'Input',
        label: `商品名称`,
        field: 'goodsName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请输入商品名称`,
        },
      },
    ],
  });

  // 表格数据
  const [registerTable] = useTable({
    // 请求接口
    api: getVideoGoodsApi,
    // 表头
    columns: columns.data,
    immediate: true,
    // 参数
    searchInfo: searchInfo.value,
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: searchFormSchema.data,
    },
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    ellipsis: false,
    // 是否显示序号列
    showIndexColumn: true,
  });

  onMounted(() => {});
</script>
<style lang="less" scoped>
  .video_goods {
    .business_load_img {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60px;
      height: 60px;
      margin: auto;

      img {
        max-width: 100%;
        max-height: 100%;
      }
    }
  }

  p {
    margin-bottom: 0;
  }
</style>
