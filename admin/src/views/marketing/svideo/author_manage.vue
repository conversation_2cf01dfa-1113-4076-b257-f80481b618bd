<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader title="作者管理" />
      <Tabs type="card" v-model:activeKey="activeKey">
        <TabPane key="1" tab="作者列表">
          <AuthorLists class="section_padding_tab_top"></AuthorLists>
        </TabPane>
        <TabPane key="2" tab="作者审核">
          <AuthorCheckLists class="section_padding_tab_top"></AuthorCheckLists>
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'AuthorManage',
  };
</script>
<script setup>
  import { ref, onMounted } from 'vue';
  import { Tabs, TabPane } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import AuthorLists from './author_lists.vue';
  import AuthorCheckLists from './author_check_lists.vue';

  const activeKey = ref('1');

  onMounted(() => {});
</script>
<style lang="less">
  .RadioGroup_back {
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within {
      box-shadow: none;
    }
  }
</style>
