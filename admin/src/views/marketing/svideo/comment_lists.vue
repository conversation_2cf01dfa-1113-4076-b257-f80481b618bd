<template>
  <div class="section_padding comment_lists">
    <div class="section_padding_back">
      <SldComHeader title="作品管理" />
      <BasicTable @register="registerTable" rowKey="videoId">
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'videoImage'">
            <Popover placement="rightTop">
              <template #content>
                <div style="width: 200px">
                  <img :src="text" alt="" style="max-width: 100%; max-height: 100%" />
                </div>
              </template>
              <div class="business_load_img">
                <img :src="text" alt="" />
              </div>
            </Popover>
          </template>
          <template v-if="column.dataIndex === 'action'">
            <TableAction
              v-if="record.commentNum > 0"
              :actions="[
                {
                  label: '查看评论',
                  ifShow: record.commentNum > 0,
                  onClick: operateVideo.bind(null, record, 'look'),
                },
                {
                  label: '删除评论',
                  ifShow: record.commentNum > 0,
                  popConfirm: {
                    title: '删除后不可恢复，是否确定删除？',
                    placement: 'left',
                    confirm: operateVideo.bind(null, record, 'del'),
                  },
                },
              ]"
            />
            <span v-else class="action_btn">--</span>
          </template>
        </template>
      </BasicTable>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'SvideoCommentLists',
  };
</script>
<script setup>
  import { ref, onMounted, reactive } from 'vue';
  import {} from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useRoute, useRouter } from 'vue-router';
  import { getVideoCommentListApi, getVideoCommentDelApi } from '/@/api/svideo/comment_lists';
  import { Popover } from 'ant-design-vue';
  import { failTip, sucTip } from '/@/utils/utils';

  const route = useRoute();

  const router = useRouter();

  const videoId = ref('');

  const columns = reactive({
    data: [
      {
        title: `作品名称`,
        dataIndex: 'videoName',
        align: 'center',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: `封面`,
        dataIndex: 'videoImage',
        align: 'center',
        width: 100,
      },
      {
        title: `作品简介`,
        dataIndex: 'introduction',
        align: 'center',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: `会员名称`,
        dataIndex: 'memberName',
        align: 'center',
        width: 150,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: `作者昵称`,
        dataIndex: 'memberNickname',
        align: 'center',
        width: 150,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: `评论数`,
        dataIndex: 'commentNum',
        align: 'center',
        width: 150,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: `作品类型`,
        dataIndex: 'videoTypeValue',
        align: 'center',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
    ],
  });

  const searchFormSchema = reactive({
    data: [
      {
        component: 'Input',
        label: `作品名称`,
        field: 'videoName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请输入作品名称`,
        },
      },
      {
        component: 'Input',
        label: `会员名称`,
        field: 'memberName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请输入会员名称`,
        },
      },
      {
        component: 'Input',
        label: `作者昵称`,
        field: 'memberNickname',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请输入作者昵称`,
        },
      },
      {
        component: 'Select',
        label: `作品类型`,
        field: 'videoType',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请选择作品类型`,
          options: [
            { value: '', label: `全部` },
            { value: '1', label: `视频` },
            { value: '2', label: `图文` },
          ],
        },
      },
    ],
  });

  // 表格数据
  const [registerTable, { reload }] = useTable({
    // 请求接口
    api: getVideoCommentListApi,
    // 表头
    columns: columns.data,
    immediate: true,
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: searchFormSchema.data,
    },
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    ellipsis: false,
    // 是否显示序号列
    showIndexColumn: true,
    // 表格右侧操作列配置
    actionColumn: {
      width: 240,
      title: '操作',
      dataIndex: 'action',
      // slots: { customRender: 'action' },
    },
  });

  // 操作  type==show 解除禁止 del 删除  ''禁止
  const operateVideo = async (record, type) => {
    try {
      let res;
      let param_data = {};
      if (type == 'del') {
        param_data.videoId = record.videoId;
        res = await getVideoCommentDelApi(param_data);
      } else if (type == 'look') {
        router.push({
          path: `/marketing_svideo/comment_lists_to_view`,
          query: { id: record.videoId, videoName: record.videoName },
        });
        return;
      }
      if (res.state == 200) {
        sucTip(res.msg);
        reload();
      } else {
        failTip(res.msg);
      }
    } catch (error) {}
  };

  onMounted(() => {});
</script>
<style lang="less">
  .comment_lists {
    .ant-table-row-expand-icon {
      float: initial;
    }

    .ant-table-cell {
      &:first-child {
        text-align: center;
      }
    }

    .business_load_img {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60px;
      height: 60px;
      margin: auto;

      img {
        max-width: 100%;
        max-height: 100%;
      }
    }

    .goods_info {
      position: relative;

      .virtual_goods_flag {
        position: absolute;
        top: 1px;
        left: 1px;
        padding: 0 3px;
        border-radius: 3px 0;
        background: linear-gradient(90deg, #f7d47e 0%, #e6b845 100%);
        color: #fff;
        font-size: 12px;
      }

      .goods_detail {
        flex: 1;
        width: 169px;
        height: 80px;
        margin-left: 10px;
      }

      .goods_img {
        display: inline-block;
        width: 80px;
        height: 80px;
        overflow: hidden;
        border: 1px solid rgb(226 229 246 / 100%);
        border-radius: 3px;
        background: rgb(248 248 248 / 100%);
      }

      .goods_name {
        display: -webkit-box;
        flex-shrink: 0;
        height: 34px;
        overflow: hidden;
        color: #333;
        font-size: 13px;
        line-height: 17px;
        text-align: left;
        text-overflow: ellipsis;
        word-break: break-word;
        -webkit-line-clamp: 2;

        /*! autoprefixer: off */
        -webkit-box-orient: vertical;
      }

      .goods_brief {
        display: inline-block;
        width: 100%;
        overflow: hidden;
        color: #666;
        font-size: 12px;
        text-align: left;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  p {
    margin-bottom: 0;
  }
</style>
