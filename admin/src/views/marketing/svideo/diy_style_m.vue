<template>
  <Spin :spinning="spinning">
    <div class="svideo_diy_style">
      <div class="diy_style_list flex_row_start_start" style="flex-wrap: wrap">
        <div class="" v-for="(item, index) in color_list" :key="index">
          <div
            class="flex_row_center_center diy_style_item"
            :class="{ active: color_index == index }"
            @click="selectColor(index)"
          >
            <div class="diy_style_color flex_row_center_center">
              <div class="diy_style_color_left" :style="{ background: item.mainColor }"></div>
            </div>
            <div class="diy_style_name">{{ item.name }}</div>
            <img
              src="@/assets/images/diy_style_checked.png"
              class="diy_style_checked"
              alt=""
              v-if="color_index == index"
            />
          </div>
        </div>
        <div
          @click="selectAutoColor"
          class="flex_row_center_center diy_style_item"
          :class="{ active: color_index == color_list.length }"
        >
          <div
            class="diy_style_color flex_row_center_center"
            v-if="color_auto.mainColor && color_auto.mainColor != 'rgba(255,255,255,1)'"
          >
            <div class="diy_style_color_left" :style="{ background: color_auto.mainColor }"></div>
          </div>
          <div class="diy_style_auto_name" v-else>自定义颜色选择</div>
          <div
            class="diy_style_name"
            v-if="color_auto.mainColor && color_auto.mainColor != 'rgba(255,255,255,1)'"
          >
            重选
          </div>
          <div class="diy_style_auto_arrow" v-else> &gt; </div>
          <img
            class="diy_style_checked"
            src="@/assets/images/diy_style_checked.png"
            alt=""
            v-if="color_index == color_list.length"
          />
        </div>
      </div>
      <div class="prew_title"> 图片预览 </div>
      <div class="flex_row_start_center prew_list">
        <div class="prew_item prew_item_center">
          <div
            class="top_bg"
            :style="{
              background:
                color_index < color_list.length
                  ? color_list[color_index].mainVerticalColor
                  : color_auto.mainVerticalColor,
            }"
          ></div>
          <img class="top_system" src="@/assets/images/marketing/top_system_info.png" />
          <div class="flex_row_between_center top_search">
            <div class="flex_row_center_center left">短视频</div>
            <div class="flex_row_start_center middle">
              <AliSvgIcon iconName="iconsousuo" width="18px" height="18px" fillColor="#FFFFFF" />
              <span>请输入关键词</span>
            </div>
            <img class="right" src="@/assets/images/marketing/top_right_btn.png" />
          </div>
          <div class="flex_row_start_center top_nav">
            <div
              class="top_nav_item"
              v-for="(item, index) in ['精选', '科技产品', '数码家电', '美食天地', '潮牌']"
              :key="index"
              >{{ item }}</div
            >
          </div>
          <div class="main_list" :style="{ backgroundImage: `url('${diy_style_video_bg}')` }"></div>
          <div
            class="flex_row_center_center center_btn"
            :style="{
              background:
                color_index < color_list.length
                  ? color_list[color_index].mainColor
                  : color_auto.mainColor,
            }"
          >
            <img src="@/assets/images/marketing/video/diy_style_head.png" />
            <div>我的视频</div>
          </div>
        </div>
      </div>
      <div class="m_diy_bottom_wrap" :style="{ position: 'fixed', left: getRealWidth + 10 + 'px' }">
        <div class="add_goods_bottom_btn add_goods_bottom_btn_sel" @click="handleSaveAllData">
          保存
        </div>
      </div>
      <SldModal
        :width="500"
        title="自定义颜色选择"
        :visible="modalVisible"
        :content="content_color"
        :showFoot="true"
        :confirmBtnLoading="false"
        @cancle-event="handleModalCancle"
        @confirm-event="handleModalConfirm"
      />
    </div>
  </Spin>
</template>
<script>
  export default {
    name: 'SvideoDiyStyleM',
  };
</script>
<script setup>
  import { ref, onMounted } from 'vue';
  import { Spin } from 'ant-design-vue';
  import SldModal from '/@/components/SldModal/index.vue';
  import { getSettingListApi, getSettingUpdateApi } from '/@/api/svideo/setting';
  import diy_style_video_bg from '/@/assets/images/marketing/video/diy_style_video_bg.png';
  import diy_style_head from '/@/assets/images/marketing/video/diy_style_head.png';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import { failTip, sucTip } from '/@/utils/utils';

  const { getRealWidth } = useMenuSetting();

  const color_index = ref(0);

  const spinning = ref(true); //loading

  const content_color = ref([]);

  const diy_coupon_reduction = ref([
    { price: 5, desc: '满49元减5元', time: '2023.04.11~2023.05.11', percent: 64 },
    { price: 10, desc: '满99元减10元', time: '2023.04.11~2023.05.11', percent: 32 },
    { price: 20, desc: '满149元减20元', time: '2023.04.13~2023.05.13', percent: 16 },
    { price: 30, desc: '满199元减30元', time: '2023.04.15~2023.05.15', percent: 0 },
  ]);

  const diy_coupon_reduction_one = ref([
    { label: '满减券', price: 5, type: '通用', desc: '满49元减5元', time: '2023.04.11~2023.05.11' },
    {
      label: '折扣券',
      discount: '8.0',
      type: '通用',
      desc: '最多优惠10元',
      time: '2023.04.11~2023.05.11',
    },
    {
      label: '折扣券',
      discount: '8.8',
      type: '指定商品使用',
      desc: '最多优惠50元',
      time: '2023.04.13~2023.05.13',
    },
    {
      label: '满减券',
      price: 10,
      type: '通用',
      desc: '满99元减10元',
      time: '2023.04.15~2023.05.15',
    },
    { label: '满减券', price: 5, type: '通用', desc: '满49元减5元', time: '2023.04.15~2023.05.15' },
  ]);

  const color_list = ref([
    //颜色列表
    {
      mainColor: '#ED0775',
      mainOpacity: '#ED0775B3',
      mainLinearColor: 'linear-gradient(90deg, #ED0775BF, #ED0775)',
      mainVerticalColor: 'linear-gradient(0deg, #ED077500, #ED0775)',
      name: '默认色',
    },
    {
      mainColor: '#39BC17',
      mainOpacity: '#39BC17B3',
      mainLinearColor: 'linear-gradient(90deg, #39BC17BF, #39BC17)',
      mainVerticalColor: 'linear-gradient(0deg, #39BC1700, #39BC17)',
      name: '翡翠绿',
    },
    {
      mainColor: '#E84165',
      mainOpacity: '#E84165B3',
      mainLinearColor: 'linear-gradient(90deg, #E84165BF, #E84165)',
      mainVerticalColor: 'linear-gradient(0deg, #E8416500, #E84165)',
      name: '雅致粉',
    },
    {
      mainColor: '#884CFF',
      mainOpacity: '#884CFFB3',
      mainLinearColor: 'linear-gradient(90deg, #884CFFBF, #884CFF)',
      mainVerticalColor: 'linear-gradient(0deg, #884CFF00, #884CFF)',
      name: '魅力紫',
    },
    {
      mainColor: '#F5BF41',
      mainOpacity: '#F5BF41B3',
      mainLinearColor: 'linear-gradient(90deg, #F5BF41BF, #F5BF41)',
      mainVerticalColor: 'linear-gradient(0deg, #F5BF4100, #F5BF41)',
      name: '经典黄',
    },
    {
      mainColor: '#ED682E',
      mainOpacity: '#ED682EB3',
      mainLinearColor: 'linear-gradient(90deg, #ED682EBF, #ED682E)',
      mainVerticalColor: 'linear-gradient(0deg, #ED682E00, #ED682E)',
      name: '活力橙',
    },
    {
      mainColor: '#1E78F5',
      mainOpacity: '#1E78F5B3',
      mainLinearColor: 'linear-gradient(90deg, #1E78F5BF, #1E78F5)',
      mainVerticalColor: 'linear-gradient(0deg, #1E78F500, #1E78F5)',
      name: '天空蓝',
    },
    {
      mainColor: '#1E1C1B',
      mainOpacity: '#1E1C1BB3',
      mainLinearColor: 'linear-gradient(90deg, #1E1C1BBF, #1E1C1B)',
      mainVerticalColor: 'linear-gradient(0deg, #1E1C1B00, #1E1C1B)',
      name: '雅酷黑',
    },
  ]);

  const color_auto = ref({
    //自定义颜色
    mainColor: '', //主色
    mainOpacity: '', //主色透明
    mainLinearColor: '', //主色渐变色
    mainVerticalColor: '', //主色垂直渐变色
  });

  const modalVisible = ref(false);

  // 获取数据
  const get_style_setting = async () => {
    try {
      let res = await getSettingListApi({ str: 'mobile_video_mall_style' });
      if (res.state == 200 && res.data) {
        let data = !(res.data[0] && res.data[0].value) ? {} : JSON.parse(res.data[0].value);
        if (Object.keys(data).length > 0) {
          let colorIndex = color_list.value.length;
          let mainColor =
            data.mainColor != undefined
              ? data.mainColor == '#fff'
                ? '#FFFFFF'
                : data.mainColor
              : '';
          let mainOpacity =
            data.mainOpacity != undefined
              ? data.mainColor == '#fff'
                ? '#FFFFFF17'
                : data.mainOpacity
              : '';
          let mainLinearColor =
            data.mainLinearColor != undefined
              ? data.mainColor == '#fff'
                ? '#FFFFFF'
                : data.mainLinearColor
              : '';
          let mainVerticalColor =
            data.mainVerticalColor != undefined
              ? data.mainColor == '#fff'
                ? '#FFFFFF'
                : data.mainVerticalColor
              : '';
          if (mainColor && mainOpacity && mainLinearColor && mainVerticalColor) {
            for (var index = 0; index < color_list.value.length; index++) {
              if (
                color_list.value[index].mainColor == mainColor &&
                color_list.value[index].mainOpacity == mainOpacity &&
                color_list.value[index].mainLinearColor == mainLinearColor &&
                color_list.value[index].mainVerticalColor == mainVerticalColor
              ) {
                colorIndex = index;
                break;
              }
            }
            if (colorIndex == color_list.value.length) {
              colorIndex = color_list.value.length;
              color_auto.value.mainColor = mainColor;
              color_auto.value.mainOpacity = mainOpacity;
              color_auto.value.mainLinearColor = mainLinearColor;
              color_auto.value.mainVerticalColor = mainVerticalColor;
              color_index.value = colorIndex;
            } else {
              color_index.value = colorIndex;
            }
          }
        }
      } else {
        failTip(res.msg);
      }
      spinning.value = false;
    } catch (error) {
      console.log(error);
      spinning.value = false;
    }
  };

  const selectAutoColor = () => {
    content_color.value = [];
    let content_info = [
      {
        type: 'more_color_picker',
        label: `主色选择`, //主色选择
        name: 'mainColor',
        initialValue: color_auto.value.mainColor ? color_auto.value.mainColor : '#fff',
        is_show: false,
      },
    ];
    content_color.value = content_info;
    modalVisible.value = true;
  };

  //选择颜色弹窗关闭
  const handleModalCancle = () => {
    content_color.value = [];
    modalVisible.value = false;
  };

  //选择颜色弹窗保存
  const handleModalConfirm = (val) => {
    let resetFlag = 0;
    color_auto.value.mainColor = val.mainColor;
    color_auto.value.mainOpacity = val.mainColor.replace(',1)', ',.7)');
    color_auto.value.mainLinearColor =
      'linear-gradient(90deg, ' +
      val.mainColor.replace(',1)', ',.75)') +
      ', ' +
      val.mainColor +
      ')';
    color_auto.value.mainVerticalColor =
      'linear-gradient(0deg, ' + val.mainColor.replace(',1)', ',0)') + ', ' + val.mainColor + ')';
    for (let key in color_auto.value) {
      if (color_auto.value[key] == 'rgba(255,255,255,1)' || !color_auto.value[key]) {
        resetFlag += 1;
      }
    }
    color_index.value = resetFlag == 1 ? 0 : color_list.value.length;
    modalVisible.value = false;
  };

  //选择颜色
  const selectColor = (index) => {
    color_index.value = index;
  };

  const handleSaveAllData = async () => {
    try {
      let params = {};
      if (color_index.value < color_list.value.length) {
        params = JSON.stringify(color_list.value[color_index.value]);
      } else {
        params = JSON.stringify(color_auto.value);
      }
      let res = await getSettingUpdateApi({ mobile_video_mall_style: params });
      if (res.state == 200) {
        sucTip(res.msg);
      } else {
        failTip(res.msg);
      }
    } catch (error) {}
  };

  onMounted(() => {
    get_style_setting();
  });
</script>
<style lang="less">
  @import './style/diy_style.less';
</style>
