.add_theme {
  .add_theme_height {
    max-height: calc(100vh - @header-height - 20px - 146px);
    overflow: auto;
  }

  .ant-table-body {
    height: auto !important;
  }

  .full_activity {
    .ant-form-inline {
      display: block;
    }

    .ant-form-item-extra {
      font-size: 12px;
    }

    .ant-form-inline .ant-form-item-with-help {
      margin-bottom: 0;
    }

    .ant-form-item-control .ant-form-item-explain {
      position: absolute;
      z-index: 2;
      top: -10px;
      right: 4%;
      min-height: 20px;
      background: #fff;
      font-size: 13px;
      text-align: left;
    }

    .cycle_box {
      .ant-form-item-control .ant-form-item-explain {
        right: 20%;
      }
    }

    .business_load_img {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60px;
      height: 60px;
      margin: auto;

      img {
        max-width: 100%;
        max-height: 100%;
      }
    }

    .extra_box {
      box-sizing: border-box;
      min-height: 24px;
      clear: both;
      transition: color 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
      color: rgb(0 0 0 / 45%);
      font-size: 12px;
      line-height: 1.5715;
    }

    .full_acm_activity {
      .item {
        padding-top: 15px;

        .left {
          width: 200px;
          height: 32px;
          padding-right: 20px;
          font-size: 13px;
          line-height: 32px;
          text-align: right;
        }

        .right {
          .reset_sel {
            display: inline-block;
            margin-top: 5px;
            margin-bottom: 10px;
            margin-left: 0;
            color: #ff711e;
            font-size: 13px;
            cursor: pointer;
          }

          .scroll_bars_table {
            max-height: 300px;
            overflow: auto;
          }

          .scroll_bars_tree {
            min-height: 100px;
            max-height: 300px;
            overflow-y: scroll;
          }
        }
      }
    }

    .flex_zi {
      flex: 1;
      align-items: center;
      justify-content: center;
    }

    .common_title_bg {
      width: 100%;
      height: 36px;
      border-radius: 2px;
      background: #fffaf7;
      line-height: 36px;

      .title {
        padding-left: 10px;
        color: #333;
        font-size: 13px;
      }

      .del_ladder_pro {
        display: none;
        padding-top: 7px;
        padding-right: 15px;
      }
    }

    .add_new {
      width: 100%;
      height: 40px;
      margin-top: 15px;
      border-radius: 3px;
      background: #fffaf7;

      .add_new_tip {
        margin-left: 10px;
        color: #ff7f40;
        font-size: 12px;
      }
    }

    .sel_goods {
      padding-left: 24px;

      .sel_tip {
        color: #999;
        font-size: 12px;
      }

      .reset_sel_goods {
        color: #ff7f40;
        font-size: 12px;
        cursor: pointer;
      }

      .goods_info {
        min-width: 260px;
        max-width: 700px;
        height: 60px;
        margin-top: 5px;
        margin-left: 10px;
        padding: 10px;
        border-radius: 3px;
        background: #f8f8f8;

        .left {
          flex-shrink: 0;
          width: 40px !important;
          height: 40px !important;
          margin-right: 10px;
          padding-right: 0 !important;
          overflow: hidden;
          border-radius: 3px;

          img {
            max-width: 100%;
            max-height: 100%;
            border-radius: 3px;
          }
        }

        .goods_name {
          color: #333;
          font-size: 13px;
        }

        .goods_price {
          margin-top: 3px;
          color: #666;
          font-size: 12px;
          white-space: nowrap;
        }
      }
    }
  }
}
