.svideo_diy_style {
  height: calc(100vh - 48px - 20px - 20px - 32px - 15px - 48px - 60px);
  padding: 10px 0;
  overflow: auto;

  .diy_style_list {
    .diy_style_item {
      position: relative;
      width: 122px;
      height: 48px;
      margin-right: 10px;
      margin-bottom: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      cursor: pointer;

      &.active {
        border-color: #fc701e;
      }

      .diy_style_color {
        width: 46px;
        height: 20px;
        overflow: hidden;

        .diy_style_color_left {
          flex: 1;
          height: 20px;
        }

        .diy_style_color_middle {
          flex: 1;
          height: 20px;
          margin-right: 2px;
          margin-left: 2px;
        }

        .diy_style_color_right {
          flex: 1;
          height: 20px;
        }
      }

      .diy_style_name {
        margin-left: 8px;
        color: #666;
        font-size: 12px;
        text-align: center;
      }

      .diy_style_auto_name {
        color: #fc701e;
        font-size: 12px;
      }

      .diy_style_auto_arrow {
        flex-shrink: 0;
        width: 10px;
        height: 22px;
        margin-left: 5px;
        color: #fc701e;
        font-family: cursive;
        font-size: 14px;
        font-weight: 600;
        line-height: 22px;
      }

      .diy_style_checked {
        position: absolute;
        z-index: 1;
        right: -1px;
        bottom: -1px;
        width: 20px;
        height: 20px;
      }
    }
  }

  .prew_title {
    margin-bottom: 10px;
    color: #222;
    font-family: 'PingFang SC';
    font-size: 15px;
    font-weight: bold;
    line-height: 40px;
  }

  .prew_list {
    padding-left: 15px;

    .prew_item {
      position: relative;
      width: 375px;
      height: 666px;
      margin-right: 50px;
      overflow: hidden;
      border-radius: 10px;
      background-color: #f5f5f5;
      box-shadow: 0 0 12px 0 rgb(129 129 129 / 30%);
      cursor: default;

      &:last-of-type {
        margin-right: 0;
      }

      &.prew_item_center {
        .top_bg {
          position: absolute;
          z-index: 1;
          top: 0;
          left: 0;
          width: 375px;
          height: 296px;
        }

        .top_system {
          position: absolute;
          z-index: 2;
          top: 10px;
          left: 50%;
          width: 355px;
          height: 11px;
          margin-left: -177px;
        }

        .top_search {
          position: absolute;
          z-index: 2;
          top: 34px;
          left: 0;
          width: 372px;
          padding-right: 5px;
          padding-left: 10px;
          color: #fff;
          font-family: 'PingFang SC';
          font-size: 14px;
          font-weight: 400;

          .left {
            font-size: 16px;
            font-weight: bold;
          }

          .middle {
            width: 194px;
            height: 32px;
            padding: 0 12px;
            border-radius: 16px;
            background: #f0eff466;

            span {
              margin-left: 6px;
            }
          }

          .right {
            width: 89px;
            height: 29px;
          }
        }

        .top_nav {
          position: absolute;
          z-index: 2;
          top: 80px;
          left: 0;
          width: 375px;
          color: #fff;

          &.live_nav {
            .top_nav_item {
              padding: 0 12px;
            }
          }

          .top_nav_item {
            position: relative;
            padding: 0 15px;
            font-size: 14px;
            font-weight: 400;
            text-align: center;

            &:nth-child(1) {
              &::after {
                content: '';
                position: absolute;
                bottom: -7px;
                left: 50%;
                width: 26px;
                height: 3px;
                margin-left: -13px;
                border-radius: 2px;
                background-color: #fff;
              }
            }
          }
        }

        .main_list {
          position: absolute;
          z-index: 3;
          bottom: 0;
          left: 50%;
          width: 360px;
          height: 544px;
          margin-left: -180px;
          border-radius: 11px;
          background-repeat: no-repeat;
          background-position: center;
          background-size: contain;
        }

        .center_btn {
          position: absolute;
          z-index: 4;
          right: 0;
          bottom: 64px;
          width: 73px;
          height: 40px;
          border-radius: 20px 0 0 20px;

          img {
            flex-shrink: 0;
            width: 34px;
            height: 34px;
          }

          div {
            position: relative;
            left: 4px;
            width: 28px;
            color: #fff;
            font-family: 'PingFang SC';
            font-size: 12px;
            font-weight: 500;
          }
        }
      }
    }
  }
}

.diy_auto_color_modal {
  margin-top: 10px;
  margin-bottom: 10px;

  .color_title {
    width: 115px;
    margin-right: 5px;
    text-align: right;
  }

  .color_show {
    .show_color {
      display: inline-block;
      width: 50px;
      height: 30px;
      margin-right: 10px;
      padding: 5px;
      border: 1px solid #eee;
      line-height: 0;
      cursor: pointer;
    }

    span {
      display: inline-block;
      width: 100%;
      height: 100%;
    }
  }

  .color_picker_wrap {
    position: absolute;
    z-index: 2;

    .color_picker_mask {
      position: fixed;
      inset: 0;
    }
  }
}
