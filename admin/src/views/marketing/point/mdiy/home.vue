<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader title="首页装修" />
      <div class="seckill_label_lists">
        <BasicTable @register="standardTable" style="padding: 0">
          <template #tableTitle>
            <div class="toolbar">
              <div class="toolbar_btn" @click="handleClick(null, 'add', null, null)">
                <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#fff" />
                <span>新建页面</span>
              </div>
            </div>
          </template>
          <template #bodyCell="{ column, record, text }">
            <template
              v-if="
                column.dataIndex == 'android' ||
                column.dataIndex == 'ios' ||
                column.dataIndex == 'h5' ||
                column.dataIndex == 'weixinXcx'
              "
            >
              <div>
                <Switch
                  @change="
                    (checked) => handleClick(record, 'switch', checked ? 1 : 0, column.dataIndex)
                  "
                  :checked="text == 1 ? true : false"
                />
              </div>
            </template>
            <template v-if="column.key == 'action'">
              <template v-if="record.isInner == 1"> -- </template>
              <template v-else>
                <TableAction
                  class="TableAction"
                  :actions="[
                    {
                      label: '装修',
                      onClick: handleClick.bind(null, record, 'diy', null),
                    },
                    {
                      label: '编辑',
                      onClick: handleClick.bind(null, record, 'edit', null),
                    },
                    {
                      label: '复制',
                      onClick: handleClick.bind(null, record, 'copy', null),
                    },
                    {
                      label: '开屏图',
                      onClick: addAdv.bind(null, record),
                    },
                    {
                      label: '删除',
                      popConfirm: {
                        title: '删除后不可恢复，是否确定删除？',
                        placement: 'left',
                        confirm: handleClick.bind(null, record, 'del', null),
                      },
                    },
                  ]"
                />
              </template>
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <SldModal
      :width="width"
      :title="title"
      :visible="visible"
      :content="content"
      :confirmBtnLoading="confirmBtnLoading"
      :showFoot="true"
      @cancle-event="handleCancle"
      @confirm-event="handleConfirm"
    />

    <SldMoreImgModal
      :width="1000"
      :title="MoreTitle"
      :modalTip="modal_tip"
      :content="modalContent"
      ref="sldMoreImgModalRef"
      @confirm="moreImgConfirm"
      mode="integral"
      :totalNum="1"
      client="mobile"
    ></SldMoreImgModal>
  </div>
</template>
<script lang="ts">
  export default {
    name: 'PointDiyHome',
  };
</script>
<script lang="ts" setup>
  import { ref,reactive } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import SldModal from '@/components/SldModal/index.vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { Switch } from 'ant-design-vue';
  import SldMoreImgModal from '@/components/SldMoreImgModal/index.vue';
  import {
    getMobileDecoListApi,
    getMobileDecoAddApi,
    getMobileDecoUpdateApi,
    getMobileDecoDelApi,
    getMobileDecoIsUseApi,
    getMobileDecoCopyApi,
  } from '/@/api/point/mdiy';
  import { validatorEmojiRules, validatorSpecialString } from '/@/utils/validate';
  import { failTip, sucTip } from '/@/utils/utils';
  import { useRouter } from 'vue-router';

  const router = useRouter();
  const modal_tip = [
    '请严格根据提示要求上传规定尺寸的图片,图片不可以超过1M,否则影响页面加载效果',
    '编辑项中的“操作”指点击该内容所产生的链接地址，可通过下拉选项选择不同的方式',
  ];
  const MoreTitle = ref('设置开屏图')
  const modal_data = ref([
  {
    imgPath: '',
    imgUrl: '',
    info: {},
    link_type: '',
    link_value: '',
    title: '',
    width:0,
    height:0,
    }
  ])
  const modalContent = reactive(
    {
    width: 580,
    height: 776,
    show_width: 290,
    show_height: 388,
    data: [
      {
          imgPath: '',
          imgUrl: '',
          info: {},
          link_type: '',
          link_value: '',
          title: '',
          width:0,
          height:0,
    }
    ]
  })
  const [standardTable, { reload }] = useTable({
    api: (arg) => getMobileDecoListApi({ ...arg }),
    rowKey: 'decoId',
    searchInfo: {
      type: 'integral',
    },
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    columns: [
      {
        title: '页面名称',
        dataIndex: 'name',
        width: 120,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
        width: 130,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: '更新时间',
        dataIndex: 'updateTime',
        width: 150,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: 'Android',
        dataIndex: 'android',
        width: 60,
      },
      {
        title: 'IOS',
        dataIndex: 'ios',
        width: 60,
      },
      {
        title: '微商城',
        dataIndex: 'h5',
        width: 60,
      },
      {
        title: '微信小程序',
        dataIndex: 'weixinXcx',
        width: 60,
      },
    ],
    actionColumn: {
      width: 250,
      title: '操作',
      dataIndex: 'action',
    },
    useSearchForm: true,
    formConfig: {
      schemas: [
        {
          field: 'name',
          component: 'Input',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
          componentProps: {
            placeholder: '请输入页面名称',
            size: 'default',
          },
          label: '页面名称',
        },
      ],
    },
    bordered: true,
    striped: false,
    showIndexColumn: true,
  });
  const width = ref(600);
  const title = ref('');
  const visible = ref(false);
  const content: any = ref([]);
  const confirmBtnLoading = ref(false);
  const operate_id = ref('');
  const operate_type = ref('');

  const decoId = ref('');

  const operate_add_edit = ref<any>([
    {
      type: 'input',
      label: `装修页面名称`,
      name: 'name',
      placeholder: `请输入装修页面名称`,
      extra: '最多输入8个字',
      maxLength: 8,
      showCount: true,
      rules: [
        {
          required: true,
          whitespace: true,
          message: '请输入装修页面名称',
        },
        validatorEmojiRules,
        {
          validator: async (rule, value) => {
            await validatorSpecialString(rule, value);
          },
        },
      ],
    },
  ]); //modal框的数据

  //表格点击回调事件
  function handleClick(item, type, check, os) {
    operate_type.value = type;
    if (item) {
      operate_id.value = item.decoId;
    }
    if (type == 'diy') {
      router.push(`/marketing_point/diy_home_to_edit?id=${item.decoId}&source=marketing_point&type=integral`);
      return;
    }
    if (type == 'add' || type == 'edit') {
      content.value.forEach((it) => {
        it.initialValue = '';
      });
      content.value = [];
      operate_add_edit.value.forEach((it) => {
        it.initialValue = '';
      });
    }
    if (type == 'add') {
      content.value = [];
      title.value = '添加首页装修';
      visible.value = true;
      content.value = operate_add_edit.value;
    } else if (type == 'edit') {
      title.value = '编辑首页装修';
      visible.value = true;
      let data = operate_add_edit.value;
      data.map((items) => {
        items.initialValue = item[items.name] ? item[items.name] : undefined;
      });
      content.value = data;
      decoId.value = item.decoId;
    } else if (type == 'del') {
      operate_role({ decoId: item.decoId });
    } else if (type == 'switch') {
      operate_role({ decoId: item.decoId, isUse: check, os: os });
    } else if (type == 'copy') {
      operate_role({ decoId: item.decoId });
    }
  }

  const sldMoreImgModalRef = ref();
  const modalState = ref({})
  const addAdv = async(val)=> {
    modalState.value = val
    let adv_data = []
    MoreTitle.value = '设置开屏图'
    if (val.showTip != null && val.showTip) {
      MoreTitle.value = '编辑开屏图'
      adv_data = JSON.parse(val.showTip.replace(/&quot;/g,"\""));
    }
    if(adv_data.length>0){
      modalContent.data = adv_data
    }else {
      modalContent.data = JSON.parse(JSON.stringify(modal_data.value))
    }
    sldMoreImgModalRef.value.setModal(true);
  }

  const moreImgConfirm = async (dataSource) => {
    const res = await getMobileDecoUpdateApi({
      decoId: modalState.value.decoId,
      type:'integral',
      showTip: JSON.stringify(dataSource.parent_data),
    });
    if (res?.state == 200) {
      sucTip(res.msg);
      modalState.value = [];
      sldMoreImgModalRef.value.setModal(false);
      reload()
    } else {
      failTip(res.msg);
    }
  };


  //弹窗取消事件
  function handleCancle() {
    visible.value = false;
    content.value = [];
    decoId.value = '';
  }

  //弹窗确认事件
  function handleConfirm(val) {
    if (operate_type.value == 'edit') {
      val.decoId = decoId.value;
    } else {
      val.data = '';
    }
    val.type = 'integral';
    operate_role(val);
  }

  //商品标签操作
  const operate_role = async (params) => {
    confirmBtnLoading.value = true;
    let res: any = {};
    if (operate_type.value == 'add') {
      res = await getMobileDecoAddApi(params);
    } else if (operate_type.value == 'edit') {
      res = await getMobileDecoUpdateApi(params);
    } else if (operate_type.value == 'del') {
      res = await getMobileDecoDelApi(params);
    } else if (operate_type.value == 'switch') {
      res = await getMobileDecoIsUseApi(params);
    } else if (operate_type.value == 'copy') {
      res = await getMobileDecoCopyApi(params);
    }
    confirmBtnLoading.value = false;
    if (res.state == 200) {
      sucTip(res.msg);
      handleCancle();
      reload();
    } else {
      failTip(res.msg);
    }
  };
</script>
<style lang="less" scoped></style>
