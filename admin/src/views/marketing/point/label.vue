<template>
  <div class="section_padding point_label_body">
    <div class="section_padding_back">
      <SldComHeader :title="'标签管理'" />
      <BasicTable @register="standardTable" style="padding: 0">
        <template #tableTitle>
          <div class="toolbar">
            <div class="toolbar_btn" @click="addLabel">
              <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#ff5908" />
              <span>新增标签</span>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'imageUrl'">
            <Popover placement="rightTop" v-if="text">
              <template #content>
                <div
                  class="flex_row_center_center"
                  style="width: 160px; height: 160px; text-align: center"
                >
                  <img :src="text" alt="" style="max-width: 100%; max-height: 100%" />
                </div>
              </template>
              <div class="business_load_img">
                <img :src="text" alt="" />
              </div>
            </Popover>
            <div v-else>--</div>
          </template>
          <template v-if="column.dataIndex == 'state'">
            <div>
              <Switch
                @change="
                  (checked) =>
                    handleClick(
                      {
                        labelId: record.labelId,
                        state: checked ? 1 : 0,
                        parentLabelId: record.parentLabelId,
                      },
                      'show',
                    )
                "
                :checked="text == 1 ? true : false"
              />
            </div>
          </template>
          <template v-if="column.key == 'action'">
            <TableAction
              class="TableAction"
              :actions="[
                {
                  label: '设置广告',
                  ifShow: record.grade == 1,
                  onClick: addAdv.bind(null, record),
                },
                {
                  label: '编辑',
                  onClick: editLabel.bind(null, record),
                },
                {
                  label: '添加子标签',
                  ifShow: record.grade == 1,
                  onClick: addNextLabel.bind(null, record),
                },
                {
                  label: '删除',
                  ifShow: record.children == null,
                  popConfirm: {
                    title: '删除后不可恢复，是否确定删除？',
                    placement: 'left',
                    confirm: handleClick.bind(
                      null,
                      { labelId: record.labelId, parentLabelId: record.parentLabelId },
                      'del',
                    ),
                  },
                },
              ]"
            />
          </template>
        </template>
      </BasicTable>
    </div>
    <SldModal
      :width="width"
      :title="title"
      :visible="modalVisible"
      :content="addData"
      :confirmBtnLoading="confirmBtnLoading"
      :showFoot="true"
      @tree-select-change-event="handleTreeSelect"
      @cancle-event="handleCancle"
      @confirm-event="handleConfirm"
    />

    <SldMoreImgModal
      :width="1000"
      :title="'设置活动轮播图'"
      :modalTip="modal_tip"
      :content="modalContent"
      ref="sldMoreImgModalRef"
      @confirm="moreImgConfirm"
      mode="integral"
      client="mobile"
    ></SldMoreImgModal>
  </div>
</template>
<script>
  export default {
    name: 'PointLabel',
  };
</script>
<script setup>
  import { ref, onMounted,reactive } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import SldMoreImgModal from '@/components/SldMoreImgModal/index.vue';
  import { Tabs, Popconfirm, Switch, Popover } from 'ant-design-vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import {
    getIntegralLabelTreeApi,
    getIntegralLabelListApi,
    getIntegralIsShowApi,
    getIntegralAddApi,
    getIntegralUpdateApi,
    getIntegralLabelDelApi,
    getIntegralSetAdvApi,
  } from '/@/api/point/label';
  import SldModal from '@/components/SldModal/index.vue';
  import { failTip, list_com_page_more, sucTip } from '/@/utils/utils';
  import { validatorEmojiRules } from '/@/utils/validate';

  const dataSource = ref([]);
  const expandedRowKeys = ref([]);
  const parentLabelId = ref('');
  const modal_tip = [
    '最多上传8张图片,每张图片不可以超过1M',
    '请严格根据提示要求上传规定尺寸的广告图片',
    '编辑项中的“操作”指点击该内容所产生的链接地址，可通过下拉选项选择不同的方式',
  ];
  const modal_data = ref([
  {
      imgPath: '',
      imgUrl: '',
      info: {},
      link_type: '',
      link_value: '',
      title: '',
      bg_color:'#8439D3',
    }, {
      imgPath: '',
      imgUrl: '',
      info: {},
      link_type: '',
      link_value: '',
      title: '',
      bg_color:'#8439D3',
    }, {
      imgPath: '',
      imgUrl: '',
      info: {},
      link_type: '',
      link_value: '',
      title: '',
      bg_color:'#8439D3',
    }, {
      imgPath: '',
      imgUrl: '',
      info: {},
      link_type: '',
      link_value: '',
      title: '',
      bg_color:'#8439D3',
    }, {
      imgPath: '',
      imgUrl: '',
      info: {},
      link_type: '',
      link_value: '',
      title: '',
      bg_color:'#8439D3',
    }, {
      imgPath: '',
      imgUrl: '',
      info: {},
      link_type: '',
      link_value: '',
      title: '',
      bg_color:'#8439D3',
    }, {
      imgPath: '',
      imgUrl: '',
      info: {},
      link_type: '',
      link_value: '',
      title: '',
      bg_color:'#8439D3',
    }, {
      imgPath: '',
      imgUrl: '',
      info: {},
      link_type: '',
      link_value: '',
      title: '',
      bg_color:'#8439D3',
    }
  ])
  const modalContent = reactive(
    {
    width: 520,
    height: 210,
    show_width: 260,
    show_height: 105,
    data: [
  {
      imgPath: '',
      imgUrl: '',
      info: {},
      link_type: '',
      link_value: '',
      title: '',
      bg_color:'#8439D3',
    }, {
      imgPath: '',
      imgUrl: '',
      info: {},
      link_type: '',
      link_value: '',
      title: '',
      bg_color:'#8439D3',
    }, {
      imgPath: '',
      imgUrl: '',
      info: {},
      link_type: '',
      link_value: '',
      title: '',
      bg_color:'#8439D3',
    }, {
      imgPath: '',
      imgUrl: '',
      info: {},
      link_type: '',
      link_value: '',
      title: '',
      bg_color:'#8439D3',
    }, {
      imgPath: '',
      imgUrl: '',
      info: {},
      link_type: '',
      link_value: '',
      title: '',
      bg_color:'#8439D3',
    }, {
      imgPath: '',
      imgUrl: '',
      info: {},
      link_type: '',
      link_value: '',
      title: '',
      bg_color:'#8439D3',
    }, {
      imgPath: '',
      imgUrl: '',
      info: {},
      link_type: '',
      link_value: '',
      title: '',
      bg_color:'#8439D3',
    }, {
      imgPath: '',
      imgUrl: '',
      info: {},
      link_type: '',
      link_value: '',
      title: '',
      bg_color:'#8439D3',
    }
  ]
  })
  const operate_item = ref({});
  const [standardTable, { collapseAll, reload }] = useTable({
    isTreeTable: true,
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
    },
    columns: [
      {
        title: '标签名称',
        dataIndex: 'labelName',
        width: 150,
      },
      {
        title: '排序',
        dataIndex: 'sort',
        width: 100,
      },
      {
        title: '二级标签图',
        dataIndex: 'imageUrl',
        width: 100,
      },
      {
        title: '是否显示',
        dataIndex: 'state',
        width: 100,
      },
    ],
    actionColumn: {
      width: 160,
      title: '操作',
      dataIndex: 'action',
    },
    dataSource: dataSource,
    bordered: true,
    striped: false,
    rowKey: 'labelId',
    onExpand: (expande, record) => onExpandTable(expande, record),
    pagination: false,
  });
  const click_event = ref(false);
  const width = ref(550);
  const title = ref('');
  const type = ref('');
  const modalVisible = ref(false);
  const addData = ref([]);
  const curData = ref({});
  const content = ref([
    {
      type: 'input',
      label: `标签名称`,
      name: 'labelName',
      extra: '最多输入4个字',
      placeholder: `请输入标签名称`,
      maxLength: 4, //最多字数
      initialValue: '',
      showCount: false, //是否展示字数
      rules: [
        {
          whitespace: true,
          required: true,
          message: `请输入标签名称`, //请输入标签名称
        },
        validatorEmojiRules,
      ],
    },
    {
      type: 'inputnum',
      label: `排序`, //数字输入框
      name: 'sort',
      placeholder: `请输入排序`,
      extra: '请输入0~255的数字,值越小,显示越靠前',
      disable: false, //是否禁用
      min: 0, //最小值
      initialValue: '',
      max: 255, //最大值
      rules: [
        {
          required: true,
          message: `请输入排序`, //请输入标签名称
        },
      ],
    },
  ]);

  const label_data = ref({
    type: 'TreeSelect',
    label: `上级标签`, //类似 Select 的选择控件，可选择的数据结构是一个树形结构
    name: 'parentLabelId',
    placeholder: `请选择上级标签`,
    extra: '默认为最顶级',
    allowClear: true,
    initialValue: undefined,
    callback: true,
    fieldNames: {
      children: 'children',
      label: 'labelName',
      value: 'labelId',
    },
    data: [],
    treeNodeFilterProp: 'labelName', //输入项过滤对应的 treeNode 属性 title对应data里的title值
  });

  const upload_data = ref({
    type: 'upload_img_upload',
    label: '二级标签图片', //图片上传 一张
    name: 'image',
    initialValue: [], //控件里file-list 回显也用这个 数组格式
    upload_name: 'file', //upload_name
    upload_url: `v3/oss/admin/upload?source=setting`, //接口
    fileList: [], //暂时不用
    img_succ_info: {}, //暂时不用
    initialValue: [],
    extra: '建议上传160*160的图片',
    limit: 10, //文件最大
    star: true,
    accept: ' .jpg, .jpeg, .png', //文件格式
  });
  const confirmBtnLoading = ref(false);

  //获取数据列表
  const get_list = async (params, grade = '') => {
    let res = await getIntegralLabelListApi({ ...params, pageSize: list_com_page_more });
    if (res.state == 200) {
      //grade为1直接赋值
      if (grade != '') {
        for (let i in dataSource.value) {
          if (grade == 1) {
            if (dataSource.value[i].labelId == params.labelId) {
              dataSource.value[i].children = res.data.list;
              break;
            }
          } else {
            if (dataSource.value[i].children != undefined) {
              for (let j in dataSource.value[i].children) {
                if (dataSource.value[i].children[j].labelId == params.labelId) {
                  dataSource.value[i].children[j].children = res.data.list;
                  break;
                }
              }
            }
          }
        }
      } else {
        dataSource.value = res.data.list;
      }
    } else {
      failTip(res.msg);
    }
  };

  const get_tree_list = async (pid = 0, grade = 1) => {
    let res = await getIntegralLabelTreeApi({ parentLabelId: pid, grade: grade, isSort: true });
    if (res.state == 200) {
      label_data.value.data = res.data;
    }
  };

  const sldMoreImgModalRef = ref();
  const modalState = ref({})
  const addAdv = async(val)=> {
    modalState.value = val
    let adv_data = []
    if (val.data) {
      adv_data = JSON.parse(val.data.replace(/&quot;/g,"\""));
    }
    if(adv_data.length>0){
      modalContent.data = adv_data
    }else {
      modalContent.data = JSON.parse(JSON.stringify(modal_data.value))
    }
    sldMoreImgModalRef.value.setModal(true);
  }

  // 点击展开
  const onExpandTable = (expanded, record) => {
    if (expanded) {
      expandedRowKeys.value.push(record.labelId);
      get_list({ labelId: record.labelId }, record.grade);
    } else {
      expandedRowKeys.value = expandedRowKeys.value.filter((item) => item != record.labelId);
    }
  };

  //实现深拷贝，防止对象相互影响
  const getNewData = (type) => {
    let tar_data = {};
    if (type == 'image') {
      tar_data = JSON.parse(JSON.stringify(upload_data.value));
    } else if (type == 'parentLabelId') {
      tar_data = JSON.parse(JSON.stringify(label_data.value));
    }
    return tar_data;
  };

  // 打开新增标签弹窗
  const addLabel = () => {
    addData.value = [];
    addData.value = content.value.filter(
      (item) => item.name != 'parentLabelId' && item.name != 'image',
    );
    for (let i = 0; i < addData.value.length; i++) {
      if (addData.value[i].name == 'labelName') {
        addData.value.splice(i + 1, 0, getNewData('parentLabelId'));
        addData.value[i].initialValue = undefined;
      } else if(addData.value[i].name == 'parentLabelId'){
        addData.value[i].initialValue = undefined;
        addData.value[i].data.forEach(item=>{
          item.children = undefined
        })
      }else{
        addData.value[i].initialValue = undefined;
      }
    }
    parentLabelId.value = 0;
    title.value = '添加积分商城标签';
    type.value = 'add';
    modalVisible.value = true;
  };

  //编辑商品标签
  const editLabel = (val) => {
    addData.value = [];
    addData.value = content.value.filter(
      (item) => item.name != 'parentLabelId' && item.name != 'image',
    );
    for (let i = 0; i < addData.value.length; i++) {
      if (addData.value[i].name == 'sort') {
        addData.value[i].initialValue = val[addData.value[i].name];
        if (val.grade == 2) {
          addData.value.splice(i + 1, 0, getNewData('image'));
        }
      }
      if (addData.value[i].name == 'image') {
        //初始化图片数据
        let fileList = [];
        let tmp_data = {};
        tmp_data.uid = val.labelId;
        tmp_data.name = val.imageUrl;
        tmp_data.status = 'done';
        tmp_data.url = val.imageUrl;
        tmp_data.response = {
          data: {
            url: val.imageUrl,
            path: val.image,
          },
        };
        fileList.push(tmp_data);
        addData.value[i].initialValue = fileList;
      } else {
        addData.value[i].initialValue = val[addData.value[i].name];
      }
    }
    operate_item.value = val;
    parentLabelId.value = val.parentLabelId;
    type.value = 'edit';
    title.value = '编辑积分标签';
    modalVisible.value = true;
    curData.value = val;
  };

  //添加子标签功能
  const addNextLabel = (val) => {
    addData.value = [];
    addData.value = content.value.filter(
      (item) => item.name != 'parentLabelId' && item.name != 'image',
    );
    for (let i = 0; i < addData.value.length; i++) {
      if (addData.value[i].name == 'labelName') {
        addData.value.splice(i + 1, 0, getNewData('parentLabelId'));
        addData.value[i].initialValue = '';
      } else if (addData.value[i].name == 'parentLabelId') {
        addData.value[i].initialValue = val.labelId;
        addData.value[i].disabled = true;
      } else if (addData.value[i].name == 'sort') {
        addData.value.splice(i + 2, 0, getNewData('image'));
        addData.value[i].initialValue = '';
      }
    }
    operate_item.value = val;
    parentLabelId.value = val.labelId;
    title.value = '添加子标签';
    type.value = 'add';
    modalVisible.value = true;
  };

  // 关闭弹窗
  const handleCancle = () => {
    modalVisible.value = false;
  };

  // 弹窗点击确定
  const handleConfirm = async (val) => {
    let sld_params = {};
    sld_params.labelName = val.labelName;
    sld_params.sort = val.sort;
    sld_params.parentLabelId = val.parentLabelId ? val.parentLabelId : 0; //父分类id,一级分类==0
    if (type.value == 'edit') {
      sld_params.parentLabelId = parentLabelId.value ? parentLabelId.value : 0;
    }
    if (val.image) {
      if (
        val.image.length == 0 ||
        !val.image[0].response ||
        val.image[0].response.data.path == undefined
      ) {
        failTip('请上传二级标签图片');
        return;
      } else {
        sld_params.image = val.image[0].response.data.path;
        sld_params.imageUrl = val.image[0].response.data.url;
      }
    }
    handleClick(sld_params, type.value);
  };

  const handleTreeSelect = (val, label, extra, index, from_info) => {
    let info = from_info;
    if (label.length == 0) {
      info = info.filter((item) => item.name != 'image');
      parentLabelId.value = 0;
    } else {
      //如果已有图片上传控件，则替换掉
      if (info[info.length - 1].type !== 'upload_img_upload') {
         //如果没有图片上传控件，则新增
         info.splice(info.length, 0, getNewData('image'));
      }
      parentLabelId.value = extra.triggerValue;
    }
    addData.value = info;
  };

  //积分标签操作事件 type add:添加 edit:编辑 del:删除 show:是否显示 adv:设置广告
  const handleClick = async (record, type) => {
    let res;
    let params = {};
    if (type == 'show') {
      parentLabelId.value = record.parentLabelId > 0 ? record.parentLabelId : '';
      delete record.parentLabelId;
      params = record;
      res = await getIntegralIsShowApi(params);
    } else if (type == 'add') {
      confirmBtnLoading.value = true;
      params = record;
      res = await getIntegralAddApi(params);
    } else if (type == 'edit') {
      record.labelId = curData.value.labelId;
      confirmBtnLoading.value = true;
      params = record;
      res = await getIntegralUpdateApi(params);
    } else if (type == 'del') {
      parentLabelId.value = record.parentLabelId > 0 ? record.parentLabelId : '';
      delete record.parentLabelId;
      params = record;
      res = await getIntegralLabelDelApi(params);
    }
    if (res.state == 200) {
      sucTip(res.msg);
      if (type != 'add' && type != 'del') {
        let temp;
        if (parentLabelId.value > 0) {
          let parentTemp = dataSource.value.filter(
            (item) => item.labelId == parentLabelId.value,
          )[0];
          temp = parentTemp.children.filter((item) => item.labelId == params.labelId)[0];
        } else {
          temp = dataSource.value.filter((item) => item.labelId == params.labelId)[0];
        }
        for (var i in params) {
          temp[i] = params[i] != undefined ? params[i] : '';
        }
      } else {
        get_list({ labelId: 0 });
        get_tree_list(0, 2);
        collapseAll();
      }
      confirmBtnLoading.value = false;
      modalVisible.value = false;
    } else {
      confirmBtnLoading.value = false;
      failTip(res.msg);
    }
  };


  const moreImgConfirm = async (dataSource) => {
    const res = await getIntegralSetAdvApi({
      labelId: modalState.value.labelId,
      data: JSON.stringify(dataSource.parent_data),
    });

    if (res?.state == 200) {
      sucTip(res.msg);
      modalState.value = [];
      sldMoreImgModalRef.value.setModal(false);
      get_list({ labelId: 0 }); //grade为1表示获取一级数据
    } else {
      failTip(res.msg);
    }
  };

  onMounted(() => {
    get_list({ labelId: 0 }); //grade为1表示获取一级数据
    get_tree_list(0, 1);
  });
</script>
<style lang="less">
  .point_label_body {
    .business_load_img {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 50px;
      height: 50px;
      margin: auto;

      img {
        max-width: 100%;
        max-height: 100%;
      }
    }

    .ant-table-body {
      height: auto !important;
    }
  }
</style>
