<template>
  <div class="section_padding point_order_detail">
    <div class="section_padding_back">
      <SldComHeader title="订单详情" back />
      <div style="width: 100%; height: 10px"></div>
      <Spin :spinning="loading">
        <div class="height_detail">
          <div class="flex_row_center_start progress">
            <div
              class="flex_column_start_center item"
              v-for="(item, index) in return_progress_data"
              :key="index"
            >
              <div class="top flex_row_center_center">
                <span class="left_line" :style="{ borderColor: item.line_color }"></span>
                <img :src="item.icon" alt="" />
                <span class="right_line" :style="{ borderColor: item.line_color }"></span>
              </div>
              <span class="state" :style="{ color: item.state_color }">{{ item.state }}</span>
              <span class="time" :style="{ color: item.time_color }">{{ item.time }}</span>
            </div>
          </div>
          <div class="state_part flex_column_start_center" v-if="order_detail.orderState == 0">
            <span class="title">订单已取消</span>
            <span class="tip">取消原因:{{order_detail.refuseReason +(order_detail.refuseRemark ? ',' + order_detail.refuseRemark : '')}}</span>
          </div>
          <div class="state_part flex_column_start_center" v-if="order_detail.orderState == 10">
            <span class="title">订单已提交,等待买家付款</span>
          </div>
          <div
            class="state_part flex_column_start_center"
            v-if="order_detail.orderState == 20"
          >
            <span class="title">付款成功,等待卖家发货</span>
          </div>
          <div class="state_part flex_column_start_center" v-if="order_detail.orderState == 30">
            <span class="title">商品已发出,等待买家收货</span>
            <div class="btnsty" v-if="order_detail.isVirtualGoods == 1">
              <div class="cancle_btn"  @click="agreeReturn('flow')">查看物流</div>
            </div>
          </div>
          <div class="state_part flex_column_start_center" v-if="order_detail.orderState == 50">
            <span class="title">买家已确认收货,订单完成</span>
            <div class="btnsty" v-if="order_detail.isVirtualGoods == 1">
              <div class="cancle_btn"  @click="agreeReturn('flow')">查看物流</div>
            </div>
          </div>
          <!-- 发票信息 start -->
          <div class="sld_common_title">发票信息:</div>
          <Description
            :class="{ invoice_info_box: invoice_info.length == 1 }"
            size="middle"
            :useCollapse="true"
            :bordered="true"
            :column="2"
            :data="order_detail"
            :schema="invoice_info"
          />
          <!-- 发票信息 end -->
          <!-- 收货人信息 start -->
          <div class="sld_common_title" v-if="order_detail.isVirtualGoods == 1">收货人信息:</div>
          <Description
            v-if="order_detail.isVirtualGoods == 1"
            size="middle"
            :useCollapse="true"
            :bordered="true"
            :column="2"
            :data="order_detail"
            :schema="receiver_info"
          />
          <!-- 收货人信息 end -->
          <!-- 用户预留信息 start -->
          <div
            class="sld_common_title"
            v-if="order_detail.isVirtualGoods == 2 && order_detail.orderReserveList.length > 0"
            >用户预留信息</div
          >
          <Description
            v-if="order_detail.isVirtualGoods == 2 && order_detail.orderReserveList.length > 0"
            size="middle"
            :useCollapse="true"
            :bordered="true"
            :column="2"
            :data="order_detail"
            :schema="reserve_info"
          />
          <!-- 用户预留信息 end -->
          <!-- 订单信息 start -->
          <div class="sld_common_title">订单信息:</div>
          <Description
            size="middle"
            :useCollapse="true"
            :bordered="true"
            :column="2"
            :data="order_detail"
            :schema="order_info"
          />
          <!-- 订单信息 end -->
          <!-- 商品信息 start -->
          <div class="sld_common_title">商品信息</div>
          <BasicTable
            :columns="columns_order_goods"
            :bordered="true"
            :pagination="false"
            :dataSource="order_detail.orderProductList"
            :maxHeight="400"
            :canResize="false"
          >
            <template #bodyCell="{ column, record, text }">
              <template v-if="column.dataIndex == 'productImage'">
                <div class="goods_info com_flex_row_flex_start">
                  <div class="goods_img" style="border: none">
                    <Popover placement="rightTop">
                      <template #content>
                        <div style="width: 200px">
                          <img :src="text" alt="" style="max-width: 100%; max-height: 100%" />
                        </div>
                      </template>
                      <div class="business_load_img">
                        <img :src="text" alt="" />
                      </div>
                    </Popover>
                  </div>
                  <div class="com_flex_column_space_between goods_detail">
                    <div
                      class="goods_name"
                      style="width: 380px; margin-top: 6px; white-space: initial"
                      :title="record.goodsName"
                    >
                      {{ record.goodsName }}
                    </div>
                    <span class="goods_brief" :title="record.specValues">
                      {{ record.specValues }}
                    </span>
                  </div>
                </div>
              </template>
            </template>
          </BasicTable>
          <!-- 商品信息 end -->
        </div>
      </Spin>
    </div>
    <SldModal
      :width="modal_width"
      :title="titleName"
      :visible="modalVisible"
      :content="operateData"
      :showFoot="showFoot"
      :height="600"
      @cancle-event="sldHandleCancle"
    />
  </div>
</template>
<script>
  export default {
    name: 'PointOrderListToDetail',
  };
</script>
<script setup>
  import { ref, onMounted,h } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { Spin, Popover,Modal } from 'ant-design-vue';
  import { useRoute } from 'vue-router';
  import { BasicTable } from '/@/components/Table';
  import StandardTableRow from '/@/components/StandardTableRow/index.vue';
  import { Description } from '/@/components/Description/index';
  import { getIntegralBillOrderDetailApi,getOrderPointTraceApi } from '/@/api/point/order';
  import SldModal from '/@/components/SldModal/index.vue';
  import { failTip } from '/@/utils/utils';

  const route = useRoute();
  const loading = ref(true);
  const reserve_info = ref([]); //用户预留信息
  const order_detail = ref({});
  const return_progress_data = ref([]); //退货进度条
  const showFoot = ref(true);
  const invoice_info = ref([
    {
      field: 'invoiceTitle',
      label: '单位名称',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'taxCode',
      label: '税号',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'receiverEmail',
      label: '收票邮箱',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
  ]); //公司——普通发票
  const receiver_info = ref([
    {
      field: 'memberName',
      label: '会员名称',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'receiverName',
      label: '收货人',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'receiverMobile',
      label: '收货人手机号',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'receiverAddress',
      label: '收货地址',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
  ]);
  const order_info = ref([
    //订单信息
    {
      field: 'orderTypeValue',
      label: '订单类型',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'orderSn',
      label: '订单号',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'storeName',
      label: '店铺名称',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'paymentName',
      label: '支付方式',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'tradeSn',
      label: '支付流水号',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'orderRemark',
      label: '订单备注',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
  ]);
  const goodsInfoList = ref([]); //商品信息
  const orderLogList = ref([]);
  const operateData = ref([]); //弹框操作数据
  const resList = ref([]); // 取消原因数据
  const modalVisible = ref(false);
  const titleName = ref('');
  const submiting = ref(false);
  const show_foot = ref(true);
  const modal_width = ref(700);
  const propType = ref('');
  const deliverModal = ref(false);
  const expressList = ref([]); //快递公司数据
  const deliverType = ref(''); //发货方式
  const columns_order_goods = ref([
    {
      title: `商品信息`,
      dataIndex: 'productImage',
      align: 'center',
      width: 500,
    },
    {
      title: `单价(元)`,
      dataIndex: 'cashPrice',
      align: 'center',
      width: 100,
    },
    {
      title: `数量`,
      dataIndex: 'productNum',
      align: 'center',
      width: 100,
    },
  ]);
  const invoice_info_other = ref([
    //不需要发票的情况
    {
      field: 'invoiceStatus',
      label: '是否需要开票',
      labelMinWidth: 10,
      contentMinWidth: 100,
      render: (val, data) => {
        return '否';
      },
    },
  ]);
  const invoice_info_personal = ref([
    {
      field: 'invoiceTitle',
      label: '发票抬头',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'receiverEmail',
      label: '收票邮箱',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
  ]); //个人发票
  const invoice_info_VAT = ref([
    {
      field: 'invoiceTitle',
      label: '单位名称',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'taxCode',
      label: '税号',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'registerAddr',
      label: '注册地址',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'registerPhone',
      label: '注册电话',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'bankName',
      label: '开户银行',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'bankAccount',
      label: '银行账户',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'receiverName',
      label: '收票人',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'receiverMobile',
      label: '收票电话',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
    {
      field: 'receiverAddress',
      label: '收票地址',
      labelMinWidth: 10,
      contentMinWidth: 100,
    },
  ]); //公司发票——增值税发票

  const get_order_detail = async (params) => {
    loading.value = true;
    let res = await getIntegralBillOrderDetailApi(params);
    if (res.state == 200) {
      loading.value = false;
      order_detail.value = res.data;
      orderLogList.value = res.data.orderLogList;
      //收票信息
      if (order_detail.value.invoiceStatus == 1) {
        let invoice_type = '';
        if (order_detail.value.invoiceInfo.titleType == 1) {
          //个人发票
          invoice_info.value = invoice_info_personal.value;
          invoice_type = `个人发票`;
        } else {
          //公司发票
          if (order_detail.value.invoiceInfo.invoiceType != 1) {
            //增值税发票
            invoice_info.value = invoice_info_VAT.value;
            invoice_type = `增值税专用发票`;
          } else {
            invoice_type = `普通发票`;
          }
        }

        //需要发票
        for (let item in invoice_info.value) {
          let name = invoice_info.value[item].field;
          invoice_info.value[item].render = (val, data) => {
            return !order_detail.value['invoiceInfo'][name]
              ? '--'
              : order_detail.value['invoiceInfo'][name];
          };
        }
        let invoice_content =
          order_detail.value.invoiceInfo.invoiceContent == 1 ? `商品明细` : `商品类别`;
        invoice_info.value = [
          {
            field: 'invoiceTypeCombine',
            label: '发票类型',
            labelMinWidth: 10,
            contentMinWidth: 100,
            render: (val, data) => {
              return invoice_type;
            },
          },
          {
            field: 'invoiceContent',
            label: '发票内容',
            labelMinWidth: 10,
            contentMinWidth: 100,
            render: (val, data) => {
              return invoice_content;
            },
          },
          ...invoice_info.value,
        ];
      } else {
        //不需要发票
        invoice_info.value = invoice_info_other.value;
      }
      //收货人信息
      for (let item in receiver_info.value) {
        if (receiver_info.value[item].field == 'receiverAddress') {
          receiver_info.value[item].render = (val, data) => {
            return order_detail.value.receiverAddress;
          };
        } else {
          receiver_info.value[item].render = (val, data) => {
            return order_detail.value[receiver_info.value[item].field];
          };
        }
      }
      //订单信息
      for (let item in order_info.value) {
        if (order_info.value[item].field == 'orderTypeValue') {
          order_info.value[item].render = (val, data) => {
            return order_detail.value.isVirtualGoods == 2 ? '虚拟订单' : '普通订单';
          };
        } else {
          order_info.value[item].render = (val, data) => {
            return order_detail.value[order_info.value[item].field] == ''|| (order_detail.value[order_info.value[item].field] == undefined || order_detail.value[order_info.value[item].field] == null)
              ? '--'
              : order_detail.value[order_info.value[item].field];
          };
        }
      }
      //用户预留信息
      if (
        order_detail.value.isVirtualGoods == 2 &&
        order_detail.value.orderReserveList.length != undefined &&
        order_detail.value.orderReserveList.length
      ) {
        reserve_info.value = [];
        order_detail.value.orderReserveList.map((item) => {
          reserve_info.value.push({
            field: item.reserveId,
            label: item.reserveName,
            labelMinWidth: 10,
            contentMinWidth: 100,
            render: (val, data) => {
              return item.reserveValue;
            },
          });
        });
      }
      return_progress_data.value = [];
      if (order_detail.value.orderState == 0) {
        // 订单取消
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/submit_pass.png', import.meta.url).href,
          state: `提交订单`,
          time:
            orderLogList.value.length > 0 && orderLogList.value[0].logTime != undefined
              ? orderLogList.value[0].logTime
              : '',
          state_color: 'rgba(255, 113, 30, .6)',
          time_color: 'rgba(255, 113, 30, .3)',
          line_color: 'rgba(255, 113, 30, .3)',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/fail_current.png', import.meta.url).href,
          state: `订单取消`,
          time:
            orderLogList.value.length > 1 && orderLogList.value[1].logTime != undefined
              ? orderLogList.value[1].logTime
              : '',
          state_color: 'rgba(255, 113, 30,1)',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: 'rgba(255, 113, 30,1)',
        });
      } else if (order_detail.value.orderState == 10) {
        //未付款订单
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/submit_current.png', import.meta.url).href,
          state: `提交订单`,
          time:
            orderLogList.value.length > 0 && orderLogList.value[0].logTime != undefined
              ? orderLogList.value[0].logTime
              : '',
          state_color: 'rgba(255, 113, 30,1)',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: 'rgba(255, 113, 30,1)',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/pay_future.png', import.meta.url).href,
          state: `付款成功`,
          time: '',
          state_color: '#999999',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: '#eee',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/deliver_future.png', import.meta.url).href,
          state: `商品发货`,
          time: '',
          state_color: '#999999',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: '#eee',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/suc_future.png', import.meta.url).href,
          state: `订单完成`,
          time: '',
          state_color: '#999999',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: '#222',
        });
      } else if (order_detail.value.orderState == 20) {
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/submit_pass.png', import.meta.url).href,
          state: `提交订单`,
          time:
            orderLogList.value.length > 0 && orderLogList.value[0].logTime != undefined
              ? orderLogList.value[0].logTime
              : '',
          state_color: 'rgba(255, 113, 30, .6)',
          time_color: 'rgba(255, 113, 30, .3)',
          line_color: 'rgba(255, 113, 30, .3)',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/pay_current.png', import.meta.url).href,
          state: `付款成功`,
          time:
            orderLogList.value.length > 1 && orderLogList.value[1].logTime != undefined
              ? orderLogList.value[1].logTime
              : '',
          state_color: 'rgba(255, 113, 30,1)',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: 'rgba(255, 113, 30,1)',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/deliver_future.png', import.meta.url).href,
          state: `商品发货`,
          time: '',
          state_color: '#999999',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: '#eee',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/suc_future.png', import.meta.url).href,
          state: `订单完成`,
          time: '',
          state_color: '#999999',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: '#222',
        });
      } else if (order_detail.value.orderState == 30) {
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/submit_pass.png', import.meta.url).href,
          state: `提交订单`,
          time:
            orderLogList.value.length > 0 && orderLogList.value[0].logTime != undefined
              ? orderLogList.value[0].logTime
              : '',
          state_color: 'rgba(255, 113, 30, .6)',
          time_color: 'rgba(255, 113, 30, .3)',
          line_color: 'rgba(255, 113, 30, .3)',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/pay_pass.png', import.meta.url).href,
          state: `付款成功`,
          time:
            orderLogList.value.length > 1 && orderLogList.value[1].logTime != undefined
              ? orderLogList.value[1].logTime
              : '',
          state_color: 'rgba(255, 113, 30, .6)',
          time_color: 'rgba(255, 113, 30, .3)',
          line_color: 'rgba(255, 113, 30, .3)',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/deliver_current.png', import.meta.url).href,
          state: `商品发货`,
          time:
            orderLogList.value.length > 2 && orderLogList.value[2].logTime != undefined
              ? orderLogList.value[2].logTime
              : '',
          state_color: 'rgba(255, 113, 30,1)',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: 'rgba(255, 113, 30,1)',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/suc_pass.png', import.meta.url).href,
          state: `订单完成`,
          time: '',
          state_color: '#999999',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: '#222',
        });
      } else if (order_detail.value.orderState == 40) {
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/submit_pass.png', import.meta.url).href,
          state: `提交订单`,
          time:
            orderLogList.value.length > 0 && orderLogList.value[0].logTime != undefined
              ? orderLogList.value[0].logTime
              : '',
          state_color: 'rgba(255, 113, 30, .6)',
          time_color: 'rgba(255, 113, 30, .3)',
          line_color: 'rgba(255, 113, 30, .3)',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/pay_pass.png', import.meta.url).href,
          state: `付款成功`,
          time:
            orderLogList.value.length > 0 && orderLogList.value[1].logTime != undefined
              ? orderLogList.value[1].logTime
              : '',
          state_color: 'rgba(255, 113, 30, .6)',
          time_color: 'rgba(255, 113, 30, .3)',
          line_color: 'rgba(255, 113, 30, .3)',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/deliver_pass.png', import.meta.url).href,
          state: `商品发货`,
          time:
            orderLogList.value.length > 1 && orderLogList.value[2].logTime != undefined
              ? orderLogList.value[2].logTime
              : '',
          state_color: 'rgba(255, 113, 30, .6)',
          time_color: 'rgba(255, 113, 30, .3)',
          line_color: 'rgba(255, 113, 30, .3)',
        });
        return_progress_data.value.push({
          icon: new URL('/@/assets/images/order/suc_current.png', import.meta.url).href,
          state: `订单完成`,
          time:
            orderLogList.value.length > 2 && orderLogList.value[3].logTime != undefined
              ? orderLogList.value[3].logTime
              : '',
          state_color: 'rgba(255, 113, 30,1)',
          time_color: 'rgba(255, 113, 30, .5)',
          line_color: 'rgba(255, 113, 30,1)',
        });
      }
    } else {
      failTip(res.msg);
    }
  };


   // 发货按钮打开弹窗
   const agreeReturn = async(type)=> {
    propType.value = type
    if(type == 'flow'){
      if(order_detail.value.deliverType==2){
        Modal.info({
          width:470,
          title: '该订单是自行配送，您可以联系配送人了解具体进度',
          content: h('div', {}, [
            h('p', `${'配送人姓名：'}${order_detail.value.deliverName}`),
            h('p', `${'配送人手机号：'}${order_detail.value.deliverMobile}`),
          ]),
          onOk() {
          },
        });
      }else{
        let res = await getOrderPointTraceApi({ orderSn: route.query.orderSn })
        if(res.state == 200){
          operateData.value.push({
            type: 'show_express', //物流进度
            content: res.data,
          })
          showFoot.value = false
          modal_width.value = 550
          titleName.value = '物流信息'
          modalVisible.value = true
        }
      }
    }
  }

     //弹框取消操作
     const sldHandleCancle = () => {
    modalVisible.value = false
    operateData.value = []
  };

  // 生成本地路径
  const newImg = (img) => {
    return new URL(img, import.meta.url).href;
  };

  onMounted(() => {
    get_order_detail({ orderSn: route.query.orderSn });
  });
</script>
<style lang="less">
  @import '@/assets/css/order.less';

  .business_load_img {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    margin: auto;

    img {
      max-width: 100%;
      max-height: 100%;
    }
  }
</style>
