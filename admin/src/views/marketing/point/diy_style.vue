<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader title="风格配置" />
      <Tabs type="card" v-model:activeKey="activeKey">
        <!-- dev_mobile-start -->
        <TabPane key="1" tab="移动端风格">
          <PointDiyStyleM></PointDiyStyleM>
        </TabPane>
        <!-- dev_mobile-end -->
        <!-- dev_pc-start -->
        <TabPane key="2" tab="PC端风格">
          <PointDiyStylePc></PointDiyStylePc>
        </TabPane>
        <!-- dev_pc-end -->
      </Tabs>
    </div>
  </div>
</template>
<script lang="ts">
  export default {
    name: 'PointDiyStyle',
  };
</script>
<script lang="ts" setup>
  import { TabPane, Tabs } from 'ant-design-vue';
import { onMounted, ref } from 'vue';
import SldComHeader from '/@/components/SldComHeader/index.vue';
  // dev_mobile-start
  import PointDiyStyleM from './diy_style_m.vue';
  // dev_mobile-end
  // dev_pc-start
  import PointDiyStylePc from './diy_style_pc.vue';
  // dev_pc-end

  const activeKey = ref('1');

  onMounted(() => {
    // dev_mobile-start
    activeKey.value='1'
    // dev_mobile-end
    // dev_pc-start
    // activeKey.value='2'
    // dev_pc-end
    return;
  });
</script>
<style lang="less">
  .RadioGroup_back {
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within {
      box-shadow: none;
    }
  }
</style>
