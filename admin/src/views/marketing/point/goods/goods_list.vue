<template>
  <div class="section_padding point_goods_list">
    <div class="section_padding_back" style="height: calc(100vh - 48px - 20px)">
      <SldComHeader
        type="3"
        :clickFlag="true"
        title="商品管理"
        @handle-toggle-tip="handleToggleTip"
      />
      <Tabs type="card" v-model:activeKey="activeKey" class="tabs_nav">
        <TabPane key="1" tab="在售列表">
          <ShowMoreHelpTip :tipData="goodsTip" v-if="sld_show_tip"></ShowMoreHelpTip>
          <GoodsOnlineLists style="padding-top: 10px" :canResize="canResize"></GoodsOnlineLists>
        </TabPane>
        <TabPane key="2" tab="待审核商品">
          <ShowMoreHelpTip :tipData="checkGoodsTip" v-if="sld_show_tip"></ShowMoreHelpTip>
          <GoodsCheckLists style="padding-top: 10px" :canResize="canResize"></GoodsCheckLists>
        </TabPane>
        <TabPane key="3" tab="仓库中商品">
          <GoodsStorageLists style="padding-top: 10px"></GoodsStorageLists>
        </TabPane>
        <TabPane key="4" tab="违规下架商品">
          <GoodsOfflineLists style="padding-top: 10px"></GoodsOfflineLists>
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'PointGoodsList',
  };
</script>
<script setup>
  import { ref, onMounted } from 'vue';
  import { Tabs, TabPane } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import ShowMoreHelpTip from '/@/components/ShowMoreHelpTip/index.vue';
  import GoodsOnlineLists from './goods_online_lists.vue';
  import GoodsCheckLists from './goods_check_lists.vue';
  import GoodsStorageLists from './goods_storage_lists.vue';
  import GoodsOfflineLists from './goods_offline_lists.vue';

  const activeKey = ref('1');

  const canResize = ref(true);

  const goodsTip = ref([
    '商品只有在审核通过才能正常出售。商品是否需审核可以在“商品设置”中配置。',
    '违规下架或审核失败的商品，商家只能重新编辑后才能够进行出售。',
  ]);

  const checkGoodsTip = ref([
    '在“商品设置”中，开启商品审核后，商家发布、编辑商品需要管理员审核才能正常销售。',
    '审核状态分为：审核通过、等待审核和审核失败三种状态，审核失败后请详细填写审核失败原因方便商家修改。',
  ]);

  const sld_show_tip = ref(true);

  const handleToggleTip = (type) => {
    sld_show_tip.value = type;
    canResize.value = type;
  };

  onMounted(() => {});
</script>
<style lang="less">
  .point_goods_list {
    .tabs_nav {
      .ant-tabs-nav {
        margin-bottom: 0;
      }
    }
  }
</style>
