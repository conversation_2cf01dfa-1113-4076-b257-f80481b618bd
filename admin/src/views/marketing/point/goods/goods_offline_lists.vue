<template>
  <div class="goods_online_lists">
    <BasicTable @register="registerTable" style="padding: 0">
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.key == 'mainImage'">
          <div class="goods_list_mainIamge">
            <Popover placement="right">
              <template #content>
                <div class="goods_list_mainIamge_pop">
                  <img :src="text" />
                </div>
              </template>
              <div
                class="goods_list_leftImage"
                :style="{ backgroundImage: `url('${text}')` }"
              ></div>
            </Popover>
            <div class="goods_list_online_rightInfo">
              <div class="goods_list_goodsname">{{ record.goodsName }}</div>
              <div class="goods_list_extraninfo">
                <div v-if="record.goodsBrief">{{ record.goodsBrief }}</div>
                <div v-if="record.storeName">所属店铺：{{ record.storeName }}</div>
              </div>
            </div>
          </div>
        </template>
        <template v-else-if="column.key">
          {{ text !== undefined && text !== null ? text : '--' }}
        </template>
      </template>
    </BasicTable>
  </div>
</template>
<script setup>
  import { ref, onMounted } from 'vue';
  import { Popover } from 'ant-design-vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import { getIntegralGoodsListApi } from '/@/api/point/goods';

  const search_form_schema = ref([
    {
      field: 'goodsName',
      component: 'Input',
      colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
      componentProps: {
        minWidth: 300,
        placeholder: '请输入商品名称',
        size: 'default',
      },
      label: '商品名称',
      labelWidth: 80,
    },
    {
      field: 'storeName',
      component: 'Input',
      colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
      componentProps: {
        minWidth: 300,
        placeholder: '请输入店铺名称',
        size: 'default',
      },
      label: '店铺名称',
      labelWidth: 80,
    },
    {
      field: 'isVirtualGoods',
      component: 'Select',
      colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
      componentProps: {
        placeHolder: '请选择商品类型',
        minWidth: 300,
        size: 'default',
        options: [
          { label: '全部', value: '' },
          { label: '实物商品', value: 1 },
          { label: '虚拟商品', value: 2 },
        ],
      },
      label: '商品类型',
      labelWidth: 80,
    },
    {
      field: '[startTime,endTime]',
      component: 'RangePicker',
      colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
      componentProps: {
        format: 'YYYY-MM-DD',
        placeholder: ['开始时间', '结束时间'],
      },
      label: `发布时间`,
      labelWidth: 80,
    },
  ]);

  const columns = ref([
    {
      title: '商品信息',
      dataIndex: 'mainImage',
      width: 250,
    },
    {
      title: '商品价格',
      dataIndex: 'cashPrice',
      width: 100,
      customRender: ({ text, record }) => {
        return `${record.integralPrice}${'积分'}${text ? `${' + ¥'}` + text : ''}`;
      },
    },
    {
      title: '商品库存',
      dataIndex: 'goodsStock',
      width: 100,
    },
    {
      title: '销量',
      dataIndex: 'actualSales',
      width: 100,
    },
    {
      title: '违规原因',
      dataIndex: 'offlineReason',
      width: 100,
      customRender: ({ text, record }) => {
        return (text ? text : '') + (record.offlineComment ? ',' + record.offlineComment : '');
      },
    },
    {
      title: '发布时间',
      dataIndex: 'createTime',
      width: 120,
    },
  ]);

  const [registerTable] = useTable({
    rowKey: 'integralGoodsId',
    api: (arg) => getIntegralGoodsListApi({ ...arg, state: 6 }),
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    columns: columns,
    useSearchForm: true,
    formConfig: {
      schemas: search_form_schema,
    },
    beforeFetch(values) {
      values.startTime = values.startTime
        ? values.startTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
      return values;
    },
    bordered: true,
    striped: false,
  });

  onMounted(() => {});
</script>
<style lang="less">
  .goods_online_lists {
    .goods_list_online_rightInfo {
      display: flex;
      flex: 1;
      flex-direction: column;
      align-items: flex-start;
      justify-content: space-between;
      margin-left: 10px;
      padding: 1px 0;

      .goods_list_goodsname {
        display: -webkit-box;
        flex-shrink: 0;
        height: 34px;
        overflow: hidden;
        color: #333;
        font-size: 14px;
        line-height: 17px;
        text-align: left;
        text-overflow: ellipsis;
        word-break: break-word;
        white-space: normal;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .goods_list_extraninfo {
        color: #666;
        font-size: 12px;
        text-align: left;

        div {
          display: -webkit-box;
          flex-shrink: 0;
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: break-word;
          white-space: normal;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }
      }
    }
  }
</style>
