<template>
  <div class="section_padding goods_bill_lists">
    <div class="section_padding_back">
      <SldComHeader
        type="2"
        :clickFlag="true"
        title="结算账单管理"
        tipTitle="温馨提示"
        :tipData="[
          '计算公式：结算金额 = 现金使用金额 + 积分抵扣金额',
          ' 结算流程：生成结算单 > 店铺确认 > 平台审核 > 结算完成',
        ]"
        @handle-toggle-tip="handleToggleTip"
      />
      <BasicTable @register="registerTable" style="padding: 0">
        <template #tableTitle>
          <div class="toolbar">
            <div class="toolbar_btn" @click="handleSldExcel">
              <AliSvgIcon
                iconName="iconziyuan23"
                width="15px"
                height="15px"
                fillColor="rgb(255, 89, 8)"
              />
              <span>结算单导出</span>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex == 'startTime'">
            <div>
              <div>{{ text }}</div>
              <div>~</div>
              <div>{{ record.endTime }}</div>
            </div>
          </template>
          <template v-if="column.dataIndex == 'action'">
            <TableAction
              :actions="[
                {
                  onClick: lookDetail.bind(null, record),
                  label: '查看',
                },
              ]"
            />
          </template>
        </template>
      </BasicTable>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'PointBillList',
  };
</script>
<script setup>
  import { ref, onMounted } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import {} from 'ant-design-vue';
  import { useRouter } from 'vue-router';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { getIntegralBillListApi, getIntegralBillExportApi } from '/@/api/point/bill';
  import { selectRadio, SelectAll } from '/@/utils/utils';

  const search_form_schema = ref([
    {
      field: 'storeName',
      component: 'Input',
      colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
      componentProps: {
        minWidth: 300,
        placeholder: '请输入店铺名称',
        size: 'default',
      },
      label: '店铺名称',
      labelWidth: 80,
    },
    {
      field: 'billSn',
      component: 'Input',
      colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
      componentProps: {
        minWidth: 300,
        placeholder: '请输入结算单号',
        size: 'default',
      },
      label: '结算单号',
      labelWidth: 80,
    },
    {
      field: 'state',
      component: 'Select',
      colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
      componentProps: {
        placeHolder: '请选择结算状态',
        minWidth: 300,
        size: 'default',
        options: [
          { label: '全部', value: '' },
          { label: '待确认', value: 1 },
          { label: '待审核', value: 2 },
          { label: '待结算', value: 3 },
          { label: '结算完成', value: 4 },
        ],
      },
      label: '结算状态',
      labelWidth: 80,
    },
    {
      field: '[startTime,endTime]',
      component: 'RangePicker',
      colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
      componentProps: {
        format: 'YYYY-MM-DD',
        placeholder: ['开始时间', '结束时间'],
      },
      label: `结算时间`,
      labelWidth: 80,
    },
  ]);

  const selectedRowKeys = ref([]);
  const selectedRows = ref([]);
  const router = useRouter();

  const columns = ref([
    {
      title: '店铺名称',
      dataIndex: 'storeName',
      width: 100,
    },
    {
      title: '结算单号',
      dataIndex: 'billSn',
      width: 100,
      customRender: ({ text, record }) => {
        return text ? text : '--';
      },
    },
    {
      title: '出账时间',
      dataIndex: 'createTime',
      width: 130,
    },
    {
      title: '结算日',
      dataIndex: 'startTime',
      width: 150,
    },
    {
      title: '使用积分',
      dataIndex: 'integral',
      width: 150,
    },
    {
      title: '积分抵扣金额(元)',
      dataIndex: 'integralCashAmount',
      width: 120,
    },
    {
      title: '支付现金(元)',
      dataIndex: 'cashAmount',
      width: 120,
    },
    {
      title: '应结金额(元)',
      dataIndex: 'settleAmount',
      width: 100,
    },
    {
      title: '结算状态',
      dataIndex: 'stateValue',
      width: 100,
    },
  ]);

  const [registerTable, { redoHeight, getForm }] = useTable({
    rowKey: 'billSn',
    ellipsis: false,
    clickToRowSelect:false,
    api: (arg) => getIntegralBillListApi({ ...arg }),
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    actionColumn: {
      width: 80,
      title: '操作',
      dataIndex: 'action',
    },
    columns: columns,
    useSearchForm: true,
    formConfig: {
      schemas: search_form_schema,
    },
    rowSelection: {
      type: 'checkbox',
      selectedRowKeys: selectedRowKeys,
      onSelect: onSelect,
      onSelectAll: onSelectAll,
    },
    beforeFetch(values) {
      values.startTime = values.startTime
        ? values.startTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
      return values;
    },
    bordered: true,
    striped: false,
  });

  // 单选多选事件
  function onSelect(record, selected) {
    let rows = selectRadio(selectedRowKeys.value, selectedRows.value, 'billSn', record, selected);
    selectedRowKeys.value = rows.selectedRowKeys;
    selectedRows.value = rows.selectedRows;
  }

  // 全选按钮事件
  function onSelectAll(selected, rows, changeRows) {
    let rowAll = SelectAll(
      selectedRowKeys.value,
      selectedRows.value,
      'billSn',
      selected,
      rows,
      changeRows,
    );
    selectedRowKeys.value = rowAll.selectedRowKeys;
    selectedRows.value = rowAll.selectedRows;
  }

  // 更新表格高度
  const handleToggleTip = () => {
    redoHeight();
  };

  // 结算单导出
  const handleSldExcel = async () => {
    let paramData = {
      ...getForm().getFieldsValue(),
    };
    for (let i in paramData) {
      if (paramData[i] == undefined) {
        delete paramData[i];
      } else if (i == 'startTime') {
        paramData.startTime = paramData.startTime.split(' ')[0] + ' 00:00:00';
        paramData.endTime = paramData.endTime.split(' ')[0] + ' 23:59:59';
      }
    }
    if (selectedRowKeys.value.length > 0) {
      paramData.billSns = selectedRowKeys.value.join(',');
    }
    paramData.fileName = `结算单导出`;
    let res = await getIntegralBillExportApi(paramData);
  };

  const lookDetail = (record) => {
    router.push({
      path: '/marketing_point/bill_list_to_detail',
      query: {
        id: record.billId,
      },
    });
  };
  onMounted(() => {});
</script>
<style lang="less">
  .goods_bill_lists {
    .goods_list_online_rightInfo {
      display: flex;
      flex: 1;
      flex-direction: column;
      align-items: flex-start;
      justify-content: space-between;
      margin-left: 10px;
      padding: 1px 0;

      .goods_list_goodsname {
        display: -webkit-box;
        flex-shrink: 0;
        height: 34px;
        overflow: hidden;
        color: #333;
        font-size: 14px;
        line-height: 17px;
        text-align: left;
        text-overflow: ellipsis;
        word-break: break-word;
        white-space: normal;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .goods_list_extraninfo {
        color: #666;
        font-size: 12px;
        text-align: left;

        div {
          display: -webkit-box;
          flex-shrink: 0;
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: break-word;
          white-space: normal;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }
      }
    }
  }
</style>
