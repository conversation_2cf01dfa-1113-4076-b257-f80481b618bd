.point_diy_style {
  height: calc(100vh - 48px - 20px - 20px - 32px - 15px - 48px - 32px);
  padding: 10px 0;
  overflow: auto;

  .diy_style_list {
    width: 1240px;

    .diy_style_item {
      position: relative;
      width: 122px;
      height: 48px;
      margin-right: 10px;
      margin-bottom: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      cursor: pointer;

      &.active {
        border-color: #fc701e;
      }

      .diy_style_color {
        width: 46px;
        height: 20px;
        overflow: hidden;

        .diy_style_color_left {
          flex: 1;
          height: 20px;
        }

        .diy_style_color_middle {
          flex: 1;
          height: 20px;
          margin-right: 2px;
          margin-left: 2px;
        }

        .diy_style_color_right {
          flex: 1;
          height: 20px;
        }
      }

      .diy_style_name {
        margin-left: 8px;
        color: #666;
        font-size: 12px;
        text-align: center;
      }

      .diy_style_auto_name {
        color: #fc701e;
        font-size: 12px;
      }

      .diy_style_auto_arrow {
        flex-shrink: 0;
        width: 10px;
        height: 22px;
        margin-left: 5px;
        color: #fc701e;
        font-family: cursive;
        font-size: 14px;
        font-weight: 600;
      }

      .diy_style_checked {
        position: absolute;
        z-index: 1;
        right: -1px;
        bottom: -1px;
        width: 20px;
        height: 20px;
      }
    }
  }

  .prew_tips {
    margin-bottom: 10px;
    color: #999;
    font-family: 'PingFang SC';
    font-size: 12px;
    font-weight: 500;
  }

  .prew_title {
    margin-bottom: 10px;
    color: #222;
    font-family: 'PingFang SC';
    font-size: 15px;
    font-weight: bold;
    line-height: 40px;
  }

  .prew_list {
    width: 1240px;
    padding-left: 15px;

    .prew_item {
      position: relative;
      width: 375px;
      height: 666px;
      margin-right: 50px;
      overflow: hidden;
      border-radius: 10px;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      box-shadow: 0 0 12px 0 rgb(129 129 129 / 30%);
      cursor: default;

      &:last-of-type {
        margin-right: 0;
      }

      &.prew_item_list {
        .diy_style_price {
          position: absolute;
          z-index: 2;
          height: 18px;
          padding: 2px 5px;
          border-radius: 9px;
          color: #fff;
          font-family: 'PingFang SC';
          font-size: 12px;
          font-weight: 500;
          line-height: 14px;

          &:nth-child(1) {
            top: 318px;
            left: 17px;
          }

          &:nth-child(2) {
            top: 318px;
            left: 203px;
          }

          &:nth-child(3) {
            bottom: 72px;
            left: 17px;
          }

          &:nth-child(4) {
            bottom: 72px;
            left: 203px;
          }
        }
      }

      &.prew_item_goods {
        .diy_style_top {
          position: absolute;
          z-index: 2;
          top: 449px;
          left: 0;
          width: 100%;
          height: 64px;
          padding: 0 12px;
          color: #fff;
          line-height: 20px;

          .diy_style_price {
            font-family: 'PingFang SC';
            font-size: 17px;
            font-weight: 500;

            div {
              &:nth-child(2) {
                align-items: baseline;
                font-family: 'PingFang SC';
                font-size: 13px;

                span {
                  &:nth-child(1) {
                    font-size: 15px;
                  }

                  &:nth-child(2) {
                    font-size: 20px;
                  }
                }
              }
            }

            .diy_style_price_bg {
              position: absolute;
              z-index: 1;
              top: 0;
              right: 0;
              height: 100%;
            }
          }

          .diy_style_price_origin {
            width: 100%;
            margin-top: 3px;

            span {
              &:nth-child(2) {
                font-family: 'PingFang SC';
                font-size: 13px;
              }
            }
          }
        }

        .diy_style_buy {
          position: absolute;
          z-index: 2;
          bottom: 5px;
          left: 17px;
          width: 344px;
          height: 38px;
          border-radius: 20px;
          color: #fff;
          font-family: 'PingFang SC';
          font-size: 15px;
          font-weight: 500;
          line-height: 38px;
          text-align: center;
        }
      }

      &.prew_item_confirm {
        .diy_style_top_price {
          position: absolute;
          z-index: 2;
          top: 330px;
          left: 107px;
          font-family: 'PingFang SC';
          font-size: 15px;
          font-weight: 500;

          div {
            &:nth-child(2) {
              align-items: baseline;
              font-size: 13px;

              span {
                &:nth-child(1) {
                  font-size: 14px;
                }

                &:nth-child(2) {
                  font-size: 17px;
                }
              }
            }
          }
        }

        .diy_style_type_list {
          position: absolute;
          z-index: 2;
          top: 454px;
          left: 14px;

          .diy_style_type_item {
            position: relative;
            z-index: 3;
            width: 65px;
            height: 32px;
            margin-right: 10px;
            transform: scale(0.95);
            border: 1px solid #f6f6f6;
            border-radius: 16px;
            background: #f6f6f6;
            color: #333;
            font-family: 'PingFang SC';
            font-size: 14px;
            font-weight: 500;
            line-height: 30px;
            text-align: center;
          }
        }

        .diy_style_buy {
          position: absolute;
          z-index: 2;
          bottom: 5px;
          left: 17px;
          width: 344px;
          height: 38px;
          border-radius: 20px;
          color: #fff;
          font-family: 'PingFang SC';
          font-size: 15px;
          font-weight: 500;
          line-height: 38px;
          text-align: center;
        }
      }

      &.prew_item_cancle {
        .mask {
          position: absolute;
          z-index: 2;
          inset: 0;
          width: 100%;
          height: 100%;
          background-color: rgb(0 0 0 / 40%);
        }

        .top_nav {
          position: absolute;
          z-index: 1;
          top: 58px;
          left: 38px;
          font-family: 'PingFang SC';
          font-size: 15px;
          font-weight: 800;

          span {
            position: absolute;
            z-index: 1;
            top: 25px;
            left: 7px;
            width: 17px;
            height: 4px;
            border-radius: 2px;
          }
        }

        .order_state {
          position: absolute;
          z-index: 1;
          top: 112px;
          right: 17px;
          font-family: 'PingFang SC';
          font-size: 13px;
          font-weight: 500;
        }

        .goods_price {
          position: absolute;
          z-index: 1;
          top: 228px;
          left: 121px;
          font-family: 'PingFang SC';
          font-size: 15px;
          font-weight: 500;
        }

        .order_price {
          position: absolute;
          z-index: 1;
          top: 277px;
          right: 11px;
          color: #333;
          font-family: 'PingFang SC';
          font-size: 12px;
          font-weight: 500;

          span {
            margin-right: 2px;
          }
        }

        .order_btn {
          position: absolute;
          z-index: 1;
          top: 303px;
          right: 8px;

          div {
            width: 63px;
            height: 26px;
            border: 1px solid;
            border-radius: 13px;
            font-family: 'PingFang SC';
            font-size: 12px;
            font-weight: 500;
            line-height: 26px;
            text-align: center;

            &:nth-child(1) {
              margin-right: 7px;
              border-color: #dcdcdc;
            }

            &:nth-child(2) {
              color: #fff;
            }
          }
        }

        .pop {
          position: absolute;
          z-index: 5;
          right: 0;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 349px;
          background-repeat: no-repeat;
          background-position: center;
          background-size: cover;

          svg {
            position: absolute;
            z-index: 5;
            top: 63px;
            right: 24px;
          }

          .pop_btn {
            position: absolute;
            z-index: 5;
            right: 0;
            bottom: 6px;
            left: 0;
            width: 100%;
            height: 688rpx;

            div {
              width: 168px;
              height: 36px;
              color: #fff;
              font-family: 'PingFang SC';
              font-size: 14px;
              font-weight: 500;
              line-height: 34px;
              text-align: center;

              &:nth-child(1) {
                border-radius: 18px 0 0 18px;
              }

              &:nth-child(2) {
                border-radius: 0 18px 18px 0;
              }
            }
          }
        }
      }
    }
  }

  .prew_pc_list {
    .prew_item {
      width: 1274px;
      height: 722px;
      margin-bottom: 35px;

      .prew_item_title {
        flex-shrink: 0;
        width: 60px;
        margin-right: 18px;
        margin-left: 70px;
        color: #222;
        font-family: 'Microsoft YaHei';
        font-size: 14px;
        font-weight: 400;
        line-height: 35px;
        text-align: right;
      }

      .prew_item_goods {
        position: relative;
        width: 1125px;
        height: 722px;
        overflow: hidden;
        border-radius: 4px;
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        box-shadow: 0 0 12px 0 rgb(129 129 129 / 30%);
        cursor: default;

        .prew_item_follow {
          position: absolute;
          z-index: 2;
          top: 32px;
          left: 305px;
          width: 42px;
          height: 28px;
          border-radius: 14px;
          color: #fff;
          font-family: 'Microsoft YaHei';
          font-size: 13px;
          font-weight: 400;
          line-height: 28px;
          text-align: center;
        }

        .prew_item_score {
          position: absolute;
          z-index: 2;
          top: 46px;
          left: 256px;

          span {
            &:nth-child(1) {
              font-family: 'Microsoft YaHei';
              font-size: 13px;
              font-weight: 400;
            }

            &:nth-child(2) {
              display: inline-block;
              position: relative;
              z-index: 2;
              top: 4px;
              left: 4px;
              border: 5px solid transparent;
              border-top: 6px solid;
            }
          }
        }

        .top_search {
          position: absolute;
          z-index: 2;
          top: 29px;
          left: 425px;

          .top_search_left {
            width: 300px;
            height: 32px;
            border: 2px solid;
            background: #fff;
            line-height: 32px;

            span {
              margin-left: 10px;
              color: #999;
              font-family: 'Microsoft YaHei';
              font-size: 12px;
              font-weight: 400;
            }
          }

          .top_search_right {
            width: 90px;
            height: 32px;
            color: #fff;
            font-family: 'Microsoft YaHei';
            font-size: 15px;
            font-weight: 400;
            line-height: 32px;
            text-align: center;
          }
        }

        .top_search_store {
          position: absolute;
          z-index: 2;
          top: 29px;
          left: 820px;
          width: 90px;
          height: 32px;
          background: #333;
          color: #fff;
          font-family: 'Microsoft YaHei';
          font-size: 15px;
          font-weight: 400;
          line-height: 32px;
          text-align: center;
        }

        .top_search_right_store {
          position: absolute;
          z-index: 2;
          top: 181px;
          right: 18px;
          transform: scale(0.85);

          div {
            &:nth-child(1) {
              width: 160px;
              height: 20px;
              padding-left: 6px;
              border-radius: 2px 0 0 2px;
              background: #fff;
              color: #999;
              font-family: 'Microsoft YaHei';
              font-size: 12px;
              font-weight: 400;
            }

            &:nth-child(2) {
              width: 32px;
              height: 20px;
              border-radius: 2px;
              color: #fff;
              font-family: 'Microsoft YaHei';
              font-size: 12px;
              font-weight: 400;
              line-height: 20px;
              text-align: center;
            }
          }
        }

        .top_store_label {
          position: absolute;
          z-index: 2;
          top: 217px;
          right: 147px;
          width: 31px;
          height: 17px;
          transform: scale(0.9);
          border-radius: 2px;
          color: #fff;
          font-family: 'Microsoft YaHei';
          font-size: 12px;
          font-weight: 400;
          line-height: 18px;
          text-align: center;
        }

        .top_cart {
          position: absolute;
          z-index: 2;
          top: 27px;
          left: 934px;
          width: 146px;
          height: 38px;
          border: 1px solid #e3e3e3;

          .top_cart_title {
            margin-right: 9px;
            margin-left: 8px;
            font-family: 'Microsoft YaHei';
            font-size: 14px;
            font-weight: 400;
            line-height: 34px;
          }

          .top_cart_num {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            color: #fff;
            font-family: 'Microsoft YaHei';
            font-size: 13px;
            font-weight: 400;
            line-height: 16px;
            text-align: center;
          }
        }

        .goods_desc {
          position: absolute;
          z-index: 2;
          top: 285px;
          left: 374px;
          font-family: 'Microsoft YaHei';
          font-size: 13px;
          font-weight: 400;
        }

        .goods_point_exchange {
          position: absolute;
          z-index: 1;
          top: 314px;
          left: 374px;
          width: 541px;
          height: 38px;
          background-repeat: no-repeat;
          background-position: center;
          background-size: cover;

          div {
            margin-right: 15px;
            color: #fff;
            font-family: 'Microsoft YaHei';
            font-size: 14px;
            font-weight: 400;
          }
        }

        .goods_price {
          position: absolute;
          z-index: 2;
          top: 366px;
          left: 442px;
          align-items: baseline;
          font-family: 'PingFang SC';
          font-size: 13px;
          font-weight: 700;

          span {
            font-size: 16px;
          }
        }

        .goods_type {
          position: absolute;
          z-index: 2;
          top: 468px;
          left: 442px;

          .goods_type_item {
            position: relative;
            width: 66px;
            height: 35px;
            margin-right: 12px;
            padding: 0 12px;
            overflow: hidden;
            border: 1px solid #dfdfdf;
            border-radius: 3px;
            color: #333;
            font-family: 'Microsoft YaHei';
            font-size: 12px;
            font-weight: 400;
            line-height: 35px;
            text-align: center;
          }
        }

        .goods_selected {
          position: absolute;
          z-index: 2;
          bottom: 101px;
          left: 65px;
          width: 50px;
          height: 50px;
          border: 1px solid;
        }

        .left_btn {
          position: absolute;
          z-index: 2;
          bottom: 64px;
          left: 54px;

          span {
            margin-right: 10px;
            margin-left: 6px;
            color: #333;
            font-family: 'Microsoft YaHei';
            font-size: 12px;
            font-weight: 400;
            line-height: 20px;
          }
        }

        .btn_list_add {
          position: absolute;
          z-index: 2;
          bottom: 50px;
          left: 440px;
          width: 150px;
          height: 42px;
          border-radius: 4px;
          color: #fff;
          font-family: 'Microsoft YaHei';
          font-size: 16px;
          font-weight: 400;
          line-height: 40px;
          text-align: center;
        }
      }

      .prew_item_line {
        width: 1125px;
        height: 1px;
        margin-bottom: 35px;
        background: #d9d9d969;
      }

      .prew_item_list {
        position: relative;
        width: 1125px;
        height: 752px;
        overflow: hidden;
        border-radius: 4px;
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        box-shadow: 0 0 12px 0 rgb(129 129 129 / 30%);
        cursor: default;

        .top_introduce_item {
          position: absolute;
          z-index: 2;
          padding: 0 8px;
          border-radius: 3px;
          color: #fff;
          font-family: 'Microsoft YaHei';
          font-size: 12px;
          font-weight: 400;
          line-height: 30px;

          span {
            font-weight: bold;
          }

          &:nth-child(1) {
            top: 285px;
            left: 60px;
          }

          &:nth-child(2) {
            top: 285px;
            left: 325px;
          }

          &:nth-child(3) {
            top: 285px;
            left: 588px;
          }

          &:nth-child(4) {
            top: 285px;
            left: 853px;
          }

          &:nth-child(5) {
            bottom: 20px;
            left: 60px;
          }

          &:nth-child(6) {
            bottom: 20px;
            left: 325px;
          }

          &:nth-child(7) {
            bottom: 20px;
            left: 588px;
          }

          &:nth-child(8) {
            bottom: 20px;
            left: 853px;
          }
        }

        .bottom_item {
          position: absolute;
          z-index: 2;
          bottom: 4px;

          &.bottom_item_one {
            left: 40px;
          }

          &.bottom_item_two {
            left: 284px;
          }

          &.bottom_item_three {
            left: 592px;
          }

          &.bottom_item_four {
            left: 888px;
          }
        }
      }
    }
  }
}

.diy_auto_color_modal {
  margin-top: 10px;
  margin-bottom: 10px;

  .color_title {
    width: 115px;
    margin-right: 5px;
    text-align: right;
  }

  .color_show {
    .show_color {
      display: inline-block;
      width: 50px;
      height: 30px;
      margin-right: 10px;
      padding: 5px;
      border: 1px solid #eee;
      line-height: 0;
      cursor: pointer;
    }

    span {
      display: inline-block;
      width: 100%;
      height: 100%;
    }
  }

  .color_picker_wrap {
    position: absolute;
    z-index: 2;

    .color_picker_mask {
      position: fixed;
      inset: 0;
    }
  }
}
