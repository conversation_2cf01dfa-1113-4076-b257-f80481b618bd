<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader
        title="基本设置"
      />
      <Spin :spinning="loading">
        <div class="site_info_scroll">
          <StandardTableRow
            width="100%"
            :tipFlag="needTipFlag"
            :data="info_data.data"
            @callback-event="callbackEvent"
            @submit-event="submitEvent"
            @btn-click-event="btnClickEvent"
          />
        </div>
      </Spin>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'IntegralSetting',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { Spin } from 'ant-design-vue';
  import StandardTableRow from '/@/components/StandardTableRow/index.vue';
  import {
    getSettingListApi,
    getSettingUpdateApi,
    getSettingEsInitApi,
    getSettingPlatFormShelfApi,
  } from '/@/api/common/common';
  import { failTip, sucTip } from '/@/utils/utils';

  const info_data = reactive({
    data: [],
  });

  const loading = ref(true);
  const clickEvent = ref(false); //事件防抖动
  const needTipFlag = ref(false);

  const origionIntegralConversionRatio = ref(0); //原始的积分换算比例
  const curIntegralConversionRatio = ref(0); //最新的积分换算比例

  // 获取数据
  const get_setting = async () => {
    try {
      const res = await getSettingListApi({
        str: 'integral_mall_is_enable,integral_audit_is_enable,integral_conversion_ratio',
      });
      if (res && res.state == 200) {
        loading.value = false;
        info_data.data = [];
        for (let i in res.data) {
          if (res.data[i].type == 1) {
            if (res.data[i].name == 'integral_conversion_ratio') {
              origionIntegralConversionRatio.value = res.data[i].value * 1;
              curIntegralConversionRatio.value = res.data[i].value * 1;
            }
            info_data.data.push({
              type: 'inputnum',
              label: res.data[i].title,
              desc: res.data[i].description,
              key: res.data[i].name,
              placeholder: '请输入' + res.data[i].title,
              min: res.data[i].name == 'integral_conversion_ratio'?1:0,
              value: res.data[i].value,
              callback: true,
              require:res.data[i].name == 'integral_conversion_ratio'?true:false,
              rules: res.data[i].name == 'integral_conversion_ratio'?[
                {
                  required: true,
                  message: '请输入' + res.data[i].title,
                },
              ]:false,
            });
          } else if (res.data[i].type == 4) {
            info_data.data.push({
              type: 'switch',
              label: res.data[i].title,
              desc: res.data[i].description,
              key: res.data[i].name,
              placeholder: '',
              value: res.data[i].value,
              checkedValue: '1',
              unCheckedValue: '0',
            });
          }
        }
        if (info_data.data.length > 0) {
          info_data.data.push({
            type: 'button',
            showSubmit: true,
            submitText: '保存',
            showCancle: false,
            tip: {
              title: '修改积分换算比例会导致之前的积分商品全部下架，确定修改吗？',
            },
            btnList: [
              {
                btnText: '立即更新商品数据',
                type: 'updateGoods',
                callback: true,
              },
            ],
          });
        }
      }
    } catch (error) {}
  };

  // 更新数据
  const callbackEvent = (item) => {
    let temp = info_data.data.filter((items) => items.key == item.contentItem.key);
    if (temp.length > 0) {
      temp[0].value = item.val1;
    }
    if (temp[0].key == 'integral_conversion_ratio') {
      curIntegralConversionRatio.value = item.val1;
      needTipFlag.value =
        origionIntegralConversionRatio.value == curIntegralConversionRatio.value||!temp[0].value ? false : true;
    }
  };

  // 立即更新商品数据
  const btnClickEvent = () => {
    if (clickEvent.value) return;
    clickEvent.value = true;
    updateEvent();
  };
  //立即更新商品数据
  const updateEvent = async () => {
    const res = await getSettingEsInitApi();
    clickEvent.value = false;
    if (res.state == 200) {
      sucTip(res.msg);
    } else {
      failTip(res.msg);
    }
  };

  // 点击保存
  const submitEvent = async (val) => {
    try {
      if (needTipFlag.value) {
        updatePointRate(val.integral_conversion_ratio);
      }
      loading.value = true;
      let res = await getSettingUpdateApi(val);
      if (res.state == 200) {
        loading.value = false;
        sucTip(res.msg);
        origionIntegralConversionRatio.value = curIntegralConversionRatio.value;
        needTipFlag.value = false;
      } else {
        loading.value = false;
        failTip(res.msg);
      }
    } catch (error) {}
  };

  //更新积分兑换比例
  const updatePointRate = async (val) => {
    let res = await getSettingPlatFormShelfApi(val);
    if (res.state != 200) {
      failTip(res.msg);
    } else {
      origionIntegralConversionRatio.value = val;
    }
  };

  onMounted(() => {
    get_setting();
  });
</script>
<style lang="less"></style>
