<template>
  <div class="section_padding add_sign">
    <div class="section_padding_back full_activity common_page com_flex_column">
      <SldComHeader title="签到活动" />
      <div class="com_line" style="height: 1px"></div>
      <Spin :spinning="loading">
        <div class="add_sign_height">
          <Form layout="inline" ref="formRef" :model="detail">
            <div>
              <CommonTitle text="规则设置" style="margin-top: 10px" />
              <div class="full_acm_activity flex_column_start_start">
                <!-- 活动时间 start -->
                <div class="item flex_row_start_start">
                  <div class="left"> <span style="color: red">*</span>签到周期</div>
                  <div class="right">
                    <Form.Item
                      name="startTime"
                      extra="活动时间不可与其他活动重叠，签到周期最长为30天"
                      style="width: 400px"
                      :rules="[
                        {
                          required: true,
                          message: `请选择活动时间`,
                        },
                      ]"
                    >
                      <RangePicker
                        style="width: 400px"
                        :disabled="viewFlag"
                        :format="'YYYY-MM-DD HH:mm:00'"
                        v-model:value="detail.startTime"
                        :disabledDate="
                          (currentDate) => currentDate && currentDate < moment().subtract(1, 'days')
                        "
                        :showTime="{ format: 'HH:mm' }"
                        :placeholder="['开始时间', '结束时间']"
                        :getPopupContainer="
                          (triggerNode) => {
                            return triggerNode.parentNode;
                          }
                        "
                      />
                    </Form.Item>
                  </div>
                </div>
                <!-- 活动时间 end -->
                <!-- 日签奖励 start -->
                <div class="item flex_row_start_start">
                  <div class="left">日签奖励</div>
                  <div class="right" style="padding-top: 5px">
                    <Checkbox v-model:checked="perSignFlag" :disabled="viewFlag"> 开启 </Checkbox>
                    <div v-if="perSignFlag">
                      <div style="width: 100%; height: 10px"></div>
                      <div class="flex_row_start_center">
                        <span style="color: red">*</span>
                        <span style="margin-right: 10px">赠送</span>
                        <Form.Item
                          name="integralPerSign"
                          style="width: 100px"
                          :rules="[
                            {
                              required: true,
                              message: `该项必填`,
                            },
                          ]"
                        >
                          <InputNumber
                            :disabled="viewFlag"
                            :max="9999999"
                            :min="1"
                            style="width: 100px !important"
                            :precision="0"
                            v-model:value="detail.integralPerSign"
                          />
                        </Form.Item>
                        <span>积分</span>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 日签奖励 end -->
                <!-- 连签奖励 start -->
                <div class="item flex_row_start_start">
                  <div class="left">连签奖励</div>
                  <div class="right" :style="{ paddingTop: continueSignFlag ? 0 : '5px' }">
                    <div class="flex_row_start_center">
                      <Checkbox v-model:checked="continueSignFlag" :disabled="viewFlag">
                        开启
                      </Checkbox>
                      <div v-if="continueSignFlag">
                        <div class="flex_row_start_center">
                          <span style="margin-right: 10px">连续签到</span>
                          <Form.Item
                            name="continueNum"
                            style="width: 100px"
                            :rules="[
                              {
                                required: true,
                                message: `该项必填`,
                              },
                            ]"
                          >
                            <InputNumber
                              :disabled="viewFlag"
                              :max="20"
                              :min="2"
                              style="width: 100px !important"
                              :precision="0"
                              v-model:value="detail.continueNum"
                            />
                          </Form.Item>
                          <span>天</span>
                        </div>
                      </div>
                    </div>
                    <div style="width: 100%; height: 10px" v-if="continueSignFlag"></div>
                    <div class="flex_row_start_center" v-if="continueSignFlag">
                      <Checkbox v-model:checked="continueSignPointFlag" :disabled="viewFlag">
                        积分
                      </Checkbox>
                      <div v-if="continueSignPointFlag">
                        <div class="flex_row_start_center">
                          <Form.Item name="bonusIntegral" style="width: 100px">
                            <InputNumber
                              :disabled="viewFlag"
                              :max="9999999"
                              :min="1"
                              style="width: 100px !important"
                              :precision="0"
                              v-model:value="detail.bonusIntegral"
                            />
                          </Form.Item>
                          <span>个</span>
                        </div>
                      </div>
                    </div>
                    <div style="width: 100%; height: 10px" v-if="continueSignFlag"></div>
                    <div class="flex_row_start_center" v-if="continueSignFlag">
                      <Checkbox
                        :checked="
                          sel_voucher.couponId != undefined && sel_voucher.couponId ? true : false
                        "
                        :disabled="viewFlag"
                        @change="handleVoucher('add')"
                      >
                        送优惠券
                      </Checkbox>
                      <span
                        v-if="
                          sel_voucher.couponId != undefined && sel_voucher.couponId && !viewFlag
                        "
                        class="reset_sel"
                        @click="handleVoucher('edit')"
                        >重新选择</span
                      >
                    </div>
                    <div
                      class="sel_goods flex_column_start_start"
                      v-if="sel_voucher.couponId != undefined && sel_voucher.couponId"
                    >
                      <div class="flex_row_start_center">
                        <span class="sel_tip">您已选择如下优惠券：</span>
                      </div>
                      <div class="goods_info flex_row_start_center">
                        <div class="left flex_row_center_center">
                          <img src="@/assets/images/voucher.png" alt="" />
                        </div>
                        <div class="flex_column_between_start">
                          <span class="goods_name">优惠券</span>
                          <span class="goods_price">{{ sel_voucher.couponName }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 连签奖励 end -->
                <!-- 规则说明 start -->
                <div class="item flex_row_start_start">
                  <div class="left"> <span style="color: red">*</span>规则说明</div>
                  <div class="right">
                    <Form.Item
                      name="bonusRules"
                      extra="最多输入200字"
                      style="width: 400px"
                      :rules="[
                        {
                          required: true,
                          message: `请输入规则说明`,
                        },
                      ]"
                    >
                      <Textarea
                        v-model:value="detail.bonusRules"
                        :disabled="viewFlag"
                        :maxlength="200"
                        style="width: 450px; min-height: 32px"
                        :rows="4"
                        placeholder="请输入规则说明"
                      ></Textarea>
                    </Form.Item>
                  </div>
                </div>
                <!-- 规则说明 end -->
              </div>
              <CommonTitle text="页面设置" style="margin-top: 10px" />
              <div class="full_acm_activity flex_column_start_start">
                <!-- 分享设置 start -->
                <div class="item flex_row_start_start">
                  <div class="left">分享设置</div>
                  <div class="right" style="padding-top: 5px">
                    <Checkbox v-model:checked="pageShareFlag" :disabled="viewFlag"> 开启 </Checkbox>
                    <div style="width: 100%; height: 10px" v-if="pageShareFlag"></div>
                    <div v-if="pageShareFlag" class="flex_row_start_center">
                      <span style="color: red">*</span>
                      <span style="margin-right: 10px">分享标题</span>
                      <Form.Item
                        name="shareTitle"
                        style="width: 350px"
                        :rules="[
                          {
                            required: true,
                            whitespace: true,
                            message: `请输入分享标题`,
                          },
                        ]"
                      >
                        <Input
                          :maxLength="15"
                          :disabled="viewFlag"
                          style="width: 350px !important"
                          placeholder="请输入分享标题"
                          v-model:value="detail.shareTitle"
                        />
                      </Form.Item>
                    </div>
                    <div style="width: 100%; height: 10px" v-if="pageShareFlag"></div>
                    <div v-if="pageShareFlag" class="flex_row_start_center">
                      <span style="color: red">*</span>
                      <span style="margin-right: 10px">分享描述</span>
                      <Form.Item
                        name="shareDesc"
                        style="width: 350px"
                        :rules="[
                          {
                            required: true,
                            whitespace: true,
                            message: `请输入分享描述`,
                          },
                        ]"
                      >
                        <Input
                          :disabled="viewFlag"
                          :maxLength="15"
                          style="width: 350px !important"
                          placeholder="请输入分享描述"
                          v-model:value="detail.shareDesc"
                        />
                      </Form.Item>
                    </div>
                    <div style="width: 100%; height: 10px" v-if="pageShareFlag"></div>
                    <div v-if="pageShareFlag" class="flex_row_start_center">
                      <Form.Item extra="建议上传的分享图片大小不超过20kb" style="width: 500px">
                        <div class="flex_row_start_start">
                          <span style="color: red">*</span>
                          <span style="margin-right: 10px">分享图片</span>
                          <div style="width: 150px">
                            <Upload
                              :maxCount="1"
                              accept=" .gif, .jpeg, .png, .jpg,"
                              name="file"
                              :disabled="viewFlag"
                              @click="beforeUploadClick"
                              :action="`${apiUrl}/v3/oss/admin/upload?source=setting`"
                              listType="picture-card"
                              :file-list="uploadImgInfo.shareImgFileList"
                              :beforeUpload="
                                (file) => beforeUpload(file, ' .gif, .jpeg, .png, .jpg,', 20)
                              "
                              @change="(e) => handleFileChange(e, 'shareImgFileList')"
                              :headers="{
                                Authorization: imgToken,
                              }"
                            >
                              <div v-if="uploadImgInfo.shareImgFileList.length < 1">
                                <PlusOutlined />
                                <div className="ant-upload-text">上传图片</div>
                              </div>
                            </Upload>
                          </div>
                        </div>
                      </Form.Item>
                    </div>
                  </div>
                </div>
                <!-- 分享设置 end -->
                <!-- 提醒设置 start -->
                <div class="item flex_row_start_start">
                  <div class="left">提醒设置</div>
                  <div class="right" style="padding-top: 5px">
                    <Form.Item
                      extra="开启提醒,未签到用户每日第1次访问商城个人中心时会弹出签到弹窗"
                      style="width: 400px"
                    >
                      <Checkbox v-model:checked="pagetipFlag" :disabled="viewFlag"> 开启 </Checkbox>
                    </Form.Item>
                    <div style="width: 100%; height: 10px" v-if="pagetipFlag"></div>
                    <div v-if="pagetipFlag" class="flex_row_start_center">
                      <Form.Item style="width: 500px">
                        <div class="flex_row_start_start">
                          <span style="color: red">*</span>
                          <span style="margin-right: 10px">弹窗图片</span>
                          <div style="width: 150px">
                            <Upload
                              :maxCount="1"
                              accept=" .gif, .jpeg, .png, .jpg,"
                              name="file"
                              @click="beforeUploadClick"
                              :disabled="viewFlag"
                              :action="`${apiUrl}/v3/oss/admin/upload?source=setting`"
                              listType="picture-card"
                              :file-list="uploadImgInfo.tipImgFileList"
                              :beforeUpload="
                                (file) => beforeUpload(file, ' .gif, .jpeg, .png, .jpg,', 20)
                              "
                              @change="(e) => handleFileChange(e, 'tipImgFileList')"
                              :headers="{
                                Authorization: imgToken,
                              }"
                            >
                              <div v-if="uploadImgInfo.tipImgFileList.length < 1">
                                <PlusOutlined />
                                <div className="ant-upload-text">上传图片</div>
                              </div>
                            </Upload>
                          </div>
                        </div>
                      </Form.Item>
                    </div>
                  </div>
                </div>
                <!-- 提醒设置 end -->
              </div>
              <div style="width: 100%; height: 60px; background: #fff"></div>
            </div>
          </Form>
        </div>
        <div
          class="m_diy_bottom_wrap"
          :style="{ position: 'fixed', left: getRealWidth + 10 + 'px' }"
        >
          <div
            class="add_goods_bottom_btn"
            :class="{ add_goods_bottom_btn_rig: viewFlag }"
            @click="goBack"
          >
            返回
          </div>
          <div
            class="add_goods_bottom_btn add_goods_bottom_btn_sel"
            v-if="!viewFlag"
            @click="handleSaveAllData"
          >
            保存
          </div>
        </div>
      </Spin>
    </div>
    <SldSelGoodsSingleDiy
      @confirm-event="handleConfirm"
      @cancle-event="handleCancle"
      :link_type="link_type"
      :modalVisible="visibleModal"
    ></SldSelGoodsSingleDiy>
  </div>
</template>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import {
    Spin,
    Form,
    Input,
    RangePicker,
    Checkbox,
    InputNumber,
    Textarea,
    Upload,
  } from 'ant-design-vue';
  import dayjs from 'dayjs';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import { useGlobSetting } from '/@/hooks/setting';
  import SldSelGoodsSingleDiy from '/@/components/SldSelGoodsSingleDiy/index.vue';
  import moment from 'moment';
  import { useRoute, useRouter } from 'vue-router';
  import CommonTitle from '/@/components/CommonTitle/index.vue';
  import { useUserStore } from '/@/store/modules/user';
  import {
    getSignActivityAddApi,
    getSignActivityUpdateApi,
    getSignActivityDetailApi,
  } from '/@/api/sign/sign';
  import { getToken } from '/@/utils/auth';
  import { failTip, sucTip } from '/@/utils/utils';
  import { pageClose } from '/@/utils/utils';
  import { useMultipleTabStore } from '/@/store/modules/multipleTab';

  const { apiUrl } = useGlobSetting();
  const { getRealWidth } = useMenuSetting();

  const loading = ref(false);
  const route = useRoute();
  const userStore = useUserStore();
  const tabStore = useMultipleTabStore();
  const router = useRouter();
  const formRef = ref();
  const imgToken = ref('Bearer ' + getToken() || ''); //上传文件token参数

  const visibleModal = ref(false);
  const projectName = ref(import.meta.env.VITE_GLOB_APP_TITLE);
  const pageTitle = ref('新建活动');
  const preview_img = ref('');
  const preview_alt_con = ref('');
  const show_preview_modal = ref('');
  const uploadImgInfo = reactive({
    shareImgFileList: [], //分享图片数据
    tipImgFileList: [], //提醒图片数据
  }); //上传的图片数据
  const perSignFlag = ref(false); //日签奖励是否开启
  const continueSignFlag = ref(false); //连签奖励是否开启
  const continueSignPointFlag = ref(false); //连签奖励积分是否开启
  const pageShareFlag = ref(false); //分享设置是否开启
  const pagetipFlag = ref(false); //提醒设置是否开启
  const sel_voucher = ref({}); //选择的优惠券信息
  const link_type = ref('voucher');
  const query = ref(route.query);
  const detail = ref({}); //活动详情数据
  const viewFlag = ref(route.query?.tar != undefined && route.query?.tar == 'view' ? true : false); //查看标识
  const dateRange = ref([]);

  //文件上传前处理数据
  function beforeUpload(file, accept, limit) {
    if (accept != undefined && accept != null && accept) {
      //校验文件格式类型
      let accept_list = accept
        .replaceAll(' ', '')
        .split(',')
        .filter((item) => item && item.trim());
      let file_type = file.name ? '.' + file.name.split('.').at(-1) : '';
      if (accept_list.length > 0 && file_type != '' && accept_list.indexOf(file_type) == -1) {
        failTip('上传文件格式有误');
        return false;
      }
    }
    let uploadLimit = limit ? limit : 20;
    if (file.size > 1024 * 1024 * uploadLimit) {
      failTip('上传文件过大，请上传小于' + uploadLimit + 'M的文件');
      return false;
    }
  }

  // 优惠券弹框确定
  const handleConfirm = (record, ids) => {
    sel_voucher.value = record;
    visibleModal.value = false;
  };

  // 优惠券弹框取消
  const handleCancle = () => {
    visibleModal.value = false;
  };

  const beforeUploadClick = async()=> {
    imgToken.value = 'Bearer ' + userStore.getToken
    if(userStore.getToken){
      let res = await userStore.getSldCommonService()
      if(res.state == 200){
        imgToken.value = 'Bearer ' + res.token
      }
    }
  }

  // 优惠券弹框打开
  const handleVoucher = (type) => {
    if (type == 'add') {
      if (sel_voucher.value.couponId != undefined && sel_voucher.value.couponId) {
        sel_voucher.value = {};
      } else {
        visibleModal.value = true;
      }
    } else {
      visibleModal.value = true;
    }
  };

  const goBack = () => {
    if (window.history && window.history.length == 1) {
      pageClose();
    } else {
      const { fullPath } = route;
      tabStore.closeTabByKey(fullPath, router);
      router.back();
    }
  };

  // 点击保存
  const handleSaveAllData = async () => {
    formRef.value
      .validate()
      .then(async (values) => {
        if (!perSignFlag.value && !continueSignFlag.value) {
          failTip(`请至少选中一种奖励方式～`);
          return false;
        }
        let params = {};
        params.startTime = values.startTime[0]
          ? values.startTime[0].format('YYYY-MM-DD HH:mm:00')
          : '';
        params.endTime = values.startTime[1]
          ? values.startTime[1].format('YYYY-MM-DD HH:mm:00')
          : '';
        //签到周期开始时间必须小于结束时间
        if (Date.parse(params.startTime) >= Date.parse(params.endTime)) {
          failTip(`签到周期开始时间必须小于结束时间～`);
          return false;
        }
        const timer = 1000 * 3600 * 24 * 30
        if((Date.parse(params.endTime) - Date.parse(params.startTime)) > timer){
          failTip('签到周期最长为30天')
          return
        }
        params.bonusIntegral =
          values.bonusIntegral != undefined && values.bonusIntegral ? values.bonusIntegral : 0; //连签奖励:积分
        params.bonusRules = values.bonusRules; //规则说明
        params.continueNum =
          values.continueNum != undefined && values.continueNum ? values.continueNum : 0; //连续签到天数；0表示无连签奖励
        //连续签到天数必须小于签到周期
        if (
          Date.parse(params.endTime) - Date.parse(params.startTime) <=
          params.continueNum * 24 * 60 * 60 * 1000
        ) {
          failTip(`连续签到天数必须小于签到周期～`);
          return false;
        }
        params.integralPerSign =
          values.integralPerSign != undefined && values.integralPerSign
            ? values.integralPerSign
            : 0; //日签奖励积分，0表示不开启日签奖励
        //连签奖励优惠券ID
        params.bonusVoucher =
          sel_voucher.value.couponId != undefined && sel_voucher.value.couponId > 0
            ? sel_voucher.value.couponId
            : '';

        if (params.continueNum > 0 && !params.bonusVoucher && !params.bonusIntegral) {
          failTip(`请设置连签奖励～`);
          return false;
        }
        let pageData = {};
        pageData.pageShareFlag = pageShareFlag.value; //分享设置是否开启
        pageData.shareData = {}; //分享数据
        if (pageData.pageShareFlag) {
          let shareData = {};
          shareData.shareTitle = values.shareTitle; //分享标题
          shareData.shareDesc = values.shareDesc; //分享描述
          //分享图片
          if (uploadImgInfo.shareImgFileList.length > 0) {
            shareData.shareImgPath = uploadImgInfo.shareImgFileList[0].response.data.path; //图片
            shareData.shareImgUrl = uploadImgInfo.shareImgFileList[0].response.data.url; //图片
          } else {
            failTip(`请上传分享图片～`);
            return false;
          }
          pageData.shareData = shareData;
        }
        pageData.pagetipFlag = pagetipFlag.value; //提醒设置是否开启
        pageData.tipData = {}; //弹窗提醒图片数据
        if (pageData.pagetipFlag) {
          let tipData = {};
          //弹窗图片
          if (uploadImgInfo.tipImgFileList.length > 0) {
            tipData.imgPath = uploadImgInfo.tipImgFileList[0].response.data.path; //图片
            tipData.imgUrl = uploadImgInfo.tipImgFileList[0].response.data.url; //图片
          } else {
            failTip(`请上传弹窗图片～`);
            return false;
          }
          pageData.tipData = tipData;
        }
        params.templateJson = JSON.stringify(pageData); //页面设置数据
        loading.value = true;
        let res = null;
        if (
          route.query?.id != undefined &&
          Number(route.query?.id) > 0 &&
          route.query?.tar == 'edit'
        ) {
          //编辑签到
          params.signActivityId = route.query?.id;
          res = await getSignActivityUpdateApi(params);
        } else {
          //新增签到
          res = await getSignActivityAddApi(params);
        }
        if (res.state == 200) {
          sucTip(res.msg);
          userStore.setDelKeepAlive([route.name,'SignGroup'])
          setTimeout(() => {
            goBack();
          }, 500);
        } else {
          loading.value = false;
          failTip(res.msg);
        }
      })
      .catch((err) => {
        console.log('error', err);
      });
  };

  //数据变化事件
  function handleFileChange(e, name) {
    if (e.file.status != undefined && e.file.status != 'error') {
      e.fileList.forEach((item) => {
        if (item.response && item.response.data) {
          item.url = item.response.data.url;
        }
      });
      uploadImgInfo[name] = e.fileList;
    }
  }

  //获取签到详情
  const get_detail = async (id) => {
    try {
      let res = await getSignActivityDetailApi({ signActivityId: id });
      if (res.state == 200) {
        if (res.data.startTime) {
          res.data.startTime = [
            dayjs(res.data.startTime, 'YYYY-MM-DD HH:mm:ss'),
            dayjs(res.data.endTime, 'YYYY-MM-DD HH:mm:ss'),
          ];
        }
        detail.value = res.data;
        //初始化选中的优惠券数据
        if (detail.value.bonusVoucher) {
          sel_voucher.value.couponId = detail.value.bonusVoucher;
          sel_voucher.value.couponName = detail.value.bonusVoucherName;
        }
        perSignFlag.value = detail.value.integralPerSign > 0 ? true : false;
        continueSignFlag.value = detail.value.continueNum > 0 ? true : false;
        continueSignPointFlag.value = detail.value.bonusIntegral > 0 ? true : false;
        //页面设置：
        if (detail.value.templateJson) {
          let pageData = JSON.parse(detail.value.templateJson.replace(/&quot;/g, '"'));
          pageShareFlag.value = pageData.pageShareFlag;
          if (pageShareFlag.value) {
            detail.value.shareTitle = pageData.shareData.shareTitle;
            detail.value.shareDesc = pageData.shareData.shareDesc;
            let shareImgFileList = [];
            if (pageData.shareData.shareImgPath) {
              let tmp_data = {};
              tmp_data.uid = pageData.shareData.shareImgPath;
              tmp_data.name = pageData.shareData.shareImgPath;
              tmp_data.status = 'done';
              tmp_data.url = pageData.shareData.shareImgUrl;
              tmp_data.response = {};
              tmp_data.response.data = {};
              tmp_data.response.data.url = pageData.shareData.shareImgUrl;
              tmp_data.response.data.path = pageData.shareData.shareImgPath;
              shareImgFileList.push(tmp_data);
            }
            uploadImgInfo.shareImgFileList = shareImgFileList;
          }
          pagetipFlag.value = pageData.pagetipFlag;
          if (pagetipFlag.value) {
            let tipImgFileList = [];
            if (pageData.tipData.imgPath) {
              let tmp_data = {};
              tmp_data.uid = pageData.tipData.imgPath;
              tmp_data.name = pageData.tipData.imgPath;
              tmp_data.status = 'done';
              tmp_data.url = pageData.tipData.imgUrl;
              tmp_data.response = {};
              tmp_data.response.data = {};
              tmp_data.response.data.url = pageData.tipData.imgUrl;
              tmp_data.response.data.path = pageData.tipData.imgPath;
              tipImgFileList.push(tmp_data);
            }
            uploadImgInfo.tipImgFileList = tipImgFileList;
          }
        } else {
          pageShareFlag.value = false;
          pagetipFlag.value = false;
        }
        loading.value = false;
      } else {
        loading.value = false;
        failTip(res.msg);
      }
    } catch (error) {
      console.info(error, 'error');
    }
  };

  onMounted(() => {
    if (route.query?.tar != undefined) {
      if (route.query?.tar == 'edit') {
        pageTitle.value = '编辑活动';
      } else if (route.query?.tar == 'view') {
        pageTitle.value = '查看活动';
      }
    }
    document.title = pageTitle.value + ' - ' + projectName.value;
    if (route.query?.id != undefined && Number(route.query?.id) > 0) {
      loading.value = true;
      get_detail(route.query?.id);
    }
  });
</script>
<style lang="less">
  @import './style/add.less';
</style>
