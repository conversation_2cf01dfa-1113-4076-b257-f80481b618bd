<template>
  <div class="sign_lists">
    <BasicTable @register="registerTable" rowKey="signActivityId" style="padding: 0;">
      <template #tableTitle>
        <div class="toolbar">
          <div class="toolbar_btn" @click="view('', 'add','SignToAdd')">
            <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" :fillColor="'#ff9237'" />
            <span style="margin-left: 4px">新建活动</span>
          </div>
        </div>
      </template>
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex == 'publishNum'">
          <router-link :to="`/marketing_promotion/coupon_to_receive_list?id=${record.couponId}`">
            <div class="voucher_num"
              >{{ record.receivedNum }}/{{ record.usedNum }}/{{ record.publishNum }}</div
            >
          </router-link>
        </template>
        <template v-if="column.dataIndex == 'isRemind'">
          <div>
            <Switch
              @change="
                (checked) =>
                  operate(
                    {
                      signActivityId: record.signActivityId,
                      isRemind: checked ? 1 : 0,
                    },
                    'isRemind',
                  )
              "
              :disabled="record.signState == 3 && text != 1 ? true : false"
              :checked="text == 1 ? true : false"
            />
          </div>
        </template>
        <template v-if="column.dataIndex == 'state'">
          <div>
            <Switch
              @change="
                (checked) =>
                  operate(
                    {
                      signActivityId: record.signActivityId,
                      isOpen: checked ? 1 : 0,
                    },
                    'state',
                  )
              "
              :disabled="record.signState == 3 && text != 1 ? true : false"
              :checked="text == 1 ? true : false"
            />
          </div>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                label: '查看',
                onClick: view.bind(null, record.signActivityId, 'view','SignToView'),
              },
              {
                label: '编辑',
                ifShow: record.signState == 1,
                onClick: view.bind(null, record.signActivityId, 'edit','SignToEdit'),
              },
              {
                label: '删除',
                ifShow: record.signState == 1 || record.signState == 3,
                popConfirm: {
                  title: '删除后不可恢复，是否确定删除？',
                  placement: 'left',
                  confirm: operate.bind(null, { signActivityId: record.signActivityId }, 'del'),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
  </div>
</template>
<script>
  export default {
    name: 'SignList',
  };
</script>
<script setup>
  import { reactive, onMounted,computed,watch } from 'vue';
  import { useRouter } from 'vue-router';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { Switch } from 'ant-design-vue';
  import { useUserStore } from '/@/store/modules/user';
  import {
    getSignActivityListApi,
    getSignActivityIsRemindApi,
    getSignActivityIsOpenApi,
    getSignActivityDelApi,
  } from '/@/api/sign/sign';
  import { failTip, sucTip } from '/@/utils/utils';

  const props = defineProps({
    activeKey: { type: String }, 
  });

  const activeKey = computed(() => {
    return props.activeKey;
  });

  
  watch(
    activeKey,
    () => {
     if(activeKey.value=='1'){
      redoHeight()
     }
    },
  );

  const router = useRouter();

  const userStore = useUserStore();

  const columns = reactive({
    data: [
      {
        title: `开始时间`,
        dataIndex: 'startTime',
        align: 'center',
        width: 100,
      },
      {
        title: `结束时间`,
        dataIndex: 'endTime',
        align: 'center',
        width: 100,
      },
      {
        title: `日签奖励`,
        dataIndex: 'integralPerSign',
        align: 'center',
        width: 100,
        customRender: ({ text }) => {
          return text ? text + '积分' : '-';
        },
      },
      {
        title: `连签奖励`,
        dataIndex: 'continueNum',
        align: 'center',
        width: 150,
        customRender: ({ record, text }) => {
          let res = '';
          if (text) {
            res += `${'连签'}${text}${'次,奖励'}`;
            if (record.bonusIntegral) {
              res += `${record.bonusIntegral}${'积分'},`;
            }
            if (record.bonusVoucher) {
              res += `${'一张'}${record.bonusVoucherName}`;
            }
          } else {
            res = '--';
          }
          return res;
        },
      },
      {
        title: `活动状态`,
        dataIndex: 'stateValue',
        align: 'center',
        width: 150,
      },
      {
        title: `是否提醒`,
        dataIndex: 'isRemind',
        align: 'center',
        width: 150,
      },
      {
        title: `启用状态`,
        dataIndex: 'state',
        align: 'center',
        width: 100,
      },
    ],
  });

  const searchFormSchema = reactive({
    data: [
      {
        colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
        component: 'RangePicker',
        label: `活动时间`,
        field: '[startTime,endTime]',
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: ['开始时间', '结束时间'],
        },
      },
    ],
  });

  // 表格数据
  const [registerTable, { reload,redoHeight }] = useTable({
    // 请求接口
    api: getSignActivityListApi,
    // 表头
    columns: columns.data,
    immediate: true,
    searchInfo: {
      systemType: 'seller',
    },
    // 点击搜索前处理的参数
    beforeFetch(values) {
      values.startTime = values.startTime
        ? values.startTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
      return values;
    },
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: searchFormSchema.data,
    },
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    ellipsis: false,
    // 是否显示序号列
    showIndexColumn: true,
    // 表格右侧操作列配置
    actionColumn: {
      width: 130,
      title: '操作',
      dataIndex: 'action',
      // slots: { customRender: 'action' },
    },
  });

  //操作  type: state 是否启用榜单
  const operate = async (id, type) => {
    let param_data = {};
    let res = null;
    if (type == 'state') {
      param_data = id;
      res = await getSignActivityIsOpenApi(param_data);
    } else if (type == 'isRemind') {
      param_data = id;
      res = await getSignActivityIsRemindApi(param_data);
    } else if (type == 'del') {
      param_data = id;
      res = await getSignActivityDelApi(param_data);
    }
    if (res.state == 200) {
      sucTip(res.msg);
      reload();
    } else {
      failTip(res.msg);
    }
  };

  const view = (id, type,pathName) => {
    userStore.setDelKeepAlive([pathName])
    if (type == 'add') {
      router.push({
        path: `/marketing_promotion/sign_to_${type}`,
      });
    } else {
      router.push({
        path: `/marketing_promotion/sign_to_${type}`,
        query: {
          id: id,
          tar: type,
        },
      });
    }
  };

  onMounted(() => {});
</script>
<style lang="less" scoped>
  p {
    margin-bottom: 0;
  }

  .sign_lists {
    .voucher_num {
      color: @primary-color;
      cursor: pointer;
    }
  }
  // .coupon_home_system_lists {
  // }
</style>
