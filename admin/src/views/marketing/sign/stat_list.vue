<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader title="签到管理" />
      <Tabs type="card" v-model:activeKey="activeKey">
        <TabPane key="1" tab="签到活动">
          <SignList :activeKey="activeKey" class="section_padding_tab_top"/>
        </TabPane>
        <TabPane key="2" tab="活动签到统计">
          <ActivityStat :activeKey="activeKey" class="section_padding_tab_top"/>
        </TabPane>
        <TabPane key="3" tab="用户签到统计">
          <MemberStat :activeKey="activeKey" class="section_padding_tab_top"></MemberStat>
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'SignGroup',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { Tabs, TabPane, message, RadioGroup, RadioButton } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import SignList from './list.vue';
  import ActivityStat from './activity_stat.vue';
  import MemberStat from './member_stat.vue';

  const activeKey = ref('1');

  const RadioGroupValue = ref('1');

  onMounted(() => {});
</script>
<style lang="less">
  .RadioGroup_back {
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within {
      box-shadow: none;
    }
  }
</style>
