<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader title="会员签到明细" :back="true" />
      <div class="com_line" style="height: 1px"></div>
      <BasicTable @register="registerTable" rowKey="logId"> </BasicTable>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'SignToMemberDetail',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { useRoute } from 'vue-router';
  import { getSignStatisticsBonusListApi } from '/@/api/sign/sign';

  const route = useRoute();

  // 参数
  const id = ref(route.query?.id);

  const searchInfo = ref({
    memberId: id.value,
  });
  const columns = reactive({
    data: [
      {
        title: `签到时间`,
        dataIndex: 'signTime',
        align: 'center',
        width: 180,
      },
      {
        title: `签到类型`,
        dataIndex: 'signTypeValue',
        align: 'center',
        width: 180,
      },
      {
        title: `获取奖励类型`,
        dataIndex: 'bonusTypeValue',
        align: 'center',
        width: 100,
        customRender: ({ text, record }) => {
          return text ? text : '--';
        },
      },
      {
        title: `获取奖励值`,
        dataIndex: 'bonusIntegral',
        align: 'center',
        width: 100,
        customRender: ({ text, record }) => {
          let res = '';
          if (text) {
            res += `${text}积分`;
          }
          if (record.bonusVoucherName) {
            if (res) {
              res += '+';
            }
            res += `${record.bonusVoucherName}`;
          }
          return res;
        },
      },
    ],
  });

  const searchFormSchema = reactive({
    data: [
      {
        component: 'Select',
        label: `签到类型`,
        field: 'signType',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请选择签到类型`,
          options: [
            { value: '', label: `全部` },
            { value: '0', label: `每日签到` },
            { value: '1', label: `连续签到` },
          ],
        },
      },
      {
        colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
        component: 'RangePicker',
        label: `签到时间`,
        field: '[startTime,endTime]',
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: ['开始时间', '结束时间'],
        },
      },
    ],
  });

  // 表格数据
  const [registerTable] = useTable({
    // 请求接口
    api: getSignStatisticsBonusListApi,
    // 表头
    columns: columns.data,
    immediate: true,
    // 参数
    searchInfo: searchInfo.value,
    // 点击搜索前处理的参数
    beforeFetch(values) {
      values.startTime = values.startTime
        ? values.startTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
      return values;
    },
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: searchFormSchema.data,
    },
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    ellipsis: false,
    // 是否显示序号列
    showIndexColumn: true,
  });
  onMounted(() => {});
</script>
<style lang="less" scoped>
  p {
    margin-bottom: 0;
  }
</style>
