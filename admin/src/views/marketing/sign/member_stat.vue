<template>
  <div class="sign_member_stat">
    <BasicTable @register="registerTable" rowKey="couponId" style="padding: 0;">
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                label: '查看',
                onClick: view.bind(null, record.memberId),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
  </div>
</template>
<script>
  export default {
    name: 'MemberStat',
  };
</script>
<script setup>
  import { ref, unref, reactive, onMounted,computed,watch } from 'vue';
  import { useRouter } from 'vue-router';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { validatorEmoji } from '/@/utils/validate';
  import { getSignStatisticsMemberListApi } from '/@/api/sign/sign';

  const router = useRouter();

  const props = defineProps({
    activeKey: { type: String }, 
  });

  const activeKey = computed(() => {
    return props.activeKey;
  });

  
  watch(
    activeKey,
    () => {
     if(activeKey.value=='3'){
      redoHeight()
     }
    },
  );

  const columns = reactive({
    data: [
      {
        title: `会员名称`,
        dataIndex: 'memberName',
        align: 'center',
        width: 100,
      },
      {
        title: `第一次签到`,
        dataIndex: 'firstSignTime',
        align: 'center',
        width: 100,
      },
      {
        title: `最近一次签到`,
        dataIndex: 'lastSignTime',
        align: 'center',
        width: 100,
      },
      {
        title: `签到总次数`,
        dataIndex: 'signTotal',
        align: 'center',
        width: 150,
      },
    ],
  });

  // 表格数据
  const [registerTable, { redoHeight}] = useTable({
    // 请求接口
    api: getSignStatisticsMemberListApi,
    // 表头
    columns: columns.data,
    immediate: true,
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: [
        {
          component: 'Input',
          label: `会员名称`,
          field: 'memberName',
          colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
          componentProps: {
            placeholder: `请输入会员名称`,
          },
          rules: [
            {
              // @ts-ignore
              validator: async (rule, value) => {
                await validatorEmoji(rule, value);
              },
              trigger: 'change',
            },
          ],
        },
      ],
    },
    // 开启搜索框
    useSearchForm: true,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    ellipsis: false,
    // 是否显示序号列
    showIndexColumn: true,
    // 表格右侧操作列配置
    actionColumn: {
      width: 130,
      title: '操作',
      dataIndex: 'action',
      // slots: { customRender: 'action' },
    },
  });

  const view = (id) => {
    router.push({
      path: `/marketing_promotion/sign_to_member_detail`,
      query: { id: id },
    });
  };

  onMounted(() => {});
</script>
<style lang="less" scoped>
  p {
    margin-bottom: 0;
  }

  .sign_member_stat {
    .voucher_num {
      color: @primary-color;
      cursor: pointer;
    }
  }
  // .coupon_home_system_lists {
  // }
</style>
