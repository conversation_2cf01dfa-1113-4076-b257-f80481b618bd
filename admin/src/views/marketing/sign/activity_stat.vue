<template>
  <div class="sign_activity_stat">
    <BasicTable @register="registerTable" rowKey="signActivityId" style="padding: 0;">
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                label: '查看',
                onClick: view.bind(null, record.signActivityId),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
  </div>
</template>
<script>
  export default {
    name: 'ActivityStat',
  };
</script>
<script setup>
  import { ref, unref, reactive, onMounted,computed ,watch} from 'vue';
  import { useRouter } from 'vue-router';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { getSignStatisticsActListApi } from '/@/api/sign/sign';

  const router = useRouter();

  const props = defineProps({
    activeKey: { type: String }, 
  });

  const activeKey = computed(() => {
    return props.activeKey;
  });

  
  watch(
    activeKey,
    () => {
     if(activeKey.value=='2'){
      redoHeight()
     }
    },
  );

  const columns = reactive({
    data: [
      {
        title: `活动周期`,
        dataIndex: 'startTime',
        align: 'center',
        width: 100,
        customRender: ({ text, record }) => {
          return text + ' ~ ' + record.endTime;
        },
      },
      {
        title: `签到用户数`,
        dataIndex: 'memberNum',
        helpMessage: ['该活动周期内签到的总用户数'],
        align: 'center',
        width: 100,
      },
      {
        title: `总签到次数`,
        dataIndex: 'totalSign',
        align: 'center',
        width: 100,
      },
      {
        title: `新签到用户数`,
        helpMessage: ['在该活动周期内第一次签到且之前从未签到过的总用户数'],
        dataIndex: 'newMemberNum',
        align: 'center',
        width: 150,
      },
      {
        title: `新用户占比`,
        helpMessage: ['新签到用户数/签到用户数'],
        dataIndex: 'newMemberRate',
        align: 'center',
        width: 150,
      },
    ],
  });

  // 表格数据
  const [registerTable,{redoHeight}] = useTable({
    // 请求接口
    api: getSignStatisticsActListApi,
    // 表头
    columns: columns.data,
    immediate: true,
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: false,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    ellipsis: false,
    // 是否显示序号列
    showIndexColumn: true,
    // 表格右侧操作列配置
    actionColumn: {
      width: 130,
      title: '操作',
      dataIndex: 'action',
      // slots: { customRender: 'action' },
    },
  });

  const view = (id) => {
    router.push({
      path: `/marketing_promotion/sign_to_activity_detail`,
      query: { id: id },
    });
  };

  onMounted(() => {});
</script>
<style lang="less">
  p {
    margin-bottom: 0;
  }

  .sign_activity_stat {
    .voucher_num {
      color: @primary-color;
      cursor: pointer;
    }

    .vben-basic-table-header-cell__help {
      vertical-align: -1px;
    }
  }
  // .coupon_home_system_lists {
  // }
</style>
