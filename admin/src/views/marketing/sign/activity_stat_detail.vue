<template>
  <div class="section_padding sign_activity_detail">
    <div class="section_padding_back">
      <SldComHeader title="活动签到明细" :back="true" />
      <div class="com_line" style="height: 1px"></div>
      <BasicTable @register="registerTable" rowKey="logId">
        <template #tableTitle>
          <div class="sign_activity_stat flex_row_between_start">
            <div
              class="item flex_row_start_center"
              v-for="(item, index) in statPart"
              :key="index"
              :style="{ background: item.right_color, width: '24%' }"
            >
              <div class="left flex_row_center_center" :style="{ background: item.left_color }">
                <img :src="item.left_icon" />
              </div>
              <div class="right flex_column_center_start" style="padding-left: 10%">
                <span class="num" style="font-size: xx-large">{{ item.value }}</span>
                <span class="tip">{{ item.right_tip }}</span>
              </div>
            </div>
          </div>
        </template>
      </BasicTable>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'SignToActivityDetail',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { useRoute } from 'vue-router';
  import { validatorEmoji } from '/@/utils/validate';
  import { getSignStatisticsBonusListApi, getSignStatisticsActDetailApi } from '/@/api/sign/sign';
  import member_sign_num from '/@/assets/images/sign/member_sign_num.png';
  import total_sign_num from '/@/assets/images/sign/total_sign_num.png';
  import new_member_num from '/@/assets/images/sign/new_member_num.png';
  import new_member_rate from '/@/assets/images/sign/new_member_rate.png';
  import { failTip } from '/@/utils/utils';

  const route = useRoute();

  // 参数
  const id = ref(route.query?.id);

  // 中间部分统计数据
  const statPart = ref([
    {
      name: 'memberNum',
      left_color: '@primary-color',
      right_color: '#FFBE92',
      left_icon: member_sign_num,
      right_tip: '签到用户数',
      value: '',
    },
    {
      name: 'totalSign',
      left_color: '#5274F1',
      right_color: '#889EFF',
      left_icon: total_sign_num,
      right_tip: '总签到次数',
      value: '',
    },
    {
      name: 'newMemberNum',
      left_color: '#FFBB4F',
      right_color: '#FFD188',
      left_icon: new_member_num,
      right_tip: '新签到人数',
      value: '',
    },
    {
      name: 'newMemberRate',
      left_color: '#77779D',
      right_color: 'rgba(119, 119, 157, 0.5)',
      left_icon: new_member_rate,
      right_tip: '新用户占比',
      value: '',
    },
  ]);

  const paddingLeft = ref(0); //页面统计模块每个item右侧的内边距
  const itemWidth = ref(0); //页面统计模块每个item的宽度
  const fontSizeNum = ref(0); //页面统计模块每个item的数据
  const fontSizeTip = ref(0); //页面统计模块每个item的文字提示

  // 搜索参数
  const searchInfo = ref({
    signActivityId: id.value,
  });

  const columns = reactive({
    data: [
      {
        title: `会员名称`,
        dataIndex: 'memberName',
        align: 'center',
        width: 180,
      },
      {
        title: `签到时间`,
        dataIndex: 'signTime',
        align: 'center',
        width: 180,
      },
      {
        title: `签到类型`,
        dataIndex: 'signTypeValue',
        align: 'center',
        width: 180,
      },
      {
        title: `获取奖励类型`,
        dataIndex: 'bonusTypeValue',
        align: 'center',
        width: 100,
        customRender: ({ text, record }) => {
          return text ? text : '--';
        },
      },
      {
        title: `获取奖励值`,
        dataIndex: 'bonusIntegral',
        align: 'center',
        width: 100,
        customRender: ({ text, record }) => {
          let res = '';
          if (text) {
            res += `${text}积分`;
          }
          if (record.bonusVoucherName) {
            if (res) {
              res += '+';
            }
            res += `${record.bonusVoucherName}`;
          }
          return res;
        },
      },
    ],
  });

  const searchFormSchema = reactive({
    data: [
      {
        field: 'memberName',
        component: 'Input',
        colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
        componentProps: {
          minWidth: 300,
          placeholder: '请输入会员名称',
          size: 'default',
        },
        label: '会员名称',
        labelWidth: 70,
        rules: [
          {
            // @ts-ignore
            validator: async (rule, value) => {
              await validatorEmoji(rule, value);
            },
            trigger: 'change',
          },
        ],
      },
      {
        component: 'Select',
        label: `签到类型`,
        field: 'signType',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请选择签到类型`,
          options: [
            { value: '', label: `全部` },
            { value: '0', label: `每日签到` },
            { value: '1', label: `连续签到` },
          ],
        },
      },
      {
        colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
        component: 'RangePicker',
        label: `签到时间`,
        field: '[startTime,endTime]',
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: ['开始时间', '结束时间'],
        },
      },
    ],
  });

  // 表格数据
  const [registerTable] = useTable({
    // 请求接口
    api: getSignStatisticsBonusListApi,
    // 表头
    columns: columns.data,
    immediate: true,
    // 参数
    searchInfo: searchInfo.value,
    // 点击搜索前处理的参数
    beforeFetch(values) {
      values.startTime = values.startTime
        ? values.startTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
      return values;
    },
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: searchFormSchema.data,
    },
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    ellipsis: false,
    // 是否显示序号列
    showIndexColumn: true,
  });

  const get_detail = async () => {
    try {
      let res = await getSignStatisticsActDetailApi({ signActivityId: route.query?.id });
      if (res.state == 200) {
        statPart.value.forEach((item) => {
          item.value = res.data[item.name];
        });
      } else {
        failTip(res.msg);
      }
    } catch (error) {}
  };
  onMounted(() => {
    get_detail();
  });
</script>
<style lang="less" scoped>
  .sign_activity_detail {
    .sign_activity_stat {
      width: 100%;
      margin-top: 10px;
      margin-bottom: 20px;
      padding: 0 10px;

      .item {
        max-width: 363px;
        height: 110px;
        overflow: hidden;
        border-radius: 10px;
        box-shadow: 0 1px 6px 0 rgb(0 0 0 / 10%);

        .left {
          width: 110px;
          height: 110px;

          img {
            width: 64px;
            height: 64px;
          }
        }

        .right {
          padding-left: 20px;

          .num {
            display: block;
            height: 30px;
            color: #fff;
            font-family: 'Microsoft YaHei';
            font-size: 36px;
            font-weight: bold;
            line-height: 30px;
          }

          .tip {
            display: block;
            height: 20px;
            margin-top: 15px;
            color: #fff;
            font-family: 'Microsoft YaHei';
            font-size: 20px;
            font-weight: 400;
            line-height: 30px;
          }
        }
      }
    }
  }

  p {
    margin-bottom: 0;
  }
</style>
