<template>
  <div class="seckill_label_lists section_padding_tab_top">
    <BasicTable @register="standardTable" style="padding: 0">
      <template #tableTitle>
        <div class="toolbar">
          <div class="toolbar_btn" @click="handleClick(null, 'add')">
            <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#fff" />
            <span>新增标签</span>
          </div>
        </div>
      </template>
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex == 'isShow'">
          <div>
            <Switch
              @change="(checked) => handleClick(record, 'switch', checked ? 1 : 0)"
              :checked="text == 1 ? true : false"
            />
          </div>
        </template>
        <template v-if="column.key == 'action'">
          <template v-if="record.isInner == 1"> -- </template>
          <template v-else>
            <TableAction
              :actions="[
                {
                  label: '编辑',
                  onClick: handleClick.bind(null, record, 'edit'),
                },
                {
                  label: '删除',
                  popConfirm: {
                    title: '删除后不可恢复，是否确定删除？',
                    placement: 'left',
                    confirm: handleClick.bind(null, record, 'del'),
                  },
                },
              ]"
            />
          </template>
        </template>
      </template>
    </BasicTable>
  </div>
  <SldModal
    :width="width"
    :title="title"
    :visible="visible"
    :content="content"
    :confirmBtnLoading="confirmBtnLoading"
    :showFoot="true"
    @cancle-event="handleCancle"
    @confirm-event="handleConfirm"
  />
</template>
<script setup>
  import { ref } from 'vue';
  import SldModal from '@/components/SldModal/index.vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { Popconfirm, Switch } from 'ant-design-vue';
  import {
    getSeckillLabelListApi,
    getSeckillLabelAddApi,
    getSeckillLabelEditApi,
    getSeckillLabelDelApi,
    getSeckilLabelSwitchApi,
  } from '/@/api/promotion/seckill';
  import { validatorEmoji } from '/@/utils/validate';
  import { failTip, sucTip } from '/@/utils/utils';

  const [standardTable, { reload }] = useTable({
    api: (arg) => getSeckillLabelListApi({ ...arg }),
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    columns: [
      {
        title: '标签名称',
        dataIndex: 'labelName',
        width: 120,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: '排序',
        dataIndex: 'sort',
        width: 100,
        customRender: ({ text }) => {
          return text||text==0 ? text : '--';
        },
      },
      {
        title: '启用状态',
        dataIndex: 'isShow',
        width: 60,
      },
    ],
    actionColumn: {
      width: 80,
      title: '操作',
      dataIndex: 'action',
    },
    useSearchForm: true,
    formConfig: {
      schemas: [
        {
          field: 'labelName',
          component: 'Input',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            placeholder: '请输入标签名称',
            size: 'default',
          },
          label: '标签名称',
          labelWidth: 70,
        },
      ],
    },
    bordered: true,
    striped: false,
    showIndexColumn: true,
  });
  const width = ref(500);
  const title = ref('');
  const visible = ref(false);
  const content = ref([]);
  const confirmBtnLoading = ref(false);
  const operate_id = ref('');
  const operate_type = ref('');

  const operate_add_edit = ref([
    {
      type: 'input',
      label: `标签名称`,
      name: 'labelName',
      placeholder: `请输入标签名称`,
      extra: '最多输入6个字',
      maxLength: 6,
      showCount: true,
      rules: [
        {
          required: true,
          whitespace: true,
          message: '请输入标签名称',
        },
        {
          validator: async (rule, value) => {
            await validatorEmoji(rule, value);
          },
        },
      ],
    },
    {
      type: 'inputnum',
      label: `排序`,
      name: 'sort',
      placeholder: `请输入排序`,
      extra: '请输入0~255的数字，值越小显示越靠前',
      min: 0,
      max: 255,
      rules: [
        {
          required: true,
          message: `请输入排序`,
        },
      ],
    },
  ]); //添加、编辑商品标签弹窗数据

  //表格点击回调事件
  function handleClick(item, type, check) {
    operate_type.value = type;
    if (item) {
      operate_id.value = item.labelId;
    }
    if (type == 'add' || type == 'edit') {
      content.value.forEach((item) => {
        item.initialValue = '';
      });
      content.value = [];
      operate_add_edit.value.forEach((item) => {
        item.initialValue = '';
      });
    }
    if (type == 'add') {
      content.value = [];
      title.value = '添加秒杀标签';
      visible.value = true;
      content.value = operate_add_edit.value;
    } else if (type == 'edit') {
      title.value = '编辑秒杀标签';
      visible.value = true;
      let data = operate_add_edit.value;
      data.map((items) => {
        items.initialValue = item[items.name]||item[items.name]==0 ? item[items.name] : undefined;
      });
      content.value = data;
    } else if (type == 'del') {
      operate_role({ labelIds: item.labelId });
    } else if (type == 'switch') {
      operate_role({ labelId: item.labelId, isShow: check });
    }
  }

  //弹窗取消事件
  function handleCancle() {
    visible.value = false;
    content.value = [];
    operate_id.value = '';
  }

  //弹窗确认事件
  function handleConfirm(val) {
    if (operate_type.value == 'edit') {
      val.labelId = operate_id.value;
    }
    operate_role(val);
  }

  //商品标签操作
  const operate_role = async (params) => {
    confirmBtnLoading.value = true;
    let res = {};
    if (operate_type.value == 'add') {
      res = await getSeckillLabelAddApi(params);
    } else if (operate_type.value == 'edit') {
      res = await getSeckillLabelEditApi(params);
    } else if (operate_type.value == 'del') {
      res = await getSeckillLabelDelApi(params);
    } else if (operate_type.value == 'switch') {
      res = await getSeckilLabelSwitchApi(params);
    }
    confirmBtnLoading.value = false;
    if (res.state == 200) {
      sucTip(res.msg);
      handleCancle();
      reload();
    } else {
      failTip(res.msg);
    }
  };
</script>
<style lang="less" scoped></style>
