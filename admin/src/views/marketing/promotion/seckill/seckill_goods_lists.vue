<template>
  <div class="section_padding seckill_goods_lists">
    <div class="section_padding_back">
      <SldComHeader title="活动商品" :back="true" />
      <BasicTable @register="registerTable" rowKey="couponId">
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex === 'goodsImage'">
            <div class="flex_com_row_center">
              <div class="flex_com_row_center goods_img_wrap_80">
                <img :src="text" alt="" />
              </div>
            </div>
          </template>
          <template v-if="column.dataIndex === 'action'">
            <TableAction
              :actions="[
                {
                  onClick: viewSpec.bind(null, record),
                  label: '查看SKU',
                },
              ]"
            />
          </template>
        </template>
      </BasicTable>
    </div>
    <SldModal
      :width="900"
      title="商品SKU"
      :visible="modalVisible"
      :height="300"
      :content="operateContent"
      :showFoot="false"
      @cancle-event="handleModalCancle"
    />
  </div>
</template>
<script>
  export default {
    name: 'SeckillGoodsList',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { useRoute } from 'vue-router';
  import { getSeckilGoodsListApi, getSeckilGoodsProductListApi } from '/@/api/promotion/seckill';
  import SldModal from '/@/components/SldModal/index.vue';
  import { validatorEmoji } from '/@/utils/validate';
  import { failTip, list_com_page_more } from '/@/utils/utils';

  const route = useRoute();

  // 参数
  const id = ref(route.query?.id);

  const modalVisible = ref(false);

  const operateContent = ref([
    {
      type: 'show_content_table', //展示表格，没有分页，不能勾选
      name: 'bind_goods',
      width: 880,
      wrapperCol: { flex: 1, style: 'margin-bottom:-19px' },
      label: ``,
      content: '',
      data: [],
      scrollHeight: false, // 设置纵向滚动，也可用于指定滚动区域的高，可以设置为像素值
      columns: [
        {
          title: ' ',
          align: 'center',
          width: 100,
          minWidth: 100,
          customRender: ({ text, record, index }) => {
            return `${index + 1}`;
          },
        },
        {
          title: `商品规格`,
          dataIndex: 'specValues',
          align: 'center',
          width: 200,
          customRender: ({ text }) => {
            return text ? text : '默认';
          },
          minWidth: 200,
        },
        {
          title: `原价(元)`,
          dataIndex: 'productPrice',
          align: 'center',
          width: 200,
          minWidth: 200,
        },
        {
          title: `商品库存`,
          dataIndex: 'productStock',
          align: 'center',
          width: 200,
          minWidth: 200,
        },
        {
          title: `秒杀价(元)`,
          dataIndex: 'seckillPrice',
          align: 'center',
          width: 200,
          minWidth: 200,
        },
        {
          title: `秒杀库存`,
          dataIndex: 'seckillStock',
          align: 'center',
          width: 200,
          minWidth: 200,
        },
        {
          title: `限购数量`,
          dataIndex: 'upperLimit',
          align: 'center',
          width: 200,
          minWidth: 200,
        },
      ],
      rowKey: 'productId',
      scroll: false,
    },
  ]);

  const searchInfo = ref({
    stageId: id.value,
  });
  const columns = reactive({
    data: [
      {
        title: `商品图片`,
        dataIndex: 'goodsImage',
        align: 'center',
        width: 180,
      },
      {
        title: `商品名称`,
        dataIndex: 'goodsName',
        align: 'center',
        width: 100,
      },
      {
        title: `店铺名称`,
        dataIndex: 'storeName',
        align: 'center',
        width: 100,
      },
      {
        title: `活动标签`,
        dataIndex: 'labelName',
        align: 'center',
        width: 100,
      },
    ],
  });

  const searchFormSchema = reactive({
    data: [
      {
        component: 'Input',
        label: `店铺名称`,
        field: 'storeName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请输入店铺名称`,
        },
        rules: [
          {
            validator: async (rule, value) => {
              await validatorEmoji(rule, value);
            },
          },
        ],
      },
      {
        component: 'Input',
        label: `商品名称`,
        field: 'goodsName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请输入商品名称`,
        },
        rules: [
          {
            validator: async (rule, value) => {
              await validatorEmoji(rule, value);
            },
          },
        ],
      },
    ],
  });

  // 表格数据
  const [registerTable] = useTable({
    // 请求接口
    api: getSeckilGoodsListApi,
    // 表头
    columns: columns.data,
    immediate: true,
    rowKey: 'goodsId',
    // 参数
    searchInfo: searchInfo.value,
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: searchFormSchema.data,
    },
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    ellipsis: false,
    // 是否显示序号列
    showIndexColumn: true,
    // 表格右侧操作列配置
    actionColumn: {
      width: 160,
      title: '操作',
      dataIndex: 'action',
      // slots: { customRender: 'action' },
    },
  });

  // 查看sku
  const viewSpec = async (record) => {
    try {
      let res = await getSeckilGoodsProductListApi({
        stageId: route.query?.id,
        goodsId: record.goodsId,
        pageSize: list_com_page_more,
      });
      if (res.state == 200) {
        operateContent.value[0].data = [];
        operateContent.value[0].data = res.data.list;
        modalVisible.value = true;
      } else {
        failTip(res.msg);
      }
    } catch (error) {
      console.log(error, 'error');
    }
  };

  //关闭弹框
  const handleModalCancle = () => {
    modalVisible.value = false;
    operateContent.value[0].data = [];
  };
  onMounted(() => {});
</script>
<style lang="less" scoped>
  p {
    margin-bottom: 0;
  }
</style>
