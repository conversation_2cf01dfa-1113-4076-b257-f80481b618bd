<template>
  <div class="seckill_label_lists">
    <BasicTable @register="standardTable">
      <template #tableTitle>
        <div class="toolbar">
          <Popconfirm
            v-if="selectedRowKeys.length > 0"
            title="确认审核通过选中的商品吗？"
            @confirm="operateGoods(selectedRowKeys.join(','))"
          >
            <div class="toolbar_btn">
              <AliSvgIcon
                iconName="iconshenhetongguo1"
                width="15px"
                height="15px"
                fillColor="#0fb39a"
              />
              <span>审核通过</span>
            </div>
          </Popconfirm>
          <div class="toolbar_btn" v-else @click="operateGoods(selectedRowKeys.join(','))">
            <AliSvgIcon
              iconName="iconshenhetongguo1"
              width="15px"
              height="15px"
              fillColor="#0fb39a"
            />
            <span>审核通过</span>
          </div>
          <div class="toolbar_btn" @click="handleClick(null, 'lockOffs')">
            <AliSvgIcon iconName="iconshenhejujue1" width="15px" height="15px" fillColor="#fa0920" />
            <span>审核拒绝</span>
          </div>
        </div>
      </template>
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'goodsImage'">
            <div class="flex_com_row_center">
              <div class="flex_com_row_center goods_img_wrap_80">
                <img :src="text" alt="" />
              </div>
            </div>
          </template>
        <template v-if="column.dataIndex == 'action'">
          <TableAction
              :actions="[
                {
                  onClick: viewSpec.bind(null, record),
                  label: '查看SKU',
                },
                {
                  label: '审核通过',
                  ifShow: record.verifyState != 3,
                  popConfirm: {
                    title: '确定审核通过该商品吗',
                    placement: 'left',
                    confirm: operate_role.bind(null, {
                    goodsIds: record.goodsId,
                    stageIds: record.stageId,
                    state: 1,
                  }, 'pass'),
                  },
                },
                {
                  onClick: handleClick.bind(null, record, 'lockOff'),
                  label: '审核拒绝',
                  ifShow: record.verifyState != 3,
                },
                {
                  label: '删除',
                  ifShow: record.verifyState == 3,
                  popConfirm: {
                    title: '删除后不可恢复，是否确定删除？',
                    placement: 'left',
                    confirm: operate_role.bind(null, record, 'del'),
                  },
                },
              ]"
            />
        </template>
      </template>
    </BasicTable>
    <SldModal
      :width="width"
      :title="title"
      :visible="modalVisible"
      :height="300"
      :content="operateContent"
      :showFoot="showFoot"
      :confirmBtnLoading="confirmBtnLoading"
      @cancle-event="handleModalCancle"
      @confirm-event="handleConfirm"
    />
  </div>
</template>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { validatorEmoji } from '/@/utils/validate';
  import { Popover, Popconfirm } from 'ant-design-vue';
  import { getSeckilGoodsAuditListApi ,getSeckilGoodsProductListApi,getSeckilGoodsAuditDelApi,getSeckilGoodsAuditApi} from '/@/api/promotion/seckill';
  import { useRouter } from 'vue-router';
  import SldModal from '/@/components/SldModal/index.vue';
  import { failTip, list_com_page_more,sucTip,selectRadio, SelectAll } from '/@/utils/utils';

  const router = useRouter();

  const props = defineProps({
    id: {
      type: String,
    },
  });

  const searchInfo = ref({
    seckillId: props.id,
  });


  const modalVisible = ref(false);
  const showFoot = ref(false);
  const title = ref('商品SKU');
  const width = ref(900);
  const confirmBtnLoading = ref(false);
  const operateContent = ref([])
  const operateContent_table = ref([
    {
      type: 'show_content_table', //展示表格，没有分页，不能勾选
      name: 'bind_goods',
      width: 880,
      wrapperCol: { flex: 1, style: 'margin-bottom:-10px;padding: 0 10px', },
      label: ``,
      content: '',
      data: [],
      scrollHeight: false, // 设置纵向滚动，也可用于指定滚动区域的高，可以设置为像素值
      columns: [
        {
          title: ' ',
          align: 'center',
          width: 100,
          minWidth: 100,
          customRender: ({ text, record, index }) => {
            return `${index + 1}`;
          },
        },
        {
          title: `商品规格`,
          dataIndex: 'specValues',
          align: 'center',
          width: 200,
          customRender: ({ text }) => {
            return text ? text : '默认';
          },
          minWidth: 200,
        },
        {
          title: `原价(元)`,
          dataIndex: 'productPrice',
          align: 'center',
          width: 200,
          minWidth: 200,
        },
        {
          title: `商品库存`,
          dataIndex: 'productStock',
          align: 'center',
          width: 200,
          minWidth: 200,
        },
        {
          title: `秒杀价(元)`,
          dataIndex: 'seckillPrice',
          align: 'center',
          width: 200,
          minWidth: 200,
        },
        {
          title: `秒杀库存`,
          dataIndex: 'seckillStock',
          align: 'center',
          width: 200,
          minWidth: 200,
        },
        {
          title: `限购数量`,
          dataIndex: 'upperLimit',
          align: 'center',
          width: 200,
          minWidth: 200,
        },
      ],
      rowKey: 'productId',
      scroll: false,
    },
  ]);

  const columns = reactive({
    data: [
    {
        title: `商品图片`,
        dataIndex: 'goodsImage',
        align: 'center',
        width: 180,
      },
      {
        title: `商品名称`,
        dataIndex: 'goodsName',
        align: 'center',
        width: 180,
      },
      {
        title: '店铺名称',
        dataIndex: 'storeName',
        width: 150,
      },
      {
        title: '活动标签',
        dataIndex: 'labelName',
        width: 150,
      },
      {
        title: '参加场次',
        dataIndex: 'stageName',
        width: 150,
      },
      {
        title: '审核状态',
        dataIndex: 'verifyStateValue',
        width: 150,
      },
      {
        title: '拒绝理由',
        dataIndex: 'remark',
        width: 150,
      },
     
    ],
  });

  const searchFormSchema = reactive({
    data: [
      {
        field: 'storeName',
        component: 'Input',
        componentProps: {
          placeholder: '请输入店铺名称',
          size: 'default',
        },
        label: '店铺名称',
      },
      {
        field: 'goodsName',
        component: 'Input',
        componentProps: {
          placeholder: '请输入商品名称',
          size: 'default',
        },
        label: '商品名称',
      },
      {
        component: 'Select',
        label: `审核状态`,
        field: 'verifyState',
        labelWidth: 90,
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请选审核状态`,
          options: [
            { value: '0', label: `全部` },
            { value: '1', label: `待审核` },
            { value: '3', label: `审核拒绝` },
          ],
        },
      },
      
    ],
  });

  const operate_lockoff_data = ref([
    {
      type: 'textarea',
      label: `拒绝理由`,
      name: 'auditReason',
      placeholder: `请输入拒绝理由`,
      extra: `最多输入100个字`,
      maxLength: 100,
      rules: [{
        required: true,
        whitespace: true,
        message: `请输入拒绝理由`,
      }],
    },
  ]);

  const operate_type = ref('')
  const operate_item = ref('')
  const selectedRowKeys = ref([]);
  const selectedRows = ref([]);
  const [standardTable,{reload}] = useTable({
    rowKey:'goodsId',
    api: (arg) => getSeckilGoodsAuditListApi({ ...arg }),
    // 参数
    clickToRowSelect:false,
    searchInfo: searchInfo.value,
    immediate: true,
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    columns: columns.data,
    actionColumn: {
      width: 220,
      title: '操作',
      dataIndex: 'action',
    },
    // 是否显示表格边框
    bordered: true,
    ellipsis: false,
    
    useSearchForm: true,
    formConfig: {
      schemas: searchFormSchema.data,
      labelWidth: 75,
    },
    rowSelection: {
      type: 'checkbox',
      selectedRowKeys: selectedRowKeys,
      onSelect: onSelect,
      onSelectAll: onSelectAll,
    },
  });

  // 单选多选事件
  function onSelect(record, selected) {
    let rows = selectRadio(selectedRowKeys.value, selectedRows.value, 'goodsId', record, selected);
    selectedRowKeys.value = rows.selectedRowKeys;
    selectedRows.value = rows.selectedRows;
  }

  // 全选按钮事件
  function onSelectAll(selected, rows, changeRows) {
    let rowAll = SelectAll(
      selectedRowKeys.value,
      selectedRows.value,
      'goodsId',
      selected,
      rows,
      changeRows,
    );
    selectedRowKeys.value = rowAll.selectedRowKeys;
    selectedRows.value = rowAll.selectedRows;
  }

  //表格点击回调事件
  const handleClick = (item, type) => {
    operate_type.value = type;
    if(item){
      operate_item.value = {
        goodsIds: item.goodsId,
        stageIds: item.stageId,
      }
    }else{
      let param = {
        goodsIds:[],
        stageIds:[]
      };
      selectedRows.value.forEach(item=>{
        param.goodsIds.push(item.goodsId);
        param.stageIds.push(item.stageId);
      })
      param.goodsIds = param.goodsIds.join(',')
      param.stageIds = param.stageIds.join(',')
      operate_item.value = param
    }
    width.value = 500
    let data = [];
    if (type == 'lockOffs') {
      if (item == null && selectedRowKeys.value.length == 0) {
        failTip('请先选中数据');
        return;
      }
    }
    data = JSON.parse(JSON.stringify(operate_lockoff_data.value));
    title.value = '审核拒绝理由';
    operateContent.value = data;
    showFoot.value = true
    title.value = '拒绝理由'
    modalVisible.value = true;
  };

   //弹窗确认事件
   const handleConfirm = async (val) => {
    if (operate_type.value == 'lockOff') {
      operate_item.value.auditReason = val.auditReason;
      operate_item.value.state = 0;
    }
    confirmBtnLoading.value = true;
    let res = await getSeckilGoodsAuditApi(operate_item.value);
    if (res.state == 200) {
      sucTip(res.msg);
      handleModalCancle()
      reload();
    } else {
      failTip(res.msg);
      confirmBtnLoading.value = false;
    }
  };

  //商品标签操作
  const operate_role = async (params,type) => {
    confirmBtnLoading.value = true;
    let res = {};
     if (type == 'del') {
      res = await getSeckilGoodsAuditDelApi({goodsId:params.goodsId,stageId:params.stageId});
    }else{
      res = await getSeckilGoodsAuditApi(params);
    }
    confirmBtnLoading.value = false;
    if (res.state == 200) {
      sucTip(res.msg);
      handleModalCancle();
      reload();
    } else {
      failTip(res.msg);
    }
  };

  // 审核通过
  const operateGoods = async (ids) => {
    if (ids.length == 0) {
      failTip('请先选中数据');
      return;
    }
    let obj = []
    selectedRows.value.forEach(item=>{
      obj.push(item.stageId)
    })
    confirmBtnLoading.value = true;
    let res = await getSeckilGoodsAuditApi({ goodsIds: ids, state: 1,stageIds:obj.join(',')});
    if (res.state == 200) {
      sucTip(res.msg);
      modalVisible.value = false;
      confirmBtnLoading.value = false;
      selectedRows.value = [];
      selectedRowKeys.value = [];
      reload();
    } else {
      failTip(res.msg);
      confirmBtnLoading.value = false;
    }
  };

  
  // 查看sku
  const viewSpec = async (record) => {
    try {
      operateContent.value = operateContent_table.value
      let res = await getSeckilGoodsProductListApi({
        stageId: record.stageId,
        goodsId: record.goodsId,
        pageSize: list_com_page_more,
      });
      if (res.state == 200) {
        operateContent.value[0].data = [];
        operateContent.value[0].data = res.data.list;
        modalVisible.value = true;
      } else {
        failTip(res.msg);
      }
    } catch (error) {
      console.log(error, 'error');
    }
  };

  //关闭弹框
  const handleModalCancle = () => {
    modalVisible.value = false;
    operateContent.value = [];
    showFoot.value = false
    operate_item.value = {}
    title.value = '商品SKU'
    width.value = 900
  };
  onMounted(() => {});
</script>
<style lang="less" scoped></style>
