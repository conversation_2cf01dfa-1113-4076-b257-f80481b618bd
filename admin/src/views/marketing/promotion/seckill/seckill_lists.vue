<template>
  <div class="seckill_lists">
    <BasicTable @register="registerTable" rowKey="couponId">
      <template #tableTitle>
        <div class="toolbar">
          <div class="toolbar_btn" @click="addSeckill">
            <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" :fillColor="'#ff9237'" />
            <span style="margin-left: 4px">新增活动</span>
          </div>
        </div>
      </template>
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'startTime'">
          <div class="voucher_time_wrap" :title="text + '~' + record.endTime">
            <span>{{ text }}</span>
            <span>~</span>
            <span>{{ record.endTime }}</span>
          </div>
        </template>
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                label: '设置轮播图',
                ifShow: record.state != 3,
                onClick: addAdv.bind(null, record),
              },
              {
                label: '查看',
                onClick: view.bind(null, record.seckillId),
              },
              {
                label: '删除',
                ifShow: record.state == 3,
                popConfirm: {
                  title: '删除后不可恢复，是否确定删除？',
                  placement: 'left',
                  confirm: operate.bind(null, { seckillId: record.seckillId }, 'del'),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <SldModal
      :width="width"
      :title="title"
      :visible="visible"
      :content="content"
      :confirmBtnLoading="confirmBtnLoading"
      :showFoot="true"
      @cancle-event="handleCancle"
      @confirm-event="handleConfirm"
    />

    <SldMoreImgModal
      :width="1000"
      :title="'设置活动轮播图'"
      :modalTip="modal_tip"
      :content="modalContent"
      ref="sldMoreImgModalRef"
      @confirm="moreImgConfirm"
      client="mobile"
    ></SldMoreImgModal>
  </div>
</template>
<script>
  export default {
    name: 'SeckillList',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import SldMoreImgModal from '@/components/SldMoreImgModal/index.vue';
  import SldModal from '@/components/SldModal/index.vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { validatorEmoji, validatorSpecialString } from '/@/utils/validate';
  import { getSeckillListApi, getSeckillAddApi,getSeckilGoodsGetSeckillApi,getSeckilGoodsSetBannerApi,getSeckilGoodsDeleteApi } from '/@/api/promotion/seckill';
  import moment from 'moment';
  import { useRouter } from 'vue-router';
  import { failTip, sucTip,getSldCopyData } from '/@/utils/utils';

  const router = useRouter();
  const visible = ref(false);
  const width = ref(860);
  const title = ref('');
  const confirmBtnLoading = ref(false);
  const content = ref([]);
  const modal_data = ref([
  {
      imgPath: '',
      imgUrl: '',
      info: {},
      link_type: '',
      link_value: '',
      title: '',
    }, {
      imgPath: '',
      imgUrl: '',
      info: {},
      link_type: '',
      link_value: '',
      title: '',
    }, {
      imgPath: '',
      imgUrl: '',
      info: {},
      link_type: '',
      link_value: '',
      title: '',
    }, {
      imgPath: '',
      imgUrl: '',
      info: {},
      link_type: '',
      link_value: '',
      title: '',
    }, {
      imgPath: '',
      imgUrl: '',
      info: {},
      link_type: '',
      link_value: '',
      title: '',
    }, {
      imgPath: '',
      imgUrl: '',
      info: {},
      link_type: '',
      link_value: '',
      title: '',
    }, {
      imgPath: '',
      imgUrl: '',
      info: {},
      link_type: '',
      link_value: '',
      title: '',
    }, {
      imgPath: '',
      imgUrl: '',
      info: {},
      link_type: '',
      link_value: '',
      title: '',
    }
  ])
  const modalContent = reactive(
    {
    width: 710,
    height: 300,
    show_width: 236,
    show_height: 100,
    data: [
  {
      imgPath: '',
      imgUrl: '',
      info: {},
      link_type: '',
      link_value: '',
      title: '',
    }, {
      imgPath: '',
      imgUrl: '',
      info: {},
      link_type: '',
      link_value: '',
      title: '',
    }, {
      imgPath: '',
      imgUrl: '',
      info: {},
      link_type: '',
      link_value: '',
      title: '',
    }, {
      imgPath: '',
      imgUrl: '',
      info: {},
      link_type: '',
      link_value: '',
      title: '',
    }, {
      imgPath: '',
      imgUrl: '',
      info: {},
      link_type: '',
      link_value: '',
      title: '',
    }, {
      imgPath: '',
      imgUrl: '',
      info: {},
      link_type: '',
      link_value: '',
      title: '',
    }, {
      imgPath: '',
      imgUrl: '',
      info: {},
      link_type: '',
      link_value: '',
      title: '',
    }, {
      imgPath: '',
      imgUrl: '',
      info: {},
      link_type: '',
      link_value: '',
      title: '',
    }
  ]
  })
  const modalTitle = ref('设置开屏图');
  const modal_tip = [
    '最多上传8张图片,每张图片不可以超过1M',
    '请严格根据提示要求上传规定尺寸的图片,图片不可以超过1M,否则影响页面加载效果',
    '编辑项中的“操作”指点击该内容所产生的链接地址，可通过下拉选项选择不同的方式',
  ];

  const operate_add_edit = ref([
    {
      type: 'input',
      label: `活动名称`,
      name: 'seckillName',
      placeholder: `请输入活动名称`,
      extra: '最多输入6个字',
      maxLength: 6,
      showCount: true,
      rules: [
        {
          required: true,
          whitespace: true,
          message: '请输入活动名称',
        },
        {
          validator: async (rule, value) => {
            await validatorEmoji(rule, value);
          },
        },
        {
          validator: async (rule, value) => {
            await validatorSpecialString(rule, value);
          },
        },
      ],
    },
    {
      type: 'seckill_time_select',
      label: '活动场次', //秒杀时间选择
      name: 'stages',
      sel_data: [], //选中的存放值  数组
      placeholder: ``,
      callback: true,
      extra:
        '每场活动时间为本场次整点开始时间到下一场次开始时间，当日设置的最后一场结束时间为当日24:00点',
    },
    {
      type: 'range_picker',
      label: `活动日期`, //开始和结束时间
      name: 'activity_time',
      placeholder: `请选择活动日期`,
      placeholder1: `开始时间`,
      placeholder2: `结束时间`,
      initialValue: [], //默认值  数组格式
      disabledDate: (currentDate) => currentDate && currentDate < moment().subtract(1, 'days'),
      rules: [
        {
          required: true,
          message: `请选择活动日期`,
        },
      ],
    },
  ]);

  const columns = reactive({
    data: [
      {
        title: `活动名称`,
        dataIndex: 'seckillName',
        align: 'center',
        width: 100,
      },
      {
        title: `活动时间`,
        dataIndex: 'startTime',
        align: 'center',
        width: 150,
        customRender: ({ text, record }) => {
          return text + '~' + record.endTime;
        },
      },
      {
        title: `活动状态`,
        dataIndex: 'stateValue',
        align: 'center',
        width: 60,
      },
    ],
  });

  const searchFormSchema = reactive({
    data: [
      {
        component: 'Input',
        label: `活动名称`,
        field: 'seckillName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请输入活动名称`,
        },
        rules: [
          {
            // @ts-ignore
            validator: async (rule, value) => {
              await validatorEmoji(rule, value);
            },
            trigger: 'change',
          },
        ],
      },
      {
        component: 'Select',
        label: `活动状态`,
        field: 'state',
        labelWidth: 90,
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请选择活动状态`,
          options: [
            { value: '', label: `全部` },
            { value: '1', label: `未开始` },
            { value: '2', label: `进行中` },
            { value: '3', label: `已结束` },
          ],
        },
      },
      {
        colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
        component: 'RangePicker',
        label: `活动时间`,
        field: '[startTime,endTime]',
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: ['开始时间', '结束时间'],
        },
      },
    ],
  });

  // 表格数据
  const [registerTable, { reload, getForm }] = useTable({
    // 请求接口
    api: getSeckillListApi,
    // 表头
    columns: columns.data,
    immediate: true,
    // 点击搜索前处理的参数
    beforeFetch(values) {
      values.startTime = values.startTime
        ? values.startTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
      return values;
    },
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: searchFormSchema.data,
    },
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    // 是否显示序号列
    showIndexColumn: true,
    // 表格右侧操作列配置
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
      // slots: { customRender: 'action' },
    },
  });

  const handleCancle = () => {
    visible.value = false;
    title.value = '';
    content.value.forEach((item) => {
      item.initialValue = '';
    });
    content.value = [];
    operate_add_edit.value.forEach((item) => {
      item.initialValue = '';
      if (item.name == 'activity_time') {
        item.sel_data = [];
      }
    });
  };

  const addSeckill = () => {
    content.value.forEach((item) => {
      item.initialValue = '';
    });
    content.value = [];
    operate_add_edit.value.forEach((item) => {
      item.initialValue = '';
      if (item.name == 'activity_time') {
        item.sel_data = [];
      }
    });
    content.value = operate_add_edit.value;
    title.value = '新增秒杀活动';
    visible.value = true;
  };

  const handleConfirm = async (val) => {
    try {
      if (val.stages.length == 0) {
        failTip('请选择活动场次');
        return;
      }
      val.stages = val.stages.join(',');
      if (val.activity_time) {
        val.startTime = val.activity_time[0] ? val.activity_time[0] : '';
        val.endTime = val.activity_time[1] ? val.activity_time[1] : '';
        delete val.activity_time;
      }
      let res = await getSeckillAddApi(val);
      if (res.state == 200) {
        sucTip(res.msg);
        handleCancle();
        reload();
      } else {
        failTip(res.msg);
      }
    } catch (error) {
      console.info(error, 'error');
    }
  };

  //操作  type: invalid 失效
  const operate = async (id, type) => {
    let param_data = {};
    let res = null;
    if (type == 'del') {
      param_data = id;
      res = await getSeckilGoodsDeleteApi(param_data);
    }
    if (res.state == 200) {
      sucTip(res.msg);
      reload();
    } else {
      failTip(res.msg);
    }
  };

  const view = (id) => {
    router.push({
      path: `/marketing_promotion/seckill_detail`,
      query: { id: id },
    });
  };
  const sldMoreImgModalRef = ref();
  const cur_data = ref({})
  const modalState = ref({})
  const addAdv = async(val)=> {
    modalState.value = val
    let res = await getSeckilGoodsGetSeckillApi({seckillId:val.seckillId})
    if (res.state == 200) {
      let adv_data = []
      if (res.data.banner) {
        adv_data = JSON.parse(res.data.banner.replace(/&quot;/g,"\""));
      }
      if(adv_data.length>0){
        modalContent.data = adv_data
      }else {
        modalContent.data = JSON.parse(JSON.stringify(modal_data.value))
      }
      sldMoreImgModalRef.value.setModal(true);
    }else{
      failTip(res.msg)
    }
  }

  const moreImgConfirm = async (dataSource) => {
    const res = await getSeckilGoodsSetBannerApi({
      seckillId: modalState.value.seckillId,
      banner: JSON.stringify(dataSource.parent_data),
    });

    if (res?.state == 200) {
      sucTip(res.msg);
      modalState.value = [];
      sldMoreImgModalRef.value.setModal(false);
      reload();
    } else {
      failTip(res.msg);
    }
  };

  onMounted(() => {});
</script>
<style lang="less" scoped>
  p {
    margin-bottom: 0;
  }

  .seckill_lists {
    .voucher_num {
      color: @primary-color;
      cursor: pointer;
    }
  }
</style>
<style lang="less">
  .full_modal {
    .ant-modal-header {
      padding: 12px 24px !important;
      background-color: @primary-color;
    }

    .ant-modal-title {
      color: #fff;
      font-size: 14px !important;
      line-height: 18px !important;
    }

    .ant-modal-close-x .anticon svg {
      fill: #fff;
    }

    .ant-modal .ant-modal-content {
      border-radius: 4px;
    }

    .ant-form-item-with-help {
      margin-bottom: 24px;
    }

    .ant-form-item-has-error .ant-input-number,
    .ant-form-item-has-error .ant-picker {
      border-color: #ed6f6f !important;
    }

    .ant-modal-close {
      top: 12px;
      right: 20px;
      height: auto !important;

      .ant-modal-close-x {
        display: flex !important;
        align-items: center;
        justify-content: center;
        width: auto !important;
        height: auto !important;
        line-height: normal;
      }
    }
  }
</style>
