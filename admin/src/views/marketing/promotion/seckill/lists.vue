<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader title="秒杀活动" />
      <Tabs type="card" v-model:activeKey="activeKey">
        <TabPane key="1" tab="秒杀活动">
          <SeckillLists class="section_padding_tab_top"/>
        </TabPane>
        <TabPane key="2" tab="秒杀标签">
          <LabelLists class="section_padding_tab_top"></LabelLists>
        </TabPane>
        <TabPane key="3" tab="秒杀设置">
          <Setting class="section_padding_tab_top"></Setting>
        </TabPane>
        <TabPane key="4" tab="风格配置">
          <RadioGroup class="RadioGroup_back section_padding_tab_top" v-model:value="RadioGroupValue">
            <!-- dev_mobile-start -->
            <RadioButton value="1">移动端风格</RadioButton>
            <!-- dev_mobile-end -->
            <!-- dev_pc-start -->
            <RadioButton value="2">PC端风格</RadioButton>
            <!-- dev_pc-end -->
          </RadioGroup>
          <!-- dev_mobile-start -->
          <SeckillDiyStyleM v-if="activeKey == '4' && RadioGroupValue == '1'" />
          <!-- dev_mobile-end -->
          <!-- dev_pc-start -->
          <SeckillDiyStylePc v-if="activeKey == '4' && RadioGroupValue == '2'" />
          <!-- dev_pc-end -->
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'SeckillIndexList',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { Tabs, TabPane, message, RadioGroup, RadioButton } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import SeckillLists from './seckill_lists.vue';
  import LabelLists from './label_lists.vue';
  // dev_mobile-start
  import SeckillDiyStyleM from './diy_style_m.vue';
  // dev_mobile-end
  // dev_pc-start
  import SeckillDiyStylePc from './diy_style_pc.vue';
  // dev_pc-end
  import Setting from './setting.vue';

  const activeKey = ref('1');

  const RadioGroupValue = ref('1');

  onMounted(() => {
    // dev_mobile-start
    RadioGroupValue.value = '1'
    return
    // dev_mobile-end
    // dev_pc-start
    RadioGroupValue.value = '2'
    // dev_pc-end
  });
</script>
<style lang="less">
  .coupon_home {
    padding: 10px;

    .coupon_home_back {
      width: 100%;
      min-height: calc(100vh - @header-height - 20px);
      padding: 10px;
      background-color: #fff;
    }
  }

  .RadioGroup_back {
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within {
      box-shadow: none;
    }
  }
</style>
