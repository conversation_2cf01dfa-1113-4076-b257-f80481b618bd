<template>
  <div class="seckill_label_lists">
    <BasicTable @register="standardTable">
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex == 'action'">
          <TableAction
            :actions="[
              {
                label: '查看商品',
                onClick: view.bind(null, record.stageId),
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
  </div>
</template>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { validatorEmoji } from '/@/utils/validate';
  import { getSeckilStageListApi } from '/@/api/promotion/seckill';
  import { useRouter } from 'vue-router';
  const router = useRouter();

  const props = defineProps({
    id: {
      type: String,
    },
  });

  const searchInfo = ref({
    seckillId: props.id,
  });
  const columns = reactive({
    data: [
      {
        title: `活动场次`,
        dataIndex: 'stageName',
        align: 'center',
        width: 180,
      },
      {
        title: `参加商品数量`,
        dataIndex: 'productCount',
        align: 'center',
        width: 180,
      },
      {
        title: `场次状态`,
        dataIndex: 'stateValue',
        align: 'center',
        width: 100,
        customRender: ({ text, record }) => {
          return text ? text : '--';
        },
      },
    ],
  });

  const searchFormSchema = reactive({
    data: [
      {
        field: 'stageName',
        component: 'Input',
        colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
        componentProps: {
          minWidth: 300,
          placeholder: '请输入活动场次',
          size: 'default',
        },
        label: '活动场次',
        labelWidth: 70,
        rules: [
          {
            validator: async (rule, value) => {
              await validatorEmoji(rule, value);
            },
          },
        ],
      },
      {
        component: 'Select',
        label: `场次状态`,
        field: 'state',
        labelWidth: 90,
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请选择场次状态`,
          options: [
            { value: '0', label: `全部` },
            { value: '1', label: `未开始` },
            { value: '2', label: `进行中` },
            { value: '3', label: `已结束` },
          ],
        },
      },
    ],
  });

  const [standardTable] = useTable({
    api: (arg) => getSeckilStageListApi({ ...arg }),
    // 参数
    searchInfo: searchInfo.value,
    immediate: true,
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    columns: columns.data,
    actionColumn: {
      width: 80,
      title: '操作',
      dataIndex: 'action',
    },
    // 是否显示表格边框
    bordered: true,
    ellipsis: false,
    useSearchForm: true,
    formConfig: {
      schemas: searchFormSchema.data,
      labelWidth: 75,
    },
  });

  const view = (id) => {
    router.push({
      path: `/marketing_promotion/seckill_goods_list`,
      query: { id: id },
    });
  };
  onMounted(() => {});
</script>
<style lang="less" scoped></style>
