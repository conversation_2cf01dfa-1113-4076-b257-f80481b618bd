<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader title="秒杀活动详情" :back="true" />
      <Tabs type="card" v-model:activeKey="activeKey">
        <TabPane key="1" tab="活动场次">
          <div class="section_padding_tab_top"></div>
          <SeckillDetailLists :id="id" />
        </TabPane>
        <TabPane key="2" tab="商品审核">
          <div class="section_padding_tab_top"></div>
          <SeckillCheckGoodsLists :id="id" />
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'SeckillDetail',
  };
</script>
<script setup>
  import { ref, onMounted } from 'vue';
  import { Tabs, TabPane } from 'ant-design-vue';
  import { useRoute } from 'vue-router';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import SeckillDetailLists from './seckill_detail_lists.vue';
  import SeckillCheckGoodsLists from './seckill_check_goods_lists.vue';
  import LabelLists from './label_lists.vue';

  const route = useRoute();

  // 参数
  const id = ref(route.query?.id);

  const activeKey = ref('1');

  onMounted(() => {});
</script>
<style lang="less">
  .RadioGroup_back {
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within {
      box-shadow: none;
    }
  }
</style>
