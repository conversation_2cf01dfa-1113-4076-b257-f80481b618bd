<template>
  <div class="coupon_home_system_lists section_padding_tab_top">
    <BasicTable @register="registerTable" rowKey="couponId">
      <template #tableTitle>
        <div class="toolbar">
          <div class="toolbar_btn" :class="{'toolbar_btn_opacity':enableFlag==0}" @click="()=>enableFlag==0?null:add_coupon('add', null,'AddCoupon')">
            <AliSvgIcon iconName="iconfabu1" width="15px" height="15px" :fillColor="'#FA6F1E'" />
            <span style="margin-left: 4px">新建优惠券</span>
          </div>
        </div>
      </template>
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex == 'publishNum'">
          <router-link :to="`/marketing_promotion/coupon_to_receive_list?id=${record.couponId}`">
            <div class="voucher_num"
              >{{ record.receivedNum }}/{{ record.usedNum }}/{{ record.publishNum }}</div
            >
          </router-link>
        </template>
        <template v-if="column.dataIndex == 'publishStartTime'">
          <div v-if="record.publishType != 3">
            <p>{{ text }}</p>
            <p>~</p>
            <p>{{ record.publishEndTime }}</p>
          </div>
          <div v-else> -- </div>
        </template>
        <template v-if="column.dataIndex == 'effectiveStart'">
          <div v-if="record.cycle">
            {{ `领取后${record.cycle}天内` }}
          </div>
          <div v-else>
            <p>{{ text }}</p>
            <p>~</p>
            <p>{{ record.effectiveEnd }}</p>
          </div>
        </template>
        <template v-if="column.dataIndex == 'isRecommend'">
          <div v-if="record.publishType == 1">
            <Switch
              @change="
                (checked) =>
                  operate(
                    {
                      couponId: record.couponId,
                      isRecommend: checked ? 1 : 0,
                    },
                    'recommend',
                  )
              "
              :checked="text == 1 ? true : false"
            />
          </div>
          <div v-else> -- </div>
        </template>
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                label: '查看',
                onClick: view.bind(null, record.couponId),
              },
              {
                label: '复制',
                onClick: add_coupon.bind(null, 'copy', record.couponId,'CopyCoupon'),
              },
              {
                label: '编辑',
                ifShow: record.state == 1,
                onClick: add_coupon.bind(null, 'edit', record.couponId,'EditCoupon'),
              },
              {
                label: '失效',
                ifShow: record.state == 3,
                popConfirm: {
                  title: '失效后不可恢复，是否确定失效？',
                  placement: 'left',
                  confirm: operate.bind(null, { couponId: record.couponId }, 'invalid'),
                },
              },
              {
                label: '删除',
                ifShow: record.state == 1 || record.state == 2 || record.state == 4,
                popConfirm: {
                  title: '删除后不可恢复，是否确定删除？',
                  placement: 'left',
                  confirm: operate.bind(null, { couponId: record.couponId }, 'del'),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
  </div>
</template>
<script>
  export default {
    name: 'CouponHomeSystem',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { Switch } from 'ant-design-vue';
  import { getSettingListApi } from '/@/api/common/common';
  import { useUserStore } from '/@/store/modules/user';
  import {
    getCouponListApi,
    getCouponIsRecommendApi,
    getCouponInvalidApi,
    getCouponDelApi,
  } from '/@/api/promotion/coupon';
  import { sucTip, failTip } from '/@/utils/utils';
  const userStore = useUserStore();

  const searchInfo = ref({
    systemType: 'admin',
  });

  const router = useRouter();

  const enableFlag = ref(0); //优惠券开关

  const columns = reactive({
    data: [
      {
        title: `优惠券名称`,
        dataIndex: 'couponName',
        align: 'center',
        width: 100,
      },
      {
        title: `优惠券类型`,
        dataIndex: 'couponTypeValue',
        align: 'center',
        width: 100,
      },
      {
        title: `优惠内容`,
        dataIndex: 'couponContent',
        align: 'center',
        width: 100,
      },
      {
        title: `已领取/已使用/发布数`,
        dataIndex: 'publishNum',
        align: 'center',
        width: 150,
      },
      {
        title: `活动时间`,
        dataIndex: 'publishStartTime',
        align: 'center',
        width: 150,
      },
      {
        title: `使用时间`,
        dataIndex: 'effectiveStart',
        align: 'center',
        width: 150,
      },
      {
        title: `获取方式`,
        dataIndex: 'publishTypeValue',
        align: 'center',
        width: 100,
      },
      {
        title: `活动状态`,
        dataIndex: 'stateValue',
        align: 'center',
        width: 100,
      },
      {
        title: `推荐`,
        dataIndex: 'isRecommend',
        align: 'center',
        width: 80,
      },
    ],
  });
  const searchFormSchema = reactive({
    data: [
      {
        component: 'Input',
        label: `优惠券名称`,
        field: 'couponName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请输入优惠券名称`,
        },
      },
      {
        component: 'Select',
        label: `活动状态`,
        field: 'state',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请选择活动状态`,
          options: [
            { value: '', label: `全部` },
            { value: '1', label: `未开始` },
            { value: '2', label: `进行中` },
            { value: '3', label: `已失效` },
            { value: '4', label: `已结束` },
          ],
        },
      },
      {
        component: 'Select',
        label: `优惠券类型`,
        field: 'couponType',
        labelWidth: 90,
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请选择优惠券类型`,
          options: [
            { value: '', label: `全部` },
            { value: '1', label: `满减券` },
            { value: '2', label: `折扣券` },
            { value: '3', label: `随机金额券` },
          ],
        },
      },
      {
        colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
        component: 'RangePicker',
        label: `活动时间`,
        field: '[publishStartTime,publishEndTime]',
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: ['开始时间', '结束时间'],
        },
      },
      {
        component: 'Select',
        label: `获取方式`,
        field: 'publishType',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请选择获取方式`,
          options: [
            { value: '', label: `全部` },
            { value: '1', label: `免费领取` },
            { value: '3', label: `活动赠送` },
          ],
        },
      },
      {
        colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
        component: 'RangePicker',
        label: `使用时间`,
        field: '[effectiveStart,effectiveEnd]',
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: ['开始时间', '结束时间'],
        },
      },
    ],
  });

  // 表格数据
  const [registerTable, { reload }] = useTable({
    // 请求接口
    api: (arg) => getCouponListApi({ ...arg,couponTypeNotEquals:4 }),
    // 表头
    columns: columns.data,
    immediate: true,
    // 参数
    searchInfo: searchInfo.value,
    // 点击搜索前处理的参数
    beforeFetch(values) {
      values.publishStartTime = values.publishStartTime
        ? values.publishStartTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.publishEndTime = values.publishEndTime
        ? values.publishEndTime.split(' ')[0] + ' 23:59:59'
        : undefined;
      values.effectiveStart = values.effectiveStart
        ? values.effectiveStart.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.effectiveEnd = values.effectiveEnd
        ? values.effectiveEnd.split(' ')[0] + ' 23:59:59'
        : undefined;
      return values;
    },
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: searchFormSchema.data,
    },
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    // 是否显示序号列
    showIndexColumn: true,
    // 表格右侧操作列配置
    actionColumn: {
      width: 200,
      title: '操作',
      dataIndex: 'action',
      // slots: { customRender: 'action' },
    },
  });

    //获取站点基本配置
    const getSetting = async () => {
    const res = await getSettingListApi({
      str: 'coupon_is_enable',
    });
    if (res.state == 200 && res.data) {
      enableFlag.value = res.data[0].value;
    } else {
      failTip(res.msg);
    }
  };

  //优惠券操作  type: invalid 失效 copy 复制  del 删除
  const operate = async (id, type) => {
    let param_data = {};
    let res = null;
    if (type == 'recommend') {
      param_data = id;
      res = await getCouponIsRecommendApi(param_data);
    }
    if (type == 'invalid') {
      param_data = id;
      res = await getCouponInvalidApi(param_data);
    }
    if (type == 'del') {
      param_data = id;
      res = await getCouponDelApi(param_data);
    }
    if (res.state == 200) {
      sucTip(res.msg);
      reload();
    } else {
      failTip(res.msg);
    }
  };

  const view = (id) => {
    router.push({
      path: `/marketing_promotion/coupon_to_view`,
      query: { id: id, type: 'system' },
    });
  };

  const add_coupon = (type, id,pathName) => {
    userStore.setDelKeepAlive([pathName])
    if (type!='add') {
      router.push({
        path: `/marketing_promotion/coupon_to_${type}`,
        query: {
          type: type,
          id: id,
        },
      });
    } else {
      router.push({
        path: '/marketing_promotion/coupon_to_add',
      });
    }
  };

  onMounted(() => {});
  getSetting()
</script>
<style lang="less" scoped>
  @import './style/system_lists.less';

  p {
    margin-bottom: 0;
  }
  // .coupon_home_system_lists {
  // }
</style>
