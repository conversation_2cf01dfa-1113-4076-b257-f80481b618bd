<template>
  <div class="coupon_home section_padding">
    <div class="section_padding_back">
      <SldComHeader title="优惠券管理" />
      <Tabs type="card" v-model:activeKey="activeKey">
        <TabPane key="1" tab="优惠券设置">
          <CouponHomeSetting v-if="activeKey == '1'"/>
        </TabPane>
        <TabPane key="2" tab="平台优惠券">
          <SystemLists v-if="activeKey == '2'" />
        </TabPane>
        <TabPane key="3" tab="风格配置">
          <RadioGroup class="RadioGroup_back section_padding_tab_top" v-model:value="RadioGroupValue">
            <!-- dev_mobile-start -->
            <RadioButton value="1">移动端风格</RadioButton>
            <!-- dev_mobile-end -->
            <!-- dev_pc-start -->
            <RadioButton value="2">PC端风格</RadioButton>
            <!-- dev_pc-end -->
          </RadioGroup>
          <!-- dev_mobile-start -->
          <CouponDiyStyleM v-if="activeKey == '3' && RadioGroupValue == '1'" />
          <!-- dev_mobile-end -->
          <!-- dev_pc-start -->
          <CouponDiyStylePc v-if="activeKey == '3' && RadioGroupValue == '2'" />
          <!-- dev_pc-end -->
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'CouponHome',
  };
</script>
<script setup>
  import { ref, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { Tabs, TabPane, message, RadioGroup, RadioButton } from 'ant-design-vue';
  import CouponHomeSetting from './setting.vue';
  import SystemLists from './system_lists.vue';
  // dev_mobile-start
  import CouponDiyStyleM from './diy_style_m.vue';
  // dev_mobile-end
  // dev_pc-start
  import CouponDiyStylePc from './diy_style_pc.vue';
  // dev_pc-end
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { useUserStore } from '/@/store/modules/user';

  const router = useRouter();
  const userStore = useUserStore();

  const RadioGroupValue = ref('1');

  const activeKey = ref('1');

  onMounted(() => {
    if(userStore.getUpdateTab.length>0){
      let index = userStore.getUpdateTab.findIndex(item=>item.name == 'CouponHome')
      if(index>-1){
        activeKey.value = userStore.getUpdateTab[index].index
        userStore.setDelTab('CouponHome')
      }
    }
    // dev_mobile-start
    RadioGroupValue.value = '1'
    return
    // dev_mobile-end
    // dev_pc-start
    RadioGroupValue.value = '2'
    // dev_pc-end
  });
</script>
<style lang="less">
  .coupon_home {
    padding: 10px;

    .coupon_home_back {
      width: 100%;
      min-height: calc(100vh - @header-height - 20px);
      padding: 10px;
      background-color: #fff;
    }
  }

  .RadioGroup_back {
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within {
      box-shadow: none;
    }
  }
</style>
