<template>
  <Spin :spinning="spinning">
    <div class="coupon_diy_style">
      <div class="diy_style_list flex_row_start_start" style="flex-wrap: wrap">
        <div class="" v-for="(item, index) in color_list" :key="index">
          <div
            class="flex_row_center_center diy_style_item"
            :class="{ active: color_index == index }"
            @click="selectColor(index)"
          >
            <div class="diy_style_color flex_row_center_center">
              <div class="diy_style_color_left" :style="{ background: item.mainColor }"></div>
            </div>
            <div class="diy_style_name">{{ item.name }}</div>
            <img
              src="@/assets/images/diy_style_checked.png"
              class="diy_style_checked"
              alt=""
              v-if="color_index == index"
            />
          </div>
        </div>
        <div
          @click="selectAutoColor"
          class="flex_row_center_center diy_style_item"
          :class="{ active: color_index == color_list.length }"
        >
          <div
            class="diy_style_color flex_row_center_center"
            v-if="color_auto.mainColor && color_auto.mainColor != 'rgba(255,255,255,1)'"
          >
            <div class="diy_style_color_left" :style="{ background: color_auto.mainColor }"></div>
          </div>
          <div class="diy_style_auto_name" v-else>自定义颜色选择</div>
          <div
            class="diy_style_name"
            v-if="color_auto.mainColor && color_auto.mainColor != 'rgba(255,255,255,1)'"
          >
            重选
          </div>
          <div class="diy_style_auto_arrow" v-else> &gt; </div>
          <img
            class="diy_style_checked"
            src="@/assets/images/diy_style_checked.png"
            alt=""
            v-if="color_index == color_list.length"
          />
        </div>
      </div>
      <div class="prew_title"> 图片预览 </div>
      <div class="flex_row_start_center prew_list">
        <div
          class="prew_item prew_item_center"
          :style="{ backgroundImage: `url('${diy_style_center}')` }"
        >
          <div
            class="top_nav"
            :style="{
              color:
                color_index < color_list.length
                  ? color_list[color_index].mainColor
                  : color_auto.mainColor,
              borderColor:
                color_index < color_list.length
                  ? color_list[color_index].mainColor
                  : color_auto.mainColor,
            }"
          >
            精选
          </div>
          <div class="item" v-for="(item, index) in diy_coupon_reduction" :key="index">
            <div class="flex_row_start_start item_top">
              <div
                class="flex_row_start_center item_top_left"
                :style="{
                  backgroundColor:
                    color_index < color_list.length
                      ? color_list[color_index].mainOpacity
                      : color_auto.mainOpacity,
                }"
              >
                <div class="flex_column_between_center item_top_price">
                  <div
                    :style="{
                      color:
                        color_index < color_list.length
                          ? color_list[color_index].mainColor
                          : color_auto.mainColor,
                    }"
                  >
                    满减券
                  </div>
                  <div
                    :style="{
                      color:
                        color_index < color_list.length
                          ? color_list[color_index].mainColor
                          : color_auto.mainColor,
                    }"
                  >
                    ￥<span>{{ item.price }}</span>
                  </div>
                </div>
                <div class="flex_column_between_start item_top_desc">
                  <span>{{ item.desc }}</span>
                  <span>{{ item.time }}</span>
                </div>
              </div>
              <div class="flex_column_center_center item_top_right">
                <div class="flex_row_center_center item_top_right_progras">
                  <div
                    :style="{
                      borderColor:
                        color_index < color_list.length
                          ? color_list[color_index].mainColor
                          : color_auto.mainColor,
                    }"
                  >
                    <span
                      :style="{
                        width: item.percent + '%',
                        background:
                          color_index < color_list.length
                            ? color_list[color_index].mainColor
                            : color_auto.mainColor,
                      }"
                    ></span>
                  </div>
                  <div>已抢{{ item.percent }}%</div>
                </div>
                <div
                  class="item_top_right_btn"
                  :style="{
                    backgroundColor:
                      color_index < color_list.length
                        ? color_list[color_index].mainColor
                        : color_auto.mainColor,
                  }"
                >
                  立即领取
                </div>
              </div>
            </div>
            <div class="item_bottom">使用规则：全部商品可用</div>
          </div>
        </div>
        <div
          class="prew_item prew_item_list"
          :style="{ backgroundImage: `url('${diy_style_list}')` }"
        >
          <div
            class="top_nav"
            :style="{
              color:
                color_index < color_list.length
                  ? color_list[color_index].mainColor
                  : color_auto.mainColor,
              borderColor:
                color_index < color_list.length
                  ? color_list[color_index].mainColor
                  : color_auto.mainColor,
            }"
          >
            未使用
          </div>
          <div
            class="flex_row_start_center item"
            v-for="(item, index) in diy_coupon_reduction_one"
            :key="index"
          >
            <div
              class="label"
              :style="{
                backgroundColor:
                  color_index < color_list.length
                    ? color_list[color_index].mainColor
                    : color_auto.mainColor,
              }"
            >
              {{ item.label }}
            </div>
            <div
              class="flex_column_center_center price"
              :style="{
                color:
                  color_index < color_list.length
                    ? color_list[color_index].mainColor
                    : color_auto.mainColor,
              }"
            >
              <div
                >{{ item.price && '￥'
                }}<span>{{ item.price || item.discount?.split('.')[0] }}</span
                >{{ item.discount && '.' + item.discount.split('.')[1] + '折' }}</div
              >
              <div>{{ item.desc }}</div>
            </div>
            <div class="flex_column_between_start desc">
              <div>{{ item.type }}</div>
              <div>{{ item.time }}</div>
            </div>
            <div class="btn">
              <AliSvgIcon
                iconName="iconyouhuiquanjuchi"
                width="94px"
                height="96px"
                :fillColor="
                  color_index < color_list.length
                    ? color_list[color_index].mainColor
                    : color_auto.mainColor
                "
              />
              <span>立即领取</span>
            </div>
          </div>
        </div>
      </div>
      <div class="m_diy_bottom_wrap" :style="{ position: 'fixed', left: getRealWidth + 10 + 'px' }">
        <div class="add_goods_bottom_btn add_goods_bottom_btn_sel" @click="handleSaveAllData">
          保存
        </div>
      </div>
      <SldModal
        :width="500"
        title="自定义颜色选择"
        :visible="modalVisible"
        :content="content_color"
        :showFoot="true"
        :confirmBtnLoading="false"
        @cancle-event="handleModalCancle"
        @confirm-event="handleModalConfirm"
      />
    </div>
  </Spin>
</template>
<script>
  export default {
    name: 'CouponDiyStyleM',
  };
</script>
<script setup>
  import { ref, onMounted } from 'vue';
  import { Spin } from 'ant-design-vue';
  import SldModal from '/@/components/SldModal/index.vue';
  import { getSettingListApi, getSettingUpdateApi } from '/@/api/common/common';
  import diy_style_center from '/@/assets/images/marketing/coupon/diy_style_center.png';
  import diy_style_list from '/@/assets/images/marketing/coupon/diy_style_list.png';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import { failTip, sucTip } from '/@/utils/utils';

  const { getRealWidth } = useMenuSetting();
  const color_index = ref(0);
  const spinning = ref(true); //loading
  const content_color = ref([]);
  const diy_coupon_reduction = ref([
    { price: 5, desc: '满49元减5元', time: '2023.04.11~2023.05.11', percent: 64 },
    { price: 10, desc: '满99元减10元', time: '2023.04.11~2023.05.11', percent: 32 },
    { price: 20, desc: '满149元减20元', time: '2023.04.13~2023.05.13', percent: 16 },
    { price: 30, desc: '满199元减30元', time: '2023.04.15~2023.05.15', percent: 0 },
  ]);

  const diy_coupon_reduction_one = ref([
    { label: '满减券', price: 5, type: '通用', desc: '满49元减5元', time: '2023.04.11~2023.05.11' },
    {
      label: '折扣券',
      discount: '8.0',
      type: '通用',
      desc: '最多优惠10元',
      time: '2023.04.11~2023.05.11',
    },
    {
      label: '折扣券',
      discount: '8.8',
      type: '指定商品使用',
      desc: '最多优惠50元',
      time: '2023.04.13~2023.05.13',
    },
    {
      label: '满减券',
      price: 10,
      type: '通用',
      desc: '满99元减10元',
      time: '2023.04.15~2023.05.15',
    },
    { label: '满减券', price: 5, type: '通用', desc: '满49元减5元', time: '2023.04.15~2023.05.15' },
  ]);

  const color_list = ref([
    //颜色列表
    {
      mainColor: '#ED0775',
      mainOpacity: '#ED077517',
      mainLinearColor: 'linear-gradient(90deg, #ED0775BF, #ED0775)',
      name: '默认色',
    },
    {
      mainColor: '#39BC17',
      mainOpacity: '#39BC1717',
      mainLinearColor: 'linear-gradient(90deg, #39BC17BF, #39BC17)',
      name: '翡翠绿',
    },
    {
      mainColor: '#E84165',
      mainOpacity: '#E8416517',
      mainLinearColor: 'linear-gradient(90deg, #E84165BF, #E84165)',
      name: '雅致粉',
    },
    {
      mainColor: '#884CFF',
      mainOpacity: '#884CFF17',
      mainLinearColor: 'linear-gradient(90deg, #884CFFBF, #884CFF)',
      name: '魅力紫',
    },
    {
      mainColor: '#F5BF41',
      mainOpacity: '#F5BF4117',
      mainLinearColor: 'linear-gradient(90deg, #F5BF41BF, #F5BF41)',
      name: '经典黄',
    },
    {
      mainColor: '#ED682E',
      mainOpacity: '#ED682E17',
      mainLinearColor: 'linear-gradient(90deg, #ED682EBF, #ED682E)',
      name: '活力橙',
    },
    {
      mainColor: '#1E78F5',
      mainOpacity: '#1E78F517',
      mainLinearColor: 'linear-gradient(90deg, #1E78F5BF, #1E78F5)',
      name: '天空蓝',
    },
    {
      mainColor: '#1E1C1B',
      mainOpacity: '#1E1C1B17',
      mainLinearColor: 'linear-gradient(90deg, #1E1C1BBF, #1E1C1B)',
      name: '雅酷黑',
    },
  ]);

  const color_auto = ref({
    //自定义颜色
    mainColor: '', //主色
    mainOpacity: '', //主色透明
    mainLinearColor: '', //主色渐变色
  });

  const modalVisible = ref(false);

  const get_style_setting = async () => {
    try {
      let res = await getSettingListApi({ str: 'mobile_coupon_mall_style' });
      if (res.state == 200 && res.data) {
        let data = !(res.data[0] && res.data[0].value) ? {} : JSON.parse(res.data[0].value);
        if (Object.keys(data).length > 0) {
          let colorIndex = color_list.value.length;
          let mainColor =
            data.mainColor != undefined
              ? data.mainColor == '#fff'
                ? '#FFFFFF'
                : data.mainColor
              : '';
          let mainOpacity =
            data.mainOpacity != undefined
              ? data.mainColor == '#fff'
                ? '#FFFFFF17'
                : data.mainOpacity
              : '';
          let mainLinearColor =
            data.mainLinearColor != undefined
              ? data.mainColor == '#fff'
                ? '#FFFFFF'
                : data.mainLinearColor
              : '';
          if (mainColor && mainOpacity && mainLinearColor) {
            for (var index = 0; index < color_list.value.length; index++) {
              if (
                color_list.value[index].mainColor == mainColor &&
                color_list.value[index].mainOpacity == mainOpacity &&
                color_list.value[index].mainLinearColor == mainLinearColor
              ) {
                colorIndex = index;
                break;
              }
            }
            if (colorIndex == color_list.value.length) {
              colorIndex = color_list.value.length;
              color_auto.value.mainColor = mainColor;
              color_auto.value.mainOpacity = mainOpacity;
              color_auto.value.mainLinearColor = mainLinearColor;
              color_index.value = colorIndex;
            } else {
              color_index.value = colorIndex;
            }
          }
        }
      } else {
        failTip(res.msg);
      }
      spinning.value = false;
    } catch (error) {
      spinning.value = false;
    }
  };

  const selectAutoColor = () => {
    content_color.value = [];
    let content_info = [
      {
        type: 'more_color_picker',
        label: `主色选择`, //主色选择
        name: 'mainColor',
        initialValue: color_auto.value.mainColor ? color_auto.value.mainColor : '#fff',
        is_show: false,
      },
    ];
    content_color.value = content_info;
    modalVisible.value = true;
  };

  const handleModalCancle = () => {
    content_color.value = [];
    modalVisible.value = false;
  };

  const handleModalConfirm = (val) => {
    let resetFlag = 0;
    color_auto.value.mainColor = val.mainColor;
    color_auto.value.mainOpacity = val.mainColor.replace(',1)', ',.1)');
    color_auto.value.mainLinearColor =
      'linear-gradient(90deg, ' +
      val.mainColor.replace(',1)', ',.75)') +
      ', ' +
      val.mainColor +
      ')';
    for (let key in color_auto.value) {
      if (color_auto.value[key] == 'rgba(255,255,255,1)' || !color_auto.value[key]) {
        resetFlag += 1;
      }
    }
    color_index.value = resetFlag == 1 ? 0 : color_list.value.length;
    modalVisible.value = false;
  };

  //选择颜色
  const selectColor = (index) => {
    color_index.value = index;
  };
  const handleSaveAllData = async () => {
    try {
      let params = {};
      if (color_index.value < color_list.value.length) {
        params = JSON.stringify(color_list.value[color_index.value]);
      } else {
        params = JSON.stringify(color_auto.value);
      }
      let res = await getSettingUpdateApi({ mobile_coupon_mall_style: params });
      if (res.state == 200) {
        sucTip(res.msg);
      } else {
        failTip(res.msg);
      }
    } catch (error) {}
  };

  onMounted(() => {
    get_style_setting();
  });
</script>
<style lang="less">
  @import './style/diy_style.less';
</style>
