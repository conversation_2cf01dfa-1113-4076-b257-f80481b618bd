<template>
  <Spin :spinning="spinning">
    <div class="coupon_diy_style">
      <div class="diy_style_list flex_row_start_start" style="flex-wrap: wrap">
        <div class="" v-for="(item, index) in color_list" :key="index">
          <div
            class="flex_row_center_center diy_style_item"
            :class="{ active: color_index == index }"
            @click="selectColor(index)"
          >
            <div class="diy_style_color flex_row_center_center">
              <div class="diy_style_color_left" :style="{ background: item.mainColor }"></div>
            </div>
            <div class="diy_style_name">{{ item.name }}</div>
            <img
              src="@/assets/images/diy_style_checked.png"
              class="diy_style_checked"
              alt=""
              v-if="color_index == index"
            />
          </div>
        </div>
        <div
          @click="selectAutoColor"
          class="flex_row_center_center diy_style_item"
          :class="{ active: color_index == color_list.length }"
        >
          <div
            class="diy_style_color flex_row_center_center"
            v-if="color_auto.mainColor && color_auto.mainColor != 'rgba(255,255,255,1)'"
          >
            <div class="diy_style_color_left" :style="{ background: color_auto.mainColor }"></div>
          </div>
          <div class="diy_style_auto_name" v-else>自定义颜色选择</div>
          <div
            class="diy_style_name"
            v-if="color_auto.mainColor && color_auto.mainColor != 'rgba(255,255,255,1)'"
          >
            重选
          </div>
          <div class="diy_style_auto_arrow" v-else> &gt; </div>
          <img
            class="diy_style_checked"
            src="@/assets/images/diy_style_checked.png"
            alt=""
            v-if="color_index == color_list.length"
          />
        </div>
      </div>
      <div class="prew_title"> 图片预览 </div>
      <div class="flex_column_start_start prew_pc_list">
        <div class="flex_row_start_start prew_item">
          <div class="prew_item_title"> 页面一、 </div>
          <div
            class="prew_item_center"
            :style="{ backgroundImage: `url('${diy_style_pc_center}')` }"
          >
            <div class="flex_row_start_center top_nav">
              <div
                class="top_nav_item active"
                :style="{
                  background:
                    color_index < color_list.length
                      ? color_list[color_index].mainColor
                      : color_auto.mainColor,
                }"
              >
                精选
              </div>
              <div class="top_nav_item">全部</div>
            </div>
            <div class="flex_row_start_start coupon_list">
              <div
                v-for="(item, index) in coupon_list"
                :key="index"
                class="flex_row_between_center coupon_item"
                :style="{ backgroundImage: `url('${diy_style_coupon_item}')` }"
              >
                <div
                  class="coupon_item_left"
                  :style="{
                    color:
                      color_index < color_list.length
                        ? color_list[color_index].mainColor
                        : color_auto.mainColor,
                  }"
                >
                  {{ item.label }}
                </div>
                <div class="flex_column_between_start coupon_item_middle">
                  <div
                    class="flex_row_start_start coupon_item_price"
                    :style="{
                      color:
                        color_index < color_list.length
                          ? color_list[color_index].mainColor
                          : color_auto.mainColor,
                      alignItems: item.price ? 'flex-start' : 'baseline',
                    }"
                  >
                    <span v-if="item.price">￥</span>
                    {{ item.price ? item.price : item.discount?.split('.')[0] }}
                    <span v-if="item.discount">.{{ item.discount.split('.')[1] }}折</span>
                  </div>
                  <div class="coupon_item_desc">
                    {{ item.desc }}
                  </div>
                  <div class="coupon_item_time">
                    {{ item.time }}
                  </div>
                  <div class="coupon_item_rule"> <span>使用规则：</span>{{ item.rule }} </div>
                </div>
                <div class="flex_column_center_center coupon_item_right">
                  <Progress
                    type="dashboard"
                    :percent="item.percent"
                    :width="75"
                    :strokeWidth="8"
                    :strokeColor="
                      item.percent < 100
                        ? color_index < color_list.length
                          ? color_list[color_index].mainColor
                          : color_auto.mainColor
                        : '#D0D0D0'
                    "
                  >
                    <template #format="val">
                      <div
                        class="flex_column_center_center coupon_item_percent"
                        :style="{
                          color:
                            item.percent === 100
                              ? '#D0D0D0'
                              : color_index < color_list.length
                              ? color_list[color_index].mainColor
                              : color_auto.mainColor,
                        }"
                      >
                        <span>{{ item.percent === 100 ? '已抢光' : '已抢' }}</span>
                        <span v-if="item.percent < 100">{{ val }}%</span>
                      </div>
                    </template>
                  </Progress>
                  <div
                    class="coupon_item_btn"
                    :style="{
                      background:
                        item.percent === 100
                          ? '#D0D0D0'
                          : color_index < color_list.length
                          ? color_list[color_index].mainColor
                          : color_auto.mainColor,
                    }"
                  >
                    立即领取
                  </div>
                </div>
              </div>
            </div>
            <div class="flex_row_center_center pagenation">
              <div class="flex_row_center_center pagenation_left">
                <div>&lt;</div>
                <div
                  :style="{
                    color:
                      color_index < color_list.length
                        ? color_list[color_index].mainColor
                        : color_auto.mainColor,
                  }"
                  >1</div
                >
                <div>2</div>
                <div>&gt;</div>
              </div>
              <div class="pagenation_middle">Go to</div>
              <div class="pagenation_right">1</div>
            </div>
          </div>
        </div>
        <div class="flex_row_start_start prew_item">
          <div class="prew_item_title"> 页面二、 </div>
          <div class="prew_item_list" :style="{ backgroundImage: `url('${diy_style_pc_list}')` }">
            <div
              class="top_nav"
              :style="{
                color:
                  color_index < color_list.length
                    ? color_list[color_index].mainColor
                    : color_auto.mainColor,
              }"
            >
              未使用
            </div>
            <div class="flex_row_start_start coupon_list">
              <template v-for="(item, index) in coupon_list" :key="index">
                <div v-if="index < 8" class="flex_column_start_center coupon_item">
                  <div
                    class="coupon_item_top"
                    :style="{
                      background:
                        color_index < color_list.length
                          ? color_list[color_index].mainColor
                          : color_auto.mainColor,
                    }"
                  >
                    <div
                      class="flex_row_center_start coupon_item_price"
                      :style="{ alignItems: item.price ? 'flex-start' : 'baseline' }"
                    >
                      <span v-if="item.price">￥</span>
                      {{ item.price ? item.price : item.discount?.split('.')[0] }}
                      <span v-if="item.discount">.{{ item.discount.split('.')[1] }}折</span>
                    </div>
                    <div class="coupon_item_desc">
                      {{ item.desc }}
                    </div>
                    <div class="coupon_item_time">
                      {{ item.time }}
                    </div>
                  </div>
                  <div
                    class="coupon_item_bottom"
                    :style="{ backgroundImage: `url('${diy_style_pc_coupon_bottom}')` }"
                  >
                    <div
                      class="coupon_item_type"
                      :style="{
                        color:
                          color_index < color_list.length
                            ? color_list[color_index].mainColor
                            : color_auto.mainColor,
                      }"
                    >
                      {{ item.label }}
                    </div>
                    <div class="coupon_item_rule"> <span>使用规则：</span>{{ item.rule }} </div>
                    <div
                      class="coupon_item_btn"
                      :style="{
                        color:
                          color_index < color_list.length
                            ? color_list[color_index].mainColor
                            : color_auto.mainColor,
                      }"
                    >
                      立即使用<span>&gt;</span>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
      <div class="m_diy_bottom_wrap" :style="{ position: 'fixed', left: getRealWidth + 10 + 'px' }">
        <div class="add_goods_bottom_btn add_goods_bottom_btn_sel" @click="handleSaveAllData">
          保存
        </div>
      </div>
      <SldModal
        :width="500"
        title="自定义颜色选择"
        :visible="modalVisible"
        :content="content_color"
        :showFoot="true"
        :confirmBtnLoading="false"
        @cancle-event="handleModalCancle"
        @confirm-event="handleModalConfirm"
      />
    </div>
  </Spin>
</template>
<script>
  export default {
    name: 'CouponDiyStyleM',
  };
</script>

<script setup>
  import { ref, onMounted } from 'vue';
  import { Spin, Progress } from 'ant-design-vue';
  import SldModal from '/@/components/SldModal/index.vue';
  import { getSettingListApi, getSettingUpdateApi } from '/@/api/common/common';
  import diy_style_pc_center from '/@/assets/images/marketing/coupon/diy_style_pc_center.png';
  import diy_style_coupon_item from '/@/assets/images/marketing/coupon/diy_style_coupon_item.png';
  import diy_style_pc_list from '/@/assets/images/marketing/coupon/diy_style_pc_list.png';
  import diy_style_pc_coupon_bottom from '/@/assets/images/marketing/coupon/diy_style_pc_coupon_bottom.png';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import { failTip, sucTip } from '/@/utils/utils';

  const { getRealWidth } = useMenuSetting();
  const color_index = ref(0);
  const spinning = ref(true); //loading
  const content_color = ref([]);
  const coupon_list = ref([
    {
      label: '满减券',
      price: 5,
      desc: '满35可用',
      time: '2023-04-20~2023-05-21',
      rule: '全场商品均可使用',
      percent: 66,
    },
    {
      label: '满减券',
      price: 10,
      desc: '满60可用',
      time: '2023-04-20~2023-05-21',
      rule: '全场商品均可使用',
      percent: 42,
    },
    {
      label: '折扣券',
      discount: '9.5',
      desc: '满200可用',
      time: '2023-04-20~2023-05-21',
      rule: '全场商品均可使用',
      percent: 28,
    },
    {
      label: '满减券',
      price: 20,
      desc: '满75可用',
      time: '2023-04-20~2023-05-21',
      rule: '全场商品均可使用',
      percent: 15,
    },
    {
      label: '满减券',
      price: 30,
      desc: '满90可用',
      time: '2023-04-20~2023-05-21',
      rule: '全场商品均可使用',
      percent: 100,
    },
    {
      label: '满减券',
      price: 50,
      desc: '满180可用',
      time: '2023-04-20~2023-05-21',
      rule: '全场商品均可使用',
      percent: 8,
    },
    {
      label: '折扣券',
      discount: '9.0',
      desc: '满500可用',
      time: '2023-04-20~2023-05-21',
      rule: '家用电器、数码产...',
      percent: 1,
    },
    {
      label: '折扣券',
      discount: '8.5',
      desc: '满1000可用',
      time: '2023-04-20~2023-05-21',
      rule: '家用电器、数码产...',
      percent: 0,
    },
    {
      label: '满减券',
      price: 100,
      desc: '满300可用',
      time: '2023-04-20~2023-05-21',
      rule: '全场商品均可使用',
      percent: 3,
    },
  ]);

  const color_list = ref([
    //颜色列表
    {
      mainColor: '#ED0775',
      mainLinearColor: 'linear-gradient(90deg, #ED0775BF, #ED0775)',
      name: '默认色',
    },
    {
      mainColor: '#39BC17',
      mainLinearColor: 'linear-gradient(90deg, #39BC17BF, #39BC17)',
      name: '翡翠绿',
    },
    {
      mainColor: '#E84165',
      mainLinearColor: 'linear-gradient(90deg, #E84165BF, #E84165)',
      name: '雅致粉',
    },
    {
      mainColor: '#884CFF',
      mainLinearColor: 'linear-gradient(90deg, #884CFFBF, #884CFF)',
      name: '魅力紫',
    },
    {
      mainColor: '#F5BF41',
      mainLinearColor: 'linear-gradient(90deg, #F5BF41BF, #F5BF41)',
      name: '经典黄',
    },
    {
      mainColor: '#ED682E',
      mainLinearColor: 'linear-gradient(90deg, #ED682EBF, #ED682E)',
      name: '活力橙',
    },
    {
      mainColor: '#1E78F5',
      mainLinearColor: 'linear-gradient(90deg, #1E78F5BF, #1E78F5)',
      name: '天空蓝',
    },
    {
      mainColor: '#1E1C1B',
      mainLinearColor: 'linear-gradient(90deg, #1E1C1BBF, #1E1C1B)',
      name: '雅酷黑',
    },
  ]);

  const color_auto = ref({
    //自定义颜色
    mainColor: '', //主色
    mainLinearColor: '', //主色渐变色
  });

  const modalVisible = ref(false);

  const get_style_setting = async () => {
    try {
      let res = await getSettingListApi({ str: 'pc_coupon_mall_style,pc_mall_style' });
      if (res.state == 200 && res.data) {
        let data = !(res.data[0] && res.data[0].value) ? {} : JSON.parse(res.data[0].value);
        if (Object.keys(data).length > 0) {
          let colorIndex = color_list.value.length;
          let mainColor =
            data.mainColor != undefined
              ? data.mainColor == '#fff'
                ? '#FFFFFF'
                : data.mainColor
              : '';
          let mainLinearColor =
            data.mainLinearColor != undefined
              ? data.mainColor == '#fff'
                ? '#FFFFFF'
                : data.mainLinearColor
              : '';
          if (mainColor && mainLinearColor) {
            for (let index = 0; index < color_list.value.length; index++) {
              if (
                color_list.value[index].mainColor == mainColor &&
                color_list.value[index].mainLinearColor == mainLinearColor
              ) {
                colorIndex = index;
                break;
              }
            }
            if (colorIndex == color_list.value.length) {
              colorIndex = color_list.value.length;
              color_auto.value.mainColor = mainColor;
              color_auto.value.mainLinearColor = mainLinearColor;
              color_index.value = colorIndex;
            } else {
              color_index.value = colorIndex;
            }
          }
        }
      } else {
        failTip(res.msg);
      }
      spinning.value = false;
    } catch (error) {
      spinning.value = false;
    }
  };

  const selectAutoColor = () => {
    content_color.value = [];
    let content_info = [
      {
        type: 'more_color_picker',
        label: `主色选择`, //主色选择
        name: 'mainColor',
        initialValue: color_auto.value.mainColor ? color_auto.value.mainColor : '#fff',
        is_show: false,
      },
    ];
    content_color.value = content_info;
    modalVisible.value = true;
  };

  const handleModalCancle = () => {
    content_color.value = [];
    modalVisible.value = false;
  };

  const handleModalConfirm = (val) => {
    let resetFlag = 0;
    color_auto.value.mainColor = val.mainColor;
    color_auto.value.mainLinearColor =
      'linear-gradient(90deg, ' +
      val.mainColor.replace(',1)', ',.75)') +
      ', ' +
      val.mainColor +
      ')';
    for (let key in color_auto.value) {
      if (color_auto.value[key] == 'rgba(255,255,255,1)' || !color_auto.value[key]) {
        resetFlag += 1;
      }
    }
    color_index.value = resetFlag == 1 ? 0 : color_list.value.length;
    modalVisible.value = false;
  };

  //选择颜色
  const selectColor = (index) => {
    color_index.value = index;
  };

  const handleSaveAllData = async () => {
    try {
      let params = {};
      if (color_index.value < color_list.value.length) {
        params = JSON.stringify(color_list.value[color_index.value]);
      } else {
        params = JSON.stringify(color_auto.value);
      }
      let res = await getSettingUpdateApi({ pc_coupon_mall_style: params });
      if (res.state == 200) {
        sucTip(res.msg);
      } else {
        failTip(res.msg);
      }
    } catch (error) {}
  };

  onMounted(() => {
    get_style_setting();
  });
</script>
<style lang="less">
  @import './style/diy_style.less';
</style>
