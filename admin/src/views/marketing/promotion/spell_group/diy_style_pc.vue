<template>
  <Spin :spinning="spinning">
    <div class="spell_diy_style">
      <div class="diy_style_list flex_row_start_start" style="flex-wrap: wrap">
        <div class="" v-for="(item, index) in color_list" :key="index">
          <div
            class="flex_row_center_center diy_style_item"
            :class="{ active: color_index == index }"
            @click="selectColor(index)"
          >
            <div class="diy_style_color flex_row_center_center">
              <div class="diy_style_color_left" :style="{ background: item.mainColor }"></div>
              <div class="diy_style_color_right" :style="{ background: item.subColor }"></div>
            </div>
            <div class="diy_style_name">{{ item.name }}</div>
            <img
              src="@/assets/images/diy_style_checked.png"
              class="diy_style_checked"
              alt=""
              v-if="color_index == index"
            />
          </div>
        </div>
        <div
          @click="selectAutoColor"
          class="flex_row_center_center diy_style_item"
          :class="{ active: color_index == color_list.length }"
        >
          <div
            class="diy_style_color flex_row_center_center"
            v-if="color_auto.mainColor && color_auto.mainColor != 'rgba(255,255,255,1)'"
          >
            <div class="diy_style_color_left" :style="{ background: color_auto.mainColor }"></div>
            <div class="diy_style_color_right" :style="{ background: color_auto.subColor }"></div>
          </div>
          <div class="diy_style_auto_name" v-else>自定义颜色选择</div>
          <div
            class="diy_style_name"
            v-if="color_auto.mainColor && color_auto.mainColor != 'rgba(255,255,255,1)'"
          >
            重选
          </div>
          <div class="diy_style_auto_arrow" v-else> &gt; </div>
          <img
            class="diy_style_checked"
            src="@/assets/images/diy_style_checked.png"
            alt=""
            v-if="color_index == color_list.length"
          />
        </div>
      </div>
      <div class="prew_tips"> 颜色说明： 从左至右颜色依次为·主色、辅助色 </div>
      <div class="prew_title"> 图片预览 </div>
      <div class="flex_column_start_start prew_pc_list">
        <div class="flex_row_start_start prew_item">
          <div class="prew_item_title">页面一、</div>
          <div class="prew_item_goods" :style="{ backgroundImage: `url('${diy_style_pc_goods}')` }">
            <div
              class="prew_item_follow"
              :style="{
                background: homeData.mainColor
                  ? homeData.mainColor
                  : color_index < color_list.length
                  ? color_list[color_index].mainColor
                  : color_auto.mainColor,
              }"
              >关注</div
            >
            <div
              class="prew_item_score"
              :style="{
                color: homeData.mainColor
                  ? homeData.mainColor
                  : color_index < color_list.length
                  ? color_list[color_index].mainColor
                  : color_auto.mainColor,
              }"
            >
              <span>5.0</span>
              <span
                :style="{
                  borderTopColor: homeData.mainColor
                    ? homeData.mainColor
                    : color_index < color_list.length
                    ? color_list[color_index].mainColor
                    : color_auto.mainColor,
                }"
              ></span>
            </div>
            <div class="flex_row_start_start top_search">
              <div
                class="flex_row_start_center top_search_left"
                :style="{
                  borderColor: homeData.mainColor
                    ? homeData.mainColor
                    : color_index < color_list.length
                    ? color_list[color_index].mainColor
                    : color_auto.mainColor,
                }"
              >
                <span>请输入关键词</span>
              </div>
              <div
                class="top_search_right"
                :style="{
                  background: homeData.mainColor
                    ? homeData.mainColor
                    : color_index < color_list.length
                    ? color_list[color_index].mainColor
                    : color_auto.mainColor,
                }"
                >搜索</div
              >
            </div>
            <div class="top_search_store">搜本店</div>
            <div class="flex_row_start_center top_search_right_store">
              <div>请输入...</div>
              <div
                :style="{
                  background: homeData.mainColor
                    ? homeData.mainColor
                    : color_index < color_list.length
                    ? color_list[color_index].mainColor
                    : color_auto.mainColor,
                }"
                >搜索</div
              >
            </div>
            <div
              class="top_store_label"
              :style="{
                background: homeData.mainColor
                  ? homeData.mainColor
                  : color_index < color_list.length
                  ? color_list[color_index].mainColor
                  : color_auto.mainColor,
              }"
              >自营</div
            >
            <div class="top_kefu">
              <AliSvgIcon
                iconName="iconkefu2"
                width="17px"
                height="17px"
                :fillColor="
                  homeData.mainColor
                    ? homeData.mainColor
                    : color_index < color_list.length
                    ? color_list[color_index].mainColor
                    : color_auto.mainColor
                "
              />
            </div>
            <div
              class="flex_row_center_center top_cart"
              :style="{
                borderColor: homeData.mainColor
                  ? homeData.mainColor
                  : color_index < color_list.length
                  ? color_list[color_index].subColor
                  : color_auto.subColor,
              }"
            >
              <AliSvgIcon
                iconName="icongouwuche1"
                width="18px"
                height="18px"
                :fillColor="
                  homeData.mainColor
                    ? homeData.mainColor
                    : color_index < color_list.length
                    ? color_list[color_index].subColor
                    : color_auto.subColor
                "
              />
              <span
                class="top_cart_title"
                :style="{
                  color: homeData.mainColor
                    ? homeData.mainColor
                    : color_index < color_list.length
                    ? color_list[color_index].subColor
                    : color_auto.subColor,
                }"
                >购物车</span
              >
              <span
                class="top_cart_num"
                :style="{
                  background: homeData.priceColor
                    ? homeData.priceColor
                    : color_index < color_list.length
                    ? color_list[color_index].mainColor
                    : color_auto.mainColor,
                }"
                >3</span
              >
            </div>
            <div
              class="goods_desc"
              :style="{
                color: homeData.mainColor
                  ? homeData.mainColor
                  : color_index < color_list.length
                  ? color_list[color_index].mainColor
                  : color_auto.mainColor,
              }"
              >60秒速泡，立享银耳原味</div
            >
            <div
              class="flex_row_end_center goods_point_exchange"
              :style="{
                backgroundImage: `url('${diy_style_pc_goods_bg}')`,
                backgroundColor:
                  color_index < color_list.length
                    ? color_list[color_index].mainColor
                    : color_auto.mainColor,
              }"
            >
              <div>已兑换 9</div>
            </div>
            <div
              class="flex_row_start_start goods_price"
              :style="{
                color: homeData.priceColor
                  ? homeData.priceColor
                  : color_index < color_list.length
                  ? color_list[color_index].mainColor
                  : color_auto.mainColor,
              }"
            >
              ￥<span>12</span>.00
            </div>
            <div class="sale_name"
              >销量<span
                :style="{
                  color: homeData.priceColor
                    ? homeData.priceColor
                    : color_index < color_list.length
                    ? color_list[color_index].mainColor
                    : color_auto.mainColor,
                }"
                >9</span
              ></div
            >
            <div
              v-for="(item, index) in diy_spell_reduction"
              :key="index"
              class="introduce_price"
              :style="{
                color: homeData.priceColor
                  ? homeData.priceColor
                  : color_index < color_list.length
                  ? color_list[color_index].mainColor
                  : color_auto.mainColor,
                top: item.top + 'px',
              }"
              >￥{{ item.price }}.00</div
            >
            <div
              class="goods_coupon"
              :style="{
                color: homeData.mainColor
                  ? homeData.mainColor
                  : color_index < color_list.length
                  ? color_list[color_index].mainColor
                  : color_auto.mainColor,
                borderColor: homeData.mainColor
                  ? homeData.mainColor
                  : color_index < color_list.length
                  ? color_list[color_index].mainColor
                  : color_auto.mainColor,
                backgroundColor: homeData.mainOpacityColor
                  ? homeData.mainOpacityColor
                  : color_index < color_list.length
                  ? color_list[color_index].mainOpacityColor
                  : color_auto.mainOpacityColor,
              }"
              >满300元减20元</div
            >
            <div class="flex_row_start_center goods_type">
              <div
                class="goods_type_item"
                :style="{
                  borderColor: homeData.mainColor
                    ? homeData.mainColor
                    : color_index < color_list.length
                    ? color_list[color_index].mainColor
                    : color_auto.mainColor,
                }"
              >
                黑色
                <AliSvgIcon
                  iconName="iconPC-biankuangxuanzhong-kebianse"
                  width="16px"
                  height="16px"
                  :style="{ position: 'absolute', right: '-2px', bottom: '-3px' }"
                  :fillColor="
                    homeData.mainColor
                      ? homeData.mainColor
                      : color_index < color_list.length
                      ? color_list[color_index].mainColor
                      : color_auto.mainColor
                  "
                />
              </div>
              <div class="goods_type_item">蓝色</div>
            </div>
            <div
              class="goods_selected"
              :style="{
                borderColor: homeData.mainColor
                  ? homeData.mainColor
                  : color_index < color_list.length
                  ? color_list[color_index].mainColor
                  : color_auto.mainColor,
              }"
            ></div>
            <div class="flex_row_start_center left_btn">
              <AliSvgIcon
                iconName="iconPC-shoucang-kebianse"
                width="18px"
                height="18px"
                :fillColor="
                  homeData.mainColor
                    ? homeData.mainColor
                    : color_index < color_list.length
                    ? color_list[color_index].mainColor
                    : color_auto.mainColor
                "
              />
              <span>收藏</span>
              <AliSvgIcon
                iconName="iconPC-fenxiang-kebianse"
                width="18px"
                height="18px"
                :fillColor="
                  homeData.mainColor
                    ? homeData.mainColor
                    : color_index < color_list.length
                    ? color_list[color_index].mainColor
                    : color_auto.mainColor
                "
              />
              <span>分享</span>
            </div>
            <div class="flex_row_start_center btn_list">
              <div
                class="flex_column_center_center btn_list_add"
                :style="{
                  color:
                    color_index < color_list.length
                      ? color_list[color_index].subColor
                      : color_auto.subColor,
                  borderColor:
                    color_index < color_list.length
                      ? color_list[color_index].subColor
                      : color_auto.subColor,
                  background:
                    color_index < color_list.length
                      ? color_list[color_index].subColor
                        ? color_list[color_index].subColor.indexOf('#') == 0
                          ? color_list[color_index].subColor + '1A'
                          : color_list[color_index].subColor
                              .replace(',1)', ',.1)')
                              .replace(',.75)', ',.1)')
                        : '#F6F6F6'
                      : color_auto.subColor
                      ? color_auto.subColor.indexOf('#') == 0
                        ? color_auto.subColor + '1A'
                        : color_auto.subColor.replace(',1)', ',.1)').replace(',.75)', ',.1)')
                      : '#F6F6F6',
                }"
              >
                <span>￥12</span>
                <span>扫码开团</span>
              </div>
              <div
                class="flex_column_center_center btn_list_buy"
                :style="{
                  borderColor:
                    color_index < color_list.length
                      ? color_list[color_index].subColor
                      : color_auto.subColor,
                  background:
                    color_index < color_list.length
                      ? color_list[color_index].subColor
                      : color_auto.subColor,
                }"
              >
                <span>￥49</span>
                <span>原价购买</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="m_diy_bottom_wrap" :style="{ position: 'fixed', left: getRealWidth + 10 + 'px' }">
        <div class="add_goods_bottom_btn add_goods_bottom_btn_sel" @click="handleSaveAllData">
          保存
        </div>
      </div>
      <SldModal
        :width="500"
        title="自定义颜色选择"
        :visible="modalVisible"
        :content="content_color"
        :showFoot="true"
        :confirmBtnLoading="false"
        @cancle-event="handleModalCancle"
        @confirm-event="handleModalConfirm"
      />
    </div>
  </Spin>
</template>
<script>
  export default {
    name: 'SeckillDiyStyleM',
  };
</script>
<script setup>
  import { ref, onMounted } from 'vue';
  import { Spin } from 'ant-design-vue';
  import SldModal from '/@/components/SldModal/index.vue';
  import { getSettingListApi, getSettingUpdateApi } from '/@/api/common/common';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import diy_style_pc_goods from '/@/assets/images/marketing/spell/diy_style_pc_goods.png';
  import diy_style_pc_goods_bg from '/@/assets/images/marketing/spell/diy_style_pc_goods_bg.png';
  import { failTip, sucTip } from '/@/utils/utils';

  const { getRealWidth } = useMenuSetting();
  const color_index = ref(0);

  const spinning = ref(true); //loading

  const content_color = ref([]);

  const diy_spell_reduction = ref([
    { price: 959, top: 444 },
    { price: 119, top: 624 },
  ]);

  const color_list = ref([
    //颜色列表
    {
      mainColor: '#ED0775',
      mainLinearColor: 'linear-gradient(90deg, #ED0775BF, #ED0775)',
      subColor: '#FF8622',
      subLinearColor: 'linear-gradient(90deg, #FF8622BF, #FF8622)',
      name: '默认色',
    },
    {
      mainColor: '#39BC17',
      mainLinearColor: 'linear-gradient(90deg, #39BC17BF, #39BC17)',
      subColor: '#4E4E61',
      subLinearColor: 'linear-gradient(90deg, #4E4E61BF, #4E4E61)',
      name: '翡翠绿',
    },
    {
      mainColor: '#E84165',
      mainLinearColor: 'linear-gradient(90deg, #E84165BF, #E84165)',
      subColor: '#F09B38',
      subLinearColor: 'linear-gradient(90deg, #F09B38BF, #F09B38)',
      name: '雅致粉',
    },
    {
      mainColor: '#884CFF',
      mainLinearColor: 'linear-gradient(90deg, #884CFFBF, #884CFF)',
      subColor: '#4E4E61',
      subLinearColor: 'linear-gradient(90deg, #4E4E61BF, #4E4E61)',
      name: '魅力紫',
    },
    {
      mainColor: '#F5BF41',
      mainLinearColor: 'linear-gradient(90deg, #F5BF41BF, #F5BF41)',
      subColor: '#ED682E',
      subLinearColor: 'linear-gradient(90deg, #ED682EBF, #ED682E)',
      name: '经典黄',
    },
    {
      mainColor: '#ED682E',
      mainLinearColor: 'linear-gradient(90deg, #ED682EBF, #ED682E)',
      subColor: '#F5BF41',
      subLinearColor: 'linear-gradient(90deg, #F5BF41BF, #F5BF41)',
      name: '活力橙',
    },
    {
      mainColor: '#1E78F5',
      mainLinearColor: 'linear-gradient(90deg, #1E78F5BF, #1E78F5)',
      subColor: '#534E61',
      subLinearColor: 'linear-gradient(90deg, #534E61BF, #534E61)',
      name: '天空蓝',
    },
    {
      mainColor: '#1E1C1B',
      mainLinearColor: 'linear-gradient(90deg, #1E1C1BBF, #1E1C1B)',
      subColor: '#F09B38',
      subLinearColor: 'linear-gradient(90deg, #F09B38BF, #F09B38)',
      name: '雅酷黑',
    },
  ]);

  const color_auto = ref({
    //自定义颜色
    mainColor: '', //主色
    mainLinearColor: '', //主色渐变色
    subColor: '', //辅助色
    subLinearColor: '', //辅助渐变色
  });

  const homeData = ref({
    mainColor: '#F41410',
    mainOpacityColor: '#F414101A',
    mainLinearColor: 'linear-gradient(90deg, #F41410BF, #F41410)',
    subColor: '#FF8828',
    subLinearColor: 'linear-gradient(90deg, #FF8828BF, #FF8828)',
    priceColor: '#F30300',
    name: '默认色',
  });

  const modalVisible = ref(false);

  const get_style_setting = async () => {
    try {
      let res = await getSettingListApi({ str: 'pc_spell_mall_style,pc_mall_style' });
      if (res.state == 200 && res.data) {
        let data = !(res.data[0] && res.data[0].value) ? {} : JSON.parse(res.data[0].value);
        let home_data = !(res.data[1] && res.data[1].value) ? {} : JSON.parse(res.data[1].value);
        homeData.value = home_data;
        if (Object.keys(data).length > 0) {
          let colorIndex = color_list.value.length;
          let mainColor =
            data.mainColor != undefined
              ? data.mainColor == '#fff'
                ? '#FFFFFF'
                : data.mainColor
              : '';
          let mainLinearColor =
            data.mainLinearColor != undefined
              ? data.mainColor == '#fff'
                ? '#FFFFFF'
                : data.mainLinearColor
              : '';
          let subColor =
            data.subColor != undefined ? (data.subColor == '#fff' ? '#FFFFFF' : data.subColor) : '';
          let subLinearColor =
            data.subLinearColor != undefined
              ? data.subColor == '#fff'
                ? '#FFFFFF'
                : data.subLinearColor
              : '';
          if (mainColor && mainLinearColor && subColor && subLinearColor) {
            for (var index = 0; index < color_list.value.length; index++) {
              if (
                color_list.value[index].mainColor == mainColor &&
                color_list.value[index].mainLinearColor == mainLinearColor &&
                color_list.value[index].subColor == subColor &&
                color_list.value[index].subLinearColor == subLinearColor
              ) {
                colorIndex = index;
                break;
              }
            }
            if (colorIndex == color_list.value.length) {
              colorIndex = color_list.value.length;
              color_auto.value.mainColor = mainColor;
              color_auto.value.mainLinearColor = mainLinearColor;
              color_auto.value.subColor = subColor;
              color_auto.value.subLinearColor = subLinearColor;
            }
          }
          color_index.value = colorIndex;
        }
      } else {
        failTip(res.msg);
      }
      spinning.value = false;
    } catch (error) {
      spinning.value = false;
    }
  };

  const selectAutoColor = () => {
    content_color.value = [];
    let content_info = [
      {
        type: 'more_color_picker',
        label: `主色选择`, //主色选择
        name: 'mainColor',
        initialValue: color_auto.value.mainColor ? color_auto.value.mainColor : '#fff',
        is_show: false,
      },
      {
        type: 'more_color_picker',
        label: `辅助色选择`, //主色选择
        name: 'subColor',
        initialValue: color_auto.value.subColor ? color_auto.value.subColor : '#fff',
        is_show: false,
      },
    ];
    content_color.value = content_info;
    modalVisible.value = true;
  };

  const handleModalCancle = () => {
    content_color.value = [];
    modalVisible.value = false;
  };

  const handleModalConfirm = (val) => {
    let resetFlag = 0;
    color_auto.value.mainColor = val.mainColor;
    color_auto.value.subColor = val.subColor;
    color_auto.value.mainLinearColor =
      'linear-gradient(90deg, ' +
      val.mainColor.replace(',1)', ',.75)') +
      ', ' +
      val.mainColor +
      ')';
    color_auto.value.subLinearColor =
      'linear-gradient(90deg, ' + val.subColor.replace(',1)', ',.75)') + ', ' + val.subColor + ')';
    for (let key in color_auto.value) {
      if (color_auto.value[key] == 'rgba(255,255,255,1)' || !color_auto.value[key]) {
        resetFlag += 1;
      }
    }
    color_index.value = resetFlag == 1 ? 0 : color_list.value.length;
    modalVisible.value = false;
  };

  //选择颜色
  const selectColor = (index) => {
    color_index.value = index;
  };

  const handleSaveAllData = async () => {
    try {
      let params = {};
      if (color_index.value < color_list.value.length) {
        params = JSON.stringify(color_list.value[color_index.value]);
      } else {
        params = JSON.stringify(color_auto.value);
      }
      let res = await getSettingUpdateApi({ pc_spell_mall_style: params });
      if (res.state == 200) {
        sucTip(res.msg);
      } else {
        failTip(res.msg);
      }
    } catch (error) {}
  };

  onMounted(() => {
    get_style_setting();
  });
</script>
<style lang="less">
  @import './style/diy_style.less';
</style>
