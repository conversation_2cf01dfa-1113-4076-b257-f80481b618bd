<template>
  <div class="seckill_home_setting">
    <Spin :spinning="loading">
      <StandardTableRow
        width="100%"
        :tipFlag="needTipFlag"
        :data="info_data.data"
        @callback-event="callbackEvent"
        @submit-event="submitEvent"
      />
    </Spin>
  </div>
</template>
<script>
  export default {
    name: 'SeckillHomeSetting',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { Spin } from 'ant-design-vue';
  import StandardTableRow from '/@/components/StandardTableRow/index.vue';
  import { getSettingListApi, getSettingUpdateApi } from '/@/api/common/common';
  import { sucTip } from '/@/utils/utils';

  const info_data = reactive({
    data: [],
  });

  const loading = ref(true);

  const origionSpellIsEnable = ref(0);

  const curSpellIsEnable = ref(0);

  const needTipFlag = ref(false);

  // 获取数据
  const get_setting = async () => {
    try {
      const res = await getSettingListApi({
        str: 'spell_is_enable,spell_order_auto_cancel_time',
      });
      if (res && res.state == 200) {
        loading.value = false;
        info_data.data = [];
        for (let i in res.data) {
          if (res.data[i].type == 1) {
            info_data.data.push({
              type: 'inputnum',
              label: res.data[i].title,
              width: 300,
              desc_width: 300,
              desc: res.data[i].description,
              key: res.data[i].name,
              placeholder: '',
              min: 0,
              max: 1439, //1440为1天，小于1天
              value: res.data[i].value,
            });
          } else if (res.data[i].type == 4) {
            info_data.data.push({
              type: 'switch',
              width: 300,
              desc_width: 300,
              label: res.data[i].title,
              desc: res.data[i].description,
              key: res.data[i].name,
              placeholder: '',
              value: res.data[i].value,
              checkedValue: '1',
              unCheckedValue: '0',
              callback: true,
            });
          }
          if (res.data[i].name == 'spell_is_enable') {
            origionSpellIsEnable.value = res.data[i].value == 1 ? true : false;
            curSpellIsEnable.value = origionSpellIsEnable.value;
          }
        }
        if (info_data.data.length > 0) {
          info_data.data.push({
            type: 'button',
            width: 300,
            desc_width: 300,
            showSubmit: true,
            submitText: '保存',
            showCancle: false,
            tip: {
              title: '修改拼团活动开关会导致正在进行中的活动失效，确定修改吗？',
            },
          });
        }
      }
    } catch (error) {}
  };

  // 更新数据
  const callbackEvent = (item) => {
    let temp = info_data.data.filter((item) => item.key == item.key);
    if (temp.length > 0) {
      temp[0].value = item.val1;
    }
    if (temp[0].key == 'spell_is_enable') {
      curSpellIsEnable.value = item.val1 == 1 ? true : false;
      needTipFlag.value = origionSpellIsEnable.value == curSpellIsEnable.value ? false : true;
    }
  };

  // 点击保存
  const submitEvent = async (val) => {
    try {
      loading.value = true;
      let res = await getSettingUpdateApi(val);
      if (res.state == 200) {
        loading.value = false;
        origionSpellIsEnable.value = curSpellIsEnable.value;
        needTipFlag.value = false;
        sucTip(res.msg);
        get_setting();
      }
    } catch (error) {}
  };

  onMounted(() => {
    get_setting();
  });
</script>
<style lang="less">
  .seckill_home_setting {
    min-height: 300px;
  }
</style>
