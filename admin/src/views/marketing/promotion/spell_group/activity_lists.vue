<template>
  <div class="activity_lists">
    <BasicTable @register="registerTable" rowKey="spellId">
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'startTime'">
          <div class="voucher_time_wrap" :title="text + '~' + record.endTime">
            <span>{{ text }}</span>
            <span>~</span>
            <span>{{ record.endTime }}</span>
          </div>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :actions="[
              {
                label: '查看详情',
                onClick: view.bind(null, record.spellId, 'detail'),
              },
              {
                label: '查看商品',
                onClick: view.bind(null, record.spellId, 'shop'),
              },
              // 只有进行中、已失效、已结束的才可以查看订单
              {
                label: '查看订单',
                ifShow: record.state == 3 || record.state == 4 || record.state == 5,
                onClick: view.bind(null, record.spellId, 'order'),
              },
              // 只有未开始、进行中的才可以失效
              {
                label: '失效',
                ifShow: record.state == 2 || record.state == 3,
                popConfirm: {
                  title: '确定失效该活动吗？',
                  placement: 'left',
                  confirm: operate.bind(null, { spellId: record.spellId }, 'invalid'),
                },
              },
              // 只有待发布、已失效、已结束的才可以删除
              {
                label: '删除',
                ifShow: record.state == 1 || record.state == 4 || record.state == 5,
                popConfirm: {
                  title: '删除后不可恢复，是否确定删除？',
                  placement: 'left',
                  confirm: operate.bind(null, { spellId: record.spellId }, 'del'),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
  </div>
</template>
<script>
  export default {
    name: 'SpellActivityLists',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { validatorEmoji } from '/@/utils/validate';
  import {
    getSpellListApi,
    getSpellInvalidApi,
    getSpellDelApi,
  } from '/@/api/promotion/spell_group';
  import { failTip, sucTip } from '/@/utils/utils';

  const router = useRouter();

  const spellId = ref(0);

  const columns = reactive({
    data: [
      {
        title: `活动名称`,
        dataIndex: 'spellName',
        align: 'center',
        width: 120,
      },
      {
        title: `店铺名称`,
        dataIndex: 'storeName',
        align: 'center',
        width: 100,
      },
      {
        title: `活动时间`,
        dataIndex: 'startTime',
        align: 'center',
        width: 100,
        customRender: ({ text, record }) => {
          return text + '~' + record.endTime;
        },
      },
      {
        title: `活动状态`,
        dataIndex: 'stateValue',
        align: 'center',
        width: 100,
      },
    ],
  });

  const searchFormSchema = reactive({
    data: [
      {
        component: 'Input',
        label: `活动名称`,
        field: 'spellName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请输入活动名称`,
        },
        rules: [
          {
            // @ts-ignore
            validator: async (rule, value) => {
              await validatorEmoji(rule, value);
            },
            trigger: 'change',
          },
        ],
      },
      {
        component: 'Input',
        label: `店铺名称`,
        field: 'storeName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请输入店铺名称`,
        },
        rules: [
          {
            // @ts-ignore
            validator: async (rule, value) => {
              await validatorEmoji(rule, value);
            },
            trigger: 'change',
          },
        ],
      },
      {
        colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
        component: 'RangePicker',
        label: `活动时间`,
        field: '[startTime,endTime]',
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: ['开始时间', '结束时间'],
        },
      },
      {
        component: 'Select',
        label: `活动状态`,
        field: 'state',
        labelWidth: 90,
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请选择活动状态`,
          options: [
            { value: '', label: `全部` },
            { value: '1', label: `待发布` },
            { value: '2', label: `未开始` },
            { value: '3', label: `进行中` },
            { value: '4', label: `已失效` },
            { value: '5', label: `已结束` },
          ],
        },
      },
    ],
  });

  // 表格数据
  const [registerTable, { reload, getForm }] = useTable({
    // 请求接口
    api: getSpellListApi,
    // 表头
    columns: columns.data,
    immediate: true,
    // 点击搜索前处理的参数
    beforeFetch(values) {
      values.startTime = values.startTime
        ? values.startTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
      return values;
    },
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: searchFormSchema.data,
    },
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    // 取消...
    ellipsis: false,
    // 是否显示序号列
    showIndexColumn: true,
    // 表格右侧操作列配置
    actionColumn: {
      width: 220,
      title: '操作',
      dataIndex: 'action',
      // slots: { customRender: 'action' },
    },
  });

  const view = (id, type) => {
    if (type == 'detail') {
      router.push({
        path: `/marketing_promotion/spell_group_to_view`,
        query: { id: id, tar: 'view' },
      });
    } else if (type == 'shop') {
      router.push({
        path: `/marketing_promotion/spell_group_bind_goods`,
        query: { id: id },
      });
    } else if (type == 'order') {
      router.push({
        path: `/marketing_promotion/spell_group_order`,
        query: { id: id },
      });
    }
  };

  //操作  type: invalid 失效
  const operate = async (id, type) => {
    let param_data = {};
    let res = null;
    if (type == 'invalid') {
      param_data = id;
      res = await getSpellInvalidApi(param_data);
    }
    if (type == 'del') {
      param_data = id;
      res = await getSpellDelApi(param_data);
    }
    if (res.state == 200) {
      sucTip(res.msg);
      reload();
    } else {
      failTip(res.msg);
    }
  };

  onMounted(() => {});
</script>
<style lang="less" scoped>
  p {
    margin-bottom: 0;
  }

  .activity_lists {
    .voucher_num {
      color: @primary-color;
      cursor: pointer;
    }
  }
</style>
<style lang="less">
  .full_modal {
    .ant-modal-header {
      padding: 12px 24px !important;
      background-color: @primary-color;
    }

    .ant-modal-title {
      color: #fff;
      font-size: 14px !important;
      line-height: 18px !important;
    }

    .ant-modal-close-x .anticon svg {
      fill: #fff;
    }

    .ant-modal .ant-modal-content {
      border-radius: 4px;
    }

    .ant-form-item-with-help {
      margin-bottom: 24px;
    }

    .ant-form-item-has-error .ant-input-number,
    .ant-form-item-has-error .ant-picker {
      border-color: #ed6f6f !important;
    }

    .ant-modal-close {
      top: 12px;
      right: 20px;
      height: auto !important;

      .ant-modal-close-x {
        display: flex !important;
        align-items: center;
        justify-content: center;
        width: auto !important;
        height: auto !important;
        line-height: normal;
      }
    }
  }
</style>
