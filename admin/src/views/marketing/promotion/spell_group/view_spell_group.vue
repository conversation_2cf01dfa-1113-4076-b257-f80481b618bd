<template>
  <div class="section_padding view_spell_group">
    <div class="section_padding_back full_activity">
      <SldComHeader title="拼团详情" :back="true" />
      <div class="com_line" style="height: 1px"></div>
      <div class="section_padding_back_add_box_one">
        <Spin :spinning="spinning">
          <div class="height_detail">
            <CommonTitle text="拼团基本信息" />
            <div
              class="full_acm_activity flex_column_start_start"
              v-for="(item, index) in spell_info"
              :key="index"
            >
              <div class="item flex_row_start_center">
                <div class="left">
                  <span style="color: #ff1515">*</span>
                  {{ item.title }}
                </div>
                <div class="right"> {{ item.content }}{{ item.text }} </div>
              </div>
            </div>
            <CommonTitle text="商品信息" style="margin-top: 10px" />
            <div class="com_line" style="height: 1px"></div>
            <div class="full_acm_activity flex_column_start_start">
              <div
                class="sele_goods"
                style="width: 100%"
                v-for="(item, index) in goodsInfo"
                :key="index"
              >
                <div class="goods_info flex_row_between_start">
                  <div class="goods_info_left flex_row_start_start">
                    <div class="goods_img_wrap flex_row_center_center">
                      <img :src="item.goodsImage" alt="" class="goods_img" />
                    </div>
                    <p class="goods_name">{{ item.goodsName }}</p>
                  </div>
                </div>
                <div v-if="item.productList != undefined && item.productList.length > 0">
                  <BasicTable
                    :ellipsis="false"
                    :canResize="false"
                    :bordered="true"
                    :columns="columns_spec"
                    :dataSource="item.productList"
                    :pagination="false"
                    rowKey="productId"
                  ></BasicTable>
                </div>
              </div>
            </div>
          </div>
        </Spin>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'SpellGroupToView',
  };
</script>
<script setup>
  import { ref, onMounted } from 'vue';
  import { Spin } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import CommonTitle from '/@/components/CommonTitle/index.vue';
  import { useRoute } from 'vue-router';
  import { BasicTable } from '/@/components/Table';
  import { getSpellDetailApi } from '/@/api/promotion/spell_group';
  import { failTip } from '/@/utils/utils';

  const route = useRoute();

  const spinning = ref(true);

  // 参数

  const spell_info = ref([
    {
      title: '活动名称',
      name: 'spellName',
      content: '',
      text: '',
    },
    {
      title: '活动时间',
      name: 'startTime',
      content: '',
      text: '',
    },
    {
      title: '活动标签',
      name: 'spellLabelName',
      content: '',
      text: '',
    },
    {
      title: '参团人数',
      name: 'requiredNum',
      content: '',
      text: '人',
    },
    {
      title: '拼团有效期',
      name: 'cycle',
      content: '',
      text: '小时',
    },
    {
      title: '限购数量',
      name: 'buyLimit',
      content: '',
      text: '件',
    },
    {
      title: '模拟成团',
      name: 'isSimulateGroupValue',
      content: '',
      text: '',
    },
    {
      title: '团长优惠',
      name: 'leaderIsPromotionValue',
      content: '',
      text: '',
    },
  ]);

  const detail = ref({}); //拼团详情

  const goodsInfo = ref([]); //拼团商品信息

  const columns_specs = ref([
    {
      title: `SKU规格`,
      dataIndex: 'specValues',
      align: 'center',
      width: 100,
      customRender: (text) => {
        return text&&text.length>0 ? text : `默认`;
      },
    },
    {
      title: `原价(¥)`,
      dataIndex: 'productPrice',
      align: 'center',
      width: 100,
    },
    {
      title: `库存`,
      dataIndex: 'stock',
      align: 'center',
      width: 100,
    },
    {
      title: `拼团价(¥)`,
      dataIndex: 'spellPrice',
      align: 'center',
      width: 100,
    },
    {
      title: `拼团库存`,
      dataIndex: 'spellStock',
      align: 'center',
      width: 100,
    },
  ]);

  const columns_spec = ref([]);

  const get_detail = async () => {
    try {
      spinning.value = true;
      let res = await getSpellDetailApi({ spellId: route.query?.id });
      if (res.state == 200) {
        spinning.value = false;
        if (res.data.leaderIsPromotion == 1) {
          //开启团长优惠，商品信息需要显示团长优惠价
          for (let i in columns_specs.value) {
            if (columns_specs.value[i].dataIndex == 'spellPrice') {
              columns_specs.value.splice(i + 1, 0, {
                title: `团长优惠价(¥)}`,
                dataIndex: 'leaderPrice',
                align: 'center',
                width: 100,
              });
              break;
            }
          }
        }
        spell_info.value.forEach((item) => {
          if (item.name == 'startTime') {
            item.content = res.data[item.name] + ' ~ ' + res.data.endTime;
          } else {
            item.content = res.data[item.name];
          }
        });
        detail.value = res.data;
        goodsInfo.value = res.data.goodsList;
        columns_spec.value = columns_specs.value
      } else {
        failTip(res.msg);
      }
    } catch (error) {}
  };

  onMounted(() => {
    get_detail();
  });
</script>
<style lang="less" scoped>
  @import './style/view_spell_group.less';

  p {
    margin-bottom: 0;
  }

  .view_coupon {
    padding: 10px;

    .view_coupon_back {
      width: 100%;
      min-height: calc(100vh - @header-height - 20px);
      padding: 10px;
      background-color: #fff;
    }
  }
</style>
