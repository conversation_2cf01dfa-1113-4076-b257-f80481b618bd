<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <a-spin :spinning="spinning">
        <SldComHeader
          :type="2"
          :title="'积分抵现设置'"
          :tipData="[
            '开启积分抵现后，买家下单时可以使用积分抵扣部分现金',
            '抵扣的现金结算时平台会支付给商家',
          ]"
        />
        <StandardTableRow
          width="100%"
          :tipFlag="needTipFlag"
          :data="rowData"
          @callback-event="callbackEvent"
          @submit-event="submitEvent"
        />
      </a-spin>
    </div>
  </div>
</template>
<script>
  import { defineComponent, ref, onMounted } from 'vue';
  import { Spin } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import StandardTableRow from '/@/components/StandardTableRow/index.vue';
  import { getSettingListApi, getSettingUpdateApi } from '/@/api/common/common';
  import { failTip, sucTip } from '/@/utils/utils';

  export default defineComponent({
    name: 'PointSetting',
    components: {
      [Spin.name]: Spin,
      SldComHeader,
      StandardTableRow,
    },
    setup() {
      const spinning = ref(false);
      const rowData = ref([]);
      const clickEvent = ref(false); //事件防抖动
      const needTipFlag = ref(false);
      const originIntegralConversionRatio = ref(0); //原始的积分换算比例

      const curIntegralConversionRatio = ref(0); //最新的积分换算比例

      //获取站点基本配置
      const get_base_site = async () => {
        spinning.value = true;
        const res = await getSettingListApi({
          str: 'integral_cash_out_is_enable,integral_conversion_ratio,integral_max_deduct_rate,integral_use_lowest_amount',
        });
        if (res.state == 200 && res.data) {
          let data = [];
          res.data.map((item) => {
            if (item.type == 1) {
              if (item.name == 'integral_conversion_ratio') {
                data.push({
                  type: 'inputnum',
                  label: item.title,
                  key: item.name,
                  placeholder: '请输入积分换算比例',
                  width: 300,
                  min: 1,
                  max: 99999999,
                  initValue: '',
                  value: item.value,
                  inputType: 'text',
                  callback: true,
                  desc: item.description,
                  rules: [
                    {
                      required: true,
                      message: '请输入积分换算比例',
                    },
                  ],
                });
                originIntegralConversionRatio.value = item.value * 1;
                curIntegralConversionRatio.value = originIntegralConversionRatio.value;
              } else if (item.name == 'integral_max_deduct_rate') {
                data.push({
                  type: 'inputnum',
                  label: item.title,
                  key: item.name,
                  placeholder: '请输入最高抵现比例',
                  width: 300,
                  min: 1,
                  max: 100,
                  initValue: '',
                  value: item.value,
                  inputType: 'text',
                  callback: true,
                  desc: item.description,
                  rules: [
                    {
                      required: true,
                      message: '请输入最高抵现比例',
                    },
                  ],
                });
              } else if (item.name == 'integral_use_lowest_amount') {
                data.push({
                  type: 'inputnum',
                  label: item.title,
                  key: item.name,
                  placeholder: '请输入积分最低使用金额',
                  width: 300,
                  min: 1,
                  max: 999999,
                  initValue: '',
                  value: item.value,
                  inputType: 'text',
                  callback: true,
                  desc: item.description,
                  rules: [
                    {
                      required: true,
                      message: '请输入积分最低使用金额',
                    },
                  ],
                });
              } else {
                data.push({
                  type: 'inputnum',
                  label: item.title,
                  key: item.name,
                  placeholder: '请输入' + item.title,
                  width: 300,
                  initValue: '',
                  value: item.value,
                  inputType: 'text',
                  callback: true,
                  desc: item.description,
                });
              }
            } else if (item.type == 4) {
              data.push({
                type: 'switch',
                label: item.title,
                key: item.name,
                width: 300,
                desc_width: 300,
                initValue: '',
                value: item.value,
                checkedValue: '1',
                unCheckedValue: '0',
                callback: true,
                desc: item.description,
              });
            }
          });
          if (data.length > 0) {
            data.push({
              type: 'button',
              width: 300,
              showSubmit: true,
              submitText: '保存',
              showCancle: false,
              tip: {
                title: '修改积分换算比例会导致之前的积分商品全部下架，确定修改吗？',
              },
            });
          }
          rowData.value = data;
        } else {
          failTip(res.msg);
        }
        spinning.value = false;
      };

      const callbackEvent = (item) => {
        let temp = rowData.value.filter((items) => items.key == item.contentItem.key);
        if (temp.length > 0) {
          temp[0].value = item.val1;
        }
        if (temp[0].key == 'integral_conversion_ratio') {
          curIntegralConversionRatio.value = item.val1;
          needTipFlag.value =
            originIntegralConversionRatio.value == curIntegralConversionRatio.value ? false : true;
        }
      };

      const submitEvent = async (val) => {
        try {
          spinning.value = true;
          let res = await getSettingUpdateApi(val);
          if (res.state == 200) {
            spinning.value = false;
            if (val.integral_conversion_ratio != undefined && val.integral_conversion_ratio >= 0) {
              originIntegralConversionRatio.value = val.integral_conversion_ratio;
            }
            sucTip(res.msg);
          }
        } catch (error) {}
      };

      //保存站点基本配置
      const save_base_site = async (params) => {
        if (clickEvent.value) return;
        clickEvent.value = true;
        const res = await saveBasicSiteSetting(params);
        if (res.state == 200) {
          sucTip(res.msg);
        } else {
          failTip(res.msg);
        }
        clickEvent.value = false;
      };

      onMounted(() => {
        get_base_site();
      });

      return {
        spinning,
        rowData,
        clickEvent,
        get_base_site,
        callbackEvent,
        submitEvent,
        save_base_site,
        needTipFlag,
      };
    },
  });
</script>
<style lang="less" scoped>
  .common_page {
    padding: 20px;
    background: #fff;
  }
</style>
