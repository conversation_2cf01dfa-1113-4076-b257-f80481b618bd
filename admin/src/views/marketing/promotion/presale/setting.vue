<template>
  <div class="seckill_home_setting">
    <Spin :spinning="loading">
      <StandardTableRow
        width="100%"
        :tipFlag="needTipFlag"
        :data="info_data.data"
        @callback-event="callbackEvent"
        @submit-event="submitEvent"
      />
    </Spin>
  </div>
</template>
<script>
  export default {
    name: 'PresalerSetting',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { Spin } from 'ant-design-vue';
  import StandardTableRow from '/@/components/StandardTableRow/index.vue';
  import { getSettingListApi, getSettingUpdateApi } from '/@/api/common/common';
  import { failTip, sucTip } from '/@/utils/utils';

  const info_data = reactive({
    data: [],
  });

  const loading = ref(true);

  const origionPresaleIsEnable = ref(0);

  const curPresaleIsEnable = ref(0);

  const needTipFlag = ref(false);

  // 获取数据
  const get_setting = async () => {
    try {
      const res = await getSettingListApi({
        str: 'presale_is_enable,presale_compensate,deposit_order_auto_cancel_time,tail_money_order_payment_reminder,presale_order_delivery_reminder,deposit_agreement',
      });
      if (res && res.state == 200) {
        loading.value = false;
        info_data.data = [];
        for (let i in res.data) {
          if (res.data[i].type == 1) {
            let tmp_data = {
              type: 'inputnum',
              label: res.data[i].title,
              width: 300,
              desc_width: 300,
              desc: res.data[i].description,
              key: res.data[i].name,
              placeholder: '',
              min: 0,
              max: 1439, //1440为1天，小于1天
              value: res.data[i].value,
            };
            if (res.data[i].name == 'presale_compensate') {
              //预售赔偿
              tmp_data.max = 10;
            } else if (res.data[i].name == 'deposit_order_auto_cancel_time') {
              //定金订单自动取消时间
              tmp_data.min = 5;
              tmp_data.max = 1440;
            } else if (res.data[i].name == 'tail_money_order_payment_reminder') {
              //尾款订单支付提醒
              tmp_data.max = 24;
            } else if (res.data[i].name == 'presale_order_delivery_reminder') {
              //预售订单发货提醒
              tmp_data.max = 24;
            }
            info_data.data.push(tmp_data);
          } else if (res.data[i].type == 4) {
            let tmp_data = {
              type: 'switch',
              width: 300,
              desc_width: 300,
              label: res.data[i].title,
              desc: res.data[i].description,
              key: res.data[i].name,
              placeholder: '',
              value: res.data[i].value,
              checkedValue: '1',
              unCheckedValue: '0',
              callback: true,
            };
            info_data.data.push(tmp_data);
          }
          if (res.data[i].name == 'presale_is_enable') {
            origionPresaleIsEnable.value = res.data[i].value == 1 ? true : false;
            curPresaleIsEnable.value = origionPresaleIsEnable.value;
          }
        }
        if (info_data.data.length > 0) {
          info_data.data.push({
            type: 'button',
            width: 300,
            desc_width: 300,
            showSubmit: true,
            submitText: '保存',
            showCancle: false,
            tip: {
              title: '修改预售活动开关会导致正在进行中的活动失效，确定修改吗？',
            },
          });
        }
      }
    } catch (error) {}
  };

  // 更新数据
  const callbackEvent = (item) => {
    let temp = info_data.data.filter((item) => item.key == item.key);
    if (temp.length > 0) {
      temp[0].value = item.val1;
    }
    if (temp[0].key == 'presale_is_enable') {
      curPresaleIsEnable.value = item.val1 == 1 ? true : false;
      needTipFlag.value = origionPresaleIsEnable.value == curPresaleIsEnable.value ? false : true;
    }
  };

  // 点击保存
  const submitEvent = async (val) => {
    try {
      for (let i in val) {
        if (i != 'presale_is_enable') {
          if (val[i] == '' || val[i] == null) {
            val[i] = 0;
          }
          if (i == 'deposit_order_auto_cancel_time' && !val[i]) {
            failTip('定金订单自动取消时间必填～');
            return false;
          }
        }
      }
      loading.value = true;
      let res = await getSettingUpdateApi(val);
      if (res.state == 200) {
        loading.value = false;
        origionPresaleIsEnable.value = curPresaleIsEnable.value;
        needTipFlag.value = false;
        sucTip(res.msg);
        get_setting();
      }
    } catch (error) {}
  };

  onMounted(() => {
    get_setting();
  });
</script>
<style lang="less">
  .seckill_home_setting {
    min-height: 300px;
  }
</style>
