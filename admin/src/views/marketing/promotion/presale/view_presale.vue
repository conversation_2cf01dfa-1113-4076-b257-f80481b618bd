<template>
  <div class="section_padding view_presale_group">
    <div class="section_padding_back full_activity">
      <SldComHeader title="预售详情" :back="true" />
      <div class="com_line" style="height: 1px"></div>
      <Spin :spinning="spinning">
        <div class="height_detail">
          <CommonTitle
            text="活动基本信息"
            :describe="
              route.query?.type == 1
                ? '温馨提示：若因商家问题导致无法发货，需要对会员进行赔偿，赔偿金额为定金的' +
                  compensateRate +
                  '倍。'
                : ''
            "
          />
          <template v-for="(item, index) in presale_info" :key="index">
            <div class="full_acm_activity flex_column_start_start" v-if="item.show">
              <div class="item flex_row_start_center">
                <div class="left">
                  <span style="color: #ff1515">*</span>
                  {{ item.title }}
                </div>
                <div class="right"> {{ item.content }}{{ item.text }} </div>
              </div>
            </div>
          </template>
          <CommonTitle text="商品信息" style="margin-top: 10px" />
          <div class="com_line" style="height: 1px"></div>
          <div class="full_acm_activity flex_column_start_start">
            <div
              class="sele_goods"
              style="width: 100%"
              v-for="(item, index) in goodsInfo"
              :key="index"
            >
              <div class="goods_info flex_row_between_start">
                <div class="goods_info_left flex_row_start_start">
                  <div class="goods_img_wrap flex_row_center_center">
                    <img :src="item.goodsImage" alt="" class="goods_img" />
                  </div>
                  <p class="goods_name">{{ item.goodsName }}</p>
                </div>
              </div>
              <div v-if="item.productList != undefined && item.productList.length > 0">
                <BasicTable
                  :ellipsis="false"
                  :canResize="false"
                  :bordered="true"
                  :columns="columns_spec"
                  :dataSource="item.productList"
                  :pagination="false"
                  rowKey="productId"
                ></BasicTable>
              </div>
            </div>
          </div>
        </div>
      </Spin>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'PresaleToView',
  };
</script>
<script setup>
  import { ref, onMounted } from 'vue';
  import { Spin } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import CommonTitle from '/@/components/CommonTitle/index.vue';
  import { useRoute } from 'vue-router';
  import { BasicTable } from '/@/components/Table';
  import { getPreSellDetailApi } from '/@/api/promotion/presale';
  import { getSettingListApi } from '/@/api/common/common';
  import { failTip } from '/@/utils/utils';

  const route = useRoute();

  const spinning = ref(true);

  const compensateRate = ref(0); //赔偿倍数

  // 参数

  const presale_info = ref([
    {
      title: '活动名称',
      name: 'presellName',
      content: '',
      text: '',
      show: true,
    },
    {
      title: '活动标签',
      name: 'presellLabelName',
      content: '',
      text: '',
      show: true,
    },
    {
      title: '定金时间',
      name: 'startTime',
      content: '',
      text: '',
      show: route.query?.type == 1 ? true : false,
    },
    {
      title: '尾款时间',
      name: 'remainStartTime',
      content: '',
      text: '',
      show: route.query?.type == 1 ? true : false,
    },
    {
      title: '活动时间',
      name: 'startTime',
      content: '',
      text: '',
      show: route.query?.type == 2 ? true : false,
    },
    {
      title: '发货时间',
      name: 'deliverTime',
      content: '',
      text: '',
      show: true,
    },
    {
      title: '限购件数',
      name: 'buyLimit',
      content: '',
      text: '件',
      show: true,
    },
  ]);

  const detail = ref({}); //拼团详情

  const goodsInfo = ref([]); //拼团商品信息

  const columns_specs = ref([
    {
      title: `SKU规格`,
      dataIndex: 'specValues',
      align: 'center',
      width: 100,
      customRender: (text) => {
        return text ? text : `默认`;
      },
    },
    {
      title: `原价(¥)`,
      dataIndex: 'productPrice',
      align: 'center',
      width: 100,
    },
    {
      title: `库存`,
      dataIndex: 'stock',
      align: 'center',
      width: 100,
    },
    {
      title: `预售价格(￥)`,
      dataIndex: 'presellPrice',
      align: 'center',
      width: 100,
    },
    {
      title: `预售定金(￥)`,
      dataIndex: 'firstMoney',
      align: 'center',
      width: 100,
    },
    {
      title: `定金膨胀(￥)`,
      dataIndex: 'firstExpand',
      align: 'center',
      width: 100,
    },
    {
      title: `预售库存`,
      dataIndex: 'presellStock',
      align: 'center',
      width: 100,
    },
  ]);

  const columns_specs_all = ref([
    {
      title: `SKU规格`,
      dataIndex: 'specValues',
      align: 'center',
      width: 100,
      customRender: (text) => {
        return text ? text : `默认`;
      },
    },
    {
      title: `原价(¥)`,
      dataIndex: 'productPrice',
      align: 'center',
      width: 100,
    },
    {
      title: `库存`,
      dataIndex: 'stock',
      align: 'center',
      width: 100,
    },
    {
      title: `预售价格(￥)`,
      dataIndex: 'presellPrice',
      align: 'center',
      width: 100,
    },
    {
      title: `预售库存`,
      dataIndex: 'presellStock',
      align: 'center',
      width: 100,
    },
  ]);

  const columns_spec = ref([]);

  // 获取详情数据
  const get_detail = async () => {
    try {
      spinning.value = true;
      let res = await getPreSellDetailApi({ presellId: route.query?.id });
      if (res.state == 200) {
        spinning.value = false;
        presale_info.value.forEach((item) => {
          if (item.name == 'startTime') {
            item.content = res.data[item.name] + ' ~ ' + res.data.endTime;
          } else if (item.name == 'remainStartTime') {
            item.content = res.data[item.name] + ' ~ ' + res.data.remainEndTime;
          } else {
            item.content = res.data[item.name];
          }
        });
        detail.value = res.data;
        goodsInfo.value = res.data.goodsList;
        if (route.query?.type == 1) {
          columns_spec.value = JSON.parse(JSON.stringify(columns_specs.value));
        } else {
          columns_spec.value = JSON.parse(JSON.stringify(columns_specs_all.value));
        }
      } else {
        failTip(res.msg);
      }
    } catch (error) {}
  };
  //获取系统配置(预售活动赔偿倍数)
  const getSetting = async () => {
    try {
      let res = await getSettingListApi({ str: 'presale_compensate' });
      if (res.state == 200) {
        compensateRate.value = res.data[0].value;
      } else {
        failTip(res.msg);
      }
    } catch (error) {}
  };

  onMounted(() => {
    getSetting();
    get_detail();
  });
</script>
<style lang="less" scoped>
  @import './style/view_presale_group.less';

  p {
    margin-bottom: 0;
  }

  .view_coupon {
    padding: 10px;

    .view_coupon_back {
      width: 100%;
      min-height: calc(100vh - @header-height - 20px);
      padding: 10px;
      background-color: #fff;
    }
  }
</style>
