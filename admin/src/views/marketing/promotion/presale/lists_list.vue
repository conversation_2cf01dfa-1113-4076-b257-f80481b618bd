<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader title="预售" />
      <Tabs type="card" v-model:activeKey="activeKey">
        <TabPane key="1" tab="定金预售">
          <PresaleLists  class="section_padding_tab_top"/>
        </TabPane>
        <TabPane key="2" tab="全款预售">
          <PresaleListsAll  class="section_padding_tab_top"/>
        </TabPane>
        <TabPane key="3" tab="活动标签">
          <LabelLists></LabelLists>
        </TabPane>
        <TabPane key="4" tab="预售设置">
          <Setting  class="section_padding_tab_top"></Setting>
        </TabPane>
        <TabPane key="5" tab="风格配置">
          <RadioGroup class="RadioGroup_back section_padding_tab_top" v-model:value="RadioGroupValue">
            <!-- dev_mobile-start -->
            <RadioButton value="1">移动端风格</RadioButton>
            <!-- dev_mobile-end -->
            <!-- dev_pc-start -->
            <RadioButton value="2">PC端风格</RadioButton>
            <!-- dev_pc-end -->
          </RadioGroup>
          <!-- dev_mobile-start -->
          <PresellDiyStyleM v-if="activeKey == '5' && RadioGroupValue == '1'" />
          <!-- dev_mobile-end -->
          <!-- dev_pc-start -->
          <PresellDiyStylePc v-if="activeKey == '5' && RadioGroupValue == '2'" />
          <!-- dev_pc-end -->
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'PresaleGroup',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { Tabs, TabPane, message, RadioGroup, RadioButton } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import PresaleLists from './presale_lists.vue';
  import PresaleListsAll from './presale_lists_all.vue';
  import LabelLists from './label_lists.vue';
  // dev_mobile-start
  import PresellDiyStyleM from './diy_style_m.vue';
  // dev_mobile-end
  // dev_pc-start
  import PresellDiyStylePc from './diy_style_pc.vue';
  // dev_pc-end
  import Setting from './setting.vue';

  const activeKey = ref('1');

  const RadioGroupValue = ref('1');

  onMounted(() => {
    // dev_mobile-start
    RadioGroupValue.value = '1'
    return
    // dev_mobile-end
    // dev_pc-start
    RadioGroupValue.value = '2'
    // dev_pc-end
  });
</script>
<style lang="less">
  .RadioGroup_back {
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within {
      box-shadow: none;
    }
  }
</style>
