<template>
  <Spin :spinning="spinning">
    <div class="presale_diy_style">
      <div class="diy_style_list flex_row_start_start" style="flex-wrap: wrap">
        <div class="" v-for="(item, index) in color_list" :key="index">
          <div
            class="flex_row_center_center diy_style_item"
            :class="{ active: color_index == index }"
            @click="selectColor(index)"
          >
            <div class="diy_style_color flex_row_center_center">
              <div class="diy_style_color_left" :style="{ background: item.mainColor }"></div>
              <div class="diy_style_color_right" :style="{ background: item.subColor }"></div>
            </div>
            <div class="diy_style_name">{{ item.name }}</div>
            <img
              src="@/assets/images/diy_style_checked.png"
              class="diy_style_checked"
              alt=""
              v-if="color_index == index"
            />
          </div>
        </div>
        <div
          @click="selectAutoColor"
          class="flex_row_center_center diy_style_item"
          :class="{ active: color_index == color_list.length }"
        >
          <div
            class="diy_style_color flex_row_center_center"
            v-if="color_auto.mainColor && color_auto.mainColor != 'rgba(255,255,255,1)'"
          >
            <div class="diy_style_color_left" :style="{ background: color_auto.mainColor }"></div>
            <div class="diy_style_color_right" :style="{ background: color_auto.subColor }"></div>
          </div>
          <div class="diy_style_auto_name" v-else>自定义颜色选择</div>
          <div
            class="diy_style_name"
            v-if="color_auto.mainColor && color_auto.mainColor != 'rgba(255,255,255,1)'"
          >
            重选
          </div>
          <div class="diy_style_auto_arrow" v-else> &gt; </div>
          <img
            class="diy_style_checked"
            src="@/assets/images/diy_style_checked.png"
            alt=""
            v-if="color_index == color_list.length"
          />
        </div>
      </div>
      <div class="prew_tips"> 颜色说明： 从左至右颜色依次为·主色、辅助色 </div>
      <div class="prew_title"> 图片预览 </div>
      <div class="flex_row_start_center prew_list">
        <div
          class="prew_item prew_item_goods"
          :style="{ backgroundImage: `url('${diy_style_goods}')` }"
        >
          <div
            class="flex_row_between_center diy_style_top"
            :style="{
              backgroundColor:
                color_index < color_list.length
                  ? color_list[color_index].mainColor
                  : color_auto.mainColor,
              backgroundImage: `url('${diy_style_goods_bg}')`,
            }"
          >
            <div class="flex_column_start_center diy_style_top_left">
              <div>预售价:</div>
              <div>
                <AliSvgIcon iconName="iconhuo" width="15px" height="15px" fillColor="#FFFFFF" />
                <span>￥</span>
                <span>50</span>
              </div>
              <div>原价:<span>￥69</span></div>
            </div>
            <div class="flex_column_start_center diy_style_top_right">
              <div class="flex_row_center_center"> 预售定金<span>￥</span><span>12</span> </div>
              <div
                :style="{
                  color:
                    color_index < color_list.length
                      ? color_list[color_index].mainColor
                      : color_auto.mainColor,
                }"
              >
                活动结束时间：2023-04-10 09:45
              </div>
            </div>
          </div>
          <div
            class="diy_style_cart_num"
            :style="{
              color:
                color_index < color_list.length
                  ? color_list[color_index].mainColor
                  : color_auto.mainColor,
              borderColor:
                color_index < color_list.length
                  ? color_list[color_index].mainColor
                  : color_auto.mainColor,
            }"
          >
            3
          </div>
          <div
            class="diy_style_buy"
            :style="{
              background:
                color_index < color_list.length
                  ? color_list[color_index].subLinearColor
                  : color_auto.subLinearColor,
            }"
          >
            立即付定金
          </div>
        </div>
        <div
          class="prew_item prew_item_confirm"
          :style="{ backgroundImage: `url('${diy_style_pop}')` }"
        >
          <div
            class="flex_row_end_center diy_style_top_price"
            :style="{
              color:
                color_index < color_list.length
                  ? color_list[color_index].mainColor
                  : color_auto.mainColor,
            }"
          >
            <div class="flex_row_start_start">￥<span>12</span>.00 </div>
            <div
              :style="{
                background:
                  color_index < color_list.length
                    ? color_list[color_index].mainColor
                    : color_auto.mainColor,
              }"
              >预售</div
            >
          </div>
          <div class="flex_row_start_center diy_style_type_list">
            <div
              class="diy_style_type_item"
              :style="{
                color:
                  color_index < color_list.length
                    ? color_list[color_index].mainColor
                    : color_auto.mainColor,
                background:
                  color_index < color_list.length
                    ? color_list[color_index].mainColor
                      ? color_list[color_index].mainColor.indexOf('#') == 0
                        ? color_list[color_index].mainColor + '1A'
                        : color_list[color_index].mainColor
                            .replace(',1)', ',.1)')
                            .replace(',.75)', ',.1)')
                      : '#F6F6F6'
                    : color_auto.mainColor
                    ? color_auto.mainColor.indexOf('#') == 0
                      ? color_auto.mainColor + '1A'
                      : color_auto.mainColor.replace(',1)', ',.1)').replace(',.75)', ',.1)')
                    : '#F6F6F6',
                borderColor:
                  color_index < color_list.length
                    ? color_list[color_index].mainColor
                    : color_auto.mainColor,
              }"
              >原木色大号</div
            >
            <div class="diy_style_type_item">原木色小号</div>
          </div>
          <div
            class="diy_style_buy"
            :style="{
              background:
                color_index < color_list.length
                  ? color_list[color_index].subLinearColor
                  : color_auto.subLinearColor,
            }"
            >立即付定金</div
          >
        </div>
        <div
          class="prew_item prew_item_list"
          :style="{ backgroundImage: `url('${diy_style_list}')` }"
        >
          <div
            class="flex_row_start_end top_title"
            :style="{
              backgroundColor:
                color_index < color_list.length
                  ? color_list[color_index].mainColor
                  : color_auto.mainColor,
            }"
          >
            <span>&lt;</span>
            <span>好物预售</span>
            <img class="top_title_info" src="@/assets/images/marketing/top_system_info.png" />
            <img class="top_title_btn" src="@/assets/images/marketing/top_right_btn.png" />
          </div>
          <div class="flex_row_start_center top_nav">
            <span
              :style="{
                color:
                  color_index < color_list.length
                    ? color_list[color_index].mainColor
                    : color_auto.mainColor,
                borderColor:
                  color_index < color_list.length
                    ? color_list[color_index].mainColor
                    : color_auto.mainColor,
              }"
              >首页</span
            >
            <span>好物预售</span>
          </div>
          <div :key="index" class="middle_info" v-for="(item, index) in diy_presale_reduction">
            <div class="flex_row_start_center middle_price">
              <div
                :style="{
                  color:
                    color_index < color_list.length
                      ? color_list[color_index].mainColor
                      : color_auto.mainColor,
                }"
                >￥<span>{{ item.price }}</span
                >.00</div
              >
              <div>￥{{ item.origin }}</div>
            </div>
            <div class="flex_row_start_center middle_group">
              <div
                class="flex_row_center_center"
                :style="{
                  borderColor:
                    color_index < color_list.length
                      ? color_list[color_index].mainColor
                      : color_auto.mainColor,
                }"
              >
                <AliSvgIcon
                  iconName="iconhuo"
                  width="18px"
                  height="18px"
                  :fillColor="
                    color_index < color_list.length
                      ? color_list[color_index].mainColor
                      : color_auto.mainColor
                  "
                />
                <span>已预定{{ item.num }}人</span>
              </div>
              <div
                :style="{
                  backgroundColor:
                    color_index < color_list.length
                      ? color_list[color_index].mainColor
                      : color_auto.mainColor,
                }"
              >
                立即预定
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="m_diy_bottom_wrap" :style="{ position: 'fixed', left: getRealWidth + 10 + 'px' }">
        <div class="add_goods_bottom_btn add_goods_bottom_btn_sel" @click="handleSaveAllData">
          保存
        </div>
      </div>
      <SldModal
        :width="500"
        title="自定义颜色选择"
        :visible="modalVisible"
        :content="content_color"
        :showFoot="true"
        :confirmBtnLoading="false"
        @cancle-event="handleModalCancle"
        @confirm-event="handleModalConfirm"
      />
    </div>
  </Spin>
</template>
<script>
  export default {
    name: 'LadderDiyStyleM',
  };
</script>
<script setup>
  import { ref, onMounted } from 'vue';
  import { Spin } from 'ant-design-vue';
  import SldModal from '/@/components/SldModal/index.vue';
  import { getSettingListApi, getSettingUpdateApi } from '/@/api/common/common';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import diy_style_goods from '/@/assets/images/marketing/presell/diy_style_goods.png';
  import diy_style_goods_bg from '/@/assets/images/marketing/presell/diy_style_goods_bg.png';
  import diy_style_pop from '/@/assets/images/marketing/presell/diy_style_pop.png';
  import diy_style_list from '/@/assets/images/marketing/presell/diy_style_list.png';
  import { failTip, sucTip } from '/@/utils/utils';

  const { getRealWidth } = useMenuSetting();

  const color_index = ref(0);

  const spinning = ref(true); //loading

  const content_color = ref([]);

  const diy_presale_reduction = ref([
    { price: 38, origin: 49, num: 2 },
    { price: 128, origin: 160, num: 5 },
    { price: 12, origin: 18, num: 5 },
    { price: 1700, origin: 2000, num: 1 },
  ]);

  const color_list = ref([
    //颜色列表
    {
      mainColor: '#ED0775',
      mainLinearColor: 'linear-gradient(90deg, #ED0775BF, #ED0775)',
      subColor: '#FF8622',
      subLinearColor: 'linear-gradient(90deg, #FF8622BF, #FF8622)',
      name: '默认色',
    },
    {
      mainColor: '#39BC17',
      mainLinearColor: 'linear-gradient(90deg, #39BC17BF, #39BC17)',
      subColor: '#4E4E61',
      subLinearColor: 'linear-gradient(90deg, #4E4E61BF, #4E4E61)',
      name: '翡翠绿',
    },
    {
      mainColor: '#E84165',
      mainLinearColor: 'linear-gradient(90deg, #E84165BF, #E84165)',
      subColor: '#F09B38',
      subLinearColor: 'linear-gradient(90deg, #F09B38BF, #F09B38)',
      name: '雅致粉',
    },
    {
      mainColor: '#884CFF',
      mainLinearColor: 'linear-gradient(90deg, #884CFFBF, #884CFF)',
      subColor: '#4E4E61',
      subLinearColor: 'linear-gradient(90deg, #4E4E61BF, #4E4E61)',
      name: '魅力紫',
    },
    {
      mainColor: '#F5BF41',
      mainLinearColor: 'linear-gradient(90deg, #F5BF41BF, #F5BF41)',
      subColor: '#ED682E',
      subLinearColor: 'linear-gradient(90deg, #ED682EBF, #ED682E)',
      name: '经典黄',
    },
    {
      mainColor: '#ED682E',
      mainLinearColor: 'linear-gradient(90deg, #ED682EBF, #ED682E)',
      subColor: '#F5BF41',
      subLinearColor: 'linear-gradient(90deg, #F5BF41BF, #F5BF41)',
      name: '活力橙',
    },
    {
      mainColor: '#1E78F5',
      mainLinearColor: 'linear-gradient(90deg, #1E78F5BF, #1E78F5)',
      subColor: '#534E61',
      subLinearColor: 'linear-gradient(90deg, #534E61BF, #534E61)',
      name: '天空蓝',
    },
    {
      mainColor: '#1E1C1B',
      mainLinearColor: 'linear-gradient(90deg, #1E1C1BBF, #1E1C1B)',
      subColor: '#F09B38',
      subLinearColor: 'linear-gradient(90deg, #F09B38BF, #F09B38)',
      name: '雅酷黑',
    },
  ]);

  const color_auto = ref({
    //自定义颜色
    mainColor: '', //主色
    mainLinearColor: '', //主色渐变色
    subColor: '', //辅助色
    subLinearColor: '', //辅助渐变色
  });

  const modalVisible = ref(false);

  const get_style_setting = async () => {
    try {
      let res = await getSettingListApi({ str: 'mobile_presell_mall_style' });
      if (res.state == 200 && res.data) {
        let data = !(res.data[0] && res.data[0].value) ? {} : JSON.parse(res.data[0].value);
        if (Object.keys(data).length > 0) {
          let colorIndex = color_list.value.length;
          let mainColor =
            data.mainColor != undefined
              ? data.mainColor == '#fff'
                ? '#FFFFFF'
                : data.mainColor
              : '';
          let mainLinearColor =
            data.mainLinearColor != undefined
              ? data.mainColor == '#fff'
                ? '#FFFFFF'
                : data.mainLinearColor
              : '';
          let subColor =
            data.subColor != undefined ? (data.subColor == '#fff' ? '#FFFFFF' : data.subColor) : '';
          let subLinearColor =
            data.subLinearColor != undefined
              ? data.subColor == '#fff'
                ? '#FFFFFF'
                : data.subLinearColor
              : '';
          if (mainColor && mainLinearColor && subColor && subLinearColor) {
            for (var index = 0; index < color_list.value.length; index++) {
              if (
                color_list.value[index].mainColor == mainColor &&
                color_list.value[index].mainLinearColor == mainLinearColor &&
                color_list.value[index].subColor == subColor &&
                color_list.value[index].subLinearColor == subLinearColor
              ) {
                colorIndex = index;
                break;
              }
            }
            if (colorIndex == color_list.value.length) {
              colorIndex = color_list.value.length;
              color_auto.value.mainColor = mainColor;
              color_auto.value.mainLinearColor = mainLinearColor;
              color_auto.value.subColor = subColor;
              color_auto.value.subLinearColor = subLinearColor;
            }
          }
          color_index.value = colorIndex;
        }
      } else {
        failTip(res.msg);
      }
      spinning.value = false;
    } catch (error) {
      spinning.value = false;
    }
  };

  const selectAutoColor = () => {
    content_color.value = [];
    let content_info = [
      {
        type: 'more_color_picker',
        label: `主色选择`, //主色选择
        name: 'mainColor',
        initialValue: color_auto.value.mainColor ? color_auto.value.mainColor : '#fff',
        is_show: false,
      },
      {
        type: 'more_color_picker',
        label: `辅助色选择`, //主色选择
        name: 'subColor',
        initialValue: color_auto.value.subColor ? color_auto.value.subColor : '#fff',
        is_show: false,
      },
    ];
    content_color.value = content_info;
    modalVisible.value = true;
  };

  const handleModalCancle = () => {
    content_color.value = [];
    modalVisible.value = false;
  };

  const handleModalConfirm = (val) => {
    let resetFlag = 0;
    color_auto.value.mainColor = val.mainColor;
    color_auto.value.subColor = val.subColor;
    color_auto.value.mainLinearColor =
      'linear-gradient(90deg, ' +
      val.mainColor.replace(',1)', ',.75)') +
      ', ' +
      val.mainColor +
      ')';
    color_auto.value.subLinearColor =
      'linear-gradient(90deg, ' + val.subColor.replace(',1)', ',.75)') + ', ' + val.subColor + ')';
    for (let key in color_auto.value) {
      if (color_auto.value[key] == 'rgba(255,255,255,1)' || !color_auto.value[key]) {
        resetFlag += 1;
      }
    }
    color_index.value = resetFlag == 1 ? 0 : color_list.value.length;
    modalVisible.value = false;
  };

  //选择颜色
  const selectColor = (index) => {
    color_index.value = index;
  };

  const handleSaveAllData = async () => {
    try {
      let params = {};
      if (color_index.value < color_list.value.length) {
        params = JSON.stringify(color_list.value[color_index.value]);
      } else {
        params = JSON.stringify(color_auto.value);
      }
      spinning.value = true;
      let res = await getSettingUpdateApi({ mobile_presell_mall_style: params });
      if (res.state == 200) {
        spinning.value = false;
        sucTip(res.msg);
      } else {
        spinning.value = false;
        failTip(res.msg);
      }
    } catch (error) {}
  };

  onMounted(() => {
    get_style_setting();
  });
</script>
<style lang="less">
  @import './style/diy_style.less';
</style>
