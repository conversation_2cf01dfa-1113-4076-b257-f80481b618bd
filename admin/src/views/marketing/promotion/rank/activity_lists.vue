<template>
  <div class="rank_activity_lists">
    <BasicTable @register="registerTable" rowKey="rankId">
      <template #tableTitle>
        <div class="toolbar">
          <div class="toolbar_btn" @click="add('', 'add','RankToAdd')">
            <AliSvgIcon iconName="iconfabu1" width="15px" height="15px" :fillColor="'#FA6F1E'" />
            <span style="margin-left: 4px">新建排行榜</span>
          </div>
          <Popconfirm
            title="关闭全部榜单后用户将无法看到，确认关闭吗？"
            @confirm="operate('', 'close')"
          >
            <div class="toolbar_btn">
              <AliSvgIcon
                iconName="iconguanbisuoyou"
                width="15px"
                height="15px"
                :fillColor="'#fa0920'"
              />
              <span style="margin-left: 4px">关闭全部榜单</span>
            </div>
          </Popconfirm>
        </div>
      </template>
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex == 'isAutoUpdateValue'">
          <div v-if="record.rankType == 4"> -- </div>
          <div v-else class="flex_row_center_center">
            {{ text }}
            <div
              class="rank_sync_icon"
              style="margin-top: 4px"
              @click="operate({ rankId: record.rankId }, 'update')"
            >
              <SyncOutlined
                title="点击更新数据"
                style="margin-left: 5px; color: #778595"
                v-if="record.isAutoUpdate == 0"
              />
            </div>
          </div>
        </template>
        <template v-if="column.dataIndex == 'state'">
          <div>
            <Switch
              @change="
                (checked) =>
                  operate(
                    {
                      rankId: record.rankId,
                      isEnable: checked,
                    },
                    'state',
                  )
              "
              :checked="text == 1 ? true : false"
            />
          </div>
        </template>
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                label: '查看',
                ifShow: record.state == 1,
                onClick: add.bind(null, record.rankId, 'view','RankToView'),
              },
              {
                label: '编辑',
                ifShow: record.state != 1,
                onClick: add.bind(null, record.rankId, 'edit','RankToEdit'),
              },
              {
                label: '删除',
                ifShow: record.state != 1,
                popConfirm: {
                  title: '删除后不可恢复，是否确定删除？',
                  placement: 'left',
                  confirm: operate.bind(null, { rankId: record.rankId }, 'del'),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
  </div>
</template>
<script>
  export default {
    name: 'RankActivityLists',
  };
</script>
<script setup>
  import { unref, reactive, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { Switch, Popconfirm } from 'ant-design-vue';
  import { SyncOutlined } from '@ant-design/icons-vue';
  import { useUserStore } from '/@/store/modules/user';
  import {
    getRankListApi,
    getCategoryListApi,
    postSwitchRankApi,
    postDelRankApi,
    getRankRefreshApi,
    getRankCloseRankApi,
  } from '/@/api/promotion/rank';
  import { failTip, list_com_page_more, sucTip } from '/@/utils/utils';
  const userStore = useUserStore();

  const router = useRouter();

  const columns = reactive({
    data: [
      {
        title: `榜单名称`,
        dataIndex: 'rankName',
        align: 'center',
        width: 100,
      },
      {
        title: `榜单分类`,
        dataIndex: 'categoryName',
        align: 'center',
        width: 100,
      },
      {
        title: `榜单类型`,
        dataIndex: 'rankTypeValue',
        align: 'center',
        width: 100,
      },
      {
        title: `创建时间`,
        dataIndex: 'createTime',
        align: 'center',
        width: 150,
        customRender: ({ text }) => {
          return text ? text : '-';
        },
      },
      {
        title: `更新时间`,
        dataIndex: 'updateTime',
        align: 'center',
        width: 150,
        customRender: ({ text }) => {
          return text ? text : '-';
        },
      },
      {
        title: `更新方式`,
        dataIndex: 'isAutoUpdateValue',
        align: 'center',
        width: 150,
      },
      {
        title: `启用状态`,
        dataIndex: 'state',
        align: 'center',
        width: 100,
      },
    ],
  });

  const searchFormSchema = reactive({
    data: [
      {
        component: 'Input',
        label: `榜单名称`,
        field: 'rankName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请输入榜单名称`,
        },
      },
      {
        component: 'Select',
        label: `榜单分类`,
        field: 'categoryId',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请选择榜单分类`,
          options: [],
        },
      },
      {
        component: 'Select',
        label: `榜单类型`,
        field: 'rankType',
        labelWidth: 90,
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请选择榜单类型`,
          options: [
            { value: '', label: `全部` },
            { value: '1', label: `畅销榜` },
            { value: '2', label: `好评榜` },
            { value: '3', label: `新品榜` },
            { value: '4', label: `自定义` },
          ],
        },
      },
    ],
  });

  // 表格数据
  const [registerTable, { reload, getForm }] = useTable({
    // 请求接口
    api: getRankListApi,
    // 表头
    columns: columns.data,
    immediate: true,
    // 点击搜索前处理的参数
    handleSearchInfoFn: (values) => {},
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: searchFormSchema.data,
    },
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    // 是否显示序号列
    showIndexColumn: true,
    // 表格右侧操作列配置
    actionColumn: {
      width: 130,
      title: '操作',
      dataIndex: 'action',
      // slots: { customRender: 'action' },
    },
  });

  //操作  type: state 是否启用榜单
  const operate = async (id, type) => {
    let param_data = {};
    let res = null;
    if (type == 'state') {
      param_data = id;
      res = await postSwitchRankApi(param_data);
    }
    if (type == 'update') {
      param_data = id;
      res = await getRankRefreshApi(param_data);
    }
    if (type == 'del') {
      param_data = id;
      res = await postDelRankApi(param_data);
    }
    if (type == 'close') {
      res = await getRankCloseRankApi();
    }
    if (res.state == 200) {
      sucTip(res.msg);
      reload();
    } else {
      failTip(res.msg);
    }
  };

  // 添加 查看
  const add = (id, type,pathName) => {
    userStore.setDelKeepAlive([pathName])
    if (id) {
      router.push({
        path: `/marketing_promotion/rank_to_${type}`,
        query: { id: id, type: type },
      });
    } else {
      router.push({
        path: `/marketing_promotion/rank_to_${type}`,
      });
    }
  };

  // 获取启用状态的排行榜分类列表
  const getCategoryList = async () => {
    const { updateSchema } = getForm();
    let res = await getCategoryListApi({ pageSize: list_com_page_more });
    if (res.state == 200) {
      let category_list = res.data;
      category_list.unshift({ categoryId: '', categoryName: '全部' });
      await updateSchema({
        field: 'categoryId',
        componentProps: {
          options: unref(category_list).map((item) => ({
            value: item.categoryId,
            label: item.categoryName,
          })),
        },
      });
    }
  };

  onMounted(() => {
    getCategoryList();
  });
</script>
<style lang="less" scoped>
  p {
    margin-bottom: 0;
  }

  .rank_activity_lists {
    .voucher_num {
      color: @primary-color;
      cursor: pointer;
    }
  }
  // .coupon_home_system_lists {
  // }
</style>
