<template>
  <div
    class="sld_det_r_item flex_row_end_center"
    :style="{
      width: '20%',
      height: height,
      borderTopWidth: '1px',
    }"
  >
    <div style="color: red" v-if="type"> * </div>
    {{ title }}
  </div>
</template>
<script>
  export default {
    name: 'RankLeft',
  };
</script>
<script setup>
  const props = defineProps({
    title: {
      type: String,
    },
    type: {
      type: Boolean,
      default: true,
    },
    height: {
      type: String,
    },
  });
</script>
<style lang="less" scoped>
  .sld_det_r_item {
    padding-right: 10px;
    border: 1px solid #f0f0f0;
    border-left: 0;
    background-color: #fff;
  }
</style>
