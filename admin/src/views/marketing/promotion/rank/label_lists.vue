<template>
  <div class="rank_label_lists">
    <BasicTable @register="registerTable" rowKey="couponId" style="padding: 0">
      <template #tableTitle>
        <div class="toolbar">
          <div class="toolbar_btn" @click="handleClick">
            <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#fff" />
            <span>新建分类</span>
          </div>
        </div>
      </template>
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex == 'publishNum'">
          <router-link :to="`/marketing_promotion/coupon_to_receive_list?id=${record.couponId}`">
            <div class="voucher_num"
              >{{ record.receivedNum }}/{{ record.usedNum }}/{{ record.publishNum }}</div
            >
          </router-link>
        </template>
        <template v-if="column.dataIndex == 'state'">
          <div>
            <Switch
              @change="
                (checked) =>
                  operate(
                    {
                      categoryId: record.categoryId,
                      isEnable: checked ? 1 : 0,
                    },
                    'switch',
                  )
              "
              :checked="text == 1 ? true : false"
            />
          </div>
        </template>
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                label: '查看榜单',
                onClick: lookOperate.bind(null, record.categoryId),
              },
              {
                label: '编辑',
                ifShow: record.state == 0,
                onClick: editLabel.bind(null, record),
              },
              {
                label: '删除',
                ifShow: record.state == 0 && record.rankNum > 0,
                tooltip: '当前分类已经关联了榜单，暂不可删除。',
              },
              {
                label: '删除',
                ifShow: record.state == 0 && record.rankNum == 0,
                popConfirm: {
                  title: '删除后不可恢复，是否确定删除？',
                  placement: 'left',
                  confirm: operate.bind(null, { categoryId: record.categoryId }, 'del'),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <SldModal
      :width="500"
      :title="title"
      :visible="modalVisible"
      :content="content"
      :showFoot="true"
      :confirmBtnLoading="modalConfirmBtnLoading"
      @cancle-event="handleModalCancle"
      @confirm-event="handleModalConfirm"
    />
  </div>
</template>
<script>
  export default {
    name: 'RankLabelLists',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { Switch } from 'ant-design-vue';
  import SldModal from '/@/components/SldModal/index.vue';
  import { validatorEmoji } from '/@/utils/validate';
  import {
    getCategoryLabelListApi,
    getAddLabelApi,
    getSwitchLabelApi,
    getDelLabelApi,
    getEditLabelApi,
  } from '/@/api/promotion/rank';
  import { failTip, sucTip } from '/@/utils/utils';

  const router = useRouter();

  // 弹框开关
  const modalVisible = ref(false);

  const type = ref('add');

  // 标题
  const title = ref('');

  const modalConfirmBtnLoading = ref(false); //modal弹框是否显示确认按钮loading，默认不显示

  const content = ref([]);

  const operateContent = ref([
    {
      type: 'input',
      label: `分类名称`,
      name: 'categoryName',
      extra: '最多输入5个字',
      initialValue: '', //默认值
      placeholder: `请输入分类名称`,
      maxLength: '5', //最多字数
      disable: false, //是否禁用
      showCount: false, //是否展示字数
      rules: [
        {
          required: true,
          message: `请输入分类名称`,
        },
        {
          validator: (rule, value) => validatorEmoji(rule, value),
        },
      ],
    },
    {
      type: 'inputnum',
      label: `排序`, //数字输入框
      name: 'sort',
      initialValue: '', //默认值
      placeholder: `请输入排序`,
      extra: `请输入0~255的数字,值越小显示越靠前`,
      disable: false, //是否禁用
      min: 0, //最小值
      max: 255, //最大值
      rules: [
        {
          required: true,
          message: `请输入排序`,
        },
      ],
    },
  ]);

  const cur_edit_id = ref(''); //id

  const columns = reactive({
    data: [
      {
        title: `分类名称`,
        dataIndex: 'categoryName',
        align: 'center',
        width: 100,
      },
      {
        title: `排序`,
        dataIndex: 'sort',
        align: 'center',
        width: 100,
      },
      {
        title: `关联榜单数量`,
        dataIndex: 'rankNum',
        align: 'center',
        width: 100,
      },
      {
        title: `启用状态`,
        dataIndex: 'state',
        align: 'center',
        width: 100,
      },
    ],
  });

  const searchFormSchema = reactive({
    data: [
      {
        component: 'Input',
        label: `分类名称`,
        field: 'categoryName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请输入分类名称`,
        },
      },
    ],
  });

  // 表格数据
  const [registerTable, { reload }] = useTable({
    // 请求接口
    api: getCategoryLabelListApi,
    // 表头
    columns: columns.data,
    immediate: true,
    // 点击搜索前处理的参数
    handleSearchInfoFn: (values) => {},
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: searchFormSchema.data,
    },
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    // 是否显示序号列
    showIndexColumn: true,
    // 表格右侧操作列配置
    actionColumn: {
      width: 130,
      title: '操作',
      dataIndex: 'action',
      // slots: { customRender: 'action' },
    },
  });

  //关闭弹框
  const handleModalCancle = () => {
    operateContent.value.map((item) => {
      item.initialValue = '';
    });
    // operateContent.value = [];
    modalVisible.value = false;
    content.value = [];
    // operateContent.value[1].content = ''
  };

  // 弹框确认
  const handleModalConfirm = async (val) => {
    try {
      let res;
      if (type.value == 'add') {
        modalConfirmBtnLoading.value = true;
        res = await getAddLabelApi(val);
      } else if (type.value == 'edit') {
        res = await getEditLabelApi({ ...val, categoryId: cur_edit_id.value });
      }
      if (res.state == 200) {
        sucTip(res.msg);
        modalConfirmBtnLoading.value = false
        modalVisible.value = false;
        content.value = [];
        operateContent.value.map((item) => {
          item.initialValue = '';
        });
        cur_edit_id.value = '';
        reload();
      }else{
        modalConfirmBtnLoading.value = false
        failTip(res.msg)
      }
    } catch (error) {
      console.info(error, 'error');
    }
  };

  // 编辑
  const editLabel = (record) => {
    content.value = [];
    operateContent.value.map((item) => {
      item.initialValue = record[item.name];
    });
    cur_edit_id.value = record.categoryId;
    content.value = operateContent.value;
    type.value = 'edit';
    title.value = '编辑分类';
    modalVisible.value = true;
  };

  //操作  type: state 是否启用榜单 del删除
  const operate = async (id, type) => {
    let param_data = {};
    let res = null;
    if (type == 'switch') {
      param_data = id;
      res = await getSwitchLabelApi(param_data);
    }
    if (type == 'del') {
      param_data = id;
      res = await getDelLabelApi(param_data);
    }
    if (res.state == 200) {
      sucTip(res.msg);
      reload();
    } else {
      failTip(res.msg);
    }
  };

  const lookOperate = (id) => {
    router.push({
      path: `/marketing_promotion/rank_to_bind`,
      query: { id: id },
    });
  };

  // 新建分类
  const handleClick = () => {
    operateContent.value.map((item) => {
      item.initialValue = '';
    });
    type.value = 'add';
    title.value = '新建分类';
    content.value = [];
    content.value = operateContent.value;
    modalVisible.value = true;
  };

  onMounted(() => {});
</script>
<style lang="less" scoped>
  p {
    margin-bottom: 0;
  }

  .rank_label_lists {
    .voucher_num {
      color: @primary-color;
      cursor: pointer;
    }

    .toolbar {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      width: 100%;
      height: 42px;
      margin-bottom: 5px;
      background: #ffe3d5;

      .toolbar_btn {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 28px;
        margin-left: 10px;
        padding: 0 7px;
        border-radius: 3px;
        background: #fff;
        cursor: pointer;

        span {
          margin-left: 5px;
        }
      }
    }
  }
  // .coupon_home_system_lists {
  // }
</style>
