<template>
  <div class="bind_rank_setting">
    <Spin :spinning="loading">
      <!-- 表格列组件-start -->
      <StandardTableRow
        width="100%"
        :data="data"
        @callback-event="callbackEvent"
        @submit-event="submitEvent"
      />
    </Spin>
    <!-- 表格列组件-start -->
    <!-- <div class="m_diy_bottom_wrap" :style="{ position: 'fixed', left: '171px' }">
      <div class="add_goods_bottom_btn add_goods_bottom_btn_sel" @click="submitEvent">
        保存
      </div>
    </div> -->
  </div>
</template>
<script>
  export default {
    name: 'RankToBind',
  };
</script>
<script setup>
  import { ref, onMounted } from 'vue';
  import StandardTableRow from '/@/components/StandardTableRow/index.vue';
  import { Spin } from 'ant-design-vue';
  import { useGlobSetting } from '/@/hooks/setting';
  import { getSettingListApi, getSettingUpdateApi } from '/@/api/common/common';
  import { sucTip } from '/@/utils/utils';

  const { apiUrl } = useGlobSetting();

  const loading = ref(true);

  const data = ref([
    {
      type: 'inputnum',
      require: true,
      label: '榜单最大可添加商品数',
      key: 'rank_max_add_goods_num',
      placeholder: '请输入榜单最大可添加商品数',
      value: '',
      min: 1,
      max: 100,
      precision: 0,
      callback: true,
      desc: '不可超过100',
    },
    {
      type: 'upload_img',
      label: '排行榜首页背景图',
      key: 'rank_background_image',
      accept: '.gif, .jpeg, .png, .jpg,',
      action: `${apiUrl}/v3/oss/admin/upload?source=setting`,
      fileList: [],
      desc: '建议上传【宽750*高354】的图片，支持gif，jpeg，jpg，png格式的图片',
      desc_width: 320,
      callback: true,
      limit: 1,
    },
  ]);

  const callbackEvent = (item) => {
    if (item.contentItem.eventType == 'change') {
      let temp = data.value.filter((items) => items.key == item.contentItem.key);
      if (temp.length > 0) {
        temp[0].fileList = item.val1.fileList;
      }
    }
  };

  const submitEvent = async (data) => {
    try {
      let param = {};
      for (let i in data) {
        if (i == 'rank_background_image') {
          if (data[i] && data[i].response && data[i].response.data) {
            param[i] = data[i].response.data.path;
          } else {
            param[i] = '';
          }
        } else {
          if (data[i]) {
            param[i] = data[i];
          } else {
            param[i] = '';
          }
        }
      }
      loading.value = true;
      let res = await getSettingUpdateApi(param);
      if (res.state == 200) {
        sucTip(res.msg);
        loading.value = false;
        getSetting();
      }
    } catch (error) {}
  };

  const getSetting = async () => {
    try {
      loading.value = true;
      let res = await getSettingListApi({
        str: 'rank_background_image,rank_max_add_goods_num',
      });
      if (res.state == 200) {
        res.data.map((item) => {
          loading.value = false;
          //处理分享礼包数据
          if (item.name == 'rank_background_image' && item.value) {
            data.value[1].fileList = [];
            let tmp_data = {};
            tmp_data.uid = item.value;
            tmp_data.name = item.value;
            tmp_data.status = 'done';
            tmp_data.url = item.imageUrl;
            tmp_data.response = {
              data: {
                url: item.imageUrl,
                path: item.value,
              },
            };
            data.value[1].fileList.push(tmp_data);
          } else if (item.name == 'rank_max_add_goods_num' && item.value) {
            data.value[0].value = item.value;
          }
        });
        if (res.data.length > 0) {
          let obj = data.value.filter((item) => item.type == 'button');
          if (obj.length > 0) {
            data.value.pop();
          }
          data.value.push({
            type: 'button',
            showSubmit: true,
            submitText: '保存',
            showCancle: false,
          });
        }
      }
    } catch (error) {}
  };

  onMounted(() => {
    getSetting();
  });
</script>
<style lang="less" scoped>
  p {
    margin-bottom: 0;
  }

  .bind_rank_setting {
    .voucher_num {
      color: @primary-color;
      cursor: pointer;
    }

    .m_diy_bottom_wrap {
      display: flex;
      position: absolute;
      z-index: 999;
      right: 10px;
      bottom: 0;
      left: 0;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      height: 60px;
      background: rgb(255 255 255 / 100%);
      box-shadow: 0 0 20px 0 rgb(187 187 187 / 15%);
    }

    .add_goods_bottom_btn {
      box-sizing: border-box;
      width: 80px;
      margin-right: 20px;
      border: 1px solid @primary-color;
      border-radius: 2px;
      color: @primary-color;
      font-size: 13px;
      line-height: 30px;
      text-align: center;
      cursor: pointer;
    }

    .add_goods_bottom_btn_sel {
      margin-right: 0;
      background: @primary-color;
      color: #fff;
    }

    .add_goods_bottom_btn_forbidden {
      margin-right: 0;
      border-color: #ddd;
      background: #ddd;
      color: #fff;
    }
  }
</style>
