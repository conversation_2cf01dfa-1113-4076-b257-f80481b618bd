<template>
  <div class="rank_list_add section_padding">
    <div class="section_padding_back">
      <SldComHeader :title="pageTitle" />
      <div class="com_line" style="height: 1px"></div>
      <Spin :spinning="loading">
        <div class="rank_list_add_from">
          <Form layout="inline" ref="formRef" :model="detail" style="justify-content: center">
            <div class="sld_det_lr_wrap">
              <div style="width: 100%; height: 20px"></div>
              <!-- 基本信息 start -->
              <div class="goods_sku_tab add_goods_wrap full_activity">
                <div class="add_rand_title_bg">
                  <span class="title">基本信息</span>
                </div>
                <!-- 榜单名称 start -->
                <div class="sld_det_lr_item_wrap flex_row_start_center">
                  <RankLeft title="榜单名称" height="76px" />
                  <div
                    class="sld_det_r_item flex_column_center_start"
                    :style="{
                      width: '80%',
                      height: '76px',
                      borderTopWidth: '1px',
                      paddingLeft: '20px',
                    }"
                  >
                    <div class="sld_det_r_text" style="width: 100%">
                      <Form.Item
                        extra="最多输入10个字"
                        name="rankName"
                        style="width: 400px"
                        :rules="rules.rankName"
                      >
                        <Input
                          :disabled="viewFlag"
                          :maxlength="10"
                          v-model:value="detail.rankName"
                          style="width: 400px"
                          placeholder="请输入榜单名称"
                        />
                      </Form.Item>
                    </div>
                  </div>
                </div>
                <!-- 榜单名称 end -->
                <!-- 关联分类 start -->
                <div class="sld_det_lr_item_wrap flex_row_start_center">
                  <RankLeft title="关联分类" height="76px" />
                  <div
                    class="sld_det_r_item flex_column_center_start"
                    :style="{
                      width: '80%',
                      height: '76px',
                      borderTopWidth: '1px',
                      paddingLeft: '20px',
                    }"
                  >
                    <div class="sld_det_r_text" style="width: 100%">
                      <Form.Item name="categoryId" style="width: 400px" :rules="rules.categoryId">
                        <Select
                          placeholder="请选择关联分类"
                          :disabled="viewFlag"
                          style="width: 400px"
                          :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                          v-model:value="detail.categoryId"
                        >
                          <Select.Option
                            v-for="(item, index) in catData"
                            :key="index"
                            :value="item.categoryId"
                            >{{ item.categoryName }}</Select.Option
                          >
                        </Select>
                      </Form.Item>
                    </div>
                  </div>
                </div>
                <!-- 关联分类 end -->
                <!-- 排序 start -->
                <div class="sld_det_lr_item_wrap flex_row_start_center">
                  <RankLeft title="排序" height="76px" />
                  <div
                    class="sld_det_r_item flex_column_center_start"
                    :style="{
                      width: '80%',
                      height: '76px',
                      borderTopWidth: '1px',
                      paddingLeft: '20px',
                    }"
                  >
                    <div class="sld_det_r_text" style="width: 100%">
                      <Form.Item
                        extra="请输入0~255的数字,值越小显示越靠前"
                        name="sort"
                        style="width: 400px"
                        :rules="rules.sort"
                      >
                        <InputNumber
                          v-model:value="detail.sort"
                          :max="255"
                          :min="0"
                          :precision="0"
                          style="width: 400px !important"
                          :disabled="viewFlag"
                          placeholder="请输入排序"
                        />
                      </Form.Item>
                    </div>
                  </div>
                </div>
                <!-- 排序 end -->
                <!-- 背景图 start -->
                <div class="sld_det_lr_item_wrap flex_row_start_center">
                  <RankLeft title="背景图" height="160px" :type="false" />
                  <div
                    class="sld_det_r_item flex_column_center_start"
                    :style="{
                      width: '80%',
                      height: '160px',
                      borderTopWidth: '1px',
                      paddingLeft: '20px',
                    }"
                  >
                    <div class="sld_det_r_text" style="width: 100%">
                      <Form.Item
                        extra="建议上传【宽750*高354】的图片，支持gif，jpeg，jpg，png格式的图片"
                        style="width: 500px"
                      >
                        <Upload
                          :maxCount="1"
                          accept=" .gif, .jpeg, .png, .jpg,"
                          name="file"
                          @click="beforeUploadClick"
                          :disabled="viewFlag"
                          :action="`${apiUrl}/v3/oss/admin/upload?source=setting`"
                          listType="picture-card"
                          :file-list="bgFileList"
                          :beforeUpload="
                            (file) => beforeUpload(file, ' .gif, .jpeg, .png, .jpg,', 20)
                          "
                          @change="(e) => handleFileChange(e)"
                          :headers="{
                            Authorization: imgToken,
                          }"
                        >
                          <div v-if="bgFileList.length < 1">
                            <PlusOutlined />
                            <div className="ant-upload-text">上传图片</div>
                          </div>
                        </Upload>
                      </Form.Item>
                    </div>
                  </div>
                </div>
                <!-- 背景图 end -->
              </div>
              <!-- 基本信息 end -->
              <div style="width: 100%; height: 20px"></div>
              <!-- 生成榜单 start -->
              <div class="goods_sku_tab add_goods_wrap full_activity">
                <div class="add_rand_title_bg">
                  <span class="title">生成榜单</span>
                </div>
                <!-- 榜单类型 start -->
                <div class="sld_det_lr_item_wrap flex_row_start_center">
                  <RankLeft title="榜单类型" height="76px" />
                  <div
                    class="sld_det_r_item flex_column_center_start"
                    :style="{
                      width: '80%',
                      height: '76px',
                      borderTopWidth: '1px',
                      paddingLeft: '20px',
                    }"
                  >
                    <div class="sld_det_r_text" style="width: 100%">
                      <Form.Item name="rankType" style="width: 400px">
                        <RadioGroup
                          v-model:value="detail.rankType"
                          :disabled="viewFlag"
                          size="small"
                          @change="rankType_change"
                        >
                          <RadioButton :value="1">畅销榜</RadioButton>
                          <RadioButton :value="2">好评榜</RadioButton>
                          <RadioButton :value="3">新品榜</RadioButton>
                          <RadioButton :value="4">自定义</RadioButton>
                        </RadioGroup>
                      </Form.Item>
                    </div>
                  </div>
                </div>
                <!-- 榜单类型 end -->
                <!-- 商品品类 start -->
                <div class="sld_det_lr_item_wrap flex_row_start_center" v-if="detail.rankType != 4">
                  <RankLeft title="商品品类" height="76px" />
                  <div
                    class="sld_det_r_item flex_column_center_start"
                    :style="{
                      width: '80%',
                      height: '76px',
                      borderTopWidth: '1px',
                      paddingLeft: '20px',
                    }"
                  >
                    <div class="sld_det_r_text" style="width: 100%">
                      <Form.Item
                        name="goodsCategoryId"
                        style="width: 400px"
                        :rules="rules.goodsCategoryId"
                      >
                        <TreeSelect
                          v-model:value="detail.goodsCategoryId"
                          :disabled="viewFlag"
                          :style="{ width: '100%' }"
                          :treeData="goodsCatData"
                          allowClear
                          placeholder="请选择生成榜单的商品分类"
                          :dropdownStyle="{ maxHeight: '300px' }"
                        />
                      </Form.Item>
                    </div>
                  </div>
                </div>
                <!-- 商品品类 end -->
                <!-- 统计时间 start -->
                <div class="sld_det_lr_item_wrap flex_row_start_center" v-if="detail.rankType != 4">
                  <RankLeft title="统计时间" height="76px" />
                  <div
                    class="sld_det_r_item flex_column_center_start"
                    :style="{
                      width: '80%',
                      height: '76px',
                      borderTopWidth: '1px',
                      paddingLeft: '20px',
                    }"
                  >
                    <div class="sld_det_r_text" style="width: 100%">
                      <Form.Item name="statsTime" style="width: 400px">
                        <RadioGroup
                          v-model:value="detail.statsTime"
                          :disabled="viewFlag"
                          size="small"
                        >
                          <Radio :value="1">近7天</Radio>
                          <Radio :value="2">近30天</Radio>
                        </RadioGroup>
                      </Form.Item>
                    </div>
                  </div>
                </div>
                <!-- 统计时间 end -->
                <!-- 计算规则 start -->
                <div class="sld_det_lr_item_wrap flex_row_start_center" v-if="detail.rankType != 4">
                  <RankLeft title="计算规则" :height="detail.rankType == 2 ? '130px' : '76px'" />
                  <div
                    class="sld_det_r_item flex_column_center_start"
                    :style="{
                      width: '80%',
                      height: detail.rankType == 2 ? '130px' : '76px',
                      borderTopWidth: '1px',
                      paddingLeft: '20px',
                    }"
                  >
                    <div class="sld_det_r_text" style="width: 100%">
                      <!-- 畅销榜 start -->
                      <Form.Item
                        name="bestSellerRankRule"
                        style="width: 400px"
                        v-if="detail.rankType == 1"
                      >
                        <RadioGroup
                          v-model:value="detail.bestSellerRankRule"
                          :disabled="viewFlag"
                          size="small"
                        >
                          <Radio :value="1">销量排行</Radio>
                          <Radio :value="2">销售额排行</Radio>
                        </RadioGroup>
                      </Form.Item>
                      <!-- 畅销榜 end -->
                      <!-- 好评榜 start -->
                      <div class="flex_column_center_start" v-if="detail.rankType == 2">
                        <Form.Item
                          name="highCommentNum"
                          style="width: 400px"
                          :rules="rules.highCommentNum"
                          class="highCommentNum_box"
                          extra="请输入1-1000的整数"
                        >
                          <div class="flex_row_start_center">
                            <span
                              style="
                                display: inline-block;
                                margin-right: 5px;
                                color: rgb(0 0 0 / 65%);
                              "
                              >好评数不低于</span
                            >
                            <InputNumber
                              v-model:value="detail.highCommentNum"
                              :max="1000"
                              :min="1"
                              :precision="0"
                              style="width: 140px !important"
                              :disabled="viewFlag"
                              placeholder="请输入好评数"
                            />
                            <span
                              style="
                                display: inline-block;
                                margin-left: 5px;
                                color: rgb(0 0 0 / 65%);
                              "
                              >条</span
                            >
                          </div>
                        </Form.Item>
                        <Form.Item
                          name="highCommentRate"
                          style="width: 400px"
                          :rules="rules.highCommentRate"
                          class="highCommentNum_box"
                          extra="请输入1-100的整数"
                        >
                          <div class="flex_row_start_center">
                            <span
                              style="
                                display: inline-block;
                                margin-right: 5px;
                                color: rgb(0 0 0 / 65%);
                              "
                              >好评率不低于</span
                            >
                            <InputNumber
                              v-model:value="detail.highCommentRate"
                              :max="100"
                              :min="1"
                              :precision="0"
                              style="width: 140px !important"
                              :disabled="viewFlag"
                              placeholder="请输入好评率"
                            />
                            <span
                              style="
                                display: inline-block;
                                margin-left: 5px;
                                color: rgb(0 0 0 / 65%);
                              "
                              >%</span
                            >
                          </div>
                        </Form.Item>
                      </div>
                      <!-- 好评榜 end -->
                      <!-- 新品榜 start -->
                      <Form.Item
                        name="newProductRankRule"
                        style="width: 550px"
                        v-if="detail.rankType == 3"
                      >
                        <RadioGroup
                          v-model:value="detail.newProductRankRule"
                          :disabled="viewFlag"
                          size="small"
                        >
                          <Radio :value="1">按照商品发布的时间降序排列</Radio>
                          <Radio :value="2">按照商品发布的时间升序排列</Radio>
                        </RadioGroup>
                      </Form.Item>
                      <!-- 新品榜 end -->
                    </div>
                  </div>
                </div>
                <!-- 计算规则 end -->
                <!-- 自动更新 start -->
                <div class="sld_det_lr_item_wrap flex_row_start_center" v-if="detail.rankType != 4">
                  <RankLeft title="自动更新" height="76px" />
                  <div
                    class="sld_det_r_item flex_column_center_start"
                    :style="{
                      width: '80%',
                      height: '76px',
                      borderTopWidth: '1px',
                      paddingLeft: '20px',
                    }"
                  >
                    <div class="sld_det_r_text" style="width: 100%">
                      <Form.Item
                        name="isAutoUpdate"
                        style="width: 550px"
                        extra="开启自动更新后榜单数据每日更新1次。关闭自动更新后榜单数据可手动进行更新。"
                      >
                        <Switch
                          :disabled="viewFlag"
                          v-model:checked="detail.isAutoUpdate"
                          :un-checked-value="0"
                          :checked-value="1"
                        />
                      </Form.Item>
                    </div>
                  </div>
                </div>
                <!-- 自动更新 end -->
                <!-- 加载榜单商品 start -->
                <div class="sld_det_lr_item_wrap flex_row_start_center" v-if="!viewFlag">
                  <RankLeft title="" height="76px" :type="false" />
                  <div
                    class="sld_det_r_item flex_column_center_start"
                    :style="{
                      width: '80%',
                      height: '76px',
                      borderTopWidth: '1px',
                      paddingLeft: '20px',
                    }"
                  >
                    <div class="sld_det_r_text" style="width: 100%">
                      <div class="rank_load_goods_btn" @click="loadGoods('btn')">
                        加载榜单商品
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 加载榜单商品 end -->
              </div>
              <!-- 生成榜单 end -->
              <!-- 榜单商品 start -->
              <div class="flex_column_start_center add_rank_goods_wrap" v-if="showLoadGoodsModule">
                <div class="add_rank_goods_title flex_row_start_center">
                  <span class="add_rank_goods_title_con">榜单商品</span>
                  <div style="width: 15px; height: 100%"></div>
                  <div
                    v-if="detail.rankType == 4 || (detail.rankType != 4 && !detail.isAutoUpdate)"
                  >
                    <span class="add_rank_goods_title_con">已选</span>
                    <span class="add_rank_goods_title_con" style="color: #ff6a12">{{
                      selectedRowKeys.length
                    }}</span>
                    <span class="add_rank_goods_title_con">款商品，</span>
                  </div>
                  <span class="add_rank_goods_title_con"
                    >最多可绑定{{ rankMaxAddGoodsNum }}款商品</span
                  >
                </div>
                <div class="" style="max-width: 980px; padding: 0 10px">
                  <BasicTable @register="registerTable">
                    <template #bodyCell="{ column, record, text }">
                      <template v-if="column.dataIndex == 'goodsImage'">
                        <div class="goods_info com_flex_row_flex_start">
                          <div class="goods_img">
                            <Popover placement="rightTop">
                              <template #content>
                                <div
                                  style="width: 200px; height: 200px; margin: 0 5px"
                                  class="flex_com_row_center"
                                >
                                  <img
                                    :src="text"
                                    alt=""
                                    style="max-width: 100%; max-height: 100%"
                                  />
                                </div>
                              </template>
                              <div class="business_load_img flex_com_row_center">
                                <img :src="text" alt="" />
                              </div>
                            </Popover>
                          </div>
                          <div class="com_flex_column_space_between goods_detail">
                            <span class="goods_name" :title="record.goodsName">
                              {{ record.goodsName }}
                            </span>
                            <span class="goods_brief" :title="record.categoryPath">
                              {{ record.categoryPath }}
                            </span>
                          </div>
                        </div>
                      </template>
                      <template v-if="column.dataIndex == 'rankReason'">
                        <div
                          class=""
                          v-if="
                            selectedRowKeys.length > 0 &&
                            selectedRowKeys.indexOf(record.goodsId) > -1 &&
                            query.type != 'view'
                          "
                        >
                          <Textarea
                            v-model:value="record.rankReason"
                            placeholder="请输入上榜理由,最多100字"
                            :rows="3"
                            :maxlength="100"
                          />
                        </div>
                        <div v-if="query.type == 'view'">
                          {{ record.rankReason }}
                        </div>
                      </template>
                      <template v-if="column.dataIndex === 'action'">
                        <TableAction
                          :actions="[
                            {
                              label: '删除',
                              popConfirm: {
                                title: '删除后不可恢复，是否确定删除？',
                                placement: 'left',
                                confirm: delGoods.bind(null, record.goodsId, 'del'),
                              },
                            },
                          ]"
                        />
                      </template>
                    </template>
                  </BasicTable>
                </div>
              </div>
              <!-- 榜单商品 end -->
              <div style="width: 100%; height: 60px; background: #fff"></div>
            </div>
          </Form>
        </div>
        <div
          class="m_diy_bottom_wrap"
          :style="{ position: 'fixed', left: getRealWidth + 10 + 'px' }"
        >
          <div
            class="add_goods_bottom_btn"
            :class="{ add_goods_bottom_btn_rig: viewFlag }"
            @click="goBack"
          >
            返回
          </div>
          <div
            class="add_goods_bottom_btn add_goods_bottom_btn_sel"
            v-if="!viewFlag"
            @click="handleSaveAllData"
          >
            保存
          </div>
        </div>
      </Spin>
    </div>
  </div>
</template>
<script setup>
  import { ref, reactive, onMounted, toRaw } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import {
    Input,
    Spin,
    Form,
    Select,
    InputNumber,
    Upload,
    RadioGroup,
    RadioButton,
    TreeSelect,
    Radio,
    Switch,
    Popover,
    Textarea,
  } from 'ant-design-vue';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import RankLeft from './rank_left.vue';
  import { failTip, list_com_page_more, sucTip } from '/@/utils/utils';
  import { useGlobSetting } from '/@/hooks/setting';
  import { useUserStore } from '/@/store/modules/user';
  import {
    getCategoryListApi,
    getGoodsCategoryListApi,
    getRankLoadGoodsApi,
    getRankUpdateApi,
    getRankAddApi,
    getRankDetailApi,
    getRankGoodsApi,
  } from '/@/api/promotion/rank';
  import { getSettingListApi } from '/@/api/common/common';
  import { getToken } from '/@/utils/auth';
  const { getRealWidth } = useMenuSetting();
  import { pageClose } from '/@/utils/utils';
  import { useMultipleTabStore } from '/@/store/modules/multipleTab';

  const { apiUrl } = useGlobSetting();
  const tabStore = useMultipleTabStore();
  const router = useRouter();
  const userStore = useUserStore();
  const route = useRoute();
  const formRef = ref();
  const imgToken = ref('Bearer ' + getToken() || ''); //上传文件token参数

  const loading = ref(false);

  const detail = ref({
    rankType: 1,
    statsTime: 1,
    bestSellerRankRule: 1,
    newProductRankRule: 1,
    isAutoUpdate: 1,
  }); //排行榜详情
  const projectName = ref(import.meta.env.VITE_GLOB_APP_TITLE);
  const pageTitle = ref('新建排行榜');
  const query = ref(route.query);
  const showBindGoods = ref(
    route.query?.id != undefined && Number(route.query?.id) > 0 && route.query?.type == 'edit'
      ? true
      : false,
  ); //是否展示删除操作
  const viewFlag = ref(
    route.query?.id != undefined && Number(route.query?.id) > 0 && route.query?.type == 'view'
      ? true
      : false,
  ); //是否是查看
  const rules = ref({
    rankName: [
      {
        required: true,
        whitespace: true,
        message: `请输入榜单名称`,
      },
    ],
    categoryId: [
      {
        required: true,
        message: `请选择关联分类`,
      },
    ],
    sort: [
      {
        required: true,
        message: `请输入排序`,
      },
    ],
    highCommentNum: [
      {
        required: true,
        message: `请输入好评数`,
      },
    ],
    highCommentRate: [
      {
        required: true,
        message: `请输入好评率`,
      },
    ],
    goodsCategoryId: [
      {
        required: true,
        message: `请选择生成榜单的商品分类`,
      },
    ],
  });
  const GoodsTablePaginationFlag = ref(false); //商品表格是否允许显示分页信息
  const GoodsTableCheckFlag = ref(false); //商品表格是否允许选择标识
  const showLoadGoodsModule = ref(false); //是否显示商品模块
  const showLoadGoodsSearchModule = ref(false); //是否显示商品搜索模块
  const loadGoodsParams = ref({}); //加载商品按钮的搜索条件
  const formValues = ref({}); //商品模块的搜索条件
  const rankMaxAddGoodsNum = ref(0); //最大绑定商品数
  const bgFileList = ref([]); //背景图
  const isFirstLoading = ref(true); //是否第一次加载
  const goodsLoading = ref(false); //商品加载
  const data = ref([]); //表格的数据
  const selectedRows = ref([]);
  const selectedRows_info = ref([]);
  const selectedRowKeys = ref([]); //selectedRows的key
  const selectedRowKeys_info = ref([]);
  const catData = ref([]); //启用的榜单分类
  const goodsCatData = ref([]); //商品分类
  const goodsData = ref({
    list: [],
    pagination: {},
  }); //商品数据
  const search_data = ref([
    {
      component: 'Input',
      label: `商品名称`,
      field: 'goodsName',
      labelWidth: 60,
      colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
      componentProps: {
        placeholder: `请输入商品名称`,
      },
    },
    {
      component: 'Input',
      label: `店铺名称`,
      field: 'storeName',
      labelWidth: 60,
      colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
      componentProps: {
        placeholder: `请输入店铺名称`,
      },
    },
  ]);
  const search_data_rank_type_4 = ref([
    {
      component: 'TreeSelect',
      label: `商品分类`,
      labelWidth: 60,
      field: 'goodsCategoryId',
      colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
      componentProps: {
        placeholder: `请选择商品分类`,
        treeData: [],
      },
    },
  ]);
  const columns = ref([]);
  const columns_rank_type = reactive({
    data: {
      columns_rank_type_1: [
        {
          title: `销量`,
          dataIndex: 'saleNum',
          align: 'center',
          width: 100,
        },
        {
          title: `销售额(¥)`,
          dataIndex: 'saleAmount',
          align: 'center',
          width: 100,
        },
      ], //畅销榜商品列
      columns_rank_type_2: [
        {
          title: `好评数`,
          dataIndex: 'highNum',
          align: 'center',
          width: 100,
        },
        {
          title: `好评率`,
          dataIndex: 'highRate',
          align: 'center',
          width: 100,
        },
      ], //好评榜商品列
      columns_rank_type_3: [
        {
          title: `发布时间`,
          dataIndex: 'createTime',
          align: 'center',
          width: 100,
        },
      ], //新品榜商品列
      columns_rank_type_4: [
        {
          title: `品牌名称`,
          dataIndex: 'brandName',
          align: 'center',
          width: 100,
          customRender: ({ text }) => {
            return text ? text : '-';
          },
        },
        {
          title: `销量`,
          dataIndex: 'saleNum',
          align: 'center',
          width: 100,
        },
        {
          title: `销售额(¥)`,
          dataIndex: 'saleAmount',
          align: 'center',
          width: 100,
        },
        {
          title: `上榜理由`,
          dataIndex: 'rankReason',
          align: 'center',
          width: 200,
        },
      ], //自定义榜商品列
    },
  });

  // 表格数据
  const [registerTable, { reload, setSelectedRowKeys, setProps,getColumns,setColumns }] = useTable({
    // 表头
    columns: columns.value,
    immediate: false,
    rowKey: 'goodsId',
    // 点击搜索前处理的参数
    handleSearchInfoFn: (values) => {},
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: search_data.value,
    },
    inset: false,
    clickToRowSelect: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: false,
    ellipsis: false,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    // 是否显示序号列
    showIndexColumn: false,
  });
  // 返回
  const goBack = () => {
    if (window.history && window.history.length == 1) {
      pageClose();
    } else {
      const { fullPath } = route;
      tabStore.closeTabByKey(fullPath, router);
      router.back();
    }
  };

  // 保存
  const handleSaveAllData = async () => {
    formRef.value.validateFields().then(async (values) => {
      if (
        bgFileList.value.length > 0 &&
        bgFileList.value[0].response != undefined &&
        bgFileList.value[0].response.data != undefined
      ) {
        values.backgroundImage = bgFileList.value[0].response.data.path; //背景图
      } else {
        values.backgroundImage = '';
      }
      if (values.rankType == 4) {
        values.isAutoUpdate = 0; //是否自动更新：1-自动；0-手动
      }
      if (values.isAutoUpdate == 0) {
        //手动更新的话需要获取商品信息
        if (selectedRows.value.length == 0) {
          failTip(`请选择商品～`);
          return false;
        } else {
          values.goodsList = [];
          selectedRows.value.map((item, index) => {
            let temp = {
              goodsId: item.goodsId,
              goodsRank: index + 1,
              rankReason: item.rankReason != undefined ? item.rankReason : '',
            };
            values.goodsList.push(temp);
          });
        }
      }
      loading.value = true;
      let res = null;
      if (
        route.query?.id != undefined &&
        Number(route.query?.id) > 0 &&
        route.query?.type == 'edit'
      ) {
        //编辑排行榜
        values.rankId = route.query?.id;
        res = await getRankUpdateApi(values);
      } else {
        //新建排行榜
        res = await getRankAddApi(values);
      }
      if (res.state == 200) {
        loading.value = false;
        sucTip(res.msg);
        if(route.query.name){
          userStore.setDelKeepAlive([route.name,route.query.name])
        }else{
          userStore.setDelKeepAlive([route.name,'RankIndex'])
        }
        setTimeout(() => {
          goBack();
        }, 500);
      } else {
        loading.value = false;
        failTip(res.msg);
      }
    });
  };

  //文件上传前处理数据
  function beforeUpload(file, accept, limit) {
    if (accept != undefined && accept != null && accept) {
      //校验文件格式类型
      let accept_list = accept
        .replaceAll(' ', '')
        .split(',')
        .filter((item) => item && item.trim());
      let file_type = file.name ? '.' + file.name.split('.').at(-1) : '';
      if (accept_list.length > 0 && file_type != '' && accept_list.indexOf(file_type) == -1) {
        failTip('上传文件格式有误');
        return false;
      }
    }
    let uploadLimit = limit ? limit : 20;
    if (file.size > 1024 * 1024 * uploadLimit) {
      failTip('上传文件过大，请上传小于' + uploadLimit + 'M的文件');
      return false;
    }
  }

  //数据变化事件
  function handleFileChange(e) {
    if (e.file.status != undefined && e.file.status != 'error') {
      e.fileList.forEach((item) => {
        if (item.response && item.response.data) {
          item.url = item.response.data.url;
        }
      });
      bgFileList.value = e.fileList;
    }
  }

  const beforeUploadClick = async()=> {
    imgToken.value = 'Bearer ' + userStore.getToken
    if(userStore.getToken){
      let res = await userStore.getSldCommonService()
      if(res.state == 200){
        imgToken.value = 'Bearer ' + res.token
      }
    }
  }


  const loadGoods = (source) => {
    let loadGoodsParams = {}; //商品筛选条件
    let formData = detail.value;
    // let dis_type = 'rank/get_all_rank_goods';
    loadGoodsParams.rankType = formData.rankType; //rankType 榜单类型 1-畅销榜；2-好评榜；3-新品榜；4-自定义
    let filed = []; //需要验证的字段
    if (formData.rankType == 4) {
      filed = [];
      showLoadGoodsSearchModule.value = true; //显示商品搜索模块
      GoodsTableCheckFlag.value = true; //允许表格商品多选
      GoodsTablePaginationFlag.value = true; //显示分页信息
    } else {
      filed = ['goodsCategoryId'];
      if (formData.rankType == 2) {
        filed = ['goodsCategoryId', 'highCommentNum', 'highCommentRate'];
      }
      //isAutoUpdate 是否自动更新：1-自动；0-手动
      if (formData.isAutoUpdate) {
        //自动更新的话，商品数据只展示，不更新
        showLoadGoodsSearchModule.value = false; //不显示商品搜索模块
        GoodsTableCheckFlag.value = false; //不允许表格商品选择
        GoodsTablePaginationFlag.value = false; //不显示分页信息
        loadGoodsParams.pageSize = rankMaxAddGoodsNum.value;
      } else {
        showLoadGoodsSearchModule.value = true; //显示商品搜索模块
        GoodsTableCheckFlag.value = true; //允许表格商品多选
        GoodsTablePaginationFlag.value = true; //显示分页信息
      }
    }
    formValues.value = source ? {} : formValues.value; //来自于加载榜单商品按钮的话，formValues要置为空
    selectedRows.value = source ? [] : selectedRows.value; //来自于加载榜单商品按钮的话，selectedRows要置为空
    selectedRowKeys.value = source ? [] : selectedRowKeys.value; //来自于加载榜单商品按钮的话，selectedRowKeys要置为空
    formRef.value.validateFields(filed).then((value) => {
      selectedRows.value = [];
      selectedRows_info.value = [];
      selectedRowKeys.value = [];
      selectedRowKeys_info.value = [];
      if (loadGoodsParams.rankType != 4) {
        loadGoodsParams.goodsCategoryId = formData.goodsCategoryId; //商品分类id
        loadGoodsParams.statsTime = formData.statsTime; //统计时间：1-近7天；2-近30天
        if (loadGoodsParams.rankType == 1) {
          loadGoodsParams.bestSellerRankRule = formData.bestSellerRankRule; //畅销榜计算规则：1-销量排行；2-销售额排行
        } else if (loadGoodsParams.rankType == 2) {
          loadGoodsParams.highCommentNum = formData.highCommentNum; //好评数
          loadGoodsParams.highCommentRate = formData.highCommentRate; //好评率
        } else if (loadGoodsParams.rankType == 3) {
          loadGoodsParams.newProductRankRule = formData.newProductRankRule; //新品榜计算规则：1-按照商品发布的时间降序排列；2-按照商品发布的时间升序排列
        }
      }
      columns.value = [];
      let column = [
        {
          title: `商品名称`,
          dataIndex: 'goodsImage',
          align: 'center',
          width: 300,
        },
        {
          title: `店铺名称`,
          dataIndex: 'storeName',
          align: 'center',
          width: 100,
        },
        {
          title: `商品价格(¥)`,
          dataIndex: 'goodsPrice',
          align: 'center',
          width: 100,
        },
        {
          title: `商品库存`,
          dataIndex: 'goodsStock',
          align: 'center',
          width: 100,
        },
      ];
      columns.value = [
        ...column,
        ...columns_rank_type.data[
          'columns_rank_type_' +
            (loadGoodsParams.rankType != undefined ? loadGoodsParams.rankType : 1)
        ],
      ];
      showLoadGoodsModule.value = true;
      showBindGoods.value = false;
      setTimeout(() => {
        setProps({
          api: getRankLoadGoodsApi,
          pagination: GoodsTablePaginationFlag.value
            ? { pageSize: loadGoodsParams.pageSize }
            : false,
          actionColumn: undefined,
          useSearchForm: showLoadGoodsSearchModule.value,
          rowSelection:
            loadGoodsParams.rankType != 4 && detail.value.isAutoUpdate != 0
              ? undefined
              : {
                  //单选多选
                  selectedRowKeys: selectedRowKeys,
                  type: 'checkbox',
                  onSelect: onSelect,
                  onSelectAll: onSelectAll,
                },
          searchInfo: { ...loadGoodsParams, ...formValues.value },
          // 搜索内容
          formConfig: {
            labelWidth: 75,
            schemas:
              formData.rankType == 4
                ? [...search_data.value, ...search_data_rank_type_4.value]
                : search_data.value,
          },
        });
        setColumns(columns.value)
        reload();
        setSelectedRowKeys([]);
      });
    });
  };

  // 单选多选事件
  function onSelect(record, selected) {
    if (selected) {
      let num = rankMaxAddGoodsNum.value > 0 ? rankMaxAddGoodsNum.value - 1 : 0;
      if (selectedRowKeys.value.length > num) {
        failTip('您选择的商品超过了最大限制，请重新选择～');
        return;
      }
      selectedRowKeys.value = [...selectedRowKeys.value, record.goodsId];
      selectedRows.value.push(record);
    } else {
      selectedRowKeys.value = selectedRowKeys.value.filter((id) => id !== record.goodsId);
      selectedRows.value = selectedRows.value.filter((item) => item.goodsId !== record.goodsId);
    }
  }

  // 全选按钮事件
  function onSelectAll(selected, rows, changeRows) {
    const changeIds = changeRows.map((item) => item.goodsId);
    let row = [];
    rows.forEach((item) => {
      if (item) {
        row.push(toRaw(item));
      }
    });
    if (selected) {
      let selectedRowKey = [];
      selectedRowKey = [...selectedRowKeys.value, ...changeIds];
      if (selectedRowKey.length > rankMaxAddGoodsNum.value) {
        failTip('您选择的商品超过了最大限制，请重新选择～');
        return;
      }
      selectedRowKeys.value = selectedRowKey;
      selectedRows.value = [...selectedRows.value, ...row];
      selectedRows.value = reduction(selectedRows.value);
    } else {
      selectedRowKeys.value = selectedRowKeys.value.filter((id) => {
        return !changeIds.includes(id);
      });
      let selectedRow = [];
      //去掉的话要删掉行数据
      selectedRows.value.forEach((item) => {
        if (selectedRowKeys.value.indexOf(item.goodsId) != -1) {
          selectedRow.push(item);
        }
      });
      selectedRows.value = selectedRow;
    }
  }

  const reduction = (tempArr) => {
    for (let i = 0; i < tempArr.length; i++) {
      for (let j = i + 1; j < tempArr.length; j++) {
        if (tempArr[i].goodsId == tempArr[j].goodsId) {
          tempArr.splice(j, 1);
          j--;
        }
      }
    }
    return tempArr;
  };

  const delGoods = (id, type) => {
    selectedRows.value = selectedRows.value.filter((item) => item.goodsId != id);
    selectedRowKeys.value = selectedRowKeys.value.filter((item) => item != id);
    goodsData.value.list = selectedRows;
    setProps({
      dataSource: goodsData.value.list,
    });
    reload();
  };

  //获取可用的榜单分类
  const getCat = async (params) => {
    try {
      let res = await getCategoryListApi(params);
      if (res.state == 200) {
        catData.value = res.data;
      }
    } catch (error) {
      console.info(error, 'error');
    }
  };

  //获取商品分类
  const getGoodsCat = async () => {
    try {
      let res = await getGoodsCategoryListApi();
      if (res.state == 200) {
        for (let i in res.data) {
          res.data[i].key = res.data[i].categoryId;
          res.data[i].value = res.data[i].categoryId;
          res.data[i].title = res.data[i].categoryName;
          if (res.data[i].children != null && res.data[i].children.length > 0) {
            res.data[i].children.map((item) => {
              item.key = item.categoryId;
              item.value = item.categoryId;
              item.title = item.categoryName;
              if (item.children != null && item.children.length > 0) {
                item.children.map((child) => {
                  child.key = child.categoryId;
                  child.value = child.categoryId;
                  child.title = child.categoryName;
                });
              }
            });
          }
        }
        goodsCatData.value = res.data;
        search_data_rank_type_4.value[0].componentProps.treeData = res.data;
      }
    } catch (error) {
      console.info(error, 'error');
    }
  };

  //验证排行榜开关
  const checkRankState = async () => {
    try {
      let res = await getSettingListApi({ str: 'rank_max_add_goods_num' });
      if (res.state == 200) {
        rankMaxAddGoodsNum.value = res.data[0].value * 1;
      }
    } catch (error) {
      console.log(error, 'error');
    }
  };

  // 点击榜单类型
  const rankType_change = (e) => {
    if (showLoadGoodsModule.value) {
      setProps({
        dataSource: [],
        api: undefined,
        actionColumn: undefined,
        useSearchForm: false,
        // 搜索内容
        formConfig: {
          labelWidth: 75,
          schemas: [],
        },
      });
      setColumns(columns.value)
      selectedRows.value = [];
      selectedRows_info.value = [];
      selectedRowKeys.value = [];
      selectedRowKeys_info.value = [];
      setTimeout(() => {
        reload();
        setSelectedRowKeys([]);
      });
    }
  };

  //获取排行榜详情
  const getDetail = async (id) => {
    try {
      loading.value = true;
      let res = await getRankDetailApi({ rankId: id });
      if (res.state == 200) {
        let data = res.data;
        //初始化背景图数据-start
        bgFileList.value = [];
        if (data.backgroundImage) {
          let tmp_data = {};
          tmp_data.uid = data.backgroundImage;
          tmp_data.name = data.backgroundImage;
          tmp_data.status = 'done';
          tmp_data.url = data.backgroundImageUrl;
          tmp_data.response = {
            data: {
              url: data.backgroundImageUrl,
              path: data.backgroundImage,
            },
          };
          bgFileList.value.push(tmp_data);
        }
        //初始化背景图数据-end
        let goods_arr = ['statsTime', 'bestSellerRankRule', 'newProductRankRule', 'isAutoUpdate'];
        for (let i in goods_arr) {
          res.data[goods_arr[i]] = res.data[goods_arr[i]] != null ? res.data[goods_arr[i]] : 1;
        }
        detail.value = res.data;
        getBindGoodsList(route.query?.id);
        loading.value = false;
      } else {
        failTip(res.msg);
      }
    } catch (error) {
      console.log(error, 'error');
    }
  };

  //获取绑定的榜单商品
  const getBindGoodsList = async (id) => {
    try {
      let res = await getRankGoodsApi({ rankId: id });
      if (res.state == 200) {
        selectedRows.value = [];
        selectedRowKeys.value = [];
        if (res.data.length > 0) {
          selectedRows.value = res.data;
          selectedRows.value.map((item) => {
            selectedRowKeys.value.push(item.goodsId);
          });
        }
        columns.value = [];
        let column = [
          {
            title: `商品名称`,
            dataIndex: 'goodsImage',
            align: 'center',
            width: 300,
          },
          {
            title: `店铺名称`,
            dataIndex: 'storeName',
            align: 'center',
            width: 100,
          },
          {
            title: `商品价格(¥)`,
            dataIndex: 'goodsPrice',
            align: 'center',
            width: 100,
          },
          {
            title: `商品库存`,
            dataIndex: 'goodsStock',
            align: 'center',
            width: 100,
          },
        ];
        columns.value = [
          ...column,
          ...columns_rank_type.data[
            'columns_rank_type_' + (detail.value.rankType != undefined ? detail.value.rankType : 1)
          ],
        ];
        GoodsTablePaginationFlag.value = false;
        GoodsTableCheckFlag.value = false;
        showLoadGoodsModule.value = true;
        goodsData.value.list = res.data;
        setTimeout(() => {
          setProps({
            useSearchForm: false,
            rowSelection: undefined,
            dataSource: res.data,
            pagination: false,
            // 表格右侧操作列配置
            actionColumn: showBindGoods.value
              ? {
                  width: 130,
                  title: '操作',
                  dataIndex: 'action',
                  // slots: { customRender: 'action' },
                }
              : undefined,
          });
          setColumns(columns.value)
          reload();
        });
      }
    } catch (error) {
      console.info(error, 'error');
    }
  };

  onMounted(() => {
    if (route.query?.type != undefined) {
      if (route.query?.type == 'edit') {
        pageTitle.value = `编辑排行榜`;
      } else {
        pageTitle.value = `查看排行榜`;
      }
    } else {
      pageTitle.value = `新建排行榜`;
    }
    document.title = pageTitle.value + ' - ' + projectName.value;
    if (route.query?.id != undefined && Number(route.query?.id) > 0) {
      getDetail(route.query?.id);
    } else {
      isFirstLoading.value = false;
    }
    getCat({ pageSize: list_com_page_more });
    getGoodsCat();
    checkRankState();
  });
</script>
<style lang="less">
  @import './style/add.less';
  .highCommentNum_box{
    .ant-form-item-extra{
      padding-left: 88px;
    }
  }
</style>
