<template>
  <div class="rank_index section_padding">
    <div class="section_padding_back">
      <SldComHeader title="排行榜" />
      <Tabs type="card" v-model:activeKey="activeKey">
        <TabPane key="1" tab="榜单列表">
          <RankActivityLists class="section_padding_tab_top"/>
        </TabPane>
        <TabPane key="2" tab="榜单分类">
          <RankLabelLists class="section_padding_tab_top"></RankLabelLists>
        </TabPane>
        <TabPane key="3" tab="排行榜设置">
          <Setting class="section_padding_tab_top"></Setting>
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'RankIndex',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { Tabs, TabPane, message, RadioGroup, RadioButton } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import RankActivityLists from './activity_lists.vue';
  import RankLabelLists from './label_lists.vue';
  import Setting from './setting.vue';

  const router = useRouter();

  const RadioGroupValue = ref('1');

  const activeKey = ref('1');

  onMounted(() => {});
</script>
<style lang="less">
  .rank_index {
    .vben-basic-table-form-container .ant-form {
      padding-top: 0;
    }
  }

  .RadioGroup_back {
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within {
      box-shadow: none;
    }
  }
</style>
