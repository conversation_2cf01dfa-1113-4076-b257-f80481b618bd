.view_ladder_group {
  .full_activity {
    .full_acm_activity {
      .item {
        padding-top: 15px;

        .left {
          width: 200px;
          height: 32px;
          padding-right: 20px;
          font-size: 13px;
          line-height: 32px;
          text-align: right;
        }

        .right {
          .reset_sel {
            display: inline-block;
            margin-top: 9px;
            margin-bottom: 10px;
            margin-left: 0;
            color: #ff711e;
            font-size: 13px;
            cursor: pointer;
          }
        }
      }

      .sele_goods {
        position: relative;
        margin: 10px 0;
        padding: 10px;
        border: 1px solid #e6e6e6;
        border-radius: 5px;

        .del_spu {
          position: absolute;
          z-index: 2;
          top: 0;
          right: 0;
          width: 28px;
          height: 30px;
        }

        .goods_info {
          height: 70px;
          margin-bottom: 10px;
          border-bottom: 1px solid #f4f3f8;

          .goods_info_left {
            .goods_img_wrap {
              flex-shrink: 0;
              width: 60px;
              height: 60px;
              overflow: hidden;
              border-radius: 5px;

              .goods_img {
                max-width: 100%;
                max-height: 100%;
              }
            }

            .goods_name {
              max-width: 320px;
              margin-left: 10px;
              padding-top: 5px;
              color: #333;
              font-size: 12px;
            }
          }

          .goods_info_right {
            height: 70px;
            padding-bottom: 10px;

            .batch_btn {
              height: 30px;
              margin-left: 10px;
              padding: 0 15px;
              border-radius: 3px;
              background: #ff711e;
              color: #fff;
              font-size: 13px;
              cursor: pointer;

              .sel_all {
                color: #fff;
              }
            }
          }
        }

        .ladder_part {
          margin-bottom: 10px;
          padding: 10px;
          background: #f8f7f9;

          .deposit_part {
            .tip {
              display: inline-block;
              margin-left: 20px;
              color: #999;
              font-size: 12px;
            }
          }

          .ladder_item {
            margin-top: 10px;

            .left_title {
              display: inline-block;
              margin-right: 20px;
              color: #333;
              font-weight: bold;
            }
          }

          .add_ladder {
            .btn {
              display: inline-block;
              margin-right: 10px;
              color: blue;
            }

            .tip {
              color: #999;
            }
          }
        }
      }
    }

    .flex_zi {
      flex: 1;
      align-items: center;
      justify-content: center;
    }

    .common_title_bg {
      width: 100%;
      height: 36px;
      border-radius: 2px;
      background: #fffaf7;
      line-height: 36px;

      .title {
        padding-left: 10px;
        color: #333;
        font-size: 13px;
      }

      .del_ladder_pro {
        display: none;
        padding-top: 7px;
        padding-right: 15px;
      }
    }

    .add_new {
      width: 100%;
      height: 40px;
      margin-top: 15px;
      border-radius: 3px;
      background: #fffaf7;

      .add_new_tip {
        margin-left: 10px;
        color: #ff7f40;
        font-size: 12px;
      }
    }

    .sel_goods {
      padding-left: 24px;

      .sel_tip {
        color: #999;
        font-size: 12px;
      }

      .reset_sel_goods {
        color: #ff7f40;
        font-size: 12px;
        cursor: pointer;
      }

      .goods_info {
        min-width: 260px;
        max-width: 700px;
        height: 60px;
        margin-top: 5px;
        margin-left: 10px;
        padding: 10px;
        border-radius: 3px;
        background: #f8f8f8;

        .left {
          flex-shrink: 0;
          width: 40px !important;
          height: 40px !important;
          margin-right: 10px;
          padding-right: 0 !important;
          overflow: hidden;
          border-radius: 3px;

          img {
            max-width: 100%;
            max-height: 100%;
            border-radius: 3px;
          }
        }

        .goods_name {
          color: #333;
          font-size: 13px;
        }

        .goods_price {
          margin-top: 3px;
          color: #666;
          font-size: 12px;
          white-space: nowrap;
        }
      }
    }
  }
}
