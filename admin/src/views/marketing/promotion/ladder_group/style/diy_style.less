.ladder_diy_style {
  height: calc(100vh - 48px - 20px - 20px - 32px - 15px - 48px - 32px - 60px);
  padding: 10px 0;
  overflow: auto;

  .diy_style_list {
    width: 1240px;

    .diy_style_item {
      position: relative;
      width: 122px;
      height: 48px;
      margin-right: 10px;
      margin-bottom: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      cursor: pointer;

      &.active {
        border-color: #fc701e;
      }

      .diy_style_color {
        width: 46px;
        height: 20px;
        overflow: hidden;

        .diy_style_color_left {
          flex: 1;
          height: 20px;
        }

        .diy_style_color_middle {
          flex: 1;
          height: 20px;
          margin-right: 2px;
          margin-left: 2px;
        }

        .diy_style_color_right {
          flex: 1;
          height: 20px;
        }
      }

      .diy_style_name {
        margin-left: 8px;
        color: #666;
        font-size: 12px;
        text-align: center;
      }

      .diy_style_auto_name {
        color: #fc701e;
        font-size: 12px;
      }

      .diy_style_auto_arrow {
        flex-shrink: 0;
        width: 10px;
        height: 22px;
        margin-left: 5px;
        color: #fc701e;
        font-family: cursive;
        font-size: 14px;
        font-weight: 600;
      }

      .diy_style_checked {
        position: absolute;
        z-index: 1;
        right: -1px;
        bottom: -1px;
        width: 20px;
        height: 20px;
      }
    }
  }

  .prew_tips {
    margin-bottom: 10px;
    color: #999;
    font-family: "PingFang SC";
    font-size: 12px;
    font-weight: 500;
  }

  .prew_title {
    margin-bottom: 10px;
    color: #222;
    font-family: "PingFang SC";
    font-size: 15px;
    font-weight: bold;
    line-height: 40px;
  }

  .prew_list {
    width: 1240px;
    padding-left: 15px;

    .prew_item {
      position: relative;
      width: 375px;
      height: 666px;
      margin-right: 50px;
      overflow: hidden;
      border-radius: 10px;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      box-shadow: 0 0 12px 0 rgb(129 129 129 / 30%);
      cursor: default;

      &:last-of-type {
        margin-right: 0;
      }

      &.prew_item_goods {
        .diy_style_top {
          position: absolute;
          z-index: 2;
          top: 374px;
          left: 0;
          width: 100%;
          height: 70px;
          padding: 0 12px;
          background-repeat: no-repeat;
          background-position: center;
          background-size: cover;
          color: #fff;
          font-family: "PingFang SC";
          font-size: 12px;
          font-weight: 500;
          line-height: 20px;

          .diy_style_top_left {
            div {
              color: #fff;
              font-family: "PingFang SC";
              font-size: 12px;
              font-weight: 500;

              &:nth-child(1) {
                align-items: baseline;

                span {
                  &:nth-child(1) {
                    font-size: 13px;
                  }

                  &:nth-child(2) {
                    font-size: 15px;
                    font-weight: 600;
                  }

                  &:nth-child(3) {
                    font-size: 18px;
                    font-weight: 600;
                  }
                }
              }

              &:nth-child(2) {
                margin-top: 4px;
              }
            }
          }

          .diy_style_top_right {
            div {
              &:nth-child(1) {
                color: #fff;
                font-family: "PingFang SC";
                font-size: 13px;
                font-weight: 500;
              }

              &:nth-child(2) {
                width: 106px;
                height: 4px;
                margin-top: 4px;
                margin-bottom: 4px;
                border: 1px solid #fff;
                border-radius: 2px;
              }

              &:nth-child(3) {
                color: #fff;
                font-family: "PingFang SC";
                font-size: 13px;
                font-weight: 500;

                span {
                  display: inline-block;
                  width: 20px;
                  height: 20px;
                  margin: 0 4px;
                  transform: scale(0.8);
                  border-radius: 50%;
                  background: #fff;
                  font-size: 12px;
                  line-height: 20px;
                  text-align: center;
                }
              }
            }
          }
        }

        .diy_style_buy {
          position: absolute;
          z-index: 2;
          right: 12px;
          bottom: 4px;
          width: 210px;
          height: 34px;
          border-radius: 17px;
          color: #fff;
          font-family: "PingFang SC";
          font-size: 15px;
          font-weight: 500;
          line-height: 34px;
          text-align: center;
        }
      }

      &.prew_item_confirm {
        .diy_style_top_price {
          position: absolute;
          z-index: 2;
          top: 334px;
          left: 105px;
          font-family: "PingFang SC";
          font-size: 13px;
          font-weight: 500;

          div {
            &:nth-child(1) {
              align-items: baseline;
              font-weight: 700;

              span {
                font-size: 22px;
              }
            }

            &:nth-child(2) {
              height: 20px;
              margin-left: 12px;
              padding: 0 5px;
              border-radius: 10px;
              color: #fff;
              font-size: 12px;
              line-height: 20px;
            }
          }
        }

        .diy_style_type_list {
          position: absolute;
          z-index: 2;
          top: 454px;
          left: 14px;

          .diy_style_type_item {
            position: relative;
            z-index: 3;
            width: 65px;
            height: 32px;
            margin-right: 10px;
            transform: scale(0.95);
            border: 1px solid #f6f6f6;
            border-radius: 16px;
            background: #f6f6f6;
            color: #333;
            font-family: "PingFang SC";
            font-size: 14px;
            font-weight: 500;
            line-height: 30px;
            text-align: center;
          }
        }

        .diy_style_buy {
          position: absolute;
          z-index: 2;
          bottom: 4px;
          left: 17px;
          width: 344px;
          height: 38px;
          border-radius: 20px;
          color: #fff;
          font-family: "PingFang SC";
          font-size: 15px;
          font-weight: 500;
          line-height: 38px;
          text-align: center;
        }
      }

      &.prew_item_list {
        .top_title {
          position: absolute;
          z-index: 2;
          top: 0;
          left: 0;
          width: 375px;
          height: 74px;
          padding-left: 12px;
          color: #fff;
          font-family: "PingFang SC";
          font-size: 15px;
          font-weight: 500;
          line-height: 44px;

          span {
            &:nth-child(1) {
              font-family: cursive;
              font-size: 21px;
            }

            &:nth-child(2) {
              margin-left: 15px;
              font-family: "PingFang SC";
              font-size: 16px;
              font-weight: bold;
            }
          }

          .top_title_info {
            position: absolute;
            top: 10px;
            width: 355px;
          }

          .top_title_btn {
            position: absolute;
            right: 8px;
            bottom: 10px;
            width: 89px;
            height: 29px;
          }
        }

        .top_nav {
          position: absolute;
          z-index: 2;
          top: 74px;
          left: 0;
          height: 42px;
          color: #333;
          font-family: "PingFang SC";
          font-size: 15px;
          font-weight: 500;

          span {
            margin-right: 15px;
            margin-left: 15px;

            &:nth-child(1) {
              padding-top: 6px;
              padding-bottom: 4px;
              border-bottom: 3px solid;
              font-weight: bold;
            }
          }
        }

        .middle_info {
          .goods_label {
            position: absolute;
            z-index: 2;
            left: 160px;
            padding: 0 3px;
            transform: scale(0.85);
            border: 1px solid;

            span {
              margin-left: 2px;
            }
          }

          .middle_price {
            position: absolute;
            z-index: 2;
            left: 162px;
            align-items: baseline;

            div {
              font-family: "PingFang SC";
              font-size: 13px;

              &:nth-child(1) {
                padding-right: 3px;

                span {
                  font-size: 18px;
                }
              }

              &:nth-child(2) {
                text-decoration: line-through;
              }
            }
          }

          .middle_group {
            position: absolute;
            z-index: 2;
            left: 167px;
            overflow: hidden;
            border: 1px solid;
            border-radius: 14px;

            div {
              height: 22px;
              font-family: "PingFang SC";
              font-size: 12px;
              font-weight: 500;
              line-height: 22px;
              text-align: center;

              &:nth-child(1) {
                width: 70px;
                border-radius: 14px 0 14px 14px;
                color: #fff;
              }

              &:nth-child(2) {
                width: 62px;
              }
            }
          }

          &:nth-child(3) {
            .goods_label {
              top: 147px;
            }

            .middle_price {
              top: 204px;
            }

            .middle_group {
              top: 240px;
            }
          }

          &:nth-child(4) {
            .goods_label {
              top: 312px;
            }

            .middle_price {
              top: 369px;
            }

            .middle_group {
              top: 404px;
            }
          }

          &:nth-child(5) {
            .goods_label {
              top: 477px;
            }

            .middle_price {
              top: 534px;
            }

            .middle_group {
              top: 571px;
            }
          }

          &:nth-child(6) {
            .goods_label {
              top: 642px;
            }

            .middle_price {
              top: 699px;
            }

            .middle_group {
              top: 735px;
            }
          }
        }
      }
    }
  }

  .prew_pc_list {
    .prew_item {
      width: 1274px;
      height: 722px;
      margin-bottom: 35px;

      .prew_item_title {
        flex-shrink: 0;
        width: 60px;
        margin-right: 18px;
        margin-left: 70px;
        color: #222;
        font-family: "Microsoft YaHei";
        font-size: 14px;
        font-weight: 400;
        line-height: 35px;
        text-align: right;
      }

      .prew_item_goods {
        position: relative;
        width: 1125px;
        height: 725px;
        overflow: hidden;
        border-radius: 4px;
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;
        box-shadow: 0 0 12px 0 rgb(129 129 129 / 30%);
        cursor: default;

        .prew_item_follow {
          position: absolute;
          z-index: 2;
          top: 30px;
          left: 345px;
          width: 42px;
          height: 28px;
          border-radius: 14px;
          color: #fff;
          font-family: "Microsoft YaHei";
          font-size: 13px;
          font-weight: 400;
          line-height: 28px;
          text-align: center;
        }

        .prew_item_score {
          position: absolute;
          z-index: 2;
          top: 45px;
          left: 248px;

          span {
            &:nth-child(1) {
              font-family: "Microsoft YaHei";
              font-size: 13px;
              font-weight: 400;
            }

            &:nth-child(2) {
              display: inline-block;
              position: relative;
              z-index: 2;
              top: 4px;
              left: 4px;
              border: 5px solid transparent;
              border-top: 6px solid;
            }
          }
        }

        .top_search {
          position: absolute;
          z-index: 2;
          top: 27px;
          left: 425px;

          .top_search_left {
            width: 300px;
            height: 32px;
            border: 2px solid;
            background: #fff;
            line-height: 32px;

            span {
              margin-left: 10px;
              color: #999;
              font-family: "Microsoft YaHei";
              font-size: 12px;
              font-weight: 400;
            }
          }

          .top_search_right {
            width: 90px;
            height: 32px;
            color: #fff;
            font-family: "Microsoft YaHei";
            font-size: 15px;
            font-weight: 400;
            line-height: 32px;
            text-align: center;
          }
        }

        .top_search_store {
          position: absolute;
          z-index: 2;
          top: 27px;
          left: 820px;
          width: 90px;
          height: 32px;
          background: #333;
          color: #fff;
          font-family: "Microsoft YaHei";
          font-size: 15px;
          font-weight: 400;
          line-height: 32px;
          text-align: center;
        }

        .top_search_right_store {
          position: absolute;
          z-index: 2;
          top: 179px;
          right: 18px;
          transform: scale(0.85);

          div {
            &:nth-child(1) {
              width: 160px;
              height: 20px;
              padding-left: 6px;
              border-radius: 2px 0 0 2px;
              background: #fff;
              color: #999;
              font-family: "Microsoft YaHei";
              font-size: 12px;
              font-weight: 400;
              line-height: 22px;
            }

            &:nth-child(2) {
              width: 32px;
              height: 20px;
              border-radius: 2px;
              color: #fff;
              font-family: "Microsoft YaHei";
              font-size: 12px;
              font-weight: 400;
              line-height: 20px;
              text-align: center;
            }
          }
        }

        .top_store_label {
          position: absolute;
          z-index: 2;
          top: 215px;
          right: 224px;
          width: 31px;
          height: 17px;
          transform: scale(0.9);
          border-radius: 2px;
          color: #fff;
          font-family: "Microsoft YaHei";
          font-size: 12px;
          font-weight: 400;
          line-height: 18px;
          text-align: center;
        }

        .top_kefu {
          position: absolute;
          z-index: 2;
          top: 215px;
          right: 131px;
        }

        .top_cart {
          position: absolute;
          z-index: 2;
          top: 27px;
          left: 934px;
          width: 142px;
          height: 32px;
          border: 1px solid #e3e3e3;

          .top_cart_title {
            margin-right: 9px;
            margin-left: 8px;
            font-family: "Microsoft YaHei";
            font-size: 14px;
            font-weight: 400;
            line-height: 34px;
          }

          .top_cart_num {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            color: #fff;
            font-family: "Microsoft YaHei";
            font-size: 13px;
            font-weight: 400;
            line-height: 16px;
            text-align: center;
          }
        }

        .goods_desc {
          position: absolute;
          z-index: 2;
          top: 286px;
          left: 368px;
          font-family: "Microsoft YaHei";
          font-size: 13px;
          font-weight: 400;
        }

        .goods_point_exchange {
          position: absolute;
          z-index: 1;
          top: 313px;
          left: 316px;
          width: 645px;
          height: 43px;
          transform: scale(0.84);
          background-repeat: no-repeat;
          background-position: center;
          background-size: cover;
          color: #fff;
          font-family: "Microsoft YaHei";
          font-size: 14px;
          font-weight: 400;

          div {
            margin-left: 88px;

            span {
              width: 20px;
              height: 20px;
              margin: 0 5px;
              border-radius: 50%;
              background: #fff;
              font-size: 13px;
              line-height: 20px;
              text-align: center;
            }
          }
        }

        .goods_price {
          position: absolute;
          z-index: 2;
          top: 368px;
          left: 437px;
          align-items: baseline;
          font-family: "PingFang SC";
          font-size: 14px;
          font-weight: 700;

          span {
            font-size: 22px;
          }
        }

        .introduce_price {
          position: absolute;
          right: 66px;
          width: 130px;
          transform: scale(0.85);
          font-family: "Microsoft YaHei";
          font-size: 12px;
          font-weight: bold;
          text-align: center;
        }

        .goods_coupon {
          position: absolute;
          z-index: 2;
          top: 405px;
          left: 438px;
          width: 112px;
          height: 25px;
          border: 1px solid;
          border-radius: 3px;
          font-family: "Microsoft YaHei";
          font-size: 13px;
          font-weight: 400;
          line-height: 24px;
          text-align: center;
        }

        .goods_type {
          position: absolute;
          z-index: 2;
          top: 535px;
          left: 435px;

          .goods_type_item {
            position: relative;
            width: 66px;
            height: 35px;
            margin-right: 12px;
            padding: 0 12px;
            overflow: hidden;
            border: 1px solid #dfdfdf;
            border-radius: 3px;
            color: #333;
            font-family: "Microsoft YaHei";
            font-size: 12px;
            font-weight: 400;
            line-height: 35px;
            text-align: center;
          }
        }

        .sale_name {
          position: absolute;
          z-index: 2;
          top: 377px;
          right: 227px;
          color: #999;
          font-family: "Microsoft YaHei";
          font-size: 13px;
          font-weight: 400;

          span {
            margin-left: 2px;
          }
        }

        .goods_selected {
          position: absolute;
          z-index: 2;
          bottom: 107px;
          left: 60px;
          width: 49px;
          height: 49px;
          border: 1px solid;
        }

        .left_btn {
          position: absolute;
          z-index: 2;
          bottom: 75px;
          left: 60px;

          span {
            margin-right: 10px;
            margin-left: 6px;
            color: #333;
            font-family: "Microsoft YaHei";
            font-size: 12px;
            font-weight: 400;
            line-height: 20px;
          }
        }

        .btn_add {
          position: absolute;
          z-index: 2;
          bottom: 30px;
          left: 435px;
          width: 150px;
          height: 42px;
          border: 1px solid;
          border-radius: 4px;
          color: #fff;
          font-family: "Microsoft YaHei";
          font-size: 14px;
          font-weight: 400;
          line-height: 17px;
          text-align: center;

          img {
            width: 17px;
            height: 17px;
            margin-right: 6px;
          }
        }
      }
    }
  }
}

.diy_auto_color_modal {
  margin-top: 10px;
  margin-bottom: 10px;

  .color_title {
    width: 115px;
    margin-right: 5px;
    text-align: right;
  }

  .color_show {
    .show_color {
      display: inline-block;
      width: 50px;
      height: 30px;
      margin-right: 10px;
      padding: 5px;
      border: 1px solid #eee;
      line-height: 0;
      cursor: pointer;
    }

    span {
      display: inline-block;
      width: 100%;
      height: 100%;
    }
  }

  .color_picker_wrap {
    position: absolute;
    z-index: 2;

    .color_picker_mask {
      position: fixed;
      inset: 0;
    }
  }
}
