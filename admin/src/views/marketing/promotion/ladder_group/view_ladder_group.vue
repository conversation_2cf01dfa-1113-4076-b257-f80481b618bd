<template>
  <div class="section_padding view_ladder_group">
    <div class="section_padding_back full_activity">
      <SldComHeader title="阶梯团详情" :back="true" />
      <div class="com_line" style="height: 1px"></div>
      <Spin :spinning="spinning">
        <div class="height_detail">
          <CommonTitle text="阶梯团基本信息" />
          <div
            class="full_acm_activity flex_column_start_start"
            v-for="(item, index) in spell_info"
            :key="index"
          >
            <div class="item flex_row_start_center">
              <div class="left">
                <span style="color: #ff1515">*</span>
                {{ item.title }}
              </div>
              <div class="right"> {{ item.content }}{{ item.text }} </div>
            </div>
          </div>
          <CommonTitle text="商品信息" style="margin-top: 10px" />
          <div class="com_line" style="height: 1px"></div>
          <div class="full_acm_activity flex_column_start_start">
            <div class="sele_goods">
              <div class="goods_info flex_row_between_start">
                <div class="goods_info_left flex_row_start_start">
                  <div class="goods_img_wrap flex_row_center_center">
                    <img :src="goodsInfo.goodsImage" alt="" class="goods_img" />
                  </div>
                  <p class="goods_name">{{ goodsInfo.goodsName }}</p>
                </div>
              </div>
              <div class="flex_column_start_start ladder_part">
                <div class="deposit_part">
                  <div class="flex_row_start_center">
                    <span :style="{ display: 'inline-block', marginRight: 5, color: '#333' }">
                      <span :style="{ color: '#FF1515' }">*</span>
                      预付定金：
                    </span>
                    <span :style="{ display: 'inline-block', color: '#333' }">
                      {{ goodsInfo.advanceDeposit }}元
                    </span>
                  </div>
                </div>
                <div v-if="detail.ruleList != undefined && detail.ruleList.length > 0">
                  <div
                    class="ladder_item"
                    v-for="(item_ladder_num, index_ladder_num) in detail.ruleList"
                    :key="index_ladder_num"
                  >
                    <div class="flex_row_start_center">
                      <span v-if="index_ladder_num == 0">
                        <span :style="{ color: '#FF1515' }">*</span>
                        参团人数：
                      </span>
                      <span
                        :style="{
                          display: 'inline-block',
                          marginRight: 5,
                          color: '#333',
                          marginLeft: index_ladder_num > 0 ? 77 : 0,
                        }"
                      >
                        <span>第{{ num_to_num()[item_ladder_num.ladderLevel] }}阶梯</span>
                      </span>
                      <span
                        :style="{
                          display: 'inline-block',
                          marginRight: '5px',
                          color: '#333',
                          marginLeft: index_ladder_num > 0 ? 77 : 0,
                        }"
                      >
                        <span
                          :style="{
                            display: 'inline-block',
                            marginLeft: '5px',
                            color: '#333',
                          }"
                          >{{ item_ladder_num.joinGroupNum }}人</span
                        >
                      </span>
                    </div>
                  </div>
                </div>
              </div>
              <div v-if="goodsInfo.productList != undefined && goodsInfo.productList.length > 0">
                <BasicTable
                  :ellipsis="false"
                  :canResize="false"
                  :bordered="true"
                  :columns="columns_spec"
                  :dataSource="goodsInfo.productList"
                  :pagination="false"
                  rowKey="productId"
                ></BasicTable>
              </div>
            </div>
          </div>
        </div>
      </Spin>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'LadderGroupToView',
  };
</script>
<script setup>
  import { ref, onMounted } from 'vue';
  import { Spin } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import CommonTitle from '/@/components/CommonTitle/index.vue';
  import { useRoute } from 'vue-router';
  import { BasicTable } from '/@/components/Table';
  import { getLadderDetailApi } from '/@/api/promotion/ladder_group';
  import { failTip, num_to_num } from '/@/utils/utils';

  const route = useRoute();

  const spinning = ref(true);

  // 参数

  const spell_info = ref([
    {
      title: '活动名称',
      name: 'groupName',
      content: '',
      text: '',
    },
    {
      title: '活动时间',
      name: 'startTime',
      content: '',
      text: '',
    },
    {
      title: '活动标签',
      name: 'labelName',
      content: '',
      text: '',
    },
    {
      title: '限购数量',
      name: 'buyLimitNum',
      content: '',
      text: '',
    },
    {
      title: '尾款时间',
      name: 'balanceTime',
      content: '',
      text: '小时',
    },
    {
      title: '是否退还定金',
      name: 'isRefundDepositValue',
      content: '',
      text: '',
    },
    {
      title: '阶梯优惠方式',
      name: 'discountTypeValue',
      content: '',
      text: '',
    },
  ]);

  const detail = ref({}); //拼团详情

  const goodsInfo = ref([]); //拼团商品信息

  const columns_specs = ref([
    {
      title: `SKU规格`,
      dataIndex: 'specValues',
      align: 'center',
      width: 100,
      customRender: (text) => {
        return text ? text : `默认`;
      },
    },
    {
      title: '原价(¥)',
      dataIndex: 'productPrice',
      align: 'center',
      width: 100,
    },
    {
      title: `库存`,
      dataIndex: 'stock',
      align: 'center',
      width: 100,
    },
  ]);

  const columns_spec = ref([]);

  const get_detail = async () => {
    try {
      spinning.value = true;
      let res = await getLadderDetailApi({ groupId: route.query?.id });
      if (res.state == 200) {
        let tar_index = 4;
        spinning.value = false;
        res.data.ruleList.forEach((item) => {
          columns_specs.value.splice(tar_index, 0, {
            title: `第${num_to_num()[item.ladderLevel]}${
              res.data.discountType == 1 ? '阶梯价格' : '阶梯折扣'
            }`,
            dataIndex: 'ladderPrice' + item.ladderLevel,
            align: 'center',
            width: 100,
          });
          tar_index += 1;
        });
        spell_info.value.forEach((item) => {
          if (item.name == 'startTime') {
            item.content = res.data[item.name] + ' ~ ' + res.data.endTime;
          } else {
            item.content = res.data[item.name];
          }
        });
        detail.value = res.data;
        goodsInfo.value = res.data.goodsInfo;
        columns_spec.value = JSON.parse(JSON.stringify(columns_specs.value));
      } else {
        failTip(res.msg);
      }
    } catch (error) {}
  };

  onMounted(() => {
    get_detail();
  });
</script>
<style lang="less" scoped>
  @import './style/view_ladder_group.less';

  p {
    margin-bottom: 0;
  }

  .view_coupon {
    padding: 10px;

    .view_coupon_back {
      width: 100%;
      min-height: calc(100vh - @header-height - 20px);
      padding: 10px;
      background-color: #fff;
    }
  }
</style>
