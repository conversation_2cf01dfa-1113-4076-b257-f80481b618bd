<template>
  <div class="section_padding ladder_team_list">
    <div class="section_padding_back">
      <SldComHeader title="团队列表" :back="true" />
      <BasicTable @register="registerTable" rowKey="couponId">
        <template #bodyCell="{ column, text }">
          <template v-if="column.dataIndex === 'goodsImage'">
            <div class="flex_com_row_center">
              <div class="flex_com_row_center com_img_wrap">
                <img :src="text" alt="" />
              </div>
            </div>
          </template>
        </template>
      </BasicTable>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'LadderGroupTeamList',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { useRoute } from 'vue-router';
  import { getLadderGoodListApi } from '/@/api/promotion/ladder_group';

  const route = useRoute();

  // 参数
  const id = ref(route.query?.id);

  const searchInfo = ref({
    groupId: id.value,
  });
  const columns = reactive({
    data: [
      {
        title: `会员名称`,
        dataIndex: 'memberName',
        align: 'center',
        width: 180,
      },
      {
        title: `参团时间`,
        dataIndex: 'participateTime',
        align: 'center',
        width: 180,
      },
      {
        title: `成功时间`,
        dataIndex: 'successTime',
        align: 'center',
        width: 100,
        customRender: ({ text, record }) => {
          return text ? text : '--';
        },
      },
      {
        title: `商品图片`,
        dataIndex: 'goodsImage',
        align: 'center',
        width: 100,
      },
      {
        title: `状态`,
        dataIndex: 'orderSubStateValue',
        align: 'center',
        width: 100,
      },
    ],
  });

  const searchFormSchema = reactive({
    data: [
      {
        component: 'Select',
        label: `状态`,
        field: 'state',
        labelWidth: 90,
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请选择状态`,
          options: [
            { value: '', label: `全部` },
            { value: '102', label: `已付定金` },
            { value: '103', label: `已付尾款` },
          ],
        },
      },
    ],
  });

  // 表格数据
  const [registerTable] = useTable({
    // 请求接口
    api: getLadderGoodListApi,
    // 表头
    columns: columns.data,
    immediate: true,
    // 参数
    searchInfo: searchInfo.value,
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: searchFormSchema.data,
    },
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    ellipsis: false,
    // 是否显示序号列
    showIndexColumn: true,
  });
  onMounted(() => {});
</script>
<style lang="less" scoped>
  .ladder_team_list {
    .com_img_wrap {
      width: 50px;
      height: 50px;
      margin: 0 5px;

      img {
        max-width: 100%;
        max-height: 100%;
      }
    }
  }

  p {
    margin-bottom: 0;
  }
</style>
