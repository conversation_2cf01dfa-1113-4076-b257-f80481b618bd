<template>
  <div class="add_full_acm">
    <Spin :spinning="spinning">
      <div class="add_full_acm_title">
        <span class="title">活动基本信息</span>
      </div>
      <Form
        ref="formRef"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
        :model="curFormData"
        :scrollToFirstError="true"
        style="margin-top: 10px"
      >
        <template v-for="(item, index) in formData">
          <template v-if="item.type == 'input'">
            <Form.Item
              :key="index"
              :name="item.name"
              :label="item.label"
              :extra="item.extra"
              :rules="item.rules"
            >
              <Input
                :maxlength="item.maxLength !== undefined ? item.maxLength : undefined"
                :disabled="item.disable"
                style="width: 400px"
                v-model:value="curFormData[item.name]"
              />
            </Form.Item>
          </template>
          <template v-if="item.type == 'range_picker'">
            <Form.Item
              :key="index"
              :name="item.name"
              :label="item.label"
              :extra="item.extra"
              :rules="item.rules"
            >
              <RangePicker
                style="width: 400px"
                :format="item.format"
                :disabled="item.disabled"
                v-model:value="curFormData[item.name]"
                :disabledDate="item.disabledDate"
                :showTime="true"
                :placeholder="[item.placeholder1, item.placeholder2]"
              />
            </Form.Item>
          </template>
          <template v-if="item.type == 'inputnum'">
            <Form.Item
              :key="index"
              :name="item.name"
              :label="item.label"
              :extra="item.extra"
              :rules="item.rules"
            >
              <InputNumber
                style="width: 400px !important"
                :placeholder="item.placeholder"
                :disabled="item.disable === true ? item.disable : false"
                v-model:value="curFormData[item.name]"
                :min="item.min ? item.min : 0"
                :max="item.max ? item.max : 9999999"
                :precision="item.precision ? item.precision : 0"
                :step="item.step ? item.step : 1"
              />
            </Form.Item>
          </template>
          <template v-if="item.type == 'inputnum_value'">
            <Form.Item
              :key="index"
              :name="item.name"
              :label="item.label"
              :extra="item.extra"
              :rules="item.rules"
            >
              <span style="margin-right: 10px">减</span>
              <InputNumber
                style="width: 100px !important"
                :placeholder="item.placeholder"
                :disabled="item.disable === true ? item.disable : false"
                v-model:value="curFormData[item.name]"
                :min="item.min ? item.min : 0"
                :max="item.max ? item.max : 9999999"
                :precision="item.precision ? item.precision : 0"
                :step="item.step ? item.step : 1"
              />
              <span style="margin-left: 10px">元</span>
            </Form.Item>
          </template>
          <template v-if="item.type == 'couponId'">
            <Form.Item :key="index" :label="item.label" class="couponId_from">
              <Checkbox
                :disabled="item.disable"
                :checked="sel_voucher != undefined && sel_voucher.length > 0 ? true : false"
              >
                送优惠券
              </Checkbox>
              <div
                class="sel_goods flex_column_start_start sel_goods_height_table"
                v-if="sel_voucher != undefined && sel_voucher.length > 0"
              >
                <BasicTable
                  rowKey="couponId"
                  :columns="columns_sel_voucher"
                  :dataSource="sel_voucher"
                  :pagination="false"
                  :bordered="true"
                  :ellipsis="false"
                >
                </BasicTable>
              </div>
            </Form.Item>
          </template>
          <template v-if="item.type == 'goodsId'">
            <Form.Item :key="index" :label="item.label" class="couponId_from">
              <Checkbox
                :disabled="item.disable"
                :checked="sel_goods != undefined && sel_goods.length > 0 ? true : false"
              >
                送赠品
              </Checkbox>
              <div
                class="sel_goods flex_column_start_start sel_goods_height_table"
                v-if="sel_goods != undefined && sel_goods.length > 0"
              >
                <BasicTable
                  rowKey="goodsId"
                  :columns="columns_spu"
                  :dataSource="sel_goods"
                  :pagination="false"
                  :bordered="true"
                  :ellipsis="false"
                >
                  <template #bodyCell="{ column, text, record }">
                    <template v-if="column.dataIndex == 'goodsImage'">
                      <Popover placement="rightTop">
                        <template #content>
                          <div style="max-width: 200px; text-align: center">
                            <img :src="text" alt="" style="max-width: 100%; max-height: 100%" />
                          </div>
                        </template>
                        <div class="business_load_img_full">
                          <img :src="text" alt="" />
                        </div>
                      </Popover>
                    </template>
                    <template v-if="column.dataIndex == 'goodsPrice'">
                      {{ text || text == 0 ? '￥' + Number(text).toFixed(2) : '--' }}
                    </template>
                    <template v-if="column.dataIndex == 'goodsStock'">
                      {{ text ? text : record.productStock ? record.productStock : '--' }}
                    </template>
                  </template>
                </BasicTable>
              </div>
            </Form.Item>
          </template>
        </template>
      </Form>
    </Spin>
  </div>
</template>
<script>
  export default {
    name: 'AddFullAcm',
  };
</script>
<script setup>
  import { ref, onMounted, computed } from 'vue';
  import moment from 'moment';
  import { BasicTable } from '/@/components/Table';
  import dayjs from 'dayjs';
  import { Spin, Form, Input, RangePicker, InputNumber, Checkbox, Popover } from 'ant-design-vue';
  import { getFullAcmDetailApi } from '/@/api/promotion/full';

  const props = defineProps({
    fullId: { type: Number, required: true }, //id
  });

  //计算属性转为ref对象
  const fullId = computed(() => {
    return props.fullId;
  });

  const spinning = ref(false);

  const curFormData = ref({});

  const formData = ref([
    {
      type: 'input',
      label: `活动名称`,
      name: 'fullName',
      placeholder: `请输入活动名称`,
      maxLength: 20, //最多字数
      disable: true, //是否禁用
      extra: '最多输入20个字',
      rules: [
        {
          required: true,
          message: `请输入活动名称`, //请输入活动名称
        },
      ],
    },
    {
      type: 'range_picker',
      label: `活动时间`, //开始和结束时间
      name: 'startTime',
      format: 'YYYY-MM-DD HH:mm:ss',
      placeholder: `请输入活动时间`,
      placeholder1: `开始时间`,
      placeholder2: `结束时间`,
      disabled: true,
      disabledDate: (currentDate) => currentDate && currentDate < moment().subtract(1, 'days'),
      rules: [
        {
          required: true,
          message: `请选择活动时间`,
        },
      ],
      extra: '活动时间不可与其他活动重叠',
    },
    {
      type: 'inputnum',
      label: `优惠门槛`, //数字输入框
      name: 'fullValue',
      placeholder: `请输入优惠门槛`,
      disable: true, //是否禁用
      min: 1, //最小值
      max: 9999999, //最大值
      precision: 2,
      extra: '以元为单位，设置使用该活动的最低金额',
      rules: [
        {
          required: true,
          message: `请选择活动时间`,
        },
      ],
    },
    {
      type: 'inputnum_value',
      label: `优惠内容`, //数字输入框
      name: 'minusValue',
      placeholder: `请输入优惠内容`,
      disable: true, //是否禁用
      min: 0.00, //最小值
      max: 9999999, //最大值
      precision: 2,
      extra: '以元为单位，满足优惠门槛后可以享受优惠的金额',
    },
    {
      type: 'couponId',
      label: ' ',
      name: 'couponId',
      disable: true, //是否禁用
    },
    {
      type: 'goodsId',
      label: ' ',
      name: 'goodsId',
      disable: true, //是否禁用
    },
  ]);

  const columns_sel_voucher = ref([
    {
      title: '优惠券名称',
      dataIndex: 'couponName',
      align: 'center',
      width: 150,
    },
    {
      title: '优惠券类型',
      dataIndex: 'couponTypeValue',
      align: 'center',
      width: 100,
    },
    {
      title: '优惠券内容',
      dataIndex: 'couponContent',
      align: 'center',
      width: 100,
    },
    {
      title: '剩余可用',
      dataIndex: 'couponStock',
      align: 'center',
      width: 100,
    },
  ]);

  const columns_spu = ref([
    {
      title: '商品图片',
      dataIndex: 'goodsImage',
      align: 'center',
      width: 100,
    },
    {
      title: '商品名称',
      dataIndex: 'goodsName',
      align: 'center',
      width: 150,
    },
    {
      title: '商品价格',
      dataIndex: 'goodsPrice',
      align: 'center',
      width: 100,
    },
    {
      title: '商品库存',
      dataIndex: 'goodsStock',
      align: 'center',
      width: 100,
    },
  ]);

  const sel_voucher = ref([]); //选择的优惠券信息
  const sel_goods = ref([]); //选择的赠品信息

  const get_full_acm_detail = async () => {
    try {
      let res = await getFullAcmDetailApi({ fullId: fullId.value });
      if (res.state == 200) {
        formData.value.forEach((item) => {
          curFormData.value[item.name] = res.data[item.name];
          if (item.name == 'startTime') {
            curFormData.value[item.name] = [
              dayjs(res.data.startTime, item.format),
              dayjs(res.data.endTime, item.format),
            ];
          }
          let detail = res.data;
          //初始化选中的优惠券数据
          if (
            detail.couponList != null &&
            detail.couponList.length != undefined &&
            detail.couponList.length > 0
          ) {
            sel_voucher.value = detail.couponList;
          }
          //初始化选中的商品数据
          if (
            detail.giftList != null &&
            detail.giftList.length != undefined &&
            detail.giftList.length > 0
          ) {
            sel_goods.value = detail.giftList;
          }
        });
      }
    } catch (error) {}
  };

  onMounted(() => {
    get_full_acm_detail();
  });
</script>
<style lang="less" scoped>
  p {
    margin-bottom: 0;
  }

  .add_full_acm {
    max-height: 600px;
    padding: 10px;
    overflow: auto;

    .add_full_acm_title {
      width: 100%;
      height: 36px;
      border-radius: 2px;
      background: #fffaf7;
      line-height: 36px;

      .title {
        padding-left: 10px;
        color: #333;
        font-size: 13px;
      }
    }
  }

  .sel_goods {
    padding-left: 24px;
  }
</style>
<style lang="less">
  .couponId_from {
    .ant-form-item-label > label {
      &::after {
        content: ' ';
      }
    }
  }
  .sel_goods_height_table {
    .ant-table-body {
      height: auto !important;
      max-height: 400px !important;
    }
  }
</style>
