<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader title="满优惠活动" />
      <Tabs type="card" v-model:activeKey="activeKey">
        <TabPane key="1" tab="满减">
          <FullAcmList class="section_padding_tab_top"/>
        </TabPane>
        <TabPane key="2" tab="阶梯满减">
          <FullAsmList class="section_padding_tab_top"/>
        </TabPane>
        <TabPane key="3" tab="满N元折扣">
          <FullAldList class="section_padding_tab_top"/>
        </TabPane>
        <TabPane key="4" tab="满N件折扣">
          <FullNldList class="section_padding_tab_top"/>
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'FullDiscount',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { Tabs, TabPane, message, RadioGroup, RadioButton } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import FullAcmList from './full_acm_list.vue';
  import FullAsmList from './full_asm_list.vue';
  import FullAldList from './full_ald_list.vue';
  import FullNldList from './full_nld_list.vue';

  const activeKey = ref('1');

  onMounted(() => {});
</script>
<style lang="less">
  .coupon_home {
    padding: 10px;

    .coupon_home_back {
      width: 100%;
      min-height: calc(100vh - @header-height - 20px);
      padding: 10px;
      background-color: #fff;
    }
  }

  .RadioGroup_back {
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within {
      box-shadow: none;
    }
  }
</style>
