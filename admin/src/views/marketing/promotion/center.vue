<template>
  
    <div class="promotion_center section_padding">
      <div class="section_padding_back_add" style="padding:20px;">
        <Spin :spinning="loading">
          <div class="section_padding_back_scroll_mark" style="padding:0; padding-right:5px;">
            <div class="center_title flex_row_start_center">应用中心</div>
            <template v-for="(item, index) in data_center.data" :key="index">
              <div class="center_item flex_column_start_start" v-if="item.show_num">
                <div class="center_item_title">{{ item.title }}</div>
                <div
                  class="center_item_content flex_row_start_center"
                  style="flex-wrap: wrap; width: 100%"
                >
                  <template v-for="(child_item, child_index) in item.child" :key="child_index">
                    <div
                      class="child_item flex_row_start_start child_width"
                      :style="{ marginLeft: child_index % 4 > 0 ? '20px' : 0 }"  @click="go(child_item)"
                    >
                      <img :src="child_item.icon" alt="" class="left_img" />
                      <div class="right_part flex_column_center_start">
                        <div class="right_part_top flex_row_start_start">
                          <span class="top_title">{{ child_item.title }}</span>
                          <span
                            v-if="child_item.extraFlag && specialFlag > -3"
                            class="top_flag flex_row_center_center"
                            >加配</span
                          >
                        </div>
                        <span class="desc" :title="child_item.desc" :style="{ width: '100%' }">{{
                          child_item.desc
                        }}</span>
                      </div>
                    </div>
                  </template>
                </div>
              </div>
            </template>
          </div>
        </Spin>
      </div>
    </div>
  
</template>
<script>
  export default {
    name: 'PromotionCenter',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { Spin } from 'ant-design-vue';
  import { specialFlag, sucTip } from '/@/utils/utils';
  import { usePermissionStore } from '/@/store/modules/permission';
  const menu = ref([
    '/marketing_spreader',
    '/marketing_promotion',
    '/marketing_svideo',
    '/marketing_live',
    '/recommend',
    '/marketing_point',
    '/sysset_notice_set',
    '/push',
    '/decorate_pc',
    '/decorate_m',
  ]); //拥有的菜单字符串数组

  const emptyFlag = ref(false); //是否展示空提示

  const permissionStore = usePermissionStore();

  const router = useRouter();

  const loading = ref(true)

  const itemW = ref((document.body.clientWidth * 1 - 90 - 195) / 4);

  const descW = ref(itemW.value - 60 - 40 - 15);

  const all_data = reactive({
    data: [
      {
        name: 'spell_group',
        icon: new URL('/@/assets/images/center/spell_group.png', import.meta.url).href,
        title: `拼团`,
        extraFlag: false,
        showFlag: false,
        desc: `通过社交关系，降低拉新成本提提高获客效率。`,
        path: '/marketing_promotion/spell_group',
      },
      {
        name: 'ladder_group',
        icon: new URL('/@/assets/images/center/ladder_group.png', import.meta.url).href,
        title: `阶梯团`,
        extraFlag: false,
        showFlag: false,
        desc: `进阶版拼团，可设置阶梯拼团价格。`,
        path: '/marketing_promotion/ladder_group',
      },
      {
        name: 'spreader',
        icon: new URL('/@/assets/images/center/spreader.png', import.meta.url).href,
        title: `推手`,
        extraFlag: true,
        desc: `招募分销员，提高销售效率。`,
        path: '/marketing_spreader',
      },
      {
        name: 'sckill',
        icon: new URL('/@/assets/images/center/sckill.png', import.meta.url).href,
        title: `秒杀`,
        extraFlag: false,
        showFlag: false,
        desc: `快速抢购，引导顾客完成消费。`,
        path: '/marketing_promotion/seckill',
      },
      {
        name: 'system_coupon',
        icon: new URL('/@/assets/images/center/system_coupon.png', import.meta.url).href,
        title: `平台优惠券`,
        extraFlag: false,
        showFlag: false,
        desc: `新建和管理平台优惠券，促进下单转化。`,
        path: '/marketing_promotion/coupon',
      },
      {
        name: 'store_coupon',
        icon: new URL('/@/assets/images/center/store_coupon.png', import.meta.url).href,
        title: `店铺优惠券`,
        extraFlag: false,
        showFlag: false,
        desc: `查看和管理平台内店铺的优惠券.`,
        path: '/marketing_promotion/store_coupon',
      },
      // dev_mobile-start
      {
        name: 'rank',
        icon: new URL('/@/assets/images/center/rank.png', import.meta.url).href,
        title: `排行榜`,
        extraFlag: false,
        showFlag: false,
        desc: `提供商品排行榜，引导顾客完成购买决策，提高转化率.`,
        path: '/marketing_promotion/rank',
      },
      {
        name: 'svideo',
        icon: new URL('/@/assets/images/center/svideo.png', import.meta.url).href,
        title: `短视频`,
        extraFlag: false,
        showFlag: false,
        desc: `短视频带货，以直观的内容促进转化。`,
        path: '/marketing_svideo',
      },
      // dev_mobile-end
      
      {
        name: 'presale',
        icon: new URL('/@/assets/images/center/presale.png', import.meta.url).href,
        title: `预售`,
        extraFlag: false,
        showFlag: false,
        desc: `热门商品提前预发。`,
        path: '/marketing_promotion/presale',
      },
      {
        name: 'full_acm',
        icon: new URL('/@/assets/images/center/full_acm.png', import.meta.url).href,
        title: `满减`,
        extraFlag: false,
        showFlag: false,
        desc: `满足条件的用户享受减价购买。`,
        path: '/marketing_promotion/full_discount',
        param: '?tab=1',
      },
      {
        name: 'full_asm',
        icon: new URL('/@/assets/images/center/full_asm.png', import.meta.url).href,
        title: `阶梯满减`,
        extraFlag: false,
        showFlag: false,
        desc: `进阶版满减，满足条件的可享受多级优惠。`,
        path: '/marketing_promotion/full_discount',
        param: '?tab=2',
      },
      {
        name: 'full_ald',
        icon: new URL('/@/assets/images/center/full_ald.png', import.meta.url).href,
        title: `满N元折扣`,
        extraFlag: false,
        showFlag: false,
        desc: `购买满N元享受折扣。`,
        path: '/marketing_promotion/full_discount',
        param: '?tab=3',
      },
      {
        name: 'full_nld',
        icon: new URL('/@/assets/images/center/full_nld.png', import.meta.url).href,
        title: `满N件折扣`,
        extraFlag: false,
        showFlag: false,
        desc: `购买满N件享受折扣。`,
        path: '/marketing_promotion/full_discount',
        param: '?tab=4',
      },
      
      // dev_mobile-start
      {
        name: 'sign',
        icon: new URL('/@/assets/images/center/sign.png', import.meta.url).href,
        title: `签到`,
        extraFlag: false,
        showFlag: false,
        desc: `签到送积分和优惠券，提高用户粘性。`,
        path: '/marketing_promotion/sign',
      },
      // dev_mobile-end
      {
        name: 'point',
        icon: new URL('/@/assets/images/center/point.png', import.meta.url).href,
        title: `积分商城`,
        extraFlag: false,
        showFlag: false,
        desc: `积分换购商品，促进用户购买。`,
        path: '/marketing_point',
      },
      // dev_mobile-start
      {
        name: 'sign',
        icon: new URL('/@/assets/images/center/lucky_draw.png', import.meta.url).href,
        title: `幸运抽奖`,
        extraFlag: false,
        showFlag: false,
        desc: `走马灯式抽奖。`,
        path: '/marketing_promotion/lucky_draw_list',
      },
      {
        name: 'sign',
        icon: new URL('/@/assets/images/center/turnplate.png', import.meta.url).href,
        title: `转盘抽奖`,
        extraFlag: false,
        showFlag: false,
        desc: `转盘式抽奖。`,
        path: '/marketing_promotion/turnplate_list',
      },
      {
        name: 'sign',
        icon: new URL('/@/assets/images/center/scratch.png', import.meta.url).href,
        title: `刮刮卡`,
        extraFlag: false,
        showFlag: false,
        desc: `刮卡式抽奖。`,
        path: '/marketing_promotion/scratch_list',
      },
      {
        name: 'sign',
        icon: new URL('/@/assets/images/center/shake.png', import.meta.url).href,
        title: `摇一摇`,
        extraFlag: false,
        showFlag: false,
        desc: `摇动手机进行抽奖。`,
        path: '/marketing_promotion/shake_list',
      },
      {
        name: 'sign',
        icon: new URL('/@/assets/images/center/turn.png', import.meta.url).href,
        title: `翻翻看`,
        extraFlag: false,
        showFlag: false,
        desc: `翻转卡片进行抽奖。`,
        path: '/marketing_promotion/turn_list',
      },
      // dev_mobile-end
      
      {
        name: 'sms_msg',
        icon: new URL('/@/assets/images/center/sms_msg.png', import.meta.url).href,
        title: `短信`,
        extraFlag: false,
        showFlag: false,
        desc: `短信形式高效触达用户。`,
        path: '/sysset_notice_set/msg_tpl',
      },
      // {
      //   name: 'push',
      //   icon: new URL('/@/assets/images/center/push.png', import.meta.url).href,
      //   title: `推送`,
      //   extraFlag: false,
      //   showFlag: specialFlag > -3 ? true : false,
      //   desc: `时效信息、促销活动，智能推送给用户。`,
      //   path: '',
      // },
      {
        name: 'toast',
        icon: new URL('/@/assets/images/center/toast.png', import.meta.url).href,
        title: `弹窗`,
        extraFlag: false,
        showFlag: false,
        desc: `阻断用户行为，提高触达转化率。`,
        path: '',
      },
      {
        name: 'system_msg',
        icon: new URL('/@/assets/images/center/system_msg.png', import.meta.url).href,
        title: `站内信`,
        extraFlag: false,
        showFlag: false,
        desc: `应用内以消息的形式触达用户。`,
        path: '/sysset_notice_set/msg_tpl',
      },
    ], //所有应用一维数据
  });

  const data_center = reactive({
    data: [
      {
        title: `拉新获客`,
        child_path_array: ['/marketing_spreader', '/marketing_promotion'],
        child_name_array: ['spreader', 'spell_group', 'ladder_group'],
        show_num: 1, //展示应用的数量
        child: [],
      },
      {
        title: `促进转化`,
        child_path_array: ['/marketing_svideo', '/marketing_live', '/marketing_promotion'],
        child_name_array: ['svideo', 'live', 'sckill', 'system_coupon', 'store_coupon', 'rank'],
        show_num: 0, //展示应用的数量
        child: [],
      },
      {
        title: `提高客单价`,
        child_path_array: ['marketing_promotion'],
        child_name_array: [
          'presale', 'full_acm', 'full_asm', 'full_ald', 'full_nld',
          
        ],
        show_num: 0, //展示应用的数量
        child: [],
      },
      {
        title: `留存复购`,
        child_path_array: ['marketing_point', 'marketing_promotion'],
        child_name_array: [
          'sign', 'point',
          
        ],
        show_num: 0, //展示应用的数量
        child: [],
      },
      {
        title: `运营工具`,
        child_path_array: ['sysset_notice_set', 'marketing_promotion', 'push', 'decorate'],
        child_name_array: ['sms_msg', 'push', 'toast', 'system_msg'],
        show_num: 0, //展示应用的数量
        child: [],
      },
    ], //应用中心数据
  });

  const initData = async() => {
    // 路由部分最后在写
    let sld_menu_data = localStorage.getItem('sld_menu_data');
    if (sld_menu_data != undefined && sld_menu_data) {
      sld_menu_data = JSON.parse(sld_menu_data);//全部菜单缓存数据
    }
    let cur_menu_data = sld_menu_data.filter(item => menu.value.indexOf(item.frontPath) > -1);//当前页面拥有的菜单数据
    if (cur_menu_data.length > 0) {
      cur_menu_data.map(item => {
        if (item.frontPath == '/marketing_svideo' || item.frontPath == '/marketing_live' || item.frontPath == '/marketing_point' || item.frontPath == '/marketing_spreader') {
          let temp_data = all_data.data.filter(child => child.path == item.frontPath);
          if(temp_data.length>0){
            temp_data[0].showFlag = true;
            temp_data[0].path = item.children[0].frontPath;
          }
        } else if (item.frontPath == '/marketing_promotion' || item.frontPath == '/sysset_notice_set') {
          let all_child_path = [];//该管理员所拥有的应用中心菜单名称数组
          item.children.map(child => {
            all_child_path.push(child.frontPath);
          });
          all_data.data.map(child => {
            if (all_child_path.indexOf(child.path) > -1) {
              child.showFlag = true;
            }
          });
        } else if (item.frontPath.indexOf('decorate') > -1) {
          //弹窗路径，优先移动端首页开屏图，再PC端开屏图
          let temp_data = all_data.data.filter(child => child.name == 'toast');
          temp_data[0].showFlag = true;
          if (item.frontPath == '/decorate_m') {
            temp_data[0].path = '/decorate_m/lists';
          } else if (item.frontPath == '/decorate_pc') {
            temp_data[0].path = '/decorate_pc/home_setting';
          }
        }
      });

      //将all_data 下面的数据都分别组装到data里面
      data_center.data.map(item => {
        all_data.data.map(child => {
          if (item.child_name_array.indexOf(child.name) > -1 && child.showFlag) {
            item.child.push({ ...child });
            item.show_num += 1;
          }
        });
      });

    } else {
      emptyFlag.value = true;
    }
    loading.value = false
  };

  const go = (val) => {
    if (val.name == 'push') {
      sucTip('更新中，敬请期待～');
    } else {
      let path = val.path;
      if (val.param != undefined && val.param) {
        path += val.param;
      }
      router.push(path);
    }
  };

  onMounted(() => {
    initData();
  });
</script>
<style lang="less">
  .promotion_center {
    .section_padding_back_scroll_mark{
      &::-webkit-scrollbar{
        display: none;
      }
    }

    .center_title {
      color: #404040;
      font-family: PingFangSC-Semibold, 'PingFang SC';
      font-size: 18px;
      font-weight: 600;
    }

    .center_item {
      .center_item_title {
        margin-top: 30px;
        color: #404040;
        font-size: 15px;
        font-weight: 600;
      }

      .center_item_content {
        .child_item {
          width: calc(25% - 15px);
          height: 100px;
          margin-top: 20px;
          padding-top: 20px;
          border-radius: 10px;
          background: #f9f9f9;
          cursor: pointer;

          .left_img {
            width: 60px;
            height: 60px;
            margin-left: 20px;
          }

          .right_part {
            width: calc(100% - 90px);
            margin-left: 15px;
            padding-right: 20px;

            .right_part_top {
              margin-top: 4px;

              .top_title {
                color: #404040;
                font-family: PingFangSC-Semibold, 'PingFang SC';
                font-size: 16px;
                font-weight: 600;
                line-height: 25px;
              }

              .top_flag {
                display: inline-block;
                height: 20px;
                margin-left: 10px;
                padding: 0 10px;
                border-radius: 10px 10px 10px 0;
                background: linear-gradient(0deg, #fcc102 0%, #ffa710 100%);
                color: #fff;
                font-family: PingFangSC-Semibold, 'PingFang SC';
                font-size: 12px;
                font-weight: 600;
                line-height: 20px;
              }
            }

            .desc {
              margin-top: 5px;
              overflow: hidden;
              color: rgb(64 64 64 / 74%);
              font-family: PingFangSC-Regular, 'PingFang SC';
              font-size: 14px;
              font-weight: 400;
              line-height: 20px;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
      }
    }
  }
</style>
