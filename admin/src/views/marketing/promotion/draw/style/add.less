.draw_add {
  .draw_add_from {
    max-height: calc(100vh - @header-height - 20px - 20px - 66px);
    overflow: auto;
  }
  //应用中心样式-end
  .rank_load_goods_btn {
    width: 125px;
    height: 34px;
    margin-right: 20px;
    padding: 0 10px;
    border: 1px solid rgb(255 113 30 / 100%);
    border-radius: 2px;
    background: #ff6a12;
    color: #fff;
    font-size: 14px;
    line-height: 32px;
    text-align: center;
    cursor: pointer;
  }

  .ant-form-item-control {
    .ant-form-item-explain {
      position: absolute;
      z-index: 2;
      top: -10px;
      right: 2%;
      min-height: 20px;
      background: #fff;
      font-size: 13px;
      text-align: left;
    }
  }

  .ant-form-inline .ant-form-item-with-help {
    margin-bottom: 0;
  }

  .full_activity {
    .add_rand_title_bg {
      width: 100%;
      height: 45px;
      border: 1px solid #f5f5f5;
      border-bottom-width: 0;
      background: #fffaf7;
      line-height: 45px;

      .title {
        padding-left: 15px;
        color: #323233;
        font-family: PingFangSC-Regular, 'PingFang SC';
        font-size: 14px;
        font-weight: 500;
      }
    }

    .full_acm_activity {
      .item {
        padding-top: 15px;

        .left {
          width: 200px;
          height: 32px;
          padding-right: 20px;
          font-size: 13px;
          line-height: 32px;
          text-align: right;
        }

        .right {
          .reset_sel {
            display: inline-block;
            margin-top: 9px;
            margin-bottom: 10px;
            margin-left: 0;
            color: #ff711e;
            font-size: 13px;
            cursor: pointer;
          }
        }
      }
    }

    .flex_zi {
      flex: 1;
      align-items: center;
      justify-content: center;
    }

    .common_title_bg {
      width: 100%;
      height: 36px;
      border-radius: 2px;
      background: #fffaf7;
      line-height: 36px;

      .title {
        padding-left: 10px;
        color: #333;
        font-size: 13px;
      }

      .del_ladder_pro {
        display: none;
        padding-top: 7px;
        padding-right: 15px;
      }
    }

    .add_new {
      width: 100%;
      height: 40px;
      margin-top: 15px;
      border-radius: 3px;
      background: #fffaf7;

      .add_new_tip {
        margin-left: 10px;
        color: #ff7f40;
        font-size: 12px;
      }
    }

    .sel_goods {
      padding-left: 24px;

      .sel_tip {
        color: #999;
        font-size: 12px;
      }

      .reset_sel_goods {
        color: #ff7f40;
        font-size: 12px;
        cursor: pointer;
      }

      .goods_info {
        min-width: 260px;
        max-width: 700px;
        height: 60px;
        margin-top: 5px;
        margin-left: 10px;
        padding: 10px;
        border-radius: 3px;
        background: #f8f8f8;

        .left {
          flex-shrink: 0;
          width: 40px !important;
          height: 40px !important;
          margin-right: 10px;
          padding-right: 0 !important;
          overflow: hidden;
          border-radius: 3px;

          img {
            max-width: 100%;
            max-height: 100%;
            border-radius: 3px;
          }
        }

        .goods_name {
          color: #333;
          font-size: 13px;
        }

        .goods_price {
          margin-top: 3px;
          color: #666;
          font-size: 12px;
          white-space: nowrap;
        }
      }
    }
  }

  .goods_sku_tab {
    border: 1px solid #f0f0f0;
  }

  .add_rand_title_bg {
    border: none !important;
  }

  .sld_det_r_item {
    border-bottom: 0;

    &:first-child {
      border-left: 0;
    }

    &:last-child {
      border-right: 0;
    }
  }

  .sld_det_lr_item_wrap {
    border-left: 0;
  }

  .business_load {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;

    .business_load_img {
      width: 20px;
      height: 20px;
      margin: 0 5px;

      img {
        max-width: 100%;
        max-height: 100%;
      }
    }
  }

  .ant-table-wrapper {
    padding: 6px 0;
  }
}
