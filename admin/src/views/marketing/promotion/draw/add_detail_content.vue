<template>
  <Modal
    :destroyOnClose="true"
    :maskClosable="false"
    :zIndex="999"
    :width="700"
    :visible="modalVisible"
    @ok="sldConfirm"
    @cancel="sldCancle"
    :title="!editPrizeKey ? '添加奖项' : '编辑奖项'"
  >
    <div class="add_detail_content full_activity com_flex_column ant_form_ex">
      <Form
        ref="formRefs"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 17 }"
        :model="detail"
        :scrollToFirstError="true"
        style="margin-top: 5px"
      >
        <div class="goods_sku_tab add_goods_wrap">
          <Form.Item
            name="prizeName"
            label="奖项名称"
            :wrapper-col="{ span: 7 }"
            extra="最多输入6个字"
            :rules="[
              {
                required: true,
                whitespace: true,
                message: `请输入奖项名称`,
              },
            ]"
          >
            <Input
              placeholder="请输入奖项名称"
              style="width: 200px !important"
              type="text"
              :maxlength="6"
              v-model:value="detail.prizeName"
            />
          </Form.Item>
        </div>
        <div>
          <Form.Item
            label="奖项图片"
            :extra="`建议上传【宽${uploadImgW}*高${uploadImgH}】的图片，支持gif，jpeg，jpg，png格式的图片`"
          >
            <div class="upload_file">
              <Upload
                :maxCount="1"
                accept=" .gif, .jpeg, .png, .jpg,"
                name="file"
                @click="beforeUploadClick"
                :action="`${apiUrl}/v3/oss/admin/upload?source=setting`"
                listType="picture-card"
                :beforeUpload="(file) => beforeUpload(file, ' .gif, .jpeg, .png, .jpg,')"
                :file-list="bgFileList"
                @change="(e) => handleFileChange(e)"
                :headers="{
                  Authorization: imgToken,
                }"
              >
                <div v-if="bgFileList.length < 1">
                  <PlusOutlined />
                  <div className="ant-upload-text">上传图片</div>
                </div>
              </Upload>
            </div>
          </Form.Item>
        </div>
        <div>
          <div class="item flex_row_start_start">
            <div class="left" style="width: 167px">
              <span :style="{ color: 'red' }">*</span>奖品设置
            </div>
            <div class="right flex_column_start_start">
              <RadioGroup size="small" :value="prizeType" @change="radio_change($event)">
                <div class="flex_column_start_start">
                  <Radio :value="1">
                    <div class="flex_row_start_center">
                      <span class="input_left_side_tip">奖励积分</span>
                      <Form.Item
                        name="integralNum"
                        :rules="
                          prizeType == 1
                            ? [
                                {
                                  required: true,
                                  message: `此处必填`,
                                },
                              ]
                            : []
                        "
                      >
                        <InputNumber
                          :disabled="prizeType == 2"
                          :max="99999"
                          :min="1"
                          :precision="0"
                          style="width: 75px !important"
                          placeholder="请输入"
                          v-model:value="detail.integralNum"
                        />
                      </Form.Item>
                      <span class="input_right_side_tip">分</span>
                    </div>
                  </Radio>
                  <div class="flex_row_start_center">
                    <Radio :value="2">
                      <div class="flex_row_start_center">
                        <span class="input_left_side_tip">奖励优惠券</span>
                        <span
                          class="reset_sel"
                          style="margin-top: 12px; font-size: 12px"
                          @click="resetSel('voucher')"
                        >
                          {{
                            sel_voucher.couponId != undefined && sel_voucher.couponId
                              ? '重新选择'
                              : '选择优惠券'
                          }}
                        </span>
                      </div>
                    </Radio>
                  </div>
                </div>
              </RadioGroup>
              <div
                class="sel_goods flex_column_start_start"
                v-if="sel_voucher.couponId != undefined && sel_voucher.couponId"
              >
                <div class="flex_row_start_center">
                  <span class="sel_tip">您已选择如下优惠券：</span>
                </div>
                <div class="goods_info flex_row_start_center">
                  <div class="left flex_row_center_center">
                    <img src="@/assets/images/voucher.png" alt="" />
                  </div>
                  <div class="flex_column_between_start">
                    <span class="goods_name">优惠券</span>
                    <span class="goods_price"
                      >{{ sel_voucher.couponName }}【{{ sel_voucher.couponContent }}】</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div>
          <div class="item flex_row_start_start">
            <div class="left" style="width: 167px">
              <span :style="{ color: 'red' }">*</span>奖品数量
            </div>
            <div class="right flex_row_start_center">
              <Form.Item
                name="prizeNum"
                :rules="[
                  {
                    required: true,
                    message: `此项必填`,
                  },
                ]"
              >
                <InputNumber
                  :max="
                    sel_voucher.couponId != undefined && sel_voucher.couponId
                      ? sel_voucher.remainNum
                      : 1000000
                  "
                  :min="1"
                  :precision="0"
                  style="width: 100px !important"
                  placeholder="请输入"
                  v-model:value="detail.prizeNum"
                />
              </Form.Item>
              <span style="margin-left: 10px">份{{ detail.fullValue }}</span>
            </div>
          </div>
        </div>
        <div>
          <div class="item flex_row_start_start">
            <div class="left" style="width: 167px">
              <span :style="{ color: 'red' }">*</span>中奖率
            </div>
            <div class="right flex_column_start_start">
              <div class="flex_row_start_center">
                <Form.Item
                  name="rate"
                  :rules="[
                    {
                      required: true,
                      message: `此项必填`,
                    },
                  ]"
                >
                  <InputNumber
                    :max="allowMaxRate"
                    :min="1"
                    :precision="0"
                    style="width: 100px !important"
                    placeholder="请输入"
                    v-model:value="detail.rate"
                  />
                </Form.Item>
                <span style="margin-right: 10px; margin-left: 10px">%{{ detail.fullValue }}</span>
                <div class="" v-if="curTotalRate > 0">
                  <span>当前已设置奖项中奖率之和为</span>
                  <span style="color: #ff1818; font-size: 14px">{{ curTotalRate }}%</span>
                </div>
              </div>
              <span class="form_item_bottom_tip"
                >设为10，则每位抽奖会员的中奖概率为10%，活动全部奖项中奖率之和不可大于100%</span
              >
            </div>
          </div>
        </div>
      </Form>
    </div>
  </Modal>
  <SldSelGoodsSingleDiy
    @confirm-event="handleConfirm"
    @cancle-event="handleCancle"
    :link_type="link_type"
    :modalVisible="visibleModal"
  ></SldSelGoodsSingleDiy>
</template>
<script>
  export default {
    name: 'AddDetailContent',
  };
</script>
<script setup>
  import { ref, onMounted, watch, computed } from 'vue';
  import { useGlobSetting } from '/@/hooks/setting';
  import {
    Form,
    Upload,
    Modal,
    InputNumber,
    Input,
    RadioGroup,
    Radio,
  } from 'ant-design-vue';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import SldSelGoodsSingleDiy from '/@/components/SldSelGoodsSingleDiy/index.vue';
  import { getToken } from '/@/utils/auth';
  import { failTip } from '/@/utils/utils';
  import { useUserStore } from '@/store/modules/user';

  const props = defineProps({
    drawType: {
      type: String,
    },
    modalVisible: {
      type: Boolean,
      default: false,
    },
    editPrizeKey: {
      type: Number,
      default: 0,
    },
    saveFlag: {
      type: Number,
      default: 1,
    },
    prizeData: {
      type: Array,
      default() {
        return [];
      },
    },
  });

  // 弹窗开关
  const modalVisible = computed(() => {
    return props.modalVisible;
  });



  // 数据
  const prizeData = computed(() => {
    return JSON.parse(JSON.stringify(props.prizeData));
  });

  const userStore = useUserStore();

  const emit = defineEmits(['cancleEvent', 'confirmEvent']);

  const visibleModal = ref(false);
  const { apiUrl } = useGlobSetting();
  const imgToken = ref('Bearer ' + getToken() || ''); //上传文件token参数
  const prizeType = ref(1); //奖品类型，1为积分，2为优惠券
  const curTotalRate = ref(0); //当前总共的中奖率之和
  const allowMaxRate = ref(0); //允许输入的最大值
  const preview_img = ref('');
  const preview_alt_con = ref('');
  const show_preview_modal = ref(false);
  const curSaveFlag = ref(false);
  const sel_voucher = ref({}); //选择的优惠券信息
  const link_type = ref('');
  const loading = ref(false);
  const detail = ref({}); //活动详情数据
  const bgFileList = ref([]); //奖项图片
  //奖品图片尺寸提示
  const uploadImgW = ref(54);
  const uploadImgH = ref(54);

  const formRefs = ref();

  const sldConfirm = () => {
    let { editPrizeKey, prizeData, drawType, saveFlag } = props;

    formRefs.value
      .validate()
      .then((res) => {
        //判断奖品名字是否重复
        if (prizeData.length > 0) {
          for (let i in prizeData) {
            if (res.prizeName == prizeData[i].prizeName) {
              if ((editPrizeKey && editPrizeKey != prizeData[i].key) || !editPrizeKey) {
                failTip(`奖项名称不可重复～`);
                return false;
              }
            }
          }
        }
        if (bgFileList.value.length == 0) {
          res.prizeImage = '';
          res.prizeImageUrl = '';
        } else {
          res.prizeImage = bgFileList.value[0].response.data.path;
          res.prizeImageUrl = bgFileList.value[0].response.data.url;
        }
        res.prizeType = prizeType.value; //奖品类型，1为积分，2为优惠券
        if (res.prizeType == 2) {
          if (sel_voucher.value.couponId == undefined) {
            failTip(`请选择优惠券～`);
            return;
          } else {
            res.couponId = sel_voucher.value.couponId;
            res.couponName = sel_voucher.value.couponName;
            res.couponContent = sel_voucher.value.couponContent;
          }
        }
        emit('confirmEvent', res);
      })
      .catch((err) => {
        console.log('error', err);
      });
  };
  //取消/关闭弹框事件
  const sldCancle = () => {
    
    emit('cancleEvent');
  };

  //文件上传前处理数据
  function beforeUpload(file, accept) {
    if (accept != undefined && accept != null && accept) {
      //校验文件格式类型
      let accept_list = accept
        .replaceAll(' ', '')
        .split(',')
        .filter((item) => item && item.trim());
      let file_type = file.name ? '.' + file.name.split('.').at(-1) : '';
      if (accept_list.length > 0 && file_type != '' && accept_list.indexOf(file_type) == -1) {
        failTip('上传文件格式有误');
        return false;
      }
    }
    let uploadLimit = 20;
    if (file.size > 1024 * 1024 * uploadLimit) {
      failTip('上传文件过大，请上传小于' + uploadLimit + 'M的文件');
      return false;
    }
  }

  //数据变化事件
  function handleFileChange(e) {
    if (e.file.status != undefined && e.file.status != 'error') {
      e.fileList.forEach((item) => {
        if (item.response && item.response.data) {
          item.url = item.response.data.url;
        }
      });
      bgFileList.value = e.fileList;
    }
  }

  // 单选框
  const radio_change = (e) => {
    prizeType.value = e.target.value;
    if (e.target.value == 2) {
      detail.value.integralNum = '';
      formRefs.value.resetFields(['integralNum', '']);
    } else {
      sel_voucher.value = {};
    }
  };

  const beforeUploadClick = async()=> {
    imgToken.value = 'Bearer ' + userStore.getToken
    if(userStore.getToken){
      let res = await userStore.getSldCommonService()
      if(res.state == 200){
        imgToken.value = 'Bearer ' + res.token
      }
    }
  }

  // 优惠券弹框确定
  const handleConfirm = (record, ids) => {
    if (record.couponType == 1) {
      record.couponName = record.publishValue * 1 + '元券';
    } else if (record.couponType == 2) {
      record.couponName = (record.publishValue / 10).toFixed(1) * 1 + '折券';
    } else if (record.couponType == 3) {
      record.couponName = record.randomMin * 1 + '～' + record.randomMax * 1 + '元随机券';
    }
    sel_voucher.value = record;
    visibleModal.value = false;
  };

  // 优惠券弹框取消
  const handleCancle = () => {
    visibleModal.value = false;
  };

  // 打开优惠券弹窗
  const resetSel = (type) => {
    link_type.value = type;
    visibleModal.value = true;
  };

  watch(
    modalVisible,
    () => {
      if (modalVisible.value) {
        bgFileList.value = []
        if (!props.editPrizeKey) {
          detail.value = {};
        }
        //抽奖活动类型，1-幸运抽奖，2-大转盘，3-刮刮卡，4-摇一摇，5-翻翻看
        if (props.drawType == '1') {
          uploadImgW.value = 54;
          uploadImgH.value = 54;
        } else if (props.drawType == '2') {
          uploadImgW.value = 62;
          uploadImgH.value = 62;
        } else if (props.drawType == '5') {
          uploadImgW.value = 66;
          uploadImgH.value = 66;
        }

        let { editPrizeKey, saveFlag } = props;
        curSaveFlag.value = saveFlag;
        let curTotalRates = 0;
        if (prizeData.value.length > 0) {
          prizeData.value.map((item) => {
            curTotalRates += item.rate * 1;
          });
        }
        allowMaxRate.value = 100 - curTotalRates;
        if (editPrizeKey > 0) {
          detail.value = prizeData.value.filter((item) => item.key == editPrizeKey)[0];
          //图片的处理
          bgFileList.value = [];
          if (detail.value.prizeImage) {
            let tmp_data = {};
            tmp_data.uid = detail.value.prizeImage;
            tmp_data.name = detail.value.prizeImage;
            tmp_data.status = 'done';
            tmp_data.url = detail.value.prizeImageUrl;
            tmp_data.response = {
              data: {
                url: detail.value.prizeImageUrl,
                path: detail.value.prizeImage,
              },
            };
            bgFileList.value.push(tmp_data);
          }
          //选中的优惠券处理
          if (detail.value.prizeType == 2) {
            detail.value.integralNum = '';
            sel_voucher.value.couponId = detail.value.couponId;
            sel_voucher.value.couponName = detail.value.couponName;
            sel_voucher.value.couponValueStr = detail.value.couponValueStr;
            sel_voucher.value.couponContent = detail.value.couponContent;
          }
          allowMaxRate.value = allowMaxRate.value + detail.value.rate * 1;
          prizeType.value = detail.value.prizeType;
        }
        curTotalRate.value = curTotalRates;
      }
    },
    { deep: true },
  );

  onMounted(() => {});
</script>
<style lang="less">
  .add_detail_content {
    max-height: 600px;
    padding: 10px;
    overflow: auto;

    .input_right_side_tip {
      display: inline-block;
      margin-left: 5px;
      color: rgb(0 0 0 / 65%);
    }

    .item {
      margin-bottom: 24px;

      .left {
        width: 200px;
        height: 32px;
        padding-right: 20px;
        font-size: 13px;
        line-height: 32px;
        text-align: right;
      }

      .right {
        .reset_sel {
          display: inline-block;
          margin-top: 9px;
          margin-bottom: 10px;
          margin-left: 0;
          color: #ff711e;
          font-size: 13px;
          cursor: pointer;
        }

        .ant-col {
          max-width: 100%;
        }

        .ant-form-item {
          margin-bottom: 0;
        }
      }
    }

    .form_item_bottom_tip {
      clear: both;
      transition: color 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
      color: rgb(0 0 0 / 45%);
      font-size: 14px;
      line-height: 1.5715;
    }

    .sel_goods {
      padding-left: 24px;

      .sel_tip {
        color: #999;
        font-size: 12px;
      }

      .reset_sel_goods {
        color: #ff7f40;
        font-size: 12px;
        cursor: pointer;
      }

      .goods_info {
        min-width: 260px;
        max-width: 700px;
        height: 60px;
        margin-top: 5px;
        margin-left: 10px;
        padding: 10px;
        border-radius: 3px;
        background: #f8f8f8;

        .left {
          flex-shrink: 0;
          width: 40px !important;
          height: 40px !important;
          margin-right: 10px;
          padding-right: 0 !important;
          overflow: hidden;
          border-radius: 3px;

          img {
            max-width: 100%;
            max-height: 100%;
            border-radius: 3px;
          }
        }

        .goods_name {
          color: #333;
          font-size: 13px;
        }

        .goods_price {
          margin-top: 3px;
          color: #666;
          font-size: 12px;
          white-space: nowrap;
        }
      }
    }
  }
</style>
