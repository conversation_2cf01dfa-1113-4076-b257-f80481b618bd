<template>
  <div style="padding: 20px">
    <BasicTable @register="registerTable" rowKey="recordId"></BasicTable>
  </div>
</template>
<script>
  export default {
    name: 'DrawRecordList',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { validatorEmoji } from '/@/utils/validate';
  import { getDrawRecordListApi } from '/@/api/promotion/draw';

  const props = defineProps({
    drawId: {
      type: Number,
    },
  });

  const drawId = ref(0);

  const columns = reactive({
    data: [
      {
        title: `会员名称`,
        dataIndex: 'memberName',
        align: 'center',
        width: 100,
      },
      {
        title: `中奖信息`,
        dataIndex: 'description',
        align: 'center',
        width: 100,
      },
      {
        title: `中奖状态`,
        dataIndex: 'isPrizeValue',
        align: 'center',
        width: 100,
      },
      {
        title: `抽奖时间`,
        dataIndex: 'recordTime',
        align: 'center',
        width: 100,
      },
    ],
  });

  const searchFormSchema = reactive({
    data: [
      {
        component: 'Input',
        label: `会员名称`,
        field: 'memberName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请输入会员名称`,
        },
        rules: [
          {
            // @ts-ignore
            validator: async (rule, value) => {
              await validatorEmoji(rule, value);
            },
            trigger: 'change',
          },
        ],
      },
      {
        colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
        component: 'RangePicker',
        label: `抽奖时间`,
        field: '[startTime,endTime]',
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: ['开始时间', '结束时间'],
        },
      },
      {
        component: 'Select',
        label: `中奖状态`,
        field: 'isPrize',
        labelWidth: 90,
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请选择中奖状态`,
          options: [
            { value: '', label: `全部` },
            { value: '0', label: `未中奖` },
            { value: '1', label: `已中奖` },
          ],
        },
      },
    ],
  });

  // 表格数据
  const [registerTable, { reload, getForm }] = useTable({
    // 请求接口
    api: getDrawRecordListApi,
    // 表头
    columns: columns.data,
    immediate: true,
    // 参数
    searchInfo: {
      drawId: props.drawId,
    },
    // 点击搜索前处理的参数
    beforeFetch(values) {
      values.startTime = values.startTime
        ? values.startTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
      return values;
    },
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: searchFormSchema.data,
    },
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    maxHeight: 300,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    // 是否显示序号列
    showIndexColumn: true,
  });

  onMounted(() => {});
</script>
<style lang="less" scoped>
  p {
    margin-bottom: 0;
  }
</style>
<style lang="less"></style>
