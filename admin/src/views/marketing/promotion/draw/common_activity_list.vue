<template>
  <div class="common_activity_list">
    <SldComHeader :title="title" />
    <BasicTable @register="registerTable" rowKey="drawId">
      <template #tableTitle>
        <div class="toolbar">
          <div class="toolbar_btn" @click="add_activity">
            <AliSvgIcon iconName="iconfabu1" width="15px" height="15px" :fillColor="'#FA6F1E'" />
            <span style="margin-left: 4px">新建活动</span>
          </div>
        </div>
      </template>
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex === 'startTime'">
          <div class="voucher_time_wrap">
            <p>{{ text }}</p>
            <p>~</p>
            <p>{{ record.endTime }}</p>
          </div>
        </template>
        <template v-if="column.dataIndex === 'prizeNum'">
          <div class="voucher_num" @click="viewDetail(record.drawId)">
            {{ record.prizeNum }}/{{ record.drawNum }}
          </div>
        </template>
        <template v-if="column.key === 'action'">
          <TableAction
            :actions="[
              {
                label: '查看',
                onClick: view.bind(null, record.drawId, 'view'),
              },
              {
                label: '编辑',
                ifShow: record.state == 1,
                onClick: view.bind(null, record.drawId, 'edit'),
              },
              {
                label: '复制',
                onClick: view.bind(null, record.drawId, 'copy'),
              },
              {
                label: '删除',
                popConfirm: {
                  title: '删除后不可恢复，是否确定删除？',
                  placement: 'left',
                  confirm: operate.bind(null, { drawId: record.drawId }, 'del'),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
  </div>
  <Modal
    destroyOnClose
    :maskClosable="false"
    title="查看中奖详情"
    :visible="modalVisible"
    :width="1000"
    :footer="null"
    @cancel="sldCancle"
  >
    <DrawRecordList :drawId="drawId"></DrawRecordList>
  </Modal>
</template>
<script>
  export default {
    name: 'CommonActivityList',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import DrawRecordList from './draw_record_list.vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { Modal } from 'ant-design-vue';
  import { validatorEmoji } from '/@/utils/validate';
  import { getDrawListApi, getDrawDelApi } from '/@/api/promotion/draw';
  import { failTip, sucTip } from '/@/utils/utils';
  import { useUserStore } from '/@/store/modules/user';

  const router = useRouter();

  const modalVisible = ref(false);

  const drawId = ref(0);

  const userStore = useUserStore();

  const props = defineProps({
    title: {
      type: String,
    },
    drawType: {
      type: Number,
    },
  });

  const visible = ref(false);

  const fullId = ref(0);

  const columns = reactive({
    data: [
      {
        title: `活动名称`,
        dataIndex: 'drawName',
        align: 'center',
        width: 100,
      },
      {
        title: `活动时间`,
        dataIndex: 'startTime',
        align: 'center',
        width: 100,
        // customRender: ({ text, record }) => {
        //   return `${}`;
        // },
      },
      {
        title: `抽奖规则`,
        dataIndex: 'ruleValue',
        align: 'center',
        width: 100,
      },
      {
        title: `中奖人数/抽奖人数`,
        dataIndex: 'prizeNum',
        align: 'center',
        width: 100,
      },
      {
        title: `活动状态`,
        dataIndex: 'stateValue',
        align: 'center',
        width: 100,
      },
    ],
  });

  const searchFormSchema = reactive({
    data: [
      {
        component: 'Input',
        label: `活动名称`,
        field: 'drawName',
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请输入活动名称`,
        },
        rules: [
          {
            // @ts-ignore
            validator: async (rule, value) => {
              await validatorEmoji(rule, value);
            },
            trigger: 'change',
          },
        ],
      },
      {
        colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
        component: 'RangePicker',
        label: `活动时间`,
        field: '[startTime,endTime]',
        componentProps: {
          format: 'YYYY-MM-DD',
          placeholder: ['开始时间', '结束时间'],
        },
      },
      {
        component: 'Select',
        label: `活动状态`,
        field: 'state',
        labelWidth: 90,
        colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
        componentProps: {
          placeholder: `请选择活动状态`,
          options: [
            { value: '', label: `全部` },
            { value: '1', label: `未开始` },
            { value: '2', label: `进行中` },
            { value: '3', label: `已结束` },
          ],
        },
      },
    ],
  });

  // 表格数据
  const [registerTable, { reload, getForm }] = useTable({
    // 请求接口
    api: getDrawListApi,
    // 表头
    columns: columns.data,
    immediate: true,
    // 参数
    searchInfo: {
      drawType: props.drawType,
    },
    // 点击搜索前处理的参数
    beforeFetch(values) {
      values.startTime = values.startTime
        ? values.startTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
      return values;
    },
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: searchFormSchema.data,
    },
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: true,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    // 是否显示序号列
    showIndexColumn: true,
    // 表格右侧操作列配置
    actionColumn: {
      width: 180,
      title: '操作',
      dataIndex: 'action',
      // slots: { customRender: 'action' },
    },
  });

  const sldHandleCancle = () => {
    visible.value = false;
  };

  const view = (id, type) => {
    if(type=='view'){
      let pathName = getTarPathName(props.drawType,'View')
      userStore.setDelKeepAlive([pathName])
    }else if(type == 'edit'){
      let pathName = getTarPathName(props.drawType,'Edit')
      userStore.setDelKeepAlive([pathName])
    }else if(type == 'copy'){
      let pathName = getTarPathName(props.drawType,'Copy')
      userStore.setDelKeepAlive([pathName])
    }
    router.push({
      path: getTarPath(props.drawType,type),
      query: { drawType: props.drawType, id: id, type: type },
    });
  };

  //操作  type: invalid 失效
  const operate = async (id, type) => {
    let param_data = {};
    let res = null;
    if (type == 'del') {
      param_data = id;
      res = await getDrawDelApi(param_data);
    }
    if (res.state == 200) {
      sucTip(res.msg);
      reload();
    } else {
      failTip(res.msg);
    }
  };

  //根据活动类型获取对应路由
  const getTarPath = (drawType,type) => {
    let target = '';
    //抽奖活动类型，1-幸运抽奖，2-大转盘，3-刮刮卡，4-摇一摇，5-翻翻看
    if (drawType == 1) {
      target = `/marketing_promotion/lucky_draw_list_to_${type}`;
    } else if (drawType == 2) {
      target = `/marketing_promotion/turnplate_list_to_${type}`;
    } else if (drawType == 3) {
      target = `/marketing_promotion/scratch_list_to_${type}`;
    } else if (drawType == 4) {
      target = `/marketing_promotion/shake_list_to_${type}`;
    } else if (drawType == 5) {
      target = `/marketing_promotion/turn_list_to_${type}`;
    }
    return target;
  };
  
  //根据活动类型获取对应路由name
  const getTarPathName = (drawType,type) => {
    let target = '';
    //抽奖活动类型，1-幸运抽奖，2-大转盘，3-刮刮卡，4-摇一摇，5-翻翻看
    if (drawType == 1) {
      target = `DrawLuckyListTo${type}`;
    } else if (drawType == 2) {
      target = `TurnplateListTo/${type}`;
    } else if (drawType == 3) {
      target = `ScratchListTo${type}`;
    } else if (drawType == 4) {
      target = `ShakeListTo${type}`;
    } else if (drawType == 5) {
      target = `TurnListTo${type}`;
    }
    return target;
  };

  // 进入新建活动页面
  const add_activity = () => {
    let pathName = getTarPathName(props.drawType,'Add')
    userStore.setDelKeepAlive([pathName])
    router.push({
      path: getTarPath(props.drawType,'add'),
      query: { drawType: props.drawType },
    });
  };

  const viewDetail = (id) => {
    drawId.value = id;
    setTimeout(() => {
      modalVisible.value = true;
    });
  };

  const sldCancle = () => {
    modalVisible.value = false;
    drawId.value = 0;
  };

  onMounted(() => {});
</script>
<style lang="less" scoped>
  p {
    margin-bottom: 0;
  }

  .common_activity_list {
    .voucher_num {
      color: @primary-color;
      cursor: pointer;
    }
  }
</style>
<style lang="less">
  .full_modal {
    .ant-modal-header {
      padding: 12px 24px !important;
      background-color: @primary-color;
    }

    .ant-modal-title {
      color: #fff;
      font-size: 14px !important;
      line-height: 18px !important;
    }

    .ant-modal-close-x .anticon svg {
      fill: #fff;
    }

    .ant-modal .ant-modal-content {
      border-radius: 4px;
    }

    .ant-form-item-with-help {
      margin-bottom: 24px;
    }

    .ant-form-item-has-error .ant-input-number,
    .ant-form-item-has-error .ant-picker {
      border-color: #ed6f6f !important;
    }

    .ant-modal-close {
      top: 12px;
      right: 20px;
      height: auto !important;

      .ant-modal-close-x {
        display: flex !important;
        align-items: center;
        justify-content: center;
        width: auto !important;
        height: auto !important;
        line-height: normal;
      }
    }
  }

  .common_activity_list {
    .vben-basic-table-form-container .ant-form {
      padding-top: 0;
    }

    .voucher_num {
      color: #ff6a12;
      cursor: pointer;
    }
  }
</style>
