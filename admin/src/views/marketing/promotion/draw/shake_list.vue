<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <CommonActivityList title="摇一摇" :drawType="4"></CommonActivityList>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'DrawShakeList',
  };
</script>
<script setup>
  import { ref, reactive, onMounted } from 'vue';
  import CommonActivityList from './common_activity_list.vue';

  const activeKey = ref('1');

  const RadioGroupValue = ref('1');

  onMounted(() => {});
</script>
<style lang="less"></style>
