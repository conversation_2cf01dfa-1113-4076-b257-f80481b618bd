<template>
  <div class="section_padding draw_add" style="min-width: 800px">
    <div class="section_padding_back">
      <SldComHeader :title="pageTitle" />
      <div class="com_line" style="height: 1px"></div>
      <Spin :spinning="loading">
        <div class="draw_add_from">
          <Form layout="inline" ref="formRef" :model="detail" style="justify-content: center">
            <div class="sld_det_lr_wrap">
              <template v-for="(item, index) in draw_info_list" :key="index">
                <div style="width: 100%; height: 20px" v-if="item.flag"></div>
                <div class="goods_sku_tab add_goods_wrap full_activity" v-if="item.flag">
                  <div class="add_rand_title_bg">
                    <span class="title">{{ item.title }}</span>
                  </div>
                  <template v-for="(it, ind) in item.children" :key="ind">
                    <div
                      class="sld_det_lr_item_wrap flex_row_start_center"
                      v-if="item.flag && it.typeFlag"
                    >
                      <div
                        class="sld_det_r_item flex_row_end_center"
                        :style="{
                          width: '20%',
                          height: it.height ? it.height : '76px',
                          borderTopWidth: '1px',
                        }"
                      >
                        <div style="color: red" v-if="it.flag"> * </div>
                        {{ it.title }}
                      </div>
                      <div
                        class="sld_det_r_item flex_column_center_start"
                        :style="{
                          width: '80%',
                          height: '76px',
                          borderTopWidth: '1px',
                          paddingLeft: '20px',
                        }"
                        v-if="it.type == 'input' && it.typeFlag"
                      >
                        <div class="sld_det_r_text" style="width: 100%">
                          <Form.Item
                            :extra="it.extra"
                            :name="it.name"
                            style="width: 400px"
                            :rules="it.required ? it.required : []"
                          >
                            <Input
                              :disabled="viewFlag"
                              :maxlength="it.maxlength"
                              v-model:value="detail[it.name]"
                              style="width: 400px"
                              :placeholder="it.placeholder"
                            />
                          </Form.Item>
                        </div>
                      </div>
                      <div
                        class="sld_det_r_item flex_column_center_start"
                        :style="{
                          width: '80%',
                          height: '76px',
                          borderTopWidth: '1px',
                          paddingLeft: '20px',
                        }"
                        v-if="it.type == 'RangePicker' && it.typeFlag"
                      >
                        <div class="sld_det_r_text" style="width: 100%">
                          <Form.Item
                            :extra="it.extra"
                            :name="it.name"
                            style="width: 400px"
                            :rules="it.required"
                          >
                            <RangePicker
                              :disabled="viewFlag"
                              :format="'YYYY-MM-DD HH:mm:00'"
                              v-model:value="detail[it.name]"
                              :disabledDate="it.disabledDate"
                              :showTime="it.show_time != undefined ? it.show_time : false"
                              :style="{ width: '100%' }"
                              :placeholder="[it.placeholder1, it.placeholder2]"
                            />
                          </Form.Item>
                        </div>
                      </div>
                      <div
                        class="sld_det_r_item flex_column_center_start"
                        :style="{
                          width: '80%',
                          height: '76px',
                          borderTopWidth: '1px',
                          paddingLeft: '20px',
                        }"
                        v-if="it.type == 'RadioInput' && it.typeFlag"
                      >
                        <RadioGroup
                          :disabled="viewFlag"
                          size="small"
                          :value="it.integralUseType"
                          @change="radio_change($event, index, ind, it.name, null)"
                        >
                          <div class="flex_row_start_center">
                            <Radio :value="1"> 无限制 </Radio>
                            <div class="flex_row_start_center">
                              <Radio :value="2">
                                <div class="flex_row_start_center">
                                  <span class="input_left_side_tip">积分抽奖，单次抽奖消耗</span>
                                  <Form.Item
                                    :extra="it.extra"
                                    :name="it.name"
                                    :rules="it.integralUseType == 2 ? it.required : []"
                                  >
                                    <InputNumber
                                      v-model:value="detail[it.name]"
                                      :max="1000000"
                                      :min="1"
                                      :precision="0"
                                      style="width: 100px !important"
                                      :disabled="viewFlag || it.integralUseType == 1"
                                      placeholder="请输入"
                                    />
                                  </Form.Item>
                                  <span class="input_right_side_tip">分</span>
                                </div>
                              </Radio>
                            </div>
                          </div>
                        </RadioGroup>
                      </div>
                      <div
                        class="sld_det_r_item flex_column_center_start"
                        :style="{
                          width: '80%',
                          height: '76px',
                          borderTopWidth: '1px',
                          paddingLeft: '20px',
                        }"
                        v-if="it.type == 'Rule' && it.typeFlag"
                      >
                        <RadioGroup
                          :disabled="viewFlag"
                          size="small"
                          :value="it.integralUseType"
                          @change="radio_change($event, index, ind, it.name, it.name_ne)"
                        >
                          <div class="flex_row_start_center">
                            <Radio :value="1">
                              <div class="flex_row_start_center">
                                <span class="input_left_side_tip">每人每天可抽奖</span>
                                <Form.Item
                                  :extra="it.extra"
                                  :name="it.name"
                                  :rules="it.integralUseType == 1 ? it.required : []"
                                >
                                  <InputNumber
                                    v-model:value="detail[it.name]"
                                    :max="1000000"
                                    :min="1"
                                    :precision="0"
                                    style="width: 100px !important"
                                    :disabled="viewFlag || it.integralUseType == 2"
                                    placeholder="请输入"
                                  />
                                </Form.Item>
                                <span class="input_right_side_tip">次</span>
                              </div>
                            </Radio>
                            <div class="flex_row_start_center">
                              <Radio :value="2">
                                <div class="flex_row_start_center">
                                  <span class="input_left_side_tip">每人总共可抽奖</span>
                                  <Form.Item
                                    :extra="it.extra"
                                    :name="it.name_ne"
                                    :rules="it.integralUseType == 2 ? it.required : []"
                                  >
                                    <InputNumber
                                      v-model:value="detail[it.name_ne]"
                                      :max="1000000"
                                      :min="1"
                                      :precision="0"
                                      style="width: 100px !important"
                                      :disabled="viewFlag || it.integralUseType == 1"
                                      placeholder="请输入"
                                    />
                                  </Form.Item>
                                  <span class="input_right_side_tip">次</span>
                                </div>
                              </Radio>
                            </div>
                          </div>
                        </RadioGroup>
                      </div>
                      <div
                        class="sld_det_r_item flex_column_center_start"
                        :style="{
                          width: '80%',
                          height: it.height ? it.height : '76px',
                          borderTopWidth: '1px',
                          paddingLeft: '20px',
                        }"
                        v-if="it.type == 'uploadImg' && it.typeFlag"
                      >
                        <Form.Item :extra="it.extra" v-if="!viewFlag">
                          <Upload
                            :maxCount="1"
                            @click="beforeUploadClick"
                            :accept="it.accept ? it.accept : undefined"
                            :name="it.upload_name"
                            :action="`${apiUrl}${'/'}${it.upload_url}`"
                            listType="picture-card"
                            :file-list="it.fileList"
                            :beforeUpload="
                              (file) =>
                                beforeUpload(file, index, it.accept ? it.accept : '', it.limit)
                            "
                            @change="(e) => handleFileChange(e, index, ind, it.name)"
                            :headers="{
                              Authorization: imgToken,
                            }"
                          >
                            <div v-if="it.fileList.length < 1">
                              <PlusOutlined />
                              <div className="ant-upload-text">上传图片</div>
                            </div>
                          </Upload>
                        </Form.Item>
                        <div class="flex_com_row_center" v-else>
                          <div v-if="detail_info[it.name + 'Url']">
                            <Popover placement="rightTop">
                              <template #content>
                                <div
                                  style="width: 400px; height: 400px; margin: 0 5px"
                                  class="flex_com_row_center"
                                >
                                  <img
                                    :src="detail_info[it.name + 'Url']"
                                    alt=""
                                    style="max-width: 100%; max-height: 100%"
                                  />
                                </div>
                              </template>
                              <div
                                class="com_img_wrap flex_com_row_center"
                                style="width: 110px; height: 110px; margin: 0 5px"
                              >
                                <img :src="detail_info[it.name + 'Url']" alt="" />
                              </div>
                            </Popover>
                          </div>
                          <div v-else> -- </div>
                        </div>
                      </div>
                      <div
                        class="sld_det_r_item flex_column_center_start"
                        :style="{
                          width: '80%',
                          height: it.height ? it.height : '76px',
                          borderTopWidth: '1px',
                          paddingLeft: '20px',
                        }"
                        v-if="it.type == 'switch' && it.typeFlag"
                      >
                        <Form.Item :name="it.name" :extra="it.extra">
                          <Switch
                            :disabled="viewFlag"
                            v-model:checked="detail[it.name]"
                            :un-checked-value="
                              it.unCheckedValue != undefined ? it.unCheckedValue : false
                            "
                            :checked-value="it.checkedValue != undefined ? it.checkedValue : true"
                          />
                        </Form.Item>
                      </div>
                      <div
                        class="sld_det_r_item flex_column_center_start"
                        :style="{
                          width: '80%',
                          height: it.height ? it.height : '76px',
                          borderTopWidth: '1px',
                          paddingLeft: '20px',
                        }"
                        v-if="it.type == 'TextArea' && it.typeFlag"
                      >
                        <Form.Item :name="it.name" :extra="it.extra">
                          <Textarea
                            show-count
                            :maxlength="100"
                            style="width: 400px; min-height: 32px"
                            :rows="2"
                            v-model:value="detail[it.name]"
                            :placeholder="it.placeholder"
                            :disabled="viewFlag"
                          >
                          </Textarea>
                        </Form.Item>
                      </div>
                      <div
                        class="sld_det_r_item flex_column_center_start"
                        :style="{
                          width: '80%',
                          height: it.height ? it.height : '76px',
                          borderTopWidth: '1px',
                          paddingLeft: '20px',
                        }"
                        v-if="it.type == 'modalButton' && it.typeFlag"
                      >
                        <Form.Item :name="it.name">
                          <div class="flex_row_start_end">
                            <div class="rank_load_goods_btn" @click="modalModalVisible">
                              {{ it.buttonName }}
                            </div>
                            <span class="btn_right_side_tip">{{ it.extra }}</span>
                          </div>
                        </Form.Item>
                      </div>
                    </div>
                  </template>
                </div>
              </template>
              <div
                class="flex_row_start_start"
                style="margin-top: 20px"
                v-if="prizeData.length > 0"
              >
                <BasicTable
                  :columns="columns"
                  :dataSource="prizeData"
                  :actionColumn="
                    !viewFlag
                      ? {
                          title: '操作',
                          dataIndex: 'action',
                        }
                      : false
                  "
                  :canResize="false"
                  :maxHeight="300"
                  :pagination="false"
                  :bordered="true"
                >
                  <template #bodyCell="{ column, record, text }">
                    <template v-if="column.dataIndex === 'prizeImageUrl'">
                      <div class="business_load" v-if="text">
                        <Popover placement="rightTop">
                          <template #content>
                            <div
                              style="width: 100px; height: 100px; margin: 0 5px"
                              class="flex_com_row_center"
                            >
                              <img :src="text" alt="" style="max-width: 100%; max-height: 100%" />
                            </div>
                          </template>
                          <div class="business_load_img flex_com_row_center">
                            <img :src="text" alt="" />
                          </div>
                        </Popover>
                      </div>
                      <span v-else>--</span>
                    </template>
                    <template v-if="column.key === 'action'">
                      <TableAction
                        :actions="[
                          {
                            label: '编辑',
                            onClick: editPrize.bind(null, record.key),
                          },
                          {
                            label: '删除',
                            popConfirm: {
                              title: '删除后不可恢复，是否确定删除？',
                              placement: 'left',
                              confirm: operate.bind(null, record.key),
                            },
                          },
                        ]"
                      />
                    </template>
                  </template>
                </BasicTable>
              </div>
              <div style="width: 100%; height: 60px; background: #fff"></div>
            </div>
          </Form>
        </div>
        <div
          class="m_diy_bottom_wrap"
          :style="{ position: 'fixed', left: getRealWidth + 10 + 'px' }"
        >
          <div
            class="add_goods_bottom_btn"
            :class="{ add_goods_bottom_btn_rig: viewFlag }"
            @click="goBack"
          >
            返回
          </div>
          <div
            class="add_goods_bottom_btn add_goods_bottom_btn_sel"
            v-if="!viewFlag"
            @click="handleSaveAllData"
          >
            保存
          </div>
        </div>
      </Spin>
    </div>
    <AddDetailContent
      :drawType="drawType"
      @cancle-event="handleModalCancle"
      @confirm-event="handleModalConfirm"
      :modalVisible="modalVisible"
      :prizeData="prizeData"
      :editPrizeKey="editPrizeKey"
    ></AddDetailContent>
  </div>
</template>
<script setup>
  import { ref, onMounted } from 'vue';
  import { useRoute } from 'vue-router';
  import dayjs from 'dayjs';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useUserStore } from '/@/store/modules/user';
  import {
    Form,
    Spin,
    Input,
    RangePicker,
    RadioGroup,
    Radio,
    InputNumber,
    Upload,
    Switch,
    Textarea,
    Popover,
  } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import moment from 'moment';
  import { useGlobSetting } from '/@/hooks/setting';
  const userStore = useUserStore();
  import { useRouter } from 'vue-router';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import AddDetailContent from './add_detail_content.vue';
  import { getDrawAddApi, getDrawUpdateApi, getDrawDetailApi } from '/@/api/promotion/draw';
  import { getToken } from '/@/utils/auth';
  import { sucTip, failTip,pageClose } from '/@/utils/utils';
  import { useMultipleTabStore } from '/@/store/modules/multipleTab';

  const { getRealWidth } = useMenuSetting();
  const route = useRoute();
  const tabStore = useMultipleTabStore();
  const projectName = ref(import.meta.env.VITE_GLOB_APP_TITLE);

  const imgToken = ref('Bearer ' + getToken() || ''); //上传文件token参数

  const { apiUrl } = useGlobSetting();

  const router = useRouter();

  const drawType = ref(route.query?.drawType);

  const columns = ref([
    {
      title: `奖项名称`,
      dataIndex: 'prizeName',
      align: 'center',
      width: 100,
    },
    {
      title: `奖品图片`,
      dataIndex: 'prizeImageUrl',
      align: 'center',
      width: 100,
    },
    {
      title: `奖品类型`,
      dataIndex: 'prizeType',
      align: 'center',
      width: 100,
      customRender: ({ text, record }) => {
        let target = '';
        //奖品类型，1-积分，2-优惠券
        if (text == 1) {
          target = `积分`;
        } else {
          target = `优惠券`;
        }
        return target;
      },
    },
    {
      title: `奖品`,
      dataIndex: 'couponName',
      align: 'center',
      width: 100,
      customRender: ({ text, record }) => {
        let target = '';
        //奖品类型，1-积分，2-优惠券
        if (record.prizeType == 1) {
          target = `${record.integralNum}积分`;
        } else {
          target = `${record.couponName}【${record.couponContent}】`;
        }
        return target;
      },
    },
    {
      title: `奖品数量`,
      dataIndex: 'prizeNum',
      align: 'center',
      width: 100,
    },
    {
      title: `中奖率`,
      dataIndex: 'rate',
      align: 'center',
      width: 100,
      customRender: ({ text }) => {
        return text + '%';
      },
    },
  ]);

  const prizeData = ref([]);
  const undrawonImgW = ref(54); //未中奖图片的宽度
  const undrawonImgH = ref(54); //未中奖图片的高度
  const editPrizeKey = ref(0); //当前编辑的奖项key
  const modalVisible = ref(false); //添加奖项弹框是否显示

  const pageTitle = ref('新建活动');

  const formRef = ref();

  const viewFlag = ref(
    route.query?.id != undefined && Number(route.query?.id) > 0 && route.query?.type == 'view'
      ? true
      : false,
  ); //是否是查看

  const saveFlag = ref(1); //添加奖项弹框保存标识

  const detail = ref({
    drawName: '',
  });

  const draw_info_list = ref([
    {
      flag: true,
      title: '基本信息',
      children: [
        {
          type: 'input',
          typeFlag: true,
          title: '活动名称',
          name: 'drawName',
          extra: '最多输入6个字',
          placeholder: '请输入活动名称',
          maxlength: 6,
          flag: true,
          required: [{ required: true, message: '请输入活动名称' }],
        },
        {
          type: 'RangePicker',
          typeFlag: true,
          title: '活动时间',
          name: 'activityTime',
          placeholder: '请选择活动时间',
          placeholder1: '开始时间',
          placeholder2: '结束时间',
          flag: true,
          disabledDate: (currentDate) => currentDate && currentDate < moment().subtract(1, 'days'),
          show_time: { format: 'HH:mm' },
          required: [{ required: true, message: '请选择活动时间' }],
        },
        {
          type: 'RadioInput',
          typeFlag: true,
          title: '活动类型',
          name: 'integralUse',
          integralUseType: 1,
          flag: true,
          required: [{ required: true, message: '此处必填' }],
        },
        {
          type: 'Rule',
          typeFlag: true,
          title: '活动规则',
          name_ne: 'ruleNumTotal',
          name: 'ruleNumDay',
          integralUseType: 1,
          flag: true,
          required: [{ required: true, message: '此处必填' }],
        },
        {
          type: 'uploadImg',
          typeFlag: true,
          title: '活动背景图',
          extra: '建议上传【宽750*高1334】的图片，支持gif，jpeg，jpg，png格式的图片',
          name: 'backgroundImage',
          upload_name: 'file', //upload_name
          upload_url: `v3/oss/admin/upload?source=setting`, //接口
          fileList: [], //暂时不用
          accept: ' .jpg, .jpeg, .png', //文件格式
          limit: 10, //文件最大
          height: '150px',
        },
        {
          type: 'switch',
          typeFlag: true,
          title: `虚拟中奖`, //开关
          name: 'openVirtual',
          extra: `开启后，中奖名单将加入虚拟中奖数据`,
          initialValue: 1, //默认值
          unCheckedValue: 0, //非选中的值 不传返回值为false
          checkedValue: 1, //选中的值 不传返回值为true
        },
        {
          type: 'TextArea',
          title: `活动说明`,
          typeFlag: true,
          name: 'drawDescription',
          placeholder: '请输入活动说明',
          extra: `最多输入100字`,
          height: '90px',
        },
      ],
    },
    {
      flag: (drawType.value == '1' || drawType.value == '2' || drawType.value == '4') ? true : false,
      title:
        drawType.value == '1'
          ? '抽奖按钮图片'
          : drawType.value == '2'
          ? '抽奖指针图片'
          : drawType.value == '4'? '摇一摇效果图' :'',
      children: [
        {
          type: 'uploadImg',
          typeFlag: true,
          title: drawType.value == '4' ? '摇动结束时图片' : '正常可用时图片',
          extra:
            drawType.value == '1'
              ? '建议上传【宽283*高296】的图片，支持gif，jpeg，jpg，png格式的图片'
              : drawType.value == '2'
              ? '建议上传【宽169*高209】的图片，支持gif，jpeg，jpg，png格式的图片'
              : '建议上传【宽384*高283】的图片，支持gif，jpeg，jpg，png格式的图片',
          name: 'availableButtonImage',
          upload_name: 'file', //upload_name
          upload_url: `v3/oss/admin/upload?source=setting`, //接口
          fileList: [], //暂时不用
          accept: ' .gif, .jpeg, .png, .jpg,', //文件格式
          limit: 20, //文件最大
          height: '150px',
        },
        {
          type: 'uploadImg',
          typeFlag: drawType.value == '1' || drawType.value == '2' ? true : false,
          title: '机会用尽时图片',
          extra:
            drawType.value == '1'
              ? '建议上传【宽282*高300】的图片，支持gif，jpeg，jpg，png格式的图片'
              : '建议上传【宽169*高209】的图片，支持gif，jpeg，jpg，png格式的图片',
          name: 'chanceOutButtonImage',
          upload_name: 'file', //upload_name
          upload_url: `v3/oss/admin/upload?source=setting`, //接口
          fileList: [], //暂时不用
          accept: ' .gif, .jpeg, .png, .jpg,', //文件格式
          limit: 20, //文件最大
          height: '150px',
        },
      ],
    },
    {
      flag: drawType.value == '1' || drawType.value == '2' || drawType.value == '5' ? true : false,
      title: '未中奖信息',
      children: [
        {
          type: 'input',
          typeFlag: true,
          title: '未中奖名称',
          name: 'losePrizeDescription',
          extra: '最多输入6个字',
          placeholder: '请输入活动名称',
          maxlength: 6,
          flag: false,
        },
        {
          type: 'uploadImg',
          typeFlag: drawType.value == '1' || drawType.value == '2'|| drawType.value == '5' ? true : false,
          title: '未中奖图片',
          extra: `建议上传【宽${undrawonImgW.value}*高${undrawonImgH.value}】的图片，支持gif，jpeg，jpg，png格式的图片`,
          name: 'losePrizeImage',
          upload_name: 'file', //upload_name
          upload_url: `v3/oss/admin/upload?source=setting`, //接口
          fileList: [], //暂时不用
          accept: ' .gif, .jpeg, .png, .jpg,', //文件格式
          limit: 20, //文件最大
          height: '150px',
        },
      ],
    },
    {
      flag: true,
      title: '奖项设置',
      children: [
        {
          type: 'modalButton',
          typeFlag: !viewFlag.value ? true : false,
          title: '添加奖项',
          buttonName: '添加奖项',
          name: 'drawPrizeInfoList',
          extra: '最多添加6个',
          maxlength: 6,
          flag: true,
        },
      ],
    },
  ]);

  // 参数
  const type = ref(route.query?.type);

  // 详情数据
  const detail_info = ref({});

  // loading
  const loading = ref(true);

  const radio_change = (e, index, ind, name, name_ne) => {
    draw_info_list.value[index].children[ind].integralUseType = e.target.value;
    if (name_ne == 'ruleNumTotal' || name == 'ruleNumDay') {
      if (e.target.value == 1) {
        formRef.value.resetFields([name_ne, '']);
        detail.value[name_ne] = '';
      } else {
        formRef.value.resetFields([name, '']);
        detail.value[name] = '';
      }
    } else {
      if (e.target.value == 1) {
        formRef.value.resetFields([name, '']);
        draw_info_list.value[index].children[ind].required = [];
        detail.value[name] = '';
      } else {
        draw_info_list.value[index].children[ind].required = [
          { required: true, message: '此处必填' },
        ];
      }
    }
  };

  const beforeUploadClick = async()=> {
    imgToken.value = 'Bearer ' + userStore.getToken
    if(userStore.getToken){
      let res = await userStore.getSldCommonService()
      if(res.state == 200){
        imgToken.value = 'Bearer ' + res.token
      }
    }
  }

  //文件上传前处理数据
  function beforeUpload(file, index, accept, limit) {
    if (accept != undefined && accept != null && accept) {
      //校验文件格式类型
      let accept_list = accept
        .replaceAll(' ', '')
        .split(',')
        .filter((item) => item && item.trim());
      let file_type = file.name ? '.' + file.name.split('.').at(-1) : '';
      if (accept_list.length > 0 && file_type != '' && accept_list.indexOf(file_type) == -1) {
        failTip('上传文件格式有误');
        return false;
      }
    }
    let uploadLimit = limit ? limit : 20;
    if (file.size > 1024 * 1024 * uploadLimit) {
      failTip('上传文件过大，请上传小于' + uploadLimit + 'M的文件');
      return false;
    }
  }

  //数据变化事件
  function handleFileChange(e, index, ind, name) {
    if (e.file.status != undefined && e.file.status != 'error') {
      e.fileList.forEach((item) => {
        if (item.response && item.response.data) {
          item.url = item.response.data.url;
        }
      });
      draw_info_list.value[index].children[ind].fileList = e.fileList;
    }
  }

  const modalModalVisible = () => {
    let curTotalRate = 0;
    if (prizeData.value.length >= 6) {
      failTip(`最多添加6个奖项～`);
      return false;
    } else {
      prizeData.value.map((item) => {
        curTotalRate += item.rate * 1;
      });
      if (curTotalRate >= 100) {
        failTip(`中奖率已超100%，无法继续添加奖项～`);
        return false;
      }
    }
    modalVisible.value = true;
  };

  // 关闭弹窗
  const handleModalCancle = () => {
    modalVisible.value = false;
  };

  // 添加奖项弹框确认
  const handleModalConfirm = (e) => {
    if (editPrizeKey.value > 0) {
      for (let i in prizeData.value) {
        if (prizeData.value[i]['key'] == editPrizeKey.value) {
          prizeData.value[i] = { ...e, key: editPrizeKey.value };
          break;
        }
      }
    } else {
      prizeData.value.push({ ...e, key: prizeData.value.length + 1 });
    }
    modalVisible.value = false;
    editPrizeKey.value = 0;
  };

  // 弹框编辑
  const editPrize = (key) => {
    editPrizeKey.value = key;
    modalVisible.value = true;
  };
  // 删除
  const operate = (key) => {
    prizeData.value = prizeData.value.filter((item) => item.key != key);
  };

  // 点击保存
  const handleSaveAllData = async () => {
    formRef.value
      .validate()
      .then(async (value) => {
        value.drawType = drawType.value;
        //活动时间处理
        if (value.activityTime) {
          value.startTime = value.activityTime[0]
            ? value.activityTime[0].format('YYYY-MM-DD HH:mm:00')
            : '';
          value.endTime = value.activityTime[1]
            ? value.activityTime[1].format('YYYY-MM-DD HH:mm:00')
            : '';
          if (value.activityTime[0].unix() == value.activityTime[1].unix()) {
            failTip(`活动结束时间必须晚于开始时间～`);
            return false;
          }
          delete value.activityTime;
        }
        draw_info_list.value.forEach((item) => {
          if (item.flag) {
            item.children.forEach((it) => {
              if (it.typeFlag) {
                if (it.typeFlag && it.type == 'uploadImg') {
                  if (
                    it.fileList.length > 0 &&
                    it.fileList[0].response != undefined &&
                    it.fileList[0].response.data != undefined
                  ) {
                    value[it.name] = it.fileList[0].response.data.path;
                  } else {
                    value[it.name] = '';
                  }
                }
                if (it.name == 'integralUse') {
                  if (it.integralUseType == 1) {
                    value[it.name] = 0;
                  }
                }
                if (it.name == 'ruleNumDay') {
                  value.ruleType = it.integralUseType;
                  if (it.integralUseType == 1) {
                    value.ruleNum = value[it.name];
                  } else {
                    value.ruleNum = value[it.name_ne];
                  }
                }
              }
            });
          }
        });
        delete value.ruleNumDay;
        delete value.ruleNumTotal;
        value.drawPrizeInfoList = [];
        if (prizeData.value.length > 0) {
          prizeData.value.map((item) => {
            let temp = {};
            temp.prizeType = item.prizeType;
            //奖品类型，1-积分，2-优惠券
            if (temp.prizeType == 1) {
              temp.integralNum = item.integralNum;
            } else {
              temp.couponId = item.couponId;
            }
            temp.prizeImage = item.prizeImage;
            temp.prizeName = item.prizeName;
            temp.prizeNum = item.prizeNum;
            temp.rate = item.rate;
            value.drawPrizeInfoList.push(temp);
          });
        } else {
          failTip(`请设置奖品～`);
          return;
        }
        let res;
        loading.value = true;
        if (
          route.query?.id != undefined &&
          Number(route.query?.id) > 0 &&
          route.query?.type == 'edit'
        ) {
          //编辑活动
          value.drawId = Number(route.query?.id);
          res = await getDrawUpdateApi(value);
        } else {
          //新建活动
          res = await getDrawAddApi(value);
        }
        if (res.state == 200) {
          sucTip(res.msg);
          // //抽奖活动类型，1-幸运抽奖，2-大转盘，3-刮刮卡，4-摇一摇，5-翻翻看
          let name = 'DrawLuckyList'
          if (route.query?.drawType == '1') {
            name = 'DrawLuckyList'
          } else if (route.query?.drawType == '2') {
            name = 'DrawTurnPlateList'
          } else if (route.query?.drawType == '3') {
            name = 'DrawScratchList'
          } else if (route.query?.drawType == '4') {
            name = 'DrawShakeList'
          } else if (route.query?.drawType == '5') {
            name = 'DrawTurnList'
          }
          userStore.setDelKeepAlive([route.name,name])
          setTimeout(() => {
            loading.value = false;
            goBack();
          }, 500);
        } else {
          loading.value = false;
          failTip(res.msg);
        }
      })
      .catch((err) => {
        console.log('error', err);
      });
  };

  const goBack = () => {
    if (window.history && window.history.length == 1) {
      pageClose();
    } else {
      const { fullPath } = route;
      tabStore.closeTabByKey(fullPath, router);
      router.back();
    }
  };

  // 获取活动详情
  const getDetail = async () => {
    loading.value = true;
    let res = await getDrawDetailApi({ drawId: route.query?.id });
    if (res.state == 200) {
      let data = res.data;
      draw_info_list.value.forEach((item) => {
        if (item.flag) {
          item.children.forEach((it) => {
            if (it.typeFlag) {
              if (it.type == 'uploadImg') {
                //初始化图片数据-start
                if (data[it.name]) {
                  let tmp_data = {};
                  tmp_data.uid = data[it.name];
                  tmp_data.name = data[it.name];
                  tmp_data.status = 'done';
                  tmp_data.url = data[it.name + 'Url'];
                  tmp_data.response = {
                    data: {
                      url: data[it.name + 'Url'],
                      path: data[it.name],
                    },
                  };
                  it.fileList = [];
                  it.fileList.push(tmp_data);
                }
                //初始化图片数据-end
              } else if (it.name == 'integralUse') {
                it.integralUseType = data.integralUse ? 2 : 1; //活动类型，1为无限制，2为积分抽奖
                if (data.integralUse) {
                  detail.value[it.name] = data[it.name];
                }
              } else if (it.name == 'ruleNumDay') {
                if (data.ruleType == 1) {
                  //活动规则，1为每人每天可抽奖次数，2为每人总共可抽奖次数
                  detail.value[it.name] = data.ruleNum;
                } else {
                  detail.value[it.name_ne] = data.ruleNum;
                }
              } else if (it.type == 'RangePicker') {
                detail.value[it.name] = [
                  dayjs(data.startTime, 'YYYY-MM-DD HH:mm:ss'),
                  dayjs(data.endTime, 'YYYY-MM-DD HH:mm:ss'),
                ];
              } else {
                detail.value[it.name] = data[it.name];
              }
            }
          });
        }
      });
      //奖项数据
      let key = 1;
      data.drawPrizeVOList.forEach((item) => {
        item.key = key;
        key++;
        prizeData.value.push(item);
      });
      if (viewFlag) {
        //查看
        columns.value = columns.value.filter((item) => item.dataIndex != 'operation');
      }
      if (route.query?.type != undefined && route.query?.type == 'copy') {
        //复制的情况，需要清空活动名称和活动时间
        detail.value.drawName = '';
        detail.value.activityTime = '';
      }
      detail_info.value = data;
      loading.value = false;
    }
  };

  onMounted(() => {
    if (route.query?.type != undefined) {
      if (route.query?.type == 'edit') {
        pageTitle.value = '编辑活动';
      } else if (route.query?.type == 'view') {
        pageTitle.value = '查看活动';
      } else {
        pageTitle.value = '复制活动';
      }
    }
    if (route.query?.id != undefined && Number(route.query?.id) > 0) {
      getDetail();
    } else {
      loading.value = false;
    }
    //抽奖活动类型，1-幸运抽奖，2-大转盘，3-刮刮卡，4-摇一摇，5-翻翻看
    if (route.query?.drawType == '1') {
      undrawonImgW.value = 54;
      undrawonImgH.value = 54;
    } else if (route.query?.drawType == '2') {
      undrawonImgW.value = 62;
      undrawonImgH.value = 62;
    } else if (route.query?.drawType == '5') {
      undrawonImgW.value = 66;
      undrawonImgH.value = 66;
    }
    document.title = pageTitle.value + ' - ' + projectName.value;
    draw_info_list.value.forEach((item) => {
      if (item.flag) {
        item.children.forEach((it) => {
          if (it.type == 'switch') {
            detail.value[it.name] = it.initialValue;
          } else {
            detail.value[it.name] = '';
          }
        });
      }
    });
  });
</script>
<style lang="less">
  @import '/@/assets/css/add_rank.less';
  @import './style/add.less';

  textarea.ant-input {
    resize: none;
  }

  .com_img_wrap img {
    max-width: 100%;
    max-height: 100%;
  }
</style>
