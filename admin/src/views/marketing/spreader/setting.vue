<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader :type="2" :title="'推手设置'" />
      <Tabs v-model:activeKey="tabIndex" type="card" @change="handleTab">
        <TabPane key="1" tab="基本设置" />
        <TabPane key="2" tab="审核设置" />
      </Tabs>
      <div class="section_padding_tab_top"></div>
      <template v-if="tabIndex == '1'">
        <div class="section_padding_set_scroll">
          <StandardTableRow
            width="100%"
            :data="basicSettings"
            @callback-event="callbackEvent"
            @submit-event="submitEvent"
          />

        </div>
      </template>
      <template v-else>
        <div class="section_padding_set_scroll">
          <StandardTableRow
            width="100%"
            :data="auditSettings"
            @callback-event="callbackEvent"
            @submit-event="submitEvent"
          />
        </div>
      </template>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'MarketingSpreader',
  };
</script>
<script setup>
  import { ref, onMounted } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import StandardTableRow from '/@/components/StandardTableRow/index.vue';
  import { Tabs } from 'ant-design-vue';
  import { getSettingList, updateSettingList } from '/@/api/spreader/spreader';
  import { useGlobSetting } from '/@/hooks/setting';
  import { mobile_reg } from '/@/utils/validate';
  import { failTip, sucTip } from '/@/utils/utils';

  const { apiUrl } = useGlobSetting();
  const tabIndex = ref('1'); //tab下标
  const basicSettings = ref([]); //基本设置配置
  const auditSettingsDoorType = ref([]); //基本设置配置
  const auditSettings = ref([]); //审核设置配置
  const auditSettingsCommen = ref([]); //审核设置公共配置
  const spreader_door_type1 = ref([]); //消费加入推手数据
  const spreader_door_type2 = ref([]); //购买推手礼包
  const settingRes = ref([]);
  const detail_one = ref({});
  const detail_two = ref({});
  const spreader_door_type_text = ref('');

  const handleTab = (e) => {
    getSettingInfo();
  };

  const callbackEvent = (e) => {
    const key = e.contentItem.key;
    // 解决输入框为input时输入框更改之后会变回之前的值问题
    if (e.contentItem.type == 'input'&&tabIndex.value == '1') {
      basicSettings.value.map((items, index) => {
        if (items.key == e.contentItem.key) {
          basicSettings.value[index].value = e.val1;
        }
      });
    }
    // 解决输入框为input时输入框更改之后会变回之前的值问题 审核设置
    if ((key=='spreader_gift_bag_name'||key=='spreader_gift_bag_amount')&&tabIndex.value == '2') {
      auditSettings.value.map((items, index) => {
        if (items.key == e.contentItem.key) {
          auditSettings.value[index].value = e.val1;
        }
      });
    }
    if (e.contentItem.eventType == 'change' && key == 'spreader_share_image') {
      let temp = basicSettings.value.filter((items) => items.key == key);
      if (temp.length > 0) {
        temp[0].fileList = e.val1.fileList;
        if (e.val1.fileList && e.val1.fileList[0].response && e.val1.fileList[0].response.data) {
          temp[0].fileList[0].path = e.val1.fileList[0].response.data.path;
          temp[0].fileList[0].url = e.val1.fileList[0].response.data.url;
        }
      }
    }
    if (e.contentItem.eventType == 'change' && key == 'spreader_gift_bag_image') {
      let temp = auditSettings.value.filter((items) => items.key == key);
      if (temp.length > 0) {
        temp[0].fileList = e.val1.fileList;
        if (e.val1.fileList && e.val1.fileList[0].response && e.val1.fileList[0].response.data) {
          temp[0].fileList[0].path = e.val1.fileList[0].response.data.path;
          temp[0].fileList[0].url = e.val1.fileList[0].response.data.url;
        }
      }
    }
    if (key == 'spreader_second_commission_is_enable' && e.val1.length > 0 && e.val1[0] == '1') {
      basicSettings.value[6] = {
        require: true,
        type: 'inputnum',
        label: '二级分佣比例',
        key: settingRes.value[6].name,
        width: 300,
        desc_width: 300,
        min: 0,
        max: 49,
        placeholder: '请输入二级分佣比例',
        value: settingRes.value[6].value,
        callback: true,
        desc: settingRes.value[6].desc
          ? settingRes.value[6].desc
          : '请输入二级分佣比例，如：30 表示总佣金的30%',
      };
    } else if (key == 'spreader_second_commission_is_enable') {
      basicSettings.value[6] = {};
    }
    let btn = [
      {
        type: 'button',
        width: 300,
        showSubmit: true,
        submitText: '保存',
        showCancle: false,
      },
    ];
    if (key == 'spreader_door_is_enable') {
      if (e.val1.length == 1) {
        if (spreader_door_type_text.value == '1') {
          auditSettings.value = [
            ...auditSettingsCommen.value,
            ...auditSettingsDoorType.value,
            ...spreader_door_type1.value,
            ...btn,
          ];
        } else {
          auditSettings.value = [
            ...auditSettingsCommen.value,
            ...auditSettingsDoorType.value,
            ...spreader_door_type2.value,
            ...btn,
          ];
        }
      } else {
        auditSettings.value = [...auditSettingsCommen.value, ...btn];
      }
    }
    if (key == 'spreader_door_type') {
      spreader_door_type_text.value = e.val1;
      if (e.val1 == '1') {
        auditSettings.value = [
          ...auditSettingsCommen.value,
          ...auditSettingsDoorType.value,
          ...spreader_door_type1.value,
          ...btn,
        ];
      } else {
        auditSettings.value = [
          ...auditSettingsCommen.value,
          ...auditSettingsDoorType.value,
          ...spreader_door_type2.value,
          ...btn,
        ];
      }
    }
    if (key == 'consumeType') {
      if (e.val1 == '1') {
        spreader_door_type1.value[1].key = 'spreader_door_min_amount';
        spreader_door_type1.value[1].value = detail_two.value.spreader_door_min_amount;
        spreader_door_type1.value[1].placeholder = '请输入金额';
        spreader_door_type1.value[1].input_before_text = '自购满';
        spreader_door_type1.value[1].input_after_text = '元';
        spreader_door_type1.value[1].desc = '购买商城商品达到该金额，输入金额必须大于0';
        spreader_door_type1.value[1].rules = [{ required: true, message: '请输入金额' }];
      }
      if (e.val1 == '2') {
        spreader_door_type1.value[1].key = 'spreader_door_min_num';
        spreader_door_type1.value[1].value = detail_two.value.spreader_door_min_num;
        spreader_door_type1.value[1].placeholder = '请输入次数';
        spreader_door_type1.value[1].input_before_text = '消费满';
        spreader_door_type1.value[1].input_after_text = '次';
        spreader_door_type1.value[1].desc = '购买商城商品达到该次数，输入次数必须大于0';
        spreader_door_type1.value[1].rules = [{ required: true, message: '请输入次数' }];
      }
    }
  };

  const submitEvent = async (params) => {
    if (tabIndex.value == '1') {
      params.spreader_share_image = params.spreader_share_image.path;
      params.spreader_is_enable =
        params.spreader_is_enable[0] && params.spreader_is_enable[0] == '1' ? '1' : '0';
      params.spreader_second_commission_is_enable =
        params.spreader_second_commission_is_enable[0] &&
        params.spreader_second_commission_is_enable[0] == '1'
          ? '1'
          : '0';
      if (params.spreader_share_image == undefined) {
        failTip('请上传推广海报图片');
        return;
      }
    } else {
      if (params.spreader_door_is_enable.length > 0) {
        if (Array.isArray(params.spreader_door_is_enable)) {
          params.spreader_door_is_enable = '1';
        } else {
          params.spreader_door_is_enable = params.spreader_door_is_enable;
        }
      } else {
        params.spreader_door_is_enable = '0';
      }
      if (params.spreader_door_is_enable == '1') {
        if (params.consumeType == '2') {
          params.spreader_door_min_amount = '';
          if ((!params.spreader_door_min_num||!params.spreader_door_min_num)&&params.spreader_door_type == '1') {
            failTip('请输入消费满次数');
            return;
          }
        } else {
          params.spreader_door_min_num = '';
          if ((!params.spreader_door_min_amount||params.spreader_door_min_amount==0)&&params.spreader_door_type == '1') {
            failTip('请输入自购满金额');
            return;
          }
        }
        delete params.consumeType;
        if (params.spreader_door_type == '1') {
          params.spreader_gift_bag_image = '';
        } else {
          params.spreader_gift_bag_image = params.spreader_gift_bag_image.path;
          params.spreader_door_min_num = '';
          params.spreader_door_min_amount = '';
        }
      }
      if (params.spreader_gift_bag_image == undefined && params.spreader_door_type == '2') {
        failTip('请上传礼包图片~');
        return;
      }
    }
    let res = await updateSettingList(params);
    if (res.state == 200) {
      sucTip(res.msg);
    } else {
      getSettingInfo();
      failTip(res.msg);
    }
  };

  // 获取配置信息
  const getSettingInfo = async () => {
    let params = {};
    if (tabIndex.value == '1') {
      params = {
        str: 'spreader_is_enable,spreader_service_phone,spreader_new_user_commission,spreader_share_image,spreader_effective_day,spreader_relation_bind_time,spreader_second_commission_is_enable,spreader_commission_second_ratio',
      };
    } else {
      params = {
        str: 'spreader_door_is_enable,spreader_door_type,spreader_door_min_amount,spreader_door_min_num,spreader_gift_bag_name,spreader_gift_bag_amount,spreader_gift_bag_description,spreader_gift_bag_image',
      };
    }
    const res = await getSettingList(params);
    settingRes.value = res.data;
    const resData = res.data;
    if (tabIndex.value == '1') {
      detail_one.value = [];
      resData.forEach((item) => {
        detail_one.value[item.name] = item.value;
        if (item.name == 'spreader_share_image') {
          detail_one.value[item.name + '_url'] = item.imageUrl;
        }
      });
      basicSettings.value = [
        {
          type: 'checkbox',
          label: '商城推手',
          key: 'spreader_is_enable',
          width: 300,
          initValue: '',
          value:
            detail_one.value.spreader_is_enable == '0' ? '' : detail_one.value.spreader_is_enable,
          callback: true,
          data: [
            {
              key: 'spreader_is_enable',
              value:'1',
              title: '开启',
            },
          ],
        },
        {
          require: true,
          type: 'input',
          label: '平台电话',
          key: 'spreader_service_phone',
          width: 300,
          desc_width: 300,
          maxlength: 11,
          placeholder: '请输入平台电话',
          value: detail_one.value.spreader_service_phone,
          callback: true,
          desc: '请输入手机号或者固话，用于移动端个人中心联系客服',
          rules: [
            {
              required: true,
              message: '请输入平台电话',
            },
            { pattern: mobile_reg, message: '请输入正确的电话' },
          ],
        },
        {
          require: true,
          type: 'inputnum',
          label: '邀请新用户注册佣金',
          key: 'spreader_new_user_commission',
          width: 300,
          desc_width: 300,
          min: 0,
          max: 9999,
          precision: 2,
          placeholder: '请输入金额',
          value: detail_one.value.spreader_new_user_commission,
          callback: true,
          desc: '会员邀请新用户注册奖励佣金，最大输入9999.00',
          input_after_text: '元',
          rules: [
            {
              required: true,
              message: '请输入金额',
            },
          ],
        },
        {
          width: 300,
          type: 'upload_img',
          require: true,
          label: '推广海报设置',
          key: 'spreader_share_image',
          desc: '展示在移动端 -> 推手中心 -> 我要推广页面的分享海报上，建议上传最大宽500*最大高500的图片',
          accept: '.jpg, .jpeg, .png, .gif',
          action: `${apiUrl}/v3/oss/admin/upload?source=setting`,
          fileList: [
            {
              uid: 'base_img',
              name: 'base_img',
              status: 'done',
              path: detail_one.value.spreader_share_image,
              url: detail_one.value.spreader_share_image_url,
            },
          ],
          desc_width: 350,
          callback: true,
        },
        {
          require: true,
          type: 'inputnum',
          label: '分享有效时间',
          key: 'spreader_effective_day',
          width: 300,
          desc_width: 300,
          min: 1,
          max: 10,
          placeholder: '请输入分享有效时间',
          value: detail_one.value.spreader_effective_day,
          callback: true,
          desc: '超过该时间，分享者无法得到佣金',
          rules: [
            {
              required: true,
              message: '请输入分享有效时间',
            },
          ],
        },
        {
          type: 'checkbox',
          label: '开启二级分佣',
          key: 'spreader_second_commission_is_enable',
          width: 300,
          initValue: '',
          value:
            detail_one.value.spreader_second_commission_is_enable == '0'
              ? ''
              : detail_one.value.spreader_second_commission_is_enable,
          callback: true,
          data: [
            {
              key: 'spreader_second_commission_is_enable',
              value:
                detail_one.value.spreader_second_commission_is_enable == '0'
                  ? ''
                  : detail_one.value.spreader_second_commission_is_enable,
              title: '开启',
            },
          ],
          desc: '各级分佣比例之和为100%',
        },
        {
          type: 'button',
          width: 300,
          showSubmit: true,
          submitText: '保存',
          showCancle: false,
        },
      ];
      if (detail_one.value.spreader_second_commission_is_enable == '1') {
        basicSettings.value.splice(6, 0, {
          require: true,
          type: 'inputnum',
          label: '二级分佣比例',
          key: 'spreader_commission_second_ratio',
          width: 300,
          desc_width: 300,
          min: 0,
          max: 49,
          placeholder: '请输入二级分佣比例',
          value: detail_one.value.spreader_commission_second_ratio,
          callback: true,
          desc: '请输入二级分佣比例，如：30 表示总佣金的30%',
          rules: [
            {
              required: true,
              message: '请输入二级分佣比例',
            },
          ],
        });
      }
    } else {
      detail_two.value = [];
      resData.forEach((item) => {
        detail_two.value[item.name] = item.value;
        detail_two.value[item.name + '_url'] = item.imageUrl;
      });
      spreader_door_type_text.value = detail_two.value.spreader_door_type;
      auditSettingsCommen.value = [
        {
          type: 'checkbox',
          label: '推手门槛',
          key: 'spreader_door_is_enable',
          width: 300,
          initValue: '',
          value: detail_two.value.spreader_door_is_enable,
          callback: true,
          desc: '开启后，成为推手需要达到设置的条件',
          data: [
            {
              key: 'spreader_door_is_enable',
              value: '1',
              title: '开启',
            },
          ],
        },
      ];
      auditSettingsDoorType.value = [
        {
          require: true,
          type: 'radio',
          label: '门槛类型',
          key: 'spreader_door_type',
          width: 300,
          initValue: '',
          value: detail_two.value.spreader_door_type,
          callback: true,
          data: [
            {
              key: 'spreader_door_is_enable' + '1',
              value: '1',
              title: '消费加入推手',
            },
            {
              key: 'spreader_door_is_enable' + '2',
              value: '2',
              title: '购买推手礼包',
            },
          ],
        },
      ];
      spreader_door_type1.value = [
        {
          type: 'radio',
          label: '',
          key: 'consumeType',
          width: 300,
          initValue: '',
          value: detail_two.value.spreader_door_min_amount ? '1' : '2',
          callback: true,
          data: [
            {
              key: '1',
              value: '1',
              title: '自购满金额',
            },
            {
              key: '2',
              value: '2',
              title: '消费满次数',
            },
          ],
        },
        {
          require: true,
          type: 'inputnum',
          label: '',
          key: detail_two.value.spreader_door_min_amount
            ? 'spreader_door_min_amount'
            : 'spreader_door_min_num',
          width: 300,
          desc_width: 300,
          min: 1,
          max: 99999999,
          isNotShow: true,
          placeholder: '请输入金额',
          value: detail_two.value.spreader_door_min_amount
            ? Number(detail_two.value.spreader_door_min_amount)
            : Number(detail_two.value.spreader_door_min_num),
          callback: true,
          input_before_text: '自购满',
          input_after_text: '元',
          desc: '购买商城商品达到该金额，输入金额必须大于0',
          rules: [
            {
              required: true,
              message: '请输入金额',
            },
          ],
        },
      ];
      if (!detail_two.value.spreader_door_min_amount) {
        spreader_door_type1.value[1].placeholder = '请输入次数';
        spreader_door_type1.value[1].input_before_text = '消费满';
        spreader_door_type1.value[1].input_after_text = '次';
        spreader_door_type1.value[1].desc = '购买商城商品达到该次数，输入次数必须大于0';
        spreader_door_type1.value[1].rules = [{ required: true, message: '请输入次数' }];
      }
      spreader_door_type2.value = [
        {
          require: true,
          type: 'input',
          label: '礼包名称',
          key: 'spreader_gift_bag_name',
          width: 300,
          desc_width: 300,
          maxLength: 15,
          placeholder: '请输入' + '礼包名称',
          value: detail_two.value.spreader_gift_bag_name,
          callback: true,
          rules: [
            {
              required: true,
              message: '请输入' + '礼包名称',
            },
          ],
        },
        {
          require: true,
          type: 'inputnum',
          label: '礼包金额',
          key: 'spreader_gift_bag_amount',
          width: 300,
          desc_width: 300,
          min: 0,
          max: 9999999,
          placeholder: '请输入' + '礼包金额',
          value: detail_two.value.spreader_gift_bag_amount,
          callback: true,
          rules: [
            {
              required: true,
              message: '请输入' + '礼包金额',
            },
          ],
        },
        {
          require: true,
          type: 'textarea',
          label: '礼包描述',
          key: 'spreader_gift_bag_description',
          width: 300,
          desc_width: 300,
          placeholder: '请输入礼包描述',
          value: detail_two.value.spreader_gift_bag_description,
          callback: true,
          rules: [
            {
              required: true,
              message: '请输入礼包描述',
            },
          ],
        },
        {
          width: 300,
          type: 'upload_img',
          require: true,
          label: '礼包图片',
          key: 'spreader_gift_bag_image',
          accept: '.jpg, .jpeg, .png, .gif',
          action: `${apiUrl}/v3/oss/admin/upload?source=setting`,
          fileList: [],
          desc_width: 350,
          callback: true,
        },
      ];
      if (detail_two.value.spreader_gift_bag_image_url) {
        spreader_door_type2.value[3].fileList = [
          {
            uid: 'spreader_gift_bag_image',
            name: 'spreader_gift_bag_image',
            status: 'done',
            path: detail_two.value.spreader_gift_bag_image,
            url: detail_two.value.spreader_gift_bag_image_url,
          },
        ];
      }
      let btn = [
        {
          type: 'button',
          width: 300,
          showSubmit: true,
          submitText: '保存',
          showCancle: false,
        },
      ];
      auditSettings.value =
        resData[0].value == '0'
          ? [...auditSettingsCommen.value, ...btn]
          : resData[1].value == '1'
          ? [
              ...auditSettingsCommen.value,
              ...auditSettingsDoorType.value,
              ...spreader_door_type1.value,
              ...btn,
            ]
          : [
              ...auditSettingsCommen.value,
              ...auditSettingsDoorType.value,
              ...spreader_door_type2.value,
              ...btn,
            ];
    }
  };

  onMounted(() => {
    getSettingInfo();
  });
</script>
<style lang="less" scoped></style>
