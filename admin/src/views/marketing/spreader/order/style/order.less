.order_list {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;

  .pagination {
    width: 100%;
    margin-top: 15px;
    text-align: right;
  }

  .header {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    height: 40px;
    margin-bottom: 10px;
    border-radius: 3px;
    background: #f5f5f5;

    li {
      color: #333;
      font-size: 14px;
    }
  }

  .order_content {
    width: 100%;

    .item:first-child {
      margin-top: 0;
    }

    .item {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: flex-start;
      width: 100%;
      margin-top: 10px;
      border-width: 0 1px 1px;
      border-style: solid;
      border-color: #ddd;

      .order_info {
        width: 100%;
        height: 35px;
        padding: 0 13px;
        background: #fff1e8;

        .left {
          margin-top: 1px;

          .num {
            margin-right: 20px;
            color: #999;
            font-size: 12px;
          }

          .order_sn {
            color: #666;
            font-size: 12px;
          }

          .order_type {
            position: relative;
            margin-left: 15px;

            .order_type_icon {
              height: 20px;
            }

            .order_type_text {
              position: absolute;
              top: 0;
              left: 4px;
              color: #fff;
              font-size: 12px;
              line-height: 19px;
            }
          }
        }

        .create_time {
          color: #999;
          font-size: 12px;
        }
      }

      .order_goods_part {
        width: 100%;

        .goods_split {
          position: relative;
        }

        .goods_split::after {
          content: '';
          position: absolute;
          z-index: 3;
          top: 0;
          right: 0;
          bottom: 0;
          transform: scaleY(0.85);
          border-right: 1px solid rgb(0 0 0 / 5%);
        }

        .goods {
          .goods_item {
            .goods_img_wrap {
              flex-shrink: 0;
              width: 82px;
              height: 82px;
              margin: 15px 10px 15px 15px;
              border: 1px solid rgb(0 0 0 / 5%);
              background: #fff;

              img {
                max-width: 100%;
                max-height: 100%;
              }
            }

            .goods_info {
              .goods_name {
                display: -webkit-box;
                height: 40px;
                overflow: hidden;
                color: #333;
                font-size: 14px;
                line-height: 20px;
                text-overflow: ellipsis;
                word-break: break-word;
                -webkit-line-clamp: 2;

                /*! autoprefixer: off */
                -webkit-box-orient: vertical;
              }

              .goods_spec {
                display: -webkit-box;
                margin-top: 12px;
                overflow: hidden;
                color: #666;
                font-size: 12px;
                line-height: 14px;
                text-overflow: ellipsis;
                white-space: nowrap;
                -webkit-line-clamp: 1;

                /*! autoprefixer: off */
                -webkit-box-orient: vertical;
              }
            }

            .goods_price {
              color: #333;
              font-size: 12px;
            }

            .buy_num {
              color: #333;
              font-size: 12px;
            }
          }
        }

        .member_info {
          .mem_name {
            padding: 0 5px;
            color: #333;
            font-size: 12px;
            word-break: break-word;
          }

          .mem_tel,
          .mem_email {
            padding: 0 5px;
            color: #666;
            font-size: 12px;
            word-break: break-word;
          }
        }

        .order_state {
          color: #666;
          font-size: 12px;
        }

        .pay_amount {
          color: #ff1818;
          font-size: 12px;
          font-weight: bold;
        }
      }
    }
  }

  .center {
    text-align: center;
  }

  .width_10 {
    width: 10%;
    padding: 5px;
  }

  .width_40 {
    width: 40%;
    padding: 5px;
  }

  .width_30 {
    width: 30%;
    padding: 5px;
  }

  .width_50 {
    width: 50%;
    padding: 5px;
  }

  .width_60 {
    width: 60%;
    padding: 5px;
  }

  .pl_100 {
    padding-left: 100px;
  }
}

/* 退货详情样式-start */
.progress {
  margin-top: 50px;

  .item {
    .top {
      position: relative;
      width: 215px;

      img {
        width: 75px;
        height: 75px;
      }

      .left_line,
      .right_line {
        content: '';
        position: absolute;
        top: 50%;
        width: 50px;
        height: 0;
        border-top: 1px solid #007fff;
      }

      .center_line {
        width: 70px;
        height: 70px;
        border-radius: 35px;
      }

      .left_line {
        left: 0;
      }

      .right_line {
        right: 0;
      }
    }

    .state {
      margin-top: 10px;
      font-size: 14px;
    }

    .time {
      font-size: 14px;
    }
  }

  .item.cur {
    .state {
      color: #007fff;
    }

    .time {
      color: rgb(0 127 255 / 50%);
    }

    .left_line,
    .right_line {
      border-color: #007fff;
    }
  }

  .item.no {
    .state {
      color: #999;
    }

    .left_line,
    .right_line {
      border-color: #eee;
    }
  }

  .item.pass {
    .state {
      color: rgb(0 127 255 / 50%);
    }

    .time {
      color: rgb(0 127 255 / 30%);
    }

    .left_line,
    .right_line {
      border-color: rgb(0 127 255 / 30%);
    }
  }
}

.state_part {
  margin-top: 50px;
  margin-bottom: 40px;

  .title {
    color: #333;
    font-size: 26px;
  }

  .tip {
    color: #999;
    font-size: 14px;
  }
}

.btnsty {
  display: flex;
  flex-direction: row;
  justify-content: space-around;

  .agree_btn {
    height: 36px;
    margin-top: 15px;
    margin-right: 20px;
    padding: 0 10px;
    border-radius: 3px;
    background: #007fff;
    color: #fff;
    font-size: 14px;
    line-height: 36px;
    text-align: center;
  }

  .refuse_btn {
    height: 36px;
    margin-top: 15px;
    padding: 0 10px;
    border: 1px solid rgb(0 127 255 / 100%);
    border-radius: 3px;
    color: #007fff;
    font-size: 14px;
    line-height: 36px;
    text-align: center;
  }

  .agree_btnnon {
    height: 36px;
    margin-top: 15px;
    margin-right: 20px;
    padding: 0 10px;
    border: 1px solid #ddd;
    border-radius: 3px;
    color: #bbb;
    font-size: 14px;
    line-height: 36px;
    text-align: center;
  }

  .lock_agree_btn {
    width: 120px;
    height: 36px;
    margin-top: 15px;
    margin-right: 20px;
    padding: 0 10px;
    border: 1px solid #ddd;
    border-radius: 3px;
    color: #bbb;
    font-size: 14px;
    line-height: 36px;
    text-align: center;
  }

  .lock_refuse_btn {
    width: 120px;
    height: 36px;
    margin-top: 15px;
    padding: 0 10px;
    border-radius: 3px;
    background: #ddd;
    color: #fff;
    font-size: 14px;
    line-height: 36px;
    text-align: center;
  }

  .cancle_btn {
    width: 120px;
    height: 36px;
    margin-top: 15px;
    margin-right: 20px;
    padding: 0 10px;
    border: 1px solid #ff711e;
    border-radius: 3px;
    color: #ff711e;
    font-size: 14px;
    line-height: 36px;
    text-align: center;
    cursor: pointer;
  }

  .deliver_btn {
    width: 120px;
    height: 36px;
    margin-top: 15px;
    padding: 0 10px;
    border-radius: 3px;
    background: #007fff;
    color: #fff;
    font-size: 14px;
    line-height: 36px;
    text-align: center;
  }
}

/* 退货详情样式-end */

.goods_info1 {
  .goods_detail {
    flex: 1;
    height: 80px;
    margin-left: 10px;
  }

  .goods_img {
    display: inline-block;
    width: 80px;
    height: 80px;
    overflow: hidden;
    border: 1px solid rgb(226 229 246 / 100%);
    border-radius: 3px;
    background: rgb(248 248 248 / 100%);
  }

  .goods_name {
    display: -webkit-box;
    height: 34px;
    margin-top: 10px;
    overflow: hidden;
    color: #333;
    font-size: 14px;
    line-height: 17px;
    text-align: left;
    text-overflow: ellipsis;
    word-break: break-word;
    -webkit-line-clamp: 2;

    /*! autoprefixer: off */
    -webkit-box-orient: vertical;
  }

  .goods_brief {
    margin-bottom: 11px;
    overflow: hidden;
    color: #666;
    font-size: 12px;
    text-overflow: ellipsis;
    word-break: keep-all;
  }
}

.operaBtn {
  width: 110px;
  height: 25px;
  margin-bottom: 5px;
  border: 1px solid #fc701e;
  border-radius: 5px;
  color: #fc701e;
  line-height: 25px;
  text-align: center;
}

.order_goods_part {
  width: 100%;

  .operate {
    .operate_btn {
      width: 85px;
      height: 26px;
      margin-top: 8px;
      border: 1px solid rgb(0 0 0 / 20%);
      border-radius: 3px;
      color: #666;
      font-size: 12px;
      line-height: 26px;
      text-align: center;
    }

    .operate_btn:first-child {
      margin-top: 0;
    }

    .operate_btn:hover,
    .operate_btn:active {
      border-color: #eb6100;
      color: #eb6100;
    }
  }
}

.goods_info {
  .goods_detail {
    flex: 1;
    height: 80px;
    margin-left: 10px;
  }

  .goods_img {
    display: inline-block;
    width: 80px;
    height: 80px;
    overflow: hidden;
    border: 1px solid rgb(226 229 246 / 100%);
    border-radius: 3px;
    background: rgb(248 248 248 / 100%);
  }

  .goods_name {
    display: -webkit-box;
    height: 34px;
    overflow: hidden;
    color: #333;
    font-size: 14px;
    line-height: 17px;
    text-align: left;
    text-overflow: ellipsis;
    word-break: break-word;
    -webkit-line-clamp: 2;

    /*! autoprefixer: off */
    -webkit-box-orient: vertical;
  }

  .goods_brief {
    overflow: hidden;
    color: #666;
    font-size: 12px;
    text-overflow: ellipsis;
    word-break: keep-all;
  }
}

:global {
  .ant-modal-confirm-info {
    .ant-modal-content {
      padding: 20px;
    }
  }

  .ant-modal-confirm-info .ant-modal-confirm-body > .anticon {
    color:  !important;
  }
}

.sld_common_title {
  margin-top: 15px;
  margin-bottom: 15px;
  margin-left: 5px;
  color: rgb(51 51 51);
  font-size: 14px;
  font-weight: bold;
}

.spreader_order_detail {
  .ant-descriptions-item-label {
    width: 20%;
  }

  .ant-descriptions-item-content {
    width: 30%;
  }

  .invoice_info_box {
    .ant-descriptions-item-content {
      width: 80%;
    }
  }
  // 详情页高度
  .height_detail {
    max-height: calc(100vh - @header-height - 127px);
    overflow: auto;
  }

  .order_detail_total {
    width: 99%;
    height: 45px;
    border-radius: 6px;
    background: #fff;
    box-shadow: 0 0 8px 0 rgb(153 153 153 / 5%), 0 0 8px 0 rgb(153 153 153 / 5%);
    font-size: 14px;
    font-weight: bold;

    .amount_detail {
      color: #333;
    }

    .amount_total {
      color: #ff2b4e;
    }
  }
}
