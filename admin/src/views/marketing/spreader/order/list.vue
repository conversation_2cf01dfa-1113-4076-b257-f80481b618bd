<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader title="订单管理" />
      <div class="spreader_goods_lists">
        <BasicTable @register="standardTable" style="padding: 0">
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.key == 'action'">
              <TableAction
                :actions="[
                  {
                    onClick: view.bind(null, record),
                    label: '查看',
                  },
                ]"
              />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'SpreaderOrderList',
  };
</script>
<script setup>
  import { onMounted, unref } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { Popover } from 'ant-design-vue';
  import { useRouter } from 'vue-router';
  import { list_com_page_more } from '/@/utils/utils';
  import { getSpreaderExtendListApi } from '/@/api/spreader/order';
  import { validatorEmoji } from '/@/utils/validate';

  const router = useRouter();

  const [standardTable, { getForm }] = useTable({
    api: (arg) => getSpreaderExtendListApi({ ...arg }),
    rowKey: 'orderSn',
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    columns: [
      {
        title: '订单号',
        dataIndex: 'orderSn',
        width: 120,
      },
      {
        title: '会员名称',
        dataIndex: 'memberName',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: '推手佣金(¥)',
        dataIndex: 'commission',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: '下单时间',
        dataIndex: 'createTime',
        width: 100,
      },
      {
        title: '订单金额(¥)',
        dataIndex: 'orderAmount',
        width: 100,
      },
      {
        title: '订单状态',
        dataIndex: 'orderStateValue',
        width: 100,
      },
      {
        title: '佣金状态',
        dataIndex: 'commissionStateValue',
        width: 100,
      },
    ],
    useSearchForm: true,
    beforeFetch(values) {
      values.createTimeAfter = values.createTimeAfter
        ? values.createTimeAfter.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.createTimeBefore = values.createTimeBefore
        ? values.createTimeBefore.split(' ')[0] + ' 23:59:59'
        : undefined;
      return values;
    },
    actionColumn: {
      width: 80,
      title: '操作',
      dataIndex: 'action',
    },
    formConfig: {
      schemas: [
        {
          field: 'orderSn',
          component: 'Input',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            placeholder: '请输入订单号',
            size: 'default',
          },
          label: '订单号',
          labelWidth: 70,
          rules: [
            {
              // @ts-ignore
              validator: async (rule, value) => {
                await validatorEmoji(rule, value);
              },
              trigger: 'change',
            },
          ],
        },
        {
          field: '[createTimeAfter,createTimeBefore]',
          component: 'RangePicker',
          colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
          componentProps: {
            format: 'YYYY-MM-DD',
            placeholder: ['开始时间', '结束时间'],
          },
          label: `下单时间`,
          labelWidth: 80,
        },
        {
          field: 'orderState',
          component: 'Select',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
          componentProps: {
            placeHolder: '请选择订单状态',
            minWidth: 300,
            size: 'default',
            options: [
              { value: '', label: `全部` },
              { value: '0', label: `已取消` },
              { value: '10', label: `待付款` },
              { value: '20', label: `待发货` },
              { value: '30', label: `待收货` },
              { value: '40', label: `已完成` },
              { value: '50', label: `已关闭` },
            ],
          },
          label: '订单状态',
          labelWidth: 80,
        },
        {
          field: 'commissionState',
          component: 'Select',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
          componentProps: {
            placeHolder: '请选择佣金状态',
            minWidth: 300,
            size: 'default',
            options: [
              { value: '', label: `全部` },
              { value: '1', label: `冻结` },
              { value: '3', label: `已结算` },
              { value: '2', label: `失效` },
            ],
          },
          label: '佣金状态',
          labelWidth: 80,
        },
      ],
    },
    bordered: true,
    striped: false,
    showIndexColumn: true,
  });

  const view = (record) => {
    router.push({
      path: `/marketing_spreader/order_list_to_detail`,
      query: { orderSn: record.orderSn },
    });
  };

  onMounted(() => {});
</script>
<style lang="less" scoped>
  .business_load_img {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    margin: auto;

    img {
      max-width: 100%;
      max-height: 100%;
    }
  }
</style>
