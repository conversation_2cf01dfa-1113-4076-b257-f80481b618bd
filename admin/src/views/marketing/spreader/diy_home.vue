<template>
  <div class="section_padding common_page_toolbar">
    <div class="section_padding_back">
      <SldComHeader :type="1" :title="'首页装修'" />
      <BasicTable @register="standardTable" style="padding: 0">
        <template #tableTitle>
          <div class="toolbar">
            <div class="toolbar_btn" @click="handleClick(null, 'add')">
              <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#2ea9ff" />
              <span>新建页面</span>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.key == 'action'">
            <TableAction
              class="TableAction"
              :actions="[
                {
                  label: '装修',
                  onClick: handleClick.bind(null, record, 'diy'),
                },
                {
                  label: '编辑',
                  onClick: handleClick.bind(null, record, 'edit'),
                },
                {
                  label: '复制',
                  onClick: handleClick.bind(null, record, 'copy'),
                },
                {
                  label: '开屏图',
                  onClick: addAdv.bind(null, record),
                },
                {
                  label: '删除',
                  popConfirm: {
                    title: '删除后不可恢复，是否确定删除？',
                    placement: 'left',
                    confirm: handleClick.bind(null, record, 'del'),
                  },
                },
              ]"
            >
            </TableAction>
            <!-- <span class="common_page_edit" @click="handleClick(record, 'edit')">装修</span>
            <span class="common_page_edit" @click="handleClick(record, 'edit')">开屏图</span>-->
          </template>
          <template
            v-else-if="
              column.key == 'android' ||
              column.key == 'ios' ||
              column.key == 'h5' ||
              column.key == 'weixinXcx'
            "
          >
            <Switch
              :checked="text == 1 ? true : false"
              @change="(e) => handleChange(e, record, column.key)"
            />
          </template>
          <template v-else-if="column.key">
            {{ text ? text : text == 0 ? text : '--' }}
          </template>
        </template>
      </BasicTable>
    </div>
    <SldModal
      :width="width"
      :title="title"
      :visible="visible"
      :content="content"
      :confirmBtnLoading="confirmBtnLoading"
      :showFoot="true"
      @radio-change-event="handleCallback"
      @cancle-event="handleCancle"
      @confirm-event="handleConfirm"
    />

    <SldMoreImgModal
      :width="1000"
      :title="MoreTitle"
      :modalTip="modal_tip"
      :content="modalContent"
      ref="sldMoreImgModalRef"
      @confirm="moreImgConfirm"
      mode="spreader"
      :totalNum="1"
      client="mobile"
    ></SldMoreImgModal>
  </div>
</template>
<script lang="ts">
  import { defineComponent, ref,reactive } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import SldModal from '@/components/SldModal/index.vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { Switch, Popconfirm } from 'ant-design-vue';
  import SldMoreImgModal from '@/components/SldMoreImgModal/index.vue';
  import {
    mobileDecoList,
    addMobileDeco,
    delMobileDeco,
    copyMobileDeco,
    updateMobileDeco,
    isUseMobileDeco,
  } from '/@/api/spreader/spreader';
  import { sucTip, failTip } from '/@/utils/utils';
  import { useRouter } from 'vue-router';

  export default defineComponent({
    name: 'SpreaderDiyHome',
    components: {
      SldComHeader,
      BasicTable,
      Switch,
      SldModal,
      Popconfirm,
      TableAction,
      SldMoreImgModal,
    },
    setup() {
      const router = useRouter();

      const modal_tip = [
        '请严格根据提示要求上传规定尺寸的图片,图片不可以超过1M,否则影响页面加载效果',
        '编辑项中的“操作”指点击该内容所产生的链接地址，可通过下拉选项选择不同的方式',
      ];
      const MoreTitle = ref('设置开屏图')

      const modal_data = ref([
        {
        imgPath: '',
        imgUrl: '',
        info: {},
        link_type: '',
        link_value: '',
        title: '',
        width:0,
        height:0,
        }
      ])

      const modalContent = reactive(
        {
        width: 580,
        height: 776,
        show_width: 290,
        show_height: 388,
        data: [
          {
            imgPath: '',
            imgUrl: '',
            info: {},
            link_type: '',
            link_value: '',
            title: '',
            width:0,
            height:0,
          }
        ]
      })

      const sldMoreImgModalRef = ref();
      const modalState = ref({})

      const [standardTable, { reload }] = useTable({
        api: (arg) => mobileDecoList({ ...arg, type: 'spreader' }),
        // 点击搜索前处理的参数
        beforeFetch(values) {
          values.startTime = values.startTime
            ? values.startTime.split(' ')[0] + ' 00:00:00'
            : undefined;
          values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
          return values;
        },
        fetchSetting: {
          pageField: 'current',
          sizeField: 'pageSize',
          listField: 'data.list',
          totalField: 'data.pagination.total',
        },
        columns: [
          {
            title: '页面名称',
            dataIndex: 'name',
            width: 100,
          },
          {
            title: '创建时间',
            dataIndex: 'createTime',
            width: 100,
          },
          {
            title: '更新时间',
            dataIndex: 'updateTime',
            width: 100,
          },
          {
            title: 'Android',
            dataIndex: 'android',
            width: 100,
          },
          {
            title: 'IOS',
            dataIndex: 'ios',
            width: 100,
          },
          {
            title: '微商城',
            dataIndex: 'h5',
            width: 100,
          },
          {
            title: '微信小程序',
            dataIndex: 'weixinXcx',
            width: 100,
          },
        ],
        actionColumn: {
          width: 170,
          title: '操作',
          dataIndex: 'action',
        },
        useSearchForm: true,
        formConfig: {
          schemas: [
            {
              field: 'name',
              component: 'Input',
              colProps: { span: 6, style: 'width:250px;max-width:100%;flex:none' },
              componentProps: {
                minWidth: 150,
                placeholder: '请输入页面名称',
                size: 'default',
              },
              label: '页面名称',
              labelWidth: 80,
            },
          ],
        },
        bordered: true,
        striped: false,
        showIndexColumn: true,
      });
      const width = ref(600);
      const title = ref('');
      const visible = ref(false);
      const content: any = ref([]);
      const confirmBtnLoading = ref(false);
      const operate_item = ref({});
      const operate_type = ref('');

      const add_mobile_deco = ref([
        {
          type: 'input',
          label: `装修页面名称`,
          name: 'name',
          placeholder: `请输入装修页面名称`,
          showCount: true,
          maxLength: 8,
          initialValue: '',
          extra: '最多输入8个字',
          rules: [
            {
              required: true,
              whitespace: true,
              message: '请输入装修页面名称',
            },
          ],
          callback: true,
        },
      ]);

      const handleClick = async (item: any, type: any) => {
        operate_type.value = type;
        operate_item.value = item ? item : {};
        if (type == 'diy') {
          router.push(`/marketing_spreader/diy_home_to_edit?id=${item.decoId}&source=marketing_spreader&type=spreader`);
        } else if (type == 'add') {
          title.value = '添加首页装修';
          visible.value = true;
          content.value = JSON.parse(JSON.stringify(add_mobile_deco.value));
        } else if (type == 'del' || type == 'copy') {
          let val = { decoId: item.decoId };
          userOperateType(val);
        } else if (type == 'edit') {
          title.value = '编辑会员';
          getDetail(item, 'edit');
        }
      };

      const getDetail = (item: any, type: any) => {
        visible.value = true;
        let data: any = [];
        if (type == 'edit') {
          data = JSON.parse(JSON.stringify(add_mobile_deco.value));
          data[0].initialValue = item.name;
          data[0].rules = [{ required: true, whitespace: true, message: '请输入装修页面名称' }];
        }
        content.value = data;
      };

      const handleChange = (e: any, item: any, key) => {
        operate_type.value = 'switch';
        userOperateType({
          decoId: item.decoId,
          isUse: e ? 1 : 0,
          os: key,
        });
      };

      // 弹窗确认
      const handleConfirm = (val: any) => {
        if (operate_type.value == 'add') {
          val.type = 'spreader';
        } else if (operate_type.value == 'edit') {
          val.type = 'spreader';
          val.decoId = operate_item.value.decoId;
        }
        userOperateType(val);
      };
      //会员列表操作
      const userOperateType = async (params: any) => {
        confirmBtnLoading.value = true;
        let res: any = {};
        if (operate_type.value == 'add') {
          res = await addMobileDeco(params);
        } else if (operate_type.value == 'del') {
          res = await delMobileDeco(params);
        } else if (operate_type.value == 'copy') {
          res = await copyMobileDeco(params);
        } else if (operate_type.value == 'edit') {
          res = await updateMobileDeco(params);
        } else if (operate_type.value == 'switch') {
          res = await isUseMobileDeco(params);
        }
        confirmBtnLoading.value = false;
        if (res.state == 200) {
          sucTip(res.msg);
          handleCancle();
          reload();
        } else {
          failTip(res.msg);
        }
      };
      // 弹窗关闭
      const handleCancle = () => {
        visible.value = false;
        content.value = [];
        operate_item.value = '';
      };

      // 弹窗回调
      const handleCallback = () => {};

      const addAdv = async(val)=> {
        modalState.value = val
        let adv_data = []
        MoreTitle.value = '设置开屏图'
        if (val.showTip != null && val.showTip) {
          MoreTitle.value = '编辑开屏图'
          adv_data = JSON.parse(val.showTip.replace(/&quot;/g,"\""));
        }
        if(adv_data.length>0){
          modalContent.data = adv_data
        }else {
          modalContent.data = JSON.parse(JSON.stringify(modal_data.value))
        }
        sldMoreImgModalRef.value.setModal(true);
      }

      const moreImgConfirm = async (dataSource) => {
        const res = await updateMobileDeco({
          decoId: modalState.value.decoId,
          type:'spreader',
          showTip: JSON.stringify(dataSource.parent_data),
        });
        if (res?.state == 200) {
          sucTip(res.msg);
          modalState.value = [];
          sldMoreImgModalRef.value.setModal(false);
          reload()
        } else {
          failTip(res.msg);
        }
      }

      return {
        width,
        title,
        visible,
        content,
        confirmBtnLoading,
        operate_item,
        operate_type,

        standardTable,
        handleClick,
        handleChange,
        handleCallback,
        handleCancle,
        handleConfirm,
        modal_tip,
        modal_data,
        MoreTitle,
        modalContent,
        sldMoreImgModalRef,
        modalState,
        addAdv,
        moreImgConfirm
      };
    },
  });
</script>
<style lang="less">
  .common_page {
    padding: 20px;
    background: #fff;

    .common_page_edit {
      position: relative;
      margin-right: 5px;
      padding-right: 5px;
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover {
        color: #ff6a12;
      }

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        right: 0;
        width: 1px;
        height: 12px;
        margin-top: -5px;
        background: #ddd;
      }

      &:last-of-type {
        &::after {
          display: none;
        }
      }
    }

    &.common_page_toolbar {
      .vben-basic-table-form-container .ant-form {
        margin-bottom: 0;
      }

      .toolbar {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: 100%;
        height: 42px;
        margin-bottom: 5px;
        background: #ffe3d5;

        .toolbar_btn {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 28px;
          margin-left: 10px;
          padding: 0 7px;
          border-radius: 3px;
          background: #fff;
          cursor: pointer;

          span {
            margin-left: 5px;
          }
        }
      }
    }
  }
</style>
