<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader title="推手商品管理" />
      <div class="spreader_goods_lists">
        <BasicTable @register="standardTable" style="padding: 0">
          <template #bodyCell="{ column, record, text }">
            <template v-if="column.dataIndex == 'goodsImage'">
              <div class="goods_info com_flex_row_flex_start">
                <div class="goods_img" style="border: none">
                  <Popover placement="rightTop">
                    <template #content>
                      <div style="width: 200px">
                        <img :src="text" alt="" style="max-width: 100%; max-height: 100%" />
                      </div>
                    </template>
                    <div class="business_load_img">
                      <img :src="text" alt="" />
                    </div>
                  </Popover>
                </div>
                <div class="com_flex_column_space_between goods_detail">
                  <div
                    class="goods_name"
                    style="width: 380px; margin-top: 6px; white-space: initial"
                    :title="record.goodsName"
                  >
                    {{ record.goodsName }}
                  </div>
                  <span class="goods_brief" :title="record.labelName">
                    {{ record.labelName }}
                  </span>
                </div>
              </div>
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'SpreaderGoodsList',
  };
</script>
<script setup>
  import { onMounted, unref } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import { Popover } from 'ant-design-vue';
  import { list_com_page_more } from '/@/utils/utils';
  import {
    getSpreaderGoodsListApi,
    getSpreaderGoodsLabelListApi,
  } from '/@/api/spreader/goods_list';
  import { validatorEmoji } from '/@/utils/validate';

  const [standardTable, { getForm }] = useTable({
    api: (arg) => getSpreaderGoodsListApi({ ...arg }),
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    columns: [
      {
        title: '商品信息',
        dataIndex: 'goodsImage',
        width: 250,
      },
      {
        title: '商品价格(¥)',
        dataIndex: 'productPrice',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: '商品佣金(¥)',
        dataIndex: 'commission',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: '商品库存',
        dataIndex: 'stock',
        width: 100,
      },
      {
        title: '发布时间',
        dataIndex: 'createTime',
        width: 100,
      },
    ],
    useSearchForm: true,
    beforeFetch(values) {
      values.startTime = values.startTime
        ? values.startTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
      return values;
    },
    formConfig: {
      schemas: [
        {
          field: 'goodsName',
          component: 'Input',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            placeholder: '请输入商品名称',
            size: 'default',
          },
          label: '商品名称',
          labelWidth: 70,
          rules: [
            {
              // @ts-ignore
              validator: async (rule, value) => {
                await validatorEmoji(rule, value);
              },
              trigger: 'change',
            },
          ],
        },
        {
          field: 'labelId',
          component: 'Select',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
          componentProps: {
            placeholder: '请选择商品标签',
            minWidth: 300,
            size: 'default',
            options: [],
          },
          label: '商品标签',
          labelWidth: 80,
        },
        {
          field: '[startTime,endTime]',
          component: 'RangePicker',
          colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
          componentProps: {
            format: 'YYYY-MM-DD',
            placeholder: ['开始时间', '结束时间'],
          },
          label: `发布时间`,
          labelWidth: 80,
        },
      ],
    },
    bordered: true,
    striped: false,
    showIndexColumn: true,
  });

  //获取所有标签
  const get_label_list = async () => {
    try {
      const { updateSchema } = getForm();
      let res = await getSpreaderGoodsLabelListApi({ pageSize: list_com_page_more });
      if (res.state == 200) {
        await updateSchema({
          field: 'labelId',
          componentProps: {
            options: unref(res.data.list).map((item) => ({
              value: item.labelId,
              label: item.labelName,
            })),
          },
        });
      }
    } catch (error) {}
  };

  onMounted(() => {
    get_label_list();
  });
</script>
<style lang="less" scoped>
  .business_load_img {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    margin: auto;

    img {
      max-width: 100%;
      max-height: 100%;
    }
  }
</style>
