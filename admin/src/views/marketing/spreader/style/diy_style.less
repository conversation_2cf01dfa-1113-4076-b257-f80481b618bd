.spreader_diy_style {
  max-height: calc(100vh - 48px - 20px - 20px - 32px - 42px);
  padding: 10px 0;
  overflow: auto;

  .diy_style_list {
    width: 1240px;

    .diy_style_item {
      position: relative;
      width: 122px;
      height: 48px;
      margin-right: 10px;
      margin-bottom: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      cursor: pointer;

      &.active {
        border-color: #fc701e;
      }

      .diy_style_color {
        width: 46px;
        height: 20px;
        overflow: hidden;

        .diy_style_color_left {
          flex: 1;
          height: 20px;
        }

        .diy_style_color_middle {
          flex: 1;
          height: 20px;
          margin-right: 2px;
          margin-left: 2px;
        }

        .diy_style_color_right {
          flex: 1;
          height: 20px;
        }
      }

      .diy_style_name {
        margin-left: 8px;
        color: #666;
        font-size: 12px;
        text-align: center;
      }

      .diy_style_auto_name {
        color: #fc701e;
        font-size: 12px;
      }

      .diy_style_auto_arrow {
        flex-shrink: 0;
        width: 10px;
        height: 22px;
        margin-left: 5px;
        color: #fc701e;
        font-family: cursive;
        font-size: 14px;
        font-weight: 600;
      }

      .diy_style_checked {
        position: absolute;
        z-index: 1;
        right: -1px;
        bottom: -1px;
        width: 20px;
        height: 20px;
      }
    }
  }

  .prew_tips {
    margin-bottom: 10px;
    color: #999;
    font-family: 'PingFang SC';
    font-size: 12px;
    font-weight: 500;
  }

  .prew_title {
    margin-bottom: 10px;
    color: #222;
    font-family: 'PingFang SC';
    font-size: 15px;
    font-weight: bold;
    line-height: 40px;
  }

  .prew_list {
    width: 1240px;
    padding-left: 15px;

    .prew_item {
      position: relative;
      width: 375px;
      height: 666px;
      margin-right: 50px;
      overflow: hidden;
      border-radius: 10px;
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
      box-shadow: 0 0 12px 0 rgb(129 129 129 / 30%);
      cursor: default;

      &:last-of-type {
        margin-right: 0;
      }

      &.prew_item_list {
        .top_nav_desc {
          position: absolute;
          z-index: 2;
          top: 179px;
          left: 5px;
          padding: 2px 7px;
          transform: scale(0.8);
          border-radius: 6px;
          color: #fff;
          font-family: 'PingFang SC';
          font-size: 12px;
          font-weight: 500;
        }

        .diy_style_price {
          position: absolute;
          z-index: 2;
          align-items: baseline;
          font-family: 'PingFang SC';
          font-size: 12px;
          font-weight: 700;

          span {
            font-size: 18px;
          }

          &:nth-child(1) {
            top: 440px;
            left: 18px;
          }

          &:nth-child(2) {
            top: 440px;
            left: 203px;
          }
        }

        .diy_style_bottom_nav {
          position: absolute;
          z-index: 2;
          bottom: 1px;
          left: 29px;

          svg {
            margin-bottom: 1px;
            margin-left: 7px;
          }

          span {
            transform: scale(0.8);
            font-family: 'PingFang SC';
            font-size: 12px;
            font-weight: 500;
          }
        }
      }

      &.prew_item_goods {
        .diy_style_top_price {
          position: absolute;
          z-index: 2;
          top: 460px;
          left: 8px;
          font-family: 'PingFang SC';
          font-size: 13px;

          .diy_style_price {
            align-items: baseline;
            font-weight: 700;

            span {
              font-size: 22px;
            }
          }

          .diy_style_gift {
            margin-left: 12px;
            font-weight: 500;

            span {
              color: #333;
            }
          }
        }

        .diy_style_buy {
          position: absolute;
          z-index: 2;
          right: 12px;
          bottom: 6px;

          div {
            width: 115px;
            height: 34px;
            color: #fff;
            font-family: 'PingFang SC';
            font-size: 14px;
            font-weight: 500;
            line-height: 34px;
            text-align: center;

            &:nth-child(1) {
              border-radius: 18px 0 0 18px;
            }

            &:nth-child(2) {
              border-radius: 0 18px 18px 0;
            }
          }
        }
      }

      &.prew_item_confirm {
        .diy_style_top_price {
          position: absolute;
          z-index: 2;
          top: 336px;
          left: 104px;
          align-items: baseline;
          font-family: 'PingFang SC';
          font-size: 13px;

          .diy_style_price {
            align-items: baseline;
            font-weight: 700;

            span {
              font-size: 22px;
            }
          }

          .diy_style_gift {
            margin-left: 12px;
            font-weight: 500;

            span {
              color: #333;
            }
          }
        }

        .diy_style_buy {
          position: absolute;
          z-index: 2;
          bottom: 5px;
          left: 17px;
          width: 344px;
          height: 38px;
          border-radius: 20px;
          color: #fff;
          font-family: 'PingFang SC';
          font-size: 15px;
          font-weight: 500;
          line-height: 38px;
          text-align: center;
        }
      }
    }
  }
}

.diy_auto_color_modal {
  margin-top: 10px;
  margin-bottom: 10px;

  .color_title {
    width: 115px;
    margin-right: 5px;
    text-align: right;
  }

  .color_show {
    .show_color {
      display: inline-block;
      width: 50px;
      height: 30px;
      margin-right: 10px;
      padding: 5px;
      border: 1px solid #eee;
      line-height: 0;
      cursor: pointer;
    }

    span {
      display: inline-block;
      width: 100%;
      height: 100%;
    }
  }

  .color_picker_wrap {
    position: absolute;
    z-index: 2;

    .color_picker_mask {
      position: fixed;
      inset: 0;
    }
  }
}
