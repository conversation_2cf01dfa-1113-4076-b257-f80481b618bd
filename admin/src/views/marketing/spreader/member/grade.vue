<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader title="等级管理" />
      <div class="spreader_grade_lists">
        <BasicTable @register="standardTable" style="padding: 0">
          <template #tableTitle>
            <div class="toolbar">
              <div class="toolbar_btn" @click="handleClick('add')">
                <AliSvgIcon
                  iconName="iconxinzeng"
                  width="15px"
                  height="15px"
                  fillColor="rgb(255, 89, 8)"
                />
                <span>新增等级</span>
              </div>
            </div>
          </template>
          <template #bodyCell="{ column, record, text, index }">
            <template v-if="column.dataIndex == 'imageValue'">
              <Popover placement="rightTop">
                <template #content>
                  <div style="width: 100px">
                    <img :src="text" alt="" style="max-width: 100%; max-height: 100%" />
                  </div>
                </template>
                <div class="business_load_img">
                  <img :src="text" alt="" />
                </div>
              </Popover>
            </template>
            <template v-if="column.key == 'action'">
              <TableAction
                class="TableAction"
                :actions="[
                  {
                    onclick: handleClick.bind(null, 'edit', record),
                    label: '编辑',
                  },
                  {
                    label: '删除',
                    ifShow: index > 0 && record.isDefault == 0,
                    popConfirm: {
                      title: '删除后不可恢复，是否确定删除？',
                      placement: 'left',
                      confirm: handleClick.bind(null, 'del', record),
                    },
                  },
                ]"
              />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <SldModal
      :width="width"
      :title="title"
      :visible="visible"
      :content="content"
      :confirmBtnLoading="confirmBtnLoading"
      :showFoot="true"
      @cancle-event="handleCancle"
      @confirm-event="handleConfirm"
    />
  </div>
</template>
<script>
  export default {
    name: 'SpreaderMemberGrade',
  };
</script>
<script setup>
  import { onMounted, ref } from 'vue';
  import SldModal from '@/components/SldModal/index.vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { Popover } from 'ant-design-vue';
  import { failTip, sucTip } from '/@/utils/utils';
  import {
    getSpreaderGradeListApi,
    getSpreaderGradeAddApi,
    getSpreaderGradeUpdateApi,
    getSpreaderGradeDelApi,
  } from '/@/api/spreader/member';
  import { validatorEmoji, validatorSpecialString } from '/@/utils/validate';

  const [standardTable, { getForm, reload }] = useTable({
    api: (arg) => getSpreaderGradeListApi({ ...arg }),
    rowKey: 'gradeId',
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    columns: [
      {
        title: '等级名称',
        dataIndex: 'gradeName',
        width: 250,
      },
      {
        title: '等级图片',
        dataIndex: 'imageValue',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: '升级规则',
        dataIndex: 'subSpreaderNum',
        width: 100,
      },
      {
        title: '等级权益',
        dataIndex: 'gradeBenefits',
        width: 150,
        customRender: ({ text }) => {
          return `获取邀请新用户佣金的${text}%`;
        },
      },
    ],
    useSearchForm: true,
    beforeFetch(values) {
      values.startTime = values.startTime
        ? values.startTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
      return values;
    },
    formConfig: {
      schemas: [
        {
          field: 'gradeName',
          component: 'Input',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            placeholder: '请输入等级名称',
            size: 'default',
          },
          label: '等级名称',
          labelWidth: 70,
          rules: [
            {
              // @ts-ignore
              validator: async (rule, value) => {
                await validatorEmoji(rule, value);
              },
              trigger: 'change',
            },
          ],
        },
      ],
    },
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
    bordered: true,
    striped: false,
    showIndexColumn: true,
  });

  const width = ref(500);
  const title = ref('');
  const visible = ref(false);
  const content = ref([]);
  const confirmBtnLoading = ref(false);
  const operate_id = ref('');
  const operate_type = ref('');

  const gradeId = ref('');

  const operate_add_edit = ref([
    {
      type: 'input',
      label: `等级名称`,
      name: 'gradeName',
      placeholder: `请输入等级名称`,
      extra: '最多5个字',
      maxLength: 5,
      showCount: true,
      rules: [
        {
          required: true,
          whitespace: true,
          message: '请输入等级名称',
        },
        {
          validator: async (rule, value) => {
            await validatorEmoji(rule, value);
          },
        },
        {
          validator: async (rule, value) => {
            await validatorSpecialString(rule, value);
          },
        },
      ],
    },
    {
      type: 'upload_img_upload',
      label: '等级图片', //图片上传 一张
      name: 'image',
      star: true,
      initialValue: [], //控件里file-list 回显也用这个 数组格式
      upload_name: 'file', //upload_name
      upload_url: `v3/oss/admin/upload?source=setting`, //接口
      limit: 20, //文件最大
      accept: ' .gif, .jpeg, .png, .jpg,', //文件格式
      extra: '建议上传宽100*高100的无背景或者透明背景的图片',
    },
    {
      type: 'inputnum',
      label: `升级规则`,
      name: 'subSpreaderNum',
      placeholder: `请输入升级规则`,
      extra: '邀请用户满足该人数则提升为该等级',
      min: 0,
      max: 9999,
      rules: [
        {
          required: true,
          message: '请输入升级规则',
        },
      ],
    },
    {
      type: 'inputnum',
      label: `等级权益`,
      name: 'gradeBenefits',
      placeholder: `请输入等级权益`,
      extra: '获得邀请新用户注册佣金的百分比',
      min: 0,
      max: 100,
      rules: [
        {
          required: true,
          message: '请输入等级权益',
        },
      ],
    },
  ]); //添加、编辑商品标签弹窗数据

  const handleClick = (type, item) => {
    operate_type.value = type;
    if (type == 'add' || type == 'edit') {
      content.value.forEach((it) => {
        it.initialValue = '';
      });
      content.value = [];
      operate_add_edit.value.forEach((it) => {
        if (it.type == 'upload_img_upload') {
          it.initialValue = [];
        } else {
          it.initialValue = '';
        }
      });
    }
    if (type == 'add') {
      content.value = [];
      title.value = '添加等级';
      content.value = operate_add_edit.value;
      visible.value = true;
    } else if (type == 'edit') {
      title.value = '编辑等级';
      let data = operate_add_edit.value;
      data.map((items) => {
        if (items.name == 'image') {
          //初始化图片数据
          let fileList = [];
          let tmp_data = {};
          tmp_data.uid = item.gradeId;
          tmp_data.name = item.imageValue;
          tmp_data.status = 'done';
          tmp_data.url = item.imageValue;
          tmp_data.response = {
            data: {
              url: item.imageValue,
              path: item.image,
            },
          };
          fileList.push(tmp_data);
          items.initialValue = fileList;
        } else {
          items.initialValue = item[items.name] ? item[items.name] : undefined;
        }
      });
      content.value = data;
      gradeId.value = item.gradeId;
      visible.value = true;
    } else if (type == 'del') {
      operate_role({ gradeId: item.gradeId });
    }
  };

  //弹窗取消事件
  function handleCancle() {
    visible.value = false;
    content.value = [];
    gradeId.value = '';
  }

  //弹窗确认事件
  function handleConfirm(val) {
    if (operate_type.value == 'edit') {
      val.gradeId = gradeId.value;
    }
    if (val.image) {
      if (
        val.image.length == 0 ||
        !val.image[0].response ||
        val.image[0].response.data.path == undefined
      ) {
        failTip('请上传等级图片');
        return;
      } else {
        val.image = val.image[0].response.data.path;
      }
    }
    operate_role(val);
  }

  const operate_role = async (val) => {
    let res;
    confirmBtnLoading.value = true;
    if (operate_type.value == 'add') {
      res = await getSpreaderGradeAddApi(val);
    } else if (operate_type.value == 'edit') {
      res = await getSpreaderGradeUpdateApi(val);
    } else if (operate_type.value == 'del') {
      res = await getSpreaderGradeDelApi(val);
    }
    if (res.state == 200) {
      confirmBtnLoading.value = false;
      sucTip(res.msg);
      handleCancle();
      reload();
    } else {
      confirmBtnLoading.value = false;
      failTip(res.msg);
    }
  };

  onMounted(() => {});
</script>
<style lang="less">
  .spreader_grade_lists {
    .business_load_img {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 50px;
      height: 50px;
      margin: auto;

      img {
        max-width: 100%;
        max-height: 100%;
      }
    }

    .vben-basic-table-form-container .ant-form {
      padding-top: 0;
    }
  }
</style>
