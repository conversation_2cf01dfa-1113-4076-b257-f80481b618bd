<template>
  <div class="spreader_check_list">
    <BasicTable @register="registerTable" style="padding: 0">
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex == 'memberAvatar'">
          <div class="goods_info">
            <Popover placement="rightTop">
              <template #content>
                <div style="width: 100px">
                  <img :src="text" alt="" style="max-width: 100%; max-height: 100%" />
                </div>
              </template>
              <div class="business_load_img">
                <img :src="text" alt="" />
              </div>
            </Popover>
          </div>
        </template>
        <template v-if="column.key == 'action'">
          <TableAction
            class="TableAction"
            :actions="[
              {
                label: '删除',
                popConfirm: {
                  title: '确认删除该条数据吗？',
                  placement: 'left',
                  confirm: operateGoods.bind(null, record.spreaderId),
                },
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <SldModal
      width="500"
      :title="title"
      :visible="modalVisible"
      :content="content"
      :confirmBtnLoading="confirmBtnLoading"
      :showFoot="true"
      :parentPage="'good_list'"
      @cancle-event="handleCancle"
      @confirm-event="handleConfirm"
    />
  </div>
</template>
<script setup>
  import { ref, onMounted } from 'vue';
  import { Popover, Popconfirm } from 'ant-design-vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { getSpreaderAuditListApi, getSpreaderDelApi } from '/@/api/spreader/member';
  import SldModal from '@/components/SldModal/index.vue';
  import { failTip, sucTip } from '/@/utils/utils';

  const search_form_schema = ref([
    {
      field: 'memberName',
      component: 'Input',
      colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
      componentProps: {
        minWidth: 300,
        placeholder: '请输入会员名称',
        size: 'default',
      },
      label: '会员名称',
      labelWidth: 80,
    },
    {
      field: '[memberRegisterTimeAfter,memberRegisterTimeBefore]',
      component: 'RangePicker',
      colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
      componentProps: {
        format: 'YYYY-MM-DD',
        placeholder: ['开始时间', '结束时间'],
      },
      label: `申请时间`,
      labelWidth: 80,
    },
  ]);

  const columns = ref([
    {
      title: '会员名称',
      dataIndex: 'memberName',
      width: 250,
    },
    {
      title: '会员头像',
      dataIndex: 'memberAvatar',
      width: 100,
    },
    {
      title: '会员昵称',
      dataIndex: 'memberNickName',
      width: 100,
      customRender: ({ text }) => {
        return text ? text : '--';
      },
    },
    {
      title: '手机号',
      dataIndex: 'memberMobile',
      width: 120,
      customRender: ({ text }) => {
        return text ? text : '--';
      },
    },
    {
      title: '消费金额(¥)',
      dataIndex: 'orderAmount',
      width: 120,
    },
    {
      title: '消费次数',
      dataIndex: 'orderNumber',
      width: 100,
    },
    {
      title: '申请时间',
      dataIndex: 'createTime',
      width: 150,
    },
    {
      title: '状态',
      dataIndex: 'stateValue',
      width: 100,
    },
    {
      title: '拒绝理由',
      dataIndex: 'failReason',
      width: 100,
      customRender: ({ text }) => {
        return text ? text : '--';
      },
    },
  ]);
  const content = ref([]);

  const [registerTable, { reload }] = useTable({
    rowKey: 'spreaderId',
    api: (arg) => getSpreaderAuditListApi({ ...arg, state: 3 }),
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    columns: columns,
    actionColumn: {
      width: 140,
      title: '操作',
      dataIndex: 'action',
    },
    useSearchForm: true,
    formConfig: {
      schemas: search_form_schema,
    },
    beforeFetch(values) {
      values.memberRegisterTimeAfter = values.memberRegisterTimeAfter
        ? values.memberRegisterTimeAfter.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.memberRegisterTimeBefore = values.memberRegisterTimeBefore
        ? values.memberRegisterTimeBefore.split(' ')[0] + ' 23:59:59'
        : undefined;
      return values;
    },
    bordered: true,
    striped: false,
  });

  // 审核通过
  const operateGoods = async (ids) => {
    let res = await getSpreaderDelApi({ spreaderId: ids });
    if (res.state == 200) {
      sucTip(res.msg);
      reload();
    } else {
      failTip(res.msg);
    }
  };

  onMounted(() => {});
</script>
<style lang="less">
  .spreader_check_list {
    .business_load_img {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60px;
      height: 60px;
      margin: auto;

      img {
        max-width: 100%;
        max-height: 100%;
      }
    }
  }
</style>
