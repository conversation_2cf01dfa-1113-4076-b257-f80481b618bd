<template>
  <div class="spreader_check_list">
    <BasicTable @register="registerTable" style="padding: 0">
      <template #tableTitle>
        <div class="toolbar">
          <Popconfirm
            v-if="selectedRowKeys.length > 0"
            title="确认审核通过选中的商品吗？"
            @confirm="operateGoods(selectedRowKeys.join(','))"
          >
            <div class="toolbar_btn">
              <AliSvgIcon
                iconName="iconshenhetongguo1"
                width="15px"
                height="15px"
                fillColor="#0fb39a"
              />
              <span>审核通过</span>
            </div>
          </Popconfirm>
          <div class="toolbar_btn" v-else @click="operateGoods(selectedRowKeys.join(','))">
            <AliSvgIcon
              iconName="iconshenhetongguo1"
              width="15px"
              height="15px"
              fillColor="#0fb39a"
            />
            <span>审核通过</span>
          </div>
          <div class="toolbar_btn" @click="handleClick(null, 'lockOff')">
            <AliSvgIcon iconName="iconshenhejujue1" width="15px" height="15px" fillColor="#fa0920" />
            <span>审核拒绝</span>
          </div>
        </div>
      </template>
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex == 'memberAvatar'">
          <div class="goods_info">
            <Popover placement="rightTop">
              <template #content>
                <div style="width: 100px">
                  <img :src="text" alt="" style="max-width: 100%; max-height: 100%" />
                </div>
              </template>
              <div class="business_load_img">
                <img :src="text" alt="" />
              </div>
            </Popover>
          </div>
        </template>
        <template v-if="column.key == 'action'">
          <TableAction
            class="TableAction"
            :actions="[
              {
                label: '审核通过',
                popConfirm: {
                  title: '确认审核通过该条数据吗？',
                  placement: 'left',
                  confirm: operateGoods.bind(null, record.spreaderId),
                },
              },
              {
                onclick: handleClick.bind(null, record, 'lockOff'),
                label: '审核拒绝',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <SldModal
      width="500"
      :title="title"
      :visible="modalVisible"
      :content="content"
      :confirmBtnLoading="confirmBtnLoading"
      :showFoot="true"
      :parentPage="'good_list'"
      @cancle-event="handleCancle"
      @confirm-event="handleConfirm"
    />
  </div>
</template>
<script setup>
  import { ref, onMounted } from 'vue';
  import { Popover, Popconfirm } from 'ant-design-vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { getSpreaderAuditListApi, getSpreaderAuditApi } from '/@/api/spreader/member';
  import { selectRadio, SelectAll, failTip, sucTip } from '/@/utils/utils';
  import SldModal from '@/components/SldModal/index.vue';

  const search_form_schema = ref([
    {
      field: 'memberName',
      component: 'Input',
      colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
      componentProps: {
        minWidth: 300,
        placeholder: '请输入会员名称',
        size: 'default',
      },
      label: '会员名称',
      labelWidth: 80,
    },
    {
      field: '[memberRegisterTimeAfter,memberRegisterTimeBefore]',
      component: 'RangePicker',
      colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
      componentProps: {
        format: 'YYYY-MM-DD',
        placeholder: ['开始时间', '结束时间'],
      },
      label: `发布时间`,
      labelWidth: 80,
    },
  ]);

  const columns = ref([
    {
      title: '会员名称',
      dataIndex: 'memberName',
      width: 250,
    },
    {
      title: '会员头像',
      dataIndex: 'memberAvatar',
      width: 100,
    },
    {
      title: '会员昵称',
      dataIndex: 'memberNickName',
      width: 100,
      customRender: ({ text }) => {
        return text ? text : '--';
      },
    },
    {
      title: '消费金额(¥)',
      dataIndex: 'orderAmount',
      width: 120,
    },
    {
      title: '消费次数',
      dataIndex: 'orderNumber',
      width: 100,
    },
    {
      title: '申请时间',
      dataIndex: 'createTime',
      width: 150,
    },
    {
      title: '状态',
      dataIndex: 'stateValue',
      width: 100,
    },
  ]);

  const selectedRowKeys = ref([]);
  const selectedRows = ref([]);
  const modalVisible = ref(false);
  const confirmBtnLoading = ref(false);
  const title = ref('违规下架商品');
  const content = ref([]);
  const operate_type = ref('');
  const operate_item = ref({});
  const operate_lockoff_data = ref([
    {
      type: 'textarea',
      label: `拒绝理由`,
      name: 'failReason',
      placeholder: `请输入拒绝理由`,
      extra: `最多输入50个字`,
      maxLength: 50,
      rules: [
        {
          required: true,
          message: `请输入拒绝理由`,
        },
      ],
    },
  ]);

  const [registerTable, { reload }] = useTable({
    rowKey: 'spreaderId',
    api: (arg) => getSpreaderAuditListApi({ ...arg, state: 1 }),
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    columns: columns,
    actionColumn: {
      width: 140,
      title: '操作',
      dataIndex: 'action',
    },
    useSearchForm: true,
    formConfig: {
      schemas: search_form_schema,
    },
    beforeFetch(values) {
      values.memberRegisterTimeAfter = values.memberRegisterTimeAfter
        ? values.memberRegisterTimeAfter.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.memberRegisterTimeBefore = values.memberRegisterTimeBefore
        ? values.memberRegisterTimeBefore.split(' ')[0] + ' 23:59:59'
        : undefined;
      return values;
    },
    bordered: true,
    striped: false,
    rowSelection: {
      type: 'checkbox',
      selectedRowKeys: selectedRowKeys,
      onSelect: onSelect,
      onSelectAll: onSelectAll,
      getCheckboxProps(record) {
        if (record.state == 4) {
          return { disabled: true };
        } else {
          return { disabled: false };
        }
      },
    },
    clickToRowSelect: false,
  });

  // 审核通过
  const operateGoods = async (ids) => {
    if (ids.length == 0) {
      failTip('请先选中数据');
      return;
    }
    confirmBtnLoading.value = true;
    let res = await getSpreaderAuditApi({ spreaderIds: ids, state: 1 });
    if (res.state == 200) {
      sucTip(res.msg);
      modalVisible.value = false;
      confirmBtnLoading.value = false;
      selectedRows.value = [];
      selectedRowKeys.value = [];
      reload();
    } else {
      failTip(res.msg);
      confirmBtnLoading.value = false;
    }
  };

  //表格点击回调事件
  const handleClick = (item, type) => {
    operate_type.value = type;
    operate_item.value = item ? item : null;
    let data = [];
    if (type == 'lockOff') {
      if (item == null && selectedRowKeys.value.length == 0) {
        failTip('请先选中数据');
        return;
      }
      data = JSON.parse(JSON.stringify(operate_lockoff_data.value));
      title.value = '拒绝理由';
    }
    content.value = data;
    modalVisible.value = true;
  };

  //弹窗取消事件
  const handleCancle = () => {
    modalVisible.value = false;
    content.value = [];
    operate_item.value = {};
  };

  // 单选多选事件
  function onSelect(record, selected) {
    let rows = selectRadio(
      selectedRowKeys.value,
      selectedRows.value,
      'spreaderId',
      record,
      selected,
    );
    selectedRowKeys.value = rows.selectedRowKeys;
    selectedRows.value = rows.selectedRows;
  }

  // 全选按钮事件
  function onSelectAll(selected, rows, changeRows) {
    let rowAll = SelectAll(
      selectedRowKeys.value,
      selectedRows.value,
      'spreaderId',
      selected,
      rows,
      changeRows,
    );
    selectedRowKeys.value = rowAll.selectedRowKeys;
    selectedRows.value = rowAll.selectedRows;
  }

  //弹窗确认事件
  const handleConfirm = async (val) => {
    if (operate_type.value == 'lockOff') {
      val.state = 0;
      val.spreaderIds =
        operate_item.value != null
          ? operate_item.value.spreaderId
          : selectedRowKeys.value.join(',');
    }
    confirmBtnLoading.value = true;
    let res = await getSpreaderAuditApi(val);
    if (res.state == 200) {
      sucTip(res.msg);
      modalVisible.value = false;
      confirmBtnLoading.value = false;
      selectedRows.value = [];
      selectedRowKeys.value = [];
      reload();
    } else {
      failTip(res.msg);
      confirmBtnLoading.value = false;
    }
  };

  onMounted(() => {});
</script>
<style lang="less">
  .spreader_check_list {
    .business_load_img {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60px;
      height: 60px;
      margin: auto;

      img {
        max-width: 100%;
        max-height: 100%;
      }
    }
  }
</style>
