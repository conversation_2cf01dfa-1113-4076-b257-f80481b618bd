<template>
  <div>
    <BasicTable @register="standardTable" style="padding: 0">
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex == 'memberAvatar'">
          <div class="goods_info">
            <Popover placement="rightTop">
              <template #content>
                <div style="width: 100px">
                  <img :src="text" alt="" style="max-width: 100%; max-height: 100%" />
                </div>
              </template>
              <div class="business_load_img">
                <img :src="text" alt="" />
              </div>
            </Popover>
          </div>
        </template>
        <template v-if="column.key == 'action'">
          <TableAction
            class="TableAction"
            :actions="[
              {
                onclick: handleClick.bind(null, record.spreaderId),
                label: '查看下级',
              },
            ]"
          />
        </template>
      </template>
    </BasicTable>
    <SldSelGoodsSingleDiy
      @cancle-event="handleCancle"
      :api="getSpreaderInvitationListApi"
      rowId="spreaderId"
      modalTitle="查看下级"
      look
      :searchInfo="{ spreaderId: spreaderId }"
      :column="column"
      :modalVisible="modalVisible"
    >
      <template #bodyCell="{ column, record, text }">
        <template v-if="column.dataIndex == 'memberAvatar'">
          <div class="goods_info">
            <Popover placement="rightTop">
              <template #content>
                <div style="width: 100px">
                  <img :src="text" alt="" style="max-width: 100%; max-height: 100%" />
                </div>
              </template>
              <div class="business_load_img">
                <img :src="text" alt="" />
              </div>
            </Popover>
          </div>
        </template>
      </template>
    </SldSelGoodsSingleDiy>
  </div>
</template>
<script>
  export default {
    name: 'SpreaderMemberCheckedList',
  };
</script>
<script setup>
  import { onMounted, ref, h } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import SldSelGoodsSingleDiy from '/@/components/SldSelGoodsSingleDiy/index.vue';
  import { Popover } from 'ant-design-vue';
  import { getSpreaderListApi, getSpreaderInvitationListApi } from '/@/api/spreader/member';
  import { validatorEmoji } from '/@/utils/validate';

  const column = ref([
    {
      title: `会员名称`,
      align: 'center',
      dataIndex: 'memberName',
      width: 100,
    },
    {
      title: `会员头像`,
      align: 'center',
      dataIndex: 'memberAvatar',
      width: 100,
    },
    {
      title: `注册时间`,
      align: 'center',
      dataIndex: 'registerTime',
      width: 150,
    },
    {
      title: `是否推手`,
      align: 'center',
      dataIndex: 'isSpreader',
      width: 100,
      customRender: ({ text }) => {
        return text == 1 ? '是' : '否';
      },
    },
    {
      title: `佣金(元)`,
      align: 'center',
      dataIndex: 'inviteRewardCommission',
      width: 100,
    },
    {
      title: `下级数量`,
      align: 'center',
      dataIndex: 'inviteSpreaderNum',
      width: 100,
    },
  ]);

  const spreaderId = ref('');
  const modalVisible = ref(false);

  const [standardTable] = useTable({
    api: (arg) => getSpreaderListApi({ ...arg }),
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    columns: [
      {
        title: '会员名称',
        dataIndex: 'memberName',
        width: 250,
      },
      {
        title: '会员头像',
        dataIndex: 'memberAvatar',
        width: 100,
      },
      {
        title: '会员昵称',
        dataIndex: 'memberNickName',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: '手机号',
        dataIndex: 'memberMobile',
        width: 120,
      },
      {
        title: '上级推手',
        dataIndex: 'parentSpreaderMemberName',
        width: 100,
        customRender: ({ text }) => {
          return text ? text : '--';
        },
      },
      {
        title: '下级推手数量',
        dataIndex: 'inviteSpreaderNum',
        width: 120,
      },
      {
        title: '总销售金额',
        dataIndex: 'sumOrderAmount',
        width: 120,
        customRender: ({ text }) => {
          return text ? text : 0;
        },
      },
      {
        title: '最后登录时间',
        dataIndex: 'signInTime',
        width: 150,
      },
    ],
    useSearchForm: true,
    beforeFetch(values) {
      values.startTime = values.startTime
        ? values.startTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
      return values;
    },
    actionColumn: {
      width: 140,
      title: '操作',
      dataIndex: 'action',
    },
    formConfig: {
      schemas: [
        {
          field: 'memberName',
          component: 'Input',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            placeholder: '请输入会员名称',
            size: 'default',
          },
          label: '会员名称',
          labelWidth: 70,
          rules: [
            {
              // @ts-ignore
              validator: async (rule, value) => {
                await validatorEmoji(rule, value);
              },
              trigger: 'change',
            },
          ],
        },
      ],
    },
    bordered: true,
    striped: false,
    showIndexColumn: true,
  });

  const handleClick = (id) => {
    spreaderId.value = id;
    setTimeout(() => {
      modalVisible.value = true;
    });
  };
  const handleCancle = () => {
    modalVisible.value = false;
  };

  onMounted(() => {});
</script>
<style lang="less" scoped>
  .business_load_img {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    margin: auto;

    img {
      max-width: 100%;
      max-height: 100%;
    }
  }
</style>
