<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader title="推手管理" />
      <Tabs type="card" v-model:activeKey="activeKey" class="spreader_list">
        <TabPane key="1" tab="推手列表">
          <SpreaderMemberCheckedList
            style="padding-top: 10px"
            v-if="activeKey == 1"
          ></SpreaderMemberCheckedList>
        </TabPane>
        <TabPane key="2" tab="推手审核">
          <SpreaderMemberCheckList
            style="padding-top: 10px"
            v-if="activeKey == 2"
          ></SpreaderMemberCheckList>
        </TabPane>
        <TabPane key="3" tab="推手审核拒绝">
          <SpreaderMemberRefusedList
            style="padding-top: 10px"
            v-if="activeKey == 3"
          ></SpreaderMemberRefusedList>
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'SpreaderMemberList',
  };
</script>
<script setup>
  import { ref, onMounted } from 'vue';
  import { Tabs, TabPane } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import SpreaderMemberCheckedList from './checked_list.vue';
  import SpreaderMemberCheckList from './check_list.vue';
  import SpreaderMemberRefusedList from './refused_list.vue';

  const activeKey = ref('1');

  onMounted(() => {});
</script>
<style lang="less">
  .RadioGroup_back {
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within {
      box-shadow: none;
    }
  }

  .spreader_list {
    .ant-tabs-nav {
      margin: 0;
    }
  }
</style>
