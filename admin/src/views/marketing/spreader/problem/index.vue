<template>
  <div class="section_padding">
    <div class="section_padding_back">
      <SldComHeader title="问题管理" />
      <Tabs type="card" v-model:activeKey="activeKey" class="problem_index">
        <TabPane key="1" tab="问题分类">
          <ProblemCat style="padding-top: 10px"></ProblemCat>
        </TabPane>
        <TabPane key="2" tab="问题列表">
          <ProblemList style="padding-top: 10px"></ProblemList>
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'SpreaderProblem',
  };
</script>
<script setup>
  import { ref, onMounted } from 'vue';
  import { Tabs, TabPane } from 'ant-design-vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import ProblemCat from './category.vue';
  import ProblemList from './list.vue';
  import { useUserStore } from '/@/store/modules/user';

  const activeKey = ref('1');

  const userStore = useUserStore();

  onMounted(() => {
    if(userStore.getUpdateTab.length>0){
      let index = userStore.getUpdateTab.findIndex(item=>item.name == 'SpreaderProblem')
      if(index>-1){
        activeKey.value = userStore.getUpdateTab[index].index
        userStore.setDelTab('SpreaderProblem')
      }
    }
  });
</script>
<style lang="less">
  .RadioGroup_back {
    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):focus-within {
      box-shadow: none;
    }
  }

  .problem_index {
    .ant-tabs-nav {
      margin: 0;
    }
  }
</style>
