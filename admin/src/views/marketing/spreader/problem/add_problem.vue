<template>
  <div class="section_padding add_problem">
    <div class="section_padding_back full_activity">
      <SldComHeader :title="title" />
      <div class="com_line" style="height: 1px"></div>
      <Spin :spinning="loading">
        <div class="add_coupon_height">
          <Form layout="inline" ref="formRef" :model="problem_detail">
            <div>
              <!-- 基本信息 start -->
              <CommonTitle text="基本信息" style="margin-top: 10px" />
              <div class="full_acm_activity flex_column_start_start">
                <!-- 问题 start -->
                <div class="item flex_row_start_start">
                  <div class="left"> <span style="color: red">*</span>问题 </div>
                  <div class="right">
                    <Form.Item
                      extra="最多输入30个字"
                      name="problemContent"
                      style="width: 400px"
                      :rules="[
                        {
                          required: true,
                          whitespace: true,
                          message: `请输入问题`,
                        },
                      ]"
                    >
                      <Input
                        :maxLength="30"
                        style="width: 400px !important"
                        placeholder="请输入问题"
                        v-model:value="problem_detail.problemContent"
                      />
                    </Form.Item>
                  </div>
                </div>
                <!-- 问题 end -->
                <!-- 分类 start -->
                <div class="item flex_row_start_start">
                  <div class="left"> <span style="color: red">*</span>分类 </div>
                  <div class="right">
                    <Form.Item
                      name="categoryId"
                      style="width: 400px"
                      :rules="[
                        {
                          required: true,
                          message: `请选择分类`,
                        },
                      ]"
                    >
                      <Select
                        placeholder="请选择分类"
                        :disabled="viewFlag"
                        style="width: 400px"
                        :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                        v-model:value="problem_detail.categoryId"
                      >
                        <Select.Option
                          v-for="(item, index) in category_list"
                          :key="index"
                          :value="item.categoryId"
                          >{{ item.categoryName }}</Select.Option
                        >
                      </Select>
                    </Form.Item>
                  </div>
                </div>
                <!-- 分类 end -->
                <!-- 是否显示 start -->
                <div class="item flex_row_start_start">
                  <div class="left"> <span style="color: red">*</span>是否显示 </div>
                  <div class="right">
                    <Form.Item name="isShow" style="width: 400px">
                      <RadioGroup size="small" v-model:value="problem_detail.isShow">
                        <Radio :value="1">是</Radio>
                        <Radio :value="0">否</Radio>
                      </RadioGroup>
                    </Form.Item>
                  </div>
                </div>
                <!-- 是否显示 end -->
                <!-- 是否显示 start -->
                <div class="item flex_row_start_start">
                  <div class="left"> <span style="color: red">*</span>排序 </div>
                  <div class="right">
                    <Form.Item
                      extra="数字范围为0~255，数字越小越靠前"
                      name="sort"
                      style="width: 400px"
                      :rules="[
                        {
                          required: true,
                          message: `请输入排序`,
                        },
                      ]"
                    >
                      <InputNumber
                        :max="255"
                        :min="0"
                        style="width: 400px !important"
                        :precision="0"
                        placeholder="请输入排序"
                        v-model:value="problem_detail.sort"
                      />
                    </Form.Item>
                  </div>
                </div>
                <!-- 是否显示 end -->
              </div>
              <!-- 基本信息 end -->
              <CommonTitle text="问题内容" style="margin-top: 10px" />
              <div class="full_acm_activity flex_column_start_start">
                <!-- 是否显示 start -->
                <div class="item flex_row_start_start">
                  <div class="left"> <span style="color: red">*</span>内容 </div>
                  <div class="right">
                    <Form.Item>
                      <SldUEditor
                        v-if="initEditorFlag"
                        :id="'agreement'"
                        :initEditorContent="initEditorContent"
                        :getContentFlag="getEditorContentFlag"
                        :getEditorContent="getEditorContent"
                      />
                    </Form.Item>
                  </div>
                </div>
                <!-- 是否显示 end -->
              </div>
            </div>
          </Form>
        </div>
        <div
          class="m_diy_bottom_wrap"
          :style="{ position: 'fixed', left: getRealWidth + 10 + 'px' }"
        >
          <div class="add_goods_bottom_btn" @click="goBack"> 返回 </div>
          <div class="add_goods_bottom_btn add_goods_bottom_btn_sel" @click="handleSaveAllData">
            保存
          </div>
        </div>
      </Spin>
    </div>
  </div>
</template>
<script>
  export default {
    name: 'SpreaderProblemAdd',
  };
</script>
<script setup>
  import { ref, onMounted } from 'vue';
  import {
    Spin,
    Input,
    Form,
    Select,
    RadioGroup,
    Radio,
    InputNumber,
  } from 'ant-design-vue';
  import SldUEditor from '/@/components/SldUEditor/index.vue';
  import CommonTitle from '/@/components/CommonTitle/index.vue';
  import { useRoute, useRouter } from 'vue-router';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import { failTip, list_com_page_more, quillEscapeToHtml, sucTip } from '/@/utils/utils';
  import { useUserStore } from '/@/store/modules/user';
  import {
    getSpreaderCategoryListApi,
    getSpreaderProblemAddApi,
    getSpreaderProblemUpdateApi,
    getSpreaderProblemDetailApi,
  } from '/@/api/spreader/problem';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  const { getRealWidth } = useMenuSetting();
  import { pageClose } from '/@/utils/utils';
  import { useMultipleTabStore } from '/@/store/modules/multipleTab';

  const route = useRoute();
  const tabStore = useMultipleTabStore();
  const router = useRouter();
  const userStore = useUserStore();
  const formRef = ref(null);

  const title = ref('');
  const loading = ref(false);

  const initEditorFlag = ref(false); //加载百度编辑器
  const getEditorContentFlag = ref(false); //获取百度编辑器内容标识
  const initEditorContent = ref(''); //百度编辑器内容
  const isFirstLoading = ref(true); //是否第一次加载
  const problem_detail = ref({
    isShow: 1,
  }); //问题详情
  const query = ref(route.query);
  const category_list = ref([]); //分类列表

  //获取分类列表
  const get_category_list = async () => {
    let res = await getSpreaderCategoryListApi({ pageSize: list_com_page_more });
    if (res.state == 200) {
      category_list.value = res.data.list;
    }
  };

  // 返回
  const goBack = () => {
    if (window.history && window.history.length == 1) {
      pageClose();
    } else {
      const { fullPath } = route;
      tabStore.closeTabByKey(fullPath, router);
      router.back();
    }
  };

  //保存并新增事件
  const handleSaveAllData = () => {
    getEditorContentFlag.value = true;
  };

  //获取编辑器内容
  const getEditorContent = (con) => {
    saveData(con);
    getEditorContentFlag.value = false;
  };

  //保存并新增事件
  const saveData = async (editorCon) => {
    formRef.value.validate().then(async (values) => {
      let tmp_data = editorCon.replaceAll('<p>', '');
      tmp_data = tmp_data.replaceAll('</p>', '');
      tmp_data = tmp_data.replaceAll('<br>', '');
      tmp_data = tmp_data.replace(/(^\s*)|(\s*$)/g, '');
      if (!tmp_data) {
        failTip(`请输入问题内容～`);
        return;
      }
      values.problemReply = editorCon; //回答内容
      let res;
      loading.value = true;
      if (route.query?.id != undefined && route.query?.id > 0) {
        //编辑
        values.problemId = route.query?.id;
        res = await getSpreaderProblemUpdateApi(values);
      } else {
        //新增
        res = await getSpreaderProblemAddApi(values);
      }
      if (res.state == 200) {
        loading.value = false;
        sucTip(res.msg);
        userStore.setDelKeepAlive([route.name,'SpreaderProblem'])
        userStore.setUpdateTab({name:'SpreaderProblem',index:'2'})
        setTimeout(() => {
          goBack();
        }, 500);
      } else {
        loading.value = false;
        failTip(res.msg);
      }
    });
  };

  //获取问题详情
  const get_detail = async (id) => {
    loading.value = true;
    let res = await getSpreaderProblemDetailApi({ problemId: id });
    if (res.state == 200) {
      problem_detail.value = res.data;
      initEditorContent.value = quillEscapeToHtml(problem_detail.value.problemReply);
      isFirstLoading.value = false;
      initEditorFlag.value = true;
      loading.value = false;
    }
  };

  onMounted(() => {
    get_category_list();
    if (route.query?.id != undefined && Number(route.query?.id) > 0) {
      title.value = '编辑问题';
      get_detail(route.query?.id);
    } else {
      title.value = '新增问题';
      initEditorFlag.value = true;
      isFirstLoading.value = false;
    }
  });
</script>
<style lang="less">
  @import './style/add_problem.less';
</style>
