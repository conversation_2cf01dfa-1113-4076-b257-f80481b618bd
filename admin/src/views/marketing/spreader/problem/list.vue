<template>
  <div class="problem_category">
    <div>
      <BasicTable @register="standardTable" style="padding: 0">
        <template #tableTitle>
          <div class="toolbar">
            <div class="toolbar_btn" @click="handleClick(null, 'add', null)">
              <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#fff" />
              <span>新增问题</span>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.dataIndex == 'isShow'">
            <div>
              <Switch
                @change="(checked) => handleClick(record, 'switch', checked ? 1 : 0)"
                :checked="text == 1 ? true : false"
              />
            </div>
          </template>
          <template v-if="column.dataIndex == 'action'">
            <template v-if="record.isInner == 1"> -- </template>
            <template v-else>
              <TableAction
                :actions="[
                  {
                    label: '编辑',
                    onClick: handleClick.bind(null, record, 'edit'),
                  },
                  {
                    label: '删除',
                    popConfirm: {
                      title: '删除后不可恢复，是否确定删除？',
                      placement: 'left',
                      confirm: handleClick.bind(null, record, 'del'),
                    },
                  },
                ]"
              />
            </template>
          </template>
        </template>
      </BasicTable>
    </div>
  </div>
</template>
<script setup>
  import { onMounted, unref } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { Switch } from 'ant-design-vue';
  import { useRouter } from 'vue-router';
  import {
    getSpreaderCategoryListApi,
    getSpreaderProblemListApi,
    getSpreaderProblemIsShowApi,
    getSpreaderProblemDelApi,
  } from '/@/api/spreader/problem';
  import { failTip, list_com_page_more, sucTip } from '/@/utils/utils';
  import { validatorEmoji } from '/@/utils/validate';
  import { useUserStore } from '/@/store/modules/user';

  const router = useRouter();
  const userStore = useUserStore();

  const [standardTable, { reload, getForm }] = useTable({
    rowKey: 'problemId',
    api: (arg) => getSpreaderProblemListApi({ ...arg }),
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    columns: [
      {
        title: '问题',
        dataIndex: 'problemContent',
        width: 150,
      },
      {
        title: '问题分类',
        dataIndex: 'categoryName',
        width: 150,
      },
      {
        title: '排序',
        dataIndex: 'sort',
        width: 100,
      },
      {
        title: '是否显示',
        dataIndex: 'isShow',
        width: 100,
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
        width: 150,
      },
    ],
    actionColumn: {
      width: 80,
      title: '操作',
      dataIndex: 'action',
    },
    useSearchForm: true,
    formConfig: {
      schemas: [
        {
          field: 'problemContent',
          component: 'Input',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            placeholder: '请输入问题',
            size: 'default',
          },
          label: '问题',
          labelWidth: 70,
          rules: [
            {
              // @ts-ignore
              validator: async (rule, value) => {
                await validatorEmoji(rule, value);
              },
              trigger: 'change',
            },
          ],
        },
        {
          field: 'categoryId',
          component: 'Select',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
          componentProps: {
            placeholder: '请选择问题分类',
            minWidth: 300,
            size: 'default',
            options: [],
          },
          label: '问题分类',
          labelWidth: 80,
        },
      ],
    },
    bordered: true,
    striped: false,
    ellipsis: false,
    showIndexColumn: true,
  });

  //问题操作  del：删除  switch: 是否显示
  const handleClick = async (record, type, check) => {
    let res;
    if (type == 'switch') {
      res = await getSpreaderProblemIsShowApi({ problemId: record.problemId, isShow: check });
    } else if (type == 'del') {
      res = await getSpreaderProblemDelApi({ problemId: record.problemId });
    } else if (type == 'add') {
      userStore.setDelKeepAlive(['SpreaderProblemAdd'])
      router.push({
        path: `/marketing_spreader/problem_list_to_add`,
      });
      return;
    } else if (type == 'edit') {
      userStore.setDelKeepAlive(['SpreaderProblemEdit'])
      router.push({
        path: `/marketing_spreader/problem_list_to_edit`,
        query: {
          id: record.problemId,
        },
      });
      return;
    }
    if (res.state == 200) {
      sucTip(res.msg);
      reload();
    } else {
      failTip(res.msg);
    }
  };

  //获取分类列表
  const get_category_list = async () => {
    try {
      const { updateSchema } = getForm();
      let res = await getSpreaderCategoryListApi({ pageSize: list_com_page_more });
      if (res.state == 200) {
        await updateSchema({
          field: 'categoryId',
          componentProps: {
            options: unref(res.data.list).map((item) => ({
              value: item.categoryId,
              label: item.categoryName,
            })),
          },
        });
      }
    } catch (error) {}
  };

  onMounted(() => {
    get_category_list();
  });
</script>
<style lang="less" scoped></style>
