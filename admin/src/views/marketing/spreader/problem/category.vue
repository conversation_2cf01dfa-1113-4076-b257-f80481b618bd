<template>
  <div class="problem_category">
    <div>
      <BasicTable @register="standardTable" style="padding: 0">
        <template #tableTitle>
          <div class="toolbar">
            <div class="toolbar_btn" @click="handleClick(null, 'add', null)">
              <AliSvgIcon iconName="iconxinzeng" width="15px" height="15px" fillColor="#fff" />
              <span>新增问题分类</span>
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record, text }">
          <template v-if="column.key == 'action'">
            <template v-if="record.isInner == 1"> -- </template>
            <template v-else>
              <TableAction
                :actions="[
                  {
                    label: '编辑',
                    onClick: handleClick.bind(null, record, 'edit'),
                  },
                  {
                    label: '删除',
                    popConfirm: {
                      title: '删除后不可恢复，是否确定删除？',
                      placement: 'left',
                      confirm: handleClick.bind(null, record, 'del'),
                    },
                  },
                ]"
              />
            </template>
          </template>
        </template>
      </BasicTable>
    </div>
    <SldModal
      :width="width"
      :title="title"
      :visible="visible"
      :content="content"
      :confirmBtnLoading="confirmBtnLoading"
      :showFoot="true"
      @cancle-event="handleCancle"
      @confirm-event="handleConfirm"
    />
  </div>
</template>
<script setup>
  import { ref } from 'vue';
  import SldComHeader from '/@/components/SldComHeader/index.vue';
  import SldModal from '@/components/SldModal/index.vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import {
    getSpreaderCategoryListApi,
    getSpreaderCategoryAddApi,
    getSpreaderCategoryUpdateApi,
    getSpreaderCategoryDelApi,
  } from '/@/api/spreader/problem';
  import { validatorEmoji, validatorSpecialString } from '/@/utils/validate';
  import { failTip, sucTip } from '/@/utils/utils';

  const [standardTable, { reload }] = useTable({
    rowKey: 'categoryId',
    api: (arg) => getSpreaderCategoryListApi({ ...arg }),
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    columns: [
      {
        title: '分类名称',
        dataIndex: 'categoryName',
        width: 120,
      },
      {
        title: '排序',
        dataIndex: 'sort',
        width: 100,
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
        width: 150,
      },
    ],
    actionColumn: {
      width: 80,
      title: '操作',
      dataIndex: 'action',
    },
    useSearchForm: true,
    formConfig: {
      schemas: [
        {
          field: 'categoryName',
          component: 'Input',
          colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
          componentProps: {
            minWidth: 300,
            placeholder: '请输入分类名称',
            size: 'default',
          },
          label: '分类名称',
          labelWidth: 70,
          rules: [
            {
              // @ts-ignore
              validator: async (rule, value) => {
                await validatorEmoji(rule, value);
              },
              trigger: 'change',
            },
          ],
        },
      ],
    },
    bordered: true,
    striped: false,
    showIndexColumn: true,
  });
  const width = ref(500);
  const title = ref('');
  const visible = ref(false);
  const content = ref([]);
  const confirmBtnLoading = ref(false);
  const operate_id = ref('');
  const operate_type = ref('');

  const categoryId = ref('');

  const operate_add_edit = ref([
    {
      type: 'input',
      label: `分类名称`,
      name: 'categoryName',
      placeholder: `请输入分类名称`,
      extra: '最多6个字',
      maxLength: 6,
      showCount: true,
      rules: [
        {
          required: true,
          whitespace: true,
          message: '请输入分类名称',
        },
        {
          validator: async (rule, value) => {
            await validatorEmoji(rule, value);
          },
        },
        {
          validator: async (rule, value) => {
            await validatorSpecialString(rule, value);
          },
        },
      ],
    },
    {
      type: 'inputnum',
      label: `排序`,
      name: 'sort',
      placeholder: `请输入排序`,
      extra: '数字越小，显示越靠前，0～255',
      min: 0,
      max: 255,
      rules: [
        {
          required: true,
          message: `请输入排序`,
        },
      ],
    },
  ]); //modal框的数据

  //表格点击回调事件
  function handleClick(item, type, check) {
    operate_type.value = type;
    if (item) {
      operate_id.value = item.categoryId;
    }
    if (type == 'add' || type == 'edit') {
      content.value.forEach((it) => {
        it.initialValue = '';
      });
      content.value = [];
      operate_add_edit.value.forEach((it) => {
        it.initialValue = '';
      });
    }
    if (type == 'add') {
      content.value = [];
      title.value = '添加问题分类';
      visible.value = true;
      content.value = operate_add_edit.value;
    } else if (type == 'edit') {
      title.value = '编辑问题分类';
      visible.value = true;
      let data = operate_add_edit.value;
      data.map((items) => {
        items.initialValue = item[items.name]||item[items.name]==0 ? item[items.name] : undefined;
      });
      content.value = data;
      categoryId.value = item.categoryId;
    } else if (type == 'del') {
      operate_role({ categoryId: item.categoryId });
    } else if (type == 'switch') {
      operate_role({ categoryId: item.categoryId, isShow: check });
    }
  }

  //弹窗取消事件
  function handleCancle() {
    visible.value = false;
    content.value = [];
    categoryId.value = '';
  }

  //弹窗确认事件
  function handleConfirm(val) {
    if (operate_type.value == 'edit') {
      val.categoryId = categoryId.value;
    }
    operate_role(val);
  }

  //商品标签操作
  const operate_role = async (params) => {
    confirmBtnLoading.value = true;
    let res = {};
    if (operate_type.value == 'add') {
      res = await getSpreaderCategoryAddApi(params);
    } else if (operate_type.value == 'edit') {
      res = await getSpreaderCategoryUpdateApi(params);
    } else if (operate_type.value == 'del') {
      res = await getSpreaderCategoryDelApi(params);
    }
    confirmBtnLoading.value = false;
    if (res.state == 200) {
      sucTip(res.msg);
      handleCancle();
      reload();
    } else {
      failTip(res.msg);
    }
  };
</script>
<style lang="less" scoped></style>
