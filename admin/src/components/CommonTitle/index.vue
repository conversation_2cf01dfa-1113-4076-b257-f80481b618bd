<template>
  <div class="common_title_bg">
    <span class="title">{{ props.text }}</span>
    <span
      :style="{ color: 'gray', fontSize: '12px', paddingLeft: '10px' }"
      v-if="props.describe.length > 0"
      >{{ props.describe }}</span
    >
  </div>
</template>
<script setup>
  const props = defineProps({
    text: { type: String, default: '标题' }, //标题
    describe: { type: String, default: '' }, //描述
  });
</script>
<style lang="less" scoped>
  .common_title_bg {
    width: 100%;
    height: 36px;
    border-radius: 2px;
    background: #fffaf7;
    line-height: 36px;

    .title {
      padding-left: 10px;
      color: #333;
      font-size: 13px;
    }
  }
</style>
