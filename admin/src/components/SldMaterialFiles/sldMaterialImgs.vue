<template>
  <div class="sld_material_ings">
    <!-- /*新增/编辑对话框-start*/ -->
    <Modal
      title="上传图片"
      :width="590"
      :visible="up_picModal"
      @ok="uploadConfirm"
      @cancel="uploadCancel"
    >
      <div
        class="flex_column_center_center"
        style="height: 200px; line-height: 30px"
        v-if="disableUpload"
      >
        <div style="width: 330px; margin: 0 auto">素材中心容量已用尽，无法继续上传图片或视频。</div>
        <div style="width: 386; margin: 0 auto">您可以：</div>
        <div style="width: 330; margin: 0 auto">- 手动删除“未引用”的素材。</div>
        <div style="width: 330; margin: 0 auto">- 联系平台运营人员寻求帮助。</div>
      </div>
      <div style="padding: 10px 20px" v-else>
        <div class="flex_row_start_start">
          <div style="flex-shrink: 0; width: 100px; margin-top: 4px">上传分类</div>
          <FormItem style="width: 450px">
            <TreeSelect
              :style="{ width: '450px' }"
              :default-value="uploadCategory"
              showSearch
              allowClear
              placeholder="请选择分类"
              :treeData="treeData"
              @select="onSelect"
              :dropdownStyle="{ maxHeight: '300px' }"
              treeNodeFilterProp="title"
            />
          </FormItem>
        </div>
        <div class="flex_row_start_start upload_list_btn">
          <div style="flex-shrink: 0; width: 100px; margin-top: 4px">本地上传</div>
          <FormItem style="width: 450px" extra="支持.gif，.jpeg, .png，.jpg，.bmp，.tif格式图片。">
            <Upload
              :withCredentials="true"
               @click="beforeUploadClick"
              :beforeUpload="
                (file, fileList) =>
                  sldBeforeUpload(file, fileList, undefined, '.gif, .jpeg, .png, .jpg, .bmp, .tif')
              "
              accept=".gif, .jpeg, .png, .jpg, .bmp, .tif"
              name="file"
              :action="apiUrl + `/v3/oss/admin/upload?source=goods`"
              listType="picture-card"
              :fileList="uploadFileList"
              @preview="viewImg"
              @change="uploadChange"
              :headers="{
                Authorization: imgToken,
              }"
            >
              <div v-if="isWithinMax">
                <PlusOutlined />
                <div class="ant-upload-text primary_color">上传图片</div>
              </div>
            </Upload>
          </FormItem>
        </div>
      </div>
      <template #footer>
        <template v-if="disableUpload">
          <Button key="submit" type="primary" @click="uploadCancel"> 确定 </Button>
        </template>
        <template v-else>
          <Button key="back" @click="uploadCancel">返回图片空间</Button>,
          <Button key="submit" type="primary" :loading="submiting" @click="uploadConfirm">
            确定
          </Button>
        </template>
      </template>
    </Modal>
    <!-- /*新增/编辑对话框-end*/ -->
    <!-- 图片空间start -->
    <Modal
      :destroy-on-close="true"
      :title="title"
      width="870px"
      :visible="materialModal"
      @cancel="materialCancel"
      @ok="materialConfirm"
    >
      <div class="common_page" style="flex: 1">
        <Spin :spinning="initLoading">
          <div class="flex_row_start_start import_store_goods">
            <div class="flex_column_between_start left_part" style="min-height: 500px">
              <div style="width: 100%">
                <p class="title flex_row_start_center">素材类目</p>
                <div class="flex_row_between_center search_box">
                  <Input
                    v-model:value="search_category_name"
                    placeholder="搜索类目名称"
                    allowClear
                  />
                  <span @click="get_category_list('search')" class="search_cate">搜索</span>
                </div>
                <div>
                  <div class="goods_cat">
                    <div v-for="(item, index) in categoryList" :key="item.categoryId">
                      <div
                        class="category_item flex_row_start_center category_item_none"
                        :class="curSelCatId == item.categoryId ? 'selected_cat_item' : ''"
                        @click="changeCategoryShow(1, index)"
                      >
                        <div>
                          <div
                            @click.stop="selectCategoryShow(index)"
                            v-if="item.subCategoryList.length"
                          >
                            <Icon
                              :icon="item.showFlag ? 'mi:caret-down' : 'mi:caret-right'"
                              size="14"
                            />
                          </div>
                          <span style="display: inline-block; width: 14px" v-else></span>
                        </div>
                        <div class="cat_name margin_left" title="item.categoryName">{{
                          // @ts-ignore
                          item.categoryName
                        }}</div>
                        <div class="category_item_num">{{ item.fileCount }}</div>
                      </div>
                      <div class="category_item_child" v-show="item.showFlag">
                        <div
                          v-for="(items, indexs) in item.subCategoryList"
                          :key="items.categoryId"
                          class="category_item flex_row_between_center"
                          :class="curSelCatId == items.categoryId ? 'selected_cat_item' : ''"
                          @click="changeCategoryShow(2, index, indexs)"
                        >
                          <div class="cat_name margin_left" :title="items.categoryName">{{
                            items.categoryName
                          }}</div>
                          <div class="category_item_num">{{ items.fileCount }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="right_goods flex_column_start_start">
              <div class="flex_row_start_center" style="width: 100%">
                <Button class="" type="primary" @click="operateImg(null, 'add')" size="small">
                  <div class="flex_row_center_center">
                    <AliSvgIcon
                      iconName="iconxinzeng"
                      fillColor="#fff"
                      width="15px"
                      height="15px"
                      :extra="{ filter: 'grayscale(100%) brightness(200%)' }"
                    ></AliSvgIcon>
                    <div class="ml-1">上传图片</div>
                  </div>
                </Button>
                <div class="ml-5 tableListForm">
                  <Form class="flex flex-row">
                    <FormItem label="图片名称" class="!mb-0">
                      <Input v-model:value="pic_name" placeholder="请输入图片名称"></Input>
                    </FormItem>
                    <FormItem class="!mb-0 !ml-10">
                      <Button type="primary" @click="search">搜索 </Button>
                      <Button @click="seaReset" class="ml-2">重置</Button>
                    </FormItem>
                  </Form>
                </div>
              </div>
              <div class="mt-3 flex_column_start_start" style="width: 100%">
                <div style="margin-bottom: 10; font-size: 13px; font-weight: 400">
                  已选 {{ selectRowData.ids.length }} 张，最多可选
                  {{ maxUploadNum != undefined ? maxUploadNum : 6 }} 张。
                </div>

                <div v-if="material_list.data.length > 0" style="width: 100%">
                  <div
                    style="height: 400px; overflow: auto"
                    class="flex_row_start_start right_goods_item"
                  >
                    <div
                      v-for="(item, index) in material_list.data"
                      :key="item.bindId"
                      class="flex_column_start_start item"
                      :class="item.checked ? 'active' : null"
                      :style="{ width: `${goods_item_width}px`, marginBottom: '10px' }"
                    >
                      <div
                        class="flex_row_center_center img_wrap"
                        @click="
                          // @ts-ignore
                          viewImg({ url: item.fileUrl })
                        "
                        :style="{
                          width: `${goods_item_width - 2}px`,
                          height: `${goods_item_width - 2}px`,
                          // @ts-ignore
                          // @ts-ignore
                          backgroundImage: 'url(' + item.fileUrl + ')',
                        }"
                      >
                        <img
                          :src="item.checked ? files_checked : files_check"
                          class="checked"
                          @click.stop="selectFiles(index)"
                        />
                        <div class="file_size">{{ item.width }}*{{ item.height }}</div>
                      </div>
                      <p
                        :title="item.fileName"
                        class="file_name"
                        :style="{ width: `${goods_item_width}px` }"
                        >{{ item.fileName }}</p
                      >
                    </div>
                  </div>

                  <div
                    class="flex_row_end_center"
                    v-if="material_list.pagination && material_list.pagination.total > 0"
                  >
                    <Pagination
                      showQuickJumper
                      showSizeChanger
                      size="small"
                      :current="material_list.pagination.current"
                      :total="material_list.pagination.total"
                      :pageSize="material_list.pagination.pageSize"
                      @change="onChangePage"
                      @showSizeChange="onShowSizeChange"
                    />
                  </div>
                </div>

                <div class="flex_row_center_center" style="width: 100%; height: 400px" v-else>
                  <Empty imageStyle="height: 80px" description="暂无数据~"> </Empty>
                </div>
              </div>
            </div>
          </div>
        </Spin>
      </div>
    </Modal>
  </div>
</template>

<script setup>
  // @ts-ignore
  import { computed, onMounted, reactive, ref, unref, watch } from 'vue';
  import {
    Modal,
    Button,
    FormItem,
    Upload,
    TreeSelect,
    Form,
    Input,
    Pagination,
    Empty,
    Spin,
  } from 'ant-design-vue';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import { sucTip, failTip, sldBeforeUpload } from '@/utils/utils';
  import { useUserStore } from '@/store/modules/user';
  import { useGlobSetting } from '/@/hooks/setting';
  import {
    get_material_center_category,
    getMaterialListApi,
    uploadMaterialFiles,
  } from '/@/api/material';
  import Icon from '@/components/Icon/Icon.vue';
  import { getImagePath, resolveImageBindId } from '/@/utils';
  import lodash from 'lodash-es';
  const userStore = useUserStore();
  const props = defineProps({
    visibleModal: {
      type: Boolean,
      default: false,
    },
    allowRepeat: {
      type: Boolean,
      default: true,
    },
    maxUploadNum: {
      type: Number,
      default: 6,
    },
    selectedData: {
      type: Object,
      default: () => {},
    },
    title: {
      type: String,
      default: '图片空间',
    },
  });

  const emit = defineEmits(['confirmMaterial', 'closeMaterial']);

  const imgToken = ref('Bearer ' + userStore.getToken || ''); //上传文件token参数

  const modalTitle = ref('');
  const submiting = ref(false);
  const up_picModal = ref(false);
  const materialModal = ref(false);
  // @ts-ignore
  const content = ref([]);
  const disableUpload = ref(false);
  const filesLength = ref(0);
  const uploadFileList = ref([]);
  const { apiUrl } = useGlobSetting();
  const treeData = ref([]);
  // @ts-ignore
  const search_category_name = ref('');
  const initLoading = ref(false);
  const categoryList = ref([]);
  const curSelCatId = ref(0);
  const pageCurrent = ref(1);
  const sldPageSize = ref(10);
  const goods_item_width = 140; //每个商品容器宽度

  const pic_name = ref(''); //搜索字段
  const uploadCategory = ref(0);

  const material_list = reactive({
    data: [],
    pagination: {},
  });

  //img
  const files_check = getImagePath('images/files_check.png');
  const files_checked = getImagePath('images/files_checked.png');
  const selectRowData = reactive({
    ids: [],
    data: [],
  });

  //是否超过最大上传数
  const isWithinMax = computed(() => {
    if (props.maxUploadNum) {
      return props.maxUploadNum - filesLength.value - uploadFileList.value.length > 0;
    } else {
      return uploadFileList.value.length >= 100;
    }
  });

  const markSelection = () => {
    if (!lodash.isEmpty(props.selectedData)) {
      selectRowData.ids = props.selectedData.ids;
      selectRowData.data = props.selectedData.data;
    }
    if (props.allowRepeat) {
      material_list.data.map((item) => {
        item.checked = false;
      });
      selectRowData.ids = [];
      selectRowData.data = [];
      filesLength.value = selectRowData.ids.length;
    } else {
      material_list.data.map((item) => {
        item.checked =
          selectRowData.ids.length == 0 || selectRowData.ids.indexOf(item.bindId) == -1
            ? false
            : true;
      });
    }
  };

  watch(
    () => props.visibleModal,
    () => {
      imgToken.value = 'Bearer ' + userStore.getToken
      materialModal.value = props.visibleModal;
    },
  );

  watch(materialModal, () => {
    if (materialModal.value) {
      sldPageSize.value = 10
      get_category_list();
    }
  });

  const viewImg = () => {};

  //上传图片回调
  const uploadChange = (info) => {
    if (info.file.status != undefined && info.file.status != 'error') {
      if (info.file.response && info.file.response.state != 200 && info.fileList.length > 0) {
        failTip(info.file.response.msg || '上传失败');
        info.fileList = info.fileList.filter((item) => item.response.state == 200);
      }
      uploadFileList.value = info.fileList;
    }
  };

  //上传图片弹框点击返回图片空间或者关闭弹框的回调
  const uploadCancel = () => {
    if (unref(disableUpload)) {
      up_picModal.value = false;
    } else {
      up_picModal.value = false;
      materialModal.value = true;
    }
  };

  //上传图片弹框点击确认回调
  const uploadConfirm = async () => {
    if (unref(uploadFileList).length == 0) {
      failTip('请上传图片');
      return;
    }
    let fileIds = unref(uploadFileList)
      .map((item) => item.response?.data?.fileId)
      .toString();
    const res = await uploadMaterialFiles({
      categoryId: unref(uploadCategory),
      fileIds,
    });

    if (res.state == 200) {
      sucTip(res.msg);
      up_picModal.value = false;
      material_list_query();
      get_category_list('update');
      selectRowData.ids = [];
      selectRowData.data = [];

      res.data.forEach((item, index) => {
        selectRowData.ids.push(resolveImageBindId(item.filePath));
        // @ts-ignore
        selectRowData.data.push({
          bindId: resolveImageBindId(item.filePath),
          fileType: 1,
          filePath: item.filePath,
          fileUrl: item.fileUrl,
          width: item.width,
          height: item.height,
        });
      });
      emit('confirmMaterial', selectRowData);
    } else {
      failTip(res.msg);
    }
  };

  //获取素材分类
  const get_category_list = async (when = 'init') => {
    const result = await get_material_center_category({
      type: 1, //素材类型 1：图片2：视频
      showFileCount: 1, //	是否展示文件数量 1：展示 2：不展示
      name: unref(search_category_name),
    });

    if (result?.state == 200) {
      if (result.data?.length) {
        const { treeSelect, reformData } = formatTreeSData(result.data, []);
        if (when == 'init') {
          // @ts-ignore
          treeData.value = treeSelect;

          uploadCategory.value = treeData.value[0].value;
        }
        curSelCatId.value = reformData[0].categoryId;
        categoryList.value = reformData;
        material_list_query();
      }
    } else {
      failTip(result?.msg || '请求错误');
    }
  };

  //格式化数据最后赋给树形数据用作 树形选择框展示
  const formatTreeSData = (cate_data, target = []) => {
    cate_data.map((item) => {
      let data = {
        key: item.categoryId,
        title: item.categoryName,
        value: item.categoryId,
        children: [],
      };
      if (item.subCategoryList?.length) {
        item.showFlag = false;
        formatTreeSData(item.subCategoryList, data.children);
      }
      target.push(data);
    });
    return {
      treeSelect: target,
      reformData: cate_data,
    };
  };

  const beforeUploadClick = async()=> {
    imgToken.value = 'Bearer ' + userStore.getToken
    if(userStore.getToken){
      let res = await userStore.getSldCommonService()
      if(res.state == 200){
        imgToken.value = 'Bearer ' + res.token
      }
    }
  }

  //图片操作
  const operateImg = (item, type, index) => {
    if (unref(disableUpload)) {
      up_picModal.value = true;
      materialModal.value = false;
      modalTitle.value = '提示';
    } else {
      up_picModal.value = true;
      materialModal.value = false;
      modalTitle.value = '上传图片';
      uploadFileList.value = [];
    }
  };

  // @ts-ignore
  const onSelect = (val) => {
    uploadCategory.value = val;
  };

  const setUploadModal = (bool) => {
    up_picModal.value = bool;
  };

  //图片空间方法start------
  const setMaterialModal = (bool, selectedData) => {
    if (selectedData) {
      selectRowData.ids = selectedData.ids;
      selectRowData.data = selectedData.data;
    }
    materialModal.value = bool;
  };

  //素材分类切换展示隐藏
  const selectCategoryShow = (index) => {
    if (categoryList.value[index].showFlag != undefined) {
      categoryList.value[index].showFlag = !categoryList.value[index].showFlag;
    }
  };

  //选择素材分类
  const changeCategoryShow = (step, index, indexs) => {
    if (step == 1) {
      // @ts-ignore
      curSelCatId.value = unref(categoryList)[index].categoryId;
    } else {
      // @ts-ignore
      curSelCatId.value = unref(categoryList)[index].subCategoryList[indexs].categoryId;
    }

    material_list_query();
  };

  const material_list_query = () => {
    let query = {
      current: unref(pageCurrent),
    };
    !!unref(curSelCatId) && (query.categoryId = unref(curSelCatId));
    !!unref(pic_name) && (query.fileName = unref(pic_name));
    get_material_list(query);
  };

  //获取素材
  const get_material_list = async (param) => {
    let params = {
      pageSize: sldPageSize.value,
      ...param,
    };
    initLoading.value = true;
    const result = await getMaterialListApi({ ...params, fileType: 1 }).catch(console.log);
    if (result?.state == 200) {
      material_list.data = result.data.list;
      material_list.pagination = result.data.pagination;
      initLoading.value = false;
      markSelection();
    } else {
      failTip(result?.msg || '请求错误');
    }
  };

  const search = () => {
    pageCurrent.value = 1;
    material_list_query();
  };

  const seaReset = () => {
    pic_name.value = '';
    pageCurrent.value = 1;
    material_list_query();
  };

  const onChangePage = (e,pageSize) => {
    pageCurrent.value = e;
    sldPageSize.value = pageSize
    material_list_query();
  };

  const onShowSizeChange = (current, size) => {
    pageCurrent.value = current;
    sldPageSize.value = size
    material_list_query();
  };

  //选择文件
  const selectFiles = (index) => {
    let { maxUploadNum } = props;
    let item = material_list.data[index];
    if (maxUploadNum == undefined) {
      maxUploadNum = 6;
    }
    if (!item.checked && selectRowData.ids.length >= maxUploadNum) {
      failTip('最多可选' + maxUploadNum + '张');
      return;
    }
    item.checked = !item.checked;
    let itemIndex = selectRowData.ids.findIndex((v) => Object.is(Number(v), Number(item.bindId)));
    if (itemIndex == -1 && item.checked) {
      //添加
      selectRowData.ids.push(item.bindId);
      selectRowData.data.push(item);
    } else if (itemIndex >= 0 && !item.checked) {
      //删除
      selectRowData.ids.splice(itemIndex, 1);
      selectRowData.data.splice(itemIndex, 1);
    }
  };

  const materialCancel = () => {
    materialModal.value = false;
    emit('closeMaterial');
  };

  const materialConfirm = () => {
    emit('confirmMaterial', selectRowData);
    materialModal.value = false;
  };

  defineExpose({ setUploadModal, setMaterialModal });

  onMounted(() => {});
</script>

<style lang="less">
  .import_store_goods {
    .left_part {
      position: relative;
      width: 220px;
      margin-right: 10px;
      border: 1px solid #ebedf0;
      border-radius: 2px;
      background: #fff;

      .title {
        width: 100%;
        height: 49px;
        padding-left: 20px;
        border-bottom: 1px solid #d8d8d8;
        color: #323233;
        font-family: PingFangSC-Medium, 'PingFang SC';
        font-size: 14px;
        font-weight: 500;
      }

      .search_box {
        width: 100%;
        padding: 10px;

        .search_cate {
          display: inline-block;
          margin-left: 10px;
          color: #323233;
          font-family: PingFangSC-Regular, 'PingFang SC';
          font-size: 14px;
          font-weight: 400;
          white-space: nowrap;
          cursor: pointer;
        }
      }

      .goods_cat {
        width: 100%;

        .cat_item,
        .category_item {
          position: relative;
          height: 44px;
          padding-left: 20px;
          cursor: pointer;

          &.category_item {
            height: unset;
            padding-top: 10px;
            padding-bottom: 10px;
            padding-left: 10px;
          }

          &.category_item_none {
            .category_item_btns {
              display: none;
            }
          }

          &.category_item_hover {
            .category_item_btns {
              display: none;
              flex: 1;
              padding-right: 10px;
              text-align: right;

              .category_item_btns_item {
                margin-left: 6px;

                &:hover {
                  color: #ff701e;
                }
              }
            }

            .category_item_num {
              display: block;
              flex: 1;
              padding-right: 10px;
              text-align: right;
            }

            &:hover {
              .category_item_btns {
                display: flex;
              }

              .category_item_num {
                display: none;
              }
            }
          }

          &:hover {
            .cat_name {
              color: @primary-color !important;
            }

            .to_right_icon svg {
              fill: @primary-color !important;
            }
          }

          .cat_name {
            color: #323233;
            font-family: PingFangSC-Regular, 'PingFang SC';
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;

            &.margin_left {
              margin-left: 6px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }

          .to_right_icon {
            display: flex;
            padding-right: 76px;
          }
        }

        .category_item_num {
          display: block;
          flex: 1;
          padding-right: 10px;
          text-align: right;
        }

        .category_item_child {
          .category_item {
            .cat_name {
              &.margin_left {
                margin-left: 28px;
              }
            }
          }
        }

        .selected_cat_item {
          background: #f9f9f9;

          .cat_name {
            color: @primary-color;
          }
        }
      }

      .more_cat {
        position: absolute;
        z-index: 999;
        top: 48px;
        left: 204px;
        width: 800px;
        padding: 25px;
        padding-left: 15px;
        zoom: 1;
        border: 1px solid #dfdfdf;
        background: #f9f9f9;

        .item {
          .second_cat {
            flex-shrink: 0;
            width: 108px;
            cursor: pointer;

            &:hover {
              .cat_name {
                color: #ff701e !important;
              }

              .to_right_icon svg {
                fill: #ff701e !important;
              }
            }

            .cat_name {
              height: 20px;
              color: #4c4c4c;
              font-family: PingFangSC-Semibold, 'PingFang SC';
              font-size: 14px;
              font-weight: 600;
              line-height: 20px;
              white-space: nowrap;
            }

            .to_right_icon {
              display: flex;
              margin-left: 6px;
            }
          }

          .third_cat {
            flex-wrap: wrap;
            margin-left: 20px;

            .item {
              height: 20px;
              margin-right: 20px;
              margin-bottom: 15px;
              color: #999;
              font-family: PingFangSC-Regular, 'PingFang SC';
              font-size: 14px;
              font-weight: 400;
              line-height: 20px;

              &:hover {
                color: #ff701e !important;
              }
            }
          }
        }
      }
    }

    .right_goods {
      display: flex;
      flex: 1;

      .operate_bg {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-start;
        width: 100%;
        height: 40px;
        margin-bottom: 10px;
        background: #f2f2f2;

        :global {
          .ant-input-search > .ant-input-suffix > .ant-input-search-button {
            height: 29px !important;
          }

          .ant-input-search.ant-input-search-enter-button > .ant-input {
            height: 29px !important;
          }
        }
      }

      .operate_wrap {
        width: 100%;
        height: 50px;
        margin-bottom: -1px;
        border: 1px solid #ebebeb;
        background: #fff;

        .btn {
          height: 28px;
          margin-left: 20px;
          padding: 0 16px;
          border: 1px solid #d9d9d9;
          border-radius: 4px;
          background: #fff;
          color: #595959;
          font-family: PingFangSC-Regular, 'PingFang SC';
          font-size: 13px;
          font-weight: 400;
          line-height: 26px;

          &:hover {
            border-color: #ff701e;
            color: #ff701e;
          }
        }

        .sel_goods_num_tip {
          color: #999;
          font-family: PingFangSC-Regular, 'PingFang SC';
          font-size: 13px;
          font-weight: 400;
        }

        .sel_goods_num {
          margin-right: 2px;
          margin-left: 2px;
          color: #ff6a12;
          font-weight: bold;
        }
      }

      .right_goods_item {
        flex-wrap: wrap;
        margin-top: 10px;

        .item {
          position: relative;
          flex-shrink: 0;
          margin-right: 10px;
          margin-bottom: 15px;
          overflow: hidden;

          &:nth-child(4n) {
            margin-right: 0;
          }

          &.active {
            .img_wrap {
              border-color: @primary-color;
            }
          }

          .img_wrap {
            position: relative;
            overflow: hidden;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            background-repeat: no-repeat;
            background-position: center;
            background-size: contain;

            .checked {
              position: absolute;
              z-index: 9;
              top: 0;
              right: 0;
              width: 22px;
              height: 22px;
              cursor: pointer;
            }

            .file_state {
              position: absolute;
              z-index: 9;
              top: 0;
              left: 0;
              height: 22px;
              line-height: 22px;
              cursor: default;

              text {
                padding-left: 4px;

                &.file_state_on {
                  color: #fff;
                }

                &.file_state_off {
                  color: #000000a6;
                }
              }

              img {
                position: absolute;
                z-index: -1;
                top: 0;
                left: 0;
                width: 58px;
                height: 22px;
              }
            }

            .file_size {
              position: absolute;
              bottom: 0;
              width: 100%;
              height: 26px;
              background: #00000085;
              color: #fff;
              line-height: 26px;
              text-align: center;
              cursor: default;
            }
          }

          .file_name {
            display: -webkit-box;
            height: 34px;
            margin-top: 5px;
            margin-bottom: 5px;
            padding-right: 6px;
            padding-left: 6px;
            overflow: hidden;
            font-family: PingFangSC-Regular, 'PingFang SC';
            font-size: 13px;
            font-weight: 400;
            line-height: 18px;
            text-overflow: ellipsis;
            word-break: break-word;
            -webkit-line-clamp: 2;

            /* autoprefixer: off */
            -webkit-box-orient: vertical;
          }

          .item_btns {
            flex-wrap: wrap;
            padding-right: 6px;
            padding-left: 6px;
            color: #ff701e;
            font-family: PingFangSC-Regular, 'PingFang SC';
            font-size: 13px;
            font-weight: 400;
            white-space: nowrap;

            span {
              margin-right: 6px;
              cursor: pointer;
            }
          }
        }
      }

      .right_goods,
      .right_goods_grid {
        flex: 1;
        flex-wrap: wrap;

        .item {
          position: relative;
          margin-bottom: 10px;
          margin-left: 10px;
          padding-bottom: 10px;
          border: 1px solid #eaeaea;
          border-radius: 2px;
          background: #fff;
          cursor: pointer;

          &:hover,
          &.active {
            border-color: #ff9147;
          }

          .checked {
            position: absolute;
            z-index: 99;
            top: -1px;
            right: -1px;
            width: 18px;
            height: 18px;
          }

          .virtual_goods_flag {
            position: absolute;
            top: -1px;
            left: -1px;
            padding: 0 3px;
            border-radius: 3px 0;
            background: linear-gradient(90deg, #f7d47e 0%, #e6b845 100%);
            color: #fff;
            font-size: 12px;
          }

          .img_wrap {
            border-radius: 2px 2px 0 0;
            background: #f8f8f8;

            img {
              max-width: 100%;
              max-height: 100%;
            }
          }

          .goods_name {
            display: -webkit-box;
            height: 40px;
            margin-top: 10px;
            padding: 0 10px;
            overflow: hidden;
            color: #333;
            font-family: PingFangSC-Regular, 'PingFang SC';
            font-size: 13px;
            font-weight: 400;
            line-height: 20px;
            text-overflow: ellipsis;
            word-break: break-word;
            -webkit-line-clamp: 2;

            /*! autoprefixer: off */
            -webkit-box-orient: vertical;
          }

          .price {
            margin: 5px 10px 10px;
            color: #fc1c00;
            font-family: PingFangSC-Semibold, 'PingFang SC';
            font-size: 16px;
            font-weight: 600;
            line-height: 22px;

            .price_title {
              margin-right: 5px;
              color: #aaa;
              font-size: 13px;
              font-weight: 400;
              white-space: nowrap;
            }
          }

          .supplier_price {
            margin: 5px 10px 0;
            color: #fc1c00;
            font-family: PingFangSC-Semibold, 'PingFang SC';
            font-size: 13px;
            font-weight: 600;
            line-height: 22px;

            .supplier_title,
            .price_title {
              margin-right: 5px;
              color: #aaa;
              font-size: 12px;
              font-weight: 400;
              white-space: nowrap;
            }

            .supplier_title {
              margin-left: 6px;
            }
          }
        }
      }
    }
  }
</style>
