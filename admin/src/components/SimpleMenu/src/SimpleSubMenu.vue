<template>
  <MenuItem
    :name="item.path"
    v-if="!menuHasChildren(item) && getShowMenu"
    v-bind="$props"
    :class="getLevelClass"
    style="position: relative;"
  >
    <img
      v-if="extra.indexOf(item.path)!=-1&&specialFlag > -3"
      src="@/assets/images/must_add_left.png"
      alt=""
      style="width: 26px; height: 14px; margin-top: -1px; margin-left: 0;position: absolute;left: 12px;"
    />
    <Icon v-if="getIcon" :icon="getIcon" :size="16" />
    <div v-if="collapsedShowTitle && getIsCollapseParent" class="mt-1 collapse-title">
      {{ getI18nName }}
    </div>
    <template #title>
      <span :class="['ml-2', `${prefixCls}-sub-title`]">
        {{ getI18nName }}
      </span>
      <SimpleMenuTag :item="item" :collapseParent="getIsCollapseParent" />
    </template>
  </MenuItem>
  <SubMenu
    :name="item.path"
    v-if="menuHasChildren(item) && getShowMenu"
    :class="[getLevelClass, theme]"
    :collapsedShowTitle="collapsedShowTitle"
  >
    <template #title>
      <img
        src="@/assets/images/must_add_left.png"
        alt=""
        v-if="extra.indexOf(item.path)!=-1&&specialFlag > -3"
        style="width: 26px; height: 14px; margin-top: -1px; margin-left: 0"
      />
      <SvgIcon
        :iconName="getIcon"
        v-else-if="getIcon"
        width="17px"
        height="17px"
        fillColor="rgba(255, 255, 255, 0.7)"
        style="margin-bottom: 1px;"
      ></SvgIcon>
      <!-- <Icon v-else-if="getIcon" :icon="getIcon" :size="16" /> -->
      <div v-if="collapsedShowTitle && getIsCollapseParent" class="mt-1 collapse-title">
        {{ getI18nName }}
      </div>

      <span v-show="getShowSubTitle" :class="['ml-2', `${prefixCls}-sub-title`,`${(item.path == '/marketing_live' || item.path == '/marketing_spreader' || item.path == '/decorate_o2o')&&specialFlag > -3?'must_add_left_pos':''}`]">
        {{ getI18nName }}
      </span>
      <SimpleMenuTag :item="item" :collapseParent="!!collapse && !!parent" />
    </template>
    <template
      v-for="childrenItem in item.children || []"
      :key="childrenItem.paramPath || childrenItem.path"
    >
      <SimpleSubMenu v-bind="$props" :item="childrenItem" :parent="false" />
    </template>
  </SubMenu>
</template>
<script lang="ts">
  import type { PropType } from 'vue';
  import type { Menu } from '/@/router/types';
  import { specialFlag } from '/@/utils/utils';
  import { defineComponent, computed,ref } from 'vue';
  import { useDesign } from '/@/hooks/web/useDesign';
  import Icon from '@/components/Icon/Icon.vue';
  import SvgIcon from '@/components/SvgIcon/index.vue';
  import MenuItem from './components/MenuItem.vue';
  import SubMenu from './components/SubMenuItem.vue';
  import { propTypes } from '/@/utils/propTypes';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { createAsyncComponent } from '/@/utils/factory/createAsyncComponent';

  export default defineComponent({
    name: 'SimpleSubMenu',
    components: {
      SvgIcon,
      SubMenu,
      MenuItem,
      SimpleMenuTag: createAsyncComponent(() => import('./SimpleMenuTag.vue')),
      Icon,
    },
    props: {
      item: {
        type: Object as PropType<Menu>,
        default: () => ({}),
      },
      parent: propTypes.bool,
      collapsedShowTitle: propTypes.bool,
      collapse: propTypes.bool,
      theme: propTypes.oneOf(['dark', 'light']),
    },
    setup(props) {
      const { t } = useI18n();
      const { prefixCls } = useDesign('simple-menu');

      const extra = ref(['/manage_goods_platform/LM','/manage_goods_platform/VOP','/manage_goods_platform/setting','/marketing_live','/marketing_spreader','/decorate_o2o','/manage_goods_platform/goods_change'])

      const getShowMenu = computed(() => !props.item?.meta?.hideMenu);
      const getIcon = computed(() => props.item?.icon);
      const getI18nName = computed(() => t(props.item?.name));
      const getShowSubTitle = computed(() => !props.collapse || !props.parent);
      const getIsCollapseParent = computed(() => !!props.collapse && !!props.parent);
      const getLevelClass = computed(() => {
        return [
          {
            [`${prefixCls}__parent`]: props.parent,
            [`${prefixCls}__children`]: !props.parent,
          },
        ];
      });

      function menuHasChildren(menuTreeItem: Menu): boolean {
        return (
          !menuTreeItem.meta?.hideChildrenInMenu &&
          Reflect.has(menuTreeItem, 'children') &&
          !!menuTreeItem.children &&
          menuTreeItem.children.length > 0
        );
      }

      return {
        prefixCls,
        menuHasChildren,
        getShowMenu,
        getIcon,
        getI18nName,
        getShowSubTitle,
        getLevelClass,
        getIsCollapseParent,
        extra,
        specialFlag
      };
    },
  });
</script>

<style lang="less">
  @menu-prefix-cls: ~'@{namespace}-menu';

  svg {
    transition: all 0.3s;
  }
  .@{menu-prefix-cls}-submenu {
    .@{menu-prefix-cls}{
      display: inline-block;
      background-color: #1C1D21;
    }
  }

  .@{menu-prefix-cls}-submenu-title:hover {
    svg {
      fill: #fff !important;
    }
  }

  .@{menu-prefix-cls}-child-item-active {
    svg {
      fill: #fff !important;
    }
  }
  .must_add_left_pos{
    margin-left: 2px;
  }
</style>
