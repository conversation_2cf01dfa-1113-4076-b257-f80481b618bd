@simple-prefix-cls: ~'@{namespace}-simple-menu';
@prefix-cls: ~'@{namespace}-menu';

.@{prefix-cls} {
  &-dark&-vertical .@{simple-prefix-cls}__parent {
    background-color: @sider-dark-bg-color;
    > .@{prefix-cls}-submenu-title {
      background-color: @sider-dark-bg-color;
    }
  }
  &-dark&-vertical .@{prefix-cls}-submenu{
    >.@{prefix-cls}-submenu-title {
      transition: border-color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1), background 0.3s cubic-bezier(0.645, 0.045, 0.355, 1), padding 0.15s cubic-bezier(0.645, 0.045, 0.355, 1);
        &:hover{
        background-color: @primary-color !important;
      }
    }

  }

  &-dark&-vertical .@{simple-prefix-cls}__children,
  &-dark&-popup .@{simple-prefix-cls}__children {
    background-color: #1C1D21;
    > .@{prefix-cls}-submenu-title {
      background-color: @sider-dark-lighten-bg-color;
    }
  }

  .collapse-title {
    overflow: hidden;
    font-size: 13px;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.@{simple-prefix-cls} {
  &-sub-title {
    overflow: hidden;
    transition: all 0.3s;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &-tag {
    display: inline-block;
    position: absolute;
    top: calc(50% - 8px);
    right: 30px;
    margin-right: 4px;
    padding: 2px 3px;
    border-radius: 2px;
    color: #fff;
    font-size: 10px;
    line-height: 14px;

    &--collapse {
      top: 6px !important;
      right: 2px;
    }

    &--dot {
      top: calc(50% - 2px);
      width: 6px;
      height: 6px;
      padding: 0;
      border-radius: 50%;
    }

    &--primary {
      background-color: @primary-color;
    }

    &--error {
      background-color: @error-color;
    }

    &--success {
      background-color: @success-color;
    }

    &--warn {
      background-color: @warning-color;
    }
  }
}
