<template>
  <div>
    <div class="flex flex-row items-center">
      <span class="mr-2">{{ label }}</span>
      <Tooltip placement="right" :title="tip" v-if="tip">
        <QuestionCircleFilled style="color: #dadada" />
      </Tooltip>
    </div>
    <div class="basic-value" :style="{ fontWeight: isBold ? 'bold' : 'normal' }">
      <CountTo
        :color="'#333'"
        :startVal="0"
        :prefix="isMoney ? '￥' : ''"
        :suffix="isPercent ? '%' : ''"
        :prefixStyle="{ fontSize: '17px' }"
        :endVal="Number(value) || 0"
        :duration="1000"
        :decimals="isMoney || isPercent ? 2 : 0"
        :isMoneyFormat="isMoney"
      />
    </div>
    <div class="flex flex-row items-center mt-3" v-if="isGrowth">
      <div class="text-gray-400">{{ growthText }}</div>
      <div class="flex flex-row items-center ml-2" :class="isGrowthUp ? 'text-green' : 'text-red'">
        <!-- <span>{{ isGrowthUp ? '+' : '-' }}</span> -->
        <span>{{ growthValue }}</span>
        <ArrowUpOutlined class="ml-1" v-if="isGrowthUp" />
        <ArrowDownOutlined class="ml-1" v-else />
      </div>
    </div>
  </div>
</template>

<script>
  import { defineComponent } from 'vue';
  import { Tooltip } from 'ant-design-vue';
  import { QuestionCircleFilled, ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons-vue';
  import { CountTo } from '@/components/CountTo';
  import { getImagePath } from '@/utils';

  export default defineComponent({
    components: {
      Tooltip,
      QuestionCircleFilled,
      CountTo,
      ArrowUpOutlined,
      ArrowDownOutlined,
    },
    props: {
      label: {
        type: String,
        required: true,
      },
      tip: {
        type: String,
      },
      value: {
        type: Number,
        required: true,
      },
      isMoney: {
        type: Boolean,
        default: false,
      },
      isBold: {
        type: Boolean,
        default: false,
      },
      isPercent: {
        type: Boolean,
        default: false,
      },
      isGrowth: {
        type: Boolean,
        default: false,
      },

      growthText: {
        type: String,
      },

      growthValue: {
        type: String,
      },

      isGrowthUp: {
        type: Boolean,
        default: true,
      },
    },
    setup() {
      const banner = getImagePath('images/today_info_bg.png');

      return { banner };
    },
  });
</script>

<style scoped lang="less">
  .position-title {
    position: absolute;
    z-index: 2;
    top: 8px;
    left: -5px;
    width: 163px;
    height: 37px;
    padding-top: 11px;
    padding-left: 16px;
    background-size: 100%;
    color: #fff;
    font-size: 15px;
  }

  .basic-value {
    margin-top: 13px;
    color: #333;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'PingFang SC',
      'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-size: 28px;
    letter-spacing: -2px;
    line-height: 28px;
  }
</style>
