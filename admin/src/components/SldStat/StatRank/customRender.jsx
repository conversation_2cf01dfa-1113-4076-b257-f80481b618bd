export function GoodsCustomRender({ record, text }) {
  return (
    <div className="flex flex-row items-center table_td_center_elliplis">
      <div className="flex_com_row_center" style={{width:'25px'}}><img src={record.goodsImage} alt="img" width={25} height={25} /></div>
      <span title={text}>
        {text}
      </span>
    </div>
  );
}
export function TextCustomRender({ record, text }) {
  return (
    <div className="table_td_center_elliplis">
      <span>{text}</span>
    </div>
  );
}
export function PriceCustomRender({ record, text }) {
  return <span>¥{Number(text).toFixed(2)}</span>;
}
export function TextHiddenCustomRender({ record, text }) {
  return (
    <div title={text} className="truncate ml-2 text-xs ">
      {text}
    </div>
  );
}

export function ImageCustomRender({ record, text }) {
  return (
    <img
      src={record.goodsImage || record.mainImage || record.productImage}
      alt="img"
      width={80}
      height={90}
    />
  );
}
