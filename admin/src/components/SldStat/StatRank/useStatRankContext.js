import { tryOnUnmounted } from '@vueuse/shared';
import { isProdMode } from '/@/utils/env';
import { ref, unref } from 'vue';
export default function useStatRankContext({ apiUrl, paramHandler }) {
  const statcharts = ref();
  const register = (instance) => {
    isProdMode() &&
      tryOnUnmounted(() => {
        statcharts.value = null;
      });
    if (statcharts.value) return;
    statcharts.value = instance;
    setBehavior();
  };
  const execApi = () => {
    unref(statcharts).execApi();
  };
  const setBehavior = () => {
    unref(statcharts).setBehavior({ apiUrl, paramHandler });
  };
  const setApiUrl = (url) => {
    unref(statcharts).setApiUrl(url);
  };
  const setDataSource = (source) => {
    unref(statcharts).setDataSource(source);
  };
  return { register, execApi, setApiUrl, setDataSource };
}
