<template>
  <div class="visualized_item" ref="visualizedRef">
    <Spin :spinning="loading">
      <div
        class="flex p-10px border-b-1 border-[#EEEEEE] items-center flex-row justify-between min-h-44px flex-wrap"
      >
        <div class="flex flex-row items-center" v-if="props.title || $slots.extraSlot">
          <div class="position-title font-bold !text-[14px] mb-4 xl:mb-0 mr-5">{{
            props.title
          }}</div>
          <div v-if="$slots.extraSlot">
            <slot name="extraSlot"></slot>
          </div>
        </div>
        <DynamicPicker @change="submitValue" v-if="props.datePicker" />
      </div>
      <div class="p-10px statRank">
        <BasicTable
          :dataSource="currentDataSource"
          :columns="formColumns"
          bordered
          :pagination="false"
          :showIndexColumn="false"
          size="small"
          class="BasicTable_width"
          rowKey="index"
          @change="tableChange"
        >
          <template #bodyCell="{ column, index }">
            <span v-if="column && column.dataIndex == 'index'">{{ index + 1 }}</span>
          </template>
          <template #emptyText>
            <div class="h-[392px] flex_column_center_center">
              <img src="@/assets/images/table_nodata_icon.png" alt="" width="120" height="78" />
              <span class="mt-[10px]">暂无数据</span>
            </div>
          </template>
        </BasicTable>
      </div>
    </Spin>
  </div>
</template>

<script setup lang="ts">
  import { getCurrentInstance, onMounted, reactive, ref, watch, watchEffect } from 'vue';
  import { defHttp } from '/@/utils/http/axios';
  import DynamicPicker from '../DynaPicker/picker.vue';
  import { Spin, TableColumnType, Table } from 'ant-design-vue';
  import { rankDataOption } from '../setOption/tableOption';
  import { PickerMode, RankOptions } from '../types';
  import { initialDateVar } from '../index';
  import { failTip } from '@/utils/utils';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';

  interface BehaviorType {
    apiUrl?: string;
    paramHandler?: (...arg) => object;
  }
  // dataHandler?: (...arg) => any;

  type RankPropType = {
    title: string;
    options: RankOptions[];
    dataSource?: Array<any>;
    datePicker?: boolean;
    isIndex?: boolean;
    rawSort?: boolean;
  };
  const props = defineProps<RankPropType>();
  const emits = defineEmits(['change', 'register']);
  const currentDataSource = ref(props.dataSource);
  const currentDayType = ref<PickerMode>('d1');
  const formColumns = ref<TableColumnType<any>[]>();
  const currentDateParam = ref(initialDateVar);
  const sortOption = ref({});
  const loading = ref(true);
  const behaviorSet = reactive<BehaviorType>({
    apiUrl: '',
    paramHandler: () => ({}),
  });

  watch(
    () => props.options,
    () => {
      formColumns.value = rankDataOption(
        props.options,
        props.isIndex == undefined ? true : props.isIndex,
      ) as TableColumnType<any>[];
    },
  );

  watchEffect(() => {
    if (props.dataSource) {
      currentDataSource.value = props.dataSource;
      loading.value = false;
    }
  });

  const submitValue = (paramForApi, dayType) => {
    currentDayType.value = dayType;
    currentDateParam.value = paramForApi;
    if (props.dataSource) {
      emitChange();
    } else {
      execApi();
    }
  };

  const tableChange = (...e) => {
    sortOption.value = e[2];
    if (props.dataSource) {
      emitChange();
    } else {
      !props.rawSort && execApi();
    }
  };

  const setBehavior = ({ apiUrl, paramHandler }: BehaviorType) => {
    apiUrl && (behaviorSet.apiUrl = apiUrl);
    paramHandler && (behaviorSet.paramHandler = paramHandler);
    // dataHandler && (behaviorSet.dataHandler = dataHandler);
  };

  const emitChange = () => {
    emits('change', { dateParam: currentDateParam.value, sortParam: sortOption.value });
  };

  const execApi = async () => {
    loading.value = true;
    const params = behaviorSet.paramHandler
      ? behaviorSet.paramHandler(currentDateParam.value, sortOption.value)
      : props.datePicker
      ? currentDateParam.value
      : {};
    const res: any = await defHttp.get<any>({ url: behaviorSet.apiUrl, params });
    if (res?.state == 200) {
      currentDataSource.value = res.data;
      loading.value = false;
    } else {
      failTip(res.msg);
    }
  };

  const setApiUrl = (url) => {
    behaviorSet.apiUrl = url;
  };

  const setDataSource = (source) => {
    currentDataSource.value = source;
  };

  const instance = getCurrentInstance();
  instance && emits('register', { setBehavior, execApi, setApiUrl, setDataSource }, instance.uid);

  onMounted(() => {
    formColumns.value = rankDataOption(
      props.options,
      props.isIndex == undefined ? true : props.isIndex,
    ) as TableColumnType<any>[];
    behaviorSet.apiUrl && execApi();
  });
</script>

<style lang="less">
  .export_excel {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    height: 28px;
    padding-right: 7px;
    padding-left: 7px;
    border-radius: 3px;
    background: #fff;
    background-color: @primary-color;
    cursor: pointer;
  }

  .visualized_item {
    width: calc(50% - 5px);
    max-height: 559px;
    margin-top: 10px;
    border-radius: 4px;
    background-color: #fff;
  }

  .position-title {
    position: relative;
    height: 18px;
    padding-left: 8px;
    border-left: 4px solid @primary-color;
    color: #3a3e43;
    font-size: 14px;
    font-weight: 700;
    line-height: 18px;
    white-space: nowrap;
  }

  .statRank {
    height: 492px;

    .ant-table-body {
      height: auto !important;
      max-height: 450px !important;
      overflow-y: scroll !important;
    }
  }
</style>
