<template>
  <div ref="dynaPicker">
    <RadioGroup v-model:value="tabValue" @change="RadioChange" class="dynamic_picker" size="small">
      <RadioButton value="d1" class="day_size"> 昨日 </RadioButton>
      <RadioButton value="d7" class="day_size"> 近7天 </RadioButton>
      <RadioButton value="d30" class="day_size"> 近30天 </RadioButton>
      <RadioButton value="diy" @click="triggerPop">
        <Popover
          placement="bottom"
          :visible="popVisible"
          :getPopupContainer="popOverGetPopupContainer"
        >
          <div class="flex flex-row items-center justify-between min-w-178px whitespace-nowrap">
            <div>
              <input
                type="text"
                class="w-0 h-0 outline-none visibility-hidden border-none"
                ref="hiddenInput"
                @blur="popVisible = false"
              />
              <span style="font-size: 13px">{{ dateValue }}</span>
            </div>
            <AliSvgIcon iconName="iconrili" width="18px" height="18px" :fillColor="'#C8C9CC'" />
          </div>
          <template #content>
            <div
              class="flex flex-row items-center justify-between picker_out_wrapper"
              :style="{ width: pickerMode == 'RangePicker' ? '660px' : '380px' }"
            >
              <div class="flex flex-col justify-center h-full pr-4 mr-4 borderRight">
                <Button
                  :type="pickerMode == 'DatePicker' ? 'primary' : 'default'"
                  @mousedown="change($event, 'DatePicker')"
                  class="mb-5"
                  size="small"
                  >自然日</Button
                >
                <Button
                  :type="pickerMode == 'WeekPicker' ? 'primary' : 'default'"
                  @mousedown="change($event, 'WeekPicker')"
                  class="mb-5"
                  size="small"
                  >自然周</Button
                >
                <Button
                  :type="pickerMode == 'MonthPicker' ? 'primary' : 'default'"
                  @mousedown="change($event, 'MonthPicker')"
                  class="mb-5"
                  size="small"
                  >自然月</Button
                >
                <Button
                  :type="pickerMode == 'YearPicker' ? 'primary' : 'default'"
                  @mousedown="change($event, 'YearPicker')"
                  class="mb-5"
                  size="small"
                  >自然年</Button
                >
                <Button
                  :type="pickerMode == 'RangePicker' ? 'primary' : 'default'"
                  @mousedown="change($event, 'RangePicker')"
                  size="small"
                  >自定义</Button
                >
              </div>
              <div ref="pickerContainer" class="picker-container">
                <section>
                  <PickerComponent
                    :open="true"
                    class="!display-none relative"
                    :getPopupContainer="getPopupContainer"
                    dropdownClassName="dropdownClass"
                    :disabled-date="disabledDate"
                    @change="dateChange"
                  />
                </section>
              </div>
            </div>
          </template>
        </Popover>
      </RadioButton>
    </RadioGroup>
  </div>
</template>

<script setup>
  import { DatePicker, Button, Popover, RadioGroup, RadioButton } from 'ant-design-vue';
  import { ref, onMounted, unref, computed, reactive } from 'vue';
  import dayjs from 'dayjs';
  import moment from 'moment';

  const pickerMode = ref('DatePicker');
  const popVisible = ref(false);
  const hiddenInput = ref();
  //动态picker组件，用于切换
  const PickerComponent = computed(() =>
    unref(pickerMode) == 'DatePicker' ? DatePicker : DatePicker[unref(pickerMode)],
  );
  const paramForApi = reactive({});
  const tabValue = ref('d1');
  const dynaPicker = ref();
  const pickerContainer = ref();
  const props = defineProps({
    pickerKey: String,
  });
  const getPopupContainer = () => pickerContainer.value;
  const popOverGetPopupContainer = () => dynaPicker.value;
  const dateValue = ref('');
  const emits = defineEmits(['change']);
  const change = (e, type) => {
    e.preventDefault();
    pickerMode.value = type;
  };

  const disabledDate = (current) => {
    switch (unref(pickerMode)) {
      case 'WeekPicker': {
        return current && current > moment().subtract(1, 'weeks').endOf('week');
      }

      case 'MonthPicker': {
        return current && current > moment().subtract(1, 'months').endOf('month');
      }

      case 'YearPicker': {
        return current && current > moment().subtract(1, 'years').endOf('year');
      }

      default: {
        return current && current > moment().subtract(1, 'days').endOf('day');
      }
    }
  };
  //各种picker选择日期后的change事件
  const dateChange = (e) => {
    let dayType = 'd1';
    switch (unref(pickerMode)) {
      case 'DatePicker': {
        dayType = 'd1';
        dateValue.value = `${e.$y}/${e.$M + 1}/${e.$D}`;
        combineFormatForParameter([e.$d, e.$d]);
        break;
      }
      case 'WeekPicker': {
        dayType = 'd7';
        const weekResult = getWeekDays(e.$d);
        combineFormatForParameter(weekResult);
        const MonDay = `${formatDayJs(weekResult[0], '/')}`;
        const Sunday = `${formatDayJs(weekResult[1], '/')}`;
        dateValue.value = `${MonDay}-${Sunday}`;
        break;
      }
      case 'MonthPicker': {
        dayType = 'd30';
        const monthResult = getMonthRange(e.$d, e.daysInMonth());
        combineFormatForParameter(monthResult);
        dateValue.value = `${e.$y}/${e.$M + 1}`;
        break;
      }
      case 'YearPicker': {
        dayType = 'dY';
        const yearResult = getYearRange(e.$d);
        combineFormatForParameter(yearResult);
        dateValue.value = `${e.$y}`;
        break;
      }
      case 'RangePicker': {
        dayType = 'dR';
        const startDate = `${formatDayJs(e[0].$d, '/')}`;
        const endDate = `${formatDayJs(e[1].$d, '/')}`;
        combineFormatForParameter([e[0].$d, e[1].$d]);
        dateValue.value = `${startDate}-${endDate}`;
        break;
      }
    }
    emits('change', paramForApi, dayType);
    hiddenInput.value.blur();
  };
  //格式化参数，专门为接口参数
  const combineFormatForParameter = (time) => {
    const startTime = `${formatDayJs(time[0], '-')} 00:00:00`;
    const endTime = `${formatDayJs(time[1], '-')} 23:59:59:999`;
    paramForApi.startTime = startTime;
    paramForApi.endTime = endTime;
  };

  /**获取一周的开始日期和结尾日期
   *@param {type} normal:正常(即该天所在周的周一和周日)
   *@param {type} fromNow:(即该天开始到一周前的日期)
   */
  const getWeekDays = (date, type = 'normal') => {
    const weekStart = new Date(date.getTime()); // 周一的日期
    if (type == 'fromNow') {
      const weekEnd = new Date(date.getTime()); // 周一的日期
      weekEnd.setDate(weekEnd.getDate() - 6);
      return [weekEnd, weekStart];
    }
    weekStart.setDate(weekStart.getDate() - weekStart.getDay() + 1); // 将日期调整为周一
    const weekEnd = new Date(weekStart.getTime()); // 周日的日期
    weekEnd.setDate(weekEnd.getDate() + 6); // 将日期调整为周日
    return [weekStart, weekEnd];
  };

  /**获取一周的开始日期和结尾日期
   *@param {type} normal:正常(即该天所在月的1号和月尾)
   *@param {type} fromNow:(即该天开始到30天前的日期)
   */
  const getMonthRange = (date, monthDays, type = 'normal') => {
    const monthStart = new Date(date.getTime()); // 当前的日期
    if (type == 'fromNow') {
      const monthEnd = new Date(monthStart.getTime()); // 结尾的日期
      monthEnd.setDate(monthEnd.getDate() - monthDays + 1); // 将日期调整为结尾
      return [monthEnd, monthStart];
    }
    monthStart.setDate(1); // 将日期调整为一号
    const monthEnd = new Date(monthStart.getTime()); // 结尾的日期
    monthEnd.setDate(monthEnd.getDate() + monthDays - 1); // 将日期调整为结尾
    return [monthStart, monthEnd];
  };
  /**获取该Date所在年份的1月1号和12月31号 */
  const getYearRange = (date) => {
    const startDate = new Date(date.getTime());
    startDate.setMonth(0);
    startDate.setDate(1);
    const endDate = new Date(date.getTime());
    endDate.setMonth(11);
    endDate.setDate(31);
    return [startDate, endDate];
  };
  //获取昨天的日期
  const yesterDay = (format) => {
    const dateNow = new Date();
    dateNow.setDate(dateNow.getDate() - 1);
    return format ? formatDayJs(dateNow, '/') : dateNow;
  };

  //出发弹层
  const triggerPop = () => {
    tabValue.value = 'd';
    popVisible.value = true;
    hiddenInput.value.focus();
  };

  //radio选择事件
  const RadioChange = (e) => {
    const { value } = e.target;
    if (value === 'diy') {
      triggerPop();
    } else {
      if (value === 'd7') {
        const [weekEnd, weekStart] = getWeekDays(yesterDay(), 'fromNow');
        combineFormatForParameter([weekEnd, weekStart]);
        dateValue.value = `${formatDayJs(weekEnd, '/')}-${formatDayJs(weekStart, '/')}`;
      } else if (value === 'd30') {
        const [monthEnd, monthStart] = getMonthRange(yesterDay(), 30, 'fromNow');
        combineFormatForParameter([monthEnd, monthStart]);
        dateValue.value = `${formatDayJs(monthEnd, '/')}-${formatDayJs(monthStart, '/')}`;
      } else if (value === 'd1') {
        dateValue.value = yesterDay(true);
        combineFormatForParameter([yesterDay(), yesterDay()]);
      }
      emits('change', paramForApi, value);
    }
  };
  //格式化日期YYYY DD MM，可以是-隔开或者是/隔开
  const formatDayJs = (date, dot) => {
    return dayjs(date).format(`YYYY${dot}MM${dot}DD`);
  };
  onMounted(() => {
    dateValue.value = yesterDay(true);
    combineFormatForParameter([yesterDay(), yesterDay()]);
  });
</script>

<style lang="less">
  .picker-container {
    position: relative;

    & > div {
      position: relative !important;
    }
  }

  .borderRight {
    border-right: 1px solid #dcdee0;
  }

  .picker_out_wrapper {
    width: 380px;
    height: 309px;
  }

  .dropdownClass {
    position: relative;

    .ant-picker-panel-container {
      box-shadow: none;
    }
  }

  .dynamic_picker {
    display: flex;
    align-items: center;

    .ant-radio-button-wrapper {
      height: 28px;
      border-color: #f2f2f2;
      line-height: 26px;
    }

    .ant-radio-button-wrapper:not(:first-child)::before {
      background-color: transparent;
    }

    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)::before {
      background-color: @primary-color;
    }

    .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
      border-color: @primary-color;
      background: #fff;
      color: @primary-color;
    }
  }

  .day_size {
    span {
      font-size: 13px;
      white-space: nowrap;
    }
  }
</style>
