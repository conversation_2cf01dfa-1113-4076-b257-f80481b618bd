<template>
  <div class="visualized_item_charts">
    <Spin :spinning="loading">
      <div
        class="flex p-10px border-b-1 border-[#EEEEEE] flex-col items-center xl:flex-row xl:justify-between min-h-44px flex-wrap"
        v-if="props.title || $slots.extraSlot"
      >
        <div class="flex flex-row items-center">
          <div class="position-title font-bold !text-[14px] mb-4 xl:mb-0 mr-5" v-if="props.title">{{
            props.title
          }}</div>
          <div v-if="$slots.extraSlot">
            <slot name="extraSlot"></slot>
          </div>
        </div>

        <div>
          <DynamicPicker @change="submitValue" v-if="props.datePicker" />
          <div v-if="$slots.rightSlot"> <slot name="rightSlot"></slot></div>
        </div>
      </div>
      <div v-if="$slots.extraMiddleSlot">
        <slot name="extraMiddleSlot"></slot>
      </div>
      <div class="p-10px relative" style="margin-top: 20px">
        <div ref="chartRef" :style="{ height: '458px', width: '100%' }"></div>
        <div
          class="flex_row_center_center absolute inset-0 bg-white"
          style="width: 100%; height: 100%"
          v-show="isEmpty"
        >
          <Empty
            :image="Empty.PRESENTED_IMAGE_SIMPLE"
            imageStyle="height: 80px"
            description="暂无统计数据~"
          >
          </Empty>
        </div>
      </div>
    </Spin>
  </div>
</template>

<script setup>
  import { onMounted, ref, getCurrentInstance } from 'vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  import DynamicPicker from '../DynaPicker/picker.vue';
  import { Spin } from 'ant-design-vue';
  import { setLinearCharts } from '../setOption/linearOption';
  import { setPieCharts } from '../setOption/pieOption';
  import { setGeoOption } from '../setOption/geoOption';
  import { initialDateVar } from '../index';
  import { defHttp } from '/@/utils/http/axios';
  import geo from '@/assets/json/geo.json';
  import { failTip } from '@/utils/utils';
  import { Empty } from 'ant-design-vue';

  const props = defineProps({
    datePicker: Boolean,
    title: {
      type: String,
    },
    apiUrl: String,
    paramHandler: Function,
    dataHandler: Function,
    mode: {
      type: String,
      default: 'linear',
    },
    isExectApi: {
      type: Boolean,
      default: true,
    },
  });
  const emits = defineEmits(['change', 'register']);
  const chartRef = ref();
  const currentDateParam = ref(initialDateVar);
  const loading = ref(true);
  const dateSource = ref();
  const currentApi = ref(props.apiUrl);
  const { setOptions, echarts } = useECharts(chartRef);

  const isEmpty = ref(false);

  echarts.registerMap('china', geo);
  const setCharts = (options, mode) => {
    let modeFinal = mode || props.mode;
    setLoading(true);
    if (modeFinal == 'linear') {
      setLinearCharts(setOptions, options)
        .then(() => setLoading(false))
        .catch((err) => console.log(err));
    } else if (modeFinal == 'pie') {
      setPieCharts(setOptions, options).then(() => setLoading(false));
    } else if (modeFinal == 'geo') {
      setGeoOption(setOptions, options).then(() => setLoading(false));
    }
  };
  const setLoading = (bool) => {
    loading.value = bool;
  };
  const submitValue = (paramForApi) => {
    currentDateParam.value = paramForApi;
    execApi();
  };

  const execApi = async () => {
    const params = props.paramHandler
      ? props.paramHandler(currentDateParam.value)
      : props.datePicker
      ? currentDateParam.value
      : {};
    const res = await defHttp.get({ url: currentApi.value, params });
    if (res?.state == 200) {
      dateSource.value = res.data;
      execDataHandler();
    } else {
      failTip(res.msg);
    }
  };
  const execDataHandler = () => {
    const result = props.dataHandler ? props.dataHandler(dateSource.value) : {};
    isEmpty.value = result.series?.length > 0 ? false : true;
    setCharts(result, props.mode);
  };
  const setApiUrl = (url) => {
    currentApi.value = url;
  };

  const instance = getCurrentInstance();
  instance && emits('register', { setCharts, execApi, execDataHandler, setApiUrl }, instance.uid);

  onMounted(() => {
    props.isExectApi && props.apiUrl && execApi();
  });
</script>

<style scoped lang="less">
  .visualized_item_charts {
    flex: 1;
    min-height: 559px;
    margin-top: 10px;
    border-radius: 4px;
    background-color: #fff;
  }

  .position-title {
    position: relative;
    height: 18px;
    padding-left: 8px;
    border-left: 4px solid @primary-color;
    color: #3a3e43;
    font-size: 14px;
    font-weight: 700;
    line-height: 18px;
    white-space: nowrap;
  }
</style>
