import { tryOnUnmounted } from '@vueuse/shared';
import { isProdMode } from '/@/utils/env';
import { ref, unref } from 'vue';
export default function useStatChartsContext() {
  const statcharts = ref();
  const register = (instance) => {
    isProdMode() &&
      tryOnUnmounted(() => {
        statcharts.value = null;
      });
    if (statcharts.value) return;
    statcharts.value = instance;
  };
  const setCharts = (options, mode) => {
    unref(statcharts).setCharts(options, mode);
  };
  const execApi = () => {
    unref(statcharts).execApi();
  };
  const execDataHandler = () => {
    unref(statcharts).execDataHandler();
  };
  const setApiUrl = (url) => {
    unref(statcharts).setApiUrl(url);
  };
  return { register, setCharts, execApi, execDataHandler, setApiUrl };
}
