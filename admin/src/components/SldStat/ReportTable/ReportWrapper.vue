<template>
  <div class="report_wrapper w-full">
    <Spin :spinning="loading">
      <div
        class="flex p-10px border-b-1 border-[#EEEEEE] flex-row items-center justify-between min-h-44px"
      >
        <div class="flex flex-row items-center">
          <div class="position-title font-bold !text-[14px] mr-5">{{ props.title }}</div>
        </div>
        <div class="export_excel flex flex-row items-center justify-center" @click="exportExcel">
          <AliSvgIcon iconName="iconziyuan23" width="15" height="15" fillColor="#fff" />
          <div class="text-sm text-white ml-1">导出报表</div>
        </div>
      </div>
      <div class="p-10px">
        <slot></slot>
      </div>
    </Spin>
  </div>
</template>

<script setup>
  import { ref, provide } from 'vue';
  import { Spin } from 'ant-design-vue';
  import { exportRequestOption } from '../setOption/tableOption';
  import { initialDateVar } from '../index';

  const props = defineProps({
    exportOption: Object,
    loading: Boolean,
    title: String,
  });
  const paramLocal = ref(initialDateVar);
  const exportExcel = () => {
    exportRequestOption(props.exportOption, paramLocal.value);
  };
  const provideFunc = (params) => {
    paramLocal.value = params;
  };

  provide('report-group', provideFunc);
</script>

<style scoped lang="less">
  .export_excel {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    height: 28px;
    padding-right: 7px;
    padding-left: 7px;
    border-radius: 3px;
    background: #fff;
    background-color: @primary-color;
    cursor: pointer;
  }

  .report_wrapper {
    flex: 1;
    border-radius: 4px;
    background-color: #fff;
  }

  .position-title {
    position: relative;
    height: 18px;
    padding-left: 8px;
    border-left: 4px solid @primary-color;
    color: #3a3e43;
    font-size: 14px;
    font-weight: 700;
    line-height: 18px;
    white-space: nowrap;
  }
</style>
