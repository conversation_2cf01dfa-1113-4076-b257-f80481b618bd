<template>
  <div class="mt-10px">
    <DynaPicker v-if="showPicker" @change="dynaChange" />
    <div class="h-10px"></div>
    <BasicTable @register="standardTable" />
  </div>
</template>

<script setup>
  import DynaPicker from '../DynaPicker/picker.vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import { initialDateVar } from '../index';
  import { ref, inject, unref, computed, watch } from 'vue';

  const groupFunc = inject('report-group');
  const props = defineProps({
    showPicker: {
      typoe: Boolean,
      default: true,
    },
    columns: {
      type: Array,
      requred: true,
    },
    searchData: {
      type: Array,
      default: () => [],
    },
    extraSearch: {
      type: Object,
      default: {},
    },
    isColumnIndex: Boolean,
    api: Function,
    beforeFetch: Function,
    hidePagination: { type: Boolean, default: false },
    listField: { type: String, default: 'data.list' },
  });

  const extraSearchData = computed(() => {
    return props.extraSearch;
  });

  watch(
    extraSearchData,
    () => {
      if (props.extraSearch && Object.keys(props.extraSearch).length > 0) {
        for (let key in props.extraSearch) {
          if (!props.extraSearch[key] || !props.extraSearch[key].trim()) {
            delete props.extraSearch[key];
          }
        }
        dynaChange(props.extraSearch);
      }
    },
    { deep: true },
  );

  const dateParam = ref(initialDateVar);
  const sortParam = ref({});

  const basicChange = (pagination, filter, sort) => {
    sortParam.value = sort;
  };

  const handlerDateSelectSchema = (schemas) => {
    for (const i in schemas) {
      if (
        ['DatePicker', 'RangePicker', 'MonthPicker', 'WeekPicker'].includes(schemas[i].component)
      ) {
        schemas[i].componentProps.disabledDate = (current) => {
          const { startTime, endTime } = unref(dateParam);
          return current && (new Date(endTime) < current.$d || current.$d < new Date(startTime));
        };
      }
    }
    return schemas;
  };

  const beforeFetchHandler = (params) => {
    groupFunc && groupFunc(params);
    return props.beforeFetch?.(params);
  };
  const [standardTable, { reload }] = useTable({
    api: props.api,
    columns: props.columns,
    useSearchForm: props.searchData.length > 0,
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: props.listField,
      totalField: 'data.pagination.total',
    },
    pagination: props.hidePagination
      ? false
      : {
          pageSize: 10,
        },
    formConfig: {
      compact: false,
      schemas: handlerDateSelectSchema(props.searchData),
    },
    searchInfo: () => dateParam.value,
    showIndexColumn: props.isColumnIndex,
    onChange: basicChange,
    beforeFetch: beforeFetchHandler,
    maxHeight: 450,
    canResize: false,
  });

  const dynaChange = (paramForApi) => {
    dateParam.value = paramForApi;
    groupFunc && groupFunc({ dateParam: paramForApi });
    reload({
      searchInfo: dateParam.value,
    });
  };
</script>

<style scoped></style>
