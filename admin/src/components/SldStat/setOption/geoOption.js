const geoOption = (options) => {
  const data = options.series.map((k) => ({
    name: k[options.dataName],
    value: k[options.dataValue],
  }));
  const dataValue = data.map((k) => k.value);
  const maxCount = Math.max.apply(null, dataValue);
  const option = {
    tooltip: {
      trigger: 'item',
      showDelay: 0,
      transitionDuration: 0.2,
    },
    visualMap: {
      left: 'right',
      min: 0,
      max: maxCount,
      inRange: {
        color: ['#e5ebfd', '#4f7efa'],
      },
      text: ['High', 'Low'],
      calculable: true,
    },
    series: [
      {
        name: options.seriesName,
        type: 'map',
        roam: true,
        map: 'china',
        zoom: 1.235,
        emphasis: {
          label: {
            show: true,
          },
        },
        data,
      },
    ],
  };
  return option;
};
export const setGeoOption = (setOptions, options) => {
  return new Promise((resolve) => {
    setOptions(geoOption(options)).then(resolve);
  });
};
