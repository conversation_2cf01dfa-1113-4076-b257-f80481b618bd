<template>
  <div :id="props.id" name="content" type="text/plain" style="width: 100%"></div>
</template>
<script setup>
  import { computed, watch, onMounted, onUnmounted,ref } from 'vue';
  import { useGlobSetting } from '/@/hooks/setting';
  import { getToken } from '/@/utils/auth';

  const { apiUrl } = useGlobSetting();
  const props = defineProps({
    id: { type: String, required: true, default: '' }, //编辑器id-唯一
    initEditorContent: { type: String, require: true, default: '' }, //编辑器内容
    getContentFlag: { type: Boolean, require: true, default: false }, //编辑器内容加载
    getEditorContent: { type: Function, require: true, default: () => null }, //编辑器内容变更事件
  });

  const imgToken = ref(getToken() || ''); //上传文件token参数

  const contentFlag = computed(() => {
    return props.getContentFlag;
  });

  const editorContent = computed(() => {
    return props.initEditorContent;
  });

  watch(
    contentFlag,
    () => {
      if (props.getContentFlag) {
        let con = UE.getEditor(props.id).getContent();
        if(con && con.indexOf("&quot;Microsoft YaHei&quot;")!== -1){
          con = con.replace(/&quot;Microsoft YaHei&quot;/g,"Microsoft YaHei");
        }
        if(con && con.indexOf("&#39;Microsoft YaHei&#39;")!== -1){
          con = con.replace(/&#39;Microsoft YaHei&#39;/g,"Microsoft YaHei");
        }
        props.getEditorContent(con);
      }
    },
    { deep: true },
  );

  watch(
    editorContent,
    () => {
      if (props.initEditorContent) {
        UE.getEditor(props.id).setContent(props.initEditorContent);
      }
    },
    { deep: true },
  );

  function initEditor() {
    const ueEditor = UE.getEditor(props.id, {
      serverUrl: `${apiUrl}/v3/oss/ueditor/upload?configPath=config.json&token=${imgToken.value}`,
    });
    ueEditor.ready((editor) => {
      if (!editor) {
        UE.delEditor(props.id);
        initEditor();
      } else {
        if (props.initEditorContent) {
          UE.getEditor(props.id).setContent(props.initEditorContent);
        }
      }
    });
  }

  onMounted(() => {
    initEditor();
  });

  onUnmounted(() => {
    UE.delEditor(props.id);
  });
</script>
