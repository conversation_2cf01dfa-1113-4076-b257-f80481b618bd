import SldSelGoodsSingleDiy from './index.vue';
import { extraProperty } from '/@/utils';

export { SldSelGoodsSingleDiy };

//选择器选择到需要弹窗的选项时的设置属性
export const setSldSelProps = (type, mode, client) => {
  const modalProps = {
    searchInfo: {},
    diy_type: mode,
    link_type: type,
    client
  };
  return modalProps;
};


export const handleDiyGoodsConfirm = (link_type, e, client, mode = 'default') => {
  let chosen_val = Array.isArray(e) ? e[0] : e;
  let current_formData_item = {};
  if (link_type == 'goods') {
    current_formData_item.link_value = chosen_val.goodsName;
    let extraProp = []

    if (mode == 'integral') {
      extraProp = [
        'integralGoodsId',
        'goodsName',
        'integralPrice',
        'cashPrice',
        'actualSales',
        'mainImage',
        'productId',
      ];
    } else if (mode == 'spreader') {
      extraProp = [
        'goodsName',
        'goodsImage',
        'productPrice',
        'saleNum',
        'spreaderGoodsId',
        'productId',
      ];
    } else {
      extraProp = [
        'actualSales',
        'productId',
        'defaultProductId',
        'goodsId',
        'goodsName',
        'goodsPrice',
        'mainImage',
        'virtualSales'
      ]
    }

    current_formData_item.info = extraProperty(chosen_val, extraProp);
  } else if (link_type == 'category') {
    if (mode == 'integral') {
      current_formData_item.link_value = chosen_val.labelName;
      current_formData_item.info = extraProperty(chosen_val, [
        'labelId',
        'labelName',
        'grade',
        'parentLabelId',
      ]);
    } else if (mode == 'spreader') {
      current_formData_item.link_value = chosen_val.labelName;
      current_formData_item.info = extraProperty(chosen_val, [
        'labelId',
        'labelName',
      ]);
    } else {

      current_formData_item.link_value = chosen_val.categoryName;
      current_formData_item.info = extraProperty(chosen_val, [
        'categoryId',
        'categoryName',
        'grade',
        'pid',
      ]);
    }
  } else if (link_type == 'topic' || link_type == 'o2o_topic') {
    if (client != undefined && client == 'mobile') {
      current_formData_item.link_value = chosen_val.name;
    } else {
      current_formData_item.link_value = chosen_val.decoName;
    }
    current_formData_item.info = extraProperty(chosen_val, ['decoId', 'name', 'decoName']);
  } else if (current_formData_item.link_type == 'seckill') {
    current_formData_item.link_value = chosen_val.seckillName;
    current_formData_item.info = extraProperty(chosen_val, ['seckillId', 'seckillName']);
  } else if (current_formData_item.link_type == 'draw') {
    current_formData_item.link_value = chosen_val.drawName;
    current_formData_item.info = extraProperty(chosen_val, ['drawName', 'drawId']);
  } else if (current_formData_item.link_type == 'store' || link_type == 'store') {
    current_formData_item.link_value = chosen_val.storeName;
    current_formData_item.info = extraProperty(chosen_val, ['storeName', 'storeId']);
  }
  return current_formData_item
};