<!-- 公共头部组件 -->
<template>
  <Modal
    destroyOnClose
    :maskClosable="false"
    :title="modal_title"
    :visible="modalVisible"
    :width="modalWidth"
    @ok="sldConfirm"
    @cancel="sldCancle"
    :footer="props.look ? null : undefined"
  >
    <div class="common_page sld_sel_goods_single_diy goods_list_mainIamge">
      <BasicTable
        @register="registerTable"
        @change="change"
        @selection-change="selectionChange"
        @fetch-success="onFetchSuccess"
        @expand="expandedRowsChange"
        :tableFlag="false"
      >
        <template #tableTitle v-if="tableTitle">
          <slot name="tableTitle"></slot>
        </template>
        <template #bodyCell="data">
          <slot name="bodyCell" v-bind="data || {}"></slot>
          <template v-if="client == 'mobile' || client == 'pc' || client == 'supplier'">
            <template v-if="diy_type == 'integral' && link_type == 'goods'">
              <template v-if="data.column.dataIndex == 'mainImage'">
                <Popover placement="right">
                  <template #content>
                    <div class="goods_list_mainIamge_pop">
                      <img :src="data.text" />
                    </div>
                  </template>
                  <div
                    class="goods_list_leftImage"
                    :style="{ backgroundImage: `url('${data.text}')` }"
                  ></div>
                </Popover>
              </template>
              <template v-else-if="data.column.dataIndex == 'integralPrice'">
                {{ data.text }}积分{{ data.record.cashPrice ? '+¥' + data.record.cashPrice : '' }}
              </template>
            </template>
            <template v-else-if="diy_type == 'spreader' && link_type == 'goods'">
              <template v-if="data.column.dataIndex == 'goodsImage'">
                <Popover placement="right">
                  <template #content>
                    <div class="goods_list_mainIamge_pop">
                      <img :src="data.text" />
                    </div>
                  </template>
                  <div
                    class="goods_list_leftImage"
                    :style="{ backgroundImage: `url('${data.text}')` }"
                  ></div>
                </Popover>
              </template>
            </template>
            <template v-else-if="link_type == 'goods'">
              <template v-if="data.column.dataIndex == 'mainImage'">
                <Popover placement="right">
                  <template #content>
                    <div class="goods_list_mainIamge_pop">
                      <img :src="data.text" />
                    </div>
                  </template>
                  <div
                    class="goods_list_leftImage"
                    :style="{ backgroundImage: `url('${data.text}')` }"
                  ></div>
                </Popover>
              </template>
            </template>
            <template v-else-if="link_type == 'store'">
              <template v-if="data.column.dataIndex == 'isOwnStore'">
                {{ data.text == 2 ? '入驻店铺' : '自营店铺' }}
              </template>
              <template v-else-if="data.column.dataIndex == 'shopType'">
                {{ data.text == 2 ? 'O2O门店' : data.text == 3 ? '供应商店铺' : '普通店铺' }}
              </template>
              <template v-else-if="data.column.dataIndex">
                {{ data.text ? data.text : '--' }}
              </template>
            </template>
            <template v-else-if="link_type == 'seckill' || link_type == 'draw'">
              <template v-if="data.column.dataIndex == 'startTime'">
                {{ data.text + '~' + data.record.endTime }}
              </template>
            </template>
            <template v-else>
              <template v-if="data.column.dataIndex">
                {{ data.text ? data.text : '--' }}
              </template>
            </template>
          </template>
          <template
            v-else-if="
              (link_type == 'crowed_coupon' || link_type == 'level_coupon') &&
              data.column.dataIndex == 'effectiveStart'
            "
          >
            <span v-if="data.record.cycle">领取后{{ data.record.cycle }}天内</span>
            <div v-else-if="data.text && data.record.effectiveEnd" class="voucher_time_wrap">
              <p>{{ data.text }}</p>
              <p>~</p>
              <p>{{ data.record.effectiveEnd }}</p>
            </div>
            <div v-else-if="data.record.useTime">
              <p>{{ data.record.useTime.split('~')[0] }}</p>
              <p>~</p>
              <p>{{ data.record.useTime.split('~')[1] }}</p>
            </div>
            <span v-else> -- </span>
          </template>
          <template
            v-else-if="
              (link_type == 'crowed_coupon' || link_type == 'level_coupon') &&
              data.column.dataIndex == 'sendNum'
            "
          >
            <template
              v-if="
                selectedKeys.length > 0 &&
                selectedKeys.indexOf(data.record.couponId) != -1 &&
                coupon_type == 'super'
              "
            >
              <InputNumber
                :max="data.record.remainNum < 10 ? data.record.remainNum : 10"
                :min="1"
                :precision="0"
                :disabled="
                  searchInfo &&
                  searchInfo.type &&
                  searchInfo.type == 'exclusive' &&
                  data.record.couponType == 4
                    ? true
                    : false
                "
                @change="(e) => change_input(e, data.record.couponId, 'couponId', 'sendNum')"
                v-model:value="data.record.sendNum"
              />
            </template>
            <template
              v-else-if="
                selectedKeys.length > 0 &&
                selectedKeys.indexOf(data.record.couponId) != -1 &&
                (coupon_type == 'coupon' || coupon_type == 'freight')
              "
            >
              <InputNumber
                :max="data.record.totalNum != undefined ? data.record.totalNum : 9999999"
                :min="1"
                :precision="0"
                @change="(e) => change_input(e, data.record.couponId, 'couponId', 'sendNum')"
                v-model:value="data.record.sendNum"
              />
            </template>
            <template
              v-else-if="
                selectedKeys.length > 0 &&
                selectedKeys.indexOf(data.record.couponId) != -1 &&
                coupon_type != 'super'
              "
            >
              <InputNumber
                :max="data.record.publishNum < 15 ? data.record.publishNum : 15"
                :min="1"
                :precision="0"
                v-model:value="data.record.sendNum"
              />
            </template>
            <span v-else></span>
          </template>
        </template>
      </BasicTable>
    </div>
  </Modal>
</template>
<script setup>
  import { ref, reactive, computed, watch } from 'vue';
  import { validatorEmoji } from '/@/utils/validate';
  import { Modal, InputNumber, Popover } from 'ant-design-vue';
  import { BasicTable, useTable } from '/@/components/Table';
  import {
    getCouponNormalListApi,
    getCouponListApi,
    getOperateListApi,
    getLabelOperateMemberApi,
    getMemberLevelCouponListApi,
  } from '/@/api/sldSelGoodsSingleDiy/index';
  import {
    getAdminGoodsList,
    getAdminStoreList,
    getMobleDecoListApi,
    getDrawList,
  } from '/@/api/decorate/deco';
  import {
    getCategoryList,
  } from '/@/api/manage/manage';
  import { getDecoList } from '/@/api/decorate/deco';
  // dev_supplier-start
  import { getAdminSupplierGoodsList, getSupplierDecoList } from '/@/api/supplier/deco_pc';
  // dev_supplier-end
  import { getIntegralGoodsListApi } from '/@/api/point/goods';
  import { getIntegralLabelListApi } from '/@/api/point/label';
  //spreader-1-start
  import {
    getSpreaderGoodsListApi,
    getSpreaderGoodsLabelListApi,
  } from '/@/api/spreader/goods_list';
  //spreader-1-end
  import { getSeckilUnfinishedApi } from '/@/api/promotion/seckill';
  import { failTip } from '@/utils/utils';

  const emit = defineEmits(['cancleEvent', 'confirmEvent', 'expandEvent', 'selectEvent']);

  const props = defineProps({
    modalTitle: { type: String, default: '选择器' }, //弹窗标题
    tableTitle: { type: Boolean, required: false, default: false }, //tableTitle插槽是否启用
    link_type: { type: String, default: '' }, //类型   voucher 优惠券 crowed_coupon 优惠券 scene_cluster 会员分群列表  operate_member_list 人群运营会员列表 level_coupon获取优惠券、运费券列表
    coupon_type: { type: String, default: '' }, //1、link_type为crowed_coupon时 super 属性 2、link_type为level_coupon时:coupon 优惠券 freight运费券
    look: { type: Boolean, required: false, default: false }, //仅查看
    modalVisible: { type: Boolean, required: false, default: false }, //弹窗开关
    modalWidth: { type: Number, required: false, default: 1000 }, //弹窗开关
    api: { type: Function, required: false, default: () => null }, //请求接口
    dataSource: { type: Array, required: false, default: [] }, //静态数据
    selectedRowKeys: {
      type: Array,
      required: true,
      default: () => [],
    }, //选中行的id
    selectedRows: {
      type: Array,
      required: true,
      default: () => [],
    }, //选中行的数据
    column: {
      type: Array,
      required: true,
      default: () => [],
    }, //表单列信息
    formConfig: {
      type: Array,
      required: false,
      default: () => [],
    }, //表单配置
    searchInfo: {
      type: Object,
      required: false,
      default: () => {},
    }, //额外的请求参数
    fetchSetting: {
      type: Object,
      required: false,
      default: () => {},
    }, //分页数据
    rowId: { type: [Number, String], default: '' }, //类型
    checkedType: { type: Boolean, required: false, default: false }, //传接口信息时多选必传 不传就是单选
    showHeader: { type: Boolean, default: true },
    showIndex: { type: Boolean, default: true },
    pagination: { type: Boolean, required: false, default: true }, //分页是否展示
    client: { type: String, default: '' }, //来源 mobile-移动端装修
    diy_type: { type: String, default: 'home' }, //装修来源类型: home-首页手机装修 topic-专题装修 integral-积分商城装修 spreader-推手装修
  });

  //由于watch无法监听modalVisible，所以需要计算属性转为ref对象
  const modalVisible = computed(() => {
    return props.modalVisible;
  });
  const modalTitle = computed(() => {
    return props.modalTitle;
  });
  const link_type = computed(() => {
    return props.link_type;
  });
  const coupon_type = computed(() => {
    return props.coupon_type;
  });
  const searchInfo = computed(() => {
    return props.searchInfo;
  });
  const tableTitle = computed(() => {
    return props.tableTitle;
  });
  const modal_title = ref('');
  const data = ref([]); //表格的数据
  const searchInfos = ref({});
  const selectedRows_row = ref([]);
  const selectedKeys = ref([]);
  const selectedRows_info = ref([]);
  const selectedRowKeys_info = ref([]);
  const rowKey = ref(''); //table 行唯一标识
  const columns = ref([]);
  const search_info = ref({});
  const rowSelection = ref({});
  const search_data = ref([
    //筛选器
    {
      component: 'Input',
      label: `活动名称`,
      field: 'drawName',
      colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
      componentProps: {
        placeholder: `请输入活动名称`,
      },
      rules: [
        {
          // @ts-ignore
          validator: async (rule, value) => {
            await validatorEmoji(rule, value);
          },
          trigger: 'change',
        },
      ],
    },
  ]);

  // 优惠券
  const voucher_columns = ref([
    {
      title: `优惠券名称`,
      dataIndex: 'couponName',
      align: 'center',
      width: 100,
    },
    {
      title: `优惠券类型`,
      dataIndex: 'couponTypeValue',
      align: 'center',
      width: 100,
    },
    {
      title: `优惠券内容`, //优惠券内容
      dataIndex: 'couponContent',
      align: 'center',
      width: 100,
    },
    {
      title: `未领取数量`, //未领取数量
      dataIndex: 'remainNum',
      align: 'center',
      width: 100,
    },
  ]);

  const crowd_voucher_columns = ref([
    {
      title: `优惠券名称`,
      dataIndex: 'couponName',
      align: 'center',
      width: 100,
    },
    {
      title: `优惠券类型`,
      dataIndex: 'couponTypeValue',
      align: 'center',
      width: 100,
    },
    {
      title: `优惠券内容`, //优惠券内容
      dataIndex: 'couponContent',
      align: 'center',
      width: 100,
    },
    {
      title: `使用范围`, //使用范围
      dataIndex: 'description',
      align: 'center',
      width: 150,
      customRender: ({ text }) => {
        return text ? text : '--';
      },
    },
    {
      title: `使用时间`, //使用时间
      dataIndex: 'effectiveStart',
      align: 'center',
      width: 150,
    },
    {
      title: `赠券数`, //赠券数
      dataIndex: 'sendNum',
      align: 'center',
      width: 150,
    },
  ]);

  const crowd_voucher_columns_super = ref([
    {
      title: `优惠券名称`,
      dataIndex: 'couponName',
      align: 'center',
      width: 100,
    },
    {
      title: `优惠券类型`,
      dataIndex: 'couponTypeValue',
      align: 'center',
      width: 100,
    },
    {
      title: `优惠券内容`, //优惠券内容
      dataIndex: 'couponContent',
      align: 'center',
      width: 100,
    },
    {
      title: `剩余可用`, //剩余可用
      dataIndex: 'remainNum',
      align: 'center',
      width: 150,
    },
    {
      title: `赠送数量`, //赠送数量
      dataIndex: 'sendNum',
      align: 'center',
      width: 150,
    },
  ]);

  const crowd_voucher_columns_super_freight = ref([
    {
      title: `运费券名称`,
      dataIndex: 'couponName',
      align: 'center',
      width: 100,
    },
    {
      title: `运费券内容`, //运费券内容
      dataIndex: 'couponContent',
      align: 'center',
      width: 100,
    },
    {
      title: `剩余可用`, //剩余可用
      dataIndex: 'remainNum',
      align: 'center',
      width: 150,
    },
    {
      title: `赠送数量`, //赠送数量
      dataIndex: 'sendNum',
      align: 'center',
      width: 150,
    },
  ]);

  const scene_cluster = ref([
    {
      title: `会员分群名称`,
      dataIndex: 'clusterName',
      align: 'center',
      width: 100,
    },
    {
      title: `分群描述`,
      dataIndex: 'clusterDes',
      align: 'center',
      width: 300,
      customRender: ({ text }) => {
        return text ? text : '--';
      },
    },
    {
      title: `会员人数`,
      dataIndex: 'memberNum',
      align: 'center',
      width: 100,
      customRender: ({ text }) => {
        return text > -1 ? text : `计算中`;
      },
    },
  ]);

  const member_list_colums = ref([
    {
      title: `会员名`,
      dataIndex: 'memberName',
      align: 'center',
      width: 100,
    },
    {
      title: `手机号`,
      dataIndex: 'memberMobile',
      align: 'center',
      width: 100,
    },
    {
      title: `账户余额`,
      dataIndex: 'balance',
      align: 'center',
      width: 100,
      sorter: true,
      showSorterTooltip: false,
      sorterName: 'balance',
      customRender: ({ text }) => {
        return text !== undefined && text !== null ? text : '--';
      },
    },
    {
      title: `积分`,
      dataIndex: 'memberIntegral',
      align: 'center',
      width: 100,
      sorter: true,
      showSorterTooltip: false,
      sorterName: 'member_integral',
      customRender: ({ text }) => {
        return text !== undefined && text !== null ? text : '--';
      },
    },
    {
      title: `注册时间`,
      dataIndex: 'registerTime',
      align: 'center',
      width: 140,
      showSorterTooltip: false,
      sorter: true,
      sorterName: 'register_time',
      customRender: ({ text }) => {
        return text ? text : '--';
      },
    },
    {
      title: `最近一次支付时间`,
      dataIndex: 'lastPayTime',
      align: 'center',
      width: 150,
      showSorterTooltip: false,
      sorter: true,
      sorterName: 'last_pay_time',
      customRender: ({ text }) => {
        return text ? text : '--';
      },
    },
    {
      title: `累计支付金额`,
      dataIndex: 'totalPayAmount',
      align: 'center',
      width: 110,
      showSorterTooltip: false,
      sorter: true,
      sorterName: 'total_pay_num',
      customRender: ({ text }) => {
        return text !== undefined && text !== null ? '¥' + Number(text).toFixed(2) : '--';
      },
    },
    {
      title: `累计支付订单数`,
      dataIndex: 'totalPayNum',
      align: 'center',
      width: 130,
      showSorterTooltip: false,
      sorter: true,
      sorterName: 'total_pay_num',
      customRender: ({ text }) => {
        return text !== undefined && text !== null ? text : '--';
      },
    },
  ]);

  const freight_columns = ref([
    {
      title: `运费券名称`,
      dataIndex: 'couponName',
      align: 'center',
      width: 100,
    },
    {
      title: `运费券内容`, //优惠券内容
      dataIndex: 'couponContent',
      align: 'center',
      width: 100,
    },
    {
      title: `使用范围`, //使用范围
      dataIndex: 'description',
      align: 'center',
      width: 150,
      customRender: ({ text }) => {
        return text ? text : '--';
      },
    },
    {
      title: `使用时间`, //使用时间
      dataIndex: 'effectiveStart',
      align: 'center',
      width: 150,
    },
    {
      title: `赠券数`, //赠券数
      dataIndex: 'sendNum',
      align: 'center',
      width: 150,
    },
  ]);

  const point_goods_columns = ref([
    {
      title: '商品图片',
      dataIndex: 'mainImage',
      align: 'center',
      width: 100,
    },
    {
      title: '商品名称',
      dataIndex: 'goodsName',
      align: 'center',
      width: 200,
    },
    {
      title: '商品价格',
      dataIndex: 'integralPrice',
      align: 'center',
      width: 100,
    },
    {
      title: '销量',
      dataIndex: 'actualSales',
      align: 'center',
      width: 100,
    },
    {
      title: '店铺名称',
      dataIndex: 'storeName',
      align: 'center',
      width: 100,
    },
  ]);

  const sperader_goods_columns = ref([
    {
      title: '商品图片',
      dataIndex: 'goodsImage',
      align: 'center',
      width: 100,
    },
    {
      title: '商品名称',
      dataIndex: 'goodsName',
      align: 'center',
      width: 200,
    },
    {
      title: '商品价格(元)',
      dataIndex: 'productPrice',
      align: 'center',
      width: 100,
    },
    {
      title: '店铺名称',
      dataIndex: 'storeName',
      align: 'center',
      width: 100,
    },
  ]);

  const goods_columns = ref([
    {
      title: '商品图片',
      dataIndex: 'mainImage',
      align: 'center',
      width: 100,
    },
    {
      title: '商品名称',
      dataIndex: 'goodsName',
      align: 'center',
      width: 200,
    },
    {
      title: '商品价格(元)',
      dataIndex: 'goodsPrice',
      align: 'center',
      width: 100,
    },
    {
      title: '销量',
      dataIndex: 'actualSales',
      align: 'center',
      width: 100,
    },
    {
      title: '店铺名称',
      dataIndex: 'storeName',
      align: 'center',
      width: 200,
    },
  ]);

  const store_columns = ref([
    {
      title: '店铺名称',
      dataIndex: 'storeName',
      align: 'center',
      width: 100,
    },
    {
      title: '店铺账号',
      dataIndex: 'vendorName',
      align: 'center',
      width: 100,
    },
    {
      title: '店铺类型',
      dataIndex: 'isOwnStore',
      align: 'center',
      width: 100,
    },
    {
      title: '店铺等级',
      dataIndex: 'storeGradeName',
      align: 'center',
      width: 100,
      customRender: ({ text }) => {
        return text ? text : '--';
      },
    },
    {
      title: '开店时间',
      dataIndex: 'createTime',
      align: 'center',
      width: 150,
    },
  ]);

  const topic_columns = ref([
    {
      title: `专题名称`,
      align: 'center',
      dataIndex: 'name',
      width: 200,
    },
    {
      title: '创建时间',
      align: 'center',
      dataIndex: 'createTime',
      width: 150,
    },
    {
      title: '修改时间',
      dataIndex: 'updateTime',
      align: 'center',
      width: 150,
    },
  ]);

  const topic_columns_pc = ref([
    {
      title: `专题名称`,
      align: 'center',
      dataIndex: 'decoName',
      width: 200,
    },
    {
      title: '创建时间',
      align: 'center',
      dataIndex: 'createTime',
      width: 150,
    },
    {
      title: '修改时间',
      dataIndex: 'updateTime',
      align: 'center',
      width: 150,
    },
  ]);

  const seckill_columns = ref([
    {
      title: '活动名称',
      dataIndex: 'seckillName',
      align: 'center',
      width: 100,
    },
    {
      title: '活动时间',
      dataIndex: 'startTime',
      align: 'center',
      width: 200,
    },
    {
      title: '活动状态',
      dataIndex: 'stateValue',
      align: 'center',
      width: 100,
    },
  ]);

  const draw_columns = ref([
    {
      title: '活动名称',
      dataIndex: 'drawName',
      align: 'center',
      width: 100,
    },
    {
      title: '活动类型',
      dataIndex: 'drawTypeValue',
      align: 'center',
      width: 100,
    },
    {
      title: '活动时间',
      dataIndex: 'startTime',
      align: 'center',
      width: 200,
    },
    {
      title: '活动状态',
      dataIndex: 'stateValue',
      align: 'center',
      width: 100,
    },
  ]);

  const category_columns = ref([
    {
      title: '分类名称',
      align: 'left',
      dataIndex: 'categoryName',
      width: 250,
    },
  ]);

  const point_category_columns = ref([
    {
      title: '分类名称',
      align: 'left',
      dataIndex: 'labelName',
      width: 250,
    },
  ]);

  const dataSource = reactive({
    //表格静态数据: 移动端装修-链接选择商品分类数据
    data: [],
  });

  watch(
    modalVisible,
    () => {
      if (modalVisible.value) {
        dataSource.data = [];
        modal_title.value = modalTitle.value;
        selectedRows_row.value = []; //选中的行数据
        selectedKeys.value = []; //选中的行id
        if (props.selectedRows.length > 0) {
          selectedRows_row.value = JSON.parse(JSON.stringify(props.selectedRows));
        }
        if (props.selectedRowKeys.length > 0) {
          selectedKeys.value = JSON.parse(JSON.stringify(props.selectedRowKeys));
        }
        columns.value = [];
        if (props.client == 'mobile' || props.client == 'pc' || props.client == 'supplier') {
          let fixedSearchInfo = {}; //搜索参数
          let api_info = null; //搜索api
          if (props.diy_type == 'integral' && props.link_type == 'goods') {
            // 获取积分商品列表
            fixedSearchInfo = { state: 3 };
            api_info = getIntegralGoodsListApi;
            modal_title.value = '选择商品';
            columns.value = point_goods_columns.value;
            search_data.value = [
              {
                component: 'Input',
                label: `商品名称`,
                field: 'goodsName',
                colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
                componentProps: {
                  placeholder: `请输入商品名称`,
                },
                rules: [
                  {
                    // @ts-ignore
                    validator: async (rule, value) => {
                      await validatorEmoji(rule, value);
                    },
                    trigger: 'change',
                  },
                ],
              },
            ];
            rowKey.value = 'integralGoodsId';
            rowSelection.value = {
              type: 'radio',
            };
          }
          //spreader-2-start
          else if (props.diy_type == 'spreader' && props.link_type == 'goods') {
            // 获取推手商品列表
            fixedSearchInfo = { state: 3 };
            api_info = getSpreaderGoodsListApi;
            modal_title.value = '选择商品';
            columns.value = sperader_goods_columns.value;
            search_data.value = [
              {
                component: 'Input',
                label: `商品名称`,
                field: 'goodsName',
                colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
                componentProps: {
                  placeholder: `请输入商品名称`,
                },
                rules: [
                  {
                    // @ts-ignore
                    validator: async (rule, value) => {
                      await validatorEmoji(rule, value);
                    },
                    trigger: 'change',
                  },
                ],
              },
            ];
            rowKey.value = 'goodsId';
            rowSelection.value = {
              type: 'radio',
            };
          }
          //spreader-2-end
          else if (props.link_type == 'goods') {
            // 获取商品列表
            fixedSearchInfo = { state: 3, TerminalSource: props.client == 'mobile' ? 2 : 1 };
            api_info = getAdminGoodsList;
            let goodsColumns = JSON.parse(JSON.stringify(goods_columns.value));
            // dev_supplier-start
            if (props.client == 'supplier') {
              api_info = getAdminSupplierGoodsList;
              fixedSearchInfo = { ...fixedSearchInfo, saleModel: '2' };
              goodsColumns.forEach((item) => {
                if (item.dataIndex == 'goodsPrice') {
                  item.dataIndex = 'wholesalePrice';
                }
              });
            }
            // dev_supplier-end
            modal_title.value = '选择商品';
            columns.value = goodsColumns;
            search_data.value = [
              {
                component: 'Input',
                label: `商品名称`,
                field: 'goodsName',
                colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
                componentProps: {
                  placeholder: `请输入商品名称`,
                },
                rules: [
                  {
                    // @ts-ignore
                    validator: async (rule, value) => {
                      await validatorEmoji(rule, value);
                    },
                    trigger: 'change',
                  },
                ],
              },
              {
                component: 'Select',
                label: `商品类型`,
                field: 'shopType',
                colProps: { span: 6, style: 'width:230px !important;max-width:100%;flex:none' },
                componentProps: {
                  placeholder: `请选择商品类型`,
                  options: [
                    { value: '', label: `全部` },
                    { value: '1', label: `普通商品` },
                    { value: '2', label: `O2O商品` },
                  ],
                },
              },
            ];
            // dev_supplier-start
            if (props.client == 'supplier') {
              search_data.value = [
                {
                  component: 'Input',
                  label: `商品名称`,
                  field: 'goodsName',
                  colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
                  componentProps: {
                    placeholder: `请输入商品名称`,
                  },
                  rules: [
                    {
                      // @ts-ignore
                      validator: async (rule, value) => {
                        await validatorEmoji(rule, value);
                      },
                      trigger: 'change',
                    },
                  ],
                },
              ];
            }
            // dev_supplier-end
            rowKey.value = 'goodsId';
            rowSelection.value = {
              type: 'radio',
            };
          } else if (props.link_type == 'store') {
            // 获取店铺列表
            api_info = getAdminStoreList;
            modal_title.value = '选择店铺';
            columns.value = store_columns.value;
            if (props.client == 'pc') {
              fixedSearchInfo = { shopTypes: '1' };
            } else {
              fixedSearchInfo = { shopTypes: '1,2' };
            }
            // dev_supplier-start
            if (props.client == 'supplier') {
              fixedSearchInfo = { shopType: 3 };
            }
            // dev_supplier-end
            search_data.value = [
              {
                component: 'Input',
                label: `店铺名称`,
                field: 'storeName',
                colProps: { span: 6, style: 'width:230px !important;max-width:100%;flex:none' },
                componentProps: {
                  placeholder: `请输入店铺名称`,
                },
                rules: [
                  {
                    // @ts-ignore
                    validator: async (rule, value) => {
                      await validatorEmoji(rule, value);
                    },
                    trigger: 'change',
                  },
                ],
              },
              {
                component: 'Input',
                label: `店铺账号`,
                field: 'vendorName',
                colProps: { span: 6, style: 'width:230px !important;max-width:100%;flex:none' },
                componentProps: {
                  placeholder: `请输入店铺账号`,
                },
                rules: [
                  {
                    // @ts-ignore
                    validator: async (rule, value) => {
                      await validatorEmoji(rule, value);
                    },
                    trigger: 'change',
                  },
                ],
              },
              {
                component: 'Select',
                label: `店铺类型`,
                field: 'isOwnStore',
                colProps: { span: 6, style: 'width:230px !important;max-width:100%;flex:none' },
                componentProps: {
                  placeholder: `请选择店铺类型`,
                  options: [
                    { value: '', label: `全部` },
                    { value: '1', label: `自营店铺` },
                    { value: '2', label: `入驻店铺` },
                  ],
                },
              },
              {
                colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
                component: 'RangePicker',
                label: `开店时间`,
                field: '[startTime,endTime]',
                componentProps: {
                  format: 'YYYY-MM-DD',
                  placeholder: ['开始时间', '结束时间'],
                },
              },
            ];
            rowKey.value = 'storeId';
            rowSelection.value = {
              type: 'radio',
            };
          } else if (props.link_type == 'topic' || props.link_type == 'o2o_topic') {
            // 获取专题列表
            api_info = props.client == 'mobile' ? getMobleDecoListApi : getDecoList;
            // dev_supplier-start
            if (props.client == 'supplier') {
              api_info = getSupplierDecoList;
            }
            if (props.client == 'supplier') {
              fixedSearchInfo = { decoType: 'topic', isEnable: 1 };
            }
            // dev_supplier-end
            if (props.client == 'pc') {
              fixedSearchInfo = { decoType: 'topic', isEnable: 1 };
            }
            modal_title.value = '选择专题';
            fixedSearchInfo.type = props.link_type == 'o2o_topic' ? 'o2o_topic' : 'topic';
            if (props.client == 'pc') {
              columns.value = topic_columns_pc.value;
            } else {
              columns.value = topic_columns.value;
            }
            // dev_supplier-start
            if (props.client == 'supplier') {
              columns.value = topic_columns_pc.value;
            }
            // dev_supplier-end
            search_data.value = [
              {
                component: 'Input',
                label: `专题名称`,
                field: props.client == 'mobile' ? 'name' : 'decoName',
                colProps: { span: 6, style: 'width:230px !important;max-width:100%;flex:none' },
                componentProps: {
                  placeholder: `请输入专题名称`,
                },
                rules: [
                  {
                    // @ts-ignore
                    validator: async (rule, value) => {
                      await validatorEmoji(rule, value);
                    },
                    trigger: 'change',
                  },
                ],
              },
            ];
            rowKey.value = 'decoId';
            rowSelection.value = {
              type: 'radio',
            };
          } else if (props.link_type == 'seckill') {
            // 获取秒杀活动列表
            api_info = getSeckilUnfinishedApi;
            modal_title.value = '选择秒杀活动';
            columns.value = seckill_columns.value;
            search_data.value = [
              {
                component: 'Input',
                label: `活动名称`,
                field: 'seckillName',
                colProps: { span: 6, style: 'width:230px !important;max-width:100%;flex:none' },
                componentProps: {
                  placeholder: `请输入活动名称`,
                },
                rules: [
                  {
                    // @ts-ignore
                    validator: async (rule, value) => {
                      await validatorEmoji(rule, value);
                    },
                    trigger: 'change',
                  },
                ],
              },
              {
                component: 'Select',
                label: `活动状态`,
                field: 'state',
                colProps: { span: 6, style: 'width:230px !important;max-width:100%;flex:none' },
                componentProps: {
                  placeholder: `活动状态`,
                  options: [
                    { value: '', label: `全部` },
                    { value: '1', label: `未开始` },
                    { value: '2', label: `进行中` },
                  ],
                },
              },
            ];
            rowKey.value = 'seckillId';
            rowSelection.value = {
              type: 'radio',
            };
          } else if (props.link_type == 'draw') {
            // 获取抽奖活动列表
            api_info = getDrawList;
            modal_title.value = '选择抽奖活动';
            columns.value = draw_columns.value;
            search_data.value = [
              {
                component: 'Input',
                label: `活动名称`,
                field: 'drawName',
                colProps: { span: 6, style: 'width:230px !important;max-width:100%;flex:none' },
                componentProps: {
                  placeholder: `请输入活动名称`,
                },
                rules: [
                  {
                    // @ts-ignore
                    validator: async (rule, value) => {
                      await validatorEmoji(rule, value);
                    },
                    trigger: 'change',
                  },
                ],
              },
              {
                component: 'Select',
                label: `活动类型`,
                field: 'drawType',
                colProps: { span: 6, style: 'width:230px !important;max-width:100%;flex:none' },
                componentProps: {
                  placeholder: `请选择活动类型`,
                  options: [
                    { value: '', label: `全部` },
                    { value: '1', label: `幸运抽奖` },
                    { value: '2', label: `大转盘抽奖` },
                    { value: '3', label: `刮刮卡` },
                    { value: '4', label: `摇一摇` },
                    { value: '5', label: `翻翻看` },
                  ],
                },
              },
              {
                component: 'Select',
                label: `活动状态`,
                field: 'state',
                colProps: { span: 6, style: 'width:230px !important;max-width:100%;flex:none' },
                componentProps: {
                  placeholder: `请选择活动状态`,
                  options: [
                    { value: '', label: `全部` },
                    { value: '1', label: `未开始` },
                    { value: '2', label: `进行中` },
                  ],
                },
              },
            ];
            rowKey.value = 'drawId';
            rowSelection.value = {
              type: 'radio',
            };
          } else if (props.diy_type == 'integral' && props.link_type == 'category') {
            //积分标签
            fixedSearchInfo = {
              pageSize: 10000,
              labelId: 0,
            };
            modal_title.value = '选择积分标签';
            columns.value = point_category_columns.value;
            rowKey.value = 'labelId';
            rowSelection.value = {
              type: 'radio',
              columnWidth: 20,
            };
          } else if (props.diy_type == 'spreader' && props.link_type == 'category') {
            //推手标签
            fixedSearchInfo = {
              pageSize: 10000,
              labelId: 0,
              isShow: 1,
            };
            modal_title.value = '选择商品标签';
            columns.value = point_category_columns.value;
            rowKey.value = 'labelId';
            rowSelection.value = {
              type: 'radio',
              columnWidth: 20,
            };
          } else if (props.link_type == 'category') {
            //商品分类
            fixedSearchInfo = {
              pageSize: 10000,
              categoryId: 0,
            };
            modal_title.value = '选择分类';
            columns.value = category_columns.value;
            rowKey.value = 'categoryId';
            rowSelection.value = {
              type: 'radio',
              columnWidth: 20,
            };
          } else {
            return;
          }
          setTimeout(() => {
            if (props.link_type == 'category') {
              setProps({
                isTreeTable: true,
                clickToRowSelect: true,
                fetchSetting: {
                  pageField: 'current',
                  sizeField: 'pageSize',
                  listField: 'data.list',
                },
                dataSource: dataSource.data,
                rowKey: rowKey.value,
                columns: columns.value,
                bordered: true,
                striped: false,
                rowSelection: rowSelection.value,
                searchInfo: props.searchInfo
                  ? { ...props.searchInfo, ...fixedSearchInfo }
                  : { ...fixedSearchInfo },
                onExpand: (expande, record) => onExpandTable(expande, record),
                pagination: false,
              });
              if (props.diy_type == 'integral') {
                get_category_list({ labelId: 0, pageSize: 10000 }, { grade: 0, pid: 0 });
              } else if (props.diy_type == 'spreader') {
                get_category_list({ labelId: 0, isShow: 1, pageSize: 10000 }, { grade: 0, pid: 0 });
              } else {
                get_category_list({ categoryId: 0, pageSize: 10000 }, { grade: 0, pid: 0 });
              }
            } else {
              setProps({
                clickToRowSelect: true,
                api: api_info,
                rowKey: rowKey.value,
                columns: columns.value,
                rowSelection: rowSelection.value,
                showIndexColumn: true,
                searchInfo: props.searchInfo
                  ? { ...props.searchInfo, ...fixedSearchInfo }
                  : { ...fixedSearchInfo },
                useSearchForm: true,
                // 搜索内容
                formConfig: {
                  labelWidth: 75,
                  schemas: search_data.value,
                },
              });
              if (props.selectedRowKeys.length > 0) {
                setSelectedRowKeys(props.selectedRowKeys);
              }
              if (props.client == 'mobile' && props.link_type == 'goods') {
                if (props.diy_type == 'home' || props.diy_type == 'topic') {
                  setTimeout(() => {
                    getForm()
                      .setFieldsValue({
                        shopType: '1',
                      })
                      .then(() => {
                        reload();
                      });
                  }, 200);
                  return;
                } else if (props.diy_type == 'o2o') {
                  setTimeout(() => {
                    getForm()
                      .setFieldsValue({
                        shopType: '2',
                      })
                      .then(() => {
                        reload();
                      });
                  }, 200);
                  return;
                }
              }
              reload();
            }
          });
        } else if (props.link_type == 'voucher') {
          let api_info = getCouponNormalListApi;
          if (props.link_type == 'voucher') {
            // 优惠券
            columns.value = voucher_columns.value;
            modal_title.value = '选择优惠券';
            search_data.value = [
              {
                component: 'Input',
                label: `优惠券名称`, //优惠券名称
                field: 'couponName',
                colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
                componentProps: {
                  placeholder: `请输入优惠券名称`,
                },
                rules: [
                  {
                    // @ts-ignore
                    validator: async (rule, value) => {
                      await validatorEmoji(rule, value);
                    },
                    trigger: 'change',
                  },
                ],
              },
            ];
            rowKey.value = 'couponId';
            rowSelection.value = {
              type: 'radio',
            };
          }
          setTimeout(() => {
            setProps({
              api: api_info,
              rowKey: rowKey.value,
              columns: columns.value,
              rowSelection: rowSelection.value,
              showIndexColumn: true,
              // 搜索内容
              formConfig: {
                labelWidth: 75,
                schemas: search_data.value,
              },
            });
            reload();
          });
        } else if (props.link_type == 'category') {
          setTimeout(() => {
            setProps({
              api: props.api,
              rowKey: props.rowId,
              columns: props.column,
              searchInfo: props.searchInfo ? props.searchInfo : {},
              showHeader: props.showHeader,
              showIndexColumn: props.showIndex,
              pagination: false,
              canResize: false,
              scroll: { y: 450 },
              rowSelection: props.look
                ? undefined
                : props.checkedType
                ? {
                    //单选多选
                    type: 'checkbox',
                  }
                : {
                    type: 'radio',
                  },
            });
            reload();
          });
        } else if (props.link_type == 'crowed_coupon') {
          let api_info = getCouponListApi;
          if (props.link_type == 'crowed_coupon') {
            // 优惠券
            if (
              props.searchInfo &&
              props.searchInfo.couponType &&
              props.searchInfo.couponType == 4 &&
              props.coupon_type == 'super'
            ) {
              columns.value = crowd_voucher_columns_super_freight.value;
            } else {
              columns.value =
                props.coupon_type == 'super'
                  ? crowd_voucher_columns_super.value
                  : crowd_voucher_columns.value;
            }
            modal_title.value =
              props.searchInfo && props.searchInfo.couponType && props.searchInfo.couponType == 4
                ? '选择运费券'
                : '选择优惠券';
            search_data.value = [
              {
                component: 'Input',
                label:
                  props.searchInfo &&
                  props.searchInfo.couponType &&
                  props.searchInfo.couponType == 4
                    ? '运费券名称'
                    : `优惠券名称`, //优惠券名称
                field: 'couponName',
                colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
                componentProps: {
                  placeholder:
                    props.searchInfo &&
                    props.searchInfo.couponType &&
                    props.searchInfo.couponType == 4
                      ? '请输入运费券名称'
                      : `请输入优惠券名称`,
                },
                rules: [
                  {
                    // @ts-ignore
                    validator: async (rule, value) => {
                      await validatorEmoji(rule, value);
                    },
                    trigger: 'change',
                  },
                ],
              },
            ];
            if (
              !(props.searchInfo && props.searchInfo.couponType && props.searchInfo.couponType == 4)
            ) {
              search_data.value.push({
                component: 'Select',
                label: `优惠券类型`,
                field: 'couponType',
                labelWidth: 90,
                colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
                componentProps: {
                  placeholder: `请选择优惠券类型`,
                  options: [
                    { value: '', label: `全部` },
                    { value: '1', label: `满减券` },
                    { value: '2', label: `折扣券` },
                    { value: '3', label: `随机金额券` },
                  ],
                },
              });
            }
            rowKey.value = 'couponId';
            rowSelection.value = {
              type: 'checkbox',
            };
          }
          setTimeout(() => {
            setProps({
              api: api_info,
              rowKey: rowKey.value,
              clickToRowSelect: false,
              columns: columns.value,
              rowSelection: rowSelection.value,
              showIndexColumn: true,
              searchInfo: props.searchInfo ? props.searchInfo : {},
              useSearchForm: true,
              afterFetch: (res) => {
                if (props.selectedRows.length > 0) {
                  res.forEach((item) => {
                    let obj = props.selectedRows.filter((it) => item.couponId == it.couponId);
                    if (obj.length > 0) {
                      item.sendNum = obj[0].sendNum;
                    }
                  });
                  return res;
                } else {
                  return res;
                }
              },
              // 搜索内容
              formConfig: {
                labelWidth: 75,
                schemas: search_data.value,
              },
            });
            reload();
            if (props.selectedRowKeys.length > 0) {
              setSelectedRowKeys(props.selectedRowKeys);
            }
          });
        } else if (props.link_type == 'scene_cluster') {
          let api_info = getOperateListApi;
          if (props.link_type == 'scene_cluster') {
            // 分群列表
            columns.value = scene_cluster.value;
            modal_title.value = '选择目标人群';
            search_data.value = [
              {
                component: 'Input',
                label: `分群名称`, //优惠券名称
                field: 'clusterName',
                colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
                componentProps: {
                  placeholder: `请输入分群名称`,
                },
                rules: [
                  {
                    // @ts-ignore
                    validator: async (rule, value) => {
                      await validatorEmoji(rule, value);
                    },
                    trigger: 'change',
                  },
                ],
              },
            ];
            rowKey.value = 'clusterId';
            rowSelection.value = {
              type: 'checkbox',
            };
          }
          setTimeout(() => {
            setProps({
              api: api_info,
              rowKey: rowKey.value,
              clickToRowSelect: false,
              columns: columns.value,
              rowSelection: rowSelection.value,
              showIndexColumn: true,
              searchInfo: props.searchInfo ? props.searchInfo : {},
              useSearchForm: true,
              // 搜索内容
              formConfig: {
                labelWidth: 75,
                schemas: search_data.value,
              },
            });
            reload();
            if (props.selectedRowKeys.length > 0) {
              setSelectedRowKeys(props.selectedRowKeys);
            }
          });
        } else if (props.link_type == 'operate_member_list') {
          let api_info = getLabelOperateMemberApi;
          search_info.value = props.searchInfo;
          if (props.link_type == 'operate_member_list') {
            // 分群列表
            columns.value = member_list_colums.value;
            modal_title.value = '添加会员' + ' 已选中' + selectedKeys.value.length + '人';
            search_data.value = [
              {
                component: 'Input',
                label: `会员名`, //优惠券名称
                field: 'memberName',
                colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
                componentProps: {
                  placeholder: `请输入会员名`,
                },
                rules: [
                  {
                    // @ts-ignore
                    validator: async (rule, value) => {
                      await validatorEmoji(rule, value);
                    },
                    trigger: 'change',
                  },
                ],
              },
              {
                component: 'Input',
                label: `手机号`, //优惠券名称
                field: 'memberMobile',
                colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
                componentProps: {
                  placeholder: `请输入手机号`,
                },
                rules: [
                  {
                    // @ts-ignore
                    validator: async (rule, value) => {
                      await validatorEmoji(rule, value);
                    },
                    trigger: 'change',
                  },
                ],
              },
              {
                colProps: { span: 6, style: 'width:300px !important;max-width:100%;flex:none' },
                component: 'RangePicker',
                label: `注册时间`,
                field: '[startRegisterTime,endRegisterTime]',
                componentProps: {
                  format: 'YYYY-MM-DD',
                  placeholder: ['开始时间', '结束时间'],
                },
              },
              {
                colProps: { span: 6, style: 'width:350px !important;max-width:100%;flex:none' },
                component: 'RangePicker',
                label: `最近一次支付时间`,
                labelWidth: 130,
                field: '[startLastPayTime,endLastPayTime]',
                componentProps: {
                  format: 'YYYY-MM-DD',
                  placeholder: ['开始时间', '结束时间'],
                },
              },
            ];
            rowKey.value = 'memberId';
            rowSelection.value = {
              type: 'checkbox',
            };
          }
          setTimeout(() => {
            setProps({
              api: api_info,
              rowKey: rowKey.value,
              clickToRowSelect: false,
              columns: columns.value,
              rowSelection: rowSelection.value,
              showIndexColumn: true,
              searchInfo: search_info.value ? search_info.value : {},
              useSearchForm: true,
              beforeFetch(values) {
                values.startRegisterTime = values.startRegisterTime
                  ? values.startRegisterTime.split(' ')[0] + ' 00:00:00'
                  : undefined;
                values.endRegisterTime = values.endRegisterTime
                  ? values.endRegisterTime.split(' ')[0] + ' 23:59:59'
                  : undefined;
                values.startLastPayTime = values.startLastPayTime
                  ? values.startLastPayTime.split(' ')[0] + ' 00:00:00'
                  : undefined;
                values.endLastPayTime = values.endLastPayTime
                  ? values.endLastPayTime.split(' ')[0] + ' 23:59:59'
                  : undefined;
                return values;
              },
              // 点击搜索前处理的参数
              handleSearchInfoFn: (values, flag) => {
                // flag==2为重置 1为查询
                if (flag == 2) {
                  columns.value.forEach((item) => {
                    if (item.sorter) {
                      item.sortOrder = null;
                    }
                  });
                  if (search_info.value) {
                    if (search_info.value.sort) {
                      delete search_info.value.sort;
                      delete search_info.value.sortType;
                    }
                  }
                  setProps({
                    searchInfo: search_info.value,
                  });
                } else {
                  setProps({
                    searchInfo: search_info.value,
                  });
                }
                return values;
              },
              // 搜索内容
              formConfig: {
                labelWidth: 75,
                schemas: search_data.value,
              },
              // 排序函数
              sortFn: (sortInfo) => {
                columns.value.forEach((item) => {
                  if (item.sorter) {
                    item.sortOrder = null;
                  }
                  if (item.dataIndex == sortInfo.field && item.sorter) {
                    if (sortInfo.order) {
                      item.sortOrder = sortInfo.order;
                    } else {
                      item.sortOrder = null;
                    }
                  }
                });
                if (sortInfo.order) {
                  search_info.value = {
                    ...search_info.value,
                    sort: sortInfo.column.sorterName,
                    sortType: sortInfo.order.split('end')[[0]],
                  };
                } else {
                  search_info.value = {};
                }
                setProps({
                  searchInfo: search_info.value,
                });
              },
            });
            reload();
            if (props.selectedRowKeys.length > 0) {
              setSelectedRowKeys(props.selectedRowKeys);
            }
          });
        } else if (props.link_type == 'level_coupon') {
          // 获取优惠券、运费券列表
          let api_info = getMemberLevelCouponListApi;
          if (props.link_type == 'level_coupon') {
            // 优惠券
            columns.value =
              props.coupon_type == 'freight' ? freight_columns.value : crowd_voucher_columns.value;
            modal_title.value = props.coupon_type == 'freight' ? '选择运费券' : '选择优惠券';
            search_data.value = [
              {
                component: 'Input',
                label: props.coupon_type == 'freight' ? `运费券名称` : '优惠券名称', //优惠券名称
                field: 'couponName',
                colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
                componentProps: {
                  placeholder:
                    props.coupon_type == 'freight' ? `请输入运费券名称` : '请输入优惠券名称',
                },
                rules: [
                  {
                    // @ts-ignore
                    validator: async (rule, value) => {
                      await validatorEmoji(rule, value);
                    },
                    trigger: 'change',
                  },
                ],
              },
            ];
            if (!(props.coupon_type == 'freight')) {
              search_data.value.push({
                component: 'Select',
                label: `优惠券类型`,
                field: 'couponType',
                labelWidth: 90,
                colProps: { span: 6, style: 'width:250px !important;max-width:100%;flex:none' },
                componentProps: {
                  placeholder: `请选择优惠券类型`,
                  options: [
                    { value: '', label: `全部` },
                    { value: '1', label: `满减券` },
                    { value: '2', label: `折扣券` },
                    { value: '3', label: `随机金额券` },
                  ],
                },
              });
            }
            rowKey.value = 'couponId';
            rowSelection.value = {
              type: 'checkbox',
            };
          }
          setTimeout(() => {
            setProps({
              api: api_info,
              rowKey: rowKey.value,
              clickToRowSelect: false,
              columns: columns.value,
              rowSelection: rowSelection.value,
              showIndexColumn: true,
              searchInfo: props.searchInfo ? props.searchInfo : {},
              useSearchForm: true,
              afterFetch: (res) => {
                if (props.selectedRows.length > 0) {
                  res.forEach((item) => {
                    let obj = props.selectedRows.filter((it) => item.couponId == it.couponId);
                    if (obj.length > 0) {
                      item.sendNum = obj[0].sendNum;
                    }
                  });
                  return res;
                } else {
                  return res;
                }
              },
              // 搜索内容
              formConfig: {
                labelWidth: 75,
                schemas: search_data.value,
              },
            });
            reload();
            if (props.selectedRowKeys.length > 0) {
              setSelectedRowKeys(props.selectedRowKeys);
            }
          });
        } else {
          props.column.forEach((item) => {
            if (!item.customRender) {
              item.customRender = ({ text }) => {
                return text !== undefined && text !== null ? text : '--';
              };
            }
          });
          setTimeout(() => {
            rowKey.value = props.rowId;
            if (props.dataSource && props.dataSource.length > 0) {
              setProps({
                dataSource: props.dataSource,
                rowKey: props.rowId, //id
                columns: props.column,
                // 参数
                searchInfo: props.searchInfo ? props.searchInfo : {},
                useSearchForm: props.formConfig.length > 0 ? true : false, //搜索开关
                // 搜索内容
                formConfig: {
                  labelWidth: 75,
                  schemas: props.formConfig,
                },

                rowSelection: props.look
                  ? undefined
                  : props.checkedType
                  ? {
                      //单选多选
                      type: 'checkbox',
                    }
                  : {
                      type: 'radio',
                    },
              });
            } else {
              setProps({
                api: props.api,
                rowKey: props.rowId, //id
                columns: props.column,
                clickToRowSelect: props.checkedType ? false : true,
                // 参数
                searchInfo: props.searchInfo ? props.searchInfo : {},
                useSearchForm: props.formConfig.length > 0 ? true : false, //搜索开关
                // 搜索内容
                formConfig: {
                  labelWidth: 75,
                  schemas: props.formConfig,
                },
                fetchSetting: props.fetchSetting
                  ? props.fetchSetting
                  : {
                      pageField: 'current',
                      sizeField: 'pageSize',
                      listField: 'data.list',
                      totalField: 'data.pagination.total',
                    },
                showHeader: props.showHeader,
                showIndexColumn: props.showIndex,
                rowSelection: props.look
                  ? undefined
                  : props.checkedType
                  ? {
                      //单选多选
                      type: 'checkbox',
                    }
                  : {
                      type: 'radio',
                    },
              });
            }
            if (props.selectedRowKeys.length > 0) {
              setSelectedRowKeys(props.selectedRowKeys);
            }
            reload();
          });
        }
      }
    },
    { deep: true },
  );

  // 表格数据
  const [
    registerTable,
    {
      reload,
      getForm,
      setProps,
      getSelectRowKeys,
      setSelectedRowKeys,
      getDataSource,
      clearSelectedRowKeys,
      setTableData,
    },
  ] = useTable({
    // 请求接口
    rowKey: props.link_type ? rowKey.value : props.rowId,
    immediate: false,
    columns: props.link_type ? columns.value : props.column,
    // 参数
    searchInfo: props.link_type ? () => searchInfos.value : () => props.searchInfo,
    beforeFetch(values) {
      values.startTime = values.startTime
        ? values.startTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
      return values;
    },
    // 点击搜索前处理的参数
    handleSearchInfoFn: (values) => {
      values.startTime = values.startTime
        ? values.startTime.split(' ')[0] + ' 00:00:00'
        : undefined;
      values.endTime = values.endTime ? values.endTime.split(' ')[0] + ' 23:59:59' : undefined;
      return values;
    },
    maxHeight: 300,
    // 搜索内容
    formConfig: {
      labelWidth: 75,
      schemas: !props.link_type ? props.formConfig : search_data.value,
    },
    inset: false,
    // 表格传参参数和接收参数
    fetchSetting: {
      pageField: 'current',
      sizeField: 'pageSize',
      listField: 'data.list',
      totalField: 'data.pagination.total',
    },
    // 开启搜索框
    useSearchForm: props.formConfig.length > 0,
    // 不显示表格设置工具
    showTableSetting: false,
    // 是否显示表格边框
    bordered: true,
    pagination: props.pagination,
    // 是否显示序号列
    showIndexColumn: props.showIndex,
    // 表格右侧操作列配置
    showHeader: props.showHeader,
    ellipsis: false,
  });

  const sldConfirm = () => {
    // 选中行：getSelectRows()
    let record;
    let recordId;
    let modalTableSeleData = {};
    let modalTableSeleDataIds = {};
    if (props.client == 'mobile') {
      if (
        props.link_type == 'goods' ||
        props.link_type == 'store' ||
        props.link_type == 'topic' ||
        props.link_type == 'o2o_topic' ||
        props.link_type == 'seckill' ||
        props.link_type == 'draw' ||
        props.link_type == 'category'
      ) {
        modalTableSeleData = selectedRows_row.value[0];
        modalTableSeleDataIds = selectedKeys.value;
      }
    } else if (props.link_type == 'voucher') {
      record = selectedRows_row.value[0];
      recordId = selectedKeys.value;
      modalTableSeleData.couponId = record.couponId;
      modalTableSeleData.couponName = record.couponName;
      modalTableSeleData.couponContent = record.couponContent;
      modalTableSeleData.publishNum = record.publishNum;
      modalTableSeleData.publishValue = record.publishValue;
      modalTableSeleData.remainNum = record.remainNum;
      modalTableSeleData.couponType = record.couponType;
      modalTableSeleData.randomMin = record.randomMin;
      modalTableSeleData.randomMax = record.randomMax;
      modalTableSeleDataIds = recordId;
    } else if (props.link_type == 'category') {
      modalTableSeleData = selectedRows_row.value[0];
      modalTableSeleDataIds = selectedKeys.value;
    } else if (!props.link_type) {
      setSelectedRowKeys(selectedKeys.value);
      // dev_supplier-start
      if (props.client == 'supplier_goods' && props.rowId == 'goodsId') {
        selectedRows_row.value.forEach((item) => {
          item.goodsPrice = item.wholesalePrice;
        });
      }
      // dev_supplier-end
      modalTableSeleData = selectedRows_row.value;
      modalTableSeleDataIds = selectedKeys.value;
    } else if (props.link_type) {
      // dev_supplier-start
      if (props.link_type == 'goods'&&props.client&&props.client=='supplier') {
        selectedRows_row.value.forEach((item) => {
          item.goodsPrice = item.wholesalePrice;
        });
      }
      // dev_supplier-end
      let obj = []
      if (props.link_type == 'goods'&& (props.client == 'mobile'||props.client == 'pc')) {
        selectedRows_row.value.forEach((item) => {
          let info = {
            goodsId:item.goodsId,
            productId:item.productId,
            goodsName:item.goodsName,
            goodsPrice:item.goodsPrice,
            mainImage:item.mainImage,
            marketPrice:item.marketPrice,
            state:item.state,
            actualSales:item.actualSales,
          }
          obj.push(info)
        });
      }else{
        obj = selectedRows_row.value
      }
      modalTableSeleData = obj;
      modalTableSeleDataIds = selectedKeys.value;
    }
    emit('confirmEvent', modalTableSeleData, modalTableSeleDataIds);
  };

  const change = (e) => {
    if (props.checkedType && !props.look) {
      clearSelectedRowKeys();
      setSelectedRowKeys(selectedKeys.value);
      selectedRows_info.value = JSON.parse(JSON.stringify(selectedRows_row.value));
      selectedRowKeys_info.value = JSON.parse(JSON.stringify(getSelectRowKeys()));
      selectedKeys.value = selectedRowKeys_info.value;
    }
  };

  const onFetchSuccess = (e) => {
    if (props.look) {
      return;
    }
    data.value = [];
    getDataSource().forEach((item) => {
      data.value.push(item[rowKey.value]);
    });
  };

  // 输入框事件
  const change_input = (e, id, typeId, item_type) => {
    selectedRows_row.value.forEach((item) => {
      if (item[typeId] == id) {
        item[item_type] = e;
      }
    });
  };

  const selectionChange = (e) => {
    if (props.look) {
      return;
    }
    if (
      props.checkedType ||
      props.link_type == 'crowed_coupon' ||
      props.link_type == 'scene_cluster' ||
      props.link_type == 'operate_member_list' ||
      props.link_type == 'level_coupon'
    ) {
      //针对翻页无法保存选择的行数据处理
      let selectedRowKey = JSON.parse(JSON.stringify(selectedKeys.value));
      for (let i in data.value) {
        if (selectedKeys.value.indexOf(data.value[i]) != -1) {
          selectedKeys.value.splice(selectedKeys.value.indexOf(data.value[i]), 1);
        }
      }
      selectedKeys.value = [
        ...new Set(selectedKeys.value.concat(JSON.parse(JSON.stringify(getSelectRowKeys())))),
      ];
      let rows = e.rows;
      let rowKeys = JSON.parse(JSON.stringify(selectedKeys.value));
      let selectedRow = selectedRows_row.value;
      let pre_sele_rows_keyarray = [];
      let main_key = rowKey.value;
      for (let i in selectedRow) {
        pre_sele_rows_keyarray.push(selectedRow[i][main_key]);
      }
      // //去掉的话要删掉行数据
      for (let i in selectedRowKey) {
        if (rowKeys.indexOf(selectedRowKey[i]) == -1) {
          selectedRow = selectedRow.filter((item) => item[main_key] != selectedRowKey[i]);
        }
      }
      // //没有的话追加行数据
      for (let i in rowKeys) {
        if (pre_sele_rows_keyarray.indexOf(rowKeys[i]) == -1) {
          let cur_row = rows.filter((item) => item[main_key] == rowKeys[i])[0];
          selectedRow.push(cur_row);
        }
      }
      selectedRows_row.value = selectedRow; //选中的行数据
      if (props.link_type == 'operate_member_list') {
        modal_title.value = '添加会员' + ' 已选中' + selectedKeys.value.length + '人';
      }
    } else {
      selectedRows_row.value = e.rows; //选中的行数据
      selectedKeys.value = e.keys; //选中的行id
    }
    emit('selectEvent', selectedKeys.value.length);
  };

  const sldCancle = () => {
    emit('cancleEvent');
  };

  const expandedRowsChange = async (expanded, record) => {
    if (props.link_type == 'category') {
      if (expanded) {
        if (props.diy_type == 'integral' || props.diy_type == 'spreader') {
          const res = await props.api({ labelId: record.labelId, pageSize: 1000 });
          if (res?.state == 200) {
            const data = getDataSource();
            const result = deepSearch(data, record.labelId, res.data.list);
            setTableData(result);
          }
        } else {
          const res = await props.api({ categoryId: record.categoryId, pageSize: 1000 });
          if (res?.state == 200) {
            const data = getDataSource();
            const result = deepSearch(data, record.categoryId, res.data.list);
            setTableData(result);
          }
        }
      }
    }

    emit('expandEvent', expanded, record);
  };

  const deepSearch = (data, categoryId, targetChildren) => {
    let key = '';
    if (props.diy_type == 'integral' || props.diy_type == 'spreader') {
      key = 'labelId';
    } else {
      key = 'categoryId';
    }
    for (let ins in data) {
      if (data[ins][key] == categoryId) {
        data[ins].children = targetChildren;
        data[ins].dataRequested = true;
      } else if (data[ins].children && data[ins].children.length) {
        deepSearch(data[ins].children, categoryId, targetChildren);
      }
    }
    return data;
  };

  //表格展开关闭事件
  function onExpandTable(expande, record) {
    if (expande && (!record.children || record.children.length == 0)) {
      if (props.diy_type == 'integral') {
        get_category_list({ labelId: record.labelId, pageSize: 10000 }, record);
      } else if (props.diy_type == 'spreader') {
        get_category_list({ labelId: record.labelId, isShow: 1, pageSize: 10000 }, record);
      } else {
        get_category_list({ categoryId: record.categoryId, pageSize: 10000 }, record);
      }
    }
  }

  const get_category_list = async (params, extra) => {
    let res = null;
    let key = '';
    if (props.diy_type == 'integral') {
      res = await getIntegralLabelListApi(params);
      key = 'labelId';
    }
    //spreader-3-start
    else if (props.diy_type == 'spreader') {
      res = await getSpreaderGoodsLabelListApi(params);
      key = 'labelId';
    }
    //spreader-3-end
    else {
      res = await getCategoryList(params);
      key = 'categoryId';
    }
    if (res.state == 200 && res.data && res.data.list) {
      if (extra.grade == 0) {
        dataSource.data = res.data.list;
      } else if (extra.grade == 1) {
        let temp = dataSource.data.filter((item) => item[key] == extra[key]);
        if (temp.length > 0) {
          temp[0].children = res.data.list;
        }
      } else if (extra.grade == 2) {
        let temp = dataSource.data.filter((item) => item[key] == extra.pid);
        if (temp.length > 0) {
          let temps = temp[0].children.filter((item) => item[key] == extra[key]);
          if (temps.length > 0) {
            temps[0].children = res.data.list;
          }
        }
      } else if (extra.grade == 3) {
        let temp = dataSource.data.filter((item) => item[key] == extra.path.split('/')[1]);
        if (temp.length > 0) {
          let temps = temp[0].children.filter((item) => item[key] == extra.pid);
          if (temps.length > 0) {
            let tempss = temps[0].children.filter((item) => item[key] == extra[key]);
            if (tempss.length > 0) {
              tempss[0].children = res.data.list;
            }
          }
        }
      }
      setProps({
        dataSource: dataSource.data,
      });
    } else {
      failTip(res.msg);
    }
  };
</script>
<style lang="less">
  .sld_sel_goods_single_diy {
    max-height: 600px;

    .ant-table-body {
      height: auto !important;
    }
  }

  p {
    margin-bottom: 0;
  }
</style>
