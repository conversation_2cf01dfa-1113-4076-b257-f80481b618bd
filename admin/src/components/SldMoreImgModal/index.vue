<template>
  <div>
    <Modal
      :maskClosable="false"
      :title="title"
      :width="width ? width : '416px'"
      :visible="modalVisible_local"
      @cancel="sldCancel"
    >
      <Form
        v-bind="formItemLayoutModal"
        :model="formData"
        ref="formRef"
        :class="{ '!mt-[20px]': !modalTip }"
      >
        <div class="diy_modal_tip_div" v-if="modalTip && modalTip.length">
          <ul>
            <li v-for="(item, index) in modalTip" :key="index">• {{ item }}</li>
          </ul>
        </div>

        <div v-if="content.title_info">
          <Row type="flex" class="more_row" v-if="'main_title' in current_model">
            <Col flex="1" class="more_cell align_right">
              <span class="table_left_con">标题名称</span>
              <span class="table_left_require">*</span>
            </Col>
            <Col flex="6" class="more_cell">
              <FormItem name="title_name" class="!mb-0" :rules="inputRules('请输入标题名称')">
                <div class="modal_img">
                  <Input
                    v-model:value="formData.title_name"
                    :placeholder="`请输入标题名称`"
                    :maxlength="4"
                  ></Input>
                </div>
              </FormItem>
            </Col>
          </Row>
        </div>

        <div class="imgs_wrap">
          <div
            v-for="(item, index) in imgListFromDataSource"
            :key="index"
            @click="seleCurData(index)"
            class="adv_more_img_wrap"
            :class="{ seleImg: sele_index == index }"
            :style="{
              width: layOutSet.show_width + 'px',
              height: layOutSet.show_height + 'px' || '100px',
            }"
          >
            <img class="adv_01_img" :src="item.value" alt="pic" v-if="item.value" />
            <span @click="del_img(index)" class="del_img">删除</span>
          </div>
        </div>

        <!-- ------------------------- -->
        <div class="">
          <Row type="flex" class="more_row">
            <Col flex="1" class="more_cell align_right">
              <span class="table_left_con">图片</span>
            </Col>
            <Col flex="6" class="more_cell">
              <div class="modal_img">
                <span class="modal_tip_color"
                  >此处对应上传上方选中标签项内容，要求宽度为 {{ layOutSet.img_width }} 像素、高度
                  {{
                    layOutSet.img_height == 0
                      ? '不限制'
                      : layOutSet.img_height + '像素的图片；支持格式gif，jpg，png。'
                  }}</span
                >
                <Button @click="imgOnClick">
                  <div class="flex items-center">
                    <UploadOutlined />
                    <span class="ml-2">上传图片</span>
                  </div>
                </Button>
              </div>
            </Col>
          </Row>

          <Row type="flex" class="more_row">
            <Col flex="1" class="more_cell align_right">
              <span class="table_left_con">操作</span>
            </Col>
            <Col flex="6" class="more_cell">
              <div class="modal_img">
                <Select
                  :value="current_model.link_type"
                  :style="{ width: '110px' }"
                  placeholder="请选择链接类型"
                  @select="sldHandSeleChange"
                  :options="current_diy_link_type"
                >
                </Select>
              </div>
            </Col>
          </Row>

          <Row type="flex" class="more_row" v-if="'main_title' in current_model">
            <Col flex="1" class="more_cell align_right">
              <span class="table_left_con">图片标题</span>
              <span class="table_left_require">*</span>
            </Col>
            <Col flex="6" class="more_cell">
              <FormItem
                :name="['data', sele_index, 'main_title']"
                class="!mb-0"
                :rules="inputRules('请输入图片标题')"
              >
                <div class="modal_img">
                  <Input
                    v-model:value="current_model.main_title"
                    :placeholder="`请输入图片标题`"
                    :maxlength="imgTitleMaxLength"
                    class="!w-150px"
                  ></Input>
                </div>
              </FormItem>
            </Col>
          </Row>

          <Row type="flex" class="more_row" v-if="'sub_title' in current_model">
            <Col flex="1" class="more_cell align_right">
              <span class="table_left_con">图片子标题</span>
              <span class="table_left_require">*</span>
            </Col>
            <Col flex="6" class="more_cell">
              <FormItem
                :name="['data', sele_index, 'sub_title']"
                class="!mb-0"
                :rules="inputRules('请输入图片子标题')"
              >
                <div class="modal_img">
                  <Input
                    v-model:value="current_model.sub_title"
                    :placeholder="`请输入图片子标题`"
                    :maxlength="6"
                  ></Input>
                </div>
              </FormItem>
            </Col>
          </Row>

          <Row
            type="flex"
            class="more_row"
            v-if="
              new_diy_link_type[current_model.link_type] &&
              new_diy_link_type[current_model.link_type].need_link_value
            "
          >
            <Col flex="1" class="more_cell align_right">
              <span class="table_left_con">{{
                singleDiy_type_name_map[current_model.link_type]
              }}</span>
              <span class="table_left_require">*</span>
            </Col>
            <Col flex="6" class="more_cell">
              <div v-if="current_model.link_type == 'url'" class="modal_img">
                <FormItem
                  :name="['data', sele_index, 'link_value']"
                  class="!mb-0"
                  :rules="inputRules('请输入链接地址')"
                >
                  <Input
                    maxLength="250"
                    v-model:value="current_model.link_value"
                    placeholder="请输入链接地址"
                  />
                </FormItem>
              </div>

              <div v-else-if="current_model.link_type == 'keyword'" class="modal_img">
                <FormItem
                  class="!mb-0"
                  style="width: 300px"
                  :name="['data', sele_index, 'link_value']"
                  :rules="inputRules('请输入关键字')"
                >
                  <Input
                    :maxlength="250"
                    style="width: 300px"
                    placeholder="请输入关键字"
                    v-model:value="current_model.link_value"
                  />
                </FormItem>
              </div>

              <div v-else class="modal_img">
                <span class="table_left_con">{{ current_model.link_value }}</span>
              </div>
            </Col>
          </Row>
        </div>
        <!-- ------------------------- -->
      </Form>

      <template #footer>
        <div v-if="show_footer">
          <Button key="back" @click="sldCancel">取消</Button>,
          <Button key="submit" type="primary" :loading="submiting" @click="sldConfirm">确定</Button>
        </div>
      </template>
    </Modal>

    <SldSelGoodsSingleDiy
      v-bind="modalProps"
      :modalVisible="singleDiy_modalVisible"
      @cancleEvent="sldHandleLinkCancle"
      @confirm-event="sldHandleLinkConfirm"
    />

    <SldMaterialImgs
      ref="sldMaterialImgsRef"
      :maxUploadNum="1"
      @confirm-material="confirmMaterial"
    ></SldMaterialImgs>
  </div>
</template>

<script setup>
  import { reactive, ref, computed, unref, watch, onMounted } from 'vue';
  import { Modal, Form, FormItem, Button, Select, Input, Col, Row } from 'ant-design-vue';
  import { UploadOutlined } from '@ant-design/icons-vue';
  import {
    new_diy_link_type,
    new_m_diy_link_type,
    singleDiy_type_name_map,
    // dev_supplier-start
    new_supplier_diy_link_type,
    // dev_supplier-end
    new_m_point_diy_link_type,
    spreader_diy_spreader_link_type,
  } from '@/utils/utils';
  import { extraProperty, resolveImageBindId } from '@/utils';
  import { setSldSelProps } from '@/components/SldSelGoodsSingleDiy';
  import { SldSelGoodsSingleDiy, handleDiyGoodsConfirm } from '@/components/SldSelGoodsSingleDiy';
  import SldMaterialImgs from '@/components/SldMaterialFiles/sldMaterialImgs.vue';
  import { cloneDeep } from 'lodash-es';
  import { validatorEmoji } from '/@/utils/validate';

  const props = defineProps({
    title: String,
    width: Number,
    visible: Boolean,
    show_footer: {
      type: Boolean,
      default: true,
    },
    modalTip: {
      type: Array,
      default: () => [],
    },
    content: {
      type: Object,
      default: () => {},
    },
    uploadLimit: Number,
    client: String,
    mode: String,
    totalNum: {
      type: Number,
      default: 3,
    },
    imgTitleMaxLength: {
      type: Number,
      default: 4,
    },
  });

  const emit = defineEmits(['confirm', 'cancel']);

  const formItemLayoutModal = {
    labelCol: {
      span: 6,
    },
    wrapperCol: {
      span: 30,
    },
  };

  const formRef = ref();

  const singleDiy_modalVisible = ref(false);

  const modalVisible_local = ref(false);

  const submiting = ref(false);

  const sldMaterialImgsRef = ref();

  // @ts-ignore
  const selectImageData = reactive({
    ids: [0],
    data: [{}],
    clearAll() {
      this.ids = [];
      this.data = [];
    },
  }); //产品图片数据

  //分类图片的基本宽高设置
  const layOutSet = reactive({
    img_width: 150,
    img_height: 150,
    show_width: 150,
    show_height: 150,
  });
  //数据

  const formData = reactive({
    title_name: '',
    data: [],
  });

  //下拉操作选项
  const current_diy_link = computed(() => {
    // dev_supplier-start
    if (props.client == 'supplier') {
      return new_supplier_diy_link_type;
    }
    // dev_supplier-end

    if (props.client == 'mobile') {
      switch (props.mode) {
        case 'integral':
          return new_m_point_diy_link_type;
        case 'spreader':
          return spreader_diy_spreader_link_type;
        default:
          return new_m_diy_link_type;
      }
    }

    return new_diy_link_type;
  });

  const current_diy_link_type = computed(() => current_diy_link.value.formatOptions());

  const modalProps = ref({});

  //表格数据源
  const sele_index = ref(0);
  const current_model = ref(formData.data[unref(sele_index)]);

  const imgListFromDataSource = ref([]);

  //删除该图片对应的数据
  const del_img = (index) => {
    imgListFromDataSource.value[index].value = '';
    imgListFromDataSource.value[index].imgPath = '';
  };

  const inputRules = (message) => [
    {
      required: true,
      whitespace: true,
      message,
    },
    {
      validator: async (rule, value) => {
        await validatorEmoji(rule, value);
      },
    },
  ];

  const chosen_select_image = () => {
    let temp = unref(imgListFromDataSource)[unref(sele_index)];
    selectImageData.clearAll();
    if (temp?.value) {
      // @ts-ignore
      selectImageData.ids = [resolveImageBindId(temp.imgPath)];
      selectImageData.data = [
        {
          bindId: resolveImageBindId(temp.imgPath),
          checked: true,
          filePath: temp.imgPath,
          fileUrl: temp.value,
          fileType: 1,
        },
      ];
    }
  };

  //选择图片操作
  const seleCurData = (index) => {
    sele_index.value = index;
    current_model.value = formData.data[unref(sele_index)];
    chosen_select_image();
  };

  const formContent = () => {
    if (props.content.title_info) {
      formData.title_name = props.content.title_info?.title_name;
    }
    let totalNum = Math.max(props.content.data.length, props.totalNum);
    for (let i = 0; i < totalNum; i++) {
      let ope = props.content.data[i];
      if (ope) {
        let _formModel = {
          link_type: ope.link_type,
          link_value: ope.link_value,
          info: ope.info ?? {},
        };
        if (ope.main_title != undefined) {
          _formModel.main_title = ope.main_title;
        }
        if (ope.sub_title != undefined) {
          _formModel.sub_title = ope.sub_title;
        }
        // @ts-ignore
        formData.data[i] = cloneDeep(_formModel);
        // @ts-ignore
        unref(imgListFromDataSource)[i] = {
          imgPath: ope.imgPath ?? '',
          value: ope.imgUrl ?? '',
        };
      } else {
        // @ts-ignore
        formData.data[i] = { link_type: '', link_value: '', info: {} };
        // @ts-ignore
        unref(imgListFromDataSource)[i] = { imgPath: '', value: '' };
      }
    }
    sele_index.value = 0;
    seleCurData(sele_index.value);
  };

  const setContent = (when) => {
    if (props.content.show_height) layOutSet.show_height = props.content.show_height;
    if (props.content.show_width) layOutSet.show_width = props.content.show_width;
    layOutSet.img_height = props.content.height;
    layOutSet.img_width = props.content.width;
    selectImageData.clearAll();
    formContent();
  };

  watch(
    () => props.content,
    () => setContent('watch'),
    {
      deep: true,
    },
  );

  watch(
    () => props.visible,
    () => {
      setModal(props.visible);
    },
  );

  //选择下拉框事件
  const sldHandSeleChange = (val) => {
    unref(current_model).link_type = val;
    unref(current_model).link_value = '';
    if (unref(current_diy_link)[val].require_select_modal) {
      modalProps.value = setSldSelProps(val, props.mode, props.client);
      singleDiy_modalVisible.value = true;
    }
  };

  //选择商品或者分类取消事件
  const sldHandleLinkCancle = () => {
    singleDiy_modalVisible.value = false;
    unref(current_model).link_type = '';
    unref(current_model).link_value = '';
  };

  const sldHandleLinkConfirm = (e) => {
    let current_formData_item = unref(current_model);
    let target = handleDiyGoodsConfirm(current_formData_item.link_type, e, props.client);
    current_formData_item.link_value = target.link_value;
    current_formData_item.info = target.info;
    singleDiy_modalVisible.value = false;
  };

  const setModal = (bool) => {
    modalVisible_local.value = bool;
    if (!bool) {
      submiting.value = false;
    }
  };

  const sldCancel = () => {
    formContent();
    setModal(false);
    emit('cancel');
  };

  const sldConfirm = async () => {
    try {
      await formRef.value.validate();
      //将数据组装，返回给上级页面
      let parent_data = [];
      formData.data.forEach((sub) => {
        let tmp_info = {
          link_value: sub.link_value,
          link_type: sub.link_type,
          info: sub.info ?? {},
        };
        if (Reflect.has(sub, 'main_title')) {
          tmp_info.main_title = sub.main_title;
        }
        if (Reflect.has(sub, 'sub_title')) {
          tmp_info.sub_title = sub.sub_title;
        }
        parent_data.push(tmp_info);
      });

      unref(imgListFromDataSource).forEach((sub, subIdx) => {
        let tmp = {
          imgUrl: sub.value,
          ...extraProperty(sub, ['imgPath']),
        };
        parent_data[subIdx] = Object.assign({}, parent_data[subIdx], tmp);
      });

      let emitData = {
        parent_data,
      };

      if (props.content.title_info?.title_name != undefined) {
        emitData.title_name = formData.title_name;
      }
      emit('confirm', emitData);
    } catch (err) {
      let [errorTarget] = err.errorFields;
      if (Array.isArray(errorTarget.name)) {
        sele_index.value = errorTarget.name[1];
      }
    }
  };

  const confirmMaterial = (val) => {
    let temp = unref(imgListFromDataSource)[unref(sele_index)];
    if (temp) {
      temp.value = val.data.length ? val.data[0].fileUrl : '';
      temp.imgPath = val.data.length ? val.data[0].filePath : '';
      temp.height = val.data.length ? val.data[0].height : '';
      temp.width = val.data.length ? val.data[0].width : '';
    }
  };

  const imgOnClick = () => {
    unref(sldMaterialImgsRef).setMaterialModal(true, selectImageData);
  };

  defineExpose({
    setModal,
  });

  onMounted(() => setContent('mounted'));
</script>

<style lang="less">
  @import './index.less';
</style>
