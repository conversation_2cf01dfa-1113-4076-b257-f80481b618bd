<template>
  <Modal
    :visible="show_preview_modal"
    :footer="null"
    :style="{ textAlign: 'center', width: modal_width }"
    @cancel="handleModalVisible"
  >
    <video
      :alt="preview_alt_con"
      :style="{
        maxWidth: '100%',
        // maxHeight: '100%',
        position: 'absolute',
        top: 0,
        left: '50%',
        right: 0,
        bottom: 0,
        transform: 'translatex(-50%)',
      }"
      :src="video"
      controls
      autoPlay
    ></video>
  </Modal>
</template>
<script setup>
  import { Modal } from 'ant-design-vue';

  const props = defineProps({
    show_preview_modal: { type: Boolean, required: true },
    modal_width: { type: Number, default: 500, required: false },
    preview_alt_con: { type: String, default: undefined, required: false },
    video: { type: String, required: true },
  });

  const emit = defineEmits(['closePreviewModal']);

  // slodon_预览关闭modal
  function handleModalVisible() {
    emit('closePreviewModal');
  }
</script>
