.diy_modal_tip_div {
  padding: 10px 15px;

  ul {
    margin-bottom: 0 !important;
    padding-left: 0 !important;

    li {
      padding: 2px 0;
      color: #db5609;
      font-size: 12px;
    }
  }
}

.imgs_wrap {
  display: flex;
  flex-flow: row wrap;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  margin-bottom: 10px;
  padding: 10px;
  border: 1px solid #e3e3e3;
  border-radius: 4px;
}

.adv_more_img_wrap {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  min-width: 80px;
  height: 44px;
  margin-bottom: 10px;
  margin-left: 10px;
  padding: 10px;
  border: dashed 1px #eee;
  line-height: 15px;
}

.adv_more_img_wrap:hover,
.seleImg {
  border-color: #fbcfac;
  background-color: #fff3da;
}

.adv_more_img_wrap .del_img {
  display: inline-block;
  position: absolute;
  top: 0;
  right: 0;
  padding: 2px 5px;
  background: #fe9700;
  color: #fff;
  font-size: 12px;
  cursor: pointer;
}

.adv_01_img {
  max-width: 100%;
  max-height: 100%;
  margin: 0 auto;
}

.del_img {
  display: inline-block;
  position: absolute;
  top: -1px;
  right: 0;
  padding: 0 4px;
  background: #fe9700;
  color: #fff;
  font-size: 12px;
  cursor: pointer;
}

.modal_tip_color {
  display: block;
  padding: 10px 0;
  color: #db5609;
  font-size: 12px;
}

.table_left_con {
  color: #333;
  font-size: 13px;
}

.table_left_require {
  color: red;
}

.modal_img {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  padding-left: 10px;
}
