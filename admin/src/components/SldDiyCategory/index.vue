<template>
  <div>
    <Modal
      :maskClosable="false"
      :title="title"
      :width="width ? width : '416px'"
      v-model:visible="modalVisible_local"
      @cancel="setModal(false)"
    >
      <div class="diy_modal_tip_div" v-if="modalTip && modalTip.length">
        <ul>
          <li v-for="(item, index) in modalTip" :key="index">• {{ item }}</li>
        </ul>
      </div>

      <div class="imgs_wrap">
        <div
          v-for="(item, index) in img_list"
          :key="index"
          @click="seleCurData(index)"
          class="adv_more_img_wrap"
          :class="{ seleImg: sele_index == index }"
          :style="{
            width: layOutSet.show_width + 'px',
            height: layOutSet.show_height + 'px' || '100px',
          }"
        >
          <img class="adv_01_img" :src="item.imgUrl" alt="pic" v-if="item.imgUrl" />
          <span @click.stop="del_img(index)" class="del_img">删除</span>
        </div>
      </div>

      <Table
        :dataSource="current_dataSource"
        :columns="columns"
        :showHeader="false"
        :pagination="false"
        :scroll="{ y: 340 }"
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex == 'name'">
            <div>
              <span class="table_left_con">{{ record.name }}</span>
              <span class="table_left_require" v-if="record.required">*</span>
            </div>
          </template>

          <template v-if="column.dataIndex == 'type'">
            <div v-if="record.type == 'img'">
              <div class="modal_img" @click="uploadEvent">
                <span class="modal_tip_color"
                  >此处对应上传上方选中标签项内容，要求宽度为 {{ layOutSet.img_width }} 像素、高度
                  {{
                    layOutSet.img_height == 0
                      ? '不限制'
                      : layOutSet.img_height + '像素的图片；支持格式gif，jpg，png。'
                  }}</span
                >
                <Button>
                  <div class="flex items-center">
                    <UploadOutlined />
                    <span class="ml-2">上传图片</span>
                  </div>
                </Button>
              </div>
            </div>

            <div v-if="record.type == 'operation'">
              <Form ref="formRef" class="!mt-[20px]">
                <FormItem
                  label="主分类名称"
                  extra="最多输入10个字"
                  :label-col="{ span: 4 }"
                  :wrapper-col="{ span: 14 }"
                >
                  <Input
                    type="text"
                    placeholder="请输入主分类名称"
                    v-model:value="formModel.main_category.title"
                    :maxlength="10"
                  ></Input>
                </FormItem>
                <FormItem label="一级分类" :label-col="{ span: 4 }" :wrapper-col="{ span: 14 }">
                  <Select
                    v-model:value="formModel.main_category.link_value"
                    placeholder="请选择一级分类"
                    :options="categoryList"
                  >
                  </Select>
                  <div>
                    <div class="extra_item" v-if="formModel.main_category.link_type">
                      <div
                        class="flex items-center"
                        v-if="formModel.main_category.link_type == 'url'"
                      >
                        <Input
                          placeholder="请输入链接地址"
                          v-model:value="formModel.main_category.link_value"
                        ></Input>
                      </div>
                      <div
                        class="flex items-center"
                        v-else-if="formModel.main_category.link_type == 'keyword'"
                      >
                        <Input
                          placeholder="请输入关键字"
                          v-model:value="formModel.main_category.link_value"
                        ></Input>
                      </div>
                      <div
                        class="flex items-start item_goods"
                        v-else-if="formModel.main_category.link_type == 'goods'"
                      >
                        <img
                          :src="formModel.main_category.info.mainImage"
                          alt=""
                          class="extra_item_goods_img"
                        />
                        <div class="area_text_name ml-2">{{
                          formModel.main_category.info.goodsName
                        }}</div>
                      </div>
                      <div class="area_text_name" v-else-if="formModel.main_category.link_value">
                        {{ singleDiy_type_name_map[formModel.main_category.link_type] }}：{{
                          formModel.main_category.link_value
                        }}
                      </div>
                    </div>
                  </div>
                </FormItem>

                <div v-for="(sub, subIdx) in formModel.sub_category" :key="subIdx">
                  <FormItem
                    :label="`子分类名称${subIdx + 1}`"
                    extra="最多输入10个字"
                    :label-col="{ span: 6 }"
                    :wrapper-col="{ span: 12 }"
                  >
                    <Input
                      type="text"
                      placeholder="请输入子分类名称"
                      v-model:value="sub.title"
                      :maxlength="10"
                    ></Input>
                  </FormItem>
                  <FormItem
                    :label="`子分类操作${subIdx + 1}`"
                    :label-col="{ span: 6 }"
                    :wrapper-col="{ span: 12 }"
                  >
                    <Select
                      :value="sub.link_type"
                      placeholder="请选择链接类型"
                      @select="(e) => sldHandSeleChange(e, sub, subIdx)"
                      :options="current_diy_link_type"
                    >
                    </Select>
                    <div>
                      <div class="extra_item" v-if="sub.link_type">
                        <div class="flex items-center" v-if="sub.link_type == 'url'">
                          <Input
                            placeholder="请输入链接地址"
                            v-model:value="sub.link_value"
                          ></Input>
                        </div>
                        <div class="flex items-center" v-else-if="sub.link_type == 'keyword'">
                          <Input placeholder="请输入关键字" v-model:value="sub.link_value"></Input>
                        </div>
                        <div
                          class="flex items-start item_goods"
                          v-else-if="sub.link_type == 'goods'"
                        >
                          <img :src="sub.info.mainImage" alt="" class="extra_item_goods_img" />
                          <div class="area_text_name ml-2">{{ sub.info.goodsName }}</div>
                        </div>
                        <div class="area_text_name" v-else-if="sub.link_value">
                          {{ singleDiy_type_name_map[sub.link_type] }}：{{ sub.link_value }}
                        </div>
                      </div>
                    </div>
                  </FormItem>
                </div>
              </Form>
            </div>
          </template>
        </template>
      </Table>

      <template #footer>
        <div v-if="show_footer">
          <Button key="back" @click="setModal(false)">取消</Button>,
          <Button key="submit" type="primary" :loading="submiting" @click="sldConfirm">确定</Button>
        </div>
      </template>
    </Modal>

    <SldSelGoodsSingleDiy
      v-bind="modalProps"
      :modalVisible="singleDiy_modalVisible"
      @cancleEvent="sldHandleLinkCancle"
      @confirm-event="sldHandleLinkConfirm"
    />

    <SldMaterialImgs
      ref="sldMaterialImgsRef"
      :maxUploadNum="1"
      @confirm-material="confirmMaterial"
    ></SldMaterialImgs>
  </div>
</template>

<script setup>
  import { reactive, ref, computed, unref, watch } from 'vue';
  import { Modal, Form, FormItem, Button, Table, Select, Input } from 'ant-design-vue';
  import { UploadOutlined } from '@ant-design/icons-vue';
  import {
    new_diy_link_type,
    new_m_diy_link_type,
    singleDiy_type_name_map,
    new_supplier_diy_link_type,
  } from '@/utils/utils';
  import { resolveImageBindId } from '@/utils';
  import {
    setSldSelProps,
    handleDiyGoodsConfirm,
    SldSelGoodsSingleDiy,
  } from '@/components/SldSelGoodsSingleDiy';
  import SldMaterialImgs from '@/components/SldMaterialFiles/sldMaterialImgs.vue';
  import { cloneDeep } from 'lodash-es';
  import { getCategoryList } from '/@/api/manage/manage';
  const props = defineProps({
    title: String,
    width: Number,
    visible: {
      type: Boolean,
      default: false,
    },
    show_footer: {
      type: Boolean,
      default: true,
    },
    modalTip: {
      type: Array,
      default: () => [],
    },
    imgLayout: {
      type: Object,
      default: () => {},
    },

    content: {
      type: Array,
      default: () => [],
    },

    client: {
      type: String,
      default: 'pc',
    },
  });

  const emit = defineEmits(['confirm', 'cancel']);

  const formItemLayoutModal = {
    labelCol: {
      span: 4,
    },
    wrapperCol: {
      span: 14,
    },
  };

  const formRef = ref();

  const singleDiy_modalVisible = ref(false);

  const modalVisible_local = ref(false);

  const submiting = ref(false);

  const sldMaterialImgsRef = ref();

  // @ts-ignore
  const selectImageData = reactive({
    ids: [0],
    data: [{}],
    clearAll() {
      this.ids = [];
      this.data = [];
    },
  }); //产品图片数据

  //分类图片的基本宽高设置
  const layOutSet = reactive({
    img_width: 150,
    img_height: 150,
    show_width: 150,
    show_height: 150,
  });

  const formModel = reactive({
    img: {},
    main_category: {
      title: '',
      link_type: '',
      link_value: '',
      info: {},
    },
    sub_category: [
      {
        title: '',
        link_type: '',
        link_value: '',
        info: {},
      },
    ],
  });

  const uploadEvent = () => {
    unref(sldMaterialImgsRef).setMaterialModal(true, selectImageData);
  };

  //下拉操作选项
  const current_diy_link = computed(() => {
    if (props.client == 'supplier') {
      return new_supplier_diy_link_type;
    }
    return props.client == 'mobile' ? new_m_diy_link_type : new_diy_link_type;
  });
  const current_diy_link_type = computed(() => current_diy_link.value.formatOptions());

  const modalProps = ref({});
  const categoryList = ref([]);

  //表格数据源
  const originData = Array(3).fill([
    {
      key: 'img',
      name: '图片',
      value: '',
      imgPath: '',
      type: 'img',
      required: true,
    },
    {
      key: 'link_type',
      name: '操作',
      value: '',
      type: 'link_type',
    },
  ]);

  const dataSource_list = ref(cloneDeep(originData));
  const sele_index = ref(0);

  const setFormModel = () => {
    let [{ img }, { main_category, sub_category }] = unref(dataSource_list)[unref(sele_index)];
    formModel.main_category = main_category;
    if (categoryList.value.length) {
      formModel.main_category.link_value = formModel.main_category.link_value
        ? formModel.main_category.link_value
        : categoryList.value[0].value;
    }
    formModel.sub_category = sub_category;
    formModel.img = img;
  };

  const columns = [
    {
      align: 'right',
      width: 150,
      dataIndex: 'name',
    },
    {
      align: 'left',
      dataIndex: 'type',
    },
  ];

  const img_list = ref([]);

  const current_dataSource = computed(() => dataSource_list.value[unref(sele_index)]);

  //删除该图片对应的数据
  const del_img = (index) => {
    img_list.value[index].imgUrl = '';
    img_list.value[index].imgPath = '';
    let [{ img }] = unref(dataSource_list)[unref(sele_index)];
    img.imgUrl = '';
    img.imgPath = '';
  };

  const formSelectImageData = (target) => {
    selectImageData.ids = [resolveImageBindId(target.imgPath)];
    selectImageData.data = [
      {
        bindId: resolveImageBindId(target.imgPath),
        checked: true,
        filePath: target.imgPath,
        fileUrl: target.value,
        fileType: 1,
      },
    ];
  };

  //选择图片操作
  const seleCurData = (index) => {
    sele_index.value = index;
    let temp = unref(img_list)[index];
    selectImageData.clearAll();
    if (temp.imgUrl) {
      formSelectImageData(temp);
    } else {
      selectImageData.clearAll();
    }

    setFormModel();
  };

  const setImgLayOut = () => {
    let { show_width, show_height, width, height } = props.imgLayout;
    if (show_height) layOutSet.show_height = show_height;
    if (show_width) layOutSet.show_width = show_width;
    layOutSet.img_height = height;
    layOutSet.img_width = width;
  };

  const setContent = () => {
    let { content } = props;
    selectImageData.clearAll();
    sele_index.value = 0;
    // @ts-ignore
    img_list.value = content.map((i) => i.img);

    function reformat(item) {
      let { img, main_category, sub_category } = item;
      let tmp_info = [];
      tmp_info.push({
        name: '图片',
        type: 'img',
        required: true,
        img: {
          imgUrl: img.imgUrl,
          imgPath: img.imgPath,
        },
      });
      tmp_info.push({
        name: '操作',
        type: 'operation',
        main_category,
        sub_category,
      });
      return tmp_info;
    }
    dataSource_list.value = content.map(reformat);
    setFormModel();
  };

  watch(
    () => props,
    () => {
      setImgLayOut();
      setContent();
    },
    {
      immediate: true,
    },
  );

  watch(
    () => props.visible,
    () => {
      setModal(props.visible);
    },
  );

  //选择下拉框事件
  let current_select_index = -1;
  const sldHandSeleChange = (val, instance, index = -1) => {
    if (index >= 0) {
      formModel.sub_category[index].link_type = val;
      formModel.sub_category[index].link_value = '';
      current_select_index = index;
    } else {
      formModel.main_category.link_type = val;
      formModel.main_category.link_value = '';
    }
    if (unref(current_diy_link)[val].require_select_modal) {
      modalProps.value = setSldSelProps(val, 'default', props.client);
      singleDiy_modalVisible.value = true;
    }
  };

  //选择商品或者分类取消事件
  const sldHandleLinkCancle = () => {
    if (current_select_index >= 0) {
      formModel.sub_category[current_select_index].link_type = '';
    } else {
      formModel.main_category.link_type = '';
    }
    current_select_index = -1;
    singleDiy_modalVisible.value = false;
  };

  const getCateList = async () => {
    const res = await getCategoryList({ categoryId: 0, pageSize: 100 });
    if (res.state == 200) {
      categoryList.value = res.data.list.map((item) => ({
        label: item.categoryName,
        value: item.categoryId,
      }));
      formModel.main_category.link_value = formModel.main_category.link_value
        ? formModel.main_category.link_value
        : categoryList.value[0].value;
    }
  };

  const sldHandleLinkConfirm = (e) => {
    let link_type =
      current_select_index >= 0
        ? formModel.sub_category[current_select_index].link_type
        : formModel.main_category.link_type;

    let item = handleDiyGoodsConfirm(link_type, e, 'pc');
    if (current_select_index >= 0) {
      formModel.sub_category[current_select_index].info = item.info;
      formModel.sub_category[current_select_index].link_value = item.link_value;
    } else {
      formModel.main_category.info = item.info;
      formModel.main_category.link_value = item.link_value;
    }
    current_select_index = -1;
    singleDiy_modalVisible.value = false;
  };

  const setModal = (bool) => {
    modalVisible_local.value = bool;
    if (!bool) {
      emit('cancel');
      submiting.value = false;
    } else {
      if (!categoryList.value.length) {
        getCateList();
      }
    }
  };

  const sldConfirm = () => {
    let emitData = unref(dataSource_list).map((item) => {
      let [image, operation] = item;
      return {
        img: image.img,
        main_category: operation.main_category,
        sub_category: operation.sub_category,
      };
    });
    emit('confirm', emitData);
  };

  const confirmMaterial = (val) => {
    let [img] = val.data;
    let temp = unref(img_list)[unref(sele_index)];
    temp.imgUrl = img?.fileUrl;
    temp.imgPath = img?.filePath;

    formModel.img.imgUrl = img?.fileUrl;
    formModel.img.imgPath = img?.filePath;
  };
</script>

<style lang="less">
  @import './index.less';

  .extra_item {
    margin-top: 12px;

    .item_goods {
      padding: 10px;
      border: 1px solid #d9d9d9;
    }

    .extra_item_goods_img {
      width: 50px;
      height: 50px;
    }

    .extra_item_label {
      margin-right: 10px;
      white-space: nowrap;
    }
  }
</style>
