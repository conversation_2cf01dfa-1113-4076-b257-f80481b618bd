<template>
  <div class="prew_item prew_item_order" :style="{ backgroundImage: `url(${backgroundImage})` }">
    <div class="flex_row_start_start diy_style_price" :style="{ color: currentColor.priceColor }">
      ￥<span>299</span>.00
    </div>
    <div class="flex_row_between_center diy_style_order_item">
      <div class="diy_style_order_item_left">商品金额</div>
      <div
        class="flex_row_start_start diy_style_order_item_right diy_style_price"
        :style="{ color: currentColor.priceColor }"
      >
        <span>￥</span>299.00
      </div>
    </div>
    <div class="flex_row_between_center diy_style_order_item">
      <div class="diy_style_order_item_left">运费</div>
      <div class="diy_style_order_item_right desc" :style="{ color: currentColor.priceColor }"
        >免运费</div
      >
    </div>
    <div class="flex_row_end_center diy_style_order_item">
      <div
        class="flex_row_start_start diy_style_order_item_right diy_style_price"
        :style="{ color: currentColor.priceColor }"
      >
        <span>-￥</span>30.00
      </div>
    </div>
    <div class="flex_row_end_center diy_style_order_bottom">
      <div class="diy_style_order_bottom_title">待付款金额:</div>
      <div
        class="flex_row_start_start diy_style_order_bottom_price"
        :style="{ color: currentColor.priceColor }"
      >
        ￥<span>269</span>.00
      </div>
      <div class="diy_style_order_bottom_btn" :style="{ background: currentColor.mainColor }"
        >提交订单</div
      >
    </div>
  </div>
</template>

<script>
  import { defineComponent } from 'vue';
  import { getImagePath } from '/@/utils';
  export default defineComponent({
    props: {
      currentColor: Object,
    },
    setup(props) {
      const backgroundImage = getImagePath('images/diy_style_order.png');
      return { backgroundImage };
    },
  });
</script>

<style lang="less" scoped>
  .prew_item {
    position: relative;
    width: 375px;
    height: 666px;
    margin-right: 50px;
    overflow: hidden;
    border-radius: 10px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    box-shadow: 0 0 12px 0 rgb(129 129 129 / 30%);
    cursor: default;

    &:last-of-type {
      margin-right: 0;
    }

    &.prew_item_order {
      .diy_style_price {
        position: absolute;
        z-index: 2;
        top: 260px;
        left: 131px;
        align-items: baseline;
        font-family: 'PingFang SC';
        font-size: 13px;
        font-weight: 700;

        span {
          font-size: 20px;
        }
      }

      .diy_style_order_item {
        position: absolute;
        width: 100%;
        padding: 0 10px;
        color: #333;
        font-family: 'PingFang SC';
        font-size: 14px;
        font-weight: 500;

        .diy_style_order_item_left {
          font-size: 13px;
          font-weight: 500;
        }

        .diy_style_order_item_right {
          &.diy_style_price {
            position: relative;
            top: unset;
            left: unset;
            font-size: 15px;

            span {
              font-size: 12px;
              font-weight: 600;
            }
          }

          &.desc {
            font-size: 13px;
            font-weight: 600;
          }
        }

        &:nth-child(2) {
          top: 421px;
        }

        &:nth-child(3) {
          top: 470px;
        }

        &:nth-child(4) {
          top: 567px;
        }
      }

      .diy_style_order_bottom {
        position: absolute;
        z-index: 2;
        right: 10px;
        bottom: 10px;
        font-family: 'PingFang SC';
        font-size: 14px;
        font-weight: 500;

        .diy_style_order_bottom_title {
          margin-right: 6px;
          color: #333;
          line-height: 32px;
        }

        .diy_style_order_bottom_price {
          align-items: baseline;
          font-size: 13px;
          font-weight: 700;

          span {
            font-size: 18px;
          }
        }

        .diy_style_order_bottom_btn {
          width: 102px;
          height: 34px;
          margin-left: 6px;
          border-radius: 17px;
          color: #fff;
          line-height: 32px;
          text-align: center;
        }
      }
    }
  }
</style>
