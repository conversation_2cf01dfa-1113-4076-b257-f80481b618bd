<template>
  <div class="prew_item prew_item_goods" :style="{ backgroundImage: `url(${backgroundImage})` }">
    <div class="flex_row_start_start diy_style_price" :style="{ color: currentColor.priceColor }">
      ￥<span>299</span>.00
    </div>
    <div class="diy_style_origin_price">￥399.00</div>
    <div class="diy_style_location">
      <AliSvgIcon
        :fillColor="currentColor.mainColor"
        width="18px"
        height="28px"
        iconName="iconweizhi-kebianse"
      />
    </div>
    <div class="diy_style_cart_num" :style="{ background: currentColor.mainColor }">6</div>
    <div class="diy_style_add" :style="{ background: currentColor.subColor }">加入购物车</div>
    <div class="diy_style_buy" :style="{ background: currentColor.mainLinearColor }">立即购买</div>
  </div>
</template>

<script>
  import { defineComponent } from 'vue';
  import { getImagePath } from '/@/utils';
  export default defineComponent({
    props: {
      currentColor: Object,
    },
    setup(props) {
      const backgroundImage = getImagePath('images/diy_style_goods.png');
      return { backgroundImage };
    },
  });
</script>

<style lang="less" scoped>
  .prew_item {
    position: relative;
    width: 375px;
    height: 666px;
    margin-right: 50px;
    overflow: hidden;
    border-radius: 10px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    box-shadow: 0 0 12px 0 rgb(129 129 129 / 30%);
    cursor: default;

    &:last-of-type {
      margin-right: 0;
    }

    &.prew_item_goods {
      .diy_style_price {
        position: absolute;
        z-index: 2;
        top: 380px;
        left: 7px;
        align-items: baseline;
        font-family: 'PingFang SC';
        font-size: 13px;
        font-weight: 700;

        span {
          font-size: 22px;
        }
      }

      .diy_style_origin_price {
        position: absolute;
        z-index: 2;
        top: 408px;
        left: 8px;
        color: #999;
        font-size: 12px;
        text-decoration: line-through;
      }

      .diy_style_location {
        position: absolute;
        z-index: 2;
        top: 547px;
        left: 51px;
      }

      .diy_style_cart_num {
        position: absolute;
        z-index: 2;
        bottom: 28px;
        left: 131px;
        width: 16px;
        height: 16px;
        transform: scale(0.9);
        border-radius: 50%;
        color: #fff;
        font-size: 12px;
        line-height: 16px;
        text-align: center;
      }

      .diy_style_add,
      .diy_style_buy {
        position: absolute;
        z-index: 2;
        bottom: 6px;
        width: 102px;
        height: 34px;
        color: #fff;
        font-family: 'PingFang SC';
        font-size: 14px;
        font-weight: 500;
        line-height: 34px;
        text-align: center;
      }

      .diy_style_add {
        right: 110px;
        border-top-left-radius: 17px;
        border-bottom-left-radius: 17px;
      }

      .diy_style_buy {
        right: 8px;
        border-top-right-radius: 17px;
        border-bottom-right-radius: 17px;
      }
    }
  }
</style>
