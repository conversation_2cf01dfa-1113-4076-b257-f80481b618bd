<template>
  <div class="prew_item prew_item_confirm" :style="{ backgroundImage: `url(${backgroundImage})` }">
    <div class="flex_row_start_start diy_style_price" :style="{ color: currentColor.priceColor }">
      ￥<span>299</span>.00
    </div>
    <div class="flex_row_start_center diy_style_type_list">
      <div
        class="diy_style_type_item"
        :style="{
          color: currentColor.mainColor,
          background: currentColor.mainOpacityColor,
          borderColor: currentColor.mainColor,
        }"
        >藕粉色</div
      >
      <div class="diy_style_type_item">冰岛蓝</div>
    </div>
    <div class="diy_style_buy" :style="{ background: currentColor.mainLinearColor }">立即购买</div>
  </div>
</template>

<script>
  import { defineComponent } from 'vue';
  import { getImagePath } from '/@/utils';
  export default defineComponent({
    props: {
      currentColor: Object,
    },
    setup(props) {
      const backgroundImage = getImagePath('images/diy_style_confirm.png');
      return { backgroundImage };
    },
  });
</script>

<style lang="less" scoped>
  .prew_item {
    position: relative;
    width: 375px;
    height: 666px;
    margin-right: 50px;
    overflow: hidden;
    border-radius: 10px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    box-shadow: 0 0 12px 0 rgb(129 129 129 / 30%);
    cursor: default;

    &:last-of-type {
      margin-right: 0;
    }

    &.prew_item_confirm {
      .diy_style_price {
        position: absolute;
        z-index: 2;
        top: 338px;
        left: 104px;
        align-items: baseline;
        font-family: 'PingFang SC';
        font-size: 13px;
        font-weight: 700;

        span {
          font-size: 22px;
        }
      }

      .diy_style_type_list {
        position: absolute;
        z-index: 2;
        top: 454px;
        left: 14px;

        .diy_style_type_item {
          width: 65px;
          height: 32px;
          margin-right: 10px;
          transform: scale(0.95);
          border: 1px solid #f6f6f6;
          border-radius: 16px;
          background: #f6f6f6;
          color: #333;
          font-family: 'PingFang SC';
          font-size: 14px;
          font-weight: 500;
          line-height: 30px;
          text-align: center;
        }
      }

      .diy_style_buy {
        position: absolute;
        z-index: 2;
        bottom: 6px;
        left: 17px;
        width: 344px;
        height: 38px;
        border-radius: 20px;
        color: #fff;
        font-family: 'PingFang SC';
        font-size: 15px;
        font-weight: 500;
        line-height: 38px;
        text-align: center;
      }
    }
  }
</style>
