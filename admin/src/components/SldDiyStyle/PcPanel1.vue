<template>
  <div class="prew_item_goods" :style="{ backgroundImage: `url(${backgroundImage})` }">
    <div class="prew_item_follow" :style="{ background: currentColor.mainColor }">关注</div>
    <div class="prew_item_score" :style="{ color: currentColor.mainColor }">
      <span>5.0</span>
      <span :style="{ borderTopColor: currentColor.mainColor }"></span>
    </div>
    <div class="flex_row_start_start top_search">
      <div
        class="flex_row_start_center top_search_left"
        :style="{ borderColor: currentColor.mainColor }"
      >
        <span>请输入关键词</span>
      </div>
      <div class="top_search_right" :style="{ background: currentColor.mainColor }">搜索</div>
    </div>
    <div class="top_search_store">搜本店</div>
    <div class="top_cart_img">
      <AliSvgIcon
        :fillColor="currentColor.mainColor"
        width="16px"
        height="16px"
        iconName="icongouwuche1"
      />
    </div>
    <div class="top_cart_num" :style="{ background: currentColor.mainColor }">3</div>
    <div class="flex_row_start_center top_search_right_store">
      <div>请输入...</div>
      <div
        :style="{
          background: currentColor.mainColor,
        }"
        >搜索</div
      >
    </div>
    <div
      class="top_store_label"
      :style="{
        background: currentColor.mainColor,
      }"
      >自营</div
    >
    <div class="top_kefu">
      <AliSvgIcon
        :fillColor="currentColor.mainColor"
        width="17px"
        height="17px"
        iconName="iconkefu2"
      />
    </div>
    <div class="goods_desc" :style="{ color: currentColor.priceColor }">运动不累 轻松瘦身</div>
    <div class="flex_row_start_start goods_price" :style="{ color: currentColor.priceColor }">
      ￥<span>499</span>.00<span>￥699.00</span>
    </div>
    <div class="goods_salenum"> 销量<span :style="{ color: currentColor.priceColor }">0</span></div>
    <div
      class="goods_coupon"
      :style="{
        color: currentColor.mainColor,
        borderColor: currentColor.mainColor,
        backgroundColor: currentColor.mainOpacityColor,
      }"
      >满300元减20元</div
    >
    <div class="flex_row_start_center goods_type">
      <div
        class="goods_type_item"
        :style="{
          borderColor: currentColor.mainColor,
        }"
      >
        白色
        <AliSvgIcon
          :fill="currentColor.mainColor"
          width="16px"
          height="16px"
          iconName="iconPC-biankuangxuanzhong-kebianse"
          :style="{ position: 'absolute', right: '-2px', bottom: '-3px' }"
        />
      </div>
      <div class="goods_type_item">原木色</div>
    </div>
    <div
      class="goods_selected"
      :style="{
        borderColor: currentColor.mainColor,
      }"
    ></div>
    <div class="flex_row_start_center left_btn">
      <AliSvgIcon
        :fillColor="currentColor.mainColor"
        width="18px"
        height="18px"
        iconName="iconPC-shoucang-kebianse"
      />
      <span>收藏</span>
      <AliSvgIcon
        :fillColor="currentColor.mainColor"
        width="18px"
        height="18px"
        iconName="iconPC-fenxiang-kebianse"
      />
      <span>分享</span>
    </div>
    <div class="flex_row_start_center btn_list">
      <div
        class="btn_list_buy"
        :style="{
          color: currentColor.mainColor,
          borderColor: currentColor.mainColor,
          background: currentColor.mainOpacityColor || '#F6F6F6',
        }"
        >立即购买</div
      >
      <div
        class="flex_row_center_center btn_list_add"
        :style="{
          borderColor: currentColor.mainColor,
          background: currentColor.mainColor,
        }"
      >
        <AliSvgIcon
          fillColor="#FFFFFF"
          width="20"
          height="20"
          iconName="iconPC-gouwuche-kebianse"
        />
        <span>加入购物车</span>
      </div>
    </div>
    <div class="right_price" :style="{ color: currentColor.mainColor }">
      <span>￥499.00</span>
      <span>￥102.00</span>
    </div>
  </div>
</template>

<script>
  import { defineComponent } from 'vue';
  import { getImagePath } from '/@/utils';
  export default defineComponent({
    props: {
      currentColor: Object,
    },
    setup(props) {
      const backgroundImage = getImagePath('images/diy_style_pc_goods.png');
      return { backgroundImage };
    },
  });
</script>

<style lang="less" scoped>
  .prew_item_goods {
    position: relative;
    width: 1125px;
    height: 696px;
    overflow: hidden;
    border-radius: 4px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    box-shadow: 0 0 12px 0 rgb(129 129 129 / 30%);
    cursor: default;

    .prew_item_follow {
      position: absolute;
      z-index: 2;
      top: 32px;
      left: 305px;
      width: 42px;
      height: 28px;
      border-radius: 14px;
      color: #fff;
      font-family: 'Microsoft YaHei';
      font-size: 13px;
      font-weight: 400;
      line-height: 28px;
      text-align: center;
    }

    .prew_item_score {
      position: absolute;
      z-index: 2;
      top: 46px;
      left: 256px;

      span {
        &:nth-child(1) {
          font-family: 'Microsoft YaHei';
          font-size: 13px;
          font-weight: 400;
        }

        &:nth-child(2) {
          display: inline-block;
          position: relative;
          z-index: 2;
          top: 4px;
          left: 4px;
          border: 5px solid transparent;
          border-top: 6px solid;
        }
      }
    }

    .top_search {
      position: absolute;
      z-index: 2;
      top: 29px;
      left: 425px;

      .top_search_left {
        width: 300px;
        height: 32px;
        border: 2px solid;
        background: #fff;
        line-height: 32px;

        span {
          margin-left: 10px;
          color: #999;
          font-family: 'Microsoft YaHei';
          font-size: 12px;
          font-weight: 400;
        }
      }

      .top_search_right {
        width: 90px;
        height: 32px;
        color: #fff;
        font-family: 'Microsoft YaHei';
        font-size: 15px;
        font-weight: 400;
        line-height: 32px;
        text-align: center;
      }
    }

    .top_search_store {
      position: absolute;
      z-index: 2;
      top: 29px;
      left: 820px;
      width: 90px;
      height: 32px;
      background: #333;
      color: #fff;
      font-family: 'Microsoft YaHei';
      font-size: 15px;
      font-weight: 400;
      line-height: 32px;
      text-align: center;
    }

    .top_cart_img {
      position: absolute;
      z-index: 2;
      top: 38px;
      right: 154px;
    }

    .top_cart_num {
      position: absolute;
      z-index: 2;
      top: 38px;
      right: 55px;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      color: #fff;
      font-size: 12px;
      line-height: 16px;
      text-align: center;
    }

    .top_search_right_store {
      position: absolute;
      z-index: 2;
      top: 181px;
      right: 18px;
      transform: scale(0.85);

      div {
        &:nth-child(1) {
          width: 160px;
          height: 20px;
          padding-left: 6px;
          border-radius: 2px 0 0 2px;
          background: #fff;
          color: #999;
          font-family: 'Microsoft YaHei';
          font-size: 12px;
          font-weight: 400;
        }

        &:nth-child(2) {
          width: 32px;
          height: 20px;
          border-radius: 2px;
          color: #fff;
          font-family: 'Microsoft YaHei';
          font-size: 12px;
          font-weight: 400;
          line-height: 20px;
          text-align: center;
        }
      }
    }

    .top_store_label {
      position: absolute;
      z-index: 2;
      top: 217px;
      right: 214px;
      width: 31px;
      height: 17px;
      transform: scale(0.9);
      border-radius: 2px;
      color: #fff;
      font-family: 'Microsoft YaHei';
      font-size: 12px;
      font-weight: 400;
      line-height: 18px;
      text-align: center;
    }

    .top_kefu {
      position: absolute;
      z-index: 2;
      top: 217px;
      right: 125px;
    }

    .goods_desc {
      position: absolute;
      z-index: 2;
      top: 285px;
      left: 375px;
      font-family: 'Microsoft YaHei';
      font-size: 13px;
      font-weight: 400;
    }

    .goods_price {
      position: absolute;
      z-index: 2;
      top: 326px;
      left: 444px;
      align-items: baseline;
      font-family: 'PingFang SC';
      font-size: 13px;
      font-weight: 700;

      span {
        &:nth-child(1) {
          font-size: 22px;
        }

        &:nth-child(2) {
          margin-left: 10px;
          color: #666;
          font-family: 'Microsoft YaHei';
          font-size: 12px;
          font-weight: 400;
          text-decoration: line-through;
        }
      }
    }

    .goods_salenum {
      position: absolute;
      z-index: 2;
      top: 334px;
      right: 220px;
      color: #999;
      font-family: 'Microsoft YaHei';
      font-size: 13px;
      font-weight: 400;

      span {
        margin-left: 3px;
      }
    }

    .goods_coupon {
      position: absolute;
      z-index: 2;
      top: 366px;
      left: 446px;
      width: 112px;
      height: 25px;
      border: 1px solid;
      border-radius: 3px;
      font-family: 'Microsoft YaHei';
      font-size: 13px;
      font-weight: 400;
      line-height: 24px;
      text-align: center;
    }

    .goods_more {
      position: absolute;
      z-index: 2;
      top: 295px;
      right: 73px;
      font-family: 'Microsoft YaHei';
      font-size: 12px;
      font-weight: 400;
    }

    .goods_type {
      position: absolute;
      z-index: 2;
      top: 503px;
      left: 444px;

      .goods_type_item {
        position: relative;
        height: 26px;
        margin-right: 10px;
        padding: 0 12px;
        overflow: hidden;
        border: 1px solid #dfdfdf;
        border-radius: 3px;
        color: #333;
        font-family: 'Microsoft YaHei';
        font-size: 12px;
        font-weight: 400;
        line-height: 24px;
        text-align: center;
      }
    }

    .goods_selected {
      position: absolute;
      z-index: 2;
      bottom: 74px;
      left: 68px;
      width: 50px;
      height: 50px;
      border: 1px solid;
    }

    .left_btn {
      position: absolute;
      z-index: 2;
      bottom: 32px;
      left: 56px;

      span {
        margin-right: 10px;
        margin-left: 6px;
        color: #333;
        font-family: 'Microsoft YaHei';
        font-size: 12px;
        font-weight: 400;
        line-height: 20px;
      }
    }

    .btn_list {
      position: absolute;
      z-index: 2;
      bottom: 37px;
      left: 445px;
      font-family: 'Microsoft YaHei';
      font-size: 16px;
      font-weight: 400;

      .btn_list_buy {
        width: 150px;
        height: 42px;
        margin-right: 20px;
        border: 1px solid;
        border-radius: 4px;
        line-height: 40px;
        text-align: center;
      }

      .btn_list_add {
        width: 150px;
        height: 42px;
        border-radius: 4px;
        line-height: 40px;
        text-align: center;

        span {
          margin-left: 6px;
          color: #fff;
        }
      }
    }

    .right_price {
      font-family: 'Microsoft YaHei';
      font-size: 12px;
      font-weight: bold;

      span {
        &:nth-child(1) {
          position: absolute;
          top: 440px;
          right: 92px;
        }

        &:nth-child(2) {
          position: absolute;
          top: 620px;
          right: 92px;
        }
      }
    }
  }
</style>
