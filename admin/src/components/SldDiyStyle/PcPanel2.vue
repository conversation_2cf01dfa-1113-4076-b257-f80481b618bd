<template>
  <div class="prew_item_list" :style="{ backgroundImage: `url(${backgroundImage})` }">
    <div class="flex_row_start_start top_search">
      <div
        class="flex_row_start_center top_search_left"
        :style="{ borderColor: currentColor.mainColor }"
      >
        <span>请输入关键词</span>
      </div>
      <div class="top_search_right" :style="{ background: currentColor.mainColor }">搜索</div>
    </div>
    <div class="top_cart_img">
      <AliSvgIcon
        :fillColor="currentColor.mainColor"
        width="16px"
        height="16px"
        iconName="icongouwuche1"
      />
    </div>
    <div class="top_cart_num" :style="{ background: currentColor.mainColor }">3</div>
    <div
      class="flex_row_center_center top_category"
      :style="{ background: currentColor.mainColor }"
    >
      <AliSvgIcon fillColor="#FFFFFF" width="28px" height="28px" iconName="icongengduo" />
      <span>产品分类</span>
    </div>
    <div class="lines" :style="{ background: currentColor.mainColor }"></div>
    <div class="category_content"
      >分类-"<span :style="{ color: currentColor.mainColor }">居家生活</span>"筛选结果</div
    >
    <div class="fiilter" :style="{ color: currentColor.mainColor }">综合</div>
    <div class="goods_list">
      <div
        :key="index"
        class="goods_item"
        :style="{ color: currentColor.mainColor }"
        v-for="(item, index) in ['20.00', '29.00', '568.00', '18.00', '50.90']"
      >
        <div class="{styles.goods_item_price}" :style="{ color: currentColor.priceColor }"
          ><span>￥</span>{{ item }}</div
        >
        <div class="goods_item_label" :style="{ backgroundColor: currentColor.mainColor }"
          >自营</div
        >
        <div class="flex_Row_start_center goods_item_btn">
          <AliSvgIcon
            fillColor="{currentColor.mainColor}"
            width="14px"
            height="14px"
            iconName="icongouwuchetianjia"
          />
          <span>加入购物车</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { getImagePath } from '/@/utils';

  export default {
    props: {
      currentColor: Object,
    },
    setup() {
      const backgroundImage = getImagePath('images/diy_style_pc_list.png');

      return { backgroundImage };
    },
  };
</script>

<style lang="less" scoped>
  .prew_item_list {
    position: relative;
    width: 1125px;
    height: 696px;
    overflow: hidden;
    border-radius: 4px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    box-shadow: 0 0 12px 0 rgb(129 129 129 / 30%);
    cursor: default;

    .top_search {
      position: absolute;
      z-index: 2;
      top: 29px;
      left: 338px;

      .top_search_left {
        width: 405px;
        height: 32px;
        border: 2px solid;
        background: #fff;
        line-height: 32px;

        span {
          margin-left: 10px;
          color: #999;
          font-family: 'Microsoft YaHei';
          font-size: 12px;
          font-weight: 400;
        }
      }

      .top_search_right {
        width: 90px;
        height: 32px;
        color: #fff;
        font-family: 'Microsoft YaHei';
        font-size: 15px;
        font-weight: 400;
        line-height: 32px;
        text-align: center;
      }
    }

    .top_search_store {
      position: absolute;
      z-index: 2;
      top: 29px;
      left: 820px;
      width: 90px;
      height: 32px;
      background: #333;
      color: #fff;
      font-family: 'Microsoft YaHei';
      font-size: 15px;
      font-weight: 400;
      line-height: 32px;
      text-align: center;
    }

    .top_cart_img {
      position: absolute;
      z-index: 2;
      top: 38px;
      right: 154px;
    }

    .top_cart_num {
      position: absolute;
      z-index: 2;
      top: 38px;
      right: 55px;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      color: #fff;
      font-size: 12px;
      line-height: 16px;
      text-align: center;
    }

    .top_category {
      position: absolute;
      z-index: 2;
      top: 97px;
      left: 55px;
      width: 160px;
      height: 39px;
      color: #fff;

      span {
        margin-right: 10px;
        margin-left: 10px;
      }
    }

    .lines {
      position: absolute;
      z-index: 2;
      top: 135px;
      width: 100%;
      height: 3px;
    }

    .category_content {
      position: absolute;
      z-index: 2;
      top: 194px;
      left: 71px;
      color: #666;
      font-family: 'Microsoft YaHei';
      font-size: 12px;
      font-weight: 400;
    }

    .fiilter {
      position: absolute;
      z-index: 2;
      top: 280px;
      left: 84px;
      font-family: 'Microsoft YaHei';
      font-size: 12px;
      font-weight: 400;
    }

    .goods_list {
      .goods_item {
        position: absolute;
        z-index: 2;
        bottom: 27px;
        width: 176px;

        .goods_item_price {
          font-family: 'Microsoft YaHei';
          font-size: 16px;
          font-weight: 400;

          span {
            font-size: 14px;
          }
        }

        .goods_item_label {
          width: 34px;
          height: 18px;
          margin-top: 63px;
          border-radius: 2px;
          color: #fff;
          font-family: 'Microsoft YaHei';
          font-size: 12px;
          font-weight: 400;
          line-height: 18px;
          text-align: center;
        }

        .goods_item_btn {
          margin-top: 16px;
          margin-left: 91px;

          svg {
            position: relative;
            top: 3px;
          }

          span {
            margin-left: 3px;
            font-family: 'Microsoft YaHei';
            font-size: 12px;
            font-weight: 400;
          }
        }

        &:nth-child(1) {
          left: 66px;
        }

        &:nth-child(2) {
          left: 273px;
        }

        &:nth-child(3) {
          left: 481px;
        }

        &:nth-child(4) {
          left: 689px;
        }

        &:nth-child(5) {
          left: 897px;
        }
      }
    }
  }
</style>
