.diy_modal_tip_div {
  padding: 10px 15px;

  ul {
    margin-bottom: 0 !important;
    padding-left: 0 !important;

    li {
      padding: 2px 0;
      color: #db5609;
      font-size: 12px;
    }
  }
}

.imgs_wrap {
  display: flex;
  flex-flow: row wrap;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  margin-bottom: 10px;
  padding: 10px;
  border: 1px solid #e3e3e3;
  border-radius: 4px;
}

.adv_more_img_wrap {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  min-width: 80px;
  height: 44px;
  margin-bottom: 10px;
  margin-left: 10px;
  padding: 10px;
  border: dashed 1px #eee;
  line-height: 15px;
}

.adv_more_img_wrap:hover,
.seleImg {
  border-color: #fbcfac;
  background-color: #fff3da;
}

.adv_more_img_wrap .del_img {
  display: inline-block;
  position: absolute;
  top: 0;
  right: 0;
  padding: 2px 5px;
  background: #fe9700;
  color: #fff;
  font-size: 12px;
  cursor: pointer;
}

.adv_01_img {
  max-width: 100%;
  max-height: 100%;
  margin: 0 auto;
}

.del_img {
  display: inline-block;
  position: absolute;
  top: -1px;
  right: 0;
  padding: 0 4px;
  background: #fe9700;
  color: #fff;
  font-size: 12px;
  cursor: pointer;
}

.modal_tip_color {
  display: block;
  padding: 10px 0;
  color: #db5609;
  font-size: 12px;
}

.table_left_con {
  color: #333;
  font-size: 13px;
}

.table_left_require {
  color: red;
}

.modal_img {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  padding-left: 10px;
}

.adv_01_img_thumb {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  min-height: 50px;
  background: #efefef;
}

.adv_01_img {
  max-width: 100%;
  max-height: 100%;
  margin: 0 auto;
}

.adv_01_img_show {
  position: absolute;
  top: 0;
  left: 50%;
  width: 1920px;
  height: 457px;
  transform: translateX(-50%);
  background-repeat: no-repeat;
  background-position: 50%;
  background-size: contain;
}

.adv_01_img_thumb .del_img {
  display: inline-block;
  position: absolute;
  top: -1px;
  right: 0;
  padding: 0 4px;
  background: #fe9700;
  color: #fff;
  font-size: 12px;
  cursor: pointer;
}

.modal_tip_color {
  display: block;
  padding: 10px 0;
  color: #db5609;
  font-size: 12px;
}

.table_left_con {
  color: #333;
  font-size: 13px;
}

.table_left_require {
  color: red;
}

.adv_02_part {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  width: 1210px;
  height: 344px;
  background: #fff;
}

.adv_02_part .adv_02_left {
  display: flex;
  position: relative;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 210px;
  height: 344px;
  background-image: url('@/assets/images/pc_diy_img/adv_floor_02_left.png');
  cursor: pointer;
}

.adv_02_part .adv_02_left img {
  max-width: 100%;
  max-height: 100%;
}

.adv_02_part .adv_02_center {
  position: relative;
  width: 736px;
  height: 344px;
  overflow: hidden;
  background-image: url('@/assets/images/pc_diy_img/adv_floor_02_goods.png');
  word-break: break-all;
  white-space: normal;
}

.adv_02_part .adv_02_center .split_h {
  position: absolute;
  z-index: 2;
  top: 172px;
  left: 0;
  width: 736px;
  height: 1px;
  background-color: #f0f2f3;
}

.adv_02_part .adv_02_center .split_v {
  position: absolute;
  z-index: 2;
  top: 0;
  left: 368px;
  width: 1px;
  height: 344px;
  background-color: #f0f2f3;
}

.adv_02_part .adv_02_right {
  display: flex;
  position: relative;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  width: 253px;
  height: 344px;
  // background-image: url('@/assets/pc_diy_img/adv_floor_02_right.png');
}
