<template>
  <div>
    <Modal
      destroyOnClose
      :maskClosable="false"
      :title="props.title"
      :visible="modalVisible"
      :zIndex="props.zIndex"
      :width="props.width"
      @ok="sldConfirm"
      :footer="props.show_foot ? undefined : null"
      @cancel="sldCancle"
    >
      <Form ref="formRef" :model="data">
        <ShowMoreHelpTip
          :tipData="modal_tip"
          :marginTop="0"
          v-if="modal_tip.length > 0"
        ></ShowMoreHelpTip>
        <div class="SldDiySingleImgModal">
          <Table
            :showHeader="false"
            :columns="columns"
            :bordered="true"
            :pagination="false"
            :dataSource="data.data"
            :maxHeight="400"
            :canResize="false"
          >
            <template #bodyCell="{ column, text, record, index }">
              <template v-if="column.dataIndex == 'name'">
                <div class="table_left_con">
                  <span>{{ text }}</span>
                  <span
                    v-if="record.required != undefined && record.required"
                    class="table_left_require"
                  >
                    *
                  </span>
                </div>
              </template>
              <template v-if="column.dataIndex == 'type'">
                <template v-if="record.type == 'img'">
                  <div class="modal_img">
                    <div
                      class="adv_01_img_thumb"
                      :style="{
                         width: !show_width||show_width == 0 ? 'auto' : (`${show_width * 0.5 > 800 ? 800 : show_width * 0.5}` + 'px'),
                         height: !show_height||show_height == 0 ? 'auto' : (`${(show_width * 0.5 > 800 ? (800 * show_height / show_width) : show_height * 0.5)}` + 'px'),
                      }"
                    >
                      <img
                        :src="record.value"
                        class="adv_01_img"
                        alt=""
                        v-if="record.value != ''"
                      />
                      <AliSvgIcon
                        iconName="iconkehubiaoqian"
                        v-else
                        width="30px"
                        height="30px"
                        fillColor="#999"
                      />
                      <span @click="del_img()" class="del_img">删除</span>
                    </div>
                    <span class="modal_tip_color"
                      >此处对应上传上方选中标签项内容，要求宽度为{{ img_width }}像素、高度{{
                        img_height == 0 ? '不限制' : img_height + '像素'
                      }}的图片；支持格式gif，jpg，png。</span
                    >
                    <div>
                      <Button @click="openMaterial">
                        <div class="flex_row_center_center">
                          <UploadOutlined />
                          <span>上传图片</span>
                        </div>
                      </Button>
                    </div>
                  </div>
                </template>
                <template v-if="record.type == 'link_type'">
                  <div class="modal_img">
                    <Select
                      placeholder="请选择链接类型"
                      style="width: 120px"
                      :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                      v-model:value="record.value"
                      @change="(e) => sldHandSeleChange(e, record.key)"
                    >
                      <Select.Option
                        v-for="(item, index) in current_diy_link_type"
                        :key="index"
                        :value="item.key"
                        >{{ item.name }}</Select.Option
                      >
                    </Select>
                  </div>
                </template>
                <template v-if="record.type == 'url'">
                  <Form.Item
                    validateFirst
                    style="width: 300px"
                    :name="['data', index, 'link_value']"
                    :rules="[
                      {
                        required: record.required,
                        whitespace: true,
                        message: `请输入链接地址`,
                      },
                      {
                        validator: async (rule, value) => {
                          await validatorEmoji(rule, value);
                        },
                      },
                    ]"
                  >
                    <Input
                      :maxlength="250"
                      style="width: 300px"
                      placeholder="请输入链接地址"
                      v-model:value="record.link_value"
                    />
                  </Form.Item>
                </template>
                <template v-if="record.type == 'keyword'">
                  <Form.Item
                    validateFirst
                    style="width: 300px"
                    :name="['data', index, 'link_value']"
                    :rules="[
                      {
                        required: record.required,
                        whitespace: true,
                        message: `请输入关键字`,
                      },
                      {
                        validator: async (rule, value) => {
                          await validatorEmoji(rule, value);
                        },
                      },
                    ]"
                  >
                    <Input
                      :maxlength="250"
                      style="width: 300px"
                      placeholder="请输入关键字"
                      v-model:value="record.link_value"
                    />
                  </Form.Item>
                </template>
                <template
                  v-if="
                    record.type == 'goods' ||
                    record.type == 'category' ||
                    record.type == 'topic' ||
                    record.type == 'o2o_topic' ||
                    record.type == 'new_goods' ||
                    record.type == 'cms' ||
                    record.type == 'company_new' ||
                    record.type == 'store'
                  "
                >
                  <div>
                    <span>{{ record.value }}</span>
                  </div>
                </template>
              </template>
            </template>
          </Table>
        </div>
      </Form>
    </Modal>
    <!-- 图片素材选择 start -->
    <SldMaterialImgs
      ref="sldMaterialImgsRef"
      :visibleModal="chooseFile == 1 ? true : false"
      :maxUploadNum="1"
      :allowRepeat="false"
      :selectedData="selectImageData"
      @closeMaterial="() => closeMaterial()"
      @confirmMaterial="(val) => confirmMaterial('image', val)"
    ></SldMaterialImgs>
    <!-- 图片素材选择 end -->

    <!-- 链接选择弹窗 - start -->
    <SldSelGoodsSingleDiy
      :client="props.client ? props.client : 'pc'"
      :link_type="link_type"
      :modalVisible="modalVisibleSelLink"
      @confirm-event="seleSku"
      @cancle-event="sldHandleLinkCancle"
    />
    <!-- 链接选择弹窗 - end -->
  </div>
</template>
<script>
  export default {
    name: 'SldDiyTitleLinkModal',
  };
</script>
<script setup>
  import { ref, onMounted, computed, watch } from 'vue';
  import { useRouter } from 'vue-router';
  import {
    failTip,
    isEmptyObject,
    diy_link_type,
    // dev_supplier-start
    diy_supplier_link_type,
    // dev_supplier-end
    m_diy_spreader_link_type,
    m_diy_point_link_type,
    m_diy_link_type,
  } from '/@/utils/utils';
  import { Modal, Form, Table, Input, Select, Button } from 'ant-design-vue';
  import SldSelGoodsSingleDiy from '@/components/SldSelGoodsSingleDiy/index.vue';
  import ShowMoreHelpTip from '/@/components/ShowMoreHelpTip/index.vue';
  import { UploadOutlined } from '@ant-design/icons-vue';
  import { validatorEmoji } from '/@/utils/validate';
  import SldMaterialImgs from '@/components/SldMaterialFiles/sldMaterialImgs.vue';

  const emit = defineEmits(['cancleEvent', 'confirmEvent']);

  const sldMaterialImgsRef = ref();

  const props = defineProps({
    width: { type: Number, default: 416 }, //弹框宽度，默认416px
    height: { type: Number }, //弹框高度
    title: { type: String, required: true }, //弹框标题
    submiting: { type: Boolean, default: false }, //弹框确认按钮loading属性，默认不显示
    modalVisible: { type: Boolean, required: true }, //弹框是否显示
    content: { type: Array }, //内容
    modal_tip: { type: Array, default: [] }, //提示语
    client: { type: String, default: 'pc' },
    mode: { type: String, default: 'default' },
    type: { type: String, default: '' },
    extra: { type: Object }, //表单数据
    zIndex: { type: Number, default: 1000 }, //弹框的层级，默认为1000，最小值也为1000
    show_foot: { type: Boolean, default: true }, //是否显示弹框底部操作按钮，默认显示
  });

  //由于watch无法监听props.modalVisible，所以需要计算属性转为ref对象
  const modalVisible = computed(() => {
    return props.modalVisible;
  });
  const modal_tip = computed(() => {
    return props.modal_tip;
  });

  //下拉操作选项

  const current_diy_link_type = computed(() => {
    if (props.client == 'mobile') {
      switch (props.mode) {
        case 'integral':
          return m_diy_point_link_type();
        case 'spreader':
          return m_diy_spreader_link_type();
        default:
          return m_diy_link_type();
      }
    }
    // dev_supplier-start
    let remap_supplier_diy_link_type = diy_supplier_link_type();
    if (props.client == 'supplier') {
      return remap_supplier_diy_link_type;
    }
    // dev_supplier-end

    let remap_diy_link_type = diy_link_type();
    return remap_diy_link_type;
  });

  const img_width = ref(150);
  const img_height = ref(150);

  const show_width = ref(150);
  const show_height = ref(150);

  const formRef = ref();
  const source = ref('');
  const modalVisibleSelLink = ref(false);
  const link_type = ref('');
  const chooseFile = ref(0); //上传素材弹窗显示 1:图片 2:视频

  //提示语
  //数据
  const data = ref({
    data: [
      {
        key: 'img',
        name: '',
        value: 'https://img.alicdn.com/simba/img/TB1jsBeKYPpK1RjSZFFSuu5PpXa.jpg',
        imgPath: '/images/brand/3e56e20d-453d-4cf8-906f-a928de37ce6a.png',
        type: 'img',
        required: true,
      },
      {
        key: 'link_type',
        name: `操作`,
        value: '',
        type: 'link_type',
      },
    ],
  });

  const columns = ref([
    { dataIndex: 'name', width: 150, align: 'right' },
    { dataIndex: 'type', align: 'left' },
  ]);

  const first_flag = ref(false);
  const selectImageData = ref({ ids: [], data: [] });

  watch(modalVisible, () => {
    if (modalVisible.value) {
      if (!first_flag.value && !isEmptyObject(props.content)) {
        let ope_data = JSON.parse(JSON.stringify(props.content.data));
        let tmp_info = [];
        let selectImageDatas = { ids: [], data: [] };

        tmp_info.push({
          key: 'img',
          name: `图片`,
          value: ope_data.imgUrl != undefined ? ope_data.imgUrl : '',
          imgPath: ope_data.imgPath != undefined ? ope_data.imgPath : '',
          type: 'img',
          required: true,
          openFileDialogOnClick: false,
        });
        tmp_info.push({
          key: 'link_type',
          name: `操作`,
          value: ope_data.link_type,
          type: 'link_type',
        });

        if (ope_data.imgPath) {
          selectImageDatas = {
            ids: [Number(ope_data.imgPath.split('?bindId=')[1])],
            data: [
              {
                bindId: ope_data.imgPath.split('?bindId=')[1],
                checked: true,
                filePath: ope_data.imgPath,
                fileUrl: ope_data.imgUrl,
                fileType: 1,
              },
            ],
          };
        }

        if (ope_data.link_value != undefined && ope_data.link_value) {
          let tmp_info_new = {
            key: 'link_value',
            name: `关键字`,
            link_value: ope_data.link_value,
            value: ope_data.link_value,
            type: ope_data.link_type,
            info: ope_data.info != undefined ? ope_data.info : {},
            required: true,
          };
          tmp_info_new[ope_data.link_type] = ope_data.link_value;
          if (ope_data.link_type == 'url') {
            tmp_info_new.name = `链接地址`;
            tmp_info_new.required = true;
          } else if (ope_data.link_type == 'keyword') {
            tmp_info_new.name = `关键字`;
            tmp_info_new.required = true;
          } else if (ope_data.link_type == 'goods') {
            tmp_info_new.name = `商品名称`;
            tmp_info_new.required = true;
          } else if (ope_data.link_type == 'category') {
            tmp_info_new.name = `分类名称`;
            tmp_info_new.required = true;
          } else if (ope_data.link_type == 'topic' || ope_data.link_type == 'o2o_topic') {
            tmp_info_new.name = `专题名称`;
            tmp_info_new.required = true;
          } else if (ope_data.link_type == 'store') {
            tmp_info_new.name = `店铺名称`;
            tmp_info_new.required = true;
          }
          tmp_info.push(tmp_info_new);
        }

        //首页开屏图单独处理
        if (props.content.source != undefined && props.content.source == 'home_modal_adv') {
          if (ope_data.show_switch != undefined) {
            tmp_info.push({
              key: 'home_modal_adv_switch',
              type: 'home_modal_adv_switch',
              name: `弹出广告开关`,
              value: ope_data.show_switch,
            });
          }
          if (ope_data.show_radio_sele != undefined) {
            tmp_info.push({
              key: 'home_modal_adv_radio',
              type: 'home_modal_adv_radio',
              name: `弹出方式`,
              value: ope_data.show_radio_sele,
            });
          }
        }

        //首页轮播图数据单独处理
        if (props.content.source != undefined && props.content.source == 'home_flash') {
          //显示标题
          if (ope_data.show_title != undefined) {
            tmp_info.push({
              key: 'home_flash_title',
              type: 'home_flash_title',
              name: `标题`,
              value: ope_data.show_title,
              required: true,
            });
          }
          //显示排序
          if (ope_data.sort != undefined) {
            tmp_info.push({
              key: 'home_flash_sort',
              type: 'home_flash_sort',
              name: `排序`,
              value: ope_data.sort + '',
              required: true,
            });
          }
          //显示展示时间
          if (ope_data.range_picker != undefined) {
            tmp_info.push({
              key: 'home_flash_range_picker',
              type: 'home_flash_range_picker',
              name: `展示时间`,
              value: ope_data.range_picker,
              required: true,
            });
          }
        }

        selectImageData.value = selectImageDatas;
        data.value.data = tmp_info;
        img_width.value = props.content.width;
        img_height.value = props.content.height;

        show_width.value = props.content.show_width ? props.content.show_width : props.content.width;
        show_height.value = props.content.show_height ? props.content.show_height : props.content.height;
        source.value = props.content.source != undefined ? props.content.source : '';
      }
    }
  });

  //删除该图片对应的数据
  const del_img = () => {
    data.value.data[0].value =  '';
    data.value.data[0].imgPath =  '';
    data.value.data[0].width = '';
    data.value.data[0].height = '';
    selectImageData.value = { ids: [], data: [] }
  };


  //关闭上传素材文件
  function closeMaterial() {
    chooseFile.value = 0;
  }

  const openMaterial = () => {
    chooseFile.value = 1;
  };

  // 链接选择弹窗关闭
  const sldHandleLinkCancle = () => {
    link_type.value = '';
    let cur_data = [];
    for (let i in data.value.data) {
      if (data.value.data[i].key != 'link_value') {
        if (data.value.data[i].key == 'link_type') {
          data.value.data[i].value = '';
        }
        cur_data.push(data.value.data[i]);
      }
    }
    data.value.data = cur_data;
    modalVisibleSelLink.value = false;
  };

  const confirmMaterial = (type, val) => {
    data.value.data[0].value = val.data.length ? val.data[0].fileUrl : '';
    data.value.data[0].imgPath = val.data.length ? val.data[0].filePath : '';
    data.value.data[0].width = val.data.length ? val.data[0].width : '';
    data.value.data[0].height = val.data.length ? val.data[0].height : '';
    chooseFile.value = 0;
  };

  //操作类型选择事件
  const sldHandSeleChange = (val, key) => {
    let flag = true;
    let cur_data = [];
    for (let i in data.value.data) {
      if (data.value.data[i].key != 'link_value') {
        if (data.value.data[i].key == 'link_type') {
          data.value.data[i].value = val;
        }
        cur_data.push(data.value.data[i]);
      }
    }
    for (let i = 0; i < cur_data.length; i++) {
      if (cur_data[i].key == 'link_type') {
        if (val == 'url') {
          cur_data.splice(i + 1, 0, {
            key: 'link_value',
            name: `链接地址`,
            value: '',
            type: 'url',
            required: true,
          });
          flag = false;
        } else if (val == 'keyword') {
          cur_data.splice(i + 1, 0, {
            key: 'link_value',
            name: `关键字`,
            value: '',
            type: 'keyword',
            required: true,
          });
          flag = false;
        } else if (val == 'goods') {
          cur_data.splice(i + 1, 0, {
            key: 'link_value',
            name: `商品名称`,
            value: '',
            info: {},
            type: 'goods',
            required: true,
          });
        } else if (val == 'category') {
          cur_data.splice(i + 1, 0, {
            key: 'link_value',
            name: `分类名称`,
            value: '',
            info: {},
            type: 'category',
            required: true,
          });
        } else if (val == 'topic') {
          cur_data.splice(i + 1, 0, {
            key: 'link_value',
            name: `专题名称`,
            value: '',
            info: {},
            type: 'topic',
            required: true,
          });
        } else if (val == 'o2o_topic') {
          cur_data.splice(i + 1, 0, {
            key: 'link_value',
            name: `专题名称`,
            value: '',
            info: {},
            type: 'o2o_topic',
            required: true,
          });
        } else if (val == 'store') {
          cur_data.splice(i + 1, 0, {
            key: 'link_value',
            name: `店铺名称`,
            value: '',
            info: {},
            type: 'store',
            required: true,
          });
        } else {
          flag = false;
        }
      }
    }

    data.value.data = cur_data;
    link_type.value = val;
    modalVisibleSelLink.value = flag;
  };

  //商品或分类选中事件
  const seleSku = (val) => {
    data.value.data.map((item) => {
      if (item.type == 'goods') {
        item.value = val[0].goodsName;
        item.link_value = val[0].goodsName;
        item.info = val[0];
      } else if (item.type == 'category') {
        item.value = val.categoryName;
        item.link_value = val.categoryName;
        item.info = val;
      } else if (item.type == 'topic' || item.type == 'o2o_topic') {
        item.value = val[0].decoName;
        item.link_value = val[0].decoName;
        item.info = val[0];
      } else if (item.type == 'store') {
        item.value = val[0].storeName;
        item.info = val[0];
        item.link_value = val[0].storeName;
      }
    });
    link_type.value = '';
    modalVisibleSelLink.value = false;
  };

  const sldConfirm = () => {
    formRef.value.validate().then((res) => {
      let tmp_info = {};
      for (let i in data.value.data) {
        if (data.value.data[i].key == 'img') {
          if (!data.value.data[i].value) {
            failTip(`请上传图片`);
            return false;
          }
          tmp_info.imgUrl = data.value.data[i].value;
          tmp_info.imgPath = data.value.data[i].imgPath;
          tmp_info.width = data.value.data[i].width;
          tmp_info.height = data.value.data[i].height;
        } else if (data.value.data[i].key == 'link_type') {
          tmp_info.link_type = data.value.data[i].value ? data.value.data[i].value : '';
          tmp_info.link_value = '';
          tmp_info.info = {};
        } else if (data.value.data[i].key == 'link_value') {
          tmp_info.link_type = data.value.data[i].type;
          tmp_info.link_value = data.value.data[i].link_value;
          tmp_info.info = data.value.data[i].info ? data.value.data[i].info : {};
        }
        if (source != undefined) {
          //首页轮播图数据处理
          if (source == 'home_flash') {
            tmp_info.show_title = values.home_flash_title;
            tmp_info.sort = values.home_flash_sort;
            tmp_info.range_picker = [];
            tmp_info.range_picker = values.home_flash_range_picker;
          }
          //首页开屏图数据处理
          if (source == 'home_modal_adv') {
            tmp_info.show_switch = values.home_modal_adv_switch;
            tmp_info.show_radio_sele = values.home_modal_adv_radio;
          }
        }
      }
      emit('confirmEvent', tmp_info);
    });
  };

  const sldCancle = () => {
    emit('cancleEvent');
  };

  const router = useRouter();

  onMounted(() => {});
</script>
<style lang="less">
  @import './index.less';

  .SldDiySingleImgModal {
    .ant-form-item-with-help {
      margin-bottom: 0;
    }

    .ant-form-item {
      margin-bottom: 0;
    }

    .table_left_con {
      color: #333;
      font-size: 13px;
    }

    .table_left_require {
      color: red;
    }
  }
</style>
