<template>
  <span class="statNumVal">{{ num }}</span>
  <span class="statNumUnit" style="color: #333;">{{ unit }}</span>
</template>
<script>
  export default {
    name: 'FormatNum',
  };
</script>
<script setup>
  import { ref, computed } from 'vue';

  const props = defineProps({
    num: { type: String || Number, required: false }, 
    toFixedNum: { type: Number, required: false }, 
    color: { type: String, default: '#333' },
    unit: { type: String, default: '万' },
  });

  const unit = computed(() => {
    return props.unit;
  })

  const num = computed(() => {
    let target = props.num * 1;
    let unit = '';
    if (target) {
      if (target < 10000) {
        target = target.toFixed(props.toFixedNum);
      } else {
        target = (target / 10000).toFixed(props.toFixedNum) * 1;
      }
      let regExpInfo = /(\d{1,3})(?=(\d{3})+(?:$|\.))/g;
      target = target.toString().replace(regExpInfo, '$1,');
    } else {
      target = num;
    }
    return target;
  });

</script>
<style lang="less">
  .statNumVal {
    font-size: 28px !important;
    font-family: -apple-system,BlinkMacSystemFont,Segoe UI,Roboto,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Helvetica Neue,Helvetica,Arial,sans-serif  !important;
  }

  .statNumUnit {
    vertical-align: 1px  !important;
    font-size: 14px !important;
    font-family: -apple-system,BlinkMacSystemFont,Segoe UI,Roboto,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Helvetica Neue,Helvetica,Arial,sans-serif  !important;
  }
</style>
