<!-- 公共表单列表组件 -->
<template>
  <div class="standard_form_row">
    <Form ref="rowForm" :model="curFormData">
      <div
        v-for="(item, index) in props.data"
        :key="index"
        :style="{
          width:
            item.type == 'title'
              ? '100%'
              : item.item_width != undefined
              ? item.item_width + (typeof item.item_width == 'number' ? 'px' : '')
              : width,
        }"
      >
        <div
          v-if="item.type == 'title'"
          class="common_title"
          :style="{
            backgroundColor: item.bgColor ? item.bgColor : '',
          }"
        >
          <span class="common_title_label">{{ item.label }}</span>
        </div>
        <div v-else-if="item.type == 'show_text'" class="common_item">
          <div
            class="common_item_left"
            :style="{
              width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
              lineHeight:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            :style="{
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
            >{{ item.value || '--' }}</div
          >
        </div>
        <div v-else-if="item.type == 'input'" class="common_item">
          <div
            class="common_item_left"
            :style="{
              width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
              lineHeight:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            :style="{
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
          >
            <FormItem
              :name="item.key != undefined ? item.key : 'form_row_' + index"
              :rules="item.rules != undefined ? item.rules : undefined"
              :style="{
                width: item.right_width
                  ? item.right_width +
                    (typeof item.right_width == 'number' ? 'px !important' : ' !important')
                  : '300px !important',
              }"
            >
              <Input
                :placeholder="item.placeholder ? item.placeholder : '请输入'"
                :disabled="item.disable === true ? item.disable : false"
                :allowClear="item.allowClear ? item.allowClear : false"
                :maxlength="item.maxlength ? item.maxlength : 255"
                v-model:value="curFormData[item.key]"
                @change="(e) => handleChange(item, e.target.value, null)"
                :style="{
                  width: item.right_width
                    ? item.right_width +
                      (typeof item.right_width == 'number' ? 'px !important' : ' !important')
                    : '300px !important',
                }"
              />
            </FormItem>
            <div
              v-if="item.desc"
              class="common_item_right_desc"
              :style="{
                width: item.desc_width
                  ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                  : '300px',
              }"
              >{{ item.desc }}</div
            >
          </div>
        </div>
        <div v-else-if="item.type == 'inputnum'" class="common_item">
          <div
            class="common_item_left"
            :style="{
              width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
              lineHeight:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            :style="{
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
          >
            <FormItem
              :name="item.key != undefined ? item.key : 'form_row_' + index"
              :rules="item.rules != undefined ? item.rules : undefined"
              :style="{
                width: item.right_width
                  ? item.right_width +
                    (typeof item.right_width == 'number' ? 'px !important' : ' !important')
                  : '300px !important',
              }"
            >
              <InputNumber
                :placeholder="item.placeholder ? item.placeholder : '请输入'"
                :min="item.min ? item.min : 0"
                :max="item.max ? item.max : 9999999"
                :precision="item.precision ? item.precision : 0"
                :disabled="item.disable === true ? item.disable : false"
                v-model:value="curFormData[item.key]"
                @change="(e) => handleChange(item, e, null)"
                :style="{
                  width: item.right_width
                    ? item.right_width +
                      (typeof item.right_width == 'number' ? 'px !important' : ' !important')
                    : '300px !important',
                }"
              />
            </FormItem>
            <div
              v-if="item.desc"
              class="common_item_right_desc"
              :style="{
                width: item.desc_width
                  ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                  : '300px',
              }"
              >{{ item.desc }}</div
            >
          </div>
        </div>
        <div v-else-if="item.type == 'textarea'" class="common_item">
          <div
            class="common_item_left"
            :style="{
              width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
              height:
                item.height !== undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
              lineHeight:
                item.height !== undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            :style="{
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
          >
            <FormItem
              :name="item.key != undefined ? item.key : 'form_row_' + index"
              :rules="item.rules != undefined ? item.rules : undefined"
              :style="{
                width: item.right_width
                  ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                  : '300px',
              }"
            >
              <a-textarea
                :placeholder="item.placeholder ? item.placeholder : '请输入'"
                :maxlength="item.maxlength ? item.maxlength : 255"
                :rows="item.rows ? item.rows : 3"
                :autoSize="{ minRows: item.rows ? item.rows : 3 }"
                :showCount="item.showCount ? item.showCount : false"
                :allowClear="item.allowClear ? item.allowClear : false"
                :disabled="item.disabled ? item.disabled : false"
                v-model:value="curFormData[item.key]"
                @change="(e) => handleChange(item, e.target.value, null)"
                :style="{
                  width: item.right_width
                    ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                    : '300px',
                }"
                :class="item.show_search_map ? 'table_search_map_textarea' : ''"
              />
              <span v-if="item.show_search_map" class="table_search_map" @click="handleSearchMap">搜索地图</span>
            </FormItem>
            <div
              v-if="item.desc"
              class="common_item_right_desc"
              :style="{
                width: item.desc_width
                  ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                  : '300px',
              }"
              >{{ item.desc }}</div
            >
          </div>
        </div>
        <div v-else-if="item.type == 'select'" class="common_item">
          <div
            class="common_item_left"
            :style="{
              width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
              lineHeight:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            :style="{
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
          >
            <FormItem
              :name="item.key != undefined ? item.key : 'form_row_' + index"
              :rules="item.rules != undefined ? item.rules : undefined"
              :style="{
                width: item.right_width
                  ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                  : '300px',
              }"
            >
              <Select
                :placeholder="item.placeholder ? item.placeholder : '请选择'"
                :mode="item.mode ? item.mode : ''"
                :allowClear="item.allowClear ? item.allowClear : false"
                :disabled="item.disabled ? item.disabled : false"
                :defaultOpen="item.defaultOpen ? item.defaultOpen : false"
                :showSearch="item.showSearch ? item.showSearch : false"
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                v-model:value="curFormData[item.key]"
                @change="(e) => handleChange(item, e, null)"
                :style="{
                  width: item.right_width
                    ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                    : '300px',
                }"
              >
                <template v-if="item.data && item.data.length > 0">
                  <SelectOption
                    v-for="(items, indexs) in item.data"
                    :key="indexs"
                    :value="item.diy && item.diy_value ? items[item.diy_value] : items.value"
                    >{{
                      item.diy && item.diy_key ? items[item.diy_key] : items.title
                    }}</SelectOption
                  >
                </template>
              </Select>
            </FormItem>
            <div
              v-if="item.desc"
              class="common_item_right_desc"
              :style="{
                width: item.desc_width
                  ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                  : '300px',
              }"
              >{{ item.desc }}</div
            >
          </div>
        </div>
        <div v-else-if="item.type == 'radio'" class="common_item">
          <div
            class="common_item_left"
            :style="{
              width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
              lineHeight:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            :style="{
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
          >
            <FormItem
              :name="item.key != undefined ? item.key : 'form_row_' + index"
              :rules="item.rules != undefined ? item.rules : undefined"
              :style="{
                width: item.right_width
                  ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                  : '',
              }"
            >
              <RadioGroup
                :disabled="item.disabled ? item.disabled : false"
                :options="item.options ? item.options : []"
                v-model:value="curFormData[item.key]"
                @change="(e) => handleChange(item, e.target.value, null)"
                :style="{
                  width: item.right_width
                    ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                    : '',
                }"
              />
            </FormItem>
            <div
              v-if="item.desc"
              class="common_item_right_desc"
              :style="{
                width: item.desc_width
                  ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                  : '300px',
              }"
              >{{ item.desc }}</div
            >
          </div>
        </div>
        <div v-else-if="item.type == 'checkbox'" class="common_item">
          <div
            class="common_item_left"
            :style="{
              width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
              lineHeight:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            :style="{
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
          >
            <FormItem
              :name="item.key != undefined ? item.key : 'form_row_' + index"
              :rules="item.rules != undefined ? item.rules : undefined"
              :style="{
                width: item.right_width
                  ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                  : '',
              }"
            >
              <CheckboxGroup
                :disabled="item.disabled ? item.disabled : false"
                v-model:value="curFormData[item.key]"
                @change="(e) => handleChange(item, e, null)"
                :style="{
                  width: item.right_width
                    ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                    : '',
                }"
              >
                <template v-if="item.data && item.data.length > 0">
                  <Checkbox
                    v-for="(items, indexs) in item.data"
                    :key="indexs"
                    :value="item.diy && item.diy_value ? items[item.diy_value] : items.value"
                    :style="{
                      width: item.checkbox_width
                        ? item.checkbox_width + (typeof item.checkbox_width == 'number' ? 'px' : '')
                        : '',
                      margin: '2px 0',
                      padding: 0,
                    }"
                    >{{ item.diy && item.diy_key ? items[item.diy_key] : items.title }}</Checkbox
                  >
                </template>
              </CheckboxGroup>
            </FormItem>
            <div
              v-if="item.desc"
              class="common_item_right_desc"
              :style="{
                width: item.desc_width
                  ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                  : '300px',
              }"
              >{{ item.desc }}</div
            >
          </div>
        </div>
        <div v-else-if="item.type == 'cascader'" class="common_item">
          <div
            class="common_item_left"
            :style="{
              width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
              lineHeight:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            :style="{
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
          >
            <FormItem
              :name="item.key != undefined ? item.key : 'form_row_' + index"
              :rules="item.rules != undefined ? item.rules : undefined"
              :style="{
                width: item.right_width
                  ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                  : '300px',
              }"
            >
              <Cascader
                :placeholder="item.placeholder ? item.placeholder : '请选择'"
                :options="item.data ? item.data : []"
                :allowClear="item.allowClear ? item.allowClear : false"
                :disabled="item.disabled ? item.disabled : false"
                :expandTrigger="item.expandTrigger ? item.expandTrigger : 'click'"
                :fieldNames="
                  item.fieldNames
                    ? item.fieldNames
                    : {
                        label: 'label',
                        value: 'value',
                        children: 'children',
                      }
                "
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                :showSearch="item.showSearch ? item.showSearch : false"
                :matchInputWidth="item.matchInputWidth ? item.matchInputWidth : true"
                v-model:value="curFormData[item.key]"
                @change="(val, options) => handleChange(item, val, options)"
                :style="{
                  width: item.right_width
                    ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                    : '300px',
                }"
              />
            </FormItem>
            <div
              v-if="item.desc"
              class="common_item_right_desc"
              :style="{
                width: item.desc_width
                  ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                  : '300px',
              }"
              >{{ item.desc }}</div
            >
          </div>
        </div>
        <div v-else-if="item.type == 'upload_img'" class="common_item">
          <div
            class="common_item_left"
            :style="{
              width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
              lineHeight:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            :style="{
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
          >
            <FormItem
              :name="item.key != undefined ? item.key : 'form_row_' + index"
              :rules="item.rules != undefined ? item.rules : undefined"
              :style="{
                width: item.right_width
                  ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                  : '300px',
              }"
            >
              <Upload
                :accept="item.accept ? item.accept : '.jpg, .jpeg, .png, .gif'"
                :action="item.action"
                :method="item.method ? item.method : 'post'"
                :beforeUpload="
                  (file, fileList) =>
                    beforeUpload(
                      file,
                      fileList,
                      item.accept ? item.accept : '.jpg, .jpeg, .png, .gif',
                    )
                "
                :data="item.data ? item.data : {}"
                 @click="beforeUploadClick"
                :disabled="item.disabled ? item.disabled : false"
                listType="picture-card"
                :fileList="item.fileList ? item.fileList : []"
                :multiple="item.multiple ? item.multiple : false"
                @change="(file, fileList) => handleChange(item, file, fileList)"
                @preview="(file) => handlePreview(item, file)"
                :style="{
                  width: item.right_width
                    ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                    : '300px',
                }"
                :headers="{
                  Authorization: imgToken,
                }"
              >
                <div
                  v-if="
                    item.limit != undefined
                      ? item.fileList.length < item.limit
                      : !item.fileList || item.fileList.length == 0
                  "
                  class="upload_image"
                >
                  <PlusOutlined v-if="true" />
                  <LoadingOutlined v-else />
                  <span>上传图片</span>
                </div>
              </Upload>
            </FormItem>
            <div
              v-if="item.desc"
              class="common_item_right_desc"
              :style="{
                width: item.desc_width
                  ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                  : '300px',
              }"
              >{{ item.desc }}</div
            >
          </div>
        </div>
        <div v-else-if="item.type == 'goods_category'" class="common_item">
          <div
            class="common_item_left"
            :style="{
              width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
              lineHeight:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            :style="{
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
          >
            <div class="flex_row_start_center goods_category_label">
              <div class="goods_category_label_content">{{ item.value ? item.value : '--' }}</div>
              <div class="goods_category_label_btn" @click="resetGoodsCategory(item)">{{
                item.btnText ? item.btnText : '选择'
              }}</div>
            </div>
            <div
              v-if="item.desc"
              class="common_item_right_desc"
              :style="{
                width: item.desc_width
                  ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                  : '300px',
              }"
              >{{ item.desc }}</div
            >
          </div>
        </div>
        <div v-else-if="item.type == 'material_file'" class="common_item">
          <div
            class="common_item_left"
            :style="{
              width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
              lineHeight:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            :style="{
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
          >
            <FormItem
              :name="item.key != undefined ? item.key : 'form_row_' + index"
              :rules="item.rules != undefined ? item.rules : undefined"
              :style="{
                width: item.right_width
                  ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                  : '300px',
              }"
            >
              <Upload
                listType="picture-card"
                :disabled="item.disabled ? item.disabled : false"
                :fileList="item.fileList ? item.fileList : []"
                :open-file-dialog-on-click="false"
                @change="(file, fileList, event) => handleChangeUpload(item, file, fileList, event)"
                @preview="(file) => handlePreview(item, file)"
                :style="{
                  width: item.right_width
                    ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                    : '300px',
                }"
              >
                <div
                  v-if="
                    item.limit != undefined
                      ? item.fileList.length < item.limit
                      : !item.fileList || item.fileList.length == 0
                  "
                  class="upload_image"
                  @click="handleMaterial(item, index)"
                >
                  <PlusOutlined v-if="true" />
                  <LoadingOutlined v-else />
                  <span>{{ item.upload_text ? item.upload_text : '上传' }}</span>
                </div>
              </Upload>
            </FormItem>
            <div
              v-if="item.desc"
              class="common_item_right_desc"
              :style="{
                width: item.desc_width
                  ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                  : '300px',
              }"
              >{{ item.desc }}</div
            >
          </div>
        </div>
        <div v-else-if="item.type == 'material_video'" class="common_item">
          <div
            class="common_item_left"
            :style="{
              width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
              lineHeight:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            :style="{
              height:
                item.height != undefined
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
            }"
          >
            <FormItem
              :name="item.key != undefined ? item.key : 'form_row_' + index"
              :rules="item.rules != undefined ? item.rules : undefined"
              :style="{
                width: item.right_width
                  ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                  : '300px',
              }"
            >
              <div
                v-if="(item.fileList.length > 0 && item.fileList[0].status == 'done' && item.fileList[0].response != undefined)"
                class="material_video_show">
                <video width="102" height="102" :src="item.fileList[0].response.data.url" controls autoPlay></video>
                <div class="material_video_del" @click="deleteMaterial(item)">
                  <AliSvgIcon
                    width="18px"
                    height="18px"
                    iconName="iconqingchu"
                    fillColor="#c8c8c8"
                  />
                </div>
              </div>
              <Upload
                v-else
                listType="picture-card"
                :disabled="item.disabled ? item.disabled : false"
                :fileList="item.fileList ? item.fileList : []"
                :open-file-dialog-on-click="false"
                @change="(file, fileList, event) => handleChangeUpload(item, file, fileList, event)"
                @preview="(file) => handlePreview(item, file)"
                :style="{
                  width: item.right_width
                    ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                    : '300px',
                }"
              >
                <div
                  v-if="
                    item.limit != undefined
                      ? item.fileList.length < item.limit
                      : !item.fileList || item.fileList.length == 0
                  "
                  class="upload_image"
                  @click="handleMaterial(item, index)"
                >
                  <PlusOutlined v-if="true" />
                  <LoadingOutlined v-else />
                  <span>{{ item.upload_text ? item.upload_text : '上传' }}</span>
                </div>
              </Upload>
            </FormItem>
            <div
              v-if="item.desc"
              class="common_item_right_desc"
              :style="{
                width: item.desc_width
                  ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                  : '300px',
              }"
              >{{ item.desc }}</div
            >
          </div>
        </div>
        <div v-else-if="item.type == 'goods_spec'" class="common_item">
          <div
            class="common_item_left"
            style="position: absolute; top: 0; bottom: 0; height: auto"
            :style="{
              width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            style="height: auto; padding: 15px 20px"
            :style="{
              marginLeft:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
            }"
          >
            <div
              style="width: 100%; margin-bottom: 15px"
              v-for="(spec_item, spec_index) in item.selectSpecList"
              :key="spec_index"
            >
              <div class="flex_row_start_center goods_spec">
                <div class="goods_spec_label">规格名：</div>
                <Select
                  show-search
                  :show-arrow="false"
                  optionFilterProp="label"
                  :defaultActiveFirstOption="false"
                  placeholder="请输入或选择规格项"
                  :value="
                    spec_item.selectedSpec && spec_item.selectedSpec.specName
                      ? spec_item.selectedSpec.specName
                      : undefined
                  "
                  @change="(e) => handleSpec('selectSpec', e, spec_index, null)"
                  @keydown.enter.native="(e) => handleSpec('addSpecDown', e.target.value, spec_index, null)"
                  style="width: 200px; margin-left: 5px"
                >
                  <SelectOption
                    v-for="(items, indexs) in item.specList"
                    :key="indexs"
                    :value="items.value"
                    :label="items.label"
                    >{{ items.label }}</SelectOption
                  >
                </Select>
                <Checkbox
                  v-if="spec_item.selectedSpec && spec_item.selectedSpec.specId"
                  style="margin-left: 20px"
                  :checked="spec_item.setImg !== undefined ? spec_item.setImg : false"
                  @click="(e) => handleSpec('setSpecImg', !spec_item.setImg, spec_index, null)"
                  >设置图片规格</Checkbox
                >
                <AliSvgIcon
                  class="goods_spec_label_delbtn"
                  width="17px"
                  height="17px"
                  iconName="iconqingchu"
                  fillColor="#c8c8c8"
                  @click="(e) => handleSpec('deleteSpec', e, spec_index, null)"
                />
              </div>
              <div
                v-if="spec_item.selectedSpec && spec_item.selectedSpec.specName"
                class="flex_row_start_start goods_spec_value"
              >
                <div class="goods_spec_value_label">规格值：</div>
                <div class="flex_row_start_center goods_spec_value_list">
                  <div
                    v-for="(spec_val_item, spec_val_index) in spec_item.selectedValueSpec"
                    :key="spec_val_index"
                    class="goods_spec_value_select"
                  >
                    <Select
                      show-search
                      :show-arrow="false"
                      optionFilterProp="label"
                      :defaultActiveFirstOption="false"
                      placeholder="请输入或选择规格值"
                      :value="spec_val_item.specValue ? spec_val_item.specValue : undefined"
                      @change="(e) => handleSpec('selectSpecVal', e, spec_index, spec_val_index)"
                      @keydown.enter.native="(e) => handleSpec('addSpecValDown', e.target.value, spec_index, spec_val_index)"
                      style="width: 200px; margin-left: 5px"
                    >
                      <SelectOption
                        v-for="(items, indexs) in spec_item.specValueList"
                        :key="indexs"
                        :value="
                          item.spec_val_diy && item.spec_val_diy_value
                            ? items[item.spec_val_diy_value]
                            : items.value
                        "
                        :label="
                          item.spec_val_diy && item.spec_val_diy_key
                            ? items[item.spec_val_diy_key]
                            : items.title
                        "
                        >{{
                          item.spec_val_diy && item.spec_val_diy_key
                            ? items[item.spec_val_diy_key]
                            : items.title
                        }}</SelectOption
                      >
                    </Select>
                    <AliSvgIcon
                      class="goods_spec_value_select_delbtn"
                      width="17px"
                      height="17px"
                      iconName="iconqingchu"
                      fillColor="#ff711e"
                      @click="(e) => handleSpec('deleteSpecVal', e, spec_index, spec_val_index)"
                    />
                  </div>
                  <div
                    v-if="
                      spec_item.selectedValueSpec &&
                      ((spec_item.selectedValueSpec.length == 1 && spec_item.selectedValueSpec[0].specValue) ||
                      (spec_item.selectedValueSpec.length > 1 && (spec_item.selectedValueSpec.length < spec_item.specValueList.length + 1
                        || spec_item.selectedValueSpec[spec_item.selectedValueSpec.length - 1].specValue)))
                    "
                    class="goods_spec_value_btn"
                    @click="(e) => handleSpec('addSpecVal', null, spec_index, null)"
                    >添加规格值</div
                  >
                </div>
              </div>
            </div>
            <div
              v-if="
                !item.selectSpecList ||
                item.selectSpecList.length == 0 ||
                Object.keys(item.selectSpecList[item.selectSpecList.length - 1].selectedSpec)
                  .length > 0
              "
              class="goods_spec_btn"
            >
              <div class="goods_spec_btn_add" @click="handleSpec('add', null, null, null)"
                >添加规格项</div
              >
            </div>
          </div>
        </div>
        <div v-else-if="item.type == 'add_reserve_info'" class="common_item">
          <div
            class="common_item_left"
            style="position: absolute; top: 0; bottom: 0; height: auto"
            :style="{
              width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            style="height: auto; padding: 15px 20px"
            :style="{
              marginLeft:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
            }"
          >
            <div
              style="width: 100%; margin-bottom: 15px"
              v-for="(reserve_item, reserve_index) in item.data"
              :key="reserve_index"
            >
              <div class="flex_row_start_center reserve_info">
                <div class="reserve_info_xing">{{ reserve_item.isRequired ? '*' : '' }}</div>
                <FormItem
                  :name="['sld_form', index, 'data', reserve_index, 'reserveName']"
                  :rules="reserve_item.isRequired ? [{ required: true, whitespace: true, message: '该项必填' }] : []"
                  :style="{
                    width: item.right_width
                      ? item.right_width +
                        (typeof item.right_width == 'number' ? 'px !important' : ' !important')
                      : '230px !important',
                  }"
                >
                  <Input
                    placeholder="请输入预留信息名称,最多10个字"
                    :maxlength="10"
                    :value="reserve_item.reserveName !== undefined ? reserve_item.reserveName : undefined"
                    @change="(e) => handleReserveInfo('edit', item, 'reserveName', reserve_index, e.target.value, ['sld_form', index, 'data', reserve_index, 'reserveName'])"
                    :style="{
                      width: item.right_width
                        ? item.right_width +
                          (typeof item.right_width == 'number' ? 'px !important' : ' !important')
                        : '230px !important',
                    }"
                  />
                </FormItem>
                <Select
                  placeholder="请选择"
                  :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                  :value="reserve_item.reserveType !== undefined ? reserve_item.reserveType : undefined"
                  @change="(e) => handleReserveInfo('edit', item, 'reserveType', reserve_index, e)"
                  style="width: 110px; margin-left: 15px"
                >
                  <SelectOption
                    v-for="(items, indexs) in item.select_list"
                    :key="indexs"
                    :value="items.value"
                    :label="item.label"
                    >{{ items.label }}</SelectOption
                  >
                </Select>
                <Checkbox
                  :checked="reserve_item.isRequired !== undefined ? reserve_item.isRequired : false"
                  @click="(e) => handleReserveInfo('edit', item, 'isRequired', reserve_index, !reserve_item.isRequired, null)"
                  style="margin-left: 15px"
                  >必填</Checkbox
                >
                <AliSvgIcon
                  width="16px"
                  height="15px"
                  iconName="iconshanchu7"
                  fillColor="#e20e0e"
                  @click="(e) => handleReserveInfo('del', item, null, reserve_index, e, ['sld_form', index, 'data', reserve_index, 'reserveName'])"
                />
              </div>
            </div>
            <div class="reserve_info_btn" @click="handleReserveInfo('add', item, null, null, null, null)">新增预留信息</div>
          </div>
        </div>
        <div v-else-if="item.type == 'show_map'" class="common_item">
          <div
            class="common_item_left"
            style="position: absolute; top: 0; bottom: 0;"
            :style="{
              width:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
              height:
                item.height != undefined
                    ? item.height + (typeof item.height == 'number' ? 'px' : '')
                    : '',
            }"
            ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
          >
          <div
            class="common_item_right"
            style="padding: 15px 20px"
            :style="{
              height:
                item.height != undefined
                    ? item.height + (typeof item.height == 'number' ? 'px' : '')
                    : '',
              marginLeft:
                left_width != undefined
                  ? left_width + (typeof left_width == 'number' ? 'px' : '')
                  : '',
            }"
          >
            <div id="wrapRef" :style="{ width: item.right_width != undefined
                ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '') : '270px', height: '270px' }"></div>
          </div>
        </div>
      </div>
    </Form>
  </div>
</template>
<script lang="ts" setup>
  import { ref, computed, watch, onMounted, nextTick } from 'vue';
  import {
    Form,
    FormItem,
    Input,
    InputNumber,
    Select,
    SelectOption,
    RadioGroup,
    Checkbox,
    CheckboxGroup,
    Cascader,
    Upload,
  } from 'ant-design-vue';
  import { PlusOutlined, LoadingOutlined } from '@ant-design/icons-vue';
  import { useGlobSetting } from '/@/hooks/setting';
  import { useScript } from '/@/hooks/web/useScript';
  import { getToken } from '/@/utils/auth';
  import { failTip } from '@/utils/utils';
  import { useUserStore } from '@/store/modules/user';


  const emit = defineEmits([
    'callbackEvent',
    'validaEvent',
    'categorySelectEvent',
    'materialEvent',
    'materialDelete',
    'goodsSpecEvent',
    'changeUpload',
    'reserveInfoEvent',
  ]);
  const rowForm: any = ref({});
  const curFormData = ref({sld_form:[]});
  const imgToken = ref('Bearer '+getToken() || ''); //上传文件token参数
  const { mapKey, mapSecurity } = useGlobSetting();

  const userStore = useUserStore();

  const props: any = defineProps({
    width: { type: String, require: false, default: '100%' }, //列表宽度
    left_width: { type: [String, Number], reqiore: false }, //列表label宽度
    data: { type: Array, required: true, default: () => [] }, //表单数据
    valida: { type: Boolean, required: false, default: false }, //是否提交进行校验
  });

  const formData: any = computed(() => {
    return props.data;
  });

  const curValida: any = computed(() => {
    return props.valida;
  });

  watch(
    formData,
    () => {
      let show_map = false;
      imgToken.value = 'Bearer ' + userStore.getToken
      props.data.map((item) => {
        if (item.key) {
          curFormData.value[item.key] =
            item.type == 'upload_img'
              ? item.fileList && item.fileList.length > 0
                ? item.fileList[0]
                : {}
              : item.value;
        }
        if (item.type == 'show_map') {
          show_map = true;
        }
      });
      curFormData.value.sld_form = JSON.parse(JSON.stringify(formData.value));
      if (show_map) {
        initMap(true);
      }
    },
    { deep: true },
  );

  watch(
    curValida,
    () => {
      imgToken.value = 'Bearer ' + userStore.getToken
      if (props.valida) {
        rowForm.value
          .validate()
          .then(() => {
            if (curFormData.value.shopType == 2 && (!curFormData.value.lng || !curFormData.value.lat)) {
              emit('validaEvent', false, '请点击搜索地图按钮，获取经纬度后再次提交');
            } else {
              emit('validaEvent', true);
            }
          })
          .catch(() => {
            emit('validaEvent', false);
          });
      }
    },
    { deep: true },
  );

  const A_MAP_URL = 'https://webapi.amap.com/maps?v=2.0&key=' + mapKey + '&plugin=AMap.Geocoder';
  window._AMapSecurityConfig = { securityJsCode: mapSecurity };
  const { toPromise } = useScript({ src: A_MAP_URL });
  const map_obj = ref();
  const map_geocoder = ref();
  const map_marker = ref();
  
  //初始化地图
  async function initMap(flag) {
    await toPromise();
    await nextTick();
    const AMap = (window as any).AMap;
    let map = new AMap.Map('wrapRef', {
      zoom: 10, //设置地图显示的缩放级别
      resizeEnable: true,
    });
    map_obj.value = map;
    AMap.plugin('AMap.ToolBar', function () {
      var toolbar = new AMap.ToolBar();
      map.addControl(toolbar);
    });
    map_geocoder.value = new AMap.Geocoder({
      city: "010", //城市设为北京，默认：“全国”
    });
    map_marker.value = new AMap.Marker();

    //地图双击选择定位
    map.on('dblclick', (e) => {
      if (e.lnglat.lng && e.lnglat.lat) {
        curFormData.value['lng'] = e.lnglat.lng;
        curFormData.value['lat'] = e.lnglat.lat;
        map_marker.value.setPosition(e.lnglat);
        map.add(map_marker.value);
        map.setFitView(map_marker.value, false);
      }
    });
  
    if (flag) {
      handleSearchMap(flag);
    }
  };

  //搜索地图
  function handleSearchMap(flag) {
    if (curFormData.value.areaInfo || curFormData.value.companyAddress) {
      searchMap((curFormData.value.areaInfo || '') + (curFormData.value.companyAddress || ''));
    } else if (!flag) {
      failTip('请输入地址');
    }
  };

  function searchMap(address) {
    //关键字搜索并定位
    map_geocoder.value.getLocation(address, function(status, result) {
      if (status === 'complete' && result.geocodes.length) {
        curFormData.value['lng'] = result.geocodes[0].location.lng;
        curFormData.value['lat'] = result.geocodes[0].location.lat;
        const lnglat = result.geocodes[0].location;
        map_marker.value.setPosition(lnglat);
        map_obj.value.add(map_marker.value);
        map_obj.value.setFitView(map_marker.value);
      }else{
        failTip('根据地址查询位置失败');
      }
    });
  };

  onMounted(() => {
    let show_map = false;
    props.data.map((item) => {
      if (item.key) {
        curFormData.value[item.key] =
          item.type == 'upload_img'
            ? item.fileList && item.fileList.length > 0
              ? item.fileList[0]
              : {}
            : item.value;
      }
      if (item.type == 'show_map') {
        show_map = true;
      }
    });
    if (show_map) {
      initMap(true);
    }
  });

  function handleChange(item, val, extra) {
    if (item.need_reset_map && (curFormData.value['lng'] || curFormData.value['lat'])) {
      curFormData.value['lng'] = '';
      curFormData.value['lat'] = '';
    }
    if (item.callback) {
      emit('callbackEvent', {
        contentItem: { ...item, eventType: 'change' },
        val,
        extra,
      });
    }
  }

  function handleChangeUpload(item, file, fileList, event) {
    emit('changeUpload', {
      contentItem: item,
      file,
      fileList,
      event
    });
  }

  function handlePreview(item, file) {
    window.open(file.url ? file.url : file.thumbUrl);
  }

  
  const beforeUploadClick = async()=> {
    imgToken.value = 'Bearer ' + userStore.getToken
    if(userStore.getToken){
      let res = await userStore.getSldCommonService()
      if(res.state == 200){
        imgToken.value = 'Bearer ' + res.token
      }
    }
  }

  //文件上传前数据校验
  function beforeUpload(file, fileList, accept, limit = 20) {
    //校验文件格式类型
    if (accept != undefined && accept != null && accept) {
      let accept_list = accept
        .replaceAll(' ', '')
        .split(',')
        .filter((item) => item && item.trim());
      let file_type = file.name ? '.' + file.name.split('.').at(-1) : '';
      if (accept_list.length > 0 && file_type != '' && accept_list.indexOf(file_type) == -1) {
        failTip('上传文件格式有误');
        return false;
      }
    }
    //校验文件大小
    if (file.size != undefined && file.size > 1024 * 1024 * limit) {
      failTip(`上传文件过大，请上传小于${limit}M的图片`);
      return false;
    }
  }

  function resetGoodsCategory(item) {
    if (item.callback) {
      emit('categorySelectEvent');
    }
  }

  function handleMaterial(item, index) {
    if (item.callback) {
      emit('materialEvent', { ...item, index });
    }
  }

  function deleteMaterial(item, index) {
    if (item.callback) {
      emit('materialDelete');
    }
  }

  function handleSpec(type, val, index, indexs) {
    emit('goodsSpecEvent', type, val, index, indexs);
  }

  function handleReserveInfo(type, item, key, index, val, ids) {
    if (item.reserveInfoBack) {
      emit('reserveInfoEvent', { type, key, index, val });
      if ((key == 'reserveName' && val) || key == 'isRequired'  || type == 'del') {
        rowForm.value.resetFields(ids);
      }
    }
  }
</script>
<style lang="less">
  .standard_form_row {
    display: flex;
    flex-wrap: wrap;
    padding: 0.5rem;
    background: #fff;

    .ant-form {
      display: flex;
      flex-wrap: wrap;
      align-items: flex-start;
      justify-content: flex-start;
      width: 100%;

      .ant-form-item {
        margin-bottom: 0;
      }
    }

    .ant-form-item-control {
      .ant-form-item-explain {
        position: absolute;
        z-index: 2;
        top: -11px;
        right: 12%;
        min-height: 20px;
        padding: 0 5px;
        background: #fff;
        font-size: 13px;
        text-align: right;
      }
    }

    .common_title {
      flex-shrink: 0;
      width: 100%;
      height: 32px;
      margin-bottom: 10px;
      padding-right: 0.5rem;
      padding-left: 0.5rem;
      line-height: 32px;

      .common_title_label {
        padding-left: 7px;
        color: rgb(0 0 0 / 85%);
        font-size: 14px;
        font-weight: 500;
      }
    }

    .common_item {
      display: flex;
      position: relative;
      flex-shrink: 0;
      align-items: center;
      justify-content: flex-start;
      font-size: 13px;

      .common_item_left {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        width: 200px;
        height: 72px;
        padding: 12px 15px;
        border: 1px solid #f0f0f0;
        background: #f8f9fa;
        color: #999;
        font-size: 13px;
        font-weight: 500;
        text-align: right;

        .common_item_require {
          color: red;
        }
      }

      .common_item_right {
        display: flex;
        flex: 1;
        flex-direction: column;
        flex-shrink: 0;
        align-items: flex-start;
        justify-content: center;
        height: 72px;
        padding-right: 20px;
        padding-left: 20px;
        border: 1px solid #f0f0f0;
        border-left: none;

        .common_item_right_desc {
          margin-top: 3px;
          color: rgb(0 0 0 / 45%);
          font-size: 12px;
          line-height: 14px;
        }

        .goods_category_label {
          .goods_category_label_content {
            color: #999;
            font-size: 12px;
            line-height: 32px;
          }

          .goods_category_label_btn {
            height: 32px;
            margin-left: 10px;
            padding: 0 15px;
            transition: all 0.3s ease;
            border-radius: 4px;
            border-color: @primary-color;
            background-color: @primary-color;
            color: #fff;
            font-weight: 400;
            line-height: 30px;
            cursor: pointer;

            &:hover {
              border-color: #ff9d52;
              background-color: #ff9d52;
            }
          }
        }

        .material_video_show {
          position: relative;
          margin-top: 12px;

          .material_video_del {
            position: absolute;
            z-index: 99;
            top: -11px;
            left: 93px;
            cursor: pointer;
          }
        }

        .reserve_info {
          .reserve_info_xing {
            width: 11px;
            color: #f5222d;
            font-size: 14px;
          }

          .svgIcon {
            margin-top: 1px;
            margin-left: 4px;
            cursor: pointer;
          }
        }

        .reserve_info_btn {
          width: 95px;
          margin-right: 20px;
          margin-left: 10px;
          border: 1px solid #FC701E;
          border-radius: 2px;
          color: #FC701E;
          font-size: 13px;
          line-height: 30px;
          text-align: center;
          cursor: pointer;
        }
      }
    }

    .upload_image {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 90px;
      height: 90px;

      span {
        margin-top: 8px;
        color: rgb(102 102 102);
        font-size: 13px;
      }
    }

    .upload_file {
      color: @primary-color;
      font-size: 13px;
      cursor: pointer;
    }

    .goods_spec {
      position: relative;
      width: 100%;
      height: 45px;
      padding: 15px 0;
      background: rgb(255 113 30 / 5%);

      .goods_spec_label {
        margin-left: 20px;
        color: #333;
        font-size: 14px;
        font-weight: bold;
      }

      .goods_spec_label_delbtn {
        display: none;
        position: absolute;
        top: 50%;
        right: 20px;
        transform: translateY(-50%);
        cursor: pointer;
      }

      &:hover {
        .goods_spec_label_delbtn {
          display: block;
        }
      }
    }

    .goods_spec_value {
      width: 100%;
      min-height: 45px;
      padding: 15px 0;
      border-bottom: 1px dashed #dcdcdc;

      .goods_spec_value_label {
        flex-shrink: 0;
        margin-bottom: 10px;
        margin-left: 20px;
        color: #333;
        font-size: 14px;
        font-weight: bold;
        line-height: 32px;
      }

      .goods_spec_value_list {
        flex-wrap: wrap;
      }

      .goods_spec_value_select {
        position: relative;
        margin-bottom: 10px;

        .goods_spec_value_select_delbtn {
          display: none;
          position: absolute;
          z-index: 9;
          top: -8px;
          right: -8px;
          cursor: pointer;
        }

        .ant-select-show-search.ant-select:not(.ant-select-customize-input)
          .ant-select-selector
          input {
          cursor: pointer;
        }

        &:hover {
          .goods_spec_value_select_delbtn {
            display: block;
          }
        }
      }

      .goods_spec_value_btn {
        flex-shrink: 0;
        height: 32px;
        margin-bottom: 10px;
        margin-left: 20px;
        color: #ff711e;
        font-size: 14px;
        font-weight: bold;
        line-height: 32px;
        cursor: pointer;
      }
    }

    .goods_spec_btn {
      position: relative;
      width: 100%;
      height: 45px;
      background: rgb(255 113 30 / 5%);

      .goods_spec_btn_add {
        width: 120px;
        height: 36px;
        margin-top: 5px;
        margin-left: 5px;
        border: 1px solid #ff711e;
        border-radius: 3px;
        background: #fff;
        color: #ff711e;
        font-size: 14px;
        font-weight: bold;
        line-height: 36px;
        text-align: center;
        cursor: pointer;
      }
    }
  }

  .table_search_map_textarea {
    padding-right: 80px;
    overflow: hidden;
  }

  .table_search_map {
    display: inline-block;
    position: relative;
    right: 76px;
    bottom: 0;
    height: 31px;
    padding: 4px 10px;
    border-radius: 0 3px 3px 0;
    background-color: rgb(255 126 40 / 15%);
    color: @primary-color;
    font-size: 14px;
    font-weight: 400;
    cursor: pointer;

    &:hover {
      border-color: @primary-color;
      color: @primary-color;
    }
  }
</style>
