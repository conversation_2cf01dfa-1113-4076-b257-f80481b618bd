// @import '~antd/lib/style/themes/default.less';

.component_sele_more {
  width: 100%;
  padding: 10px 20px 20px;

  .vben-basic-form .ant-form-item-with-help {
    margin-bottom: 20px !important;
  }

  .basic-form-sld {
    .from-search {
      right: -170px;
      width: auto;
    }
  }

  .scroll_box {
    overflow-x: hidden;
    overflow-y: auto;
  }

  .content {
    width: 100%;
    overflow: hidden;

    .left,
    .right {
      flex-wrap: wrap;
      align-content: flex-start;
      width: 438px;
      border: 1px solid rgb(238 238 238 / 100%);
      background: rgb(245 245 245 / 100%);
    }

    .item {
      position: relative;
      width: 200px;
      height: 80px;
      margin: 10px 10px 0;
      border-radius: 3px;
      background: #fff;

      .mask {
        position: absolute;
        z-index: 99;
        inset: 0;
        background: rgb(0 0 0 / 40%);
        color: #fff;
        font-size: 13px;
        font-weight: bold;
      }

      .item_left {
        position: relative;
        flex-shrink: 0;
        width: 80px;
        height: 80px;
        overflow: hidden;
        border-radius: 3px 0 0 3px;

        .storage {
          position: absolute;
          z-index: 2;
          right: 0;
          bottom: 0;
          left: 0;
          height: 20px;
          background: rgb(0 0 0 / 30%);
          color: #fff;
          font-size: 12px;
        }

        .left_play_icon {
          position: absolute;
          z-index: 2;
          top: 0;
          left: 0;
          width: 18px;
          height: 12px;
        }

        img.live_img {
          max-width: 100%;
          max-height: 100%;
        }

        .play_icon {
          position: absolute;
          z-index: 2;
          top: 50%;
          left: 50%;
          width: 22px;
          height: 22px;
          margin-top: -11px;
          margin-left: -11px;
        }

        .play_num {
          position: absolute;
          z-index: 2;
          bottom: 0;
          left: 0;
          width: 80px;
          height: 20px;
          padding: 0 4px;
          overflow: hidden;
          border-radius: 0 0 3px 3px;
          background: linear-gradient(0deg, rgb(51 51 51 / 100%) 0%, rgb(0 0 0 / 0%) 100%);
          color: #fff;
          font-size: 10px;
          line-height: 20px;
          text-align: center;
          text-overflow: ellipsis;
          white-space: nowrap;

          span {
            display: inline-block;
            transform: scale(0.85);
          }
        }
      }

      .item_right {
        flex: 1;
        padding: 10px;

        .svideo_name {
          display: -webkit-box;
          width: 105px;
          height: 32px;
          overflow: hidden;
          color: #333;
          font-size: 12px;
          line-height: 16px;
          text-overflow: ellipsis;
          word-break: break-word;
          -webkit-line-clamp: 2;

          /*! autoprefixer: off */
          -webkit-box-orient: vertical;
        }

        .svideo_label {
          display: inline-block;
          width: 82px;
          height: 14px;
          margin-top: 15px;
          overflow: hidden;
          color: #fc1c1c;
          font-size: 12px;
          line-height: 14px;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .sele_svideo_flag {
          position: absolute;
          z-index: 2;
          right: 0;
          bottom: 0;
          width: 19px;
          height: 19px;
        }
      }
    }

    .item:nth-child(2n) {
      margin-right: 0 !important;
      margin-left: 0;
    }

    .center {
      z-index: 0;
      flex: 1;
      height: 100%;
      transform: rotate(90deg);
    }
  }

  :global {
    .ant-form {
      padding-top: 0 !important;
    }
  }
}
