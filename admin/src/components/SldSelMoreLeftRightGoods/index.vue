<template>
  <div>
    <Modal
      destroyOnClose
      :maskClosable="false"
      :title="props.title"
      :visible="props.modalVisible"
      :width="props.width"
      @ok="sldConfirm"
      @cancel="sldCancle"
    >
      <template #footer>
        <Button key="back" @click="sldCancle">{{ props.cancelBtnText }}</Button>
        <Button
          key="submit"
          type="primary"
          :loading="props.confirmBtnLoading"
          @click="sldConfirm"
          >{{ props.confirmBtnText }}</Button
        >
      </template>
      <div class="component_sele_more flex_column_start_start">
        <div class="tableListForm">
          <div style="position: relative">
            <BasicForm ref="formRef" submitOnReset @register="registerForm" class="basic-form-sld">
            </BasicForm>
          </div>
        </div>
        <div class="content flex_row_start_start" :style="{ height: height + 'px' }">
          <div
            class="scroll_box"
            :style="{ height: height + 'px', background: '#f5f5f5', width: '438px', zIndex: 1 }"
            @scroll="handleScrollLeft"
          >
            <div class="left flex_row_start_start" :style="{ height: height + 'px' }">
              <template v-if="data.list != undefined && data.list.length > 0">
                <a
                  v-for="(item, index) in data.list"
                  :key="index"
                  class="item flex_row_start_start"
                  :style="{ marginBottom: index == data.list.length - 1 ? 10 : 0 }"
                  @click="handleLeftItem(item)"
                >
                  <!-- <div class="mask flex_row_center_center" v-if="item.activityState != 1">已参加其他活动</div>
                  <div class="mask flex_row_center_center" v-if="item.activityState == 1 && item.goodsStock == 0">暂时无货</div> -->
                  <div class="item_left flex_row_center_center">
                    <img :src="diy_type == 'spreader' ? item.goodsImage : item.mainImage" alt="" class="live_img" />
                  </div>
                  <div class="item_right flex_column_start_start" :style="{ padding: diy_type == 'integral' ? '6px 10px' : '10px' }">
                    <span class="svideo_name">{{ item.goodsName }}</span>
                    <template v-if="diy_type == 'integral'">
                      <span class="svideo_label" style="margin-top: 6px;">{{ item.integralPrice }}积分</span>
                      <span class="svideo_label" style="margin-top: 2px">¥{{ item.cashPrice }}</span>
                    </template>
                    <template v-else-if="diy_type == 'spreader'">
                      <span class="svideo_label">¥{{ item.productPrice }}</span>
                    </template>
                    <template v-else>
                      <span class="svideo_label">¥{{ item.goodsPrice }}</span>
                    </template>
                    <div class="sele_svideo_flag" v-if="selectedRowKeys.indexOf(item[dataKey]) > -1">
                      <AliSvgIcon
                        iconName="iconyixuan"
                        width="19px"
                        height="19px"
                        fillColor="#FF711E"
                      />
                    </div>
                  </div>
                </a>
              </template>
              <template v-if="data.list == undefined || data.list.length == 0">
                <div class="flex_column_center_center" :style="{ width: '100%', height: '100%' }">
                  <Empty :image="simpleImage"></Empty>
                </div>
              </template>
            </div>
          </div>
          <div class="center flex_row_center_center">
            <AliSvgIcon iconName="iconmove-up1" width="39px" height="32px" fillColor="#fff6f4" />
          </div>
          <div
            class="scroll_box"
            :style="{ height: height + 'px', background: '#f5f5f5', width: '438px', zIndex: 1 }"
          >
            <div class="right flex_row_start_start" :style="{ height: height + 'px' }">
              <template v-if="selectedRows.length > 0">
                <a
                  v-for="(item, index) in selectedRows"
                  :key="index"
                  class="item flex_row_start_start"
                  :style="{ marginBottom: index == selectedRows.length - 1 ? 10 : 0 }"
                  @click="handleRightItem(item)"
                >
                  <div class="item_left flex_row_center_center">
                    <img :src="diy_type == 'spreader' ? item.goodsImage : item.mainImage" alt="" class="live_img" />
                  </div>
                  <div class="item_right flex_column_start_start" :style="{ padding: diy_type == 'integral' ? '6px 10px' : '10px' }">
                    <span class="svideo_name">{{ item.goodsName }}</span>
                    <template v-if="diy_type == 'integral'">
                      <span class="svideo_label" style="margin-top: 6px;">{{ item.integralPrice }}积分</span>
                      <span class="svideo_label" style="margin-top: 2px">¥{{ item.cashPrice }}</span>
                    </template>
                    <template v-else-if="diy_type == 'spreader'">
                      <span class="svideo_label">¥{{ item.productPrice }}</span>
                    </template>
                    <template v-else>
                      <span class="svideo_label">¥{{ item.goodsPrice }}</span>
                    </template>
                    <div class="sele_svideo_flag">
                      <AliSvgIcon
                        iconName="iconziyuan21"
                        width="19px"
                        height="19px"
                        fillColor="#FF711E"
                      />
                    </div>
                  </div>
                </a>
              </template>
              <template v-else>
                <div class="flex_column_center_center" :style="{ width: '100%', height: '100%' }">
                  <Empty :image="simpleImage" description="您还未选择数据"></Empty>
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  </div>
</template>
<script>
  export default {
    name: 'SldSelMoreLeftRightGoods',
  };
</script>
<script setup>
  import { ref, onMounted, computed, watch } from 'vue';
  import { Modal, Button, Empty } from 'ant-design-vue';
  import { 
    getGoodsListApi,
    // dev_supplier-start
    getGoodsSupplierListApi
    // dev_supplier-end
   } from '/@/api/common/common';
  import { getIntegralGoodsListApi } from '/@/api/point/goods';
  //spreader-1-start
  import { getSpreaderGoodsListApi } from '/@/api/spreader/goods_list';
  //spreader-1-end
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { list_com_page_size_16, failTip } from '/@/utils/utils';
  import { validatorEmoji } from '/@/utils/validate';

  const selectedRows = ref([]);
  const selectedRowKeys = ref([]); //selectedRows的key
  const data = ref({});
  const formValues = ref({});
  const loading_pagination_flag = ref({});
  const params = ref({ pageSize: list_com_page_size_16 }); //搜索条件
  const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;

  const props = defineProps({
    title: { type: String, required: true }, //弹框标题
    modalVisible: { type: Boolean, required: true }, //弹框是否显示
    confirmBtnLoading: { type: Boolean, default: false }, //弹框确认按钮loading属性，默认不显示
    height: { type: Number }, //弹框内容高度
    width: { type: Number, default: 416 }, //弹框宽度，默认416px
    confirmBtnText: { type: String, default: '确认' }, //弹框底部确认按钮显示文案，默认为确认
    cancelBtnText: { type: String, default: '取消' }, //弹框底部取消按钮显示文案，默认为取消
    selectedRow: { type: Array, required: true, default: () => [] }, //表单数据
    arrType:{ type: String, default: 'object' }, 
    selectedRowKey: { type: Array, required: true, default: () => [] }, //表单数据
    extra: { type: Object }, //表单数据
    formValue:{type: Object},//参数
    client: { type: String, default: '' }, //装修类型
    diy_type: { type: String, default: 'home' }, //装修来源类型: home-首页手机装修 topic-专题装修 integral-积分商城装修 spreader-推手装修
  });

  const dataKey = ref('goodsId'); //列表key值，默认为商品id

  const emit = defineEmits(['cancleEvent', 'confirmEvent']);

  //由于watch无法监听props.modalVisible，所以需要计算属性转为ref对象
  const modalVisible = computed(() => {
    return props.modalVisible;
  });
  //由于watch无法监听props.modalVisible，所以需要计算属性转为ref对象
  const formValue = computed(() => {
    return props.formValue;
  });

  watch(modalVisible, () => {
    if (modalVisible.value) {
      if (props.diy_type == 'integral') {
        dataKey.value = 'integralGoodsId';
      } else {
        dataKey.value = 'goodsId';
      }
      get_list({ pageSize: list_com_page_size_16 }, true);
      if(props.arrType=='array'){
        let obj = []
        props.selectedRow.forEach(item=>{
          obj.push(...item)
        })
        selectedRows.value = obj;
        selectedRowKeys.value = [...props.selectedRowKey];
      }else{
        selectedRows.value = [...props.selectedRow];
        selectedRowKeys.value = [...props.selectedRowKey];
      }
    }
  });

  const schemas = ref([
    {
      field: 'goodsName',
      component: 'Input',
      colProps: { span: 6, style: 'width:250px;max-width:100%;flex:none' },
      componentProps: {
        placeholder: '请输入商品名称',
        size: 'default',
      },
      label: '商品名称',
      labelWidth: 70,
      rules: [
        {
          // @ts-ignore
          validator: async (rule, value) => {
            await validatorEmoji(rule, value);
          },
          trigger: 'change',
        },
      ],
    },
  ]);

  // 获取数据
  const get_list = async (param, flag) => {
    let new_params = { ...param, ...formValues.value ,...props.formValue,state:3};
    let res = null;
    if (props.diy_type == 'integral') {
      res = await getIntegralGoodsListApi(new_params);
    }
    //spreader-2-start
    else if (props.diy_type == 'spreader') {
      res = await getSpreaderGoodsListApi(new_params);
    }
    //spreader-2-end
    else {
      if (flag) {
        schemas.value = schemas.value.filter(item => item.field != 'shopType');
        if (props.client == 'mobile') {
          let obj = {
            component: 'Select',
            label: `商品类型`,
            field: 'shopType',
            colProps: { span: 6, style: 'width:230px !important;max-width:100%;flex:none' },
            componentProps: {
              placeholder: `请选择商品类型`,
              options: [
                { value: '', label: `全部` },
                { value: '1', label: `普通商品` },
                { value: '2', label: `O2O商品` },
              ],
            },
          }
          schemas.value.push(obj);
          if (props.diy_type == 'home' || props.diy_type == 'topic') {
            new_params.shopType = '1';
            setTimeout(() => {
              setFieldsValue({
                shopType: '1',
              })
            }, 200);
          } else if (props.diy_type == 'o2o') {
            new_params.shopType = '2';
            setTimeout(() => {
              setFieldsValue({
                shopType: '2',
              })
            }, 200);
          }
          new_params.TerminalSource = 2;
        } else {
          new_params.shopType = '1';
          new_params.TerminalSource = 1;
        }
      } else {
        new_params.TerminalSource = props.client == 'mobile' ? 2 : 1;
      }
      // dev_supplier-start
      if(props.client&&props.client=='supplier'){
        res = await getGoodsSupplierListApi({...new_params,saleModel:'2'});
      }else{
      // dev_supplier-end
        res = await getGoodsListApi(new_params);
      // dev_supplier-start
      }
      // dev_supplier-end
    }
    if (res.state == 200) {
      if (res.data.pagination != undefined) {
        // dev_supplier-start
        if(props.client&&props.client=='supplier'){
          res.data.list.forEach(item=>{
            item.goodsPrice = item.wholesalePrice
          })
        }
        // dev_supplier-end
        if (res.data.pagination.current == 1) {
          data.value = res.data;
        } else {
          data.value.list = data.value.list.concat(res.data.list);
          data.value.pagination = res.data.pagination;
        }
      }
      loading_pagination_flag.value = false;
    }
  };

  //表单
  const [registerForm, { validate, getFieldsValue, setFieldsValue }] = useForm({
    schemas: schemas,
    labelWidth: 80,
    baseColProps: { span: 6 },
    actionColOptions: { span: 24 },
    autoSubmitOnEnter: true,
    showAdvancedButton: true,
    submitFunc: search,
    resetFunc: reset,
  });

  // 搜索
  async function search() {
    await validate();
    let values = getFieldsValue();
    for (let i in values) {
      if (values[i] == undefined) {
        delete values[i];
      }
    }
    params.value = { pageSize: list_com_page_size_16 };
    formValues.value = values;
    get_list({ ...params.value }, null);
  }

  async function reset() {
    params.value.current = 1;
  }

  // 确定
  const sldConfirm = () => {
    if (selectedRowKeys.value.length > 0) {
      if (
        props.extra.min_num != undefined &&
        props.extra.min_num > 0 &&
        selectedRowKeys.value.length < props.extra.min_num
      ) {
        failTip(`该模块至少需要选择${props.extra.min_num}个商品`);
        return false;
      }
      if (
        props.extra.total_num != undefined &&
        props.extra.total_num > 0 &&
        selectedRowKeys.value.length != props.extra.total_num
      ) {
        failTip(`该模块需要选择${props.extra.total_num}个商品`); //该模块需要选择   个商品
        return false;
      }
      if (
        props.extra.max_num != undefined &&
        props.extra.max_num > 0 &&
        selectedRowKeys.value.length > props.extra.max_num
      ) {
        failTip(`该模块最多选择${props.extra.max_num}个商品`);
        return false;
      }
      let obj = []
      if(props.client=='mobile'){
        if(props.diy_type=='integral'){
          selectedRows.value.forEach(item=>{
            let info = {
              integralGoodsId:item.integralGoodsId,
              productId:item.productId,
              goodsName:item.goodsName,
              integralPrice:item.integralPrice,
              cashPrice:item.cashPrice,
              mainImage:item.mainImage,
              marketPrice:item.marketPrice,
              state:item.state,
              actualSales:item.actualSales,
            }
            obj.push(info)
          })
        }else if(props.diy_type=='spreader'){
          selectedRows.value.forEach(item=>{
            let info = {
              goodsId:item.goodsId,
              goodsName:item.goodsName,
              productId:item.productId,
              productPrice:item.productPrice,
              spreaderGoodsId:item.spreaderGoodsId,
              goodsImage:item.goodsImage,
              state:item.state,
            }
            obj.push(info)
          })
        }else{
          selectedRows.value.forEach(item=>{
            let info = {
              goodsId:item.goodsId,
              productId:item.productId,
              goodsName:item.goodsName,
              goodsPrice:item.goodsPrice,
              mainImage:item.mainImage,
              marketPrice:item.marketPrice,
              state:item.state,
              actualSales:item.actualSales,
            }
            obj.push(info)
          })
        }
      }else if(props.client == 'pc'){
        selectedRows.value.forEach(item=>{
          let info = {
            goodsId:item.goodsId,
            productId:item.productId,
            goodsName:item.goodsName,
            goodsPrice:item.goodsPrice,
            mainImage:item.mainImage,
            marketPrice:item.marketPrice,
            state:item.state,
            actualSales:item.actualSales,
            virtualSales:item.virtualSales,
          }
          obj.push(info)
        })
      }else{
        obj = JSON.parse(JSON.stringify(selectedRows.value))
      }
      emit('confirmEvent', JSON.parse(JSON.stringify(obj)), selectedRowKeys.value);
    } else {
      failTip(`请选择商品`); //请选择商品
    }
  };

  // 取消
  const sldCancle = () => {
    selectedRows.value = [];
    selectedRowKeys.value = [];
    params.value = { pageSize: list_com_page_size_16 };
    formValues.value = {};
    emit('cancleEvent');
  };

  const handleScrollLeft = (e) => {
    //e.srcElement.scrollTop: 滚动条距离页面顶部距离
    //e.srcElement.clientHeight: 滚动页面高度
    //e.srcElement.scrollHeight: 滚动条高度
    // if (e.srcElement.scrollTop + e.srcElement.clientHeight > e.srcElement.scrollHeight - 10) {//-50距离底部50px是触发以下内容
    // }
    if (
      data.value.pagination.current * list_com_page_size_16 < data.value.pagination.total &&
      !loading_pagination_flag.value
    ) {
      //请求分页数据
      loading_pagination_flag.value = true;
      get_list({ pageSize: list_com_page_size_16, current: data.value.pagination.current * 1 + 1 }, null);
    }
  };

  //左侧数据点击事件（将选中的数据添加到右侧，左侧添加选中标识）
  const handleLeftItem = (item) => {
    if (selectedRowKeys.value.indexOf(item[dataKey.value]) == -1) {
      selectedRowKeys.value.push(item[dataKey.value]);
      selectedRows.value.push(item);
    }
  };

  //右侧数据点击事件（移除选中数据，右侧将不显示，左侧的选中标识去掉）
  const handleRightItem = (item) => {
    selectedRows.value = selectedRows.value.filter((items) => items[dataKey.value] != item[dataKey.value]);
    selectedRowKeys.value = selectedRowKeys.value.filter((items) => items != item[dataKey.value]);
  };

  onMounted(() => {});
</script>
<style lang="less">
  @import './index.less';
</style>
