// 分类管理-品牌弹窗列表
export const cate_list_brand_column = [
  {
    title: '品牌名称',
    dataIndex: 'brandName',
    width: 100,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 100,
  },
];
// 分类管理-品牌弹窗筛选项
export const cate_list_brand_schema = [
  {
    field: 'brandName',
    component: 'Input',
    colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
    componentProps: {
      minWidth: 300,
      maxlength: 10,
      placeholder: '请输入品牌名称',
      size: 'default',
    },
    label: '品牌名称',
    labelWidth: 70,
  },
];
// 分类管理-属性弹窗筛选项
export const cate_list_attr_column = [
  {
    title: '属性名称',
    dataIndex: 'attributeName',
    width: 100,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 100,
  },
  {
    title: '排序',
    dataIndex: 'sort',
    width: 100,
  },
];
// 分类管理-属性弹窗筛选项
export const cate_list_attr_schema = [
  {
    field: 'attributeName',
    component: 'Input',
    colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
    componentProps: {
      minWidth: 300,
      maxlength: 10,
      placeholder: '请输入属性名称',
      size: 'default',
    },
    label: '属性名称',
    labelWidth: 70,
  },
];

// 资源列表
export const column_resource_list = [
  {
    title: '资源名称',
    dataIndex: 'content',
    width: 300,
  },
  {
    title: 'url',
    dataIndex: 'url',
  },
];
// 资源列表筛选项
export const column_resource_list_schema = [
  {
    field: 'url',
    component: 'Input',
    colProps: { span: 6, style: 'width:280px;max-width:100%;flex:none' },
    componentProps: {
      minWidth: 300,
      placeholder: '请输入url',
      size: 'default',
    },
    label: 'url',
    labelWidth: 30,
  },
];