<template>
  <div class="hot_area_display" :style="{ ...outSideStyle }">
    <div class="relative overflow-hidden" :style="innerStyle">
      <template v-if="data.area_list.length">
        <div
          v-for="(item, index) in data.area_list"
          :key="index"
          class="area_item"
          :style="getStyle(item)"
        />
      </template>

      <img :src="data.img_info.imgUrl" alt="" v-if="data.img_info.imgUrl" class="main_image" />

      <template v-else>
        <div class="empty flex_column_center_center">
          <AliSvgIcon
            iconName="icontupian_huaban"
            width="60px"
            height="60px"
            fillColor="#fff"
          ></AliSvgIcon>
          <div class="diy_img_placeholder flex_row_center_center">
            <span> 此处添加图片【宽高不限】 </span>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup>
  import { computed, unref } from 'vue';
  import { DECO_DISPLAY_WIDTH_MOBILE } from '../src/common';

  const props = defineProps({
    data: {
      type: Object,
      default: () => ({
        area_list: [],
      }),
    },
  });

  const { img_info } = props.data;

  const realWidth = computed(() => {
    let { padding } = props.data.style;
    return DECO_DISPLAY_WIDTH_MOBILE - (padding.left + padding.right);
  });

  const realHeight = computed(() => {
    let { width, height } = img_info;
    return (realWidth.value * height) / width;
  });

  //ratio = W/H
  const getStyle = (item) => {
    let { areaWidth_ratio, areaHeight_ratio, startX_ratio, startY_ratio } = item;
    let width = areaWidth_ratio * realWidth.value + 'px';
    let height = areaHeight_ratio * realHeight.value + 'px';
    let left = startX_ratio * realWidth.value + 'px';
    let top = startY_ratio * realHeight.value + 'px';
    return {
      width,
      height,
      top,
      left,
    };
  };

  const outSideStyle = computed(() => {
    let { padding, bgColor } = props.data.style;
    return {
      padding: `${padding.top}px ${padding.right}px ${padding.bottom}px ${padding.left}px`,
      backgroundColor: bgColor,
    };
  });

  const innerStyle = computed(() => {
    let { borderRadius } = props.data.style;
    return {
      borderRadius: `${borderRadius.top}px ${borderRadius.top}px ${borderRadius.bottom}px  ${borderRadius.bottom}px`,
    };
  });
</script>

<style lang="scss" scoped>
  .hot_area_display {
    width: 371px;
  }

  .area_item {
    position: absolute;
    background: rgb(41 128 185 / 70%);
  }

  .empty {
    width: 100%;
    height: 200px;
    background-color: #ecf5ff;
  }

  .main_image {
    width: 100%;
    height: 100%;
    user-select: none;
  }
</style>
