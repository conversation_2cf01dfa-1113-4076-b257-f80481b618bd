<template>
  <div>
    <Modal
      v-model:visible="modalVisible"
      title="热区"
      :width="1200"
      wrapClassName="hot_area_modal"
      @cancel="modalVisible = false"
      @ok="hotEditConfirm"
    >
      <div class="operation_floor" :style="{ height: MODAL_HEIGHT + 'px' }">
        <div class="imgBox">
          <div class="container_con flex_row_between_start">
            <div
              class="img_area relative"
              :style="{
                width: `${EDIT_AREA_WIDTH + 7}px`,
                height: `${EDIT_AREA_HEIGHT + 8}px`,
              }"
              @mouseleave="mouseleave"
            >
              <!--已有的热区-->
              <AreaBox
                v-for="(item, index) in areaData"
                :id="index"
                :key="item.uid"
                :area-init.sync="item"
                @delAreaBox="delAreaBox"
              />
              <div class="img_wrapper">
                <img
                  ref="backgroundImg"
                  :src="imgInfo.imgUrl"
                  alt="hotarea"
                  draggable="false"
                  :style="imgStyle.domStyle"
                />
              </div>
            </div>

            <!-- 热区列表 -->
            <div class="hot_area_list_container">
              <Button type="primary" @click="addArea">
                <div class="flex_row_center_center">
                  <PlusOutlined />
                  <span class="ml-2">添加热区</span>
                </div>
              </Button>

              <div class="hot_area_list mt-4" :style="{ height: MODAL_HEIGHT - 90 + 'px' }">
                <div
                  class="flex_row_start_start mb-5"
                  v-for="(item, index) in areaData"
                  :key="index"
                >
                  <div class="mt-1 mr-4">热区：{{ index + 1 }}</div>
                  <div>
                    <div class="flex_row_start_center">
                      <Select
                        :options="diy_link_options"
                        defaultValue=""
                        :value="item.link_type"
                        class="w-30"
                        @change="(e) => selectChange(e, item)"
                      />
                      <div class="ml-3">
                        <AliSvgIcon
                          iconName="iconshanchu"
                          width="14px"
                          height="14px"
                          fillColor="primaryColor"
                          class="cursor-pointer"
                          @click="delAreaBox(item.uid)"
                        ></AliSvgIcon>
                      </div>
                    </div>
                    <div class="extra_item" v-if="item.link_type">
                      <div class="flex items-center" v-if="item.link_type == 'url'">
                        <Input placeholder="请输入链接地址" v-model:value="item.link_value"></Input>
                      </div>
                      <div class="flex items-center" v-else-if="item.link_type == 'keyword'">
                        <Input placeholder="请输入关键字" v-model:value="item.link_value"></Input>
                      </div>
                      <div
                        class="flex items-start item_goods"
                        v-else-if="item.link_type == 'goods'"
                      >
                        <img
                          :src="item.info.mainImage ? item.info.mainImage : item.info.goodsImage"
                          alt=""
                          class="extra_item_goods_img"
                        />
                        <div class="area_text_name ml-2">{{ item.info.goodsName }}</div>
                      </div>
                      <div class="area_text_name" v-else-if="item.link_value">
                        {{ singleDiy_type_name_map[item.link_type] }}：{{ item.link_value }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Modal>
    <SldSelGoodsSingleDiy
      :modalVisible="diyVisible"
      v-bind="modalProps"
      @cancleEvent="selectDiyCancel"
      @confirmEvent="selectDiyConfirm"
    />
  </div>
</template>

<script setup>
  import AreaBox from './Area.vue';
  import { computed, ref, unref } from 'vue';
  import { Modal, Button, Select, Input } from 'ant-design-vue';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import {
    new_diy_link_type,
    new_m_diy_link_type,
    singleDiy_type_name_map,
    new_m_point_diy_link_type,
    new_supplier_diy_link_type,
    spreader_diy_spreader_link_type,
  } from '/@/utils/utils';
  import { AliSvgIcon } from '@/components/SvgIcon';
  import {
    setSldSelProps,
    handleDiyGoodsConfirm,
    SldSelGoodsSingleDiy,
  } from '../../SldSelGoodsSingleDiy';
  import { MODAL_HEIGHT, EDIT_AREA_HEIGHT, EDIT_AREA_WIDTH } from './common';
  import { buildShortUUID } from '/@/utils/uuid';

  const modalVisible = ref(false);

  const props = defineProps({
    visibleModal: Boolean,
    imgInfo: {
      type: Object,
      default: () => {},
    },
    areaDataList: {
      type: Array,
      default: () => [],
    },

    client: {
      type: String,
      default: 'mobile',
    },

    mode: {
      type: String,
      default: 'default',
    },
  });

  /**
   * 图片长宽
   * @desc 不同长宽的图片放入容器时，进行计算，保证图片长和宽其一充满容器。
   *        如果图片长宽比例大于容器长宽比例，则图片长度满容器宽，高度按比例变化，
   *        否则高度满容器高，长度按比例变化
   * */
  const imgStyle = computed(() => {
    let { width, height } = props.imgInfo;
    let rImg = width / height;
    let rArea = EDIT_AREA_WIDTH / EDIT_AREA_HEIGHT;
    let realWidth, realWheight;
    if (rImg < rArea) {
      realWidth = EDIT_AREA_WIDTH;
      realWheight = EDIT_AREA_WIDTH / rImg;
    } else {
      realWidth = EDIT_AREA_HEIGHT * rImg;
      realWheight = EDIT_AREA_HEIGHT;
    }
    return {
      domStyle: {
        width: `${realWidth}px`,
        height: `${realWheight}px`,
      },
      realWidth,
      realWheight,
    };
  });

  const emit = defineEmits(['confirm']);

  const diy_link = computed(() => {
    if (props.client == 'mobile') {
      switch (props.mode) {
        case 'integral':
          return new_m_point_diy_link_type;
        case 'spreader':
          return spreader_diy_spreader_link_type;
        default:
          return new_m_diy_link_type;
      }
    }
    if (props.client == 'supplier') {
      return new_supplier_diy_link_type;
    }
    return new_diy_link_type;
  });

  const diy_link_options = computed(() => diy_link.value.formatOptions());

  const areaData = ref(props.areaDataList);

  const modalProps = ref({});

  const diyVisible = ref(false);

  const addArea = () => {
    const data = {
      startX: 0,
      startY: 0,
      areaWidth: 100,
      areaHeight: 100,
      link_type: '',
      link_value: '',
      info: {},
      uid: buildShortUUID('hot_area'),
    };
    areaData.value.push(data);
  };

  // 删除指定热区
  const delAreaBox = (uid) => {
    let index = unref(areaData).findIndex((v) => v.uid == uid);
    areaData.value.splice(index, 1);
  };

  const mouseleave = () => {
    document.onmousemove = null;
  };

  const currentHot = ref({});

  //select change回调
  const selectChange = (val, item) => {
    item.link_type = val;
    item.link_value = '';
    currentHot.value = item;
    if (unref(diy_link)[val].require_select_modal) {
      modalProps.value = setSldSelProps(val, props.mode, props.client);
      diyVisible.value = true;
    }
  };
  //弹窗确认事件
  const selectDiyConfirm = (e) => {
    diyVisible.value = false;
    let { link_type } = unref(currentHot);
    let item = handleDiyGoodsConfirm(link_type, e, props.client, props.mode);
    unref(currentHot).info = item.info;
    unref(currentHot).link_value = item.link_value;
  };

  const selectDiyCancel = () => {
    diyVisible.value = false;
    unref(currentHot).link_type = '';
  };

  const open = (bool) => {
    modalVisible.value = bool;
  };

  const handleRatio = () => {
    let { realWidth, realWheight } = unref(imgStyle);

    areaData.value.map((item) => {
      item.startX_ratio = item.startX / realWidth;
      item.startY_ratio = item.startY / realWheight;
      item.areaWidth_ratio = item.areaWidth / realWidth;
      item.areaHeight_ratio = item.areaHeight / realWheight;
    });
  };

  const hotEditConfirm = () => {
    handleRatio();
    open(false);
    emit('confirm');
  };

  defineExpose({
    open,
  });
</script>

<style>
  .hot_area_modal {
    min-width: 1200px;
  }
</style>

<style scoped lang="less">
  .operation_floor {
    position: relative;
    width: 100%;

    .header {
      .titleBox {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 100px;

        .name {
          font-size: 13px;
          font-weight: bold;
        }
      }

      .textBox {
        margin-bottom: 10px;
        color: #777;
        font-size: 12px;
      }
    }

    .imgBox {
      display: flex;
      align-items: center;
      min-width: 1160px;
      height: 100%;
      padding: 20px;

      .container_con {
        position: relative;
      }

      .img_area {
        flex: 1;
        height: 100%;
        overflow: scroll;
        background-color: #eee;
      }

      img {
        user-select: none;
        object-fit: contain;
        touch-action: none;
      }

      .area {
        position: absolute;
        top: 300px;
        left: 200px;
        width: 200px;
        height: 200px;
        border: 1px dashed #34495e;
        background: rgba(#2980b9, 0.3);
      }
    }
  }

  .area_text_name {
    display: -webkit-box;
    width: 140px;
    overflow: hidden;
    font-size: 13px;
    text-overflow: ellipsis;
    word-break: break-all;
    white-space: normal;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .hot_area_list_container {
    width: 320px;
    height: 100%;
    margin-left: 20px;

    .hot_area_list {
      overflow: auto;
    }
  }

  .extra_item {
    margin-top: 12px;

    .item_goods {
      padding: 10px;
      border: 1px solid #d9d9d9;
    }

    .extra_item_goods_img {
      width: 50px;
      height: 50px;
    }

    .extra_item_label {
      margin-right: 10px;
      white-space: nowrap;
    }
  }
</style>
