<template>
  <Modal
    title="样式设置"
    width="600px"
    :visible="visible"
    @cancel="emit('cancel')"
    @ok="handleConfirm"
  >
    <div class="style_set_modal">
      <div class="hot_area_item">
        <div class="subtitle">选择背景色</div>
        <div class="flex_row_start_center pl-5">
          <ColorPicker :color="bgColor" @update:color="handleColorUpdate" />
        </div>
      </div>

      <div class="hot_area_item">
        <div class="subtitle">边距</div>
        <div class="flex_row_start_center mb-2 pl-5">
          <div class="item_label">上边距</div>
          <Slider
            v-model:value="paddingValue.top"
            :min="0"
            :max="20"
            class="w-40"
            @afterChange="sliderAfterChange"
          ></Slider>
          <div class="item_px_label">{{ paddingValue.top }}&nbsp;px</div>
        </div>
        <div class="flex_row_start_center mb-2 pl-5">
          <div class="item_label">下边距</div>
          <Slider
            v-model:value="paddingValue.bottom"
            :min="0"
            :max="20"
            class="w-40"
            @afterChange="sliderAfterChange"
          ></Slider>
          <div class="item_px_label">{{ paddingValue.bottom }}&nbsp;px</div>
        </div>
        <div class="flex_row_start_center mb-2 pl-5">
          <div class="item_label">左边距</div>
          <Slider
            v-model:value="paddingValue.left"
            :min="0"
            :max="20"
            class="w-40"
            @afterChange="sliderAfterChange"
          ></Slider>
          <div class="item_px_label">{{ paddingValue.left }}&nbsp;px</div>
        </div>
        <div class="flex_row_start_center mb-2 pl-5">
          <div class="item_label">右边距</div>
          <Slider
            v-model:value="paddingValue.right"
            :min="0"
            :max="20"
            class="w-40"
            @afterChange="sliderAfterChange"
          ></Slider>
          <div class="item_px_label">{{ paddingValue.right }}&nbsp;px</div>
        </div>
      </div>

      <div class="hot_area_item">
        <div class="subtitle">圆角</div>
        <div class="flex_row_start_center mb-2 pl-5">
          <div class="item_label">上圆角</div>
          <Slider
            v-model:value="borderRadiusValue.top"
            :min="0"
            :max="20"
            class="w-40"
            @afterChange="sliderAfterChange"
          ></Slider>
          <div class="item_px_label">{{ borderRadiusValue.top }}&nbsp;px</div>
        </div>
        <div class="flex_row_start_center mb-2 pl-5">
          <div class="item_label">下圆角</div>
          <Slider
            v-model:value="borderRadiusValue.bottom"
            :min="0"
            :max="20"
            class="w-40"
            @afterChange="sliderAfterChange"
          ></Slider>
          <div class="item_px_label">{{ borderRadiusValue.bottom }}&nbsp;px</div>
        </div>
      </div>
    </div>
  </Modal>
</template>

<script setup>
  import { Modal, Slider } from 'ant-design-vue';
  import { reactive, ref, unref } from 'vue';
  import ColorPicker from '/@/components/ColorPicker/index.vue';

  const props = defineProps({
    style: {
      type: Object,
      default: () => ({
        bgColor: '#ffffff',
        padding: {},
        borderRadius: {},
      }),
    },
    visible: Boolean,
  });

  const emit = defineEmits(['cancel', 'confirm']);

  const initialize = (type) => {
    switch (type) {
      case 'bgColor': {
        let { style } = props;
        return style.bgColor ?? '#ffffff';
      }

      case 'padding':
      case 'borderRadius': {
        let { style } = props;
        let item = {};
        for (let k in style[type]) {
          item[k] = style[type][k] ?? 0;
        }
        return item;
      }
    }
  };

  // 颜色选择器
  const handleColorUpdate = (val) => {
    bgColor.value = 'rgba(' + val.r + ',' + val.g + ',' + val.b + ',' + val.a + ')';
  };

  const bgColor = ref(initialize('bgColor'));

  const paddingValue = reactive(initialize('padding'));

  const borderRadiusValue = reactive(initialize('borderRadius'));

  const sliderAfterChange = () => {};

  const handleConfirm = () => {
    emit('confirm', {
      bgColor: unref(bgColor),
      padding: { ...paddingValue },
      borderRadius: { ...borderRadiusValue },
    });
  };
</script>

<style lang="less" scoped>
  .hot_area_item {
    padding: 0 20px 20px;
    border-bottom: 5px solid #f6f7f9;

    .subtitle {
      color: #47565d;
      font-size: 13px;
      font-weight: bolder;
      line-height: 40px;
    }
  }

  .style_set_modal {
    padding: 0 50px;
  }

  .item_label {
    margin-right: 10px;
    color: #666;
    font-size: 13px;
  }

  .item_px_label {
    width: 70px;
    height: 30px;
    margin-left: 5px;
    border-radius: 5px;
    background-color: #eee;
    line-height: 30px;
    text-align: center;
  }
</style>
