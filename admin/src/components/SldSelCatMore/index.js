
import { getSeckilUnfinishedApi } from '@/api/promotion/seckill';
import { ImageCustomRender } from '@/components/SldStat';
import SldSelGoodsSingleDiy from './index.vue';

export { SldSelGoodsSingleDiy };

//选择器选择到需要弹窗的选项时的设置属性
export const setSldSelProps = (type, mode, client) => {
  const modalProps = {
    searchInfo: {},
    diy_type: mode,
    link_type: type,
    client
  };
  return modalProps;
};
