<template>
  <span :class="`${prefixCls} flex items-center must_add_head_position`">
    <!-- <Icon
      v-if="getIcon && getPath != '/marketing_promotion'"
      :icon="getIcon"
      :size="18"
      :class="`${prefixCls}-wrapper__icon mr-2`"
    /> -->

    <!-- <SvgIcon
      :iconName="getIcon"
      v-if="getIcon"
      width="16px"
      height="16px"
      :class="`${prefixCls}-wrapper__icon mr-2`"
      fillColor="tranparent"
    ></SvgIcon>

    -->
    <img
      src="@/assets/images/must_add.png"
      alt=""
      v-if="extra.indexOf(getPath)!=-1&&specialFlag > -3"
      class="must_add_head"
    /> 
    <span class="text-[16px]">{{ getI18nName }}</span>
  </span>
</template>
<script lang="ts">
  import { computed, defineComponent,ref } from 'vue';
  import Icon from '@/components/Icon/Icon.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { specialFlag } from '/@/utils/utils';
  import { contentProps } from '../props';
  import SvgIcon from '@/components/SvgIcon/index.vue';

  const { t } = useI18n();

  export default defineComponent({
    name: 'MenuItemContent',
    components: {
      Icon,
      SvgIcon,
    },
    props: contentProps,
    setup(props) {
      const { prefixCls } = useDesign('basic-menu-item-content');
      const getI18nName = computed(() => t(props.item?.name));
      const getIcon = computed(() => props.item?.icon);
      const getPath = computed(() => props.item?.path);
      const extra = ref(['/operation','/supplier'])
      return {
        prefixCls,
        getI18nName,
        getIcon,
        getPath,
        extra,
        specialFlag
      };
    },
  });
</script>

<style lang="less">
  .ant-menu-item-active,
  .ant-menu-item-selected {
    fill: @primary-color !important;
  }
  .must_add_head_position{
    position: relative;
    .must_add_head{
      width: 28px;
      height: 14px;
      position: absolute;
      top: 9px;
      right: -27px;
    }
  }
</style>
