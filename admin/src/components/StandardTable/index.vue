<template>
  <div class="standard_table">
    <BasicTable @register="standardTable">
      <template #tableTitle>
        <template v-for="(item, index) in props.toolbar" :key="index">
          <a-button :type="item.btnType ? item.btnType : 'default'" @click="handleClickBtn(item)">{{
            item.label
          }}</a-button>
        </template>
      </template>
      <template #bodyCell="{ column, record, text }">
        <template v-for="(item, index) in props.bodyCell" :key="index">
          <template v-if="column.key == item.key">
            {{ item.render(record, text) }}
          </template>
        </template>
      </template>
    </BasicTable>
  </div>
</template>
<script setup>
  import { BasicTable, useTable } from '/@/components/Table';

  const props = defineProps({
    api: { type: Function, required: true, default: () => null }, //请求接口
    title: { type: String, required: false, default: '' }, //表格标题
    titleHelpMessage: { type: String, required: false, default: '' }, //表格标题右侧温馨提醒
    fetchSetting: {
      type: Object,
      required: false,
      default: function () {
        return {
          pageField: 'current',
          sizeField: 'pageSize',
          listField: 'data.list',
          totalField: 'data.pagination.total',
        };
      },
    }, //接口请求配置
    columns: {
      type: Array,
      required: true,
      default: () => [],
    }, //表单列信息
    actionColumn: {
      type: Object,
      required: false,
      default: () => {},
    }, //表格右侧操作列配置
    rowSelection: {
      type: Object,
      required: false,
      default: () => {},
    }, //选择列配置
    clickToRowSelect: { type: Boolean, required: false, default: false }, //选择列配置是否点击行触发选择
    useSearchForm: { type: Boolean, required: false, default: false }, //是否使用搜索表单
    handleSearchInfoFn: { type: Function, required: false, default: () => null }, //开启表单后，在请求之前处理搜索条件参数
    formConfig: {
      type: Array,
      required: false,
      default: () => [],
    }, //表单配置
    searchInfo: {
      type: Object,
      required: false,
      default: () => {},
    }, //额外的请求参数
    loading: { type: Boolean, required: false, default: false }, //表格 loading 状态
    inset: { type: Boolean, required: false, default: false }, //是否取消表格的默认 padding
    showHeader: { type: Boolean, required: false, default: true }, //是否显示表头
    bordered: { type: Boolean, required: false, default: false }, //是否显示表格边框
    showIndexColumn: { type: Boolean, required: false, default: false }, //是否展示序号列
    striped: { type: Boolean, required: false, default: false }, //是否显示斑马纹
    showTableSetting: { type: Boolean, required: false, default: false }, //是否显示表格设置工具
    beforeFetch: { type: Function, required: false, default: () => null }, //请求之前对参数进行处理
    afterFetch: { type: Function, required: false, default: () => null }, //请求之后对返回值进行处理
    toolbar: {
      type: Array,
      required: false,
      default: () => [],
    }, //操作按钮列表
    bodyCell: {
      type: Array,
      required: false,
      default: () => [],
    }, //自定义列表
  });

  const emit = defineEmits(['btnclickEvent']);

  const [standardTable] = useTable({
    api: (arg) => props.api({ ...arg }),
    title: props.title,
    titleHelpMessage: props.titleHelpMessage,
    fetchSetting: props.fetchSetting,
    columns: props.columns,
    actionColumn: props.actionColumn,
    rowSelection: props.rowSelection,
    clickToRowSelect: props.clickToRowSelect,
    useSearchForm: props.useSearchForm,
    handleSearchInfoFn: props.handleSearchInfoFn,
    formConfig: props.formConfig,
    searchInfo: props.searchInfo,
    loading: props.loading,
    inset: props.inset,
    showHeader: props.showHeader,
    bordered: props.bordered,
    showIndexColumn: props.showIndexColumn,
    striped: props.striped,
    showTableSetting: props.showTableSetting,
    beforeFetch: props.beforeFetch,
    afterFetch: props.afterFetch,
  });

  //按钮点击事件
  const handleClickBtn = (item) => {
    emit('btnclickEvent', item);
  };
</script>
<style lang="less" scoped>
  .standard_table {
    background: #fff;
  }
</style>
