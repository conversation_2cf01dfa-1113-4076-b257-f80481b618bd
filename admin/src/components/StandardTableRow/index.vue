<!-- 公共表格列表组件 -->
<template>
  <div class="standard_table_row" :style="{ borderTop: hideTopBorder ? 'none' : '' }">
    <Form ref="tableForm" :model="curFormData">
      <template v-for="(item, index) in props.data" :key="index">
        <FormItem
          v-if="!item.hidden"
          :name="item.key != undefined ? item.key : 'table_row_' + index"
          :rules="item.rules != undefined ? item.rules : undefined"
        >
          <div v-if="item.type == 'title'" class="common_title" :style="{ width: width,borderTop:index==0?'none':'1px solid #f0f0f0' }">
            <!-- 标题 -->
            <span class="common_title_label">{{ item.label }}</span>
          </div>
          <div
            v-else-if="item.type == 'line'"
            class="common_line"
            :style="{
              height: item.height ? item.height + (typeof item.height == 'number' ? 'px' : '') : '',
            }"
          >
            <!-- 占位行 -->
          </div>
          <div v-else-if="item.type == 'show_text'" class="common_item" :style="{ width: width }">
            <!-- 文本 -->
            <div
              class="common_item_left"
              :style="{
                width: item.width ? item.width + (typeof item.width == 'number' ? 'px' : '') : '',
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
                lineHeight: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
                paddingRight: item.paddingRight
                  ? item.paddingRight + (typeof item.paddingRight == 'number' ? 'px' : '')
                  : '',
              }"
              ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
            >
            <div class="common_item_right">{{ item.value }}</div>
          </div>
          <div v-else-if="item.type == 'input'" class="common_item" :style="{ width: width }">
            <!-- 输入框 -->
            <div
              class="common_item_left"
              :style="{
                width: item.width ? item.width + (typeof item.width == 'number' ? 'px' : '') : '',
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
                lineHeight: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
                paddingRight: item.paddingRight
                  ? item.paddingRight + (typeof item.paddingRight == 'number' ? 'px' : '')
                  : '',
              }"
              ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
            >
            <div
              class="common_item_right"
              :style="{
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
              }"
            >
              <a-input
                :type="item.inputType ? item.inputType : 'text'"
                :placeholder="item.placeholder ? item.placeholder : '请输入'"
                :maxlength="item.maxlength ? item.maxlength : undefined"
                :showCount="item.showCount ? item.showCount : false"
                :allowClear="item.allowClear ? item.allowClear : false"
                :disabled="item.disabled ? item.disabled : false"
                v-model:value="curFormData[item.key]"
                @change="(e) => handleSelectChange(item, e.target.value, null)"
                :style="{
                  width: item.right_width
                    ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                    : '300px',
                }"
              />
              <div
                v-if="item.desc"
                class="common_item_right_desc"
                :style="{
                  width: item.desc_width
                    ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                    : '300px',
                }"
                >{{ item.desc }}</div
              >
            </div>
          </div>
          <div v-else-if="item.type == 'inputnum'" class="common_item" :style="{ width: width }">
            <!-- 数字输入框 -->
            <div
              class="common_item_left"
              :style="{
                width: item.width ? item.width + (typeof item.width == 'number' ? 'px' : '') : '',
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
                lineHeight: item.lineHeight ? item.lineHeight + (typeof item.lineHeight == 'number' ? 'px' : '')
                  : item.height ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
                paddingRight: item.paddingRight
                  ? item.paddingRight + (typeof item.paddingRight == 'number' ? 'px' : '')
                  : '',
              }"
              ><span v-if="item.require && !item.isNotShow" class="common_item_require">*</span
              >{{ item.label }}</div
            >
            <div
              class="common_item_right"
              :style="{
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
              }"
            >
              <div class="common_item_right_input">
                <span
                  v-if="item.input_before_text"
                  :style="{
                    marginRight: item.beftore_text_right
                      ? item.beftore_text_right +
                        (typeof item.beftore_text_right == 'number' ? 'px' : '')
                      : '10px',
                  }"
                  ><span v-if="item.require" class="common_item_require">*</span
                  >{{ item.input_before_text }}</span
                >
                <InputNumber
                  :placeholder="item.placeholder ? item.placeholder : '请输入'"
                  :min="item.min ? item.min : 0"
                  :max="item.max ? item.max : 9999999"
                  :precision="item.precision ? item.precision : 0"
                  :disabled="item.disable === true ? item.disable : false"
                  v-model:value="curFormData[item.key]"
                  @change="(e) => handleSelectChange(item, e, null)"
                  :style="{
                    width: item.width
                      ? item.width +
                        (typeof item.width == 'number' ? 'px !important' : ' !important')
                      : '300px !important',
                  }"
                />
                <span
                  v-if="item.input_after_text"
                  :style="{
                    marginLeft: item.after_text_left
                      ? item.after_text_left + (typeof item.after_text_left == 'number' ? 'px' : '')
                      : '10px',
                  }"
                  >{{ item.input_after_text }}</span
                >
              </div>
              <div
                v-if="item.desc"
                class="common_item_right_desc"
                :style="{
                  width: item.desc_width
                    ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                    : '300px',
                }"
                >{{ item.desc }}</div
              >
            </div>
          </div>
          <div v-else-if="item.type == 'textarea'" class="common_item" :style="{ width: width }">
            <!-- 文本区域 -->
            <div
              class="common_item_left"
              :style="{
                width: item.width ? item.width + (typeof item.width == 'number' ? 'px' : '') : '',
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '120px',
                lineHeight: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '120px',
                paddingRight: item.paddingRight
                  ? item.paddingRight + (typeof item.paddingRight == 'number' ? 'px' : '')
                  : '',
              }"
              ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
            >
            <div class="common_item_right">
              <a-textarea
                :placeholder="item.placeholder ? item.placeholder : '请输入'"
                :maxlength="item.maxlength ? item.maxlength : 255"
                :rows="item.rows ? item.rows : 3"
                :autosize="{ minRows: item.rows ? item.rows : 3 }"
                :showCount="item.showCount ? item.showCount : false"
                :allowClear="item.allowClear ? item.allowClear : false"
                :disabled="item.disabled ? item.disabled : false"
                v-model:value="item.value"
                @change="(e) => handleSelectChange(item, e.target.value, null)"
                :style="{
                  width: item.right_width
                    ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                    : '300px',
                }"
              />
              <div
                v-if="item.desc"
                class="common_item_right_desc"
                :style="{
                  width: item.desc_width
                    ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                    : '300px',
                }"
                >{{ item.desc }}</div
              >
            </div>
          </div>
          <div v-else-if="item.type == 'select'" class="common_item" :style="{ width: width }">
            <!-- 下拉选择框 -->
            <div
              class="common_item_left"
              :style="{
                width: item.width ? item.width + (typeof item.width == 'number' ? 'px' : '') : '',
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
                lineHeight: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
                paddingRight: item.paddingRight
                  ? item.paddingRight + (typeof item.paddingRight == 'number' ? 'px' : '')
                  : '',
              }"
              ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
            >
            <div
              class="common_item_right"
              :style="{
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
              }"
            >
              <Select
                :placeholder="item.placeholder ? item.placeholder : '请选择'"
                :mode="item.mode ? item.mode : ''"
                :allowClear="item.allowClear ? item.allowClear : false"
                :disabled="item.disabled ? item.disabled : false"
                :defaultOpen="item.defaultOpen ? item.defaultOpen : false"
                :showSearch="item.showSearch ? item.showSearch : false"
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                v-model:value="item.value"
                @change="(e) => handleSelectChange(item, e, null)"
                :style="{
                  width: item.right_width
                    ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                    : '300px',
                }"
              >
                <template v-if="item.data && item.data.length > 0">
                  <SelectOption
                    v-for="(items, indexs) in item.data"
                    :key="indexs"
                    :value="items.value"
                    >{{ items.title }}</SelectOption
                  >
                </template>
              </Select>
              <div
                v-if="item.desc"
                class="common_item_right_desc"
                :style="{
                  width: item.desc_width
                    ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                    : '300px',
                }"
                >{{ item.desc }}</div
              >
            </div>
          </div>
          <div v-else-if="item.type == 'radio'" class="common_item" :style="{ width: width }">
            <!-- 单选框 -->
            <div
              class="common_item_left"
              :style="{
                width: item.width ? item.width + (typeof item.width == 'number' ? 'px' : '') : '',
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
                lineHeight: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
                paddingRight: item.paddingRight
                  ? item.paddingRight + (typeof item.paddingRight == 'number' ? 'px' : '')
                  : '',
              }"
              ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
            >
            <div
              class="common_item_right"
              :style="{
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
              }"
            >
              <RadioGroup
                :disabled="item.disabled ? item.disabled : false"
                v-model:value="item.value"
                @change="(e) => handleSelectChange(item, e.target.value, null)"
                :style="{
                  width: item.right_width
                    ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                    : '',
                }"
              >
                <template v-if="item.radio_type == 'button'">
                  <RadioButton
                    v-for="(items, indexs) in item.data"
                    :key="indexs"
                    :value="items.value"
                    >{{ items.title }}</RadioButton
                  >
                </template>
                <template v-else>
                  <Radio v-for="(items, indexs) in item.data" :key="indexs" :value="items.value">{{
                    items.title
                  }}</Radio>
                </template>
              </RadioGroup>
              <div
                v-if="item.desc"
                class="common_item_right_desc"
                :style="{
                  width: item.desc_width
                    ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                    : '300px',
                }"
                >{{ item.desc }}</div
              >
            </div>
          </div>
          <div v-else-if="item.type == 'checkbox'" class="common_item" :style="{ width: width }">
            <!-- 多选框 -->
            <div
              class="common_item_left"
              :style="{
                width: item.width ? item.width + (typeof item.width == 'number' ? 'px' : '') : '',
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
                lineHeight: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
                paddingRight: item.paddingRight
                  ? item.paddingRight + (typeof item.paddingRight == 'number' ? 'px' : '')
                  : '',
              }"
              ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
            >
            <div
              class="common_item_right"
              :style="{
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
              }"
            >
              <CheckboxGroup
                :disabled="item.disabled ? item.disabled : false"
                v-model:value="item.value"
                @change="(e) => handleSelectChange(item, e, null)"
                :style="{
                  width: item.right_width
                    ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                    : '',
                }"
              >
                <template v-if="item.data && item.data.length > 0">
                  <Checkbox
                    v-for="(items, indexs) in item.data"
                    :key="indexs"
                    :value="items.value"
                    :style="{
                      width: item.checkbox_width
                        ? item.checkbox_width + (typeof item.checkbox_width == 'number' ? 'px' : '')
                        : '',
                      margin: '2px 0',
                      padding: 0,
                      marginRight:item.checkbox_margin_right
                        ? item.checkbox_margin_right + (typeof item.checkbox_margin_right == 'number' ? 'px' : '')
                        : '10px',
                    }"
                    >{{ items.title }}</Checkbox
                  >
                </template>
              </CheckboxGroup>
              <div
                v-if="item.desc"
                class="common_item_right_desc"
                :style="{
                  width: item.desc_width
                    ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                    : '300px',
                }"
                >{{ item.desc }}</div
              >
            </div>
          </div>
          <div v-else-if="item.type == 'switch'" class="common_item" :style="{ width: width }">
            <!-- 开关 -->
            <div
              class="common_item_left"
              :style="{
                width: item.width ? item.width + (typeof item.width == 'number' ? 'px' : '') : '',
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
                lineHeight: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
                paddingRight: item.paddingRight
                  ? item.paddingRight + (typeof item.paddingRight == 'number' ? 'px' : '')
                  : '',
              }"
              ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
            >
            <div
              class="common_item_right"
              :style="{
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
              }"
            >
              <Switch
                :checkedValue="item.checkedValue ? item.checkedValue : true"
                :unCheckedValue="item.unCheckedValue ? item.unCheckedValue : false"
                :disabled="item.disabled ? item.disabled : false"
                :loading="item.loading ? item.loading : false"
                v-model:checked="item.value"
                @change="(e) => handleSelectChange(item, e, null)"
                :style="{
                  width: item.right_width
                    ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                    : '',
                }"
              />
              <div
                v-if="item.desc"
                class="common_item_right_desc"
                :style="{
                  width: item.desc_width
                    ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                    : '300px',
                }"
                >{{ item.desc }}</div
              >
            </div>
          </div>
          <div v-else-if="item.type == 'cascader'" class="common_item" :style="{ width: width }">
            <!-- 级联选择器 -->
            <div
              class="common_item_left"
              :style="{
                width: item.width ? item.width + (typeof item.width == 'number' ? 'px' : '') : '',
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
                lineHeight: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
                paddingRight: item.paddingRight
                  ? item.paddingRight + (typeof item.paddingRight == 'number' ? 'px' : '')
                  : '',
              }"
              ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
            >
            <div
              class="common_item_right"
              :style="{
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
              }"
            >
              <Cascader
                :placeholder="item.placeholder ? item.placeholder : '请选择'"
                :options="item.data ? item.data : []"
                :allowClear="item.allowClear ? item.allowClear : false"
                :disabled="item.disabled ? item.disabled : false"
                :expandTrigger="item.expandTrigger ? item.expandTrigger : 'click'"
                :fieldNames="
                  item.fieldNames
                    ? item.fieldNames
                    : {
                        label: 'label',
                        value: 'value',
                        children: 'children',
                      }
                "
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                :showSearch="item.showSearch ? item.showSearch : false"
                :matchInputWidth="item.matchInputWidth ? item.matchInputWidth : true"
                v-model:value="item.value"
                @change="(e) => handleSelectChange(item, e, null)"
                :style="{
                  width: item.right_width
                    ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                    : '300px',
                }"
              />
              <div
                v-if="item.desc"
                class="common_item_right_desc"
                :style="{
                  width: item.desc_width
                    ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                    : '300px',
                }"
                >{{ item.desc }}</div
              >
            </div>
          </div>
          <div v-else-if="item.type == 'datepicker'" class="common_item" :style="{ width: width }">
            <!-- 日期选择器 -->
            <div
              class="common_item_left"
              :style="{
                width: item.width ? item.width + (typeof item.width == 'number' ? 'px' : '') : '',
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
                lineHeight: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
                paddingRight: item.paddingRight
                  ? item.paddingRight + (typeof item.paddingRight == 'number' ? 'px' : '')
                  : '',
              }"
              ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
            >
            <div
              class="common_item_right"
              :style="{
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
              }"
            >
              <DatePicker
                :placeholder="item.placeholder ? item.placeholder : '请选择'"
                :allowClear="item.allowClear ? item.allowClear : false"
                :disabled="item.disabled ? item.disabled : false"
                :format="item.format ? item.format : 'YYYY-MM-DD'"
                :showTime="item.showTime ? item.showTime : false"
                :showToday="item.showToday ? item.showToday : false"
                v-model:value="item.value"
                @change="(date, dateString) => handleSelectChange(item, dateString, date)"
                :style="{
                  width: item.right_width
                    ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                    : '300px',
                }"
              />
              <div
                v-if="item.desc"
                class="common_item_right_desc"
                :style="{
                  width: item.desc_width
                    ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                    : '300px',
                }"
                >{{ item.desc }}</div
              >
            </div>
          </div>
          <div v-else-if="item.type == 'monthpicker'" class="common_item" :style="{ width: width }">
            <!-- 月份选择器 -->
            <div
              class="common_item_left"
              :style="{
                width: item.width ? item.width + (typeof item.width == 'number' ? 'px' : '') : '',
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
                lineHeight: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
                paddingRight: item.paddingRight
                  ? item.paddingRight + (typeof item.paddingRight == 'number' ? 'px' : '')
                  : '',
              }"
              ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
            >
            <div
              class="common_item_right"
              :style="{
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
              }"
            >
              <MonthPicker
                :placeholder="item.placeholder ? item.placeholder : '请选择'"
                :allowClear="item.allowClear ? item.allowClear : false"
                :disabled="item.disabled ? item.disabled : false"
                :format="item.format ? item.format : 'YYYY-MM'"
                v-model:value="item.value"
                @change="(date, dateString) => handleSelectChange(item, dateString, date)"
                :style="{
                  width: item.right_width
                    ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                    : '300px',
                }"
              />
              <div
                v-if="item.desc"
                class="common_item_right_desc"
                :style="{
                  width: item.desc_width
                    ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                    : '300px',
                }"
                >{{ item.desc }}</div
              >
            </div>
          </div>
          <div v-else-if="item.type == 'rangepicker'" class="common_item" :style="{ width: width }">
            <!-- 范围选择器 -->
            <div
              class="common_item_left"
              :style="{
                width: item.width ? item.width + (typeof item.width == 'number' ? 'px' : '') : '',
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
                lineHeight: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
                paddingRight: item.paddingRight
                  ? item.paddingRight + (typeof item.paddingRight == 'number' ? 'px' : '')
                  : '',
              }"
              ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
            >
            <div
              class="common_item_right"
              :style="{
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
              }"
            >
              <RangePicker
                :allowClear="item.allowClear ? item.allowClear : false"
                :disabled="item.disabled ? item.disabled : false"
                :format="item.format ? item.format : 'YYYY-MM-DD HH:mm:ss'"
                :showTime="item.showTime ? item.showTime : false"
                v-model:value="item.value"
                @change="(date, dateString) => handleSelectChange(item, dateString, date)"
                :style="{
                  width: item.right_width
                    ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                    : '300px',
                }"
              />
              <div
                v-if="item.desc"
                class="common_item_right_desc"
                :style="{
                  width: item.desc_width
                    ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                    : '300px',
                }"
                >{{ item.desc }}</div
              >
            </div>
          </div>
          <div v-else-if="item.type == 'treeselect'" class="common_item" :style="{ width: width }">
            <!-- 树型选择器 -->
            <div
              class="common_item_left"
              :style="{
                width: item.width ? item.width + (typeof item.width == 'number' ? 'px' : '') : '',
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
                lineHeight: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
                paddingRight: item.paddingRight
                  ? item.paddingRight + (typeof item.paddingRight == 'number' ? 'px' : '')
                  : '',
              }"
              ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
            >
            <div
              class="common_item_right"
              :style="{
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
              }"
            >
              <TreeSelect
                :placeholder="item.placeholder ? item.placeholder : '请选择'"
                :treeData="item.data ? item.data : []"
                :allowClear="item.allowClear ? item.allowClear : false"
                :disabled="item.disabled ? item.disabled : false"
                :multiple="item.multiple ? item.multiple : false"
                :maxTagCount="item.maxTagCount ? item.maxTagCount : 255"
                :maxTagPlaceholder="item.maxTagPlaceholder ? item.maxTagPlaceholder : 255"
                :showSearch="item.showSearch ? item.showSearch : false"
                :treeDefaultExpandAll="
                  item.treeDefaultExpandAll ? item.treeDefaultExpandAll : false
                "
                :treeCheckable="item.treeCheckable ? item.treeCheckable : false"
                v-model:value="item.value"
                @change="(e) => handleSelectChange(item, e, null)"
                :style="{
                  width: item.right_width
                    ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                    : '300px',
                }"
              />
              <div
                v-if="item.desc"
                class="common_item_right_desc"
                :style="{
                  width: item.desc_width
                    ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                    : '300px',
                }"
                >{{ item.desc }}</div
              >
            </div>
          </div>
          <div
            v-else-if="item.type == 'upload_img'"
            class="common_item common_item_upload"
            :style="{ width: width }"
          >
            <!-- 上传图片 -->
            <div
              class="common_item_left"
              :style="{
                width: item.width ? item.width + (typeof item.width == 'number' ? 'px' : '') : '',
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '150px',
                lineHeight: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '150px',
                paddingRight: item.paddingRight
                  ? item.paddingRight + (typeof item.paddingRight == 'number' ? 'px' : '')
                  : '',
              }"
              ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
            >
            <div
              class="common_item_right"
              :style="{
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '150px',
              }"
            >
              <Upload
                @click="beforeUploadClick"
                :accept="item.accept ? item.accept : '.jpg, .jpeg, .png, .gif'"
                :action="item.action"
                :method="item.method ? item.method : 'post'"
                :beforeUpload="
                  (file, fileList) =>
                    beforeUpload(
                      file,
                      fileList,
                      item.accept ? item.accept : '.jpg, .jpeg, .png, .gif',
                    )
                "
                :data="item.data ? item.data : {}"
                :disabled="item.disabled ? item.disabled : false"
                listType="picture-card"
                :fileList="item.fileList ? item.fileList : []"
                :multiple="item.multiple ? item.multiple : false"
                @change="(file, fileList) => handleSelectChange(item, file, fileList)"
                @preview="(file) => handlePreview(item, file)"
                :style="{
                  width: item.right_width
                    ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                    : '300px',
                }"
                :headers="{
                  Authorization: imgToken,
                }"
              >
                <div
                  v-if="
                    item.limit != undefined
                      ? item.fileList.length < item.limit
                      : !item.fileList || item.fileList.length == 0
                  "
                  class="upload_image"
                >
                  <PlusOutlined v-if="true" />
                  <LoadingOutlined v-else />
                  <span>上传图片</span>
                </div>
              </Upload>
              <div
                v-if="item.desc"
                class="common_item_right_desc"
                :style="{
                  width: item.desc_width
                    ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                    : '300px',
                }"
                >{{ item.desc }}</div
              >
            </div>
          </div>
          <div
            v-else-if="item.type == 'upload_file'"
            class="common_item common_item_upload"
            :style="{ width: width }"
          >
            <!-- 上传文件 -->
            <div
              class="common_item_left"
              :style="{
                width: item.width ? item.width + (typeof item.width == 'number' ? 'px' : '') : '',
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
                lineHeight: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
                paddingRight: item.paddingRight
                  ? item.paddingRight + (typeof item.paddingRight == 'number' ? 'px' : '')
                  : '',
              }"
              ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
            >
            <div
              class="common_item_right"
              :style="{
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
              }"
            >
              <Upload
                :accept="item.accept ? item.accept : '.doc, .docx, .xls, .xlsx, .pdf'"
                :action="item.action"
                @click="beforeUploadClick"
                :method="item.method ? item.method : 'post'"
                :beforeUpload="
                  (file, fileList) =>
                    beforeUpload(
                      file,
                      fileList,
                      item.accept ? item.accept : '.doc, .docx, .xls, .xlsx, .pdf',
                    )
                "
                :data="item.data ? item.data : {}"
                :disabled="item.disabled ? item.disabled : false"
                :fileList="item.fileList ? item.fileList : []"
                listType="picture"
                :multiple="item.multiple ? item.multiple : false"
                @change="(file, fileList) => handleSelectChange(item, file, fileList)"
                :style="{
                  width: item.right_width
                    ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                    : '300px',
                }"
                :headers="{
                  Authorization: imgToken,
                }"
              >
                <div class="upload_file">点击上传 &gt;&gt;</div>
              </Upload>
              <div
                v-if="item.desc"
                class="common_item_right_desc"
                :style="{
                  width: item.desc_width
                    ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                    : '300px',
                }"
                >{{ item.desc }}</div
              >
            </div>
          </div>
          <div v-else-if="item.type == 'button'" class="common_item" :style="{ width: width }">
            <!-- 按钮 -->
            <div
              class="common_item_left"
              :style="{
                width: item.width ? item.width + (typeof item.width == 'number' ? 'px' : '') : '',
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '54px',
                lineHeight: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '54px',
                paddingRight: item.paddingRight
                  ? item.paddingRight + (typeof item.paddingRight == 'number' ? 'px' : '')
                  : '',
              }"
            ></div>
            <div
              class="common_item_right common_btn"
              :style="{
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '54px',
              }"
            >
              <Popconfirm
                :title="item.tip.title"
                :ok-text="item.tip.okText ? item.tip.okText : '确定'"
                :cancel-text="item.tip.cancelText ? item.tip.cancelText : '取消'"
                @confirm="handleSubmitEvent"
                v-if="(item.showSubmit === undefined || item.showSubmit === true) && tipFlag"
              >
                <Button type="primary">{{ item.submitText ? item.submitText : '保存' }}</Button>
              </Popconfirm>
              <Button
                v-if="(item.showSubmit === undefined || item.showSubmit === true) && !tipFlag"
                type="primary"
                @click="handleSubmitEvent"
                >{{ item.submitText ? item.submitText : '保存' }}</Button
              >
              <Button
                v-if="item.showCancle === undefined || item.showCancle === true"
                @click="handleCancleEvent"
                >{{ item.cancleText ? item.cancleText : '取消' }}</Button
              >
              <template v-if="item.btnList && item.btnList.length > 0">
                <Button
                  v-for="(items, indexs) in item.btnList"
                  :key="indexs"
                  @click="btnClickEvent(items)"
                  >{{ items.btnText ? items.btnText : '按钮' }}</Button
                >
              </template>
            </div>
          </div>
          <div v-else-if="item.type == 'check_select_box'" class="common_item" :style="{ width: width }">
            <!-- 多选、下拉组合框 -->
            <div
              class="common_item_left"
              :style="{
                width: item.width ? item.width + (typeof item.width == 'number' ? 'px' : '') : '',
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
                lineHeight: item.lineHeight
                  ? item.lineHeight + (typeof item.lineHeight == 'number' ? 'px' : '')
                  : item.height ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
                paddingRight: item.paddingRight
                  ? item.paddingRight + (typeof item.paddingRight == 'number' ? 'px' : '')
                  : '',
              }"
              ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
            >
            <div
              class="common_item_right"
              :style="{
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
              }"
            >
              <CheckboxGroup
                :disabled="item.disabled ? item.disabled : false"
                v-model:value="curFormData[item.key]"
                @change="(e) => handleSelectChange(item, e, null)"
                :style="{
                  width: item.right_width
                    ? item.right_width + (typeof item.right_width == 'number' ? 'px' : '')
                    : '',
                }"
              >
                <template v-if="item.data && item.data.length > 0">
                  <Checkbox
                    v-for="(items, indexs) in item.data"
                    :key="indexs"
                    :value="items.value"
                    :style="{
                      width: item.checkbox_width[indexs]
                        ? item.checkbox_width[indexs] + (typeof item.checkbox_width[indexs] == 'number' ? 'px' : '')
                        : '',
                      margin: '2px 0',
                      padding: 0,
                    }"
                    >{{ items.title }}</Checkbox
                  >
                  <template v-if="item.value && item.value.length > 1">
                    <span style=" margin-right: 10px;margin-left: 20px;">默认优先展示：</span>
                    <Select
                      size="small"
                      placeholder="请选择"
                      :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                      v-model:value="item.selectValue"
                      @change="(e) => handleSelectChange(item, e, 'select')"
                      :style="{ width: '124px' }"
                    >
                      <template v-if="item.data && item.data.length > 0">
                        <template v-for="(items, indexs) in item.data" :key="indexs">
                          <SelectOption v-if="items.useable" :value="items.value">{{ items.title }}</SelectOption>
                        </template>
                      </template>
                    </Select>
                  </template>
                </template>
              </CheckboxGroup>
              <div
                v-if="item.desc"
                class="common_item_right_desc"
                :style="{
                  width: item.desc_width
                    ? item.desc_width + (typeof item.desc_width == 'number' ? 'px' : '')
                    : '300px',
                }"
                >{{ item.desc }}</div
              >
            </div>
          </div>
          <div v-else-if="item.type == 'input_list'" class="common_item" :style="{ width: width }">
            <!-- 多输入框组件 -->
            <div
              class="common_item_left"
              :style="{
                width: item.width ? item.width + (typeof item.width == 'number' ? 'px' : '') : '',
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
                lineHeight: item.lineHeight
                  ? item.lineHeight + (typeof item.lineHeight == 'number' ? 'px' : '')
                  : item.height ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
                paddingRight: item.paddingRight
                  ? item.paddingRight + (typeof item.paddingRight == 'number' ? 'px' : '')
                  : '',
              }"
              ><span v-if="item.require" class="common_item_require">*</span>{{ item.label }}</div
            >
            <div
              class="common_item_right"
              :style="{
                height: item.height
                  ? item.height + (typeof item.height == 'number' ? 'px' : '')
                  : '',
              }"
            >
              <div
                v-for="(items,indexs) in item.list"
                :key="indexs"
                class="flex_row_start_center"
                :style="{
                  marginBottom: (indexs < item.list.length-1) ? '10px' : 0,
                }"
              >
                <div
                  :style="{
                    width: item.right_title_width
                      ? item.right_title_width + (typeof item.right_title_width == 'number' ? 'px' : '')
                      : '',
                  }"
                >{{ items.label }}</div>
                <a-input
                  :type="items.inputType ? items.inputType : 'text'"
                  :placeholder="items.placeholder ? items.placeholder : '请输入'"
                  :maxlength="items.maxlength ? items.maxlength : undefined"
                  :showCount="items.showCount ? items.showCount : false"
                  :allowClear="items.allowClear ? items.allowClear : false"
                  :disabled="items.disabled ? items.disabled : false"
                  v-model:value="items.value"
                  @change="(e) => handleSelectChange(item, e.target.value, indexs)"
                  :style="{
                    width: items.right_width
                      ? items.right_width + (typeof items.right_width == 'number' ? 'px' : '')
                      : '300px',
                  }"
                />
              </div>
            </div>
          </div>
        </FormItem>
      </template>
    </Form>
  </div>
</template>
<script setup>
  import { ref, computed, watch } from 'vue';
  import {
    Form,
    FormItem,
    InputNumber,
    Select,
    SelectOption,
    Radio,
    RadioGroup,
    RadioButton,
    Switch,
    CheckboxGroup,
    Checkbox,
    Cascader,
    DatePicker,
    MonthPicker,
    RangePicker,
    TreeSelect,
    Upload,
    Button,
    Popconfirm,
  } from 'ant-design-vue';
  import { PlusOutlined, LoadingOutlined } from '@ant-design/icons-vue';
  import { getToken } from '/@/utils/auth';
  import { failTip } from '@/utils/utils';
  import { useUserStore } from '@/store/modules/user';

  const emit = defineEmits(['submitEvent', 'cancleEvent', 'btnClickEvent', 'callbackEvent', 'errEvent']);
  const tableForm = ref({});
  const curFormData = ref({});
  const imgToken = 'Bearer ' + getToken() || ''; //上传文件token参数

  const props = defineProps({
    width: { type: String, required: false, default: '1000px' }, //表单宽度
    hideTopBorder: { type: Boolean, require: false, default: false }, //是否隐藏表单顶部边框线
    tipFlag: { type: Boolean, require: false, default: false }, //按钮确定是否二次确认
    data: { type: Array, required: true, default: () => [] }, //表单数据
  });

  const formData = computed(() => {
    return props.data;
  });

  const userStore = useUserStore();

  watch(
    formData,
    () => {
      props.data.map((item) => {
        if (item.key) {
          curFormData.value[item.key] =
            item.type == 'upload_img'
              ? item.fileList && item.fileList.length > 0
                ? item.fileList[0]
                : {}
              : item.value;
        }
      });
    },
    { deep: true },
  );

  const handleSelectChange = (item, val1, val2) => {
    if (item.callback) {
      emit('callbackEvent', {
        contentItem: { ...item, eventType: 'change' },
        val1: val1,
        val2: val2,
      });
    }
  };

  const handlePreview = (item, file) => {
    if (file.response && file.response.data && file.response.data.url) {
      window.open(file.response.data.url);
    } else {
      window.open(file.url);
    }
  };

  const beforeUploadClick = async()=> {
    imgToken.value = 'Bearer ' + userStore.getToken
    if(userStore.getToken){
      let res = await userStore.getSldCommonService()
      if(res.state == 200){
        imgToken.value = 'Bearer ' + res.token
      }
    }
  }
  
  //文件上传前数据校验
  const beforeUpload = (file, fileList, accept, limit = 20) => {
    //校验文件格式类型
    if (accept != undefined && accept != null && accept) {
      let accept_list = accept
        .replaceAll(' ', '')
        .split(',')
        .filter((item) => item && item.trim());
      let file_type = file.name ? '.' + file.name.split('.').at(-1) : '';
      if (accept_list.length > 0 && file_type != '' && accept_list.indexOf(file_type) == -1) {
        failTip('上传文件格式有误');
        return false;
      }
    }
    //校验文件大小
    if (file.size != undefined && file.size > 1024 * 1024 * limit) {
      failTip(`上传文件过大，请上传小于${limit}M的图片`);
      return false;
    }
  };

  // 让父组件调用方法
  const standardSubmit = ()=> {
    handleSubmitEvent()
  }

   // 暴露方法
   defineExpose({
		standardSubmit
	})


  //保存事件
  function handleSubmitEvent() {
    tableForm.value
      .validate()
      .then((res) => {
        let data = Object.fromEntries(
          Object.entries(res).filter(([key]) => !key.includes('table_row_')),
        );
        emit('submitEvent', data);
      })
      .catch((err) => {
        emit('errEvent', err);
      });
  }

  //取消事件
  function handleCancleEvent() {
    emit('cancleEvent');
  }

  //按钮点击事件
  function btnClickEvent(item) {
    if (item.callback) {
      emit('btnClickEvent', item.type ? item.type : '');
    }
  }
</script>
<style lang="less">
  .standard_table_row {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    border-top: 1px solid #f0f0f0;
    background: #fff;

    .ant-form-item-control {
      .ant-form-item-explain {
        position: absolute;
        z-index: 2;
        top: -10px;
        right: 42%;
        min-width: 150px;
        min-height: 20px;
        font-size: 13px;
        text-align: left;
      }
    }

    .ant-form {
      width: 100%;

      .ant-form-item {
        margin-bottom: 0;
      }
    }

    .ant-upload-picture-card-wrapper {
      .ant-upload-select-picture-card {
        position: relative;

        &:hover {
          .upload_image_hover {
            opacity: 1;
          }
        }
      }
    }

    .common_title {
      width: 1000px;
      height: 45px;
      border: 1px solid #f0f0f0;
      border-width: 0 1px 1px;
      background: #fffaf7;
      line-height: 45px;

      .common_title_label {
        padding: 0 20px;
        color: #333;
        font-size: 13px;
      }
    }

    .common_item {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      width: 1000px;
      transition: all 0.3s ease;
      border: 1px solid #f0f0f0;
      border-top: none;
      font-size: 13px;

      &:hover {
        background-color: #fcf9f9;
      }

      .common_item_left {
        flex-shrink: 0;
        width: 200px;
        padding: 10px 0;
        padding-right: 20px;
        text-align: right;

        .common_item_require {
          color: red;
        }
      }

      .common_item_right {
        display: flex;
        flex: 1;
        flex-direction: column;
        align-items: flex-start;
        justify-content: center;
        min-height: 50px;
        padding: 10px 0;
        padding-right: 20px;
        padding-left: 20px;
        border-left: 1px solid #f0f0f0;

        .common_item_require {
          color: red;
        }

        .common_item_right_input {
          display: flex;
          align-items: center;
        }

        .common_item_right_desc {
          margin-top: 8px;
          color: rgb(0 0 0 / 45%);
          font-size: 12px;
          line-height: 14px;
          word-break: break-all;
        }

        .upload_image {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 90px;
          height: 90px;

          span {
            margin-top: 8px;
            color: rgb(102 102 102);
            font-size: 13px;
          }
        }

        .upload_file {
          color: @primary-color;
          font-size: 13px;
          cursor: pointer;
        }

        &.common_btn {
          flex-direction: row;
          align-items: center;
          justify-content: flex-start;

          button {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 32px;
            margin-right: 12px;
            padding: 6px 16px;
            border-radius: 4px;
            box-shadow: 0 2px 0 rgb(0 0 0 / 5%);
            font-size: 13px;
            font-weight: 400;
            text-shadow: 0 -1px 0 rgb(0 0 0 / 12%);
            white-space: nowrap;
            cursor: pointer;
          }
        }

        .ant-upload-list-item-actions {
          display: flex;
          align-items: center;
          justify-content: center;

          a {
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }

        .ant-input-affix-wrapper{
          border: 1px solid #f0f0f0;
          border-radius: 3px;
        }
      }

      &.common_item_upload {
        .common_item_right_desc {
          margin-top: 0;
        }
      }
    }

    .common_line {
      width: 100%;
      height: 10px;
    }
  }
</style>
