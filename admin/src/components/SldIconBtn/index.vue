<template>
  <div
    :style="{
      marginLeft: wrapML + 'px',
      marginRight: wrapMR + 'px',
      cursor: cursor != undefined ? cursor : '',
    }"
    class="sld_common_btn"
    :title="title"
  >
    <AliSvgIcon :fillColor="svgColor" :width="svgW + 'px'" :height="svgH + 'px'" :iconName="svg" />
    <span :style="{ marginLeft: textML + 'px', fontSize: '13px', color: '#333' }">{{ text }}</span>
  </div>
</template>

<script>
  import { defineComponent } from 'vue';
  import { AliSvgIcon } from '@/components/SvgIcon';
  export default defineComponent({
    props: {
      wrapML: Number,
      wrapMR: Number,
      textML: {
        type: Number,
        default: 4,
      },
      text: String,
      svgColor: String,
      svgW: {
        type: Number,
        default: 15,
      },
      svgH: {
        type: Number,
        default: 15,
      },
      svg: String,
    },
  });
</script>

<style lang="less" scoped>
  .sld_common_btn {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    height: 28px;
    padding-right: 7px;
    padding-left: 7px;
    border-radius: 3px;
    background: #fff;
    cursor: pointer;
  }
</style>
