<template>
  <div class="common_header_tip">
    <div class="common_header_tip_list">
      <ul>
        <template v-for="item in props.tipData">
          <li v-if="item">• {{ item }}</li>
        </template>
      </ul>
    </div>
  </div>
</template>

<script setup>
  const props = defineProps({
    tipData: Object,
  });
</script>

<style lang="less" scoped>
  .common_header_tip {
    width: 100%;
    margin-top: 10px;
    padding: 10px 15px;
    border-radius: 4px;
    background: fade(@primary-color, 7);

    .common_header_tip_title {
      color: #db5609;
      font-size: 16px;
    }

    .common_header_tip_list {
      ul {
        margin: 0;
        padding: 0;

        li {
          padding: 2px 0;
          color: #db5609;
          font-size: 12px;
        }
      }
    }
  }
</style>
