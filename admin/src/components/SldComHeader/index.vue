<!-- 公共头部组件 -->
<template>
  <div class="common_header">
    <div
      class="common_header_main"
      :class="
        props.type == 2 && (props.tipTitle || props.tipData.length > 0) && showTipData
          ? ''
          : 'common_bottom'
      "
    >
      <div class="common_header_main_title">
        <div v-if="line" class="common_header_line"></div>
        <div class="common_header_title">{{ props.title }}</div>
        <div
          v-if="
            (props.type == 2 && (props.tipTitle || props.tipData.length > 0) && showTipBtnData) ||
            props.type == 3
          "
          class="common_header_icon"
          @click="showTips"
        >
          <AliSvgIcon iconName="iconziyuan18" width="16px" height="16px" fillColor="#ff9864" />
        </div>
      </div>
      <div style="display: flex">
        <div class="common_sld_common_btn" v-if="tipBtn" @click="btnClick">
          <AliSvgIcon
            :iconName="tipBtnIcon"
            :width="tipBtnWidth + 'px'"
            :height="tipBtnHight + 'px'"
            :fillColor="tipBtnColor"
          />
          <span class="sld_common_btn_text">{{ tipBtn }}</span>
        </div>
        <div class="common_sld_common_btn" v-if="back" style="margin-left: 10px" @click="goBack">
          <AliSvgIcon iconName="iconfanhui" width="15px" height="15px" fillColor="#fff" />
          <span class="sld_common_btn_text">返回上级页面</span>
        </div>
      </div>
    </div>
    <div
      v-if="
        (props.type == 2 || props.type == 3) &&
        (props.tipTitle || props.tipData.length > 0) &&
        show_tip
      "
      class="common_header_tip"
    >
      <div v-if="props.tipTitle" class="common_header_tip_title">{{ props.tipTitle }}</div>
      <div v-if="props.tipData.length > 0" class="common_header_tip_list">
        <ul>
          <template v-for="(item, index) in props.tipData">
            <li v-if="item" :key="index">• {{ item }}</li>
          </template>
        </ul>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { computed, watch,ref } from 'vue';
  import { useRouter,useRoute } from 'vue-router';
  import { pageClose } from '/@/utils/utils';
  import { useMultipleTabStore } from '/@/store/modules/multipleTab';

  const props = defineProps({
    type: { type: Number, required: false, default: 1 }, //类型
    line: { type: Boolean, required: false, default: true }, //标题左侧竖线
    title: { type: String, required: true, default: '' }, //主标题
    tipBtn: { type: String, required: false, default: '' }, //自定义按钮内容
    tipBtnIcon: { type: String, required: false, default: '' }, //自定义按钮图标
    tipBtnWidth: { type: Number, required: false, default: 15 }, //自定义按钮图标宽
    tipBtnHight: { type: Number, required: false, default: 15 }, //自定义按钮图标高
    tipBtnColor: { type: String, required: false, default: '#fff' }, //自定义按钮图标颜色
    showTip: { type: Boolean, required: false, default: true }, //提示内容是否展示
    showTipBtn: { type: Boolean, required: false, default: true }, //提示信息开关是否显示
    back: { type: Boolean, required: false, default: false }, //是否显示返回上一页
    tipTitle: { type: String, required: false, default: '' }, //提示内容标题
    tipData: { type: Array, required: false, default: () => [] }, //提示内容信息
    clickFlag: { type: Boolean, required: false, default: false }, //是否需要点击事件
  });

  const showTipData: any = computed(() => {
    //提示信息是否显示
    return props.showTip;
  });

  const showTipBtnData: any = computed(() => {
    //提示信息开关是否显示
    return props.showTipBtn;
  });

  const show_tip = ref(true)

  const tabStore = useMultipleTabStore();

  watch(
    showTipData,
    () => {
      showTipData.value = props.showTip;
      show_tip.value = props.showTip;
    },
    { deep: true },
  );

  watch(
    showTipBtnData,
    () => {
      showTipBtnData.value = props.showTipBtn;
    },
    { deep: true },
  );

  const router = useRouter();
  const route = useRoute();

  const emit = defineEmits(['handleToggleTip', 'tipBtnClick']);

  //切换提示信息显示/隐藏
  function showTips() {
    show_tip.value = !show_tip.value;
    if (props.clickFlag) {
      emit('handleToggleTip', show_tip.value);
    }
  }

  const btnClick = () => {
    emit('tipBtnClick');
  };

  const goBack = () => {
    if (window.history && window.history.length == 1) {
      pageClose();
    } else {
      const { fullPath } = route;
      tabStore.closeTabByKey(fullPath, router);
      router.back();
    }
  };
</script>
<style lang="less" scoped>
  .common_header {
    background-color: #fff;
    margin-bottom: 13px !important;

    .common_bottom {
    }

    .common_header_main {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .common_header_main_title {
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }

      .common_header_line {
        display: inline-block;
        width: 4px;
        height: 16px;
        margin-right: 8px;
        background-color: rgb(250 111 30);
      }

      .common_header_title {
        color: #101010;
        font-size: 14px;
        font-weight: 700;
      }

      .common_header_icon {
        position: relative;
        top: 3px;
        margin-left: 8px;
        cursor: pointer;
      }

      .common_sld_common_btn {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        height: 28px;
        padding-right: 7px;
        padding-left: 7px;
        border-radius: 3px;
        background-color: @primary-color;
        cursor: pointer;

        .sld_common_btn_text {
          display: inline-block;
          margin-left: 5px;
          color: rgb(255 255 255);
          font-size: 13px;
        }
      }
    }

    .common_header_tip {
      width: 100%;
      margin-top: 10px;
      padding: 10px 15px;
      border-radius: 4px;
      background: fade(@primary-color, 7);

      .common_header_tip_title {
        color: #db5609;
        font-size: 16px;
      }

      .common_header_tip_list {
        ul {
          margin: 0;
          padding: 0;

          li {
            padding: 2px 0;
            color: #db5609;
            font-size: 12px;
          }
        }
      }
    }
  }
</style>
