<!-- 魔方编辑组件 -->
<template>
  <div class="list_cube_area">
    <div class="sub_part">
      <div class="subtitle">上边距</div>
      <Slider
        :min="0"
        :max="200"
        :defaultValue="data.top_margin"
        @change="(e) => onChange(e, 'top_margin')"
      />
    </div>
    <div class="sub_part">
      <div class="subtitle">下边距</div>
      <Slider
        :min="0"
        :max="200"
        :defaultValue="data.bottom_margin"
        @change="(e) => onChange(e, 'bottom_margin')"
      />
    </div>
    <div class="sub_part">
      <div class="subtitle">背景色</div>
      <div class="flex_row_start_center fzx_color_show">
        <ColorPicker
          :color="data.color"
          @update:color="
            (e) => onChange('rgba(' + e.r + ',' + e.g + ',' + e.b + ',' + e.a + ')', 'color')
          "
          :hideReset="true"
        ></ColorPicker>
        <a style="margin-left: 10px" href="javascript:void(0)" @click="onChange('#fff', 'color')"
          >重置</a
        >
      </div>
    </div>
    <div class="flex_row_start_center mb-[20px]">
      <span class="cube_d_text">魔方密度：</span>
      <Select
        :options="cubeDensityOptions"
        @change="cubeDensityChange"
        :defaultValue="cubeSize"
        class="w-[100px]"
      ></Select>
    </div>
    <Controller ref="cubeInstance" :cube-size="cubeSize" :list="props.data.data"></Controller>
    <div class="mt-[20px] relative">
      <div class="bg-[#f7f8fa] p-[10px] flex_row_start_start">
        <div class="img_wrapper flex_column_center_center" @click="openMaterial">
          <img :src="crtCube.img.imgUrl" alt="" v-if="crtCube.img && crtCube.img.imgUrl" />
          <div class="img_placeholder flex_column_center_center" v-else>
            <SvgIcon iconName="iconjiahao" width="18px" height="18px" fillColor="#333"></SvgIcon>
          </div>
        </div>
        <div class="ml-[10px] h-full">
          <Select
            :options="m_diy_link_options"
            placeholder="选择操作"
            v-model:value="m_diy_value"
            class="w-[150px]"
            @change="selectChange"
          ></Select>
          <div class="mt-[12px]" v-if="m_diy_value == ''">无操作</div>
          <div class="mt-[12px] flex items-center" v-else-if="m_diy_value == 'url'">
            <Input
              placeholder="请输入链接地址"
              v-model:value="crtCube.content.link_value"
              @blur="setInputValue"
            ></Input>
          </div>
          <div class="mt-[12px] flex items-center" v-else-if="m_diy_value == 'keyword'">
            <Input
              placeholder="请输入关键字"
              v-model:value="crtCube.content.link_value"
              @blur="setInputValue"
            ></Input>
          </div>
          <div class="mt-[12px] text_name" v-else>
            {{ crtCube.content.link_value }}
          </div>
        </div>
      </div>
      <div class="disabled" v-show="!crtCube.uid"></div>
    </div>

    <SldMaterialImgs
      ref="sldMaterialImgs"
      @confirmMaterial="selectedMaterialImg"
      :max-upload-num="1"
    />
    <SldSelGoodsSingleDiy
      :modalVisible="diyVisible"
      v-bind="modalProps"
      @cancleEvent="diyVisible = false"
      @confirmEvent="selectDiyConfirm"
    />
  </div>
</template>

<script setup>
  import SldSelGoodsSingleDiy from '@/components/SldSelGoodsSingleDiy/index.vue';
  import SldMaterialImgs from '/@/components/SldMaterialFiles/sldMaterialImgs.vue';
  import { setSldSelProps, handleDiyGoodsConfirm } from '/@/components/SldSelGoodsSingleDiy';
  import { computed, provide, reactive, ref, unref } from 'vue';
  import ColorPicker from '/@/components/ColorPicker/index.vue';
  import Controller from './Controller.vue';
  import { Select, message, Input, Slider } from 'ant-design-vue';
  import {
    new_m_diy_link_type,
    new_m_point_diy_link_type,
    spreader_diy_spreader_link_type,
  } from '/@/utils/utils';
  import SvgIcon from '@/components/SvgIcon/index.vue';
  import { resolveImageBindId } from '/@/utils';

  const props = defineProps({
    data: {
      type: Object,
      default: () => {},
    },
    mode: {
      type: String,
      default: 'default',
    },
  });

  const emit = defineEmits(['handleCurSelData']);

  const cubeDensityOptions = [
    { label: '4x4', value: 4 },
    { label: '5x5', value: 5 },
    { label: '6x6', value: 6 },
    { label: '7x7', value: 7 },
  ];

  const cubeSize = ref(props.data.size ?? 4);

  const modalProps = ref({});

  const diyVisible = ref(false);

  const m_diy_link = computed(() => {
    switch (props.mode) {
      case 'integral':
        return new_m_point_diy_link_type;
      case 'spreader':
        return spreader_diy_spreader_link_type;
      default:
        return new_m_diy_link_type;
    }
  });

  const m_diy_link_options = computed(() => m_diy_link.value.formatOptions());

  const cubeInstance = ref();

  const m_diy_value = ref('');

  const crtCube = reactive({
    content: {},
    img: {},
    areaInfo: {},
  });

  const sldMaterialImgs = ref();

  /*
   * 输入框内容更改事件
   * val 组件传回来的值
   * type 修改值对应的键名
   * */
  function onChange(val, type) {
    props.data[type] = val;
    emit('handleCurSelData', props.data);
  }

  const cubeDensityChange = (e) => {
    cubeSize.value = e;
    m_diy_value.value = '';
    Object.keys(crtCube).map((key) => {
      delete crtCube[key];
    });
    emit('handleCurSelData', { data: [], size: e });
  };

  const selectChange = (val) => {
    crtCube.content.link_value = '';
    let content = {
      link_type: val,
      link_value: '',
      info: {},
    };
    cubeInstance.value.setMaskInfo({ content, uid: crtCube.uid });

    if (unref(m_diy_link)[val].require_select_modal) {
      modalProps.value = setSldSelProps(val, props.mode, 'mobile');
      diyVisible.value = true;
    }
  };

  const getCurrentCube = (target) => {
    m_diy_value.value = target.content?.link_type;
    Object.keys(target).forEach((key) => {
      crtCube[key] = target[key];
    });
  };

  const setInputValue = () => {
    if (unref(m_diy_value) == 'keyword' || unref(m_diy_value) == 'url') {
      if (crtCube.content.link_value) {
        crtCube.content.link_type = unref(m_diy_value);
        cubeInstance.value.setMaskInfo({ content: crtCube.content, uid: crtCube.uid });
      }
    }
  };

  const emitMaskList = (list) => {
    emit('handleCurSelData', { data: list });
  };

  provide('editorProvider', { getCurrentCube, emitMaskList });

  const openMaterial = () => {
    if (!crtCube.uid) {
      message.warn('请选择魔方块');
      return;
    }
    let selectedData = { data: [], ids: [] };
    if (crtCube.img.imgPath) {
      let fileData = {
        bindId: resolveImageBindId(crtCube.img.imgPath),
        width: crtCube.img.width,
        height: crtCube.img.height,
        fileUrl: crtCube.img.imgUrl,
        filePath: crtCube.img.imgPath,
      };

      selectedData = {
        data: [fileData],
        ids: [fileData.bindId],
      };
      sldMaterialImgs.value.setMaterialModal(true, selectedData);
    } else {
      sldMaterialImgs.value.setMaterialModal(true, selectedData);
    }
  };

  const selectedMaterialImg = ({ data }) => {
    let [img] = data;
    crtCube.img = {
      imgUrl: img.fileUrl,
      imgPath: img.filePath,
      width: img.width,
      height: img.height,
    };
    cubeInstance.value.setMaskInfo({ img: crtCube.img, uid: crtCube.uid });
  };

  const selectDiyConfirm = (e) => {
    let item = handleDiyGoodsConfirm(unref(m_diy_value), e, 'mobile', props.mode);
    diyVisible.value = false;
    item.link_type = unref(m_diy_value);
    crtCube.content = item;
    cubeInstance.value.setMaskInfo({ content: crtCube.content, uid: crtCube.uid });
  };
</script>

<style lang="less" scoped>
  .img_wrapper {
    width: 80px;
    min-width: 80px;
    height: 80px;
    border: 1px solid #e5e5e5;
    background-color: #fff;
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }

    &.grey {
      background-color: #f2f4f6;
    }

    &:hover {
      border: 1px dashed @primary-color;
    }

    .img_placeholder {
      color: @primary-color;
      font-size: 30px;
      font-weight: bold;
    }
  }

  .list_cube_area {
    padding: 20px;

    .sub_part {
      margin-bottom: 18px;
      padding-bottom: 18px;
      border-bottom: 1px solid #eee;

      .subtitle {
        color: #343434;
        font-size: 14px;
        line-height: 40px;
      }
    }

    .cube_d_text {
      margin-right: 16px;
      color: #969799;
      font-size: 14px;
      line-height: 18px;
      white-space: nowrap;
    }
  }

  .text_name {
    font-size: 13px;
  }

  .disabled {
    position: absolute;
    z-index: 10;
    opacity: 0.5;
    inset: 0;
    background: #fff;
  }
</style>
