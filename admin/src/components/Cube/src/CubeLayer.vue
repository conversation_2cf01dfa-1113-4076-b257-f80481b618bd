<template>
  <div>
    <ul v-for="(ulItem, ulIndex) in cubeSize" :key="ulIndex" class="flex_row_start_center">
      <li
        class="custom-rubik-cube"
        :class="{
          selected: cubeVar[ulIndex][liIndex] == 2,
          selecting: cubeVar[ulIndex][liIndex] == 1,
        }"
        v-for="(liItem, liIndex) in cubeSize"
        :key="liIndex"
        :style="liStyle"
        @mousedown="toSelectCube(ulIndex, liIndex)"
        @mousemove="(e) => selectingCube(ulIndex, liIndex, e)"
        @mouseup="(e) => selectedCube(ulIndex, liIndex)"
      >
        <span v-show="cubeVar[ulIndex][liIndex] == 0">+</span>
      </li>
    </ul>
  </div>
</template>

<script setup>
  import { computed, onMounted, ref, watch } from 'vue';
  import lodash from 'lodash-es';

  const emit = defineEmits(['selected']);

  const props = defineProps({
    layoutSize: {
      type: Number,
      default: 350,
    },

    cubeSize: {
      type: Number,
      default: 4,
    },

    list: {
      type: Array,
      default: [],
    },
  });

  //填充魔方块 size--大小
  const formatFill = (size) => {
    let target = [];
    for (let i = 0; i < size; i++) {
      let arr = [];
      for (let j = 0; j < size; j++) {
        arr.push(0);
      }
      target.push(arr);
    }
    return target;
  };

  //魔方整体长度除以魔方块行数---->一个魔方块的宽高
  const liStyle = computed(() => ({
    width: props.layoutSize / props.cubeSize + 'px',
    height: props.layoutSize / props.cubeSize + 'px',
  }));

  /**
   * cubeVar：每个方块的值
   *  value: 0-未标记，1-正在标记，2-已标记
   */
  const cubeVar = ref(formatFill(props.cubeSize));

  /**
   * selectedPosition: 单次标记时的起始位置和结束位置
   *
   */
  const selectedPosition = {
    start: {},
    end: {},
    reset() {
      this.start = {};
      this.end = {};
    },
  };

  //格式化selectedPosition
  //比如起始位置可能比结束位置在坐标值上大，就要坐下转换
  const formatPosition = () => {
    let { start, end } = selectedPosition;

    let width = Math.abs(start.x - end.x) + 1;
    let height = Math.abs(start.y - end.y) + 1;

    let realstart = {
      y: Math.min(start.y, end.y),
      x: Math.min(start.x, end.x),
    };

    let realend = {
      y: Math.max(start.y, end.y),
      x: Math.max(start.x, end.x),
    };

    emit('selected', { start: realstart, width, height, end: realend });
  };

  //正在标记状态（1）的方块改成2
  const markSelected = () => {
    for (let i = 0; i < props.cubeSize; i++) {
      for (let j = 0; j < props.cubeSize; j++) {
        if (cubeVar.value[i][j] == 1) {
          cubeVar.value[i][j] = 2;
        }
      }
    }
  };

  //鼠标按下开始选择方块
  const toSelectCube = (ulIndex, liIndex, e) => {
    if (lodash.isEmpty(selectedPosition.start)) {
      selectedPosition.start = { x: liIndex, y: ulIndex };
      cubeVar.value[ulIndex][liIndex] = 1;
    }
  };
  //鼠标移动开始选择方块
  const selectingCube = (ulIndex, liIndex, e) => {
    if (lodash.isEmpty(selectedPosition.start)) {
      return;
    }
    let { start } = selectedPosition;
    let x_start = Math.min(start.x, liIndex);
    let x_end = Math.max(start.x, liIndex);
    let y_start = Math.min(start.y, ulIndex);
    let y_end = Math.max(start.y, ulIndex);
    for (let j = 0; j < props.cubeSize; j++) {
      for (let i = 0; i < props.cubeSize; i++) {
        if (i >= x_start && i <= x_end && j >= y_start && j <= y_end) {
          //在鼠标划过方向的方块如果已经标记了则不标记
          if (cubeVar.value[j][i] == 2) {
            return;
          }
          //实时记录结束时的结束坐标
          selectedPosition.end = { x: liIndex, y: ulIndex };
          cubeVar.value[j][i] = 1;
        } else if (cubeVar.value[j][i] == 1) {
          //如果滑动方向遇到正在标记的方块，则表明选择的区域正在收缩
          cubeVar.value[j][i] = 0;
        }
      }
    }
  };
  //鼠标松开，开始将正在标记状态(1)的方块标记为2
  const selectedCube = (ulIndex, liIndex) => {
    if (lodash.isEmpty(selectedPosition.end)) {
      selectedPosition.end = { x: liIndex, y: ulIndex };
    }
    formatPosition();
    markSelected();
    selectedPosition.reset();
  };
  //清空start,end围成范围中的方块状态为0
  const clearTargetArea = (start, end) => {
    let x_start = Math.min(start.x, end.x);
    let x_end = Math.max(start.x, end.x);
    let y_start = Math.min(start.y, end.y);
    let y_end = Math.max(start.y, end.y);
    for (let j = y_start; j <= y_end; j++) {
      for (let i = x_start; i <= x_end; i++) {
        cubeVar.value[j][i] = 0;
      }
    }
  };

  //此方法用于数据回显，批量将单个坐标组的范围标记为已标记（2）
  const batchSet = () => {
    let areaInfoMap = props.list.map((v) => v.areaInfo);
    for (let area of areaInfoMap) {
      let { start, end } = area;
      for (let j = start.y; j <= end.y; j++) {
        for (let i = start.x; i <= end.x; i++) {
          if (j !== undefined && j !== null && i !== undefined && i !== null) {
            cubeVar.value[j][i] = 2;
          }
        }
      }
    }
  };

  //重组区域
  const reformatArea = () => {
    cubeVar.value = formatFill(props.cubeSize);
  };

  watch(() => props.cubeSize, reformatArea);

  onMounted(batchSet);

  defineExpose({
    clearTargetArea,
    reformatArea,
    batchSet,
  });
</script>

<style lang="less" scoped>
  ul,
  li {
    list-style: none;
  }

  ul {
    margin-bottom: 0;

    li.custom-rubik-cube {
      user-select: none;

      &:first-child {
        border-left: 1px solid #e5e5e5;
      }
    }

    &:last-child {
      li.custom-rubik-cube {
        border-bottom: 1px solid #e5e5e5;
      }
    }
  }

  .cube-wrap {
    position: relative;
  }

  .custom-rubik-cube {
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #e5e5e5;
    border-bottom: 0;
    border-left: 0;
    background: #f8f8f8;
    color: #bbb;
    font-size: 20px;
    cursor: pointer;

    &.selected {
      background: #ebf8fd;
    }

    &.selecting {
      background: #ebf8fd;
    }
  }
</style>
