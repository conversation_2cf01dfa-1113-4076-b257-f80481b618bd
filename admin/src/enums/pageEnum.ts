const meun_list = localStorage.getItem('sld_menu_data') ? JSON.parse(localStorage.getItem('sld_menu_data')) : [];
let base_home = ''
if(meun_list&& Array.isArray(meun_list)&&meun_list.length>0){
  base_home = meun_list[0].children[0].frontPath
}
export enum PageEnum {
  // basic login path
  BASE_LOGIN = '/login',
  // basic home path
  BASE_HOME = base_home?base_home:'/sysset_home/basic',
  // error page path
  ERROR_PAGE = '/exception',
  // error log page path
  ERROR_LOG_PAGE = '/error-log/list',
}
export const PageWrapperFixedHeightKey = 'PageWrapperFixedHeight';
