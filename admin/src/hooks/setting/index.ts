import type { GlobConfig } from '/#/config';

import { getAppEnvConfig } from '/@/utils/env';

export const useGlobSetting = (): Readonly<GlobConfig> => {
  const {
    VITE_GLOB_APP_TITLE,
    VITE_GLOB_API_URL,
    VITE_GLOB_API_URL_PREFIX,
    VITE_PC_URL,
    VITE_GLOB_IM_URL,
    VITE_GLOB_MAP_KEY,
    VITE_GLOB_MAP_SECURITY,
  } = getAppEnvConfig();

  // Take global configuration
  const glob: Readonly<GlobConfig> = {
    title: VITE_GLOB_APP_TITLE,
    apiUrl: VITE_GLOB_API_URL,
    shortName: VITE_GLOB_APP_TITLE.replace(/\s/g, '_').replace(/-/g, '_'),
    urlPrefix: VITE_GLOB_API_URL_PREFIX,
    pcUrl: VITE_PC_URL,
    imUrl: VITE_GLOB_IM_URL,
    mapKey: VITE_GLOB_MAP_KEY,
    mapSecurity: VITE_GLOB_MAP_SECURITY,
  };
  return glob as Readonly<GlobConfig>;
};
