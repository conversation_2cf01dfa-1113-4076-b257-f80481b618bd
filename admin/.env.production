# 是否开启mock数据，关闭时需要自行对接后台接口
VITE_USE_MOCK = false

# 资源公共路径,需要以 /开头和结尾
VITE_PUBLIC_PATH = /

# 接口地址
VITE_GLOB_API_URL = 'https://admin.shengu.shop'

# 客服地址
VITE_GLOB_IM_URL = 'https://im.shengu.shop'

# 接口前端
VITE_GLOB_API_URL_PREFIX = '/v3'

# 用于PC装修的预览，有PC端需要填，否则留空即可
VITE_PC_URL = 'https://shop.shengu.shop'

# 高德地图 key / security
VITE_GLOB_MAP_KEY ='b618f24ac6d6644c8c8c525470cf7a2f'
VITE_GLOB_MAP_SECURITY ='c32cf6394971458c91bd13149a7cc11f'

# 时间延迟
VITE_TIMEOUT = 15

# 删除console
VITE_DROP_CONSOLE = true

# copyright *** slodon *** version-v5.5.2.1 *** date-2024-09-27 ***主版本v5.5.2.2