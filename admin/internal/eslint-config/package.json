{"name": "@vben/eslint-config", "version": "1.0.0", "private": true, "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": {"url": "https://github.com/vbenjs/vue-vben-admin/issues"}, "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "internal/eslint-config"}, "license": "MIT", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./strict": {"types": "./dist/strict.d.ts", "import": "./dist/strict.mjs", "require": "./dist/strict.cjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"clean": "pnpm rimraf .turbo node_modules dist", "lint": "pnpm eslint .", "stub": "pnpm unbuild --stub"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.3.0", "@typescript-eslint/parser": "^6.3.0", "eslint": "^8.46.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.28.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-vue": "^9.17.0", "vue-eslint-parser": "^9.3.1"}}