# 佣金组件修复验证

## 修复内容

### 问题1：佣金比例显示和提交值不正确
- **问题描述**：输入2.3时，应该显示2.3%，但提交给接口的commissionRate值应该是0.023
- **修复方案**：
  - 添加了`commissionRateDisplay`字段用于显示值
  - `commissionRateDisplay`存储用户输入的百分比数值（如2.3）
  - `commissionRate`存储实际提交给接口的小数值（如0.023）

### 问题2：佣金比例应该只能输入整数
- **问题描述**：佣金比例输入框应该只能输入1-99的整数，不能有小数
- **修复方案**：
  - 设置InputNumber的`:precision="0"`确保只能输入整数
  - 设置`:min="1"`和`:max="99"`限制输入范围
  - 设置`:step="1"`确保步进值为1
  - 使用`:parser="parseIntegerOnly"`和`:formatter="formatIntegerOnly"`强制处理为整数
  - 使用`@keypress="preventDecimalInput"`阻止用户输入小数点等非数字字符
  - 在`handleCommissionRate`和`blurCommissionRate`函数中使用`parseInt()`确保值为整数

## 修改的关键代码部分

### 1. 输入框配置
```vue
<InputNumber
  :min="1"
  :max="99"
  :precision="0"
  :step="1"
  :parser="parseIntegerOnly"
  :formatter="formatIntegerOnly"
  class="number_input"
  v-model:value="item.commissionRateDisplay"
  :placeholder="L('请输入比例')"
  @change="handleCommissionRate($event, item.goodsId)"
  @blur="blurCommissionRate($event, item.goodsId)"
  @keypress="preventDecimalInput"
>
  <template #addonAfter>%</template>
</InputNumber>
```

### 2. 预计佣金计算
```vue
{{ L('预计佣金') }}: ¥{{
  ((Number(item.goodsPrice) * (item.commissionRateDisplay || 0)) / 100).toFixed(2)
}}
```

### 3. 处理函数
```javascript
const handleCommissionRate = (e, goodsId) => {
  let target = selectedRows.value.filter((items) => items.goodsId == goodsId)[0];
  // 确保输入值为整数，范围1-99
  let displayValue = parseInt(e) || 1;
  if (displayValue > 99) {
    displayValue = 99;
  } else if (displayValue < 1) {
    displayValue = 1;
  }
  target.commissionRateDisplay = displayValue; // 保存显示值（如2）
  target.commissionRate = displayValue / 100; // 保存实际值（如0.02）
};

// 解析器：只允许整数输入
const parseIntegerOnly = (value) => {
  if (!value) return '';
  // 移除所有非数字字符，包括小数点
  const numericValue = value.toString().replace(/[^\d]/g, '');
  const intValue = parseInt(numericValue) || '';
  return intValue.toString();
};

// 格式化器：确保显示为整数
const formatIntegerOnly = (value) => {
  if (!value) return '';
  const intValue = parseInt(value) || '';
  return intValue.toString();
};

// 阻止小数点输入
const preventDecimalInput = (event) => {
  // 阻止输入小数点、负号等非数字字符
  const char = String.fromCharCode(event.which);
  if (!/[0-9]/.test(char)) {
    event.preventDefault();
  }
};
```

## 测试场景

1. **输入整数测试**：
   - 输入2，显示2%，commissionRate = 0.02
   - 输入99，显示99%，commissionRate = 0.99
   - 输入1，显示1%，commissionRate = 0.01

2. **边界值测试**：
   - 输入0，自动调整为1%
   - 输入100，自动调整为99%
   - 输入负数，自动调整为1%

3. **小数输入测试**：
   - 输入2.3，自动转换为2%
   - 输入2.7，自动转换为2%

4. **预计佣金计算测试**：
   - 商品价格100元，输入2%，预计佣金显示¥2.00
   - 商品价格50元，输入5%，预计佣金显示¥2.50

## 提交给接口的数据格式

当用户选择佣金比例类型并输入2时：
- 界面显示：2%
- 预计佣金计算：基于2%计算
- 提交给接口的commissionRate：0.02

## 修复的两个组件

### 1. SldSelMoreLeftRightSpreaderGoods 组件
- 文件路径：`seller/src/components/SldSelMoreLeftRightSpreaderGoods/index.vue`
- 修复方式：直接在 InputNumber 组件上添加属性和事件处理

### 2. SpreaderGoodsList 页面的 SldModal 组件
- 文件路径：`seller/src/views/spreader/goods_list.vue` 和 `seller/src/components/SldModal/index.vue`
- 修复方式：
  1. 在表单配置中添加 `parser`、`formatter`、`keypress` 属性
  2. 在 SldModal 组件中添加对应的处理函数
  3. 支持通过字符串名称引用处理函数

## 新增的 SldModal 组件功能

SldModal 组件现在支持以下新属性：
- `parser`: 解析器函数名（字符串）
- `formatter`: 格式化器函数名（字符串）
- `keypress`: 键盘事件处理函数名（字符串）

内置的处理函数：
- `parseIntegerOnly`: 只允许整数输入的解析器
- `formatIntegerOnly`: 整数格式化器
- `preventDecimalInput`: 阻止小数点输入的键盘事件处理器
