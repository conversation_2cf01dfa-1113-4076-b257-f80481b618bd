<template>
  <view class="profile-container">
    <view class="header">
      <text class="title">个人中心</text>
    </view>
    <view class="profile-content">
      <text v-if="userStore.isLoggedIn">欢迎，{{ userStore.getUserInfo.name }}</text>
      <text v-else>请先登录</text>
    </view>
  </view>
</template>

<script setup>
import { useUserStore } from '@/stores/user.js'

const userStore = useUserStore()

// 可以直接调用store的方法
// userStore.login(userData)
// userStore.logout()
</script>

<style scoped>
.profile-container {
  padding: 20rpx;
}
.header {
  text-align: center;
  margin-bottom: 40rpx;
}
.title {
  font-size: 32rpx;
  font-weight: bold;
}
</style>