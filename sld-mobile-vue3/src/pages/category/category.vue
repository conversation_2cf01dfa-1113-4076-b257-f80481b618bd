<template>
  <view class="goods-container">
    <view class="header">
      <text class="title">商品列表</text>
    </view>
    <!-- 商品内容区域 -->
    <view class="goods-content">
      <text>商品页面内容</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      goodsList: []
    }
  },
  onLoad() {
    // 加载商品数据
  }
}
</script>

<style scoped>
.goods-container {
  padding: 20rpx;
}
.header {
  text-align: center;
  margin-bottom: 40rpx;
}
.title {
  font-size: 32rpx;
  font-weight: bold;
}
</style>