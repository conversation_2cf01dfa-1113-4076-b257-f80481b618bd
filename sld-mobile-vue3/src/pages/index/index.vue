<template>
  <view class="content">
    <image class="logo" src="/static/logo.png"></image>
    <view class="text-area">
      <text class="title">{{ title }}</text>
    </view>
    <button class="product-btn" @click="goToProduct">查看商品详情</button>
  </view>
</template>

<script>
export default {
  data() {
    return {
      title: 'Hello',
    }
  },
  onLoad() {},
  methods: {
    goToProduct() {
      const productId = import.meta.env.VITE_MOCK_PRODUCT_ID
      uni.navigateTo({
        url: `/pages/product/detail?productId=${productId}`
      })
    }
  },
}
</script>

<style>
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.logo {
  height: 200rpx;
  width: 200rpx;
  margin-top: 200rpx;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 50rpx;
}

.text-area {
  display: flex;
  justify-content: center;
}

.title {
  font-size: 36rpx;
  color: #8f8f94;
}

.product-btn {
  margin-top: 40rpx;
  padding: 20rpx 40rpx;
  background: #007aff;
  color: #fff;
  border-radius: 10rpx;
  border: none;
  font-size: 28rpx;
}
</style>
