<template>
  <view class="product-detail">
    <!-- 商品图片轮播 -->
    <view class="product-images">
      <swiper class="swiper" indicator-dots autoplay>
        <swiper-item v-for="(image, index) in defaultProduct.goodsPics" :key="index">
          <image :src="image" mode="aspectFill" />
        </swiper-item>
      </swiper>
    </view>

    <!-- 商品基本信息卡片 -->
    <view class="card product-info-card">
      <view class="price-section">
        <view class="price-container">
          <view class="current-price-wrapper">
            <text class="currency">¥</text><text class="current-price">{{ formatPrice(showPrice) }}</text>
          </view>
          <view v-if="originalPrice" class="original-price">原价 ¥{{ formatPrice(originalPrice) }}</view>
        </view>
        <view class="share-btn">
          <image src="https://sg-mall-prod.oss-accelerate.aliyuncs.com/mall-vue3/image/icon/icon_share.png" class="share-icon" />
          <text class="share-text">分享</text>
        </view>
      </view>
      <view class="product-name">徐贺的大厂JAVA课程</view>
      <view class="sales-info">
        <text>销量: {{ goodsData.sales || 0 }}</text>
        <text>库存: {{ defaultProduct.productStock || 99 }}</text>
      </view>
    </view>

    <!-- 配送/运费卡片 -->
    <view class="card delivery-card">
      <view class="delivery-item">
        <text class="delivery-label">配送</text>
        <text class="delivery-value">请选择收货地址</text>
        <image src="https://sg-mall-prod.oss-accelerate.aliyuncs.com/mall-vue3/image/icon/icon_arrow.png" class="delivery-arrow" />
      </view>
      <view class="delivery-item">
        <text class="delivery-label">运费</text>
        <text class="delivery-value">免运费</text>
      </view>
    </view>

    <!-- 规格选择卡片 -->
    <view class="card spec-section" v-if="specs.length > 0">
      <view class="spec-title">选择规格</view>
      <view v-for="spec in specs" :key="spec.specId" class="spec-group">
        <view class="spec-name">{{ spec.specName }}</view>
        <view class="spec-values">
          <view 
            v-for="value in spec.specValueList" 
            :key="value.specValueId"
            :class="['spec-value', { active: value.checkState === 1 }]"
            @click="selectSpec(spec.specId, value.specValueId)"
          >
            {{ value.specValueName }}
          </view>
        </view>
      </view>
    </view>

    <!-- 店铺信息卡片 -->
    <view class="card store-info" v-if="storeInf">
      <view class="store-header">
        <image src="https://sg-mall-prod.oss-accelerate.aliyuncs.com/mall-vue3/image/icon/icon_avatar.png" class="store-avatar" />
        <view class="store-details">
          <view class="store-name">{{ storeInf.storeName || '潜动力心理' }}</view>
          <view class="store-desc">{{ storeInf.storeDesc || '专业心理服务' }}</view>
        </view>
        <view class="enter-store">
          <text class="enter-store-text">进入店铺</text>
          <image src="https://sg-mall-prod.oss-accelerate.aliyuncs.com/mall-vue3/image/icon/icon_arrow.png" class="enter-store-arrow" />
        </view>
      </view>
    </view>

    <!-- 商品详情卡片 -->
    <view class="card product-detail-content">
      <view class="detail-title">关于退换服务</view>
      <view class="detail-item">>> 调换服务</view>
      <rich-text :nodes="goodsData.goodsDetail"></rich-text>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottom-actions">
      <view class="action-left">
        <view class="action-item" @click="goHome">
          <image src="https://sg-mall-prod.oss-accelerate.aliyuncs.com/mall-vue3/image/icon/icon_home.png" class="action-icon" />
          <text class="action-text">首页</text>
        </view>
        <view class="action-item" @click="contactService">
          <image src="https://sg-mall-prod.oss-accelerate.aliyuncs.com/mall-vue3/image/icon/icon_worker.png" class="action-icon" />
          <text class="action-text">客服</text>
        </view>
        <view class="action-item" @click="toggleFavorite">
          <image src="https://sg-mall-prod.oss-accelerate.aliyuncs.com/mall-vue3/image/icon/icon_product_fav.png" class="action-icon" />
          <text class="action-text">收藏</text>
        </view>
      </view>
      <view class="action-right">
        <button class="add-cart-btn" @click="addToCart">加入购物车</button>
        <button class="buy-now-btn" @click="buyNow">立即购买</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import request from '@/utils/request'

// 响应式数据
const goodsData = reactive({
  goodsName: '',
  goodsSubtitle: '',
  goodsImageList: [],
  goodsDetail: '',
  sales: 0,
  state: 0,
  shareLink: '',
  shareImage: '',
  isVirtualGoods: false,
  deliveryMethod: []
})

const defaultProduct = ref({})
const specs = ref([])
const storeInf = ref({})
const specIdMap = reactive(new Map())
const changePrice = ref(0)
const showPrice = ref(52.00)
const originalPrice = ref(52.99)
const promotionId = ref(0)

// 页面参数
const productId = ref('')
const goodsId = ref('')
const source = ref('')

// 获取商品详情动态数据
const getGoodsDetailDynamic = async () => {
  try {
    const res = await request({
      url: "v3/goods/front/goods/details2",
      data: {
        productId: productId.value,
        goodsId: goodsId.value,
        source: source.value,
      },
    })

    if (res.state === 200) {
      // 更新动态数据
      const dynamicData = [
        "effectSpecValueIds",
        "followGoods", 
        "sales",
        "state",
        "shareLink",
        "shareImage",
        "isVirtualGoods",
        "storeInf",
        "deliveryMethod",
      ]
      
      dynamicData.forEach((item) => {
        goodsData[item] = res.data[item]
      })

      // 设置默认货品信息
      defaultProduct.value = res.data.defaultProduct
      changePrice.value = defaultProduct.value.productPrice
      showPrice.value = defaultProduct.value.superPrice || defaultProduct.value.productPrice

      // 设置规格列表
      specs.value = res.data.specs || []
      specs.value.forEach((item) => {
        const value = item.specValueList.find((i) => i.checkState === 1)
        if (value) {
          specIdMap.set(item.specId, value.specValueId)
        }
      })

      // 设置店铺信息
      storeInf.value = res.data.storeInf
      
      // 设置活动ID
      promotionId.value = defaultProduct.value.promotionId
    }
  } catch (error) {
    console.error('获取商品详情失败:', error)
    uni.showToast({
      title: '获取商品详情失败',
      icon: 'none'
    })
  }
}

// 选择规格
const selectSpec = (specId, specValueId) => {
  // 更新规格选择状态
  specs.value.forEach(spec => {
    if (spec.specId === specId) {
      spec.specValueList.forEach(value => {
        value.checkState = value.specValueId === specValueId ? 1 : 0
      })
    }
  })
  
  // 更新规格映射
  specIdMap.set(specId, specValueId)
  
  // 这里可以添加根据规格组合获取对应货品信息的逻辑
  // updateProductBySpecs()
}

// 加入购物车
const addToCart = () => {
  // 实现加入购物车逻辑
  uni.showToast({
    title: '已加入购物车',
    icon: 'success'
  })
}

// 立即购买
const buyNow = () => {
  // 实现立即购买逻辑
  uni.navigateTo({
    url: '/pages/order/confirm'
  })
}

// 格式化价格（保留两位小数）
const formatPrice = (price) => {
  return Number(price).toFixed(2)
}

// 返回首页
const goHome = () => {
  uni.switchTab({
    url: '/pages/index/index'
  })
}

// 联系客服
const contactService = () => {
  uni.showToast({
    title: '联系客服',
    icon: 'none'
  })
}

// 收藏/取消收藏
const toggleFavorite = () => {
  uni.showToast({
    title: '收藏成功',
    icon: 'success'
  })
}

// 页面加载时获取参数
onLoad((options) => {
  productId.value = options.productId || import.meta.env.VITE_MOCK_PRODUCT_ID || ''
  goodsId.value = options.goodsId || ''
  source.value = options.source || ''
})

// 页面挂载后获取数据
onMounted(() => {
    getGoodsDetailDynamic()
})
</script>

<style scoped>
.product-detail {
  background: #f5f5f5;
  min-height: 100vh;
  padding: 0 24rpx 160rpx 24rpx;
}

.product-images {
  background: #fff;
  margin: 0 -24rpx 24rpx -24rpx;
}

.swiper {
  height: 750rpx;
}

.swiper image {
  width: 100%;
  height: 100%;
}

/* 卡片通用样式 */
.card {
  background: #fff;
  border-radius: 32rpx;
  padding: 30rpx;
  margin-bottom: 24rpx;
}

/* 商品信息卡片 */
.product-info-card {
  padding: 30rpx;
}

.price-section {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.price-container {
  display: flex;
  flex-direction: column;
}

.current-price-wrapper {
  display: flex;
  align-items: baseline;
}

.currency {
  font-size: 28rpx;
  color: #222222;
  font-weight: bold;
}

.current-price {
  font-size: 48rpx;
  color: #222222;
  font-weight: bold;
}

.share-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.share-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 8rpx;
}

.share-text {
  font-size: 24rpx;
  color: #666;
}

.original-price {
  font-size: 26rpx;
  color: #999;
  text-decoration: line-through;
  margin-top: 8rpx;
}

.product-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.sales-info {
  display: flex;
  gap: 40rpx;
  font-size: 26rpx;
  color: #999;
}

/* 配送卡片 */
.delivery-card {
  padding: 0;
}

.delivery-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.delivery-item:last-child {
  border-bottom: none;
}

.delivery-label {
  font-size: 28rpx;
  color: #333;
  width: 80rpx;
  margin-right: 30rpx;
}

.delivery-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.delivery-arrow {
  width: 40rpx;
  height: 40rpx;
}

/* 规格选择卡片 */
.spec-section {
  padding: 30rpx;
}

.spec-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
}

.spec-group {
  margin-bottom: 30rpx;
}

.spec-name {
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.spec-values {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.spec-value {
  padding: 15rpx 30rpx;
  border: 2rpx solid #ddd;
  border-radius: 10rpx;
  font-size: 26rpx;
  background: #fff;
}

.spec-value.active {
  border-color: #70BF74;
  color: #70BF74;
  background: #f0fff0;
}

/* 店铺信息卡片 */
.store-info {
  padding: 30rpx;
}

.store-header {
  display: flex;
  align-items: center;
}

.store-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 24rpx;
}

.store-details {
  flex: 1;
}

.store-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.store-desc {
  font-size: 26rpx;
  color: #666;
}

.enter-store {
  display: flex;
  align-items: center;
}

.enter-store-text {
  font-size: 26rpx;
  color: #999;
  margin-right: 8rpx;
}

.enter-store-arrow {
  width: 40rpx;
  height: 40rpx;
}

/* 商品详情卡片 */
.product-detail-content {
  padding: 30rpx;
}

.detail-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 30rpx;
  border-bottom: 2rpx solid #333;
  padding-bottom: 10rpx;
  display: inline-block;
  width: 100%;
}

.detail-item {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  background: #fff;
  padding: 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.1);
}

.action-left {
  display: flex;
  margin-right: 20rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 20rpx;
}

.action-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 8rpx;
}

.action-text {
  font-size: 22rpx;
  color: #666;
}

.action-right {
  display: flex;
  flex: 1;
  gap: 16rpx;
}

.add-cart-btn, .buy-now-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
  border: none;
  color: #fff;
  outline: none;
}

.add-cart-btn {
  background: linear-gradient(135deg, #FFA726 0%, #FF8A65 100%);
}

.buy-now-btn {
  background: #70BF74;
}
</style>
