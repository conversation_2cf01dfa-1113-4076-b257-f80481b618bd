<template>
  <view>
    <LoginPopup />
  </view>
</template>


<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { setupLoginInterceptor } from '@/utils/interceptor.js'
import LoginPopup from '@/components/LoginPopup.vue';


onMounted(async () => {

  console.log('🚀 App Launch')
  console.log('🌍 当前环境:', import.meta.env.MODE)
  console.log('🔗 API地址:', import.meta.env.VITE_API_BASE_URL)
  console.log('📱 应用名称:', import.meta.env.VITE_APP_NAME)
  console.log('⚙️ 环境变量:', import.meta.env)


  setupLoginInterceptor()

  await nextTick()
  console.log('📱 App已挂载')
})
</script>
