import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { updateStatCommonProperty } from '../utils/stat.js'
import request from '../utils/request.js'
import { useMemberConfig } from '../utils/hooks.js'

export const useUserStore = defineStore('user', () => {
  // State
  const hasLogin = ref(false)
  const userInfo = ref(uni.getStorageSync('userInfo') ?? {})
  const userCenterData = ref(uni.getStorageSync('userCenterData') ?? {})
  const cartData = ref({})
  const addressList = ref([])
  const chatBaseInfo = ref(uni.getStorageSync('chatBaseInfo') ? uni.getStorageSync('chatBaseInfo') : {})
  const memberConfig = ref({})
  const x_diyStyle = ref({})
  const msgNumState = ref(0)
  const shopSetting = ref(uni.getStorageSync('shopSetting') ?? {})
  const verificationCodeCheckIsEnable = ref(0)

  // Getters
  const isLoggedIn = computed(() => hasLogin.value)
  const getUserInfo = computed(() => userInfo.value)

  // Actions
  const setVerificationCodeCheck = (value) => {
    verificationCodeCheckIsEnable.value = value
  }

  const setHasLogin = (provider) => {
    hasLogin.value = provider
  }

  const login = (provider) => {
    hasLogin.value = true
    const { role, access_token, refresh_token, ...userData } = provider
    const target = userData
    const {
      member_access_token,
      member_refresh_token
    } = userInfo.value
    
    target.member_access_token = member_access_token
    target.member_refresh_token = member_refresh_token
    target[`${role}_access_token`] = access_token
    target[`${role}_refresh_token`] = refresh_token
    
    userInfo.value = target
    uni.setStorageSync('userInfo', userInfo.value)
    
    const app = getApp()
    app.$vm.$globalSocketIO.initialModule()
    updateStatCommonProperty({ memberId: provider.memberId })
  }

  const logout = () => {
    hasLogin.value = false
    userInfo.value = {}
    userCenterData.value = {}
    cartData.value = {}
    addressList.value = []
    chatBaseInfo.value = {}
    
    uni.removeStorage({ key: 'addressId' })
    uni.removeStorage({ key: 'userInfo' })
    uni.removeStorage({ key: 'userCenterData' })
    updateStatCommonProperty({ memberId: 0 })
    
    const app = getApp()
    app.$vm.$globalSocketIO.closeSocket()
  }

  const setUserCenterData = (provider) => {
    userCenterData.value = provider
    uni.setStorageSync('userCenterData', provider)
  }

  const operateCartData = (provider) => {
    cartData.value = provider
  }

  const operateAddressData = (provider) => {
    addressList.value = provider
  }

  const saveChatBaseInfo = (provider) => {
    chatBaseInfo.value = provider
    uni.setStorageSync('chatBaseInfo', provider)
  }

  const saveMemberConfig = (payload) => {
    memberConfig.value = { ...memberConfig.value, ...payload }
  }

  const saveDiyStyle = (provider) => {
    x_diyStyle.value = provider
    uni.setStorageSync('x_diyStyle', provider)
  }

  const setMsgNum = (provider) => {
    msgNumState.value = provider
  }

  const getMemberConfig = async (args) => {
    return useMemberConfig(args, { commit: saveMemberConfig })
  }

  const getTlSetting = async () => {
    try {
      const res = await request({
        url: 'v3/system/front/setting/getSettings',
        data: {
          names: 'tl_is_enable'
        }
      })
      if (res.state == 200) {
        // setTl(res.data[0]) - 需要添加setTl方法
      }
    } catch (error) {
      console.error('获取TL设置失败:', error)
    }
  }

  const getVerificationCodeCheckSetting = async () => {
    try {
      const res = await request({
        url: 'v3/system/front/setting/getSettings',
        data: {
          names: 'verificationCodeCheckIsEnable'
        }
      })
      setVerificationCodeCheck(0)
      // if (res.state == 200 && res.data && res.data.length > 0) {
      //   setVerificationCodeCheck(Number(res.data[0].value))
      // }
    } catch (error) {
      console.error('获取验证码设置失败:', error)
    }
  }

  return {
    // State
    hasLogin,
    userInfo,
    userCenterData,
    cartData,
    addressList,
    chatBaseInfo,
    memberConfig,
    x_diyStyle,
    msgNumState,
    shopSetting,
    verificationCodeCheckIsEnable,
    // Getters
    isLoggedIn,
    getUserInfo,
    // Actions
    setVerificationCodeCheck,
    setHasLogin,
    login,
    logout,
    setUserCenterData,
    operateCartData,
    operateAddressData,
    saveChatBaseInfo,
    saveMemberConfig,
    saveDiyStyle,
    setMsgNum,
    getMemberConfig,
    getTlSetting,
    getVerificationCodeCheckSetting
  }
})