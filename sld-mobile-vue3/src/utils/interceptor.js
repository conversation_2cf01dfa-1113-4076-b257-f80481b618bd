import { useUserStore } from '@/stores/user.js'

// 需要登录的页面路径
const loginRequiredPages = [
  '/pages/user/user',
  '/pages/cart/cart',
  '/pages/order/list',
  '/pages/order/confirm'
]

// 登录拦截器
export function setupLoginInterceptor() {
  console.log('🔒 登录拦截器已启动')
  console.log('📋 需要登录的页面:', loginRequiredPages)

  // 页面跳转拦截
  uni.addInterceptor('navigateTo', {
    invoke(args) {
      const userStore = useUserStore()
      const url = args.url

      // 提取页面路径（去掉参数）
      const pagePath = url.split('?')[0]

      console.log('🚀 页面跳转拦截:', {
        原始URL: url,
        页面路径: pagePath,
        登录状态: userStore.isLoggedIn,
        需要登录: loginRequiredPages.includes(pagePath)
      })

      // 检查是否需要登录
      if (loginRequiredPages.includes(pagePath) && !userStore.isLoggedIn) {
        console.log('❌ 拦截跳转 - 用户未登录')

        // 阻止跳转
        uni.showToast({
          title: '请先登录',
          icon: 'none'
        })

        // 直接调用store方法显示登录弹窗
        console.log('navigateTo📱 触发登录弹窗')
        uni.$emit('showLoginModal')
        return false
      }

      console.log('✅ 允许跳转')
      return true
    }
  })

}

// 移除拦截器
export function removeLoginInterceptor() {
  console.log('🔓 移除登录拦截器')
  uni.removeInterceptor('navigateTo')
  uni.removeInterceptor('switchTab')
}
