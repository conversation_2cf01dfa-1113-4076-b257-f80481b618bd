import { useUserStore } from '../stores/user.js'

const apiUrl = import.meta.env.VITE_API_BASE_URL

const getCurLang = () => {
	return uni.getStorageSync('language') || 'zh-CN'
}

// 获取token
const getToken = () => {
	const userStore = useUserStore()
	const userInfo = userStore.userInfo
	return {
		access_token: userInfo.member_access_token,
		refresh_token: userInfo.member_refresh_token
	}
}

export default async function request(opt) {
	opt = opt || {};
	opt.url = opt.url || '';
	opt.data = opt.data || null;
	opt.method = opt.method || 'GET';

	let otherParam = {};
	if (!opt.responseType) {
		opt.header = opt.header || {
			"Content-Type": "application/x-www-form-urlencoded",
			"Language": getCurLang()
		};
		otherParam.dataType = 'json'
	} else {
		opt.header = {
			"Language": getCurLang()
		}
		otherParam.responseType = opt.responseType;
		if (opt.dataType) {
			otherParam.dataType = opt.dataType
		}
	}

	let userInfo = uni.getStorageSync('userInfo') || '';
	if (opt.url.indexOf('frontLogin/oauth/token') == -1) {
		let tokenInfo = getToken()
		let token = tokenInfo.access_token || '';
		opt.header.Authorization = 'Bearer ' + token;

		// 判断token是否过期(58分钟，token的有效期是60分钟)
		let cur_time = new Date().getTime();
		let start_time = uni.getStorageSync('sld_login_time');
		let sld_refresh_token = tokenInfo.refresh_token;
		if (start_time && (cur_time - start_time > (58 * 60 * 1000)) && sld_refresh_token) {
			let res = await refreshToken(sld_refresh_token);
			res = res.data != undefined ? res.data : { state: 255 }
			if (res.state == 200) {
				// 更新本地存储的用户信息
				let updatedUserInfo = { ...userInfo, ...res.data }
				uni.setStorageSync('userInfo', updatedUserInfo)
				uni.setStorage({
					key: 'sld_login_time',
					data: new Date().getTime(),
				});
				opt.header.Authorization = 'Bearer ' + res.data.access_token;
			} else {
				// 清空本地用户信息
				uni.removeStorageSync('userInfo')
				uni.removeStorageSync('sld_login_time')
			}
		}
	}

	// 设置终端来源
	// #ifdef H5
	opt.header['Terminal-source'] = 2;
	// #endif
	// #ifdef MP
	opt.header['Terminal-source'] = 6;
	// #endif

	return new Promise((resolve, reject) => {
		uni.request({
			url: apiUrl + opt.url,
			data: opt.data,
			method: opt.method,
			header: opt.header,
			...otherParam,
			success: res => {
				if (res.data.state == 266) {
					uni.removeStorageSync('userInfo')
					uni.removeStorageSync('sld_login_time')
				} else if (res.data.state == 270) {
					res.data.msg = '操作失败，输入的文本包含敏感信息' + res.data.msg + '，请修改后重新操作'
					resolve(res.data);
				} else {
					resolve(res.data);
				}
			},
			fail: err => {
				reject(err);
			},
			complete: (res) => { }
		})
	})
}

/** 
 * 刷新token
 * @zjf-2021-07-22
 */
function refreshToken(refresh_token) {
	return new Promise(func => {
		uni.request({
			url: apiUrl + 'v3/frontLogin/oauth/token',
			data: {
				grant_type: 'refresh_token',
				refresh_token: refresh_token,
			},
			method: 'POST',
			header: {
				"Content-Type": "application/x-www-form-urlencoded",
				"Language": getCurLang(),
			},
			success: res => {
				func(res);
			}
		})
	})
}
