<template>
    <view>
        <uni-popup
            ref="popupRef"
            type="top"
        >
                HI
        </uni-popup>
    </view>
</template>

<script setup>
console.log('组件初始化:')
import { onMounted, ref } from 'vue'

const popupRef = ref(null)

onMounted(() => {
    console.log('组件挂载:')
})

uni.on('showLoginModal', (data) => {
    console.log('收到登录弹窗事件:', data)
    popupRef.value.open()
})

</script>