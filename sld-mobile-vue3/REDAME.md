
# 潜动力电商vue3版本
### 快速开始
```bash
nvm use 18 # uniapp vue3 必须使用node18以上 
npm install
npm run dev:h5
```

```bash 
npm run dev:h5:prod  # 本地启动生产环境调试
```

### 配置文件
[.env.development](.env.development) # 开发环境  
[.env.test](.env.test) # 测试环境  
[.env.production](.env.production) # 生产环境  

### 重点内容讲解
```js
// 获取环境 development |test| production  开发 测试 生产环境
process.env.NODE_ENV 

// 获取配置
import.meta.env.VITE_MOCK_PRODUCT_ID

// 只支持es6写法
require('@/utils/XXX.js') ❌
import XXX from '@/utils/XXX.js' ✅
```


### 推荐阅读
[vue2迁移vue3](https://uniapp.dcloud.net.cn/tutorial/migration-to-vue3.html)

[状态管理pinia](https://uniapp.dcloud.net.cn/tutorial/vue3-pinia.html)