**/node_modules
**/unpackage
**/dist
**/.DS_Store
**/.turbo
.vscode
.idea
**/.class

target/
!.mvn/wrapper/maven-wrapper.jar

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans

### IntelliJ IDEA ###
.idea
.idea/
*.iws
*.iml
*.ipr
.DS_Store

### NetBeans ###
nbproject/private/
build/
nbbuild/
dist/
nbdist/
*.pid
.DS_Storeworkspace
workspace

### VSCode ###
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.vscode/*.log
*.code-workspace.backup
*.code-search
.gitlens/
.vscode/extensions/
*.tmp
*.tmp.*
*.swo
*.swp
*.autosave.*
.history/
out/
node_modules/

### log ###
app.log

**/target/
