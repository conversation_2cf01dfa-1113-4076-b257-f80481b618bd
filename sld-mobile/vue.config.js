const TransformPages = require('uni-read-pages')
const { webpack } = new TransformPages()

module.exports = {
  filenameHashing: true,
  
  configureWebpack: {
    output: {
      filename: 'static/js/[name].[hash:8].js',
      chunkFilename: 'static/js/[name].[hash:8].js'
    },
    plugins: [
      new webpack.DefinePlugin({
        ROUTES: webpack.DefinePlugin.runtimeValue(() => {
          const tfPages = new TransformPages({
            includes: ['path', 'name', 'meta']
          });
          return JSON.stringify(tfPages.routes)
        }, true)
      })
    ]
  }
}