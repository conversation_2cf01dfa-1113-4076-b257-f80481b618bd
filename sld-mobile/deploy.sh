#!/bin/bash

echo "当前环境: $SG_ENV"

# 拉取最新代码
git pull origin dev

# 构建H5项目
# 根据环境变量决定部署目录
if [ "$SG_ENV" = "test" ]; then
    npm run build:h5:test
    DEPLOY_DIR="/www/wwwroot/m-test.shengu.shop"
    echo "部署到测试环境: $DEPLOY_DIR"
    cp -r dist/build/h5/* "$DEPLOY_DIR/"
elif [ "$SG_ENV" = "prod" ]; then
    # 生产环境部署
    npm run build:h5
    DEPLOY_DIR="/www/wwwroot/m.shengu.shop"
    echo "部署到生产环境: $DEPLOY_DIR"
    cp -r dist/build/h5/* "$DEPLOY_DIR/"
else
    # 本地开发模式
    echo "本地开发模式，不进行部署"
    npm run dev:h5
fi

echo "操作完成"