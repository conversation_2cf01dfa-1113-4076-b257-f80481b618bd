<template>
	<view v-show="isShowMask1" class="wrapper1"
		  @click.stop.prevent="() => null" @touchmove.stop.prevent="() => null">
		<transition name="content">
			<view class="content_view" v-show="isShow">
				<view class="title_view">
					<view class="back_view" v-if="isBack_zuo">
						<image :src="imgUrl+'to_right.png'" mode="aspectFit" v-if="isBack" @click="backToAdd"></image>
						<image src="" mode="aspectFit" v-else></image>
					</view>
					<view class="title">{{$L('请选择所在地区')}}</view>
					<view class="close_view" @click="hidden">
						<icon class="close_icon" :type="'clear'" size="20" color='#DCDCDC' />
					</view>
				</view>
				<view class="select_top">
					<view :class="{select_top_item:true,sel_on:currentIndex == index}" ref="select_top_item"
						v-for="(item,index) in dataList" :key="index" @click="select_top_item_click(index)" :style="{color:currentIndex == index&&color_flag&&color_style?color_style:currentIndex == index?'var(--color_main)':'#333',borderBottomColor:currentIndex == index&&color_flag&&color_style?color_style:currentIndex == index?'var(--color_main)':'#fff'}">
						<text class="address_value">{{item.name}}</text>
					</view>

				</view>
				<swiper class="swiper" :current="currentIndex" @change="swiperChange">
					<swiper-item v-for="(swiper_item,swiper_index) in dataList" :key="swiper_index">
						<view class="swiper-item">
							<scroll-view class="scroll-view-item" scroll-y="true">
								<view class="address_item flex_row_between_center"
									v-for="(item,index) in cityAreaArray[swiper_index]" :key="index"
									@click="address_item_click(swiper_index,index,item)"
									:style="{background:selectIndexArr[swiper_index] === index?bgGrey:bgWhite}">
									{{item.regionName}}
                  <text class="iconfont iconxuanzhong address_item_icon" v-if="selectIndexArr[swiper_index] === index" :style="{color:color_flag&&color_style?color_style:'var(--color_main)'}"></text>

								</view>
							</scroll-view>
						</view>
					</swiper-item>
				</swiper>
			</view>
		</transition>
		<view v-show="isShowMask" class="mask"
			  @click="hidden" @touchmove.stop.prevent="() => null"></view>
	</view>
</template>

<script>
	// import areaData from '../../static/area.json'
	export default {
		data() {
			return {
				imgUrl: process.env.VUE_APP_IMG_URL,
				isShow: false,
				isShowMask: false,
				isShowMask1: false,
				dataList: [{
					code: '',
					name: this.$L('请选择')
				}],
				currentIndex: 0,
				cityData: {},
				cityAreaArray: [],
				selectIndexArr: [],
				indicatorStyleLeft: 16,
				sel_area_tip: {
					code: '',
					name: this.$L('请选择')
				},
				bgGrey: '#F8F8F8',
				bgWhite: '#fff',
				areaData: []
			};
		},
		props: {
			sel_data: {
				type: Array,
				value: []
			},
			isBack: {
				type: Boolean,
				default: false
			},
      isBack_zuo: {
				type: Boolean,
				default: true
			},//是否需要标题左边的内容
      color_flag:{
        type: Boolean,
        default: false
      },//是否需要自定义颜色
      color_style: {
      	type: String,
      	default: ''
      }//自定义颜色
      
		},
		methods: {
			show() {
				this.getAddressList()
				this.isShow = true
				this.isShowMask1 = true
				this.isShowMask = true
				// #ifdef H5
				document.body.style.overflow = "hidden"
				// #endif
			},
			showNoMask() {
				this.getAddressList()
				this.isShowMask1 = true
				this.isShow = true
				// #ifdef H5
				document.body.style.overflow = "hidden"
				// #endif
			},
			getAddressList() {
				let param = {};
				param.url = 'v3/system/common/regionList';
				param.data = {};
				param.method = 'GET';
				this.$request(param).then(res => {
					if (res.state == 200) {
						this.areaData = res.data
						this.cityData = this.areaData
						this.cityAreaArray.push(this.areaData)
					} else {
						this.$api.msg(res.msg);
					}
				}).catch((e) => {
					//异常处理
				})
			},
			hidden() {
				this.isShow = false
				setTimeout(() => {
					this.isShowMask = false
					this.isShowMask1 = false
				}, 100);
				// #ifdef H5
				document.body.style.overflow = ""
				// #endif
			},
			select_top_item_click(index) {
				this.currentIndex = index
				this.$nextTick(() => {
					this.changeIndicator(index)
				})

			},
			swiperChange(event) {
				let index = event.detail.current
				this.currentIndex = index

				this.changeIndicator(index)
			},
			changeIndicator(index) {
				let indicatorWidth = 30
				const query = uni.createSelectorQuery().in(this);
				let arr = query.selectAll('.select_top_item .address_value')
				arr.fields({
					size: true,
					scrollOffset: false
				}, data => {

					let itemWidth = data[index]["width"] > 80 ? 70 : data[index]["width"]
					let itemCenterX = 10 + index * 80 + itemWidth / 2
					let left = itemCenterX - indicatorWidth / 2

					this.indicatorStyleLeft = left

				}).exec();


			},
			address_item_click(swiper_index, index, item) {
				this.selectIndexArr.splice(swiper_index, 5, index)

				//判断当前是否为最下一级
				if (swiper_index === 0) { //第一级
					let currentObj = this.cityData[index]
					let city = currentObj.regionName

					this.dataList.splice(swiper_index, 0, {
						code: currentObj.regionCode,
						name: currentObj.regionName
					});
					this.dataList.splice(swiper_index + 1);
					this.dataList.splice(swiper_index + 1, 0, {
						code: '',
						name: this.$L('请选择')
					})
					this.cityAreaArray.splice(swiper_index + 1, 1, currentObj["children"])
					setTimeout(() => {
						this.currentIndex = 1
						this.changeIndicator(1)
					}, 50);


				} else {
					let currentAreaArray = this.cityAreaArray[swiper_index]
					let currentObj = currentAreaArray[index]
					let area = currentObj["children"]
					if (area.length != 0) {

						let city = currentObj.regionName
						this.dataList.splice(swiper_index, 0, {
							code: currentObj.regionCode,
							name: currentObj.regionName
						})
						this.dataList.splice(swiper_index + 1);
						this.dataList.splice(swiper_index + 1, 0, {
							code: '',
							name: this.$L('请选择')
						})
						this.cityAreaArray.splice(swiper_index + 1, 1, currentObj["children"])

						setTimeout(() => {
							this.currentIndex = swiper_index + 1
							this.changeIndicator(swiper_index + 1)
						}, 50);

					} else { //是最下一级

						let city = currentObj.regionName
						this.dataList.splice(swiper_index, 0, {
							code: currentObj.regionCode,
							name: currentObj.regionName
						})
						//删除最后一个
						this.dataList.splice(swiper_index + 1)

						//选择成功返回数据
						this.$emit("selectAddress", this.dataList)

						this.$nextTick(() => {
							this.changeIndicator(swiper_index)
						})

						this.hidden()

					}

				}

			},
			moveHandle() {

			},
			backToAdd() {
				this.$emit('backToAdd')
			}
		},


		created() {
			// this.cityData = this.areaData
			// this.cityAreaArray.push(this.areaData)
		},
		mounted() {

			// this.changeIndicator(0)
		}
	}
</script>

<style lang="scss">
	// 不换行
	@mixin no-wrap() {
		text-overflow: ellipsis;
		overflow: hidden;
		white-space: nowrap;
	}

	.wrapper1 {
		z-index: 1999;
		position: absolute;
		top: -88rpx;
		left: 0;
		bottom: 0;
		right: 0;
		background-color: rgba(0, 0, 0, 0.4);

		.content_view {
			z-index: 999;
			background: #fff;
			position: fixed;
			height: 65%;
			left: 0;
			bottom: 0;
			right: 0;
			border-top-left-radius: 10rpx;
			border-top-right-radius: 10rpx;
			width: 750rpx;
			margin: 0 auto;

			.title_view {
				height: 12%;
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 0 30rpx;
				height: 100rpx;

				.title {
					font-size: 34rpx;
					font-weight: bold;
					color: #333333;
				}

				.close_view {
					height: 60rpx;
					width: 40rpx;
					display: flex;
					justify-content: center;
					align-items: center;
				}
			}

			.select_top {
				height: 90rpx;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				padding: 0 30rpx;
				position: relative;
				box-sizing: border-box;
				border-bottom: 1rpx solid #f2f2f2;

				.select_top_item {
					margin-right: 30rpx;
					font-size: 28rpx;
					line-height: 80rpx;
					@include no-wrap();
					color: #333;
					border-bottom: 6rpx solid #fff;
					max-width: 200rpx;

					// &:last-child{
					// 	color: var(--color_main);
					// 	border-bottom: 6rpx solid var(--color_main);
					// 	font-weight: bold;
					// 	height: 90rpx;
					// }
					&.sel_on {
						color: var(--color_main);
						border-bottom: 6rpx solid var(--color_main);
						font-weight: bold;
						height: 90rpx;
					}
				}
			}

			.swiper {
				height: 75% !important;
				position: relative;
				left: 0;
				top: 0;
				bottom: 0;
				right: 0;

				.swiper-item {
					height: 100%;

					.scroll-view-item {
						height: 100%;

						.address_item {
							color: #333333;
							font-size: 28rpx;
							display: flex;
							align-items: center;
							height: 90rpx;
							line-height: 90rpx;
							padding: 0 30rpx;

							.address_item_icon {
								font-size: 28rpx;
                color: var(--color_main);
								margin-left: 20rpx;
							}
						}
					}
				}
			}
		}

		.mask {
			position: fixed;
			width: 100%;
			height: 100%;
			top: 0;
			left: 0;
			bottom: 0;
			right: 0;
			background: $uni-text-color-grey;
			opacity: 0.4;
		}
	}

	.content-enter {
		transform: translateY(100%);
	}

	.back_view {
		display: flex;
		align-items: center;

		image {
			width: 30rpx;
			height: 30rpx;
		}

		text {
			font-size: 28rpx;
		}
	}

	.content-enter-to {
		transform: translateY(0%);
	}

	.content-enter-active {
		transition: transform 0.5s;
	}

	.content-leave {
		transform: translateY(0%);
	}

	.content-leave-to {
		transform: translateY(100%);
	}

	.content-leave-active {
		transition: transform 0.5s;
	}
</style>
