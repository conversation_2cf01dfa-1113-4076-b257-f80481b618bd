<template>
	<!-- 优惠券弹框 start -->
	<BottomPop title="领取优惠券" :scrollHeight="880" ref="couponPopup" :hasTab="true">
		<view class="coupon_model">
			<!-- 有优惠券 -->
			<scroll-view  v-if="coupon_list.length>0" class="coupon_model_list" scroll-y="true">
				<view class="my_coupon_pre" v-for="(item, index) in coupon_list" :key="index">
					<view class="coupon_pre_top">
						<image :src="imgUrl + 'coupon/coupon_pre_img_bg.png'" mode="" class="coupon_pre_top_bg_img">
						</image>
						<svgGroup type="to_youhuiquanjuchi" width="190" height="190" px="rpx" class="to_youhuiquan"
							v-if='item.isReceive == 1' :color="diyStyle_var['--color_coupon_main']">
						</svgGroup>
						<svgGroup type="to_youhuiquanjuchi" width="190" height="190" px="rpx" class="to_youhuiquan"
							v-if='item.isReceive == 2' :color="diyStyle_var['--color_coupon_opacity']">
						</svgGroup>
						<svgGroup type="to_youhuiquanjuchi" width="190" height="190" px="rpx" class="to_youhuiquan"
							v-if='item.isReceive == 3' color="#dddddd">
						</svgGroup>
						<view class="coupon_pre_left">
							<!-- 固定券 start -->
							<view class="coupon_pre_price"
								:class="{ coupon_pre_price_high: item.publishValue.toString().length > 6 }"
								v-if="item.couponType == 1">
								<text class="unit">¥ </text>
								<text class="price_int"
									v-if="item.publishValue.toString().indexOf('.') != -1 && item.publishValue.toString().split('.')[1] > 0">{{
									item.publishValue }}</text>
								<text class="price_int" v-else>{{ $getPartNumber(item.publishValue, 'int') }}</text>
							</view>
							<!-- 固定券 end -->
							<!-- 折扣券 start -->
							<view class="coupon_pre_price" v-if="item.couponType == 2">
								<view class=""></view>
								<text
									class="price_int">{{ filters.toSplit(filters.toFixNum(item.publishValue, 1))[0] }}</text>.
								<text
									class="price_decimal">{{ filters.toSplit(filters.toFixNum(item.publishValue, 1))[1] }}</text>
								<text class="price_decimal">{{ $L('折') }}</text>
							</view>
							<!-- 折扣券 end -->
							<!-- 随机券 start -->
							<view class="coupon_pre_price" v-if="item.couponType == 3">
								<text class="unit">¥ </text>
								<text class="price_int">{{ $getPartNumber(item.randomMax, 'int') }}</text>
							</view>
							<!-- 随机券 end -->
							<view class="coupon_pre_active">{{ item.couponContent }}</view>
						</view>
						<view class="coupon_pre_cen">
							<view class="coupon_pre_title">{{ item.useTypeValue }}</view>
							<view class="coupon_pre_time">{{ item.effectiveStart }}~{{ item.effectiveEnd }}
							</view>
							<view class="coupon_pre_rules" @click="descriptionOpen(item.couponId)">
								<text>{{ $L('使用规则') }}</text>
								<image :src="item.isOpen ? imgUrl + 'coupon/up.png' : imgUrl + 'coupon/down.png'"
									mode="aspectFit">
								</image>
							</view>
						</view>
						<view class="coupon_pre_right" v-if="item.isReceive == 3">{{ $L('已抢完') }}</view>
						<view class="coupon_pre_right" v-if="item.isReceive == 2">{{ $L('已领') }}</view>
						<view class="coupon_pre_right" v-if="item.isReceive == 1" @click="goReceive(item)">
							{{ $L('立即领取') }}
						</view>
					</view>
					<view class="coupon_rules" v-if="item.isOpen == true">
						<view class="coupon_rules_title">{{ $L('使用规则:') }}{{ item.description }}</view>
					</view>
					<view class="coupon_type">{{ item.couponTypeValue }}</view>
					<view class="coupon_progress" v-if="item.isReceive != 3">
						{{ $L('已抢') }}{{ item.receivePercent }}%
						<view class="progress_con">
							<progress :percent="item.receivePercent" stroke-width="3" activeColor="#FFFFFF"
								:backgroundColor="diyStyle_var['--color_coupon_main']" border-radius="2px" />
						</view>
					</view>
				</view>
			</scroll-view>
			<!-- 无优惠券 -->
			<view class="empty_coupon_wrap" v-else>
				<image :src="imgUrl + 'no_coupon.png'" mode="aspectFit" class="empty_coupon_img"></image>
				<view class="empty_coupon_text">{{ $L('该店铺暂无优惠券~') }}</view>
			</view>
		</view>
	</BottomPop>
</template>

<script>
	import { mapState } from 'vuex'
	import BottomPop from '../BottomPop.vue'
	import filters from '@/utils/filter.js'
	export default {
		components: {
			BottomPop
		},
		
		computed: {
			...mapState(['hasLogin']),
		},
		data(){
			return{
				imgUrl:process.env.VUE_APP_IMG_URL,
				goReceiveBg: process.env.VUE_APP_IMG_URL + 'coupon/coupon_pre_bg.png', //立即领取背景
				finishReceiveBg: process.env.VUE_APP_IMG_URL + 'coupon/finishReceiveBg.png', //已抢完背景
				hasReceiveBg: process.env.VUE_APP_IMG_URL + 'coupon/hasReceiveBg.png', //已领取背景
				coupon_list:[],
				filters
			}
		},
		
		methods:{
			//立即领取
			goReceive(item) {
				let couponId = item.couponId
				let param = {}
				param.url = 'v3/promotion/front/coupon/receiveCoupon'
				param.method = 'GET'
				param.data = {}
				param.data.couponId = couponId
				this.$request(param).then((res) => {
						if (res.state == 200) {
							this.$refs.couponPopup.close()
							let result = res.data
							this.$api.msg(this.$L('领取成功!'))
							this.getShopCoupon(item.storeId)
						} else {
							this.$api.msg(res.msg)
							this.getShopCoupon(item.storeId)
						}
					})
			},
			
			//规则展开
			descriptionOpen(couponId) {
				this.coupon_list.map((item) => {
					if (item.couponId == couponId) {
						if (item.description != '') {
							item.isOpen = !item.isOpen
							this.$forceUpdate()
						}
					}
				})
			},
			
			
			open(storeId){
				this.$refs.couponPopup.open()
				this.getShopCoupon(storeId)
			},
			
			
			// 打开优惠券弹框,获取店铺优惠券列表
			getShopCoupon(storeId) {
				if (this.hasLogin) {
					let param = {}
					param.url =
						'v3/promotion/front/coupon/storeCouponList?storeId=' + storeId
					param.method = 'GET'
					this.$request(param).then((res) => {
						if (res.state == 200) {
							this.coupon_list = res.data.list
							this.coupon_list.forEach((item, index) => {
								item.isOpen = false
								if (item.isReceive == 3) {
									item.couponBg = this.finishReceiveBg
								}
								if (item.isReceive == 2) {
									item.couponBg = this.hasReceiveBg
								}
								if (item.isReceive == 1) {
									item.couponBg = this.goReceiveBg
								}
							})
						}
					})
				}
			}
		}
	}
</script>

<style lang="scss">
	/* 优惠券弹框 start */
	.coupon_model {
		width: 100%;
		height: 468px;
		background: #f5f5f5;
		border-radius: 8px 8px 0 0;
		
		.coupon_model_list {
			box-sizing: border-box;
			height: 880rpx;
			width: 750rpx;
			overflow-x: hidden;
			padding: 20rpx 20rpx 0;
			box-sizing: border-box;
		}

		.my_coupon_pre {
			margin-bottom: 20rpx;
			position: relative;

			.coupon_pre_top {
				width: 710rpx;
				height: 190rpx;
				background-size: 100% 100%;
				display: flex;
				align-items: center;
				position: relative;

				.coupon_pre_top_bg_img {
					position: absolute;
					left: 0;
					top: 0;
					width: 585rpx;
					height: 190rpx;
				}

				.to_youhuiquan {
					position: absolute;
					right: -26rpx;
					top: 0;
				}

				.coupon_pre_left {
					position: relative;
					display: flex;
					flex-direction: column;
					width: 203rpx;
					align-items: center;

					.coupon_pre_price {
						font-size: 20rpx;
						font-family: Source Han Sans CN;
						font-weight: bold;
						color: var(--color_coupon_main);
						line-height: 31rpx;
						display: flex;
						align-items: baseline;

						text:nth-child(2) {
							font-size: 48rpx;
							font-family: Source Han Sans CN;
							font-weight: bold;
							color: var(--color_coupon_main);
							line-height: 31rpx;
						}

						.price_int {
							text-align: center;
							word-break: break-all;
						}
					}

					.coupon_pre_price_high {
						position: relative;
						left: 2rpx;
						top: 14rpx;
						margin-top: 8rpx;

						text:nth-child(2) {
							line-height: 40rpx;
						}
					}

					.coupon_pre_active {
						font-size: 24rpx;
						font-family: Source Han Sans CN;
						font-weight: 400;
						color: var(--color_coupon_main);
						line-height: 31rpx;
						text-align: center;
						margin-top: 20rpx;
					}
				}

				.coupon_pre_cen {
					position: relative;
					display: felx;
					flex-direction: column;
					flex: 1;
					padding-left: 44rpx;

					.coupon_pre_title {
						font-size: 30rpx;
						font-family: PingFang SC;
						font-weight: bold;
						color: #111111;
						line-height: 31rpx;
					}

					.coupon_pre_time {
						font-size: 24rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #333333;
						line-height: 31rpx;
						margin: 21rpx 0 17rpx;
					}

					.coupon_pre_rules {
						display: flex;
						align-items: center;

						text {
							font-size: 24rpx;
							font-family: PingFang SC;
							font-weight: 500;
							color: #999999;
							line-height: 31rpx;
						}

						image {
							width: 12rpx;
							height: 7rpx;
							margin-left: 20rpx;
						}
					}
				}

				.coupon_pre_right {
					position: relative;
					width: 130rpx;
					box-sizing: border-box;
					font-size: 24rpx;
					font-family: Source Han Sans CN;
					font-weight: 400;
					color: #ffffff;
					text-align: center;
				}
			}

			.coupon_rules {
				width: 710rpx;
				padding: 20rpx 43rpx;
				box-sizing: border-box;
				font-size: 22rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #666666;
				line-height: 30rpx;
				background: #ffffff;
				border-top: 1rpx solid #f2f2f2;
				border-radius: 0 0 15rpx 15rpx;

				.coupon_rules_title {
					margin-bottom: 10rpx;
				}
			}

			.coupon_type {
				position: absolute;
				top: 0;
				left: 0;
				padding: 0 5rpx;
				height: 30rpx;
				background: var(--color_coupon_main);
				font-size: 20rpx;
				font-family: Source Han Sans CN;
				font-weight: 400;
				color: #ffffff;
				line-height: 30rpx;
				text-align: center;
				border-radius: 15rpx 0 15rpx 0;
			}

			.coupon_progress {
				position: absolute;
				width: 130rpx;
				top: 10rpx;
				right: 0rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				font-size: 18rpx;
				font-family: Source Han Sans CN;
				font-weight: 400;
				color: #ffffff;
				line-height: 31rpx;

				.progress_con {
					width: 84rpx;
					margin-top: 5rpx;
					border-radius: 5rpx;

					progress {
						border: 1rpx solid #ffffff;
						border-radius: 5rpx;
					}
				}
			}
		}


	}

	/* 空优惠券 */
	.empty_coupon_wrap {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		padding-top: 260rpx;
		box-sizing: border-box;

		.empty_coupon_img {
			width: 380rpx;
			height: 280rpx;
			margin-bottom: 30rpx;
		}

		.empty_coupon_text {
			font-size: 26rpx;
			color: #999;
		}
	}

	/* 优惠券弹框 end */
</style>