<template>
	<view>
		<view class="cart-item" @longpress="showOperate">
			<!-- 长按商品出现蒙层及操作 start -->
			<view v-if="is_show_mask" class="mask flex_row_center_center">
				<view class="move flex_row_center_center" @click.stop="operateCartGoods('move')" v-if="mode!='o2o'">
					{{ $L('移入收藏夹') }}
				</view>
				<view class="del flex_row_center_center" @click.stop="operateCartGoods('del')">
					{{ $L('删除商品') }}
				</view>
				<text class="iconfont iconziyuan51" @click="is_show_mask=false"></text>
			</view>
			<!-- 长按商品出现蒙层及操作 end -->

			<view class="image-wrapper flex_row_start_center">
				<text class="stock_not_icon iconfont iconziyuan43" v-if="cartInfo.productState == 3" />
				<view @click="changeSelectState" style="height: 100%;display: flex;align-items: center;" v-else>
					<text class="iconfont" :class="{
						  color_main:mode=='normal'&&cartInfo.isChecked,
						  color_o2o_main:mode=='o2o'&&cartInfo.isChecked,
						  iconziyuan33: cartInfo.isChecked,
						  item_check: cartInfo.isChecked,
						  iconziyuan43: !cartInfo.isChecked
					  }" />
				</view>
				<view class="goods-img" :style="{ backgroundImage: 'url(' + cartInfo.productImage + ')' }"
					@click.stop="goGoodsDetail(cartInfo.productId, cartInfo.goodsId)"></view>
			</view>

			<view class="item-right">
				<view class="right_top" @click.stop="goGoodsDetail(cartInfo.productId, cartInfo.goodsId)">
					<text
						:class="cartInfo.productState == 3 ? 'title stock_not_enough' : 'title'">{{ cartInfo.goodsName}}</text>
					<text class="attr" v-if="cartInfo.specInfo">{{ cartInfo.specInfo }}</text>
				</view>
				<view class="goods_spec">{{ cartInfo.specValues }}</view>
				<view class="right_bottom flex_row_between_center">
					<view class="right_bottom_left">
						<view
							v-if="cartInfo.promotionList && cartInfo.promotionList.length > 0"
							class="trade_promotion_btn flex_row_center_center"
							:class="mode"
							:style="{borderColor:promotionBorder}" 
							@click="showPromotionBox"
						>
							<text>{{$L('换促销')}}</text>
							<text class="iconfont iconziyuan11"></text>
						</view>
						<view class="price_wrap"
							:class="{price_wrap_hide:!isShowBigNumBox,price_wrap_super:mode=='normal'&&cartInfo.isSupper}">
							<text class="unit"
								:class="{color_price:mode=='normal',color_o2o_price:mode=='o2o'}">¥</text>
							<text class="price_int"
								:class="{color_price:mode=='normal',color_o2o_price:mode=='o2o'}">{{$getPartNumber(cartInfo.productPrice,'int')}}</text>
							<text class="price_decimal"
								:class="{color_price:mode=='normal',color_o2o_price:mode=='o2o'}">
								<text>{{$getPartNumber(cartInfo.productPrice,'decimal')}}</text>
								<text class="price_super_img"
									:style="'background-image:url('+imgUrl+'super/super_price.png)'"
									v-if="cartInfo.isSupper&&parseInt(cartInfo.productPrice)<1000">
									会员价
								</text>
							</text>
						</view>
					</view>
					<view :class="cartInfo.productState == 3 ? 'goods_num_wrap' : 'exceed_price_wrap'">
						<view class="goods_num_box" v-if="isShowBigNumBox" @click="changeBuyNum">
							*{{ cartInfo.buyNum }}
						</view>
						<block v-else>
							<uni-number-box :disabled="editFlag" @change="changeNum" :min="1" :max="numberMax"
								:value="cartInfo.buyNum" :resctritTip="{
								min:this.$L('商品不能再减少了'),
								max:this.$L('超过购买上限'),
							}" />

						</block>
						<block v-if="isShowBigNumBox">
							<image :src="closeImg" mode="aspectFit" class="close_img" @click="changeBuyNum">
							</image>
						</block>

						<view class="stock_not" v-if="cartInfo.productState == 3">
							{{ $L('库存不足') }}
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 换促销弹框 -->
		<uni-popup ref="popup" type="bottom" @touchmove.stop.prevent="moveHandle">
			<view class="promotion_box">
				<view class="promotion_goods_wrap">
					<view class="promotion_goods_img_wrap">
						<image :src="cartInfo.productImage" mode="aspectFit" class="promotion_goods_img"></image>
					</view>
					<view class="promotion_goods_right">
						<view class="promotion_goods_price">
							<view class="promotion_goods_price_now">
								<text class="small_price">￥</text>
								<text class="big_price">{{ $getPartNumber(cartInfo.productPrice, 'int') }}</text>
								<text class="small_price">{{ $getPartNumber(cartInfo.productPrice, 'decimal') }}</text>
							</view>
						</view>
						<view class="promotion_goods_spec">
							{{ $L('已选：') }}{{ cartInfo.specValues == '' ? $L('默认规格') : cartInfo.specValues }}
						</view>
						<image :src="imgUrl + 'close2.png'" mode="aspectFit" class="close_icon"
							@click="closePromotionBox">
						</image>
					</view>
				</view>
				<scroll-view class="promotion_rules_wrap" scroll-y="true">
					<view class="promotion_rules_title">{{ $L('修改促销') }}</view>
					<view class="promotion_rule_item" v-for="(item2, index2) in promotion_list" :key="index2"
						@click="changePromotion(index2, item2)">
						<text class="iconfont" :class="{ item_check: true, iconfont: true, iconziyuan33: true }"
							v-if="goodsPromotionId == item2.goodsPromotionId"></text>
						<text :class="{ iconfont: true, iconziyuan43: true }" v-else></text>
						<view class="promotion_text">
							<jyfParser :html="item2.promotionDesParsed" :isAll="true"></jyfParser>
						</view>
					</view>
				</scroll-view>
				<view class="confirm_btn_wrap">
					<view class="confirm_btn" @click="confirmChangePromotion">{{ $L('确定') }}</view>
				</view>
			</view>
		</uni-popup>
		<!-- ss -->

	</view>
	<!-- ss -->
</template>
<script>
	import {
		mapState
	} from 'vuex'

	import uniNumberBox from '@/components/uni-number-box/uni-number-box.vue'
	import uniPopup from '@/components/uni-popup/uni-popup.vue'
	import jyfParser from '@/components/jyf-parser/jyf-parser.vue'
	const operateApi = {
		move: 'v3/business/front/cart/moveToCollection',
		del: 'v3/business/front/cart/deleteCarts'
	}

	let storeAntiShakeTimer = null
	import {
		set16ToRgb
	} from '@/diyStyle/index.js'

	const imgUrl = process.env.VUE_APP_IMG_URL
	export default {
		props: {
			cartInfo: {
				type: Object,
				default: () => {}
			},
			editFlag: Boolean,
			mode: {
				type: String,
				default: 'normal'
			}
		},
		components: {
			uniNumberBox,
			uniPopup,
			jyfParser
		},
		data() {
			return {
				imgUrl:imgUrl,
				is_show_mask: false,
				closeImg: imgUrl + 'recharge_fail.png',
				isShowBigNumBox: false,
				promotion_list: [],
				goodsPromotionId: -1
			}
		},
		computed: {
			...mapState(['hasLogin']),
			numberMax() {
				return Math.min(this.cartInfo.productStock, 99999)
			},
			promotionBorder(){
				const diyStyle = this.diyStyle_var
				let target = this.mode=='normal'?'--color_main':'--color_o2o_main'
				return 	set16ToRgb(diyStyle[target], 0.5)
			}
		},

		created() {
			let price = this.$getPartNumber(this.cartInfo.productPrice, 'int')
			if (price.toString().length > 5) {
				this.isShowBigNumBox = true
			}
		},

		mounted() {
			
		},
		
		
		watch:{
			cartInfo(){
				
			}
		},

		methods: {

			// 关闭换促销弹框
			closePromotionBox() {
				this.$refs.popup.close()
			},

			//商品长按事件
			showOperate(cartId, productId) {
				if (this.editFlag) {
					return
				}
				this.is_show_mask = true
			},


			/*
			 *操作商品事件
			 * type:move移入收藏夹，del删除商品
			 */
			operateCartGoods(type) {

				if (this.editFlag) {
					return
				}

				if (!this.hasLogin) {
					this.$emit('done', type, this.cartInfo)
					return
				}
				
				let data = {
					cartIds: this.cartInfo.cartId
				}
				
				if(this.mode=='o2o'){
					data.storeId = this.cartInfo.storeId
				}

				this.$request({
					url: operateApi[type],
					method: 'post',
					data
				}).then((res) => {
					if (res.state == 200) {
						this.$api.msg(res.msg, 'success')
						this.$emit('done', type, res.data)
						this.is_show_mask = false
					}
				})
			},


			changeNumApi(number) {
				if (!this.hasLogin) {
					this.$emit('done', 'changeNum', this.cartInfo)
					return
				}

				if (this.hasLogin) { //登录
					let param = {}
					param.url = 'v3/business/front/cart/changeNum'
					param.method = 'POST'
					param.data = {
						cartId: this.cartInfo.cartId,
						number
					}
					if (this.mode == 'o2o') {
						param.data.storeId = 0;
					}
					this.$request(param).then(res => {
						if (res.state == 200) {
							this.$emit('done', 'changeNum', res.data)
						} else {
							this.$api.msg(res.msg);
							// this.changeNumApi(1)
						}
					})
				}
			},


			changeNum(type, num) {
				let maxMin = Math.min(this.cartInfo.productStock, 99999)
				if (storeAntiShakeTimer) {
					clearTimeout(storeAntiShakeTimer)
				}
				storeAntiShakeTimer = setTimeout(() => {
					this.changeNumApi(num)
				}, 450);
				this.$forceUpdate()
			},

			// 点击商品、状态处理
			changeSelectState() {

				if (this.editFlag) {
					return
				}

				if (!this.hasLogin) {
					this.$emit('done', 'select_state', {subType:'goods',...this.cartInfo})
					return
				}

				let param = {}
				param.url = 'v3/business/front/cart/checkedCarts'
				param.method = 'POST'
				param.data = {};
				param.data.cartIds = this.cartInfo.cartId;
				param.data.checked = this.cartInfo.isChecked == 1 ? 0 : 1;
				if (this.mode == 'o2o') {
					param.data.storeId = 0;
				}
				this.$request(param).then(res => {
					if (res.state == 200) {
						this.$emit('done', 'select_state', res.data)
					}
				})
			},

			changeBuyNum() {
				if (this.editFlag) {
					return
				}
				this.isShowBigNumBox = !this.isShowBigNumBox
			},

			// 打开换促销弹框
			showPromotionBox() {
				let {
					productImage,
					productPrice,
					specValues,
					promotionId,
					promotionList,
					promotionType
				} = this.cartInfo

				let findProIm = promotionList.findIndex(
					(proIm) =>
					proIm.promotionId == promotionId &&
					proIm.promotionType == promotionType
				)

				if (findProIm > -1) {
					this.goodsPromotionId = promotionList[findProIm].goodsPromotionId
				} else {
					this.goodsPromotionId = ''
				}

				this.promotionType = promotionType
				promotionList.map(i => {
					i.promotionDesParsed = i.promotionDes.replace(/<(.+?)>/g, (num) => (
						"<text style='color:var(--color_main)'>" +
						num.slice(1, num.length - 1) +
						'</text>'
					))
					i.promotionDesParsed = i.promotionDes.replace(/x[\d]/g, (num) => (
						`<text style='color:var(--color_main)'>${num}</text>`
					))
				})
				this.promotion_list = promotionList
				this.$refs.popup.open()
			},

			// 修改促销活动
			changePromotion(index, {
				promotionDes,
				goodsPromotionId,
				promotionType,
				promotionId
			}) {
				this.promotionDes = promotionDes
				this.promotionType = promotionType
				this.promotionId = promotionId
				this.goodsPromotionId = goodsPromotionId
			},


			// 确定修改促销活动
			confirmChangePromotion() {
				let param = {}
				param.url = 'v3/business/front/cart/changePromotion'
				param.method = 'POST'
				param.data = {
					cartId: this.cartInfo.cartId,
					promotionDescription: this.promotionDes,
					promotionId: this.promotionId,
					promotionType: this.promotionType
				}
				if (this.mode == 'o2o') {
					param.data.storeId = 0;
				}
				this.$request(param).then((res) => {
					if (res.state == 200) {
						uni.showToast({
							title: this.$L('修改成功！'),
							icon: 'none'
						})
						this.$emit('done', 'changePromotion', res.data)
						this.$refs.popup.close()
						this.$forceUpdate()
					}
				})
			},

			//进入商品详情页
			goGoodsDetail(productId, goodsId) {
				this.$Router.push({
					path: '/standard/product/detail',
					query: {
						productId,
						goodsId
					}
				})
			},

		},
	}
</script>
<style lang="scss">
	page {}

	.iconfont {
		font-size: 34rpx;
		color: #bbbbbb;
	}

	.color_main {
		color: var(--color_main) !important;
	}

	.color_o2o_main {
		color: var(--color_o2o_main) !important;
	}

	.color_price {
		color: var(--color_price) !important;
	}

	.color_o2o_price {
		color: var(--color_o2o_price) !important;
	}

	.close_img {
		width: 36rpx;
		height: 36rpx;
		margin-left: 6rpx;
	}

	.promotion_box {
		width: 750rpx;
		height: 900rpx;
		background-color: #fff;
		border-radius: 15rpx 15rpx 0 0;
		padding: 0 20rpx;
		box-sizing: border-box;

		.promotion_goods_wrap {
			border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
			display: flex;
			padding: 20rpx 0;

			.promotion_goods_img_wrap {
				width: 180rpx;
				height: 180rpx;
				border-radius: 15rpx;
				margin-right: 20rpx;
				background-color: #eee;

				.promotion_goods_img {
					width: 180rpx;
					height: 180rpx;
					border-radius: 15rpx;
				}
			}

			.promotion_goods_right {
				width: 100%;
				position: relative;

				.promotion_goods_price {
					display: flex;
					align-items: flex-end;

					.promotion_goods_price_now {
						color: var(--color_price);
						font-weight: bold;
						margin-right: 20rpx;

						.small_price {
							font-size: 24rpx;
						}

						.big_price {
							font-size: 34rpx;
						}
					}

					.promotion_goods_price_old {
						font-size: 28rpx;
						color: #999;
						text-decoration: line-through;
					}
				}

				.promotion_goods_spec {
					font-size: 28rpx;
					color: #333;
					text-overflow: -o-ellipsis-lastline;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					line-clamp: 2;
					-webkit-box-orient: vertical;
					position: absolute;
					left: 0;
					bottom: 20rpx;
				}

				.close_icon {
					width: 30rpx;
					height: 30rpx;
					position: absolute;
					right: 0;
					top: 0;
				}
			}
		}

		.promotion_rules_wrap {
			height: 472rpx;
			overflow-y: scroll;

			.promotion_rules_title {
				margin-top: 30rpx;
				font-size: 32rpx;
				color: #333;
				font-weight: bold;
			}

			.promotion_rule_item {
				margin-top: 45rpx;
				display: flex;
				align-items: center;

				.promotion_text {
					width: 100%;
					overflow: hidden;
					display: inline-block;
					font-size: 28rpx;
					color: #333;
					margin-left: 24rpx;
				}
			}
		}

		.confirm_btn_wrap {
			width: 750rpx;
			position: fixed;
			/* #ifdef H5 */
			bottom: calc(100rpx + env(safe-area-inset-bottom));
			/* #endif */
			/* #ifdef MP||APP-PLUS */
			bottom: 0;
			/* #endif */

			left: 0;
			height: 100rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			box-shadow: 0px 0px 20px 0px rgba(86, 86, 86, 0.08);

			.confirm_btn {
				width: 690rpx;
				height: 70rpx;
				color: #fff;
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 28rpx;
				background: var(--color_main);
				border-radius: 35rpx;
			}
		}
	}

	// ss

	/* 购物车列表项 */
	.cart-item {
		display: flex;
		position: relative;
		height: 200rpx;
		margin-top: 20rpx;

		.mask {
			position: absolute;
			z-index: 4;
			left: 0;
			right: 0;
			top: 0;
			bottom: 0;
			background: rgba(0, 0, 0, 0.6);
			border-radius: 15rpx;
			gap: 90rpx;

			.iconziyuan51 {
				font-size: 26rpx;
				position: absolute;
				top: 20rpx;
				right: 20rpx;
				color: #fff;
			}

			.move,
			.del {
				width: 200rpx;
				height: 80rpx;
				border-radius: 40rpx;
				color: #fff;
				font-size: 28rpx;
			}

			.move {
				background: var(--color_vice_bg);
			}

			.del {
				background: var(--color_main_bg);
			}
		}

		.image-wrapper {
			flex-shrink: 0;
			position: relative;
			background-color: #fff;

			.goods-img {
				background-size: contain;
				background-position: center center;
				background-repeat: no-repeat;
				width: 200rpx;
				height: 200rpx;
				overflow: hidden;
				background-color: #f8f6f7;
				border-radius: 14rpx;
				margin-left: 20rpx;
			}
		}

		.checkbox {
			position: absolute;
			left: -16rpx;
			top: -16rpx;
			z-index: 8;
			font-size: 44rpx;
			line-height: 1;
			padding: 4rpx;
			color: $font-color-disabled;
			background: #fff;
			border-radius: 50px;
		}

		.item-right {
			flex: 1;
			overflow: hidden;
			position: relative;
			padding-left: 20rpx;
			box-sizing: border-box;
			display: flex;
			flex-direction: column;
			justify-content: space-between;

			.right_top {}

			.goods_spec {
				font-size: 24rpx;
				color: #999999;
				/* overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap; */
			}

			.title {
				color: $main-font-color;
				font-weight: bold;
				font-size: 28rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
				word-break: break-word;
				line-height: 36rpx;
			}

			.attr {
				font-size: 24rpx;
				line-height: 28rpx;
				color: $main-third-color;
				background-color: #f8f8f8;
				padding: 3rpx 7rpx;
				border-radius: 6rpx;
			}

			.price {
				height: 50rpx;
				line-height: 50rpx;
			}

			.right_bottom {

				.right_bottom_left {
					.trade_promotion_btn {
						margin: 4rpx 0;
						width: 128rpx;
						height: 38rpx;
						border-radius: 6rpx 6rpx 6rpx 6rpx;
						text-align: center;
						border-width: 1px;
						border-style: solid;
						
						&.normal{
							color: var(--color_price);
						}
						
						&.o2o{
							color: var(--color_o2o_price);
						}
						
						
						text {
							font-size: 22rpx;
							font-family: PingFang SC;
							font-weight: 500;
						}
						
						.iconziyuan11{
							display: block;
							margin-left: 10rpx;
							margin-top: 2rpx;
							font-size: 20rpx;
							color: inherit;
							transform: rotate(90deg);
						}

					}

					.price_wrap {

						height: 50rpx;

						&.price_wrap_hide {
							width: 223rpx;
							white-space: nowrap;
							text-overflow: ellipsis;
							overflow: hidden;
						}

						&.price_wrap_super {

							.unit,
							.price_decimal,
							.price_int {
								color: #242846 !important;
							}
						}

						.unit,
						.price_decimal {
							font-size: 24rpx;
							font-weight: 600;
							color: var(--color_price);
						}

						.price_int {
							font-weight: 600;
							font-size: 34rpx;
							color: var(--color_price);
						}

						.price_decimal {
							position: relative;

							.price_super_img {
								position: absolute;
								bottom: 0rpx;
								display: inline-block;
								width: 102rpx;
								height: 34rpx;
								line-height: 36rpx;
								color: #CFB295;
								font-size: 20rpx;
								font-family: PingFang SC;
								font-weight: 500;
								text-align: center;
								text-indent: 6rpx;
								background-position: center;
								background-repeat: no-repeat;
								background-size: cover;
								margin-left: 6rpx;
							}
						}
					}
				}

				.goods_num_box {
					height: 50rpx;
					font-size: 24rpx;
					font-weight: 600;
					text-align: center;
					line-height: 50rpx;
					color: #2d2d2d;
					border: 1px solid #f2f2f2;
					border-radius: 6px;
					padding: 0 15rpx;
					margin-right: 20rpx;
				}
			}
		}

		.del-btn {
			padding: 4rpx 10rpx;
			font-size: 34rpx;
			height: 50rpx;
			color: $font-color-light;
		}
	}
	
</style>
<style lang="scss" scoped>
	.stock_not {
		text-align: right;
		font-size: 24rpx;
		color: #ff0d24;
		margin-right: 20rpx;
		margin-top: 8rpx;
	}
	
	.stock_not_enough {
		color: #666666;
	}
	
	.stock_not_icon {
		color: #eeeeee;
	}
</style>