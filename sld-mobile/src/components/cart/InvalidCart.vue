<template>
	<!-- 失效购物车列表 -->
	<view class="invalid_list_wrap" v-if="invalidList.length > 0">

		<view class="invalid_list_title_con" :class="mode">
			<view class="invalid_list_title">
				<text class="invalid_count" :class="mode">{{ $L('失效商品') }}{{ invalidList.length }}{{ $L('件') }}</text>
				<text class="invalid_clear" :class="mode" @click="clearFailureGoods('open')"
					v-if="mode=='normal'">{{ $L('清空失效商品') }}</text>
				<view class="flex_row_between_center" @click="clearFailureGoods('open')" v-else>
					<!-- <image :src="imgUrl+'del_search.png'" mode=""></image> -->
					<text class="item_del iconfont iconshanchu-copy-copy-copy" />
					<text class="invalid_clear" :class="mode">清空失效商品</text>
				</view>
			</view>
		</view>


		<view class="invalid_list_content" :class="{'invalid_list_content_o2o':o2oType}">
			<view class="invalid_list_item" v-for="(item2, index2) in invalidList" :key="index2">
				<text class="stock_not_icon iconfont iconziyuan43"></text>
				<view class="invalid_img">
					<view class="invalid_icon">{{ $L('失效') }}</view>
					<image :src="item2.productImage" mode="aspectFit"></image>
				</view>
				<view class="invalid_goods_wrap">
					<view class="invalid_goods_name">
						{{ item2.goodsName }}
					</view>
					<text class="invalid_goods_spec" v-if="item2.specValues">{{ item2.specValues }}</text>
					<!-- <view class="invalid_goods_text">{{$L('商品已下架')}}</view> -->
				</view>
			</view>
		</view>


		<uni-popup ref="clearPopup" type="dialog">
			<uni-popup-dialog type="input" :title="$L('提示')" :content="$L('确定清空所有失效商品?')" :duration="2000"
				@confirm="clearFailureGoods('confirm')"></uni-popup-dialog>
		</uni-popup>
	</view>
</template>

<script>
	import uniPopup from '@/components/uni-popup/uni-popup.vue'
	import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue'
	export default {
		components: {
			uniPopup,
			uniPopupDialog,
		},
		props: {
			invalidList: {
				type: Array,
				default: () => []
			},
			mode: {
				type: String,
				default: 'normal'
			},
      o2oType: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				imgUrl: process.env.VUE_APP_IMG_URL,
			}
		},
		methods: {
			// 清空失效商品
			clearFailureGoods(type) {
				switch (type) {
					case 'open': {
						this.$refs.clearPopup.open()
						break
					}
					case 'confirm': {
						this.$refs.clearPopup.close()
						let _this = this
						let param = {}
						param.url = 'v3/business/front/cart/emptyInvalid'
						param.method = 'POST'
						param.data = {};

						let cartIds = [];
						this.invalidList.map(item => {
							cartIds.push(item.cartId);
						})
						param.data.cartIds = cartIds.join(',');

						if (_this.mode == 'o2o') {
							param.data.storeId = 0;
						}
						_this.$request(param).then((res) => {
							if (res.state == 200) {
								this.$emit('clear')
								uni.showToast({
									title: this.$L('清除成功！')
								})
							}
						})
						break
					}
				}
			},
		}
	}
</script>

<style lang="scss">
	.invalid_list_wrap {
		background-color: #fff;
		margin-top: 20rpx;
		border-radius: 15rpx;
		// padding: 0 20rpx;

		.invalid_list_title_con {

			&.o2o {
				padding-left: 58rpx;
			}

			.invalid_list_title {
				width: 100%;
				height: 80rpx;
				font-weight: 600;
				display: flex;
				align-items: center;
				justify-content: space-between;
				border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
				padding: 0 20rpx;

				.invalid_count {
					&.o2o {
						font-size: 26rpx;
						color: #666666;
					}

					&.normal {
						font-size: 30rpx;
						color: #333;
					}
				}

				.item_del {
					font-size: 28rpx;
					color: #666;
					font-weight: 400;
				}

				image {
					width: 36rpx;
					height: 36rpx;
				}

				.invalid_clear {
					&.o2o {
						font-size: 26rpx;
						color: #888888;
						font-weight: 400;
						margin-left: 10rpx;
					}

					&.normal {
						font-size: 24rpx;
						color: var(--color_main);
					}
				}
			}
		}

		.invalid_list_content {
			width: 100%;
			padding: 0 20rpx;
			padding-bottom: 20rpx; 
			.invalid_list_item {
				padding-top: 20rpx;
				display: flex;
				align-items: center;

				.stock_not_icon {
					color: #BDBDBD;
					margin-right: 20rpx;
          font-size: 34rpx;
					opacity: 0.4;
					background: #EFEFEF;
					border-radius: 50%;
				}

				.invalid_img {
					margin-right: 20rpx;
					position: relative;

					.invalid_icon {
						position: absolute;
						top: 50%;
						left: 50%;
						transform: translate(-50%, -50%);
						width: 120rpx;
						height: 120rpx;
						background: rgba(0, 0, 0, 0.55);
						line-height: 120rpx;
						text-align: center;
						font-size: 28rpx;
						color: #FFFFFF;
						z-index: 6;
						border-radius: 50%;
					}

					image {
						width: 200rpx;
						height: 200rpx;
						border-radius: 14rpx;
					}
				}

				.invalid_goods_wrap {
					width: 100%;
					height: 200rpx;
					display: flex;
					flex-direction: column;
					position: relative;

					.invalid_goods_name {
						font-size: 28rpx;
						/* height:80rpx; */
						color: rgba(51, 51, 51, 0.4);
						font-weight: 600;
						text-overflow: -o-ellipsis-lastline;
						overflow: hidden;
						text-overflow: ellipsis;
						word-break: break-all;
						display: -webkit-box;
						-webkit-line-clamp: 2;
						-webkit-box-orient: vertical;
					}

					.invalid_goods_text {
						position: absolute;
						bottom: 28rpx;
						font-size: 24rpx;
						color: #333;
						font-weight: 600;
					}

					.invalid_goods_spec {
						font-size: 24rpx;
						color: #999999;
						margin-top: 10rpx;
					}
				}
			}
		}
    .invalid_list_content_o2o{
      padding-left: 0px;
      padding-right: 0px;
    }
	}

	// ss
</style>