<template>
  <view class="uni-navbar" :class="{ 'uni-navbar--fixed': fixedTop }"
    :style="{ height:navHeight ? navHeight + 'px': 'auto' }">
    <view :class="{ 'uni-navbar--fixed': fixed, 'uni-navbar--shadow': false, 'uni-navbar--border': false }"
      :style="{ 'backgroundColor': backgroundColor }" class="uni-navbar__content">
      <uni-status-bar v-if="statusBar" />
      <view :style="{ color,backgroundColor,height:height + 'px' }" class="uni-navbar__header uni-navbar__content_view"
        :class="{'header_position':headerPosition}">
        <view @tap="onClickLeft" class="uni-navbar__header-btns uni-navbar__content_view"
          :class="{'uni-navbar__header-btns-left':mode=='center','uni-navbar__header-btns-left_withTitle':mode=='left'}">
          <view class="uni-navbar__content_view" v-if="leftIcon.length">
            <uni-icons :color="color" :type="leftIcon" size="24" />
          </view>
          <view :class="{ 'uni-navbar-btn-icon-left': !leftIcon.length }"
            class="uni-navbar-btn-text uni-navbar__content_view" v-if="leftText.length">
            <text :style="{ color: color, fontSize: leftFontSize }">{{ leftText }}</text>
          </view>
          <slot name="left" />
        </view>
        <view class="uni-navbar__header-container uni-navbar__content_view">
          <view class="uni-navbar__header-container-inner uni-navbar__content_view" v-if="title.length">
            <text class="uni-nav-bar-text" :style="{color: color }">{{ title }}</text>
          </view>
          <!-- 标题插槽 -->
          <slot />
        </view>
        <view :class="title.length ? 'uni-navbar__header-btns-right' : ''" @tap="onClickRight"
          class="uni-navbar__header-btns uni-navbar__content_view">
          <view class="uni-navbar__content_view" v-if="showClear && rightIcon.length">
            <uni-icons :color="color" :type="rightIcon" size="24" />
          </view>
          <!-- 优先显示图标 -->
          <view class="uni-navbar-btn-text uni-navbar__content_view" v-if="rightText.length && !rightIcon.length">
            <text class="uni-nav-bar-right-text">{{ rightText }}</text>
          </view>
          <slot name="right" />
        </view>
      </view>
    </view>
    <view class="uni-navbar__placeholder" v-if="fixed">
      <uni-status-bar v-if="statusBar" />
      <view class="uni-navbar__placeholder-view" v-if="placeholderView" />
    </view>
  </view>
</template>

<script>
  import uniStatusBar from "./uni-status-bar.vue";
  import uniIcons from "../uni-icons/uni-icons.vue";

  export default {
    name: "UniNavBar",
    components: {
      uniStatusBar,
      uniIcons
    },
    props: {
      title: {
        type: String,
        default: ""
      },
      leftText: {
        type: String,
        default: ""
      },
      rightText: {
        type: String,
        default: ""
      },
      leftIcon: {
        type: String,
        default: ""
      },
      rightIcon: {
        type: String,
        default: ""
      },
      fixed: {
        type: [Boolean, String],
        default: false
      },
      fixedTop: {
        type: [Boolean, String],
        default: false
      },
      color: {
        type: String,
        default: "#000000"
      },
      backgroundColor: {
        type: String,
        default: "#FFFFFF"
      },
      statusBar: {
        type: [Boolean, String],
        default: false
      },
      shadow: {
        type: [String, Boolean],
        default: false
      },
      border: {
        type: [String, Boolean],
        default: true
      },
      showClear: {
        type: Boolean,
        default: true
      },
      mode: {
        type: String,
        default: "center"
      },

      leftFontSize: {
        type: String,
        default: "14px"
      },
      height: {
        type: Number,
        default: 40
      },
      navHeight: {
        type: Number,
        default: 0
      },
      headerPosition: {
        type: Boolean,
        default: false
      },
      placeholderView: {
        type: Boolean,
        default: true
      }

    },
    mounted() {
      if (uni.report && this.title !== '') {
        uni.report('title', this.title)
      }
    },
    methods: {
      onClickLeft() {
        this.$emit("clickLeft");
      },
      onClickRight() {
        if (this.showClear) {
          this.$emit("clickRight");
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  $nav-height: 40px;

  .uni-nav-bar-text {
    //app-1-start



    //app-1-end

    font-size: 30rpx;

    font-weight: bold;
  }

  .uni-nav-bar-right-text {
    font-size: $uni-font-size-base;
  }

  .uni-navbar {
    width: 750rpx;
  }

  .uni-navbar__content {
    position: relative;
    width: 750rpx;
    background-color: $uni-bg-color;
    overflow: hidden;
  }

  .uni-navbar__content_view {

    display: flex;

    align-items: center;
    flex-direction: row;
    // background-color: #FFFFFF;
  }

  .header_position {
    position: relative;
  }

  .uni-navbar__header {

    display: flex;

    flex-direction: row;
    width: 750rpx;
    height: $nav-height;
    line-height: $nav-height;
    font-size: 16px;
    // background-color: #ffffff;
  }

  .uni-navbar__header-btns {

    display: flex;

    flex-wrap: nowrap;
    width: 150rpx;
    padding: 0 6px;
    justify-content: center;
    align-items: center;
  }

  .uni-navbar__header-btns-left {

    display: flex;

    width: 150rpx;
    justify-content: flex-start;
  }

  .uni-navbar__header-btns-left_withTitle {

    display: flex;

    justify-content: flex-start;
    white-space: nowrap;
  }

  .uni-navbar__header-btns-right {

    display: flex;

    width: 150rpx;
    padding-right: 30rpx;
    justify-content: flex-end;
  }

  .uni-navbar__header-container {
    flex: 1;
  }

  .uni-navbar__header-container-inner {

    display: flex;

    flex: 1;
    align-items: center;
    justify-content: center;
    font-size: $uni-font-size-base;
  }


  .uni-navbar__placeholder-view {
    height: $nav-height;
  }

  .uni-navbar--fixed {
    position: fixed;
    z-index: 998;
    transition: all .3s;
  }

  .uni-navbar--shadow {

    box-shadow: 0 1px 6px #ccc;

  }

  .uni-navbar--border {
    border-bottom-width: 1rpx;
    border-bottom-style: solid;
    border-bottom-color: $uni-border-color;
  }
</style>