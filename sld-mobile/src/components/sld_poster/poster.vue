
<!-- 
	canvas_poster.vue
	用于生成固定模板海报
	zoucb 2023.03.20
	绘制步骤为线性依次绘制，不是同时绘制所有
 -->
<template>
	<view class="content">
		
		<view class="poster">
			<canvas canvas-id="canvas" id="canvas" :style="{width:canvasInfo.tWidth+'px',height:canvasInfo.tHeight+'px'}"></canvas>
		</view>
		
		<!-- #ifdef H5 -->
		<view class="poster_image" @tap="prevImg" id="poster_image">
			<block v-if="imgUrl">
				<block v-if="$isWeiXinBrower()">
					<image :src="imgUrl" mode="aspectFit">
					</image>
				</block>
				<block v-else>
					<canvas class="poster_image">
						<img :src="imgUrl" width="100%" height="100%" style="-webkit-touch-callout: default;"/>
					</canvas>
				</block>
			</block>
			<block v-else>
				<view class="classLoading flex_row_center_center">
					<text class="text_loading">生成中...</text>
				</view>
			</block>
		</view>
		<!-- #endif -->
		
		<!-- #ifndef H5 -->
		<view class="poster_image" @tap="prevImg" >
			<image :src="imgUrl" width="100%" height="100%" style="-webkit-touch-callout: default;" id="poster_image" mode="aspectFit"></image>
		</view>
		<!-- #endif -->
		
		<view class="uqrcode" width="width: 126px;height: 126px;">
			<canvas id="qrcode" canvas-id="qrcode"/>
		</view>
	</view>
</template>

<script>
	import uqrcode from './uqrcode.js'
	export default{
		props:{
			info:{
				type:Object,
				default:()=>{}
			},
			mode:{
				type:String,
				default:'goods'
			}
		},
		data(){
			return{
				//canvas参数
				canvasInfo:{
					maxWidth:414,  // 商品图片宽度改为全宽
					startX:0,      // 商品图片从左边缘开始
					startY:0,      // 商品图片从顶部开始
					tWidth:414,
					tHeight:720,   // 增加高度以适应1:1图片比例 (414px图片 + 306px其他内容)
				},

				//统计的计算高度，数组
				calHeight:[0],  // 从0开始

				imgUrl:'',


				qrCode:''


			}
		},
		
		watch:{
			info:{
				handler(nv,ov){
					this.posterInfo = nv
				},
				deep:true
			}
		},
		
		mounted(){
			this.posterInfo = this.info
			console.log('sldPoster:',this.posterInfo)
			if(this.posterInfo.posterCache){
				this.imgUrl = this.posterInfo.posterCache
				console.log('sldPoster:posterCache',length(this.imgUrl))
				return
			}
			if(this.mode=='tshou_promote'){
				this.canvasInfo.tHeight = 580
			}
			this.iniCanvas()
		},
		
		methods:{
			iniCanvas(){
				this.ctx = uni.createCanvasContext('canvas',this)
				this.drawBackground()
			},
			
			drawBackground(){
				this.ctx.setFillStyle('#ffffff');
				this.ctx.fillRect(0, 0, this.canvasInfo.tWidth, this.canvasInfo.tHeight);
				this.ctx.save()
				if(this.mode=='goods'){
					this.drawImage_main()  // 先绘制商品图片
				}else if(this.mode=='tshou_promote'){
					this.drawImage_main()
				}
			},
			
			setFontSize(size){
				this.ctx.font = `400 ${size}px PingFangSC-Regular, "Microsoft YaHei", Helvetica, "Droid Sans", Arial, sans-serif`
			},
			
			/**
			 * 用于绘制文字
			 * @param 
			 * 	   text: 文本
			 * 	   size: 字号大小
			 * 	   maxWidth: 文本的最大长度
			 *     x、y: 文本所在位置
			 *     isWrap: 是否折行,最大行数2
			 *     noCal: 绘制该文本时，不去统计该文本所在位置的纵轴Y值 
			**/
			drawText(text,size,maxWidth,x,y,line=1,noCal){
				
				const find_wrap_index = (_text)=>{
					//该文本如果长度大于最大宽度(maxWidth有值)的话，则找出超过最大宽度时的那个单字
					let index = _text.split('').findIndex((val,idx)=>{
						let width = this.ctx.measureText(_text.substring(0,idx+1)).width
						return width>=maxWidth
					})
					return index
				}
				
				
				if(maxWidth){
					//如果有传入的文本最大宽度，则需要考虑文本是折行还是省略
					let oringinaltext = text
					let index9 = 0
					let startY = y
					let count = 0
					while(true){
						index9 = find_wrap_index(oringinaltext)
						count++
						if(index9>-1){
							let single = oringinaltext.substring(0,index9)
							if(count==line){
								single = oringinaltext.substring(0,index9-1) + '...'
								this.ctx.fillText(single, x, startY)
								startY+=(size+6)
								break
							}
							oringinaltext = oringinaltext.substring(index9)
							this.ctx.fillText(single, x, startY)
							startY+=(size+6)
						}else{
							this.ctx.fillText(oringinaltext, x, startY)
							startY+=(size+6)
							break
						}
					}
					!noCal&&(this.calHeight.push(startY))
				}else{
					//否则直接绘制文字
					this.ctx.fillText(text, x, y)
					!noCal&&(this.calHeight.push(y+size))
				}
			},
			
			/**
			 * 用于绘制主图，以1:1比例展示
			 * @param
			 *     x、y: 主图所在位置
			 * 	   image:通过 image
			 *     imageHeight: 图片区域高度
			**/
			drawMainImage(image,x,y,width,height,imageHeight = 300){
				const {maxWidth} = this.canvasInfo
				
				// 强制1:1比例显示，使用容器宽度作为正方形边长
				const squareSize = maxWidth
				
				// 计算图片的缩放比例，确保能完全填充正方形区域
				const scaleX = squareSize / width
				const scaleY = squareSize / height
				const scale = Math.max(scaleX, scaleY)  // 使用较大的缩放比例确保填满
				
				// 计算缩放后的图片尺寸
				const scaledWidth = width * scale
				const scaledHeight = height * scale
				
				// 计算居中裁剪的位置
				const offsetX = (scaledWidth - squareSize) / 2
				const offsetY = (scaledHeight - squareSize) / 2
				
				// 绘制参数
				const drawParam = {
					x: x - offsetX,
					y: y - offsetY,
					width: scaledWidth,
					height: scaledHeight
				}
				
				this.calHeight.push(y + squareSize)
				this.ctx.drawImage(image, drawParam.x, drawParam.y, drawParam.width, drawParam.height)
			},
			
			getUid(prefix) {
			    prefix = prefix || '';
			
			    return (
			        prefix +
			        'xxyxxyxx'.replace(/[xy]/g, c => {
			            let r = (Math.random() * 16) | 0;
			            let v = c === 'x' ? r : (r & 0x3) | 0x8;
			            return v.toString(16);
			        })
			    );
			},
			
			
			getBase64Image(base64){
				let uid = Math.random()
				let filename = `${wx.env.USER_DATA_PATH}/${this.getUid()}.png`;
				
				let fileManager = wx.getFileSystemManager();
				
				return new Promise((resolve,reject)=>{
					fileManager.writeFile({
					    filePath: filename,
					    data: base64.replace(/data:image\/(.*);base64,/, ''),
					    encoding: 'base64',
					    success (res) {
							resolve(filename)
					    },
					    fail (res) {
					        reject(res);
					    },
					})
				})
			},
			
			
			/**
			 * 每次绘制时，将绘制的 纵轴y值加上字体或者图片的高度 push进calHeight
			 * 而 calHeightPos 将返回最大值，用于下个绘制时的纵轴Y值计算，
			 * @param 
			 *     pos: 如果有该值则 已这个值的前的数值为区间得出最大值
			 * 			用于 绘制和上一个绘制目标位置平行的 目标
			**/
			calHeightPos(pos){
				if(pos){	 
					return Math.max.apply({},this.calHeight.slice(0,pos))
				}else{
					return Math.max.apply({},this.calHeight)
				}
			},
			
			//绘制商品名
			drawText_name(){
				let {name} = this.posterInfo
				const {maxWidth} = this.canvasInfo
				const startX = 15  // 商品名称左边距
				const startY = this.calHeightPos() + 20  

				this.ctx.setTextBaseline('top')
				this.setFontSize(18)  // 调整字体大小
				this.ctx.setFillStyle('#333333');
				this.drawText(name, 18, maxWidth - 30, startX, startY, 2)  // 左右各留15px边距
				this.ctx.save()
				this.drawText_tip()  // 商品名称后绘制推荐信息
			},
			
			//绘制价格
			drawText_price(){
				this.ctx.setTextBaseline('top')
				let {price,marketPrice,brief} = this.posterInfo
				const startX = 15  // 价格左边距
				const startY = this.calHeightPos() + 70  // 商品图片下方70px

				this.ctx.setFillStyle('#7abc79');  // 橙红色价格
				
				// 检查是否是价格区间格式 (如: ¥19.9-39.9)
				if (price && price.includes('-') && price.startsWith('¥')) {
					// 解析价格区间
					const priceText = price.substring(1) // 去掉¥符号
					
					// 绘制小号¥符号
					this.setFontSize(24)  // ¥符号使用小字体
					this.drawText('¥', 24, null, startX, startY + 8, 1)  // 稍微下移对齐
					const ySymbolWidth = this.ctx.measureText('¥').width
					
					// 绘制价格数字部分
					this.setFontSize(36)  // 数字使用大字体
					this.drawText(priceText, 32, null, startX + ySymbolWidth + 2, startY, 1)  // 2px间距
				} else {
					// 原有逻辑：普通价格显示
					this.setFontSize(36)  // 价格字体加大
					this.drawText(price, 32, null, startX, startY, 1)
				}
				
				this.ctx.save()
				if(marketPrice){
					this.drawText_marketPrice()
				}else{
					this.drawText_name()  // 价格后绘制商品名称
				}
			},
			
			//绘制市场价或者其他划线价格
			drawText_marketPrice(){
				const {price,marketPrice,brief} = this.posterInfo
				const startX = 15  // 与价格对齐
				const {calHeight,calHeightPos} = this
				this.setFontSize(18)  // 市场价字体稍大
				this.ctx.setFillStyle('rgb(148, 148, 148)');

				// 计算价格文本宽度，考虑新的价格区间格式
				let priceWidth = 0
				if (price && price.includes('-') && price.startsWith('¥')) {
					// 价格区间格式：计算¥符号宽度 + 数字部分宽度
					this.setFontSize(24)  // ¥符号字体
					const ySymbolWidth = this.ctx.measureText('¥').width
					
					this.setFontSize(32)  // 数字字体
					const priceText = price.substring(1) // 去掉¥符号
					const numberWidth = this.ctx.measureText(priceText).width
					
					priceWidth = ySymbolWidth + 2 + numberWidth  // 2px间距
				} else {
					// 普通价格格式
					this.setFontSize(32)
					priceWidth = this.ctx.measureText(price).width
				}
				
				this.setFontSize(18)  // 恢复市场价字体

				let position_x = priceWidth + 20 + startX
				// 修正垂直对齐：使用与价格相同的基线位置
				let position_y = this.calHeightPos() - 20  // 与价格基线对齐
				this.drawText(marketPrice, 18, null, position_x, position_y, 1, true)
				let textWidth = this.ctx.measureText(marketPrice).width
				this.ctx.beginPath()
				this.ctx.rect(position_x, position_y + 9, textWidth, 1);  // 划线位置调整
				this.ctx.fill()
				this.ctx.save()
				this.drawText_name()  // 市场价后绘制商品名称
			},
			
			// 绘制商品主图
			drawImage_main(){
				const {maxWidth} = this.canvasInfo
				const {image} = this.posterInfo
				// 商品图片使用1:1比例，正方形显示
				const squareSize = maxWidth  // 使用容器宽度作为正方形边长
				const startX = 0
				const startY = this.calHeightPos()
				
				// 设置裁剪区域为正方形
				this.ctx.rect(startX, startY, squareSize, squareSize)
				this.ctx.clip()
				
				this.loadMainImage().then(res => {
					//wx-1-start
					// #ifdef MP-WEIXIN
					this.drawMainImage(res.path, startX, startY, res.width, res.height, squareSize)
					// #endif
					//wx-1-end
					// #ifdef APP-PLUS||H5
					const obj = {...res[0],...res[1]}
					this.drawMainImage(obj.downUrl, startX, startY, obj.width, obj.height, squareSize)
					// #endif
					this.ctx.restore()
					this.ctx.save()
					// 商品图片绘制完成后，绘制商品信息
					this.drawText_price()  // 先绘制价格
				})
			},
			
			//绘制二维码或者太阳码
			drawImage_code(){
				const {qrcode,brief} = this.posterInfo
				const {calHeightPos} = this
				// 二维码位置：右下角，距离右边和底部各20px
				const qrSize = 120  
				const qrX = this.canvasInfo.tWidth - qrSize - 20
				const qrY = this.canvasInfo.tHeight - qrSize - 20

				//wx-2-start
				// #ifdef MP-WEIXIN
				if(!qrcode){
					this.$api.msg('请等待资源加载完成后重试')
					return
				}

				this.getBase64Image(qrcode).then((res)=>{
					this.ctx.drawImage(res, qrX, qrY, qrSize, qrSize)
					this.ctx.restore()
					this.ctx.save()
					// 二维码绘制完成后，完成海报
					this.finalizePoster()
				})
				// #endif
				//wx-2-end
				// #ifndef MP-WEIXIN
				this.makeQrCode().then(res=>{
					const qrCode = res.tempFilePath
					this.qrCode = res.tempFilePath
					this.ctx.drawImage(qrCode, qrX, qrY, qrSize, qrSize)
					this.ctx.restore()
					this.ctx.save()
					// 二维码绘制完成后，完成海报
					this.finalizePoster()
				})
				// #endif
			},

			// 完成海报绘制
			finalizePoster() {
				this.ctx.save()
				this.ctx.draw()
				//完成后生成图片
				this.saveCanvasToImage()
			},
			//绘制商品描述，没有则跳过
			drawText_brief(){
				const {brief} = this.posterInfo
				const {calHeightPos} = this
				const startX = 15  // 与其他元素对齐
				let size = 14
				this.setFontSize(size)
				this.ctx.setFillStyle('#666666');
				let position_x = startX
				let position_y = calHeightPos() + 15  // 价格下方15px
				let textWidth = this.canvasInfo.tWidth - 30  // 左右各留15px边距
				this.drawText(brief, size, textWidth, position_x, position_y, 3)  // 最多3行
				this.ctx.save()
				this.drawImage_code()  // 绘制二维码
			},
			
			// 绘制用户头像
			drawUserAvatar(x, y, callback) {
				// 头像大小调整为40px，与用户名(16px) + 副标题(12px) + 间距(22px) ≈ 50px 的高度相匹配
				const avatarSize = 40
				const {userAvatar} = this.posterInfo

				console.log('绘制头像:', userAvatar)
				if(!userAvatar) {
					console.log('没有头像数据')
					if(callback) callback()
					return
				}

				// 绘制圆形头像
				this.ctx.save()
				this.ctx.beginPath()
				this.ctx.arc(x + avatarSize/2, y + avatarSize/2, avatarSize/2, 0, 2 * Math.PI)
				this.ctx.clip()

				// 加载并绘制头像
				uni.getImageInfo({
					src: userAvatar,
					success: (res) => {
						console.log('头像加载成功:', res)
						this.ctx.drawImage(res.path, x, y, avatarSize, avatarSize)
						this.ctx.restore()
						if(callback) callback()
					},
					fail: (err) => {
						console.log('头像加载失败:', err)
						this.ctx.restore()
						if(callback) callback()
					}
				})
			},

			//绘制提示
			drawText_tip(){
				const startX = 15
				// 计算推荐模块位置，基于二维码位置向下移动20px
				const qrSize = 80
				const qrCodeY = this.canvasInfo.tHeight - qrSize - 20  // 二维码Y位置
				const position_y = qrCodeY + 20

				console.log('绘制推荐信息:', this.posterInfo)

				// 获取店铺名称，如果没有则使用默认
				const shopName = this.posterInfo.shopName || '招财猫'
				let text_tip = `${shopName}的推荐`
				console.log('推荐文案:', text_tip)

				// 绘制用户头像（如果有），头像在文字左侧
				if(this.posterInfo.userAvatar) {
					console.log('开始绘制头像')
					// 头像位置调整，让头像与文字垂直居中对齐
					const avatarY = position_y - 5  // 头像稍微上移以便与文字对齐
					this.drawUserAvatar(startX, avatarY, () => {
						// 头像绘制完成后绘制文字，文字位置要考虑头像宽度
						this.setFontSize(18)  // 用户名字体加大
						this.ctx.setFillStyle('#222222')  // 使用#222222颜色
						this.drawText(text_tip, 18, null, startX + 50, position_y, 1)  // 头像宽度40px + 10px间距

						// 绘制副标题
						let subText = '值得买的好宝贝，别错过哦~'
						this.setFontSize(12)
						this.ctx.setFillStyle('rgb(153, 153, 153)')
						this.drawText(subText, 12, null, startX + 50, position_y + 24, 1)  // 副标题与用户名对齐

						// 绘制二维码
						this.drawImage_code()
					})
				} else {
					console.log('没有用户头像数据，直接绘制文字')
					// 没有头像直接绘制文字
					this.setFontSize(18)  // 用户名字体加大
					this.ctx.setFillStyle('#222222')  // 使用#222222颜色
					this.drawText(text_tip, 18, null, startX, position_y, 1)

					// 绘制副标题
					let subText = '值得买的好宝贝，别错过哦~'
					this.setFontSize(12)
					this.ctx.setFillStyle('rgb(153, 153, 153)')
					this.drawText(subText, 12, null, startX, position_y + 24, 1)  // 调整间距

					// 绘制二维码
					this.drawImage_code()
				}
			},
			
			
			//获取商品主图的信息，包括宽高
			loadMainImage(){
				const {image} = this.posterInfo
				let _this = this
				const systemInfo = uni.getSystemInfoSync()
				const p1 = new Promise((resolve,reject)=>{
					let _this = this
					uni.getImageInfo({
						src:image,
						success(res){
							resolve({
								url:image,
								width:res.width,
								height:res.height,
								path:res.path
							})
						},
						fail(err){
							_this.$api.msg(err.errMsg)
						}
					})
				})

			
				let funcs=p1
				// #ifdef APP-PLUS||H5
				const p2 = new Promise((resolve,reject)=>{
					uni.downloadFile({
						url:image,
						success(res) {
							resolve({
								downUrl:res.tempFilePath
							})
						},
						fail() {
							_this.$api.msg(err.errMsg)            
						}
					})
				})
				
				
				funcs= Promise.all([p1,p2])
				// #endif

				return funcs


				
			},
			
			//生成图片
			saveCanvasToImage () {
			    let self = this;       
				const {tWidth,tHeight} = this.canvasInfo
				const device = uni.getSystemInfoSync && wx.getSystemInfoSync() || {};
				const delay = device.system.indexOf('iOS') === -1 ? 300 : 100
			    // 延时保存有两个原因，一个是等待绘制delay的元素，另一个是安卓上样式会错乱
			    setTimeout(() => {			
			        let obj = {
			            x: 0,
			            y: 0,
			            width: tWidth,
			            height: tHeight,
			            canvasId: 'canvas',
			            success: function (res) {
			                self.imgUrl = res.tempFilePath;
							self.$emit('cachePoster',self.imgUrl)
							uni.hideLoading()
			            },
			            fail: function (res) {
			                console.log(res,'error')
						}
			        }
			        uni.canvasToTempFilePath(obj, this);
			    }, delay);
			},
			
			//生成二维码
			makeQrCode(options) {
				const {url} = this.posterInfo
				return uqrcode.make({
					canvasId: 'qrcode',
					size: 126,
					margin: 10,
					text: url
				}, this)
			},
			
			prevImg() {
				uni.previewImage({
					urls: [this.imgUrl]
				})
			},
			
			//保存图片--小程序 app
			savePoster() {
				const that = this
				if(!this.imgUrl){
					return
				}
				
				uni.saveImageToPhotosAlbum({
					filePath: this.imgUrl,
					success: function() {
						wx.showToast({
							title: '保存成功',
							icon: 'none',
							duration: 1500
						});
					},
					fail(err) {
						if (err.errMsg === "saveImageToPhotosAlbum:fail:auth denied" || err.errMsg ===
							"saveImageToPhotosAlbum:fail auth deny" || err.errMsg ===
							"saveImageToPhotosAlbum:fail authorize no response") {
							uni.showModal({
								title: '提示',
								content: '需要您授权保存相册',
								showCancel: false,
								success: modalSuccess => {
									uni.openSetting({
										success(settingdata) {
											if (settingdata.authSetting[
													'scope.writePhotosAlbum']) {
												uni.saveImageToPhotosAlbum({
													filePath: that.data.imgUrl,
													success: function() {
														uni.showToast({
															title: '保存成功',
															icon: 'success',
															duration: 2000
														})
													},
												})
											} else {
												wx.showToast({
													title: '授权失败，请稍后重新获取',
													icon: 'none',
													duration: 1500
												});
											}
										}
									})
								}
							})
						}
					}
				})
			},
		}
	}
</script>

<style lang="scss">
	.content{
		position: relative;
	}
	.poster{
		
		position: absolute;
		left: 10000px;
		visibility: hidden;
		height: 0;
		overflow: hidden;
		
		canvas{
			width: 414px;
			height: 720px;
		}
	}
	
	
	.poster_image{
		canvas{
			width: 500rpx;
			height: 870rpx;
		}
		
		image,img{
			width: 500rpx;
			height: 870rpx;
			object-fit: contain;
		}
	}
	
	.classLoading{
		width: 500rpx;
		height: 870rpx;
		background-color: white;
		.text_loading{
			font-size: 30rpx;
			color: #999;
		}
	}
	
	.uqrcode{
		position: absolute;
		left: 10000px;
		visibility: hidden;
		height: 0;
		overflow: hidden;
		
		#qrcode{
			width: 126px;
			height: 126px;
		}
	}
</style>