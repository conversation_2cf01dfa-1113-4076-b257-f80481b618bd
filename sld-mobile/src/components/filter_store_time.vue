<template>
	<view>
		<uni-drawer ref="showRight" mode="right" :mask-click="true">
			<view class="mask_body">
				<view class="mask_item">
					<view class="mask_top flex_row_between_center">
						<view class="mask_left">{{$L('按时间')}}</view>
						<!-- <view class="mask_right flex_row_end_center" @click="changeShow">
							<span>展开</span>
							<image :class="{rotate:!showTime}" :src="downImg" mode="widthFix"></image>
						</view> -->
					</view>
					<view class="mask_main flex_row_start_center" v-if="showTime">
						<view class="mask_main_item" :class="{active:timeIndex==index}" :key="index"
							v-for="(item,index) in timeList" @click="changeIndex(index)">{{item.name}}</view>
					</view>
				</view>
				<view class="mask_bottom flex_row_center_center">
					<view class="mask_btn flex_row_center_center">
						<view class="mask_reset" @click="reset">{{$L('重置')}}</view>
						<view class="mask_submit" @click="submit">{{$L('确定')}}</view>
					</view>
				</view>
			</view>
		</uni-drawer>
	</view>

</template>

<script>
	export default {
		components: {},
		data() {
			return {
				downImg: process.env.VUE_APP_IMG_URL + 'goods_detail/down.png',
				timeList: [],
				timeIndex: 0,
				showTime: true
			}
		},
		mounted() {
			this.getTime();
		},
		methods: {
			// 打开筛选
			open() {
				this.$refs.showRight.open()
			},
			// 关闭筛选
			close() {
				this.$refs.showRight.close()
			},
			// 获取时间数据
			getTime() {
				let param = {
					url: 'v3/business/front/orderInfo/timeList'
				};
				this.$request(param).then(res=>{
					if(res.state == 200){
						this.timeList = res.data;
					}
				})
			},
			//切换筛选下标
			changeIndex(index) {
				if(this.timeIndex != index){
					this.timeIndex = index;
				}
			},
			//切换展示隐藏
			changeShow() {
				this.showTime = !this.showTime;
			},
			//重置
			reset() {
				this.timeIndex = 0;
				this.showTime = true;
				this.submit();
			},
			//筛选确定
			submit() {
				let { timeList, timeIndex } = this;
				this.$emit('updateFilter',{
					timeId: timeList[timeIndex].id
				});
				this.close();
			},
		}
	}
</script>

<style lang="scss" scoped>
.mask_body {
	height: 100%;
	padding-top: 20rpx;
	border-top-left-radius: 26rpx;
	background-color: #FFFFFF;
	
	.mask_item {
		padding-left: 36rpx;
		padding-right: 36rpx;
	
		.mask_top {
			margin-top: 16rpx;
			margin-bottom: 24rpx;
			
			.mask_left {
				color: #333333;
				font-size: 30rpx;
				font-family: PingFang-SC-Bold, PingFang-SC;
				font-weight: 700;
			}
			.mask_right {
				color: #919191;
				font-size: 24rpx;
				font-family: PingFang-SC-Medium, PingFang-SC;
				font-weight: 500;
				
				image {
					width: 16rpx;
					height: 10rpx;
					margin-left: 10rpx;
					
					&.rotate {
						transform: rotate(180deg);
					}
				}
			}
		}
		.mask_main {
			flex-wrap: wrap;
			
			.mask_main_item {
				min-width: 164rpx;
				height: 62rpx;
				line-height: 62rpx;
				font-size: 26rpx;
				font-family: PingFang-SC-Regular, PingFang-SC;
				font-weight: 400;
				text-align: center;
				margin: 16rpx 16rpx;
				padding-left: 42rpx;
				padding-right: 42rpx;
				border-radius: 40rpx;
				background: #F9F9F9;
				
				&.active {
					color: #FFFFFF;
					background-color: var(--color_main);
				}
			}
		}
	}
	.mask_bottom {
		position: absolute;
		left: 0;
		right: 0;
		bottom: 0;
		border-top: 1rpx solid #E6E6E6;
		padding-top: 24rpx;
		padding-bottom: calc(24rpx + constant(safe-area-inset-bottom)/2); /* 兼容 iOS < 11.2 */
		padding-bottom: calc(24rpx + env(safe-area-inset-bottom)/2); /* 兼容 iOS >= 11.2 */
		
		.mask_btn {
			width: 500rpx;
			height: 74rpx;
			line-height: 74rpx;
			font-size: 28rpx;
			font-family: PingFang-SC-Medium, PingFang-SC;
			font-weight: 500;
			text-align: center;
			border: 1rpx solid var(--color_main);
			border-radius: 36rpx;
			background: var(--color_main);
			overflow: hidden;
			
			.mask_reset {
				width: 250rpx;
				height: 74rpx;
				color: var(--color_main);
				background-color: #FFFFFF;
				border-bottom-right-radius: 36rpx;
			}
			.mask_submit {
				width: 250rpx;
				height: 74rpx;
				color: #FFFFFF;
			}
		}
	}
}
</style>
