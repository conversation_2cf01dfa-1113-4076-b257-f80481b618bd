<template name="recommendGoods">
	<view v-if="recommendGoods && recommendGoods.length > 0">
		<view class="recommend-title" v-if="recommendTitle">
			<image :src="imgUrl + 'recommend/recom_left.png'" mode=""></image>
			<text>{{recommendTitle}}</text>
			<image :src="imgUrl + 'recommend/recom_right.png'" mode=""></image>
		</view>
		<view class="recommend-title_space" v-else></view>
		<view class="recommend-goods flex_row_start_start">
			<goodsItemV v-for="(item,index) in recommendGoods" 
				:goods_info="item" :key='index' 
				:show_sale="false" :icon_type="1" 
				@reloadCartList="reloadCartList" 
				@addCart="addCart" 
				ref="recom_goods" 
				:border_radius="10"
				:isOwnBefore="isSmartEnable"
				@before2Detail="statClick"
			/>
		</view>
		<loadingState :state='loadingState'/>
	</view>
</template>

<script>
	import goodsItemV from "@/components/goods_item_v.vue";
	import loadingState from "@/components/loading-state.vue";
	
	let pageMapping = {
		'/pages/index/index':{
			name:'recommend_position_index',
			code:'index',
			disabledShow:false
		},
		'/standard/product/detail':{
			name:'recommend_position_goods',
			code:'goods',
			disabledShow:false
		},
		'/pages/cart/cart':{
			name:'recommend_position_cart',
			code:'cart',
			disabledShow:true
		},
		'/pages/order/tradeSuccess':{
			name:'recommend_position_pay',
			code:'pay',
			disabledShow:true
		},
		'/pages/order/list':{
			name:'recommend_position_order_list',
			code:'order_list',
			disabledShow:true
		},
		'/pages/order/detail':{
			name:'recommend_position_order_detail',
			code:'order_detail',
			disabledShow:true
		},
		'/pages/member/collect':{
			name:'recommend_position_collect',
			code:'collect',
			disabledShow:true
		},
		'/standard/store/attentionStore':{
			name:'recommend_position_follow',
			code:'follow',
			disabledShow:true
		},
		'/pages/user/user':{
			name:'recommend_position_member',
			code:'member',
			disabledShow:true
		},
		'/pages/member/history':{
			name:'recommend_position_look',
			code:'look',
			disabledShow:true
		}
	}
	
	
	
	export default {
		name: "recommendGoods",
		data() {
			return {
				imgUrl: process.env.VUE_APP_IMG_URL,
				recommendGoods: [],
				loadingState: 'first_loading',
				pageSize: 8,
				current: 1,
				hasMore: true,//是否还有数据
				recommendTitle:'',
				isSmartEnable:false
			}
		},
		props: {
			arriveBotFlag: {
				type: Boolean,
				default: false,
			}
		},
		components: {
			goodsItemV,
			loadingState
		},
		created() {
			this.intelliRecomSetting()
		},
		mounted() {
			this.$emit('loadedFn')
		},
		methods: {
			intelliRecomSetting(){			
				let {path} = this.$Route
				this.$request({
					url:'v3/system/front/setting/getSettings',
					data:{
						names:pageMapping[path]?.name??''
					}
				}).then(res=>{
					if(res.state==200){
						let [result] = res.data
						if(result){
							let parsedRes = JSON.parse(result)
							this.isSmartEnable = parsedRes.enable
							if(parsedRes.enable){
								this.recommendTitle = parsedRes.recommend_title
								this.getData()
							}else{
								this.recommendTitle = this.$L('猜你喜欢')
								if(pageMapping[path].disabledShow){
									this.getData();//获取推荐商品数据
								}else{
									this.hasMore = false
								}
							}
						}else{
							this.hasMore = false
						}
					}
				})
			},
			
			
			getData() {
				let {path,query} = this.$Route
				let param = {};
				param.data = {};
				param.method = 'GET';
				param.url = 'v3/goods/front/goods/recommendList';
				if(this.isSmartEnable){
					param.data.positionCode = pageMapping[path].code;
					if(path=='/standard/product/detail'){
						param.data.goodsId =query.goodsId;
					}
					if(path=='/pages/order/tradeSuccess'){
						param.data.paySn =query.orderSn; 
					}
					if(path=='/pages/order/detail'){
						param.data.orderSn =query.orderSn;
					}
				}else{
					param.data.queryType ='cart';
				}
				param.data.pageSize = this.pageSize;
				param.data.current = this.current;
				
				this.loadingState = this.loadingState == 'first_loading'?this.loadingState:'loading';
				this.$request(param).then(res => {
					if (res.state == 200) {
						if(this.current == 1){
							this.recommendGoods = res.data.list;
						}else{
							this.recommendGoods = this.recommendGoods.concat(res.data.list);
						}
						this.hasMore = this.$checkPaginationHasMore(res.data.pagination||{});//是否还有数据
						if(this.hasMore){
							this.current++;
							this.loadingState = 'allow_loading_more';
						}else{
							this.loadingState = 'no_more_data';
						}
						// 子组件向父组件传值
						uni.$emit("recommendGoods",{
							recommendLen:this.recommendGoods.length
						})
					} else {
						//错误提示
					}
				})
			},
			//页面到底部加载更多数据
			getMoreData(){
				if(this.hasMore){
					this.getData();
				}
			},
			reloadCartList(val){
				this.$emit('reload_cart',val)
			},
			addCart(val){
				this.$emit('addToCart',val)
			},
			
			statClick(goods_info){
				let {path} = this.$Route
				this.$Router.push({
					path:'/standard/product/detail',
					query: {
						productId: (goods_info.productId || goods_info.defaultProductId),
						goodsId: goods_info.goodsId,
						fromRecommend:pageMapping[path].code
					}
				})
			},
			
			isWidthinFrom(code){
				let _array = Object.values(pageMapping)
				return _array.find(item=>item.code==code)
			}
		}
	}
</script>
<style lang='scss'>
	.list-scroll-content{
		height: 100vh;
	}
	.recommend-title {
		display: flex;
		justify-content: center;
		
		padding: 47rpx 0;

		image {
			width: 56rpx;
			height: 41rpx;
		}
		
		text{
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: bold;
			color: #333333;
			margin: 0 22rpx;
		}
	}
	
	.recommend-title_space{
		padding: 20rpx 0;
	}
	
	.recommend-goods {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		width: 100%;
		padding:0 20rpx;
		box-sizing: border-box;
	}
</style>
