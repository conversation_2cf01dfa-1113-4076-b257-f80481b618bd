<template>
	<view class="uni-indexed-list" ref="list" id="list">
		<!-- app-1-start -->




		<!-- app-1-end -->

		<scroll-view :scroll-into-view="scrollViewId" class="uni-indexed-list__scroll" scroll-y @scrolltolower="getMore">
			<view v-for="(list, idx) in lists" :key="idx" :id="'uni-indexed-list-' + idx">

				<uni-indexed-list-item :list="list" :loaded="loaded" :idx="idx" :showSelect="showSelect" @itemClick="onClick"></uni-indexed-list-item>

			</view>
		</scroll-view>

		<!-- app-2-start -->




		<!-- app-2-end -->
		<view class="uni-indexed-list__menu">
			<view v-for="(list, key) in lists" :key="key" class="uni-indexed-list__menu-item" @click="goInitial(key)">
				<text class="uni-indexed-list__menu-text" :class="touchmoveIndex == key ? 'uni-indexed-list__menu-text--active' : ''">{{ list.key }}</text>
			</view>
		</view>
		<view v-if="touchmove" class="uni-indexed-list__alert-wrapper">
			<text class="uni-indexed-list__alert">{{ lists[touchmoveIndex].key }}</text>
		</view>
	</view>
</template>
<script>
	import uniIcons from '../uni-icons/uni-icons.vue'
	import uniIndexedListItem from './uni-indexed-list-item.vue'
	//app-3-start



	//app-3-end
	//app-4-start














	//app-4-end
	function touchMove(e) {
		let pageY = e.touches[0].pageY
		let index = Math.floor((pageY - this.winOffsetY) / this.itemHeight)
		if (this.touchmoveIndex === index) {
			return false
		}
		let item = this.lists[index]
		if (item) {

			this.scrollViewId = 'uni-indexed-list-' + index
			this.touchmoveIndex = index

			//app-5-start






			//app-5-end
		}
	}
	//app-6-start



	//app-6-end
	
	/**
	 * IndexedList 索引列表
	 * @description 用于展示索引列表
	 * @tutorial https://ext.dcloud.net.cn/plugin?id=375
	 * @property {Boolean} showSelect = [true|false] 展示模式
	 * 	@value true 展示模式
	 * 	@value false 选择模式
	 * @property {Object} options 索引列表需要的数据对象
	 * @event {Function} click 点击列表事件 ，返回当前选择项的事件对象
	 * @example <uni-indexed-list options="" showSelect="false" @click=""></uni-indexed-list>
	 */
	export default {
		name: 'UniIndexedList',
		components: {
			uniIcons,
			uniIndexedListItem
		},
		props: {
			options: {
				type: Array,
				default () {
					return []
				}
			},
			showSelect: {
				type: Boolean,
				default: false
			},
			hasMore:{
				type:Boolean,
				default:false
			}
		},
		data() {
			return {
				lists: [],
				winHeight: 0,
				itemHeight: 0,
				winOffsetY: 0,
				touchmove: false,
				touchmoveIndex: -1,
				scrollViewId: '',
				touchmoveTimeout: '',
				loaded: false
			}
		},
		watch: {
			options: {
				handler: function() {
					this.setList()
				},
				deep: true
			}
		},
		mounted() {
			setTimeout(() => {
				this.setList()
			}, 50)
			setTimeout(() => {
				this.loaded = true
			}, 300);
		},
		methods: {
			setList() {
				let index = 0;
				this.lists = [];
				this.options.forEach((value, index) => {
					if (value.goodsBrandInfoList.length === 0) {
						return
					}
					let indexBefore = index
					let items = value.goodsBrandInfoList.map(item => {
						let obj = {}
						obj['key'] = value.brandInitial;
						obj['name'] = item.brandName;
						obj['image'] = item.imageUrl;
						obj['brandDesc'] = item.brandDesc;
						obj['brandId'] = item.brandId;
						obj['itemIndex'] = index
						index++
						obj.checked = item.checked ? item.checked : false
						return obj
					})
					this.lists.push({
						title: value.brandInitial,
						key: value.brandInitial,
						items: items,
						itemIndex: indexBefore
					})
				})

				uni.createSelectorQuery()
					.in(this)
					.select('#list')
					.boundingClientRect()
					.exec(ret => {
						this.winOffsetY = ret[0].top
						this.winHeight = ret[0].height
						this.itemHeight = this.winHeight / this.lists.length
					})

				//app-7-start







				//app-7-end
			},
			//右侧字母导航点击事件，点击字母，列表自动滚动到对应的部分
			goInitial(index) {
				let item = this.lists[index]
				if (item) {
					this.scrollViewId = 'uni-indexed-list-' + index
					this.touchmoveIndex = index
					//app-8-start





					//app-8-end
				}
			},
			onClick(e) {
				let {
					item
				} = e
				//根据品牌id跳转到商品列表
				this.$Router.push({path:'/standard/product/list',query:{brandId}})

			},
			getMore(){
				if(this.hasMore){
					this.$emit("getData")
				}
			}
		}
	}
</script>
<style lang="scss" scoped>
	.uni-indexed-list {
		position: absolute;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;

		display: flex;

		flex-direction: row;
		width: 750rpx;
		margin: 0 auto;
	}

	.uni-indexed-list__scroll {
		flex: 1;
	}

	.uni-indexed-list__menu {
		width: 80rpx;
		background-color: transparent;

		display: flex;

		flex-direction: column;
		position: absolute;
		right: 0;
		z-index: 2;
		top: 15vh;
	}

	.uni-indexed-list__menu-item {
		position: relative;

		display: flex;

		flex: 1;
		align-items: center;
		justify-content: center;

	}

	.uni-indexed-list__menu-text {
		line-height: 38rpx;
		color: #2D2D2D;
		font-size: 26rpx;
		text-align: center;
	}

	.uni-indexed-list__menu--active {}

	.uni-indexed-list__menu-text--active {
		color: var(--color_main);
		
		&:after {
			content: '';
			position: absolute;
			width: 38rpx;
			height: 38rpx;
			top: 50%;
			left: 50%;
			transform: translate(-50%,-50%);
			border-radius: 50%;
			background: rgba(252, 28, 28, .2);
		}
	}

	.uni-indexed-list__alert-wrapper {
		position: absolute;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;

		display: flex;

		flex-direction: row;
		align-items: center;
		justify-content: center;
	}

	.uni-indexed-list__alert {
		width: 80px;
		height: 80px;
		border-radius: 80px;
		text-align: center;
		line-height: 80px;
		font-size: 35px;
		color: #fff;
		background-color: rgba(0, 0, 0, 0.5);
	}
</style>
