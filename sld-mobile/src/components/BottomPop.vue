<template>
	<uni-popup ref="popModel" type="bottom" @change="change" :style="{zIndex}">
		<view class="bottom_pop_container">
			<view class="top" :class="{'flex_row_start_center':titleAlign=='left','flex_row_center_center':titleAlign=='center'}">
				<view class="title">{{ title }}</view>
				
				
				<view class="right">
					<block v-if="$slots.headerRight">
						<slot name="headerRight"/>
					</block>
					<block v-else>
						<image class="close_icon" :src="imgUrl + 'close_2.png'" mode="aspectFit" @click="close">
						</image>
					</block>
				</view>
				
			</view>
			<block v-if="scroller">
				<scroll-view scroll-y="true" class="scroll_list" @touchmove.stop.prevent="moveHandle" :style="{height:scrollHeight+'rpx'}">
					<slot/>
				</scroll-view>
			</block>
			<block v-else>
				<slot/>
			</block>
			
			<view class="tab_space" v-if="hasTab">
				
			</view>
		</view>
	</uni-popup>
</template>

<script>
	export default{
		props:{
			title:String,
			titleAlign:{
				type:String,
				default:'left'
			},
			scrollHeight:Number,
			beforeClose:{
				type:Boolean,
				default:false
			},
			zIndex:{
				type:Number,
				default:100
			},
			scroller:{
				type:Boolean,
				default:true
			},
			hasTab:Boolean
		},
		data:()=>({
			imgUrl:process.env.VUE_APP_IMG_URL
		}),
		methods:{
			close(){
				
				if(this.beforeClose){
					this.$emit('close',()=>{
						this.close()
					})
					return
				}
				this.close()
				this.$emit('close')
			},
			
			change(e){
				this.$emit('change',e)
			},
			
			close(){
				this.$refs.popModel.close()
			},
			open(){
				this.$refs.popModel.open()
			}
		}
	}
</script>

<style lang="scss">
	.bottom_pop_container{
		background: #FFFFFF;
		border-radius: 30rpx 30rpx 0rpx 0rpx;
		overflow: hidden;
		position: relative;
		z-index: 200;
		.top{
			border-bottom: 1px solid #ebebeb;
			height: 96rpx;
			padding: 0 30rpx;
			position: relative;
			.title{
				font-weight: bold;
				font-size: 34rpx;
				color: #000000;
			}
			.right{
				position: absolute;
				right: 30rpx;
				.close_icon{
					width: 40rpx;
					height: 40rpx;
				}
			}
		}
		
		.scroll_list{
			height: 498rpx;
		}
		
		.tab_space{
			height: calc(100rpx + env(safe-area-inset-bottom));
		}
	}
</style>

