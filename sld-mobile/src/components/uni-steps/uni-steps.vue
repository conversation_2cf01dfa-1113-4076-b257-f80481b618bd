<template>
	<view class="uni-steps">
		<view :class="[direction==='column'?'uni-steps__column':'uni-steps__row']">
			<block v-for="(item,index) in options" :key="index">
				<view class="flex_row_center_center" style="position: relative;">
					<view :class="[direction==='column'?'uni-steps__column-container':'uni-steps__row-container']">
						<view :class="[direction==='column'?'uni-steps__column-line-item':'uni-steps__row-line-item']">
							
							<view :class="[direction==='column'?'uni-steps__column-check':'uni-steps__row-check']"
								v-if="index === 0">
								<uni-icons :color="activeColor" type="checkbox-filled" size="24"></uni-icons>
							</view>
							<view
								:class="[direction==='column'?'uni-steps__column-circle circle_big':'uni-steps__row-circle']"
								v-else-if="index>0&&index<4" :style="{backgroundColor:deactiveColor}"></view>
							<view
								:class="[direction==='column'?'uni-steps__column-circle circle_small':'uni-steps__row-circle']"
								v-else :style="{backgroundColor:deactiveColor}"></view>
							<view
								:class="[direction==='column'?'uni-steps__column-line':'uni-steps__row-line',direction==='column'?'uni-steps__column-line--after':'uni-steps__row-line--after']"
								:style="{backgroundColor:deactiveColor}"></view>
						</view>
					</view>
					<view
						:class="[direction==='column'?'uni-steps__column-text-container':'uni-steps__row-text-container']">
						<view :class="[direction==='column'?'uni-steps__column-text':'uni-steps__row-text']">
							<text :style="{color:index<=active?activeColor:deactiveColorText}"
								:class="[direction==='column'?'uni-steps__column-title':'uni-steps__row-title']">{{item.title}}</text>
							<text :style="{color:index<=active?activeColor:deactiveColorText}"
								:class="[direction==='column'?'uni-steps__column-desc':'uni-steps__row-desc']">{{item.desc}}</text>
						</view>
					</view>
				</view>
			</block>

		</view>
	</view>
</template>

<script>
	import uniIcons from '../uni-icons/uni-icons.vue'
	export default {
		name: 'UniSteps',
		components: {
			uniIcons
		},
		props: {
			direction: {
				// 排列方向 row column
				type: String,
				default: 'row'
			},
			activeColor: {
				// 激活状态颜色
				type: String,
				default: 'var(--color_main)'
			},
			deactiveColor: {
				// 未激活状态颜色
				type: String,
				default: '#C9C9C9'
			},
			active: {
				// 当前步骤
				type: Number,
				default: 0
			},
			options: {
				type: Array,
				default () {
					return []
				}
			}, // 数据
		},
		data() {
			return {
				deactiveColorText:'#999'
			}
		}
	}
</script>

<style lang="scss" scoped>
	.uni-steps {

		display: flex;
		width: 100%;

		//app-1-start



		//app-1-end
		flex-direction: column;
	}

	.uni-steps__row {

		display: flex;

		flex-direction: row;
	}

	.uni-steps__column {

		display: flex;

		flex-direction: column;
	}

	.uni-steps__row-text-container {

		display: flex;

		flex-direction: row;
	}

	.uni-steps__column-text-container {

		display: flex;

		flex-direction: column;
		flex: 1;
		padding: 0 20rpx 20rpx 0;
	}

	.uni-steps__row-text {

		display: inline-flex;

		flex: 1;
		flex-direction: column;
	}

	.uni-steps__column-text {
		margin-left: 80rpx;
		padding-bottom: 6px;

		display: flex;

		flex-direction: column;
	}

	.uni-steps__row-title {
		font-size: $uni-font-size-base;
		line-height: 16px;
		text-align: center;
	}

	.uni-steps__column-title {
		font-size: $uni-font-size-base;
		text-align: left;
		line-height: 18px;
	}

	.uni-steps__row-desc {
		font-size: 12px;
		line-height: 14px;
		text-align: center;
	}

	.uni-steps__column-desc {
		font-size: 28rpx;
		text-align: left;
		line-height: 1.5;
		word-break: break-all;
	}

	.uni-steps__row-container {

		display: flex;

		flex-direction: row;
	}

	.uni-steps__column-container {

		display: inline-flex;

		width: 30px;
		flex-direction: column;
		height: 100%;
		position: absolute;
		left: 0;
	}

	.uni-steps__row-line-item {

		display: inline-flex;

		flex-direction: row;
		flex: 1;
		height: 14px;
		line-height: 14px;
		align-items: center;
		justify-content: center;
	}

	.uni-steps__column-line-item {

		display: flex;

		flex-direction: column;
		flex: 1;
		align-items: center;
		justify-content: center;
	}

	.uni-steps__row-line {
		flex: 1;
		height: 1px;
		background-color: $uni-text-color-grey;
	}

	.uni-steps__column-line {
		width: 1px;
		background-color: $uni-text-color-grey;
	}

	.uni-steps__row-line--after {
		transform: translateX(1px);
	}

	.uni-steps__column-line--after {
		flex: 1;
		transform: translate(0px, 1px);
	}

	.uni-steps__row-line--before {
		transform: translateX(-1px);
	}

	.uni-steps__column-line--before {
		height: 6px;
		transform: translate(0px, -1px);
	}

	.uni-steps__row-circle {
		width: 5px;
		height: 5px;
		border-radius: 100px;
		background-color: $uni-text-color-grey;
		margin: 0px 3px;
	}

	.uni-steps__column-circle {
		width: 5px;
		height: 5px;
		border-radius: 100px;
		background-color: $uni-text-color-grey;
		margin: 4px 0px 5px 0px;
		background: #C9C9C9;

		&.circle_big {
			width: 26rpx;
			height: 26rpx;
		}

		&.circle_small {
			width: 18rpx;
			height: 18rpx;
		}
	}

	.uni-steps__row-check {
		margin: 0px 6px;
	}

	.uni-steps__column-check {
		height: 14px;
		line-height: 14px;
		margin: 2px 0px;
	}
</style>