<!-- 商品组件：竖直方向
	点击进入商品详情页
	加入购物车事件
-->
<template name="goodsItemV">
	<view class="goods_v_item flex_column_start_start" @click="goGoodsDetail(goods_info)"
		  :style="{
			width: 'calc((750rpx - ' + page_margin*4 + 'rpx - ' + goods_margin*2 + 'rpx)/2)',
			borderRadius: border_radius + 'px',
			border: border_style == 'border_eee' ? '1rpx solid #eee' : '',
			boxShadow: border_style == 'card-shadow' ? 'rgba(93, 113, 127, 0.08) 0px 2px 8px' : '',
			marginBottom: margin_bottom + 'rpx'
		  }">
		<view class="goods_desc_top" :style="{height:height*2+'rpx'}">
			<view class="goods-img"
				:style="{backgroundImage: 'url('+(goods_info.mainImage||goods_info.goodsImage)+')',width:'calc((750rpx - '+page_margin*4+'rpx - '+goods_margin*2+'rpx)/2)',borderTopLeftRadius:border_radius*2+'rpx',borderTopRightRadius:border_radius*2+'rpx'}">
			</view>
			<text class="goods-name">{{goods_info.goodsName}}</text>
		</view>
		<view :class="show_sale == true?'goods-price':'goods-price have_no_sale'"
			:style="{marginTop:isIos?'26rpx':0}">
			<view class="left">
				<text class="unit">{{$L('￥')}}</text>
				<text
					class="price_int">{{goods_info.goodsPrice>=0?$getPartNumber(goods_info.goodsPrice,'int'):goods_info.productPrice>=0?$getPartNumber(goods_info.productPrice,'int'):$getPartNumber(goods_info.marketPrice,'int')}}</text>
				<text
					class="price_decimal">{{goods_info.goodsPrice>=0?$getPartNumber(goods_info.goodsPrice,'decimal'):goods_info.productPrice>=0?$getPartNumber(goods_info.productPrice,'decimal'):$getPartNumber(goods_info.marketPrice,'decimal')}}</text>
			</view>
			<view class="pre_bottom" :class="show_sale == true?'goods_item_bottom':''">
				<view class="have_sold_text" v-if="show_sale == true">
					{{$L('已售')}}{{goods_info.actualSales>=0?goods_info.actualSales:goods_info.saleNum}}件</view>
				<view class="icon_set" @click.stop="addCart(goods_info.productId || goods_info.defaultProductId)">
					<svgGroup :type="`addCart${icon_type}`" :color="diyStyle_var['--color_price']" width="20" height="20"></svgGroup>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
	} from 'vuex';
	export default {
		name: "goodsItemV",
		data() {
			return {
				imgUrl: process.env.VUE_APP_IMG_URL,
				icon2: process.env.VUE_APP_IMG_URL + 'index/add2.png',
				icon3: process.env.VUE_APP_IMG_URL + 'index/add.png',
				icon4: process.env.VUE_APP_IMG_URL + 'index/add3.png',
				icon5: process.env.VUE_APP_IMG_URL + 'index/stop.png',
				icon_url: '', //加车图标
				goods_pic: '', //商品图片
				goods_sale: '', //销量
				isIos: uni.getSystemInfoSync().platform == 'ios', //是否ios手机
			}
		},
		computed: {
			...mapState(['userInfo'])
		},
		props: {
			goods_info: {
				type: Object,
				value: {}
			},
			icon_type: {
				type: Number,
			},
			show_sale: {
				type: Boolean
			},
			border_radius: {
				type: Number
			},
			border_style: {
				type: String
			},
			// 商品边距
			goods_margin: {
				type: Number,
				default: 10
			},
			// 页面边距
			page_margin: {
				type: Number,
				default: 10
			},
			height: {
				type: Number,
				default: 258
			},
			transType:{
				type:String,
				default:''
			},
			isOwnBefore:{
				type:Boolean,
				default:false
			},
			margin_bottom: {
				type: Number,
				default: 20
			}
		},
		onLoad() { },
		methods: {
			//进入商品详情页
			goGoodsDetail(goods_info) {
				this.$emit('before2Detail',goods_info)
				if(this.isOwnBefore){
					return
				}
				let path = '/standard/product/detail'
				this.$Router.push({
					path,
					query: {
						productId: (goods_info.productId || goods_info.defaultProductId),
						goodsId: goods_info.goodsId
					}
				})
			},
			// 加入购物车
			addCart(productId) {
				let that = this
				if (this.userInfo.member_access_token) { //登录
					let param = {}
					param.url = 'v3/business/front/cart/add'
					param.method = 'POST'
					param.data = {
						productId: productId,
						number: 1
					}
					
					this.$request(param).then(res => {
						if (res.state == 200) {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
							this.$emit('reloadCartList', true)
							this.$sldStatEvent({
								behaviorType: 'cart',
								goodsId: this.goods_info.goodsId,
								storeId: this.goods_info.storeId
							})
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none',
								duration: 700
							})
						}
					})
				} else { //未登录
				
					let local_cart_list = this.$addLocalCart(this.goods_info)
				
					uni.showToast({
						title: that.$L('加入购物车成功！'),
						icon: 'none'
					})
					this.$sldStatEvent({
						behaviorType: 'cart',
						goodsId: this.goods_info.goodsId,
						storeId: this.goods_info.storeId
					})
					
					
					this.$emit('addCart', local_cart_list)
				}
			},
			changeImg(val) {
				return val.mainImage ? val.mainImage : val.goodsImage
			},
			saleNum(val) {
				if (val.actualSales) {
					return val.actualSales
				} else {
					return val.saleNum
				}
				// return val.actualSales?val.actualSales:val.saleNum
			}
		},
	}
</script>

<style lang='scss'>
	.goods_v_item {
		background: #fff;
		border-radius: 20rpx;
		overflow: hidden;
		margin-bottom: 22rpx;
		display: flex;
		flex-direction: column;

		& {
			margin-right: 0 !important;
		}

		.goods_desc_top {
			height: 516rpx;

			.goods-img {
				background-size: cover;
				background-position: center center;
				background-repeat: no-repeat;
				height: calc((750rpx - 60rpx)/2);
				overflow: hidden;
				background-color: #fff;
			}

			.goods-name {
				height: 173rpx;
				margin-top: 20rpx;
				font-size: 28rpx;
				color: $com-main-font-color;
				line-height: 40rpx;
				height: 80rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
				word-break: break-word;
				padding: 0 20rpx;
			}

			.goods_des {
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #888888;
				line-height: 36rpx;
				padding: 0 20rpx;
				box-sizing: border-box;
				width: 355rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}

		.goods-price {
			padding: 0 20rpx;
			width: 100%;
			display: flex;
			align-items: center;
			flex-direction: column;

			.left {
				width: 100%;
				color: var(--color_price);

				.unit,
				.price_decimal {
					font-size: 24rpx;
					margin-right: 3rpx;
				}

				.price_int {
					font-size: 34rpx;
					line-height: 34rpx;
				}
			}

			image {
				width: 42rpx;
				height: 42rpx;
			}
		}
	}

	.goods_item_bottom {
		width: 100%;
		display: flex;
		justify-content: space-between;
		margin-top: 20rpx;
		padding-bottom: 20rpx;
	}

	.have_sold_text {
		font-size: 24rpx;
		color: #9a9a9a;
	}

	.pre_bottom {
		display: flex;
		align-items: center;
	}

	.have_no_sale {
		width: 100%;
		flex-direction: row !important;
		justify-content: space-between !important;
		padding: 20rpx !important;
    padding-bottom: 26rpx !important;
	}
</style>