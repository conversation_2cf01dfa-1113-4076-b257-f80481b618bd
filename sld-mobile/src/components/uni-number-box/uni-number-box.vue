<template>
	<view class="uni-numbox">
		<view @click="_calcValue('minus')" class="uni-numbox__minus">
			<text class="uni-numbox--text" :class="{ 'uni-numbox--disabled': inputValue <= min || disabled }">-</text>
		</view>
		<input :disabled="disabled" @blur="_onBlur" class="uni-numbox__value" type="number" v-model="inputValue" maxlength="5"/>
		<view @click="_calcValue('plus')" class="uni-numbox__plus">
			<text class="uni-numbox--text" :class="{ 'uni-numbox--disabled': inputValue >= max || disabled }">+</text>
		</view>
	</view>
</template>
<script>
	export default {
		name: "UniNumberBox",
		props: {
			value: {
				type: [Number, String],
				default: 1
			},
			min: {
				type: Number,
				default: 1
			},
			max: {
				type: Number,
				default: 100
			},
			step: {
				type: Number,
				default: 1
			},
			disabled: {
				type: Boolean,
				default: false
			},
			resctritTip:{
				type: Object,
				default: ()=>{}
			}
		},
		data() {
			return {
				inputValue: 0
			};
		},
		watch: {
			value(val) {
				this.inputValue = Number(val);
			},
		},
		created() {
			this.inputValue = this.value;
		},
		methods: {
			_calcValue(type) {
				if (this.disabled) {
					return;
				}
				const scale = this._getDecimalScale();
				let value = Number(this.inputValue) * scale;
				let step = this.step * scale;
				
				switch(type){
					case "minus":{
						if(value==this.min){
							uni.showToast({
								title: this.resctritTip.min,
								icon: 'none'
							})
							return
						}
						value -= step;
						break
					}
				
					case "plus":{
						value += step;
						if (value > this.max) {
							value = this.max
							uni.showToast({
								title: this.resctritTip.max,
								icon: 'none'
							})
							return;
						}
					}
				}
				
				this.inputValue = String(value / scale);
				this.$emit('change',type,this.inputValue)
			},
			_getDecimalScale() {
				let scale = 1;
				// 浮点型
				if (~~this.step !== this.step) {
					scale = Math.pow(10, (this.step + "").split(".")[1].length);
				}
				return scale;
			},
			_onBlur(event) {
				let value = event.detail.value;
				if (!value || value == '') {
					value = 1;
				}
				value = +value;
				if (value > this.max) {
					value = this.max;
				} else if (value < this.min) {
					value = this.min;
				}
				if (value < 1) {
					value = 1
				}
				this.inputValue = value;
				this.$emit('change','blur',this.inputValue)
			}
		}
	};
</script>
<style lang="scss" scoped>
	$box-height: 35px;
	//app-1-start



	//app-1-end
	$box-line-height: 26px;
	$box-width: 35px;

	.uni-numbox {

		display: flex;

		flex-direction: row;
		height: 50rpx;
		line-height: 50rpx;
	}

	.uni-numbox__value {
		background-color: $uni-bg-color;
		width: 78rpx;
		height: 50rpx;
		text-align: center;
		font-size: 24rpx;
		border-width: 1rpx;
		border-style: solid;
		border-color: rgba(0, 0, 0, .1);
	}

	.uni-numbox__minus {

		display: flex;

		flex-direction: row;
		align-items: center;
		justify-content: center;
		width: 50rpx;
		height: 50rpx;



		// line-height: $box-line-height;
		// text-align: center;
		font-size: 20px;
		color: $uni-text-color;
		background-color: #fff;
		border-width: 1rpx;
		border-style: solid;
		border-color: rgba(0, 0, 0, .1);
		border-top-left-radius: $uni-border-radius-base;
		border-bottom-left-radius: $uni-border-radius-base;
		border-right-width: 0;
	}

	.uni-numbox__plus {

		display: flex;

		flex-direction: row;
		align-items: center;
		justify-content: center;
		width: 50rpx;
		height: 50rpx;
		height: 50rpx;



		border-width: 1rpx;
		border-style: solid;
		border-color: rgba(0, 0, 0, .1);
		border-top-right-radius: 6rpx;
		border-bottom-right-radius: 6rpx;
		background-color: #fff;
		border-left-width: 0;
	}

	.uni-numbox--text {
		font-size: 30rpx;
		color: #2D2D2D;
	}

	.uni-numbox--disabled {
		color: #949494;
	}

	.uni-input-input {
		font-size: 24rpx !important;
		color: #2D2D2D;
	}
</style>