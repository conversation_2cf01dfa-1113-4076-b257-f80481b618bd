<template>
	<view class="container3" :class="{ prevent: isCookie }">
		<!-- 头部分类 -->
		<tab-menu :backGround="tab_index == 0 ? pure_bg_color : top_bg" :tabInfo="sort_nav_list" ref="tabmenu"
			@getChildList="getChildList" v-if="is_show_top == true && home_is_show_top_cat"></tab-menu>

		<!-- 首页装修 -->
		<view v-if="is_show_index == true && isShow == true">
			<view :class="{ deco_loading: isDecoDataEmpty }">
				<view class="index_deco" v-for="(decoItem, decoIndex) in deco_info" :key="decoIndex">

					<view class="carousel-section" v-if="decoItem.type == 'top_cat_nav' && decoItem.data.length > 0" :style="{
						paddingTop: ((decoItem.top_margin !== undefined && decoItem.top_margin !== null) ? decoItem.top_margin * 2 : 20) + 'rpx',
						paddingBottom: ((decoItem.bottom_margin !== undefined && decoItem.bottom_margin !== null) ? decoItem.bottom_margin * 2 : 0) + 'rpx',
						backgroundColor: decoItem.color || 'unset',
					}">
						<!-- 背景色区域 -->
						<view
							:class="decoItem.swiper_bg_style == 1 ? 'titleNview-background top_swiper_style1' : 'titleNview-background top_swiper_style2'"
							:style="{
								height: (((decoItem.top_margin || 0) + (decoItem.bottom_margin || 0)) + 100) + 'rpx',
								background: titleNViewBackground,
								borderRadius: decoItem.swiper_bg_style == 1 ? 'border_radius' : '0'
							}">
						</view>
						<uni-swiper-dot :current="swiperCurrent" :info="decoItem.data" mode="dot" :dotsStyles="dotsStyles">
							<swiper class="carousel" circular @change="swiperChange" autoplay="true"
								:style="{ margin: isIos ? '10rpx' : 0, width: '710rpx', height: (decoItem.height / decoItem.width * 710) + 'rpx' }">
								<swiper-item v-for="(item, index) in decoItem.data" :key="index" class="carousel-item">
									<!-- <image :src="item.img" mode="aspectFit" class="itemImg"/> -->
									<view class="itemImg" :style="'background-image:url(' + item.img + ')'"
										@click="skipTo(item.url_type, item)">
									</view>
								</swiper-item>
							</swiper>
						</uni-swiper-dot>
					</view>

					<!-- 公告 -->
					<view class="notice_box" v-if="decoItem.type == 'gonggao' && decoItem.is_show == true" :style="{
						paddingTop: ((decoItem.top_margin !== undefined && decoItem.top_margin !== null) ? decoItem.top_margin * 2 : 20) + 'rpx',
						paddingBottom: ((decoItem.bottom_margin !== undefined && decoItem.bottom_margin !== null) ? decoItem.bottom_margin * 2 : 20) + 'rpx',
						background: decoItem.color || 'unset',
					}">
						<!-- 公告样式一 -->
						<view class="notice_wrap1" v-if="decoItem.type == 'gonggao' && decoItem.show_style == 'one'"
							@click="skipTo(decoItem.url_type, decoItem)">
							<image :src="noticeImg1" mode="aspectFit" class="notice_img1"></image>
							<view class="notice_content_wrap">
								<uni-notice-bar :scrollable="true" :single="true" :text="decoItem.text" color="#666"
									backClass="backColorClass1" />
							</view>
							<view class="notice_wrap1_line"></view>
							<view class="notice_more">>></view>
						</view>
						<!-- 公告样式二 -->
						<view class="notice_wrap2" v-if="decoItem.type == 'gonggao' && decoItem.show_style == 'two'"
							@click="skipTo(decoItem.url_type, decoItem)">
							<image :src="noticeImg2" mode="aspectFit" class="notice_img2"></image>
							<view class="notice_content_wrap2">
								<uni-notice-bar :scrollable="true" :single="true" :text="decoItem.text" color="#fff"
									backClass="backColorClass2" />
							</view>
							<view class="notice_wrap2_line"></view>
							<view class="notice_more">>></view>
						</view>
					</view>

					<!-- 导航 -->
					<block v-if="decoItem.type == 'nav' && decoItem.is_show == true">
						<Nav :decoItem="decoItem" @skipTo="skipTo"></Nav>
					</block>


					<!-- 客服 -->
					<view class="service_wrap" v-if="decoItem.type == 'kefu' && decoItem.is_show == true"
						@click="callUp(decoItem.tel)" :style="{
							paddingTop: ((decoItem.top_margin !== undefined && decoItem.top_margin !== null) ? decoItem.top_margin * 2 : 20) + 'rpx',
							paddingBottom: ((decoItem.bottom_margin !== undefined && decoItem.bottom_margin !== null) ? decoItem.bottom_margin * 2 : 0) + 'rpx',
							backgroundColor: decoItem.color || 'unset',
						}">
						<view class="service_wrap_main">
							<image :src="telImg" mode="aspectFit"></image>
							<text>{{ decoItem.text }}{{ decoItem.tel }}</text>
						</view>
					</view>

					<!-- 富文本 -->
					<block v-if="decoItem.type == 'fuwenben' && decoItem.is_show == true">
						<RichTextC :decoItem="decoItem"></RichTextC>
					</block>

					<!-- 图片组合 -->
					<CombinationWrap :decoItem="decoItem" @skipTo="skipTo"
						v-if="decoItem.type == 'tupianzuhe' && decoItem.is_show == true"></CombinationWrap>

					<!-- 搭配 -->
					<block v-if="decoItem.type == 'dapei' && decoItem.is_show == true">
						<Match :decoItem="decoItem" />
					</block>


					<!-- 辅助线 -->
					<view class="subline_wrap" v-if="decoItem.type == 'fzx' && decoItem.is_show == true" :style="{
						marginTop: decoItem.tbmargin * 2 + 'rpx',
						marginBottom: decoItem.tbmargin * 2 + 'rpx',
						paddingLeft: decoItem.lrmargin * 2 + 'rpx',
						paddingRight: decoItem.lrmargin * 2 + 'rpx',
						paddingTop: ((decoItem.top_margin !== undefined && decoItem.top_margin !== null) ? decoItem.top_margin * 2 : 30) + 'rpx',
						paddingBottom: ((decoItem.bottom_margin !== undefined && decoItem.bottom_margin !== null) ? decoItem.bottom_margin * 2 : 30) + 'rpx',
						background: decoItem.bg_color || 'unset',
					}">
						<view class="subline" :style="{
							height: decoItem.tbmargin + 'px',
							borderBottomColor: decoItem.color,
							borderBottomStyle: decoItem.val
						}">
						</view>
						<view :style="{ height: decoItem.tbmargin + 'px' }"></view>
					</view>

					<!-- 轮播图 -->
					<view class="carousel_bottom_wrap" v-if="decoItem.type == 'lunbo' && decoItem.is_show == true"
						style="padding:0;" :style="{
							marginTop: decoIndex == 0 ? 0 : '',
							paddingTop: ((decoItem.top_margin !== undefined && decoItem.top_margin !== null) ? decoItem.top_margin * 2 : 20) + 'rpx',
							paddingBottom: ((decoItem.bottom_margin !== undefined && decoItem.bottom_margin !== null) ? decoItem.bottom_margin * 2 : 0) + 'rpx',
							background: decoItem.color || 'unset',
						}">
						<uni-swiper-dot :current="swiperCurrent2" :info="decoItem.data" mode="dot" :dotsStyles="dotsStyles">
							<swiper class="carousel carousel_bottom" circular autoplay="true" @change="swiperChangeIndex"
								:style="{ width: '710rpx', height: (decoItem.height / decoItem.width * 710) + 'rpx' }">
								<swiper-item v-for="(item, index) in decoItem.data" :key="index" class="carousel-item"
									style="padding:0;" @click="skipTo(item.url_type, item)">
									<view class="carousel_img" :style="'background-image:url(' + item.img + ')'">
									</view>
								</swiper-item>
							</swiper>
						</uni-swiper-dot>
					</view>

					<!-- 推荐商品样式 -->
					<RecommendWrap :decoItem="decoItem" @toGoodsDetail="toGoodsDetail" @addCart="addCart"
						v-if="decoItem.type == 'tuijianshangpin' && decoItem.is_show == true">
						<block>
							<block v-for="(item, index) in decoItem.data.info" :key="index">
								<goods-item-v v-if="item.state == 3" :goods_info="item"
									:show_sale="decoItem.isshow_sales == 1 ? true : false"
									:icon_type="decoItem.cart_icon_type" :height="225"
									:border_radius="decoItem.border_radius" :border_style="decoItem.border_style"
									:goods_margin="decoItem.goods_margin" :page_margin="decoItem.page_margin"
									:margin_bottom="((decoItem.data.info.length % 2 == 0 && index >= (decoItem.data.info.length - 2))
										|| (decoItem.data.info.length % 2 == 1 && index >= (decoItem.data.info.length - 1))) ? 0 : 20"
									@reloadCartList="cartUpdate" @addCart="cartUpdate">
								</goods-item-v>
							</block>
						</block>
					</RecommendWrap>


					<!-- 辅助空白 -->
					<view class="blank_wrap" v-if="decoItem.type == 'fzkb' && decoItem.id && decoItem.is_show == true"
						:style="{
							paddingTop: ((decoItem.top_margin !== undefined && decoItem.top_margin !== null) ? decoItem.top_margin * 2 : 20) + 'rpx',
							paddingBottom: ((decoItem.bottom_margin !== undefined && decoItem.bottom_margin !== null) ? decoItem.bottom_margin * 2 : 0) + 'rpx',
							backgroundColor: decoItem.color || 'unset',
							height: decoItem.text + 'px'
						}"></view>

					<!-- tab切换 -->
					<sort-list v-if="decoItem.type == 'more_tab' && decoItem.is_show == true" :card_height="225"
						:nav_list="decoItem.data" :isShowSale="false" :borderRadius="decoItem.border_radius"
						:sort_data="decoItem" @updateCart="cartUpdate">
					</sort-list>

					<!-- 短视频直播 -->
					<Media v-if="decoItem.type == 'svideo' || decoItem.type == 'live'" :decoItem="decoItem"
						@skipTo="skipTo" />

					<!-- 魔方start -->
					<cube v-if="decoItem.type == 'cube' && decoItem.is_show == true" :decoItem="decoItem" />
					<!-- 魔方end -->

					<!-- 绘图热区start -->
					<hotArea v-if="decoItem.type == 'hot_area' && decoItem.is_show == true" :decoItem="decoItem" />
					<!-- 绘图热区end -->

					<!-- 绘图热区start -->
					<!-- <Comment v-if="decoItem.type == 'comment' && decoItem.is_show == true" :decoItem="decoItem" /> -->
					<!-- 绘图热区end -->


				</view>
			</view>
			<view :class="{ deco_loading: !isDecoDataEmpty }" class="deco_loading_style flex_column_center_center">
				<image class="loading_more_icon" :src="imgUrl + 'loading_further.gif'" />
				<text>加载中</text>
			</view>
		</view>

		<!-- 顶部分类切换 -->
		<view class="sort_sub_wrap" v-if="tab_index != 0 && sort_obj.categoryList.length > 0">
			<view class="sort_sub_top">
				<view class="sort_sub_item" v-for="(item, index) in sort_obj.categoryList" :key="index"
					@click="goGoodsList(item.categoryId)">
					<view class="sort_sub_img">
						<image :src="item.categoryImage" mode="aspectFit"></image>
					</view>
					<view class="sort_sub_name">{{ filterFun(item.categoryName) }}</view>
				</view>
				<view class="see_more_wrap" @click="toAllSort(sort_obj.categoryId)" v-if="sort_obj.categoryList.length > 7">
					<view class="more_icon_circle">
						<image :src="imgUrl + 'index/more.png'" mode="aspectFit" class="more_icon"></image>
					</view>
					<view class="see_more_text">查看更多</view>
				</view>
			</view>
			<view class="sort_sub_goods">
				<goods-item-v :goods_info="item" :show_sale="false" :icon_type="1" :border_radius="8" :height="225"
					v-for="(item, index) in sort_obj.goodsList" :key="index"></goods-item-v>
			</view>
		</view>

		<!-- 空首页装修 -->
		<view class="empty_sort_page" v-if="deco_info == null && tab_index == 0" style="padding-top: 25vh;">
			<image :src="imgUrl + 'empty_fitUp.png'" mode="aspectFit" class="empty_img"></image>
			<view class="empty_text">首页暂未装修</view>
		</view>
		<!-- 专题空页面 -->
		<view class="empty_sort_page"
			v-if="is_show_top == false && deco_info.length == 0 && noData == false && noDatas == false"
			style="padding-top: 25vh;height:100vh;">
			<image :src="imgUrl + 'empty_fitUp.png'" mode="aspectFit" class="empty_img"></image>
			<view class="empty_text">{{ topicNotExit ? '专题页不存在' : '专题页暂未装修' }}</view>
		</view>
		<!-- 分类空页面 -->
		<view class="empty_sort_page" v-if="tab_index > 0 && is_show_empty == true">
			<image :src="imgUrl + 'empty_goods.png'" mode="aspectFit" class="empty_img"></image>
			<view class="empty_text">该分类暂无相关商品</view>
		</view>

		<!-- 分类空商品 -->
		<view class="empty_sort_page" v-if="tab_index > 0 && is_show_empty_goods == true && is_show_empty == false"
			style="padding-top: 260rpx;">
			<image :src="imgUrl + 'empty_goods.png'" mode="aspectFit" class="empty_img"></image>
			<view class="empty_text">该分类暂无相关商品</view>
		</view>

		<!-- 首页开屏框 start -->
		<view :class="isCookie == true ? 'open_screen show-dialog' : 'open_screen hide-dialog'"
			v-if="isCookie == true && home_page_img && home_page_img.length > 0 && home_page_img[0].imgUrl"
			@touchmove.stop.prevent="() => { }">
			<view class="open_screen_con" @click="gotoGoods_detail('home')">
				<view class="con_img" @click.stop="close_openScreen">
					<image :src="openscnImg"></image>
				</view>
				<image class="open_screen_con_img image_mode_fill_h5" mode="aspectFit" :src="home_page_img[0].imgUrl"
					:style="{ width: width + 'rpx', 'height': height + 'rpx' }">
				</image>
			</view>
		</view>
		<!-- 开屏框 end -->
	</view>
</template>

<script>
import Comment from '@/components/decoTemplate/comment.vue';
import cube from '@/components/decoTemplate/cube.vue';
import hotArea from '@/components/decoTemplate/hotArea.vue';
import goodsItemV from '@/components/goods_item_v.vue';
import sortList from '@/components/index-sort-list.vue';
import jyfParser from '@/components/jyf-parser/jyf-parser.vue';
import uniNoticeBar from '@/components/uni-notice-bar/uni-notice-bar.vue';
import uniSwiperDot from '@/components/uni-swiper-dot/uni-swiper-dot.vue';
import {
getSceneParam,
quillEscapeToHtml
} from '@/utils/common.js';
import filters from "@/utils/filter.js";
import {
mapState
} from 'vuex';
import CombinationWrap from '../decoTemplate/CombinationWrap.vue';
import Match from '../decoTemplate/Match.vue';
import Media from '../decoTemplate/Media.vue';
import Nav from '../decoTemplate/Nav.vue';
import RecommendWrap from '../decoTemplate/RecommendWrap.vue';
import RichTextC from '../decoTemplate/RichText.vue';
import tabMenu from './index-tab-menu.vue';
export default {
	data() {
		return {
			imgUrl: process.env.VUE_APP_IMG_URL,
			titleNViewBackground: '',
			swiperCurrent: 0,
			//wx-12-start
			// #ifdef MP
			menuButtonHeight: uni.getMenuButtonBoundingClientRect().height + 'px',
			menuButtonTop: uni.getMenuButtonBoundingClientRect().top + 'px',
			// #endif
			//wx-12-end
			swiperCurrent2: 0,
			swiperLength: 0,
			carouselList: [],
			goodsList: [],
			current: 0, //轮播图指示点
			dotsStyles: {
				selectedBackgroundColor: '#fff',
				width: 8,
				height: 8,
				selectedBorder: 'none',
				backgroundColor: 'rgba(255,255,255,0.4)',
				border: 'none',
				bottom: 8
			},
			navSwiperCurrent: 0, //导航组件轮播下标
			navDotsStyles: {
				selectWidth: 14,
				selectedBackgroundColor: 'var(--color_main)',
				selectedBorder: 'none',
				selectedOpacity: 0.75,
				width: 4,
				height: 4,
				backgroundColor: 'var(--color_main)',
				opacity: 0.2,
				border: 'none',
				bottom: 5,
				margin: '0 6rpx'
			},
			noticeImg1: process.env.VUE_APP_IMG_URL + 'index/notice1.png',
			noticeImg2: process.env.VUE_APP_IMG_URL + 'index/notice2.png',
			// deco_info:[] ,//首页装修数据
			// home_page_img:[] , //首页开屏图列表
			dataObj: {}, //完整版首页装修数据
			border_radius: '', //顶部轮播背景圆角
			broadcastData1: [], //公告1滚动文字
			broadcastData2: [], //公告2滚动文字
			broadcastStyle1: { //滚动文字样式设置
				speed: 1, //每秒30px
				font_size: "24", //字体大小(rpx)
				text_color: "#666", //字体颜色
				back_color: "linear-gradient(to right,rgba(250,244,244,0.2) 0%, rgba(255,244,244,1) 50%, rgba(250,244,244,0.2) 100%);", //背景色
			},
			broadcastStyle2: { //滚动文字样式设置
				speed: 1, //每秒30px
				font_size: "24", //字体大小(rpx)
				text_color: "#fff", //字体颜色
				back_color: "#3A3A3A", //背景色
			},
			telImg: process.env.VUE_APP_IMG_URL + 'index/mobile.png',
			sort_nav_list: [], //顶部分类列表
			sort_obj: {}, //二级分类列表+分类商品列表
			tab_index: 0, //分类切换下标
			is_show_index: true,
			adArr: [], //公告数组
			icon2: process.env.VUE_APP_IMG_URL + 'index/add2.png',
			icon3: process.env.VUE_APP_IMG_URL + 'index/add.png',
			icon4: process.env.VUE_APP_IMG_URL + 'index/add3.png',
			icon5: process.env.VUE_APP_IMG_URL + 'index/stop.png',
			isCookie: false,
			storeIsCookie: false,
			openscnImg: process.env.VUE_APP_IMG_URL + 'index/close_screen.png',
			isShowTab: true, //是否显示顶部分类
			sortLen: '', //二级分类长度
			isShow: true,
			is_show_empty: false, //是否展示分类空页面
			pure_bg_color: '', //顶部栏有弧度纯色
			border_radius1: '', //推荐商品二角度设置
			border_radius2: '', //推荐商品三角度设置
			border_radius3: '',
			top_bg: 'var(--color_main)', //顶部状态栏颜色
			noData: false, //暂无数据
			noDatas: true, //暂无数据
			is_show_empty_goods: false, //是否展示分类空商品页面
			// #ifdef APP-PLUS || H5
			isIos: uni.getSystemInfoSync().platform == 'ios', //是否ios手机
			// #endif
			goods_info: {},
			swiperIndex1: 1,
			filters,
			showState: false,
			stateBarHeight: 20,
			nav_left_icon: 'back', //底部tab进入的话为空，否则为back
			navBarColor: {
				backgroundColor: 'rgba(255,255,255,0)',
				color: '#fff'
			},
		};
	},
	components: {
		tabMenu,
		sortList,
		uniSwiperDot,
		goodsItemV,
		jyfParser,
		uniNoticeBar,
		RichTextC,
		Nav,
		cube,
		hotArea,
		CombinationWrap,
		RecommendWrap,
		Media,
		Match,
		Comment
	},
	props: [
		'is_show_top', 'deco_info', 'topic_name', 'is_from_found', 'is_from_founds', 'home_is_show_top_cat',
		'home_page_img',
		'width', 'height', 'store_width', 'store_height', 'store_page_img', 'topicNotExit', 'store_id', 'type',
		'transpar'
	],
	computed: {
		...mapState(['hasLogin', 'userInfo', 'msgNumState']),
		// dev_o2o_pickup-start
		...mapState('location', ['area_variant']),
		// dev_o2o-pickup-end
		client() {
			//app-2-start



			//app-2-end
			//wx-13-start
			// #ifdef MP
			return 'mp'
			// #endif
		},

		isDecoDataEmpty() {
			return JSON.stringify(this.deco_info) == '[]'
		}
	},
	async mounted() {
		this.getSortList();
		//首页装修开平图缓存
		let type = this.type != undefined && this.type ? this.type : ''
		let cookievalue = uni.getStorageSync('homeCookie' + type);
		if (!cookievalue) {
			this.isCookie = true;
			uni.setStorage({
				key: 'homeCookie' + type,
				data: new Date().getTime()
			});
		} else {
			if (new Date().getTime() * 1 - cookievalue * 1 > 24 * 60 * 60 * 1000) {
				this.isCookie = true;
				uni.setStorage({
					key: 'homeCookie' + type,
					data: new Date().getTime()
				});
			} else {
				this.isCookie = false;
			}
		}
		let systemInfo = uni.getSystemInfoSync()
		this.stateBarHeight = systemInfo.statusBarHeight
		// 推荐商品圆角设置

		// 更新直播观看数量
		uni.$on('updateWatchNum', (res) => {
			this.deco_info[res.decoIndex].data.info[res.index].viewingNum = Number(this.deco_info[res.decoIndex].data.info[res.index].viewingNum) + 1
		})
	},
	destroyed() {
		uni.$off('updateWatchNum');
	},
	watch: {
		deco_info(val) {
			console.log('decoInfo:before', val)
			this.noData = val && val.length == 0 ? true : false
			this.noDatas = val && val.length == 0 ? false : true
			let is_top_cat_nav = false
			val && val.map((item, index) => {
				if (item.type == 'top_cat_nav' && item.data && item.data.length > 0) {
					is_top_cat_nav = true
					let {
						swiperCurrent
					} = this
					if (item.data[swiperCurrent] && item.data[swiperCurrent].bg_color) {
						this.pure_bg_color = item.data[swiperCurrent].bg_color;
						this.$emit('currentColor', this.pure_bg_color)
					} else {
						this.pure_bg_color = 'var(--color_main_bg)';
					}
					this.titleNViewBackground = 'linear-gradient(' + item.data[swiperCurrent]?.bg_color +
						' 0%,' + item.data[0]?.bg_color + ' 42%,#ffffff 100%)';
				}
				if (item.type == 'top_cat_nav') {
					var newList = []
					for (var i = 0; i < item.data.length; i++) {
						if (item.data[i].img) {
							newList.push(item.data[i])
						}
					}
					item.data = newList
				} else if (item.type == 'fuwenben' && item.is_show == true) {
					let translateText = quillEscapeToHtml(item.text);
					item.text = quillEscapeToHtml(translateText);
				}
			})
			console.log('decoInfo:after', val)
			if (!is_top_cat_nav) {
				this.pure_bg_color = 'var(--color_main)';
			}
		},
	},
	methods: {
		restoreTab() {
			if (this.$refs.tabmenu) {
				this.$refs.tabmenu.currIndex = 0
				this.getChildList([], 0, 0)
			}

		},
		filterFun: function (value) {
			if (value && value.length > 4) {
				value = value.substring(0, 4);
			}
			return value;
		},

		// px转rpx
		pxToRpx(px) {
			const screenWidth = uni.getSystemInfoSync().screenWidth
			return (750 * Number.parseInt(px)) / screenWidth
		},
		// rpx转px
		rpxToPx(rpx) {
			const screenWidth = uni.getSystemInfoSync().screenWidth
			return (screenWidth * Number.parseInt(rpx)) / 750
		},


		calCardSectionHeight(decoItem) {
			let { splitArrayNav } = this
			let height = 0
			let ceilHeight = Math.ceil(splitArrayNav(decoItem.data, decoItem.page_num)[0].length / 5)
			let imgMarginButtom = this.rpxToPx(10)
			let slideRpx = this.rpxToPx(decoItem.slide * 2)
			let textPx = this.rpxToPx(28)
			let textMarginButtom = this.rpxToPx(20)
			let ceil2Height = slideRpx + imgMarginButtom + textPx + textMarginButtom
			if (decoItem.style_set == 'nav' && decoItem.icon_set == 'up' && decoItem.is_show) {
				height = this.pxToRpx(ceil2Height) * ceilHeight
				return (height + this.pxToRpx(12)) + 'rpx'
			} else if (decoItem.style_set == 'nav' && decoItem.icon_set == 'no-icon' && decoItem.is_show) {
				height = Math.ceil(splitArrayNav(decoItem.data, decoItem.page_num)[0].length / 5) * 56 + 40
			} else if (decoItem.style_set == 'nav' && decoItem.icon_set == 'left' && decoItem.is_show) {
				height = (Math.ceil(splitArrayNav(decoItem.data, decoItem.page_num)[0].length / 5) * 126 + 40)
			} else {
				height = (Math.ceil(splitArrayNav(decoItem.data, decoItem.page_num)[0].length / 2) * 180 + 40)
			}
			return (height + 100) + 'rpx'
		},


		//跳转商品列表
		goGoodsList(categoryId) {
			this.$Router.push({
				path: '/standard/product/list',
				query: {
					categoryId
				}
			})
		},
		onScroll(res) {
			if (res.scrollTop > 100) {
				this.navBarColor.backgroundColor = 'rgb(255,255,255)'
				this.navBarColor.color = '#333'
			} else {
				this.navBarColor.backgroundColor = 'rgba(255,255,255,0)'
				this.navBarColor.color = '#fff'
			}
		},
		// 扫描二维码
		scanCode(type) {
			let _this = this
			uni.scanCode({
				onlyFromCamera: true,
				success: function (res) {
					let regExp = /^([0-9a-zA-Z]\-?)+$/; //返回的登录code格式
					if (res.result && regExp.test(res.result)) {
						// 扫码登录
						_this.$Router.push({
							path: '/pages/public/codeLogin',
							query: {
								u: res.result
							}
						})
					} else {
						let producPathExp = /standard\/product\/detail/g
						if (res.result && producPathExp.test(res.result)) {
							let productId = getSceneParam(res.result, 'productId')
							_this.$Router.push({
								path: '/standard/product/detail',
								query: {
									productId
								}
							})
						} else {
							_this.$api.msg('未识别二维码，请稍后重试')
						}
					}
				},
				fail(err) {
					_this.$api.msg('扫码失败')
				}
			});
		},
		// 返回上一页
		toBack() {
			this.$Router.back(1)
		},
		//轮播图切换修改背景色
		swiperChange(e) {
			const index = e.detail.current;
			this.swiperCurrent = index;
			this.deco_info && this.deco_info.map(item => {
				if (item.type == 'top_cat_nav' && item.data && item.data.length) {
					this.pure_bg_color = item.data[index].bg_color
					this.$emit('currentColor', this.pure_bg_color)
					if (item.swiper_bg_style == 1) {
						this.titleNViewBackground = item.data[index].bg_color
					} else {
						this.titleNViewBackground = 'linear-gradient(' + item.data[index].bg_color + ' 0%,' +
							item.data[index].bg_color + ' 42%,#ffffff 100%)'
					}
				}
			})
		},
		// 短视频轮播
		swiperChange2(e) {
			this.swiperIndex1 = e.detail.current
		},
		// 轮播图模块切换下标
		swiperChangeIndex(e) {
			this.swiperCurrent2 = e.detail.current
		},
		// 导航切换轮播下标
		navSwiperChangeIndex(e) {
			this.navSwiperCurrent = e.detail.current
		},
		// 获取分类列表
		getSortList() {
			let param = {}
			param.url = 'v3/goods/front/goods/category/topCategory'
			param.method = 'GET'
			this.$request(param).then(res => {
				if (res.state == 200) {
					this.sort_nav_list = res.data
					this.sort_nav_list.unshift({
						categoryName: '首页'
					})
				}
			})
		},
		// 获取二级分类及分类商品列表
		getChildList(list, index, categoryId) {
			if (this.tab_index != index) {
				// 切换tab回到顶部
				uni.pageScrollTo({
					scrollTop: 0,
					duration: 0
				})
				this.sort_list = [];
			}
			this.tab_index = index

			this.$emit('currentColor', this.tab_index == 0 ? this.pure_bg_color : this.top_bg)

			if (index > 0) {
				this.sort_obj = list
				this.tab_current = 1
				if (this.sort_obj.categoryList.length > 0) {
					this.sortLen = this.sort_obj.categoryList.length
					this.is_show_empty = false
				} else {
					this.is_show_empty = true
				}

				if (this.sortLen > 9) {
					this.sort_obj.categoryList = this.sort_obj.categoryList.slice(0, 9)
				}
				if (this.sort_obj.goodsList.length > 0) {
					this.is_show_empty_goods = false
				} else {
					this.is_show_empty_goods = true
				}
				this.is_show_index = false
				this.tab_categoryId = categoryId
				this.tab_hasMore = this.$checkPaginationHasMore(list.pagination)
			} else {
				this.is_show_index = true
				this.swiperChange({
					detail: {
						current: 0
					}
				});
			}
			this.isShow = false
			this.isShow = true
		},
		// 拨打客服电话
		callUp(tel) {
			uni.makePhoneCall({
				phoneNumber: tel.toString()
			})
		},

		// 相关跳转
		skipTo(type, item, extra) {
			console.log('>>>skipTo', type, item, extra)
			let { url, info } = item
			if (type == 'url') { //跳转链接地址
				if (!url) {
					return;
				}
				// #ifdef H5
				window.open(url)
				// #endif
				//app-3-start



				//app-3-end
				//wx-14-start
				// #ifdef MP
				this.$Router.push({
					path: '/pages/index/skip_to',
					query: {
						url
					}
				})
				// #endif
				//wx-14-end
			} else if (type == 'goods') { //跳转商品详情页
				this.$Router.push({
					path: '/standard/product/detail',
					query: {
						productId: info.productId ?? info.defaultProductId,
						goodsId: info.goodsId
					}
				})
			} else if (type == 'category') { // 分类列表
				this.$Router.push({
					path: '/standard/product/list',
					query: {
						categoryId: info.categoryId
					}
				})
			} else if (type == 'keyword') { // 关键词
				if (this.store_id) {
					this.$Router.push({
						path: '/standard/product/list',
						query: {
							keyword: url,
							source: 'search',
							storeId: this.store_id
						}
					})
				} else {
					this.$Router.push({
						path: '/standard/product/list',
						query: {
							keyword: url,
							source: 'search'
						}
					})
				}
			} else if (type == 'topic') { //跳转专题页
				this.$Router.push({
					path: '/pages/index/topic',
					query: {
						id: (info.decoId ? info.decoId : info.id),
					}
				})
			} else if (type == 'o2o_topic') { //跳转o2o专题页
				this.$Router.push({
					path: '/pages/index/topic',
					query: {
						id: (info.decoId ? info.decoId : info.id),
						type: 'o2o_topic'
					}
				})
			} else if (type == 'brand_home') { //品牌列表
				this.$Router.push('/pages/public/brand')
			} else if (type == 'seckill') { //秒杀
				this.$Router.push({
					path: '/standard/seckill/seckill',
					query: {
						seckillId: info.seckillId
					}
				})
			} else if (type == 'ladder_group') { //阶梯团
				this.$Router.push('/standard/ladder/index/index')
			} else if (type == 'presale') { //预售入口页
				this.$Router.push('/standard/presale/index/list')
			} else if (type == 'voucher_center') { //优惠券领券中心
				this.$Router.push('/standard/coupon/couponCenter')
			} else if (type == 'point') { //积分商城首页
				this.$Router.push('/standard/point/index/index')
			} else if (type == 'svideo_center') { //短视频列表
				this.$Router.push('/extra/svideo/svideoList')
			} else if (type == 'live_center') { //直播列表
				this.$Router.push('/extra/live/liveList')
			} else if (type == 'spreader_center') { //推手中心
				if (!this.hasLogin) {
					this.$emit('needLogin')
				} else {
					console.log('>>>jump')
					this.$Router.push('/extra/tshou/index/index')
					return
				}
			} else if (type == 'live') { //直播播放页面
				this.$livePlayNav({
					live_id: item.liveId,
					decoIndex: extra.decoIndex,
					index: extra.index,
				})
			} else if (type == 'svideo') { //短视频播放页面
				let path = item.videoType == 1 ? '/extra/svideo/svideoPlay' : '/extra/graphic/graphicDetail'
				this.$Router.push({
					path,
					query: {
						video_id: item.videoId,
						label_id: item.labelId,
						author_id: item.authorId
					}
				})
			} else if (type == 'spell_group') {
				this.$Router.push('/standard/pinGroup/index/index')
			} else if (type == 'sign_center') {
				if (!this.hasLogin) {
					this.$emit('needLogin')
				} else {
					this.$Router.push('/standard/signIn/signIn')
				}
			} else if (type == 'rank') {
				this.$Router.push('/standard/rank/aggr')
			} else if (type == 'draw') {
				if (!this.hasLogin) {
					this.$emit('needLogin')
				} else {
					this.$Router.push({
						path: '/standard/lottery/detail',
						query: {
							drawId: info.drawId
						}
					})
				}
			} else if (type == 'store_list') { //店铺街
				this.$Router.push('/standard/store/list')
			} else if (type == 'store') {
				this.$Router.push(`/standard/store/shopHomePage?vid=${info.storeId}`)
			}
		},
		// 跳转商品详情页
		toGoodsDetail(productId, goodsId) {
			this.$Router.push({
				path: '/standard/product/detail',
				query: {
					productId,
					goodsId
				}
			})
		},
		// 跳转消息列表页
		toMsg() {
			if (!this.hasLogin) {
				this.$emit('needLogin')
			} else {
				this.$Router.push('/standard/chat/list')
			}
		},
		// 跳转全部分类页
		toAllSort(cateId) {
			let app = getApp()
			app.globalData.cateId = cateId
			this.$Router.pushTab('/pages/category/category')
		},
		// 加入购物车
		addCart(productId, goodsId, item) {
			let _this = this;
			this.goods_info = item
			if (this.userInfo.member_access_token) {
				let param = {}
				param.url = 'v3/business/front/cart/add'
				param.method = 'POST'
				param.data = {
					productId: productId,
					number: 1
				}
				this.$request(param).then(res => {
					if (res.state == 200) {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
						_this.cartUpdate();
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 700
						})
					}
				})
			} else {
				let cart_list = {
					storeCartGroupList: [{
						promotionCartGroupList: [{
							cartList: [{
								buyNum: 1,
								goodsId: this.goods_info.goodsId,
								productId: this.goods_info.productId || this
									.goods_info
									.defaultProductId,
								productImage: this.goods_info.goodsPic ? this
									.goods_info
									.goodsPic : this.goods_info.goodsImage,
								goodsName: this.goods_info.goodsName,
								isChecked: 1,
								productPrice: this.goods_info.goodsPrice,
								// productStock: this.goods_info.productStock
							}],
						}],
						storeId: this.goods_info.storeId,
						storeName: this.goods_info.storeName,
						checkedAll: true
					}],
					checkedAll: true,
					invalidList: []
				}

				let local_cart_list = uni.getStorageSync('cart_list') //购物车列表本地缓存
				if (local_cart_list) {
					let tmp_list1 = []
					let tmp_list2 = []
					cart_list.storeCartGroupList.forEach(item => {
						item.promotionCartGroupList.forEach(item1 => {
							item1.cartList.forEach(item2 => {
								local_cart_list.storeCartGroupList.forEach(v => {
									v.promotionCartGroupList.forEach(v1 => {
										v1.cartList.forEach(v2 => {
											if (v2.productId ==
												item2
													.productId && v
														.storeId ==
												item
													.storeId) {
												tmp_list1.push(
													v)
											}
										})
										tmp_list2 = local_cart_list
											.storeCartGroupList.filter(
												v => {
													return v.storeId ==
														item
															.storeId
												})
									})
								})
							})
						})
					})
					if (tmp_list1.length > 0 && tmp_list2.length > 0) { //同一店铺同一商品
						local_cart_list.storeCartGroupList.map(item => {
							item.promotionCartGroupList.map(item1 => {
								item1.cartList.map(item2 => {
									if (item2.productId == this.goods_info.productId &&
										item
											.storeId == this.goods_info.storeId) {
										item2.buyNum += 1
									}
								})
							})
						})
					} else if (tmp_list1.length == 0 && tmp_list2.length > 0) { //同一店铺不同商品
						local_cart_list.storeCartGroupList.map(item => {
							if (item.storeId == this.goods_info.storeId) {
								item.promotionCartGroupList.map(item2 => {
									item2.cartList.push(cart_list.storeCartGroupList[0]
										.promotionCartGroupList[0].cartList[0])
								})
							}
						})
					} else { //不同店铺不同商品
						local_cart_list.storeCartGroupList.push(cart_list.storeCartGroupList[0])
					}

					// 未登录购物车展示数据
					uni.setStorage({
						key: 'cart_list',
						data: local_cart_list,
						success: function () {
							//更新购物车数量和购物车数据
						}
					});

				} else {
					uni.setStorage({
						key: 'cart_list',
						data: cart_list,
						success: function () {
							//更新购物车数量和购物车数据
						}
					});
				}
				uni.showToast({
					title: '加入购物车成功！',
					icon: 'none'
				})
				_this.cartUpdate();
			}
		},
		cartUpdate() {
			this.$emit('cartUpdate');
		},
		//关闭首页广告屏
		close_openScreen() {
			this.isCookie = false;
		},
		//关闭首页广告屏
		close_storeOpenScreen() {
			this.storeIsCookie = false;
		},
		//点击广告屏跳转到详情页面
		gotoGoods_detail(type) {
			if (type == 'home') {
				this.isCookie = false;
				let osValue = this.home_page_img[0];
				this.$diyNavTo(osValue, 'home');
			} else {
				this.storeIsCookie = false
				let osValue = this.store_page_img[0];

				this.$diyNavTo(osValue, this.store_id);
			}
		},
		getMoreData() {
			if (this.tab_index == 0) {
				return
			}
			if (this.tab_hasMore) {
				this.tab_current++
				this.$request({
					url: 'v3/goods/front/goods/category/list?categoryId1=' + this.tab_categoryId,
					data: {
						current: this.tab_current
					}
				}).then(res => {
					if (res.state == 200) {
						this.sort_obj.goodsList = this.sort_obj.goodsList.concat(res.data.goodsList)
						this.tab_hasMore = this.$checkPaginationHasMore(res.data.pagination)
					}
				})
			}
		},
		//导航组件多屏滚动展示数据处理
		splitArrayNav(arr, groupSize) {
			var result = [];
			for (var i = 0; i < arr.length; i += groupSize) {
				result.push(arr.slice(i, i + groupSize));
			}
			return result;
		},
	},
	// #ifndef MP
	// 标题栏input搜索框点击
	onNavigationBarSearchInputClicked: async function (e) {
		this.$Router.push('/pages/search/search')
	},
	//点击导航栏 buttons 时触发
	onNavigationBarButtonTap(e) {
		const index = e.index;
		if (index === 0) {
			this.$api.msg('点击了扫描');
		} else if (index === 1) {
			//app-4-start








			//app-4-end
			this.$Router.push('/pages/notice/notice')
		}
	},
	// #endif
}
</script>

<style lang="scss">
::v-deep.uni-swiper-wrapper {
	border-radius: 5px !important;
}

// 开屏 -- start
.container3 {
	width: 100%;
	margin: 0 auto;
	position: relative;
}

.fixed_top_mp {
	position: fixed;
	top: 0;
	z-index: 100;
	background: #fff;
}

.deco_loading {
	display: none
}

.deco_loading_style {
	position: fixed;
	height: 100vh;
	top: 0;
	background: #fff;
	left: 0;
	right: 0;
	width: 750rpx;
	margin: 0 auto;

	// justify-content: flex-start;
	// padding-top: 30vh;
	text {
		font-size: 28rpx;
		margin-top: 35rpx;
		color: #A8ABAD;
		/* #ifdef H5 */
		margin-bottom: calc(var(--window-bottom) + 20rpx);
		/* #endif */
	}

	.loading_more_icon {
		width: 96rpx;
		height: 96rpx;
	}
}

.fixed_top {
	position: fixed;
	top: 0;
	z-index: 100;

	.fixed_bar {
		width: 100%;
		background: linear-gradient(90deg, #6984a4 0%, #3a4b5c 100%);
	}
}

.show-dialog {
	animation: 100ms showDialog linear forwards;
}

.hide-dialog {
	animation: 100ms hideDialog linear forwards;
}

@keyframes hideDialog {
	0% {
		opacity: 1;
	}


	25% {
		opacity: 0.75;
	}


	50% {
		opacity: 0.5;
	}


	75% {
		opacity: 0.25;
	}


	100% {
		opacity: 0;
	}
}

@keyframes showDialog {
	0% {
		opacity: 0;
	}


	25% {
		opacity: 0.25;
	}


	50% {
		opacity: 0.5;
	}


	75% {
		opacity: 0.75;
	}


	100% {
		opacity: 1;
	}
}

.container3 .open_screen {
	width: 750rpx;
	height: calc(100vh);
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	margin: 0 auto;
	background-color: rgba(0, 0, 0, 0.3);
	z-index: 99999;
}

.container3 .open_screen .open_screen_con {
	maring: 0 auto;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	border-radius: 15rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.container3 .open_screen .open_screen_con .open_screen_con_img {
	max-width: 580rpx !important;
	max-height: 776rpx !important;
	background-size: contain;
	border-radius: 15rpx;
}

.container3 .open_screen .open_screen_con .con_img {
	width: 58rpx;
	height: 58rpx;
	position: absolute;
	top: -58rpx;
	right: -58rpx;
}

.open_screen_con .con_img image {
	width: 100%;
	height: 100%;
}

// 开屏 -- end

/* #ifdef H5 */
.deco_wrap {
	margin-top: 158rpx !important;
}

/* #endif */
//wx-9-start
/* #ifdef MP */
.deco_wrap {
	// margin-top: calc(150rpx + var(--status-bar-height)) !important;
	padding-top: 0rpx;
}

//wx-9-end
.deco_wrap2 {
	margin-top: 108rpx;
}

/* #endif */
//app-5-start






//app-5-end
.container3 {
	overflow-x: hidden;
	background-color: #F5F5F5;
}

//app-6-start








//app-6-end
.mp-search-box_fixed {
	position: fixed;
	z-index: 9999;
	width: 750rpx;
	height: 100rpx;
	background: #fff;
}

page {
	.topic_top {
		padding-top: 0 !important;
	}



	.carousel-section {
		padding: 20rpx;
		box-sizing: border-box;
		background-color: #FFFFFF;

		.titleNview-placing {
			padding-top: 0;
			height: 0;
		}

		.carousel {
			.carousel-item {
				padding: 0;
			}
		}

		.swiper-dots {
			left: 45upx;
			bottom: 40upx;
		}
	}
}





page {
	background: #f5f5f5;
}

.m-t {
	margin-top: 16upx;
}

/* 头部 轮播图 */
.carousel-section {
	position: relative;
	padding-top: 10px;

	.titleNview-placing {
		height: var(--status-bar-height);
		padding-top: 88px;
		box-sizing: content-box;
	}

	.titleNview-background {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 200upx;
		transition: .4s;
		border-radius: 0 0 30rpx 30rpx;
	}
}

.carousel {
	width: 100%;

	.carousel-item {
		width: 100%;
		height: 100%;
		// padding: 0 28upx !important;
		overflow: hidden;
	}

	image {
		width: 100%;
		height: 100%;
		border-radius: 10upx;
		overflow: hidden;
	}

	.itemImg {
		width: 100%;
		height: 100%;
		background-position: center center;
		background-repeat: no-repeat;
		background-size: contain;
		border-radius: 10upx;
	}

	.carousel_img {
		width: 100%;
		height: 100%;
		background-position: center top;
		background-repeat: no-repeat;
		background-size: contain;
	}
}

.swiper-dots {
	display: flex;
	position: absolute;
	left: 60upx;
	bottom: 15upx;
	width: 72upx;
	height: 36upx;
	background-image: url(data:image/png;base64,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);
	background-size: 100% 100%;

	.num {
		width: 36upx;
		height: 36upx;
		border-radius: 50px;
		font-size: 24upx;
		color: #fff;
		text-align: center;
		line-height: 36upx;
	}

	.sign {
		position: absolute;
		top: 0;
		left: 50%;
		line-height: 36upx;
		font-size: 12upx;
		color: #fff;
		transform: translateX(-50%);
	}
}

.notice_box {
	padding: 20rpx 20rpx 0 20rpx;
	background: #FFFFFF;
	// border-radius: 14rpx;

	// margin-bottom:20rpx;
	.notice_wrap1 {
		width: 100%;
		height: 80rpx;
		background-color: #fff;
		display: flex;
		align-items: center;
		border-radius: 14rpx;

		// border-radius: 6px;
		.notice_img1 {
			width: 127rpx;
			height: 80rpx;
			border-radius: 6px 0 0 6px;
		}

		.notice_content_wrap {
			font-size: 28rpx;
			font-weight: 600;
			width: 530rpx;
			white-space: nowrap;
			text-overflow: ellipsis;
			overflow: hidden;
			margin-left: -16rpx;

			.notice_content_title {
				color: #E1261C;
			}

			.notice_content {
				color: #666666;
			}
		}

		.notice_wrap1_line {
			width: 1rpx;
			height: 34rpx;
			background-color: rgba(0, 0, 0, 0.1);
			margin-left: 6rpx;
		}

		.notice_more {
			width: 80rpx;
			text-align: center;
			font-size: 29rpx;
			color: #2E2E2E;
			font-weight: 600;
		}
	}

	.notice_wrap2 {
		width: 100%;
		height: 80rpx;
		// margin-bottom: 20rpx;
		background-color: #3A3A3A;
		display: flex;
		align-items: center;
		box-shadow: 1px 6px 19px 1px rgba(86, 86, 86, 0.1);

		// border-radius: 6px;
		.notice_img2 {
			width: 138rpx;
			height: 80rpx;
			border-radius: 6px 0 0 6px;
		}

		.notice_content_wrap2 {
			font-size: 26rpx;
			font-weight: 600;
			width: 510rpx;
			white-space: nowrap;
			text-overflow: ellipsis;
			overflow: hidden;
			color: #fff;
			margin-left: 5rpx;
		}

		.notice_wrap2_line {
			width: 1rpx;
			height: 38rpx;
			background-color: #fff;
			margin-left: 2rpx;
		}

		.notice_more {
			width: 80rpx;
			text-align: center;
			font-size: 26rpx;
			color: #fff;
			font-weight: 600;
		}
	}
}

.service_wrap {
	width: 100%;
	padding: 20rpx 20rpx 0;

	.service_wrap_main {
		display: flex;
		align-items: center;
		color: #333;
		font-size: 26rpx;
		padding: 20rpx;
		background-color: #fff;
		border-radius: 14rpx;

		image {
			width: 30rpx;
			height: 32rpx;
			margin-right: 10rpx;
		}
	}
}



.subline_wrap {
	padding: 30rpx 0;
	background-color: #fff;

	// margin: 20rpx 20rpx 0 20rpx;
	.subline {
		width: 100%;
		border-bottom: 1px dotted #fff;
	}
}

.carousel_bottom_wrap {
	background-color: #fff;
	display: flex;
	justify-content: center;

	.carousel_bottom {
		width: 100%;
		// margin-top: 20rpx;
		// margin-bottom: 20rpx;
		// padding-top: 20rpx;
	}
}

.recommend_goods_wrap {
	display: flex;
	justify-content: space-between;
	flex-wrap: wrap;
	padding: 20rpx 20rpx 0;
}

.carousel_img {
	// height:340rpx !important;
}

.blank_wrap {
	padding: 20rpx 20rpx 0;
}



.sort_sub_wrap {
	width: 100%;
	background: #F5F5F5;

	.sort_sub_top {
		margin: 20rpx 21rpx 0 21rpx;
		display: flex;
		border-radius: 10rpx;
		background-color: #fff;
		padding: 0 20rpx 20rpx 20rpx;
		flex-wrap: wrap;

		.sort_sub_item {
			display: flex;
			flex-direction: column;
			margin-right: 35rpx;
			justify-content: center;
			align-items: center;
			margin-top: 20rpx;

			.sort_sub_img {
				width: 106rpx;
				height: 106rpx;
				border-radius: 50%;
				margin-bottom: 20rpx;

				image {
					width: 106rpx;
					height: 106rpx;
					border-radius: 50%;
				}
			}

			.sort_sub_name {
				font-size: 24rpx;
				color: #333;
				font-weight: 600;
				width: 96rpx;
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
				word-break: break-all;
				text-align: center;
			}
		}
	}

	.sort_sub_top>view:nth-child(5n) {
		margin-right: 0 !important;
	}

	.sort_sub_goods {
		width: 100%;
		padding: 0 20rpx;
		box-sizing: border-box;
		display: flex;
		justify-content: space-between;
		flex-wrap: wrap;
		margin-top: 20rpx;
	}

	.sort_sub_goods>view:nth-child(2n) {
		margin-right: 0 !important;
	}
}

// 图片组合样式
.goods {
	border: none;
	background: #fff;
	margin-top: 0;
}

.index_block {
	width: 750rpx;
	clear: both;
	overflow: hidden;
	background: #fff;
	display: block;
}

.goods .content {
	background: #f0f2f5;
	clear: both;
	overflow: hidden;
	display: block;
}

.goods .goods-small.goods-item:nth-child(2n+1) {
	padding-right: 8rpx;
}

.goods-small.goods-item {
	overflow: hidden;
	float: left;
	width: 50%;
	box-sizing: border-box;
	padding-bottom: 8rpx;
	position: relative;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
}

.goods-item navigator {
	display: block;
	background: #fff;
}

.goods-item-pic {
	vertical-align: middle;
	line-height: 0;
	display: table-cell;
	text-align: center;
	width: calc(50vw - 30rpx);
	height: calc(50vw - 30rpx);
}

.goods-item-pic image {
	width: calc(50vw - 30rpx);
	height: calc(50vw - 30rpx);
}

.goods-small .goods-item-name {
	height: 66rpx;
	font-size: 26rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	word-break: break-word;
	color: #232326;
	margin-top: 10rpx;
	line-height: 33rpx;
	margin-bottom: 6rpx;
	padding: 0 8rpx;
}

.goods-item-price {
	color: #f23030;
	display: inline-block;
	padding: 0 10rpx 0 8rpx;
	position: relative;
	top: 2rpx;
	height: 50rpx;
	line-height: 50rpx;
}

.goods-item-price .yens {
	font-size: 26rpx;
}

.goods-item-price .bigprice {
	font-size: 32rpx;
	font-weight: bold;
	display: inline-block;
}

.goods-big.goods-item {
	overflow: hidden;
	float: left;
	width: 100%;
	box-sizing: border-box;
	padding-bottom: 8rpx;
	position: relative;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	padding: 16rpx 16rpx 0;
}

.goods-item-name {
	height: 66rpx;
	font-size: 26rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	word-break: break-word;
	color: #232326;
	margin-top: 10rpx;
	line-height: 34rpx;
	margin-bottom: 6rpx;
	padding: 0 8rpx;
}

.goods-big .goods-item-price {
	color: #f23030;
	display: inline-block;
	padding: 0 10rpx 0 8rpx;
	position: relative;
	top: 2rpx;
	height: 50rpx;
	line-height: 50rpx;
}

.goods-big .goods-item-pic image {
	width: 734rpx;
	height: 734rpx;
	padding: 0 8rpx;
}

.hide_title .goods-item .goods-item-name {
	display: none !important;
}

.hide_price .goods-item .goods-item-price {
	display: none !important;
}

.goods-list.goods-item .goods-item-pic {
	float: left;
	width: 214rpx !important;
	height: 214rpx !important;
}

.goods-list.goods-item {
	overflow: hidden;
	float: left;
	width: 100%;
	box-sizing: border-box;
	padding-bottom: 8rpx;
	position: relative;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	background: #fff;
	margin-bottom: 16rpx;
}

.goods-list.goods-item .goods-item-pic image {
	width: 214rpx !important;
	height: 214rpx !important;
}

.goods-list .goods-item-name {
	padding-top: 40rpx;
}

.goods .new-content .goods-item.goods-list .goods-item-name {
	padding-top: 10rpx;
}





.countdown {
	width: 100%;
	height: 49.2rpx;
	line-height: 49.2rpx;
	font-size: 39.4rpx;
}

.countdown .countdown-name {
	float: left;
	display: block;
	-webkit-transform: scale(0.8);
}

.countdown .countdown-main {
	display: block;
	-webkit-transform: scale(0.8);
}

.countdown .countdown-num {
	background-color: #000000;
	display: inline-block;
	padding: 0 0rpx;
	width: 25px;
	height: 32.2rpx;
	line-height: 32.2rpx;
}



.big_price {
	font-size: 34rpx;
}

.small_price {
	font-size: 24rpx;
}




.scan_img {
	width: 44rpx;
	height: 44rpx;
	position: absolute;
	left: 15rpx;
}

.scan_login {
	position: absolute;
	right: 110rpx;
	top: 50%;
	margin-top: -25rpx;
	z-index: 9;
	width: 48rpx;
	height: 48rpx;
	color: #666666;
	font-size: 48rpx;
}



.paddingTB20 {
	padding: 0 20rpx;
}

.no_margin_right {
	padding: 0px
}

.no_margin_right>view:nth-child(2n) {
	margin-right: 0 !important;
}

.no_margin_right2>view:nth-child(3n) {
	margin-right: 0 !important;
}

.see_more_wrap {
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
	margin-top: 14rpx;

	.more_icon_circle {
		width: 106rpx;
		height: 106rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		border-radius: 50%;
		background-color: #F8F8F8;
		margin-bottom: 20rpx;

		.more_icon {
			width: 52rpx;
			height: 14rpx;
		}
	}

	.see_more_text {
		font-size: 24rpx;
		color: #333;
		font-weight: 600;
	}
}

.carousel-section ::v-deep .uni-swiper__warp {
	// margin-top: 158rpx !important;
}

//app-8-start







//app-8-end
/* #ifdef H5 */
.carousel-section ::v-deep .uni-swiper__warp {
	margin-top: -2rpx !important;
	padding-top: 8rpx !important;
}

/* #endif */


.mp-search-box ::v-deep .ser-input ::v-deep .uni-input-wrapper ::v-deep .uni-input-input {
	background-color: #fff;
}

.search_input {
	text-align: left;
}

.rec_goods_wrap {
	width: 750rpx;
	display: flex;
	justify-content: space-between;
	flex-wrap: wrap;
}

.city_wrap {
	font-size: 30rpx;
	display: flex;
	align-items: center;
	color: #fff;
	flex-shrink: 0;
	max-width: 58px;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
	display: inline-block;
}

.top_icon {
	width: 24rpx;
	height: 24rpx;
	margin-left: 11rpx;
	margin-right: 17rpx;
	margin-top: 2rpx;
	flex-shrink: 0;
}

.deco_wrap {
	width: 750rpx;
}


/* ----TAB切换：---- */
.tab_nav {
	margin-top: 100rpx;
}

.tab_nav_scroll {
	white-space: nowrap;
}

.tab_nav_block {
	display: inline-block;
	width: 25%;
	text-align: center;
}

.tab_nav_block_t {
	color: #2D2D2D;
	font-size: 28rpx;
	padding-bottom: 14rpx;
	margin: 0 50rpx 4rpx 50rpx
}

.tab_nav_block_on {
	color: #333333;
	font-weight: 700;
	font-size: 32rpx;
	margin-bottom: 8rpx;
}

// 短视频3d轮播
.swiper-block {
	width: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	height: 346rpx !important;
	margin: 0
}

.optionBox {
	position: relative;
}

.slide-image {
	height: 320rpx;
	width: 520rpx;
	border-radius: 9rpx;
	box-shadow: 0px 0px 30rpx rgba(0, 0, 0, .2);
	margin: 0rpx 30rpx;
	z-index: 1;
}

.active1 {
	/* transform: scale(1.44); */
	background-position: center center;
	background-repeat: no-repeat;
	background-size: cover;
	transition: all .2s ease-in 0s;
	height: 100%;
	width: 100%;
	border-radius: 16rpx;
	animation: swiperMove1 .6s ease-in-out;
}

@keyframes swiperMove {
	from {
		height: 280rpx;
	}

	to {
		height: 345rpx;
	}
}

.active2 {
	background-position: center center;
	background-repeat: no-repeat;
	background-size: cover;
	width: 100%;
	height: 100%;
	border-radius: 16rpx;
	animation: swiperMove0 .6s ease-in-out
}

@keyframes swiperMove {
	from {
		height: 345rpx;
	}

	to {
		height: 280rpx;
	}
}

// 空页面
.empty_sort_page {
	width: 100%;
	// height: 100vh;
	background: #F5F5F5;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding-top: 340rpx;

	.empty_img {
		width: 380rpx;
		height: 280rpx;
		margin-bottom: 32rpx;
	}

	.empty_text {
		font-size: 26rpx;
		color: #999;
	}
}

.scan,
.message {
	margin-left: 18rpx;
	display: flex;
	align-items: center;
	flex-direction: column;
	flex-shrink: 0;

	image {
		width: 34rpx;
		height: 34rpx;
	}

	text {
		color: white;
		font-size: 20rpx;
		line-height: 22rpx;
		margin-top: 6rpx;
	}
}

.search_con {
	position: relative;
	flex: 1;
}

.svideo_person_num {
	width: 40rpx;
	height: 40rpx;
	border-radius: 0 0 50% 0;
}


.swiper-item {
	color: #fff;
	overflow: hidden;
	left: 40rpx !important;
	right: 20rpx;
	top: 20rpx;
	bottom: 20rpx;
	width: 80% !important;
	height: 280rpx !important;
	// transform: translate(80%, 0px) translateZ(0px);
}

.swiper-item1 {
	color: #fff;
	box-sizing: border-box;
	height: 346rpx;
	width: 346rpx !important;
	top: 0rpx;
	bottom: 0rpx;
	overflow: hidden;
	left: 80rpx;
}













// 专题页头部
.topic_top_bar {
	width: 750rpx;
	height: 60rpx;
	display: flex;
	// align-items: center;
	justify-content: flex-start;
	font-size: 30rpx;
	color: #2d2d2d;
	z-index: 99999;
	margin: 0 auto;
	background: linear-gradient(90deg, #6984a4 0%, #3a4b5c 100%);

	.topic_top_bar_bix {
		display: flex;
		align-items: center;
		justify-content: flex-start;
	}

	image {
		width: 32rpx;
		height: 32rpx;
	}

	.topic_name {
		color: #fff;
		font-size: 35rpx;
		line-height: 35rpx;
	}
}



.svideo2_wrap ::v-deep .uni-scroll-view ::v-deep .uni-scroll-view ::v-deep .uni-scroll-view-content>view:nth-last-child(1) {
	margin-right: 0 !important;
}

.top_swiper_style1 {
	background: #FF1D1D;
}

.top_swiper_style2 {
	background: linear-gradient(#FC1D1C 0%, #FF7A18 42%, #fff 100%);
}

.svideo4_wrap ::v-deep .uni-scroll-view ::v-deep .uni-scroll-view ::v-deep .uni-scroll-view-content>view:nth-last-child(1) {
	margin-right: 0 !important;
}





.topic_back_icon {
	width: 60rpx;
	height: 38rpx;
	margin-left: 20rpx;
}




.carousel-section ::v-deep .uni-swiper__warp ::v-deep .uni-swiper__dots-box ::v-deep .uni-swiper__dots-item {
	width: 16rpx !important;
}

.deco_wrap_no_top_cat {
	/* #ifndef MP */
	margin-top: 100rpx !important;
	/* #endif */
	//app-9-start
	/* #ifdef APP-PLUS */
	margin-top: calc(var(--status-bar-height) + 100rpx) !important;
	/* #endif */
	//app-9-end
}



.active .tab_nav_block_t {
	color: var(--color_main);
	font-size: 30rpx;
	border-bottom: 2px solid var(--color_main);
	font-weight: bold
}







.o2o_store_box {
	padding: 0 20rpx;

	.o2o_store_box_img {
		width: 710rpx;
		height: 92rpx;
	}

	.o2o_store_box_one {
		width: 100%;
		border-radius: 20rpx;
	}
}
</style>