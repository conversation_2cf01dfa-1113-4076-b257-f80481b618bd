<template>
	<view class="home_top_con">
		<view class="home_top" :style="{background}" id="home_top">
			<view class="fixed_height" :style="{height:topHeight}"></view>
			
			<!-- #ifdef MP -->
			<view class="middle_height flex_row_start_center" :style="{height:middleHeight}">
				<text class="mp_title_name">莘古商城</text>
				
			</view>
			<!-- #endif -->
			
			<view class="mp-search-box">
				<image :src="imgUrl + 'search.png'" mode="aspectFit" class="search_img"></image>
				<view class="ser-input" @click.stop="toSearchPage">输入关键字搜索</view>
				<!-- app-start -->




				<!-- app-end -->
				<view class="msg_img">
					<image :src="imgUrl + 'message_index.png'" mode="aspectFit" @click="toMsg"></image>
					<view class="message_new" v-if="msgNumState >= 1">
						{{ msgNumState > 99 ? '99+' : msgNumState }}
					</view>
				</view>
			</view>
		</view>

		<view class="space_con" :style="{height:spaceHeight}">

		</view>

		<!-- app-start -->
		<view class="smegma" v-if="perm"></view>
		<view class="frame" v-if="perm">
			{{perm}}
			<view class="fra_title">摄像头权限说明</view>
			<view class="">
				申请摄像头拍摄权限以便您能通过扫一扫、上传照片或视频实现扫描二维码、识别商品、评价晒单、售后、发布种草服务。拒绝或取消授权不影响使用其他服务。
			</view>
		</view>
		<!-- app-end -->
	</view>

</template>
<script>
	// app-start
	import permision from "@/utils/permission";
	// app-end
	import {
mapState
} from 'vuex';

	const system = uni.getSystemInfoSync()

	// #ifdef MP
	const menu = uni.getMenuButtonBoundingClientRect()
	// #endif

	export default {
		props: {
			background: String,
			homeTabType: {
				type: Boolean
			},
			homeTopIndex: Number
		},
		components: {

		},
		data() {
			return {
				imgUrl: process.env.VUE_APP_IMG_URL,
				tab_index_hour: 1,
				spaceTmpHeight: 0,
				spaceHeight: 0,
				showFail: false,
				// app-start
				perm: false,
				showState: false,
				systemInfo: null, //手机端平台类型
				// app-end
			}
		},
		computed: {
			...mapState(['hasLogin','msgNumState']),
			topHeight() {
				return system.statusBarHeight + 'px'
			},
			middleHeight() {
				// #ifdef MP
				return `${menu.height + 10}px`
				// #endif
			},
			midleTop() {
				// #ifdef MP
				return `${menu.height + 10 -29}px`
				// #endif
			},
		},

		onShow() {

		},

		created() {
			this.selectQuery = uni.createSelectorQuery().in(this)
			},

		async mounted() {
			this.tab_index_hour = this.homeTopIndex
			this.queryEl()
			// app-start



			// app-end
		},

		destroyed() {},

		methods: {
			toSearchPage() {
				if (this.tab_index_hour == 2) {
					} else {
					this.$Router.push('/pages/search/search')
				}
			},

			queryEl() {
				setTimeout(() => {
					this.selectQuery.select('#home_top').boundingClientRect(res => {
						this.spaceTmpHeight = res.height
						this.spaceHeight = this.spaceTmpHeight + 'px'
					})
					this.selectQuery.exec()
				}, 200)

			},

			// 跳转消息列表页
			toMsg() {
				if (!this.hasLogin) {
					this.$emit('needLogin')
				} else {
					this.$Router.push('/standard/chat/list')
				}
			},
			// app-start
			async requestAndroidPermission(permisionID) {
				if (this.systemInfo == 'android') {
					setTimeout(() => {
						this.perm = true;
					}, 100)

					var result = await permision.requestAndroidPermission(permisionID)
					var strStatus

					if (result == 1) {
						setTimeout(() => {
							this.perm = false
						}, 101)
						this.scanCode()
						this.$forceUpdate()
					} else if (result == 0) {
						setTimeout(() => {
							this.perm = false
						}, 101)
						strStatus = "未获得授权"
					} else {
						setTimeout(() => {
							this.perm = false
						}, 101)
						strStatus = "被永久拒绝权限"
					}
				} else {
					this.scanCode()
				}

			},
			// 扫描二维码
			scanCode(type) {
				let _this = this
				uni.scanCode({
					onlyFromCamera: true,
					success: function(res) {
						let regExp = /^([0-9a-zA-Z]\-?)+$/; //返回的登录code格式
						if (res.result && regExp.test(res.result)) {
							// 扫码登录
							_this.$Router.push({
								path: '/pages/public/codeLogin',
								query: {
									u: res.result
								}
							})
						} else {
							let producPathExp = /standard\/product\/detail/g
							if (res.result && producPathExp.test(res.result)) {
								let productId = getSceneParam(res.result, 'productId')
								_this.$Router.push({
									path: '/standard/product/detail',
									query: {
										productId
									}
								})
							} else {
								_this.$api.msg('未识别二维码，请稍后重试')
							}
						}
					},
					fail(err) {
						_this.$api.msg('扫码失败')
					}
				});
			},
			// app-end
		},
	}
</script>
<style lang="scss">
	page {}

	.scan_login {
		position: absolute;
		right: 110rpx;
		top: 50%;
		margin-top: -25rpx;
		z-index: 9;
		width: 48rpx;
		height: 48rpx;
		color: #666666;
		font-size: 48rpx;
	}

	.space_con {
		width: 100%;
	}

	.home_top {
		position: fixed;
		top: 0;
		z-index: 20;
		background-color: var(--color_main);
	}

	.fixed_height {
		background: transparent;
	}

	.middle_height {
		background: transparent;
		padding: 0 35rpx;
		position: relative;

		.tab_index_hour {
			position: absolute;
			left: 0;
			right: 0;
			height: 100%;

			.tab_index_hour_body {
				height: 56rpx;
				border-radius: 28rpx;
				background: rgba(255, 255, 255, 0.3);

				.tab_item {
					height: 56rpx;
					line-height: 56rpx;
					padding: 0 22rpx;
					font-size: 30rpx;
					/* #ifdef MP */
					padding: 0 12rpx;
					font-size: 28rpx;
					max-width: 184rpx;
					/* #endif */
					color: #FFFFFF;
					border-radius: 28rpx;
					text-align: center;

					&.active {
						background: #FFFFFF;
						color: var(--color_main);
					}
				}
			}
		}
	}

	.mp_title_name {
		font-size: 35rpx;
		color: #fff;
	}

	.location {
		padding-left: 20rpx;
		height: 22px;
		margin: 2px 0;
		position: relative;

		.fail_state {
			position: absolute;
			bottom: -92rpx;
			width: 710rpx;
			height: 80rpx;
			background: rgba(0, 0, 0, 0.75);
			border-radius: 50rpx;
			left: 20rpx;
			z-index: 30;
			padding: 0 30rpx;
			padding-right: 20rpx;

			.manual_switch {
				width: 160rpx;
				height: 56rpx;
				background: #FF7300;
				border-radius: 28rpx;
				font-size: 26rpx;
				color: #FFFFFF;
				line-height: 56rpx;
				text-align: center;
				margin-right: 32rpx;
			}

			image {
				width: 48rpx;
				height: 48rpx;
			}

			.fail_state_text {
				font-weight: bold;
				font-size: 32rpx;
				color: #FFFFFF;
			}
		}

		.icon {
			width: 36rpx;
			height: 36rpx;
		}

		.location_text {
			font-weight: bold;
			font-size: 30rpx;
			color: #FFFFFF;
			margin-left: 10rpx;
			margin-right: 10rpx;
			margin-right: 20rpx;
			margin-bottom: 4rpx;
			max-width: 600rpx;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}

		.iconziyuan11 {
			transform: rotate(90deg);
			font-weight: bold;
			font-size: 20rpx;
			color: #FFFFFF;
		}

	}


	.mp-search-box {
		position: relative;
		width: 750rpx;
		height: 100rpx;
		padding: 0 20rpx 0 20rpx;
		display: flex;
		box-sizing: border-box;
		align-items: center;
		overflow: hidden;
		justify-content: space-between;

		.search_img {
			position: absolute;
			width: 31rpx;
			height: 31rpx;
			left: 40rpx;
			top: 50%;
			transform: translateY(-50%);
		}

		.ser-input {
			height: 65upx;
			line-height: 68upx;
			text-align: left;
			font-size: 28rpx;
			color: #999;
			border-radius: 20px;
			background: rgba(255, 255, 255, .6);
			padding-left: 62rpx;
			flex: 1;
			box-sizing: border-box;
			background-color: #fff;
		}

		.msg_img {
			height: 46rpx;
			margin-left: 20rpx;
			position: relative;

			image {
				width: 40rpx;
				height: 46rpx;
			}

			.message_new {
				min-width: 34rpx;
				height: 34rpx;
				background-color: #fff;
				position: absolute;
				/* #ifdef MP */
				top: -15rpx;
				left: 16rpx;
				/* #endif */
				/* #ifndef MP */
				top: -12rpx;
				right: -12rpx;
				/* #endif */
				border-radius: 50rpx;
				font-size: 20rpx;
				color: var(--color_price);
				text-align: center;
				vertical-align: middle;
				display: flex;
				justify-content: center;
				align-items: center;
			}
		}
	}

	/* app-start */
	.smegma {
		position: fixed;
		top: 0;
		left: 0;
		height: 100vh;
		width: 100vw;
		opacity: .8;
	}

	.frame {
		position: fixed;
		top: 0;



		left: 0;
		padding: 20rpx 20rpx;
		z-index: 10000;
		width: 100vw;
		line-height: 50rpx;
		background-color: #fff;
		border-radius: 10rpx;
	}

	.fra_title {
		font-weight: 700;
		font-size: 35rpx;
		margin-bottom: 10rpx;
	}

	/* app-end */
</style>