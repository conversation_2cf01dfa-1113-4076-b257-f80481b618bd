<!-- 商品组件：横向展示，一行一个，商品列表页面
	点击进入商品详情页
	加入购物车事件
-->
<template name="goodsItemH">
	<view class="collection_wrap" :style="{left:left == true?'-160rpx':'0'}">
		<view class="goods_h_item flex_row_start_start" @click="goGoodsDetail(goods_info)">
			<view class="goods-img" :style="{backgroundImage: 'url('+goods_info.productImage+')'}"></view>
			<!-- <coverImage :src="goods_info.productImage" class="goods-img"></coverImage> -->
			<view class="right flex_column_between_start">
				<view class="top flex_column_start_start" style="width: 100%;">
					<text class="goods-name">{{goods_info.goodsName}}</text>
					<text class="goods-brief">{{goods_info.goodsBrief}}</text>
				</view>
				<view class="goods-price flex_row_between_center">
					<view class="left flex_row_start_end">
						<text class="unit">{{$L('￥')}}</text>
						<text class="price_int">{{this.$getPartNumber(goods_info.productPrice,'int')}}</text>
						<text class="price_decimal">{{this.$getPartNumber(goods_info.productPrice,'decimal')}}</text>
					</view>
				</view>
			</view>
		</view>
		<!-- #ifdef H5 -->
		<view class="operate_wrap" :style="{paddingTop:isWeiXinBrower?'20rpx':'74rpx'}">
			<view class="to_shop_btn operate_btn" @click.stop="toShopDetail(goods_info.storeId)">{{$L('去店铺')}}</view>
			<view class="share_btn operate_btn" @click.stop="goShare(goods_info)" v-if="isWeiXinBrower">{{$L('分享')}}</view>
			<view class="delete_btn operate_btn" @click.stop="delGoods(goods_info.productId)">{{$L('删除')}}</view>
		</view>
		<!-- #endif -->
		<!-- app-1-start -->
		<!-- #ifndef H5 -->
		<view class="operate_wrap">
			<view class="to_shop_btn operate_btn" @click.stop="toShopDetail(goods_info.storeId)">{{$L('去店铺')}}</view>
			<!-- #ifdef APP-PLUS||MP-WEIXIN -->
			<view class="share_btn operate_btn" @click.stop="goShare(goods_info)">{{$L('分享')}}</view>
			<!-- #endif -->
			<view class="delete_btn operate_btn" @click.stop="delGoods(goods_info.productId)">{{$L('删除')}}</view>
		</view>
		<!-- #endif -->
		<!-- app-1-end -->
	</view>
</template>

<script>
	export default {
		name: "goodsItemH",
		data() {
			return {
				is_show_btn:false, //是否展示
				followId:'',
				startX:'',
				startY:'',
			}
		},
		props: {
			goods_info: {
				type: Object,
				value: {}
			},
			isWeiXinBrower:{
				type:Boolean
			},
			left:{
				type:Boolean
			}
		},
		methods: {
			//分享商品
			goShare(goods_info){
				this.$emit("goShare",goods_info)
			},
			//进入商品详情页
			goGoodsDetail(goods_info) {
				this.$Router.push({path:'/standard/product/detail',query:{productId:goods_info.productId,goodsId:goods_info.goodsId}})
			},
			//删除收藏商品
			delGoods(id) {
				this.$emit("delGoods", id);
			},
			toShopDetail(storeId){
				this.$Router.push({path:'/standard/store/shopHomePage',query:{vid:storeId}})
			}
		}
	}
</script>

<style lang='scss'>
	.collection_wrap{
		position:relative;
		transition: all 0.3s;
	}
	.goods_h_item {
		width: 750rpx;
		background: #fff;
		padding: 0 20rpx 20rpx;
		overflow: hidden;
		background: #fff;
		/* position: absolute; */
		&:first-child {
			padding-top: 20rpx;
		}
		border-bottom: 2px solid #f5f5f5;
		.goods-img {
			background-position: center center;
			background-repeat: no-repeat;
			background-size: cover;
			width: 290rpx;
			height: 290rpx;
			overflow: hidden;
			border-radius: 10rpx;
			background-color: #F8F6F7;
			flex-shrink: 0;
		}

		.right {
			height: 290rpx;
			padding: 10rpx 0 30rpx;
			width: 420rpx;

			.top {
				padding-left: 20rpx;
			}
		}

		.goods-name {
			font-size: 30rpx;
			color: $com-main-font-color;
			line-height: 38rpx;
			height: 76rpx;
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
			word-break: break-word;
		}

		.goods-brief {
			color: $main-third-color;
			font-size: 24rpx;
			margin-top: 10rpx;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			width: 100%;
		}

		.goods-price {
			padding: 0 20rpx;
			width: 100%;
			margin-top: 15rpx;

			.left {
				color: var(--color_price);
				align-items: baseline;

				.unit,
				.price_decimal {
					font-size: 24rpx;
				}

				.price_int {
					font-size: 38rpx;
					line-height: 38rpx;
				}

			}

			.iconfont {
				font-size: 45rpx;
				color: $main-font-color;
			}

			image {
				width: 42rpx;
				height: 42rpx;
			}
		}
	}
	
	.operate_wrap{
		position:absolute;
		width:160rpx;
		height:330rpx;
		display: flex;
		flex-direction: column;
		right: -162rpx;
		top:0;
		padding-top:20rpx;
		background-color: #fff;
		box-sizing: border-box;
		.operate_btn{
			width:160rpx;
			height:96rpx;
			font-size:28rpx;
			color:#fff;
			display: flex;
			justify-content: center;
			align-items: center;
			background: var(--color_vice_bg);
			transition: all 0.3s;
		}
		.share_btn{
			background: var(--color_vice_bg);
		}
		.to_shop_btn{
			background: var(--color_main);
			border-top-right-radius: 10rpx;
		}
		.delete_btn{
			background: var(--color_vice);
			border-bottom-right-radius: 10rpx;
		}
	}
</style>
