<template>
	<uni-popup ref="popup" type="center">
		<view class="popup-box">
			<view :class="['recharge-popup', {'mp-recharge-popup': isMpWeixin}]">
				<view class="popup-content">
					<view class="title">充值金额</view>
					<view class="tip-text">您还需要充值<text class="amount">{{ input_amount }}</text>元可完成该商品购买</view>
					<view class="amount_view">
						<text :class="{num: true,flex_row_center_center: true,hasSelAmout: Number(amount) == item}"
							v-for="(item, index) in amountList" :key="index"
							@click="selAmount(item)">{{ item }}{{ $L('元') }}</text>
						<input
							:class="{num: true,flex_row_center_center: true,input_amount: true,hasSelAmout: !amount && input_amount}"
							:placeholder="$L('请输入金额')" v-model="input_amount" placeholder-class="input_placeholder"
							maxlength="5" @focus="selSetAmount" @blur="handleBlur" type="digit" />
					</view>
					<view class="pay_part">
						<text class="part-title">{{ $L('选择充值方式') }}</text>
						<view v-for="(item, index) in payMethod" :key="index" @click="selectPayMethod(item)"
							:class="{item: true,b_b: index < payMethod.length - 1,flex_row_between_center: true}">
							<view class="left flex_row_start_center">
								<image class="pay_icon" :src="payIcon[item.payMethod]" />
								<text class="tit">{{ item.payMethodName }}</text>
							</view>
							<text
								:class="{iconfont: true,iconziyuan33: selData.payMethod == item.payMethod,iconziyuan43: selData.payMethod != item.payMethod,has_sel: selData.payMethod == item.payMethod}"></text>
						</view>
					</view>
					<view class="btn_recharge" @click="reCharge">{{ $L('确认充值') }}</view>
				</view>
			</view>
		</view>
	</uni-popup>
</template>

<script>
// #ifdef H5 
import { getWxH5Appid } from '@/static/h5/wxH5Auth.js';
// #endif
export default {
	data() {
		return {
			amount: 0,
			amountList: [50, 100, 200, 500, 1000],
			input_amount: '',
			payMethod: [], //支付方式
			selData: {},
			client: 'wxbrowser',
			isClick: true,
			fromUrl: null,
			isMpWeixin: false,
			payIcon:{
				balance:process.env.VUE_APP_IMG_URL +`pay/balance_pay_icon.png`,
				wx:process.env.VUE_APP_IMG_URL +`pay/wx_pay_icon.png`,
				alipay:process.env.VUE_APP_IMG_URL +`pay/alipay_pay_icon.png`,
				paypal:process.env.VUE_APP_IMG_URL +`pay/paypal_pay_icon.png`,
			}
		}
	},
	created() {
		// #ifdef MP-WEIXIN
		this.client = 'wxxcx'
		this.isMpWeixin = true
		// #endif
		// #ifdef MP-ALIPAY
		this.client = 'alipay'
		// #endif
	},
	methods: {
		open(options) {
			this.input_amount = options.amount || ''
			this.fromUrl = options.fromUrl
			this.getPayMethod()
			this.$refs.popup.open()
		},
		close() {
			this.$refs.popup.close()
		},
		//获取支付方式
		getPayMethod() {
			this.$request({
				url: 'v3/system/common/payMethod',
				data: {
					source: this.client,
					type: 2 //支付发起类型 1==下单支付，2==余额充值/订单列表
				}
			}).then((res) => {
				if (res.state == 200) {
					this.payMethod = res.data
					if (this.payMethod.length) {
						this.selData = this.payMethod[0]
					}
				}
			})
		},
		//选择支付方式事件
		selectPayMethod(val) {
			this.selData = val
			this.isClick = true
		},
		//选择充值金额
		selAmount(amount) {
			this.input_amount = ''
			this.amount = amount
		},
		//设置金额聚焦事件
		selSetAmount() {
			this.amount = ''
			this.isClick = true
		},
		//输入金额
		handleBlur(e) {
			let val = parseFloat(e.detail.value.toString())
			if (val > 5000) {
				this.$api.msg(this.$L('一次最多充值5000元'))
				this.input_amount = 5000
			} else {
				if (val <= 0) {
					this.$api.msg(this.$L('充值金额不能为负数或者为零'))
					this.input_amount = ''
				} else if (Number.isNaN(val)) {
					this.input_amount = ''
				} else {
					//小数点后最多后两位
					this.input_amount =
						val.toString().indexOf('.') == -1 ?
						val :
						val.toString().substring(0, val.toString().indexOf('.') + 3) > 0 ?
						val.toString().substring(0, val.toString().indexOf('.') + 3) :
						''
					if (this.input_amount == '') {
						this.$api.msg(this.$L('充值金额不能为负数或者为零'))
					}
				}
			}
		},
		//确认充值事件
		reCharge() {
			const {
				selData,
				amount,
				input_amount,
				client
			} = this
			let _this = this
			if (!this.isClick) {
				return false
			}
			this.isClick = false
			uni.showLoading()
			let param = {}
			param.method = 'POST'
			param.data = {}
			param.url = 'v3/member/front/balanceRecharge/recharge'
			param.data.amount = amount || input_amount
			if (!param.data.amount) {
				this.$api.msg(_this.$L('请设置充值金额'))
				return false
			}
			param.data.payType = selData.payType
			param.data.payMethod = selData.payMethod

			if (client == 'wxxcx') {
				//微信小程序支付
				uni.login({
					success: (code) => {
						param.data.code = code.code
						param.data.codeSource = 1
						this.$request(param).then((res) => {
							if (res.state == 200) {
								uni.hideLoading()
								let tmp_data = res.data.payData
								if (res.data.actionType == null) {
									uni.requestPayment({
										timeStamp: tmp_data.timeStamp,
										nonceStr: tmp_data.nonceStr,
										package: tmp_data.packageValue?tmp_data.packageValue:tmp_data.package,
										signType: 'MD5',
										paySign: tmp_data.paySign,
										success: function(res) {
											_this.payTip('success')
										},
										fail: function(res) {
											_this.payTip('fail')
										}
									})
								}
							} else {
								this.isClick = true
								this.$api.msg(res.msg)
								uni.hideLoading()
							}
						})
					}
				})
			} else {
				// 其他支付逻辑...
				this.$request(param).then((res) => {
					if (res.state == 200) {
						uni.hideLoading()
						let tmp_data = res.data.payData
						if (res.data.actionType == 'redirect') {
							window.location.href = tmp_data
						} else if (res.data.actionType == null) {
							if (client == 'wxbrowser') {
								this.$weiXinBrowerPay({
									timestamp: tmp_data.timeStamp,
									nonceStr: tmp_data.nonceStr,
									package: tmp_data.packageValue?tmp_data.packageValue:tmp_data.package,
									signType: 'MD5',
									paySign: tmp_data.paySign,
									appId: tmp_data.appId,
									success: function(r) {
										if (r.errMsg == 'chooseWXPay:ok') {
											_this.payTip('success')
										} else {
											_this.payTip('fail')
										}
									},
									cancel: function(r) {
										_this.payTip('fail')
									}
								})
							}
						}
					} else {
						this.isClick = true
						this.$api.msg(res.msg)
						uni.hideLoading()
					}
				})
			}
		},
		//支付操作完成提示
		payTip(type) {
			if (type == 'success') {
				this.isClick = true
				this.$api.msg(this.$L('充值成功'))
				this.close()
				// 发送充值成功事件给父组件
				this.$emit('rechargeSuccess')
				if(this.fromUrl) {
					// 不直接跳转，而是通知父组件刷新
					this.$emit('updateBalance')
				}
			} else if (type == 'fail') {
				this.$api.msg(this.$L('充值失败,请重试～'))
				this.isClick = true
				this.getPayMethod()
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.popup-box {
	position: relative;
	display: flex;
	justify-content: center;
	align-items: center;
	max-height: 90vh;
}

.recharge-popup {
	position: relative;
	width: 650rpx;
	background: #fff;
	border-radius: 20rpx;
	display: flex;
	flex-direction: column;
	max-height: 80vh;
}

.popup-content {
	position: relative;
	padding: 30rpx;
	background: #fff;
	border-radius: 20rpx;
	display: flex;
	flex-direction: column;
	overflow-y: auto;
	-webkit-overflow-scrolling: touch;
}

.title {
	font-size: 32rpx;
	color: $main-font-color;
	margin-bottom: 20rpx;
}

.tip-text {
	font-size: 28rpx;
	color: $main-font-color;
	margin-bottom: 30rpx;
	
	.amount {
		color: var(--color_main);
		margin: 0 4rpx;
	}
}

.amount_view {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	margin-bottom: 20rpx;
	
	.num {
		width: 180rpx;
		height: 90rpx;
		background: #fff;
		box-shadow: 0px 0px 20rpx 0px rgba(153, 153, 153, 0.2);
		border-radius: 10rpx;
		color: $main-font-color;
		font-size: 28rpx;
		margin-bottom: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		
		&.hasSelAmout {
			border: 1rpx solid var(--color_main);
		}
	}
	
	.input_placeholder {
		color: $main-third-color;
		font-size: 24rpx;
		text-align: center;
	}
	
	.input_amount {
		text-align: center;
		width: 180rpx;
	}
}

.pay_part {
	margin-top: 30rpx;
	width: 100%;
	background: #fff;
	border-top: none !important;
	flex: 1;
	overflow-y: auto;
	
	.part-title {
		font-size: 32rpx;
		color: $main-font-color;
		margin-bottom: 20rpx;
		display: block;
	}
	
	.item {
		width: 100%;
		padding: 20rpx 0;
		display: flex;
		justify-content: space-between;
		align-items: center;
		
		.left {
			display: flex;
			align-items: center;
			
			.pay_icon {
				width: 60rpx;
				height: 60rpx;
			}
			
			.tit {
				color: $main-font-color;
				font-size: 28rpx;
				margin-left: 20rpx;
			}
		}
		
		.iconfont {
			color: $main-third-color;
			font-size: 32rpx;
			
			&.has_sel {
				color: var(--color_main);
			}
		}
		
		&.b_b {
			border-bottom: 1rpx solid #f5f5f5;
		}
	}
}

.btn_recharge {
	width: 100%;
	height: 88rpx;
	background: var(--color_main_bg);
	border-radius: 44rpx;
	color: #fff;
	font-size: 32rpx;
	margin-top: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	position: sticky;
	bottom: 0;
	z-index: 1;
}

/* #ifdef MP-WEIXIN */
.mp-recharge-popup {
	width: 580rpx !important;
	max-height: 75vh !important;
	
	.popup-content {
		max-height: calc(75vh - 60rpx);
	}
	
	.num {
		width: 160rpx !important;
	}
	
	.input_amount {
		width: 160rpx !important;
	}
}
/* #endif */
</style> 