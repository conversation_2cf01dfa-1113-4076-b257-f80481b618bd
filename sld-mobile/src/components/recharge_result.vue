<template name="rechargeResult">
	<view class="container flex_column_between_center">
		<view class="top_bg" :style="{backgroundImage: 'url('+imgUrl +'recharge_result_bg.png)'}">
		</view>
		<view class="top_content flex_column_start_center">
			<!-- wx-1-start -->
			<!-- #ifdef MP -->
			<view class="nav flex_row_center_center" :style="{marginTop:menuButtonTop,padding:0,height:menuButtonHeight}">
				<text class="iconfont iconziyuan2" style="top:20rpx"></text>
				<text class="title">{{$L('充值结果')}}</text>
			</view>
			<!-- #endif -->
			<!-- wx-1-end -->
			<!-- #ifdef H5 -->
			<view class="navs flex_row_center_center"></view>
			<!-- #endif -->
			<!-- app-1-start -->






			<!-- app-1-end -->
			<view class="detail flex_column_start_center">
				<image class="result_icon" :src='rechargeData.icon' />
				<text class="result">{{rechargeData.title}}</text>
				<view v-for="(item,index) in rechargeData.detail"  :key='index' class="item flex_row_between_center">
					<text class="left">{{item.name}}</text>
					<text class="right">{{item.value}}</text>
				</view>
			</view>
		</view>
		<view v-if="rechargeData.resule=='success'" class="operate flex_row_center_center">
			{{$L('确认')}}
		</view>
		<view v-if="rechargeData.resule=='fail'" class="flex_column_end_center">
			<view class="operate flex_row_center_center">
				{{$L('再次充值')}}
			</view>
			<view class="go_back flex_row_center_center">
				{{$L('返回')}}
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "rechargeResult",
		data() {
			return {
				imgUrl: process.env.VUE_APP_IMG_URL,
				//wx-2-start
				// #ifdef MP
				menuButtonHeight:uni.getMenuButtonBoundingClientRect().height+'px',
				menuButtonTop:uni.getMenuButtonBoundingClientRect().top+'px',
				// #endif
				//wx-2-end
				stateBarHeight:0,
			}
		},
		props: {
			rechargeData: {
				type: Object,
				value: {}
			}
		},
		onLoad(){},
		mounted() {
		  let systemInfo = uni.getSystemInfoSync()
		  this.stateBarHeight = systemInfo.statusBarHeight
		},
		methods: {}
	}
</script>

<style lang="scss">
	page {
		background: $bg-color-split;

		.container {
			width: 750rpx;
			height: 100vh;

			.top_bg {
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				width: 750rpx;
				height: 533rpx;
				background-size: contain;
				z-index: 0;
			}

			.top_content {
				width: 100%;

				.nav {
					width: 100%;
					padding: 20rpx;
					height: 90rpx;
					position: relative;
					margin-bottom: 40rpx;

					.iconfont {
						position: absolute;
						font-size: 26rpx;
						left: 20rpx;
						top: 37rpx;
						color: #fff;
					}

					.title {
						color: #fff;
						font-size: 36rpx;
					}
				}
        .navs{
          width: 100%;
          height: 1rpx;
          position: relative;
          margin-bottom: 40rpx;
        }

				.detail {
					width: 670rpx;
					background: #fff;
					box-shadow: 0px 0px 20px 0px rgba(86, 86, 86, 0.15);
					border-radius: 10rpx;
					z-index: 1;
					padding: 30rpx 30rpx 50rpx;

					.result_icon {
						width: 146rpx;
						height: 146rpx;
						margin-top: 60rpx;
					}

					.result {
						color: $main-third-color;
						font-size: 28rpx;
						width: 100%;
						text-align: center;
						border-bottom: 1rpx solid rgba(0,0,0,.1);
						padding-bottom: 50rpx;
					}

					.item {
						width: 100%;
						color: $main-third-color;
						font-size: 26rpx;
						margin-top: 30rpx;
					}
				}
			}

			.operate {
				width: 670rpx;
				height: 88rpx;
				margin-bottom: 40rpx;
				background: var(--color_main_bg);
				border-radius: 44rpx;
				color: #fff;
				font-size: 36rpx;
			}
			.go_back{
				width:670rpx;
				height:88rpx;
				background:#fff;
				border:1rpx solid var(--color_main);
				border-radius:44rpx;
				color: var(--color_main);
				font-size: 36rpx;
				margin-bottom: 40rpx;
			}
		}
	}
</style>
