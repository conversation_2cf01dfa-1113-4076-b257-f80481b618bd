<template>
  <view>
    <view class="top_bar">
      <view class="top_header">
        <view class="top_header_left" @click="goBack('open')">
          <image :src="imgUrl + 'index/back.png'" mode=""></image>
        </view>
        <view class="top_header_cen">{{ title }}</view>
        <view class="top_white_space">
          <slot></slot>
        </view>
      </view>
    </view>

    <view class="top_bar_fixed" v-if="topFixed">
      <view class="top_header">
        <view class="top_header_left" @click="goBack('open')">
          <image :src="imgUrl + 'index/back.png'" mode=""></image>
        </view>
        <view class="top_header_cen">{{ title }}</view>
        <view class="top_white_space">
          <slot></slot>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
// import uniPopup from '@/components/uni-popup/uni-popup.vue'
// import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue'
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    topFixed: {
      type: Boolean,
      default: false
    }
  },
  components: {
    // uniPopup,
    // uniPopupDialog
  },
  data() {
    return {
      imgUrl: process.env.VUE_APP_IMG_URL
    }
  },
  methods: {
    goBack(type) {
      this.$emit('pop')
      // switch(type){
      // 	case 'open':{
      // 		if()
      // 		this.$refs.popBack.open()
      // 		break
      // 	}
      // 	case 'confirm':{
      // 		this.$Router.back(1)
      // 		break
      // 	}
      // }
    }
  }
}
</script>

<style lang="scss">
.top_bar_fixed {
  position: fixed;
  z-index: 600;
  padding-top: calc(var(--status-bar-height) + 44rpx);
  top: 0;
  background-color: #3a72b2;
}

@keyframes pageLoading {
  0% {
    opacity: 0;
  }

  20% {
    opacity: 0.2;
  }

  40% {
    opacity: 0.4;
  }

  60% {
    opacity: 0.6;
  }

  80% {
    opacity: 0.9;
  }

  100% {
    opacity: 1;
  }
}

.top_bar,
.top_bar_fixed {
  width: 750rpx;
  width: 750rpx;

  .top_header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10rpx 0;

    .top_header_left {
      padding-left: 20rpx;

      image {
        width: 17rpx;
        height: 29rpx;
      }
    }

    .top_header_cen {
      margin: 0 50rpx;
      font-size: 36rpx;
      font-family: PingFang SC;
      color: #ffffff;
    }

    .top_white_space {
      width: 40rpx;
      height: 49rpx;
      padding-right: 20rpx;

      image {
        width: 40rpx;
        height: 40rpx;
      }
    }
  }
}
</style>
