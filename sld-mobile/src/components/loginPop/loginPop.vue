<template>
	<uni-popup ref="loginModal" type="bottom" :maskClick="false" safeArea>
		<!-- #ifdef MP -->
		<wxBindPhone ref="wxBindPhone"></wxBindPhone>
		<!-- #endif -->

		<!-- #ifdef MP -->
		<privacyPop ref="priPop"></privacyPop>
		<!-- #endif -->

		<view class="login-popup">
			<!-- Gradient header -->
			<view class="popup-header"></view>

			<!-- Close button -->
			<view class="close-btn" @click="handleClose">
				<text class="iconfont icon-close"></text>
			</view>

			<!-- Content -->
			<view class="content">
				<view class="title">欢迎来到莘古电商平台</view>
				<view class="subtitle">登录后使用全平台功能</view>

				<!-- Login button -->
				<!-- #ifndef MP -->
				<button class="login-btn" @click="handleH5Login">公众号一键登录</button>
				<!-- #endif -->

				<!-- #ifdef MP -->
				<button class="login-btn" v-if="canIUseGetUserProfile" @tap="handleGetUserProfile">
					一键登录
				</button>
				<button class="login-btn" v-else open-type="getUserInfo" @getuserinfo="handleGetUser">
					点击一键登录
				</button>
				<!-- #endif -->

				<!-- Skip login button -->
				<view class="skip-login">
					<text @click="handleClose">暂不登录</text><text @click="handleMobileLogin">｜手机号登录</text>
				</view>
			</view>
		</view>
	</uni-popup>
</template>

<script>
import wxBindPhone from '@/components/wxBindPhone.vue';
import { mapMutations } from 'vuex';

// #ifdef H5 
import { getWxH5Appid } from '@/static/h5/wxH5Auth.js';
// #endif

// #ifdef MP
import privacyPop from '@/components/privacy-pop.vue';
// #endif

import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue';
import uniPopup from '@/components/uni-popup/uni-popup.vue';

export default {
	components: {
		wxBindPhone,
		uniPopup,
		uniPopupDialog,
		// #ifdef MP
		privacyPop,
		// #endif
	},

	data() {
		return {
			type: '', //一般为 no_replace 不用 replace跳转
			canIUseGetUserProfile: false,
			isClick: true,
			imgUrl: process.env.VUE_APP_IMG_URL,
			client: 1, //终端类型， 1、H5(微信内部浏览器) 2、H5(微信小程序)；3、app
			wxClient: 1,
			mobile: '',
		}
	},

	mounted() {
		this.client = this.$getLoginClient()
		this.wxClient = this.$wxLoginClient()
		//#ifdef MP-WEIXIN
		if (uni.getUserProfile) {
			this.canIUseGetUserProfile = true
		}
		//#endif	
	},

	methods: {
		...mapMutations(['login', 'setUserCenterData', 'loginRole', 'logout']),

		handleClose() {
			this.$emit('closeLogin')
			this.$refs.loginModal.close()
		},
		handleMobileLogin() {
			this.$emit('closeLogin')
			this.$refs.loginModal.close()
			if (!this.type) {
				this.$setFromUrl()
			}
			this.$Router.push('/pages/public/loginMobile')
		},

		handleH5Login() {
			this.$refs.loginModal.close()
			this.$emit('confirmLogin')

			if (!this.type) {
				this.$setFromUrl()
			}
			const login_url = window.location.origin + '/pages/public/loginBlank';
			getWxH5Appid(login_url)
		},

		handleGetUserProfile() {
			this.$emit('confirmLogin')
			console.log('小程序登录:handleGetUserProfile')

			if (!this.type) {
				this.$setFromUrl()
			}

			this.$refs.loginModal.close()

			if (!process.env.VUE_APP_ALLOW_PRIVACY) {
				this.$refs.priPop.open();
				return;
			}

			if (!this.isClick) return;

			this.isClick = false
			let that = this

			uni.getUserProfile({
				desc: that.$L('用于完善个人信息'),
				success: (res) => {
					if (res.errMsg == 'getUserProfile:ok') {
						let userinfo = res.userInfo
						this.getWxXcxCoce(userinfo)
					}
				},
				complete() {
					that.isClick = true
				}
			})
		},

		handleGetUser(e) {
			this.$refs.loginModal.close()

			if (!process.env.VUE_APP_ALLOW_PRIVACY) {
				this.$refs.priPop.open();
				return;
			}

			if (e.detail.errMsg == 'getUserInfo:ok') {
				let userinfo = e.detail.userInfo
				this.getWxXcxCoce(userinfo)
			}
		},

		getWxXcxCoce(userinfo) {
			let _this = this

			uni.showLoading({
				title: _this.$L('正在请求...'),
				mask: true
			})

			uni.login({
				success: (code) => {
					console.log('小程序登录code', code)
					let tar_params = {}
					tar_params.source = _this.wxClient
					tar_params.code = code.code
					tar_params.userInfo = JSON.stringify(userinfo)
					_this.goLogin(tar_params)
					this.wtCode = code.code
				}
			})
		},

		goLogin(data) {
			let _this = this
			let param = {
				url: 'v3/member/front/login/wechat/login',
				method: 'POST',
				data: {
					...data
				}
			}

			if (this.$getUnLoginCartParam()) {
				param.data.cartInfo = this.$getUnLoginCartParam()
			}

			this.$request(param).then((res) => {
				uni.hideLoading()
				if (res.state == 200) {
					uni.setStorage({
						key: 'sld_login_time',
						data: new Date().getTime()
					})

					console.log('登录重定向', res.data.redirect)
					if (res.data.redirect == undefined) {
						uni.removeStorage({
							key: 'cart_list'
						})
						res.data.loginTime = Date.parse(new Date())
						_this.login(res.data)

						_this.$request({
							url: 'v3/member/front/member/memberInfo'
						}).then((result) => {
							_this.setUserCenterData(result.data)

							//#ifdef H5
							let isWeiXinH5 = this.$isWeiXinBrower()
							if (isWeiXinH5) {
								let fromurl = uni.getStorageSync('fromurl')
								if (fromurl.url) {
									setTimeout(() => {
										_this.$Router.replace({
											path: fromurl.url,
											query: fromurl.query
										})
									}, 1000)
								} else {
									_this.$loginGoPage()
								}
							} else {
								_this.$loginGoPage()
							}
							//#endif

							//#ifndef H5
							_this.$loginGoPage()
							//#endif
						})
					} else if (res.data.redirect != undefined) {
						//#ifdef H5
						let isWeiXinH5 = this.$isWeiXinBrower()
						if (isWeiXinH5 && this.$getQueryVariable('code')) {
							history.replaceState({}, '', location.href.split('?')[0])
						}
						//#endif

						// #ifndef MP-WEIXIN
						let query = {
							code: res.data.bindKey
						}
						if (uni.getStorageSync('u')) {
							query.spreaderKey = uni.getStorageSync('u')
						}
						this.$Router.push({
							path: '/pages/public/bindMobile',
							query
						})
						// #endif

						// #ifdef MP-WEIXIN
						this.$refs.wxBindPhone.openKey(res.data.bindKey)
						// #endif
					}
				} else if (res.state == 267) {
					// #ifndef MP-WEIXIN
					_this.$api.msg(_this.$L('请绑定手机号'))
					// #endif

					// #ifdef MP-WEIXIN
					uni.showToast({
						title: _this.$L('请绑定手机号'),
						icon: 'none',
						duration: 2000
					})
					// #endif

					setTimeout(() => {
						_this.$Router.push({
							path: '/pages/public/bindMobile',
							query: {
								u: res.data.u,
								type: 'u'
							}
						})
					}, 2000)
				} else {
					// #ifndef MP-WEIXIN
					_this.$api.msg(res.msg)
					// #endif

					// #ifdef MP-WEIXIN
					uni.showToast({
						title: res.msg,
						icon: 'none',
						duration: 2000
					})
					// #endif

					// #ifdef H5
					if (this.$isWeiXinBrower()) {
						history.replaceState({}, '', window.origin + this.$Route.path)
					}
					// #endif
				}
			})
		},

		openLogin(type) {
			this.type = type
			this.$refs.loginModal.open()
		},
		close(fn) {
			fn && fn()
			this.$emit('closeLogin')
			this.$refs.loginModal.close()
		},
		handleAcc() {
			let regEn = /[`~!@#$%^&*()+<>?:"{},.\/;'[\]]/gi,
				regCn = /[·！#￥（——）：；""'，|《。》？【】[\]]/gi
			if (regEn.test(this.mobile) || regCn.test(this.mobile)) {
				this.mobile = ''
				this.$api.msg(this.$L('名称不能包含特殊字符.'))
			}
		}
	}
}
</script>

<style lang="scss">
.login-popup {
	background-color: #fff;
	border-radius: 24rpx 24rpx 0 0;
	position: relative;
	padding: 60rpx 40rpx 120rpx 40rpx;
	overflow: hidden;

	.popup-header {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 160rpx;
		background: linear-gradient(180deg, rgba(137, 186, 127, 0.2), #fff);
		z-index: 0;
	}

	.close-btn {
		position: absolute;
		right: 30rpx;
		top: 30rpx;
		padding: 20rpx;
		z-index: 1;

		.iconfont {
			font-size: 40rpx;
			color: #999;
		}
	}

	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;
		z-index: 999;

		.title {
			font-size: 40rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 12rpx;
		}

		.subtitle {
			font-size: 28rpx;
			color: #666;
			margin-bottom: 50rpx;
		}

		.login-btn {
			width: 600rpx;
			height: 88rpx;
			background: linear-gradient(to right, #89BA7F, #89BA7F);
			border-radius: 44rpx;
			color: #fff;
			font-size: 32rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-bottom: 14rpx;
			border: none;

			&:active {
				opacity: 0.8;
			}
		}

		.skip-login {
			padding: 12rpx 20rpx 32rpx 20rpx;
			font-size: 28rpx;
			color: #999;

			&:active {
				opacity: 0.8;
			}
		}
	}
}

/* 添加动画 */
.uni-popup {
	.uni-popup__wrapper {
		transition: transform 0.3s ease-out;
	}
}
</style>
