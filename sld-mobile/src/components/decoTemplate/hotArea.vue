<template>
	<view class="hot_area_display" :style="outSideStyle"> 
		<view class="inner_con" :style="innerStyle">
			<block v-if="decoItem.area_list.length">
				<view v-for="(item, index) in getAreaList" :key="index" class="area_item" :style="item.style"
					@click="handleClick(item)" />
			</block>
			<view :style="{width:realWidth+'px',height:realHeight+'px'}" v-if="decoItem.img_info.imgUrl">
				<image :src="decoItem.img_info.imgUrl" alt="" 
					class="main_image" mode="aspectFill"></image>
			</view>
			<block v-else>
				<view class="empty flex_row_center_center">
				</view>
			</block>
		</view>
		<loginPop ref="loginPop"></loginPop>
	</view>
</template>

<script setup>
	import {
		mapState
	} from 'vuex'
	export default {
		props: {
			decoItem: {
				type: Object,
				default: () => ({
					area_list: [],
				}),
			},
			mode:{
				type:String,
				defautl:'default'
			}
		},


		data() {
			return {
				windowWidth: uni.getSystemInfoSync().windowWidth,
				decoWidth: 375,
				height: 'auto'
			}
		},

		mounted() {},

		computed: {
			...mapState(['hasLogin']),
			transStylePadding() {
				let {
					padding
				} = this.decoItem.style;
				let {
					windowWidth,
					decoWidth
				} = this
				let ratio = windowWidth / decoWidth
				let transValues = {}
				for (let k in padding) {
					transValues[k] = padding[k] * ratio
				}
				return transValues
			},


			transStyleBorderRadius() {
				let {
					borderRadius
				} = this.decoItem.style;
				let {
					windowWidth,
					decoWidth
				} = this
				let ratio = windowWidth / decoWidth
				let transValues = {}
				for (let k in borderRadius) {
					transValues[k] = borderRadius[k] * ratio
				}
				return transValues
			},


			realWidth() {
				let {
					windowWidth,
					transStylePadding
				} = this
				return windowWidth - (transStylePadding.left + transStylePadding.right);
			},
			
			realHeight(){
				let {
					windowWidth,
					transStylePadding,
					decoItem:{img_info}
				} = this
				
				let realWidth = windowWidth - (transStylePadding.left + transStylePadding.right)
				let realHeight = realWidth/(img_info.width/img_info.height)
				return realHeight;
			},
			
			outSideStyle() {
				let {
					bgColor
				} = this.decoItem.style;
				let {
					transStylePadding,
					height
				} = this
				// #ifdef MP-WEIXIN
				return `padding:${transStylePadding.top}px ${transStylePadding.right}px ${transStylePadding.bottom}px ${transStylePadding.left}px;background-color:${bgColor};height:${height}`
				// #endif
				// #ifndef MP-WEIXIN
				return {
					padding: `${transStylePadding.top}px ${transStylePadding.right}px ${transStylePadding.bottom}px ${transStylePadding.left}px`,
					backgroundColor: bgColor,
					height: height,
				};
				// #endif

			},

			innerStyle() {
				let {
					transStyleBorderRadius
				} = this
				// #ifdef MP-WEIXIN
				return `borderRadius: ${transStyleBorderRadius.top}px ${transStyleBorderRadius.top}px ${transStyleBorderRadius.bottom}px  ${transStyleBorderRadius.bottom}px`
				// #endif
				// #ifndef MP-WEIXIN
				return {
					borderRadius: `${transStyleBorderRadius.top}px ${transStyleBorderRadius.top}px ${transStyleBorderRadius.bottom}px  ${transStyleBorderRadius.bottom}px`,
				};
				// #endif

			},

			getAreaList() {
				let list = this.decoItem.area_list.map(val => {
					let style = this.getStyle(val)
					return {
						...val,
						style,
					}
				})
				return list
			},

		},

		methods: {
			//ratio = W/H
			getStyle(item) {
				let {
					areaWidth_ratio,
					areaHeight_ratio,
					startX_ratio,
					startY_ratio
				} = item;
				let {realWidth,realHeight} = this
				let {width:imgWidth,height:imgHeight} = this.decoItem.img_info
				let width = areaWidth_ratio * realWidth + 'px';
				let height = areaHeight_ratio * realHeight + 'px';
				let top = startY_ratio * realHeight + 'px';
				let left = startX_ratio * realWidth + 'px';
				// #ifdef MP-WEIXIN
				return `width:${width};height:${height};top:${top};left:${left};`
				// #endif
				// #ifndef MP-WEIXIN
				return {
					width,
					height,
					top,
					left,
				};
				// #endif

			},

			handleClick(item) {
			
				
				let {
					link_value,
					link_type,
					info
				} = item
			
			
				if (link_type == 'url') { //跳转链接地址
					if (!link_value) {
						return;
					}
					// #ifdef H5
					window.open(link_value)
					// #endif
					//app-3-start



					//app-3-end
					//wx-14-start
					// #ifdef MP
					this.$Router.push({
						path: '/pages/index/skip_to',
						query: {
							url: link_value
						}
					})
					// #endif
					//wx-14-end
				} else if (link_type == 'goods') { //跳转商品详情页
			
					if (this.mode == 'spreader') {
						this.$Router.push({
							path: '/extra/tshou/goods/detail',
							query: {
								productId: info.productId,
								goodsId: info.goodsId
							}
						})
					} else if (this.mode == 'point') {
						//跳转商品详情页
						this.$Router.push({
							path: '/standard/point/product/detail',
							query: {
								productId: info.productId,
								goodsId: info.goodsId
							}
						})
					} else {
						this.$Router.push({
							path: '/standard/product/detail',
							query: {
								productId: info.productId,
								goodsId: info.goodsId
							}
						})
					}
				} else if (link_type == 'category') { // 分类列表
					if (this.mode == 'spreader') {
						this.$emit('skipTo', link_type,info.labelId)
						return
					}
			
					if (this.mode == 'point') {
						this.$Router.push({
							path: '/standard/point/search/good_list',
							query: {
								labelId: info.labelId,
								type: info.labelName
							}
						})
						return
					}
			
					this.$Router.push({
						path: '/standard/product/list',
						query: {
							categoryId: info.categoryId
						}
					})
				} else if (link_type == 'keyword') { // 关键词
					if (this.mode == 'spreader') {
						// 关键词
						this.$Router.push({
							path: '/extra/tshou/goods/list',
							query: {
								name: link_value
							}
						})
					}else if (this.mode == 'point') {
						// 关键词
						this.$Router.push({
							path: '/standard/point/search/search',
							query: {
								keyword: link_value
							}
						})
					} else {
						this.$Router.push({
							path: '/standard/product/list',
							query: {
								keyword: link_value,
								source: 'search'
							}
						})
					}
				} else if (link_type == 'topic') { //跳转专题页
					this.$Router.push({
						path: '/pages/index/topic',
						query: {
							id: (info.decoId ? info.decoId : info.id)
						}
					})
				} else if (link_type == 'o2o_topic') { //跳转专题页
					this.$Router.push({
						path: '/pages/index/topic',
						query: {
							id: (info.decoId ? info.decoId : info.id),
							type: 'o2o_topic'
						}
					})
				} else if (link_type == 'brand_home') { //品牌列表
					this.$Router.push('/pages/public/brand')
				} else if (link_type == 'seckill') { //秒杀
					this.$Router.push({
						path: '/standard/seckill/seckill',
						query: {
							seckillId: info.seckillId
						}
					})
				} else if (link_type == 'ladder_group') { //阶梯团
					this.$Router.push('/standard/ladder/index/index')
				} else if (link_type == 'presale') { //预售入口页
					this.$Router.push('/standard/presale/index/list')
				} else if (link_type == 'voucher_center') { //优惠券领券中心
					this.$Router.push('/standard/coupon/couponCenter')
				} else if (link_type == 'point' || link_type == 'point_center') { //积分商城首页
					this.$Router.push('/standard/point/index/index')
				} else if (link_type == 'svideo_center') { //短视频列表
					// this.$Router.pushTab('/pages/index/information')
					this.$Router.push('/extra/svideo/svideoList')
				} else if (link_type == 'live_center') { //直播列表
					this.$Router.push('/extra/live/liveList')
				} else if (link_type == 'spreader_center') { //推手中心
					if (!this.hasLogin) {
			
						this.$refs.loginPop.openLogin()
					} else {
						this.$Router.push('/extra/tshou/index/index')
					}
				} else if (link_type == 'live') { //直播播放页面
					this.$livePlayNav({
						live_id: info.live_id,
						decoIndex: info.pid,
					})
				} else if (link_type == 'svideo') { //短视频播放页面
					this.$videoPlayNav({
						video_id: info.videoId,
						curLabelId: info.labelId,
						author_id: info.authorId
					})
				} else if (link_type == 'spell_group') {
					this.$Router.push('/standard/pinGroup/index/index')
				} else if (link_type == 'sign_center') {
					if (!this.hasLogin) {
						this.$refs.loginPop.openLogin()
					} else {
						this.$Router.push('/standard/signIn/signIn')
					}
				} else if (link_type == 'rank') {
					this.$Router.push('/standard/rank/aggr')
				} else if (link_type == 'draw') {
					if (!this.hasLogin) {
						this.$refs.loginPop.openLogin()
					} else {
						this.$Router.push({
							path: '/standard/lottery/detail',
							query: {
								drawId: info.drawId
							}
						})
					}
				} else if (link_type == 'store_list') { //店铺街
					this.$Router.push('/standard/store/list')
				} else if (link_type == 'store') {
					this.$Router.push(`/standard/store/shopHomePage?vid=${info.storeId}`)
				}
			}
			//
		}
	}
</script>

<style lang="scss" scoped>
	.hot_area_display {
		width: 750rpx;
	}
	
	page{
		padding: 0;
	}

	.area_item {
		position: absolute;
		z-index: 10;
	}

	.empty {
		width: 100%;
		height: 200px;
		background: #eee;
	}

	.main_image {
		user-select: none;
		width: 100%;
		height: 100%;
		
		div {
			display: none;
		}
	}

	.inner_con {
		position: relative;
		overflow: hidden;
		padding: 0;
	}
</style>