<template>
	<!-- 富文本 -->
	<!-- #ifdef H5 -->
	<view class="rich_text_wrap" :style="{
		paddingTop: ((decoItem.top_margin !== undefined && decoItem.top_margin !== null) ? decoItem.top_margin*2 : 0) + 'rpx',
		paddingBottom: ((decoItem.bottom_margin !== undefined && decoItem.bottom_margin !== null) ? decoItem.bottom_margin*2 : 0) + 'rpx',
		backgroundColor: decoItem.color || 'unset',
	}">
	<!-- #endif -->
	<!-- #ifndef H5 -->
	<view class="rich_text_wrap" :style="{
		paddingTop: ((decoItem.top_margin !== undefined && decoItem.top_margin !== null) ? decoItem.top_margin*2 : 30) + 'rpx',
		paddingBottom: ((decoItem.bottom_margin !== undefined && decoItem.bottom_margin !== null) ? decoItem.bottom_margin*2 : 30) + 'rpx',
		backgroundColor: decoItem.color || 'unset',
	}">
	<!-- #endif -->
		<view class="rich_text_wrap_main" :style="{backgroundColor: decoItem.color || '#fff'}">
			<jyfParser :isAll="true" :html="decoItem.text"></jyfParser>
		</view>
	</view>
</template>

<script>
	import jyfParser from '../jyf-parser/jyf-parser.vue'
	export default{
		components:{
			jyfParser
		},
		props:{
			decoItem:{
				type:Object,
				default:()=>{}
			}
		}
	}
</script>

<style lang="scss">
	.rich_text_wrap {
		.rich_text_wrap_main {
			color: #333;
			font-size: 28rpx;
			box-sizing: border-box;
			background: #fff;
			/* #ifdef H5 */
			padding: 0 8rpx;
			/* #endif */
			/* #ifndef H5 */
			padding: 30rpx;
			/* #endif */
		}
	}
	
</style>