<template>
	<view :style="outSideStyle" class="outer">
		<view class="cube_display">
			<view class="cube_item" v-for="(item, index) in getCubeImgList" :key="index" :style="item.style"
				@click="handleClick(item)">
				<image :src="item.img.imgUrl" v-if="item.img.imgUrl" mode="aspectFit" />
			</view>
			<loginPop ref="loginPop"></loginPop>
		</view>
	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex'
	export default {
		props: {
			decoItem: {
				type: Object,
				default: () => ({
					area_list: [],
				}),
			},
			mode: {
				type: String,
				defautl: 'default'
			}
		},
		computed: {
			...mapState(['hasLogin']),
			getCubeImgList() {
				let list = this.decoItem.data.map(val => {
					let style = this.dynaStyle(val)
					return {
						...val,
						style
					}
				})
				return list
			},
			outSideStyle() {

				let style = {
					background: this.decoItem.color ?? '#fff',
					paddingTop: (this.decoItem.top_margin ?? 0) + 'rpx',
					paddingBottom: (this.decoItem.bottom_margin ?? 0) + 'rpx',
				}

				let style_text = `
					background:${this.decoItem.color ??'#fff'};
					padding-top:${(this.decoItem.top_margin??0)}rpx;
					padding-bottom:${(this.decoItem.bottom_margin??0)}rpx
				`

				// #ifndef MP-WEIXIN
				return style
				// #endif

				// #ifdef MP-WEIXIN
				return style_text
				// #endif

			}
		},
		mounted() {},
		methods: {
			dynaStyle({
				areaInfo
			}) {
				let {
					start,
					width,
					height
				} = areaInfo;

				let unitLength = 750 / this.decoItem.size;
				// #ifdef MP-WEIXIN
				return `width: ${width * unitLength}rpx;height:${height * unitLength}rpx;left:${start.x * unitLength}rpx;top:${start.y * unitLength}rpx`
				// #endif
				// #ifndef MP-WEIXIN
				return {
					width: width * unitLength + 'rpx',
					height: height * unitLength + 'rpx',
					left: start.x * unitLength + 'rpx',
					top: start.y * unitLength + 'rpx',
				};
				// #endif
			},

			handleClick(item) {

				let {
					content
				} = item
				let {
					link_value,
					link_type,
					info
				} = content


				if (link_type == 'url') { //跳转链接地址
					if (!link_value) {
						return;
					}
					// #ifdef H5
					window.open(link_value)
					// #endif
					//app-3-start



					//app-3-end
					//wx-14-start
					// #ifdef MP
					this.$Router.push({
						path: '/pages/index/skip_to',
						query: {
							url: link_value
						}
					})
					// #endif
					//wx-14-end
				} else if (link_type == 'goods') { //跳转商品详情页

					if (this.mode == 'spreader') {
						this.$Router.push({
							path: '/extra/tshou/goods/detail',
							query: {
								productId: info.productId,
								goodsId: info.goodsId
							}
						})
					} else if (this.mode == 'point') {
						//跳转商品详情页
						this.$Router.push({
							path: '/standard/point/product/detail',
							query: {
								productId: info.productId,
								goodsId: info.goodsId
							}
						})
					} else {
						this.$Router.push({
							path: '/standard/product/detail',
							query: {
								productId: info.productId,
								goodsId: info.goodsId
							}
						})
					}
				} else if (link_type == 'category') { // 分类列表
					if (this.mode == 'spreader') {
						this.$emit('skipTo',link_type, info.labelId)
						return
					}

					if (this.mode == 'point') {
						this.$Router.push({
							path: '/standard/point/search/good_list',
							query: {
								labelId: info.labelId,
								type: info.labelName
							}
						})
						return
					}

					this.$Router.push({
						path: '/standard/product/list',
						query: {
							categoryId: info.categoryId
						}
					})
				} else if (link_type == 'keyword') { // 关键词
					if (this.mode == 'spreader') {
						// 关键词
						this.$Router.push({
							path: '/extra/tshou/goods/list',
							query: {
								name: link_value
							}
						})
					}else if (this.mode == 'point') {
						// 关键词
						this.$Router.push({
							path: '/standard/point/search/search',
							query: {
								keyword: link_value
							}
						})
					} else {
						this.$Router.push({
							path: '/standard/product/list',
							query: {
								keyword: link_value,
								source: 'search'
							}
						})
					}
				} else if (link_type == 'topic') { //跳转专题页
					this.$Router.push({
						path: '/pages/index/topic',
						query: {
							id: (info.decoId ? info.decoId : info.id)
						}
					})
				} else if (link_type == 'o2o_topic') { //跳转专题页
					this.$Router.push({
						path: '/pages/index/topic',
						query: {
							id: (info.decoId ? info.decoId : info.id),
							type: 'o2o_topic'
						}
					})
				} else if (link_type == 'brand_home') { //品牌列表
					this.$Router.push('/pages/public/brand')
				} else if (link_type == 'seckill') { //秒杀
					this.$Router.push({
						path: '/standard/seckill/seckill',
						query: {
							seckillId: info.seckillId
						}
					})
				} else if (link_type == 'ladder_group') { //阶梯团
					this.$Router.push('/standard/ladder/index/index')
				} else if (link_type == 'presale') { //预售入口页
					this.$Router.push('/standard/presale/index/list')
				} else if (link_type == 'voucher_center') { //优惠券领券中心
					this.$Router.push('/standard/coupon/couponCenter')
				} else if (link_type == 'point' || link_type == 'point_center') { //积分商城首页
					this.$Router.push('/standard/point/index/index')
				} else if (link_type == 'svideo_center') { //短视频列表
					// this.$Router.pushTab('/pages/index/information')
					this.$Router.push('/extra/svideo/svideoList')
				} else if (link_type == 'live_center') { //直播列表
					this.$Router.push('/extra/live/liveList')
				} else if (link_type == 'spreader_center') { //推手中心
					if (!this.hasLogin) {

						this.$refs.loginPop.openLogin()
					} else {
						this.$Router.push('/extra/tshou/index/index')
					}
				} else if (link_type == 'live') { //直播播放页面
					this.$livePlayNav({
						live_id: info.live_id,
						decoIndex: info.pid,
					})
				} else if (link_type == 'svideo') { //短视频播放页面
					this.$videoPlayNav({
						video_id: info.videoId,
						curLabelId: info.labelId,
						author_id: info.authorId
					})
				} else if (link_type == 'spell_group') {
					this.$Router.push('/standard/pinGroup/index/index')
				} else if (link_type == 'sign_center') {
					if (!this.hasLogin) {
						this.$refs.loginPop.openLogin()
					} else {
						this.$Router.push('/standard/signIn/signIn')
					}
				} else if (link_type == 'rank') {
					this.$Router.push('/standard/rank/aggr')
				} else if (link_type == 'draw') {
					if (!this.hasLogin) {
						this.$refs.loginPop.openLogin()
					} else {
						this.$Router.push({
							path: '/standard/lottery/detail',
							query: {
								drawId: info.drawId
							}
						})
					}
				} else if (link_type == 'store_list') { //店铺街
					this.$Router.push('/standard/store/list')
				} else if (link_type == 'store') {
					this.$Router.push(`/standard/store/shopHomePage?vid=${info.storeId}`)
				}
			}
			//
		}
	}
</script>

<style lang="scss">
	.outer{
		background: #eee;
	}
	.cube_display {
		position: relative;
		width: 750rpx;
		height: 750rpx;

		.cube_item {
			position: absolute;
			z-index: 10;

			image {
				width: 100%;
				height: 100%;
				object-fit: contain;
			}
		}
	}
</style>