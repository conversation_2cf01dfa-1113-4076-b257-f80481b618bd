<template>
	<!-- 搭配 -->
	<view class="match_wrap" :style="{
			paddingTop: ((decoItem.top_margin !== undefined && decoItem.top_margin !== null) ? decoItem.top_margin*2 : 20) + 'rpx',
			paddingBottom: ((decoItem.bottom_margin !== undefined && decoItem.bottom_margin !== null) ? decoItem.bottom_margin*2 : 0) + 'rpx',
			backgroundColor: decoItem.color || 'unset',
		}">
		<view class="match_wrap_main">
			<view class="match_top">
				<view class="match_top_title" v-if="decoItem.dapei_title">
					{{ decoItem.dapei_title }}
				</view>
				<view class="match_image_wrap  flex_row_center_center">
					<view mode="aspectFit" class="match_image" :style="{
						width: '750rpx',
						height: 750 * decoItem.height / decoItem.width + 'rpx',
						backgroundImage: 'url(' + decoItem.dapei_img + ')',
					}">
					</view>
				</view>
				<view class="match_top_text" v-if="decoItem.dapei_desc">
					{{ decoItem.dapei_desc }}
				</view>
			</view>
			<view class="match_main_wrap">
				<view class="match_main" v-if="decoItem && decoItem.data && decoItem.data.info.length">
					<view class="match_item" v-for="(item, index) in decoItem.data.info" :key="index"
						@click="toGoodsDetail(item.productId || item.defaultProductId, item.goodsId)">
						<view class="match_goods_img">
							<view class="image" :style="{ backgroundImage: 'url(' + item.mainImage + ')' }">
							</view>
						</view>
						<view class="match_goods_name">{{ item.goodsName }}</view>
						<view class="match_goods_price">
							<text class="small_price">￥</text>
							<text class="big_price">{{ $getPartNumber(item.goodsPrice, 'int') }}</text>
							<text class="small_price">{{ $getPartNumber(item.goodsPrice, 'decimal') }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	import {
		mapState
	} from 'vuex'
	export default {
		props:{
			decoItem:{
				type:Object,
				default:()=>({})
			},
			mode:String
		},
		components: {

		},
		data() {
			return {
				imgUrl: process.env.VUE_APP_IMG_URL
			}
		},
		onLoad() {

		},

		onShow() {

		},

		mounted() {

		},

		methods: {
			// 跳转商品详情页
			toGoodsDetail(productId, goodsId) {
				this.$Router.push({
					path: '/standard/product/detail',
					query: {
						productId,
						goodsId
					}
				})
			},
		},
	}
</script>
<style lang="scss">
	page {}
	.match_wrap {
		padding-top: 20rpx;
	
		.match_wrap_main {
			box-sizing: border-box;
			flex-direction: column;
	
			.match_top {
				display: flex;
				flex-direction: column;
	
				image {
					width: 100%;
				}
	
				.match_image_wrap {
					width: 100%;
					display: flex;
					justify-content: center;
	
					.match_image {
						margin: 0 auto;
						background-position: center center;
						background-repeat: no-repeat;
						background-size: contain;
					}
				}
	
				.match_top_title {
					text-align: center;
					padding-bottom: 20rpx;
					font-size: 32rpx;
					color: #333;
				}
	
				.match_top_text {
					color: #333;
					font-size: 28rpx;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
					padding: 20rpx;
				}
			}
	
			.match_main_wrap {
				margin-top: 20rpx;
			}
	
			.match_main {
				display: flex;
				justify-content: space-between;
				overflow-x: scroll;
				box-sizing: border-box;
				// padding: 0 20rpx;
				width: 710rpx;
				margin: 0 auto;
	
				.match_item {
					width: 222rpx;
					height: 370rpx;
					margin-right: 20rpx;
					background-color: #fff;
					border-radius: 15rpx;
					position: relative;
	
					.match_goods_img {
						width: 222rpx;
						height: 222rpx;
						background-color: #ccc;
						border-radius: 15rpx 15rpx 0 0;
	
						.image {
							background-position: center center;
							background-repeat: no-repeat;
							background-size: cover;
							width: 222rpx;
							height: 222rpx;
							border-radius: 10rpx 10rpx 0 0;
						}
					}
	
					.match_goods_name {
						color: #333;
						font-size: 28rpx;
						text-overflow: -o-ellipsis-lastline;
						overflow: hidden;
						text-overflow: ellipsis;
						margin: 0 9rpx;
						padding: 10rpx 0 0;
						display: -webkit-box;
						-webkit-line-clamp: 2;
						line-clamp: 2;
						-webkit-box-orient: vertical;
						box-sizing: border-box;
					}
	
					.match_goods_price {
						position: absolute;
						bottom: 12rpx;
						// left: 20rpx;
						color: var(--color_price);
						font-size: 28rpx;
						font-weight: 600;
						margin: 0 9rpx;
					}
				}
			}
		}
	}
	
</style>