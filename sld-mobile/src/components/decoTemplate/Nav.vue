<template>
	<view class="nav_wrap">
		<view class="nav_wrap_box" :style="{
			background: decoItem.color || 'unset',
			paddingTop: ((decoItem.top_margin !== undefined && decoItem.top_margin !== null) ? decoItem.top_margin*2 : 0) + 'rpx',
			paddingBottom: ((decoItem.bottom_margin !== undefined && decoItem.bottom_margin !== null) ? decoItem.bottom_margin*2 : 0) + 'rpx',
		}">
			<block v-if="decoItem.page_set == 'scroll'">
				<uni-swiper-dot v-if="decoItem.data.length > 0" mode="dot"
					:info="splitArrayNav" :current="navSwiperCurrent" :dotsStyles="{
					...navDotsStyles,
					bottom: (decoItem.style_set == 'nav' && decoItem.icon_set == 'up' && decoItem.page_num > 5) ? 5 : 5,
				}">
					<swiper @change="navSwiperChangeIndex" :style="{width:'710rpx',height: swiperHeight}">
						<swiper-item v-for="(nav_item, nav_index) in splitArrayNav" :key="nav_index">
							<!-- 导航样式一、二（图标在上/不显示图标） -->
							<view class="cate-section"
								v-if="decoItem.style_set == 'nav' && decoItem.icon_set == 'up' || decoItem.icon_set == 'no-icon'"
								style="flex-wrap: wrap;align-items: flex-start;justify-content: flex-start;">
								<view class="cate-item" v-for="(item, index) in nav_item" :key="index"
									@click="skipTo(item)"
									:style="{
									width: decoItem.data.length > 5 ? '20%' : 'unset',
									flex: decoItem.data.length > 5 ? 'unset' : 1,
									marginBottom: '20rpx',
								}">
									<image :src="item.img" v-if="decoItem.icon_set == 'up'" :style="'width:' + decoItem.slide * 2 + 'rpx;height:' + decoItem.slide * 2 + 'rpx'"></image>
									<text>{{ item.name.substring(0, 9) }}</text>
								</view>
							</view>

							<!-- 导航样式三 （图标文字左右显示）-->
							<view class="cate-section"
								v-if="decoItem.style_set == 'nav' && decoItem.icon_set == 'left'"
								style="flex-wrap: wrap;align-items: flex-start;justify-content: flex-start;">
								<view class="cate-item2" v-for="(item, index) in nav_item" :key="index"
									@click="skipTo(item)"
									:style="{
										width: decoItem.data.length > 5 ? '20%' : 'unset',
										flex: decoItem.data.length > 5 ? 'unset' : 1,
										marginBottom: '30rpx',
									}">
									<image :src="item.img" style="margin-right:10rpx;" mode="aspectFit"
										:style="'width:' + (decoItem.slide > 54 ? 54 : decoItem.slide) * 2 + 'rpx;height:' + (decoItem.slide > 54 ? 54 : decoItem.slide) * 2 + 'rpx'">
									</image>
									<view class="cate_name">{{ item.name.substring(0, 9) }}</view>
								</view>
							</view>

							<!-- 导航分组 -->
							<view class="nav_group" v-if="decoItem.style_set == 'tag-nav'">
								<view class="nav_group_item" v-for="(item, index) in nav_item" :key="index"
									@click="skipTo(item)">
									<image :src="item.img" mode="aspectFit"
										:style="'width:' + decoItem.slide * 2 + 'rpx;height:' + decoItem.slide * 2 + 'rpx'">
									</image>
									<view class="nav_group_name">{{ item.name }}</view>
								</view>
							</view>
						</swiper-item>
					</swiper>
				</uni-swiper-dot>
			</block>
			<block v-else>
				<!-- 导航样式一、二（图标在上/不显示图标） -->
				<view class="cate-section"
					v-if="decoItem.style_set == 'nav' && decoItem.icon_set == 'up' || decoItem.icon_set == 'no-icon'">
					<view class="cate-item" v-for="(item, index) in decoItem.data" :key="index"
						@click="skipTo(item)">
						<image :src="item.img" v-if="decoItem.icon_set == 'up'"
							:style="'width:' + decoItem.slide * 2 + 'rpx;height:' + decoItem.slide * 2 + 'rpx'">
						</image>
						<text>{{ item.name.substring(0, 9) }}</text>
					</view>
				</view>

				<!-- 导航样式三 （图标文字左右显示）-->
				<view class="cate-section"
					v-if="decoItem.style_set == 'nav' && decoItem.icon_set == 'left'"
					style="justify-content:space-around;padding:10rpx;">
					<view class="cate-item2" v-for="(item, index) in decoItem.data" :key="index"
						@click="skipTo(item)">
						<image :src="item.img" style="margin-right:10rpx;" mode="aspectFit"
							:style="'width:' + (decoItem.slide > 54 ? 54 : decoItem.slide) * 2 + 'rpx;height:' + (decoItem.slide > 54 ? 54 : decoItem.slide) * 2 + 'rpx'">
						</image>
						<view class="cate_name">{{ item.name.substring(0, 9) }}</view>
					</view>
				</view>

				<!-- 导航分组 -->
				<view class="nav_group" v-if="decoItem.style_set == 'tag-nav'">
					<view class="nav_group_item" v-for="(item, index) in decoItem.data" :key="index"
						@click="skipTo(item)">
						<image :src="item.img" mode="aspectFit"
							:style="'width:' + decoItem.slide * 2 + 'rpx;height:' + decoItem.slide * 2 + 'rpx'">
						</image>
						<view class="nav_group_name">{{ item.name }}</view>
					</view>
				</view>
			</block>
		</view>
	</view>
</template>

<script>
	export default{
		props:{
			decoItem:{
				type:Object,
				default:()=>({})
			},
			mode:String
		},
		data(){
			return{
				navSwiperCurrent: 0, //导航组件轮播下标
				navDotsStyles: {
					selectWidth: 14,
					selectedBackgroundColor: 'var(--color_main)',
					selectedBorder: 'none',
					selectedOpacity: 0.75,
					width: 4,
					height: 4,
					backgroundColor: 'var(--color_main)',
					opacity: 0.2,
					border: 'none',
					bottom: 5,
					margin: '0 6rpx'
				},
			}
		},
		computed:{
			//导航组件多屏滚动展示数据处理
			splitArrayNav() {
				let arr = this.decoItem.data, groupSize = this.decoItem.page_num
				var result = [];
				for (var i = 0; i < arr.length; i += groupSize) {
					result.push(arr.slice(i, i + groupSize));
				}
				return result;
			},
			swiperHeight(){
				let {decoItem,splitArrayNav} = this
				if(decoItem.style_set == 'nav' && decoItem.icon_set == 'up'){
					return (((decoItem.slide * 2) + 51) * Math.ceil(splitArrayNav[0].length/5)) + 80 + 'rpx'
				} else if(decoItem.style_set == 'nav' && decoItem.icon_set == 'no-icon'){
					return (Math.ceil(splitArrayNav[0].length/5)*56 + 60) + 'rpx'
				} else if(decoItem.style_set == 'nav' && decoItem.icon_set == 'left'){
					return (Math.ceil(splitArrayNav[0].length / 5) * 126 + 60) + 'rpx'
				} 
				
				return (Math.ceil(splitArrayNav[0].length / 2) * 180 + 60) + 'rpx'
			},
			
			
		},
		methods:{
			// 导航切换轮播下标
			navSwiperChangeIndex(e) {
				this.navSwiperCurrent = e.detail.current
			},
			
			skipTo(item){
				console.log('[skipTo]:',item)
				this.$emit('skipTo',item.url_type, item)
			}
		}
	}
</script>

<style lang="scss">
	.nav_wrap {
		width: 100%;
		// padding: 0 20rpx;
		box-sizing: border-box;
	}
	
	.nav_wrap_box {
		// border-radius: 20rpx;
	}
	
	.nav_wrap::-webkit-scrollbar {
		display: none;
	}
	
	/* 分类 */
	.cate-section {
		display: flex;
		justify-content: space-around;
		align-items: center;
		position: relative;
		z-index: 5;
		overflow-x: hidden;
		padding: 20rpx 0;
	
		.cate-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			font-size: 26upx;
			color: #303133;
			flex: 1;
	
			image {
				overflow: visible;
				margin-bottom: 10rpx;
			}
	
			text {
				white-space: nowrap;
				/* #ifdef MP-BAIDU */
				font-size: 24rpx;
				/* #endif */
			}
		}
	
		.cate-item2 {
			display: flex;
			align-items: center;
			font-size: 24upx;
			color: #303133;
		}
	
		.cate_name {
			// width: 78rpx;
		}
	
		/* 原图标颜色太深,不想改图了,所以加了透明度 */
		image {
			width: 88upx;
			height: 88upx;
			// margin-bottom: 14upx;
			border-radius: 50%;
		}
	}
	
	// 导航分组
	.nav_group {
		// padding: 0 86rpx;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
	
		.nav_group_item {
			width: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			padding-top: 40rpx;
			margin-bottom: 40rpx;
	
			image {
				width: 90rpx;
				height: 90rpx;
				border-radius: 50%;
				margin-right: 20rpx;
			}
	
			.nav_group_name {
				font-size: 26rpx;
				color: #333;
			}
		}
	
		.nav_group_item:nth-last-child(1) {
			margin-right: 0;
		}
	}
	
	.nav_group>view:nth-child(2n) {
		margin-right: 0;
	}
	
</style>