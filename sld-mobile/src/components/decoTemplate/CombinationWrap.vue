<template>
	<view class="combination_wrap" :style="{
			paddingTop: ((decoItem.top_margin !== undefined && decoItem.top_margin !== null) ? decoItem.top_margin*2 : 0) + 'rpx',
			paddingBottom: ((decoItem.bottom_margin !== undefined && decoItem.bottom_margin !== null) ? decoItem.bottom_margin*2 : 0) + 'rpx',
			background: decoItem.color || 'unset',
		}">
		<!-- 图片组合0123 -->
		<view v-if="decoItem.sele_style < 4">
			<view class="modules-slide">
				<view :class="'image-list style' + decoItem.sele_style" v-if="decoItem.sele_style < 3">
					<view
						:class="decoItem.sele_style == 2 ? 'combination_style no_margin_right flex_row_start_start tupianzuhe2' : 'space_between combination_style'"
						:style="{ 'display': decoItem.sele_style < 2 ? 'block' : 'flex' }">
						<view v-for="(childitem, childindex) in decoItem.data" :key="childindex"
							:class="decoItem.sele_style < 2 ? 'combine1' : 'combine2'"
							:style="{ 'marginTop': decoItem.sele_style == 1 ? '20rpx' : '0', 'marginRight': decoItem.sele_style < 3 ? '0' : '20rpx', 'marginBottom': decoItem.sele_style < 3 ? '0' : '20rpx' }">
							<view class="flex_column_start_center"
								v-if="decoItem.sele_style == 0 || decoItem.sele_style == 1">
								<image v-if="decoItem.sele_style == 0" @click="skipTo(childitem.url_type, childitem)"
									mode="aspectFit" :src="childitem.img"
									:style="{ 'display': 'block', 'width': '750rpx', 'height': (750 * childitem.height / childitem.width) + 'rpx' }">
								</image>
								<image v-if="decoItem.sele_style == 1" @click="skipTo(childitem.url_type, childitem)"
									mode="aspectFit" :src="childitem.img"
									:style="{ 'display': 'block', 'width': '710rpx', 'height': (710 * childitem.height / childitem.width) + 'rpx', 'margin-bottom': childindex == (decoItem.data.length - 1) ? '20rpx' : 0 }">
								</image>
							</view>

							<view class="flex_row_center_center combine3" v-if="decoItem.sele_style == 2"
								:style="{ 'height': childindex % 2 == 0 ? ((345 * childitem.height / childitem.width) + 'rpx') : ((345 * decoItem.data[childindex - 1].height / decoItem.data[childindex - 1].width) + 'rpx'), 'margin-left': '20rpx' }">
								<image @click="skipTo(childitem.url_type, childitem)" mode="aspectFit"
									:src="childitem.img" style="width: 100%;height: 100%;">
								</image>
							</view>
						</view>
					</view>
				</view>

				<view :class="'image-list style' + decoItem.sele_style" v-if="decoItem.sele_style == 3">
					<view class="combination_style no_margin_right2"
						style="display: flex;margin-bottom: 20rpx;flex-wrap:wrap">
						<view v-for="(childitem, childindex) in decoItem.data" :key="childindex" class="combine2">
							<view class="combine4"
								:style="{ 'height': childindex % 3 == 0 ? ((690 / 3 * childitem.height / childitem.width) + 'rpx') : (childindex % 3 == 1 ? ((690 / 3 * decoItem.data[childindex - 1].height / decoItem.data[childindex - 1].width) + 'rpx') : ((690 / 3 * decoItem.data[childindex - 2].height / decoItem.data[childindex - 2].width) + 'rpx')), }">
								<image @click="skipTo(childitem.url_type, childitem)" mode="aspectFit"
									:src="childitem.img"></image>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!--图片组合4  -->
		<view v-if="decoItem.sele_style == 4">
			<view class="modules-slide">
				<view class="image-ad clearfix images-tpl" style="padding: 0;">
					<view style="display: flex;">
						<view class="tupianzuhe04_left flex_row_center_center"
							@click="skipTo(decoItem.data[0].url_type, decoItem.data[0])">
							<image mode="aspectFit" :src="decoItem.data[0].img"></image>
						</view>
						<view style="display: flex;flex-direction: column;justify-content: space-between;">
							<block v-for="(item,index) in decoItem.data" :key="index">
								<view class="tupianzuhe04_right_item flex_row_center_center"
									@click="skipTo(item.url_type, item)" v-if="index>0">
									<image mode="aspectFit" :src="item.img"></image>
								</view>
							</block>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 图片组合5 6 -->
		<view v-if="decoItem.sele_style == 5 || decoItem.sele_style == 6">
			<view class="modules-slide">
				<view class="image-ad clearfix images-tpl" style="padding-top: 0;">
					<view class="combine5_wrap" style="display:flex;flex-wrap:wrap;width:100%;"
						v-if="decoItem.sele_style == 5">
						<view v-for="(childitem, childindex) in decoItem.data" :key="childindex"
							class="combine5 flex_row_center_center" @click="skipTo(childitem.url_type, childitem)"
							:style="{
									width: childindex == 0 || childindex == 3 ? 230 + 'rpx' : 460 + 'rpx',
									height: '230rpx', marginTop: '20rpx', marginLeft: '20rpx', backGround: 'red'
								}">
							<image mode="aspectFit" :src="childitem.img" style="width: 100%;height: 100%;">
							</image>
						</view>
					</view>

					<view class="" v-if="decoItem.sele_style == 6" style="display:flex;">
						<view class="combine6" style="margin-left: 20rpx;">
							<view class="flex_row_center_center"
								:style="{ width: '345rpx', height: 345 / 2 + 'rpx', 'flex-shrink': 0, 'margin-bottom': '20rpx' }"
								@click="skipTo(decoItem.data[0].url_type, decoItem.data[0])">
								<image :src="decoItem.data[0].img" mode="aspectFit" style="width: 100%;height: 100%;">
								</image>
							</view>
							<view class="flex_row_center_center"
								:style="{ width: '345rpx', height: '345rpx', 'flex-shrink': 0 }"
								@click="skipTo(decoItem.data[1].url_type, decoItem.data[1])">
								<image :src="decoItem.data[1].img" mode="aspectFit" style="width: 100%;height: 100%;">
								</image>
							</view>
						</view>
						<view class="combine6">
							<view class="flex_row_center_center"
								:style="{ width: '345rpx', height: '345rpx', 'flex-shrink': 0, 'margin-bottom': '20rpx' }"
								@click="skipTo(decoItem.data[2].url_type, decoItem.data[2])">
								<image :src="decoItem.data[2].img" mode="aspectFit" style="width: 100%;height: 100%;">
								</image>
							</view>
							<view class="flex_row_center_center"
								:style="{ width: '345rpx', height: 345 / 2 + 'rpx', 'flex-shrink': 0 }"
								@click="skipTo(decoItem.data[3].url_type, decoItem.data[3])">
								<image :src="decoItem.data[3].img" mode="aspectFit" style="width: 100%;height: 100%;">
								</image>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 图片组合7-->
		<view v-if="decoItem.sele_style == 7" data-index="index">
			<view class="modules-slide">
				<view class="image-ad images-tpl"
					style="display: flex;justify-content: flex-start;align-items: center;padding-top: 0;">
					<view :style="{ 'display': 'flex', 'flex-wrap': 'wrap', 'width': (670 / 3 * 2 + 61) + 'rpx', }">
						<view class="flex_row_center_center" v-for="(childitem, childindex) in decoItem.data"
							v-if="childindex < 4" :key="childindex" @click="skipTo(childitem.url_type, childitem)"
							:style="{ 'margin-left': '20rpx', width: 670 / 3 + 'rpx', height: 670 / 3 + 'rpx', marginTop: '20rpx', 'flex-shrink': 0 }">
							<image mode="aspectFit" :src="childitem.img" style="width: 100%;height: 100%;">
							</image>
						</view>
					</view>
					<view class="flex_row_center_center" v-if="decoItem.data[4]"
						@click="skipTo(decoItem.data[4].url_type, decoItem.data[4])"
						:style="{ 'margin-top': '20rpx', width: 670 / 3 + 'rpx', height: (670 / 3 * 2 + 20) + 'rpx' }">
						<image mode="aspectFit" :src="decoItem.data[4].img" style="width: 100%;height: 100%;">
						</image>
					</view>
				</view>
			</view>
		</view>
		
		
		<!-- 图片组合8 start -->
		<view  v-if="decoItem.sele_style == 8"
			:style="{
				paddingTop: ((decoItem.top_margin !== undefined && decoItem.top_margin !== null) ? decoItem.top_margin*2 : 20) + 'rpx',
				paddingBottom: ((decoItem.bottom_margin !== undefined && decoItem.bottom_margin !== null) ? decoItem.bottom_margin*2 : 20) + 'rpx',
				background: decoItem.color || 'unset',
			}">
			<view class="featured">
				<view class="featured_item" v-for="(itemTp, indexTp) in decoItem.data" :key="indexTp">
					<view class="featured_tit" @click="skipTo(itemTp.url_type, itemTp)">{{ itemTp.main_title }}</view>
					<view class="featured_text" @click="skipTo(itemTp.url_type, itemTp)">{{ itemTp.sub_title }}</view>
					<view class="featured_img">
						<block v-for="(itemPic, indexPic) in itemTp.img" :key="indexPic">
							<image :src="itemPic.img" mode="aspectFit" @click="skipTo(itemPic.url_type, itemPic)"></image>
						</block>
					</view>
				</view>
			</view>
		</view>
		<!-- 图片组合 end -->
	</view>
</template>
<script>
	import {
		mapState
	} from 'vuex'
	export default {
		props:{
			decoItem:{
				type:Object,
				default:()=>({})
			},
			mode:String
		},
		components: {

		},
		data() {
			return {
				imgUrl: process.env.VUE_APP_IMG_URL
			}
		},
		onLoad() {

		},

		onShow() {

		},

		mounted() {

		},

		methods: {
			skipTo(...args){
				this.$emit.apply(this,['skipTo',...args])
			}
		},
	}
</script>
<style lang="scss">
	.combination_wrap {
		width: 100%;
		background-color: #fff;
		box-sizing: border-box;
	}
	.combine6 {
		display: flex;
		flex-direction: column;
		margin-right: 20rpx;
		margin-top: 20rpx;
	}
	
	
	.combine3 {
		width: calc((750rpx - 60rpx)/2);
	}
	
	.combine4 {
		width: calc((750rpx - 80rpx)/3);
		margin-top: 20rpx;
		margin-left: 20rpx;
	
		image {
			width: 100%;
			height: 100%;
		}
	}
	
	.combine1 {
		display: flex !important;
		flex-direction: column !important;
		text-align: center;
	}
	
	.combine2 {
		display: flex !important;
	}
	
	
	.combine5_wrap>view:nth-child(2n) {
		margin-right: 0 !important;
	}
	
	
	/*图片组合样式  */
	
	.modules-slide {
		display: block;
	}
	
	.modules-slide .image-list.style0,
	.modules-slide .image-list.style0 ul {
		padding: 0;
		margin: 0;
		box-sizing: border-box;
	}
	
	.modules-slide .image-list.style0,
	.modules-slide .image-list.style0 ul {
		padding: 0;
		margin: 0;
		box-sizing: border-box;
		display: block;
	}
	
	.modules-slide .image-list ul {
		overflow: hidden;
		box-sizing: border-box;
		display: block;
	}
	
	.modules-slide .image-list.style0 ul li {
		display: block;
		box-sizing: border-box;
	}
	
	.modules-slide .image-list ul li navigator {
		display: block;
	}
	
	.modules-slide .image-list ul li navigator image {
		width: 100%;
		background: #f1f1f1;
		display: block !important;
	}
	
	.modules-slide image {
		max-width: 100%;
		vertical-align: middle;
		display: inline-block !important;
	}
	
	.modules-slide .image-list {
		overflow: hidden;
	
		.tupianzuhe2 {
			flex-wrap: wrap;
		}
	}
	
	.modules-slide .image-list.style1 ul li {
		display: block;
		margin: 0 16rpx 16rpx;
	}
	
	.modules-slide .image-list.style1 ul li image {
		height: 100%;
	}
	
	.modules-slide .image-list ul li navigator image {
		width: 100%;
		background: #f1f1f1;
		display: block !important;
	}
	
	.modules-slide .image-list.style2 ul,
	.modules-slide .image-list.style3 ul {
		padding-right: 16rpx;
	}
	
	.modules-slide .image-list.style2 ul li {
		box-sizing: border-box;
		padding: 0 0 16rpx 16rpx;
		width: 50%;
		float: left;
	}
	
	.modules-slide .image-list.style3 ul li {
		float: left;
		width: 33.33333%;
		box-sizing: border-box;
		padding: 0 0 16rpx 16rpx;
	}
	
	.modules-slide .image-ad {
		padding: 20rpx 0;
		box-sizing: border-box;
		display: block;
		clear: both;
		overflow: hidden;
	
		.tupianzuhe04_left {
			width: calc((750rpx - 60rpx)/2);
			height: calc((750rpx - 60rpx)/2 + 20rpx);
			margin-right: 20rpx;
			margin-left: 20rpx;
	
			image {
				width: 100%;
				height: 100%
			}
		}
	
		.tupianzuhe04_right_item {
			width: calc((750rpx - 60rpx)/2);
			height: calc((750rpx - 60rpx)/4);
	
			image {
				width: 100%;
				height: 100%
			}
		}
	}
	
	.modules-slide .image-ad>div {
		float: left;
		width: 50%;
		box-sizing: border-box;
	}
	
	.modules-slide .image-ad div navigator {
		display: block;
		margin: 0 16rpx 16rpx 0;
		box-sizing: border-box;
	}
	
	.modules-slide .images-tpl image {
		width: 374rpx;
		vertical-align: middle;
		box-sizing: border-box;
	}
	
	.modules-slide .image-ad2 {
		margin: 0 16rpx 0 0;
		box-sizing: border-box;
		display: block;
		clear: both;
		overflow: hidden;
	}
	
	.modules-slide .image-ad2 .clearfix {
		display: block;
		clear: both;
		overflow: hidden;
	}
	
	.modules-slide .image-ad2 div:first-child navigator:nth-child(1),
	.modules-slide .image-ad2 div:first-child navigator:nth-child(1) image {
		width: 228rpx;
	}
	
	.modules-slide .image-ad2 div:first-child .big:nth-child(2),
	.modules-slide .image-ad2 div:first-child .big:nth-child(2) image {
		width: 473rpx;
	}
	
	.modules-slide .image-ad2 div navigator {
		display: block;
		float: left;
		margin: 0 0 16rpx 16rpx;
		box-sizing: border-box;
	}
	
	.modules-slide .images-tpl img {
		width: 100%;
		vertical-align: middle;
		box-sizing: border-box;
	}
	
	.modules-slide .image-ad2 div:last-child navigator:nth-child(1),
	.modules-slide .image-ad2 div:last-child navigator:nth-child(1) image {
		width: 473rpx;
	}
	
	.modules-slide .image-ad2 div:last-child navigator:nth-child(2),
	.modules-slide .image-ad2 div:last-child navigator:nth-child(2) image {
		width: 228rpx;
	}
	
	.modules-slide .image-ad3 {
		padding: 0 0 0 16rpx;
		box-sizing: border-box;
		display: block;
		clear: both;
		overflow: hidden;
	}
	
	.modules-slide .image-ad3 div {
		width: 367rpx;
		float: left;
		box-sizing: border-box;
	}
	
	.modules-slide .image-ad3 div image {
		width: 351rpx;
	}
	
	.modules-slide .image-ad3 div navigator {
		padding: 0 16rpx 16rpx 0;
		display: inline-block;
		box-sizing: border-box;
	}
	
	.modules-slide .image-ad4 {
		padding: 0 16rpx 16rpx 0;
		box-sizing: border-box;
		display: block;
	}
	
	.modules-slide .image-ad4 div {
		float: left;
		width: 33.33333%;
		box-sizing: border-box;
	}
	
	.modules-slide .image-ad4 div navigator {
		display: block;
		margin: 0 0 16rpx 16rpx;
	}
	
	.featured {
		display: flex;
		justify-content: center;
		flex-wrap: wrap;
		background: #ffffff;
	}
	
	.featured_item {
		width: 49.6%;
		color: #333333;
		border-top: 1rpx solid #DFDFDF;
		border-left: 1rpx solid #DFDFDF;
		padding: 32rpx 22rpx 36rpx;
	}
	
	.featured>.featured_item:nth-child(1) {
		border-top: none;
	}
	
	.featured>.featured_item:nth-child(2) {
		border-top: none;
	}
	
	.featured>.featured_item:nth-child(odd) {
		border-left: none;
	}
	
	.featured_tit {
		font-size: 36rpx;
		font-weight: bold;
		white-space: nowrap;
	}
	
	.featured_text {
		font-size: 26rpx;
		font-weight: 400;
		margin-top: 12rpx;
		margin-bottom: 24rpx;
		white-space: nowrap;
	}
	
	.featured_img {
		overflow: hidden;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
	
	.featured_img>image {
		width: 182rpx;
		height: 192rpx;
		border-radius: 4rpx;
		background: #ECF5FF;
	}
	
	.featured_img>image:first-of-type {
		margin-right: 8rpx;
	}
	
	.featured_img>image:last-of-type {
		margin-left: 8rpx;
	}
	
	.featured_img ::v-deep img {
		opacity: 0 !important;
	}
	
	.featured_img ::v-deep div {
		background-size: cover !important;
	}
	
</style>