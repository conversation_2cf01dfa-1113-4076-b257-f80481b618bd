<template>
	<view>
		<!-- 推荐商品样式一 -->
		<view class="recommend_goods_wrap" v-if="decoItem.show_style == 'small'"
			style="padding:0" :style="{
				paddingTop: ((decoItem.top_margin !== undefined && decoItem.top_margin !== null) ? decoItem.top_margin*2 : 20) + 'rpx',
				paddingBottom: ((decoItem.bottom_margin !== undefined && decoItem.bottom_margin !== null) ? decoItem.bottom_margin*2 : 0) + 'rpx',
				backgroundColor: decoItem.color || 'unset',
			}">
			<view class="rec_goods_wrap" :style="{
					paddingLeft: decoItem.page_margin * 2 + 'rpx',
					paddingRight: decoItem.page_margin * 2 + 'rpx',
					backgroundColor: decoItem.color ? 'unset' : decoItem.border_style == 'border_none_grey_bg' ? 'f8f8f8' : '#fff',
				}">
				<slot></slot>
			</view>
		</view>
	
		<!-- 推荐商品样式二 -->
		<view class="recommend_goods_wrap"
			v-if="decoItem.show_style == 'list'"
			:style="{
				marginTop: 0,
				paddingTop: ((decoItem.top_margin !== undefined && decoItem.top_margin !== null) ? decoItem.top_margin*2 : 20) + 'rpx',
				paddingBottom: ((decoItem.bottom_margin !== undefined && decoItem.bottom_margin !== null) ? decoItem.bottom_margin*2 : 0) + 'rpx',
				paddingLeft: decoItem.page_margin + 'px',
				paddingRight: decoItem.page_margin + 'px',
				backgroundColor: decoItem.color ? decoItem.color : decoItem.border_style == 'border_none_grey_bg' ? 'f8f8f8' : '#fff',
			}">
			<view class="rec_goods_wrap">
				<block v-for="(item, index) in decoItem.data.info" :key="index">
				  <view class="recommend_goods1" v-if="!item.state||(item.state&&item.state == 3)"
						:style="{
						  borderRadius: decoItem.border_radius + 'px',
						  border: decoItem.border_style == 'border_eee' ? '1rpx solid #eee' : '',
						  boxShadow: decoItem.border_style == 'card-shadow' ? 'rgba(93, 113, 127, 0.08) 0px 2px 8px' : '',
						  marginBottom: (index == decoItem.data.info.length-1 ? 0 : decoItem.goods_margin) + 'px'
						}">
						<view class="recommend_goods_img1"
							@click="$emit('toGoodsDetail',item.productId || item.defaultProductId, item.goodsId)">
							<view class="image"
								:style="{ backgroundImage: `url(${item.mainImage})`, borderRadius: border_radius2 }">
							</view>
						</view>
						<view class="recommend_goods_right">
							<view class="recommend_goods_name"
								@click="$emit('toGoodsDetail',item.productId || item.defaultProductId, item.goodsId)">
								{{ item.goodsName }}
							</view>
							<view :class="decoItem.isshow_sales == 1 ? '' : 'hide_sold_wrap'">
								
								<view class="recommend_goods_price" :style="{ position:decoItem.isshow_sales == 1 ? 'absolute' : 'static'}" v-if="mode=='point'">
									<view class="goods_price" v-if="item.marketPrice">
										<text>￥</text>
										<text>{{ $getPartNumber(item.marketPrice, 'int') }}</text>
										<text>{{$getPartNumber(item.marketPrice, 'decimal')}}</text>
									</view>
									<view class="goods_price_point" v-if="item.integralPrice || item.cashPrice">
										<text v-if="item.integralPrice">{{ item.integralPrice }}{{ $L('积分') }}</text>
										<text v-if="item.integralPrice && item.cashPrice">+</text>
										<text v-if="item.cashPrice &&item.integralPrice.toString().length +item.cashPrice.toString().length <=14">{{ $L('￥') }}{{ filters.toFix(item.cashPrice) }}</text>
										<text v-if="item.integralPrice.toString().length +item.cashPrice.toString().length >14">{{ $L('￥') }}...</text>
									</view>
								</view>
								
								
								<view class="recommend_goods_price" :style="{ position: 'absolute'}" v-if="mode=='home'">
									<text class="small_price">￥</text>
									<text class="big_price">{{ $getPartNumber(item.goodsPrice, 'int') }}</text>
									<text class="small_price">{{ $getPartNumber(item.goodsPrice, 'decimal') }}</text>
								</view>
								
								
								<view class="recommend_goods_bottom" 
									:style="{ position: 'absolute', width: '100%' }">
									<view class="have_sold" v-if="decoItem.isshow_sales == 1">
										已售{{ item.actualSales }}件
									</view>
									<block v-if="mode=='home'">
										<image :src="imgUrl + 'add-cart.png'" mode="aspectFit"
											v-if="decoItem.cart_icon_type == 1"
											@click="$emit('addCart',item.productId || item.defaultProductId, item.goodsId, item)">
										</image>
										<image :src="icon2" mode="aspectFit"
											v-if="decoItem.cart_icon_type == 2"
											@click="$emit('addCart',item.productId || item.defaultProductId, item.goodsId, item)">
										</image>
										<image :src="icon3" mode="aspectFit"
											v-if="decoItem.cart_icon_type == 3"
											@click="$emit('addCart',item.productId || item.defaultProductId, item.goodsId, item)">
										</image>
										<image :src="icon4" mode="aspectFit"
											v-if="decoItem.cart_icon_type == 4"
											@click="$emit('addCart',item.productId || item.defaultProductId, item.goodsId, item)">
										</image>
									</block>
								</view>
							</view>
						</view>
					</view>
				</block>
			</view>
		</view>
		
		
		<!-- 推荐商品样式三 -->
		<view class="recommend_goods_wrap"
			v-if="decoItem.show_style == 'big'"
			:style="{
				marginTop: 0,
				paddingTop: ((decoItem.top_margin !== undefined && decoItem.top_margin !== null) ? decoItem.top_margin*2 : 20) + 'rpx',
				paddingBottom: ((decoItem.bottom_margin !== undefined && decoItem.bottom_margin !== null) ? decoItem.bottom_margin*2 : 0) + 'rpx',
				paddingLeft: decoItem.page_margin + 'px',
				paddingRight: decoItem.page_margin + 'px',
				backgroundColor: decoItem.color ? decoItem.color : decoItem.border_style == 'border_none_grey_bg' ? 'f8f8f8' : '#fff',
			}">
			<block v-for="(item, index) in decoItem.data.info" :key="index">
				<view class="recommend_goods2" 
				  v-if="!item.state||(item.state&&item.state == 3)"
					:style="{
					  borderRadius: decoItem.border_radius + 'px',
					  border: decoItem.border_style == 'border_eee' ? '1rpx solid #eee' : '',
					  boxShadow: decoItem.border_style == 'card-shadow' ? 'rgba(93, 113, 127, 0.08) 0px 2px 8px' : '',
					  marginBottom: (index == decoItem.data.info.length-1 ? 0 : decoItem.goods_margin) + 'px'
					}">
					<view class="recommend_goods_img2"
						@click="$emit('toGoodsDetail',item.productId || item.defaultProductId, item.goodsId)">
						<image :src="item.mainImage" mode="aspectFit"
							:style="{ borderRadius: border_radius1 }"></image>
					</view>
					<view class="recommend_goods_bottom2" :style="{ borderRadius: border_radius3 }">
						<view class="recommend_goods_name2"
							@click="$emit('toGoodsDetail',item.productId || item.defaultProductId, item.goodsId)">
							{{ item.goodsName }}
						</view>
						
						<view class="goods_des" v-if="item.goodsBrief">{{ item.goodsBrief }}</view>
						
						<view class="goods_bottom" :style="{ flexDirection: decoItem.isshow_sales == 1 ? '' : 'row', justifyContent: decoItem.isshow_sales == 1 ? '' : 'space-between' }">
							
							
							<view class="recommend_goods_price" style="color:var(--color_main);" v-if="mode=='home'">
								<text class="small_price">￥</text>
								<text class="big_price">{{ $getPartNumber(item.goodsPrice, 'int') }}</text>
								<text class="small_price">{{ $getPartNumber(item.goodsPrice, 'decimal') }}</text>
							</view>
							
							
							<view class="goods_price_point" v-if="(item.integralPrice || item.cashPrice)&&mode=='point'">
								<text v-if="item.integralPrice">{{ item.integralPrice }}{{ $L('积分') }}</text>
								<text v-if="item.integralPrice && item.cashPrice">+</text>
								<text v-if="item.cashPrice &&item.integralPrice.toString().length +item.cashPrice.toString().length <=14">{{ $L('￥') }}{{ filters.toFix(item.cashPrice) }}</text>
								<text v-if="item.integralPrice.toString().length +item.cashPrice.toString().length >14">{{ $L('￥') }}...</text>
							</view>
							
							<view class="recommond_goods3_wrap">
								<view class="have_sold" v-if="decoItem.isshow_sales == 1">
									已售{{ item.actualSales }}件
								</view>
								<block v-if="mode=='home'">
									<image :src="imgUrl + 'add-cart.png'" mode="aspectFit"
										v-if="decoItem.cart_icon_type == 1"
										@click="$emit('addCart',item.productId || item.defaultProductId, item.goodsId, item)">
									</image>
									<image :src="icon2" mode="aspectFit" v-if="decoItem.cart_icon_type == 2"
										@click="$emit('addCart',item.productId || item.defaultProductId, item.goodsId, item)">
									</image>
									<image :src="icon3" mode="aspectFit" v-if="decoItem.cart_icon_type == 3"
										@click="$emit('addCart',item.productId || item.defaultProductId, item.goodsId, item)">
									</image>
									<image :src="icon4" mode="aspectFit" v-if="decoItem.cart_icon_type == 4"
										@click="$emit('addCart',item.productId || item.defaultProductId, item.goodsId, item)">
									</image>
								</block>
							</view>
						</view>
					</view>
				</view>
			</block>
		</view>
	</view>
</template>
<script>
	import filters from '../../utils/filter'
	export default {
		props: {
			decoItem:{
				type:Object,
				default:()=>({})
			},
			mode:{
				type:String,
				default:'home'
			}
		},
		components: {
		},
		
		watch:{
			decoItem(val){
				if (val.show_style == "big") {
					this.border_radius1 = val.border_radius + 'px' + ' ' + val.border_radius + 'px' + ' 0 0'
					this.border_radius3 = '0 0 ' + val.border_radius + 'px' + ' ' + val.border_radius + 'px'
				} else if (val.show_style == "list") {
					this.border_radius2 = val.border_radius + 'px' + ' 0 0 ' + val.border_radius + 'px'
				}
			}
		},
		
		
		data() {
			return {
				imgUrl: process.env.VUE_APP_IMG_URL,
				border_radius1:"",
				border_radius3:"",
				border_radius2:"",
				filters
			}
		},
		onLoad() {

		},

		onShow() {

		},

		mounted() {

		},

		methods: {
			toGoodsDetail(productId, goodsId) {
				this.$Router.push({
					path: '/standard/product/detail',
					query: {
						productId,
						goodsId
					}
				})
			},

		},
	}
</script>
<style lang="scss">
	page {}
	.recommend_goods_wrap {
		display: flex;
		justify-content: space-between;
		flex-wrap: wrap;
		padding: 20rpx 20rpx 0;
	}
	
	.hide_sold_wrap {
		width: 100%;
		position: absolute;
		bottom: 20rpx;
		left: 0;
		display: flex;
		justify-content: space-between;
	}
	
	.recommond_goods3_wrap {
		display: flex;
		justify-content: space-between;
		align-items: center;
		position: absolute;
		bottom: 22rpx;
		left:22rpx;
		right: 22rpx;
	}
	
	.have_sold {
		font-size: 24rpx;
		color: #9a9a9a;
	}
	
	.rec_goods_wrap {
		width: 750rpx;
		display: flex;
		justify-content: space-between;
		flex-wrap: wrap;
	}
	
	.rec_goods_wrap>view:nth-child(2n) {
		margin-right: 0 !important;
	}
	
	.big_price {
		font-size: 34rpx;
	}
	
	.small_price {
		font-size: 24rpx;
	}
	
	.goods_price {
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 500;
		text-decoration: line-through;
		color: #999999;
		margin-bottom: 20rpx;
	}
	
	.goods_price_point {
		height: 35rpx;
		background: var(--color_integral_main);
		border-radius: 18px;
		padding: 0 13rpx;
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #ffffff;
		line-height: 35rpx;
		white-space: nowrap;
		display: inline-block;
	}
	
	
	// 推荐商品
	.recommend_goods1 {
		width: 100%;
		height: 350rpx;
		display: flex;
		background-color: #fff;
		border-radius: 20rpx;
		margin-bottom: 20rpx;
		border-radius: 20rpx;
	
		.recommend_goods_img1 {
			width: 350rpx;
			height: 350rpx;
			margin-right: 20rpx;
	
			.image {
				width: 350rpx;
				height: 350rpx;
				background-color: #ccc;
				background-position: center center;
				background-size: cover;
				background-repeat: no-repeat;
			}
		}
	
		.recommend_goods_right {
			width: 100%;
			height: 350rpx;
			position: relative;
			
			.goods_des {
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #888888;
				line-height: 36rpx;
				box-sizing: border-box;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
	
			.recommend_goods_name {
				padding-right: 20rpx;
				font-size: 30rpx;
				margin-top: 20rpx;
				text-overflow: -o-ellipsis-lastline;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				line-clamp: 2;
				-webkit-box-orient: vertical;
			}
	
			.recommend_goods_price {
				position: absolute;
				bottom: 80rpx;
				left: 0;
				color: var(--color_price);
			}
	
			.recommend_goods_bottom {
				width: 100%;
				position: absolute;
				bottom: 20rpx;
				left: 0;
				display: flex;
				justify-content: space-between;
	
				image {
					width: 42rpx;
					height: 42rpx;
					margin-right: 20rpx;
				}
			}
		}
	}
	
	.recommend_goods2 {
		display: flex;
		flex-direction: column;
		width: 100%;
	
		.recommend_goods_img2 {
			width: 100%;
			height: 702rpx;
	
			image {
				width: 100%;
				height: 702rpx;
				background-color: #ccc;
			}
		}
	
		.recommend_goods_bottom2 {
			width: 100%;
			height: 204rpx;
			padding: 20rpx;
			box-sizing: border-box;
			background-color: #fff;
			position: relative;
	
			.recommend_goods_name2 {
				font-size: 30rpx;
				text-overflow: -o-ellipsis-lastline;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				line-clamp: 2;
				-webkit-box-orient: vertical;
				margin-bottom: 10rpx;
			}
	
			.goods_bottom {
	
				image {
					width: 42rpx;
					height: 42rpx;
				}
			}
		}
	}
	
</style>