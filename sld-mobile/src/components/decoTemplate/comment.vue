<template>
    <!-- 评价 start-->
    <view :style="mix_diyStyle">
        <view class="container" id="deContainer">
            <view class="eva_section" id="nav2">
                <view class="e_header flex_row_between_center">
                    <view class="left flex_row_start_end">
                        <text class="tit">{{ $L("商品评价") }}</text>
                        <text class="e_num" v-if="goodsCommentsInfo && goodsCommentsInfo.commentsCount">({{
                            goodsCommentsInfo.commentsCount }})</text>
                        <text class="e_rate" v-if="goodsCommentsInfo && goodsCommentsInfo.highPercent">{{
                            goodsCommentsInfo.highPercent != "0%"
                            ? $L("好评率") + goodsCommentsInfo.highPercent
                            : $L("暂无好评")
                        }}</text>
                    </view>
                    <view class="right flex_row_end_center" @click="goEvaluation">
                        <text class="view_more">{{ $L("查看更多") }}</text>
                        <image :src="imgUrl + 'goods_detail/right_down.png'" mode="aspectFit" class="right_down">
                        </image>
                    </view>
                </view>
                <view class="eva_box flex_column_start_start" v-if="goodsCommentsInfo &&
                    goodsCommentsInfo.list &&
                    goodsCommentsInfo.list.length > 0
                    ">
                    <view class="e_member_info flex_row_start_center">
                        <image class="portrait" :src="goodsCommentsInfo.list[0].memberAvatar" mode="aspectFill">
                        </image>
                        <text class="name">{{
                            filters.toAnonymous(goodsCommentsInfo.list[0].memberName)
                        }}</text>
                        <!-- 只读状态 -->
                        <uni-rate :readonly="true" :value="goodsCommentsInfo.list[0].score" active-color="var(--color_main)"
                            disabledColor="#ccc" :size="18" />
                    </view>
                    <text class="con" v-if="goodsCommentsInfo.list[0].content">{{
                        goodsCommentsInfo.list[0].content
                    }}</text>
                    <text class="con" v-else>{{
                        goodsCommentsInfo.list[0].score >= 4
                        ? $L("好评")
                        : goodsCommentsInfo.list[0].score < 2 ? $L("差评") : $L("中评") }}</text>
                </view>
            </view>
            <!-- 评价end -->
        </view>
    </view>
</template>
<script type="ts">
import filters from "@/utils/filter.js";
export default {
    data() {
        return {
            filters,
            goodsCommentsInfo: {},
            imgUrl: process.env.VUE_APP_IMG_URL,
        }
    },
    props: {
        goodsId: {
            type: String,
            default: ''
        }
    },
    async mounted() {
        console.log('onload...')
        this.getGoodsComment()
    },
    methods: {
        //获取商品评价
        getGoodsComment() {
            let param = {};
            param.url = "v3/goods/front/goods/comment";
            param.method = "GET";
            param.data = { productId: 200001030018 };
            console.log('>>>', param.data)
            this.$request(param).then((res) => {
                if (res.state == 200) {
                    let result = res.data;
                    this.goodsCommentsInfo = result;
                } else {
                    this.$api.msg(res.msg);
                }
            });
        },
        //去商品评价页面
        goEvaluation() {
            this.$Router.push({
                path: "/standard/product/evaluation",
                query: {
                    productId: this.productId,
                },
            });
        },
    }
}
</script>
<style lang="scss" scoped>
page {
    background: $bg-color-split;
    width: 750rpx;
    margin: 0 auto;
}

button::after {
    border: none;
}

.container {
    position: relative;
}

/* 评价 start*/
.eva_section {
    display: flex;
    flex-direction: column;
    padding: 30rpx 0 30rpx;
    background: #fff;
    width: 100%;
    margin-top: 20rpx;

    .e_header {
        height: 33rpx;
        display: flex;
        align-items: center;
        padding: 0 8rpx 0 20rpx;
        box-sizing: border-box;

        .left {
            color: #333333;

            /* 				display: flex;
				align-items: center; */
            .tit {
                font-size: 30rpx;
            }

            .e_num {
                font-size: 30rpx;
                margin-left: 6rpx;
                padding-bottom: 4rpx;
            }

            .e_rate {
                color: #666;
                font-size: 22rpx;
                margin-left: 19rpx;
            }
        }

        .right {
            color: #666666;

            .view_more {
                font-size: 24rpx;
            }

            .iconfont {
                font-size: 18rpx;
            }
        }
    }

    .eva_box {
        .e_member_info {
            margin-top: 30rpx;
            padding: 0 20rpx;

            .portrait {
                width: 50rpx;
                height: 50rpx;
                border-radius: 50%;
            }

            .name {
                color: #2d2d2d;
                font-size: 26rpx;
                margin: 0 20rpx;
            }
        }

        .con {
            color: #333333;
            font-size: 26rpx;
            line-height: 38rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            word-break: break-word;
            width: 710rpx;
            margin: 20rpx auto 0;
        }

        .view_more_eva {
            width: 100%;
            color: #2d2d2d;
            font-size: 26rpx;
            margin-top: 22rpx;

            &:before {
                content: " ";
                width: 160rpx;
                height: 1rpx;
                background: rgba(0, 0, 0, 0.1);
                margin-right: 20rpx;
            }

            &:after {
                content: " ";
                width: 160rpx;
                height: 1rpx;
                background: rgba(0, 0, 0, 0.1);
                margin-left: 20rpx;
            }
        }
    }
}
</style>