<!-- 商品组件：横向展示，一行一个，商品列表页面
	点击进入商品详情页
	加入购物车事件
-->
<template name="goodsCollectItemH">
	<view class="flex_row_start_center" :class="{goods_main}">
		<view class="goods_body" :class="{bottom_radius:bottom_radius,paddingSide}"
			:style="{width: bodyWidth}">
			<view class="goods_h_item flex_row_start_start" @click="goGoodsDetail(goods_info)">
				<view class="goods-img4" :style="{backgroundImage: 'url('+(goods_info.goodsImage?goods_info.goodsImage:goods_info.productImage)+')'}"></view>
				<view class="right flex_column_between_start" :style="{height:'170rpx'}">
					<view class="top flex_column_start_start">
						<view class="goods-name">{{goods_info.goodsName}}</view>
						<text class="goods-brief">{{goods_info.goodsBrief}}</text>
					</view>
					<view class="bottom flex_row_between_center">
						<view class="bottom_price">
							<view class="left_price flex_row_start_end">
								<text class="unit">￥</text>
								<text class="price_int" v-if="goods_info.productPrice">{{$getPartNumber(goods_info.productPrice,'int')}}</text>
								<text class="price_int" v-else-if="goods_info.goodsPrice">{{$getPartNumber(goods_info.goodsPrice,'int')}}</text>
								<text class="price_int" v-else>0</text>
								<text class="price_decimal" v-if="goods_info.productPrice">{{$getPartNumber(goods_info.productPrice,'decimal')}}</text>
								<text class="price_decimal" v-else-if="goods_info.goodsPrice">{{$getPartNumber(goods_info.goodsPrice,'decimal')}}</text>
							</view>
						</view>
						<view class="bottom_num" v-if="deliverNum && goods_info.deliverNum">
							x{{goods_info.deliverNum}}
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				imgUrl: process.env.VUE_APP_IMG_URL,
			}
		},
		props: {
			goods_info: {
				type: Object,
				default: ()=>{}
			},
			goods_type: {	//商品组件底部展示类型: 1 金额/销量/加购弹窗  2 金额/购买数量
				type: Number,
				default: 1
			},
			bottom_radius: { //最后一个商品底部是否显示圆角
				type: Boolean,
				default: false,
			},
			orderSn: {	//是否点击商品跳转到订单详情
				type: String,
				default: ''
			},
			bodyWidth: {
				type: String,
				default: '750rpx'
			},
			deliverNum: { //物流信息-展示数量
				type: Boolean,
				default: false
			},
			goods_main: { //最外层padding样式是否启用
				type: Boolean,
				default: false
			},
			paddingSide:{
				type: Boolean,
				default: true
			}
		},
		mounted() {
		},
		methods: {
			//跳转店铺详情页面
			goStoreDetail(vid) {
				this.$Router.push({
					path: '/standard/store/shopHomePage',
					query: {
						vid
					}
				})
			},
			//进入商品详情页/订单详情页
			goGoodsDetail(goods_info) {
				if (this.orderSn) {
					let query = {
						orderSn: this.orderSn
					}
					this.$Router.push({
						path: '/pages/order/detail',
						query
					})
				} else {
					this.$Router.push({
						path: '/standard/product/detail',
						query: {
							productId: goods_info.defaultProductId ? 
								goods_info.defaultProductId : goods_info.productId,
							goodsId: goods_info.goodsId
						}
					})
				}
			}
		}
	}
</script>

<style lang='scss'>
	.goods_main {
		/* #ifdef MP-WEIXIN ||H5*/
		width: 750rpx;
		padding: 0 20rpx;
		background: #ffffff;
		.goods_body {
			padding: 0;
		}
		/* #endif */
	}
	.goods_body {
		position:relative;
		transition: all 0.3s;
		width: 100%;
		overflow: hidden;
		flex-shrink: 0;
		/* padding: 20rpx 0; */
		background: #ffffff;
		&.bottom_radius {
			border-bottom-left-radius: 20rpx;
			border-bottom-right-radius: 20rpx;
		}
		&.paddingSide{
			padding-left: 20rpx;
			padding-right: 20rpx;
		}
	}

	.spec {
		width: 100%;
		line-height: 42rpx;
		color: #001111;
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: bold;
		border-top: 1rpx solid #f5f5f5;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		background: #fff;
		padding: 14rpx 0;
	}

	.goods_h_item {
		width: 100%;
		background: #fff;
		padding: 2rpx 0;
		overflow: hidden;
		background: #fff;

		/* &:first-child {
			padding-top: 20rpx;
		} */

		.goods-img {
			background-size: cover;
			background-position: center center;
			background-repeat: no-repeat;
			width: 260rpx;
			height: 260rpx;
			overflow: hidden;
			border-radius: 10rpx;
			background-color: #F8F6F7;
			flex-shrink: 0;
		}

		.goods-img4 {
			background-size: cover;
			background-position: center center;
			background-repeat: no-repeat;
			width: 170rpx;
			height: 170rpx;
			overflow: hidden;
			border-radius: 10rpx;
			background-color: #F8F6F7;
			flex-shrink: 0;
		}

		.right {
			position: relative;
			height: 290rpx;
			padding: 10rpx 0 0rpx;
			flex: 1;
			padding-left: 20rpx;
			
			.more_option{
				position: absolute;
				bottom: 0rpx;
				right: 10rpx;
			}

			.AtoProJ {
				margin-top: 10rpx;
				image {
					width: 40rpx;
					height: 40rpx;
				}
			}
		}

		.goods-name {
			width: 100%;
			font-size: 28rpx;
			color: $com-main-font-color;
			line-height: 38rpx;
			height: 76rpx;
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
			word-break: break-all;
		}
		
		.top {
			width: 100%;
			&.fix_top {
				height: 138rpx;
				.goods-name {
					height: auto;
					min-height: 40rpx;
				}
			}
		}

		.goods-brief {
			color: $main-third-color;
			font-size: 22rpx;
			margin-top: 10rpx;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			width: 100%;
		}

		.state_info {
			view {
				background: #F7F6F7;
				border-radius: 19px;
				padding: 10rpx 20rpx;
				font-size: 22rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #5F5E5C;
				margin-right: 12rpx;
			}
		}

		.goods-price {
			width: 100%;
			margin-bottom: 6rpx;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;

			.left {
				color: #F30300;

				.left_price {
					align-items: baseline;
				}

				.unit,
				.price_decimal {
					font-size: 26rpx;
				}

				.price_int {
					font-size: 36rpx;
				}
				
				.price_img {
					width: 84rpx;
					height: 42rpx;
					margin-left: 4rpx;
					margin-right: 10rpx;
					flex-shrink: 0;
				}
				.price_ori {
					color: #999999;
					font-size: 24rpx;
					font-family: PingFang SC;
					text-decoration: line-through;
				}

				.sales {
					color: $main-third-color;
					font-size: 22rpx;
					margin-left: 26rpx;
					margin-top: 2rpx;
				}
			}

			image {
				width: 42rpx;
				height: 42rpx;
			}
		}

		.bottom {
			width: 100%;

			.bottom_price {
				color: #222222;
				font-size: 24rpx;
				font-family: PingFang-SC-Medium, PingFang-SC;
				font-weight: 600;

				.left_price {
					align-items: baseline;
				}

				.unit,
				.price_decimal {
					font-size: 24rpx;
				}

				.price_int {
					font-size: 34rpx;
				}
			}

			.bottom_num {
				color: #666;
				font-size: 26rpx;
				font-family: PingFang-SC-Medium, PingFang-SC;
				font-weight: 500;
			}
		}
	}

	.activity_con1 {
		display: flex;
		font-size: 22rpx;
		color: #ffffff;

		.act_label {
			height: 32rpx;
			border-radius: 15rpx;
			line-height: 32rpx;
			padding: 0 10rpx;
			margin-left: 10rpx;
		}

		.ladder_group {
			background: linear-gradient(22deg, #FE901E 0%, #FEAD28 100%);
		}

		.discounts {
			background: linear-gradient(17deg, #AB32CC 0%, #D20FA6 100%);
		}

		.secKill {
			background: linear-gradient(to right, #fc5300, #ff1353);
		}

		.preSale {
			background: linear-gradient(to right, #a62fcd, #ff006c);
		}

		.act_label:nth-child(1) {
			margin-left: 0rpx;
		}

		.spellGroup {
			background: linear-gradient(to right, #ff6000, #ff9c00);
		}

	}

	.store_enter1 {
		display: flex;
		font-size: 24rpx;
		align-items: center;
		margin-bottom: 8rpx;
	}

	.stroe_name {
		max-width: 200rpx;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		color: #666666;
		font-size: 26rpx;
	}

	.store_enter_btn {
		color: #333333;
		font-weight: bold;
		margin-left: 10rpx;
		font-size: 26rpx;
	}

	.store_enter_image {
		width: 11rpx;
		height: 19rpx;
		margin-left: 10rpx;
	}

	.rank_tag {
		display: inline;
		background: #FEF1E8;
		border-radius: 2px;
		padding: 2rpx 6rpx;
		font-size: 20rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #FF6932;
	}

	.operate_wrap{
		position: relative;
		width: 160rpx;
		height: 374rpx;
		transition: all 0.3s;
		background-color: #fff;
		box-sizing: border-box;
		.operate_btn{
			width: 160rpx;
			height: 374rpx;
			font-size:28rpx;
			color:#fff;
			text-align: center;
			display: flex;
			justify-content: center;
			align-items: center;
			background: #FF9518;
			transition: all 0.3s;
		}
		.share_btn{
			background: #FF9518;
		}
		.to_shop_btn{
			background: #FDBF19;
			border-top-right-radius: 10rpx;
		}
		.delete_btn{
			background: #FF0D24;
			border-bottom-right-radius: 10rpx;
		}
	}
</style>