<template>
	<view class="address-section" @click="$emit('click')">
		<view class="order-content">
			<block v-if="addressData.addressId != undefined">
				<view class="cen">
					<view class="top flex_row_start_center">
						<view v-if="addressData.isDefault == 1" class="tag">默认</view>
						<text>{{ addressData.addressAll }}</text>
					</view>
					<text class="address">{{ addressData.addressAll }}{{ addressData.detailAddress }}</text>
					<view class="member_info flex_row_start_center">
						<text class="name">{{ addressData.memberName }}</text>
						<text class="mobile">{{ addressData.telMobile }}</text>
					</view>
				</view>
				<text class="iconfont iconziyuan11"></text>
			</block>
			
			<view class="empty_address flex_row_center_center" v-if="addressData.addressId == undefined">
				<text class="add_icon">+</text>
				<text class="tit">{{ $L('新建收货地址') }}</text>
			</view>
		</view>
		
	</view>
</template>

<script>
	import {
		imgUrl
	} from '@/utils/config'
	export default {
		props: ['addressData'],
		data() {
			return{
				imgUrl
			}
		}
	}
</script>

<style lang="scss">
	.address-section {
		background: #fff;
		position: relative;
	
		.order-content {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 20rpx;
			padding-bottom: 28rpx;
			.iconfont {
				color: $main-second-color;
				font-size: 26rpx;
			}
	
			.cen {
				.top {
					.tag {
						width: 63rpx;
						height: 32rpx;
						margin-right: 20rpx;
						background: var(--color_vice);
						line-height: 32rpx;
						text-align: center;
						font-size: 24rpx;
						color: #fff;
						border-radius:4rpx
					}
	
					text {
						color: #2d2d2d;
						font-size: 26rpx;
					}
				}
	
				.address {
					margin-top: 16rpx;
					margin-right: 20rpx;
					color: #2d2d2d;
					font-size: 32rpx;
					font-weight: bold;
					line-height: 40rpx;
					text-overflow: -o-ellipsis-lastline;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					line-clamp: 2;
					-webkit-box-orient: vertical;
					word-break: break-all;
				}
	
				.member_info {
					margin-top: 15rpx;
					font-size: 28rpx;
	
					.name {
						color: #2d2d2d;
					}
	
					.mobile {
						color: $main-third-color;
						margin-left: 40rpx;
					}
				}
			}
	
			.empty_address {
				color: $main-font-color;
				font-size: 34rpx;
				flex: 1;
				height: 120rpx;
	
				.add_icon {
					margin-right: 20rpx;
					font-size: 28rpx;
					color: #2D2D2D;
				}
				
				.tit{
					font-size: 28rpx;
					color: #2D2D2D;
				}
			}
		}
	
		.cen {
			display: flex;
			flex-direction: column;
			flex: 1;
			font-size: 28rpx;
		}
	}
	//ss
</style>