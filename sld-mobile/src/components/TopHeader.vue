<template>
	<view :style="{height:topFixedHeight,background}">

	</view>
</template>
<script>
	import {
		mapState
	} from 'vuex'
	export default {
		props: {
			background:String,
		},
		
		computed:{
			topFixedHeight(){
				let system = uni.getSystemInfoSync()
				return system.statusBarHeight + 'px'
			}
		},
		
		components: {

		},
		data() {
			return {
				imgUrl: process.env.VUE_APP_IMG_URL
			}
		},
		onLoad() {

		},

		onShow() {

		},

		mounted() {

		},

		methods: {

		},
	}
</script>
<style lang="scss">
	page {}
</style>