## 1.2.3（2024-09-19）
1. 更新示例工程
## 1.2.2（2024-05-08）
1. 修改util.js路径为@/uni_modules/sp-html2canvas-render/utils/index.js
## 1.2.1（2024-05-08）
1. 更新示例工程
## 1.2.0（2024-04-30）
1. 示例工程中示例二已更新解决截图模糊的问题，请参考
## 1.1.9（2024-04-30）
1.更新示例工程
## 1.1.8（2024-04-30）
1. 优化了报错提示
2. domId设为必填
## 1.1.7（2024-04-29）
1. h2c配置项更新
## 1.1.6（2024-03-08）
1. 更新示例工程
2. 更新文档
## 1.1.5（2024-02-29）
1. 更新示例工程示例四：横向长截图
## 1.1.4（2024-02-27）
1. 更新base64ToPath方法，以供需要用临时路径保存文件的小伙伴，见示例一中使用
## 1.1.3（2024-02-24）
1. 更新示例工程：示例3：动态domid，可动态选择单独导出指定dom
## 1.1.2（2024-02-06）
1. 更新文档
## 1.1.1（2024-02-06）
1. 重大更新，详见文档
2. 更新示例工程
## 1.1.0（2024-02-05）
1. 更新文档
## 1.0.9（2024-02-05）
1. 重要更新：插件更新内置了urlToBase64工具方法，图片报错toDataURL on HTMLCanvasElement或其他问题导致图片无法正常渲染的，可以使用该工具方法将图片路径(支持网络路径和本地相对路径)转换为base64格式
## 1.0.8（2024-02-04）
1. 更新示例工程
## 1.0.7（2024-01-24）
1. 更新示例工程延迟渲染示例
## 1.0.6（2024-01-16）
1. 更新示例工程
2. 更新文档
## 1.0.5（2024-01-15）
1. 更新示例工程
## 1.0.4（2023-12-08）
1. 更新html2canvas中部分配置
2. 重新上传示例工程，还请麻烦用hbuilderx一键导入示例，不要使用zip下载的方式
3. 更新文档说明，很多人问toDataURL on HTMLCanvasElement的问题，有的人哪怕直接使用实例工程也会报错，有的人却一切正常，我对此提供了部分解决方式。
## 1.0.3（2023-11-29）
1.更新示例工程，内联使用方式详见inner页面
## 1.0.2（2023-11-27）
1.更新示例工程样例
## 1.0.1（2023-11-27）
1.解决在vue2的ap真机环境下可能出现的报错

2.更新示例代码，兼容vue2/3写法，支持图片生成样例
## 1.0.0（2023-10-20）
1. 组件更新，使用方式详见README
2. 更新示例项目
