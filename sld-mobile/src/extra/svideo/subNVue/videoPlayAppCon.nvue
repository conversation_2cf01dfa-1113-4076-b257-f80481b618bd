<template>
	<view>
		<list v-if="loadReady" ref="listBox" :loadmoreoffset="wHeight*3" :show-scrollbar="true" :pagingEnabled="true"
			:scrollable="true" @scroll="touchMove" @loadmore="getMoreVideo">
			<cell v-for="(item,index) in videoList" :key="index" :data-index="index" @appear="appear" @disappear="disappear">
				<view class="wrap" :style="'height: '+ boxStyle.height +';'">
					<video v-if="item.videoDetail && Math.abs(curVideoIndex-index)<2" :id="'sldVideo_child'+item.video_id" ref="sldVideo_child" class="live_back"
						:src="item.videoDetail.videoPath" loop controls="false" show-fullscreen-btn="false" show-progress object-fit="contain" :autoplay="false"
						show-center-play-btn="false" enable-play-gesture :style="boxStyle" http-cache :data-index="index" @play="bindplay" @pause="bindpause">
					</video>
					
					<view class="live_pause" @tap.stop="chansgedd">
						<image v-if="!item.videoPlay" class="live_pause_img" :src="imgUrl + 'svideo/svideo_play.png'" mode="aspectFit" lazy-load></image>
					</view>
					
					<view class="live_header" v-if="item.authorDetail">
						<image class='live_header_go_back' :src="imgUrl+'svideo/white_arrow_l.png'" mode="scaleToFill" @tap="goBack" />
						<view class="live_header_avator" :data-authorId="item.authorDetail.authorId" @tap="goLiveUserCenter">
							<image mode="aspectFill" class="live_header_avator_img" :src="item.authorDetail.memberAvatar" />
						</view>
						<view class="live_header_mem_info" :data-authorId="item.authorDetail.author_id" @tap="goLiveUserCenter">
							<text class="live_header_mem_info_name">{{item.authorDetail.memberNickname?item.authorDetail.memberNickname:item.authorDetail.memberName}}</text>
							<view class="live_header_mem_info_stat_num">
								<text class="live_header_mem_info_stat_num_click_num">{{item.authorDetail.fansNum}}{{Ls('粉丝')}}</text>
							</view>
						</view>
						<view v-if="!hasLogin">
							<view class="live_header_live_fllow" @tap="collect">
								<image class="live_header_live_fllow_image" :src="imgUrl+'svideo/fav_a.png'"></image>
								<text class="live_header_live_fllow_text">{{Ls('关注')}}</text>
							</view>
						</view>
						<view v-else>
							<view v-if="!item.videoDetail.isSelf" class="live_header_live_fllow" @tap="collect"
								:style="'backgroundColor:' + (item.authorDetail.isFollow?'#999':'#fc1c1c')">
								<image class="live_header_live_fllow_image" v-if="!item.authorDetail.isFollow" :src="imgUrl+'svideo/fav_a.png'"></image>
								<text class="live_header_live_fllow_text">{{item.authorDetail.isFollow?Ls('已关注'):Ls('关注')}}</text>
							</view>
						</view>
					</view>
					
					<view class="right_control" v-if="item.videoDetail">
						<block v-if="item.videoDetail.state==2">
							<image class="right_control_image" @tap="like"
								:src="imgUrl+(item.videoDetail.isLike == true ?'svideo/dianzan_complete.png':'svideo/dianzan.png')"></image>
							<text class="right_control_text">{{item.videoDetail.likeNum}}</text>
							<image class="right_control_image" :src="imgUrl+'svideo/play_comment.png'" @tap="openCommnet"></image>
							<text class="right_control_text">{{(item.videoDetail.commentNum && item.videoDetail.commentNum > 0) ? item.videoDetail.commentNum : '评论'}}</text>
							<button @tap="showShare" class="right_control_share_btn">
								<image class="right_control_image" :src="imgUrl+'svideo/share.png'"></image>
							</button>
							<text class="right_control_text">{{Ls('分享')}}</text>
						</block>
						<block v-if="item.videoDetail.isSelf == 1">
							<image class="right_control_image" :src="imgUrl+'svideo/del_video.png'" @tap="del_video"></image>
							<text class="right_control_text">{{Ls('删除')}}</text>
						</block>
						<view class="personal_homepage" @tap="gPensonalCenter" data-curTab="video">
							<image class="personal_homepage_personal_homepage_image" :src="item.member_avatar"></image>
						</view>
					</view>

					<view v-if="shareWrap" class="select-wrap" catchtouchmove="touchmoveshare" @tap.stop="touch">
						<view class="select-wrap_share-mode">
							<view class="select-wrap_share-mode_share-img"></view>

							<view class="select-wrap_share-mode_ul">
								<button @tap.stop="sldShare(0,'WXSceneSession',$event)" class="share-mode_item">
									<image class="share-mode_item_image" :src="imgUrl+'goods_detail/wx_share.png'" mode="widthFix"></image>
									<text class="share-mode_item_text">{{Ls('微信好友')}}</text>
								</button>
								<button @tap.stop="sldShare(0,'WXSenceTimeline',$event)" class="share-mode_item">
									<image class="share-mode_item_image" :src="imgUrl+'svideo/pyq_share.png'" mode="widthFix"></image>
									<text class="share-mode_item_text">{{Ls('微信朋友圈')}}</text>
								</button>
							</view>

							<view class="select-wrap_close" @tap="closeShare">
								<image class="select-wrap_close_image" :src="imgUrl+'svideo/share_close.png'" mode="widthFix"></image>
							</view>
						</view>
					</view>

					<view class="video_footer" v-if="item.videoDetail">
						<text class="video_footer_title">{{item.videoDetail.videoName}}</text>
						<text class="video_footer_desc">{{item.videoDetail.introduction}}</text>
						<view v-if="item.goodsList.length == 0" style="width:100%;height:20rpx"></view>
						<scroll-view scroll-x="true" class="video_footer_video_goods" v-if="item.goodsList.length > 0">
							<view v-for="(items, index) in item.goodsList" :key="index" class="video_footer_goods_item">
								<view class="video_footer_goods_item_goods_img" :data-goodsId="items.goodsId" @tap="goGoodsDetail(items)">
									<image class="video_footer_goods_item_goods_img_img" :src="items.goodsImage" mode="aspectFill" />
								</view>
								<view class="video_footer_goods_item_goods_detail">
									<text class="video_footer_goods_item_goods_name" :data-goodsId="items.goodsId"
										@tap="goGoodsDetail(items)">{{items.goodsName}}</text>
									<view class="video_footer_goods_item_goods_info">
										<text class="video_footer_goods_item_goods_price">{{Ls('¥')}}{{items.goodsPrice}}</text>
										<image class="video_footer_goods_item_add_cart" :data-goodsId="items.goodsId"
											@tap="addCart(items)" :src="imgUrl+'svideo/add_cart.png'"></image>
									</view>
								</view>
							</view>
						</scroll-view>
					</view>

					<!-- 评论模块 start -->
					<view class="video_comment" v-if="item.showComment" @touchmove.stop.prevent="moveHandle">
						<view class="video_comment_title">
							<view class="video_comment_title_com_t_l">
								<text class="video_comment_title_com_t_l_com_t_l_title">{{Ls('全部评论')}}</text>
								<text class="video_comment_title_com_t_l_com_t_l_total">{{Ls('共有')}}{{item.videoDetail.commentNum}}{{Ls('条评论')}}</text>
							</view>
							<image class="video_comment_title_com_t_close" :src="imgUrl+'svideo/close.png'" @tap="closeComment"></image>
						</view>

						<scroll-view class="video_comment_comment" scroll-y="true" @scrolltolower="getMoreCom">
							<view v-for="(items, index) in item.commentList" :key="index" class="video_comment_item_wrap"
								:index="index" v-if="item.commentList.length>0">
								<view class="video_comment_item_wrap_item">
									<view class="video_comment_item_wrap_item_l">
										<view class="video_comment_item_wrap_item_l_com_avator">
											<image class="video_comment_item_wrap_item_l_com_avator_img" mode="aspectFill" :src="items.memberAvatar" />
										</view>
										<view class="video_comment_item_wrap_item_l_com_detail" :data-commentId="items.commentId"
											:data-authorId="items.authorId" :data-memberNickname="items.authorName" @tap="replyComment">
											<text class="video_comment_item_wrap_item_l_com_detail_name">{{items.authorName?items.authorName:items.fromAuthorName}}</text>
											<text class="video_comment_item_wrap_item_l_com_detail_con">{{items.content}}</text>
											<view class="video_comment_item_wrap_item_l_com_detail_other">
												<text class="video_comment_item_wrap_item_l_com_detail_other_time">{{items.createTime}}</text>
												<text v-if="items.isSelf" class="video_comment_item_wrap_item_l_com_detail_other_del_com"
													data-type="comment" :data-commentId="items.commentId" @tap.stop="delCom">{{Ls('删除')}}</text>
											</view>
										</view>
									</view>
									<view class="video_comment_item_wrap_item_r">
										<image class="video_comment_item_wrap_item_r_image" :data-commentId="items.commentId" @tap="likeComment"
											:src="imgUrl+(items.isLike?'svideo/dz_complete.png':'svideo/dz.png')"></image>
										<text class="video_comment_item_wrap_item_r_text">{{items.likeNum}}</text>
									</view>
								</view>

								<view v-for="(itemReplay, index2) in items.replyList" :key="index2" class="video_comment_child"
									:index="itemIndex" v-if="items.replyList.length>0">
									<view class="video_comment_child_item">
										<view class="video_comment_child_item_l">
											<view class="video_comment_child_item_l_avator">
												<image class="video_comment_child_item_l_avator_img" mode="aspectFill" :src="itemReplay.memberAvatar" />
											</view>
											<view class="video_comment_child_item_l_detail" :data-commentId="items.commentId"
												:data-authorId="itemReplay.from_author_id" :data-memberNickname="itemReplay.fromAuthorName" @tap="replyComment">
												<text class="video_comment_child_item_l_detail_name">{{itemReplay.fromAuthorName}}</text>
												<div class="video_comment_child_item_l_detail_con_wrap">
													<text class="video_comment_child_item_l_detail_con">{{Ls('回复')}}</text>
													<text class="video_comment_child_item_l_detail_con_replay_name">{{itemReplay.toAuthorName}}:{{itemReplay.content}}</text>
												</div>
												<view class="video_comment_item_wrap_item_l_com_detail_other">
													<text class="video_comment_item_wrap_item_l_com_detail_other_time">{{itemReplay.createTime}}</text>
													<text v-if="itemReplay.isSelf == 1" class="video_comment_item_wrap_item_l_com_detail_other_del_com"
														data-type="reply" :data-commentId="item.commentId" :data-replyId="itemReplay.replyId" @tap.stop="delCom">{{Ls('删除')}}</text>
												</view>
											</view>
										</view>
										<view class="video_comment_item_wrap_item_r">
											<image class="video_comment_item_wrap_item_r_image" :data-commentId="item.commentId"
												:data-replyId="itemReplay.replyId" @tap="likeReply"
												:src="imgUrl+(itemReplay.isLike?'svideo/dz_complete.png':'svideo/dz.png')">
											</image>
											<text class="video_comment_item_wrap_item_r_text">{{itemReplay.likeNum}}</text>
										</view>
									</view>
								</view>
								<!-- 查看更多回复 -->
								<view v-if="items.replyList.length>3&&items.reply_has_more == 1" class="reply_pagnation"
									@tap="getMoreReply" :data-commentId="items.commentId" :data-rpn="items.rpn">
									<view class="reply_pagnation_left_line"></view>
									<text class="reply_pagnation_more_reply">{{Ls('查看更多回复')}}</text>
									<view class="reply_pagnation_right_line"></view>
								</view>
								<!-- 收起更多回复 -->
								<view v-if="items.replyList.length>4&&items.reply_has_more == 0" class="reply_pagnation"
									@tap="closeMoreReply" :data-commentId="items.commentId">
									<view class="reply_pagnation_left_line"></view>
									<text class="reply_pagnation_more_reply">{{Ls('收起更多回复')}}</text>
									<view class="reply_pagnation_right_line"></view>
								</view>
							</view>

							<!-- 数据加载完毕 -->
							<!-- <dataLoaded :showFlag="!hasmore&&commentList.length>0" /> -->
							<text v-if="!hasmore&&item.commentList.length>0" class="no-has-more" style="background-color: transparent">
								{{Ls('数据加载完毕~')}}
							</text>
							<!-- 数据加载中 -->
							
							<dataLoading :showFlag="hasmore&&loading" />

							<!-- 页面loading -->
							<view v-if="firstLoading" class="page_loading_child">
								<image class="page_loading_child_image" :src="imgUrl + 'live/page_loading_icon.gif'"></image>
							</view>
						</scroll-view>

						<view v-if="!firstLoading && !item.commentList.length" class="video_comment_empty">
							<view class="empty_data">
								<image class="empty_data_image" :src="imgUrl+'live/live_list_empty_icon.png'" mode="aspectFit"></image>
								<text class="empty_data_text">{{Ls('暂无数据')}}</text>
							</view>
						</view>

						<view class="replay">
							<view class="replay_input_wrap">
								<view v-if="replyInfo!=''" class="replay_reply_tip">
									<text class="replay_reply_text">{{Ls('回复')}}@</text>
									<text class="replay_reply_name">{{replyInfo.memberNickname?replyInfo.memberNickname:replyInfo.memberName}}</text>
								</view>
								<image v-if="replyInfo==''" class="replay_input_wrap_image" :src="imgUrl+'svideo/edit.png'"></image>
								<input type="text" cursor-spacing="10" :focus="showFocus" name="reply_con"
									class="replay_input_wrap_reply_inp" :placeholder="Ls('赶快发表你的评论吧~')"
									placeholder-style="replay_input_wrap_reply_inp_placeholder" confirm-type="send"
									@confirm="sendReplyComment" maxlength="100" v-model="input_val"></input>
							</view>
						</view>
					</view>
					<!-- 评论模块 end -->
				</view>
			</cell>
		</list>
	</view>
</template>

<script>
	import { checkPageHasMore } from "@/utils/live";
	import request from "@/utils/request";
	import pageLoading from "../../component/pageLoading.vue";
	import dataLoading from "../../component/dataLoading.vue";
	import dataLoaded from "../../component/dataLoaded.vue";
	import { weiXinAppShare } from '@/utils/base.js';
	import {getCurLanguage} from '@/utils/base.js'
	import Config from '@/utils/config.js'
	const imgUrl = process.env.VUE_APP_imgUrl;
	let cur_time = 0; //记录发送评论的时间，由于微信小程序本身input的发送时间在swiper里面会触发两次，所以时间间隔规避一下这个问题
	import { mapState, mapMutations } from 'vuex';
	let swiperTimeout = null;
	export default {
		components: {
			pageLoading,
			dataLoading,
			dataLoaded,
		},
		data() {
			return {
				loadReady: false,
				loading: false, //数据加载状态
				imgUrl: process.env.VUE_APP_imgUrl, //图片地址
				bgStyle: 'background-size:contain;background-position:center center;background-repeat: no-repeat;', //背景图片样式
				
				videoDetail: '', //短视频详情
				member_avatar: '', //默认作者头像
				authorDetail: '',
				goodsList: [], //短视频绑定的商品
				commentList: [], // 评论列表
				replyInfo: '', //回复评论的数据
				
				showComment: false, //是否显示评论
				pn: 1, //当前页
				pageSize: 10, //每页数据
				hasmore: true, //是否还有评论数据
				input_val: '', //输入框评论内容
				showFocus: false, //评论回复输入框默认失去焦点
				firstLoading: true, //是否初次加载，是的话展示页面居中的loading效果，
				
				key: '', //登录的key值
				shareWrap: false,
				stat_end: 5, //终端类型
				
				video_id: '',
				label_id: '',
				author_id: '',
				
				wHeight: 0,
				boxStyle: {
					width: '750rpx',
					height: '750rpx'
				},
				
				videoList: [], //视频列表
				curVideo: 1, //当前短视频列表页洗下标
				videoMore: true, //是否有更多视频数据
				curVideoIndex: 0, //当前视频下标
				appearOld: 0, //切换时当前视频下标
				appearNew: 0, //切换后视频下标
				videoTouchEnd: false,
				isShow: false, //onshow事件开启监听
				loginState: false, //登录状态
				listMoveY: 0, //list滑动高度
				
				Ls:getCurLanguage //多语言变量在nvue里单独处理
			};
		},
		components: {},
		computed: {
			...mapState(['hasLogin', 'cartData', 'userInfo'])
		},
		beforeMount() {
			//从缓存获取
			this.video_id = uni.getStorageSync('svideo_id');
			this.author_id = uni.getStorageSync('sauthor_id');
			this.label_id = uni.getStorageSync('slabel_id');
		},
		mounted() {
			this.loginState = this.hasLogin;
			this.getPlatform();
		
			uni.$on('nvueShow', (data) => {
				/* if(!this.isShow){
					this.isShow = true;
				}else{ */
					if(data){
						this.changeVideoState('play')
					}else{
						this.changeVideoState('pause')
					}
				// }
				// if(!this.loginState && this.loginState!=this.hasLogin && data){
				// 	this.loginState = this.hasLogin;
				// 	this.getVideoInfo(this.videoList[this.curVideoIndex].video_id,this.curVideoIndex);
				// }
			})
		
			this.videoList.push({
				video_id: this.video_id,
				author_id: this.author_id,
				videoDetail: {},
				authorDetail: {},
				goodsList: [],
				commentList: [],
				replyInfo: '',
				showComment: false,
				videoPlay: true, //视频是否播放中--暂停按钮不显示
			})
			this.getVideoInfo(); //获取第一个视频的详情
			this.getVideoList(); //获取第一组视频列表数据
      uni.$on('svideoPlay', (e)=>{
        let index = uni.getStorageSync('bindpause_index')
        this.changeVideoState('play')
      	this.videoList[index].videoPlay = true;
      })
		},
		moved() {},
		destroyed() {
			uni.removeStorageSync('svideo_id');
			uni.removeStorageSync('slabel_id');
			uni.removeStorageSync('author_id');
      uni.removeStorageSync('bindpause_index');
      this.$off('svideoPlay')
		},
		watch: {
			listMoveY(nv,ov) {
				if(this.stat_end == 4) {
					if (this.listMoveY%this.wHeight == 0) {
						if (nv>ov) { //下滑
							this.appearOld = Math.abs(this.listMoveY/this.wHeight)+1
						} else { //上滑
							this.appearOld = Math.abs(this.listMoveY/this.wHeight)-1
						}
						if(this.appearOld == this.appearNew) {
							return;
						}
						// console.log('暂停' + this.appearOld + ', 播放' + this.appearNew)
						this.curVideoIndex = this.appearNew
						if(swiperTimeout){ //定时器防止抖动
							clearTimeout(swiperTimeout)
							swiperTimeout = null;
						}
						let swiperTimeout = setTimeout(()=>{
							this.getVideoInfo(this.videoList[this.curVideoIndex].video_id,this.curVideoIndex);
							this.changeVideoState();
						}, 200)
					}
				}
			},
		},
		methods: {
			//获取当前终端
			getPlatform() {
				let info = uni.getSystemInfoSync();
				if (uni.getSystemInfoSync().platform == 'ios') {
					this.stat_end = 4;
					this.wHeight = info.safeArea.bottom; //获取屏幕高度
					this.boxStyle.height = info.safeArea.bottom+'px'; //获取屏幕高度
				} else if (uni.getSystemInfoSync().platform == 'android') {
					this.stat_end = 5;
					this.wHeight = info.windowHeight;
					this.boxStyle.height = info.windowHeight+'px';
				}
			},
      touch(e){
        e.stopPropagation()
      },
			
			//滑动屏幕时关闭评论弹窗
			touchMove(e){
				if(this.stat_end == 4){
					this.listMoveY = e.contentOffset.y;
				}
				if(this.videoList[this.curVideoIndex].showComment){
					this.closeComment();
				}
			},
			//开始滑动屏幕
			appear(e) {
				this.appearNew = e.currentTarget.attr.dataIndex
			},
			//结束滑动屏幕
			disappear(e) {
				if(this.stat_end == 4) { return; } //ios端不触发该方法，修改为watch监听处理
				this.appearOld = e.currentTarget.attr.dataIndex
				if (this.appearOld == this.appearNew) {
					// console.log('未切换')
				} else {
					if(Math.abs(this.appearOld-this.appearNew)>1){ //拖动屏幕距离过大但未松手时也会触发这里，做限制
						return;
					}
					// console.log('暂停' + this.appearOld + ', 播放' + this.appearNew)
					this.curVideoIndex = this.appearNew
					if(swiperTimeout){ //定时器防止抖动
						clearTimeout(swiperTimeout)
						swiperTimeout = null;
					}
					let swiperTimeout = setTimeout(()=>{
						this.getVideoInfo(this.videoList[this.curVideoIndex].video_id,this.curVideoIndex);
						this.changeVideoState();
					}, 200)
				}
			},
			//视频播放暂停事件
			changeVideoState(type) {
				let curPath = this.videoList[this.curVideoIndex].videoDetail.videoPath //当前播放视频的路径
				this.$refs.sldVideo_child.forEach((item,index)=>{
					if(type == 'play'){ //当前播放
						this.$refs.sldVideo_child[index].play();
					}else if(type == 'pause'){ //当前暂停
						this.$refs.sldVideo_child[index].pause();
					}else{ //滑动切换的播放暂停
						if(item.attr.src == curPath){
							this.$refs.sldVideo_child[index].play();
						}else{
							this.$refs.sldVideo_child[index].pause();
						}
					}
				})
			},
			
			// 获取短视频列表数据
			getVideoList(videoId,labelId) {
				let param = {}
				param.url = 'v3/video/front/video/videoList'
				param.method = 'GET'
				param.data = {}
				param.data.videoId = this.video_id
				param.data.labelId = this.label_id
				param.data.current = this.curVideo
				param.data.pageSize = 2
				request(param).then(res => {
					if (res.state == 200) {
						if(res.data.list.length > 0){
							let arr = [];
							res.data.list.forEach(item=>{
								arr.push({
									video_id: item.videoId,
									author_id: item.authorId,
									videoDetail: {},
									authorDetail: {},
									goodsList: [],
									commentList: [],
									replyInfo: '',
									showComment: false,
									videoPlay: true,
								})
							})
							let len = this.videoList.length;
							this.videoList = this.videoList.concat(arr);
							res.data.list.forEach((item,index)=>{
								this.getVideoInfo(item.videoId,len+index)
							})
						}
						this.videoMore = checkPageHasMore(res.data.pagination);//是否还有数据
						if(this.videoMore){
							this.curVideo++;
						}
					}
				})
			},
			//获取更多短视频列表数据
			getMoreVideo() {
				if(this.videoMore){
					this.getVideoList();
				}
			},
			
			//获取短视频详情
			getVideoInfo(id,index) {
				let video_id = id ? id : this.videoList[this.curVideoIndex].video_id;
				let param = {}
				param.data = {}
				param.url = 'v3/video/front/video/playingPage'
				param.method = 'GET'
				param.data.videoId = video_id
				request(param).then(res => {
					if (res.state == 200) {
						// this.goodsList = res.data.goodsList
						// this.videoDetail = res.data.videoInfo
						// this.member_avatar = res.data.memberAvatar
						// this.authorDetail = res.data.authorInfo
						
						if(index){
							this.videoList[index].goodsList = res.data.goodsList
							this.videoList[index].videoDetail = res.data.videoInfo
							this.videoList[index].member_avatar = res.data.memberAvatar
							this.videoList[index].authorDetail = res.data.authorInfo
						}else{
							this.loadReady = true;
							this.videoList[0].goodsList = res.data.goodsList
							this.videoList[0].videoDetail = res.data.videoInfo
							this.videoList[0].member_avatar = res.data.memberAvatar
							this.videoList[0].authorDetail = res.data.authorInfo
							
							setTimeout(()=>{
								this.$refs.sldVideo_child[0].play();
							}, 200)
						}
					}
				})
			},
			
			//视频播放/暂停
			chansgedd() {
				if (this.videoList[this.curVideoIndex].videoPlay) {
					this.videoList[this.curVideoIndex].videoPlay = false;
					this.changeVideoState('pause');
				} else {
					this.videoList[this.curVideoIndex].videoPlay = true;
					this.changeVideoState('play');
				}
			},
			bindplay(e) {
				let index = e.target.dataset.index;
				this.videoList[index].videoPlay = true;
			},
			bindpause(e) {
				let index = e.target.dataset.index;
        uni.setStorageSync('bindpause_index',e.target.dataset.index)
				this.videoList[index].videoPlay = false;
			},
			
			//更新视频点击量
			updateVideoClick() {
				let param = {}
				param.data = {}
				param.url = 'v3/video/front/video/updateClickNum'
				param.method = 'POST'
				param.data.videoId = this.video_id
				request(param).then(res => {
					if (res.state == 200) {}
				})
			},
				
			//进入作者页面
			goLiveUserCenter(e) {
				let author_id = this.videoList[this.curVideoIndex].author_id;
				let page = getCurrentPages();
				let len = page.length;
				uni.$emit('goLiveUserCenter', {
					pageLength: len,
					url: '/extra/svideo/myVideo?author_id=' + author_id
				});
			},
			
			//进入直播个人中心
			gPensonalCenter(e) {
				var curTab = e.currentTarget.dataset.curtab;
				if (!this.hasLogin) {
					getApp().globalData.goLogin(this.$Route);
				} else {
					uni.navigateTo({
						url: '/extra/svideo/myVideo'
					});
				}
			},
	
			//跳转商品详情页
			goGoodsDetail(good) {
				let page = getCurrentPages();
				let len = page.length;
				if (len > 4) {
					uni.redirectTo({
						url: '/standard/product/detail?productId=' + good.defaultProductId + '&goodsId=' + good
							.goodsId + '&videoId=' + this.video_id
					});
				} else {
					uni.navigateTo({
						url: '/standard/product/detail?productId=' + good.defaultProductId + '&goodsId=' + good
							.goodsId + '&videoId=' + this.video_id
					});
				}
			},
			
			//获取评论列表
			getCommentList() {
				let item = this.videoList[this.curVideoIndex];
				let param = {}
				param.data = {}
				param.url = 'v3/video/front/video/comment/list'
				param.method = 'GET'
				param.data.current = this.pn
				param.data.pageSize = this.pageSize
				param.data.videoId = item.video_id
				request(param).then(res => {
					if (res.state == 200) {
						let list = res.data.list;
						list.forEach(el => el.rpn = 1);

						if (this.pn == 1) {
							item.commentList = list;
						} else {
							item.commentList = item.commentList.concat(list);
						}

						if (checkPageHasMore(res.data.pagination)) {
							this.pn++;
						} else {
							this.hasmore = false;
						} //初次加载的话更改状

						if (this.firstLoading) {
							this.firstLoading = false;
						}
						this.loading = false
					}
				})
			},
			//评论列表滑到底部加载数据
			getMoreCom() {
				if (this.hasmore) {
					this.getCommentList();
				}
			},
			
			// 返回上级页面
			goBack() {
				uni.$emit('goBack');
			},
			
			//关注、取消关注事件
			collect(e) {
				let item = this.videoList[this.curVideoIndex];
				if (this.hasLogin) {
					let param = {}
					param.data = {}
					param.method = 'POST'
					param.data.authorId = item.author_id
					if (item.authorDetail.isFollow) {
						param.url = 'v3/video/front/video/cancelFollow'
					} else {
						param.url = 'v3/video/front/video/followAuthor'
					}
					request(param).then(res => {
						if (res.state == 200) {
							item.isFollow =  !(item.isFollow);
							this.getVideoInfo(this.videoList[this.curVideoIndex].video_id,this.curVideoIndex)
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							});
						}
					})
				} else {
					getApp().globalData.goLogin();
				}
			},

			//加入购物车事件
			addCart(good) {
				let videoDetail = this.videoList[this.curVideoIndex].videoDetail
				if (videoDetail.state == 1) {
					uni.showToast({
						title: this.Ls('该作品正在审核中'),
						icon: 'none'
					})
					return;
				} else if (videoDetail.state == 3) {
					uni.showToast({
						title: this.Ls('该作品审核不通过'),
						icon: 'none'
					})
					return;
				}
				if (!this.hasLogin) {
					let cart_list = {
						storeCartGroupList: [{
							promotionCartGroupList: [{
								cartList: [{
									buyNum: 1,
									goodsId: good.goodsId - 0,
									productId: good.defaultProductId,
									productImage: good.goodsImage,
									goodsName: good.goodsName,
									isChecked: 1,
									productPrice: good.goodsPrice,
									productStock: good.goodsStock
								}],
							}],
							storeId: good.storeId,
							storeName: good.storeName,
							checkedAll: true
						}],
						checkedAll: true,
						invalidList: []
					}
					//未登录加入本地缓存
					let local_cart_list = uni.getStorageSync('cart_list') //购物车列表本地缓存
					if (local_cart_list) { //如果不是第一次存储
						let tmp_list1 = []
						let tmp_list2 = []
						cart_list.storeCartGroupList.forEach(item => {
							item.promotionCartGroupList.forEach(item1 => {
								item1.cartList.forEach(item2 => {
									local_cart_list.storeCartGroupList.forEach(v => {
										v.promotionCartGroupList.forEach(v1 => {
											v1.cartList.forEach(v2 => {
												if (v2.productId == item2
													.productId && v
													.storeId == item
													.storeId) {
													tmp_list1.push(v)
												}
											})
										})
									})
									tmp_list2 = local_cart_list.storeCartGroupList.filter(v => {
										return v.storeId == item.storeId
									})
								})
							})
						})
						if (tmp_list1.length > 0 && tmp_list2.length > 0) { //同一店铺同一商品
							local_cart_list.storeCartGroupList.map(item => {
								item.promotionCartGroupList.map(item1 => {
									item1.cartList.map(item2 => {
										if (item2.productId == this.productId && item.storeId ==
											this.storeInf.storeId) {
											item2.buyNum += this.currentSpecNum
										}
									})
								})
							})
						} else if (tmp_list1.length == 0 && tmp_list2.length > 0) { //同一店铺不同商品
							local_cart_list.storeCartGroupList.map(item => {
								if (item.storeId == this.storeInf.storeId) {
									item.promotionCartGroupList.map(item2 => {
										item2.cartList.push(cart_list.storeCartGroupList[0]
											.promotionCartGroupList[0].cartList[0])
									})
								}
							})
						} else { //不同店铺不同商品
							local_cart_list.storeCartGroupList.push(cart_list.storeCartGroupList[0])
						}
						// 未登录购物车展示数据
						uni.setStorage({
							key: 'cart_list',
							data: local_cart_list,
							success: function() {
								//更新购物车数量和购物车数据
							}
						});

					} else {
						uni.setStorage({
							key: 'cart_list',
							data: cart_list,
							success: function() {
								//更新购物车数量和购物车数据
							}
						});
					}
					uni.showToast({
						title: this.Ls('加入购物车成功！'),
						icon: 'none',
						duration: 700
					})
				} else { //已登录
					request({
						url: 'v3/business/front/cart/add',
						data: {
							productId: good.defaultProductId,
							number: 1,
						},
						method: 'POST'
					}).then(res => {
						if (res.state == 200) {
							//更新购物车数量
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					}).catch((e) => {})
				}
			},
			
			//点赞事件
			like() {
				let videoDetail = this.videoList[this.curVideoIndex].videoDetail;
				if (videoDetail.state != 2) {
					uni.showToast({
						title: this.Ls('该视频未审核通过,不能点赞哦～'),
						icon: 'none'
					})
					return
				}
				if (this.hasLogin) {
					let param = {}
					param.data = {}
					param.url = 'v3/video/front/video/clickPraise'
					param.method = 'POST'
					param.data.videoId = this.videoList[this.curVideoIndex].video_id
					request(param).then(res => {
						if (res.state == 200) {
							if (res.state == 200) {
								videoDetail.isLike = videoDetail.isLike == true ? false : true;
								if (videoDetail.isLike == true) {
									videoDetail.likeNum = Number(videoDetail.likeNum) + 1
								} else {
									videoDetail.likeNum = Number(videoDetail.likeNum) - 1
								}
								this.videoList[this.curVideoIndex].videoDetail = videoDetail
							} else {
								uni.showToast({
									title: res.msg,
									icon: 'none'
								});
							}
						}
					})
				} else {
					getApp().globalData.goLogin();
				}
			},
			
			//删除视频提示
			del_video() {
				uni.showModal({
					title: this.Ls('提示'),
					content: this.Ls(`确定删除该视频？`),
					confirmText: this.Ls('确定'),
					cancelText:this.Ls('取消'),
					success: res => {
						if (res.confirm) {
							this.delVideo();
						}
					}
				});
			},
			//删除视频
			delVideo() {
				let param = {}
				param.data = {}
				param.url = 'v3/video/front/video/delVideo'
				param.method = 'POST'
				param.data.videoId = this.videoList[this.curVideoIndex].video_id
				request(param).then(res => {
					uni.showToast({
						title: res.msg,
						icon: 'none'
					});
					if (res.state == 200) {
						//返回上一页
						uni.$emit('updateState')
						uni.$emit('goBack');
					}
				})
			},
		
			//分享点击事件
			showShare(e) {
        e.stopPropagation()
				this.shareWrap = true
			},
			//分享 type：分享类型 0 图文 2图片，scene 场景 WXSceneSession：分享朋友  WXSenceTimeline：分享朋友圈
			sldShare(type, scene,e) {
        e.stopPropagation()
				let video_id = this.videoList[this.curVideoIndex].video_id
				let videoDetail = this.videoList[this.curVideoIndex].videoDetail
				let authorDetail = this.videoList[this.curVideoIndex].authorDetail
				let shareData = {};
				if (type == 0) {
					shareData.href = process.env.VUE_APP_API_URL+'extra/svideo/svideoPlay?video_id=' + video_id;
					shareData.title = videoDetail.videoName;
					shareData.summary = this.Ls('我正在看') + authorDetail.memberNickname ? authorDetail.memberNickname : authorDetail.memberName + this.Ls('的精彩内容，快来围观~');
					shareData.imageUrl = videoDetail.videoImage;
				}
				weiXinAppShare(type, scene, shareData);
				this.closeShare_o(); //关闭分享
			},
			//微信分享
			weiXinAppShare(type, scene, shareData) {
				if (type == 0) {
					//分享图文
					uni.share({
						provider: "weixin",
						scene: scene,
						type: type, //0为图文
						href: shareData.href,
						title: shareData.title,
						summary: shareData.summary,
						imageUrl: shareData.imageUrl, //图片,图片过大的话不展示，建议小于20kb
						success: function(res) {},
						fail: function(err) {}
					});
				} else if (type == 2) {
					//分享图片
					uni.share({
						provider: "weixin",
						scene: scene,
						type: type, //2为图片
						imageUrl: shareData.imageUrl, //图片,图片过大的话不展示，建议小于20kb
						success: function(res) {},
						fail: function(err) {}
					});
				}
			},
			//关闭分享
			closeShare(e) {
         e.stopPropagation()  
				this.shareWrap = false;
			},
			closeShare_o(){
			  this.shareWrap = false;
			},
			//评论模块隐藏键盘
			moveHandle() {
				this.showFocus = false;
				if(this.videoList[this.curVideoIndex].replyInfo){
					this.videoList[this.curVideoIndex].replyInfo = '';
				}
				uni.hideKeyboard()
			},
			//评论模块删除事件
			delCom(e) {
				e.stopPropagation()
				let tmp_data = e.currentTarget.dataset;
				uni.showModal({
					title: '',
					content: this.Ls(`确认删除该评论？`),
					confirmText: this.Ls('确定'),
					cancelText:this.Ls('取消'),
					success: res => {
						if (res.confirm) {
							if (tmp_data.type == 'comment') {
								this.delComment(tmp_data.commentId);
							} else {
								this.delApply(tmp_data.replyId, tmp_data.commentId);
							}
						}
					}
				});
			},
			// 删除回复
			delApply(replyId, commentId) {
				let commentList = this.videoList[this.curVideoIndex].commentList;
				let param = {}
				param.data = {}
				param.url = 'v3/video/front/video/comment/delReply'
				param.method = 'POST'
				param.data.replyId = replyId
				request(param).then(res => {
					uni.showToast({
						title: res.msg,
						icon: 'none'
					});
					if (res.state == 200) {
						let index = commentList.findIndex(el => el.commentId == commentId);
						let r_index = commentList[index].replyList.findIndex(el => el.replyId == replyId);
						commentList[index].replyList.splice(r_index, 1);
						this.videoList[this.curVideoIndex].commentList = commentList
					}
				})
			},
			// 删除评论
			delComment(commentId) {
				let videoDetail = this.videoList[this.curVideoIndex].videoDetail;
				let commentList = this.videoList[this.curVideoIndex].commentList;
				let param = {}
				param.data = {}
				param.url = 'v3/video/front/video/comment/delete'
				param.method = 'POST'
				param.data.commentId = commentId
				request(param).then(res => {
					uni.showToast({
						title: res.msg,
						icon: 'none'
					});
					if (res.state == 200) {
						videoDetail.commentNum = Number(videoDetail.commentNum) - 1;
						let index = commentList.findIndex(el => el.commentId == commentId);
						commentList.splice(index, 1);
						this.videoList[this.curVideoIndex].videoDetail = videoDetail
						this.videoList[this.curVideoIndex].commentList = commentList
					}
				})
			},
			// 点赞评论
			likeComment(e) {
				let commentId = e.currentTarget.dataset.commentId;
				let commentList = this.videoList[this.curVideoIndex].commentList;
				let param = {}
				param.data = {}
				param.url = 'v3/video/front/video/comment/clickPraise'
				param.method = 'POST'
				param.data.commentId = commentId
				if (this.hasLogin) {
					request(param).then(res => {
						if (res.state == 200) {
							let index = commentList.findIndex(el => el.commentId == commentId);
							let isLike = commentList[index].isLike;
							commentList[index].isLike = isLike == true ? false : true;
							commentList[index].likeNum = res.data;
							this.videoList[this.curVideoIndex].commentList = commentList
						} else {
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							});
						}
					})
				} else {
					getApp().globalData.goLogin();
				}
			},
			// 点赞回复
			likeReply(e) {
				let commentId = e.currentTarget.dataset.commentId;
				let replyId = e.currentTarget.dataset.replyId;
				let commentList = this.videoList[this.curVideoIndex].commentList;
				let param = {}
				param.data = {}
				param.url = 'v3/video/front/video/comment/replyClickPraise'
				param.method = 'POST'
				param.data.replyId = replyId
				if (this.hasLogin) {
					request(param).then(res => {
						if (res.state == 200) {
							let index = commentList.findIndex(el => el.commentId == commentId);
							let r_index = commentList[index].replyList.findIndex(el => el.replyId == replyId);
							let isLike = commentList[index].replyList[r_index].isLike;
							commentList[index].replyList[r_index].isLike = isLike == true ? false : true;
							commentList[index].replyList[r_index].likeNum = res.data;
							this.videoList[this.curVideoIndex].commentList = commentList
						} else {
							uni.showToast({
								title: res.data.msg,
								icon: 'none'
							});
						}
					})
				} else {
					getApp().globalData.goLogin();
				}
			},
			//回复评论
			replyComment(e) {
				let tmp = e.currentTarget.dataset;
				if (this.hasLogin) {
					this.replyInfo = {
						commentId: tmp.commentId,
						replyId: tmp.replyId,
						memberNickname: tmp.memberNickname
					},
					this.showFocus = true
				} else {
					uni.showToast({
						title: this.Ls('需要登录才能回复哦'),
						icon: 'none'
					});
				}
			},
			//评论功能
			sendReplyComment(e) {
				let _this = this
				if (!e.detail.value) {
					return
				}
				if (Date.parse(new Date()) - cur_time < 10) {
					return false;
				} else {
					cur_time = Date.parse(new Date());
				}
				if (!this.hasLogin) {
					getApp().globalData.goLogin(this.$Route);
					return;
				}

				let replyInfo = this.replyInfo;
				let video_id = this.videoList[this.curVideoIndex].video_id;
				let videoDetail = this.videoList[this.curVideoIndex].videoDetail;
				let commentList = this.videoList[this.curVideoIndex].commentList;
					
				if (replyInfo == '') {
					//发布评论
					let param = {}
					param.data = {}
					param.url = 'v3/video/front/video/comment/publish'
					param.method = 'POST'
					param.data.content = e.detail.value
					param.data.videoId = video_id
					request(param).then(res => {
						if (res.state == 200) {
							videoDetail.commentNum = Number(videoDetail.commentNum) + 1;
							res.data.replyList = []
							commentList.unshift(res.data);
							// _this.getCommentList()
							//清空输入内容
							setTimeout(() => {
								_this.input_val = ''
								_this.showFocus = false
								_this.videoList[this.curVideoIndex].videoDetail = videoDetail
								_this.videoList[this.curVideoIndex].commentList = JSON.parse(JSON.stringify(commentList))
							}, 0)

						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							});
						}
					})
				} else {
					//回复评论
					let param = {}
					param.data = {}
					param.url = 'v3/video/front/video/comment/reply'
					param.method = 'POST'
					param.data.content = e.detail.value
					param.data.commentId = replyInfo.commentId
					param.data.parentReplyId = replyInfo.replyId ? replyInfo.replyId : 0
					request(param).then(res => {
						if (res.state == 200) {
							let index = commentList.findIndex(el => el.commentId == replyInfo.commentId);
							commentList[index].replyList.push(res.data); //清空输入内容
							setTimeout(() => {
								_this.input_val = ''
								this.replyInfo = ''
								_this.showFocus = false
								_this.videoList[this.curVideoIndex].commentList = commentList
								_this.videoList[this.curVideoIndex].replyInfo = ''
							}, 0)
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							});
						}
					})
				}
			},
			//关闭评论
			closeComment() {
				this.pn = 1;
				this.hasmore = true;
				this.firstLoading = true;
				this.input_val = '';
				this.videoList[this.curVideoIndex].commentList = [];
				this.videoList[this.curVideoIndex].showComment = false;
			},
			//打开评论
			openCommnet() {
				if (this.videoList[this.curVideoIndex].videoDetail.state != 2) {
					uni.showToast({
						title:this.Ls('该视频未审核通过,不能评论哦～') ,
						icon: 'none'
					})
					return
				}
				this.videoList[this.curVideoIndex].showComment = true;
				this.getCommentList();
			},
			// 查看更多回复
			getMoreReply(e) {
				let tmp = e.currentTarget.dataset;
				let commentList = this.videoList[this.curVideoIndex].commentList
				uni.request({
					url: process.env.VUE_APP_svideo_url + '/index.php?app=reply&mod=index&comment_id=' + tmp.commentid
						+ '&pageSize=' + this.pageSize + '&current=' + tmp.rpn,
					success: res => {
						if (res.data.state == 200) {
							let index = commentList.findIndex(el => el.comment_id === tmp.commentid);
							let list = res.data.data.reply;

							if (tmp.rpn == 1) {
								commentList[index].reply = list;
							} else {
								commentList[index].reply = commentList[index].reply.concat(list);
							}

							let ishasmore = checkPageHasMore(res.data.data.pagination);

							if (!ishasmore) {
								commentList[index].reply_has_more = 0;
							} else {
								commentList[index].rpn = ++tmp.rpn;
							}

							this.videoList[this.curVideoIndex].commentList = commentList
						}
					}
				});
			},
			// 收起更多回复
			closeMoreReply(e) {
				let comment_id = e.currentTarget.dataset.commentid;
				let commentList = this.videoList[this.curVideoIndex].commentList
				let index = commentList.findIndex(el => el.comment_id === comment_id);
				commentList[index].reply = commentList[index].reply.slice(0, 4);
				commentList[index].reply_has_more = 1;
				commentList[index].rpn = 1;
				this.videoList[this.curVideoIndex].commentList = commentList
			},
			
			//视频暂停播放，flag为true播放，否则暂停
			videoCon(flag) {
				let {
					video_id
				} = this;
				let str = "sldVideo_child" + video_id;
				let videoContext = uni.createVideoContext(str);
				videoContext.pause(); //暂停播放
			},
		}
	};
</script>
<style>
	page {
		background: transparent;
	}

	.wrap {
		position: relative;
		background-color: #555555;
		width: 750rpx;
		overflow: hidden;
	}
	
	.live_pause {
		position: absolute;
		/* top: 214rpx;
		left: 0;
		right: 130rpx;
		bottom: 318rpx; */
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 1;
		overflow: hidden;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.live_pause_img {
		/* position: relative;
		left: 65rpx; */
		width: 100rpx;
		height: 115rpx;
	}

	.live_header {
		position: absolute;
		top: 120rpx;
		left: 20rpx;
		z-index: 99;
		height: 80rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: flex-start;
	}

	.live_header_go_back {
		width: 45rpx;
		height: 47rpx;
	}

	.live_header_avator {
		width: 80rpx;
		height: 80rpx;
		border-radius: 40rpx;
		border-width: 2rpx;
		border-style: solid;
		border-color: #fff;
		margin-left: 12rpx;
		overflow: hidden;
	}

	.live_header_avator_img {
		width: 76rpx;
		height: 76rpx;
		border-radius: 38rpx;
		overflow: hidden;
	}

	.live_header_mem_info {
		margin-left: 20rpx;
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		justify-content: space-between;
	}

	.live_header_live_fllow {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		width: 120rpx;
		height: 50rpx;
		background-color:#fc1c1c;
		border-radius: 25rpx;
		margin-left: 25rpx;
	}

	.live_header_live_fllow_image {
		width: 46rpx;
		height: 46rpx;
	}

	.live_header_live_fllow_text {
		color: #fff;
		font-size: 24rpx;
		margin-right: 8rpx;
	}

	.live_header_mem_info_name {
		max-width: 200rpx;
		color: #fff;
		font-size: 28rpx;
		line-height: 32rpx;
		margin-bottom: 15rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		lines: 1;
		font-weight: 600;
		display: -webkit-box;
	}

	.live_header_mem_info_stat_num {
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
	}

	.live_header_mem_info_stat_num_click_num {
		color: #fff;
		line-height: 36rpx;
		font-size: 22rpx;
		font-weight: 600;
		white-space: nowrap;
		lines: 1;
		margin-right: 20rpx;
	}

	.live_back {
		width: 752rpx;
		margin-left: -2rpx;
	}

	.select-wrap {
		position: fixed;
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
		z-index: 9999;
		/* margin: 0 auto; */
		width: 750rpx;
		background-color: rgba(0, 0, 0, 0.45);
	}

	.select-wrap_share-mode {
		position: absolute;
		bottom: 100rpx;
		left: 0;
		right: 0;
		width: 750rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.select-wrap_share-mode_share-img {
		width: 72vw;
		border-radius: 20rpx;
		overflow: hidden;
	}

	.select-wrap_share-mode_share-img_image {
		width: 100%;
		height: 0;
		border-radius: 20rpx;
	}

	.select-wrap_share-mode_ul {
		display: flex;
		flex-direction: row;
		align-items: center;
		width: 750rpx;
		padding-left: 30px;
		padding-right: 30px;
		justify-content: space-around;
	}

	.share-mode_item {
		display: flex;
		flex-direction: column;
		align-items: center;
		text-align: center;
		cursor: pointer;
		border: none;
		margin: 0;
		padding: 0;
		line-height: 1;
		background-color: transparent;
		border-width: 0;
	}

	.share-mode_item_image {
		width: 106rpx;
		height: 0;
	}

	.share-mode_item_text {
		color: #fff;
		font-size: 24rpx;
		margin-top: 30rpx;
	}

	.select-wrap_close {
		width: 750rpx;
		height: 120rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
	}

	.select-wrap_close_image {
		width: 18rpx;
		height: 18rpx;
		padding: 20rpx;
	}

	.select-wrap_share-mode_share-img {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.right_control {
		position: absolute;
		bottom: 310rpx;
		right: 30rpx;
		z-index: 99;
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: center;
	}

	.right_control_image {
		width: 68rpx;
		height: 68rpx;
		margin-bottom: 13rpx;
	}

	.right_control_text {
		width: 68rpx;
		text-align: center;
		color: #fff;
		font-size: 24rpx;
		margin-bottom: 36rpx;
		white-space: nowrap;
		lines: 1;
	}

	.right_control_share_btn {
		background-color: transparent;
		border: none;
		color: transparent;
		line-height: 0;
		border-color: rgba(0, 0, 0, 0);
	}

	.right_control_share_btn::after {
		border: none !important;
	}

	/* 进入个人主页按钮入口 */
	.personal_homepage {
		width: 66rpx;
		height: 66rpx;
		border-radius: 50%;
		border-width: 2rpx;
		border-style: solid;
		border-color: #fff;
		z-index: 99;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #ffffff;
	}

	.personal_homepage_personal_homepage_image {
		width: 66rpx;
		height: 66rpx;
		border-radius: 50%;
		margin-bottom: 0;
	}

	.video_footer {
		position: absolute;
		left: 30rpx;
		right: 0;
		bottom: 37rpx;
		z-index: 2;
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		justify-content: flex-end;
	}

	.video_footer_title {
		color: #fff;
		font-size: 32rpx;
		font-weight: 600;
		margin-bottom: 10rpx;
	}

	.video_footer_desc {
		color: #fff;
		font-size: 26rpx;
		line-height: 40rpx;
		margin-bottom: 18rpx;
		width: 570rpx;
	}

	.video_footer_video_goods {
		width: 750rpx;
		height: 172rpx;
		white-space: nowrap;
		lines: 1;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
	}

	.video_footer_goods_item {
		width: 410rpx;
		height: 172rpx;
		display: inline-block;
		border-radius: 15rpx;
		flex-direction: row;
		justify-content: flex-start;
		align-items: flex-start;
		margin-right: 30rpx;
		clear: both;
		overflow: hidden;
	}

	.video_footer_goods_item_goods_img {
		width: 172rpx;
		height: 172rpx;
		background-color: #fff;
		border-top-left-radius: 15rpx;
		border-top-right-radius: 0;
		border-bottom-right-radius: 0;
		border-bottom-left-radius: 15rpx;
		float: left;
	}

	.video_footer_goods_item_goods_img_img {
		width: 172rpx;
		height: 172rpx;
		background-color: #fff;
		border-top-left-radius: 15rpx;
		border-top-right-radius: 0;
		border-bottom-right-radius: 0;
		border-bottom-left-radius: 15rpx;
		float: left;
	}

	.video_footer_goods_item_goods_detail {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: flex-start;
		flex: 1;
		background-color: #222;
		height: 142rpx;
		border-top-left-radius: 0;
		border-top-right-radius: 15rpx;
		border-bottom-right-radius: 15rpx;
		border-bottom-left-radius: 0;
		padding: 15rpx 20rpx;
		height: 172rpx;
	}

	.video_footer_goods_item_goods_name {
		width: 200rpx;
		color: #fff;
		font-size: 24rpx;
		line-height: 36rpx;
		height: 72rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		word-break: break-word;
		white-space: normal;
	}

	.video_footer_goods_item_goods_info {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		width: 200rpx;
	}

	.video_footer_goods_item_goods_price {
		color: #fff;
		font-size: 28rpx;
		letter-spacing: 2rpx;
	}

	.video_footer_goods_item_add_cart {
		width: 48rpx;
		height: 48rpx;
	}

	.video_comment {
		position: fixed;
		z-index: 100;
		left: 0;
		right: 0;
		bottom: 0;
		width: 750rpx;
		/* height: 90vw; */
		background-color: #fff;
		border-top-left-radius: 15rpx;
		border-top-right-radius: 15rpx;
		border-bottom-right-radius: 0;
		border-bottom-left-radius: 0;
	}

	.video_comment_title {
		display: flex;
		padding: 30rpx;
		flex-direction: row;
		justify-content: space-between;
		align-content: center;
		height: 100rpx;
		border-bottom-width: 1rpx;
		border-bottom-color: rgba(0, 0, 0, 0.1);
		border-bottom-style: solid;
	}

	.video_comment_title_com_t_l {
		display: flex;
		flex: 1;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
	}

	.video_comment_title_com_t_l_com_t_l_title {
		color: #2d2d2d;
		font-size: 32rpx;
		margin-right: 18rpx;
	}

	.video_comment_title_com_t_l_com_t_l_total {
		color: #949494;
		font-size: 22rpx;
	}

	.video_comment_title_com_t_close {
		width: 48rpx;
		height: 48rpx;
	}

	.video_comment_item_wrap {
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;
	}

	.video_comment_comment {
		width: 750rpx;
		height: 530rpx;
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;
		margin-top: 20rpx;
		/* position: relative; */
		padding-bottom: 100rpx;
	}

	.video_comment_item_wrap_item,
	.video_comment_child_item {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: flex-start;
		width: 690rpx;
		margin-left: 30rpx;
	}

	.video_comment_item_wrap_item_l,
	.video_comment_child_item_l {
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: flex-start;
		width: 600rpx;
	}

	.video_comment_item_wrap_item_l_com_avator {
		width: 43rpx;
		height: 43rpx;
		border-radius: 50%;
		margin-right: 18rpx;
		margin-top: 10rpx;
	}

	.video_comment_item_wrap_item_l_com_avator_img {
		width: 43rpx;
		height: 43rpx;
		border-radius: 50%;
	}

	.video_comment_child_item_l_avator {
		width: 30rpx;
		height: 30rpx;
		border-radius: 50%;
		margin-right: 18rpx;
		margin-top: 10rpx;
	}

	.video_comment_child_item_l_avator_img {
		width: 30rpx;
		height: 30rpx;
		border-radius: 50%;
	}

	.video_comment_item_wrap_item_l_com_detail {
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;
		flex: 1;
	}

	.video_comment_item_wrap_item_l_com_detail_name {
		color: #2d2d2d;
		font-size: 28rpx;
		margin-bottom: 12rpx;
	}

	.video_comment_item_wrap_item_l_com_detail_con {
		color: #2d2d2d;
		font-size: 26rpx;
		line-height: 39rpx;
		margin-bottom: 7rpx;
	}

	.video_comment_item_wrap_item_l_com_detail_other {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: flex-start;
		margin-bottom: 12rpx;
	}

	.video_comment_item_wrap_item_l_com_detail_other_time {
		color: #949494;
		font-size: 22rpx;
		margin-right: 20rpx;
	}

	.video_comment_item_wrap_item_l_com_detail_other_del_com {
		color: #4d8efb;
		font-size: 20rpx;
	}

	.video_comment_item_wrap_item_r {
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: center;
	}

	.video_comment_item_wrap_item_r_image {
		width: 46rpx;
		height: 46rpx;
	}

	.video_comment_item_wrap_item_r_text {
		color: #949494;
		font-size: 20rpx;
		margin-top: -3rpx;
	}

	.video_comment_child {
		padding-left: 60rpx;
	}

	.video_comment_child_item_l_avator {
		width: 30rpx;
		height: 30rpx;
		margin-top: 0;
		margin-right: 10rpx;
	}

	.video_comment_child_item_l_detail_name {
		font-size: 26rpx;
		margin-bottom: 5rpx;
	}

	.video_comment_child_item_l_detail_con_wrap {
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: flex-start;
	}

	.video_comment_child_item_l_detail_con {
		font-size: 24rpx;
	}

	.video_comment_child_item_l_detail_con_replay_name {
		color: #949494;
		font-size: 24rpx;
	}

	.video_comment_child_item {
		width: 630rpx;
	}

	.video_comment_child_item_l {
		width: 550rpx;
	}
	
	.video_comment_empty {
		position: absolute;
		z-index: -1;
		left: 0;
		right: 0;
		bottom: 0;
		width: 750rpx;
		height: 750rpx;
	}
	
	.empty_data {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		background-color: transparent;
		width: 750rpx;
		margin-top: 350rpx;
	}
	
	.empty_data_image {
		width: 179rpx;
		height: 140rpx;
	}
	
	.empty_data_text {
		font-size: 28rpx;
		margin-top: 20rpx;
		color: #949494;
	}

	.reply_pagnation {
		width: 750rpx;
		display: flex;
		flex-direction: row;
		justify-content: space-around;
		align-items: center;
		height: 40rpx;
		padding-left: 110rpx;
		padding-right: 90rpx;
	}

	.reply_pagnation_left_line,
	.reply_pagnation_right_line {
		height: 1rpx;
		background-color: rgba(0, 0, 0, 0.1);
		width: 166rpx;
	}

	.reply_pagnation_more_reply {
		color: #2d2d2d;
		font-size: 22rpx;
	}

	.page_loading_child {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 750rpx;
		height: 750rpx;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		background-color: transparent;
	}

	.page_loading_child_image {
		width: 60rpx;
		height: 60rpx;
	}

	.no-has-more {
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		height: 80rpx;
		text-align: center;
		/* line-height: 80rpx; */
		color: #949494;
		font-size: 22rpx;
		margin: 0 auto;
		/* width: 100%; */
		width: 600rpx;
	}

	.no-has-more::after,
	.no-has-more::before {
		position: absolute;
		content: '';
		width: 166rpx;
		height: 1rpx;
		background-color: rgba(0, 0, 0, 0.1);
		top: 50%;
		transform: translateY(-50%) scaleY(0.5);
	}

	.no-has-more::after {
		right: 114rpx;
	}

	.no-has-more::before {
		left: 114rpx;
	}
	
	.replay {
		position: absolute;
		z-index: 4;
		height: 100rpx;
		width: 750rpx;
		padding: 0 30rpx;
		background-color: #fff;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		left: 0;
		right: 0;
		bottom: 0;
		box-shadow: 0px 0px 20px 0px rgba(86, 86, 86, 0.08);
	}

	.replay_input_wrap {
		background-color: #ddd;
		border-radius: 6px;
		width: 690rpx;
		height: 57rpx;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
	}

	.replay_input_wrap_reply_inp {
		display: flex;
		flex: 1;
		font-size: 24rpx;
		/* app-1-start */



		/* app-1-end */
	}

	.replay_input_wrap_reply_inp_placeholder {
		font-size: 24rpx;
		color: #949494;
	}

	.replay_input_wrap_image {
		width: 47rpx;
		height: 47rpx;
		margin: 0 10rpx;
	}

	.replay_reply_tip {
		color: #2d2d2d;
		font-size: 24rpx;
		padding: 0 15rpx;
		display: flex !important;
		flex-direction: row;
		lines: 1;
	}
	
	.replay_reply_text{
		font-size: 24rpx;
		color: #2d2d2d;
		margin-right: 10rpx;
		line-height: 26rpx;
	}

	.replay_reply_name {
		color: #4d8efb;
		font-size: 24rpx;
		max-width: 130rpx;
		text-overflow: ellipsis;
		line-height: 26rpx;
		lines: 1;
	}

</style>
