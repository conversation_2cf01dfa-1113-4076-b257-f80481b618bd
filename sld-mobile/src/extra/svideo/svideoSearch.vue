<template>
	<view class="container">
		<!-- #ifdef MP-WEIXIN -->
		<view :style="{height:statusBarHeight,width:'100%'}"></view>
		<!-- #endif -->
		
		<!-- 搜索框 -->
		<view class="custom-nav" :class="{'custom-nav-h5': true}" :style="{
			height: getNavHeight,
			top: getNavTop
		}">
			<view class="nav-content" :class="{'nav-content-h5': true}" :style="{
				paddingRight: getPaddingRight
			}">
				<view class="back-icon" @click="goBack">
					<uni-icons type="back" size="24" color="#000"></uni-icons>
				</view>
				<view class="search-box">
					<uni-icons type="search" size="20" color="#ccc"></uni-icons>
					<input class="search-input" 
						v-model="in_value"
						:placeholder="$L('请输入关键词')"
						placeholder-class="placeholder"
						confirm-type="search"
						@confirm="in_confirm"
						@input="input"
						focus
					/>
					<text v-if="in_value" class="clear-icon" @tap="clearInputVal">×</text>
				</view>
			</view>
		</view>

		<!-- 搜索历史 -->
		<view class="search-content" :class="{'search-content-h5': true}">
			<view class="history" v-if="history_val.length > 0">
				<view class="title">
					<text>{{ $L('搜索历史') }}</text>
					<uni-icons type="trash" size="20" color="#666" @tap="clear_history"></uni-icons>
				</view>
				<view class="history-list">
					<text v-for="(item, index) in history_val" 
						:key="index" 
						class="history-item"
						@tap="quick_search(item)">
						{{item}}
					</text>
				</view>
			</view>
		</view>

		<!-- 搜索结果 -->
		<block v-if="show_result">
			<view class="search_result" v-if="authorList.length>0">
				<view class="title">
					<text>{{$L('相关用户')}}</text>
					<view class="right" @tap="goAuthorList" v-if="showMore">
						<text>{{$L('查看更多')}}</text>
						<uni-icons type="arrowright" size="14" color="#666"></uni-icons>
					</view>
				</view>
				<!-- 主播item -->
				<videoFollow :list="authorList" @collect="collect" :bgStyle="bgStyle" :showFans="true" />
			</view>
		</block>
		<!-- {{show_result}}--{{videoList}} -->
		<block v-if="show_result">
			<!-- 短视频列表item -->
			<view class="search_result" v-if="videoList && videoList.length>0">
				<view class="title">
					<text>{{$L('相关内容')}}</text>
				</view>
				<searchVideoItem :videoList="videoList" :bgStyle="bgStyle" :listPlayIcon="imgUrl+'svideo/play.png'" />
			</view>
			<!-- 数据加载完毕 -->
			<dataLoaded :showFlag="!hasmore&&videoList.length>0" />

			<!-- 数据加载中 -->
			<dataLoading :showFlag="(!firstLoading)&&hasmore&&loading" />

			<!-- 页面loading -->
			<pageLoading :firstLoading="firstLoading&&show_result"
				:loadingIcon="imgUrl + 'svideo/page_loading_icon.gif'" />

			<!-- 页面空数据 -->
			<emptyData :showFlag="!firstLoading&&isShow_empty1&&isShow_empty2 "
				:emptyIcon="imgUrl+'svideo/live_list_empty_icon.png'" />

		</block>
	</view>
</template>

<script>
	//获取应用实例
	import {
	checkSpace
} from "@/utils/common";
import {
mapState
} from 'vuex';
import dataLoaded from "../component/dataLoaded.vue";
import dataLoading from "../component/dataLoading.vue";
import emptyData from "../component/emptyData.vue";
import pageLoading from "../component/pageLoading.vue";
import searchVideoItem from "../component/video/searchVideoItem.vue";
import videoFollow from "../component/video/videoFollow.vue";
	export default {
		components: {
			pageLoading,
			emptyData,
			dataLoading,
			dataLoaded,
			searchVideoItem,
			videoFollow,
		},
		data() {
			return {
				imgUrl: '',
				in_value: '',
				history_val: [],
				//搜索历史数组
				show_result: false,
				//是否展示搜索结果
				authorList: [],
				//作者列表
				videoList: [],
				//直播列表
				hasmore: true,
				//是否还有数据，用于页面展示
				loading: false,
				firstLoading: true,
				//是否初次加载，是的话展示页面居中的loading效果，
				bgStyle: 'background-size:contain;background-position:center center;background-repeat: no-repeat;', //背景图片样式
				isShow_empty1: true,
				isShow_empty2: true,
				current: 1,
				showMore: false,
				menuButtonInfo: null,
				statusBarHeight: 0,
			};
		},

		props: {},
		//是否还有数据
		onLoad: function (options) {
      setTimeout(()=>{
        uni.setNavigationBarTitle({
          title: this.$L('图文搜索')
        });    
      },0);
			
			this.getHistoryList();
			// #ifdef MP-WEIXIN
			const menuButtonInfo = wx.getMenuButtonBoundingClientRect()
			this.menuButtonInfo = menuButtonInfo
			this.statusBarHeight = menuButtonInfo.top + 'px'
			// #endif
		},
		created() {
			// 初始化imgUrl
			this.imgUrl = process.env.VUE_APP_IMG_URL || this.$store.state.imgUrl;
			console.log('imgUrl:', this.imgUrl);
		},
		computed: {
			...mapState(['hasLogin', 'cartData', 'userInfo']),
			getNavHeight() {
				// #ifdef MP-WEIXIN
				return this.menuButtonInfo ? this.menuButtonInfo.height + 'px' : '32px'
				// #endif
				// #ifdef H5
				return '44px'
				// #endif
			},
			getNavTop() {
				// #ifdef MP-WEIXIN
				return this.menuButtonInfo ? this.menuButtonInfo.top + 'px' : 'var(--status-bar-height)'
				// #endif
				// #ifdef H5
				return '0'
				// #endif
			},
			getPaddingRight() {
				// #ifdef MP-WEIXIN
				return this.menuButtonInfo ? (this.menuButtonInfo.width + 20) + 'px' : '97px'
				// #endif
				// #ifdef H5
				return '20px'
				// #endif
			}
		},
		onShow: function () {
			//更新作者列表的关注状态和粉丝数 data为作者id
			if (this.in_value) {
				this.btn_search();
			}
		},

		onReachBottom() {
			if (this.hasmore) {
				this.getVideoList();
			}
		},

		methods: {
			//获取历史记录
			getHistoryList() {
				var history_val = uni.getStorageSync('hisSldSearchSv');

				if (history_val) {
					var his_array = history_val.split("~");
					var last_arr = [];

					for (var i = 0; i < his_array.length; i++) {
						!checkSpace(his_array[i]) && last_arr.push(his_array[i]);
					}
					this.history_val = last_arr
				}
			},

			input(e) {
				this.in_value = e.detail.value
				this.show_result = false
			},

			//点击弹起的键盘按钮时触发
			in_confirm: function (e) {
				if (this.in_value != this.$L('请输入关键词') || this.in_value != '') {
					this.btn_search();
				}
			},
			//关注、取消关注事件
			collect(e) {
				let author_id = e.author_id;
				let key = uni.getStorageSync('token');
				let {
					authorList
				} = this;
				let tmp_data = authorList.filter(item => item.authorId == author_id)[0];
				let param = {}
				param.data = {}
				param.method = 'POST'
				if (tmp_data.isFollow) {
					param.url = 'v3/video/front/video/cancelFollow'
				} else {
					param.url = 'v3/video/front/video/followAuthor'
				}
				param.data.authorId = author_id
				param.method = 'POST'
				if (this.hasLogin) {
					this.$request(param).then(res => {
						if (res.state == 200) {
							tmp_data.isFollow = tmp_data.isFollow == true ? false : true;
							tmp_data.fansNum = tmp_data.isFollow == true ? tmp_data.fansNum * 1 + 1 : tmp_data.fansNum * 1 - 1;
							this.authorList = authorList
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							});
						}
					})
				} else {
					getApp().globalData.goLogin();
				}
			},
			//点击历史搜索
			quick_search(e) {
				this.show_result = true
				this.current = 1
				this.in_value = e
				this.getAuthor();
				this.getVideoList();
			},

			//搜索事件
			btn_search: function () {
				let {
					in_value
				} = this;
				in_value = in_value.trim();
				this.firstLoading = false
				if (in_value.length > 0) {

					this.show_result = true
					this.current = 1;
					this.setHistoryData();
					this.getAuthor();
					this.getVideoList();
					this.$forceUpdate();
				} else {
					uni.showToast({
						title: this.$L('请输入关键词进行搜索'),
						icon: 'none'
					});
				}
			},

			//查找作者
			getAuthor() {
				let {
					authorList,
					firstLoading,
					in_value
				} = this;
				let param = {}
				param.data = {}
				param.data.pageSize = 3
				param.data.current = 1
				param.data.keyword = this.in_value.trim()
				param.url = 'v3/video/front/video/search/userList'
				param.method = 'GET'
				this.$request(param).then(res => {
					if (res.state == 200) {
						let isShow
						if (res.state == 200) {
							let list = res.data.list;
							authorList = list;
							isShow = list.length > 0 ? false : true;
							if (res.data.pagination.total > 3) {
								this.showMore = true
							} else {
								this.showMore = false
							}
						} //初次加载的话更改状态

						if (firstLoading) {
							firstLoading = false;
						}

						this.authorList = authorList
						this.firstLoading = firstLoading
						this.isShow_empty1 = isShow
					}
				})
			},

			//查找短视频
			getVideoList() {
				this.loading = true
				let {
					videoList,
					hasmore,
					firstLoading,
					in_value
				} = this;
				let param = {}
				param.data = {}
				param.data.pageSize = 3
				param.data.current = this.current
				param.data.keyword = this.in_value.trim()
				param.url = 'v3/video/front/video/search/videoList'
				param.method = 'GET'
				this.$request(param).then(res => {
					if (res.state == 200) {
						let list = res.data.list;

						if (this.current == 1) {
							videoList = list;
						} else {
							videoList = videoList.concat(list);
						}
						if (this.$checkPaginationHasMore(res.data.pagination)) {
							this.current++;
						} else {
							this.hasmore = false;
							hasmore = false;
						}
						if (firstLoading) {
							firstLoading = false;
						}

						let isShow = videoList.length > 0 ? false : true;

						this.loading = false
						this.videoList = videoList
						this.firstLoading = firstLoading
						this.isShow_empty2 = isShow
						this.$forceUpdate();
					}
				})
			},

			//设置缓存
			setHistoryData() {
				let {
					history_val,
					in_value
				} = this;
				history_val.unshift(in_value); // 最多取30条，不重复且不为空的数据

				history_val = history_val.reduce((a, b) => {
					a.length <= 30 && b && a.indexOf(b) == -1 ? a.push(b) : null;
					return a;
				}, []);
				let history_val_str = history_val.join('~');
				this.history_val = history_val
				uni.setStorage({
					key: "hisSldSearchSv",
					data: history_val_str
				});
			},

			//清空历史记录事件
			clear_history: function (e) {
				uni.removeStorageSync('hisSldSearchSv');
				this.history_val = []
			},

			//清空输入内容
			clearInputVal() {
				this.in_value = ''
				this.show_result = false
			},

			//进入更多主播列表
			goAuthorList() {
				let {
					in_value
				} = this;
				let page = getCurrentPages();
				let len = page.length;

				if (len > 4) {
					this.$Router.replace({ path: '/extra/svideo/svideoAuthorList', query: { search_name: in_value } })
				} else {
					this.$Router.push({ path: '/extra/svideo/svideoAuthorList', query: { search_name: in_value } })
				}
			},
			goBack() {
				console.log('返回上一页');
				uni.navigateBack({
					delta: 1,
					fail: () => {
						uni.switchTab({
							url: '/pages/index/index'
						});
					}
				});
			},
		}
	};
</script>
<style lang="scss">
.container {
	min-height: 100vh;
	background: #fff;
}

.custom-nav {
	position: fixed;
	left: 0;
	right: 0;
	background: #fff;
	display: flex;
	align-items: center;
	padding: 0 20rpx;
	z-index: 999;
}

.nav-content {
	flex: 1;
	display: flex;
	align-items: center;
}

.back-icon {
	width: 88rpx;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: left;
}

.search-box {
	flex: 1;
	height: 64rpx;
	background: #f6f6f6;
	border-radius: 36rpx;
	display: flex;
	align-items: center;
	padding: 0 30rpx;
}

.search-icon {
	width: 40rpx;
	height: 40rpx;
	margin-right: 16rpx;
}

.search-input {
	flex: 1;
	height: 100%;
	font-size: 28rpx;
}

.placeholder {
	font-size: 28rpx;
	color: #999;
}

.clear-icon {
	font-size: 40rpx;
	color: #999;
	padding: 0 10rpx;
}

.search-content {
	margin-top: 120rpx;
	padding: 0 30rpx;
}

.history {
	.title {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 30rpx;
		
		text {
			font-size: 30rpx;
			color: #333;
			font-weight: bold;
		}
		
		.delete-icon {
			width: 40rpx;
			height: 40rpx;
		}
	}
	
	.history-list {
		display: flex;
		flex-wrap: wrap;
		
		.history-item {
			padding: 10rpx 30rpx;
			background: #f6f6f6;
			border-radius: 30rpx;
			font-size: 26rpx;
			color: #666;
			margin: 0 20rpx 20rpx 0;
		}
	}
}

.search_result {
	background: #f8f8f8;
}

.search_result .title {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	height: 77rpx;
	width: 750rpx;
	padding: 0 20rpx;
}

.search_result .title text {
	color: #2d2d2d;
	font-size: 28rpx;
}

.search_result .title .right {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: flex-end;
}

.search_result .title .right text {
	color: #949494;
	font-size: 24rpx;
	margin-right: 15rpx;
}

.search_result .title .right image {
	width: 11rpx;
	height: 19rpx;
}

/* #ifdef H5 */
.custom-nav-h5 {
	z-index: 999;
	background: #fff;
	// box-shadow: 0 1px 6px rgba(0,0,0,0.1);
}

.nav-content-h5 {
	height: 44px;
}

.search-content-h5 {
	margin-top: 64px;
}
/* #endif */
</style>