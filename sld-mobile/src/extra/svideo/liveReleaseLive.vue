<template>
  <view>
    <view>暂不支持发布</view>
    <view
      class="release"
      v-if="false"
      :style="'background-image: url(' + imgUrl + 'svideo/rbg1.png)'"
    >
      <view
        class="release_header_bg"
        :style="'background:' + (topFixed ? '#3A72B2' : '')"
      >
        <view class="header_nav">
          <view class="go_back_wrap" @tap="goBack">
            <image
              class="go_back"
              :src="imgUrl + 'svideo/white_arrow_l.png'"
            ></image>
          </view>
          <text class="title">{{ $L('发布') }}</text>
        </view>
      </view>
      <view class="release_main">
        <view class="title_part">
          <view
            class="title_tag"
            :style="
              'background:url(' +
              imgUrl +
              'svideo/title.png) no-repeat center;background-size:cover;'
            "
          >
            <text class="title">{{ $L('标题') }}</text>
          </view>
          <view class="title_wrap">
            <view class="left">
              <textarea
                class="input"
                data-type="live_name"
                :placeholder="$L('请输入名称，最多20字(必填)')"
                placeholder-style="font-size:26rpx;color:#949494;z-index:1,zoom:1"
                :value="live_name"
                maxlength="20"
                @input="editData($event, 'live_name')"
                auto-height="true"
              ></textarea>
            </view>
            <view class="right">
              <view
                v-if="JSON.stringify(cover) == '{}'"
                class="cover"
                @tap="uploadCover"
              >
                <image
                  class="cover_icon"
                  :src="imgUrl + 'svideo/cover_icon.png'"
                ></image>
              </view>
              <view
                class="cover"
                v-if="JSON.stringify(cover) != '{}' && cover.live_image_url"
                @tap="uploadCover"
              >
                <image
                  @tap="previewImage"
                  class="cover_img"
                  :src="cover.live_image_url"
                  :data-cover="cover.live_image_url"
                ></image>
                <image
                  class="del_cover"
                  @tap.stop="delCover"
                  :src="imgUrl + 'svideo/del_cover.png'"
                >
                </image>
              </view>
              <text class="cover_tip">{{ $L('选封面(必填)') }}</text>
            </view>
          </view>
        </view>
        <view class="avator">
          <view class="left">
            <text class="con">{{ $L('添加标签') }}</text>
            <image class="icon" :src="imgUrl + 'svideo/tag.png'"></image>
          </view>
          <view class="right">
            <picker
              class="con"
              range-key="labelName"
              @change="seleLabel"
              :range="label_list"
            >
              <view class="picker">
                {{
                  selectLabel.labelId != undefined && selectLabel.labelId
                    ? selectLabel.labelName
                    : $L('请选择标签(必填)')
                }}
              </view>
            </picker>
            <image
              class="arrow_r"
              :src="imgUrl + 'svideo/mem_arrow_r.png'"
            ></image>
          </view>
        </view>

        <!-- <view v-if="live_virtual_click_num_switch == 1" class="avator">
					<view class="left">
						<text class="con">{{$L('虚拟观看')}}</text>
						<image class="icon" :src="imgUrl+'svideo/virtual.png'"></image>
					</view>
					<view class="right">
						<input type="number" data-type="virtual_click_num" name="talk_con" :value="virtual_click_num"
							class="talk_con con" :placeholder="$L('最多输入1000000(选填)')"
							placeholder-style="font-size:24rpx;color:color: #949494;" @input="editData"
							maxlength="7"></input>
						<image class="arrow_r" :src="imgUrl+'svideo/mem_arrow_r.png'"></image>
					</view>
				</view>

				<view v-if="live_virtual_like_num_switch == 1" class="avator">
					<view class="left">
						<text class="con">{{$L('虚拟人气')}}</text>
						<image class="icon" :src="imgUrl+'svideo/virtual.png'"></image>
					</view>
					<view class="right">
						<input type="number" data-type="virtual_like_num" name="talk_con" :value="virtual_like_num"
							class="talk_con con" :placeholder="$L('最多输入1000000(选填)')"
							placeholder-style="font-size:24rpx;color:color: #949494;" @input="editData"
							maxlength="7"></input>
						<image class="arrow_r" :src="imgUrl+'svideo/mem_arrow_r.png'"></image>
					</view>
				</view> -->

        <view
          class="avator"
          :style="
            selGoods.length > 0
              ? 'border-radius:15rpx 15rpx 0 0'
              : 'border-radius:15rpx'
          "
        >
          <view class="left">
            <text class="con">{{ $L('商品选择') }}</text>
            <image class="icon" :src="imgUrl + 'svideo/product.png'"></image>
          </view>
          <view class="right" @tap="seleGoods">
            <text v-if="selGoods.length == 0" class="con"
              >{{ $L('请选择')
              }}{{
                live_bind_goods_num > 0
                  ? $L('(最多选择') + live_bind_goods_num + $L('件)')
                  : ''
              }}{{ $L('商品') }}</text
            >
            <view v-if="selGoods.length > 0">
              <text class="text">{{ $L('已选商品') }}：</text>
              <text class="text" style="color: #fc1c1c">{{
                selGoods.length
              }}</text>
              <text v-if="live_bind_goods_num * 1 > 0" class="text"
                >/{{ live_bind_goods_num }}</text
              >
            </view>
            <image
              class="arrow_r"
              :src="imgUrl + 'svideo/mem_arrow_r.png'"
            ></image>
          </view>
        </view>
        <!-- 商品列表item -->
        <view class="live_user_tab_content" scroll-y="true">
          <liveReleaseGoods
            :goodsData="selGoods"
            :addCartIcon="imgUrl + 'svideo/add_cart.png'"
            :eyeIcon="imgUrl + 'svideo/eye.png'"
            @delGoods="delGoods"
          />
        </view>
      </view>
      <view class="live_btn">
        <text v-if="liveState"
			  class="live_btn_text"
			  style="
				background: linear-gradient(90deg, rgba(255, 38, 65, 1) 0%, rgba(255, 122, 0, 1) 100%);
				box-shadow: 0 3px 10px 0 rgba(255, 13, 0, 0.26);
			  "
			  @tap="startLive"
		>{{ $L('开始直播') }}</text>
        <text v-else class="live_btn_text">{{ $L('开始直播') }}</text>
      </view>
    </view>
  </view>
</template>

<script>
  import { mapState } from 'vuex'
import liveReleaseGoods from '../templates/live/liveReleaseGoods.vue'
// import request from "@/utils/request";
const bus = getApp().globalData.bus

export default {
  components: {
    liveReleaseGoods
  },
  data() {
    return {
      imgUrl: process.env.VUE_APP_IMG_URL,
      //图片地址
      bgStyle:
        'background-size:contain;background-position:top center;background-repeat: no-repeat;',
      //背景图片样式
      cover: {},
      //封面信息
      virtual_click_num: '',
      //虚拟观看数
      virtual_like_num: '',
      //虚拟人气数
      live_name: '',
      //标题
      label_list: [],
      //标签列表
      label_index: '',
      //标签数组角标
      goods_ids: [],
      //已选商品的id数组
      goods_lists: [],
      //已选的商品
      live_bind_goods_num: 0,
      //平台设置信息
      width: 300,
      //裁剪的图片宽度
      height: 300,
      //裁剪的图片高度
      type: '',
      selectLabel: {}, //选择的标签
      selGoods: [], //已选中的商品列表
      storeId: '',
      showType: '',
      topFixed: false
    }
  },
  props: {},

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.initData()
    this.storeId = this.$Route.query.storeId
    uni.removeStorageSync('selGoods')
  },
  onShow: function () {},

  onPageScroll(e) {
    if (e.scrollTop > 20) {
      this.topFixed = true
    } else {
      this.topFixed = false
    }
  },

  computed: {
    liveState() {
      return (
        this.live_name != '' &&
        JSON.stringify(this.selectLabel) != '{}' &&
        JSON.stringify(this.cover) != '{}'
      )
    }
  },
  computed: {
    ...mapState(['hasLogin', 'userInfo'])
  },
  methods: {
    //更新选择的商品
    updateData(data) {
      this.setData({
        goods_ids: data.goods_ids,
        goods_lists: data.goodsList
      })
    },

    initData() {
      this.getLableList()
      this.getSetting()
    },

    // 返回上级页面
    goBack() {
      uni.removeStorageSync('selGoods')
      this.$Router.back(1)
    },

    // 选封面
    uploadCover() {
      let { cover } = this
      uni.chooseImage({
        count: 1,
        sizeType: ['original', 'compressed'],
        //可选择原图或压缩后的图片
        sourceType: ['album', 'camera'],
        //可选择性开放访问相册、相机
        success: (res) => {
          uni.uploadFile({
            url: process.env.VUE_APP_API_URL + 'v3/oss/front/upload',
            filePath: res.tempFilePaths[0],
            name: 'file',
            formData: {
              source: 'goods'
            },
            header: {
                Authorization:  'Bearer '+that.userInfo.member_access_token
             },
            success: (resup) => {
              resup = JSON.parse(resup.data)
              if (resup.state == 200) {
                // //获取到image-cropper实例
                this.cover = resup.data
                this.cover.live_image_url = resup.data.url
                this.cover.live_image = resup.data.path
              }
            }
          })
        }
      })
    },

    //删除封面
    delCover() {
      this.cover = {}
    },

    //编辑内容
    editData(e, type) {
      this[type] = e.detail.value
    },

    // 获取标签列表
    getLableList() {
      let param = {}
      param.data = {}
      param.method = 'GET'
      param.url = 'v3/video/front/video/live/labelList'
      this.$request(param).then((res) => {
        if (res.state == 200) {
          this.label_list = res.data
        }
      })
    },

    // 获取设置信息
    getSetting() {
      let param = {}
      param.url = 'v3/video/front/video/setting/getSettingList'
      param.method = 'GET'
      param.data = {}
      param.data.str = 'live_bind_goods_num,live_virtual_like_num_switch'
      this.$request(param).then((res) => {
        if (res.state == 200) {
          let result = res.data
          this.live_bind_goods_num = Number(result[0].value)
        }
      })
    },

    //选择标签
    seleLabel(e) {
      // this.setData({
      // 	label_index: e.detail.value
      // });
      this.selectLabel = this.label_list[e.detail.value]
    },

    //开始直播
    startLive() {
      let {
        live_name,
        cover,
        label_index,
        virtual_click_num,
        virtual_like_num,
        label_list,
        goods_ids,
        selectLabel,
        selGoods
      } = this

      if (live_name.trim() == '') {
        uni.showToast({
          title: this.$L('请输入标题'),
          icon: 'none'
        })
        return
      }

      if (JSON.stringify(cover) == '{}') {
        uni.showToast({
          title: this.$L('请上传封面图片'),
          icon: 'none'
        })
        return
      }

      if (JSON.stringify(selectLabel) == '{}') {
        uni.showToast({
          title: this.$L('请选择直播标签'),
          icon: 'none'
        })
        return
      }

      let param = {}
      param.data = {}
      param.method = 'POST'
      let selGoodsIds = []
      this.selGoods.map((selGoodsItem) => {
        if (selGoodsItem.isSel == true) {
          selGoodsIds.push(selGoodsItem.goodsId)
        }
      })
      param.data = {
        goodsIds: selGoodsIds.join(','),
        labelId: selectLabel.labelId,
        liveCover: cover.live_image,
        liveName: live_name
      }

      param.url = 'v3/video/front/video/live/publishLive'
      this.$request(param).then((res) => {
        if (res.state == 200) {
          uni.removeStorageSync('selGoods')
          if (res.data) {
            let live_id = ''
            live_id = res.data
            this.showType = 'livePush'
            this.$Router.replace({
              path: '/extra/live/livePush',
              query: { live_id, backGrade: 2 }
            })
          }
        } else {
          this.$api.msg(res.msg)
        }
      })
    },

    //选择商品
    seleGoods() {
      this.showType = 'selGoods'
      this.$Router.push({
        path: '/extra/live/liveSeleGoods',
        query: {
          liveBindGoodsNum: this.live_bind_goods_num,
          storeId: this.storeId
        }
      })
    },

    //删除商品
    delGoods(options) {
      this.selGoods.map((item, index) => {
        let iPid = item.defaultProductId
          ? item.defaultProductId
          : item.productId
        if (iPid == options.productId) {
          this.selGoods.splice(index, 1)
        }
      })
      uni.setStorageSync('selGoods', this.selGoods)
    },

    //图片预览
    previewImage(e) {
      let url = e.currentTarget.dataset.cover
      uni.previewImage({
        urls: [url]
      })
    }
  },

  beforeRouteLeave(to, from, next) {
    const pages = getCurrentPages() //获取页面栈
    const beforePage = pages[pages.length - 2] //前一个页面
    if (
      beforePage &&
      beforePage.route.indexOf('liveUserCenter') != -1 &&
      beforePage.curTab == 'live'
    ) {
      beforePage.$vm.changeTab('goods', true)
      beforePage.$vm.changeTab('live', true)
    }

    next()
  }
}
</script>
<style>
/* addons/pages/liveReleaseLive/liveReleaseLive.wxss */

page {
  background: #f8f8f8;
}

view {
  box-sizing: content-box;
}

.release {
  background-position: 0 0;
  background-repeat: no-repeat;
  background-size: contain;
  padding-bottom: 100rpx;
}

.release_header_bg {
  width: 750rpx;
  position: fixed;
  top: 0;
  padding-top: 80rpx;

  right: 0;
  z-index: 200;
}

.header_nav {
  padding: 0 20rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  height: 80rpx;
}

.header_nav .go_back_wrap {
  position: absolute;
  left: 0;
  width: 80rpx;
  height: 47rpx;
}

.header_nav .go_back {
  width: 45rpx;
  height: 47rpx;
}

.header_nav .title {
  color: #fff;
  font-size: 34rpx;
  font-weight: bold;
}

.release_main {
  padding-top: 160rpx;
  left: 0;
  right: 0;
  overflow: auto;
  z-index: 2;
}

.release_main .title_part {
  width: 690rpx;
  height: 240rpx;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 0px 15px 0px rgba(86, 86, 86, 0.1);
  border-radius: 15px;
  margin-left: 20rpx;
  padding: 20rpx 0 20rpx 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}

.release_main .title_part .title_tag {
  width: 131rpx;
  height: 50rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-left: -32rpx;
}

.release_main .title_part .title_tag .title {
  font-size: 32rpx;
  font-weight: 500;
  color: rgba(255, 255, 255, 1);
  line-height: 32rpx;
}

.release_main .title_part .title_wrap {
  width: 100%;
  height: 164rpx;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
}

.release_main .title_part .title_wrap .left {
  width: 465rpx;
  height: 100%;
  border-right: 1rpx solid rgba(0, 0, 0, 0.1);
}

.release_main .title_part .title_wrap .left .input {
  margin-top: 20rpx;
  color: #2d2d2d;
  font-size: 32rpx;
  width: 445rpx;
}

.release_main .title_part .title_wrap .right {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  flex: 1;
}

.release_main .title_part .title_wrap .right .cover {
  width: 125rpx;
  height: 125rpx;
  background: rgba(238, 238, 238, 1);
  border-radius: 15rpx;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  position: relative;
}

.release_main .title_part .title_wrap .right .cover .cover_img {
  width: 125rpx;
  height: 125rpx;
  border-radius: 15rpx;
}

.release_main .title_part .title_wrap .right .cover .del_cover {
  width: 45rpx;
  height: 45rpx;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 2;
}

.release_main .title_part .title_wrap .right .cover .cover_icon {
  width: 37rpx;
  height: 36rpx;
}

.release_main .title_part .title_wrap .right .cover_tip {
  font-size: 24rpx;
  color: #949494;
  line-height: 32rpx;
  margin-top: 15rpx;
}

.release_main .avator {
  background: #fff;
  width: 670rpx;
  height: 50rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
  margin-left: 20rpx;
  border-radius: 15rpx;
}

.release_main .avator .left {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}

.release_main .avator .left .con {
  color: #2d2d2d;
  font-size: 32rpx;
}

.release_main .avator .left .icon {
  width: 47rpx;
  height: 47rpx;
}

.release_main .avator .right {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  height: 100%;
  overflow: hidden;
}

.release_main .avator .right .con {
  color: #949494;
  font-size: 24rpx;
  text-align: right;
  width: 410rpx;
}

.release_main .avator .right .text {
  color: #2d2d2d;
  font-size: 24rpx;
  text-align: right;
}

.release_main .avator .right .img {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  overflow: hidden;
}

.release_main .avator .right .arrow_r {
  width: 40rpx;
  height: 42rpx;
  margin-left: 6rpx;
}

.live_btn {
  padding-left: 85rpx;
  position: fixed;
  z-index: 3;
  left: 0;
  right: 0;
  bottom: 0;
  background: #f8f8f8;
  height: 110rpx;
}

.live_btn .live_btn_text {
  font-size: 34rpx;
  font-weight: bold;
  color: #fff;
  line-height: 32rpx;
  width: 580rpx;
  height: 70rpx;
  background: #aaa;
  border-radius: 35rpx;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin: 20rpx 0;
}
</style>
