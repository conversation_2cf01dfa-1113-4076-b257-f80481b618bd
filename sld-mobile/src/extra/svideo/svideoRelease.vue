<template>

	<view :style="mix_diyStyle">
		<!-- #ifdef H5 -->
		<view class="release" v-show="!previewVideoSrcFlag">
			<scroll-view class="release_main" scroll-y>
				<view class="title_part">
					<view class="title_tag">
						<svgGroup type="to_title" class="title_tag_img" width="131" height="50" px="rpx"
							:color="diyStyle_var['--color_video_main']"></svgGroup>
						<text class="title">{{$L('标题')}}</text>
					</view>
					<view class="title_wrap">
						<view class="top">

							<textarea class="input" :placeholder="$L('请输入名称，最多20字(必填)')"
								placeholder-style="font-size:26rpx;color:#949494;z-index:1,zoom:1" :value="live_name"
								maxlength="20" @input="editData($event,'live_name')" auto-height="true"
								confirm-type="done">
								</textarea>
						</view>
						<view class="bottom">
							<view class="left">
								<view v-if="JSON.stringify(upload_video) == '{}'&&video_flag !=='200'" class="cover"
									@tap="openspecModel">
									<image class="cover_icon" :src="imgUrl+'svideo/upload_video_icon.png'">
									</image>
									<text class="cover_tip">{{$L('上传短视频(必填)')}}</text>
								</view>

								<view class="cover" v-else>
									<view class="cover_img flex_row_center_center" ref="videoWrapHls">
										<video :disabled="false" :controls="false" :src="upload_video.video_url"
											id="myVideo">
											<cover-view class="htz-image-upload-Item-video-fixed"
												@click="previewVideoSrcFlag = true"></cover-view>
											<cover-view class="htz-image-upload-Item-del-cover"
												@click="delVideo">×</cover-view>
										</video>
									</view>
								</view>
							</view>
							<view class="right">
								<view v-if="JSON.stringify(cover) == '{}'" class="cover" @tap="uploadCover">
									<image class="cover_icon" :src="imgUrl+'svideo/cover_icon.png'"></image>
									<text class="cover_tip">{{$L('选封面(必填)')}}</text>
								</view>
								<view class="cover" v-if="JSON.stringify(cover) != '{}' && cover.video_url != ''"
									@click="uploadCover">
									<image @click.stop="previewImage" class="cover_img" :src="cover.video_url"
										:data-cover="cover.video_url" mode="aspectFit"></image>
									<image class="del_cover" @click.stop="delCover"
										:src="imgUrl+'svideo/del_cover.png'"></image>
								</view>
							</view>
						</view>
					</view>
				</view>

				<view class="desc">
					<view class="desc_title flex_row_start_center">
						<svgGroup type="to_brief" class="desc_title_img" width="37" height="37" px="rpx"
							:color="diyStyle_var['--color_video_main']"></svgGroup>
						<text class="title">{{$L('视频简介')}}</text>
					</view>
					<textarea class="desc_con" :placeholder="$L('请输入简介，最多30字(必填)')"
						placeholder-style="font-size:26rpx;color:#949494;z-index:1,zoom:1" :value="video_desc"
						maxlength="30" @input="editData($event,'video_desc')" confirm-type="done"></textarea>
				</view>

				<view class="avator">
					<view class="left flex_row_start_center">
						<svgGroup type="to_label" class="desc_title_img" width="37" height="37" px="rpx"
							:color="diyStyle_var['--color_video_main']"></svgGroup>
						<text class="con">{{$L('添加标签')}}</text>
					</view>
					<view class="right">
						<picker class="con" range-key="labelName" @change="seleLabel" :range="label_list">
							<view class="picker">
								{{selectLabel.labelId != undefined &&
								selectLabel.labelId?selectLabel.labelName:$L('请选择标签(必填)')}}
							</view>
						</picker>
						<image class="arrow_r" :src="imgUrl+'svideo/mem_arrow_r.png'"></image>
					</view>
				</view>

				<view v-if="setting.video_virtual_click_num_switch == 1" class="avator">
					<view class="left">
						<text class="con">{{$L('虚拟播放')}}</text>
						<image class="icon" :src="imgUrl+'svideo/virtual.png'"></image>
					</view>
					<view class="right">
						<input type="number" data-type="virtual_click_num" name="talk_con" :value="virtual_click_num"
							class="talk_con con" :placeholder="$L('最多输入1000000(选填)')"
							placeholder-style="font-size:24rpx;color:color: #949494;" @input="editData"
							maxlength="7"></input>
						<image class="arrow_r" :src="imgUrl+'svideo/mem_arrow_r.png'"></image>
					</view>
				</view>

				<view v-if="setting.video_virtual_like_num_switch == 1" class="avator">
					<view class="left">
						<text class="con">{{$L('虚拟点赞')}}</text>
						<image class="icon" :src="imgUrl+'svideo/virtual.png'"></image>
					</view>
					<view class="right">
						<input type="number" data-type="virtual_like_num" name="talk_con" :value="virtual_like_num"
							class="talk_con con" :placeholder="$L('最多输入1000000(选填)')"
							placeholder-style="font-size:24rpx;color:color: #949494;" @input="editData"
							maxlength="7"></input>
						<image class="arrow_r" :src="imgUrl+'svideo/mem_arrow_r.png'"></image>
					</view>
				</view>
				<view class="avator" v-if="setting.member_bind_goods == 1"
					:style="goods_lists.length > 0?'border-radius:15rpx 15rpx 0 0':'border-radius:15rpx'">
					<view class="left">
						<svgGroup type="to_shop" class="desc_title_img" width="37" height="37" px="rpx"
							:color="diyStyle_var['--color_video_main']"></svgGroup>
						<text class="con">{{$L('商品选择')}}</text>
					</view>
					<view class="right" @tap="seleGoods">
						<text v-if="selGoods.length == 0" class="con">{{setting.bind_goods_num > 0 ? $L('请选择,最多选择') +
							setting.bind_goods_num +$L('件商品(选填)') : $L('请选择商品')}}
						</text>
						<view v-if="selGoods.length > 0">
							<text class="text">{{$L('已选商品')}}：</text>
							<text class="text" style="color:var(--color_video_main)">{{selGoods.length}}</text>
							<text v-if="setting.bind_goods_num*1>0" class="text">/{{setting.bind_goods_num*1}}</text>
						</view>
						<image class="arrow_r" :src="imgUrl+'svideo/mem_arrow_r.png'"></image>
					</view>
				</view>
				<!-- 商品列表item -->
				<view class="live_user_tab_content">
					<videoReleaseGoods :goodsData="selGoods" :addCartIcon="imgUrl+'svideo/add_cart.png'"
						:eyeIcon="imgUrl+'svideo/eye.png'" @delGoods="delGoods" />
				</view>
			</scroll-view>
			<view class="live_btn">
				<text v-if="enableRelease"
					style="background:var(--color_video_main_bg);box-shadow:0px 3px 10px 0px var(--color_video_halo);"
					@tap="$frequentyleClick(startLive)" class="live_btn_text">{{$L('发布')}}</text>
				<text v-else class="live_btn_text">{{$L('发布')}}</text>
			</view>
		</view>
		<view class="preview-full" v-if="previewVideoSrcFlag">
			<video :autoplay="true" :src="upload_video.video_url" :show-fullscreen-btn="false">
				<cover-view class="preview-full-close" @click="previewVideoSrcFlag=false"> ×
				</cover-view>
			</video>
		</view>


		<w-compress ref='wCompress' />
		<!-- #endif -->
	</view>
</template>

<script>
	// import request from "@/utils/request";
	import videoReleaseGoods from "@/components/video/videoReleaseGoods.vue";
import {
mapState,
} from 'vuex';
import wCompress from "../../components/w-compress/w-compress.vue";
	export default {
		components: {
			videoReleaseGoods,
			wCompress
		},
		data() {
			return {
				imgUrl: process.env.VUE_APP_IMG_URL,
				//图片地址
				bgStyle: 'background-size:cover;background-position:top center;background-repeat: no-repeat;',
				//背景图片样式
				cover: {},
				//封面信息
				upload_video: {},
				//上传的短视频
				virtual_click_num: '',
				//虚拟观看数
				virtual_like_num: '',
				//虚拟人气数
				live_name: '',
				//标题
				video_desc: '',
				//视频简介
				label_list: [],
				//标签列表
				selectLabel: {},
				goods_ids: [],
				//已选商品的id数组
				goods_lists: [],
				//已选的商品
				setting: {},
				//平台设置信息
				video_id: '', //短视频id，编辑短视频用
				type: "",
				roleType: 1, // 默认会员 1   2：商家
				storeId: '',
				selGoods: [], //已选中的商品列表
				onOff: true,
				video_flag: '0', //上传成功标志 0初始值 200成功 用来判断遮罩
				videoCon: null,
				firstOpen: true, // //是否第一次加载页面,
				previewVideoSrcFlag: false,
			};
		},

		props: {},

		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			setTimeout(() => {
				uni.setNavigationBarTitle({
					title: this.$L('发布短视频')
				});
			}, 0);

			if (options.edit) {
				setTimeout(() => {
					uni.setNavigationBarTitle({
						title: this.$L('编辑短视频')
					})
				}, 0);

			}
			uni.removeStorageSync('selGoods')
			if (this.$Route.query.video_id) {
				this.video_id = this.$Route.query.video_id;
				setTimeout(() => {
					uni.setNavigationBarTitle({
						title: this.$L('编辑')
					})
				}, 0);

				this.getVideoDetail();
			}
			this.roleType = this.$Route.query.roleType;
			this.storeId = this.$Route.query.storeId;
			if (this.firstOpen) {
				this.initData();
			}
		},



		onShow: function() {
			if (!this.firstOpen) {
				this.initData();
			} else {
				this.firstOpen = false;
			}
		},

		onUnload() {
			//wx-1-start
			// #ifdef MP-WEIXIN
			let context = wx.createVideoContext('myVideo')
			context.exitPictureInPicture()
			// #endif
			//wx-1-end
		},
		onHide() {
			//wx-2-start
			// #ifdef MP-WEIXIN
			let context = wx.createVideoContext('myVideo')
			context.exitPictureInPicture()
			// #endif
			//wx-2-end
		},
		onReady() {
			//app-3-start

			//app-3-end
		},

		onBeforeBack() {
			uni.removeStorageSync('selGoods')
		},

		onBackPress() {
			uni.removeStorageSync('selGoods')
		},


		computed: {
			...mapState(['userInfo']),
			enableRelease() {
				console.log("视频发布",this.live_name,JSON.stringify(this.selectLabel),JSON.stringify(this.cover),this.video_desc,JSON.stringify(this.upload_video))
				return this.live_name != '' && (JSON.stringify(this.selectLabel) != '{}') && (JSON.stringify(this.cover) !=
					'{}') && this.video_desc != '' && (JSON.stringify(this.upload_video) != '{}')
			}
		},
		methods: {

			compressImage(image) {
				return this.$refs.wCompress.start(image, {
					pixels: 4000000, // 最大分辨率，默认二百万
					quality: 0.9, // 压缩质量，默认0.8
					base64: false, // 是否返回base64，默认false，非H5有效
					coverCropped: true
				})
			},

			//app-4-start

			//app-4-end

			openspecModel() {
				this.uploadVideo()
			},

			//更新选择的商品
			updateData(data) {
				this.setData({
					goods_ids: data.goods_ids,
					goods_lists: data.goodsList
				});
			},

			initData() {
				this.getLableList();
				this.getSetting();
			},

			// 返回上级页面
			goBack() {
				this.$Router.back(1)
			},

			//获取短视频详情
			getVideoDetail() {
				let {
					video_id
				} = this;

				let param = {};
				param.url = 'v3/video/front/video/detail';
				param.method = 'GET';
				param.data = {};
				param.data.videoId = video_id;
				this.$request(param).then(res => {
					if (res.state == 200) {
						let result = res.data;
						this.live_name = result.videoName;
						this.video_desc = result.introduction;
						this.selectLabel.labelId = result.labelId;
						this.selectLabel.labelName = result.labelName;
						this.cover.video_url = result.videoThemeImageUrl;
						this.cover.video_path = result.videoThemeImage;
						this.cover.height = result.height;
						this.cover.width = result.width;
						this.upload_video.video_url = result.videoPathUrl;
						this.upload_video.video_path = result.videoPath;
						this.selGoods = result.goodsList;
						this.videoThemeImage = '';
						this.video_flag = 200;

					}
				})
			},



			// 上传短视频
			uploadVideo() {
				let {
					upload_video
				} = this;
				let that = this;

				if (JSON.stringify(upload_video) != '{}') {
					return
				}

				uni.chooseVideo({
					sourceType: ['album'],
					compressed: true,
					//默认压缩
					maxDuration: 60,
					camera: 'back',
					success(res) {
						console.log('upload-video-success', res)
						if (res.size > 20971520) {
							uni.showToast({
								title: that.$L('超出文件最大限制20m，请重新上传！'),
								icon: 'none',
								duration: 1500
							})
						} else {
							setTimeout(() => {
								uni.showLoading({
									title: that.$L('上传中'),
									mask: true
								})
							})
							uni.uploadFile({
								url: process.env.VUE_APP_API_URL + 'v3/oss/front/upload',
								filePath: res.tempFilePath,
								name: 'file',
								formData: {
									'source': 'video',
								},
								header: {
									Authorization: 'Bearer ' + that.userInfo.member_access_token
								},
								success: resup => {

									resup = JSON.parse(resup.data);




									if (resup.state == 200) {
										that.video_flag = '200'
										that.upload_video = resup.data;
										that.upload_video.video_url = resup.data.url;
										that.upload_video.video_path = resup.data.path;

									}
								},
								complete: (res) => {
									uni.hideLoading();
									// if (JSON.parse(res.data).state != 200) {
									if (!res.data && res.errMsg) {
										uni.showToast({
											title: res.errMsg ? res.errMsg : JSON.parse(res
												.data).msg,
											icon: 'none',
											duration: 1000
										})
									} else {
										//app-5-start



										//app-5-end
										uni.showToast({
											title: that.$L('上传成功'),
											duration: 1000
										})
									}
								},
							});
						}
					},
					complete(res) {}

				});
			},


			// 选封面
			uploadCover() {
				let {
					cover
				} = this;
				let that = this;
				let key = uni.getStorageSync('token');
				uni.chooseImage({
					count: 1,
					sizeType: ['original', 'compressed'],
					//可选择原图或压缩后的图片
					sourceType: ['album', 'camera'],
					//可选择性开放访问相册、相机
					success: async res => {
						let imageUrl = await this.compressImage(res.tempFilePaths[0]).catch(console.log)
						console.log(imageUrl, 'imageUrl')
						uni.uploadFile({
							url: process.env.VUE_APP_API_URL + 'v3/oss/front/upload',
							// #ifndef H5
							filePath: imageUrl.tempFilePath,
							// #endif
							// #ifdef H5
							filePath: imageUrl,
							// #endif
							name: 'file',
							formData: {
								'source': 'goods',
							},
							header: {
								Authorization: 'Bearer ' + that.userInfo.member_access_token
							},
							success: resup => {

								resup = JSON.parse(resup.data);





								if (resup.state == 200) {
									// //获取到image-cropper实例
									that.cover = resup.data;
									that.cover.video_path = resup.data.path;
									that.cover.video_url = resup.data.url;
									that.cover.height = resup.data.height;
									that.cover.width = resup.data.width
								}
							}
						});
					}
				});
			},

			//删除封面
			delCover() {
				this.cover = {};
			},

			//删除视频
			delVideo() {
				this.upload_video = {};
				this.video_flag = 0
				// #ifdef H5
				this.player_video.src('');
				// #endif
			},

			//编辑内容
			editData(e, type) {
				this[type] = e.detail.value;
			},

			// 获取标签列表
			getLableList() {
				let param = {};
				param.url = 'v3/video/front/video/labelList';
				param.method = 'GET';
				this.$request(param).then(res => {
					if (res.state == 200) {
						this.label_list = res.data.list;
					}
				})
			},

			// 获取设置信息
			getSetting() {
				let param = {};
				param.url = 'v3/video/front/video/setting/getSettingList';
				param.method = 'GET';
				param.data = {};
				param.data.str = 'bind_goods_num,member_bind_goods';
				this.$request(param).then(res => {
					if (res.state == 200) {
						let result = res.data;
						result && result.map(settingItem => {
							if (settingItem.name == 'bind_goods_num') { //绑定商品数
								this.setting.bind_goods_num = settingItem.value
							} else if (settingItem.name == 'member_bind_goods') { //会员是否绑定商品
								this.setting.member_bind_goods = settingItem.value
								this.$forceUpdate()
							}
						})
					}
				})
			},

			//选择标签
			seleLabel(e) {
				this.selectLabel = this.label_list[e.detail.value];
			},

			//开始直播
			startLive() {

				let {
					live_name,
					cover,
					upload_video,
					video_desc,
					virtual_click_num,
					virtual_like_num,
					label_list,
					goods_ids,
					selectLabel,
					setting,
					video_id
				} = this;
				let that = this
				if (live_name.trim() == '') {
					uni.showToast({
						title: this.$L('请输入标题'),
						icon: 'none'
					});
					return;
				}

				if (JSON.stringify(upload_video) == '{}') {
					uni.showToast({
						title: this.$L('请上传短视频'),
						icon: 'none'
					});
					return;
				}

				if (JSON.stringify(cover) == '{}') {
					uni.showToast({
						title: this.$L('请上传封面图片'),
						icon: 'none'
					});
					return;
				}

				if (video_desc == '') {
					uni.showToast({
						title: this.$L('请输入视频简介'),
						icon: 'none'
					});
					return;
				}

				if (JSON.stringify(selectLabel) == '{}') {
					uni.showToast({
						title: this.$L('请选择视频标签'),
						icon: 'none'
					});
					return;
				}
				let param = {}
				param.data = {};
				param.method = 'POST';
				let selGoodsIds = [];
				this.selGoods.map(selGoodsItem => {
					if (video_id != '') {
						selGoodsIds.push(selGoodsItem.goodsId)
					} else {
						if (selGoodsItem.isSel == true) {
							selGoodsIds.push(selGoodsItem.goodsId)
						}
					}
				})

				param.data = {
					introduction: video_desc,
					labelId: selectLabel.labelId,
					videoName: live_name,
					videoPath: upload_video.video_path,
					goodsIds: selGoodsIds.join(','),
					type: 1,
					videoThemeImage: cover.video_path,
					width: cover.width,
					height: cover.height,
				};
				if (video_id != '') {
					param.url = 'v3/video/front/video/editVideo';
					param.data.videoId = video_id;
				} else {
					param.url = 'v3/video/front/video/releaseVideo';
				}
				this.$request(param).then(res => {
					if (res.state == 200) {

						uni.showToast({
							title: this.video_id ? that.$L('编辑成功！') : that.$L('发布成功！'),
							icon: 'none',
							duration: 500
						}).then(() => {
							// this.$Router.replace('/extra/user/my')

							uni.removeStorage({
								key: 'selGoods',
							})


							let pages = getCurrentPages();
							let flag = true;
							let preIndex = '';
							pages.forEach((item, index) => { // 处理返回列表页 后退跳转层级重复问题
								if (item.route == "extra/user/my") {
									if (!preIndex) {
										preIndex = index;
									}
								} else if (item.route == "extra/svideo/svideoRelease") {
									flag = false;
									setTimeout(() => {
										uni.$emit('updateState')
										this.$Router.back(index - preIndex)
									}, 1000)
								}
							})
							if (flag) {
								let prevPage = pages[pages.length - 2]; //上一个页面
								if (prevPage) {
									prevPage.$vm.initData();
								}
								setTimeout(() => {
									uni.$emit('updateState')
									this.$Router.back(1)
								}, 1000)
							}








						})
					} else {
						this.$api.msg(res.msg);
					}
				})
			},

			//选择商品
			seleGoods() {

				let query = {
					bindGoodsNum: this.setting.bind_goods_num,
					selGoods: JSON.stringify(this.selGoods),
					roleType: this.roleType
				}

				if (this.storeId) {
					query.storeId = this.storeId
				}


				this.$Router.push({
					path: '/extra/svideo/svideoSeleGoods',
					query
				})
			},

			//删除商品
			delGoods(options) {
				this.selGoods.map((item, index) => {
					let iPid = item.defaultProductId ? item.defaultProductId : item.productId
					if (iPid == options.productId) {
						this.selGoods.splice(index, 1)
					}
				})
				uni.setStorageSync('selGoods', this.selGoods)
			},

			//图片预览
			previewImage(e) {
				let url = e.currentTarget.dataset.cover;
				uni.previewImage({
					urls: [url]
				});
			},

			cropperload(e) {},

			clickcut(e) {
				//点击裁剪框阅览图片
				uni.previewImage({
					current: e.detail.url,
					// 当前显示图片的http链接
					urls: [e.detail.url] // 需要预览的图片http链接列表

				});
			},
		},

		beforeRouteLeave(to, from, next) {
			const pages = getCurrentPages(); //获取页面栈
			const beforePage = pages[pages.length - 2]; //前一个页面
			if (beforePage) {
				if (beforePage.route.indexOf('liveUserCenter') != -1 && beforePage.$vm.curTab == 'video') {
					beforePage.$vm.changeTab('goods', true);
				}
				if (beforePage.$vm.getAuthorInfo) {
					beforePage.$vm.getAuthorInfo()
				}
			}

			next()

		}
	};
</script>
<style scoped lang="scss">
	/*遮罩判断标志*/
	.video_flag {
		display: flex !important;
	}

	page {
		background: $bg-color-split;
	}

	.page {
		width: 750rpx;
		margin: 0 auto;
		background: #f8f8f8;
	}

	.release {
		position: relative;

	}

	.release_header_bg {
		width: 750rpx;
		height: 462rpx;
	}

	.htz-image-upload-Item-video-fixed {
		position: absolute;
		top: 0;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 100%;
		border-radius: 10rpx;
		z-index: 6;
	}

	.htz-image-upload-Item-del-cover {
		background-color: #999;
		font-size: 24rpx;
		position: absolute;
		width: 35rpx;
		height: 35rpx;
		text-align: center;
		top: 0;
		right: 0;
		color: #fff;
		//app-6-start



		//app-6-end

		line-height: 35rpx;

		z-index: 9;
	}

	.preview-full {
		position: fixed;
		top: 0;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 100%;
		z-index: 1002;

		video {
			width: 100%;
			height: 100%;
			z-index: 1002;
		}
	}

	.preview-full-close {
		position: fixed;
		right: 32rpx;
		top: 25rpx;
		width: 80rpx;
		height: 80rpx;
		line-height: 60rpx;
		text-align: center;
		z-index: 1003;
		/* 	background-color: #808080; */
		color: #fff;
		font-size: 65rpx;
		font-weight: bold;

	}

	.header_nav {
		position: absolute;
		top: 80rpx;
		left: 20rpx;
		right: 0;
		z-index: 2;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		height: 80rpx;
	}

	.header_nav .go_back_wrap {
		position: absolute;
		left: 0;
		width: 80rpx;
		height: 47rpx;
	}

	.header_nav .go_back {
		width: 45rpx;
		height: 47rpx;
	}

	.header_nav .title {
		color: #fff;
		font-size: 34rpx;
		font-weight: bold;
	}

	.release_main {
		padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
		padding-top: 25rpx;
	}

	.release_main .title_part {
		width: 690rpx;
		height: 400rpx;
		background: rgba(255, 255, 255, 1);
		box-shadow: 0px 0px 15px 0px rgba(86, 86, 86, 0.1);
		border-radius: 15px;
		margin-left: 20rpx;
		padding: 20rpx 0 20rpx 20rpx;
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;
		box-sizing: content-box;
	}

	.release_main .title_part .title_tag {
		width: 131rpx;
		height: 50rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: center;
		margin-left: -32rpx;
		position: relative;
	}

	.release_main .title_part .title_tag_img {
		position: absolute;
		left: 0;
		top: 0;
	}

	.release_main .title_part .title_tag .title {
		font-size: 32rpx;
		font-weight: 500;
		color: rgba(255, 255, 255, 1);
		position: relative;
		line-height: 32rpx;
	}

	.release_main .title_part .title_wrap {
		width: 100%;
		height: 340rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: flex-start;
	}

	.release_main .title_part .title_wrap .top {
		width: 100%;
		margin-top: 20rpx;
		margin-bottom: 20rpx;
	}

	.release_main .title_part .title_wrap .top textarea {
		width: 100%;
		padding-left: 11rpx;
		color: #2D2D2D;
		font-size: 30rpx;


	}

	.release_main .title_part .title_wrap .bottom {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		width: 100%;
	}

	.release_main .title_part .title_wrap .bottom .left {
		/* width: 345rpx; */
		height: 257rpx;
		border-right: 1rpx solid rgba(0, 0, 0, 0.1);
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		margin-left: 11rpx;
		position: relative;
	}

	.release_main .title_part .title_wrap .bottom .left .cover {
		width: 345rpx;
		height: 257rpx;
		background: rgba(238, 238, 238, 1);
		border-radius: 15rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		position: relative;
		flex-shrink: 0;
		margin-right: 25rpx;
	}

	.coverAbsolute {
		position: absolute !important;
		z-index: 100;
		left: 0rpx;
	}

	.release_main .title_part .title_wrap .bottom .left .cover .cover_icon {
		width: 80rpx;
		height: 80rpx;
	}

	.release_main .title_part .title_wrap .bottom .left .cover .cover_img {
		width: 345rpx;
		height: 257rpx;
		border-radius: 15rpx;
		position: relative;

		video {
			width: 342rpx;
			height: 252rpx;
			border-radius: 15rpx;
		}
	}

	.release_main .title_part .title_wrap .bottom .left .cover .cover_img ::v-deep .video-js {
		overflow: hidden;
	}

	.release_main .title_part .title_wrap .bottom .right {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		flex: 1;
	}

	.release_main .title_part .title_wrap .bottom .right .cover {
		width: 257rpx;
		height: 256rpx;
		background: rgba(238, 238, 238, 1);
		border-radius: 15rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		position: relative;
	}

	.release_main .title_part .title_wrap .bottom .right .cover .cover_img {
		width: 257rpx;
		height: 256rpx;
		border-radius: 15rpx;
	}

	.release_main .title_part .title_wrap .bottom .right .cover .del_cover,
	.release_main .title_part .title_wrap .bottom .left .cover .del_cover {
		width: 45rpx;
		height: 45rpx;
		position: absolute;
		right: 0;
		top: 0;
		z-index: 100;
	}

	.release_main .title_part .title_wrap .bottom .right .cover .cover_icon {
		width: 80rpx;
		height: 80rpx;
	}

	.release_main .title_part .title_wrap .bottom .right .cover_tip,
	.release_main .title_part .title_wrap .bottom .left .cover_tip {
		font-size: 24rpx;
		color: #949494;
		line-height: 32rpx;
		margin-top: 15rpx;
	}

	.release_main .avator {
		background: #fff;
		width: 710rpx;
		padding: 20rpx 10rpx;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		margin-top: 20rpx;
		margin-left: 20rpx;
		border-radius: 15rpx;
	}

	.release_main .avator .left {
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
	}

	.release_main .avator .left .con {
		color: #2d2d2d;
		font-size: 32rpx;
	}

	.release_main .avator .left .icon {
		width: 47rpx;
		height: 47rpx;
	}

	.release_main .avator .right {
		display: flex;
		flex-direction: row;
		justify-content: flex-end;
		align-items: center;
		height: 100%;
		overflow: hidden;
	}

	.release_main .avator .right .con {
		color: #949494;
		font-size: 24rpx;
		text-align: right;
	}

	.release_main .avator .right .text {
		color: #2d2d2d;
		font-size: 24rpx;
		text-align: right;
	}

	.release_main .avator .right .img {
		width: 80rpx;
		height: 80rpx;
		border-radius: 40rpx;
		overflow: hidden;
	}

	.release_main .avator .right .arrow_r {
		width: 40rpx;
		height: 42rpx;
		margin-left: 6rpx;
	}

	.live_btn {
		/* padding-left: 85rpx; */
		position: fixed;
		z-index: 3;
		left: 0;
		right: 0;
		bottom: env(safe-area-inset-bottom);

		background: #f8f8f8;
		height: 110rpx;
		width: 750rpx;
		margin: 0 auto;
	}

	.live_btn .live_btn_text {
		font-size: 34rpx;
		font-weight: bold;
		color: #fff;
		line-height: 32rpx;
		width: 680rpx;
		height: 70rpx;
		background: #aaa;
		border-radius: 35rpx;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		margin: 20rpx auto;
	}

	.desc {
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;
		width: 710rpx;
		height: 190rpx;
		background: #fff;
		border-radius: 8rpx;
		margin: 20rpx 0 0 20rpx;
		padding: 20rpx;
		padding-left: 10rpx;
		box-sizing: border-box;
	}

	.desc_title {
		display: flex;
		align-items: center;
	}

	.desc_title .title {
		color: #2D2D2D;
		font-size: 32rpx;
	}

	.desc .desc_con {
		color: #949494;
		font-size: 28rpx;
		line-height: 40rpx;
		margin-top: 20rpx;
		width: 100%;
		padding-left: 12rpx;
		height: 90rpx !important;
	}

	.spec_model_box {
		width: 750rpx;
		min-height: 481rpx;
		background: #F8F8F8;
		border-radius: 15rpx;
		padding: 27rpx 0;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.spec_model_box_ba {
		position: relative;
		width: 710rpx;
		height: 214rpx;
		padding-left: 41rpx;
		padding-top: 63rpx;
	}

	.spec_model_box_ba image {
		width: 710rpx;
		height: 214rpx;
		position: absolute;
		left: 0;
		top: 0;
	}

	.spec_model_box_ba_p1 {
		position: relative;
		font-size: 34rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #2D2D2D;

	}

	.spec_model_box_ba_p2 {
		position: relative;
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #949494;
		margin-top: 20rpx;
	}
</style>