<template>
	<view :style="mix_diyStyle">
  <view>
    <!-- <image class="bg" :src="imgUrl+'svideo/list_bg.png'"></image> -->
    <view class="bg_1">
      
    </view> 
    <view
      class="live_list"
    >
      <view v-if="theme_img" class="rec_svideo">
        <image class="rec_img" :src="theme_img" mode="aspectFit"> </image>
      </view>

      <!-- 短视频列表item -->
      <listVideoItem
        :videoList="videoList"
        :bgStyle="bgStyle"
        :listFavIcon="imgUrl + 'svideo/fav.png'"
        :listPlayNum="imgUrl + 'svideo/zx_play.png'"
      />

      <!-- 数据加载完毕 -->
      <dataLoaded :showFlag="!hasmore && videoList.length > 0" />

      <!-- 数据加载中 -->
      <dataLoading :showFlag="hasmore && loading" />

      <!-- 页面loading -->
      <pageLoading
        :firstLoading="firstLoading"
        :loadingIcon="imgUrl + 'svideo/page_loading_icon.gif'"
      />

      <!-- 页面空数据 -->
      <emptyData
        :showFlag="!firstLoading && !videoList.length"
        :emptyIcon="imgUrl + 'live/live_list_empty_icon.png'"
      />

      <view class="empty_h"></view>
    </view>
  </view>
	</view>
</template>

<script>
//短视频-推荐主题页面
import { checkPageHasMore, initNum } from '@/utils/live'
import listVideoItem from '../component/video/listVideoItem.vue'
import pageLoading from '../component/pageLoading.vue'
import emptyData from '../component/emptyData.vue'
import dataLoading from '../component/dataLoading.vue'
import dataLoaded from '../component/dataLoaded.vue'
import request from '@/utils/request'

export default {
  data() {
    return {
      theme_img: '', // 主题图片
      theme_id: '',
      videoList: [], //短视频列表
      imgUrl: process.env.VUE_APP_IMG_URL, //图片地址
      hasmore: true, //是否还有数据，用于页面展示
      loading: false,
      current: 1,
      bgStyle:
        'background-size:cover;background-position:center center;background-repeat: no-repeat;', //背景图片样式
      firstLoading: true, //是否初次加载，是的话展示页面居中的loading效果，
      title: '',
      pageSize: 10
    }
  },

  components: {
    listVideoItem,
    pageLoading,
    emptyData,
    dataLoading,
    dataLoaded
  },
  props: {},
  //是否还有数据
  onReachBottom() {
    if (this.hasmore) {
      this.getList()
    }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // this.setData({ ...options
    // });
    this.theme_id = this.$Route.query.theme_id

    if (this.$Route.query.title) {
      setTimeout(()=>{
        uni.setNavigationBarTitle({
          title: this.$Route.query.title
        })    
      },0);
      
    }

    this.getList()
  },
  methods: {
    //获取短视频列表
    getList() {
      this.loading = true
      let { videoList, hasmore, firstLoading, theme_img } = this
      let param = {}
      param.data = {}
      param.data.pageSize = this.pageSize
      param.data.current = this.current
      param.data.themeId = this.theme_id
      param.url = 'v3/video/front/video/themeDetail'
      param.method = 'GET'
      this.$request(param).then((res) => {
        if (res.state == 200) {
          let list = res.data.videoList

          theme_img = res.data.themeInfo.image
          if (this.current == 1) {
            this.videoList = list
          } else {
            this.videoList = this.videoList.concat(list)
          }

          if (checkPageHasMore(res.data.pagination)) {
            this.current++
          } else {
            this.hasmore = false
            hasmore = false
          }
        } //初次加载的话更改状

        if (firstLoading) {
          firstLoading = false
        }

        this.loading = false
        this.hasmore = hasmore

        this.firstLoading = firstLoading
        this.theme_img = theme_img
      })
    }
  }
}
</script>
<style>
page {
  background-color: #f8f8f8;
  width: 750rpx;
  margin: 0 auto;
}

.nav {
  position: fixed;
  top: 80rpx;
  left: 0;
  width: 750rpx;
  height: 80rpx;
  background-color: 'transpanrent';
  display: block;
  white-space: nowrap;
  overflow: hidden;
  z-index: 9999;
}

.empty_h {
  height: 20rpx;
  width: 100%;
  background-color: 'transpanrent';
}

.bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 750rpx;
  height: 468rpx;
  z-index: 0;
  margin: 0 auto;
}
.bg_1 {
  position: fixed;
  top: 0;
  left: 0;
  width: 750rpx;
  height: 530rpx;
  z-index: 0;
  right: 0;
  margin: 0 auto;
  background:var(--color_video_main_vertical_bg);
}

.live_list {
  width: 100%;
  height: 100vh;
  background-color: transparent;
  padding-top: 20rpx;
  background-position: 0 0;
  background-repeat: no-repeat;
  background-size: contain;
  position: relative;
}

.rec_svideo {
  width: 750rpx;
  height: 345rpx;
  margin-bottom: 20rpx;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.rec_svideo .rec_img {
  width: 710rpx;
  height: 345rpx;
  border-radius: 15rpx;
}
</style>
