<template>
	<view :style="mix_diyStyle">
		<view v-if="loadEnd">
			<block v-if="videoList.length == 1">
				<view style="width: 750rpx; height: 100vh; overflow-y: hidden">
					<videoPlay @showVideoInfo="showVideoInfo" :id="'sldVideo_'" :video_id="videoList[0].video_id"
						:activeIndex="videoList[0].activeIndex" :author_id="videoList[0].author_id"
						:prevIndex="videoList[0].prevIndex" :nextIndex="videoList[0].nextIndex"
						:showHide="videoList[0].showHide" :preUpdateIndex="videoList[0].preUpdateIndex"></videoPlay>
				</view>
			</block>
			<block v-else>
				<swiper class="swiper" vertical :interval="interval" :duration="duration"
					@animationfinish="animationfinish" @change="videoChange" :current="videoCurrent">
					<swiper-item v-for="(item, index) in videoList" :key="index">
						<view style="width: 750rpx; height: 100vh; overflow-y: hidden">
							<videoPlay @showVideoInfo="showVideoInfo" :id="'sldVideo_'" :video_id="item.video_id"
								:activeIndex="item.activeIndex" :author_id="item.author_id" :listIndex="index"
								:prevIndex="item.prevIndex" :nextIndex="item.nextIndex" :showHide="item.showHide"
								:preUpdateIndex="item.preUpdateIndex" ref="videoPlayGroup" :videoCurrent="videoCurrentId"
								@error="handleVideoError">
							</videoPlay>
						</view>
					</swiper-item>
				</swiper>
			</block>
			
			<scroll-view scroll-y enable-flex class="video-list-scroll" 
				@scrolltolower="onScrollToLower" v-if="hasMore">
				<view class="loading-more" v-if="loadingMore">{{ $L('加载中...') }}</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	import {
		checkPageHasMore
	} from '@/utils/live'
	import request from '@/utils/request'
	import videoPlay from '../component/video/videoPlay'

	export default {
		data() {
			return {
				theme_id: 0,
				//theme_id: 该主题下的所有视频
				author_id: 0,
				//author_id: 该作者下的所有视频
				type: '',
				//type: 我喜欢的视频
				video_id: 0,
				//当前短视频id
				playFlag: true,
				//视频是否播放
				showPauseBtn: false,
				//是否显示暂停按钮
				showBtnIcn: '',
				//视频播放控制层按钮图片
				activeIndex: 0,
				//当前播放的视频id
				videoUrl: [
					// 视频列表
					{
						now: ''
					}
				],
				videoId: [],
				// 所有视频id
				prevIndex: '',
				// 上个视频的id
				nextIndex: '', // 下个视频的id
				videoInfo: '',
				ShowHide: 'hide',
				preUpdateIndex: '', //是否需要更新上一个页面的数据 -- 取操作的列表下标

				current: 0,
				videoList: [],
				interval: 2000,
				duration: 500,
				pageCurrent: 1,
				pageSize: 5,
				hasMore: true,
				labelId: '',
				loadEnd: false,
				videoInfoList: [],
				videoCurrent: 0,
				// 添加视频格式支持配置
				videoConfig: {
					// #ifdef MP-WEIXIN
					formats: ['mp4', 'mov'], // 小程序支持的视频格式
					// #endif
					// #ifdef H5 
					formats: ['mp4', 'webm', 'ogg', 'm3u8', 'mov'], // 扩展H5支持的视频格式，添加mov支持
					options: {
						html5: {
							hls: {
								withCredentials: true // 允许跨域请求携带认证信息
							},
							nativeTextTracks: false, // 禁用原生文本轨道以提高兼容性
							nativeVideoTracks: false,
							nativeAudioTracks: false,
							overrideNative: true, // 覆盖原生播放器
						},
						techOrder: ['html5'], // 设置技术优先级
						sources: [], // 将在运行时填充
						controls: true,
						autoplay: false,
						preload: 'auto',
						crossOrigin: 'anonymous'
					}
					// #endif
				},
				// 添加视频源处理配置
				videoSourceConfig: {
					// 移除URL中的引用标记
					cleanUrl: (url) => {
						if(!url) return ''
						return url.split('#')[0]
					},
					// 添加跨域参数和格式检查
					addCorsParam: (url) => {
						if(!url) return ''
						// 检查URL是否已经有参数
						const hasParams = url.includes('?')
						// 添加必要的参数
						const corsParam = hasParams ? '&' : '?'
						return `${url}${corsParam}cors=1&_t=${Date.now()}`
					},
					// 获取视频MIME类型
					getMimeType: (url) => {
						const ext = url.split('.').pop().toLowerCase()
						const mimeTypes = {
							'mp4': 'video/mp4',
							'webm': 'video/webm',
							'ogg': 'video/ogg',
							'm3u8': 'application/x-mpegURL',
							'mov': 'video/quicktime' 
						}
						return mimeTypes[ext] || 'video/mp4'
					}
				}
			}
		},

		components: {
			videoPlay
		},
		props: {},
		//继续播放按钮图片

		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad(options) {
			if (this.$Route.query.index) {
				this.preUpdateIndex = String(this.$Route.query.index)
				uni.$emit('updateView', this.$Route.query.index) //更新浏览次数
			}
			if (this.$Route.query.scene) {
				let url = decodeURIComponent(this.$Route.query.scene)
				this.video_id = url.split('=')[1]
			}
			this.labelId = this.$Route.query.curLabelId
			this.activeIndex = Number(this.$Route.query.video_id)
			this.video_id = Number(this.$Route.query.video_id)
			this.author_id = Number(this.$Route.query.author_id)
			this.getVideoList()

			this.videoList.push({
				activeIndex: Number(this.$Route.query.video_id),
				video_id: Number(this.$Route.query.video_id),
				author_id: Number(this.$Route.query.author_id),
				prevIndex: '',
				nextIndex: '',
				showHide: 'hide',
				preUpdateIndex: this.preUpdateIndex ? String(this.preUpdateIndex) : ''
			})
		},
		
		computed:{
			videoCurrentId(){
				let target = this.videoList.find((_,index)=>index==this.current)
				return target?.video_id
			}
		},
		
		
		onShow() {
			
			this.instanceHander('show')
			
		},
		onHide() {
			this.instanceHander('hide')
			
		},
		onShareAppMessage: function() {
			return {
				title: this.videoInfo.videoName,
				path: `/extra/svideo/svideoPlay?video_id=${this.videoInfo.videoId}&curLabelId=${this.labelId}`,
				imageUrl: this.videoInfo.videoThemeImage
			}
		},
		onShareTimeline: function() {
			return {
				title: this.videoInfo.videoName,
				query: `video_id=${this.videoInfo.videoId}&curLabelId=${this.labelId}`,
				imageUrl: this.videoInfo.videoThemeImage
			}
		},
		methods: {
			
			
			instanceHander(type){
				
				let execType={
					show:'startVideoPlay',
					hide:'startVideoPause'
				}
				console.log(type,'type')
				if(!this.$refs.videoPlayGroup) return 
				
				if(Array.isArray(this.$refs.videoPlayGroup)){
					this.$refs.videoPlayGroup.forEach(ins => ins[execType[type]]())
					return 
				}
				this.$refs.videoPlayGroup[execType[type]]()
			},
			
			videoChange(e) {
				this.videoCurrent = e.detail.current
				this.videoInfoList.forEach(item => {
					if (item.videoId == this.videoList[this.videoCurrent].video_id) {
						this.videoInfo = item
					}
				})
			},

			// 处理视频URL
			processVideoUrl(url) {
				if(!url) return ''
				let processedUrl = this.videoSourceConfig.cleanUrl(url)
				
				// #ifdef H5
				// 检查视频格式是否支持
				const ext = processedUrl.split('.').pop().toLowerCase()
				if(!this.videoConfig.formats.includes(ext)) {
					console.warn(`Video format ${ext} may not be supported in H5`)
				}
				
				// 如果是mov格式，尝试转换为mp4
				if(ext === 'mov') {
					// 尝试将mov替换为mp4（如果服务器支持）
					const mp4Url = processedUrl.replace(/\.mov$/i, '.mp4')
					// 如果mp4版本存在，使用mp4版本
					if(this.checkUrlExists(mp4Url)) {
						processedUrl = mp4Url
					}
				}
				
				// 添加跨域和缓存参数
				processedUrl = this.videoSourceConfig.addCorsParam(processedUrl)
				// 返回带有MIME类型的视频源对象
				return {
					src: processedUrl,
					type: this.videoSourceConfig.getMimeType(processedUrl)
				}
				// #endif
				
				// #ifdef MP-WEIXIN
				return processedUrl
				// #endif
			},
			
			// 检查URL是否存在
			checkUrlExists(url) {
				try {
					const xhr = new XMLHttpRequest()
					xhr.open('HEAD', url, false)
					xhr.send()
					return xhr.status !== 404
				} catch (error) {
					console.error('Error checking URL:', error)
					return false
				}
			},

			// 获取短视频列表数据
			getVideoList() {
				let param = {}
				param.url = 'v3/video/front/video/videoList'
				param.method = 'GET'
				param.data = {}
				param.data.videoId = this.video_id
				param.data.labelId = this.labelId
				param.data.current = this.pageCurrent
				param.data.pageSize = this.pageCurrent == 1 ? 4 : this.pageSize
				this.$request(param).then((res) => {
					this.loadEnd = true
					if (res.state == 200) {
						if (res.data && res.data.list && res.data.list.length > 0) {
							let arr = []
							res.data.list.forEach((item) => {
								// 处理视频URL
								if(item.videoUrl) {
									item.videoUrl = this.processVideoUrl(item.videoUrl)
								}
								// 如果是H5环境，检查视频格式
								// #ifdef H5
								if(item.videoUrl && !this.checkVideoFormat(item.videoUrl)) {
									console.warn(`Video format not supported for URL: ${item.videoUrl}`)
								}
								// #endif
								arr.push({
									activeIndex: item.videoId,
									video_id: item.videoId,
									author_id: item.authorId,
									prevIndex: '',
									nextIndex: '',
									showHide: 'hide',
									preUpdateIndex: '',
									videoUrl: item.videoUrl
								})
							})
							this.videoList = this.videoList.concat(arr)
						}
						this.hasMore = this.$checkPaginationHasMore(res.data.pagination)
						if (this.hasMore) {
							this.pageCurrent++
						}
					}
				})
			},

			//设置短视频播放/暂停状态
			playVideo() {
				let _this = this
				let videoId = ''
				
				// #ifdef H5
				videoId = 'myVideo' + this.videoList[this.current].video_id
				let current_video = videojs.getPlayer(videoId)
				if(current_video) {
					// 配置video.js播放器
					const videoSource = this.processVideoUrl(this.videoList[this.current].videoUrl)
					
					// 设置视频选项
					current_video.options_ = {
						...current_video.options_,
						...this.videoConfig.options,
						html5: {
							...this.videoConfig.options.html5,
							nativeVideoTracks: false,
							nativeAudioTracks: false,
							nativeTextTracks: false,
							hls: {
								overrideNative: true
							}
						}
					}
					
					// 设置跨域属性
					current_video.crossOrigin = 'anonymous'
					
					// 错误处理
					current_video.on('error', (e) => {
						console.error('Video error:', current_video.error())
						// 如果是mov格式且播放失败，尝试其他方案
						if(videoSource.type === 'video/quicktime') {
							console.warn('MOV format failed, trying alternative...')
							// 这里可以添加备用方案，比如尝试使用不同的编解码器或格式
						}
						this.handleVideoError(e)
					})
					
					// 设置视频源
					current_video.src(videoSource)
					
					// 尝试播放
					current_video.play().catch(err => {
						console.error('Video play error:', err)
						this.handleVideoError(err)
					})
				}
				
				// 暂停其他视频
				this.videoList.forEach((item) => {
					let pauseId = 'myVideo' + item.video_id
					if (pauseId != videoId) {
						let other_video = videojs.getPlayer(pauseId)
						if(other_video) {
							other_video.pause()
							other_video.currentTime(0)
						}
					}
				})
				// #endif
				
				// #ifdef MP-WEIXIN
				videoId = 'sldVideo_child' + this.videoList[this.current].video_id
				this.videoList.forEach((item) => {
					let pauseId = 'sldVideo_child' + item.video_id
					if (pauseId == videoId) {
						this.instanceHander('show')
					} else {
						this.instanceHander('hide')
					}
				})
				// #endif
			},

			animationfinish(e) {
				if (this.current != e.detail.current) {
					this.current = e.detail.current
					this.playVideo()
					if (this.pageCurrent == this.videoList.length && this.hasMore) {
						this.getVideoList()
					}
				}
			},

			showVideoInfo(videoInfo) {
				this.videoInfoList.push(videoInfo)
				if (videoInfo.videoId == this.video_id) {
					this.videoInfo = videoInfo
				}
			},

			//视频暂停/继续播放事件
			videoPlayControl() {
				let {
					playFlag,
					showPauseBtn
				} = this
				this.videoContext = uni.createVideoContext('sldVideo')

				if (playFlag) {
					if (!showPauseBtn) {
						this.setData({
							showPauseBtn: true,
							showBtnIcn: this.pauseBtnIcon
						}) //3s后自动消失

						setTimeout(() => {
							this.setData({
								showPauseBtn: false
							})
						}, 3000)
					} else {
						this.videoContext.pause() //暂停播放

						this.setData({
							showPauseBtn: false,
							playFlag: false,
							showBtnIcn: this.playBtnIcon
						})
					}
				} else {
					this.videoContext.play() //开始播放

					this.setData({
						playFlag: true,
						showPauseBtn: false
					})
				}
			},

			// 添加视频错误处理方法
			handleVideoError(e) {
				console.error('视频加载错误:', e)
				// #ifdef MP-WEIXIN
				uni.showToast({
					title: this.$L('视频加载失败，请检查网络'),
					icon: 'none'
				})
				// #endif
				
				// #ifdef H5
				if(e.target && e.target.error) {
					let errorMsg = ''
					switch(e.target.error.code) {
						case 1:
							errorMsg = this.$L('视频加载中断')
							break
						case 2:
							errorMsg = this.$L('网络错误')
							break
						case 3:
							errorMsg = this.$L('视频解码错误')
							break
						case 4:
							errorMsg = this.$L('视频格式不支持')
							break
						default:
							errorMsg = this.$L('未知错误')
					}
					uni.showToast({
						title: errorMsg,
						icon: 'none'
					})
				}
				// #endif
			},
			
			// 检查视频格式是否支持
			checkVideoFormat(url) {
				if(!url) return false
				const ext = url.split('.').pop().toLowerCase()
				return this.videoConfig.formats.includes(ext)
			}
		}
	}
</script>
<style lang="scss">
	page {
		width: 750rpx;
		margin: 0 auto;
		position: relative;
	}

	.video-list-scroll {
		position: relative;
		width: 100%;
		display: flex;
		flex-direction: column;
		
		.loading-more {
			width: 100%;
			height: 60rpx;
			line-height: 60rpx;
			text-align: center;
			background-color: #f5f5f5;
			color: #999;
			font-size: 24rpx;
		}
	}

	.swiper {
		position: relative;
		width: 100%;
		//去除tabbar高度
		height: 100vh;
		background: #000000;

		.swiper-item {
			.info {
				z-index: 1;
				position: absolute;
				bottom: 60upx;
				color: white;
				text-indent: 1em;
				font-size: 30upx;
			}

			.audio {
				position: absolute;
				bottom: 20upx;
				z-index: 1;
				text-indent: 1em;
				color: white;
				font-size: 30upx;
				display: flex;
				width: 100%;
				flex-direction: row;
				justify-content: space-between;
				align-items: flex-end;

				@-webkit-keyframes move {
					0% {
						left: 100vw;
					}

					100% {
						left: -50vw;
					}
				}

				@-moz-keyframes move {
					0% {
						left: 100vw;
					}

					100% {
						left: -50vw;
					}
				}

				@-ms-keyframes move {
					0% {
						left: 100vw;
					}

					100% {
						left: -50vw;
					}
				}

				@keyframes move {
					0% {
						left: 100vw;
					}

					100% {
						left: -50vw;
					}
				}

				.text-group {
					position: relative;
					width: 50vw;
					height: 40upx;

					overflow: hidden;

					.text {
						position: absolute;
						top: 0upx;
						white-space: nowrap;
						/*文本不会换行，文本会在在同一行上继续*/
						-webkit-animation: 10s move infinite;
						-moz-animation: 10s move infinite;
						-ms-animation: 10s move infinite;
						animation: 10s move infinite;
						width: 50vw;
						left: 100vw;
					}
				}

				.icon {
					width: 60upx;
					height: 60upx;
					border-radius: 60upx;
					animation: turn 3s linear infinite;
					margin-right: 5vw;
					border: 10upx solid white;
				}

				/* 
					  turn : 定义的动画名称
					  1s : 动画时间
					  linear : 动画以何种运行轨迹完成一个周期
					  infinite :规定动画应该无限次播放
					 */
				@keyframes turn {
					0% {
						-webkit-transform: rotate(0deg);
					}

					25% {
						-webkit-transform: rotate(90deg);
					}

					50% {
						-webkit-transform: rotate(180deg);
					}

					75% {
						-webkit-transform: rotate(270deg);
					}

					100% {
						-webkit-transform: rotate(360deg);
					}
				}
			}

			.video {
				width: 100%;
				z-index: 0;
				height: calc(100vh - 120rpx);
			}

			.buttons {
				display: flex;
				flex-direction: column;
				position: absolute;
				right: 5vw;
				bottom: 12vh;
				color: white;
				text-align: center;
				justify-content: center;
				z-index: 1;

				.header_group {
					margin-bottom: 50upx;
					height: 90upx;
					width: 90upx;
					position: relative;

					.header {
						border: 2px solid white;
						margin: 0 auto;
						border-radius: 90upx;
						height: 90upx;
						width: 90upx;
					}

					.add {
						position: absolute;
						bottom: -30upx;
						margin: 0 auto;
						right: 0upx;
						background-color: #f15b6c;
						left: 0upx;
						width: 50upx;
						height: 50upx;
						font-size: 50upx;
						line-height: 50upx;
						border-radius: 50upx;
					}
				}

				.button {
					text-align: center;
					font-size: 25upx;

					.icon {
						margin: 20upx;
						width: 60upx;
					}
				}
			}
		}
	}
</style>