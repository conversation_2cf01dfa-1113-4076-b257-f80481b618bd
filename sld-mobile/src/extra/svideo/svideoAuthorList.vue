<template>
	<view :style="mix_diyStyle">
  <view>
    <scroll-view scroll-y class="fllow_list" v-if="list.length">
      <!-- item -->
      <videoFollow :list="list" :bgStyle="bgStyle" :showFans="true" />
    </scroll-view>
    <!-- 数据加载完毕 -->
    <dataLoaded :showFlag="!hasmore && list.length > 0" />

    <!-- 数据加载中 -->
    <dataLoading :showFlag="hasmore && loading" />

    <!-- 页面loading -->
    <pageLoading
      :firstLoading="firstLoading"
      :loadingIcon="imgUrl + 'svideo/page_loading_icon.gif'"
    />

    <!-- 页面空数据 -->
    <emptyData
      :showFlag="!firstLoading && !list.length"
      :emptyIcon="imgUrl + 'live/live_list_empty_icon.png'"
    />
  </view>
	</view>
</template>

<script>
import { checkPageHasMore } from '@/utils/live'
import request from '@/utils/request'
import pageLoading from '../component/pageLoading.vue'
import emptyData from '../component/emptyData.vue'
import dataLoading from '../component/dataLoading.vue'
import dataLoaded from '../component/dataLoaded.vue'
import videoFollow from '../component/video/videoFollow.vue'
const bus = getApp().globalData.bus

export default {
  components: {
    pageLoading,
    emptyData,
    dataLoading,
    dataLoaded,
    videoFollow
  },
  data() {
    return {
      imgUrl: process.env.VUE_APP_IMG_URL,
      list: [],
      //当前列表数据
      hasmore: true,
      //是否还有数据，用于页面展示
      loading: false,
      firstLoading: true,
      //是否初次加载，是的话展示页面居中的loading效果，
      bgStyle:
        'background-size:contain;background-position:center center;background-repeat: no-repeat;',
      //背景图片样式
      search_name: '', //搜索内容
      pageSize: 10,
      current: 1
    }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    setTimeout(()=>{
      uni.setNavigationBarTitle({
        title: this.$L('更多用户')
      })
    },0);
    
    this.search_name = this.$Route.query.search_name
    this.getList()
  },
  onShow: function () {
    //更新作者列表的关注状态和粉丝数 data为作者id
    bus.on('updateAuthorListFollow', (data) => {
      let { list } = this
      let cur_data = list.filter((item) => item.author_id == data)[0]
      cur_data.is_follow = cur_data.is_follow == 1 ? 0 : 1
      cur_data.fans_num =
        cur_data.is_follow == 1
          ? cur_data.fans_num * 1 + 1
          : cur_data.fans_num * 1 - 1
      bus.emit('updateAuthorFollow', data)
      this.list = list
    })
  },

  onReachBottom() {
    if (this.hasmore) {
      this.getList()
    }
  },

  methods: {
    //获取数据
    getList() {
      let { list, firstLoading, loading, hasmore, search_name } = this
      this.loading = true
      let param = {}
      param.data = {}
      param.data.pageSize = this.pageSize
      param.data.current = this.current
      param.data.keyword = this.search_name
      param.url = 'v3/video/front/video/search/userList'
      param.method = 'GET'
      this.$request(param).then((res) => {
        if (res.state == 200) {
          let temp_list = res.data.list

          if (this.current == 1) {
            list = temp_list
          } else {
            list = list.concat(temp_list)
          }

          if (checkPageHasMore(res.data.pagination)) {
            this.current++
          } else {
            this.hasmore = false
            hasmore = false
          }
        } //初次加载的话更改状

        if (firstLoading) {
          firstLoading = false
        }

        this.loading = false
        this.hasmore = hasmore
        this.list = list
        this.firstLoading = firstLoading
      })
    }
  }
}
</script>

<style>
page {
  background: #f8f8f8;
}

.live_fllow_tab {
  /* width: 100%; */
  width: 750rpx;
  height: 80rpx;
  background: #fff;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;
  margin: 0 auto;
}

.live_fllow_tab text {
  color: #2d2d2d;
  font-size: 26rpx;
  line-height: 80rpx;
  border-bottom: 2px solid #fff;
}

.live_fllow_tab text.sel {
  color: #fc1c1c;
  border-bottom: 2px solid #ec1313;
}

.fllow_list {
  margin-top: 20rpx;
  height: 100%;
  width: 100%;
}

.empty_h {
  height: 100rpx;
  width: 100%;
  background-color: 'transpanrent';
}
</style>
