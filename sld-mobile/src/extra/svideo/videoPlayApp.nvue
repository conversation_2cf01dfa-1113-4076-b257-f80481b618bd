<!-- 
	@zoucb 2023-05-12
	短视频app端播放页
 -->  
<template>
	<view>
		<swiper 
			ref="listBox" 
			vertical 
			@animationfinish="animationfinish" 
			class="swiper" 
			@transition="videoTransition" 
			:style="'height: '+ boxStyle.height +';'"
			@change="videoChange"
		>
			<swiper-item v-for="(item,videoIdx) in videoList" :key="item.video_id" :data-index="videoIdx">
				<view class="wrap" :style="'height: '+ boxStyle.height +';'" v-if="item.videoDetail">
					<video :id="'sldVideo_child'+item.video_id" ref="sldVideo_child" class="live_back"
						:src="item.videoDetail.videoPath" loop controls="false" show-fullscreen-btn="false"
						show-progress object-fit="contain" :autoplay="false" show-center-play-btn="false"
						enable-play-gesture :style="boxStyle" http-cache :data-index="videoIdx" @play="bindplay"
						@pause="bindpause" @click.stop="chansgedd">
					</video>

					<view class="live_pause" @click.stop="chansgedd" v-if="!item.videoPlay" >
						<image class="live_pause_img" :src="imgUrl + 'svideo/svideo_play.png'"
							mode="aspectFit" lazy-load></image>
					</view>

					<view class="live_header" v-if="item.authorDetail">
						<image class='live_header_go_back' :src="imgUrl+'svideo/white_arrow_l.png'" mode="scaleToFill"
							@click="goBack" />
						<view class="live_header_avator" :data-authorId="item.authorDetail.authorId"
							@click="goLiveUserCenter">
							<image mode="aspectFill" class="live_header_avator_img"
								:src="item.authorDetail.memberAvatar" />
						</view>
						<view class="live_header_mem_info" :data-authorId="item.authorDetail.author_id"
							@click="goLiveUserCenter">
							<text
								class="live_header_mem_info_name">{{item.authorDetail.memberNickname?item.authorDetail.memberNickname:item.authorDetail.memberName}}</text>
							<view class="live_header_mem_info_stat_num">
								<text
									class="live_header_mem_info_stat_num_click_num">{{item.authorDetail.fansNum}}{{Ls('粉丝')}}</text>
							</view>
						</view>
						<view v-if="!hasLogin">
							<view class="live_header_live_fllow" @click="collect">
								<image class="live_header_live_fllow_image" :src="imgUrl+'svideo/fav_a.png'"></image>
								<text class="live_header_live_fllow_text">{{Ls('关注')}}</text>
							</view>
						</view>
						<view v-else>
							<view v-if="!item.videoDetail.isSelf" class="live_header_live_fllow" @click="collect"
								:style="'backgroundColor:' + (item.authorDetail.isFollow?'#999':'#fc1c1c')">
								<image class="live_header_live_fllow_image" v-if="!item.authorDetail.isFollow"
									:src="imgUrl+'svideo/fav_a.png'"></image>
								<text
									class="live_header_live_fllow_text">{{item.authorDetail.isFollow?Ls('已关注'):Ls('关注')}}</text>
							</view>
						</view>
					</view>

					<view class="right_control" v-if="item.videoDetail">
						<block v-if="item.videoDetail.state==2">
							<image class="right_control_image" @click="like"
								:src="imgUrl+(item.videoDetail.isLike == true ?'svideo/dianzan_complete.png':'svideo/dianzan.png')">
							</image>
							<text class="right_control_text">{{item.videoDetail.likeNum}}</text>
							<image class="right_control_image" :src="imgUrl+'svideo/play_comment.png'"
								@click="openCommnet"></image>
							<text
								class="right_control_text">{{(item.videoDetail.commentNum && item.videoDetail.commentNum > 0) ? item.videoDetail.commentNum : '评论'}}</text>
							<button @click="showShare" class="right_control_share_btn">
								<image class="right_control_image" :src="imgUrl+'svideo/share.png'"></image>
							</button>
							<text class="right_control_text">{{Ls('分享')}}</text>
						</block>
						<block v-if="item.videoDetail.isSelf == 1">
							<image class="right_control_image" :src="imgUrl+'svideo/del_video.png'" @click="del_video">
							</image>
							<text class="right_control_text">{{Ls('删除')}}</text>
						</block>
						<view class="personal_homepage" @click="gPensonalCenter" data-curTab="video">
							<image class="personal_homepage_personal_homepage_image" :src="item.member_avatar"></image>
						</view>
					</view>

					
					<view class="video_footer" v-if="item.videoDetail">
						<text class="video_footer_title">{{item.videoDetail.videoName}}</text>
						<text class="video_footer_desc">{{item.videoDetail.introduction}}</text>
						<view v-if="item.goodsList.length == 0" style="width:100%;height:20rpx"></view>
						<scroll-view scroll-x="true" class="video_footer_video_goods" v-if="item.goodsList.length > 0">
							<view v-for="(items, index) in item.goodsList" :key="index" class="video_footer_goods_item">
								<view class="video_footer_goods_item_goods_img" :data-goodsId="items.goodsId"
									@click="goGoodsDetail(items)">
									<image class="video_footer_goods_item_goods_img_img" :src="items.goodsImage"
										mode="aspectFill" />
								</view>
								<view class="video_footer_goods_item_goods_detail">
									<text class="video_footer_goods_item_goods_name" :data-goodsId="items.goodsId"
										@click="goGoodsDetail(items)">{{items.goodsName}}</text>
									<view class="video_footer_goods_item_goods_info">
										<text
											class="video_footer_goods_item_goods_price">{{Ls('¥')}}{{items.goodsPrice}}</text>
										<image class="video_footer_goods_item_add_cart" :data-goodsId="items.goodsId"
											@click="addCart(items)" :src="imgUrl+'svideo/add_cart.png'"></image>
									</view>
								</view>
							</view>
						</scroll-view>
					</view>

				</view>
			</swiper-item>
		</swiper>	
		
		<videoComment ref="videoComment" @updateComment="updateComment"></videoComment>
		<shareWrap ref="shareWrap"></shareWrap>
	</view>
</template>

<script>
	import {checkPageHasMore} from "@/utils/live";
	import request from "@/utils/request";
	import dataLoading from "../component/dataLoading.vue";
	import dataLoaded from "../component/dataLoaded.vue";
	import videoComment from '../component/video/videoComment.nvue'
	import shareWrap from '../component/video/shareWrap.nvue'
	import {addCartCommon} from '@/utils/live.js'
	import {getCurLanguage} from '@/utils/base.js'
	import Config from '@/utils/config.js'
	const imgUrl = process.env.VUE_APP_imgUrl;
	let cur_time = 0; //记录发送评论的时间，由于微信小程序本身input的发送时间在swiper里面会触发两次，所以时间间隔规避一下这个问题
	import {mapState,mapMutations} from 'vuex';
	let swiperTimeout = null;

	export default {
		components: {
			dataLoading,
			dataLoaded,
			videoComment,
			shareWrap
		},
		data() {
			return {
				loading: false, //数据加载状态
				imgUrl: process.env.VUE_APP_imgUrl, //图片地址
				bgStyle: 'background-size:contain;background-position:center center;background-repeat: no-repeat;', //背景图片样式

				videoDetail: '', //短视频详情
				member_avatar: '', //默认作者头像
				authorDetail: '',
				goodsList: [], //短视频绑定的商品
				commentList: [], // 评论列表
				replyInfo: '', //回复评论的数据

				firstLoading: true, //是否初次加载，是的话展示页面居中的loading效果，
				
				key: '', //登录的key值
				shareWrap: false,
				stat_end: 5, //终端类型

				video_id: '',
				label_id: '',
				author_id: '',

				wHeight: 0,
				boxStyle: {
					width: '752rpx',
					height: '750rpx'
				},

				videoList: [], //视频列表
				curVideo: 1, //当前短视频列表页洗下标
				videoMore: true, //是否有更多视频数据
				curVideoIndex: 0, //当前视频下标
				appearOld: 0, //切换时当前视频下标
				appearNew: 0, //切换后视频下标
				videoTouchEnd: false,
				isShow: false, //onshow事件开启监听
				loginState: false, //登录状态
				listMoveY: 0, //list滑动高度
				Ls: getCurLanguage ,//多语言变量在nvue里单独处理
				secondTrigger:false,
				query:{},
				replylimit:{
					currentCommentId:0
				}
			};
		},
		computed: {
			...mapState(['hasLogin', 'cartData', 'userInfo'])
		},
		onLoad(options) {
			this.loginState = this.hasLogin;
			this.getPlatform();
			this.label_id = options.curLabelId;
			this.video_id = options.video_id
			this.author_id = options.author_id
			
			let DTO = {
				video_id: this.video_id,
				author_id: this.author_id,
				videoDetail: {},
				authorDetail: {},
				goodsList: [],
				commentList: [],
				replyInfo: '',
				showComment: false,
				videoPlay: true, //视频是否播放中--暂停按钮不显示
			}

			this.videoList.push(DTO)
			this.getVideoInfo(this.video_id, 0,'init'); //获取第一个视频的详情
			this.getVideoList(this.video_id); //获取第一组视频列表数据
		},
		
		onReady() {
			this.secondTrigger = true
		},
		
		
		onHide() {
			this.$refs.sldVideo_child[this.curVideoIndex].pause()
		},
		
		onUnload() {
			this.$refs.sldVideo_child[this.curVideoIndex].pause()
		},
		
		onShow(){
			if(this.secondTrigger){
				this.$refs.sldVideo_child[this.curVideoIndex].play()
			}
		},

		methods: {
			videoChange(e) {
				// this.curVideoIndex = e.detail.current
			},
			
			videoTransition(){
				this.$refs.videoComment.closeComment()
			},


			animationfinish(e) {
				let oldIndex = this.curVideoIndex
				if (this.curVideoIndex != e.detail.current) {
					console.log('change')
					this.curVideoIndex = e.detail.current
					this.changeVideoState("pause", 'slide', oldIndex);
					if (this.videoList[oldIndex].showComment) {
						this.closeComment();
					}
					
					
					if (this.curVideoIndex == (this.videoList.length-2) && this.videoMore) {
					  this.getVideoList()
					}
				}
			},

			//获取当前终端
			getPlatform() {
				let info = uni.getSystemInfoSync();
				if (uni.getSystemInfoSync().platform == 'ios') {
					this.stat_end = 4;
					this.wHeight = info.safeArea.bottom; //获取屏幕高度
					this.boxStyle.height = info.safeArea.bottom + 'px'; //获取屏幕高度
				} else if (uni.getSystemInfoSync().platform == 'android') {
					this.stat_end = 5;
					this.wHeight = info.windowHeight;
					this.boxStyle.height = info.windowHeight + 'px';
				}
			},
			//视频播放暂停事件
			changeVideoState(type, mode, lastTarget) {
				switch (mode) {
					case 'slide': {
						try {
							this.$refs.sldVideo_child[lastTarget].pause();
							this.$refs.sldVideo_child[this.curVideoIndex].play();
						} catch (e) {
							console.log(e.message)
						}
						break
					}

					case 'tap': {
						if (type == 'play') { //当前播放
							this.$refs.sldVideo_child[this.curVideoIndex].play();
						} else if (type == 'pause') { //当前暂停
							this.$refs.sldVideo_child[this.curVideoIndex].pause();
						}
						break
					}
				}
			},

			// 获取短视频列表数据
			getVideoList() {
				let param = {}
				param.url = 'v3/video/front/video/videoList'
				param.method = 'GET'
				param.data = {}
				param.data.videoId = this.video_id
				param.data.labelId = this.label_id
				param.data.current = this.curVideo
				param.data.pageSize = 3
				request(param).then(res => {
					if (res.state == 200) {
						if (res.data.list.length > 0) {
							let arr = [];
							res.data.list.forEach((item, index) => {
								arr.push({
									video_id: item.videoId,
									author_id: item.authorId,
									videoDetail: {},
									authorDetail: {},
									goodsList: [],
									commentList: [],
									replyInfo: '',
									showComment: false,
									videoPlay: true,
								})
							})
							let len = this.videoList.length;
							this.videoList = this.videoList.concat(arr);
							res.data.list.forEach((item, index) => {
								this.getVideoInfo(item.videoId, len + index,'init')
							})
						}
						this.videoMore = checkPageHasMore(res.data.pagination); //是否还有数据
						if (this.videoMore) {
							this.curVideo++;
						}
					}
				})
			},
			//获取更多短视频列表数据
			getMoreVideo() {
				if (this.videoMore) {
					this.getVideoList();
				}
			},

			//获取短视频详情
			getVideoInfo(video_id, index, operate,author_id) {
				let param = {}
				param.data = {}
				param.url = 'v3/video/front/video/playingPage'
				param.method = 'GET'
				param.data.videoId = video_id
				request(param).then(res => {
					if (res.state == 200) {
						
						switch(operate){
							case 'init':{
								if (index) {
									this.videoList[index].goodsList = res.data.goodsList
									this.videoList[index].videoDetail = res.data.videoInfo
									this.videoList[index].member_avatar = res.data.memberAvatar
									this.videoList[index].authorDetail = res.data.authorInfo
								} else {
									this.videoList[0].goodsList = res.data.goodsList
									this.videoList[0].videoDetail = res.data.videoInfo
									this.videoList[0].member_avatar = res.data.memberAvatar
									this.videoList[0].authorDetail = res.data.authorInfo
									setTimeout(() => {
										this.$refs.sldVideo_child[0].play();
									}, 200)
								}
								
								break
							}
							
							//如果某个视频关注其作者，那么列表里其他视频信息里同一作者的关注状态更新
							case 'updateAuthor':{
								this.videoList[index].authorDetail = res.data.authorInfo
								for(let i in this.videoList){
									let {authorDetail} = this.videoList[i]
									if(authorDetail.authorId == author_id){
										this.videoList[i].authorDetail = res.data.authorInfo
									}
								}
							}
						}
					}
				})
			},

			//视频播放/暂停
			chansgedd() {
				if (this.videoList[this.curVideoIndex].videoPlay) {
					this.videoList[this.curVideoIndex].videoPlay = false;
					this.changeVideoState('pause', 'tap');
				} else {
					this.videoList[this.curVideoIndex].videoPlay = true;
					this.changeVideoState('play', 'tap');
				}
			},
			bindplay(e) {
				let index = e.target.dataset.index;
				if (this.curVideoIndex == index) {
					this.videoList[index].videoPlay = true;
				}
			},
			bindpause(e) {
				let index = e.target.dataset.index;
				if (this.curVideoIndex == index) {
					this.videoList[index].videoPlay = false;
				}
			},

			//更新视频点击量
			updateVideoClick() {
				let param = {}
				param.data = {}
				param.url = 'v3/video/front/video/updateClickNum'
				param.method = 'POST'
				param.data.videoId = this.video_id
				request(param).then(res => {
					if (res.state == 200) {}
				})
			},

			//进入作者页面
			goLiveUserCenter(e) {
				let author_id = this.videoList[this.curVideoIndex].author_id;
				let page = getCurrentPages();
				let len = page.length;
				uni.navigateTo({
					url: '/extra/user/my?author_id=' + author_id
				});
			},

			//进入直播个人中心
			gPensonalCenter(e) {
				var curTab = e.currentTarget.dataset.curtab;
				if (!this.hasLogin) {
					this.goLogin()
				} else {
					uni.navigateTo({
						url: '/extra/user/my'
					});
				}
			},

			//跳转商品详情页
			goGoodsDetail(good) {
				let page = getCurrentPages();
				let {video_id} = this.videoList[this.curVideoIndex]
				let len = page.length;
				if (len > 4) {
					uni.redirectTo({
						url: '/standard/product/detail?productId=' + good.defaultProductId + '&goodsId=' + good
							.goodsId + '&videoId=' + video_id
					});
				} else {
					uni.navigateTo({
						url: '/standard/product/detail?productId=' + good.defaultProductId + '&goodsId=' + good
							.goodsId + '&videoId=' + video_id
					});
				}
			},

			// 返回上级页面
			goBack() {
				uni.navigateBack()
			},

			//关注、取消关注事件
			collect(e) {
				let item = this.videoList[this.curVideoIndex];
				if (this.hasLogin) {
					let param = {}
					param.data = {}
					param.method = 'POST'
					param.data.authorId = item.author_id
					if (item.authorDetail.isFollow) {
						param.url = 'v3/video/front/video/cancelFollow'
					} else {
						param.url = 'v3/video/front/video/followAuthor'
					}
					request(param).then(res => {
						if (res.state == 200) {
							item.isFollow = !(item.isFollow);
							this.getVideoInfo(this.videoList[this.curVideoIndex].video_id, this.curVideoIndex,'updateAuthor',item.author_id)
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							});
						}
					})
				} else {
					this.goLogin()
				}
			},

			//加入购物车事件
			addCart(good) {
				let videoDetail = this.videoList[this.curVideoIndex].videoDetail
				if (videoDetail.state == 1) {
					uni.showToast({
						title: this.Ls('该作品正在审核中'),
						icon: 'none'
					})
					return;
				} else if (videoDetail.state == 3) {
					uni.showToast({
						title: this.Ls('该作品审核不通过'),
						icon: 'none'
					})
					return;
				}
				addCartCommon(good,this)
			},

			//点赞事件
			like() {
				let videoDetail = this.videoList[this.curVideoIndex].videoDetail;
				if (videoDetail.state != 2) {
					uni.showToast({
						title: this.Ls('该视频未审核通过,不能点赞哦～'),
						icon: 'none'
					})
					return
				}
				if (this.hasLogin) {
					let param = {}
					param.data = {}
					param.url = 'v3/video/front/video/clickPraise'
					param.method = 'POST'
					param.data.videoId = this.videoList[this.curVideoIndex].video_id
					request(param).then(res => {
						if (res.state == 200) {
							if (res.state == 200) {
								videoDetail.isLike = videoDetail.isLike == true ? false : true;
								if (videoDetail.isLike == true) {
									videoDetail.likeNum = Number(videoDetail.likeNum) + 1
								} else {
									videoDetail.likeNum = Number(videoDetail.likeNum) - 1
								}
								this.videoList[this.curVideoIndex].videoDetail = videoDetail
							} else {
								uni.showToast({
									title: res.msg,
									icon: 'none'
								});
							}
						}
					})
				} else {
					this.goLogin()
				}
			},
			
			goLogin(){
				let Route = {
					path:'/extra/svideo/videoPlayApp',
					query:{
						curLabelId:this.label_id,
						video_id:this.video_id,
						author_id:this.author_id,
					}
				}
				
				uni.showModal({
					title: '提示',
					content: '请登录',
					success: res => {
						if (res.confirm) {
							let url = Route.path;
							const query = Route.query;
							uni.setStorageSync('fromurl', {
								url,
								query
							});
							uni.navigateTo({
								url:'/pages/public/login'
							})
						}
					}
				});
			},

			//删除视频提示
			del_video() {
				uni.showModal({
					title: this.Ls('提示'),
					content: this.Ls(`确定删除该视频？`),
					confirmText: this.Ls('确定'),
					cancelText: this.Ls('取消'),
					success: res => {
						if (res.confirm) {
							this.delVideo();
						}
					}
				});
			},
			//删除视频
			delVideo() {
				let param = {}
				param.data = {}
				param.url = 'v3/video/front/video/delVideo'
				param.method = 'POST'
				param.data.videoId = this.videoList[this.curVideoIndex].video_id
				request(param).then(res => {
					uni.showToast({
						title: res.msg,
						icon: 'none'
					});
					if (res.state == 200) {
						//返回上一页
						uni.$emit('updateState')
						uni.navigateBack()
					}
				})
			},

			//分享点击事件
			showShare() {
				let {videoDetail,authorDetail,video_id} = this.videoList[this.curVideoIndex]
				this.$refs.shareWrap.injectShare({
					video_id,
					videoName:videoDetail.videoName,
					authorName:authorDetail.memberName||authorDetail.memberNickName,
					videoImage:videoDetail.videoImage
				})
			},
			
			
			//打开评论
			openCommnet() {
				if (this.videoList[this.curVideoIndex].videoDetail.state != 2) {
					uni.showToast({
						title: this.Ls('该视频未审核通过,不能评论哦～'),
						icon: 'none'
					})
					return
				}
				this.$refs.videoComment.injectComment({
					video_id:this.videoList[this.curVideoIndex].video_id,
					commentNum:this.videoList[this.curVideoIndex].videoDetail.commentNum,
					label_id:this.label_id,
					author_id:this.videoList[this.curVideoIndex].author_id,
				})
			},
			
			updateComment(num){
				this.videoList[this.curVideoIndex].videoDetail.commentNum = num
			}
		}
	};

	
</script>
<style>
	page {
		background: transparent;
	}

	.swiper {
		height: 100vh;
	}

	.wrap {
		position: relative;
		background-color: #555555;
		width: 750rpx;
		overflow: hidden;
	}

	.live_pause {
		position: absolute;
		/* top: 214rpx;
		left: 0;
		right: 130rpx;
		bottom: 318rpx; */
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 1;
		overflow: hidden;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.live_pause_img {
		/* position: relative;
		left: 65rpx; */
		width: 100rpx;
		height: 115rpx;
	}

	.live_header {
		position: absolute;
		top: 120rpx;
		left: 20rpx;
		z-index: 99;
		height: 80rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: flex-start;
	}

	.live_header_go_back {
		width: 45rpx;
		height: 47rpx;
	}

	.live_header_avator {
		width: 80rpx;
		height: 80rpx;
		border-radius: 40rpx;
		border-width: 2rpx;
		border-style: solid;
		border-color: #fff;
		margin-left: 12rpx;
		overflow: hidden;
	}

	.live_header_avator_img {
		width: 76rpx;
		height: 76rpx;
		border-radius: 38rpx;
		overflow: hidden;
	}

	.live_header_mem_info {
		margin-left: 20rpx;
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		justify-content: space-between;
	}

	.live_header_live_fllow {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		width: 120rpx;
		height: 50rpx;
		background-color: #fc1c1c;
		border-radius: 25rpx;
		margin-left: 25rpx;
	}

	.live_header_live_fllow_image {
		width: 46rpx;
		height: 46rpx;
	}

	.live_header_live_fllow_text {
		color: #fff;
		font-size: 24rpx;
		margin-right: 8rpx;
	}

	.live_header_mem_info_name {
		max-width: 200rpx;
		color: #fff;
		font-size: 28rpx;
		line-height: 32rpx;
		margin-bottom: 15rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		lines: 1;
		font-weight: 600;
		display: -webkit-box;
	}

	.live_header_mem_info_stat_num {
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
	}

	.live_header_mem_info_stat_num_click_num {
		color: #fff;
		line-height: 36rpx;
		font-size: 22rpx;
		font-weight: 600;
		white-space: nowrap;
		lines: 1;
		margin-right: 20rpx;
	}

	.live_back {
		width: 752rpx;
		margin-left: -2rpx;
	}
	
	
	
	
	.right_control {
		position: absolute;
		bottom: 310rpx;
		right: 30rpx;
		z-index: 99;
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: center;
	}

	.right_control_image {
		width: 68rpx;
		height: 68rpx;
		margin-bottom: 13rpx;
	}

	.right_control_text {
		width: 68rpx;
		text-align: center;
		color: #fff;
		font-size: 24rpx;
		margin-bottom: 36rpx;
		white-space: nowrap;
		lines: 1;
	}

	.right_control_share_btn {
		background-color: transparent;
		border: none;
		color: transparent;
		line-height: 0;
		border-color: rgba(0, 0, 0, 0);
	}

	.right_control_share_btn::after {
		border: none !important;
	}

	/* 进入个人主页按钮入口 */
	.personal_homepage {
		width: 66rpx;
		height: 66rpx;
		border-radius: 50%;
		border-width: 2rpx;
		border-style: solid;
		border-color: #fff;
		z-index: 99;
		display: flex;
		justify-content: center;
		align-items: center;
		background-color: #ffffff;
	}

	.personal_homepage_personal_homepage_image {
		width: 66rpx;
		height: 66rpx;
		border-radius: 50%;
		margin-bottom: 0;
	}

	.video_footer {
		position: absolute;
		left: 30rpx;
		right: 0;
		bottom: 37rpx;
		z-index: 2;
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		justify-content: flex-end;
	}

	.video_footer_title {
		color: #fff;
		font-size: 32rpx;
		font-weight: 600;
		margin-bottom: 10rpx;
	}

	.video_footer_desc {
		color: #fff;
		font-size: 26rpx;
		line-height: 40rpx;
		margin-bottom: 18rpx;
		width: 570rpx;
	}

	.video_footer_video_goods {
		width: 750rpx;
		height: 172rpx;
		white-space: nowrap;
		lines: 1;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
	}

	.video_footer_goods_item {
		width: 410rpx;
		height: 172rpx;
		display: inline-block;
		border-radius: 15rpx;
		flex-direction: row;
		justify-content: flex-start;
		align-items: flex-start;
		margin-right: 30rpx;
		clear: both;
		overflow: hidden;
	}

	.video_footer_goods_item_goods_img {
		width: 172rpx;
		height: 172rpx;
		background-color: #fff;
		border-top-left-radius: 15rpx;
		border-top-right-radius: 0;
		border-bottom-right-radius: 0;
		border-bottom-left-radius: 15rpx;
		float: left;
	}

	.video_footer_goods_item_goods_img_img {
		width: 172rpx;
		height: 172rpx;
		background-color: #fff;
		border-top-left-radius: 15rpx;
		border-top-right-radius: 0;
		border-bottom-right-radius: 0;
		border-bottom-left-radius: 15rpx;
		float: left;
	}

	.video_footer_goods_item_goods_detail {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: flex-start;
		flex: 1;
		background-color: #222;
		height: 142rpx;
		border-top-left-radius: 0;
		border-top-right-radius: 15rpx;
		border-bottom-right-radius: 15rpx;
		border-bottom-left-radius: 0;
		padding: 15rpx 20rpx;
		height: 172rpx;
	}

	.video_footer_goods_item_goods_name {
		width: 200rpx;
		color: #fff;
		font-size: 24rpx;
		line-height: 36rpx;
		height: 72rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		word-break: break-word;
		white-space: normal;
	}

	.video_footer_goods_item_goods_info {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		width: 200rpx;
	}

	.video_footer_goods_item_goods_price {
		color: #fff;
		font-size: 28rpx;
		letter-spacing: 2rpx;
	}

	.video_footer_goods_item_add_cart {
		width: 48rpx;
		height: 48rpx;
	}

</style>