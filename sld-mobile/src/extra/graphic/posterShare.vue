<template>
	<view class="poster-share-container">
		<!-- 只保留返回按钮 -->
		<!-- #ifdef H5 -->
		<view class="custom-nav-h5">
			<view class="nav-left" @click="goBack">
				<text class="back_icon iconfont iconziyuan2"></text>
			</view>
		</view>
		<!-- #endif -->
		
		<!-- #ifdef MP-WEIXIN -->
		<!-- 小程序使用原生导航栏，这里不需要自定义导航栏 -->
		<!-- #endif -->
		
		<!-- 海报卡片 -->
		<view class="page-content" v-if="dataLoaded">
			<!-- 添加html2canvas渲染组件 -->
			<sp-html2canvas-render domId="poster-card-container" ref="renderRef" @renderOver="renderOver"></sp-html2canvas-render>
			
			<view class="poster-card" id="poster-card-container" @click="handlePosterCardClick">
				<!-- 添加长按提示浮层 -->
				<view class="save-tip" v-if="showSaveTip">
					<view class="save-tip-content">
						<text>{{$L('点击保存海报到相册')}}</text>
					</view>
				</view>
				<!-- 卡片头部图片和日期 -->
				<view class="card-header-container">
					<image class="card-header" :src="headerImgUrl" mode="widthFix"></image>
					<view class="card-date">
						<text class="date-label">{{$L('日期')}}</text>
						<text class="date-value">{{formatDate}}</text>
					</view>
				</view>
				
				<!-- 用户信息区域 -->
				<view class="user-info">
					<image class="avatar" :src="memberInfo.memberAvatar || defaultAvatar" mode="aspectFill" @error="handleAvatarError"></image>
					<view class="user-details">
						<text class="nickname">{{memberInfo.memberNickName || memberInfo.memberName || '用户'}}</text>
						<text class="fans" v-if="memberInfo.fansNum">{{memberInfo.fansNum}}{{$L('粉丝')}}</text>
					</view>
				</view>
				
				<!-- 文章内容区域 -->
				<view class="content-text">
					<text class="content-title">{{videoDetail.videoName}}</text>
					<text class="content-introduction">{{videoDetail.introduction}}</text>
					<text class="content-body" v-if="videoDetail.videoContent">{{videoContent}}</text>
				</view>
				
				<!-- 图片预览区域 -->
				<view class="content-images" :class="'images-count-' + imagesCount">
					<!-- 单张图片 -->
					<template v-if="imagesCount === 1">
						<image class="single-image" :src="videoImages[0]" mode="aspectFill"></image>
					</template>
					
					<!-- 两张图片 -->
					<template v-else-if="imagesCount === 2">
						<view class="image-item" v-for="(item, index) in videoImages" :key="index">
							<image :src="item" mode="aspectFill"></image>
						</view>
					</template>
					
					<!-- 三张图片 -->
					<template v-else-if="imagesCount === 3">
						<view class="image-item" v-for="(item, index) in videoImages" :key="index">
							<image :src="item" mode="aspectFill"></image>
						</view>
					</template>
					
					<!-- 四张图片 -->
					<template v-else-if="imagesCount === 4">
						<view class="image-grid">
							<view class="image-item" v-for="(item, index) in videoImages" :key="index">
								<image :src="item" mode="aspectFill"></image>
							</view>
						</view>
					</template>
					
					<!-- 5-9张图片 -->
					<template v-else>
						<view class="image-grid">
							<view class="image-item" v-for="(item, index) in videoImages.slice(0, 9)" :key="index">
								<image :src="item" mode="aspectFill"></image>
							</view>
							<view class="image-more" v-if="imagesCount > 9">+{{imagesCount - 9}}</view>
						</view>
					</template>
				</view>
				
				<!-- 二维码区域（左右结构） -->
				<view class="qrcode-section">
					<view class="qrcode-left">
						<image 
							class="qrcode" 
							:src="qrCodeUrl" 
							mode="aspectFill"
							@error="onQrCodeError"
						></image>
					</view>
					<view class="qrcode-right">
						<text class="qrcode-tip" style="font-weight: 700;">{{$L('如何查看精彩内容?')}}</text>
						<text class="qrcode-subtip">{{$L('长按/扫描二维码解锁详情')}}</text>
					</view>
				</view>
			</view>
			

		</view>
		
		<!-- 加载中提示 -->
		<view class="loading-container" v-else>
			<view class="loading-text">{{$L('海报生成中...')}}</view>
		</view>
		
		<!-- 分享菜单 -->
		<view class="share-options">
			<view class="share-grid">
				<!-- #ifdef MP-WEIXIN -->
				<!-- 微信小程序环境下显示所有三个按钮 -->
				<!-- 微信好友和群聊分享按钮 -->
				<button 
					class="share-option" 
					v-for="(option, index) in shareOptions" 
					:key="index"
					:open-type="option.type === 'WECHAT_FRIEND' || option.type === 'WECHAT_GROUP' ? 'share' : ''"
					:data-type="option.type"
					@click="handleShareOptionClick(option.type)"
				>
					<image class="share-icon" :src="option.icon" mode="aspectFill"></image>
					<text class="share-text">{{option.text}}</text>
				</button>
				<!-- #endif -->
				
				<!-- #ifdef H5 -->
				<!-- H5环境下只显示下载海报按钮 -->
				<view 
					class="share-option h5-center-button" 
					@click="savePoster"
				>
					<image 
						class="share-icon" 
						:src="shareOptions.find(opt => opt.type === 'DOWNLOAD').icon" 
							mode="aspectFill"
					></image>
					<text class="share-text">{{shareOptions.find(opt => opt.type === 'DOWNLOAD').text}}</text>
				</view>
				<!-- #endif -->
				
				<!-- #ifdef APP-PLUS -->
				<!-- APP环境下显示所有按钮 -->
				<view 
					class="share-option" 
					v-for="(option, index) in shareOptions" 
					:key="index" 
					@click="handleShareOptionClick(option.type)"
				>
					<image class="share-icon" :src="option.icon" mode="aspectFill"></image>
					<text class="share-text">{{option.text}}</text>
				</view>
				<!-- #endif -->
			</view>
		</view>
		
		<!-- 引入Painter组件 -->
		<!-- #ifdef MP-WEIXIN -->
		<view style="position: absolute; top: -9999px; left: -9999px;">
			<painter 
				:palette="painterPalette" 
				@imgOK="onPainterImageOK" 
				@imgErr="onPainterImageError"
				:dirty="true"
				:dancePalette="dancePaletteData"
				:customStyle="'width: 600px; height: 1000px;'"
				:widthPixels="750"
				:use2D="true"
				:LRU="false"
				:imgHost="imgHost"
			/>
		</view>
		<!-- #endif -->
		
		<!-- H5环境下使用普通canvas替代 -->
		<!-- #ifdef H5 -->
		<view style="position: absolute; top: -9999px; left: -9999px;">
			<canvas 
				id="posterCanvas"
				canvas-id="posterCanvas"
				:style="{width: canvasWidth + 'px', height: canvasHeight + 'px'}"
			></canvas>
		</view>
		<!-- #endif -->
		
		<!-- H5环境下的海报预览模态窗口，改为全屏样式 -->
		<!-- #ifdef H5 -->
		<view class="poster-fullscreen-preview" v-if="previewVisible" @click.self="closePreview">
			<view class="preview-tip">{{$L('长按图片保存到相册')}}</view>
			<image 
				class="preview-image" 
				:src="previewImageUrl" 
				mode="widthFix" 
				show-menu-by-longpress
			></image>
			<view class="preview-close-btn" @click="closePreview">×</view>
		</view>
		<!-- #endif -->
	</view>
</template>

<script>
import html2canvas from 'html2canvas';
// 不再直接使用uqrcode.js，改用静态API生成二维码
// import uqrcode from '../../components/sld_poster/uqrcode.js'
// 导入base64ToPath工具函数
import { base64ToPath } from "@/uni_modules/sp-html2canvas-render/utils/index.js";

export default {
	data() {
		return {
			imgUrl: process.env.VUE_APP_IMG_URL,
			headerImgUrl: 'https://sg-mall-prod.oss-cn-shanghai.aliyuncs.com/java/bbc/mobile/graphic/card-header.png',
			videoId: '',
			videoDetail: {},
			authorDetail: {},
			memberInfo: {}, // 当前用户信息
			qrCodeUrl: '',
			videoImages: [], // 存储所有图片
			imagesCount: 0, // 图片数量
			dataLoaded: false,
			currentDate: new Date(),
			shareSource: '', // 当前选择的分享渠道
			shareOptions: [
				{
					icon: 'https://sg-mall-prod.oss-cn-shanghai.aliyuncs.com/java/bbc/mobile/graphic/icon-share-wechat.png',
					text: '微信好友',
					type: 'WECHAT_FRIEND',
					channel: 'wechat_friend'
				},
				{
					icon: 'https://sg-mall-prod.oss-cn-shanghai.aliyuncs.com/java/bbc/mobile/graphic/icon-share-group.png',
					text: '分享群聊',
					type: 'WECHAT_GROUP',
					channel: 'wechat_group'
				},
				{
					icon: 'https://sg-mall-prod.oss-cn-shanghai.aliyuncs.com/java/bbc/mobile/graphic/icon-share-download.png',
					text: '下载海报',
					type: 'DOWNLOAD',
					channel: 'download'
				}
			],
			// 共享选项显示条件
			showShareOptions: {
				MP_WEIXIN: ['WECHAT_FRIEND', 'WECHAT_GROUP', 'DOWNLOAD'],
				H5: ['DOWNLOAD'],
				APP_PLUS: ['WECHAT_FRIEND', 'WECHAT_GROUP', 'DOWNLOAD']
			},
			isWeixinMP: false,
			canvasWidth: 600,
			canvasHeight: 1000,
			// Painter 相关数据
			painterPalette: {}, // Painter画板配置
			painterImagePath: '', // 生成的图片路径
			posterLoading: false, // 海报生成中
			dancePaletteData: {},  // 动态更新画板数据
			showSaveTip: false, // 点击保存提示
			canShowImagePreview: false, // 是否显示图片预览
			h5ImageBase64: '', // H5环境下的图片base64
			imgHost: 'https://sg-mall-prod.oss-cn-shanghai.aliyuncs.com/', // 图片域名前缀
			waitingForPainter: false,  // 是否正在等待Painter完成
			defaultQrCodeUrl: 'https://m-test.shengu.shop/static/images/default-qr.png', // 默认二维码URL
			h5Poster: false, // 标记是否是H5环境的海报
			defaultAvatar: 'https://sg-mall-prod.oss-cn-shanghai.aliyuncs.com/java/bbc/mobile/graphic/default-avatar.png', // 添加默认头像
			previewVisible: false,
			previewImageUrl: '',
		}
	},
	computed: {
		// 格式化日期，例如 2025/03/17
		formatDate() {
			const date = this.currentDate
			const year = date.getFullYear()
			const month = String(date.getMonth() + 1).padStart(2, '0')
			const day = String(date.getDate()).padStart(2, '0')
			return `${year}/${month}/${day}`
		},
		// 处理内容显示，超出部分显示...
		videoContent() {
			if (!this.videoDetail.videoContent) return ''
			const content = this.videoDetail.videoContent
			if (content.length > 100) {
				return content.substring(0, 100) + '...'
			}
			return content
		}
	},
	onLoad(options) {
		// 获取参数
		this.videoId = options.video_id || ''
		
		// #ifdef H5
		// 在H5环境下，确保分享渠道为download
		this.shareSource = 'DOWNLOAD'
		// #endif
		
		// 获取视频详情数据
		this.getVideoInfo()
		// 获取用户信息
		this.getMemberInfo()
	},
	mounted() {
		// 判断是否是微信小程序环境
		// #ifdef MP-WEIXIN
		this.isWeixinMP = true;
		// #endif
		
		// 获取设备信息以设置canvas尺寸
		const systemInfo = uni.getSystemInfoSync();
		const scale = systemInfo.windowWidth / 750; // 计算缩放比例
		this.canvasWidth = systemInfo.windowWidth;
		this.canvasHeight = 1334 * scale;
		
		// 添加调试信息
		console.log('memberInfo:', JSON.stringify(this.memberInfo));
		console.log('Avatar URL:', this.memberInfo.memberAvatar);
		
		// 检查头像URL是否可访问
		if(this.memberInfo && this.memberInfo.memberAvatar) {
			console.log('检查头像URL可访问性...');
			const img = new Image();
			img.onload = () => console.log('头像URL可以正常加载');
			img.onerror = () => console.error('头像URL无法加载');
			img.src = this.memberInfo.memberAvatar;
		}
	},
	onUnload() {
		// 确保页面卸载时关闭所有loading
		try {
			uni.hideLoading();
		} catch (e) {
			console.error('卸载页面时关闭loading失败:', e);
		}
		
		// 重置状态
		this.posterLoading = false;
		this.waitingForPainter = false;
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack({
				delta: 1
			})
		},
		
		// Base64编码函数
		encodeBase64(str) {
			try {
				// 确保str是字符串类型
				str = String(str);
				
				// #ifdef H5
				// H5环境下使用window.btoa
				return window.btoa(encodeURIComponent(str));
				// #endif
				
				// #ifndef H5
				// 小程序环境下使用简单实现
				// 先尝试使用uni API
				try {
					// 对中文进行特殊处理
					const utf8Bytes = encodeURIComponent(str)
						.replace(/%([0-9A-F]{2})/g, (match, p1) => {
							return String.fromCharCode('0x' + p1);
						});
					
					// 使用uni API进行base64编码
					return uni.arrayBufferToBase64(new Uint8Array([...utf8Bytes].map(char => char.charCodeAt(0))));
				} catch (innerError) {
					// 如果API调用失败，使用备选方案
					console.error('使用uni API编码失败，使用备选方案:', innerError);
					
					// Base64编码表
					const BASE64_CHARS = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
					
					// 转换为UTF-8字节数组
					const bytes = [];
					for (let i = 0; i < str.length; i++) {
						const charCode = str.charCodeAt(i);
						if (charCode < 0x80) {
							bytes.push(charCode);
						} else if (charCode < 0x800) {
							bytes.push(0xc0 | (charCode >> 6), 
										0x80 | (charCode & 0x3f));
						} else if (charCode < 0xd800 || charCode >= 0xe000) {
							bytes.push(0xe0 | (charCode >> 12), 
										0x80 | ((charCode >> 6) & 0x3f), 
										0x80 | (charCode & 0x3f));
						} else {
							// 处理UTF-16代理对
							i++;
							const nextCharCode = str.charCodeAt(i);
							const codePoint = (((charCode & 0x3ff) << 10) | (nextCharCode & 0x3ff)) + 0x10000;
							bytes.push(0xf0 | (codePoint >> 18),
										0x80 | ((codePoint >> 12) & 0x3f),
										0x80 | ((codePoint >> 6) & 0x3f),
										0x80 | (codePoint & 0x3f));
						}
					}
					
					// Base64编码
					let base64 = '';
					const len = bytes.length;
					for (let i = 0; i < len; i += 3) {
						const b1 = bytes[i];
						const b2 = i + 1 < len ? bytes[i + 1] : 0;
						const b3 = i + 2 < len ? bytes[i + 2] : 0;
						
						const triplet = (b1 << 16) | (b2 << 8) | b3;
						
						base64 += BASE64_CHARS[(triplet >> 18) & 0x3F];
						base64 += BASE64_CHARS[(triplet >> 12) & 0x3F];
						base64 += i + 1 < len ? BASE64_CHARS[(triplet >> 6) & 0x3F] : '=';
						base64 += i + 2 < len ? BASE64_CHARS[triplet & 0x3F] : '=';
					}
					
					return base64;
				}
				// #endif
			} catch (e) {
				console.error('Base64编码失败:', e);
				// 如果编码失败，返回原始字符串
				return str;
			}
		},
		
		// 获取用户信息
		getMemberInfo() {
			let param = {}
			param.data = {}
			param.url = 'v3/member/front/member/memberInfo'
			param.method = 'GET'
			
			this.$request(param).then((res) => {
				if (res.state == 200 && res.data) {
					this.memberInfo = res.data
				} else {
					console.error('获取用户信息失败', res)
				}
			}).catch(err => {
				console.error('获取用户信息失败', err)
			})
			
			// 在获取数据后添加头像处理
			// 确保头像URL是完整的URL
			if(this.memberInfo && this.memberInfo.memberAvatar) {
				// 检查URL是否以http开头，如不是则添加域名
				if(!this.memberInfo.memberAvatar.startsWith('http')) {
					this.memberInfo.memberAvatar = this.imgHost + this.memberInfo.memberAvatar;
				}
				
				// 尝试预加载头像
				const img = new Image();
				img.src = this.memberInfo.memberAvatar;
			}
		},
		
		// 获取视频详情
		getVideoInfo() {
			if (!this.videoId) {
				this.$api.msg(this.$L('参数错误'))
				setTimeout(() => {
					this.goBack()
				}, 1500)
				return
			}
			
			let param = {}
			param.data = {}
			param.url = 'v3/video/front/video/playingPage'
			param.method = 'GET'
			param.data.videoId = this.videoId
			
			this.$request(param).then((res) => {
				if (res.state == 200) {
					this.videoDetail = res.data.videoInfo
					this.authorDetail = res.data.authorInfo
					
					// 处理视频图片
					if (this.videoDetail.videoImage && this.videoDetail.videoImage.length > 0) {
						this.videoImages = this.videoDetail.videoImage
						this.imagesCount = this.videoImages.length
					}
					
					// 获取二维码
					this.generateQrCode()
				} else {
					this.$api.msg(res.msg || this.$L('获取数据失败'))
					setTimeout(() => {
						this.goBack()
					}, 1500)
				}
			}).catch(err => {
				console.error('获取视频详情失败', err)
				this.$api.msg(this.$L('获取数据失败'))
				setTimeout(() => {
					this.goBack()
				}, 1500)
			})
		},
		
		// 生成分享二维码
		generateQrCode(channel = '') {
			// 获取全局API基础URL
			const baseUrl = 'https://m-test.shengu.shop/';
			
			// 在H5环境下强制设置channel为download
			// #ifdef H5
			channel = 'download';
			// 仅保存渠道信息，但不自动处理下载行为
			this.shareSource = channel;
			// #endif
			
			// #ifndef H5
			// 在非H5环境下设置分享渠道
			this.shareSource = channel || this.shareSource;
			// #endif
			
			// 获取token（统一获取，避免在条件编译中重复声明）
			const token = uni.getStorageSync('token');
			
			// u参数使用base64编码
			const uParam = this.encodeBase64("3");
			
			// #ifdef MP-WEIXIN
			// 小程序环境下使用太阳码接口
			// 构建请求参数，与curl命令完全一致
			const requestData = {
				page: 'extra/graphic/graphicDetail',
				params: {
					video_id: parseInt(this.videoId),
					author_id: parseInt(this.authorDetail.id || 1),
					curLabelId: 1,
					index: 0,
					u: uParam
				}
			};
			
			// 如果channel有值，添加到params中
			if (channel) {
				requestData.params.c = channel;
			}
			
			console.log('小程序二维码请求参数:', JSON.stringify(requestData));
			
			// 尝试直接使用uni.request请求，完全匹配curl命令的请求头
			uni.request({
				url: baseUrl + 'v3/goods/common/generateSunCode',
				method: 'POST',
				header: {
					'authority': 'm-test.shengu.shop',
					'content-type': 'application/json',
					'language': 'zh',
					'terminal-source': '2',  // 使用字符串类型
					'authorization': token ? 'Bearer ' + token : ''
				},
				data: requestData,
				success: (res) => {
					console.log('太阳码原始响应状态码:', res.statusCode);
					console.log('太阳码原始响应数据:', res.data);
					
					if (res.statusCode === 200 && res.data && res.data.data) {
						// 检查是否已经包含前缀，如果没有则添加
						let imageData = res.data.data;
						if (imageData && !imageData.startsWith('data:image')) {
							// 打印base64前缀，以便确认格式
							console.log('Base64前缀:', imageData.substring(0, 20) + '...');
							imageData = 'data:image/png;base64,' + imageData;
						}
						this.qrCodeUrl = imageData;
						console.log('获取二维码成功，长度:', imageData.length);
						
						// 处理特定渠道的后续操作
						if (channel && this.shareSource) {
							this.processChannelAction(channel);
						}
					} else {
						// 如果无法获取二维码，使用默认图片
						this.qrCodeUrl = this.defaultQrCodeUrl;
						console.error('获取二维码失败', res.data || res);
					}
					
					this.dataLoaded = true;
					
					// 数据加载完成后，准备Painter的数据
					this.$nextTick(() => {
						this.preparePainterData();
					});
				},
				fail: (err) => {
					console.error('请求失败:', err);
					this.qrCodeUrl = this.defaultQrCodeUrl;
					this.dataLoaded = true;
				}
			});
			// #endif
			
			// #ifdef H5
			// H5环境下使用静态API服务生成二维码，避免uqrcode问题
			try {
				// 构建跳转URL，使用base64编码的u参数
				const h5Url = `${window.location.origin}/pages/extra/graphic/graphicDetail?video_id=${this.videoId}&author_id=${this.authorDetail.id || 1}&curLabelId=1&index=0&u=${uParam}&c=download`;
				console.log('H5二维码URL:', h5Url);
				
				// 直接使用静态API生成二维码，跳过uqrcode
				this.generateStaticQRCode(h5Url);
			} catch (error) {
				console.error('生成二维码失败:', error);
				// 使用默认图片
				this.qrCodeUrl = this.defaultQrCodeUrl;
				this.dataLoaded = true;
			}
			// #endif
		},
		
		// 生成静态二维码
		generateStaticQRCode(url) {
			if (!url || typeof url !== 'string') {
				console.error('无效的URL:', url);
				this.qrCodeUrl = this.defaultQrCodeUrl;
				this.dataLoaded = true;
				return;
			}
			
			// 获取编码后的URL
			const encodedUrl = encodeURIComponent(url);
			
			// 使用外部二维码生成服务
			const qrApiUrl = `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodedUrl}`;
			console.log('使用静态API生成二维码:', qrApiUrl);
			
			// #ifdef H5
			// 在H5环境中，我们可以使用Image对象直接加载QR码图片
			try {
				const img = new Image();
				img.crossOrigin = 'Anonymous'; // 允许跨域
				img.onload = () => {
					console.log('静态QR码加载成功');
					// 图片加载成功后，创建canvas将其转换为base64
					const canvas = document.createElement('canvas');
					canvas.width = img.width;
					canvas.height = img.height;
					const ctx = canvas.getContext('2d');
					ctx.drawImage(img, 0, 0);
					
					try {
						// 将canvas转换为base64
						const base64 = canvas.toDataURL('image/png');
						this.qrCodeUrl = base64;
						console.log('静态QR码API生成成功');
						this.dataLoaded = true;
						
						// 数据加载完成后，准备海报数据
						this.$nextTick(() => {
							this.preparePainterData();
						});
						
						// 只处理渠道操作，但不自动下载
						if (this.shareSource) {
							// 只触发非下载渠道的后续操作
							if (this.shareSource !== 'DOWNLOAD') {
								this.processChannelAction(this.shareSource);
							}
						}
					} catch (error) {
						console.error('转换QR码为base64失败，使用默认图片:', error);
						this.qrCodeUrl = this.defaultQrCodeUrl;
						this.dataLoaded = true;
					}
				};
				
				img.onerror = (e) => {
					console.error('加载静态QR码图片失败，使用默认图片', e);
					this.qrCodeUrl = this.defaultQrCodeUrl;
					this.dataLoaded = true;
					
					// 处理特定渠道的后续操作
					if (this.shareSource && this.shareSource !== 'DOWNLOAD') {
						this.processChannelAction(this.shareSource);
					}
				};
				
				// 加载QR码图片
				img.src = qrApiUrl;
			} catch (error) {
				console.error('使用静态QR码API失败，使用默认图片:', error);
				this.qrCodeUrl = this.defaultQrCodeUrl;
				this.dataLoaded = true;
				
				// 处理特定渠道的后续操作
				if (this.shareSource && this.shareSource !== 'DOWNLOAD') {
					this.processChannelAction(this.shareSource);
				}
			}
			// #endif
			
			// #ifndef H5
			// 非H5环境，使用默认二维码或通过网络请求获取图片
			// 为简化实现，直接使用默认二维码
			this.qrCodeUrl = this.defaultQrCodeUrl;
			this.dataLoaded = true;
			// #endif
		},
		
		// 数据加载完成后，准备Painter的数据
		preparePainterData() {
			// 如果在H5环境，使用不同的处理方式
			// #ifdef H5
			this.prepareH5PosterData();
			return;
			// #endif
			
			const systemInfo = uni.getSystemInfoSync();
			const screenWidth = systemInfo.screenWidth;
			const scale = screenWidth / 750; // 计算缩放比例
			
			// 构建Painter的配置对象
			const paletteData = {
				width: '750px',
				height: '1500px', // 增加高度，确保内容完整显示
				background: '#ffffff',
				views: [
					// 背景区域 - 整体白色背景带圆角
					{
						type: 'rect',
						css: {
							width: '750px',
							height: '1500px',
							top: '0px',
							left: '0px',
							color: '#ffffff',
							borderRadius: '20px' // 添加整体圆角
						}
					},
					// 头部图片 - 使用本地图片而非远程图片提高成功率
					{
						type: 'image',
						url: this.headerImgUrl,
						css: {
							width: '750px',
							height: '300px',
							top: '0px',
							left: '0px',
							mode: 'aspectFill', // 确保图片填充满整个区域
							borderRadius: '20px 20px 0 0' // 只有顶部有圆角
						}
					},
					// 日期标签
					{
						type: 'text',
						text: this.$L('日期'),
						css: {
							right: '30px',
							top: '40px',
							fontSize: '24px',
							color: '#ffffff',
							textAlign: 'right'
						}
					},
					// 日期值
					{
						type: 'text',
						text: this.formatDate,
						css: {
							right: '30px',
							top: '70px',
							fontSize: '28px',
							color: '#ffffff',
							textAlign: 'right'
						}
					}
				]
			};
			
			// 头像 - 确保图片是绝对路径或已有前缀
			const avatarUrl = this.ensureImageUrl(this.memberInfo.memberAvatar || this.imgUrl + 'graphic/default_avatar.png');
			paletteData.views.push({
				type: 'image',
				url: avatarUrl,
				css: {
					width: '80px',
					height: '80px',
					top: '320px',
					left: '30px',
					borderRadius: '40px',
					mode: 'aspectFill'
				}
			});
			
			// 用户名
			paletteData.views.push({
				type: 'text',
				text: this.memberInfo.memberNickName || this.memberInfo.memberName || '',
				css: {
					left: '130px',
					top: '330px',
					fontSize: '32px',
					color: '#333333',
					fontWeight: 'bold',
					maxLines: 1,
					width: '300px'
				}
			});
			
			// 添加粉丝数
			if (this.memberInfo.fansNum) {
				paletteData.views.push({
					type: 'text',
					text: `${this.memberInfo.fansNum}${this.$L('粉丝')}`,
					css: {
						left: '130px',
						top: '370px',
						fontSize: '24px',
						color: '#999999'
					}
				});
			}
			
			// 添加标题（确保最多两行）
			if (this.videoDetail.videoName) {
				paletteData.views.push({
					type: 'text',
					text: this.videoDetail.videoName,
					css: {
						left: '30px',
						top: '420px',
						width: '690px',
						maxLines: 2,
						fontSize: '36px',
						lineHeight: '50px',
						color: '#333333',
						fontWeight: 'bold'
					}
				});
			}
			
			// 动态计算下一个元素的位置
			let nextY = 420;
			nextY += this.videoDetail.videoName ? (this.videoDetail.videoName.length > 20 ? 100 : 50) : 0;
			
			// 添加简介
			if (this.videoDetail.introduction) {
				paletteData.views.push({
					type: 'text',
					text: this.videoDetail.introduction,
					css: {
						left: '30px',
						top: `${nextY}px`,
						width: '690px',
						maxLines: 3,
						fontSize: '28px',
						lineHeight: '40px',
						color: '#666666'
					}
				});
				
				// 更新下一个元素位置
				nextY += Math.min(this.videoDetail.introduction.length, 100) / 20 * 40 + 20;
			}
			
			// 添加正文内容
			if (this.videoContent) {
				paletteData.views.push({
					type: 'text',
					text: this.videoContent,
					css: {
						left: '30px',
						top: `${nextY}px`,
						width: '690px',
						maxLines: 2,
						fontSize: '26px',
						lineHeight: '36px',
						color: '#999999'
					}
				});
				
				// 更新下一个元素位置
				nextY += Math.min(this.videoContent.length, 100) / 25 * 36 + 30;
			}
			
			// 添加内容图片（根据图片数量处理，确保所有图片URL都是完整的）
			if (this.imagesCount > 0 && this.videoImages.length > 0) {
				// 改为支持最多9张图片显示
				const maxImages = Math.min(this.imagesCount, 9);
				
				if (maxImages === 1) {
					// 单张图片
					const imgUrl = this.ensureImageUrl(this.videoImages[0]);
					paletteData.views.push({
						type: 'image',
						url: imgUrl,
						css: {
							width: '690px',
							height: '400px',
							top: `${nextY}px`,
							left: '30px',
							borderRadius: '20rpx',
							mode: 'aspectFill'
						}
					});
					nextY += 420;
				} else if (maxImages <= 3) {
					// 2-3张图片
					const imgWidth = (690 - (maxImages - 1) * 10) / maxImages;
					this.videoImages.slice(0, maxImages).forEach((img, index) => {
						const imgUrl = this.ensureImageUrl(img);
						paletteData.views.push({
							type: 'image',
							url: imgUrl,
							css: {
								width: `${imgWidth}px`,
								height: '250px',
								top: `${nextY}px`,
								left: `${30 + index * (imgWidth + 10)}px`,
								borderRadius: '20rpx',
								mode: 'aspectFill'
							}
						});
					});
					nextY += 270;
				} else {
					// 4-9张图片，按3列网格排列
					const imgWidth = (690 - 20) / 3; // 3列，每列之间间距10px
					const imgHeight = imgWidth; // 保持正方形
					const rows = Math.ceil(maxImages / 3); // 计算行数
					
					this.videoImages.slice(0, maxImages).forEach((img, index) => {
						const row = Math.floor(index / 3);
						const col = index % 3;
						const imgUrl = this.ensureImageUrl(img);
						
						paletteData.views.push({
							type: 'image',
							url: imgUrl,
							css: {
								width: `${imgWidth}px`,
								height: `${imgHeight}px`,
								top: `${nextY + row * (imgHeight + 10)}px`, // 行间距10px
								left: `${30 + col * (imgWidth + 10)}px`, // 列间距10px
								borderRadius: '20rpx',
								mode: 'aspectFill'
							}
						});
					});
					nextY += rows * (imgHeight + 10) - 10; // 减去最后一行的间距
				}
			}
			
			// 添加分隔线
			paletteData.views.push({
				type: 'rect',
				css: {
					width: '690px',
					height: '1px',
					top: `${nextY}px`,
					left: '30px',
					color: '#f5f5f5'
				}
			});
			
			nextY += 20;
			
			// 添加二维码 - 确保图片URL是完整的
			const qrCodeUrl = this.ensureImageUrl(this.qrCodeUrl);
			paletteData.views.push({
				type: 'image',
				url: qrCodeUrl,
				css: {
					width: '160px',
					height: '160px',
					top: `${nextY}px`,
					left: '30px',
					borderRadius: '5px'
				}
			});
			
			// 添加二维码说明文字
			paletteData.views.push({
				type: 'text',
				text: this.$L('如何查看精彩内容?'),
				css: {
					left: '210px',
					top: `${nextY + 40}px`,
					fontSize: '28px',
					color: '#333333',
					fontWeight: 'bold'
				}
			});
			
			paletteData.views.push({
				type: 'text',
				text: this.$L('长按/扫描二维码解锁详情'),
				css: {
					left: '210px',
					top: `${nextY + 80}px`,
					fontSize: '24px',
					color: '#999999'
				}
			});
			
			// 更新Painter数据
			this.painterPalette = paletteData;
		},
		
		// H5环境下准备海报数据
		prepareH5PosterData() {
			// 由于H5不支持painter组件，这里我们设置好数据，之后使用简单的展示方式
			this.dataLoaded = true;
			
			// 如果存在触发下载的请求，并且Painter生成的图片路径不存在
			if (this.posterLoading && this.shareSource === 'DOWNLOAD' && !this.painterImagePath) {
				// 在H5环境下，直接使用海报卡片的DOM节点转为图片
				this.createH5PosterImage();
			} else if (this.posterLoading) {
				// 如果是其他渠道的请求或不需要下载，确保loading被关闭
				uni.hideLoading();
				this.posterLoading = false;
			}
		},
		
		// 在H5环境中创建海报图片
		createH5PosterImage() {
			// #ifdef H5
			try {
				// 获取海报卡片元素
				const posterCard = document.querySelector('.poster-card');
				if (!posterCard) {
					throw new Error('海报卡片元素不存在');
				}
				
				uni.showLoading({
					title: this.$L('正在生成图片...')
				});
				
				// 使用html2canvas生成图片
				import('html2canvas').then(html2canvas => {
					html2canvas.default(posterCard, {
						useCORS: true, // 允许跨域
						scale: 2, // 提高清晰度
						allowTaint: true,
						backgroundColor: '#FFFFFF',
						logging: false,
						onclone: (clonedDoc) => {
							// 在克隆的文档中处理样式
							const clonedCard = clonedDoc.querySelector('.poster-card');
							if (clonedCard) {
								clonedCard.style.transform = 'none';
							}
						}
					}).then(canvas => {
						// 转换为base64图片
						this.h5ImageBase64 = canvas.toDataURL('image/png');
						this.canShowImagePreview = true;
						
						// 如果是下载操作，执行下载
						if (this.posterLoading && this.shareSource === 'DOWNLOAD') {
							this.h5Poster = true;
							this.painterImagePath = this.h5ImageBase64;
							this.downloadH5Image(this.h5ImageBase64);
						}
						
						uni.hideLoading();
						this.posterLoading = false;
					}).catch(error => {
						console.error('截图失败:', error);
						uni.hideLoading();
						uni.showToast({
							title: this.$L('生成海报失败'),
							icon: 'none'
						});
						this.posterLoading = false;
					});
				}).catch(error => {
					console.error('加载html2canvas失败:', error);
					uni.hideLoading();
					uni.showToast({
						title: this.$L('生成海报失败'),
						icon: 'none'
					});
					this.posterLoading = false;
				});
			} catch (error) {
				console.error('创建H5海报图片失败:', error);
				uni.hideLoading();
				uni.showToast({
					title: this.$L('生成海报失败'),
					icon: 'none'
				});
				this.posterLoading = false;
			}
			// #endif
		},
		
		// Painter生成图片成功回调
		onPainterImageOK(e) {
			// #ifdef MP-WEIXIN
			console.log('生成海报图片成功:', e);
			this.painterImagePath = e.detail.path;
			this.waitingForPainter = false;
			
			// 生成成功后，无论何种情况都尝试保存
			if (this.posterLoading) {
				this.savePosterToAlbum();
			}
			// #endif
			
			// #ifdef H5
			// 在H5环境中，处理生成的图片URL，便于预览和保存
			this.processH5ImagePath(e.detail);
			// #endif
		},
		
		// 在H5环境中处理图片路径
		processH5ImagePath(imageDetail) {
			// #ifdef H5
			try {
				// 检查是否有base64数据
				if (imageDetail.base64) {
					// 直接使用base64数据
					this.h5ImageBase64 = imageDetail.base64;
					this.canShowImagePreview = true;
				} else if (imageDetail.path) {
					// 将临时路径转换为base64（这在某些环境下可能需要）
					// 因为H5环境中，painter生成的路径可能无法直接在预览中使用
					const canvas = document.createElement('canvas');
					const ctx = canvas.getContext('2d');
					const img = new Image();
					img.crossOrigin = 'Anonymous';
					img.onload = () => {
						canvas.width = img.width;
						canvas.height = img.height;
						ctx.drawImage(img, 0, 0);
						this.h5ImageBase64 = canvas.toDataURL('image/png');
						this.canShowImagePreview = true;
					};
					img.src = imageDetail.path;
				}
			} catch (error) {
				console.error('处理H5图片路径失败:', error);
			}
			// #endif
		},
		
		// Painter生成图片失败回调
		onPainterImageError(e) {
			console.error('生成海报图片失败:', e);
			uni.hideLoading();
			uni.showToast({
				title: this.$L('生成海报失败'),
				icon: 'none'
			});
			this.posterLoading = false;
		},
		
		// 保存海报到相册
		savePosterToAlbum() {
			// #ifdef H5
			// H5环境下，采用特殊处理方式
			if (this.h5ImageBase64) {
				uni.hideLoading();
				
				// 直接下载图片
				this.downloadH5Image(this.h5ImageBase64);
				return;
			}
			// #endif
			
			if (!this.painterImagePath) {
				console.error('没有可用的海报图片');
				uni.hideLoading();
				uni.showToast({
					title: this.$L('生成海报失败'),
					icon: 'none'
				});
				return;
			}
			
			// 判断平台，针对不同平台使用不同的保存方法
			// #ifdef H5
			if (this.isInWechat()) {
				// 微信浏览器内需要长按图片保存
				this.saveImageInWechat();
				return;
			}
			// #endif
			
			// #ifdef MP-WEIXIN
			// 先检查是否有保存到相册的权限
			uni.getSetting({
				success: res => {
					if (!res.authSetting['scope.writePhotosAlbum']) {
						// 没有权限，先申请权限
						uni.authorize({
							scope: 'scope.writePhotosAlbum',
							success: () => {
								// 授权成功后保存图片
								this.actualSaveImage();
							},
							fail: err => {
								console.error('授权失败:', err);
								uni.hideLoading();
								uni.showModal({
									title: this.$L('提示'),
									content: this.$L('需要您授权保存图片到相册'),
									confirmText: this.$L('去设置'),
									success: modal => {
										if (modal.confirm) {
											uni.openSetting();
										}
									}
								});
							}
						});
					} else {
						// 已有权限，直接保存
						this.actualSaveImage();
					}
				},
				fail: err => {
					console.error('获取设置失败:', err);
					this.actualSaveImage(); // 尝试直接保存
				}
			});
			// #endif
		},
		
		// 实际执行保存图片的方法
		actualSaveImage() {
			console.log('实际执行保存，路径:', this.painterImagePath);
			uni.saveImageToPhotosAlbum({
				filePath: this.painterImagePath,
				success: () => {
					uni.hideLoading();
					uni.showToast({
						title: this.$L('保存成功'),
						icon: 'success'
					});
					this.posterLoading = false;
				},
				fail: (err) => {
					console.error('保存到相册失败:', err);
					uni.hideLoading();
					
					// 不同情况的错误处理
					if (err.errMsg && err.errMsg.includes('auth deny')) {
						uni.showModal({
							title: this.$L('提示'),
							content: this.$L('需要您授权保存图片'),
							success: (res) => {
								if (res.confirm) {
									uni.openSetting();
								}
							}
						});
					} else if (err.errMsg && err.errMsg.includes('fail file not exist')) {
						// 文件不存在，可能是路径问题
						uni.showToast({
							title: this.$L('图片文件不存在'),
							icon: 'none'
						});
						// 重新触发生成
						this.regeneratePoster();
					} else {
						uni.showToast({
							title: this.$L('保存失败'),
							icon: 'none'
						});
					}
					this.posterLoading = false;
				}
			});
		},
		
		// 重新生成海报
		regeneratePoster() {
			this.posterLoading = true;
			this.painterImagePath = '';
			this.waitingForPainter = true;
			uni.showLoading({
				title: this.$L('重新生成海报...')
			});
			
			// 重新准备Painter数据
			this.preparePainterData();
			setTimeout(() => {
				// 强制触发Painter重绘
				this.dancePaletteData = JSON.parse(JSON.stringify(this.painterPalette));
			}, 500);
		},
		
		// 在微信浏览器中保存图片
		saveImageInWechat() {
			uni.hideLoading();
			
			// 显示引导用户长按保存的提示
			uni.showModal({
				title: this.$L('提示'),
				content: this.$L('由于微信浏览器限制，请长按图片，选择"保存图片"'),
				showCancel: false,
				confirmText: this.$L('我知道了'),
				success: () => {
					// #ifdef H5
					// 在H5环境，直接下载图片
					if (this.h5ImageBase64) {
						this.downloadH5Image(this.h5ImageBase64);
					}
					// #endif
				}
			});
		},
		
		// H5环境下下载图片
		downloadH5Image(base64) {
			// #ifdef H5
			try {
				console.log('准备下载H5图片');
				const isWechat = this.isInWechat();
				
				if (isWechat) {
					// 微信浏览器环境下，提示用户长按保存
					uni.hideLoading();
					
					// 创建图片预览元素
					const previewDiv = document.createElement('div');
					previewDiv.style.cssText = `
						position: fixed;
						top: 0;
						left: 0;
						width: 100%;
						height: 100%;
						background: rgba(0,0,0,0.7);
						z-index: 9999;
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: center;
					`;
					
					// 添加提示文本
					const tipText = document.createElement('div');
					tipText.innerText = this.$L('长按图片保存到相册');
					tipText.style.cssText = `
						color: #fff;
						font-size: 16px;
						margin-bottom: 20px;
					`;
					previewDiv.appendChild(tipText);
					
					// 添加图片
					const img = document.createElement('img');
					img.src = base64;
					img.style.cssText = `
						max-width: 90%;
						max-height: 70%;
						box-shadow: 0 4px 12px rgba(0,0,0,0.15);
					`;
					previewDiv.appendChild(img);
					
					// 添加关闭按钮
					const closeBtn = document.createElement('div');
					closeBtn.innerText = this.$L('关闭');
					closeBtn.style.cssText = `
						color: #fff;
						border: 1px solid #fff;
						border-radius: 20px;
						padding: 8px 30px;
						margin-top: 30px;
						cursor: pointer;
					`;
					closeBtn.onclick = () => {
						document.body.removeChild(previewDiv);
					};
					previewDiv.appendChild(closeBtn);
					
					// 点击空白处关闭预览
					previewDiv.addEventListener('click', (e) => {
						if (e.target === previewDiv) {
							document.body.removeChild(previewDiv);
						}
					});
					
					document.body.appendChild(previewDiv);
					
					// 显示提示
					uni.showToast({
						title: this.$L('长按图片保存'),
						icon: 'none',
						duration: 2000
					});
				} else {
					// 非微信浏览器环境，使用通用的下载方法
					try {
						// 1. 尝试使用Blob和createObjectURL方法
						const byteString = atob(base64.split(',')[1]);
						const mimeString = base64.split(',')[0].split(':')[1].split(';')[0];
						const ab = new ArrayBuffer(byteString.length);
						const ia = new Uint8Array(ab);
						for (let i = 0; i < byteString.length; i++) {
							ia[i] = byteString.charCodeAt(i);
						}
						const blob = new Blob([ab], { type: mimeString });
						const url = URL.createObjectURL(blob);
						
						// 2. 创建下载链接
						const link = document.createElement('a');
						link.href = url;
						link.download = `海报_${new Date().getTime()}.png`; // 设置文件名
						link.style.display = 'none';
						document.body.appendChild(link);
						
						// 3. 触发点击
						link.click();
						
						// 4. 清理
						setTimeout(() => {
							document.body.removeChild(link);
							URL.revokeObjectURL(url);
						}, 100);
						
						uni.hideLoading();
						uni.showToast({
							title: this.$L('保存成功'),
							icon: 'success'
						});
					} catch (e) {
						console.error('Blob下载失败，尝试使用base64直接下载:', e);
						
						// 降级方案：直接使用base64下载
						const link = document.createElement('a');
						link.href = base64;
						link.download = `海报_${new Date().getTime()}.png`;
						link.style.display = 'none';
						document.body.appendChild(link);
						
						try {
							link.click();
							uni.hideLoading();
							uni.showToast({
								title: this.$L('保存成功'),
								icon: 'success'
							});
						} catch (e2) {
							console.error('Base64下载也失败:', e2);
							// 最后的降级方案：在新窗口打开图片
							window.open(base64);
							uni.hideLoading();
							uni.showToast({
								title: this.$L('请右键保存图片'),
								icon: 'none',
								duration: 2000
							});
						} finally {
							document.body.removeChild(link);
						}
					}
				}
			} catch (error) {
				console.error('下载H5图片失败:', error);
				uni.hideLoading();
				uni.showToast({
					title: this.$L('保存失败'),
					icon: 'none'
				});
			}
			// #endif
		},
		
		// 检测是否在微信浏览器中
		isInWechat() {
			// #ifdef H5
			const ua = navigator.userAgent.toLowerCase();
			return ua.indexOf('micromessenger') !== -1;
			// #endif
			return false;
		},
		
		// 处理分享选项点击
		handleShareOptionClick(type) {
			const option = this.shareOptions.find(opt => opt.type === type)
			if (!option) return
			
			this.shareSource = type
			
			// #ifdef H5
			// 在H5环境下，无论点击什么按钮都使用download渠道
			this.generateQrCode('download')
			return
			// #endif
			
			// 在其他环境下，使用选项自身的渠道
			this.generateQrCode(option.channel)
		},
		
		// 处理特定渠道的后续操作
		processChannelAction(channel) {
			// 根据不同渠道处理分享
			switch (channel) {
				case 'wechat_friend':
				case 'wechat_group':
				case 'wechat_moment':
					// 微信分享由button的open-type处理
					break;
					
				case 'download':
					// #ifdef H5
					// 在H5环境下，只有通过点击下载按钮才处理下载
					if (this.shareSource === 'DOWNLOAD') {
						this.downloadPoster();
					} else {
						// 如果是初始加载，不执行下载
						if (this.posterLoading) {
							uni.hideLoading();
							this.posterLoading = false;
						}
					}
					// #endif
					
					// #ifndef H5
					// 非H5环境直接下载海报
					this.downloadPoster();
					// #endif
					break;
					
				default:
					// 确保加载状态被清除
					if (this.posterLoading) {
						uni.hideLoading();
						this.posterLoading = false;
					}
					break;
			}
		},
		
		// 图片加载错误处理
		onQrCodeError(e) {
			console.error('二维码图片加载失败:', e);
			// 使用默认图片
			this.qrCodeUrl = this.defaultQrCodeUrl;
		},
		
		// 处理海报卡片点击
		handlePosterCardClick() {
			// 避免重复点击
			if (this.posterLoading) return;
			
			// 显示保存提示
			this.showSaveTip = true;
			
			// 3秒后自动隐藏提示
			setTimeout(() => {
				this.showSaveTip = false;
			}, 3000);
			
			// 设置标识
			this.posterLoading = true;
			this.waitingForPainter = true;
			this.shareSource = 'CLICK_CARD';
			
			// 判断是否已生成海报图片
			if (this.painterImagePath) {
				// 直接保存
				this.savePosterToAlbum();
			} else {
				// 先生成海报
				uni.showLoading({
					title: this.$L('正在生成海报...')
				});
				
				// 先准备Painter数据
				this.preparePainterData();
				// 然后延迟触发重绘，确保数据已准备好
				setTimeout(() => {
					this.dancePaletteData = JSON.parse(JSON.stringify(this.painterPalette));
				}, 300);
				
				// 设置超时处理
				setTimeout(() => {
					if (this.waitingForPainter) {
						console.log('生成海报超时，重试...');
						this.waitingForPainter = false;
						uni.hideLoading();
						uni.showToast({
							title: this.$L('生成超时，请重试'),
							icon: 'none'
						});
						this.posterLoading = false;
					}
				}, 15000); // 15秒超时
			}
		},
		
		// 下载海报
		downloadPoster() {
			if (this.posterLoading) return; // 防止重复点击
			
			this.posterLoading = true;
			this.waitingForPainter = true;
			this.shareSource = 'DOWNLOAD';
			
			uni.showLoading({
				title: this.$L('正在生成海报...')
			});
			
			// #ifdef H5
			// H5环境下使用html2canvas直接截取海报卡片
			this.captureH5Poster();
			return;
			// #endif
			
			// #ifdef MP-WEIXIN
			// 微信小程序环境下使用Canvas API
			this.captureMpPoster();
			// #endif
		},
		
		// H5环境下截取海报卡片
		captureH5Poster() {
			// #ifdef H5
			try {
				// 获取海报卡片DOM元素（使用更精确的选择器）
				const posterCard = document.querySelector('.page-content .poster-card');
				if (!posterCard) {
					console.error('找不到海报卡片元素');
					uni.hideLoading();
					uni.showToast({
						title: this.$L('生成海报失败'),
						icon: 'none'
					});
					this.posterLoading = false;
					this.waitingForPainter = false;
					return;
				}
				
				console.log('找到海报卡片元素，开始截图');
				uni.showLoading({
					title: this.$L('正在生成海报...')
				});
				
				// 直接使用导入的html2canvas
				html2canvas(posterCard, {
					useCORS: true, // 允许跨域
					scale: 2, // 提高清晰度
					allowTaint: true,
					backgroundColor: '#FFFFFF',
					logging: false,
					onclone: (clonedDoc) => {
						// 在克隆的文档中处理样式
						const clonedCard = clonedDoc.querySelector('.page-content .poster-card');
						if (clonedCard) {
							clonedCard.style.width = posterCard.offsetWidth + 'px';
							clonedCard.style.height = posterCard.offsetHeight + 'px';
							clonedCard.style.position = 'relative';
							clonedCard.style.transform = 'none';
						}
					}
				}).then(canvas => {
					try {
						// 转换为base64图片
						const base64 = canvas.toDataURL('image/png');
						console.log('截图成功，准备下载图片');
						
						// 直接调用下载方法
						this.downloadH5Image(base64);
						
						uni.hideLoading();
						this.posterLoading = false;
						this.waitingForPainter = false;
					} catch (e) {
						console.error('Canvas转图片失败:', e);
						uni.hideLoading();
						uni.showToast({
							title: this.$L('生成图片失败'),
							icon: 'none'
						});
						this.posterLoading = false;
						this.waitingForPainter = false;
					}
				}).catch(error => {
					console.error('截图失败:', error);
					uni.hideLoading();
					uni.showToast({
						title: this.$L('生成海报失败'),
						icon: 'none'
					});
					this.posterLoading = false;
					this.waitingForPainter = false;
				});
			} catch (error) {
				console.error('创建H5海报图片失败:', error);
				uni.hideLoading();
				uni.showToast({
					title: this.$L('生成海报失败'),
					icon: 'none'
				});
				this.posterLoading = false;
				this.waitingForPainter = false;
			}
			// #endif
		},
		
		// 微信小程序环境下截取海报卡片
		captureMpPoster() {
			// #ifdef MP-WEIXIN
			try {
				uni.showLoading({
					title: this.$L('准备生成海报...')
				});
				
				// 1. 获取海报卡片元素信息
				const query = uni.createSelectorQuery();
				query.select('.poster-card')
					.fields({ node: true, size: true, rect: true }, (res) => {
						if (!res) {
							uni.hideLoading();
							uni.showToast({
								title: this.$L('获取海报元素失败'),
								icon: 'none'
							});
							this.posterLoading = false;
							return;
						}
						
						const width = res.width;
						const height = res.height;
						console.log('海报尺寸:', width, height);
						
						// 2. 创建离屏canvas
						try {
							const canvasWidth = width * 2;
							const canvasHeight = height * 2;
							
							const canvas = wx.createOffscreenCanvas({
								type: '2d',
								width: canvasWidth,
								height: canvasHeight
							});
							const ctx = canvas.getContext('2d');
							
							// 放大2倍以提高清晰度
							ctx.scale(2, 2);
							
							// 背景色
							ctx.fillStyle = '#FFFFFF';
							ctx.fillRect(0, 0, width, height);
							
							// 3. 分步获取和绘制海报元素
							this.drawMpPoster(ctx, width, height, canvas);
						} catch (error) {
							console.error('创建Canvas失败:', error);
							uni.hideLoading();
							uni.showToast({
								title: this.$L('创建海报画布失败'),
								icon: 'none'
							});
							this.posterLoading = false;
						}
					})
					.exec();
			} catch (error) {
				console.error('微信小程序海报截图失败:', error);
				uni.hideLoading();
				uni.showToast({
					title: this.$L('生成海报失败'),
					icon: 'none'
				});
				this.posterLoading = false;
			}
			// #endif
		},
		
		// 分步绘制微信小程序海报
		drawMpPoster(ctx, width, height, canvas) {
			// #ifdef MP-WEIXIN
			const drawPromises = [];
			
			// 1. 绘制头部区域
			drawPromises.push(new Promise((resolve) => {
				uni.showLoading({ title: this.$L('绘制海报中(1/5)...') });
				
				// 加载头部图片
				const headerImg = canvas.createImage();
				headerImg.onload = () => {
					// 计算合适的头部高度以保持原始比例
					// 改为固定高度值，避免拉伸
					const headerHeight = width * 0.2; // 降低高度，使用固定比例
					
					// 添加圆角处理
					ctx.save();
					ctx.beginPath();
					ctx.moveTo(0, 20); // 左上角圆角
					ctx.arcTo(0, 0, 20, 0, 20);
					ctx.lineTo(width - 20, 0); // 顶部边
					ctx.arcTo(width, 0, width, 20, 20); // 右上角圆角
					ctx.lineTo(width, headerHeight); // 右边
					ctx.lineTo(0, headerHeight); // 底部边
					ctx.closePath();
					ctx.clip();
					
					ctx.drawImage(headerImg, 0, 0, width, headerHeight);
					ctx.restore();
					
					// 绘制日期
					ctx.fillStyle = '#FFFFFF';
					ctx.font = 'bold 14px sans-serif';
					ctx.textAlign = 'right';
					ctx.fillText(this.$L('日期'), width - 15, 30);
					ctx.fillText(this.formatDate, width - 15, 50);
					
					resolve(headerHeight); // 返回头部高度供后续使用
				};
				headerImg.onerror = () => {
					console.error('加载头部图片失败');
					resolve(width * 0.2); // 返回默认高度
				};
				headerImg.src = this.headerImgUrl;
			}));
			
			// 2. 绘制用户信息
			drawPromises.push(new Promise((resolve, reject) => {
				uni.showLoading({ title: this.$L('绘制海报中(2/5)...') });
				
				// 等待第一步完成后获取头部高度
				drawPromises[0].then((headerHeight) => {
					// 设置统一左边距
					const leftPadding = 20; // 统一左边距
					const rightPadding = 20; // 统一右边距
					
					// 调整头像位置，使其与文字左对齐
					const avatarImg = canvas.createImage();
					avatarImg.onload = () => {
						// 绘制圆形头像
						ctx.save();
						ctx.beginPath();
						const avatarX = leftPadding; // 使用统一左边距
						const avatarY = headerHeight + 20; // 头部下方20px处
						const avatarSize = 40;
						ctx.arc(avatarX + avatarSize/2, avatarY + avatarSize/2, avatarSize/2, 0, Math.PI * 2);
						ctx.clip();
						ctx.drawImage(avatarImg, avatarX, avatarY, avatarSize, avatarSize);
						ctx.restore();
						
						// 绘制用户名和粉丝数，增加左侧间距
						const textLeftMargin = leftPadding + avatarSize + 15; // 头像右侧15px处开始文本
						
						// 用户名
						ctx.fillStyle = '#333333';
						ctx.font = 'bold 16px sans-serif';
						ctx.textAlign = 'left';
						ctx.fillText(this.memberInfo.memberNickName || this.memberInfo.memberName || '', 
							textLeftMargin, avatarY + 20);
						
						// 粉丝数
						if (this.memberInfo.fansNum) {
							ctx.fillStyle = '#999999';
							ctx.font = '14px sans-serif';
							ctx.fillText(`${this.memberInfo.fansNum}${this.$L('粉丝')}`, 
								textLeftMargin, avatarY + 43);
						}
						
						// 返回头像底部位置，用于后续计算
						resolve({
							bottomY: avatarY + avatarSize + 30, // 头像底部位置+30px间距
							leftPadding: leftPadding,
							rightPadding: rightPadding,
							contentWidth: width - leftPadding - rightPadding // 内容区宽度
						});
					};
					avatarImg.onerror = () => {
						console.error('加载头像失败');
						// 即使头像加载失败，也需要计算位置
						resolve({
							bottomY: headerHeight + 20 + 40 + 30, // 默认头像底部位置+30px间距
							leftPadding: leftPadding,
							rightPadding: rightPadding,
							contentWidth: width - leftPadding - rightPadding
						});
					};
					avatarImg.src = this.memberInfo.memberAvatar;
				}).catch(reject);
			}));
			
			// 3. 绘制文章标题和内容
			drawPromises.push(new Promise((resolve, reject) => {
				uni.showLoading({ title: this.$L('绘制海报中(3/5)...') });
				
				// 等待用户信息绘制完成
				Promise.all([drawPromises[0], drawPromises[1]]).then(([_, layoutInfo]) => {
					const { bottomY, leftPadding, contentWidth } = layoutInfo;
					let yPosition = bottomY; // 从头像下方开始
					
					// 绘制标题
					ctx.fillStyle = '#333333';
					ctx.font = 'bold 18px sans-serif';
					ctx.textAlign = 'left';
					
					// 文本换行处理
					const titleLines = this.wrapText(ctx, this.videoDetail.videoName, contentWidth, 2);
					titleLines.forEach((line) => {
						ctx.fillText(line, leftPadding, yPosition);
						yPosition += 25;
					});
					
					// 增加标题与简介间的间距
					yPosition += 5;
					
					// 绘制简介
					if (this.videoDetail.introduction) {
						yPosition += 10;
						ctx.fillStyle = '#666666';
						ctx.font = '14px sans-serif';
						
						const introLines = this.wrapText(ctx, this.videoDetail.introduction, contentWidth, 2);
						introLines.forEach((line) => {
							ctx.fillText(line, leftPadding, yPosition);
							yPosition += 20;
						});
					}
					
					// 绘制内容
					if (this.videoContent) {
						yPosition += 10;
						ctx.fillStyle = '#999999';
						ctx.font = '13px sans-serif';
						
						const contentLines = this.wrapText(ctx, this.videoContent, contentWidth, 2);
						contentLines.forEach((line) => {
							ctx.fillText(line, leftPadding, yPosition);
							yPosition += 18;
						});
					}
					
					resolve({
						bottomY: yPosition + 10, // 文本内容底部位置+10px间距
						leftPadding,
						contentWidth
					});
				}).catch(reject);
			}));
			
			// 4. 绘制图片内容
			drawPromises.push(new Promise((resolve, reject) => {
				uni.showLoading({ title: this.$L('绘制海报中(4/5)...') });
				
				// 等待标题内容绘制完成
				Promise.all([drawPromises[0], drawPromises[1], drawPromises[2]]).then(([_, __, layoutInfo]) => {
					const { bottomY, leftPadding, contentWidth } = layoutInfo;
					let yPosition = bottomY; // 从文本内容下方开始
					
					if (this.videoImages && this.videoImages.length > 0) {
						if (this.imagesCount === 1) {
							// 单张图片
							const img = canvas.createImage();
							img.onload = () => {
								const imgHeight = contentWidth * 0.6; // 保持合理高宽比
								
								// 添加圆角处理
								const x = leftPadding;
								const y = yPosition;
								const radius = 10; // 圆角半径
								
								ctx.save();
								ctx.beginPath();
								ctx.moveTo(x + radius, y);
								ctx.lineTo(x + contentWidth - radius, y);
								ctx.arcTo(x + contentWidth, y, x + contentWidth, y + radius, radius);
								ctx.lineTo(x + contentWidth, y + imgHeight - radius);
								ctx.arcTo(x + contentWidth, y + imgHeight, x + contentWidth - radius, y + imgHeight, radius);
								ctx.lineTo(x + radius, y + imgHeight);
								ctx.arcTo(x, y + imgHeight, x, y + imgHeight - radius, radius);
								ctx.lineTo(x, y + radius);
								ctx.arcTo(x, y, x + radius, y, radius);
								ctx.closePath();
								ctx.clip();
								
								ctx.drawImage(img, leftPadding, yPosition, contentWidth, imgHeight);
								ctx.restore();
								
								resolve({
									bottomY: yPosition + imgHeight + 20, // 图片底部位置+20px间距
									leftPadding,
									contentWidth
								});
							};
							img.onerror = () => {
								console.error('加载图片失败');
								resolve({
									bottomY: yPosition, // 保持原位置
									leftPadding,
									contentWidth
								});
							};
							img.src = this.videoImages[0];
						} else {
							// 多张图片，支持最多9张
							const maxImages = Math.min(this.imagesCount, 9);
							let loadedCount = 0;
							
							if (maxImages <= 3) {
								// 2-3张图片处理
								const imgWidth = (contentWidth - (maxImages - 1) * 5) / maxImages;
								const imgHeight = imgWidth * 0.8;
								
								// 创建一个计数器，因为可能有图片加载失败
								let finalBottomY = yPosition + imgHeight;
								
								for (let i = 0; i < maxImages; i++) {
									const img = canvas.createImage();
									img.onload = () => {
										// 添加圆角处理
										const x = leftPadding + i * (imgWidth + 5);
										const y = yPosition;
										const radius = 10; // 圆角半径
										
										ctx.save();
										ctx.beginPath();
										ctx.moveTo(x + radius, y);
										ctx.lineTo(x + imgWidth - radius, y);
										ctx.arcTo(x + imgWidth, y, x + imgWidth, y + radius, radius);
										ctx.lineTo(x + imgWidth, y + imgHeight - radius);
										ctx.arcTo(x + imgWidth, y + imgHeight, x + imgWidth - radius, y + imgHeight, radius);
										ctx.lineTo(x + radius, y + imgHeight);
										ctx.arcTo(x, y + imgHeight, x, y + imgHeight - radius, radius);
										ctx.lineTo(x, y + radius);
										ctx.arcTo(x, y, x + radius, y, radius);
										ctx.closePath();
										ctx.clip();
										
										ctx.drawImage(
											img, 
											x, 
											y, 
											imgWidth, 
											imgHeight
										);
										ctx.restore();
										
										loadedCount++;
										if (loadedCount === maxImages) {
											resolve({
												bottomY: finalBottomY + 20, // 图片底部位置+20px间距
												leftPadding,
												contentWidth
											});
										}
									};
									img.onerror = () => {
										console.error(`加载图片${i}失败`);
										loadedCount++;
										if (loadedCount === maxImages) {
											resolve({
												bottomY: finalBottomY + 20,
												leftPadding,
												contentWidth
											});
										}
									};
									img.src = this.videoImages[i];
								}
							} else {
								// 4-9张图片，按3列网格排列
								const imgSize = (contentWidth - 10) / 3; // 3列，列间距5px
								const rows = Math.ceil(maxImages / 3);
								
								// 计算最终底部位置
								let finalBottomY = yPosition + rows * (imgSize + 5) - 5;
								
								for (let i = 0; i < maxImages; i++) {
									const row = Math.floor(i / 3);
									const col = i % 3;
									const img = canvas.createImage();
									
									img.onload = () => {
										// 添加圆角处理
										const x = leftPadding + col * (imgSize + 5);
										const y = yPosition + row * (imgSize + 5);
										const radius = 10; // 圆角半径
										
										ctx.save();
										ctx.beginPath();
										ctx.moveTo(x + radius, y);
										ctx.lineTo(x + imgSize - radius, y);
										ctx.arcTo(x + imgSize, y, x + imgSize, y + radius, radius);
										ctx.lineTo(x + imgSize, y + imgSize - radius);
										ctx.arcTo(x + imgSize, y + imgSize, x + imgSize - radius, y + imgSize, radius);
										ctx.lineTo(x + radius, y + imgSize);
										ctx.arcTo(x, y + imgSize, x, y + imgSize - radius, radius);
										ctx.lineTo(x, y + radius);
										ctx.arcTo(x, y, x + radius, y, radius);
										ctx.closePath();
										ctx.clip();
										
										ctx.drawImage(
											img,
											x,
											y,
											imgSize,
											imgSize
										);
										ctx.restore();
										
										loadedCount++;
										if (loadedCount === maxImages) {
											resolve({
												bottomY: finalBottomY + 20,
												leftPadding,
												contentWidth
											});
										}
									};
									img.onerror = () => {
										console.error(`加载图片${i}失败`);
										loadedCount++;
										if (loadedCount === maxImages) {
											resolve({
												bottomY: finalBottomY + 20,
												leftPadding,
												contentWidth
											});
										}
									};
									img.src = this.videoImages[i];
								}
							}
						}
					} else {
						// 没有图片
						resolve({
							bottomY: yPosition,
							leftPadding,
							contentWidth
						});
					}
				}).catch(reject);
			}));
			
			// 5. 绘制二维码区域
			drawPromises.push(new Promise((resolve, reject) => {
				uni.showLoading({ title: this.$L('绘制海报中(5/5)...') });
				
				// 等待图片内容绘制完成
				Promise.all([drawPromises[0], drawPromises[1], drawPromises[2], drawPromises[3]]).then(([_, __, ___, layoutInfo]) => {
					const { bottomY, leftPadding, contentWidth } = layoutInfo;
					
					// 绘制分隔线
					ctx.fillStyle = '#f5f5f5';
					ctx.fillRect(leftPadding, bottomY, contentWidth, 1);
					
					const qrcodeY = bottomY + 15; // 分隔线下方15px处
					
					// 绘制二维码
					const qrImg = canvas.createImage();
					qrImg.onload = () => {
						// 绘制二维码，左对齐
						const qrSize = 80;
						ctx.drawImage(qrImg, leftPadding, qrcodeY, qrSize, qrSize);
						
						// 绘制二维码说明文字
						const textLeftMargin = leftPadding + qrSize + 15; // 二维码右侧15px处
						
						ctx.fillStyle = '#333333';
						ctx.font = 'bold 14px sans-serif';
						ctx.textAlign = 'left';
						ctx.fillText(this.$L('如何查看精彩内容?'), textLeftMargin, qrcodeY + 30);
						
						ctx.fillStyle = '#999999';
						ctx.font = '12px sans-serif';
						ctx.fillText(this.$L('长按/扫描二维码解锁详情'), textLeftMargin, qrcodeY + 55);
						
						resolve({
							bottomY: qrcodeY + qrSize + 20, // 二维码底部位置+20px间距
							leftPadding,
							contentWidth
						});
					};
					qrImg.onerror = () => {
						console.error('加载二维码失败');
						
						// 即使二维码加载失败，也绘制提示文字
						const textLeftMargin = leftPadding + 80 + 15; // 假设二维码宽度80px
						
						ctx.fillStyle = '#333333';
						ctx.font = 'bold 14px sans-serif';
						ctx.textAlign = 'left';
						ctx.fillText(this.$L('如何查看精彩内容?'), textLeftMargin, qrcodeY + 30);
						
						ctx.fillStyle = '#999999';
						ctx.font = '12px sans-serif';
						ctx.fillText(this.$L('长按/扫描二维码解锁详情'), textLeftMargin, qrcodeY + 55);
						
						resolve({
							bottomY: qrcodeY + 80 + 20, // 假设二维码高度80px
							leftPadding,
							contentWidth
						});
					};
					qrImg.src = this.qrCodeUrl;
				}).catch(reject);
			}));
			
			// 等待所有绘制完成，生成图片
			Promise.all(drawPromises).then(() => {
				uni.showLoading({ title: this.$L('生成图片中...') });
				
				// 使用canvas生成图片
				wx.canvasToTempFilePath({
					canvas: canvas,
					success: (res) => {
						this.painterImagePath = res.tempFilePath;
						this.savePosterToAlbum();
					},
					fail: (err) => {
						console.error('生成临时文件失败:', err);
						uni.hideLoading();
						uni.showToast({
							title: this.$L('生成海报失败'),
							icon: 'none'
						});
						this.posterLoading = false;
					}
				});
			}).catch((error) => {
				console.error('绘制海报失败:', error);
				uni.hideLoading();
				uni.showToast({
					title: this.$L('生成海报失败'),
					icon: 'none'
				});
				this.posterLoading = false;
			});
			// #endif
		},
		
		// 文本换行处理
		wrapText(ctx, text, maxWidth, maxLines = 2) {
			if (!text) return [];
			
			const lines = [];
			let line = '';
			
			// 遍历文本
			for (let i = 0; i < text.length; i++) {
				const char = text.charAt(i);
				const testLine = line + char;
				const metrics = ctx.measureText(testLine);
				const testWidth = metrics.width;
				
				if (testWidth > maxWidth && i > 0) {
					// 当前行已经满了，添加到lines
					lines.push(line);
					line = char;
					
					// 检查是否达到最大行数
					if (lines.length >= maxLines - 1) {
						// 最后一行，需要考虑省略号
						if (i < text.length - 1) {
							while (ctx.measureText(line + '...').width > maxWidth && line.length > 0) {
								line = line.slice(0, -1);
							}
							line += '...';
						}
						break;
					}
				} else {
					line = testLine;
				}
			}
			
			// 添加最后一行
			if (line) {
				if (lines.length >= maxLines) {
					// 如果已经达到最大行数，确保最后一行有省略号
					if (!line.endsWith('...')) {
						while (ctx.measureText(line + '...').width > maxWidth && line.length > 0) {
							line = line.slice(0, -1);
						}
						line += '...';
					}
				}
				lines.push(line);
			}
			
			return lines;
		},
		
		// 确保图片URL是完整的
		ensureImageUrl(url) {
			if (!url) return this.imgUrl + 'graphic/default_avatar.png';
			
			// 如果已经是完整URL，直接返回
			if (url.startsWith('http://') || url.startsWith('https://') || url.startsWith('data:image')) {
				return url;
			}
			
			// 如果是base64数据，直接返回
			if (url.startsWith('data:image')) {
				return url;
			}
			
			// 否则添加域名前缀
			return this.imgHost + url;
		},
		
		// 保存海报方法
		savePoster() {
			// 调用html2canvas渲染组件的截图方法
			this.$refs.renderRef.h2cRenderDom();
		},
		
		// 渲染完成回调
		renderOver(base64) {
			// #ifdef H5
			// H5环境展示海报预览窗口，让用户长按保存
			try {
				this.previewImageUrl = base64;
				this.previewVisible = true;
				
				uni.showToast({
					title: this.$L('海报生成成功'),
					icon: 'success'
				});
			} catch (err) {
				console.error('图片处理失败:', err);
				uni.showToast({
					title: this.$L('生成失败'),
					icon: 'none'
				});
			}
			// #endif
			
			// #ifndef H5
			// 非H5环境代码保持不变
			base64ToPath(base64).then(path => {
				// 保存图片到相册
				uni.saveImageToPhotosAlbum({
					filePath: path,
					success: () => {
						uni.showToast({
							title: this.$L('保存成功'),
							icon: 'success'
						});
					},
					fail: (err) => {
						console.error('保存失败:', err);
						uni.showToast({
							title: this.$L('保存失败'),
							icon: 'none'
						});
					}
				});
			}).catch(err => {
				console.error('图片处理失败:', err);
				uni.showToast({
					title: this.$L('图片处理失败'),
					icon: 'none'
				});
			});
			// #endif
		},
		
		// 关闭海报预览
		closePreview() {
			this.previewVisible = false;
		},
		
		// 头像加载错误处理
		handleAvatarError(e) {
			console.error('头像加载失败:', e);
			// 设置默认头像
			if(this.memberInfo) {
				this.memberInfo.memberAvatar = this.defaultAvatar;
			}
		},
	}
}
</script>

<style lang="scss">
page {
	background-color: #fff;
}

.poster-share-container {
	min-height: 100vh;
	padding-bottom: calc(env(safe-area-inset-bottom) + 200rpx);
	
	/* #ifdef H5 */
	.custom-nav-h5 {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		height: 88rpx;
		background: #fff;
		display: flex;
		align-items: center;
		padding: 0 16rpx;
		z-index: 99;
		box-shadow: 0 1px 0 0 rgba(0,0,0,0.05);
		
		.nav-left {
			width: 56rpx;
			height: 56rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		
		.back_icon {
			font-size: 28rpx;
			color: #333;
		}
	}
	/* #endif */
	
	/* #ifdef MP-WEIXIN */
	.custom-nav-mp {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		background-color: #fff;
		z-index: 999;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding-top: var(--status-bar-height);
		height: 44px;
		box-sizing: content-box;
		
		.nav-left {
			padding-left: 16rpx;
			width: 80rpx;
			height: 44px;
			display: flex;
			align-items: center;
			
			.back_icon {
				font-size: 38rpx;
			}
		}
		
		.nav-title {
			flex: 1;
			text-align: center;
			font-size: 34rpx;
			font-weight: 500;
		}
		
		/* 预留右侧胶囊按钮的空间 */
		.nav-right-placeholder {
			width: 160rpx;
			height: 32px;
		}
	}
	/* #endif */
	
	/* #ifdef MP-WEIXIN */
	.page-content {
		padding: 20rpx 30rpx;
		/* 移除顶部间距，使用小程序原生的导航栏间距 */
		margin-top: 0;
	}
	/* #endif */
	
	/* #ifdef H5 */
	.page-content {
		padding: 20rpx 30rpx;
		margin-top: calc(44px + var(--status-bar-height, 0px));
	}
	/* #endif */
	
	.poster-card {
		position: relative;
		width: 100%;
		background-color: #fff;
		border-radius: 20rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		
		/* 头部区域添加日期显示 */
		.card-header-container {
			position: relative;
			width: 100%;
			
			.card-header {
				width: 100%;
				height: auto;
			}
			
			.card-date {
				position: absolute;
				top: 40rpx;
				right: 30rpx;
				color: #fff;
				display: flex;
				flex-direction: column;
				align-items: flex-end;
				
				.date-label {
					font-size: 24rpx;
					margin-bottom: 4rpx;
				}
				
				.date-value {
					font-size: 28rpx;
				}
			}
		}
		
		/* 用户信息区域 */
		.user-info {
			padding: 30rpx;
			display: flex;
			align-items: center;
			
			.avatar {
				width: 40px;
				height: 40px;
				border-radius: 50%;
				background-color: #f0f0f0; /* 添加背景色作为头像加载前的占位 */
				object-fit: cover;
			}
			
			.user-details {
				margin-left: 20rpx;
				display: flex;
				flex-direction: column;
				
				.nickname {
					font-size: 32rpx;
					font-weight: bold;
					color: #333;
				}
				
				.fans {
					font-size: 24rpx;
					color: #999;
					margin-top: 6rpx;
				}
			}
		}
		
		/* 文章内容区域 */
		.content-text {
			padding: 0 30rpx 30rpx;
			
			.content-title {
				font-size: 36rpx;
				font-weight: bold;
				color: #333;
				margin-bottom: 15rpx;
				display: block;
			}
			
			.content-introduction {
				font-size: 28rpx;
				color: #666;
				line-height: 1.5;
				margin-bottom: 20rpx;
				display: block;
			}
			
			.content-body {
				font-size: 26rpx;
				color: #999;
				line-height: 1.5;
				display: block;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 2;
				overflow: hidden;
			}
		}
		
		/* 图片展示区域 */
		.content-images {
			padding: 0 30rpx 30rpx;
			
			/* 单图展示 */
			&.images-count-1 {
				.single-image {
					width: 100%;
					height: 400rpx;
					border-radius: 20rpx;
					background-color: #f5f5f5;
				}
			}
			
			/* 两图展示 */
			&.images-count-2 {
				display: flex;
				/* #ifndef H5 */
				gap: 10rpx;
				/* #endif */
				
				/* #ifdef H5 */
				/* H5环境下使用margin来代替gap */
				.image-item:first-child {
					margin-right: 10rpx;
				}
				/* #endif */
				
				.image-item {
					/* #ifndef H5 */
					flex: 1;
					/* #endif */
					
					/* #ifdef H5 */
					/* H5环境下使用margin来代替gap */
					width: calc(50% - 5rpx);
					/* #endif */
					
					height: 300rpx;
					
					image {
						width: 100%;
						height: 100%;
						border-radius: 20rpx;
						background-color: #f5f5f5;
					}
				}
			}
			
			/* 三图展示 */
			&.images-count-3 {
				display: flex;
				/* #ifndef H5 */
				gap: 10rpx;
				/* #endif */
				
				/* #ifdef H5 */
				/* H5环境下使用margin来模拟gap效果 */
				.image-item:not(:last-child) {
					margin-right: 10rpx;
				}
				/* #endif */
				
				.image-item {
					/* #ifndef H5 */
					flex: 1;
					/* #endif */
					
					/* #ifdef H5 */
					/* H5环境下使用margin来代替gap */
					width: calc((100% - 20rpx) / 3);
					/* #endif */
					
					height: 250rpx;
					
					image {
						width: 100%;
						height: 100%;
						border-radius: 20rpx;
						background-color: #f5f5f5;
					}
				}
			}
			
			/* 四图及以上展示 */
			&.images-count-4, &.images-count-5, &.images-count-6,
			&.images-count-7, &.images-count-8, &.images-count-9 {
				.image-grid {
					display: flex;
					flex-wrap: wrap;
					/* #ifndef H5 */
					gap: 10rpx;
					/* #endif */
					
					/* #ifdef H5 */
					/* H5环境下使用margin来代替gap */
					margin-right: -10rpx;
					margin-bottom: -10rpx;
					/* #endif */
					
					.image-item {
						/* #ifndef H5 */
						width: calc((100% - 20rpx) / 3);
						height: 200rpx;
						margin-bottom: 5rpx;
						/* #endif */
						
						/* #ifdef H5 */
						width: calc((100% - 30rpx) / 3);
						height: 200rpx;
						margin-right: 10rpx;
						margin-bottom: 10rpx;
						/* #endif */
						
						image {
							width: 100%;
							height: 100%;
							border-radius: 20rpx;
							background-color: #f5f5f5;
						}
					}
					
					.image-more {
						/* #ifndef H5 */
						width: calc((100% - 20rpx) / 3);
						/* #endif */
						
						/* #ifdef H5 */
						width: calc((100% - 30rpx) / 3);
						margin-right: 10rpx;
						margin-bottom: 10rpx;
						/* #endif */
						
						height: 200rpx;
						background-color: rgba(0, 0, 0, 0.5);
						color: #fff;
						border-radius: 20rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						font-size: 40rpx;
					}
				}
			}
		}
		
		/* 二维码区域 */
		.qrcode-section {
			padding: 30rpx;
			display: flex;
			flex-direction: row;
			align-items: center;
			border-top: 1px solid #f5f5f5;
			
			.qrcode-left {
				width: 160rpx;
				height: 160rpx;
				margin-right: 20rpx;
				
				.qrcode {
					width: 100%;
					height: 100%;
					background-color: #f5f5f5;
				}
			}
			
			.qrcode-right {
				flex: 1;
				display: flex;
				flex-direction: column;
				
				.qrcode-tip {
					font-size: 28rpx;
					color: #333;
					margin-bottom: 10rpx;
				}
				
				.qrcode-subtip {
					font-size: 24rpx;
					color: #999;
				}
			}
		}
	}
	
	.loading-container {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 100vh;
		
		.loading-text {
			font-size: 30rpx;
			color: #999;
		}
	}
	
	/* 分享菜单样式 */
	.share-options {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		background-color: #fff;
		border-radius: 20rpx 20rpx 0 0;
		padding: 20rpx 30rpx;
		z-index: 100;
			
		.share-grid {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 20rpx 30rpx;
			
			/* H5单按钮居中样式，不再使用 */
			&.h5-single-button {
				justify-content: center;
				
				.share-option {
					width: auto;
					max-width: none;
					margin: 0 auto;
				}
			}
			
			/* #ifdef MP-WEIXIN */
			button.share-option {
				background: none !important;
				border: none !important;
				padding: 0 !important;
				margin: 0 !important;
				line-height: 1 !important;
				width: 33.33% !important;
				max-width: 200rpx !important;
				height: auto !important;
				font-size: inherit !important;
				
				&::after {
					display: none !important;
				}
				
				&.button-hover {
					background-color: transparent !important;
				}
			}
			/* #endif */
				
			.share-option {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				width: 33.33%;
				max-width: 200rpx;
					
				.share-icon {
					width: 84rpx;
					height: 84rpx;
					margin-bottom: 16rpx;
				}
					
				.share-text {
					font-size: 24rpx;
					color: #333;
					line-height: 1.4;
					text-align: center;
					white-space: nowrap;
				}
			}
		}
		
		/* 新增H5居中按钮样式 */
		.h5-center-button {
			margin: 0 auto !important;
			width: auto !important;
			max-width: none !important;
		}
	}
	
	/* 海报分享按钮样式 */
	.poster-icon {
		width: 42rpx !important;
		height: 42rpx !important;
	}
	
	/* 添加点击保存提示样式 */
	.save-tip {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.6);
		z-index: 10;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 20rpx;
		
		.save-tip-content {
			background-color: rgba(0, 0, 0, 0.7);
			color: #fff;
			padding: 20rpx 30rpx;
			border-radius: 10rpx;
			font-size: 30rpx;
		}
	}
	
	/* 删除之前的预览窗口样式，更新为全屏预览 */
	/* H5环境下的全屏预览 */
	.poster-fullscreen-preview {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.9);
		z-index: 999;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		
		.preview-close-btn {
			position: relative;
			margin-top: 40rpx;
			width: 80rpx;
			height: 80rpx;
			line-height: 70rpx;
			text-align: center;
			background-color: rgba(255, 255, 255, 0.2);
			color: #fff;
			font-size: 60rpx;
			border-radius: 50%;
		}
		
		.preview-tip {
			color: #fff;
			font-size: 28rpx;
			margin-bottom: 30rpx;
		}
		
		.preview-image {
			width: 85%;
			max-width: 750rpx;
			border-radius: 8rpx;
		}
	}
	
	/* 删除原先的poster-preview-modal样式 */
	.poster-preview-modal {
		display: none;
	}
}
</style> 