<template>
	<view :style="mix_diyStyle">
		<view class="main_gra_detail">
			<block v-if="loadingShow">
				<!-- #ifdef H5 -->
				<view class="custom-nav-h5">
					<view class="nav-left" @click="goback">
						<text class="back_icon iconfont iconziyuan2"></text>
					</view>
					<view class="nav-title">{{ $L('图文详情') }}</view>
				</view>
				<!-- #endif -->
				<view class="gra_user">
					<view @tap="goLiveUserCenter" style="display: flex" :data-authorId="authorDetail.authorId">
						<view class="avator" :data-authorId="authorDetail.authorId"
							:style="'background-image:url(' + authorDetail.memberAvatar + ');'"></view>
						<view class="mem_info" :data-authorId="authorDetail.authorId">
							<text class="name">{{
              authorDetail.memberNickname
                ? authorDetail.memberNickname
                : authorDetail.memberName
            }}</text>
							<view class="stat_num">
								<text class="click_num"
									style="margin-right: 20rpx">{{ authorDetail.fansNum }}{{ $L('粉丝') }}</text>
							</view>
						</view>
						<view v-if="!videoDetail.isSelf" class="live_fllow" @tap.stop="collect"
							:class="{live_fllow_no:!authorDetail.isFollow}" :style="'background:' +(authorDetail.isFollow == true ? '#999' : 'var(--color_main)')">
							<image v-if="authorDetail.isFollow == false" :src="imgUrl + 'svideo/fav_a.png'"></image>
							<text>{{authorDetail.isFollow == true ? $L('已关注') : $L('关注')}}</text>
						</view>
					</view>

					<block v-if="authorDetail.isSelf">
						<view class="uni-navbar__content_view" @tap="delGraphic('open')">
							<uni-icons color="rgb(0, 0, 0)" type="trash" size="24" />
						</view>
					</block>
				</view>

				<view class="gra_swpier" v-if="videoDetail.videoImage && videoDetail.videoImage.length > 0">
					<swiper class="swiper-box" :current="current" indicator-dots="true" indicator-color="#d9d9d9"
						indicator-active-color="#FFFFFF" :style="'height:' + videoDetail.maxHeight + 'rpx;'">
						<swiper-item class="swiper-item" v-for="(item, idxA) in videoDetail.videoImage" :key="idxA">
							<view class="gra_pic_item" :style="
                'background-image:url(' +
                item +
                ');background-size:' +
                videoDetail.width +
                'rpx'
              "></view>
						</swiper-item>
					</swiper>
				</view>

				<view class="gra_text">
					<view class="gra_desc">
						<view class="gra_title">{{ videoDetail.videoName }}</view>
						<view class="gra_intro">{{ videoDetail.introduction }}</view>
					</view>
					<view class="gra_content">
						<text>{{ videoDetail.videoContent }}</text>
					</view>
				</view>

				<!-- sss'' -->
				<view class="gra_bottom">
					<view class="left">
						<view :class="{ collect: true, islike: videoDetail.isLike }" @click="like">
							<image :src="imgUrl + 'graphic/col_heart.png'"></image>
						</view>
						<!-- <view class="collect" @click="openComment">
							<image :src="imgUrl + 'graphic/message.png'"></image>
							<view class="messageNum" v-if="videoDetail.commentNum > 0">{{
              videoDetail.commentNum
            }}</view>
						</view> -->
						
						<!-- #ifdef APP-PLUS -->
						<!-- APP环境下的分享按钮 -->
						<view class="collect" @click="showShare">
							<image :src="imgUrl + 'graphic/share.png'"></image>
						</view>
						<!-- #endif -->
						
						<!-- #ifdef H5 -->
						<!-- H5环境下的分享按钮，只在微信浏览器中显示 -->
						<view class="collect" @click="showShare" v-if="isWeiXinBrower">
							<image :src="imgUrl + 'graphic/share.png'"></image>
						</view>
						<!-- #endif -->
						
						<!-- 海报分享按钮，在所有环境都显示 -->
						<view class="collect" @click="goPosterShare">
							<image :src="imgUrl + 'graphic/share.png'" mode="aspectFit" class="poster-icon"></image>
						</view>
					</view>
					<view class="right" @click="openGoods">
						<view class="goods_btn">{{ $L('文中商品') }}({{ goodsList.length }})</view>
					</view>
				</view>
				<!-- ''' -->
			</block>

			<uni-popup ref="goodsModel" type="bottom" @touchmove.stop.prevent="moveHandle">
				<view class="goods_list_con">
					<view class="goods_top">
						<view class="goods_top_text">{{ $L('文中商品') }}({{ goodsList.length }})</view>
						<image :src="imgUrl + 'goods_detail/close.png'" mode="" @click="goodsModelClose"></image>
					</view>
					<scroll-view scroll-y="true" class="goods_list">
						<graGoodsItem v-for="(item, index) in goodsList" :key="index" :item="item"
							:canAdd="videoDetail.state == 2 ? true : false"></graGoodsItem>
						<loadingState v-if="loadingState == 'first_loading' || goodsList.length > 0"
							:state="loadingState" />
					</scroll-view>
				</view>
			</uni-popup>

			<uni-popup ref="commentModel" type="bottom" @change="changePop">
				<comment :videoDetail="videoDetail" ref="comment" @close="closeComment" :videoId="video_id"
					@updateCommentNum="updateCommentNum"></comment>
			</uni-popup>

			<view class="select-wrap" catchtouchmove="touchmoveshare" v-if="shareWrap">
				<view class="share-mode">
					<view class="share-img"></view>
					<view class="ul">
						<!-- app-1-start -->










						<!-- app-1-end -->

						<!-- #ifdef  H5 -->
						<button @tap.stop="sldShareBrower(1)" class="item" v-if="isWeiXinBrower">
							<image :src="imgUrl + 'goods_detail/wx_share.png'" mode="widthFix"></image>
							<text>{{ $L('微信好友') }}</text>
						</button>
						<button @tap.stop="sldShareBrower(2)" class="item" v-if="isWeiXinBrower">
							<image :src="imgUrl + 'svideo/pyq_share.png'" mode="widthFix"></image>
							<text>{{ $L('微信朋友圈') }}</text>
						</button>
						<!-- #endif -->
						<!-- wx-2-start -->
						<!-- #ifdef MP -->
						<button open-type="share" @click="shareWrap = false" class="item">
							<view class="img_con flex_column_between_center">
								<image :src="imgUrl + 'rank/wx_share.png'" mode="aspectFit"></image>
								<text>{{ $L('微信好友') }}</text>
							</view>
						</button>
						<!-- #endif -->
						<!-- wx-2-end -->
					</view>

					<view class="close" @tap="closeShare">
						<image :src="imgUrl + 'svideo/share_close2.png'" mode="aspectFit"></image>
					</view>
				</view>
			</view>
			<view class="wx_brower_share_mask" v-if="showWeiXinBrowerTip">
				<view class="wx_brower_share_top_wrap">
					<image :src="imgUrl + 'wx_share_tip.png'" mode="widthFix" @tap="closeShare"
						class="wx_brower_share_img">
					</image>
				</view>
			</view>

			<uni-popup ref="popup" type="dialog">
				<uni-popup-dialog type="input" :title="$L('提示')" :content="$L('确定要删除吗?')" :duration="2000"
					@confirm="delGraphic('confirm')"></uni-popup-dialog>
			</uni-popup>
			
			
			<loginPop ref="loginPop"></loginPop>
		</view>
	</view>
</template>

<script>
	import loadingState from '@/components/loading-state.vue'
import loginPop from '@/components/loginPop/loginPop.vue'
import uniIcons from '@/components/uni-icons/uni-icons.vue'
import uniNavBar from '@/components/uni-nav-bar/uni-nav-bar.vue'
import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue'
import uniPopup from '@/components/uni-popup/uni-popup.vue'
import uniSwiperDot from '@/components/uni-swiper-dot/uni-swiper-dot.vue'
import {
mapState
} from 'vuex'
import comment from '../component/graphic/comment.vue'
import graGoodsItem from '../component/graphic/graGoodItem.vue'
	export default {
		components: {
			uniNavBar,
			uniSwiperDot,
			uniPopup,
			graGoodsItem,
			comment,
			loadingState,
			uniPopupDialog,
			uniIcons,
			loginPop
		},
		data() {
			return {
				imgUrl: process.env.VUE_APP_IMG_URL,
				member_avatar: '',
				current: 0,
				goodsList: [],
				videoDetail: {},
				authorDetail: {},
				roleType: '',
				video_id: '',
				goodsMore: true,
				loadingState: '',
				shareWrap: false,
				showWeiXinBrowerTip: false,
				isWeiXinBrower: false,
				iconSelf: '',
				isShow: false,
				index: null, //当前操作的图文下标
				loadingShow: false,
				preUpdateIndex: -1 //是否需要更新上一个页面的数据 -- 取操作的列表下标
			}
		},
		async onLoad(options) {
			await this.$handleFx()
			setTimeout(() => {
				uni.setNavigationBarTitle({
					title: this.$L('图文详情')
				})
			}, 0);
			if (this.$Route.query.index!==undefined) {
				this.preUpdateIndex = this.$Route.query.index
			}
			if (this.$Route.query.scene) {
				let url = decodeURIComponent(this.$Route.query.scene)
				this.video_id = url.split('=')[1]
			}
			this.video_id = this.$Route.query.video_id
			this.roleType = this.$Route.query.roleType
			this.index = this.$Route.query.index
			this.getVideoInfo()
			// #ifdef H5
			this.isWeiXinBrower = this.$isWeiXinBrower()
			// #endif
		},
		onShow() {
			if (this.isShow) {
				this.getVideoInfo()
			}
		},
		computed: {
			...mapState(['hasLogin'])
		},
		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage: function(options) {
			let {
				videoDetail,
				video_id
			} = this
			return {
				title: videoDetail.videoName,
				path: '/extra/graphic/graphicDetail?video_id=' + video_id,
				imageUrl: videoDetail.videoImage
			}
		},
		onShareTimeline: function(options) {
			let {
				videoDetail,
				video_id
			} = this
			return {
				title: videoDetail.videoName,
				query: 'video_id=' + video_id,
				imageUrl: videoDetail.videoImage
			}
		},
		methods: {
			openGoods() {
				if (this.goodsList.length > 0) {
					this.$refs.goodsModel.open()
				}
			},
			goback() {
				this.$Router.back(1)
			},
			//获取短视频详情
			getVideoInfo() {
				let {
					video_id
				} = this
				let _this = this
				let param = {}
				param.data = {}
				param.url = 'v3/video/front/video/playingPage'
				param.method = 'GET'
				param.data.videoId = this.video_id
				this.$request(param).then((res) => {
					if (res.state == 200) {
						this.loadingState = 'no_more_data'
						this.goodsList = res.data.goodsList

						// 处理图文轮播图宽高适配
						let w = 375 //默认宽沾满 750rpx
						let h = (res.data.videoInfo.height / res.data.videoInfo.width) * 375
						let maxHeight = h > 500 ? 500 : h //高最大为 1000rpx
						res.data.videoInfo.width = w * 2
						res.data.videoInfo.height = h * 2
						res.data.videoInfo.maxHeight = maxHeight * 2

						this.videoDetail = res.data.videoInfo
						this.member_avatar = res.data.memberAvatar
						if (this.videoDetail.state == 1) {
							this.loadingShow = true
						} else if (this.videoDetail.state == 2) {
							//更新视频点击量
							this.loadingShow = true
							// 确保数据加载完成后再更新点击量
							if(this.videoDetail && typeof this.videoDetail.clickNum !== 'undefined') {
								this.updateVideoClick()
							}
						} else if (this.videoDetail.state == 3) {
							uni.showModal({
								title: '',
								content: this.$L('审核失败，') + this.videoDetail.remark,
								showCancel: true,
								confirmText: this.$L('重新编辑'),
								confirmColor: '#FC1C1C',
								success: (res) => {
									if (res.confirm) {
										this.isShow = true
										_this.$Router.replace({
											path: '/extra/graphic/graphicRelease',
											query: {
												video_id: this.video_id,
												roleType: this.roleType,
												index: this.index
											}
										})
									} else {
										_this.$Router.back(1)
									}
								}
							})
						} else if (this.videoDetail.state == 4) {
							if (!this.videoDetail.isSelf) {
								this.$api.msg(this.$L('该图文已禁止显示'))
								this.$Router.back(1)
							} else {
								uni.showModal({
									title: '',
									content: this.$L('禁止显示，') + this.videoDetail.remark,
									showCancel: true,
									confirmText: this.$L('重新编辑'),
									cancelText: this.$L('取消'),
									confirmColor: '#FC1C1C',
									success: (res) => {
										if (res.confirm) {
											this.isShow = true
											_this.$Router.replace({
												path: '/extra/graphic/graphicRelease',
												query: {
													video_id: this.video_id
												}
											})
										} else {
											_this.$Router.back(1)
										}
									}
								})
							}
						}
						this.authorDetail = res.data.authorInfo
					} else if (res.state == 267) {
						uni.showModal({
							title: this.$L('提示'),
							content: res.msg,
							confirmText: this.$L('确定'),
							cancelText: this.$L('取消'),
							success: (res) => {
								if (res.confirm) {
									// uni.navigateBack()
									_this.$Router.back(1)
								} else {
									// uni.navigateBack()
									_this.$Router.back(1)
								}
							}
						})
					}
				})
			},
			//更新视频点击量
			updateVideoClick() {
				let param = {}
				param.data = {}
				param.url = 'v3/video/front/video/updateClickNum'
				param.method = 'POST'
				param.data.videoId = this.video_id
				this.$request(param).then((res) => {
					if (res.state == 200) {
						// 确保所有必要数据都存在且有效
						if(this.videoDetail && 
							this.preUpdateIndex > -1 && 
							typeof this.videoDetail.clickNum !== 'undefined') {
							uni.$emit('updateView', {
								index: this.preUpdateIndex,
								data: {
									clickNum: this.videoDetail.clickNum,
									videoId: this.video_id
								}
							})
						}
					}
				})
			},
			goodsModelClose() {
				this.$refs.goodsModel.close()
			},
			//点赞事件
			like() {
				if (this.videoDetail.state == 3) {
					this.$api.msg(this.$L('该图文未审核通过,不能点赞哦～'))
					return
				} else if (this.videoDetail.state == 4) {
					this.$api.msg(this.$L('该图文已下架,不能点赞哦～'))
					return
				} else if (this.videoDetail.state == 1) {
					this.$api.msg(this.$L('该图文正在审核中,不能点赞哦～'))
					return
				}
				let {
					video_id,
					videoDetail
				} = this
				if (this.hasLogin) {
					let param = {}
					param.data = {}
					param.url = 'v3/video/front/video/clickPraise'
					param.method = 'POST'
					param.data.videoId = this.video_id
					this.$request(param).then((res) => {
						if (res.state == 200) {
							if (res.state == 200) {
								videoDetail.isLike = videoDetail.isLike == true ? false : true
								videoDetail.likeNum = res.data.likeNum
								this.videoDetail = videoDetail
								if (this.preUpdateIndex > -1) {
									uni.$emit('updateLike', videoDetail.isLike)
								}
							} else {
								uni.showToast({
									title: res.msg,
									icon: 'none'
								})
							}
						}
					})
				} else {
					this.$refs.loginPop.openLogin('no_replace')
				}
			},
			openComment() {
				if (this.videoDetail.state == 3) {
					this.$api.msg(this.$L('该图文未审核通过,不能评论哦～'))
					return
				} else if (this.videoDetail.state == 4) {
					this.$api.msg(this.$L('该图文已下架,不能评论哦～'))
					return
				} else if (this.videoDetail.state == 1) {
					this.$api.msg(this.$L('该图文正在审核中,不能评论哦～'))
					return
				}
				this.$refs.commentModel.open()
			},
			changePop(e) {
				if (e.show) {
					this.$refs.comment.getCommentList()
				}
			},
			//关闭评论
			closeComment() {
				this.$refs.commentModel.close()
			},
			// 更新评论数量
			updateCommentNum(val) {
				this.videoDetail.commentNum = val
			},
			//关闭分享
			closeShare() {
				this.shareWrap = false
				this.showWeiXinBrowerTip = false //微信浏览器提示层
			},

			//分享 type：分享类型 0 图文 2图片，scene 场景 WXSceneSession：分享朋友  WXSenceTimeline：分享朋友圈
			sldShare(type, scene) {
				let {
					videoDetail,
					video_id
				} = this
				let shareData = {}
				if (type == 0) {
					shareData.href =
						process.env.VUE_APP_API_URL +
						'extra/graphic/graphicDetail?video_id=' +
						video_id
					shareData.title = videoDetail.videoName
					shareData.summary =
						this.$L('我正在看') + this.authorDetail.memberNickname ?
						this.authorDetail.memberNickname :
						this.authorDetail.memberName + this.$L('的精彩内容，快来围观~')
					shareData.imageUrl = videoDetail.videoImage
				}
				this.$weiXinAppShare(type, scene, shareData)
				this.closeShare() //关闭分享
			},

			//浏览器分享
			sldShareBrower(type) {
				let {
					videoDetail,
					video_id
				} = this
				//展示分享提示
				this.showWeiXinBrowerTip = true
				this.shareWrap = false

				this.$WXBrowserShareThen(type, {
					title: videoDetail.videoName,
					desc: this.$L('我正在看') + this.authorDetail.memberNickname ?
						this.authorDetail.memberNickname :
						this.authorDetail.memberName + this.$L('的精彩内容，快来围观~'),
					link: process.env.VUE_APP_API_URL +
						'extra/graphic/graphicDetail?video_id=' +
						video_id,
					imgUrl: videoDetail.videoImage[0]
				})
			},

			//进入作者页面
			goLiveUserCenter(e) {
				let author_id = e.currentTarget.dataset.authorid
				let page = getCurrentPages()
				let len = page.length
				if (len > 4) {
					this.isShow = true
					this.$Router.replace({
						path: '/extra/user/my',
						query: {
							author_id
						}
					})
				} else {
					this.isShow = true
					this.$Router.push({
						path: '/extra/user/my',
						query: {
							author_id
						}
					})
				}
			},

			showShare() {
				if (this.videoDetail.state != 2) {
					this.$api.msg(this.$L('该作品未审核通过,无法分享～'))
					return
				}

				// #ifdef H5
				if (this.isWeiXinBrower) {
					this.shareWrap = true
				}
				// #endif
				// #ifdef APP-PLUS || MP-WEIXIN
				this.shareWrap = true
				// #endif
			},
			delGraphic(type) {
				switch (type) {
					case 'open': {
						this.$refs.popup.open()
						break
					}
					case 'confirm': {
						this.$refs.popup.close()
						let param = {}
						param.data = {}
						param.data.videoId = this.video_id
						param.url = 'v3/video/front/video/delVideo'
						param.method = 'POST'
						this.$request(param).then((res) => {
							if (res.state == 200) {
								if (this.this.preUpdateIndex > -1) {
									uni.$emit('updateState')
								}
								this.$api.msg(res.msg)
								this.$Router.back(1)
							} else {
								this.$api.msg(res.msg)
							}
						})
					}
				}
			},
			//关注、取消关注事件
			collect(e) {
				let {
					authorDetail
				} = this
				if (this.hasLogin) {
					let param = {}
					param.data = {}
					param.method = 'POST'
					param.data.authorId = this.authorDetail.authorId
					if (this.authorDetail.isFollow) {
						param.url = 'v3/video/front/video/cancelFollow'
					} else {
						param.url = 'v3/video/front/video/followAuthor'
					}
					this.$request(param).then((res) => {
						if (res.state == 200) {
							this.authorDetail.isFollow =
								this.authorDetail.isFollow == true ? false : true
							// if (this.authorDetail.isFollow) {
							// 	this.authorDetail.fansNum += 1
							// } else {
							// 	this.authorDetail.fansNum -= 1
							// }
							this.getVideoInfo()
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					})
				} else {
					this.$refs.loginPop.openLogin('no_replace')
				}
			},
			goPosterShare() {
				// 检查视频状态
				if (this.videoDetail.state != 2) {
					this.$api.msg(this.$L('该作品未审核通过,无法生成海报～'))
					return
				}
				
				// 检查用户是否已登录
				if (this.hasLogin) {
					// 使用Router组件方法替代直接使用uni.navigateTo
					this.$Router.push({
						path: '/extra/graphic/posterShare',
						query: {
							video_id: this.video_id
						}
					})
				} else {
					// 使用登录回调，确保登录后可以继续执行海报分享操作
					this.$refs.loginPop.openLogin('no_replace', () => {
						this.$Router.push({
							path: '/extra/graphic/posterShare',
							query: {
								video_id: this.video_id
							}
						})
					})
				}
			}
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #fff;
	}

	/* #ifdef H5 */
	.custom-nav-h5 {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		height: 88rpx;
		background: #fff;
		display: flex;
		align-items: center;
		padding: 0 16rpx;
		z-index: 99;
		
		.nav-left {
			width: 56rpx;
			height: 56rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		
		.back_icon {
			font-size: 28rpx;
			color: #000;
		}
		
		.nav-title {
			flex: 1;
			text-align: center;
			font-size: 32rpx;
			color: #000;
			font-weight: bold;
		}
	}
	
	.main_gra_detail {
		padding-top: 88rpx;
	}
	/* #endif */

	.avator {
		width: 78rpx;
		height: 78rpx;
		border-radius: 50%;
		margin-left: 8rpx;
		background-size: contain;
		background-position: center center;
		background-repeat: no-repeat;
		background-color: #f7f7f7;
	}

	// .swiper-box {
	// 	height: 350rpx;
	// }

	.gra_pic_item {
		background-position: center center;
		background-repeat: no-repeat;
		background-size: contain;
		width: 100%;
		height: 100%;
	}

	.shareButton {
		display: flex;
		flex-direction: column;
		align-items: center;
		background: transparent;
		border-radius: 0;
		height: auto;
		line-height: unset;
		padding: 0;
		margin: 0;

		&::after {
			border-width: 0;
		}
	}

	.swiper-item {
		background-color: #f5f5f5;
	}

	.mem_info {
		margin-left: 20rpx;
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		justify-content: space-between;

		.name {
			max-width: 350rpx;
			font-size: 32rpx;
			font-family: PingFang;
			font-weight: bold;
			line-height: 32rpx;
			margin-bottom: 15rpx;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}

		.stat_num {
			display: flex;
			flex-direction: row;
			justify-content: flex-start;

			text {
				font-size: 22rpx;
				font-family: PingFang;
				color: #666666;
				line-height: 36rpx;
				white-space: nowrap;
				font-weight: 600;
				opacity: 0.6;
			}
		}
	}

	.gra_user {
		position: relative;
		display: flex;
		padding: 20rpx;
		align-items: center;

		.uni-navbar__content_view {
			position: absolute;
			right: 30rpx;
			top: 50%;
			transform: translateY(-50%);

			display: flex;

			align-items: center;
			flex-direction: row;
			// background-color: #FFFFFF;
		}
	}

	.live_fllow {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		width: 120rpx;
		height: 50rpx;
		background: #fc1c1c;
		border-radius: 25rpx;
		margin-left: 25rpx;

		image {
			width: 46rpx;
			height: 46rpx;
		}

		text {
			color: #fff;
			font-size: 24rpx;
			margin-right: 8rpx;
		}
	}

	.live_fllow_no {
		background: var(--color_video_main) !important;
	}

	.gra_text {
		margin-top: 20rpx;
		padding-top: 10rpx;
		padding-left: 30rpx;
		padding-right: 30rpx;
		padding-bottom: calc(env(safe-area-inset-bottom) + 140rpx);
		z-index: 10;

		.gra_desc {
			padding-bottom: 20rpx;
			border-bottom: 1px solid #f7f7f7;

			.gra_title {
				font-size: 38rpx;
				font-family: PingFang;
				font-weight: bold;
				color: #333333;
				margin-bottom: 16rpx;
			}

			.gra_intro {
				font-size: 30rpx;
				font-family: PingFang;
				font-weight: 500;
				color: #666666;
			}
		}

		.gra_content {
			color: #333333;
			line-height: 42rpx;
			font-size: 28rpx;
			font-family: PingFang;
			font-weight: 400;
			margin-top: 20rpx;
			word-break: break-all;
		}
	}

	.gra_bottom {
		position: fixed;
		bottom: 0;
		width: 100%;
		height: calc(120rpx + env(safe-area-inset-bottom));
		padding: 20rpx 20rpx;
		box-shadow: 0px 0px 29rpx 1px rgba(86, 86, 86, 0.08);
		padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
		display: flex;
		align-items: center;
		justify-content: space-between;
		background-color: #fff;

		.left {
			display: flex;

			.collect {
				width: 78rpx;
				height: 78rpx;
				background: #040404;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-left: 20rpx;
				position: relative;

				image {
					width: 42rpx;
					height: 42rpx;
				}

				.messageNum {
					font-size: 22rpx;
					background-color: var(--color_video_main);
					color: #fff;
					border-radius: 16rpx;
					position: absolute;
					right: -4rpx;
					top: -4rpx;
					padding: 2rpx 6rpx;
				}
			}

			.islike {
				background: var(--color_video_main) !important;
			}
		}

		.right {
			width: 242rpx;
			height: 78rpx;
			background: var(--color_video_main_bg);
			border-radius: 39rpx;
			font-size: 30rpx;
			font-family: PingFang;
			font-weight: 500;
			color: #ffffff;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}

	.goods_list_con {
		border-radius: 5px 5px 0;
	}

	.goods_top {
		padding: 20rpx 30rpx;
		border-radius: 5px 5px 0 0;
		font-size: 32rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: #333333;
		display: flex;
		justify-content: space-between;
		align-items: center;
		background: #fff;
		border-bottom: 0.5px solid #f2f2f2;

		image {
			width: 50rpx;
			height: 50rpx;
		}
	}

	.goods_list {
		background-color: #fff;
		height: 900rpx;
	}

	.select-wrap {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		margin: 0 auto;
		width: 750rpx;
		height: 100vh;
		background-color: rgba(0, 0, 0, 0.45);
		z-index: 9999;

		.share-mode {
			position: absolute;
			width: 100%;
			height: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;

			.share-img {
				width: 72vw;
				border-radius: 20rpx;
				overflow: hidden;
				flex: 1;
				display: flex;
				align-items: center;
				justify-content: center;

				image {
					width: 100%;
					height: 0;
					border-radius: 20rpx;
				}
			}

			.ul {
				display: flex;
				align-items: center;
				width: 100%;
				justify-content: space-evenly;
			}

			.item {
				display: flex;
				flex-direction: column;
				align-items: center;
				text-align: center;
				cursor: pointer;
				border: none;
				margin: 0;
				padding: 0;
				line-height: 1;
				background-color: transparent;
				height: 180rpx;
				width: 122rpx;

				&::after {
					border: none;
				}

				image {
					width: 112rpx;
					height: 112rpx;
				}

				text {
					color: #fff;
					font-size: 24rpx;
					margin-top: 30rpx;
				}
			}
		}

		.close {
			width: 750rpx;
			height: 140rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			image {
				/* wx-1-start */
				/* #ifdef MP-WEIXIN */
				width: 26rpx;
				height: 26rpx;
				/* #endif */
				/* wx-1-end */
				/* #ifndef MP-WEIXIN */
				width: 18rpx;
				height: 18rpx;
				padding: 20rpx;
				/* #endif */
				opacity: unset;
			}

			uni-image>img {
				opacity: unset !important;
			}
		}
	}

	/* 海报分享按钮样式 */
	.poster-icon {
		width: 42rpx !important;
		height: 42rpx !important;
	}


</style>