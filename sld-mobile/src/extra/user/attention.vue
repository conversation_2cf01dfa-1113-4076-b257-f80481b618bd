
<template>
	<view :style="mix_diyStyle">
  <view class="empty-w">
    <view class="live_fllow_tab">
      <text
        v-for="(item, index) in tabData"
        :key="index"
        :class="{ sel: curTabIndex == item.index }"
        @click="changeTab(item.index)"
        >{{ item.name }}</text
      >
    </view>

    <scroll-view scroll-y class="fllow_list" v-if="list.length">
      <!-- item -->
      <videoFollow
        :list="list"
        :bgStyle="bgStyle"
        :showFans="false"
        @collect="collect"
      />
      <!-- 数据加载完毕 -->
      <dataLoaded :showFlag="!hasmore && list.length > 0" />

      <!-- 数据加载中 -->
      <dataLoading :showFlag="hasmore && loading" />

      <view class="empty_h"></view>
    </scroll-view>

    <!-- 页面loading -->
    <pageLoading
      :firstLoading="firstLoading"
      :loadingIcon="imgUrl + 'svideo/page_loading_icon.gif'"
    />

    <!-- 页面空数据 -->
    <emptyData
      :showFlag="!firstLoading && !list.length"
      :emptyIcon="imgUrl + 'svideo/live_list_empty_icon.png'"
    />
  </view>
	</view>
</template>

<script>
import { checkPageHasMore } from '@/utils/live'
import request from '@/utils/request'
import pageLoading from '../component/pageLoading.vue'
import emptyData from '../component/emptyData.vue'
import dataLoading from '../component/dataLoading.vue'
import dataLoaded from '../component/dataLoaded.vue'
import videoFollow from '../component/video/videoFollow.vue'

export default {
  components: {
    pageLoading,
    emptyData,
    dataLoading,
    dataLoaded,
    videoFollow
  },
  data() {
    return {
      tabData: [
        {
          index: 1,
          name: this.$L('关注')
        },
        {
          index: 2,
          name: this.$L('粉丝')
        }
      ],
      //tab 数据
      curTabIndex: 1,
      //当前tabindex
      list: [],
      //当前列表数据
      hasmore: true,
      //是否还有数据，用于页面展示
      loading: false,
      firstLoading: true,
      //是否初次加载，是的话展示页面居中的loading效果，
      imgUrl: process.env.VUE_APP_IMG_URL,
      //图片地址
      bgStyle:
        'background-size:contain;background-position:center center;background-repeat: no-repeat;', //背景图片样式
      pn: 1,
      pageSize: 10
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    setTimeout(()=>{
     uni.setNavigationBarTitle({
        title: this.$L('关注/粉丝')
      }) 
    },0);
    
    if (this.$Route.query.type != undefined && this.$Route.query.type) {
      this.curTabIndex = this.$Route.query.type
    }

    this.getList()
  },

  onReachBottom() {
    if (this.hasmore) {
      this.getList()
    }
  },

  methods: {
    //获取数据
    getList() {
      let { list, firstLoading, loading, hasmore, curTabIndex } = this
      this.loading = true
      let param = {}
      param.data = {
        pageSize: this.pageSize,
        current: this.pn,
        followType: curTabIndex
      }
      param.url = 'v3/video/front/video/author/followList'
      param.method = 'GET'
      this.$request(param).then((res) => {
        if (res.state == 200) {
          let result = res.data.list
          if (this.pn == 1) {
            this.list = result
          } else {
            this.list = this.list.concat(result)
          }
          if (checkPageHasMore(res.data.pagination)) {
            this.pn++
          } else {
            this.hasmore = false
            hasmore = false
          }
        }

        if (this.firstLoading) {
          this.firstLoading = false
        }
        this.loading = false
      })
    },

    //tab切换事件
    changeTab(index) {
      this.curTabIndex = index
      this.firstLoading = true
      this.list = []
      this.pn = 1
      this.hasmore = true
      this.getList()
    },

    //关注、取消关注事件
    collect(e) {
      let param = {}
      if (e.isFollow) {
        //取消关注
        param.url = 'v3/video/front/video/cancelFollow'
      } else {
        //关注
        param.url = 'v3/video/front/video/followAuthor'
      }
      param.method = 'POST'
      param.data = {}
      param.data.authorId = e.author_id
      this.$request(param).then((res) => {
        if (res.state == 200) {
          uni.showToast({
            title: res.msg,
            icon: 'none'
          })
          this.pn = 1
          this.getList()
        }
      })
    }
  }
}
</script>
<style>
page {
  background: #f8f8f8;
}
.empty_data {
  width: 750rpx;
  margin-left: calc((100vw - 750rpx) / 2);
}
.live_fllow_tab {
  width: 750rpx;
  height: 80rpx;
  background: #fff;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;
  margin: 0 auto;
}

.live_fllow_tab text {
  color: #2d2d2d;
  font-size: 30rpx;
  line-height: 80rpx;
  border-bottom: 2px solid #fff;
}

.live_fllow_tab text.sel {
  color: #fc1c1c;
  border-bottom: 2px solid #ec1313;
  font-weight: bold;
}

.fllow_list {
  margin-top: 20rpx;
}

.empty_h {
  height: 100rpx;
  width: 100%;
  background-color: 'transpanrent';
}
</style>
