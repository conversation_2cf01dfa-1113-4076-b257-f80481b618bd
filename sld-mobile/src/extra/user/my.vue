<!-- 我的视频 -->
<template>
	<view :style="mix_diyStyle">
		<view class="author_info"
			:style="'background-image:url(' + authorInfo.backgroundImage + ');height:' + topHeight + ';'">
			<!-- wx-3-start -->
			<!-- #ifdef MP -->
			<view class="" :style="{ paddingTop: menuButtonTop }">
				<view class="nav-bar" :style="{ height: menuButtonHeight }">
					<image :src="imgUrl + 'tshou/back_icon.png'" mode="" @click="$back"></image>
					<view class="">我的视频</view>
				</view>
			</view>
			<!-- #endif -->
			<!-- wx-3-end -->
			<view class="info">
				<!-- wx-4-start -->
				<!-- #ifdef MP -->
				<image class="author_avator" :src="authorInfo.memberAvatar" mode="aspectFill"
					v-if="authorInfo && authorInfo.memberAvatar"></image>
				<!-- #endif -->
				<!-- wx-4-end -->
				<!-- #ifdef H5 -->
				<view class="author_avator" :style="'background-image:url(' + authorInfo.memberAvatar + ');' + bgStyle">
				</view>
				<!-- #endif -->
				<!-- app-1-start -->





				<!-- app-1-end -->
				<view class="right_info">
					<view class="member_info">
						<text class="author_name">{{ authorInfo.memberNickname ? authorInfo.memberNickname :
							authorInfo.memberName }}
						</text>

						<view v-if="JSON.stringify(authorInfo) != '{}' && authorInfo.isSelf" @tap="editMemInfo"
							class="edit_mem_info">
							<text>{{ $L('编辑资料') }}</text>
							<image :src="imgUrl + 'svideo/edit_mem_info.png'"></image>
						</view>

						<view v-if="JSON.stringify(authorInfo) != '{}' && !authorInfo.isSelf" class="fav_info"
							:style="'background:' + (authorInfo.isFollow ? '#999999' : '#FC1C1C')"
							:class="{ 'fav_info_bg': !authorInfo.isFollow }" @tap="collect">
							<image v-if="!authorInfo.isFollow" :src="imgUrl + 'svideo/fav_a.png'"></image>
							<text>{{ !authorInfo.isFollow ? $L('关注') : $L('已关注') }}</text>
						</view>

						<block v-if="setting.video_switch == 1 && authorInfo.isSelf">
							<view class="msg_wrap" @tap="goComments">
								<image :src="imgUrl + 'svideo/msg.png'"></image>
								<text v-if="authorInfo.msgNum > 0">{{ authorInfo.msgNum > 99 ? '99+' :
									authorInfo.msgNum }}</text>
							</view>
						</block>

						<!-- 						<block v-if="authorInfo.isSelf">
							<navigator :url="'/pages/shopHomePage/shopHomePage?vid=' + authorInfo.vid">
								<view class="go_vendor">
									<text>进店</text>
								</view>
							</navigator>
						</block> -->
					</view>
					<block v-if="authorInfo.isSelf">
						<!-- 发布权限状态:0-默认，1-待审核，2-审核通过(正常发布)，3-审核拒绝，4-禁止发布 -->
						<text v-if="authorInfo.permissionState == 0" @tap="applicationTip($L('是否立即申请发布权限'))"
							class="check_tip">{{ authorInfo.permissionStateValue }}></text>
						<text v-if="authorInfo.permissionState == 1" @tap="applicationShowTip($L('审核中，请耐心等待'))"
							class="check_tip">{{ authorInfo.permissionStateValue }}></text>
						<text v-if="authorInfo.permissionState == 3"
							@tap="applicationTip(authorInfo.permissionStateValue + '：' + authorInfo.remark)"
							class="check_tip">{{ authorInfo.permissionStateValue }}></text>
						<text
							v-if="authorInfo.permissionState == 4 || (authorInfo.roleType == 2 && authorInfo.liveState == 0)"
							@tap="applicationShowTip(authorInfo.permissionStateValue + '：' + authorInfo.remark ? authorInfo.remark : authorInfo.forbidReason)"
							class="check_tip">{{ authorInfo.permissionStateValue }}</text>
						<!-- 禁止直播 -->
						<!-- <text v-if="authorInfo.roleType == 2 && authorInfo.liveState == 0" @tap="applicationShowTip('禁止发布' + '：' + authorInfo.remark)" class="check_tip">{{authorInfo.permissionStateValue}}</text> -->
					</block>
				</view>
			</view>
			<view class="author_desc" @tap="editMemInfo">
				{{ authorInfo.introduction ? authorInfo.introduction : !authorInfo.isSelf ? $L('这个人很懒，什么都没有写') : $L('请设置个人简介~') }}
			</view>
			<view class="stat_num">
				<view data-index="follow"
					:data-title="authorInfo.memberNickname ? authorInfo.memberNickname : authorInfo.memberName">
					<text class="num">{{ authorInfo && authorInfo.likeNum ? authorInfo.likeNum : '0' }}</text>
					<text class="desc">{{ $L('获赞') }}</text>
				</view>
				<view
					@click="goAttention(1, authorInfo.memberNickname ? authorInfo.memberNickname : authorInfo.memberName)">
					<text class="num">{{ authorInfo && authorInfo.followNum ? authorInfo.followNum : '0' }}</text>
					<text class="desc">{{ $L('关注') }}</text>
				</view>
				<view
					@click="goAttention(2, authorInfo.memberNickname ? authorInfo.memberNickname : authorInfo.memberName)">
					<text class="num">{{ authorInfo && authorInfo.fansNum ? authorInfo.fansNum : '0' }}</text>
					<text class="desc">{{ $L('粉丝') }}</text>
				</view>
			</view>

			<view class="tab"
				:style="'justify-content:' + ((memberInfo.is_own != 1 && !(setting.live_switch == 1 && authorInfo.role_type == 2)) || settingData.video_switch != 1 ? 'space-around' : 'space-between')">
				<block v-if="true">
					<text :class="curTab == 'video' ? 'sel' : ''" @tap="changeTab('video')">{{ $L('动态') }}({{ authorInfo &&
						authorInfo.videoNum ? authorInfo.videoNum * 1 > 99 ? '99+' : authorInfo.videoNum : '0' }})</text>
				</block>
				<block v-if="setting.live_switch == 1 && authorInfo.roleType == 2">
					<text :class="curTab == 'live' ? 'sel' : ''" @tap="changeTab('live')" data-index="live">{{ $L('直播')
					}}({{ authorInfo && authorInfo.liveNum ? authorInfo.liveNum * 1 > 99 ? '99+' : authorInfo.liveNum :
	'0' }})</text>
				</block>
				<text :class="curTab == 'goods' ? 'sel' : ''" @tap="changeTab('goods')" data-index="goods">{{ $L('商品')
				}}({{ authorInfo && authorInfo.goodsNum ? authorInfo.goodsNum * 1 > 99 ? '99+' : authorInfo.goodsNum :
	0 }})</text>
				<block v-if="authorInfo.isSelf">
					<text :class="curTab == 'favorite' ? 'sel' : ''" @tap="changeTab('favorite')"
						data-index="favorite">{{
						$L('喜欢') }}({{ authorInfo && authorInfo.likeVideoNum ? authorInfo.likeVideoNum * 1 > 99 ? '99+' :
		authorInfo.likeVideoNum : '0' }})</text>
				</block>
			</view>

		</view>

		<view class="live_user_tab_content_main" :style="{ top: contentTop }">
			<!-- 动态模块 -->
			<userVideo v-if="curTab == 'video'" :memberInfo="memberInfo" :author_id="author_id" ref="video"
				:settingData="settingData" :authorInfo="authorInfo" @liveEvent="liveEvent"
				@getAuthorInfo="getAuthorInfo">
			</userVideo>

			<!-- 直播模块 -->
			<userLive v-if="curTab == 'live' && setting.live_switch == 1" :memberInfo="memberInfo"
				:author_id="author_id" :settingData="settingData" ref="live" :authorInfo="authorInfo"
				@liveEvent="liveEvent"></userLive>

			<!-- 商品模块 -->
			<userGoods v-if="curTab == 'goods'" :memberInfo="memberInfo" :author_id="author_id"
				:settingData="settingData" ref="goods" :authorInfo="authorInfo"></userGoods>

			<!-- 喜欢模块 -->
			<userFavorite v-if="curTab == 'favorite'" :memberInfo="memberInfo" :settingData="settingData"
				:authorInfo="authorInfo" ref="favorite"></userFavorite>
		</view>
		<!-- #ifdef H5 -->
		<!-- 发布按钮，目前只有发布直播 -->
		<block v-if="authorInfo.isSelf">
			<view class="release">
				<view class="release_img flex_row_center_center" @click="release" data-flag="true">
					<svgGroup type="to_add" width="110" height="110" px='rpx'
						:color="diyStyle_var['--color_video_main']">
					</svgGroup>
				</view>
				<view class="release_bot"> </view>
			</view>
		</block>
		<!-- 发布按钮弹层 start -->
		<view class="release_mask" v-if="showReleaseType">
			<view class="content">
				<view class="detail">
					<view v-if="setting.video_switch == 1" @tap="releaseLive('video')" class="item">
						<image :src="imgUrl + 'svideo/release_video_icon.png'"></image>
						<text>{{ $L('短视频') }}</text>
					</view>
					<view @tap="releaseLive('graphic')" class="item">
						<image :src="imgUrl + 'svideo/createhb.png'"></image>
						<text>{{ '图文' }}</text>
					</view>
					<!-- wx-1-start -->

					<!-- wx-1-end -->
					<!-- app-2-start -->

					<!-- app-2-end -->
				</view>
				<image class="colse" :data-flag="false" @tap="showReleaseTypeFun"
					:src="imgUrl + 'svideo/release_close.png'"></image>
			</view>
		</view>
		<!-- 发布按钮弹层 end -->
		<!-- #endif -->

	</view>
</template>

<script>
	// import request from "@/utils/request";
	const bus = getApp().globalData.bus
	import {
mapState
} from 'vuex'
import userFavorite from '../component/user/userFavorite.vue'
import userGoods from '../component/user/userGoods.vue'
import userLive from '../component/user/userLive.vue'
import userVideo from '../component/user/userVideo.vue'

	export default {
		data() {
			return {
				author_id: '',
				//wx-8-start
				// #ifdef MP
				menuButtonHeight: uni.getMenuButtonBoundingClientRect().height + 'px',
				menuButtonTop: uni.getMenuButtonBoundingClientRect().top + 'px',
				// #endif
				//wx-8-end
				//作者id
				settingData: {},
				//平台设置信息
				authorInfo: {},
				//作者信息
				memberInfo: {},
				//用户相关信息
				imgUrl: process.env.VUE_APP_IMG_URL,
				//图片地址
				curTab: 'video',
				//当前tab
				showReleaseType: false, //是否展示发布类型
				role_type: '',
				publish_live_can: '',
				//店铺信息
				store_info: '',
				setting: {}, //平台设置信息
				bgStyle: 'background-size:cover;background-position:center center;background-repeat: no-repeat;',
				routeCurTab: '',
				// #ifdef H5
				contentTop: '380rpx',
				// #endif
				// #ifndef H5
				contentTop: '461rpx',
				// #endif
				showState: false,
				windowHeight: uni.getSystemInfoSync().windowHeight
			}
		},

		components: {
			userGoods,
			userFavorite,
			userLive,
			userVideo
		},
		props: {},
		computed: {
			...mapState(['hasLogin']),
			topHeight() {
				//wx-2-start
				// #ifdef MP-WEIXIN
				return `calc(${this.menuButtonTop} + ${this.menuButtonHeight} + 380rpx)`
				// #endif
			}
		},
		mounted() {
			this.initData()
		},
		/**
		 * 生命周期函数--监听页面加载
		 */
		onLoad: function(options) {
			setTimeout(() => {
				uni.setNavigationBarTitle({
					title: this.$L('我的发文')
				})
			}, 0);

			if (this.$Route.query.author_id) {
				this.author_id = this.$Route.query.author_id.toString()
			}
			if (this.$Route.query.curTab) {
				this.routeCurTab = this.$Route.query.curTab
			}
			this.getSetting()

			uni.$on('updateState', () => {
				this.$refs[this.curTab].pn = 1
				this.$refs.video.getVideoList()
			})
		},
		onShow: function() {
			if (this.curTab == 'favorite') {
				//从视频播放页返回后刷新列表
				this.$refs.favorite.getFavoriteList()
			}

			if (this.showState) {
				this.showState = false
				if (this.curTab == 'video' && this.$refs.video) {
					this.$refs[this.curTab].pn = 1
					this.$refs.video.getVideoList()
				} else if (this.curTab == 'goods' && this.$refs.goods) {
					this.$refs[this.curTab].pn = 1
					this.$refs.goods.getGoods()
				} else if (this.curTab == 'favorite' && this.$refs.favorite) {
					this.$refs[this.curTab].pn = 1
					this.$refs.favorite.getFavoriteList()
				} else if (this.curTab == 'live' && this.$refs.live) {
					this.$refs[this.curTab].pn = 1
					this.$refs.live.getLiveList()
				}
			}

			this.initData()
		},

		onReady() {
			// #ifndef H5
			let query = uni.createSelectorQuery().in(this)
			query.select('.author_info').boundingClientRect((res) => {
				if (res) {
					this.contentTop = res.height + 'px'
				}
			}).exec()
			// #endif
		},

		onHide() {
			this.showReleaseType = false
		},

		onUnload() {
			uni.$off('updateState')
			if (this.setting.live_switch == 1) {
				uni.$off('updateLiveList')
			}
		},

		methods: {
			//初始化数据
			initData() {
				this.getAuthorInfo()
			},
			//获取作者信息
			getAuthorInfo() {
				let {
					author_id
				} = this
				let param = {}
				param.data = {}
				if (author_id != '') {
					param.data.authorId = author_id
				}
				param.url = 'v3/video/front/video/author/personPage'
				param.method = 'GET'
				this.$request(param).then((res) => {
					if (res.state == 200) {
						this.authorInfo = res.data
						uni.$emit('fromLiveAttention')
						if (!this.authorInfo.isSelf) {
							uni.setNavigationBarTitle({
								title: this.authorInfo.memberNickname
							})
						}
					}
				})
			},

			// 获取设置信息
			getSetting() {
				let param = {}
				param.url = 'v3/video/front/video/setting/getSettingList'
				param.method = 'GET'
				param.data = {}
				param.data.str = 'video_switch,live_switch'

				this.$request(param).then((res) => {
					if (res.state == 200) {
						let result = res.data
						result.map((settingItem) => {
							if (settingItem.name == 'video_switch') {
								//绑定商品数
								this.setting.video_switch = settingItem.value
							}
							if (settingItem.name == 'live_switch') {
								//绑定商品数
								this.setting.live_switch = settingItem.value

								if (this.setting.live_switch == 1) {
									uni.$on('updateLiveList', () => {
										setTimeout(() => {
											this.$refs[this.curTab].pn = 1
											this.$refs.live.getLiveList()
										}, 1000)
									})
								}
							}
							this.setting = JSON.parse(JSON.stringify(this.setting))
						})
						this.curTab = 'video'
					}
				})
			},
			// 返回上级页面
			goBack() {
				this.$Route.back(1)
			},

			//关注、取消关注事件
			collect() {
				if (this.hasLogin) {
					let param = {}
					param.data = {}
					param.method = 'POST'
					param.data.authorId = this.author_id
					if (this.authorInfo.isFollow) {
						//取消关注
						param.url = 'v3/video/front/video/cancelFollow'
					} else {
						//关注作者
						param.url = 'v3/video/front/video/followAuthor'
					}
					this.$request(param).then((res) => {
						if (res.state == 200) {
							uni.showToast({
								title: res.msg,
								icon: 'none',
								duration: 500
							})
							this.$api.prePage() && this.$api.prePage().updateCollect(this.author_id)
							this.getAuthorInfo()
						}
					})
				} else {
					getApp().globalData.goLogin(this.$Route)
				}
			},

			//查看粉丝
			goAttention(index, title) {
				if (this.authorInfo.isSelf) {
					this.$Router.push({
						path: '/extra/user/attention',
						query: {
							type: index
						}
					})
				}
			},

			//编辑资料
			editMemInfo() {
				if (this.authorInfo.isSelf) {
					this.$Router.push('/extra/user/authorInfo')
				}
			},

			//tab切换
			changeTab(targetTab) {
				this.curTab = targetTab
				this.$Route.query.curTab = targetTab
			},

			//进入评论列表
			goComments() {
				this.$Router.push('/extra/svideo/svideoComments')
			},

			//点击底部加号事件
			release(e) {
				this.$Router.push('/pages/graphic/graphicRelease')
				// this.showReleaseTypeFun(e)
			},

			// 发布直播
			releaseLive(type) {
				let {
					author_id,
					authorInfo,
					memberInfo
				} = this
				this.showState = true
				if (type == 'live') {
					if (this.setting.live_switch == 1) {
						//直播
						if (this.authorInfo.liveState == 0) {
							this.applicationShowTip(this.authorInfo.forbidReason)
							return
						}
					}

					this.$Router.push({
						path: '/extra/live/liveReleaseLive',
						query: {
							roleType: this.authorInfo.roleType,
							storeId: this.authorInfo.storeId
						}
					})
				} else if (type == 'video') {
					if (this.authorInfo.permissionState == 0) {
						this.applicationTip(this.$L('是否立即申请发布权限'))
						return
					} else if (this.authorInfo.permissionState == 1) {
						if (!(this.authorInfo.remark && this.authorInfo.forbidReason)) {
							this.applicationShowTip(this.$L('审核中，请耐心等待'))
							return
						}
					} else if (this.authorInfo.permissionState == 3) {
						this.applicationTip(this.authorInfo.remark)
						return
					} else if (this.authorInfo.permissionState == 4) {
						if (!(this.authorInfo.remark && this.authorInfo.forbidReason)) {
							this.applicationShowTip(this.authorInfo.remark)
							return
						}
					}

					let query = {
						roleType: this.authorInfo.roleType
					}
					if (this.authorInfo.storeId) {
						query.storeId = this.authorInfo.storeId
					}

					this.$Router.push({
						path: '/extra/svideo/svideoRelease',
						query
					})
				} else if (type == 'graphic') {

					if (this.authorInfo.permissionState == 0) {
						this.applicationTip(this.$L('是否立即申请发布权限'))
						return
					} else if (this.authorInfo.permissionState == 1) {
						if (!(this.authorInfo.remark && this.authorInfo.forbidReason)) {
							this.applicationShowTip(this.$L('审核中，请耐心等待'))
							return
						}
					} else if (this.authorInfo.permissionState == 3) {
						this.applicationTip(this.authorInfo.remark)
						return
					} else if (this.authorInfo.permissionState == 4) {
						if (!(this.authorInfo.remark && this.authorInfo.forbidReason)) {
							this.applicationShowTip(this.authorInfo.remark)
							return
						}
					}

					this.$Router.push({
						path: '/extra/graphic/graphicRelease'
					})
				}
			},

			applicationShowTip(tip) {
				let tips = tip
				if (this.authorInfo.remark && this.authorInfo.forbidReason) {
					tips = this.$L('禁止发布：') + this.authorInfo.remark + ',' + this.authorInfo.forbidReason
				}
				if (this.authorInfo.permissionState == 1) {
					uni.showModal({
						title: this.$L('提示'),
						content: tips,
						showCancel: false,
						confirmText: this.$L('确定')
					})
				} else {
					uni.showModal({
						title: this.$L('您已被平台限制发布'),
						content: tips,
						showCancel: false,
						confirmText: this.$L('确定')
					})
				}
			},
			applicationTip(tips) {
				this.applicationFb(tips)
			},

			applicationFb(tip) {
				uni.showModal({
					title: this.$L('提示'),
					content: tip,
					confirmText: this.authorInfo.permissionState == 3 ?
						this.$L('再次申请') : this.$L('确定'),
					cancelText: this.$L('取消'),
					success: (res) => {
						if (res.confirm) {
							let param = {}
							param.data = {}
							param.method = 'POST'
							param.url = 'v3/video/front/video/author/apply'
							this.$request(param).then((res) => {
								if (res.state == 200) {
									uni.showToast({
										title: res.msg,
										icon: 'none'
									})
									this.getAuthorInfo()
								}
							})
						}
					}
				})
			},

			liveEvent(e) {
				this.getAuthorInfo()
			},

			showReleaseTypeFun(e) {
				this.showReleaseType = e.currentTarget.dataset.flag
			}
		}
	}
</script>
<style lang="scss">
	page {
		background: #f8f8f8;
		width: 750rpx;
		margin: 0 auto;
	}

	.author_info {
		width: 750rpx;
		height: 380rpx;
		background-size: cover;
		background-position: center;
		background-repeat: no-repeat;
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		padding: 20rpx 20rpx 0rpx 0rpx;
		//wx-5-start
		/* #ifdef MP */
		padding-top: 0rpx;
		/* #endif */
		//wx-5-end
		box-sizing: border-box;
		overflow: hidden;
		position: relative;
		top: 0;

		.go_back {
			width: 45rpx;
			height: 47rpx;
		}

		.info {
			display: flex;
			flex-direction: row;
			justify-content: flex-start;
			align-items: center;
			width: 100%;
			height: 130rpx;
			margin-top: 5rpx;
			//wx-6-start
			/* #ifdef MP */
			margin-top: 25rpx;
			/* #endif */
			//wx-6-end
			margin-left: 20rpx;

			.right_info {
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: flex-start;
			}

			.right_info .check_tip {
				color: #fff;
				font-size: 24rpx;
				margin-left: 20rpx;
				margin-top: 10rpx;
			}

			.right_info .member_info {
				display: flex;
				flex-direction: row;
				justify-content: flex-start;
			}

			.author_avator {
				width: 130rpx;
				height: 130rpx;
				border-radius: 65rpx;
				display: flex;
				flex-direction: row;
				justify-content: center;
				align-items: center;
			}

			.author_name {
				max-width: 310rpx;
				color: #fff;
				font-size: 36rpx;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
				margin-left: 20rpx;
			}

			.edit_mem_info {
				width: 155rpx;
				height: 40rpx;
				background: var(--color_video_main);
				border-radius: 6rpx;
				display: flex;
				flex-direction: row;
				justify-content: center;
				align-items: center;
				margin-left: 20rpx;

				text {
					color: #f9e9e9;
					font-size: 24rpx;
					margin-right: 5rpx;
				}

				image {
					width: 40rpx;
					height: 40rpx;
					margin-left: -4rpx;
				}
			}

			.fav_info {
				display: flex;
				flex-direction: row;
				justify-content: center;
				align-items: center;
				border-radius: 8rpx;
				background: #fc1c1c;
				padding: 8rpx 10rpx;
				margin-left: 20rpx;

				image {
					width: 30rpx;
					height: 30rpx;
				}

				text {
					color: #f9e9e9;
					font-size: 24rpx;
					margin-left: 5rpx;
					margin-right: 5rpx;
				}
			}

			.fav_info_bg {
				background: var(--color_video_main) !important;
			}

			.go_vendor {
				background: linear-gradient(to left, #fc1c1c, #ffa300);
				width: 73rpx;
				height: 40rpx;
				display: flex;
				flex-direction: row;
				justify-content: center;
				align-items: center;
				border-radius: 20rpx;
				margin-left: 20rpx;

				text {
					color: #fff;
					font-size: 22rpx;
				}
			}
		}

		.author_desc {
			color: #fff;
			width: 670rpx;
			margin-top: 25rpx;
			font-size: 26rpx;
			margin-left: 20rpx;
			line-height: 32rpx;
			text-overflow: ellipsis;
			overflow: hidden;
			word-break: break-all;
			height: 60rpx;
		}

		.stat_num {
			color: #fff;
			margin-top: 10rpx;
			display: flex;
			flex-direction: row;
			justify-content: flex-start;
			align-items: center;
			margin-left: 20rpx;

			view {
				margin-right: 30rpx;

				.num {
					font-size: 30rpx;
					font-weight: bold;
					margin-right: 10rpx;

					.desc {
						font-size: 22rpx;
					}
				}
			}
		}

		.tab {
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			align-items: center;
			padding: 10rpx 20rpx;
			width: 750rpx;
			margin-top: 10rpx;
			//wx-7-start
			/* #ifdef MP */
			width: 710rpx;
			/* #endif */
			//wx-7-end
			position: absolute;
			bottom: 10rpx;

			text {
				font-size: 32rpx;
				color: #fff;
				padding-bottom: 8rpx;

				&.sel {
					font-weight: bold;
					border-bottom: 2px solid #fff;
				}
			}
		}
	}

	.live_user_tab_content_main {
		width: 750rpx;
		top: 467rpx;
		margin: 0 auto;
	}

	//wx-9-start
	/* #ifdef MP*/
	.live_user_tab_content {
		width: 750rpx;
		position: fixed;
		top: 390rpx;
		left: 0;
		bottom: 0;
	}

	/* #endif */
	//wx-9-end
	/* #ifdef H5  */
	.live_user_tab_content {
		width: 750rpx;
		position: fixed;
		top: 448rpx;
		/* left: 0; */
		bottom: 0;
	}

	/* #endif */

	.author_info .info .msg_wrap {
		width: 34rpx;
		height: 34rpx;
		position: relative;
		margin-left: 20rpx;
	}

	.author_info .info .msg_wrap image {
		width: 34rpx;
		height: 34rpx;
	}

	.author_info .info .msg_wrap text {
		position: absolute;
		z-index: 2;
		top: -26rpx;
		left: 6rpx;
		color: #fff;
		background: var(--color_video_main);
		min-width: 58rpx;
		padding: 6rpx 4rpx;
		border-radius: 50%;
		text-align: center;
		transform: scale(0.5);
	}

	.release {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 2;
		width: 750rpx;
		height: 100rpx;
		background: transparent;
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: center;
		margin: 0 auto;
	}

	// .release image {
	// 	position: absolute;
	// 	bottom: 40rpx;
	// 	z-index: 3;
	// 	width: 120rpx;
	// 	height: 120rpx;
	// }

	.release .release_bot {
		position: absolute;
		left: 0;
		right: 0;
		bottom: 0;
		height: 100rpx;
		background: #fff;
	}

	.release_img {
		position: absolute;
		bottom: 40rpx;
		z-index: 3;
		width: 120rpx;
		height: 120rpx;
		background-color: #fff;
		border-radius: 50%;
	}

	.release_mask {
		width: 750rpx;
		position: fixed;
		z-index: 99;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.6);
		margin: 0 auto;
	}

	.release_mask .content {
		position: absolute;
		bottom: 35rpx;
		left: 0;
		right: 0;
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: center;
		width: 100%;
	}

	.release_mask .content .colse {
		width: 47rpx;
		height: 47rpx;
		margin-top: 38rpx;
	}

	.release_mask .content .detail {
		display: flex;
		justify-content: space-around;
		align-items: center;
		width: 100%;
	}

	.release_mask .content .detail .item {
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: center;
	}

	.release_mask .content .detail .item image {
		width: 110rpx;
		height: 110rpx;
	}

	.release_mask .content .detail .item text {
		color: #fff;
		font-size: 34rpx;
		margin-top: 28rpx;
	}


	.nav-bar {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: flex-start;
		padding-left: 20rpx;
	}

	.nav-bar image {
		width: 21rpx;
		height: 35rpx;
	}

	.nav-bar view {
		font-size: 36rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #FFFFFF;
		margin-left: 21rpx;
	}
</style>