<!-- 短视频列表item -->
<template name="listVideoItem">
  <view class="video_list flex_row_between_start">
    <block v-for="(list, index) in videoListCut" :key="index">
      <view class="column_list">
        <view
          v-for="(item, idx) in list"
          :key="idx"
          class="video_item"
          @tap="goVideoPlay(item)"
        >
          <!-- 视频播放数量 / 图文浏览数量 -- 左下固定 -->
          <!-- 视频类型图标 -- 右上固定 -->
          <view v-if="item.videoType == 1" class="play_type_video">
            <image :src="imgUrl + 'svideo/play_video1.png'"></image>
          </view>
          <view class="video_img">
            <view v-if="item.videoType == 1" class="play_num_bg">
              <image mode="aspectFit" :src="listPlayNum"></image>
              <text class="play_num"
                >{{ item.clickNum }}{{ $L('次播放') }}</text
              >
            </view>
            <view v-else class="play_eye_bg">
              <image mode="aspectFit" :src="viewImgUrl"></image>
              <text class="play_num"
                >{{ item.clickNum }}{{ $L('次播放') }}</text
              >
            </view>
            <image
              :src="item.videoImage"
              mode="aspectFit"
              :style="{ height: (item.height / item.width) * 345 + 'rpx' }"
            ></image>
          </view>
          <text class="name">{{ item.videoName }}</text>
          <view class="author">
            <view class="author_info">
              <view
                class="avator"
                :style="'background:url(' + item.memberAvatar + ');' + bgStyle"
              ></view>
              <text>{{
                item.memberNickname ? item.memberNickname : item.memberName
              }}</text>
            </view>
            <view class="fav">
              <image :src="listFavIcon"></image>
              <text>{{ item.likeNum }}</text>
            </view>
          </view>
        </view>
      </view>
    </block>
  </view>
</template>

<script>
export default {
  name: 'listVideoItem',
  data() {
    return {
      imgUrl: process.env.VUE_APP_IMG_URL,
      viewImgUrl: process.env.VUE_APP_IMG_URL + 'svideo/eye.png'
    }
  },
  props: {
    videoList: {
      type: Array,
      value: []
    },
    bgStyle: {
      type: String,
      value: ''
    },
    listFavIcon: {
      type: String,
      value: ''
    },
    listPlayNum: {
      type: String,
      value: ''
    },
    curLabelId: {
      type: Number,
      value: 0
    }, //当前标签id
    authorId: {
      type: Number,
      value: 0
    } //当前作者id
  },
  computed: {
    videoListCut() {
      let index = 0
      let newArray = []
      let len = this.videoList.length / 2
      while (index < this.videoList.length) {
        newArray.push(this.videoList.slice(index, (index += len)))
      }
      return newArray
    }
  },
  methods: {
    //进入播放页面
    goVideoPlay(item) {
      if (item.videoType == 1) {
        // #ifdef H5
        this.$Router.push({
          path: '/extra/svideo/svideoPlay',
          query: {
            video_id: item.videoId,
            author_id: item.authorId,
            curLabelId: this.curLabelId
          }
        })
        // #endif
        // #ifdef MP-WEIXIN
        uni.navigateTo({
          url: '/extra/svideo/svideoPlay?video_id=' + item.videoId + '&author_id=' + item.authorId + '&curLabelId=' + this.curLabelId
        })
        // #endif
      } else {
        // #ifdef H5
        this.$Router.push({
          path: '/extra/graphic/graphicDetail',
          query: {
            video_id: item.videoId,
            author_id: item.authorId
          }
        })
        // #endif
        // #ifdef MP-WEIXIN
        uni.navigateTo({
          url: '/extra/graphic/graphicDetail?video_id=' + item.videoId + '&author_id=' + item.authorId
        })
        // #endif
      }
    }
  }
}
</script>

<style lang="scss">
.column_list {
  display: flex;
  flex-direction: column;
}

.video_list {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  flex-wrap: wrap;
  margin-top: 20rpx;
}

.video_list .video_item {
  width: 345rpx;
  height: auto;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  margin-left: 20rpx;
  background: #fff;
  border-radius: 14rpx;
  margin-bottom: 20rpx;
  position: relative;
  padding-bottom: 20rpx;
}

.play_num_bg {
  /* width: 108rpx; */
  height: 38rpx;
  border-radius: 19rpx;
  padding-right: 10rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  bottom: 20rpx;
  /* 20rpx; */
  left: 15rpx;
  z-index: 2;
  image {
    width: 32rpx !important;
    height: 32rpx !important;
  }
}

.video_list .video_item .play_eye_bg {
  /* width: 108rpx; */
  height: 38rpx;
  border-radius: 19rpx;
  padding-right: 10rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  position: absolute;
  top: 20rpx;
  left: 15rpx;
  z-index: 2;
  image {
    width: 40rpx !important;
    height: 40rpx !important;
  }
}

.play_eye_bg image {
  height: 100%;
  position: absolute;
  left: 6rpx;
  top: 0;
  z-index: -1;
  filter: grayscale(100%) brightness(200%);
}

.video_list .video_item .play_num {
  color: #fff;
  font-size: 24rpx;
  padding-left: 20rpx;
}

.video_list .play_eye_bg .play_num {
  padding-left: 48rpx;
}

.video_list .video_item .play_type_video {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  z-index: 2;
  width: 40rpx;
  height: 40rpx;
}

.video_list .video_item .play_type_video image {
  width: 100%;
  height: 100%;
}

.video_list .video_item .video_img {
  width: 345rpx;
  height: auto;
  border-radius: 14rpx 14rpx 0 0;
  overflow: hidden;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  position: relative;

  image {
    width: 345rpx;
    height: auto;
  }
}

.video_list .video_item .name {
  height: 80rpx;
  color: #2d2d2d;
  font-weight: 700;
  font-size: 28rpx;
  line-height: 40rpx;
  margin-top: 16rpx;
  margin-left: 16rpx;
  margin-right: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-word;
}

.video_list .video_item .author {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-top: 13rpx;
  margin-left: 20rpx;
  width: 315rpx;
}

.video_list .video_item .author .author_info {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  width: 200rpx;
  overflow: hidden;
  text-overflow: ellipsis;
}

.video_list .video_item .author .author_info .avator {
  width: 42rpx;
  height: 42rpx;
  border-radius: 50%;
}

.video_list .video_item .author .author_info text {
  color: #949494;
  font-size: 22rpx;
  margin-left: 10rpx;
  white-space: nowrap;
  width: 148rpx;
  overflow: hidden;
  text-overflow: ellipsis;
}

.video_list .video_item .author .fav {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}

.video_list .video_item .author .fav image {
  width: 42rpx;
  height: 42rpx;
}

.video_list .video_item .author .fav text {
  font-size: 24rpx;
  color: #949494;
  margin-left: 0rpx;
}
</style>
