<template>
	<view>
		<!-- 直播/短视频 个人中心 商品模块 -->
		<scroll-view class="live_user_tab_content_scroll" scroll-y @scrolltolower="getMoreGoods" v-if="videoList.length>0" :style="{height:topHeight}">
			<view class="video_list">
				<view v-for="(item, index) in videoList" :key="index" class="item">
					<view class="top">
						<view :class="{base_info:true,fullWidth:!(authorInfo.isSelf && (item.state == 1 || item.state == 3 || item.state == 4))}">
							<text class="time">{{item.createTime}}</text>
							<text class="title">{{item.videoName}}</text>
							<text class="desc">{{item.introduction}}</text>
							<text class="reject" v-if="item.state==4">禁止理由:{{item.remark}}</text>
						</view>
						<block v-if="authorInfo.isSelf && (item.state == 1 || item.state == 3 || item.state == 4)">
							<block v-if="item.state == 1">
								<image class="status" :src="imgUrl+'svideo/s_com.png'"></image>
							</block>
							<block v-if="item.state == 3">
								<image class="status" :src="imgUrl+'svideo/s_err.png'"></image>
							</block>
							<block v-if="item.state == 4">
								<image class="status" :src="imgUrl+'svideo/s_hide.png'"></image>
							</block>
						</block>
					</view>
					<view class="video" :style="'background-image:url('+item.videoImage+');background-size:cover;background-position:center center;background-repeat: no-repeat;'" @tap="goVideoPlay(item,index)">
						<image class="play_btn" :src="imgUrl+'svideo/play.png'" v-if="item.videoType==1"></image>
						<view class="graphic_label" v-if="item.videoType==2">
							<text>图文</text>
						</view>
					</view>
					<view class="stat_num">
						<view class="left"  v-if="authorInfo.isSelf" @click="delVideo(item.videoId)">
							<image :src="imgUrl+'svideo/del.png'"></image>
							<text>{{$L('删除')}}</text>
						</view>
						<view class="left" v-if="authorInfo.isSelf && authorInfo.permissionState == 2 && (item.state == 3 || item.state == 1)" @tap="editVideo(item.videoId,item.videoType,index)">
							<image class="ml_10" :src="imgUrl+'svideo/edit.png'"></image>
							<text>{{$L('重新编辑')}}</text>
						</view>
						
						<view class="right">
							<image :src="imgUrl+'svideo/watch.png'"></image>
							<text>{{item.clickNum}}</text>
							<image class="ml_30" :src="imgUrl+'svideo/fav.png'"></image>
							<text>{{item.likeNum}}</text>
							<image class="ml_30" :src="imgUrl+'svideo/comment.png'"></image>
							<text>{{item.commentNum}}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 数据加载完毕 -->
			<dataLoaded :showFlag="!hasmore&&videoList.length>0" />

			<!-- 数据加载中 -->
			<dataLoading :showFlag="hasmore&&loading&&!firstLoading" />

			<!-- 页面loading -->
			<pageLoading :firstLoading="firstLoading" :loadingIcon="imgUrl + 'svideo/page_loading_icon.gif'" :topVal="467" />
			
			<block  v-if="authorInfo.isSelf">
				<!-- wx-1-start -->
				<!-- #ifdef MP-WEIXIN -->
				<view style="height:90rpx;width:750rpx"></view>
				<!-- #endif -->
				<!-- wx-1-end -->
				<!-- #ifdef H5 -->
				<view style="height:130rpx;width:750rpx"></view>
				<!-- #endif -->
				<!-- app-1-start -->



				<!-- app-1-end -->
			</block>
		</scroll-view>
		<!-- 页面空数据 -->
		<emptyData :showFlag="!firstLoading&&videoList.length==0" :emptyIcon="imgUrl+'svideo/live_list_empty_icon.png'" />
	</view>
</template>

<script>
	import {
		checkPageHasMore
	} from "@/utils/live";
	import request from "@/utils/request";
	import pageLoading from "../../component/pageLoading.vue";
	import emptyData from "../../component/emptyData.vue";
	import dataLoading from "../../component/dataLoading.vue";
	import dataLoaded from "../../component/dataLoaded.vue";

	const imgUrl = process.env.VUE_APP_IMG_URL;
	let pn = 1; //当前页
	//当前页
	let pageSize = 10; //每页数据
	//每页数据
	let hasmore = true; //是否还有数据

	export default {
		components: {
			pageLoading,
			emptyData,
			dataLoading,
			dataLoaded,
		},
		data() {
			return {
				pn: 1,
				//当前页
				pageSize: 10,
				//每页数据
				loading: false,
				//数据加载状态
				videoList: [],
				//动态列表数据
				addCartIcon: imgUrl + 'live/add_cart.png',
				eyeIcon: imgUrl + 'live/eye.png',
				hasmore: true,
				//是否还有数据，用于页面展示
				firstLoading: true,
				//是否初次加载，是的话展示页面居中的loading效果，
				imgUrl: process.env.VUE_APP_IMG_URL, //图片地址
				//wx-3-start
				// #ifdef MP
				menuButtonHeight: uni.getMenuButtonBoundingClientRect().height + 'px',
				menuButtonTop: uni.getMenuButtonBoundingClientRect().top + 'px',
				// #endif
				//wx-3-end
			};
		},
		props: {
			memberInfo: {
				// 用户信息
				type: Object
			},
			authorInfo: {
				// 作者信息
				type: Object
			},
			author_id: {
				// 作者id
				type: String,
				default: ''
			},
			settingData: {
				// 平台设置信息
				type: Object
			}
		},
		
		created() {
			// 更新浏览次数
			let operateIndex = null;
			uni.$on('updateView',(index)=>{
				operateIndex = index;
				if(operateIndex<0){
					return
				}
				if(this.videoList[index].state != 1 && this.videoList[index].state != 3){
					this.videoList[index].clickNum = Number(this.videoList[index].clickNum)+1;
				}
			})
			// 更新评论条数
			uni.$on('updateComment',(num)=>{
				if(operateIndex<0){
					return
				}
				if(this.videoList[this.operateIndex].state != 1 && this.videoList[this.operateIndex].state != 3){
					this.videoList[this.operateIndex].commentNum = num;
				}
			})
			
			uni.$on('updateLike',(isLike)=>{
				if(operateIndex<0){
					return
				}
				let {likeNum} = this.videoList[operateIndex]
				if(this.videoList[operateIndex].state != 1 && this.videoList[operateIndex].state != 3){
					if(isLike){
						likeNum++
					}else{
						likeNum--
					}
					this.videoList[operateIndex].likeNum = likeNum
					
				}
			})
			
			
			// 更新编辑后的图文审核状态
			uni.$on('updateState',(val)=>{
				// if(val>=0){
				// 	this.videoList[val].state = 1;
				// }else{
					this.pn = 1;
					this.hasmore = true;
					this.getVideoList();
				// }
			})
		},

		mounted() {
			this.getVideoList();
		},
		
		destroyed() {
			uni.$off('updateView');
			uni.$off('updateComment');
			uni.$off('updateState');
		},
		
		computed: {
			topHeight() {
				//wx-2-start
				// #ifdef MP-WEIXIN
				return `calc(100vh - ${this.menuButtonTop} - ${this.menuButtonHeight} - 380rpx - 49px)`
				// #endif
				//wx-2-end
				// #ifndef MP-WEIXIN
				return ''
				// #endif 
			}
		},
		
		methods: {
			//获取动态数据
			getVideoList() {
				this.loading = true;
				let {
					author_id,
					firstLoading,
					videoList,
					pageSize,
					pn,
					hasmore
				} = this;
				let param = {}
				param.data={};
				if(author_id!=''){
					param.data.authorId=author_id;
				}
				param.data.pageSize=pageSize;
				param.data.current=pn;
				param.url = 'v3/video/front/video/author/videoList';
				param.method = 'GET';
				this.$request(param).then(res => {
					if (res.state == 200) {
						let result = res.data.list;
						if (this.pn == 1) {
							this.videoList = result;
						} else {
							this.videoList = videoList.concat(result);
						}
						
						if (checkPageHasMore(res.data.pagination)) {
							this.pn++;
						} else {
							this.hasmore = false;
						}
					}
					if (this.firstLoading) {
						this.firstLoading = false;
					}
					this.loading = false;
				})
			},

			getMoreGoods() {
				if (this.hasmore) {
					this.getVideoList();
					
				}
			},

			//进入播放页面
			goVideoPlay(item,index) {
				let author_id = this.authorInfo.authorId || this.author_id || '';
				this.operateIndex = index
				if(item.videoType==1){
					this.$videoPlayNav({video_id:item.videoId,author_id,index})
				}else{
					this.$Router.push({path:'/extra/graphic/graphicDetail',query:{video_id:item.videoId,index:index,roleType:this.authorInfo.roleType}})
				}
			},

			//删除短视频
			delVideo(videoId) {
				uni.showModal({
					title: '提示',
					content: '确定删除该条数据吗?',
					success: res => {
						if (res.confirm) {
							let param = {}
							param.data={};
							param.data.videoId = videoId;
							param.url = 'v3/video/front/video/delVideo';
							param.method = 'POST';
							this.$request(param).then(res => {
								if (res.state == 200) {
	
									this.$api.msg(res.msg)
									let index = this.videoList.findIndex(i=>i.videoId == videoId)
									this.videoList.splice(index,1)
									this.$emit('getAuthorInfo')
								}else{
									this.$api.msg(res.msg)
								}
							})
						}
					}
				});
			},

			//重新编辑功能
			editVideo(videoId,videoType,index) {
				if(videoType==1){
					this.$Router.push({
						path:'/extra/svideo/svideoRelease',
						query:{
							video_id:videoId, 
							roleType:this.authorInfo.roleType,
							storeId:this.authorInfo.storeId?this.authorInfo.storeId:'',
							edit:true,
						},
					})
				}else{
					this.$Router.push({
						path:'/extra/graphic/graphicRelease',
						query:{ 
							video_id:videoId, 
							roleType:this.authorInfo.roleType,
							storeId:this.authorInfo.storeId?this.authorInfo.storeId:'',
							index:index,
						},
					})
				}
			}

		}
	};
</script>
<style lang="scss">
	.live_user_tab_content_scroll {
		height: calc(100vh - 380rpx);



		width: 100%;
	}

	.video_list {
		width: 100%;
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;
	}

	.video_list .item {
		/* #ifndef MP-WEIXIN */
		width: 100%;
		/* #endif */
		padding-left: 30rpx;
		background: #fff;
		margin-bottom: 20rpx;
		padding: 30rpx 30rpx 0;
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;
	}

	.video_list .item .top {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: flex-start;
		width: 100%;
	}

	.video_list .item .top .base_info {
		width: 550rpx;
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;
	}
	
	.video_list .item .top .fullWidth{
		width: 100%;
	}

	.video_list .item .top .status {
		width: 100rpx;
		height: 77rpx;
	}

	.video_list .item .top .base_info .time {
		color: #949494;
		font-size: 22rpx;
		line-height: 32rpx;
	}

	.video_list .item .top .base_info .title {
		color: #2d2d2d;
		font-size: 30rpx;
		line-height: 40rpx;
		padding: 10rpx 0;
	}

	.video_list .item .top .base_info .desc {
		width: 550rpx;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		color: #949494;
		font-size: 26rpx;
	}
	
	.video_list .item .top .base_info .reject {
		width: 550rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		color: #949494;
		font-size: 26rpx;
	}

	.video_list .item .video {
		width: 690rpx;
		height: 690rpx;
		border-radius: 15rpx;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		margin-top: 20rpx;
		position: relative;
		.graphic_label{
			position: absolute;
			top: 0;
			left: 0;
			border-radius: 15rpx 0 15rpx 0;
			background:var(--color_video_main_bg);
			color: #fff;
			padding: 4rpx 20rpx;
			font-size: 22rpx;
			display: flex;
			align-items: center;
			image{
				margin-right: 10rpx;
				width: 30rpx;
				height: 30rpx;
			}
		}
	}

	.video_list .item .video .play_btn {
		width: 88rpx;
		height: 88rpx;
	}

	.video_list .item .stat_num {
		width: 100%;
		height: 76rpx;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		margin-left: 0;
		margin-top: 0;
	}

	.video_list .item .stat_num .left,
	.video_list .item .stat_num .right {
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
	}

	.video_list .item .stat_num .left image,
	.video_list .item .stat_num .right image {
		width: 43rpx;
		height: 43rpx;
	}

	.video_list .item .stat_num .left text,
	.video_list .item .stat_num .right text {
		color: #666;
		font-size: 22rpx;
	}

	.video_list .item .detail_num {
		width: 100%;
		height: 64rpx;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
		border-top: 1px solid rgba(0, 0, 0, 0.1);
	}

	.video_list .item .detail_num text {
		color: #666;
		font-size: 24rpx;
	}

	.video_list .item .detail_num .color_9 {
		color: #999;
	}

	.video_list .item .detail_num .color_3 {
		color: #333;
	}

	.ml_30 {
		margin-left: 30rpx;
	}

	.ml_10 {
		margin-left: 10rpx;
	}

	.mr_30 {
		margin-right: 30rpx;
	}
</style>
