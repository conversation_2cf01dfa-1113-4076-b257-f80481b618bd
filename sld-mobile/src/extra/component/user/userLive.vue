<template>
	<view>
		<!-- 直播/短视频 个人中心 直播模块 -->
		<scroll-view class="live_user_tab_content_scroll" scroll-y @scrolltolower="getMoreGoods"
			v-if="liveList.length > 0" :style="{height:topHeight}">
			<block v-for="(item, index) in liveList" :key="index">
				<block v-if="item.liveState == 2">
					<view :class=" 'user_living_item ' + (index == 0 ? 'live_list_item_first' : '')">
						<view class="top_info">
							<text class="status_text">{{ item.liveStateValue }}</text>
							<text class="publish_time">{{ item.createTime }}</text>
						</view>

						<view class="live_list_item" :data-liveId="item.liveId" @tap="goLivePlay">
							<view class="left" :style="{ backgroundImage: 'url(' + item.liveCover + ')' }">
								<view class="left_top">
									<block v-if="item.liveState != '2'">
										<image class="back_img" :src="listPlayBackIcon" mode="aspectFit"></image>
									</block>
									<block v-else>
										<image class="back_img_living" :src="listLivingIcon" mode="aspectFit"></image>
									</block>
									<text class="watch_num_text">{{ item.viewingNum }}{{ $L('观看') }}</text>
								</view>
								<view class="right_bottom" v-if="item.popularityNum > 0">
									<image class="heart_icon" :src="heartIcon" mode="aspectFit"></image>
									<text class="watch_num_text">{{ item.popularityNum }}</text>
								</view>
							</view>

							<view class="right">
								<text class="live_name">{{ item.liveName }}</text>
								<block v-if="item.goodsList.length">
									<view class="goods_info">
										<view class="first_goods"
											:style="{ backgroundImage: 'url(' + item.goodsList[0].goodsImage + ')'}">
											<text
												class="goods_price">{{ $L('¥') }}{{ item.goodsList[0].goodsPrice }}</text>
										</view>
										<block v-if="item.goodsList.length > 1">
											<view class="second_goods"
												:style="{backgroundImage:'url(' + item.goodsList[1].goodsImage + ')'}">
												<text
													class="total">{{item.goodsNum > 99 ? '99+' : item.goodsNum}}</text>
											</view>
										</block>
									</view>
								</block>
							</view>
						</view>
						<block v-if="memberInfo.is_own == 1">
							<view class="zhibo_orderBox">
								<view class="zhibo_order_item">
									<text class="zhibo_order_text">{{ $L('订单销量') }}：</text>
									<text class="zhibo_order_xiaoliang">{{item.order_num}}</text>
								</view>
								<view class="zhibo_order_item ml_77">
									<text class="zhibo_order_text">{{ $L('订单金额') }}：</text>
									<text class="zhibo_order_price">{{ $L('￥') }}{{ item.order_total_price }}</text>
								</view>
							</view>
						</block>
						<block
							v-if="memberInfo.is_own == 1 &&(settingData.live_virtual_click_num_switch == 1 ||settingData.live_virtual_like_num_switch == 1)">
							<view class="detail_num">
								<block v-if="settingData.live_virtual_click_num_switch == 1">
									<text>{{ $L('观看数') }}</text>
									<text class="color_9">{{ $L('(虚/实)') }}：</text>
									<text
										class="color_3 mr_30">{{ item.virtual_click_num }}/{{item.real_click_num}}</text>
								</block>
								<block v-if="settingData.live_virtual_like_num_switch == 1">
									<text>{{ $L('人气数') }}</text>
									<text class="color_9">{{ $L('(虚/实)') }}：</text>
									<text class="color_3">{{ item.virtual_like_num }}/{{ item.real_like_num }}</text>
								</block>
							</view>
						</block>
					</view>
				</block>

				<block v-else>
					<view class="live_back_item">
						<view class="top_info">
							<text class="status_text">{{ item.liveStateValue }}</text>
							<text class="publish_time">{{ item.createTime }}</text>
							<image class="forbid_flag" v-if="item.playState == 2" :src="forbidPlayImg"></image>
						</view>
						<view class="live_item" @tap="goGoodsDetail">
							<view class="live_img" @tap="goLiving(item, index)">
								<image class="play_btn_icon" v-if="item.liveState == 4" :src="videoPlayImg"
									mode="aspectFit"></image>
								<image class="img" :src="item.liveCover" mode="aspectFit"></image>
								<view v-if="item.liveState == 4" class="live_time">
									<text v-if="item.liveState == 4">{{item.playbackTime}}</text>
								</view>
							</view>
							<view class="right">
								<view class="top">
									<text class="name">{{ item.liveName }}</text>
									<text
										class="recommend_num">{{ $L('推荐商品') }}：{{item.goodsNum * 1 > 99 ? '99+' : item.goodsNum}}</text>
								</view>
								<view class="bottom">
									<view class="click_num" style="margin-left: -12rpx">
										<image class="img" :src="userWatchIcon" mode="aspectFit"></image>
										<text>{{ item.viewingNum }}</text>
									</view>
									<view class="click_num" style="margin-left: 20rpx">
										<image class="img" :src="userFavIcon" mode="aspectFit"></image>
										<text>{{ item.popularityNum }}</text>
									</view>
								</view>
							</view>
						</view>
						<block v-if="memberInfo.is_own == 1">
							<view class="zhibo_orderBox">
								<view class="zhibo_order_item">
									<text class="zhibo_order_text">{{ $L('订单销量') }}：</text>
									<text class="zhibo_order_xiaoliang">{{item.order_num}}</text>
								</view>
								<view class="zhibo_order_item ml_77">
									<text class="zhibo_order_text">{{ $L('订单金额') }}：</text>
									<text class="zhibo_order_price">{{ $L('￥') }}{{ item.order_total_price }}</text>
								</view>
							</view>
						</block>
						<block
							v-if="memberInfo.is_own == 1 &&(settingData.live_virtual_click_num_switch == 1 ||settingData.live_virtual_like_num_switch == 1)">
							<view class="detail_num">
								<block v-if="settingData.live_virtual_click_num_switch == 1">
									<text>{{ $L('观看数') }}</text>
									<text class="color_9">{{ $L('(虚/实)') }}：</text>
									<text class="color_3">{{ item.virtual_click_num }}/{{item.real_click_num}}</text>
								</block>
								<block v-if="settingData.live_virtual_like_num_switch == 1">
									<text class="ml_30">{{ $L('人气数') }}</text>
									<text class="color_9">{{ $L('(虚/实)') }}：</text>
									<text class="color_3">{{ item.virtual_like_num }}/{{ item.real_like_num }}</text>
								</block>
							</view>
						</block>
						<block v-if="memberInfo.is_own == 1 && item.status != 1 && item.status != 4">
							<image :src="zhibo_shanchu" class="zhibo_delHuifang" :data-id="item.live_id"
								@tap="del_huifang"></image>
						</block>
					</view>
				</block>
			</block>

			<!-- 数据加载完毕 -->
			<dataLoaded :showFlag="!hasmore && liveList.length > 0" />

			<!-- 数据加载中 -->
			<dataLoading :showFlag="hasmore && loading && !firstLoading" />

			<!-- 页面loading -->
			<pageLoading :firstLoading="firstLoading" :loadingIcon="imgUrl + 'svideo/page_loading_icon.gif'"
				:topVal="467" />

			<block>
				<view class="video_bottom"></view>
			</block>
		</scroll-view>
		<!-- 页面空数据 -->
		<emptyData :showFlag="!firstLoading && !liveList.length"
			:emptyIcon="imgUrl + 'svideo/live_list_empty_icon.png'" />
	</view>
</template>

<script>
	import {
		checkPageHasMore
	} from '@/utils/live'
	import {
		mapState
	} from "vuex"
	import pageLoading from '../../component/pageLoading.vue'
	import emptyData from '../../component/emptyData.vue'
	import dataLoading from '../../component/dataLoading.vue'
	import dataLoaded from '../../component/dataLoaded.vue'
	const imgUrl = process.env.VUE_APP_IMG_URL

	export default {
		components: {
			pageLoading,
			emptyData,
			dataLoading,
			dataLoaded
		},
		data() {
			return {
				pn: 1,
				//当前页
				pageSize: 10,
				//每页数据
				loading: false,
				//数据加载状态
				liveList: [],
				//直播列表数据
				forbidPlayImg: process.env.VUE_APP_IMG_URL + 'svideo/forbid_play.png',
				videoPauseImg: process.env.VUE_APP_IMG_URL + 'svideo/video_pause.png',
				videoPlayImg: process.env.VUE_APP_IMG_URL + 'svideo/video_play.png',
				userWatchIcon: process.env.VUE_APP_IMG_URL + 'svideo/user_watch.png',
				userFavIcon: process.env.VUE_APP_IMG_URL + 'svideo/user_fav.png',
				listLivingIcon: process.env.VUE_APP_IMG_URL + 'svideo/list_living.gif',
				heartIcon: process.env.VUE_APP_IMG_URL + 'svideo/heart_icon.png',
				zhibo_shanchu: process.env.VUE_APP_IMG_URL + 'svideo/zhibo_shanchu.png',
				hasmore: true,
				//是否还有数据，用于页面展示
				firstLoading: true,
				//是否初次加载，是的话展示页面居中的loading效果，
				imgUrl: process.env.VUE_APP_IMG_URL, //图片地址
				//wx-1-start
				// #ifdef MP
				menuButtonHeight: uni.getMenuButtonBoundingClientRect().height + 'px',
				menuButtonTop: uni.getMenuButtonBoundingClientRect().top + 'px',
				// #endif
				//wx-1-end
			}
		},

		props: {
			memberInfo: {
				// 用户信息
				type: Object
			},
			authorInfo: {
				// 作者信息
				type: Object
			},
			author_id: {
				// 作者id
				type: String,
				default: ''
			},
			settingData: {
				// 平台设置信息
				type: Object
			}
		},

		mounted() {
			uni.$on('updateListWatchNum', (e) => {
				this.liveList[e.index].viewingNum =Number(e.num)
			})
			uni.$on('updateLikeNum', (e) => {
				this.liveList[e.index].popularityNum =
					Number(this.liveList[e.index].popularityNum) + 1
			})
			this.getLiveList()
		},

		computed: {
			...mapState(['hasLogin']),
			topHeight() {
				//wx-2-start
				// #ifdef MP-WEIXIN
				return `calc(100vh - ${this.menuButtonTop} - ${this.menuButtonHeight} - 380rpx - 49px)`
				// #endif
				//wx-2-end
				// #ifndef MP-WEIXIN
				return ''
				// #endif 
			}
		},

		onShow() {
			this.getLiveList()
		},

		destroyed() {
			uni.$off('updateListWatchNum')
			uni.$off('updateLikeNum')
		},

		methods: {
			//获取直播数据
			getLiveList() {
				this.loading = true
				let {
					author_id,
					firstLoading,
					liveList,
					pageSize,
					pn,
					hasmore
				} = this

				let param = {}
				param.data = {}
				param.data.authorId = this.authorInfo.authorId
				param.data.pageSize = pageSize
				param.data.current = pn
				param.url = 'v3/video/front/video/live/playbackList'
				param.method = 'GET'
				this.$request(param).then((res) => {
					if (res.state == 200) {
						let list = res.data.list
						if (pn == 1) {
							this.liveList = list
						} else {
							this.liveList = this.liveList.concat(list)
						}
						if (checkPageHasMore(res.data.pagination)) {
							this.pn++
						} else {
							this.hasmore = false
						}
					}

					if (firstLoading) {
						this.firstLoading = false
					}
					this.loading = false
				})
			},

			getMoreGoods() {
				if (this.hasmore) {
					this.getLiveList()
				}
			},

			//进入直播或者回放
			goLiving(item, index) {
				if (item.liveState == 1) {
					//待直播
					//wx-3-start
					
					//wx-3-end
					//app-1-start
					
					//app-1-end
					return
				}

				if (item.liveState == 3) {
					uni.showToast({
						title: this.$L('视频录制中，请稍后再试'),
						icon: 'none'
					})
					return
				}

				let page = getCurrentPages()
				let len = page.length
				if (len > 4) {
					this.$livePlayNav({
						live_id: item.liveId,
						author_id: item.authorId,
						index
					}, 'replace')
				} else {
					this.$livePlayNav({
						live_id: item.liveId,
						author_id: item.authorId,
						index
					}, 'push')
				}
			},

			//进入播放页面
			goLivePlay(e) {
				let cur_live_id = e.currentTarget.dataset.liveid
				let page = getCurrentPages()
				let len = page.length
				if (len > 4) {
					this.$livePlayNav({
						live_id: cur_live_id
					}, 'replace')
				} else {
					this.$livePlayNav({
						live_id: cur_live_id
					}, 'push')
				}
			},

			//跳转商品详情页
			goGoodsDetail(e) {
				let gid = e.currentTarget.dataset.gid

				if (gid) {
					let page = getCurrentPages()
					let len = page.length

					if (len > 4) {
						this.$Router.replace({
							path: '/pages/goods_detail/goods_detail',
							query: {
								gid
							}
						})
					} else {
						this.$Router.push({
							path: '/pages/goods_detail/goods_detail',
							query: {
								gid
							}
						})
					}
				}
			},

			// 点击删除回放
			del_huifang(e) {
				let live_id = e.currentTarget.dataset.id
				uni.showModal({
					title: this.$L('提示'),
					content: this.$L('确定删除该直播回放?'),
					success: (res) => {
						if (res.confirm) {
							request({
								url: getApp().globalData.svideo_url +
									'/index.php?app=live&mod=softDelLive',
								method: 'post',
								data: {
									key: uni.getStorageSync('token'),
									live_id
								}
							}).then((res) => {
								if (res.state == 200) {
									this.setData({
										pn: 1
									})
									this.getLiveList()
									this.$emit('liveEvent', {
										detail: {}
									})
								}
							})
						}
					}
				})
			}
		}
	}
</script>
<style>
	view {
		box-sizing: content-box;
	}

	.live_user_tab_content_scroll {
		height: calc(100vh - 380rpx);
		background: #f5f5f5;
	}

	.live_user_tab_content_scroll .live_back_item {
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;
		position: relative;
		background: #fff;
		width: 672rpx;
		padding: 20rpx;
		margin-top: 20rpx;
		border-radius: 14rpx;
		margin-left: 20rpx;
		padding-bottom: 20rpx;
	}

	.live_user_tab_content_scroll .live_back_item .top_info {
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
	}

	.live_user_tab_content_scroll .live_back_item .top_info .status_text {
		padding: 0 10rpx;
		height: 30rpx;
		color: #fff;
		background-color: #bcaefe;
		font-size: 22rpx;
		border-radius: 15rpx;
		text-align: center;
		line-height: 30rpx;
	}

	.live_user_tab_content_scroll .live_back_item .top_info .publish_time {
		margin-left: 20rpx;
		color: #949494;
		font-size: 22rpx;
	}

	.live_user_tab_content_scroll .live_back_item .top_info .forbid_flag {
		position: absolute;
		top: 0;
		right: 8rpx;
		width: 127rpx;
		height: 37rpx;
	}

	.live_user_tab_content_scroll .live_item {
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
		margin-top: 20rpx;
		/* padding-bottom: 22rpx; */
		width: 100%;
	}

	.live_user_tab_content_scroll .live_item .live_img {
		width: 246rpx;
		height: 246rpx;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		position: relative;
	}

	.live_user_tab_content_scroll .live_item .live_img .play_btn_icon {
		height: 48rpx;
		max-width: 48rpx;
		position: absolute;
		top: 99rpx;
		left: 99rpx;
		z-index: 2;
	}

	.live_user_tab_content_scroll .live_item .live_img .img {
		max-height: 100%;
		max-width: 100%;
		border-radius: 15rpx;
	}

	.live_user_tab_content_scroll .live_item .live_img .live_time {
		position: absolute;
		bottom: 0;
		right: 0;
		z-index: 2;
		background-color: rgba(0, 0, 0, 0.3);
		border-radius: 15rpx 0 15rpx 0;
		padding: 0 8rpx;
	}

	.live_user_tab_content_scroll .live_item .live_img .live_time text {
		color: #fff;
		font-size: 22rpx;
		height: 38rpx;
		line-height: 38rpx;
	}

	.live_user_tab_content_scroll .live_item .right {
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: flex-start;
		height: 220rpx;
		padding: 0rpx 20rpx;
		padding-top: 24rpx;
	}

	.live_user_tab_content_scroll .live_item .right .top {
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;
	}

	.live_user_tab_content_scroll .live_item .right .top .name {
		color: #2d2d2d;
		font-size: 28rpx;
		line-height: 42rpx;
		width: 414rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		word-break: break-word;
	}

	.live_user_tab_content_scroll .live_item .right .top .recommend_num {
		color: #949494;
		font-size: 26rpx;
		line-height: 36rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		margin-top: 30rpx;
		width: 414rpx;
	}

	.live_user_tab_content_scroll .live_item .right .bottom {
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-self: flex-end;
		width: 414rpx;
	}

	.live_user_tab_content_scroll .live_item .right .bottom .price .unit {
		color: #fc1c1c;
		font-size: 24rpx;
	}

	.live_user_tab_content_scroll .live_item .right .bottom .price .num {
		font-size: 36rpx;
		color: #fc1c1c;
		margin-left: 3rpx;
	}

	.live_user_tab_content_scroll .live_item .right .bottom .click_num {
		display: flex;
		flex-direction: row;
		align-items: center;
	}

	.live_user_tab_content_scroll .live_item .right .bottom .click_num .add_cart {
		width: 42rpx;
		height: 42rpx;
	}

	.live_user_tab_content_scroll .live_item .right .bottom .click_num image {
		width: 50rpx;
		height: 50rpx;
	}

	.live_user_tab_content_scroll .live_item .right .bottom .click_num text {
		color: #999;
		font-size: 24rpx;
	}

	.live_list_item {
		width: 750rpx;
		height: 380rpx;
		background-color: #fff;
		border-radius: 15rpx;
		overflow: hidden;
		padding: 0 20rpx 0 20rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
	}

	.live_list_item_first {
		margin-top: 0 !important;
	}

	.live_list_item .left {
		width: 360rpx;
		height: 360rpx;
		border-radius: 15rpx;
		background-size: contain;
		background-repeat: no-repeat;
		/* background-size:100%;  */
		position: relative;
	}

	.live_list_item .left .left_top {
		position: absolute;
		top: 0;
		left: 0;
		padding-right: 10rpx;
		height: 40rpx;
		background-color: rgba(0, 0, 0, 0.3);
		display: flex;
		flex-direction: row;
		align-items: flex-end;
		justify-content: flex-end;
		border-bottom-right-radius: 15rpx;
		border-top-left-radius: 15rpx;
	}

	.left .left_top .back_img {
		width: 46rpx;
		height: 46rpx;
	}

	.left .left_top .back_img_living {
		width: 35rpx;
		height: 35rpx;
		margin-top: -14rpx;
	}

	.left .watch_num_text {
		color: #fff;
		font-size: 20rpx;
	}

	.live_list_item .left .right_bottom {
		position: absolute;
		bottom: 10rpx;
		right: 5rpx;
		display: flex;
		flex-direction: column;
		align-items: flex-end;
		height: 75rpx;
	}

	.left .right_bottom .heart_icon {
		width: 48rpx;
		height: 48rpx;
	}

	.left .right_bottom text {
		margin-right: 10rpx;
	}

	.live_list_item .right {
		display: flex;
		flex: 1;
		flex-direction: column;
		align-items: flex-start;
		justify-content: center;
		padding: 30rpx 0rpx 30rpx 15rpx;
		height: 300rpx;
	}

	.right .live_name {
		height: 72rpx;
		color: '#2D2D2D';
		font-size: 26rpx;
		line-height: 36rpx;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.right .member_info,
	.right .goods_info {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: flex-start;
		margin-top: 20rpx;
	}

	.right .member_info .avator {
		width: 42rpx;
		height: 42rpx;
		border-radius: 21rpx;
	}

	.right .member_info .nickname {
		color: '#949494';
		font-size: 22rpx;
		margin-left: 10rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.right .goods_info .first_goods,
	.right .goods_info .second_goods {
		width: 140rpx;
		height: 140rpx;
		border-radius: 15rpx;
		overflow: hidden;
		margin-right: 10rpx;
		background-repeat: no-repeat;
		background-size: contain;
	}

	.right .goods_info .second_goods {
		margin-right: 0;
	}

	.right .goods_info .first_goods .goods_price {
		display: flex;
		width: 140rpx;
		height: 120rpx;
		flex-direction: row;
		justify-content: center;
		align-items: flex-end;
		color: #fff;
		margin-top: 20rpx;
		background: -webkit-linear-gradient(to bottom,
				rgba(221, 221, 221, 0),
				rgba(221, 221, 221, 0),
				#000);
		font-size: 20rpx;
		line-height: 44rpx;
		border-radius: 0 0 15rpx 15rpx;
	}

	.right .goods_info .second_goods .total {
		display: flex;
		width: 140rpx;
		height: 140rpx;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		background-color: rgba(0, 0, 0, 0.3);
		font-size: 24rpx;
		color: #fff;
		border-radius: 15rpx;
	}

	.user_living_item {
		width: 750rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: flex-start;
		background-color: #fff;
	}

	.user_living_item .top_info {
		width: 710rpx;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: flex-end;
		padding-left: 20rpx;
		background: #fff;
		height: 70rpx;
		padding-bottom: 10rpx;
	}

	.user_living_item .top_info .status_text {
		width: 80rpx;
		height: 30rpx;
		background: linear-gradient(45deg,
				rgba(252, 28, 28, 1) 0%,
				rgba(255, 163, 0, 1) 100%);
		border-radius: 15rpx;
		font-size: 22rpx;
		color: #fff;
		text-align: center;
		line-height: 30rpx;
	}

	.user_living_item .top_info .publish_time {
		margin-left: 20rpx;
		color: #949494;
		font-size: 22rpx;
	}

	.detail_num {
		width: 100%;
		height: 80rpx;
		display: flex;
		flex-direction: row;
		justify-content: flex-start;
		align-items: center;
		border-top: 1px solid rgba(0, 0, 0, 0.1);
	}

	.user_living_item .detail_num {
		width: 710px;
		padding-left: 20rpx;
		background: #fff;
	}

	.detail_num text {
		color: #666;
		font-size: 24rpx;
	}

	.detail_num .color_9 {
		color: #999;
	}

	.detail_num .color_3 {
		color: #333;
		font-weight: bold;
	}

	.ml_30 {
		margin-left: 30rpx;
	}

	.ml_10 {
		margin-left: 10rpx;
	}

	.mr_30 {
		margin-right: 30rpx;
	}

	.ml_77 {
		margin-left: 77rpx;
	}

	.zhibo_orderBox {
		height: 80rpx;
		display: flex;
		align-items: center;
	}

	.user_living_item .zhibo_orderBox {
		padding-left: 20rpx;
	}

	.zhibo_orderBox .zhibo_order_text {
		color: #666666;
		font-size: 24rpx;
	}

	.zhibo_orderBox .zhibo_order_xiaoliang {
		color: #ff4c05;
		font-size: 30rpx;
		font-weight: bold;
	}

	.zhibo_orderBox .zhibo_order_price {
		color: #d90e0e;
		font-size: 30rpx;
		font-weight: bold;
	}

	.zhibo_delHuifang {
		position: absolute;
		right: 0;
		bottom: 0;
		width: 50rpx;
		height: 50rpx;
	}

	.video_bottom {
		height: 161rpx;
		width: 750rpx;
	}

	/* #ifdef H5 */
	.video_bottom {
		height: 261rpx;
	}

	/* #endif */
</style>