<template>
  <view>
    <!-- 直播/短视频 个人中心 喜欢模块 -->
    <scroll-view class="live_user_tab_content_scroll" scroll-y="true" @scrolltolower="getMoreGoods" v-if="favoriteList.length > 0" :style="{height:topHeight}">
      <view class="favorite">
        <view v-for="(item, index) in favoriteList" :key="index" class="f_item" :style="'margin-right:' + (index % 3 == 2 ? '0rpx' : '3rpx')"
          @tap="goVideoPlay(item)">
          <image class="f_img" :src="item.videoImage" mode="aspectFill"></image>
          <view class="bottom">
            <view class="left">
              <image :src="favIcon"></image>
              <text>{{ item.likeNum }}</text>
            </view>
          </view>
          <view class="article" v-if="item.videoType == 2">
            <image :src="articleIcon"></image>
          </view>
        </view>
      </view>
      <!-- 数据加载完毕 -->
      <dataLoaded :showFlag="!hasmore && favoriteList.length > 0" />

      <!-- 数据加载中 -->
      <dataLoading :showFlag="hasmore && loading && !firstLoading" />

      <!-- 页面loading -->
      <pageLoading
        :firstLoading="firstLoading"
        :loadingIcon="imgUrl + 'svideo/page_loading_icon.gif'"
        :topVal="467"
      />

      <block v-if="authorInfo.isSelf">
        <view class="video_bottom"></view>
      </block>
    </scroll-view>
    <!-- 页面空数据 -->
    <view class="live_user_tab_content_scroll" v-if="!firstLoading && !favoriteList.length">
      <emptyData
        :showFlag="!firstLoading && !favoriteList.length"
        :emptyIcon="imgUrl + 'svideo/live_list_empty_icon.png'"
      />
    </view>
  </view>
</template>

<script>
import { checkPageHasMore } from '@/utils/live'
import { mapState } from "vuex"
import pageLoading from '../../component/pageLoading.vue'
import emptyData from '../../component/emptyData.vue'
import dataLoading from '../../component/dataLoading.vue'
import dataLoaded from '../../component/dataLoaded.vue'
const imgUrl = process.env.VUE_APP_IMG_URL

export default {
  components: {
    pageLoading,
    emptyData,
    dataLoading,
    dataLoaded
  },
  data() {
    return {
      pn: 1,
      //当前页
      pageSize: 10,
      //每页数据
      loading: false,
      //数据加载状态
      favoriteList: [],
      //喜欢列表数据
      favIcon: imgUrl + 'svideo/fav_w.png',
      playIcon: imgUrl + 'svideo/watch_w.png',
      articleIcon: imgUrl + 'svideo/articleIcon.png',
      hasmore: true,
      //是否还有数据，用于页面展示
      firstLoading: true,
      //是否初次加载，是的话展示页面居中的loading效果，
      imgUrl: process.env.VUE_APP_IMG_URL, //图片地址
	  //wx-3-start
	  // #ifdef MP
	  menuButtonHeight: uni.getMenuButtonBoundingClientRect().height + 'px',
	  menuButtonTop: uni.getMenuButtonBoundingClientRect().top + 'px',
	  // #endif
	  //wx-3-end
    }
  },

  props: {
    memberInfo: {
      // 用户信息
      type: Object
    },
    authorInfo: {
      // 作者信息
      type: Object
    },
    settingData: {
      // 平台设置信息
      type: Object
    }
  },

  mounted() {
    this.getFavoriteList()
  },
  
  computed: {
    ...mapState(['hasLogin']),
  	topHeight() {
		//wx-1-start
  		// #ifdef MP-WEIXIN
  		return `calc(100vh - ${this.menuButtonTop} - ${this.menuButtonHeight} - 380rpx - 49px)`
  		// #endif
  		//wx-1-end
  		// #ifndef MP-WEIXIN
  		return ''
  		// #endif 
  	}
  },

  methods: {
    //获取喜欢数据
    getFavoriteList() {
      this.loading = true
      let { firstLoading, favoriteList, pageSize, pn, hasmore } = this

      let param = {}
      param.data = {}
      param.data.current = pn
      param.data.pageSize = 20
      param.url = 'v3/video/front/video/author/likeList'
      param.method = 'GET'
      this.$request(param).then((res) => {
        if (res.state == 200) {
          let list = res.data.list

          if (pn == 1) {
            this.favoriteList = list
          } else {
            this.favoriteList = this.favoriteList.concat(list)
          }
          if (checkPageHasMore(res.data.pagination)) {
            this.pn++
          } else {
            this.hasmore = false
          }
        }

        if (this.firstLoading) {
          this.firstLoading = false
        }
        this.loading = false
      })
    },

    getMoreGoods() {
      if (this.hasmore) {
        this.getFavoriteList()
      }
    },

    //进入播放页面
    goVideoPlay(item) {
      if (item.videoType == 2) {
        this.$Router.push({
          path: '/extra/graphic/graphicDetail',
          query: { video_id: item.videoId }
        })
      } else {
		this.$videoPlayNav({video_id: item.videoId})
      }
    }
  }
}
</script>
<style scoped>
.live_user_tab_content_scroll {
  height: calc(100vh - 467rpx);
  background: #333;
  overflow: auto;
}

.live_user_tab_content_scroll .favorite {
  position: relative;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: flex-start;
}

.live_user_tab_content_scroll .favorite .f_item {
  width: calc((100% - 6rpx) / 3);
  height: 323rpx;
  overflow: hidden;
  position: relative;
  margin-bottom: 3rpx;
  margin-right: 3rpx;
}

.live_user_tab_content_scroll .favorite .f_item .f_img {
  width: 100%;
  height: 100%;
}

.f_item ::v-deep img {
  display: none;
}

.live_user_tab_content_scroll .favorite .f_item .bottom {
  position: absolute;
  bottom: 0;
  left: 0rpx;
  right: 0rpx;
  height: 92rpx;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-end;
  z-index: 2;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.8));
  padding: 0 10rpx 5rpx 5rpx;
}

.live_user_tab_content_scroll .favorite .f_item .bottom .left,
.live_user_tab_content_scroll .favorite .f_item .bottom .right {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}

.live_user_tab_content_scroll .favorite .f_item .bottom .right {
  justify-content: flex-end;
}

.live_user_tab_content_scroll .favorite .f_item .bottom .left image,
.live_user_tab_content_scroll .favorite .f_item .bottom .right image {
  width: 42rpx;
  height: 42rpx;
}

.live_user_tab_content_scroll .favorite .f_item .bottom .left text,
.live_user_tab_content_scroll .favorite .f_item .bottom .right text {
  color: #fff;
  font-size: 24rpx;
  margin-left: 3rpx;
}

.article {
  position: absolute;
  top: 15rpx;
  right: 20rpx;
  z-index: 9;
}
.article image {
  width: 34rpx;
  height: 34rpx;
}

.video_bottom {
  height: 161rpx;
  width: 750rpx;
}
/* #ifdef H5 */
.video_bottom {
  height: 140rpx;
}
/* #endif */
/* app-1-start */





/* app-1-end */
/* wx-2-start */
/* #ifdef MP-WEIXIN */
.video_bottom {
  height: 90rpx;
}
/* #endif */
/* wx-2-end */
</style>
