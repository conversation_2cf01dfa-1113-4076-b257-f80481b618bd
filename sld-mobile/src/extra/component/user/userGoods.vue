<template>
  <view>
    <!-- 直播/短视频 个人中心 商品模块 -->
    <scroll-view
      class="live_user_tab_content_scroll"
      scroll-y="true"
      @scrolltolower="getMoreGoods"
      v-if="goodsData.length > 0"
	  :style="{height:topHeight}"
	>
      <view>
        <view
          v-for="(item, index) in goodsData"
          :key="index"
          class="goods_item"
          @click="goGoodsDetail(item.productId, item.goodsId, item.videoId)"
        >
          <view class="goods_img">
            <image :src="item.goodsImage" mode="aspectFit"></image>
          </view>
          <view class="right">
            <view class="top">
              <text class="name">{{ item.goodsName }}</text>
              <text class="jingle">{{ item.goodsBrief }}</text>
            </view>
            <view class="bottom">
              <view class="price">
                <text class="unit">¥</text>
                <text class="num">{{ item.goodsPrice }}</text>
              </view>
              <view class="click_num"> </view>
              <view class="click_num">        
				<view class="" @click.stop="addCart(item.productId)" v-if="author_id">
					<svgGroup type="addCart1" :color="diyStyle_var['--color_video_main']" width="20" height="20"></svgGroup>
				</view>
                <block v-if="!author_id">
                  <image class="img" :src="eyeIcon" mode="aspectFit"></image>
                  <text>{{ item.clickNum }}</text>
                </block>
              </view>
            </view>
          </view>
        </view>

        <!-- 数据加载完毕 -->
        <dataLoaded :showFlag="!hasmore && goodsData.length > 0" />

        <!-- 数据加载中 -->
        <dataLoading :showFlag="hasmore && loading && !firstLoading" />

        <!-- 页面loading -->
        <pageLoading
          :firstLoading="firstLoading"
          :loadingIcon="imgUrl + 'svideo/page_loading_icon.gif'"
          :topVal="467"
        />
      </view>
      <block v-if="authorInfo.isSelf">
        <view class="video_bottom"></view>
      </block>
    </scroll-view>
    <!-- 页面空数据 -->
    <emptyData
      :showFlag="!firstLoading && !goodsData.length"
      :emptyIcon="imgUrl + 'svideo/live_list_empty_icon.png'"
    />
  </view>
</template>

<script>
import { checkPageHasMore } from '@/utils/live'
import { mapState } from 'vuex'
import pageLoading from '../../component/pageLoading.vue'
import emptyData from '../../component/emptyData.vue'
import dataLoading from '../../component/dataLoading.vue'
import dataLoaded from '../../component/dataLoaded.vue'
const imgUrl = process.env.VUE_APP_IMG_URL

export default {
  components: {
    pageLoading,
    emptyData,
    dataLoading,
    dataLoaded
  },
  data() {
    return {
      pn: 1,
      //当前页
      pageSize: 10,
      //每页数据
      loading: false,
      //数据加载状态
      goodsData: [],
      //商品数据
      addCartIcon: imgUrl + 'svideo/add_cart1.png',
      eyeIcon: imgUrl + 'svideo/eye.png',
      hasmore: true,
      //是否还有数据，用于页面展示
      firstLoading: true,
      //是否初次加载，是的话展示页面居中的loading效果，
      imgUrl: process.env.VUE_APP_IMG_URL, //图片地址
      stat_end: 1, //终端，默认为1，pc端
	  //wx-4-start
	  // #ifdef MP
	  menuButtonHeight: uni.getMenuButtonBoundingClientRect().height + 'px',
	  menuButtonTop: uni.getMenuButtonBoundingClientRect().top + 'px',
	  // #endif
	  //wx-4-end
    }
  },
  props: {
    memberInfo: {
      // 用户信息
      type: Object
    },
    authorInfo: {
      // 作者信息
      type: Object
    },
    author_id: {
      // 作者id
      type: String,
      default: ''
    },
    settingData: {
      // 平台设置信息
      type: Object
    }
  },

  mounted() {
    this.getGoods()
    this.getPlatform()
  },

  computed: {
    ...mapState(['hasLogin']),
	topHeight() {
		//wx-1-start
		// #ifdef MP-WEIXIN
		return `calc(100vh - ${this.menuButtonTop} - ${this.menuButtonHeight} - 380rpx - 49px)`
		// #endif
		//wx-1-end
		// #ifndef MP-WEIXIN
		return ''
		// #endif 
	}
  },

  methods: {
    //获取当前终端的方法
    getPlatform() {
      //判断终端
      this.stat_end = 1
      // #ifdef H5
      this.stat_end = 2
      // #endif
	  //wx-2-start
      // #ifdef MP-WEIXIN
      this.stat_end = 3
      // #endif
	  //wx-2-end
	  /* app-1-start */







	  /* app-1-end */
    },
    //获取商品数据
    getGoods() {
      this.loading = true
      let { author_id, firstLoading, goodsData, pageSize, pn, hasmore } = this
      let param = {}
      param.data = {}
      if (author_id != '') {
        param.data.authorId = author_id
      }
      param.data.current = pn
      param.data.pageSize = pageSize
      param.url = 'v3/video/front/video/author/goodsList'
      param.method = 'GET'
      this.$request(param).then((res) => {
        if (res.state == 200) {
          let result = res.data.list

          if (this.pn == 1) {
            this.goodsData = result
          } else {
            this.goodsData = goodsData.concat(result)
          }

          if (checkPageHasMore(res.data.pagination)) {
            this.pn++
          } else {
            this.hasmore = false
          }
        }

        if (this.firstLoading) {
          this.firstLoading = false
        }
        this.loading = false
      })
    },

    getMoreGoods() {
      if (this.hasmore) {
        this.getGoods()
      }
    },

    //跳转商品详情页
    goGoodsDetail(productId, goodsId, videoId) {
      let index = this.goodsData.findIndex((i) => i.productId == productId)
      if (index > -1) {
        this.goodsData[index].clickNum += 1
      }
      this.$Router.push({
        path: '/standard/product/detail',
        query: {
          productId,
          videoId,
          goodsId
        }
      })
    },

    //加入购物车事件
    // 加入购物车
    addCart(productId) {
      if (this.hasLogin) {
        //登录
        let param = {}
        param.url = 'v3/business/front/cart/add'
        param.method = 'POST'
        param.data = {
          productId,
          number: 1
        }
        this.$request(param).then((res) => {
          if (res.state == 200) {
            uni.showToast({
              title: res.msg,
              icon: 'none'
            })
          } else {
            uni.showToast({
              title: res.msg,
              icon: 'none',
              duration: 700
            })
          }
        })
      } else {
        //未登录
        let cart_list = {
          storeCartGroupList: [
            {
              promotionCartGroupList: [
                {
                  cartList: [
                    {
                      buyNum: 1,
                      goodsId: this.goods_info.goodsId,
                      productId:
                        this.goods_info.productId ||
                        this.goods_info.defaultProductId,
                      productImage: this.goods_info.goodsPic
                        ? this.goods_info.goodsPic
                        : this.goods_info.goodsImage,
                      goodsName: this.goods_info.goodsName,
                      isChecked: 1,
                      productPrice: this.goods_info.goodsPrice,
                      productStock: this.goods_info.productStock
                    }
                  ]
                }
              ],
              storeId: this.goods_info.storeId,
              storeName: this.goods_info.storeName,
              checkedAll: true
            }
          ],
          checkedAll: true,
          invalidList: []
        }

        let local_cart_list = uni.getStorageSync('cart_list') //购物车列表本地缓存
        if (local_cart_list) {
          let tmp_list1 = []
          let tmp_list2 = []
          cart_list.storeCartGroupList.forEach((item) => {
            item.promotionCartGroupList.forEach((item1) => {
              item1.cartList.forEach((item2) => {
                local_cart_list.storeCartGroupList.forEach((v) => {
                  v.promotionCartGroupList.forEach((v1) => {
                    v1.cartList.forEach((v2) => {
                      if (
                        v2.productId == item2.productId &&
                        v.storeId == item.storeId
                      ) {
                        tmp_list1.push(v)
                      }
                    })
                    tmp_list2 = local_cart_list.storeCartGroupList.filter(
                      (v) => {
                        return v.storeId == item.storeId
                      }
                    )
                  })
                })
              })
            })
          })
          if (tmp_list1.length > 0 && tmp_list2.length > 0) {
            //同一店铺同一商品
            local_cart_list.storeCartGroupList.map((item) => {
              item.promotionCartGroupList.map((item1) => {
                item1.cartList.map((item2) => {
                  if (
                    item2.productId ==
                      (this.goods_info.productId ||
                        this.goods_info.defaultProductId) &&
                    item.storeId == this.goods_info.storeId
                  ) {
                    item2.buyNum += 1
                  }
                })
              })
            })
          } else if (tmp_list1.length == 0 && tmp_list2.length > 0) {
            //同一店铺不同商品
            local_cart_list.storeCartGroupList.map((item) => {
              if (item.storeId == this.goods_info.storeId) {
                item.promotionCartGroupList.map((item2) => {
                  item2.cartList.push(
                    cart_list.storeCartGroupList[0].promotionCartGroupList[0]
                      .cartList[0]
                  )
                })
              }
            })
          } else {
            //不同店铺不同商品
            local_cart_list.storeCartGroupList.push(
              cart_list.storeCartGroupList[0]
            )
          }

          this.$api.msg(this.$L('加入购物车成功！'))
          uni.setStorageSync('cart_list', local_cart_list)
        } else {
          this.$api.msg(this.$L('加入购物车成功！'))
          uni.setStorageSync('cart_list', cart_list)
        }
      }
    }
  }
}
</script>
<style>
.live_user_tab_content_scroll {
  height: calc(100vh - 380rpx);
  background: #f5f5f5;
  z-index: 100;
}

.goods_item {
  width: 710rpx;
  padding: 20rpx;
  background: #fff;
  border-radius: 14rpx;
  height: 236rpx;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  margin-top: 20rpx;
  margin-left: 20rpx;
  box-sizing: content-box;
}

.goods_item .goods_img {
  width: 236rpx;
  height: 236rpx;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.goods_item .goods_img image {
  max-height: 100%;
  max-width: 100%;
  border-radius: 15rpx;
}

.goods_item .right {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  height: 216rpx;
  padding: 10rpx 20rpx;
}

.goods_item .right .top {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}

.goods_item .right .top .name {
  color: #2d2d2d;
  font-size: 28rpx;
  line-height: 42rpx;
  height: 84rpx;
  width: 414rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-word;
}

.goods_item .right .top .jingle {
  color: #949494;
  font-size: 26rpx;
  line-height: 36rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-top: 10rpx;
  width: 414rpx;
}

.goods_item .right .bottom {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-end;
  width: 414rpx;
}

.goods_item .right .bottom .price .unit {
  color:var(--color_video_main);
  font-size: 24rpx;
}

.goods_item .right .bottom .price .num {
  font-size: 36rpx;
  color:var(--color_video_main);
  margin-left: 3rpx;
}

.goods_item .right .bottom .click_num {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.goods_item .right .bottom .click_num .add_cart {
  width: 42rpx;
  height: 42rpx;
}

.goods_item .right .bottom .click_num image {
  width: 42rpx;
  height: 42rpx;
}

.goods_item .right .bottom .click_num text {
  color: #949494;
  font-size: 22rpx;
}

.video_bottom {
  height: 161rpx;
  width: 750rpx;
}

/* #ifdef H5 */
.video_bottom {
  height: 140rpx;
}
/* #endif */

/* wx-3-start */
/* #ifdef MP-WEIXIN */
.video_bottom {
  height: 90rpx;
}
/* #endif */
/* wx-3-end */
</style>
