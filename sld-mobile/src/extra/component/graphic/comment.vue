<template>
	<!-- 评论模块 start -->
	<view class="video_comment" @touchmove.stop.prevent="moveHandle">
		<view class="title">
			<view class="com_t_l">
				<text class="com_t_l_title">{{ $L('全部评论') }}</text>
				<text class="com_t_l_total">{{ $L('共有') }}{{ commentSum }}{{ $L('条评论') }}</text>
			</view>
			<image class="com_t_close" :src="imgUrl + 'svideo/close.png'" @tap="closeComment"></image>
		</view>

		<scroll-view class="comment" scroll-y="true" @scrolltolower="getMoreCom">
			<block v-if="commentList.length > 0">
				<view v-for="(item, index) in commentList" :key="index" class="com_item_wrap" :index="index">
					<view class="com_item">
						<view class="com_item_l">
							<view class="com_avator" :style="'background-image:url(' + item.memberAvatar + ');'">
							</view>
							<view class="com_detail" :data-commentId="item.commentId" :data-authorId="item.authorId"
								:data-memberNickname="item.authorName ? item.authorName : item.fromAuthorName" @tap="replyComment">
								<text class="com_name">{{ item.authorName ? item.authorName : item.fromAuthorName }}</text>
								<text class="com_con">{{ item.content }}</text>
								<view class="com_other">
									<text class="com_time">{{ item.createTime }}</text>
									<text v-if="item.isSelf" class="del_com" data-type="comment" @tap.stop="delCom"
										:data-commentId="item.commentId">{{ $L('删除') }}</text>
								</view>
							</view>
						</view>
						<view class="com_item_r">
							<view class="" :data-commentid="item.commentId" @tap="likeComment">
								<svgGroup type="dianzan" width="40" height="40" px="rpx" v-if="item.isLike"
									:color="diyStyle_var['--color_video_main']"></svgGroup>
								<svgGroup type="dianzan" width="40" height="40" px="rpx" v-else color="#dedede"></svgGroup>
							</view>
							<text>{{ item.likeNum }}</text>
						</view>
					</view>
					<block v-if="item.replyList.length > 0">
						<view v-for="(itemReplay, index2) in item.replyList" :key="index2" class="child" :index="index2">
							<view class="com_item" v-if="index2 < item.limit">
								<view class="com_item_l">
									<view class="com_avator" :style="'background:url(' + itemReplay.memberAvatar + ');' + bgStyle">
									</view>
									<view class="com_detail" :data-commentId="item.commentId" :data-replyId="itemReplay.replyId"
										:data-memberNickname="itemReplay.fromAuthorName" @tap="replyComment">
										<text class="com_name">{{ itemReplay.fromAuthorName }}</text>
										<text class="com_con">{{ $L('回复') }}<text
												class="replay_name">{{ itemReplay.toAuthorName != undefined ? itemReplay.toAuthorName : "" }}：</text>{{ itemReplay.content }}
										</text>
										<view class="com_other">
											<text class="com_time">{{ itemReplay.createTime }}</text>
											<text v-if="itemReplay.isSelf == 1" class="del_com" data-type="reply" @tap.stop="delCom"
												:data-commentId="item.commentId" :data-replyId="itemReplay.replyId">{{ $L('删除') }}</text>
										</view>
									</view>
								</view>
								<view class="com_item_r">
									<view class="" :data-commentid="item.commentId" :data-replyid="itemReplay.replyId" @tap="likeReply">
										<svgGroup type="dianzan" width="40" height="40" px="rpx" v-if="itemReplay.isLike"
											:color="diyStyle_var['--color_video_main']"></svgGroup>
										<svgGroup type="dianzan" width="40" height="40" px="rpx" v-else color="#dedede"></svgGroup>
									</view>
									<text>{{ itemReplay.likeNum }}</text>
								</view>
							</view>
						</view>
					</block>
					<!-- 查看更多回复 -->
					<view v-if="item.replyList.length > item.limit && item.isFoldReply" class="reply_pagnation"
						@tap="getMoreReply(item)" :data-commentId="item.commentId" :data-rpn="item.rpn">
						<view class="left_line"></view>
						<text class="more_reply">{{ $L('查看更多回复') }}</text>
						<view class="right_line"></view>
					</view>
					<!-- 收起更多回复 -->
					<view v-if="item.replyList.length == item.limit && !item.isFoldReply" class="reply_pagnation"
						@tap="closeMoreReply(item)" :data-commentId="item.commentId">
						<view class="left_line"></view>
						<text class="more_reply">{{ $L('收起更多回复') }}</text>
						<view class="right_line"></view>
					</view>
				</view>
			</block>
			<!-- 数据加载完毕 -->
			<dataLoaded :showFlag="!hasmore && commentList.length > 0" />

			<!-- 数据加载中 -->
			<dataLoading :showFlag="hasmore && loading" />



			<!-- 页面loading -->
			<view class="page_loading_child" v-if="firstLoading">
				<image :src="imgUrl + 'svideo/page_loading_icon.gif'"></image>
			</view>
		</scroll-view>

		<view class="comment_empty" v-if="!firstLoading && !commentList.length">
			<!-- 页面空数据 -->
			<emptyData :emptyIcon="imgUrl + 'svideo/live_list_empty_icon.png'" :showFlag="!firstLoading && !commentList.length" />
		</view>

		<view class="replay">
			<view class="input_wrap">
				<text v-if="replyInfo != ''" class="reply_tip">{{ $L('回复') }}@<text
						class="reply_name">{{ replyInfo.memberNickname ? replyInfo.memberNickname : replyInfo.memberName }}</text></text>
				<image v-if="replyInfo == ''" :src="imgUrl + 'svideo/edit.png'"></image>
				<input type="text" cursor-spacing="30" :focus="showFocus" name="reply_con" class="reply_inp"
					:placeholder="$L('赶快发表你的评论吧~')" placeholder-style="font-size:24rpx;color:#949494" confirm-type="send"
					@confirm="sendReplyComment" maxlength="100" v-model="input_val" :adjust-position="true"
					@blur="cancelBlur"></input>
			</view>
		</view>
	</view>
	<!-- 评论模块 end -->
</template>

<script>
import {
	checkPageHasMore
} from "@/utils/live";
import {
	mapState
} from 'vuex'
import dataLoading from "../../component/dataLoading.vue";
import dataLoaded from "../../component/dataLoaded.vue";
import emptyData from "../../component/emptyData.vue";
let cur_time = 0;
export default {
	props: ['videoDetail', 'videoId'],
	data () {
		return {
			commentList: [],
			pn: 1,
			hasmore: true,
			loading: false,
			firstLoading: true,
			imgUrl: process.env.VUE_APP_IMG_URL,
			replyInfo: '',
			showFocus: false,
			input_val: '',
			bgStyle: 'background-size:contain;background-position:center center;background-repeat: no-repeat;',
			commentSum: 0
		}
	},
	components: {
		dataLoading,
		dataLoaded,
		emptyData
	},
	computed: {
		...mapState(['hasLogin'])
	},
	watch: {
		commentSum (val) {
			this.$emit('updateCommentNum', val);	//更新当前图文详情条数
			uni.$emit('updateComment', val); //更新图文列表首页条数
		}
	},
	updated () {
		this.commentSum = this.videoDetail.commentNum
	},
	methods: {
		//获取评论列表
		getCommentList () {
			let param = {}
			param.data = {}
			param.url = 'v3/video/front/video/comment/list'
			param.method = 'GET'
			param.data.pageSize = 10
			param.data.current = this.pn
			param.data.videoId = this.videoId
			this.$request(param).then(res => {
				if (res.state == 200) {
					let list = res.data.list;
					list.forEach(el => el.rpn = 1);

					if (this.pn == 1) {
						this.commentList = list;
					} else {
						this.commentList = this.commentList.concat(list);
					}

					if (checkPageHasMore(res.data.pagination)) {
						this.pn++;
					} else {
						this.hasmore = false;
					} //初次加载的话更改状
					this.commentList.map((item) => {
						this.$set(item, 'limit', 3)
						this.$set(item, 'isFoldReply', true)
					})
					if (this.firstLoading) {
						this.firstLoading = false;
					}

					this.loading = false
				}
			})
		},
		// 查看更多回复
		getMoreReply (reply) {
			reply.limit = reply.replyList.length
			reply.isFoldReply = false
		},

		cancelBlur () {
			this.replyInfo = ''
		},

		// 收起更多回复
		closeMoreReply (reply) {
			reply.limit = 3
			reply.isFoldReply = true
		},
		//评论功能
		sendReplyComment (e) {
			if (!this.input_val.trim()) {
				return
			}
			if (Date.parse(new Date()) - cur_time < 10) {
				return false;
			} else {
				cur_time = Date.parse(new Date());
			}
			if (!this.hasLogin) {
				getApp().globalData.goLogin(this.$Route);
				return;
			}
			let that = this
			let {
				replyInfo,
				videoId,
				commentList,
				videoDetail
			} = this;
			if (replyInfo == '') {
				//发布评论
				let param = {}
				param.data = {}
				param.url = 'v3/video/front/video/comment/publish'
				param.method = 'POST'
				param.data.content = e.detail.value
				param.data.videoId = videoId
				param.data.isXcx = false
				// #ifdef MP-WEIXIN
				param.data.isXcx = true
				// #endif
				that.$request(param).then(res => {
					if (res.state == 200) {
						let that = this

						res.data.replyList = []
						res.data.limit = 3
						commentList.unshift(res.data);
						//清空输入内容
						setTimeout(() => {
							this.commentSum = Number(this.commentSum) + 1;

							this.commentList = JSON.parse(JSON.stringify(commentList))
							this.input_val = ''
							this.showFocus = false
						}, 0)
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						});
					}
				})
			} else {
				//回复评论
				let param = {}
				param.data = {}
				param.url = 'v3/video/front/video/comment/reply'
				param.method = 'POST'
				param.data.content = e.detail.value
				param.data.commentId = replyInfo.commentId
				param.data.parentReplyId = replyInfo.replyId ? replyInfo.replyId : 0

				that.$request(param).then(res => {
					if (res.state == 200) {

						let index = this.commentList.findIndex(el => el.commentId == replyInfo.commentId);
						let spliceIndex = this.commentList[index].replyList.length == 0 ? 0 : this.commentList[index].replyList.length - 1
						this.commentList[index].replyList.splice(spliceIndex, 0, res.data)

						if (this.commentList[index].replyList.length > 3) {
							this.commentList[index].isFoldReply = false
							this.commentList[index].limit = commentList[index].replyList.length
						}
						this.input_val = ''
						this.showFocus = false
						this.replyInfo = ''
						this.$forceUpdate()


					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						});
					}
				})
			}
		},
		likeReply (e) {
			let commentId = e.currentTarget.dataset.commentid;
			let replyId = e.currentTarget.dataset.replyid;
			let key = uni.getStorageSync('token');
			let {
				commentList
			} = this;
			let param = {}
			param.data = {}
			param.url = 'v3/video/front/video/comment/replyClickPraise'
			param.method = 'POST'
			param.data.replyId = replyId
			if (this.hasLogin) {
				this.$request(param).then(res => {
					if (res.state == 200) {
						let index = commentList.findIndex(el => el.commentId == commentId);
						let r_index = commentList[index].replyList.findIndex(el => el.replyId == replyId);
						let isLike = commentList[index].replyList[r_index].isLike;
						commentList[index].replyList[r_index].isLike = isLike == true ? false : true;
						commentList[index].replyList[r_index].likeNum = res.data;
						this.commentList = commentList
					} else {
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						});
					}
				})
			} else {
				getApp().globalData.goLogin(this.$Route);
			}
		},
		//回复评论
		replyComment (e) {
			let tmp = e.currentTarget.dataset;
			let {
				replyInfo
			} = this;
			if (this.hasLogin) {
				this.replyInfo = {
					commentId: tmp.commentid,
					replyId: tmp.replyid,
					memberNickname: tmp.membernickname
				},
					this.showFocus = true
			} else {
				uni.showToast({
					title: this.$L('需要登录才能回复哦'),
					icon: 'none'
				});
			}
		},
		// 点赞评论
		likeComment (e) {
			let commentId = e.currentTarget.dataset.commentid;
			let {
				commentList
			} = this;
			let param = {}
			param.data = {}
			param.url = 'v3/video/front/video/comment/clickPraise'
			param.method = 'POST'
			param.data.commentId = commentId
			if (this.hasLogin) {
				this.$request(param).then(res => {
					if (res.state == 200) {
						let index = commentList.findIndex(el => el.commentId == commentId);
						let isLike = commentList[index].isLike;
						commentList[index].isLike = isLike == true ? false : true;
						commentList[index].likeNum = res.data;
						this.commentList = commentList
					} else {
						uni.showToast({
							title: res.data.msg,
							icon: 'none'
						});
					}
				})
			} else {
				getApp().globalData.goLogin(this.$Route);
			}
		},
		// 删除评论
		delComment (commentId) {
			let {
				videoId,
				videoDetail,
				commentList
			} = this;
			let param = {}
			param.data = {}
			param.url = 'v3/video/front/video/comment/delete'
			param.method = 'POST'
			param.data.commentId = commentId
			this.$request(param).then(res => {
				uni.showToast({
					title: res.msg,
					icon: 'none'
				});

				if (res.state == 200) {
					// videoDetail.commentNum = --videoDetail.commentNum;
					let index = commentList.findIndex(el => el.commentId == commentId);
					commentList.splice(index, 1);
					this.videoDetail = videoDetail
					this.commentList = commentList
					this.commentSum = --this.commentSum
				}
			})
		},
		// 删除回复
		delApply (replyId, commentId) {
			let {
				videoId,
				videoDetail,
				commentList
			} = this;
			let param = {}
			param.data = {}
			param.url = 'v3/video/front/video/comment/delReply'
			param.method = 'POST'
			param.data.replyId = replyId
			this.$request(param).then(res => {
				uni.showToast({
					title: res.msg,
					icon: 'none'
				});
				if (res.state == 200) {
					let index = commentList.findIndex(el => el.commentId == commentId);
					let r_index = commentList[index].replyList.findIndex(el => el.replyId == replyId);
					commentList[index].replyList.splice(r_index, 1);
					this.commentList = commentList

				}
			})
		},
		//评论模块删除事件
		delCom (e) {
			e.stopPropagation()
			let tmp_data = e.currentTarget.dataset;
			uni.showModal({
				title: '',
				content: this.$L(`确认删除该评论？`),
				cancelText: this.$L('取消'),
				confirmText: this.$L('确定'),
				success: res => {
					if (res.confirm) {
						if (tmp_data.type == 'comment') {
							this.delComment(tmp_data.commentid);
						} else {
							this.delApply(tmp_data.replyid, tmp_data.commentid);
						}
					}
				}
			});
		},

		closeComment () {
			this.$emit('close')
		},
		//评论列表滑到底部加载数据
		getMoreCom () {
			if (this.hasmore) {
				this.getCommentList();
			}
		},
	}
}
</script>

<style lang="scss">
.video_comment {
	width: 750rpx;
	/* height: 90vw; */
	background: #fff;
	border-radius: 15rpx 15rpx 0 0;
	position: relative;

	.title {
		display: flex;
		padding: 30rpx;
		flex-direction: row;
		justify-content: space-between;
		align-content: center;
		height: 102rpx;
		border-bottom: 1px solid #f5f5f5;

		.com_t_l {
			display: flex;
			flex: 1;
			flex-direction: row;
			justify-content: flex-start;
			align-items: center;

			.com_t_l_title {
				color: #2d2d2d;
				font-size: 32rpx;
				margin-right: 18rpx;
			}

			.com_t_l_total {
				color: #949494;
				font-size: 22rpx;
			}
		}

		.com_t_close {
			width: 48rpx;
			height: 48rpx;
		}
	}

	.comment_empty {
		position: absolute;
		z-index: -1;
		left: 0;
		right: 0;
		bottom: 0;
		top: 70rpx;
	}

	.comment {
		width: 100%;
		height: 750rpx;
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: flex-start;
		margin-top: 20rpx;
		/* position: relative; */
		padding-bottom: 100rpx;

		.com_item_wrap {
			display: flex;
			flex-direction: column;
			justify-content: flex-start;
			align-items: flex-start;
		}

		.com_item {
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			align-items: flex-start;
			width: calc(750rpx - 60rpx);
			margin-left: 30rpx;

			.com_item_l {
				display: flex;
				flex-direction: row;
				justify-content: flex-start;
				align-items: flex-start;
				width: 600rpx;

				.com_avator {
					width: 43rpx;
					height: 43rpx;
					border-radius: 50%;
					margin-right: 18rpx;
					margin-top: 10rpx;
					background-size: contain;
					background-position: center center;
					background-repeat: no-repeat;
				}

				.com_detail {
					display: flex;
					flex-direction: column;
					justify-content: flex-start;
					align-items: flex-start;
					flex: 1;

					.com_name {
						color: #2d2d2d;
						font-size: 28rpx;
						margin-bottom: 12rpx;
					}

					.com_con {
						color: #2d2d2d;
						font-size: 26rpx;
						line-height: 39rpx;
						margin-bottom: 7rpx;
						word-break: break-all;
					}

					.com_other {
						display: flex;
						flex-direction: row;
						justify-content: center;
						align-items: flex-start;
						margin-bottom: 12rpx;

						.com_time {
							color: #949494;
							font-size: 22rpx;
							margin-right: 20rpx;
						}

						.del_com {
							color: var(--color_video_main);
							font-size: 20rpx;
						}
					}
				}
			}

			.com_item_r {
				display: flex;
				flex-direction: column;
				justify-content: flex-start;
				align-items: center;

				image {
					width: 46rpx;
					height: 46rpx;
				}

				text {
					color: #949494;
					font-size: 20rpx;
					margin-top: -3rpx;
				}
			}
		}

		.child {
			padding-left: 60rpx;

			.com_item {
				width: calc(750rpx - 120rpx);

				.com_item_l {
					width: 550rpx;

					.com_detail {
						.com_name {
							font-size: 26rpx;
							margin-bottom: 5rpx;
						}

						.com_con {
							font-size: 24rpx;
						}

						.replay_name {
							color: #949494;
							font-size: 24rpx;
						}
					}
				}

				.com_avator {
					width: 30rpx;
					height: 30rpx;
					margin-top: 0;
					margin-right: 10rpx;
				}
			}
		}

	}

	.reply_pagnation {
		width: 550rpx;
		display: flex;
		flex-direction: row;
		justify-content: space-around;
		align-items: center;
		height: 40rpx;
		/* padding-left: 110rpx;
			padding-right: 90rpx; */
		margin: 0 auto;

		.left_line,
		.right_line {
			height: 1rpx;
			background: rgba(0, 0, 0, 0.1);
			width: 166rpx;
		}

		.more_reply {
			color: #2d2d2d;
			font-size: 22rpx;
		}
	}
	
	.page_loading_child {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 750rpx;
		height: 750rpx;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		background-color: transparent;
	}

	.page_loading_child image {
		width: 60rpx;
		height: 60rpx;
	}

	.replay {
		position: absolute;
		z-index: 4;
		height: 100rpx;
		width: 750rpx;
		padding: 0 30rpx;
		background: #fff;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		left: 0;
		right: 0;
		bottom: 0;
		box-shadow: 0px 0px 20px 0px rgba(86, 86, 86, 0.08);

		.input_wrap {
			background-color: #f5f5f5;
			border-radius: 12px;
			width: 690rpx;
			height: 65rpx;
			display: flex;
			flex-direction: row;
			justify-content: flex-start;
			align-items: center;

			.reply_inp {
				display: flex;
				flex: 1;
			}

			image {
				width: 47rpx;
				height: 47rpx;
				margin: 0 10rpx;
			}
		}

		.reply_tip {
			color: #2d2d2d;
			font-size: 24rpx;
			padding: 0 15rpx;
		}

		.reply_name {
			color: #4d8efb;
		}
	}
}

.video_comment .comment_empty {
	z-index: 2;
}
</style>