<template>
	<view :style="mix_diyStyle">
		<view v-if="isShowindex">
			<view class="fixed_top_status_bar"></view>
			<ts-deco :deco_info="deco_data" :width="width" :activity_open="activity_open" :height="height"
				:home_page_img="home_page_img" :is_show_top="true" :home_is_show_top_cat="home_is_show_top_cat"
				:is_from_found="false" type="Ts" ref="deco" @getShareItem="getShareItem"
				:isShowSwiper="isShowSwiper"></ts-deco>
			<DiyTabBar idx="0" v-if="(deco_data && deco_data.length > 0) || activity_open"></DiyTabBar>
		</view>
	</view>
</template>

<script>
	import {
mapMutations,
mapState
} from 'vuex'
import DiyTabBar from '../component/DiyTabBar.vue'
import TsDeco from '../component/ts_deco.vue'
	export default {
		data() {
			return {
				deco_data: [], //首页装修数据
				home_is_show_top_cat: true, //是否显示顶部分类，默认显示
				home_page_img: [],
				width: '',
				height: '',
				indexShareData: {},
				shareData: {},
				activity_open: false,
				isShowSwiper: true,
				isShowindex: false,
			}
		},
		components: {
			TsDeco,
			DiyTabBar
		},
		computed: {
			...mapState(['hasLogin', 'userInfo', 'userCenterData'])
		},
		onReachBottom() {
			this.$refs.deco.load()
		},
		onLoad() {
			if (!this.hasLogin) {
				let urls = this.$Route.path
				const query = this.$Route.query
				uni.setStorageSync('fromurl', {
					url: urls,
					query
				})
				this.$Router.push('/pages/public/login')
			}
			this.ifOpen()
		},
		/**
		 * 用户点击右上角分享
		 */
		onShareAppMessage: function() {
			const {
				shareData
			} = this

			if (shareData.type == 'index') {
				return shareData
			} else {
				//因为微信小程序去除了分享的回调，只能分享商品后将shareData赋值回首页分享
				let timer = setTimeout(() => {
					console.log('success')
					this.shareData = JSON.parse(JSON.stringify(this.indexShareData))
				}, 500)
				return shareData
			}
		},

		/**
		 * 用户点击右上角分享
		 */
		onShareTimeline: function() {
			const {
				shareData
			} = this

			if (shareData.type == 'index') {
				return shareData
			} else {
				return {
					...shareData,
					complete() {
						this.shareData = JSON.parse(JSON.stringify(this.indexShareData))
					}
				}
			}
		},

		methods: {
			...mapMutations(['login', 'setUserCenterData']),
			// 判断活动是否开启
			ifOpen() {
				let param = {}
				param.data = {}
				param.data.names = 'spreader_is_enable'
				param.url = 'v3/system/front/setting/getSettings'
				param.method = 'GET'
				this.$request(param).then((res) => {
					if (res.state == 200) {
						if (res.data[0] == '1') {
							this.activity_open = true
							this.loadData()
						} else {
							this.isShowindex = true
							this.activity_open = false
						}
					}
				})
			},
			async loadData() {
				uni.showLoading({
					title: this.$L('加载中...')
				})
				// #ifdef H5
				this.client = 'h5'
				// #endif

				//app-1-start
				// #ifdef APP-PLUS
				switch (uni.getSystemInfoSync().platform) {
					case 'android':
						this.client = 'android'
						break
					case 'ios':
						this.client = 'ios'
						break
					default:
						break
				}
				// #endif
				//app-1-end
				//wx-1-start
				// #ifdef MP
				this.client = 'weixinXcx'
				// #endif
				//wx-1-end
				let param = {
					url: 'v3/system/front/deco/index?os=' + this.client,
					method: 'GET',
					data: {
						type: 'spreader'
					}
				}
				this.$request(param).then((res) => {
					this.isShowindex = true
					if (res.state == 200) {
						if (JSON.stringify(res.data) == '{}') {
							this.deco_data = null
							uni.hideLoading()
							return
						}
						if (res.data.data != '') {
							this.deco_data = JSON.parse(res.data.data)
						} else {
							this.deco_data = null
						}
						//wx-2-start
						// #ifdef MP
						this.indexShareData = {
							title: res.data.siteName,
							path: '/extra/tshou/index/index',
							imageUrl: res.data.xcxImage,
							type: 'index'
						}
						this.shareData = JSON.parse(JSON.stringify(this.indexShareData))
						// #endif
						//wx-2-end
						if (res.data.showTip != null) {
							this.home_page_img = JSON.parse(res.data.showTip)
							const {
								windowWidth,
								windowHeight
							} = uni.getSystemInfoSync()
							this.width = this.home_page_img[0].width || windowWidth * 0.75 * 1.8
							this.height =
								this.home_page_img[0].height || windowHeight * 0.56 * 1.8
						} else {
							this.home_page_img = []
						}


						if (
							this.deco_data &&
							this.deco_data.length != undefined &&
							this.deco_data.length > 0
						) {
							this.home_is_show_top_cat =
								this.deco_data[0].type == 'top_cat_nav' ? true : false
							this.isShowSwiper =
								this.deco_data[0].type == 'top_cat_nav' &&
								this.deco_data[0].data.length ?
								true :
								false
						}
						uni.hideLoading()
					}
				})
			},
			//阻止模态框下页面滚动
			moveHandle() {},
			getShareItem(data) {
				this.shareData = data
			}
		}
	}
	//;;
</script>

<style lang="scss">
	.fixed_top_status_bar {
		position: fixed;
		//app-2-start
		/* #ifdef APP-PLUS */
		height: var(--status-bar-height);
		/* #endif */
		//app-2-end
		/* #ifndef APP-PLUS */
		height: 0;
		/* #endif */
		top: 0;
		left: 0;
		right: 0;
		z-index: 999;
		background: #fff;
	}

	page {
		background: #f5f5f5;
	}
</style>