<template>
	<view :style="mix_diyStyle">
	<view class="main_vip">
		<topHeader :title="$L('推手等级')"></topHeader>
		<view class="vip_con">
			<view class="vip_m">
				<image :src="curVipImg" mode="aspectFit"></image>
			</view>
			<view class="vip_l">
				<!-- #ifndef MP-ALIPAY -->
				<swiper class="swiper" previous-margin="200rpx" next-margin="200rpx" :current="swiperCur">
					<swiper-item class="swiper-item" v-for="(item, index) in vipInfo" :key="index">
						<view class="vip_l_item" :class="{ curvipT: index == swiperCur }">
							<text class="vip_n">{{ item.gradeName }}{{ $L('推手') }}</text>
							<view class="vip_l_pro">
								<view class="reach">
									<view class="reach_bar"></view>
									<image :src="imgUrl + 'tshou/tsv_chosen.png'" mode="aspectFit"
										v-if="index + 1 <= curvip"></image>
									<image :src="imgUrl + 'tshou/tsv_lock.png'" mode="aspectFit" v-else>
									</image>
									<view class="reach_bar"></view>
								</view>
								<text class="vip_s">{{ index== swiperCur ? $L('当前邀请') : $L('邀请')}}{{ index==
								swiperCur ? curNum : item.subSpreaderNum}}{{ $L('人') }}</text>
							</view>
						</view>
					</swiper-item>
					<!-- #endif -->
					<!-- #ifdef MP-ALIPAY -->
					<swiper class="swiper" :current="swiperCur" @change="aliChange">
						<swiper-item class="swiper-item" v-for="(item, index) in vipInfo" :key="index">
							<view class="vip_l_item" :class="{ curvipT: index == swiperCur }">
								<text class="vip_n">{{ item.gradeName }}{{ $L('推手') }}</text>
								<view class="vip_l_pro">
									<view class="reach">
										<view class="reach_bar"></view>
										<image :src="imgUrl + 'tshou/tsv_chosen.png'" mode="aspectFit"
											v-if="index + 1 <= curvip"></image>
										<image :src="imgUrl + 'tshou/tsv_lock.png'" mode="aspectFit" v-else>
										</image>
										<view class="reach_bar"></view>
									</view>
									<text class="vip_s">{{ index== swiperCur ? $L('当前邀请') : $L('邀请')}}{{
									index== swiperCur ? curNum : item.subSpreaderNum}}{{ $L('人') }}</text>
								</view>
							</view>
						</swiper-item>
						<!-- #endif -->




					</swiper>
			</view>
			<view class="vip_d">
				<view class="vip_a">
					<view class="vip_a_top">
						<image :src="imgUrl + 'tshou/vip_a_left.png'" mode="aspectFit"></image>
						<text>{{ $L('等级权益') }}</text>
						<image :src="imgUrl + 'tshou/vip_a_right.png'" mode="aspectFit"></image>
					</view>
					<view class="vip_a_bottom">
						<view class="vip_a_bottom_item">
							<view class="flex_item a">
								<text>{{ $L('会员等级') }}</text>
							</view>
							<view class="flex_item b">
								<text>{{ $L('邀请人数') }}</text>
							</view>
							<view class="flex_item">
								<text>{{ $L('等级权益') }}</text>
							</view>
						</view>
						<view class="vip_a_bottom_item" v-for="(item, index) in vipInfo" :key="index">
							<view class="flex_item a">
								<image :src="item.imageValue" mode="aspectFit" v-if="item.imageValue"></image>
								<text>{{ item.gradeName }}{{ $L('推手') }}</text>
							</view>
							<view class="flex_item b">
								<text>{{ item.subSpreaderNum }}</text>
							</view>
							<view class="flex_item c">
								<text>{{ $L('邀请用户佣金比例') }}{{ item.gradeBenefits }}%</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
	</view>
</template>

<script>
import topHeader from '../component/topheader.vue'
import { mapState } from 'vuex'
export default {
	components: {
		topHeader
	},
	data() {
		return {
			imgUrl: process.env.VUE_APP_IMG_URL,
			vipImglist: {
				1: 'tshou/tsv_brown.png',
				2: 'tshou/tsv_silver.png',
				3: 'tshou/tsv_gold.png',
				4: 'tshou/tsv_gold.png',
				5: 'tshou/tsv_gold.png',
				6: 'tshou/tsv_gold.png',
				7: 'tshou/tsv_gold.png',
				8: 'tshou/tsv_gold.png',
				9: 'tshou/tsv_gold.png',
				10: 'tshou/tsv_gold.png',
			},
			vipInfo: [],
			swiperCur: 0,
			curvip: 0,
			curVipImg: '',
			curNum: 0,
			// #ifdef MP-ALIPAY
			aliIndex: 0
			// #endif
		}
	},
	onLoad(op) {
		if (!this.hasLogin) {
			let urls = this.$Route.path;
			const query = this.$Route.query;
			uni.setStorageSync('fromurl', {
				url: urls,
				query
			});
			this.$Router.push('/pages/public/login')
		}
		this.curvip = this.$Route.query.curvip
		this.curNum = this.$Route.query.curNum
		this.getVip()

	},
	computed: {
		...mapState(['hasLogin'])
	},
	methods: {
		goBack() {
			this.$Router.back(1)
		},
		getVip() {
			this.$request({
				url: 'v3/spreader/front/spreader/gradeList',
				data: {
					current: 1
				}
			}).then(res => {
				if (res.state == 200) {
					this.vipInfo = res.data.list.sort((a, b) => a.gradeId - b.gradeId)
					this.swiperCur = this.vipInfo.findIndex(i => i.gradeId == this.curvip)
					this.curVipImg = this.vipInfo[this.swiperCur].imageValue
				}
			})
		},

		// #ifdef MP-ALIPAY
		aliChange(e) {
			this.aliIndex = e.detail.current
		}
		// #endif
	}
}
</script>

<style lang="scss">
@mixin flex($justify, $align, $direction) {
	display: flex;
	justify-content: $justify;
	align-items: $align;
	flex-direction: $direction;
}

@mixin image($width, $height, $radius) {
	width: $width;
	height: $height;
	border-radius: $radius;
}

@mixin text($size, $color) {
	font-size: $size;
	color: $color;
}

.flex_item {
	@include flex(center, center, row);

	&.a {
		width: 200rpx;
	}

	&.b {
		width: 170rpx;
	}

	&.c {}
}

swiper {
	width: 100%;
	height: 200rpx;
	position: relative;
}

.swiper-item {
	display: inline-block;
	height: 200rpx;


}

.main_vip {
	background: linear-gradient(315deg, #CBAF7A 0%, #EBCB90 71%, #F2D294 100%, #F8DEAC 100%);
	background-position: center center;
	background-size: 100% 100%;
	height: 100vh;
	padding-top: 44rpx;
	/* #ifdef APP-PLUS||MP-WEIXIN||MP-TOUTIAO||MP-BAIDU */
	padding-top: calc(var(--status-bar-height) + 44rpx);

	/* #endif */
	.vip_con {
		width: 100%;
		margin-top: 22rpx;

		.vip_m {
			image {
				display: block;
				@include image(80rpx, 96rpx, none);
				margin: 0 auto;
			}
		}

		.curvipT {
			.vip_n {
				font-size: 40rpx !important;
				color: #845A3A !important;
			}

			.vip_s {
				font-size: 28rpx !important;
				color: #846B55 !important;
			}
		}

		.vip_l {
			@include flex(flex-start, center, row);
			margin-top: 30rpx;
			position: relative;

			.vip_l_con {
				display: flex;
				position: relative;
			}

			.vip_l_item {
				@include flex(space-between, center, column);
				height: 170rpx;
				width: 100%;

				/* #ifdef MP-ALIPAY */
				// position: absolute;
				width: auto;
				// left: 0;
				// top: 0;
				/* #endif */


				.vip_n {
					@include text(28rpx, #845A3A)
				}

				.vip_s {
					@include text(28rpx, #322F2D)
				}

				/* #ifndef MP-ALIPAY */
				&:last-child {
					margin-right: calc((100vw/2) - 180rpx);
				}

				/* #endif */

				.vip_l_pro {
					@include flex(center, center, column);

					image {
						@include image(44rpx, 44rpx, none)
					}
				}
			}
		}

		.vip_d {
			margin-top: 42rpx;
			padding: 0 20rpx;

			.vip_a {
				padding-top: 40rpx;
				background-color: #fff;
				border-radius: 12rpx;
				height: 800rpx;
				overflow-y: auto;

				.vip_a_top {
					@include flex(center, center, row);

					image {
						@include image(88rpx, 18rpx, none)
					}

					text {
						font-size: 28rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #322F2D;
						margin: 0 20rpx;
					}
				}

				.vip_a_bottom {
					margin-top: 40rpx;

					image {
						@include image(36rpx, 42rpx, none);
						margin-right: 14rpx;
					}

					.vip_a_bottom_item {
						@include flex(flex-start, center, row);
						border-bottom: 2rpx solid #f5f5f5;
						padding: 30rpx 30rpx;

						text {
							@include text(26rpx, #999999)
						}

						&:first-child {
							border-top: 2rpx solid #f5f5f5;

							text {
								@include text(28rpx, #322F2D)
							}

						}
					}
				}
			}
		}
	}
}

.reach {
	@include flex(center, center, row);
	margin-bottom: 14rpx;

	.reach_bar {
		width: 156rpx;
		height: 6rpx;
		background: #CF9B46;

		&.long {
			width: 156rpx;
		}
	}
}
</style>
