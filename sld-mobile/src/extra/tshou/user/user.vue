<template>
	<view :style="mix_diyStyle">
		<view class="main_user_bg_box">
			<view class="main_user_bg" :style="'background-image:url('+imgUrl+'tshou/census_bg.png)'">

			</view>
		</view>
		<view class="main_user" >
			<view class="u_top_arr">
				<!-- <image :src="imgUrl+'tshou/back_icon.png'" mode="aspectFit"></image> -->
			</view>
			<view class="u_mid">
				<view class="avatar">
					<image :src="userInfo.memberAvatar" mode="aspectFit"></image>
				</view>
				<view class="u_info_text">
					<view class="u_info_text_top">
						<text>{{userInfo.memberNickName? userInfo.memberNickName: userInfo.memberName}}</text>
						<view class="vip_baner" @click="navTo(`/extra/tshou/user/vip?curvip=${userInfo.gradeId}&curNum=${userInfo.inviteNum}`)" v-if="userInfo.isSpreader">
							<image :src="imgUrl + '' + gradeInfo[userInfo.gradeId]" mode="aspectFit"></image>
							<text>{{ userInfo.gradeName }}{{ $L('推手') }}</text>
							<image :src="imgUrl + 'tshou/tiny_right.png'" mode="aspectFit"></image>
						</view>
					</view>
					<text>{{ userInfo.memberName }}</text>
				</view>
			</view>
			<view class="u_mid_b">
				<block v-if="!userInfo.isSpreader && toBeTs == 3">
					<view class="ts_apply pad19">
						<view class="ts_apply_left">
							<image :src="imgUrl + 'tshou/tsu_glod.png'" mode="aspectFit"></image>
							<view class="ts_apply_text">
								<text>{{ $L('立即申请') }}</text>
								<text>{{ $L('成为平台推手') }}</text>
							</view>
						</view>
						<view class="ts_apply_right">
							<view class="ts_apply_button" @click="toTs(userInfo.type)">{{butTxt}}</view>
						</view>
					</view>
				</block>

				<block v-if="!userInfo.isSpreader && toBeTs == 2">
					<view class="ts_achive_con pad15">
						<view class="ts_achive_left">
							<image :src="imgUrl + 'tshou/tsu_ok.png'" mode="aspectFit"></image>
							<view class="ts_achive_text">
								<text>{{ $L('达到消费金额成为平台推手') }}</text>
								<view class="progress">
									<view class="progress_bar" :style="'width:' +(Number(userInfo.buyAmount) / Number(userInfo.minAmount)).toFixed(2) * 100 +'%'"></view>
								</view>
							</view>
						</view>
						<view class="ts_achive_right">
							<text>{{ userInfo.buyAmount }}</text>
							<text>/{{ userInfo.minAmount }}</text>
						</view>
					</view>
				</block>

				<block v-if="!userInfo.isSpreader && toBeTs == 1">
					<view class="ts_achive_con pad15">
						<view class="ts_achive_left">
							<image :src="imgUrl + 'tshou/tsu_ok.png'" mode="aspectFit"></image>
							<view class="ts_achive_text">
								<text>{{ $L('达到消费次数成为平台推手') }}</text>
								<view class="progress">
									<view class="progress_bar" :style="'width:' +(userInfo.buyNum / userInfo.minNum).toFixed(2) * 100 +'%'"></view>
								</view>
							</view>
						</view>
						<view class="ts_achive_right">
							<text>{{ userInfo.buyNum }}</text>
							<text>/{{ userInfo.minNum }}</text>
						</view>
					</view>
				</block>

				<block v-if="userInfo.isSpreader">
					<view class="to_ts pad19">
						<view class="to_ts_con">
							<text>{{ $L('¥') }}{{ userInfo.income }}</text>
							<text>{{ $L('累计收入') }}</text>
						</view>
						<view class="divide"></view>
						<view class="to_ts_con">
							<text>{{ $L('¥') }}{{ userInfo.expectIncome }}</text>
							<text>{{ $L('预计收入') }}</text>
						</view>
						<view class="divide"></view>
						<view class="to_ts_con">
							<text>{{ userInfo.spreaderOrderNum }}</text>
							<text>{{ $L('订单数') }}</text>
						</view>
					</view>
				</block>
			</view>
			<view class="list_cell_c">
				<!-- <view class="list_cell_con" @click="navTo('/extra/tshou/user/promote')" v-if="userInfo.isSpreader">
					<view class="list_cell">
						<view class="cell_tit">
							<image :src="imgUrl + 'tshou/auto_promote.png'" mode="aspectFit"></image>
							<text>{{ $L('我要推广') }}</text>
						</view>
						<view class="cell_more">
							<image class="img" :src="imgUrl + 'shop/to_right.png'" mode="aspectFit"></image>
						</view>
					</view>
				</view> -->

				<view class="list_cell_con" @click="navTo(item.url, index)" v-for="(item, index) in list" :key="index">
					<view class="list_cell">
						<view class="cell_tit">
							<image :src="imgUrl + '' + item.img" mode="aspectFit"></image>
							<text>{{ item.title }}</text>
						</view>
						<view class="cell_more">
							<image class="img" :src="imgUrl + 'shop/to_right.png'" mode="aspectFit"></image>
						</view>
					</view>
				</view>
			</view>
			<uni-popup ref="popup" type="dialog">
				<uni-popup-dialog type="input" :confirmText="$L('重新申请')" :content="userInfo.failReason" :duration="2000"
					@confirm="acDialog"></uni-popup-dialog>
			</uni-popup>

			<DiyTabBar idx="3"></DiyTabBar>
		</view>
	</view>
</template>

<script>
	import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue'
import uniPopup from '@/components/uni-popup/uni-popup.vue'
import {
mapState
} from 'vuex'
import DiyTabBar from '../component/DiyTabBar.vue'
	export default {
		components: {
			uniPopup,
			uniPopupDialog,
			DiyTabBar
		},
		data() {
			return {
				imgUrl: process.env.VUE_APP_IMG_URL,
				list: [{
						img: 'tshou/tsu_order.png',
						title: this.$L('推手订单'),
						url: '/extra/tshou/user/order'
					},
					{
						img: 'tshou/tsu_vip.png',
						title: this.$L('我的下级'),
						url: '/extra/tshou/user/mySub'
					},
					// {
					// 	img: 'tshou/tsu_com.png',
					// 	title: this.$L('常见问题'),
					// 	url: '/extra/tshou/comProblem/comCate'
					// },
					// {
					// 	img: 'tshou/tsu_kefu.png',
					// 	title: this.$L('联系客服'),
					// 	url: ''
					// }
				],
				userInfo: {},
				gradeInfo: {
					1: 'tshou/tsv_brown.png',
					2: 'tshou/tsv_silver.png',
					3: 'tshou/tsv_gold.png',
					4: 'tshou/tsv_gold.png',
					5: 'tshou/tsv_gold.png',
					6: 'tshou/tsv_gold.png',
					7: 'tshou/tsv_gold.png',
					8: 'tshou/tsv_gold.png',
					9: 'tshou/tsv_gold.png',
					10: 'tshou/tsv_gold.png'
				},
				toBeTs: 0,
				butTxt: this.$L('立即申请'),
				disableBut: false
			}
		},
		onLoad() {
			if (!this.hasLogin) {
				let urls = this.$Route.path
				const query = this.$Route.query
				uni.setStorageSync('fromurl', {
					url: urls,
					query
				})
				this.$Router.push('/pages/public/login')
			}
			this.getInfo()
		},
		computed: {
			...mapState(['hasLogin'])
		},
		methods: {
			navTo(url, ex) {
				if ((ex == 0 || ex == 1) && !this.userInfo.isSpreader) {
					uni.showToast({
						title: this.$L('无权查看'),
						icon: 'none'
					})
					return false
				}

				if (ex == 3) {
					if (this.userInfo.servicePhone) {
						uni.makePhoneCall({
							phoneNumber: this.userInfo.servicePhone
						})
					} else {
						this.$api.msg(this.$L('后台暂无客服电话'))
					}
				} else {
					this.$Router.push(url)
				}
			},
			getInfo() {
				this.$request({
					url: 'v3/spreader/front/spreader/getInfo'
				}).then((res) => {
					if (res.state == 200) {
						this.userInfo = res.data
						if (this.userInfo.type) {
							if (this.userInfo.type == 1) {
								this.toBeTs = 3
							} else if (this.userInfo.type == 2) {
								this.toBeTs =
									this.userInfo.buyAmount >= this.userInfo.minAmount ? 3 : 2
							} else if (this.userInfo.type == 3) {
								this.toBeTs = this.userInfo.minNum <= this.userInfo.buyNum ? 3 : 1
							} else if (this.userInfo.type == 4) {
								this.toBeTs = 3
								this.butTxt = this.$L('购买礼包')
							}
						}

						if (this.userInfo.state == 1) {
							this.toBeTs = 3;
							(this.butTxt = this.userInfo.stateValue), (this.disableBut = true)
						} else if (this.userInfo.state == 3) {
							this.toBeTs = 3;
							(this.butTxt = this.userInfo.stateValue), (this.disableBut = true)
							this.$refs.popup.open()
						}
					}
				})
			},

			toTs(type) {
				if (this.disableBut) {
					return
				}

				if (type && type == 4) {
					this.navTo('/extra/tshou/tsGift/tsGift')
				} else {
					this.$request({
						url: 'v3/spreader/front/spreader/addSpreader',
						method: 'POST'
					}).then((res) => {
						if (res.state == 200) {
							this.$api.msg(res.msg)
							this.getInfo()
						}
					})
				}
			},
			acDialog() {
				this.disableBut = false
				this.butTxt = this.$L('立即申请')
			}
		}
	}
</script>

<style lang="scss">
	@mixin flex($justify, $align, $direction) {
		display: flex;
		justify-content: $justify;
		align-items: $align;
		flex-direction: $direction;
	}

	@mixin image($width, $height, $radius) {
		width: $width;
		height: $height;
		border-radius: $radius;
	}

	@mixin text($size, $color) {
		font-size: $size;
		color: $color;
	}

	page {
		background: #f5f5f5;
	}

	.main_user_bg_box {
		background: var(--color_extral_main_bgZero);

		.main_user_bg {
			background-position: 0 0;
			background-repeat: no-repeat;
			background-size: contain;
			height: 363rpx;
			width: 750rpx;
		}
	}

	.main_user {
		position: fixed;
		top: 0;
		z-index: 99;
		margin-top: -2rpx;
		width: 750rpx;
		background-position: 0 0;
		background-repeat: no-repeat;
		background-size: contain;
		padding-top: 52rpx;
		/* #ifdef APP-PLUS||MP-WEIXIN||MP-BAIDU||MP-TOUTIAO */
		padding-top: calc(var(--status-bar-height) + 44rpx);
		/* #endif */
		padding-bottom: 100rpx;
		padding-left: 20rpx;
		padding-right: 20rpx;

		.u_top_arr {
			height: 40rpx;

			image {
				@include image(26rpx, 40rpx, none);
			}
		}

		.u_mid {
			margin-top: 30rpx;
			display: flex;
			padding-left: 10rpx;
			align-items: center;

			.avatar {
				width: 110rpx;
				height: 110rpx;
				background-color: #ffffff;
				border-radius: 50%;
				@include flex(center, center, row);

				image {
					@include image(100rpx, 100rpx, 50%);
				}
			}

			.u_info_text {
				height: 100rpx;
				margin-left: 54rpx;
				@include flex(space-between, unset, column);

				.u_info_text_top {
					@include flex(flex-start, center, row);

					text {
						display: inline-block;
						max-width: 350rpx;
						white-space: nowrap;
						text-overflow: ellipsis;
						overflow: hidden;
						@include text(34rpx, #fff);
						opacity: 0.9;
					}

					.vip_baner {
						border-radius: 0px 20rpx 20rpx 0px;
						background-color: #e9ca96;
						height: 30rpx;
						padding: 0 8rpx;
						margin-left: 20rpx;
						@include flex(flex-start, center, row);

						image {
							&:first-child {
								margin-right: 6rpx;
								@include image(24rpx, 24rpx, none);
							}

							&:last-child {
								margin-left: 6rpx;
								@include image(12rpx, 12rpx, none);
							}
						}

						&>text {
							font-size: 20rpx;
						}
					}
				}

				text {
					color: #fff;
					opacity: 0.9;
					@include text(24rpx, #fff);
				}
			}
		}

		.u_mid_b {
			border-radius: 12rpx;
			margin-top: 62rpx;
			min-height: 120rpx;

			.pad15 {
				padding: 20rpx 30rpx;
			}

			.pad19 {
				padding: 20rpx 0;
			}

			.ts_apply {
				border-radius: 12rpx;
				padding: 20rpx 38rpx;
				background: #fff;
				box-shadow: 0px 8rpx 22rpx -6rpx rgba(213, 213, 213, 0.09);

				@include flex(space-between, center, row);

				.ts_apply_left {
					display: flex;

					image {
						@include image(80rpx, 80rpx, none);
					}

					.ts_apply_text {
						@include text(24rpx, #666);
						line-height: 36rpx;
						@include flex(center, unset, column);
						border-left: 2rpx solid #666;
						margin-left: 36rpx;
						padding-left: 38rpx;
					}
				}

				.ts_apply_right {
					width: 164rpx;
					height: 58rpx;
					background: var(--color_extral_main);
					border-radius: 8rpx;
					text-align: center;
					line-height: 58rpx;
					@include text(28rpx, #fff);
				}
			}

			.ts_achive_con {
				height: 120rpx;
				background-color: #fff;
				border-radius: 12rpx;
				@include flex(space-between, center, row);

				.ts_achive_left {
					display: flex;

					image {
						@include image(52rpx, 52rpx, none);
					}

					.ts_achive_text {
						margin-left: 22rpx;

						text {
							@include text(28rpx, #333333);
						}

						.progress {
							margin-top: 12rpx;
							width: 348rpx;
							height: 10rpx;
							background: #efefef;
							border-radius: 6rpx;
							overflow: hidden;

							.progress_bar {
								width: 50%;
								height: 10rpx;
								background: var(--color_extral_main);
							}
						}
					}
				}

				.ts_achive_right {
					text {
						color: #5d5d5d;

						&:first-child {
							@include text(28rpx, #5d5d5d);
						}

						&:last-child {
							@include text(24rpx, #5d5d5d);
						}
					}
				}
			}

			.to_ts {
				@include flex(center, center, row);
				background-color: #fff;
				border-radius: 12rpx;

				.divide {
					width: 2rpx;
					height: 46rpx;
					background: #95692f;
					opacity: 0.6;
				}

				.to_ts_con {
					@include flex(space-between, center, column);
					flex: 1;

					text {
						&:first-child {
							@include text(36rpx, #333);
						}

						&:last-child {
							margin-top: 6rpx;
							@include text(22rpx, #333);
						}
					}
				}
			}
		}

		.list_cell_c {
			margin-top: 30rpx;

			.list_cell_con {
				padding: 0 32rpx;
				background-color: #fff;

				&:first-child {
					border-radius: 16rpx 16rpx 0 0;

					.list_cell {
						border-top: none;
					}
				}

				&:last-child {
					border-radius: 0 0 16rpx 16rpx;

					.list_cell {
						border-bottom: none;
					}
				}
			}

			.list_cell {
				display: flex;
				align-items: center;

				line-height: 100rpx;
				height: 100rpx;
				position: relative;
				background: #fff;
				border-bottom: 1rpx solid #f2f2f2;
				justify-content: center;

				.cell_more {
					color: $main-third-color;
					font-size: 18rpx;
					margin-left: 10rpx;
					margin-top: -5rpx;

					image {
						width: 15rpx;
						height: 25rpx;
						display: inline-block;
						margin-left: 20rpx;
					}
				}

				.cell_tit {
					flex: 1;
					font-size: 28rpx;
					margin-right: 10rpx;
					color: #333333;
					@include flex(flex-start, center, row);

					image {
						margin-right: 30rpx;
						@include image(42rpx, 42rpx, none);
					}
				}
			}
		}
	}
</style>