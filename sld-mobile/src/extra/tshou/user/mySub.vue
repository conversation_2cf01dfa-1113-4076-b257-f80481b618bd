<template>
	<view :style="mix_diyStyle">
  <view class="main_mysub">
    <scroll-view scroll-y="true" @scrolltolower="load" v-if="subList.length">
      <view class="sub_count">
        <text>{{ $L('当前拥有下级') }}：{{ total }}</text>
      </view>
      <block v-for="(item, index) in subList" :key="index">
        <subItem :is_but="item.inviteNum" :sub="item"></subItem>
      </block>
      <loadingState :state="loadState"></loadingState>
    </scroll-view>
    <tsEmpty :text="$L('暂无下级')" v-else></tsEmpty>
  </view>
	</view>
</template>

<script>
import subItem from '../component/subItem.vue'
import tsEmpty from '../component/tsEmpty.vue'
import loadingState from '@/components/loading-state.vue'
import { mapState } from 'vuex'
export default {
  components: {
    subItem,
    tsEmpty,
	loadingState
  },
  data() {
    return {
      subList: [],
      loading: true,
      loadState: '',
      hasmore: true,
      pn: 1,
      total: 0
    }
  },
  computed: {
    ...mapState(['userCenterData', 'hasLogin'])
  },
  onLoad() {
    setTimeout(()=>{
      uni.setNavigationBarTitle({
        title: this.$L('我的下级')
      })
    },0);
    
    if (!this.hasLogin) {
      let urls = this.$Route.path
      const query = this.$Route.query
      uni.setStorageSync('fromurl', {
        url: urls,
        query
      })
      this.$Router.push('/pages/public/login')
    }
    this.getSub()
  },
  methods: {
    load() {
      if (this.hasmore) {
        this.loadState = 'loading'
        this.getSub()
      }
    },
    getSub() {
      let param = {
        url: 'v3/spreader/front/spreaderInvite/list'
      }
      this.$request(param).then((res) => {
        if (res.state == 200) {
          this.total = res.data.pagination.total
          if (this.pn == 1) {
            this.subList = res.data.list
          } else {
            this.subList = this.subList.concat(res.data.list)
          }

          if (this.$checkPaginationHasMore(res.data.pagination)) {
            this.hasmore = true
            this.loadState = 'allow_loading_more'
          } else {
            this.hasmore = false
            this.loadState = 'no_more_data'
          }
        } else {
          this.$api.msg(res.msg)
        }
      })
    }
  }
}
</script>

<style lang="scss">
page {
  background: #f7f7f7;
}
.sub_count {
  text-align: right;
  padding: 22rpx 0;
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #262626;
}
.main_mysub {
  padding: 0 20rpx;
}
</style>
