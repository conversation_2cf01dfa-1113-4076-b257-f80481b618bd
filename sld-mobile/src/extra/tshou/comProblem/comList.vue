<!--协议规则 -->
<template>
	<view :style="mix_diyStyle">
  <view class="main_com">
    <scroll-view
      scroll-y="true"
      @scrolltolower="load"
      v-if="comList.length > 0"
    >
      <view
        class="list_cell_con"
        @click="
          navTo({
            path: `/extra/tshou/comProblem/comDetail`,
            query: { problemId: item.problemId }
          })
        "
        v-for="(item, index) in comList"
        :key="index"
      >
        <view class="list_cell">
          <text class="cell_tit">{{ item.problemContent }}</text>
          <text class="cell_more">
            <image
              class="img"
              :src="imgUrl + 'shop/to_right.png'"
              mode="aspectFit"
            ></image>
          </text>
        </view>
      </view>
    </scroll-view>
    <tsEmpty :text="$L('暂无数据')" v-else></tsEmpty>
  </view>
	</view>
</template>

<script>
import tsEmpty from '../component/tsEmpty.vue'
export default {
  components: {
    tsEmpty
  },
  data() {
    return {
      imgUrl: process.env.VUE_APP_IMG_URL,
      comList: [],
      pn: 1,
      hasmore: true,
      categoryId: 0
    }
  },
  onLoad(op) {
    setTimeout(()=>{
      uni.setNavigationBarTitle({
        title: this.$L('分类')
      })
    },0);
    
    this.categoryId = this.$Route.query.categoryId
    this.getComList()
  },
  methods: {
    load() {
      if (this.hasmore) {
        this.getComList()
      }
    },
    getComList() {
      let param = {
        url: 'v3/spreader/front/problem/list',
        data: {
          current: this.pn,
          categoryId: this.categoryId
        }
      }
      this.$request(param).then((res) => {
        if (res.state == 200) {
          if (this.pn == 1) {
            this.comList = res.data.list
          } else {
            this.comList = this.comList.concat(res.data.list)
          }

          if (this.$checkPaginationHasMore(res.data.pagination)) {
            this.pn++
            this.hasmore = true
          } else {
            this.hasmore = false
          }
        } else {
          this.$api.msg(res.msg)
        }
      })
    },
    navTo(url) {
      this.$Router.push(url)
    }
  }
}
</script>

<style lang="scss">
page {
  background: $bg-color-split;
  width: 750rpx;
  margin: 0 auto;
}

.main_com {
  padding: 20rpx 20rpx;
}

.list_cell_con {
  padding: 0 32rpx;
  background-color: #fff;

  &:first-child {
    border-radius: 16rpx 16rpx 0 0;

    .list_cell {
      border-top: none;
    }
  }

  &:last-child {
    border-radius: 0 0 16rpx 16rpx;

    .list_cell {
      border-bottom: none;
    }
  }
}

.list_cell {
  display: flex;
  align-items: center;

  line-height: 100rpx;
  height: 100rpx;
  position: relative;
  background: #fff;
  border-bottom: 1rpx solid #f2f2f2;
  justify-content: center;

  .cell_more {
    color: $main-third-color;
    font-size: 18rpx;
    margin-left: 10rpx;
    margin-top: -5rpx;

    .img {
      width: 15rpx;
      height: 25rpx;
      display: inline-block;
      margin-left: 20rpx;
    }
  }

  .cell_tit {
    width: 650rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    font-size: 28rpx;
    margin-right: 10rpx;
    color: #333333;
  }
}
</style>
