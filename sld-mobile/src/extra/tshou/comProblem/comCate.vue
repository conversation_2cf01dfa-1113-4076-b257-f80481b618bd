<template>
	<view :style="mix_diyStyle">
  <view class="main_com">
    <scroll-view
      class="main_com_scroll"
      scroll-y="true"
      @scrolltolower="load"
      v-if="comCate.length > 0"
    >
      <view>
        <view
          class="list_cell_con"
          @click="
            navTo({
              path: '/extra/tshou/comProblem/comList',
              query: { categoryId: item.categoryId }
            })
          "
          v-for="(item, index) in comCate"
          :key="index"
        >
          <view class="list_cell">
            <text class="cell_tit">{{ item.categoryName }}</text>
            <text class="cell_more">
              <image
                class="img"
                :src="imgUrl + 'shop/to_right.png'"
                mode="aspectFit"
              ></image>
            </text>
          </view>
        </view>
      </view>
      <loadingState :state="loadState"></loadingState>
    </scroll-view>
    <tsEmpty
      :text="$L('暂无分类')"
      v-if="comCate.length < 0 && !loading"
    ></tsEmpty>
  </view>
	</view>
</template>

<script>
import loadingState from '@/components/loading-state.vue'
import tsEmpty from '../component/tsEmpty.vue'
import { mapState } from 'vuex'
export default {
  components: {
    loadingState,
    tsEmpty
  },
  data() {
    return {
      imgUrl: process.env.VUE_APP_IMG_URL,
      pn: 1,
      hasmore: true,
      comCate: [],
      loading: true,
      loadState: ''
    }
  },
  onLoad() {
    setTimeout(()=>{
      uni.setNavigationBarTitle({
        title: this.$L('常见问题')
      })
    },0);
    
    if (!this.hasLogin) {
      let urls = this.$Route.path
      const query = this.$Route.query
      uni.setStorageSync('fromurl', {
        url: urls,
        query
      })
      this.$Router.push('/pages/public/login')
    }
    this.getComCate()
  },
  computed: {
    ...mapState(['hasLogin'])
  },
  methods: {
    load() {
      if (this.hasmore) {
        this.loadState = 'loading'
        this.getComCate()
      }
    },
    getComCate() {
      let param = {
        url: 'v3/spreader/front/problemCategory/list',
        data: {
          current: this.pn
        }
      }
      this.$request(param).then((res) => {
        if (res.state == 200) {
          this.loading = false
          if (this.pn == 1) {
            this.comCate = res.data.list
          } else {
            this.comCate = this.comCate.concat(res.data.list)
          }

          if (this.$checkPaginationHasMore(res.data.pagination)) {
            this.pn++
            this.hasmore = true
            this.loadState = 'allow_loading_more'
          } else {
            this.hasmore = false
            this.loadState = 'no_more_data'
          }
        } else {
          this.$api.msg(res.msg)
        }
      })
    },
    navTo(url) {
      this.$Router.push(url)
    }
  }
}
</script>

<style lang="scss">
page {
  background: $bg-color-split;
  width: 750rpx;
  margin: 0 auto;
}

.main_com {
  padding: 20rpx;

  .main_com_scroll {
    height: calc(100vh - 40rpx);
  }
}

.list_cell_con {
  padding: 0 32rpx;
  background-color: #fff;

  &:first-child {
    border-radius: 16rpx 16rpx 0 0;

    .list_cell {
      border-top: none;
    }
  }

  &:last-child {
    border-radius: 0 0 16rpx 16rpx;

    .list_cell {
      border-bottom: none;
    }
  }
}

.list_cell {
  display: flex;
  align-items: center;

  line-height: 100rpx;
  height: 100rpx;
  position: relative;
  background: #fff;
  border-bottom: 1rpx solid #f2f2f2;
  justify-content: center;

  .cell_more {
    color: $main-third-color;
    font-size: 18rpx;
    margin-left: 10rpx;
    margin-top: -5rpx;

    .img {
      width: 15rpx;
      height: 25rpx;
      display: inline-block;
      margin-left: 20rpx;
    }
  }

  .cell_tit {
    width: 650rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    font-size: 28rpx;
    margin-right: 10rpx;
    color: #333333;
  }
}
</style>
