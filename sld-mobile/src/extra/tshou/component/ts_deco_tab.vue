<template>
  <view class="content_ts_tab" :style="[{ background: backGround }]">
    <view class="nav_wrap">
      <view
        class="nav_item"
        v-for="(item, index) in tabInfo"
        :key="index"
        @click="changeTab(index, item.labelId)"
      >
        <view
          :class="currIndex == index ? 'active_nav nav_text' : 'nav_text'"
          >{{ item.labelName }}</view
        >
        <image
          :src="icon"
          mode="aspectFit"
          class="nav_icon"
          v-if="currIndex == index"
        ></image>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'tabMenu',
  data() {
    return {
      icon: process.env.VUE_APP_IMG_URL + 'index/icon.png',
      sortImg: process.env.VUE_APP_IMG_URL + 'index/sort.png',
      currIndex: 0
    }
  },
  props: ['backGround', 'tabInfo'],
  methods: {
    changeTab(index, labelId) {
      if (this.currIndex == index) {
        return
      }
      this.currIndex = index
      if (index > 0) {
        this.$emit('getChildList', labelId, index)
      } else {
        this.$emit('getChildList', null, index)
      }
    },
    toSortPage() {
      this.$Router.pushTab('/pages/category/category')
    }
  }
}
</script>

<style lang="scss">
.content_ts_tab {
  width: 750rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  display: flex;
  align-items: flex-start;
  z-index: 1000;
  overflow-y: hidden;

  .nav_wrap {
    width: 700rpx;
    height:84rpx;
    display: flex;
    overflow-x: scroll;
    padding-top: -9rpx;
    float: left;
    .nav_item {
      margin-right: 35rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      /* #ifndef MP-WEIXIN */
      padding-top: 12rpx;
      /* #endif */
      box-sizing: border-box;
      .nav_text {
        font-size: 32rpx;
        color: #fff;
        white-space: nowrap;
        line-height: 38rpx;
      }
      .nav_icon {
        width: 27rpx;
        height: 9rpx;
      }
    }
  }
  .gap_line {
    width: 13rpx;
    height: 30rpx;
    background: linear-gradient(
      -90deg,
      #ffffff 0%,
      rgba(255, 255, 255, 0) 100%
    );
    opacity: 0.8;
    float: left;
    margin-right: 6rpx;
    margin-left: 4rpx;
    align-self: end;
	/* wx-1-start */
    /* #ifdef MP-WEIXIN */
    margin-top: 10rpx;
    /* #endif */
	/* wx-1-end */
  }
  .sort_wrap {
    font-size: 30rpx;
    color: #fff;
    display: flex;
    justify-content: flex-start;
    box-sizing: border-box;
    float: right;
    text-align: right;
    width: max-content;
    margin-top: -20rpx;
    image {
      width: 32rpx;
      height: 26rpx;
      margin: 8rpx 7rpx 0 7rpx;
    }
    text {
      line-height: 30rpx;
      display: inline-block;
      margin-top: 8rpx;
    }
  }
}
.active_nav {
  font-weight: bold;
  margin-bottom: 6rpx;
}
</style>
