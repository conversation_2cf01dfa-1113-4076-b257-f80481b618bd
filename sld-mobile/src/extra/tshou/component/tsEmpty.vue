<template>
  <view class="empty_data">
    <image
      :src="imgUrl + '' + img"
      mode="aspectFit"
      :style="{ width: width + 'rpx', height: height + 'rpx' }"
    ></image>
    <view class="text">{{ text }}</view>
  </view>
</template>

<script>
export default {
  props: {
    img: {
      type: String,
      default: 'store/no_content.png'
    },
    width: {
      type: String,
      default: '200'
    },
    height: {
      type: String,
      default: '200'
    },
    text: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      imgUrl: process.env.VUE_APP_IMG_URL
    }
  }
}
</script>

<style lang="scss">
.empty_data {
  display: flex;
  align-items: center;
  flex-direction: column;
  width: 100%;
  padding-top: 200rpx;
  image {
    width: 252rpx;
    height: 240rpx;
  }
  view {
    margin-top: 20rpx;
    font-size: 30rpx;
    color: #999999;
  }
}
</style>
