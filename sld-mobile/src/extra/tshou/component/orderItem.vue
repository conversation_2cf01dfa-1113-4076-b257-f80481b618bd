<template>
  <view class="order_item">
    <view class="o_top">
      <text>{{ $L('订单号') }}：{{ order.orderSn }}</text>
      <text>{{ order.orderStateValue }}</text>
    </view>
    <view class="o_middle">
      <view class="o_mid_left">
        <view class="o_left_a">
          <image :src="order.memberAvatar" mode="aspectFit"></image>
          <text>{{ order.memberName }}</text>
        </view>
        <view class="o_left_b">
          <text>{{ $L('订单佣金') }}：¥{{ order.commission }}</text>
          <text>{{ $L('付款金额') }}：¥{{ order.orderAmount }}</text>
        </view>
      </view>
      <view class="o_mid_right">
       <svgGroup type="to_thaw" width="44" height="44" px='rpx' v-if="order.commissionState==3" :color="diyStyle_var['--color_extral_main']">
       </svgGroup>
       <svgGroup type="to_freeze" width="44" height="44" px='rpx' v-else :color="diyStyle_var['--color_extral_vice']">
       </svgGroup>
        <text
          :class="{
            blue: order.commissionState == 3,
            red: order.commissionState != 3
          }"
          >{{ order.commissionStateValue }}</text
        >
      </view>
    </view>
    <view class="o_mid_bottom">
      <text>{{ $L('下单时间') }}：{{ order.createTime }}</text>
      <view
        class=""
        :style="'background-image:url(' + imgUrl + 'tshou/ts_b.png)'"
        >{{
          order.spreaderOrderType == 1 ? $L('一级推手订单') : $L('二级推手订单')
        }}</view
      >
    </view>
  </view>
</template>

<script>
export default {
  props: ['order'],
  data() {
    return {
      imgUrl: process.env.VUE_APP_IMG_URL
    }
  }
}
</script>

<style lang="scss">
.order_item {
  box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding: 20rpx;
  margin-bottom: 20rpx;
  background-color: #fff;
  .o_top {
    display: flex;
    padding-bottom: 12rpx;
    border-bottom: 2rpx solid #f7f7f7;
    justify-content: space-between;
    font-size: 28rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    text {
      &:first-child {
        color: #333333;
      }
      &:last-child {
        color: #7f7f7f;
      }
    }
  }
  .o_middle {
    margin-top: 24rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .o_mid_left {
      display: flex;
      align-items: center;
      .o_left_a {
        border-right: 2rpx solid #f7f7f7;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20rpx 30rpx 20rpx 20rpx;
        text {
          font-size: 22rpx;
        }
        image {
          width: 66rpx;
          height: 66rpx;
          background: #f2f2f2;
          border-radius: 50%;
          margin-bottom: 4rpx;
        }
      }
      .o_left_b {
        margin-left: 50rpx;
        text {
          display: block;
          &:first-child {
            font-size: 28rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #333333;
            line-height: 40rpx;
          }
          &:last-child {
            font-size: 24rpx;
            font-family: PingFangSC-Light, PingFang SC;
            font-weight: 300;
            color: #666666;
            line-height: 40rpx;
          }
        }
      }
    }
    .o_mid_right {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 14rpx;
      image {
        width: 44rpx;
        height: 44rpx;
      }
      text {
        margin-top: 12rpx;

        font-size: 24rpx;
        &.blue {
          color:var(--color_extral_main);
        }
        &.red {
          color:var(--color_extral_vice);
        }
      }
    }
  }
  .o_mid_bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20rpx;
    text {
      font-size: 24rpx;
      font-family: PingFangSC-Light, PingFang SC;
      font-weight: 300;
      color: #999999;
    }
    view {
      padding: 6rpx 12rpx 6rpx 40rpx;
      background-repeat: no-repeat;
      background-position: center;
      background-size: 100% 100%;
      font-size: 22rpx;
      color: #75777f;
    }
  }
}
</style>
