<template>
  <view class="main_sub">
    <view class="banner_con">
      <view class="banner" :style="'background-image:url(' + imgUrl + 'tshou/sub_banner.png)'">
        <text>{{ $L('拥有下级') }}：{{ sub.inviteNum }}</text>
      </view>
      <view class="user_info">
        <image :src="sub.memberAvatar" mode="" class="user_avatar"></image>
        <view class="user_name">{{ sub.memberNickName }}</view>
      </view>
      <!-- <view class="">
          <text>{{ $L('奖励') }}：</text>
          <text
            >{{ $L('¥') }}{{ sub.commissionSum ? sub.commissionSum : 0 }}</text
          >
        </view> -->
      <view :class="{ banner_thi: true, no_but: !is_but }">
        <view>
          <view>
            <image :src="imgUrl + 'tshou/tss_tel.png'" mode="aspectFit"></image>
            <text>{{ sub.memberMobile }}</text>
          </view>
          <view>
            <image :src="imgUrl + 'tshou/tss_re.png'" mode="aspectFit"></image>
            <text>{{ sub.createTime }} {{ $L('注册') }}</text>
          </view>
        </view>
        <view v-if="is_but">
          <view class="but" @click="check(1)">{{ $L('查看下级') }}</view>
        </view>
      </view>
    </view>
    <view class="banner_sub" v-if="sub.spreaderInviteRelationVOList &&
      sub.spreaderInviteRelationVOList.length &&
      checkState
      ">
      <view v-for="(itm, idx) in sub.spreaderInviteRelationVOList" :key="idx" class="sub_item">
        <view class="banner_sec">
          <view class="">{{ itm.memberNickName }}</view>
          <view class="">
            <text>{{ $L('奖励') }}：</text>
            <text>{{ $L('¥')
            }}{{ itm.commissionSum ? itm.commissionSum : 0 }}</text>
          </view>
        </view>
        <view class="banner_thi">
          <view>
            <view>
              <text>{{ itm.memberMobile }}</text>
            </view>
            <view>
              <text>{{ itm.createTime }} {{ $L('注册') }}</text>
            </view>
          </view>
        </view>
        <view class="banner_fth" v-show="idx == 1">
          <view class="but" @click="check(0)">{{ $L('收起下级') }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: ['is_but', 'sub'],
  data() {
    return {
      imgUrl: process.env.VUE_APP_IMG_URL,
      checkState: 0,
      isBut: '',
      subSub: []
    }
  },
  mounted() {
    let param = {
      url: 'v3/spreader/front/spreaderInvite/list',
      data: {
        memberId: this.sub.inviteMemberId
      }
    }
    this.$request(param).then(res => {
      if (res.state == 200) {
        this.subSub = res.data.list
      } else {
        this.$api.msg(res.msg)
      }
    })
  },
  methods: {
    check(type) {
      this.checkState = type
    }
  }
}
</script>

<style lang="scss">
@mixin but {
  font-size: 24rpx;
  width: 128rpx;
  height: 40rpx;
  background: #ffffff;
  border-radius: 22rpx;
  border: 2rpx solid var(--color_extral_main);
  color: var(--color_extral_main);
  text-align: center;
  line-height: 40rpx;
  display: flex;
  justify-content: center;
}

.user_info {
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  margin-top: 20rpx;

  .user_avatar {
    width: 50rpx;
    height: 50rpx;
    border-radius: 50%;
    margin-right: 12rpx;
  }

  .user_name {
    font-size: 24rpx;
    color: #999;
    width: 160rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}


.main_sub {
  border-radius: 12rpx;
  background-color: #fff;
  margin-bottom: 20rpx;

  .banner_con {
    .banner {
      position: relative;
      top: -2rpx;
      left: 0;
      background-repeat: no-repeat;
      background-size: contain;
      padding-left: 20rpx;

      text {
        font-size: 20rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #75777f;
      }
    }
  }

  .banner_sec {
    display: flex;
    padding: 0 20rpx;
    margin-top: 20rpx;
    align-items: flex-end;
    justify-content: space-between;

    view {
      &:first-child {
        font-size: 28rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #262626;
      }

      &:last-child {
        font-size: 24rpx;

        text {
          &:first-child {
            color: #262626;
          }

          &:last-child {
            font-size: 32rpx;
            color: var(--color_extral_vice);
          }
        }
      }
    }
  }


  .banner_thi {
    margin-top: 6rpx;
    padding: 20rpx;
    display: flex;
    justify-content: space-between;
    font-size: 24rpx;
    color: #666666;

    &.no_but {
      justify-content: flex-start !important;
    }

    image {
      margin-right: 10rpx;
      width: 32rpx;
      height: 32rpx;
    }

    view {
      &:first-child {
        display: flex;

        view {
          display: flex;
          align-items: center;
          margin-right: 12rpx;
        }
      }

      &:last-child {
        .but {
          @include but;
        }
      }
    }
  }

  //dd
  .banner_sub {
    background-color: #fcfcfc;
    margin-top: -2rpx;
    border: 2rpx solid #f3f3f3;
    border-radius: 0px 0px 12rpx 12rpx;
    padding-left: 60rpx;
    padding-bottom: 20rpx;

    .banner_thi {
      padding: 8rpx 20rpx;
      justify-content: flex-start;
      border-bottom: 2rpx solid #f3f3f3;
    }

    .sub_item {
      &:last-child {
        .banner_thi {
          border-bottom: none;
        }
      }
    }

    .banner_fth {
      padding-right: 20rpx;
      display: flex;
      justify-content: flex-end;

      .but {
        @include but;
      }
    }
  }
}
</style>
