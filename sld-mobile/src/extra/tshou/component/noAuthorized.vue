<template>
  <view class="empty_data">
    <image
      :src="imgUrl + 'store/no_content.png'"
      mode="aspectFit"
      style="width:200rpx,height:200rpx"
    ></image>
    <view class="text">{{ $L('无权查看') }}</view>
  </view>
</template>

<script>
export default {
  props: ['img', 'width', 'height'],
  data() {
    return {
      imgUrl: process.env.VUE_APP_IMG_URL
    }
  }
}
</script>

<style lang="scss">
.empty_data {
  display: flex;
  align-items: center;
  flex-direction: column;
  width: 100%;
  padding-top: 260rpx;
  image {
    width: 252rpx;
    height: 240rpx;
  }
  view {
    margin-top: 20rpx;
    font-size: 30rpx;
    color: #999999;
  }
}
</style>
