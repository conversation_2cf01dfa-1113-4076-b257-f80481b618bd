<template>
	<view class="goods_item" @click="goDetail">
		<view class="check_box">
			<slot name="checkbox"></slot>
		</view>
		<view class="item_info">
			<view class="coversIO" :style="'background-image:url('+goods.goodsImage+')'" v-if="goods.goodsImage"></view>	
			<view class="coversIO" :style="'background-image:url('+goods.productImage+')'" v-else></view>
			<view class="info_text">
				<view>
					<view class="item_name font">{{goods.goodsName}}</view>
					<view class="item_brief" v-if="goods.goodsBrief">{{goods.goodsBrief}}</view>
					<view class="store-name" v-if="goods && goods.storeName">
						<text>店铺名称: {{goods.storeName}}</text>
					</view>
				</view>
				
				<view>
					<view class="item_price">{{$L('¥')}}{{' '}}{{filters.toFix(goods.productPrice)}}</view>
					<slot name="commission"></slot>
				</view>
			</view>
		</view>
		<view class="slot_button_con">
			<slot name="button"></slot>
		</view>
			
		</view>
	</view>
</template>
<script>
	import filters from "@/utils/filter.js"
	export default{
		props:['goods','spreaderMemberId','isOwner'],
		
		props:{
			goods:{
				type:Object,
				default:()=>{}
			},
			
			spreaderMemberId:{
				type:String,
				default:''
			},
			
			isOwner:{
				type:Boolean,
				default:true
			}
		},
		
		data(){
			return{
				filters
			}
			
		},
		methods:{
			goDetail(){
				this.$emit('upStateShow')
				
				if(this.isOwner){
					this.$Router.push({path:'/extra/tshou/goods/detail',query:{productId:this.goods.productId}})
				}else{
					this.$Router.push({path:'/standard/product/detail',query:{productId:this.goods.productId,u:this.spreaderMemberId}})
				}
				
			}
		}
	}
</script>

<style lang="scss">
	.font{
		font-size: 28rpx;
		font-family: PingFangSC-Medium, PingFang SC;
		font-weight: 500;
		color: #666666;
		line-height: 34rpx;
	}
	.goods_item{
		border-top: 2rpx solid #F7F7F7;
		width: 750rpx;
		display: flex;
		padding: 20rpx;
		position: relative;
		background-color: #fff;
		.check_box{
			display: flex;
			align-items: center;
			justify-content: center;
			// width: 42rpx;
		}
		.item_info{
			display: flex;
			position: relative;
			.image{
				display: block;
				width: 200rpx;
				height: 200rpx;
				border-radius: 10rpx;
				background-color: #EEEEEE;
			}
			.info_text{
				margin-left: 25rpx;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				.item_name{
					max-width: 420rpx;
					margin-top: 14rpx;
					word-break: break-all;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					overflow: hidden;
				}
				.item_brief{
					margin-top: 6rpx;
					max-width: 440rpx;
					font-size: 26rpx;
					color: #666666;
					word-break: break-all;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					overflow: hidden;
				}
				.item_price{
					margin-top: 10rpx;
					font-size: 34rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: 500;
					color:var(--color_extral_main);
					line-height: 58rpx;
				}
			}
			
		}
		.slot_button_con{
		
			position: absolute;
			right: 32rpx;
			bottom: 20rpx;
			
		}
	}

	.coversIO{
		background-position: center center;
		background-repeat: no-repeat;
		background-size: cover;
		overflow: hidden;
		border-radius: 10rpx;
		background-color: #F8F6F7;
		flex-shrink: 0;
		width: 200rpx;
		height: 200rpx;
	}

	.store-name {
		font-size: 24rpx;
		color: #999;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
	}
</style>
