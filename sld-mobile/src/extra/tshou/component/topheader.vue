<template>
  <view class="top_bar">
    <view class="top_header">
      <view class="top_header_left" @click="goBack" v-if="!back">
        <image :src="imgUrl + 'index/back.png'" mode="aspectFit"></image>
      </view>
      <view class="top_header_left" v-else>
        
      </view>
      <view class="top_header_cen">{{ title }}</view>
      <view class="top_white_space">
        <slot></slot>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    back: {
      type: Boolean,
      default: false
    },
    
  },
  data() {
    return {
      imgUrl: process.env.VUE_APP_IMG_URL
    }
  },
  methods: {
    goBack() {
      this.$back(1)
    }
  }
}
</script>

<style lang="scss">
.top_bar {
  width: 750rpx;
  width: 750rpx;
  .top_header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .top_header_left {
      padding-left: 20rpx;
      image {
        width: 17rpx;
        height: 29rpx;
      }
    }
    .top_header_cen {
      margin: 0 50rpx;
      font-size: 36rpx;
      font-family: PingFang SC;
      color: #ffffff;
    }
    .top_white_space {
      width: 40rpx;
      height: 49rpx;
      padding-right: 20rpx;
      image {
        width: 40rpx;
        height: 40rpx;
      }
    }
  }
}
</style>
