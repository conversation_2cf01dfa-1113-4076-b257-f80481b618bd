import Router from 'uni-simple-router';
import Vue from 'vue';
import store from './store';
import {
	statAfterEachAction
} from "./utils/stat.js";

import diyStyle from './diyStyle';

// #ifdef MP-WEIXIN
//方法重写----当页面栈接近8时 push方法用replace方法替换
const oldPush = Router.prototype.push
Router.prototype.push = function (arg) {
	if (getCurrentPages() && getCurrentPages().length > 8) {
		Router.prototype.replace.apply(this, [arg])
	} else {
		oldPush.apply(this, [arg])
	}
}
// #endif

//#ifdef H5 ||MP-WEIXIN
// 方法重写----如果上级页面不存在，则重定向回首页
const oldBack = Router.prototype.back
Router.prototype.back = function (arg) {
	const pages = getCurrentPages()
	console.log('routerBack:', pages)
	const prePage = pages[pages.length - 2]
	if (prePage) {
		oldBack.apply(this, [arg])
	} else {
		Router.prototype.pushTab.apply(this, ['/pages/index/index'])
	}
}
// #endif

const tabberRoute = [
	'/pages/index/index',
	'/pages/category/category',
	'/pages/index/information',
	'/pages/cart/cart',
	'/pages/user/user',
];

Vue.use(Router)
//初始化
const router = new Router({
	encodeURI: false, //默认为true
	routes: [...ROUTES] //路由表
});
const setStyleBefore = diyStyle.getDiyStyleOnce()

//全局路由前置守卫（通过微信分享卡进入时不会触发守卫）
router.beforeEach((to, from, next) => {
	//在前置守卫里调用，不然进入页面会出现颜色闪动的情况
	setStyleBefore()
	let userInfo = uni.getStorageSync('userInfo')
	const hasLogin = store.state.hasLogin
	console.log('前置守卫:', from, '>>>', to, hasLogin)
	//正常守卫判断
	if (to.meta && to.meta.checkLogin) {

		if (!hasLogin && !userInfo) {
			this.$setFromUrl()
			next({ path: '/pages/public/login' })
		} else {
			next();
		}
	} else {
		next();
	}
})



// 全局路由后置守卫
router.afterEach((to, from) => {

	console.log('后置守卫:', from, '>>>', to)
	// #ifdef H5
	if (!tabberRoute.includes(from.path) && tabberRoute.includes(to.path)) {
		diyStyle.dynaSetTabbar(() => {
			setTimeout(() => uni.showTabBar(), 50)
		})
	}
	// #endif

	//后置守卫下的统计操作
	statAfterEachAction(to, from)
})
export default router;