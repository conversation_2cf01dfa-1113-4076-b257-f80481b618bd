

export const getWxH5Appid = async (_unicode='') => {
	let tar_url = process.env.VUE_APP_API_URL + 'v3/member/front/login/wxjsConf?source=1&method=getWxH5Appid';

	console.log('getWxH5Appid:url',location.href,'\nencode url=',encodeURIComponent(location.href))

	uni.request({
		url: tar_url,
		method: 'GET',
		data: {
			url: encodeURIComponent(location.href)
		},
		success(res) {
			let data = res.data.data;
			if(data.appId){
				let tar_url = location.href
				tar_url += location.href.indexOf('?') > -1 ? '&' : '?'
				tar_url += 'ori_url=' + location.href
				let uricode = _unicode?_unicode:tar_url
				let search = new URLSearchParams({
					appid:data.appId,
					redirect_uri:uricode,
					response_type:'code',
					scope:'snsapi_userinfo',
					state:'STATE',
				})
				console.log('getWxH5Appid:search',search)
				
				window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?${search.toString()}#wechat_redirect`
			}else{
				uni.showToast({
					title:'appId获取失败',
					icon:"error",
					mask:true
				})
			}
		}
	})
}

export const replaceRedirectUrl = (query)=>{
	let oriUrl = query.ori_url
	let tmp_data = ''
	for (let i in query) {
		if (i != 'ori_url') {
			tmp_data += i + '=' + query[i] + '&'
		}
	}
	oriUrl += '?' + tmp_data
	//微信浏览器的话要把浏览器地址里面的code去掉
	history.replaceState({}, '', oriUrl)
}