const createScript = (src)=>{
	const el = document.createElement('script')
	el.src = src
	return el
}

const createLink = (link)=>{
	const el = document.createElement('link')
	el.rel = "stylesheet"
	el.href = link
	return el
}


const jsPrefix = 'js/'

const cssPrefix = 'css/'


const JS_FILE_PATH = [
	'quill.min.js',
	'image-resize.min.js',
	'video.min.js'
]

const CSS_FILE_PATH = [
	'react-quill.css',
	'video-js.min.css'
]

!function(){
	JS_FILE_PATH.forEach(p=>{
		let el = createScript(`${process.env.VUE_APP_IMG_URL}${jsPrefix}${p}`)
		document.body.appendChild(el)
	})
	
	CSS_FILE_PATH.forEach(p=>{
		let el = createLink(`${process.env.VUE_APP_IMG_URL}${cssPrefix}${p}`)
		document.head.appendChild(el)
	})
	
}()