@use "sass:math";
$mid-width: math.div(750rpx - 60rpx,2);

/* 页面左右间距 */
$page-row-spacing: 30upx;
$page-color-base: #f8f8f8;
$page-color-light: #f8f6fc;
$base-color: #fa436a;

/* 文字尺寸 */
$font-sm: 24upx;
$font-base: 28upx;
$font-lg: 32upx;
/*文字颜色*/
$font-color-dark: #303133;
$font-color-base: #606266;
$font-color-light: #909399;
$font-color-disabled: #C0C4CC;
$font-color-spec: #4399fc;
/* 边框颜色 */
$border-color-dark: #DCDFE6;
$border-color-base: #E4E7ED;
$border-color-light: #EBEEF5;
/* 图片加载中颜色 */
$image-bg-color: #eee;
/* 行为相关颜色 */
$uni-color-primary:#fa436a;
$uni-color-success: #4cd964;
$uni-color-warning: #f0ad4e;
$uni-color-error: #dd524d;

/* zokxc-设计规范 */
$bg-color-split: #f5f5f5; //用于分割背景的底色,页面背景色
$border-color-split: rgba(0,0,0,.1);//辅助分割线
$main-color: #FC1C1C;//主体红色
$com-v-border: 20rpx;//主体内容距离两边的距离
$com-h-border: 20rpx;//主体内容距上下模块的间距
$com-main-font-color: #2D2D2D;//主要内容字体颜色，eg:商品名称
$main-font-color: #333;//用于重要级文字信息、内页标题信息，如导航名称、大板块标题、类目名称等
$main-second-color: #666;//用于普通级段落信息引导词二级标题信息，如商品详情段落标题信息
$main-third-color: #999;//用于辅助、次要的文字信息，如商品评论、规格标识

/* 积分商城 颜色*/ 
$point-good-price-color:#B64CFB;//用于积分商城商品价格背景颜色
$point-btn-color:#9344FF;//用于积分商城兑换按钮背景颜色
$point-index-title-color:#7B2AD0;//用于积分商城首页顶部背景颜色

/*推手*/
$ts-but:linear-gradient(270deg, #F04047 0%, #FE7951 100%);
$ts-but2:linear-gradient(270deg, #FF2C40 0%, #FF3F5E 100%);
$ts-ac:#FF3044;
$ts-font-color:#E9323E;
$ts-font-color2:#FF0000;



/* 颜色变量 */

/* 行为相关颜色 */
$uni-color-primary: #007aff;
$uni-color-success: #4cd964;
$uni-color-warning: #f0ad4e;
$uni-color-error: #dd524d;

/* 文字基本颜色 */
$uni-text-color:#333;//基本色
$uni-text-color-inverse:#fff;//反色
$uni-text-color-grey:#999;//辅助灰色，如加载更多的提示信息
$uni-text-color-placeholder: #808080;
$uni-text-color-disable:#c0c0c0;

/* 背景颜色 */
$uni-bg-color:#ffffff;
$uni-bg-color-grey:#f8f8f8;
$uni-bg-color-hover:#f1f1f1;//点击状态颜色
$uni-bg-color-mask:rgba(0, 0, 0, 0.4);//遮罩颜色

/* 边框颜色 */
$uni-border-color:#c8c7cc;

/* 尺寸变量 */

/* 文字尺寸 */
$uni-font-size-sm:24rpx;
$uni-font-size-base:28rpx;
$uni-font-size-lg:32rpx;

/* 图片尺寸 */
$uni-img-size-sm:40rpx;
$uni-img-size-base:52rpx;
$uni-img-size-lg:80rpx;

/* Border Radius */
$uni-border-radius-sm: 4rpx;
$uni-border-radius-base: 6rpx;
$uni-border-radius-lg: 12rpx;
$uni-border-radius-circle: 50%;

/* 水平间距 */
$uni-spacing-row-sm: 10px;
$uni-spacing-row-base: 20rpx;
$uni-spacing-row-lg: 30rpx;

/* 垂直间距 */
$uni-spacing-col-sm: 8rpx;
$uni-spacing-col-base: 16rpx;
$uni-spacing-col-lg: 24rpx;

/* 透明度 */
$uni-opacity-disabled: 0.3; // 组件禁用态的透明度

/* 文章场景相关 */
$uni-color-title: #2C405A; // 文章标题颜色
$uni-font-size-title:40rpx;
$uni-color-subtitle: #555555; // 二级标题颜色
$uni-font-size-subtitle:36rpx;
$uni-color-paragraph: #3F536E; // 文章段落颜色
$uni-font-size-paragraph:30rpx;

