<!-- 我的优惠券 -->
<template>
	<view :style="mix_diyStyle">
		<view class="my_coupon">
			<view class="my_coupon_nav">
				<view class="my_coupon_nav_pre" :class="{ active: useState == '1' }" @click="handleNav('1')">
					{{ $L('未使用') }}
				</view>
				<view class="my_coupon_nav_pre" :class="{ active: useState == '2' }" @click="handleNav('2')">
					{{ $L('已使用') }}
				</view>
				<view class="my_coupon_nav_pre" :class="{ active: useState == '3' }" @click="handleNav('3')">
					{{ $L('已过期') }}
				</view>
			</view>
			<view v-if="couponList.length" class="coupon_con">
				<view class="my_coupon_center" @click="goCouponCenter()">
					<image :src="imgUrl + 'coupon/coupon_center_bg.png'" mode="aspectFit"></image>
				</view>
				<view class="my_coupon_list">
					<view class="my_coupon_pre" v-for="(item, index) in couponList" :key="index">
						<!-- <view class="coupon_pre_top" :style="{ backgroundImage: 'url(' + item.couponBg + ')' }"> -->
						<view class="coupon_pre_top">
							<image :src="imgUrl + 'coupon/coupon_pre_img_bg.png'" mode="" class="coupon_pre_top_bg_img"></image>
							<view class="coupon_pre_left">
								<view class="coupon_pre_price" :class="{ coupon_pre_price_high: item.publishValue.toString().length > 6,grey: item.useState != 1 }"
									v-if="item.couponType == 4">
									<block v-if="Number(item.publishValue)>0">
										<text class="unit">{{ $L('¥ ') }}</text>
										<text class="price_int">{{ $getPartNumber(item.publishValue, 'int') }}</text>
										<text class="price_int" v-if="item.publishValue.toString().indexOf('.') != -1">{{
											$getPartNumber(item.publishValue, 'decimal') }}</text>
									</block>
									<block v-else>
										<text class="price_freight">免运费</text>
									</block>
								</view>





								<view v-else-if="item.couponType != 2" class="coupon_pre_price"
									:class="{ grey: item.useState != 1, coupon_pre_price_high: item.publishValue.toString().length > 6 }">
									<text class="unit">{{ $L('¥') }}</text>
									<text class="price_int">{{ $getPartNumber(item.publishValue, 'int') }}</text>
									<text class="price_decimal">{{ $getPartNumber(item.publishValue, 'decimal') }}</text>
								</view>
								<view v-else class="coupon_pre_price" :class="{ grey: item.useState != 1 }">
									<view class=""></view>
									<text class="price_int">{{ filters.toSplit(filters.toFixNum(item.publishValue, 1))[0] }}</text>.
									<text class="price_decimal">{{ filters.toSplit(filters.toFixNum(item.publishValue, 1))[1] }}</text>
									<text class="price_decimal">{{ $L('折') }}</text>
								</view>
								<view class="coupon_pre_active" :class="{ grey_con: item.useState != 1 }">
									{{ item.couponContent }}
								</view>
							</view>
							<view class="coupon_pre_cen">
								<view class="coupon_pre_title">{{ item.useTypeValue }}</view>
								<view class="coupon_pre_time">{{ item.effectiveStart }}~{{ item.effectiveEnd }}</view>
								<view class="coupon_pre_rules" @click="descriptionOpen(item.couponMemberId)">
									<text>{{ $L('使用规则') }}</text>
									<image :src="item.isOpen ? imgUrl + 'coupon/up.png' : imgUrl + 'coupon/down.png'" mode="aspectFit">
									</image>
								</view>
							</view>
							<view class="coupon_pre_right_box">
								<svgGroup type="to_coupon" class="coupon_pre_right_img" width="192" height="190" px="rpx"
									:color="diyStyle_var['--color_coupon_main']" v-if='item.useState == 1'>
								</svgGroup>
								<svgGroup type="to_coupon" class="coupon_pre_right_img" width="192" height="190" px="rpx" color="#ddd"
									v-else>
								</svgGroup>
								<view class="coupon_pre_right" @click="goGoodsList(item)">
									{{ item.useState == '1' ? '立即使用' : item.useState == '2' ? '已使用' : '已过期' }}
								</view>
							</view>
						</view>
						<view class="coupon_rules" v-if="item.isOpen == true">
							<view class="coupon_rules_title">{{ $L('使用规则') }}:{{ item.description }}</view>
						</view>
						<view class="coupon_type" :class="{ grey_type: item.useState != 1 }">{{ item.couponTypeValue }}
						</view>
					</view>
					<loadingState v-if="loadingState == 'first_loading' || couponList.length > 0" :state="loadingState" />
				</view>
			</view>
			<view class="no_data" v-else>
				<image :src="imgUrl + 'no_coupon.png'" mode="aspectFit"></image>
				<text>{{ $L('暂无优惠券，去领券中心看看吧') }}~</text>
				<view class="go_coupon_center" @click="goCouponCenter">{{ $L('领券中心') }}</view>
			</view>
		</view>
	</view>
</template>
<script>
import loadingState from '@/components/loading-state.vue'
import filters from '../../utils/filter.js'
import {
	mapState
} from 'vuex'
export default {
	components: {
		loadingState
	},
	data () {
		return {
			imgUrl: process.env.VUE_APP_IMG_URL,
			useState: '1', //使用状态
			pageSize: 10,
			current: 1,
			couponList: [], //优惠券列表
			noData: false, //无数据
			hasMore: false, //是否还有数据
			loadingState: 'first_loading',
			goReceiveBg: process.env.VUE_APP_IMG_URL + 'coupon/coupon_pre_bg.png', //未使用
			finishReceiveBg: process.env.VUE_APP_IMG_URL + 'coupon/finishReceiveBg.png', //已使用，已过期,
			showState: false,
			filters
		}
	},
	computed: {
		...mapState(['hasLogin', 'userInfo'])
	},
	async onLoad (options) {
		setTimeout(() => {
			uni.setNavigationBarTitle({
				title: this.$L('优惠券')
			})
		}, 0);

		this.getCouponList()
	},

	onShow () {
		if (this.showState) {
			this.getCouponList()
			this.showState = false
		}
	},

	// 触底加载更多
	onReachBottom () {
		if (this.hasMore) {
			this.getCouponList()
		}
	},
	methods: {
		//获取优惠券列表
		getCouponList () {
			let param = {}
			param.url = 'v3/promotion/front/coupon/list'
			param.method = 'GET'
			param.data = {}
			param.data.current = this.current
			param.data.pageSize = this.pageSize
			param.data.useState = this.useState
			this.$request(param)
				.then((res) => {
					if (res.state == 200) {
						let result = res.data
						if (this.current == 1) {
							this.couponList = result.list
						} else {
							this.couponList = this.couponList.concat(result.list)
						}
						this.couponList.forEach((item, index) => {
							if (item.useState == 1) {
								item.couponBg = this.goReceiveBg
							} else {
								item.couponBg = this.finishReceiveBg
							}
						})
						this.hasMore = this.$checkPaginationHasMore(res.data.pagination) //是否还有数据
						if (this.hasMore) {
							this.current++
							this.loadingState = 'allow_loading_more'
						} else {
							this.loadingState = 'no_more_data'
						}
					} else {
						this.$api.msg(res.msg)
					}
				})
				.catch((e) => {
					//异常处理
				})
		},
		//点击nav导航
		handleNav (type) {
			this.useState = type
			this.current = 1
			this.getCouponList()
			uni.pageScrollTo({
				scrollTop: 0
			})
		},
		//去领券中心页面
		goCouponCenter () {
			this.showState = true
			this.$Router.push('/standard/coupon/couponCenter')
		},
		//规则展开
		descriptionOpen (couponMemberId) {
			this.couponList.map((item) => {
				if (item.couponMemberId == couponMemberId) {
					if (item.description != '') {
						item.isOpen = !item.isOpen
						this.$forceUpdate()
					}
				} else {
					item.isOpen = false
				}
			})
		},
		//去优惠券对应的商品列表
		goGoodsList (item) {
			if (item.useState == 1) {
				let params = {}
				if (item.storeId > 0) {
					params.storeId = item.storeId
				}

				if (item.useType == 2 && item.goodsIds) {
					params.goodsIds = item.goodsIds
				} else if (item.useType == 3 && item.cateIds) {
					params.categoryId = item.cateIds
				}
				this.$Router.push({
					path: '/standard/product/list',
					query: {
						source: 'coupon',
						...params
					}
				})
			}
		}
	}
}
</script>

<style lang="scss">
page {
	background: $bg-color-split;
}

.price_freight {
  font-size: 30rpx;
}

.my_coupon {
	width: 750rpx;
	margin: 0 auto;

	.my_coupon_nav {
		padding-left: 39rpx;
		padding-right: 35rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		background: #ffffff;
		position: fixed;
		top: var(--bar-height);
		z-index: 100;
		width: 750rpx;
		box-sizing: border-box;

		.my_coupon_nav_pre {
			font-size: 32rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #333333;
			line-height: 39rpx;
			padding: 26rpx 0;
		}

		.active {
			font-size: 32rpx;
			font-family: PingFang SC;
			font-weight: bold;
			color: var(--color_coupon_main);
			line-height: 39rpx;
			border-bottom: 6rpx solid var(--color_coupon_main);
		}
	}

	.coupon_con {
		padding-top: 88rpx;
	}

	.my_coupon_center {
		background: #ffffff;

		image {
			width: 717rpx;
			height: 245rpx;
		}
	}

	.my_coupon_list {
		padding-top: 20rpx;

		.my_coupon_pre {
			width: 710rpx;
			margin-bottom: 20rpx;
			position: relative;
			margin: 0 auto 20rpx;

			.coupon_pre_top {
				height: 190rpx;
				background-size: 100% 100%;
				display: flex;
				align-items: center;
				position: relative;

				.coupon_pre_top_bg_img {
					position: absolute;
					left: 0;
					top: 0;
					width: 585rpx;
					height: 190rpx;
				}

				.coupon_pre_left {
					position: relative;
					display: flex;
					flex-direction: column;
					width: 203rpx;
					align-items: center;

					.coupon_pre_price {
						font-size: 20rpx;
						font-family: Source Han Sans CN;
						font-weight: bold;
						color: var(--color_coupon_main);
						line-height: 31rpx;
						display: flex;
						align-items: baseline;

						text:nth-child(2) {
							font-size: 48rpx;
							font-family: Source Han Sans CN;
							font-weight: bold;
							color: var(--color_coupon_main);
							line-height: 31rpx;
						}

						.price_int {
							text-align: center;
							word-break: break-all;
						}
					}

					.coupon_pre_price_high {
						position: relative;
						left: 2rpx;
						top: 14rpx;
						margin-top: 8rpx;

						text:nth-child(2) {
							line-height: 40rpx;
						}
					}

					.coupon_pre_active {
						font-size: 24rpx;
						font-family: Source Han Sans CN;
						font-weight: 400;
						color: var(--color_coupon_main);
						line-height: 31rpx;
						text-align: center;
						margin-top: 20rpx;
					}

					.grey {
						color: #333333;

						.price_int {
							color: #333333 !important;
						}
					}

					.grey_con {
						color: #999999;
					}
				}

				.coupon_pre_cen {
					position: relative;
					display: felx;
					flex-direction: column;
					flex: 1;
					padding-left: 44rpx;

					.coupon_pre_title {
						font-size: 30rpx;
						font-family: PingFang SC;
						font-weight: bold;
						color: #111111;
						line-height: 31rpx;
					}

					.coupon_pre_time {
						font-size: 24rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #333333;
						line-height: 31rpx;
						margin: 21rpx 0 17rpx;
					}

					.coupon_pre_rules {
						display: flex;
						align-items: center;

						text {
							font-size: 24rpx;
							font-family: PingFang SC;
							font-weight: 500;
							color: #999999;
							line-height: 31rpx;
						}

						image {
							width: 12rpx;
							height: 7rpx;
							margin-left: 20rpx;
						}
					}
				}

				.coupon_pre_right_box {
					position: relative;
					background: #fff;
					border-bottom-right-radius: 15rpx;
					border-top-right-radius: 15rpx;

					.coupon_pre_right_img {
						position: absolute;
						right: -26rpx;
						top: 0;
						z-index: 0;
					}
				}

				.coupon_pre_right {
					position: relative;
					font-size: 24rpx;
					font-family: Source Han Sans CN;
					font-weight: 400;
					color: #ffffff;
					width: 130rpx;
					text-align: center;
					box-sizing: border-box;
					height: 190rpx;
					line-height: 190rpx;
				}
			}

			.coupon_rules {
				position: relative;
				width: 710rpx;
				padding: 20rpx 43rpx;
				box-sizing: border-box;
				font-size: 22rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #666666;
				line-height: 30rpx;
				background: #ffffff;
				border-top: 1rpx solid #f2f2f2;
				border-radius: 0 0 15rpx 15rpx;

				.coupon_rules_title {
					position: relative;
					margin-bottom: 10rpx;
				}
			}

			.coupon_type {
				position: absolute;
				top: 0;
				left: 0;
				padding: 0 5rpx;
				height: 30rpx;
				background: var(--color_coupon_main);
				font-size: 20rpx;
				font-family: Source Han Sans CN;
				font-weight: 400;
				color: #ffffff;
				line-height: 30rpx;
				text-align: center;
				border-radius: 15rpx 0 15rpx 0;
			}

			.grey_type {
				background: linear-gradient(0deg,
						#a9a28c 0%,
						#b1b7b0 0%,
						#9ea59d 0%,
						#9fa19e 0%,
						#6c6d74 100%);
				color: #ffffff;
			}
		}
	}

	.no_data {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-top: 180rpx;

		image {
			width: 380rpx;
			height: 280rpx;
		}

		text {
			font-size: 26rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: #999999;
			margin: 30rpx 0;
		}

		.go_coupon_center {
			width: 160rpx;
			height: 54rpx;
			background: var(--color_coupon_halo);
			border-radius: 27rpx;
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: var(--color_coupon_main);
			text-align: center;
			line-height: 54rpx;
		}
	}
}
</style>