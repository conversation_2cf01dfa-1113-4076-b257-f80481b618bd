<template>
	<view :style="mix_diyStyle">
  <view class="rank_detail" v-if="!loading && rankDetail != null">
    <banner
      :bannerBg="bannerBg"
      :fixed="topFixed"
      :info="bannerInfo"
      :showHeader="showHeader"
      @share="$refs.popShare.open()"
    ></banner>
    <view class="goods_list">
      <block v-for="(item, index) in rankDetail.goodsList" :key="index">
        <goodsItem1
          :info="item"
          :index="index"
          :isDiy="rankDetail.description"
        ></goodsItem1>
      </block>
      <view class="more flex_row_between_center" @click="seeMore">
        <view class="flex_row_between_center">
          <image :src="imgUrl + 'rank/icon2.png'"></image>
          <text>{{ $L('更多榜单') }}</text>
        </view>
        <text>{{ $L('查看全部榜单') }}></text>
      </view>
      <block v-for="(item2, index2) in rankRecomList" :key="index">
        <goodsItem2 :info="item2"></goodsItem2>
      </block>
    </view>
    <uni-popup ref="popShare" type="bottom">
      <view class="popShare">
        <view class="tri_share">
		  <!-- app-1-start -->



















		  <!-- app-1-end -->
		  <!-- wx-1-start -->
          <!-- #ifdef MP-WEIXIN -->
          <button
            open-type="share"
            @click="$refs.popShare.close()"
            class="shareButton"
          >
            <view class="img_con flex_column_between_center" open-type="share">
              <image
                :src="imgUrl + 'rank/wx_share.png'"
                mode="aspectFit"
              ></image>
              <text>{{ $L('微信好友') }}</text>
            </view>
          </button>
          <!-- #endif -->
		  <!-- wx-1-end -->
          <!-- #ifdef H5 -->
          <block v-if="$isWeiXinBrower()">
            <view
              class="img_con flex_column_between_center"
              @tap.stop="sldShareBrower(1)"
            >
              <image
                :src="imgUrl + 'rank/wx_share.png'"
                mode="aspectFit"
              ></image>
              <text>{{ $L('微信好友') }}</text>
            </view>
            <view
              class="img_con flex_column_between_center"
              @tap.stop="sldShareBrower(2)"
            >
              <image
                :src="imgUrl + 'rank/pyq_share.png'"
                mode="aspectFit"
              ></image>
              <text>{{ $L('朋友圈') }}</text>
            </view>
          </block>
          <!-- #endif -->
         <view class="img_con flex_column_between_center" @click="copyLink">
            <image
              :src="imgUrl + 'rank/copy_link.png'"
              mode="aspectFit"
            ></image>
            <text>{{ $L('复制链接') }}</text>
          </view>
        </view>
        <view class="can_con" @click="$refs.popShare.close()">{{
          $L('取消')
        }}</view>
      </view>
    </uni-popup>

    <!-- 微信浏览器分享提示  start-->
    <view class="wx_brower_share_mask" v-if="showWeiXinBrowerTip">
      <view class="wx_brower_share_top_wrap">
        <image
          :src="imgUrl + 'wx_share_tip.png'"
          mode="widthFix"
          @tap="showWeiXinBrowerTip = false"
          class="wx_brower_share_img"
        ></image>
      </view>
    </view>
    <!-- 微信浏览器分享提示  end-->
  </view>
  <view v-else-if="!loading && rankDetail == null">
    <empty></empty>
  </view>
	</view>
</template>

<script>
import empty from './components/empty.vue'
import { getCurLanguage } from '@/utils/base.js'
import h5Copy from '@/static/h5/H5copy.js'
import uniPopup from '@/components/uni-popup/uni-popup.vue'
import goodsItem2 from './components/goodsItem2.vue'
import banner from './components/banner.vue'
import goodsItem1 from './components/goodsItem1.vue'
export default {
  components: {
    banner,
    goodsItem1,
    goodsItem2,
    uniPopup,
    empty
  },
  data() {
    return {
      imgUrl: process.env.VUE_APP_IMG_URL,
      rankId: null,
      rankId: 0,
      rankDetail: {},
      rankRecomList: [],
      topFixed: false,
      bannerInfo: {},
      showHeader: false,
      shareData: {
        title: '',
        desc: getCurLanguage(
          this.$L('商品太多不知道买什么？快来排行榜看看吧！')
        ),
        path: '',
        imageUrl: ''
      },
      loading: true,
      openState: true,
      showWeiXinBrowerTip: false,
      isWeiXinBroswer: false,
      bannerBg: process.env.VUE_APP_IMG_URL + 'rank/rank_bg.png'
    }
  },

  onShareAppMessage: function () {
    return {
      ...this.shareData
    }
  },

  onShareTimeline: function () {
    return {
      ...this.shareData
    }
  },

  onLoad(options) {
    this.isWeiXinBroswer = this.$isWeiXinBrower()
    this.rankId = this.$Route.query.rankId
    if (options.scene) {
      let url = decodeURIComponent(options.scene)
      this.rankId = url
    }
    this.shareData.path = `/standard/rank/detail?rankId=${this.rankId}`
    this.shareData.link = `${
      process.env.VUE_APP_API_URL
    }standard/rank/detail?rankId=${this.rankId}`
    this.shareData.query = `rankId=${this.rankId}`
    this.getRankList()
    this.getRecomRank()
  },

  onUnload() {
    this.shareData = {}
  },

  onReachBottom() {},

  onHide() {
    this.showWeiXinBrowerTip = false
  },

  onPageScroll(e) {
    if (e.scrollTop > 1) {
      this.topFixed = true
    } else {
      this.topFixed = false
    }

    if (e.scrollTop > 60) {
      this.showHeader = true
    } else {
      this.showHeader = false
    }
  },

  methods: {
    getRankList() {
      this.$request({
        url: 'v3/goods/front/goods/rank/detail',
        data: {
          rankId: this.rankId
        }
      }).then((res) => {
        this.loading = false
        if (res.state == 200) {
          this.rankDetail = res.data
          if (this.rankDetail) {
            this.bannerBg = res.data.backgroundImage
              ? res.data.backgroundImage
              : process.env.VUE_APP_IMG_URL + 'rank/rank_bg.png'
            this.shareData.title = this.rankDetail.rankName
            this.shareData.imageUrl =
              this.rankDetail.goodsList.length &&
              this.rankDetail.goodsList[0].goodsImage
            this.shareData.imgUrl =
              this.rankDetail.goodsList.length &&
              this.rankDetail.goodsList[0].goodsImage
            this.bannerInfo = {
              rankName: this.rankDetail.rankName,
              description: this.rankDetail.description
            }
          }
        } else {
          this.$api.msg(res.msg)
        }
      })
    },
    getRecomRank() {
      this.$request({
        url: 'v3/goods/front/goods/rank/moreRankRecommend',
        data: {
          rankId: this.rankId
        }
      }).then((res) => {
        if (res.state == 200) {
          this.rankRecomList = res.data
        }
      })
    },
    seeMore() {
      this.$Router.push({
        path: '/standard/rank/aggr',
        query: { categoryId: this.rankDetail.categoryId }
      })
    },
    sldShareBrower(type) {
      this.$refs.popShare.close()
      this.showWeiXinBrowerTip = true
      this.share_model = false
      this.$WXBrowserShareThen(type, {
        ...this.shareData
      })
    },

    sldShare(type, scene) {
      this.$refs.popShare.close()
      let shareData1 = {}
      let { shareData } = this
      if (type == 0) {
        shareData1.href = shareData.link
        shareData1.title = shareData.title
        shareData1.summary = shareData.desc
        shareData1.imageUrl = shareData.imageUrl
      } else if (type == 2) {
        shareData1.imageUrl = shareData.imageUrl
      }
      this.$weiXinAppShare(type, scene, shareData1)
    },

    copyLink() {
      let col =
        process.env.VUE_APP_API_URL +
        `standard/rank/detail?rankId=${this.rankId}`
      let that = this
      // #ifdef H5
      const result = h5Copy(col)
      if (result === false) {
        this.$api.msg(this.$L('不支持'))
      } else {
        this.$api.msg(this.$L('已复制到剪贴板'))
      }
      // #endif

      // #ifdef APP-PLUS || MP-WEIXIN
      uni.setClipboardData({
        data: col,
        success: function () {
          that.$api.msg(this.$L('已复制到剪贴板'))
        }
      })
      // #endif
      this.$refs.popShare.close()
    }
  }
}
</script>

<style lang="scss">
page {
  background-color: #f6f6f6;
}

.rank_detail {
  .goods_list {
    margin-top: -2rpx;
    padding: 20rpx;
    background-color: #f6f6f6;
    position: relative;
    .more {
      background-color: #fff;
      padding: 16rpx 20rpx;
      border-radius: 6px;
      margin-bottom: 20rpx;

      image {
        width: 36rpx;
        height: 36rpx;
        margin-right: 16rpx;
      }

      text {
        font-size: 28rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #333333;
      }
    }
  }
}

.popShare {
  border-radius: 10px 10px 0px 0px;
  background: #ebebeb;
  padding: 20rpx;
  height: 354rpx;

  .tri_share {
    display: flex;
    justify-content: space-evenly;
    margin-top: 50rpx;

    .img_con {
      width: 108rpx;

      image {
        width: 108rpx;
        height: 108rpx;
      }

      text {
        margin-top: 16rpx;
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #8c8c8c;
      }
    }
  }

  .can_con {
    width: 100%;
    height: 68rpx;
    background: #ffffff;
    border-radius: 10px;
    text-align: center;
    line-height: 68rpx;
    font-size: 28rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #4c4c4c;
    margin-top: 40rpx;
  }
}

.shareButton {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: transparent;
  border-radius: 0;
  height: auto;
  line-height: unset;
  padding: 0;
  margin: 0;

  &::after {
    border-width: 0;
  }
}

/* 微信浏览器分享提示 start */
.wx_brower_share_mask {
  width: 750rpx;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.45);
  position: fixed;
  z-index: 99999;
  top: 0;
}

::-webkit-scrollbar {
  display: none;
}

scroll-view ::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}

.wx_brower_share_top_wrap {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  margin-top: 30rpx;
}

.wx_brower_share_top_wrap .wx_brower_share_img {
  width: 450rpx;
  height: 150rpx;
  margin-right: 30rpx;
}

.share_h5 {
  width: 100% !important;
  height: 100% !important;
}

uni-image > img {
  opacity: unset;
  object-fit: contain;
}

.share_h5_operate_img {
  width: 440rpx !important;
  height: 120rpx !important;
}

.share_h5_close_img {
  width: 50rpx !important;
  height: 50rpx !important;
}

.share_h5_img_bottom {
  width: 50rpx !important;
  height: 200rpx !important;
}

/* 微信浏览器分享提示 end */
</style>
