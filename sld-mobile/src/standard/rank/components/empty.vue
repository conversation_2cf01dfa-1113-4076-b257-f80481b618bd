<!-- 推荐商品列表组件 -->
<template name="activity_not_open">
  <view class="activity_not_open">
    <view class="empty_sort_page">
      <image :src="imgUrl + 'empty_goods.png'" mode="aspectFit" class="empty_img">
      </image>
      <view class="empty_text">{{ $L('暂无数据') }}</view>
      <view class="back" @click="$Router.back(1)">{{ $L('返回') }}</view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ActivityNotOpen',
  components: {},
  data() {
    return {
      imgUrl: process.env.VUE_APP_IMG_URL
    }
  },
  mounted() {}
}
</script>

<style lang="scss">
.activity_not_open {
  .empty_sort_page {
    width: 100%;
    // height: 100vh;
    background: #f5f5f5;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 340rpx;

    .empty_img {
      width: 380rpx;
      height: 280rpx;
      margin-bottom: 32rpx;
    }

    .empty_text {
      font-size: 26rpx;
      color: #999;
    }

    .back {
      margin-top: 80rpx;
      padding: 10rpx 50rpx;
      font-size: 28rpx;
      border: 2rpx solid #999;
      border-radius: 30rpx;
      color: #999;
    }
  }
}
</style>
