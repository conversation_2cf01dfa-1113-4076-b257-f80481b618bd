<template>
  <view class="goods_item2">
    <navigator :url="`/standard/rank/detail?rankId=${info.rankId}`">
      <view class="rank_title flex_row_start_center">
        <image :src="imgUrl + 'rank/icon3.png'" mode="aspectFit"></image>
        <text>{{ info.rankName }}</text>
        <image :src="imgUrl + 'ladder/right1.png'" mode="aspectFit"></image>
      </view>
    </navigator>
    <view
      :class="[
        'rank_goods',
        info.goodsList.length == 3
          ? 'flex_row_between_center'
          : 'flex_row_start_center'
      ]"
      v-if="info.goodsList.length"
    >
      <block v-for="(item, index) in info.goodsList" :key="index">
        <navigator
          :url="`/standard/product/detail?productId=${item.productId}&goodsId=${item.goodsId}`"
        >
          <view class="good_item">
            <view
              :class="'top' + (index + 1)"
              class="flex_row_center_center rank_top"
              >{{ index + 1 }}</view
            >
            <view
              class="image"
              :style="{ backgroundImage: 'url(' + item.goodsImage + ')' }"
            ></view>
            <view class="text flex_column_center_center">
              <view>{{ item.goodsName }}</view>
              <text
                >{{ $L('¥') }}{{ $getPartNumber(item.productPrice, 'int')
                }}{{ $getPartNumber(item.productPrice, 'decimal') }}</text
              >
            </view>
          </view>
        </navigator>
      </block>
    </view>
  </view>
</template>

<script>
export default {
  props: ['info'],
  data() {
    return {
      imgUrl: process.env.VUE_APP_IMG_URL
    }
  }
}
</script>

<style lang="scss">
.goods_item2 {
  padding: 20rpx;
  border-radius: 6px;
  background-color: #fff;
  margin-bottom: 20rpx;

  .rank_title {
    text {
      font-size: 30rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #1c1c38;
      line-height: 30rpx;
      font-weight: bold;
    }

    image:first-child {
      width: 60rpx;
      height: 44rpx;
      margin-right: 10rpx;
    }

    image:last-child {
      margin-top: 2rpx;
      margin-left: 20rpx;
      width: 14rpx;
      height: 22rpx;
      // transform: rotate(180deg);
    }
  }

  .rank_goods {
    margin-top: 20rpx;
  }

  .good_item {
    width: 208rpx;
    position: relative;

    .image {
      background-position: center center;
      background-repeat: no-repeat;
      background-size: cover;
      width: 208rpx;
      height: 208rpx;
      border-radius: 6px 6px 0 0;
      background-color: #f7f7f7;
    }

    .rank_top {
      position: absolute;
      width: 42rpx;
      height: 42rpx;
      top: 10rpx;
      left: 10rpx;
      border-radius: 50%;
      color: #fff;
    }

    .text {
      padding: 14rpx;
      font-size: 22rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #333333;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      view:first-child {
        width: 200rpx;
        margin-bottom: 10rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        text-align: center;
      }
    }

    .top1 {
      background: linear-gradient(158deg, #ffe197 0%, #e8a848 100%);
    }

    .top2 {
      background: linear-gradient(134deg, #f1f1f1 0%, #a9a9af 100%);
    }

    .top3 {
      background: linear-gradient(134deg, #f4bc4b 0%, #e88949 100%);
    }
  }
}
</style>
