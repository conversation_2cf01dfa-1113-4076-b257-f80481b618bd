<template>
	<!-- 商品链接 start -->
	<view class="chat_goods_link" @click="goGoodsDetail">
		<view class="goods_links">
			<view class="goods_image">
				<image :src="msgContent.goodsImage" mode="aspectFill"></image>
			</view>
			<view class="goods_des">
				<view class="goods_name">
					{{ msgContent.goodsName }}</view>
				<view class="goods_bottom">
					<view class="goods_price">
						{{ $L('￥') }}{{ parseFloat(msgContent.goodsPrice).toFixed(2) }}
					</view>
					<view class="send_link" @click.stop="sendGoods">
						{{ $L('发送链接') }}
					</view>
				</view>
			</view>
		</view>
	</view>
	<!-- 商品链接 end -->
</template>

<script>
	export default{
		props:{
			msgContent:Object
		},
		
		methods:{
			sendGoods(){
				this.$emit('sendGoods',this.msgContent)
			},
			goGoodsDetail(val, type) {
				this.$Router.push({
					path: '/standard/product/detail',
					query: {
						productId:this.msgContent.productId
					}
				})
			},
		}
	}
</script>

<style lang="scss">
	.chat_goods_link {
		display: flex;
		justify-content: center;
		margin-top: 20rpx;
	
		.goods_links {
			width: 670rpx;
			height: 192rpx;
			background: #ffffff;
			border-radius: 6rpx;
			align-items: center;
			display: flex;
	
			.goods_image {
				margin: 0 20rpx;
	
				image {
					width: 156rpx;
					height: 156rpx;
					border-radius: 6rpx;
				}
			}
	
			.goods_des {
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				height: 192rpx;
				padding: 20rpx 0;
				box-sizing: border-box;
	
				.goods_name {
					font-size: 26rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #2d2d2d;
					line-height: 39rpx;
					width: 453rpx;
					text-overflow: -o-ellipsis-lastline;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					line-clamp: 2;
					-webkit-box-orient: vertical;
				}
	
				.goods_bottom {
					display: flex;
					justify-content: space-between;
	
					.goods_price {
						font-size: 26rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color:var(--color_price);
						line-height: 39rpx;
	
						text:nth-child(2) {
							font-size: 30rpx;
						}
					}
	
					.send_link {
						width: 141rpx;
						height: 38rpx;
						background:var(--color_price);
						border-radius: 19rpx;
						font-size: 24rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #ffffff;
						line-height: 38rpx;
						text-align: center;
					}
				}
			}
		}
	}
	// ss
</style>