<template>
	<view :style="mix_diyStyle">
		<view class="container_box">

			<view class="fixed_top_status_bar" :style="{ height: statusBarHeight + 'px' }"></view>

			<!-- wx-1-start -->
			<!-- #ifdef MP -->
			<view class="fixed_top_status_bar" :style="{ height: menuButtonTop }"></view>
			<!-- #endif -->
			<!-- wx-1-end -->
			<!-- #ifdef H5 -->
			<view class="seckill_top_wrap" :style="{ height: topHeight}" :class="{'seckill_top_wrap_seck':top_bg}">
			<!-- #endif -->
			<!-- wx-2-start -->
			<!-- #ifdef MP -->
			<view class="seckill_top_wrap" :class="{'seckill_top_wrap_seck':top_bg}" :style="{ height: topHeight,marginTop:'calc(0rpx + '+menuButtonHeights+'px)' }">
				<view class="seckill_top" :style="{
					  background: seckill_top_color,
					  marginTop: menuButtonTop,
					  height:menuButtonHeight+'px'
					}" :class="is_fixed_nav ? 'fixed_top' : ''">
					<view class="seckill_top_box_o">

						<image :src="imgUrl + 'seckill/to_back.png'" mode="aspectFit" class="to_back_icon"
							@click="toPrev">
						</image>

						<view class="seckill_title" @click="titleBack">{{ $L('限时秒杀') }}</view>
					</view>
				</view>
				<view class="seckill_title_box" :style="{height:'10rpx',background: seckill_top_color,top:menuButtonHeights+'px'}"></view>
				<!-- #endif -->
					<!-- wx-2-end -->
					<!-- app-1-start -->
















						<!-- app-1-end -->
						<view class="nav_list_wrap" v-if="activity_open&&nav_list.length > 0">
							<view class="nav_list" v-if="nav_list.length > 0">
								<view :class="current == 'all' ? 'nav_item active' : 'nav_item'"
									v-if="nav_list.length > 0" @click="changeSort('all', 0)">{{ $L('全部') }}</view>
								<view :class="current == index ? 'nav_item active' : 'nav_item'"
									v-for="(item, index) in nav_list" :key="index"
									@click="changeSort(index, item.labelId)">{{ item.labelName }}</view>
							</view>
						</view>
						<view class="swiper_wrap" v-if="activity_open && is_show_banner"
							:style="{ paddingTop: is_show_banner ? '10rpx' : '150rpx' }">
							<uniSwiperDot :dotsStyles="dotsStyles" :current="swiperCurrent" :info="swiper_list"
								field="content">
								<swiper class="carousel" circular @change="swiperChange" autoplay="true">
									<block v-for="(swiperItem, swiperIndex) in swiper_list" :key="swiperIndex">
										<block v-if="swiperItem.imgUrl">
											<swiper-item class="carousel_item" @click="skipTo(swiperItem)">
												<image :src="swiperItem.imgUrl" class="carousel_item"
													mode="aspectFit" />
											</swiper-item>
										</block>
									</block>
								</swiper>
							</uniSwiperDot>
						</view>
					</view>
					<!-- app-2-start -->







							<!-- app-2-end -->
							<!-- wx-3-start -->
							<!-- #ifdef MP -->
							<view v-if="activity_open" :style="{ marginTop:'10rpx' }">
								<view :class="
          is_fixed_nav ? 'fixed_nav seckill_time_wrap' : 'seckill_time_wrap'
        " :style="{ marginTop: swiper_list.length > 0 ? '0' : '10rpx',top:menuButtonHeights+'px' }"
									v-if="time_list.length > 0 && current == 'all'">
								<!-- #endif -->
									<!-- wx-3-end -->
									<!-- #ifdef H5 -->
									<view v-if="activity_open"
										:style="{ marginTop: is_show_banner ? '20rpx' : '10rpx' }">
										<view class="seckill_time_wrap" :class="
          is_fixed_nav ? 'fixed_nav seckill_time_wrap' : 'seckill_time_wrap'
        " :style="{ marginTop: swiper_list.length > 0 ? '0' : '0' ,top:0}"
											v-if="time_list.length > 0 && current == 'all'">
										<!-- #endif -->

											<view class="seckill_icon_wrap">
												<svgGroup type="iconmiaosha_jinrimiaosha" width="84" height="94"
													px="rpx" :color="diyStyle_var['--color_seckill_main']">
												</svgGroup>
												<svgGroup class="seckill_icon" type="iconmiaosha_jinrimiaosha_one"
													width="84" height="94" px="rpx" color="#000">
												</svgGroup>
											</view>

											<view class="seckill_time">
												<view class="seckill_time_item" v-for="(item, index) in time_list"
													:key="index" @click="changeList(index, item.stageId)">
													<view :class="
                currIndex == index ? 'active_time time_item' : 'time_item'
              ">{{ item.time }}</view>
													<view :class="
                currIndex == index ? 'active_time_text time_text' : 'time_text'
              ">
														{{ item.state == 1 ? $L('即将开始') : $L('已开抢') }}
													</view>
												</view>
											</view>
										</view>

										<view class="goods_wrap"
											:style="{ paddingTop: is_fixed_nav&&(time_list.length>0&&current == 'all') ? '160rpx' : '10rpx' }">
											<view class="goods_item" v-for="(item, index) in goods_list" :key="index"
												@click="toGoodsDetail(item.productId, item.goodsId)">
												<view class="goods_img_wrap">
													<image :src="item.mainImage" mode="aspectFit" class="goods_img">
													</image>
												</view>

												<view class="goods_item_wrap">
													<view class="goods_name">{{ item.goodsName }}</view>
													<view class="goods_bottom_wrap">
														<view class="goods_bottom_left">
															<view class="goods_price">
																<text class="small_price">{{ $L('￥') }}</text>
																<text class="big_price">{{
                    $getPartNumber(item.seckillPrice, 'int')
                  }}</text>
																<text class="small_price">{{
                    $getPartNumber(item.seckillPrice, 'decimal')
                  }}</text>
															</view>
															<view class="old_price">
																{{ $L('￥') }}{{ $getPartNumber(item.productPrice, 'int')
                  }}{{ $getPartNumber(item.productPrice, 'decimal') }}
															</view>
														</view>
														<!-- 进行中去抢购 -->
														<view class="goods_bottom_right_box"
															v-if="item.state == 2 && item.secKillProgress != '100%'">
															<view class="goods_bottom_right_one">

															</view>
															<view class="goods_bottom_right_three">

															</view>
															<view class="goods_bottom_right"
																v-if="item.state == 2 && item.secKillProgress != '100%'">
																<text class="to_buy">{{ $L('去抢购') }}</text>
																<view class="progress-box">
																	<progress
																		:percent="$formatPercent(item.secKillProgress)"
																		stroke-width="3" backgroundColor="rgba(0,0,0,0)"
																		border-radius="5" activeColor="#fff" />
																</view>
																<text
																	class="have_buy_percent">{{ $L('已抢') }}{{ item.secKillProgress }}</text>
															</view>
														</view>
														<!-- 进行中已抢完 -->
														<view class="sold_out_wrap"
															v-if="item.state == 2 && item.secKillProgress == '100%'">
															<view class="sold_out_btn" @click.stop="haveSoldOut">{{
                  $L('已抢完')
                }}</view>
															<view class="sold_out_num">
																{{ item.buyQuantity }}{{ $L('件已抢完') }}</view>
														</view>
														<!-- 即将开始设置/取消提醒 -->
														<view class="remind_wrap" v-if="item.state == 1">
															<view class="set_remind_btn remind_btn"
																v-if="!item.isRemind"
																@click.stop="setRemind(item.stageProductId)">
																{{ $L('设置提醒') }}</view>
															<view class="cancel_remind_btn remind_btn" v-else
																@click.stop="setRemind(item.stageProductId)">
																{{ $L('取消提醒') }}</view>
														</view>
													</view>
												</view>
											</view>
											<view class="is_more" v-if="goods_list.length > 0">{{
          hasmore ? $L('数据加载中...') : $L('数据加载完毕~')
        }}</view>
										</view>

										<!-- 无秒杀空页面 -->
										<view class="empty_page" v-if="is_show_empty">
											<image :src="imgUrl + 'empty_goods.png'" mode="aspectFit" class="empty_img">
											</image>
											<view class="empty_text">{{ $L('今日无秒杀') }}</view>
										</view>
									</view>
									<loginPop ref="loginPop"></loginPop>
									<notOpen v-if="!activity_open"></notOpen>
								</view>
							</view>
</template>

<script>
	import {
		mapState
	} from 'vuex'
	import notOpen from '../../components/not_open.vue'
	import uniSwiperDot from '@/components/uni-swiper-dot/uni-swiper-dot.vue'
	export default {
		data() {
			return {
				imgUrl: process.env.VUE_APP_IMG_URL,
				top_bg: '',
				top_bg2: process.env.VUE_APP_IMG_URL + 'seckill/top_bg2.png',
				purchase_url: process.env.VUE_APP_IMG_URL + 'seckill/purchase_bg.png',
				nav_list: [],
				swiper_list: [],
				time_list: [],
				goods_list: [],
				current: 'all',
				swiperCurrent: 0,
				currIndex: 0,
				label_id: 0, //分类id
				stage_id: '', //场次id
				pn: 1,
				has_more: true,
				seckillId: '',
				is_show_banner: true,
				is_show_empty: false,
				banner_height: '358rpx',
				is_fixed_nav: false, //是否固定场次
				if_hide: false,
				scroll_top: 0,
				activity_open: false,
				height: '160rpx',
				// statusBarHeight:0,
				seckill_top_color: 'var(--color_seckill_main)',
				//wx-7-start
				// #ifdef MP
				menuButtonHeight: uni.getMenuButtonBoundingClientRect().height,
				menuButtonTop: uni.getMenuButtonBoundingClientRect().top + 'px',
				menuButtonHeights: uni.getMenuButtonBoundingClientRect().height + uni.getMenuButtonBoundingClientRect()
					.top,
				// #endif
				//wx-7-end
				dotsStyles: {
					selectedBackgroundColor: '#fff',
					width: 6,
					height: 6,
					selectedBorder: 'none',
					backgroundColor: 'rgba(0, 0, 0, .2)',
					border: 'none',
					bottom: 8,
					seckill_height: 0,
				},
				seckill_time_height: 0,
				seckill_time_height_flag: false,
			}
		},
		components: {
			notOpen,
			uniSwiperDot
		},
		computed: {
			...mapState(['hasLogin', 'userInfo']),
			statusBarHeight() {






				var res = uni.getSystemInfoSync()
				return res.statusBarHeight

			},

			topHeight() {
				if (this.swiper_list.length) {
					return '358rpx'
				}
				if (this.nav_list.length == 0) {
					return 'auto'
				} else {
					return 'auto'
				}
			}
		},
		onLoad(option) {
			this.ifOpen()
			this.seckillId = this.$Route.query.seckillId





		},
		mounted() {

		},
		onShow() {
			if (this.if_hide && this.activity_open) {
				if (this.stage_id) {
					this.getActivityGoods()
				}
			}
		},
		onHide() {
			this.if_hide = true
		},
		onPageScroll(e) {
			this.scroll_top = e.scrollTop
			if (this.scroll_top > 0) {
				this.seckill_top_color = this.diyStyle_var['--color_seckill_main_bg']
			} else {
				this.seckill_top_color = this.diyStyle_var['--color_seckill_main_bg']
			}

			if (this.is_show_banner) {
				//有轮播图
				// #ifdef H5
				let that = this
				if (!this.seckill_time_height_flag) {
					let querys = wx.createSelectorQuery().in(this)
					querys.select('.seckill_time_wrap').boundingClientRect(data => {
						if (data) {
							that.seckill_time_height = data.top
						} else {
							that.seckill_time_height = 0
						}
					}).exec();
					this.seckill_time_height_flag = true
				}
				if (e.scrollTop > this.seckill_time_height) {
					this.top_bg = ''
					this.is_fixed_nav = true
				} else {
					;
					(this.top_bg = this.is_show_banner ?
						1 :
						''),
					(this.is_fixed_nav = false)
				}
				// #endif
				// #ifndef H5
				if (e.scrollTop > 179) {
					this.top_bg = ''
					this.is_fixed_nav = true
				} else {
					;
					(this.top_bg = this.is_show_banner ?
						1 :
						''),
					(this.is_fixed_nav = false)
				}
				// #endif
			} else {
				//没有轮播图
				if (e.scrollTop > 40) {
					this.is_fixed_nav = true
				} else {
					this.is_fixed_nav = false
				}
			}
		},
		onReachBottom() {
			if (this.hasmore) {
				this.getActivityGoods()
			}
		},
		methods: {
			// 判断活动是否开启
			ifOpen() {
				let param = {}
				param.data = {}
				param.data.names = 'seckill_is_enable'
				param.url = 'v3/system/front/setting/getSettings'
				param.method = 'GET'
				this.$request(param).then((res) => {
					if (res.state == 200) {
						if (res.data[0] == '1') {
							this.activity_open = true
							this.getSeckillList()
							this.getBannerList()
						} else {
							this.activity_open = false
						}
					}
				})
			},

			titleBack() {
				//wx-8-start
				// #ifdef MP
				this.$Router.back(1)
				// #endif
				//wx-8-end
			},


			toPrev() {
				// #ifdef H5
				const pages = getCurrentPages()
				if (pages.length > 1) {
					this.$Router.back(1)
					return
				}
				//使用vue-router返回上一级
				let a = this.$Router.back(1)
				if (a == undefined) {
					//重新定向跳转页面
					this.$Router.replaceAll('/pages/index/index')
				}
				return
				// #endif
				this.$Router.back(1)
			},
			//获取轮播图
			getBannerList() {
				let _this = this
				this.$request({
					url: 'v3/promotion/front/seckill/banner',
					method: 'GET',
					data: {
						seckillId: _this.seckillId
					}
				}).then((res) => {
					if (res.state == 200) {
						var newList = []
						if (res.data.banner) {
							_this.swiper_list = JSON.parse(res.data.banner)
							_this.swiper_list.map((item) => {
								if (item.imgUrl) {
									newList.push(item)
								}
							})
						}
						_this.swiper_list = newList
						if (_this.swiper_list.length == 0) {
							_this.top_bg = ''
							_this.is_show_banner = false
						} else {
							_this.top_bg = 1
							_this.is_show_banner = true
						}

						// _this.getSessionList()
					} else if (res.state == 255) {
						_this.is_show_empty = true
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					}
				})
			},

			// 获取顶部分类
			getSeckillList() {
				let _this = this
				this.$request({
					url: 'v3/promotion/front/seckill/getSeckillLabel',
					method: 'GET',
					data: {
						seckillId: _this.seckillId
					}
				}).then((res) => {
					if (res.state == 200) {
						let result = res.data.list
						if (result.length > 0) {
							_this.nav_list = result
							// _this.label_id = _this.label_id == ''?_this.nav_list[0].labelId:_this.label_id
							_this.currIndex = 0
						} else {
							_this.is_show_empty = true
						}
						_this.getSessionList()
					} else if (res.state == 255) {
						_this.is_show_empty = true
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					}
				})
			},
			// 获取场次列表
			getSessionList() {
				let _this = this
				this.$request({
					url: 'v3/promotion/front/seckill/getSeckillStage',
					method: 'GET',
					data: {
						seckillId: _this.seckillId
					}
				}).then((res) => {
					if (res.state == 200) {
						let result = res.data.list
						if (result != null && result.length > 0) {
							_this.time_list = result
							_this.stage_id = _this.time_list[_this.currIndex].stageId
							this.getActivityGoods()
						} else {
							_this.is_show_empty = true
						}

					} else if (res.state == 255) {
						_this.is_show_empty = true
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					}
				})
			},
			// 获取商品列表activitySession
			getActivityGoods() {
				uni.showLoading()
				let _this = this
				var param = {}
				param.labelId = _this.label_id
				if (_this.current == 'all') {
					param.stageId = _this.stage_id
				}
				param.seckillId = _this.seckillId
				this.$request({
					url: 'v3/promotion/front/seckill/goodsList',
					data: param,
					method: 'GET'
				}).then((res) => {
					if (res.state == 200) {
						let result = res.data
						if (_this.current == 'all') {
							_this.is_show_empty = _this.time_list.length == 0 ? true : false
						} else {
							_this.is_show_empty = result.list.length == 0 ? true : false
						}

						if (_this.pn == 1) {
							_this.goods_list = result.list
						} else {
							_this.goods_list = _this.goods_list.concat(result.list)
						}
						let page = result.pagination
						if (page.current < Math.ceil(page.total / page.pageSize)) {
							_this.hasmore = true
						} else {
							_this.hasmore = false
						}
						if (_this.hasmore) {
							_this.pn++
						}
						uni.hideLoading()
					} else {
						uni.hideLoading()
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					}
				})
			},
			// 切换分类
			async changeSort(index, id) {
				if (this.current != index) {
					this.current = index
					this.label_id = id
					this.currIndex = 0
					this.time_list = []
					this.goods_list = []
					await this.getSessionList()
				}
			},
			// 切换场次
			async changeList(index, id) {
				this.currIndex = index
				this.stage_id = id
				await this.getActivityGoods()
			},
			swiperChange(e) {
				this.swiperCurrent = e.detail.current
			},
			// 设置/取消提醒
			setRemind(stageProductId) {
				if (this.hasLogin) {
					let param = {
						stageProductId
					}
					let _this = this
					this.$request({
						url: 'v3/promotion/front/seckill/isRemind',
						method: 'POST',
						data: param
					}).then((res) => {
						if (res.state == 200) {
							uni.showToast({
								title: res.msg
							})
							setTimeout(() => {
								_this.getActivityGoods()
							}, 1500)
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					})
				} else {
					let url = this.$Route.path
					const query = this.$Route.query
					uni.setStorageSync('fromurl', {
						url,
						query
					})
					this.$Router.push('/pages/public/login')
				}
			},
			toGoodsDetail(productId, goodsId) {
				this.$Router.push({
					path: '/standard/product/detail',
					query: {
						productId,
						goodsId
					}
				})
			},
			// 已抢完
			haveSoldOut() {
				uni.showToast({
					title: this.$L('该商品已抢完，去看看别的吧！'),
					icon: 'none'
				})
			},
			// 相关跳转
			skipTo(swiperitem) {
				if (swiperitem.link_type == 'url') {
					//跳转链接地址
					// #ifdef H5
					window.open(swiperitem.link_value)
					// #endif

					//app-3-start



					//app-3-end
					//wx-9-start
					// #ifdef MP
					this.$Router.push({
						path: '/pages/index/skip_to',
						query: {
							url: swiperitem.link_value
						}
					})
					// #endif
					//wx-9-end
				} else if (swiperitem.link_type == 'goods') {
					//跳转商品详情页
					this.$Router.push({
						path: '/standard/product/detail',
						query: {
							productId: swiperitem.info.defaultProductId,
							goodsId: swiperitem.info.goodsId
						}
					})
				} else if (swiperitem.link_type == 'category') {
					// 分类列表
					this.$Router.push({
						path: '/standard/product/list',
						query: {
							categoryId: swiperitem.info.categoryId
						}
					})
				} else if (swiperitem.link_type == 'keyword') {
					// 关键词
					this.$Router.push({
						path: '/standard/product/list',
						query: {
							keyword: swiperitem.link_value,
							source: 'search'
						}
					})
				} else if (swiperitem.link_type == 'topic') {
					//跳转专题页
					this.$Router.push({
						path: '/pages/index/topic',
						query: {
							id: swiperitem.info.decoId
						}
					})
				} else if (swiperitem.link_type == 'o2o_topic') {
					//跳转专题页
					this.$Router.push({
						path: '/pages/index/topic',
						query: {
							id: swiperitem.info.decoId,
							type: 'o2o_topic'
						}
					})
				} else if (swiperitem.link_type == 'brand_home') {
					//品牌列表
					this.$Router.push('/pages/public/brand')
				} else if (swiperitem.link_type == 'seckill') {
					//秒杀
					this.$Router.push({
						path: '/standard/seckill/seckill',
						query: {
							seckillId: swiperitem.info.seckillId
						}
					})
				} else if (swiperitem.link_type == 'voucher_center') {
					//优惠券领券中心
					this.$Router.push('/standard/coupon/couponCenter')
				} else if (swiperitem.link_type == 'spreader_center') {
					//推手中心
					if (!this.hasLogin) {
						this.$refs.loginPop.openLogin('no_replace')
					} else {
						this.$Router.push('/extra/tshou/index/index')
					}
				} else if (swiperitem.link_type == 'sign_center') {
					if (!this.hasLogin) {
						this.$refs.loginPop.openLogin('no_replace')
					} else {
						this.$Router.push('/standard/signIn/signIn')
					}
				} else if (swiperitem.link_type == 'draw') {
					if (!this.hasLogin) {
						this.$refs.loginPop.openLogin('no_replace')
					} else {
						this.$Router.push({
							path: '/standard/lottery/detail',
							query: {
								drawId: swiperitem.info.drawId
							}
						})
					}
				} else {
					//本页面未声明的操坐类型
					this.$diyNavTo(swiperitem)
				}
			}
		}
	}
</script>
<style lang="scss" scoped>
	::v-deep.uni-swiper-wrapper {
		border-radius: 5px !important;
	}
</style>
<style lang="scss">
	.fixed_top_status_bar {
		position: fixed;
		/* #ifdef APP-PLUS||MP */
		height: var(--status-bar-height);
		/* #endif */
		/* #ifdef H5||MP-ALIPAY */
		height: 0;
		/* #endif */
		top: 0;
		left: 0;
		right: 0;
		z-index: 999999;
		background: #fff;
		/* #ifdef APP-PLUS||MP */
		background: var(--color_seckill_main);
		/* #endif */
	}

	.uni-swiper__dots-box {
		border-radius: 5px !important;
	}

	page {
		background-color: #f5f5f5;
	}

	.container_box {
		// padding-top: var(--status-bar-height);
		//


		width: 750rpx;
		margin: 0 auto;

		.seckill_top_wrap {
			width: 750rpx;
			// height: 358rpx;
			background-size: 100% 100%;
			background: var(--color_seckill_main);

			.seckill_top {
				z-index: 999999;
				width: 750rpx;
				height: 88rpx;
				display: flex;
				justify-content: center;
				//wx-4-start
				/* #ifdef MP */
				justify-content: flex-start;
				/* #endif */
				//wx-4-end
				align-items: center;
				position: fixed;
				top: 0;

				.seckill_top_box_o {
					display: flex;
					justify-content: center;
					//wx-5-start
					/* #ifdef MP */
					justify-content: flex-start;
					/* #endif */
					//wx-5-end
					align-items: center;
					position: relative;
				}

				.to_back_icon {
					width: 17rpx;
					height: 29rpx;
					position: absolute;
					left: 20rpx;
					margin-top: 6rpx;
				}

				.seckill_title {
					font-size: 36rpx;
					color: #fff;
					//wx-6-start
					/* #ifdef MP */
					margin-left: 60rpx;
					/* #endif */
					//wx-6-end
				}
			}

			.fixed_top {
				position: fixed;
				top: 0;
				left: 0;
				z-index: 9;
			}

			.nav_list_wrap {
				width: 100%;
				box-sizing: border-box;
				padding: 10rpx 0;
				margin-right: 20rpx;
				// padding-top: 88rpx;

				.nav_list {
					height: 50rpx;
					display: flex;
					align-items: center;
					overflow-x: scroll;
					padding-left: 20rpx;
					box-sizing: border-box;
					margin-right: 20rpx;

					.nav_item {
						white-space: nowrap;
						font-size: 32rpx;
						color: #fff;
						margin-right: 53rpx;
					}
				}
			}

			.swiper_wrap {
				padding: 10rpx 20rpx 0 20rpx;
				height: 300rpx;
				/* #ifdef H5 */
				padding-top: 20rpx !important;
				height: 310rpx;

				/* #endif */
				.carousel {
					width: 100%;
					height: 300rpx;
					border-radius: 5px;

					.carousel_item {
						width: 100%;
						height: 300rpx;
						border-radius: 5px;
					}
				}
			}
		}

		.seckill_top_wrap_seck {
			background: var(--color_seckill_main_bg_zero);
		}

		.seckill_time_wrap {
			width: 750rpx;
			padding: 0 20rpx;
			height: 137rpx;
			display: flex;
			align-items: center;
			margin-top: 82rpx;
			box-sizing: border-box;

			.seckill_icon_wrap {
				margin-right: 25rpx;
				position: relative;

				.seckill_icon {
					position: absolute;
					left: 2rpx;
					top: 0rpx;
				}
			}

			.seckill_time {
				width: 100%;
				display: flex;
				overflow-x: scroll;

				.seckill_time_item {
					margin-right: 40rpx;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;

					.time_item {
						font-size: 32rpx;
						color: #333;
						font-weight: bold;
					}

					.time_text {
						font-size: 24rpx;
						color: #666;
						white-space: nowrap;
						padding: 5rpx 10rpx;
						line-height: 24rpx;
					}
				}
			}

			.seckill_time view:last-child {
				margin-right: 0;
			}
		}

		.goods_wrap {
			width: 750rpx;
			padding: 0 20rpx;
			box-sizing: border-box;

			.goods_item {
				width: 100%;
				height: 310rpx;
				background-color: #fff;
				border-radius: 15rpx;
				display: flex;
				padding: 20rpx;
				box-sizing: border-box;
				margin-bottom: 20rpx;

				.goods_img_wrap {
					width: 270rpx;
					height: 270rpx;
					border-radius: 15rpx;
					margin-right: 20rpx;
					background-color: #f8f8f8;

					.goods_img {
						width: 270rpx;
						height: 270rpx;
						border-radius: 15rpx;
					}
				}

				.goods_item_wrap {
					width: 100%;
					position: relative;

					.goods_name {
						font-size: 28rpx;
						color: #2d2d2d;
						font-weight: 600;
						overflow: hidden;
						text-overflow: ellipsis;
						display: -webkit-box;
						-webkit-line-clamp: 2;
						-webkit-box-orient: vertical;
						word-break: break-word;
						margin-top: 20rpx;
					}

					.goods_bottom_wrap {
						width: 100%;
						position: absolute;
						bottom: 0;
						left: 0;
						display: flex;
						justify-content: space-between;

						.goods_bottom_left {
							padding-top: 10rpx;
							box-sizing: border-box;

							.goods_price {
								color: var(--color_seckill_main);
								font-weight: bold;

								.small_price {
									font-size: 24rpx;
								}

								.big_price {
									font-size: 34rpx;
								}
							}

							.old_price {
								font-size: 20rpx;
								color: #999;
								text-decoration: line-through;
							}
						}

						.sold_out_wrap {
							.sold_out_btn {
								width: 138rpx;
								height: 50rpx;
								font-size: 26rpx;
								display: flex;
								justify-content: center;
								align-items: center;
								color: #fff;
								background: #999999;
								border-radius: 25rpx;
							}

							.sold_out_num {
								font-size: 22rpx;
								color: #666;
								margin-top: 14rpx;
								text-align: center;
							}
						}

						.goods_bottom_right_box {
							position: relative;
							display: flex;
							margin-right: 20rpx;

							.goods_bottom_right_one,
							.goods_bottom_right_three {
								width: 0px;
								height: 0px;
								border-top: 50rpx solid rgba(0, 0, 0, 0);
								border-right: 50rpx solid rgba(0, 0, 0, 0);
								border-bottom: 86rpx solid var(--color_seckill_main);
								border-left: 16rpx solid rgba(0, 0, 0, 0);
							}

							.goods_bottom_right_three {
								border-right: 16rpx solid rgba(0, 0, 0, 0);
								border-left: 50rpx solid rgba(0, 0, 0, 0);
							}

							.goods_bottom_right_one {
								position: absolute;
								left: -16rpx;
								top: -50rpx;

							}

							.goods_bottom_right_three {
								position: absolute;
								right: -16rpx;
								top: -50rpx;
							}
						}

						.goods_bottom_right {
							width: 129rpx;
							height: 86rpx;
							display: flex;
							flex-direction: column;
							align-items: center;
							background-size: 100% 100%;
							background: var(--color_seckill_main);

							.to_buy {
								font-size: 26rpx;
								color: #fff;
								margin: 6rpx 0;
							}

							.progress-box {
								width: 90rpx;
							}

							.have_buy_percent {
								font-size: 20rpx;
								color: #fff;
								transform: scale(0.8);
								font-weight: 400;
							}
						}

						.remind_wrap {
							height: 86rpx;
							display: flex;
							align-items: flex-end;

							.set_remind_btn {
								color: #fff;
								background: var(--color_seckill_main);
							}

							.cancel_remind_btn {
								color: #333;
								border: 1rpx solid #a6a6a6;
								box-sizing: border-box;
							}

							.remind_btn {
								width: 138rpx;
								height: 50rpx;
								font-size: 26rpx;
								display: flex;
								justify-content: center;
								align-items: center;
								border-radius: 25rpx;
							}
						}
					}
				}
			}
		}
	}

	.active {
		font-size: 32rpx !important;
		font-weight: bold;
	}

	.active_time {
		font-size: 32rpx;
		font-weight: bold;
		color: var(--color_seckill_main) !important;
	}

	.active_time_text {
		padding: 2rpx 10rpx;
		background-color: var(--color_seckill_main);
		color: #fff !important;
		border-radius: 17rpx;
	}

	.progress-box ::v-deep .uni-progress ::v-deep .uni-progress-bar {
		border: 1rpx solid #fff;
		height: 4rpx !important;
		border-radius: 3rpx;
	}

	.empty_page {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		margin-top: 200rpx;

		.empty_img {
			width: 380rpx;
			height: 280rpx;
			margin-bottom: 32rpx;
		}

		.empty_text {
			font-size: 26rpx;
			color: #666;
		}
	}

	/* 加载更多，没有更多 */
	.is_more {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 22rpx;
		color: #999999;
		line-height: 22rpx;
		padding: 10rpx 0 30rpx;
		background: #f5f5f5;
	}

	.fixed_nav {
		position: fixed;
		top: 78rpx;
		//app-4-start



		//app-4-end
		left: 50%;
		transform: translateX(-50%);
		z-index: 999;
		background-color: #f5f5f5;
	}

	.seckill_title_box {
		position: fixed;
		width: 750rpx;
		z-index: 999999;
	}
</style>