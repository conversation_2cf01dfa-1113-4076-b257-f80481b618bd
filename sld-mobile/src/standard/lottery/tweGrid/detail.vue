<template>
	<view :style="mix_diyStyle">
  <view
    class="grid_main" :style="{ backgroundImage: 'url(' + backgroundImg + ')', minHeight: screenHeight + 'px'}">
    <view class="grid_main_con">
     
      <view class="grid_title bg_style">
        <!-- :style="{backgroundImage:'url('+imgUrl+'grid_title.png)'}" -->
      </view>
	  <view class="right_intro">
	    <view class="right_item" @click="open">{{ $L('活动详情') }}</view>
	  </view>
      <view :style="{ marginTop: '-20rpx', marginBottom: '30rpx' }">
        <twgTable
          @res="res"
          ref="twgTable"
          :userInt="myInt"
          :drawId="drawId"
        ></twgTable>
      </view>
      <view
        :class="[
          'self_info',
          lotDetail.integralUse > 0
            ? 'flex_row_between_center'
            : 'flex_row_center_center'
        ]"
      >
        <view class="info_btn" v-if="lotDetail.integralUse > 0"
          >{{ $L('我的积分') }}：{{ myInt.integral }}{{ $L('积分') }}</view
        >
        <view class="info_btn" @click="toRec">{{ $L('我的中奖记录') }}</view>
      </view>

      <view
        class="grid_bot"
        v-if="reactAcDetail.acState && reactAcDetail.acState.flag == 1"
      >
        <view
          class="grid_bot_con bg_style_cover"
          :style="{ backgroundImage: 'url(' + imgUrl + 'card_print_bg.png)' }"
        >
          <view class="roll_in_list flex_column_between_center">
            <view class="roll_title">{{ $L('中奖名单') }}</view>
            <view class="roll_swiper flex_row_center_center">
              <swiper
                :indicator-dots="false"
                :autoplay="true"
                :interval="5000"
                :duration="1000"
                :disable-touch="true"
                :vertical="true"
                circular
                @change="change"
                display-multiple-items="3"
              >
                <swiper-item v-for="(item, index) in lotList" :key="index">
                  <view
                    :class="{ roll_swiper_item: true, sel: index == current }"
                    >{{ $L('恭喜') }} {{ item.memberNameHide }} {{ $L('获得')
                    }}{{ item.prizeName }}</view
                  >
                </swiper-item>

                <block v-if="3 - lotList.length > 0">
                  <swiper-item v-for="i in 3 - lotList.length" :key="i"> </swiper-item>
                </block>
              </swiper>
            </view>
          </view>
        </view>
      </view>
    </view>

    <popInfo ref="popInfo" @revive="revive"></popInfo>
  </view>
	</view>
</template>

<script>
import { arrCom } from '@/utils/common.js'
import twgTable from './twgTable.vue'
import popInfo from '../component/popInfo.vue'
export default {
  components: {
    popInfo,

    twgTable
  },
  data() {
    return {
      imgUrl: process.env.VUE_APP_IMG_URL + 'lottery/',
      current: 0,
      lotList: [],
      lotResult: {},
      myInt: {},
      screenHeight: 0,
      timer: null,
      isCreated: false,
      disMutiItem: 0
    }
  },
  props: {
    lotDetail: {
      type: Object
    },
    userInt: {
      type: Object
    },
    drawId: {
      type: Number
    }
  },
  inject: ['getAcDetail'],
  computed: {
    reactAcDetail() {
      return this.isCreated ? this.getAcDetail() : {}
    },
    backgroundImg() {
      let { backgroundImageUrl } = this.lotDetail

      return backgroundImageUrl
        ? backgroundImageUrl
        : this.imgUrl + 'grid_lotte_bg.png'
    }
  },
  created() {
    this.isCreated = true
    this.getlotRecAsync()
    this.getlotInt()
    this.screenHeight = uni.getSystemInfoSync().windowHeight
  },
  beforeDestroy() {
    clearInterval(this.timer)
  },
  methods: {
    change(e) {
      this.current = e.detail.current + 1
    },
    open() {
      this.$refs.popInfo.open({}, 'acDetail')
    },
    openShare() {},

    res(seq, lotResult) {
      if (this.lotDetail.integralUse > 0)
        this.myInt.integral -= this.lotDetail.integralUse
      if (this.myInt.remainNum > 0) this.myInt.remainNum -= 1
      if (lotResult.prizeType == 1)
        this.myInt.integral += parseInt(lotResult.description)
      setTimeout(() => {
        this.$refs.popInfo.open(
          lotResult,
          lotResult.isPrize == 1 ? 'roll_on' : 'roll_off'
        )
      }, 500)
    },
    revive() {
      this.$refs.twgTable.rollOn = false
      this.$refs.twgTable.countDownM = -1
    },

    async getlotRecAsync() {
      if (this.reactAcDetail.acState.flag != 1) {
        return
      }

      this.lotList = await this.getlotRec()
      this.timer = setInterval(async () => {
        let res = await this.getlotRec()
        if (res.length) {
          this.lotList.push(...arrCom(this.lotList, res, 'winId'))
          this.disMutiItem = this.lotList.length > 3 ? 3 : this.lotList.length
        }
      }, 6000)
    },

    getlotRec() {
      return new Promise((resolve) => {
        this.$request({
          url: 'v3/promotion/front/draw/winList',
          data: {
            drawId: this.drawId
          }
        }).then((res) => {
          if ((res.state = 200)) {
            resolve(res.data)
          }
        })
      })
    },
    getlotInt() {
      this.$request({
        url: 'v3/promotion/front/draw/integral',
        data: {
          drawId: this.drawId
        }
      }).then((res) => {
        if ((res.state = 200)) {
          this.myInt = res.data
        }
      })
    },

    toRec() {
      if (this.$refs.twgTable.rollOn) {
        this.$api.msg(this.$L('正在抽奖中，请抽奖完成后操作'))
        return
      }

      this.$Router.push({
        path: '/standard/lottery/lotRec',
        query: { drawId: this.lotDetail.drawId }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
uni-swiper {
  display: block;
  width: 636rpx;
  height: 150rpx;
}
uni-swiper-item {
  display: flex;
  align-items: center;
  justify-content: center;
}
//wx-1-start
/* #ifdef MP */
swiper {
  width: 636rpx !important;
  height: 150rpx !important;
}
swiper-item {
  display: flex;
  align-items: center;
  justify-content: center;
}
/* #endif */
//wx-1-end
.bg_style {
  background-position: center center;
  background-repeat: no-repeat;
  background-size: contain;
}

.bg_style_cover {
  background-position: center center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.grid_main {
  margin: 0;
  //app-1-start



  //app-1-end

  padding-top: 90rpx;

  padding-bottom: 20rpx;
  background-position: top;
  background-repeat: no-repeat;
  background-size: cover;

  .grid_main_con {
    

    margin-top: calc(22rpx + var(--status-bar-height));

    position: relative;
  }

  .scra_st {
    margin-bottom: 32rpx;

    view {
      min-width: 320rpx;
      height: 50rpx;
      background: linear-gradient(
        90deg,
        #ffe155 0%,
        #f4e878 0%,
        #fff7b7 52%,
        #f3e578 100%
      );
      box-shadow: 0px 6px 3px 0px rgba(145, 41, 1, 0.41);
      text-align: center;
      line-height: 50rpx;
      border-radius: 25px;
      font-size: 30rpx;
      font-family: SourceHanSansCN;
      font-weight: 600;
      color: #d00908;
    }
  }

  .grid_table {
    margin-top: 10rpx;

    .grid_table_con {
      height: 714rpx;
      position: relative;
      padding: 20rpx;

      .grid_times {
        font-size: 28rpx;
        font-family: SourceHanSansCN;
        font-weight: bold;
        color: #bf4001;
        text-align: center;
        height: 70rpx;
        line-height: 70rpx;
      }

      .grid_desc {
        position: absolute;
        bottom: 30rpx;
        font-size: 22rpx;
        font-family: SourceHanSansCN;
        color: #bf4001;
        text-align: center;
        height: 50rpx;
        line-height: 50rpx;
      }
    }
  }

  .self_info {
    padding: 0 70rpx;

    .info_btn {
      font-size: 24rpx;
      font-family: SourceHanSansCN;
      font-weight: 600;
      color: #a95e01;
      background: #fede99;
      border-radius: 25px;
      padding: 10rpx 20rpx;
    }
  }

  .grid_title {
    margin-top: -20rpx;
    height: 277rpx;
  }

  .right_intro {
    display: flex;
    justify-content: flex-end;

    .right_item {
      width: 141rpx;
      height: 50rpx;
      background: #8d0d0c;
      border-radius: 25rpx 0 0 25rpx;
      text-align: center;
      line-height: 50rpx;
      font-size: 26rpx;
      font-family: SourceHanSansCN;
      font-weight: 600;
      color: #ffffff;
    }
  }

  .grid_bot {
    padding: 0 50rpx;
    margin-top: 30rpx;
    .grid_bot_con {
      height: 250rpx;
      padding: 20rpx 0;
      position: relative;
    }

    .bot_instruct {
      padding: 0 72rpx;

      view:first-child {
        text-align: center;
        font-size: 26rpx;
        font-family: SourceHanSansCN;
        font-weight: bold;
        color: #ab0100;
      }

      view:last-child {
        margin-top: 6rpx;
        font-size: 18rpx;
        font-family: SourceHanSansCN;
        font-weight: 400;
        color: #d13912;
      }
    }

    .roll_in_list {
      padding-top: 10rpx;
      width: 100%;

      .roll_title {
        letter-spacing: 6px;
        text-align: center;
        font-size: 28rpx;
        font-family: zcoolqingkehuangyouti;
        font-weight: 600;
        color: #b31111;
        line-height: 32rpx;
      }

      .roll_swiper {
        margin-top: 20rpx;
        width: 550rpx;
      }

      .roll_swiper_item {
        text-align: center;
        font-size: 22rpx;
        font-family: SourceHanSansCN;
        font-weight: 400;
        color: #666666;

        &.sel {
          color: #b2260c;
          font-weight: 600;
          font-size: 24rpx;
        }
      }
    }
  }
}
</style>
