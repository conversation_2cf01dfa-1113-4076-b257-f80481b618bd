<template>
	<view :style="mix_diyStyle">
  <view class="lotRec">
    <view class="lot_main">
      <view
        class="lot_item flex_row_start_center"
        v-for="(item, index) in recList" :key="index"
      >
        <image
          :src="
            imgUrl + (item.prizeType == 1 ? 'lotRec_int.png' : 'lotRec_cp.png')
          "
          mode="aspectFit"
        ></image>
        <view class="item_mi flex_row_between_center">
          <view class="item_mi_right flex_column_between_start">
            <view class="">{{ item.description }}</view>
            <view class="">{{ item.recordTime }}</view>
          </view>
          <view class="item_mi_left" @click="toDetail(item)">
            {{ $L('查看') }}
          </view>
        </view>
      </view>
      <loadingState :state="loadingState" v-if="recList.length"></loadingState>
    </view>
    <view class="activity_not_open" v-if="recList.length == 0">
      <view class="empty_sort_page">
        <image
          :src="imgUrl2 + 'empty_goods.png'"
          mode="aspectFit"
          class="empty_img"
        >
        </image>
        <view class="empty_text">{{ $L('暂无数据') }}</view>
      </view>
    </view>
  </view>
	</view>
</template>

<script>
import loadingState from '@/components/loading-state.vue'
export default {
  data() {
    return {
      imgUrl: process.env.VUE_APP_IMG_URL + 'lottery/',
      imgUrl2: process.env.VUE_APP_IMG_URL,
      current: 1,
      curDrawId: 0,
      current: 1,
      recList: [],
      hasmore: true,
      loadingState: 'first_loading'
    }
  },

  components: {
    loadingState
  },
  onLoad() {
    setTimeout(()=>{
       uni.setNavigationBarTitle({
          title: this.$L('我的中奖记录')
        })   
    },0);
    
    this.curDrawId = this.$Route.query.drawId
    this.getRecListMy()
  },

  onReachBottom() {
    if (this.hasmore) {
      this.getRecListMy()
    }
  },

  methods: {
    getRecListMy() {
      let { curDrawId } = this
      this.$request({
        url: 'v3/promotion/front/draw/drawRecordList',
        data: {
          drawId: curDrawId,
          isMyPrize: true,
          current: this.current
        }
      }).then((res) => {
        if (res.state == 200) {
          if (this.current == 1) {
            this.recList = res.data.list
          } else {
            this.recList = this.recList.concat(res.data.list)
          }

          if (this.$checkPaginationHasMore(res.data.pagination)) {
            this.loadingState = 'allow_loading_more'
            this.hasmore = true
            this.current++
          } else {
            this.loadingState = 'no_more_data'
            this.hasmore = false
          }
        }
      })
    },
    toDetail(item) {
      if (item.prizeType == 1) {
        this.$Router.push('/pages/user/myIntegral')
      } else {
        this.$Router.push('/standard/coupon/myCoupon')
      }
    }
  }
}
</script>

<style lang="scss">
page {
  background: #f5f5f5;
}
.lot_main {
  margin-top: 20rpx;
  padding: 0 20rpx;
  background: #fff;
  .lot_item {
    padding: 20rpx 0;
    border-bottom: 1px solid #eeeeee;
    image {
      margin-right: 30rpx;
      width: 97rpx;
      height: 97rpx;
    }
    .item_mi {
      width: 81%;
      height: 90rpx;
      .item_mi_right {
        height: 100%;
        view:first-child {
          font-size: 30rpx;
          font-family: PingFang;
          font-weight: bold;
          color: #db991a;
        }
        view:last-child {
          font-size: 24rpx;
          font-family: PingFang;
          font-weight: 500;
          color: #666666;
          opacity: 0.8;
        }
      }
      .item_mi_left {
        width: 80rpx;
        height: 40rpx;
        background: #f86515;
        border-radius: 20px;
        font-size: 24rpx;
        font-family: PingFang;
        font-weight: 500;
        color: #ffffff;
        text-align: center;
        line-height: 40rpx;
      }
    }
  }
}

.activity_not_open {
  .empty_sort_page {
    width: 100%;
    // height: 100vh;
    background: #f5f5f5;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 340rpx;

    .empty_img {
     width: 380rpx;
     height: 280rpx;
      margin-bottom: 32rpx;
    }

    .empty_text {
      font-size: 26rpx;
      color: #999;
    }

    .back {
      margin-top: 80rpx;
      padding: 10rpx 50rpx;
      font-size: 28rpx;
      border: 2rpx solid #999;
      border-radius: 30rpx;
      color: #999;
    }
  }
}
</style>
