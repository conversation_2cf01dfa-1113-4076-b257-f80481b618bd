<template>
  <view class="top_header">
    <view class="fixed_top_bar"></view>
      <!-- wx-1-start -->
	  <!-- #ifdef MP -->
      <view class="" :style="{
          paddingTop:statusBarHeight,
        }">
      </view>
      <view class="header_v header_o" :style="{height:menuButtonHeight,top:menuButtonTop,width:menuButtonleft}">
        <image :src="backs" class="header_o_img" @click="back"></image>
        <image :src="imgUrl + 'lotte_share.png'" @click="openShareVo"></image>
      </view>
      <!-- #endif -->
	  <!-- wx-1-end -->
      <!-- #ifndef MP -->
      <view class="header_v">
        <image :src="imgUrl + 'lotte_back.png'" @click="back"></image>
        <image :src="imgUrl + 'lotte_share.png'" @click="openShareVo"></image>
      </view>
      <!-- #endif -->
  </view>
</template>

<script>
export default {
  data() {
    return {
      imgUrl: process.env.VUE_APP_IMG_URL + 'lottery/',
	  //wx-2-start
      // #ifdef MP
      backs:process.env.VUE_APP_IMG_URL+'index/back.png',
      statusBarHeight:uni.getSystemInfoSync().statusBarHeight+'px',
      menuButtonHeight:uni.getMenuButtonBoundingClientRect().height+'px',
      menuButtonTop:uni.getMenuButtonBoundingClientRect().top+'px',
      menuButtonleft:uni.getMenuButtonBoundingClientRect().left+'px',
      // #endif
	  //wx-2-end
    }
  },
  methods: {
    openShareVo() {
      this.$emit('openShare')
    },
    back() {
      const page = getCurrentPages()
      if (page.length > 1) {
        this.$Router.back(1)
      } else {
        // this.$Router.replace('/pages/index/index')
        uni.switchTab({
          url: '/pages/index/index'
        })
        this.$Router.pushTab('/pages/index/index')
      }
    }
  }
}
</script>

<style lang="scss">
.top_header {
  position: absolute;
  top: 0;
  width: 100%;
}

//app-1-start








//app-1-end

.header_v {
  padding: 20rpx 20rpx 0rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  image {
    width: 50rpx;
    height: 50rpx;
  }
}
.header_o{
  z-index: 999;
  .header_o_img{
    width: 15rpx;
    height: 28rpx;
  }
  padding: 0;
  position: fixed;
  padding: 0 20rpx;
}
</style>
