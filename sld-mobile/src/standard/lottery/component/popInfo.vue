<template>
  <uni-popup ref="roll" type="center" @change="popChange">
    <view
      class="popup_box bg_style"
      :style="{ backgroundImage: 'url(' + imgUrl + 'roll_integral.png)' }"
      v-if="type == 'roll_on'"
    >
      <view class="title">{{ $L('恭喜获得') }}</view>
      <view class="info">
        <view class="info_name" v-if="lotResu.prizeType == 1">
          <text>{{ parseFloat(lotResu.description) }}</text>
          <text>{{ $L('积分') }}</text>
        </view>
        <view
          class="info_name"
          v-if="lotResu.couponType == 1 || lotResu.couponType == 3"
        >
          <text>{{ parseFloat(lotResu.description) }}</text>
          <text>{{ `${$L('元')}${$L('优惠券')}` }}</text>
        </view>
        <view class="info_name" v-if="lotResu.couponType == 2">
          <text>{{ parseFloat(lotResu.publishValue / 10) }}</text>
          <text>{{ `${$L('折')}${$L('优惠券')}` }}</text>
        </view>
		<view
		  class="info_name"
		  v-if="lotResu.couponType == 4"
		>
		<block v-if="parseFloat(lotResu.publishValue)>0">
			<text>{{ parseFloat(lotResu.publishValue) }}</text>
			<text>{{ `${$L('元')}${$L('运费券')}` }}</text>
		</block>
		<block v-else>
			<text></text>
			<text style="font-size: 30rpx;">{{ $L('免运费券')}}</text>
		</block>
		</view>
      </view>
      <view class="desc flex_column_between_center">
        <view class="desc_desc">{{
          lotResu.prizeType == 1
            ? $L('积分已存入“我的-积分”')
            : $L('优惠券已存入“我的-优惠券”')
        }}</view>
        <view class="desc_button flex_row_between_center">
          <view class="btn1" @click="shareAc">{{ $L('分享活动') }}</view>
          <view class="btn2" @click="deployUse">{{ $L('立即查看') }}</view>
        </view>
      </view>
    </view>

    <view
      class="popup_box_fail bg_style"
      :style="{ backgroundImage: 'url(' + imgUrl + 'roll_fail.png)' }"
      v-if="type == 'roll_off'"
    >
      <view class="title">{{ $L('未中奖') }}</view>
      <view class="roll_on flex_row_center_center">
        <view class="roll_on_but" @click="closePop">{{ $L('继续抽奖') }}</view>
      </view>
    </view>

    <view class="ac_detail" v-if="type == 'acDetail'">
      <view class="detail_title flex_row_center_center">
        <image :src="imgUrl + 'tiny_gram_left.png'" mode="aspectFit"></image>
        <text>{{ $L('活动详情') }}</text>
        <image :src="imgUrl + 'tiny_gram_right.png'" mode="aspectFit"></image>
      </view>
      <view class="detail_desc">
        <view>{{ $L('活动时间') }}:</view>
        <view class="s_font"
          >{{ reactAcDetail.startTime }}~{{ reactAcDetail.endTime }}</view
        >
        <view>{{ $L('活动说明') }}:</view>
        <view class="s_font">{{
          reactAcDetail.drawDescription ? reactAcDetail.drawDescription : ''
        }}</view>
        <view class="s_font">
          {{
            reactAcDetail.ruleType == 1
              ? `${$L('每人每天可抽奖')}${reactAcDetail.ruleNum}${$L('次')}`
              : `${$L('每人总共可抽奖')}${reactAcDetail.ruleNum}${$L('次')}`
          }},
          {{
            reactAcDetail.integralUse > 0
              ? `${$L('每次抽奖消耗')}${reactAcDetail.integralUse}${$L('积分')}`
              : `${$L('每次抽奖不消耗积分')}`
          }}
        </view>
      </view>
    </view>
    <view class="close flex_row_center_center">
      <view class="close_blank" @click="closePop">
        <image :src="imgUrl + 'close.png'" mode="aspectFit"></image>
      </view>
    </view>
  </uni-popup>
</template>

<script>
export default {
  data() {
    return {
      imgUrl: process.env.VUE_APP_IMG_URL + 'lottery/',
      type: '',
      lotResu: {},
      isCreated: false
    }
  },
  created() {
    this.isCreated = true
  },
  inject: ['getAcDetail', 'openShare'],
  computed: {
    reactAcDetail() {
      return this.isCreated ? this.getAcDetail() : {}
    }
  },
  methods: {
    change(e) {
      this.current = e.detail.current + 1
    },
    open(obj, type) {
      this.type = type
      this.lotResu = obj
      this.$refs.roll.open()
    },
    closePop() {
      this.$refs.roll.close()
      this.$emit('revive')
    },
    popChange(e) {
      if (!e.show) {
        this.$emit('revive')
      }
    },
    shareAc() {
      this.closePop()
      this.openShare()
    },
    deployUse() {
      let path =
        this.lotResu.prizeType == 1
          ? '/pages/user/myIntegral'
          : '/standard/coupon/myCoupon'
      this.$Router.push(path)
    }
  }
}
</script>

<style lang="scss">
.uni-popup {
  z-index: 100;
}

.bg_style {
  background-position: center center;
  background-repeat: no-repeat;
  background-size: contain;
}

.close {
  width: 100%;
  position: absolute;
  bottom: -140rpx;

  .close_blank {
    image {
      width: 55rpx;
      height: 55rpx;
    }
  }
}
.popup_box {
  width: 480rpx;
  height: 530rpx;
  position: relative;

  .info {
    margin-top: 48rpx;
    height: 182rpx;
    padding: 0 30rpx;
    text-align: center;

    .info_name {
      height: 138rpx;
      text-align: center;
      line-height: 138rpx;

      & > text:first-child {
        font-size: 57rpx;
        font-family: SourceHanSansCN;
        font-weight: bold;
        color: #ff0d0d;
      }

      & > text:last-child {
        margin-left: 10rpx;
        font-size: 23rpx;
        font-family: SourceHanSansCN;
        font-weight: 500;
        color: #b66e07;
      }
    }

    .info_time {
      padding: 0 20rpx;
      font-size: 15rpx;
      font-family: SourceHanSansCN;
      font-weight: 400;
      color: #999999;
      display: flex;
      justify-content: space-between;
      align-items: center;

      & > text:last-child {
        font-size: 14rpx;
        color: #a86608;
        padding: 4rpx 10rpx;
        border-radius: 15px;
        background: #ffeac7;
      }
    }
  }

  .desc {
    margin-top: 40rpx;
    padding: 40rpx;

    .desc_desc {
      font-size: 24rpx;
      font-family: SourceHanSansCN;
      font-weight: 400;
      color: #666666;
      text-align: center;
    }
    .desc_button {
      width: 100%;
      margin-top: 32rpx;
      .btn1 {
        width: 161rpx;
        height: 53rpx;
        background: linear-gradient(0deg, #fff0ca 0%, #ffe198 100%);
        border-radius: 10px;
        text-align: center;
        line-height: 53rpx;
        font-size: 23rpx;
        font-family: SourceHanSansCN;
        font-weight: 400;
        color: #735e40;
      }
      .btn2 {
        width: 161rpx;
        height: 53rpx;
        background: linear-gradient(
          0deg,
          #fce000 0%,
          #fed600 0%,
          #ff5d00 0%,
          #fb3e31 100%
        );
        border-radius: 10px;
        text-align: center;
        line-height: 53rpx;
        font-size: 23rpx;
        font-family: SourceHanSansCN;
        font-weight: 400;
        color: #ffffff;
      }
    }
  }
}

.popup_box_fail {
  width: 480rpx;
  height: 520rpx;
  .roll_on {
    width: 100%;
    position: absolute;
    bottom: 43rpx;
    .roll_on_but {
      width: 161rpx;
      height: 53rpx;
      background: linear-gradient(
        0deg,
        #fce000 0%,
        #fed600 0%,
        #ff5d00 0%,
        #fb3e31 100%
      );
      border-radius: 10px;
      font-size: 23rpx;
      font-family: SourceHanSansCN;
      font-weight: 400;
      color: #ffffff;
      text-align: center;
      line-height: 53rpx;
    }
  }
}

.title {
  padding-top: 10rpx;
  text-align: center;
  font-size: 37rpx;
  font-family: SourceHanSansCN;
  font-weight: 500;
  color: #ffffff;
}

.ac_detail {
  padding: 40rpx 20rpx;
  width: 479rpx;
  min-height: 505rpx;
  background: linear-gradient(0deg, #ff562f 0%, #f1b135 100%);
  border-radius: 30px;
  .detail_title {
    text {
      font-size: 37rpx;
      font-family: SourceHanSansCN;
      font-weight: 500;
      color: #ffffff;
      margin: 0 30rpx;
    }
    image {
      width: 72rpx;
      height: 4rpx;
    }
  }

  .detail_desc {
    margin-top: 30rpx;
    word-break: break-all;
    line-height: 40rpx;
    color: #fff;
    .s_font {
      font-size: 22rpx;
      line-height: 34rpx;
      margin-bottom: 20rpx;
    }
  }
}
</style>
