<template>
	<view :style="mix_diyStyle">
  <view
    class="turn_main"
    :style="{
      backgroundImage: 'url(' + backgroundImg + ')',
      minHeight: screenHeight + 'px'
    }"
  >
    <view class="turn_main_con">
      <view class="turn_title bg_style"></view>
      <view class="right_intro">
        <view class="right_item" @click="open">{{ $L('活动详情') }}</view>
        <view class="right_item" @click="toRec">{{ $L('中奖记录') }}</view>
      </view>
      <view class="turn_table">
        <turnTable
          @res="res"
          ref="turnTable"
          :userInt="myInt"
          :drawId="drawId"
        ></turnTable>
      </view>

      <view class="turn_bot">
        <block v-if="reactAcDetail.acState && reactAcDetail.acState.flag == 1">
          <view
            class="turn_bot_con bg_style_cover"
            :style="{ backgroundImage: 'url(' + imgUrl + 'turn_print_bg.png)' }"
          >
            <view class="bot_instruct">
              <view class="">{{
                `${$L('剩余抽奖机会')}: ${myInt.remainNum}`
              }}</view>
              <view
                class="flex_row_between_center"
                v-if="lotDetail.integralUse > 0"
              >
                <text
                  >{{ $L('我的积分') }}：{{ myInt.integral
                  }}{{ $L('积分') }}</text
                >
                <text
                  >{{ $L('使用积分') }}：{{ lotDetail.integralUse
                  }}{{ $L('积分') }}</text
                >
              </view>
            </view>
            <view class="roll_in_list flex_column_between_center">
              <view class="roll_title">{{ $L('中奖名单') }}</view>
              <view class="roll_swiper flex_row_center_center">
                <swiper
                  :indicator-dots="false"
                  :autoplay="true"
                  :interval="5000"
                  :duration="1000"
                  :disable-touch="true"
                  :vertical="true"
                  circular
                  @change="change"
                  display-multiple-items="3"
                >
                  <swiper-item v-for="(item, index) in lotList" :key="index">
                    <view
                      :class="{ roll_swiper_item: true, sel: index == current }"
                      >{{ $L('恭喜') }} {{ item.memberNameHide }} {{ $L('获得')
                      }}{{ item.prizeName }}</view
                    >
                  </swiper-item>

                  <block v-if="3 - lotList.length > 0">
                    <swiper-item v-for="i in 3 - lotList.length" :key="i"> </swiper-item>
                  </block>
                </swiper>
              </view>
            </view>
          </view>
        </block>
        <block v-else>
          <view class="flex_column_between_center">
            <view class="flex_row_center_center roll_title_con">
              <view class="roll_title_offAc">{{
                reactAcDetail.acState.desc
              }}</view>
            </view>
            <view class="flex_row_center_center">
              <view class="roll_title_end_offAc">{{
                reactAcDetail.acState.time
              }}</view>
            </view>
          </view>
        </block>
      </view>
    </view>

    <popInfo ref="popInfo" @revive="revive"></popInfo>

    <share ref="share"></share>
  </view>
	</view>
</template>

<script>
import { arrCom } from '@/utils/common.js'
import turnTable from './turnTable.vue'
import popInfo from '../component/popInfo.vue'
import topHeader from '../component/topHeader.vue'
import share from '../component/share.vue'
export default {
  components: {
    topHeader,
    popInfo,
    share,
    turnTable
  },
  props: {
    lotDetail: {
      type: Object
    },
    drawId: {
      type: Number
    }
  },
  inject: ['getAcDetail'],
  computed: {
    reactAcDetail() {
      return this.isCreated ? this.getAcDetail() : {}
    },
    backgroundImg() {
      let { backgroundImageUrl } = this.lotDetail
      return backgroundImageUrl
        ? backgroundImageUrl
        : this.imgUrl + 'turn_lotte_bg2.jpg'
    }
  },
  data() {
    return {
      imgUrl: process.env.VUE_APP_IMG_URL + 'lottery/',
      current: 0,
      lotList: [],
      rollRes: '',
      acState: '',
      myInt: {},
      screenHeight: null,
      timer: null,
      isCreated: false
    }
  },

  created() {
    this.isCreated = true
  },

  mounted() {
    this.getlotRecAsync()
    this.getlotInt()
    this.screenHeight = uni.getSystemInfoSync().windowHeight
  },

  beforeDestroy() {
    clearInterval(this.timer)
  },

  methods: {
    change(e) {
      this.current = e.detail.current + 1
    },
    open() {
      this.$refs.popInfo.open({}, 'acDetail')
    },

    res(seq, lotResult) {
      if (this.lotDetail.integralUse > 0)
        this.myInt.integral -= this.lotDetail.integralUse
      if (this.myInt.remainNum > 0) this.myInt.remainNum -= 1
      if (lotResult.prizeType == 1)
        this.myInt.integral += parseInt(lotResult.description)
      setTimeout(() => {
        this.$refs.popInfo.open(
          lotResult,
          lotResult.isPrize == 1 ? 'roll_on' : 'roll_off'
        )
      }, 500)
    },
    async getlotRecAsync() {
      if (this.reactAcDetail.acState.flag != 1) {
        return
      }

      this.lotList = await this.getlotRec()
      this.timer = setInterval(async () => {
        let res = await this.getlotRec()
        if (res.length) {
          this.lotList.push(...arrCom(this.lotList, res, 'winId'))
        }
      }, 6000)
    },
    getlotRec() {
      return new Promise((resolve) => {
        this.$request({
          url: 'v3/promotion/front/draw/winList',
          data: {
            drawId: this.drawId
          }
        }).then((res) => {
          if ((res.state = 200)) {
            resolve(res.data)
          }
        })
      })
    },
    getlotInt() {
      this.$request({
        url: 'v3/promotion/front/draw/integral',
        data: {
          drawId: this.drawId
        }
      }).then((res) => {
        if ((res.state = 200)) {
          this.myInt = res.data
        }
      })
    },

    doDraw() {},
    toRec() {
      this.$Router.push({
        path: '/standard/lottery/lotRec',
        query: {
          drawId: this.drawId
        }
      })
    },
    revive() {}
  }
}
</script>

<style lang="scss" scoped>
uni-swiper {
  display: block;
  height: 130rpx;
  width: 450rpx;
}

uni-swiper-item {
  display: flex;
  align-items: center;
  justify-content: center;
}
//wx-1-start
/* #ifdef MP */
swiper {
  width: 450rpx !important;
  height: 130rpx !important;
}

swiper-item {
  display: flex;
  align-items: center;
  justify-content: center;
}
/* #endif */
//wx-1-end
.bg_style {
  background-position: center center;
  background-repeat: no-repeat;
  background-size: contain;
}

.bg_style_cover {
  background-position: center center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.turn_main {
  margin: 0;
  //app-1-start



  //app-1-end

  padding-top: 90rpx;

  background-position: top;
  background-repeat: no-repeat;
  background-size: cover;
  width: 750rpx;
  overflow: hidden;

  .turn_main_con {
    margin-top: 20rpx;
    position: relative;
  }

  .turn_title {
    padding: 0 40rpx;
    // 687px
    height: 218rpx;
  }

  .right_intro {
    position: absolute;
    right: 0;
    top: 217rpx;
	//app-2-start



	//app-2-end
    z-index: 88;

    .right_item {
      width: 141rpx;
      height: 50rpx;
      background: #fede99;
      border-radius: 25rpx 0 0 25rpx;
      text-align: center;
      line-height: 50rpx;
      font-size: 26rpx;
      font-family: SourceHanSansCN;
      font-weight: 600;
      color: #da3e00;

      &:last-child {
        margin-top: 20rpx;
      }
    }
  }

  .turn_table {
    margin-top: 20rpx;
    display: flex;
    justify-content: center;
  }

  .turn_bot {
    padding: 30rpx 40rpx;

    .turn_bot_con {
      height: 322rpx;
      padding: 20rpx 0;
      position: relative;
    }

    .roll_title_offAc {
      letter-spacing: 6px;
      text-align: center;
      font-size: 36rpx;
      font-family: zcoolqingkehuangyouti;
      font-weight: 600;
      color: #ffffff;
    }

    .roll_title_end_offAc {
      color: #ffffff;
      margin-top: 10rpx;
    }

    .bot_instruct {
      padding: 0 72rpx;

      view:last-child {
        margin-top: 6rpx;
        font-size: 18rpx;
        font-family: SourceHanSansCN;
        font-weight: 400;
        color: #d13912;
      }

      view:first-child {
        text-align: center;
        font-size: 26rpx;
        font-family: SourceHanSansCN;
        font-weight: bold;
        color: #ab0100;
      }
    }

    .roll_in_list {
      position: absolute;
      bottom: 10rpx;
      width: 100%;

      .roll_title {
        text-align: center;
        font-size: 28rpx;
        font-family: zcoolqingkehuangyouti;
        font-weight: 600;
        color: #b31111;
        line-height: 32rpx;
      }

      .roll_title_end {
        color: #ffffff;
        margin-top: 10rpx;
      }

      .roll_swiper {
        width: 450rpx;
      }

      .roll_swiper_item {
        text-align: center;
        font-size: 22rpx;
        font-family: SourceHanSansCN;
        font-weight: 400;
        color: #666666;

        &.sel {
          color: #666666;
          font-weight: 600;
          font-size: 24rpx;
        }
      }
    }
  }
}
</style>
