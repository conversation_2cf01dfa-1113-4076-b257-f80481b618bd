<template>
	<view :style="mix_diyStyle">
  <view class="lottery_detail" v-if="loadComplete">
    <topHeader @openShare="shareVo"></topHeader>
    <shakeDetail
      :lotDetail="lotDetail"
      ref="shakeDetail"
      :drawId="drawId"
      v-if="lotDetail.drawType == 4"
    ></shakeDetail>
    <scratcherDetail
      :lotDetail="lotDetail"
      ref="scratcherDetail"
      :drawId="drawId"
      v-if="lotDetail.drawType == 3"
    ></scratcherDetail>
    <cardDetail
      :lotDetail="lotDetail"
      ref="cardDetail"
      :drawId="drawId"
      v-if="lotDetail.drawType == 5"
    ></cardDetail>
    <twgDetail
      :lotDetail="lotDetail"
      ref="twgDetail"
      :drawId="drawId"
      v-if="lotDetail.drawType == 1"
    ></twgDetail>
    <turnDetail
      :lotDetail="lotDetail"
      ref="turnDetail"
      :drawId="drawId"
      v-if="lotDetail.drawType == 2"
    ></turnDetail>
    <share ref="share" :shareData="shareData"></share>
  </view>
	</view>
</template>

<script>
import { mapState } from 'vuex'
import { getCurLanguage } from '@/utils/base.js'
import popInfo from './component/popInfo.vue'
import topHeader from './component/topHeader.vue'
import share from './component/share.vue'
import shakeDetail from './shaRoll/detail.vue'
import scratcherDetail from './scratcher/detail.vue'
import cardDetail from './card/detail.vue'
import twgDetail from './tweGrid/detail.vue'
import turnDetail from './turn/detail.vue'
export default {
  components: {
    topHeader,
    popInfo,
    share,
    shakeDetail,
    scratcherDetail,
    cardDetail,
    twgDetail,
    turnDetail
  },
  provide() {
    return {
      getAcDetail: () => this.acDetail,
      relotDetail: () => this.lotDetail,
      openShare: () => {
        this.$refs.share.open()
      }
    }
  },
  data() {
    return {
      drawId: null,
      lotDetail: {},
      acDetail: { endTime: 'sssss', startTime: 'eeeee' },
      myInt: {},
      shareData: {
        title: getCurLanguage(this.$L('抽奖')),
        desc: getCurLanguage(this.$L('幸运大抽奖，好礼等你拿！')),
        path: '/standard/lottery/detail',
        link: process.env.VUE_APP_API_URL + 'standard/lottery/detail',
        imageUrl: process.env.VUE_APP_IMG_URL + 'lottery/lotte_share_icon.png',
        imgUrl: process.env.VUE_APP_IMG_URL + 'lottery/lotte_share_icon.png',
        query: 'drawId=' + this.drawId
      },
      loadComplete: false
    }
  },
  onShow() {
    if (this.$refs.shakeDetail) {
      this.$refs.shakeDetail.onShowAcc()
    }
  },
  onLoad(options) {
    this.drawId = Number(this.$Route.query.drawId)
    if (options && options.scene) {
      let url = decodeURIComponent(options.scene)
      this.drawId = Number(url)
    }

    if (!this.hasLogin) {
      uni.setStorageSync('fromurl', {
        url: this.$Route.path,
        query: { drawId: this.drawId }
      })
      this.$Router.push('/pages/public/login')
    }
    this.shareData.path = `/standard/lottery/detail?drawId=${this.drawId}`
    this.shareData.link =
      process.env.VUE_APP_API_URL +
      `standard/lottery/detail?drawId=${this.drawId}`
    this.shareData.query = `drawId=${this.drawId}`

    this.getlotDetail()
  },
  onUnload() {
    if (this.$refs.shakeDetail) {
      uni.stopAccelerometer()
    }
  },
  onHide() {
    if (this.$refs.shakeDetail) {
      this.$refs.shakeDetail.showState = 'screen'
      uni.stopAccelerometer()
    }
  },

  onShareAppMessage() {
    return {
      ...this.shareData
    }
  },

  onShareTimeline() {
    return {
      ...this.shareData
    }
  },

  computed: {
    ...mapState(['hasLogin'])
  },

  methods: {
    shareVo() {
      this.$refs.share.open()
    },
    getlotDetail() {
      this.$request({
        url: 'v3/promotion/front/draw/detail',
        data: {
          drawId: this.drawId
        }
      }).then((res) => {
        if (res.state == 200) {
          this.lotDetail = res.data







          this.loadComplete = true

          this.drawId = Number(res.data.drawId)
          let {
            startTime,
            endTime,
            ruleNum,
            ruleType,
            drawDescription,
            integralUse
          } = res.data
          let now = new Date().getTime(),
            start = new Date(startTime.replace(/-/g, '/')).getTime(),
            end = new Date(endTime.replace(/-/g, '/')).getTime()
          let acState = {}
          if (now < start) {
            acState.flag = 0
            acState.desc = this.$L('活动未开始')
            acState.time = startTime
          } else if (now > end) {
            acState.flag = 2
            acState.desc = this.$L('活动已结束')
            acState.time = endTime
          } else {
            acState.flag = 1
            acState.desc = this.$L('剩余可抽奖次数：')
          }
          this.acDetail = {
            startTime,
            endTime,
            ruleNum,
            ruleType,
            drawDescription,
            acState,
            integralUse
          }
          this.shareData.title = res.data.drawTypeValue
          // #ifdef H5
          this.$WXBrowserShareThen(1, this.shareData)
          this.$WXBrowserShareThen(2, this.shareData)
          // #endif
        } else {
          this.$api.msg(res.msg)
        }
      })
    }
  }
}
</script>

<style>
page {
  width: 100%;
  height: 100%;
}

.lottery_detail {
  width: 100%;
}
</style>
