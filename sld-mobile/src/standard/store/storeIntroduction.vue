<template>
	<view :style="mix_diyStyle">
		<view class="store_introduction">
			<view class="content">
				<view class="store_con"
					:style="'background-image:url( ' +store_banner +');background-size:100% 100%;background-repeat:no-repeat'">
					<view class="store_con_back">

					</view>
					<!--  店铺详情  -->
					<view class="info_left" v-if="vender_detail">
						<image class="avat" :src="vender_detail.storeLogoUrl" mode="aspectFit"></image>
						<view class="info_des">
							<view class="info_top">
								<text>{{ vender_detail.storeName }}</text>
								<view v-if="vender_detail.isOwnShop == 1" class="info_top_zi" :style="{borderColor:color60}">{{ $L('自营') }}</view>
							</view>
							<view class="info_bot">
								<!-- <image :src="imgUrl + 'store/renqizhi.png'"></image> -->
								<text class="iconfont iconrenqizhi" style="margin-right: 9rpx;"></text>
								<text>人气：{{ vender_detail.followNumber }}</text>
							</view>
							<view class="info_bot_gong">
								<text
									class="info_bot_text">店铺公告：{{vender_detail.storeNotice?vender_detail.storeNotice:'--'}}</text>
							</view>
						</view>
					</view>
					<!-- 店铺二维码 -->
					<view class="store_code">
						<view class="store_code_top">
							<text>{{ $L('店铺二维码') }}</text>
							<image :src="imgUrl + 'store/erweima.png'" @tap="handleShare"></image>
						</view>
						<view class="store_code_des">
							<view class="des_pre">
								<text class="des_pre_text" style="margin-right: 62rpx;">{{ $L('描述相符') }}</text>
								<uni-rate :size="16" active-color="var(--color_price)" :margin="5" readonly
									:value="vender_detail.descriptionScore?vender_detail.descriptionScore: 0" />
							</view>
							<view class="des_pre">
								<text class="des_pre_text" style="margin-right: 62rpx;">{{ $L('服务态度') }}</text>
								<uni-rate :size="16" active-color="var(--color_price)" :margin="5" readonly
									:value="vender_detail.serviceScore?vender_detail.serviceScore: 0" />
							</view>
							<view class="des_pre">
								<text class="des_pre_text" style="margin-right: 62rpx;">{{ $L('发货速度') }}</text>
								<uni-rate :size="16" active-color="var(--color_price)" :margin="5" readonly
									:value="vender_detail.deliverScore?vender_detail.deliverScore: 0" />
							</view>
						</view>
					</view>
					<!-- 公司相关 -->
					<view class="company_related">
						<view class="related_pre" v-if="vender_detail.companyName">
							<text>{{ $L('公司名称') }}</text>
							<text>{{vender_detail.companyName ? vender_detail.companyName : '--'}}</text>
						</view>
						<!-- dev_o2o-start -->
						<view class="related_pre" v-if="vender_detail.shopType==2">
							<text>{{ $L('营业状态') }}</text>
							<text>{{vender_detail.businessState==1 ? $L('正常') : '--'}}</text>
						</view>
						<view class="related_pre" v-if="vender_detail.shopType==2" @click="openModel">
							<text>{{ $L('营业时间') }}</text>
							<text class="related_pre_time">{{businessTimeInfoValue}}</text>
							<image v-if="vender_detail.businessTimeType > 1" class="right_arrow_img" :src="right_arrow"
								mode="aspectFit" @click="openModel"></image>
						</view>
						<view class="related_pre" v-if="vender_detail.shopType==2&&vender_detail.businessLicenseImage"
							@click="previewImg">
							<text>{{ $L('营业资质') }}</text>
							<image class="right_arrow_img" :src="right_arrow" mode="aspectFit"
								@click="previewImg('businessLicenseImage')"></image>
						</view>
						<view class="related_pre"
							v-if="vender_detail.shopType==2&&vender_detail.storeEnvironmentImageUrl.length>0"
							@click="previewImg('storeEnvironmentImageUrl')">
							<text>{{ $L('店铺环境') }}</text>
							<image class="right_arrow_img" :src="right_arrow" mode="aspectFit" @click="openModel">
							</image>
						</view>
						<!-- dev_o2o-end -->
						<view class="related_pre">
							<text>{{ $L('所在地') }}</text>
							<text>{{vender_detail.address ? vender_detail.address : '--'}}</text>
						</view>
						<view class="related_pre">
							<text>{{ $L('开店时间') }}</text>
							<text>{{vender_detail.createTime ? vender_detail.createTime : '--'}}</text>
						</view>
						<view class="related_yewu">
							<text>{{ $L('主营商品') }}</text>
							<text>{{
              vender_detail.mainBusiness
                ? vender_detail.mainBusiness.replace(/,/g, '、')
                : '--'
            }}</text>
						</view>
					</view>
					<view style="width: 100%;height: 20rpx;">

					</view>
				</view>
			</view>
			<!-- 遮罩层 -->
			<view class="mask" catchtouchmove="preventTouchMove" v-if="modalDlg" @tap="close"></view>
			<!-- 二维码分享弹框 -->
			<view class="modalDlg" v-if="modalDlg">
				<text>{{ vender_detail.storeName }}</text>
				<text>{{ $L('邀请好友来扫一扫分享店铺给TA') }}</text>

				<view style="margin-top: 50rpx;">
					<yuanqiQrCode ref="yuanqiQRCode" :text="storeLocation" :size="300" :borderSize="15"></yuanqiQrCode>
				</view>


			</view>
		</view>
		<!-- dev_o2o-start -->
		<uni-popup ref="controlPopup" type="bottom">
			<view class="control_popup">
				<view class="popup_top">
					<text>{{$L('营业时间')}}</text>
					<image :src="imgUrl + 'close_2.png'" mode="aspectFit" @click="cancelModal" class="top_image">
					</image>
				</view>
				<scroll-view class="uni-list control_list" scroll-y="true">
					<block v-for="(item,index) in vender_detail.businessTimeInfo" :key="index">
						<view class="week_name">{{ vender_detail.businessTimeType == 2 ? $L('每天') : item.weeks }}</view>
						<view class="flex_row_start_start time_list">
							<block v-for="(items,indexs) in item.timeList" :key="indexs">
								<view class="time_item" v-if="items.startTime && items.endTime">
									{{ items.startTime }}-{{ items.endTime }}
								</view>
							</block>
						</view>
					</block>
				</scroll-view>
			</view>
		</uni-popup>
		<!-- dev_o2o-end -->
	</view>
</template>

<script>
	import uniRate from '@/components/uni-rate/uni-rate.vue'
	import yuanqiQrCode from "@/components/yuanqi-qr-code/yuanqi-qr-code.vue";
	import {
		set16ToRgb
	} from '@/diyStyle/index.js'
	export default {
		data() {
			return {
				bid: '',
				vid: '',
				store_list: [],
				vender_detail: '',
				//店铺详情
				modalDlg: false,
				//模态框
				imgUrl: process.env.VUE_APP_IMG_URL,
				right_arrow: process.env.VUE_APP_IMG_URL + 'ladder/right1.png',
				//图片地址
				store_banner: '', //店铺首页banner图
				city_site_open: '',
				searchList: [],
				show: false,
				businessTimeInfoValue: '',
				storeLocation: ''
			}
		},

		components: {
			yuanqiQrCode,
			uniRate
		},
		props: {},
		onLoad: function(options) {
			setTimeout(() => {
				uni.setNavigationBarTitle({
					title: this.$L('店铺信息')
				})
			}, 0);

			this.vid = this.$Route.query.vid
			this.$sldStatEvent({
				behaviorType: 'spv',
				storeId: this.vid
			})
			this.venderDetail() //店铺详情
		},
		
		computed:{
			color60(){
				const diyStyle = this.diyStyle_var
				return 	set16ToRgb(diyStyle['--color_main'], 0.6)
			}
		},
		
		methods: {
			// dev_o2o-start
			//打开弹窗
			openModel() {
				if (this.vender_detail.businessTimeType > 1) {
					this.$refs.controlPopup.open();
				}
			},
			//关闭弹框
			cancelModal() {
				this.$refs.controlPopup.close();
			},
			//店铺环境
			previewImg(type) {
				let _this = this
				this.$refs.controlPopup.close();
				let img_info = []
				if (type == 'storeEnvironmentImageUrl') {
					img_info = JSON.parse(JSON.stringify(this.vender_detail.storeEnvironmentImageUrl))
				} else {
					let obj = this.vender_detail.businessLicenseImage.split(',')
					img_info = obj
				}
				uni.previewImage({
					urls: img_info,
					current: 0,
					longPressActions: {
						success: function(data) {
							uni.saveImageToPhotosAlbum({
								filePath: urls[data.index],
								success: function() {
									_this.$api.msg(_this.$L('保存成功'))
								},
								fail: function(err) {
									_this.$api.msg(err.errMsg)
								}
							})
						},
						fail: function(err) {
							_this.$api.msg(err)
						}
					}
				})
			},
			// dev_o2o-end
			// 商铺详情
			venderDetail(e) {
				let key = uni.getStorageSync('token')
				let that = this
				let {
					vid
				} = that
				let client = ''
				//wx-2-start
				// #ifdef MP
				client = 'wx_xcx'
				// #endif
				//wx-2-end
				if (vid) {
					let param = {}
					param.data = {}
					param.data.storeId = vid
					param.url = 'v3/seller/front/store/detail'
					this.$request(param)
						.then((res) => {
							if (res.state == 200) {
								let vender_detail = res.data
								this.vender_detail = vender_detail
								this.store_banner = vender_detail.storeBackdropUrl
								// dev_o2o-start
								let businessTimeInfo = res.data.businessTimeTypeValue
								if (res.data.businessTimeType == 2 || res.data.businessTimeType == 3) {
									res.data.businessTimeInfo.forEach((item, index) => {
										if (res.data.businessTimeType == 3) {
											businessTimeInfo = businessTimeInfo + ' ' + item.weeks + ' '
										}
										item.timeList.forEach((it, ind) => {
											if (it.startTime && it.endTime) {
												businessTimeInfo = businessTimeInfo + (ind == 0 ? '' :
													'、') + it.startTime + '~' + it.endTime
											}
										})
									})
									this.businessTimeInfoValue = businessTimeInfo
								} else {
									this.businessTimeInfoValue = businessTimeInfo
								}
								this.storeLocation =`${process.env.VUE_APP_API_URL}standard/store/shopHomePage?vid=${vender_detail.storeId}`
								if (vender_detail.shopType == 2) {
									this.storeLocation =`${process.env.VUE_APP_API_URL}extra/o2o/store/index?storeId=${vender_detail.storeId}`
								}
								// dev_o2o-end
							} else {
								this.$api.msg(res.msg)
							}
						})
						.catch((e) => {
							//异常处理
						})
				}
			},

			//点击二维码分享
			handleShare(e) {
				this.modalDlg = true
			},

			// 禁止屏幕滚动
			preventTouchMove: function() {},

			//关闭弹框
			close(e) {
				this.modalDlg = false
			},

			back() {
				;
				(this.searchList = []), (this.show = false)
				this.searchPn = 1
				this.search_hasmore = true
			}
		}
	}
</script>
<style lang="scss">
	page {
		background: #f5f5f5;
	}

	/* pages/storeIntroduction/storeIntroduction.wxss */
	.store_introduction {
		width: 100%;
		height: 100%;
		background: #f5f5f5;
	}

	.content {
		width: 751rpx;
		height: 100%;
		background-size: 100%;
		background: #f5f5f5;
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
	}

	.store_top {
		width: 100%;
		height: 47rpx;
		color: #ffffff;
		display: flex;
		align-items: center;
		justify-content: flex-end;
		padding: 0 18rpx;
		box-sizing: border-box;
		position: fixed;
		top: 0;
	}

	.store_top text {
		font-size: 36rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: rgba(255, 255, 255, 1);
		line-height: 32rpx;
	}

	.store_top image {
		width: 48rpx;
		height: 40rpx;
		margin-left: 239rpx;
	}

	.store_con {
		width: 100%;
		height: 566rpx;
		padding-top: 40rpx;
		background: #f8f8f8;
		padding-left: 21rpx;
		box-sizing: border-box;
		position: relative;
	}

	.store_con_back {
		width: 100%;
		height: 566rpx;
		background: linear-gradient(180deg, rgba(0, 0, 0, 0.7) 0%, rgba(255, 255, 255, 0.5) 100%);
		position: absolute;
		top: 0;
		left: 0;
		z-index: 1;
	}

	.info_left {
		width: 100%;
		display: flex;
		z-index: 999;
		position: relative;
	}

	.info_left .avat {
		width: 112rpx;
		height: 112rpx;
		border-radius: 20rpx;
		margin-right: 16rpx;
		z-index: 999;
		position: relative;
	}

	.info_des {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: space-between;
		flex: 1;
		z-index: 999;
		position: relative;
	}

	.info_top {
		display: flex;
		align-items: center;
		z-index: 999;
		position: relative;
	}

	.info_top text {
		font-size: 30rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: rgba(255, 255, 255, 1);
		line-height: 34rpx;
		margin-right: 20rpx;
	}

	.info_top image {
		width: 40rpx;
		height: 40rpx;
		margin-right: 14rpx;
	}

	.info_top .info_top_zi {
		height: 32rpx;
		background: var(--color_halo);
		border-radius: 6rpx 6rpx 6rpx 6rpx;
		border: 1rpx solid var(--color_main);
		font-size: 22rpx;
		color: var(--color_main);
		display: flex;
		align-items: center;
		padding: 0 10rpx;
		padding-left: 8rpx;
	}

	.info_bot {
		display: flex;
		align-items: center;
		margin-top: 13rpx;
	}

	.info_bot image {
		width: 40rpx;
		height: 40rpx;
		margin-right: 8rpx;
	}

	.info_bot text {
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: rgba(255, 255, 255, 1);
		line-height: 32rpx;
	}

	.info_bot_gong {
		padding-right: 20rpx;
	}

	.info_bot_gong .info_bot_text {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 1);
		line-height: 32rpx;
	}

	.store_code {
		width: 710rpx;
		height: 283rpx;
		background: rgba(255, 255, 255, 1);
		border-radius: 15rpx;
		margin: 30rpx 0 20rpx;
		z-index: 999;
		position: relative;
	}

	.store_code_top {
		/* width: 100%; */
		height: 89rpx;
		padding: 0 20rpx;
		box-sizing: border-box;
		display: flex;
		z-index: 999;
		justify-content: space-between;
		align-items: center;
		border-bottom: 1rpx solid #f2f2f2;
		border-radius: 15rpx 15rpx 0 0;
	}

	.store_code_top text {
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: rgba(45, 45, 45, 1);
		line-height: 32rpx;
	}

	.store_code_top image {
		width: 35rpx;
		height: 35rpx;
	}

	.store_code_des {
		width: 100%;
		height: 193rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		padding: 25rpx 0;
		box-sizing: border-box;
	}

	.des_pre {
		display: flex;
		align-items: center;
		padding-left: 20rpx;
		box-sizing: border-box;
	}

	.des_pre .des_pre_text {
		font-size: 26rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: rgba(102, 102, 102, 1);
		line-height: 30rpx;
	}

	.company_related {
		width: 710rpx;
		/* height:476rpx; */
		padding-bottom: 29rpx;
		background: rgba(255, 255, 255, 1);
		border-radius: 15rpx;
		z-index: 999;
		position: relative;
	}

	.related_pre {
		/* width: 100%; */
		/* height: 89rpx; */
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-left: 20rpx;
		padding-right: 20rpx;
		padding-top: 30rpx;
		padding-bottom: 30rpx;
		box-sizing: border-box;
		border-bottom: 1rpx solid #f2f2f2;
	}

	.related_pre .right_arrow_img {
		width: 12rpx;
		height: 21rpx;
		margin-left: 14rpx;
	}

	.related_pre text:nth-child(1) {
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: rgba(45, 45, 45, 1);
		line-height: 32rpx;
	}

	.related_pre text:nth-child(2) {
		font-size: 26rpx;
		font-family: PingFang SC;
		font-weight: 400;
		color: rgba(102, 102, 102, 1);
		line-height: 32rpx;
		width: 500rpx;
		/* white-space: nowrap; */
		text-overflow: ellipsis;
		overflow: hidden;
		word-break: break-all;
		text-align: right;
	}

	.related_pre_time {
		width: 470rpx !important;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		margin-left: 40rpx;
	}

	.related_yewu {
		display: flex;
		flex-direction: column;
		padding: 0 20rpx;
		box-sizing: border-box;
	}

	.related_yewu text:nth-child(1) {
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: rgba(45, 45, 45, 1);
		line-height: 32rpx;
		display: flex;
		height: 88rpx;
		padding: 32rpx 0 0 0;
		box-sizing: border-box;
	}

	.related_yewu text:nth-child(2) {
		font-size: 26rpx;
		font-family: PingFang SC;
		font-weight: 400;
		color: rgba(102, 102, 102, 1);
		line-height: 36rpx;
	}

	.mask {
		width: 750rpx;
		height: 100%;
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		margin: 0 auto;
		background: #000;
		z-index: 9000;
		opacity: 0.5;
	}

	/* 弹出层 */
	.modalDlg {
		width: 620rpx;
		height: 650rpx;
		background: rgba(255, 255, 255, 1);
		border: 1px solid rgba(187, 187, 187, 1);
		border-radius: 15px;
		position: fixed;
		top: 359rpx;
		left: 0;
		right: 0;
		z-index: 9999;
		margin: 0 auto;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.modalDlg text:nth-child(1) {
		font-size: 30rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: rgba(45, 45, 45, 1);
		line-height: 32rpx;
		margin: 48rpx 0 20rpx;
	}

	.modalDlg text:nth-child(2) {
		font-size: 26rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: rgba(102, 102, 102, 1);
		line-height: 32rpx;
	}

	.modalDlg image {
		width: 388rpx;
		height: 389rpx;
		margin-top: 59rpx;
	}

	.control_popup {
		width: 100%;
		height: 700rpx;
		background: #ffffff;
		border-radius: 30rpx 30rpx 0rpx 0rpx;
		position: fixed;
		width: 100% !important;
		z-index: 20;
		bottom: 0;

		.popup_top {
			height: 96rpx;
			width: 100%;
			display: flex;
			padding: 0 30rpx 0 30rpx;
			align-items: center;
			border-bottom: 1rpx solid #f8f8f8;
			justify-content: space-between;

			text {
				font-size: 34rpx;
				color: #000000;
				font-family: PingFang SC;
				font-weight: bold;
				line-height: 32rpx;
			}

			.top_image {
				width: 36rpx !important;
				height: 36rpx !important;
			}
		}

		.control_list {
			padding-bottom: calc(constant(safe-area-inset-bottom) + 10rpx);
			padding-bottom: calc(env(safe-area-inset-bottom) + 10rpx);
			box-sizing: border-box;
			height: 460rpx;

			.cancle_pre {
				width: 100%;
				padding: 29rpx 36rpx;
				padding-right: 18rpx;
				box-sizing: border-box;
				display: flex;
				justify-content: space-between;

				text {
					font-weight: bold;
					font-size: 32rpx;
					color: #000000;
					line-height: 32rpx;
				}
			}

			.week_name {
				margin: 46rpx 31rpx 22rpx;
				color: #000000;
				font-size: 32rpx;
				font-family: PingFang SC;
				font-weight: bold;
			}

			.time_list {
				margin: 0 31rpx;

				.time_item {
					color: #666666;
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: 500;
					margin-right: 30rpx;
				}
			}
		}

		.control_popup_btn {
			position: fixed;
			bottom: 34rpx;
			z-index: 30;
			display: flex;
			width: 100%;
			justify-content: center;

			.confrim_btn {
				width: 668rpx;
				height: 70rpx;
				background: var(--color_main_bg);
				border-radius: 44rpx;
				font-size: 36rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #ffffff;
				line-height: 70rpx;
				text-align: center;
			}
		}
	}
</style>