<template>
	<view :style="mix_diyStyle">
		<view class="container">
			<view v-if="isShow == true">
				<view class="special_care_wrap" v-if="!is_show_empty">
					<block v-for="(part, partIndex) in [storeListTop, storeListNormal]" :key="partIndex">
						<block v-if="part.length">
							<view class="shop_content">
								<view class="shop_tips">
									<view class="shop_follow_tag">
										<text>{{ partIndex == 0 ? '特别关注' : '普通关注' }} </text>
									</view>
								</view>
								<view class="shop_item special_care" v-for="(item, index) in part" :key="item.storeId"
									@touchstart="touchStart($event, item.storeId)" @touchmove="touchMove($event, item.storeId)"
									:style="{ left: is_show_btn && item.storeId == storeId ? '-160rpx' : '0' }"
									:class="{ shop_item_nogoods: item.goodsList.length == 0 }">
									<view class="shop_detail_wrap" @click="toShopDetail(item.storeId, item)">
										<view class="shop_img" :style="'background-image:url(' + item.storeLogo + ')'">
										</view>
										<view class="shop_info">
											<view class="shop_name">{{ item.storeName }}</view>
											<view class="attention_num">

												<view class="isOwnStore self_support_icon" v-if="item.isOwnStore == 1">
													自营</view>
												<text class="attention_text">{{ $L('关注') }}{{ item.followNumber }}</text>
											</view>
										</view>
									</view>
									<view class="shop_img_wrap">
										<view class="shop_goods_box" v-for="(goodsItem, goodsIndex) in item.goodsList" :key="goodsIndex"
											v-if="goodsIndex < 3" @click="toGoodsDetail(goodsItem.defaultProductId, goodsItem.goodsId)">
											<view class="shop_goods_pic" :style="'background-image:url(' + goodsItem.mainImage + ')'"></view>
											<view class="shop_goods_price">
												<text class="small_price">￥</text><text class="big_price">{{ $getPartNumber(goodsItem.goodsPrice,
		'int') }}</text><text class="small_price">{{ $getPartNumber(goodsItem.goodsPrice,
		'decimal') }}</text>
											</view>
										</view>
									</view>

									<view class="operate_btn_wrap" :class="{ operate_btn_wrap_nogoods: item.goodsList == 0 }">
										<view class="special_focus_btn operate_btn" @click="setSpecialFocus(item.followId, item.isTop)"
											v-if="item.isTop == 0" :style="{ height: item.goodsList.length == 0 ? '81rpx' : '162rpx' }">
											<view class="">{{ $L('设为') }}</view>
											<view class="">{{ $L('特别关注') }}</view>
										</view>
										<view class="cancel_focus_btn operate_btn cancel_special_focus_btn"
											@click="cancelFocus(item.followId, item.isTop, 'special')" v-if="item.isTop == 1"
											:style="{ height: item.goodsList.length == 0 ? '81rpx' : '162rpx', background: 'var(--color_main)' }">
											<view class="">{{ $L('取消') }}</view>
											<view class="">{{ $L('特别关注') }}</view>
										</view>
										<view class="cancel_focus_btn operate_btn" @click="cancelFocus(item.storeId, item.isTop)"
											:style="{ height: item.goodsList.length == 0 ? '81rpx' : '162rpx' }">取消关注
										</view>
									</view>
								</view>
							</view>
						</block>
					</block>

				</view>
				<!-- 无关注店铺列表空页面 -->
				<view class="empty_page" v-if="is_show_empty">
					<image :src="imgUrl + 'empty_attention.png'" mode="aspectFit" class="empty_img"></image>
					<view class="empty_text">{{ $L('暂无关注') }}</view>
				</view>
				<recommendGoods ref="recomment_goods" v-if="!this.hasMore" />
				<loadingState :state="loadingState" v-if="recommendLen == 0" />
			</view>
		</view>
	</view>
</template>

<script>
import loadingState from '@/components/loading-state.vue'
import recommendGoods from '@/components/recommend-goods.vue'
export default {
	data () {
		return {
			is_show_btn: false, //是否展示右侧操作按钮
			startX: '',
			startY: '',
			is_show_empty: false, //是否展示空页面
			storeId: '',
			imgUrl: process.env.VUE_APP_IMG_URL,
			isShow: true,
			loadingState: 'first_loading',
			pageSize: 10,
			current: 1,
			loading: false, //是否加载数据
			hasMore: true, //是否还有数据
			storeListTop: [], //关注店铺列表
			storeListNormal: [],
			storeData: {}, //店铺数据
			recommendLen: 0, //推荐商品总数
			showState: false
		}
	},
	components: {
		loadingState,
		recommendGoods
	},
	onLoad () {
		setTimeout(() => {
			uni.setNavigationBarTitle({
				title: this.$L('关注店铺')
			})
		}, 0);

		this.getStoreList()
		// 父页面接收子组件recommend——goods.vue传过来的值
		uni.$on('recommendGoods', (options) => {
			this.recommendLen = JSON.parse(options.recommendLen)
		})
	},
	onShow () {
		if (this.showState) {
			this.recommendLen = 0
			this.current = 1
			this.getStoreList()
		}
	},
	onHide () {
		this.storeId = -1
	},
	onReachBottom () {
		if (!this.hasMore) {
			this.$refs.recomment_goods?.getMoreData()
		} else {
			if (this.hasMore) {
				this.getStoreList()
			}
		}
	},
	methods: {
		getStoreList () {
			let param = {}
			param.url = 'v3/member/front/followStore/list'
			param.data = {
				pageSize: this.pageSize,
				current: this.current
			}
			param.method = 'GET'
			this.loadingState = this.loadingState == 'first_loading' ? this.loadingState : 'loading'
			this.$request(param).then((res) => {
				if (res.state == 200) {
					this.storeData = res.data
					let storeListTop = res.data.storeList.filter(sto => sto.isTop == 1)
					let storeListNormal = res.data.storeList.filter(sto => sto.isTop == 0)
					if (this.current == 1) {
						this.storeListTop = storeListTop
						this.storeListNormal = storeListNormal
					} else {
						this.storeListTop = this.storeListTop.concat(storeListTop)
						this.storeListNormal = this.storeListNormal.concat(storeListNormal)
					}

					this.hasMore = this.$checkPaginationHasMore(res.data.pagination)
					if (this.hasMore) {
						this.current++
						this.loadingState = 'allow_loading_more'
					} else {
						this.loadingState = 'no_more_data'
					}
					if (this.storeListTop.length == 0 && this.storeListNormal == 0) {
						this.is_show_empty = true
					} else {
						this.is_show_empty = false
					}
				}
				this.loading = false
			})
		},
		touchStart (e) {
			this.startX = e.touches[0].clientX
			this.startY = e.touches[0].clientY
		},
		touchMove (e, storeId) {
			this.storeId = storeId
			// 获得当前坐标
			this.currentX = e.touches[0].clientX
			this.currentY = e.touches[0].clientY
			const x = this.startX - this.currentX //横向移动距离
			const y = Math.abs(this.startY - this.currentY) //纵向移动距离，若向左移动有点倾斜也可以接受

			if (x > 5 && this.startX > 30) {
				//向左滑显示
				e.preventDefault()
				this.is_show_btn = true
			} else if (x < 5) {
				//向右滑隐藏
				this.is_show_btn = false
			}

			if (y > 150) {
				this.is_show_btn = false
			}
		},
		// 设为特别关注
		setSpecialFocus (followId, isTop) {
			let param = {}
			param.url = 'v3/member/front/followStore/updateSpecial'
			param.method = 'POST'
			param.data = {
				followId: followId,
				isTop: 1
			}
			this.$request(param).then((res) => {
				if (res.state == 200) {
					this.storeId = -1
					this.is_show_btn = false
					uni.showToast({
						title: this.$L('特别关注成功！')
					})
					setTimeout(() => {
						this.current = 1
						this.getStoreList()
						this.storeId = ''
						this.isShow = true
					}, 1500)
				}
			})
		},
		// 取消关注
		cancelFocus (followId, isTop, type) {
			let param = {}
			param.data = {}
			if (type != 'special') {
				//普通取消关注
				param.url = 'v3/member/front/followStore/update'
				param.data.isCollect = false
				param.data.storeIds = followId
			} else {
				//取消特别关注
				param.url = 'v3/member/front/followStore/updateSpecial'
				param.data.followId = followId
				param.data.isTop = 0
			}
			param.method = 'POST'
			this.$request(param).then((res) => {
				if (res.state == 200) {
					this.storeId = -1
					this.is_show_btn = false
					uni.showToast({
						title: this.$L('取消关注成功！')
					})
					setTimeout(() => {
						this.current = 1
						this.getStoreList()
						this.storeId = ''
						this.isShow = false
						this.isShow = true
					}, 1500)
				}
			})
		},
		// 去店铺详情
		toShopDetail (storeId, item) {
			this.showState = true
			if (item.shopType != 2) {
				this.$Router.push({
					path: '/standard/store/shopHomePage',
					query: {
						vid: storeId
					}
				})
			} else {
				this.$Router.push({
					path: '/extra/o2o/store/index',
					query: {
						storeId: storeId
					}
				})
			}
		},
		toGoodsDetail (productId, goodsId) {
			this.showState = true
			this.$Router.push({
				path: '/standard/product/detail',
				query: {
					productId,
					goodsId
				}
			})
		}
	}
}
</script>

<style lang="scss">
page {
	background-color: #f5f5f5;
}

::-webkit-scrollbar {
	display: none;
}

.container {
	width: 750rpx;
	box-sizing: border-box;
	overflow: hidden;
	margin: 0 auto;

	.special_care_wrap,
	.common_care_wrap {
		width: 100%;
		border-radius: 15rpx;
		padding: 20rpx;
		overflow: hidden;

		.shop_content {
			background: #ffffff;
			overflow: hidden;

			.shop_tips {
				padding-top: 30rpx;
				margin-top: 20rpx;

				.attention_icon {
					width: 137rpx;
					height: 40rpx;
				}

				.shop_follow_tag {
					width: 178rpx;
					height: 50rpx;
					text-align: center;
					line-height: 50rpx;
					background: var(--color_halo);
					font-size: 28rpx;
					font-weight: bold;
					color: var(--color_main);
					border-radius: 0 25rpx 25rpx 0;
				}
			}
		}

		.shop_item {
			position: relative;
			border-bottom: 1rpx solid #f2f2f2;
			transition: all 0.3s;
			background: #ffffff;
			padding-top: 30rpx;

			&.shop_item_nogoods {
				height: 202rpx;
			}

			.shop_detail_wrap {
				display: flex;
				width: 100%;
				height: 80rpx;
				padding: 0 20rpx;
				box-sizing: border-box;
				position: relative;
				margin-bottom: 30rpx;

				.shop_img {
					background-position: center center;
					background-repeat: no-repeat;
					background-size: cover;
					width: 80rpx;
					height: 80rpx;
					margin-right: 20rpx;
					border-radius: 50%;
					background-color: #f8f8f8;
				}

				.shop_info {
					display: flex;
					flex-direction: column;
					justify-content: space-between;

					.shop_name {
						width: 330rpx;
						font-size: 30rpx;
						color: #2d2d2d;
						font-weight: bold;
						white-space: nowrap;
						text-overflow: ellipsis;
						overflow: hidden;
					}

					.attention_num {
						display: flex;
						align-items: center;

						.self_support_icon {
							width: 60rpx;
							height: 30rpx;
							margin-right: 10rpx;
							font-size: 20rpx;
							background: var(--color_main);
							color: #fff;
							text-align: center;
							line-height: 30rpx;
							border-radius: 15rpx;
						}

						.attention_text {
							font-size: 24rpx;
							color: #999;
						}
					}
				}

				.shop_more_icon {
					width: 6rpx;
					height: 30rpx;
					position: absolute;
					right: 20rpx;
					top: 0;
				}
			}

			.shop_img_wrap {
				padding: 0 20rpx 20rpx 20rpx;
				display: flex;

				.shop_goods_box {
					width: 216rpx;
					height: 216rpx;
					border-radius: 10rpx;
					position: relative;
					margin-right: 10rpx;

					.shop_goods_pic {
						background-position: center center;
						background-repeat: no-repeat;
						background-size: cover;
						width: 216rpx;
						height: 216rpx;
						background-color: #f8f8f8;
						border-radius: 10rpx;
					}

					.shop_goods_price {
						width: 216rpx;
						height: 36rpx;
						color: #fff;
						text-align: center;
						line-height: 36rpx;
						position: absolute;
						bottom: 0;
						left: 0;
						background-color: rgba(0, 0, 0, 0.3);
						border-radius: 0 0 10rpx 10rpx;
						display: inline-block;

						.small_price {
							font-size: 20rpx;
						}

						.big_price {
							font-size: 24rpx;
						}
					}
				}
			}

			.shop_img_wrap>view:last-child {
				margin-right: 0;
			}

			.operate_btn_wrap {
				position: absolute;
				top: 0;
				width: 160rpx;
				height: 374rpx;
				right: -160rpx;
				display: flex;
				flex-direction: column;
				justify-content: center;
				border-bottom: 1rpx solid #f2f2f2;
				background: #ffffff;

				&.operate_btn_wrap_nogoods {
					height: 202rpx;
				}

				.operate_btn {
					width: 160rpx;
					height: 162rpx;
					font-size: 28rpx;
					color: #fff;
					display: flex;
					justify-content: center;
					align-items: center;
					padding: 0 24rpx;
					box-sizing: border-box;
					transition: all 0.3s;
				}

				.special_focus_btn {
					background: var(--color_main) !important;
					flex-direction: column !important;
				}

				.cancel_focus_btn {
					background: var(--color_vice);
				}
			}
		}
	}
}

.empty_page {
	width: 750rpx;
	height: 543rpx;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	margin-top: 120rpx;

	.empty_img {
		width: 380rpx;
		height: 280rpx;
		margin-bottom: 24rpx;
	}

	.empty_text {
		font-size: 26rpx;
		color: #666;
	}
}

.cancel_special_focus_btn {
	flex-direction: column;
}

.common_care_wrap {
	margin-top: 20rpx;
}
</style>