<!-- 申请退款页面 -->
<template>
	<view class="content" :style="mix_diyStyle">
		<view class="apply_refund_content">
			<!-- 有批量选择商品返回的  批量商品 start -->
			<view class="batch_apply_list" v-if="batchSelList.length > 1">
				<scroll-view scroll-x class="batch_apply_pre" v-for="(item, index) in batchSelList" :key="index"
					@click="goProductDetail(item.productId, item.goodsId)">
					<view class="batch_apply_pre_image">
						<!-- <image :src="item.productImage" mode="aspectFit"></image> -->
						<!-- <coverImage :src="item.productImage" width="140" height="140"></coverImage> -->
						<view class="image" :style="'background-image:url(' + item.productImage + ')'"></view>
					</view>
					<view class="batch_apply_pre_num">*{{ item.currNum }}</view>
				</scroll-view>
			</view>
			<!-- 有批量选择商品返回的  批量商品 end -->
			<!-- 单个商品 start -->
			<view class="refund_goods" v-else @click="goProductDetail(orderProduct.productId, orderProduct.goodsId)">
				<view class="refund_goods_image">
					<!-- <image :src="orderProduct.productImage" mode="aspectFit"></image> -->
					<!-- <coverImage :src="orderProduct.productImage" width="140" height="140"></coverImage> -->
					<view class="image" :style="'background-image:url(' + orderProduct.productImage + ')'"></view>
				</view>
				<view class="refund_goods_des">
					<text class="refund_goods_name">{{ orderProduct.goodsName }}</text>
					<text class="refund_goods_spec" v-if="orderProduct.specValues">{{orderProduct.specValues}}</text>
				</view>
			</view>
			<!-- 单个商品 end -->

			<!--进入批量选择商品页面 start -->

			<view class="batch_refund" v-if="orderListLen > 1">
				<view class="batch_refund_title">
					{{
						serviceType == '0'
						? $L('批量退款')
						: serviceType == '1'
						  ? $L('批量退货')
						  : serviceType == '2'
							? $L('批量换货')
							: $L('批量退款')
					  }}
				</view>
				<view class="batch_refund_opt" @click="goBatchSel">
					<!-- 已选择商品  start-->
					<text v-if="tosource == 1 && batchSelList.length > 1">{{ $L('已选') }}{{ refundInfos.number }}{{ $L('件商品')}}</text>
					<!-- 已选择商品 end-->
					<!-- 未选择商品  start-->
					<text v-else>{{ $L('同订单商品可批量申请') }}</text>
					<!-- 未选择商品 end-->
					<image :src="imgUrl + 'order-detail/to_right.png'" mode="aspectFit"></image>
				</view>
			</view>

			<!--进入批量选择商品页面 end-->
			<view class="refund_reason_des">
				<!-- 由待发货详情页面进入 start-->
				<block v-if="sourceType == 'orderDetail'||sourceType == 'refundDetail'">
					<view class="refund_reason">
						<view class="refund_reason_left">
							<text>*</text>
							<text>{{ $L('退款原因') }}</text>
						</view>
						<view class="refund_reason_right" @click="reasonModel">
							<text>{{ reasonContent ? reasonContent : $L('请选择') }}</text>
							<image :src="imgUrl + 'order-detail/to_right.png'" mode="aspectFit"></image>
						</view>
					</view>
				</block>
				<!-- 由待发货详情页面进入 end-->

				<!-- 由选择服务页面进入 start-->
				<block v-if="sourceType == 'selecTService'">
					<!-- 仅退款 start -->
					<block v-if="serviceType == '0'">
						<view class="refund_reason">
							<view class="refund_reason_left">
								<text>*</text>
								<text>{{ $L('货物状态') }}</text>
							</view>
							<view class="refund_reason_right" @click="cargoStatusModel">
								<text>{{cargoStatusContent ? cargoStatusContent : '请选择'}}</text>
								<image :src="imgUrl + 'order-detail/to_right.png'" mode="aspectFit"></image>
							</view>
						</view>
						<view class="refund_reason">
							<view class="refund_reason_left">
								<text>*</text>
								<text>{{ $L('退款原因') }}</text>
							</view>
							<view class="refund_reason_right" @click="reasonModel">
								<text>{{ reasonContent ? reasonContent : '请选择' }}</text>
								<image :src="imgUrl + 'order-detail/to_right.png'" mode="aspectFit"></image>
							</view>
						</view>
					</block>
					<!-- 仅退款 end -->

					<!-- 退货退款 start -->
					<block v-if="serviceType == '1'">
						<view class="refund_reason">
							<view class="refund_reason_left">
								<text>*</text>
								<text>{{ $L('退货原因') }}</text>
							</view>
							<view class="refund_reason_right" @click="reasonModel">
								<text>{{ reasonContent ? reasonContent : '请选择' }}</text>
								<image :src="imgUrl + 'order-detail/to_right.png'" mode="aspectFit"></image>
							</view>
						</view>
					</block>
					<!-- 退货退款 end -->

					<!-- 换货 start -->
					<block v-if="serviceType == '2'">
						<view class="refund_reason">
							<view class="refund_reason_left">
								<text>*</text>
								<text>{{ $L('换货原因') }}</text>
							</view>
							<view class="refund_reason_right" @click="reasonModel">
								<text>{{ reasonContent }}</text>
								<image :src="imgUrl + 'order-detail/to_right.png'" mode="aspectFit"></image>
							</view>
						</view>
					</block>
					<!-- 换货 end -->
				</block>
				<!-- 由选择服务页面进入 end-->
			</view>
			<view class="refund_info">
				<!-- 仅退款可以修改退款金额，申请仅退款时，若选择未收货则不可修改退款金额，若选择已收货，则可以修改退款金额，为选择货物状态时，页面展示按照未收货进行展示； -->
				<block v-if="serviceType == '0' && cargoStatusCurrent == 1">
					<view class="refund_amount refund_amount_edit">
						<text class="refund_amout_title">{{ $L('退款金额') }}:￥</text>
						<view class="refund_amount_price">
							<input type="digit" v-model="returnAmount" :placeholder="returnAmount ? returnAmount : $L('请输入退款金额')" @blur="getReturnAmount" />
						</view>
					</view>
				</block>
				<block v-else>
					<view class="refund_amount">
						<text class="refund_amout_title">{{ $L('退款金额') }}:</text>
						<view class="refund_amount_price">
							<!-- 批量选择之后 -->
							<text v-if="tosource == 1">￥{{ refundInfos.maxReturnMoney }}</text>
							<!-- 单个商品 -->
							<text v-else>{{ $L('￥') }}{{ refundDetail.maxReturnMoney }}</text>
						</view>
					</view>
					<view class="refund_amount_limit">
						{{ $L('退款金额不可修改，最多') }}
						{{ $L('￥')}}
						{{
						  tosource == 1
						  ? refundInfos.maxReturnMoney
						  : refundDetail.maxReturnMoney
						}}
						<text v-if="refundInfos.containsFee && tosource == 1">{{ $L('(含运费') }}{{ refundInfos.returnExpressFee}}{{ $L('元)') }}</text>
						<text v-else-if="refundDetail.containsFee && tosource == 0">{{ $L('(含运费') }}{{ refundDetail.returnExpressFee}}{{ $L('元)') }}</text>
						<text v-if="(!refundInfos.containsFee && tosource == 1) ||(!refundDetail.containsFee && tosource == 0)">{{ $L('(不包含运费)') }}</text>
					</view>
				</block>
				<!-- 仅退款可以修改退款金额，退货和换货可以修改申请件数； -->
				<block v-if="(batchSelList.length == 1 && tosource == 1) || tosource == 0">
					<view class="refund_number1" v-if="serviceType == '1' || serviceType == '2'">
						<text class="refund_number1_title">{{ $L('申请件数') }}：</text>
						<view class="refund_number_edit">
							<text @click="editNum('reduce')">-</text>
							<input type="number" v-model="applyNum" @blur="editNum('edit', $event)" />
							<text @click="editNum('add')">+</text>
						</view>
					</view>
					<view class="refund_number" v-else>
						<text>{{ $L('申请件数') }}：</text>
						<text>{{tosource == 1 ? refundInfos.number : refundDetail.number}}</text>
					</view>
				</block>
				<view class="refund_instructions">
					<text>{{serviceType == '2' ? $L('换货说明:') : $L('退款说明:')}}</text>
					<input type="text" :value="refundInstructions" :placeholder="$L('请输入退款说明')" @input="refundDes"
						placeholder-style="color: #bbbbbb;"
						maxlength="200" />
				</view>
			</view>
			<view class="upload_voucher">
				<view class="upload_voucher_title">{{ $L('上传凭证') }}</view>
				<view class="upload_voucher_con1">
					<view class="upload_img" v-if="uploadFiles.length > 0" v-for="(item, index) in uploadFiles"
						:key="index">
						<view class="image" :style="'background-image:url(' + item + ')'" @click="viewImage(index)">
						</view>
						<image :src="imgUrl + 'order-detail/guanbi.png'" mode="aspectFit" @click="delImg(item, index)">
						</image>
					</view>
					<view class="upload_voucher_con" @click="uploadVoucher" v-if="uploadFiles.length < 5">
						<view class="upload_image">
							<image :src="imgUrl + 'order-detail/xiangji.png'" mode="aspectFit"></image>
							<text>{{ $L('添加图片') }}</text>
							<text class="upload_limit">{{ uploadFiles && uploadFiles.length }}/5</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="submit" @click="submit">
			<view class="text">
				{{ $L('提交') }}
			</view>
		</view>
		<!-- 退款原因弹框 -->
		<uni-popup ref="cancelPopup" type="bottom">
			<view class="cancel_popup">
				<view class="popup_top">
					<text>{{serviceType == '1' ? $L('退货原因') : $L('退款原因')}}</text>
					<image :src="imgUrl + 'order-detail/guanbi.png'" mode="aspectFit" @click="notCancel"></image>
				</view>
				<scroll-view class="uni-list cancel_list" scroll-y="true">
					<radio-group @change="radioChange">
						<label class="cancle_pre" v-for="(item, index) in cancelList" :key="index">
							<text>{{ item.content }}</text>
							<radio :value="item.value" :checked="item.value == current" color="var(--color_main)"
								style="transform: scale(0.8); margin-right: 0" />
						</label>
					</radio-group>
				</scroll-view>
				<view class="cancel_popup_btn">
					<text class="confrim_btn" @click="confirmRefund()">{{$L('确定')}}</text>
				</view>
			</view>
		</uni-popup>
		<!-- 货物状态弹框 -->
		<uni-popup ref="cargoStatusPopup" type="bottom">
			<view class="cancel_popup">
				<view class="popup_top">
					<text>{{ $L('货物状态') }}</text>
					<image :src="imgUrl + 'order-detail/guanbi.png'" mode="aspectFit" @click="notCancel"></image>
				</view>
				<view class="uni-list cancel_list">
					<radio-group @change="radioChangeStatus">
						<label class="cancle_pre" v-for="(item, index) in cargoStatuslList" :key="index">
							<text>{{ item.content }}</text>
							<radio :value="item.value" :checked="item.value == cargoStatusCurrent"
								color="var(--color_main)" style="transform: scale(0.8); margin-right: 0" />
						</label>
					</radio-group>
				</view>
				<view class="cancel_popup_btn">
					<text class="confrim_btn" @click="confirmCargoStatus()">{{$L('确定')}}</text>
				</view>
			</view>
		</uni-popup>

		<uni-popup ref="popup" type="dialog">
			<uni-popup-dialog type="input" :title="$L('提示')" :content="$L('确定退款?')" :duration="2000"
				@confirm="goSubmit('confirm')">
			</uni-popup-dialog>
		</uni-popup>
	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex'
	import recommendGoods from '@/components/recommend-goods.vue'
	import uniPopup from '@/components/uni-popup/uni-popup.vue'
	import uniPopupMessage from '@/components/uni-popup/uni-popup-message.vue'
	import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue'
	import {
		multifileUpload
	} from '@/utils/common.js'
	let startY = 0,
		moveY = 0,
		pageAtTop = true
	export default {
		components: {
			recommendGoods,
			uniPopup,
			uniPopupMessage,
			uniPopupDialog
		},
		data() {
			return {
				imgUrl: process.env.VUE_APP_IMG_URL,
				coverTransform: 'translateY(0px)',
				coverTransition: '0s',
				moving: false,
				orderProductId: '', //订单明细id
				orderRefundDet: {}, //拒绝的订单
				orderProduct: {}, //拒绝订单的商品
				orderRefundOrder: {}, //拒绝订单的详情信息
				cancelList: [], //退款原因列表
				current: '0', //退款原因当前点击的是第0项
				uploadFiles: [], //上传凭证，凭证数组
				upLoadFile: [], //给后台传的上传凭证
				refundInstructions: '', //退款说明
				reasonContent: '', //退款原因的内容
				reasonId: -1, //退款原因当前点击的原因id
				cargoStatusCurrent: '0', //收货状态当前点击的是第0项
				cargoStatusCurId: -1, //货物状态id
				cargoStatusContent: '', //货物状态内容
				cargoStatuslList: [
					//货物状态
					{
						content: this.$L('未收到货'),
						value: '0',
						cargoStatusCurId: 0
					},
					{
						content: this.$L('已收到货'),
						value: '1',
						cargoStatusCurId: 1
					}
				],
				serviceType: '', //服务类型   0： 仅退款（无需退货） 1 ： 退货退款  2：换货
				sourceType: '', //页面的来源，上一页为   selecTService由选择服务页面进入   由待发货详情页面进入
				applyNum: 1, //申请件数
				batchSelList: [], //选中的商品列表
				returnAmount: '', //输入的退款金额
				refundDetail: {}, //计算后的退货订单信息
				refundInfos: {}, //从售后商品选择返回的同一个订单的批量选择计算后的退款金额信息
				tosource: '0',
				orderListLen: 1, //同订单的订单长度
				batchGoods: [] //售后商品选择页面传递的值，默认为空
			}
		},
		async onLoad(option) {
			setTimeout(() => {
				uni.setNavigationBarTitle({
					title: this.$L('申请退款')
				})
			}, 0);

			this.orderProductId = this.$Route.query.orderProductId
			this.serviceType = this.$Route.query.serviceType //服务类型
			this.sourceType = this.$Route.query.sourceType //页面来源
			this.initData()
			this.getOrderDetail()
			this.getCancelList()
		},
		onShow() {
			let that = this
			let pages = getCurrentPages()
			let currPage = pages[pages.length - 1] //当前页面
			let refundInfos = currPage.$vm.refundInfos //下一个页面传过来的值   批量选择后的退款金额信息
			let tosource = currPage.$vm.tosource || '0'
			that.tosource = tosource
			if (refundInfos) {
				this.refundInfos = JSON.parse(JSON.stringify(refundInfos))
			} else {
				this.refundInfos = {}
			}
			let batchSelList = currPage.$vm.batchSelList //下一个页面传过来的 选中的商品列表
			this.batchSelList = batchSelList || []
			this.batchGoods = currPage.$vm.batchGoods || [] //下一个页面穿过来的，商品列表信息
		},
		// #ifndef MP
		onNavigationBarButtonTap(e) {
			const index = e.index
			if (index === 0) {
				this.navTo('/pages/set/set')
			} else if (index === 1) {
				//app-1-start








				//app-1-end
				this.$Router.push('/pages/notice/notice')
			}
		},
		// #endif
		computed: {
			...mapState(['hasLogin', 'userInfo'])
		},
		methods: {
			initData() {},

			/**
			 * 统一跳转接口,拦截未登录路由
			 * navigator标签现在默认没有转场动画，所以用view
			 */
			navTo(url) {
				if (!this.hasLogin) {
					let urls = this.$Route.path
					const query = this.$Route.query
					uni.setStorageSync('fromurl', {
						url: urls,
						query
					})
					url = '/pages/public/login'
				}
				this.$Router.push(url)
			},

			viewImage(index) {
				uni.previewImage({
					// 预览图片  图片路径必须是一个数组 => ["http://192.168.100.251:8970/6_1597822634094.png"]
					current: index,
					urls: this.uploadFiles
				})
			},


			//获取售后订单货品详情
			getOrderDetail() {
				let param = {}
				param.url = 'v3/business/front/after/sale/apply/getOrderProductDetail'
				param.method = 'GET'
				param.data = {}
				param.data.orderProductId = this.orderProductId //订单货品id
				this.$request(param)
					.then((res) => {
						if (res.state == 200) {
							let [result] = res.data
							this.orderProduct = result
							if (this.orderProduct) {
								this.getRefundDetail()
								this.getOrderProductList()
							}
						} else {
							this.$api.msg(res.msg)
						}
					})
			},

			//获取售后改商品的其他列表
			getOrderProductList() {
				let param = {}
				param.url = 'v3/business/front/after/sale/apply/getOrderProductList'
				param.method = 'GET'
				param.data = {}
				param.data.orderProductId = this.orderProductId //订单货品id
				param.data.orderSn = this.orderProduct.orderSn
				this.$request(param).then((res) => {
					if (res.state == 200) {
						let result = res.data.filter(ba => ba.afsButton == this.orderProduct.afsButton)
						this.orderListLen = result.length
					} else {
						this.$api.msg(res.msg)
					}
				})
			},


			//获取计算售后退款信息 计算结果为此次最多可退金额
			getRefundDetail() {
				let param = {}
				param.url = 'v3/business/front/after/sale/apply/countReturnMoney'
				param.method = 'GET'
				param.data = {}
				param.data.orderSn = this.orderProduct.orderSn
				//单个商品
				let orderProductInfos = ''
				orderProductInfos =
					this.orderProduct.orderProductId + '-' + this.orderProduct.productNum
				param.data.orderProductInfos = orderProductInfos //退换的订单货品列表，格式为：id1-num1,id2-num2...num为空时表示此订单货品全部退换
				this.$request(param)
					.then((res) => {
						if (res.state == 200) {
							let result = res.data
							this.refundDetail = result
							this.returnAmount = '' + result.maxReturnMoney
							this.applyNum = this.refundDetail.number
						} else {
							this.$api.msg(res.msg)
						}
					})
					.catch((e) => {
						//异常处理
					})
			},
			//打开退款原因弹框
			reasonModel() {
				if (
					this.sourceType == 'selecTService' &&
					this.serviceType == '0' &&
					!this.cargoStatusContent
				) {
					uni.showToast({
						title: this.$L('请选择货物状态'),
						icon: 'none',
						duration: 700
					})
				} else {
					this.$refs.cancelPopup.open()
				}
			},
			//货物状态弹框
			cargoStatusModel() {
				this.$refs.cargoStatusPopup.open()
			},
			//获取订单退款原因列表
			getCancelList() {
				let param = {}
				param.url = 'v3/system/front/reason/list'
				param.method = 'GET'
				param.data = {}
				//原因类型：101-违规下架；102-商品审核拒绝；103-入驻审核拒绝；104-会员取消订单；105-仅退款-未收货；106-仅退款-已收货；107-退款退货原因；108-商户取消订单
				if (this.sourceType == 'orderDetail' || this.sourceType == 'refundDetail') {
					//待发货
					param.data.type = 105
				} else if (this.sourceType == 'selecTService') {
					//选择服务进入
					if (this.serviceType == '0') {
						//仅退款无需退货
						if (this.cargoStatusCurrent == '0') {
							//未收到货
							param.data.type = 105
						} else if (this.cargoStatusCurrent == '1') {
							param.data.type = 106
						} else {
							param.data.type = 106
						}
					} else if (this.serviceType == '1') {
						//退货退款
						param.data.type = 107
					} else if (this.serviceType == '2') {
						//换货
						// param.data.type =  换货类型暂无
					}
				}
				this.$request(param)
					.then((res) => {
						if (res.state == 200) {
							this.cancelList = res.data || []
							this.cancelList &&
								this.cancelList.map((item, index) => (item.value = '' + index))
							this.reasonId = this.reasonId ?
								this.reasonId :
								this.cancelList[0].reasonId
							this.cancelList.map((item, index) => {
								if (item.reasonId == this.reasonId) {
									this.current = item.value = '' + index
								} else {
									// if(this.current == '0'){
									// }else{}
									// this.current = '0';
								}
							})
						} else {
							this.$api.msg(res.msg)
						}
					})
					.catch((e) => {
						//异常处理
					})
			},
			//取消原因单选框切换
			radioChange(e) {
				for (let i = 0; i < this.cancelList.length; i++) {
					if (this.cancelList[i].value === e.target.value) {
						this.reasonId = this.cancelList[i].reasonId
						this.reasonContent = this.cancelList[i].content
						this.current = this.reasonId != -1 ? i : 0
						break
					}
				}
			},
			//确定退款原因的选择
			confirmRefund() {
				this.cancelList.map((item, index) => {
					if (this.reasonId == -1) {
						this.reasonId = this.cancelList[0].reasonId
						this.reasonContent = this.cancelList[0].content
						this.current = '0'
					}
				})

				this.$refs.cancelPopup.close()
			},
			//货物状态的单选按钮的切换
			radioChangeStatus(e) {
				this.current = -1
				this.reasonContent = ''
				for (let i = 0; i < this.cargoStatuslList.length; i++) {
					if (this.cargoStatuslList[i].value === e.target.value) {
						this.cargoStatusCurId = this.cargoStatuslList[i].cargoStatusCurId
						this.cargoStatusContent = this.cargoStatuslList[i].content
						this.cargoStatusCurrent = this.cargoStatusCurId != -1 ? i : '0'
						break
					}
				}
			},
			//确定货物状态的选择
			confirmCargoStatus() {
				this.cargoStatuslList.map((item, index) => {
					if (this.cargoStatusCurId == -1) {
						this.cargoStatusCurId = this.cargoStatuslList[0].cargoStatusCurId
						this.cargoStatusContent = this.cargoStatuslList[0].content
						this.cargoStatusCurrent = '0'
					}
				})
				this.getCancelList()
				this.$refs.cargoStatusPopup.close()
			},
			//关闭退款弹框.货物状态的弹框
			notCancel() {
				this.$refs.cancelPopup.close()
				this.$refs.cargoStatusPopup.close()
			},
			//上传凭证
			uploadVoucher() {
				let that = this
				// 从相册选择5张图
				uni.chooseImage({
					count: 5,
					sourceType: ['album', 'camera'],

					sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有

					success: function(res) {
						setTimeout(() => {
							uni.showLoading({
								title: that.$L('上传中'),
								mask: true
							})
						})
						if (res.tempFiles[0].size > 20971520) {
							uni.showToast({
								title: that.$L('超出了文件限制20M'),
								icon: 'none',
								duration: 700
							})
							uni.hideLoading()
						} else {
							let params = {
								url: process.env.VUE_APP_API_URL + 'v3/oss/front/upload',
								name: 'file',
								formData: {
									source: 'afterSale'
								},
								header: {
									Authorization: 'Bearer ' + that.userInfo.member_access_token
								},
							}
							let tmp = res.tempFilePaths
							if (that.uploadFiles.length + res.tempFilePaths.length > 5) {
								tmp.splice(
									5 - that.uploadFiles.length,
									that.uploadFiles.length + res.tempFilePaths.length - 5
								)
							}

							multifileUpload({
								fileArray: tmp,
								params,
								success: function(result) {
									that.uploadFiles = that.uploadFiles.concat(
										result.map((item) => {
											return item.data.url
										})
									) //绝对路径用于前端显示
									that.upLoadFile = that.upLoadFile.concat(
										result.map((item) => {
											return item.data.path
										})
									) //相对路径用于给后台传值
									uni.hideLoading()
								},
								fail: function(error) {
									uni.hideLoading()
									if (error.status === 200) {
										return
									}
									uni.showToast({
										title: error.msg,
										icon: 'none',
										duration: 700
									})
								}
							})
							that.$forceUpdate()
						}
					}
				})
			},
			//金额的校验
			isNumber(value) {
				var regPos = /^\d+(\.\d+)?$/ //非负浮点数
				var regPos1 = / ^\d+$/ //正整数
				if (regPos.test(value) || regPos1.test(value)) {
					return true
				} else {
					return false
				}
			},
			//退款输入的退款金额
			getReturnAmount(e) {
				let that = this
				let value = Number(e.detail.value)
				let maxReturnMoney = Number(that.refundDetail.maxReturnMoney)
				let isVal = that.isNumber(value)
				if (!isVal) {
					uni.showToast({
						title: that.$L('请输入正确的数字'),
						icon: 'none',
						duration: 700
					})
					that.returnAmount = ''
				} else {
					if (value >= maxReturnMoney) {
						that.returnAmount = '' + maxReturnMoney.toFixed(2)
					} else if (value < 0.01) {
						that.returnAmount = '' + 0.01
					} else {
						that.returnAmount = '' + value.toFixed(2)
					}
				}
			},
			//退款说明
			refundDes(e) {
				let that = this
				that.refundInstructions = e.detail.value
			},
			//申请件数的修改
			editNum(type, e) {
				let that = this
				if (that.serviceType == '1' || that.serviceType == '2') {
					if (!e && type == 'add') {
						that.applyNum++
					} else if (!e && type == 'reduce') {
						if (that.applyNum <= 1) {
							that.applyNum = 1
							this.$api.msg(that.$L('不能低于最低申请件数'))
						} else {
							that.applyNum--
						}
					} else if (e && type == 'edit') {
						if (that.applyNum <= 1) {
							that.applyNum = 1
							this.$api.msg(that.$L('不能低于最低申请件数'))
						} else {
							that.applyNum = e.detail.value
						}
					}
					if (that.applyNum > that.refundDetail.number) {
						uni.showToast({
							title: that.$L('超过了已购买的最大申请件数'),
							icon: 'none',
							duration: 500
						})
						that.applyNum = that.refundDetail.number
					}
					this.getRefundMoney()
				}
			},
			//根据申请件数获取退款金额
			getRefundMoney() {
				let param = {}
				param.url = 'v3/business/front/after/sale/apply/getReturnMoney'
				param.method = 'GET'
				param.data = {}
				param.data.orderProductId = this.orderProduct.orderProductId
				param.data.applyNum = this.applyNum
				this.$request(param)
					.then((res) => {
						if (res.state == 200) {
							let result = res.data
							this.refundDetail.maxReturnMoney = result
						} else {
							this.$api.msg(res.msg)
						}
					})
					.catch((e) => {
						//异常处理
					})
			},
			//批量选择商品
			goBatchSel() {
				//申请件数可编辑
				let isEdit = 'true'
				if (this.serviceType == '1' || this.serviceType == '2') {
					isEdit = 'true'
				} else {
					//申请件数不可编辑
					isEdit = 'false'
				}
				this.$Router.push({
					path: '/standard/refund/batchSel',
					query: {
						orderSn: this.orderProduct.orderSn,
						orderProductId: this.orderProduct.orderProductId,
						afsState: this.orderProduct.afsButton,
						batchGoods: JSON.stringify(this.batchGoods)
					}
				})
			},
			//提交
			submit() {
				let that = this
				if ((that.sourceType == 'orderDetail' || that.sourceType == 'refundDetail') && that.reasonContent == '') {
					uni.showToast({
						title: that.$L('请选择退款原因'),
						duration: 500,
						icon: 'none'
					})
				} else if (that.sourceType == 'selecTService') {
					if (that.serviceType == 0 && that.reasonContent == '') {
						uni.showToast({
							title: that.$L('请选择退款原因'),
							duration: 500,
							icon: 'none'
						})
					} else if (that.serviceType == 1 && that.reasonContent == '') {
						uni.showToast({
							title: that.$L('请选择退货原因'),
							duration: 500,
							icon: 'none'
						})
					} else if (that.serviceType == '2' && that.reasonContent == '') {
						uni.showToast({
							title: that.$L('请选择换货原因'),
							duration: 500,
							icon: 'none'
						})
					} else if (that.cargoStatusContent == '' && that.serviceType == '0') {
						uni.showToast({
							title: that.$L('请选择货物状态'),
							duration: 500,
							icon: 'none'
						})
					} else {
						that.goSubmit('open')
					}
				} else {
					that.goSubmit('open')
				}
			},
			// 去提交
			goSubmit(type) {
				switch (type) {
					case 'open': {
						this.$refs.popup.open()
						break
					}
					case 'confirm': {
						this.$refs.popup.close()
						let productList = []
						this.$refs.popup.close()
						let that = this
						if (that.tosource != 1) {
							//单个商品 由待发货详情页面进入
							let orderProduct = {}
							orderProduct.orderProductId = that.orderProduct.orderProductId
							orderProduct.afsNum =
								that.serviceType == '1' || that.serviceType == '2' ?
								that.applyNum :
								that.orderProduct.productNum
							productList.push(orderProduct)
						} else {
							//批量申请 由批量选择商品页面进入
							productList =
								that.batchSelList &&
								that.batchSelList.map(function(item) {
									return {
										orderProductId: item.orderProductId,
										afsNum: item.currNum
									}
								})
						}
						let param = {}
						param.url = 'v3/business/front/after/sale/apply/submit'
						param.method = 'POST'
						param.header = {
							'content-type': 'application/json'
						}
						param.data = {}
						param.data.orderSn = that.orderProduct.orderSn
						// serviceType:'',  //服务类型   0： 仅退款（无需退货） 1 ： 退货退款  2：换货
						param.data.afsType = ""
						//申请类型（售后服务单类型，1-退货退款单（需关联处理退款金额），2-换货单，3-仅退款单）
						if (this.sourceType == 'selecTService') {
							//除了 serviceType为0时afsType为3 之外 afsType和serviceType 同等值
							param.data.afsType = that.serviceType == '0' ? 3 : that.serviceType
						} else {
							param.data.afsType = 3
						}
						param.data.applyReasonContent = that.reasonContent //申请售后服务原因，售后服务原因列表选择的内容
						param.data.afsDescription = that.refundInstructions //详细问题描述
						param.data.applyImage = that.upLoadFile.join(',') //申请提交图片,多个图片用英文逗号隔开
						param.data.productList = productList //订单货品列表
						if (that.sourceType == 'orderDetail' || that.sourceType == 'refundDetail') {
							param.data.goodsState = 0 //货物状态
						} else if (that.sourceType == 'selecTService') {
							if (that.serviceType == '0') {
								param.data.goodsState = that.cargoStatusCurrent
							} else if (that.serviceType == '1' || that.serviceType == '2') {
								param.data.goodsState = 1
							}
						}
						param.data.finalReturnAmount =
							that.tosource == 1 ?
							that.refundInfos.maxReturnMoney :
							that.serviceType == '0' ?
							that.returnAmount :
							that.refundDetail.maxReturnMoney //退款金额
						param.data = JSON.stringify(param.data)
						that.$request(param).then((res) => {
							if (res.state == 200) {
								res?.data.forEach(afsSn => {
									this.$sldStatEvent({
										behaviorType: 'returnApply',
										afsSn
									})
								})
								that.$api.msg(res.msg)
								let pages = getCurrentPages()
								if (that.sourceType == 'orderDetail' || that.sourceType == 'refundDetail') {
									let beforePage = pages[pages.length - 2] //上一页
									beforePage && beforePage.$vm.getOrderDetail() //更新上一页数据 订单详情页面
									this.$Router.back(1)
								} else {
									// 返回上两页，并更新上两页数据
									let beforePage = pages[pages.length - 3] //上两页
									this.$Router.back(2)
								}
							} else {
								that.$api.msg(res.msg)
							}
						})
						break
					}
				}
			},
			//去商品详情页
			goProductDetail(productId, goodsId) {
				this.$Router.push({
					path: '/standard/product/detail',
					query: {
						productId,
						goodsId
					}
				})
			},
			//删除图片
			delImg(curItem, curIndex) {
				this.uploadFiles.splice(curIndex, 1)
				this.upLoadFile.splice(curIndex, 1)
			}
		}
	}
</script>

<style lang="scss">
	page {
		background: $bg-color-split;
		width: 750rpx;
		margin: 0 auto;
	}

	uni-page-body {
		display: flex;
		height: 100%;
	}

	.content {
		position: relative;
		background: #fff;
		margin-top: 20rpx;
		width: 100%;
		/* height: 100%; */

		.apply_refund_content {
			box-sizing: border-box;
			background: #ffffff;

			padding-bottom: calc(168rpx + env(safe-area-inset-bottom));

			/* height: 100%; */
			.refund_goods {
				display: flex;
				margin-left: 20rpx;
				padding: 20rpx 20rpx 20rpx 0;
				border-bottom: 1rpx solid #f0f0f0;

				.refund_goods_image {
					width: 140rpx;
					height: 140rpx;
					margin-right: 20rpx;
					background: #f3f3f3;
					border-radius: 14rpx;

					.image {
						background-position: center center;
						background-repeat: no-repeat;
						background-size: cover;
						width: 140rpx;
						height: 140rpx;
						border-radius: 14rpx;
					}
				}

				.refund_goods_des {
					display: flex;
					flex-direction: column;
					padding-top: 19rpx;

					.refund_goods_name {
						width: 530rpx;
						font-size: 28rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #333333;
						line-height: 39rpx;
						white-space: nowrap;
						text-overflow: ellipsis;
						overflow: hidden;
						word-break: break-all;
						margin-bottom: 17rpx;
					}

					.refund_goods_spec {
						font-size: 26rpx;
						font-family: PingFang SC;
						font-weight: 400;
						color: #949494;
						line-height: 30rpx;
					}
				}
			}

			.batch_apply_list {
				display: flex;
				align-items: center;
				border-bottom: 1rpx solid #f5f5f5;

				.batch_apply_pre {
					display: flex;
					flex-direction: column;
					padding: 0 20rpx;
					box-sizing: border-box;
					margin-bottom: 30rpx;
					margin-top: 20rpx;
					width: 180rpx;
					border-right: 1rpx solid #f5f5f5;

					.batch_apply_pre_image {
						width: 140rpx;
						height: 140rpx;
						background: #f3f3f3;
						border-radius: 14rpx;

						.image {
							background-position: center center;
							background-repeat: no-repeat;
							background-size: cover;
							width: 140rpx;
							height: 140rpx;
							border-radius: 14rpx;
						}
					}

					.batch_apply_pre_num {
						font-size: 26rpx;
						font-family: PingFang SC;
						font-weight: 400;
						color: #343434;
						line-height: 39rpx;
						margin-top: 20rpx;
						text-align: center;
					}
				}
			}

			.batch_refund {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 0 20rpx;
				box-sizing: border-box;
				height: 100rpx;

				.batch_refund_title {
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #333333;
					line-height: 39rpx;
				}

				.batch_refund_opt {
					display: flex;
					align-items: center;

					text {
						font-size: 26rpx;
						font-family: PingFang SC;
						font-weight: 400;
						color: #999999;
						line-height: 39rpx;
					}

					image {
						width: 14rpx;
						height: 24rpx;
						margin-left: 19rpx;
					}
				}
			}

			.refund_reason_des {
				border-top: 20rpx solid #f5f5f5;

				.refund_reason {
					display: flex;
					height: 100rpx;
					margin-left: 20rpx;
					padding-right: 20rpx;
					justify-content: space-between;
					align-items: center;
					border-bottom: 1rpx solid #ececec;

					.refund_reason_left {
						display: flex;
						align-items: center;
						font-size: 28rpx;
						font-family: PingFang SC;
						font-weight: 500;
						line-height: 39rpx;
						color: #333333;

						text:nth-child(1) {
							color: #ff0c17;
							margin-right: 5rpx;
						}
					}

					.refund_reason_right {
						display: flex;
						align-items: center;

						text {
							font-size: 26rpx;
							font-family: PingFang SC;
							font-weight: 400;
							color: #949494;
							line-height: 39rpx;
						}

						image {
							width: 14rpx;
							height: 24rpx;
							margin-left: 19rpx;
						}
					}
				}

				.refund_reason:nth-last-of-type(1) {
					border-bottom: none;
				}
			}

			.refund_info {
				border-top: 20rpx solid #f5f5f5;
				padding: 30rpx 20rpx 0rpx 20rpx;
				box-sizing: border-box;

				.refund_amount {
					display: flex;
					align-items: center;

					.refund_amout_title {
						font-size: 26rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #333333;
						line-height: 24rpx;
					}

					.refund_amount_price {
						font-size: 24rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #333333;
						line-height: 24rpx;
						display: inline-block;

						text:nth-child(2) {
							font-size: 26rpx;
						}

						input {
							font-size: 26rpx;
						}
					}
				}

				.refund_amount_edit {
					margin-bottom: 20rpx;
				}

				.refund_amount_limit {
					font-size: 24rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #bbbbbb;
					line-height: 24rpx;
					margin: 30rpx 0;
				}

				.refund_number {
					display: inline-block;
					font-size: 26rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #333333;
					line-height: 24rpx;

					text:nth-child(1) {
						margin-right: 10rpx;
					}
				}

				.refund_number1 {
					display: flex;
					align-items: center;

					.refund_number1_title {
						font-size: 26rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #333333;
						line-height: 24rpx;
					}

					.refund_number_edit {
						margin-right: 10rpx;
						display: flex;
						align-items: center;

						text {
							width: 51rpx;
							height: 50rpx;



							font-size: 24rpx;
							font-family: PingFang SC;
							font-weight: 400;
							color: #949494;
							line-height: 50rpx;
							border: 1rpx solid #e7e7e7;
							text-align: center;
						}

						input {
							height: 50rpx;
							width: 78rpx;
							font-size: 24rpx;
							font-family: PingFang SC;
							font-weight: 500;
							color: #2d2d2d;
							line-height: 50rpx;
							text-align: center;
							border-top: 1rpx solid #e7e7e7;
							border-bottom: 1rpx solid #e7e7e7;
						}
					}
				}

				.refund_instructions {
					display: flex;
					align-items: center;
					margin: 30rpx 0;

					text {
						font-size: 26rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #333333;
						line-height: 24rpx;
					}

					input {
						font-size: 26rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #949494;
						line-height: 24rpx;
						margin-left: 10rpx;
						width: 580rpx;
					}
				}
			}
		}

		.upload_voucher {
			border-top: 20rpx solid #f5f5f5;
			padding: 30rpx 20rpx;

			.upload_voucher_title {
				font-size: 26rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #333333;
				margin-bottom: 30rpx;
			}

			.upload_voucher_con1 {
				display: flex;
				flex-wrap: wrap;
				overflow-x: auto;
				overflow-y: hidden;
				white-space: nowrap;
				margin-bottom: 102rpx;
				/* 解决ios手机页面滑动卡顿问题 */
				-webkit-overflow-scrolling: touch;

				.upload_img {
					width: 160rpx;
					height: 170rpx;
					margin: 0 20rpx 20rpx 0;
					position: relative;
					padding-top: 10rpx;

					.image:nth-child(1) {
						background-position: center center;
						background-repeat: no-repeat;
						background-size: cover;
						width: 152rpx;
						height: 152rpx;
					}

					image:nth-child(2) {
						width: 30rpx;
						height: 30rpx;
						position: absolute;
						top: 0rpx;
						right: -15rpx;
					}
				}

				.upload_voucher_con {
					padding-top: 10rpx;

					.upload_image {
						width: 156rpx;
						height: 156rpx;
						border: 2rpx solid #eeeeee;
						display: flex;
						flex-direction: column;
						justify-content: center;
						align-items: center;

						image {
							width: 50rpx;
							height: 40rpx;
						}

						text {
							font-size: 26rpx;
							font-family: PingFang SC;
							font-weight: 400;
							color: #949494;
							margin-top: 17rpx;
						}

						.upload_limit {
							font-size: 20rpx;
							font-family: PingFang SC;
							font-weight: 400;
							color: #949494;
							margin-top: 9rpx;
						}
					}
				}
			}
		}

		.upload_voucher::-webkit-scrollbar {
			display: none;
		}

		.submit {
			width: 100%;
			position: fixed;
			bottom: 0;
			z-index: 9;
			background: #ffffff;
			display: flex;
			justify-content: center;
			align-items: center;
			height: 168rpx;

			.text {
				width: 668rpx;
				height: 88rpx;
				background: var(--color_main_bg);
				border-radius: 44rpx;
				font-size: 36rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #ffffff;
				line-height: 88rpx;
				text-align: center;
			}
		}

		.cancel_popup {
			width: 100%;
			height: 700rpx;
			background: #ffffff;
			border-radius: 15rpx 15rpx 0 0;
			position: fixed;
			width: 100% !important;
			z-index: 20;
			bottom: 0;

			.popup_top {
				height: 100rpx;
				width: 100%;
				display: flex;
				padding: 0 30rpx 0 30rpx;
				align-items: center;
				border-bottom: 1rpx solid #f8f8f8;
				justify-content: space-between;

				text {
					font-size: 32rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #343434;
					line-height: 32rpx;
				}

				image {
					width: 30rpx;
					height: 30rpx;
				}
			}

			.cancel_list {
				padding-bottom: 128rpx;
				box-sizing: border-box;
				height: 600rpx;

				.cancle_pre {
					width: 100%;
					padding: 29rpx 40rpx;
					box-sizing: border-box;
					display: flex;
					justify-content: space-between;

					text {
						font-size: 28rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #666666;
						line-height: 32rpx;
					}
				}
			}

			.cancel_popup_btn {
				position: fixed;
				bottom: 34rpx;
				z-index: 30;
				display: flex;
				width: 100%;
				justify-content: center;

				.confrim_btn {
					width: 668rpx;
					height: 70rpx;
					background: var(--color_main_bg);
					border-radius: 44rpx;
					font-size: 36rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #ffffff;
					line-height: 70rpx;
					text-align: center;
				}
			}
		}
	}
</style>