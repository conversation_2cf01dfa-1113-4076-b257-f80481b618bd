<template>
  <view class="banner-container" v-if="bannerList && bannerList.length">
    <swiper class="banner-swiper" circular :indicator-dots="true" :autoplay="true" :interval="3000" :duration="1000">
      <swiper-item v-for="(item, index) in bannerList" :key="index" @click="handleBannerClick(item)">
        <image class="banner-image" :src="imgUrl + 'goods_detail/test_banner.jpg'" mode="aspectFill"/>
      </swiper-item>
    </swiper>
  </view>
</template>

<script>
export default {
  name: 'productBanner',
  data() {
    return {
      imgUrl: '',
      bannerList: []
    }
  },
  created() {
    // 确保imgUrl正确获取
    try {
      this.imgUrl = process.env.VUE_APP_IMG_URL || ''
      // 测试数据
      this.bannerList = [{
        id: 1,
        uid: '123',
        url: 'https://live-game-test.shengu.info/character/question.html'
      }]
    } catch (e) {
      console.error('初始化banner数据失败:', e)
    }
  },
  methods: {
    handleBannerClick(item) {
      if (!item || !item.url) return
      
      const fullUrl = `${item.url}?uid=${item.uid}`
      
      // #ifdef H5
      window.open(fullUrl, '_blank')
      // #endif
      
      // #ifdef MP-WEIXIN
      // 确保pages/webview/webview页面存在
      uni.navigateTo({
        url: `/pages/webview/webview?url=${encodeURIComponent(fullUrl)}`,
        fail: (err) => {
          console.error('跳转失败:', err)
          // 如果跳转失败，可以尝试直接打开
          uni.setClipboardData({
            data: fullUrl,
            success: () => {
              uni.showModal({
                title: '提示',
                content: '链接已复制，请在浏览器中打开'
              })
            }
          })
        }
      })
      // #endif
    }
  }
}
</script>

<style lang="scss" scoped>
.banner-container {
  width: 100%;
  padding: 0 30rpx;
  margin: 20rpx 0;
  background-color: #fff;
  box-sizing: border-box;
  
  .banner-swiper {
    width: 100%;
    height: 160rpx;
    border-radius: 12rpx;
    overflow: hidden;
  }
  
  .banner-image {
    width: 100%;
    height: 100%;
    display: block;
  }
}
</style> 