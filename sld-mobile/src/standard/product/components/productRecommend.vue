<template>
  <view class="store_recommend" id="recommendedVideoList" v-if="shouldShowRecommend">
    <view class="store_recommend_top">
      <view class="store_recommend_title">{{ $L("种草推荐") }}</view>
      <view class="store_recommend_more" @click.stop="goInformation">
        <text>{{ $L("查看更多") }}</text>
        <image :src="imgurl + 'goods_detail/right_down.png'" mode="aspectFit" class="right_down"></image>
      </view>
    </view>
    <view class="store_recommend_list">
      <view class="store_recommend_pre" 
        v-for="(item, index) in displayVideoList" 
        :key="index"
        @click="goProductDetail(item)">
        <view class="store_reco_pre_image">
          <view class="image" :style="'background-image:url(' + item.videoImage + ')'"></view>
        </view>
        <view class="store_reco_pre_name">{{ item.videoName }}</view>
      </view>
      <!-- 发布入口 -->
      <!-- #ifdef H5 -->
      <view class="store_recommend_pre store_recommend_publish" @click="goPublish">
        <view class="store_reco_pre_image">
          <view class="publish_box">
            <view class="publish_icon">+</view>
            <text class="publish_text">发布种草</text>
          </view>
        </view>
      </view>
      <!-- #endif -->
      <!-- #ifdef MP-WEIXIN -->
      <view class="store_recommend_pre store_recommend_publish_mp" @click="goPublish">
        <view class="store_reco_pre_image">
          <view class="publish_box">
            <view class="publish_icon">+</view>
            <text class="publish_text">发布种草</text>
          </view>
        </view>
      </view>
      <!-- #endif -->
    </view>
  </view>
</template>

<script>
export default {
  name: 'productRecommend',
  props: {
    recommendedVideoList: {
      type: Array,
      default: () => []
    },
    imgurl: {
      type: String,
      required: true
    },
    goodsId: {
      type: [String, Number],
      default: ''
    },
    productId: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {}
  },
  computed: {
    shouldShowRecommend() {
      return this.recommendedVideoList && this.recommendedVideoList.length > 0 || !this.recommendedVideoList || this.recommendedVideoList.length === 0
    },
    displayVideoList() {
      return this.recommendedVideoList.slice(0, 2)
    }
  },
  methods: {
    goInformation() {
      // #ifdef MP
      uni.switchTab({
        url: '/pages/index/information'
      })
      // #endif
      // #ifdef H5
      this.$Router.push('/pages/index/information')
      // #endif
    },
    goProductDetail(item) {
      const path = item.videoType == 1 ? '/extra/svideo/svideoPlay' : '/extra/graphic/graphicDetail'
      // #ifdef MP-WEIXIN
      uni.navigateTo({
        url: `${path}?video_id=${item.videoId}&author_id=${item.authorId}`
      })
      // #endif
      // #ifdef H5
      this.$Router.push({
        path,
        query: {
          video_id: item.videoId,
          author_id: item.authorId
        }
      })
      // #endif
    },
    goPublish() {
      // #ifdef MP-WEIXIN
      // 先将商品ID存储到本地，因为 switchTab 不能带参数
      if (this.goodsId) {
        console.log('准备保存商品信息到本地存储，goodsId:', this.goodsId, 'productId:', this.productId);
        
        // 先移除旧的存储
        uni.removeStorageSync('temp_publish_goodsId');
        uni.removeStorageSync('temp_publish_productId');
        
        // 设置新的存储
        uni.setStorageSync('temp_publish_goodsId', this.goodsId);
        uni.setStorageSync('temp_publish_productId', this.productId);
        
        // 立即验证存储是否成功
        const savedGoodsId = uni.getStorageSync('temp_publish_goodsId');
        const savedProductId = uni.getStorageSync('temp_publish_productId');
        console.log('存储验证 - goodsId:', savedGoodsId, 'productId:', savedProductId);
        
        if (!savedGoodsId || !savedProductId) {
          console.error('存储验证失败，重试存储');
          // 重试一次
          uni.setStorageSync('temp_publish_goodsId', this.goodsId);
          uni.setStorageSync('temp_publish_productId', this.productId);
        }
      }
      
      // 确保存储成功后再跳转
      setTimeout(() => {
        uni.switchTab({
          url: '/pages/graphic/graphicRelease',
          success: () => {
            console.log('跳转成功，最终存储状态 - goodsId:', uni.getStorageSync('temp_publish_goodsId'), 
              'productId:', uni.getStorageSync('temp_publish_productId'));
          },
          fail: (err) => {
            console.error('跳转失败:', err);
            uni.showToast({
              title: '跳转失败',
              icon: 'none'
            });
          }
        });
      }, 100);
      // #endif
      
      // #ifdef H5
      console.log('H5端跳转发布页面，商品信息：', {
        goodsId: this.goodsId,
        productId: this.productId
      });
      
      // 清除旧的缓存
      uni.removeStorageSync('graphicReleaseContent');
      
      // 使用replace而不是push，避免返回时出现重复页面
      this.$Router.replace({
        path: '/pages/graphic/graphicRelease',
        query: {
          goodsId: this.goodsId,
          productId: this.productId,
          labelId: 2
        }
      });
      // #endif
    }
  }
}
</script>

<style lang="scss" scoped>
.store_recommend {
  width: 750rpx;
  background: #fff;
  margin-top: 20rpx;
  padding: 30rpx 0;
  
  .store_recommend_top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20rpx;
    margin-bottom: 30rpx;
    
    .store_recommend_title {
      font-size: 32rpx;
      font-family: PingFang SC;
      font-weight: bold;
      color: #333;
    }
    
    .store_recommend_more {
      display: flex;
      align-items: center;
      font-size: 26rpx;
      color: #999;
      
      image {
        width: 24rpx;
        height: 24rpx;
        margin-left: 10rpx;
      }

      .right_down {
        width: 36rpx;
        height: 36rpx;
        margin-left: 10rpx;
      }
    }
  }
  
  .store_recommend_list {
    display: flex;
    padding: 0 20rpx;

    .store_recommend_pre {
      margin-right: 20rpx;
      width: 223rpx;

      &:last-child {
        margin-right: 0;
      }

      .store_reco_pre_image {
        position: relative;
        width: 223rpx;
        height: 223rpx;
        border-radius: 15rpx;
        overflow: hidden;

        .image {
          background-position: center center;
          background-repeat: no-repeat;
          background-size: cover;
          width: 223rpx;
          height: 223rpx;
          border-radius: 15rpx;
        }

        .store_reco_pre_price {
          width: 223rpx;
          height: 40rpx;
          background: rgba(0, 0, 0, 0.3);
          border-radius: 0 0 15rpx 15rpx;
          position: absolute;
          bottom: 0;
          left: 0;
          font-size: 20rpx;
          font-family: PingFang SC;
          font-weight: 500;
          color: #ffffff;
          text-align: center;
          line-height: 40rpx;
        }
      }

      .store_reco_pre_name {
        font-size: 28rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #2d2d2d;
        line-height: 36rpx;
        width: 223rpx;
        text-overflow: -o-ellipsis-lastline;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        margin-top: 20rpx;
      }
    }

    /* #ifdef H5 */
    .store_recommend_publish {
      .store_reco_pre_image {
        background: #ffffff;
        border-radius: 15rpx;
        box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1);
        
        .publish_box {
          width: 223rpx;
          height: 223rpx;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;

          .publish_icon {
            width: 60rpx;
            height: 60rpx;
            background: var(--color_main);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40rpx;
            color: #fff;
            line-height: 40rpx;
            padding-bottom: 4rpx;
          }

          .publish_text {
            font-size: 24rpx;
            color: #222;
            margin-top: 16rpx;
          }
        }
      }
    }
    /* #endif */

    /* #ifdef MP */
    .store_recommend_publish_mp {
      .store_reco_pre_image {
        background-color: #ffffff !important;
        border-radius: 15rpx !important;
        box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.1) !important;
        
        .publish_box {
          width: 223rpx;
          height: 223rpx;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;

          .publish_icon {
            width: 60rpx;
            height: 60rpx;
            background: var(--color_main);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40rpx;
            color: #fff;
            line-height: 40rpx;
            padding-bottom: 4rpx;
          }

          .publish_text {
            font-size: 24rpx;
            color: #222;
            margin-top: 16rpx;
          }
        }
      }
    }
    /* #endif */
  }
}
</style> 