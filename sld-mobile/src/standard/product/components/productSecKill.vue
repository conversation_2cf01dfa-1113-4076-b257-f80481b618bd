<template>
  <view class="second_kill" v-if="secKillInfo && (secKillInfo.state == 1 || secKillInfo.state == 2)">
    <view class="second_kill_con_box">
      <view class="second_kill_con" :style="'background-image:url(' + imgUrl + 'goods_detail/second_kill_bg_1.png);background-size:100% 100%;background-repeat:no-repeat;'">
        <!-- 活动进行中 -->
        <view class="second_kill_left" v-if="secKillInfo.state == 2">
          <view class="second_kill_goods_price" v-if="splitPrice(secKillInfo.seckillPrice)[0] && splitPrice(secKillInfo.seckillPrice)[1]">
            <text>￥</text><text>{{ splitPrice(secKillInfo.seckillPrice)[0] }}</text>.<text>{{ splitPrice(secKillInfo.seckillPrice)[1] }}</text>
          </view>
          <view class="second_kill_price line_through">
            <text>{{ $L("原价") }}:￥{{ toFix(secKillInfo.productPrice) }}</text>
          </view>
        </view>
        <!-- 活动未开始 -->
        <view class="second_kill_left" v-else>
          <view class="second_kill_goods_price" v-if="splitPrice(secKillInfo.productPrice)[0] && splitPrice(secKillInfo.productPrice)[1]">
            <text>￥</text><text>{{ splitPrice(secKillInfo.productPrice)[0] }}</text>.<text>{{ splitPrice(secKillInfo.productPrice)[1] }}</text>
          </view>
          <view class="second_kill_price">
            <text>{{ $L("秒杀价") }}:￥{{ toFix(secKillInfo.seckillPrice) }}</text>
          </view>
        </view>

        <!-- 倒计时 -->
        <view class="second_kill_right">
          <view class="second_kill_text">
            {{ secKillInfo.state == 1 ? $L("距开始") : secKillInfo.state == 2 ? $L("距结束") : "" }}
          </view>
          <view class="sec_kill_countdown">
            <text class="day" v-show="countdown.day != '00'">{{ countdown.day }}天</text>
            <text class="time">{{ countdown.hour }}</text>
            <text class="time_tips">:</text>
            <text class="time">{{ countdown.minute }}</text>
            <text class="time_tips">:</text>
            <text class="time">{{ countdown.second }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 秒杀预告 -->
    <view class="sec_kill_preview" v-if="secKillInfo.state == 1">
      <view class="sec_kill_preview_left">
        {{ $L("秒杀预告") }}: {{ secKillInfo.startTime + " " + $L("开始") }}
      </view>

      <view class="sec_kill_preview_right flex_row_center_center" v-if="!secKillInfo.isRemind" @click="handleRemind">
        <image :src="imgUrl + 'goods_detail/time.png'" mode="aspectFit"></image>
        <view class="tip">{{ $L("提醒我") }}</view>
      </view>
      <view class="cancel_preview" v-else @click="handleRemind">
        {{ $L("取消提醒") }}
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'productSecKill',
  props: {
    secKillInfo: {
      type: Object,
      default: () => ({})
    },
    imgUrl: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      countdown: {
        day: '00',
        hour: '00',
        minute: '00',
        second: '00'
      },
      timer: null
    }
  },
  methods: {
    toFix(num) {
      return Number(num).toFixed(2)
    },
    splitPrice(price) {
      return this.toFix(price).split('.')
    },
    handleRemind() {
      this.$emit('remind')
    },
    startCountdown() {
      if (this.timer) {
        clearInterval(this.timer)
      }
      
      this.timer = setInterval(() => {
        const now = new Date().getTime()
        const target = this.secKillInfo.state === 1 
          ? new Date(this.secKillInfo.startTime).getTime()
          : new Date(this.secKillInfo.endTime).getTime()
        
        let diff = Math.max(0, target - now)
        
        if (diff === 0) {
          clearInterval(this.timer)
          this.$emit('time-end')
          return
        }

        const day = Math.floor(diff / (24 * 60 * 60 * 1000))
        diff = diff % (24 * 60 * 60 * 1000)
        const hour = Math.floor(diff / (60 * 60 * 1000))
        diff = diff % (60 * 60 * 1000)
        const minute = Math.floor(diff / (60 * 1000))
        diff = diff % (60 * 1000)
        const second = Math.floor(diff / 1000)

        this.countdown = {
          day: String(day).padStart(2, '0'),
          hour: String(hour).padStart(2, '0'),
          minute: String(minute).padStart(2, '0'),
          second: String(second).padStart(2, '0')
        }
      }, 1000)
    }
  },
  created() {
    this.startCountdown()
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  watch: {
    secKillInfo: {
      handler() {
        this.startCountdown()
      },
      deep: true
    }
  }
}
</script>

<style lang="scss" scoped>
.second_kill {
  width: 750rpx;
  background-color: #ffffff;
  
  .second_kill_con_box {
    width: 750rpx;
    height: 120rpx;
    padding: 0 24rpx;
    box-sizing: border-box;
    
    .second_kill_con {
      width: 702rpx;
      height: 120rpx;
      display: flex;
      align-items: center;
      padding: 0 24rpx;
      box-sizing: border-box;
      
      .second_kill_left {
        flex: 1;
        
        .second_kill_goods_price {
          color: #ffffff;
          font-weight: bold;
          
          text:first-child {
            font-size: 28rpx;
          }
          
          text:nth-child(2) {
            font-size: 40rpx;
          }
          
          text:last-child {
            font-size: 28rpx;
          }
        }
        
        .second_kill_price {
          font-size: 24rpx;
          color: #ffffff;
          margin-top: 4rpx;
          
          &.line_through {
            text-decoration: line-through;
          }
        }
      }

      .second_kill_right {
        .second_kill_text {
          font-size: 24rpx;
          color: #ffffff;
          text-align: center;
        }
        
        .sec_kill_countdown {
          margin-top: 4rpx;
          
          .day {
            font-size: 24rpx;
            color: #ffffff;
            margin-right: 8rpx;
          }
          
          .time {
            display: inline-block;
            min-width: 40rpx;
            height: 36rpx;
            line-height: 36rpx;
            text-align: center;
            background: #ffffff;
            border-radius: 4rpx;
            font-size: 24rpx;
            color: #FF0000;
            font-weight: bold;
          }
          
          .time_tips {
            color: #ffffff;
            margin: 0 4rpx;
          }
        }
      }
    }
  }

  .sec_kill_preview {
    width: 750rpx;
    height: 88rpx;
    background: #FFF2F2;
    padding: 0 24rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .sec_kill_preview_left {
      font-size: 24rpx;
      color: #FF0000;
    }
    
    .sec_kill_preview_right {
      display: flex;
      align-items: center;
      
      image {
        width: 28rpx;
        height: 28rpx;
        margin-right: 8rpx;
      }
      
      .tip {
        font-size: 24rpx;
        color: #FF0000;
      }
    }
    
    .cancel_preview {
      font-size: 24rpx;
      color: #999999;
    }
  }
}
</style> 