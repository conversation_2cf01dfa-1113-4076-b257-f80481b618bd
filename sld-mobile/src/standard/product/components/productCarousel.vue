<template>
  <view class="carousel" id="nav1">
    <uni-swiper-dot 
      :info="goodsPics" 
      :current="goodsVideo ? swiperCurrent - 1 : swiperCurrent"
      :showControls="showControls" 
      field="content" 
      :mode="mode"
    >
      <swiper 
        class="swiper-box" 
        @change="change"
        :current="swiperCurrent"
      >
        <!-- #ifdef H5 || APP-PLUS-->
        <swiper-item v-if="goodsVideo">
          <view 
            :style="{
              backgroundImage: 'url(' + (goodsPics && goodsPics[0]) + ')',
            }" 
            class="videoImage"
          ></view>
          <image :src="imgUrl + 'play.png'" class="play_btn" @click="toPlayPage"></image>
        </swiper-item>
        <!-- #endif -->
        <swiper-item v-for="(item, index) in goodsPics" :key="index">
          <image 
            :src="item" 
            mode="aspectFill" 
            class="carousel_img"
            @click="previewImg(index)"
          ></image>
        </swiper-item>
      </swiper>
    </uni-swiper-dot>
  </view>
</template>

<script>
export default {
  name: 'productCarousel',
  props: {
    goodsPics: {
      type: Array,
      default: () => []
    },
    goodsVideo: {
      type: String,
      default: ''
    },
    imgUrl: {
      type: String,
      required: true
    },
    current: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      mode: 'dot',
      showControls: false,
      swiperCurrent: 0
    }
  },
  watch: {
    current: {
      immediate: true,
      handler(newVal) {
        // 如果有视频，需要考虑视频占用了第一个位置
        this.swiperCurrent = this.goodsVideo ? newVal + 1 : newVal;
      }
    }
  },
  methods: {
    change(e) {
      const currentIndex = e.detail.current;
      // 如果有视频，需要减去视频占用的位置
      this.$emit('update:current', this.goodsVideo ? currentIndex - 1 : currentIndex);
      this.swiperCurrent = currentIndex;
    },
    previewImg(index) {
      uni.previewImage({
        current: index,
        urls: this.goodsPics
      })
    },
    toPlayPage() {
      this.$emit('play-video')
    }
  }
}
</script>

<style lang="scss" scoped>
.carousel {
  width: 750rpx;
  height: 750rpx;
  position: relative;
  
  .swiper-box {
    width: 750rpx;
    height: 750rpx;
  }
  
  .carousel_img {
    width: 750rpx;
    height: 750rpx;
  }
  
  .videoImage {
    width: 750rpx;
    height: 750rpx;
    background-size: cover;
    background-position: center;
  }
  
  .play_btn {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 98rpx;
    height: 98rpx;
  }
}
</style> 