<template>
	<view :style="mix_diyStyle">
		<view>
			<block v-if="!openState">
				<notOpen></notOpen>
			</block>
			<block v-else>
				<!-- app-1-start -->



				<!-- app-1-end -->
				<!-- wx-1-start -->
				<!-- #ifdef MP -->
				<view class="nav-bars" :style="{paddingTop:menuButtonTop}">
					<view class="nav-bar" :style="{height:menuButtonHeight}">
						<image :src="imgUrl+'tshou/back_icon.png'" mode="" @click="$back"></image>
						<view class="">阶梯团</view>
					</view>
					<view class="" style="padding-top: 10rpx;width: 100%;height: 1rpx;"></view>
				</view>
				<view class="nav_label" :style="{top:mpFlag==1?'calc('+statusBarHeight+'px)':0}">
				<!-- #endif -->
				<!-- wx-1-end -->
					<!-- #ifndef MP -->
					<view class="nav_label flex_row_start_center">
						<view class="back_icon1" @click="goBack">
							<image :src="imgUrl + 'store/white_arrow_l.png'" mode="aspectFit"></image>
						</view>
						<!-- #endif -->
						<scroll-view scroll-x class="nav">
							<view :class="'nav_item ' + (active == '0' ? 'on' : '')" @tap="changeNav" data-id="0">
								<text>{{ $L('首页') }}</text>
							</view>
							<view v-for="(item, index) in list" :key="index"
								:class="'nav_item ' + (active == item.labelId ? 'on' : '')" @tap="changeNav"
								:data-id="item.labelId">
								<text>{{ item.labelName }}</text>
							</view>
						</scroll-view>
					</view>

					<view class="goods_list" v-if="goodsList.length">
						<navigator v-for="(item, index) in goodsList" :key="index" class="goods_item" :url="'/standard/product/detail?productId=' +item.productId +'&promotionId=' +item.groupId" hover-class="none">
							<view class="item_left">
								<view class="image" :style="'background-image:url(' + item.goodsImage + ')'"></view>
							</view>
							<view class="item_right">
								<view class="goods_name">
									<view class="goods_name_img">
										<view class="goods_name_img_box">
										  <svgGroup type="to_ladder" width="24" height="28" px='rpx' :color="diyStyle_var['--color_ladder_main']"></svgGroup>
										  <view class="">
										    {{$L('阶梯团')}}
										  </view>
										</view>
									</view>
									<text>{{ item.goodsName }}</text>
								</view>
								<view class="goods_info">
									<view class="price">
										<view class="small_price">
											{{ $L('￥') }}
											<text class="big_price">{{$getPartNumber(item.spellPrice, 'int')}}</text>
											<text class="small_price" style="margin-right: 10rpx">{{$getPartNumber(item.spellPrice, 'decimal')}}</text>
										</view>
										<text class="num">￥{{ $getPartNumber(item.productPrice, 'int')}}{{ $getPartNumber(item.productPrice, 'decimal') }}</text>
									</view>
									<view class="group_now">
										<view class="group_now_left">
											{{ $L('马上拼') }} >
										</view>
										<view class="group_now_right">{{ $L('已团') }}{{ item.saleNum }}{{ $L('件') }}
										</view>
									</view>
								</view>
							</view>
						</navigator>
					</view>
					<view class="empty" v-if="!goodsList.length && loading">
						<image :src="imgUrl + 'empty_goods.png'"></image>
						<text>{{ $L('暂无商品') }}</text>
					</view>
					<view class="top_wrap" v-show="isShowTopBtn == true">
						<image :src="topImg" mode="aspectFit" @click="top"></image>
					</view>
					<!-- <common title="阶梯团"></common> -->
			</block>
		</view>
	</view>
</template>

<script>
	import notOpen from '@/components/not_open.vue'
	import request from '../../../utils/request'
	import uniNavBar from '@/components/uni-nav-bar/uni-nav-bar.vue'
	export default {
		data() {
			return {
				list: [],
				active: '0',
				goodsList: [],
				autoplay: true,
				interval: 5000,
				duration: 1000,
				//wx-7-start
				// #ifdef MP
				menuButtonHeight: uni.getMenuButtonBoundingClientRect().height + 'px',
				menuButtonTop: uni.getMenuButtonBoundingClientRect().top + 'px',
				menuButtonHeights: uni.getMenuButtonBoundingClientRect().height + uni.getMenuButtonBoundingClientRect()
					.top,
				// #endif
				//wx-7-end
				indicatorDots: true,
				loading: false,
				imgUrl: process.env.VUE_APP_IMG_URL, //图片地址
				home_info: '',
				bgImg: process.env.VUE_APP_IMG_URL + 'ladder/ladder_bg.png',
				icon: process.env.VUE_APP_IMG_URL + 'ladder/icon2.png',
				topImg: process.env.VUE_APP_IMG_URL + 'ladder/top.png',
				isShowTopBtn: false,
				ifOnShow: false,
				openState: true,
				pn: 1,
				nav_left_icon: 'back', //底部tab进入的话为空，否则为back
				navBackground: 'linear-gradient(90deg, #FE901F, #FDA827)',
				mpFlag: -1,
				color: '#FFF',
				statusBarHeight: ''
			}
		},
		components: {
			notOpen,
			uniNavBar
		},
		props: {},
		onLoad: function(options) {
			//wx-8-start
			// #ifdef MP
			this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight + 44,
				this.mpFlag = 1
			// #endif
			//wx-8-end
			this.getClassList()
			// this.getIndexData();
			this.getGoodsList()
			// #ifdef H5
			setTimeout(() => {
				uni.setNavigationBarTitle({
					title: '阶梯团'
				})
			}, 0);

			// #endif
		},

		onReachBottom() {
			if (this.hasmore) {
				this.getGoodsList()
			}
		},

		methods: {
			// 获取装修数据
			// getIndexData() {
			//   request({
			//     url: getApp().globalData.ser_url + '/index.php?app=index&mod=index_data&sld_addons=pin_ladder',
			//     success: res => {
			//       let home_info_data = res.data.datas.tmp_data;
			//       this.setData({
			//         home_info: home_info_data
			//       });
			//     }
			//   });
			// },

			// 获取阶梯团分类
			getClassList() {
				let params = {
					url: 'v3/promotion/front/ladder/group/list',
					method: 'GET'
				}
				this.$request(params).then((res) => {
					if (res.state == 200) {
						this.list = res.data.labelList
					}
				})
			},

			// 获取商品列表
			getGoodsList() {
				let params = {
					url: 'v3/promotion/front/ladder/group/list',
					method: 'GET',
					data: {
						labelId: this.active,
						current: this.pn
					}
				}
				this.$request(params).then((res) => {
					if (res.state == 200) {
						this.openState = true
						if (this.pn == 1) {
							this.goodsList = res.data.goodsList
						} else {
							this.goodsList = this.goodsList.concat(res.data.goodsList)
						}
						this.loading = true
						this.hasmore = this.$checkPaginationHasMore(res.data.pagination)
						if (this.hasmore) {
							this.pn++
						}
					} else if (res.state == 258) {
						this.loading = true
						this.openState = false
					} else {
						this.loading = true
					}
				})
			},

			//
			changeNav(e) {
				let id = e.currentTarget.dataset.id
				let {
					active
				} = this
				if (active == id) return
				this.active = id
				this.loading = false
				this.pn = 1
				this.hasmore = true
				this.getGoodsList()
			},
			// 获取滚动距离
			onPageScroll(e) {
				//根据距离顶部距离是否显示回到顶部按钮
				if (e.scrollTop > 600) {
					//当距离大于600时显示回到顶部按钮
					this.isShowTopBtn = true
				} else {
					//当距离小于600时隐藏回到顶部按钮
					this.isShowTopBtn = false
				}
			},
			// 回到顶部
			top() {
				uni.pageScrollTo({
					scrollTop: 0,
					duration: 300
				})
			},
			goBack() {
				this.$Router.back(1)
			}
		}
	}
</script>
<style lang="scss">
	page {
		background-color: #f5f5f5;
		/* #ifndef MP */
		padding-top: 80rpx;
		/* #endif */
		width: 750rpx;
		margin: 0 auto;
	}

	/* #ifdef H5 */
	page {
		padding-top: 0rpx;
	}

	/* #endif */

	.nav {
		display: block;
		white-space: nowrap;
		overflow: hidden;
		z-index: 9999;
		background-size: 100% 100%;
	}

	.back_icon1 {
		display: flex;
		align-items: center;
		padding-left: 10rpx;

		image {
			width: 52rpx;
			height: 49rpx;
		}
	}

	//app-2-start










	//app-2-end

	.nav_label {
		display: flex;
		position: relative;
		position: fixed;
		top: 0;
		//app-4-start



		//app-4-end
		left: 0;
		right: 0;
		margin: 0 auto;
		width: 750rpx;
		height: calc(88rpx + var(--status-bar-height));
		z-index: 9999;
		background: var(--color_ladder_main_bg);
		//wx-2-start
		/* #ifdef MP */
		background: #fff;
		height: 100rpx;
		/* #endif */
		//wx-2-end
	}

	.nav_item {
		display: inline-block;
		line-height: 100rpx;
		text-align: center;
		color: #fff;
		font-size: 30rpx;
		padding: 0 30rpx;
		overflow: hidden;
		//wx-3-start
		/* #ifdef MP */
		color: #333333;
		/* #endif */
		//wx-3-end
	}

	.nav_item.on {
		font-weight: bold;
		font-size: 32rpx;
		//wx-4-start
		/* #ifdef MP */
		font-weight: 800;
		color: var(--color_ladder_main);
		/* #endif */
		//wx-4-end
	}
	//wx-5-start
	/* #ifdef MP */
	.nav_item.on text {
		position: relative;

		&::before {
			content: '';
			width: 100%;
			height: 3rpx;
			background: var(--color_ladder_main);
			position: absolute;
			left: 50%;
			transform: translate(-50%);
			bottom: -10rpx;
			box-sizing: border-box;
			// margin: 0 10rpx;
		}
	}
	/* #endif */
	//wx-5-end
	.goods_list {
		padding: 0 20rpx;
		padding-bottom: 20rpx;
		//app-3-start



		//app-3-end
		//wx-6-start
		/* #ifdef MP */
		padding-top: 100rpx;
		/* #endif */
		//wx-6-end
		/* #ifdef H5 */
		padding-top: 88rpx;
		/* #endif */
	}

	.goods_list .goods_item {
		width: 100%;
		height: 310rpx;
		display: flex;
		background-color: #fff;
		border-radius: 15rpx;
		box-sizing: border-box;
		margin-top: 20rpx;
		padding: 20rpx;
	}

	.goods_item .item_left {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 270rpx;
		height: 270rpx;
		margin-right: 20rpx;
		border-radius: 15rpx;
		overflow: hidden;
	}

	.goods_item .item_left .image {
		background-position: center center;
		background-repeat: no-repeat;
		background-size: cover;
		width: 100%;
		height: 270rpx;
		background-color: #f7f7f7;
		border-radius: 15rpx;
	}

	.goods_item .item_right {
		display: flex;
		min-height: 200rpx;
		flex-direction: column;
		justify-content: space-around;
		position: relative;
	}

	.goods_item .item_right .goods_name {
		width: 370rpx;
		font-size: 28rpx;
		line-height: 44rpx;
		font-weight: 600;
		color: #2d2d2d;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		word-break: break-all;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		margin-top: -20px;
	}

	.goods_name_img {
		display: inline-block;
		box-sizing: border-box;
		vertical-align:-4rpx;
		margin-right: 5rpx;
	}
	
	.goods_name_img_box{
	  display: flex;
	  align-items: center;
	  justify-content: center;
	  width: 124rpx;
	  height: 32rpx;
	  border:1rpx solid var(--color_ladder_main);
	  box-sizing: border-box;
	}
  .goods_name_img view{
    font-size: 24rpx;
    font-family: PingFang SC;
    font-weight: 500;
    color: var(--color_ladder_main);
    margin-left: 5rpx;
  }

	.goods_item .item_right .goods_name image {
		display: inline-block;
		width: 122rpx;
		height: 31rpx;
		margin-bottom: -5rpx;
		margin-right: 10rpx;
	}

	.item_right .goods_info {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.goods_info .price {
		display: flex;
		align-items: center;
		font-weight: bold;
		color: var(--color_ladder_main);
		margin-top: 20rpx;
		margin-right: 10rpx;
		position: absolute;
		bottom: 82rpx;
		left: 0;
	}

	.goods_info .small_price {
		font-size: 24rpx;
	}

	.goods_info .big_price {
		font-size: 34rpx;
	}

	/* .goods_info .goods_price>text:nth-child(1) {
  color: #EE1B21;
  font-size: 34rpx;
}

.goods_info .goods_price>text text{
  font-size: 50rpx;
}

.goods_info .goods_price text:nth-child(2) {
  color: #808080;
  font-size: 24rpx;
  margin-left: 30rpx;
  margin-top: 20rpx;
} */
	.group_now {
		position: absolute;
		bottom: 10rpx;
		left: 0;
		width: 270rpx;
		height: 50rpx;
		line-height: 50rpx;
		border-radius: 25px;
		display: flex;
		align-items: center;
		border: 1rpx solid var(--color_ladder_main);
		box-sizing: border-box;
	}

	.group_now .group_now_left {
		font-size: 26rpx;
		color: #fff;
		width: 148rpx;
		text-align: center;
		border-radius: 25rpx 0 0 25rpx;
		background:var(--color_ladder_main_bg);
		background-size: 100% 100%;
		box-sizing: border-box;
		margin-left: -1rpx;
		border-bottom-right-radius: 27rpx;
	}

	.group_now .group_now_right {
		width: 122rpx;
		text-align: center;
		font-size: 22rpx;
		color: var(--color_ladder_main);
		font-weight: 400;
		box-sizing: border-box;
	}

	.goods_info .price .num {
		font-weight: 500;
		color: #9a9a9a;
		font-size: 24rpx;
		text-decoration: line-through;
	}

	.goods_info .goods_btn {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 172rpx;
		height: 64rpx;
		border-radius: 32rpx;
		color: #fff;
		font-size: 28rpx;
		background: #ed6307;
	}

	.goods_info .goods_btn image {
		width: 10rpx;
		height: 18rpx;
		margin-left: 15rpx;
		transform: rotate(180deg);
	}

	.empty {
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		height: 70vh;
	}

	.empty image {
		width: 380rpx;
		height: 280rpx;
		margin-bottom: 30rpx;
	}

	.empty text {
		color: #333;
		font-size: 28rpx;
	}

	.top_wrap {
		position: fixed;
		right: 46rpx;
		bottom: 66rpx;
		width: 85rpx;
		height: 85rpx;
		left: 0;
		right: 0;
		margin: 0 auto;
		transform: translateX(286rpx);
	}

	.top_wrap image {
		width: 85rpx;
		height: 85rpx;
	}

	.nav-bar {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: flex-start;
		padding-left: 20rpx;
	}

	.nav-bar image {
		width: 19rpx !important;
		height: 33rpx !important;
		margin-top: 5rpx !important;
		position: initial !important;
	}

	.nav-bar view {
		font-size: 36rpx;
		font-family: PingFang SC;
		color: #FFFFFF;
		margin-left: 21rpx;
	}

	.nav-bars {
		z-index: 999;
		background: var(--color_ladder_main_bg);
		width: 100%;
		top: 0;
	}
</style>