<template>
	<view class="member_level_main" :style="mix_level_style">
		<view class="fixed_top_bar"></view>
		<view class="top_bar">
			<view class="top_header">
				<view class="top_header_left" @click="goBack">
					<image :src="imgUrl + 'index/back.png'" mode="aspectFit"></image>
				</view>
				<view class="top_header_cen">会员中心</view>
				<!-- #ifndef MP-WEIXIN -->
				<view class="top_white_space" @click="navTo('/standard/memLev/rule')">
					<text>等级规则</text>
				</view>
				<!-- #endif -->
				<!-- wx-1-start -->
				<!-- #ifdef MP-WEIXIN -->
				<view class="top_white_space" style="width: 130rpx;"></view>
				<!-- #endif -->
				<!-- wx-1-end -->
			</view>
		</view>
		<!-- wx-2-start -->
		<!-- #ifdef MP-WEIXIN -->
		<view @click="navTo('/standard/memLev/rule')" class="rule_mp flex_row_end_center">
			<text>等级规则</text>
		</view>
		<!-- #endif -->
		<!-- wx-2-end -->
		<view class="mem_ban">
			<view class="ban_center " >
				<view class="ban_center_con flex_row_start_start">
					<view class="flex_row_between_center" style="width: 100%;">
						<view class="ban_c_left flex_row_start_center">
							<view class="avatar" :style="{backgroundImage:'url('+lvData.memberAvatar+')'}"></view>
							<view class="growth flex_column_start_between">
								<text class="grow_n">成长值</text>
								<text class="grow_v">{{lvData.memberGrowthValue||0}}</text>
							</view>
						</view>
						<view class="ban_c_right">
							<view class="lv_name flex_row_between_center">
								<image :src="imgUrl1+'vip_gold.png'" mode="aspectFit" class="lv_logo"></image>
								<view class="tab_space">
								</view>
								<text class="lv_n">{{cur_lev.levelName||"普通用户"}}</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<view class="ban_bottom" :style="{backgroundImage:'url('+imgUrl1+'ban_bottom.png)'}">
				<swiper :indicator-dots="false" :current="current" @change="change"
					previous-margin="250rpx" next-margin="250rpx">
					<swiper-item v-for="(item,index) in lvData.memberLevelList" :key="index">
						<view :class="{swiper_item:true,sel:index<=current}">
							<view class="level_title">{{item.levelName}}</view>
							<view class="level_pro flex_row_center_center">
								<view class="pro_line"></view>
								<view class="eq_dot" v-if="index<=current"></view>
								<!-- <image :src="imgUrl1+'level_dot_cho.png'" mode="" v-if="index<=current"></image> -->
								<image :src="imgUrl1+'level_dot.png'" mode="" v-else></image>
								<view class="pro_line"></view>
							</view>
							<view class="level_value">{{item.growthValue}}</view>
						</view>
					</swiper-item>
				</swiper>
			</view>
		</view>

		<view class="mem_list">
			<view class="lv_equity">
				<view class="eq_title">等级权益</view>
				<view class="eq_list flex_row_start_center">
					<view class="eq_item flex_column_center_center" v-if="cur_lev.couponEnable==1" @click="open_cp('couponList')">
						<!-- <image :src="imgUrl1+'coupon.png'" mode="aspectFit"></image> -->
						<view class="eq_icon">
							<text class="iconfont iconyouhuiquan1"></text>
						</view>
						<text class="eq_item_text">优惠券</text>
					</view>
					<view class="eq_item flex_column_center_center" v-if="cur_lev.freightCouponEnable==1" @click="open_cp('freightCouponList')">
						<!-- <image :src="imgUrl1+'coupon_fee.png'" mode="aspectFit"></image> -->
						<view class="eq_icon">
							<text class="iconfont iconyunfeiquan"></text>
						</view>
						<text class="eq_item_text">运费券</text>
					</view>
					<view class="eq_item flex_column_center_center" v-if="cur_lev.integralMultipleEnable==1">
						<!-- <image :src="imgUrl1+'integral.png'" mode="aspectFit"></image> -->
						<view class="eq_icon">
							<text class="iconfont iconjifen"></text>
						</view>
						<text class="eq_item_text">{{cur_lev.integralMultipleValue}}倍积分</text>
					</view>
					<view class="eq_item flex_column_center_center" @click="navTo('/standard/memLev/equity')">
						<!-- <image :src="imgUrl1+'all_eq.png'" mode="aspectFit"></image> -->
						<view class="eq_icon">
							<text class="iconfont iconquanbuquanyi"></text>
						</view>
						<text class="eq_item_text">全部权益</text>
					</view>
				</view>
			</view>

			<view class="lv_mission">
				<view class="mi_title">任务中心</view>
				<view class="mi_list">
					<block v-for="(item,index) in mi_list" :key="index">
						<view class="mi_item flex_row_between_center" v-if="item.show&&item.enable==1">
							<view class="mi_i_left flex_row_start_center">
								<!-- <image :src="imgUrl1+item.img" mode="aspectFit"></image> -->
								<view class="eq_icon_xs">
									<text :class="['iconfont',item.img]"></text>
								</view>
								<view class="mi_text flex_column_between_start">
									<view>
										<text class="text_1">{{item.name}}</text>
									</view>
									<text class="text_2">{{item.desc}}</text>
								</view>
							</view>
							<view class="mi_i_right">
								<view class="fi_but_1" v-if="item.state==0" @click="navTo(item.desti)">去完成</view>
								<view class="fi_but_2" v-else @click="navTo(item.desti)">去逛逛</view>
							</view>
						</view>
					</block>

				</view>
			</view>
		</view>
		
		<block v-if="userCenterData.isSuper!==1&&superEnable">
			<navigator url="/standard/super/index?type=0">
				<view class="bottom_btn_wrap" >
					<view class="bottom_btn" >
						开通付费会员，享超值尊贵特权
					</view>
				</view>
			</navigator>
		</block>
		
		
		<!-- 推荐商品 start-->
		<recommendGoods ref='recomment_goods' @reload_cart="getCartNum" />
		<!-- 推荐商品 end-->


		<eqPop ref="eqPop" :data_cp="ch_cp"  :cpLen="chCp_len"></eqPop>
		

	</view>
</template>

<script>
	import recommendGoods from "@/components/recommend-goods.vue"
	import eqPop from './eqPop.vue'
	import {mapState} from 'vuex'
	import style from './style'
	export default {
		mixins:[style],
		components: {
			eqPop,
			recommendGoods,
		},
		data() {
			return {
				imgUrl: process.env.VUE_APP_IMG_URL,
				imgUrl1: process.env.VUE_APP_IMG_URL + 'member_level/',
				lvData: {},
				showState: false,
				current:0,
				cur_lev:{},
				ch_cp:{},
				chCp_len:0,
				superEnable:false,
				mi_list: [{
						name: '完善信息',
						desc: '完成后获得$1点成长值',
						img: 'iconwanshanxinxi',
						state: 0,
						desti: '/pages/user/info',
						show: true,
						stxS:0,
						stxE:2
					},
					{
						name: '购买商品',
						desc: '每消费$2元，获得$1点成长值',
						img: 'icongoumaishangpin',
						state: 0,
						desti: '/pages/index/index',
						show: true,
						stxS:2,
						stxE:5
					},
					{
						name: '发布评价',
						desc: '每发布$2条优质评价，获得$1点成长值',
						img: 'iconfabupinglun',
						state: 0,
						desti: '/pages/order/list?state=5',
						show: true,
						stxS:5,
						stxE:8
					},
					{
						name: '登录',
						desc: '每日登录可获得$1点成长值',
						img: 'icondenglu',
						state: 1,
						desti: '/pages/index/index',
						show: true,
						stxS:8,
						stxE:10
					},
					{
						name: '签到',
						desc: '每日签到可获得$1点成长值',
						img: 'iconqiandao',
						state: 0,
						desti: '/standard/signIn/signIn',
						show: true,
						stxS:10,
						stxE:12
					},
				],
				set_str:[
					'task_complete_info_enabled',
					'task_complete_info_growth_value',
					
					'task_purchase_enabled',
					'task_purchase_growth_value',
					'task_purchase_every',
					
					'task_comment_enabled',
					'task_comment_growth_value',
					'task_comment_every',
					
					'task_login_enabled',
					'task_login_growth_value',
					
					'task_sign_enabled',
					'task_sign_growth_value',
					
					'super_is_enable'
				]
				
			}
		},

		onLoad() {
			this.getData()


		},

		onShow() {
			if (this.showState) {
				this.getData()
			}
		},
		
		onReachBottom() {
			this.$refs.recomment_goods.getMoreData();
		},
		
		computed:{
			...mapState(['userCenterData'])
		},

		methods: {
			goBack() {
				this.$back()
			},
			
			change(e){
				// this.current = e.detail.current
			},

			getData() {
				this.$request({
					url: 'v3/member/front/memberLevel/getInfo',
				}).then(res => {
					if (res.state == 200) {
						this.lvData = res.data
						this.lvData.memberLevelList = res.data.memberLevelList.sort((a,b)=>a.level-b.level)
						this.cur_lev = res.data.currentLevel
						
						this.current = this.lvData.memberLevelList.findIndex(lev=>lev.level==this.cur_lev.level)
						
						
						
						if (this.lvData.isSignCompleted) {
							this.mi_list[4].desti = '/pages/index/index'
							this.mi_list[4].state = 1
						}

						if (this.lvData.isInfoCompleted) {
							this.mi_list[0].state = 1
							this.mi_list[0].show = false
						}
						
						this.getSetting()
					} else if (res.state == 266) {
						this.$Route.push('/pages/public/login')
					}
				})
			},


			getSetting() {
				let param = {}
				param.data = {}
				param.data.str =this.set_str.join(',');
				param.url = 'v3/member/front/memberSetting/getSettingList'
				this.$request(param).then(res => {
					this.set_list = res.data;
					let super_is_enable = res.data.pop()
					this.superEnable = Number(super_is_enable.value)?true:false
					this.mi_list.map(mi=>{
						let set_fra = this.set_list.slice(mi.stxS,mi.stxE)
						mi.enable = set_fra[0].value
						mi.growth_value = set_fra[1].value
						mi.desc = mi.desc.replace('$1',mi.growth_value)
						
						if(set_fra[2]){
							mi.every = set_fra[2].value
							mi.desc = mi.desc.replace('$2',mi.every)
						}
					})
					this.$forceUpdate()
					
				})
			},

			navTo(url) {
				if (this.$isTabPage(url)) {
					this.$Router.pushTab(url)
				} else {
					this.showState = true
					this.$Router.push(url)
				}
			},
			
			open_cp(prop){
				this.ch_cp = this.cur_lev[prop][0]
				this.chCp_len = this.cur_lev[prop].length
				this.$refs.eqPop.open()
			},
			
			
			//获取购物车数据
			getCartNum() {
				if (this.hasLogin) {
					let param = {};
					param.url = 'v3/business/front/cart/cartNum';
					param.method = 'GET';
					param.data = {};
					// param.data.key = this.userInfo.member_access_token;
					this.$request(param).then(res => {
						if (res.state == 200) {
							if (res.data > 0) {
								uni.setTabBarBadge({
									index: 3,
									text: res.data.toString()
								})
							} else {
								uni.hideTabBarRedDot({
									index: 3
								})
							}
						} else {
							this.$api.msg(res.msg);
						}
					})
				}
			},
		}
	}
</script>

<style lang="scss">
	page {
		background: #F5F5F5;
	}
	
	uni-swiper {
		display: block;
		height: 150rpx;
	}
	uni-swiper-item {
		display: flex;
		align-items: center;
		justify-content: center;
	}
	//wx-3-start
	/* #ifdef MP */
	swiper{
		height: 150rpx !important;
	}
	swiper-item{
		display: flex;
		align-items: center;
		justify-content: center;
	}
	/* #endif */
	//wx-3-end
	.eq_icon{
		width: 90rpx;
		height: 90rpx;
		border-radius: 50%;
		background: var(--level_halo);
		display: flex;
		align-items: center;
		justify-content: center;
		text{
			font-size: 55rpx;
			color: var(--level_border);
		}
	}
	
	.eq_icon_xs{
		width: 88rpx;
		height: 88rpx;
		border-radius: 50%;
		background: var(--level_halo_2);
		display: flex;
		align-items: center;
		justify-content: center;
		text{
			font-size: 50rpx;
			color: var(--level_border);
		}
	}
	
	.eq_dot {
	  width: 37rpx;
	  min-height: 37rpx;
	  background: var(--level_line);
	  position: relative;
	  border-radius: 50%;
	
	  &::before {
	    content: '';
	    width: 20rpx;
	    height: 20rpx;
	    border-radius: 50%;
	    background-color: #fff;
	    position: absolute;
	    left: 50%;
	    top: 50%;
	    transform: translate(-10rpx, -10rpx);
	  }
	}

	.member_level_main {
		padding-top: 20rpx;
		height: 100%;
		position: relative;
		background: linear-gradient(118deg, rgba(49, 49, 49, 0.84), rgba(0, 0, 0, 0.84));
		background-size: 750rpx 374rpx;
		background-repeat: no-repeat;
		padding-bottom: calc(80rpx + env(safe-area-inset-bottom));

		.fixed_top_bar {
			height: var(--status-bar-height);
			width: 100%;
		}

		.top_bar {
			.top_header {
				display: flex;
				align-items: center;
				justify-content: space-between;

				.top_header_left {
					padding-left: 20rpx;

					image {
						width: 17rpx;
						height: 29rpx;
					}
				}

				.top_header_cen {
					margin-left: 100rpx;
					font-size: 36rpx;
					font-family: PingFang SC;
					color: #FFFFFF;
				}

				.top_white_space {
					color: #FFFFFF;
					font-size: 28rpx;
					// width: 40rpx;
					// height: 49rpx;
					padding-right: 20rpx;
					// image{
					// 	width: 40rpx;
					// 	height: 40rpx;
					// }
				}
			}
		}
		
		.rule_mp{
			color: #fff;
			margin-top: 30rpx;
			padding: 0 20rpx;
		}

		.mem_ban {
			margin-top: 60rpx;

			.ban_center {
				width: 710rpx;
				height: 220rpx;
				margin: 0 auto;
				background-position: center center;
				background-repeat: no-repeat;
				background-size: cover;
				background-color: #fff;
				border-radius: 30rpx;
				
				
				.ban_center_con{
					border-radius: 30rpx;
					padding: 0 20rpx;
					padding-top: 40rpx;
					width: 100%;
					height: 100%;
					background: var(--level_bg);
				}

				.ban_c_left {
					.avatar {
						width: 100rpx;
						height: 100rpx;
						border-radius: 50%;
						background-position: center center;
						background-size: cover;
						background-repeat: no-repeat;
					}

					.growth {
						margin-left: 20rpx;

						.grow_n {
							font-size: 26rpx;
							font-family: PingFang SC;
							font-weight: 500;
							color: #FFFFFF;
						}

						.grow_v {
							font-size: 52rpx;
							font-family: PingFang SC;
							font-weight: 800;
							color: #FFFFFF;
						}
					}
				}

				.ban_c_right {
					.lv_name {
						padding: 10rpx 20rpx;
						background: #FFEFD4;
						border-radius: 28rpx;
						position: relative;

						.lv_n {
							font-size: 30rpx;
							font-family: PingFang SC;
							font-weight: bold;
							color: #BB9546;
							/* #ifndef MP */
							background: linear-gradient(45deg, #7A492F 0%, #C97D3B 100%);
							-webkit-background-clip: text;
							-webkit-text-fill-color: transparent;
							/* #endif */
						}

						.tab_space {
							width: 30rpx;
							height: 30rpx;
							margin-right: 20rpx;
						}

						.lv_logo {
							width: 84rpx;
							height: 84rpx;
							position: absolute;
							left: -26rpx;
							bottom: -8rpx;
						}
					}

				}
			}

			.ban_bottom {
				width: 750rpx;
				height: 204rpx;
				background-position: center center;
				background-repeat: no-repeat;
				background-size: cover;
				margin-top: -50rpx;
				padding-bottom: 20rpx;
				padding-top: 52rpx;
			}
		}

		.mem_list {
			padding: 20rpx;

			.lv_equity {
				background: #FFFFFF;
				border-radius: 20rpx;
				padding: 30rpx 20rpx;

				.eq_title {
					font-size: 32rpx;
					font-family: PingFang SC;
					font-weight: bold;
					color: #000000;
				}

				.eq_list {
					margin-top: 36rpx;

					.eq_item {
						margin-right: 100rpx;
						&:last-child{
							margin-right: 0;
						}
						image {
							width: 90rpx;
							height: 90rpx;
						}

						.eq_item_text {
							margin-top: 20rpx;
							font-size: 24rpx;
							font-family: PingFang SC;
							font-weight: 500;
							color: #505050;
						}
					}
				}
			}

			.lv_mission {
				background: #FFFFFF;
				border-radius: 20rpx;
				padding: 30rpx 20rpx;
				margin-top: 20rpx;

				.mi_title {
					font-size: 32rpx;
					font-family: PingFang SC;
					font-weight: bold;
					color: #000000;
				}

				.mi_list {
					margin-top: 26rpx;

					.mi_item {
						border-bottom: 1px solid #E8E8E8;
						padding: 30rpx 0;

						&:last-child {
							border-bottom: none;
							padding-bottom: 0;
						}

						.mi_i_left {
							image {
								width: 88rpx;
								height: 88rpx;
							}

							.mi_text {
								margin-left: 20rpx;
								height: 88rpx;
								padding: 4rpx 0;

								.text_1 {
									font-size: 28rpx;
									font-family: PingFang SC;
									font-weight: 500;
									color: #141414;
								}

								.text_2 {
									font-size: 24rpx;
									font-family: PingFang SC;
									font-weight: 500;
									color: #999999;
								}
							}
						}

						.mi_i_right {

							.fi_but_1 {
								width: 132rpx;
								height: 52rpx;
								background: var(--level_fade_btn);
								border-radius: 26rpx;
								line-height: 52rpx;
								text-align: center;
								font-size: 26rpx;
								font-family: PingFang SC;
								font-weight: 500;
								color: #FFFFFF;
							}

							.fi_but_2 {
								width: 132rpx;
								height: 52rpx;
								background: #fff;
								border-radius: 26rpx;
								line-height: 52rpx;
								text-align: center;
								font-size: 26rpx;
								font-family: PingFang SC;
								font-weight: 500;
								color: var(--level_border);
								border: 1px solid var(--level_border);
							}

						}



					}

				}

			}
		}

		
		.bottom_btn_wrap {
			margin: 0 auto;
			width: 750rpx;
			height: 80rpx;
			background:#FFFFFF;
			font-family: PingFang SC;
			font-weight: bold;
			color: #FFFFFF;
			position: fixed;
			bottom: 0;
			left: 0;
			right: 0;
			z-index: 99;
			
			.bottom_btn{
				width: 100%;
				height: 100%;
				background: var(--level_bg);
				text-align: center;
				line-height: 80rpx;
				font-size: 30rpx;
			}
		}
	
		.swiper_item{
			flex: 1;
			opacity: 0.4;
			.level_title{
				font-size: 26rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: var(--level_line);
				text-align: center;
				margin-bottom: 6rpx;
			}
			
			.level_value{
				font-size: 26rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: #666;
				text-align: center;
				margin-top: 6rpx;
			}
			
			.level_pro{
				image{
					width: 35rpx;
					height: 35rpx;
				}
				
				.pro_line{
					height: 3rpx;	
					background: var(--level_line);
					flex: 1;
					border-left: 1px solid var(--level_line);
					border-right: 1px solid var(--level_line);
				}
				
			}
			
			&.sel{
				opacity: 1;
				.pro_line{
					height: 6rpx;	
					background: var(--level_line);
					flex: 1;
				}
			}
		}
	}
</style>
