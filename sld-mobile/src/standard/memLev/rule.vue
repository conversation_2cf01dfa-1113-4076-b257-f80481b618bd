<template>
	<view class="container_pri">




		<rich-text :nodes="h5_wx_html"></rich-text>

	</view>
</template>

<script>
import jyfParser from '@/components/jyf-parser/jyf-parser.vue'
import { quillEscapeToHtml } from '@/utils/common.js'
export default {
  data() {
    return {
      imgUrl: process.env.VUE_APP_IMG_URL,
      isLoading: true,
	  h5_wx_html:''
    };
  },
  components: {jyfParser},
  props: {},
  onLoad(){
  	  this.getRule();
	 
  },
  methods:{
  	  getRule(){
	    let param = {}
	    param.data = {}
	    param.data.str= 'level_rule';
	    param.url = 'v3/member/front/memberSetting/getSettingList'
	    this.$request(param).then(res=>{
			this.h5_wx_html = res.data[0].value ? quillEscapeToHtml(res.data[0].value) : '';
	    })
  	  }
  }
};
</script>
<style>
	page{
		width: 750rpx;
		overflow: auto;
		margin: 0 auto;
	}
	
	.container_pri{
		width: 680rpx;
		margin: 0 auto 20rpx;
		word-break: break-all;
	}
	
	p{
		margin-top: 20px;
	}
	
	.container_pri ::v-deep .rich_text_image {
		width: 680rpx;
	}
</style>
