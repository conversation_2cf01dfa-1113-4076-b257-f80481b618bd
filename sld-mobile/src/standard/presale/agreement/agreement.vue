//预售定金协议
<template>
  <view class="container_pri">




    <rich-text :nodes="h5_wx_html"></rich-text>

  </view>
</template>

<script>
import jyfParser from '@/components/jyf-parser/jyf-parser.vue'
import { quillEscapeToHtml } from '@/utils/common.js'
export default {
  data() {
    return {
      type: '',
      // banance: 余额   points: 积分
      key: '',
      imgUrl: process.env.VUE_APP_IMG_URL,
      isLoading: true,
      title: '',
      num: '',
      list: '',
      pn: 1, //当前页
      hasmore: true,
      h5_wx_html: ''
    }
  },

  components: { jyfParser },
  props: {},
  onLoad() {
    setTimeout(()=>{
      uni.setNavigationBarTitle({
        title: this.$L('预售定金协议')
      })    
    },0);
    
    this.getPrivacyPolicy()
  },
  methods: {
    getPrivacyPolicy() {
      let param = {}
      param.data = {}
      param.data.agreementCode = 'presell_deposit_agreement'
      param.url = 'v3/system/front/agreement/detail'
      this.$request(param).then((res) => {
        this.h5_wx_html = res.data.content
          ? quillEscapeToHtml(res.data.content)
          : ''
      })
    }
  }
}
</script>
<style>
page {
  width: 750rpx;
  margin: 0 auto;
}
.container_pri {
  width: 90%;
  height: 100vh;
  margin: 0 auto;
  word-break: break-all;
}
p {
  margin-top: 20px;
}
</style>
