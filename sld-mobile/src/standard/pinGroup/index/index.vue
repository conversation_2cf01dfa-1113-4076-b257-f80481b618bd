<template>
	<view :style="mix_diyStyle">
		<view>
			<block v-if="!openState">
				<notOpen></notOpen>
			</block>
			<block v-else>
				<!-- app-1-start -->



				<!-- app-1-end -->
				<!-- wx-1-start -->
				<!-- #ifdef MP -->
				<!-- <image class="bg" :src="imgUrl + 'svideo/list_bg1.png'" :style="{height:'calc('+statusBarHeight+' + 468rpx)'}"></image> -->
				<view class="nav-bars" :style="{paddingTop:menuButtonTop,position:'fixed'}">
					<view class="nav-bar" :style="{height:menuButtonHeight}">
						<image :src="img_url+'tshou/back_icon.png'" mode="" @click="$back"></image>
						<view class="">拼团</view>
					</view>
					<view class="" style="padding-top: 10rpx;width: 100%;height: 1rpx;"></view>
				</view>
				<view class="nav_label_box_mp">
					<view class="nav_label" :style="{top:mpFlag==1?'calc('+statusBarHeight+'px)':0}">
				<!-- #endif -->
				<!-- wx-1-end -->
				<!-- #ifndef MP -->
				<view class="nav_label_box">
					<view class="nav_label" :style="{ backgroundImage: 'url(' + bgImg + ')'}">
						<view class="back_icon1" @click="goBack">
							<image :src="img_url + 'store/white_arrow_l.png'" mode="aspectFit"></image>
						</view>
				<!-- #endif -->
						<scroll-view class="nav-container" scroll-x>
							<view :class="'item ' + (tid == 0 ? 'on' : '')" data-id="0" @tap="changeNav">{{ $L('首页') }}</view>
								<view v-for="(item, index) in NavData" :key="index"
									:class="'item ' + (tid == item.spellLabelId ? 'on' : '')" :data-id="item.spellLabelId"
									@tap="changeNav">{{ item.spellLabelName }}</view>
						</scroll-view>
					</view>           
				</view>
					<!-- #ifndef MP -->
					<view class="goods-list" v-if="goodsList.length" :style="{'padding-bottom':bottomSateArea}">
					<!-- #endif -->
						<!-- wx-2-start -->
						<!-- #ifdef MP -->
						<view class="goods-list" v-if="goodsList.length"
							:style="{'padding-bottom':bottomSateArea,paddingTop:'calc('+statusBarHeight+'px'+' + 100rpx)'}">
						<!-- #endif -->
						<!-- wx-2-end -->
							<view v-for="(item, index) in goodsList" :key="index" class="item">
								<navigator :url="'/standard/product/detail?productId=' +item.productId +'&promotionId=' +item.spellId" hover-class="none">
									<view class="goods-list-top">
										<view class="img">
											<view class="image" :style="'background-image:url(' + item.goodsImage + ')'"></view>
										</view>

										<view class="goods-info">
											<view class="info">
												<view class="goods_nameBox">
													<text class="goods_nameText">{{ item.goodsName }}</text>
												</view>

												<view class="goods_price_wrap">
													<view class="now_price">
														<text class="small_price">{{ $L('￥') }}</text><text
															class="big_price">{{item.spellPrice.toString().split('.')[0]}}.</text><text class="small_price">{{item.spellPrice.toString().split('.')[1]}}</text>
													</view>
													<view class="old_price">{{ $L('￥') }}{{ item.productPrice }}</view>
												</view>
												<view style="display: inline-block">
													<view class="group_num_wrap">
														<svgGroup type="to_pinGroup" width="31" height="27" px="rpx"
															:color="diyStyle_var['--color_spell_main']" class="iconImg">
														</svgGroup>
														<text>{{ item.requiredNum }}{{ $L('人团') }}</text>
														<view class="go_to_group">{{ $L('去拼团') }}</view>
													</view>
												</view>
											</view>
										</view>
									</view>
								</navigator>
								<view class="activity-info">
									<!-- <image :src="saveImg" mode="aspectFit" class="save_img"></image> -->
                  <view class="activity-saveImg"> 
                    {{$L('省')}}
                  </view>
									<text class="group_bottom_text">{{ $L('拼团购买立省') }}{{ item.spellDiscount}}
										<block v-if="item.leaderDiscount != 0">,{{ $L('团长优惠') }}{{ item.leaderDiscount}}{{ $L('元') }}</block>;{{ $L('单独购买价') }}{{ item.productPrice }}
									</text>
								</view>
							</view>
						</view>

						<view class="empty" v-if="!goodsList.length && isLoading">
							<view class="img">
								<image :src="img_url + 'empty_goods.png'" mode="widthFix"></image>
							</view>
							<view class="tip">{{ $L('暂无商品') }}</view>
							<text>{{ $L('选择其他分类试试吧!') }}</text>
						</view>

						<view class="top_wrap" v-show="isShowTopBtn == true">
							<image :src="topImg" mode="aspectFit" @click="top"></image>
						</view>
			</block>
		</view>
	</view>
</template>

<script>
	import notOpen from '@/components/not_open.vue'
	import uniNavBar from '@/components/uni-nav-bar/uni-nav-bar.vue'
	export default {
		data() {
			return {
				key: '',
				tid: '',
				isLoading: false,
				NavData: '',
				goodsList: '',
				pn: 1,
				hasmore: true,
				img_url: process.env.VUE_APP_IMG_URL,
				bgImg: process.env.VUE_APP_IMG_URL + 'pinGroup/spell_bg.png',
				iconImg: process.env.VUE_APP_IMG_URL + 'pinGroup/icon.png',
				saveImg: process.env.VUE_APP_IMG_URL + 'pinGroup/save.png',
				topImg: process.env.VUE_APP_IMG_URL + 'pinGroup/top.png',
				isShowTopBtn: false,
				bottomSateArea: 0, //iphone手机底部一条黑线的高度
				gids: [],
				openState: true,
				nav_left_icon: 'back', //底部tab进入的话为空，否则为back
				navBackground: 'linear-gradient(90deg, #EE1122, #F7482D)',
				mpFlag: -1,
				color: '#FFF',
				statusBarHeight: '',
				//wx-9-start
				// #ifdef MP
				menuButtonHeight: uni.getMenuButtonBoundingClientRect().height + 'px',
				menuButtonTop: uni.getMenuButtonBoundingClientRect().top + 'px',
				menuButtonHeights: uni.getMenuButtonBoundingClientRect().height + uni.getMenuButtonBoundingClientRect()
					.top,
				// #endif
				//wx-9-end
			}
		},

		components: {
			notOpen,
			uniNavBar
		},
		props: {},
		onLoad: function(options) {
			//wx-10-start
			// #ifdef MP
			this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight + 44,
				this.mpFlag = 1
			// #endif
			//wx-10-end
			this.initData()
			// #ifdef H5
			setTimeout(() => {
				uni.setNavigationBarTitle({
					title: '拼团'
				})
			}, 0);

			// #endif
		},

		onReachBottom() {
			if (this.hasmore) {
				this.getList()
			}
		},

		methods: {
			initData() {
				this.getNavData()
				this.getList()
			},

			getNavData() {
				let params = {
					url: 'v3/promotion/front/spell/list',
					method: 'GET'
				}
				this.$request(params).then((res) => {
					this.isLoading = true
					if (res.state == 200) {
						this.NavData = res.data.labelList
					}
				})
			},

			getList() {
				let params = {
					url: 'v3/promotion/front/spell/list',
					method: 'GET',
					data: {
						labelId: this.tid,
						current: this.pn
					}
				}
				this.$request(params).then((res) => {
					if (res.state == 200) {
						this.openState = true
						if (this.pn == 1) {
							this.goodsList = res.data.goodsList
						} else {
							this.goodsList = this.goodsList.concat(res.data.goodsList)
						}
						this.loading = true
						this.hasmore = this.$checkPaginationHasMore(res.data.pagination)
						if (this.hasmore) {
							this.pn++
						}
					} else if (res.state == 258) {
						this.openState = false
					}
				})
			},

			changeNav(e) {
				let newTid = e.currentTarget.dataset.id
				let {
					tid
				} = this
				if (newTid == tid) return
				this.pn = 1
				this.hasmore = true
				this.tid = newTid

				this.getList()
			},
			// 获取滚动距离
			onPageScroll(e) {
				//根据距离顶部距离是否显示回到顶部按钮
				if (e.scrollTop > 600) {
					//当距离大于600时显示回到顶部按钮
					this.isShowTopBtn = true
				} else {
					//当距离小于600时隐藏回到顶部按钮
					this.isShowTopBtn = false
				}
			},
			// 回到顶部
			top() {
				uni.pageScrollTo({
					scrollTop: 0,
					duration: 300
				})
			},
			goBack() {
				this.$Router.back(1)
			}
		}
	}
</script>
<style lang="scss">
	/* integral/activity/pin_index/pin_index.wxss */

	page {
		background-color: #f5f5f5;
		/* #ifndef MP */
		padding-top: 90rpx;
		/* #endif */
		width: 750rpx;
		margin: 0 auto;
	}

	/* #ifdef H5 */
	page {
		padding-top: 0rpx;
	}

	/* #endif */

	//app-2-start










	//app-2-end

	.nav_label {
		display: flex;
		justify-content: flex-start;
		position: fixed;
		top: 0;
		//app-3-start



		//app-3-end
		left: 0;
		right: 0;
		z-index: 9999;
		width: 750rpx;
		height: calc(88rpx + var(--status-bar-height));
		//wx-3-start
		/* #ifdef MP */
		background: #fff;
		height: 100rpx;
		/* #endif */
		//wx-3-end
	}

	.nav-container {
		white-space: nowrap;
		z-index: 999;
		background-size: 100% 100%;
		right: 0;
		//wx-4-start
		/* #ifdef MP */
		background: #fff;
		/* #endif */
		//wx-4-end
	}

	.back_icon1 {
		display: flex;
		align-items: center;
		padding-left: 10rpx;

		image {
			width: 52rpx;
			height: 49rpx;
		}
	}

	.nav-container .item {
		display: inline-block;
		color: #fff;
		font-size: 30rpx;
		margin: 0 30rpx;
		line-height: 40rpx;
		margin-top: 25rpx;
		//wx-5-start
		/* #ifdef MP */
		color: #333333;
		/* #endif */
		//wx-5-end
	}

	.nav-container .item.on {
		font-weight: bold;
		font-size: 32rpx;
		padding-bottom: 8rpx;
		box-sizing: border-box;
		//wx-6-start
		/* #ifdef MP */
		font-weight: 800;
		color: var(--color_spell_main);
		position: relative;

		&::before {
			content: '';
			width: 90%;
			height: 3rpx;
			color: var(--color_spell_main);
			position: absolute;
			left: 50%;
			transform: translate(-50%);
			bottom: -10rpx;
			box-sizing: border-box;
			// margin: 0 10rpx;
		}
		/* #endif */
		//wx-6-end
	}

	.goods-list {
		padding: 0 20rpx;
		//app-4-start



		//app-4-end
		/* #ifdef H5 */
		padding-top: calc(88rpx + var(--status-bar-height));
		/* #endif */
	}

	.goods-list .item {
		height: 382rpx;
		display: flex;
		flex-direction: column;
		border-radius: 15rpx;
		box-sizing: border-box;
		background-color: #fff;
		font-size: 24rpx;
		color: #999;
		margin-top: 20rpx;
		padding: 20rpx 20rpx 0 20rpx;
	}

	.goods-list-top .img {
		position: relative;
		width: 270rpx;
		height: 270rpx;
		margin-right: 20rpx;
		background-color: #f8f8f8;
		border-radius: 15rpx;
	}

	.img .image {
		background-position: center center;
		background-repeat: no-repeat;
		background-size: cover;
		width: 270rpx;
		height: 270rpx;
		border-radius: 15rpx;
	}

	.img .activity-info {
		position: absolute;
		bottom: 30rpx;
		left: 30rpx;
		width: 120rpx;
		height: 80rpx;
		background-color: #ef1b21;
		border-radius: 4rpx;
		color: #fff;
		line-height: 30rpx;
		text-align: center;
	}

	.goods-list .item {
		display: flex;
		flex-direction: column;
	}

	.goods-list-top {
		display: flex;
	}

	.activity-info .t {
		display: block;
		margin: 6rpx;
		height: 36rpx;
		line-height: 36rpx;
		color: #ef1b21;
		background-color: #fff;
	}

	.goods-info {
		display: flex;
		flex-direction: column;
		position: relative;
		width: 100%;
	}

	.goods-info .info view {
		margin-bottom: 5rpx;
	}

	.goods-info .goods_price_wrap {
		display: flex;
		align-items: flex-end;
		margin-top: 30rpx;
	}

	.goods_price_wrap .now_price {
		/* display: flex; */
		font-size: 24rpx;
		font-weight: 600;
		color: var(--color_spell_main);
		font-weight: bold;
	}

	.now_price .big_price {
		font-size: 34rpx;
	}

	.goods-info .info .old_price {
		color: #9a9a9a;
		font-size: 20rpx;
		margin-left: 10rpx;
		text-decoration: line-through;
		padding-bottom: 4rpx;
	}

	.goods-info .info .p {
		font-size: 24rpx;
		color: #666;
		display: flex;
		align-items: center;
	}

	.goods-info .p .line {
		display: block;
		width: 1rpx;
		height: 20rpx;
		margin: 0 15rpx;
		background-color: #999;
	}

	.goods-info navigator {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 50rpx;
		color: #fff;
		font-size: 28rpx;
		border-radius: 38rpx;
		position: absolute;
		bottom: 0;
		left: 0;
	}

	.group_num_wrap {
		position: relative;
		display: flex;
		justify-content: center;
		align-items: center;
		height: 46rpx;
		color: var(--color_spell_main);
		font-size: 25rpx;
		border-radius: 25rpx 0 0 25rpx;
		border: 1rpx solid var(--color_spell_main);
		padding-left: 10rpx;
		padding-right: 30rpx;
	}

	.goods-info .iconImg {
		width: 31rpx;
		height: 27rpx;
		margin-right: 10rpx;
	}

	.go_to_group {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 148rpx;
		height: 46rpx;
		font-size: 26rpx;
		color: #fff;
		background-color: var(--color_spell_main);
		border-radius: 0 25rpx 25rpx 25rpx;
		margin-left: 10rpx;
		position: absolute;
		right: -128rpx;
    top: -2rpx;
	}

	.empty {
		display: flex;
		width: 750rpx;
		height: calc(100vh - 100rpx);
		flex-direction: column;
		align-items: center;
		/* justify-content: center; */
		margin-top: 200rpx;
		//wx-7-start
		/* #ifdef MP */
		margin-top: 300rpx;
		/* #endif */
		//wx-7-end
	}

	.empty .img {
		width: 380rpx;
		height: 280rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.empty .img image {
		width: 380rpx;
		height: 280rpx;
	}

	.empty .tip {
		color: #000;
		font-size: 30rpx;
		margin: 30rpx 0;
	}

	.empty text {
		font-size: 26rpx;
		color: #999;
	}

	.goods_nameBox {
		margin-top: 20rpx;
	}

	.goods_nameText {
		color: #2e2e2e;
		font-size: 29rpx;
		font-weight: 600;
		text-overflow: -o-ellipsis-lastline;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		line-clamp: 2;
		-webkit-box-orient: vertical;
	}

	.activity-info {
		width: 100%;
		height: 70rpx;
		display: flex;
		align-items: center;
		font-size: 26rpx;
		color: #666;
		margin-top: 20rpx;
		border-top: 1rpx solid #f2f2f2;
	}

	.activity-info .save_img {
		width: 30rpx;
		height: 30rpx;
		margin-right: 20rpx;
	}

	.top_wrap {
		position: fixed;
		right: 46rpx;
		bottom: 66rpx;
		width: 85rpx;
		height: 85rpx;
	}

	.top_wrap image {
		width: 85rpx;
		height: 85rpx;
	}

	.group_bottom_text {
		white-space: nowrap;
		text-overflow: ellipsis;
		overflow: hidden;
	}

	.nav-bar {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: flex-start;
		padding-left: 20rpx;
	}

	.nav-bar image {
		width: 19rpx !important;
		height: 33rpx !important;
		margin-top: 5rpx !important;
		position: initial !important;
	}

	.nav-bar view {
		font-size: 36rpx;
		font-family: PingFang SC;
		color: #FFFFFF;
		margin-left: 21rpx;
	}

	.nav-bars {
		z-index: 999;
		background: var(--color_spell_main_bg);
		width: 100%;
		top: 0;
	}
  .nav_label_box{
    display: flex;
    justify-content: flex-start;
    position: fixed;
    top: 0;
	//app-5-start



	//app-5-end
    left: 0;
    right: 0;
    z-index: 9999;
    width: 750rpx;
    height: calc(88rpx + var(--status-bar-height));
    background-color: var(--color_spell_main);
	//wx-8-start
    /* #ifdef MP */
    background: #fff;
    height: 100rpx;
    /* #endif */
	//wx-8-end
  }
  .activity-saveImg{
    font-size: 21rpx;
    width: 30rpx;
    height: 30rpx;
    background-color: var(--color_spell_main);
    color: #fff;
    border-radius: 5rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 5rpx;
  }
</style>