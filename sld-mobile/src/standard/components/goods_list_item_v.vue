<!-- 商品组件：竖直方向
	点击进入商品详情页
	应用于商品列表页面
-->
<template name="goodsListItemV">
	<view class="goods_v_item flex_column_start_start" @click="goGoodsDetail(goods_info)">
		<view class="goods-img" :style="{backgroundImage: 'url('+goods_info.goodsImage+')'}"></view>
		<view class="bottom_part flex_column_between_start">
			<view class="top flex_column_start_start">
				<view class="goods-name">
					{{goods_info.goodsName}}
				</view>
				<text v-if="goods_info.goodsBrief" class="goods-brief">{{goods_info.goodsBrief}}</text>
			</view>
			<view class="activity_con" v-if="goods_info.activityList&&goods_info.activityList.length">
				<block v-for="(item,index) in goods_info.activityList" :key="index">
					<view class="act_label ladder_group" v-if="item.promotionType==105">
						{{item.promotionName}}
					</view>
					<view class="act_label discounts" v-if="item.promotionType==201||item.promotionType==202||item.promotionType==203||item.promotionType==204">
						{{item.promotionName}}
					</view>
					
					<view class="act_label secKill" v-if="item.promotionType==104">
						{{item.promotionName}}
					</view>
					
					<view class="act_label preSale" v-if="item.promotionType==103">
						{{item.promotionName}}
					</view>
					
					<view class="act_label spellGroup" v-if="item.promotionType==102">
						{{item.promotionName}}
					</view>
				</block>
			</view>
			<view class="bottom flex_column_start_between">
				<view class="goods-price flex_row_start_center">
					<view class="left flex_row_start_center">
						<view v-if="isSuper && goods_info.superPrice" class="left_super_price flex_row_start_end">
							<text class="unit">￥</text>
							<text class="price_int">{{$getPartNumber(goods_info.superPrice,'int')}}</text>
							<text class="price_decimal">{{$getPartNumber(goods_info.superPrice,'decimal')}}</text>
						</view>
						<view v-else class="left_price flex_row_start_end">
							<text class="unit">￥</text>
							<text class="price_int">{{$getPartNumber(goods_info.goodsPrice,'int')}}</text>
							<text class="price_decimal">{{$getPartNumber(goods_info.goodsPrice,'decimal')}}</text>
						</view>
						<view v-if="isSuper && goods_info.superPrice" class="left_super_price_img"
							:style="'background-image:url('+imgUrl+'super/super_price.png)'">会员价</view>
						<!-- <text class="sales">{{$formatW(goods_info.saleNum)}}人付款</text> -->
					</view>
				</view>
				<view class="goods-sales">{{$formatW(goods_info.saleNum)}}人付款
				</view>
				<view class="store_enter" :style="{marginBottom:goods_info.goodsRank?'18rpx':'0rpx'}" @click.stop="goStoreDetail(goods_info)">
					
					<text class="stroe_name" :style="{ flex: goods_info.shopType==2?1:'' }">{{goods_info.storeName}}</text>
					<!-- <text class="store_enter_btn">{{$L('进店')}}</text> -->
					<image class="store_enter_image" :src="imgUrl+'good_search/enter_store.png'" mode="aspectFit"></image>
				</view>
				<view style="padding:0 20rpx;" v-if="goods_info.goodsRank" @click.stop="toRankDetail">
					<text class="rank_tag">{{goods_info.goodsRank}}{{' '}}></text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "goodsItemV",
		components:{
			},
		data() {
			return {
				imgUrl: process.env.VUE_APP_IMG_URL
			}
		},
		props: {
			goods_info: {
				type: Object,
				value: {}
			},
			isSuper: {
				type: Boolean,
				default: false
			}
		},
		methods: {
			//跳转店铺详情页面
			goStoreDetail(item) {
				if (item.shopType == 2) {
					this.$Router.push({ path: '/extra/o2o/store/index', query: { storeId: item.storeId }})
				} else {
					this.$Router.push({ path: '/standard/store/shopHomePage', query: { vid: item.storeId }})
				}
			},
			//进入商品详情页
			goGoodsDetail(goods_info) {
				this.$Router.push({ path: '/standard/product/detail', query: { productId: goods_info.defaultProductId,goodsId: goods_info.goodsId }})
			},
			toRankDetail(){
				this.$Router.push({path:'/standard/rank/detail',query:{rankId:this.goods_info.rankId}})
			}
		}
	}
</script>

<style lang='scss'>
	.goods_v_item {
		width: 345rpx;
		background: #fff;
		/* margin-top: 20rpx; */
		border-radius: 14rpx;
		overflow: hidden;
		margin-left: 20rpx;

		&:nth-child(1),
		&:nth-child(2) {
			margin-top: 0;
		}
		margin-bottom: 20rpx;

		.goods-img {
			background-size: cover;
			background-position: center center;
			/* background-repeat: no-repeat; */
			width: 345rpx;
			height: 345rpx;
			overflow: hidden;
			background-color: #fff;
		}

		.bottom_part {
			flex: 1;
			padding-bottom: 20rpx;
			width: 100%;
			
			.bottom {
				width: 100%;
			}
		}

		.top {
			width: 100%;
		}

		.goods-name {
			margin-top: 20rpx;
			font-size: 30rpx;
			color: $com-main-font-color;
			line-height: 38rpx;
			overflow: hidden;
			text-overflow: ellipsis;
			/* white-space: nowrap; */
			padding: 0 20rpx;
			width: 100%;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
			word-break: break-all;
			
			max-height: 76rpx;
		}

		.goods-brief {
			padding: 5rpx 20rpx 0;
			font-size: 24rpx;
			color: $main-third-color;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			width: 100%;
			height: 40rpx;
			margin-top: 6rpx;
		}

		.goods-price {
			width: 100%;
			margin-top: 10rpx;
			padding: 0 20rpx;

			.left {
				color: var(--color_main);
				align-items: baseline;

				.unit,
				.price_decimal {
					font-size: 24rpx;
				}

				.price_int {
					font-size: 34rpx;
					line-height: 34rpx;
				}
				
				.left_super_price {
					color: #242846;
					font-size: 22px;
					font-family: PingFang SC;
					font-weight: 500;
				}
				
				
				.left_super_price_img {
					width: 102rpx;
					height: 34rpx;
					line-height: 36rpx;
					color: #CFB295;
					font-size: 20rpx;
					font-family: PingFang SC;
					font-weight: 500;
					text-align: center;
					text-indent: 6rpx;
					background-position: center;
					background-repeat: no-repeat;
					background-size: cover;
					margin-left: 20rpx;
				}
			}

			.sales {
				color: $main-third-color;
				font-size: 22rpx;
				margin-left: 22rpx;
				/* margin-left: 29rpx; */
			}
		}
	
		.goods-sales {
			color: #999999;
			font-size: 22rpx;
			font-family: PingFang SC;
			font-weight: 500;
			margin-top: 18rpx;
			padding-left: 20rpx;
		}
	}

	.activity_con {
		display: flex;
		font-size: 22rpx;
		color: #ffffff;
		padding-left: 20rpx;
		height: 30rpx;
		margin-top: 19rpx;

		.act_label{
			height: 32rpx;
			border-radius: 15rpx;
			line-height: 32rpx;
			padding: 0 10rpx;
			margin-left: 10rpx;
		}

		.ladder_group{
			background: linear-gradient(22deg, #FE901E 0%, #FEAD28 100%);
		}
		
		.discounts {
			background: linear-gradient(17deg, #AB32CC 0%, #D20FA6 100%);
		}
		
		.secKill{
			background: linear-gradient(to right, #fc5300, #ff1353);
		}
		
		.preSale{
			background: linear-gradient(to right, #a62fcd, #ff006c);
		}
		
		.spellGroup{
			background: linear-gradient(to right, #ff6000, #ff9c00);
		}

		.ladder_group:nth-child(1) {
			margin-left: 0rpx;
		}
	}

	.store_enter {
		display: flex;
		align-items: center;
		width: calc(100% - 28rpx);
		font-size: 24rpx;
		padding-left: 20rpx;
		padding-right: 20rpx;
		margin-top: 18rpx;
		margin-bottom: 18rpx;
	}

	.stroe_name {
		max-width: 200rpx;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		color: #666666;
	}

	.store_enter_btn {
		color: #333333;
		font-weight: bold;
		margin-left: 10rpx;
	}

	.store_enter_image {
		flex-shrink: 0;
		width: 11rpx;
		height: 19rpx;
		margin-left: 10rpx;
	}
	
	.rank_tag{
		display: inline;
		background: #FEF1E8;
		border-radius: 2px;
		padding: 4rpx 6rpx;
		font-size: 24rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #FF6932;
		white-space: nowrap;
		overflow: hidden;
	}
</style>
