<!-- 店铺组件-->
<template name="storeItem">
	<view  class="shop_pre">
		<view class="pre_top" :data-vid="store_info.vid">
			<view class="shop_left">
				<view class="shop_avatar_img">
					<view class="shop_avatar" :style="'background-image:url('+store_info.storeLogoUrl+')'"></view>	
				</view>
				<view class="shop_des">
					<view class="des_top">
						<text class="shop_name">{{store_info.storeName}}</text>
					</view>
					<view class="des_bottom">
						<text class="shop_type" v-if="store_info.isOwnShop == 1">{{$L('自营')}}</text>
						<text class="popularity">{{$L('关注')}}{{store_info.followNumber}}</text>
						<text class="line">|</text>
						<text class="payer_number">{{$L('已售')}}{{store_info.orderFinishedCount}}</text>
					</view>
				</view>
			</view>
			<view class="go_shop" @click='goStoreDetail(store_info)'>{{$L('进店')}}</view>
		</view>
		<view class="pre_content" v-if="store_info.goodsListVOList.length">
			<view v-for="(item1, index2) in store_info.goodsListVOList" :key="index2" class="commodity" v-if="index2 < 3" :data-gid="item1.goodsId"
			 @tap="goods_detail(item1)">
				<view class="commodity_images">
					<view class="commodity_img" :style="'background-image:url('+item1.goodsImage+')'"></view>
				</view>
				<view class="commodity_price"><text>￥</text><text>{{$getPartNumber(item1.goodsPrice,'int')}}</text><text>{{$getPartNumber(item1.goodsPrice,'decimal')}}</text></view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "goodsItemH",
		data() {
			return {

			}
		},
		props: {
			store_info: {
				type: Object,
				value: {}
			},
			store_index: {
				type: Number,
				value: 0
			}
		},
		methods: {
			//进入商品详情页
			goods_detail(goods_info) {
				this.$Router.push({path:'/standard/product/detail',query:{productId:goods_info.defaultProductId,goodsId:goods_info.goodsId}})
			},
			//跳转店铺详情页面
			goStoreDetail(item) {
				let path = item.shopType==2?'/extra/o2o/store/index':'/standard/store/shopHomePage'
				this.$Router.push({
					path,
					query: {
						[item.shopType==2?'storeId':'vid']: item.storeId
					}
				})
			}
		}
	}
</script>

<style lang='scss'>
	.shop_lists {
		margin-top: 182rpx;
		width: 100%;
		background: #F5F5F5;
		padding: 20rpx 0;
		box-sizing: border-box;
	}
	
	.shop_pre {
		width: 710rpx;
		background: rgba(255, 255, 255, 1);
		border-radius: 15rpx;
		margin: 0 auto;
		box-sizing: border-box;
		margin-bottom: 20rpx;
		padding: 0 21rpx;
	}
	
	.pre_top {
		display: flex;
		width: 100%;
		height: 140rpx;
		justify-content: space-between;
		align-items: center;
	}
	
	.shop_left {
		display: flex;
		align-items: center;
	}
	
	.shop_avatar_img {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		margin-right: 20rpx;
		background: #F8F8F8;
	}
	
	.shop_avatar {
		background-position: center center;
		background-repeat: no-repeat;
		background-size: cover;
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		margin-right: 20rpx;
	}
	
	.shop_des {
		display: flex;
		flex-direction: column;
	}
	
	.des_top {
		display: flex;
		margin-bottom: 20rpx;
	}
	
	.shop_name {
		max-width: 323rpx;
		font-size: 30rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: rgba(45, 45, 45, 1);
		line-height: 32rpx;
		white-space: nowrap;
		text-overflow: ellipsis;
		overflow: hidden;
		word-break: break-all;
	}
	
	.shop_type {
		width: 56px;
		height: 30px;
		background: var(--color_main);
		border-radius: 15px;
		font-size: 22px;
		font-family: PingFang SC;
		font-weight: 600;
		color: #FFFFFF;
		line-height: 30px;
		text-align: center;
		
		transform: scale(0.5);
		margin-left: -14px;
		margin-top: -7px;
		
		
		
		
	}
	
	.des_bottom {
		display: flex;
	}
	
	.popularity {
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 600;
		color: #999999;
		line-height: 32rpx;
		margin-left: -10rpx;
	}
	
	.line {
		height: 24rpx;
		color: #999999;
		font-size: 24rpx;
		margin: 0 15rpx;
	}
	
	.payer_number {
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 600;
		color: #999999;
		line-height: 32rpx;
	}
	
	.go_shop {
		width: 100rpx;
		height: 50rpx;
		border: 1px solid var(--color_main);
		border-radius: 25rpx;
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: 600;
		color:  var(--color_main);
		line-height: 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.pre_content {
		display: flex;
	}
	
	.commodity {
		width: 216rpx;
		height: 216rpx;
		position: relative;
		margin-right: 10rpx;
		margin-bottom: 21rpx;
		background: #F5F5F5;
		border-radius: 10rpx;
	}
	
	.commodity:nth-of-type(3) {
		margin-right: 0;
	}
	
	.commodity_images {
		width: 216rpx;
		height: 216rpx;
		border-radius: 10rpx;
		margin-bottom: 20rpx;
		background: #F8F8F8;
	}
	
	.commodity_img {
		background-position: center center;
		background-repeat: no-repeat;
		background-size: cover;
		width: 216rpx;
		height: 216rpx;
		border-radius: 10rpx;
		margin-bottom: 20rpx;
	}
	
	.commodity_price {
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 600;
		color: #FFFFFF;
		line-height: 24rpx;
		position: absolute;
		width: 100%;
		bottom: 0;
		z-index: 7;
		text-align: center;
		height: 36rpx;
		background: rgba(0, 0, 0, 0.3);
		border-radius: 0 0 10rpx 10rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.commodity_price text:nth-child(1),
	.commodity_price text:nth-last-child(1) {
		font-size: 20rpx;
	}
	
	.filter_title {
		margin-top: 40rpx;
		margin: 28rpx;
	}
	
	.default {
		color: #dddddd !important;
	}
	
	.default .iconfont {
		color: #dddddd !important;
	}
	.attributr_sel_con{
		.attributr_sel_text{
			color: red;
		}
		
		font-size: 12rpx;
	}
</style>
