<template>
	<view class="presale2_box">
		<view class="presale2" :style="'background-image:url('+imgUrl+'preSale/open_bg.png);background-size:100% 100%;background-repeat:no-repeat;position:relative'">
			<view class="presale1_left">
				<!-- 进行中 -->
				<view class="presale1_price">
					<view class="presale1_title">{{$L('预售价')}}: </view>
					<view class="presale1_title_b"><text>{{$L('￥')}}</text><text>{{preSellInfo.presellPrice}}</text></view>
					<view class="presale1_price1 presale1_price2">
						<!-- <image :src="imgUrl + 'site/yushouhuore.png'" mode="aspectFit"></image> -->
						<view>
							<view class="presale_oragin_price">{{$L('原价')}}：{{preSellInfo.productPrice}}</view>
						</view>
					</view>
				</view>
			</view>
			<view class="presale1_right">
				<view class="collect_deposit" v-if="preSellInfo.type==1">
					<text>{{$L('预售定金')}}</text>
					<text>{{$L('￥')}}{{preSellInfo.firstMoney}}</text>
					<text v-if="preSellInfo.firstExpand>0">{{$L('可抵')}}</text>
					<text v-if="preSellInfo.firstExpand>0">{{$L('￥')}}{{preSellInfo.firstExpand}}</text>
				</view>
				<view class="collect_deposit" v-if="preSellInfo.type==2&&preSellInfo.pre_run!=1">
					<text>{{$L('已售')}}</text>
					<text>{{preSellInfo.saleNum}}{{$L('件')}}</text>
				</view>
				<view class="collect_deposit" v-if="preSellInfo.type==2&&preSellInfo.pre_run==1">
					<text></text>
					<text>{{$L('即将开始')}}</text>
				</view>
				<view class="presale1_start_time" v-if="preSellInfo.pre_run == 1">{{$L('活动开始时间')}}:{{preSellInfo.startTime}}</view>
				<view class="presale1_start_time1" v-else>{{$L('活动结束时间')}}：{{preSellInfo.endTime}}</view>
			</view>
		</view>
		<view class="presale3" v-if="preSellInfo.pre_run!=1">
			<view class="presale_desc" v-if="preSellInfo.type==1">{{$L('付尾款时间')}}：{{preSellInfo.remainStartTime}} ~ {{preSellInfo.remainEndTime}}</view>
			<view class="presale_desc">{{$L('发货时间')}}：{{preSellInfo.deliverTime}}</view>
		</view>
	</view>
</template>

<script>
	export default{
		props:['preSellInfo'],
		data(){
			return{
				imgUrl:process.env.VUE_APP_IMG_URL
			}
		},
		created() {
			let countTime = 0;
			let now = new Date()
			let startTime = new Date(this.preSellInfo.startTime)
			if (now < startTime) {
				countTime = (startTime.getTime() - now.getTime()) / 1000

			} else {
				countTime = this.preSellInfo.distanceEndTime
			}
			this.secInterval = setInterval(() => {
				if (countTime == 0) {
					//倒计时结束，清除倒计时
					clearInterval(this.secInterval);
					this.$emit('getGoodsDetail')
				} else {
					countTime--;
				}
			}, 1000)
		}
	}
</script>

<style lang="scss">
	.presale3{
		background-color: #fff;
		padding: 20rpx;
		.presale_desc{

			font-size: 26rpx;
			color: #999999;
			&:nth-child(2){
				margin-top: 14rpx;
			}
		}
	}

	.presale1 {
		width: 100%;
		height: 163rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 20rpx 0 53rpx;
		box-sizing: border-box;
	}

	.presale1_left {
		display: flex;
		flex-direction: column;
		padding-top: 15rpx;
		height: 100%;
	}

	.presale1_price {
		display: flex;
		flex-direction: column;

	}

	.presale1_title {
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: nornal;
		color: rgba(254, 254, 254, 1);
		line-height: 40rpx;
	}

	.presale1_title_b {
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: nornal;
		color: rgba(254, 254, 254, 1);
		line-height: 46rpx;
		margin-left: 40rpx;
		text:nth-child(2){
			font-size: 45rpx;
		}
	}

	.presale1_price1 {
		display: flex;
		align-items: center;
	}

	.presale1_price2 {
		display: flex;
		align-items: flex-start;
		margin-top: 10rpx;
	}

	.presale1_price1 image {
		width: 24rpx;
		height: 28rpx;
	}

	.presale1_prices {
		display: flex;
		font-size: 30rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: rgba(255, 255, 255, 1);
	}

	.presale_oragin_price {
		font-size: 22rpx;
		font-family: PingFang SC;
		font-weight: 400;
		text-decoration: line-through;
		color: rgba(254, 254, 254, 1);
		opacity: 0.7;
		margin-left: 36rpx;
	}

	.presale1_right {
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.presale2 {
		width: 100%;
		height: 163rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 20rpx 0 39rpx;
		box-sizing: border-box;
	}

	.collect_deposit {
		display: flex;
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: rgba(255, 255, 255, 1);
		align-items: center;
		white-space: pre-wrap;
	}

	.collect_deposit text:nth-child(2){
		font-size: 40rpx;
		margin-right: 10rpx;
		font-weight: bold;
		white-space: pre-wrap;
	}
	.collect_deposit text:nth-child(4) {
		font-size: 26rpx;
		margin-right: 10rpx;
		font-weight: bold;
		white-space: pre-wrap;
	}

	.presale1_start_time {
		width: 380rpx;
		height: 30rpx;
		background: rgba(255, 255, 255, 1);
		border-radius: 15rpx;
		font-size: 20rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: var(--color_presell_main);
		text-align: center;
		line-height: 30rpx;
		margin-top: 10rpx;
	}

	.presale1_start_time1 {
		width: 380rpx;
		height: 30rpx;
		background:rgba(255, 255, 255, 1);
		border-radius: 15rpx;
		font-size: 20rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: var(--color_presell_main);
		text-align: center;
		line-height: 30rpx;
		margin-top: 10rpx;
	}
  .presale2_box{
    background: var(--color_presell_main_bg);
  }
</style>
