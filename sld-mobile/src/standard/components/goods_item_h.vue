<!-- 商品组件：横向展示，一行一个，商品列表页面
	点击进入商品详情页
	加入购物车事件
-->
<template name="goodsCollectItemH">
	<view class="goods_h_item flex_row_start_start" @click="goGoodsDetail(goods_info)">
		<view class="goods-img" :style="{backgroundImage: 'url('+goods_info.goodsImage+')'}"></view>
		<view class="right flex_column_between_start">
			<view class="top flex_column_start_start" style="width: 100%;">
				<view class="goods-name">
					{{goods_info.goodsName}}
				</view>
				<text class="goods-brief">{{goods_info.goodsBrief}}</text>
			</view>
			<view class="activity_con1" v-if="goods_info.activityList&&goods_info.activityList.length>0">
				<block v-for="(item,index) in goods_info.activityList" :key="index">
					<view class="act_label ladder_group" v-if="item.promotionType==105">
						{{item.promotionName}}
					</view>
					<view class="act_label discounts"
						v-if="item.promotionType==201||item.promotionType==202||item.promotionType==203||item.promotionType==204">
						{{item.promotionName}}
					</view>

					<view class="act_label secKill" v-if="item.promotionType==104">
						{{item.promotionName}}
					</view>

					<view class="act_label preSale" v-if="item.promotionType==103">
						{{item.promotionName}}
					</view>

					<view class="act_label spellGroup" v-if="item.promotionType==102">
						{{item.promotionName}}
					</view>
				</block>
			</view>

			<view class="bottom flex_column_start_between">
				<!-- 价格 -->
				<view class="goods-price flex_row_start_center">
					<view class="left flex_row_start_center">
						<block v-if="isSuper && goods_info.superPrice">
							<view class="left_super_price">
								<text class="unit">￥</text>
								<text class="price_int">{{$getPartNumber(goods_info.superPrice,'int')}}</text>
								<text class="price_decimal">{{$getPartNumber(goods_info.superPrice,'decimal')}}</text>
							</view>
							<view class="left_super_price_img"
								:style="'background-image:url('+imgUrl+'super/super_price.png)'">会员价
							</view>
						</block>
						<view class="left_price flex_row_start_end"
							:class="{super_original_price:(isSuper && goods_info.superPrice)}"
							v-if="(goods_info.superPrice&&textLen<14)||!(goods_info.superPrice)">
							<text class="unit">￥</text>
							<text class="price_int">{{$getPartNumber(goods_info.goodsPrice,'int')}}</text>
							<text class="price_decimal">{{$getPartNumber(goods_info.goodsPrice,'decimal')}}</text>
						</view>
						<text class="sales" v-if="!goods_info.superPrice">{{$formatW(goods_info.saleNum)}}人付款</text>
					</view>
				</view>
				
				
				
				<!-- 活动标签 -->
				<view class="" v-if="goods_info.goodsRank" @click.stop="toRankDetail">
					<text class="rank_tag">{{goods_info.goodsRank}}{{' '}}></text>
				</view>
			</view>
		</view>
	</view>
	</view>
</template>

<script>
	import jyfParser from '@/components/jyf-parser/jyf-parser.vue'
	export default {
		name: "goodsCollectItemH",
		components: {
			jyfParser,
			},
		data() {
			return {
				imgUrl: process.env.VUE_APP_IMG_URL
			}
		},
		props: {
			goods_info: {
				type: Object,
				value: {}
			},
			isSuper: {
				type: Boolean,
				default: false
			}
		},
		computed: {
			textLen() {
				let {
					goodsPrice,
					superPrice
				} = this.goods_info
				let goodsPrice_len = goodsPrice.toString().length
				let superPrice_len = (superPrice ?? "").toString().length
				return goodsPrice_len + superPrice_len
			}
		},
		methods: {
			//跳转店铺详情页面
			goStoreDetail(item) {
				if (item.shopType == 2) {
					this.$Router.push({ path: '/extra/o2o/store/index', query: { storeId: item.storeId }})
				} else {
					this.$Router.push({ path: '/standard/store/shopHomePage', query: { vid: item.storeId }})
				}
			},
			//进入商品详情页
			goGoodsDetail(goods_info) {
				this.$Router.push({
					path: '/standard/product/detail',
					query: {
						productId: goods_info.defaultProductId,
						goodsId: goods_info.goodsId
					}
				})
			},
			toRankDetail() {
				this.$Router.push({
					path: '/standard/rank/detail',
					query: {
						rankId: this.goods_info.rankId
					}
				})
			}

		}
	}
</script>

<style lang='scss'>
	.goods-name ::v-deep.ql-editor {
		padding: 0;
	}


	.goods_h_item {
		width: 100%;
		background: #fff;
		padding: 20rpx;
		overflow: hidden;
		background: #fff;
		border-top: 1rpx solid #f5f5f5;
		/* &:first-child {
			padding-top: 20rpx;
		} */

		.goods-img {
			background-size: cover;
			background-position: center center;
			background-repeat: no-repeat;
			width: 290rpx;
			height: 290rpx;
			overflow: hidden;
			border-radius: 10rpx;
			background-color: #F8F6F7;
			flex-shrink: 0;
		}

		.right {
			height: 290rpx;
			padding: 10rpx 0 0rpx;
			width: 420rpx;
			padding-left: 20rpx;

		}

		.goods-name {
			width: 100%;
			font-size: 28rpx;
			color: #333;
			line-height: 38rpx;
			height: 76rpx;
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
			word-break: break-all;
		}

		.goods-brief {
			color: $main-third-color;
			font-size: 22rpx;
			margin-top: 10rpx;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			width: 100%;
		}

		.goods-price {
			width: 100%;
			margin-bottom: 6rpx;

			.left {
				color: var(--color_price);

				.left_price {
					align-items: baseline;
				}

				.super_original_price {
					margin-left: 20rpx;
					color: #848484;

					.unit,
					.price_decimal {
						font-size: 22rpx;
					}

					.price_int {
						font-size: 26rpx;
					}
				}

				.left_super_price {
					color: #242846;
					font-family: PingFang SC;
					font-weight: 600;
				}

				.left_super_price_img {
					width: 102rpx;
					height: 34rpx;
					line-height: 36rpx;
					color: #CFB295;
					font-size: 20rpx;
					font-family: PingFang SC;
					font-weight: 500;
					text-align: center;
					text-indent: 6rpx;
					background-position: center;
					background-repeat: no-repeat;
					background-size: cover;
					margin-left: 20rpx;
				}

				.unit,
				.price_decimal {
					font-size: 24rpx;
				}

				.price_int {
					font-size: 34rpx;
				}

				.sales {
					color: $main-third-color;
					font-size: 22rpx;
					margin-left: 26rpx;
					margin-top: 2rpx;
				}
			}

			image {
				width: 42rpx;
				height: 42rpx;
			}
		}

		.goods-sales {
			color: #999999;
			font-size: 22rpx;
			font-family: PingFang SC;
			font-weight: 500;
			margin-bottom: 10rpx;
		}
	}

	.activity_con1 {
		display: flex;
		font-size: 22rpx;
		color: #ffffff;

		.act_label {
			height: 32rpx;
			border-radius: 15rpx;
			line-height: 32rpx;
			padding: 0 10rpx;
			margin-left: 10rpx;
		}

		.ladder_group {
			background: linear-gradient(22deg, #FE901E 0%, #FEAD28 100%);
		}

		.discounts {
			background: linear-gradient(17deg, #AB32CC 0%, #D20FA6 100%);
		}

		.secKill {
			background: linear-gradient(to right, #fc5300, #ff1353);
		}

		.preSale {
			background: linear-gradient(to right, #a62fcd, #ff006c);
		}

		.act_label:nth-child(1) {
			margin-left: 0rpx;
		}

		.spellGroup {
			background: linear-gradient(to right, #ff6000, #ff9c00);
		}

	}

	.store_enter1 {
		display: flex;
		font-size: 24rpx;
		align-items: center;
	}

	.stroe_name {
		max-width: 200rpx;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		color: #666666;
	}

	.store_enter_btn {
		color: #333333;
		font-weight: bold;
		margin-left: 10rpx;

	}

	.store_enter_image {
		width: 11rpx;
		height: 19rpx;
		margin-left: 10rpx;
	}

	.rank_tag {
		display: inline;
		background: #FEF1E8;
		border-radius: 2px;
		padding: 2rpx 6rpx;
		font-size: 20rpx;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #FF6932;
	}
</style>