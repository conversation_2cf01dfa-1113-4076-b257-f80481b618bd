<template>
	<view class="content" v-if="isShow" :style="mix_diyStyle">
		<view class="navbar">
			<view v-for="(item, index) in navList" :key="index" class="nav-item"
				:class="{ current: tabCurrentIndex === index }" @click="tabClick(index)">
				{{ item.text }}
			</view>
		</view>
		<swiper :current="tabCurrentIndex" class="swiper-box" duration="300" @change="changeTab"
			:disable-touch="disTouch">
			<swiper-item class="tab-content" v-for="(tabItem, tabIndex) in navList" :key="tabIndex">
				<scroll-view class="list-scroll-content" scroll-y @scrolltolower="scrollLoadData"
					@scroll="(e) => handleScroll(e, tabIndex)" :scroll-top="scrollToTop">
					<!-- 订单列表 -->
					<template v-if="tabItem.orderList.length > 0">
						<view v-for="(item, index) in tabItem.orderList" :key="index"
							class="order-item flex_column_start_start" @click="goOrderDetail(item.orderSn)">
							<view class="i-top">
								<text class="order_sn">{{ $L('订单编号') }}：{{ item.orderSn }}</text>
								<text class="state" :style="{ color: item.stateTipColor }">{{item.orderStateValue}}</text>
							</view>
							<view v-if="item.orderProductList" class="goods-box flex_row_start_start"
								v-for="(goodsItem, goodsIndex) in item.orderProductList" :key="goodsIndex">
								<view class="left flex_row_start_start">
									<view class="goods-img" :style="{backgroundImage: 'url(' + goodsItem.productImage + ')'}"></view>
								</view>
								<view class="right flex_column_between_start">
									<view class="right_top">
										<view class="goods_name">{{ goodsItem.goodsName }}</view>
										<view class="goods_info flex_row_between_center">
											<text class="spec_info">{{goodsItem.specValues ? goodsItem.specValues : ''}}</text>
											<text class="goods_num">*{{ goodsItem.productNum }}</text>
										</view>
									</view>
									<view class="goods_point_price flex_row_start_end">
										<view class="goods_point" v-if="goodsItem.integralPrice">
											{{ goodsItem.integral }}{{ $L('积分') }}
										</view>
										<view class="goods_point"
											v-if="goodsItem.integralPrice && goodsItem.cashAmount">+</view>
										<view class="goods_price" v-if="goodsItem.cashAmount">
											￥{{ filters.toFix(goodsItem.cashAmount) }}</view>
									</view>
								</view>
							</view>
							<view class="price-box flex_row_end_end">
								<text class="order_mount">{{ $L('合计') }}：</text>
								<view class="integral_des">
									<text class="integral_des_point">{{ item.integral }}{{ $L('积分') }}+</text>
									<text class="total_price">{{ $L('¥') }}{{ $getPartNumber(item.cashAmount, 'int')}}{{ $getPartNumber(item.cashAmount, 'decimal') }}</text>
								</view>
							</view>
							<view class="action-box" v-show="item.orderState != 20">
								<!-- 待收货、已完成订单可以查看物流 -->
								<button v-if="(item.orderState == 30 || item.orderState == 40) &&item.isVirtualGoods == 1" class="action-btn flex_row_center_center" @click.stop="lookLogistics(item.orderSn, item.productLeadLittle)">
									{{ $L('查看物流') }}
								</button>
								<!-- 待收货订单可以确认收货 -->
								<button v-if="item.orderState == 30" class="action-btn recom flex_row_center_center"
									@click.stop="confirmReceipt(item.orderSn)">
									{{ $L('确认收货') }}
								</button>
								<!-- 待付款订单可以取消订单 -->
								<button v-if="item.orderState == 10" class="action-btn flex_row_center_center"
									@click.stop="cancelPopup(item.orderSn)">
									{{ $L('取消订单') }}
								</button>
								<!-- 待付款订单可以立即支付 -->
								<button v-if="item.orderState == 10" class="action-btn recom flex_row_center_center"
									@click.stop="goPay(item.paySn)">
									{{ $L('立即支付') }}
								</button>
								<!-- 已取消、全部评价完成订单可以删除订单 -->
								<button v-if="item.orderState == 0 || item.orderState == 40"
									class="action-btn flex_row_center_center" @click.stop="delOrder(item.orderSn)">
									{{ $L('删除订单') }}
								</button>
							</view>
						</view>
					</template>
					<view v-if="tabItem.loadingState != 'first_loading' &&tabItem.orderList.length == 0" class="empty_part flex_column_start_center">
						<image :src="imgUrl + 'empty_orders.png'" />
						<text>{{ $L('这里空空如也~快去商品中心加购商品吧') }}！</text>
						<button class="flex_row_center_center" @click="goGoodsList" :plain="true">
							{{ $L('马上去逛逛') }}
						</button>
					</view>
					<view v-if="!recommendShow && navList[tabCurrentIndex].loadingState != 'no_more_data' && (tabItem.loadingState == 'first_loading' || tabItem.orderList.length > 0)"
						:class="(scrollTop>0 && scrollCurrent>0) ? 'scroll_loading' : ''">
						<loadingState :state="tabItem.loadingState" />
					</view>
					<!-- <block v-if="tabCurrentIndex == 1">
						<loadingState :state='tabItem.loadingState' />
					</block> -->
					<view v-if="recommendShow && navList[tabCurrentIndex].loadingState != 'first_loading' && tabIndex == tabCurrentIndex">
						<recommendList ref="recommendList"></recommendList>
					</view>
				</scroll-view>
			</swiper-item>
		</swiper>

		<!-- 取消订单选择原因弹框 -->
		<uni-popup ref="cancelPopup" type="bottom">
			<view class="cancel_popup">
				<view class="popup_top">
					<text>{{ $L('取消原因') }}</text>
					<image :src="imgUrl + 'order-detail/guanbi.png'" mode="aspectFit" @click="notCancel"></image>
				</view>
				<scroll-view class="uni-list cancel_list" scroll-y="true">
					<radio-group @change="radioChange">
						<label class="cancle_pre" v-for="(item, index) in cancelList" :key="index">
							<text>{{ item.content }}</text>
							<radio :value="item.value" :checked="item.value === current"
								:color="diyStyle_var['--color_integral_main']"
								style="transform: scale(0.8); margin-right: 0" />
						</label>
					</radio-group>
				</scroll-view>
				<view class="cancel_popup_btn">
					<text class="" @click="notCancel()">{{ $L('暂不取消') }}</text>
					<text class="" @click="confirmCancel()">{{ $L('确定取消') }}</text>
				</view>
			</view>
		</uni-popup>
	</view>
</template>
<script>
	import loadingState from "@/components/loading-state.vue";
	import recommendList from "../components/recommend_list.vue";
	import filters from "../../../utils/filter.js";
	import {
		mapState
	} from 'vuex';
	export default {
		components: {
			loadingState,
			recommendList
		},
		data() {
			return {
				imgUrl: process.env.VUE_APP_IMG_URL,
				tabCurrentIndex: 0,
				navList: [
					{
						state: 0,
						text: this.$L('全部'),
						loadingState: 'first_loading',
						orderList: [],
						current: 1, //分页
					},
					{
						state: 1,
						text: this.$L('待付款'),
						loadingState: 'first_loading',
						orderList: [],
						current: 1, //分页
					},
					{
						state: 2,
						text: this.$L('待发货'),
						loadingState: 'first_loading',
						orderList: [],
						current: 1, //分页
					},
					{
						state: 3,
						text: this.$L('待收货'),
						loadingState: 'first_loading',
						orderList: [],
						current: 1, //分页
					}
				],
				stopPullDownRefresh: false, //是否下拉刷新中
				current: '0', //取消原因当前点击的是第0项
				reasonId: -1, //取消原因当前点击的原因id
				cancelList: [], //取消原因列表
				curOrderSn: '', //当前订单的订单号
				isHasMore: true,
				pn: 1,
				recGoodsList: [],
				isloading: 'first_loading',
				isShow: false,
				recommendShow: false, //推荐商品是否显示
				ifOnShow: true,
				filters,
				// #ifdef H5
				disTouch: true,
				// #endif
				// #ifndef H5
				disTouch: false,
				// #endif
				
				scrolling: false, //是否进入滚动事件处理中
				scrollTop: 0, //滚动条滚动高度
				scrollToTop: 0, //返回列表页时当前tab滚动到的高度
				scrollCurrent: 0, //返回列表页时当前tab滚动到的页数
			};
		},
		onLoad(options) {
			setTimeout(() => {
				uni.setNavigationBarTitle({
					title: this.$L('我的积分订单')
				});
			}, 0);

			/**
			 * 修复app端点击除全部订单外的按钮进入时不加载数据的问题
			 * 替换onLoad下代码即可
			 */
			if (this.$Route.query.state) {
				this.tabCurrentIndex = +this.$Route.query.state;
			} else {
				this.tabCurrentIndex = 0;
			}
			//wx-1-start
			// #ifdef MP
			if (this.$Route.query.state == 0) {
				this.initialList()
			}
			// #endif
			//wx-1-end
			uni.showLoading({
				title: this.$L('加载中'),
				icon: 'none'
			})
		},
		onShow() {
			// 返回列表页判断是否进入滚动定位-start
			if (this.scrollTop > 0 && this.navList[this.tabCurrentIndex].current > 1) {
				this.scrollToTop = 0;
				this.scrollCurrent = this.navList[this.tabCurrentIndex].current;
			}
			// 返回列表页判断是否进入滚动定位-end
			if (this.ifOnShow) {
				let index = this.tabCurrentIndex;
				if (index > 0) {
					this.navList[0].loadingState = 'first_loading';
					this.navList[0].orderList = [];
					this.navList[0].current = 1;
				}
				this.initialList()
				this.recommendShow = false;
				this.ifOnShow = false
			}

		},
		computed: {
			...mapState(['userInfo'])
		},
		//下拉刷新
		onPullDownRefresh() {
			this.stopPullDownRefresh = true; //下拉刷新状态
			this.initialList();
		},
		methods: {
			initialList(){
				let index = this.tabCurrentIndex;
				let navItem = this.navList[index];
				const userInfo = uni.getStorageSync('userInfo')
				if(this.hasLogin||userInfo){
					uni.showLoading({
						title: this.$L('加载中'),
						icon: 'none'
					})
					navItem.loadingState = 'first_loading';
					navItem.orderList = [];
					navItem.current = 1;
					this.loadData();
				}else{
					this.$refs.loginPop.openLogin('no_replace')
					navItem.loadingState = 'no_more_data';
					this.recommendShow = true
				}
			},
			//推荐商品
			getData() {
				if (this.tabCurrentIndex != 1) {
					this.$refs.recommendList[this.tabCurrentIndex].getMoreData();
				}
			},
			//获取订单列表
			loadData(source) {
				//将订单挂载到tab列表下,起到缓存的效果，避免多次请求
				let index = this.tabCurrentIndex;
				let navItem = this.navList.filter(item => item.state == index)[0];
				let state = navItem.state;

				if (source === 'tabChange' && navItem.loadingState !== 'first_loading') {
					//tab切换只有第一次需要加载数据
					return;
				}
				if (navItem.loadingState === 'loading') {
					//防止重复加载
					return;
				}

				if (navItem.loadingState == 'no_more_data') {
					//加载推荐商品
					this.getData();
					//已经没有数据，无需再请求
				} else {
					this.getOrderList();
				}
			},
			//滚动触底事件
			scrollLoadData() {
				if (this.scrolling || this.scrollCurrent>0) return;
				let index = this.tabCurrentIndex;
				let navItem = this.navList.filter(item => item.state == index)[0];
				if (navItem.loadingState != 'no_more_data') {
					this.scrolling = true;
					this.getOrderList()
				}
			
				if (navItem.loadingState == 'no_more_data') {
					this.getData();
				}
			},
			//滚动条滚动事件
			handleScroll(e, tabIndex) {
				if (this.tabCurrentIndex == tabIndex && !this.scrollCurrent) {
					this.scrollTop = e.target.scrollTop;
				}
			},
			//此方法只有删除订单，取消订单等需要从列表中删除订单时调用，其余获取订单列表请调用loadData
			getOrderList() {
				let _this = this;
				let index = this.tabCurrentIndex;
				let navItem = this.navList.filter(item => item.state == index)[0];
				let state = navItem.state;

				let param = {};
				param.url = 'v3/integral/front/integral/order/list';
				param.data = {};
				param.data.pageSize = 10;
				param.data.current = navItem.current;
				navItem.loadingState = navItem.loadingState == 'first_loading' ? navItem.loadingState : 'loading';
				//状态处理 订单状态：0-已取消；10-待付款订单；20-代发货订单；30-待收货订单；40-已完成
				if (navItem.state == 0) {
					param.data.state = ''; //全部订单
				} else if (navItem.state == 1) {
					param.data.orderState = 10; //待付款
				} else if (navItem.state == 2) {
					param.data.orderState = 20; //待发货
				} else if (navItem.state == 3) {
					param.data.orderState = 30; //待收货
				}

				this.$request(param).then(res => {
					if (this.scrolling) {
						this.scrolling = false;
					}
					if (res.state == 200) {
						if (navItem.current == 1) {
							navItem.orderList = res.data.list
						} else {
							navItem.orderList = navItem.orderList.concat(res.data.list);
						}
						let hasMore = this.$checkPaginationHasMore(res.data.pagination); //是否还有数据
						if (hasMore) {
							navItem.current++;
							if (!(this.scrollCurrent > 0 && this.scrollCurrent >= navItem.current)) {
								navItem.loadingState = 'allow_loading_more';
							}
						} else {
							navItem.loadingState = 'no_more_data';
							this.recommendShow = true;
						}
						uni.hideLoading();
						this.isShow = true;

						// 返回列表页判断是否进入滚动定位-start
						if (this.scrollCurrent > 0) {
							if (hasMore && this.scrollCurrent >= navItem.current) {
								navItem.loadingState = 'loading';
								this.getOrderList();
							} else if ((!hasMore && this.scrollCurrent == navItem.current) || (hasMore && this.scrollCurrent < navItem.current)) {
								//滚动到最后一个需要获取的页面时，再给滚动高度赋值
								this.scrollCurrent = 0;
								setTimeout(() => {
									_this.scrollToTop = _this.scrollTop;
								}, 200)
							}
						}
						// 返回列表页判断是否进入滚动定位-end
					} else {
						this.$api.msg(res.msg);
					}
					if (this.stopPullDownRefresh) {
						this.stopPullDownRefresh = false;
						uni.stopPullDownRefresh();
					}
				}).catch((e) => {
					//异常处理
				})
			},
			//刷新当前列表
			refreshOrderList() {
				let index = this.tabCurrentIndex;
				let navItem = this.navList.filter(item => item.state == index)[0];
				navItem.current = 1
				this.getOrderList()
			},
			//swiper 切换
			changeTab(e) {
				this.tabCurrentIndex = e.target.current;
				this.isHasMore = true;
				this.pn = 1;
				this.recGoodsList = [];
				this.isloading = 'first_loading';
				this.loading = false;
				this.loadData('tabChange');
			},
			//顶部tab点击
			tabClick(index) {
				this.tabCurrentIndex = index;
				this.isHasMore = true;
				this.pn = 1;
				this.isloading = 'first_loading';
				this.loading = false;
				this.recGoodsList = [];



			},
			//马上去逛逛事件
			goGoodsList() {
				this.ifOnShow = true
				this.$Router.push(`/standard/point/index/index`)
			},
			//跳转订单详情页
			goOrderDetail(orderSn) {
				this.ifOnShow = true
				this.$Router.push({
					path: '/standard/point/order/detail',
					query: {
						orderSn
					}
				})
			},
			//删除订单操作
			delOrder(orderSn) {
				let that = this;
				uni.showModal({
					title: that.$L('提示'),
					content: that.$L('确定删除该订单?'),
					success: function(res) {
						if (res.confirm) {
							let param = {};
							param.url = 'v3/integral/front/integral/orderOperate/delete';
							param.method = 'POST';
							param.data = {};
							param.data.orderSn = orderSn;
							that.$request(param).then(res => {
								if (res.state == 200) {
									that.refreshOrderList();
									that.refreshOrder()
									that.$api.msg(res.msg);
								} else {
									that.$api.msg(res.msg);
								}
							}).catch((e) => {
								//异常处理
							})
						}
					}
				})
			},
			//立即支付
			goPay(paySn) {
				this.ifOnShow = true
				this.$Router.push({
					path: '/standard/point/product/pay',
					query: {
						paySn,
						payMethodType: 'orderList'
					}
				})
			},
			//打开取消订单弹框
			cancelPopup(parentSn) {
				this.$refs.cancelPopup.open();
				this.curOrderSn = parentSn;
				this.getCancelList();
			},
			//获取取消订单原因列表
			getCancelList() {
				let param = {};
				param.url = 'v3/system/front/reason/list';
				param.method = 'GET';
				param.data = {};
				param.data.type = 104;
				this.$request(param).then(res => {
					if (res.state == 200) {
						this.cancelList = res.data || [];
						this.cancelList && this.cancelList.map((item, index) => item.value = '' + index);
						this.reasonId = this.cancelList[0].reasonId;
					} else {
						this.$api.msg(res.msg);
					}
				}).catch((e) => {
					//异常处理
				})
			},
			//取消原因单选框切换
			radioChange(e) {
				for (let i = 0; i < this.cancelList.length; i++) {
					if (this.cancelList[i].value === e.target.value) {
						this.reasonId = this.cancelList[i].reasonId;
						break;
					}
				}
			},
			//暂不取消订单
			notCancel() {
				this.$refs.cancelPopup.close();
				// this.goRefresh();
			},
			//确定取消订单
			confirmCancel() {
				let that = this;
				uni.showModal({
					title: that.$L('提示'),
					content: that.$L('确定取消该订单?'),
					success: function(res) {
						if (res.confirm) {
							let param = {};
							param.url = 'v3/integral/front/integral/orderOperate/cancel';
							param.method = 'POST';
							param.data = {};
							param.data.orderSn = that.curOrderSn;
							param.data.reasonId = that.reasonId;
							that.$request(param).then(res => {
								if (res.state == 200) {
									that.refreshOrderList()
									that.refreshOrder()
									that.$api.msg(res.msg);
									that.$refs.cancelPopup.close();
								} else {
									that.$api.msg(res.msg);
								}
							}).catch((e) => {
								//异常处理
							})
						} else if (res.cancel) {
							that.$refs.cancelPopup.close();
						}
					}
				})
			},
			//更新当前页面方法
			goRefresh() {
				let pages = getCurrentPages();
				let currPage = pages[pages.length - 1]; //当前页面
				let index = this.tabCurrentIndex;
				let navItem = this.navList[index];
				navItem.loadingState = 'first_loading';
				navItem.orderList = [];
				navItem.current = 1;
				currPage.loadData(); //更新当前页面数据
			},
			//查看物流
			lookLogistics(orderSn, productLeadLittle) {
				this.ifOnShow = true
				this.$Router.push({
					path: '/standard/point/order/lookLogistics',
					query: {
						orderSn
					}
				})
			},
			// 确认收货
			confirmReceipt(orderSn) {
				let _this = this
				uni.showModal({
					title: _this.$L('提示'),
					content: _this.$L('确认收货？'),
					success: function(res) {
						if (res.confirm) {
							let param = {}
							param.url = 'v3/integral/front/integral/orderOperate/receive'
							param.method = 'POST'
							param.data = {
								orderSn: orderSn
							}
							_this.$request(param).then(res => {
								if (res.state == 200) {
									uni.showToast({
										title: _this.$L('收货成功！'),
										icon: 'none',
										duration: 700
									})
									_this.refreshOrderList()
									_this.refreshOrder()
								} else {
									this.$api.msg(res.msg)
								}
							})
						}
					}
				})
			},
			//当全部的部分订单操作后，其他状态的列表要更新
			refreshOrder() {
				let index = this.tabCurrentIndex;
				if (index == 0) {
					this.navList[1].loadingState = 'first_loading'
					this.navList[2].loadingState = 'first_loading'
					this.navList[3].loadingState = 'first_loading'
				}else{
					this.navList[0].loadingState = 'first_loading'
				}
			}
		}
	}
</script>

<style lang="scss">
	.content {
		background: $bg-color-split;
		height: 100%;
		width: 750rpx;
		margin: 0 auto;
	}

	button::after {
		border: none;
	}

	.swiper-box {
		height: calc(100vh - 40px);
	}

	.list-scroll-content {
		height: 100%;
		
		.scroll_loading {
			width: 100%;
			position: fixed;
			top: 80rpx;
			left: 50%;
			transform: translateX(-50%);
		}
	}

	.navbar {
		display: flex;
		height: 80rpx;
		padding: 0 5px;
		background: #fff;
		position: relative;
		z-index: 10;

		.nav-item {
			flex: 1;
			display: flex;
			justify-content: center;
			align-items: center;
			height: 100%;
			font-size: 32rpx;
			color: $main-font-color;
			position: relative;

			&.current {
				color: var(--color_integral_main);
				font-size: 32rpx;
				font-weight: bold;

				&:after {
					content: '';
					position: absolute;
					left: 50%;
					bottom: 0;
					transform: translateX(-50%);
					width: 35rpx;
					height: 8rpx;
					background-color: var(--color_integral_main);
					border-radius: 4rpx;
				}
			}
		}
	}

	.uni-swiper-item {
		height: auto;
	}

	.order-item {
		width: 100%;
		padding-left: 20rpx;
		background: #fff;
		margin-top: 20rpx;

		.i-top {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding-right: 20rpx;
			position: relative;
			width: 100%;
			margin-top: 30rpx;
			padding-bottom: 30rpx;

			.order_sn {
				font-size: 28rpx;
				color: #2d2d2d;
				margin-left: 10rpx;
			}

			.state {
				font-size: 26rpx;
				color: var(--color_integral_main);
			}
		}

		.goods-box {
			position: relative;
			padding: 20rpx 0;
			width: 100%;
			border-bottom: 1rpx solid #f2f2f2;

			.goods_return_status {
				position: absolute;
				bottom: 24rpx;
				right: 21rpx;
				font-size: 22rpx;
				color: #fc1c1c;
			}

			.left {
				.goods-img {
					background-size: cover;
					background-position: center center;
					background-repeat: no-repeat;
					width: 200rpx;
					height: 200rpx;
					overflow: hidden;
					background-color: #f8f6f7;
					border-radius: 14rpx;
					flex-shrink: 0;
				}
			}

			.right {
				padding: 7rpx 0 22rpx 20rpx;
				overflow: hidden;
				flex-shrink: 0;
				height: 200rpx;

				.right_top {
					.goods_name {
						width: 485rpx;
						color: #2d2d2d;
						font-size: 28rpx;
						overflow: hidden;
						text-overflow: ellipsis;
						display: -webkit-box;
						-webkit-line-clamp: 2;
						-webkit-box-orient: vertical;
						word-break: break-word;
						line-height: 38rpx;
					}

					.goods_info {
						padding-top: 10rpx;

						.spec_info {
							color: #949494;
							font-size: 24rpx;
							line-height: 28rpx;
							background-color: #f8f8f8;
							padding: 3rpx 7rpx;
							border-radius: 6rpx;
							margin-top: 10rpx;
						}

						.goods_num {
							color: #2d2d2d;
							font-size: 24rpx;
							text-align: right;
						}
					}
				}

				.goods_point_price {
					font-size: 30rpx;
					font-family: PingFang SC;
					font-weight: bold;
					color: var(--color_integral_price);
					line-height: 30rpx;
				}

				.title {
					font-size: 24rpx;
					color: $font-color-dark;
					line-height: 1;
				}

				.attr-box {
					font-size: 22rpx;
					color: $font-color-light;
					padding: 10rpx 12rpx;
				}
			}
		}

		.price-box {
			display: flex;
			justify-content: flex-end;
			align-items: baseline;
			padding: 0 18rpx;
			font-size: 24rpx;
			color: #949494;
			width: 100%;
			margin: 30rpx 0;

			.order_mount {
				color: #333333;
				font-size: 24rpx;
				margin-left: 20rpx;
			}

			.integral_des {
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: var(--color_integral_price);
				line-height: 30rpx;
			}
		}

		.action-box {
			display: flex;
			justify-content: flex-end;
			align-items: flex-start;
			max-height: 70rpx;
			position: relative;
			padding-right: 20rpx;
			width: 100%;
			padding-bottom: 20rpx;
		}

		.action-btn {
			width: 125rpx;
			height: 50rpx;
			margin: 0;
			margin-left: 10rpx;
			padding: 0;
			text-align: center;
			line-height: 50rpx;
			font-size: 24rpx;
			color: $main-font-color;
			background: #fff;
			border-radius: 25rpx;
			border: 1rpx solid #eeeeee;

			&:after {
				border: none;
			}

			&.recom {
				color: #fff;
				background: var(--color_integral_main);
				border: none;
			}
		}
	}

	.empty_part {
		padding-top: 108rpx;

		image {
			width: 380rpx;
			height: 280rpx;
		}

		text {
			color: $main-third-color;
			font-size: 26rpx;
			margin-top: 57rpx;
		}

		button {
			width: 245rpx;
			height: 66rpx;
			background: var(--color_integral_halo);
			border-radius: 33rpx;
			color: var(--color_integral_main);
			font-size: 30rpx;
			font-weight: bold;
			margin-top: 29rpx;
			border: none;
		}

		uni-button:after {
			border-radius: 200rpx;
			border-color: #fff;
		}
	}

	.cancel_popup {
		width: 100%;
		height: 700rpx;
		background: #ffffff;
		border-radius: 15rpx 15rpx 0 0;
		position: fixed;
		width: 100% !important;
		z-index: 20;
		bottom: 0;

		.popup_top {
			height: 100rpx;
			width: 100%;
			display: flex;
			padding: 0 39rpx;
			align-items: center;
			justify-content: space-between;
			border-bottom: 1rpx solid #f8f8f8;

			text {
				font-size: 32rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #343434;
				line-height: 32rpx;
			}

			image {
				width: 30rpx;
				height: 30rpx;
			}
		}

		.cancel_list {
			padding-bottom: 128rpx;
			box-sizing: border-box;
			height: 600rpx;
			z-index: 150;

			.cancle_pre {
				width: 100%;
				padding: 29rpx 40rpx;
				box-sizing: border-box;
				display: flex;
				justify-content: space-between;

				text {
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #666666;
					line-height: 32rpx;
				}
			}
		}

		.cancel_popup_btn {
			position: fixed;
			bottom: 40rpx;
			z-index: 30;
			display: flex;
			width: 100%;
			justify-content: center;

			text:nth-child(1) {
				width: 334rpx;
				height: 70rpx;
				background: var(--color_integral_vice);
				border-radius: 35rpx 0 0 35rpx;
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #ffffff;
				line-height: 32rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			text:nth-child(2) {
				width: 334rpx;
				height: 70rpx;
				background: var(--color_integral_main);
				border-radius: 0 35rpx 35rpx 0;
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #ffffff;
				line-height: 32rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}
</style>