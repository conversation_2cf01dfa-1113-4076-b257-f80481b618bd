<template>
  <view :style="mix_diyStyle">
    <view class="fixed_top_status_bar"></view>
    <home-deco
      ref="home_deco"
      :deco_info="deco_data"
      :activity_open="activity_open"
      :width="width"
      :height="height"
      :home_page_img="home_page_img"
      :is_show_top="true"
      :home_is_show_top_cat="home_is_show_top_cat"
      :is_from_found="false"
    ></home-deco>
  </view>
</template>

<script>
import HomeDeco from '../components/home_deco.vue'
export default {
  data() {
    return {
      deco_data: [], //首页装修数据
      home_is_show_top_cat: true, //是否显示顶部分类，默认显示
      home_page_img: [],
      width: '',
      height: '',
      shareData: {},
      activity_open: false
    }
  },
  components: {
    HomeDeco
  },
  onLoad() {
    // uni.setNavigationBarTitle({
    //   title: this.$L('消息中2心')
    // });
    this.ifOpen()
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    const { shareData } = this
    return shareData
  },
  onReachBottom() {
    this.$refs.home_deco.getMoreData()
  },
  methods: {
    // 判断活动是否开启
    ifOpen() {
      let param = {}
      param.data = {}
      param.data.names = 'integral_mall_is_enable'
      param.url = 'v3/system/front/setting/getSettings'
      param.method = 'GET'
      this.$request(param).then((res) => {
        if (res.state == 200) {
          if (res.data[0] == '1') {
            this.activity_open = true
            this.loadData()
          } else {
            this.activity_open = false
          }
        }
      })
    },
    async loadData() {
      uni.showLoading({
        title: this.$L('加载中...')
      })
      // #ifdef H5
      this.client = 'h5'
      // #endif

	  //app-1-start












	  //app-1-end
	  //wx-1-start
      // #ifdef MP
      this.client = 'weixinXcx'
      // #endif
	  //wx-1-end
      let param = {}
      param.data = {}
      param.data.type = 'integral'
      param.url = 'v3/system/front/deco/index?os=' + this.client
      param.method = 'GET'
      this.$request(param).then((res) => {
        if (res.state == 200) {
          if (JSON.stringify(res.data) == '{}') {
            this.deco_data = null
            uni.hideLoading()
            return
          }
          if (res.data.data != '') {
            this.deco_data = JSON.parse(res.data.data)
          } else {
            this.deco_data = null
          }
		  //wx-2-start
          // #ifdef MP
          this.shareData = {
            title: res.data.siteName,
            path: '/pages/index/index',
            imageUrl: res.data.xcxImage
          }
          // #endif
		  //wx-2-end
          if (res.data.showTip != null) {
            this.home_page_img = JSON.parse(res.data.showTip)
            const { windowWidth, windowHeight } = uni.getSystemInfoSync()
            this.width = this.home_page_img[0].width || windowWidth * 0.75 * 1.8
            this.height =
              this.home_page_img[0].height || windowHeight * 0.56 * 1.8
          } else {
            this.home_page_img = []
          }

          if (
            this.deco_data &&
            this.deco_data.length != undefined &&
            this.deco_data.length > 0
          ) {
            this.home_is_show_top_cat =
              this.deco_data[0].type == 'top_cat_nav' ? true : false
          }
          uni.hideLoading()
        } else {
          // this.api.$msg(res.msg)
          uni.hideLoading()
        }
      })
    }
  }
}
</script>

<style lang="scss">
.fixed_top_status_bar {
  position: fixed;
  //app-2-start



  //app-2-end

  height: 0;

  top: 0;
  left: 0;
  right: 0;
  z-index: 99;
  background: #fff;
}
page {
  background: #f5f5f5;
}
</style>
