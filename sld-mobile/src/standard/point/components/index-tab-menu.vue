<template>
  <view class="content" :style="[{ background: backGround },{marginTop:statusBarFlag==1?statusBarHeight:'auto'}]">
    <view class="nav_wrap">
      <view
        class="nav_item"
        v-for="(item, index) in tabInfo"
        :key="index"
        @click="changeTab(index, item.labelId, item)"
      >
        <view
          :class="currIndex == index ? 'active_nav nav_text' : 'nav_text'"
          >{{ item.labelName }}</view
        >
        <image
          :src="icon"
          mode="aspectFit"
          class="nav_icon"
          v-if="currIndex == index"
        ></image>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'tabMenu',
  data() {
    return {
      icon: process.env.VUE_APP_IMG_URL + 'index/icon.png',
      sortImg: process.env.VUE_APP_IMG_URL + 'index/sort.png',
	  //wx-2-start
      // #ifdef MP
      statusBarHeight:uni.getSystemInfoSync().statusBarHeight+'px',
      statusBarFlag:1,
      // #endif
	  //wx-2-end
      // #ifndef MP
      statusBarFlag:-1,
      // #endif
      currIndex: 0
    }
  },
  props: ['backGround', 'tabInfo'],
  methods: {
    changeTab(index, labelId) {
      this.currIndex = index
      if (index > 0) {
        let param = {}
        param.data = {}
        param.data.labelId = labelId
        param.url = 'v3/integral/front/integral/mall/list'
        param.method = 'GET'
        this.$request(param).then((res) => {
          if (res.state == 200) {
            this.$emit('getChildList', res.data.list[index - 1], index)
          }
        })
      } else {
        this.$emit('getChildList', null, index)
      }
    },
    toSortPage() {
      this.$Router.replaceAll(`/pages/category/category`)
    }
  }
}
</script>

<style lang="scss">
.content {
  position: fixed;
  top: 0;
  /* left:0; */
  //app-1-start



  //app-1-end
  width: 750rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  /* #ifndef MP */
  padding-top: 110rpx;
  /* #endif */
  //wx-3-start
  /* #ifdef MP */
  padding-top: 50px;
  /* #endif */
  //wx-3-end
  background: var(--color_integral_main);
  z-index: 1000;
  /* #ifndef H5 */
  left: 0;
  /* #endif */
  height: 160rpx;
  overflow-y: hidden;

  .nav_wrap {
	/* wx-1-start */
    /* #ifdef MP-WEIXIN */
    height: auto;
    /* #endif */
	/* wx-1-end */
    /* #ifndef MP-WEIXIN */
    height: 84rpx;
    /* #endif */
    display: flex;
    overflow-x: scroll;
    padding-top: -9rpx;
    float: left;

    .nav_item {
      margin-right: 35rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-top: 12rpx;
      box-sizing: border-box;

      .nav_text {
        font-size: 32rpx;
        color: #fff;
        white-space: nowrap;
        line-height: 38rpx;
      }

      .nav_icon {
        width: 27rpx;
        height: 9rpx;
      }
    }
  }

  .gap_line {
    width: 13rpx;
    height: 30rpx;
    background: linear-gradient(
      -90deg,
      #ffffff 0%,
      rgba(255, 255, 255, 0) 100%
    );
    opacity: 0.8;
    float: left;
    margin-right: 6rpx;
    margin-left: 4rpx;
    align-self: end;
    margin-top: 17rpx;
  }

  .sort_wrap {
    font-size: 30rpx;
    color: #fff;
    display: flex;
    justify-content: flex-start;
    box-sizing: border-box;
    float: right;
    text-align: right;
    width: max-content;
    margin-top: -9rpx;

    image {
      width: 32rpx;
      height: 26rpx;
      margin: 0 7rpx 0 7rpx;
    }

    text {
      line-height: 30rpx;
    }
  }
}

.active_nav {
  font-weight: bold;
  margin-bottom: 6rpx;
}
</style>
