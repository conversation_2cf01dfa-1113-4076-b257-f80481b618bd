<template>
	<view :style="mix_diyStyle">

	<view class="sld_point_good" :style="mix_diyStyle">
		<view class="fixed_top_status_bar"></view>
      <view class="top_con_bg" :style="{'height':type=='more'?'154rpx':'90rpx'}"></view>
		<view class="top_con" :style="{'height':type=='more'?'154rpx':'90rpx'}">
			<view class="top_title">
				<image class="return" :src="imgUrl+'point/return.png'" @click="back" mode="aspectFit"></image>
				<view class="title">
					{{title}}
				</view>
				<image class="search" :src="imgUrl+'point/search.png'" @click="goSearch" mode="aspectFit"></image>
			</view>
			<view class="top_cate">
				<view :class="{cate_item:true,active:current_index==index}" v-for="(item,index) in label_list" @click="changeLabel(index,item.labelId)"
				 :key='index'>
					 {{item.labelName}}
				</view>
			</view>
		</view>
		<!-- 积分商城搜索列表 -->
		<!-- app-1-start -->



		<!-- app-1-end -->

		<view class="recommend_list" :style="{'marginTop':type=='more'?'170rpx':'110rpx'}">

			<recommendGoods class='recommend_pre' v-for="(recommendItem,recommendIndex) in recommendedList" :goods_info="recommendItem" :key="recommendIndex"></recommendGoods>
		</view>
		<loadingState v-if="loadingState == 'first_loading'||recommendedList.length > 0" :state='loadingState' />

		<!-- 分类空页面 -->
		<view class="empty_sort_page" v-if="is_show_empty">
			<image :src="imgUrl+'empty_goods.png'" mode="aspectFit" class="empty_img"></image>
			<view class="empty_text">{{$L('暂无数据')}}</view>
		</view>
	</view>

	</view>
</template>

<script>
	import {
		mapState,
	} from 'vuex';
	import recommendGoods from '../components/recommend_item_v.vue'
	import loadingState from "../components/loading-state.vue";
	export default {
		data() {
			return {
				imgUrl: process.env.VUE_APP_IMG_URL,
				loadingState: 'first_loading',
				current: 1,
				pageSize: 10,
				hasMore: false,
				is_show_empty: false,
				top_bg1: process.env.VUE_APP_IMG_URL + 'point/top_bg1.png',
				top_bg2: process.env.VUE_APP_IMG_URL + 'point/top_bg2.png',
				title: '',
				recommendedList: [],
				cate_index: 0,
				labelId: '',
				label_list: [],
				current_index: 0,
				type:''
			};
		},
		components: {
			recommendGoods,
			loadingState
		},
		computed: {
			...mapState(['hasLogin', 'userInfo', 'userCenterData'])
		},
		onLoad(options) {
			this.type=this.$Route.query.type
			if (this.$Route.query.labelId) {
				this.labelId = this.$Route.query.labelId
				this.label_id = this.$Route.query.labelId//用于搜索页
			}
			if (this.$Route.query.type == 'more') {
				this.title = this.$L('积分商城')
				this.getLabelList()
			} else {
				this.title = this.$Route.query.type
				this.getGoodList()
			}
		},
		onReachBottom() {
			if (this.hasMore) {
				this.getGoodList()
			}
		},
		methods: {
			goSearch(){
				this.$Router.push({path:'/standard/point/search/search',query:{labelId:(this.type=='more'?this.label_id:this.labelId)}})
			},
			back(){
				this.$Router.back(1)
			},
			changeLabel(index, labelId) {
				this.current_index = index
				this.labelId = labelId
				this.getGoodList()
			},
			getLabelList() {
				uni.showLoading()
				let param = {}
				param.data = {}
				param.data.labelId = this.labelId
				param.url = 'v3/integral/front/integral/mall/labelList'
				param.method = 'GET'
				this.$request(param).then(res => {
					if (res.state == 200) {
						this.label_list = res.data.list
						this.labelId=res.data.list[0].labelId
						this.getGoodList()
						uni.hideLoading()
					} else {
						uni.hideLoading()
						that.$api.msg(res.msg)
					}
				})
			},
			getGoodList() {
				uni.showLoading()
				let param = {}
				param.data = {}
				param.data.labelId = this.labelId
				param.url = 'v3/integral/front/integral/mall/goodsList'
				param.method = 'GET'
				this.$request(param).then(res => {
					if (res.state == 200) {
						this.recommendedList = res.data.list
						if (this.recommendedList.length == 0) {
							this.is_show_empty = true
						}else{
							this.is_show_empty = false
						}
						this.hasMore = this.$checkPaginationHasMore(res.data.pagination); //是否还有数据
						if (this.hasMore) {
							this.current++;
							this.loadingState = 'allow_loading_more';
						} else {
							this.loadingState = 'no_more_data';
						}
						uni.hideLoading()
					} else {
						uni.hideLoading()
						that.$api.msg(res.msg)
					}
				})
			}
		},
	}
</script>

<style lang="scss">
	.fixed_top_status_bar {
		//app-2-start



		//app-2-end

		height: 0;

		top: 0;
		left: 0;
		right: 0;
		z-index: 99;
		background: #fff;
	}
	page {
		background: #F5F5F5;
		width: 750rpx;
		margin: 0 auto;
	}

    .top_con_bg{
      position: fixed;
	  //app-3-start



	  //app-3-end

      top: 0;

      left: 0;
      right: 0;
      margin: 0 auto;
      width: 750rpx;
      background: #fff;
    }
	.sld_point_good {
		.top_con {
			position: fixed;
			//app-4-start



			//app-4-end

			top: 0;

			left: 0;
			right: 0;
			margin: 0 auto;
			width: 750rpx;
			// height: 154rpx;
			background-size: 100%;
			padding: 0 20rpx;
			box-sizing: border-box;
			
			padding-top: 20rpx;
			background: var(--color_integral_main_bg);
			overflow-y: hidden;
			.top_title {
				display: flex;
				align-items: center;

				.return {
					width: 17rpx;
					height: 29rpx;
				}

				.title {
					width: 663rpx;
					text-align: center;
					font-size: 36rpx;
					color: white;
				}

				.search {
					width: 30rpx;
					height: 30rpx;
				}
			}

			.top_cate {
				display: flex;
				align-items: flex-start;
				margin-top: 30rpx;
				overflow-x: scroll;
				width: 100%;
				height: 66rpx;
				.cate_item {
					color: white;
					font-size: 32rpx;
					margin-right: 60rpx;
					min-width: fit-content;

					&.active {
						font-weight: bold;
					}

					&:last-child {
						margin-right: 0rpx;
					}
				}
			}
		}

	}

	.mp_search_box {
		display: flex;
		align-items: center;
		background-color: white;
		padding: 11rpx 0;

		.search_con {
			width: 631rpx;
			height: 65rpx;
			background: #F5F5F5;
			border-radius: 33rpx;
			padding: 18rpx 22rpx;
			display: flex;
			align-items: center;
			margin-left: 20rpx;

			.ser_input {
				padding-left: 20rpx;
			}

			.search_img {
				width: 30rpx;
				height: 30rpx;
			}

			.search_input {
				font-size: 28rpx;
				color: #999999;
			}
		}

		.search_btn {
			margin-left: 20rpx;
			font-size: 30rpx;
			color: #333333;
		}
	}

	.recommend_list {
		margin-top: 20rpx;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		padding: 0 20rpx;
		.recommend_pre {
			// margin-right: 20rpx !important;

			// &:nth-child(2n) {
			// 	margin-right: 0 !important;
			// }
		}
	}

	// 空页面
	.empty_sort_page {
		width: 100%;
		// height: 100vh;
		background: #F5F5F5;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-top: 228rpx;

		.empty_img {
			width: 380rpx;
			height: 280rpx;
			margin-bottom: 32rpx;
		}

		.empty_text {
			font-size: 26rpx;
			color: #999;
		}
	}
</style>
