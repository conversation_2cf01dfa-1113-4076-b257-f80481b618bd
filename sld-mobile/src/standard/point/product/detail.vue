<template>
	<view :style="mix_diyStyle">
		<view :style="mix_diyStyle">
			<view class="container" :style="{ display: isLoading ? 'block' : 'none' }">
				<!-- #ifndef MP -->
				<block v-if="!isNavShow">
					<view class="go_back" @click="goBack()">
						<image :src="imgUrl + 'point/goods/go_back.png'" mode="aspectFit"></image>
					</view>
					<view class="go_more" @tap="tipsShow">
						<image :src="imgUrl + 'goods_detail/more_tips.png'" mode="aspectFit"></image>
						<block v-if="tips_show">
							<view class="triangle-up"> </view>
							<view class="tips">
								<button v-for="(item, index) in tips" :key="index" class="tips_pre" @tap="handleLink"
									:data-link="item.tips_link" :open-type="item.type" :data-type="item.type"
									plain="true">
									<image :src="item.tips_img"></image>
									<text>{{ item.tips_name }}</text>
								</button>
							</view>
						</block>
					</view>
				</block>
				<!-- #endif -->
				<!-- 透明遮罩层 -->
				<view class="transparent_mask" v-if="transparent_mask" @tap="hideMask"></view>
				<!-- app-1-start -->



				<!-- app-1-end -->
				<view class="nav_list" v-if="isNavShow" :class="{ nav_list_no_opcity: noOpcity }">
					<!-- #ifndef MP -->
					<view class="go_back_nav flex_row_center_center" @click="goBack()">
						<image :src="imgUrl + 'point/goods/back.png'" mode="aspectFit"></image>
					</view>
					<!-- #endif -->
					<view class="nav_list_pre" :class="{ nav_list_pre_active: currentNav == item.id }"
						v-for="(item, index) in navList" :key="index" @click="clickNav(item.id)">
						{{ item.text }}
					</view>
					<view class="more_tips" @tap="tipsShow">
						<image class="more" :src="imgUrl + 'goods_detail/more.png'"></image>
						<block v-if="tips_show">
							<view class="triangle-up"> </view>
							<view class="tips">
								<view v-for="(item, index) in tips" :key="index" class="tips_pre" @tap="handleLink"
									:data-link="item.tips_link" :open-type="item.type" :data-type="item.type"
									plain="true">
									<image :src="item.tips_img"></image>
									<text>{{ item.tips_name }}</text>
								</view>
							</view>
						</block>
					</view>
				</view>

				<view class="detail_con">
					<view class="carousel" id="nav1">
						<uni-swiper-dot :info="defaultProduct.goodsPics" field="content" :mode="mode"
							:current="goodsVideo ? current - 1 : current">
							<swiper class="swiper-box" @change="change" :current="current">
								<!-- #ifdef H5 -->
								<swiper-item v-if="goodsVideo">
									<image :src="defaultProduct.goodsPics[0]" class="slide-image"></image>
									<image :src="imgUrl + 'play.png'" class="play_btn" @click="toPlayPage"></image>
								</swiper-item>
								<!-- #endif -->
								<!-- #ifdef MP|| APP-PLUS  -->
								<swiper-item v-if="goodsVideo">
									<block v-if="!playVFlag">
										<image :src="defaultProduct.goodsPics[0]" class="slide-image"></image>
										<image :src="imgUrl + 'play.png'" class="play_btn" @click="playVideo('on')">
										</image>
									</block>
									<block v-else>
										<video controls :src="goodsVideo" class="video_btn"
											:poster="defaultProduct.goodsPics[0]" autoplay="true"
											@ended="playVideo('end')"></video>
									</block>
								</swiper-item>
								<!-- #endif -->
								<!-- 默认规格 图  start-->
								<block v-if="defaultProduct &&defaultProduct.goodsPics &&defaultProduct.goodsPics.length > 0">
									<swiper-item class="swiper-item" v-for="(item, index) in defaultProduct.goodsPics"
										:key="index">
										<view class="image-wrapper">
											<image :src="item" class="loaded" mode="aspectFit"></image>
										</view>
									</swiper-item>
								</block>
								<!-- 默认规格 图  end-->
							</swiper>
						</uni-swiper-dot>
					</view>

					<view class="introduce_section_point flex_column_between_start">
						<image :src="imgUrl+'diy_style_confirm_bg.png'" class="introduce_back_img"></image>
						<view class="point_top flex_row_start_end">
							<view class="point_num" v-if="defaultProduct.integralPrice">
								<text>{{ defaultProduct.integralPrice }}</text><text>{{ $L('积分') }}</text>
							</view>
							<view class="point_tips" v-if="defaultProduct.integralPrice && defaultProduct.cashPrice">+
							</view>
							<view class="point_price" v-if="defaultProduct.cashPrice">
								<text class="unit">￥</text>
								<text class="price_int">{{
                            $getPartNumber(defaultProduct.cashPrice, 'int')
                            }}</text>
								<text class="price_decimal">{{
                            $getPartNumber(defaultProduct.cashPrice, 'decimal')
                            }}</text>
							</view>
						</view>
						<view class="point_original_sales flex_row_between_center">
							<view class="point_original_price" v-if="defaultProduct.marketPrice">
								{{ $L('原价') }}：￥<text>{{
                        filters.toFix(defaultProduct.marketPrice)
                        }}</text>
							</view>
							<view class="point_sales">{{ $L('已兑换') }}{{ goodsData.sales }}</view>
						</view>
					</view>

					<!-- 有活动商品样式 start -->
					<view class="introduce_section_activity">
						<view class="activity_goods_des">
							<view class="activity_goods_name">
								{{ goodsData.goodsName }}
							</view>
							<view class="activity_share_collection">
								<view class="activity_goods_share" @click="goShare">
									<text class="iconfont iconfenxiang"></text>
									<text class="show_text">{{ $L('分享') }}</text>
								</view>
							</view>
						</view>
						<view class="activity_goods_brief" v-if="goodsData.goodsBrief">
							{{ goodsData.goodsBrief }}
						</view>
					</view>
					<!-- 有活动商品样式 end -->

					<!-- 选择规格 start -->
					<view class="spec_con" @click="showSpecModel">
						<view class="spec_left">
							<view class="spec_left_title">{{ $L('已选') }}</view>
							<view class="spec_left_content" v-if="defaultProduct.specValues">
								{{ defaultProduct.specValues }}
							</view>
							<view class="spec_left_content" v-else>{{ $L('默认') }}</view>
						</view>
						<image :src="imgUrl + 'goods_detail/right_down.png'" mode="aspectFit" class="spec_right">
						</image>
					</view>
					<!-- 选择规格 end -->

					<view class="detail-desc" id="nav2">
						<view class="detail-desc_title">
							<text>{{ $L('商品详情') }}</text>
							<!-- <image :src="imgUrl + 'goods_detail/spec_param_bg.png'" mode="aspectFit"></image> -->
							<view class="image"
								:style="'background-image:url(' +imgUrl +'goods_detail/spec_param_bg.png)'">
							</view>
						</view>
						<jyf-parser :pHidden="false" :isAll="true" :html="goodsData.goodsDetails"
							ref="description"></jyf-parser>
						<view class="bottom_block" />
					</view>

					<!-- 店铺推荐 推荐礼品 start -->
					<recommendList id="nav3" ref="recommendList"></recommendList>
					<!-- 店铺推荐 推荐礼品 end -->
				</view>

				<!-- 底部操作菜单 -->
				<view class="page_bottom" v-if="goodsData.state != 3">
					<view class="action_btn_group no_stock" @click="showSpecModel()">{{
                $L('商品已下架')
                }}</view>
				</view>
				<view class="page_bottom" v-else>
					<view class="action_btn_group no_stock" @click="showSpecModel()"
						v-if="defaultProduct.productStock == 0">{{ $L('库存不足') }}</view>
					<view class="action_btn_group" @click="showSpecModel('buy')" v-else>{{$L('立即兑换')}}</view>
				</view>

				<uni-popup ref="popup" type="dialog" @touchmove.stop.prevent="moveHandle">
					<uni-popup-dialog type="input" :title="dialogTitle" :content="dialogCon" :duration="2000"
						before-close="true" @close="dialogCancle" @confirm="dialogConfirm"></uni-popup-dialog>
				</uni-popup>


				<!-- 分享 -->
				<view class="share_container">
					<goodsShare ref="goodsShare" type="point"></goodsShare>
				</view>

				<!-- 规格弹框 start -->
				<uni-popup class="spec_model" ref="specModel" type="bottom" @touchmove.stop.prevent="moveHandle">
					<view class="spec_model_con" @touchmove.stop.prevent="moveHandle">
						<view class="spec_model_content">
							<view class="spec_model_top">
								<view class="spec_model_goods">
									<view class="spec_goods_image" v-if="defaultProduct &&defaultProduct.goodsPics &&defaultProduct.goodsPics[0]">
										<!-- <image :src="defaultProduct.goodsPics[0]" mode="aspectFit"></image> -->
										<view class="image" :style="{backgroundImage: 'url(' + defaultProduct.goodsPics[0] + ')'}">
										</view>
									</view>
									<view class="spec_goods_right">
										<view class="spec_goods_price_con">
											<view class="spec_prices">
												<view class="spec_goods_point" v-if="defaultProduct.integralPrice">
													{{ defaultProduct.integralPrice }}{{ $L('积分') }}
												</view>
												<view class="flex_row_start_end">
													<view class="spec_goods_price" v-if="defaultProduct.cashPrice">
														<text>￥</text>
														<text>{{
                                                    $getPartNumber(defaultProduct.cashPrice, 'int')
                                                    }}</text>
														<text>{{
                                                    $getPartNumber(defaultProduct.cashPrice, 'decimal')
                                                    }}</text>
													</view>
													<view class="spec_goods_cash" v-if="defaultProduct.marketPrice">
														￥{{ filters.toFix(defaultProduct.marketPrice) }}
													</view>
												</view>
											</view>
										</view>
										<!-- 普通商品 start -->
										<view class="spec_goods_des">
											{{ $L('已选规格') }}：{{
                                    defaultProduct.specValues
                                    ? defaultProduct.specValues
                                    : '默认'
                                    }}
										</view>
										<!-- 普通商品 end -->
									</view>
								</view>
								<image :src="imgUrl + 'goods_detail/close.png'" mode="aspectFit" class="close_spec"
									@click="closeSpecModel"></image>
							</view>
							<scroll-view scroll-y="true" class="spec_content">
								<view class="spec_list" v-if="specs && specs.length > 0">
									<view class="spec_list_pre" v-for="(item, index) in specs" :key="index">
										<view class="spec_list_pre_name">{{ item.specName }}</view>
										<block v-if="item && item.specValueList && item.specValueList.length > 0" v-for="(item1, index1) in item.specValueList" :key="index1">
											<!-- checkState : 1-选中，2-可选，3-禁用 -->
											<view class="spec_list_pre_desc" :class="{spec_list_pre_desc_disabled: item1.checkState == '3'}" v-if="item1.checkState == '3'">
												<view class="spec_list_pre_con">
													<image :src="item1.image" mode="aspectFit" v-if="item1.image">
													</image>
													<text>{{ item1.specValue }}</text>
												</view>
											</view>
											<view class="spec_list_pre_desc" :class="{spec_list_pre_desc_active: item1.checkState == '1'}" @click="selectSpecVal(item.specId, item1.specValueId)" v-else>
												<view class="spec_list_pre_con">
													<image :src="item1.image" mode="aspectFit" v-if="item1.image">
													</image>
													<text>{{ item1.specValue }}</text>
												</view>
											</view>
										</block>
									</view>
								</view>
								<view class="spec_num">
									<view class="spec_num_left">
										{{ $L('购买数量') }}
									</view>
									<view class="spec_num_right">
										<text @click="editNum('reduce')"
											:class="{ no_edit: currentSpecNum == 1 }">-</text>
										<input type="number" v-model="currentSpecNum" @blur="editNum('edit', $event)" />
										<text @click="editNum('add')"
											:class="{no_edit: currentSpecNum == defaultProduct.productStock}">+</text>
									</view>
								</view>
							</scroll-view>
						</view>
						<!-- 规格弹框的底部按钮 start -->
						<view class="spec_btn" v-if="goodsData.state != 3">
							<!-- 已下架 start -->
							<button class="no_stock_spec_btn">{{ $L('商品已下架') }}</button>
							<!-- 已下架 end -->
						</view>
						<view class="spec_btn" v-else>
							<!-- 库存不足 start -->
							<button class="no_stock_spec_btn" v-if="defaultProduct.productStock == 0">
								{{ $L('库存不足') }}
							</button>
							<!-- 库存不足 end -->
							<!--立即兑换 start -->
							<button class="spec_btn_only" @click="goConfirm" v-else>
								{{ $L('立即兑换') }}
							</button>
							<!--立即兑换 end -->
						</view>

						<!-- 规格弹框的底部按钮 end -->
					</view>
				</uni-popup>
				<!-- 规格弹框 end -->

				<purchasePop ref="purchasePop" :exList="exceptionProList" :exState="exState" :exStateTxt="exStateTxt"
					@goNext="goNext" type="point"></purchasePop>
					
				<!-- #ifdef MP-WEIXIN -->
				<w-compress ref='wCompress' />
				<!-- #endif -->	
			</view>

		</view>
	</view>
</template>
<script>
	import goodsShare from '@/components/goodsShare.vue'
	import purchasePop from '@/components/purchasePop.vue'
	import filters from '@/utils/filter.js'
	import uniPopup from '@/components/uni-popup/uni-popup.vue'
	import uniPopupMessage from '@/components/uni-popup/uni-popup-message.vue'
	import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue'
	import selectAddress from '@/components/yixuan-selectAddress/yixuan-selectAddress'
	import uniSwiperDot from '@/components/uni-swiper-dot/uni-swiper-dot.vue'
	import recommendList from '../components/recommend_list.vue'
	import {
		quillEscapeToHtml
	} from '@/utils/common.js'
	import {
		mapState
	} from 'vuex'
	import {
		weBtoa
	} from '@/utils/base.js'
	// #ifdef MP-WEIXIN
	import WCompress from '@/components/w-compress/w-compress.vue'
	// #endif
	export default {
		components: {
			uniPopup,
			uniPopupMessage,
			uniPopupDialog,
			selectAddress,
			uniSwiperDot,
			recommendList,
			purchasePop,
			goodsShare
		},
		data() {
			return {
				imgUrl: process.env.VUE_APP_IMG_URL,
				dialogTitle: this.$L('温馨提示!'),
				dialogCon: this.$L('您需要先登录哦～'),
				goodsData: {},
				goodsId: '', //商品id
				curFreight: '', //当前运费
				currentSelId: '', //默认选中第一个地址
				isNavShow: false, //是否显示导航
				navList: [{
						text: this.$L('商品'),
						id: 0
					},
					{
						text: this.$L('详情'),
						id: 1
					},
					{
						text: this.$L('推荐'),
						id: 2
					}
				],
				currentNav: 0, //当前点击的默认是第一项
				nav1ScrollTop: 0, //商品模块距离顶部的距离
				nav2ScrollTop: 0, //详情模块距离顶部的距离
				nav3ScrollTop: 0, //推荐模块距离顶部的距离
				specModel: false, //规格弹框
				currentSpecNum: 1, //当前规格弹框中的购买数量
				defaultProduct: {}, //默认货品信息
				specs: [], //商品规格列表
				productId: '', //货品id
				isWeiXinBrower: false, //是否微信浏览器
				source: 0, //来源，终端类型，1、pc 2、app；3、公众号或微信内部浏览器；4、小程序
				mode: 'nav', //轮播图的显示样式类型
				current: 0, //轮播图默认显示第一页
				showSpecModelType: '', //规格弹框的底部按钮的显示类型	默认：加入购物车及立即购买都有	add：加入购物车	buy：立即购买		nosocket库存不足	offshelf商品下架
				noOpcity: false, //顶部导航的背景是否有透明度，轮播图以上有，以下没有
				isLoading: false,
				transparent_mask: false, //透明遮罩蒙层
				tips_show: false, //分享链接弹框
				tips: [{
						tips_img: process.env.VUE_APP_IMG_URL + 'goods_detail/home.png',
						tips_name: this.$L('积分首页'),
						tips_link: '/standard/point/index/index',
						type: 'navigateTo'
					},
					{
						tips_img: process.env.VUE_APP_IMG_URL + 'goods_detail/go_cart.png',
						tips_name: this.$L('购物车'),
						tips_link: '/pages/cart/cart',
						type: 'switchTab'
					},
					{
						tips_img: process.env.VUE_APP_IMG_URL + 'goods_detail/preson.png',
						tips_name: this.$L('个人中心'),
						tips_link: '/pages/user/user',
						type: 'switchTab'
					}
				],
				onceLoad: true, //页面首次加载标识
				goodsVideo: '',
				showControls: true, //是否显示轮播角标
				playVFlag: false,
				goBuyDisableStatus: false,
				filters,

				//下单价格提示
				exceptionProList: [],
				exState: 200,
				exStateTxt: ''
			}
		},
		computed: {
			...mapState(['hasLogin', 'userInfo']),
		},
		onReachBottom() {
			this.$refs.recommendList.getMoreData()
		},
		onShow() {
			if (!this.onceLoad) {
				this.getGoodsDetail(this.productId)
			}
		},
		onLoad(options) {
			this.onceLoad = false
			uni.showLoading({
				title: '加载中！'
			})
			// #ifdef H5
			this.isWeiXinBrower = this.$isWeiXinBrower()
			// #endif
			//wx-1-start
			//#ifdef MP-WEIXIN
			this.source = 4
			//#endif
			//wx-1-end









			this.productId = this.$Route.query.productId
			this.goodsId = this.$Route.query.goodsId

			if (this.$Route.query.scene) {
				let url = decodeURIComponent(
					decodeURIComponent(this.$Route.query.scene)
				).split(',')
				this.productId = url[0]
				this.goodsId = url[1]
			}

			this.getGoodsDetail(this.productId)

			uni.getSystemInfo({
				success: (res) => {
					let clientHeight = res.windowHeight,
						clientWidth = res.windowWidth,
						rpxR = 750 / clientWidth
					let calc = clientHeight * rpxR
					this.winHeight = calc
				}
			})
			this.goTop() //一键回到页面顶部
		},
		
		onShareAppMessage: async function() {
			if(!this.imageUrl){
				this.imageUrl = await this.compressImage().catch(console.log)
			}
			return {
				title: `${this.goodsData.goodsName}`,
				path: '/standard/product/detail?productId=' + this.productId + '&goodsId=' + this.goodsId,
				imageUrl:this.imageUrl.tempFilePath
			};
		},
		
		onShareTimeline: async function() {
			if(!this.imageUrl){
				this.imageUrl = await this.compressImage().catch(console.log)
			}
			return {
				title: this.goodsData.goodsName,
				query: 'productId=' + this.productId + '&goodsId=' + this.goodsId,
				imageUrl: this.imageUrl.tempFilePath
			};
		},

		onHide() {
			this.$refs.goodsShare.closeShareModel()
		},

		//页面滚动事件
		onPageScroll(res) {
			//监听滚动事件，获取滚动条的当前位置
			this.tips_show = false
			if (res.scrollTop > 50) {
				this.isNavShow = true
				if (res.scrollTop > 350) {
					this.noOpcity = true
				} else {
					this.noOpcity = false
				}
				if (res.scrollTop < this.nav2ScrollTop) {
					this.currentNav = 0
				} else if (
					res.scrollTop >= this.nav2ScrollTop &&
					res.scrollTop < this.nav3ScrollTop
				) {
					this.currentNav = 1
				} else if (res.scrollTop >= this.nav3ScrollTop) {
					this.currentNav = 2
				}
			} else {
				this.isNavShow = false
			}
		},
		methods: {
			
			// #ifdef MP-WEIXIN
			compressImage(){
				return this.$refs.wCompress.start(this.defaultProduct.goodsPics[0], {
					pixels: 4000000, // 最大分辨率，默认二百万
					quality: 0.9, // 压缩质量，默认0.8
					base64: false, // 是否返回base64，默认false，非H5有效
					wxShareCropped:true
				})
			},
			// #endif
			
			//立即兑换
			goConfirm() {
				if (!this.hasLogin) {
					getApp().globalData.goLogin(this.$Route)
					return
				}
				this.$refs.specModel.close()

				this.$request({
					url: 'v3/integral/front/integral/orderOperate/check',
					method: 'POST',
					data: {
						number: this.currentSpecNum,
						productId: this.productId,
						cashPrice: this.defaultProduct.cashPrice,
						integralPrice: this.defaultProduct.integralPrice
					}
				}).then((res) => {
					if (res.state == 200) {
						this.$Router.push({
							path: '/standard/point/product/confirm_order',
							query: {
								number: this.currentSpecNum,
								productId: this.productId
							}
						})
					} else if (res.state == 267) {
						this.exceptionProList = res.data.productList
						this.exState = res.data.state
						this.exStateTxt = res.data.stateValue
						if (this.exState == 7) {
							this.$refs.purchasePop.open(0)
						} else if (this.exState == 5) {
							this.$refs.purchasePop.open(1)
						} else {
							this.$api.msg(res.data.stateValue)
						}
					} else {
						this.$api.msg(res.data.stateValue)
					}
				})
			},
			//获取页面元素模块距离顶部的距离
			getSelectorQuery() {
				let query = uni.createSelectorQuery().in(this)
				// 获取状态栏的高度
				let statusBarHeight = 0
				//app-2-start




				//app-2-end
				//获取对应模块到顶部的距离
				query
					.select('#nav1')
					.boundingClientRect((res) => {
						if (res) {
							this.nav1ScrollTop = res.top - (50 + statusBarHeight)
						}
					})
					.exec()
				query
					.select('#nav2')
					.boundingClientRect((res) => {
						if (res) {
							this.nav2ScrollTop = res.top - (50 + statusBarHeight)
						}
					})
					.exec()
				query
					.select('#nav3')
					.boundingClientRect((res) => {
						if (res) {
							this.nav3ScrollTop = res.top - (50 + statusBarHeight)
						}
					})
					.exec()
			},
			// 点击导航
			clickNav(navId) {
				this.currentNav = navId
				if (navId == 0) {
					this.isNavShow = true
					uni.pageScrollTo({
						scrollTop: 0,
						duration: 300
					})
					this.isNavShow = false
				} else if (navId == 1) {
					uni.pageScrollTo({
						scrollTop: this.nav2ScrollTop,
						duration: 300
					})
				} else if (navId == 2) {
					uni.pageScrollTo({
						scrollTop: this.nav3ScrollTop + 5,
						duration: 600
					})
				}
			},
			//轮播图切换
			change: function(e) {
				this.current = e.detail.current
				if (this.goodsVideo && e.detail.current == 0) {
					this.showControls = false
				} else {
					this.showControls = true
				}
			},

			// 点击播放按钮,或者视频播放结束隐藏视频组件
			playVideo(type) {
				if (type == 'on') {
					this.playVFlag = true
				} else if (type == 'end') {
					this.playVFlag = false
				}
			},

			goNext() {
				this.showState = 1
				this.$Router.push({
					path: '/standard/point/product/confirm_order',
					query: {
						number: this.currentSpecNum,
						productId: this.productId
					}
				})
			},

			//回到页面顶部
			goTop() {
				uni.pageScrollTo({
					scrollTop: 0,
					duration: 300
				})
				this.currentNav = 0
			},
			//获取商品详情信息
			getGoodsDetail(productId) {
				this.$request({
						url: 'v3/integral/front/integral/mall/details',
						data: {
							integralProductId: this.productId //积分货品Id
						}
					})
					.then((res) => {
						if (res.state == 200) {
							if (res.data.goodsDetails) {
								res.data.goodsDetails = quillEscapeToHtml(res.data.goodsDetails)
							}
							this.goodsData = res.data //详情信息
							this.defaultProduct = res.data.defaultProduct //默认货品信息
							if (!this.defaultProduct.productStock) {
								//如果默认库存为0，则此商品全规格库存都为0，则禁用button
								this.goBuyDisableStatus = true
							}
							this.productId = this.defaultProduct.integralProductId //货品id
							this.specs = res.data.specs //规格列表
							this.goodsId = res.data.integralGoodsId //商品的goodsId;
							;
							(this.goodsVideo = res.data.goodsVideo),
							(this.showControls = this.goodsVideo ? false : true)
							this.isLoading = true
							uni.hideLoading()
						} else {
							this.isLoading = true
							//错误提示
							this.$api.msg(res.msg)
						}
					})
					.then(() => {
						this.getSelectorQuery()
					})
			},


			/**选择规格值
			 * @param {Object} specId 父级规格值
			 * @param {Object} specValueId   点击的当前的规格值
			 */
			selectSpecVal(specId, specValueId) {
				let that = this
				let curParSpec = [] //当前点击的规格的父级id的当前项
				curParSpec = that.specs.filter((item) => item.specId == specId)
				let curSPec = [] //当前点击的规格的规格id的当前项
				curSPec = curParSpec[0].specValueList.filter(
					(item1) => item1.specValueId == specValueId
				)
				curSPec[0].checkState = 1
				//被选择的规格值的id
				let choiceSpecIds = []
				that.specs.forEach((item) => {
					if (item.specId != specId) {
						item.specValueList.forEach((item1) => {
							if (item1.checkState == '1') {
								// checkState: 1-选中，2-可选，3-禁用
								choiceSpecIds.push(item1.specValueId)
							}
						})
					} else {
						choiceSpecIds.push(specValueId)
					}
				})

				let param = {}
				param.url = 'v3/integral/front/integral/mall/productInfo'
				param.method = 'GET'
				param.data = {}
				param.data.integralGoodsId = that.goodsId
				param.data.specValueIds = choiceSpecIds.join(',')
				that
					.$request(param)
					.then((res) => {
						if (res.state == 200) {
							let result = res.data
							that.defaultProduct = result.defaultProduct //默认货品信息
							that.productId = result.defaultProduct.integralProductId //货品id
							that.specs = res.data.specs //规格列表
							that.goodsId = res.data.integralGoodsId //商品的goodsId;
							that.currentSpecNum = 1
						} else {
							that.$api.msg(res.msg)
						}
					})
					.catch((e) => {
						//异常处理
					})
			},

			moveHandle() {},
			//弹层取消事件
			dialogCancle() {
				this.$refs.popup.close()
			},
			//弹层确认事件
			dialogConfirm() {
				let url = this.$Route.path
				const query = this.$Route.query
				uni.setStorageSync('fromurl', {
					url,
					query
				})
				this.$Router.push('/pages/public/login')
				this.$refs.popup.close()
			},
			//去分享
			goShare() {
				this.$refs.goodsShare.injectInfo(this.goodsData)
			},


			//打开规格弹框
			showSpecModel(type) {
				if (this.goBuyDisableStatus && !type) {
					return
				}
				if (type == 'add') {
					this.showSpecModelType = 'add'
				} else if (type == 'buy') {
					this.showSpecModelType = 'buy'
				} else if (type == 'offshelf') {
					this.showSpecModelType = 'offshelf'
				} else if (type == 'nosocket') {
					this.showSpecModelType = 'nosocket'
				} else {
					this.showSpecModelType = ''
				}
				this.$refs.specModel.open()
			},
			//编辑数量
			editNum(type, e) {
				let that = this

				let reg = /\./g
				let reg0 = /0+\d/

				if (
					reg.test(this.currentSpecNum) ||
					reg0.test(this.currentSpecNum) ||
					this.currentSpecNum <= 0
				) {
					setTimeout(() => {
						that.currentSpecNum = 1
					}, 0)
				}

				if (type == 'add') {
					if (that.currentSpecNum >= that.defaultProduct.productStock) {
						if (that.defaultProduct.productStock <= 0) {
							//如果商品库存为0,禁止操作购买数量即可
							return
						}
						that.currentSpecNum = that.defaultProduct.productStock
					} else {
						if (that.currentSpecNum > 99999) {
							that.currentSpecNum = 99999
						} else {
							that.currentSpecNum++
						}
					}
				} else if (type == 'edit') {
					if (that.currentSpecNum > that.defaultProduct.productStock) {
						setTimeout(() => {
							that.currentSpecNum = that.defaultProduct.productStock
						}, 0)
					} else {
						that.currentSpecNum = e.detail.value
						if (that.currentSpecNum == 0) {
							setTimeout(() => {
								that.currentSpecNum = 1
							}, 0)
						} else {
							that.currentSpecNum = that.currentSpecNum.replace(/\D/g, '')
							if (that.currentSpecNum > 99999) {
								setTimeout(() => {
									that.currentSpecNum = 99999
								}, 0)
							} else {
								setTimeout(() => {
									that.currentSpecNum = that.currentSpecNum
								}, 0)
							}
						}
					}
				} else if (type == 'reduce') {
					if (that.currentSpecNum > 1) {
						that.currentSpecNum--
					} else {
						that.currentSpecNum = 1
					}
				}
			},
			//关闭规格弹框
			closeSpecModel() {
				this.$refs.specModel.close()
			},
			//去商品详情页面
			goGoodsDetail(defaultProductId, goodsId) {
				this.$Router.push({
					path: '/standard/product/detail',
					query: {
						productId: defaultProductId,
						goodsId
					}
				})
			},
			//分享链接的弹框展示
			tipsShow() {
				this.tips_show = !this.tips_show
				this.transparent_mask = !this.transparent_mask
			},
			//三点分享链接
			handleLink(e) {
				let link = e.currentTarget.dataset.link
				let type = e.currentTarget.dataset.type
				if (type != 'share') {
					if (type == 'navigateTo') {
						this.$Router.push(link)
					} else {
						this.$Router.pushTab(link)
					}
				}
				this.tips_show = false
			},
			//隐藏透明遮罩层
			hideMask() {
				this.transparent_mask = false
				this.tips_show = false
			},
			//返回上一页
			goBack() {
				// #ifdef H5
				const pages = getCurrentPages()
				//有返回的页面则直接返回，uni.navigateBack默认返回失败之后会自动刷新页面，无法继续返回
				if (pages.length > 1) {
					uni.navigateBack(1)
					return
				}
				//vue router 可以返回uni.navigateBack失败的页面，但是会重新加载
				let a = this.$router.go(-1)
				//router.go失败之后则重定向到积分首页
				if (a == undefined) {
					this.$Router.replace('/standard/point/index/index')
				}
				return
				// #endif
				this.$Router.back(1)
			},
			//h5跳转新页面播放视频
			toPlayPage() {
				this.$Router.push({
					path: '/standard/product/video',
					query: {
						video_url: this.goodsVideo,
						posterImage: this.defaultProduct.goodsPics[0]
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	page {
		background: $bg-color-split;
		/* padding-bottom: 100rpx; */
		width: 750rpx;
		margin: 0 auto;
	}

	.container {
		position: relative;
	}

	button::after {
		border: none;
	}

	.uni-swiper-dot,
	.swiper-box,
	.video_btn,
	.swiper_item,
	.slide-image {
		width: 100%;
		height: 750rpx;
	}

	.play_btn {
		width: 90rpx;
		height: 90rpx;
		position: absolute;
		left: 50%; //起始是在body中，横向距左50%的位置
		top: 50%; //起始是在body中，纵向距上50%的位置，这个点相当于body的中心点，div的左上角的定位
		transform: translate(-50%, -50%); //水平、垂直都居中,也可以写成下面的方式
	}

	.go_back {
		width: 50rpx;
		height: 50rpx;
		position: absolute;

		top: 28rpx;

		left: 25rpx;
		z-index: 99;

		image {
			width: 50rpx;
			height: 50rpx;
		}
	}

	.go_more {
		width: 50rpx;
		height: 50rpx;
		position: absolute;
		/* #ifdef H5 */
		top: 28rpx;
		/* #endif */
		//app-3-start



		//app-3-end
		right: 25rpx;
		z-index: 99;

		image {
			width: 50rpx;
			height: 50rpx;
		}

		.triangle-up {
			position: absolute;
			display: block;
			top: 40rpx;
			right: -15rpx;
			width: 0rpx;
			height: 0rpx;
			background: #ffffff;
			border: 1px solid #cccccc;

			&::before {
				box-sizing: content-box;
				width: 0;
				height: 0;
				position: absolute;
				top: -2px;
				right: 25rpx;
				padding: 0;
				border-bottom: 8px solid #ffffff;
				border-top: 8px solid transparent;
				border-left: 8px solid transparent;
				border-right: 8px solid transparent;
				display: block;
				content: '';
				z-index: 12;
			}

			&::after {
				box-sizing: content-box;
				width: 0px;
				height: 0px;
				position: absolute;
				top: -5px;
				right: 20rpx;
				padding: 0;
				border-bottom: 9px solid rgba(102, 102, 102, 0.1);
				border-top: 9px solid transparent;
				border-left: 9px solid transparent;
				border-right: 10px solid transparent;
				display: block;
				content: '';
				z-index: 10;
			}
		}

		.tips {
			position: absolute;
			z-index: 20;
			top: 70rpx;
			right: -10rpx;
			width: 226rpx;
			background: rgba(255, 255, 255, 1);
			box-shadow: 0px 0px 10rpx 0px rgba(102, 102, 102, 0.2);
			opacity: 0.94;
			border-radius: 15rpx;
			display: flex;
			flex-direction: column;

			.tips_pre {
				width: 100%;
				height: 88rpx;
				display: flex;
				align-items: center;
				padding-left: 30rpx;
				box-sizing: border-box;
			}

			button::after {
				border: none;
				border-bottom: 1rpx solid #f1f1f1;
			}

			button[plain] {
				border: none;
			}

			image {
				width: 32rpx;
				height: 32rpx;
				margin-right: 20rpx;
			}

			text {
				font-size: 26rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: rgba(51, 51, 51, 1);
				line-height: 32rpx;
			}
		}
	}

	.icon-you {
		font-size: $font-base;
		color: #888;
	}

	/* 透明遮罩层 */
	.transparent_mask {
		width: 100%;
		height: 100%;
		position: fixed;
		background-color: #ffffff;
		top: 0;
		left: 0;
		opacity: 0;
		z-index: 10;
	}

	.fixed_top_status_bar {
		position: fixed;
		//app-4-start



		//app-4-end

		height: 0;

		top: 0;
		left: 0;
		right: 0;
		z-index: 99;
		background: rgba(250, 250, 250, 0.9);
	}

	.fixed_top_status_bar_no_opcity {
		background: #ffffff;
	}

	.nav_list {
		display: flex;
		justify-content: space-between;
		position: fixed;
		width: 750rpx;
		height: 100rpx;
		background: rgba(250, 250, 250, 0.9);
		padding: 0 50rpx 0 20rpx;
		//wx-2-start
		/* #ifdef MP */
		padding-left: 150rpx;
		/* #endif */
		//wx-2-end
		align-items: center;
		z-index: 50;

		.go_back_nav {
			width: 50rpx;
			height: 50rpx;

			image {
				width: 20rpx;
				height: 32rpx;
			}
		}

		.nav_list_pre {
			font-size: 32rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #333333;
			line-height: 32rpx;
			padding-bottom: 5rpx;
		}

		.nav_list_pre_active {
			border-bottom: 5rpx solid var(--color_integral_main);
		}

		/* 三点更多分享 */
		.more_tips {
			position: relative;
			display: flex;
			align-items: center;

			.more {
				width: 50rpx;
				height: 50rpx;
			}

			.triangle-up {
				position: absolute;
				display: block;
				top: 40rpx;
				left: 25rpx;
				width: 0rpx;
				height: 0rpx;
				background: #ffffff;
				border: 1px solid #cccccc;

				&::before {
					box-sizing: content-box;
					width: 0;
					height: 0;
					position: absolute;
					top: -2px;
					right: 25rpx;
					padding: 0;
					border-bottom: 8px solid #ffffff;
					border-top: 8px solid transparent;
					border-left: 8px solid transparent;
					border-right: 8px solid transparent;
					display: block;
					content: '';
					z-index: 12;
				}

				&::after {
					box-sizing: content-box;
					width: 0px;
					height: 0px;
					position: absolute;
					top: -5px;
					right: 20rpx;
					padding: 0;
					border-bottom: 9px solid rgba(102, 102, 102, 0.1);
					border-top: 9px solid transparent;
					border-left: 9px solid transparent;
					border-right: 10px solid transparent;
					display: block;
					content: '';
					z-index: 10;
				}
			}

			.tips {
				position: absolute;
				z-index: 20;
				top: 70rpx;
				right: -30rpx;
				width: 226rpx;
				background: rgba(255, 255, 255, 1);
				box-shadow: 0px 0px 10rpx 0px rgba(102, 102, 102, 0.2);
				opacity: 0.94;
				border-radius: 15rpx;
				display: flex;
				flex-direction: column;

				.tips_pre {
					width: 100%;
					height: 88rpx;
					display: flex;
					align-items: center;
					padding-left: 40rpx;
					box-sizing: border-box;
				}

				button::after {
					border: none;
					border-bottom: 1rpx solid #f1f1f1;
				}

				button[plain] {
					border: none;
				}

				image {
					width: 32rpx;
					height: 32rpx;
					margin-right: 20rpx;
				}

				text {
					font-size: 26rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: rgba(51, 51, 51, 1);
					line-height: 32rpx;
				}
			}
		}
	}

	.nav_list_no_opcity {
		background: #ffffff;
	}

	.detail_con {
		padding-bottom: 100rpx;
	}

	.carousel {
		height: 750rpx;
		position: relative;

		/* #ifndef MP */
		margin-top: var(--status-bar-height);

		/* #endif */
		.swiper-box {
			width: 750rpx;
			height: 750rpx;
		}

		swiper {
			height: 100%;
		}

		.image-wrapper {
			width: 100%;
			height: 100%;
		}

		.swiper-item {
			display: flex;
			justify-content: center;
			align-content: center;
			height: 750rpx;
			overflow: hidden;

			image {
				max-width: 100%;
				max-height: 100%;
			}
		}
	}

	/* 积分商品商品信息 start */
	.introduce_section_point {
		width: 750rpx;
		height: 130rpx;
		padding: 15rpx 0 20rpx 24rpx;
		box-sizing: border-box;
		background: var(--color_integral_main);
		position: relative;

		.introduce_back_img {
			position: absolute;
			right: 0;
			top: 0;
			height: 130rpx;
			width: 306rpx;
		}

		.point_top {
			.point_num {
				text {
					font-size: 40rpx;
					font-family: PingFang SC;
					font-weight: 600;
					color: #ffffff;

					&:nth-child(2) {
						font-size: 30rpx;
					}
				}
			}

			.point_tips {
				font-size: 40rpx;
				font-family: PingFang SC;
				font-weight: 600;
				color: #ffffff;
				margin: 0 5rpx;
			}

			.point_price {
				text {
					font-size: 30rpx;
					font-family: PingFang SC;
					font-weight: 600;
					color: #ffffff;

					&:nth-child(2) {
						font-size: 40rpx;
					}
				}
			}
		}

		.point_original_sales {
			width: 700rpx;

			.point_original_price {
				font-size: 26rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #ffffff;
				opacity: 0.7;

				text {
					text-decoration: line-through;
				}
			}

			.point_sales {
				font-size: 26rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #ffffff;
			}
		}
	}

	/* 积分商品商品信息 end */

	/* 有活动的商品描述详情 start */
	.introduce_section_activity {
		background: #ffffff;
		padding: 28rpx 0 30rpx 0;
		margin-bottom: 20rpx;

		.activity_goods_des {
			display: flex;
			justify-content: space-between;
			padding: 0 30rpx 0 21rpx;

			.activity_goods_name {
				width: 533rpx;
				font-size: 32rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: #333333;
				line-height: 48rpx;
				text-overflow: -o-ellipsis-lastline;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				line-clamp: 2;
				-webkit-box-orient: vertical;
				word-break: break-all;
			}

			.activity_share_collection {
				display: flex;

				.activity_goods_collection {
					display: flex;
					flex-direction: column;
					align-items: center;
					width: 100rpx;

					.iconfont {
						font-size: 44rpx;
					}

					.iconaixin1 {
						color: #fb1c1c;
					}

					.show_text {
						font-size: 22rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #2d2d2d;
					}
				}

				.activity_goods_share {
					display: flex;
					flex-direction: column;
					align-items: center;
					margin-left: 15rpx;

					.iconfont {
						font-size: 44rpx;
					}

					.show_text {
						font-size: 22rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #2d2d2d;
					}
				}
			}
		}

		.activity_goods_brief {
			width: 681rpx;
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #555555;
			line-height: 36rpx;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			padding: 0 20rpx;
			box-sizing: border-box;
			margin-top: 8px;
		}
	}

	/* 有活动的商品描述详情 end */

	/* 标题简介 */
	.introduce_section {
		background: #fff;
		padding: 20rpx;
		margin-bottom: 20rpx;

		.price_part {
			.left {
				display: flex;
				flex-direction: column;

				.sell_price {
					color: $main-color;

					.unit {
						font-size: 26rpx;
						font-weight: bold;
					}

					.price_int {
						font-size: 50rpx;
						line-height: 50rpx;
						margin-left: 4rpx;
						font-weight: bold;
					}

					.price_decimal {
						font-size: 26rpx;
						font-weight: bold;
					}
				}

				.original_price {
					color: #949494;
					font-size: 22rpx;
					text-decoration: line-through;
				}
			}

			.right {
				.collection {
					display: flex;
					flex-direction: column;
					width: 72rpx;

					.iconaixin1 {
						color: #f80f0c;
					}

					.iconaixin {
						color: #2d2d2d;
					}

					text {
						font-size: 22rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #2d2d2d;
					}
				}

				view {
					.iconfont {
						font-size: 50rpx;
						color: #2d2d2d;

						&.active {
							color: $main-color;
						}
					}

					.show_text {
						color: #2d2d2d;
						font-size: 22rpx;

						&.active {
							color: $main-color;
						}
					}

					&:last-child {
						margin-left: 25rpx;
					}
				}
			}
		}

		.goods_name {
			font-size: 32rpx;
			font-weight: 600;
			color: #333;
			line-height: 45rpx;
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
			word-break: break-word;
			margin-top: 20rpx;
		}

		.goods_ad {
			color: #666666;
			font-size: 28rpx;
			line-height: 40rpx;
			margin-top: 20rpx;
		}


		.coupon-tip {
			align-items: center;
			padding: 4rpx 10rpx;
			background: $uni-color-primary;
			font-size: $font-sm;
			color: #fff;
			border-radius: 6rpx;
			line-height: 1;
			transform: translateY(-4rpx);
		}
	}

	/* 分享 */
	.share-section {
		display: flex;
		align-items: center;
		color: $font-color-base;
		background: linear-gradient(to right, #fdf5f6, #fbebf6);
		padding: 12rpx 30rpx;

		.share-icon {
			display: flex;
			align-items: center;
			width: 70rpx;
			height: 30rpx;
			line-height: 1;
			border: 1px solid $uni-color-primary;
			border-radius: 4rpx;
			position: relative;
			overflow: hidden;
			font-size: 22rpx;
			color: $uni-color-primary;

			&:after {
				content: '';
				width: 50rpx;
				height: 50rpx;
				border-radius: 50%;
				left: -20rpx;
				top: -12rpx;
				position: absolute;
				background: $uni-color-primary;
			}
		}

		.icon-xingxing {
			position: relative;
			z-index: 1;
			font-size: 24rpx;
			margin-left: 2rpx;
			margin-right: 10rpx;
			color: #fff;
			line-height: 1;
		}

		.tit {
			font-size: $font-base;
			margin-left: 10rpx;
		}

		.icon-bangzhu1 {
			padding: 10rpx;
			font-size: 30rpx;
			line-height: 1;
		}

		.share-btn {
			flex: 1;
			text-align: right;
			font-size: $font-sm;
			color: $uni-color-primary;
		}

		.icon-you {
			font-size: $font-sm;
			margin-left: 4rpx;
			color: $uni-color-primary;
		}
	}

	.spec_con {
		padding: 22rpx 8rpx 22rpx 20rpx;
		box-sizing: border-box;
		display: flex;
		justify-content: space-between;
		/* align-items: center;
		height: 90rpx; */
		background: #ffffff;

		.spec_left {
			display: flex;
			/* align-items: center; */

			.spec_left_title {
				font-size: 28rpx;
				color: #666666;
				line-height: 45rpx;
				margin-right: 35rpx;
			}

			.spec_left_content {
				width: 550rpx;
				/* white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden; */
				word-break: break-all;
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #343434;
				line-height: 45rpx;
				margin-right: 10rpx;
			}
		}

		.spec_right {
			width: 36rpx;
			height: 36rpx;
		}
	}

	.c-list {
		font-size: $font-sm;
		color: $font-color-base;
		background: #fff;

		.c-row {
			display: flex;
			align-items: center;
			padding: 20rpx 20rpx;
			position: relative;
		}

		.tit {
			color: #666;
			font-size: 26rpx;
			margin-right: 35rpx;
		}

		.con {
			flex: 1;
			color: #333;
			font-size: 28rpx;

			.selected-text {
				margin-right: 10rpx;
			}
		}

		.bz-list {
			height: 40rpx;
			font-size: $font-sm;
			color: $font-color-dark;

			text {
				display: inline-block;
				margin-right: 30rpx;
			}
		}

		.con-list {
			flex: 1;
			display: flex;
			flex-direction: column;
			color: $font-color-dark;
			line-height: 40rpx;
		}

		.red {
			color: $uni-color-primary;
		}
	}

	/* 积分 start */
	.integral {
		background: #ffffff;

		.integral_content {
			border-top: 1rpx solid #f2f2f2;
			display: flex;
			align-items: center;
			height: 100rpx;
			padding: 0 20rpx;

			.integral_title {
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #666666;
			}

			.integral_con {
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #333333;
				margin-left: 35rpx;
			}
		}
	}

	/* 积分 end */

	/*  详情 */
	.detail-desc {
		background: #fff;
		margin-top: 20rpx;
		overflow-x: hidden;
		padding: 30rpx 20rpx;
		box-sizing: border-box;

		.detail-desc_title {
			margin-bottom: 30rpx;
			display: flex;
			flex-direction: column;

			text {
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: #2d2d2d;
				line-height: 36rpx;
			}

			.image {
				background-position: center center;
				background-repeat: no-repeat;
				background-size: contain;
				width: 675rpx;
				height: 22rpx;
			}
		}
	}

	/*  弹出层 */
	.popup {
		position: fixed;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
		z-index: 99;

		&.show {
			display: block;

			.mask {
				animation: showPopup 0.2s linear both;
			}

			.layer {
				animation: showLayer 0.2s linear both;
			}
		}

		&.hide {
			.mask {
				animation: hidePopup 0.2s linear both;
			}

			.layer {
				animation: hideLayer 0.2s linear both;
			}
		}

		&.none {
			display: none;
		}

		.mask {
			position: fixed;
			top: 0;
			width: 100%;
			height: 100%;
			z-index: 1;
			background-color: rgba(0, 0, 0, 0.4);
		}

		.layer {
			position: fixed;
			z-index: 99;
			bottom: 0;
			width: 100%;
			min-height: 40vh;
			border-radius: 10rpx 10rpx 0 0;
			background-color: #fff;

			.btn {
				height: 66rpx;
				line-height: 66rpx;
				border-radius: 100rpx;
				background: $uni-color-primary;
				font-size: $font-base;
				color: #fff;
				margin: 30rpx auto 20rpx;
			}
		}

		@keyframes showPopup {
			0% {
				opacity: 0;
			}

			100% {
				opacity: 1;
			}
		}

		@keyframes hidePopup {
			0% {
				opacity: 1;
			}

			100% {
				opacity: 0;
			}
		}

		@keyframes showLayer {
			0% {
				transform: translateY(120%);
			}

			100% {
				transform: translateY(0%);
			}
		}

		@keyframes hideLayer {
			0% {
				transform: translateY(0);
			}

			100% {
				transform: translateY(120%);
			}
		}
	}

	.bottom_block {
		width: 750rpx;
		height: calc(100rpx + constant(safe-area-inset-bottom));
		/* 兼容 iOS < 11.2 */
		height: calc(100rpx + env(safe-area-inset-bottom));
		/* 兼容 iOS >= 11.2 */
	}

	/* 底部操作菜单 */
	.page_bottom {
		position: fixed;
		left: 0rpx;
		right: 0rpx;
		margin: 0 auto;
		bottom: 0rpx;
		z-index: 95;
		padding-bottom: env(safe-area-inset-bottom);
		display: flex;
		justify-content: center;
		align-items: center;
		width: 750rpx;
		height: calc(98rpx + env(safe-area-inset-bottom));
		background: #fff;
		box-shadow: 0px 0px 20px 0px rgba(86, 86, 86, 0.2);

		.p_b_btn {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			font-size: $font-sm;
			color: $font-color-base;
			width: 96rpx;
			height: 80rpx;
			position: relative;

			image {
				width: 40rpx;
				height: 40rpx;
				margin-bottom: 10rpx;
			}

			.show_text {
				color: #2d2d2d;
				font-size: 20rpx;
			}

			.cart_num {
				width: 30rpx;
				height: 30rpx;
				position: absolute;
				right: 10rpx;
				top: -10rpx;
				background: #ff0000;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				color: #ffffff;
			}
		}

		.action_btn_group {
			width: 690rpx;
			height: 70rpx;
			background: var(--color_integral_main);
			border-radius: 35rpx;
			font-size: 30rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #ffffff;
			line-height: 70rpx;
			text-align: center;

			&.no_stock {
				background: #bbbbbb;
				color: #ffffff;
			}
		}
	}



	button {
		padding: 0;
		margin: 0;
	}


	.spec_model_con {
		width: 750rpx;
		height: 900rpx;
		background: #ffffff;
		border-radius: 10rpx 10rpx 0;
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		margin: 0 auto;
		z-index: 150;

		.spec_model_content {
			padding-bottom: 115rpx;

			.spec_model_top {
				display: flex;
				justify-content: space-between;
				padding: 30rpx 22rpx 0 30rpx;
				box-sizing: border-box;

				.spec_model_goods {
					display: flex;
					height: 151rpx;
					/* align-items: center; */

					.spec_goods_image {
						width: 151rpx;
						height: 151rpx;
						background: #eeeeee;
						border-radius: 15rpx;

						.image {
							width: 151rpx;
							height: 151rpx;
							border-radius: 15rpx;
							background-position: center center;
							background-size: cover;
							background-repeat: no-repeat;
						}
					}

					.spec_goods_right {
						width: 450rpx;
						margin-left: 30rpx;
						display: flex;
						flex-direction: column;
						justify-content: space-between;

						.spec_goods_price_con {
							display: flex;
							align-items: center;

							.spec_prices {
								.spec_goods_point {
									font-size: 30rpx;
									font-family: PingFang SC;
									font-weight: 600;
									color: var(--color_integral_main);
								}

								.spec_goods_tips {
									font-size: 28rpx;
									font-family: PingFang SC;
									font-weight: 600;
									color: var(--color_integral_main);
									margin-right: 5rpx;
								}

								.spec_goods_price {
									display: inline-block;

									text {
										font-size: 24rpx;
										font-family: PingFang SC;
										font-weight: 600;
										color: var(--color_integral_main);
									}

									text:nth-child(2) {
										font-size: 30rpx;
									}
								}

								.spec_goods_cash {
									font-size: 24rpx;
									font-family: PingFang SC;
									font-weight: 500;
									text-decoration: line-through;
									color: #999999;
									margin-left: 10rpx;
								}
							}

							.sec_kill_tips {
								width: 130rpx;
								height: 40rpx;
								background: linear-gradient(90deg,
										#ffaa06 0%,
										#ff8323 0%,
										#fc5300 0%,
										#ff1353 100%);
								border-radius: 20rpx;
								font-size: 24rpx;
								font-family: PingFang SC;
								font-weight: 500;
								color: #ffffff;
								text-align: center;
								line-height: 40rpx;
								margin-left: 20px;
							}
						}

						.spec_goods_des {
							font-size: 28rpx;
							font-family: PingFang SC;
							font-weight: 500;
							color: #343434;
							margin-bottom: 10rpx;
							width: 520rpx;
							/* white-space: nowrap;
							text-overflow: ellipsis;
							overflow: hidden; */
							word-break: break-all;
						}
					}
				}

				.close_spec {
					position: absolute;
					top: 14rpx;
					right: 14rpx;
					z-index: 9;
					width: 46rpx;
					height: 46rpx;
				}
			}

			.spec_content {
				height: 620rpx;

				.spec_list {
					margin: 0 30rpx;
					padding-top: 34rpx;

					.spec_list_pre {
						border-bottom: 1rpx solid #f5f5f5;

						.spec_list_pre_name {
							font-size: 28rpx;
							font-family: PingFang SC;
							font-weight: 500;
							color: #666666;
							margin-bottom: 30rpx;
						}

						.spec_list_pre_desc {
							display: inline-table;
							padding: 13rpx 25rpx;
							box-sizing: border-box;
							box-sizing: border-box;
							background: #f5f5f5;
							border-radius: 50rpx;
							margin-bottom: 30rpx;
							margin-right: 30rpx;
							border: 1rpx solid #f5f5f5;

							.spec_list_pre_con {
								display: flex;
								align-items: center;

								text {
									font-size: 26rpx;
									font-family: PingFang SC;
									font-weight: 500;
									color: #343434;
									text-align: left;
								}

								image {
									width: 36rpx;
									height: 36rpx;
									margin-right: 20rpx;
								}
							}
						}

						.spec_list_pre_desc_active {
							background: #ffffff;
							border: 1rpx solid var(--color_integral_main);

							.spec_list_pre_con {
								text {
									color: var(--color_integral_main);
								}
							}
						}

						.spec_list_pre_desc_disabled {
							background: #f5f5f5;
							opacity: 0.2;

							.spec_list_pre_con {
								text {
									color: #2d2d2d;
								}
							}
						}
					}
				}

				.spec_num {
					height: 50rpx;
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding: 0 20rpx 0 30rpx;
					box-sizing: border-box;
					margin-top: 16rpx;

					.spec_num_left {
						font-size: 28rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #666666;

						text {
							color: #949494;
						}
					}

					.spec_num_right {
						width: 220rpx;
						height: 60rpx;
						border: 1rpx solid #E5E5E5;
						display: flex;
						justify-content: center;
						align-items: center;
						font-size: 24rpx;
						font-family: PingFang SC;
						font-weight: bold;
						color: #A6A6A6;
						line-height: 30rpx;
						border-radius: 30rpx;
						overflow: hidden;

						text {
							width: 60rpx;
							height: 60rpx;
							text-align: center;
							line-height: 60rpx;
							border-left: 1px solid #E5E5E5;
							font-size: 34rpx;
							color: #333;

							&.no_edit {
								background: #EDEDED;
								opacity: 0.5;
								color: #949494;
							}
						}

						text:nth-child(1) {
							color: #949494;
							border-right: 1rpx solid #E5E5E5;
							border-left: none;
						}

						input {
							width: 88rpx;
							height: 50rpx;
							line-height: 50rpx;
							text-align: center;
							font-size: 24rpx;




							font-size: 28rpx;
							color: #2D2D2D;
						}
					}
				}
			}
		}

		.spec_btn {
			width: 750rpx;
			height: calc(98rpx + env(safe-area-inset-bottom));
			padding-bottom: env(safe-area-inset-bottom);
			background: #ffffff;
			box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(86, 86, 86, 0.08);
			display: flex;
			align-items: center;
			justify-content: center;
			position: fixed;
			bottom: 0;

			.spec_btn_only {
				width: 690rpx;
				height: 70rpx;
				border-radius: 35rpx;
				font-size: 30rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #ffffff;
				text-align: center;
				line-height: 70rpx;
				background: var(--color_integral_main);
			}

			.no_stock_spec_btn {
				width: 690rpx;
				height: 70rpx;
				border-radius: 35rpx;
				font-size: 30rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #ffffff;
				text-align: center;
				line-height: 70rpx;
				background: #bbbbbb;
			}
		}
	}
</style>