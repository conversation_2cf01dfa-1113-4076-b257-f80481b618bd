<!-- 支付页面 -->
<template>
	<view class="container" v-if="reqFlag == 2" :style="mix_diyStyle">
		<view class="order_info flex_column_start_start">
			<view class="item b_b flex_row_between_center">
				<text class="tit">{{$L('支付单号')}}</text>
				<text class="order_sn">{{payInfo.paySn}}</text>
			</view>
			<view class="item flex_row_between_center">
				<text class="tit">{{$L('订单金额')}}</text>
				<view class="order_amount">
					<text class="unit">￥</text>
					<text class="price_int">{{$getPartNumber(payInfo.needPay,'int')}}</text>
					<text class="price_decimal">{{payInfo.needPay?$getPartNumber(payInfo.needPay,'decimal'):''}}</text>
				</view>
			</view>
		</view>
		<view class="pay_part flex_column_start_start">
			<text class="title">{{$L('选择支付方式')}}</text>
			<block v-if="payMethod.length">
				<view v-for="(item,index) in payMethod" :key='index' @click="selectPayMethod(item)"
					:class="{item:true, b_b:index<payMethod.length-1, flex_row_between_center:true}">
					<view class="left flex_row_start_center">
						<svgGroup type="balanceIcon" v-if="item.payType=='BALANCE'" width="80" height="80" px="rpx"
							:color="balanceActive?'#FF9500':'#999'"></svgGroup>
						<image class="pay_icon" :src="item.payIcon" v-else />
						<text class="tit">{{item.payMethodName}}</text>
					</view>
					<view class="right">
						<text class="balance_available" v-if="item.payMethod == 'balance'"
							:class="{active:filters.toNum(payInfo.balanceAvailable) >= filters.toNum(payInfo.needPay)}">{{'余额：￥'+filters.toFix(payInfo.balanceAvailable)}}</text>
						<text
							:class="{iconfont:true, iconziyuan33:selData.payMethod == item.payMethod,iconziyuan43:selData.payMethod != item.payMethod,has_sel:selData.payMethod == item.payMethod}"></text>
					</view>
				</view>
			</block>
			<block v-else>
				<view class="no_payMth">
					<image :src="imgUrl+'store/no_content.png'"></image>
					<text>暂无支付方式</text>
				</view>
			</block>
		</view>
		<!-- 余额支付支付密码 start -->
		
			<view class="balance_password" v-if="balanceCon">
				<view class="balance_password_title">{{ $L('支付密码') }}</view>
				<view class="input_password">
					<view class="input_password_con">
						<!-- #ifndef MP -->
						<input type="password" v-model="passwordVal" :placeholder="$L('请输入支付密码')" @blur="passwordBlur"
							v-if="!isShowPasw" />
						<!-- #endif -->
						<!-- wx-2-start -->
						<!-- #ifdef MP -->
						<input type="password" password="true" v-model="passwordVal" :placeholder="$L('请输入支付密码')"
							@blur="passwordBlur" v-if="!isShowPasw" />
						<!-- #endif -->
						<!-- wx-2-end -->
						<input type="text" v-model="passwordVal" :placeholder="$L('请输入支付密码')" @blur="passwordBlur"
							v-else />
						<view :class="{
						  iconfont: true,
						  iconziyuan81: isShowPasw,
						  iconziyuan9: !isShowPasw
					}" @click.stop="showPasword">
						</view>
					</view>
				</view>
			</view>
		
		<!-- 余额支付支付密码 end -->
		<view v-if="payMethod.length" class="btn_recharge flex_row_center_center"
			:style="{ top: windowHeight - 60 + 'px' }" @click="clickPay">{{ $L('立即支付') }}
		</view>

		
	</view>
</template>
<script>
	import {
mapState
} from 'vuex';
import filters from "../../utils/filter.js";
	// #ifdef H5
	import { getWxH5Appid } from '@/static/h5/wxH5Auth.js';
	// #endif
	export default {
		data() {
			return {
				paySn: '', //支付单号
				payInfo: {}, //订单信息
				selData: {},
				payMethod: [], //支付方式
				client: 'wxbrowser', //支付发起来源 pc==pc,mbrowser==移动设备浏览器,app==app,wxxcx==微信小程序,wxbrowser==微信内部浏览器
				payMethodType: 'create', //从哪里进入支付，create 下单，orderList 订单列表 orderDetail 订单详情 recharge 充值
				isAllowAutoPay: true, //当浏览器地址有code时，是否允许自动支付，如果支付失败的话置为false
				autoPayInterval: '', //定时器
				wxBrowerCode: '', //微信浏览器支付的code
				balanceCon: false, //输入余额支付密码的框是否显示
				passwordVal: '', //密码值
				isShowPasw: false, //支付密码是否可见
				hasSetPassword: false, //用户是否已经设置了余额支付支付密码
				oriUrl: '', //不带code的页面地址
				windowHeight: '',
				ifOnShow: false,
				imgUrl: process.env.VUE_APP_IMG_URL,
				reqFlag: 0,
				filters,
				paypal_client_id: '', //paypal配置信息
				paypal_is_sandBox: '',
				payType: 1, //购买类型，1-月卡；2-季卡；3-年卡
				ableClick: true,
			}
		},
		computed: {
			...mapState([
				'hasLogin',
				'userInfo',
				'userCenterData',
				]),
			balanceActive() {
				return this.filters.toNum(this.payInfo.balanceAvailable) >= this.filters.toNum(this.payInfo.needPay)
			}
		},
		onHide() {
			this.ifOnShow = true
		},
		onShow() {
			if (this.ifOnShow) {
				this.isSetPassword()
			}
			//#ifdef H5
			if (uni.getSystemInfoSync().platform == 'ios') {
				let wxBrowerBack = uni.getStorageSync('wxBrowerBack')
				if (wxBrowerBack) {
					setTimeout(() => {
						uni.redirectTo({
							url: '/standard/super/tradeSuccess?orderSn=' +
								this.payInfo.paySn +
								'&sourceType=tradeSuccess'
						})
					}, 1000)
				}
			}
			//#endif
		},
		onLoad(option) {
			setTimeout(() => {
				uni.setNavigationBarTitle({
					title: this.$L('收银台')
				})
			}, 0);
			this.initClient();
			this.payType = this.$Route.query.payType;
			this.getPayInfo();

			//#ifdef H5
			//判断code地址的参数 start
			let cur_code = this.$getQueryVariable('code')
			if (cur_code) {
				uni.showLoading({
					title: this.$L('支付中...'),
					mask: true
				})
				this.ableClick = false
				let oriUrl = process.env.VUE_APP_API_URL + 'standard/super/superPay'
				let tmp_data = ''
				for (let i in this.$Route.query) {
					if (i != 'ori_url' && i != 'code') {
						if (i == 'payMethodType' && Array.isArray(this.$Route.query[i])) {
							tmp_data += `${i}=${this.$Route.query[i][0]}&`
						} else {
							tmp_data += `${i}=${this.$Route.query[i]}&`
						}
					}
				}
				oriUrl += '?' + tmp_data
				this.oriUrl = oriUrl
				if (this.client == 'wxbrowser') {
					//微信浏览器的话要把浏览器地址里面的code去掉
					history.replaceState({}, '', this.oriUrl)
				}
				this.wxBrowerCode = cur_code
			}
			//判断code地址的参数 end
			//#endif

			this.payMethodType = this.$Route.query.payMethodType != undefined ?
				this.$Route.query.payMethodType : this.payMethodType;
			this.getPayMethod();
			this.isSetPassword();
			uni.getSystemInfo({
				success: (res) => {
					this.windowHeight = res.windowHeight;
				}
			});

			//app-1-start



			//app-1-end

		},
		onUnload() {
			if (this.autoPayInterval) {
				clearInterval(this.autoPayInterval)
			}
			//#ifdef H5
			if (uni.getSystemInfoSync().platform == 'android') {
				let wxBrowerBack = uni.getStorageSync('wxBrowerBack');
				if (wxBrowerBack) {
					uni.navigateTo({
						url: '/standard/super/tradeSuccess?orderSn=' + this.payInfo.paySn +
							'&sourceType=tradeSuccess'
					})
				}
			}
			//#endif
		},
		methods: {
			//获取paypal设置信息
			getSetting() {
				let param = {};
				param.url = 'v3/system/front/setting/getSettings';
				param.method = 'GET';
				param.data = {};
				param.data.names = 'paypal_client_id,paypal_is_sandBox';
				this.$request(param).then(res => {
					if (res.state == 200) {
						this.paypal_client_id = res.data[0];
						this.paypal_is_sandBox = res.data[1] == 1 ? 'sandbox' : 'live'; //沙盒模式 1:是  0:否;
					}
				})
			},
			//初始化终端类型
			initClient() {
				//app-2-start



				//app-2-end
				//#ifdef H5
				this.client = this.$isWeiXinBrower() ? 'wxbrowser' : 'mbrowser';
				//#endif
				//wx-2-start
				//#ifdef MP-WEIXIN
				this.client = 'wxxcx';
				//#endif
				//wx-2-end









			},
			//获取支付信息
			getPayInfo() {
				let param = {};
				param.url = 'v3/member/front/memberSuper/pay/buySuper';
				param.method = 'POST';
				param.data = {};
				param.data.payType = this.payType;
				this.$request(param).then(res => {
					if (res.state == 200 || res.state == 267) {
						this.payInfo = res.data;
						this.paySn = res.data.paySn;
						if (this.reqFlag == 1) {
							this.reqFlag = 2
						} else if (this.reqFlag == 0) {
							this.reqFlag = 1
						}
					}
				})
			},
			//获取支付方式
			getPayMethod() {
				let {
					client,
					wxBrowerCode
				} = this;
				this.$request({
					url: 'v3/system/common/payMethod',
					data: {
						source: client,
						type: 1
					},
				}).then(res => {
					if (res.state == 200) {
						res.data.map((item) => {
							switch (item.payMethod) {
								case 'tl_wx':
									item.payIcon = process.env.VUE_APP_IMG_URL +
										`pay/wx_pay_icon.png`;
									break

								case 'tl_alipay':
									item.payIcon = process.env.VUE_APP_IMG_URL +
										`pay/alipay_pay_icon.png`;
									break

								case 'tl_bal':
									item.payIcon = process.env.VUE_APP_IMG_URL +
										`pay/balance_pay_icon.png`;
									break

								default:
									item.payIcon = process.env.VUE_APP_IMG_URL +
										`pay/${item.payMethod}_pay_icon.png`
							}
						})
						this.payMethod = res.data;
						if (!wxBrowerCode) {
							this.selData = this.payMethod[0];
							if (this.selData.payMethod.indexOf('bal') > -1) this.balanceCon = true
						} else {
							//有code的话要默认选中微信支付，并直接提交订单
							this.selData = this.payMethod.filter(item => item.payMethod.indexOf('wx') > -1)[0];
							let _this = this;
							if (this.isAllowAutoPay) {
								this.autoPayInterval = setInterval(function() {
									if (_this.payInfo.needPay != undefined) {
										_this.pay();
										//清除倒计时
										clearInterval(_this.autoPayInterval)
									}
								}, 1000);
							}
						}

						//getPayInfo和getPayMethod方法进行比较哪个最后请求，初始值为0,第一个请求的接口结果将reqFlag置为1，到第二个请求将已经置为1的reqflag置为2
						//让reqFlag == 2 置为所有关于页面显示的接口加载完成后显示页面的 加载标志
						if (this.reqFlag == 1) {
							this.reqFlag = 2
						} else if (this.reqFlag == 0) {
							this.reqFlag = 1
						}
					}
				})
			},
			//选择支付方式事件
			selectPayMethod(val) {
				let _this = this
				if (val.payMethod == 'balance') {
					//余额支付
					if (this.hasSetPassword) {
						if (
							Number(this.payInfo.balanceAvailable) < Number(this.payInfo.needPay)
						) {
							//余额小于订单金额
							uni.showToast({
								title: _this.$L('您的余额不足,请选择其他支付方式！'),
								icon: 'none'
							})
						} else {
							this.balanceCon = true
							this.selData = val
						}
					} else {
						uni.showModal({
							title: _this.$L('温馨提示!'),
							content: _this.$L('未设置支付密码'),
							confirmColor: '#FC1E1C',
							confirmText: _this.$L('立即设置'),
							success: function(res) {
								if (res.confirm) {
									_this.$Router.push({
										path: '/pages/account/managePwd',
										query: {
											source: 'set_pay'
										}
									})
								} else if (res.cancel) {}
							}
						})
					}

				}else {
					this.selData = val
					this.balanceCon = false
				}
			},
			//余额支付是否已设置过密码
			isSetPassword() {
				this.$request({
					url: 'v3/business/front/orderPay/payPwdCheck',
					method: 'GET'
				}).then((res) => {
					if (res.state == 200) {
						this.hasSetPassword = res.data
					}
				})
			},
			//失焦,获取输入密码值
			passwordBlur(e) {
				this.passwordVal = e.detail.value;
				var reg = /^[\S]{6,20}$/;
				//密码的验证 6～20位，英文、数字或符号
				let flag = reg.test(this.passwordVal);
				if (!flag) {
					uni.showToast({
						title: this.$L('请输入6~20位英文、数字或符号'),
						icon: 'none',
						duration: 1000
					})
					this.passwordVal = ''
				}
			},
			//密码是否可见
			showPasword() {
				this.isShowPasw = !this.isShowPasw
			},

			clickPay() {
				if (!this.ableClick) {
					return
				}

				this.ableClick = false

				this.pay()
			},

			//立即支付事件
			pay() {
				const {
					selData,
					client,
					wxBrowerCode
				} = this;
				let _this = this;
				let param = {};
				param.url = 'v3/member/front/memberSuper/pay/doPay';
				param.method = 'POST';
				param.data = {};
				param.data.payType = selData.payType;
				param.data.payMethod = selData.payMethod;
				param.data.paySn = this.payInfo.paySn;

				if (selData.payMethod == 'balance') {
					//余额支付
					//支付密码,使用余额时必传
					if (this.hasSetPassword) {
						param.data.payPwd = this.$base64Encrypt(this.passwordVal)
					} else {
						this.ableClick = true
						uni.showModal({
							title: _this.$L('温馨提示!'),
							content: _this.$L('未设置支付密码'),
							confirmColor: '#FC1E1C',
							confirmText: _this.$L('立即设置'),
							success: function(res) {
								if (res.confirm) {
									_this.$Router.push({
										path: '/pages/account/managePwd',
										query: {
											source: 'set_pay'
										}
									})
								} else if (res.cancel) {}
							}
						})
					}
				} else {
					if (client == 'wxxcx') {
						//微信小程序支付
						uni.login({
							success: (code) => {
								param.data.code = code.code
								param.data.codeSource = 1 //用户code来源（JSAPI支付时必填）：1==小程序，2==微信内部浏览器
								_this.$request(param).then((res) => {
									if (res.state == 200) {
										let tmp_data = res.data.payData
										if (res.data.actionType == null) {
											//微信小程序支付
											uni.requestPayment({
												timeStamp: tmp_data.timeStamp,
												nonceStr: tmp_data.nonceStr,
												package: tmp_data.packageValue?tmp_data.packageValue:tmp_data.package,
												signType: 'MD5',
												paySign: tmp_data.paySign,
												success: function(res) {
													_this.payTip('success')
												},
												fail: function(res) {
													_this.ableClick = true
													_this.payTip('fail')
												}
											})
										}
									} else {
										_this.$api.msg(res.msg)
									}
								})
							}
						})
						return false
					} else if (client == 'wxbrowser') {
						//微信h5支付
						if (!wxBrowerCode) {
							getWxH5Appid()
							return false
						} else {
							param.data.code = wxBrowerCode
							param.data.codeSource = 2 //用户code来源（JSAPI支付时必填）：1==小程序，2==微信内部浏览器
						}
					}
				}

				this.$request(param).then((res) => {
					if (res.state == 200) {
						this.ableClick = true
						if (selData.payMethod == 'balance') {
							uni.showToast({
								title: _this.$L('支付成功!'),
								duration: 700
							})
							setTimeout(() => {
								_this.$Router.replace({
									path: '/standard/super/tradeSuccess',
									query: {
										orderSn: _this.payInfo.paySn,
										sourceType: 'tradeSuccess'
									}
								})
							}, 1000)
						}else {
							let tmp_data = res.data.payData

							if (selData.payMethod == 'paypal') {
								// #ifdef H5
								window.location.href = tmp_data
								return
								// #endif
								//app-3-start









































								//app-3-end
							}

							if (res.data.actionType == 'redirect') {
								window.location.href = tmp_data;
							} else if (res.data.actionType == null) {
								if (client == 'wxbrowser') {
									uni.hideLoading();
									_this.wxBrowerCode = '';
									//微信h5支付
									_this.$weiXinBrowerPay({
										timestamp: tmp_data.timeStamp,
										nonceStr: tmp_data.nonceStr,
										package: tmp_data.packageValue?tmp_data.packageValue:tmp_data.package,
										signType: 'MD5',
										paySign: tmp_data.paySign,
										appId: tmp_data.appId, //此参数可不用
										success: function(r) {
											if (r.errMsg == "chooseWXPay:ok") {
												uni.setStorage({
													key: 'wxBrowerBack',
													data: true
												});
												setTimeout(() => {
													uni.navigateBack({
														delta: 2
													})
												}, 300)
											} else {
												_this.payTip('fail');
												_this.isAllowAutoPay = false; //支付失败后禁止自动支付
											}
										},
										cancel: function(r) {
											_this.payTip('fail')
											_this.isAllowAutoPay = false //支付失败后禁止自动支付
										}
									})
								} else if (client == 'app') {
									//APP支付
									let provider = '';
									let orderInfo = {};
									if (selData.payMethod == 'wx') {
										provider = 'wxpay';
										orderInfo.appid = tmp_data.appId;
										orderInfo.noncestr = tmp_data.nonceStr;
										orderInfo.package = tmp_data.packageValue?tmp_data.packageValue:tmp_data.package;
										orderInfo.partnerid = tmp_data.partnerId;
										orderInfo.prepayid = tmp_data.prepayId;
										orderInfo.timestamp = tmp_data.timeStamp;
										orderInfo.sign = tmp_data.sign;
									} else if (selData.payMethod.indexOf('alipay')>-1) {
										provider = 'alipay'
									}
									uni.requestPayment({
										provider: provider,
										orderInfo: provider == 'alipay' ? res.data.payData :
										orderInfo, //订单数据
										success: function(res) {
											_this.payTip('success');
										},
										fail: function(err) {
											_this.payTip('fail');
										}
									});

								}
							} else if (res.data.actionType == 'autopost') {
								document.write(res.data.payData);
							} else if (res.data.actionType == 'native') {
								this.showState = true
								// #ifdef H5
								window.location.href = res.data.payData
								// #endif
								// #ifdef APP-PLUS||MP-WEIXIN
								this.$Router.push({
									path: '/pages/index/skip_to',
									query: {
										url: res.data.payData
									}
								})
								// #endif
							}
						}
					} else {
						this.ableClick = true
						this.$api.msg(res.msg)
						if (this.passwordVal != '') {
							this.passwordVal = '';
						}
					}
				})
			},

			//支付操作完成提示
			payTip(type) {
				let _this = this;
				//如果来自下单，直接跳转订单列表，否则返回上一级页面（订单列表或者订单详情），并更新数据
				if (type == 'success') {
					//提示支付成功
					uni.showToast({
						title: this.$L('支付成功!'),
						duration: 700
					})
					setTimeout(() => {
						uni.redirectTo({
							url: '/standard/super/tradeSuccess?orderSn=' + _this.payInfo.paySn +
								'&sourceType=tradeSuccess'
						})
					}, 1500)
				} else if (type == 'fail') {
					//提示支付失败
					this.$api.msg(this.$L('支付失败,请重试～'))
					if (this.client == 'wxbrowser') {
						setTimeout(() => {
							if (window.history.length > 3) {
								window.history.go(-2)
							} else {
								window.history.back()
							}
						}, 700)
						if (this.payMethodType == 'create') {
							setTimeout(() => {
								uni.redirectTo({
									url: '/standard/super/index'
								})
							}, 1500)
						}
						return
					}
					if (this.payMethodType == 'create') {
						//下单
						if (type == 'fail') {
							setTimeout(() => {
								uni.redirectTo({
									url: '/standard/super/index'
								})
							}, 1000)
						} else {
							_this.$Router.replace({
								path: '/standard/super/tradeSuccess',
								query: {
									orderSn: _this.payInfo.paySn,
									sourceType: 'tradeSuccess',
								}
							})
						}
					}
				}
			}
		}
	}
</script>

<style lang="scss">
	page {
		background: #FFFFFF;
		width: 750rpx;
		margin: 0 auto;
	}

	.container {
		display: flex;
		flex-direction: column;
		flex: 1;

		.order_info {
			border-top: 20rpx solid #F5F5F5;
			width: 750rpx;
			background: #fff;

			.item {
				position: relative;
				height: 100rpx;
				width: 100%;
				padding: 0 20rpx;

				.tit {
					color: $main-font-color;
					font-size: 30rpx;
				}

				.order_sn {
					color: #2D2D2D;
					font-size: 26rpx;
				}

				&.b_b:after {
					left: 20rpx;
				}

				.order_amount {
					color: var(--color_price);
					font-weight: bold;

					.unit,
					.price_decimal {
						font-size: 24rpx;
						margin-right: 3rpx;
						line-height: 24rpx;
					}

					.price_int {
						font-size: 34rpx;
						line-height: 34rpx;
					}
				}
			}
		}

		.pay_part {
			border-top: 20rpx solid #F5F5F5;
			background: #fff;
			flex: 1;

			.title {
				color: $main-font-color;
				font-size: 32rpx;
				margin-top: 30rpx;
				margin-left: 20rpx;
			}

			.item {
				width: 100%;
				padding: 20rpx;
				position: relative;

				.left {
					.pay_icon {
						width: 80rpx;
						height: 80rpx;
					}

					.tit {
						color: $main-font-color;
						font-size: 28rpx;
						margin-left: 20rpx;
					}

				}

				.right {
					.balance_available {
						font-size: 28rpx;
						color: #999;
						margin-right: 20rpx;
					}

					.active {
						color: var(--color_price);
					}

					.inactive {
						background-color: #dadada;
						border-radius: 50%;
					}

					.iconfont {
						color: $main-third-color;
						font-size: 32rpx;
					}

					.has_sel {
						color: var(--color_main);
					}

					&.b_b:after {
						left: 20rpx;
					}
				}

			}

			.no_payMth {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				width: 100%;
				padding-top: 30%;

				image {
					width: 190rpx;
					height: 190rpx;
				}

				text {
					color: #999;
					margin-top: 20rpx;
				}
			}

		}

		.balance_password {
			border-top: 20rpx solid #F5F5F5;

			.balance_password_title {
				width: 750rpx;
				height: 100rpx;
				display: flex;
				align-items: center;
				font-size: 30rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #333333;
				line-height: 39rpx;
				padding-left: 20rpx;
				box-sizing: border-box;
				border-bottom: 1rpx solid #F5F5F5;
				background: #FFFFFF;
			}

			.input_password {
				width: 750rpx;
				height: 86rpx;
				background: #FFFFFF;

				.input_password_con {
					margin: 0 41rpx;
					height: 86rpx;
					display: flex;
					justify-content: space-between;
					align-items: center;
					border-bottom: 1rpx solid #F5F5F5;

					input {
						font-size: 26rpx;
						font-family: PingFang SC;
						font-weight: 500;
						color: #999999;
						line-height: 39rpx;
						width: 80%;
					}

					view {
						margin-left: 20rpx;
						color: #666666;
						font-size: 22rpx;
					}
				}
			}
		}

		.btn_recharge {
			width: 670rpx;
			height: 88rpx;
			background: var(--color_main_bg);
			border-radius: 44rpx;
			color: #fff;
			font-size: 36rpx;
			position: absolute;
			right: 0rpx;
			left: 0rpx;
			margin: 0 auto;
		}
	}
</style>