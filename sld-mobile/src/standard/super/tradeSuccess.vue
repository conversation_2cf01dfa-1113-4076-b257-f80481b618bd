<!-- 交易成功页面 -->
<template>
	<scroll-view v-if="isReady" class="container" scroll-y @scrolltolower='getData'>
		<view class="main_content">
			<!-- 订单状态 start -->
			<view class="order_state">
				<image :src="imgUrl+'super/success.png'" mode="aspectFit" class="order_state_img"></image>
				<view class="order_state_text">{{$L('支付成功')}}</view>
				<view class="order_state_desc">尊贵的超级会员，您已成功购买{{cardValue}}<br/>有效期至 {{cardTime}}</view>
				<view class="order_btn" @click="backIndex">{{$L('返回首页')}}</view>
			</view>
			<!-- 订单状态 end -->
			<!-- 推荐商品 start-->
			<view class="recomment">
				<recommendGoods ref='recomment_goods' />
			</view>
			<!-- 推荐商品 end-->
		</view>
	</scroll-view>
</template>
<script>
	import {
		mapState
	} from 'vuex';
	import recommendGoods from "@/components/recommend-goods.vue";
	import uniPopup from '@/components/uni-popup/uni-popup.vue';
	import uniPopupMessage from '@/components/uni-popup/uni-popup-message.vue';
	import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue';
	let startY = 0,
		moveY = 0,
		pageAtTop = true;
	export default {
		components: {
			recommendGoods,
			uniPopup,
			uniPopupMessage,
			uniPopupDialog
		},
		data() {
			return {
				imgUrl: process.env.VUE_APP_IMG_URL,
				coverTransform: 'translateY(0px)',
				coverTransition: '0s',
				moving: false,
				orderSn:'',   //订单号
				sourceType:'',	//页面来源	orderDetail:订单详情	orderList:订单列表	tradeSuccess:支付成功,
				cardValue: '', //会员类型
				cardTime: '', //有效期
				isReady: false,
			}
		},
		async onLoad(option) {
			//#ifdef H5
			let wxBrowerBack = uni.getStorageSync('wxBrowerBack');
			if(wxBrowerBack){
				uni.removeStorageSync('wxBrowerBack');
				if(uni.getSystemInfoSync().platform == 'ios') {
					setTimeout(()=>{
						this.$router.go(0)
					}, 300)
				}
			}
			//#endif
			//订单号
			this.orderSn = this.$Route.query.orderSn;
			this.sourceType =this.$Route.query.sourceType;
			this.initData();
		},
		computed: {
			...mapState(['hasLogin', 'userInfo', 'userCenterData'])
		},
		methods: {
			initData() {
				let param = {};
				param.url = 'v3/member/front/memberSuper/pay/superPaySuccess';
				param.data = {};
				param.data.paySn = this.orderSn;
				this.$request(param).then(res=>{
					if (res.state == 200) {
						this.cardValue = res.data.payTypeValue;
						this.cardTime = res.data.superExpirationTime.split(' ')[0];
						this.isReady = true;
					} else {
						this.$api.msg(res.msg);
						setTimeout(()=>{
							this.backIndex();
						}, 1500)
					}
				})
			},

			/**
			 * 统一跳转接口,拦截未登录路由
			 * navigator标签现在默认没有转场动画，所以用view
			 */
			navTo(url) {
				if (!this.hasLogin) {
					let urls = this.$Route.path;
					const query = this.$Route.query;
					uni.setStorageSync('fromurl', {
						url:urls,
						query
					});
					url = '/pages/public/login';
				}
				this.$Router.push(url)
			},

			/**
			 *  会员卡下拉和回弹
			 *  1.关闭bounce避免ios端下拉冲突
			 *  2.由于touchmove事件的缺陷（以前做小程序就遇到，比如20跳到40，h5反而好很多），下拉的时候会有掉帧的感觉
			 *    transition设置0.1秒延迟，让css来过渡这段空窗期
			 *  3.回弹效果可修改曲线值来调整效果，推荐一个好用的bezier生成工具 http://cubic-bezier.com/
			 */
			coverTouchstart(e) {
				if (pageAtTop === false) {
					return;
				}
				this.coverTransition = 'transform .1s linear';
				startY = e.touches[0].clientY;
			},
			coverTouchmove(e) {
				moveY = e.touches[0].clientY;
				let moveDistance = moveY - startY;
				if (moveDistance < 0) {
					this.moving = false;
					return;
				}
				this.moving = true;
				if (moveDistance >= 80 && moveDistance < 100) {
					moveDistance = 80;
				}
				if (moveDistance > 0 && moveDistance <= 80) {
					this.coverTransform = `translateY(${moveDistance}px)`;
				}
			},
			coverTouchend() {
				if (this.moving === false) {
					return;
				}
				this.moving = false;
				this.coverTransition = 'transform 0.3s cubic-bezier(.21,1.93,.53,.64)';
				this.coverTransform = 'translateY(0px)';
			},
			//返回首页
			backIndex(){
				this.$Router.pushTab('/pages/index/index');
			},
			//去评价页面
			goEvaluate(){
				this.$Router.replace({path:'/pages/order/publishEvaluation',query:{orderSn:this.orderSn,sourceType:this.sourceType}});
			},
			//查看订单
			lookOrder(){
				this.$Router.replace('/pages/order/list');
			},
		}
	}
</script>
<style lang='scss'>
	page {
		background: $bg-color-split;
	}

	.container {
		display: flex;
		flex: 1;
		height: 100vh;
		position: relative;
		width: 750rpx;
		margin: 0 auto;

		.main_content{
			width: 100%;
			.order_state{
				display: flex;
				flex-direction: column;
				align-items: center;
				background-color: #FFFFFF;
				padding-top: 92rpx;
				padding-bottom: 80rpx;
				.order_state_img{
					width: 80rpx;
					height: 80rpx;
				}
				.order_state_text{
					color: #0D141C;
					font-size: 34rpx;
					font-family: PingFang SC;
					font-weight: 800;
					margin-top: 30rpx;
				}
				.order_state_desc {
					width: 600rpx;
					color: #666666;
					line-height: 42rpx;
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: 500;
					text-align: center;
					margin-top: 50rpx;
				}
				.order_btn{
					width: 210rpx;
					height: 60rpx;
					line-height: 60rpx;
					color: #C7944B;
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: 500;
					text-align: center;
					border: 1rpx solid #C7944B;
					border-radius: 30rpx;
					margin-top: 50rpx;
				}
			}
			.recomment{
				background:linear-gradient(to bottom, #FFFFFF 0%, #F5F5F5 20%, #F5F5F5 100%) ;
				box-sizing: border-box;
				border-radius: 30rpx 30rpx 0 0;
			}
		}
	}

</style>

