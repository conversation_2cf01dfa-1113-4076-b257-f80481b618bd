<!-- 付费会员用户协议 -->
<template>
	<view class="container_pri">




		<rich-text :nodes="h5_wx_html"></rich-text>

	</view>
</template>

<script>
	import jyfParser from '@/components/jyf-parser/jyf-parser.vue'
	import { quillEscapeToHtml } from '@/utils/common.js'
	export default {
		components: {
			jyfParser
		},
		data() {
			return {
				h5_wx_html: ''
			};
		},
		onLoad() {
			this.getPolicy();
		},
		methods: {
			getPolicy() {
				let param = {}
				param.data = {}
				param.data.agreementCode = 'supper_member_agreement';
				param.url = 'v3/system/front/agreement/detail'
				this.$request(param).then(res => {
					this.h5_wx_html = res.data.content ? quillEscapeToHtml(res.data.content) : '';
				})
			}
		}
	};
</script>
<style>
	page {
		width: 750rpx;
		overflow: auto;
		margin: 0 auto;
	}

	.container_pri {
		width: 680rpx;
		margin: 0 auto 20rpx;
		word-break: break-all;
	}

	p {
		margin-top: 20px;
	}

	.container_pri ::v-deep .rich_text_image {
		width: 680rpx;
	}
</style>
