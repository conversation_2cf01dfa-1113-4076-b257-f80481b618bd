<template>
	<view class="content" :style="mix_diyStyle">
		<!-- 左侧竖向导航 -->
		<scroll-view scroll-y class="left_aside" :style="{height:windowHeight+'px'}">
			<view class="content_scroll">
				<view v-for="(item_l, index_l) in leftData" :key="index_l" class="f_item"
					:class="{ active: item_l.categoryId === currentId,active_o:currIndex-1==index_l,active_t:currIndex+1==index_l }"
					@click="changeCate(item_l.categoryId, index_l)">
					{{item_l.categoryName}}
				</view>
				<view class="content_scroll_box" v-if="leftData.length>0&&currIndex+1==leftData.length"></view>
			</view>
		</scroll-view>
		<!-- 右侧商品显示 -->
		<scroll-view scroll-with-animation scroll-y class="right_aside">
			<!-- 每个分类上部轮播图 -->
			<view class="cat_swiper" v-if="topSwiperData.length > 0 && topSwiperData[0].imgUrl != ''">
				<uni-swiper-dot :info="topSwiperData" :current="current" field="content"
					:mode="topSwiperData && topSwiperData.length > 1 ? 'dot' : ''" :dotsStyles="dotsStyles">
					<swiper class="swiper-box" @change="change" circular autoplay="true">
						<swiper-item v-for="(item_top, index_top) in topSwiperData" :key="index_top">
							<view class="swiper-item flex_row_center_center" @click="$diyNavTo(item_top)">
								<image class="item_img" :src="item_top.imgUrl" mode="aspectFit" />
							</view>
						</swiper-item>
					</swiper>
				</uni-swiper-dot>
			</view>
			<block v-if="!rightData || (rightData && rightData.length == 0 && noData)">
				<view class="noData">
					<image :src="imgUrl + 'empty_goods.png'" mode="aspectFit"></image>
					<text>{{ $L('暂无任何商品~') }}</text>
				</view>
			</block>
			<block v-else>
				<!-- 每个二级分类盒子 -->
				<view v-for="item_r in rightData" :key="item_r.categoryId" class="s_list"
					:id="'main-' + item_r.categoryId">
					<!-- 二级分类标题 -->
					<text class="s_item" @click="navToList(item_r)">{{item_r.categoryName}}</text>
					<!-- 二级分类列表 -->
					<view class="t_list" v-if="item_r.children.length != undefined">
						<view @click="navToList(item_r_3)" class="t_item" v-for="item_r_3 in item_r.children"
							:key="item_r_3.categoryId">
							<view class="cat_img" :style="{ backgroundImage: 'url(' + item_r_3.categoryImage + ')'}"></view>
							<text>{{ item_r_3.categoryName }}</text>
						</view>
					</view>
				</view>
			</block>
		</scroll-view>
	</view>
</template>

<script>
	let app = getApp()
	import uniSwiperDot from '@/components/uni-swiper-dot/uni-swiper-dot.vue'
	import {
		mapState
	} from 'vuex'
	export default {
		components: {
			uniSwiperDot
		},
		data() {
			return {
				current: 0,
				currentId: '',
				leftData: [], //左侧分类数据
				rightData: [], //右侧分类数据
				topSwiperData: [], //一级分类轮播图
				imgUrl: process.env.VUE_APP_IMG_URL, //全局图片地址
				dotsStyles: {
					selectedBackgroundColor: '#fff',
					width: 6,
					height: 6,
					selectedBorder: 'none',
					backgroundColor: 'rgba(0, 0, 0, .2)',
					border: 'none',
					bottom: 8
				},
				noData: false, //无分类数据
				ifOnShow: false,
				currIndex: 0,
			}
		},
		onLoad() {
			setTimeout(() => {
				uni.setNavigationBarTitle({
					title: this.$L('分类')
				})
			}, 0);

			this.initData()
		},
		onShow() {
			//wx-1-start
			// #ifdef MP-WEIXIN
			let url = process.env.VUE_APP_API_URL.substring(
				0,
				process.env.VUE_APP_API_URL.length - 1
			)
			this.$sldStatEvent({
				behaviorType: 'pv',
				pageUrl: url + '/pages/category/category',
				referrerPageUrl: ''
			})
			// #endif
			//wx-1-end
			this.getCartNum()
		},
		computed: {
			...mapState(['hasLogin']),
			windowHeight(){
				const system = uni.getSystemInfoSync()
				return system.windowHeight
			}
		},
		methods: {
			initData() {
				let param = {}
				param.url = 'v3/goods/front/goods/category/topCategory'
				this.$request(param).then((res) => {
					if (res.data.length > 0) {
						this.leftData = res.data
						this.currentId = this.leftData[0].categoryId
						this.changeCate(this.currentId, 0)
					}
				})
			},

			changeCate(categoryId1, index) {
				this.currIndex = index
				this.currentId = categoryId1
				this.rightData = []
				let param = {}
				param.url = 'v3/goods/front/goods/category/bottomCategory'
				param.data = {}
				param.data.categoryId1 = categoryId1

				this.$request(param).then((res) => {
					if (res.data.length > 0) {
						this.noData = false
						this.rightData = res.data
						this.current = 0

						if (this.leftData[index].mobileImage != null) {
							if (this.leftData[index].mobileImage.substr(0, 1) == '[') {
								this.topSwiperData = JSON.parse(this.leftData[index].mobileImage)
							} else {
								this.topSwiperData.push(this.leftData[index].mobileImage)
							}
							this.topSwiperData = this.topSwiperData.filter(
								(item) => item.imgUrl != ''
							)
						} else {
							this.topSwiperData = []
						}
					} else {
						this.noData = true
					}
				})
			},

			//获取购物车数据
			getCartNum() {
				if (this.hasLogin) {
					let param = {}
					param.url = 'v3/business/front/cart/cartNum'
					param.method = 'GET'
					param.data = {}
					this.$request(param)
						.then((res) => {
							if (res.state == 200) {
								if (res.data > 0) {
									uni.setTabBarBadge({
										index: 3,
										text: res.data.toString()
									})
								} else {
									uni.hideTabBarRedDot({
										index: 3
									})
								}
							} else {
								this.$api.msg(res.msg)
							}
						})
						.catch((e) => {
							//异常处理
						})
				} else {
					this.getNoLoginCartNum()
				}
			},
			//获取未登录，购物车数量
			getNoLoginCartNum() {
				let cartNum = 0
				let cart_list = uni.getStorageSync('cart_list')
				if (cart_list && cart_list.storeCartGroupList) {
					cart_list.storeCartGroupList.map((item) => {
						item.promotionCartGroupList.map((item1) => {
							item1.cartList.map((item2) => {
								cartNum++
							})
						})
					})
				}
				if (cartNum > 0) {
					uni.setTabBarBadge({
						index: 3,
						text: cartNum.toString()
					})
				} else {
					uni.hideTabBarRedDot({
						index: 3
					})
				}
			},

			navToList(item) {
				this.$Router.push({
					path: '/standard/product/list',
					query: {
						categoryId: item.categoryId
					}
				})
			},
			change(e) {
				this.current = e.detail.current
			}
		}
	}
</script>

<style lang="scss">
	page,
	.content {
		height: 100%;
		background-color: $bg-color-split;
		width: 750rpx;
		margin: 0 auto;
	}

	.content {
		display: flex;
	}

	.left_aside {
		flex-shrink: 0;
		width: 190rpx;
		background-color: #fff;
	}

	.f_item {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 190rpx;
		height: 100rpx;
		color: #666;
		position: relative;
		font-size: 28rpx;
		background: #fff;

		&.active {
			color: var(--color_main);
			background: #f5f5f5;
			font-size: 32rpx;
			font-weight: bold;

			&:before {
				content: '';
				position: absolute;
				left: 0;
				top: 50%;
				transform: translateY(-50%);
				height: 100rpx;
				width: 5rpx;
				background-color: var(--color_main);
				border-radius: 0 4px 4px 0;
				opacity: 0.8;
			}
		}

		&.active_o {
			border-radius: 0rpx 0rpx 30rpx 0rpx;
		}

		&.active_t {
			border-radius: 0rpx 30rpx 0rpx 0rpx;

		}
	}

	.right_aside {
		flex: 1;
		overflow: hidden;
		padding-left: 20rpx;
	}

	.s_item {
		display: flex;
		align-items: center;
		font-size: 28rpx;
		color: #333;
		margin-top: 30rpx;
		margin-bottom: 20rpx;
		padding-left: 6rpx;
		font-weight: bold;
	}

	.t_list {
		display: flex;
		flex-wrap: wrap;
		background: #fff;
		border-radius: 6rpx;
		margin-right: 20rpx;

		&:after {
			content: '';
			flex: 99;
			height: 0;
		}
	}

	.t_item {
		flex-shrink: 0;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-direction: column;
		font-size: 26rpx;
		color: #666;
		padding: 20rpx 18rpx;

		.cat_img {
			width: 133rpx;
			height: 133rpx;
			background-size: contain;
			background-position: center center;
			background-repeat: no-repeat;
			overflow: hidden;
		}

		text {
			color: #666;
			font-size: 24rpx;
			margin-top: 20rpx;
			font-weight: 600;
		}
	}

	.cat_swiper {
		width: 520rpx;
		height: 210rpx;
		margin-top: 20rpx;

		.swiper-box {
			width: 520rpx;
			height: 210rpx;
			border-radius: 10rpx;
			overflow: hidden;

			.swiper-item {
				.item_img {
					width: 520rpx;
					height: 210rpx;
					border-radius: 10rpx;
					overflow: hidden;
				}
			}
		}
	}

	.noData {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-top: 265rpx;

		image {
			width: 380rpx;
			height: 280rpx;
		}

		text {
			font-size: 26rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: #999999;
			margin-top: 31rpx;
		}
	}

	.content_scroll {
		background: #f5f5f5;
	}

	.content_scroll_box {
		width: 100%;
		height: 30rpx;
		border-radius: 0 30rpx 0 0;
		background-color: #fff;
	}
</style>