<template>
	<view :style="mix_diyStyle">
		<view>
			<view :style="{ height: statusBarHeight, width: '100%' }"></view>
			<block v-if="!openState">
				<notOpen></notOpen>
			</block>
			<block v-else>
				<view class="bg_1"></view>
				<!-- #ifdef MP -->
				<view class="custom-nav" :style="{
					height: menuButtonInfo ? menuButtonInfo.height + 'px' : '32px',
					top: menuButtonInfo ? menuButtonInfo.top + 'px' : 'var(--status-bar-height)'
				}">
					<view class="nav-content"
						:style="{ paddingRight: menuButtonInfo ? (menuButtonInfo.width + 20) + 'px' : '97px' }">
						<image class="logo" mode="aspectFill" :src="imgUrl+'graphic/shop_logo_10kb.jpg'"></image>
						<view class="search-box" @tap.stop="goSearch">
							<image class="search-icon" :src="imgUrl + 'svideo/search_icon.png'" mode="aspectFit"></image>
							<text class="placeholder">{{ $L('搜索你感兴趣的内容') }}</text>
						</view>
					</view>
				</view>
				<!-- #endif -->
				<!-- #ifndef MP -->
				<view class="custom-nav-h5" :style="{ top: statusBarHeight }">
					<view class="nav-content-h5">
						<image class="logo" mode="aspectFill" :src="imgUrl+'graphic/shop_logo_10kb.jpg'"></image>
						<view class="search-box" @tap.stop="goSearch">
							<image class="search-icon" :src="imgUrl + 'svideo/search_icon.png'" mode="aspectFit"></image>
							<text class="placeholder">{{ $L('搜索你感兴趣的内容') }}</text>
						</view>
					</view>
				</view>
				<!-- #endif -->

				<view class="nav" v-if="labelList.length">
					<view class="nav_mine">
						<view v-for="(item, index) in labelList" :key="index"
							:class="'nav_item ' + (curLabelId == item.labelId ? 'on' : '')" @tap="changeLabel"
							:data-id="item.labelId">
							<text class="label_name" style="font-size: 17px">{{ item.labelName }}</text>
							<text class="bottom_line"></text>
						</view>
					</view>
				</view>

				<scroll-view :scroll-y="labelList.length > 0" class="live_list" v-if="labelList.length">
					<swiper v-if="themeList.length > 0" class="rec_svideo" autoplay circular>
						<swiper-item v-for="(item, index) in themeList" :key="index">
							<image :data-themeId="item.themeId" :data-themeName="item.themeName" class="rec_img"
								:src="item.carouselImage" mode="aspectFit" @tap="goRecTopic">
							</image>
						</swiper-item>
					</swiper>

					<!-- 短视频列表item -->
					<listVideoItem :videoList="videoList" :bgStyle="bgStyle" :listFavIcon="imgUrl + 'svideo/fav.png'"
						:listPlayNum="imgUrl + 'svideo/zx_play.png'" :curLabelId="curLabelId * 1" :authorId="author_id" />

					<!-- 数据加载完毕 -->
					<dataLoaded :showFlag="!hasmore && videoList && videoList.length > 0" />

					<!-- 数据加载中 -->
					<dataLoading :showFlag="!firstLoading && hasmore && loading" />
					<!-- 页面空数据 -->
					<emptyData :showFlag="!firstLoading && showEmpty"
						:emptyIcon="imgUrl + 'svideo/live_list_empty_icon.png'" />
					<!-- 页面loading -->
					<pageLoading :firstLoading="firstLoading" :loadingIcon="imgUrl + 'svideo/page_loading_icon.gif'" />
					<view class="empty_h" :style="'height:' + bottomSateArea"></view>
				</scroll-view>
				<!-- #ifndef MP -->
				<!-- 页面空数据 -->
				<emptyData class="empty_label" :showFlag="!labelList.length"
					:emptyIcon="imgUrl + 'svideo/live_list_empty_icon.png'" />
				<!-- #endif -->
				<!-- wx-3-start -->
				<!-- #ifdef MP -->
				<emptyData class="empty_label" v-if="!labelList.length" :showFlag="!labelList.length"
					:emptyIcon="imgUrl + 'svideo/live_list_empty_icon.png'" />
				<!-- #endif -->
				<!-- wx-3-end -->
				<!-- 进入短视频个人主页入口 -->
				<!-- #ifdef H5 -->
				<view class="personal_homepage" @tap="goLiveUserCenter" data-curTab="video">
					<view class="personal_avator">
						<image :src="member_avatar" mode="aspectFit"></image>
					</view>
					<view class="my_video">{{ $L('我的发文') }}</view>
				</view>
				<!-- #endif -->
			</block>
		</view>
	</view>
</template>

<script>
import notOpen from '@/components/not_open.vue'
import {
checkPageHasMore
} from '@/utils/live'
import {
mapState
} from 'vuex'
import dataLoaded from './templates/live/dataLoaded.vue'
import dataLoading from './templates/live/dataLoading.vue'
import emptyData from './templates/live/emptyData.vue'
import pageLoading from './templates/live/pageLoading.vue'
import listVideoItem from './templates/svideo/listVideoItem.vue'
export default {
	components: {
		pageLoading,
		emptyData,
		dataLoading,
		dataLoaded,
		listVideoItem,
		notOpen
	},
	data() {
		return {
			menuButtonInfo: null,
			statusBarHeight: 0,
			labelList: [],
			curLabelId: 0,
			themeList: [],
			videoList: [],
			openState: true,
			hasmore: true,
			imgUrl: process.env.VUE_APP_IMG_URL,
			//图片地址
			loading: false,
			bgStyle: 'background-size:cover;background-position:center center;background-repeat: no-repeat;',
			//背景图片样式
			firstLoading: true,
			//是否初次加载，是的话展示页面居中的loading效果，
			author_id: 0,
			//作者id
			key: '',
			//登录的key值
			member_avatar: '', //默认作者头像
			pageSize: 10,
			current: 1,
			bottomSateArea: getApp().globalData.bottomSateArea, //iphone手机底部一条黑线的高度
			ifshow: false,
			showEmpty: false,
			targetGoodsIds: null, // 目标商品ID，用于筛选列表
		}
	},
	computed: {
		...mapState(['hasLogin', 'cartData', 'userInfo'])
	},
	props: {},
	//是否还有数据

	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad: function (options) {
		// #ifdef MP-WEIXIN
		const menuButtonInfo = wx.getMenuButtonBoundingClientRect()
		this.menuButtonInfo = menuButtonInfo
		this.statusBarHeight = menuButtonInfo.top + 'px'
		// #endif
		setTimeout(() => {
			uni.setNavigationBarTitle({
				title: this.$L('商城首页')
			})
		}, 0);

		// 保存传入的 labelId
		this.defaultLabelId = options?.labelId;
		
		this.getLabelList()
	},
	onShow() {
		// #ifdef MP-WEIXIN
		// 从本地存储获取要跳转的标签ID和商品ID
		const targetLabelId = uni.getStorageSync('temp_target_labelId');
		const targetGoodsIds = uni.getStorageSync('temp_target_goodsIds');
		if (targetLabelId || targetGoodsIds) {
			// 清除存储
			uni.removeStorageSync('temp_target_labelId');
			uni.removeStorageSync('temp_target_goodsIds');
			
			// 如果当前标签不是目标标签，则切换
			if (targetLabelId && this.curLabelId != targetLabelId) {
				this.curLabelId = Number(targetLabelId);
				this.firstLoading = true;
				this.videoList = [];
				this.current = 1;
				this.hasmore = true;
				// 如果有商品ID，添加到请求参数
				if (targetGoodsIds) {
					this.targetGoodsIds = targetGoodsIds;
				}
				this.getVideoList();
				return;
			}
		}
		// #endif
		
		// #ifdef H5
		// 获取当前路由参数
		const query = this.$Route.query;
		if ((query.labelId && this.curLabelId != query.labelId) || query.goodsIds) {
			if (query.labelId) {
				this.curLabelId = Number(query.labelId);
			}
			if (query.goodsIds) {
				this.targetGoodsIds = query.goodsIds;
			}
			this.firstLoading = true;
			this.videoList = [];
			this.current = 1;
			this.hasmore = true;
			this.getVideoList();
			return;
		}
		// #endif

		if (this.ifshow) {
			this.getVideoList()
		}
	},
	onHide() {
		this.ifshow = true
	},
	onReachBottom() {
		if (this.hasmore) {
			this.getVideoList()
		}
	},

	onPullDownRefresh() {
		this.current == 1
		this.getVideoList()
	},

	methods: {
		// 获取短视频分类
		getLabelList() {
			let {
				curLabelId,
				firstLoading
			} = this
			let param = {}
			param.data = {}
			param.url = 'v3/video/front/video/list'
			param.method = 'GET'
			this.$request(param).then((res) => {
				if (res.state == 200) {
					this.openState = true
					this.labelList = res.data.labelList
					this.member_avatar = res.data.memberAvatar
					if (this.labelList.length) {
						// 如果有默认标签ID,则使用默认标签ID
						if (this.defaultLabelId) {
							this.curLabelId = Number(this.defaultLabelId);
						} else {
							this.curLabelId = this.labelList[0].labelId;
						}
						this.getVideoList()
					}
					if (firstLoading) {
						firstLoading = false
					}
					this.firstLoading = firstLoading
				} else if (res.state == 258) {
					this.openState = false
				}
			})
		},

		//获取短视频列表
		getVideoList() {
			this.loading = true
			let {
				videoList,
				hasmore,
				firstLoading,
				themeList
			} = this
			let param = {}
			param.data = {}
			param.data.pageSize = this.pageSize
			param.data.current = this.current
			param.data.labelId = this.curLabelId
			// 如果有商品ID，添加到请求参数
			if (this.targetGoodsIds) {
				param.data.goodsIds = this.targetGoodsIds;
				// 使用完后清除，避免影响下次请求
				this.targetGoodsIds = null;
			}
			param.url = 'v3/video/front/video/list'
			param.method = 'GET'
			this.$request(param).then((res) => {
				uni.stopPullDownRefresh()
				if (res.state == 200) {
					if (this.current == 1) {
						videoList = res.data.videoList
						themeList = res.data.themeList
					} else {
						videoList = videoList.concat(res.data.videoList)
					}
					if (checkPageHasMore(res.data.pagination)) {
						this.current++
					} else {
						this.hasmore = false
						hasmore = false
					}
				} //初次加载的话更改状

				if (firstLoading) {
					firstLoading = false
				}

				this.loading = false
				this.hasmore = hasmore
				this.videoList = videoList
				this.firstLoading = firstLoading
				this.themeList = themeList
				if (this.videoList.length == 0) {
					this.showEmpty = true
				} else {
					this.showEmpty = false
				}
			})
		},

		//标签点击事件
		changeLabel(e) {
			let id = e.currentTarget.dataset.id
			let {
				curLabelId
			} = this
			if (curLabelId == id) return
			this.curLabelId = id
			this.firstLoading = true
			this.videoList = []
			this.current = 1
			this.hasmore = true
			this.getVideoList(id)
		},

		//跳转搜索页面
		goSearch: function () {
			console.log('搜索按钮被点击');
			// #ifdef MP
			console.log('小程序环境');
			try {
				uni.navigateTo({
					url: '/extra/svideo/svideoSearch',
					fail: function (err) {
						console.error('导航失败:', err);
					}
				});
			} catch (error) {
				console.error('发生错误:', error);
			}
			// #endif

			// #ifdef H5
			console.log('H5环境');
			this.$Router.push({ path: '/extra/svideo/svideoSearch', query: {} });
			// #endif
		},

		//进入推荐主题页面
		goRecTopic(e) {
			let tmp_data = e.currentTarget.dataset
			this.$Router.push({
				path: '/extra/svideo/svideoRecTopic',
				query: {
					theme_id: tmp_data.themeid,
					title: tmp_data.themename
				}
			})
		},

		//进入直播个人中心
		goLiveUserCenter(e) {
			var curTab = e.currentTarget.dataset.curtab

			if (!this.hasLogin) {
				getApp().globalData.goLogin(this.$Route)
			} else {
				this.$Router.push('/extra/user/my')
			}
		}
	}
}
</script>
<style lang="scss">
page {
	background: #fff;
}

.custom-nav {
	position: fixed;
	left: 0;
	right: 0;
	background: #fff;
	display: flex;
	align-items: center;
	padding: 0 20rpx;
	z-index: 999;
}

.nav-content {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: space-between;
	flex-direction: row;
}

.logo {
	width: 64rpx;
	height: 64rpx;
	margin-right: 20rpx;
	border-radius: 50%;
}

.search-box {
	flex: 1;
	height: 64rpx;
	background: #f6f6f6;
	border-radius: 36rpx;
	display: flex;
	align-items: center;
	padding: 0 30rpx;
	width: '70%';
}

.search-icon {
	width: 40rpx;
	height: 40rpx;
	margin-right: 16rpx;
	filter: brightness(0.2);
}

.placeholder {
	font-size: 28rpx;
	color: #999;
}

.nav_item {
	display: inline-block;
	line-height: 74rpx;
	text-align: center;
	color: #222222;
	font-size: 28rpx;
	padding: 0 25rpx;
	position: relative;
}

.nav_item.on {
	position: relative;
	font-weight: 700;
}

.nav_item.on text:nth-child(2) {
	display: block;
	width: 40rpx;
	height: 8rpx;
	background: #7ABC79;
	z-index: 1000;
	border-radius: 40rpx;
	line-height: unset;
	margin: -10rpx auto 0;
	position: absolute;
	bottom: 4rpx;
	left: 50%;
	transform: translateX(-50%);
}

.bg {
	position: fixed;
	top: 0;
	left: 0;
	width: 750rpx;
	height: 468rpx;
	z-index: 0;
	right: 0;
	margin: 0 auto;
}

.bg_1 {
	position: fixed;
	top: 0;
	left: 0;
	width: 750rpx;
	height: 530rpx;
	z-index: 0;
	right: 0;
	margin: 0 auto;
	background: linear-gradient(180deg, #ffffff 0%, rgba(255, 255, 255, 0) 100%);
}

.live_list {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	background: #fff;
	/* #ifdef MP */
	top: calc(220rpx + var(--status-bar-height));
	/* #endif */
	/* #ifdef H5 */
	top: 102px;
	/* #endif */
	margin-top: 20rpx;
}

.nav {
	position: fixed;
	left: 0;
	right: 0;
	width: 750rpx;
	height: 84rpx;
	background-color: 'transpanrent';
	display: block;
	white-space: nowrap;
	overflow: hidden;
	z-index: 99;
	right: 0;
	margin: 20rpx auto 0;
	overflow-y: hidden;
	/* #ifdef MP */
	top: calc(130rpx + var(--status-bar-height));
	/* #endif */
	/* #ifdef H5 */
	top: 88rpx;
	/* #endif */
}

.empty_h {
	/* height: 170rpx; */
	width: 100%;
	background-color: 'transpanrent';
}

/* #ifdef H5 */
.empty_label {
	position: absolute;
	top: 20%;
}
/* #endif */

.nav_mine {
	overflow-x: scroll;
	box-sizing: border-box;
	overflow-y: hidden;
	-webkit-overflow-scrolling: touch;
	/* padding-bottom: 25px; */
}

.nav_mine::-webkit-scrollbar {
	display: none;
}

/* #ifdef H5 */
.empty_label {
	position: absolute;
	top: 20%;
}
/* #endif */

/* wx-1-start */
/* #ifdef MP-WEIXIN */
.empty_label {
	position: absolute;
	top: 30%;
}
/* #endif */
/* wx-1-end */

.personal_homepage {
	width: 150rpx;
	height: 80rpx;
	background: #89ba7f;
	border-radius: 40rpx 0 0 40rpx;
	display: flex;
	align-items: center;
	position: fixed;
	right: 0;
	bottom: 208rpx;
	z-index: 10;
	padding: 0 20rpx 0 8rpx;
	box-sizing: border-box;
}

.personal_avator {
	width: 64rpx;
	height: 60rpx;
	border-radius: 50%;
	margin-right: 8rpx;
	background: #ffffff;
	display: flex;
	justify-content: center;
	align-items: center;
}

.personal_avator image {
	width: 64rpx;
	height: 60rpx;
	border-radius: 50%;
}

.my_video {
	font-size: 24rpx;
	font-family: PingFang SC;
	font-weight: 500;
	color: #ffffff;
	line-height: 30rpx;
}

.shop_logo {
	width: 100rpx;
	height: 100rpx;
}

/* #ifdef H5 */
.custom-nav-h5 {
	position: fixed;
	left: 0;
	right: 0;
	background: #fff;
	display: flex;
	align-items: center;
	padding: 0 20rpx;
	z-index: 999;
	height: 116rpx;
}

.nav-content-h5 {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: space-between;
	flex-direction: row;
	// padding-right: 97rpx;
}
/* #endif */
</style>