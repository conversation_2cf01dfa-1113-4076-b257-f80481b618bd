<template>
	<view>
		<!-- <view class="version-panel">v1215-2044</view> -->
		<view :style="mix_diyStyle">

			<HomeTop @needLogin="needLogin" :homeTabType="homeTabType" :homeTopIndex="homeTopIndex" v-if="homeTypeTop"
				:background="homeTopBg" @homeUpdate="getHomeTab" />

			<home-deco :deco_info="deco_data" :width="width" :height="height" :home_page_img="home_page_img"
				:is_show_top="homeType == 'home'" :home_is_show_top_cat="home_is_show_top_cat" :is_from_found="false"
				:homeType="homeType" :overflow="privacyPolicyModel" @cartUpdate="getCartNum" ref="homeDeco"
				@needLogin="needLogin" :is_from_founds="true" @currentColor="setCurrentColor" />

			<recommendGoods ref="recommendGoods" v-if="isShowRecommend"></recommendGoods>
			<!-- 登录弹框 -->
			<loginPop ref="loginPop" @confirmLogin="confirmLogin"></loginPop>
			<!-- 登录弹框 -->
			<!-- wx-2-start -->
			<!-- #ifdef MP -->
			<privacyPop ref="priPop"></privacyPop>
			<!-- #endif -->
			<!-- wx-2-end -->
		</view>
	</view>
</template>

<script>
console.log('index.vue')
import HomeDeco from '@/components/homeDeco/home_deco.vue';
//app-1-start
// #ifdef APP-PLUS
// import {
// 	getAppInfo
// } from '@/static/app-plus/js/utils.js';
// #endif
//app-1-end
//wx-3-start
// #ifdef MP
import privacyPop from '@/components/privacy-pop.vue';
// #endif
//wx-3-end
import HomeTop from '@/components/homeDeco/HomeTop.vue';
import recommendGoods from '@/components/recommend-goods.vue';
import {
mapMutations,
mapState
} from 'vuex';

// #ifdef H5
import vconsole from 'vconsole';
// #endif
export default {
	data() {
		return {
			deco_data: [], //首页装修数据
			home_is_show_top_cat: true, //是否显示顶部分类，默认显示
			home_page_img: [],
			width: '',
			height: '',
			shareData: {},
			privacyPolicyHtml: '', //隐私政策协议的富文本
			privacyPolicyModel: false,
			triggered: false,
			isShowRecommend: true,
			homeTopBg: '',
			homeTopIndex: 1,
			homeType: 'home',
			homeTime: null,
			homeTabType: false,
			homeTypeTop: false,
		}
	},
	components: {
		HomeDeco,
		//wx-4-start
		// #ifdef MP
		privacyPop,
		// #endif
		//wx-4-end
		recommendGoods,
		HomeTop
	},
	computed: {
		...mapState(['hasLogin', 'userInfo', 'userCenterData'])
	},
	provide() {
		return {
			recommendShow: (bool) => {
				this.isShowRecommend = bool
			}
		}
	},
	async onLoad(options) {
		await this.$handleFx()
		let _this = this;

		// #ifdef APP-PLUS
		// getAppInfo(0) //获取线上APP版本信息  参数type 0自动检查  1手动检查（手动检查时，之前取消更新的版本也会提示出来）
		// #endif

		// #ifdef MP-WEIXIN
		let url = process.env.VUE_APP_API_URL.substring(0, process.env.VUE_APP_API_URL.length - 1)
		this.$sldStatEvent({
			behaviorType: 'pv',
			pageUrl: url + '/pages/index/index',
			referrerPageUrl: ''
		})
		// #endif

		// #ifdef H5
		const debug = this.$Route && this.$Route.query?.debug
		console.log('onLoad:query=', this.$Route.query)
		if (this.hasLogin&&this.userInfo?.memberId) {
			const {memberName,memberId}=this.userInfo;
			console.log('onLoad:memberName=', memberName,memberId)
			if(memberName.indexOf('test')!=-1||memberName.indexOf('sg')!=-1)
			new vconsole()
		}
		// #endif

		// //dev_o2o-start
		// this.getSetting()
		// return
		// //dev_o2o-end
		this.homeTypeTop = true
		this.loadData()



	},
	/**
	 * 用户点击右上角分享
	 */
	onShareAppMessage: function () {
		const {
			shareData
		} = this
		return shareData
	},

	onShareTimeline: function () {
		const {
			shareData
		} = this
		return shareData
	},

	onPullDownRefresh() {
		this.loadData()
	},

	onReachBottom() {
		if (this.$refs.recommendGoods) {
			this.$refs.recommendGoods.getMoreData()
		}
		this.$refs.homeDeco.getMoreData()
	},

	onShow() {
		if (this.hasLogin) {
			this.$globalSocketIO.getMsgNum()
		}
		this.getCartNum()
	},

	onTabItemTap(e) {
		this.$refs.homeDeco.restoreTab()
	},

	methods: {
		...mapMutations(['login', 'setUserCenterData']),



		needLogin() {
			this.$refs.loginPop.openLogin()
		},
		confirmLogin() {
			this.$refs.loginPop.close()
		},

		setCurrentColor(color) {
			this.homeTopBg = color
		},
		async loadData() {
			// #ifdef H5
			this.client = 'h5'
			// #endif

			//app-4-start
			//app-4-end
			//wx-6-start
			// #ifdef MP
			this.client = 'weixinXcx'
			// #endif
			//wx-6-end
			let param = {}
			param.url = 'v3/system/front/deco/index?os=' + this.client
			param.method = 'GET'
			if (this.homeTopIndex == 2) {
				param.data = {}
				param.data.type = 'o2o_home'

			}
			this.$request(param).then((res) => {
				uni.stopPullDownRefresh()
				if (res.state == 200) {
					if (JSON.stringify(res.data) == '{}') {
						this.deco_data = null
						return
					}
					if (res.data.data != '[]' && res.data.data != '') {
						let deco_data = JSON.parse(res.data.data)
						const commentTpl = {
							"isUse": 1,
							"bottom_margin": 0,
							"color": "rgba(255,250,246,1)",
							"apply": ",home",
							"url_type": "",
							"icon": "comment",
							"sort": "7",
							"top_margin": 0,
							"type": "comment",
							"is_show": true,
							"url": "",
							"show_style": "one",
							"name": "滚动评论",
							"text": "test",
							"id": 100,
							"tplId": 18,
							"info": ""
						}

						this.deco_data = deco_data
					} else {
						this.deco_data = null
					}

					uni.stopPullDownRefresh()
					//wx-7-start
					// #ifdef MP
					this.shareData = {
						title: res.data.siteName,
						path: '/pages/index/index',
						imageUrl: res.data.xcxImage
					}
					// #endif
					//wx-7-end
					if (res.data.showTip != null) {
						this.home_page_img = JSON.parse(res.data.showTip)
						const {
							windowWidth,
							windowHeight
						} = uni.getSystemInfoSync()
						this.width = this.home_page_img[0].width || windowWidth * 0.75 * 1.8
						this.height =
							this.home_page_img[0].height || windowHeight * 0.56 * 1.8
					} else {
						this.home_page_img = []
					}

					if (Array.isArray(this.deco_data) && this.deco_data.length > 0) {
						const [first] = this.deco_data
						this.home_is_show_top_cat = first.type == 'top_cat_nav' ? true : false

						if (this.home_is_show_top_cat) {

						}

					}
				}
			})
		},
		//阻止模态框下页面滚动
		moveHandle() { },
		//获取购物车数据
		getCartNum() {
			if (this.hasLogin) {
				let param = {}
				param.url = 'v3/business/front/cart/cartNum'
				param.method = 'GET'
				param.data = {}
				// param.data.key = this.userInfo.member_access_token;
				this.$request(param)
					.then((res) => {
						if (res.state == 200) {
							if (res.data > 0) {
								uni.setTabBarBadge({
									index: 3,
									text: res.data.toString()
								})
							} else {
								uni.hideTabBarRedDot({
									index: 3
								})
							}
						} else {
							this.$api.msg(res.msg)
						}
					})
					.catch((e) => {
						//异常处理
					})
			} else {
				this.getNoLoginCartNum()
			}
		},
		//获取未登录，购物车数量
		getNoLoginCartNum() {
			let cartNum = 0
			let cart_list = uni.getStorageSync('cart_list')
			if (cart_list && cart_list.storeCartGroupList) {
				cart_list.storeCartGroupList.map((item) => {
					item.promotionCartGroupList.map((item1) => {
						item1.cartList.map((item2) => {
							cartNum++
						})
					})
				})
			}
			if (cartNum > 0) {
				uni.setTabBarBadge({
					index: 3,
					text: cartNum.toString()
				})
			} else {
				uni.hideTabBarRedDot({
					index: 3
				})
			}
		}
	}
}
</script>

<style lang="scss">
.fixed_top_status_bar {
	position: fixed;
	//app-5-start



	//app-5-end
	/* #ifndef APP-PLUS */
	height: 0;
	/* #endif */
	top: 0;
	left: 0;
	right: 0;
	z-index: 99;
	background: #fff;
}

page {
	background: #f5f5f5;
}

.version-panel {
	position: absolute;
	z-index: 1;
	bottom: 10rpx;
	right: 10rpx;
	font-size: 20rpx;
	color: gray;
}

.privacy_policy_model {
	width: 750rpx;
	height: 100vh;
	background: rgba(0, 0, 0, 0.7);
	position: fixed;
	top: 0;
	left: 0;
	z-index: 9999999;
	overflow: hidden;

	.privacy_model {
		width: 625rpx;
		height: 70vh;
		background: #ffffff;
		border-radius: 15rpx;
		margin: 0 auto;
		margin-top: 17vh;
		padding-bottom: 24rpx;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
		justify-content: space-between;

		.privacy_model_title {
			height: 104rpx;
			font-size: 34rpx;
			font-family: Source Han Sans CN;
			font-weight: bold;
			color: #000000;
			line-height: 104rpx;
			text-align: center;
			border-bottom: 1rpx solid #f2f2f2;
		}

		.privacy_policy {
			width: 625rpx;
			height: 50vh;
			padding: 0 43rpx;
			box-sizing: border-box;
		}

		.privacy_model_confirm {
			width: 542rpx;
			height: 72rpx;
			background: linear-gradient(90deg, var(--color_main) 0%, #ff7918 100%);
			border-radius: 36rpx;
			font-size: 28rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: #ffffff;
			line-height: 72rpx;
			text-align: center;
			margin: 20rpx auto;
		}

		.privacy_model_cancel {
			font-size: 28rpx;
			font-family: Source Han Sans CN;
			font-weight: 400;
			color: #ff991e;
			text-align: center;
			line-height: 28rpx;
		}
	}
}
</style>