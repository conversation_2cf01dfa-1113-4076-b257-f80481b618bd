<template>
  <view class="container" :style="mix_diyStyle">
    <!-- #ifdef MP-->
    <uni-nav-bar fixed="true" fixedTop color="#fff" status-bar="true" left-icon="back" @clickLeft="$back"
      backgroundColor="transparent" mode="center"></uni-nav-bar>
    <!-- #endif -->
    <!-- #ifndef MP-->
    <uni-nav-bar left-icon="back" :fixed="true" :statusBar="false" backgroundColor="transparent" color="#fff"
      :placeholderView="false" @clickLeft="$back"></uni-nav-bar>
    <!-- #endif -->
    <!-- #ifdef H5-->
    <home-deco :deco_info="deco_data" :topicNotExit="topicNotExit" :is_show_top="false" :topic_name="topic_name"
      :is_from_found="false" :transpar='true' ref="homeDeco"></home-deco>
    <!-- #endif -->
    <!-- #ifdef MP||APP-PLUS -->
    <home-deco :deco_info="deco_data" :topicNotExit="topicNotExit" :is_show_top="false" :topic_name="topic_name"
      :is_from_found="true" :transpar='true' ref="homeDeco"></home-deco>
    <!-- #endif -->
  </view>
</template>

<script>

import HomeDeco from '@/components/homeDeco/home_deco.vue'
import uniNavBar from '@/components/uni-nav-bar/uni-nav-bar.vue'


// #ifdef MP
const menu = uni.getMenuButtonBoundingClientRect()
// #endif

const system = uni.getSystemInfoSync()

export default {
  data() {
    return {
      deco_data: [],
      topic_id: '', //专题id
      topic_name: '', //专题名称
      client: '', //客户端类型
      topicNotExit: false,
      topicType: 'topic'
    }
  },
  components: {
    HomeDeco,
    uniNavBar
  },
  computed: {
    // #ifdef MP
    middleHeight() {
      return `${menu.height + 10}`
    },
    middleTopHeight() {
      return `${Number(menu.top)}`
    },
    topHeight() {
      return system.statusBarHeight + 'px'
    },
    // #endif
  },
  async onLoad(options) {
    await this.$handleFx()
    setTimeout(() => {
      uni.setNavigationBarTitle({
        title: this.$L('专题')
      })
    }, 0);
    if (option.type && option.type == 'o2o_topic') {
      this.topicType = 'o2o_topic'
    }
    this.topic_id = this.$Route.query.id
    this.loadData(this.topic_id)

  },
  onShow() {

  },

  onPageScroll(res) {
    this.$refs.homeDeco.onScroll(res)
  },
  methods: {
    /**
     * 请求静态数据只是为了代码不那么乱
     * 分次请求未作整合
     */
    async loadData(id) {
      uni.showLoading({
        title: '加载中...'
      })
      let param = {}
      param.url = 'v3/system/front/deco/special?decoId=' + id + '&type=' + this.topicType
      param.method = 'GET'
      this.$request(param).then((res) => {
        if (res.state == 200) {



          uni.hideLoading()
          if (res.data == null) {
            this.topicNotExit = true
            this.deco_data = []
            return
          }
          if (res.data.data != '') {


            let deco_da = JSON.parse(res.data.data)
            deco_da.forEach(item => {
              if (item.type == 'tuijianshangpin') {
                let info = item.data.info
                item.data.info = []
                let divisor
                let remainder
                let shopNum = 0
                let num = 1
                let timer = null
                this.deco_data = deco_da
                if (info.length > 10) {
                  divisor = Math.ceil(info.length / 10)
                  remainder = info.length % 10
                  timer = setInterval(() => {
                    if (num > divisor) {
                      clearInterval(timer)
                    } else {
                      let addNum = remainder != 0 && num == divisor ? remainder : 10;
                      let obj = []
                      for (var i = shopNum; i < shopNum + addNum; i++) {
                        item.data.info.push(info[i])
                      }
                      this.deco_data = deco_da
                      num++
                      shopNum = shopNum + 10
                    }
                  }, 100)
                } else {
                  item.data.info.push(...info)
                  this.deco_data = deco_da
                }
              }
            })
            this.deco_data = deco_da
          } else {
            this.deco_data = []
          }
          this.topic_name = res.data.name

		  // 微信分享
          // const share_url = window.location.href;
          // const share_content = {
          //   title: this.topic_name,
          //   desc: "精选15+热门课程解读优惠 火爆双11！",
          //   link: share_url,
          //   imgUrl: "https://sg-mall-prod.oss-cn-shanghai.aliyuncs.com/resources/images/wx/SG_MALL_1111.jpg",
          // }
          // console.log('share:', share_content)
          // this.$weiXinBrowerShare(1, share_content);
          
          uni.setNavigationBarTitle({
            title: this.topic_name
          })
          uni.hideLoading()
        }
      })
    }
  }
}
</script>

<style lang="scss"></style>
