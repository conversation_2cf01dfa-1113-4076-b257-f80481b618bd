<template>
	<view :style="mix_diyStyle">
		<block v-if='addressList.length'>
			<view class="content">
				<view class="address_list">
					<view v-for="(item, index) in addressList" :key="index" @click="checkAddress(item)"
						:class="{ 'list': true, 'b-b': index != addressList.length - 1, disabled: !item.deliver }">
						<view class="wrapper flex_row_start_center">
							<text v-if="source != 2 && sourceId == item.addressId" class="iconfont iconziyuan33"></text>
							<view class="flex_column_start_start">
								<view class="u-box flex_row_start_center">
									<view class="tag" v-if="item.addressId == defaultAddressId">默认</view>
									<text class="name">{{ item.memberName }}</text>
									<text class="mobile">{{ item.telMobile }}</text>
								</view>
								<view class="address-box">
									<text class="address">{{ item.addressAll }} {{ item.detailAddress }}</text>
								</view>
							</view>
						</view>

						<view class="operate_con flex_row_between_center">
							<view class="set_default flex_row_start_center" @click.stop="setDefault(item, index)">
								<text class="iconfont iconziyuan33" v-if="item.addressId == defaultAddressId"></text>
								<text v-else-if="sourceId" class="iconfont iconziyuan43"></text>
								<text>默认</text>
							</view>

							<view class="op_left flex_row_end_center">
								<view class="op_left_item flex_row_start_center" @click.stop="operateAddress('edit', item.addressId)">
									<image :src="imgUrl + 'yixuan-selectAddress/edit_address.png'" mode=""></image>
									<text>编辑</text>
								</view>

								<view class="op_left_item flex_row_start_center" @click.stop="delAddress(item.addressId)">
									<image :src="imgUrl + 'yixuan-selectAddress/del_address.png'" mode=""></image>
									<text>删除</text>
								</view>
							</view>
						</view>
					</view>
				</view>
				<view class="add_btn_bottom flex_row_center_center">
					<button class="add_btn flex_row_center_center" @click="operateAddress('add')">+
						{{ $L('新建收货地址') }}</button>
				</view>
				<loadingState v-if="loadingState == 'first_loading' || addressList.length > 0" :state='loadingState' />
				<uni-popup ref="popup" type="dialog">
					<uni-popup-dialog type="input" :before-close="true" :title="$L('提示')" :content="popTip" :duration="2000"
						@close="cancelChange" @confirm="confirmChange"></uni-popup-dialog>
				</uni-popup>
			</view>
		</block>
		<block v-if="!addressList.length && loadingState != 'first_loading'">
			<view class="flex_column_start_center empty_part">
				<image class="img" :src="imgUrl + 'empty_address.png'" />
				<text class="tip_con">{{ $L('还没有收货地址哦') }}~</text>
				<view class="ope_btn flex_row_center_center" @click="operateAddress('add')">
					{{ $L('新建地址') }}
				</view>
			</view>
		</block>
	</view>
</template>

<script>
import loadingState from "@/components/loading-state.vue";
import uniPopup from '@/components/uni-popup/uni-popup.vue'
import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue'
import {
	mapState,
	mapMutations
} from 'vuex';
export default {
	components: {
		loadingState,
		uniPopup,
		uniPopupDialog
	},
	data () {
		return {
			imgUrl: process.env.VUE_APP_IMG_URL,
			source: 0,
			sourceId: '', //下单页面选中的地址addressId
			curOperateId: '',
			loadingState: 'first_loading',
			orderSn: '', //订单号
			currentAddressId: '', //记录当前长按点击的地址
			editing: false,
			addressId: 0,
			popTip: '',
			popType: '',
			defaultAddressId: 0,
			addressList: []
		}
	},
	onLoad (option) {
		let title = this.$L('我的地址')
		setTimeout(() => {
			uni.setNavigationBarTitle({ title })
		}, 0);

		this.orderSn = this.$Route.query.orderSn;
		this.source = this.$Route.query.source ? this.$Route.query.source : 0; // source ：3 从订单详情页的修改地址进入的    2：从订单列表的修改地址进入    1：从确认下单页面的修改地址进入的

		if (this.$Route.query.sourceOrderAddress) {
			this.sourceOrderAddress = decodeURIComponent(this.$Route.query.sourceOrderAddress);
		}

		if (this.$Route.query.sourceId) {
			this.sourceId = this.$Route.query.sourceId;
		}

		this.getAddressList();
	},
	computed: {
		...mapState(['userInfo'])
	},
	//滚动事件地址蒙层
	onPageScroll (e) {
	},
	methods: {
		...mapMutations(['operateAddressData']),
		//获取地址列表
		getAddressList () {

			let data = {}

			if (this.$Route.query.storeId) {
				data.storeId = this.$Route.query.storeId
			}

			this.$request({
				url: 'v3/member/front/memberAddress/list',
				method: 'GET',
				data
			}).then(res => {
				if (res.state == 200) {
					this.operateAddressData(res.data.list);

					if (data.storeId) {
						res.data.list = res.data.list.filter(i => i.deliver)
					}

					const default_addr = res.data.list.find(item => item.isDefault == 1)
					if (default_addr) {
						this.defaultAddressId = default_addr.addressId
					} else {
						this.defaultAddressId = 0
					}
					this.addressList = res.data.list
					res.data.list.forEach(item => {
						if (this.sourceOrderAddress) {
							let orderaddress = this.sourceOrderAddress.split(',')
							if (item.detailAddress == orderaddress[0] && item.telMobile == orderaddress[1]) {
								this.sourceId = item.addressId
							}
						}
					})

				} else {
					this.$api.msg(res.msg);
				}
				this.loadingState = 'complete';
			}).catch((e) => {
				//异常处理
			})
		},
		cancelChange () {
			this.editing = false
			this.$refs.popup.close()
		},
		confirmChange () {
			if (this.inComfirm) {
				return;
			}
			this.$refs.popup.close()
			if (this.popType == 'edit') {
				this.inComfirm = true;
				this.$request({
					url: 'v3/business/front/orderOperate/updateAddress',
					data: {
						orderSn: this.orderSn,
						addressId: this.addressId,
					},
					method: 'POST'
				}).then(res => {
					if (res.state == 200) {
						if (this.source == 3) { //从订单详情页的修改地址进入的
							this.$api.msg(res.msg);
							setTimeout(() => {
								this.$api.prePage().getOrderDetail();
								this.$Router.back(1)
								this.editing = false
								uni.setStorageSync('addressId', this.addressId)
							}, 1500)
						} else if (this.source == 2) { //从订单列表的修改地址进入的
							this.$api.msg(res.msg);
							setTimeout(() => {
								this.$Router.back(1)
								this.editing = false
							}, 1500)
						}
					} else {
						this.inComfirm = false
						this.editing = false
						this.$api.msg(res.msg);
					}
				})
			} else if (this.popType == 'del') {
				this.operateAddressData(this.addressList);
				this.$request({
					url: 'v3/member/front/memberAddress/delete',
					data: {
						addressIds: this.addressId,
					},
					method: 'POST'
				}).then(res => {
					this.$api.msg(res.msg);
					if (res.state == 200) {
						//更新数据
						let tmp_data_index = this.addressList.findIndex(item => item.addressId == this.addressId)
						this.addressList.splice(tmp_data_index, 1)
						this.operateAddressData(this.addressList);
					}
				})
			}
		},


		//选择地址
		checkAddress (item) {
			if (this.editing) {
				return
			}
			if (this.source == 1) {
				// this.$api.prePage().orderAddress=item
				this.$api.prePage() && this.$api.prePage().changeAddress(item);
				this.$Router.back(1)
			} else if (this.source == 2 || this.source == 3) {
				this.editing = true
				//从订单详情或订单列表里面进入的地址列表
				this.addressId = item.addressId
				this.popTip = '确认修改地址?'
				this.popType = 'edit'
				this.$refs.popup.open()
			}
		},
		operateAddress (type, addressId) {

			if (type == 'add') {
				if (this.addressList.length >= 20) {
					this.$api.msg('最多添加20个收货地址')
					return
				}
			}

			this.curOperateId = '';
			let query = {
				type,
				...this.$Route.query
			}
			if (type == 'edit') {
				query.addressId = addressId
			}

			this.$Router.push({
				path: '/pages/address/operate',
				query
			})
		},

		//删除地址事件
		delAddress (addressId) {
			this.popTip = '确定删除地址?'
			this.popType = 'del'
			this.addressId = addressId
			this.$refs.popup.open()
		},

		//设置默认地址
		setDefault (item, index) {
			this.$request({
				url: 'v3/member/front/memberAddress/changeDefaultAddress',
				method: 'POST',
				data: {
					addressId: item.addressId,
					isDefault: this.defaultAddressId == item.addressId ? 0 : 1
				}
			}).then(res => {
				if (res.state == 200) {
					if (this.defaultAddressId == item.addressId) {
						this.defaultAddressId = ''
					} else {
						this.defaultAddressId = item.addressId
					}
				} else {
					this.$api.msg(res.msg)
				}
			})
		}
	}
}
</script>

<style lang='scss'>
page {
	width: 750rpx;
	margin: 0 auto;
	background: #F5F5F5;
	-webkit-touch-callout: none;
	/*系统默认菜单被禁用*/
	-webkit-user-select: none;
	/*webkit浏览器*/
	-khtml-user-select: none;
	/*早期浏览器*/
	-moz-user-select: none;
	/*火狐*/
	-ms-user-select: none;
	/*IE10*/
	user-select: none;
	-webkit-touch-callout: none;
	-moz-touch-callout: none;
	-ms-touch-callout: none;
	touch-callout: none;
}

.disabled {
	position: relative;

	&:after {
		position: absolute;
		inset: 0;
		z-index: 20;
		background-color: rgba(255, 255, 255, .6);
	}
}

uni-page-body {
	display: flex;
	height: 100%;
}

.content {
	position: relative;
	/* background: #fff; */
	margin-top: 20rpx;
	width: 750rpx;
	padding-bottom: 148rpx;
}

.address_list {}

.list {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	justify-content: flex-start;
	background: #fff;
	position: relative;
	margin-bottom: 20rpx;

	&.b-b {
		&:after {
			position: absolute;
			z-index: 3;
			left: 20rpx;
			right: 0;
			height: 0;
			content: '';
			-webkit-transform: scaleY(0.5);
			transform: scaleY(0.5);
			border-bottom: 1px solid rgba(0, 0, 0, .1);
		}
	}

	.mask {
		position: absolute;
		z-index: 4;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		background: rgba(0, 0, 0, .6);

		view {
			width: 166rpx;
			height: 90rpx;
			border-radius: 45rpx;
			color: #fff;
			font-size: 34rpx;

			&.edit {
				background: linear-gradient(-90deg, rgba(254, 152, 32, 1), rgba(255, 183, 43, 1));
			}

			&.del {
				background: linear-gradient(-90deg, rgba(252, 29, 28, 1), rgba(255, 122, 24, 1));
				margin-left: 80rpx;
			}
		}

	}
}

.wrapper {
	flex: 1;
	background: #fff;
	padding: 30rpx 30rpx;

	.iconfont {
		color: var(--color_main);
		font-size: 32rpx;
		margin-right: 30rpx;
	}
}

.address-box {
	display: flex;
	align-items: center;
	margin-top: 10rpx;

	.address {
		font-size: 28rpx;
		color: $main-font-color;
		line-height: 38rpx;
		margin-top: 5rpx;
		word-break: break-all;
	}
}

.u-box {
	font-size: 30rpx;
	color: $font-color-light;
	color: $main-font-color;
	font-weight: bold;

	.name {
		margin-right: 40rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		max-width: 240rpx;
	}

	.tag {
		width: 63rpx;
		height: 32rpx;
		margin-right: 20rpx;
		background: var(--color_vice);
		line-height: 32rpx;
		text-align: center;
		font-size: 24rpx;
		color: #fff;
		border-radius: 4rpx
	}

	.mobile {
		color: #999999;
		font-weight: 500;
		font-size: 26rpx;
	}
}

.icon-bianji {
	display: flex;
	align-items: center;
	height: 80rpx;
	font-size: 40rpx;
	color: $font-color-light;
	padding-left: 30rpx;
}

.add_btn_bottom {
	position: fixed;
	width: 750rpx;
	height: 168rpx;
	bottom: 0;
	padding: 40rpx 0;
	margin: 0 auto;
	z-index: 95;

	.add_btn {
		width: 664rpx;
		font-size: 34rpx;
		color: #fff;
		height: 88rpx;
		background: var(--color_main_bg);
		border-radius: 44rpx;
		letter-spacing: 1rpx;
	}
}

.empty_part {
	display: flex;
	flex: 1;
	width: 750rpx;
	height: 99vh;
	background: #fff;
	padding-top: 150rpx;

	.img {
		width: 380rpx;
		height: 280rpx;
		margin-bottom: 37rpx;
		margin-top: 88rpx;
	}

	.tip_con {
		color: $main-third-color;
		font-size: 26rpx;
	}

	.ope_btn {
		color: var(--color_main);
		font-size: 28rpx;
		padding: 0 25rpx;
		height: 54rpx;
		background: var(--color_halo);
		border-radius: 27rpx;
		margin-top: 20rpx;
	}
}

.operate_con {
	width: 100%;
	border-top: 1px solid #eee;
	padding: 30rpx 30rpx;
	font-size: 24rpx;
	font-family: PingFang SC;
	font-weight: 400;
	color: #333333;

	.set_default {
		image {
			width: 30rpx;
			height: 30rpx;
			margin-right: 10rpx;
		}

		.iconfont {
			font-size: 30rpx;
			margin-right: 10rpx;
			color: #999;

			&.iconziyuan33 {
				color: var(--color_main);
			}
		}
	}

	.op_left_item {
		margin-left: 30rpx;

		image {
			width: 30rpx;
			height: 30rpx;
			margin-right: 10rpx;
		}
	}
}
</style>
