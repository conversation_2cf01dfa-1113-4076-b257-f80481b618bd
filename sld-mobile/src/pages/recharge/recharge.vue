<!-- 账号充值 -->
<template>
	<view class="container" :style="mix_diyStyle">
		<block v-if="rechargeEnable">
			<block v-if="payMethod.length">
				<view v-if="!rechargeSn" class="amount_part flex_column_start_start">
					<text class="title">{{ $L('充值金额') }}</text>
					<view class="amount_view flex_row_start_start">
						<text :class="{num: true,flex_row_center_center: true,hasSelAmout: Number(amount) == item}"
							v-for="(item, index) in amountList" :key="index"
							@click="selAmount(item)">{{ item }}{{ $L('元') }}</text>
						<input
							:class="{num: true,flex_row_center_center: true,input_amount: true,hasSelAmout: !amount && input_amount}"
							:placeholder="$L('请输入金额')" v-model="input_amount" placeholder-class="input_placeholder"
							maxlength="5" @focus="selSetAmount" @blur="handleBlur" type="digit" />
					</view>
				</view>
				<view class="pay_part flex_column_start_start">
					<text class="title">{{ $L('选择充值方式') }}</text>
					<view v-for="(item, index) in payMethod" :key="index" @click="selectPayMethod(item)"
						:class="{item: true,b_b: index < payMethod.length - 1,flex_row_between_center: true}">
						<view class="left flex_row_start_center">
							<image class="pay_icon" :src="payIcon[item.payMethod]" />
							<text class="tit">{{ item.payMethodName }}</text>
						</view>
						<text
							:class="{iconfont: true,iconziyuan33: selData.payMethod == item.payMethod,iconziyuan43: selData.payMethod != item.payMethod,has_sel: selData.payMethod == item.payMethod}"></text>
					</view>
				</view>
				<view v-show="showBtn" class="btn_recharge flex_row_center_center" @click="reCharge">{{ $L('确认充值') }}
				</view>
			</block>
			<block v-else>
				<view class="no_payMth1">
					<image :src="imgUrl + 'store/no_content.png'"></image>
					<text>{{ $L('暂无支付方式') }}</text>
				</view>
			</block>
		</block>

		<block v-else>
			<view class="no_payMth1">
				<image :src="imgUrl + 'store/no_content.png'"></image>
				<text>充值功能已关闭</text>
			</view>
		</block>

	</view>
</template>

<script>
// #ifdef H5 
import { getWxH5Appid } from '@/static/h5/wxH5Auth.js';
// #endif
	import {
mapState
} from 'vuex';
	export default {
		data() {
			return {
				rechargeSn: '',
				selData: {},
				payMethod: [], //支付方式
				amount: 0,
				amountList: [50, 100, 200, 500, 1000],
				input_amount: '',
				balance: 0, //账户总金额
				client: 'wxbrowser', //支付发起来源 pc==pc,mbrowser==移动设备浏览器,app==app,wxxcx==微信小程序,wxbrowser==微信内部浏览器
				isAllowAutoPay: true, //当浏览器地址有code时，是否允许自动支付，如果支付失败的话置为false
				wxBrowerCode: '', //微信浏览器支付的code
				payMethodType: '', //支付发起来源，上个页面 rechargeBalance:用户充值	rechargeDetail：充值详情
				rechargeId: '', //从充值详情过来的，充值id
				showBtn: true, // 输入金额键盘弹出时隐藏确认按钮
				windowHeight: '', // 屏幕高度判断键盘弹出收起,
				isClick: true,
				imgUrl: process.env.VUE_APP_IMG_URL,
				rechargeEnable: false, //充值开关
				payIcon:{
					balance:process.env.VUE_APP_IMG_URL +`pay/balance_pay_icon.png`,
					wx:process.env.VUE_APP_IMG_URL +`pay/wx_pay_icon.png`,
					alipay:process.env.VUE_APP_IMG_URL +`pay/alipay_pay_icon.png`,
					paypal:process.env.VUE_APP_IMG_URL +`pay/paypal_pay_icon.png`,
				}
				
			}
		},
		computed: {
			...mapState([
				'hasLogin', 'userInfo',
				])
		},
		onLoad(options) {
			setTimeout(() => {
				uni.setNavigationBarTitle({
					title: this.$L('账户充值')
				})
			}, 0);

			if (this.$Route.query.rechargeSn) {
				this.rechargeSn = this.$Route.query.rechargeSn
			}
			if (this.$Route.query.payMethodType) {
				this.payMethodType = this.$Route.query.payMethodType
			}
			if (this.$Route.query.rechargeId) {
				this.rechargeId = this.$Route.query.rechargeId
			}
			//处理差额参数
			if (this.$Route.query.diffAmount) {
				this.input_amount = this.$Route.query.diffAmount
			} else {
				this.amount = 50
			}
			
			this.initClient()
			this.getOutputSet()
			
			// #ifdef H5
			//判断code地址的参数 start
			let code = this.$getQueryVariable('code')
			if (code) {
				this.wxBrowerCode = code
				if (this.$Route.query.type == 'sel') {
					this.amount = this.$Route.query.amount
				} else if (this.$Route.query.type == 'input') {
					this.input_amount = this.$Route.query.amount
				}
				let oriUrl = window.location.origin + '/pages/recharge/recharge'
				let tmp_data = `?balance=${this.$Route.query.balance}&payMethodType=rechargeBalance`
				oriUrl += tmp_data
				if (this.client == 'wxbrowser') {
					//微信浏览器的话要把浏览器地址里面的code去掉
					history.replaceState({}, '', oriUrl)
				}
			} else {
				this.amount = 50
			}
			//判断code地址的参数 end
			// #endif
			
			this.balance = parseFloat(this.$Route.query.balance) || 0
			this.getPayMethod()
			
			// 手机软键盘收起时显示确认按钮
			uni.getSystemInfo({
				success: (res) => {
					this.windowHeight = res.windowHeight
				}
			})
			
			// #ifdef H5
			uni.onWindowResize((res) => {
				if (res.size.windowHeight < this.windowHeight) {
					this.showBtn = false
				} else {
					this.showBtn = true
				}
			})
			// #endif
		},

		// #ifdef H5
		onBackPress() {
			if (this.$isWeiXinBrower()) {
				if (window.history.length > 3) {
					window.history.go(-2)
				} else {
					window.history.back()
				}
				return true
			}
		},
		// #endif

		methods: {
			//初始化终端类型
			initClient() {
				//app-1-start



				//app-1-end
				//#ifdef H5
				this.client = this.$isWeiXinBrower() ? 'wxbrowser' : 'mbrowser'
				//#endif
				//wx-1-start
				//#ifdef MP-WEIXIN
				this.client = 'wxxcx'
				//#endif
				//wx-1-end











			},

			//获取支付方式
			getPayMethod() {
				let {
					client,
					wxBrowerCode,
					isAllowAutoPay
				} = this
				this.$request({
					url: 'v3/system/common/payMethod',
					data: {
						source: client,
						type: 2 //支付发起类型 1==下单支付，2==余额充值/订单列表
					}
				}).then((res) => {
					if (res.state == 200) {
						
						this.payMethod = res.data
						if (!this.payMethod.length) {
							return
						}
						if (!wxBrowerCode) {
							this.selData = this.payMethod[0]
						} else {
							//有code的话要默认选中微信支付，并直接提交订单
							this.selData = this.payMethod.find((item) => item.payMethod.indexOf('wx')>-1)
							if (isAllowAutoPay) {
								this.reCharge()
							}
						}
					}
				})
			},
			//选择支付方式事件
			selectPayMethod(val) {
				let that = this
				that.selData = val
				this.isClick = true
			},
			//确认充值事件
			reCharge() {
				const {
					selData,
					rechargeSn,
					amount,
					input_amount,
					wxBrowerCode,
					client
				} = this
				let _this = this
				if (!this.isClick) {
					return false
				}
				this.isClick = false
				uni.showLoading()
				let param = {}
				param.method = 'POST'
				param.data = {}
				if (rechargeSn) {
					param.url = 'v3/member/front/balanceRecharge/rechargeContinue'
					param.data.rechargeSn = rechargeSn
				} else {
					param.url = 'v3/member/front/balanceRecharge/recharge'
					param.data.amount = amount || input_amount
					if (!param.data.amount) {
						this.$api.msg(_this.$L('请设置充值金额'))
						return false
					}
				}
				param.data.payType = selData.payType
				param.data.payMethod = selData.payMethod

				if (client == 'wxxcx') {
					//微信小程序支付
					uni.login({
						success: (code) => {
							param.data.code = code.code
							param.data.codeSource = 1 //用户code来源（JSAPI支付时必填）：1==小程序，2==微信内部浏览器
							this.$request(param).then((res) => {
								if (res.state == 200) {
									uni.hideLoading()
									this.$sldStatEvent({
										behaviorType: 'rechargeApply',
										rechargeSn: res.data.rechargeSn
									})
									let tmp_data = res.data.payData
									if (res.data.actionType == null) {
										//微信小程序支付
										uni.requestPayment({
											timeStamp: tmp_data.timeStamp,
											nonceStr: tmp_data.nonceStr,
											package: tmp_data.packageValue?tmp_data.packageValue:tmp_data.package,
											signType: 'MD5',
											paySign: tmp_data.paySign,
											success: function(res) {
												_this.payTip('success')
											},
											fail: function(res) {
												_this.payTip('fail')
											}
										})
									}
								} else {
									this.isClick = true
									this.$api.msg(res.msg)
									uni.hideLoading()
								}
							})
						}
					})
					return false
				} else if (client == 'wxbrowser') {
					//微信h5支付
					if (!wxBrowerCode) {
						let link = location.href
						let param = ''
						if (rechargeSn) {
							param = 'type=continue'
						} else {
							if (amount) {
								param = 'type=sel'
							}
							if (input_amount) {
								param = 'type=input'
							}
							param += '&amount=' + (amount || input_amount)
						}
						if (link.indexOf('?') > -1) {
							link += `&${param}`
						} else {
							link += `?${param}`
						}
						uni.hideLoading()
						// let uricode = encodeURIComponent(link)
						console.log('wxh5:',link)
						getWxH5Appid(link)
						return false
					} else {
						param.data.code = wxBrowerCode
						param.data.codeSource = 2 //用户code来源（JSAPI支付时必填）：1==小程序，2==微信内部浏览器
					}
				}
				console.log('recharge:',param)
				this.$request(param)
					.then((res) => {
						if (res.state == 200) {
							uni.hideLoading()
							this.$sldStatEvent({
								behaviorType: 'rechargeApply',
								rechargeSn: res.data.rechargeSn
							})
							let tmp_data = res.data.payData
							if (res.data.actionType == 'redirect') {
								window.location.href = tmp_data
							} else if (res.data.actionType == null) {
								if (client == 'wxbrowser') {
									
									//微信h5支付
									this.$weiXinBrowerPay({
										timestamp: tmp_data.timeStamp,
										nonceStr: tmp_data.nonceStr,
										package: tmp_data.packageValue?tmp_data.packageValue:tmp_data.package,
										signType: 'MD5',
										paySign: tmp_data.paySign,
										appId: tmp_data.appId, //此参数可不用
										success: function(r) {
											if (r.errMsg == 'chooseWXPay:ok') {
												_this.payTip('success')
											} else {
												_this.isAllowAutoPay = false //支付失败后禁止自动支付
												_this.payTip('fail')
											}
										},
										cancel: function(r) {
											_this.isAllowAutoPay = false //支付失败后禁止自动支付
											_this.wxBrowerCode = ''
											_this.payTip('fail')
											_this.isClick = true
										}
									})
								} else if (client == 'wxxcx') {
									//微信小程序支付
									uni.requestPayment({
										timeStamp: tmp_data.timeStamp,
										nonceStr: tmp_data.nonceStr,
										package: tmp_data.packageValue?tmp_data.packageValue:tmp_data.package,
										signType: 'MD5',
										paySign: tmp_data.paySign,
										success: function(res) {
											_this.payTip('success')
										},
										fail: function(res) {
											_this.payTip('fail')
											_this.isClick = true
										}
									})
								} else if (client == 'app') {
									//APP支付
									let provider = ''
									let orderInfo = {}
									if (selData.payMethod == 'wx') {
										provider = 'wxpay'
										orderInfo.appid = tmp_data.appId
										orderInfo.noncestr = tmp_data.nonceStr
										orderInfo.package = tmp_data.packageValue?tmp_data.packageValue:tmp_data.package
										orderInfo.partnerid = tmp_data.partnerId
										orderInfo.prepayid = tmp_data.prepayId
										orderInfo.timestamp = tmp_data.timeStamp
										orderInfo.sign = tmp_data.sign
									} else if (selData.payMethod == 'alipay') {
										provider = 'alipay'
									}
									uni.requestPayment({
										provider: provider,
										orderInfo: provider == 'alipay' ? res.data.payData : orderInfo, //订单数据
										success: function(res) {
											_this.payTip('success')
										},
										fail: function(err) {
											_this.payTip('fail')
											_this.isClick = true
										}
									})
								}
							} else if (res.data.actionType == 'autopost') {
								document.write(res.data.payData)
							} else if (res.data.actionType == 'native') {
								this.isClick = true
								// #ifdef H5
								window.location.href = res.data.payData
								// #endif
								// #ifdef APP-PLUS ||MP-WEIXIN
								this.$Router.push({
									path: '/pages/index/skip_to',
									query: {
										url: res.data.payData
									}
								})
								// #endif
							}
						} else {
							this.isClick = true
							this.$api.msg(res.msg)
							uni.hideLoading()
						}
					})

			},

			//支付操作完成提示
			payTip(type) {
				if (type == 'success') {
					this.isClick = true
					//提示充值成功，如果来自账户充值页面，直接跳转充值明细列表页面，否则返回上一级页面（充值详情页面），并更新数据	rechargeBalance:用户充值		rechargeDetail：充值详情
					this.$api.msg(this.$L('充值成功'))
					
					// #ifdef H5
					if (this.client == 'wxbrowser') {
						//获取fromurl并跳转
						let fromUrl = uni.getStorageSync('fromurl')
						if(fromUrl) {
							uni.removeStorageSync('fromurl')
							this.$Router.replace({
								path: fromUrl.url,
								query: fromUrl.query
							})
							return
						}
						if (window.history.length > 3) {
							window.history.go(-2)
						} else {
							window.history.back()
						}
						return
					}
					// #endif
					
					//获取fromurl并跳转
					let fromUrl = uni.getStorageSync('fromurl')
					if(fromUrl) {
						uni.removeStorageSync('fromurl')
						this.$Router.replace({
							path: fromUrl.url,
							query: fromUrl.query
						})
						return
					}
					
					if (this.payMethodType == 'rechargeBalance') {
						this.$Router.replace('/pages/recharge/list')
					} else if (this.payMethodType == 'rechargeDetail') {
						const pages = getCurrentPages() //当前页面栈
						setTimeout(() => {
							this.$Router.back(1)
						}, 1000)
					}
				} else if (type == 'fail') {
					//提示充值失败 刷新界面
					this.$api.msg(this.$L('充值失败,请重试～'))
					this.isClick = true
					this.getPayMethod()
				}
			},

			//选择充值金额
			selAmount(amount) {
				this.input_amount = ''
				this.amount = amount
			},

			//设置金额聚焦事件
			selSetAmount() {
				this.amount = ''
				this.showBtn = false
				this.isClick = true
			},
			//输入金额
			handleBlur(e) {
				this.showBtn = true
				let val = parseFloat(e.detail.value.toString())
				if (val > 5000) {
					this.$api.msg(this.$L('一次最多充值5000元'))
					this.input_amount = 5000
				} else {
					if (this.balance + val > 999999.0) {
						this.$api.msg(this.$L('当前账号余额已达最大值'))
					} else if (val <= 0) {
						this.$api.msg(this.$L('充值金额不能为负数或者为零'))
						this.input_amount = ''
					} else if (Number.isNaN(val)) {
						this.input_amount = ''
					} else {
						//小数点后最多后两位
						this.input_amount =
							val.toString().indexOf('.') == -1 ?
							val :
							val.toString().substring(0, val.toString().indexOf('.') + 3) > 0 ?
							val.toString().substring(0, val.toString().indexOf('.') + 3) :
							''
						if (this.input_amount == '') {
							this.$api.msg(this.$L('充值金额不能为负数或者为零'))
						}
					}
				}
			},
			//获取配置开关
			getOutputSet() {
				let param = {}
				param.url = 'v3/system/front/setting/getSettings'
				param.method = 'GET'
				param.data = {}
				param.data.names = 'recharge_is_enable'
				this.$request(param).then((res) => {
					if (res.state == 200) {
						this.rechargeEnable = res.data[0] == '1' ? true : false
					} else {
						this.$api.msg(res.msg)
					}
				})
			},
		}
	}
</script>

<style lang="scss">
	page {
		background: $bg-color-split;
		width: 750rpx;
		margin: 0 auto;
	}

	uni-page-body {
		display: flex;
		height: 100%;
	}

	.no_payMth1 {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 100%;
		padding-top: 30%;

		image {
			width: 190rpx;
			height: 190rpx;
		}

		text {
			color: #999;
			margin-top: 20rpx;
		}
	}

	.container {
		display: flex;
		flex-direction: column;
		flex: 1;

		.amount_part {
			margin-top: 20rpx;
			width: 750rpx;
			height: 400rpx;
			background: #fff;

			.title {
				color: $main-font-color;
				font-size: 32rpx;
				line-height: 36rpx;
				margin: 30rpx 0 10rpx 20rpx;
			}

			.amount_view {
				flex-wrap: wrap;

				.num {
					width: 223rpx;
					height: 129rpx;
					background: rgba(255, 255, 255, 1);
					box-shadow: 0px 0px 20rpx 0px rgba(153, 153, 153, 0.2);
					border-radius: 15rpx;
					color: $main-font-color;
					font-size: 36rpx;
					margin: 20rpx 0 0 20rpx;
					line-height: 129rpx;

					&.hasSelAmout {
						border: 1rpx solid var(--color_main);
					}
				}

				.input_placeholder {
					color: $main-third-color;
					font-size: 24rpx;
					text-align: center;
				}

				.input_amount {
					text-align: center;
				}
			}
		}

		.pay_part {
			margin-top: 20rpx;
			background: #fff;
			flex: 1;

			.title {
				color: $main-font-color;
				font-size: 32rpx;
				margin-top: 30rpx;
				margin-left: 20rpx;
			}

			.item {
				width: 100%;
				padding: 20rpx;
				position: relative;

				.left {
					.pay_icon {
						width: 80rpx;
						height: 80rpx;
					}

					.tit {
						color: $main-font-color;
						font-size: 28rpx;
						margin-left: 20rpx;
					}
				}

				.iconfont {
					color: $main-third-color;
					font-size: 32rpx;
				}

				.has_sel {
					color: var(--color_main);
				}

				&.b_b:after {
					left: 20rpx;
				}
			}
		}

		.btn_recharge {
			width: 670rpx;
			margin: 0 40rpx;
			height: 88rpx;
			background: var(--color_main_bg);
			box-shadow: 0px 3rpx 20rpx 0rpx var(--color_halo);
			border-radius: 44rpx;
			color: #fff;
			font-size: 36rpx;
			position: fixed;
			left: 50%;
			transform: translateX(-375rpx);
			bottom: 40rpx;
			// top: calc(100vh - 88rpx);
			// top: 1200rpx;
		}
	}
</style>