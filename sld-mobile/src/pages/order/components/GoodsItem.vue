<template>
	<view class="product_con">
		<view class="g-item flex_row_start_start">
			<view class="image_con">
				<view class="virtual_tag" v-if="isVirtual == 2">{{ $L('虚拟') }}</view>
				<view class="image" :style="{ backgroundImage: 'url(' + product.image + ')' }"></view>
			</view>
			<view class="right flex_column_between_start">
				<view class="flex_column_start_start">
					<text class="title">{{ product.goodsName }}</text>
				</view>
				<view class="goods_item_specs">
					<text class="goods_item_spec" v-if="product.specValues">{{product.specValues}}</text>
					<text class="goods_item_buynum">*{{ product.buyNum }}</text>
				</view>
				<view
					class="price-box price_wrap_super">
					<text class="unit">¥</text>
					<text class="price_int">{{$getPartNumber(product.price,'int')}}</text>
					<text class="price_decimal" v-if="product.isSupper">
						{{$getPartNumber(product.price,'decimal')}}
						<text class="price_super_img"
							:style="'background-image:url('+imgUrl+'super/super_price_tag.png)'"></text>
					</text>
					<text class="price_decimal" v-else>{{$getPartNumber(product.price,'decimal')}}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default{
		props:['product','isVirtual'],
		data:()=>({
			imgUrl:process.env.VUE_APP_IMG_URL
		})
	}
</script>

<style lang="scss">
	
	.product_con {
		border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
	
		.product_giveaway {
			color: #666666;
			background-color: #f8f8f8;
			padding: 6rpx 10rpx;
			font-size: 24rpx;
			width: fit-content;
			margin-bottom: 20rpx;
			margin-left: 20rpx;
			border-radius: 3px;
		}
		
		
		.g-item {
			display: flex;
			padding: 20rpx 20rpx 20rpx 0;
			margin-left: 20rpx;
			width: calc(750rpx - 20rpx);
		
			.image_con {
				position: relative;
		
				.virtual_tag {
					position: absolute;
					top: 0;
					left: 0;
					background: #e8bc4d;
					color: #fff;
					padding: 4rpx;
					font-size: 24rpx;
					border-radius: 15rpx 0 0 0;
				}
		
				.image {
					flex-shrink: 0;
					display: block;
					width: 220rpx;
					height: 220rpx;
					border-radius: 15rpx;
					background-size: cover;
					background-position: center center;
					background-repeat: no-repeat;
					background-color: #f8f6f7;
				}
			}
		
			.right {
				flex: 1;
				padding: 15rpx 0 15rpx 24rpx;
				overflow: hidden;
				height: 220rpx;
			}
		
			.title {
				font-size: 28rpx;
				color: $main-font-color;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
				word-break: break-word;
				line-height: 40rpx;
			}
		
			.spec {
				font-size: 24rpx;
				color: #949494;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 1;
				-webkit-box-orient: vertical;
				word-break: break-word;
				line-height: 40rpx;
			}
		
			.goods_item_specs {
				display: flex;
				align-items: center;
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #999999;
				line-height: 34rpx;
		
				.goods_item_spec {
					margin-right: 10rpx;
				}
			}
		
			.price-box {
				font-size: 32rpx;
				color: var(--color_price);
				&.price_wrap_super {
					.unit,
					.price_decimal,
					.price_int {
						color: #242846;
					}
				}
		
				.unit {
					font-size: 24rpx;
					font-weight: bold;
				}
		
				.price_int {
					font-size: 34rpx;
					margin-left: 4rpx;
					font-weight: bold;
				}
		
				.price_decimal {
					font-size: 24rpx;
					font-weight: bold;
				}
				
				.price_decimal {
					position: relative;
					
					.price_super_img {
						position: absolute;
						bottom: 4rpx;
						display: inline-block;
						width: 82rpx;
						height: 28rpx;
						line-height: 26rpx;
						color: #CFB295;
						font-size: 20rpx;
						font-family: PingFang SC;
						font-weight: 500;
						text-align: center;
						text-indent: 6rpx;
						background-position: center;
						background-repeat: no-repeat;
						background-size: cover;
						margin-left: 16rpx;
					}
				}
			}
		
			.step-box {
				position: relative;
			}
		}
		//ss
	}
	//ss
	
</style>