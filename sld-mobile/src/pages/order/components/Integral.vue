<template>
	<view v-if="intRuleList[0] == 1 && integralList.length">
		<view class="yt-list-cell b-b" @click="select_integral">
			<view class="cell_int">
				<text class="cell-tit clamp">{{ $L('积分抵现') }}</text>
				<image :src="imgUrl + 'order/int_ques.png'" mode="aspectFit" @click.stop="showIntRule">
				</image>
			</view>
			<text class="cell-tip"
				v-if="integralList.length &&Number(totalAmount) >= Number(intRuleList[2])">
				{{integral > 0? `${$L('积分抵现')}：¥${integral / integralInfo.integralScale}`: `${$L('可用积分')}：${integralInfo.memberIntegral}`}}
			</text>
			<block v-else>
				<text class="cell-tip disabled"
					v-if="Number(totalAmount) * Number(intRuleList[3] * 0.01) <Number(intRuleList[2])">{{ $L('订单金额') }}：{{ totalAmount }}，{{ $L('满')}}{{ Number(intRuleList[2]).toFixed(2) }}{{ $L('可用') }}</text>
				<text class="cell-tip disabled"
					v-else>{{ $L('当前积分') }}：{{ integralInfo.memberIntegral }}，{{ $L('满')}}{{ integralInfo.integralScale }}{{ $L('可用') }}</text>
			</block>
		</view>
		
		<!-- 积分抵现弹框 -->
		<uni-popup ref="integralModel" type="bottom">
			<view class="integral_list_con">
				<view class="integral_top">
					<view class="integral_top_text">{{ $L('积分抵现') }}</view>
					<image :src="imgUrl + 'goods_detail/close.png'" mode="aspectFit" @click="conInt('close')">
					</image>
				</view>
				<view class="member_int">
					<view>{{ $L('可用积分') }}：{{ integralInfo.memberIntegral }}</view>
				</view>
				<scroll-view scroll-y="true" class="integral_list" @touchmove.stop.prevent="moveHandle">
					<radio-group>
						<view v-for="(item, index) in integralList" :key="index" class="list"
							@click="selInt(item)">
							<view class="wrapper flex_row_start_center">
								<view class="flex_column_start_start">
									<view class="integral-box">
										<text class="int_desc">{{ $L('抵扣') }}</text>
										<text class="int_desc"
											style="color: var(--color_main)">¥{{ item / integralInfo.integralScale }}</text>
										<text class="int_desc">{{ $L('使用') }}</text>
										<text class="int_desc" style="color: var(--color_main)">{{item}}</text>
										<text class="int_desc">{{ $L('积分') }}</text>
									</view>
								</view>
							</view>
							<label class="wrapper_right">
								<radio :value="item.toString()" color="var(--color_main)" :checked="item == tmpInt"
									:disabled="item > integralInfo.memberIntegral" />
							</label>
						</view>
					</radio-group>
				</scroll-view>
				<view class="other_integral">
					<view class="integral_opt">
						<view class="no_int" @click="conInt('noInt')">{{$L('暂不使用积分')}}</view>
						<view class="int_con" @click="conInt('confirm')">{{ $L('确定')}}</view>
					</view>
				</view>
			</view>
		</uni-popup>
		
		<!-- 积分规则 -->
		<uni-popup ref="intRule" type="center">
			<view class="intRule_box">
				<view class="int_title">{{ $L('使用规则') }}</view>
				<view class="int_content">
					<view>{{ $L('订单大于等于')}}<text style="color: var(--color_main)">{{ intRuleList[2] }}{{ $L('元') }}
						</text>{{ $L('可用') }};</view>
					<view>{{ $L('积分支付不超过订单金额的')}}<text style="color: var(--color_main)">{{ intRuleList[3] }}%;</text>
					</view>
					<view>{{ $L('积分使用数量为')}}<text
							style="color: var(--color_main)">{{ intRuleList[1] }}</text>{{ $L('的整数倍') }};
					</view>
					<view>{{ intRuleList[1] }}{{ $L('积分等于')}}<text
							style="color: var(--color_main)">1{{ $L('元') }}</text>;</view>
				</view>
			</view>
		</uni-popup>
		<!-- s -->
		
		
	</view>
</template>

<script>
	export default{
		props:{
			integralList:{
				type:Array,
				default:()=>[]
			},
			totalAmount:{
				type:Number|String,
				default:0
			},
			integralInfo:{
				type:Object,
				default:()=>{}
			}
		},
		data(){
			return{
				integral: 0,
				intRuleList: [],
				tmpInt: 0,
				imgUrl:process.env.VUE_APP_IMG_URL
			}
		},
		
		mounted() {
			this.getIntRule()
		},
		
		methods:{
			select_integral() {
				if (!this.integralList.length && Number(this.totalAmount) >= this.intRuleList[2]) {
					return
				}
				this.tmpInt = this.integral
				this.$refs.integralModel.open()
			},
			showIntRule() {
				this.$refs.intRule.open()
			},
			
			selInt(e) {
				this.tmpInt = e
			},
			conInt(type) {
				switch (type) {
					case 'confirm': {
						this.integral = this.tmpInt
						this.$refs.integralModel.close()
						this.$emit('select',this.integral)
						break
					}
					case 'close': {
						this.$refs.integralModel.close()
						break
					}
					case 'noInt': {
						this.integral = 0
						this.tmpInt = 0
						this.confirmOrder(2)
						this.$emit('select',this.integral)
						break
					}
				}
			},
			
			getIntRule() {
				this.$request({
					url: 'v3/system/front/setting/getSettings',
					data: {
						names: 'integral_cash_out_is_enable,integral_conversion_ratio,integral_use_lowest_amount,integral_max_deduct_rate'
					}
				}).then((res) => {
					if (res.state == 200) {
						this.intRuleList = res.data
					}
				})
			},
		}
	}
</script>

<style lang="scss">
	
	.uni-popup {
		z-index: 100;
	}
	
	.integral_list_con {
		border-radius: 5px 5px 0;
		
		.integral_top {
			padding: 20rpx 30rpx;
			border-radius: 5px 5px 0 0;
			font-size: 32rpx;
			font-family: PingFang SC;
			font-weight: bold;
			color: #333333;
			display: flex;
			justify-content: space-between;
			align-items: center;
			background: #fff;
			border-bottom: 0.5px solid #f2f2f2;
		
			image {
				width: 50rpx;
				height: 50rpx;
			}
		}
		
		.member_int {
			height: 79rpx;
			background: #f8f8f8;
			line-height: 79rpx;
			font-size: 28rpx;
			font-family: Adobe Heiti Std;
			font-weight: bold;
			color: #333333;
			padding-left: 30rpx;
		}
		
		.integral_list {
			width: 750rpx;
			height: 610rpx;
			margin: 0 auto;
			z-index: 150;
			background-color: #fff;
		}
		
		.wrapper {
			flex: 1;
			background: #fff;
		
			.iconfont {
				color: var(--color_main);
				font-size: 32rpx;
				margin-right: 30rpx;
			}
		
			image {
				width: 36rpx;
				height: 38rpx;
				margin-right: 22rpx;
			}
		}
		
		.wrapper_right {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 32rpx;
		}
		
		.integral-box {
			display: flex;
			align-items: center;
		
			.int_desc {
				font-size: 28rpx;
				color: #333;
				line-height: 38rpx;
				margin-top: 5rpx;
				padding: 0 5rpx;
			}
		
			.tag {
				width: 63rpx;
				height: 30rpx;
				margin-left: 20rpx;
				margin-right: 0rpx;
			}
		}
		
		.other_integral {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			height: 110rpx;
			background: #fff;
		
			.integral_opt {
				display: flex;
				align-items: center;
				justify-content: center;
				height: 80rpx;
		
				.no_int {
					width: 334rpx;
					border-radius: 44rpx 0 0 44rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 30rpx;
					color: #fff;
					background-color: #999;
					height: 70rpx;
				}
		
				.int_con {
					width: 334rpx;
					border-radius: 0 44rpx 44rpx 0;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 30rpx;
					color: #fff;
					background-color: var(--color_main);
					height: 70rpx;
				}
			}
		
			.other_btn {
				background: linear-gradient(-90deg, #fc1d1c 0%, #ff7a18 100%);
				border-radius: 44rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 34rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #fefefe;
			}
		}
	}
	
	.list {
		display: flex;
		/* flex-direction: column; */
		align-items: center;
		justify-content: flex-start;
		padding: 24rpx 30rpx;
		background: #fff;
		position: relative;
	}
	
	.yt-list-cell {
		display: flex;
		align-items: center;
		padding: 20rpx;
		line-height: 60rpx;
		position: relative;
	
		.iconfont {
			color: #999999;
			font-size: 26rpx;
			margin-left: 19rpx;
		}
	
		&.cell-hover {
			background: #fafafa;
		}
	
		&.b-b:after {
			left: 20rpx;
		}
	
		.cell-icon {
			height: 32rpx;
			width: 32rpx;
			font-size: 22rpx;
			color: #fff;
			text-align: center;
			line-height: 32rpx;
			background: #f85e52;
			border-radius: 4rpx;
			margin-right: 12rpx;
	
			&.hb {
				background: #ffaa0e;
			}
	
			&.lpk {
				background: #3ab54a;
			}
		}
	
		.voice {
			color: #333333 !important;
			display: flex;
			align-items: center;
		}
	
		.cell-more {
			align-self: center;
			font-size: 24rpx;
			color: $font-color-light;
			margin-left: 8rpx;
			margin-right: -10rpx;
		}
	
		.cell-tit {
			flex: 1;
			font-size: 26rpx;
			color: #333333;
			margin-right: 20rpx;
		}
	
		.cell-tip {
			font-size: 26rpx;
			color: var(--color_price);
	
			&.disabled {
				color: $font-color-light;
			}
	
			&.active {
				color: $base-color;
			}
	
			&.red {
				color: $base-color;
			}
		}
	
		&.desc-cell {
			.cell-tit {
				max-width: 90rpx;
			}
		}
	
		.desc {
			flex: 1;
			font-size: $font-base;
			color: $font-color-dark;
		}
	
		.cell_int {
			display: flex;
			flex: 1;
			align-items: center;
	
			image {
				width: 30rpx;
				height: 30rpx;
			}
	
			.cell-tit {
				flex: unset;
			}
		}
	}
	
	.intRule_box {
		width: 578rpx;
		height: 464rpx;
		background: #ffffff;
		border-radius: 10px;
	
		.int_title {
			padding: 20rpx 30rpx;
			font-size: 32rpx;
			font-family: Adobe Heiti Std;
			font-weight: bold;
			color: #2d2d2d;
			border-bottom: 1px solid #f2f2f2;
		}
	
		.int_content {
			padding: 30rpx;
	
			view {
				line-height: 40rpx;
				font-size: 26rpx;
				color: #2d2d2d;
				margin-bottom: 20rpx;
			}
		}
	}
	
	uni-radio ::v-deep .uni-radio-input {
		width: 32rpx;
		height: 32rpx;
	}
	
	uni-radio ::v-deep .uni-radio-input.uni-radio-input-checked:before {
		font-size: 28rpx;
	}
</style>