<template>
	<!-- 虚拟商品的预留信息 -->
	<view class="pre_message">
		<block v-if="virtualPre.length">
			<block v-for="(item, index) in virtualPre" :key="index">
				<view class="pre_msg_item flex_row_start_center" v-if="!orderSn || item.reserveValue">
					<view class="msg_left flex_row_end_center"><text style="color: red"
							v-if="item.isRequired == 1">*</text><text>{{ item.reserveName }}</text>：</view>
					<view class="msg_right">
						<block v-if="item.reserveType == 1">
							<input type="number" :placeholder="`${$L('请输入')}${item.reserveName}`"
								v-model="item.reserveValue" maxlength="11" @blur="handleBlur" @focus="handleFocus"
								v-if="!orderSn" placeholder-style="font-size:26rpx" />
							<text v-else style="opacity: 0.4">{{ item.reserveValue }}</text>
						</block>
						<block v-else-if="item.reserveType == 3">
							<input type="number" :placeholder="`${$L('请输入')}${item.reserveName}`"
								v-model="item.reserveValue" maxlength="50" @blur="handleBlur" @focus="handleFocus"
								v-if="!orderSn" placeholder-style="font-size:26rpx" />
							<text v-else style="opacity: 0.4">{{ item.reserveValue }}</text>
						</block>
						<block v-else-if="item.reserveType == 5">
							<input type="text" :placeholder="`${$L('请输入')}${item.reserveName}`"
								v-model="item.reserveValue" maxlength="30" @blur="handleBlur" @focus="handleFocus"
								v-if="!orderSn" placeholder-style="font-size:26rpx" />
							<text v-else style="opacity: 0.4">{{ item.reserveValue }}</text>
						</block>
						<block v-else>
							<input type="text" :placeholder="`${$L('请输入')}${item.reserveName}`"
								v-model="item.reserveValue" :maxlength="item.reserveType == 2 ? 18 : 50"
								@blur="handleBlur" @focus="handleFocus" v-if="!orderSn"
								placeholder-style="font-size:26rpx" />
							<text v-else style="opacity: 0.4">{{ item.reserveValue }}</text>
						</block>
					</view>
				</view>
			</block>
		</block>
	</view>
	<!-- 虚拟商品的预留信息 -->
</template>

<script>
	export default{
		props:['virtualInfo','orderSn'],
		data(){
			return{
				virtualPre:[]
			}
		},
		created() {
			this.virtualPre = this.virtualInfo
		},
		
		methods:{
			//备注输入框聚焦
			handleFocus() {
				this.$emit('focus')
			},
			//失去焦点
			handleBlur() {
				this.$emit('blur')
			},
			//校验预留信息
			checkPreMsg(type, value, name, isRequired) {
				switch (type) {
					case 1: {
						if (isRequired == 1) {
							return this.$checkMobile(value, name)
						} else {
							let regMobile = /(1[3-9]\d{9}$)/
							if (value && !regMobile.test(value)) {
								this.$api.msg(`请输入正确的${name}!`)
								return false
							} else {
								return true
							}
						}
			
						break
					}
					case 2: {
						if (isRequired == 1) {
							return this.$checkIdentity(value, name)
						} else {
							if (value) {
								let reg18 =
									/^[1-9][0-9]{5}(18|19|20)[0-9]{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)[0-9]{3}([0-9]|(X|x))/
								let reg15 =
									/^[1-9][0-9]{5}[0-9]{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)[0-9]{2}[0-9]/
								if (reg18.test(value) || reg15.test(value)) {
									return true
								} else {
									this.$api.msg(`请输入正确的${name}`)
									return false
								}
							} else {
								return true
							}
						}
			
						break
					}
			
					case 3: {
						let regNum = /[0-9]+(.[0-9]+)?/
						if (isRequired == 1) {
							if (!value) {
								this.$api.msg(`请输入${name}`)
								return false
							} else if (!regNum.test(value)) {
								this.$api.msg(`请输入正确的${name}`)
								return false
							} else {
								return true
							}
						} else {
							return true
						}
						break
					}
					case 4: {
						if (isRequired == 1) {
							if (!value) {
								this.$api.msg(`请输入${name}`)
								return false
							} else {
								return true
							}
						} else {
							return true
						}
						break
					}
					case 5: {
						if (isRequired == 1) {
							return this.$checkEmail(value, name)
						} else {
							let reg =
								/^([a-zA-Z0-9]+[-_.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[-_.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,6}$/
							if (value && !reg.test(value)) {
								this.$api.msg(`请输入正确的${name}!`)
								return false
							} else {
								return true
							}
						}
			
						break
					}
				}
			},
			handlePreInfo(){
				let reserveInfoList = []
				if (this.virtualPre.length > 0) {
					for (let i = 0; i < this.virtualPre.length; i++) {
						let {
							reserveName,
							reserveType,
							reserveValue,
							reserveNameId,
							isRequired
						} = this.virtualPre[i]
						if (
							this.checkPreMsg(reserveType, reserveValue, reserveName, isRequired)
						) {
							reserveInfoList.push({
								reserveName,
								reserveValue,
								reserveNameId
							})
						} else {
							return null
						}
					}
				}
				return reserveInfoList
			},
		}
	}
</script>

<style lang="scss">
	.pre_message {
		background: #fff;
	
		.pre_msg_item {
			padding: 28rpx 22rpx;
			width: 100%;
			border-bottom: 2rpx solid #f2f2f2;
	
			.msg_left {
				font-size: 28rpx;
				font-family: MicrosoftYaHei;
				color: #333333;
				font-weight: 600;
				width: 200rpx;
				text-align: right;
				word-break: break-all;
			}
	
			.msg_right {
				margin-left: 20rpx;
	
				input {
					font-size: 24rpx;
					width: 500rpx;
				}
			}
		}
	}
	//ss
</style>