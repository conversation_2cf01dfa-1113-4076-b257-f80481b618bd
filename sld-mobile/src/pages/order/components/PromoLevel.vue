<template>
	<view class="ladder_list b-b">
		<text class="ladder_tip">{{$L('其他优惠可在支付尾款时选择')}}</text>
		<view class="ladder_process">
			<view class="process_level on">
				<view class="level_top">
					<view class="level_left">
						<svgGroup type="to_ladder_img" width="43" height="47" px="rpx"
							:color="diyStyle_var['--color_price']">
						</svgGroup>
						<text>{{ $L("定金支付") }}</text>
					</view>
					<view class="level_right">
						<text>{{ $L('￥')}}{{ info.firstMoney?info.firstMoney:info.firstPrice }}</text>
					</view>
				</view>
				<view class="level_gang"></view>
			</view>
			<view :class="{process_level: true,on: state == 102}">
				<view class="level_gang"></view>
				<view class="level_top">
					<view class="level_left">
						<svgGroup v-if="state == 102"
							type="to_commission" width="43" height="47" px="rpx"
							:color="diyStyle_var['--color_price']"></svgGroup>
						<svgGroup v-else type="to_commission" width="43" height="47" px="rpx"
							color="#ddd"></svgGroup>
						<text>{{ $L("尾款结算") }}</text>
					</view>
					<view :class="{level_right: state == 102}">
						<block v-if="type=='ladder'">
							<text v-if="state == 102">￥{{$getPartNumber(info.remainAmount,'all')}}</text>
							<text v-else>--</text>
						</block>
						<block v-else>
							<text>￥{{ info.secondMoney?info.secondMoney:info.secondPrice }}</text>
						</block>
					</view>
				</view>
			</view>
			<view class="preDesc_con" style="margin-top: 48rpx">
				<block v-if="type=='ladder'">
					<view class="preSale_desc" v-if="info.ladderLevel > 0">
						{{ $L('当前阶梯为第')}}{{ info.ladderLevel}}{{ $L('阶梯，商品价格优惠')}}{{ info.discount }}{{ $L('元') }}
					</view>
					<view class="preSale_desc" v-if="info.ladderLevel == 0">
						{{ $L('未达到第一阶梯，不享受优惠') }}
					</view>
				</block>
				<block v-else>
					<view class="preSale_desc"
						v-if="info.remainStartTime">
						<text style="color: var(--color_main)">{{info.remainStartTime}}</text><text
							style="color: #2d2d2d">{{$L('开始支付尾款')}}</text>
					</view>
					<view class="preSale_desc" v-if="info.firstExpand">
						{{ $L('预售定金可抵') }}{{ info.firstExpand}}{{ $L('元') }}，{{ $L('支付定金后尾款优惠')}}{{ info.finalDiscount }}元
					</view>
				</block>
			</view>
		</view>
	</view>
</template>

<script>
	export default{
		props:['state','info','type'],
		
	}
</script>

<style lang="scss">
	.ladder_list {
		padding: 20rpx 40rpx;
		background-color: #fff;
		position: relative;
	
		.ladder_tip {
			font-size: 24rpx;
			font-family: PingFang SC;
			font-weight: 400;
			color: #2d2d2d;
		}
	
		.ladder_process {
			margin-top: 40rpx;
	
			.on {
				.level_left {
					text {
						color: var(--color_price) !important;
					}
				}
	
				.level_right {
					color: var(--color_price) !important;
				}
	
				.level_gang {
					background: var(--color_price) !important;
				}
			}
	
			.process_level {
				.level_top {
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin-bottom: 6rpx;
					margin-top: 6rpx;
					font-size: 28rpx;
	
					.level_left {
						display: flex;
						align-items: center;
	
						text {
							line-height: 43rpx;
							margin-left: 32rpx;
							font-size: 28rpx;
							font-family: PingFang SC;
							font-weight: 400;
							color: #2d2d2d;
						}
	
						image {
							width: 39rpx;
							height: 43rpx;
						}
					}
	
					.level_right {
						font-size: 28rpx;
						font-family: PingFang SC;
						font-weight: bold;
						color: #2d2d2d;
					}
				}
	
				.level_gang {
					width: 1px;
					height: 31rpx;
					background: #e1e1e1;
					margin-left: 18rpx;
				}
			}
		}
	
		.ladder_total {
			display: flex;
			justify-content: flex-end;
			margin-top: 42rpx;
			font-size: 28rpx;
			font-family: PingFang SC;
	
			text:first-child {
				color: #666666;
				margin-right: 10rpx;
			}
	
			text:nth-child(2) {
				color: #2d2d2d;
			}
	
			text:nth-child(3) {
				color: var(--color_price);
			}
		}
	}
	//ss
</style>