<template>
	<view>
		<view class="yt-list-cell b-b" v-if="couponLocalList.length == 0">
			<text class="cell-tit clamp">{{ $L(typeName) }}</text>
			<text class="cell-tip voice">{{ $L(`暂无可用${$L(typeName)}`) }}</text>
		</view>
		<view class="yt-list-cell b-b" v-else>
			<view style="display: flex;width: 100%;" @click="openFlag">
				<text class="cell-tit clamp">{{ $L(typeName) }}</text>
				<text class="cell-tip" v-if="couponCode">-￥{{couponText}}</text>
				<text class="cell-tip voice" v-else>不选择{{$L(typeName)}}</text>
				<text class="iconfont iconziyuan11"></text>
			</view>
		</view>

		<view class="coupon_wrap" v-if="showFlag">
			<view class="tranparent_back" @tap="transparentClick"></view>
			<view class="store_red">
				<view class="title flex_row_between_center">
					<text>{{ $L(`使用${typeName}`) }}</text>
					<text @click="couponConfirm" v-if="type=='freight'">确定</text>
				</view>
				<scroll-view scroll-y class="store_red_list">
					<view v-for="(reditem, index) in couponLocalList" :key="index" class="ticket-item">
						<view class="circle_radio a">
							<block v-if="type=='freight'">
								<view style="padding-left: 40rpx;" @tap="checkCoupon(reditem)">
									<text class="iconfont"
										:class="{iconziyuan43:!reditem.preChecked,iconziyuan33:reditem.preChecked}"></text>
								</view>
							</block>
		
							<view class="red_item_wrap" style="padding-left: 34rpx;">
								<view class="red_h1">
									<text v-if="reditem.value">{{ reditem.value }}{{ $L('元') }}</text>
									<text v-else>免运费</text>
								</view>
								<view class="red_h2">
									<view>{{ reditem.content }}</view>
									<view>{{ reditem.useTime }}</view>
								</view>
								<view class="red_h3">
									<view class="red_h3_top">
										<image :src="imgUrl + 'ok_w.png'"></image>
										<text>{{ $L('您已领券') }}</text>
									</view>
									<block v-if="type!=='freight'">
										<view class="red_h3_bottom" @tap="selectCoupon(reditem)">
											{{couponCode == reditem.couponCode? $L('已选择'): $L('点击使用')}}
										</view>
									</block>
								</view>
							</view>
						</view>
						<view class="line_left"></view>
						<view class="line_right"></view>
						<view class="red_p">
							<text style="margin-left: 40rpx">{{ reditem.description }}</text>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>
		

		
		
	</view>
</template>

<script>
	export default {
		props: {
			couponList: {
				type: Array,
				default: () => []
			},
			type: String,
			expressFee:Number
		},
		data() {
			return {
				couponCode: '',
				couponText: '',
				showFlag: false,
				imgUrl: process.env.VUE_APP_IMG_URL,
				couponLocalList: []
			}
		},

		created() {
			this.handleCoupon()
		},
		
		watch:{
			couponList:{
				handler(){
					this.handleCoupon()
				},
				deep:true,
			}
		},

		computed: {
			typeName() {
				if(this.type=='store') return this.$L('店铺优惠券')
				return this.type == 'platform' ? '平台优惠券' : '运费券'
			}
		},

		methods: {
			
			handleCoupon(){
				this.couponLocalList = this.couponList.map(fcp => ({
					...fcp,
					preChecked: fcp.checked
				}))
				
				let checkedCoupon = this.couponList.filter(cp=>cp.checked)
				if(checkedCoupon.length){
					this.couponText = checkedCoupon.reduce((total,prev)=>total+prev.discount,0)
					this.couponCode = checkedCoupon.map(i=>i.couponCode).toString()
				}
			},
			
			transparentClick() {
				this.showFlag = false
				this.couponLocalList.map(fcp=>{
					fcp.preChecked = fcp.checked
				})
			},
			
			openFlag(){
				this.showFlag = true
			},
	
			
			selectCoupon(coupon){
				if (this.couponCode == coupon.couponCode) {
					this.couponCode = ''
					this.couponText = ''
				} else {
					this.couponCode = coupon.couponCode
					this.couponText = coupon.discount
				}
				this.showFlag = false
				this.$emit('confirm',this.couponCode)
			},
			
			checkAvailable(item){
				let checkeds = this.couponLocalList.filter(item=>item.preChecked).map(litem=>litem.discount)
				let totalDiscount = checkeds.reduce((prev,current)=>prev+Number(current),0)
				if(totalDiscount>=this.expressFee&&!item.preChecked){
					this.$api.msg('运费券总优惠已大于运费总和，不可再选')
					return false
				}
				return true
			},
			
			
			checkCoupon(item){
				
				if(!this.checkAvailable(item)){
					return
				}
				
				item.preChecked = !item.preChecked
				let checkeds = this.couponLocalList.filter(item=>item.preChecked)
				this.couponCode =checkeds.map(jtem=>jtem.couponCode).toString()
			},
			
			couponConfirm(){
				this.showFlag = false
				this.$emit('confirm',this.couponCode)
			}

		}
	}
</script>

<style lang="scss">
	
	.yt-list-cell {
		display: flex;
		align-items: center;
		padding: 20rpx;
		line-height: 60rpx;
		position: relative;
	
		.iconfont {
			color: #999999;
			font-size: 26rpx;
			margin-left: 19rpx;
		}
	
		&.cell-hover {
			background: #fafafa;
		}
	
		&.b-b:after {
			left: 20rpx;
		}
	
		.cell-icon {
			height: 32rpx;
			width: 32rpx;
			font-size: 22rpx;
			color: #fff;
			text-align: center;
			line-height: 32rpx;
			background: #f85e52;
			border-radius: 4rpx;
			margin-right: 12rpx;
	
			&.hb {
				background: #ffaa0e;
			}
	
			&.lpk {
				background: #3ab54a;
			}
		}
	
		.voice {
			color: #333333 !important;
			display: flex;
			align-items: center;
		}
	
		.cell-more {
			align-self: center;
			font-size: 24rpx;
			color: $font-color-light;
			margin-left: 8rpx;
			margin-right: -10rpx;
		}
	
		.cell-tit {
			flex: 1;
			font-size: 28rpx;
			color: #666;
			margin-right: 20rpx;
		}
	
		.cell-tip {
			font-size: 28rpx;
			color: var(--color_price);
	
			&.disabled {
				color: $font-color-light;
			}
	
			&.active {
				color: $base-color;
			}
	
			&.red {
				color: $base-color;
			}
		}
	
		&.desc-cell {
			.cell-tit {
				max-width: 90rpx;
			}
		}
	
		.desc {
			flex: 1;
			font-size: $font-base;
			color: $font-color-dark;
		}
	
		.cell_int {
			display: flex;
			flex: 1;
			align-items: center;
	
			image {
				width: 30rpx;
				height: 30rpx;
			}
	
			.cell-tit {
				flex: unset;
			}
		}
	}
	//ss
	
	.coupon_wrap {
		position: fixed;
		top: 0;
		left: 0;
		width: 750rpx;
		height: 100vh;
		background-color: rgba(0, 0, 0, 0.6);
		z-index: 555;
		right: 0;
		margin: 0 auto;
	}

	.tranparent_back {
		position: absolute;
		inset: 0;
		z-index: 555;
	}

	.store_red {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 750rpx;
		height: 50vh;
		background-color: #f5f5f5;
		box-sizing: border-box;
		z-index: 600;
	}

	.store_red .title {
		line-height: 110rpx;
		padding: 0 30rpx;
		border-bottom: 1rpx solid #eee;
		font-size: 30rpx;
		color: #666;
		background: #fff;
	}

	.store_red_list {
		height: 78%;
		box-sizing: border-box;
		padding: 30rpx 30rpx 0;
	}

	.store_red_list .ticket-item {
		position: relative;
		display: block;
		margin-bottom: 30rpx;
		border-radius: 20rpx;
	}

	.ticket-item .line_left,
	.ticket-item .line_right {
		position: absolute;
		top: 150rpx;
		width: 20rpx;
		height: 20rpx;
		border-radius: 50%;
		background-color: #f5f5f5;
	}

	.ticket-item .line_left {
		left: -10rpx;
	}

	.ticket-item .line_right {
		right: -10rpx;
	}

	.store_red_list .ticket-item:last-child {
		margin: 0;
	}

	.store_red_list .circle_radio {
		display: flex;
		align-items: center;
		height: 160rpx;
		color: #fff;
		border-top-left-radius: 15rpx;
		border-top-right-radius: 15rpx;
		background: var(--color_coupon_main_bg);
	}

	.circle_radio .red_item_wrap {
		display: flex;
		width: 100%;
		padding: 0 30rpx 0 50rpx;
		align-items: center;
		justify-content: space-between;
	}

	.circle_radio .red_item_wrap .red_h1 {
		font-size: 26rpx;
	}

	.circle_radio .red_item_wrap .red_h1 em {
		font-size: 54rpx;
	}

	.circle_radio .red_item_wrap .red_h2 {
		font-size: 26rpx;
	}

	.circle_radio .red_item_wrap .red_h2 em {
		display: block;
	}

	.circle_radio .red_item_wrap .red_h2 em:nth-child(2) {
		font-size: 20rpx;
		padding-top: 10rpx;
	}

	.circle_radio .red_item_wrap .red_h3 .red_h3_top {
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 22rpx;
	}

	.red_h3 .red_h3_top image {
		width: 20rpx;
		height: 20rpx;
		margin-right: 15rpx;
	}

	.circle_radio .red_item_wrap .red_h3 .red_h3_bottom {
		padding: 10rpx 15rpx;
		font-size: 28rpx;
		color: #fff;
		background: rgba(255, 255, 255, 0.5);
		margin-top: 18rpx;
		border-radius: 6rpx;
		text-align: center;
	}

	.ticket-item .red_p {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 80rpx;
		font-size: 26rpx;
		color: #666666;
		background: #fff;
		border-top: 3rpx dashed #fff;
		border-bottom-left-radius: 15rpx;
		border-bottom-right-radius: 15rpx;
	}

	.ticket-item .red_p image {
		width: 26rpx;
		height: 26rpx;
		margin-right: 10rpx;
	}

	.ticket-item .red_p text {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
</style>