<!-- 交易成功页面 -->
<template>
	<view :style="mix_diyStyle">
		<view class="container">
			<view class="main_content">
				<!-- 订单状态 start -->
				<view class="order_state">
					<image :src="imgUrl + 'order-detail/dagou.png'" mode="aspectFit" class="order_state_img"></image>
					<view class="order_state_text">{{ $L('交易成功') }}</view>
					<view class="order_btn">
						<text @click="backIndex">{{ $L('返回首页') }}</text>
						<text @click="lookOrder" v-if="sourceType == 'tradeSuccess' || sourceType == 'point'">{{ $L('查看订单')
						}}</text>
						<text @click="goEvaluateList" v-else-if="sourceType == 'renqiTradeSuccess'">{{ $L('立即评价') }}</text>
						<text @click="goEvaluate" v-else>{{ $L('立即评价') }}</text>
					</view>
				</view>
				<!-- 订单状态 end -->


				<!-- 推荐商品 start-->
				<!-- <view v-if="recommendShow" class="recomment">
					<recommendGoods ref="recomment_goods" />
				</view> -->
				<!-- 推荐商品 end-->
			</view>
		</view>
		<view class="new_img" v-if="newImg">
			<image :src="newImg" mode="aspectFit" class="order_qrcode_img"></image>
			<text>扫描上方二维码，扫码接联系老师，</text>
			<text>进行专属服务</text>
		</view>
	</view>
</template>
<script>
import recommendGoods from '@/components/recommend-goods.vue'
import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue'
import uniPopupMessage from '@/components/uni-popup/uni-popup-message.vue'
import uniPopup from '@/components/uni-popup/uni-popup.vue'
import {
mapState
} from 'vuex'
let startY = 0,
	moveY = 0,
	pageAtTop = true
export default {
	components: {
		recommendGoods,
		uniPopup,
		uniPopupMessage,
		uniPopupDialog
	},
	data() {
		return {
			imgUrl: process.env.VUE_APP_IMG_URL,
			newImg: '',
			coverTransform: 'translateY(0px)',
			coverTransition: '0s',
			moving: false,
			orderSn: '', //订单号
			sourceType: '', //页面来源 orderDetail:订单详情 orderList:订单列表 tradeSuccess:支付成功 point:积分订单支付成功
			recommendShow: false, //是否显示推荐模块
		}
	},
	computed: {
		...mapState(['hasLogin'])
	},
	async onLoad(option) {
		if (this.hasLogin || uni.getStorageSync('userInfo')) {
			this.recommendShow = true;
		}

		//#ifdef H5
		let wxBrowerBack = uni.getStorageSync('wxBrowerBack')
		if (wxBrowerBack) {
			uni.removeStorageSync('wxBrowerBack')
			if (uni.getSystemInfoSync().platform == 'ios') {
				setTimeout(() => {
					this.$router.go(0)
				}, 300)
			}
		}
		//#endif
		//订单号
		this.orderSn = this.$Route.query.orderSn
		this.sourceType = this.$Route.query.sourceType
		console.log('tradeSuccess:', this.orderSn, this.sourceType)
		this.getQRcode()
	},
	methods: {
		initData() { },

		/**
		 * 统一跳转接口,拦截未登录路由
		 * navigator标签现在默认没有转场动画，所以用view
		 */
		navTo(url) {
			if (!this.hasLogin) {
				let urls = this.$Route.path
				const query = this.$Route.query
				uni.setStorageSync('fromurl', {
					url: urls,
					query
				})
				url = '/pages/public/login'
			}
			this.$Router.push(url)
		},

		/**
		 *  会员卡下拉和回弹
		 *  1.关闭bounce避免ios端下拉冲突
		 *  2.由于touchmove事件的缺陷（以前做小程序就遇到，比如20跳到40，h5反而好很多），下拉的时候会有掉帧的感觉
		 *    transition设置0.1秒延迟，让css来过渡这段空窗期
		 *  3.回弹效果可修改曲线值来调整效果，推荐一个好用的bezier生成工具 http://cubic-bezier.com/
		 */
		coverTouchstart(e) {
			if (pageAtTop === false) {
				return
			}
			this.coverTransition = 'transform .1s linear'
			startY = e.touches[0].clientY
		},
		coverTouchmove(e) {
			moveY = e.touches[0].clientY
			let moveDistance = moveY - startY
			if (moveDistance < 0) {
				this.moving = false
				return
			}
			this.moving = true
			if (moveDistance >= 80 && moveDistance < 100) {
				moveDistance = 80
			}

			if (moveDistance > 0 && moveDistance <= 80) {
				this.coverTransform = `translateY(${moveDistance}px)`
			}
		},
		coverTouchend() {
			if (this.moving === false) {
				return
			}
			this.moving = false
			this.coverTransition = 'transform 0.3s cubic-bezier(.21,1.93,.53,.64)'
			this.coverTransform = 'translateY(0px)'
		},
		//返回首页
		backIndex() {
			this.$Router.pushTab('/pages/index/index')
		},
		//去评价页面
		goEvaluate() {
			this.$Router.replace({
				path: '/pages/order/publishEvaluation',
				query: {
					orderSn: this.orderSn,
					sourceType: this.sourceType
				}
			})
		},
		goEvaluateList() {
			this.$Router.push({
				path: '/pages/order/list',
				query: {
					state: 5
				}
			})
		},
		getQRcode() {
			let data = {
				orderSn: this.orderSn,
			}
			console.log('getQRcode:', this.orderSn)
			this.$request({
				url: 'v3/business/front/orderInfo/getShowQRCode',
				data
			}).then((res) => {
				console.log('getQRcode:rsp=', res)
				if (res.state == 200) {
					this.newImg = res.msg
				}
			}).catch((e) => {
				console.error('getQRcode:err=', e)
			})
		},
		//查看订单
		lookOrder() {
			if (this.sourceType == 'tradeSuccess') {
				this.$Router.replace('/pages/order/list');
			} else if (this.sourceType == 'point') {
				this.$Router.replace('/standard/point/order/list');
			}
		}
	}
}
</script>
<style lang="scss">
page {
	background: $bg-color-split;
}

.container {
	display: flex;
	flex: 1;
	// height: 100vh;
	position: relative;
	width: 750rpx;
	margin: 0 auto;

	.main_content {
		width: 100%;
		padding-top: 92rpx;
		box-sizing: border-box;
		background: var(--color_main_bg_zero);

		.order_state {
			display: flex;
			flex-direction: column;
			align-items: center;
			padding-bottom: 80rpx;

			.order_state_img {
				width: 99rpx;
				height: 99rpx;
			}


			.order_state_text {
				font-size: 34rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: #ffffff;
				margin: 30rpx 0 50rpx;
			}

			.order_btn {
				display: flex;
				align-items: center;

				text:nth-child(1) {
					width: 210rpx;
					height: 60rpx;
					border: 1rpx solid #ffffff;
					border-radius: 30rpx;
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #ffffff;
					text-align: center;
					line-height: 60rpx;
				}

				text:nth-child(2) {
					width: 210rpx;
					height: 60rpx;
					background: #ffffff;
					box-shadow: 0rpx 10rpx 20rpx 0rpx var(--color_halo);
					border-radius: 30rpx;
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: var(--color_main);
					text-align: center;
					line-height: 60rpx;
					margin-left: 90rpx;
				}
			}
		}

		.recomment {
			background: linear-gradient(to bottom, #ffffff 0%, #f5f5f5 20%, #f5f5f5 100%);
			box-sizing: border-box;
			border-radius: 30rpx 30rpx 0 0;
		}
	}
}

.new_img {
	display: flex;
	flex-direction: column;
	align-items: center;

	image {
		margin: 50rpx auto;
		width: 800rpx;
	}

	text {
		color: rgba(108, 108, 108, 1);
		font-size: 12px;
		text-align: center;
		font-family: SourceHanSansSC-regular;
	}
}</style>