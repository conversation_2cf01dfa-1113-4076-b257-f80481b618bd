<template>
	<view style="margintop: 20rpx" id="container" :style="mix_diyStyle">



		<view style="position: relative;" v-if="!isO2o">
			<block v-if="topShowType.hasAddress">
				<!-- 地址信息start -->
				<AddressInfo :addressData="orderAddress" @click="operateAddress" v-if="isVG == 1"></AddressInfo>
				<!-- 地址信息start -->

				<!-- 虚拟商品的预留信息 -->
				<PreMsg ref="preMsgRef" :virtualInfo="virtualPre" :orderSn="orderSn" @blur="handleBlur" @focus="handleFocus"
					v-else></PreMsg>
				<!-- 虚拟商品的预留信息 -->
			</block>



			<image class="a-bg" :src="imgUrl + 'address_split_border.png'"></image>
		</view>

		<view class="goods-section flex_column_start_start" v-if="goodsData.length > 0">
			<!-- 商品列表 -->
			<view class="store_list" v-for="(item, index) in goodsData" :key="item.storeId">
				<view class="store_name" @click.stop="goStoreDetail(item)">
					<image class="store_logo" :src="imgUrl + 'goods_detail/store_logo.png'"></image>
					<text class="store_name_text">{{ item.storeName }}</text>
					<text class="iconfont iconziyuan11"></text>
				</view>

				<block v-for="(product, indexs) in item.productList" :key="indexs">
					<GoodsItem :product="product" :isVirtual="isVG"></GoodsItem>
				</block>

				<view class="yt-list store_info">
					<!-- 满赠商品 -->
					<view class="yt-list-cell-giveaway b-b" v-if="item.sendProductList && item.sendProductList.length > 0">
						<text class="cell-tit clamp">{{ $L('赠品信息') }}</text>
						<view class="giveaway_list">
							<view class="giveaway_item" v-for="(giveaway, indexss) in item.sendProductList" :key="indexss">
								<view class="giveaway_item_left">
									<text class="giveaway_item_index">{{ $L('赠品') }}{{ index + 1 }}：</text>
									<text class="giveaway_item_name">{{ giveaway.goodsName }}</text>
									<text>{{ $L('（赠完即止）') }}</text>
								</view>
								<text class="giveaway_item_number">*{{ giveaway.num }}</text>
							</view>
						</view>
					</view>
					<!-- 暂无优惠券 -->

					<!-- 付定金的时候不显示优惠券 -->
					<block v-if="noDepositState">

						<Coupon :couponList="item.availableCouponList" type="store"
							@confirm="(code) => store_red(code, item)">
						</Coupon>
						<view class="yt-list-cell b-b" v-if="goodsData.length == 1">
							<text class="cell-tit clamp">{{ $L('商品金额') }}</text>
							<text class="cell-tip">{{ $L('￥') }}{{ $getPartNumber(item.goodsAmount, 'int') }}{{
								$getPartNumber(item.goodsAmount, 'decimal') }}</text>
						</view>
					</block>



					<!-- 预售start -->
					<block v-if="allData.promotionType == 103 && allData.presellInfo.type != 2">
						<PromoLevel :state="allData.presellInfo.presellState" :info="allData.presellInfo" type="presale">
						</PromoLevel>
					</block>
					<!-- 预售end -->

					<!-- 阶梯团start -->
					<block v-if="allData.promotionType == 105">
						<PromoLevel :state="allData.ladderGroupInfo.ladderGroupState" :info="allData.ladderGroupInfo"
							type="ladder"></PromoLevel>
					</block>
					<!-- 阶梯团send -->

					<!-- 付定金的时候不显示运费、优惠信息 -->
					<block v-if="noDepositState">
						<view class="yt-list-cell b-b" v-if="item.deliverMethod == 1 || item.deliverMethod == 3">
							<text class="cell-tit clamp">{{ $L('运费') }}</text>
							<text class="cell-tip">{{ item.originalExpressFee ? $L('+￥')
								+ $getPartNumber(item.originalExpressFee, 'int') + $getPartNumber(item.originalExpressFee,
									'decimal') : $L('免运费') }}</text>
						</view>
						<view class="yt-list-cell b-b">
							<text class="cell-tit clamp">{{ $L('店铺总优惠') }}</text>
							<text class="cell-tip">-{{ $L('￥') }}{{ $getPartNumber(item.totalDiscount, 'int') }}{{
								$getPartNumber(item.totalDiscount, 'decimal') }}</text>
						</view>
					</block>
					<view class="yt-list-cell b-b" v-if="goodsData.length > 1">
						<text class="cell-tit clamp">{{ $L('小计') }}</text>
						<text class="cell-tip">￥{{ $getPartNumber((item.totalAmount * 1000 + item.originalExpressFee * 1000)
							/ 1000, 'int') }}{{ $getPartNumber((item.totalAmount * 1000 + item.originalExpressFee * 1000) /
		1000, 'decimal') }}</text>
					</view>
				</view>

				<!-- 付定金的时候不备注 -->
				<block v-if="noDepositState">
					<view class="yt-list order_remark">
						<text class="title">{{ $L('订单备注') }}</text>
						<textarea :placeholder="$L('给商家留言,最多100字')" v-model="item.remark" :adjust-position="true"
							placeholder-class="placeholder" class="content uni-input" maxlength="100" cursor-spacing="10"
							@focus="handleFocus" @blur="handleBlur" :data-index="index" @input="inputRemark"></textarea>
					</view>
				</block>
			</view>
		</view>

		<!-- 发票、平台优惠券(阶梯团付定金的时候不显示发票、平台优惠券) -->
		<block v-if="noDepositState">
			<view class="yt-list">
				<view class="yt-list-cell b-b">
					<view style="display: flex;width: 100%;" @click="toInvoice">
						<text class="cell-tit clamp">{{ $L('发票') }}</text>
						<text class="cell-tip voice" v-if="invoice_info == '' || invoice_info == '不需要发票'">{{ $L('不需要发票')
						}}</text>
						<view class="cell-tip voice" v-else><text class="limVoice">{{ invoice_info
						}}</text><text>{{ invoice_content == 1 ? $L('商品明细') : $L('商品类别') }}</text>
						</view>
						<text class="iconfont iconziyuan11"></text>
					</view>
				</view>

				<!-- 平台优惠券 -->
				<view>
					<Coupon :couponList="platformCouponList" type="platform" @confirm="platform_red" />
				</view>
				<!-- 平台优惠券 -->

				<!-- 运费券start -->
				<!-- <view v-if="topShowType.hasAddress">
					<Coupon :couponList="freightCoupon.list" type="freight" @confirm="freight_red"
						:expressFee="totalExpressFee" />
				</view> -->
				<!-- 运费券end -->

				<!-- 积分抵现 -->
				<!--<Integral :totalAmount="allData.totalAmount" :integralList="allData.integralList"
					:integralInfo="integralInfo" @select="selectIntegral"></Integral> -->

			</view>
		</block>

		<template v-if="allData.promotionType == 105 && allData.ladderGroupInfo.ladderGroupState == 101">
			<view class="agreement-part flex_row_start_center">
				<text class="iconfont iconkebianse_weixuanzhong register_icon" v-if="!show_check_icon"
					@click="checkAgrement"></text>
				<text class="iconfont iconkebianse_xuanzhong register_icon" style="color:var(--color_main);"
					@click="checkAgrement" v-else></text>
				{{ $L('我已阅读并同意') }}
				<text class="agreement" @click="agreement('ladder')">{{ $L('《阶梯团定金协议》') }}</text>
			</view>
		</template>

		<template
			v-if="allData.promotionType == 103 && allData.presellInfo.presellState == 101 && allData.presellInfo.type != 2">
			<view class="agreement-part flex_row_start_center">
				<text class="iconfont iconkebianse_weixuanzhong register_icon" v-if="!show_check_icon"
					@click="checkAgrement"></text>
				<text class="iconfont iconkebianse_xuanzhong register_icon" style="color:var(--color_main);"
					@click="checkAgrement" v-else></text>
				{{ $L('我已阅读并同意') }}
				<text class="agreement" @click="agreement('preSale')">{{ $L('《预售定金协议》') }}</text>
			</view>
		</template>


		<!-- 金额明细 -->
		<view class="empty_h"></view>

		<view class="flex_row_end_center disable-reason" v-if="submitStateVar.disableReason != undefined">
			{{ submitStateVar.disableReason }}
		</view>


		<!-- 底部 -->
		<view class="footer flex_row_end_center" v-if="isBottomShow">

			<view class="price-content flex_column_center_end">
				<view class="should_pay flex_row_end_end">
					<text class="tit">{{ $L('待付金额') }}：</text>
					<text class="unit">{{ $L('￥') }}</text>
					<text class="big_price">{{ $getPartNumber(allData.totalAmount, 'int') }}</text>
					<text class="small_price">{{ $getPartNumber(allData.totalAmount, 'decimal') }}</text>
				</view>

			</view>
			<text class="submit flex_row_center_center" :class="{ disable: !submitStateVar.submitState }"
				@click="submitOrder">

				<block v-if="orderSn">
					{{ $L('去付尾款') }}
				</block>
				<block v-else>
					{{ submitStateVar.submitText }}
				</block>

			</text>
		</view>


		<purchasePop ref="purchasePop" :exList="exceptionProList" :exState="exState" :exStateTxt="exStateTxt"
			@goNext="goNext" @delNext="delNext"></purchasePop>
	</view>
</template>

<script>
import AddressInfo from '@/components/address/AddressInfo.vue';
import purchasePop from '@/components/purchasePop.vue';
import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import filter from '@/utils/filter.js';
import { pay } from '@/utils/pay.js';
import {
mapState
} from 'vuex';
import Coupon from './components/Coupon.vue';
import GoodsItem from './components/GoodsItem.vue';
import Integral from './components/Integral.vue';
import PreMsg from './components/PreMsg.vue';
import PromoLevel from './components/PromoLevel.vue';

export default {
	components: {
		uniPopup,
		uniPopupDialog,
		purchasePop,
		AddressInfo,
		PreMsg,
		GoodsItem,
		PromoLevel,
		Coupon,
		Integral,
	},
	data() {
		return {
			imgUrl: process.env.VUE_APP_IMG_URL,
			loadFlag: false,
			preParam: {}, //上一个页面的参数
			orderAddress: {}, //下单所用的地址信息
			goodsData: [], //商品数据
			goodsRemark: [], //商品订单备注
			allData: {}, //确认下单返回的所有数据信息


			isBottomShow: true, //底部是否显示
			windowHeight: '',
			remark: '',

			ifOnShow: false,
			invoice_info: '', //发票信息
			invoice_content: '',
			invoiceId: '', //发票id

			platformCouponList: [], //平台优惠券列表
			platformCouponCode: '',
			freightCoupon: {
				list: [],
				couponCode: '',
			},

			totalDiscount: '',
			no_good_info: {},
			timer: '',
			isVatInvoice: true,
			orderSn: '', //订单号，目前阶梯团付尾款的时候会用到
			check_agreement: false,
			show_check_icon: false,
			isAloneBuy: false, //拼团是否单独购买
			spellTeamId: 0,
			spreaderMemberId: 0, //专门的推手分享ID设置

			overFlowInterval: true,
			//虚拟商品的相关字段
			isVG: null,
			virtualPre: [],
			reserveInfoList: [],
			isPreventClick: false,

			exceptionProList: [],
			exState: 0,
			exStateTxt: '',
			showState: '',
			filter,
			submitDisable: false,

			integral: 0,
			integralInfo: {},
			storeId: 0,
			isO2o: false,

		}
	},
	async onLoad(option) {
		setTimeout(() => {
			uni.setNavigationBarTitle({
				title: this.$L('确认订单')
			})
		}, 0);

		this.preParam = this.$Route.query
		this.orderSn = this.$Route.query.orderSn
		this.isAloneBuy = this.$Route.query.isAloneBuy
		this.spellTeamId = this.$Route.query.spellTeamId
		this.spreaderMemberId = uni.getStorageSync('spreaderId')

		this.getSetting()

		const invoice_msg = uni.getStorageSync('invoice_info')

		if (invoice_msg != '') {
			if (invoice_msg.invoice_title) {
				this.invoice_info = invoice_msg.invoice_title + ' '
				this.invoice_content = invoice_msg.invoice_content
			} else {
				this.invoice_info = invoice_msg.company_name + ' '
				this.invoice_content = invoice_msg.invoice_content
			}
			this.invoiceId = invoice_msg.invoiceId
		}


		uni.onWindowResize(function (res) {
			let windowHeight = uni.getSystemInfoSync().windowHeight
			if (res.size.windowHeight < this.windowHeight) {
				this.isBottomShow = false
			} else {
				this.isBottomShow = true
			}
		})


	},
	computed: {
		...mapState(['hasLogin', 'userInfo', 'userCenterData']),
		...mapState('location', ['location_variant']),

		noDepositState() {
			let {
				promotionType,
				ladderGroupInfo = {},
				presellInfo = {}
			} = this.allData
			return !((promotionType == 105 && ladderGroupInfo.ladderGroupState == 101) ||
				(promotionType == 103 && (presellInfo.presellState == 101 || presellInfo.type == 1)))

		},

		topShowType() {

			let typeSet = this.goodsData.map(i => i.deliverMethod)
			let hasAddress = typeSet.some(i => i == 1)
			let hasPickup = typeSet.some(i => i == 2)
			return {
				hasAddress,
				hasPickup
			}
		},

		submitStateVar() {

			let submitText = '提交订单'
			let submitState = true

			if (this.goodsData.length > 0) {
				for (const goodsItem of this.goodsData) {
					for (const productItem of goodsItem.productList) {
						const { goodsName, price } = productItem
						console.log('[goods]', goodsName, price)
						if (price == 1 && goodsName.indexOf('人气') > 0 && this.allData.totalAmount > 0) {
							return {
								submitText: '无法购买',
								submitState: false,
								disableReason: '人气票只支持领券购买',
							}
						}

					}

				}
			}

			return {
				submitText,
				submitState: submitState && !this.submitDisable
			}
		},

		totalExpressFee() {
			let expressList = this.goodsData.map(i => Number(i.originalExpressFee ?? 0))
			return expressList.reduce((prev, current) => prev + current, 0)
		}
	},
	onHide() {
		this.ifOnShow = true
	},

	onUnload() {
		//清除定时器
		if (this.timer) {
			clearTimeout(this.timer)
			this.timer = null
		}
	},
	onShow() {
		if (this.ifOnShow) {
			const is_need_invoice = uni.getStorageSync('is_need_invoice')
			const invoice_msg = uni.getStorageSync('invoice_info')
			if (!is_need_invoice) {
				this.invoice_info = this.$L('不需要发票')
			}
			if (invoice_msg != '') {
				if (invoice_msg.invoice_title) {
					this.invoice_info = invoice_msg.invoice_title + ' '
					this.invoice_content = invoice_msg.invoice_content
				} else {
					this.invoice_info = invoice_msg.company_name + ' '
					this.invoice_content = invoice_msg.invoice_content
				}
				this.invoiceId = invoice_msg.invoiceId
			}
		}
		if (this.showState == 'from_address') {
			this.getAddressList(2)
		}

		if (this.$refs.deliverTypeRef) {
			this.$refs.deliverTypeRef.forEach(intance => intance.show())
		}
	},
	//该生命周期，不一定所有终端都能触发
	onBackPress() {
		uni.removeStorageSync('goodsRemark')
		uni.removeStorageSync('invoice_info')
		uni.removeStorageSync('is_need_invoice')
	},

	methods: {

		getSysSettingApi(names) {
			return this.$request({
				url: 'v3/system/front/setting/getSettings',
				data: { names }
			})
		},

		//获取配置(o2o模块是否开启)
		async getSetting() {

			this.getAddressList(1)
		},

		//获取地址列表
		async getAddressList(type = 1) {

			let data = {}

			const res = await this.$request({
				url: 'v3/member/front/memberAddress/list',
				method: 'GET',
				data
			})

			if (res.state !== 200) {
				this.$api.msg('获取地址错误', 'error')
				return
			}

			if (res.data.list.length > 0) {

				let filterdList = res.data.list.filter(i => this.o2o_storeId ? i.deliver : true)

				if (uni.getStorageSync('addressId')) {
					let addressID = uni.getStorageSync('addressId')
					let used_index = filterdList.findIndex(
						(i) => i.addressId == addressID
					)
					if (used_index > -1) {
						this.orderAddress = filterdList[used_index]
					}
				} else {
					let defaultAddress = filterdList.find(i => i.isDefault == 1)
					if (defaultAddress) {
						this.orderAddress = defaultAddress
					} else {
						this.orderAddress = filterdList[0]
					}
				}
			} else {
				this.orderAddress = {}
			}

			if (type == 1) {
				this.confirmOrder(type)
			}
		},


		setSubmitState(bool) {
			this.submitDisable = bool
		},


		//阶梯团定金协议点击事件
		checkAgrement() {
			this.check_agreement = !this.check_agreement
			this.show_check_icon = this.check_agreement ?
				true :
				false;
		},
		agreement(type) {
			if (type == 'ladder') {
				this.$Router.push('/standard/ladder/agreement/agreement')
			} else if (type == 'preSale') {
				this.$Router.push('/standard/presale/agreement/agreement')
			}
		},
		operateAddress() {
			this.showState = 'from_address'
			this.$Router.push({
				path: `/pages/address/list`,
				query: {
					source: 1,
					sourceId: this.orderAddress.addressId != undefined ?
						this.orderAddress.addressId : ''
				}
			})
		},

		moveHandle() { },

		//选择积分
		selectIntegral(int) {
			this.integral = int
			this.confirmOrder(2)
		},


		//选择店铺优惠券
		store_red(couponCode, item) {
			item.storeCouponCode = couponCode
			this.confirmOrder(2)
		},


		//选择平台优惠券
		platform_red(platformCouponCode) {
			this.platformCouponCode = platformCouponCode
			this.confirmOrder(2)
		},

		//选择运费券
		freight_red(couponCode) {
			this.freightCoupon.couponCode = couponCode
			this.confirmOrder(2)
		},

		returnLastPage(state) {
			this.$Router.back(1)
		},

		// 输入订单备注信息
		inputRemark(e) {
			let index = e.currentTarget.dataset.index
			let val = e.detail.value
			this.goodsRemark[index] = val
			uni.setStorageSync('goodsRemark', JSON.stringify(this.goodsRemark))
		},



		goodsDataParam(from) {
			let storeInfoList = []

			for (let item of this.goodsData) {

				let storeitem = {
					storeId: item.storeId,
					deliverMethod: item.deliverMethod,
					shopType: item.shopType
				}

				if (item.storeCouponCode) {
					storeitem.storeCouponCode = item.storeCouponCode
				}

				if (this.invoiceId) {
					storeitem.invoiceId = this.invoiceId
					storeitem.invoiceContent = this.invoice_content
				}

				if (item.remark) {
					storeitem.remark = item.remark
				}

				if (item.storeCouponCode) {
					storeitem.storeCouponCode = item.storeCouponCode
				}
				else if (item.deliverMethod == 2) {
					if (from == 'check') {
						if (!item.pointId && item.shopType == 1) {
							this.$api.msg('请选择自提点')
							return null
						}
						if (!item.pickupTime) {
							this.$api.msg('请选择自提时间')
							return null
						}
					}

					storeitem.pickupTime = item.pickupTime
					item.pointId && (storeitem.pointId = item.pointId)
					item.startTime && (storeitem.startTime = item.startTime)
					item.endTime && (storeitem.endTime = item.endTime)
					item.day && (storeitem.day = item.day)
				}
				storeInfoList.push(storeitem)
			}
			return storeInfoList
		},



		//用于切换地址，使用优惠券，获取信息，运费等
		confirmOrder(type) {
			let goodsRemark = uni.getStorageSync('goodsRemark')
			if (goodsRemark) {
				goodsRemark = JSON.parse(goodsRemark)
			}
			const {
				preParam
			} = this
			let param = {}
			let url = this.orderSn ?
				'v3/business/front/orderOperate/balanceConfirm' :
				'v3/business/front/orderOperate/confirm'
			param.url = url
			param.method = 'POST'
			param.data = {
				addressId: this.orderAddress.addressId,

				isAloneBuy: this.isAloneBuy, //拼团商品是否单独购买
				storeInfoList: this.goodsDataParam('confirm'),
				source: type
			}
			param.header = {
				'Content-Type': 'application/json'
			}

			this.orderSn && (param.data.orderSn = this.orderSn)
			this.platformCouponCode && (param.data.platformCouponCode = this.platformCouponCode)
			this.freightCoupon.couponCode && (param.data.couponFreightCode = this.freightCoupon.couponCode)

			if (this.spellTeamId != 0) {
				param.data.spellTeamId = this.spellTeamId
			}
			if (this.integral > 0) {
				param.data.integral = this.integral
			}

			if (preParam.ifcart == 1) {
				//来自于购物车
				param.data.isCart = true

				if (this.$Route.query.storeId) {
					param.data.storeId = this.$Route.query.storeId
				}
				let { latitude, longitude } = this.orderAddress
				if (latitude && longitude) {
					param.data.latitude = latitude
					param.data.longitude = longitude
				}


			} else {
				//立即购买
				param.data.productId = preParam.productId
				param.data.number = preParam.numbers
				param.data.isCart = false
			}

			if (preParam.storeId) {
				param.data.storeId = preParam.storeId;
			}

			this.$request(param).then((res) => {
				if (res.state == 200) {
					let result = res.data
					console.log('[confirmOrder]', result)

					this.allData = result

					if (type == 1) {
						this.goodsData = result.storeGroupList.map(item => {
							let additional = {}

							additional.deliverMethod = item.defaultDeliveryMethod

							additional.storeCouponCode = item.storeCouponCode = item.availableCouponList.filter(item => item.checked).map(sub => sub.couponCode).join(',')

							if (!goodsRemark) {
								this.goodsRemark.push('')
							}
							return {
								...item,
								...additional
							}
						})
					} else {
						let tmpList = result.storeGroupList
						this.goodsData.map((item, index) => {
							if (goodsRemark) {
								this.$set(item, 'remark', goodsRemark[index] ? goodsRemark[index] : '')
							} else {
								this.goodsRemark.push('')
							}
							item.originalExpressFee = tmpList[index].originalExpressFee
							item.totalDiscount = tmpList[index].totalDiscount
							item.availableCouponList = tmpList[index].availableCouponList
						})
					}
					this.isVatInvoice = result.isVatInvoice
					this.totalDiscount = result.totalDiscount
					//处理平台优惠券
					this.platformCouponList = result.availableCouponList
					this.platformCouponCode = this.platformCouponList.find(item => item.checked)?.couponCode
					//处理运费券
					this.freightCoupon.list = result.availableFreightCouponList
					this.freightCoupon.couponCode = this.freightCoupon.list.filter(item => item.checked)?.toString()
					this.loadFlag = true
					//积分信息
					this.integralInfo.memberIntegral = this.allData.memberIntegral
					this.integralInfo.integralScale = this.allData.integralScale


					// 虚拟商品相关字段
					if (type == 1) {
						this.isVG = result.isVirtualGoods
						if (this.orderSn) {
							this.virtualPre = result.orderReserveList
						} else {
							this.virtualPre = result.reserveNameList.map((item) => {
								item.reserveValue = ''
								return item
							})
						}
					}
				} else {
					this.$api.msg(res.msg)
				}
			})
		},


		clearFailureGoods() {
			let param = {}
			param.url = 'v3/business/front/cart/emptyInvalid'
			param.method = 'POST'
			this.$request(param).then((res) => {
				if (res.state == 200) {
					this.submit()
				}
			})
		},

		submitOrder() {

			if (!this.submitStateVar.submitState) {
				return
			}

			let {
				testConfirmOrder,
				$preventMutiClick
			} = this
			$preventMutiClick(testConfirmOrder, this)
		},


		//确认订单前，检验商品是否可结算
		testConfirmOrder(continueExec) {

			const {
				preParam
			} = this
			let param = {}

			param.method = 'POST'
			param.data = {}
			param.header = {
				'Content-Type': 'application/json'
			}
			this.reserveInfoList = []

			if (this.isVG == 1) {

				if (this.isO2o) {
					let { addressId } = this.orderAddress
					if (this.o2o_methodType == 1 && !addressId) {
						this.$api.msg(this.$L('请设置收货地址'))
						continueExec()
						return
					}
					param.data.addressId = this.orderAddress.addressId
				} else {
					if (!this.orderAddress.addressId && this.topShowType.hasAddress) {
						this.$api.msg(this.$L('请设置收货地址'))
						continueExec()
						return
					}
				}
			}


			if (this.isVG == 2 && !this.orderSn) {
				let reserveInfoList = this.$refs.preMsgRef.handlePreInfo()
				if (!reserveInfoList) {
					continueExec()
					return
				}
				this.reserveInfoList = reserveInfoList
				param.data.reserveInfoList = reserveInfoList
			}

			if (
				this.allData.promotionType == 105 &&
				this.allData.ladderGroupInfo.ladderGroupState == 101
			) {
				//阶梯团付定金阶段
				if (!this.check_agreement) {
					continueExec()
					this.$api.msg(this.$L('请同意阶梯团定金协议!'))
					return false
				}
			} else if (
				this.allData.promotionType == 103 &&
				this.allData.presellInfo.presellState == 101 &&
				this.allData.presellInfo.type != 2
			) {
				//阶梯团付定金阶段
				if (!this.check_agreement) {
					continueExec()
					this.$api.msg(this.$L('请同意预售定金协议!'))
					return false
				}
			}

			let storeInfoList = this.goodsDataParam('check')
			if (!storeInfoList) {
				continueExec?.()
				return
			}
			param.data.storeInfoList = storeInfoList

			if (this.orderSn) {
				//阶梯团付尾款时用
				param.url = 'v3/business/front/orderOperate/balanceConfirm'
				preParam.isCart = 'false' //目的是要让param.data.isCart为false
				param.data.source = 2
				param.data.orderSn = this.orderSn
			} else {
				param.url = 'v3/business/front/orderOperate/check'
			}

			if (preParam.ifcart == 1) {
				param.data.isCart = true
			} else {
				let {
					price
				} = this.goodsData[0].productList[0]
				let {
					promotionId,
					promotionType
				} = this.allData

				param.data.isCart = false
				param.data.productId = preParam.productId
				param.data.number = preParam.numbers
				param.data.promotionId = promotionId
				param.data.promotionType = promotionType
				param.data.platformCouponCode = this.platformCouponCode
				param.data.couponFreightCode = this.freightCoupon.couponCode

				//针对阶梯团，预售的producPrice传值的处理
				switch (promotionType) {
					case 103: {
						let {
							type,
							firstPrice,
							secondPrice,
							presellState
						} =
							this.allData.presellInfo

						switch (type) {
							case 1: {
								if (presellState == 101) {
									param.data.productPrice = firstPrice
								} else {
									param.data.productPrice = secondPrice
								}
								break
							}
							case 2: {
								param.data.productPrice = price
								break
							}
						}

						break
					}

					case 105: {
						let {
							firstPrice,
							secondPrice,
							remainAmount,
							ladderGroupState
						} =
							this.allData.ladderGroupInfo

						if (ladderGroupState == 101) {
							param.data.productPrice = firstPrice
						} else {
							param.data.productPrice = secondPrice
						}

						break
					}

					default: {
						param.data.productPrice = price
						break
					}
				}

				if (this.isAloneBuy && promotionType == '102') {
					delete param.data.promotionId
					delete param.data.promotionType
				}

				if (this.spellTeamId != 0) {
					param.data.spellTeamId = this.spellTeamId
				}
			}


			param.data.isAloneBuy = this.isAloneBuy
			this.$request(param).then((res) => {
				if (res.state == 267) {
					this.exState = res.data.state
					this.exceptionProList = res.data.productList
					this.exStateTxt = res.data.stateValue
					if (
						this.exState == 7 ||
						this.exState == 4 ||
						this.exState == 3 ||
						this.exState == 2 ||
						this.exState == 1
					) {
						this.$refs.purchasePop.open(0)
					} else if (this.exState == 5) {
						this.$refs.purchasePop.open(1)
					} else {
						this.$api.msg(res.data.stateValue, 'error')
					}

					continueExec()
				} else if (res.state == 200) {
					this.submit(continueExec)
				} else {
					this.$api.msg(res.msg)
					continueExec()
				}
			})
		},

		delNext() {
			this.clearFailureGoods()
		},

		goNext() {
			this.submit()
		},

		clearInfoStorage() {
			uni.removeStorageSync('goodsRemark')
			uni.removeStorageSync('invoice_info')
			uni.removeStorageSync('is_need_invoice')
		},


		//根据收货地址id获取总的运费
		changeAddress(selAddress) {
			this.orderAddress = selAddress
			this.confirmOrder(2)
			uni.setStorageSync('addressId', this.orderAddress.addressId)
		},


		getOrderFrom() {
			let order_from = 0
			// #ifdef H5
			order_from = 2
			// #endif

			//app-1-start












			//app-1-end
			//wx-2-start
			// #ifdef MP-WEIXIN
			order_from = 5
			// #endif
			//wx-2-end












			return order_from
		},


		//提交订单
		submit(continueExec) {
			this.order_from = this.getOrderFrom()

			const {
				preParam
			} = this
			let param = {}
			param.method = 'POST'
			param.header = {
				'Content-Type': 'application/json'
			}
			console.log('coupon:', this)
			param.data = {
				platformCouponCode: this.platformCouponCode,
				couponFreightCode: this.freightCoupon.couponCode,
				storeCouponCode: this.storeCouponCode,
				storeInfoList: this.goodsDataParam('submit'),
				orderFrom: this.order_from,
				reserveInfoList: this.reserveInfoList,
				isAloneBuy: this.isAloneBuy,
				addressId: this.orderAddress.addressId
			}

			if (this.orderSn) {
				param.url = 'v3/business/front/orderOperate/balanceSubmit'
				preParam.isCart = 'false'
				param.data.source = 2
				param.data.orderSn = this.orderSn
			} else {
				param.url = 'v3/business/front/orderOperate/submit'
				param.data.source = 3
			}
			if (preParam.ifcart == 1) {
				//来自于购物车
				param.data.isCart = true

			} else {
				param.data.productId = JSON.parse(preParam.productId)
				param.data.number = preParam.numbers
				param.data.isCart = false
				if (this.spellTeamId != 0) {
					param.data.spellTeamId = this.spellTeamId
				}
			}
			if (this.integral > 0) {
				param.data.integral = this.integral
			}

			this.$request(param).then((res) => {
				if (res.state == 200) {
					let need_pay = res.data.needPay
					let paySn = res.data.paySn
					uni.showLoading({
						title: '正在提交订单'
					})
					this.queryPayState(paySn, continueExec)
				} else {
					continueExec?.()
					this.$api.msg(res.msg)
				}
			})
		},

		async queryPayState(paySn, continueExec) {
			const res = await this.getPayInfo(paySn, continueExec)
			if (res.state == 267) {
				uni.removeStorage({
					key: 'currentPointSite'
				})
				uni.hideLoading()
				if (this.timer) {
					clearTimeout(this.timer)
					this.timer = null
				}
				// setTimeout(() => this.$api.msg(this.$L('投票成功，给个好评吧❤️'), 'success'), 500)

				let data = {
					orderSn: paySn,
				}
				this.$request({
					url: 'v3/business/front/orderInfo/getShowQRCode',
					data
				}).then((res) => {
					console.log('getQRcode:rsp=', res)
					if (res.state == 200) {
						this.newImg = res.msg
						setTimeout(() => this.$api.msg(this.$L('感恩支持!快去领取福利吧～'), 'success'), 500)
						setTimeout(() => {
							this.$Router.push({
								path: '/pages/order/tradeSuccess',
								query: {
									sourceType: "renqiTradeSuccess",
									orderSn: paySn
								}
							})
						}, 1000)

					} else {
						setTimeout(() => this.$api.msg(this.$L('投票成功，给个好评吧❤️'), 'success'), 500)
						this.$Router.push({
							path: '/pages/order/list',
							query: {
								state: 5
							}
						})

					}


				})

				// /pages/order/tradeSuccess?orderSn=400003000008&sourceType=tradeSuccess

				this.clearInfoStorage()
			} else if (res.data.dealState == 3 || res.data.dealState == 2) {
				uni.hideLoading()
				if (this.timer) {
					clearTimeout(this.timer)
					this.timer = null
				}
				if (res.data.dealState == 2) {
					continueExec?.()
					setTimeout(() => this.$api.msg(res.data.failReason || this.$L('提交订单失败，请稍后重试'), 'error'),
						500)
				} else {
					this.$api.msg(this.$L('发起支付请求...'), 'success')
					if (!this.orderSn) {
						this.$sldStatEvent({
							behaviorType: 'buy',
							paySn
						})
					}
					uni.removeStorage({
						key: 'currentPointSite'
					})
					// #ifdef MP-WEIXIN
					
					pay({
						paySn: paySn,
						payMethod: 'wx', // 支付方式，这里使用微信支付 wx/balance/alipay
						payType: 'MINIAPP', // 支付类型，这里使用小程序支付 MINIAPP/JSAPI
						success: (res) => {
							console.warn('支付成功', res)
							setTimeout(() => {
								this.$Router.replace({
									path: '/pages/order/tradeSuccess',
									query: {
										orderSn: paySn,
										sourceType: 'tradeSuccess'
									}
								})
								this.clearInfoStorage();
							}, 1000)
							
						},
						fail: (res) => {
							console.warn('支付失败', res)
							setTimeout(() => this.$api.msg(res, 'error'), 500)
						}
					})

					// #endif
					// #ifndef MP-WEIXIN
					this.$Router.replace({
						path: '/pages/order/pay',
						query: {
							paySn,
							payMethodType: 'create'
						}
					})
					this.clearInfoStorage()
					// #endif
					
				}
			} else {
				Promise.resolve().then(() => {
					this.timer = setTimeout(() => this.queryPayState(paySn, continueExec), 200)
				})
			}
		},

		//获取订单支付数据
		async getPayInfo(paySn, continueExec) {
			let _this = this
			let param = {}
			param.url = 'v3/business/front/orderPay/payInfo'
			param.method = 'get'
			param.data = {
				paySn: paySn,
				payFrom: 1
			}
			try {
				const result = await this.$request(param)
				if (result.state == 200 || result.state == 267) {
					return result
				} else {
					uni.hideLoading()
					setTimeout(() => this.$api.msg(result.msg, 'error'), 500)
					continueExec()
					return {}
				}
			} catch (err) {
				uni.hideLoading()
				setTimeout(() => this.$api.msg(err.message, 'error'), 500)
				continueExec()
				return {}
			}
		},

		//跳转店铺详情页面
		goStoreDetail(item) {
			this.$Router.push({ path: '/standard/store/shopHomePage', query: { vid: item.storeId } })
		},


		//备注输入框聚焦
		handleFocus() {
			//wx-4-start
			// #ifdef MP
			this.isBottomShow = false
			// #endif
			//wx-4-end
		},
		//失去焦点
		handleBlur() {
			this.isBottomShow = true
		},
		// 跳转我的发票页面
		toInvoice() {
			this.showState = 'from_invoice'
			this.$Router.push({
				path: '/pages/invoice/myInvoice',
				query: {
					isVatInvoice: this.isVatInvoice ? 1 : 0
				}
			})
		},

		delivery_select(type, item, data) {
			switch (type) {
				case 'type': {
					item.deliverMethod = data.type
					this.confirmOrder(2)
					break
				}
				case 'time': {
					let {
						day,
						time,
						info
					} = data

					item.day = day.day
					if (info.timePeriodType == 2 || info.timePeriodType == 1) {
						item.pickupTime = `${day.year}-${day.day} ${time.startTime}`
						item.startTime = time.startTime
					} else {
						item.pickupTime = `${day.year}-${day.day} ${time.startTime}-${time.endTime}`
						item.startTime = time.startTime
						item.endTime = time.endTime
					}
					break
				}
				case 'site': {
					item.pointId = data.site.pointId
					if (data.site.period) {
						item.pickupTime = data.site.period
					}
					break
				}
			}
		},

	}
}
</script>

<style lang="scss">
page {
	background: $bg-color-split;
	padding-bottom: 100rpx;
	/* wx-3-start */
	/* #ifdef MP */
	padding-bottom: calc(100rpx + env(safe-area-inset-bottom));
	/* #endif */
	/* wx-3-end */
	width: 750rpx;
	margin: 0 auto;
}

.a-bg {
	position: absolute;
	left: 0;
	bottom: 0;
	display: block;
	width: 100%;
	height: 7rpx;
}



.preSale_desc {
	margin-top: 18rpx;
	font-size: 24rpx;
	font-family: PingFang SC;
	font-weight: 500;
	color: #949494;
}


.goods-section {
	margin-top: 20rpx;
	background: #fff;
	box-sizing: border-box;

	.store_list {
		width: 100%;
	}

}


.yt-list {
	margin-top: 20rpx;
	background: #fff;

	&.order_remark {
		padding: 10rpx 20rpx 20rpx;

		.title {
			color: $main-font-color;
			font-size: 28rpx;
			line-height: 32rpx;
		}

		.placeholder {
			color: $main-third-color;
			font-size: 24rpx;
		}

		.content {
			width: inherit;
			height: 103rpx;
			background: rgba(245, 245, 245, 1);
			border-radius: 6rpx;
			padding: 20rpx;
			color: $main-second-color;
			font-size: 26rpx;
			margin-top: 10rpx;
		}
	}
}

.store_info {
	margin-top: 0;
}

.yt-list-cell-giveaway {
	padding: 20rpx;
	line-height: 60rpx;

	&.b-b:after {
		left: 20rpx;
	}

	position: relative;

	.cell-tit {
		flex: 1;
		font-size: 28rpx;
		color: #666;
		margin-right: 20rpx;
	}

	.giveaway_list {
		padding: 30rpx 20rpx;
		background: #f5f5f5;
		border-radius: 6px 6px 6px 6px;
		font-size: 24rpx;

		.giveaway_item {
			display: flex;
			align-items: center;
			justify-content: space-between;
		}

		.giveaway_item_left {
			display: flex;
			align-items: center;

			.giveaway_item_index {
				color: #999999;
			}

			.giveaway_item_name {
				color: #333333;
				max-width: 400rpx;
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
				display: inline-block;
			}
		}

		.giveaway_item_number {
			float: right;
		}
	}
}

.yt-list-cell {
	display: flex;
	align-items: center;
	padding: 20rpx;
	line-height: 60rpx;
	position: relative;

	.iconfont {
		color: #999999;
		font-size: 22rpx;
		margin-left: 19rpx;
	}

	&.cell-hover {
		background: #fafafa;
	}

	&.b-b:after {
		left: 20rpx;
	}

	.cell-icon {
		height: 32rpx;
		width: 32rpx;
		font-size: 22rpx;
		color: #fff;
		text-align: center;
		line-height: 32rpx;
		background: #f85e52;
		border-radius: 4rpx;
		margin-right: 12rpx;

		&.hb {
			background: #ffaa0e;
		}

		&.lpk {
			background: #3ab54a;
		}
	}

	.voice {
		color: #333333 !important;
		display: flex;
		align-items: center;
	}

	.cell-more {
		align-self: center;
		font-size: 24rpx;
		color: $font-color-light;
		margin-left: 8rpx;
		margin-right: -10rpx;
	}

	.cell-tit {
		flex: 1;
		font-size: 28rpx;
		color: #666;
		margin-right: 20rpx;
	}

	.cell-tip {
		font-size: 28rpx;
		color: var(--color_price);

		&.disabled {
			color: $font-color-light;
		}

		&.active {
			color: $base-color;
		}

		&.red {
			color: $base-color;
		}
	}

	&.desc-cell {
		.cell-tit {
			max-width: 90rpx;
		}
	}

	.desc {
		flex: 1;
		font-size: $font-base;
		color: $font-color-dark;
	}
}

.disable-reason {
	font-size: 20rpx;
	padding: 10rpx 30rpx 10rpx 0rpx;
	background-color: #fff;
	color: var(--color_main);
}

.footer {
	position: fixed;
	left: 0;
	right: 0;
	margin: 0 auto;
	bottom: 0;
	z-index: 60;
	width: 750rpx;
	height: calc(98rpx + env(safe-area-inset-bottom));
	padding-bottom: env(safe-area-inset-bottom);
	font-size: 30rpx;
	background-color: #fff;
	color: var(--color_main);

	.price-content {
		.should_pay {
			color: var(--color_price);

			.tit {
				color: $main-font-color;
				font-size: 30rpx;
				line-height: 30rpx;
			}

			.unit,
			.small_price {
				font-size: 24rpx;
				font-weight: bold;
				line-height: 26rpx;
			}

			.big_price {
				font-size: 30rpx;
				font-weight: bold;
				line-height: 30rpx;
			}
		}

		.promotion_total {
			color: $main-font-color;
			font-size: 22rpx;
			margin-top: 8rpx;
		}
	}

	.submit {
		width: 200rpx;
		height: 70rpx;
		background: var(--color_main_bg);
		border-radius: 35rpx;
		color: #fff;
		font-size: 30rpx;
		margin: 0 20rpx;

		&.disable {
			background: #EBEAEA;
			color: #BCBCBD;
		}
	}
}


.limVoice {
	display: block;
	max-width: 400rpx;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	margin-right: 12rpx;
}


.bottom_super {
	width: 750rpx;
	height: 100rpx;
	margin-top: 20rpx;
	padding-left: 20rpx;
	padding-right: 20rpx;
	background: linear-gradient(90deg, #FBE7CF, #EFD2A3);

	.bottom_super_left {
		image {
			position: relative;
			bottom: 4rpx;
			width: 40rpx;
			height: 40rpx;
			margin-right: 20rpx;
		}

		span {
			color: #684625;
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
		}
	}

	.bottom_super_right {
		color: #000000;
		font-size: 26rpx;
		font-family: PingFang SC;
		font-weight: bold;
	}
}

.empty_h {
	width: 750rpx;
	height: 20rpx;
}

.store_name {
	padding-left: 20rpx;
	padding-bottom: 30rpx;
	margin-top: 30rpx;
	display: flex;
	align-items: center;
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);

	.ladder_group {
		height: 32rpx;
		border-radius: 15rpx;
		line-height: 32rpx;
		padding: 0 10rpx;
		margin-right: 10rpx;
		background: linear-gradient(22deg, #fe901e 0%, #fead28 100%);
		font-size: 22rpx;
		color: #ffffff;
	}

	image {
		width: 34rpx;
		height: 32rpx;
	}

	.store_name_text {
		font-size: 32rpx;
		color: #2d2d2d;
		font-weight: bold;
		margin-left: 10rpx;
	}

	.iconfont {
		font-size: 24rpx;
		margin-left: 10rpx;
	}
}


.agreement-part {
	width: 100%;
	font-size: 24rpx;
	color: #999999;
	text-align: center;
	box-sizing: border-box;
	padding: 15rpx 0 20rpx 10rpx;

	.register_icon {
		font-size: 30rpx;
		margin-right: 5rpx;
		margin-top: 5rpx;
	}

	.agreement {
		color: var(--color_main);
		border-bottom: 1rpx solid var(--color_main);
	}
}


.uni-popup {
	z-index: 1000;
}
</style>