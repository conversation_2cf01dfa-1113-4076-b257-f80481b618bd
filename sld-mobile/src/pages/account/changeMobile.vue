<!-- 账号安全页面 -->
<template>
  <view class="container_change flex_column_start_start" :style="mix_diyStyle">
    <template v-if="step == 1">
      <view class="bind_mobile flex_row_start_center">
        {{ $L('使用已绑定手机号')
        }}{{
          userCenterData.memberMobile
            ? this.$replaceConByPosition(
                this.userCenterData.memberMobile,
                4,
                6,
                '****'
              )
            : ''
        }}{{ $L('进行身份验证') }}
      </view>
      <view class="main_content" v-if="verificationCodeCheckIsEnable == 1">
        <view class="input-item pwd_wrap flex_row_between_center" >
          <input
            type="number"
            class="sms_code"
            :value="smsCodeOri"
            maxlength="6"
            :placeholder="$L('请输入短信验证码')"
            placeholder-class="smsCodePlaceholder"
            data-key="smsCodeOri"
            @input="inputChange"
            @confirm="toLogin"
          />
          <view class="pwd-right flex_row_end_center">
            <text
              class="clear-pwd iconfont iconziyuan51"
              v-show="smsCodeOri"
              @click="clearContent('smsCodeOri')"
            ></text>
            <view
              :style="{ opacity: countDownM ? 0.3 : 1 }"
              class="sms-code-view"
              @click="getSmsCode"
            >
              <text class="sms-code">{{
                countDownM
                  ? `${countDownM}s${$L('后重新获取')}`
                  : $L('获取验证码')
              }}</text>
            </view>
          </view>
        </view>
      </view>
    </template>
    <template v-if="step == 2">
      <view class="second_main_content">
        <view class="flex_row_between_center input-item pwd_wrap">
          <input
            type="number"
            class="sms_code"
            :value="mobile"
            maxlength="11"
            :placeholder="$L('请输入新手机号')"
            placeholder-class="smsCodePlaceholder"
            data-key="mobile"
            @input="inputChange"
          />
          <view class="pwd-right flex_row_end_center">
            <text
              class="clear-pwd iconfont iconziyuan51"
              v-show="mobile"
              @click="clearContent('mobile')"
            ></text>
          </view>
        </view>
        <view
          class="flex_row_between_center input-item pwd_wrap"
          style="margin-top: 20rpx"
          v-if="verificationCodeCheckIsEnable == 1"
        >
          <input
            type="number"
            class="sms_code"
            :value="smsCode"
            maxlength="6"
            :placeholder="$L('请输入短信验证码')"
            placeholder-class="smsCodePlaceholder"
            data-key="smsCode"
            @input="inputChange"
            @confirm="toLogin"
          />
          <view class="pwd-right flex_row_end_center">
            <text
              class="clear-pwd iconfont iconziyuan51"
              v-show="smsCode"
              @click="clearContent('smsCode')"
            ></text>
            <view
              :style="{ opacity: countDownM ? 0.3 : 1 }"
              class="sms-code-view"
              @click="getSmsCode"
            >
              <text class="sms-code">{{
                countDownM
                  ? `${countDownM}s${$L('后重新获取')}`
                  : $L('获取验证码')
              }}</text>
            </view>
          </view>
        </view>
      </view>
    </template>
    <view class="confirm_btn flex_row_center_center" @click="confirm">
      {{ $L('确定') }}
    </view>
    <uni-popup ref="popup" type="dialog">
      <uni-popup-dialog
        type="input"
        :title="$L('温馨提示')"
        :content="bindTip"
        :duration="2000"
        before-close="true"
        @close="closeDialog"
        @confirm="confirmBind"
      ></uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
import uniPopup from '@/components/uni-popup/uni-popup.vue'
import uniPopupMessage from '@/components/uni-popup/uni-popup-message.vue'
import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue'
export default {
  components: {
    uniPopup,
    uniPopupMessage,
    uniPopupDialog
  },
  data() {
    return {
      mobile: '', //新手机号
      smsCodeOri: '', //手机短信验证码（原先的）
      smsCode: '', //新手机短信验证码
      step: 1, //步骤
      countDownM: 0, //短信验证码倒计时
      timeOutId: '', //定时器的返回值
      bind: '', //是否进行绑定，1-解绑，0-不解绑
      bindTip: '' //弹框内容提示
    }
  },
  computed: {
    ...mapState(['userInfo', 'userCenterData', 'verificationCodeCheckIsEnable'])
  },
  onLoad() {
    setTimeout(()=>{
      uni.setNavigationBarTitle({
        title: this.$L('身份验证')
      })
    },0);
    
  },
  methods: {
    ...mapMutations(['setUserCenterData']),
    //获取个人中心数据
    initData() {
      if (this.userInfo.member_access_token) {
        this.$request({
          url: 'v3/member/front/member/getInfo'
        })
          .then((res) => {
            if (res.state == 200) {
              this.setUserCenterData(res.data)
            } else {
              this.$api.msg(res.msg)
            }
          })
          .catch((e) => {})
      }
    },
    inputChange(e) {
      const key = e.currentTarget.dataset.key
      this[key] = e.detail.value
    },
    //清空输入的内容
    clearContent(type) {
      this[type] = ''
    },
    //获取短信验证码
    getSmsCode() {
      if (this.countDownM) {
        return
      }
      //验证新手机号
      if (this.step == 2 && !this.$checkMobile(this.mobile)) {
        return
      }
      let param = {}
      param.url = 'v3/msg/front/commons/sendVerifyCode'
      param.data = {}
      param.data.verifyType = 2
      param.data.changeType = this.step == 1 ? 'old' : 'new'
      param.data.verifyAddr =
        this.step == 1 ? this.userCenterData.memberMobile : this.mobile
      param.data.type = ''
      this.$request(param).then((res) => {
        this.$api.msg(res.msg)
        if (res.state == 200) {
          this.countDownM = 60
          this.countDown()
        } else if (res.state == 267) {
          if (this.step == 2) {
            this.step = 1
            //设置页面标题
            uni.setNavigationBarTitle({
              title: this.$L('身份验证')
            })
          }
        }
      })
    },
    //倒计时
    countDown() {
      this.countDownM--
      if (this.countDownM == 0) {
        clearTimeout(this.timeOutId)
      } else {
        this.timeOutId = setTimeout(this.countDown, 1000)
      }
    },
    //确认事件
    confirm() {
      let param = {}
      param.data = {}
      param.data.key = this.userInfo.member_access_token
      if (this.step == 1) {
        param.method = 'POST'
        param.url = 'v3/member/front/memberPassword/verifyOldMobile' 
        param.data.smsCode = this.smsCodeOri
        param.data.memberMobile = this.userCenterData.memberMobile

        if (!this.smsCodeOri.trim()) {
          this.$api.msg(this.$L('请输入验证码'))
          return
        }
      } else {
        param.method = 'POST'
        param.url = 'v3/member/front/memberPassword/editMobile'
        param.data.memberMobile = this.mobile
        param.data.smsCode = this.smsCode
        if (this.bind != '') {
          param.data.isUnbound = this.bind
        }

        if (!this.mobile.trim()) {
          this.$api.msg(this.$L('请输入手机号'))
          return
        }

        if (!this.smsCode.trim()) {
          this.$api.msg(this.$L('请输入验证码'))
          return
        }
      }

      this.$request(param).then((res) => {
        if (res.state == 200) {
          this.$api.msg(res.msg)
          this.countDownM = 0
          clearTimeout(this.timeOutId)
          if (this.step == 1) {
            this.step = 2
            //设置页面标题
            uni.setNavigationBarTitle({
              title: this.$L('修改手机号')
            })
          } else {
            //更新个人信息数据
            this.userCenterData.memberMobile = this.mobile
            this.setUserCenterData(this.userCenterData)
            //返回上级页面
            setTimeout(() => {
              this.$Router.back(1)
            }, 2000)
          }
        } else if (res.state == 267) {
          //提示是否进行绑定
          this.bindTip = res.msg
          this.$refs.popup.open()
        } else {
          this.$api.msg(res.msg)
        }
      })
    },
    //关闭弹框
    closeDialog() {
      this.bind = 0
      this.$refs.popup.close()
      //返回上级页面
      setTimeout(() => {
        this.$Router.back(1)
      }, 200)
    },
    //确认绑定
    confirmBind() {
      this.bind = 1
      this.$refs.popup.close()
      this.confirm()
    }
  }
}
</script>

<style lang="scss">
page {
  background: $bg-color-split;
  display: flex;
  flex: 1;
  height: 100%;

  margin: 0 auto;
}

.container_change {
  display: flex;
  width: 750rpx;
  flex: 1;
  .bind_mobile {
    height: 80rpx;
    width: 100%;
    width: 100%;
    background: #f8f8f8;
    padding-left: 40rpx;
    color: $main-font-color;
    font-size: 26rpx;
  }

  .second_main_content {
    display: flex;
    flex-direction: column;
    flex: 1;
    width: 100%;
    background-color: #fff;
    margin-top: 20rpx;
    padding: 20rpx 40rpx 0;
  }

  .main_content {
    display: flex;
    flex: 1;
    width: 100%;
    background-color: #fff;
    padding: 20rpx 40rpx 0;
  }
  .input-item {
    height: 100rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
    position: relative;
    width: 100%;
    .sms_code_laceholder {
      color: #949494;
      font-size: 26rpx;
    }

    .sms_code {
      font-size: 28rpx;
      color: $main-font-color;
    }

    .clear-account {
      position: absolute;
      right: 6rpx;
      top: 28rpx;
      font-size: 24rpx;
      color: #999;
    }

    .pwd-right {
      flex-shrink: 0;
      margin-left: 30rpx;
      .clear-pwd {
        font-size: 24rpx;
        color: #999;
      }

      .sms-code-view {
        border: 1px solid var(--color_main);
        padding: 14rpx;
        border-radius: 6rpx;
        line-height: 0;
        margin-left: 20rpx;

        .sms-code {
          color: var(--color_main);
          font-size: 24rpx;
          line-height: 24rpx;
        }
      }
    }

    .tit {
      height: 50upx;
      line-height: 56upx;
      font-size: $font-sm + 2upx;
      color: $font-color-base;
    }

    input {
      height: 60upx;
      font-size: $font-base + 2upx;
      color: $font-color-dark;
      width: 100%;
    }
  }

  .confirm_btn {
    position: fixed;
    width: 668rpx;
    margin: 0 41rpx;
    height: 88rpx;
    background:var(--color_main_bg);
    border-radius: 44rpx;
    left: 50%;
    bottom: 40rpx;
    transform: translateX(-375rpx);
    color: #fff;
    font-size: 36rpx;
  }
}







</style>
