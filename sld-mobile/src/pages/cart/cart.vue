<!-- 购物车 -->
<template>
	<view :style="mix_diyStyle">
		<!-- 头部start -->
		<!-- #ifdef MP -->
		<uni-nav-bar fixed="true" color="#333" :right-text="navBarProps.rightText"
			@clickRight="manageCart" :height="Number(middleHeight)+Number(middleTopHeight)" :navHeight="Number(middleHeight)+Number(middleTopHeight)" :headerPosition="true">
		<!-- #endif -->
		<!-- #ifdef H5 -->
		<uni-nav-bar fixed="true" color="#333" status-bar="true" :right-text="navBarProps.rightText" :left-text="navBarProps.leftText"
			@clickRight="manageCart" @clickLeft="manageCart" :height="44" v-if="tab_cat_list.length > 1">
		<!-- #endif -->




			<!-- #ifndef MP -->
			<view style="width: 100%;">
				<view class="tab_index_hour  flex_row_center_center">
					
			<!-- #endif -->



           <!-- #ifndef MP -->
        </view>
			</view>
		<!-- #endif -->
		<!-- #ifdef MP -->
		<view class="tab_index_left" :style="{top:middleTopHeight+'px',height:(Number(middleHeight)-10)+'px',lineHeight:(Number(middleHeight)-10)+'px',}" slot="left" @click="manageCart" v-if="navBarProps.leftText">{{navBarProps.leftText}}</view>
			<view style="width: 100%;" :style="{height:(Number(middleHeight)+Number(middleTopHeight))+'px'}">
				<view class="fixed_height" :style="{height:middleTopHeight+'px'}"></view>
				<view class="tab_index_hour  flex_row_center_center" :class="{'tab_index_hour_mp':tab_cat_list.length > 1}" :style="{right:middleRight+'px',height:(Number(middleHeight)-10)+'px'}">
					<view class="tab_index_hour_body flex_row_center_center" :style="{height:(Number(middleHeight)-10)+'px',lineHeight:(Number(middleHeight)-10)+'px',borderRadius:(Number(middleHeight)-10)+'px'}" v-if="tab_cat_list.length > 1&&topTitleFlag">
						
					</view>
					<view class="tab_index_size" :style="{height:(Number(middleHeight)-10)+'px',lineHeight:(Number(middleHeight)-10)+'px'}" v-if="(tab_cat_list.length==0||tab_cat_list.length==1)&&topTitleFlag">{{tab_cat_list.length==0 ? '购物车' :  tab_cat_list.length==1?(tab_cat_list[0]==2 ? '小时达购物车' : '购物车'):'购物车'}}</view>
				</view>
			</view>
		<!-- #endif -->
		</uni-nav-bar>
		<!-- 头部end -->
		
		<block v-if="!isloading">
		<!-- 普通购物车start -->
		<view :style="{ paddingBottom: !tempList.storeCartGroupList ? 0 : '120rpx' }" v-if="tab_index_hour == 1">
			<!-- 空白页 start-->
			<view
				v-if="(empty && tempList.invalidList && tempList.invalidList.length == 0) &&(tempList && tempList.storeCartGroupList && tempList.storeCartGroupList.length == 0)"
				class="flex_column_start_center empty_part">
				<image class="img" :src="imgUrl + 'no_cart.png'" />
				<text class="tip_con">{{ $L('这里空空如也~快去商品中心加购商品吧！') }}</text>
				<view class="ope_btn flex_row_center_center" @click="goGoodsList()">
					{{ $L('马上去逛逛') }}
				</view>
			</view>
			<!-- 空白页 end-->
			<view v-if="tempList.storeCartGroupList">
				<view class="cart-list">
					<view class="shop_list_wrap">
						<view class="shop_item" v-for="(item1, index1) in tempList.storeCartGroupList" :key="index1">
							<view class="shop_item_top">
								<text
									:class="{ item_check: true, iconfont: true, iconziyuan33: item1.checkedAll && !item1.lackAll }"
									@click="changeStoreSelect(item1)"></text>
								<text :class="{ iconfont: true, iconziyuan43: !item1.checkedAll && !item1.lackAll }"
									@click="changeStoreSelect(item1)"></text>
								<text
									:class="{ stock_not_icon: true, iconfont: true, iconziyuan43: item1.lackAll }"></text>
								<view style="display: flex; align-items: center"
									@click.stop="toShopDetail(item1.storeId)">
									<image :src="imgUrl + 'shop/shop_icon.png'" mode="aspectFit" class="shop_icon">
									</image>
									<view class="shop_name">{{ item1.storeName }}</view>
									<image :src="imgUrl + 'shop/to_right.png'" mode="aspectFit" class="to_right_icon">
									</image>
								</view>
								<view class="coupon_icon" v-if="!editFlag && item1.hasCoupon"
									@click="couponOpen(item1.storeId)">{{
									$L('领券') }}</view>
							</view>
							<!-- 购物车列表 -->
							<view v-for="(item2, index2) in item1.promotionCartGroupList" :key="index2">
								<view class="discount_left" v-if="item2.promotionDes">
									<view class="discount_icon">{{ $L('满减') }}</view>
									<view class="promotion_text">
										<view class="discount_text">
											<jyfParser :html="getParsedPromotionDes(item2.promotionDes)" :isAll="true">
											</jyfParser>
										</view>
									</view>
								</view>
								<view class="discount_activity" v-for="(item, index) in item2.cartList" :key="index">
									<CartGoodsItem :cartInfo="item" @done="handleState" />
								</view>
							</view>
						</view>
					</view>
					<!-- 失效购物车列表 -->
					<InvalidCart :invalidList="tempList.invalidList" />
				</view>
				<!-- 底部菜单栏 -->
				<view class="action-section flex_row_between_center"
					v-if="tempList.storeCartGroupList && tempList.storeCartGroupList.length > 0">
					<view class="checkbox flex_row_start_center">
						<text :class="{ item_check: true, iconfont: true, iconziyuan33: tempList.checkedAll }"
							@click="check(tempList.checkedAll)"></text>
						<text :class="{ iconfont: true, iconziyuan43: !tempList.checkedAll }"
							@click="check(tempList.checkedAll)"></text>
						<text class="check_all_tit">{{ $L('全选') }}</text>
					</view>
					<!-- 去结算样式 start -->
					<template v-if="!editFlag">
						<view class="total-box flex_row_end_center">
							<text>{{ $L('合计：') }}</text>
							<view class="price_wrap">
								<text class="unit">¥</text><text class="price_int">{{ $getPartNumber(tempList.totalAmount,
									'int') }}</text><text class="price_decimal">{{ $getPartNumber(tempList.totalAmount, 'decimal') }}</text>
							</view>
						</view>
						<button class="no-border confirm-btn flex_row_center_center" @click="createOrder">
							{{ $L('结算') }}<text class="settle_num">({{ tempList.totalCheck }})</text>
						</button>
					</template>
					<!-- 去结算样式 end -->
					<!-- 点击管理之后的样式 start -->
					<template v-if="editFlag">
						<view class="flex_row_end_center">
							<view class="move_collect flex_row_center_center" @click="batchCollect"
								v-if="tempList.isShowCollectBtn">{{
								$L('加入收藏夹') }}</view>
							<view class="del_more flex_row_center_center" @click="batchDelete('open')">{{ $L('删除所选') }}
							</view>
						</view>
					</template>
					<!-- 点击管理之后的样式 end -->
				</view>
			</view>
			<!-- 优惠券弹框 end -->
			<!-- 商品全部，部分无货弹窗 start-->
			<view id="store_no_good" v-if="store_show_no_good" @touchmove.stop.prevent="moveHandle">
				<view class="content">
					<view class="content_title">
						<text> {{ no_good_info.stateValue }}</text>
						<image @tap="hide_good_dialog" :src="imgUrl + 'order/store_no_good_cancel.png'"
							mode="aspectFit">
						</image>
					</view>
					<view class="good_list">
						<view v-for="(item, index) in no_good_info.productList" :key="index" class="good_item">
							<image :src="item.image" mode="aspectFit"></image>
							<view class="good_info">
								<view class="good_name">
									{{ item.goodsName }}
								</view>
								<view class="good_spec">
									<text>{{ item.specValues }}</text>
								</view>
								<text class="num">*{{ item.buyNum }}</text>
							</view>
						</view>
					</view>
					<view class="part_no_goods_another">
						<view class="return" @click="refreshCartInfo">
							{{ $L('确定') }}
						</view>
					</view>
				</view>
			</view>
			<!-- 商品全部，部分无货弹窗 end-->
			
			<!-- 商品全部，部分无货弹窗 end-->
			<uni-popup ref="batchDelPopup" type="dialog">
				<uni-popup-dialog type="input" :title="$L('提示')" :content="$L(`确定删除选中商品?`)" :duration="2000"
					@confirm="batchDelete('confirm')"></uni-popup-dialog>
			</uni-popup>
			<CouponModal ref="couponModal" />
		</view>
		<!-- 普通购物车end -->
		
		
					
		<!-- 推荐商品 start-->
		<recommendGoods ref="recomment_goods" @reload_cart="reload_cart" @addToCart="addToCart" />
		<!-- 推荐商品 end-->
		</block>
	</view>
</template>
<script>
	import {
		mapState,
		mapMutations
	} from 'vuex'
	import uniNumberBox from '@/components/uni-number-box/uni-number-box.vue'
	import recommendGoods from '@/components/recommend-goods.vue'
	import uniNavBar from '@/components/uni-nav-bar/uni-nav-bar.vue'
	import uniPopup from '@/components/uni-popup/uni-popup.vue'
	import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue'
	import jyfParser from '@/components/jyf-parser/jyf-parser.vue'
	import filters from '../../utils/filter.js'
	import CartGoodsItem from '@/components/cart/CartGoodsItem.vue'
	import InvalidCart from '@/components/cart/InvalidCart.vue'
	import CouponModal from '@/components/cart/CouponModal.vue'

	const system = uni.getSystemInfoSync()

	// #ifdef MP
	const menu = uni.getMenuButtonBoundingClientRect()
	// #endif

	export default {
		components: {
			uniNumberBox,
			recommendGoods,
			uniNavBar,
			uniPopup,
			uniPopupDialog,
			jyfParser,
			CartGoodsItem,
			InvalidCart,
			CouponModal
		},
		computed: {
			...mapState(['hasLogin', 'cartData', 'userInfo', 'userCenterData']),
			topHeight() {
				return system.statusBarHeight
			},
			// #ifdef MP
			middleHeight() {
				return `${menu.height + 10}`
			},
			middleTopHeight() {
				return `${Number(menu.top)}`
			},
			middleRight() {
				return `${(system.screenWidth-menu.right)+menu.width}`
			},
			midleTop() {
				return `${menu.height + 10 -29}`
			},
			// #endif
			navBarProps() {
				// #ifndef MP
				return {
					rightText: this.tab_index_hour == 1 ? this.topRightText : '',
					leftText: ''
				}
				// #endif
				// #ifdef MP
				return {
					rightText: '',
					leftText: this.tab_index_hour == 1 ? this.topRightText : ''
				}
				// #endif
			}
		},
		data() {
			return {
				editFlag: false, //是否编辑标识
				empty: false, //空白页现实  true|false
				cartList: [],
				topRightText: '', //导航右侧文字，没有数据的话不显示
				tempList: [],
				isShow: true, //重载页面
				showPrice: '',
				ifOnShow: false, //从其他页面进入时重载当前页面
				isDisabled: false, //结算按钮是否禁用
				imgUrl: process.env.VUE_APP_IMG_URL,
				settings: [], //配置信息
				store_show_no_good: false,
				no_good_info: '',
				filters,
				tab_cat_list: [], //  标题判断
				tab_index_hour: 0,
				isloading: true,
				topTitleFlag: false,
				}
		},
		onHide() {
			},
		onShow() {
			//wx-1-start
			//统计埋点方法--针对微信小程序
			// #ifdef MP-WEIXIN
			let url = process.env.VUE_APP_API_URL.substring(0,process.env.VUE_APP_API_URL.length - 1)
			this.$sldStatEvent({
				behaviorType: 'pv',
				pageUrl: url + '/pages/cart/cart',
				referrerPageUrl: ''
			})
			// #endif
			//wx-1-end
			this.getCartNum()
			
			if (this.hasLogin) {
				this.tab_index_hour = 1
				this.getCartData()
			} else {
				this.tab_index_hour = 1
				this.initCartData()
			}
		},
		onLoad(option) {

			uni.showLoading({
				title: this.$L('加载中！'),
				icon: 'none'
			})
			if (this.hasLogin) {
				this.getCartData()
			} else {
				this.initCartData()
			}
		},
		onReachBottom() {
			this.$refs.recomment_goods.getMoreData()
		},

		onPullDownRefresh() {
			if (this.hasLogin) {
				this.getCartData()
			} else {
				this.initCartData()
			}
		},

		watch: {
			//显示空白页
			tempList(e) {
				if (e) {
					let empty = (!e.storeCartGroupList || e.storeCartGroupList.length == 0) ? true : false
					if (this.empty !== empty) {
						this.empty = empty
					}
				} else {
					this.empty = true
				}

				if (e.storeCartGroupList && e.storeCartGroupList.length > 0) {
					this.topRightText = '管理'
				} else {
					this.topRightText = ''
				}

			}
		},

		onPageScroll(e) {
			if (e.scrollTop > 0) {
				this.closeMask()
			}
		},
		methods: {
			...mapMutations(['login', 'operateCartData']),
			moveHandle() {},
			
			// 隐藏商品无货弹窗
			hide_good_dialog() {
				this.store_show_no_good = false
			},
			refreshCartInfo() {
				this.store_show_no_good = false
				this.getCartData()
			},

			getParsedPromotionDes(promotionDes) {
				let des = promotionDes.replace(/<(.+?)>/g, (num) => (
					"<text style='color:var(--color_main)'>" +
					num.slice(1, num.length - 1) +
					'</text>'
				))

				des = des.replace(/x[\d]/g, (num) => (
					"<text style='color:var(--color_main)'>" + num + '</text>'
				))

				return des
			},

			handleState(type, target) {
				if (!this.hasLogin) {
					this.unLoginCartOpt(type, target)
					return
				}
				this.tempList = target
				this.$forceUpdate()
				this.checkNum()
			},

			initCartData() {
				//从缓存获取购物车信息
				this.tempList = uni.getStorageSync('cart_list')
				if (this.tempList) {
					this.tempList.isShowCollectBtn = false

					this.$forceUpdate()
					this.calcTotal()
				} else {
					this.tempList = {
						storeCartGroupList: [],
						invalidList: []
					}
				}
				this.isloading = false;
				uni.stopPullDownRefresh()
				uni.hideLoading()
			},

			//从购物车获取数据
			getCartData() {
				let url = 'v3/business/front/cart/cartList'
				this.$request({
					url,
					method: 'GET'
				}).then((res) => {
					uni.stopPullDownRefresh()
					if (res.state == 200) {
						this.tempList = res.data
						this.operateCartData(res.data)
						let checkedLen = 0
						let list = res.data.storeCartGroupList
						uni.hideLoading()
					} else {
						this.$api.msg(res.msg)
						uni.hideLoading()
					}
					this.isloading = false;
				})
			},

			//确认订单前，检验商品是否可结算
			testConfirmOrder() {
				const {
					preParam
				} = this
				let param = {}
				param.url = 'v3/business/front/orderOperate/check'
				param.method = 'POST'
				param.data = {}
				param.header = {
					'Content-Type': 'application/json'
				}
				param.data.isCart = true
				this.$request(param)
					.then((res) => {
						if (res.state == 267) {
							this.no_good_info = res.data
							this.store_show_no_good = true
						} else if (res.state == 200) {
							this.$Router.push({
								path: '/pages/order/confirmOrder',
								query: {
									orderType: 1,
									ifcart: 1
								},
							})
						}
					})
					.catch((e) => {
						//异常处理
					})
			},
			//全选状态处理 type, index
			check(isCheckedAll) {
				if (this.hasLogin) {
					//登录
					let cartIds = ''
					this.cartMapOperate('goods', (item2) => {
						cartIds += item2.cartId + ','
					})
					cartIds = cartIds.substring(0, cartIds.length - 1)
					let param = {}
					param.url = 'v3/business/front/cart/checkedCarts'
					param.method = 'POST'
					param.data = {
						cartIds,
						checked: isCheckedAll == 0 ? 1 : 0 //0?1:0
					}
					this.$request(param).then((res) => {
						if (res.state == 200) {
							this.tempList = res.data
						} else {
							this.$api.msg(res.msg)
						}
					})
				} else {
					//未登录
					this.unLoginCartOpt('checkedAll', {})
				}
			},
			// 点击商品、店铺状态处理
			changeStoreSelect(cartItem) {


				let _this = this
				if (this.editFlag) {
					return
				}

				if (this.hasLogin) {
					//登录
					let cartIds = ''
					cartItem.promotionCartGroupList.map((item1) => {
						item1.cartList.map((item2) => {
							cartIds += item2.cartId + ','
						})
					})
					cartIds = cartIds.substring(0, cartIds.length - 1)
					let param = {}
					param.url = 'v3/business/front/cart/checkedCarts'
					param.method = 'POST'
					param.data = {
						cartIds: cartIds,
						checked: cartItem.checkedAll ? 0 : 1
					}
					
					this.$request(param).then((res) => {
						if (res.state == 200) {
							this.tempList = res.data
							this.checkNum()
						} else {
							this.$api.msg(res.msg)
						}
					})
				} else {
					this.unLoginCartOpt('select_state', {
						subType: 'store',
						storeId: cartItem.storeId
					})
				}
			},

			// 批量删除
			batchDelete(type) {
				let cartIds = ''
				let tempArr = []
				let goodsIds = ''
				let unLoginArr = []
				let checkedNum = 0

				this.cartMapOperate('goods', (item2) => {
					if (item2.isChecked == 1) {
						checkedNum++
						cartIds += item2.cartId + ','
						tempArr.push(item2.cartId)
						unLoginArr.push(item2.productId)
					}
				})

				switch (type) {
					case 'open': {
						if (checkedNum == 0) {
							uni.showToast({
								title: this.$L('请选择删除的商品！'),
								icon: 'none'
							})
						} else {
							this.$refs.batchDelPopup.open()
						}
						break
					}
					case 'confirm': {
						this.$refs.batchDelPopup.close()

						if (this.hasLogin) {
							//登录时批量删除
							cartIds = cartIds.substring(0, cartIds.length - 1)
							let param = {}
							param.url = 'v3/business/front/cart/deleteCarts'
							param.method = 'POST'
							param.data = {
								cartIds
							}
							this.$request(param).then((res) => {
								if (res.state == 200) {
									uni.showToast({
										title: this.$L('删除成功！')
									})
									this.checkNum()
									this.getCartData()
									this.getCartNum()
								}
							})
						} else {
							this.unLoginCartOpt('batch_delete', {
								unLoginArr
							})
						}
						break
					}
				}
			},

			// 批量移入收藏夹
			batchCollect() {
				let cartId = ''
				let checkedNum = 0
				this.cartMapOperate('goods', (item2) => {
					if (item2.isChecked == 1) {
						checkedNum++
						cartId += item2.cartId + ','
					}
				})
				if (checkedNum == 0) {
					uni.showToast({
						title: this.$L('请选择收藏的商品！'),
						icon: 'none'
					})
				} else {
					cartId = cartId.substring(0, cartId.length - 1)
					let param = {}
					param.url = 'v3/business/front/cart/moveToCollection'
					param.method = 'POST'
					param.data = {
						cartIds: cartId
					}
					this.$request(param).then((res) => {
						if (res.state == 200) {
							uni.showToast({
								title: this.$L('收藏成功')
							})
							this.curOperateId = ''
							this.tempList = res.data
							this.tempList.isShowCollectBtn = true
							this.getCartNum()
						}
					})
				}
			},
			// 计算结算数量及改变按钮样式
			checkNum() {
				let checkedLen = 0
				this.cartMapOperate('goods', (item2) => {
					if (item2.isChecked == 1 && item2.productState == 1) {
						checkedLen++
					}
				})
				this.isDisabled = checkedLen == 0 ? true : false
			},

			//创建订单
			createOrder() {
				let _this = this
				if (this.hasLogin) {
					//登录跳转确认下单页
					let settle_num = 0
					this.cartMapOperate('goods', (item2) => {
						if (item2.isChecked == 1 && item2.productState == 1) {
							settle_num++
						}
					})
					if (settle_num == 0) {
						uni.showToast({
							title: _this.$L('您还没有选中商品'),
							icon: 'none'
						})
					} else {
						this.testConfirmOrder()
					}
				} else {
					//未登录提示登录
					uni.showModal({
						title: _this.$L('提示'),
						content: _this.$L('需要先登录才能下单哦~'),
						confirmText: _this.$L('去登录'),
						cancelText: _this.$L('我再看看'),
						success: (res) => {
							if (res.confirm) {
								let url = _this.$Route.path
								const query = _this.$Route.query
								uni.setStorageSync('fromurl', {
									url,
									query
								})
								_this.$Router.push('/pages/public/login')
							}
						}
					})
				}
			},
			//马上去逛逛事件
			goGoodsList() {
				this.$Router.push(`/standard/product/list`)
			},
			//管理购物车数据
			manageCart() {
				this.editFlag = !this.editFlag
				if (
					this.tempList &&
					this.tempList.storeCartGroupList &&
					this.tempList.storeCartGroupList.length > 0
				) {
					if (this.editFlag) {
						this.topRightText = this.$L('完成')
						this.is_show_mask = false
						this.cartMapOperate('goods', (item2) => {
							if (item2.productState == 3) {
								item2.productState = 1
								item2.isChecked = 1
								this.$forceUpdate()
							}
						})
					} else {
						this.topRightText = this.$L('管理')
						this.is_show_mask = true
						this.cartMapOperate('goods', (item2) => {
							if (item2.buyNum > item2.productStock) {
								item2.productState = 3
								item2.isChecked = 0
							}
						})
					}
					this.checkNum()
				} else {
					this.topRightText = ''
				}
			},

			reload_cart(val) {
				if (this.hasLogin) {
					this.getCartData()
				} else {
					this.initCartData()
				}
				this.getCartNum()
				},

			// 点击关闭长按蒙层
			closeMask(e) {
				this.is_show_mask = false
			},
			addToCart(val) {
				this.initCartData()
				this.$forceUpdate()
				this.getCartNum()
				},
			//获取购物车数据
			getCartNum() {
				this.$getCartNum()
			},
			

			// 去店铺详情
			toShopDetail(storeId) {
				this.$Router.push({
					path: '/standard/store/shopHomePage',
					query: {
						vid: storeId
					}
				})
			},

			// 打开优惠券弹框,获取店铺优惠券列表
			couponOpen(storeId) {
				this.$refs.couponModal.open(storeId)
			},

			//计算总价
			calcTotal() {
				// 未登录时计算合计价格,结算数量
				let subTotal = 0
				let checkedNum = 0
				this.cartMapOperate('goods', (item2) => {
					if (item2.isChecked == 1) {
						subTotal += item2.buyNum * Number(item2.productPrice)
						checkedNum++
					}
				})

				this.tempList.totalAmount = subTotal.toFixed(2)
				this.tempList.totalCheck = checkedNum
			},

			unLoginCartOpt(type, {
				subType,
				cartId,
				storeId,
				productId,
				promotionCartGroupList,
				cartList,
				unLoginArr
			}) {
				// 未登录
				const {
					cartMapOperate
				} = this
				let checkedNum = 0 //选中数量
				let shopChecked = 0
				let cartNum = 0 //购物车数量
				switch (type) {
					case 'select_state': {
						if (subType == 'goods') {
							cartMapOperate(subType, (item2) => {
								if (item2.productId == productId) {
									item2.isChecked = item2.isChecked == 0 ? 1 : 0
								}
							})
						} else {
							//店铺
							cartMapOperate('group', (item) => {
								if (item.storeId == storeId) {
									item.checkedAll = !item.checkedAll
								}
							})
						}

						this.resolveUnLoginCartState(subType)

						break
					}


					case 'del': {
						cartMapOperate('goods', (item2, item1) => {
							if (item2.productId == productId) {
								item1.cartList = item1.cartList.filter(i => i.productId !== item2.productId)
							}
						})

						this.resolveUnLoginCartState('goods')

						break
					}

					case 'changeNum': {
						cartMapOperate('goods', item2 => {
							if (item2.productId == productId) {
								item2.buyNum = num - 0
							}
						})



						break
					}

					case 'batch_delete': {
						//未登录时批量删除
						if (this.tempList.checkedAll == true) {
							uni.removeStorageSync('cart_list')
							this.tempList.storeCartGroupList = []
						} else {
							cartMapOperate('store', (item) => {
								unLoginArr.forEach((v) => {
									item1.cartList = item1.cartList.filter(k => k.productId != v)
								})
							})
						}

						this.resolveUnLoginCartState('del')
						break
					}

					case 'checkedAll': {
						this.tempList.checkedAll = !this.tempList.checkedAll
						this.resolveUnLoginCartState('checkedAll')
					}
				}
				this.calcTotal()
				uni.setStorage({
					key: 'cart_list',
					data: this.tempList,
				})
			},

			cartMapOperate(type, func) {
				if (type == 'group') {
					this.tempList.storeCartGroupList.map(func)
					return
				}
				this.tempList.storeCartGroupList.map((item1) => {
					if (type == 'store') {
						item1.promotionCartGroupList.map(item2 => func(item2, item1))
					} else {
						item1.promotionCartGroupList.map((item2) => {
							item2.cartList.map(item3 => func(item3, item2))
						})
					}
				})

			},

			resolveUnLoginCartState(type) {
				let {
					storeCartGroupList
				} = this.tempList
				storeCartGroupList.map((item) => {

					item.promotionCartGroupList = item.promotionCartGroupList.filter(k => k.cartList.length > 0)

					if (type == 'store') {
						item.promotionCartGroupList.map(item2 => {
							item2.cartList.map(item3 => {
								item3.isChecked = item.checkedAll ? 1 : 0
							})
						})
					}

					if (type == 'goods') {
						item.checkedAll = item.promotionCartGroupList.every(item2 => {
							return item2.cartList.every(item3 => item3.isChecked == 1)
						})
					}

					if (type == 'checkedAll') {
						item.checkedAll = this.tempList.checkedAll
						item.promotionCartGroupList.map(item2 => {
							item2.cartList.map(item3 => {
								item3.isChecked = item.checkedAll ? 1 : 0
							})
						})
					}
				})
				storeCartGroupList = storeCartGroupList.filter(k => k.promotionCartGroupList.length > 0)
				if (type !== 'checkAll') {
					this.tempList.checkedAll = storeCartGroupList.every(k => k.checkedAll)
				}
				this.tempList.storeCartGroupList = storeCartGroupList
			},

		}
	}
</script>

<style lang="scss">
	page {
		background-color: $bg-color-split;
		width: 750rpx;
		margin: 0 auto;
	}

	.container {
		padding-bottom: 120rpx;
	}

	.tab_index_left {
		position: absolute;
		left: 30rpx;
		font-size: 28rpx;
	}

	.tab_index_size {
		font-size: 34rpx;
	}

	// #ifdef MP 
	.tab_index_hour_mp {
		position: absolute;
		margin-right: 10rpx;
	}

	// #endif

	.tab_index_hour {

		.tab_index_hour_body {
			height: 60rpx;
			background: #EEEEEE;
			border-radius: 30rpx;

			.tab_item {
				height: 100%;
				border-radius: 30rpx;
				// #ifndef MP
				padding: 0 24rpx;
				font-size: 30rpx;
				line-height: 60rpx;
				// #endif
				// #ifdef MP
				padding: 0 18rpx;
				font-size: 28rpx;
				// #endif
				color: #181818;

				&.active {
					background-color: var(--color_main);
					color: #fff;
				}
			}
		}

	}

	.cart_header {
		background: #ffffff;
		padding: 20rpx 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;


		.cart_header_text {
			display: flex;
			align-items: center;

			image {
				width: 32rpx;
				height: 32rpx;
				margin-right: 10rpx;
			}

			text {
				font-size: 28rpx;
			}
		}

		.cart_header_btn {
			font-size: 28rpx;
			position: relative;
		}

		.cart_header_xian {
			width: 1rpx;
			height: 26rpx;
			background: #D2D2D2;
			position: absolute;
			left: -41rpx;
			top: 50%;
			transform: translateY(-50%);
		}
	}

	.cart-list {
		padding: 0 20rpx;
		box-sizing: border-box;
		width: 750rpx;
		margin: 0 auto;
	}


	/* 底部栏 */
	.action-section {
		margin: 0 auto;
		/* #ifdef H5 */
		margin-bottom: calc(env(safe-area-inset-bottom) + 90rpx);
		/* #endif */
		position: fixed;
		left: 0rpx;
		bottom: 0rpx;
		right: 0;
		z-index: 95;
		display: flex;
		align-items: center;
		height: 100rpx;
		padding: 0 20rpx;
		background: #fff;
		box-shadow: 0px 0px 10px 0px rgba(153, 153, 153, 0.1);
		width: 750rpx;

		.checkbox {
			.iconfont {
				color: #bbbbbb;
				font-size: 32rpx;
			}

			.check_all_tit {
				color: #949494;
				font-size: 26rpx;
				margin-left: 15rpx;
			}
		}

		.clear-btn {
			position: absolute;
			left: 26rpx;
			top: 0;
			z-index: 4;
			width: 0;
			height: 52rpx;
			line-height: 52rpx;
			padding-left: 38rpx;
			font-size: $font-base;
			color: #fff;
			background: $font-color-disabled;
			border-radius: 0 50px 50px 0;
			opacity: 0;
			transition: 0.2s;

			&.show {
				opacity: 1;
				width: 120rpx;
			}
		}

		.total-box {
			flex: 1;
			padding-right: 40rpx;
			font-size: 26rpx;
			color: #2d2d2d;

			.price_wrap {

				.unit,
				.price_decimal {
					font-size: 24rpx;
					font-weight: bold;
					color: var(--color_price);
					margin-top: 4rpx;
				}

				.price_int {
					font-weight: bold;
					font-size: 34rpx;
					line-height: 34rpx;
					color: var(--color_price);
					margin-left: 2rpx;
				}
			}

			.coupon {
				font-size: $font-sm;
				color: $font-color-light;

				text {
					color: $font-color-dark;
				}
			}
		}

		.confirm-btn {
			width: 200rpx;
			height: 70rpx;
			background: var(--color_main_bg);
			border-radius: 35rpx;
			font-size: 28rpx;
			color: #fff;

			.settle_num {
				font-size: 28rpx;
			}
		}

		.move_collect {
			width: 200rpx;
			height: 60rpx;
			background: #ffffff;
			border: 1px solid var(--color_vice);
			border-radius: 30rpx;
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: var(--color_vice);
			line-height: 60rpx;
		}

		.del_more {
			width: 200rpx;
			height: 60rpx;
			border: 1px solid var(--color_main);
			border-radius: 30rpx;
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: var(--color_main);
			line-height: 60rpx;
			margin-left: 20rpx;
		}
	}

	/* 复选框选中状态 */
	.action-section .checkbox.checked,
	.cart-item .checkbox.checked {
		color: $uni-color-primary;
	}

	.empty_part {
		display: flex;
		flex: 1;
		width: 100%;
		height: 586rpx;
		background: #fff;
		margin-top: 20rpx;

		.img {
			width: 380rpx;
			height: 280rpx;
			margin-bottom: 37rpx;
			margin-top: 88rpx;
		}

		.tip_con {
			color: $main-third-color;
			font-size: 26rpx;
		}

		.ope_btn {
			color: var(--color_main);
			font-size: 28rpx;
			padding: 0 25rpx;
			height: 54rpx;
			background: var(--color_halo);
			border-radius: 27rpx;
			margin-top: 20rpx;
		}
	}

	.iconfont {
		font-size: 32rpx;
		color: #bbbbbb;

		&.item_check {
			color: var(--color_main) !important;

			&.color_main {
				color: var(--color_main) !important;
			}

			&.color_o2o_main {
				color: var(--color_o2o_main) !important;
			}

			&.color_price {
				color: var(--color_price) !important;
			}

			&.color_o2o_price {
				color: var(--color_o2o_price) !important;
			}
		}
	}

	.disabled_btn {
		width: 200rpx;
		height: 70rpx;
		border-radius: 35rpx;
		font-size: 28rpx;
		background: #adadad !important;

		.settle_num {
			font-size: 28rpx;
			/* margin-top: 7rpx; */
			/* padding-bottom: 6rpx; */
		}
	}

	.stock_not {
		font-size: 24rpx;
		color: #ff0d24;
		margin-right: 20rpx;
		margin-top: 8rpx;
	}

	.stock_not_enough {
		color: #666666;
	}

	.stock_not_icon {
		color: #eeeeee;
	}


	/* 领取优惠券弹框 */
	.coupon_box {
		height: 900rpx;
		background-color: #f5f5f5;
		border-radius: 15rpx 15rpx 0 0;

		.coupon_title {
			height: 90rpx;
			display: flex;
			align-items: center;
			font-size: 32rpx;
			color: #333;
			font-weight: bold;
			justify-content: space-between;
			padding: 0 20rpx 0 30rpx;
			box-sizing: border-box;
			background-color: #fff;

			.close_icon {
				width: 30rpx;
				height: 30rpx;
			}
		}

		.coupon_content_wrap {
			height: 810rpx;
			overflow-y: scroll;
			padding: 0 20rpx 120rpx 20rpx;
			box-sizing: border-box;

			.coupon_item {
				width: 100%;
				margin-top: 20rpx;

				.coupon_top {
					height: 180rpx;
					display: flex;
					align-items: center;
					background-color: #fff7f5;
					border-radius: 15rpx;
					position: relative;

					.coupon_price {
						width: 200rpx;
						font-size: 60rpx;
						color: var(--color_main);
						font-weight: bold;
						display: flex;
						justify-content: center;
					}

					.coupon_rules_wrap {
						.coupon_rule {
							font-size: 34rpx;
							color: #333;
							font-weight: bold;
						}

						.coupon_time {
							font-size: 28rpx;
							color: #999;
							margin-top: 18rpx;
							font-weight: 400;
						}
					}

					.have_apply {
						width: 135rpx;
						height: 50rpx;
						position: absolute;
						right: 28rpx;
						top: 50%;
						transform: translateY(-50%);
					}
				}

				.coupon_bottom {
					font-size: 26rpx;
					color: #333;
					padding: 20rpx 30rpx;
					box-sizing: border-box;
					background-color: #fff;
					border-radius: 15rpx;
					position: relative;

					.rule_icon {
						width: 28rpx;
						height: 28rpx;
						position: absolute;
						right: 30rpx;
						top: 50%;
						transform: translateY(-50%);
					}
				}
			}
		}
	}

	.coupon_rule_text {
		width: 581rpx;
		white-space: nowrap;
		text-overflow: ellipsis;
		overflow: hidden;
	}

	.shop_list_wrap {
		.shop_item {
			margin-top: 20rpx;
			background-color: #fff;
			border-radius: 15rpx;
			box-sizing: border-box;

			.shop_item_top {
				display: flex;
				align-items: center;
				position: relative;
				padding-bottom: 20rpx;
				border-bottom: 1rpx solid #f2f2f2;
				padding: 20rpx;
				box-sizing: border-box;

				.shop_icon {
					width: 34rpx;
					height: 32rpx;
					margin: 0 10rpx 0 20rpx;
				}

				.shop_name {
					font-size: 28rpx;
					color: #2d2d2d;
					font-weight: 600;
					margin-right: 10rpx;
				}

				.to_right_icon {
					width: 13rpx;
					height: 22rpx;
				}

				.coupon_icon {
					width: 62rpx;
					height: 30rpx;
					position: absolute;
					top: 50%;
					right: 0;
					transform: translateY(-50%);
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 22rpx;
					background: var(--color_halo);
					color: var(--color_main);
					border-bottom-left-radius: 30rpx;
					border-top-left-radius: 30rpx;
				}
			}
		}
	}

	.discount_activity {
		padding: 0 20rpx 20rpx;
		box-sizing: border-box;
	}

	.discount_left {
		margin-left: 50rpx;
		display: flex;
		align-items: center;
		margin-top: 24rpx;
		margin-bottom: 22rpx;

		.discount_icon {
			width: 62rpx;
			height: 30rpx;
			font-size: 24rpx;
			color: #fff;
			display: flex;
			justify-content: center;
			align-items: center;
			background: var(--color_main);
			border-radius: 15rpx;
			margin-right: 10rpx;
		}

		.promotion_text {
			width: 550rpx;
			height: 36rpx;
			display: flex;
			text-overflow: -o-ellipsis-lastline;
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 1;
			line-clamp: 1;
			-webkit-box-orient: vertical;

			.discount_text {
				font-size: 26rpx;
				color: #2d2d2d;
				line-height: 36rpx;
			}
		}
	}

	.discount_activity_wrap {
		display: flex;
		justify-content: space-between;
		margin-top: 20rpx;
	}


	.goods_num_wrap {
		text-align: right;
	}

	.exceed_price_wrap {
		display: flex;
		align-items: center;
	}

	#store_no_good {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	#store_no_good {
		position: fixed;
		top: 0;
		left: 0;
		width: 750rpx;
		height: 100vh;
		background-color: rgba(0, 0, 0, 0.6);
		z-index: 999;
		right: 0;
		margin: 0 auto;
	}

	#store_no_good .content {
		width: 580rpx;
		height: 773rpx;
		background-color: white;
		border-radius: 15rpx;

		.content_title {
			margin-top: 24rpx;
			margin-bottom: 24rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 30rpx;
			font-size: 32rpx;
			color: #2d2d2d;

			image {
				width: 22rpx;
				height: 22rpx;
			}
		}

		.good_list {
			height: 593rpx;
			overflow-y: scroll;
			width: 100%;

			.good_item {
				display: flex;
				align-items: center;
				padding: 30rpx;
				border-top: 1rpx solid #f2f2f2;

				image {
					width: 70rpx;
					height: 70rpx;
					border-radius: 6rpx;
				}

				.good_info {
					margin-left: 20rpx;
					position: relative;

					.good_name {
						width: 382rpx;
						font-size: 26rpx;
					}

					.good_spec {
						margin-top: 20rpx;
						font-size: 22rpx;
					}

					.num {
						position: absolute;
						bottom: 0rpx;
						right: 0rpx;
						font-size: 24rpx;
						color: #333333;
					}
				}
			}
		}

		.part_no_goods {
			width: 520rpx;
			height: 60rpx;
			font-size: 30rpx;
			color: white;

			display: flex;
			align-items: center;
			margin: 0 auto;
			border-radius: 30rpx;
			margin-top: 15rpx;

			.return {
				width: 50%;
				height: 60rpx;
				line-height: 60rpx;
				background-color: #ff8809;
				text-align: center;
				border-radius: 30rpx 0 0 30rpx;
			}

			.remove {
				width: 50%;
				height: 60rpx;
				line-height: 60rpx;
				background-color: #f90208;
				text-align: center;
				border-radius: 0 30rpx 30rpx 0;
			}
		}
	}

	.part_no_goods_another {
		width: 520rpx;
		height: 60rpx;
		font-size: 30rpx;
		color: white;
		display: flex;
		align-items: center;
		margin: 0 auto;
		border-radius: 30rpx;
		background-color: #f90208;

		.return {
			width: 100%;
			text-align: center;
		}
	}

	.uni-input-input {
		font-size: 24rpx !important;
		color: #2D2D2D;
	}
</style>