<template>
  <view class="container" :style="mix_diyStyle">
    <view class="fixed_top_status_bar"></view>
    <view
      class="integral_top"
      :style="{ backgroundImage: 'url(' + top_bg + ')' }"
    >
      <!-- #ifndef MP -->
      <image
        :src="imgUrl + 'go_back.png'"
        mode="aspectFit"
        class="go_back_icon"
        @click="goBack"
      ></image>
      <!-- #endif -->
      <text class="points_num">{{ my_points }}</text>
    </view>
    <view class="integral_main">
      <view class="integral_title">
        <view
          :class="is_default ? 'active' : 'integral_title_item'"
          @click="changeStyle(1)"
          >{{ $L('收入') }}</view
        >
        <view
          :class="!is_default ? 'active' : 'integral_title_item'"
          @click="changeStyle(2)"
          >{{ $L('支出') }}</view
        >
      </view>

      <view class="integral_item_wrap">
        <view
          class="integral_item"
          v-for="(item, index) in info_list"
          :key="index"
        >
          <view class="integral_item_title">
            <text>{{ item.typeDesc }}</text>
            <text>{{ is_default ? '+' : '-' }}{{ item.value }}</text>
          </view>
          <view class="integral_item_content">{{ item.description }}</view>
          <view class="integral_item_date">{{ item.createTime }}</view>
        </view>
        <loadingState :state="loadingState" v-if="!is_show_empty" />
      </view>
      <view class="empty_page" v-if="is_show_empty">
        <image
          :src="imgUrl + 'empty_goods.png'"
          mode="aspectFit"
          class="empty_img"
        ></image>
        <view class="empty_text">{{ $L('暂无数据') }}</view>
      </view>
    </view>
  </view>
</template>

<script>
import loadingState from '@/components/loading-state.vue'
export default {
  components: {
    loadingState
  },
  data() {
    return {
      top_bg: process.env.VUE_APP_IMG_URL + 'points_bg.png',
      is_default: true, //是否默认选择收入
      my_points: '', //我的积分
      info_list: [], //收支列表
      current: 1,
      pageSize: 10,
      loadingState: 'first_loading',
      type: 1, //收入：1，支出：2
      imgUrl: process.env.VUE_APP_IMG_URL,
      is_show_empty: false,
      hasMore: false,
      statusBarHeight: 0
    }
  },
  onLoad() {
    this.getUserPoints()
    this.getPointsList(this.type)






    var res = uni.getSystemInfoSync()
    this.statusBarHeight = res.statusBarHeight

  },
  onReachBottom() {
    if (this.hasMore) {
      this.getPointsList(this.type)
    }
  },
  methods: {
    getUserPoints() {
      let param = {}
      param.url = 'v3/member/front/integralLog/getMemberIntegral'
      param.method = 'GET'
      this.$request(param).then((res) => {
        if (res.state == 200) {
          this.my_points = res.data.memberIntegral
        }
      })
    },
    getPointsList(type) {
      let param = {}
      param.url = 'v3/member/front/integralLog/list'
      param.method = 'POST'
      param.data = {
        type,
        current: this.current,
        pageSize: this.pageSize
      }
      this.$request(param).then((res) => {
        if (this.current == 1) {
          this.info_list = res.data.list
        } else {
          this.info_list = this.info_list.concat(res.data.list)
        }
        this.hasMore = this.$checkPaginationHasMore(res.data.pagination) //是否还有数据
        if (this.hasMore) {
          this.current++
          this.loadingState = 'allow_loading_more'
        } else {
          this.loadingState = 'no_more_data'
        }
        if (this.info_list.length == 0) {
          this.is_show_empty = true
          this.$forceUpdate()
        } else {
          this.is_show_empty = false
        }
      })
    },
    // 切换收入支出
    changeStyle(type) {
      this.is_default = type == 1
      this.type = type
      this.current = 1
      this.getPointsList(this.type)
    },
    // 返回上一页
    goBack() {
      // #ifdef H5
      const pages = getCurrentPages()
      //有返回的页面则直接返回，uni.navigateBack默认返回失败之后会自动刷新页面，无法继续返回
      if (pages.length > 1) {
        this.$Router.back(1)
        return
      }
      //vue router 可以返回uni.navigateBack失败的页面，但是会重新加载
      let a = this.$Router.back(1)
      //router.go失败之后则重定向到个人中心
      if (a == undefined) {
        this.$Router.replaceAll('/pages/user/user')
      }
      return
      // #endif
      this.$Router.back(1)
    }
  }
}
</script>

<style lang="scss">
.fixed_top_status_bar {
  position: fixed;
  /* app-1-start */



  /* app-1-end */
  /* #ifdef H5*/
  height: 0;
  /* #endif */
  top: 0;
  left: 0;
  right: 0;
  z-index: 99;
  background: #fff;
}

.container {
  width: 750rpx;
  margin: 0 auto;
  overflow-x: hidden;

  .integral_top {
	/* app-2-start */



	/* app-2-end */
    height: 396rpx;
    background-size: 100% 100%;
    position: relative;

    .go_back_icon {
      width: 50rpx;
      height: 50rpx;
      position: absolute;
      left: 25rpx;
      top: 25rpx;
	  /* wx-1-start */
      /* #ifdef MP-WEIXIN */
      top: 70rpx;
      /* #endif */
	  /* wx-1-end */
    }

    .points_num {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 54rpx;
      color: #ffe9a6;
      // background: linear-gradient(-90deg, #FFF6E1 0%, #FFD27A 100%);
      // -webkit-background-clip: text;
      // -webkit-text-fill-color: transparent;
    }
  }

  .integral_main {
    .integral_title {
      height: 87rpx;
      font-size: 32rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 200rpx;
      border-bottom: 1rpx solid #f2f2f2;
    }

    .integral_item_wrap {
      padding-left: 30rpx;
      box-sizing: border-box;

      .integral_item {
        padding: 30rpx 30rpx 30rpx 0;
        border-bottom: 1rpx solid #f2f2f2;

        .integral_item_title {
          font-weight: bold;
          display: flex;
          justify-content: space-between;
        }

        .integral_item_title text:nth-child(1) {
          font-size: 28rpx;
          color: #2d2d2d;
        }

        .integral_item_title text:nth-child(2) {
          font-size: 32rpx;
          color: var(--color_integral_main);
        }

        .integral_item_content {
          font-size: 28rpx;
          color: #666;
          margin-top: 10rpx;
        }

        .integral_item_date {
          font-size: 24rpx;
          color: #999;
          margin-top: 10rpx;
        }
      }
    }

    .integral_item_wrap > view:nth-last-child(1) {
      border-bottom: none;
    }
  }
}

.active {
  height: 100%;
  display: flex;
  align-items: center;
  color: var(--color_integral_main);
  font-weight: bold;
  border-bottom: 6rpx solid var(--color_integral_main);
  box-sizing: border-box;
}

.empty_page {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 150rpx;

  .empty_img {
   width: 380rpx;
   height: 280rpx;
    margin-bottom: 20rpx;
  }

  .empty_text {
    font-size: 26rpx;
    color: #666;
  }
}
</style>
