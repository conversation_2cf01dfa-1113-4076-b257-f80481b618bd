<template>
  <view class="container" :style="mix_diyStyle">
    <!-- app-start -->








    <!-- app-end -->
    <!-- app-start -->

    <!-- app-end -->
    <view
      class="list_cell m_t mem_avatar_wrap flex_row_between_center"
      hover-class="cell_hover"
      :hover-stay-time="50"
      @click="setAvatar"
    >
    <!-- app-start -->

    <!-- app-end -->
      <text class="cell_tit">{{ $L('修改会员头像') }}</text>
      <view class="flex_row_end_center">
        <view
          class="avatar"
          :style="{
            backgroundImage: 'url(' + memberAvatar + ')',
            backgroundSize: 'cover'
          }"
        />
        <text class="cell_more iconfont iconziyuan11"></text>
      </view>
    </view>

    <view class="list_cell m_t" hover-class="cell_hover" :hover-stay-time="50">
      <text class="cell_tit">{{ $L('会员名') }}</text>
      <view>
        <text class="cell_right_con">{{ memberName }}</text>
      </view>
    </view>

    <view
      class="list_cell b_b m_t"
      @click="changeName('true')"
      hover-class="cell_hover"
      :hover-stay-time="50"
    >
      <text class="cell_tit">{{ '真实姓名' }}</text>
      <view>
        <text class="cell_right_con">{{
          memberTrueName ? memberTrueName : '--'
        }}</text>
        <text class="cell_more iconfont iconziyuan11"></text>
      </view>
    </view>
    <view
      class="list_cell b_b"
      @click="changeName('nick')"
      hover-class="cell_hover"
      :hover-stay-time="50"
    >
      <text class="cell_tit">{{ $L('昵称') }}</text>
      <view>
        <text class="cell_right_con">{{
          memberNickName ? memberNickName : '--'
        }}</text>
        <text class="cell_more iconfont iconziyuan11"></text>
      </view>
    </view>
    <view hover-class="cell_hover" :hover-stay-time="50">
      <picker @change="selSex" :value="gender" :range="sexArray">
        <view class="list_cell b_b">
          <text class="cell_tit">{{ $L('性别') }}</text>
          <view class="flex_row_end_center">
            <view class="uni_birthday">{{ sexArray[gender] }}</view>
            <text class="cell_more iconfont iconziyuan11"></text>
          </view>
        </view>
      </picker>
    </view>
    <view hover-class="cell_hover" :hover-stay-time="50">
      <picker
        mode="date"
        :end="filters.getDateTime(endtime)"
        :value="memberBirthday"
        @change="selBirthDay"
      >
        <view class="list_cell">
          <text class="cell_tit">{{ $L('生日') }}</text>
          <view class="flex_row_end_center">
            <view class="uni_birthday">{{ memberBirthdayCon }}</view>
            <text class="cell_more iconfont iconziyuan11"></text>
          </view>
        </view>
      </picker>
    </view>
    <!-- app-start -->
    <view class="smegma" v-if="perm"></view>
    <view class="frame"  v-if="perm">
    	<view class="fra_title">摄像头权限说明</view>
    	<view class="">
    		申请摄像头拍摄权限以便您能通过扫一扫、上传照片或视频实现扫描二维码、识别商品、评价晒单、售后、发布种草服务。拒绝或取消授权不影响使用其他服务。
    	</view>
    </view>
    <!-- app-end -->
    
	<!-- wx-1-start -->
	<!-- #ifdef MP -->
	<privacyPop ref="priPop"></privacyPop>
	<!-- #endif -->
	<!-- wx-1-end -->
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex'
// app-start
import permision from "@/utils/permission";
// app-end
//wx-4-start
// #ifdef MP
import privacyPop from '@/components/privacy-pop.vue';
// #endif
//wx-4-end
import filters from '@/utils/filter.js'
export default {
  //wx-2-start
  // #ifdef MP
  components: {
	privacyPop,
  },
  // #endif
  //wx-2-end
  data() {
    return {
      memberAvatarLocal: '',
      memberAvatar: process.env.VUE_APP_IMG_URL + 'default/member-avatar.png', //会员头像
      memberName: '',
      memberNickName: '', //昵称
      memberTrueName: '',
      gender: 0,
      sexArray: [this.$L('保密'), this.$L('男'), this.$L('女')],
      memberBirthday: '',
      memberBirthdayCon: this.$L('请选择生日'),
      endtime: new Date().getTime(),
      filters,
      // app-start
      perm:false,
      systemInfo: null,  //手机端平台类型
      // app-end
    }
  },
  onLoad() {
    setTimeout(()=>{
       uni.setNavigationBarTitle({
          title: this.$L('个人信息')
        })   
    },0);
    
    this.getMmeberInfo()
    // app-start



    // app-end
  },
  onShow() {
  	let _this = this;
	// #ifdef MP
  	// 判断用户隐私授权
	this.$refs.priPop?.configurePrivacy()
  	// #endif
  },
  computed: {
    ...mapState(['userInfo', 'userCenterData'])
  },
  methods: {
    ...mapMutations(['setUserCenterData']),
    //获取会员信息
    getMmeberInfo() {
      let _this = this
      this.$request({
        url: 'v3/member/front/member/memberInfo',
        method: 'GET'
      })
        .then((res) => {
          if (res.state == 200) {
            let result = res.data
            _this.memberAvatar = result.memberAvatar
            _this.memberName = result.memberName
            _this.memberNickName = result.memberNickName
            _this.gender = result.gender
            _this.memberBirthdayCon = result.memberBirthday
              ? result.memberBirthday
              : '请选择生日'
            this.memberTrueName = result.memberTrueName
          } else {
            this.$api.msg(res.msg)
          }
        })
        .catch((e) => {})
    },
    navTo(url) {
      this.$Router.push(url)
    },
    //选择生日
    selBirthDay(e) {
      this.saveMemInfo('memberBirthday', e.detail.value)
    },
    //选择性别
    selSex(e) {
      this.saveMemInfo('gender', e.detail.value)
    },
    //更新会员昵称
    updateMemInfo(index, val) {
      this[index] = val
      //更新个人信息数据
      this.userCenterData[index] = val
      this.setUserCenterData(this.userCenterData)
    },
    //保存会员信息
    saveMemInfo(index, val) {
      if (val == this[index]) {
        return
      }

      let param = {}
      param.url = 'v3/member/front/member/updateInfo'
      param.data = {}
      param.method = 'POST'
      param.data[index] = val
      this.$request(param)
        .then((res) => {
          this.$api.msg(res.msg)
          if (res.state != 200) {
            this.$api.msg(res.msg)
          } else {
            if (index == 'memberAvatar') {
              this.memberAvatar = this.memberAvatarLocal
              //更新个人信息数据
              this.userCenterData.profilePic = this.memberAvatar
              this.userCenterData.memberAvatar = this.memberAvatar
              this.setUserCenterData(this.userCenterData)
            } else if (index == 'memberBirthday') {
              this[index] = val
              this.memberBirthdayCon = val
            } else {
              this[index] = val
            }
          }
        })
        .catch((e) => {
          //异常处理
        })
    },
    // app-start
    async requestAndroidPermission(permisionID) {
    	if(this.systemInfo == 'android'){
    			this.perm = true;
    		var result = await permision.requestAndroidPermission(permisionID)
    		var strStatus
    		if (result == 1) {
    			this.perm = false
    			this.setAvatar()
    		} else if (result == 0) {
    			this.perm = false
    			strStatus = "未获得授权"
    		} else {
    			this.perm = false
    			strStatus = "被永久拒绝权限"
    		}
    	}else {
    		this.setAvatar()
    	}
    	
    },
    // app-end
    //设置头像
    setAvatar() {
	  //wx-5-start
	  // #ifdef MP
	  if (!process.env.VUE_APP_ALLOW_PRIVACY) {
		this.$refs.priPop.open();
		return;
	  }
	  // #endif
	  //wx-5-end
      let _this = this
      uni.chooseImage({
        count: 1,
        sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
        success: function (res) {
          uni.showLoading({
            title: _this.$L('上传中...')
          })
          if (res.tempFiles[0].size > Math.pow(1024, 2) * 4) {
            uni.hideLoading()
            uni.showToast({
              title: _this.$L('超出了图片大小限制4M'),
              icon: 'none',
              duration: 700
            })
          } else {
            uni.uploadFile({
              url: process.env.VUE_APP_API_URL + 'v3/oss/front/upload',
              filePath: res.tempFilePaths[0],
              name: 'file',
              formData: {
                source: 'headImg'
              },
              header: {
                  Authorization:  'Bearer '+_this.userInfo.member_access_token
               },
              success: (uploadFileRes) => {





                let result = JSON.parse(uploadFileRes.data)

                if (result.state == 200) {
                  uni.hideLoading()
                  _this.memberAvatarLocal = result.data.url
                  _this.saveMemInfo('memberAvatar', result.data.path)
                } else {
                  uni.hideLoading()
                  uni.showToast({
                    title: uploadFileRes.msg,
                    icon: 'none',
                    duration: 700
                  })
                }
              },
              fail: (uploadFileRes) => {
                uni.showToast({
                  title: uploadFileRes.msg,
                  icon: 'none',
                  duration: 700
                })
              }
            })
          }
        }
      })
    },
    //修改昵称事件
    changeName(type) {
      switch (type) {
        case 'nick': {
          this.$Router.push({
            path: '/pages/user/changeInfo',
            query: {
              name: this.memberNickName
                ? encodeURIComponent(this.memberNickName)
                : '',
              modifier: 'nick'
            }
          })
          break
        }
        case 'true': {
          this.$Router.push({
            path: '/pages/user/changeInfo',
            query: {
              name: this.memberTrueName
                ? encodeURIComponent(this.memberTrueName)
                : '',
              modifier: 'true'
            }
          })
          break
        }
      }
    }
  }
}
</script>

<style lang="scss">
page {
  background: $bg-color-split;
  width: 750rpx;
  margin: 0 auto;
}

.list_cell {
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  line-height: 100rpx;
  height: 100rpx;
  position: relative;
  background: #fff;
  justify-content: center;

  &.cell_hover {
    background: #fafafa;
  }

  &.b_b:after {
    left: 20rpx;
  }

  &.m_t {
    margin-top: 20rpx;
  }

  .cell_more {
    color: $main-third-color;
    font-size: 18rpx;
    margin-left: 10rpx;
  }

  .cell_tit {
    flex: 1;
    font-size: 28rpx;
    color: #2d2d2d;
    margin-right: 10rpx;
  }

  .cell_right_con,
  .uni_birthday {
    color: #949494;
    font-size: 26rpx;
  }

  &.mem_avatar_wrap {
    height: 120rpx;
    line-height: 120rpx;
  }

  .avatar {
    width: 82rpx;
    height: 82rpx;
    border-radius: 50%;
    background-position: center center;
    background-repeat: no-repeat;
    background-color: #f8f6f7;
  }
}
/* app-start */
  .smegma {
      position: fixed;
      top: 0;
      left: 0;
      height: 100vh;
      width: 100vw;
      opacity: .8;
  }
  .frame {
      position: fixed;
      top: 0;



      left: 0;
      padding: 20rpx 20rpx;
      z-index: 10000;
      width: 100vw;
      line-height: 50rpx;
      background-color: #fff;
      border-radius: 10rpx;
  }
  .fra_title {
      font-weight: 700;
      font-size: 35rpx;
      margin-bottom: 10rpx;
  }
  /* app-end */
</style>
