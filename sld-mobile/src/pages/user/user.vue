<template>
	<view :style="mix_diyStyle">
		<view class="container" :style="{ backgroundImage: 'url(' + imgUrl + 'user/user_bg.png)' }">
			<!-- 会员信息 上滑展示 start -->
			<view v-if="memberSliding" class="member_info_sliding"
				:style="{paddingTop: 'calc(10rpx + ' + stateHeight + 'px)',backgroundImage: 'url(' + imgUrl + 'user/user_bg.png)'}">
				<view class="member_info_sliding_left flex_row_start_center">
					<image :src="userCenterData.memberAvatar != undefined? userCenterData.memberAvatar: defaultAvatar"
						mode="aspectFill"></image>
					<text v-if="userCenterData.memberNickName || userCenterData.memberMobile">
						{{ userCenterData.memberNickName || userCenterData.memberMobile }}</text>
					<text v-else>{{ $L('登录') }}</text>
				</view>

				<!-- #ifndef MP -->
				<view class="member_info_sliding_right">
					
					<view class="user_msg" @click="goMsg()">
						<image :src="user_msg" mode="aspectFit" class="user_msg_img"></image>
						<text v-if="msgNumState > 0">
							{{ msgNumState > 9 ? '9+' : msgNumState }}</text>
					</view>
					<view class="user_sets" @click="goSet()">
						<image :src="user_setting" mode="aspectFit" class="user_set_img"></image>
					</view>
				</view>
				<!-- #endif -->
			</view>
			<!-- 会员信息 上滑展示 end -->

			<!-- 会员信息 start 未上滑 -->
			<view class="user-section">
				<!-- <image class="bg" :src="userSectionBg" mode="widthFix"></image> -->
				<view class="fixed_top_status_bar" :style="{ height: stateHeight + 'px' }"></view>

				<view class="flex_row_between_center user_info_just">
					<view class="user-info-box">
						<view class="portrait-box">
							<view class="portrait-bgc">
								<image
									:src="userCenterData && userCenterData.memberAvatar != undefined? userCenterData.memberAvatar: defaultAvatar"
									mode="aspectFill" class="portrait"></image>
							</view>
							<view v-if="memberConfig.super_is_enable==1&&userCenterData.isSuper==1"
								class="portrait-vip">超级VIP</view>
						</view>
						<view class="mem-info">
							<text class="nick-name"
								v-if="userCenterData &&(userCenterData.memberMobile || userCenterData.memberNickName)"
								@click="navTo('/pages/user/info')">{{userCenterData.memberNickName || userCenterData.memberMobile}}</text>
							<!-- wx-1-start -->
							<!-- #ifdef MP-WEIXIN -->
							<button class="login_btn" @tap="getUserProfile"
								v-if="(!userCenterData ||(userCenterData && !userCenterData.memberId)) &&canIUseGetUserProfile">
								<text class="nick-name">{{ $L('登录') }}</text>
							</button>
							<button class="login_btn" open-type="getUserInfo" @getuserinfo="getUser"
								v-if="(!userCenterData ||(userCenterData && !userCenterData.memberId)) &&!canIUseGetUserProfile">
								<text class="nick-name">{{ $L('登录') }}</text>
							</button>
							<!-- #endif -->
							<!-- wx-1-end -->
							<!-- #ifndef MP-WEIXIN -->
							<text class="nick-name" @tap="gotoLogin"
								v-if="!userCenterData ||(userCenterData && !userCenterData.memberName)">{{ $L('登录') }}</text>
							<!-- #endif -->

							<block v-if="hasLogin">
								<text class="member-name" @click="navTo('/standard/memLev/index')"
									v-if="userCenterData && userCenterData.memberGrowthValue!==undefined">{{$L('成长值')}}:{{userCenterData.memberGrowthValue}}</text>
								<text class="member-name" @click="navTo('/pages/user/info')"
									v-if="userCenterData && userCenterData.memberName">{{$L('会员名:')}}{{userCenterData.memberName}}</text>
							</block>
						</view>
					</view>
					<view class="user_set">
						
						<view class="user_msg flex_row_center_center" @click="goMsg()">
							<image :src="user_msg" mode="aspectFit" class="user_msg_img"></image>
							<text v-if="msgNumState > 0">
								{{msgNumState > 9 ? '9+' : msgNumState}}</text>
						</view>
						<view class="user_msg flex_row_center_center" @click="goSet()">
							<image :src="user_setting" mode="aspectFit" class="user_set_img"></image>
						</view>
					</view>
				</view>

				<view class="user_info_tab flex_row_between_center">
					<view class="user_info_tab_item flex_column_center_center"
						@click="navTo('/standard/store/attentionStore')">
						<text class="tab_item_text2">{{userCenterData.followStoreNum || 0}}</text>
						<text class="tab_item_text1">{{ $L('关注店铺') }}</text>
					</view>
					<view class="user_info_tab_item flex_column_center_center" @click="navTo('/pages/member/collect')">
						<text class="tab_item_text2">{{userCenterData.followProductNum || 0}}</text>
						<text class="tab_item_text1">{{ $L('我的收藏') }}</text>
					</view>
					<view class="user_info_tab_item flex_column_center_center" @click="navTo('/pages/member/history')">
						<text class="tab_item_text2">{{userCenterData.lookLogNum || 0}}</text>
						<text class="tab_item_text1">{{ $L('我的足迹') }}</text>
					</view>
				</view>
			</view>
			<!-- 会员信息 未上滑 end -->

			<view class="main-content">
				<!-- 超级会员 start -->
				<block v-if="memberConfig.super_is_enable==1">
					<view class="super_user flex_row_between_center"
						:style="'background-image:url(' + imgUrl + (userCenterData.isSuper==2 ? 'user/vip_off_bg.png' : 'user/vip_bg.png')+')'">
						<view class="super_user_left flex_row_start_center">
							<image class="super_user_vip" :class="userCenterData.isSuper==2 ? 'sm' : ''"
								mode="aspectFit" :src="memberConfig.super_personal_center_icon"
								v-if="memberConfig.super_personal_center_icon"></image>
							<image class="super_user_vip" :class="userCenterData.isSuper==2 ? 'sm' : ''"
								mode="aspectFit"
								:src="imgUrl + (userCenterData.isSuper==2 ? 'user/vip_off.png' : 'user/vip.png')"
								v-else></image>
							<view class="super_user_tips flex_column_center_start">
								<span v-if="userCenterData.isSuper==2"
									class="super_user_title isOff">{{memberConfig.super_custom_name}}已到期</span>
								<span v-else
									class="super_user_title">{{userCenterData.isSuper==1 ? memberConfig.super_custom_name : `开通${memberConfig.super_custom_name}`}}</span>
								<span v-if="userCenterData.isSuper==1"
									class="super_user_time">{{userCenterData.superExpirationDay}}到期</span>
							</view>
						</view>
						<view class="super_user_right">
							<view v-if="userCenterData.isSuper==2" class="super_user_btn repay" @click="navtoVip">立即续费
							</view>
							<view v-else class="super_user_btn" @click="navtoVip">
								{{userCenterData.isSuper==1 ? '查看权益' : '立即开通'}}
							</view>
						</view>
					</view>
				</block>
				<!-- 超级会员 end -->
				<!-- 我的订单 start-->
				<view class="order-part flex_column_start_start">
					<view class="title flex_row_between_center">
						<text class="left">{{ $L('我的订单') }}</text>
						<view class="right flex_row_end_center"
							@click="navTo({ path: '/pages/order/list', query: { state: 0 } })">
							<text>{{ $L('全部') }}</text>
							<image :src="imgUrl + 'member/right_down.png'" mode="aspectFit" lazy-load />
						</view>
					</view>
					<view class="detail flex_row_around_center">
						<view class="wait-pay item flex_column_center_center"
							@click="navTo({ path: '/pages/order/list', query: { state: 1 } })">
							<view class="detail_top">
								<image :src="imgUrl + 'user/wait_pay_icon1.png'" mode="aspectFit" lazy-load />
								<text class="nums flex_row_center_center"
									:class="{ num: userCenterData.toPaidOrder <= 9 }"
									v-if="userCenterData && userCenterData.toPaidOrder">
									{{userCenterData.toPaidOrder > 99? '99+': userCenterData.toPaidOrder}}</text>
							</view>
							<text class="show-text">{{ $L('待付款') }}</text>
						</view>
						<view class="wait-pay item flex_column_center_center"
							@click="navTo({ path: '/pages/order/list', query: { state: 2 } })">
							<view class="detail_top">
								<image :src="imgUrl + 'user/wait_deliver_icon1.png'" mode="aspectFit" lazy-load />
								<text v-if="userCenterData && userCenterData.toDeliverOrder"
									class="nums flex_row_center_center"
									:class="{ num: userCenterData.toDeliverOrder <= 9 }">{{userCenterData.toDeliverOrder > 99? '99+': userCenterData.toDeliverOrder}}</text>
							</view>
							<text class="show-text">{{ $L('待发货') }}</text>
						</view>
						<view class="wait-pay item flex_column_center_center"
							@click="navTo({ path: '/pages/order/list', query: { state: 3 } })">
							<view class="detail_top">
								<image :src="imgUrl + 'user/wait_receive_icon1.png'" mode="aspectFit" lazy-load />
								<text v-if="userCenterData && userCenterData.toReceivedOrder"
									class="nums flex_row_center_center"
									:class="{ num: userCenterData.toReceivedOrder <= 9 }">{{userCenterData.toReceivedOrder > 99? '99+': userCenterData.toReceivedOrder}}</text>
							</view>
							<text class="show-text">{{ $L('待收货') }}</text>
						</view>
						<view class="wait-pay item flex_column_center_center"
							@click="navTo({ path: '/pages/order/list', query: { state: 5 } })">
							<view class="detail_top">
								<image :src="imgUrl + 'user/wait_evaluate_icon1.png'" mode="aspectFit" lazy-load />
								<text v-if="userCenterData && userCenterData.toEvaluateOrder"
									class="nums flex_row_center_center"
									:class="{ num: userCenterData.toEvaluateOrder <= 9 }">{{userCenterData.toEvaluateOrder > 99? '99+': userCenterData.toEvaluateOrder}}</text>
							</view>
							<text class="show-text">{{ $L('待评价') }}</text>
						</view>
						<view class="wait-pay item flex_column_center_center"
							@click="navTo({path: '/standard/refund/returnAndRefundList',query: { state: 0 }})">
							<view class="detail_top">
								<image :src="imgUrl + 'user/wait_service_icon1.png'" mode="aspectFit" lazy-load />
								<text v-if="userCenterData && userCenterData.afterSaleNum"
									class="nums flex_row_center_center"
									:class="{ num: userCenterData.afterSaleNum <= 9 }">{{userCenterData.afterSaleNum > 99? '99+': userCenterData.afterSaleNum}}</text>
							</view>
							<text class="show-text">{{ $L('退款/售后') }}</text>
						</view>
					</view>
				</view>
				<!-- 我的订单 end-->

				

				<!-- 开店横版广告展示 start -->
				<view class="picture_adv_poster_con" @click="sellerOpt"
					v-if="hasLogin && userCenterData.sellerSwitch == 1">
					<image :src="imgUrl + 'store_poster.png'" mode="aspectFit" lazy-load></image>
				</view>
				<!-- 开店横版广告展示 end -->

				<!-- 常用服务 start -->
				<view class="common_services">
					<view class="common_services_title">{{ $L('常用服务') }}</view>
					<view class="common_services_list">
						
						<!-- #ifndef MP-WEIXIN -->
						<view class="common_services_pre" @click="navTo('/pages/balance/balance')">
							<view class="common_serv_pre_left">
								<image :src="imgUrl + 'user/mine_wallet.png'" mode="aspectFit" lazy-load></image>
								<text>{{ $L('店铺余额') }}</text>
							</view>
							<view class="common_serv_pre_right">
								<text
									v-if="userCenterData && userCenterData.memberBalance">{{ $L('￥') }}{{ userCenterData.memberBalance }}</text>
								<image :src="imgUrl + 'member/right_down.png'" mode="aspectFit" />
							</view>
						</view>
						<view class="common_services_pre" @click="goSpreaderCenter">
							<view class="common_serv_pre_left">
								<image :src="imgUrl + 'user/mine_integral.png'" mode="aspectFit" lazy-load></image>
								<text>{{ $L('我的推广') }}</text>
							</view>
							<view class="common_serv_pre_right">
								<text
									v-if="userCenterData && userCenterData.couponNum">{{userCenterData.couponNum}}</text>
								<image :src="imgUrl + 'member/right_down.png'" mode="aspectFit" lazy-load />
							</view>
						</view>
						<!-- #endif -->

						<!-- <view class="common_services_pre" @click="toPointsPage">
							<view class="common_serv_pre_left">
								<image :src="imgUrl + 'user/mine_integral.png'" mode="aspectFit" lazy-load></image>
								<text>{{ $L('我的积分') }}</text>
							</view>
							<view class="common_serv_pre_right">
								<text
									v-if="userCenterData && userCenterData.memberIntegral">{{userCenterData.memberIntegral}}</text>
								<image :src="imgUrl + 'member/right_down.png'" mode="aspectFit" lazy-load />
							</view>
						</view> -->
						<view class="common_services_pre" @click="goMyCoupon">
							<view class="common_serv_pre_left">
								<image :src="imgUrl + 'user/my_coupon.png'" mode="aspectFit" lazy-load></image>
								<text>{{ $L('我的优惠券') }}</text>
							</view>
							<view class="common_serv_pre_right">
								<text
									v-if="userCenterData && userCenterData.couponNum">{{userCenterData.couponNum}}</text>
								<image :src="imgUrl + 'member/right_down.png'" mode="aspectFit" lazy-load />
							</view>
						</view>
						
						<!-- #ifndef MP-WEIXIN -->
						<view class="common_services_pre" @click="goMyPointOrder">
							<view class="common_serv_pre_left">
								<image :src="imgUrl + 'user/point_order.png'" mode="aspectFit" lazy-load></image>
								<text>{{ $L('我的积分订单') }}</text>
							</view>
							<view class="common_serv_pre_right">
								<text v-if="pointOrderNums && hasLogin">{{pointOrderNums}}</text>
								<image :src="imgUrl + 'member/right_down.png'" mode="aspectFit" lazy-load />
							</view>
						</view>
						<!-- #endif -->


						<view class="common_services_pre" @click="goMyVideo"
							v-if="hasLogin && setting.video_switch == '1'">
							<view class="common_serv_pre_left">
								<image :src="imgUrl + 'user/my_video.png'" mode="aspectFit" lazy-load></image>
								<text>{{ $L('我的发文') }}</text>
							</view>
							<view class="common_serv_pre_right">
								<image :src="imgUrl + 'member/right_down.png'" mode="aspectFit" lazy-load />
							</view>
						</view>
						<view class="common_services_pre" @click="goMyVideo"
							v-if="hasLogin && setting.live_switch == '1' && authorInfo.roleType == 2">
							<view class="common_serv_pre_left">
								<image :src="imgUrl + 'user/my_live.png'" mode="aspectFit" lazy-load></image>
								<text>{{ $L('我的直播') }}</text>
							</view>
							<view class="common_serv_pre_right">
								<image :src="imgUrl + 'member/right_down.png'" mode="aspectFit" lazy-load />
							</view>
						</view>
						<view v-if="isSignOpen" class="common_services_pre" @click="navTo('/standard/signIn/signIn')">
							<view class="common_serv_pre_left">
								<image :src="imgUrl + 'user/my_sign.png'" mode="aspectFit" lazy-load></image>
								<text>{{ $L('签到中心') }}</text>
							</view>
							<view class="common_serv_pre_right">
								<image :src="imgUrl + 'member/right_down.png'" mode="aspectFit" lazy-load />
							</view>
						</view>
						<view class="common_services_pre" @click="navTo('/pages/address/list')">
							<view class="common_serv_pre_left">
								<image :src="imgUrl + 'user/my_addr.png'" mode="aspectFit" lazy-load></image>
								<text>{{ $L('我的地址') }}</text>
							</view>
							<view class="common_serv_pre_right">
								<image :src="imgUrl + 'member/right_down.png'" mode="aspectFit" lazy-load />
							</view>
						</view>
						<view class="common_services_pre" @click="makeCall">
							<view class="common_serv_pre_left">
								<image :src="imgUrl + 'user/customer_help_icon.png'" mode="aspectFit" lazy-load>
								</image>
								<text>{{ $L('官方客服') }}</text>
							</view>
							<view class="common_serv_pre_right">
								<image :src="imgUrl + 'member/right_down.png'" mode="aspectFit" lazy-load />
							</view>
						</view>
					</view>
				</view>
				<!-- 常用服务 end -->
			</view>

			<!-- 推荐商品 start-->
			<recommendGoods ref="recomment_goods" @reload_cart="getCartNum" />
			<!-- 推荐商品 end-->

			<!-- 开屏框 start -->
			<view v-if="signOpenCookie && !isSign" class="open_screen">
				<view :style="{ width: width + 'rpx', height: height + 'rpx' }" class="open_screen_con"
					@click="navTo('/standard/signIn/signIn')">
					<view class="con_img" @click.stop="signOpenCookie = false">
						<image :src="imgUrl + 'signIn/close_screen.png'"></image>
					</view>
					<image class="open_screen_con_img" :src="JSON.stringify(signNotice) ? signNotice : openscnImg"
						style="height: 821rpx" mode="aspectFit" lazy-load></image>
				</view>
			</view>
			<!-- 开屏框 end -->

			<uni-popup ref="popup" type="dialog">
				<uni-popup-dialog type="input" :title="$L('温馨提示')" :content="sellerTip" :confirmText="$L('复制')"
					:duration="2000" @confirm="copyClipp"></uni-popup-dialog>
			</uni-popup>
			<!-- wx-2-start -->
			<!-- #ifdef MP-WEIXIN -->
			<wxBindPhone ref="wxBindPhone"></wxBindPhone>
			<!-- #endif -->
			<!-- wx-2-end -->
			<loginPop ref="loginPop" @confirmLogin="confirmLogin"></loginPop>
			<!-- wx-3-start -->
			<!-- #ifdef MP -->
			<privacyPop ref="priPop"></privacyPop>
			<!-- #endif -->
			<!-- wx-3-end -->

			<uni-popup ref="vendorDisTipPop" type="dialog">
				<uni-popup-dialog type="input" :title="$L('温馨提示')" :content="vendorDisableTip" :confirmText="$L('复制链接')"
					:contentStyle="{lineHeight:'42rpx',fontSize:'26rpx',textAlign:'center'}" :duration="2000"
					@confirm="copyClipp">
				</uni-popup-dialog>
			</uni-popup>

		</view>
	</view>
</template>
<script>
	import recommendGoods from '@/components/recommend-goods.vue'
import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue'
import uniPopup from '@/components/uni-popup/uni-popup.vue'
import wxBindPhone from '@/components/wxBindPhone'
import h5Copy from '@/static/h5/H5copy.js'
import {
mapActions,
mapMutations,
mapState
} from 'vuex'
	//wx-4-start
	// #ifdef MP
	import privacyPop from '@/components/privacy-pop.vue'
	// #endif
	//wx-4-end
	
	// #ifdef H5
	import { getWxH5Appid } from '../../static/h5/wxH5Auth'
	// #endif
	
	export default {
		components: {
			recommendGoods,
			uniPopup,
			uniPopupDialog,
			wxBindPhone,
			//wx-5-start
			// #ifdef MP
			privacyPop,
			// #endif
			//wx-5-end
		},
		data() {
			return {
				imgUrl: process.env.VUE_APP_IMG_URL,
				defaultAvatar: process.env.VUE_APP_IMG_URL + 'user/member-avatar.png', //默认头像
				client: 1, //终端类型， 1、H5(微信内部浏览器) 2、H5(微信小程序)；3、app
				oriUrl: '', //不带code的页面地址
				memberSliding: false, //页面上滑效果是否显示
				pointOrderNums: 0, //为完成积分订单数量
				videoNum: 0, //我的视频数量
				setting: {}, //平台配置信息
				bgStyle: 'background-size:contain;background-position:center center;background-repeat: no-repeat;',
				canIUseGetUserProfile: false, //是否可以使用getUserProfile获取用户信息，用于微信小程序,
				openscnImg: process.env.VUE_APP_IMG_URL + 'signIn/signner.png',
				width: 636,
				height: 821,
				signOpenCookie: false, //签到提醒flag,
				isSignOpen: false, //签到活动是否可用
				isSign: false, //是否签到,
				showState: false,
				signNotice: {},
				adminTel: '',
				sellerTip: '',
				isClick: true,
				wtCode: '',
				adminSerAvatar: '',
				adminSerName: '',
				user_role_change: process.env.VUE_APP_IMG_URL + 'user/user_role_change.png',
				user_setting: process.env.VUE_APP_IMG_URL + 'user/user_setting.png',
				user_msg: process.env.VUE_APP_IMG_URL + 'user/user_msg.png',
				stateHeight: 0,
				authorInfo: {},
				business_info: {},
				vendorDisableTip: ''
			}
		},
		onLoad(option) {
			this.stateHeight = uni.getSystemInfoSync().statusBarHeight
			this.client = this.$wxLoginClient()
			//wx-6-start
			//#ifdef MP-WEIXIN
			if (uni.getUserProfile) {
				this.canIUseGetUserProfile = true
			}
			//#endif
			//wx-6-end
			//#ifdef H5
			let code = this.$getQueryVariable('code')
			if (code) {
				//如果是微信浏览器，而且浏览器地址里有code，需要把code再跳转下一页，防止返回的时候再去验证code
				history.replaceState({}, '', location.href.split('?')[0])
				this.toLogin(code, '')
			}
			//#endif
			this.initData()
			this.getSetting()
			this.getSysSetting()
			this.getMemberConfig(['super_is_enable', 'super_custom_name', 'super_personal_center_icon'])
			if (this.hasLogin) {
				this.$globalSocketIO.getMsgNum()
				this.signOpenScreen()
			}
		},
		onPullDownRefresh() {
			this.initData()
		},
		onShow() {
			let _this = this;
			
			if (this.$refs.loginPop != undefined) {
				this.$refs.loginPop.close()
			}
			this.memberSliding = false
			if (this.showState) {
				this.initData()
				this.getSetting()
				this.signOpenScreen()
				this.$globalSocketIO.getMsgNum()
				this.getMemberConfig(['super_is_enable', 'super_custom_name', 'super_personal_center_icon'])
				this.$refs.loginPop.close()
			}
			//wx-8-start
			//统计埋点方法--针对微信小程序
			// #ifdef MP-WEIXIN
			// 判断用户隐私授权
			this.$refs.priPop?.configurePrivacy()
			let url = process.env.VUE_APP_API_URL.substring(0,process.env.VUE_APP_API_URL.length - 1)
			this.$sldStatEvent({
				behaviorType: 'pv',
				pageUrl: url + '/pages/user/user',
				referrerPageUrl: ''
			})
			// #endif
			//wx-8-end
			this.getCartNum()

			if (this.hasLogin) {
				}
		},
		onHide() {
			this.signOpenCookie = false
		},
		// #ifndef MP
		onNavigationBarButtonTap(e) {
			const index = e.index
			if (index === 0) {
				this.navTo('/pages/set/set')
			} else if (index === 1) {
				//app-1-start








				//app-1-end
				this.$Router.push('/pages/notice/noticeCenter')
			}
			this.showState = true
		},
		// #endif
		computed: {
			...mapState([
				'hasLogin', 'userInfo', 'userCenterData', 'memberConfig', 
				'msgNumState',
			])
		},
		onReachBottom() {
			this.$refs.recomment_goods.getMoreData()
		},
		//监听页面滚动，顶部上滑效果显示
		onPageScroll(res) {
			if (res.scrollTop > 191 + this.stateHeight) {
				this.memberSliding = true
			} else {
				this.memberSliding = false
			}
		},
		methods: {
			...mapMutations(['login', 'setUserCenterData', 'saveChatBaseInfo', 'loginRole']),
			...mapActions(['getMemberConfig']),

			confirmLogin() {
				this.$refs.loginPop.close()
				this.showState = true
			},

			//获取个人中心数据
			initData() {
				let that = this
				if (this.hasLogin) {
					this.$request({
						url: 'v3/member/front/member/getInfo'
					}).then((res) => {
						uni.stopPullDownRefresh()
						if (res.state == 200) {
							if (res.data.superExpirationTime) {
								let time = res.data.superExpirationTime.split(' ')[0].split('-');
								res.data.superExpirationDay = time[0] + '年' + time[1] + '月' + time[2] + '日';
							}
							let combineData = Object.assign({}, this.userCenterData, res.data)
							this.setUserCenterData(combineData)
						} else {
							this.$api.msg(res.msg)
						}
					})

					//获取订单订单未完成数量
					this.$request({
						url: 'v3/integral/front/integral/order/orderCount'
					}).then((res) => {
						if (res.state == 200) {
							this.pointOrderNums = res.data
						}
					})

					this.$request({
						url: 'v3/video/front/video/author/personPage'
					}).then((res) => {
						if (res.state == 200) {
							this.authorInfo = res.data
						}
					})
				} else {
					uni.stopPullDownRefresh()
				}
			},
			// 获取设置信息
			getSetting() {
				let param = {}
				param.url = 'v3/video/front/video/setting/getSettingList'
				param.method = 'GET'
				param.data = {}
				param.data.str = 'video_switch,live_switch'
				this.$request(param).then((res) => {
					if (res.state == 200) {
						let result = res.data
						result &&
							result.map((settingItem) => {
								if (settingItem.name == 'video_switch') {
									//绑定商品数
									this.setting.video_switch = settingItem.value
								} else {
									this.setting.live_switch = settingItem.value
								}
							})
					}
				})
			},

			getSysSetting() {
				let param = {
					url: 'v3/system/front/setting/getSettings',
					data: {
						names: 'basic_site_phone,platform_customer_service_name,platform_customer_service_logo'
					}
				}
				this.$request(param).then((res) => {
					if (res.state == 200) {
						this.adminTel = res.data[0]
						this.adminSerName = res.data[1]
						this.adminSerAvatar = res.data[2]
					}
				})
			},

			makeCall() {
				if (!this.hasLogin) {
					this.$refs.loginPop.openLogin()
					return
				}

				let chatInfo = {
					memberId: this.userCenterData.memberId,
					memberName: this.userCenterData.memberName,
					memberNickName: this.userCenterData.memberNickName,
					memberAvatar: this.userCenterData.memberAvatar,
					storeId: 0,
					storeLogo: this.adminSerAvatar,
					storeName: this.adminSerName,
					showData: {},
					source: 'userCenter'
				}

				this.saveChatBaseInfo(chatInfo)
				this.$Router.push({
					path: '/standard/chat/detail',
					query: {
						vid: 0
					}
				})
			},

			sellerOpt() {
				this.sellerTip = `${this.$L('请复制地址到电脑端操作')}:\n${this.userCenterData.sellerUrl}`
				this.$refs.popup.open()
			},

			copyClipp() {
				this.$refs.popup.close()
				// #ifdef H5
				const result = h5Copy(this.userCenterData.sellerUrl)
				if (result === false) {
					this.$api.msg(this.$L('不支持'))
				} else {
					this.$api.msg(this.$L('已复制到剪贴板'))
				}
				// #endif

				// #ifdef APP-PLUS || MP-WEIXIN
				uni.setClipboardData({
					data: this.userCenterData.sellerUrl,
					success: function() {
						this.$api.msg(this.$L('已复制到剪贴板'))
					}
				})
				// #endif
			},

			gotoLogin() {
				this.showState = true;

				// #ifdef APP-PLUS || MP
				this.$Router.push('/pages/public/login')
				// #endif

				// #ifdef H5
				let isWexin = this.$isWeiXinBrower()
				if (isWexin) {
					getWxH5Appid()
				} else {
					this.$Router.push('/pages/public/login')
				}
				// #endif
			},

			getUser(e) {
				//wx-9-start
				// #ifdef MP
				if (!process.env.VUE_APP_ALLOW_PRIVACY) {
					this.$refs.priPop.open();
					return;
				}
				// #endif
				//wx-9-end
				if (e.detail.errMsg == 'getUserInfo:ok') {
					let userinfo = e.detail.userInfo
					this.getWxXcxCoce(userinfo)
				}
			},

			getUserProfile(e) {
				//wx-10-start
				// #ifdef MP
				if (!process.env.VUE_APP_ALLOW_PRIVACY) {
					this.$refs.priPop.open();
					return;
				}
				// #endif
				//wx-10-end
				let that = this
				if (!this.isClick) {
					return
				}
				this.isClick = false
				// 推荐使用wx.getUserProfile获取用户信息，开发者每次通过该接口获取用户个人信息均需用户确认
				// 开发者妥善保管用户快速填写的头像昵称，避免重复弹窗
				uni.getUserProfile({
					desc: that.$L('用于完善个人信息'), // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
					success: (res) => {
						that.isClick = true
						if (res.errMsg == 'getUserProfile:ok') {
							let userinfo = res.userInfo
							this.getWxXcxCoce(userinfo)
						}
					},
					complete() {
						that.isClick = true
					}
				})
			},

			//微信小程序根据用户信息获取code
			getWxXcxCoce(userinfo) {
				uni.showLoading({
					title: this.$L('正在请求...'),
					mask: true
				})
				uni.login({
					success: (code) => {
						this.toLogin(code.code, JSON.stringify(userinfo))
					}
				})
			},

			//登录 code为用户的code【微信小程序或者微信h5】  userInfo为获取到的微信用户信息
			toLogin(code, userInfo = '') {
				this.wtCode = code

				let {
					client
				} = this
				let _this = this
				let param = {}
				param.url = 'v3/member/front/login/wechat/login'
				param.data = {}
				param.data.source = client
				param.data.code = code
				//app-2-start



				//app-2-end
				//如果有缓存的购物车数据，登录需要把数据同步，并清除本地缓存
				if (this.$getUnLoginCartParam()) {
					param.data.cartInfo = this.$getUnLoginCartParam()
				}
				if (userInfo) {
					param.data.userInfo = userInfo
				}
				param.method = 'POST'
				this.$request(param)
					.then((res) => {
						if (res.state == 200) {
							uni.hideLoading()
							//更新登录时间
							uni.setStorage({
								key: 'sld_login_time',
								data: new Date().getTime(),
							});

							if (res.data.redirect == undefined) {
								uni.removeStorage({
									key: 'cart_list'
								}) //清除购物车数据
								uni.setStorage({
									key: 'userInfo',
									data: res.data,
									success: () => {
										//登录时间
										res.data.loginTime = Date.parse(new Date())
										this.login(res.data)

									
										//登录成功 获取个人中心的数据
										this.$request({
											url: 'v3/member/front/member/memberInfo'
										}).then((result) => {
											this.setUserCenterData(result.data)
											this.initData()
											this.getSetting()
											this.$forceUpdate()
										})
										
									}
								})
							} else if (res.data.redirect != undefined) {
								//用户未注册，需要绑定手机号进行注册
								this.showState = true

								// #ifndef MP-WEIXIN
								this.$Router.push({
									path: '/pages/public/bindMobile',
									query: {
										code: res.data.bindKey
									}
								})
								// #endif
								//wx-11-start
								// #ifdef MP-WEIXIN
								this.$refs.wxBindPhone.openKey(res.data.bindKey)
								// #endif
								//wx-11-end
							}
						} else if (res.state == 267) {
							//错误提示
							// #ifndef MP-WEIXIN
							_this.$api.msg(_this.$L('请绑定手机号'))
							// #endif
							//wx-12-start
							// #ifdef MP-WEIXIN
							uni.showToast({
								title: _this.$L('请绑定手机号'),
								icon: 'none',
								duration: 2000
							})
							// #endif
							//wx-12-end
							setTimeout(() => {
								_this.$Router.push({
									path: '/pages/public/bindMobile',
									query: {
										u: res.data.u,
										type: 'u'
									}
								})
							}, 2000)
						} else {
							uni.hideLoading()
							//错误提示
							_this.$api.msg(res.msg)
						}
					})
			},

			/**
			 * 统一跳转接口,拦截未登录路由
			 * navigator标签现在默认没有转场动画，所以用view
			 */
			navTo(url) {
				this.showState = true
				if (url == '') {
					this.$sldCommonTip()
					return
				}
				if (!this.hasLogin) {
					if (url == '/pages/user/info') {
						this.$Router.push('/pages/public/login')
					} else {
						this.$refs.loginPop.openLogin()
					}
				} else {
					if (url == '/standard/signIn/signIn') {
						this.signOpenCookie = false
					}
					this.$Router.push(url)
				}
			},
				//去消息页面
			goSpreaderCenter() {
				if (!this.hasLogin) {
					this.$refs.loginPop.openLogin()
				} else {
					this.showState = true
					this.$Router.push('/extra/tshou/user/user')
				}
			},
			//去设置页面
			goSet() {
				if (!this.hasLogin) {
					this.$refs.loginPop.openLogin()
				} else {
					this.showState = true
					this.$Router.push('/pages/set/set')
				}
			},
			//去消息页面
			goMsg() {
				if (!this.hasLogin) {
					this.$refs.loginPop.openLogin()
				} else {
					this.showState = true
					this.$Router.push('/standard/chat/list')
				}
			},
			// 去我的积分页面
			toPointsPage() {
				if (!this.hasLogin) {
					this.$refs.loginPop.openLogin()
				} else {
					this.showState = true
					this.$Router.push('/pages/user/myIntegral')
				}
			},
			//去我的优惠券页面
			goMyCoupon() {
				if (!this.hasLogin) {
					this.$refs.loginPop.openLogin()
				} else {
					this.showState = true
					this.$Router.push('/standard/coupon/myCoupon')
				}
			},
			//去我的积分订单页面
			goMyPointOrder() {
				if (!this.hasLogin) {
					this.$refs.loginPop.openLogin()
				} else {
					this.showState = true
					this.$Router.push({
						path: '/standard/point/order/list',
						query: {
							state: 0
						}
					})
				}
			},
			//去我的视频页面
			goMyVideo() {
				if (!this.hasLogin) {
					this.$refs.loginPop.openLogin()
				} else {
					this.showState = true
					this.$Router.push('/extra/user/my')
				}
			},

			/*
			 * 活动签到提醒
			 * 从签到信息接口获取是否提醒的字段(isRemind)
			 * isRemind为1，则开启提醒，设置cookie存入提醒字段，每天只显示一次
			 *
			 */
			signOpenScreen() {
				this.$request({
					url: 'v3/promotion/front/sign/activity/detail'
				}).then((res) => {
					if (res.state == 200) {
						this.signNotice = res.data.notice.imgPath
						this.isSignOpen = true
						let isRemind = res.data.isRemind
						let signCookie = uni.getStorageSync('signCookie')
						this.isSign = res.data.isSign == 2 ? true : false
						if (isRemind == 1) {
							if (!signCookie) {
								this.signOpenCookie = true
								uni.setStorage({
									key: 'signCookie',
									data: new Date().getTime()
								})
							} else {
								if (
									new Date().getTime() >
									new Date(new Date().toLocaleDateString()).getTime() &&
									new Date().getTime() > signCookie
								) {
									this.signOpenCookie = false
								} else {
									this.signOpenCookie = true
									uni.setStorage({
										key: 'signCookie',
										data: new Date().getTime()
									})
								}
							}
						} else {
							this.signOpenCookie = false
							uni.removeStorage({
								key: 'signCookie'
							})
						}
					} else {
						this.isSignOpen = false
					}
				})
			},
			toInfo() {
				if (this.hasLogin) {
					this.$Router.push('/pages/user/info')
				}
			},

			//获取购物车数据
			getCartNum() {
				if (this.hasLogin) {
					let param = {}
					param.url = 'v3/business/front/cart/cartNum'
					param.method = 'GET'
					param.data = {}
					this.$request(param)
						.then((res) => {
							if (res.state == 200) {
								if (res.data > 0) {
									uni.setTabBarBadge({
										index: 3,
										text: res.data.toString()
									})
								} else {
									uni.hideTabBarRedDot({
										index: 3
									})
								}
							} else {
								this.$api.msg(res.msg)
							}
						})
						.catch((e) => {
							//异常处理
						})
				} else {
					this.getNoLoginCartNum()
				}
			},
			//获取未登录，购物车数量
			getNoLoginCartNum() {
				let cartNum = 0
				let cart_list = uni.getStorageSync('cart_list')
				if (cart_list && cart_list.storeCartGroupList) {
					cart_list.storeCartGroupList.map((item) => {
						item.promotionCartGroupList.map((item1) => {
							item1.cartList.map((item2) => {
								cartNum++
							})
						})
					})
				}
				if (cartNum > 0) {
					uni.setTabBarBadge({
						index: 3,
						text: cartNum.toString()
					})
				} else {
					uni.hideTabBarRedDot({
						index: 3
					})
				}
			},
			//获取购物车数据
			getCartNum() {
				if (this.hasLogin) {
					let param = {}
					param.url = 'v3/business/front/cart/cartNum'
					param.method = 'GET'
					param.data = {}
					this.$request(param)
						.then((res) => {
							if (res.state == 200) {
								if (res.data > 0) {
									uni.setTabBarBadge({
										index: 3,
										text: res.data.toString()
									})
								} else {
									uni.hideTabBarRedDot({
										index: 3
									})
								}
							} else {
								this.$api.msg(res.msg)
							}
						})
						.catch((e) => {
							//异常处理
						})
				} else {
					this.getNoLoginCartNum()
				}
			},
			//前往超级会员充值页
			navtoVip() {
				if (!this.hasLogin) {
					this.$refs.loginPop.openLogin()
				} else {
					this.$Router.push('/standard/super/index?type=' + this.userCenterData.isSuper)
				}
			},
		}
	}
</script>

<style>
	page {
		background: $bg-color-split;
		width: 750rpx;
		margin: 0 auto;
		position: relative;
		background: #f5f5f5;
	}
</style>
<style lang="scss" scoped>
	.container {
		height: 100%;
		position: relative;
		background-size: 750rpx 443rpx;
		/* #ifdef MP||APP-PLUS */
		background-size: 750rpx calc(443rpx + var(--status-bar-height));
		/* #endif */
		background-repeat: no-repeat;
	}

	.picture_adv_poster_con image {
		width: 100%;
		height: 120rpx;
		display: block;
		cursor: pointer;
	}

	.picture_adv_poster_con {
		&:nth-child(2) {
			margin-top: 20rpx;
			margin-bottom: 20rpx;
		}

		margin-top: 20rpx;
		margin-bottom: 20rpx;
	}

	.picture_adv_poster_con .video_poster {
		width: 100%;
		height: 100%;
	}

	%flex-center {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}

	%section {
		display: flex;
		justify-content: space-around;
		align-content: center;
		background: #fff;
		border-radius: 10upx;
	}

	.sld-success-title {
		line-height: 42rpx;
		font-size: 32rpx;
		color: #666666;
		padding: 40rpx 30rpx;
		text-align: center;
	}

	.sld-success {
		background-color: #fff;
		color: #666666;
		font-size: 28rpx;
		width: 500rpx;
		border-radius: 20rpx;
		padding: 30rpx;

		.sld-success-content {
			line-height: 45rpx;
			margin-bottom: 30rpx;
			padding: 0 35rpx;
			color: #999;
			font-size: 26rpx;
		}

		.sld-btns {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-top: 20rpx;
			width: 100%;

			button {
				width: 200rpx;
				height: 50rpx;
				border-radius: 40rpx;
				font-size: 28rpx;
				line-height: 50rpx;
				outline: none;
				background-color: #fff;
				color: #999;
				border: 1px solid #999999;
				margin: 0;

				&.confirm {
					color: #fff;
					background: linear-gradient(135deg,
							#fb3e31 0%,
							#fed600 0%,
							#ff4e00 0%,
							#ff001d 100%);
					border: none;
				}

				&::after {
					border: none;
				}
			}
		}
	}

	.check-in {
		position: absolute;
		//app-3-start



		//app-3-end

		top: 160rpx;

		right: 0;
		width: 140rpx;
		height: 48rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 24rpx 0 0 24rpx;
		color: #fff;
		font-size: 24rpx;
		background: rgba(250, 250, 250, 0.2);
		z-index: 999;
	}

	.check-in image {
		width: 21rpx;
		height: 23rpx;
		margin-right: 5rpx;
	}

	/* 开屏 -- start */
	.open_screen {
		width: 750rpx;
		height: 100vh;
		position: absolute;
		top: 0;
		left: 0;
		background-color: rgba(0, 0, 0, 0.3);
		z-index: 99999;
	}

	.open_screen .open_screen_con {
		maring: 0 auto;
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		border-radius: 15rpx;
		display: flex;
		align-items: center;
		flex-direction: column;
	}

	.open_screen .open_screen_con .open_screen_con_img {
		background-size: contain;
		border-radius: 15rpx;
	}

	.open_screen .open_screen_con .con_img {
		width: 58rpx;
		height: 58rpx;
		position: relative;
		top: 0;
		right: 0;
		z-index: 999;
		align-self: flex-end;
	}

	.open_screen_con .con_img image {
		width: 100%;
		height: 100%;
	}

	/* 开屏 -- end */

	/* 会员信息上滑 start */
	.member_info_sliding {
		width: 750rpx;
		background: linear-gradient(135deg,
				#fb3e31 0%,
				#fed600 0%,
				#ff4e00 0%,
				#ff001d 100%);
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 30rpx 10rpx 20rpx;
		box-sizing: border-box;
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		margin: 0 auto;
		z-index: 10;

		.member_info_sliding_left {
			text {
				margin-left: 20rpx;
				font-size: 30rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: #010101;
			}

			image {
				width: 78rpx;
				height: 78rpx;
				border: 4rpx solid rgba(255, 255, 255, 0.5);
				border-radius: 50%;
			}
		}

		.member_info_sliding_center {
			font-size: 36rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #ffffff;
			line-height: 32rpx;
		}

		.member_info_sliding_right {
			display: flex;
			align-items: center;

			.user_sets {
				width: 42rpx;
				height: 42rpx;

				image {
					width: 40rpx;
					height: 40rpx;
				}
			}

			.user_msg {
				position: relative;
				display: flex;
				align-items: center;

				.user_msg_img {
					width: 40rpx;
					height: 40rpx;
					margin-right: 24rpx;
				}

				text {
					position: absolute;
					top: -6rpx;
					right: 16rpx;
					width: 26rpx;
					height: 26rpx;
					line-height: 26rpx;
					background: #ffffff;
					border-radius: 50%;
					font-size: 20rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #ffffff;
					text-align: center;
					background-color: #ff152c;
				}
			}
		}
	}

	/* 会员信息上滑 end */

	.user-section {
		position: relative;
		padding: 22rpx 22rpx 0;
		//app-4-start



		//app-4-end
		//wx-13-start
		/* #ifdef MP */
		padding: 45rpx 22rpx 0;

		/* #endif */
		//wx-13-end
		.bg {
			position: absolute;
			left: 0;
			top: 0;
			width: 100%;
			height: 382rpx;
			//app-5-start



			//app-5-end
		}

		.user_info_tab {
			z-index: 1;
			margin-top: 20rpx;
			padding-left: 54rpx;
			padding-right: 54rpx;

			.user_info_tab_item {
				.tab_item_text1 {
					font-size: 26rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #333333;
				}

				.tab_item_text2 {
					font-size: 36rpx;
					font-family: PingFang SC;
					font-weight: bold;
					color: #000000;
				}
			}
		}
	}

	.user_set {
		display: flex;
		justify-content: flex-end;
		align-items: center;

		.user_set_img {
			width: 40rpx;
			height: 40rpx;
		}

		.sign_entry {
			width: 156rpx;
			height: 52rpx;
			line-height: 52rpx;
			border: 1px solid #bcbcbc;
			background: rgba(249, 249, 249, 0.3);
			border-radius: 26rpx;
			margin-right: 26rpx;

			image {
				width: 32rpx;
				height: 32rpx;
				margin-right: 4rpx;
			}

			text {
				color: #333;
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 500;
			}
		}

		.user_msg {
			position: relative;

			.user_msg_img {
				width: 40rpx;
				height: 40rpx;
				margin-right: 24rpx;
			}

			text {
				min-width: 28rpx;
				height: 28rpx;
				line-height: 28rpx;
				background: #ffffff;
				font-size: 20rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #ffffff;
				text-align: center;
				background-color: var(--color_price);
				border-radius: 50rpx;
				position: absolute;
				top: -10rpx;
				left: 50%;
				transform: translateX(-40%);
			}
		}
	}

	.user-info-box {
		display: flex;
		align-items: center;
		position: relative;
		z-index: 1;

		.portrait-box {
			width: 128rpx;
			height: 128rpx;
			border-radius: 50%;
			box-sizing: border-box;
			overflow: hidden;
			display: flex;
			align-items: center;
			justify-content: center;

			.portrait-bgc {
				width: 100rpx;
				height: 100rpx;
				box-sizing: border-box;
				border-radius: 50%;
				overflow: hidden;
				background-color: #ffffff;
				display: flex;
				align-items: center;
				justify-content: center;

				.portrait {
					width: 100rpx;
					height: 100rpx;
					border-radius: 50%;
				}
			}

			.portrait-vip {
				position: absolute;
				bottom: 4rpx;
				color: #F8E2D1;
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 500;
				text-align: center;
				border-radius: 4rpx;
				background: linear-gradient(#51483f, #43362d);
				padding: 4rpx 10rpx;
				transform: scale(0.85);
				letter-spacing: 2rpx;
			}
		}

		.mem-info {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: flex-start;
			margin-left: 10rpx;

			.nick-name {
				color: #222336;
				font-size: 38rpx;
				font-weight: bold;
				max-width: 250rpx;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}

			.member-name {
				color: #222336;
				font-size: 20rpx;
				border-radius: 13rpx;
				margin-top: 13rpx;
				height: 28rpx;
				display: flex;
				justify-content: center;
				align-items: center;
			}

			.login_btn {
				background: transparent;
				line-height: 44rpx;
				height: 44rpx;
				padding-left: 0;

				&::after {
					border: none;
				}

				text-align: left;
			}
		}
	}

	/* 我的记录 start */
	.mine_record {
		width: 710rpx;
		background: #ffffff;
		border-radius: 10rpx;
		display: flex;
		justify-content: space-around;
		padding: 35rpx 20rpx 41rpx;
		box-sizing: border-box;
		margin: -127rpx auto 20rpx;
		//app-6-start



		//app-6-end
		position: relative;

		.mine_record_pre {
			display: flex;
			flex-direction: column;
			align-items: center;

			image {
				width: 45rpx;
				height: 45rpx;
				margin-bottom: 30rpx;
			}

			text {
				font-size: 26rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #666666;
				line-height: 26rpx;
			}
		}
	}

	/* 我的记录 end */

	.main-content {
		width: 750rpx;
		padding: 0 $com-v-border;
		position: relative;
		margin-top: 32rpx;

		/* 超级会员 start */
		.super_user {
			position: relative;
			top: 20rpx;
			width: 100%;
			height: 114rpx;
			padding: 0 20rpx;
			background-size: cover;
			background-position: center;
			background-repeat: no-repeat;

			.super_user_left {
				position: relative;
				bottom: 8rpx;

				.super_user_vip {
					width: 50rpx;
					height: 40rpx;
					margin-right: 18rpx;

					&.sm {
						margin-top: 4rpx;
						width: 41rpx;
						height: 33rpx;
					}
				}

				.super_user_tips {
					font-family: PingFang SC;

					.super_user_title {
						color: #F8D4AC;
						font-size: 28rpx;
						font-weight: 500;

						&.isOff {
							color: #515259;
						}
					}

					.super_user_time {
						color: #AC9073;
						font-size: 22rpx;
						font-family: PingFang SC;
						font-weight: 400;
					}
				}
			}

			.super_user_right {
				position: relative;
				bottom: 8rpx;

				.super_user_btn {
					width: 162rpx;
					height: 48rpx;
					line-height: 48rpx;
					color: #201C19;
					font-size: 28rpx;
					font-family: PingFang SC;
					font-weight: 500;
					text-align: center;
					background: linear-gradient(-90deg, #FDCB9A, #FBE7CF);
					border-radius: 24rpx;

					&.repay {
						color: #F9D7AF;
						background: #000000;
					}
				}
			}
		}

		/* 超级会员 end */



		.order-part {
			width: 100%;
			height: 236rpx;
			background: #fff;
			box-shadow: 0px 0px 20px 0px rgba(153, 153, 153, 0.15);
			border-radius: 18rpx;
			position: relative;

			.title {
				width: 100%;
				height: 70rpx;
				margin-top: 12rpx;
				margin-bottom: 20rpx;
				padding-right: 30rpx;
				box-sizing: border-box;

				.left {
					flex-shrink: 0;
					padding-left: 29rpx;
					font-size: 30rpx;
					font-family: PingFang SC;
					font-weight: bold;
					color: #15151a;
				}

				.right {
					text {
						flex-shrink: 0;
						font-size: 26rpx;
						font-family: PingFang SC;
						font-weight: 400;
						color: #888888;
						margin-right: 10rpx;
					}

					image {
						width: 11rpx;
						height: 19rpx;
						opacity: 0.6;
					}
				}
			}

			.detail {
				width: 100%;

				.item {
					position: relative;

					.detail_top {
						position: relative;
					}

					.show-text {
						color: #333333;
						font-size: 28rpx;
						line-height: 28rpx;
						margin-top: 12rpx;
					}

					.nums {
						box-sizing: border-box;
						position: absolute;
						top: -12rpx;
						right: -12rpx;
						min-width: 30rpx;
						height: 30rpx;
						line-height: 30rpx;
						color: #fff;
						font-size: 20rpx;
						font-family: PingFang SC;
						font-weight: 500;
						text-align: center;
						border-radius: 16rpx 16rpx 16rpx 0;
						padding: 0rpx 5rpx;
						background: var(--color_price);
					}

					.num {
						border-radius: 50%;
						border-bottom-left-radius: 0;
					}

					image {
						width: 54rpx;
						height: 54rpx;
					}
				}
			}
		}

		/* 常用服务 start */
		.common_services {
			width: 710rpx;
			min-height: 485rpx;
			background: #ffffff;
			border-radius: 15rpx;
			margin: 20rpx auto 0;

			.common_services_title {
				width: 100%;
				height: 92rpx;
				line-height: 92rpx;
				color: #2d2d2d;
				font-size: 30rpx;
				font-family: PingFang SC;
				font-weight: bold;
				border-bottom: 1rpx solid #f8f8f8;
				padding-left: 30rpx;
			}

			.common_services_list {
				display: flex;
				flex-direction: column;

				.common_services_pre {
					height: 110rpx;
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin: 0 30rpx;
					border-bottom: 1rpx solid #f8f8f8;

					&:nth-last-child(1) {
						border-bottom: none;
					}

					.common_serv_pre_left {
						display: flex;
						align-items: center;

						image {
							width: 54rpx;
							height: 54rpx;
						}

						text {
							font-size: 28rpx;
							font-family: PingFang SC;
							font-weight: 500;
							color: #2d2d2d;
							line-height: 39rpx;
							margin-left: 25rpx;
						}
					}

					.common_serv_pre_right {
						display: flex;
						align-items: center;

						text {
							font-size: 28rpx;
							font-family: PingFang SC;
							font-weight: bold;
							color: #333333;
							line-height: 39rpx;
							margin-right: 20rpx;
						}

						image {
							width: 11rpx;
							height: 19rpx;
							opacity: 0.6;
						}
					}
				}
			}
		}

		/* 常用服务 end */

		.recommend-title {
			display: flex;
			justify-content: center;

			image {
				width: 387rpx;
				height: 34rpx;
				margin-top: 30rpx;
				margin: 30rpx 0;
			}
		}

		.recommend-goods {
			flex-wrap: wrap;
		}
	}

	.fixed_top_status_bar {
		//app-7-start



		//app-7-end

		height: 0;

		top: 0;
		left: 0;
		right: 0;
		z-index: 99;
		background: transparent;
	}
</style>