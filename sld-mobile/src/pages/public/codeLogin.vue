<template>
	<view :style="mix_diyStyle">
		<view class="main">
			<image class="bgImg" :src="bgImg" mode="widthFix"></image>
			<view class="title">商城账号电脑端登录确认</view>
			<view class="tips">为确保账号安全，请确认是您本人操作</view>
			<view class="submit" @click="login">确认登录电脑端</view>
			<view class="cancle" @click="back">取消登录</view>
		</view>
	</view>
</template>

<script>
	import {mapState} from 'vuex'
	export default {
		data() {
			return {
				bgImg: process.env.VUE_APP_IMG_URL + 'index/login_success.png',
				refresh_token: '',
				u: '',
				isLogin: false, //是否点击确认登录
			}
		},
		onLoad(options) {
			this.u = this.$Route.query.u;
		},
		onShow() {
		},
		computed:{
			...mapState(['userInfo'])
		},
		methods: {
			// 授权登录
			login() {
				let _this = this;
				if (_this.isLogin) {
					return;
				}
				_this.isLogin = true;
				let {member_refresh_token} = this.userInfo
				let param = {}
				param.url = 'v3/member/front/qr/login/grant'
				param.method = 'POST'
				param.data = {
					refreshToken: member_refresh_token,
					u: _this.u,
				}
				if (!member_refresh_token) {
					// 未登录
					_this.$Router.push({
						path: '/pages/public/login',
						query: {
							scanCode: true
						}
					});
					_this.isLogin = false;
					return;
				}
				_this.$request(param).then(res => {
					if (res.state == 200) {
						uni.showToast({
							title: '授权登录成功',
							duration: 1500
						})
						setTimeout(() => {
							_this.back();
						}, 1500)
					} else if (res.state == 266) {
						// 未登录
						_this.$Router.push({
							path: '/pages/public/login',
							query: {
								scanCode: true
							}
						});
						_this.isLogin = false;
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 1500
						})
						_this.isLogin = false;
					}
				})
			},
			// 返回上一页
			back() {
				this.$Router.back(1)
			}
		}
	}
</script>

<style scoped>
	.main {
		display: flex;
		flex-direction: column;
		justify-content: center;
	}

	.bgImg {
		width: 260rpx;
		height: 180rpx;
		margin: 170rpx auto 120rpx;
	}

	.title {
		color: #333333;
		font-size: 30rpx;
		font-weight: 700;
		text-align: center;
		margin-bottom: 30rpx;
	}

	.tips {
		color: #797979;
		font-size: 26rpx;
		text-align: center;
	}

	.submit {
		width: 688rpx;
		line-height: 80rpx;
		color: #ffffff;
		font-size: 30rpx;
		text-align: center;
		background: var(--color_main_bg);
		border: 2rpx solid var(--color_main);
		border-radius: 44rpx;
		margin: 94rpx auto 24rpx;
	}

	.cancle {
		width: 688rpx;
		line-height: 80rpx;
		color: var(--color_vice);
		font-size: 30rpx;
		text-align: center;
		border: 2rpx solid var(--color_vice);
		border-radius: 44rpx;
		margin: 0 auto;
	}
</style>