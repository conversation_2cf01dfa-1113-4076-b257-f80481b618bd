<template>
	<view class="container" :style="mix_diyStyle">
		<text class="back-btn iconfont iconziyuan2" @click="navBack"></text>
		<!-- 设置白色背景防止软键盘把下部绝对定位元素顶上来盖住输入框等 -->
		<view class="wrapper" v-if="bindState">
			<view class="login-title">
				{{ $L('绑定手机号') }}
			</view>
			<view class="input-content">
				<view class="input-item">
					<input type="number" :value="mobile" :placeholder="$L('请输入手机号')" maxlength="11" data-key="mobile"
						@input="inputChange" @focus="setFocus" />
					<text class="clear-account iconfont iconziyuan34" v-show="mobile && curFocus == 'mobile'"
						@click="clearContent('mobile')"></text>
				</view>
				<!-- <view class="input-item">
					<input type="text" :value="imgCode" maxlength="4" :placeholder="$L('请输入图形验证码')" data-key="imgCode"
						@input="inputChange" @focus="setFocus" />
					<view class="pwd-right">
						<text class="clear-pwd iconfont iconziyuan34" v-show="imgCode && curFocus == 'imgCode'"
							@click="clearContent('imgCode')"></text>
						<image @click="getImgCode" :src="showCodeImg" class="img_code" />
					</view>
				</view>  -->
				<view class="input-item pwd_wrap" v-if="verificationCodeCheckIsEnable !== 0">
					<input type="number" :value="smsCode" maxlength="6" :placeholder="$L('请输入短信验证码')" data-key="smsCode"
						@input="inputChange" @confirm="toLogin" @focus="setFocus" />
					<view class="pwd-right">
						<text class="clear-pwd iconfont iconziyuan34" v-show="smsCode && curFocus == 'smsCode'"
							@click="clearContent('smsCode')"></text>
						<view :style="{ opacity: countDownM ? 0.3 : 1 }" class="sms-code-view" @click="getSmsCode">
							<text class="sms-code">{{ countDownM ? `${countDownM}s${$L('后重新获取')}` : $L('获取验证码') }}</text>
						</view>
					</view>
				</view>
			</view>
			<button class="confirm-btn" @click="toLogin"
				:style="{ opacity: verificationCodeCheckIsEnable !== 0 ? (!(mobile && smsCode && imgCode) || logining ? 0.5 : 1) : (!(mobile && imgCode) || logining ? 0.5 : 1) }">
				{{ $L('点我登录') }}
			</button>
		</view>
		<!-- 绑定选择部分 -->
		<view class="wrapper" v-else>
			<view class="bind_state_top">
				<image class="mobile_logo" :src="imgUrl + 'bind_mobile.png'"></image>
				<text class="mobile_num">{{ mobile }}</text>
				<text class="mobile_binded">{{ $L('该手机号已被绑定') }}</text>
			</view>
			<view class="bind_change_info">
				<view class="bind_change_info_text">{{ $L('继续绑定：将解除与账号') }}<text class="change_info_mobile">{{ bindedAccount
				}}</text>{{ $L('的绑定关系') }}</view>
				<view class="bind_change_info_text" v-if="type_u != 'u'">{{ $L('更新信息：授权信息将绑定到账号') }}<text
						class="change_info_mobile">{{ bindedAccount }}</text>{{ $L('上') }}</view>
			</view>
			<view class="bind_state_btn_con" v-if="type_u != 'u'">
				<view class="update_btn" @click="confirmBind(3)" v-if="type_u != 'u'">{{ $L('更新信息') }}</view>
				<view class="go_on_btn" @click="confirmBind(2)">{{ $L('继续绑定') }}</view>
			</view>
			<view class="bind_state_btn_con" v-else style="justify-content: center;">
				<view class="go_on_btn_one" @click="confirmBind(2)">{{ $L('继续绑定') }}</view>
			</view>
		</view>
	</view>
</template>

<script>
import { fetchImgCode } from '@/utils/common.js';
import {
mapMutations,
mapState
} from 'vuex';
export default {
	data() {
		return {
			bindState: true,
			imgUrl: process.env.VUE_APP_IMG_URL,
			mobile: '',
			smsCode: '',
			logining: false,
			countDownM: 0, //短信验证码倒计时
			timeOutId: '', //定时器的返回值
			curFocus: '', //当前光标所在的位置
			code: '', //用户code
			bindType: 1, //绑定方式，默认 1==去绑定，检测手机号是否已绑定其他账号；2==继续绑定，新增一条会员信息；3==更新信息，更新原会员的微信信息
			client: 1, //终端类型， 1、H5(微信内部浏览器) 2、H5(微信小程序)；3、app
			bindedAccount: '', //已经绑定的账号
			source: '', //页面来源，该用户已登录，却强行被解绑了，来源为 'account'
			isPreventClick: false,
			spreaderKey: '',
			showCodeImg: '',
			imgCode: 'ABCD', //图形验证码
			imgCodeKey: 'ABCD', //图形验证码的key
			u: '',
			type_u: '',
		}
	},
	onLoad(option) {
		this.initClient()
		if (this.$Route.query.code) {
			this.code = this.$Route.query.code
		}
		if (this.$Route.query.source) {
			this.source = this.$Route.query.source
		}
		if (this.$Route.query.spreaderKey) {
			this.spreaderKey = decodeURIComponent(this.$Route.query.spreaderKey)
		}
		if (this.$Route.query.u) {
			this.u = this.$Route.query.u
		}
		if (this.$Route.query.type && this.$Route.query.type == 'u') {
			this.type_u = 'u'
		}
		// this.getImgCode()
	},

	computed: {
		// dev_tl-start
		// dev_tld-start
		...mapState(['$Tlenable', 'verificationCodeCheckIsEnable'])
		// dev_tld-end
		// dev_tl-end
	},

	methods: {
		...mapMutations(['login', 'setUserCenterData']),
		//光标聚焦事件
		setFocus(e) {
			this.curFocus = e.currentTarget.dataset.key
		},
		inputChange(e) {
			const key = e.currentTarget.dataset.key
			this[key] = e.detail.value

		},
		navBack() {
			this.$Router.back(1)
		},

		//提示绑定操作
		confirmBind(type) {
			this.bindType = type;
			this.toLogin();
		},


		//初始化终端类型
		initClient() {
			let {
				client
			} = this
			//app-1-start



			//app-1-end
			//wx-1-start
			//#ifdef MP-WEIXIN
			client = 4
			//#endif
			//wx-1-end
			//#ifdef H5
			client = 3
			//#endif
			this.client = client
		},

		toLogin() {
			const {
				mobile,
				smsCode,
				logining,
				code,
				bindType,
				client,
				imgCode
			} = this
			let _this = this
			// Modify validation to check SMS code only when verification is enabled
			if (this.verificationCodeCheckIsEnable !== 0) {
				if (!(mobile && smsCode && imgCode) || logining) {
					return
				}
			} else {
				if (!(mobile && imgCode) || logining) {
					return
				}
			}
			let param = {}
			if (this.$Route.query.type && this.$Route.query.type == 'u') {
				param.url = 'v3/member/front/login/wechat/bindMobile2'
			} else {
				param.url = 'v3/member/front/login/wechat/bindMobile'
			}
			param.data = {}
			param.data.bindType = bindType
			param.data.mobile = mobile
			param.data.resource = client
			param.data.smsCode = smsCode
			if(this.verificationCodeCheckIsEnable==0){
				param.data.smsCode='123456'
			}
			param.data.verifyCode = this.imgCode
			param.data.verifyKey = this.imgCodeKey
			//app-2-start






			//app-2-end
			if (!_this.$checkMobile(mobile)) {
				return false
			}
			if (this.spreaderKey && this.type_u != 'u') {
				param.data.spreaderKey = this.spreaderKey
			}
			//如果有缓存的购物车数据，登录需要把数据同步，并清除本地缓存
			if (this.$getUnLoginCartParam()) {
				param.data.cartInfo = this.$getUnLoginCartParam()
			}
			_this.logining = true
			if (this.type_u != 'u') {
				param.data.bindKey = code
			} else {
				param.data.u = _this.u
			}
			param.method = 'POST'
			_this.$request(param).then((res) => {
				if (res.state == 200) {
					//更新登录时间
					uni.setStorage({
						key: 'sld_login_time',
						data: new Date().getTime(),
					});
					uni.removeStorage({
						key: 'cart_list'
					}) //清除购物车数据
					res.data.loginTime = Date.parse(new Date()) //登录时间
					_this.login(res.data)
					if (res.data.reg) {
						this.$sldStatEvent({
							behaviorType: 'reg'
						}) //统计埋点
					}

					let fromurl = uni.getStorageSync('fromurl')
					if (fromurl.url) {
						console.log('返回登录前页面')
						setTimeout(() => {
							_this.$Router.replace({
								path: fromurl.url,
								query: fromurl.query
							})
						}, 1000)
						return
					}

					//获取个人中心的数据
					_this.$request({
						url: 'v3/member/front/member/memberInfo'
					}).then((result) => {
						uni.removeStorage({ key: 'u' })
						if (_this.source == 'account') {
							const pages = getCurrentPages() //当前页面栈
							if (pages.length > 1) {
								const beforePage = pages[pages.length - 2] //获取上一个页面实例对象
								beforePage.$vm.isBindMobile = true //修改上一页的数据 	isBindMobile 为true，即为已绑定
							}
							_this.$Router.back(1)
						} else {
							uni.setStorageSync('fromurl', {
								url: '/pages/user/user'
							})
							_this.setUserCenterData(result.data)
							_this.$loginGoPage()
						}
					})

				} else if (res.state == 267) {
					//手机号已被绑定,提示用户
					_this.bindState = false
					_this.bindedAccount = res.data
				} else {
					//错误提示
					this.getImgCode()
					_this.$api.msg(res.msg)
				}
				_this.logining = false
			})
				.catch((e) => { })
		},

		//清空输入的内容
		clearContent(type) {
			this[type] = ''
		},

		//获取图形验证码
		async getImgCode() {
			const result = await fetchImgCode()
			this.showCodeImg = result.codeImg
			this.imgCodeKey = result.imgCodeKey
		},

		//提示绑定操作
		confirmBind(type) {
			this.bindType = type
			this.toLogin()
		},

		//获取短信验证码
		getSmsCode() {
			if (this.isPreventClick) {
				return
			}
			this.isPreventClick = true

			if (this.countDownM) {
				return
			}
			if (!this.$checkMobile(this.mobile)) {
				return false
			} else if (!this.imgCode) {
				this.isPreventClick = false
				this.$api.msg(this.$L('请输入图形验证码!'))
			} else {

				let param = {}
				param.data = {}
				param.data.mobile = this.mobile


				// dev_tld-start
				if (!this.$Tlenable) {
					param.url = 'v3/msg/front/commons/smsCode'
					param.data.type = 'wxAuth'
					param.data.verifyCode = this.imgCode
					param.data.verifyKey = this.imgCodeKey
				}
				// dev_tld-end


				// dev_tl-start
				if (this.$Tlenable) {
					param.url = 'v3/member/common/sendTlVerify'
				}
				// dev_tl-end

				this.$request(param).then((res) => {
					this.$api.msg(res.msg)
					if (res.state == 200) {
						this.countDownM = 60
						this.countDown()
					} else {
						this.isPreventClick = false
					}
				})
			}
		},
		//跳转事件 type:跳转类型，1为redirectTo 2为navigateTo
		navTo(url, type) {
			if (type == 1) {
				this.$Router.replace(url)
			} else if (type == 2) {
				this.$Router.push(url)
			}
		},
		//倒计时
		countDown() {
			this.countDownM--
			if (this.countDownM == 0) {
				this.isPreventClick = false
				clearTimeout(this.timeOutId)
			} else {
				this.timeOutId = setTimeout(this.countDown, 1000)
			}
		}
	}
}
</script>

<style lang="scss">
page {
	background: #fff;
}

.container {
	position: relative;
	width: 750rpx;
	height: 100vh;
	overflow: hidden;
	background: #fff;
}

.wrapper {
	position: relative;
	z-index: 90;
	background: #fff;
	padding-bottom: 40upx;
}

.back-btn {
	margin-left: 40rpx;
	margin-top: 40rpx;
	/* #ifndef H5 */
	margin-top: 88rpx;
	/* #endif */
	font-size: 32rpx;
	color: $main-font-color;
	display: inline-block;
}

.login-title {
	position: relative;
	margin-top: 90rpx;
	margin-bottom: 70rpx;
	margin-left: 65rpx;
	font-size: 36rpx;
	color: #333;
	font-weight: bold;

	&:after {
		position: absolute;
		left: 0;
		bottom: -10rpx;
		content: '';
		width: 76rpx;
		height: 6rpx;
		background: linear-gradient(90deg,
				rgba(252, 28, 28, 1) 0%,
				rgba(255, 138, 0, 0) 100%);
	}
}

.input-content {
	padding: 0 65rpx;
}

.input-item {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	justify-content: center;
	height: 80rpx;
	margin-bottom: 50upx;
	border-bottom: 1px solid rgba(0, 0, 0, 0.1);
	position: relative;

	image {
		width: 132rpx;
		height: 55rpx;
		box-shadow: 0px 5rpx 9rpx 1rpx rgba(102, 102, 102, 0.1);
		border-radius: 6rpx;
		margin-left: 20rpx;
	}

	input {
		color: #2d2d2d;
		font-size: 30rpx;
	}

	.clear-account {
		position: absolute;
		right: 6rpx;
		top: 28rpx;
		font-size: 26rpx;
		color: #ddd;
	}


	.pwd-right {
		position: absolute;
		right: 6rpx;
		top: 6rpx;
		display: flex;
		align-items: center;

		.clear-pwd {
			font-size: 26rpx;
			color: #ddd;
		}

		.sms-code-view {
			border: 1px solid var(--color_main);
			padding: 14rpx;
			border-radius: 6rpx;
			line-height: 0;
			margin-left: 20rpx;

			.sms-code {
				color: var(--color_main);
				font-size: 24rpx;
				line-height: 24rpx;
			}
		}
	}


	.tit {
		height: 50upx;
		line-height: 56upx;
		font-size: $font-sm + 2upx;
		color: $font-color-base;
	}

	input {
		height: 60upx;
		font-size: $font-base + 2upx;
		color: $font-color-dark;
	}
}

.confirm-btn {
	width: 620rpx;
	height: 88rpx;
	line-height: 88rpx;
	color: #fff;
	font-size: 36rpx;
	margin-top: 90rpx;
	background: linear-gradient(90deg,
			rgba(252, 31, 29, 1) 0%,
			rgba(253, 115, 38, 1) 100%);
	box-shadow: 0px 3rpx 14rpx 1rpx rgba(253, 38, 29, 0.26);
	border-radius: 44rpx;
}

.other-login {
	position: absolute;
	left: 0;
	bottom: 140rpx;
	width: 100%;
	display: flex;
	flex-direction: column;

	.title {
		display: flex;
		justify-content: center;
		align-items: center;

		&:before {
			content: ' ';
			width: 150rpx;
			height: 1rpx;
			background: #cbcbcb;
		}

		&:after {
			content: ' ';
			width: 150rpx;
			height: 1rpx;
			background: #cbcbcb;
		}

		text {
			color: #999999;
			font-size: 26rpx;
			margin: 0 20rpx;
		}
	}

	.login-method {
		display: flex;
		justify-content: center;
		margin-top: 20rpx;

		.wechat-login {
			display: flex;
			flex-direction: column;
			align-items: center;
			width: 100%;

			.wechat-icon {
				width: 110rpx;
				height: 110rpx;
			}

			text {
				color: #666666;
				font-size: 26rpx;
			}
		}
	}
}

.agreement-part {
	position: absolute;
	left: 0;
	bottom: 60rpx;
	width: 100%;
	font-size: 26rpx;
	color: #999999;
	text-align: center;

	.agreement {
		color: var(--color_main);
		border-bottom: 1rpx solid var(--color_main);
	}
}

.login-register {
	display: flex;
	justify-content: center;
	margin-top: 33rpx;

	.mobile-login {
		color: #2d2d2d;
		font-size: 28rpx;
		line-height: 34rpx;
		border-right: 1px solid rgba(0, 0, 0, 0.1);
		padding-right: 30rpx;
		margin-right: 30rpx;
	}

	.register {
		color: var(--color_main);
		font-size: 28rpx;
		line-height: 34rpx;
	}
}

/* 绑定结果页 */
.bind_state_top {
	margin-top: 172rpx;
	display: flex;
	flex-direction: column;
	align-items: center;

	.mobile_logo {
		width: 120rpx;
		height: 120rpx;
	}

	.mobile_num {
		font-size: 30rpx;
		font-weight: 500;
		color: #fc2624;
		margin-top: 39rpx;
	}

	.mobile_binded {
		font-size: 30rpx;
		font-weight: 500;
		color: #333333;
		margin-top: 19rpx;
	}
}

.bind_change_info {
	font-size: 28rpx;
	text-align: center;
	color: #666666;
	line-height: 39rpx;
	margin-top: 51rpx;

	.change_info_mobile {
		color: #333333;
	}
}

.bind_state_btn_con {
	width: 620rpx;
	height: 70rpx;
	margin: 0 auto;
	margin-top: 80rpx;
	font-size: 30rpx;
	display: flex;

	.update_btn {
		width: 308rpx;
		height: 69rpx;
		line-height: 69rpx;
		color: #ff0000;
		text-align: center;
		border-radius: 35rpx 0 0 35rpx;
		border: 1px solid #ff0000;
	}

	.go_on_btn {
		width: 308rpx;
		height: 69rpx;
		line-height: 69rpx;
		text-align: center;
		background-color: #ff0000;
		color: white;
		border-radius: 0 35rpx 35rpx 0;
		border: 1px solid #ff0000;
	}

	.go_on_btn_one {
		width: 308rpx;
		height: 69rpx;
		line-height: 69rpx;
		text-align: center;
		background-color: #ff0000;
		color: white;
		border-radius: 35rpx;
		border: 1px solid #ff0000;
	}
}
</style>