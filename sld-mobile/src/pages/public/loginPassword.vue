<template>
	<view class="container" :style="mix_diyStyle">
		<!-- #ifndef MP -->
		<view class="back-btn" :class="{ wx_back_btn: isWeiXinH5 }">
			<text class="iconfont iconziyuan2" @click="navBack"></text>
		</view>
		<!-- #endif -->

		<!-- 设置白色背景防止软键盘把下部绝对定位元素顶上来盖住输入框等 -->

		<view class="wrapper">
			<view class="login-title">
				{{ $L('密码登录') }}
			</view>
			<view class="input-content">
				<view class="input-item">
					<input type="text" :value="mobile" :placeholder="$L('请输入账号/手机号')" maxlength="20" data-key="mobile"
						@input="inputChange" @focus="setFocus" @blur="handleAcc" placeholder-class="placeStyle"
						:adjust-position="false" />
					<text class="clear-account iconfont iconziyuan34" v-show="mobile && curFocus == 'mobile'"
						@click="clearContent('mobile')"></text>
				</view>
				<view class="input-item pwd_wrap">
					<input type="text" style="width: 56%" :value="password" :placeholder="$L('请输入密码')" maxlength="20"
						:password="!showPwd" data-key="password" @input="inputChange" @confirm="toLogin"
						:adjust-position="false" @focus="setFocus" placeholder-class="placeStyle" />
					<view class="pwd-right">
						<text class="clear-pwd iconfont iconziyuan34" v-show="password && curFocus == 'password'"
							@click="clearContent('password')"></text>
						<text :class="{ 'pwd-tab': true, iconfont: true, iconziyuan81: showPwd, iconziyuan9: !showPwd }"
							@click.stop="changePwdState"></text>
						<text class="forget-pwd" @click.stop="navTo('/pages/public/forgetPwd', 2)">{{ $L('忘记密码') }}</text>
					</view>
				</view>

				<view class="input-item">
					<input type="text" :value="imgCode" maxlength="4" :placeholder="$L('请输入图形验证码')" data-key="imgCode"
						@input="inputChange" @focus="setFocus" placeholder-class="placeStyle" />
					<view class="pwd-right flex_row_center_center">
						<text class="clear-pwd iconfont iconziyuan34" v-show="imgCode && curFocus == 'imgCode'"
							@click="clearContent('imgCode')"></text>
						<image @click="getImgCode" :src="showCodeImg" class="img_code" />
					</view>
				</view>
			</view>
			<button class="confirm-btn" :class="{ wx_confirm_btn: isWeiXinH5 }" @click="toLogin"
				:style="{ opacity: !(mobile && password && imgCode) || logining ? 0.5 : 1 }">
				{{ $L('登录') }}
			</button>
			<view class="login-register">
				<text class="mobile-login" @click="navTo('/pages/public/loginMobile', 1)">{{ $L('验证码登录') }}</text>
				<text class="register" @click="navTo('/pages/public/register', 1)">{{ $L('用户注册') }}</text>
			</view>
		</view>
		<view class="other-login">
			<!-- #ifdef H5 -->
			<view class="title" v-if="isWeiXinH5" @click="navTo('/pages/public/loginMobile', 1)">
				<text>{{ $L('其他登录') }}</text>
			</view>
			<!-- #endif -->

			<!-- #ifndef H5 -->
			<view class="title" v-if="isWxEnable == 1">
				<text>{{ $L('其他登录') }}</text>
			</view>
			<!-- #endif -->

			<view class="login-method">
				<!-- wx-1-start -->
				<!-- #ifdef MP-WEIXIN -->
				<button class="wechat-login" v-if="canIUseGetUserProfile" @tap="getUserProfile">
					<image class="wechat-icon" :src="imgUrl + 'wechat_icon.png'" mode="aspectFill" />
					<text>{{ $L('一键登陆') }}</text>
				</button>

				<button class="wechat-login" v-if="!canIUseGetUserProfile" open-type="getUserInfo" @getuserinfo="getUser">
					<image class="wechat-icon" :src="imgUrl + 'wechat_icon.png'" mode="aspectFill" />
					<text>{{ $L('一键登陆') }}</text>
				</button>
				<!-- #endif -->
				<!-- wx-1-end -->
				<!-- app-1-start -->
				<!-- app-1-end -->
			</view>
		</view>
		<!-- #ifdef H5 -->
		<view class="agreement-part">{{ $L('登录即代表您已同意') }}<text class="agreement"
				@click="agreement('register_agreement')">{{ $L('《用户协议》') }}</text>{{ $L('和') }}
			<text class="agreement" @click="agreement('privacy_policy')">{{ $L('《隐私协议》') }}</text>
		</view>
		<!-- #endif -->
		<!-- #ifdef APP-PLUS||MP-WEIXIN -->
		<view class="agreement-part flex_row_center_center">
			<view @click="checkAgrement" class="flex_row_center_center" style="margin-right: 10rpx;margin-top: 4rpx;">
				<svgGroup type="checked2" width="16" height="16" :color="diyStyle_var['--color_main']"
					v-if="check_agreement">
				</svgGroup>
				<svgGroup type="uncheck2" width="16" height="16" :color="diyStyle_var['--color_main']" v-else>
				</svgGroup>
			</view>
			{{ $L('我已阅读并同意') }}
			<text class="agreement" @click="agreement('register_agreement')">{{ $L('《用户协议》') }}</text>{{ $L('和') }}
			<text class="agreement" @click="agreement('privacy_policy')">{{ $L('《隐私协议》') }}</text>
		</view>
		<!-- #endif -->
		<!-- wx-2-start -->
		<!-- #ifdef MP-WEIXIN -->
		<wxBindPhone ref="wxBindPhone"></wxBindPhone>
		<!-- #endif -->
		<!-- wx-2-end -->
		<!-- wx-6-start -->
		<!-- #ifdef MP -->
		<privacyPop ref="priPop"></privacyPop>
		<!-- #endif -->
		<!-- wx-6-end -->

		<uni-popup ref="controlPopup" type="bottom">
			<view class="control_popup">
				<view class="popup_top">
					<text>{{ $L('选择自提点') }}</text>
					<image :src="imgUrl + 'close_2.png'" mode="aspectFit" @click="cancelModal"></image>
				</view>
				<scroll-view class="uni-list control_list" scroll-y="true">
					<radio-group @change="radioChange" style="padding-top: 20rpx;">
						<label class="cancle_pre" v-for="(item, index) in controlList" :key="index">
							<text>{{ item.pointName }}</text>
							<radio :value="item.pointId.toString()" color="var(--color_main)"
								style="transform: scale(0.8); margin-right: 0;" />
						</label>
					</radio-group>
				</scroll-view>
			</view>
		</uni-popup>
		<uni-popup ref="tipsModal" type="dialog">
			<uni-popup-dialog type="input" :title="$L('提示')" :content="$L('暂无可用自提点')" :duration="2000"
				:cancelText="$L('关闭')" :hideConfirm="true" @close="tipsClose" :before-close="true"></uni-popup-dialog>
		</uni-popup>
	</view>
</template>

<script>
import wxBindPhone from '@/components/wxBindPhone.vue';
import {
fetchImgCode
} from '@/utils/common.js';
import {
mapMutations,
mapState
} from 'vuex';
//wx-7-start
// #ifdef MP
import privacyPop from '@/components/privacy-pop.vue';
// #endif
//wx-7-end
import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
// #ifdef H5 
import { getWxH5Appid } from '@/static/h5/wxH5Auth.js';
// #endif
export default {
	data() {
		return {
			showCodeImg: '',
			imgCode: '', //图形验证码
			imgCodeKey: '', //图形验证码的key
			imgUrl: process.env.VUE_APP_IMG_URL,
			mobile: '',
			password: '',
			logining: false,
			showPwd: false,
			curFocus: '', //当前光标所在的位置
			client: 1, //终端类型， 1、H5(微信内部浏览器) 2、H5(微信小程序)；3、app
			oriUrl: '', //不带code的页面地址
			isWeiXinH5: false, //是否为微信浏览器h5
			check_agreement: false,
			show_check_icon: process.env.VUE_APP_IMG_URL + 'register_uncheck.png',
			canIUseGetUserProfile: false, //是否可以使用getUserProfile获取用户信息，用于微信小程序
			isWxEnable: 0,
			isClick: true,
			wtCode: '',
			scanCode: false, //是否从app扫码授权登录跳转
			wxClient: 1,
			controlList: [],
			userInfo: {},
		}
	},
	components: {
		wxBindPhone,
		//wx-8-start
		// #ifdef MP
		privacyPop,
		// #endif
		//wx-8-end
		uniPopup,
		uniPopupDialog,
	},
	onLoad(option) {
		this.client = this.$getLoginClient()
		this.wxClient = this.$wxLoginClient()
		//wx-3-start
		//#ifdef MP-WEIXIN
		if (uni.getUserProfile) {
			this.canIUseGetUserProfile = true
		}
		//#endif
		//wx-3-end
		//#ifdef H5
		let code = this.$getQueryVariable('code')
		this.isWeiXinH5 = this.$isWeiXinBrower()
		if (code) {
			let oriUrl = this.$Route.query.ori_url
			let tmp_data = ''
			for (let i in this.$Route.query) {
				if (i != 'ori_url') {
					tmp_data += i + '=' + this.$Route.query[i] + '&'
				}
			}
			oriUrl += '?' + tmp_data
			this.oriUrl = oriUrl

			if (this.$isWeiXinBrower()) {
				//微信浏览器的话要把浏览器地址里面的code去掉
				history.replaceState({}, '', this.oriUrl)
			}

			let tar_params = {}
			tar_params.source = this.wxClient
			tar_params.code = code
			this.goLogin(tar_params)
		}
		//#endif

		//app-2-start




		//app-2-end

		this.getImgCode()
	},
	onShow() {
		let _this = this;
		//wx-9-start
		// #ifdef MP
		this.$refs.priPop?.configurePrivacy()
		// #endif
		//wx-9-end
	},
	computed: {
		...mapState(['userCenterData'])
	},
	methods: {
		...mapMutations(['login', 'setUserCenterData', 'loginRole', 'logout']),
		//注册协议点击事件
		checkAgrement() {
			this.check_agreement = !this.check_agreement
			this.show_check_icon = this.check_agreement ?
				process.env.VUE_APP_IMG_URL + 'register_checked.png' :
				process.env.VUE_APP_IMG_URL + 'register_uncheck.png'
		},
		getUser(e) {
			//wx-10-start
			// #ifdef MP
			if (!process.env.VUE_APP_ALLOW_PRIVACY) {
				this.$refs.priPop.open();
				return;
			}
			// #endif
			//wx-10-end
			if (e.detail.errMsg == 'getUserInfo:ok') {
				let userinfo = e.detail.userInfo
				this.getWxXcxCoce(userinfo)
			}
		},

		handleAcc() {
			let regEn = /[`~!@#$%^&*()+<>?:"{},.\/;'[\]]/gi,
				regCn = /[·！#￥（——）：；“”‘，|《。》？【】[\]]/gi
			if (regEn.test(this.mobile) || regCn.test(this.mobile)) {
				this.mobile = ''
				this.$api.msg(this.$L('名称不能包含特殊字符.'))
			}
		},

		//微信小程序根据用户信息获取code
		getWxXcxCoce(userinfo) {
			let {
				client
			} = this
			let _this = this
			uni.showLoading({
				title: _this.$L('正在请求...'),
				mask: true
			})
			uni.login({
				success: (code) => {
					let tar_params = {}
					tar_params.source = _this.wxClient
					tar_params.code = code.code
					tar_params.userInfo = JSON.stringify(userinfo)
					_this.goLogin(tar_params)
					this.wtCode = code.code
				}
			})
		},

		getUserProfile(e) {
			//wx-11-start
			// #ifdef MP
			if (!process.env.VUE_APP_ALLOW_PRIVACY) {
				this.$refs.priPop.open();
				return;
			}
			// #endif
			//wx-11-end
			let that = this
			if (!this.isClick) {
				return
			}
			this.isClick = false
			// 推荐使用wx.getUserProfile获取用户信息，开发者每次通过该接口获取用户个人信息均需用户确认
			// 开发者妥善保管用户快速填写的头像昵称，避免重复弹窗
			uni.getUserProfile({
				desc: that.$L('用于完善个人信息'), // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
				success: (res) => {
					if (res.errMsg == 'getUserProfile:ok') {
						let userinfo = res.userInfo
						this.getWxXcxCoce(userinfo)
					}
				},
				complete() {
					that.isClick = true
				}
			})
		},

		//跳转协议页面
		agreement(type) {
			this.$Router.push({
				path: '/pages/privacyPolicy/privacyPolicy',
				query: {
					type: type
				}
			})
		},
		//光标聚焦事件
		setFocus(e) {
			this.curFocus = e.currentTarget.dataset.key
		},

		//获取图形验证码
		async getImgCode() {
			const result = await fetchImgCode()
			this.showCodeImg = result.codeImg
			this.imgCodeKey = result.imgCodeKey
		},

		inputChange(e) {
			const key = e.currentTarget.dataset.key
			this[key] = e.detail.value
		},
		navBack() {
			this.$back()
			// this.$Router.pushTab('/pages/user/user')
		},
		toLogin() {
			if (!(this.mobile && this.password) || this.logining) {
				uni.showToast({
					title: this.$L('请输入账号密码'),
					icon: 'none',
					duration: 700
				})
				return
			}
			// #ifdef APP-PLUS||MP-WEIXIN
			if (!this.check_agreement) {
				this.$api.msg(this.$L('请同意用户隐私政策!'))
				return false
			}
			// #endif
			this.logining = true
			const {
				mobile,
				password,
				imgCode,
				imgCodeKey
			} = this

			let codeCheck = this.$checkImgCode(this.imgCode)
			if (codeCheck !== true) {
				this.imgCode = ''
				this.logining = false
				this.$api.msg(codeCheck)
				return false
			}

			//密码的验证 6～20位，英文、数字或符号
			if (!this.$checkPwd(password)) {
				this.logining = false
				return false
			}
			//app-3-start




















			//app-3-end
			let param = {}
			param.url = 'v3/frontLogin/oauth/token'
			param.data = {}
			param.data.username = mobile //登陆类型为1时：是用户名；为2时：是手机号
			param.data.password = this.$base64Encrypt(password)
			param.data.loginType = 1 //登陆类型：1-账号密码登陆，2-手机验证码登陆
			param.data.source = this.$getLoginClient()
			param.data.verifyCode = imgCode
			param.data.verifyKey = imgCodeKey
			//app-4-start



			//app-4-end
			//如果有缓存的购物车数据，登录需要把数据同步，并清除本地缓存
			if (this.$getUnLoginCartParam()) {
				param.data.cartInfo = this.$getUnLoginCartParam()
			}
			param.method = 'POST'
			this.$request(param).then((res) => {
				if (res.state == 200) {
					//更新登录时间
					uni.setStorage({
						key: 'sld_login_time',
						data: new Date().getTime()
					})

					uni.removeStorage({
						key: 'cart_list'
					}) //清除购物车数据
					res.data.loginTime = Date.parse(new Date()) //登录时间

					if (res.data.role == 'vendor') {
					} else {
						this.login(res.data)
						//获取个人中心的数据
						this.$request({
							url: 'v3/member/front/member/memberInfo'
						}).then((result) => {
							this.setUserCenterData(result.data)
							this.$loginGoPage()
						})
					}
				} else {
					//错误提示
					this.$api.msg(res.msg)
					this.getImgCode()
				}
				this.logining = false
			})
		},
		cancelModal() {
			this.logout();
			this.$refs.controlPopup.close();
		},
		tipsClose() {
			this.logout();
			this.$refs.tipsModal.close();
		},

		//清空输入的内容
		clearContent(type) {
			this[type] = ''
		},

		//是否显示密码切换事件
		changePwdState() {
			this.showPwd = !this.showPwd
		},

		//跳转事件 type:跳转类型，1为redirectTo 2为navigateTo
		navTo(url, type) {
			if (type == 1) {
				this.$Router.replace(url)
			} else if (type == 2) {
				this.$Router.push(url)
			}
		},
		//授权登录
		quickLogin() {
			//app-5-start
			//app-5-end
			if (this.scanCode) {
				uni.setStorageSync('fromurl', {
					url: '/pages/public/codeLogin'
				})
			}
			let {
				client
			} = this
			let _this = this
			//app-6-start
			//app-6-end
			//#ifdef H5
			getWxH5Appid()
			//#endif
		},

		//登录 data为除购物车信息外的接口参数，对象类型
		goLogin(data) {
			//app-7-start
			//app-7-end
			let _this = this
			let param = {}
			param.url = 'v3/member/front/login/wechat/login'
			param.data = {
				...data
			}
			//app-8-start



			//app-8-end
			//如果有缓存的购物车数据，登录需要把数据同步，并清除本地缓存
			if (this.$getUnLoginCartParam()) {
				param.data.cartInfo = this.$getUnLoginCartParam()
			}
			param.method = 'POST'
			this.$request(param).then((res) => {
				uni.hideLoading()
				if (res.state == 200) {
					//更新登录时间
					uni.setStorage({
						key: 'sld_login_time',
						data: new Date().getTime()
					})
					if (res.data.redirect == undefined) {
						uni.removeStorage({
							key: 'cart_list'
						}) //清除购物车数据
						res.data.loginTime = Date.parse(new Date()) //登录时间
						_this.login(res.data)
						//登录成功 获取个人中心的数据
						_this.$request({
							url: 'v3/member/front/member/memberInfo'
						}).then((result) => {
							_this.setUserCenterData(result.data)
							//#ifdef H5
							let isWeiXinH5 = this.$isWeiXinBrower()
							if (isWeiXinH5) {
								let fromurl = uni.getStorageSync('fromurl')
								if (fromurl.url) {
									setTimeout(() => {
										_this.$Router.replace({
											path: fromurl.url,
											query: fromurl.query
										})
									}, 1000)
								} else {
									_this.$loginGoPage()
								}
							} else {
								_this.$loginGoPage()
							}
							//#endif
							//#ifndef H5
							_this.$loginGoPage()
							//#endif
						})
					} else if (res.data.redirect != undefined) {
						//用户未注册，需要绑定手机号进行注册
						//#ifdef H5
						let isWeiXinH5 = this.$isWeiXinBrower()
						if (isWeiXinH5 && this.$getQueryVariable('code')) {
							//如果是微信浏览器，而且浏览器地址里有code，需要把code再跳转下一页，防止返回的时候再去验证code
							history.replaceState({}, '', location.href.split('?')[0])
						}
						//#endif

						// #ifndef MP-WEIXIN
						let query = {
							code: res.data.bindKey
						}
						//如果推手分享，则建立推手分享关系
						if (uni.getStorageSync('u')) {
							query.spreaderKey = uni.getStorageSync('u')
						}
						this.$Router.push({
							path: '/pages/public/bindMobile',
							query
						})
						// #endif
						//wx-4-start
						// #ifdef MP-WEIXIN
						this.$refs.wxBindPhone.openKey(res.data.bindKey)
						// #endif
						//wx-4-end
					}
				} else if (res.state == 267) {
					//错误提示
					// #ifndef MP-WEIXIN
					_this.$api.msg(_this.$L('请绑定手机号'))
					// #endif
					//wx-12-start
					// #ifdef MP-WEIXIN
					uni.showToast({
						title: _this.$L('请绑定手机号'),
						icon: 'none',
						duration: 2000
					})
					// #endif
					//wx-12-end
					setTimeout(() => {
						_this.$Router.push({
							path: '/pages/public/bindMobile',
							query: {
								u: res.data.u,
								type: 'u'
							}
						})
					}, 2000)
				} else {
					//错误提示
					// #ifndef MP-WEIXIN
					_this.$api.msg(res.msg)
					// #endif
					//wx-5-start
					// #ifdef MP-WEIXIN
					uni.showToast({
						title: res.msg,
						icon: 'none',
						duration: 2000
					})
					// #endif
					//wx-5-end
					// #ifdef H5
					if (this.$isWeiXinBrower()) {
						//微信浏览器的话要把浏览器地址里面的code去掉
						history.replaceState({}, '', window.origin + this.$Route.path)
					}
					// #endif
				}
			})
		},

		//微信登录互联开关
		getWxAuthority() {
			this.$request({
				url: 'v3/system/front/setting/getSettings',
				data: {
					names: 'login_wx_app_is_enable'
				}
			}).then((res) => {
				this.isWxEnable = res.data[0]
			})
		},
	}
}
</script>

<style lang="scss">
/* #ifdef H5 */
@media screen and (max-height:623px) {
	.wx_back_btn {
		display: none !important;
	}

	.wx_confirm_btn {
		margin: 60rpx auto !important;
	}
}

/* #endif */


page {
	background: #fff;
	width: 750rpx;
	margin: 0 auto;
}

.container {
	/* padding-top: 19.2vh; */
	position: relative;
	width: 750rpx;
	height: 100vh;
	overflow: hidden;
	background: #fff;
}

.placeStyle {
	color: #999999;



}

.wrapper {
	position: relative;
	z-index: 90;
	background: #fff;
	padding-bottom: 40upx;
}

.back-btn {
	margin-left: 40rpx;
	margin-top: 40rpx;
	/* #ifndef H5 */
	margin-top: 88rpx;
	/* #endif */
	font-size: 32rpx;
	color: $main-font-color;
	display: inline-block;
}

.login-title {
	position: relative;
	margin-top: 50rpx;
	margin-bottom: 70rpx;
	margin-left: 65rpx;
	font-size: 36rpx;
	color: #333;
	font-weight: bold;

	&:after {
		position: absolute;
		left: 0;
		bottom: -10rpx;
		content: '';
		width: 76rpx;
		height: 6rpx;
		background: linear-gradient(90deg, var(--color_main) 0%, rgba(255, 138, 0, 0) 100%);
	}
}

.input-content {
	padding: 0 65rpx;
}

.input-item {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	justify-content: center;
	height: 80rpx;
	margin-bottom: 50upx;
	border-bottom: 1px solid rgba(0, 0, 0, 0.1);
	position: relative;

	input {
		color: #2d2d2d;
	}

	.clear-account {
		position: absolute;
		right: 6rpx;
		top: 28rpx;
		font-size: 26rpx;
		color: #ddd;
	}

	image {
		width: 132rpx;
		height: 55rpx;
		box-shadow: 0px 5rpx 9rpx 1rpx rgba(102, 102, 102, 0.1);
		border-radius: 6rpx;
		margin-left: 20rpx;
	}

	.pwd-right {
		position: absolute;
		right: 6rpx;
		top: 14rpx;
		z-index: 999;

		.clear-pwd {
			font-size: 26rpx;
			color: #ddd;
		}

		.pwd-tab {
			font-size: 30rpx;
			color: #666;
			margin-left: 20rpx;
			margin-right: 28rpx;

			&.iconziyuan9 {
				font-size: 15rpx;
				transform: scale(0.1);
			}

			&.iconziyuan81 {
				font-size: 20rpx;
				transform: scale(0.1);
			}
		}

		.forget-pwd {
			color: #2d2d2d;
			font-size: 28rpx;
			line-height: 28rpx;
			font-weight: 400;
			border-left: 1px solid $border-color-split;
			padding-left: 28rpx;
		}
	}


	input {
		height: 60upx;
		font-size: $font-base + 2upx;
		color: $font-color-dark;
		flex: 1;
	}
}

.confirm-btn {
	width: 620rpx;
	height: 88rpx;
	line-height: 88rpx;
	margin: 90rpx auto;
	background: var(--color_main_bg);
	border-radius: 44rpx;
	color: #fff !important;
	font-size: 36rpx;
	border: none !important;
	outline: none;

	&::after {
		border: none !important;
	}
}

.sg-login {
	position: absolute;
	left: 0;
	top: 640rpx;
	width: 100%;
	display: flex;
	flex-direction: column;

	.title {
		display: flex;
		justify-content: center;
		align-items: center;

		&:before {
			content: ' ';
			width: 150rpx;
			height: 1rpx;
			background: #cbcbcb;
		}

		&:after {
			content: ' ';
			width: 150rpx;
			height: 1rpx;
			background: #cbcbcb;
		}

		text {
			color: #999999;
			font-size: 26rpx;
			margin: 0 20rpx;
		}
	}

	.login-method {
		display: flex;
		justify-content: center;
		margin-top: 20rpx;

		.wechat-login {
			display: flex;
			flex-direction: column;
			align-items: center;
			width: 100%;
			background: transparent;
			line-height: auto;
			height: auto;

			&::after {
				border: none;
			}

			image {
				width: 78rpx;
				height: 78rpx;
			}

			text {
				color: #666666;
				font-size: 26rpx;
			}
		}
	}
}


.other-login {
	position: absolute;
	left: 0;
	bottom: 140rpx;
	width: 100%;
	display: flex;
	flex-direction: column;

	.title {
		display: flex;
		justify-content: center;
		align-items: center;

		&:before {
			content: ' ';
			width: 150rpx;
			height: 1rpx;
			background: #cbcbcb;
		}

		&:after {
			content: ' ';
			width: 150rpx;
			height: 1rpx;
			background: #cbcbcb;
		}

		text {
			color: #999999;
			font-size: 26rpx;
			margin: 0 20rpx;
		}
	}

	.login-method {
		display: flex;
		justify-content: center;
		margin-top: 20rpx;

		.wechat-login {
			display: flex;
			flex-direction: column;
			align-items: center;
			width: 100%;
			background: transparent;
			line-height: auto;
			height: auto;

			&::after {
				border: none;
			}

			image {
				width: 78rpx;
				height: 78rpx;
			}

			text {
				color: #666666;
				font-size: 26rpx;
			}
		}
	}
}

.agreement-part {
	position: absolute;
	left: 0;
	bottom: 60rpx;
	width: 100%;
	font-size: 26rpx;
	color: #999999;
	text-align: center;

	.register_icon {
		width: 46rpx;
		height: 46rpx;
	}

	.agreement {
		color: var(--color_main);
		border-bottom: 1rpx solid var(--color_main);
	}
}

.login-register {
	display: flex;
	justify-content: center;
	margin-top: 33rpx;

	.mobile-login {
		color: #2d2d2d;
		font-size: 28rpx;
		line-height: 34rpx;
		border-right: 1px solid rgba(0, 0, 0, 0.1);
		padding-right: 30rpx;
		margin-right: 30rpx;
	}

	.register {
		color: var(--color_main);
		font-size: 28rpx;
		line-height: 34rpx;
	}
}

.control_popup {
	width: 100%;
	height: 700rpx;
	background: #ffffff;
	border-radius: 30rpx 30rpx 0rpx 0rpx;
	position: fixed;
	width: 100% !important;
	z-index: 20;
	bottom: 0;

	.popup_top {
		height: 96rpx;
		width: 100%;
		display: flex;
		padding: 0 30rpx 0 30rpx;
		align-items: center;
		border-bottom: 1rpx solid #f8f8f8;
		justify-content: space-between;

		text {
			font-size: 34rpx;
			color: #000000;
			font-family: PingFang SC;
			font-weight: bold;
			line-height: 32rpx;
		}

		image {
			width: 36rpx;
			height: 36rpx;
		}
	}

	.control_list {
		padding-bottom: calc(constant(safe-area-inset-bottom) + 10rpx);
		padding-bottom: calc(env(safe-area-inset-bottom) + 10rpx);
		box-sizing: border-box;
		height: 600rpx;

		.cancle_pre {
			width: 100%;
			padding: 29rpx 36rpx;
			padding-right: 18rpx;
			box-sizing: border-box;
			display: flex;
			justify-content: space-between;

			text {
				font-weight: bold;
				font-size: 32rpx;
				color: #000000;
				line-height: 32rpx;
			}
		}
	}

	.control_popup_btn {
		position: fixed;
		bottom: 34rpx;
		z-index: 30;
		display: flex;
		width: 100%;
		justify-content: center;

		.confrim_btn {
			width: 668rpx;
			height: 70rpx;
			background: var(--color_main_bg);
			border-radius: 44rpx;
			font-size: 36rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #ffffff;
			line-height: 70rpx;
			text-align: center;
		}
	}
}
</style>