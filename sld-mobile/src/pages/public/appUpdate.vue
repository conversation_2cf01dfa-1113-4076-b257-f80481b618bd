<template>
	<view class="page-height" v-if="popup_show && update_info" :style="mix_diyStyle">
		<view class="page-content">
			<view class="wrap">
				<view class="popup-bg">
					<view class="popup-content" :class="{ 'popup-content-show': popup_show }">
						<view class="update-wrap_box">
							<view class="update-wrap">
								<image :src="top_img" class="top-img" mode="widthFix"></image>
								<view class="content">
									<!-- <text class="title">
									发现新版本V{{update_info.upType == 1 ? +update_info.version : update_info.versionCode}}
								</text> -->
									<!-- 升级描述 -->
									<view class="title-sub" v-html="update_info.note"></view>
									<!-- 升级按钮 -->
									<button class="btn" v-if="downstatus < 1" @click="onUpdate()">
										{{ $L('立即更新') }}
									</button>
									<!-- 下载进度 -->
									<view class="sche-wrap" v-else>
										<!-- 更新包下载中 -->
										<view class="sche-bg">
											<view class="sche-bg-jindu" :style="{width: lengthWidth,backgroundImage: imgUrl + 'app_update/round.png'}"></view>
										</view>
										<text class="down-text">{{ $L('下载进度') }}:{{(downSize / 1024 / 1024).toFixed(2)}}M/{{ (fileSize / 1024 / 1024).toFixed(2) }}M</text>
									</view>
								</view>

							</view>
						</view>
						<image :src="close_img" class="close-ioc" @click="closeUpdate()"
							v-if="downstatus < 1 && update_info.force == 0"></image>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				imgUrl: process.env.VUE_APP_IMG_URL,
				top_img: process.env.VUE_APP_IMG_URL + 'app_update/renew_img.png',
				close_img: process.env.VUE_APP_IMG_URL + 'app_update/close.png',
				popup_show: true,
				update_info: null, //上一页面传过来的升级参数
				note: [], //升级说明数组格式
				fileSize: 0, //文件大小
				downSize: 0, //已下载大小
				downing: false, //是否下载中
				downstatus: 0, //0未下载  1已开始 2已连接到资源  3已接收到数据  4下载完成
			}
		},
		onLoad(option) {
			if (option.updata_info) {
				this.update_info = JSON.parse(option.updata_info)
				this.note = this.update_info.note.split("\n"); //版本说明
			} else {
				plus.nativeUI.toast(this.$L('参数出错'));
				setTimeout(() => {
					uni.navigateBack()
				}, 500)
			}
		},
		onBackPress(e) {
			if (e.from == "backbutton") return true; //APP安卓物理返回键逻辑
		},
		computed: {
			// 下载进度计算
			lengthWidth: function() {
				let w = this.downSize / this.fileSize * 100;
				if (!w) {
					w = 0
				} else {
					w = w.toFixed(2)
				}
				return w + "%"; //return 宽度半分比

			},
			getHeight: function() {
				let bottom = 0;
				if (this.tabbar) {
					bottom = 50;
				}
				return {
					"bottom": bottom + 'px',
					"height": "auto"
				}
			}
		},
		methods: {
			// 当点击更新时
			onUpdate() {
				//判断是否为WIFI网络 并且是非强制更新
				if (this.update_info.net_check == 1 && this.update_info.net_check == 0) {
					//判断是否为wifi模式
					uni.getNetworkType({
						success: (res) => {
							if (res.networkType == "wifi") {
								this.startUpdate(); //开始更新
							} else {
								uni.showModal({
									title: this.$L('提示'),
									content: this.$L('当前网络非WIFI,继续更新可能会产生流量,确认要更新吗？'),
									success: (modal_res) => {
										if (modal_res.confirm) {
											this.startUpdate(); //开始更新
										}
									}
								});
							}
						}
					});
				} else {
					this.startUpdate(); //开始更新
				}
			},
			//开始更新
			startUpdate() {
				if (this.downing) return false; //如果正在下载就停止操作
				this.downing = true; //状态改变 正在下载中
				if (/\.apk$/.test(this.update_info.now_url)) {
					// 如果是apk地址
					this.download_wgt('apk') // 安装包更新
				} else if (/\.wgt$/.test(this.update_info.now_url)) {
					// 如果是更新包
					this.download_wgt('wgt') // 升级包更新
				} else {
					plus.runtime.openURL(this.update_info.now_url, function() { //调用外部浏览器打开更新地址
						plus.nativeUI.toast(this.$L('打开错误'));
					});
				}
			},
			// 下载升级资源包
			download_wgt(type) {
				plus.nativeUI.showWaiting(this.$L('下载更新文件...')); //下载更新文件...
				let options = {
					method: "get"
				};
				let dtask = plus.downloader.createDownload(this.update_info.now_url, options);
				dtask.addEventListener("statechanged", (task, status) => {
					if (status === null) {} else if (status == 200) {
						//在这里打印会不停的执行，请注意，正式上线切记不要在这里打印东西!
						this.downstatus = task.state;
						switch (task.state) {
							case 3: // 已接收到数据
								plus.nativeUI.closeWaiting();
								this.downSize = task.downloadedSize;
								if (task.totalSize) {
									this.fileSize = task.totalSize; //服务器须返回正确的content-length才会有长度
								}
								break;
							case 4:
								this.installWgt(task.filename, type); // 安装
								break;
						}
					} else {
						plus.nativeUI.closeWaiting();
						plus.nativeUI.toast(this.$L('下载出错'));
						this.downing = false;
						this.downstatus = 0;
					}
				});
				dtask.start();
			},
			// 安装文件
			installWgt(path, type) {
				const that = this
				plus.nativeUI.showWaiting(this.$L('安装更新文件...')) //安装更新文件...
				// {force:true} 解决wgt包下载version错报不能安装
				plus.runtime.install(path, {
					force: true
				}, function(e) {
					if (type == 'wgt' && e.version && e.versionCode) {
						uni.setStorageSync('appVersion', {
							version: e.version,
							versionCode: e.versionCode
						});
					}
					plus.nativeUI.closeWaiting();
					// 应用资源下载完成！
					plus.nativeUI.alert(that.$L('更新完成,请重启APP！'), function() {
						plus.runtime.restart(); //重启APP
					});
				}, function(e) {
					plus.nativeUI.closeWaiting();
					// 安装更新文件失败
					plus.nativeUI.alert(that.$L('安装更新文件失败[') + e.code + "]：" + e.message);
				});
			},
			// 取消更新
			closeUpdate() {
				uni.setStorageSync("update_ignore", this.update_info.version);
				uni.navigateBack()
			},
		}
	}
</script>

<style lang="scss" scoped>
	.page-height {
		height: 100vh;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		background-color: rgba($color: #000000, $alpha: 0.7);
	}

	.popup-bg {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		width: 750rpx;
	}

	.popup-content {
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.popup-content-show {
		animation: mymove 300ms;
		transform: scale(1);
	}

	@keyframes mymove {
		0% {
			transform: scale(0);
			/*开始为原始大小*/
		}

		100% {
			transform: scale(1);
		}
	}

	.update-wrap_box {
		width: 546rpx;
		border-radius: 18rpx;
		background: #fff;
	}

	.update-wrap {
		width: 546rpx;
		border-radius: 18rpx;
		position: relative;
		display: flex;
		flex-direction: column;
		background: var(--color_main_bg);

		.top-img {
			width: 546rpx;
			height: 442rpx;
		}

		.content {
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 0 30rpx 40rpx;
			background: #fff;

			.title {
				font-size: 32rpx;
				font-weight: bold;
				color: FE3B31;
			}

			.title-sub {
				color: #666666;
				font-size: 24rpx;
				text-align: left;
				padding: 30rpx 0;
				max-height: 40vh;
				overflow-y: auto;
			}

			.btn {
				width: 400rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				color: #ffffff;
				font-size: 30rpx;
				height: 80rpx;
				line-height: 80rpx;
				border-radius: 100px;
				background: var(--color_main_chat_bg);
				box-shadow: 0 0 10rpx 0 var(--color_main_chat_bg);
				margin-top: 20rpx;
			}
		}
	}

	.close-ioc {
		width: 44rpx;
		height: 44rpx;
		margin-top: 40rpx;
	}

	.sche-wrap {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: flex-end;
		padding: 10rpx 50rpx 0;

		.sche-wrap-text {
			font-size: 24rpx;
			color: #666;
			margin-bottom: 20rpx;
		}

		.sche-bg {
			position: relative;
			background-color: #cccccc;
			height: 20rpx;
			border-radius: 100px;
			width: 480rpx;
			display: flex;
			align-items: center;

			.sche-bg-jindu {
				position: absolute;
				left: 0;
				top: 0;
				height: 20rpx;
				min-width: 40rpx;
				border-radius: 100px;
				background: var(--color_main);
				background-position: center right;
				background-repeat: no-repeat;
				background-size: 26rpx 26rpx;
			}
		}

		.down-text {
			font-size: 24rpx;
			color: var(--color_main);
			margin-top: 16rpx;
		}
	}
</style>