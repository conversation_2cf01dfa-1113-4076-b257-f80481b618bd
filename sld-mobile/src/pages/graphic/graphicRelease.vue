<template>
	<view class="main_graRe">
		<!-- #ifdef H5 -->
		<topHeader />
		<!-- #endif -->
		<view class="content">
			<!-- 商品选择 -->
			<view class="goods-section" v-if="setting && setting.member_bind_goods == '1'">
				<text class="section-title">商品</text>
				<view class="goods-wrap">
					<view class="goods-item" v-for="(item, index) in selGoods" :key="index">
						<view class="goods-content">
							<image class="goods-image" :src="item.goodsImage" mode="aspectFill"></image>
							<view class="goods-info">
								<text class="goods-name">{{ item.goodsName }}</text>
								<view class="goods-price">
									<text class="unit">¥</text>
									<text class="price">{{ item.goodsPrice || item.productPrice }}</text>
								</view>
							</view>
							<view class="goods-delete" @tap.stop="delGoods(index)">×</view>
						</view>
					</view>
					<view class="goods-item add-item" @tap="seleGoods"
						v-if="selGoods.length < (setting.bind_goods_num || 4)">
						<view class="goods-content add-content">
							<image class="goods-icon" :src="imgUrl + 'product.png'"></image>
							<text class="add-text">添加商品</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 发布类型选择 -->
			<view class="type-section">
				<text class="section-title">发布类型</text>
				<view class="type-list">
					<view class="type-item" v-for="(item, index) in publishTypes" :key="index"
						:class="{ 'type-selected': currentType === item.type }" @click="selectType(item)">
						{{ item.name }}
					</view>
				</view>
			</view>

			<!-- 上传区域 -->
			<view class="upload-section">
				<!-- 图文上传 -->
				<template v-if="currentType === 'graphic'">
					<view class="pic_part_sel">
						<template v-if="imageList.length === 0">
							<view class="upload-placeholder large" @tap="uploadCover">
								<view class="upload-content">
									<image class="camera-icon" :src="imgUrl + 'svideo/cover_icon.png'"></image>
									<text class="upload-text">上传图片最多9张</text>
								</view>
							</view>
						</template>
						<view class="image-grid" v-else>
							<view class="cover_image" v-for="(item, index) in imageList" :key="index">
								<image :src="item.url" class="image" mode="aspectFit"></image>
								<view class="close_img" @click="delUploadImg(index)">×</view>
							</view>
							<view class="upload-placeholder small" @tap="uploadCover" v-if="imageList.length < 9">
								<view class="upload-content">
									<image class="camera-icon" :src="imgUrl + 'svideo/cover_icon.png'"></image>
									<text class="upload-text">继续上传</text>
								</view>
							</view>
						</view>
					</view>
				</template>

				<!-- 视频上传 -->
				<template v-else>
					<view class="video-upload">
						<view class="video-main">
							<view v-if="!upload_video.video_url" class="upload-placeholder" @tap="uploadVideo">
								<view class="upload-content">
									<image class="camera-icon" :src="imgUrl + 'svideo/upload_video_icon.png'"></image>
									<text class="upload-text">上传短视频(必填)</text>
								</view>
							</view>
							<view v-else class="video-preview">
								<video :src="upload_video.video_url" id="myVideo">
									<cover-view class="preview-overlay" @click="previewVideoSrcFlag = true"></cover-view>
									<cover-view class="delete-btn" @click="delVideo">×</cover-view>
								</video>
							</view>
						</view>

						<view class="video-cover">
							<view v-if="!cover.video_url" class="upload-placeholder" @tap="uploadVideoCover">
								<view class="upload-content">
									<image class="camera-icon" :src="imgUrl + 'svideo/cover_icon.png'"></image>
									<text class="upload-text">选封面(必填)</text>
								</view>
							</view>
							<view v-else class="cover-preview">
								<image @click="previewImage" class="cover-img" :src="cover.video_url" mode="aspectFit">
								</image>
								<image class="delete-btn" @click="delCover" :src="imgUrl + 'svideo/del_cover.png'"></image>
							</view>
						</view>
					</view>
				</template>
			</view>

			<!-- 标题输入区域 - 仅图文类型显示 -->
			<view class="input-section" v-if="currentType === 'graphic'">
				<text class="section-title">标题</text>
				<input class="input" :placeholder="$L('填写好的标题会获得更多的赞哦~')" placeholder-style="color: #999999" maxlength="20"
					v-model="title" />
			</view>
			
			<view class="input-section" v-if="currentType === 'graphic'">
				<text class="section-title">简介</text>
				<input class="input" :placeholder="$L('一句话简介')" placeholder-style="color: #999999" maxlength="20"
					v-model="introduction" />
			</view>

			<!-- 内容详情输入区域 -->
			<view class="input-section summary-section" v-if="currentType === 'graphic'">
				<text class="section-title">内容详情</text>
				<textarea class="textarea" :placeholder="$L('请输入内容详情，最多500字')"
					placeholder-style="color: #999999; margin-top: 20rpx;" maxlength="500" v-model="content">
</textarea>
			</view>

			<!-- 视频简介输入区域 -->
			<view class="input-section summary-section" v-if="currentType === 'video'">
				<text class="section-title">视频简介</text>
				<textarea class="textarea" :placeholder="$L('请输入简介，最多30字(必填)')"
					placeholder-style="color: #999999; margin-top: 20rpx;" maxlength="30" v-model="video_desc">
</textarea>
			</view>

			<!-- 标签选择 -->
			<view class="tag-section">
				<text class="section-title">标签</text>
				<view class="tag-list">
					<view class="tag-item" v-for="(item, index) in label_list" :key="index"
						:class="{ 'tag-selected': selectLabel.labelId === item.labelId }" @click="selectTag(item)">
						{{ item.labelName }}
					</view>
				</view>
			</view>

			<!-- 底部发布按钮 -->
			<view class="publish-button" @click="startGraphic">
				<text>发布</text>
			</view>
		</view>

		<!-- 视频预览弹窗 -->
		<view class="preview-full" v-if="previewVideoSrcFlag">
			<video :autoplay="true" :src="upload_video.video_url" :show-fullscreen-btn="false">
				<cover-view class="preview-full-close" @click="previewVideoSrcFlag = false">×</cover-view>
			</video>
		</view>
		<loginPop ref="loginPop"></loginPop>
		<w-compress ref='wCompress' />


	</view>
</template>

<script>
// #ifdef H5
import topHeader from '@/components/graphic/topheader.vue';
import EXIF from '@/utils/exif.js';
// #endif
import loginPop from '@/components/loginPop/loginPop.vue';
import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import videoReleaseGoods from "@/components/video/videoReleaseGoods.vue";
import {
mapState,
} from 'vuex';

export default {
	// #ifdef H5
	components: {
		topHeader,
		videoReleaseGoods,
		uniPopup,
		uniPopupDialog,
		loginPop
	},
	// #endif
	// #ifndef H5
	components: {
		videoReleaseGoods,
		uniPopup,
		uniPopupDialog,
		loginPop
	},
	// #endif
	data() {
		return {
			imgUrl: process.env.VUE_APP_IMG_URL,
			video_id: '',
			title: '',
			imageList: [],
			imageSize: [], //图片对应的宽高
			content: '', // 内容详情
			introduction: '',
			setting: {},
			label_list: [],
			roleType: 1, // 默认会员 1   2：商家
			selGoods: [], //已选中的商品列表
			selectLabel: {},
			closeImg: process.env.VUE_APP_IMG_URL + 'order-detail/guanbi.png',
			onOff: true,
			graTitle: '发布',
			index: null, //当前操作的图文下标
			topFixed: false,
			firstOpen: true, //是否第一次加载页面
			storeId: null,
			token: '',
			isWeiXinBrower: false,
			publishTypes: [
				{ type: 'graphic', name: '图文' },
				// { type: 'video', name: '视频' }
			],
			currentType: 'graphic',
			upload_video: {},
			video_desc: '',
			previewVideoSrcFlag: false,
			cover: {},
			tempOptions: null,
			isTemporaryOperation: false,
		}
	},

	/**
	 * 生命周期函数--监听页面加载
	 */
	onLoad: function (options) {
		console.log('graphicRelease onLoad options:', options);
		
		// 声明变量
		let currentGoodsId, currentProductId;
		
		// #ifdef H5
		// H5端，从路由参数获取商品信息
		currentGoodsId = options.goodsId;
		currentProductId = options.productId;
		// #endif
		
		// #ifdef MP-WEIXIN
		// 小程序端，从本地存储获取商品信息
		currentGoodsId = options.goodsId || uni.getStorageSync('temp_publish_goodsId');
		currentProductId = options.productId || uni.getStorageSync('temp_publish_productId');
		// #endif
		
		console.log('onLoad - 当前商品ID:', currentGoodsId);
		console.log('onLoad - 当前产品ID:', currentProductId);
		
		if (options.video_id) {
			this.graTitle = this.$L('编辑')
			this.video_id = options.video_id;
			this.getVideoDetail();
		} else {
			// 检查是否有保存的内容
			const savedContent = uni.getStorageSync('graphicReleaseContent');
			if (savedContent) {
				// 判断新的goodsId和缓存中的goodsId是否一致
				if (!currentGoodsId || (currentGoodsId && savedContent.goodsId == currentGoodsId)) {
					console.log('onLoad - 商品ID一致或无新商品ID，恢复缓存内容');
					this.restoreContentFromStorage();
				} else {
					// 如果goodsId不一致，清除缓存
					console.log('onLoad - 新旧商品ID不一致，清除缓存');
					this.clearContent();
					uni.removeStorageSync('graphicReleaseContent');
					
					// 如果有新的商品ID和产品ID，获取商品详情
					if (currentGoodsId && currentProductId) {
						console.log('onLoad - 获取新商品详情');
						this.getGoodsDetail(currentGoodsId, currentProductId);
					}
				}
			} else if (currentGoodsId && currentProductId) {
				// 如果没有缓存内容但有商品ID和产品ID，直接获取商品详情
				console.log('onLoad - 无缓存内容，获取新商品详情');
				this.getGoodsDetail(currentGoodsId, currentProductId);
			}
		}
		
		// 保存参数，等待标签列表加载完成后再设置
		this.tempOptions = options;
		
		this.roleType = options.roleType ? options.roleType : 1;
		this.storeId = options.storeId;
		this.index = options.index;
		
		// 初始化数据
		this.initData();
		
		// #ifdef H5
		this.isWeiXinBrower = this.$isWeiXinBrower()
		setTimeout(() => {
			uni.setNavigationBarTitle({
				title: this.graTitle
			})
		}, 0);
		// #endif
	},
	onShow: function () {
		console.log('graphicRelease onShow');
		
		// #ifdef H5
		// H5端，从路由参数更新tempOptions
		if (this.$Route && this.$Route.query) {
			console.log('H5端更新tempOptions:', this.$Route.query);
			this.tempOptions = this.$Route.query;
		}
		// #endif
		
		// 获取当前的商品ID和产品ID（从本地存储）
		const currentGoodsId = uni.getStorageSync('temp_publish_goodsId');
		const currentProductId = uni.getStorageSync('temp_publish_productId');
		console.log('onShow 当前商品ID:', currentGoodsId);
		console.log('onShow 当前产品ID:', currentProductId);
		
		if (!this.firstOpen) {
			// 检查是否有保存的内容
			const savedContent = uni.getStorageSync('graphicReleaseContent');
			console.log('onShow 缓存内容:', savedContent);
			
			if (savedContent) {
				// 判断新的goodsId和缓存中的goodsId是否一致
				if (!currentGoodsId || (currentGoodsId && savedContent.goodsId == currentGoodsId)) {
					console.log('onShow 商品ID一致或无新商品ID，恢复缓存内容');
					this.restoreContentFromStorage();
				} else {
					// 如果goodsId不一致，清除缓存
					console.log('onShow 新旧商品ID不一致，清除缓存');
					this.clearContent();
					uni.removeStorageSync('graphicReleaseContent');
					
					// 如果有新的商品ID和产品ID，获取商品详情
					if (currentGoodsId && currentProductId) {
						console.log('onShow 获取新商品详情');
						this.getGoodsDetail(currentGoodsId, currentProductId);
					} else {
						console.log('onShow 缺少商品ID或产品ID，无法获取商品详情');
					}
				}
				return;
			}
			
			// 如果没有缓存内容但有新的商品ID和产品ID，获取商品详情
			if (currentGoodsId && currentProductId) {
				console.log('onShow 无缓存内容，获取新商品详情');
				this.getGoodsDetail(currentGoodsId, currentProductId);
			}
			
			// 重新初始化数据
			this.initData();
		} else {
			this.firstOpen = false;
		}
		
		// 在onShow结束时调用handleGoodsAndLabel
		this.$nextTick(() => {
			this.handleGoodsAndLabel();
		});
	},

	onPageScroll(e) {
		if (e.scrollTop > 20) {
			this.topFixed = true
		} else {
			this.topFixed = false
		}
	},

	onBackPress() {
		return this.handlePageExit();
	},
	computed: {
		...mapState(['userInfo', 'hasLogin']),
		enableRelease() {
			if (this.currentType === 'graphic') {
				return this.title != '' &&
					this.imageList.length > 0 &&
					this.content != '' &&
					this.selectLabel.labelId;
			} else {
				return this.upload_video.video_url &&
					this.cover.video_url &&
					this.video_desc != '' &&
					this.selectLabel.labelId;
			}
		}
	},
	methods: {
		// 检查登录状态
		checkLogin() {
			if (!this.hasLogin) {
				this.$refs.loginPop.openLogin()
				return false
			}
			return true
		},

		initData() {
			// 获取商品ID和产品ID
			const goodsId = uni.getStorageSync('temp_publish_goodsId');
			const productId = uni.getStorageSync('temp_publish_productId');
			console.log('initData - 从本地存储获取的 goodsId:', goodsId);
			console.log('initData - 从本地存储获取的 productId:', productId);
			
			// 如果有商品ID和产品ID，获取商品详情
			if (goodsId && productId) {
				console.log('initData - 准备获取商品详情');
				this.getGoodsDetail(goodsId, productId);
			}
			
			this.getLableList();
			this.getSetting();
		},

		//获取短视频详情
		getVideoDetail() {
			let {
				video_id
			} = this;

			let param = {};
			param.url = 'v3/video/front/video/detail';
			param.method = 'GET';
			param.data = {};
			param.data.videoId = video_id;
			this.$request(param).then(res => {
				if (res.state == 200) {
					let result = res.data;
					this.title = result.videoName;
					this.introduction = result.introduction;
					this.content = result.content;
					this.selectLabel.labelId = result.labelId;
					this.selectLabel.labelName = result.labelName;
					result.videoImageUrl.forEach((item, index) => {
						this.imageList.push({
							url: item,
							path: result.videoImage.split(',')[index]
						})
					});
					if (res.data.imageInfo) {
						this.imageSize = JSON.parse(res.data.imageInfo);
					}
					this.selGoods = result.goodsList;
					uni.setStorageSync('selGoods', this.selGoods)
				}
			})
		},

		// 获取标签列表
		getLableList() {
			let param = {};
			param.url = 'v3/video/front/video/labelList';
			param.method = 'GET';
			this.$request(param).then(res => {
				if (res.state == 200) {
					this.label_list = res.data.list;
					
					// 标签列表加载完成后，处理商品和标签
					this.handleGoodsAndLabel();
				}
			})
		},
		
		// 处理商品和标签的设置
		handleGoodsAndLabel() {
			if (!this.tempOptions) return;
			
			// #ifdef H5
			// H5端，直接从tempOptions获取商品信息
			if (this.tempOptions.goodsId && this.tempOptions.productId) {
				console.log('H5端处理商品信息 - goodsId:', this.tempOptions.goodsId, 'productId:', this.tempOptions.productId);
				this.getGoodsDetail(this.tempOptions.goodsId, this.tempOptions.productId);
				
				// 设置标签
				if (this.tempOptions.labelId && this.label_list.length > 0) {
					const targetLabel = this.label_list.find(item => item.labelId == this.tempOptions.labelId);
					if (targetLabel) {
						this.selectLabel = targetLabel;
					}
				}
			}
			// #endif
			
			// #ifdef MP-WEIXIN
			// 小程序端，从本地存储获取商品信息
			const tempGoodsId = uni.getStorageSync('temp_publish_goodsId');
			const tempProductId = uni.getStorageSync('temp_publish_productId');
			console.log('小程序端处理商品信息 - tempGoodsId:', tempGoodsId, 'tempProductId:', tempProductId);
			
			if (tempGoodsId && tempProductId) {
				console.log('从小程序商品详情页跳转，准备获取商品详情');
				this.getGoodsDetail(tempGoodsId, tempProductId);
				// 设置标签为"种草"
				const labelId = 2;
				const targetLabel = this.label_list.find(item => item.labelId == labelId);
				if (targetLabel) {
					this.selectLabel = targetLabel;
				}
				// 使用完后清除
				uni.removeStorageSync('temp_publish_goodsId');
				uni.removeStorageSync('temp_publish_productId');
			}
			// #endif
		},

		// 获取设置信息
		getSetting() {
			let param = {};
			param.url = 'v3/video/front/video/setting/getSettingList';
			param.method = 'GET';
			param.data = {};
			param.data.str = 'bind_goods_num,member_bind_goods';
			this.$request(param).then(res => {
				if (res.state == 200) {
					let result = res.data;
					result && result.map(settingItem => {
						if (settingItem.name == 'bind_goods_num') { //绑定商品数
							this.setting.bind_goods_num = settingItem.value
						} else if (settingItem.name == 'member_bind_goods') { //会员是否定商品
							this.setting.member_bind_goods = settingItem.value
							this.$forceUpdate()
						}
					})
				}
			})
		},

		//选择标签
		seleLabel(e) {
			if (!this.checkLogin()) return
			this.selectLabel = this.label_list[e.detail.value];
		},

		// 选封面
		uploadCover() {
			if (!this.checkLogin()) return
			let {
				cover
			} = this;
			let that = this;

			// 标记为临时操作
			this.isTemporaryOperation = true;

			uni.chooseImage({
				count: 9 - this.imageList.length,
				sizeType: ['original', 'compressed'],
				sourceType: ['album', 'camera'],
				success: async res => {
					uni.showLoading({
						title: '图片上传中'
					});
					for (let index = 0; index < res.tempFilePaths.length; index++) {
						if (index + this.imageList.length == 9) {
							uni.hideLoading()
							uni.showToast({
								title: that.$L('最多上传9张图片！'),
								icon: 'none',
							})
							break;
						}

						uni.uploadFile({
							url: process.env.VUE_APP_API_URL + 'v3/oss/front/upload',
							filePath: res.tempFilePaths[index],
							name: 'file',
							formData: {
								'source': 'goods',
							},
							header: {
								Authorization: 'Bearer ' + that.userInfo.member_access_token
							},
							success: (uploadFileRes) => {
								let result = JSON.parse(uploadFileRes.data);
								if (result.state == 200) {
									this.imageList.push({
										url: result.data.url,
										path: result.data.path
									});
									this.imageSize.push({
										width: result.data.width,
										height: result.data.height
									});
									// #ifdef H5
									/** setSize方法 处理部分机型像素过高时图片旋转导致的宽高值不正确问题 */
									this.setSize(this.imageList.length - 1, res.tempFilePaths[
										index]);
									// #endif
								}
							},
							complete: () => {
								uni.hideLoading();
							}
						});
					}
				}
			});
		},

		// #ifdef H5
		async setSize(index, url) {
			let Orientation = 0; // 0: 正常无旋转 1: 横屏拍摄 6:竖屏拍摄照片旋转
			//获取图片META信息
			await this.getImageTag(url, 'Orientation', function (e) {
				if (e != undefined) Orientation = e;
			})
			if (Orientation > 1) { //竖屏拍照时照片转的话则图片的宽高值互换
				let w = this.imageSize[index].width;
				let h = this.imageSize[index].height;
				this.imageSize[index].width = h;
				this.imageSize[index].height = w;
			}
		},
		getImageTag(file, tag, suc) {
			if (!file) return 0;
			return new Promise((resolve, reject) => {
				/* eslint-disable func-names */
				// 箭头函数会修改this，所以这里不能用箭头函数
				let imgObj = new Image();
				imgObj.src = file
				uni.getImageInfo({
					src: file,
					success(res) {
						EXIF.getData(imgObj, function () {
							EXIF.getAllTags(this);
							let or = EXIF.getTag(this, 'Orientation');
							resolve(suc(or))
						});
					}
				})
			});
		},
		// #endif

		// 删除评价图片
		delUploadImg(index, product) {
			if (!this.checkLogin()) return
			this.imageList.splice(index, 1);
			this.imageSize.splice(index, 1);
		},

		//选择商品
		seleGoods() {
			if (!this.checkLogin()) return

			// 标记为临时操作
			this.isTemporaryOperation = true;

			this.$Router.push({
				path: '/extra/svideo/svideoSeleGoods',
				query: {
					bindGoodsNum: this.setting.bind_goods_num,
					selGoods: JSON.stringify(this.selGoods),
				},
			})
		},

		//发布
		startGraphic() {
			if (!this.checkLogin()) return
			if (this.currentType === 'graphic') {
				// 图文发布验证
				if (this.title.trim() == '') {
					uni.showToast({
						title: this.$L('请输入标题'),
						icon: 'none'
					});
					return;
				}

				if (this.content.trim() == '') {
					uni.showToast({
						title: this.$L('请输入内容详情'),
						icon: 'none'
					});
					return;
				}

				if (this.imageList.length == 0) {
					uni.showToast({
						title: this.$L('请上传图片'),
						icon: 'none'
					});
					return;
				}

				if (!this.selectLabel.labelId) {
					uni.showToast({
						title: this.$L('请选择标签'),
						icon: 'none'
					});
					return;
				}
			} else {
				// 视频发布逻辑
				if (JSON.stringify(this.upload_video) == '{}') {
					uni.showToast({
						title: this.$L('请上传短视频'),
						icon: 'none'
					});
					return;
				}

				if (JSON.stringify(this.cover) == '{}') {
					uni.showToast({
						title: this.$L('请上传封面图片'),
						icon: 'none'
					});
					return;
				}

				if (this.video_desc == '') {
					uni.showToast({
						title: this.$L('请输入视频简介'),
						icon: 'none'
					});
					return;
				}
			}

			// 构建请求参数
			let param = {
				method: 'POST',
				data: {}
			};

			let selGoodsIds = this.selGoods.map(item => {
				return item.goodsId;
			}).filter(id => id);

			if (this.currentType === 'graphic') {
				// 图文发布参数
				param.url = this.video_id ? 'v3/video/front/video/editVideo' : 'v3/video/front/video/releaseVideo';
				param.data = {
					introduction: this.introduction,
					labelId: this.selectLabel.labelId,
					videoName: this.title,
					videoContent: this.content,
					content: this.content,
					videoImage: this.imageList.map(i => i.path).join(','),
					goodsIds: selGoodsIds.join(','),
					type: 2,
					width: this.imageSize[0].width,
					height: this.imageSize[0].height,
					imageInfo: JSON.stringify(this.imageSize)
				};

				// 打印参数用于调试
				console.log('发布参数:', param.data);
			} else {
				// 视频发布参数
				param.url = this.video_id ? 'v3/video/front/video/editVideo' : 'v3/video/front/video/releaseVideo';
				param.data = {
					introduction: this.video_desc,
					labelId: this.selectLabel.labelId,
					videoPath: this.upload_video.video_path,
					goodsIds: selGoodsIds.join(','),
					type: 1,
					videoThemeImage: this.cover.video_path,
					width: this.cover.width,
					height: this.cover.height
				};
			}

			if (this.video_id) {
				param.data.videoId = this.video_id;
			}

			// 发送请求
			console.log('准备发送请求，参数：', JSON.stringify(param));
			
			this.$request(param).then(res => {
				console.log('收到响应：', JSON.stringify(res));
				if (res.state == 200) {
					// 发布成功后清除内容和storage
					this.clearContent();
					uni.removeStorageSync('graphicReleaseContent');

					uni.showToast({
						title: this.video_id ? this.$L('编辑成功！') : this.$L('发布成功！'),
						icon: 'none',
						duration: 1000
					});
					
					setTimeout(() => {
						// 获取商品ID字符串
						const goodsIds = selGoodsIds.join(',');
						
						// 使用条件编译处理不同平台的路由跳转
						// #ifdef H5
						this.$Router.push({
							path: '/pages/index/information',
							query: {
								labelId: 2,  // 跳转到种草标签
								goodsIds: goodsIds  // 传递商品ID
							}
						});
						// #endif
						// #ifdef MP-WEIXIN
						// 先将目标标签ID和商品ID存储到本地
						uni.setStorageSync('temp_target_labelId', 2);
						uni.setStorageSync('temp_target_goodsIds', goodsIds);
						uni.switchTab({
							url: '/pages/index/information'
						});
						// #endif
					}, 1000);
				} else {
					this.$api.msg(res.msg);
				}
			}).catch(err => {
				console.error('请求错误：', err);
				this.$api.msg(err.msg || '发布失败');
			});
		},

		//删除商品
		delGoods(index) {
			if (index >= 0 && index < this.selGoods.length) {
				this.selGoods.splice(index, 1);
				uni.setStorageSync('selGoods', this.selGoods);
			}
		},

		popCon(type) {
			let obj = [
				'title',
				"imageList",
				"content",
				"selGoods",
				"selectLabel",
			]

			let res = obj.some(item => Boolean(this[item].length && JSON.stringify(this[item]) != '{}' && this[item]))

			switch (type) {
				case 'open': {
					if (res) {
						this.$refs.popBack.open()
					} else {
						this.$Router.back(1)
					}

					break
				}
				case 'confirm': {
					this.$Router.back(1)
					break
				}
			}
		},
		selectTag(item) {
			if (!this.checkLogin()) return
			this.selectLabel = item;
		},

		// 选择发布类型
		selectType(item) {
			if (!this.checkLogin()) return
			this.currentType = item.type;
		},

		// 上传视频
		uploadVideo() {
			if (!this.checkLogin()) return
			if (JSON.stringify(this.upload_video) != '{}') return;

			// 标记为临时操作
			this.isTemporaryOperation = true;

			uni.chooseVideo({
				sourceType: ['album'],
				compressed: true,
				maxDuration: 60,
				camera: 'back',
				
				success: res => {
					if (res.size > 20971520) {
						uni.showToast({
							title: this.$L('超出文件最大限制20m，请重新上传！'),
							icon: 'none',
							duration: 1500
						});
						return;
					}

					uni.showLoading({
						title: '上传中',
						mask: true
					});

					uni.uploadFile({
						url: process.env.VUE_APP_API_URL + 'v3/oss/front/upload',
						filePath: res.tempFilePath,
						name: 'file',
						formData: { source: 'video' },
						header: {
							Authorization: 'Bearer ' + this.userInfo.member_access_token
						},
						success: (uploadFileRes) => {
							uploadFileRes = JSON.parse(uploadFileRes.data);
							if (uploadFileRes.state == 200) {
								this.upload_video = {
									video_url: uploadFileRes.data.url,
									video_path: uploadFileRes.data.path
								};
								uni.showToast({ title: this.$L('上传成功') });
							}
						},
						complete: () => {
							uni.hideLoading();
						}
					});
				}
			});
		},

		// 上传视频封面
		uploadVideoCover() {
			if (!this.checkLogin()) return

			// 标记为临时操作
			this.isTemporaryOperation = true;

			uni.chooseImage({
				count: 1,
				sizeType: ['original', 'compressed'],
				sourceType: ['album', 'camera'],
				success: async res => {
					let imageUrl = await this.compressImage(res.tempFilePaths[0]);

					uni.uploadFile({
						url: process.env.VUE_APP_API_URL + 'v3/oss/front/upload',
						filePath: imageUrl.tempFilePath || imageUrl,
						name: 'file',
						formData: { source: 'goods' },
						header: {
							Authorization: 'Bearer ' + this.userInfo.member_access_token
						},
						success: resup => {
							resup = JSON.parse(resup.data);
							if (resup.state == 200) {
								this.cover = {
									video_url: resup.data.url,
									video_path: resup.data.path,
									height: resup.data.height,
									width: resup.data.width
								};
							}
						}
					});
				}
			});
		},

		// 删除视频
		delVideo() {
			if (!this.checkLogin()) return
			this.upload_video = {};
		},

		// 删除视频封面
		delCover() {
			if (!this.checkLogin()) return
			this.cover = {};
		},

		// 清除所有内容
		clearContent() {
			this.title = '';
			this.content = '';
			this.imageList = [];
			this.imageSize = [];
			this.selectLabel = {};
			this.selGoods = [];
			this.upload_video = {};
			this.video_desc = '';
			this.cover = {};
			this.isTemporaryOperation = false;
			// 清除所有相关的storage
			uni.removeStorageSync('selGoods');
			uni.removeStorageSync('graphicReleaseContent');
			uni.removeStorageSync('temp_publish_goodsId');
			uni.removeStorageSync('temp_publish_productId');
		},

		// 保存内容到storage
		saveContentToStorage() {
			const contentData = {
				title: this.title,
				content: this.content,
				imageList: this.imageList,
				imageSize: this.imageSize,
				selectLabel: this.selectLabel,
				selGoods: this.selGoods,
				tempOptions: this.tempOptions,  // 保存商品和标签相关的参数
				productId: this.$Route?.query?.productId || uni.getStorageSync('temp_publish_productId'),  // 保存 productId
				goodsId: this.$Route?.query?.goodsId || uni.getStorageSync('temp_publish_goodsId')  // 保存 goodsId
			};
			uni.setStorageSync('graphicReleaseContent', contentData);
		},

		// 从storage恢复内容
		restoreContentFromStorage() {
			const contentData = uni.getStorageSync('graphicReleaseContent');
			if (contentData) {
				this.title = contentData.title;
				this.content = contentData.content;
				this.imageList = contentData.imageList;
				this.imageSize = contentData.imageSize;
				this.selectLabel = contentData.selectLabel;
				this.selGoods = contentData.selGoods;
				this.tempOptions = contentData.tempOptions;
				
				// 恢复 productId
				if (contentData.productId) {
					// #ifdef H5
					if (!this.$Route.query.productId) {
						this.$Router.replace({
							path: this.$Route.path,
							query: {
								...this.$Route.query,
								productId: contentData.productId
							}
						});
					}
					// #endif
					// #ifdef MP-WEIXIN
					uni.setStorageSync('temp_publish_productId', contentData.productId);
					uni.setStorageSync('temp_publish_goodsId', contentData.goodsId);
					// #endif
				}
			}
		},

		// 检查是否有未保存的内容
		hasUnsavedContent() {
			return Boolean(
				this.title.trim() ||
				this.content.trim() ||
				this.imageList.length ||
				this.selGoods.length ||
				Object.keys(this.selectLabel).length ||
				(this.currentType === 'video' && (
					Object.keys(this.upload_video).length ||
					Object.keys(this.cover).length ||
					this.video_desc.trim()
				))
			);
		},

		// 处理页面离开
		handlePageExit() {
			if (this.hasUnsavedContent()) {
				return new Promise((resolve, reject) => {
					uni.showModal({
						title: this.$L('发布内容未保存'),
						content: this.$L('是否保存当前编辑的内容？'),
						success: (res) => {
							if (res.confirm) {
								this.saveContentToStorage();
								uni.showToast({
									title: this.$L('内容已保存'),
									icon: 'none',
									duration: 1500
								});
							} else {
								this.clearContent();
								uni.removeStorageSync('graphicReleaseContent');
							}
							resolve(true);
						},
						fail: () => {
							resolve(true);
						}
					});
				});
			}
			return Promise.resolve(true);
		},

		// 添加页面隐藏处理
		onHide() {
			// 如果是发布成功后的隐藏，不需要处理
			if (!this.hasUnsavedContent()) return;
			
			// 检查是否是选择图片等临时操作
			if (this.isTemporaryOperation) {
				this.isTemporaryOperation = false;
				return;
			}
			
			this.handlePageExit();
		},

		// 添加页面卸载处理
		onUnload() {
			// 如果是发布成功后的卸载，不需要处理
			if (!this.hasUnsavedContent()) return;
			
			// 检查是否是选择图片等临时操作
			if (this.isTemporaryOperation) {
				this.isTemporaryOperation = false;
				return;
			}
			
			this.handlePageExit();
		},

		// 监听tab切换
		onTabItemTap(e) {
			// #ifdef MP-WEIXIN
			// 获取目标页面路径
			const targetPath = e.pagePath;
			
			// 如果是点击发布组件tab，直接返回
			if (targetPath.includes('graphic/graphicRelease')) {
				return;
			}

			if (this.hasUnsavedContent()) {
				uni.showModal({
					title: this.$L('发布内容未保存'),
					content: this.$L('是否保存当前编辑的内容？'),
					success: (res) => {
						if (res.confirm) {
							this.saveContentToStorage();
							uni.showToast({
								title: this.$L('内容已保存'),
								icon: 'none',
								duration: 1500
							});
						} else {
							this.clearContent();
							uni.removeStorageSync('graphicReleaseContent');
						}
						
						uni.switchTab({
							url: '/' + targetPath
						});
					}
				});
			} else {
				uni.switchTab({
					url: '/' + targetPath
				});
			}
			// #endif
		},

		// 获取商品详情
		getGoodsDetail(goodsId, productId) {
			console.log('getGoodsDetail 被调用，goodsId:', goodsId);
			console.log('getGoodsDetail 被调用，productId:', productId);
			
			if (!goodsId || !productId) {
				console.log('缺少必要参数，goodsId:', goodsId, 'productId:', productId);
				return;
			}
			
			let param = {};
			param.url = 'v3/goods/front/goods/details';
			param.method = 'GET';
			param.data = {
				goodsId: goodsId,
				productId: productId
			};
			
			console.log('准备发送请求，参数:', param);
			
			this.$request(param).then(res => {
				console.log('获取商品详情响应:', res);
				if (res.state == 200) {
					const goodsData = res.data;
					this.selGoods = [{
						goodsId: goodsData.goodsId,
						goodsImage: goodsData.shareImage || goodsData.defaultProduct?.goodsPics?.[0],
						goodsName: goodsData.goodsName,
						goodsPrice: goodsData.defaultProduct?.productPrice,
						productPrice: goodsData.defaultProduct?.marketPrice,
						productId: productId  // 添加productId到selGoods中
					}];
					console.log('设置 selGoods:', this.selGoods);
					uni.setStorageSync('selGoods', this.selGoods);
				} else {
					this.$api.msg(res.msg);
				}
			}).catch(err => {
				console.error('获取商品详情失败:', err);
				this.$api.msg('获取商品详情失败');
			});
		},

		// H5端路由变化处理
		handleRouteChange(event) {
			// 如果是发布页面，不处理
			if (location.href.includes('graphic/graphicRelease')) {
				return;
			}

			if (this.hasUnsavedContent()) {
				event.preventDefault();
				uni.showModal({
					title: this.$L('发布内容未保存'),
					content: this.$L('是否保存当前编辑的内容？'),
					success: (res) => {
						if (res.confirm) {
							this.saveContentToStorage();
							uni.showToast({
								title: this.$L('内容已保存'),
								icon: 'none',
								duration: 1500
							});
						} else {
							this.clearContent();
							uni.removeStorageSync('graphicReleaseContent');
						}
						// 继续路由跳转
						this.$Router.push(location.pathname);
					}
				});
			}
		},

		// H5端路由变化处理
		handleBeforeUnload(event) {
			if (this.hasUnsavedContent()) {
				event.preventDefault();
				event.returnValue = '';
				this.handlePageExit();
			}
		},
	},

	mounted() {
		// #ifdef H5
		// 监听刷新
		window.addEventListener('beforeunload', this.handleBeforeUnload);
		// #endif
	},
	
	beforeDestroy() {
		// #ifdef H5
		window.removeEventListener('beforeunload', this.handleBeforeUnload);
		// #endif
	},

	// #ifdef H5
	beforeRouteLeave(to, from, next) {
		// 如果是发布页面，直接通过
		if (to.path.includes('graphic/graphicRelease')) {
			next();
			return;
		}

		if (this.hasUnsavedContent()) {
			uni.showModal({
				title: this.$L('发布内容未保存'),
				content: this.$L('是否保存当前编辑的内容？'),
				success: (res) => {
					if (res.confirm) {
						this.saveContentToStorage();
						uni.showToast({
							title: this.$L('内容已保存'),
							icon: 'none',
							duration: 1500
						});
					} else {
						this.clearContent();
						uni.removeStorageSync('graphicReleaseContent');
					}
					next();
				},
				fail: () => {
					next(false);
				}
			});
		} else {
			next();
		}
	},
	// #endif
}
</script>

<style lang="scss" scoped>
.main_graRe {
	background: #fff;
	min-height: 100vh;
	padding-bottom: 160rpx;

	.content {
		padding: 32rpx 32rpx 0;

		.section-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #333;
			margin-bottom: 24rpx;
		}

		.summary-section {
			.section-title {
				margin-bottom: 32rpx;
			}
		}

		.upload-section {
			margin-bottom: 48rpx;

			.pic_part_sel {
				.image-grid {
					display: flex;
					flex-wrap: wrap;
					gap: 16rpx;
				}

				.cover_image,
				.upload-placeholder.small {
					width: 216rpx;
					height: 216rpx;
					background: #F5F5F5;
					border-radius: 16rpx;
				}

				.cover_image {
					position: relative;
					overflow: hidden;

					.image {
						width: 100%;
						height: 100%;
						object-fit: cover;
					}

					.close_img {
						position: absolute;
						top: 10rpx;
						right: 10rpx;
						width: 40rpx;
						height: 40rpx;
						line-height: 36rpx;
						text-align: center;
						background: rgba(0, 0, 0, 0.5);
						color: #FFFFFF;
						border-radius: 50%;
						font-size: 28rpx;
						z-index: 1;
					}
				}

				.upload-placeholder {
					display: flex;
					align-items: center;
					justify-content: center;

					&.large {
						width: 100%;
						height: 400rpx;
						background: #F5F5F5;
						border-radius: 16rpx;

						.camera-icon {
							width: 80rpx !important;
							height: 80rpx !important;
							margin-bottom: 24rpx !important;
						}

						.upload-text {
							font-size: 28rpx !important;
						}
					}

					&.small {
						.camera-icon {
							width: 48rpx;
							height: 48rpx;
							margin-bottom: 12rpx;
						}

						.upload-text {
							font-size: 24rpx;
						}
					}

					.upload-content {
						display: flex;
						flex-direction: column;
						align-items: center;

						.camera-icon {
							display: block;
						}

						.upload-text {
							color: #999999;
						}
					}
				}
			}

			.video-upload {
				display: flex;
				gap: 24rpx;

				.video-main {
					flex: 2;
					height: 400rpx;

					.video-preview {
						width: 100%;
						height: 100%;
						position: relative;

						video {
							width: 100%;
							height: 100%;
							border-radius: 12rpx;
						}

						.preview-overlay {
							position: absolute;
							top: 0;
							left: 0;
							width: 100%;
							height: 100%;
						}

						.delete-btn {
							position: absolute;
							top: 12rpx;
							right: 12rpx;
							width: 40rpx;
							height: 40rpx;
							background: rgba(0, 0, 0, 0.5);
							color: #fff;
							border-radius: 50%;
							text-align: center;
							line-height: 40rpx;
						}
					}
				}

				.video-cover {
					flex: 1;
					height: 400rpx;

					.cover-preview {
						width: 100%;
						height: 100%;
						position: relative;

						.cover-img {
							width: 100%;
							height: 100%;
							border-radius: 12rpx;
						}

						.delete-btn {
							position: absolute;
							top: 12rpx;
							right: 12rpx;
							width: 40rpx;
							height: 40rpx;
						}
					}
				}
			}
		}

		.input-section {
			margin-bottom: 48rpx;

			.input {
				width: 100%;
				height: 88rpx;
				// border-radius: 12rpx;
				// padding: 0 24rpx;
				font-size: 28rpx;
				// border: 2rpx solid #EEEEEE;
			}

			.textarea-low {
				width: 100%;
				margin-top: 24rpx;
				height: 120rpx;
				// border-radius: 12rpx;
				// padding: 24rpx;
				font-size: 28rpx;
				// border: 2rpx solid #EEEEEE;
			}

			.textarea {
				width: 100%;
				margin-top: 24rpx;
				height: 240rpx;
				// border-radius: 12rpx;
				// padding: 24rpx;
				font-size: 28rpx;
				// border: 2rpx solid #EEEEEE;
			}

			&.summary-section {
				.section-title {
					margin-bottom: 24rpx;
				}
			}
		}

		.tag-section {
			margin-bottom: 48rpx;

			.tag-list {
				display: flex;
				flex-wrap: wrap;
				gap: 24rpx;
				margin-top: 24rpx;

				.tag-item {
					padding: 16rpx 32rpx;
					border-radius: 40rpx;
					background: #f8f8f8;
					font-size: 28rpx;
					color: #666;

					&.tag-selected {
						background: #7ABC79;
						color: #fff;
					}
				}
			}
		}

		.goods-section {
			margin-bottom: 48rpx;

			.goods-wrap {
				display: flex;
				flex-wrap: wrap;
				margin: -8rpx;
				margin-top: 24rpx;

				.goods-item {
					width: calc(33.33% - 16rpx);
					margin: 8rpx;
					background: #FFFFFF;
					border-radius: 12rpx;
					overflow: hidden;

					.goods-content {
						position: relative;

						.goods-image {
							width: 100%;
							height: 220rpx;
							background: #f8f8f8;
						}

						.goods-info {
							padding: 12rpx;

							.goods-name {
								font-size: 24rpx;
								color: #2d2d2d;
								line-height: 32rpx;
								margin-bottom: 4rpx;
								display: -webkit-box;
								-webkit-box-orient: vertical;
								-webkit-line-clamp: 2;
								overflow: hidden;
									word-break: break-word;
							}

							.goods-price {
								display: flex;
								align-items: center;
								color: var(--color_video_main, #FF0000);

								.unit {
									font-size: 20rpx;
								}

								.price {
									font-size: 28rpx;
									font-weight: bold;
									margin-left: 2rpx;
								}
							}
						}

						.goods-delete {
							position: absolute;
							top: 8rpx;
							right: 8rpx;
							width: 36rpx;
							height: 36rpx;
							line-height: 32rpx;
							text-align: center;
							background: rgba(0, 0, 0, 0.5);
							color: #FFFFFF;
							border-radius: 50%;
							font-size: 24rpx;
							z-index: 1;
						}
					}

					&.add-item {
						.add-content {
							height: 220rpx;
							display: flex;
							flex-direction: column;
							align-items: center;
							justify-content: center;
							background: #f8f8f8;

							.goods-icon {
								width: 48rpx;
								height: 48rpx;
								margin-bottom: 12rpx;
							}

							.add-text {
								font-size: 24rpx;
								color: #999999;
							}
						}
					}
				}
			}
		}
	}
}

.type-section {
	margin-bottom: 48rpx;

	.type-list {
		display: flex;
		gap: 24rpx;
		margin-top: 24rpx;

		.type-item {
			padding: 16rpx 32rpx;
			border-radius: 40rpx;
			background: #f8f8f8;
			font-size: 28rpx;
			color: #666;

			&.type-selected {
				background: #7ABC79;
				color: #fff;
			}
		}
	}
}

.video-upload {
	display: flex;
	gap: 24rpx;

	.video-main {
		flex: 2;
		height: 400rpx;

		.video-preview {
			width: 100%;
			height: 100%;
			position: relative;

			video {
				width: 100%;
				height: 100%;
				border-radius: 12rpx;
			}

			.preview-overlay {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
			}

			.delete-btn {
				position: absolute;
				top: 12rpx;
				right: 12rpx;
				width: 40rpx;
				height: 40rpx;
				background: rgba(0, 0, 0, 0.5);
				color: #fff;
				border-radius: 50%;
				text-align: center;
				line-height: 40rpx;
			}
		}
	}

	.video-cover {
		flex: 1;
		height: 400rpx;

		.cover-preview {
			width: 100%;
			height: 100%;
			position: relative;

			.cover-img {
				width: 100%;
				height: 100%;
				border-radius: 12rpx;
			}

			.delete-btn {
				position: absolute;
				top: 12rpx;
				right: 12rpx;
				width: 40rpx;
				height: 40rpx;
			}
		}
	}
}

.upload-placeholder {
	width: 100%;
	height: 100%;
	background: #f8f8f8;
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	justify-content: center;

	.upload-content {
		display: flex;
		flex-direction: column;
		align-items: center;

		.camera-icon {
			width: 80rpx;
			height: 80rpx;
			margin-bottom: 24rpx;
		}

		.upload-text {
			font-size: 26rpx;
			color: #999;
		}
	}
}

.publish-button {
	width: 100%;
	margin-top: 30rpx;
	height: 88rpx;
	background-color: #89BA7F;
	border-radius: 44rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 99;

	text {
		color: #FFFFFF;
		font-size: 32rpx;
		font-weight: 500;
	}
}
</style>