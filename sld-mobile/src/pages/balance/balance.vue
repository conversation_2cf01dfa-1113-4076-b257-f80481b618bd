<!-- 我的钱包页面 -->
<template>
  <view class="container" :style="mix_diyStyle">
    <view class="content">
      <image class="bg" :src="imgUrl + 'balance_bg.png'" lazy-load />
      <view class="detail flex_column_start_start">
        <text class="balance_title">{{ $L('可用余额(元)') }}</text>
		<view class="balance_num flex_row_start_end" v-if="balanceData&&balanceData.balanceAvailable!=undefined">
          <text class="unit">{{ $L('￥') }}</text>
          <text class="amount">{{
            $getPartNumber(balanceData.balanceAvailable, 'int')
          }}</text>
          <text class="unit">{{
            $getPartNumber(balanceData.balanceAvailable, 'decimal')
          }}</text>
        </view>
        <view class="total_amount flex_row_start_center">
          <image
            class="amount_icon"
            :src="imgUrl + 'amount_icon.png'"
            lazy-load
          />
          <text class="con tit">{{ $L('账户总额 (元)') }}</text>
          <text class="con unit">{{ $L('￥') }}</text>
          <text class="con amount">{{ balanceData.rechargeSum }}</text>
        </view>
      </view>
      <view class="right_btns flex_column_center_center">
        <view
          v-if="rechargeEnable"
          class="recharge_btn flex_row_center_center"
          @click="
            navTo({
              path: '/pages/recharge/recharge',
              query: {
                balance: balanceData.rechargeSum,
                payMethodType: 'rechargeBalance'
              }
            })
          "
        >
          <text class="con">{{ $L('充值') }}</text>
          <text class="iconfont iconziyuan11"></text>
        </view>
        <view
          v-if="outputEnable"
          class="output_btn flex_row_center_center"
          @click.stop="output()"
        >
          <text class="con">{{ $L('提现') }}</text>
          <text class="iconfont iconziyuan11"></text>
        </view>
      </view>
    </view>
    <view class="detail_list flex_column_start_start">
      <view
        class="item flex_row_between_center b_b"
        @click="navTo('/pages/balance/list')"
      >
        <view class="left flex_row_start_center">
          <image class="icon" :src="imgUrl + 'balance_icon.png'" lazy-load />
          <text class="tit">{{ $L('余额明细') }}</text>
        </view>
        <text class="iconfont iconziyuan11"></text>
      </view>
      <view
        class="item flex_row_between_center"
        :class="{ b_b: outputEnable }"
        @click="navTo('/pages/recharge/list')"
        v-if="rechargeEnable"
      >
        <view class="left flex_row_start_center">
          <image class="icon" :src="imgUrl + 'recharge_icon.png'" lazy-load />
          <text class="tit">{{ $L('充值明细') }}</text>
        </view>
        <text class="iconfont iconziyuan11"></text>
      </view>
      <view
        v-if="outputEnable"
        class="item flex_row_between_center b_b"
        @click="navTo('/pages/balance/outputList')"
      >
        <view class="left flex_row_start_center">
          <image class="icon" :src="imgUrl + 'output_history.png'" lazy-load />
          <text class="tit">{{ $L('提现记录') }}</text>
        </view>
        <text class="iconfont iconziyuan11"></text>
      </view>
      <view
        v-if="outputEnable"
        class="item flex_row_between_center"
        @click="navTo('/pages/balance/account')"
      >
        <view class="left flex_row_start_center">
          <image class="icon" :src="imgUrl + 'output_account.png'" lazy-load />
          <text class="tit">{{ $L('提现账号') }}</text>
        </view>
        <text class="iconfont iconziyuan11"></text>
      </view>
    </view>
    <uni-popup ref="tips" type="dialog">
      <uni-popup-dialog
        type="input"
        :title="$L('提示')"
        :content="$L('您尚未设置支付密码，请设置支付密码后再进行后续操作。')"
        :confirmText="$L('设置密码')"
        :duration="2000"
        @close="closePop('tips')"
        @confirm="confirmOutput"
      ></uni-popup-dialog>
    </uni-popup>
    <uni-popup ref="pad" type="dialog" @change="changePad" :animation="false">
      <view class="pad_main">
        <image
          class="pad_close"
          @click="closePad"
          :src="imgUrl + 'chat/close.png'"
          mode="widthFix"
          lazy-load
        ></image>
        <view class="pad_tit">{{ $L('请输入支付密码') }}</view>
       <view class="pad_tip" :class="{ error: padErr }">{{
          padErr ? padErr : $L('请输入支付密码以完成后续操作')
        }}</view>
       <input
          type="password"
          class="pad_pd"
          cursor-spacing="80"
          v-model="password"
          maxlength="30"
          :placeholder="$L('请输入平台支付密码')"
        />
        <view class="pad_line"></view>
        <view class="pad_btn" @click="gotoOutput">{{ $L('确定') }}</view>
        <view class="forgot-pwd" @click="navTo({path: '/pages/account/managePwd', query: {source: 'reset_pay'}})">
          {{ $L('忘记支付密码？') }}
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import uniPopupDialog from '@/components/uni-popup/uni-popup-dialog.vue'
import uniPopup from '@/components/uni-popup/uni-popup.vue'
import { mapMutations, mapState } from 'vuex'
export default {
  components: {
    uniPopup,
    uniPopupDialog
  },
  data() {
    return {
      imgUrl: process.env.VUE_APP_IMG_URL,
      balanceData: {}, //余额
      password: '',
      padErr: '',
      hasAccount: false,
      rechargeEnable: false, //充值开关
      outputEnable: false, //提现开关
      isFirst: true
    }
  },
  computed: {
    ...mapState(['userInfo', 'userCenterData'])
  },
  onLoad() {
	setTimeout(()=>{
		uni.setNavigationBarTitle({
			title: this.$L('我的钱包')
		})
	},0)
    this.getInfo()
  },
  onShow() {
    if (!this.isFirst) {
      this.getInfo()
    } else {
      this.isFirst = false
    }
    this.getAccountList()
    this.getOutputSet()
  },
  methods: {
    ...mapMutations(['setUserCenterData']),
    //获取配置开关
    getOutputSet() {
      let param = {}
      param.url = 'v3/system/front/setting/getSettings'
      param.method = 'GET'
      param.data = {}
      param.data.names = 'recharge_is_enable,withdraw_is_enable'
      this.$request(param).then((res) => {
        if (res.state == 200) {
          this.rechargeEnable = res.data[0] == '1' ? true : false
          this.outputEnable = res.data[1] == '1' ? true : false
        } else {
          this.$api.msg(res.msg)
        }
      })
    },
    //获取会员账户余额
    getInfo() {
      this.$request({
        url: 'v3/member/front/balanceRecharge/getMemberBalance',
        method: 'GET'
      })
        .then((res) => {
          if (res.state == 200) {
            this.balanceData = res.data
            if (
              this.userCenterData.memberBalance !=
              this.balanceData.balanceAvailable
            ) {
              this.userCenterData.memberBalance =
                this.balanceData.balanceAvailable
              this.setUserCenterData(this.userCenterData)
            }
          } else {
            this.$api.msg(res.msg)
          }
        })
        .catch((e) => {})
    },
    //获取提现账号列表
    getAccountList() {
      let param = {}
      param.method = 'GET'
      param.url = 'v3/member/front/member/cash/list'
      param.data = {}
      param.data.current = 1
      param.data.pageSize = 1
      this.$request(param).then((res) => {
        if (res.state == 200 && res.data.list.length) {
          this.hasAccount = true
        } else {
          this.hasAccount = false
        }
      })
    },
    output() {
      if (!this.hasAccount) {
        this.$api.msg(this.$L('您尚未添加提现账号，请添加账号后再进行提现。'))
      } else if (this.userCenterData.hasPayPassword) {
        this.$refs.pad.open()
      } else {
        this.$refs.tips.open()
      }
    },
    closePop(type) {
      if (type == 'tips') {
        this.$refs.tips.close()
      } else {
        this.$refs.pad.close()
      }
    },
    confirmOutput() {
      this.$refs.tips.close()
      this.navTo({
        path: '/pages/account/managePwd',
        query: {
          source: 'set_pay'
        }
      })
    },
    navTo(url) {
      // if (url.path=='/pages/recharge/recharge') {
      // 	this.getReChargeSetting(url);
      // } else {
      this.$Router.push(url)
      // }
    },
    changePad(e) {
      if (!e.show) {
        this.password = ''
        this.padErr = ''
      }
    },
    closePad() {
      this.$refs.pad.close()
      this.password = ''
      this.padErr = ''
    },
    gotoOutput() {
      if (!this.password) {
        this.padErr = this.$L('请输入平台支付密码')
      } else {
        let param = {}
        param.method = 'GET'
        param.url = 'v3/member/front/member/cash/log/verifyPwd'
        param.data = {}
        param.data.payPwd = this.$base64Encrypt(this.password)
        this.$request(param).then((res) => {
          if (res.state == 200) {
            this.padErr = ''
            this.$refs.pad.close()
            this.$Router.push('/pages/balance/outputDo')
          } else {
            this.padErr = res.msg
          }
        })
      }
    },

    getReChargeSetting(url) {
      this.$request({
        url: 'v3/system/front/setting/getSettings',
        data: {
          names: 'recharge_is_enable'
        }
      }).then((res) => {
        if (res.state == 200) {
          this.rechargeSetting = res.data[0]
          if (this.rechargeSetting == '1') {
            this.$Router.push(url)
          } else {
            this.$api.msg(this.$L('暂未开启充值功能'))
          }
        }
      })
    }
  }
}
</script>

<style lang="scss">
uni-page-body {
  display: flex;
  height: 100%;
}

page {
  background: $bg-color-split;
  width: 750rpx;
  margin: 0 auto;

  .container {
    margin-top: 20rpx;
    display: flex;
    flex-direction: column;
    flex: 1;

    .content {
      position: relative;
      width: 750rpx;
      height: 400rpx;
      padding: 30rpx;
      background-color: #ffffff;

      .right_btns {
        position: absolute;
        top: 70rpx;
        right: 30rpx;
        z-index: 2;
        width: 142rpx;
        height: 150rpx;

        .recharge_btn,
        .output_btn {
          width: 142rpx;
          height: 60rpx;
          background: linear-gradient(
            90deg,
            rgba(238, 238, 238, 0.5) 0%,
            rgba(238, 238, 238, 0) 100%
          );
          // opacity:0.5;
          border-radius: 30rpx;
          color: #fff;
          font-size: 30rpx;

          .iconfont {
            font-size: 16rpx;
            transform: scale(0.75);
            display: inline-block;
            margin-left: 5rpx;
          }
        }

        .output_btn {
          margin-top: 30rpx;
        }
      }

      .bg {
        width: 690rpx;
        height: 340rpx;
      }

      .detail {
        position: absolute;
        left: 30rpx;
        right: 30rpx;
        top: 30rpx;
        bottom: 30rpx;
        z-index: 2;
        padding: 32rpx 40rpx;
        color: #fff;

        .balance_title {
          font-size: 26rpx;
        }

        .balance_num {
          font-weight: bold;
          margin-top: 41rpx;
          color: #fff;

          .unit {
            font-size: 46rpx;
            font-weight: bold;
            line-height: 52rpx;
          }

          .amount {
            font-size: 66rpx;
            font-weight: bold;
            line-height: 66rpx;
          }
        }

        .total_amount {
          margin-top: 84rpx;

          .amount_icon {
            width: 27rpx;
            height: 23rpx;
            margin-right: 9rpx;
          }

          .con {
            color: rgba(255, 255, 255, 0.8);
          }

          .tit,
          .amount {
            font-size: 26rpx;
          }

          .unit {
            font-size: 22rpx;
            margin-left: 5rpx;
          }
        }
      }
    }

    .detail_list {
      padding: 0 30rpx;
      background-color: #ffffff;

      .item {
        width: 100%;
        height: 141rpx;
        position: relative;

        .left {
          .icon {
            width: 80rpx;
            height: 80rpx;
            margin-right: 20rpx;
          }

          .tit {
            color: $main-font-color;
            font-size: 30rpx;
          }
        }

        .iconfont {
          color: $main-third-color;
          font-size: 32rpx;
          transform: scale(0.75);
          display: inline-block;
        }
      }
    }

    .pad_main {
      position: relative;
      width: 560rpx;
      border-radius: 14rpx;
      background-color: #fff;
      overflow: hidden;

      .pad_close {
        position: absolute;
        top: 30rpx;
        right: 30rpx;
        width: 22rpx;
      }

      .pad_tit {
        line-height: 46rpx;
        color: #111111;
        font-size: 34rpx;
        font-family: PingFang SC;
        font-weight: bold;
        text-align: center;
        margin-top: 30rpx;
        margin-bottom: 30rpx;
      }

      .pad_tip {
        line-height: 42rpx;
        color: #333333;
        font-size: 28rpx;
        font-family: PingFang SC;
        font-weight: 500;
        text-align: center;
        margin-bottom: 66rpx;

        &.error {
          color: #f50202;
        }
      }

      .pad_pd {
        font-size: 28rpx;
        margin-left: 30rpx;
        margin-right: 30rpx;
      }
	  
      .pad_line {
        width: 90%;
        height: 2rpx;
        background-color: #e7e1e1;
        margin: 30rpx auto;
      }

      .pad_btn {
        width: 500rpx;
        height: 70rpx;
        line-height: 70rpx;
        color: #ffffff;
        font-family: PingFang SC;
        font-weight: 500;
        text-align: center;
        background: var(--color_main_bg);
        border-radius: 70rpx;
        margin: 30rpx auto;
      }

      .forgot-pwd {
        text-align: center;
        font-size: 26rpx;
        color: #666;
        padding-bottom: 30rpx;
        text-decoration: underline;
      }
    }
  }
}
</style>
