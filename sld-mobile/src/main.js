// #ifdef H5
import './static/h5/mountFile.js'
// #endif
import TopHeader from '@/components/TopHeader.vue'
import loadingState from '@/components/loading-state.vue'
import loginPop from '@/components/loginPop/loginPop.vue'
import diyStyle from '@/diyStyle/index.js'
import svgGroup from '@/diyStyle/svgGroup.vue'
import { GlobalSocketInstance } from '@/utils/socket.js'
import { RouterMount } from 'uni-simple-router'
import Vue from 'vue'
import App from './App'
import uniPopup from './components/uni-popup/uni-popup.vue'
import store from './store'
import { addLocalCart, base64Encrypt, getCurLanguage, isWeiXinBrower, weiXinAppShare } from './utils/base.js'
import {
	WXBrowserShareThen,
	back,
	handleFx,
	checkEmail,
	checkIdentity,
	checkImgCode,
	checkMobile,
	checkPaginationHasMore,
	checkPwd,
	checkSpace,
	checkTel,
	copy,
	diyNavTo,
	formatChatTime,
	formatPercent,
	formatW,
	frequentyleClick,
	getCartNum,
	getLoginClient,
	getPartNumber,
	getPath,
	getQueryVariable,
	getUnLoginCartParam,
	isShowTime,
	isTabPage,
	livePlayNav,
	loginGoPage,
	msg,
	phoneCall,
	prePage,
	preventMutiClick,
	replaceConByPosition,
	replaceEmoji,
	setCookie,
	setPointIsCookie,
	setStoreIsCookie,
	sldCommonTip,
	validatorEmoji,
	videoPlayNav,
	weiXinBrowerPay,
	weiXinBrowerShare,
	wxLoginClient,
	setFromUrl
} from './utils/common.js'
import request from './utils/request'
import { sldStatEvent } from './utils/stat.js'




Vue.component('loginPop',loginPop)
Vue.component('svgGroup',svgGroup)
Vue.component('TopHeader',TopHeader)
Vue.component('loadingState',loadingState)
Vue.component('uniPopup',uniPopup)
Vue.use(diyStyle)

Vue.config.productionTip = false
Vue.prototype.$store = store;
Vue.prototype.$setFromUrl=setFromUrl
Vue.prototype.$request = request;
Vue.prototype.$checkPaginationHasMore = checkPaginationHasMore;
Vue.prototype.$checkSpace = checkSpace;
Vue.prototype.$replaceConByPosition = replaceConByPosition;
Vue.prototype.$getPartNumber = getPartNumber;
Vue.prototype.$checkMobile = checkMobile;
Vue.prototype.$checkPwd = checkPwd;
Vue.prototype.$loginGoPage = loginGoPage;
Vue.prototype.$formatW = formatW;
Vue.prototype.$checkEmail = checkEmail;
Vue.prototype.$diyNavTo = diyNavTo;
Vue.prototype.$setCookie = setCookie;
Vue.prototype.$formatPercent = formatPercent;
Vue.prototype.$setStoreIsCookie = setStoreIsCookie;
Vue.prototype.$setPointIsCookie = setPointIsCookie;
Vue.prototype.$frequentyleClick = frequentyleClick;
Vue.prototype.$L = getCurLanguage;
Vue.prototype.$api = {msg,prePage};
Vue.prototype.$isWeiXinBrower = isWeiXinBrower;
Vue.prototype.$weiXinBrowerShare = weiXinBrowerShare;
Vue.prototype.$weiXinAppShare = weiXinAppShare;
Vue.prototype.$getQueryVariable = getQueryVariable;
Vue.prototype.$weiXinBrowerPay = weiXinBrowerPay;
Vue.prototype.$sldCommonTip = sldCommonTip;
Vue.prototype.$getLoginClient = getLoginClient;
Vue.prototype.$wxLoginClient = wxLoginClient
Vue.prototype.$back= back;
Vue.prototype.$isShowTime = isShowTime;
Vue.prototype.$formatChatTime = formatChatTime;
Vue.prototype.$sldStatEvent = sldStatEvent;
Vue.prototype.$checkTel = checkTel
Vue.prototype.$checkIdentity = checkIdentity
Vue.prototype.$WXBrowserShareThen = WXBrowserShareThen
Vue.prototype.$videoPlayNav = videoPlayNav
Vue.prototype.$livePlayNav = livePlayNav
Vue.prototype.$base64Encrypt = base64Encrypt
Vue.prototype.$getUnLoginCartParam = getUnLoginCartParam
Vue.prototype.$checkImgCode = checkImgCode
Vue.prototype.$isTabPage = isTabPage
Vue.prototype.$preventMutiClick=preventMutiClick
Vue.prototype.$globalSocketIO = GlobalSocketInstance()
Vue.prototype.$phoneCall = phoneCall
Vue.prototype.$copy = copy
Vue.prototype.$validatorEmoji = validatorEmoji
Vue.prototype.$replaceEmoji = replaceEmoji
Vue.prototype.$getCartNum = getCartNum
Vue.prototype.$addLocalCart = addLocalCart
Vue.prototype.$getPath = getPath
Vue.prototype.$handleFx = handleFx



App.mpType = 'app'


const app = new Vue({
	...App
})

//v1.3.5起 H5端 你应该去除原有的app.$mount();使用路由自带的渲染方式
// #ifdef H5
RouterMount(app,'#app');
// #endif
// #ifndef H5
app.$mount(); //为了兼容小程序及app端必须这样写才有效果
// #endif
console.log('main.js...')