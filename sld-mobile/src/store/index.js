import Vue from 'vue'
import Vuex from 'vuex'
import { updateStatCommonProperty } from '../utils/stat.js'
import request from '../utils/request.js'
import { useMemberConfig } from '../utils/hooks.js'
Vue.use(Vuex)


const store = new Vuex.Store({
	state: {
		hasLogin: uni.getStorageSync('hasLogin') ?? false,
		userInfo: uni.getStorageSync('userInfo') ?? {},
		userCenterData: uni.getStorageSync('userCenterData') ?? {}, //个人中心页面数据
		cartData: {}, //购物车数据
		addressList: [], //收货地址列表
		chatBaseInfo: uni.getStorageSync('chatBaseInfo') ? uni.getStorageSync('chatBaseInfo') : {},//聊天的基本信息，包含会员id、头像、店铺id、头像
		memberConfig: {},
		x_diyStyle: {},
		msgNumState: 0,
		shopSetting: uni.getStorageSync('shopSetting') ?? {},
		verificationCodeCheckIsEnable: 0,
	},

	mutations: {
		setVerificationCodeCheck(state, value) {
			state.verificationCodeCheckIsEnable = value;
		},

		setHasLogin(state, provider) {
			state.hasLogin = provider;
			uni.setStorageSync('hasLogin', provider);
		},

		//登录时存储信息
		login(state, provider) {
			state.hasLogin = true;
			uni.setStorageSync('hasLogin', true);
			let { role, access_token, refresh_token, ...userData } = provider
			let target = userData
			let {
				member_access_token,
				member_refresh_token
			} = state.userInfo
			target.member_access_token = member_access_token
			target.member_refresh_token = member_refresh_token
			target[`${role}_access_token`] = access_token
			target[`${role}_refresh_token`] = refresh_token
			state.userInfo = target;
			//缓存用户登陆状态
			uni.setStorageSync('userInfo', state.userInfo);
			const app = getApp()
			app.$vm.$globalSocketIO.initialModule()
			updateStatCommonProperty({ memberId: provider.memberId });//登录成功需要更新统计里面的会员id
		},

		loginRole(state, provider) {
		},
		logout(state) {
			state.hasLogin = false;
			state.userInfo = {};
			state.userCenterData = {};
			state.cartData = {};
			state.addressList = [];
			state.chatBaseInfo = {}
			uni.removeStorage({ key: 'addressId' });
			uni.removeStorage({ key: 'userInfo' });
			uni.removeStorage({ key: 'userCenterData' });
			uni.removeStorage({ key: 'hasLogin' });
			updateStatCommonProperty({ memberId: 0 });//退出登录需要将会员id置为0
			const app = getApp()
			app.$vm.$globalSocketIO.closeSocket()
		},

		//设置个人中心的数据
		setUserCenterData(state, provider) {
			state.userCenterData = provider
			//缓存用户个人信息
			uni.setStorageSync('userCenterData', provider)
		},

		//操作购物车的数据
		operateCartData(state, provider) {
			state.cartData = provider
		},

		//操作收货地址
		operateAddressData(state, provider) {
			state.addressList = provider
		},
		//保存聊天的会员id、会员头像，店铺id、店铺头像
		saveChatBaseInfo(state, provider) {
			state.chatBaseInfo = provider
			//缓存聊天的基本信息
			uni.setStorageSync('chatBaseInfo', provider)
		},

		saveMemberConfig(state, payload) {
			state.memberConfig = { ...state.memberConfig, ...payload }
		},
		saveDiyStyle(state, provider) {
			state.x_diyStyle = provider
			uni.setStorageSync('x_diyStyle', provider)
		},

		setMsgNum(state, provider) {
			state.msgNumState = provider
		},
	},
	actions: {
		getMemberConfig(context, args) {
			return useMemberConfig(args, context)
		},
		getTlSetting(context) {
			request({
				url: 'v3/system/front/setting/getSettings',
				data: {
					names: 'tl_is_enable'
				}
			}).then(res => {
				if (res.state == 200) {
					context.commit('setTl', res.data[0])
				}
			})
		},

		getVerificationCodeCheckSetting(context) {
			request({
				url: 'v3/system/front/setting/getSettings',
				data: {
					names: 'verificationCodeCheckIsEnable'
				}
			}).then(res => {
				// TODO
				context.commit('setVerificationCodeCheck', 0)
				// if (res.state == 200 && res.data && res.data.length > 0) {
				// 	context.commit('setVerificationCodeCheck', Number(res.data[0].value))
				// }
			})
		},
	},

	modules: {
	}
})

export default store
