<script>
import {
	mapActions,
	mapMutations,
	mapState
} from 'vuex';
import Router from './router.js';
import {
	initStat
} from "./utils/stat.js";

// #ifdef H5
import {
	WXBrowserShareStart
} from '@/utils/common.js';
// #endif


export default {
	globalData: {
		goLogin(Route, cb) {
			let _this = this
			uni.showModal({
				title: '提示',
				content: '请登录',
				success: res => {
					if (res.confirm) {
						cb?.()
						let url = Route.path;
						const query = Route.query;
						uni.setStorageSync('fromurl', {
							url,
							query
						});
						Router.push('/pages/public/login')
					}
				}
			});
		},
		...process.env
	}, //全局配置
	computed: {
		...mapState(['userInfo', 'userCenterData'])
	},
	methods: {
		...mapMutations(['setHasLogin']),
		...mapActions(['getTlSetting', 'getVerificationCodeCheckSetting']),

		async initStatics() {
			//初始化统计数据
			let source = 'h5';
			//app-2-start

			//app-2-end
			//wx-1-start
			// #ifdef MP-WEIXIN
			source = 'xcx';
			// #endif
			//wx-1-end

			// #ifdef H5
			await WXBrowserShareStart()
			console.log('微信分享配置')
			// #endif
			//统计初始化


			console.log('统计配置')
			await initStat(this.globalData.statShowDebug, {
				equipmentType: 2, //设备类型，1-pc，2-移动设备，3-其他
				source: source, //终端名称，pc-pc；h5-H5；android-Android；ios-IOS；xcx-微信小程序
				memberId: 0, //会员id默认为0
				ip: '', //移动端ip默认都为空
			});
		},
	},
	async onLaunch() {
		console.log('App Launch...')

		//设置tabbar自定义内容
		this.$diyStyle.dynaSetTabbar()

		//初始化统计
		await this.initStatics()

		//获取验证码检查设置
		await this.getVerificationCodeCheckSetting()

		let userInfo = uni.getStorageSync('userInfo');
		if (userInfo) {
			//更新登陆状态
			this.$globalSocketIO.initialModule()
			this.setHasLogin(true)
		}

		uni.getSystemInfo({
			success: res => {
				this.globalData.bottomSateArea = 0; //手机底部安全区域高度
				let iphoneXArr = ["iPhone X", "iPhone 11", "iPhone 11 Pro Max", "iPhone XR",
					"iPhoneXS"]; //iphone手机底部一条黑线
				if (iphoneXArr.indexOf(res.model) != -1) {
					this.globalData.bottomSateArea = "30rpx";
				}
				this.globalData.statusBarHeight = res.statusBarHeight;
			}
		});

		const lastVersion = uni.getStorageSync('appVersion');
		if (lastVersion !== process.env.VUE_APP_VERSION) {
			uni.showToast({
				title: '提示',
				content: `发现新版本 自动升级${process.env.VUE_APP_VERSION}`,
				showCancel: true,
				success: function () {
					location.reload();
					uni.setStorageSync('appVersion', process.env.VUE_APP_VERSION);
				},
				duration: 1000,
			})

		}

		console.log('App Launch!')

	},

	onShow: function (options) {
	},
	onHide: function () {



	},
}
console.log('App.vue')
</script>

<style lang='scss'>
@import './common/css/icon.css';
@import './common/css/base.css';
@import './common/css/ql_edit.css';
/* #ifdef H5 */
@import './common/css/ql_edit.css';
/* #endif */

[type="search"]::-webkit-search-decoration {
	display: none;
}








/* 微信浏览器分享提示 start */
.wx_brower_share_mask {
	width: 750rpx;
	height: 100vh;
	background-color: rgba(0, 0, 0, 0.45);
	position: fixed;
	z-index: 99999;
	top: 0;
}

/* #ifdef H5 */
page {
	width: 750rpx;
	margin: 0 auto;
}

/* #endif */








/* app-5-start */






/* app-5-end */

.wx_brower_share_top_wrap {
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: flex-end;
	align-items: flex-start;
	margin-top: 150rpx;
}

.wx_brower_share_top_wrap .wx_brower_share_img {
	width: 450rpx;
	height: 150rpx;
	margin-right: 80rpx;
}

.share_h5 {
	width: 100% !important;
	height: 100% !important
}

uni-image>img {
	opacity: unset;
	object-fit: contain;
}

.share_h5_operate_img {
	width: 440rpx !important;
	height: 120rpx !important;
}

.share_h5_close_img {
	width: 50rpx !important;
	height: 50rpx !important;
}

.share_h5_img_bottom {
	width: 50rpx !important;
	height: 160rpx !important;
}

/* 微信浏览器分享提示 end */

.yticon {
	font-family: "yticon" !important;
	font-size: 16px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

/* wx-2-start */
/* #ifdef MP-WEIXIN */
.ql-container {
	display: flex;
	align-items: center;
	line-height: 62rpx;
}

/* #endif */
/* wx-2-end */

.ql-editor {
	padding: 0;
}

button::after {
	border: none;
}

view,
scroll-view,
swiper,
swiper-item,
cover-view,
cover-image,
icon,
text,
rich-text,
progress,
button,
checkbox,
form,
input,
label,
radio,
slider,
switch,
textarea,
navigator,
audio,
camera,
image,
video {
	box-sizing: border-box;
}

/* 骨架屏替代方案 */
.Skeleton {
	background: #f3f3f3;
	padding: 20upx 0;
	border-radius: 8upx;
}

/* 图片载入替代方案 */
.image-wrapper {
	font-size: 0;
	background: #f3f3f3;
	border-radius: 4px;

	image {
		width: 100%;
		height: 100%;
		transition: .6s;
		opacity: 0;

		&.loaded {
			opacity: 1;
		}
	}
}

.clamp {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	display: block;
}

.common-hover {
	background: #f5f5f5;
}

/*边框*/
.b-b:after,
.b-t:after {
	position: absolute;
	z-index: 3;
	left: 0;
	right: 0;
	height: 0;
	content: '';
	transform: scaleY(.5);
	border-bottom: 1rpx solid rgba(0, 0, 0, .1);
}

/*边框*/
.b_b:after,
.b_t:after {
	position: absolute;
	z-index: 3;
	left: 0;
	right: 0;
	height: 0;
	content: '';
	transform: scaleY(.5);
	border-bottom: 1rpx solid rgba(0, 0, 0, .1);
}

.b_b:after {
	bottom: 0;
}

.b_t:after {
	top: 0;
}

.b-b:after {
	bottom: 0;
}

.b-t:after {
	top: 0;
}

/* button样式改写 */
uni-button,
button {
	height: 80upx;
	line-height: 80upx;
	font-size: $font-lg + 2upx;
	font-weight: normal;

	&.no-border:before,
	&.no-border:after {
		border: 0;
	}
}

uni-button[type=default],
button[type=default] {
	color: $font-color-dark;
}

/* input 样式 */
.input-placeholder {
	color: #999999;
}

.placeholder {
	color: #949494;
	font-size: 26rpx;
}

/* wx-3-start */
/* #ifdef MP-WEIXIN */
input {
	font-weight: normal !important;
}

/* #endif */
/* wx-3-end */
::-webkit-scrollbar {
	display: none;
	width: 0;
	height: 0;
	color: transparent;
	display: flex;
	justify-content: center;
}

uni-tabbar .uni-tabbar {
	-webkit-box-shadow: 0 -2px 7px 0 hsla(0, 0%, 89.8%, .5);
	box-shadow: 0 -2px 7px 0 hsla(0, 0%, 89.8%, .5);
	width: 750rpx !important;
	left: auto !important;
	right: auto !important;
}

uni-tabbar {
	right: 0;
	display: flex;
	justify-content: center;
}

uni-page-head .uni-page-head {
	width: 750rpx !important;
	left: auto !important;
	right: auto !important;
}

uni-page-head {
	display: flex !important;
	justify-content: center;
}

body {
	font-family: "PingFangSC-Regular", "Microsoft YaHei", "Helvetica", "Droid Sans", "Arial", "sans-serif";
}

* {
	-webkit-font-smoothing: subpixel-antialiased
}

/* 商品详情页 H5 图文详情强制换行处理 start */
/* #ifdef H5 */
.detail-desc {
	word-break: break-all;
}

/* #endif */
/* 商品详情页 H5 图文详情强制换行处理 end */
.uni-page-head .uni-page-head-ft .uni-page-head-btn .uni-btn-icon {
	font-size: 40rpx;
	margin-right: 15rpx;
}

.uni-popup.spec_model.bottom .uni-transition.fade-out {
	width: 750rpx;
	right: 0;
	left: 0;
	margin: 0 auto;
}

.uni-picker-container,
.uni-picker-toggle,
.uni-picker-toggle {
	width: 750rpx !important;
	left: 0 !important;
	right: 0 !important;
	margin: 0 auto !important;
}

.uni-popup .fade-out {
	width: 750rpx;
	margin: 0 auto;
}

.select_address .content_view {
	width: 750rpx;
	margin: 0 auto;
}

/* 分页loading */
.loading {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 80rpx;
	font-size: 24rpx;
	color: #999;
}

.spinner {
	display: inline-block;
	vertical-align: middle;
	margin-right: 20rpx;
	font-size: 26rpx;
	width: 26rpx;
	height: 26rpx;
	text-align: left;
	border-radius: 50%;
	box-shadow: inset 0 0 0 3rpx rgba(58, 168, 237, .3);
}

.spinner text {
	position: absolute;
	clip: rect(0, 26rpx, 26rpx, 13rpx);
	width: 26rpx;
	height: 26rpx;
	animation: spinner-circle-clipper 1s ease-in-out infinite;
	-webkit-animation: spinner-circle-clipper 1s ease-in-out infinite;
}

@keyframes spinner-circle-clipper {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(180deg);
	}
}

@-webkit-keyframes spinner-circle-clipper {
	0% {
		-webkit-transform: rotate(0deg);
	}

	100% {
		-webkit-transform: rotate(180deg);
	}
}

.spinner text:after {
	position: absolute;
	clip: rect(0, 26rpx, 26rpx, 13rpx);
	width: 26rpx;
	height: 26rpx;
	content: '';
	animation: spinner-circle 1s ease-in-out infinite;
	-webkit-animation: spinner-circle 1s ease-in-out infinite;
	border-radius: 50%;
	box-shadow: inset 0 0 0 3rpx #3aa8ed;
}

@keyframes spinner-circle {
	0% {
		transform: rotate(-180deg);
	}

	100% {
		transform: rotate(180deg);
	}
}

@-webkit-keyframes spinner-circle {
	0% {
		-webkit-transform: rotate(-180deg);
	}

	100% {
		-webkit-transform: rotate(180deg);
	}
}

.loading {
	width: 100%;
	display: flex;
	justify-content: center;
	padding: 10rpx 0;
	opacity: 0;
	transition: all 0.2s;
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	z-index: 4;

	image {
		width: 100rpx;
		height: 56rpx;
	}

	&.show_loading {
		opacity: 1;
	}

}

.scroll_content {
	position: relative;
}

/* 自定义发布按钮样式 */
.uni-tabbar__item .uni-tabbar__icon__diff {
	width: 36px !important;
	height: 36px !important;
	position: relative;
	// top: -15px;
}

.uni-tabbar__item .uni-tabbar__icon__diff img {
	width: 100% !important;
	height: 100% !important;
}
</style>