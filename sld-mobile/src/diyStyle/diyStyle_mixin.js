/**
 * 自定义颜色变量，用于动态变换颜色
 * zoucb 2023-03-24
 * 该方法为mixin混入进入实例里，在需要的页面最外层view 定义:style="mix_diyStyle"即可
 * 使用缓存和Vuex进行管理
 **/
export default {
	data() {
		return {
			diyStyle_obj: {
				// 商城主色 start
				'--color_main': '#F30300',
				'--color_main_bg': 'linear-gradient(-90deg, #F30300 85%,#F30300 100%)',
				'--color_main_bg_zero': 'linear-gradient(0deg, #f41410b3, #f41410e6)',
				'--color_price': '#F30300',
				'--color_vice': '#FF821C',
				'--color_vice_bg': '#FF821C',
				'--color_halo': 'rgba(243,3,0,0.08)',
				'--color_main_chat_bg': 'linear-gradient(90deg, #F41410BF, #F41410)',
				// 积分商城 start
				'--color_integral_main': '#9344FF',
				'--color_integral_main_bg': 'linear-gradient(90deg, #9344ffbf, #884CFF)',
				'--color_integral_vice': '#4E4E61',
				'--color_integral_price': '#DB1107',
				'--color_integral_halo': '#b54cfb17',
				// 积分商城 end
				// 推手 start
				'--color_extral_main': '#FF8622',
				'--color_extral_vice': '#FF3D3D',
				'--color_extral_main_bg': 'linear-gradient(90deg, rgb(246, 119, 16), rgb(244, 92, 71))',
				'--color_extral_main_bgZero': 'linear-gradient(360deg, #F98D30 0%, #F65826 90%)',
				'--color_extral_halo': '#fff',
				// 推手 end
				// 拼团 start
				'--color_spell_main': '#fb2d2d',
				'--color_spell_main_bg': 'linear-gradient(90deg, #FC1C1C 0%, #FF6C00 100%)',
				'--color_spell_vice': '#ff7918',
				'--color_spell_halo': 'rgba(251,45,45,0.1)',
				// 拼团 end
				// 预售 start
				'--color_presell_main': '#fe006d',
				'--color_presell_main_bg': 'linear-gradient(90deg, #ec0093 0%, #ff085b 100%)',
				'--color_presell_vice': '#FF7A18',
				'--color_presell_halo': 'rgba(254,0,109,0.1)',
				// 预售 end
				// 阶梯团 start
				'--color_ladder_main': '#FE9A22',
				'--color_ladder_main_bg': 'linear-gradient(45deg, #ff7a18 0%, #fea10e 100%)',
				'--color_ladder_vice': '#fc1c1c',
				'--color_ladder_halo': 'rgba(254,154,34,0.1)',
				// 阶梯团 end
				// 秒杀 start
				'--color_seckill_main': '#E31719',
				'--color_seckill_main_bg': 'linear-gradient(45deg, #E31719 0%, #E31719 100%)',
				'--color_seckill_vice': '#FF811F',
				'--color_seckill_halo': 'rgba(245, 9, 10, 0.1)',
				'--color_seckill_main_bg_zero': 'linear-gradient(0deg, #e3171900, #e31719)',
				// 秒杀 end
				// 优惠券 start
				'--color_coupon_main': '#FE9A22',
				'--color_coupon_main_bg': 'linear-gradient(45deg, #ff7a18 0%, #fea10e 100%)',
				'--color_coupon_opacity': '#f4bd7a',
				// 优惠券 end
				// 直播和短视频部分 start
				'--color_video_main': '#fc1c1c',
				'--color_video_main_vertical_bg': 'linear-gradient(180deg, #FF2424 0%, rgba(255,99,99,0.2) 90%,rgba(255,255,255) 100%)',
				'--color_video_main_bg': 'linear-gradient(45deg, #ff2020, #ff7d1e)',
				'--color_video_halo': '#ff9e9e',
				// 直播和短视频部分 end

				//
				//商家端配色start
				'--color_store_main': '#2E67F7',
				'--color_store_main_bg': 'linear-gradient(-90deg, #2E67F7 85%, rgba(46,103,247,0.86) 100%)',
				'--color_store_main_bg_zero': 'linear-gradient(0deg, #2E67F7b3, #2E67F7e6)',
				'--color_store_price': '#DB1107',
				'--color_store_vice': '#FF821C',
				'--color_store_vice_bg': '#FF821C',
				'--color_store_halo': 'rgba(46,103,247,0.1)',
				'--color_store_opacity': 'rgba(46,103,247,0.3)',
				'--color_store_main_chat_bg': 'linear-gradient(90deg, #F41410BF, #F41410)',
				//商家端配色end
				//
			},
		}
	},

	computed: {
		//用于全局定义颜色变量
		mix_diyStyle() {
			const {
				x_diyStyle
			} = this.$store.state
			const {
				diyStyle_obj
			} = this
			let true_style = {}
			if (x_diyStyle && typeof x_diyStyle == 'object') {
				true_style = Object.assign({}, diyStyle_obj, x_diyStyle)
			} else if (x_diyStyle && typeof x_diyStyle == 'string') {
				true_style = diyStyle_obj
			}
			let style_s = Object.keys(true_style).map(key => `${key}:${true_style[key]};`)
			return style_s.join('')
		},

		//用于单独取某个颜色值
		diyStyle_var() {
			const {
				x_diyStyle
			} = this.$store.state
			const {
				diyStyle_obj
			} = this
			const true_style = Object.assign({}, diyStyle_obj, x_diyStyle)
			return true_style
		}
	}
}