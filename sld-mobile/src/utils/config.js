module.exports = {
  apiUrl: "https://m-test.shengu.shop/",
  chatUrl: "wss://im-test.shengu.shop",
  imgUrl: "https://sg-mall-test.oss-accelerate.aliyuncs.com/java/bbc/mobile/", //静态资源地址——线上开发
  // imgUrl: '/static/local/',//静态资源地址——本地开发

  uploadMaxSize: 20, //上传最大限制，以M为单位
  curLang: "zh", //当前语言,zh:中文，若为其他语言，需要对应/static/language下面的文件名
  h5GdKey: "b618f24ac6d6644c8c8c525470cf7a2f", //高德web-js key
  h5GdSecurityCode: "c32cf6394971458c91bd13149a7cc11f", //高德web-js 安全密钥
  webApiGdKey: "348689a641ba287bdafa1cf6c5fe1e09", //高德web-api key，切换城市定位需要
  //wx-start
  WxXcxGdKey: "d5f0baf0fc89f309ea963a5f350cbb54", //高德小程序key
  allow_privacy: false, //用户允许隐私授权，默认未允许
  //wx-end
  statShowDebug: false, //是否开启统计的调试
  //app-start
  licenceURL:
    "https://license.vod2.myqcloud.com/license/v2/1257234854_1/v_cube.license", //腾讯云licenceURL
  licenceKey: "0459291d48188ef124b0216240a22ba6", //腾讯云licencekey
  //app-end
};

/** copyright *** slodon *** version-v5.5.2.1 *** date-2024-09-27 ***主版本v5.5.2.2**/
