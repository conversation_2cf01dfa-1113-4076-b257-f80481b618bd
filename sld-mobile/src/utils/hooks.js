import request from './request.js'
import {
	mapState
} from 'vuex'
import store from '../store/index.js'


//获取会员配置接口，带有防抖功能，和vuex共用
const useMemberConfigFunction = () => {
	let timer = null
	let callBackFn = []
	let args_arr = []
	let store_context
	const execAndClear = (obj) => {
		callBackFn.forEach(fn => fn(obj))
		callBackFn = []
		args_arr = []
	}
	const fetchConfig = () => {
		let obj = {}
		request({
			url: 'v3/member/front/memberSetting/getSettingList',
			data: {
				str: args_arr.join(',')
			}
		}).then(res => {
			if (res.state == 200) {
				for (let i in args_arr) {
					obj[args_arr[i]] = res.data[i].imageUrl || res.data[i].value
				}
			}
		}).finally(() => {
			store_context.commit('saveMemberConfig', obj)
			execAndClear(obj)
		})
	}
	const queryConfigRaw = (args, callBack) => {
		callBackFn.push(callBack)
		clearTimeout(timer)
		timer = null
		timer = setTimeout(() => {
			args_arr = Array.from(new Set([...args_arr, ...args]))
			fetchConfig()
		}, 300)
	}
	const queryConfig = (args, context) => {
		store_context = context
		return new Promise((resolve, reject) => {
			queryConfigRaw(args, resolve)
		})
	}
	return queryConfig
}
export const useMemberConfig = useMemberConfigFunction()



