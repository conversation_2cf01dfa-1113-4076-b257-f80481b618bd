import { base64Encrypt, getCurLanguage, isWeiXinBrower } from '@/utils/base.js';
import request from '@/utils/request';
import { sldStatEvent } from '@/utils/stat.js';
import Router from '../router.js';
// #ifdef H5
import { getWxH5Appid } from '@/static/h5/wxH5Auth';
// #endif
const L = getCurLanguage;
export const getClient = () => {
    let client=undefined;
    //#ifdef H5
    client= isWeiXinBrower() ? 'wxbrowser' : 'mbrowser'
    //#endif

    //#ifdef MP-WEIXIN
    client = 'wxxcx'
    //#endif
    return client
}

const client = getClient()



export const oneClickSubmitOrder=(reqInfo)=>{
    const {productId,number}=reqInfo
    let param = {}
    param.url = 'v3/open/front/order/oneClickSubmitOrder'
    param.method = 'POST'
    param.header = {
        'Content-Type': 'application/json'
    }
    param.data = {}
    param.data['productId']=productId;
    param.data['number']=number;
    return request(param)
}


//立即支付事件
export const pay = (payReq) => {

    const { paySn, payMethod,payType, wxBrowerCode, success, fail } = payReq
    console.log('pay:', payReq)

    let param = {}
    param.url = 'v3/business/front/orderPay/doPay'
    param.method = 'POST'
    param.data = {}
    param.data.paySn = paySn
    param.data.payMethod = payMethod // wx/alipay/balance
    param.data.payType = payType  // MINIAPP/JSAPI

    if (payMethod == 'balance') {
        //余额支付
        //支付密码,使用余额时必传
        const passwordVal = payReq?.payReq;
        if (!passwordVal) {
            uni.showModal({
                title: L('温馨提示!'),
                content: L('未设置支付密码'),
                confirmColor: '#FC1E1C',
                confirmText: L('立即设置'),
                success: function (res) {
                    if (res.confirm) {
                        Router.push({
                            path: '/pages/account/managePwd',
                            query: {
                                source: 'set_pay'
                            }
                        })
                    } else if (res.cancel) { }
                }
            })
            return
        }
        param.data.payPwd = base64Encrypt(this.passwordVal)

    } else {
        if (client == 'wxxcx') {
            //微信小程序支付
            uni.login({
                success: (code) => {
                    param.data.code = code.code
                    param.data.codeSource = 1 //用户code来源（JSAPI支付时必填）：1==小程序，2==微信内部浏览器
                    sldStatEvent({
                        behaviorType: 'payApply',
                        paySn: paySn
                    })
                    request(param).then((res) => {
                        if (res.state == 200) {
                            let tmp_data = res.data.payData
                            console.log('wxxcx:payData:requestPayment', tmp_data)
                            if (res.data.actionType == null) {
                                //微信小程序支付
                                uni.requestPayment({
                                    timeStamp: tmp_data.timeStamp,
                                    nonceStr: tmp_data.nonceStr,
                                    package: tmp_data.packageValue ? tmp_data.packageValue : tmp_data.package,
                                    signType: 'MD5',
                                    paySign: tmp_data.paySign,
                                    success: success,
                                    fail: fail
                                })
                            }
                        } else {
                            fail(res.msg)
                        }
                    })
                }
            })
            return false
        } else if (client == 'wxbrowser') {
            //微信h5支付
            if (!wxBrowerCode) {
                getWxH5Appid()
                return false
            } else {
                param.data.code = wxBrowerCode
                param.data.codeSource = 2 //用户code来源（JSAPI支付时必填）：1==小程序，2==微信内部浏览器
            }
        }
    }
    sldStatEvent({
        behaviorType: 'payApply',
        paySn: paySn
    })
    request(param).then((res) => {
        if (res.state == 200) {
            if (payMethod == 'balance') {
                uni.showToast({
                    title: L('支付成功!'),
                    duration: 700
                })
                setTimeout(() => {
                    Router.replace({
                        path: '/pages/order/tradeSuccess',
                        query: {
                            orderSn: paySn,
                            sourceType: 'tradeSuccess'
                        }
                    })
                }, 1000)
            } else {
                let tmp_data = res.data.payData

                if (payMethod == 'paypal') {
                    // #ifdef H5
                    window.location.href = tmp_data
                    // #endif

                }
                if (res.data.actionType == 'redirect') {
                    window.location.href = tmp_data
                } else if (res.data.actionType == null) {
                    if (client == 'wxbrowser') {
                        uni.hideLoading()
                        _this.wxBrowerCode = ''


                        //微信h5支付
                        weiXinBrowerPay({
                            timestamp: tmp_data.timeStamp,
                            nonceStr: tmp_data.nonceStr,
                            package: tmp_data.packageValue ? tmp_data.packageValue : tmp_data.package,
                            signType: 'MD5',
                            paySign: tmp_data.paySign,
                            appId: tmp_data.appId, //此参数可不用
                            success: function (r) {
                                if (r.errMsg == 'chooseWXPay:ok') {
                                    uni.setStorage({
                                        key: 'wxBrowerBack',
                                        data: true
                                    })
                                    setTimeout(() => {
                                        uni.navigateBack({
                                            delta: 2
                                        })
                                    }, 300)
                                } else {
                                    fail('fail')
                                    return false //支付失败后禁止自动支付
                                }
                            },
                            cancel: function (r) {
                                fail('fail')
                                return false //支付失败后禁止自动支付
                            }
                        })


                    } else if (client == 'app') {
                        //APP支付
                        let provider = ''
                        let orderInfo = {}
                        if (payMethod == 'wx') {
                            provider = 'wxpay'
                            orderInfo.appid = tmp_data.appId
                            orderInfo.noncestr = tmp_data.nonceStr
                            orderInfo.package = tmp_data.packageValue ? tmp_data.packageValue : tmp_data.package
                            orderInfo.partnerid = tmp_data.partnerId
                            orderInfo.prepayid = tmp_data.prepayId
                            orderInfo.timestamp = tmp_data.timeStamp
                            orderInfo.sign = tmp_data.sign
                        } else if (payMethod.indexOf('alipay') > -1) {
                            provider = 'alipay'
                        }
                        uni.requestPayment({
                            provider: provider,
                            orderInfo: provider == 'alipay' ? res.data.payData :
                                orderInfo, //订单数据
                            success: function (res) {
                                fail('success')
                            },
                            fail: function (err) {
                                fail('fail')
                            }
                        })
                    }
                } else if (res.data.actionType == 'autopost') {
                    document.write(res.data.payData)
                } else if (res.data.actionType == 'native') {
                    this.showState = true
                    // #ifdef H5
                    window.location.href = res.data.payData
                    // #endif
                    // #ifdef APP-PLUS||MP-WEIXIN
                    Router.push({
                        path: '/pages/index/skip_to',
                        query: {
                            url: res.data.payData
                        }
                    })
                    // #endif
                }
            }
        } else {
            fail(res.msg)
        }
    })
}