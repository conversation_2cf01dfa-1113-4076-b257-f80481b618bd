// #ifdef H5
var jweixin = require('../static/h5/jweixin.js'); //引入微信浏览器分享的jssdk
import h5Copy from '@/static/h5/H5copy.js';
// #endif 
import request from '@/utils/request';
import Router from '../router.js';
import store from '../store/index.js';
import {
	getCurLanguage, weBtoa
} from './base.js';
let L = getCurLanguage

// 绑定分销关系
const bindSpreaderRelation = async (spreaderMemberId) => {
	const { path, query } = Router.$Route
	let fullPath = path
	const newQuery = {
		...query,
		v: process.env.VUE_APP_VERSION
	}

	// 拼接查询参数
	if (newQuery && Object.keys(newQuery).length > 0) {
		const params = Object.keys(newQuery).map(key => `${key}=${encodeURIComponent(newQuery[key])}`).join('&')
		fullPath = `${path}?${params}`
	}
	uni.setStorageSync('last_spreader_u', spreaderMemberId)
	const res = await request({
		url: 'v3/spreader/front/spreaderShare/editRelation',
		method: 'POST',
		data: {
			spreaderMemberId,
			path: fullPath
		}
	})
	if (res.state == 200) {
		console.log('分销绑定成功!', { spreaderMemberId: spreaderMemberId, memberId: store.state.userInfo?.memberId, fullPath: fullPath })
		// 清除缓存
		uni.removeStorageSync('cached_spreader_u')
		return true
	}
	return false
}

export const handleFx = async () => {
	const path = Router.$Route.path
	const query = Router.$Route.query

	let u = query?.u
	u = u && decodeURIComponent(u)

	const hasLogin = store.state.hasLogin;
	const userInfo = store.state.userInfo;
	console.log('handleFx:input', path, query, u)
	console.log('handleFx:login', hasLogin, userInfo)

	if (!hasLogin) {
		// 如果未登录且参数包含u=XX，则缓存到本地
		if (u) {
			uni.setStorageSync('cached_spreader_u', u)
			console.log('handleFx:未登录缓存分销参数:', u)
		}
		return
	}

	const memberId = store.state.userInfo?.memberId
	if (!memberId) {
		console.error('handleFx:未登录获取会员ID失败')
		return
	}

	const _u = weBtoa(memberId);

	// 检查缓存的分销参数
	const cachedU = uni.getStorageSync('cached_spreader_u')

	// 如果当前没有u参数但存在缓存且不等于用户本人，则使用缓存进行关联
	if (!u && cachedU && cachedU !== _u) {
		console.log('[fx]使用缓存分销参数进行关联', cachedU)
		await bindSpreaderRelation(cachedU)
		u = cachedU // 设置u用于后续URL更新
	}
	// 如果登录且检测当前页面存在不等于用户本人的u，则关联
	else if (u && u !== _u) {
		await bindSpreaderRelation(u)
	} else if (u == _u) {
		console.info('相同u参数')
		// 清除缓存（如果存在）
		uni.removeStorageSync('cached_spreader_u')
		return
	}

	const newQuery = {
		...query,
		u: _u
	}
	console.log('handleFx:刷新页面为带分销:old=', u, 'new=', _u, newQuery)
	Router.replace({
		path: path,
		query: newQuery
	})
	return
}


export const getPath = () => {
	const { path, query } = Router.$Route;
	let params = ''

	if (query) {
		for (const key in query) {
			const prefix = params === '' ? '?' : '&'
			params += `${prefix}${key}=${decodeURIComponent(query[key])}`
		}
	}


	let url = ''
	// #ifndef MP
	url = location.href
	// #endif

	// #ifdef MP
	url = path + params;
	// #endif 

	return url
}


export const sgCheckLogin = () => {

	const userInfo = uni.getStorageSync('userInfo')
	if (!store.state.hasLogin && !userInfo) {
		uni.showToast({
			title: L('请登录'),
			icon: 'none',
			duration: 3000,
			success: () => {
				let url = Router.path;
				const query = Router.query;
				uni.setStorageSync('fromurl', {
					url,
					query
				});
				Router.push('/pages/public/login');

			}
		})
		return false;
	}
	return true;
}


/*
 * 判断分页是否还有数据
 */
export function checkPaginationHasMore({
	current,
	pageSize,
	total
}) {
	return current * pageSize < total * 1;
}

// 检查字符串是否全是空格
export function checkSpace(str) {
	return str.trim() == "" ? true : false;
}

/*
 * 短信图形验证码的验证：4位英文字母和数字
 * 验证通过，返回boolean值true，否则返回具体的错误信息
 * @zjf-2021-01-07
 * */
export function checkImgCode(val) {
	let reg = /^[a-zA-Z0-9]{4}$/;
	if (!val) {
		return L("请输入图形验证码");
	} else if (!reg.test(val)) {
		return L("请输入正确的图形验证码");
	} else {
		return true;
	}
}

/* 
 * 替换指定位置的字符
 * str 
 * startIndex 要替换的字符串的开始位置
 * stopIndex  要替换的字符串的结束位置
 * replacetext  指定位置要替换成的内容
 */
export function replaceConByPosition(str, startIndex, stopIndex, replacetext) {
	let target_str = str.substring(0, startIndex - 1) + replacetext + str.substring(stopIndex + 1);
	return target_str;
}

/*
 * 返回一个数字的整数和小数
 * number 需要处理的数据
 * type: 要获取的数据 int 整数  decimal 小数
 */
export function getPartNumber(number, type) {
	let target = '';
	if (number == undefined) {
		return 0;
	}

	number = number.toString();

	let int = number.split('.')[0];

	let decimal = number.split('.')[1] != undefined ? ('.' + number.split('.')[1].slice(0, 2)) : '.00';
	if (decimal.length < 3) {
		decimal += '0';
	}

	if (type == 'int') {
		target = int
	} else if (type == 'decimal') {
		target = decimal
	} else if (type == 'all') {
		target = `${int}${decimal}`
	}
	return target;
}

// 验证输入是否包含表情
export function validatorEmoji(value) {
	let emoji_reg = /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/gi;
	if (!emoji_reg.test(value)) {
		return true;
	} else {
		return false;
	}
}

// 过滤表情
export function replaceEmoji(value) {
	let emoji_reg = /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/gi;
	if (str) {
		return str.replace(emoji_reg, '');
	}
}

// 手机号的验证
export function checkMobile(mobile, name) {
	let regMobile = /(1[3-9]\d{9}$)/;
	if (!mobile) {
		this.$api.msg(name ? `${L('请输入')}${name}!` : L('请输入手机号!'));
		return false;
	} else if (!regMobile.test(mobile)) {
		this.$api.msg(name ? `${L('请输入正确的')}${name}!` : L('请输入正确的手机号!'));
		return false;
	} else {
		return true;
	}
}

//座机和移动手机号码的验证
export function checkTel(mobile, name) {
	let regMobile = /(1[3-9]\d{9}$)/;
	let regTel = /(\d{4}-)\d{6,8}/
	if (!mobile) {
		this.$api.msg(name ? `${L('请输入')}${name}!` : L('请输入手机号!'));
		return false;
	} else if (!regMobile.test(mobile) && !regTel.test(mobile)) {
		this.$api.msg(name ? `${L('请输入正确的')}${name}!` : L('请输入正确的手机号!'));
		return false;
	} else {
		return true;
	}
}

// 6～20位，由英文、数字或符号组成的验证
export function checkPwd(pwd) {
	if (pwd.length < 6) {
		this.$api.msg(L('密码最少6位哦～'));
		return false;
	} else if (pwd.length > 20) {
		this.$api.msg(L('密码最多20位哦～'));
		return false;
	} else if (/[\u4E00-\u9FA5]/g.test(pwd)) {
		this.$api.msg(L('密码不可以有中文哦～'));
		return false;
	} else if (!(/^\S*$/.test(pwd))) {
		this.$api.msg(L('密码中不可以有空格哦～'));
		return false;
	} else {
		return true;
	}
}

//设置cookie，判断首页是否弹出开屏
export function setCookie() {
	uni.setStorage({
		key: 'cookie',
		data: 'cookie'
	});
}
//设置cookie，判断店铺首页是否弹出开屏
export function setStoreIsCookie(vid) {
	uni.setStorage({
		key: 'storeIsCookie' + vid,
		data: 'storeIsCookie' + vid
	});
}
//设置cookie，判断积分商城首页是否弹出开屏
export function setPointIsCookie() {
	uni.setStorage({
		key: 'pointIsCookie',
		data: 'pointIsCookie'
	});
}

// 登录成功的页面跳转
export function loginGoPage() {
	const pages = getCurrentPages();
	let fromurl = uni.getStorageSync('fromurl');
	if (fromurl && fromurl.url) {
		uni.removeStorage({
			key: 'fromurl',
			success: function (res) { }
		});
		if (fromurl.url.indexOf("pages/user/user") > -1 ||
			fromurl.url.indexOf("pages/index/index") > -1 ||
			fromurl.url.indexOf("pages/cart/cart") > -1) {
			Router.pushTab(fromurl.url)
		} else {
			Router.replace({
				path: fromurl.url,
				query: fromurl.query
			})
		}
	} else if (pages.length > 1) {
		Router.back(1)
	} else {
		Router.pushTab('/pages/user/user')
	}
}

export function isTabPage(url) {
	let tabPage = [
		'/pages/user/user',
		'/pages/index/index',
		'/pages/cart/cart',
		'/pages/index/information',
		'/pages/category/category',
	]
	return tabPage.some(u => u.indexOf(url) > -1)
}

// 数字格式化为以w为单位，保留2为小数
export function formatW(num) {
	return num > 10000 ? (num / 10000).toFixed(1) * 1 + 'w' : num;
}

// 邮箱的验证
export function checkEmail(email, name) {
	let reg = /^([a-zA-Z0-9]+[-_.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[-_.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,6}$/;
	if (!email) {
		this.$api.msg(name ? `${L('请输入')}${name}!` : L('请输入邮箱!'));
		return false;
	} else if (!reg.test(email)) {
		this.$api.msg(name ? `${L('请输入正确的')}${name}!` : L('请输入正确的邮箱!'));
		return false;
	} else {
		return true;
	}
}

//验证身份证
export function checkIdentity(value, name) {
	let reg18 = /^[1-9][0-9]{5}(18|19|20)[0-9]{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)[0-9]{3}([0-9]|(X|x))/
	let reg15 = /^[1-9][0-9]{5}[0-9]{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)[0-9]{2}[0-9]/
	if (!value) {
		this.$api.msg(name ? `${L('请输入')}${name}!` : L('请输入身份证号'))
		return false
	} else if (reg18.test(value) || reg15.test(value)) {
		return true;
	} else {
		this.$api.msg(name ? `${L('请输入正确的')}${name}!` : L('请输入正确的身份证号'))
		return false;
	}
}

// 装修地址跳转
export function diyNavTo(val, HorS) {
	if (val.link_type == 'url') {
		//链接地址,只有h5可以跳转外部链接，其他端都不可以
		//ifdef H5
		window.location.href = val.link_value;
		//endif
		//app-1-start



		//app-1-end
		//wx-3-start
		// #ifdef MP
		Router.push({
			path: '/pages/index/skip_to',
			query: {
				url: val.link_value
			}
		})
		// #endif
		//wx-3-end
	} else if (val.link_type == 'keyword') {
		//关键词
		let query = {
			keyword: encodeURIComponent(val.link_value),
			source: 'search'
		}
		if (HorS != 'home') {
			query.storeId = HorS
		}
		Router.push({
			path: '/standard/product/list',
			query
		})
	} else if (val.link_type == 'goods') {
		//商品
		Router.push({
			path: '/standard/product/detail',
			query: {
				productId: val.info.defaultProductId ? val.info.defaultProductId : val.info.productId,
				goodsId: val.info.goodsId
			}
		})
	} else if (val.link_type == 'category') {
		//商品分类
		let query = {
			categoryId: val.info.categoryId
		}
		if (val.info.grade == 3) {
			query.pid = val.info.pid
		}
		Router.push({
			path: '/standard/product/list',
			query
		})
	} else if (val.link_type == 'topic') {
		//专题
		Router.push({
			path: '/pages/index/topic',
			query: {
				id: val.info.decoId ? val.info.decoId : val.info.id
			}
		})
	} else if (val.link_type == 'o2o_topic') {
		//o2o专题
		Router.push({
			path: '/pages/index/topic',
			query: {
				id: val.info.decoId ? val.info.decoId : val.info.id,
				type: 'o2o_topic'
			}
		})
	} else if (val.link_type == 'voucher_center') {
		if (!this.hasLogin) {
			setFromUrl({
				url: '/standard/coupon/couponCenter'
			})
			this.$emit('needLogin')
		} else {
			this.$Router.push('/standard/coupon/couponCenter')
		}
	} else if (val.link_type == 'brand_home') {
		this.$Router.push('/pages/public/brand')
	} else if (val.link_type == 'seckill') {
		this.$Router.push({
			path: '/standard/seckill/seckill',
			query: {
				seckillId: val.info.seckillId
			}
		})
	} else if (val.link_type == 'rank') {
		this.$Router.push({
			path: '/standard/rank/aggr'
		})
	} else if (val.link_type == 'spell_group') {
		this.$Router.push('/standard/pinGroup/index/index')
	} else if (val.link_type == 'ladder_group') { //阶梯团
		this.$Router.push('/standard/ladder/index/index')
	} else if (val.link_type == 'presale') { //预售入口页
		this.$Router.push('/standard/presale/index/list')
	} else if (val.link_type == 'point') { //积分商城首页
		this.$Router.push('/standard/point/index/index')
	} else if (val.link_type == 'svideo_center') { //短视频列表
		this.$Router.push('/extra/svideo/svideoList')
	} else if (val.link_type == 'live_center') { //直播列表
		this.$Router.push('/extra/live/liveList')
	} else if (val.link_type == 'spreader_center') { //推手中心
		if (!this.hasLogin) {
			setFromUrl({
				url: '/extra/tshou/index/index'
			})
			this.$emit('needLogin')
		} else {
			this.$Router.push('/extra/tshou/index/index')
		}

	} else if (val.link_type == 'live') { //直播播放页面
		livePlayApp({
			live_id: val.link_value
		})
	} else if (val.link_type == 'svideo') { //短视频播放页面
		videoPlayNav({
			video_id: val.info.videoId,
			curLabelId: val.info.labelId,
			author_id: val.info.authorId
		})
	} else if (val.link_type == 'draw') {
		if (!this.hasLogin) {
			setFromUrl({
				url: '/standard/lottery/detail',
				query: {
					drawId: val.info.drawId
				}
			})
			this.$emit('needLogin')
		} else {
			this.$Router.push({
				path: '/standard/lottery/detail',
				query: {
					drawId: val.info.drawId
				}
			})
		}
	} else if (val.link_type == 'rank') {
		this.$Router.push('/standard/rank/aggr')
	} else if (val.link_type == 'sign_center') {
		if (!this.hasLogin) {
			setFromUrl({
				url: '/standard/signIn/signIn'
			})
			this.$emit('needLogin')
		} else {
			this.$Router.push('/standard/signIn/signIn')
		}
	} else if (val.link_type == 'store_list') { //店铺街
		this.$Router.push('/standard/store/list')
	} else if (val.link_type == 'store') {
		this.$Router.push(`/standard/store/shopHomePage?vid=${info.storeId}`)
	}
}

//登录时存储当前页面的路径
export function setFromUrl(fromurl) {
	if (!fromurl) {
		// 如果没有传入参数，则使用当前路由信息
		const url = Router.$Route.path;
		let query = { ...Router.$Route.query };

		// 如果路径不存在v则添加v=版本
		if (!query.v) {
			query.v = process.env.VUE_APP_VERSION;
		}

		// 如果路径不存在t则添加t=时间戳
		if (!query.t) {
			query.t = Date.now();
		}

		fromurl = { url, query };
		console.log('set fromurl from current route', url, query);
	}

	uni.removeStorageSync('fromurl');
	uni.setStorageSync('fromurl', fromurl);
}

// 装修类型判断
export function decoType(info) {
	let deco_obj = {}
	deco_obj.tupianzuhe = []
	// deco_obj.gonggao = []
	info.map(item => {
		if (item.type == 'more_tab') { //tab切换
			deco_obj.more_tab = {
				border_radius: item.border_radius,
				data: item.data,
			}
		} else if (item.type == 'lunbo') { //轮播图
			deco_obj.lunbo = {
				is_show: item.is_show,
				data: item.data
			}
		} else if (item.type == 'gonggao') { //公告
			if (item.show_style == 'one') {
				deco_obj.gonggao1 = {
					show_style: item.show_style,
					text: item.text,
					is_show: item.is_show,
				}
			} else {
				deco_obj.gonggao2 = {
					show_style: item.show_style,
					text: item.text,
					is_show: item.is_show,
				}
			}
		} else if (item.type == 'top_cat_nav') { //顶部轮播图
			deco_obj.top_cat_nav = {
				swiper_bg_style: item.swiper_bg_style,
				data: item.data
			}
		} else if (item.type == 'nav') { //导航
			deco_obj.nav = {
				is_show: item.is_show,
				data: item.data,
				icon_set: item.icon_set
			}
		} else if (item.type == 'kefu') { //客服
			deco_obj.kefu = {
				is_show: item.is_show,
				tel: item.tel,
				text: item.text
			}
		} else if (item.type == 'fuwenben') { //富文本
			deco_obj.fuwenben = {
				is_show: item.is_show,
				text: item.text
			}
		} else if (item.type == 'tupianzuhe') { //图片组合
			deco_obj.tupianzuhe.push({
				is_show: item.is_show,
				sele_style: item.sele_style,
				width: item.width,
				height: item.height,
				data: item.data,
			})
		} else if (item.type == 'dapei') { //搭配
			deco_obj.dapei = {
				is_show: item.is_show,
				dapei_desc: item.dapei_desc,
				dapei_img: item.dapei_img,
				data: item.data,
				width: item.width,
				height: item.height
			}
		} else if (item.type == 'fzx') { //辅助线
			deco_obj.fzx = {
				color: item.color,
				is_show: item.is_show,
				lrmargin: item.lrmargin,
				tbmargin: item.tbmargin,
				type: item.val
			}
		} else if (item.type == 'tuijianshangpin') { //推荐商品
			deco_obj.recommond_goods = {
				border_style: item.border_style,
				data: item.data,
				goods_margin: item.goods_margin,
				is_show: item.is_show,
				page_margin: item.page_margin,
				show_style: item.small,
				text_align: item.text_align,
				text_style: item.normal,
				isshow_sales: item.isshow_sales
			}
		} else if (item.type == 'fzkb') { //辅助空白
			deco_obj.fzkb = {
				color: item.color,
				is_show: item.is_show,
				text: item.text,
			}
		}
	})
	return deco_obj
}


//#ifdef H5
/** 
 * 微信浏览器里面的分享功能
 * type 分享类型  1 为微信好友分享 2为微信朋友圈分享
 * shareData  分享数据数组 里面的参数分别如下：
 * 		title: '', // 分享标题
 * 		desc: '', // 分享描述
 * 		link: '', // 分享链接
 * 		imgUrl: '', // 分享图片
 * 		type: '', // 分享类型,music、video或link，不填默认为link
 * 		dataUrl: '', // 如果type是music或video，则要提供数据链接，默认为空
 * isTs 是否推手模块，默认false
 * @zjf-2020-11-06
 */
export function weiXinBrowerShare(type, shareData) {
	let tar_url = process.env.VUE_APP_API_URL + 'v3/member/front/login/wxjsConf?source=1&method=weiXinBrowerShare';

	uni.request({
		url: tar_url,
		method: 'GET',
		data: {
			url: encodeURIComponent(location.href)
		},
		success(res) {
			``
			let data = res.data;
			jweixin.config({
				debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
				appId: data.data.appId, // 必填，公众号的唯一标识
				timestamp: data.data.timestamp, // 必填，生成签名的时间戳
				nonceStr: data.data.nonceStr, // 必填，生成签名的随机串
				signature: data.data.signature, // 必填，签名
				jsApiList: ["updateAppMessageShareData", "updateTimelineShareData", "getLocation"] // 必填，需要使用的JS接口列表
			});
			jweixin.ready(function () {
				if (type == 1) {
					//获取“分享给朋友”按钮点击状态及自定义分享内容接口
					jweixin.updateAppMessageShareData({
						title: shareData.title != undefined ? shareData.title : '', // 分享标题
						desc: shareData.desc != undefined ? shareData.desc : '', // 分享描述
						link: shareData.link != undefined ? shareData.link :
							'', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
						imgUrl: shareData.imgUrl, // 分享图标
						type: '', // 分享类型,music、video或link，不填默认为link
						dataUrl: '', // 如果type是music或video，则要提供数据链接，默认为空
						success: function () {

						}
					})
				} else if (type == 2) {
					//获取“分享到朋友圈”按钮点击状态及自定义分享内容接口
					jweixin.updateTimelineShareData({
						title: shareData.title != undefined ? shareData.title : '', // 分享标题
						link: shareData.link != undefined ? shareData.link :
							'', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
						imgUrl: shareData.imgUrl, // 分享图标
						success: function () {
							// 设置成功
						}
					})

				}
			})
		}
	})
}

/** 
 * 用于页面启动时配置微信分享参数
 * 
 * @zcb-2021-11-17
 */
export async function WXBrowserShareStart() {

	let tar_url = process.env.VUE_APP_API_URL + 'v3/member/front/login/wxjsConf?source=1&method=WXBrowserShareStart';
	let hrefUrl = encodeURIComponent(location.href)
	const tmp = await uni.request({
		url: tar_url,
		method: 'GET',
		data: {
			url: hrefUrl
		}
	})
	const res = tmp && tmp.length === 2 && tmp[1] || {};
	if (res) {
		let data = res.data;
		if (data.data) {
			// #ifdef H5
			console.log('微信SDK配置init:', hrefUrl, data.data)
			return jweixin.config({
				debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
				appId: data.data.appId, // 必填，公众号的唯一标识
				timestamp: data.data.timestamp, // 必填，生成签名的时间戳
				nonceStr: data.data.nonceStr, // 必填，生成签名的随机串
				signature: data.data.signature, // 必填，签名
				jsApiList: ["updateAppMessageShareData",
					"updateTimelineShareData",
					"getLocation"
				] // 必填，需要使用的JS接口列表
			});
			//#endif
		}
	}
	return
}


/** 
 * 用于需要有微信分享的页面的方法
 * 
 * @zcb-2021-11-17
 */
export async function WXBrowserShareThen(type, shareData) {
	console.log('WXBrowserShareThen', type, shareData)
	await WXBrowserShareStart()
	jweixin.error(function (res) {
		console.error('微信 JS-SDK 配置失败:', res.errMsg);
	});
	jweixin.ready(function () {
		console.log('j微信 JS-SDK 准备完成')
		let hrefUrl = encodeURIComponent(location.href)
		console.log('shareLink:', hrefUrl)
		if (type == 1) {
			//获取“分享给朋友”按钮点击状态及自定义分享内容接口
			jweixin.updateAppMessageShareData({
				title: shareData.title != undefined ? shareData.title : '', // 分享标题
				desc: shareData.desc != undefined ? shareData.desc : '', // 分享描述
				link: hrefUrl, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
				imgUrl: shareData.imgUrl ? shareData.imgUrl : '123.png', // 分享图标
				type: '', // 分享类型,music、video或link，不填默认为link
				dataUrl: '', // 如果type是music或video，则要提供数据链接，默认为空
				success: function () {
					console.log('微信分享成功')
				},
				fail: function (err) {
					console.error('微信分享失败', err)
				}
			})
		} else if (type == 2) {
			//获取“分享到朋友圈”按钮点击状态及自定义分享内容接口
			jweixin.updateTimelineShareData({
				title: shareData.title != undefined ? shareData.title : '', // 分享标题
				link: hrefUrl, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
				imgUrl: shareData.imgUrl ? shareData.imgUrl : '123.png', // 分享图标
				success: function () {
					// 设置成功
					console.log('微信分享成功')
				},
				fail: function (err) {
					console.error('微信分享失败', err)
				}
			})

		}
	})
}




/** 
 * 获取浏览器地址参数
 * variable 为参数名，存在的话返回具体值，否则返回false
 * 
 * @zjf-2020-11-17
 */
export function getQueryVariable(variable) {
	let query = window.location.search.substring(1);
	let vars = query.split("&");
	for (let i = 0; i < vars.length; i++) {
		let pair = vars[i].split("=");
		if (pair[0] == variable) {
			return pair[1];
		}
	}
	return false;
}

/** 
 * 微信浏览器里面的支付
 * payData  支付数据数组 里面的参数分别如下：
 * 		timestamp: '',  // 支付签名时间戳，注意微信jssdk中的所有使用timestamp字段均为小写。但最新版的支付后台生成签名使用的timeStamp字段名需大写其中的S字符
 * 		nonceStr: '', // 支付签名随机串，不长于 32 位
 * 		package: '', // 统一支付接口返回的prepay_id参数值，提交格式如：prepay_id=***）
 * 		signType: '', // 签名方式，默认为'SHA1'，使用新版支付需传入'MD5'
 * 		paySign: '', // 支付签名
 * 		appId: '', 
 * 		success: function (res) { // 支付成功后的回调函数 }
 *		fail: function (res) { // 失败时执行的回调函数 }
 * 		complete: function (res) { // 接口调用完成时执行的回调函数，无论成功或失败都会执行 }
 * 		cancel: function (res) { // 用户点击取消时的回调函数，仅部分有用户取消操作的api才会用到 }
 * 		trigger: function (res) { // 监听Menu中的按钮点击时触发的方法，该方法仅支持Menu中的相关接口 }
 * 
 * @zjf-2020-11-06
 */
export function weiXinBrowerPay(payData) {
	let tar_url = process.env.VUE_APP_API_URL + 'v3/member/front/login/wxjsConf?source=1&method=weiXinBrowerPay';
	uni.request({
		url: tar_url,
		method: 'GET',
		data: {
			url: encodeURIComponent(location.href)
		},
		success(res) {
			let data = res.data;
			// #ifdef H5
			jweixin.config({
				debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
				appId: data.data.appId, // 必填，公众号的唯一标识
				timestamp: data.data.timestamp, // 必填，生成签名的时间戳
				nonceStr: data.data.nonceStr, // 必填，生成签名的随机串
				signature: data.data.signature, // 必填，签名
				jsApiList: ['chooseWXPay', 'scanQRCode'] // 必填，需要使用的JS接口列表
			});
			jweixin.ready(function () {
				jweixin.chooseWXPay(payData);
			})
			//#endif
		}
	})
}
//#endif





/** 
 * 通用提示
 * con  String  提示的内容,无特殊要求的话可不传
 * 
 * @zjf-2020-11-18
 */
export function sldCommonTip(con = L('该功能在升级中～')) {
	uni.showToast({
		title: con,
		icon: 'none',
	});
}
export function formatPercent(val) {
	return val.substring(0, val.length - 1)
}


/** 
 * 获取用户登录模块的终端类型
 * 
 * @zjf-2020-11-23
 */
export function getLoginClient() {
	let client = 1;
	//app-2-start



	//app-2-end
	//wx-1-start
	//#ifdef MP-WEIXIN
	client = 6;
	//#endif
	//wx-1-end
	//#ifdef H5
	client = 2;
	//#endif









	return client;
}

/**
 * 微信登录要传的client值
 * */
export function wxLoginClient() {
	let client = 1;
	//app-3-start



	//app-3-end
	//wx-2-start
	//#ifdef MP-WEIXIN
	client = 2;
	//#endif
	//wx-2-end
	//#ifdef H5
	client = 1;
	//#endif
	return client;
}

/**
 * 防止用户多此点击触发事件
 * @ljp - 2021-2-7
 * */
export function frequentyleClick(fn) {
	let that = this;
	if (that.onOff) {
		that.onOff = false;
		fn();
		setTimeout(() => {
			that.onOff = true;
		}, 1500)
	} else {
		//如果一直走else，可能是你没有页面的data下面挂载onOff = true; 不然一直会走else
	}
}
/**
 * h5端页面返回处理，刷新后返回到首页
 * @ww - 2021-2-21
 * */
export function back(fn) {
	console.info('调用 back')
	// #ifdef H5
	const pages = getCurrentPages()

	if (pages.length > 1) {
		Router.back(1)
		return;
	} else {
		//重新定向跳转页面
		Router.replaceAll('/pages/index/index')
	}
	return;
	// #endif
}

/*
 * 判断是否显示聊天页面的时间,2条消息之间间隔超过3分钟显示
 * 返回Boolean类型
 * preMsgTime 上一条消息的发送时间，curMsgTime该条消息的发送时间
 * @zjf-2021-03-05
 * */
export function isShowTime(preMsgTime, curMsgTime) {
	let res = false;
	let cur, pre
	if (typeof curMsgTime == "number") {
		cur = curMsgTime
	} else {
		cur = new Date(curMsgTime.toString().replace(/-/g, '/')) * 1
	}
	pre = new Date(preMsgTime.toString().replace(/-/g, '/')) * 1
	if (cur - pre > 3 * 60 * 1000) {
		res = true;
	}


	return res;
}

/*
 * 格式化聊天时间
 * 返回格式化后的数据，字符串类型
 * time 时间戳 13位
 * @zjf-2021-03-05
 * */
export function formatChatTime(time) {
	if (time.toString().indexOf('-') == -1 && typeof (time) == 'number') {
		return formatNorm(time);
	} else {
		return format(new Date(time.toString().replace(/-/g, '/')), 'yyyy年MM月dd日 hh:mm');
	}

}

export function formAdd0(m) {
	return m < 10 ? '0' + m : m;
}

/** 
 * 时间戳转标准时间
 * timeStep:Number
 */
export function formatNorm(timeStep) {
	let time = new Date(parseInt(timeStep));
	let y = time.getFullYear();
	let m = time.getMonth() + 1;
	let d = time.getDate();
	let h = time.getHours();
	let mm = time.getMinutes();
	let s = time.getSeconds();
	let newTime = y + '-' + formAdd0(m) + '-' + formAdd0(d) + ' ' + formAdd0(h) + ':' + formAdd0(mm) + ':' + formAdd0(
		s);
	return format(new Date(newTime.toString().replace(/-/g, '/')), 'yyyy年MM月dd日 hh:mm');
}

/** 
 * 格式化时间,转时间为字符串
 * date:Date
 * fmt:String
 */
export function format(date, fmt) {
	let o = {
		"y+": date.getFullYear(), //年
		"M+": date.getMonth() + 1, //月份
		"d+": date.getDate(), //日
		"h+": date.getHours(), //小时
		"m+": date.getMinutes(), //分
		"s+": date.getSeconds(), //秒
		"q+": Math.floor((date.getMonth() + 3) / 3), //季度
		"S": date.getMilliseconds() //毫秒
	};
	if (/(y+)/.test(fmt)) {
		fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
	}
	for (let k in o) {
		if (new RegExp("(" + k + ")").test(fmt)) {
			fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
		}
	}
	return fmt;
}

/** 
 * 基于uniapp.uploadFile的多文件上传，支持 跨端
 * fileArray:Array
 * params:Object
 * success:Function
 * @lijm-2021-06-15
 */
export function multifileUpload({
	fileArray,
	params,
	success,
	fail
}) {
	if (!fileArray || !params) {
		return '';
	}
	let promiseArray = [];
	fileArray.forEach((item, index) => {
		params.filePath = item;
		// params.formData.file = item;
		//将每个请求封装成promise
		promiseArray[index] = uni.uploadFile(params)
	})
	if (success && typeof success === 'function') {
		//当所有请求完成后，调用success回调，返回请求后的reponse
		Promise.all(promiseArray).then((res) => {
			let data = []
			res.forEach((item, index) => {

				if (JSON.parse(item[1].data).state === 200) {
					data.push(JSON.parse(item[1].data))
				}

			})
			if (data.length) {
				success(data);
			}
			//如果有失败回调，则返回失败信息
			if (fail && typeof fail === 'function') {
				let errData = '';
				let tempStatus = res.some((item) => {


					if (JSON.parse(item[1].data).state != 200) {
						errData = JSON.parse(item[1].data);
						return true;
					}


					return false;
				})
				fail(errData || {
					status: 200,
					msg: 'no errors'
				})
			}
		})
	}

}

/*
 * 富文本内容反转义（接口返回的富文本内容经过了转义，导致内容无法展示，所以需要反转义）
 * @param {String} str 富文本内容
 * @zjf-2022-01-07
 * */
export function quillEscapeToHtml(str) {
	if (str != undefined) {
		const arrEntities = {
			'lt': '<',
			'gt': '>',
			'nbsp': ' ',
			'amp': '&',
			'quot': '"'
		}
		return str.replace(/&(lt|gt|nbsp|amp|quot);/ig, function (all, t) {
			return arrEntities[t]
		}).replace(/\<img/g, "<img class='rich_text_image'")
	} else {
		return '';
	}
}


/*
 * 源数组和新数组进行某个key值的对比，新数组里出现源数组相比新的值则加入源数组
 * @param {Array} source 源数组
 * @param {Array} target 新数组
 * @param {str} key 属性值
 * @zcb-2021-06-06
 * */
export function arrCom(source, target, key) {

	let res = []

	if (source.length == 0 && target.length > 0) {
		return target
	} else if (target.length == 0 && source.length > 0) {
		return []
	} else {
		target.forEach(item => {
			let idx = source.findIndex(d => d[key] == item[key])
			if (idx < 0) res.push(item)
		})

		return res
	}
}

/*
 * h5获取路由的参数进行取值
 * @param {String} str 地址字符串
 * @param {String} key 要取的key
 * @zcb-2022-03-02
 * */
export function getSceneParam(str, key) {
	let res = '';
	let param_str = str.split('?');

	if (param_str[1]) {
		let param_array = param_str[1].split('&');

		for (let i in param_array) {
			let tmp_data = param_array[i].split('=');

			if (tmp_data[0] == key) {
				res = tmp_data[1];
				break;
			}
		}
	}

	return res;
}


/*
 * 统一提示方便全局修改
 * @param {title} str 提示语
 * @param {duration} number 持续时间
 * @param {mask} Boolean 是否遮罩
 * @param {icon} String 图标类型
 * */
export const msg = (title, icon = 'none', duration = 1500, mask = true) => {
	if (Boolean(title) === false) {
		return;
	}

	uni.showToast({
		title,
		duration,
		mask,
		icon
	});
}

//获取上一页信息
export const prePage = () => {
	let pages = getCurrentPages();
	let prePage = pages[pages.length - 2];
	return prePage?.$vm;
}

/**
 * @zoucb 2023-05-12
 * 集中判断 跳转短视频播放页
 * app和替他端分开处理
 **/
export function videoPlayNav(arg) {
	Router.push({
		path: '/extra/svideo/svideoPlay',
		query: {
			...arg
		}
	})

}

/**
 * @zoucb 2023-05-16
 * 集中判断 跳转直播播放页
 * app和替他端分开处理
 **/
export function livePlayNav(arg, type = 'push') {
	Router[type]({
		path: '/extra/live/livePlay',
		query: {
			...arg
		}
	})

}

//当未登录去登录或者注册时，缓存里的购物车数据带进接口里
export const getUnLoginCartParam = () => {
	let local_cart_list = uni.getStorageSync('cart_list')
	let cartInfo = []
	if (local_cart_list && Object.keys(local_cart_list).length) {
		local_cart_list.storeCartGroupList.map((item) => {
			item.promotionCartGroupList.map((item1) => {
				item1.cartList.map((item2) => {
					cartInfo.push({
						productId: item2.productId,
						buyNum: item2.buyNum
					})
				})
			})
		})
	}
	return cartInfo.length > 0 ? JSON.stringify(cartInfo) : null
}


//获取图形验证码
export function fetchImgCode() {
	return new Promise((resolve, reject) => {
		request({
			url: 'v3/captcha/common/getCaptcha'
		}).then((res) => {
			if (res.state == 200) {
				resolve({
					codeImg: 'data:image/png;base64,' + res.data.captcha,
					imgCodeKey: res.data.key
				})
			} else {
				resolve({})
				api.msg(res.data.msg);
			}
		})
	})
}


export async function getSaveAmount(cityCode, productId, num) {
	const result = await store.dispatch('getMemberConfig', ['super_is_enable'])
	let data = {
		productId,
		num
	}
	cityCode && (data.cityCode = cityCode)
	return new Promise((resolve, reject) => {
		if (result.super_is_enable != 1) {
			resolve({})
		}
		request({
			url: 'v3/goods/front/goods/saveAmount',
			data
		}).then(res => {
			if (res.state == 200) {
				resolve(res.data)
			} else {
				resolve({})
				msg(res.msg)
			}
		})
	})
}


export const preventMutiClick = (targetFn, instance) => {
	if (!targetFn || !(typeof targetFn == 'function')) {
		throw new Error('targetFn is not a function')
	}
	let name = targetFn.name || 'anynomous'
	let str = `${name.replace(/[\s]*/g, '')}_isPreventClick`
	const continueExec = () => instance[str] = false
	if (!instance[str]) {
		instance[str] = true
		targetFn(continueExec)
	}
}

//防抖函数
export function deBounce(fn, delay = 500, immediate) {
	let timer = null,
		immediateTimer = null;

	return function () {
		let args = arguments,
			context = this;

		// 第一次触发
		if (immediate && !immediateTimer) {
			fn.apply(context, args);
			//重置首次触发标识，否则下个周期执行时会受干扰
			immediateTimer = setTimeout(() => {
				immediateTimer = null;
			}, delay);
		}
		// 存在多次执行时，重置动作需放在timer中执行；
		if (immediateTimer) clearTimeout(immediateTimer);
		if (timer) clearTimeout(timer);

		timer = setTimeout(() => {
			fn.apply(context, args);
			timer = null;
			immediateTimer = null;
		}, delay);
	}
}

//拨打电话
export function phoneCall(phoneNumber) {
	uni.makePhoneCall({
		phoneNumber
	})
}

//深度克隆
export function diyClone(source, map, except) {
	except = except || []
	map = map || {}
	let target = {}
	for (let key of Object.keys(source)) {
		if (except.includes(key)) {
			//
		} else if (map[key]) {
			let remapKey = map[key]
			target[remapKey] = source[key]
		} else {
			target[key] = source[key]
		}
	}
	return JSON.parse(JSON.stringify(target))
}

//复制函数
export function copy(text) {
	let col = text
	// #ifdef H5
	const result = h5Copy(col)
	if (result === false) {
		this.$api.msg(this.$L('不支持'))
	} else {
		this.$api.msg(this.$L('已复制到剪贴板'))
	}
	// #endif

	// #ifdef APP-PLUS || MP-WEIXIN
	uni.setClipboardData({
		data: col,
		success: function () {
			this.$api.msg(this.$L('已复制到剪贴板'))
		}
	});
	// #endif
}



//获取购物车数量
export const getCartNum = () => {
	let promiseList = [
		request({ url: 'v3/business/front/cart/cartNum' })
	]
	if (store.state.hasLogin) {
		return new Promise(async (resolve, reject) => {
			const resList = await Promise.all(promiseList)
			if (resList.every(i => i && i.state == 200)) {
				let total = resList.reduce((prev, current) => prev + current.data, 0)
				if (total > 0) {
					uni.setTabBarBadge({
						index: 3,
						text: total.toString()
					})
				} else {
					uni.hideTabBarRedDot({
						index: 3
					})
				}

				resolve(resList.reduce((prev, current) => prev + current.data, 0))
			} else {
				reject('购物车数量获取异常')
			}
		})
	} else {

		let cartNum = 0

		let cart_list = uni.getStorageSync('cart_list')
		if (cart_list && cart_list.storeCartGroupList) {
			cart_list.storeCartGroupList.map((item) => {
				item.promotionCartGroupList.map((item1) => {
					item1.cartList.map((item2) => {
						cartNum++
					})
				})
			})
		}

		if (cartNum > 0) {
			uni.setTabBarBadge({
				index: 3,
				text: cartNum.toString()
			})
		} else {
			uni.hideTabBarRedDot({
				index: 3
			})
		}

		return Promise.resolve(cartNum)
	}
}

