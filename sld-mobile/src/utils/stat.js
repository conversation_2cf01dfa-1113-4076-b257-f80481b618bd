//封装的关于统计的方法
import { base64Encode, getCurLanguage, isWeiXinBrower, waiting } from './base.js';
import request from './request';
let L = getCurLanguage
//wx-1-start
// #ifdef MP-WEIXIN
var amapFile = require('../static/mp-qq/libs/amap-wx.js');
// #endif
//wx-1-end
// #ifdef H5
var jweixin = require('../static/h5/jweixin.js'); //引入微信浏览器分享的jssdk
// #endif 
/** 
 * 初始化统计
 * @params showDebug Boolean 是否开启统计日志，默认false 不开启
 * @zjf-2021-06-27
 */

// #ifdef H5
import AMapLoader from '@amap/amap-jsapi-loader';
// #endif

let getLocationPromise = null

const {resume,wait,start} = waiting()

export async function initStat(showDebug = false, initStatCommonProperty) {
	uni.setStorage({
		key: 'sldStatShowDebug',
		data: showDebug,
	});
	let uuid = null
	//获取udid
	if(uni.getStorageSync('sldStatCommonProperty').uuid){
		uuid = uni.getStorageSync('sldStatCommonProperty').uuid
	}else{
		uuid = getUUID();
	}
	//wx-2-start
	// #ifdef MP-WEIXIN
	//获取openid
	start()
	let code = await uniLogin();
	let openid = await getOpenid(code)
	uuid = openid;
	// #endif
	//wx-2-end
	//获取位置信息
	initStatCommonProperty = {
		...initStatCommonProperty,
		uuid: uuid,
	}
	updateStatCommonProperty(initStatCommonProperty)
	
	// #ifdef MP
	resume()
	if (!process.env.VUE_APP_ALLOW_PRIVACY && wx.getPrivacySetting) {
		return false
	};
	// #endif
	
	getLocation()
}

/** 
 * 获取微信小程序的code
 * @zjf-2021-06-28
 */
export function uniLogin() {
	let code = '';
	return new Promise(func => {
		uni.login({
			provider: 'weixin',
			success: res => {
				func(res.code)
			},
			fail() {
				func(code)
			}
		})
	})
}

/** 
 * 获取微信小程序的openid
 * @params code String uni.login获取到的code
 * @zjf-2021-06-27
 */
export async function getOpenid(code) {
	let openId = '';
	if (!code) {
		return openId;
	}
	let params = {
		url: 'v3/member/front/login/wechat/getOpenId',
		method: 'POST',
		data: {
			code: code,
			source: 2
		}
	}
	await request(params).then(res => {
		if (res.state == 200) {
			openId = res.data;
		}
	})
	return openId;
}

/** 
 * 设置/更新统计的公共属性
 * @params data Object 要更新的属性数据
 * @zjf-2021-06-27
 */
export function updateStatCommonProperty(data) {
	let target = {};
	try {
		const value = uni.getStorageSync('sldStatCommonProperty');
		if (value) {
			target = value;
		}
	} catch (e) {}
	target = {
		...target,
		...data
	}; //更新或者新增统计的公共属性
	uni.setStorageSync('sldStatCommonProperty', target);
}

/** 
 * 同步获取指定key对应的内容
 * @params key 指定的缓存key
 * @zjf-2021-06-27
 */
export function getStatStorage(key) {
	let target = {};
	try {
		const value = uni.getStorageSync(key);
		if (value) {
			target = value;
		}
	} catch (e) {}
	return target;
}

/** 
 * 获取uuid
 * 如：1624819897644-1389918-0ed8161319cedb-22991203
 * Math.random().toString(16).replace('.', '')：0～1的随机数以十六进制显示，并去掉小数点，如：0.f03fb618bf531，并去掉小数点
 * @zjf-2021-06-27
 */
export function getUUID() {
	return "" + Date.now() + '-' + Math.floor(1e7 * Math.random()) + '-' + Math.random().toString(16).replace('.', '') +
		'-' + String(Math.random() * 31242).replace('.', '').slice(0, 8);
}

/** 
 * 获取地理位置信息,各个终端分别获取
 * @zjf-2021-06-27
 */
export async function getLocation() {
	let locationData = {
		cityCode: '', //城市编码
		cityName: '', //城市名称
		location: '', //经纬度，英文逗号分隔
		provinceCode: '', //	省份编码
		provinceName: '', //	省份名称
	};
	// #ifdef H5
	locationData = await getH5Location(locationData);
	// #endif 
	










	
	// #ifdef MP-WEIXIN
	locationData = await getWxXcxLocation(locationData);
	// #endif 
	
	uni.setStorageSync('location', locationData)
	
	return locationData;
}

/** 
 * 获取H5的地理位置
 * @zjf-2021-06-28
 */

//初始化高德地图实例
export function loadAmap(){
	//自2021年12月02日升级，升级之后所申请的 key 必须配备安全密钥 一起使用
	//配置安全密钥的固定写法
	window._AMapSecurityConfig = {
	    securityJsCode:process.env.VUE_APP_G,
	}
	return new Promise((res,rej)=>{
		AMapLoader.load({
			key: process.env.VUE_APP_H5_GD_SECURITY_CODE, // 申请好的Web端开发者Key，首次调用 load 时必填
			// version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
			plugins: ['AMap.Geocoder', 'AMap.Geolocation','AMap.Adaptor'], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
		}).then(AMap=>{
			res(AMap)
		}).catch(rej)
	})
}

export function getH5Location(locationData) {
	
	if(getLocationPromise){
		return getLocationPromise
	}
	
	getLocationPromise = new Promise((func) => {
		if (!isWeiXinBrower()) {   
			loadAmap().then(AMap=>{
				AMap.plugin('AMap.Geolocation', function () {
					let geolocation = new AMap.Geolocation({
						enableHighAccuracy: false, //是否使用高精度定位，默认:true
						timeout: 15000, //超过15秒后停止定位，默认：无穷大
					});
					geolocation.getCurrentPosition((status, res)=>{
						if(status=='complete'){
							locationData.location = res.position.lng + ',' + res.position.lat;
							if(res==undefined||res.addressComponent==undefined){
								console.warn('获取地理位置信息为空',res);
							}else{
								let {province,city,adcode,township,street} = res.addressComponent
								locationData.provinceName = province.substring(0, res.addressComponent.province.length - 1);
								locationData.cityName = city || province;
								locationData.adcode = adcode.slice(0, 3)
								locationData.cityCode = adcode.slice(0, 4) + '00'
								locationData.regionData = {
									name:township,
									desc:street
								}
							}
							
							func(locationData);
						}else{
							console.info('获取地理位置信息出错，出错信息为：', res);
							func(locationData);
						}
					});
				})
			}).catch(()=>func(locationData));
		} else {
			//微信h5获取位置信息
			jweixin.ready(function() {
				jweixin.getLocation({
					type: 'wgs84', // 默认为wgs84的gps坐标，如果要返回直接给openLocation用的火星坐标，可传入'gcj02'
					success: function(res) {
						locationData.location = res.longitude + ',' + res.latitude;
						//根据经纬度获取详细地址信息
						loadAmap().then(AMap=>{
							AMap.plugin('AMap.Geocoder', function () {
								var geocoder = new AMap.Geocoder({
									city: '全国'
								})
								var lnglat = [res.longitude, res.latitude];
								geocoder.getAddress(lnglat, function (status, result) {
									if (status === 'complete' &&result.info === 'OK') {
										let {province,city,adcode,township,street} = result.regeocode.addressComponent
										locationData.provinceName =province.substring(0,province.length - 1);
										locationData.cityName =province;
										locationData.adcode = adcode.slice(0,3)
										locationData.cityCode = adcode.slice(0,4)+'00'
										locationData.regionData = {
											name:township,
											desc:street
										}
										func(locationData);
									} else {
										func(locationData);
									}
								})
							})
						})
					},
					fail(err){
						func(locationData);
					}
				});
			})
			// let tar_url = process.env.VUE_APP_API_URL + 'v3/member/front/login/wxjsConf?source=1&method=getH5Location';
			// uni.request({
			// 	url: tar_url,
			// 	method: 'GET',
			// 	data: {
			// 		url: encodeURIComponent(location.href)
			// 	},
			// 	success(res) {
			// 		let data = res.data;
			// 		// #ifdef H5
			// 		jweixin.config({
			// 			debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
			// 			appId: data.data.appId, // 必填，公众号的唯一标识
			// 			timestamp: data.data.timestamp, // 必填，生成签名的时间戳
			// 			nonceStr: data.data.nonceStr, // 必填，生成签名的随机串
			// 			signature: data.data.signature, // 必填，签名
			// 			jsApiList: ["getLocation"] // 必填，需要使用的JS接口列表
			// 		});
				
			// 		//#endif
			// 	}
			// })
		}
	})
	
	getLocationPromise.finally(()=>{
		getLocationPromise = null
	})
	
	return getLocationPromise
}

/** 
 * 获取APP的地理位置
 * @zjf-2021-06-28
 */
export function getAppLocation(locationData) {
	
	const system = uni.getSystemInfoSync()
	let type = system.platform == 'android'?'gcj02':'wgs84'
	
	if(getLocationPromise){
		return getLocationPromise
	}
	
	getLocationPromise = new Promise(func => {
		uni.getLocation({
			geocode: true,
			type,
			success: function(res) {
				locationData.location = res.longitude + ',' + res.latitude;
				locationData.cityName = res.address.city;
				if (res.address&&(res.address.city == res.address.province)) {
					locationData.provinceName = res.address.city.substring(0, res.address.city
						.length - 1);
				}
				if(system.platform == 'android'){
					locationData.regionData = {
						name:res.address.poiName,
						desc:`${res.address.district}${res.address.street}`
					}
				}else{
					locationData.regionData = {
						name:res.address.street,
						desc:res.address.district
					}
				}
				
				func(locationData);
			},
			fail: function(err) {
				console.log('获取位置失败', err)
				func(locationData);
			}
		})
	})
	
	getLocationPromise.finally(()=>{
		getLocationPromise = null
	})
	
	return getLocationPromise
}

/** 
 * 获取微信小程序的地理位置
 * @zjf-2021-06-28
 */

function handleLocationErr(err){
	let title = ''
	switch(err.errCode){
		case "0":{
			title = '定位失败，请打开定位服务后重试'
			break
		}
		
		case "2":{
			title = '定位失败，请打开GPS或wifi后重试'
		}
		
		case "404":{
			title = '定位失败，未获取到定位服务'
		}
	
	}
	
	uni.showToast({
		title,
		icon:'none',
		duration:3000
	})
}



function WxXcxLocation(func,locationData){
	const myAmapFun = new amapFile.AMapWX({
		key: process.env.VUE_APP_WX_XCX_GD_KEY
	});
	myAmapFun.getRegeo({
		success: function(data) {
			let {longitude,latitude,name,desc} = data[0]
			let temp = data[0].regeocodeData.addressComponent;
			locationData.location =`${longitude},${latitude}`;
			if (temp&&(temp.city.length != undefined && temp.city.length ==0)) {
				locationData.provinceName = temp.province.substring(0,
					temp.province.length - 1);
				locationData.cityName = temp.province;
			} else {
				locationData.provinceName = temp.province;
				locationData.cityName = temp.city;
			}
			locationData.adcode = temp.adcode.slice(0,3)
			locationData.cityCode = temp.adcode.slice(0,4)+'00'
			locationData.regionData = {
				name,
				desc
			}
			func(locationData)
		}
	})
}
export function getWxXcxLocation(locationData) {
	
	if(getLocationPromise){
		return getLocationPromise
	}
	
	getLocationPromise = new Promise(func => {
		uni.getSetting({
			success: function(res) {
				if (res.authSetting['scope.userFuzzyLocation']) {
					WxXcxLocation(func,locationData)
				} else {
					// 初次定位时，延后30秒再定位(针对审核)
					setTimeout(()=>{
						uni.authorize({
							scope: 'scope.userFuzzyLocation',
							success() {
								WxXcxLocation(func,locationData)
							},
							fail(e) {
								handleLocationErr(e)
								func(locationData);
							}
						});
					},30000)
					
				}
			}
		})
	})
	
	getLocationPromise.finally(()=>{
		getLocationPromise = null
	})
	
	return getLocationPromise
}

/** 
 * 获取设备信息，各个设备分别获取
 * @zjf-2021-06-27
 */
export function getSystemInfo() {
	uni.getSystemInfo({
		"success": function(t) {
			console.info('设备信息：', t);
		},
	})
}

/** 
 * 统计事件
 * @params params Object 参数
 * @zjf-2021-06-27
 */
export async function sldStatEvent(data) {
	// #ifdef MP
	await wait()
	// #endif
	//将data和公共属性合并得到最终要发送的数据
	let sldStatCommonProperty = getStatStorage('sldStatCommonProperty');
	let targetParams = {
		...sldStatCommonProperty,
		...data
	};
	
	let location = uni.getStorageSync('location')
	//没有位置信息的话 要获取位置信息
	if (!targetParams.location && location) {
		targetParams = {
			...targetParams,
			...location
		};
		updateStatCommonProperty(location)
	}
	
	//日志开启的话需要打印数据
	const sldStatShowDebug = uni.getStorageSync('sldStatShowDebug');
	if (sldStatShowDebug) {
		console.info('统计传输数据: ', targetParams);
	}
	
	//发送请求
	let params = {
		url: 'v3/statistics/front/member/behavior/save',
		method: 'POST',
		data: {
			u: base64Encode(JSON.stringify(targetParams))
		}
	}
	
	//app-3-start















	//app-3-end
	

	// 非app端直接调用
	request(params).then(res => {})

}

function openLocateSetting(){
	return new Promise((resolve,reject)=>{
		request({
			url:'v3/system/front/setting/getSettings',
			data:{
				names:'app_apply_market_review'
			}
		}).then(res=>{
			if(res.state==200){
				resolve(res.data[0]!=1)
			}else{
				resolve(false)
			}
		}).catch(err=>{
			reject(false)
		})
	})
}


/**
 * cbList:回调函数列表，将传过来的cb参数压进cbList。
 * 当cbList有多个时，当前只调用第一次的getLocation方法，getLocation获取定位还是失败后将cbList里的所有cb执行
 * 目的是initCityInfo被同时多次调用时，防止getLocation被执行多次，一次就行。
 * 如果getApp().globalData.locationInfo有值的话，优先返回getApp().globalData.locationInfo
 */
let cbList = []
export async function initCityInfo(restore) {
	let {locationInfo:storageInfo,locateStatus} = getApp().globalData
	if (restore) {
		storageInfo = {}
	}
	if (storageInfo&&Object.keys(storageInfo).length) {
		return {state:200,...storageInfo}
	}
	
	//如果用户拒绝定位授权则直接回调
	if(locateStatus==='user_cancel'&&!restore){
		return {}
	}
	
	const location = await getLocation()
	
	if (location && location.cityName && location.location) {
		let cityInfo = {
			cityName: location.cityName,
			latitude: location.location.split(',')[1],
			longitude: location.location.split(',')[0],
			cityCode: location.cityCode,
			regionData:location.regionData
		}
		getApp().globalData.locationInfo = cityInfo
		return {state:200,...cityInfo}
	} else {
		return {state:500}
	}
}

//后置守卫下的统计操作
export function statAfterEachAction(to,from){
	let url = process.env.VUE_APP_API_URL.substring(0, process.env.VUE_APP_API_URL.length - 1);
	//商品详情页、店铺的页面需要单独统计，但是需要把pageUrl和referrerPageUrl先存进去
	let specialPages = [
		'/pages/index/guidePage', //引导页
		'/standard/product/detail', //商品详情页
		'/standard/store/shopHomePage', //店铺首页
		'/standard/store/storeIntroduction', //店铺信息页
		'/standard/store/productSearch', //店铺商品列表页
		'/extra/tshou/goods/detail', //推手商品详情页
	];
	
	let statPvFlag = true;
	for (let i in specialPages) {
		if (specialPages[i].indexOf(to.path) > -1) {
			statPvFlag = false;
			break;
		}
	}
	
	if (!statPvFlag) {
		//不需要pv类型的统计
		updateStatCommonProperty({
			pageUrl: url + to.path,
			referrerPageUrl: url + from.path
		});
	} else {
		setTimeout(() => {
			//wx-2-start
			//#ifdef MP-WEIXIN
			if (to.path !== '/pages/index/index') {
				sldStatEvent({
					behaviorType: 'pv',
					pageUrl: url + to.path,
					referrerPageUrl: url + from.path
				});
			}
			//#endif
			//wx-2-end
			//#ifndef MP-WEIXIN
			sldStatEvent({
				behaviorType: 'pv',
				pageUrl: url + to.path,
				referrerPageUrl: url + from.path
			});
			//#endif
		}, 3000)
	}
	
}