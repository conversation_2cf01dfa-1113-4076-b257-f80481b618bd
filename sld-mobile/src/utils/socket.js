/**
 * 双socket模式
 * <AUTHOR> 
 * 2023-12-06
**/

import io from '@hyoga/uni-socket.io'
import request from '@/utils/request'
import store from '../store/index.js'

let audioContext,isInnerSocketOn = false

//此socket作为全局socket连接,用作消息数监听
export function GlobalSocketInstance() {
	let timer = null
	let usedMsgNum = 0
	let _socket = null
	let clickCount = 0
	let identity = 'member'
	audioContext = uni.createInnerAudioContext()
	audioContext.src = process.env.VUE_APP_IMG_URL + 'voice/msg.mp3'
	
	const initialModule = ()=>{
		//在App onLauch初始化时,vuex初始化完成未确定。
		//使用缓存的userInfo能确保
		let userInfo = uni.getStorageSync('userInfo')
		
		if(!userInfo){
			return
		}
		
		initSocket()
	}
	
	

	const getMsgNum = () => {
		let {hasLogin} = store.state
		
		let url = identity=='member'?'v3/helpdesk/front/chat/unReadMsgNum':'/v3/helpdesk/seller/chat/unReadMsgNum'
		
		if(hasLogin){
			request({
				url
			}).then((res) => {
				if (res.state == 200) {
					if(usedMsgNum!==res.data){
						usedMsgNum = res.data
						store.commit('setMsgNum', res.data)
					}
				}
			})
		}else{
			store.commit('setMsgNum', 0)
		}
	}
	
	const socketIdentityChange = (name)=>{
		identity = name
	}
	
	const socketGetIdentity = ()=> identity
	
	const initSocket = ()=>{
		if(_socket){
			return
		}
		_socket = io(process.env.VUE_APP_CHAT_URL, {
		  reconnection: true,
		  jsonp: true,
		  transports: ['websocket', 'polling'],
		  timeout: 5000
		}) 
		_socket.on('connect',()=>{
			let {userCenterData,userInfo} = store.state
			if(identity=='member'){
				_socket.emit('connect_success', {
				  storeId: '',
				  userId: userCenterData.memberId,
				  role: 1
				})
			}
			
			_socket.on('contact_change',(e)=>{
				//做防抖处理，频繁接受消息时，防止消息数变动频繁渲染
				clearTimeout(timer)
				timer = setTimeout(()=>{
					let {shopSetting} = store.state
					getMsgNum()
				},300)
			})
		})
	}
	
	const closeSocket = ()=>{
		if(_socket){
			_socket.close()
			_socket.destroy()
			_socket = null
		}
	}
	
	return {
		initialModule,
		socketGetIdentity,
		socketIdentityChange,
		getMsgNum,
		initSocket,
		closeSocket,
		getSocket:()=>_socket
	}
}

//内部socket，作用于聊天详情
export function InnerSocketInstance(){
	const socket = io(process.env.VUE_APP_CHAT_URL, {
	  reconnection: true,
	  jsonp: true,
	  transports: ['websocket', 'polling'],
	  timeout: 5000
	}) 
	
	const oldDestroy = socket.destroy
	socket.destroy = function(...args){
		isInnerSocketOn = false
		oldDestroy.apply(this,args)
	}
	
	return {
		getSocket:()=>{
			isInnerSocketOn = true
			return socket
		},
	}
}