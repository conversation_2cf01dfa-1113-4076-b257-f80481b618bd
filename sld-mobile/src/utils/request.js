import store from '../store'
import { getModuleToken } from './base.js'

export default async function request(opt) {

	opt = opt || {};
	opt.url = opt.url || '';
	opt.data = opt.data || null;
	opt.method = opt.method || 'GET';

	let otherParam = {};
	if (!opt.responseType) {
		opt.header = opt.header || {
			"Content-Type": "application/x-www-form-urlencoded",
			"Language": process.env.VUE_APP_CUR_LANG,
		};
		otherParam.dataType = 'json'
	} else {
		opt.header = {
			"Language": process.env.VUE_APP_CUR_LANG
		}
		otherParam.responseType = opt.responseType;
		if (opt.dataType) {
			otherParam.dataType = opt.dataType
		}
	}

	if (opt.url.indexOf('frontLogin/oauth/token') == -1) {
		let getRoleToken = getModuleToken(store)
		let token = '';
		if (getRoleToken.access_token) {
			token = getRoleToken.access_token;
		}
		opt.header.Authorization = 'Bearer ' + token;

		//判断时间
		let cur_time = new Date().getTime();
		let start_time = uni.getStorageSync('sld_login_time');
		let sld_refresh_token = getRoleToken.refresh_token;
		if (start_time && (cur_time - start_time > (58 * 60 * 1000)) && sld_refresh_token) {
			//用户token过期之后重新根据refresh_token获取token(58分钟，token的有效期是60分钟)
			let res = await refreshToken(sld_refresh_token);
			res = res.data != undefined ? res.data : {
				state: 255
			}
			if (res.state == 200) {
				store.commit('login', res.data)
				//更新sld_token的时间
				uni.setStorage({
					key: 'sld_login_time',
					data: new Date().getTime(),
				});
				opt.header.Authorization = 'Bearer ' + res.data.access_token;
			} else {
				//清空本地用户信息
				store.commit('logout', '')
			}
		}
	}

	//用户登录终端来源：2、H5(包括微信内部浏览器)；3、Android；4、IOS；6、微信小程序；7、百度小程序；8、支付宝小程序；9、抖音小程序
	// #ifdef H5
	opt.header['Terminal-source'] = 2;
	// #endif
	// #ifdef MP
	opt.header['Terminal-source'] = 6;
	// #endif

	opt.header['sg-app-version'] = process.env.VUE_APP_VERSION;


	return new Promise((resolve, reject) => {
		uni.request({
			url: process.env.VUE_APP_API_URL + opt.url,
			data: opt.data,
			method: opt.method,
			header: opt.header,
			...otherParam,
			success: res => {
				if (res.data.state == 266) {
					store.commit('logout', '')
				} else if (res.data.state == 270) {
					res.data.msg = '操作失败，输入的文本包含敏感信息' + res.data.msg + '，请修改后重新操作'
					resolve(res.data);
				} else {
					resolve(res.data);
				}
			},
			fail: err => {
				reject(err);
			},
			complete: (res) => { }
		})
	})

}

/** 
 * 刷新token
 * @zjf-2021-07-22
 */
function refreshToken(refresh_token) {
	return new Promise(func => {
		uni.request({
			url: process.env.VUE_APP_API_URL + 'v3/frontLogin/oauth/token',
			data: {
				grant_type: 'refresh_token',
				refresh_token: refresh_token,
			},
			method: 'POST',
			header: {
				"Content-Type": "application/x-www-form-urlencoded",
				"Language": process.env.VUE_APP_CUR_LANG,
			},
			success: res => {
				func(res);
			}
		})
	})
}