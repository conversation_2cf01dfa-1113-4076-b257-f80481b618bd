{"name": "莘古商城", "appid": "__UNI__EF081A9", "description": "分享美好", "versionName": "8.1", "versionCode": 20241004, "transformPx": false, "sassImplementationName": "dart-sass", "networkTimeout": {"uploadFile": 20000, "request": 20000}, "app-plus": {"usingComponents": true, "nvueCompiler": "uni-app", "splashscreen": {"alwaysShowBeforeRender": false, "waiting": false, "autoclose": false, "delay": 0}, "modules": {"OAuth": {}, "Payment": {}, "Share": {}, "VideoPlayer": {}, "Geolocation": {}, "Camera": {}, "Barcode": {}, "Maps": {}}, "distribute": {"android": {"permissions": ["<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.INTERNET\"/>", "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"], "schemes": "sldjavabbcv2", "abiFilters": ["armeabi-v7a", "arm64-v8a"], "minSdkVersion": 18, "targetSdkVersion": 30}, "ios": {"privacyDescription": {"NSPhotoLibraryUsageDescription": "APP想访问您的相册，为了把您想要下载的图片存到你手机里和上传图片等功能", "NSCameraUsageDescription": "APP想访问您的相机，为了帮您扫描二维码或商品等功能", "NSLocalNetworkUsageDescription": "是否允许app访问您的本地网络以便与服务器连接", "NSPhotoLibraryAddUsageDescription": "是否允许APP保存图片到相册", "NSMicrophoneUsageDescription": "是否允许App使用您的麦克风，为了帮您录制音频功能", "NSCalendarsUsageDescription": "是否允许App使用您的日历", "NSBluetoothPeripheralUsageDescription": "是否允许App使用您的蓝牙", "NSBluetoothAlwaysUsageDescription": "是否允许App使用您的蓝牙", "NSSpeechRecognitionUsageDescription": "语音识别权限", "NSLocationWhenInUseUsageDescription": "我们需要获取您的地理位置以便获取您附近的商家", "NSLocationAlwaysUsageDescription": "我们需要获取您的地理位置以便获取您附近的商家", "NSLocationAlwaysAndWhenInUseUsageDescription": "是否允许APP使用您的地理位置", "NSSiriUsageDescription": "是否允许App使用您的Siri", "NSContactsUsageDescription": "是否允许App使用您的通讯录"}, "urltypes": "sldjavabbcv2", "idfa": false, "dSYMs": false}, "sdkConfigs": {"ad": {}, "oauth": {"weixin": {"appid": "wxeeaa210bf966bd56", "appsecret": "4953f951b9ebaaef8ccca5511d4b1c41", "UniversalLinks": "https://www.slodon.cn/wechat-auth/"}}, "payment": {"alipay": {"__platform__": ["ios", "android"]}, "weixin": {"__platform__": ["ios", "android"], "appid": "wxeeaa210bf966bd56", "UniversalLinks": "https://www.slodon.cn/wechat-auth/"}, "paypal": {"__platform__": ["ios", "android"], "returnURL_ios": "com.slodon.javabbc://paypalpay", "returnURL_android": "com.slodonapp.javabbc//paypalpay"}}, "share": {"weixin": {"appid": "wxeeaa210bf966bd56", "UniversalLinks": "https://www.slodon.cn/wechat-auth/"}}, "geolocation": {"amap": {"__platform__": ["ios", "android"], "appkey_ios": "6904e4052c4370a9aa1cfb271aad5012", "appkey_android": "45878cceaf58ddf4eefbc85239857c9c", "name": "amap_18337250385C2UD5aaDC"}}, "push": {"unipush": {}}, "maps": {"amap": {"name": "amap_18337250385C2UD5aaDC", "appkey_ios": "6904e4052c4370a9aa1cfb271aad5012", "appkey_android": "45878cceaf58ddf4eefbc85239857c9c"}}}, "splashscreen": {"androidStyle": "default", "android": {"hdpi": "/Users/<USER>/Downloads/未命名(1)(2) (1).png", "xhdpi": "static/app-plus/laucher/720x1242.9.png", "xxhdpi": "static/app-plus/laucher/1080x1882.9.png"}}, "icons": {"android": {"hdpi": "unpackage/res/icons/72x72.png", "xhdpi": "unpackage/res/icons/96x96.png", "xxhdpi": "unpackage/res/icons/144x144.png", "xxxhdpi": "unpackage/res/icons/192x192.png"}, "ios": {"appstore": "unpackage/res/icons/1024x1024.png", "iphone": {"app@2x": "unpackage/res/icons/120x120.png", "app@3x": "unpackage/res/icons/180x180.png", "spotlight@2x": "unpackage/res/icons/80x80.png", "spotlight@3x": "unpackage/res/icons/120x120.png", "settings@2x": "unpackage/res/icons/58x58.png", "settings@3x": "unpackage/res/icons/87x87.png", "notification@2x": "unpackage/res/icons/40x40.png", "notification@3x": "unpackage/res/icons/60x60.png"}, "ipad": {"app": "unpackage/res/icons/76x76.png", "app@2x": "unpackage/res/icons/152x152.png", "notification": "unpackage/res/icons/20x20.png", "notification@2x": "unpackage/res/icons/40x40.png", "proapp@2x": "unpackage/res/icons/167x167.png", "settings": "unpackage/res/icons/29x29.png", "settings@2x": "unpackage/res/icons/58x58.png", "spotlight": "unpackage/res/icons/40x40.png", "spotlight@2x": "unpackage/res/icons/80x80.png"}}}}, "privacy": {"prompt": "template", "template": {"title": "感谢下载我们的APP", "message": "<font style=\"fontSize:40px;lineHeight:60px;\">亲，感谢您对我们一直以来的信任</font>！我们依据最新的监管要求更新了我们的<a href=\"https://m.55sld.com/pages/privacyPolicy/privacyPolicy?type=register_agreement\">《用户协议》</a>和<a href=\"https://m.55sld.com/pages/privacyPolicy/privacyPolicy?type=privacy_policy\">《隐私协议》</a>了解详细信息，特向您说明：<br/>1.为向您提供交易相关基本功能，我们会收集、使用必要的信息；<br/>2.未经您同意,我们不会从第三方获取、共享或向其提供您的信息；<br/>3.你可以查询、更正、删除您的个人信息，我们也提供账户注销的渠道", "buttonAccept": "同意", "buttonRefuse": "不同意", "second": {"title": "您需要同意该隐私政策才能继续", "message": "若不同意，很遗憾我们将无法为您提供服务", "buttonAccept": "同意", "buttonRefuse": "退出应用"}}}, "nativePlugins": {}}, "quickapp": {}, "mp-weixin": {"libVersion": "latest", "usingComponents": true, "appid": "wx61dac0f67130ce73", "setting": {"urlCheck": true, "minified": true, "es6": true, "postcss": true}, "optimization": {"subPackages": true}, "permission": {"scope.userFuzzyLocation": {"desc": "需要开启地理位置信息才能为您推荐更多好物"}}, "requiredPrivateInfos": ["getFuzzyLocation", "chooseLocation"], "__usePrivacyCheck__": true, "navigateToMiniProgramAppIdList": ["wx7643d5f831302ab0"]}, "h5": {"title": "莘古商城", "domain": "https://m.shengu.shop/", "sdkConfigs": {"maps": {"amap": {"key": "c711d6c2ace2cc0973d8357373c62de9", "securityJsCode": "30b47d41b12345751c43f40723a4bc17", "serviceHost": ""}}}, "template": "template.h5.html", "optimization": {"treeShaking": {"enable": true}}, "router": {"mode": "history"}}, "mp-toutiao": {"setting": {"urlCheck": false}, "appid": "tte48592387bbc677501"}, "mp-alipay": {"appid": "2021002186686377"}, "mp-baidu": {"appid": "24979140"}}