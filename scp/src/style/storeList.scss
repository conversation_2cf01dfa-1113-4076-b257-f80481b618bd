*,
html,
body {
    padding: 0;
    margin: 0; /* font-family: 'Helvetica Neue',<PERSON><PERSON>,'Liberation Sans',FreeSans,'Hiragino Sans GB',sans-serif,"Microsoft YaHei",微软雅黑,"Microsoft JhengHei",华文细黑,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>*/
}

.iconfont {
    font-family: "iconfont" !important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

ul,
ol,
li {
    list-style: none;
}

ul {
    margin-bottom: 0px;
}

.fl {
    float: left;
}
.fr {
    float: right;
}

.clearfix:after {
    content: ".";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
}

a {
    text-decoration: none;
    color: #459ae9;
}

a:hover {
    color: #049dee;
    text-decoration: none;
}

em,
i {
    font-style: normal;
}

body {
    color: #666;
    background: #fff;
    min-width: 1210px;
}
.sld-img-center {
    position: relative;
    width: 80px;
    height: 80px;
    overflow: hidden;
    .img {
        width: 150px;
        height: 150px;
        background-position: center center;
        background-repeat: no-repeat;
        background-size: cover;
    }
}

.sld_store_list {
    width: 1210px;
    margin: 0 auto;
    padding-left: 0px;
    padding-right: 0px;
    .store_list_banner {
        width: 100%;
    }
    .fav_list {
        position: relative;
        padding: 10px 0;
        margin: 0 0 10px;
        /*border-bottom: 1px solid #e6e6e6;*/
        height: 303px;
        &.fav_list_box {
            padding: 0;
            height: auto;
        }
    }
    .seller_list_search {
        margin: 10px 0;
        height: 46px;
        font-size: 12px;
        padding-top: 8px;
        background-color: #f7f5f5;
        vertical-align: middle;

        a {
            display: inline-block;
            height: 30px;
            width: 80px;
            margin: 0 5px;
            border: 1px solid #dcdcdc;
            text-align: center;
            color: #606060;
            vertical-align: middle;
            line-height: 28px;
            background-color: #fff;
            cursor: pointer;
            i {
                font-size: 13px;
            }
        }
        a.on {
            background-color: var(--color_main);
            border-color: var(--color_main);
            color: #fff;
        }
        form {
            display: inline-block;
        }
        input {
            width: 227px;
            margin-left: 15px;
            height: 30px;
            border: 1px solid #dcdcdc;
            line-height: 28px;
            padding-left: 8px;
            outline: none;
        }
        button {
            width: 54px;
            height: 30px;
            background-color: #dcdcdc;
            color: #333333;
            border: none;
            margin-left: -4px;
            outline: none;
            vertical-align: top;
            cursor: pointer;
        }
    }

    .sld_store_item {
        width: 100%;
        height: 308px;
        margin-top: 10px;
        background-color: #fff;
        border: 1px solid #eeeeee;
        &.skeleton_sld_store_item {
            .sld_vendor_logo {
                background: $colorSkeleton;
            }
            .sld_vendor_name {
                background: $colorSkeleton;
                height: 14px;
            }
            .el-rate {
                background: $colorSkeleton;
            }
        }
        .main_lbbox {
            position: relative;
            width: 967px;
            height: 308px;
            min-width: 967px;
            overflow: hidden;
        }
        .bd li {
            float: left;
            height: 228px;
        }
        .sld_vendor {
            position: relative;
            width: 241px;
            height: 308px;
            padding: 46px 60px 0 60px;
            box-sizing: border-box;
            border-right: 1px solid #eee;
            .sld_vendor_logo {
                width: 114px;
                height: 114px;
                img {
                    width: 114px;
                    height: 114px;
                }
            }
            .sld_vendor_name {
                text-align: center;
                color: #444444;
                font-size: 13px;
                margin-top: 10px;
            }
            .sld_rate {
                margin-top: 8px;
                text-align: center;
                i {
                    float: left;
                    margin: 0 3px;
                    color: var(--color_main);
                }
            }
            .sld_vendor_bottom {
                position: absolute;
                bottom: 0;
                left: 0;
                text-align: center;
                width: 100%;
                border-top: 1px solid #eee;
                a {
                    width: 100%;
                    height: 46px;
                    text-align: center;
                    line-height: 46px;
                    color: #444444;
                    font-size: 14px;
                    &:hover {
                        color: var(--color_main);
                    }
                }
                a i {
                    font-size: 14px;
                    color: var(--color_main);
                    margin-right: 6px;
                }
            }
        }
    }

    .sld_vendor_collect {
        height: 70px;
        padding: 15px 0 20px 15px;
        box-sizing: border-box;
        a {
            float: left;
            display: inline-block;
            width: 120px;
            height: 34px;
            line-height: 34px;
            text-align: center;
            border: 1px solid #eeeeee;
            background-color: #fff;
            color: #444444;
            font-size: 14px;
        }
        a:last-child {
            border-left: none;
        }
        .sld_follow_on {
            background-color: var(--color_main);
            color: #fff;
        }
        .fr {
            padding: 0 20px;
            color: #333333;
            line-height: 34px;
            font-size: 14px;
            span:nth-child(1) {
                margin-right: 36px;
            }
            em {
                color: var(--color_main);
            }
        }
    }

    .sld_vendor_goods {
        position: relative;
        width: 967px;
        height: 228px;
        padding: 0 70px;
        overflow: hidden;
        li {
            width: 150px;
            float: left;
            margin-right: 20px;
            .sld_img {
                width: 150px;
                height: 150px;
            }
        }
        .goods_name {
            width: 150px;
            margin-top: 15px;
            color: #444444;
            font-size: 14px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            &:hover {
                color: var(--color_main);
            }
        }
        .goods_price {
            font-size: 16px;
            color: var(--color_main);
            margin-top: 3px;
            font-weight: 600;
        }
        .sale {
            color: #999999;
            font-size: 14px;
            line-height: 24px;
        }
        .hd {
            .next,
            .prev {
                position: absolute;
                top: 45px;
                width: 32px;
                height: 70px;
                font-size: 16px;
                color: #fff;
                background-color: #8d8b8b;
                // background-color: #ccc;
                text-align: center;
                line-height: 70px;
            }
            .next {
                right: 0;
            }
            .prev {
                left: 0;
                background-color: #ccc;
                -webkit-transform: rotate(180deg);
                -moz-transform: rotate(180deg);
                -ms-transform: rotate(180deg);
                -o-transform: rotate(180deg);
                transform: rotate(180deg);
            }
            i {
                font-size: 20px;
            }
        }
        ul {
            // width: 821px;
            display: flex;
            overflow-x: auto;
            overflow-y: hidden;
            transition: all 0.3s;
        }
        .slide_wrap {
            width: 829px;
            height: 228px;
            overflow: hidden;
        }
    }
}
.el-pager li.active {
    color: var(--color_main);
    cursor: default;
}
.el-pager li:hover {
    color: var(--color_main);
}
