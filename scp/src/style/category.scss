*,
html,
body {
  padding: 0;
  margin: 0;
}

ul,
ol,
li {
  list-style: none;
}

a {
  text-decoration: none;
  color: #459ae9;

  &:hover {
    text-decoration: none;
  }
}

body {
  color: #666;
  background: #fff;
}

ul {
  margin-bottom: 0;
}

.clearfix {
  display: block;
  zoom: 1;
}

em,
i {
  font-style: normal;
}

.fl {
  float: left;
}

.fr {
  float: right;
}

.clear {
  zoom: 1;

  &:after {
    content: "";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
  }
}

.goods_sort {
  position: relative;
  width: 1200px;
  margin: 0 auto;
  min-height: 700px;
}

.class_ification_wrap {
  position: relative;
  padding: 12px 0 0 0;
  background-color: #fff;
  margin-bottom: 30px;
  box-sizing: border-box;
  z-index: -1;

  .class_ification_con {
    width: 209px;
  }

  .class_ification {
    padding-right: 10px;
    height: calc(100vh - 100px);
    overflow-y: auto;
    width: 209px;

    &.fixed {
      position: fixed;
      top: 60px;
    }

    &.bottomFixed {
      position: absolute;
      bottom: 0;
    }

    .cat_item {
      width: 179px;
      height: 30px;
      margin-top: 10px;
      line-height: 30px;
      color: #010101;
      font-size: 12px;
      border-radius: 15px;
      overflow: visible;
      background: #f5f5f5;
      cursor: pointer;
      & > div {
        text-align: center;
      }

      &:nth-child(9n + 1):hover,
      &:nth-child(9n + 1).on {
        color: #fff;
        background: #edd557;
        background: linear-gradient(to right, #edd557, #f0a223);
      }

      &:nth-child(9n + 2):hover,
      &:nth-child(9n + 2).on {
        color: #fff;
        background: #42caab;
        background: linear-gradient(to right, #42caab, #0c97ee);
      }

      &:nth-child(9n + 3):hover,
      &:nth-child(9n + 3).on {
        color: #fff;
        background: #8fa4fc;
        background: linear-gradient(to right, #8fa4fc, #b386ea);
      }

      &:nth-child(9n + 4):hover,
      &:nth-child(9n + 4).on {
        color: #fff;
        background: #62d993;
        background: linear-gradient(to right, #62d993, #41ae65, #0c97ee);
      }

      &:nth-child(9n + 5):hover,
      &:nth-child(9n + 5).on {
        color: #fff;
        background: #64b1e4;
        background: linear-gradient(to right, #64b1e4, #6876f2);
      }

      &:nth-child(9n + 6):hover,
      &:nth-child(9n + 6).on {
        color: #fff;
        background: #f887c3;
        background: linear-gradient(to right, #f887c3, #f12086);
      }

      &:nth-child(9n + 7):hover,
      &:nth-child(9n + 7).on {
        color: #fff;
        background: #fdc237;
        background: linear-gradient(to right, #fdc237, #ff7802);
      }

      &:nth-child(9n + 8):hover,
      &:nth-child(9n + 8).on {
        color: #fff;
        background: #3dcbdd;
        background: linear-gradient(to right, #3dcbdd, #5594e4);
      }

      &:nth-child(9n):hover,
      &:nth-child(9n).on {
        color: #fff;
        background: #ff787b;
        background: linear-gradient(to right, #ff787b, #ff305c);
      }
    }
  }

  .class_detail {
    z-index: 99;
    flex: 1;
    margin-left: 20px;

    .detail_item {
      color: #333;
      font-size: 12px;
      margin-bottom: 20px;
      margin-top: 10px;

      .detail_title {
        font-size: 18px;
        font-weight: bold;
        padding: 2px 0 10px;
        line-height: 1;
      }

      .cat_wrap_dd {
        display: block;
        padding: 7px 0;

        .cat_dd_item {
          min-height: 38px;
          border-bottom: 1px dashed #dddddd;
        }

        .cat_left {
          width: 106px;
          padding-top: 10px;
          font-size: 12px;
          font-weight: bold;
        }

        .cat_add_right {
          padding-left: 106px;
          line-height: 38px;

          span {
            height: 18px;
            line-height: 18px;
            padding: 0 15px;
            box-sizing: border-box;
            border-right: 1px solid #ddd;
            display: inline-block;

            &:last-child {
              border-right: none;
            }
          }

          .line {
            display: inline-block;
            width: 1px;
            height: 18px;
            background-color: #dddddd;
            vertical-align: middle;

            &:nth-of-type(1) {
              display: none;
            }
          }
        }
      }
    }
  }
}

.detail_item {
  //将detail-item暂定最小高度，及内容溢出滚动
  overflow-y: auto;

  &.item_style1 {
    .detail_title {
      color: #f2b15c;
      border-bottom: 1px solid #f2b15c;
    }
    .cat_left {
      color: #f2b15c;
    }
  }

  &.item_style2 {
    .detail_title {
      color: #5cb0f2;
      border-bottom: 1px solid #5cb0f2;
    }
    .cat_left {
      color: #5cb0f2;
    }
  }

  &.item_style3 {
    .detail_title {
      color: #b585ea;
      border-bottom: 1px solid #b585ea;
    }
    .cat_left {
      color: #b585ea;
    }
  }

  &.item_style4 {
    .detail_title {
      color: #52c16e;
      border-bottom: 1px solid #52c16e;
    }
    .cat_left {
      color: #52c16e;
    }
  }

  &.item_style5 {
    .detail_title {
      color: #687ff0;
      border-bottom: 1px solid #687ff0;
    }
    .cat_left {
      color: #687ff0;
    }
  }

  &.item_style6 {
    .detail_title {
      color: #f33b96;
      border-bottom: 1px solid #f33b96;
    }
    .cat_left {
      color: #f33b96;
    }
  }

  &.item_style7 {
    .detail_title {
      color: #ff7802;
      border-bottom: 1px solid #ff7802;
    }
    .cat_left {
      color: #ff7802;
    }
  }

  &.item_style8 {
    .detail_title {
      border-bottom: 1px solid #40c3de;
    }
    .cat_left {
      color: #40c3de;
    }
  }

  &.item_style0 {
    .detail_title {
      color: #ff6a75;
      border-bottom: 1px solid #ff6a75;
    }
    .cat_left {
      color: #ff6a75;
    }
  }
}
// width:106px;
