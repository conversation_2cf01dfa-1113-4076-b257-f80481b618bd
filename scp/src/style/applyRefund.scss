// 申请退款页面样式
.sld_apply_refund {
    width: 1010px;
    min-height: 567px;
    background: #ffffff;
    border: 1px solid #dfdfdf;
    border-radius: 2px;
    margin: 20px 0 0 20px;
    float: left;
    .apply_refund_top {
        width: 1008px;
        height: 38px;
        background: #f8f8f8;
        padding-right: 10px;
        box-sizing: border-box;
        cursor: pointer;
        img {
            width: 20px;
            height: 20px;
            margin-right: 10px;
        }
        span {
            font-size: 14px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #333333;
            &:nth-child(2) {
                color: #999999;
            }
        }
    }
    .apply_refund_info {
        width: 1007px;
        border-top: 1px solid #dddddd;
        padding: 20px 0;
        box-sizing: border-box;
        .refund_amount {
            .refund_amount_edit {
                font-size: 14px;
                font-family: Microsoft YaHei;
                font-weight: bold;
                color: #999999;
                .refund_amount_price {
                    color: var(--color_price);
                }
                .edit_price_btn {
                    color: var(--color_price);
                    margin: 0 30px;
                    cursor: pointer;
                }
            }
            .edit_refund_amount {
                .amount_input {
                    .el-input__inner {
                        border-right: 0px;
                        height: 30px;
                        border-radius: 4px 0 0 4px;
                    }
                }
                span {
                    width: 52px;
                    height: 30px;
                    line-height: 30px;
                    text-align: center;
                    display: block;
                    font-size: 12px;
                    font-family: Microsoft YaHei;
                    font-weight: 400;
                    cursor: pointer;
                }
                .cancel_edit_amount {
                    background: #dfdfdf;
                    border: 1px solid #dfdfdf;
                    color: #333333;
                }
                .ok_edit_amount {
                    background: var(--color_main);
                    border-radius: 0 5px 5px 0;
                    color: #ffffff;
                }
            }
        }
        .apply_number {
            .edit_apply_number {
                width: 200px;
                .el-input__inner {
                    height: 30px;
                }
            }
        }
        .upload_voucher {
            padding-top: 10px;
            .upload_img {
                width: 178px;
                height: 178px;
                border: 1px solid #dddddd;
            }
        }
    }
    .apply_refund_btn {
        width: 80px;
        height: 30px;
        background: var(--color_main);
        border-radius: 5px;
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #ffffff;
        line-height: 30px;
        text-align: center;
        margin: 37px auto;
        cursor: pointer;
    }
    .pre_img_model {
        max-height: 560px;
        margin: 0 auto;
        .pre_img {
            width: 100%;
            height: 100%;
        }
    }

    // 同类名
    .refund_title {
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #333333;
        display: flex;
        justify-content: flex-end;
        width: 100px;
        margin-right: 48px;
        span {
            color: var(--color_main);
        }
    }
    .marginBottom20 {
        margin-bottom: 20px;
    }
}
// 批量售后商品选择弹框 start
.batch_selModel {
    border: 1px solid #e5e5e5;
    height: 298px;
    padding: 5px;
    .batch_default {
        &:last-child {
            .batch_default_pre:last-child {
                margin-bottom: 0px;
            }
        }

        .batch_default_pre {
            margin-bottom: 20px;
            .batch_default_sel {
                width: 12px;
                height: 12px;
                cursor: pointer;
            }
            .batch_default_img {
                width: 70px;
                height: 70px;
                background-size: contain;
                background-repeat: no-repeat;
                background-position: center;
                margin: 0 10px;
                cursor: pointer;
            }
            .batch_default_des {
                cursor: pointer;
                height: 70px;
                p {
                    max-width: 277px;
                    font-size: 14px;
                    font-family: Microsoft YaHei;
                    font-weight: 400;
                    color: #333333;
                    line-height: 18px;
                    &.goodsName {
                        text-overflow: -o-ellipsis-lastline;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        line-clamp: 2;
                        -webkit-box-orient: vertical;
                        margin-bottom: 20px;
                    }
                    &.specValues {
                        color: #999999;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                }
            }
        }

        .editNum {
            color: #999 !important;
            font-size: 14px !important;
        }
    }
}
.batch_model_btn {
    margin-top: 20px;
    span {
        display: block;
        width: 56px;
        height: 32px;
        background: #ffffff;
        border-radius: 3px;
        text-align: center;
        line-height: 32px;
        cursor: pointer;
        &:nth-child(1) {
            border: 1px solid #dddddd;
        }
        &:nth-child(2) {
            color: #ffffff;
            background: var(--color_main_bg);
            margin-left: 10px;
        }
    }
}
// 批量售后商品选择弹框 end

//element-plus组件样式修改
#elUpload,
.upload {
    display: flex;
}
.el-upload-list__item {
    transition: none !important;
}
.el-upload--picture-card:hover,
.el-upload:focus {
    border-color: var(--color_main);
    color: var(--color_main);
}

.el-textarea__inner:focus {
    outline: 0;
    border-color: var(--color_main);
}
.el-select .el-input__inner:focus {
    border-color: var(--color_main);
}
.el-select .el-input.is-focus .el-input__inner {
    border-color: var(--color_main);
}
.el-upload-list--picture-card {
    display: flex;
    .el-upload-list__item {
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .el-upload-list__item-thumbnail {
        display: inline-block;
        max-width: 146px;
        max-height: 146px;
        margin: 0 auto;
        width: unset;
        height: unset;
    }
}

.el-dialog {
    padding: 15px;
}
.el-icon-close::before {
    font-size: 20px;
}
