.sld_login {
    .sld_login_header {
        background: #fff;
        box-shadow: 0 3px 5px rgba(0, 0, 0, 0.05);

        .content {
            display: flex;
            padding: 20px 0;
            align-items: center;
            width: 1200px;
            margin: auto;

            .l_logo {
                cursor: pointer;
                display: flex;
                margin-right: 70%;

                .img {
                    display: inline-block;
                    vertical-align: top;
                    max-width: 190px;
                    max-height: 43px;
                }

                .text {
                    font-size: 0;
                    display: inline-block;
                    vertical-align: top;
                    line-height: 48px;
                    margin-left: 5px;
                }
            }
        }
    }

    .sld_login_content {
        width: 100%;
        height: 600px;
        position: relative;

        .bg {
            position: absolute;
            margin: auto;
            max-width: 100%;
            height: 100%;
            overflow: hidden;
            display: block;
        }

        .login {
            width: 1200px;
            height: 100%;
            margin: auto;
            z-index: 99;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            position: relative;

            .login_box {
                position: relative;
                z-index: 10000;
                background: #fff;
                padding: 10px 2px 50px;

                .top {
                    margin-top: 20px;
                    padding: 0 20px;
                    display: flex;
                    width: 356px;
                    cursor: default;
                    .item {
                        flex: 1;
                        text-align: center;
                        font-size: 18px;
                        color: #999999;
                        cursor: pointer;
                        position: relative;

                        &:first-child {
                            padding: 16px 45px 18px 35px;
                            &.active {
                                &:after {
                                    position: absolute;
                                    left: 50%;
                                    bottom: 0;
                                    margin-left: -45px;
                                    content: "";
                                    width: 80px;
                                    height: 1px;
                                    background-color: var(--color_main);
                                }
                            }
                        }

                        &:last-child {
                            padding: 16px 35px 18px 45px;
                            &:before {
                                position: absolute;
                                left: 0;
                                top: 12px;
                                bottom: 12px;
                                width: 2px;
                                height: auto;
                                display: block;
                                content: " ";
                                font-size: 0;
                                background: #eee;
                            }
                            &.active {
                                &:after {
                                    position: absolute;
                                    left: 50%;
                                    bottom: 0;
                                    margin-left: -35px;
                                    content: "";
                                    width: 80px;
                                    height: 1px;
                                    background-color: var(--color_main);
                                }
                            }
                        }

                        &.active {
                            color: #333333; /* var(--color_main); */
                            font-weight: 700;
                        }
                    }
                }

                .center {
                    padding: 30px 30px 50px;

                    .item {
                        position: relative;
                        margin-top: 20px;
                        border-radius: 2px;

                        &:first-child {
                            margin-top: 0;
                        }

                        .icon {
                            position: absolute;
                            left: 1px;
                            top: 1px;
                            width: 50px;
                            text-align: center;
                            height: 38px;
                            background: #f8f8f8;

                            .input {
                                border: 1px solid #e8e8e8;
                                height: 40px;
                                padding: 0 44px 0 60px;
                                width: 296px;
                            }
                        }

                        .input {
                            border: 1px solid #e8e8e8;
                            height: 40px;
                            padding: 0 44px 0 60px;
                            width: 296px;
                        }

                        &.code {
                            .input {
                                padding-right: 10px;
                                width: 150px;
                            }
                        }

                        .img_code {
                            position: absolute;
                            right: 0;
                            top: 0;
                            border: 1px solid #eee;
                            border-left: 0;
                            width: 80px;
                            height: 40px;
                        }
                    }

                    .cancel {
                        position: absolute;
                        right: 0;
                        top: 1px;
                        width: 44px;
                        height: 38px;
                        cursor: pointer;

                        :before {
                            position: absolute;
                            top: 9px;
                            left: 14px;
                        }
                    }

                    .send_code {
                        position: absolute;
                        right: 0;
                        top: 0;
                        background: #f9f9f9;
                        border: 1px solid #eee;
                        border-left: 0;
                        width: 80px;
                        height: 40px;
                        line-height: 40px;
                        text-align: center;
                        color: #000;

                        :hover {
                            color: var(--color_main);
                        }
                    }

                    .error {
                        margin-top: 10px;
                        position: relative;
                        color: rgb(225, 37, 27);
                        height: 16px;
                        line-height: 16px;
                    }

                    .login_btn {
                        display: block;
                        margin-top: 35px;
                        background: var(--color_main_bg);
                        color: #fff;
                        text-align: center;
                        border-radius: 2px;
                        height: 45px;
                        line-height: 45px;
                        font-size: 18px;
                        letter-spacing: 5px;

                        &:hover {
                            opacity: 0.9;
                        }
                    }
                }

                .bottom {
                    height: 51px;
                    background: #fefefe;
                    border-top: 1px solid #eee;
                    position: absolute;
                    width: 100%;
                    bottom: 0;
                    left: 0;
                    display: flex;
                    padding: 0 16px;
                    box-sizing: border-box;

                    a {
                        display: block;
                        line-height: 50px;
                        margin-right: 10px;
                        color: #999;

                        &:hover {
                            color: #000;
                        }
                    }
                    img {
                        width: 28px;
                        height: 28px;
                        cursor: pointer;
                    }
                }
            }
        }
    }
}
