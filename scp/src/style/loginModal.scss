.sld_login_content {
  width: 376px;
  height: 391px;
  position: relative;

  .close_icon {
    position: absolute;
    top: -26px;
    z-index: 999;
    right: 25px;
    color: #999;
  }

  .login {
    width: 376px;
    height: 391px;
    margin: auto;
    z-index: 99;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    position: relative;

    .login_box {
      position: relative;
      z-index: 10000;
      background: #fff;
      padding: 10px 10px 50px;

      .qrLogin {
        position: absolute;
        top: 0px;
        left: 0;
        z-index: 9;
        .qrLeft {
          width: 45px;
          cursor: pointer;
        }
        .qrRight {
          width: 86px;
          height: 28px;
          position: absolute;
          top: 4px;
          left: 50px;
          z-index: 9;
        }
      }

      .qrMain {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 356px;
        height: 345px;
        cursor: default;
        .qrMain_code {
          position: relative;
          width: 210px;
          height: 210px;
          .qrMain_code_bg {
            width: 100%;
            height: 100%;
          }
          .qrMain_code_cover {
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            position: absolute;
            top: 0;
            left: 0;
            z-index: 9;
            color: #ffffff;
            text-align: center;
            p {
              font-size: 19px;
              font-weight: 700;
              margin-top: 65px;
              margin-bottom: 40px;
            }
            .qrMain_code_cover_p {
              margin-top: 90px;
            }
            div {
              width: 100px;
              line-height: 34px;
              font-size: 13px;
              font-weight: 700;
              letter-spacing: 1px;
              background: #e2231b;
              border-radius: 3px;
              margin: 0 auto;
              cursor: pointer;
            }
          }
        }
        .qrMain_title {
          color: #999999;
          font-size: 13px;
          font-weight: 700;
          margin-top: 17px;
          margin-bottom: 12px;
        }
        .qrMain_tips {
          color: #999999;
          display: flex;
          align-items: center;
          justify-content: center;
          .qrMain_tips_item {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            &:last-child {
              margin-right: 0;
            }
            img {
              position: relative;
              top: 1px;
              width: 15px;
            }
            span {
              margin-left: 6px;
            }
          }
        }
      }

      .top {
        margin-top: 40px;
        padding: 0 20px;
        display: flex;
        width: 356px;
        cursor: default;

        .item {
          flex: 1;
          text-align: center;
          font-size: 18px;
          color: #999999;
          cursor: pointer;
          position: relative;

          &:first-child {
            padding: 16px 45px 18px 35px;
            &.active {
              &:after {
                position: absolute;
                left: 50%;
                bottom: 0;
                margin-left: -45px;
                content: "";
                width: 80px;
                height: 1px;
                background-color: var(--color_main);
              }
            }
          }

          &:last-child {
            padding: 16px 35px 18px 45px;
            &:before {
              position: absolute;
              left: 0;
              top: 12px;
              bottom: 12px;
              width: 2px;
              height: auto;
              display: block;
              content: " ";
              font-size: 0;
              background: #eee;
            }
            &.active {
              &:after {
                position: absolute;
                left: 50%;
                bottom: 0;
                margin-left: -35px;
                content: "";
                width: 80px;
                height: 1px;
                background-color: var(--color_main);
              }
            }
          }

          &.active {
            color: #333333; /* var(--color_main); */
            font-weight: 700;
          }
        }
      }

      .center {
        padding: 30px 30px 40px;

        .item {
          position: relative;
          margin-top: 15px;
          border-radius: 2px;

          &:first-child {
            margin-top: 0;
          }

          .icon {
            position: absolute;
            left: 1px;
            top: 1px;
            width: 50px;
            text-align: center;
            height: 38px;
            background: #f8f8f8;

            .input {
              border: 1px solid #e8e8e8;
              height: 40px;
              padding: 0 44px 0 60px;
              width: 296px;
            }
          }

          .input {
            border: 1px solid #e8e8e8;
            height: 40px;
            padding: 0 44px 0 60px;
            width: 296px;
          }

          &.code {
            .input {
              padding-right: 10px;
              width: 150px;
            }
          }

          .img_code {
            position: absolute;
            right: 0;
            top: 0;
            border: 1px solid #eee;
            border-left: 0;
            width: 80px;
            height: 40px;
          }
        }

        .cancel {
          position: absolute;
          right: 0;
          top: 1px;
          width: 44px;
          height: 38px;
          cursor: pointer;

          :before {
            position: absolute;
            top: 9px;
            left: 14px;
          }
        }

        .send_code {
          position: absolute;
          right: 0;
          top: 0;
          background: #f9f9f9;
          border: 1px solid #eee;
          border-left: 0;
          width: 80px;
          height: 40px;
          line-height: 40px;
          text-align: center;
          color: #000;

          :hover {
            color: var(--color_main);
          }
        }

        .error {
          margin-top: 10px;
          position: relative;
          color: $colorMain;
          height: 16px;
          line-height: 16px;
        }

        .login_btn {
          display: block;
          margin-top: 35px;
          background: var(--color_main_bg);
          color: #fff;
          text-align: center;
          border-radius: 2px;
          height: 45px;
          line-height: 45px;
          font-size: 18px;
          letter-spacing: 5px;

          &:hover {
            opacity: 0.9;
          }
        }
      }

      .bottom {
        height: 51px;
        background: #fcfcfc;
        border-top: 1px solid #eee;
        position: absolute;
        width: 100%;
        bottom: 0;
        left: 0;
        display: flex;
        padding: 0 32px;
        box-sizing: border-box;

        a {
          display: block;
          line-height: 50px;
          margin-right: 10px;
          color: #999;

          &:hover {
            color: var(--color_main);
          }
        }

        img {
          width: 28px;
          height: 28px;
        }
      }
    }
  }
}
