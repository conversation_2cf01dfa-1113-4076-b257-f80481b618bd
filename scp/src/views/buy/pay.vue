<!--
 * @Author: wangwei
 * @Date: 2020-12-29 15:56:16
 * @LastEditTime: 2021-01-27 12:24:12
 * @LastEditors: Please set LastEditors
 * @Description: 订单支付页面
 * @FilePath: /java-pc/src/views/buy/pay.vue
-->
<template>
	<div id="pay">
		<div class="top_info">
			<div class="top_info_header">
				<div class="top_logo">
					<router-link to="/" replace>
						<div :style="`background-image:url(${configInfo.supplier_main_site_logo})`"></div>
					</router-link>
				</div>
				<div class="top_info_progress flex_row_end_center">
					<div class="progress_item" v-if="ifcart == 'true'">
						<div class="progress flex_row_center_center">
							<span class="active">1</span>
							<div class="progress_line">
								<div :class="{ content: true, active: true }"></div>
							</div>
						</div>
						<p>{{ L['购物车'] }}</p>
					</div>

					<div class="progress_item">
						<div class="progress flex_row_center_center">
							<span class="active">{{ ifcart == 'true' ? 2 : 1 }}</span>
							<div class="progress_line">
								<div :class="{ content: true, active: true, current: false }"></div>
							</div>
						</div>
						<p style="margin-left: -11px">{{ L['确认订单'] }}</p>
					</div>

					<div class="progress_item">
						<div class="progress flex_row_center_center">
							<span class="active">{{ ifcart == 'true' ? 3 : 2 }}</span>
							<div class="progress_line">
								<div :class="{ content: true, active: payComplete }"></div>
							</div>
						</div>
						<p>{{ L['付款'] }}</p>
					</div>

					<div class="progress_item" style="margin-left: -24px">
						<div class="progress flex_row_end_center">
							<span :class="{ active: payComplete }">{{
						ifcart == 'true' ? 4 : 3
					}}</span>
						</div>
						<p>{{ L['支付成功'] }}</p>
					</div>
				</div>
			</div>
			<div class="top_info_text flex_row_between_between">
				<span>{{ L['订单提交成功，请您尽快付款！ 订单号：'] }}
					{{ pay_info.data.paySn }}</span>
				<div class="price">
					{{ L['应付金额'] }}
					<span>{{ pay_info.data.needPay }}</span> {{ L['元'] }}
				</div>
			</div>
			<div class="top_info_text flex_row_between_between">
				<span>
					{{ L['请您在提交订单后'] }}
					<span>{{ L['24小时内'] }}</span>{{ L['完成支付，否则订单会自动取消'] }}
				</span>
				<div class="show_detail pointer" @click="showDetail">
					{{ show_detail ? L['收起详情'] : L['展开详情'] }}
				</div>
			</div>
			<div class="receive_info">
				<p v-if="show_detail &&
						pay_info.data.receiveAddress &&
						pay_info.data.receiverName
						">
					{{ L['收货地址：'] }}{{ pay_info.data.receiveAddress }}
					{{ L['收货人：'] }}{{ pay_info.data.receiverName }}
					{{ pay_info.data.receiverMobile }}
				</p>
				<p v-if="show_detail">{{ L['商品名称：'] }}{{ buy_name_list }}</p>
			</div>
		</div>
		<div class="bg">
			<div class="pay_method" v-if="hasPayMethod">
				<div v-if="!show_wx_pay">
					<div class="balance other_pay_method" v-if="other_pay_list.data.length > 0">
						<img class="logo" :src="otherPay" alt />
						<div class="balance_info flex">
							<div :class="{ other_pay_item: true, flex_row_around_center: true, wechat: index != 0 }"
								v-for="(item, index) in other_pay_list.data" :key="index" @click="changePayMethod(item)">
								<i
									:class="{ iconfont: true, 'iconyuanquan1': current_pay_method.data.payMethod != item.payMethod, 'iconduihao1': current_pay_method.data.payMethod == item.payMethod }"></i>
								<img :src="payIcon[item.payMethod]" alt />
								<span>{{ item.payMethodName }}</span>
							</div>
						</div>
					</div>
					<div class="pay" @click="submitPay" style="cursor: pointer;">
						{{ L['立即支付'] }}
					</div>
				</div>
				<div class="wx_pay_con" v-if="show_pay">
					<div class="title" @click="hideWxPay">
						<div style="cursor: pointer;display:inline">
							<i class="iconfont iconziyuan2"></i>
							<span>{{ L['选择其它支付方式'] }}</span>
						</div>
					</div>
					<div class="qr flex_row_around_center">
						<div class="left flex_column_start_center">

							<!-- dev_tld-start -->
							<img :src="wxPayQrImg" alt="" v-if="current_pay_method.data.payMethod == 'wx'" />
							<!-- dev_tld-end -->

							<!-- dev_tl-start -->
							<div class="img flex_row_center_center" id="qr_img" v-if="current_pay_method.data.payMethod != 'wx'">
							</div>
							<!-- dev_tl-end -->

							<p>
								{{ L['使用'] }}
								<span v-if="show_pay == 'wx'">{{ L['微信支付'] }}</span>
								<span v-if="show_pay == 'ali'">{{ L['支付宝支付'] }}</span>
								APP
							</p>
							<p>{{ L['扫码完成支付'] }}</p>
							<div class="refresh" @click="refreshWxQrcode">
								{{ L['刷新'] }}
							</div>
						</div>
						<img class="wx_png" :src="wx_pay_tip" v-if="show_pay == 'wx'" />
					</div>
				</div>

			</div>
			<div class="no_payMethod" v-else>
				<img src="@/assets/member/member_empty.png" alt="" />
				<p>{{ L['暂无可用的支付方式，平台正在紧急处理中～'] }}</p>
			</div>
		</div>

	</div>
</template>

<script>
import { reactive, getCurrentInstance, ref, onMounted, onUnmounted } from 'vue'
import { ElMessage, } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { preventMutiClick } from '@/utils/common'
import { qrcanvas } from 'qrcanvas'

export default {
	name: 'Pay',
	components: {
	},
	setup () {
		const ifcart = ref('')
		const balance = require('../../assets/buy/balance.png')
		const otherPay = require('../../assets/buy/otherPay.png')
		const ali = require('../../assets/buy/ali.png')
		const wechat = require('../../assets/buy/wechat.png')
		const banks = require('@/assets/allinpay/bank_card.png')
		const wx_pay_tip = require('../../assets/buy/wx_pay_tip.png')
		let continueExecFunc = null
		const route = useRoute()
		const router = useRouter()
		const store = useStore()
		const { proxy } = getCurrentInstance()
		const L = proxy.$getCurLanguage()
		const pay_method = reactive({ data: [] })
		const pay_info = reactive({ data: {} })
		const paySn = ref('')
		const current_pay_method = reactive({ data: {} })
		const balance_list = reactive({ data: [] })
		const other_pay_list = reactive({ data: [] })
		const buy_name_list = ref('')
		const password = ref('')
		const wxPayQrImg = ref('') //微信支付二维码
		const timer = ref(0) //定时器
		const show_detail = ref(true)
		const hasPayMethod = ref(true)
		const configInfo = ref(store.state.configInfo)
		const memberInfo = ref(store.state.memberInfo)
		const show_pay = ref('')

		const payIcon = {
			tl_alipay: ali,
			tl_wx: wechat,
			alipay: ali,
			wx: wechat,
			tl_bank: banks
		}

		const defaultImg = ref(
			'this.src="' + require('../../assets/common_top_logo.png') + '"'
		)
		//获取支付方式
		const getPayMethod = () => {
			proxy
				.$get('v3/system/common/payMethod', {
					source: 'pc',
					type: 1
				})
				.then(res => {
					if (res.state == 200) {
						hasPayMethod.value = res.data.length > 0 ? true : false
						res.data.map(item => {
							if (item.payMethod == 'balance' || item.payMethod == "tl_bal") {
								balance_list.data.push(item)
							} else {
								other_pay_list.data.push(item)
							}
						})
					} else {
						ElMessage(res.msg)
					}
				})
		}
		//获取订单支付数据
		const getPayInfo = () => {
			proxy
				.$get('v3/supplier/channel/orderPay/payInfo', {
					paySn: route.query.paySn,
					payFrom: route.query.payFrom
				})
				.then(res => {
					if (res.state == 200) {
						pay_info.data = res.data
						buy_name_list.value = res.data.goodsNameList.join(' + ')
					} else if (res.state == 267) {
						ElMessage.success(res.msg + L[',2s后自动跳转订单列表'])
						//清除定时器
						if (timer.value) {
							clearInterval(timer.value)
							timer.value = 0
						}
						setTimeout(() => {
							router.replace({
								path: '/member/order/list'
							})
						}, 2000)
					} else {
						ElMessage(res.msg)
					}
				})

		}
		const changePayMethod = pay_method => {
			current_pay_method.data = pay_method
		}
		//去支付
		const payComplete = ref(false)

		const pay = (continueExec) => {
			continueExecFunc = continueExec()
			if (!current_pay_method.data.payMethod) {
				ElMessage.warning(L['请选择支付方式'])
				continueExec()
				return
			}
			let param = {}
			param.payType = current_pay_method.data.payType
			param.payMethod = current_pay_method.data.payMethod
			param.paySn = pay_info.data.paySn

			if (timer.value) {
				clearInterval(timer.value)
				timer.value = null
			}


			//请求支付接口
			proxy.$sldStatEvent({ behaviorType: 'payApply', paySn: pay_info.data.paySn })
			proxy
				.$post('v3/supplier/channel/orderPay/doPay', param)
				.then(res => {
					continueExec()
					if (res.state == 200) {

						// dev_tld-start
						if (param.payMethod == 'balance') {
							ElMessage.success(L['支付成功,2s后自动跳转订单列表'])
							payComplete.value = true
							//清除定时器
							setTimeout(() => {
								router.replace({
									path: '/member/order/list'
								})
							}, 2000)
						} else if (param.payMethod == 'alipay') {
							document.write(res.data.payData) //自动提交表单数据
						} else if (param.payMethod == 'wx') {
							show_pay.value = 'wx'
							wxPayQrImg.value = 'data:image/png;base64,' + res.data.payData //微信支付二维码
							// 定时查询是否支付成功
							timer.value = setInterval(() => {
								queryPayState()
							}, 3000)
							//两小时自动跳转订单列表
							setTimeout(() => {
								router.replace({
									path: '/member/order/list'
								})
							}, *********)
						}
						// dev_tld-end

						// dev_tl-start
						if (param.payMethod == "tl_wx" || param.payMethod == "tl_alipay") {
							show_pay.value = param.payMethod == "tl_wx" ? 'wx' : 'ali';

							let canvas = qrcanvas({
								data: res.data.payData, //二维码内容
								size: 150,
								colorDark: "red",
							});
							setTimeout(() => {
								document.getElementById("qr_img").innerHTML = "";
								document.getElementById("qr_img").appendChild(canvas);
							}, 10)

							// 定时查询是否支付成功
							timer.value = setInterval(() => {
								queryPayState();
							}, 3000);
							//两小时自动跳转订单列表
							setTimeout(() => {
								router.replace({
									path: '/member/order/list'
								})
							}, *********)
						} else if (param.payMethod == "tl_bank") {
							window.location.href = res.data.payData
						}
						// dev_tl-end


					} else if (res.state == 267) {
						proxy.$confirm(res.msg, '提示', {
							confirmButtonText: '去设置',
							cancelButtonText: '取消',
							type: 'warning'
						}).then(() => {
							router.push('/member/pwd/pay')
						})
					} else {
						ElMessage(res.msg)
					}
				})
		}

		const submitPay = preventMutiClick(pay)

		// 定时查询是否支付成功
		const queryPayState = () => {
			if (!timer.value) return
			getPayInfo()
		}
		//隐藏微信支付内容
		const hideWxPay = () => {
			show_pay.value = ''
			clearInterval(timer.value)
			timer.value = null
			continueExecFunc()
		}
		//刷新微信支付二维码
		const refreshWxQrcode = () => {
			submitPay()
		}
		//收起展开详情
		const showDetail = () => {
			show_detail.value = !show_detail.value
		}

		const rechargeEnble = ref(false)
		const getRechargeEnble = () => {
			proxy.$get('v3/system/common/getSettings', {
				names: 'recharge_is_enable'
			}).then(res => {
				if (res.state == 200) {
					rechargeEnble.value = res.data[0] == '1'
				}
			})
		}


		onMounted(() => {
			paySn.value = route.query.paySn
			ifcart.value = route.query.ifcart
			getPayMethod()
			getPayInfo()
			getRechargeEnble()
		})
		const goRecharge = () => {
			router.push('/member/recharge')
		}
		const forgetPassword = () => {
			router.push('/member/pwd/reset')
		}
		onUnmounted(() => {
			if (timer.value) {
				clearInterval(timer.value)
				timer.value = null
			}
		})
		return {
			payIcon,
			L,
			pay_method,
			balance,
			otherPay,
			ali,
			wechat,
			pay_info,
			current_pay_method,
			balance_list,
			other_pay_list,
			changePayMethod,
			buy_name_list,
			password,
			pay,
			wx_pay_tip,
			hideWxPay,
			refreshWxQrcode,
			wxPayQrImg,
			goRecharge,
			forgetPassword,
			showDetail,
			show_detail,
			hasPayMethod,
			configInfo,
			defaultImg,
			memberInfo,
			payComplete,
			rechargeEnble,
			submitPay,
			show_pay,
		}
	},

	beforeRouteEnter (to, from, next) {
		// ...
		if (from.name == 'Login') {
			next('/index')
		} else {
			next()
		}
	}
}
</script>

<style lang="scss">
@import '../../style/pay.scss';
@import '../../style/base.scss';

.refresh {
	cursor: pointer;
}

.no_payMethod {
	margin: 0 auto;
	width: 1200px;
	height: 560px;
	background-color: #fff;
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;

	img {
		width: 190px;
		height: 140px;
	}

	p {
		margin-top: 39px;
		font-size: 14px;
		font-family: Microsoft YaHei;
		font-weight: 400;
		color: #333333;
	}
}
</style>
