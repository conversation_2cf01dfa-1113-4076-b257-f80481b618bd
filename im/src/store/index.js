/*
 * @Author: your name
 * @Date: 2021-01-08 21:09:07
 * @LastEditTime: 2021-01-15 11:36:36
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /java-pc/src/store/index.js
 */
import Vuex from 'vuex';
import { getLocalStorageObject, getLocalStorageStr } from '../utils/common';

export default Vuex.createStore({
  state: {
    loginFlag: getLocalStorageStr('loginFlag') ? true : false,
    sellerInfo: getLocalStorageObject('sellerInfo') || {},//店铺信息
    adminInfo: getLocalStorageObject('adminInfo') || {},//平台信息
    loginInfo: getLocalStorageObject('loginInfo') || {},//登录信息
    msgVoiceSetting: getLocalStorageStr('msgVoiceSetting'),//接收新消息的提示音设置
    identity: getLocalStorageStr('identity') || 'ID_NULL'
  },
  mutations: {

    //更新系统配置信息
    updateConfigInfo(state, payload) {
      state.configInfo = payload;
      localStorage.setItem('configInfo', JSON.stringify(state.configInfo));
    },
    //更新店铺信息
    updateSellerInfo(state, payload) {
      state.sellerInfo = payload;
      localStorage.setItem('sellerInfo', JSON.stringify(state.sellerInfo));
    },

    //更新店铺信息
    updateAdminInfo(state, payload) {
      state.adminInfo = payload;
      localStorage.setItem('adminInfo', JSON.stringify(state.adminInfo));
    },


    //声音设置
    updateMsgVoiceSetting(state, payload) {
      state.msgVoiceSetting = payload;
      localStorage.setItem('msgVoiceSetting', payload);
    },

    //传入登录参数时更新token
    loginUpdate(state, payload) {
      state.loginInfo.sld_token = payload.sld_token
      state.loginInfo.sld_refresh_token = payload.sld_refresh_token
      state.loginInfo.time = payload.time
      localStorage.setItem('loginInfo', JSON.stringify(payload));
    },

    setIdentity(state, payload) {
      state.identity = payload
      localStorage.setItem('identity', payload);
    },


    //清空vuex里所有数据
    clearAllData(state) {
      localStorage.removeItem('loginInfo')
      localStorage.removeItem('adminInfo')
      localStorage.removeItem('loginFlag')
      localStorage.removeItem('sellerInfo')
      localStorage.removeItem('identity')
      localStorage.removeItem('loginFlag')
    }
  }
})
