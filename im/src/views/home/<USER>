<template>
    <div class="common-layout" v-loading="isLoading">
        <headBar v-if="!isLoading"></headBar>
        <el-container>
            <sideBar></sideBar>
            <el-container :style="{ maxWidth: `${clientWidth - 150}px`, minWidth: `${clientWidth - 150}px` }"
                id="elContainer" v-if="!isLoading">
                <el-main style="margin:10px">
                    <router-view @getCurMemberId="getCurMemberId"></router-view>
                </el-main>
            </el-container>
        </el-container>
    </div>
</template>

<script>
import headBar from "@/components/headBar";
import sideBar from "@/components/sideBar";
import '@/style/base.scss';
import { getIdentity } from "@/utils/common";
import canAutoPlay from 'can-autoplay';
import { ElMessageBox } from 'element-plus';
import { getCurrentInstance, h, onMounted, onUnmounted, provide, reactive, ref } from "vue";
import { useRouter } from "vue-router";
import { useStore } from "vuex";

export default {
    name: 'Home',
    components: {
        sideBar,
        headBar
    },
    beforeCreate() {
        this.$socket.emit("connect_success", this.connectBaseData);
        this.sockets.subscribe('contact_change', (e) => {
            if (this.curMemberInfo.memberId != e.memberId || e.memberId == -1) {
                if ((localStorage.msgVoiceSetting === true || localStorage.msgVoiceSetting === 'true')) {
                    this.play();
                }
            } else {
                document.title = 'slodon'
            }
        });
    },

    created() {
        this.sockets.subscribe('connect', (e) => {
            this.$socket.emit("connect_success", this.connectBaseData, (e) => {
                if (this.isDisConnect) {
                    this.$socket.emit("vendor_change_room", { memberId: this.curMemberInfo.memberId, ...this.connectBaseData }, () => { });
                    this.isDisConnect = false
                }
            });
        });
        this.sockets.subscribe('disconnect', (e) => {
            this.isDisConnect = true
        });
    },

    setup() {
        const isDisConnect = ref(false)
        const store = useStore();
        const { proxy } = getCurrentInstance();
        const router = useRouter();
        const sellerInfo = reactive({ data: store.state.sellerInfo });//店铺基本信息
        const adminInfo = reactive({ data: store.state.adminInfo });//平台基本信息
        const clientWidth = ref(0)
        const connectBaseData = {
            storeId: getIdentity('seller') ? sellerInfo.data.storeId : 0,
            userId: getIdentity('seller') ? sellerInfo.data.vendorId || 1 : adminInfo.data.adminId || 1,
            role: 2
        };
        const L = proxy.$getCurLanguage()
        const curMemberInfo = ref({})
        const isLoading = ref(true)

        provide('adminInfo', adminInfo)
        provide('sellerInfo', sellerInfo)



        //获取店铺的基本信息
        const getStoreBaseInfo = () => {
            proxy.$get('v3/seller/seller/store/indexStoreInfor').then(res => {
                if (res.state == 200) {
                    sellerInfo.data = Object.assign(sellerInfo.data, res.data)
                    store.commit("updateSellerInfo", sellerInfo.data);
                    isLoading.value = false

                }
            })
        }


        //获取声音的设置
        const getVoiceSetting = () => {
            proxy.$post(`v3/helpdesk/$IDENTITY$/sound/setting/isOpen`).then(res => {
                if (res.data) {
                    store.commit("updateMsgVoiceSetting", res.data.isOpen ? true : false);
                }
            })
        }


        const getAdminSetting = () => {
            proxy.$get('v3/system/admin/setting/getSettingList', {
                str: 'platform_customer_service_name,platform_customer_service_logo'
            }).then(res => {
                if (res.state == 200) {
                    let data = {}
                    if (res.data[0].value) {
                        data.adminUserName = res.data[0].value
                    }
                    if (res.data[1].imageUrl) {
                        data.admin_logo = res.data[1].imageUrl
                    }
                    adminInfo.data = Object.assign(adminInfo.data, data)
                    store.commit('updateAdminInfo', adminInfo.data)
                    isLoading.value = false
                }
            })
        }




        const getCurMemberId = (memberInfo) => {
            curMemberInfo.value = memberInfo
        }


        // 消息提示音
        const play = () => {
            console.log('playHome')
            let audioElement = document.createElement('audio');
            let voice = require('@/assets/voice/msg.mp3')
            audioElement.setAttribute('src', voice);
            audioElement.setAttribute('autoplay', 'autoplay');
        }

        router.beforeEach((to, from, next) => {
            // to and from are both route objects. must call `next`.
            if (from.name == 'service') {
                proxy.$socket.emit("vendor_change_room", {
                    oldMemberId: curMemberInfo.value.oldMemberId,
                    memberId: -1,
                    ...connectBaseData
                })
            }
            next()
        })


        const detectAutoPlay = () => {
            canAutoPlay.audio().then(res => {
                if (!res.result) {
                    ElMessageBox({
                        title: '提示',
                        message: h('div', null, [
                            h('p', null, '您的浏览器没有开启自动播放声音的功能，点击确认后可接收消息提示音。'),
                            h('p', null, '也可以点击浏览器地址前面的图标->网站设置->声音，选择允许，下次可以自动接收消息提示音。')
                        ]),
                        confirmButtonText: '确认',
                    }).then(() => {

                    })
                }
            })
        }

        //打开seller的客服的话，admin监听缓存变化，提示这个，让后提示刷新
        const detectStorage = () => {
            window.addEventListener('storage', function (e) {
                if (e.key == 'identity' && e.newValue != e.oldValue) {
                    proxy.$alert('客服环境已发生变化，请刷新', '提示', {
                        confirmButtonText: '确定',
                        type: 'warning'
                    }).then(() => {
                        window.location.replace(e.url)
                    })
                }
            })

            window.onbeforeunload = () => {
                proxy.$socket.disconnect()
            }
        }

        onMounted(() => {
            if (getIdentity('seller')) {
                getStoreBaseInfo();
            } else {
                getAdminSetting()
            }
            getVoiceSetting();
            detectAutoPlay()
            proxy.$socket.emit("connect_success", connectBaseData);
            clientWidth.value = document.body.offsetWidth || document.documentElement.offsetWidth
            detectStorage()
        })

        onUnmounted(() => {
            proxy.$socket.disconnect()
        })

        return {
            isLoading,
            clientWidth, L, getIdentity,
            curMemberInfo, play, isDisConnect,
            adminInfo, connectBaseData, getCurMemberId
        }
    }
}
</script>


<style lang="scss">
.el-container {
    min-width: unset !important;
}
</style>

<style lang="scss" scoped>
.el-header,
.el-footer {
    background-color: #fff;
}

.el-header {
    padding-left: 0 !important;
    padding-right: 10px;


}

.el-aside {
    color: #fff;
    text-align: center;
    background-color: #1D2647;
    padding-top: 3px;
}

.el-main {
    background-color: #fff;
    height: calc(100vh - 70px);
    padding: 15px;
}

.el-container {
    background: #f0f2f5;
}

.el-menu {
    height: calc(100vh - 50px);
    background-color: #1D2647;
}

.el-menu-item,
.el-menu-item i {
    color: #fff;
}

.el-menu-item:hover {
    background-color: #fff;

    .el-menu-item,
    .el-menu-item i {
        color: #1D2647;
    }
}

.el-menu-item.is-active {
    color: #1D2647;
}

.el-container:nth-child(5) .el-aside,
.el-container:nth-child(6) .el-aside {
    line-height: 260px;
}

.el-container:nth-child(7) .el-aside {
    line-height: 320px;
}

.el-menu-vertical-demo:not(.el-menu--collapse) {
    width: 150px;
    min-height: 400px;
}
</style>
