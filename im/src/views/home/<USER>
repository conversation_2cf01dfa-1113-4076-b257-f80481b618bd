<template>
	<div>

	</div>
</template>

<script>
import { useStore } from 'vuex'
import { onMounted, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus';
export default {
	setup () {
		const store = useStore()
		const platInfo = ref({})
		const router = useRouter()
		const route = useRoute()
		const listenMessage = () => {
			let jmpFlag = false
			if (route.query.source) {
				const source = new URL(route.query.source)
				window.opener?.postMessage('im_store_create', source.origin)
				window.onmessage = (e) => {
					const eventData = e.data
					if (eventData && eventData.identity && e.origin === source.origin) {
						store.commit('setIdentity', eventData.identity)
						store.commit('loginUpdate', {
							...eventData.tokenInfo,
							time: new Date().getTime().toString()
						})
						platInfo.value = eventData.platInfo
						let storeEventName = eventData.identity == 'admin' ? 'updateAdminInfo' : 'updateSellerInfo'
						jmpFlag = true
						store.commit(storeEventName, platInfo.value)
						router.replace('/service')
					}
				}
			}

			if (route.query.immidiate) {
				router.replace('/service')
			}

			setTimeout(() => {
				if (!jmpFlag) {
					router.replace('/service')
				}
			}, 5000)
		}


		onMounted(() => {
			const { identity } = store.state
			listenMessage()
		})
	}
}
</script>

<style lang="scss" scoped></style>