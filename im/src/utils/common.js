import {
    apiUrl,
    pcUrl,
    sourceUrl,
} from "@/utils/config";
import { lang_en } from '../assets/language/en';
import { lang_zn } from '../assets/language/zh';
import store from '../store';
import { curLang } from './config';



/*
* 获取缓存的值——String类型
* 返回值字符串  有值返回具体的值，否则返回空字符串
* @zjf-2020-11-26
* */
export function getLocalStorageStr (key) {
    let val = localStorage.getItem(key);
    return val != undefined && val != null && val ? val : '';
}

/*
* 获取缓存的值——Object类型
* 返回值对象  有值返回具体的值，否则返回空对象
* @zjf-2020-11-26
* */
export function getLocalStorageObject (key) {
    let val = localStorage.getItem(key);
    return val != undefined && val != null && val ? JSON.parse(val) : {};
}

/*
* 获取当前语言下的数据 —— Object类型
* 返回对象  语言数据对象
* @zjf-2020-12-28
* */
export function getCurLanguage () {
    const language = {
        'zh': lang_zn,
        'en': lang_en,
    }


    return language[curLang];
}


//返回左边一个竖线，右侧文字的结构_添加商品(字体大小，颜色变化) 增加上下左边的距离
export function sldLlineRtextAddGoodsAddMargin (leftColor, Rtext, ml = 0, mt = 0, mb = 0) {
    return `<div style=" margin-left: ${ml}px; margin-top: ${mt}px; margin-bottom: ${mb}px;display: flex;color: #101010;font-size: 14px;font-weight: 700;justify-content: flex-start;align-items: center;height: 20px">
            <span style="background: ${leftColor};display: inline-block;width: 4px;height: 16px;margin-right: 7px;display: inline-block;"></span>
            <span style="color: #333;font-size: 14px;font-weight: 700;display: inline-block;vertical-align: middle;width: 200px">${Rtext}</span>
        </div>`;
}



/*
 * 判断是否显示聊天页面的时间,2条消息之间间隔超过3分钟显示
 * 返回Boolean类型
 * preMsgTime 上一条消息的发送时间，curMsgTime该条消息的发送时间
 * @zjf-2021-03-05
 * */
export function isShowTime (preMsgTime, curMsgTime) {
    let res = false;
    if (Date.parse(new Date(curMsgTime)) * 1 - Date.parse(new Date(preMsgTime)) * 1 > 3 * 60 * 1000) {
        res = true;
    }
    return res;
}

/*
 * 格式化聊天时间
 * 返回格式化后的数据，字符串类型
 * time 时间戳 13位
 * @zjf-2021-03-05
 * */
export function formatChatTime (time) {
    return format(new Date(time), 'yyyy年MM月dd日 hh:mm');
}

export function format (date, fmt) {
    let o = {
        "y+": date.getFullYear(), //年
        "M+": date.getMonth() + 1, //月份
        "d+": date.getDate(), //日
        "h+": date.getHours(), //小时
        "m+": date.getMinutes(), //分
        "s+": date.getSeconds(), //秒
        "q+": Math.floor((date.getMonth() + 3) / 3), //季度
        "S": date.getMilliseconds() //毫秒
    };
    if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
    }
    for (let k in o) {
        if (new RegExp("(" + k + ")").test(fmt)) {
            fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        }
    }
    return fmt;
}

/**
 * 获取浏览器地址参数
 * variable 为参数名，存在的话返回具体值，否则返回false
 *
 * @zjf-2020-11-17
 */
export function getQueryVariable (variable) {
    let query = window.location.search.substring(1);
    let vars = query.split("&");
    for (let i = 0; i < vars.length; i++) {
        let pair = vars[i].split("=");
        if (pair[0] == variable) {
            return pair[1];
        }
    }
    return false;
}

//查询当前是不是这个身份
export function getIdentity (id) {
    return store.state.identity === id
}

//获取最新身份
export function useIdentity () {
    return store.state.identity || 'seller'
}
//根据身份判断当前接口地址和网址地址
export function getCurrentUrl () {
    const identity = localStorage.getItem('identity') || 'seller'
    return {
        apiUrl: apiUrl[identity],
        sourceUrl: sourceUrl[identity]
    }
}



//获取admin的 详情路径的 其中一部分
const getOpenUrlType = (type) => {

    let typeSet = {
        goods: 'manage_product',
        order: 'manage_order'
    }

    return typeSet[type]
}


//跳转详情
export const toDetail = (item, type) => {
    const { sourceUrl } = getCurrentUrl()

    switch (type) {
        case 'order': {
            let url
            if (getIdentity('admin')) {
                url = `${sourceUrl}/${getOpenUrlType('order')}/order_lists_to_detail?orderSn=${item.orderSn}`
            } else {
                url = `${sourceUrl}/order/order_lists_to_detail?orderSn=${item.orderSn}`
            }
            window.open(url, '_blank')
            break;
        }

        case 'goods': {
            let url
            if (pcUrl) {
                url = `${pcUrl}goods/detail?productId=${item.productId}`
            } else {
                if (getIdentity('admin')) {
                    url = `${sourceUrl}/${getOpenUrlType('goods')}/goods_list_to_detail?id=${item.goodsId}`
                } else {
                    url = `${sourceUrl}/goods/goods_list_to_edit?id=${item.goodsId}`
                }
            }
            window.open(url, '_blank')
            break;
        }
    }
}

