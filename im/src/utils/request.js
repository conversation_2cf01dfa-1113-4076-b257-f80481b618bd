import { getCurrentUrl } from '@/utils/common';
import axios from 'axios';
import qs from "qs";
import Store from '../store';

const instance = axios.create({
    baseURL: getCurrentUrl().apiUrl,
    timeout: 10000
})

//跳转登录页
const backToLogin = () => {
    const { sourceUrl } = getCurrentUrl()
    let des_url = window.location.href;
    console.warn('des_url:',des_url,sourceUrl)
    if (des_url.indexOf('redirect=') != -1) {
        des_url = des_url.substring(0, des_url.indexOf('redirect=') - 1);
    }
    console.warn('redirct:',sourceUrl + '/login?redirect=' + encodeURIComponent(des_url))
    window.location.href = sourceUrl + '/login?redirect=' + encodeURIComponent(des_url);
    window.reload();
}

export const get = async (url, data = {}) => {
    let res = await initRequestHeader('get', url, data);
    data = res.data;
    const { identity } = Store.state
    const trueUrl = url.replace(/\$IDENTITY\$/g, identity)
    return new Promise((resolve, reject) => {
        instance.get(trueUrl, { params: data, headers: res.headers }).then((response) => {
            if (response.data.state == 266) {
                backToLogin()
            } else {
                resolve(response.data)
            }
        }, err => {
            reject(err)
        })
    })
}

export const post = async (url, data = {}, type = 'urlencoded') => {
    let res = await initRequestHeader('post', url, data, type);
    const { identity } = Store.state
    const trueUrl = url.replace(/\$IDENTITY\$/g, identity)
    return new Promise((resolve, reject) => {
        instance.post(trueUrl, res.data, {
            headers: res.headers
        }).then((response) => {
            if (response.data.state == 266) {
                backToLogin()
            } else {
                resolve(response.data)
            }
        }, err => {
            reject(err)
        })
    })
}

/**
 * 初始化请求头和数据
 * @zjf-2020-12-25
 */
const initRequestHeader = async (method, url, data = {}, type = 'urlencoded') => {
    let result = {};
    let headers = {};
    const { identity, loginInfo } = Store.state

    //用户登录状态下，每次更新refresh_token58分钟之后需要更新access_token
    if (loginInfo.sld_refresh_token) {
        let cur_time = new Date().getTime();
        if ((loginInfo.time && (cur_time - loginInfo.time > 58 * 60 * 1000))) {
            let params = {
                'grant_type': 'refresh_token',
                'client_id': 'UW1KalUyVnNiR1Z5',
                'client_secret': 'U21GMllVQkRiM0I1VW1sbmFIUkFVMnh2Wkc5dQ==',
                'refresh_token': loginInfo.sld_refresh_token,
            };
            await instance.post(`v3/${identity}Login/oauth/token`, qs.stringify(params), {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                }
            }).then((response) => {
                if (response.data.state == 200) {
                    //存储token更新时间
                    Store.commit('loginUpdate', {
                        sld_token: response.data.data.access_token,
                        sld_refresh_token: response.data.data.refresh_token,
                        time: new Date().getTime().toString()
                    })
                }
            }, err => {
                console.log('更新access_token出错：', err);
            })
        }
    }

    if (method == 'post') {
        if (type == 'urlencoded') {
            headers = { 'Content-Type': 'application/x-www-form-urlencoded' };
            data = qs.stringify(data);
        } else if (type == 'json') {
            headers = { 'Content-Type': 'application/json' };
            data = JSON.parse(JSON.stringify(data));
        } else if (type == 'form') {//form-data
            headers = { 'Content-Type': 'multipart/form-data' };
            let tmp_data = new FormData();
            Object.keys(data).forEach((item) => {
                tmp_data.append(item, data[item])
            });
            data = tmp_data;
        }
    }

    if (url.indexOf(`v3/${identity}Login/oauth/token`) == -1) {
        let token = loginInfo.sld_token;
        headers.Authorization = 'Bearer ' + token;
    }

    result = { url, data, headers };
    return result;
}
