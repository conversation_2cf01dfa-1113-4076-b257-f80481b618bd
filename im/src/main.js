import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import { chatUrl } from './utils/config'
import 'normalize.css'
import './style/index.scss'
import { get, post } from './utils/request'
import {
  getLocalStorageStr, getCurLanguage, sldLlineRtextAddGoodsAddMargin, formatChatTime, isShowTime, setQueryVar
} from './utils/common';
import ElementPlus from 'element-plus';
import 'element-plus/lib/theme-chalk/index.css';
import locale from 'element-plus/lib/locale/lang/zh-cn';
import VueSocketIO from "vue-socket.io";

const app = createApp(App);

//VueSocketIO的install方法的参数vue的protoType是vue2的，重写一下把vue3的config.globalProperties赋给prototype
//就不会报'cannot set properties of undefined setting $socket'的错误
const olderInstall = VueSocketIO.prototype.install
VueSocketIO.prototype.install = function (Vue) {
  Vue.prototype = Vue.config.globalProperties
  olderInstall.apply(this, [Vue])
}

app.config.globalProperties.$get = get;
app.config.globalProperties.$post = post;
app.config.globalProperties.$getLocalStorageStr = getLocalStorageStr;
app.config.globalProperties.$getCurLanguage = getCurLanguage;
app.config.globalProperties.$sldLlineRtextAddGoodsAddMargin = sldLlineRtextAddGoodsAddMargin;
app.config.globalProperties.$formatChatTime = formatChatTime;
app.config.globalProperties.$isShowTime = isShowTime;
app.use(ElementPlus, { locale });
app.use(new VueSocketIO({
  debug: false,
  connection: chatUrl,
}))

app.use(router).use(store).mount('#app')


router.beforeEach(() => {
  window.scrollTo(0, 0)
})


