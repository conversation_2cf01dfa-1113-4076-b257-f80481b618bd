/*
 * @Author: your name
 * @Date: 2020-12-25 19:22:45
 * @LastEditTime: 2021-01-15 13:50:58
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /java-pc/src/router/index.js
 */
import { getCurrentUrl } from '@/utils/common';
import { createRouter, createWebHistory } from 'vue-router';
import Store from '../store';

function checkLogin (next, to) {
  console.warn('checkLogin...')
  const { sld_token } = Store.state.loginInfo;
  if (sld_token) {
    next()
  } else {
    let des_url = window.location.href;
    if (des_url.indexOf('redirect=') != -1) {
      des_url = des_url.substring(0, des_url.indexOf('redirect=') - 1);
    }
    console.warn('redirct:',getCurrentUrl().sourceUrl + '/login?redirect=' + encodeURIComponent(des_url))
    window.location.href = getCurrentUrl().sourceUrl + '/login?redirect=' + encodeURIComponent(des_url);
    window.reload();
  }
}

const routes = [{
  name: 'Home',
  component: () => import(/* webpackChunkName: "home" */ '../views/home/<USER>'),
  redirect: '/service',
  children: [
    {
      path: '/setting',
      name: 'Setting',    //聊天设置
      component: () => import(/* webpackChunkName: "home" */ '../views/home/<USER>'),
      beforeEnter (to, from, next) { checkLogin(next, to); }

    },
    {
      path: '/service',
      name: 'service',    //对话平台
      component: () => import('../views/chatService/chatPlatform/chatPage'),
      beforeEnter (to, from, next) { checkLogin(next, to); }

    },
    {
      path: '/history',
      name: 'history',   //聊天记录
      component: () => import('../views/chatService/chatHistory'),
      beforeEnter (to, from, next) { checkLogin(next, to); }

    },
    {
      path: '/autoReply',
      name: 'autoReply', //欢迎语
      component: () => import('../views/chatService/autoReply.vue'),
      beforeEnter (to, from, next) { checkLogin(next, to); }

    },
    {
      path: '/quickReply',
      name: 'quickReply',//快捷回复
      component: () => import('../views/chatService/quickReply.vue'),
      beforeEnter (to, from, next) { checkLogin(next, to); }

    },
    {
      path: '/casualQues',
      name: 'casualQues',//常见问题
      component: () => import('../views/chatService/casualQues'),
      beforeEnter (to, from, next) { checkLogin(next, to); }
    }
  ],
},
{
  path: '/redirect',
  name: 'redirect',
  component: () => import(/* webpackChunkName: "home" */ '../views/home/<USER>'),
}
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

router.beforeEach((to, from, next) => {
  next()
})

export default router
