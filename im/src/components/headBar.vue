<template>
	<el-header height="50px">
		<div class="flex_row_between_center">
			<div class="flex_row_start_center">
				<div class="flex_row_center_center img_logo_part">
					<img class="img_logo" :src="globalSetting.background_logo ? globalSetting.background_logo : imLogo" alt
						@click='goHome' />
				</div>
				<span class="header_title">{{ getIdentity('seller') ? '商户-客服助手' : '平台-客服助手' }}</span>
			</div>
			<div class="header_right flex_row_end_center">
				<div @click="pageClose" class="flex_row_start_center" style="cursor: pointer;" v-if="isShowBackBtn">
					<div class="back_img">
						<img src="@/assets/back.png" alt="">
					</div>
					<span class="back_text">返回管理后台</span>
				</div>

				<div class="right_tab">
					<div class="right_tab_vi flex_row_center_center">
						<span class="store_name">{{ displayName }}</span>
					</div>
					<div class="right_tab_hi flex_row_center_center" @click="loginOut">
						<span class="el-icon-switch-button"></span>
						<span class="tab_name">退出登录</span>
					</div>
				</div>
			</div>
		</div>
	</el-header>
</template>

<script>
import { getCurrentInstance, computed, reactive, onMounted } from "vue";
import { getIdentity, getCurrentUrl } from "@/utils/common"
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
export default {
	setup () {
		const router = useRouter()
		const store = useStore()
		const { proxy } = getCurrentInstance();
		const { sourceUrl } = getCurrentUrl()
		const L = proxy.$getCurLanguage()
		const imLogo = require("@/assets/im_logo.png");

		//全局设置
		const globalSetting = reactive({
			init: 'init'
		})

		const isShowBackBtn = computed(() => {
			const { sellerInfo } = store.state
			return getIdentity('admin') || getIdentity('seller') && sellerInfo.isStoreAdmin
		})

		const displayName = computed(() => {
			const { sellerInfo, adminInfo } = store.state
			return getIdentity('seller') ? `${sellerInfo.vendorName}：${sellerInfo.storeName}` : `${adminInfo.adminUserName}：${adminInfo.adminName}`
		})

		//退出登录功能
		const loginOut = () => {
			//调用商户的退出登录接口
			const { loginInfo } = store.state
			let sld_refresh_token = loginInfo.sld_refresh_token
			proxy.$post(`v3/$IDENTITY$Login/oauth/logout`, {
				grant_type: 'refresh_token',
				refresh_token: sld_refresh_token,
			}).then(res => {
				if (res.state == 200) {
					localStorage.clear();
					window.location.replace(sourceUrl + '/login?flag=exit');
				}
			})
		}

		const getGlobalSetting = () => {
			proxy.$get(`v3/system/$IDENTITY$/setting/getSettingList`, {
				str: 'customer_service_background_logo'
			}).then(res => {
				if (res.state == 200) {
					if (res.data[0].imageUrl) {
						globalSetting.background_logo = res.data[0].imageUrl
					}
				}
			})
		}

		const pageClose = () => {
			if (navigator.userAgent.indexOf("MSIE") > 0) {
				if (navigator.userAgent.indexOf("MSIE 6.0") > 0) {
					window.opener = null;
					window.close();
				} else {
					window.open('', '_top');
					window.top.close();
				}
			}
			else if (navigator.userAgent.indexOf("Firefox") > 0) {
				window.location.href = 'about:blank ';
			} else {
				window.opener = null;
				window.open('', '_self', '');
				window.close();
			}
		}

		const goHome = () => {
			router.push({
				path: "/service",
			});
		}

		onMounted(() => {
			getGlobalSetting()
		})

		return { L, isShowBackBtn, displayName, goHome, loginOut, pageClose, globalSetting, getIdentity, imLogo }
	}
}
</script>

<style lang="scss" scoped>
.img_logo_part {
	width: 150px;
	height: 50px;
	background: #1D2647;

	.img_logo {
		max-width: 37px;
		cursor: pointer;
		max-width: 140px;
	}
}

.header_title {
	display: inline-block;
	height: 50px;
	font-size: 20px;
	font-family: Microsoft YaHei;
	font-weight: bold;
	color: #114A97;
	line-height: 50px;
	margin-left: 20px;
}

.header_right {
	.login_out {
		cursor: pointer;

		&:hover {
			color: #1c94fa;
		}
	}
}


.back_text {
	margin-right: 20px;
	font-size: 14px;
	font-family: Source Han Sans CN;
	font-weight: 400;
	color: #A2A7BB;
}

.back_img {
	margin-right: 10px;
	cursor: pointer;

	img {
		width: 20px;
		height: 20px;
	}
}


.right_tab {
	position: relative;
	// min-width: 132px;
	// width: 132px;
	// max-width: 132px;

	&:hover {
		.right_tab_hi {
			display: flex;
		}

		.right_tab_vi {


			.store_name,
			.el-icon-caret-bottom {
				color: #333 !important;

			}

			.store_name {}
		}
	}

	.right_tab_vi {

		height: 50px;
		background: #0E6FD7;
		cursor: pointer;

		padding: 0 20px;

		.store_name {
			font-size: 16px;
			font-family: Microsoft YaHei;
			font-weight: 400;
			color: #FFFEFE;
		}

		.el-icon-caret-bottom {
			font-size: 14px;
			color: #FFFEFE;
			margin-left: 9px;
		}
	}

	.right_tab_hi {
		height: 51px;
		background: #F5F5F5;
		position: absolute;
		top: 50px;
		width: 100%;
		left: 0;
		font-size: 14px;
		font-family: Source Han Sans CN;
		font-weight: 400;
		color: #333333;
		cursor: pointer;
		display: none;

		.tab_name {
			margin-left: 8px;
			margin-top: 2px;
		}

		.el-icon-switch-button {
			font-weight: bold;
			font-size: 16px;
		}
	}
}
</style>