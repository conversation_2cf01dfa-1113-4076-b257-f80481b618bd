<template>
	<el-aside width="150px">
		<router-link :to="{ path: '/service' }" class="sld_left_nav_item">
			<i class="el-icon-postcard icon"></i>
			<span class="text">{{ L['对话平台'] }}</span>
		</router-link>
		<router-link :to="{ path: '/history' }" class="sld_left_nav_item">
			<i class="el-icon-chat-line-square icon"></i>
			<span class="text">{{ L['聊天记录'] }}</span>
		</router-link>
		<router-link :to="{ path: '/autoReply' }" class="sld_left_nav_item">
			<i class="el-icon-chat-dot-round icon"></i>
			<span class="text">{{ L['欢迎语'] }}&nbsp;&nbsp;&nbsp;&nbsp;</span>
		</router-link>
		<router-link :to="{ path: '/quickReply' }" class="sld_left_nav_item">
			<i class="el-icon-chat-line-round icon"></i>
			<span class="text">{{ L['快捷回复'] }}</span>
		</router-link>
		<router-link :to="{ path: '/casualQues' }" class="sld_left_nav_item">
			<i class="el-icon-collection icon"></i>
			<span class="text">{{ L['常见问题'] }}</span>
		</router-link>
		<router-link :to="{ path: '/setting' }" class="sld_left_nav_item">
			<i class="el-icon-set-up icon"></i>
			<span class="text">{{ L['聊天设置'] }}</span>
		</router-link>
	</el-aside>
</template>

<script>
import { getCurrentInstance, computed } from "vue";
import { getIdentity } from "@/utils/common"
import { useStore } from 'vuex'
export default {
	setup () {
		const store = useStore()
		const { proxy } = getCurrentInstance();
		const isStoreAdmin = computed(() => {
			const { sellerInfo } = store.state
			return getIdentity('seller') && sellerInfo.isStoreAdmin
		})

		const isAdminSuper = computed(() => {
			const { adminInfo } = store.state
			return getIdentity('admin') && adminInfo.isSuper
		})
		const L = proxy.$getCurLanguage()

		return {
			L,
			isStoreAdmin,
			isAdminSuper
		}
	}
}
</script>

<style lang="scss" scoped>
.sld_left_nav_item {
	width: 100%;
	display: block;
	color: #fff;
	height: 50px;
	line-height: 50px;
	background: #1D2647;

	.icon {
		color: #fff;
		font-size: 16px;
		margin-right: 10px;
		font-weight: bold;
	}

	.text {
		color: #fff;
		font-size: 14px;
	}

	&.router-link-active {
		background: #fff;

		.icon {
			color: #1D2647;
		}

		.text {
			color: #1D2647;
		}
	}
}
</style>