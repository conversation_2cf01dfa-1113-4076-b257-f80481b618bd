<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <link rel="icon" href="./favicon.ico">
  <title>
    <%= htmlWebpackPlugin.options.title %>
  </title>
  <script>
    var width = document.documentElement.clientWidth || document.body.clientWidth;
    var ratio = width / 375;
    var fontSize = 100 * ratio;
    document.getElementsByTagName('html')[0].style['font-size'] = fontSize + 'px';
  </script>


  <script>
    //在vue-router初始化之前先处理，及时将refresh_token获取并替换掉
    !function () {
      let { pathname, search } = window.location
      if (pathname !== '/redirect') {
        return
      }
      const param = new URLSearchParams(search)
      let targetParam = {
        time: new Date().getTime()
      }

      if (param.get('sld_token')) {
        targetParam.sld_token = param.get('sld_token')
        param.delete('sld_token')
      }

      if (param.get('sld_refresh_token')) {
        targetParam.sld_refresh_token = param.get('sld_refresh_token')
        param.delete('sld_refresh_token')
      }

      localStorage.setItem('loginInfo', JSON.stringify(targetParam));

      if (param.get('userInfo')) {
        let parsed = (param.get('userInfo'))
        let userInfo = (JSON.parse(parsed))
        param.delete('userInfo')
        let storageName = userInfo.adminId ? 'adminInfo' : 'sellerInfo'
        localStorage.setItem(storageName, JSON.stringify(userInfo));
        localStorage.setItem('identity', userInfo.adminId ? 'admin' : 'seller');
      }

      for (const [key, value] of param.entries()) {
        if (key !== 'immidiate' && key !== 'source') {
          param.delete(key)
        }
      }

      let newUrl = location.origin + location.pathname + '?' + param.toString()
      history.replaceState({}, '', newUrl)
    }()
  </script>


</head>

<body>
  <noscript>
    <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled.
        Please enable it to continue.</strong>
  </noscript>
  <div id="app"></div>
  <!-- built files will be auto injected -->
</body>

</html>