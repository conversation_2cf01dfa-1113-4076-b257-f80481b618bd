{"name": "slodon", "version": "0.1.0", "private": true, "scripts": {"serve": "cp src/utils/config_test.js src/utils/config.js&&vue-cli-service serve", "serve:prod": "cp src/utils/config_prod.js src/utils/config.js&&vue-cli-service serve", "build": "cp src/utils/config_prod.js src/utils/config.js&&vue-cli-service build", "build:test": "cp ./src/utils/config_test.js ./src/utils/config.js&&vue-cli-service build"}, "dependencies": {"axios": "^0.21.0", "can-autoplay": "^3.0.2", "core-js": "^3.6.5", "element-plus": "^1.0.1-beta.14", "normalize.css": "^8.0.1", "qrcanvas": "^3.1.2", "qs": "^6.13.0", "socket.io-client": "^2.1.1", "vue": "^3.0.0", "vue-router": "^4.0.0-0", "vue-socket.io": "^3.0.10", "vuex": "^4.0.0-0", "webpack": "4"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.19", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "@vue/eslint-config-standard": "^5.1.2", "babel-eslint": "^10.1.0", "babel-plugin-component": "^1.1.1", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^7.0.0-0", "sass": "^1.26.5", "sass-loader": "^8.0.2"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {"no-unused-vars": "off", "no-unreachable": "off"}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}