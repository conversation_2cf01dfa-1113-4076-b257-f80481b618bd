.sld_login {
  .sld_login_header {
    background: #fff;
    box-shadow: 0 3px 5px rgba(0, 0, 0, 0.05);

    .content {
      display: flex;
      padding: 20px 0;
      align-items: center;
      width: 1200px;
      margin: auto;

      .l_logo {
        cursor: pointer;
        display: flex;
        margin-right: 70%;

        .img {
          display: inline-block;
          vertical-align: top;
          max-width: 190px;
          max-height: 43px;
        }

        .text {
          font-size: 0;
          display: inline-block;
          vertical-align: top;
          line-height: 48px;
          margin-left: 5px;
        }
      }

      .r_register_wrap {
        font-size: 14px;

        .go_register_btn {
          font-size: 13px;
          display: inline-block;
          padding: 6px 22px;
          background: var(--color_main_bg);
          color: #fff;
          border-radius: 30px;
          margin-left: 7px;
          font-family: Microsoft YaHei;
          height: 30px;
          line-height: 17px;
        }
      }
    }
  }

  .sld_login_content {
    width: 100%;
    height: 600px;
    position: relative;
    overflow-x: hidden;
    .bg {
      position: absolute;
      margin: auto;
      width: 1920px;
      height: 100%;
      margin-left: -960px;
      left: 50%;
      overflow: hidden;
      display: block;
    }
    .login {
      width: 1200px;
      height: 100%;
      margin: auto;
      z-index: 99;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      position: relative;

      .login_box {
        position: relative;
        z-index: 10000;
        background: #fff;
        padding: 10px;

        .top {
          margin-top: 20px;
          padding: 0 20px;
          display: flex;
          width: 356px;
          cursor: default;
          .item1 {
            flex: 1;
            text-align: center;
            font-size: 18px;
            color: #666;
            position: relative;
          }
        }

        .center {
          padding: 30px 30px 40px;

          .item {
            position: relative;
            margin-top: 15px;
            border-radius: 2px;

            &:first-child {
              margin-top: 0;
            }

            .icon {
              position: absolute;
              left: 1px;
              top: 1px;
              width: 50px;
              text-align: center;
              height: 38px;
              background: #f8f8f8;

              .input {
                border: 1px solid #e8e8e8;
                height: 40px;
                padding: 0 44px 0 60px;
                width: 296px;
              }
            }

            .input {
              border: 1px solid #e8e8e8;
              height: 40px;
              padding: 0 44px 0 60px;
              width: 296px;
            }

            &.code {
              .input {
                padding-right: 10px;
                width: 150px;
              }
            }

            .img_code {
              position: absolute;
              right: 0;
              top: 0;
              border: 1px solid #eee;
              border-left: 0;
              width: 80px;
              height: 40px;
            }
          }

          .cancel {
            position: absolute;
            right: 0;
            top: 1px;
            width: 44px;
            height: 38px;
            cursor: pointer;

            .show_pwd {
              &:before {
                top: 7px;
                left: 13px;
              }
            }

            :before {
              position: absolute;
              top: 9px;
              left: 14px;
            }
          }

          .send_code {
            position: absolute;
            right: 0;
            top: 0;
            background: #f9f9f9;
            border: 1px solid #eee;
            border-left: 0;
            width: 80px;
            height: 40px;
            line-height: 40px;
            text-align: center;
            color: #000;

            :hover {
              color: var(--color_main);
            }
          }

          .error {
            margin-top: 10px;
            position: relative;
            color: rgb(225, 37, 27);
            height: 16px;
            line-height: 16px;
          }

          .login_btn {
            display: block;
            margin-top: 35px;
            background: var(--color_main_bg);
            color: #fff;
            text-align: center;
            border-radius: 2px;
            height: 45px;
            line-height: 45px;
            font-size: 18px;
            letter-spacing: 5px;

            &:hover {
              opacity: 0.9;
            }
          }
        }
      }
    }
  }
}
