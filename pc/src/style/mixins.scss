@mixin ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
//flex布局复用
@mixin flex($hov:space-between,$col:center){
  display:flex;
  justify-content:$hov;
  align-items:$col;
}
@mixin bgImg($w:0,$h:0,$img:'',$size:contain){
  display:inline-block;
  width:$w;
  height:$h;
  background:url($img) no-repeat center;
  background-size:$size;
}
@mixin position($pos:absolute,$top:0,$left:0,$w:100%,$h:100%){
  position:$pos;
  top:$top;
  left:$left;
  width:$w;
  height:$h;
}
@mixin positionImg($pos:absolute,$top:0,$right:0,$w:0,$h:0,$img:''){
  position:$pos;
  top:$top;
  right:$right;
  width:$w;
  height:$h;
  background:url($img) no-repeat center;
  background-size:contain;
}
@mixin height($h:0,$lh:$h) {
  height: $h;
  line-height: $lh;
}
@mixin wH($w:0,$h:0) {
  width:$w;
  height: $h;
}
@mixin border($bw:1px,$bc:$colorF,$bs:solid) {
  border: $bw $bs $bc;
}
