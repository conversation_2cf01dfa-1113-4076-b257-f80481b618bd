.confirm_order_container {
    width: 1200px;
    margin: 0 auto;
    margin-top: 20px;
    .receive_info {
        border: 1px solid #dddddd;
        border-radius: 2px;
        .receive_info_title {
            height: 40px;
            line-height: 40px;
            font-size: 12px;
            padding-left: 20px;
            background-color: #f8f8f8;
            color: #666666;
        }
        .receive_info_content {
            height: 159px;
            padding: 0 40px;
        }
        .content_left {
            width: 785px;
            border-right: 1px solid #dddddd;
            .member {
                font-size: 14px;
            }
            .address_detail {
                width: 700px;
            }
            span:not(:first-child) {
                font-size: 14px;
                line-height: 14px;
                margin-top: 22px;
            }
        }
        .content_right {
            width: 370px;
            font-size: 14px;
            .replace_address {
                color: var(--color_integral_main);
                line-height: 14px;
            }
        }
        .add_address {
            width: 100px;
            height: 30px;
            background-color: var(--color_integral_main);
            color: white;
            line-height: 30px;
            text-align: center;
            margin-top: 29px;
            border-radius: 3px;
        }
        .nomartop {
            margin-top: 0;
        }
    }

    .pre_message,
    .receive_info {
        border: 1px solid #dddddd;
        border-radius: 2px;
        .pre_message_title,
        .receive_info_title {
            height: 40px;
            line-height: 40px;
            font-size: 12px;
            padding-left: 20px;
            background-color: #f8f8f8;
            color: #666666;
        }
        .receive_info_content {
            height: 159px;
            padding: 0 40px;
        }

        .pre_message_info {
            padding: 10px 40px;
            .tag_pre {
                display: inline-block;
                line-height: 25px;
                text-align: right;
                font-weight: 400;
                margin-right: 10px;
                font-size: 14px;
                color: #333333;
                display: flex;
                min-width: 100px;
                max-width: 200px;
                justify-content: flex-end;
            }

            strong {
                font-size: 14px;
                color: var(--color_integral_main);
                margin-right: 3px;
            }
        }

        .content_left {
            width: 785px;
            border-right: 1px solid #dddddd;
            .member {
                font-size: 14px;
            }
            .address_detail {
                width: 700px;
            }
            span:not(:first-child) {
                font-size: 14px;
                line-height: 14px;
                margin-top: 22px;
            }
        }
        .content_right {
            width: 370px;
            font-size: 14px;
            .replace_address {
                color: var(--color_integral_main);
                line-height: 14px;
            }
        }
        .add_address {
            width: 100px;
            height: 30px;
            background-color: var(--color_integral_main);
            color: white;
            line-height: 30px;
            text-align: center;
            margin-top: 29px;
            border-radius: 3px;
        }
        .nomartop {
            margin-top: 0;
        }
    }

    .total_money {
        font-size: 14px;
        text-align: right;
        color: #333333;
        .price_section {
            color: var(--color_integral_main);
            .bold {
                font-weight: bold;
                font-size: 18px;
            }
            .tag {
                font-size: 15px;
            }

            .price_tag {
                font-size: 15px;
            }
        }
    }

    .store_item {
        border: 1px solid #dddddd;
        margin-top: 20px;
        .store_name {
            height: 40px;
            line-height: 40px;
            font-size: 12px;
            padding-left: 20px;
            background-color: #f8f8f8;
            color: #666666;
        }
        .good_container {
            padding: 0 20px;
            .good_item {
                font-size: 14px;
                padding: 20px 0 10px 0;
                border-bottom: 1px solid #eeeeee;
                .good_info {
                    width: 785px;
                    .good_image {
                        width: 100px;
                        height: 100px;
                    }
                    .good_info_text {
                        align-self: flex-start;
                        margin-left: 20px;
                        .good_name {
                            width: 400px;
                            color: #333333;
                            margin-top: 10px;
                        }
                        .good_spec {
                            width: 400px;
                            color: #999999;
                            margin-top: 20px;
                        }
                    }
                }
                .good_price {
                    width: 230px;
                    text-align: center;
                    color: #333333;
                }
                .num {
                    flex: 1;
                    text-align: center;
                }
            }
        }
        .store_coupon {
            width: 1156px;
            margin: 0 auto;
            margin-top: 10px;
            border: 1px solid #dddddd;
            font-size: 14px;
            .store_coupon_title {
                width: 100%;
                background-color: #f8f8f8;
                height: 40px;
                line-height: 40px;
                padding-left: 20px;
                color: #666666;
                box-sizing: border-box;
            }
            .store_coupon_con {
                height: 60px;
                padding-left: 20px;
                padding-right: 20px;
                .title {
                    width: 130px;
                    height: 30px;
                    text-align: center;
                    line-height: 30px;
                    border: 1px solid #dddddd;
                    color: #999999;
                }
                .remark {
                    font-size: 12px;
                    margin-left: 20px;
                    height: 20px;
                    ::-webkit-input-placeholder {
                        color: #aaaaaa;
                    }
                    border: none;
                    width: 80%;
                }
            }
        }
        .settle_info {
            width: 1158px;
            margin: 33px auto;
            box-sizing: border-box;
        }
    }
    .platform_discount {
        width: 100%;
        margin-top: 20px;
        .platform_discount_con {
            padding-left: 20px;
            .voice_select {
                margin: 15px 0;
            }
            .integral {
                margin-bottom: 10px;
                padding: 15px 0;
                border-top: 1px solid #dddddd;
                width: 100%;
                .int_ground {
                    display: flex;
                    align-items: center;
                    font-size: 14px;
                    font-family: Microsoft YaHei;
                    font-weight: 400;
                    color: #333333;
                    img {
                        margin-left: 5px;
                        width: 15px;
                        height: 15px;
                    }
                }
                .int_avalible {
                    display: inline-flex;
                    margin-top: 15px;
                    font-size: 14px;
                    border: 1px solid #dddddd;
                    padding: 9px 14px;
                    border-radius: 3px;
                    width: unset;
                    span:first-child {
                        color: #999999;
                    }
                    span:last-child {
                        color: var(--color_integral_main);
                    }
                }
            }
            width: 100%;
            .title {
                width: 130px;
                height: 30px;
                text-align: center;
                line-height: 30px;
                border: 1px solid #dddddd;
                color: #999999;
                margin-left: 20px;
            }
        }
    }
    .order_total_settle {
        width: 100%;
        border-radius: 2px;
        margin-top: 20px;
        font-size: 16px;
        zoom: 1;
        &::after {
            content: "";
            display: block;
            clear: both;
        }

        .exchange_info {
            background-color: #f4f4f4;
            border: 1px solid #e5e5e5;
            padding: 23px;
        }

        .order_price {
            text-align: right;
            color: #333333;
            .order_price_num {
                padding-left: 70px;
                font-size: 30px;
                color: var(--color_price);
            }
            .red {
                padding-left: 70px;
                color: var(--color_price);
            }
        }
        .order_discount {
            margin-top: 30px;
        }
        .goBuy {
            margin-bottom: 40px;
            width: 188px;
            height: 45px;
            background-color: var(--color_integral_main);
            float: right;
            margin-top: 20px;
            border-radius: 4px;
            color: white;
            font-size: 18px;
            font-weight: bold;
            line-height: 45px;
            text-align: center;
        }
    }
    .invoice_con {
        height: 70px;
        margin-left: 20px;
        .no_select_invoice {
            img {
                width: 18px;
                height: 18px;
            }
            span {
                font-size: 14px;
                line-height: 14px;
                margin-left: 20px;
            }
            .invoice_info {
                margin-left: 50px;
                border-left: 1px solid #dddddd;
                span {
                    font-size: 14px;
                    line-height: 14px;
                    margin-left: 50px;
                }
                .choose {
                    color: #257bfd;
                    cursor: pointer;
                }
            }
        }
    }
    // 发票弹窗 start
    p {
        color: #666666;
        font-size: 14px;
        width: 485px;
        margin: 0 auto;
    }
    .voice_list {
        width: 485px;
        height: 330px;
        overflow-y: scroll;
        scrollbar-width: none !important;
        -ms-overflow-style: none !important;
        margin: 0 auto;
        margin-top: 20px;
        .voice_item:not(:first-child) {
            margin-top: 20px;
        }
        .voice_item {
            width: 100%;
            height: 36px;
            border: 1px solid #dddddd;
            position: relative;
            line-height: 36px;
            font-size: 14px;
            color: #333333;
            padding: 0 20px;
            box-sizing: border-box;
            span:first-child {
                float: left;
            }

            span:nth-child(2) {
                float: right;
                display: inline-block;
                text-align: center;
                width: 35px;
                font-size: 12px;
                color: var(--color_integral_main);
                height: 20px;
                line-height: 20px;
                margin-top: 7px;
                border: 1px solid var(--color_integral_main);
                border-radius: 5px;
            }

            img {
                width: 14px;
                height: 14px;
                position: absolute;
                right: 0;
                bottom: 0;
            }
            .img {
                width: 14px;
                height: 14px;
                position: absolute;
                right: -1px;
                bottom: 0;
            }
        }
        .active {
            border: 1px solid var(--color_integral_main);
        }
        .add_voice_btn {
            width: 80px;
            height: 30px;
            line-height: 30px;
            text-align: center;
            background-color: var(--color_integral_main);
            color: white;
            margin: 20px 300px 0;
            border-radius: 2px;
        }
    }
    .add_voice_content {
        height: 330px;
        width: 485px;
        margin: 0 auto;
        overflow-y: scroll;
        scrollbar-width: none !important;
        -ms-overflow-style: none !important;
        .item:not(:first-child) {
            margin-top: 20px;
        }
        .item {
            font-size: 14px;
            .title {
                height: 30px;
                line-height: 30px;
                width: 70px;
                text-align: right;
            }
            .right_info {
                .input {
                    margin-left: 10px;
                    width: 400px;
                }
                .nomartop {
                    margin-top: 0px;
                }
                p {
                    width: 400px;
                    color: #bbbbbb;
                    font-size: 12px;
                    margin-left: 10px;
                    margin-top: 10px;
                    line-height: 16px;
                }
                .select_btn {
                    width: 120px;
                    height: 30px;
                    background: #ffffff;
                    border: 1px solid #dfdfdf;
                    border-radius: 2px;
                    line-height: 30px;
                    text-align: center;
                    position: relative;
                    color: #999999;
                }
                .active {
                    border: 1px solid var(--color_integral_main);
                    color: #333333;
                }
                .select_btn:nth-child(1) {
                    margin-left: 10px;
                }
                .select_btn:nth-child(2) {
                    margin-left: 70px;
                }
                img {
                    width: 14px;
                    height: 14px;
                    position: absolute;
                    right: 0;
                    bottom: 0;
                }
                .img {
                    position: absolute;
                    right: 0;
                    bottom: -7px;
                }
            }
        }
        .is_defalut {
            margin-top: 10px;
        }

        span {
            margin-right: 10px;
        }
    }
    //商品无货缺货
    .out_stock_dialog {
        width: 460px;
        margin: 0 auto;
        height: 330px;
        overflow-y: scroll;
        .good_item {
            font-size: 14px;

            img {
                width: 80px;
                height: 80px;
            }
            .good_info {
                margin-left: 10px;
            }
            .good_name {
                width: 320px;
                color: #333333;
                line-height: 14px;
                margin-top: 10px;
                display: inline-block;
            }
            .spec_num {
                margin-top: 26px;
                .good_spec {
                    color: #999999;
                }
                .good_num {
                    float: right;
                    color: #333333;
                }
            }
        }
    }
    .btn_con {
        font-size: 14px;
        margin-top: 20px;
        .return {
            width: 60px;
            height: 30px;
            line-height: 30px;
            text-align: center;
            color: #333333;
            border-radius: 3px;
            border: 1px solid #dddddd;
        }
        .red {
            background-color: var(--color_integral_main);
            color: white;
        }
        .remove {
            width: 120px;
            height: 30px;
            line-height: 30px;
            text-align: center;
            border-radius: 3px;
            background-color: var(--color_integral_main);
            margin-left: 10px;
            color: white;
        }
        .confirm_add_voice {
            width: 60px;
            height: 30px;
            line-height: 30px;
            text-align: center;
            color: white;
            border-radius: 3px;
            background-color: var(--color_integral_main);
            margin-left: 10px;
        }
    }
    // 选择地址
    .address_con {
        height: 330px;
        overflow-y: scroll;
        scrollbar-width: none !important;
        -ms-overflow-style: none !important;
        .address_item {
            &:not(:first-child) {
                margin-top: 20px;
                margin-bottom: 2px;
            }
            width: 458px;
            //    height: 104px;
            box-sizing: border-box;
            border: 1px solid #dfdfdf;
            position: relative;
            padding: 20px;
            span,
            div:not(:first-child) {
                margin-top: 12px;
            }
            .address_text {
                display: flex;
                width: 400px;
            }
            .selected {
                width: 14px;
                height: 14px;
                position: absolute;
                right: 0;
                bottom: -1px;
            }
        }
        .select {
            border: 1px solid var(--color_integral_main);
        }
    }
    ::-webkit-scrollbar {
        width: 0 !important;
    }
    ::-webkit-scrollbar {
        width: 0 !important;
        height: 0;
    }
}

.el-select-dropdown__item.selected {
    color: var(--color_integral_main);
    font-weight: 700;
}

.el-select .el-input__inner:focus {
    border-color: unset;
}

select .el-input.is-focus .el-input__inner {
    border-color: unset;
}

.top_order_info {
    width: 1200px;
    margin: 20px auto;
    display: flex;
    justify-content: space-between;
    .top_logo {
        width: 135px;
        height: 98px;
        div {
            width: 135px;
            height: 98px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center center;
        }
    }

    .top_info_progress {
        width: 600px;
        .progress_item {
            p {
                margin-top: 10px;
            }
            .progress {
                text-align: center;
                margin-top: 3px;
                span {
                    position: relative;
                    display: inline-block;
                    width: 24px;
                    height: 24px;
                    line-height: 14px;
                    border: 5px solid #eeeeee;
                    border-radius: 50%;
                    color: #9f9f9f;
                    z-index: 2;

                    &.active {
                        border: 5px solid var(--color_integral_main);
                        color: var(--color_integral_main);
                    }
                }

                .progress_line {
                    width: 140px;
                    height: 6px;
                    background-color: #eeeeee;
                    z-index: 1;
                    .content {
                        width: 100%;
                        height: 100%;
                        &.active {
                            background-color: var(--color_integral_main);
                        }
                        &.current {
                            width: 60%;
                        }
                    }
                }
            }
        }
    }
}

.overflow_ellipsis_clamp2 {
    width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    word-break: break-all;

    line-height: 19px;
}

.integral_avalible {
    height: 36px;
    background: #f8f8f8;
    padding-left: 15px;
    line-height: 36px;
    span:first-child {
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #333333;
    }

    span:nth-child(2) {
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: bold;
        color: #333333;
    }
}

.use_integral_list {
    margin-top: 10px;
    .integral_item {
        position: relative;
        display: flex;
        padding-top: 10px;
        padding-bottom: 10px;
        justify-content: space-between;
        &:hover {
            background-color: #f8f8f8;
        }
        span {
            display: inline-block;
            color: #333333;
            font-size: 14px;
            height: 20px;
            line-height: 20px;
        }

        label {
            display: block;
            width: 18px;
            height: 18px;
            z-index: 1;
            .img {
                display: block;
                width: 16px;
                height: 16px;
                background: url(../../../assets/buy/not_select.png) center;
                background-size: 100%;
            }
            .img_selected {
                display: none;
            }
        }

        input[type="radio"] {
            display: none;
            z-index: 0;
        }

        input[type="radio"]:checked + label {
            .img {
                display: none;
            }
            .img_selected {
                display: block;
                z-index: 1;
            }
            .img {
                width: 18px;
                height: 18px;
                background: url(../../../assets/buy/selected.png) center;
                background-size: 100%;
                z-index: 1;
            }
        }
        .no_avalible {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #666666;
            opacity: 0.3;
            z-index: 999;
        }
    }
}

.integral_btn {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    button {
        border: none;
        width: 110px;
        height: 30px;
        border-radius: 3px;
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #ffffff;
        line-height: 30px;
    }

    .integral_abandom {
        background: #999999;
    }

    .integral_engage {
        background-color: var(--color_integral_main);
        margin-left: 20px;
    }
}

.intagral_lack {
    width: 160px;
    height: 50px;
    background: #999999;
    float: right;
    margin-top: 25px;
    border-radius: 3px;
    color: white;
    font-size: 20px;
    font-weight: bold;
    line-height: 50px;
    text-align: center;
}
