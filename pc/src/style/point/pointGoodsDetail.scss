.sld_point_goods_detail {
    width: 100%;
    .contain_con {
        width: 100%;
        background: #ffffff;
        height: 48px;
        position: fixed;
        top: 0;
        box-shadow: 0 0 10px 10px rgba(0, 0, 0, 0.2);
        z-index: 100;
        -webkit-animation: goodsFixed 0.5s ease-in-out;
        animation: goodsFixed 0.5s ease-in-out;
        @-webkit-keyframes goodsFixed {
            0% {
                top: -50px;
            }

            to {
                top: 0;
            }
        }

        @keyframes goodsFixed {
            0% {
                top: -50px;
            }

            to {
                top: 0;
            }
        }
        .contain_content {
            width: 1200px;
            margin: 0 auto;
            .contain_con_left {
                width: 250px;
                height: 48px;
                background: #f9f9f9;
                border-radius: 2px 2px 0 0;
                padding-left: 15px;
                box-sizing: border-box;
                border-left: 1px solid #dfdfdf;
                border-right: 1px solid#DFDFDF;
                .store_type {
                    width: 31px;
                    height: 16px;
                    background: var(--color_main_bg);
                    border-radius: 3px;
                    font-size: 12px;
                    font-family: Microsoft YaHei;
                    font-weight: 400;
                    color: #ffffff;
                    line-height: 16px;
                    text-align: center;
                }
                .store_title {
                    font-size: 16px;
                    font-family: Microsoft YaHei;
                    font-weight: bold;
                    color: #333333;
                    line-height: 39px;
                    margin: 0 10px;
                }
                img {
                    width: 20px;
                    height: 20px;
                    cursor: pointer;
                }
            }
            .contain_con_right {
                width: 935px;
                margin-left: 15px;
                border: 1px solid #dfdfdf;
                border-radius: 3px;
                .goods_description_title {
                    height: 46px;
                    background: #f8f8f8;
                    .description_title_left {
                        span {
                            min-width: 120px;
                            height: 46px;
                            display: block;
                            font-size: 14px;
                            font-family: Microsoft YaHei;
                            font-weight: 400;
                            color: #333333;
                            text-align: center;
                            line-height: 46px;
                        }
                        .description_active {
                            background: #ffffff;
                            border-top: 2px solid var(--color_main);
                            color: var(--color_main);
                        }
                    }
                    .description_title_right {
                        padding-right: 10px;
                        .mobile_order {
                            position: relative;
                            &:hover {
                                .mobile_order_model {
                                    display: block;
                                }
                            }
                            span {
                                font-size: 12px;
                                font-family: Microsoft YaHei;
                                font-weight: 400;
                                color: #666666;
                                line-height: 74px;
                            }
                            .goods_code {
                                margin-left: 16px;
                            }
                            .mobile_order_model {
                                position: absolute;
                                display: none;
                                top: 70px;
                                width: 180px;
                                height: 190px;
                                background: #ffffff;
                                border: 1px solid #dfdfdf;
                                left: -15px;
                                &::before {
                                    box-sizing: content-box;
                                    width: 0;
                                    height: 0;
                                    position: absolute;
                                    top: -16px;
                                    right: 81px;
                                    padding: 0;
                                    border-bottom: 8px solid #ffffff;
                                    border-top: 8px solid transparent;
                                    border-left: 8px solid transparent;
                                    border-right: 8px solid transparent;
                                    display: block;
                                    content: "";
                                    z-index: 12;
                                }
                                &::after {
                                    box-sizing: content-box;
                                    width: 0px;
                                    height: 0px;
                                    position: absolute;
                                    top: -18px;
                                    right: 80px;
                                    padding: 0;
                                    border-bottom: 9px solid #cccccc;
                                    border-top: 9px solid transparent;
                                    border-left: 9px solid transparent;
                                    border-right: 9px solid transparent;
                                    display: block;
                                    content: "";
                                    z-index: 10;
                                }
                                .model_goods_code {
                                    width: 126px;
                                    height: 126px;
                                    margin: 20px auto 0;
                                    img {
                                        max-width: 126px;
                                        max-height: 126px;
                                        margin: auto;
                                    }
                                }
                                p {
                                    text-align: center;
                                    margin-top: 12px;
                                }
                            }
                        }
                        .line {
                            width: 1px;
                            height: 27px;
                            background: #dddddd;
                            margin: 0 10px;
                        }
                        .go_add_cart {
                            width: 90px;
                            height: 30px;
                            background: var(--color_main_bg);
                            border-radius: 3px;
                            font-size: 14px;
                            font-family: Microsoft YaHei;
                            font-weight: 400;
                            color: #ffffff;
                            line-height: 30px;
                            text-align: center;
                        }
                    }
                }
            }
        }
    }
    .goods_detail_content {
        width: 100%;
        margin: 0 auto;
        // 商品所属分类 ，联系客服，关注店铺 start
        .goods_about_con {
            width: 100%;
            height: 40px;
            background: #f8f8f8;
            .goods_about {
                width: 1200px;
                height: 40px;
                margin: 0 auto;
                .goods_classify {
                    font-size: 12px;
                    font-family: Microsoft YaHei;
                    font-weight: 400;
                    color: #999999;
                }
                .goods_about_right {
                    .goods_about_store {
                        span:nth-child(1) {
                            font-size: 14px;
                            font-family: Microsoft YaHei;
                            font-weight: 400;
                            color: #666666;
                            line-height: 14px;
                            &:hover {
                                color: var(--color_main);
                            }
                        }
                        span:nth-child(2) {
                            font-size: 12px;
                            font-family: Microsoft YaHei;
                            font-weight: 400;
                            color: #fefefe;
                            line-height: 14px;
                            width: 31px;
                            height: 16px;
                            background: var(--color_main);
                            border-radius: 2px;
                            text-align: center;
                            margin-left: 10px;
                        }
                    }
                    .contact_service {
                        margin: 0 20px 0 30px;
                        cursor: pointer;
                        &:hover {
                            span {
                                color: var(--color_main);
                            }
                        }
                        span {
                            font-size: 14px;
                            font-family: Microsoft YaHei;
                            font-weight: 400;
                            color: #666666;
                            line-height: 14px;
                        }
                        img {
                            width: 20px;
                            height: 20px;
                        }
                    }
                    .focus_store {
                        line-height: 20px;
                        margin-left: 20px;
                        width: 90px;
                        text-align: right;
                        span {
                            line-height: 20px;
                            text-align: center;
                            float: right;
                            margin-right: 10px;
                        }
                        img {
                            float: right;
                        }
                        .img {
                            float: right;
                        }
                    }
                }
            }
        }
        // 商品所属分类 ，联系客服，关注店铺 end
        // 商品主要信息 start
        .main_con {
            width: 1200px;
            margin: 20px auto 0;
            .goods_des {
                display: flex;
                margin-bottom: 40px;
                .goods_des_left {
                    margin-right: 20px;
                    .goods_main_picture {
                        position: relative;
                        .preview-box {
                            position: relative;
                            width: 350px;
                            height: 350px;
                            border-radius: 2px;
                            .default_image {
                                width: 350px;
                                height: 350px;
                                border-radius: 2px;
                                background-size: contain;
                                background-repeat: no-repeat;
                                background-position: center;
                                &.skeleton_default_image {
                                    background: $colorSkeleton;
                                }
                            }
                            .v_btn {
                                width: 350px;

                                z-index: 990;
                                position: absolute;
                                bottom: 62px;
                                img {
                                    cursor: pointer;
                                    display: block;
                                    margin: 0 auto;
                                }
                            }

                            .mask {
                                position: absolute;
                                top: 0;
                                left: 0;
                                width: 120px;
                                height: 120px;
                                z-index: 10;
                                background: #ffffff;
                                opacity: 0.2;
                                cursor: move;
                            }
                            .magnifier_icon {
                                position: absolute;
                                bottom: 0;
                                right: 0;
                                width: 27px;
                                height: 27px;
                                background: rgba(0, 0, 0, 0.3);
                                z-index: 10;
                                i {
                                    font-size: 16px;
                                    color: #ffffff;
                                    text-align: center;
                                    line-height: 27px;
                                }
                            }
                        }
                        .goods_picture_big {
                            position: absolute;
                            left: 370px;
                            top: 0;
                            z-index: 50;
                            width: 500px;
                            height: 500px;
                            overflow: hidden;
                            .default_image_big {
                                position: absolute;
                                width: 1458px;
                                height: 1458px;
                                background-size: contain;
                                background-repeat: no-repeat;
                                background-position: center;
                            }
                        }
                    }
                    .goods_picture_con {
                        margin-top: 11px;
                        width: 350px;
                        &.skeleton_goods_picture_con {
                            background: #eee;
                            height: 59px;
                        }
                        .left_arrow {
                            font-size: 6px;
                            color: #999999;
                            cursor: pointer;
                        }
                        .show_box {
                            position: relative;
                            flex: 1;
                            overflow: hidden;
                            width: 326px;
                            margin: 0 2px;
                            height: 59px;
                            .goods_picture_list {
                                position: absolute;
                                top: 0;
                                left: 0;
                                .goods_img {
                                    width: 58px;
                                    height: 58px;
                                    margin-right: 8px;
                                    border: 1px solid #cccccc;
                                    cursor: pointer;
                                    &:nth-last-child(1) {
                                        margin-right: 0;
                                    }
                                    .goods_image {
                                        width: 56px;
                                        height: 56px;
                                        background-size: contain;
                                        background-repeat: no-repeat;
                                        background-position: center;
                                    }
                                }
                                .goods_img_active {
                                    border: 1px solid var(--color_main);
                                }
                            }
                        }
                        .right_arrow {
                            font-size: 6px;
                            color: #999999;
                            cursor: pointer;
                        }
                        .no_left_arrow {
                            color: #dddddd;
                        }
                    }
                    // 分享 收藏 start
                    .collection_share_btn {
                        padding-left: 10px;
                        .collection_btn {
                            margin-top: 25px;
                            img {
                                width: 24px;
                                height: 24px;
                            }
                            span {
                                font-size: 12px;
                                font-family: Microsoft YaHei;
                                font-weight: 400;
                                color: #666666;
                                margin-left: 7px;
                            }
                        }
                        .share_btn {
                            position: relative;
                            margin-left: 18px;
                            margin-top: 25px;
                            .share_btn_con {
                                padding-left: 10px;
                                img {
                                    width: 24px;
                                    height: 24px;
                                }
                                span {
                                    font-size: 12px;
                                    font-family: Microsoft YaHei;
                                    font-weight: 400;
                                    color: #666666;
                                    margin-left: 8px;
                                }
                                &:hover span {
                                    color: #333333;
                                }
                            }
                            .share_list {
                                margin-left: -1px;
                                width: 80px;
                                height: 140px;
                                border: 1px solid #dfdfdf;
                                border-radius: 2px;
                                padding: 10px 0 10px 10px;
                                position: absolute;
                                top: -12px;
                                display: none;
                                .share_btn_pre {
                                    margin-top: 1px;
                                    margin-bottom: 8px;
                                    &:nth-last-of-type(1) {
                                        margin-bottom: 0;
                                    }
                                    img {
                                        width: 24px;
                                        height: 24px;
                                    }
                                    span {
                                        font-size: 12px;
                                        font-family: Microsoft YaHei;
                                        font-weight: 400;
                                        color: #666666;
                                        margin-left: 8px;
                                    }
                                    &:hover span {
                                        color: #333333;
                                    }
                                }
                                .wx_share_code {
                                    position: absolute;
                                    right: -135px;
                                    bottom: 0px;
                                    width: 130px;
                                    height: 135px;
                                    background: #ffffff;
                                    border: 1px solid #d9d9d9;
                                    .wx_share_code_img {
                                        canvas {
                                            width: 90px !important;
                                            height: 90px !important;
                                        }
                                    }
                                    .wx_share_code_title {
                                        font-size: 10px;
                                        font-family: Microsoft YaHei;
                                        font-weight: 400;
                                        color: #333333;
                                        margin-top: 6px;
                                    }
                                }
                            }
                            &:hover .share_btn_con {
                                display: none;
                            }
                            &:hover .share_list {
                                display: block;
                            }
                        }
                    }
                    // 分享 收藏 end
                }
                .m_item_inner {
                    float: left;
                    .item_info {
                        position: relative;
                        width: 630px;
                        padding-bottom: 20px;
                    }
                    .detaile_name {
                        font-size: 16px;
                        font-family: Microsoft YaHei;
                        font-weight: bold;
                        color: #333333;
                        margin-bottom: 6px;
                        text-overflow: -o-ellipsis-lastline;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        display: -webkit-box;
                        -webkit-line-clamp: 2;
                        line-clamp: 2;
                        -webkit-box-orient: vertical;
                        line-height: 25px;
                        &.skeleton_detaile_name {
                            background: $colorSkeleton;
                            width: 100%;
                            height: 18px;
                        }
                    }
                    .p_ad {
                        font-size: 14px;
                        font-family: Microsoft YaHei;
                        font-weight: 400;
                        color: var(--color_main);
                        text-overflow: ellipsis;
                        overflow: hidden;
                        word-break: break-all;
                        margin-bottom: 12px;
                        line-height: 18px;
                        &.skeleton_p_ad {
                            background: $colorSkeleton;
                            width: 100%;
                            height: 16px;
                        }
                    }
                    .summary {
                        width: 630px;
                        height: 160px;
                        box-sizing: border-box;
                        .sld_summary_item {
                            width: 630px;
                            height: 114px;
                            padding: 18px 20px;
                            background: url(../../../assets/point/point_detail_bg_foot.png) 100% 100%;
                        }
                    }
                }
            }
        }
        // 商品主要信息 end
    }
    .point_sales {
        padding-right: 10px;
        text-align: right;
        width: 630px;
        height: 46px;
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #ffffff;
        line-height: 46px;
        background: url(../../../assets/point/diy_style_pc_goods_bg.png) 100% 100%;
        background-color: var(--color_integral_main);
    }
    .sld_summary_goods_left {
        display: flex;
        justify-content: space-between;
        .goods_price {
            display: flex;
            .price_title1 {
                margin-right: 20px;
                letter-spacing: 10px;
                font-size: 14px;
                font-family: Microsoft YaHei;
                font-weight: 400;
                color: #666666;
            }
            .p_price {
                color: var(--color_integral_main);
                font-size: 16px;
                font-weight: 600;
                margin-left: -6px;
                &.skeleton_p_price {
                    background: var(--color_integral_main);
                    width: 70px;
                    display: inline-block;
                    height: 18px;
                }
                span.integralPrice,
                span.cashPrice {
                    font-size: 24px;
                }
            }
        }
    }
    .market_price {
        font-size: 12px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #999999;
        margin-top: 27px;
        .price_title {
            margin-right: 20px;
            letter-spacing: 10px;
            font-size: 14px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #666666;
        }
        .price {
            text-decoration: line-through;
        }
    }
    .actual_sales {
        font-size: 14px;
        font-weight: 400;
        color: #aaaaaa;
        line-height: 30px;
        span {
            color: var(--color_main);
        }
    }

    // 优惠券 start
    .coupon {
        margin-top: 10px;
        .coupon_title {
            font-size: 14px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #666666;
            line-height: 30px;
        }
        .coupon_list {
            margin-left: 22px;
            .coupon_pre_rules {
                height: 26px;
                border: 1px solid var(--color_main);
                border-radius: 3px;
                background: #ffffff;
                font-size: 14px;
                font-family: PingFang SC;
                font-weight: 500;
                color: var(--color_main);
                line-height: 26px;
                padding: 0 8px;
                text-align: center;
                margin-right: 10px;
            }
        }
    }
    // 优惠券 end
    // 促销 start
    .promotion {
        margin-top: 10px;
        .promotion_left {
            .promotion_title {
                font-size: 14px;
                font-family: Microsoft YaHei;
                font-weight: 400;
                color: #666666;
                line-height: 30px;
                margin-right: 20px;
                letter-spacing: 10px;
            }
            .promotion_list {
                width: 415px;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
                word-break: break-all;
                .promotion_block {
                    .promotion_pre {
                        font-size: 14px;
                        font-family: Microsoft YaHei;
                        font-weight: 400;
                        color: #666666;
                        line-height: 30px;
                    }
                }
            }
        }
        .promotion_right {
            font-size: 14px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #306fbc;
            line-height: 30px;
            cursor: pointer;
        }
        .full_discount_model {
            height: 250px;
            overflow-y: auto;
            .full_discount_pre {
                .full_pre_tips {
                    width: 5px;
                    height: 5px;
                    background: var(--color_main);
                    border-radius: 50%;
                    margin-top: 5px;
                }
                .full_pre_con {
                    margin-left: 10px;
                    margin-bottom: 15px;
                    .full_pre_title {
                        font-size: 16px;
                        font-family: Microsoft YaHei;
                        font-weight: bold;
                        color: #333333;
                    }
                    .full_pre_des {
                        width: 450px;
                        border-bottom: 1px dashed #e6e6e6;
                        border-radius: 2px;
                        padding: 10px;
                        box-sizing: border-box;
                        margin-top: 10px;
                        .full_pre_name {
                            span {
                                display: block;
                                &:nth-child(1) {
                                    // width: 159px;
                                    font-size: 14px;
                                    font-family: Microsoft YaHei;
                                    font-weight: bold;
                                    color: #333333;
                                    border-radius: 8px;
                                    font-family: Microsoft YaHei;
                                    font-weight: 400;
                                    line-height: 16px;
                                }
                                &:nth-child(2) {
                                    font-size: 12px;
                                    font-family: Microsoft YaHei;
                                    font-weight: 400;
                                    color: #999999;
                                    margin-left: 20px;
                                }
                            }
                        }
                        .full_pre_desc {
                            margin-top: 10px;
                            .full_pre_img {
                                width: 40px;
                                height: 40px;
                                background: #ffffff;
                                border-radius: 2px;
                                background-size: contain;
                                background-repeat: no-repeat;
                                background-position: center;
                                margin-right: 7px;
                            }
                            .full_pre_right {
                                p,
                                span {
                                    width: 328px;
                                    font-size: 12px;
                                    font-family: Microsoft YaHei;
                                    font-weight: 400;
                                    color: #333333;
                                    overflow: hidden;
                                    text-overflow: ellipsis;
                                    white-space: nowrap;
                                }
                                span {
                                    color: #666666;
                                    margin-top: 6px;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    i {
        font-style: normal;
    }
    // 促销 end
    // 配送地 start
    .delivery {
        padding-left: 20px;
        margin-top: 22px;
        .delivery_title {
            font-size: 14px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #666666;
            line-height: 30px;
        }
        .delivery_address {
            width: 180px;
            height: 30px;
            background: #ffffff;
            border: 1px solid #dfdfdf;
            border-radius: 3px;
            padding: 0 11px;
            box-sizing: border-box;
            margin-left: 20px;
            span {
                font-size: 14px;
                font-family: Microsoft YaHei;
                font-weight: 400;
                color: #999999;
                line-height: 30px;
            }
            i {
                font-size: 12px;
            }
        }
    }
    // 配送地 end
    // 运费 start
    .freight {
        padding-left: 20px;
        margin-top: 20px;
        .freight_title {
            font-size: 14px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #666666;
            letter-spacing: 12px;
            .skeleton_freight_amount {
                background: $colorSkeleton;
                display: inline-block;
                width: 50px;
                height: 16px;
            }
        }
        span {
            font-size: 14px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #333333;
            margin-left: 8px;
        }
    }
    // 运费 end
    // 商品规格 start
    .goods_spec {
        padding-left: 20px;
        margin-top: 20px;
        flex-wrap: wrap;
        .goods_spec_pre {
            .goods_spec_pre_title {
                justify-content: space-between;
                font-size: 14px;
                font-family: Microsoft YaHei;
                font-weight: 400;
                color: #666666;
                line-height: 30px;
                // min-width: 42px;
                text-indent: "justify";
                white-space: nowrap;
            }
            .goods_spec_pre_list {
                margin-left: 20px;
                flex-wrap: wrap;
                .specval_pre {
                    margin-right: 21px;
                    margin-bottom: 20px;
                    .specval_pre_image {
                        width: 48px;
                        height: 48px;
                        border-radius: 3px;
                        background-size: contain;
                        background-repeat: no-repeat;
                        background-position: center;
                    }
                    span {
                        display: inline-block;
                        max-width: 190px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        background: #ffffff;
                        border: 1px solid #dfdfdf;
                        border-radius: 3px;
                        font-size: 14px;
                        font-family: Microsoft YaHei;
                        font-weight: 400;
                        color: #333333;
                        display: block;
                        padding: 10px 20px;
                        box-sizing: border-box;
                    }
                }
                .specval_pre_active {
                    position: relative;
                    span {
                        display: inline-block;
                        max-width: 184px;
                        border: 1px solid var(--color_integral_main);
                    }
                    .specval_pre_image {
                        border: 1px solid var(--color_integral_main);
                    }
                    .check_mark {
                        width: 14px;
                        height: 14px;
                        position: absolute;
                        bottom: 3px;
                        right: 0;
                    }
                }
                .specval_pre_disabled {
                    position: relative;
                    opacity: 0.2;
                }
            }
        }
    }
    // 商品规格 end
    // 数量加减及库存 start
    .quantity_inventory {
        padding: 0 20px 0;
        padding-bottom: 40px;
        border-bottom: 1px solid #dfdfdf;
        .quantity_title {
            font-size: 14px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #666666;
            line-height: 30px;
            letter-spacing: 10px;
        }
        .quantity_edit {
            border: 1px solid #dddddd;
            margin-left: 10px;
            span {
                display: block;
                width: 30px;
                height: 30px;
                border: none;
                border-radius: 3px;
                text-align: center;
                line-height: 30px;
                cursor: pointer;
            }

            input {
                width: 60px;
                height: 30px;
                border: none;
                border-left: 1px solid #ddd;
                border-right: 1px solid #ddd;
                text-align: center;
                cursor: pointer;
            }
            /*** 消除input元素 type="number" 时默认的 加减按钮*/
            input[type="number"]::-webkit-inner-spin-button,
            input[type="number"]::-webkit-outer-spin-button {
                -webkit-appearance: none;
                margin: 0;
            }
            /*** 消除input元素 type="number" 时默认的 加减按钮---moz版*/
            input[type="number"] {
                -moz-appearance: textfield;
            }
        }
        .inventory {
            font-size: 12px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #999999;
            line-height: 30px;
            margin-left: 17px;
        }
    }
    // 数量加减及库存 end
    //立即购买 加入购物车 start
    .options_btn {
        margin-top: 30px;

        .option_desc {
            color: #bbbbbb;
            font-size: 14px;
            margin-bottom: 20px;
        }
        .recoOffShop {
            width: 829px;
            display: flex;
            flex-wrap: wrap;
            margin-top: 20px;
            border: 1px solid #e7e7e7;

            .reCon {
                display: block;
                width: 182px;
                margin: 15px 0 15px 20px;
                .reComImg {
                    display: block;
                    width: 182px;
                    height: 182px;
                    img {
                        width: 100%;
                        height: 100%;
                    }
                }
                .recomName {
                    color: #333333;
                    cursor: pointer;
                    display: block;
                    width: 182px;
                    height: 35px;
                    line-height: 17px;
                    margin-top: 10px;
                    margin-bottom: 10px;
                    font-size: 13px;
                    letter-spacing: 1px;
                    word-break: break-all;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 2;
                    overflow: hidden;
                    &:hover {
                        color: var(--color_main);
                    }
                }
                span {
                    font-size: 13px;
                    font-weight: bold;
                    color: var(--color_main);
                }
            }
        }
        .goods_off_shelves {
            width: 180px;
            height: 50px;
            background: #bbbbbb;
            border-radius: 5px;
            font-size: 20px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #ffffff;
            line-height: 50px;
            text-align: center;
            cursor: pointer;
        }
        .goods_out_stock {
            width: 180px;
            height: 50px;
            background: #bbbbbb;
            border-radius: 5px;
            font-size: 20px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #ffffff;
            line-height: 50px;
            text-align: center;
            cursor: pointer;
        }
        .buy_now {
            width: 180px;
            height: 50px;
            background: var(--color_integral_main);
            border: 1px solid var(--color_integral_main);
            border-radius: 5px;
            font-size: 20px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #fff;
            line-height: 30px;
            cursor: pointer;
            margin-right: 20px;
        }
        .add_cart {
            width: 180px;
            height: 50px;
            background: var(--color_main_bg);
            border-radius: 5px;
            font-size: 20px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #ffffff;
            line-height: 30px;
            cursor: pointer;
            img {
                margin-right: 10px;
            }
        }
    }
    //立即购买 加入购物车 end

    // 看了又看 start
    .more_goods {
        margin-left: 24px;
        margin-bottom: 24px;
        .more_goods_title {
            font-size: 14px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #333333;
            line-height: 30px;
            text-align: center;
        }
        .more_goods_list {
            .more_goods_pre {
                margin-bottom: 10px;
                cursor: pointer;
                .skeleton_more_goods_pre_goods_name {
                    background: $colorSkeleton;
                    width: 100%;
                    height: 13px;
                }
                .skeleton_more_goods_pre_goods_price {
                    background: $colorSkeleton;
                    height: 13px;
                    width: 60px;
                    display: inline-block;
                }
                .more_goods_pre_img {
                    width: 150px;
                    height: 150px;
                    overflow: hidden;
                    position: relative;
                    &.skeleton_more_goods_pre_img {
                        background: $colorSkeleton;
                    }
                    &:hover {
                        img {
                            -webkit-transform: scale(1.05);
                            -ms-transform: scale(1.05);
                            -o-transform: scale(1.05);
                            -moz-transform: scale(1.05);
                            transform: scale(1.05);
                        }
                    }
                    img {
                        max-width: 100%;
                        max-width: 100%;
                        -webkit-transition: -webkit-transform 0.5s;
                        transition: -webkit-transform 0.5s;
                        -moz-transition:
                            transform 0.5s,
                            -moz-transform 0.5s;
                        transition: transform 0.5s;
                        transition:
                            transform 0.5s,
                            -webkit-transform 0.5s,
                            -moz-transform 0.5s;
                    }
                    .price {
                        width: 150px;
                        position: absolute;
                        bottom: 0;
                        height: 30px;
                        background: rgba(0, 0, 0, 0.3);
                        color: #fff;
                        line-height: 30px;
                        text-align: center;
                        font-size: 12px;
                        font-family: Microsoft YaHei;
                    }
                }
                .goodName {
                    width: 150px;
                    font-size: 12px;
                    font-family: Microsoft YaHei;
                    font-weight: 400;
                    color: #333333;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    word-break: break-all;
                    text-align: center;
                    margin: 10px 0;
                }
            }
        }
    }
    // 看了又看 end

    // 店铺信息 start
    .store_info {
        width: 250px;
        background: #ffffff;
        border: 1px solid #dfdfdf;
        border-radius: 2px;
        padding-bottom: 15px;
        .store_info_title {
            width: 248px;
            height: 48px;
            background: #f9f9f9;
            border-radius: 2px 2px 0 0;
            padding-left: 15px;
            box-sizing: border-box;
            .store_type {
                width: 31px;
                height: 16px;
                background: var(--color_main_bg);
                border-radius: 3px;
                font-size: 12px;
                font-family: Microsoft YaHei;
                font-weight: 400;
                color: #ffffff;
                line-height: 16px;
                text-align: center;
            }
            .store_title {
                font-size: 16px;
                font-family: Microsoft YaHei;
                font-weight: bold;
                color: #333333;
                line-height: 39px;
                margin: 0 10px;
                cursor: pointer;
            }
            img {
                width: 20px;
                height: 20px;
                cursor: pointer;
            }
        }
        .store_des {
            .store_des_pre {
                padding-left: 15px;
                margin-top: 18px;
                span:nth-of-type(1) {
                    font-size: 12px;
                    font-family: Microsoft YaHei;
                    font-weight: 400;
                    color: #666666;
                    margin-right: 18px;
                }
                span:nth-of-type(2),
                span:nth-of-type(3) {
                    font-size: 12px;
                    font-family: Microsoft YaHei;
                    font-weight: 400;
                    color: #ff5501;
                    margin-right: 18px;
                }
            }
            .pre_deliver {
                span:nth-of-type(2),
                span:nth-of-type(3) {
                    font-size: 12px;
                    font-family: Microsoft YaHei;
                    font-weight: 400;
                    color: var(--color_main);
                }
            }
            .pre_service {
                span:nth-of-type(2),
                span:nth-of-type(3) {
                    font-size: 12px;
                    font-family: Microsoft YaHei;
                    font-weight: 400;
                    color: #333333;
                }
            }
        }
        .store_btn {
            margin-top: 14px;
            .store_btn_pre {
                width: 103px;
                height: 29px;
                background: var(--color_main_bg);
                border-radius: 3px;
                margin-right: 14px;
                cursor: pointer;
                img {
                    width: 20px;
                    height: 20px;
                    margin-right: 5px;
                }
                .go_store_btn {
                    font-size: 12px;
                    font-family: Microsoft YaHei;
                    font-weight: 400;
                    color: #ffffff;
                    line-height: 39px;
                }
                &:nth-child(2) {
                    margin-right: 0;
                    border: 1px solid #dfdfdf;
                    background: #ffffff;
                    span {
                        color: #333333;
                    }
                }
            }
        }
    }
    // 店铺信息 end
    //店铺推荐及热门收藏 start
    .store_popular {
        width: 250px;
        background: #ffffff;
        border: 1px solid #dfdfdf;
        border-radius: 3px;
        .store_popular_btn {
            span {
                width: 250px;
                height: 40px;
                font-size: 14px;
                font-family: Microsoft YaHei;
                font-weight: bold;
                background: #f8f8f8;
                color: #333333;
                line-height: 40px;
                text-align: center;
                display: block;
                cursor: pointer;
            }
            .store_popular_active {
                background: var(--color_main_bg);
                color: #ffffff;
            }
        }
        .store_popular_list {
            padding: 20px 21px 0;
            .store_popular_pre {
                margin-bottom: 20px;
                border-bottom: 1px solid #f2f2f2;
                &:nth-last-child(1) {
                    border-bottom: none;
                }
                cursor: pointer;
                .store_popular_img {
                    width: 208px;
                    height: 208px;
                    border-radius: 2px;
                    overflow: hidden;
                    &:hover {
                        img {
                            -webkit-transform: scale(1.05);
                            -ms-transform: scale(1.05);
                            -o-transform: scale(1.05);
                            -moz-transform: scale(1.05);
                            transform: scale(1.05);
                        }
                    }
                    img {
                        max-width: 100%;
                        max-width: 100%;
                        -webkit-transition: -webkit-transform 0.5s;
                        transition: -webkit-transform 0.5s;
                        -moz-transition:
                            transform 0.5s,
                            -moz-transform 0.5s;
                        transition: transform 0.5s;
                        transition:
                            transform 0.5s,
                            -webkit-transform 0.5s,
                            -moz-transform 0.5s;
                    }
                }
                p {
                    width: 197px;
                    font-size: 14px;
                    font-family: Microsoft YaHei;
                    font-weight: 400;
                    color: #333333;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    word-break: break-all;
                    margin-top: 14px;
                }
                p:nth-of-type(2) {
                    margin: 21px 0 13px;
                    span {
                        font-size: 14px;
                        font-family: Microsoft YaHei;
                        font-weight: bold;
                        color: var(--color_price);
                    }
                }
            }
        }
    }
    // 店铺推荐及热门收藏 end

    // 商品详情 评价 商品服务 店铺热销  start
    .goods_description {
        margin-left: 15px;
        border: 1px solid #dfdfdf;
        border-radius: 3px;
        .goods_description_title {
            width: 934px;
            height: 40px;
            background: #f8f8f8;
            .description_title_left {
                span {
                    min-width: 120px;
                    height: 40px;
                    display: block;
                    font-size: 14px;
                    font-family: Microsoft YaHei;
                    font-weight: 400;
                    color: #333333;
                    text-align: center;
                    line-height: 39px;
                }
                .description_active {
                    background: #ffffff;
                    border-top: 2px solid var(--color_main);
                    color: var(--color_main);
                }
            }
            .description_title_right {
                padding-right: 10px;
                .mobile_order {
                    position: relative;
                    &:hover {
                        .mobile_order_model {
                            display: block;
                        }
                    }
                    span {
                        font-size: 12px;
                        font-family: Microsoft YaHei;
                        font-weight: 400;
                        color: #666666;
                        line-height: 74px;
                    }
                    .goods_code {
                        margin-left: 16px;
                    }
                    .mobile_order_model {
                        position: absolute;
                        display: none;
                        top: 70px;
                        width: 180px;
                        height: 190px;
                        background: #ffffff;
                        border: 1px solid #dfdfdf;
                        left: -15px;
                        &::before {
                            box-sizing: content-box;
                            width: 0;
                            height: 0;
                            position: absolute;
                            top: -16px;
                            right: 81px;
                            padding: 0;
                            border-bottom: 8px solid #ffffff;
                            border-top: 8px solid transparent;
                            border-left: 8px solid transparent;
                            border-right: 8px solid transparent;
                            display: block;
                            content: "";
                            z-index: 12;
                        }
                        &::after {
                            box-sizing: content-box;
                            width: 0px;
                            height: 0px;
                            position: absolute;
                            top: -18px;
                            right: 80px;
                            padding: 0;
                            border-bottom: 9px solid #cccccc;
                            border-top: 9px solid transparent;
                            border-left: 9px solid transparent;
                            border-right: 9px solid transparent;
                            display: block;
                            content: "";
                            z-index: 10;
                        }
                        .model_goods_code {
                            width: 126px;
                            height: 126px;
                            margin: 20px auto 0;
                            img {
                                max-width: 126px;
                                max-height: 126px;
                                margin: auto;
                            }
                        }
                        p {
                            text-align: center;
                            margin-top: 12px;
                        }
                    }
                }
                .line {
                    width: 1px;
                    height: 27px;
                    background: #dddddd;
                    margin: 0 10px;
                }
                .go_add_cart {
                    width: 90px;
                    height: 30px;
                    background: var(--color_main_bg);
                    border-radius: 3px;
                    font-size: 14px;
                    font-family: Microsoft YaHei;
                    font-weight: 400;
                    color: #ffffff;
                    line-height: 30px;
                    text-align: center;
                }
            }
        }
        .goods_description_con {
            width: 934px;
            min-height: 500px;
            .goods_des_con {
                padding-top: 31px;
                .brand {
                    margin-bottom: 31px;
                    padding-left: 30px;
                    span {
                        font-size: 14px;
                        font-family: Microsoft YaHei;
                        font-weight: 400;
                        color: #999999;
                        &:nth-child(1) {
                            margin-right: 3px;
                        }
                        &:nth-child(2) {
                            color: #6195ff;
                        }
                    }
                }
                .goods_parameter_list {
                    // margin-top: 32px;
                    display: flex;
                    flex-wrap: wrap;
                    padding: 0 30px;
                    box-sizing: border-box;
                    &.goods_paramter_more {
                        overflow: hidden;
                        height: 130px;
                    }
                    .goods_parameter_pre {
                        margin-bottom: 21px;
                        margin-right: 24px;
                        width: 200px;
                        span {
                            font-size: 12px;
                            font-family: Microsoft YaHei;
                            font-weight: 400;
                            color: #999999;
                            display: inline-block;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            overflow: hidden;
                            word-break: break-all;
                            &:nth-child(1) {
                                max-width: 80px;
                                margin-right: 6px;
                            }
                            &:nth-child(2) {
                                color: #333333;
                                width: 135px;
                            }
                        }
                        &:nth-of-type(4n) {
                            margin-right: 0;
                        }
                    }
                }
                .collapse_unfold {
                    span {
                        font-size: 12px;
                        font-family: Microsoft YaHei;
                        font-weight: 400;
                        color: #333333;
                        line-height: 21px;
                    }
                    i {
                        font-size: 12px;
                        color: #333333;
                        margin-left: 10px;
                    }
                }
                .goods_html {
                    padding: 0 21px 0 24px;
                    .goods_htmls {
                        img {
                            max-width: 100%;
                            max-height: 100%;
                        }
                    }
                }
            }
            // 商品评价 start
            .goods_comments {
                padding: 20px;
                .goods_comments_top {
                    border-bottom: 1px solid #dddddd;
                    display: flex;
                    .goods_comments_left {
                        width: 250px;
                        background: #ffffff;
                        border: 1px solid #dddddd;
                        border-bottom: none;
                        padding: 20px 0;
                        box-sizing: border-box;
                        .comments_title {
                            padding: 0 27px;
                            box-sizing: border-box;
                            .comments_tips {
                                font-size: 14px;
                                font-family: Microsoft YaHei;
                                font-weight: 400;
                                color: #333333;
                            }
                        }
                        .good_comment {
                            margin-top: 20px;
                            span {
                                font-size: 14px;
                                font-family: Microsoft YaHei;
                                font-weight: 400;
                                color: #333333;
                                &:nth-child(2) {
                                    display: inline-block;
                                    font-size: 50px;
                                    font-family: Microsoft YaHei;
                                    font-weight: 400;
                                    color: var(--color_main);
                                    margin-left: 15px;
                                }
                            }
                        }
                    }
                    .goods_comments_nav {
                        display: flex;
                        flex-wrap: wrap;
                        margin-left: 20px;
                        .comments_nav_pre {
                            width: 120px;
                            height: 30px;
                            background: #ffffff;
                            border: 1px solid #dddddd;
                            font-size: 14px;
                            font-family: PingFang SC;
                            font-weight: 500;
                            color: #333333;
                            line-height: 30px;
                            text-align: center;
                            margin-right: 20px;
                            cursor: pointer;
                            &:nth-child(4n) {
                                margin-right: 0;
                            }
                            &.comments_nav_pre_active {
                                color: var(--color_main);
                            }
                        }
                    }
                }
                .goods_comments_list {
                    .goods_comments_pre {
                        margin-top: 30px;
                        padding-bottom: 40px;
                        border-bottom: 1px dashed #dddddd;
                        .goods_comments_avatar {
                            width: 50px;
                            height: 50px;
                            border-radius: 50%;
                            background-size: cover;
                        }
                        .list_des {
                            margin-left: 21px;
                            .list_top {
                                .list_left {
                                    .list_top_tile {
                                        .list_top_tips {
                                            font-size: 16px;
                                            font-family: Microsoft YaHei;
                                            font-weight: bold;
                                            color: #333333;
                                            margin-right: 20px;
                                        }
                                    }
                                    .spec {
                                        font-size: 14px;
                                        font-family: Microsoft YaHei;
                                        font-weight: 400;
                                        color: #999999;
                                        margin-top: 10px;
                                    }
                                }
                                .list_top_right {
                                    font-size: 14px;
                                    font-family: Microsoft YaHei;
                                    font-weight: 400;
                                    color: #999999;
                                }
                            }
                            .list_pre {
                                margin-top: 20px;
                                .CommentContent {
                                    width: 813px;
                                    font-size: 14px;
                                    font-family: Microsoft YaHei;
                                    font-weight: 400;
                                    color: #333333;
                                    line-height: 21px;
                                }

                                .replyContent {
                                    margin-top: 15px;
                                    height: 26px;
                                    line-height: 26px;
                                    font-size: 13px;
                                    span {
                                        color: #999999;
                                        font-size: 12px;
                                    }
                                }

                                .list_pre_img_list {
                                    margin-top: 15px;
                                    flex-wrap: wrap;
                                    .list_pre_img {
                                        width: 80px;
                                        height: 80px;
                                        background-size: contain;
                                        background-position: center;
                                        background-repeat: no-repeat;
                                        margin-right: 10px;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            // 商品评价 end
            // 商品服务 start
            .goods_server_list {
                .goods_server_pre {
                    margin: 0 39px 0 51px;
                    border-bottom: 1px solid #f2f2f2;
                    padding-bottom: 20px;
                    padding-top: 22px;
                    .server_pre_top {
                        .server_pre_tips {
                            width: 5px;
                            height: 5px;
                            background: var(--color_main);
                            border-radius: 50%;
                            display: block;
                            margin-right: 10px;
                        }
                        .server_pre_name {
                            font-size: 16px;
                            font-family: Microsoft YaHei;
                            font-weight: bold;
                            color: #333333;
                        }
                    }
                    .server_pre_content {
                        font-size: 14px;
                        font-family: Microsoft YaHei;
                        font-weight: 400;
                        padding-left: 12px;
                        margin-top: 21px;
                        color: #666666;
                    }
                }
            }
            // 商品服务 end
            // 店铺热销 start
            .store_hot_sales {
                .store_hot_sales_list {
                    display: flex;
                    flex-wrap: wrap;
                    padding: 11px 13px;
                    .goods_pre {
                        margin-bottom: 11px;
                        width: 216px;
                        height: 313px;
                        border: 1px solid #dddddd;
                        border-radius: 2px;
                        margin-right: 12px;
                        cursor: pointer;
                        padding-bottom: 10px;
                        &:hover {
                            border: 1px solid var(--color_main);
                        }
                        &:nth-of-type(4n) {
                            margin-right: 0;
                        }
                        .goods_pre_img {
                            width: 214px;
                            height: 221px;
                            border-radius: 2px 2px 0 0;
                            background-size: contain;
                            background-repeat: no-repeat;
                        }
                        .goods_name {
                            font-size: 14px;
                            font-family: Microsoft YaHei;
                            font-weight: 400;
                            color: #333333;
                            line-height: 18px;
                            text-overflow: -o-ellipsis-lastline;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            display: -webkit-box;
                            -webkit-line-clamp: 2;
                            line-clamp: 2;
                            -webkit-box-orient: vertical;
                            margin: 10px 10px 19px;
                        }
                        .goods_price {
                            padding-left: 11px;
                            .selling_price {
                                font-size: 16px;
                                font-family: Microsoft YaHei;
                                font-weight: 400;
                                color: var(--color_main);
                            }
                        }
                    }
                }
            }
            // 店铺热销 end
        }
    }
    // 商品详情 评价 商品服务 店铺热销  end
    // 优惠券弹框 start
    .coupon_model {
        .model_coupon_list {
            display: flex;
            flex-wrap: wrap;
            .sld_coupon_item {
                margin-right: 10px !important;
            }
            .sld_coupon_item:nth-child(2n) {
                margin-right: 0 !important;
            }
        }
    }
    // 优惠券弹框 end
    .cursor_pointer {
        cursor: pointer;
    }
    // 分页
    .sld_page_bottom {
        margin-top: 20px;
    }
    //空页面数据样式
    .empty_data {
        padding-top: 170px;
        span {
            margin-top: 30px;
        }
    }
}

.imgOpen {
    max-width: 1200px;
    max-height: 700px;
    margin: 0 auto;
    display: block;
}
