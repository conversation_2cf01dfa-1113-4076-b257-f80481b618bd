.sld_address_list {
    border: 1px solid #eeeeee;
    width: 1007px;
    margin: 10px 0 0 10px;
    float: left;
    .el-breadcrumb {
        margin-left: 22px;
    }
    .add_default_con,
    .address_list_con {
        background-color: white;

        .title {
            line-height: 60px;
            color: #1c1c1c;
            font-size: 16px;
            font-weight: 600;
            padding-left: 22px;
        }
        .mange {
            color: #333;
            font-size: 14px;
        }
    }
    .add_default_con {
        border-bottom: 1px solid #eeeeee;
        padding-bottom: 10px;
    }
    .address_list_con {
        padding-bottom: 100px;

        .container {
            flex-wrap: wrap;
            .address_item {
                .iconfont {
                    display: none;
                }
            }
            .address_item:hover {
                .iconfont {
                    display: inline-block;
                }
            }
        }
    }
    .address_item {
        width: 245px;
        padding: 18px 8px 0 12px;
        height: 162px;
        background-size: 100% 100%;
        margin-left: 20px;
        margin-bottom: 20px;

        .defalut_edit {
            height: 14px;

            .red {
                color: var(--color_main);
            }

            .iconfont {
                margin-right: 5px;
                font-size: 12px;
                color: #c8c8c8;

                &:hover {
                    color: var(--color_main);
                }
            }
        }

        .name_phone {
            margin-top: 10px;
            color: #333;
            font-size: 12px;
            line-height: 24px;
            height: 50px;
            border-bottom: 1px dashed #c4c4c4;
            word-break: break-all;
            .name {
                margin-right: 10px;
            }
            .phone {
                word-break: break-word;
            }
        }

        .add_info {
            line-height: 30px;
            color: #333;
            font-size: 12px;
        }
        .add_detail {
            color: #999999;
            line-height: 16px;
            word-break: break-all;
        }
    }
    .add_address {
        width: 245px;
        height: 153px;
        margin-left: 20px;
        border: 2px dashed #e5e5e5;
        margin-bottom: 20px;
        .iconxinzengdizhi {
            display: block;
            font-size: 28px;
            text-align: center;
            color: #e5e5e5;
        }
        p {
            color: #999999;
            font-size: 12px;
            margin-top: 19px;
            text-align: center;
        }
    }
}
