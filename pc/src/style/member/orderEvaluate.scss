.sld_order_evaluate {
    width: 1200px;

    .title {
        margin: 20px auto;
        text-align: center;

        p {
            color: #1c1c1c;
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 13px;
        }

        .order_info {
            color: #666666;

            .time {
                margin-left: 40px;
            }
        }
    }

    .store_item {
        width: 100%;

        .store_info {
            width: 100%;
            height: 132px;
            border: 1px solid #f1f1f1;

            .store_info_left {
                width: 303px;
                padding-left: 25px;

                .image {
                    width: 90px;
                    height: 90px;
                    border: 1px solid #f3f3f3;
                    margin-right: 13px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    img {
                        max-width: 100%;
                        max-height: 100%;
                    }
                }

                .rate {
                    align-self: flex-start;

                    .con {
                        width: 165px;
                        margin-top: 20px;

                        span:nth-child(2) {
                            margin-top: 15px;
                        }
                    }
                }

                .store_name {
                    font-weight: 600;
                    font-size: 14px;
                    color: #1c1c1c;
                    margin-top: 5px;
                    display: inline-block;
                }
            }

            .store_info_right {
                margin-left: 160px;
                text-align: center;

                .item {
                    margin-left: 50px;

                    span {
                        margin-bottom: 15px;
                        display: inline-block;
                    }
                }
            }
        }

        .shop_info {
            width: 100%;
            min-height: 382px;
            border: 1px solid #f1f1f1;
            margin: 10px 0;
            padding: 38px;

            .good_info {
                width: 278px;
                margin-right: 80px;
                text-align: center;

                .image {
                    width: 203px;
                    height: 203px;
                    margin: 0 auto;
                    border: 1px solid #f3f3f3;

                    img {
                        max-width: 100%;
                        max-height: 100%;
                    }
                }

                .name {
                    margin-top: 15px;
                    color: #666666;
                    line-height: 16px;
                }

                .price {
                    margin-top: 15px;
                    color: #1c1c1c;
                    font-size: 14px;
                    font-weight: 600;
                }
            }

            .right {
                align-self: flex-start;

                .shop_rate {
                    margin: 0px 0 20px;

                    .text {
                        margin-right: 20px;
                        display: inline-block;
                    }
                }

                .remark {
                    .textarea {
                        width: 675px;
                        margin-top: 15px;
                    }
                }

                .picture {
                    margin-top: 15px;
                    .picture_wrap {
                        display: flex;
                        margin-top: 15px;
                        position: relative;
                        .pic_btn {
                            top: 0;
                            width: 72px;
                            height: 72px;
                            background: #ffffff;
                            border: 1px solid #999999;
                            border-radius: 7px;
                            display: flex;
                            justify-content: center;
                            flex-direction: column;
                            align-items: center;
                            cursor: pointer;
                            img {
                                width: 23px;
                                height: 20px;
                                margin-top: 8px;
                            }
                            .countDown {
                                font-size: 10px;
                                font-family: Microsoft YaHei;
                                font-weight: 400;
                                color: #999999;
                                margin-top: 5px;
                                height: 17px;
                            }
                        }

                        input[type="file"] {
                            display: none;
                        }
                    }
                    ul,
                    li {
                        list-style: none;
                    }

                    .picture_list {
                        display: flex;
                        .picture_item {
                            width: 72px;
                            height: 72px;
                            margin-right: 10px;
                            border: 1px solid #dcdcdc;
                            display: flex;

                            position: relative;
                            .close_icon {
                                display: none;
                                position: absolute;
                                top: -7px;
                                right: -7px;
                            }
                            .img_item {
                                width: 70px;
                                height: 70px;
                                background-position: center center;
                                background-size: contain;
                                background-repeat: no-repeat;
                            }

                            &:hover {
                                .close_icon {
                                    display: block;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .submit {
        width: 100%;
        height: 132px;
        text-align: center;
        margin-bottom: 60px;
        border: 1px solid #f1f1f1;

        .btn {
            cursor: pointer;
            display: inline-block;
            width: 190px;
            height: 48px;
            background-color: var(--color_main);
            color: #fff;
            font-size: 14px;
            text-align: center;
            line-height: 48px;
        }
    }
}
