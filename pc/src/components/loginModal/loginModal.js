
import { createApp, render, h, createVNode } from "vue";
import loginModal from './loginModal'
import test from './test'

import { ElDialog } from 'element-plus'
import 'element-plus/lib/theme-chalk/index.css'

let instance

const renderGO = () => {
     const container = document.createElement('div')
     const modalApp = h(test)
     render(modalApp, container)
     document.body.appendChild(container)
     return modalApp
}

const showLogin = () => {
     console.log('showLogin')
     // const dom = document.createElement('div')
     // document.body.appendChild(dom)
     // const app = createApp(test, {})
     // console.log(app)
     // app.use(ElDialog).mount(dom)

     if (!instance) {
          instance = renderGO()
          showLogin()
     } else {
          console.log(instance, 'instance')
          instance.component.proxy.open(0)
     }
}

export default showLogin