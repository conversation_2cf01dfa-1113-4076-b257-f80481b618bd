<template>
	<router-view v-if="systemLoaded" />
</template>

<script>
import { getCurrentInstance, onMounted, reactive, ref, watch } from 'vue'
import { useStore } from 'vuex';
import { useRoute } from 'vue-router';
import { title } from '@/utils/config'
import { useSystemSetting, queryMemberConfig } from './utils/hooks';
export default {
	name: 'App',

	setup () {
		const store = useStore()
		const route = useRoute()
		const defaultImg = require('./assets/common_top_logo.png')
		const defaultBgImg = require('./assets/login_bg.png')
		const defaultForgetBg = require('./assets/forget_pwd_bg.png')
		const defaultMemberTop = require('./assets/member/top_logo.png')
		const defaultRegisterBg = require('./assets/login_bg.png')
		const systemLoaded = ref(false)

		// 获取系统配置信息
		const getSystemConfigInfo = async () => {
			let settingList = [
				"main_site_logo", "main_user_center_logo",
				"main_user_logon_bg", "main_user_register_bg",
				"pc_home_bottom_adv", "main_user_forget_password_bg",
				"basic_site_name", "basic_site_icp",
				"basic_site_copyright", "basic_site_technical_support",
				"platform_customer_service_name", "platform_customer_service_logo",
				"pc_browser_icon",
				]
			const result = await useSystemSetting.getSystemSetting(settingList)
			systemLoaded.value = true
			//下列字段如果没有值则使用默认的图片
			result.main_site_logo = result.main_site_logo || defaultImg
			result.main_user_logon_bg = result.main_user_logon_bg || defaultBgImg
			result.main_user_register_bg = result.main_user_register_bg || defaultRegisterBg
			result.main_user_center_logo = result.main_user_center_logo || defaultMemberTop
			result.main_user_forget_password_bg = result.main_user_forget_password_bg || defaultForgetBg

			//监听路由, 商品详情使用商品名做title,其余都是basic_site_name做title
			watch(() => route.name, (nv) => {
				if (nv !== 'GoodsDetail') {
					if (result.basic_site_name) {
						document.title = result.basic_site_name
					} else {
						document.title = title
					}
				}
			}, { immediate: true })

			store.commit('updateConfigInfo', result);
			var link = document.querySelector("link[rel*='icon']") || document.createElement('link');
			link.type = 'image/x-icon';
			link.rel = 'shortcut icon';
			link.href = result.pc_browser_icon ? result.pc_browser_icon : location.origin + '/favicon.ico';
			document.getElementsByTagName('head')[0].appendChild(link);
		};





		// 全局处理超级会员配置
		const getSuperMemberConfig = () => {

			//超级会员权益
			const memberConfigField = [
				'super_is_enable',
				'super_custom_name',
				'super_pc_page',
				'super_personal_center_icon',
				'super_discount_enabled',
				'super_discount',
				'super_freight_coupon_enabled',
				'super_coupon_enabled',
				'super_integral_multiple_enabled',
				'super_integral_multiple',
				'super_birthday_coupon_enabled',
			]

			const getConfig = async () => {
				const result = await queryMemberConfig(memberConfigField)
				store.commit('saveMemberConfig', result)
			}
			getConfig()
			watch(() => store.state.loginFlag, (nv) => {
				if (nv) {
					getConfig()
				}
			})
		}


		onMounted(() => {
			getSystemConfigInfo()
			getSuperMemberConfig()
		})

		return {
			systemLoaded
		}

	}

}
</script>

<style lang="scss">
@import "./style/reset.scss";
@import "./style/base.scss";

//放大镜位置
.mouse-cover-canvas {
	position: absolute;
	top: 173px !important;
	left: 740px !important;
}

.el-checkbox {
	.el-checkbox__input {

		&.is-checked,
		&.is-indeterminate {
			.el-checkbox__inner {
				border-color: var(--color_main);
				background-color: var(--color_main);
			}
		}

		&.is-focus {
			.el-checkbox__inner {
				border-color: var(--color_main);
			}
		}

		.el-checkbox__inner {
			&:hover {
				border-color: var(--color_main);
			}
		}

		&+.el-checkbox__label {
			color: var(--color_main);
		}
	}
}
</style>
