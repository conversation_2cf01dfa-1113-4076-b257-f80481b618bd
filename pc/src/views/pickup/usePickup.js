import { computed, ref, unref } from "vue";

export function useOrderPickInfo () {

	const orderVOS = ref([])

	const withDeliverMethod = computed(() => {
		if (!orderVOS.value.length) {
			return 0
		}
		let deliverMethods = orderVOS.value.map(item => item.deliverMethod)

		return {
			hasPickup: deliverMethods.some(i => i == 2),
			hasExpress: deliverMethods.some(i => i == 1)
		}
	})


	const showSite10 = computed(() => {
		const [product] = unref(orderVOS)
		return product ? product.selfPickupPointInfo : null
	})


	return {
		showSite10,
		withDeliverMethod,
		orderVOS
	}

}