<template>
	<div class="pick_order_info flex_row_start_start">
		<div style="margin-right: 180px;" v-if="showFull">
			<p class="title">{{ L['配送信息'] }}</p>
			<div>
				<div class="order_item">
					<span class="label">{{ L['配送方式'] }}：</span>
					<span class="value">{{ L['上门自提'] }}</span>
				</div>
				<div class="order_item">
					<span class="label">{{ L['自提时间'] }}：</span>
					<span class="value">{{ orderInfo.pickupTime }}</span>
				</div>
				<div class="order_item">
					<span class="label">{{ L['提货手机号'] }}：</span>
					<span class="value">{{ orderInfo.receiverMobile }}</span>
				</div>
			</div>
		</div>


		<div class="pick_up_info">
			<p class="title">{{ L['自提点信息'] }}</p>
			<div class="pick_up_info_i flex_row_start_start">
				<div class="logo">
					<img :src="pickInfo.pointLogo" alt="">
				</div>
				<div style="margin-left: 20px;flex: 1;">
					<div class="order_item flex_row_start_start" style="flex: 1">
						<span class="label">{{ L['自提点'] }}：</span>
						<span class="value">{{ pickInfo.pointName }}</span>
					</div>

					<div class="order_item">
						<span class="label">{{ L['联系电话'] }}：</span>
						<span class="value">{{ pickInfo.contactPhone }}</span>
					</div>
					<div class="order_item">
						<span class="label">{{ L['详细地址'] }}：</span>
						<span class="value">{{ pickInfo.addressAll }}{{ pickInfo.detailAddress }}</span>
					</div>
					<div class="order_item">
						<span class="label">{{ L['营业时间'] }}：</span>
						<span class="value">
							<template v-if="pickInfo.businessTimeInfo">
								<template v-for="(item, index) in formatBusTime" :key="index">
									<span style="margin-right: 10px;">{{ item }}</span>
								</template>
							</template>
							<template v-else>
								<span>{{ pickInfo.businessTimeTypeValue }}</span>
							</template>
						</span>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
import { getCurrentInstance, computed } from 'vue'
export default {
	props: {
		pickInfo: {
			type: Object,
			default: () => { }
		},
		orderInfo: {
			type: Object,
			default: () => { }
		},
		showFull: {
			type: Boolean,
			default: false
		}
	},
	setup (props) {
		const { proxy } = getCurrentInstance();
		const L = proxy.$getCurLanguage()
		const formatBusTime = computed(() => {
			let text = props.pickInfo.businessTimeInfo?.map(item => {
				let { weeks, timeList } = item
				let toStringTime = timeList.map(i => `${i.startTime}${i.endTime ? ('-' + i.endTime) : ''}`)
				return `${weeks.replace(/,/g, ' ')} ${toStringTime.toString()}`
			})
			return text ?? ''
		})
		return {
			formatBusTime,
			L
		}
	}
}
</script>
<style lang="scss">
.pick_order_info {
	.title {
		color: #1c1c1c;
		font-size: 16px;
		line-height: 35px;
		font-weight: 600;
	}

	.order_item {
		line-height: 30px;

		.label {
			font-size: 12px;
			color: #888888;
			white-space: nowrap;
		}

		.value {
			font-size: 12px;
			color: #1C1C1C;
			word-break: break-all;
		}
	}

	.navigate {
		cursor: pointer;

		img {
			width: 12px;
			height: 14px;
			margin-right: 4px;
		}
	}

	.pick_up_info {
		.pick_up_info_i {
			margin-top: 14px;
			width: 360px;
		}

		.logo {
			img {
				width: 64px;
				height: 64px;
				border-radius: 4px;
				object-fit: contain;
				border: 1px solid #EAEAEA;
			}
		}
	}
}
</style>