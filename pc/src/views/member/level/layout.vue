<template>
  <div class="member_level_layout" id="member_level_layout">
    <mainIndex></mainIndex>
  </div>
</template>


<script>
import { onMounted, nextTick } from 'vue'
import { useStore } from 'vuex';
import { set16ToRgb } from '@/diyStyle'
import mainIndex from './index.vue'
export default {
  components: {
    mainIndex
  },
  setup () {
    const store = useStore()
    const diyStyle = store.state.x_diyStyle
    const colorMainDiy = (op) => set16ToRgb(diyStyle['--color_main'], op)

    const member_level_colorSet = {
      '--level_fade_btn': `linear-gradient(90deg, ${colorMainDiy(0.65)} 0%, ${colorMainDiy(0.65)} 0%, ${colorMainDiy(0.85)} 100%)`,
      '--level_line': colorMainDiy(0.7),
      '--level_halo': colorMainDiy(0.08),
      '--level_halo_2': colorMainDiy(0.06),
      '--level_bg': `linear-gradient(90deg, ${colorMainDiy(0.6)} 0%, ${colorMainDiy(0.8)} 100%)`,
      '--level_border': colorMainDiy(0.7),
      '--level_circle': colorMainDiy(0.99),
      '--level_circle_shadow': colorMainDiy(0.26),
      '--level_eq_dot': colorMainDiy(0.8),
      '--level_eq_dot_halo': colorMainDiy(0.6),
    }

    onMounted(() => nextTick(() => {
      const member_level_layout = document.querySelector('#member_level_layout');
      for (let i in member_level_colorSet) {
        member_level_layout.style.setProperty(i, member_level_colorSet[i]);
      }
    }))

    return {}
  }
}
</script>


<style lang="scss">
.eq_icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--level_halo);
  display: flex;
  align-items: center;
  justify-content: center;

  i {
    font-style: normal;
    font-size: 36px;
    color: var(--level_border);
  }
}

.eq_icon_xs {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: var(--level_halo_2);
  display: flex;
  align-items: center;
  justify-content: center;

  i {
    font-style: normal;
    font-size: 28px;
    color: var(--color_main);
  }
}

.eq_dot {
  width: 25px;
  min-height: 25px;
  background: var(--level_eq_dot_halo);
  position: relative;
  border-radius: 50%;

  &::before {
    content: '';
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: var(--level_eq_dot);
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-8px, -8px);
  }
}
</style>

