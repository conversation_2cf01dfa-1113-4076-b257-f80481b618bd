<template>
     <div class="level_rule">
          <el-dialog v-model="dialogVisible" width="600px">
               <div class="eq_d_title">
                    <span>等级规则</span>
               </div>
               <div class="rule_detail">
                    <el-scrollbar>
                         <div v-html="rule"></div>

                    </el-scrollbar>
               </div>

          </el-dialog>
     </div>
</template>

<script>
import { quillEscapeToHtml } from '@/utils/common'
import { ref, reactive, watch, onMounted, getCurrentInstance } from 'vue'
export default {
     setup(props) {

          const { proxy } = getCurrentInstance()
          const dialogVisible = ref(false)
          const rule = ref('')
          function getRule() {
               let param = {}
               param.str = 'level_rule';
               proxy.$get('v3/member/front/memberSetting/getSettingList', param).then(res => {
                    rule.value = res.data[0].value ? quillEscapeToHtml(res.data[0].value) : '';
               })
          }

          onMounted(() => {
               getRule()
          })

          return {
               dialogVisible,
               rule
          }
     }
}
</script>

<style lang="scss">
.level_rule {
     .el-dialog__header {
          display: none;
     }

     .el-dialog__body {
          padding: 0;
          border-radius: 10px;

     }

     .el-dialog {
          border-radius: 10px;

     }
}

.eq_d_title {
     text-align: center;
     font-size: 28px;
     font-family: PingFang;
     font-weight: 600;
     color: #000000;
     position: relative;
     padding-top: 20px;
}

.rule_detail {
     padding: 20px;
     height: 600px;

     img {
          max-width: 560px;
     }
}
</style>