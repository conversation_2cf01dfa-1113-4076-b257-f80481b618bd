<template>
	<div class="all_eq_con">
		<el-dialog v-model="dialogVisible" width="600px">
			<div class="eq_d_title">
				<span>会员权益</span>
			</div>
			<div class="all_equity">
				<el-scrollbar>
					<div></div>
					<div class="eq_item" v-for="(item, index) in this.lvData" :key="index">
						<div class="circle_icon_wrap">
							<!-- <div class="dots"></div> -->
							<!-- <img src="@/assets/member_level/dots.png" class="dots" /> -->
							<div class="eq_dot"></div>
							<div class="progress_line"></div>
						</div>
						<div class="progress_text">
							<div class="eq_text flex_row_start_center">
								<div class="text_1">{{ item.levelName }}</div>
								<div class="text_2">成长值达到{{ item.growthValue }}</div>
							</div>
							<div class="cur_lv" v-if="cur_lvId == item.levelId">
								<span>当前成长值</span>
								<span class="text_0">{{ curMemGrowth }}</span>
							</div>
							<div class="eq_list flex_row_between_center">
								<div class="eq_item flex_column_center_center" @click="open_cp(item, 'couponList')"
									v-if="item.couponEnable == 1">
									<!-- <img src="@/assets/member_level/coupon.png" /> -->
									<div class="eq_icon">
										<i class="iconfont iconyouhuiquan1"></i>
									</div>
									<div class="eq_item_div">优惠券</div>
								</div>
								<div class="eq_item flex_column_center_center" @click="open_cp(item, 'freightCouponList')"
									v-if="item.freightCouponEnable == 1">
									<!-- <img src="@/assets/member_level/coupon_fee.png" /> -->
									<div class="eq_icon">
										<i class="iconfont iconyunfeiquan"></i>
									</div>
									<div class="eq_item_div">运费券</div>
								</div>
								<div class="eq_item flex_column_center_center" v-if="item.integralMultipleEnable == 1">
									<!-- <img src="@/assets/member_level/integral.png" mode="aspectFit" /> -->
									<div class="eq_icon">
										<i class="iconfont iconjifen"></i>
									</div>
									<div class="eq_item_div">{{ item.integralMultipleValue }}倍积分</div>
								</div>
							</div>
						</div>
					</div>
				</el-scrollbar>
			</div>
		</el-dialog>
		<eqPop ref="eqPop" :data_cp="ch_cp" :cpLen="chCp_len"></eqPop>

	</div>
</template>

<script>
import eqPop from './eqPop.vue'
import { ref, reactive, watch, onMounted, getCurrentInstance } from 'vue'
export default {
	components: {
		eqPop
	},


	setup (props) {
		const cur_lvId = ref(0)
		const ch_cp = ref({})
		const chCp_len = ref(0)
		const lvData = ref([])
		const curMemGrowth = ref(0)
		const { proxy } = getCurrentInstance()
		const dialogVisible = ref(false)
		function getData () {
			proxy.$get('v3/member/front/memberLevel/levelList',).then(res => {
				if (res.state == 200) {
					lvData.value = res.data.sort((a, b) => a.level - b.level)
				}
			})
		}

		function open_cp (obj, prop) {
			// ch_cp.value = obj[prop][0]
			// chCp_len.value = obj[prop].length
			let cpState = {}
			cpState.chCp_len = obj[prop].length
			cpState.ch_cp = obj[prop][0]
			proxy.$refs.eqPop.setData(cpState)
			proxy.$refs.eqPop.dialogVisible = true
			proxy.$refs.eqPop.dialogVisible = true
		}


		onMounted(() => {
			getData()
		})

		return {
			open_cp,
			dialogVisible,
			lvData,
			cur_lvId,
			curMemGrowth
		}
	}
}
</script>

<style lang="scss">
.eq_d_title {
	text-align: center;
	font-size: 28px;
	font-family: PingFang;
	font-weight: 600;
	color: #000000;
	position: relative;
	padding-top: 20px;

	image {
		position: absolute;
		right: 0px;
		width: 84px;
		height: 31px;
	}
}

.all_eq_con {
	.el-dialog__header {
		display: none;
	}

	.el-dialog__body {
		padding: 0;
		border-radius: 10px;

	}

	.el-dialog {
		border-radius: 10px;

	}
}

.all_equity {
	padding: 0 20px;
	padding-top: 30px;
	height: 620px;

	.eq_item {
		display: flex;
		align-items: flex-start;
		position: relative;

		&:last-child {
			.circle_icon_wrap {
				justify-content: flex-start;

				.progress_line {
					display: none;
				}

			}
		}

		.circle_icon_wrap {
			width: 40px;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			margin-right: 37px;
			position: absolute;
			top: 0;
			height: 100%;

			.dots {
				width: 25px;
				height: 25px;
				border-radius: 50%;

			}

			.progress_line {
				height: 100%;
				width: 2px;
				margin-top: -2px;
				background: var(--level_line);
				margin-right: 0px;
			}
		}

		.progress_text {
			white-space: pre-wrap;
			color: #999999;
			font-weight: 500;
			margin-left: 50px;
			padding-bottom: 50px;

			.eq_text {
				transform: translateY(2px);

				.text_1 {
					font-size: 18px;
					font-family: PingFang SC;
					font-weight: bold;
					color: #000000;
				}

				.text_2 {
					margin-left: 10px;
					font-size: 16px;
					font-family: PingFang SC;
					font-weight: 500;
					color: #666666;
				}
			}

			.cur_lv {
				font-size: 16px;
				font-family: PingFang SC;
				margin-top: 15px;

				.text_0 {
					color: var(--color_main);
				}
			}

			.eq_list {
				margin-top: 36px;

				.eq_item {
					margin-right: 50px;
					text-align: center;

					&:last-child {
						margin-right: 0px;
					}

					img {
						width: 60px;
						height: 60px;
					}

					.eq_item_div {
						width: 100%;
						text-align: center;
						margin-top: 10px;
						font-size: 16px;
						font-family: PingFang SC;
						font-weight: 500;
						color: #505050;
					}
				}
			}
		}

	}

}
</style>