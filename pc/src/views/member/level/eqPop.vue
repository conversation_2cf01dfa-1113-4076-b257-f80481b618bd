<template>
	<div class="equity_pop">
		<el-dialog type="center" v-model="dialogVisible" width="460px">
			<div class="eq_d">
				<div class="eq_d_title">
					<span>权益详情</span>
				</div>
				<div class="eq_dD flex_column_start_start">
					<div class="cp_bg flex_row_center_center" :style="{ backgroundImage: `url(${cp_bg})` }">
						<div class="cp_type">
							{{ curCoupon.couponTypeValue }}
						</div>
						<div class="cp_left flex_column_center_center">
							<div class="price_c" v-if="curCoupon.couponType == 2">
								<span class="p_b">{{ curCoupon.publishValue }}</span>
								<span class="p_s">%</span>
							</div>
							<div class="price_c" v-else>
								<template v-if="curCoupon.couponType == 1 || curCoupon.couponType == 3">
									<span class="p_s">￥</span>
									<span class="p_b">{{ curCoupon.publishValue || curCoupon.randomMax }}</span>
								</template>
								<!-- 免运费券展示优惠券名 -->
								<div class="p_b" :title="curCoupon.couponName" v-else>
									<span v-if="curCoupon.value > 0">
										￥{{ curCoupon.value }}
									</span>
									<span v-else>免运费</span>
								</div>
							</div>
							<span class="cp_desc">{{ curCoupon.couponContent }}</span>
						</div>
						<div class="cp_right flex_column_center_center">
							<div class="desc_1">{{ curCoupon.description }}</div>
							<div class="desc_2" v-if="curCoupon.effectiveTimeType == 1">{{ curCoupon.effectiveStart
							}}-{{ curCoupon.effectiveEnd }}</div>
							<div class="desc_2" v-if="curCoupon.effectiveTimeType == 2">有效期时长{{ curCoupon.cycle }}天
							</div>
						</div>
					</div>
					<div class="cp_bg_copied" :style="{ backgroundImage: `url(${cp_bg})` }"></div>
					<div class="desc_p">
						共{{ curCpLength }}张，领取后可去我的优惠券查看
					</div>
				</div>
				<div class="eq_d_but_con flex_row_center_center">
					<div class="eq_d_but" @click="dialogVisible = false">确定</div>
				</div>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import { ref, reactive, watch, onMounted } from 'vue'
export default {
	props: {
		data_cp: {
			type: Object,
			default: () => { }
		},

		cpLen: {
			type: Number,
			default: 0
		}
	},

	setup (props) {
		const dialogVisible = ref(false)
		const curCpLength = ref(0)
		const curCoupon = ref({})
		const cp_bg = require('@/assets/member_level/cp_bg.png')
		const setData = (state) => {
			let { chCp_len, ch_cp } = state
			curCoupon.value = new Proxy(ch_cp, {
				get (target, prop) {
					if ((prop == 'effectiveStart' || prop == 'effectiveEnd') && target[prop]) {
						let res = target[prop].split(':')
						res.pop()
						return res.join(':')
					}
					return target[prop];
				}
			})
			curCpLength.value = chCp_len
		}

		const calSize = (name) => {
			let basic = 35
			return name.length > 4 ? (basic - name.length * 1.5) + 'px' : basic + 'px'
		}

		onMounted(() => {
		})

		return {
			calSize,
			dialogVisible,
			setData,
			curCoupon,
			curCpLength,
			cp_bg
		}
	}
}
</script>

<style lang="scss">
.equity_pop {
	.el-dialog__header {
		display: none;
	}

	.el-dialog__body {
		padding: 0;
		border-radius: 10px;

	}

	.el-dialog {
		border-radius: 10px;

	}
}


.eq_d {
	width: 460px;
	height: 393px;
	background: #FFFFFF;
	box-shadow: 0px 0px 27px 6px rgba(85, 85, 85, 0.3);
	border-radius: 10px;
	padding-top: 13px;
	padding-left: 18px;
	padding-right: 18px;
	position: relative;

	.eq_d_title {
		text-align: center;
		font-size: 18px;
		font-family: PingFang;
		font-weight: 500;
		color: #000000;
		position: relative;

		image {
			position: absolute;
			right: 0px;
			width: 84px;
			height: 31px;
		}
	}

	.eq_dD {
		margin-top: 40px;
		position: relative;

		.cp_bg {
			width: 423px;
			height: 158px;
			background-position: center center;
			background-repeat: no-repeat;
			background-size: cover;
			z-index: 4;
			position: relative;

			.cp_left {
				width: 163px;

				.price_c {
					color: #604E44;

					.p_s {
						font-size: 15px;
						font-family: PingFang SC;
						font-weight: bold;
					}

					.p_b {
						font-size: 35px;
						font-family: Source Han Sans CN;
						font-weight: 800;

						&.cp_name {
							max-width: 120px;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
						}
					}


				}

				.cp_desc {
					margin-top: 6px;
					font-size: 14px;
					font-family: Source Han Sans CN;
					font-weight: 400;
				}
			}

			.cp_right {
				width: 268px;

				.desc_1 {
					font-size: 24px;
					font-family: Source Han Sans CN;
					font-weight: 500;
					color: #604E44;
					overflow: hidden;
					text-overflow: ellipsis;
					word-break: break-all;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					max-width: 208px;
				}

				.desc_2 {
					margin-top: 8px;
					font-size: 15px;
					font-family: Source Han Sans CN;
					font-weight: 400;
					color: #604E44;
					white-space: nowrap;
					transform: scale(0.75);
				}
			}

			.cp_type {
				position: absolute;
				top: 0;
				left: 0;
				height: 31px;
				padding: 0 10px;
				background: var(--level_bg);
				border-radius: 9px 0px 9px 0px;
				line-height: 31px;
				font-size: 18px;
				font-weight: 400;
				color: #FFF;
			}

		}

		.cp_bg_copied {
			width: 423px;
			height: 158px;
			background-position: center center;
			background-repeat: no-repeat;
			background-size: cover;
			position: absolute;
			left: 5px;
			top: 3px;
		}


		.desc_p {
			margin-top: 10px;
			font-size: 16px;
			font-family: PingFang SC;
			font-weight: 500;
			color: #999999;
		}
	}


	.eq_d_but {
		margin-top: 50px;
		width: 150px;
		height: 42px;
		background: var(--level_fade_btn);
		border-radius: 33px;
		line-height: 42px;
		text-align: center;
		color: #fff;
		cursor: pointer;
	}
}

// asd
</style>