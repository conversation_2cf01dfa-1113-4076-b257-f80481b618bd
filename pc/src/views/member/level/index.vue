<template>
     <div class="member_level_main" id="member_level_main">
          <div class="member_level">
               <div class="top_banner bg_style" :style="{ backgroundImage: `url(${top_banner})` }"
                    v-if="memberInfo.isSuper !== 1">
                    <router-link to="/super/confirm">
                         <span>开通{{ memberConfig.super_custom_name || '超级会员' }}，享受超值尊享特权</span>
                    </router-link>
               </div>

               <div class="level_info flex_row_between_start">
                    <div class="level_left">
                         <div class="top_info flex_row_between_center bg_style">
                              <div class="info1 flex_row_start_center">
                                   <div class="avatar_con flex_row_center_center">
                                        <div class="avatar bg_style"
                                             :style="{ backgroundImage: `url(${lvData.memberAvatar})` }">
                                        </div>
                                   </div>
                                   <div class="growth_name">
                                        <div class="name">成长值</div>
                                        <div class="growth">{{ lvData.memberGrowthValue }}</div>
                                   </div>
                              </div>

                              <div class="info2">

                                   <div class="to_rule flex_row_start_center" @click="rule_open">
                                        <span class="name_txt">等级规则</span>
                                        <span class="iconfont iconziyuan11"></span>
                                   </div>


                                   <div class="lv_name flex_row_between_center">
                                        <img src="@/assets/member_level/vip_gold.png" mode="aspectFit" class="lv_logo" />
                                        <div class="tab_space">
                                        </div>
                                        <span class="lv_n" v-if="lvData.currentLevel">{{ lvData.currentLevel.levelName
                                        }}</span>
                                        <span class="lv_n" v-else>普通用户</span>
                                   </div>
                              </div>

                              <div :style="{ backgroundImage: `url(${info_bg})` }" class="white_bottom"></div>
                         </div>
                         <div>
                              <el-scrollbar>
                                   <div class="level_list flex_row_start_center" id="level_list">
                                        <div v-for="(item, index) in level_list" :key="index" class="level_item"
                                             :id="`level_${item.levelId}`">
                                             <div :class="{ swiper_item: true, sel: index <= current }">
                                                  <div class="level_title" :class="{ hidden: item.levelId == 0 }">{{
                                                       item.levelName
                                                  }}
                                                  </div>
                                                  <div class="level_pro flex_row_center_center">
                                                       <div class="dot_cho" v-if="index <= current"></div>
                                                       <div class="dot_cho blank" v-else></div>
                                                       <!-- <img src="@/assets/member_level/level_dot.png" v-else /> -->
                                                       <div class="pro_line" v-if="item.levelId != flevelId">
                                                            <div class="bold_line"
                                                                 :style="{ width: proStyle(index, current) }">
                                                            </div>
                                                       </div>
                                                  </div>
                                                  <div class="level_value">{{ item.growthValue }}</div>
                                             </div>
                                        </div>
                                   </div>
                              </el-scrollbar>
                         </div>
                         <div class="lv_equity">
                              <div class="eq_title">等级权益</div>
                              <div class="eq_list flex_row_start_center">
                                   <div class="eq_item flex_column_center_center" @click="open_cp('couponList')"
                                        style="cursor: pointer;" v-if="cur_lev.couponEnable == 1">
                                        <div class="eq_icon">
                                             <i class="iconfont iconyouhuiquan1"></i>
                                        </div>
                                        <span>优惠券</span>
                                   </div>
                                   <div class="eq_item flex_column_center_center" @click="open_cp('freightCouponList')"
                                        style="cursor: pointer;" v-if="cur_lev.freightCouponEnable == 1">
                                        <div class="eq_icon">
                                             <i class="iconfont iconyunfeiquan"></i>
                                        </div>
                                        <span>运费券</span>
                                   </div>
                                   <div class="eq_item flex_column_center_center" v-if="cur_lev.integralMultipleEnable == 1">
                                        <!-- <img src="@/assets/member_level/integral.png" /> -->
                                        <div class="eq_icon">
                                             <i class="iconfont iconjifen"></i>
                                        </div>
                                        <span>{{ cur_lev.integralMultipleValue }}倍积分</span>
                                   </div>
                                   <div class="eq_item flex_column_center_center" @click="open_all()"
                                        style="cursor: pointer;">
                                        <!-- <img src="@/assets/member_level/all_eq.png" /> -->
                                        <div class="eq_icon">
                                             <i class="iconfont iconquanbuquanyi"></i>
                                        </div>
                                        <span>全部权益</span>
                                   </div>
                              </div>
                         </div>
                    </div>

                    <div class="level_right">
                         <div class="lv_mission">
                              <div class="mi_title">任务中心</div>
                              <div class="mi_list" v-show="mi_list_loaded">
                                   <template v-for="(item, index) in mi_list">
                                        <div class="mi_item flex_row_between_center" :key="index"
                                             v-if="item.show && item.enable == 1">
                                             <div class="mi_i_left flex_row_start_center">
                                                  <!-- <img :src="item.img" /> -->

                                                  <div class="eq_icon_xs">
                                                       <i :class="['iconfont', item.img]"></i>
                                                  </div>

                                                  <div class="mi_text flex_column_between_start">
                                                       <div>
                                                            <span class="text_1">{{ item.name }}</span>
                                                       </div>
                                                       <span class="text_2">{{ item.desc }}</span>
                                                  </div>
                                             </div>
                                             <div class="mi_i_right">
                                                  <div class="fi_but_1" v-if="item.state == 0" @click="navTo(item.desti)">去完成
                                                  </div>
                                                  <div class="fi_but_2" v-else @click="navTo(item.desti)">去逛逛</div>
                                             </div>
                                        </div>
                                   </template>
                              </div>
                         </div>
                    </div>
               </div>


               <div class="recommend_goods">
                    <div class="title flex_row_center_center">
                         <img src="@/assets/member_level/recommend_dots.png">
                         <div class="title_name">热销推荐</div>
                         <img src="@/assets/member_level/recommend_dots.png">
                    </div>

                    <div class="hot_recommend_list1 flex_row_start_center">
                         <div class="hot_recommend_pre flex_column_between_start"
                              v-for="(hotItem, hotIndex) in hotRecommendList" :key="hotIndex"
                              @click="goGoodsDetail(hotItem.defaultProductId)">
                              <div class="recommend_pre_top">
                                   <div class="hot_recommend_pre_img"
                                        :style="{ backgroundImage: 'url(' + hotItem.goodsImage + ')' }"></div>
                                   <div class="hot_recommend_pre_price">
                                        <span>￥{{ hotItem.goodsPrice }}&nbsp;&nbsp;</span>
                                        <span v-if="hotItem.marketPrice">￥{{ hotItem.marketPrice }}</span>
                                   </div>
                                   <div class="hot_recommend_pre_name">{{ hotItem.goodsName }}</div>
                                   <div class="hot_recommend_pre_brief">{{ hotItem.goodsBrief }}</div>
                              </div>
                              <div class="recommend_pre_bottom">
                                   <div class="hot_recommend_pre_sale">{{ L['已售'] }}<span>{{ hotItem.saleNum }}</span></div>
                                   <router-link target="_blank" :to="`/store/index?vid=${hotItem.storeId}`"
                                        class="hot_recommend_pre_store" @click.stop>
                                        {{ hotItem.storeName }} >
                                   </router-link>
                                   <div class="hot_recommend_pre_activity">
                                        <span class="recommend_activity_pre"
                                             v-for="(acItem, actIndex) in hotItem.activityList" :key="actIndex">{{
                                                  acItem.promotionName }}</span>
                                   </div>
                              </div>
                         </div>
                    </div>
               </div>


               <equity ref="equity"></equity>
               <rule ref="rule"></rule>
               <eqPop ref="eqPop" :data_cp="ch_cp" :cpLen="chCp_len"></eqPop>
          </div>
     </div>
</template>


<script>
import rule from './rule.vue'
import eqPop from './eqPop.vue'
import equity from './equity.vue'
import { ref, getCurrentInstance, onMounted, toRefs, reactive, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex';
export default {
     components: {
          equity,
          eqPop,
          rule
     },
     setup (props) {
          const router = useRouter()
          const store = useStore()
          const memberInfo = ref(store.state.memberInfo)
          const memberConfig = ref(store.state.memberConfig)
          const top_banner = require('@/assets/member_level/top_banner.png')
          const info_bg = require('@/assets/member_level/white_bottom.png')
          const current = ref(0)
          const { proxy } = getCurrentInstance()
          const L = proxy.$getCurLanguage()
          const hotRecommendList = ref([])
          const lvData = ref({})
          const cur_lev = ref(0)
          const level_list = ref([])
          const flevelId = ref(0)
          const cpState = reactive({
               ch_cp: {},
               chCp_len: 0,
          })
          const mi_list_loaded = ref(false)

          const mi_list = ref([{
               name: '完善信息',
               desc: '完成后获得$1点成长值',
               img: 'iconwanshanxinxi',
               state: 0,
               desti: '/member/info',
               show: true,
               stxS: 0,
               stxE: 2
          },
          {
               name: '购买商品',
               desc: '每消费$2元，获得$1点成长值',
               img: 'icongoumaishangpin',
               state: 0,
               desti: '/index',
               show: true,
               stxS: 2,
               stxE: 5
          },
          {
               name: '发布评价',
               desc: '每发布$2条优质评价，获得$1点成长值',
               img: 'iconfabupinglun',
               state: 0,
               desti: '/member/order/evaluation',
               show: true,
               stxS: 5,
               stxE: 8
          },
          {
               name: '登录',
               desc: '每日登录可获得$1点成长值',
               img: 'icondenglu',
               state: 1,
               desti: '/index',
               show: true,
               stxS: 8,
               stxE: 10
          }
          ])



          const set_str = [
               'task_complete_info_enabled',
               'task_complete_info_growth_value',

               'task_purchase_enabled',
               'task_purchase_growth_value',
               'task_purchase_every',

               'task_comment_enabled',
               'task_comment_growth_value',
               'task_comment_every',

               'task_login_enabled',
               'task_login_growth_value',
          ]






          //获取热门推荐商品列表
          const getHotRecommendList = () => {
               let params = {
                    queryType: 'cart',
                    pageSize: 10,
                    current: 1
               };
               proxy.$get("v3/goods/front/goods/recommendList", params).then((res) => {
                    if (res.state == 200) {
                         hotRecommendList.value = res.data.list;
                    }
               });
          };




          const getData = () => {
               proxy.$get('v3/member/front/memberLevel/getInfo').then(res => {
                    if (res.state == 200) {
                         lvData.value = res.data
                         lvData.value.memberLevelList = res.data.memberLevelList.sort((a, b) => a.level - b.level)
                         cur_lev.value = res.data.currentLevel

                         if (!cur_lev.value.level) {
                              lvData.value.memberLevelList.unshift({
                                   growthValue: 0,
                                   level: 0,
                                   levelId: 0,
                                   levelName: "--"
                              })
                              cur_lev.value.levelId = 0
                              lvData.value.memberGrowthValue = 0
                         }
                         // setData()
                         flevelId.value = lvData.value.memberLevelList[lvData.value.memberLevelList.length - 1].levelId
                         if (lvData.value.memberLevelList.length > 4) {
                              handleLv()
                         } else {
                              level_list.value = lvData.value.memberLevelList
                              let eid = level_list.value.findIndex(i => i.levelId == cur_lev.value.levelId)
                              current.value = eid
                         }
                         if (lvData.value.isSignCompleted) {
                              mi_list.value[3].desti = '/index'
                              mi_list.value[3].state = 1
                         }

                         if (lvData.value.isInfoCompleted) {
                              mi_list.value[0].state = 1
                              mi_list.value[0].show = false
                         }
                         getSetting()
                    }
               })
          }



          const handleLv = () => {
               let arr = lvData.value.memberLevelList
               let eid = arr.findIndex(i => i.levelId == cur_lev.value.levelId)
               if (eid > -1) {
                    level_list.value = arr
                    current.value = eid
                    nextTick(() => {
                         document.getElementById('level_list').querySelector(`#level_${cur_lev.value.levelId}`).scrollIntoView({
                              // 定义动画过渡效果， "auto"或 "smooth" 之一。默认为 "auto"
                              behavior: "smooth",
                              // 定义垂直方向的对齐， "start", "center", "end", 或 "nearest"之一。默认为 "start"
                              block: "center",
                              // 定义水平方向的对齐， "start", "center", "end", 或 "nearest"之一。默认为 "nearest"
                              inline: "nearest"
                         })
                    })
               }
          }


          const getSetting = () => {
               let param = {}
               param.str = set_str.join(',');
               proxy.$get('v3/member/front/memberSetting/getSettingList', param).then(res => {
                    let set_list = res.data;
                    mi_list.value.map(mi => {
                         let set_fra = set_list.slice(mi.stxS, mi.stxE)
                         mi.enable = set_fra[0].value
                         mi.growth_value = set_fra[1].value
                         mi.desc = mi.desc.replace('$1', mi.growth_value)

                         if (set_fra[2]) {
                              mi.every = set_fra[2].value
                              mi.desc = mi.desc.replace('$2', mi.every)
                         }
                    })
                    mi_list_loaded.value = true
               })
          }


          const open_cp = (prop) => {
               cpState.ch_cp = cur_lev.value[prop][0]
               cpState.chCp_len = cur_lev.value[prop].length
               proxy.$refs.eqPop.setData(cpState)
               proxy.$refs.eqPop.dialogVisible = true
          }


          const open_all = () => {
               proxy.$refs.equity.cur_lvId = cur_lev.value.levelId
               proxy.$refs.equity.curMemGrowth = lvData.value.memberGrowthValue
               proxy.$refs.equity.dialogVisible = true
          }

          const rule_open = () => {
               proxy.$refs.rule.dialogVisible = true
          }


          const proStyle = (index, current) => {

               let width
               if (index < current) {
                    width = '100%'
               } else if (index > current) {
                    width = '0%'
               } else {
                    let nextGrowth = level_list.value[index + 1].growthValue
                    let curGrowth = level_list.value[index].growthValue

                    let diff_len = nextGrowth - curGrowth
                    let offset = lvData.value.memberGrowthValue - curGrowth
                    width = `${parseInt((offset / diff_len) * 100)}%`

               }
               return width
          }

          const navTo = (url) => {
               let newWin = router.resolve(url)
               window.open(newWin.href, '_blank')
          }





          //去商品详情页
          const goGoodsDetail = (productId) => {
               proxy.$goGoodsDetail(productId);
          };

          onMounted(() => {
               getHotRecommendList()
               getData()
          })



          return {
               mi_list_loaded,
               top_banner,
               info_bg,
               current,
               mi_list,
               hotRecommendList,
               L,
               goGoodsDetail,
               lvData,
               cur_lev,
               open_cp,
               open_all,
               rule_open,
               level_list,
               flevelId,
               proStyle,
               ...toRefs(cpState),
               navTo,
               memberConfig,
               memberInfo
          }
     }
}
</script>


<style lang="scss">
.bg_style {
     background-repeat: no-repeat;
     background-size: contain;
     background-position: center center;
     text-align: center;
}


.sld_member_main_content {
     background: #F4F5FA;
}


.dot_cho {
     width: 22px;
     height: 22px;
     background: var(--level_circle);
     box-shadow: 0px 0px 6px 0px var(--level_circle_shadow);
     border-radius: 50%;
     position: relative;

     &::before {
          content: '';
          display: block;
          width: 14px;
          height: 14px;
          background: #FFFFFF;
          border-radius: 50%;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-7px, -6.5px);

     }

     &.blank {
          background: #fff;
          box-shadow: 0px 0px 6px 0px var(--level_circle_shadow);
          border-radius: 50%;
          position: relative;
          opacity: 0.4;

          &::before {
               background: #E4E4E4;
          }
     }
}


.member_level {
     width: 1200px;
     margin: 0 auto;
     padding-top: 20px;

     .top_banner {
          width: 100%;
          height: 100px;
          margin-bottom: 20px;


          span {
               font-size: 28px;
               font-family: PingFang SC;
               font-weight: 500;
               color: #E9CAA2;
               line-height: 100px;
          }
     }

     .level_info {

          .level_left {
               width: 590px;
               background: #fff;

               .top_info {
                    width: 100%;
                    height: 151px;
                    position: relative;
                    background: var(--level_bg);

                    .white_bottom {
                         position: absolute;
                         left: 0;
                         right: 0;
                         bottom: 0;
                         height: 39px;
                         background-position: center center;
                         background-repeat: no-repeat;
                         background-size: contain;
                    }

                    .info1 {
                         margin-left: 20px;
                         margin-bottom: 20px;

                         .avatar_con {
                              width: 83px;
                              height: 83px;
                              border-radius: 50%;
                              background: #fff;

                              .avatar {
                                   width: 75px;
                                   height: 75px;
                                   border-radius: 50%;
                                   background-color: #666;
                              }
                         }

                         .growth_name {
                              text-align: left;
                              margin-left: 12px;

                              .name {
                                   font-size: 16px;
                                   font-family: Microsoft YaHei;
                                   font-weight: 400;
                                   color: #FFFFFF;
                              }

                              .growth {
                                   margin-top: 5px;
                                   font-size: 34px;
                                   font-family: Microsoft YaHei;
                                   font-weight: bold;
                                   color: #FFF;
                              }
                         }
                    }

                    .info2 {
                         position: relative;

                         .to_rule {
                              position: absolute;
                              top: -35px;
                              font-size: 14px;
                              font-family: Microsoft YaHei;
                              font-weight: 400;
                              color: #FFFFFF;
                              white-space: nowrap;
                              right: 10px;
                              cursor: pointer;

                              .name_txt:hover {
                                   text-decoration: underline;
                              }

                              .iconziyuan11 {
                                   font-size: 11px;
                                   margin-left: 3px;
                                   margin-top: 2px;
                              }

                         }

                         .lv_name {
                              padding: 6px 8px;
                              background: #FFEFD4;
                              position: relative;

                              .lv_n {
                                   font-size: 19px;
                                   font-family: PingFang SC;
                                   font-weight: bold;
                                   color: #BB9546;
                                   background: linear-gradient(45deg, #7A492F 0%, #C97D3B 100%);
                                   -webkit-background-clip: text;
                                   -webkit-text-fill-color: transparent;

                              }

                              .tab_space {
                                   width: 20px;
                                   height: 20px;
                                   margin-right: 10px;
                              }

                              .lv_logo {
                                   width: 58px;
                                   height: 59px;
                                   position: absolute;
                                   left: -26px;
                                   bottom: -8px;
                              }
                         }
                    }
               }

               .level_list {
                    margin-top: 30px;
                    width: 590px;
                    padding-bottom: 30px;

                    .level_item {
                         &:last-child {
                              .pro_line {
                                   display: none !important;
                              }
                         }
                    }

                    .swiper_item {
                         flex: 1;
                         width: 110px;
                         margin-right: 30px;



                         .level_title {
                              opacity: 0.4;
                              font-size: 16px;
                              font-family: PingFang SC;
                              font-weight: bold;
                              color: var(--color_main);
                              text-align: center;
                              margin-bottom: 6px;

                              &.hidden {
                                   visibility: hidden;
                              }
                         }

                         .level_value {
                              opacity: 0.4;
                              font-size: 16px;
                              font-family: PingFang SC;
                              font-weight: bold;
                              color: #666666;
                              text-align: center;
                              margin-top: 6px;
                         }

                         .level_pro {
                              position: relative;

                              img {
                                   width: 35px;
                                   height: 35px;
                                   opacity: 0.4;

                              }

                              .pro_line {
                                   width: 119px;
                                   height: 3px;
                                   background: #E4E4E4;
                                   flex: 1;
                                   position: absolute;
                                   left: 59%;
                                   display: flex;
                                   align-items: center;

                                   .bold_line {
                                        height: 6px;
                                        background: var(--level_circle);
                                   }
                              }

                         }

                         &.sel {
                              .level_pro {
                                   img {
                                        opacity: 1;

                                   }
                              }

                              .level_value,
                              .level_title {
                                   opacity: 1;

                              }


                              /* .pro_line {
                                        height: 6px;
                                        background: #FE6847;
                                        border-radius: 2px;
                                        flex: 1;
                                   } */
                         }
                    }
               }

               .lv_equity {
                    background: #FFFFFF;
                    padding: 30px 20px;

                    .eq_title {
                         font-size: 18px;
                         font-family: PingFang SC;
                         font-weight: 400;
                         color: #000000;
                    }

                    .eq_list {
                         margin-top: 24px;
                         padding: 0 15px;

                         .eq_item {
                              margin-right: 90px;

                              &:last-child {
                                   margin-right: 0;
                              }

                              img {
                                   width: 60px;
                                   height: 60px;
                              }

                              span {
                                   margin-top: 19px;
                                   font-size: 16px;
                                   font-family: PingFang SC;
                                   font-weight: 500;

                                   color: #666666;
                              }
                         }
                    }
               }
          }

          .level_right {
               width: 590px;
               background: #fff;

               .lv_mission {
                    background: #FFFFFF;
                    padding: 20px;
                    padding-bottom: 19px;

                    .mi_title {
                         font-size: 16px;
                         font-family: PingFang SC;
                         font-weight: bold;
                         color: #000000;
                    }

                    .mi_list {
                         margin-top: 20px;

                         .mi_item {
                              border-bottom: 1px solid #E8E8E8;
                              padding: 15px 0;
                              margin-bottom: 20px;

                              &:last-child {
                                   border-bottom: none;
                                   padding-bottom: 0;
                              }

                              .mi_i_left {


                                   img {
                                        width: 48px;
                                        height: 48px;
                                   }

                                   .mi_text {
                                        margin-left: 20rpx;
                                        height: 48px;
                                        padding: 2px 0;
                                        margin-left: 10px;

                                        .text_1 {
                                             font-size: 16px;
                                             font-family: PingFang SC;
                                             font-weight: 500;
                                             color: #141414;
                                        }

                                        .text_2 {
                                             font-size: 14px;
                                             font-family: PingFang SC;
                                             font-weight: 500;
                                             color: #999999;
                                        }
                                   }
                              }

                              .mi_i_right {

                                   .fi_but_1 {
                                        width: 82px;
                                        height: 32px;
                                        background: var(--level_fade_btn);
                                        border-radius: 16px;
                                        line-height: 32px;
                                        text-align: center;
                                        font-size: 16px;
                                        font-family: PingFang SC;
                                        font-weight: 500;
                                        color: #FFFFFF;
                                        cursor: pointer;
                                   }

                                   .fi_but_2 {
                                        width: 82px;
                                        height: 32px;
                                        background: #fff;
                                        border-radius: 16px;
                                        line-height: 32px;
                                        text-align: center;
                                        font-size: 16px;
                                        font-family: PingFang SC;
                                        font-weight: 500;
                                        color: var(--color_main);
                                        border: 1px solid var(--level_border);
                                        cursor: pointer;

                                   }

                              }



                         }

                    }
               }



          }
     }


     .recommend_goods {
          margin-top: 20px;
          background: #fff;
          padding-top: 35px;

          .title {
               img {
                    width: 35px;
                    height: 17px;
               }

               .title_name {
                    font-size: 20px;
                    font-family: Microsoft YaHei;
                    font-weight: bold;
                    color: #333333;
                    margin: 0 15px;
               }
          }

          .hot_recommend_list1 {
               padding: 0 10px;
               padding-top: 30px;
               flex-wrap: wrap;

               .hot_recommend_pre {
                    width: 220px;
                    height: 425px;
                    background: #FFFFFF;
                    border-radius: 2px;
                    margin-right: 19px;
                    margin-bottom: 20px;
                    cursor: pointer;

                    &:nth-child(5n) {
                         margin-right: 0;
                    }
               }

               .recommend_pre_top {
                    .hot_recommend_pre_img {
                         width: 220px;
                         height: 220px;
                         border-radius: 2px;
                         background-repeat: no-repeat;
                         background-size: contain;
                         background-position: center;
                    }

                    .hot_recommend_pre_price {
                         margin: 15px 0;

                         span {
                              font-size: 16px;
                              font-family: Microsoft YaHei;
                              font-weight: 400;
                              color: var(--color_main);

                              &:nth-child(2) {
                                   font-size: 12px;
                                   font-family: Microsoft YaHei;
                                   font-weight: 400;
                                   text-decoration: line-through;
                                   color: #999999;
                              }
                         }
                    }

                    .hot_recommend_pre_name {
                         width: 220px;
                         font-size: 14px;
                         font-family: Microsoft YaHei;
                         font-weight: 400;
                         color: #333333;
                         line-height: 18px;
                         text-overflow: -o-ellipsis-lastline;
                         overflow: hidden;
                         text-overflow: ellipsis;
                         display: -webkit-box;
                         -webkit-line-clamp: 2;
                         line-clamp: 2;
                         -webkit-box-orient: vertical;
                    }

                    .hot_recommend_pre_brief {
                         width: 220px;
                         overflow: hidden;
                         text-overflow: ellipsis;
                         white-space: nowrap;
                         margin: 14px 0 15px;
                    }
               }

               .recommend_pre_bottom {
                    .hot_recommend_pre_sale {
                         font-size: 12px;
                         font-family: Microsoft YaHei;
                         font-weight: 400;
                         color: #999999;

                         span {
                              color: var(--color_main);
                         }
                    }

                    .hot_recommend_pre_store {
                         font-size: 14px;
                         font-family: Microsoft YaHei;
                         font-weight: 400;
                         color: #666666;
                         line-height: 18px;
                         margin: 15px 0;
                         display: block;
                    }

                    .hot_recommend_pre_activity {
                         .recommend_activity_pre {
                              min-width: 60px;
                              height: 20px;
                              border: 1px solid $colorMain;
                              border-radius: 3px;
                              font-size: 12px;
                              font-family: Microsoft YaHei;
                              font-weight: 400;
                              color: var(--color_main);
                              text-align: center;
                              display: inline-block;
                              line-height: 17px;
                              padding: 0 10px;
                         }
                    }
               }
          }
     }

}
</style>