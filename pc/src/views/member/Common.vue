<template>
  <div style="background: #fff;width: 100%;">
    <NavTopBar />
    <MemberTop></MemberTop>
    <div class="sld_member_main_content">
      <div class="container">
        <MemberLeftNav></MemberLeftNav>
        <router-view></router-view>
      </div>
      <div class="container" style="margin-top: 13px;">
        <recommendGoods></recommendGoods>
      </div>
    </div>
    <FooterService />
    <FooterLink />
  </div>
</template>

<script>
import NavTopBar from '../../components/NavTopBar'
import FooterService from '../../components/FooterService'
import FooterLink from '../../components/FooterLink'
import MemberTop from '../../components/MemberTop'
import MemberLeftNav from '../../components/MemberLeftNav'
import recommendGoods from '@/components/recommendGoods.vue';

import { reactive, getCurrentInstance, provide, onMounted } from 'vue'
export default {
  name: 'MemberCommon',
  components: {
    MemberTop,
    MemberLeftNav,
    NavTopBar,
    FooterService,
    FooterLink,
    recommendGoods
  },
  setup () {
    const isRouterAlive = true
    const { proxy } = getCurrentInstance()
    const balanceSetting = reactive({
      outputEnable: false,
      rechargeEnable: false
    })

    provide('balanceSetting', balanceSetting)


    const getbalanceSetting = () => {
      proxy.$get('v3/system/front/setting/getSettings', {
        names: 'withdraw_is_enable,withdraw_alipay_is_enable,recharge_is_enable'
      }).then(res => {
        if (res.state == 200) {
          balanceSetting.outputEnable = res.data[0] == '1' && res.data[1] == '1' ? true : false
          balanceSetting.rechargeEnable = res.data[2] == '1'
        }
      })
    }

    onMounted(getbalanceSetting)

    return { isRouterAlive }
  }
}
</script>

<style lang="scss" scoped>
.sld_member_main_content {
  width: 100%;
  background-color: #f7f7f7;
  border-top: 1px solid #ebebeb;
  padding-bottom: 10px;

  .container {
    width: 1210px;
    margin: 0 auto;
    padding-left: 0px;
    padding-right: 0px;

    &:before,
    &:after {
      display: table;
      content: ' ';
    }

    &:after {
      clear: both;
    }
  }
}
</style>
