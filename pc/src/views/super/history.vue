<template>
    <div class="sld_super_history">
        <div class="flex_column_center_center super_header">
            <div class="top_img">
                <img class="top_img_head" src="@/assets/super/head_logo.png" />
                <img class="top_img_logo" :src="memberInfo.memberAvatar" />
            </div>
            <div class="flex_column_center_center">
                <div class="desc_text">{{ memberInfo.memberNickName || memberInfo.memberName }}</div>
                <div class="desc_text_s">{{ superExpirationDay }}到期</div>
                <div class="desc_text_btn" @click="goBuy">立即续费</div>
            </div>
            <!-- <div class="tips">Hi，实力土豪，我在冬天你！ </div> -->
        </div>

        <div class="nav flex_row_center_center">
            <div class="nav_item flex_column_center_center" :class="{ active: navIndex == 0 }" @click="changeNav(0)">
                <div>累计省钱</div>
                <div>￥{{ totalSaveAmount }}</div>
            </div>
            <div class="nav_item flex_column_center_center" :class="{ active: navIndex == 1 }" @click="changeNav(1)">
                <div>本月省钱</div>
                <div>￥{{ currentMonthSaveAmount }}</div>
            </div>
        </div>
        <div class="main" :class="{ autoHeight: !onready }">
            <template v-if="navIndex == 0">
                <div class="list" v-for="(item, index) in totalSaveRecord.data" :key="index">
                    <div class="list_info flex_row_between_center">
                        <div class="list_info_time">{{ index }}</div>
                        <div class="list_info_money">共省<span>￥{{ item.monthTotalSaveAmount }}</span></div>
                    </div>
                    <div class="list_goods flex_row_start_center" v-for="(items, indexs) in item.monthSaveProductVO"
                        :key="indexs">
                        <div class="list_goods_img" :style="`background-image:url('` + items.productImage + `')`"></div>
                        <div class="list_goods_info">{{ items.goodsName }}</div>
                        <div class="list_goods_spec">{{ items.specValues }}</div>
                        <div class="list_goods_num">共{{ items.productNum }}件</div>
                        <div class="list_goods_money">已省￥{{ items.productSaveAmount }}
                        </div>
                    </div>
                </div>
                <SldCommonEmpty v-if="onready && !(Object.keys(totalSaveRecord.data).length)" totalWidth="1164"
                    totalHeight="350" />
            </template>

            <template v-else>
                <div class="list">
                    <div></div>
                    <div class="list_goods flex_row_start_center"
                        v-for="(items, indexs) in currentMonthSaveRecord.data.monthSaveProductVO" :key="indexs">
                        <div class="list_goods_img" :style="`background-image:url('` + items.productImage + `')`"></div>
                        <div class="list_goods_info">{{ items.goodsName }}</div>
                        <div class="list_goods_spec">{{ items.specValues }}</div>
                        <div class="list_goods_num">共{{ items.productNum }}件</div>
                        <div class="list_goods_money">已省￥{{ items.productSaveAmount }}
                        </div>
                    </div>
                </div>
                <SldCommonEmpty v-if="onready && !(Object.keys(currentMonthSaveRecord.data).length)" totalWidth="1164"
                    totalHeight="350" />
            </template>

        </div>
    </div>
</template>

<script>
import { getCurrentInstance, ref, reactive, onMounted, computed } from "vue";
import { useStore } from "vuex";
import SldCommonEmpty from '@/components/SldCommonEmpty';
import { useRouter } from "vue-router";
export default {
    name: "SuperHistory",
    components: {
        SldCommonEmpty
    },
    setup () {
        const { proxy } = getCurrentInstance();
        const L = proxy.$getCurLanguage();
        const store = useStore();
        const memberInfo = ref(store.state.memberInfo);
        const navIndex = ref(0);
        const totalSaveAmount = ref(0);
        const currentMonthSaveAmount = ref(0);
        const totalSaveRecord = reactive({ data: {} });
        const currentMonthSaveRecord = reactive({ data: {} });
        const onready = ref(false);
        const router = useRouter()

        const superExpirationDay = computed(() => {
            let { superExpirationTime: time } = memberInfo.value
            time = time.split(' ')[0]
            return time
        })

        //立即开通
        const goBuy = () => {
            router.push('/super/confirm');
        };

        onMounted(() => {
            getList();
        })

        //切换导航
        const changeNav = (index) => {
            if (navIndex.value != index) {
                navIndex.value = index;
                getList();
            }
        };

        const getList = () => {
            proxy.$post('v3/member/front/memberSuper/saveRecord', {
                type: navIndex.value == 0 ? 1 : 2 //类型，1-累计省钱；2-本月累计省钱
            }).then(res => {
                if (res.state == 200) {
                    totalSaveAmount.value = res.data.totalSaveAmount == 0 ?
                        res.data.totalSaveAmount : Number(res.data.totalSaveAmount).toFixed(2);
                    currentMonthSaveAmount.value = res.data.currentMonthSaveAmount == 0 ?
                        res.data.currentMonthSaveAmount : Number(res.data.currentMonthSaveAmount).toFixed(2);
                    totalSaveRecord.data = res.data.totalSaveRecord ? res.data.totalSaveRecord : {};
                    currentMonthSaveRecord.data = res.data.currentMonthSaveRecord ? res.data.currentMonthSaveRecord : {};
                }
                onready.value = true;
            })
        };

        return {
            L,
            memberInfo,
            navIndex,
            totalSaveAmount,
            currentMonthSaveAmount,
            totalSaveRecord,
            currentMonthSaveRecord,
            onready,
            changeNav,
            getList,
            superExpirationDay,
            goBuy
        }
    }
}
</script>

<style lang="scss" scoped>
.sld_super_history {
    width: 1200px;
    margin: 0 auto 50px;
    padding: 0;

    .super_header {
        padding: 60px 0;
    }

    .desc_text {
        margin-top: 12px;
        font-size: 30px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        font-size: 30px;
    }

    .desc_text_btn {
        width: 352px;
        height: 70px;
        background: linear-gradient(90deg, #F8D9AD, #D6A364);
        border-radius: 35px;
        font-size: 30px;
        font-family: PingFang SC;
        font-weight: 500;
        color: #8D5A0B;
        line-height: 70px;
        text-align: center;
        margin-top: 50px;
        cursor: pointer;
    }

    .desc_text_s {
        font-size: 24px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #9C7E57;
        margin-top: 12px;
    }

    .top_img {
        position: relative;
        width: 93px;
        height: 93px;

        .top_img_head {
            position: absolute;
            top: -44px;
            left: -50px;
            z-index: 1;
            width: 162px;
            height: 101px;
        }

        .top_img_logo {
            position: relative;
            z-index: 2;
            width: 93px;
            height: 93px;
            border-radius: 50%;
            overflow: hidden;
        }
    }

    .tips {
        color: #CFA768;
        font-size: 24px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        text-align: center;
        margin-bottom: 39px;
    }

    .nav {
        .nav_item {
            width: 600px;
            height: 96px;
            color: #E1B67D;
            background-color: #2E3244;

            &.active {
                color: #2E3244;
                background-color: #E1B67D;
            }

            &:nth-child(1) {
                border-top-left-radius: 10px;
            }

            &:nth-child(2) {
                border-top-right-radius: 10px;
            }

            div {
                font-size: 26px;
                font-family: Source Han Sans CN;
                font-weight: 500;

                &:nth-child(1) {
                    margin-bottom: 6px;
                }

                &:nth-child(2) {
                    font-size: 24px;
                }
            }
        }
    }

    .main {
        width: 1200px;
        background: #FFFFFF;
        box-shadow: 0 0 15px 0 rgba(97, 97, 97, 0.15);
        border-radius: 0 0 10px 10px;
        margin: 0 auto;
        padding: 0 18px 20px;
        overflow: hidden;

        &.autoHeight {
            min-height: 370px;
        }

        .list {
            margin-top: 20px;

            .list_info {
                margin-bottom: 20px;

                .list_info_time {
                    color: #555555;
                    font-size: 16px;
                    font-family: PingFang SC;
                    font-weight: 500;
                }

                .list_info_money {
                    color: #555555;
                    font-size: 14px;
                    font-family: Microsoft YaHei;
                    font-weight: 400;

                    span {
                        color: #caa466;
                    }
                }
            }

            .list_goods {
                width: 1164px;
                height: 122px;
                font-size: 14px;
                font-family: Microsoft YaHei;
                font-weight: 400;
                background: #FFFFFF;
                border: 1px solid #DCDCDC;
                border-top: none;
                padding: 20px;

                &:nth-child(2) {
                    border-top: 1px solid #DCDCDC;
                }

                .list_goods_img {
                    width: 78px;
                    height: 78px;
                    // border: 1px solid #DCDCDC;
                    border-radius: 4px;
                    background-position: center;
                    background-repeat: no-repeat;
                    background-size: contain;
                }

                .list_goods_info {
                    width: 325px;
                    height: 80px;
                    line-height: 24px;
                    color: #333333;
                    margin-left: 25px;
                    padding-top: 6px;
                }

                .list_goods_spec,
                .list_goods_num,
                .list_goods_money {
                    color: #AAAAAA;
                }

                .list_goods_spec {
                    width: 250px;
                    margin-left: 50px;
                }

                .list_goods_num {
                    width: 224px;
                    margin-left: 20px;
                }

                .list_goods_money {
                    width: 130px;
                    margin-left: 20px;
                }
            }
        }
    }
}
</style>