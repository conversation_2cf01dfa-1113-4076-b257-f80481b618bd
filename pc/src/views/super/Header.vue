<template>
    <div style="background: #fff;width: 100%;">
        <NavTopBar/>
        <SuperTop></SuperTop>
        <div class="sld_member_main_content">
            <router-view></router-view>
        </div>
        <FooterService/>
        <FooterLink/>
    </div>
</template>

<script>
    import NavTopBar from "../../components/NavTopBar";
    import SuperTop from "../../components/superTop";
    import FooterService from "../../components/FooterService";
    import FooterLink from "../../components/FooterLink";

    export default {
        name: 'SuperCommon',
        components: {
            NavTopBar,
            SuperTop,
            FooterService,
            FooterLink,
        },
        setup() {
            const isRouterAlive = true
            return {isRouterAlive}
        }
    }
</script>

<style lang="scss" scoped>
    .sld_member_main_content {
        width: 100%;
        // background-color: #F7F7F7;
        border-top: 1px solid #EBEBEB;
    }
</style>
