<template>
    <div class="sld_super_pay">
        <div class="top flex_column_center_center" :style="`background-image:url('` + topBg + `')`">
            <div class="title">开通{{ memberConfig.super_custom_name || '超级会员' }}</div>
            <div class="desc">领专属会员大礼包，一键回本，购物畅享，运费无忧</div>
        </div>
        <div class="main">
            <div class="title">会员类型选择</div>
            <div class="methods flex_row_start_center">
                <div v-for="(item, index) in methodsList.data" :key="index" @click="changeype(index)"
                    class="methods_item flex_column_start_center" :class="{ active: typeIndex == index }">
                    <img v-if="index == 2" class="methods_item_introduce" src="@/assets/super/pay_introduce.png" />
                    <img class="methods_item_img" src="@/assets/super/pay_type_logo.png" />
                    <div class="methods_item_name">{{ item.payTypeValue }}</div>
                    <div class="methods_item_price">
                        <span>￥</span>
                        <span>{{ item.needPay }}</span>
                        <span v-if="item.unit">/{{ item.unit }}</span>
                    </div>
                    <div class="methods_item_origin" v-if="item.saveAmount">
                        ￥{{ (Number(item.needPay) + Number(item.saveAmount)).toFixed(2) }}
                    </div>
                    <img v-if="typeIndex == index" class="methods_item_check" src="@/assets/super/pay_checked.png" />
                </div>
            </div>
            <div class="title">包含权益</div>
            <div class="role flex_row_center_center">
                <div class="role_item flex_column_center_center"
                    v-if="memberConfig.super_discount_enabled == 1 && memberConfig.super_discount > 0">
                    <img src="@/assets/super/pay_role_1.png" />
                    <span>购物打{{ memberConfig.super_discount }}折</span>
                </div>
                <div class="role_item flex_column_center_center"
                    v-if="memberConfig.super_integral_multiple_enabled == 1 && memberConfig.super_integral_multiple > 0">
                    <img src="@/assets/super/pay_role_2.png" />
                    <span>{{ memberConfig.super_integral_multiple }}倍领积分</span>
                </div>
                <div class="role_item flex_column_center_center" v-if="memberConfig.super_coupon_enabled == 1">
                    <img src="@/assets/super/pay_role_3.png" />
                    <span>专属优惠券</span>
                </div>
                <div class="role_item flex_column_center_center" v-if="memberConfig.super_birthday_coupon_enabled == 1">
                    <img src="@/assets/super/pay_role_4.png" />
                    <span>生日大礼包</span>
                </div>
                <div class="role_item flex_column_center_center" v-if="memberConfig.super_freight_coupon_enabled == 1">
                    <img src="@/assets/super/pay_role_5.png" />
                    <span>运费优惠券</span>
                </div>
            </div>
            <div class="btn" @click="goBuy">
                <div class="btn_content">立即支付</div>
                <template v-if="payInfo.data.payType">
                    <span>￥</span>
                    <span>{{ payInfo.data.needPay }}</span>
                    <span v-if="payInfo.data.unit">/{{ payInfo.data.unit }}</span>
                </template>
                <div v-if="payInfo.data.payType && payInfo.data.saveAmount" class="coupon"
                    :style="`background-image:url('` + couponBg + `')`">
                    已优惠￥{{ Number(payInfo.data.saveAmount).toFixed(2) }}
                </div>
            </div>
            <div class="policy">开通即同意<span @click="gotoPolicy">《付费会员用户协议》</span></div>
        </div>


        <el-dialog lock-scroll="false" top="15vh" custom-class="already_pay" v-model="showModel" width="400px">
            <div class="pay_model flex_column_center_center">
                <div class="pay_model_info flex_column_center_center">
                    <div class="pay_model_title">你有待支付的订单</div>
                    <div class="pay_model_tips">若支付新订单，需取消原订单</div>
                    <div class="pay_model_detail flex_row_between_center">
                        <div class="pay_model_detail_tips">原订单</div>
                        <div class="pay_model_detail_title">{{ waitTiPay.payTypeValue }}</div>
                        <div class="pay_model_detail_price">￥{{ waitTiPay.needPay }}</div>
                    </div>
                    <div class="pay_model_btn flex_row_around_center">
                        <div class="pay_model_cancle" @click="closeModel">取消原订单</div>
                        <div class="pay_model_pay" @click="submitModel">支付原订单</div>
                    </div>
                </div>
            </div>
        </el-dialog>


    </div>
</template>

<script>
import { getCurrentInstance, ref, reactive, onMounted } from "vue";
import { useRouter } from 'vue-router';
import { useStore } from "vuex";
import { ElMessage } from 'element-plus';
export default {
    name: "SuperConfirm",
    components: {},
    setup () {
        const { proxy } = getCurrentInstance();
        const L = proxy.$getCurLanguage();
        const router = useRouter();
        const store = useStore();
        const memberConfig = ref(store.state.memberConfig)
        const showModel = ref(false)
        const topBg = require('@/assets/super/pay_bg.png');
        const typeIndex = ref(2);
        const couponBg = require('@/assets/super/pay_coupon.png');
        const payInfo = reactive({ data: {} });
        const methodsList = reactive({ data: {} });
        const waitTiPay = ref({})

        onMounted(() => {
            getMethods();
        });

        //获取会员类型
        const getMethods = () => {
            proxy.$get('v3/member/front/memberSuper/pay/superPayInfo').then(res => {
                if (res.state == 200) {
                    methodsList.data = res.data;
                    payInfo.data = methodsList.data[2];
                }
            })
        };
        //切换会员类型
        const changeype = (index) => {
            if (typeIndex.value != index) {
                typeIndex.value = index;
                payInfo.data = methodsList.data[index];
            } else {
                typeIndex.value = -1;
                payInfo.data = {};
            }
        };
        //立即支付
        const goBuy = () => {
            if (payInfo.data.payType) {

                proxy.$post('v3/member/front/memberSuper/pay/buySuper', {
                    payType: payInfo.data.payType
                }).then(res => {
                    if (res.state == 200) {
                        router.push({
                            path: '/super/pay',
                            query: {
                                payType: payInfo.data.payType
                            }
                        })
                    } else if (res.state == 267) {
                        showModel.value = true
                        waitTiPay.value.payTypeValue = res.data.payTypeValue
                        waitTiPay.value.payType = res.data.payType
                        waitTiPay.value.paySn = res.data.paySn
                        waitTiPay.value.needPay = res.data.needPay
                    }
                })
            } else {
                ElMessage.warning('请选择会员类型')
            }
        };
        //查看协议
        const gotoPolicy = () => {
            router.push('/super/agreement');
        };

        // 关闭支付订单弹窗
        const closeModel = () => {
            showModel.value = false;
            proxy.$post('v3/member/front/memberSuper/pay/cancelPay', {
                paySn: waitTiPay.value.paySn
            }).then(res => {
                ElMessage[res.state == 200 ? 'success' : 'warning'](res.msg)
            })
        }

        // 支付
        const submitModel = () => {
            router.push({
                path: '/super/pay',
                query: {
                    payType: waitTiPay.value.payType
                }
            })
        }


        return {
            submitModel,
            closeModel,
            L,
            topBg,
            typeIndex,
            couponBg,
            payInfo,
            methodsList,
            getMethods,
            changeype,
            gotoPolicy,
            goBuy,
            showModel,
            waitTiPay,
            memberConfig
        }
    }
}
</script>

<style lang="scss" scoped>
.sld_super_pay {
    width: 1200px;
    margin: 0 auto;
    padding-top: 15px;
    padding-bottom: 30px;

    .top {
        width: 1200px;
        height: 136px;
        background-repeat: no-repeat;
        background-size: cover;
        background-position: center;

        .title,
        .desc {
            color: #E9CAA2;
            font-family: PingFang SC;
            font-weight: 500;
        }

        .title {
            font-size: 30px;
        }

        .desc {
            font-size: 16px;
            margin-top: 17px;
        }
    }

    .main {
        width: 1200px;
        height: 834px;
        background: #FFFFFF;
        box-shadow: 0 0 18px 0 rgba(97, 97, 97, 0.2);
        border-radius: 0 0 4px 4px;
        padding: 29px 100px 99px;

        .title {
            color: #1D1E2C;
            font-size: 24px;
            font-family: Microsoft YaHei;
            font-weight: bold;
            margin-bottom: 31px;
        }

        .methods {
            .methods_item {
                position: relative;
                width: 250px;
                height: 246px;
                background: #FFFFFF;
                border: 1px solid #DDDDDD;
                border-radius: 4px;
                margin: 0 45px 31px;
                cursor: pointer;

                &.active,
                &:hover {
                    border: 2px solid #D3A672;
                    box-shadow: 0 0 16px 0 rgba(206, 137, 37, 0.32);
                }

                .methods_item_introduce {
                    position: absolute;
                    top: -1px;
                    left: -1px;
                    z-index: 1;
                    width: 61px;
                    height: 60px;
                }

                .methods_item_img {
                    width: 55px;
                    height: 41px;
                    margin-top: 24px;
                }

                .methods_item_name {
                    color: #555555;
                    font-size: 20px;
                    font-family: Microsoft YaHei;
                    font-weight: 400;
                    margin-top: 12px;
                }

                .methods_item_price {
                    margin-top: 30px;

                    span {
                        font-size: 18px;
                        font-family: Microsoft YaHei;
                        font-weight: 400;

                        &:nth-child(1) {
                            color: #D3A672;
                        }

                        &:nth-child(2) {
                            color: #D3A672;
                            font-size: 48px;
                            font-family: PingFang SC;
                            font-weight: bold;
                        }

                        &:nth-child(3) {
                            color: #CF0E0E;
                        }
                    }
                }

                .methods_item_origin {
                    color: #999999;
                    font-size: 16px;
                    font-family: Microsoft YaHei;
                    font-weight: 400;
                    text-decoration: line-through;
                    margin-top: 2px;
                }

                .methods_item_check {
                    position: absolute;
                    right: -2px;
                    bottom: 0;
                    z-index: 1;
                    width: 41px;
                    height: 39px;
                }
            }
        }

        .role {
            .role_item {
                margin-left: 30px;
                margin-right: 30px;

                img {
                    width: 70px;
                    height: 70px;
                }

                span {
                    color: #666666;
                    font-size: 16px;
                    font-family: Microsoft YaHei;
                    font-weight: 400;
                    margin-top: 22px;
                    white-space: nowrap;
                }
            }
        }
    }

    .btn {
        position: relative;
        width: 410px;
        height: 75px;
        color: #191738;
        font-size: 24px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        text-align: center;
        background: linear-gradient(90deg, #F4D2A2, #DCAB6E);
        border-radius: 38px;
        margin: 80px auto 0;
        padding-top: 18px;
        cursor: pointer;
        display: flex;
        align-items: baseline;
        justify-content: center;

        .btn_content {
            line-height: 36px;
        }

        span {
            &:nth-child(2) {
                font-size: 18px;
                margin-left: 5px;
            }

            &:nth-child(3) {
                font-size: 30px;
                font-weight: bold;
            }

            &:nth-child(4) {
                font-size: 24px;
                margin-left: 2px;
            }
        }

        .coupon {
            position: absolute;
            top: -34px;
            right: -40px;
            z-index: 9;
            min-width: 139px;
            min-height: 31px;
            line-height: 26px;
            color: #FFFFFF;
            font-size: 16px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            padding-left: 4px;
            padding-right: 4px;
            background-position: center;
            background-repeat: no-repeat;
            background-size: cover;
            cursor: default;
        }
    }

    .policy {
        color: #999999;
        font-size: 16px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        text-align: center;
        margin-top: 27px;

        span {
            color: #d6a859;
            cursor: pointer;
        }
    }
}


.already_pay {
    width: 340px;
}

.pay_model {
    .pay_model_info {
        width: 330px;
        font-family: PingFang SC;
        font-weight: 500;
        background: #FFFFFF;
        border-radius: 20px;

        .pay_model_title {
            color: #0F0F18;
            font-size: 17px;
            font-weight: bold;
            margin-top: 23px;
        }

        .pay_model_tips {
            color: #666666;
            font-size: 14px;
            margin-top: 12px;
            margin-bottom: 24px;
        }

        .pay_model_detail {
            position: relative;
            width: 290px;
            height: 56px;
            font-family: PingFang SC;
            font-weight: 500;
            background: #FCF2D9;
            border-radius: 10px;
            overflow: hidden;

            .pay_model_detail_tips {
                position: absolute;
                top: -16px;
                left: -33px;
                width: 85px;
                height: 50px;
                line-height: 74px;
                color: #FFFFFF;
                text-align: center;
                background-color: #DEB553;
                transform: rotate(-45deg) scale(.68);
            }

            .pay_model_detail_title {
                color: #0F0F18;
                font-size: 14px;
                margin-left: 45px;
            }

            .pay_model_detail_price {
                color: #B58848;
                font-size: 14px;
                margin-right: 15px;
            }
        }

        .pay_model_btn {
            margin-top: 27px;
            margin-bottom: 28px;

            .pay_model_cancle,
            .pay_model_pay {
                width: 110px;
                height: 32px;
                line-height: 32px;
                color: #252939;
                font-size: 13px;
                font-family: PingFang SC;
                font-weight: 500;
                text-align: center;
                background: linear-gradient(90deg, #F8D9AD, #E9B87A);
                border-radius: 16px;
                cursor: pointer;
            }

            .pay_model_cancle {
                margin-right: 25px;
                cursor: pointer;
            }

            .pay_model_pay {}
        }
    }
}
</style>