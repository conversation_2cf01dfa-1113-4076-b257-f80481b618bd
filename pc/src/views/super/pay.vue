<template>
    <div id="pay" class="super_pay">
        <div class="top_info">
            <div class="top_info_header">
                <div class="top_logo">
                    <div :style="`background-image:url(${configInfo.main_site_logo})`" :onerror='defaultImg'></div>
                </div>
                <div class="top_info_progress flex_row_end_center">
                    <div class="progress_item">
                        <div class="progress flex_row_center_center">
                            <span class="active">1</span>
                            <div class="progress_line">
                                <div class="content active"></div>
                            </div>
                        </div>
                        <p style="margin-left: -11px">确认订单</p>
                    </div>
                    <div class="progress_item">
                        <div class="progress flex_row_center_center">
                            <span class="active">2</span>
                            <div class="progress_line">
                                <div :class="{ content: true, active: payComplete }"></div>
                            </div>
                        </div>
                        <p>付款</p>
                    </div>
                    <div class="progress_item" style="margin-left: -24px">
                        <div class="progress flex_row_end_center">
                            <span :class="{ active: payComplete }">3</span>
                        </div>
                        <p>支付成功</p>
                    </div>
                </div>
            </div>
            <div class="top_info_text flex_row_between_between">
                <span>订单提交成功，请您尽快付款！ 订单号： {{ pay_info.data.paySn }}</span>
                <div class="price">
                    应付金额
                    <span>{{ pay_info.data.needPay }}</span> 元
                </div>
            </div>

            <div class="receive_info">

            </div>
        </div>
        <div class="bg">
            <div class="pay_method" v-if="hasPayMethod">
                <div v-if="!show_pay">
                    <div class="balance" v-if="balance_list.data.length > 0">
                        <img class="logo" :src="balance" alt />
                        <div class="balance_info">
                            <i :class="{
                        iconfont: true, 'iconyuanquan1': pay_info.data.balanceAvailable >= pay_info.data.needPay, 'iconduihao1':
                            current_pay_method.data.payMethod ==
                            balance_list.data[0].payMethod, 'iconjinzhi1':
                            pay_info.data.balanceAvailable < pay_info.data.needPay
                    }" @click="pay_info.data.balanceAvailable >= pay_info.data.needPay ? changePayMethod(balance_list.data[0]) : ''"></i>
                            <span class="weight big_size">{{ L['使用余额支付'] }}</span>
                            <span class="avai">
                                <span>（{{ L['可用余额'] }}：</span>
                                <span class="weight">{{ pay_info.data.balanceAvailable }}</span>
                                <span>{{ L['元'] }}，{{ L['目前需要在线支付'] }}：</span>
                                <span class="weight">{{ pay_info.data.needPay }}{{ L['元'] }}</span>
                                <span>）</span>
                                <template v-if="rechargeEnble">
                                    <span>{{ L['余额不足？'] }}</span>
                                    <span class="weight pointer" @click="goRecharge" style="color:#168ED8">{{
                        L['马上充值'] }}</span>
                                </template>
                            </span>
                        </div>


                        <div class="password"
                            v-if="current_pay_method.data.payMethod == balance_list.data[0].payMethod">
                            <el-input class="password_input" :placeholder="L['支付密码']" v-model="password"
                                :disabled="!memberInfo.hasPayPassword" show-password>
                            </el-input>
                            <p v-if="!memberInfo.hasPayPassword">
                                <span style="color: #FFB33A;">
                                    <i class="el-icon-warning"></i>
                                    {{ L['未设置支付密码，马上去设置'] }}
                                </span>
                                <router-link class="set_password pointer" :to="'/member/pwd/pay'">
                                    {{ L['设置密码'] }}
                                </router-link>
                            </p>
                            <p v-else>
                                <span class="forget_password pointer" @click="forgetPassword">{{ L['忘记密码?']
                                    }}</span>
                            </p>
                        </div>

                    </div>
                    <div class="balance other_pay_method" v-if="other_pay_list.data.length > 0">
                        <img class="logo" :src="otherPay" alt />
                        <div class="balance_info flex">
                            <div :class="{ other_pay_item: true, flex_row_around_center: true, wechat: index != 0 }"
                                v-for="(item, index) in other_pay_list.data" :key="index"
                                @click="changePayMethod(item)">
                                <i
                                    :class="{ iconfont: true, 'iconyuanquan1': current_pay_method.data.payMethod != item.payMethod, 'iconduihao1': current_pay_method.data.payMethod == item.payMethod }"></i>
                                <img :src="payIcon[item.payMethod]" alt />
                                <span>{{ item.payMethodName }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="pay" @click="submitOrder" style="cursor: pointer;">
                        {{ L['立即支付'] }}
                    </div>
                </div>
                <div class="wx_pay_con" v-if="show_pay">
                    <p class="title" @click="hideWxPay">
                    <div style="cursor: pointer;display:inline">
                        <i class="iconfont iconziyuan2"></i>
                        <span>{{ L['选择其它支付方式'] }}</span>
                    </div>
                    </p>
                    <div class="qr flex_row_around_center">
                        <div class="left flex_column_start_center">

                            <!-- dev_tld-start -->
                            <img :src="wxPayQrImg" alt="" v-if="current_pay_method.data.payMethod == 'wx'" />
                            <!-- dev_tld-end -->

                            

                            <p>
                                {{ L['使用'] }}
                                <span v-if="show_pay == 'wx'">{{ L['微信支付'] }}</span>
                                <span v-if="show_pay == 'ali'">{{ L['支付宝支付'] }}</span>
                                APP
                            </p>
                            <p>{{ L['扫码完成支付'] }}</p>
                            <div class="refresh" @click="refreshWxQrcode">
                                {{ L['刷新'] }}
                            </div>
                        </div>
                        <img class="wx_png" :src="wx_pay_tip" v-if="show_pay == 'wx'" />
                    </div>
                </div>

            </div>
            <div class="no_payMethod" v-else>
                <img src="@/assets/member/member_empty.png" alt="">
                <p>暂无可用的支付方式，平台正在紧急处理中～</p>
            </div>
        </div>
    </div>
</template>

<script>
import { reactive, getCurrentInstance, ref, onMounted, onUnmounted } from "vue";
import { ElMessage, ElInput } from "element-plus";
import { useRoute, useRouter } from "vue-router";
import { useStore } from "vuex";
import { preventMutiClick } from '@/utils/common'
import { qrcanvas } from 'qrcanvas'
export default {
    name: "superPay",
    components: {
        ElInput
    },
    setup () {
        const balance = require("../../assets/buy/balance.png");
        const otherPay = require("../../assets/buy/otherPay.png");
        const ali = require("../../assets/buy/ali.png");
        const wechat = require("../../assets/buy/wechat.png");
        const wx_pay_tip = require("../../assets/buy/wx_pay_tip.png");
        const banks = require('@/assets/allinpay/bank_card.png')
        const show_pay = ref('')
        let continueExecFunc = null
        const route = useRoute();
        const router = useRouter();
        const store = useStore();
        const { proxy } = getCurrentInstance();
        const L = proxy.$getCurLanguage()
        const pay_method = reactive({ data: [] });
        const pay_info = reactive({ data: {} });
        const paySn = ref("");
        const current_pay_method = reactive({ data: {} });
        const balance_list = reactive({ data: [] });
        const other_pay_list = reactive({ data: [] });
        const buy_name_list = ref("");
        const password = ref("");
        const show_wx_pay = ref(false);
        const wxPayQrImg = ref('');//微信支付二维码
        const timer = ref(0);//定时器
        const show_detail = ref(true);
        const hasPayMethod = ref(true);
        const configInfo = ref(store.state.configInfo);
        const memberInfo = ref(store.state.memberInfo);
        const defaultImg = ref('this.src="' + require('../../assets/common_top_logo.png') + '"');
        const payComplete = ref(false);

        const payIcon = {
            tl_alipay: ali,
            tl_wx: wechat,
            alipay: ali,
            wx: wechat,
            tl_bank: banks
        }

        onMounted(() => {
            getPayMethod();
            getPayInfo(route.query.payType);
        });
        onUnmounted(() => {
            if (timer.value) {
                clearInterval(timer.value);
                timer.value = 0;
            }
        });

        //获取支付方式
        const getPayMethod = () => {
            proxy
                .$get("v3/system/common/payMethod", {
                    source: "pc",
                    type: 1
                })
                .then(res => {
                    if (res.state == 200) {
                        hasPayMethod.value = res.data.length > 0 ? true : false
                        res.data.map(item => {
                            if (item.payMethod == "balance") {
                                balance_list.data.push(item);
                            } else {
                                other_pay_list.data.push(item);
                            }
                        });
                    } else {
                        ElMessage(res.msg);
                    }
                })
        };
        //获取订单支付数据
        const getPayInfo = (payType) => {
            proxy
                .$post("v3/member/front/memberSuper/pay/buySuper", {
                    payType
                })
                .then(res => {
                    if (res.state == 200 || res.state == 267) {
                        pay_info.data = res.data;
                        paySn.value = res.data.paySn;
                    } else {
                        ElMessage(res.msg);
                    }
                })
                .catch(() => {
                    //异常处理
                });
        };
        //切换支付方式
        const changePayMethod = pay_method => {
            current_pay_method.data = pay_method;
        };
        //去支付
        const pay = (continueExec) => {
            continueExecFunc = continueExec
            if (!current_pay_method.data.payMethod) {
                ElMessage.warning("请选择支付方式");
                continueExec()
                return;
            }
            let param = {};
            param.payType = current_pay_method.data.payType;
            param.payMethod = current_pay_method.data.payMethod;
            param.paySn = pay_info.data.paySn;

            //余额支付
            if (param.payMethod == 'balance') {
                if (password.value == "") {
                    ElMessage.warning("请输入支付密码");
                    continueExec()
                    return;
                }
                param.payPwd = proxy.$base64Encrypt(password.value); //支付密码,使用余额时必传
            }

            //请求支付接口
            proxy.$post("v3/member/front/memberSuper/pay/doPay", param).then(res => {
                if (res.state == 200) {

                    // dev_tld-start
                    if (param.payMethod == 'balance') {
                        ElMessage.success('支付成功,2s后自动跳转会员中心');
                        payComplete.value = true
                        //清除定时器
                        setTimeout(() => {
                            router.replace({
                                path: '/member/index'
                            })
                        }, 2000)
                    } else if (param.payMethod == 'alipay') {
                        document.write(res.data.payData) //自动提交表单数据
                    } else if (param.payMethod == 'wx') {
                        show_pay.value = 'wx'
                        wxPayQrImg.value = 'data:image/png;base64,' + res.data.payData //微信支付二维码
                        // 定时查询是否支付成功
                        timer.value = setInterval(() => {
                            queryPayState()
                        }, 3000)
                        //两小时自动跳转订单列表
                        setTimeout(() => {
                            router.replace({
                                path: '/member/index'
                            })
                        }, 432000000)
                    }
                    // dev_tld-end
                    } else {
                    ElMessage(res.msg);
                }
            })
        };

        const submitOrder = preventMutiClick(pay)


        // 定时查询是否支付成功
        const queryPayState = (continueExec) => {
            proxy.$get('v3/member/front/memberSuper/pay/superPaySuccess', {
                paySn: paySn.value
            }).then(res => {
                if (res.state == 200) {
                    if (res.data.apiPayState == 1) {
                        ElMessage.success(res.msg + ',2s后自动跳转会员中心')
                        //清除定时器
                        if (timer.value) {
                            clearInterval(timer.value)
                            timer.value = 0
                        }
                        setTimeout(() => {
                            router.replace({
                                path: '/super/index'
                            })
                        }, 2000)
                    }
                } else {
                    continueExec()
                    ElMessage.warning(res.msg)
                }
            }).catch(continueExec)

        }
        //隐藏微信支付内容
        const hideWxPay = () => {
            show_pay.value = '';
            clearInterval(timer.value)
            timer.value = null
            continueExecFunc()
        };
        //刷新微信支付二维码
        const refreshWxQrcode = () => {
            pay();
        };
        //收起展开详情
        const showDetail = () => {
            show_detail.value = !show_detail.value
        };
        //充值余额
        const goRecharge = () => {
            router.push('/member/recharge')
        };
        //忘记支付密码
        const forgetPassword = () => {
            router.push('/member/pwd/reset')
        };
        return {
            L,
            pay_method,
            balance,
            otherPay,
            ali,
            wechat,
            pay_info,
            current_pay_method,
            balance_list,
            other_pay_list,
            changePayMethod,
            buy_name_list,
            password,
            pay,
            wx_pay_tip,
            show_wx_pay,
            hideWxPay,
            refreshWxQrcode,
            wxPayQrImg,
            goRecharge,
            forgetPassword,
            showDetail,
            show_detail,
            hasPayMethod,
            configInfo,
            defaultImg,
            memberInfo,
            payComplete,
            submitOrder,
            payIcon,
            show_pay

        };
    }
};
</script>

<style lang="scss">
@import "../../style/pay.scss";
@import "../../style/base.scss";

.super_pay {
    .no_payMethod {
        margin: 0 auto;
        width: 1200px;
        height: 560px;
        background-color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;

        img {
            width: 163px;
            height: 114px;
        }

        p {
            margin-top: 39px;
            font-size: 14px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #333333;
        }
    }
}

#pay {
    &.super_pay {
        .top_info {
            .receive_info {
                margin-top: 20px;
            }
        }

        .bg {
            margin-top: 5px;
        }
    }
}
</style>