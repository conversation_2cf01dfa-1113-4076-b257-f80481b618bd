<template>
    <div class="sld_login">
        <div class="agreement_container">
            <h2 class="agreement_title">{{ agreeContent.title }}</h2>
            <pre class="agreement_content" v-html="agreeContent.content"></pre>
        </div>
    </div>
</template>

<script>
import { useRoute, useRouter } from 'vue-router'
import { ref, getCurrentInstance, reactive, onMounted } from 'vue';
import { useStore } from "vuex";

export default {
    name: "agreement",
    setup() {
        const store = useStore();
        const router = useRouter()
        const route = useRoute()
        const { proxy } = getCurrentInstance();
        const L = proxy.$getCurLanguage();
        const configInfo = ref(store.state.configInfo);
        const defaultImg = require('@/assets/common_top_logo.png');
        const agreeContent = reactive({
            content: '',
            title: ''
        })

        onMounted(() => {
            getInitData()
        });

        const getInitData = () => {
            proxy.$get('v3/system/front/agreement/detail', {
                agreementCode: 'supper_member_agreement'
            }).then(res => {
                if (res.state == 200) {
                    agreeContent.content = proxy.$quillEscapeToHtml(res.data.content)
                    agreeContent.title = res.data.title
                }
            })
        };

        return { L, agreeContent, configInfo, defaultImg }
    },
};
</script>
<style lang="scss" scoped>
@import '@/style/agreement.scss';

.sld_login {
    .agreement_container {
        width: 1200px;
        margin-top: 0;
    }

    .agreement_content {
        font-size: 15px;
        line-height: 35px;
        white-space: normal;
        word-break: break-all;
    }
}
</style>
<style lang="scss">
.agreement_content {
    img {
        max-width: 100%;
    }
}
</style>
