<template>
    <div class="sld_super_index" :style="`background-image:url('` + mainBg + `')`" v-if="memberConfig.super_is_enable == '1'&&memberFlag">
        <div class="top_box flex_column_center_center"
            :style="`background-image:url(${memberConfig.super_pc_page ? memberConfig.super_pc_page : topBg})`">
            <div v-if="memberInfo.isSuper" class="top_box_main flex_column_center_center">
                <div class="top_box_main_imgs">
                    <img class="top_box_main_head" src="@/assets/super/head_logo.png" />
                    <img class="top_box_main_logo" :src="memberInfo.memberAvatar" />
                </div>
                <div class="top_box_main_name">{{ memberInfo.memberNickName || memberInfo.memberName }}</div>
                <div class="top_box_main_time">{{ memberInfo.superExpirationDay }}</div>
                <div class="top_box_main_next" @click="goBuy">立即续费</div>
                <div class="top_box_main_total" @click="gotoHistory">累计已省<span>{{
                    memberInfo.totalSaveAmount }}</span><span>元></span></div>
            </div>
            <div v-else class="top_box_main">
                <div class="top_box_main_title">开通{{ memberConfig.super_custom_name ? memberConfig.super_custom_name :
                    '超级会员' }}
                </div>
                <div class="top_box_main_desc">专属特权</div>
                <div class="top_box_main_btn" @click="goBuy">立即开通</div>
            </div>
        </div>
        <div class="main_box flex_column_start_center">
            <img class="main_box_line" src="@/assets/super/top_line.png" />
            <div class="main_box_title">开通即刻享受{{ numberMap[rightList.length] }}大权益</div>
            <div class="main_box_list flex_row_center_center">
                <div class="main_box_item flex_column_center_center"
                    v-if="memberConfig.super_discount_enabled == 1 && memberConfig.super_discount > 0">
                    <img src="@/assets/super/role_1.png" />
                    <span>购物打{{ memberConfig.super_discount }}折</span>
                </div>
                <div class="main_box_item flex_column_center_center"
                    v-if="memberConfig.super_integral_multiple_enabled == 1 && memberConfig.super_integral_multiple > 0">
                    <img src="@/assets/super/role_2.png" />
                    <span>{{ memberConfig.super_integral_multiple }}倍领积分</span>
                </div>
                <div class="main_box_item flex_column_center_center" v-if="memberConfig.super_coupon_enabled == 1">
                    <img src="@/assets/super/role_3.png" />
                    <span>专属优惠券</span>
                </div>
                <div class="main_box_item flex_column_center_center" v-if="memberConfig.super_birthday_coupon_enabled == 1">
                    <img src="@/assets/super/role_4.png" />
                    <span>生日大礼包</span>
                </div>
                <div class="main_box_item flex_column_center_center" v-if="memberConfig.super_freight_coupon_enabled == 1">
                    <img src="@/assets/super/role_5.png" />
                    <span>运费优惠券</span>
                </div>
            </div>
        </div>
        <section class="block_class">
            <div v-for="(item, index) in rightList" :key="index">
                <div class="list_img" :style="{ backgroundImage: `url(${block_img.block_1})` }"
                    v-if="item.type == 'super_discount'">
                    <div class="block_class_1 flex_column_center_center">
                        <h2 class="sort">{{ index + 1 }}</h2>
                        <div class="white_text mb10">自营商品购物全场{{ memberConfig.super_discount }}折，同享其他优惠</div>
                        <div class="flex_row_center_center">
                            <h2 class="sort_big font65">{{ memberConfig.super_discount }}</h2>
                            <span class="sort_small">折</span>
                        </div>
                    </div>
                </div>
                <div class="list_img" :style="{ backgroundImage: `url(${block_img.block_2})` }"
                    v-if="item.type == 'super_integral_multiple'">
                    <div class="block_class_2 flex_column_center_center">
                        <h2 class="sort">{{ index + 1 }}</h2>
                        <div class="flex_row_center_center">
                            <h2 class="sort_big font50" style="letter-spacing:10px">{{ memberConfig.super_integral_multiple
                            }}倍</h2>
                            <div class="white_text" style="font-size: 30px;">积分</div>
                        </div>
                        <div class="white_text block_class_2_text3">购物下单、订单评价赠送积分翻倍
                            积分可抵现</div>
                    </div>

                    <div class="block_class_3 flex_row_between_center">
                        <div class="block_class_3_reg1">
                            <div class="reg1_text1">普通会员购物</div>
                            <div>
                                <span class="reg1_text1">积分为</span>
                                <span class="reg1_text2 sort_big">1倍</span>
                            </div>
                        </div>

                        <div class="block_class_3_reg1">
                            <div class="reg1_text1 dark">开通{{ memberConfig.super_custom_name }}</div>
                            <div>
                                <span class="reg1_text1 dark">积分为</span>
                                <span class="reg1_text2 sort_big dark">{{ memberConfig.super_integral_multiple }}倍</span>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="list_img" :style="{ backgroundImage: `url(${block_img.block_3})` }"
                    v-if="item.type == 'super_coupon'">
                    <div class="block_class_5 flex_row_start_center">
                        <div class="block_class_5_section1 flex_row_center_center">
                            <span class="price_small sort_big">￥</span>
                            <span class="price_big sort_big">{{ item.value }}</span>
                        </div>
                        <div class="block_class_5_section1 flex_column_center_center">
                            <div class="flex_row_center_center block_class_5_text">
                                <span class="dot"></span>
                                &nbsp;&nbsp;
                                <span>{{ memberConfig.super_custom_name }}</span>
                                &nbsp;&nbsp;
                                <span class="dot"></span>
                            </div>
                            <br />
                            <div class="block_class_5_text">专属礼券</div>
                        </div>
                    </div>
                    <div class="block_class_4 block_class_1 flex_column_center_center">
                        <h2 class="sort">{{ index + 1 }}</h2>
                        <div class="white_text mb10">
                            赠送{{ memberConfig.super_custom_name ? memberConfig.super_custom_name : '超级会员' }}专属</div>
                        <div class="flex_row_center_center">
                            <span class="sort_small">优惠券礼包</span>
                        </div>
                    </div>
                </div>
                <div class="list_img" :style="{ backgroundImage: `url(${block_img.block_4})` }"
                    v-if="item.type == 'super_birthday_coupon'">
                    <div class="block_class_6 block_class_2 flex_column_center_center">
                        <h2 class="sort">4</h2>
                        <div class="sort_big font39">生日大礼包</div>
                        <div class="white_text block_class_2_text4" style="margin-top: 27px;">
                            {{ memberConfig.super_custom_name ? memberConfig.super_custom_name : '超级会员' }}生日专享大礼包
                        </div>
                    </div>
                </div>
                <div class="list_img" :style="{ backgroundImage: `url(${block_img.block_3})` }"
                    v-if="item.type == 'super_freight_coupon'">
                    <div class="block_class_5 flex_row_start_center">
                        <div class="block_class_5_section1 flex_row_center_center">
                            <span class="price_small sort_big" v-if="item.value > 0">￥</span>
                            <span class="price_big sort_big" v-if="item.value > 0">{{ item.value }}</span>
                            <span class="price_small sort_big" v-else>免运费</span>
                        </div>
                        <div class="block_class_5_section1 flex_column_center_center">
                            <div class="flex_row_center_center block_class_5_text">
                                <span class="dot"></span>
                                &nbsp;&nbsp;
                                <span>{{ memberConfig.super_custom_name }}</span>
                                &nbsp;&nbsp;
                                <span class="dot"></span>
                            </div>
                            <br />
                            <div class="block_class_5_text">专属礼券</div>
                        </div>
                    </div>
                    <div class="block_class_4 block_class_1 flex_column_center_center">
                        <h2 class="sort">{{ index + 1 }}</h2>
                        <div class="white_text mb10">赠运费券，购物无忧 </div>
                        <div class="flex_row_center_center">
                            <span class="sort_small">运费优惠券</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
    <div v-if="memberConfig.super_is_enable != '1'&&memberFlag">
        <SldCommonEmpty tip="超级会员功能已关闭" paddingTop="300" totalHeight="700"></SldCommonEmpty>
    </div>
</template>

<script>
import { getCurrentInstance, ref, onMounted } from "vue";
import { useRouter } from 'vue-router';
import { useStore } from "vuex";
import { getUserInfo } from '@/utils/common'
import SldCommonEmpty from '@/components/SldCommonEmpty'
import { useSystemSetting, queryMemberConfig } from '@/utils/hooks';
export default {
    name: "SuperIndex",
    components: {
        SldCommonEmpty
    },
    setup () {
        const { proxy } = getCurrentInstance();
        const L = proxy.$getCurLanguage();
        const router = useRouter();
        const store = useStore();
        const memberFlag = ref(false)
        const memberConfig = ref({})
        const memberInfo = ref(store.state.memberInfo);
        const mainBg = require('@/assets/super/main_bg.png');
        const topBg = require('@/assets/super/top_bg.png');

        const block_img = {
            block_1: require('@/assets/super/block_1.png'),
            block_2: require('@/assets/super/block_2.png'),
            block_3: require('@/assets/super/block_3.png'),
            block_4: require('@/assets/super/block_4.png'),
        }

        const numberMap = {
            1: '一',
            2: '两',
            3: '三',
            4: '四',
            5: '五'
        }

        //立即开通
        const goBuy = () => {
            router.push('/super/confirm');
        };

        //累计省钱
        const gotoHistory = () => {
            router.push('/super/history');
        };

        const rightList = ref([])

        const getRights = () => {
            return proxy.$get('v3/member/front/memberSetting/rights')
        }

        const handleRight = async () => {
            let partial_right_res = await getRights()
            let partial_right = (partial_right_res.state == 200 && partial_right_res.data) ? partial_right_res.data : {}
            let mainList = []
            if (memberConfig.value.super_discount_enabled == 1) {
                mainList.push({
                    type: 'super_discount',
                    img: block_img.block_1
                })
            }
            if (memberConfig.value.super_integral_multiple_enabled == 1) {
                mainList.push({
                    type: 'super_integral_multiple',
                    img: block_img.block_2
                })
            }
            if (memberConfig.value.super_coupon_enabled == 1 && Number(partial_right.superAmount) > 0) {
                mainList.push({
                    type: 'super_coupon',
                    value: Number(partial_right.superAmount),
                    img: block_img.block_3
                })
            }
            if (memberConfig.value.super_birthday_coupon_enabled == 1) {
                mainList.push({
                    type: 'super_birthday_coupon',
                    img: block_img.block_4
                })
            }
            if (memberConfig.value.super_freight_coupon_enabled == 1 && partial_right.freightAmount) {
                mainList.push({
                    type: 'super_freight_coupon',
                    value: isNaN(Number(partial_right.freightAmount)) ? 0 : Number(partial_right.freightAmount),
                    img: block_img.block_3
                })
            }
            rightList.value = mainList
        }

        onMounted(async () => {
            //超级会员权益
			let memberConfigField = [
				'super_is_enable',
				'super_custom_name',
				'super_pc_page',
				'super_personal_center_icon',
				'super_discount_enabled',
				'super_discount',
				'super_freight_coupon_enabled',
				'super_coupon_enabled',
				'super_integral_multiple_enabled',
				'super_integral_multiple',
				'super_birthday_coupon_enabled',
			]

            const result = await queryMemberConfig(memberConfigField)
            memberConfig.value = result
            memberFlag.value = true
            handleRight()
            const info = await getUserInfo()
            memberInfo.value.superExpirationDay = info.superExpirationDay
        })

        return {
            rightList,
            L,
            memberInfo,
            mainBg,
            topBg,
            goBuy,
            gotoHistory,
            memberConfig,
            block_img,
            numberMap,
            memberFlag
        }
    }
}
</script>

<style lang="scss">
.sld_super_index {
    width: 100%;
    margin: 0 auto;
    padding-bottom: 60px;
    background-position: top center;
    background-repeat: no-repeat;
    background-size: cover;

    .top_box {
        width: 100%;
        height: 497px;
        color: #FFECE2;
        font-size: 48px;
        font-family: PingFang SC;
        font-weight: 500;
        background-position: top center;
        background-repeat: no-repeat;
        background-size: cover;
        // background-color: #16171B;

        .top_box_main {
            font-size: 24px;
            font-family: Source Han Sans CN;
            font-weight: 400;

            .top_box_main_imgs {
                position: relative;
                width: 93px;
                height: 93px;
                margin-top: 40px;

                .top_box_main_head {
                    position: absolute;
                    top: -44px;
                    left: -50px;
                    z-index: 1;
                    width: 162px;
                    height: 101px;
                }

                .top_box_main_logo {
                    position: relative;
                    z-index: 2;
                    width: 93px;
                    height: 93px;
                    border-radius: 50%;
                    overflow: hidden;
                }
            }

            .top_box_main_name {
                color: #FFFFFF;
                font-size: 30px;
                font-weight: bold;
                margin-top: 20px;
            }

            .top_box_main_time {
                color: #9C7E57;
                margin-top: 12px;
            }

            .top_box_main_next {
                width: 352px;
                height: 70px;
                line-height: 70px;
                color: #8D5A0B;
                font-size: 30px;
                font-family: PingFang SC;
                font-weight: 500;
                text-align: center;
                background: linear-gradient(90deg, #F8D9AD, #D6A364);
                border-radius: 35px;
                cursor: pointer;
                margin-top: 50px;
            }

            .top_box_main_total {
                color: #7C613F;
                margin-top: 25px;
                cursor: pointer;

                span {
                    color: #f3cf9e;

                    &:nth-child(1) {
                        font-size: 30px;
                        font-weight: bold;
                        margin-left: 5px;
                        margin-right: 5px;
                    }
                }
            }

            .top_box_main_title {
                font-size: 34px;
                font-family: PingFang SC;
                font-weight: 500;
                color: #FFECE2;
                text-align: center;
                text-shadow: -2px 18px 40px rgba(9, 8, 8, 0.85);
                background: linear-gradient(180deg, #ECE0C1 0%, #FFCDA1 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }

            .top_box_main_desc {
                font-size: 60px;
                font-family: PingFang SC;
                font-weight: bold;
                color: #FFECE2;
                text-align: center;
                text-shadow: -2px 18px 40px rgba(9, 8, 8, 0.85);
                background: linear-gradient(180deg, #ECE0C1 0%, #FFCDA1 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                margin-top: 26px;
            }

            .top_box_main_btn {
                width: 320px;
                height: 64px;
                line-height: 64px;
                color: #191738;
                font-size: 30px;
                font-family: Microsoft YaHei;
                font-weight: 400;
                text-align: center;
                background: linear-gradient(0deg, #EDE2C4, #E2C29C);
                border-radius: 35px;
                margin-top: 90px;
                cursor: pointer;
            }
        }
    }

    .main_box {
        padding-top: 15px;

        .main_box_line {
            width: 1200px;
        }

        .main_box_title {
            color: #E7B274;
            font-size: 32px;
            font-family: PingFang SC;
            font-weight: 500;
            margin-top: 22px;
        }

        .main_box_list {
            width: 1200px;
            height: 265px;
            background: linear-gradient(0deg, #27283280 0%, #272A3580 100%);
            border-radius: 20px;
            margin-top: 40px;
            padding: 0 30px;

            .main_box_item {
                flex: 1;

                img {
                    width: 92px;
                    height: 92px;
                }

                span {
                    color: #FCD4A5;
                    font-size: 28px;
                    font-family: PingFang SC;
                    font-weight: 400;
                    margin-top: 22px;
                }
            }
        }
    }

    .list_img {
        display: block;
        width: 1200px;
        height: auto;
        margin: 34px auto;
        height: 354px;
        background-position: center center;
        background-size: contain;
        background-repeat: no-repeat;
        position: relative;
    }
}


.block_class {

    .white_text {
        font-size: 24px;
        font-family: PingFang SC;
        font-weight: 500;
        color: #FFFFFF;
    }

    .mb10 {
        margin-bottom: 10px;
    }

    .sort {
        font-size: 100px;
        font-family: Acumin Variable Concept;
        font-weight: normal;
        color: #FFEACC;
        opacity: 0.1;
        margin-bottom: 5px;
        font-style: italic;
    }

    .sort_big {
        font-family: PingFang SC;
        font-weight: bold;
        color: #D7A968;
    }

    .font65 {
        font-size: 65px;
    }

    .font50 {
        font-size: 50px;
    }

    .font39 {
        font-size: 39px;
    }

    .dark {
        color: #25273A !important;
    }

    .block_class_1 {
        position: absolute;
        right: 40px;
        top: 40px;

        .sort_small {
            font-size: 30px;
            font-family: PingFang SC;
            font-weight: 500;
            color: #D7A968;
            margin-top: 17px;
            margin-left: 8px;
        }

        .white_text {
            font-size: 24px;
            font-family: PingFang SC;
            font-weight: 500;
            color: #FFFFFF;
        }
    }


    .block_class_2 {
        position: absolute;
        left: 40px;
        top: 30px;

        .no_small {
            font-size: 37px;
            font-family: PingFang SC;
            font-weight: 500;
            color: #D7A968;
            margin-top: 16px;
        }

        &_text3 {
            width: 340px;
            word-break: break-all;
            text-align: center;
            margin-top: 20px;
            line-height: 35px;
        }

    }

    .block_class_3 {

        position: absolute;
        top: 75px;
        right: 130px;
        width: 550px;
        height: 205px;

        .block_class_3_reg1 {
            .reg1_text1 {
                font-size: 26px;
                font-family: PingFang SC;
                font-weight: 500;
                color: #FFFFFF;
                margin-bottom: 15px;
            }

            .reg1_text2 {
                font-size: 38px;
                color: #F7C681;
            }
        }
    }

    .block_class_4 {
        right: 140px;
    }

    .block_class_5 {

        position: absolute;
        left: 123px;
        top: 110px;
        width: 500px;
        height: 145px;

        .price_small {
            font-size: 41px;
        }

        .price_big {
            font-size: 70px;
        }

        &_section1 {
            width: 230px;
        }

        &_text {
            font-size: 25px;
            font-family: PingFang SC;
            font-weight: 500;
            color: #F2C99D;
        }

        .dot {
            width: 4px;
            height: 4px;
            background: #F7CA93;
            border-radius: 50%;
        }

    }

}
</style>