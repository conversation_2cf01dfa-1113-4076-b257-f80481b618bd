/**
 *  项目的配置文件
 */

export const apiUrl = `https://pc.shengu.shop/`; //接口请求地址
export const chatUrl = `wss://im.shengu.shop`; //客服地址
export const mUrl = `https://m.shengu.shop/`; //移动端网页地址
export const emojiPath = `https://sg-mall-prod.oss-accelerate.aliyuncs.com/java/bbc/mobile/emoji/`; //表情图片线上前缀，pc和im和移动端必须一致
export const curLang = "zh"; //当前语言,zh:中文，若为其他语言，需要对应/src/assets/language下面的文件名
export const title = "莘古商城"; //浏览器顶部title
export const gdKey = "b618f24ac6d6644c8c8c525470cf7a2f"; //高德web-js key
export const gdSecurityCode = "c32cf6394971458c91bd13149a7cc11f"; //高德web-js 安全密钥
export const statShowDebug = false; //是否开启统计的调试

/** copyright *** slodon *** version-v5.5.1 *** date-2024-09-27 ***主版本v5.5.2.2**/
