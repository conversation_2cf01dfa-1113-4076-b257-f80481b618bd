import { get } from './request'
import { onMounted, ref } from 'vue'

//集中处理的获取系统配置的hook
const useSettingFunc = () => {

	let funcSet = []
	let argsSet = []
	let requestCount = 0
	let timer = null

	const execSystemSetting = () => {
		get("v3/system/front/setting/getSettings", { names: argsSet.toString() }).then(res => {
			let obj = {}
			for (let i in argsSet) {
				obj[argsSet[i]] = res.data[i]
			}
			funcSet.forEach(({ resolve }) => {
				resolve(obj)
			})
		}).catch(() => {
			funcSet.forEach(({ reject }) => {
				reject({})
			})
		}).finally(() => {
			argsSet = []
			funcSet = []
		})
	}

	const getSystemSetting = (args) => {
		argsSet = Array.from(new Set([...argsSet, ...args]))
		clearTimeout(timer)
		return new Promise((resolve, reject) => {
			funcSet = funcSet.concat({ resolve, reject })
			timer = setTimeout(() => {
				if (requestCount === 1) {
					execSystemSetting()
				} else if (requestCount > 1) {
					execSystemSetting()
				}
				requestCount = 0
			}, 100)
			requestCount++;
		})
	}

	return {
		getSystemSetting
	}
}
export const useSystemSetting = useSettingFunc()


//集中处理的获取会员配置的hook
const useMemberConfigFunc = () => {
	let funcSet = []
	let argsSet = []
	let requestCount = 0
	let timer = null
	const execMemberConfig = () => {
		get("v3/member/front/memberSetting/getSettingList", { str: argsSet.toString() }).then(res => {
			let obj = {}
			for (let i in argsSet) {
				obj[argsSet[i]] = res.data[i].imageUrl || res.data[i].value
			}
			funcSet.forEach(({ resolve }) => {
				resolve(obj)
			})
		}).catch(() => {
			funcSet.forEach(({ reject }) => {
				reject({})
			})
		}).finally(() => {
			argsSet = []
			funcSet = []
		})
	}

	const queryMemberConfig = (args) => {
		argsSet = Array.from(new Set([...argsSet, ...args]))
		clearTimeout(timer)
		return new Promise((resolve, reject) => {
			funcSet = funcSet.concat({ resolve, reject })
			timer = setTimeout(() => {
				if (requestCount === 1) {
					execMemberConfig()
				} else if (requestCount > 1) {
					execMemberConfig()
				}
				requestCount = 0
			}, 100)
			requestCount++;
		})
	}
	return queryMemberConfig
}
export const queryMemberConfig = useMemberConfigFunc()

//使用图形验证码
export const useImageCode = () => {
	const showCodeImg = ref('')
	const imgCode = ref('')
	const imgCodeKey = ref('')

	//获取图形验证码
	const getImgCode = () => {
		get('v3/captcha/common/getCaptcha', {}).then(res => {
			if (res.state == 200) {
				showCodeImg.value = 'data:image/png;base64,' + res.data.captcha
				imgCodeKey.value = res.data.key
			}
		})
	}
	onMounted(getImgCode)
	return { showCodeImg, imgCode, getImgCode, imgCodeKey }
}







