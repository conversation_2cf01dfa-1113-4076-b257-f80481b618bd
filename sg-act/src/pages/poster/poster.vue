<template>
  <view class="poster-container">
    <!-- 模糊背景层 -->
    <view class="background-blur" :style="backgroundStyle"></view>
    <!-- 背景遮罩层 -->
    <view class="background-overlay"></view>

    <!-- 加载状态 -->
    <view class="loading-container" v-if="isGenerating">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在生成海报...</text>
    </view>





    <!-- 海报滑动区域 -->
    <view class="poster-content" v-if="posters && posters.length > 0 && !isGenerating">
      <!-- 海报滑动展示 -->
      <swiper
        class="poster-swiper"
        :current="currentIndex"
        @change="onSwiperChange"
        :indicator-dots="false"
        :autoplay="false"
        :circular="false"
        :display-multiple-items="1"
        previous-margin="40rpx"
        next-margin="120rpx"
        :skip-hidden-item-layout="true"
      >
        <swiper-item
          v-for="(poster, index) in posters"
          :key="index"
          class="swiper-item"
        >
          <view class="poster-card" :class="{ 'active': index === currentIndex }">
            <image
              :src="poster.url"
              mode="widthFix"
              class="poster-image"
              @longpress="onLongPress(poster.url)"
              @click="onLongPress(poster.url)"
              @error="onImageError"
            />
          </view>
        </swiper-item>
      </swiper>



      <!-- 提示文字 -->
      <view class="tip-container">
        <text class="tip-text">🌟 点击图片预览长按保存到相册</text>
        <text class="tip-subtitle">或点击右上角分享给好友</text>
      </view>

      <!-- 设备ID显示 -->
      <view class="device-id-display" v-if="deviceId">
        <text class="device-id-text">设备ID: {{ deviceId }}</text>
      </view>
    </view>

    <!-- 错误状态 -->
    <view class="error-container" v-if="hasError">
      <text class="error-text">😢 海报生成失败</text>
      <button class="retry-button" @click="generatePosters">重新生成</button>
    </view>

    <!-- 底部操作按钮 -->
    <view class="action-section" v-if="posters.length > 0 && !isGenerating">
      <view class="action-btn" @click="goBack">
        <text class="btn-text">返回首页</text>
      </view>
    </view>
  </view>
</template>

<script>
import fastposter from 'fastposter'
import config from '@/config/env.js'
import { ensureDeviceId } from '@/utils/deviceId.js'

export default {
  name: 'PosterPage',
  data() {
    return {
      config,
      isGenerating: false,
      posterUrl: '', // 保留兼容性
      posters: [], // 新增：存储多个海报
      currentIndex: 0, // 新增：当前显示的海报索引
      hasError: false,
      characterData: {},
      generatedImage: '',
      dimensionScores: {},
      userInfo: null,
      client: null,
      deviceId: '',
      traceId: '', // 新增：追踪ID
      templateIds: ['af5850ec2c8548c5', '6480fefe7cf84a36'] // 新增：模板ID数组
    }
  },
  
  computed: {
    // 背景样式
    backgroundStyle() {
      if (this.generatedImage) {
        return {
          backgroundImage: `url(${this.generatedImage})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          opacity: 1
        }
      }
      return {
        opacity: 0 // 没有图片时隐藏背景层
      }
    }
  },
  
  async onLoad() {
    // 初始化fastposter客户端
    this.client = fastposter.init({
      token: "a70880aade1b4afe8120"
    })

    // 使用统一的设备ID获取方法
    this.deviceId = await ensureDeviceId()
    console.log('📱 Poster页面 - 设备ID已获取:', this.deviceId)

    // 从本地存储获取数据
    this.loadCharacterData()

    // 获取追踪ID
    this.loadTraceId()

    // 开始生成海报
    this.generatePosters()
  },
  
  methods: {


    // 加载角色数据
    loadCharacterData() {
      try {
        const savedCharacterData = uni.getStorageSync('currentCharacterData')
        const savedGeneratedImage = uni.getStorageSync('currentGeneratedImage')
        const savedDimensionScores = uni.getStorageSync('currentDimensionScores')
        const savedUserInfo = uni.getStorageSync('userInfo')

        if (savedCharacterData) {
          this.characterData = savedCharacterData
        }

        if (savedGeneratedImage) {
          this.generatedImage = savedGeneratedImage
        }

        if (savedDimensionScores) {
          this.dimensionScores = savedDimensionScores
        }

        if (savedUserInfo) {
          this.userInfo = savedUserInfo
          console.log('📋 加载的用户信息:', this.userInfo)
        }
        
        console.log('📋 加载的角色数据:', this.characterData)
        console.log('🖼️ 加载的图片数据:', this.generatedImage.substring(0, 50) + '...')
        
      } catch (error) {
        console.error('❌ 加载数据失败:', error)
        this.hasError = true
      }
    },

    // 加载追踪ID
    loadTraceId() {
      try {
        const traceId = uni.getStorageSync('traceId')
        if (traceId) {
          console.log('🔍 加载追踪ID:', traceId)
          this.traceId = traceId
        } else {
          console.log('⚠️ 未找到追踪ID')
        }
      } catch (error) {
        console.error('❌ 加载追踪ID失败:', error)
        this.traceId = ''
      }
    },

    // 获取海报标题（使用用户真实姓名）
    getPosterTitle() {
      // 如果有用户信息，使用用户真实姓名
      if (this.userInfo && this.userInfo.name) {
        // 从角色名中提取身份标签（支持多种分隔符格式）
        if (this.characterData.角色名) {
          const originalName = this.characterData.角色名
          
          // 检查是否包含分隔符（支持多种格式）
          if (originalName.includes(' · ') || originalName.includes(' ·') || originalName.includes('· ') || originalName.includes('·')) {
            let parts
            // 尝试不同的分隔符格式
            if (originalName.includes(' · ')) {
              parts = originalName.split(' · ')
            } else if (originalName.includes(' ·')) {
              parts = originalName.split(' ·')
            } else if (originalName.includes('· ')) {
              parts = originalName.split('· ')
            } else {
              parts = originalName.split('·')
            }
            
            if (parts.length === 2) {
              // 保留身份标签，替换角色名为用户名字
              return `${parts[0]} · ${this.userInfo.name}`
            }
          }
          
          // 如果没有分隔符，直接拼接
          return `${originalName} · ${this.userInfo.name}`
        }
        
        // 如果没有角色名，就只显示用户名
        return this.userInfo.name
      }
      
      // 如果没有用户信息，使用原有的角色名
      return (this.characterData.角色名 || "神秘角色")
    },

    // 格式化分值显示
    formatScoreText() {
      if (!this.dimensionScores || Object.keys(this.dimensionScores).length === 0) {
        return ''
      }
      // 检查是否有有效的分值数据
      const hasValidScores = Object.values(this.dimensionScores).some(score => score > 0)
      if (!hasValidScores) {
        return ''
      }

      const scores = Object.entries(this.dimensionScores)
        .map(([key, value]) => `${key}:${value}`)
        .join(' ')  // 使用冒号分隔，两个空格间隔
      return scores
    },

    // 滑动切换事件
    onSwiperChange(e) {
      this.currentIndex = e.detail.current
      console.log('🔄 切换到海报:', this.currentIndex)
    },

    // 生成多个海报
    async generatePosters() {
      try {
        this.isGenerating = true
        this.hasError = false
        this.posters = []

        console.log('🎨 开始生成多个海报')

        // 并行生成所有海报
        const posterPromises = this.templateIds.map((templateId) =>
          this.generateSinglePoster(templateId)
        )

        const results = await Promise.allSettled(posterPromises)

        // 处理结果
        results.forEach((result, index) => {
          if (result.status === 'fulfilled' && result.value) {
            this.posters.push({
              url: result.value,
              templateId: this.templateIds[index]
            })
          }
        })

        // 设置第一个海报为兼容性posterUrl
        if (this.posters.length > 0) {
          this.posterUrl = this.posters[0].url
        }

        if (this.posters.length === 0) {
          throw new Error('所有海报生成失败')
        }

        console.log(`✅ 成功生成 ${this.posters.length} 个海报`)
        console.log('📋 海报数组:', this.posters)
        this.posters.forEach((poster, index) => {
          console.log(`📋 海报 ${index}:`, poster)
        })

        // 强制触发 Vue 响应式更新
        this.$forceUpdate()
        console.log('🔄 强制更新视图')

      } catch (error) {
        console.error('❌ 海报生成失败:', error)
        this.hasError = true

        uni.showModal({
          title: '生成失败',
          content: '海报生成遇到问题，请稍后重试',
          showCancel: false
        })
      } finally {
        this.isGenerating = false
        console.log('🔄 海报生成流程结束')
        console.log('📊 当前状态:', {
          postersLength: this.posters.length,
          isGenerating: this.isGenerating,
          hasError: this.hasError
        })
      }
    },

    // 生成单个海报
    async generateSinglePoster(templateId) {
      console.log(`🎨 开始生成海报，模板ID: ${templateId}`)

      // 获取mask图片URL
      let maskUrl = ''
      if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        // 开发环境：使用在线mask图片
        maskUrl = 'https://store.fastposter.net/101517/upload/2025/07/05/232100857ee441fa.png'
        console.log('🎭 使用在线mask图片(开发环境)')
      } else {
        // 生产环境：使用本地mask图片
        const baseUrl = window.location.origin
        maskUrl = `${baseUrl}/static/images/poster_mask.png`
        console.log('🎭 使用本地mask图片(生产环境)')
      }

      console.log('🎨 Mask URL:', maskUrl)

      // 准备海报参数 - 使用用户真实姓名
      const posterTitle = this.getPosterTitle()
      // 规范化圆点符号和间距，尝试不同的格式
      const normalizedTitle = posterTitle.replace(/\s*[·•]\s*/g, '·')
      console.log('🏷️ 海报标题:', posterTitle)
      console.log('🏷️ 规范化后:', normalizedTitle)
      
      // 组合人物故事和觉醒关键点作为海报内容
      const story = this.characterData.人物故事 || "未知故事"
      const awakening = this.characterData.觉醒关键点 || "未知觉醒点"
      const posterContent = `${story}\n\n【觉醒关键点】\n${awakening}`
      
      let params = {
        "poster_bg": this.generatedImage,
        "poster_content": posterContent,
        // "poster_mask": maskUrl,
        // "poster_qrcode": `${this.config.apiBaseUrl}/`,
        "poster_title": normalizedTitle,
        "poster_text_score": this.formatScoreText()
      }

      // 根据模板ID添加不同的参数
      if (templateId === 'af5850ec2c8548c5') {
        // 第一个模板需要扫描标题
        params = {
          ...params,
          "poster_scan_title1": "活动咨询",
          "poster_scan_title2": "预约大咖沙龙"
        }
      }
      // 6480fefe7cf84a36 模板不需要 poster_scan_title1 和 poster_scan_title2

      // 构建新的请求体
      const requestBody = {
        "uid": this.deviceId || "unknown_device",
        "tempId": templateId, // 使用传入的模板ID
        "params": params,
        "tid": this.traceId // 添加追踪ID
      }

      console.log('📋 请求体:', requestBody)
      console.log('🎭 原角色名:', this.characterData.角色名)
      console.log('🏷️ 海报标题:', posterTitle)
      console.log('👤 用户姓名:', this.userInfo?.name || '未获取')
      console.log('🎯 分值格式:', this.formatScoreText())

      // 使用新的API接口
      const response = await uni.request({
        url: `${this.config.apiBaseUrl}/api/common/poster/gen`,
        method: 'POST',
        data: requestBody,
        header: {
          'Content-Type': 'application/json'
        }
      })

      if (response.data && response.data.code === 0 && response.data.data && response.data.data.img_url) {
        const eid = response.data.data.eid // 获取子事务ID
        console.log(`✅ 海报生成成功，模板ID: ${templateId}`)
        console.log('🔍 子事务EID:', eid) // 调试打印eid

        // 预加载图片
        this.preloadImage(response.data.data.img_url)

        return response.data.data.img_url
      } else {
        throw new Error(response.data?.msg || '海报生成返回空结果')
      }
    },

    // 保留原有的generatePoster方法以兼容性
    async generatePoster() {
      return this.generatePosters()
    },
    
    // 预加载图片
    preloadImage(url) {
      const img = new Image()
      img.onload = () => {
        console.log('✅ 海报图片预加载完成')
      }
      img.onerror = () => {
        console.error('❌ 海报图片预加载失败')
      }
      img.src = url
    },
    
    // 长按保存
    onLongPress(imageUrl) {
      const url = imageUrl || this.posterUrl
      console.log('👆 用户长按海报:', url)

      // 使用 uni.previewImage 预览图片，用户可以长按保存
      uni.previewImage({
        urls: [url],
        current: url,
        success: () => {
          console.log('✅ 图片预览成功')
        },
        fail: (error) => {
          console.error('❌ 图片预览失败:', error)
          // 如果预览失败，给出提示
          uni.showToast({
            title: '预览失败，请重试',
            icon: 'none'
          })
        }
      })
    },
    
    // 保存到相册（小程序环境）
    saveToAlbum() {
      if (!this.posterUrl) return
      
      uni.saveImageToPhotosAlbum({
        filePath: this.posterUrl,
        success: () => {
          uni.showToast({
            title: '保存成功',
            icon: 'success'
          })
        },
        fail: (error) => {
          console.error('❌ 保存失败:', error)
          uni.showModal({
            title: '保存失败',
            content: '请检查相册权限设置',
            showCancel: false
          })
        }
      })
    },
    
    // 图片加载错误
    onImageError(error) {
      console.error('❌ 海报图片加载错误:', error)
      this.hasError = true
    },
    
    // 返回首页
    goBack() {
      uni.reLaunch({
        url: '/pages/index/index'
      })
    }
  }
}
</script>

<style scoped>
.poster-container {
  min-height: 100vh;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  /* 适配微信H5环境，减少顶部和底部padding，考虑微信工具栏 */
  padding: 10rpx 20rpx 20rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
  /* 确保在微信环境中也能正常显示 */
  box-sizing: border-box;
}

/* 模糊背景层 */
.background-blur {
  position: absolute;
  top: -20rpx;
  left: -20rpx;
  right: -20rpx;
  bottom: -20rpx;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  filter: blur(20rpx);
  z-index: 0;
  transition: opacity 0.6s ease-in-out;
}

/* 背景遮罩层 */
.background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    180deg, 
    rgba(0, 0, 0, 0.3) 0%,
    rgba(0, 0, 0, 0.5) 50%,
    rgba(0, 0, 0, 0.7) 100%
  );
  z-index: 1;
}

.loading-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  position: relative;
  z-index: 2;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 32rpx;
  color: white;
  font-weight: 500;
}

.poster-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: start;
  width: 100%;
  max-width: 750rpx;
  position: relative;
  z-index: 2;
  /* 适配微信H5环境，减少上下间距 */
  padding: 20rpx 0 60rpx;
  /* 确保在有限空间内合理显示 */
  min-height: 600rpx;

}

/* 海报滑动容器 */
.poster-swiper {
  width: 100%;
  height: 1100rpx;
  /* margin-bottom: 30rpx; */
}

.swiper-item {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 10rpx;
  height: 100%;
}

.poster-card {
  transition: all 0.3s ease;
  transform: scale(0.92);
  opacity: 0.7;
  width: 100%;
  display: flex;
  justify-content: center;
}

.poster-card.active {
  transform: scale(1);
  opacity: 1;
}

.poster-image {
  width: 100%;
  max-width: 680rpx;
  border-radius: 20rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
  display: block;
}



.tip-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  padding: 20rpx 30rpx;
  border-radius: 25rpx;
  text-align: center;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  /* 适配微信H5环境，减少高度占用 */
  margin-top: 20rpx;
}

.tip-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 10rpx;
}

.tip-subtitle {
  font-size: 24rpx;
  color: #666;
  display: block;
}

.device-id-display {
  margin-top: 20rpx;
  text-align: center;
  /* background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx); */
  border-radius: 15rpx;
  padding: 0 20rpx;
  /* border: 1rpx solid rgba(255, 255, 255, 0.1); */
}

.device-id-text {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.7);
  letter-spacing: 1rpx;
}

.error-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  position: relative;
  z-index: 2;
}

.error-text {
  font-size: 32rpx;
  margin-bottom: 40rpx;
  text-align: center;
}

.retry-button {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid white;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

.retry-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 底部操作按钮样式 */
.action-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 30rpx;
  padding: 20rpx 30rpx 40rpx;
  background: linear-gradient(180deg, rgba(15, 15, 35, 0) 0%, rgba(15, 15, 35, 0.8) 30%, rgba(15, 15, 35, 1) 100%);
  backdrop-filter: blur(10rpx);
  z-index: 10;
}

.action-btn {
  background: linear-gradient(135deg, #1a1a3a 0%, #2d2d5a 100%);
  border: 2rpx solid #00d4ff;
  border-radius: 50rpx;
  padding: 24rpx 48rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  /* 蓝色科技风格发光效果 */
  box-shadow:
    0 0 10rpx rgba(0, 212, 255, 0.3),
    0 0 20rpx rgba(0, 212, 255, 0.1),
    inset 0 0 10rpx rgba(0, 212, 255, 0.05);
  min-width: 240rpx;
  text-align: center;
}

.action-btn:hover {
  /* 悬停时增强发光效果 */
  box-shadow:
    0 0 15rpx rgba(0, 212, 255, 0.4),
    0 0 30rpx rgba(0, 212, 255, 0.2),
    inset 0 0 15rpx rgba(0, 212, 255, 0.08);
  border-color: #33e6ff;
}

.action-btn:active {
  transform: scale(0.95);
  /* 点击时的发光效果 */
  box-shadow:
    0 0 8rpx rgba(0, 212, 255, 0.6),
    0 0 16rpx rgba(0, 212, 255, 0.3),
    inset 0 0 12rpx rgba(0, 212, 255, 0.2);
}

.btn-text {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: bold;
  /* 文字发光效果 */
  text-shadow:
    0 0 10rpx rgba(0, 212, 255, 0.8),
    0 0 20rpx rgba(0, 212, 255, 0.4),
    0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  letter-spacing: 1rpx;
}

</style>