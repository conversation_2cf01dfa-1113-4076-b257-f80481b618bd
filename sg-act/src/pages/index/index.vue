<template>
  <view class="container">
    <view class="main-content">
      <view class="top-section">
        <image class="hero-banner" src="https://sg-mall-prod.oss-cn-shanghai.aliyuncs.com/act/image_top_banner.png" mode="widthFix"></image>
        <view class="particles-container">
          <view class="particle particle-1"></view>
          <view class="particle particle-2"></view>
          <view class="particle particle-3"></view>
          <view class="particle particle-4"></view>
          <view class="particle particle-5"></view>
          <view class="particle particle-6"></view>
          <view class="particle particle-7"></view>
          <view class="particle particle-8"></view>
          <view class="particle particle-9"></view>
          <view class="particle particle-10"></view>
          <view class="particle particle-11"></view>
          <view class="particle particle-12"></view>
        </view>
      </view>
      
      <view class="bottom-section">
        <image class="bottom-bg" src="https://sg-mall-prod.oss-cn-shanghai.aliyuncs.com/act/image_bottom.png" mode="widthFix"></image>
        <view class="button-container">
          <image 
            class="action-btn" 
            :class="{ 'loading': isLoading }"
            src="/static/images/image_bottom_button.png" 
            mode="widthFix" 
            @click="getLifeScript"
          ></image>
          <view class="device-id-display" v-if="deviceId">
            <text class="device-id-text">设备ID: {{ deviceId }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 隐藏的测试区域 - 连续点击5次清空缓存 -->
    <view class="debug-area" @click="handleDebugClick"></view>
    
    <!-- 用户信息输入弹窗 -->
    <UserInfoInput 
      :visible="showUserInfoModal" 
      @submit="handleUserInfoSubmit" 
      @cancel="handleUserInfoCancel"
    />
  </view>
</template>

<script>
import { ensureDeviceId } from '@/utils/deviceId.js'
import UserInfoInput from '@/components/UserInfoInput.vue'
import config from '@/config/env.js'

export default {
  components: {
    UserInfoInput
  },
  data() {
    return {
      config,
      name: import.meta.env.VITE_APP_NAME || '人生剧本',
      isLoading: false,
      deviceId: '',
      showUserInfoModal: false,
      userInfo: null,
      traceId: '', // 新增：存储追踪ID
      // 调试相关
      debugClickCount: 0,
      debugClickTimer: null
    }
  },
  async onLoad() {
    // 使用统一的设备ID获取方法
    this.deviceId = await ensureDeviceId()
    console.log('📱 Index页面 - 设备ID已获取:', this.deviceId)

    // 校验缓存版本和时间
    this.validateCacheVersion()
  },
  methods: {
    // 校验缓存版本和时间
    validateCacheVersion() {
      console.log('🔍 开始校验缓存版本和时间')

      // 获取当前版本号（基于今天的日期）
      const currentVersion = this.getCurrentVersion()
      console.log('📅 当前版本号:', currentVersion)

      // 获取缓存的版本信息
      const cacheInfo = uni.getStorageSync('cacheInfo')
      console.log('💾 缓存信息:', cacheInfo)

      // 定义最小有效版本（2025-07-10）
      const minValidVersion = '20250710'

      let shouldClearCache = false

      if (!cacheInfo || !cacheInfo.cache_time || !cacheInfo.version) {
        console.log('⚠️ 缓存信息不完整，需要清空缓存')
        shouldClearCache = true
      } else {
        // 检查版本是否过旧
        if (cacheInfo.version < minValidVersion) {
          console.log('⚠️ 缓存版本过旧，需要清空缓存')
          shouldClearCache = true
        }

        // 检查缓存时间是否超过5天
        const cacheTime = new Date(cacheInfo.cache_time)
        const now = new Date()
        const daysDiff = Math.floor((now - cacheTime) / (1000 * 60 * 60 * 24))

        if (daysDiff > 5) {
          console.log(`⚠️ 缓存时间超过5天 (${daysDiff}天)，需要清空缓存`)
          shouldClearCache = true
        }
      }

      if (shouldClearCache) {
        console.log('🗑️ 清空无效缓存')
        uni.clearStorage()

        // 重新设置当前版本信息
        this.setCacheInfo()
      } else {
        console.log('✅ 缓存校验通过')
      }
    },

    // 获取当前版本号（格式：YYYYMMDD）
    getCurrentVersion() {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      return `${year}${month}${day}`
    },

    // 设置缓存信息
    setCacheInfo() {
      const cacheInfo = {
        version: this.getCurrentVersion(),
        cache_time: new Date().toISOString()
      }
      uni.setStorageSync('cacheInfo', cacheInfo)
      console.log('💾 缓存信息已更新:', cacheInfo)
    },

    async getLifeScript() {
      if (this.isLoading) return
      
      console.log('🎭 点击生成剧本按钮')
      
      // 检查本地存储是否有用户信息缓存
      const cachedUserInfo = uni.getStorageSync('userInfo')
      if (cachedUserInfo) {
        console.log('✅ 发现用户信息缓存，直接跳转到报告页面')
        this.userInfo = cachedUserInfo
        this.isLoading = true
        
        // 直接跳转到报告页面
        uni.navigateTo({
          url: `/pages/report/report`,
          success: () => {
            console.log('✅ 成功跳转到报告页面')
          },
          fail: (err) => {
            console.error('❌ 页面跳转失败:', err)
            uni.showModal({
              title: '跳转失败',
              content: '无法跳转到报告页面，请重试',
              showCancel: false
            })
          },
          complete: () => {
            this.isLoading = false
          }
        })
        return
      }
      
      console.log('🎭 未发现用户信息缓存，显示用户信息输入弹窗')
      
      // 显示用户信息输入弹窗
      this.showUserInfoModal = true
    },

    async handleUserInfoSubmit(userInfo) {
      console.log('✅ 用户提交信息:', userInfo)
      this.userInfo = userInfo
      this.showUserInfoModal = false
      
      // 保存用户信息到本地存储
      uni.setStorageSync('userInfo', userInfo)
      console.log('💾 用户信息已保存到本地存储')

      // 更新缓存信息
      this.setCacheInfo()
      
      this.isLoading = true
      
      try {
        // 调用 trace/new API 创建追踪记录
        console.log('🔍 创建追踪记录...')
        const traceResponse = await uni.request({
          url: `${this.config.apiBaseUrl}/api/common/trace/new`,
          method: 'POST',
          header: {
            'Content-Type': 'application/json'
          },
          data: {
            device_id: this.deviceId,
            app_code: "act",
            event_code: "726poster",
            context_info: {
              nick_name: userInfo.name,
              birth_date: userInfo.birthdate,
              sex: userInfo.gender
            }
          }
        })
        
        console.log('📊 追踪API响应:', traceResponse)
        
        if (traceResponse.statusCode === 200 && traceResponse.data && traceResponse.data.data) {
          this.traceId = traceResponse.data.data.tid
          console.log('✅ 追踪ID获取成功:', this.traceId)
          
          // 保存追踪ID到本地存储
          uni.setStorageSync('traceId', this.traceId)
        } else {
          console.error('❌ 追踪API调用失败:', traceResponse)
          // 即使追踪失败，也继续流程
        }
        
      } catch (error) {
        console.error('❌ 创建追踪记录失败:', error)
        // 即使追踪失败，也继续流程
      }
      
      // 跳转到报告页面
      uni.navigateTo({
        url: `/pages/report/report`,
        success: () => {
          console.log('✅ 成功跳转到报告页面')
        },
        fail: (err) => {
          console.error('❌ 页面跳转失败:', err)
          uni.showModal({
            title: '跳转失败',
            content: '无法跳转到报告页面，请重试',
            showCancel: false
          })
        },
        complete: () => {
          this.isLoading = false
        }
      })
    },

    handleUserInfoCancel() {
      console.log('❌ 用户取消输入')
      this.showUserInfoModal = false
    },

    // 隐藏的调试点击处理
    handleDebugClick() {
      this.debugClickCount++
      console.log(`🐛 首页调试点击次数: ${this.debugClickCount}`)

      // 清除之前的定时器
      if (this.debugClickTimer) {
        clearTimeout(this.debugClickTimer)
      }

      // 如果连续点击5次，清空缓存
      if (this.debugClickCount >= 5) {
        console.log('🗑️ 首页触发调试清空缓存')

        // 清空所有缓存
        uni.clearStorage()

        // 显示提示
        uni.showToast({
          title: '缓存已清空',
          icon: 'success',
          duration: 2000
        })

        // 重置计数
        this.debugClickCount = 0

        return
      }

      // 设置定时器，3秒后重置计数
      this.debugClickTimer = setTimeout(() => {
        this.debugClickCount = 0
        console.log('🐛 首页调试点击计数已重置')
      }, 3000)
    }

  }
}
</script>

<style>
.container {
  min-height: 100vh;
  background: #000;
  position: relative;
  overflow: hidden;
}

.main-content {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #1d2150;
}

.top-section {
  width: 100%;
  position: relative;
}

.hero-banner {
  width: 100%;
  height: auto;
  display: block;
}

.particles-container {
  position: absolute;
  top: calc(50% + 200px);
  left: 50%;
  transform: translate(-50%, -50%);
  width: 300rpx;
  height: 300rpx;
  pointer-events: none;
  z-index: 2;
}

.particle {
  position: absolute;
  width: 8rpx;
  height: 8rpx;
  background: #00d4ff;
  border-radius: 50%;
  box-shadow: 0 0 10rpx #00d4ff, 0 0 20rpx #00d4ff, 0 0 30rpx #00d4ff;
  opacity: 0.8;
}

.particle-1 {
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  animation: orbit1 4s linear infinite;
}

.particle-2 {
  top: 25rpx;
  right: 25rpx;
  animation: orbit2 3.5s linear infinite;
}

.particle-3 {
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  animation: orbit3 4.2s linear infinite;
}

.particle-4 {
  bottom: 25rpx;
  right: 25rpx;
  animation: orbit4 3.8s linear infinite;
}

.particle-5 {
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  animation: orbit5 4.1s linear infinite;
}

.particle-6 {
  bottom: 25rpx;
  left: 25rpx;
  animation: orbit6 3.7s linear infinite;
}

.particle-7 {
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  animation: orbit7 4.3s linear infinite;
}

.particle-8 {
  top: 25rpx;
  left: 25rpx;
  animation: orbit8 3.9s linear infinite;
}

.particle-9 {
  top: 12rpx;
  left: 75rpx;
  animation: orbit9 3.6s linear infinite;
}

.particle-10 {
  top: 75rpx;
  right: 12rpx;
  animation: orbit10 4.4s linear infinite;
}

.particle-11 {
  bottom: 12rpx;
  right: 75rpx;
  animation: orbit11 3.3s linear infinite;
}

.particle-12 {
  bottom: 75rpx;
  left: 12rpx;
  animation: orbit12 4.5s linear infinite;
}

@keyframes orbit1 {
  0% { transform: translateX(-50%) rotate(0deg) translateX(120rpx) rotate(0deg); }
  100% { transform: translateX(-50%) rotate(360deg) translateX(120rpx) rotate(-360deg); }
}

@keyframes orbit2 {
  0% { transform: rotate(30deg) translateX(110rpx) rotate(-30deg); }
  100% { transform: rotate(390deg) translateX(110rpx) rotate(-390deg); }
}

@keyframes orbit3 {
  0% { transform: translateY(-50%) rotate(90deg) translateX(130rpx) rotate(-90deg); }
  100% { transform: translateY(-50%) rotate(450deg) translateX(130rpx) rotate(-450deg); }
}

@keyframes orbit4 {
  0% { transform: rotate(150deg) translateX(115rpx) rotate(-150deg); }
  100% { transform: rotate(510deg) translateX(115rpx) rotate(-510deg); }
}

@keyframes orbit5 {
  0% { transform: translateX(-50%) rotate(180deg) translateX(125rpx) rotate(-180deg); }
  100% { transform: translateX(-50%) rotate(540deg) translateX(125rpx) rotate(-540deg); }
}

@keyframes orbit6 {
  0% { transform: rotate(210deg) translateX(105rpx) rotate(-210deg); }
  100% { transform: rotate(570deg) translateX(105rpx) rotate(-570deg); }
}

@keyframes orbit7 {
  0% { transform: translateY(-50%) rotate(270deg) translateX(135rpx) rotate(-270deg); }
  100% { transform: translateY(-50%) rotate(630deg) translateX(135rpx) rotate(-630deg); }
}

@keyframes orbit8 {
  0% { transform: rotate(330deg) translateX(108rpx) rotate(-330deg); }
  100% { transform: rotate(690deg) translateX(108rpx) rotate(-690deg); }
}

@keyframes orbit9 {
  0% { transform: rotate(45deg) translateX(140rpx) rotate(-45deg); }
  100% { transform: rotate(405deg) translateX(140rpx) rotate(-405deg); }
}

@keyframes orbit10 {
  0% { transform: rotate(135deg) translateX(100rpx) rotate(-135deg); }
  100% { transform: rotate(495deg) translateX(100rpx) rotate(-495deg); }
}

@keyframes orbit11 {
  0% { transform: rotate(225deg) translateX(145rpx) rotate(-225deg); }
  100% { transform: rotate(585deg) translateX(145rpx) rotate(-585deg); }
}

@keyframes orbit12 {
  0% { transform: rotate(315deg) translateX(95rpx) rotate(-315deg); }
  100% { transform: rotate(675deg) translateX(95rpx) rotate(-675deg); }
}

.bottom-section {
  width: 100%;
  position: relative;
}

.bottom-bg {
  width: 100%;
  height: auto;
  display: block;
}

.button-container {
  position: absolute;
  bottom: 230rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 600rpx;
  max-width: 80%;
}

/* iOS和微信浏览器适配 */
@media screen and (max-height: 750px) {
  .button-container {
    bottom: 230rpx; /* 减少底部距离 */
  }
}

@media screen and (max-height: 667px) {
  .button-container {
    bottom: 80rpx; /* iPhone SE等小屏幕进一步减少距离 */
    width: 500rpx; /* 稍微缩小按钮 */
  }
}

.action-btn {
  width: 100%;
  height: auto;
  display: block;
  animation: breathe 2s ease-in-out infinite;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.action-btn.loading {
  opacity: 0.6;
  animation-play-state: paused;
  pointer-events: none;
}

.action-btn:active {
  animation-play-state: paused;
  transform: scale(0.95);
}

.device-id-display {
  margin-top: 20rpx;
  text-align: center;
}

.device-id-text {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
  letter-spacing: 1rpx;
}

@keyframes breathe {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* 隐藏的测试区域样式 */
.debug-area {
  position: fixed;
  top: 0;
  right: 0;
  width: 60rpx;
  height: 60rpx;
  background: transparent;
  z-index: 9999;
  opacity: 0;
}

@media (max-width: 750rpx) {
  .particles-container {
    width: 250rpx;
    height: 250rpx;
  }
  
  .particle {
    width: 6rpx;
    height: 6rpx;
  }
  
  .button-container {
    width: 500rpx;
    bottom: 60rpx;
  }
}
</style>
