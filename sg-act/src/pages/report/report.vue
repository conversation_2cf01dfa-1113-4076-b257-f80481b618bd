<template>
  <view class="report-container">
    <!-- 背景 -->
    <view class="background-layer">
      <!-- 背景图片层 -->
      <view class="bg-image"></view>
      <!-- 黑色半透明遮罩层 -->
      <view class="bg-overlay"></view>
      <!-- 保留粒子效果作为装饰 -->
      <view class="bg-particles">
        <!-- 星河粒子 -->
        <view class="particle" v-for="n in 20" :key="n" :style="getParticleStyle()"></view>
        <!-- 大型星云效果 -->
        <view class="nebula" v-for="n in 2" :key="'nebula-' + n" :style="getNebulaStyle()"></view>
      </view>
    </view>
    
    <!-- 主要内容 -->
    <view class="main-content">
      <!-- 标题区域 -->
      <view class="header-section">
        <text class="title">您的专属人生剧本</text>
        <text class="subtitle">{{ subtitleText }}</text>
      </view>
      
      <!-- 卡片轮播区域 -->
      <view class="cards-section">
        <view class="cards-container">
          <!-- 5张卡片层叠 -->
          <view 
            class="card-wrapper"
            v-for="(card, index) in cards" 
            :key="index"
            :class="{ 
              'active': currentCard === index, 
              'flipped': card.flipped,
              'loading': isGenerating && currentCard === index
            }"
            :style="getCardStyle(index)"
            @click="handleCardClick(index)"
          >
            <view class="card">
              <!-- 卡片背面（未翻转时显示） -->
              <view class="card-back">
                <view class="card-pattern">
                  <image :src="card.backgroundImage" class="card-background-image" mode="aspectFit"></image>
                </view>
              </view>
              
              <!-- 卡片正面（翻转后显示） -->
              <view class="card-front" :style="generatedImage && card.flipped ? `background-image: url(${generatedImage})` : ''">
                <view class="card-overlay"></view>
                <!-- 角色信息在卡片内部 -->
                <view class="card-character-info" v-if="card.flipped && generatedImage">
                  <text class="card-character-name">{{ getCharacterDisplayName() }}</text>
                  <text class="card-character-scores" v-if="formatScoreText()">{{ formatScoreText() }}</text>
                  <text class="card-character-story">{{ characterData.人物故事 || '未知故事' }}</text>
                  <text class="card-awakening-title">觉醒关键点</text>
                  <text class="card-awakening-content">{{ characterData.觉醒关键点 || '未知觉醒点' }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部固定区域 -->
      <view class="bottom-fixed-section">
        <!-- 打字机效果提示文案 -->
        <view class="typewriter-section" v-if="isGenerating && !reportCompleted">
          <view class="typewriter-container">
            <text class="typewriter-text">{{ currentTypewriterText }}</text>
            <text class="typewriter-cursor">|</text>
          </view>
        </view>

        <!-- 设备ID显示区域 -->
        <view class="device-id-section" v-if="(showActions || generateFailed) && deviceId">
          <text class="device-id-text">设备ID: {{ deviceId }}</text>
        </view>

        <!-- 操作按钮区域 -->
        <view class="action-section" v-if="showActions || generateFailed">
          <!-- 正常完成时的按钮 -->
          <template v-if="showActions && !generateFailed">
            <view class="action-btn" @click="goBack">
              <text class="btn-text">返回首页</text>
            </view>
            <view class="action-btn primary" @click="saveReport">
              <text class="btn-text">保存分享剧本</text>
            </view>
          </template>

          <!-- 生成失败时的按钮 -->
          <template v-if="generateFailed">
            <view class="action-btn primary" @click="regenerateScript">
              <text class="btn-text">重新生成</text>
            </view>
            <view class="action-btn" @click="goBack">
              <text class="btn-text">返回首页</text>
            </view>
          </template>
        </view>
      </view>
    </view>

    <!-- 隐藏的测试区域 - 连续点击5次清空缓存 -->
    <view class="debug-area" @click="handleDebugClick"></view>

  </view>
</template>

<script>
import config from '@/config/env.js'
import { ensureDeviceId } from '@/utils/deviceId.js'
import { getTotemByBirthdate, getTotemTraits } from '@/utils/totem.js'

export default {
  data() {
    return {
      config,
      characterData: {},
      generatedImage: '',
      deviceId: '',
      userInfo: null,
      userTotem: null,
      traceId: '', // 新增：追踪ID
      // 5个维度分值
      dimensionScores: {
        觉知: 0,
        智慧: 0,
        行动: 0,
        快乐: 0,
        勇气: 0
      },
      isGenerating: false,
      currentCard: 0,
      showActions: false,
      reportCompleted: false,
      generateFailed: false, // 新增：生成失败状态
      hasExistingData: false, // 新增：是否有已存在的数据
      // 打字机效果相关
      currentTypewriterText: '',
      typewriterMessages: [
        '正在查阅你的人生档案...',
        '提取你的人生关键信息中...',
        '分析你的性格特征...',
        '构建你的专属剧本...',
        '档案查询完毕...',
        '正在生成你的专属形象...',
        '绘制你的人生画卷...',
        '即将为你呈现...'
      ],
      typewriterIndex: 0,
      typewriterCharIndex: 0,
      typewriterTimer: null,
      // 调试相关
      debugClickCount: 0,
      debugClickTimer: null,
      cards: [
        {
          id: 1,
          flipped: false,
          backgroundImage: 'https://sg-mall-prod.oss-cn-shanghai.aliyuncs.com/act/card1.jpg'
        },
        {
          id: 2,
          flipped: false,
          backgroundImage: 'https://sg-mall-prod.oss-cn-shanghai.aliyuncs.com/act/card2.jpg'
        },
        {
          id: 3,
          flipped: false,
          backgroundImage: 'https://sg-mall-prod.oss-cn-shanghai.aliyuncs.com/act/card3.jpg'
        },
        {
          id: 4,
          flipped: false,
          backgroundImage: 'https://sg-mall-prod.oss-cn-shanghai.aliyuncs.com/act/card4.jpg'
        },
        {
          id: 5,
          flipped: false,
          backgroundImage: 'https://sg-mall-prod.oss-cn-shanghai.aliyuncs.com/act/card5.jpg'
        },
      ]
    }
  },
  
  computed: {
    subtitleText() {
      if (this.generateFailed) {
        return '生成遇到问题，请重新尝试'
      } else if (this.reportCompleted) {
        return '精彩人生已生成，人生剧本只有一次'
      } else if (this.hasExistingData) {
        return '欢迎回来，您的剧本已准备就绪'
      } else {
        return '神秘面纱即将揭开...'
      }
    }
  },
  
  async onLoad() {
    console.log('🎭 进入报告详情页')
    console.log('🔍 检查本地存储数据...')

    // 使用统一的设备ID获取方法
    this.deviceId = await ensureDeviceId()
    console.log('📱 Report页面 - 设备ID已获取:', this.deviceId)

    // 获取用户信息
    this.loadUserInfo()

    // 获取追踪ID
    this.loadTraceId()

    // 启动卡片轮播效果
    this.startCardRotation()

    // 检测屏幕高度并应用适配
    this.adaptToScreenHeight()

    // 检查是否有已存在的数据（刷新页面场景）
    this.checkExistingData()
  },
  
  methods: {
    // 检测屏幕高度并应用动态适配
    adaptToScreenHeight() {
      try {
        const systemInfo = uni.getSystemInfoSync()
        const screenHeight = systemInfo.screenHeight
        const windowHeight = systemInfo.windowHeight
        const safeAreaHeight = systemInfo.safeArea ? systemInfo.safeArea.height : windowHeight

        console.log('📱 屏幕适配信息:', {
          screenHeight,
          windowHeight,
          safeAreaHeight,
          platform: systemInfo.platform
        })

        // 根据可用高度动态调整样式
        this.$nextTick(() => {
          const scoreElements = document.querySelectorAll('.card-character-scores')
          if (scoreElements.length > 0) {
            scoreElements.forEach(element => {
              if (safeAreaHeight < 600) {
                // 极小屏幕
                element.style.fontSize = '8rpx'
                element.style.transform = 'scale(0.7)'
                element.style.letterSpacing = '0rpx'
              } else if (safeAreaHeight < 700) {
                // 小屏幕
                element.style.fontSize = '12rpx'
                element.style.transform = 'scale(0.8)'
                element.style.letterSpacing = '0.5rpx'
              } else if (safeAreaHeight < 800) {
                // 中等屏幕
                element.style.fontSize = '14rpx'
                element.style.transform = 'scale(0.9)'
                element.style.letterSpacing = '1rpx'
              }
              // 大屏幕保持默认样式
            })
          }
        })
      } catch (error) {
        console.error('❌ 屏幕适配失败:', error)
      }
    },

    // 加载用户信息
    loadUserInfo() {
      try {
        const userInfo = uni.getStorageSync('userInfo')
        if (userInfo && userInfo.name && userInfo.birthdate) {
          console.log('📋 加载用户信息:', userInfo)
          this.userInfo = userInfo
          
          // 计算用户图腾
          if (userInfo.birthdate) {
            this.userTotem = getTotemByBirthdate(userInfo.birthdate)
            console.log('🔮 用户图腾信息:', this.userTotem)
            
            // 验证图腾计算结果
            if (!this.userTotem || !this.userTotem.totem || !this.userTotem.fullName) {
              console.error('❌ 图腾计算结果异常:', this.userTotem)
              this.userTotem = null
            }
          }
        } else {
          console.log('⚠️ 未找到完整的用户信息')
        }
      } catch (error) {
        console.error('❌ 加载用户信息失败:', error)
        this.userInfo = null
        this.userTotem = null
      }
    },

    // 加载追踪ID
    loadTraceId() {
      try {
        const traceId = uni.getStorageSync('traceId')
        if (traceId) {
          console.log('🔍 加载追踪ID:', traceId)
          this.traceId = traceId
        } else {
          console.log('⚠️ 未找到追踪ID')
        }
      } catch (error) {
        console.error('❌ 加载追踪ID失败:', error)
        this.traceId = ''
      }
    },

    // 获取角色显示名称（包含用户名字）
    getCharacterDisplayName() {
      console.log('🎭 getCharacterDisplayName 调用')
      console.log('📋 this.characterData:', this.characterData)
      console.log('👤 this.userInfo:', this.userInfo)
      
      if (!this.characterData.角色名) {
        console.log('⚠️ 角色名为空，返回神秘角色')
        return '神秘角色'
      }
      
      const originalName = this.characterData.角色名
      console.log('🏷️ 原始角色名:', originalName)
      
      // 如果有用户信息，替换角色名中的名字部分
      if (this.userInfo && this.userInfo.name) {
        console.log('👤 用户名字:', this.userInfo.name)
        // 检查原始角色名是否包含分隔符（支持多种格式）
        if (originalName.includes(' · ') || originalName.includes(' ·') || originalName.includes('· ') || originalName.includes('·')) {
          let parts
          // 尝试不同的分隔符格式
          if (originalName.includes(' · ')) {
            parts = originalName.split(' · ')
          } else if (originalName.includes(' ·')) {
            parts = originalName.split(' ·')
          } else if (originalName.includes('· ')) {
            parts = originalName.split('· ')
          } else {
            parts = originalName.split('·')
          }
          
          if (parts.length === 2) {
            // 保留身份标签，替换角色名为用户名字
            const newName = `${parts[0]} · ${this.userInfo.name}`
            console.log('✅ 替换后的角色名:', newName)
            return newName
          }
        }
        // 如果格式不符合预期，直接返回 "身份标签 · 用户名字"
        const newName = `${originalName} · ${this.userInfo.name}`
        console.log('✅ 格式不符合预期，新角色名:', newName)
        return newName
      }
      
      console.log('⚠️ 无用户信息，返回原始角色名:', originalName)
      return originalName
    },

    // 生成随机维度分值（控制均值差别，确保最多只有一个10分）
    generateDimensionScores() {
      // 设定目标均值范围（5-7之间）
      const targetAverage = 5.5 + Math.random() * 1.5 // 5.5-7的目标均值
      
      // 先生成基础分值（1-9范围，避免直接生成10分）
      const baseScores = [
        Math.floor(Math.random() * 9) + 1,
        Math.floor(Math.random() * 9) + 1,
        Math.floor(Math.random() * 9) + 1,
        Math.floor(Math.random() * 9) + 1,
        Math.floor(Math.random() * 9) + 1
      ]
      
      // 计算当前均值
      const currentAverage = baseScores.reduce((sum, score) => sum + score, 0) / 5
      
      // 计算需要调整的差值
      const adjustment = targetAverage - currentAverage
      
      // 按比例调整每个分值
      const adjustedScores = baseScores.map(score => {
        let newScore = Math.round(score + adjustment)
        // 确保分值在1-9范围内（先不允许10分）
        newScore = Math.max(1, Math.min(9, newScore))
        return newScore
      })
      
      // 最终验证：如果调整后的均值偏差仍然较大，进行微调
      let finalAverage = adjustedScores.reduce((sum, score) => sum + score, 0) / 5
      if (Math.abs(finalAverage - targetAverage) > 0.5) {
        // 找到需要微调的分值
        for (let i = 0; i < adjustedScores.length && Math.abs(finalAverage - targetAverage) > 0.2; i++) {
          if (finalAverage < targetAverage && adjustedScores[i] < 9) {
            adjustedScores[i]++
            finalAverage = adjustedScores.reduce((sum, score) => sum + score, 0) / 5
          } else if (finalAverage > targetAverage && adjustedScores[i] > 1) {
            adjustedScores[i]--
            finalAverage = adjustedScores.reduce((sum, score) => sum + score, 0) / 5
          }
        }
      }
      
      // 随机选择一个维度作为优势维度，有30%概率设为10分
      if (Math.random() < 0.3) {
        const maxIndex = Math.floor(Math.random() * 5)
        adjustedScores[maxIndex] = 10
      }
      
      // 确保最多只有一个10分
      let tenScoreCount = 0
      for (let i = 0; i < adjustedScores.length; i++) {
        if (adjustedScores[i] === 10) {
          tenScoreCount++
          if (tenScoreCount > 1) {
            adjustedScores[i] = 9 // 将多余的10分改为9分
          }
        }
      }
      
      this.dimensionScores = {
        觉知: adjustedScores[0],
        智慧: adjustedScores[1],
        行动: adjustedScores[2],
        快乐: adjustedScores[3],
        勇气: adjustedScores[4]
      }
      
      const resultAverage = Object.values(this.dimensionScores).reduce((sum, score) => sum + score, 0) / 5
      console.log('🎯 生成维度分值:', this.dimensionScores)
      console.log('📊 目标均值:', targetAverage.toFixed(2), '实际均值:', resultAverage.toFixed(2))
      
      // 验证10分个数
      const tenCount = Object.values(this.dimensionScores).filter(score => score === 10).length
      console.log('✅ 10分维度个数:', tenCount, '(最多1个)')
    },

    // 格式化分值显示
    formatScoreText() {
      // 检查是否有有效的分值数据
      const hasValidScores = Object.values(this.dimensionScores).some(score => score > 0)
      if (!hasValidScores) {
        return '' // 如果没有有效分值，返回空字符串
      }

      // 根据屏幕大小动态调整格式
      const systemInfo = uni.getSystemInfoSync()
      const safeAreaHeight = systemInfo.safeArea ? systemInfo.safeArea.height : systemInfo.windowHeight

      let separator = ' '  // 默认单个空格
      if (safeAreaHeight < 600) {
        // 极小屏幕使用更紧凑的格式
        separator = ' '
      }

      const scores = Object.entries(this.dimensionScores)
        .map(([key, value]) => `${key}:${value}`)
        .join(separator)
      return scores
    },

    // 生成剧本
    async generateScript() {
      try {
        console.log('🎭 开始生成剧本')
        this.isGenerating = true

        // 启动打字机效果
        this.startTypewriter()
        
        // 随机选择不同的创作主题和风格，增加多样性
        const themes = [
          "古代神话与现代心理",
          "奇幻世界的心灵探索",
          "神秘岛屿的灵魂契约",
          "时空穿越者的内心独白",
          "魔法世界的情感羁绊"
        ]
        
        const obsessionTypes = [
          "对完美的病态追求与深层恐惧",
          "渴望被爱却害怕失去的矛盾",
          "逃避痛苦现实的自我保护机制",
          "通过控制一切来获得安全感的强迫",
          "迷失自我只为获得他人认同",
          "害怕亲密关系会带来伤害的防御",
          "对未知命运的焦虑与无力感",
          "童年创伤在成年后的投射与重现",
          "对失败的极度恐惧导致的停滞不前",
          "在爱与恨之间徘徊的情感纠葛"
        ]
        
        const artStyles = [
          // "CGSociety, character design, cartoon style, Pixar Disney, game art"
          "CG艺术，角色设计，卡通风格，新海诚绘画风格，游戏美术"
        ]
        
        // 随机选择元素，增加更多变化
        const randomTheme = themes[Math.floor(Math.random() * themes.length)]
        const randomObsession = obsessionTypes[Math.floor(Math.random() * obsessionTypes.length)]
        const randomArtStyle = artStyles[Math.floor(Math.random() * artStyles.length)]
        const timestamp = Date.now()
        const randomSeed = Math.floor(Math.random() * 10000)
        
        // 获取已生成的身份标签历史，用于启发新的创作方向
        const generatedLabelsHistory = uni.getStorageSync('generatedLabels') || []
        const recentLabels = generatedLabelsHistory.slice(-15) // 获取最近15个，用于创新启发
        
        // 创建反重复创新提示
        const innovationGuidance = recentLabels.length > 0 ? 
          `\n💡 **创新突破挑战**：近期已创作过：${recentLabels.join('、')}。请你彻底跳出这些思维框架！
          
🚀 **创新方向指引**：
- 忘记传统职业概念，思考"存在的本质"
- 从抽象概念、情感状态、能量形式入手
- 创造前所未有的词汇组合
- 每个标签都要让人产生好奇心和想象空间
          
⚡ **创新要求**：绝对不允许任何传统职业词汇！要创造全新的、虚幻的、富有想象力的身份概念！` : 
          `\n💡 **首次创作引导**：请发挥无限想象力，创造独一无二的身份标签。
          
🎯 **创新核心**：不要想"职业"，而要想"存在状态"、"能量形式"、"抽象概念"
⚡ **突破要求**：创造前所未有的虚幻身份，让人第一次听到就充满好奇！`
        
        // 随机选择系统消息，增加AI创作视角的多样性
        const systemMessages = [
          "你是一位深谙人性的资深编剧，擅长挖掘角色内心深处的秘密和恐惧，用高频积极向上的词汇描述故事。",
          "你是具有荣格心理学背景的心理专家，能洞察集体无意识中的原型象征，用高频积极向上的词汇描述故事。。",
          "你是游历各地的民俗学者，对不同文化背景下的人性理解有着独特见解，用高频积极向上的词汇描述故事。。",
          "你是经验丰富的艺术治疗师，善于通过创作表达内心最隐秘的情感，用高频积极向上的词汇描述故事。。",
          "你是研究现代都市心理的社会学家，对当代人的精神困境有深入观察，用高频积极向上的词汇描述故事。。"
        ]
        const randomSystemMessage = systemMessages[Math.floor(Math.random() * systemMessages.length)]
        
        // 移除图腾相关内容
        
        const prompt = `## 任务说明
请你作为资深编剧和心理学专家，基于"${randomTheme}"主题，为我创作一个深度剧本角色。
创作种子：${randomSeed} | 时间戳：${timestamp}${innovationGuidance}

## ⚠️ 重要约束
身份标签创作中严禁使用以下任何字符：编、织、守、护、治、愈、修、复、解、码、程、时、空、星、辰、光、影、心、灵、魂、梦、境、忆、运、言、法、想、约、印、化、示、赎、导、接、梁、扉、匙、镜、具、翼、朵、晶、文、腾、语、式、谕、密、秘、幻、虚、渊、槃、回、果、命、机、谜、忌、洁、圣、高、上、恒、限、极、对、美、粹、始、源、点、岸、界、临、折、变、华、越、破、脱、由、缚、锢、锁、笼、牢、囚、阱、宫、题、码、号、信、号、道、长、鸣、谐、衡、称、环、旋、涡、洞、度、元、间、去、在、来、瞬、那、臾、眼、息、刻
必须创造全新的、前所未有的身份概念！

## 性别约束
用户性别：${this.userInfo?.gender || '未知'}
- 人物故事中的角色必须是${this.userInfo?.gender === '男' ? '男性角色' : this.userInfo?.gender === '女' ? '女性角色' : '性别中性角色'}
- 文生图prompt中的人物描述必须对应${this.userInfo?.gender === '男' ? '男性特征' : this.userInfo?.gender === '女' ? '女性特征' : '中性特征'}
- 故事情节和觉醒关键点应该符合${this.userInfo?.gender === '男' ? '男性' : this.userInfo?.gender === '女' ? '女性' : '性别中性'}的生活体验和心理特点

## 输出要求
### 内容格式
角色名：
人物故事：
觉醒关键点：
文生图prompt：

### 角色创作规范

#### 角色命名要求
- 格式：\`身份标签 · 角色名\`
- 身份标签要求：必须完全原创，3-4个字，富有创新性和想象力，创作时发挥无限想象力，减少限制，允许更多创意表达
- 身份标签创作思路：可以是全新概念、独特存在、原创身份等，必须前所未有，鼓励大胆创新
- 角色名要求：必须是两个字，诗意优雅、独特新颖，避免常见名字
- 必须确保本次生成的身份标签和角色名完全不同于以往任何生成结果
- 严格遵守禁用词汇列表，绝对不能使用列表中的任何字符

#### 人物故事创作重点
本次执念类型启发：${randomObsession}

故事创作要求：
1. **禁用角色名**：人物故事中严禁出现角色名字，使用"她/他"、"主人公"等代词描述
2. **独特性**：必须与之前生成的所有执念完全不同
3. **深度挖掘**：探索人性中的复杂面向和内心冲突
4. **现代共鸣**：结合当代人的心理状态和社会压力
5. **情感真实**：源于真实的人性体验，避免空洞说教和阴暗说辞，要积极向上，高频词汇
6. **篇幅控制**：70字以内，语言生动具体
7. **心理层次**：体现意识、潜意识和集体无意识的交织
8. **神秘象征**：可以融入神秘学或原型学的深层象征

#### 觉醒关键点创作要求
1. **禁用角色名**：觉醒关键点中严禁出现角色名字，使用"她/他"等代词描述
2. **篇幅控制**：40字以内，精炼有力
3. **转折性**：体现角色的重要转变或觉醒时刻
4. **积极向上**：传达正能量和成长价值
5. **独特性**：避免陈词滥调，力求新颖独特

#### 文生图prompt创作指南
艺术风格方向：${randomArtStyle}

**总体风格要求：必须创作治愈系卡通风格的画面，使用明亮柔和的暖色调，营造温馨可爱的疗愈氛围，避免任何阴暗、恐怖或消极元素。画面必须是卡通化的，不要写实风格。**

创作要求：
1. **性别约束**：人物形象必须严格符合${this.userInfo?.gender === '男' ? '男性特征，包括男性体型、发型、服饰和气质' : this.userInfo?.gender === '女' ? '女性特征，包括女性体型、发型、服饰和气质' : '中性特征'}
2. **画风美学**：采用治愈系卡通风格，画面充满温暖和希望感，人物和场景都必须是卡通化的，避免写实风格和阴暗元素，画面描述丰富
3. **色彩搭配**：使用明亮柔和的色调，主要运用暖色系（粉色、淡黄、橙色、薄荷绿、天空蓝等），营造可爱疗愈的氛围
4. **视觉独特性**：确保画面概念前所未有，避免重复，但要保持温馨可爱的基调
5. **情感表达**：通过柔美的视觉元素传达内心的温暖和成长，体现正能量和治愈感
6. **象征手法**：运用温柔的隐喻和象征（如花朵、光芒、彩虹、星辰、蝴蝶等）表达故事主题
7. **技术规格**：超高清细节，柔光构图，温暖光影，营造梦幻唯美的质感
8. **环境设计**：创造充满生机和美好的场景环境（如花园、星空、彩虹桥、水晶宫殿等），体现角色内心的美好世界

## 创新要求
1. **绝对原创**：身份标签、角色名、执念、画面描述必须完全原创，每一次输出绝不重复
2. **身份标签深度创新**：
   - **思维突破**：请彻底跳出传统职业、身份的框架，从以下维度重新思考：
     * 从「感知方式」思考：涟漪感应者、回声捕捉者、微光察觉者
     * 从「时间关系」思考：瞬间收藏者、记忆编织者、未来低语者  
     * 从「空间概念」思考：虚境漫步者、层次穿越者、维度游弋者
     * 从「能量形态」思考：暖流引导者、共振营造者、频率调谐者
     * 从「意识状态」思考：深层倾听者、内心陪伴者、灵魂拥抱者
     * 从「抽象哲学」思考：存在见证者、意义编织者、真理轻抚者
     * 从「艺术创作」思考：色彩呢喃者、线条舞蹈者、形状诗人
     * 从「自然元素」思考：晨露收集者、微风传递者、花香编织者
     * 从「情感载体」思考：温柔守护者、细语陪伴者、心灵点亮者
   - **创新挑战**：要求自己：这个身份标签能让人第一次听到就感到惊喜和好奇吗？
   - **原创验证**：确保这个身份概念在现实和虚构作品中都前所未有
   - **维度拓展**：每次创作都要从全新的维度思考，避免重复概念领域
3. **两字角色名**：必须是两个汉字，具有诗意美感，读音优美，寓意深刻
4. **避免模式化**：打破常规思维，创造意想不到的组合，每次都要有全新的创意
5. **严禁词汇列表**：身份标签中绝对不能包含以下任何词汇或字符：编、织、守、护、治、愈、修、复、解、码、程、时、空、星、辰、光、影、心、灵、魂、梦、境、忆、运、言、法、想、约、印、化、示、赎、导、接、梁、扉、匙、镜、具、翼、朵、晶、文、腾、语、式、谕、密、秘、幻、虚、渊、槃、回、果、命、机、谜、忌、洁、圣、高、上、恒、限、极、对、美、粹、始、源、点、岸、界、临、折、变、华、越、破、脱、由、缚、锢、锁、笼、牢、囚、阱、宫、题、码、号、信、号、道、长、鸣、谐、衡、称、环、旋、涡、洞、度、元、间、去、在、来、瞬、那、臾、眼、息、刻等字符
6. **深层心理**：融入荣格心理学、精神分析等深度理论
7. **当代性**：结合现代社会的独特心理现象
8. **艺术性**：追求文学性和视觉美感的完美结合
9. **独特组合**：确保身份标签+角色名的组合在概念上独一无二，形成前所未有的存在概念

## 输出格式
严格按照JSON格式输出，必须使用中文字段名，不包含任何其他内容：
{
  "角色名": "身份标签 · 角色名",
  "人物故事": "80字以内的故事内容",
  "觉醒关键点": "40字以内的觉醒关键点",
  "文生图prompt": "详细的画面描述"
}

⚠️ 重要：字段名必须是中文，不能使用英文字段名如roleName, characterStory等

## 输出要求
- 角色名：保持原有格式
- 人物故事：80字以内，讲述角色的核心故事，积极向上，严禁出现角色名字，只用代词描述
- 觉醒关键点：40字以内，描述角色的觉醒时刻，严禁出现角色名字，只用代词描述
- 文生图prompt：必须体现治愈系卡通风格，使用明亮柔和的暖色调描述，包含温馨可爱的视觉元素，避免阴暗元素和写实风格
- 总字数控制在120字以内（人物故事+觉醒关键点）`

        console.log('🎭 本次创作参数:')
        console.log('📝 主题:', randomTheme)
        console.log('💭 执念类型:', randomObsession)
        console.log('🎨 艺术风格:', randomArtStyle)
        console.log('🎲 随机种子:', randomSeed)
        console.log('👤 系统身份:', randomSystemMessage)
        console.log('📡 发送剧本生成请求')
        console.log('🔗 API端点: https://ark.cn-beijing.volces.com/api/v3/chat/completions')
        
        const res = await uni.request({
          url: 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
          method: 'POST',
          header: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer e65df678-4995-4f43-a90d-eba6d909654b`
          },
          data: {
            messages: [
              {
                role: 'system',
                content: randomSystemMessage
              },
              {
                role: 'user',
                content: prompt
              }
            ],
            model: 'deepseek-v3-250324',
            temperature: 0.9, // 提高随机性
            max_tokens: 1000,
            top_p: 0.95, // 增加创新性
            presence_penalty: 0.6, // 鼓励使用新词汇
            frequency_penalty: 0.3 // 减少重复
          }
        })
        
        console.log('📥 剧本生成API响应状态码:', res.statusCode)
        console.log('📥 剧本生成API响应数据:', res.data)
        
        if (res.statusCode === 200 && res.data?.choices?.[0]?.message?.content) {
          const content = res.data.choices[0].message.content
          console.log('✅ 剧本生成成功!')
          console.log('📖 生成的剧本内容:', content)
          
          // 尝试解析JSON格式的返回内容
          let characterData
          try {
            console.log('🔍 开始解析剧本内容')
            // 清理markdown代码块标记
            let cleanContent = content.trim()
            if (cleanContent.startsWith('```json')) {
              cleanContent = cleanContent.replace(/^```json\s*/, '').replace(/\s*```$/, '')
            } else if (cleanContent.startsWith('```')) {
              cleanContent = cleanContent.replace(/^```\s*/, '').replace(/\s*```$/, '')
            }
            
            // 修复JSON中的换行符问题 - 处理字符串值中的换行
            cleanContent = cleanContent.replace(/"([^"]*)\s*\n\s*([^"]*?)"/g, '"$1$2"')
            // 处理键值对之间的换行
            cleanContent = cleanContent.replace(/"\s*,?\s*\n\s*"/g, '", "')
            
            characterData = JSON.parse(cleanContent)
            
            // 兼容英文字段名，转换为中文字段名
            if (characterData.roleName && !characterData.角色名) {
              characterData.角色名 = characterData.roleName
            }
            if (characterData.characterStory && !characterData.人物故事) {
              characterData.人物故事 = characterData.characterStory
            }
            if (characterData.awakeningPoint && !characterData.觉醒关键点) {
              characterData.觉醒关键点 = characterData.awakeningPoint
            }
            if (characterData.imagePrompt && !characterData.文生图prompt) {
              characterData.文生图prompt = characterData.imagePrompt
            }
            
            console.log('✅ 成功解析JSON格式数据')
            console.log('📋 解析后的角色数据:', characterData)
            console.log('🎭 角色名字段值:', characterData.角色名)
          } catch (e) {
            // 如果不是标准JSON，尝试提取关键信息
            console.log('⚠️ 返回内容不是标准JSON，使用文本解析')
            characterData = this.parseCharacterText(content)
          }
          
          if (characterData && characterData.文生图prompt) {
            console.log('🎯 找到文生图prompt，开始生成图片')
            console.log('🖼️ 即将生成图片的prompt:', characterData.文生图prompt)
            
            // 保存角色数据
            this.characterData = characterData

            // 生成维度分值（只在新生成时创建）
            this.generateDimensionScores()

            // 开始生成图片
            await this.generateImage()
          } else {
            console.log('❌ 无法解析文生图prompt')
            this.isGenerating = false
            this.stopCardRotation()
            this.stopTypewriter() // 停止打字机效果

            uni.showModal({
              title: '生成失败',
              content: '剧本内容解析失败，请重试',
              showCancel: false
            })
          }
        } else {
          console.error('❌ 剧本生成API响应异常')
          console.error('❌ 状态码:', res.statusCode)
          console.error('❌ 响应数据:', res.data)
          throw new Error('API响应异常')
        }
        
      } catch (error) {
        console.error('💥 生成剧本失败:', error)
        console.error('💥 错误详情:', error.message)

        this.isGenerating = false
        this.stopCardRotation()
        this.stopTypewriter() // 停止打字机效果

        uni.showModal({
          title: '生成失败',
          content: '剧本生成遇到问题，请稍后重试',
          showCancel: false
        })
      }
    },
    
    // 解析文本格式的角色信息
    parseCharacterText(text) {
      try {
        console.log('🔍 开始解析文本格式的角色信息')
        console.log('📄 待解析文本:', text)
        
        const lines = text.split('\n').filter(line => line.trim())
        const result = {}
        
        for (const line of lines) {
          if (line.includes('角色名：') || line.includes('角色名:')) {
            result.角色名 = line.split(/[：:]/)[1]?.trim()
            console.log('✅ 找到角色名:', result.角色名)
          } else if (line.includes('人物故事：') || line.includes('人物故事:')) {
            result.人物故事 = line.split(/[：:]/)[1]?.trim()
            console.log('✅ 找到人物故事:', result.人物故事)
          } else if (line.includes('觉醒关键点：') || line.includes('觉醒关键点:')) {
            result.觉醒关键点 = line.split(/[：:]/)[1]?.trim()
            console.log('✅ 找到觉醒关键点:', result.觉醒关键点)
          } else if (line.includes('文生图prompt：') || line.includes('文生图prompt:')) {
            result.文生图prompt = line.split(/[：:]/)[1]?.trim()
            console.log('✅ 找到文生图prompt:', result.文生图prompt)
          }
        }
        
        const parsedResult = Object.keys(result).length > 0 ? result : null
        console.log('📋 文本解析结果:', parsedResult)
        return parsedResult
      } catch (e) {
        console.error('💥 文本解析失败:', e)
        return null
      }
    },
    
    // 生成图片
    async generateImage() {
      
      try {
        console.log('🎨 开始生成图片')
        
        // 添加固定风格提示词
        const stylePrompt = "，CG艺术，角色设计，卡通风格，新海诚绘画风格，游戏美术，高度详细、超高分辨率、32K UHD、最佳质量、杰作，疯狂的细节，工作室光，C4D，blender建模，OC渲染"
        const finalPrompt = this.characterData.文生图prompt + stylePrompt
        
        console.log('🎨 最终prompt:', finalPrompt)
        
        // 准备API请求数据
        const requestData = {
          prompt: finalPrompt,
          width: 720,
          height: 1280,
          tid: this.traceId // 添加追踪ID
        }
        
        console.log('📤 API请求数据:', requestData)
        
        // 发起API请求
        const imageRes = await uni.request({
          url: `${this.config.apiBaseUrl}/api/common/ai/jimeng/draw`,
          method: 'POST',
          header: {
            'accept': 'application/json',
            'Content-Type': 'application/json'
          },
          data: requestData,
          timeout: 120000
        })
        
        console.log('📥 API响应状态码:', imageRes.statusCode)
        console.log('📥 API响应数据:', imageRes.data)
        
        if (imageRes.statusCode === 200) {
          if (imageRes.data?.code === 0 && imageRes.data?.data?.img_url) {
            const imageUrl = imageRes.data.data.img_url
            const eid = imageRes.data.data.eid // 获取子事务ID
            console.log('✅ 图片生成成功！')
            console.log('🖼️ 生成的图片链接:', imageUrl)
            console.log('🔍 子事务EID:', eid) // 调试打印eid

            // 直接使用原始URL，fastposter会处理图片
            this.generatedImage = imageUrl
            console.log('✅ 图片URL已保存')

            // 保存数据到本地存储
            console.log('💾 保存数据到本地存储...')
            console.log('📋 保存的角色数据:', this.characterData)
            console.log('🖼️ 保存的图片数据:', this.generatedImage.substring(0, 50) + '...')

            uni.setStorageSync('currentCharacterData', this.characterData)
            uni.setStorageSync('currentGeneratedImage', this.generatedImage)
            uni.setStorageSync('currentDimensionScores', this.dimensionScores)

            // 保存身份标签到历史记录，用于下次避免重复
            if (this.characterData.角色名 && this.characterData.角色名.includes(' · ')) {
              const identityLabel = this.characterData.角色名.split(' · ')[0]
              if (identityLabel) {
                const labelsHistory = uni.getStorageSync('generatedLabels') || []
                labelsHistory.push(identityLabel)
                // 只保留最近30个，避免历史记录过长
                const trimmedHistory = labelsHistory.slice(-30)
                uni.setStorageSync('generatedLabels', trimmedHistory)
                console.log('📝 身份标签已保存到历史:', identityLabel)
              }
            }

            console.log('✅ 数据保存完成')

            // 停止轮播并翻转当前卡片
            this.stopCardRotation()
            this.stopTypewriter() // 停止打字机效果
            this.reportCompleted = true
            setTimeout(() => {
              this.flipCard(this.currentCard) // 翻转当前轮播的卡片
              this.showActions = true
            }, 1000)
          } else {
            console.error('❌ API返回错误:', imageRes.data)
            throw new Error(`API返回错误: ${imageRes.data?.msg || '未知错误'}`)
          }
        } else {
          console.error('❌ API请求失败, 状态码:', imageRes.statusCode)
          throw new Error(`API请求失败: ${imageRes.statusCode}`)
        }
        
      } catch (error) {
        console.error('💥 图片生成失败:', error)

        // 设置生成失败状态
        this.generateFailed = true
        this.stopTypewriter() // 停止打字机效果

        uni.showModal({
          title: '生成失败',
          content: '图片生成遇到问题，请稍后重试。您可以点击底部按钮重新生成。',
          showCancel: false
        })
      } finally {
        this.isGenerating = false
      }
    },
    
    // 翻转卡片
    flipCard(index) {
      this.cards[index].flipped = !this.cards[index].flipped
      console.log(`🔄 卡片 ${index} 翻转状态:`, this.cards[index].flipped)
    },
    
    // 处理卡片点击
    handleCardClick(index) {
      if (this.isGenerating) return
      // 只允许点击当前轮播的卡片
      if (index === this.currentCard && this.generatedImage) {
        this.flipCard(index)
      }
    },
    
    // 启动卡片轮播
    startCardRotation() {
      this.rotationInterval = setInterval(() => {
        if (!this.reportCompleted) {
          this.currentCard = (this.currentCard + 1) % this.cards.length
        }
      }, 1500) // 从800ms改为1500ms，更柔和的轮播速度
    },
    
    // 停止卡片轮播
    stopCardRotation() {
      if (this.rotationInterval) {
        clearInterval(this.rotationInterval)
        this.rotationInterval = null
      }
    },
    
    // 获取卡片样式（实现5张卡片布局：左2张，右2张，中间顶部1张）
    getCardStyle(index) {
      const offset = index - this.currentCard

      // 翻转的卡片始终在最上层且居中
      if (this.cards[index].flipped) {
        return {
          transform: 'translate(-50%, -50%) scale(1.5)',  // 与中心卡片保持一致
          opacity: 1,
          zIndex: 999
        }
      }

      // 根据偏移量确定卡片位置
      let position, scale, zIndex, opacity

      if (offset === 0) {
        // 中心卡片 - 最大最前，保持居中位置
        position = 'translate(-50%, -50%)'
        scale = 1.5  // 增大中间卡片的缩放比例
        zIndex = 10
        opacity = 1
      } else if (offset === -1 || (offset === 4 && this.cards.length === 5)) {
        // 左侧第一张卡片
        position = 'translate(-50%, -50%) translateX(-160rpx)'
        scale = 0.9
        zIndex = 8
        opacity = 0.85
      } else if (offset === -2 || (offset === 3 && this.cards.length === 5)) {
        // 左侧第二张卡片（更远）
        position = 'translate(-50%, -50%) translateX(-200rpx)'
        scale = 0.8
        zIndex = 6
        opacity = 0.7
      } else if (offset === 1 || (offset === -4 && this.cards.length === 5)) {
        // 右侧第一张卡片
        position = 'translate(-50%, -50%) translateX(160rpx)'
        scale = 0.9
        zIndex = 8
        opacity = 0.85
      } else if (offset === 2 || (offset === -3 && this.cards.length === 5)) {
        // 右侧第二张卡片（更远）
        position = 'translate(-50%, -50%) translateX(200rpx)'
        scale = 0.8
        zIndex = 6
        opacity = 0.7
      } else {
        // 其他卡片隐藏在后面
        position = 'translate(-50%, -50%)'
        scale = 0.6
        zIndex = 1
        opacity = 0.3
      }

      return {
        transform: `${position} scale(${scale})`,
        opacity,
        zIndex
      }
    },
    
    // 获取粒子样式
    getParticleStyle() {
      const randomX = Math.random() * 100
      const randomY = Math.random() * 100
      const randomDelay = Math.random() * 5
      const randomDuration = 6 + Math.random() * 8 // 6-14秒的随机持续时间

      // 创建星河聚集效果 - 某些区域粒子更密集
      const clusterX = Math.random() < 0.3 ? (Math.random() * 30 + 35) : randomX // 30%概率在中心区域
      const clusterY = Math.random() < 0.2 ? (Math.random() * 40 + 30) : randomY // 20%概率在中上区域

      // 随机粒子大小和发光强度
      const sizeVariant = Math.random()
      let particleSize, glowIntensity

      if (sizeVariant < 0.6) {
        // 60% 小粒子
        particleSize = 3 + Math.random() * 4 // 3-7rpx
        glowIntensity = 0.4 + Math.random() * 0.3 // 0.4-0.7
      } else if (sizeVariant < 0.9) {
        // 30% 中等粒子
        particleSize = 6 + Math.random() * 6 // 6-12rpx
        glowIntensity = 0.6 + Math.random() * 0.3 // 0.6-0.9
      } else {
        // 10% 大粒子（亮星）
        particleSize = 10 + Math.random() * 8 // 10-18rpx
        glowIntensity = 0.8 + Math.random() * 0.2 // 0.8-1.0
      }

      return {
        left: `${clusterX}%`,
        top: `${clusterY}%`,
        animationDelay: `${randomDelay}s`,
        animationDuration: `${randomDuration}s`,
        '--particle-size': `${particleSize}rpx`,
        '--glow-size': `${particleSize * 3}rpx`,
        '--glow-size-2': `${particleSize * 6}rpx`,
        '--glow-size-3': `${particleSize * 9}rpx`,
        '--blur-amount': `${0.2 + Math.random() * 1}rpx`,
        opacity: glowIntensity
      }
    },

    // 获取星云样式
    getNebulaStyle() {
      const randomX = Math.random() * 80 + 10 // 10-90%
      const randomY = Math.random() * 80 + 10 // 10-90%
      const randomDelay = Math.random() * 10
      const randomDuration = 15 + Math.random() * 10 // 15-25秒
      const randomSize = 200 + Math.random() * 300 // 200-500rpx

      return {
        left: `${randomX}%`,
        top: `${randomY}%`,
        width: `${randomSize}rpx`,
        height: `${randomSize * 0.6}rpx`, // 椭圆形
        animationDelay: `${randomDelay}s`,
        animationDuration: `${randomDuration}s`
      }
    },

    // 开始打字机效果
    startTypewriter() {
      console.log('🖨️ 开始打字机效果')
      this.typewriterIndex = 0
      this.typewriterCharIndex = 0
      this.currentTypewriterText = ''
      this.typeNextChar()
    },

    // 打字机逐字显示
    typeNextChar() {
      if (this.typewriterIndex >= this.typewriterMessages.length) {
        // 所有消息都显示完了，停止打字机效果
        return
      }

      const currentMessage = this.typewriterMessages[this.typewriterIndex]

      if (this.typewriterCharIndex < currentMessage.length) {
        // 继续显示当前消息的下一个字符
        this.currentTypewriterText = currentMessage.substring(0, this.typewriterCharIndex + 1)
        this.typewriterCharIndex++

        // 设置下一个字符的延迟
        this.typewriterTimer = setTimeout(() => {
          this.typeNextChar()
        }, 80) // 每个字符间隔80ms
      } else {
        // 当前消息显示完毕，等待一段时间后显示下一条消息
        this.typewriterTimer = setTimeout(() => {
          this.typewriterIndex++
          this.typewriterCharIndex = 0

          if (this.typewriterIndex < this.typewriterMessages.length) {
            // 清空当前文本，准备显示下一条消息
            this.currentTypewriterText = ''
            setTimeout(() => {
              this.typeNextChar()
            }, 300) // 消息间隔300ms
          }
        }, 1500) // 每条消息显示完后等待1.5秒
      }
    },

    // 停止打字机效果
    stopTypewriter() {
      console.log('🛑 停止打字机效果')
      if (this.typewriterTimer) {
        clearTimeout(this.typewriterTimer)
        this.typewriterTimer = null
      }
      this.currentTypewriterText = ''
    },

    // 返回首页
    goBack() {
      uni.reLaunch({
        url: '/pages/index/index'
      })
    },
    
    // 保存报告 - 跳转到海报页面
    async saveReport() {
      try {
        console.log('💾 开始生成专属海报')

        // 检查是否有必要的数据
        if (!this.characterData || !this.generatedImage) {
          uni.showModal({
            title: '数据不完整',
            content: '请先生成完整的角色报告',
            showCancel: false
          })
          return
        }

        // 确保数据已保存到本地存储
        uni.setStorageSync('currentCharacterData', this.characterData)
        uni.setStorageSync('currentGeneratedImage', this.generatedImage)
        uni.setStorageSync('currentDimensionScores', this.dimensionScores)
        
        // 保存身份标签到历史记录（如果还未保存）
        if (this.characterData.角色名 && this.characterData.角色名.includes(' · ')) {
          const identityLabel = this.characterData.角色名.split(' · ')[0]
          if (identityLabel) {
            const labelsHistory = uni.getStorageSync('generatedLabels') || []
            if (!labelsHistory.includes(identityLabel)) {
              labelsHistory.push(identityLabel)
              const trimmedHistory = labelsHistory.slice(-30)
              uni.setStorageSync('generatedLabels', trimmedHistory)
              console.log('📝 身份标签已补充保存到历史:', identityLabel)
            }
          }
        }
        
        console.log('📋 数据已保存，跳转到海报页面')

        // 跳转到海报页面
        uni.navigateTo({
          url: '/pages/poster/poster'
        })

      } catch (error) {
        console.error('💥 跳转海报页面失败:', error)
        uni.showModal({
          title: '跳转失败',
          content: '无法跳转到海报页面，请重试',
          showCancel: false,
          confirmText: '确定'
        })
      }
    },




    // 将图片转换为base64格式（仅在小程序环境中使用）
    async convertImageToBase64(imageUrl) {
      return new Promise((resolve) => {
        try {
          console.log('🔄 将图片转换为base64:', imageUrl)
          
          // 在小程序环境中使用uni.downloadFile
          // #ifndef H5
          uni.downloadFile({
            url: imageUrl,
            success: (res) => {
              if (res.statusCode === 200) {
                // 小程序环境中返回文件路径
                console.log('✅ 小程序下载图片成功')
                resolve(res.tempFilePath)
              } else {
                console.error('❌ 小程序下载图片失败')
                resolve(null)
              }
            },
            fail: (error) => {
              console.error('❌ 小程序下载图片错误:', error)
              resolve(null)
            }
          })
          // #endif
          
          // H5环境下由于CORS限制，不进行转换
          // #ifdef H5
          console.log('⚠️ H5环境下由于CORS限制，不进行转换')
          resolve(null)
          // #endif
          
        } catch (error) {
          console.error('❌ 转换base64遇到错误:', error)
          resolve(null)
        }
      })
    },

    // 小程序环境的图片合成
    async generateCompositeImageMiniProgram() {
      return new Promise((resolve, reject) => {
        try {
          // 创建canvas上下文
          const ctx = uni.createCanvasContext('compositeCanvas', this)

          // 设置画布尺寸 (9:16比例，适合分享)
          const canvasWidth = 750
          const canvasHeight = 1334

          // 设置背景
          ctx.fillStyle = '#ffffff'
          ctx.fillRect(0, 0, canvasWidth, canvasHeight)

          // 下载并绘制生成的图片
          uni.downloadFile({
            url: this.generatedImage,
            success: (res) => {
              if (res.statusCode === 200) {
                // 绘制主图片 (占据上半部分)
                const imageHeight = 800
                ctx.drawImage(res.tempFilePath, 0, 0, canvasWidth, imageHeight)

                // 绘制底部信息区域
                this.drawBottomInfo(ctx, canvasWidth, canvasHeight, imageHeight)

                // 绘制完成，导出图片
                ctx.draw(false, () => {
                  setTimeout(() => {
                    uni.canvasToTempFilePath({
                      canvasId: 'compositeCanvas',
                      success: (canvasRes) => {
                        console.log('✅ 小程序合成图片生成成功:', canvasRes.tempFilePath)
                        resolve(canvasRes.tempFilePath)
                      },
                      fail: (error) => {
                        console.error('❌ 导出canvas失败:', error)
                        reject(error)
                      }
                    }, this)
                  }, 500)
                })
              } else {
                reject(new Error('下载图片失败'))
              }
            },
            fail: (error) => {
              console.error('❌ 下载图片失败:', error)
              reject(error)
            }
          })

        } catch (error) {
          console.error('💥 小程序图片合成失败:', error)
          reject(error)
        }
      })
    },

    // H5环境绘制底部信息
    drawBottomInfoH5(ctx, canvasWidth, canvasHeight, imageHeight) {
      const bottomHeight = canvasHeight - imageHeight
      const startY = imageHeight

      // 设置底部背景
      ctx.fillStyle = '#ffffff'
      ctx.fillRect(0, startY, canvasWidth, bottomHeight)

      let currentY = startY + 40

      // 绘制"我的角色"标题
      ctx.fillStyle = '#333333'
      ctx.font = 'bold 32px Arial, sans-serif'
      ctx.textAlign = 'center'
      ctx.fillText('我的角色', canvasWidth / 2, currentY)

      currentY += 60

      // 绘制角色名
      ctx.fillStyle = '#000000'
      ctx.font = 'bold 48px Arial, sans-serif'
      ctx.textAlign = 'center'
      const characterName = this.characterData.角色名 || '神秘角色'
      ctx.fillText(characterName, canvasWidth / 2, currentY)

      currentY += 80

      // 绘制核心执念
      ctx.fillStyle = '#666666'
      ctx.font = '28px Arial, sans-serif'
      ctx.textAlign = 'center'
      ctx.fillText('核心执念', canvasWidth / 2, currentY)

      currentY += 50

      // 绘制执念内容 (支持换行)
      const obsession = this.characterData.人物故事 || '未知故事'
      this.drawMultilineTextH5(ctx, obsession, canvasWidth / 2, currentY, canvasWidth - 80, 36)

      currentY += 120

      // 绘制二维码区域
      this.drawQRCodeAreaH5(ctx, canvasWidth, currentY)
    },

    // H5环境绘制多行文本
    drawMultilineTextH5(ctx, text, x, y, maxWidth, lineHeight) {
      ctx.fillStyle = '#333333'
      ctx.font = '32px Arial, sans-serif'
      ctx.textAlign = 'center'

      const words = text.split('')
      let line = ''
      let currentY = y

      for (let i = 0; i < words.length; i++) {
        const testLine = line + words[i]
        const metrics = ctx.measureText(testLine)

        if (metrics.width > maxWidth && line !== '') {
          ctx.fillText(line, x, currentY)
          line = words[i]
          currentY += lineHeight
        } else {
          line = testLine
        }
      }
      ctx.fillText(line, x, currentY)
    },

    // H5环境绘制二维码区域
    drawQRCodeAreaH5(ctx, canvasWidth, startY) {
      const qrSize = 120
      const qrSpacing = 80
      const totalWidth = qrSize * 2 + qrSpacing
      const startX = (canvasWidth - totalWidth) / 2

      // 绘制左侧二维码占位
      ctx.fillStyle = '#f0f0f0'
      ctx.fillRect(startX, startY, qrSize, qrSize)
      ctx.strokeStyle = '#cccccc'
      ctx.lineWidth = 2
      ctx.strokeRect(startX, startY, qrSize, qrSize)

      // 绘制右侧二维码占位
      ctx.fillRect(startX + qrSize + qrSpacing, startY, qrSize, qrSize)
      ctx.strokeRect(startX + qrSize + qrSpacing, startY, qrSize, qrSize)

      // 绘制二维码下方文字
      ctx.fillStyle = '#999999'
      ctx.font = '24px Arial, sans-serif'
      ctx.textAlign = 'center'

      // 左侧文字
      ctx.fillText('活动咨询', startX + qrSize / 2, startY + qrSize + 35)

      // 右侧文字
      ctx.fillText('扫码预约大咖沙龙', startX + qrSize + qrSpacing + qrSize / 2, startY + qrSize + 35)
    },

    // 绘制底部信息
    drawBottomInfo(ctx, canvasWidth, canvasHeight, imageHeight) {
      const bottomHeight = canvasHeight - imageHeight
      const startY = imageHeight

      // 设置底部背景
      ctx.fillStyle = '#ffffff'
      ctx.fillRect(0, startY, canvasWidth, bottomHeight)

      let currentY = startY + 40

      // 绘制"我的角色"标题
      ctx.fillStyle = '#333333'
      ctx.font = 'bold 32px sans-serif'
      ctx.textAlign = 'center'
      ctx.fillText('我的角色', canvasWidth / 2, currentY)

      currentY += 60

      // 绘制角色名
      ctx.fillStyle = '#000000'
      ctx.font = 'bold 48px sans-serif'
      ctx.textAlign = 'center'
      const characterName = this.characterData.角色名 || '神秘角色'
      ctx.fillText(characterName, canvasWidth / 2, currentY)

      currentY += 80

      // 绘制核心执念
      ctx.fillStyle = '#666666'
      ctx.font = '28px sans-serif'
      ctx.textAlign = 'center'
      ctx.fillText('核心执念', canvasWidth / 2, currentY)

      currentY += 50

      // 绘制执念内容 (支持换行)
      const obsession = this.characterData.人物故事 || '未知故事'
      this.drawMultilineText(ctx, obsession, canvasWidth / 2, currentY, canvasWidth - 80, 36)

      currentY += 120

      // 绘制二维码区域
      this.drawQRCodeArea(ctx, canvasWidth, currentY)
    },

    // 绘制多行文本
    drawMultilineText(ctx, text, x, y, maxWidth, lineHeight) {
      ctx.fillStyle = '#333333'
      ctx.font = '32px sans-serif'
      ctx.textAlign = 'center'

      const words = text.split('')
      let line = ''
      let currentY = y

      for (let i = 0; i < words.length; i++) {
        const testLine = line + words[i]
        const metrics = ctx.measureText(testLine)

        if (metrics.width > maxWidth && line !== '') {
          ctx.fillText(line, x, currentY)
          line = words[i]
          currentY += lineHeight
        } else {
          line = testLine
        }
      }
      ctx.fillText(line, x, currentY)
    },

    // 绘制二维码区域
    drawQRCodeArea(ctx, canvasWidth, startY) {
      const qrSize = 120
      const qrSpacing = 80
      const totalWidth = qrSize * 2 + qrSpacing
      const startX = (canvasWidth - totalWidth) / 2

      // 绘制左侧二维码占位
      ctx.fillStyle = '#f0f0f0'
      ctx.fillRect(startX, startY, qrSize, qrSize)
      ctx.strokeStyle = '#cccccc'
      ctx.lineWidth = 2
      ctx.strokeRect(startX, startY, qrSize, qrSize)

      // 绘制右侧二维码占位
      ctx.fillRect(startX + qrSize + qrSpacing, startY, qrSize, qrSize)
      ctx.strokeRect(startX + qrSize + qrSpacing, startY, qrSize, qrSize)

      // 绘制二维码下方文字
      ctx.fillStyle = '#999999'
      ctx.font = '24px sans-serif'
      ctx.textAlign = 'center'

      // 左侧文字
      ctx.fillText('活动咨询', startX + qrSize / 2, startY + qrSize + 35)

      // 右侧文字
      ctx.fillText('扫码预约大咖沙龙', startX + qrSize + qrSpacing + qrSize / 2, startY + qrSize + 35)
    },

    // 保存图片到相册
    async saveImageToAlbum(imagePath) {
      return new Promise((resolve, reject) => {
        // #ifdef H5
        // H5环境使用下载方式
        this.downloadImageH5(imagePath).then(resolve).catch(reject)
        // #endif

        // #ifndef H5
        // 小程序环境使用保存到相册
        uni.saveImageToPhotosAlbum({
          filePath: imagePath,
          success: () => {
            console.log('✅ 图片已保存到相册')
            resolve()
          },
          fail: (error) => {
            console.error('❌ 保存到相册失败:', error)
            // 如果是权限问题，提示用户
            if (error.errMsg.includes('auth')) {
              uni.showModal({
                title: '需要相册权限',
                content: '请在设置中允许访问相册权限',
                showCancel: false
              })
            }
            reject(error)
          }
        })
        // #endif
      })
    },

    // H5环境下载图片
    downloadImageH5(imageUrl) {
      return new Promise((resolve, reject) => {
        try {
          // 创建下载链接
          const link = document.createElement('a')
          link.href = imageUrl
          link.download = `我的角色_${this.characterData.角色名 || '神秘角色'}_${Date.now()}.png`

          // 触发下载
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)

          // 释放URL对象
          if (imageUrl.startsWith('blob:')) {
            URL.revokeObjectURL(imageUrl)
          }

          console.log('✅ H5图片下载完成')
          resolve()

        } catch (error) {
          console.error('❌ H5下载图片失败:', error)
          reject(error)
        }
      })
    },

    // 检查是否有已存在的数据（刷新页面场景）
    checkExistingData() {
      console.log('🔍 开始检查本地存储数据...')

      // 从本地存储或全局状态检查是否有已生成的数据
      const savedCharacterData = uni.getStorageSync('currentCharacterData')
      const savedGeneratedImage = uni.getStorageSync('currentGeneratedImage')
      const savedDimensionScores = uni.getStorageSync('currentDimensionScores')

      console.log('📋 本地存储的角色数据:', savedCharacterData)
      console.log('🖼️ 本地存储的图片链接:', savedGeneratedImage)

      if (savedCharacterData && savedGeneratedImage) {
        console.log('🔄 检测到已存在的数据，恢复状态')
        console.log('📋 恢复的角色数据:', savedCharacterData)
        console.log('🖼️ 恢复的图片链接:', savedGeneratedImage)

        this.characterData = savedCharacterData
        this.generatedImage = savedGeneratedImage
        // 恢复维度分值，只有在有缓存时才恢复，否则保持初始状态
        if (savedDimensionScores) {
          this.dimensionScores = savedDimensionScores
          console.log('🎯 恢复缓存的维度分值:', this.dimensionScores)
        } else {
          console.log('⚠️ 没有找到缓存的维度分值，保持当前状态')
        }
        this.hasExistingData = true
        this.reportCompleted = true
        this.showActions = true

        // 停止卡片轮播
        this.stopCardRotation()

        // 翻转当前卡片以显示生成的图片
        setTimeout(() => {
          this.flipCard(this.currentCard)
        }, 500)
      } else {
        console.log('🆕 没有已存在的数据，开始生成新剧本')
        this.hasExistingData = false

        // 开始生成剧本，然后生成图片
        setTimeout(() => {
          this.generateScript()
        }, 1000)
      }
    },

    // 重新生成（用户主动触发）
    regenerateScript() {
      console.log('🔄 用户点击重新生成')

      // 重置状态
      this.characterData = {}
      this.generatedImage = ''
      this.dimensionScores = {
        觉知: 0,
        智慧: 0,
        行动: 0,
        快乐: 0,
        勇气: 0
      }
      this.isGenerating = false
      this.reportCompleted = false
      this.showActions = false
      this.generateFailed = false
      this.hasExistingData = false

      // 重置打字机状态
      this.stopTypewriter()

      // 重置卡片状态
      this.cards.forEach(card => {
        card.flipped = false
      })

      // 清除本地存储
      uni.removeStorageSync('currentCharacterData')
      uni.removeStorageSync('currentGeneratedImage')
      uni.removeStorageSync('currentDimensionScores')

      // 重新启动卡片轮播
      this.stopCardRotation()
      this.startCardRotation()

      // 开始生成新剧本
      setTimeout(() => {
        this.generateScript()
      }, 1000)
    },

    // 调试方法：清理本地存储
    clearLocalStorage() {
      uni.removeStorageSync('currentCharacterData')
      uni.removeStorageSync('currentGeneratedImage')
      uni.removeStorageSync('currentDimensionScores')
      console.log('🗑️ 本地存储已清理')
    },

    // 调试方法：查看本地存储
    checkLocalStorage() {
      const savedCharacterData = uni.getStorageSync('currentCharacterData')
      const savedGeneratedImage = uni.getStorageSync('currentGeneratedImage')
      console.log('📋 当前本地存储的角色数据:', savedCharacterData)
      console.log('🖼️ 当前本地存储的图片链接:', savedGeneratedImage)
    },

    // 隐藏的调试点击处理
    handleDebugClick() {
      this.debugClickCount++
      console.log(`🐛 调试点击次数: ${this.debugClickCount}`)

      // 清除之前的定时器
      if (this.debugClickTimer) {
        clearTimeout(this.debugClickTimer)
      }

      // 如果连续点击5次，清空缓存并重新生成
      if (this.debugClickCount >= 5) {
        console.log('🗑️ 触发调试清空缓存')

        // 清空所有缓存
        uni.clearStorage()

        // 显示提示
        uni.showToast({
          title: '缓存已清空',
          icon: 'success',
          duration: 2000
        })

        // 重置计数
        this.debugClickCount = 0

        // 延迟重新生成
        setTimeout(() => {
          this.regenerateScript()
        }, 2000)

        return
      }

      // 设置定时器，3秒后重置计数
      this.debugClickTimer = setTimeout(() => {
        this.debugClickCount = 0
        console.log('🐛 调试点击计数已重置')
      }, 3000)
    }
  }
}
</script>

<style>
.report-container {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.background-layer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

/* 背景图片层 */
.bg-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('https://sg-mall-prod.oss-cn-shanghai.aliyuncs.com/act/report_bg.png');
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  /* 高斯模糊效果 */
  filter: blur(8rpx);
  /* 稍微放大以避免模糊边缘 */
  transform: scale(1.1);
  z-index: 1;
}

/* 黑色半透明遮罩层 */
.bg-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  z-index: 2;
}

.bg-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3; /* 确保粒子在遮罩层之上 */
}

.particle {
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(0, 212, 255, 0.8) 0%, rgba(0, 212, 255, 0.4) 30%, rgba(0, 212, 255, 0.1) 70%, transparent 100%);
  /* 不同大小的粒子 */
  width: var(--particle-size, 6rpx);
  height: var(--particle-size, 6rpx);
  /* 星河流体效果 */
  box-shadow:
    0 0 var(--glow-size, 20rpx) rgba(0, 212, 255, 0.6),
    0 0 var(--glow-size-2, 40rpx) rgba(0, 212, 255, 0.3),
    0 0 var(--glow-size-3, 60rpx) rgba(0, 212, 255, 0.1);
  animation: starFlow var(--flow-duration, 8s) ease-in-out infinite;
  /* 模糊效果 */
  filter: blur(var(--blur-amount, 0.5rpx));
}

/* 星河流体动画 */
@keyframes starFlow {
  0% {
    transform: translateY(0px) translateX(0px) scale(0.8);
    opacity: 0.3;
  }
  25% {
    transform: translateY(-30rpx) translateX(15rpx) scale(1.2);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-10rpx) translateX(-20rpx) scale(1);
    opacity: 1;
  }
  75% {
    transform: translateY(-40rpx) translateX(10rpx) scale(0.9);
    opacity: 0.6;
  }
  100% {
    transform: translateY(0px) translateX(0px) scale(0.8);
    opacity: 0.3;
  }
}

/* 不同类型的粒子 */
.particle:nth-child(3n) {
  --particle-size: 8rpx;
  --glow-size: 25rpx;
  --glow-size-2: 50rpx;
  --glow-size-3: 75rpx;
  --flow-duration: 6s;
  --blur-amount: 0.8rpx;
  background: radial-gradient(circle, rgba(0, 255, 200, 0.7) 0%, rgba(0, 212, 255, 0.4) 40%, rgba(0, 150, 255, 0.1) 80%, transparent 100%);
}

.particle:nth-child(5n) {
  --particle-size: 4rpx;
  --glow-size: 15rpx;
  --glow-size-2: 30rpx;
  --glow-size-3: 45rpx;
  --flow-duration: 10s;
  --blur-amount: 0.3rpx;
  background: radial-gradient(circle, rgba(100, 200, 255, 0.9) 0%, rgba(0, 212, 255, 0.5) 30%, rgba(0, 180, 255, 0.2) 70%, transparent 100%);
}

.particle:nth-child(7n) {
  --particle-size: 12rpx;
  --glow-size: 35rpx;
  --glow-size-2: 70rpx;
  --glow-size-3: 105rpx;
  --flow-duration: 12s;
  --blur-amount: 1.2rpx;
  background: radial-gradient(circle, rgba(0, 150, 255, 0.6) 0%, rgba(0, 212, 255, 0.3) 50%, rgba(0, 100, 200, 0.1) 90%, transparent 100%);
}

/* 星云效果 */
.nebula {
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(ellipse, rgba(0, 212, 255, 0.08) 0%, rgba(0, 150, 255, 0.04) 40%, rgba(0, 100, 200, 0.02) 70%, transparent 100%);
  filter: blur(3rpx);
  animation: nebulaFlow 20s ease-in-out infinite;
  pointer-events: none;
}

@keyframes nebulaFlow {
  0%, 100% {
    transform: translateX(0px) translateY(0px) scale(1);
    opacity: 0.3;
  }
  25% {
    transform: translateX(50rpx) translateY(-30rpx) scale(1.2);
    opacity: 0.5;
  }
  50% {
    transform: translateX(-30rpx) translateY(40rpx) scale(0.8);
    opacity: 0.7;
  }
  75% {
    transform: translateX(20rpx) translateY(-20rpx) scale(1.1);
    opacity: 0.4;
  }
}

.main-content {
  position: relative;
  z-index: 10; /* 提高z-index确保在背景层之上 */
  padding: 20rpx 30rpx 120rpx;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.header-section {
  text-align: center;
  margin-bottom: 80rpx;
  padding-top: 10rpx;
  /* iOS适配：确保标题不被状态栏遮挡 */
  padding-top: env(safe-area-inset-top, 10rpx);
  min-height: 120rpx; /* 确保有足够空间显示标题 */
}

.title {
  font-size: 56rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 0 20rpx #00d4ff;
  display: block;
  /* margin-bottom: 20rpx; */
  margin-top: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #a0a0ff;
  display: block;
  margin-top: 20rpx;
}

.cards-section {
  position: relative;
  width: 100%;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: visible;
  margin-bottom: 150rpx; /* 增加底部空间，为固定的底部区域留出空间 */
  /* iOS适配：调整卡片区域高度 */
  min-height: 400rpx;
  max-height: 60vh; /* 限制最大高度，防止卡片过大 */
}

/* 底部固定区域 */
.bottom-fixed-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 15;
  pointer-events: none; /* 让非按钮区域不阻挡其他交互 */
}

/* 打字机效果样式 */
.typewriter-section {
  position: relative;
  /* width: 100%; */
  text-align: center;
  padding: 30rpx;
  z-index: 10;
  pointer-events: auto; /* 恢复文字区域的交互 */
}

.typewriter-container {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: rgba(26, 26, 58, 0.8);
  border: 1rpx solid rgba(0, 212, 255, 0.3);
  border-radius: 30rpx;
  padding: 20rpx 40rpx;
  backdrop-filter: blur(10rpx);
  box-shadow:
    0 0 20rpx rgba(0, 212, 255, 0.2),
    inset 0 0 20rpx rgba(0, 212, 255, 0.05);
}

.typewriter-text {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: 500;
  letter-spacing: 1rpx;
  text-shadow:
    0 0 10rpx rgba(0, 212, 255, 0.6),
    0 0 20rpx rgba(0, 212, 255, 0.3);
  min-height: 1.2em;
  display: inline-block;
}

.typewriter-cursor {
  font-size: 28rpx;
  color: #00d4ff;
  margin-left: 4rpx;
  animation: blink 1s infinite;
  text-shadow:
    0 0 10rpx rgba(0, 212, 255, 0.8),
    0 0 20rpx rgba(0, 212, 255, 0.4);
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.cards-container {
  position: relative;
  width: 100%;
  height: 550rpx;  /* 增加高度以适应更大的中间卡片 */
  overflow: visible;
}

.card-wrapper {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  /* 调整尺寸比例匹配图片 720:1280 ≈ 9:16，优化iOS显示 */
  width: 60vw; /* 减小基础宽度 */
  height: calc(60vw * 1.778); /* 16/9 = 1.778 */
  max-width: 380rpx; /* 减小最大宽度 */
  max-height: calc(380rpx * 1.778); /* 约676rpx */
  min-width: 280rpx; /* 减小最小宽度 */
  min-height: calc(280rpx * 1.778); /* 约498rpx */
  transition: all 1.2s cubic-bezier(0.23, 1, 0.32, 1); /* 更柔和的动画：增加时长并使用更平滑的贝塞尔曲线 */
  transform-style: preserve-3d;
  cursor: pointer;
  /* 确保卡片位置始终固定 */
  transform-origin: center center;
}

/* 翻转状态的卡片保持固定位置 */
.card-wrapper.flipped {
  /* 翻转后强制保持在中心位置，覆盖动态样式 */
  transform: translate(-50%, -50%) scale(1.5) !important;  /* 与中间卡片保持一致 */
  /* 确保位置完全固定 */
  top: 50% !important;
  left: 50% !important;
  /* 防止任何可能的位移 */
  margin: 0 !important;
  position: absolute !important;
  /* 翻转后的卡片稍微大一些 */
  z-index: 999 !important;
}

/* 移除loading时的pulse动画效果 */

.card-wrapper.flipped .card {
  /* 只翻转卡片本身，不改变wrapper的位置 */
  transform: rotateY(180deg);
}

@keyframes pulse {
  0%, 100% { opacity: 0.8; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.1); }
}

.card {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  transition: transform 1.0s cubic-bezier(0.23, 1, 0.32, 1); /* 卡片翻转动画也使用更柔和的效果 */
}

.card-back,
.card-front {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 20rpx;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.3);
}

.card-back {
  background: linear-gradient(135deg, #1a1a3a 0%, #2d2d5a 100%);
  border: 2rpx solid #00d4ff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.card-front {
  background: linear-gradient(135deg, #2d2d5a 0%, #1a1a3a 100%);
  border: 2rpx solid #00d4ff;
  transform: rotateY(180deg);
  /* 确保图片完全填充，不留空隙 */
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  position: absolute;
  /* 确保内容不会影响卡片位置 */
  contain: layout style;
  /* 强制固定位置，防止内容影响布局 */
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  /* 确保图片填充整个区域 */
  background-clip: padding-box;
  /* 蓝色科技风格发光效果 */
  box-shadow:
    0 0 20rpx rgba(0, 212, 255, 0.4),
    0 0 40rpx rgba(0, 212, 255, 0.2),
    0 0 60rpx rgba(0, 212, 255, 0.1),
    inset 0 0 20rpx rgba(0, 212, 255, 0.1);
}

/* 扫光效果 */
.card-front::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    transparent 0%,
    transparent 40%,
    rgba(255, 255, 255, 0.1) 45%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0.1) 55%,
    transparent 60%,
    transparent 100%
  );
  z-index: 10;
  pointer-events: none;
  transform: translateX(-200%) translateY(-200%) skew(-45deg);
}

/* 卡片翻转时触发扫光动画 */
.card-wrapper.flipped .card-front::after {
  animation: scanLight 1.0s ease-out 0.3s forwards;
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    180deg, 
    rgba(0, 0, 0, 0) 0%, 
    rgba(0, 0, 0, 0.05) 40%, 
    rgba(0, 0, 0, 0.3) 80%, 
    rgba(0, 0, 0, 0.6) 100%
  );
  border-radius: 18rpx;
}

.card-character-info {
  position: absolute;
  bottom: 40rpx;
  left: 30rpx;
  right: 30rpx;
  z-index: 3;
  text-align: left; /* 改为左对齐 */
  opacity: 0;
  animation: fadeInOnly 0.8s ease 0.5s forwards;
  /* 确保动画不会影响布局 */
  will-change: opacity;
  /* 移除可能影响位置的transform */
}

.card-character-name {
  font-size: 32rpx;  /* 从42rpx减小到32rpx，适配1.5倍缩放 */
  font-weight: bold;
  color: #ffffff;
  text-shadow:
    0 0 10rpx rgba(0, 212, 255, 0.8),
    0 0 20rpx rgba(0, 212, 255, 0.6),
    0 2rpx 4rpx rgba(0, 0, 0, 0.8);
  display: block;
  margin-bottom: 12rpx;  /* 稍微减少间距 */
  letter-spacing: 2rpx;
}

.card-character-scores {
  font-size: 16rpx;  /* 基础字体大小 */
  font-weight: bold;
  color: #ffffff;
  display: block;
  margin-bottom: 12rpx;
  text-shadow:
    0 0 8rpx rgba(255, 215, 0, 0.6),
    0 1rpx 2rpx rgba(0, 0, 0, 0.8);
  opacity: 0.9;
  letter-spacing: 1rpx;  /* 减少字符间距以节省空间 */
  line-height: 1.3;  /* 减少行高 */
  white-space: nowrap;
  /* 添加动态缩放以适应不同屏幕 */
  transform-origin: left center;
  transition: transform 0.3s ease;
}

.card-character-story {
  font-size: 16rpx;  /* 从20rpx减小到18rpx，适配1.5倍缩放 */
  color: #ffffff;
  line-height: 1.5;  /* 稍微减少行高 */
  display: block;
  text-shadow:
    0 0 10rpx rgba(160, 160, 255, 0.5),
    0 1rpx 2rpx rgba(0, 0, 0, 0.8);
  opacity: 0.95;
  margin-bottom: 16rpx;
}

.card-awakening-title {
  font-size: 20rpx;
  color: #00d4ff;
  font-weight: bold;
  display: block;
  text-shadow:
    0 0 15rpx rgba(0, 212, 255, 0.8),
    0 1rpx 2rpx rgba(0, 0, 0, 0.8);
  margin-bottom: 8rpx;
  margin-top: 8rpx;
}

.card-awakening-content {
  font-size: 16rpx;
  color: #ffffff;
  line-height: 1.5;
  display: block;
  text-shadow:
    0 0 10rpx rgba(160, 160, 255, 0.5),
    0 1rpx 2rpx rgba(0, 0, 0, 0.8);
  opacity: 0.95;
  /* font-style: italic; */
}

.card-pattern {
  text-align: center;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 2;
  position: relative;
}

.card-background-image {
  width: 100%;
  height: 100%;
  object-fit: fill;
  border-radius: 18rpx;
  position: absolute;
  top: 0;
  left: 0;
}


.card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: space-between;
}


@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 新增：只改变透明度的动画，不影响位置 */
@keyframes fadeInOnly {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* 扫光动画 */
@keyframes scanLight {
  0% {
    transform: translateX(-200%) translateY(-200%) skew(-45deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateX(200%) translateY(200%) skew(-45deg);
    opacity: 0;
  }
}


.device-id-section {
  position: relative;
  display: flex;
  justify-content: center;
  padding: 15rpx 30rpx 0;
  z-index: 10;
  pointer-events: auto;
}

.device-id-text {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
  letter-spacing: 1rpx;
  text-align: center;
}

.action-section {
  position: relative;
  display: flex;
  justify-content: center;
  gap: 40rpx;
  padding: 20rpx 30rpx 40rpx;
  background: linear-gradient(180deg, rgba(15, 15, 35, 0) 0%, rgba(15, 15, 35, 0.8) 30%, rgba(15, 15, 35, 1) 100%);
  backdrop-filter: blur(10rpx);
  z-index: 10;
  opacity: 0;
  animation: fadeInUp 0.8s ease 0.3s forwards;
  pointer-events: auto; /* 恢复按钮区域的交互 */
}

.action-btn {
  background: linear-gradient(135deg, #1a1a3a 0%, #2d2d5a 100%);
  border: 2rpx solid #00d4ff;
  border-radius: 50rpx;
  padding: 24rpx 48rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  /* 确保按钮文字居中 */
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  /* 蓝色科技风格发光效果 */
  box-shadow:
    0 0 10rpx rgba(0, 212, 255, 0.3),
    0 0 20rpx rgba(0, 212, 255, 0.1),
    inset 0 0 10rpx rgba(0, 212, 255, 0.05);
}

.action-btn.primary {
  background: linear-gradient(135deg, #0066cc 0%, #0099ff 100%);
  border-color: #00d4ff;
  /* 主按钮更强的发光效果 */
  box-shadow:
    0 0 15rpx rgba(0, 212, 255, 0.5),
    0 0 30rpx rgba(0, 212, 255, 0.2),
    0 0 45rpx rgba(0, 212, 255, 0.1),
    inset 0 0 15rpx rgba(0, 212, 255, 0.1);
}

.action-btn:hover {
  /* 悬停时增强发光效果 */
  box-shadow:
    0 0 15rpx rgba(0, 212, 255, 0.4),
    0 0 30rpx rgba(0, 212, 255, 0.2),
    inset 0 0 15rpx rgba(0, 212, 255, 0.08);
  border-color: #33e6ff;
}

.action-btn.primary:hover {
  /* 主按钮悬停时的强化效果 */
  box-shadow:
    0 0 20rpx rgba(0, 212, 255, 0.6),
    0 0 40rpx rgba(0, 212, 255, 0.3),
    0 0 60rpx rgba(0, 212, 255, 0.15),
    inset 0 0 20rpx rgba(0, 212, 255, 0.15);
  border-color: #33e6ff;
}

.action-btn:active {
  transform: scale(0.95);
  /* 点击时的发光效果 */
  box-shadow:
    0 0 8rpx rgba(0, 212, 255, 0.6),
    0 0 16rpx rgba(0, 212, 255, 0.3),
    inset 0 0 12rpx rgba(0, 212, 255, 0.2);
}

.btn-text {
  font-size: 28rpx;
  color: #ffffff;
  font-weight: bold;
  /* 文字发光效果 */
  text-shadow:
    0 0 10rpx rgba(0, 212, 255, 0.8),
    0 0 20rpx rgba(0, 212, 255, 0.4),
    0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  letter-spacing: 1rpx;
}

/* 小屏幕宽度适配 */
@media (max-width: 750rpx) {
  .main-content {
    padding: 15rpx 20rpx 100rpx;
  }
  
  .cards-section {
    min-height: calc(100vh - 280rpx);
    margin-bottom: 80rpx;
  }
  
  .cards-container {
    padding: 0 20rpx;
    height: 85%;
  }
  
  .card-wrapper {
    width: 70vw;
    height: calc(70vw * 1.778);
    max-width: 360rpx;
    max-height: calc(360rpx * 1.778);
  }
  
  .action-section {
    gap: 30rpx;
    padding: 15rpx 20rpx 30rpx;
  }
  
  .action-btn {
    min-width: 240rpx;
    text-align: center;
  }
  
  .title {
    font-size: 48rpx;
  }
  
  .subtitle {
    font-size: 24rpx;
  }
}

/* iOS和小屏幕高度适配 */
@media screen and (max-height: 750px) {
  .header-section {
    margin-bottom: 40rpx;
    min-height: 100rpx;
  }

  .cards-section {
    max-height: 50vh;
    margin-bottom: 120rpx;
  }

  .card-wrapper {
    width: 55vw;
    height: calc(55vw * 1.778);
    max-width: 320rpx;
    max-height: calc(320rpx * 1.778);
  }

  .title {
    font-size: 44rpx;
  }

  /* 高度受限时缩小分值文字 */
  .card-character-scores {
    font-size: 14rpx;
    letter-spacing: 0.5rpx;
    transform: scale(0.9);
  }
}

@media screen and (max-height: 667px) {
  .header-section {
    margin-bottom: 40rpx;
    min-height: 80rpx;
  }

  .cards-section {
    max-height: 45vh;
    margin-bottom: 100rpx;
  }

  .card-wrapper {
    width: 50vw;
    height: calc(50vw * 1.778);
    max-width: 280rpx;
    max-height: calc(280rpx * 1.778);
  }

  .title {
    font-size: 40rpx;
  }

  .subtitle {
    font-size: 22rpx;
  }

  /* 极小屏幕时进一步缩小分值文字 */
  .card-character-scores {
    font-size: 12rpx;
    letter-spacing: 0rpx;
    transform: scale(0.8);
    line-height: 1.2;
  }
}

/* WebView环境特殊适配 */
@media screen and (max-height: 600px) {
  .card-character-scores {
    font-size: 10rpx !important;
    letter-spacing: 0rpx !important;
    transform: scale(0.75) !important;
    line-height: 1.1 !important;
    margin-bottom: 8rpx !important;
  }

  .card-character-name {
    font-size: 20rpx !important;
    margin-bottom: 8rpx !important;
  }

  .card-character-story {
    font-size: 14rpx !important;
    line-height: 1.3 !important;
    margin-bottom: 12rpx !important;
  }

  .card-awakening-title {
    font-size: 14rpx !important;
    margin-bottom: 6rpx !important;
  }

  .card-awakening-content {
    font-size: 12rpx !important;
    line-height: 1.3 !important;
  }
}

/* 极端小高度适配（如某些APP的webview） */
@media screen and (max-height: 500px) {
  .card-character-scores {
    font-size: 8rpx !important;
    transform: scale(0.7) !important;
    margin-bottom: 6rpx !important;
  }

  .card-character-info {
    padding: 15rpx !important;
  }
}

/* 隐藏的测试区域样式 */
.debug-area {
  position: fixed;
  top: 0;
  right: 0;
  width: 60rpx;
  height: 60rpx;
  background: transparent;
  z-index: 9999;
  opacity: 0;
}
</style>