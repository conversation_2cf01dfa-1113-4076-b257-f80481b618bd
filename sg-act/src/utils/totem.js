/**
 * 玛雅图腾查询工具
 * 根据出生日期查询对应的玛雅图腾
 */

// 玛雅图腾列表 (20个图腾，按照玛雅历法顺序)
const MAYA_TOTEMS = [
  '红龙', '白风', '蓝夜', '黄种子', '红蛇', 
  '白世界桥', '蓝手', '黄星', '红月', '白狗',
  '蓝猴', '黄人', '红天行者', '白巫师', '蓝鹰',
  '黄战士', '红地球', '白镜', '蓝风暴', '黄太阳'
];

// 色调对应关系 (13个色调)
const MAYA_TONES = [
  '磁性', '月亮', '电性', '自我存在', '超频',
  '韵律', '共鸣', '银河星系', '太阳', '行星',
  '光谱', '水晶', '宇宙'
];

/**
 * 计算指定日期到玛雅历法基准日期的天数差
 * 使用更准确的玛雅历法计算方法
 * 参考: https://www.starroot.com 的计算方式
 * @param {string} dateString - 日期字符串 (YYYY-MM-DD)
 * @returns {number} 天数差
 */
function getDaysDifference(dateString) {
  // 使用GMT时间避免时区问题
  const targetDate = new Date(dateString + 'T00:00:00.000Z');
  
  // 玛雅历法起始点: 公元前3114年8月11日（格里高利历）
  // 但实际计算中，我们使用一个更近的参考点进行计算
  // 1987年4月25日作为参考点，对应白风图腾
  const referenceDate = new Date('1987-04-25T00:00:00.000Z');
  const referenceKin = 2; // 1987年4月25日对应KIN 2，图腾索引1对应白风
  
  // 计算天数差
  const timeDiff = targetDate.getTime() - referenceDate.getTime();
  const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
  
  // 计算目标日期的KIN值，确保结果在1-260范围内
  let targetKin = referenceKin + daysDiff;
  
  // 处理负数和超出范围的情况
  while (targetKin <= 0) {
    targetKin += 260;
  }
  while (targetKin > 260) {
    targetKin -= 260;
  }
  
  return targetKin;
}

/**
 * 根据出生日期查询玛雅图腾
 * @param {string} birthdate - 出生日期 (YYYY-MM-DD)
 * @returns {Object} 图腾信息
 */
export function getTotemByBirthdate(birthdate) {
  try {
    // 验证输入参数
    if (!birthdate || typeof birthdate !== 'string') {
      throw new Error('Invalid birthdate parameter');
    }
    
    const kinValue = getDaysDifference(birthdate);
    
    // 验证KIN值
    if (isNaN(kinValue) || kinValue < 1 || kinValue > 260) {
      console.error('Invalid KIN value:', kinValue, 'for birthdate:', birthdate);
      throw new Error(`Invalid KIN value: ${kinValue}`);
    }
    
    // 计算图腾索引 (20个图腾循环)，确保索引为正数
    const totemIndex = ((kinValue - 1) % 20 + 20) % 20;
    const totem = MAYA_TOTEMS[totemIndex];
    
    // 计算色调索引 (13个色调循环)，确保索引为正数
    const toneIndex = ((kinValue - 1) % 13 + 13) % 13;
    const tone = MAYA_TONES[toneIndex];
    
    // 计算波符 (每13天一个波符)，确保索引为正数
    const wavespellIndex = (Math.floor((kinValue - 1) / 13) % 20 + 20) % 20;
    const wavespell = MAYA_TOTEMS[wavespellIndex];
    
    // 验证计算结果
    if (!totem || !tone || !wavespell) {
      console.error('Invalid calculation results:', {
        kinValue,
        totemIndex,
        totem,
        toneIndex,
        tone,
        wavespellIndex,
        wavespell
      });
      throw new Error('Failed to calculate totem properties');
    }
    
    const result = {
      birthdate,
      kinValue,
      totem,
      tone,
      wavespell,
      fullName: `${tone}${totem}`,
      description: `您的玛雅图腾是${tone}${totem}，KIN值为${kinValue}`
    };
    
    console.log('🔮 图腾计算成功:', result);
    return result;
  } catch (error) {
    console.error('计算玛雅图腾失败:', error);
    return {
      birthdate,
      kinValue: 1,
      totem: '白风',
      tone: '磁性',
      wavespell: '红龙',
      fullName: '磁性白风',
      description: '无法计算图腾，使用默认值'
    };
  }
}

/**
 * 验证示例：1987年4月25日应该对应白风图腾
 */
export function validateTotemCalculation() {
  const testDate = '1987-04-25';
  const result = getTotemByBirthdate(testDate);
  console.log('测试日期:', testDate);
  console.log('计算结果:', result);
  console.log('是否包含白风:', result.totem.includes('白风') || result.fullName.includes('白风'));
  return result;
}

/**
 * 根据图腾名称获取图腾特质描述
 */
export function getTotemTraits(totem) {
  const totemTraits = {
    '红龙': '创造力强，具有生命力和母性能量',
    '白风': '善于沟通，具有灵性和直觉能力',
    '蓝夜': '梦想家，富有想象力和神秘感',
    '黄种子': '潜力无限，善于学习和成长',
    '红蛇': '生命力旺盛，具有生存本能',
    '白世界桥': '连接者，善于建立关系',
    '蓝手': '实干家，善于创造和治愈',
    '黄星': '追求美感，具有艺术天赋',
    '红月': '情感丰富，具有净化能力',
    '白狗': '忠诚可靠，具有爱心和同情心',
    '蓝猴': '幽默风趣，具有创新精神',
    '黄人': '自由意志，追求智慧和自我实现',
    '红天行者': '探索者，具有冒险精神',
    '白巫师': '魔法师，具有转化能力',
    '蓝鹰': '远见卓识，具有领导才能',
    '黄战士': '勇敢果断，具有战斗精神',
    '红地球': '稳重踏实，具有包容力',
    '白镜': '反思者，追求真理和秩序',
    '蓝风暴': '变革者，具有突破力',
    '黄太阳': '光明使者，具有启发能力'
  };
  
  return totemTraits[totem] || '神秘的能量特质';
}

// 导出测试函数，用于开发调试
export { validateTotemCalculation as testTotem };