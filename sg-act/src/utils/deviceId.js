/**
 * 设备ID管理工具
 * 统一管理设备ID的获取、缓存和兜底逻辑
 */

/**
 * 获取设备ID的通用方法
 * 优先从缓存获取，如果没有则尝试获取系统信息，最后使用随机生成的ID作为兜底
 * @returns {Promise<string>} 设备ID
 */
export function getDeviceId() {
  return new Promise((resolve) => {
    console.log('🔍 开始获取设备ID...')
    
    try {
      // 先尝试从缓存中获取
      const cachedDeviceId = uni.getStorageSync('deviceId')
      if (cachedDeviceId) {
        console.log('📱 从缓存获取设备ID:', cachedDeviceId)
        resolve(cachedDeviceId)
        return
      }

      console.log('📱 缓存中无设备ID，尝试获取系统信息...')
      
      // 使用uni.getSystemInfo替代getDeviceInfo
      uni.getSystemInfo({
        success: (res) => {
          console.log('📱 系统信息获取成功:', res)
          // 尝试多个字段获取设备标识
          const deviceId = res.deviceId || res.uuid || res.deviceUUID || res.brand + '_' + res.model + '_' + Date.now()
          console.log('📱 设备ID获取成功:', deviceId)
          
          // 缓存设备ID
          uni.setStorageSync('deviceId', deviceId)
          console.log('💾 设备ID已缓存')
          resolve(deviceId)
        },
        fail: (error) => {
          console.error('❌ 获取系统信息失败:', error)
          // 生成一个随机ID作为备用
          const deviceId = `device_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
          uni.setStorageSync('deviceId', deviceId)
          console.log('🔄 使用备用设备ID:', deviceId)
          resolve(deviceId)
        }
      })
    } catch (error) {
      console.error('❌ 获取设备ID异常:', error)
      // 生成一个随机ID作为备用
      const deviceId = `device_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
      uni.setStorageSync('deviceId', deviceId)
      console.log('🔄 使用备用设备ID:', deviceId)
      resolve(deviceId)
    }
  })
}

/**
 * 同步获取设备ID的方法
 * 只从缓存中获取，如果没有则返回null
 * @returns {string|null} 设备ID或null
 */
export function getDeviceIdSync() {
  try {
    const cachedDeviceId = uni.getStorageSync('deviceId')
    if (cachedDeviceId) {
      console.log('📱 同步获取设备ID:', cachedDeviceId)
      return cachedDeviceId
    }
    console.log('⚠️ 缓存中未找到设备ID')
    return null
  } catch (error) {
    console.error('❌ 同步获取设备ID异常:', error)
    return null
  }
}

/**
 * 确保设备ID存在的方法
 * 如果缓存中没有设备ID，会主动获取并缓存
 * @returns {Promise<string>} 设备ID
 */
export function ensureDeviceId() {
  return new Promise((resolve) => {
    const cachedDeviceId = getDeviceIdSync()
    if (cachedDeviceId) {
      resolve(cachedDeviceId)
      return
    }
    
    // 如果缓存中没有，则获取新的设备ID
    getDeviceId().then(resolve)
  })
}

/**
 * 清除设备ID缓存（用于调试）
 */
export function clearDeviceId() {
  try {
    uni.removeStorageSync('deviceId')
    console.log('🗑️ 设备ID缓存已清除')
  } catch (error) {
    console.error('❌ 清除设备ID缓存失败:', error)
  }
}
