/**
 * 环境配置文件
 * 统一管理不同环境下的配置
 */

// 获取当前环境
const getEnv = () => {
  return import.meta.env.MODE || 'development'
}

// 获取API基础URL
const getApiBaseUrl = () => {
  return import.meta.env.VITE_API_BASE_URL || 'https://m-test.shengu.shop'
}

// 获取应用名称
const getAppName = () => {
  return import.meta.env.VITE_APP_NAME || '活动'
}

// 获取火山引擎API Key
const getVolcArkApiKey = () => {
  return import.meta.env.VITE_VOLC_ARK_API_KEY || ''
}

// 导出配置
export const config = {
  env: getEnv(),
  apiBaseUrl: getApiBaseUrl(),
  appName: getAppName(),
  volcArkApiKey: getVolcArkApiKey(),
  
  // 判断是否为生产环境
  isProduction: () => getEnv() === 'production',
  
  // 判断是否为测试环境
  isTest: () => getEnv() === 'test',
  
  // 判断是否为开发环境
  isDevelopment: () => getEnv() === 'development'
}

export default config
