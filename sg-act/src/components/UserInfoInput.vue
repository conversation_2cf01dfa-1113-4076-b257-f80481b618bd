<template>
  <view class="user-info-modal" v-if="visible" @click="handleMaskClick">
    <view class="modal-content" @click.stop>
      <view class="modal-header">
        <text class="modal-title">个人信息</text>
        <text class="modal-subtitle">请输入您的基本信息</text>
      </view>
      
      <view class="form-section">
        <view class="form-group">
          <text class="form-label">姓名</text>
          <input 
            class="form-input" 
            v-model="formData.name" 
            placeholder="请输入您的姓名"
            maxlength="10"
            @input="handleNameInput"
          />
          <view class="error-text" v-if="errors.name">{{ errors.name }}</view>
        </view>
        
        <view class="form-group">
          <text class="form-label">性别</text>
          <picker 
            mode="selector" 
            :value="formData.genderIndex" 
            :range="genderOptions"
            @change="handleGenderChange"
            class="gender-picker"
          >
            <view class="form-picker" :class="{ 'placeholder': formData.genderIndex === -1 }">
              {{ formData.genderIndex === -1 ? '请选择性别' : genderOptions[formData.genderIndex] }}
            </view>
          </picker>
          <view class="error-text" v-if="errors.gender">{{ errors.gender }}</view>
        </view>
        
        <view class="form-group">
          <text class="form-label">出生日期</text>
          <picker 
            mode="date" 
            :value="formData.birthdate" 
            @change="handleDateChange"
            :start="minDate"
            :end="maxDate"
            class="date-picker"
          >
            <view class="form-picker" :class="{ 'placeholder': !formData.birthdate }">
              {{ formData.birthdate || '请选择出生日期' }}
            </view>
          </picker>
          <view class="error-text" v-if="errors.birthdate">{{ errors.birthdate }}</view>
        </view>
      </view>
      
      <view class="modal-actions">
        <view class="action-btn secondary" @click="handleCancel">
          <text class="btn-text">取消</text>
        </view>
        <view class="action-btn primary" @click="handleSubmit">
          <text class="btn-text">确定</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'UserInfoInput',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      formData: {
        name: '',
        genderIndex: -1,
        birthdate: ''
      },
      errors: {
        name: '',
        gender: '',
        birthdate: ''
      },
      genderOptions: ['男', '女'],
      minDate: '1900-01-01',
      maxDate: new Date().toISOString().split('T')[0] // 今天的日期
    }
  },
  methods: {
    handleNameInput(e) {
      this.formData.name = e.detail.value
      if (this.errors.name) {
        this.errors.name = ''
      }
    },
    
    handleGenderChange(e) {
      this.formData.genderIndex = e.detail.value
      if (this.errors.gender) {
        this.errors.gender = ''
      }
    },
    
    handleDateChange(e) {
      this.formData.birthdate = e.detail.value
      if (this.errors.birthdate) {
        this.errors.birthdate = ''
      }
    },
    
    // 备用方案：使用原生日期选择器
    showNativeDatePicker() {
      // #ifdef H5
      // H5环境下使用input[type="date"]
      const input = document.createElement('input')
      input.type = 'date'
      input.value = this.formData.birthdate
      input.min = this.minDate
      input.max = this.maxDate
      input.style.opacity = '0'
      input.style.position = 'absolute'
      input.style.top = '-9999px'
      document.body.appendChild(input)
      
      input.addEventListener('change', (e) => {
        this.formData.birthdate = e.target.value
        if (this.errors.birthdate) {
          this.errors.birthdate = ''
        }
        document.body.removeChild(input)
      })
      
      input.click()
      // #endif
      
      // #ifndef H5
      // 非H5环境使用picker组件（已经在模板中实现）
      // #endif
    },
    
    handleMaskClick() {
      // 点击遮罩不关闭弹窗，用户必须选择取消或确定
    },
    
    handleCancel() {
      this.resetForm()
      this.$emit('cancel')
    },
    
    handleSubmit() {
      if (this.validateForm()) {
        this.$emit('submit', {
          name: this.formData.name.trim(),
          gender: this.genderOptions[this.formData.genderIndex],
          birthdate: this.formData.birthdate
        })
        this.resetForm()
      }
    },
    
    validateForm() {
      let isValid = true
      
      // 重置错误信息
      this.errors = {
        name: '',
        gender: '',
        birthdate: ''
      }
      
      // 验证姓名
      if (!this.formData.name || !this.formData.name.trim()) {
        this.errors.name = '请输入您的姓名'
        isValid = false
      } else if (this.formData.name.trim().length < 2) {
        this.errors.name = '姓名至少需要2个字符'
        isValid = false
      }
      
      // 验证性别
      if (this.formData.genderIndex === -1) {
        this.errors.gender = '请选择性别'
        isValid = false
      }
      
      // 验证出生日期
      if (!this.formData.birthdate) {
        this.errors.birthdate = '请选择出生日期'
        isValid = false
      } else {
        const birthYear = new Date(this.formData.birthdate).getFullYear()
        const currentYear = new Date().getFullYear()
        if (birthYear < 1900 || birthYear > currentYear) {
          this.errors.birthdate = '请选择有效的出生日期'
          isValid = false
        }
      }
      
      return isValid
    },
    
    resetForm() {
      this.formData = {
        name: '',
        genderIndex: -1,
        birthdate: ''
      }
      this.errors = {
        name: '',
        gender: '',
        birthdate: ''
      }
    }
  }
}
</script>

<style scoped>
.user-info-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  backdrop-filter: blur(10rpx);
}

.modal-content {
  background: linear-gradient(135deg, #1a1a3a 0%, #2d2d5a 100%);
  border: 2rpx solid #00d4ff;
  border-radius: 30rpx;
  padding: 60rpx 40rpx 40rpx;
  width: 90%;
  max-width: 600rpx;
  position: relative;
  z-index: 101;
  box-shadow: 
    0 0 30rpx rgba(0, 212, 255, 0.3),
    0 0 60rpx rgba(0, 212, 255, 0.1),
    inset 0 0 20rpx rgba(0, 212, 255, 0.05);
}

.modal-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.modal-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 0 20rpx #00d4ff;
  display: block;
  margin-bottom: 20rpx;
}

.modal-subtitle {
  font-size: 28rpx;
  color: #a0a0ff;
  display: block;
}

.form-section {
  margin-bottom: 60rpx;
}

.form-group {
  margin-bottom: 40rpx;
}

.form-label {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: bold;
  display: block;
  margin-bottom: 20rpx;
  text-shadow: 0 0 10rpx rgba(0, 212, 255, 0.5);
}

.form-input {
  width: 100%;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid rgba(0, 212, 255, 0.3);
  border-radius: 20rpx;
  padding: 0 30rpx;
  font-size: 32rpx;
  color: #ffffff;
  box-sizing: border-box;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: #00d4ff;
  box-shadow: 0 0 20rpx rgba(0, 212, 255, 0.3);
}

.form-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.form-picker {
  width: 100%;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid rgba(0, 212, 255, 0.3);
  border-radius: 20rpx;
  padding: 0 30rpx;
  font-size: 32rpx;
  color: #ffffff;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.form-picker.placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.form-picker:active {
  border-color: #00d4ff;
  box-shadow: 0 0 20rpx rgba(0, 212, 255, 0.3);
}

.date-picker {
  position: relative;
  z-index: 102;
}

.error-text {
  font-size: 24rpx;
  color: #ff6b6b;
  margin-top: 10rpx;
  text-shadow: 0 0 10rpx rgba(255, 107, 107, 0.5);
}

.modal-actions {
  display: flex;
  justify-content: center;
  gap: 40rpx;
}

.action-btn {
  background: linear-gradient(135deg, #1a1a3a 0%, #2d2d5a 100%);
  border: 2rpx solid #00d4ff;
  border-radius: 50rpx;
  padding: 24rpx 48rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-width: 180rpx;
  box-shadow:
    0 0 10rpx rgba(0, 212, 255, 0.3),
    0 0 20rpx rgba(0, 212, 255, 0.1),
    inset 0 0 10rpx rgba(0, 212, 255, 0.05);
}

.action-btn.primary {
  background: linear-gradient(135deg, #0066cc 0%, #0099ff 100%);
  border-color: #00d4ff;
  box-shadow:
    0 0 15rpx rgba(0, 212, 255, 0.5),
    0 0 30rpx rgba(0, 212, 255, 0.2),
    0 0 45rpx rgba(0, 212, 255, 0.1),
    inset 0 0 15rpx rgba(0, 212, 255, 0.1);
}

.action-btn.secondary {
  background: linear-gradient(135deg, #3a3a3a 0%, #4a4a4a 100%);
  border-color: #888888;
  box-shadow:
    0 0 10rpx rgba(136, 136, 136, 0.3),
    0 0 20rpx rgba(136, 136, 136, 0.1),
    inset 0 0 10rpx rgba(136, 136, 136, 0.05);
}

.action-btn:hover {
  box-shadow:
    0 0 15rpx rgba(0, 212, 255, 0.4),
    0 0 30rpx rgba(0, 212, 255, 0.2),
    inset 0 0 15rpx rgba(0, 212, 255, 0.08);
  border-color: #33e6ff;
}

.action-btn.primary:hover {
  box-shadow:
    0 0 20rpx rgba(0, 212, 255, 0.6),
    0 0 40rpx rgba(0, 212, 255, 0.3),
    0 0 60rpx rgba(0, 212, 255, 0.15),
    inset 0 0 20rpx rgba(0, 212, 255, 0.15);
  border-color: #33e6ff;
}

.action-btn:active {
  transform: scale(0.95);
}

.btn-text {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: bold;
  text-shadow:
    0 0 10rpx rgba(0, 212, 255, 0.8),
    0 0 20rpx rgba(0, 212, 255, 0.4),
    0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  letter-spacing: 1rpx;
}

.action-btn.secondary .btn-text {
  text-shadow:
    0 0 10rpx rgba(136, 136, 136, 0.8),
    0 0 20rpx rgba(136, 136, 136, 0.4),
    0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

@media (max-width: 750rpx) {
  .modal-content {
    padding: 40rpx 30rpx 30rpx;
    width: 95%;
  }
  
  .modal-title {
    font-size: 40rpx;
  }
  
  .modal-subtitle {
    font-size: 24rpx;
  }
  
  .form-label {
    font-size: 28rpx;
  }
  
  .form-input,
  .form-picker {
    height: 70rpx;
    font-size: 28rpx;
  }
  
  .action-btn {
    min-width: 160rpx;
    padding: 20rpx 40rpx;
  }
  
  .btn-text {
    font-size: 28rpx;
  }
}
</style>