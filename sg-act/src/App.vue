<script>
export default {
  onLaunch: function () {
    console.log('App Launch')
    // 应用启动时立即获取设备ID
    this.initDeviceId()
  },
  onShow: function () {
    console.log('App Show')
  },
  onHide: function () {
    console.log('App Hide')
  },
  methods: {
    // 初始化设备ID - 确保在应用启动时就获取到设备ID
    initDeviceId() {
      console.log('🔍 App启动 - 开始初始化设备ID...')

      try {
        // 先检查缓存中是否已有设备ID
        const cachedDeviceId = uni.getStorageSync('deviceId')
        if (cachedDeviceId) {
          console.log('📱 App启动 - 从缓存获取设备ID:', cachedDeviceId)
          return
        }

        console.log('📱 App启动 - 缓存中无设备ID，开始获取系统信息...')

        // 使用uni.getSystemInfo获取设备信息
        uni.getSystemInfo({
          success: (res) => {
            console.log('📱 App启动 - 系统信息获取成功:', res)
            // 尝试多个字段获取设备标识
            const deviceId = res.deviceId || res.uuid || res.deviceUUID || res.brand + '_' + res.model + '_' + Date.now()
            console.log('📱 App启动 - 设备ID获取成功:', deviceId)

            // 缓存设备ID
            uni.setStorageSync('deviceId', deviceId)
            console.log('💾 App启动 - 设备ID已缓存')
          },
          fail: (error) => {
            console.error('❌ App启动 - 获取系统信息失败:', error)
            // 生成一个随机ID作为备用
            const deviceId = `device_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
            uni.setStorageSync('deviceId', deviceId)
            console.log('🔄 App启动 - 使用备用设备ID:', deviceId)
          }
        })
      } catch (error) {
        console.error('❌ App启动 - 获取设备ID异常:', error)
        // 生成一个随机ID作为备用
        const deviceId = `device_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
        uni.setStorageSync('deviceId', deviceId)
        console.log('🔄 App启动 - 使用备用设备ID:', deviceId)
      }
    }
  }
}
</script>

<style>
/*每个页面公共css */
</style>
