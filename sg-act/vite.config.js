import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    uni(),
    {
      name: 'image-proxy',
      configureServer(server) {
        server.middlewares.use('/api/proxy/image', async (req, res, next) => {
          if (req.method === 'POST') {
            try {
              let body = '';
              req.on('data', chunk => {
                body += chunk.toString();
              });
              
              req.on('end', async () => {
                try {
                  const { imageUrl } = JSON.parse(body);
                  if (!imageUrl) {
                    res.writeHead(400, { 'Content-Type': 'application/json' });
                    res.end(JSON.stringify({ error: '缺少图片URL' }));
                    return;
                  }
                  
                  console.log('🔄 代理获取图片:', imageUrl);
                  
                  // 使用fetch获取图片
                  const response = await fetch(imageUrl, {
                    headers: {
                      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                      'Accept': 'image/*,*/*;q=0.8'
                    }
                  });
                  
                  if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                  }
                  
                  const imageBuffer = await response.arrayBuffer();
                  
                  res.writeHead(200, {
                    'Content-Type': response.headers.get('content-type') || 'image/png',
                    'Access-Control-Allow-Origin': '*',
                    'Content-Length': imageBuffer.byteLength
                  });
                  res.end(Buffer.from(imageBuffer));
                  
                  console.log('✅ 图片代理成功');
                  
                } catch (error) {
                  console.error('❌ 图片代理错误:', error);
                  res.writeHead(500, { 'Content-Type': 'application/json' });
                  res.end(JSON.stringify({ error: '获取图片失败' }));
                }
              });
            } catch (error) {
              console.error('❌ 请求处理错误:', error);
              res.writeHead(500, { 'Content-Type': 'application/json' });
              res.end(JSON.stringify({ error: '请求处理失败' }));
            }
          } else {
            next();
          }
        });
      }
    }
  ],
  server: {
    proxy: {
      '/api/volc': {
        target: 'https://visual.volcengineapi.com',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/volc/, ''),
        configure: (proxy) => {
          proxy.on('proxyReq', (proxyReq, req) => {
            // 保持原始请求头
            if (req.headers['x-date']) {
              proxyReq.setHeader('X-Date', req.headers['x-date']);
            }
            if (req.headers['authorization']) {
              proxyReq.setHeader('Authorization', req.headers['authorization']);
            }
            // 确保Host头正确设置（代理会自动处理，但我们要确保一致）
            proxyReq.setHeader('Host', 'visual.volcengineapi.com');
          });
        }
      }
    }
  }
})