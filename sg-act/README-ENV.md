# 环境配置说明

## 概述

本项目支持多环境配置，包括开发环境、测试环境和生产环境。不同环境使用不同的API域名和配置。

## 环境配置文件

### 1. 开发环境 (.env.development)
```
NODE_ENV=development
VITE_APP_NAME=活动(开发)
VITE_VOLC_ARK_API_KEY=e65df678-4995-4f43-a90d-eba6d909654b
VITE_API_BASE_URL=https://m-test.shengu.shop
```

### 2. 测试环境 (.env.test)
```
NODE_ENV=production
VITE_APP_NAME=活动(测试Test)
VITE_API_BASE_URL=https://m-test.shengu.shop
```

### 3. 生产环境 (.env.production)
```
NODE_ENV=production
VITE_APP_NAME=活动(生产)
VITE_API_BASE_URL=https://m.shengu.shop
```

## 域名配置

- **开发/测试环境**: `https://m-test.shengu.shop`
- **生产环境**: `https://m.shengu.shop`

## 使用方法

### 开发模式

```bash
# 开发环境 (默认)
npm run dev:h5

# 测试环境
npm run dev:h5:test

# 生产环境
npm run dev:h5:prod
```

### 构建模式

```bash
# 开发环境构建 (默认)
npm run build:h5

# 测试环境构建
npm run build:h5 -m test

# 生产环境构建
npm run build:h5 -m production
```

## 代码中的使用

项目中使用统一的配置文件 `src/config/env.js` 来管理环境变量：

```javascript
import config from '@/config/env.js'

// 在组件中使用
export default {
  data() {
    return {
      config,
      // 其他数据...
    }
  },
  methods: {
    async callApi() {
      // 使用配置中的API基础URL
      const response = await uni.request({
        url: `${this.config.apiBaseUrl}/api/endpoint`,
        // ...
      })
    }
  }
}
```

## 配置项说明

- `VITE_APP_NAME`: 应用名称，用于区分不同环境
- `VITE_API_BASE_URL`: API基础URL，根据环境自动切换
- `VITE_VOLC_ARK_API_KEY`: 火山引擎API密钥

## 注意事项

1. 所有环境变量必须以 `VITE_` 开头才能在前端代码中访问
2. 修改环境配置后需要重新启动开发服务器
3. 生产环境部署时确保使用正确的环境配置文件
4. 不要在代码中硬编码域名，统一使用配置文件管理
