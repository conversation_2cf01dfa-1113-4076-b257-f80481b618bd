


### 快速启动

```bash
# 安装依赖
pnpm install

```
### 清除缓存调试
allow pasting 回车
localStorage.clear()

```bash
# 默认本地开发 .env.development
npm run dev:h5

# 指定测试环境开发 .env.test
npm run dev:h5:test
# 指定生产环境开发 .env.production
npm run dev:h5:prod
```


### test部署

```bash
# 登录服务器 密码问徐同学
ssh <EMAIL>

cd /www/server/sld-front
git pull origin dev
cd /www/server/sld-front/sg-act
npm run build:h5:test
cp -r dist/build/h5/* /www/wwwroot/m-test.shengu.shop/act/
```