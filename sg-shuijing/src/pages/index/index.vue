<template>
	<view class="content">
		
		<!-- 新增：标题和链接文本 -->
		<div class="title-container">
			<div class="sg-desc">水晶盲盒惊喜上市</div>
			<div class="sg-desc">查看线下门店了解详情</div>
		</div>

		<!-- 新增：二维码显示 -->
		<div class="qr-code-container">
			<img src="https://sg-tduck.oss-cn-beijing.aliyuncs.com/image/%E5%B7%A7%E7%8F%BA%E6%B0%B4%E6%99%B6VIP.jpeg"
				alt="巧珺水晶VIP二维码" class="qr-code-image" />
		</div>

	</view>
</template>

<script>
import CommonFooter from '@/components/CommonFooter.vue';

export default {
	components: {
		CommonFooter,
	},
	data() {
		return {
			title: 'Hello'
		}
	},
	onLoad() {

	},
	methods: {

	}
}
</script>

<style>
.content {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.logo {
	height: 200rpx;
	width: 200rpx;
	margin: 200rpx auto 50rpx auto;
}

.text-area {
	display: flex;
	justify-content: center;
}

.title {
	font-size: 36rpx;
	color: #8f8f94;
}

.qr-code-container img {
	width: 100px;
	height: 100px;
}

/* 新增：标题容器样式 */
.title-container {
	margin-top: 20px;
	text-align: center;
}

/* 新增：全局样式 sg-title */
.sg-title {
	color: #ff6600;
	font-size: 30px;
}
</style>