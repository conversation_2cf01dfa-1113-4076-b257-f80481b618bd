<template>
  <div class="code-red">
    <div class="sg-title">恭喜中奖</div>
    <div class="sg-desc">中奖概率10%</div>
    <div class="sg-desc">兑换码: {{ code }}</div>
    <div v-if="loading">加载中...</div>
    <div v-else-if="error">{{ error }}</div>
    <div v-else>
      <!-- Delete: <p v-if="data.reward_id === 0">盲盒专属彩虹卡</p> -->
      <div class="copy-code">
        <button @click="copyCode" class="copy-button">点我复制</button>
        <p>复制兑换码添加小助理兑换解读</p>
        <img :src="qrcodeUrl" alt="二维码" class="qrcode" />
      </div>
    </div>
    <CommonFooter />
  </div>
</template>

<script>
import CommonFooter from '@/components/CommonFooter.vue';

export default {
  components: {
    CommonFooter,
  },
  data() {
    return {
      code: '',
      loading: true,
      error: null,
      data: {},
      randomCardId: null,
      qrcodeUrl: 'https://sg-tduck.oss-cn-beijing.aliyuncs.com/quiz/shuijing/shuijing-qrcode.png'
    };
  },
  mounted() {
    this.code = this.$route.query.code;
    if (!this.code) {
      this.error = '无效的兑换key';
      this.loading = false;
      return;
    }
    this.fetchData();
  },
  computed: {
    randomCardImageUrl() {
      return `https://sg-tduck.oss-cn-beijing.aliyuncs.com/quiz/jinjuka/${this.randomCardId}.webp`;
    }
  },
  methods: {
    async fetchData() {
      try {
        const response = await fetch(`/api/shuijing/code/getByCode?code=${this.code}`, {
          method: 'GET',
          headers: {
            'accept': 'application/json'
          }
        });
        const result = await response.json();
        if (result.code === 0) {
          this.data = result.data;
          if (this.data.reward_id === 0) {
            this.generateRandomCardId();
          }
        } else {
          this.error = result.msg || '请求失败';
        }
      } catch (err) {
        this.error = '网络错误，请稍后重试';
      } finally {
        this.loading = false;
      }
    },
    generateRandomCardId() {
      this.randomCardId = Math.floor(Math.random() * 456) + 1;
    },
    copyCode() {
      navigator.clipboard.writeText(this.code).then(() => {
        alert('兑换码已复制');
      }).catch(err => {
        console.error('复制失败', err);
        alert('复制失败，请手动复制');
      });
    }
  }
};
</script>

<style scoped>
.code-red {
  text-align: center;
  margin-top: 50px;
}

.centered-image {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
}

.centered-image img {
  max-width: 100%;
  height: auto;
}

.copy-code {
  margin-top: 20px;
  text-align: center;
}

.copy-button {
  padding: 10px 20px;
  font-size: 16px;
  cursor: pointer;
  border-radius: 20px;
  /* 添加圆角 */
  margin: 0 10px;
  /* 添加左右边距 */
  background-color: #007bff;
  color: white;
  border: none;
}

.copy-code p {
  margin: 10px 0;
  font-size: 14px;
}

.copy-code .qrcode {
  max-width: 100%;
  height: auto;
}

.view-details {
  margin-top: 10px;
  font-size: 14px;
  color: #007bff;
  cursor: pointer;
}</style>


