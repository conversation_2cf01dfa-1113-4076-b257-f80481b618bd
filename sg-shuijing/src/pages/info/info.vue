<template>
	<div class="page-container">
		<div class="code-red">
			<div v-if="loading">加载中...</div>
			<div v-else-if="error">{{ error }}</div>
			<div v-else>
				<!-- 新增：当 resource_id 为 2 时展示中奖内容 -->
				<div v-if="data.resource_id === 2" class="prize-content">
					<div class="sg-desc">恭喜中奖</div>

					<div class="prize-description">
						<span class="sg-title">“莘古彩虹卡”一套</span><br />
						（价值88元）<br />
						联系巧珺水晶任意柜台领取：<br />
					</div>
				</div>
				<!-- 原有逻辑：当 reward_id 为 0 时展示随机卡片 -->
				<div v-else class="centered-image">
					<img :src="randomCardImageUrl" alt="随机卡片" />
				</div>
			</div>
		</div>
		<!-- 新增：通用 footer 部分 -->
		<CommonFooter />
	</div>
</template>

<script>
import CommonFooter from '@/components/CommonFooter.vue';

export default {
	components: {
		CommonFooter,
	},
	data() {
		return {
			code: '',
			loading: true,
			error: null,
			data: {
				resource_id: 0,
			},
			randomCardId: null // 新增字段，用于存储随机生成的卡片ID
		};
	},
	async mounted() {
		this.code = this.$route.query.code; // 从URL参数中读取code
		if (!this.code) {
			this.error = '无效的兑换key'; // 修改提示信息
			this.loading = false;
			return;
		}
		console.log('code:', this.code);
		await this.fetchData();
	},
	computed: {
		// 计算属性，生成随机卡片的图片链接
		randomCardImageUrl() {
			return `https://sg-tduck.oss-cn-beijing.aliyuncs.com/quiz/jinjuka/${this.randomCardId}.webp`;
		}
	},
	methods: {
		copyCode() {
			navigator.clipboard.writeText(this.code).then(() => {
				alert('兑换码已复制');
			}).catch(err => {
				console.error('复制失败', err);
				alert('复制失败，请手动复制');
			});
		},
		async fetchData() {
			console.log('fetchData');
			try {
				// 使用 uni.request 替代 fetch
				const res = await uni.request({
					url: `/api/shuijing/code/getByCode?code=${this.code}`,
					method: 'GET',
					header: {
						'accept': 'application/json'
					}
				});

				const result = res.data;
				console.log('result:', result);
				if (result.code === 0) {
					this.data = result.data;
					this.generateRandomCardId(); // 当 reward_id=0 时生成随机卡片ID
				} else {
					this.error = result.msg || '请求失败';
				}
			} catch (err) {
				this.error = '网络错误，请稍后重试';
			} finally {
				this.loading = false;
			}
		},
		// 生成 1-456 的随机卡片ID
		generateRandomCardId() {
			this.randomCardId = Math.floor(Math.random() * 300) + 1;
		}
	}
};
</script>

<style scoped>
.page-container {
	display: flex;
	flex-direction: column;
	min-height: 100vh;
	/* 确保页面占满屏幕高度 */
}

.code-red {
	width: 750rpx;
	/* 固定页面宽度 */
	margin: 0 auto;
	/* 水平居中 */
	text-align: center;
	padding: 20px;
	box-sizing: border-box;
}

.centered-image {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	margin-top: 20px;
	padding: 40rpx;
}

.centered-image img {
	max-width: 100%;
	height: auto;
	border-radius: 10rpx;
}

.centered-image div {
	margin-top: 10px;
}

.prize-content {
	margin-top: 20px;
	padding: 20px;
	border: 1px solid #ccc;
	border-radius: 10px;
	text-align: center;
}

.prize-title {
	font-size: 20px;
	font-weight: bold;
	color: #ff6600;
	margin-bottom: 10px;
}

.copy-code {
	margin-top: 20px;
	text-align: center;
}

.copy-button {
	padding: 10px 20px;
	font-size: 16px;
	cursor: pointer;
	border-radius: 20px;
	margin: 0 10px;
	background-color: #007bff;
	color: white;
	border: none;
}

.copy-code p {
	margin: 10px 0;
	font-size: 14px;
}

.copy-code .qrcode {
	max-width: 100%;
	height: auto;
}

.prize-description {
	color: #333;
	line-height: 1.5;
}

.highlight {
	font-size: 26px;
	font-weight: bold;
	color: #ff6600;
}

.common-footer {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	background-color: #f8f8f8;
	text-align: center;
	padding: 10px 0;
	font-size: 14px;
	color: #666;
	box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1);
}</style>