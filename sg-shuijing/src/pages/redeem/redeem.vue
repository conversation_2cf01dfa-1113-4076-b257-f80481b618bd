<template>
	<div class="page-container">
		<div class="container">
			<input v-model="redeemCode" type="text" placeholder="请输入8位核销码" class="highlight-input" />
			<!-- 修改：为按钮添加 highlight-button 样式 -->
			<button @click="handleRedeem" class="highlight-button">核销</button>

			<div v-if="redeemResult" class="result-container">
				<p>是否首次核销: {{ redeemResult.is_first_redeem ? '是' : '否' }}</p>
				<p>兑换商品: {{ redeemResult.resource_name }}</p>
				<p v-if="redeemResult.resource_price > 0">价格: {{ redeemResult.resource_price }}</p>
				<p v-if="!redeemResult.is_first_redeem">最近一次核销时间: {{ redeemResult.last_redeem_time }}</p>
			</div>
		</div>
		<!-- 替换为通用 Footer 组件 -->
		<CommonFooter />
	</div>
</template>

<script>
import CommonFooter from '@/components/CommonFooter.vue';

export default {
	components: {
		CommonFooter,
	},
	data() {
    return {
      redeemCode: '',
      redeemResult: null,
    };
  },
  methods: {
    async handleRedeem() {
      if (this.redeemCode.length !== 8) {
        alert('请输入8位核销码');
        return;
      }

      try {
        console.log('核销码:', this.redeemCode);
        const {data}= await uni.request({
          url: '/api/shuijing/code/redeem',
          method: 'POST',
          data: {
            code: this.redeemCode,
          },
          header: {
            'accept': 'application/json'
          }
        });
        this.redeemResult = data.data;
        console.log('核销结果:', this.redeemResult);
      } catch (error) {
        console.error('核销失败:', error);
        alert('核销失败，请重试');
      }
    },
  },
};
</script>

<style scoped>
.page-container {
	display: flex;
	flex-direction: column;
	min-height: 100vh; /* 确保页面占满屏幕高度 */
}

.container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: calc(100vh - 50px); /* 减去 footer 的高度 */
	text-align: center;
}

.highlight-input {
  width: 80%;
  /* 输入框宽度适配 */
  padding: 15px;
  margin-bottom: 20px;
  font-size: 16px;
  border: 2px solid #ff6600;
  /* 突出显示输入框 */
  border-radius: 10px;
  outline: none;
  transition: border-color 0.3s ease;
}

.highlight-input:focus {
  border-color: #ff4d4d;
  /* 聚焦时改变边框颜色 */
}

/* 新增：按钮样式，与输入框保持一致 */
.highlight-button {
  padding: 5px 30px;
  font-size: 16px;
  color: white;
  background-color: #ff6600;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.highlight-button:hover {
  background-color: #ff4d4d;
  /* 鼠标悬停时改变背景颜色 */
}

.result-container {
  margin-top: 20px;
  text-align: center;
}

/* 新增：通用 footer 样式 */
.common-footer {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	background-color: #f8f8f8;
	text-align: center;
	padding: 10px 0;
	font-size: 14px;
	color: #666;
	box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1);
}
</style>